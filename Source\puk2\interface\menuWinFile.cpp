﻿//-----------------------------------------------------------------------------------------------
//	ウインドウ情报セーブファイル关系
//-----------------------------------------------------------------------------------------------
#include "menuAddressBook.h"
#include <io.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <stdio.h>

//ウインドウ情报セーブ用状态构造体
WINDOW_SAVE_DAT WindowSaveDat;

unsigned int FileSize;
unsigned int WriteByte;

BOOL FileSizeCheckAndInit(void);

// ウインドウセーブファイル読み込み
BOOL WindowStateLoad( void ){

	FILE *fp;
	int fh;
	long readbufferlen;
	BOOL InitCheck;

	//一度デフォルト值を入れておく
	SetWindowFlagFromDefault();

	// セーブファイルを开く
	if( (fp = fopen( WIN_SAVE_FILENAME, "rb+" )) == NULL ){
		//开けないのでデフォルト设定

		//デフォルト设定
		SetWindowFlagFromDefault();

		return FALSE;
	}else{
		//読み込むファイル设定

		//ファイルサイズの取得
		if((fh = _open( WIN_SAVE_FILENAME, _O_RDWR | _O_CREAT, _S_IREAD | _S_IWRITE )) != -1){
			FileSize=_filelength( fh );
			_close( fh );
		}else{
			//取得失败

			//デフォルト设定
			SetWindowFlagFromDefault();
			fclose( fp );
			return FALSE;
		}

		if( ferror( fp ) ){
			//読み込み失败

			//デフォルト设定
			SetWindowFlagFromDefault();
			fclose( fp );
			return FALSE;
		}

		//ファイル読み込み
		readbufferlen = fread( (void *)&WindowSaveDat, 1, sizeof( WINDOW_SAVE_DAT ), fp );

		if( ferror( fp ) ){
			//読み込み失败

			//デフォルト设定
			SetWindowFlagFromDefault();
			fclose( fp );
			return FALSE;
		}

		// セーブデータバージョンのチェック
		if( WindowSaveDat.ver != WINDOW_SAVE_DATA_VERSION || SaveFileCheck()==TRUE){
			//デフォルトデータを反映
			SetWindowFlagFromDefault();
		}else{
			//ファイルデータを反映
			InitCheck=SetWindowFlagFromSaveData();
			//ファイルデータを反映する际に以上があればやはりデフォルトを使用
			if(InitCheck==FALSE)
				SetWindowFlagFromDefault();
		}

		fclose( fp );

		return TRUE;
	}
}

// ウインドウセーブファイル书き込み
BOOL WindowStateSave( void )
{
	FILE *fp;

	//ファイル开く
	if( (fp = fopen( WIN_SAVE_FILENAME, "wb+" )) != NULL ){

		//WindowFlagからセーブファイルへ格纳
		SetWindowStateFromWinFlag();

		//ファイル书き込み
		fwrite( (void *)&WindowSaveDat, 1, sizeof(WINDOW_SAVE_DAT), fp );

		fclose( fp );
	}
	return TRUE;
}

#ifdef WIN_SIZE_DEF
	//根据窗口分辨率计算界面控件的显示位置
void SetWindowFlagFromWindowSize(void) {
	//固定
	WindowFlag[MENU_WINDOW_MENU].wy = DEF_APPSIZEY - 28;					//游戏窗口下方菜单条位置(640*480分辨率下默认坐标0,452)
	WindowFlag[MENU_WINDOW_RIGHT_COCKPIT_LARGE].wx = DEF_APPSIZEX - 184;	//按窗口分辨率与按钮个数计算游戏窗口右上方功能按钮条位置(640*480分辨率下默认坐标456, 0)
	WindowFlag[MENU_WINDOW_RIGHT_COCKPIT_SMALL].wx = DEF_APPSIZEX - 14;		//按窗口分辨率与按钮个数计算游戏窗口右上方功能按钮条位置(640*480分辨率下默认坐标626, 0)
	WindowFlag[MENU_WINDOW_MAPNAME].wx = SymOffsetX + 196;					//根据窗口分辨率计算切换地图后地图名称窗口的显示位置(默认196,180)
	WindowFlag[MENU_WINDOW_MAPNAME].wy = SymOffsetY + 180;
	WindowFlag[MENU_WINDOW_SYSTEM].wx = DEF_APPSIZEX - 231;					//根据窗口分辨率计算系统菜单窗口的显示位置(默认409,100)
	WindowFlag[MENU_OLD_CHAT_WINDOW].wy = DEF_APPSIZEY - 52;				//根据窗口分辨率计算旧版聊天窗口的显示位置(默认0,428)
	WindowFlag[MENU_WINDOW_SURPRISE].wx = SymOffsetX + 224;					//根据窗口分辨率计算偷袭文字窗口的显示位置(默认224,162)
	WindowFlag[MENU_WINDOW_SURPRISE].wy = SymOffsetY + 162;

	//可移动
	WindowFlag[MENU_WINDOW_MAP].wx = DEF_APPSIZEX - 287;					//根据窗口分辨率计算小地图窗口的显示位置(默认353,55)
	WindowFlag[MENU_WINDOW_ACTION].wx = DEF_APPSIZEX - 65;					//动作窗口显示位置(默认575,97)
	WindowFlag[MENU_CHAT_WINDOW].wx = DEF_APPSIZEX - 276;					//聊天框位置(640 * 480分辨率默认坐标为364, 428)
	WindowFlag[MENU_CHAT_WINDOW].wy = DEF_APPSIZEY - 52;

	//台服坐标修正
	SystemSwitch[1].ofx = 69;												//台服系统菜单的排列格式
	SystemSwitch[2].ofx = 69;
	SystemSwitch[3].ofx = 69;
	SystemSwitch[4].ofx = 69;
	SystemSwitch[5].ofx = 69;

}
#endif

//セーブ用构造体→ウインドウ设定
BOOL SetWindowFlagFromSaveData( void ){

	int i;

	//ウインドウ座标ｘ、ｙ
	for(i=0;i<MENU_WINDOW_TYPE_NUM;i++){
		WindowFlag[i].wx=WindowSaveDat.WindowSaveDat[i][0];
		WindowFlag[i].wy=WindowSaveDat.WindowSaveDat[i][1];
	}

	//
	FileSizeCheckAndInit();

	//アイテムウインドウの情报
	ItemFrameFlag=WindowSaveDat.WindowSaveDat[MENU_WINDOW_ITEM][2];
	BtlitemFrameFlag=WindowSaveDat.WindowSaveDat[MENU_WINDOW_ITEM][3];

	//システムウインドウの情报
	ShortCutMode=WindowSaveDat.WindowSaveDat[MENU_WINDOW_SYSTEM][2];

	//チャットウインドウの情报
	ChatWinST.WidthSize=WindowSaveDat.WindowSaveDat[MENU_CHAT_WINDOW][2];
	ChatWinST.HeightSize=WindowSaveDat.WindowSaveDat[MENU_CHAT_WINDOW][3];
	ChatMode=WindowSaveDat.WindowSaveDat[MENU_CHAT_WINDOW][4];

	//左コクピット
	//ウインドウサイズフラグ
	MenuWindowLeftSizeFlag=WindowSaveDat.WindowSaveDat[MENU_WINDOW_LEFT_COCKPIT_LARGE][2];
	//体力バー	
	if(WindowSaveDat.WindowSaveDat[MENU_WINDOW_LEFT_COCKPIT_LARGE][3]==1){
		CharHPLPStatusFlag=TRUE;
	}else{
		CharHPLPStatusFlag=FALSE;
	}

	//右コクピット
	//ウインドウサイズフラグ
	MenuWindowRightSizeFlag=WindowSaveDat.WindowSaveDat[MENU_WINDOW_RIGHT_COCKPIT_LARGE][2];

	//マップ
	//ウインドウ开いていたフラグ
	WindowFlag[MENU_WINDOW_MAP].ReOpen=WindowSaveDat.WindowSaveDat[MENU_WINDOW_MAP][2];

	//アクション
	//ウインドウ开いていたフラグ
	WindowFlag[MENU_WINDOW_ACTION].ReOpen=WindowSaveDat.WindowSaveDat[MENU_WINDOW_ACTION][2];

	//アドレスブック
	//Ｖｉｅｗモード
	menuWindowAddressBookViewMode=(VIEW_MODE)WindowSaveDat.WindowSaveDat[MENU_WINDOW_ADDRESS][2];

#ifdef WIN_SIZE_DEF
	SetWindowFlagFromWindowSize();
#endif
	return TRUE;
}

//デフォルト→ウインドウ设定
void SetWindowFlagFromDefault( void ){

	int i;
	const WINDOW_DATA *mwd;

	//ウインドウ座标ｘ、ｙ
	for(i=0;i<MENU_WINDOW_TYPE_NUM;i++){
		mwd = WindowData[ i ];
		WindowFlag[i].wx=mwd->wx;
		WindowFlag[i].wy=mwd->wy;
	}

	//アイテムウインドウの情报
	ItemFrameFlag = -1;
	BtlitemFrameFlag = -1;

	//システムウインドウの情报
	ShortCutMode=ShortCutPuk2Mode;

	//チャットウインドウの情报
	ChatWinST.WidthSize=12;
	ChatWinST.HeightSize=4;

	//左コクピット
	MenuWindowLeftSizeFlag=0;

	//右コクピット
	MenuWindowRightSizeFlag=0;

	//マップ
	WindowFlag[MENU_WINDOW_MAP].ReOpen=0;

	//アクション
	WindowFlag[MENU_WINDOW_ACTION].ReOpen=0;

	//アドレスブック
	menuWindowAddressBookViewMode=EnumMenuWindowAddressBookViewModeDetail;

#ifdef WIN_SIZE_DEF
	SetWindowFlagFromWindowSize();
#endif
}

//セーブ用构造体クリア
void ClearWindowStateData( void ){

	// セーブデータバッファクリア
	memset( &WindowSaveDat, 0, sizeof( WINDOW_SAVE_DAT ) );

}

//ウインドウ设定→セーブ用构造体
void SetWindowStateFromWinFlag( void ){

	int i;

	//内容初期化
	ClearWindowStateData();

	//バージョン情报
	WindowSaveDat.ver=WINDOW_SAVE_DATA_VERSION;

	//ウインドウ座标ｘ、ｙ
	for(i=0;i<MENU_WINDOW_TYPE_NUM;i++){
		WindowSaveDat.WindowSaveDat[i][0]=WindowFlag[i].wx;
		WindowSaveDat.WindowSaveDat[i][1]=WindowFlag[i].wy;
	}

	//アイテムウインドウの情报
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_ITEM][2]=ItemFrameFlag;
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_ITEM][3]=BtlitemFrameFlag;

	//システムウインドウの情报
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_SYSTEM][2]=ShortCutMode;

	//チャットウインドウの情报
	WindowSaveDat.WindowSaveDat[MENU_CHAT_WINDOW][2]=ChatWinST.WidthSize;
	WindowSaveDat.WindowSaveDat[MENU_CHAT_WINDOW][3]=ChatWinST.HeightSize;
	WindowSaveDat.WindowSaveDat[MENU_CHAT_WINDOW][4]=ChatMode;

	//左コクピット
	//ウインドウサイズフラグ
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_LEFT_COCKPIT_LARGE][2]=MenuWindowLeftSizeFlag;
	//体力バー	
	if(CharHPLPStatusFlag==TRUE){
		WindowSaveDat.WindowSaveDat[MENU_WINDOW_LEFT_COCKPIT_LARGE][3]=1;
	}else{
		WindowSaveDat.WindowSaveDat[MENU_WINDOW_LEFT_COCKPIT_LARGE][3]=0;
	}

	//右コクピット
	//ウインドウサイズフラグ
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_RIGHT_COCKPIT_LARGE][2]=MenuWindowRightSizeFlag;

	//マップ
	//ウインドウ开いていたフラグ
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_MAP][2]=WindowFlag[MENU_WINDOW_MAP].ReOpen;

	//アクション
	//ウインドウ开いていたフラグ
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_ACTION][2]=WindowFlag[MENU_WINDOW_ACTION].ReOpen;

	//アドレスブック
	//Ｖｉｅｗモード
	WindowSaveDat.WindowSaveDat[MENU_WINDOW_ADDRESS][2]=menuWindowAddressBookViewMode;

}

//セーブデータのチェック
BOOL SaveFileCheck(void){

	//左コクピットの座标は动かないので
	if(WindowSaveDat.WindowSaveDat[MENU_WINDOW_LEFT_COCKPIT_LARGE][0]!=WindowDataMenuLeftLarge.wx){
		return TRUE;
	}

	//右コクピットの座标は动かないので
	if(WindowSaveDat.WindowSaveDat[MENU_WINDOW_RIGHT_COCKPIT_LARGE][0]!=WindowDataMenuRightLarge.wx){
		return TRUE;
	}

	//下メニューの座标は动かないので
	if(WindowSaveDat.WindowSaveDat[MENU_WINDOW_MENU][0]!=WindowDataMenuMenu.wx){
		return TRUE;
	}

	return FALSE;
}

BOOL FileSizeCheckAndInit(void){

	int AddSize;
	int AddCount;
	int FileWinCount;
	int NowWinCount;
	BOOL ReturnFlag=FALSE;
	int i;
	const WINDOW_DATA *mwd;

	if(FileSize!=sizeof(WINDOW_SAVE_DAT)){

		//差分取得
		AddSize=sizeof(WINDOW_SAVE_DAT)-FileSize;
		//ファイルのウインドウ数		
		FileWinCount=(FileSize-WindowSaveDat.ver)/sizeof(WindowSaveDat.WindowSaveDat[0]);
		//最新のウインドウ数
		NowWinCount=MENU_WINDOW_TYPE_NUM;
		//新规に增えたウインドウ数
		AddCount=AddSize/sizeof(WindowSaveDat.WindowSaveDat[0]);	

		//ファイルサイズが违うので差分だけ初期化する
		if(AddSize<0){
			//持ってるファイルのほうがサイズが大きかったら异常
			return FALSE;
		}

		if((AddSize%sizeof(WindowSaveDat.WindowSaveDat[0]))!=0){
			//割り切れないはずが无いので异常
			return FALSE;
		}

		//追加分だけ初期化值で上书き
		for(i=FileWinCount;i<FileWinCount+AddCount;i++){
			mwd = WindowData[ i+1 ];
			WindowFlag[i+1].wx=mwd->wx;
			WindowFlag[i+1].wy=mwd->wy;
		}
	}

	return TRUE;
}
