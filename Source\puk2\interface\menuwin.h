﻿/************************/
/*	menuwin.h			*/
/************************/
#ifndef _MENUWIN_H_
#define _MENUWIN_H_

#include "menuwinonelineinfo.h"

//--------------------------------------------------------------------------//
// 定数定义																	//
//--------------------------------------------------------------------------//
// ウィンドウの种类
enum {

	MENU_WINDOW_NONE,

//左コクピット
	MENU_WINDOW_LEFT_COCKPIT_LARGE,		// 左コンソール
	MENU_WINDOW_LEFT_COCKPIT_MIDDLE,	// 左コンソール（小型化）
	MENU_WINDOW_LEFT_COCKPIT_SMALL,		// 左コンソール（最小）

//右コクピット
	MENU_WINDOW_RIGHT_COCKPIT_LARGE,	// 右コンソール
	MENU_WINDOW_RIGHT_COCKPIT_SMALL,	// 右コンソール（最小）

//メニュー
	MENU_WINDOW_MENU,					// メニューウィンドウ

	MENU_WINDOW_STATUS,					// 状态ウインドウ
	MENU_WINDOW_DETAIL,					// ディティールウインドウ
	MENU_WINDOW_TITLE,					// タイトルウインドウ

	MENU_WINDOW_SKILL,					// スキルウインドウ
	MENU_WINDOW_SKILLCREATE,			// スキルクリエートウィンドウ
	MENU_WINDOW_SKILLGATHER,			// スキル采取ウィンドウ
	MENU_WINDOW_SKILLOTHERSRESULT,		// スキルその他结果ウィンドウ
	MENU_WINDOW_ITEM,					// アイテムウインドウ
	MENU_WINDOW_MONSTER,				// モンスターウインドウ
	MENU_WINDOW_MONSTER_STATUS,			// モンスター状态ウインドウ
	MENU_WINDOW_MONSTER_DETAIL,			// モンスターディティールウインドウ
	MENU_WINDOW_MONSTER_SKILL,			// モンスタースキルウインドウ
	MENU_WINDOW_ALBUM,					// アルバムウインドウ
	MENU_WINDOW_ALBUM_DETAIL,			// アルバムディティールウインドウ
	MENU_WINDOW_ADDRESS,				// アドレスウインドウ（未使用）
	MENU_WINDOW_ADDRESS_SENDMAIL,		// メール送信ウインドウ（杉）
	MENU_WINDOW_ADDRESS_SENDPETMAIL,	// ペットメール送信ウインドウ（杉）
	MENU_WINDOW_SYSTEM,					// システムウインドウ
	MENU_WINDOW_SYSTEMSHORTCUT,			// ショートカットウインドウ

//バトル
	MENU_WINDOW_BATTLE,					// バトルウィンドウ
	MENU_WINDOW_BTLITEM,				// 战闘用アイテムウィンドウ
	MENU_WINDOW_BTLPET,					// 战闘用使い魔ウィンドウ
	MENU_WINDOW_WATCH,					// 観战ウィンドウ
	MENU_WINDOW_RESULT,					// バトル结果ウィンドウ
	MENU_WINDOW_DUELRESULT,				// デュエル结果ウインドウ
	MENU_WINDOW_SURPRISE,				// 不意打ちウィンドウ

//右コクピット中ウインドウ
	MENU_WINDOW_MAP,					// マップウインドウ
	MENU_WINDOW_ACTION,					// アクションウインドウ

//チャット
	MENU_CHAT_WINDOW,					// チャットウインドウ

//共用

//サーバリクエスト
	MENU_WINDOW_GENERAL,				// 通常版泛用ウィンドウ
	MENU_WINDOW_SRGENERAL,				// サーバーリクエスト版泛用ウィンドウ
	MENU_WINDOW_GUILMONFOOD,			// 公会宠物饵やりウィンドウ

	MENU_WINDOW_MAPNAME,				// マップ名
	MENU_WINDOW_HISTORY,
	MENU_WINDOW_BANK,					// 银行ウィンドウ


	MENU_WINDOW_CALCULATOR,				// 电卓ウィンドウ

	MENU_WINDOW_TRADE,					// トレードウィンドウ
	MENU_WINDOW_TARGETSEL,				// 对象选择ウィンドウ
	MENU_WINDOW_TARGETSEL1,				// 对象选择ウィンドウ１
	MENU_WINDOW_TARGETSEL2,				// 对象选择ウィンドウ２

	MENU_WINDOW_MULTIPURPOSE,		// アドレスウインドウ
	MENU_WINDOW_GUILDINFO,			// guildSettingWindow
	MENU_WINDOW_GROUP_MAIL,			// groupMail

	MENU_WINDOW_BBS,					// ＢＢＳ（揭示板）

	MENU_WINDOW_GUILMONSTATUS,			// 公会宠物状态ウィンドウ

	MENU_WINDOW_SHOPTOP,				// ショップトップウィンドウ

	MENU_WINDOW_ITEMSHOP,				// アイテムショップウィンドウ

	MENU_WINDOW_SKILLSHOP,				// スキルショップウィンドウ

	MENU_WINDOW_CHANGE_GUILD_TITLE,		// 家族称号变更ウインドウ

	MENU_WINDOW_ORTHOPEDIST,			// 整形外科医ウィンドウ

	MENU_OLD_CHAT_WINDOW,				// 旧版チャットウインドウ

#ifdef PUK3_PROF
	MENU_WINDOW_PROFILE,				// プロフィールウインドウ
	MENU_WINDOW_PROFILE_CATEGORY,		// プロフィール分类
	MENU_WINDOW_PROFILE_PROFILE_1,		// プロフィールＢＢＳ１（分类选择）
	MENU_WINDOW_PROFILE_PROFILE_2,		// プロフィールＢＢＳ２（对象者一覧）
	MENU_WINDOW_PROFILE_PROFILE_3,		// プロフィールＢＢＳ３（１对象者の详细）
	MENU_WINDOW_PROFILE_PROFILE_4,		// プロフィールＢＢＳ４（メール）
	MENU_WINDOW_PROFILE_USER_NPC,		// 他の人のプロフィール
#endif

	MENU_WINDOW_TYPE_NUM
};

// スイッチの种类（SWITCH_DATA & SWITCH_INFO の type に使用）
enum {
	SWITCH_NONE,						// 表示のみ
	SWITCH_GRAPHIC,						// 画像スイッチ
	SWITCH_ACTION,						// アクションスイッチ
	SWITCH_TEXT,						// テキスト
	SWITCH_BUTTON,						// 通常ボタン
	SWITCH_DIALOG,						// テキスト入力ボックス
	SWITCH_CHATSTR,						// チャットの履历文字表示
	SWITCH_NUMBER,						// グラフィック数字描画スイッチ

	SWITCH_TYPE_NUM
};

// ウィンドウ属性フラグ（WINDOW_DATA & WINDOW_INFO の flag に使用）
#define		MENU_ATTR_NOHIT		(1<<0)	// マウス判定を透过
#define		MENU_ATTR_NOMOVE	(1<<1)	// 动かせない


// WINDOW_INFO用ウィンドウ制御フラグ
#define		WIN_INFO_MOVING		(1<<11)	// 移动中
#define		WIN_INFO_ACTIVE		(1<<12)	// アクティブウィンドウ（使うのか？）
#define		WIN_INFO_PRIO		(1<<13)	// プライオリティ入れ替え
#define		WIN_INFO_HIDE		(1<<14)	// ウィンドウ非表示
#define		WIN_INFO_DEL		(1<<15)	// ウィンドウ消去

// マウス状态
#define		MENU_MOUSE_OVER			(1<<0)	// かさなり
#define		MENU_MOUSE_LEFT			(1<<1)	// 右键
#define		MENU_MOUSE_RIGHT		(1<<2)	// 右クリック
#define		MENU_MOUSE_LEFTHOLD		(1<<3)	// 左押されてる
#define		MENU_MOUSE_RIGHTHOLD	(1<<4)	// 右押されてる
#define		MENU_MOUSE_DRAGOVER		(1<<5)	// ドラッグ中のマウスカーソルが重なっている
#define		MENU_MOUSE_DROP			(1<<6)	// ものがドロップされた
#define		MENU_MOUSE_DROPRETURN	(1<<7)	// ドロップしたものが戾ってきた
#define		MENU_MOUSE_LEFT_DC		(1<<8)	// 左ダブルクリック
#define		MENU_MOUSE_RIGHT_DC		(1<<9)	// 右ダブルクリック
#define		MENU_MOUSE_LEFTAUTO		(1<<10)	// 右键リピート
#define		MENU_MOUSE_RIGHTAUTO	(1<<11)	// 右クリックリピート

// WINDOW_FLAG用フラグ
#define		WIN_FLAG_MOVING	(1<<11)	// 削除予定フラグ
#define		WIN_FLAG_ACTIVE	(1<<12)	// 

#define		WIN_INIT		-1		// 初期化フラグ



// 各种设定数值
//#define		MENU_WINDOW_MAX		40
#define		MENU_WINDOW_MAX		MENU_WINDOW_TYPE_NUM
#define		MAX_SWITCH			32

// ウインドウ中でドラッグムーブが设定できる数
#define		MAX_WIN_DRAGMOVE		10

// ACTION *openMenuWindow( int type, unsigned char flg, char opentype );
// で、flg に渡す事のできるフラグ
#define OPENMENUWINDOW_HIT 1

//--------------------------------------------------------------------------//
// 构造体定义																//
//--------------------------------------------------------------------------//

//杉
//自家制ＸＹＷＨ
typedef struct {
	int		x;			// x
	int		y;			// y
	int		w;			// w
	int		h;			// h
} SUGIXYWH;

//ウインドウでのドラッグ移动できる范围
typedef struct {
	int		count;						// つかめる矩形の数
	SUGIXYWH rect[MAX_WIN_DRAGMOVE];	// 矩形の范围
} WINDOW_DRAGMOVE;

//ダイアログ管理构造体
typedef struct {
	void *SwAdd;
} STRUCT_DIAROG_STATUS;

// スイッチ登録用构造体
typedef struct {
	int			type;
	int			ofx,ofy;
	int			sx,sy;
	BOOL		Enabled;
	void        *Switch;
	BOOL		(*func)( int, unsigned int );
} SWITCH_DATA;

// スイッチ管理构造体
typedef struct {
	int			type;							// スイッチの种类
	int			status;							// スイッチの状态
	int			ofx,ofy;						// ウィンドウ基准座标からの相对座标（スイッチの表示座标もかねる）
	int			sx,sy;							// スイッチの判定サイズ（枠のサイズにも使う）
	BOOL		Enabled;						// スイッチの机能をするかしないか
	void        *Switch;						// そのスイッチへのポインタ
	BOOL		(*func)( int, unsigned int );	// スイッチ处理关数
} SWITCH_INFO;

//---------------------------

//ボタンスイッチ
typedef struct{

	int status;	
	int x;
	int y;

} BUTTON_SWITCH;

//テキストスイッチ
typedef struct{

	unsigned char color;	//文字カラー
	unsigned char FontSize;	//文字サイズ
	char *text;				//文字列

} TEXT_SWITCH;

//アクションスイッチ（实态）
typedef struct{

	ACTION *ActionAdd;	
	BOOL AnimNoLoop;

} ACTION_SWITCH;

//アクションスイッチ（初期化用メンバ）（ヘッダ中でスイッチとして使用する场合はこちらを使用）
typedef struct{

	int ActionGraphicNo;

} ACTION_SWITCH_INIT;

//画像スイッチ
typedef struct{

	int	graNo;				
	int u;					
	int v;
	int w;
	int h;
	unsigned int rgba;						

} GRAPHIC_SWITCH;

//ダイアログスイッチ
typedef struct{
	INPUT_STR *InpuStrAdd;
} DIALOG_SWITCH;


typedef struct{

	INPUT_STR *inputStr;// INPUT_STR本体のアドレス
	int ofx; int ofy;	// 文字列の座标(ウインドウからのOFFセット)
	int fontPrio;		// 表示の优先度
	int fontKind;		// 文字の种类
	int color;			// 文字の色　
	char str[516]; 		// 初期文字列
	int lineMax; 		// 最大行数
	int lineLen;		// 一行の文字の长さ
	int lineDist;		// 行と行の间隔（Ｙサイズ）
	BOOL blindFlag;		// 文字を见えなくする（＊＊＊＊＊）

}INIT_STR_STRUCT;


//チャットスイッチ
typedef struct{

	int	DUMMY;

} CHATSTR_SWITCH;



//グラフィック数字描画スイッチ
typedef struct{

	char *str;			// 数值文字列(使用できる文字は0～9までの半角数字と、+-:の3种类の文字と、半角スペース)
	char color;			// 色を指定する、DrawGraphicNumber关数のl_colorに渡すものと同じ
	char size;			// 大きさを指定する、DrawGraphicNumber关数のl_sizeに渡すものと同じ
	unsigned char flag;	// 动作を指定するためのフラグ、DrawGraphicNumber关数のl_flagに渡すものと同じ

} NUMBER_SWITCH;

//---------------------------

// ウィンドウ登録用构造体
typedef struct {
	int				flag;
	int				prio;					// ウィンドウのプライオリティ种别
	int				wx,wy;					// ウィンドウの初期座标
	int				w,h;					// ウィンドウのサイズ
	unsigned int	rgba;					// ウィンドウの背景の色
	int				num;					// スイッチの数
	SWITCH_DATA		*sw;					// スイッチデータへのポインタ
	BOOL			(*func)( int );			// ウィンドウ计算处理
	BOOL			(*funcDraw)( int );		// ウィンドウ描画处理
	BOOL			(*funcDel)(void);		// ウィンドウ削除する际の处理

} WINDOW_DATA;

// メニューウィンドウ管理构造体
typedef struct {
	int			flag;						// ウィンドウフラグ
	int			type;						// ウィンドウの种类
	int			prio;						// プライオリティ
	int			wx,wy;						// ウィンドウの基准座标（要するに左上）
	int			sx,sy;						// ウィンドウ全体のサイズ
	int			mx,my;						// マウス移动用の座标
	union {
		struct {
			char	b,g,r,a;
		};
		unsigned int rgba;						// ウィンドウの背景色
	};
	int			swNum;						// スイッチの数
	SWITCH_INFO	*sw;						// スイッチ登録用バッファ
	BOOL		(*func)( int );				// ウィンドウ计算处理
	BOOL		(*funcDraw)( int );			// ウィンドウ描画关数
	BOOL		(*funcDel)(void);			// ウィンドウ削除する际の处理
	WINDOW_DRAGMOVE DragMove;				//ドラッグムーブ用构造体

} WINDOW_INFO;

// メニューウィンドウ状态管理构造体
// （これは、ウィンドウの种类分バッファを用意する）
typedef struct {
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 
	unsigned char	swData[64];				// スイッチごとにチェックするデータ（使い方は状况次第）
	int				ReOpen;					// 初期化时にオープンするかのフラグ
} WINDOW_FLAG;


//ウインドウ情报セーブ用状态构造体
struct WINDOW_SAVE_DAT{

	int ver;										//バージョン情报
	int WindowSaveDat[MENU_WINDOW_TYPE_NUM][64+10];	//セーブ情报

};


// ドラッグしているもの、ドロップしたものの种类
enum WINDD_OBJECTTYPE{
	WINDD_NONE = 0,		// 何も无し
	WINDD_CANNOTDRAG,	// ドラッグ禁止
	WINDD_ITEM,			// アイテム
	WINDD_MONSTER,		// 使い魔
	WINDD_SKILL,		// スキル
	WINDD_GUILMONFOOD,	// 公会宠物饵箱内アイテム
	WINDD_TRADEITEM,	// トレードアイテム
	WINDD_TRADEMONSTER,	// トレードモンスター
	WINDD_GUILMONSTATUS,// 公会宠物状态ウィンドウからのドラッグアイテム
	WINDD_ITEMSHOP,		// アイテムショップウィンドウアイテムボックスからのドラッグアイテム
	WINDD_SKILLCREATE,	// スキルクリエートウィンドウアイテムボックスからのドラッグアイテム
	WINDD_MONSTERSKILL,	// 使い魔スキル
};


//--------------------------------------------------------------------------//
// 变数宣言																	//
//--------------------------------------------------------------------------//
extern WINDOW_FLAG WindowFlag[ MENU_WINDOW_TYPE_NUM ];

//ウインドウの座标系デバッグ机能フラグ
extern int NewWinDebugFlag;

#define NEW_WIN_DEBUG_WN	0x01
#define NEW_WIN_DEBUG_SW	0x02
#define NEW_WIN_DEBUG_MV	0x04

//--------------------------------------------------------------------------//
// プロト种类宣言															//
//--------------------------------------------------------------------------//

//Switch Function
BOOL MenuSwitchNone( int no, unsigned int flag );
BOOL MenuSwitch_Status( int no, unsigned int flag );
BOOL MenuSwitchKill( int no, unsigned int flag );
BOOL MenuSwitchXGTime( int no, unsigned int flag );
BOOL MenuSwitchDialog( int no, unsigned int flag );
BOOL MenuSwitchChatTextNum( int no, unsigned int flag );
BOOL MenuSwitchLeft1( int no, unsigned int flag );
BOOL MenuSwitchLeft2( int no, unsigned int flag );
BOOL MenuSwitchLeft3( int no, unsigned int flag );
BOOL MenuSwitchMap( int no, unsigned int flag );
BOOL MenuSwitchStatusKill( int no, unsigned int flag );
BOOL MenuSwitchDetailKill( int no, unsigned int flag );
BOOL MenuSwitchTitleKill( int no, unsigned int flag );

BOOL MenuSwitchCloseButton( int no, unsigned int flag );
void CloseWindowSelectType( int MyType );

// マウスの判定が透过しないようにする
BOOL MenuSwitchDelMouse( int no, unsigned int flag );

// スクロールバー
BOOL MenuSwitchScrollBarV( int no, unsigned int flag );
BOOL MenuSwitchScrollBarH( int no, unsigned int flag );
/*********************
注
	このスクロールバーを使うには、
	1.	この关数を持つボタンは、BUTTON_SWITCH である必要があります
	2.	この关数を持つボタンの一つ前に、つまみとなるものを配置する必要があります。
	3.	ドラッグ中の场合、BUTTON_SWITCH::status に 1 がフラグとして立ちます。
	4.	BUTTON_SWITCH::status に 2 のフラグは关数内で使用しているため、使用しないで下さい。
	5.	ドラッグ中の场合、MenuSwitchScrollBarV は、BUTTON_SWITCH::y に、MenuSwitchScrollBarH は、BUTTON_SWITCH::x に结果を返します。
	6.	结果の最大值は、この关数を持つボタンの关数が MenuSwitchScrollBarV の场合は SWITCH_INFO::sy、MenuSwitchScrollBarH の场合は SWITCH_INFO::sx です。
*********************/
	// 戾り值变换用关数
	int ScrollVPointToNum( SWITCH_INFO *sw, int Max );
	int ScrollHPointToNum( SWITCH_INFO *sw, int Max );
	// 变换后戾り值を元に、つまみを移动
	void NumToScrollVMove( SWITCH_INFO *sw, int Max, int Now );
	void NumToScrollHMove( SWITCH_INFO *sw, int Max, int Now );
	// スクロールバー縦ホイール移动(戾り值は移动后のNowの值)
	int WheelToMove( SWITCH_INFO *sw, int Now, int Max, int wheel );

// 电卓コントロール(表示处理込み)
// ボタンの幅を109、高さを85にする必要がある
// 戾り值は、
//	ＯＫボタンを押した ---- CALCULATOR_OK
//	何かボタンを押した ---- CALCULATOR_PUSH
// がフラグとして立つ
#define CALCULATOR_PUSH 1
#define CALCULATOR_OK 2

// 以下の定义は、引数optionへ渡す值を生成するためのフラグです
#define CALCULATOR_OPT_ZERO_OK 1		// 值が０でもＯＫボタンを押せる
#define CALCULATOR_OPT_SHORTCUT 2		// ショートカットキーを机能させる

char CalculatorControl( WINDOW_INFO *wi, int no, unsigned int flag, int AllNum, int Prio, char **OneLineInfo, char option=0, unsigned long rgba=0xffffffff );

//======================================//
//			ドラッグ??ドロップ			//
//======================================//

// ドラッグ??ドロップ元ウィンドウ取得关数
int WinDD_WinType();
// ドラッグ??ドロップ元ボタン番号取得关数
int WinDD_ButtonNo();
// ドラッグ??ドロップデータ种类确认关数	[ ObjData == NULL, SettleDropItem == NULL 可 ]
enum WINDD_OBJECTTYPE WinDD_CheckObjType();
// ドロップ场所取得关数
int WinDD_DropX();
int WinDD_DropY();
// ドロップデータ确认关数(あくまで确认用)
void *WinDD_ObjData();
#ifdef PUK2_NEWDRAG
// ドラッグ开始关数							( 成功 ---- TRUE, 失败 ---- FALSE )
BOOL WinDD_DragStart( enum WINDD_OBJECTTYPE ObjType, void *ObjData, void (*DragFunc)( int ProcNo, unsigned int flag, void *ObjData ) );
#else
// ドラッグ开始关数							( 成功 ---- TRUE, 失败 ---- FALSE )
BOOL WinDD_DragStart( enum WINDD_OBJECTTYPE ObjType, void *ObjData );
#endif
// ドラッグ者登録变更
BOOL WinDD_DragerChange( int WindowType, int ButtonNo );
// ドラッグ終了关数
void WinDD_DragFinish();
#ifdef PUK2_NEWDRAG
// ドロップ关数
BOOL WinDD_DropObject( enum WINDD_OBJECTTYPE ObjType, void *ObjData, short DropX, short DropY, void (*DragFunc)( int ProcNo, unsigned int flag, void *ObjData ) );
// ドロップデータ受け取り关数
void WinDD_AcceptObject();
// ドラッグキャンセル关数
void WinDD_DragCancel();
#else
// ドロップ关数								( 成功 ---- TRUE, 失败 ---- FALSE )
BOOL WinDD_DropObject( enum WINDD_OBJECTTYPE ObjType, void *ObjData, void (*SettleDropItem)(), short DropX, short DropY );
// ドロップデータ受け取り关数				( 成功 ---- データへのポインタ, 失败 ---- NULL )
void *WinDD_GetObject();
#endif
#ifdef PUK2_NEWDRAG
	enum{
		WINDDPROC_DRAG,				// ドラッグ中(ドラッグアイテムの表示、ドラッグのキャンセル、ドロップ等を行います)
		WINDDPROC_DRAGCANCEL,		// ドラッグがキャンセルされました
		WINDDPROC_DROP_FIELD,		// ウィンドウが受け取らず、フィールドにドロップされました、その际の处理と、ドラッグアイテムの解放处理を行います
		WINDDPROC_DROP_ACCEPT,		// WinDD_ReceptObject()关数で受け取られました、ドラッグアイテムの解放处理を行います
		WINDDPROC_DROP_NOACCEPT,	// WinDD_ReceptObject()でもフィールドにも、受け取られませんでした、ドラッグアイテムの解放处理を行います
	};
#endif

// 注		WinDD_DragStart, WinDD_DropObject の二つの关数は、ドラッグ中、ドロップ中には使用できません、失败します。


//Window Function
BOOL MenuWindowChat( int mouse );

static BOOL checkMouseCursor( int x, int y, int w, int h );
static void displaySwitchFrame( SWITCH_INFO *sw, int flag, int col );
void deleteMenuWindow( WINDOW_INFO *win );
WINDOW_INFO *createMenuWindow( int type );
void pushMenuWindow( WINDOW_INFO *win );
void pushMenuWindowAll( void );
void popMenuWindow( WINDOW_INFO *win );
void initMenuWindow( void );
void initMenuWindowFealdSeq( void );
void initMenuWindowBattleSeq( void );
void displayMenuWindow( void );
unsigned int checkMenuWindowMouse( void );
int arrangementMenuWindow( void );
void MenuSwitchProc( int no );
void MenuWindowProc( void );
void ResetWindowPos( void );
BOOL loadNowWindowState( void );
void SetMenuWindowReOpen(void);
void ReSetMenuWindowReOpen(void);

//ログイン时に初期化
void InitNewMenuInLogin(void);
//登出时に初期化
void InitNewMenuInLogout(void);

void MenuWindowStartExe( void );
void MenuWindowEndExe( void );

void initWindowDDSystem(void);
void initDialogSystem(void);
void MenuWindowReOpen(void);
void SetDelFlagAllMenuWindow( void );


// ウィンドウオープン关数
ACTION *openMenuWindowPos( short x, short y, short w, short h, int type, unsigned char flg, char opentype, ACTION **ret=NULL );
ACTION *openMenuWindow( int type, unsigned char flg, char opentype, ACTION **ret=NULL );



// DrawGraphicNumberのl_size用定数
enum{
	G_NUM_SIZE__9,
	G_NUM_SIZE_15,
	G_NUM_SIZE_16,
	G_NUM_SIZE__9_S,
	G_NUM_SIZE_NUM,
};

// DrawGraphicNumberのl_flag用フラグ
#define G_NUM_FLAG_LEFT_JUSTIFIED 0
#define G_NUM_FLAG_RIGHT_JUSTIFIED 1

// l_colorの指定は、フォントの色指定と同じ物を使用する、ただし、GREEN2の色までしかこの关数は对应していない

// グラフィック数值描画关数
void DrawGraphicNumber( int l_x, int l_y, char *l_str, char l_size, char l_color, unsigned char l_flag, int l_Prio );

// グラフィック数值横幅取得关数
int GraphicNumberWidth( char *l_str, char l_size );
// グラフィック数值縦幅取得关数
int GraphicNumberHeight( char l_size );


#include "menuWinEtcObj.h"

#endif

/*
??スイッチがやること

	スイッチが押されたときの制御

*/


/*
??ウィンドウがやること

	ウィンドウ全体とスイッチ类の表示
	ウィンドウの移动とプライオリティ管理
	鲭侧から来た情报の管理（ウィンドウ番号とか？）
	ウィンドウ全体情报の送信

*/

/*

	鲭からの要求で开かれたウィンドウは、基本的に返信する事が前提になるので、
	管理はウィンドウ单位になる（たぶん）

	メニュー的なウィンドウはスイッチ单位での制御が基本になるので、
	管理はスイッチ单位で行えば良い（たぶん）

*/




/* ウィンドウの种类

??メニューウィンドウ（信息ウィンドウと切り替え）＝＞プライオリティ固定
??左コクピット												　＝＞プライオリティ固定
??右コクピット												　＝＞プライオリティ固定

??マップ
??

??状态（三种切り替え）
??



??チャットウィンドウ										　＝＞プライオリティ最低で固定

*/



