﻿//
//  ログイン、サーバ选择处理
//   ゲームが始まるまでをログインとする（タイトルとか）
//
#include<string.h>
#include"../systeminc/version.h"
#include"../systeminc/system.h"
#include"../systeminc/process.h"
#include"../systeminc/netproc.h"
#include"../systeminc/netmain.h"
#include"../systeminc/pc.h"
#include"../systeminc/tool.h"
#include"../systeminc/character.h"
#include"../systeminc/loadsprbin.h"
#include"../systeminc/savedata.h"
#include"../systeminc/chat.h"
#include"../systeminc/ime_sa.h"
#include"../systeminc/menu.h"
#include"../systeminc/t_music.h"
#include"../systeminc/netmain.h"
#include"../systeminc/map.h"
#include"../systeminc/nrproto_cli.h"
#include"../systeminc/field.h"

#include"../systeminc/anim_tbl.h"
#ifdef MULTI_GRABIN
#include"../systeminc/anim_tblex.h"
#include"../systeminc/anim_tbl2.h"
#endif
#include"../systeminc/login.h"

#include"../systeminc/produce.h"

#include "../systeminc/directDraw.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/mouse.h"
#include "../systeminc/pattern.h"
#include "../systeminc/math2.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/sndcnf.h"

#include "../systeminc/loadrealbin.h"

#ifdef PUK2
	#include "../systeminc/sprmgr.h"
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"
	#include "../systeminc/guild.h"
#endif

#ifdef PUK3_PROF
	#include "../PUK3/profile/profile.h"
#endif


//-------------------------------------------------------------------------//
// クライアントバージョン                                                  //
//-------------------------------------------------------------------------//
#ifdef PUK2
	#define NR_VERSION "7.1.00"
#else
	#define NR_VERSION	"7.1.00"
#endif

//-------------------------------------------------------------------------//
// サーバ选择ウィンドウの项目                                              //
//-------------------------------------------------------------------------//
#ifdef VERSION_TW
char selectServerName[][64] =
{
	LANG_LOGIN_CPP_SELECTSERVERNAME_1,
	LANG_LOGIN_CPP_SELECTSERVERNAME_2,
	LANG_LOGIN_CPP_SELECTSERVERNAME_3,
	LANG_LOGIN_CPP_SELECTSERVERNAME_4,
	LANG_LOGIN_CPP_SELECTSERVERNAME_5,
	LANG_LOGIN_CPP_SELECTSERVERNAME_6,
	LANG_LOGIN_CPP_SELECTSERVERNAME_7,
	LANG_LOGIN_CPP_SELECTSERVERNAME_8,
	LANG_LOGIN_CPP_SELECTSERVERNAME_9,
	LANG_LOGIN_CPP_SELECTSERVERNAME_10,
	LANG_LOGIN_CPP_SELECTSERVERNAME_11,
	LANG_LOGIN_CPP_SELECTSERVERNAME_12,
	LANG_LOGIN_CPP_SELECTSERVERNAME_13,
	LANG_LOGIN_CPP_SELECTSERVERNAME_14,
	LANG_LOGIN_CPP_SELECTSERVERNAME_15,
	LANG_LOGIN_CPP_SELECTSERVERNAME_16,
	LANG_LOGIN_CPP_SELECTSERVERNAME_17,
	LANG_LOGIN_CPP_SELECTSERVERNAME_18,
	LANG_LOGIN_CPP_SELECTSERVERNAME_19,
	LANG_LOGIN_CPP_SELECTSERVERNAME_20,
	LANG_LOGIN_CPP_SELECTSERVERNAME_21,
	LANG_LOGIN_CPP_SELECTSERVERNAME_22,
	LANG_LOGIN_CPP_SELECTSERVERNAME_23
};

char selectServerNoStr[][64] =
{
	"I",                                                                 //MLHIDE
	"II",                                                                //MLHIDE
	"II",                                                                //MLHIDE
	"IV",                                                                //MLHIDE
	"V",                                                                 //MLHIDE
	"VI",                                                                //MLHIDE
	"VII",                                                               //MLHIDE
	"VIII",                                                              //MLHIDE
	"IX",                                                                //MLHIDE
	"X",                                                                 //MLHIDE
	"11",                                                                //MLHIDE
	"12",                                                                //MLHIDE
	"13",                                                                //MLHIDE
	"14",                                                                //MLHIDE
	"15",                                                                //MLHIDE
	"16",                                                                //MLHIDE
	"17",                                                                //MLHIDE
	"18",                                                                //MLHIDE
	"19",                                                                //MLHIDE
	"20"                                                                 //MLHIDE
};
#else
char selectServerName[][64] =
{
	"牧羊",                                                                //MLHIDE
	"金牛",                                                                //MLHIDE
	"双子",                                                                //MLHIDE
	"巨蟹",                                                                //MLHIDE
	"狮子",                                                                //MLHIDE
	"处女",                                                                //MLHIDE
	"Yeoskeer【イオスキア】",                                                   //MLHIDE
	"Farkalt【ファルカト】",                                                    //MLHIDE
	"Milliotice【ミリオティス】",                                                //MLHIDE
	"Finia【フィニア】",                                                       //MLHIDE
	"Erenor【エレノア】",                                                      //MLHIDE
	"Karen【カレン】"                                                         //MLHIDE
};
#endif

//-------------------------------------------------------------------------//
// ログイン处理が終わるまでに使用するフラグ                                //
//-------------------------------------------------------------------------//
unsigned char loginFlag;

//-------------------------------------------------------------------------//
// 登出を开始した时のフラグ                                          //
//-------------------------------------------------------------------------//
BOOL logOutFlag = FALSE;



//-------------------------------------------------------------------------//

short createCharFlag = 0;
//-------------------------------------------------------------------------//


int giCreateRenewalFlg = 0;	// キャラ作成时にリニューアルかどうか
#ifdef PUK2
	int FaceFlg=0;
#endif

//-------------------------------------------------------------------------//
/*
#if !(_DEBUG)
BYTE PackageVer = PV_NORMAL;	// パッケージの布ジョン
#else
//BYTE PackageVer = PV_TRIAL;
//BYTE PackageVer = PV_NORMAL;
#endif
*/

// サーバーから指示されているバージョン
BYTE PackageVer = PV_NORMAL;

// インストールされているバージョン
int GetInstallVersion( void );

//-------------------------------------------------------------------------//

// サーバーの返すコード。この enum はサーバーと共通である。

enum{
	AUTH_OK = 0,				// エラー无し。成功。
	AUTH_ERROR_CDKEY = 1,		// ＣＤ－ＫＥＹ不良
	AUTH_ERROR_BADNAME = 2,		// ユーザー未登録。
	AUTH_ERROR_PASSWORD = 3,	// パスワード不良。
	AUTH_ERROR_EXPIRE = 4,		// 期限切れ
	AUTH_ERROR_LOCKOUT = 5,		// ＩＳＡＯよりロックアウト
	AUTH_ERROR_ISAO_DB = 6,		// ＩＳＡＯのＤＢが动いていない
	AUTH_ERROR_OTHER = 7,		// その他のエラー

	AUTH_ERROR_END
};


//-------------------------------------------------------------------------//
// ＩＤ??パスワード??ＣＤｋｅｙ入力处理                                    //
//-------------------------------------------------------------------------//

static INPUT_STR idKey;						// ユーザＩＤ入力バッファ
static INPUT_STR passwd;					// パスワード入力バッファ
static INPUT_STR cdKey;						// ＣＤキー入力バッファ
static INPUT_STR shiftKey;					// 卒业するのに必要な入力バッファ
static INPUT_STR *idPasswordFocus[] =
	{ &cdKey, &passwd };			// フォーカス切り替えようワークエリア
INPUT_STR *idPasswordStr = &cdKey;
static int idPasswordFocusSw = 0;			// 入力フォーカス切り替えスイッチ
static short idKeyBoxX, idKeyBoxY;			// ＩＤ入力バッファ表示座标
static short passwdBoxX, passwdBoxY;		// パスワード入力バッファ表示座标
static short cdKeyBoxX, cdKeyBoxY;			// ＣＤキー入力バッファ表示座标



ACTION *pActTitlLogo = NULL;
enum{
	E_IDPASS_INIT,
	E_IDPASS_WHITE,		// 白画面
	E_IDPASS_SANDCLOCK,	// 砂时计
	E_IDPASS_XGLOGO,	// ＸＧロゴ飞んでくる
	E_IDPASS_PRE_TITLE,
	E_IDPASS_SKIP_TITLE, // スキップしてタイトルへ
	E_IDPASS_TITLE,		// タイトル正式表示して入力モード
	E_IDPASS_ENTER,		// 入力された
	E_IDPASS_ENTER_ERR,	// 入力されたがエラー
	E_IDPASS_ENTER_ERR_WAIT, // ＯＫ待ち
	E_IDPASS_NEXT, 		// 次の画面へフェードイン
};
void V2_idPasswordProc( void )
{
	int flag = 2;
	int ret;
	static char msg[256];

	switch( SubProcNo ){
	case E_IDPASS_INIT:	// 初期化时
		DeathAllAction( );
		pActTitlLogo = NULL;	// ポインタ初期化

		SubProcNo = E_IDPASS_WHITE;	// 白にする
		PaletteChange( 30, 0 );		//
		// 砂时计表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_IDPASS_SANDCLOCK, 0 );
		break;

	case E_IDPASS_WHITE:	// 初期化时
		// 砂时计表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_IDPASS_SANDCLOCK, 0 );
		PaletteChange( 29, 60 );	// フェードイン
		SubProcNo = E_IDPASS_SANDCLOCK;	// 砂时计画面へ
		break;

	case E_IDPASS_SANDCLOCK:	// 砂时计画面
		// 砂时计表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_IDPASS_SANDCLOCK, 0 );
		if( palChageStatus == TRUE )break;	// フェードイン中
		// タイトルロゴアクション起こす
		pActTitlLogo = GetAction( PRIO_CHR, 0 );
		if( pActTitlLogo != NULL ){
			pActTitlLogo->anim_chr_no = V2_IDPASS_XG2LOGO;
			pActTitlLogo->anim_no = ANIM_STAND;
			pActTitlLogo->dispPrio = DISP_PRIO_CHAR;
			pActTitlLogo->x = 0;
			pActTitlLogo->y = 240;
			pActTitlLogo->anim_ang = 0;
			pattern( pActTitlLogo, ANM_NOMAL_SPD, ANM_LOOP );
			SubProcNo = E_IDPASS_XGLOGO;	// ロゴ飞ぶへ
		}else{
			SubProcNo = E_IDPASS_TITLE;	// 失败したら入力へ
		}
		break;

	case E_IDPASS_XGLOGO:	// ロゴ飞んでくる
		// 砂时计表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_IDPASS_SANDCLOCK, 0 );
		pActTitlLogo->x += 12;	// 移动
		if( pActTitlLogo->x < 320 )break;
		// 中央にきたら停止
		pActTitlLogo->x = 320;
		PaletteChange( 30, 6 );		// 白フェード
		SubProcNo = E_IDPASS_PRE_TITLE;	// タイトル表示へ
		break;

	case E_IDPASS_PRE_TITLE:	// タイトル表示へ
		// 砂时计表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_IDPASS_SANDCLOCK, 0 );
		if( palChageStatus == FALSE ){
			DeathAction( pActTitlLogo );
#ifdef PUK3_CHECK_VALUE
			pActTitlLogo = NULL;
#endif
			initInputIdPassword();
			SubProcNo = E_IDPASS_TITLE;	// タイトル表示へ
			PaletteChange( 29, 12 );	// フェードイン
			StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_ID_PASS_WINDOW, 0 );
		}
		break;

	case E_IDPASS_SKIP_TITLE:	// キャンセルされてタイトル表示へ
		PaletteChange( 29, 6 );	// フェードイン
		// 砂时计表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_IDPASS_SANDCLOCK, 0 );
		DeathAction( pActTitlLogo );
#ifdef PUK3_CHECK_VALUE
		pActTitlLogo = NULL;
#endif
		initInputIdPassword();
		SubProcNo = E_IDPASS_TITLE;	// タイトル表示へ
		PaletteChange( 29, 12 );	// フェードイン
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_ID_PASS_WINDOW, 0 );
		break;




	case E_IDPASS_TITLE:	// タイトル表示へ
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_ID_PASS_WINDOW, 0 );
		flag = 0;	// ＩＤ??パスワード入力可能フラグ
		break;

	case E_IDPASS_ENTER:	// 入力された
		if( palChageStatus == FALSE ){
			// フェードアウト１段阶目は通常のみ
			if( giInstallVersion == 0 ){
				PaletteChange( 18, 30 );
			}else{
				PaletteChange( 19, 60 );	// それ以外は通常フェードアウト
			}
			SubProcNo = E_IDPASS_NEXT;		// 次の画面へ
		}
		break;

	case E_IDPASS_ENTER_ERR:			// エラーの时
		initCommonMsgWin();
		// ウィンドウ开く音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
		SubProcNo = E_IDPASS_ENTER_ERR_WAIT;
		break;

	case E_IDPASS_ENTER_ERR_WAIT:	// エラーで确认待ち
		if( commonMsgWin( msg ) ){
			// ＯＫボタンが押された
			SubProcNo = E_IDPASS_TITLE;
		}
		break;

	case E_IDPASS_NEXT:		// 次の画面へ行くなら
//		inputIdPassword( 1 );	// 入力负荷で表示
		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_ID_PASS_WINDOW, 0 );
		if( palChageStatus == FALSE ){
			// タイトル画面へ
			ChangeProc( PROC_TITLE_MENU );
			DeathAllAction( );
			return;
		}
		break;


	}

	// 右键でスキップ
	if( SubProcNo < E_IDPASS_PRE_TITLE ){
		if( (mouse.onceState & MOUSE_LEFT_CRICK) ){
			SubProcNo = E_IDPASS_SKIP_TITLE;	// スキップ
		}
	}

	// 入力状态になったら
	if( SubProcNo != E_IDPASS_TITLE ){
		// フラグがFALSEなら入力できなくする
		GetKeyInputFocus( NULL );
		return ;
	}

	ret = inputIdPassword( 0 );
	if( ret == 1 ){
		// 入力されたID,PASSWDをとりあえずファイルに保存
#ifndef VERSION_TW
		//台服版取消id保存到SaveDat
		setId( idKey.buffer );
#endif
		setPassword( passwd.buffer );
		setCdkey( cdKey.buffer );
		if( saveNowState() )
		{
		}

		// 决定ａ
		play_se( SE_NO_OK, 320, 240 );
		// 决定时のパレットチェンジ
		if( giInstallVersion >= 1 ){
//			PaletteChange( 25, 30 );	// ＥＸ用
			PaletteChange( 30, 30 );	// ＥＸ用
		}else{
			PaletteChange( 17, 30 );
		}
		SubProcNo = E_IDPASS_NEXT;	// 次へ
	}
	else
	// 終了ボタンが押されたので終了处理
	if( ret == 2 ){
		// 返回音
		play_se( SE_NO_BACK, 320, 240 );
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
	}
	//-------------------------------------------------------------
	// ID,PASSWORDが空の时のエラー信息
	else
	if( ret < 0 ){
		SubProcNo = E_IDPASS_ENTER_ERR;
		strcpy( msg, ML_STRING(263, "请输入帐号和密码") );
	}

}

void idPasswordProc( void )
{
	int flag = 2;
	int ret;
	static char msg[256];

	// 初期化
	if( SubProcNo == 0 )
	{
		SubProcNo++;
		// インストールバージョンセット
		giInstallVersion = GetInstallVersion( );
		initInputIdPassword();
		// パレット设定
#ifdef PUK2
		if( giInstallVersion == 3 ){
			PaletteChange( 29, 30 );	// Ｖ２用
		}else
#endif
		if( giInstallVersion == 2 ){
			PaletteChange( 29, 30 );	// Ｖ２用
		}else
		if( giInstallVersion == 1 ){
			PaletteChange( 28, 30 );	// ＥＸ用
		}else{
			PaletteChange( 16, 30 );
		}

	}
	else
	if( SubProcNo == 1 )
	{

		if( palChageStatus == FALSE )
		{
			SubProcNo++;
		}
	}
	else
	if( SubProcNo == 2 )
	{
		flag = 0;	// ＩＤ??パスワード入力可能フラグ
	}
	else
	if( SubProcNo == 3 )
	{
		if( palChageStatus == FALSE )
		{
			// フェードアウト１段阶目は通常のみ
			if( giInstallVersion == 0 ){
				PaletteChange( 18, 30 );
			}else{
				PaletteChange( 19, 60 );	// それ以外は通常フェードアウト
			}
			SubProcNo++;
		}
	}
	else
	if( SubProcNo == 4 )
	{
		if( palChageStatus == FALSE )
		{
			PaletteChange( 19, 30 );
			SubProcNo++;
		}
	}
	else
	if( SubProcNo == 5 )
	{
		if( palChageStatus == FALSE )
		{
			// タイトル画面へ
			ChangeProc( PROC_TITLE_MENU );
		}
	}

	ret = inputIdPassword( flag );

	if( ret == 1 )
	{
		// 入力されたID,PASSWDをとりあえずファイルに保存
#ifndef VERSION_TW
		//台服版取消id保存到SaveDat
		setId( idKey.buffer );
#endif
		setPassword( passwd.buffer );
		setCdkey( cdKey.buffer );
		if( saveNowState() )
		{
		}

		// 决定ａ
		play_se( SE_NO_OK, 320, 240 );
		// 决定时のパレットチェンジ
		if( giInstallVersion == 1 ){
//			PaletteChange( 25, 30 );	// ＥＸ用
			PaletteChange( 19, 30 );	// ＥＸ用
		}else{
			PaletteChange( 17, 30 );
		}
		SubProcNo = 3;
	}
	else
	// 終了ボタンが押されたので終了处理
	if( ret == 2 )
	{
		// 返回音
		play_se( SE_NO_BACK, 320, 240 );
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
	}

	//-------------------------------------------------------------
	// ID,PASSWORDが空の时のエラー信息
	else
	if( ret < 0 )
	{
		SubProcNo = 100;
		strcpy( msg, ML_STRING(263, "请输入帐号和密码") );
	}

	// エラー信息
	if( SubProcNo == 100 )
	{
		initCommonMsgWin();
		// ウィンドウ开く音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
		SubProcNo++;
	}
	if( SubProcNo == 101 )
	{
		if( commonMsgWin( msg ) )
		{
			// ＯＫボタンが押された
			SubProcNo = 1;
		}
	}
}


// ＩＤ??パスワード入力バッファ初期化
void initInputIdPassword( void )
{
	char str[256] = {0};

	// インストールしているバージョンを调べる
	giInstallVersion = GetInstallVersion( );

	// 新しいバージョンはこちら
	if( giInstallVersion >= 1 ){
		// ID入力バッファ初期化
		//idKeyBoxX = 195+4;
		//idKeyBoxY = 344+3;
		idKeyBoxX = 0;
		idKeyBoxY = 0;
		idKey.buffer[0] = '\0';
		idKey.cnt = 0;
		idKey.cursorByte = 0;
#ifdef _DEBUG
		getId( str );
		InitInputStr( &idKey, idKeyBoxX, idKeyBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
			FONT_PAL_BLUE, str, 1, 40, 0, 0 );
#endif
		// パスワード入力バッファ初期化
		passwdBoxX = 195+4;
		passwdBoxY = 375+3;
		passwd.buffer[0] = '\0';
		passwd.cnt = 0;
		passwd.cursorByte = 0;
#ifdef _DEBUG
		getPassword( str );
#endif
		InitInputStr( &passwd, passwdBoxX, passwdBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
			//FONT_PAL_BLUE, str, 1, 8, 0, 0 );	// ohta
			FONT_PAL_BLUE, str, 1, 20, 0, TRUE );

		// ＣＤキー入力バッファ初期化
		cdKeyBoxX = 195+4;
		//cdKeyBoxY = 406+3;
		cdKeyBoxY = 334+3;
		cdKey.buffer[0] = '\0';
		cdKey.cnt = 0;
		cdKey.cursorByte = 0;
#ifdef _DEBUG
		getCdkey( str );
#endif
		InitInputStr( &cdKey, cdKeyBoxX, cdKeyBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
			FONT_PAL_BLUE, str, 1, 23, 0, 0 );
	}else{
		// ID入力バッファ初期化
		//idKeyBoxX = 204;
		//idKeyBoxY = 288;
		idKeyBoxX = 0;
		idKeyBoxY = 0;
		idKey.buffer[0] = '\0';
		idKey.cnt = 0;
		idKey.cursorByte = 0;
#ifdef _DEBUG
		getId( str );
//		InitInputStr( &idKey, idKeyBoxX, idKeyBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE, FONT_PAL_BLUE, str, 1, 40, 0, 0 );
#endif
		// パスワード入力バッファ初期化
		passwdBoxX = 204;
		passwdBoxY = 319;
		passwd.buffer[0] = '\0';
		passwd.cnt = 0;
		passwd.cursorByte = 0;
#ifdef _DEBUG
		getPassword( str );
#endif
		InitInputStr( &passwd, passwdBoxX, passwdBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
			//FONT_PAL_BLUE, str, 1, 8, 0, 0 );	// ohta
			FONT_PAL_BLUE, str, 1, 20, 0, TRUE );
//	passwd.blindFlag = TRUE;	// 入力を隠すフラグ

		// ＣＤキー入力バッファ初期化
		cdKeyBoxX = 204;
//		cdKeyBoxY = 350;
		cdKeyBoxY = 288;
		cdKey.buffer[0] = '\0';
		cdKey.cnt = 0;
		cdKey.cursorByte = 0;
//		getCdkey( str );
		InitInputStr( &cdKey, cdKeyBoxX, cdKeyBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
			FONT_PAL_BLUE, str, 1, 23, 0, 0 );
	}

	// フォーカス设定
	idPasswordFocusSw = 0;
	GetKeyInputFocus( idPasswordFocus[idPasswordFocusSw] );
}



#if defined(PUK2)&&defined(_DEBUG)
	extern int setInstallVersion;
#endif
// ここではある画像番号を使ってインストールされているバージョンを决定する。
int GetInstallVersion( void ){
#ifdef MULTI_GRABIN
	int aiJudgeImageTbl[] = {
#ifdef PUK3_UPGRADE
		GID_Puk3TitleOpening4,	// PUK3
#endif
#ifdef PUK2
		PUK2_TITLE,	// PUK2
#endif
		V2_ID_PASS_WINDOW,	// V2
		EX_ID_PASS_WINDOW,	// EX
		CG_ID_PASS_WINDOW		// 通常
	};
	U4 BmpNo;
	int i, iVersion = 0,iMax = sizeof( aiJudgeImageTbl ) / sizeof( aiJudgeImageTbl[0] );
	int ErrCheckFlg = 0;

#if defined(PUK2)&&defined(_DEBUG)
	if (setInstallVersion>=0) return setInstallVersion;
#endif
	// Ｖ２のローディング画面ある？
	for( i = 0; i < iMax; i ++ ){
		// その画像ある？
		realGetNo( aiJudgeImageTbl[i], (U4 *)&BmpNo );
		if( BmpNo > 0 ){	// あるみたいだ。
			iVersion = i;
			break;
		}
	}
	// それより下のバージョンもある？
	{
#ifdef PUK2
		for( ; i < iMax; i ++ ){
			// その画像ある？
			realGetNo( aiJudgeImageTbl[i], (U4 *)&BmpNo );
			if( BmpNo > 0 ){	// あるみたいだ。
				iVersion = i;
				break;
			}
		}
		if (i >= iMax){
			ErrCheckFlg = 1;	// ないよお。インストールエラーだ。
			return -1;
		}
#else
		// その画像ある？
		realGetNo( aiJudgeImageTbl[2], (U4 *)&BmpNo );
		if( BmpNo > 0 ){	// あるみたいだ。
		}else{
			ErrCheckFlg = 1;	// ないよお。インストールエラーだ。
			return -1;
		}
#endif

	}
#ifdef PUK3_UPGRADE
	return (iMax-1) - i;
#else
	// 见つからなかったらしかたないから通常バージョンということで
	switch( iVersion ){
#ifdef PUK2
	case 0:	// 最初のやつがみつかったらPUK2である。
		return 3;
	case 1:	// 次のがみつかったらバージョン２である。
		return 2;
	case 2:	// 次の次がみつかったらバージョンＥＸである。
		return 1;
#else
	case 0:	// 最初のやつがみつかったらバージョン２である。
		return 2;
	case 1:	// 次のがみつかったらバージョンＥＸである。
		return 1;
#endif

	default:	// それ以外は通常バージョン
		return 0;
	}
#endif
#else
	return 0;
#endif

}


	GRA_BTN_INFO2 IDPASS_okBtn_Ex =
	{// 表示座标(x,y)
		320, 240, //380, 270,
	 // 范围始点座标(x,y)
		379, 273,
	 // 范围幅(w,h)
		444-379, 307-273,
	 // ボタン画像番号(凸画像,凹画像)
		0, EX_ID_PASS_OK
	};
	// ＱＵＩＴボタン
	GRA_BTN_INFO2 IDPASS_quitBtn_Ex =
	{// 表示座标(x,y)
		320, 240, // 492, 270,
	 // 范围始点座标(x,y)
		492, 273,
	 // 范围幅(w,h)
		592-492, 312-273,
	 // ボタン画像番号(凸画像,凹画像)
		0, EX_ID_PASS_QUIT
	};

// 入力处理??画面表示
//
//  戾り值：	 0 ... 入力中
//				 1 ... 入力完了
//				 2 ... 終了ボタン
//				-1 ... 入力栏が空
int inputIdPassword( int flag )
{
#ifdef PUK2
	int aiIDPassGraNo[] = {
		CG_ID_PASS_WINDOW,
		EX_ID_PASS_WINDOW,
		V2_ID_PASS_WINDOW,
		V2_ID_PASS_WINDOW,
	};
#else
	int aiIDPassGraNo[] = { CG_ID_PASS_WINDOW, EX_ID_PASS_WINDOW, V2_ID_PASS_WINDOW };
#endif
	int id;
	int selUseFlag;
	static BOOL flag2 = FALSE;
	static int oldId = 0;
	int ret = 0;
	int x1, y1, x2, y2;
	// ＯＫボタン
	GRA_BTN_INFO2 okBtn =
	{// 表示座标(x,y)
		243, 427,
	 // 范围始点座标(x,y)
		219, 405,
	 // 范围幅(w,h)
		48, 44,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_ID_PASS_OK
	};
	// ＱＵＩＴボタン
	GRA_BTN_INFO2 quitBtn =
	{// 表示座标(x,y)
		375, 427,
	 // 范围始点座标(x,y)
		339, 405,
	 // 范围幅(w,h)
		72, 44,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_ID_PASS_QUIT
	};
	GRA_BTN_INFO2 *pOkBtn = &okBtn, *pQuitBtn = &quitBtn;

	// インストールしているバージョンを调べる
	giInstallVersion = GetInstallVersion( );

	if( giInstallVersion >= 1 ){	// ＥＸだったら
		pOkBtn = &IDPASS_okBtn_Ex;
		pQuitBtn = &IDPASS_quitBtn_Ex;
	}

	// ボタン等の选择可??不可の设定
	if( flag == 0 )
	{
		selUseFlag = 0;	// ボタンの选择可
	}
	else
	{
		selUseFlag = 1;	// ボタンの选择不可
	}


	// ＯＫボタンが押された
	if( (pushGraBtnInfo2( pOkBtn ) & BTN_LEFT_CLICK)
	 && !selUseFlag )
	{
		// ID??パスワード??CDキーが设定されているか
		if( strlen( idKey.buffer ) > 0
		 && strlen( passwd.buffer ) > 0
		 && strlen( cdKey.buffer ) > 0 )
		{
			idKey.buffer[idKey.cnt] = '\0';
			passwd.buffer[passwd.cnt] = '\0';
			cdKey.buffer[cdKey.cnt] = '\0';
			ret = 1;
		}
		else
		{
			// 设定されていない
			ret = -1;
		}
	}
	else
	// 終了ボタンが押された
	if( (pushGraBtnInfo2( pQuitBtn ) & BTN_LEFT_CLICK)
	 && !selUseFlag )
	{
		ret = 2;
	}

	if( flag < 2 )
	{
		StockFontBuffer2( &idKey );
		StockFontBuffer2( &passwd );
		StockFontBuffer2( &cdKey );
	}

	id = -1;

	// 入力フォーカス变更
	if( keyOnRep( VK_TAB )
	 || keyOnRep( VK_RETURN ) )
	{
		if( oldId == 0 )
		{
			id = 1;
		}
		else
		if( oldId == 1 )
		{
			id = 2;
		}
		else
		if( oldId == 2 )
		{
			id = 0;
		}
	}

	if( !selUseFlag )
	{
		// ＥＸ以后だったらこっちのメニュー
		if( giInstallVersion >= 1 ){
			x1 = idKeyBoxX-5;
			y1 = idKeyBoxY-2;
			x2 = x1 + 368+2;
			y2 = y1 + 22;
			if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
				id = 0;
			}
			x1 = passwdBoxX-5;
			y1 = passwdBoxY-2;
			x2 = x1 + 224+2;
			y2 = y1 + 22;
			if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
				id = 1;
			}
			x1 = cdKeyBoxX-5;
			y1 = cdKeyBoxY-2;
			x2 = x1 + 224+2;
			y2 = y1 + 22;
			if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
				id = 2;
			}
		}else{
			x1 = idKeyBoxX-5;
			y1 = idKeyBoxY-2;
			x2 = x1 + 368;
			y2 = y1 + 22;
			if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
				id = 0;
			}
			x1 = passwdBoxX-5;
			y1 = passwdBoxY-2;
			x2 = x1 + 224;
			y2 = y1 + 22;
			if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
				id = 1;
			}
			x1 = cdKeyBoxX-5;
			y1 = cdKeyBoxY-2;
			x2 = x1 + 224;
			y2 = y1 + 22;
			if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
				id = 2;
			}
		}
	}


	if( flag > 0 )
	{
		// フラグがFALSEなら入力できなくする
		GetKeyInputFocus( NULL );
		flag2 = TRUE;
	}
	else
	if( (0 <= id && id <= 2)
	 || flag2 )
	{
		if( flag2 )
			id = oldId;
		GetKeyInputFocus( idPasswordFocus[id] );
		flag2 = FALSE;
		oldId = id;
	}

	// ＯＫボタン
	drawGraBtnInfo2( pOkBtn, DISP_PRIO_BG+1, 0, 0, selUseFlag );

	// ＱＵＩＴボタン
	drawGraBtnInfo2( pQuitBtn, DISP_PRIO_BG+1, 0, 0, selUseFlag );

	// ＢＧ表示


	{ int iIdPassGraNo;
		iIdPassGraNo = aiIDPassGraNo[giInstallVersion];	// その画像で行こう
		StockDispBuffer( 320, 240, DISP_PRIO_BG, aiIDPassGraNo[giInstallVersion], 0 );
	}

	// バージョン表示
#ifdef _TAIKEN
	StockFontBuffer( 480, 424, FONT_PRIO_BACK, FONT_PAL_BLUE, "体验版", 0 ); //MLHIDE
#endif
#ifdef PUK2_NEWVER
	if( giInstallVersion >= 1 ){
		StockFontBuffer( 460, 424, FONT_PRIO_BACK, FONT_PAL_BLUE, NR_VERSION, 0 );
	}else{
		StockFontBuffer( 460, 424, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
	}
#else
	if( giInstallVersion >= 1 ){
		StockFontBuffer( 540, 424, FONT_PRIO_BACK, FONT_PAL_BLUE, NR_VERSION, 0 );
	}else{
		StockFontBuffer( 540, 424, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
	}
#endif

	return ret;
}


//*******************************************************************
//折り返し文字列を设定する
//
//char* 描画したい不特定な文字列
//WORD	初期座标
//WORD	初期座标
//char  何文字で改行するかを指定
//char  １行の幅の长さを指定
//char	色を指定する
//※文字列は「|」の记号でも改行を行ってくれる
//
//*******************************************************************
void SetlapelString(char *String,WORD XPos,WORD YPos,WORD TurnPt,
										char LengthPt, char Setcolor){


	char Font[2] = "";	//追加する文字
	char Str[256] = "";	//１行で表示する文字列
	int  StrNum = 0;	//全体の文字の长さを测る
	CHAR Xpt = 0;		//Ｘ轴の文字の长さを测る
	CHAR Ypt = 0;

	int Len = strlen(String);//文字の长さを测る

	if(TurnPt >= 80)	//配列の制限
		TurnPt = 79;

	while( StrNum < Len){

		Font[0] = String[StrNum];

		if( (Font[0] != '|') && (Xpt < TurnPt) )
		{
			//Xpt以下か ｜ 以外なら文字列追加
			strncat(Str,Font,1);//文字列の追加
			Xpt++;

		}else{

			//XptがTurnptを超えるか | 文字が入った场合くる
			Xpt = 0;	//横幅の计算
			Ypt++;		//縦幅の计算

			StockFontBuffer(XPos,YPos + (Ypt * LengthPt),FONT_PRIO_BACK,Setcolor,Str,0);

			//バッファにためた后はクリア
			Str[0] = '\0';

		}

		StrNum++;	//全体の文字の长さを计算

	}

	Ypt++;

	StockFontBuffer(XPos,YPos + (Ypt * LengthPt),FONT_PRIO_BACK, Setcolor,Str,0);


};
/*
//******************************************************************
//[ProbationProc]卒业フォーム管理部分
//												 H14.01.17 By Kadoya
//
//
//******************************************************************
void ProbationProc( void )
{
	int Inputflag = 1;
	int ret = 0;
	static char msg[256]="";
	char str[256]="";
	short shiftBoxX = 272,	shiftBoxY = 378;
	static BYTE NowPage = 1;

	//ページ进み用矢印
	GRA_BTN_INFO1 leftBtn =
	{// 表示座标(x,y)
		274,334,
	 // 范围始点座标(x,y)
		264,324,
	 // 范围幅(w,h)
		22, 22,
	 // ボタン画像番号(凸画像,凹画像)
		CG_LEARN_ALLOW_DOWN_L, CG_LEARN_ALLOW_L
	};
	GRA_BTN_INFO1 rightBtn =
	{// 表示座标(x,y)
		343,334,
	 // 范围始点座标(x,y)
		333,324,
	 // 范围幅(w,h)
		22, 22,
	 // ボタン画像番号(凸画像,凹画像)
		CG_LEARN_ALLOW_DOWN_R, CG_LEARN_ALLOW_R
	};


	//サブフラグを见て、处理を变える
	switch (SubProcNo){

		case 0:

			shiftKey.buffer[0] = '\0';
			shiftKey.cnt = 0;	shiftKey.cursorByte = 0;

			//getCdkey( str );	//保存フォームからCDKEYをとって来る

			//どこにどれくらいの文字を入れるかを设定
			InitInputStr( &shiftKey, shiftBoxX, shiftBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
				FONT_PAL_BLUE, str, 1, 23, 0, 0 );


			// フォーカス设定
			GetKeyInputFocus( &shiftKey );

			//パレットの变更
			PaletteChange( 0, 0 );
			SubProcNo = 1;

			break;
		case 1:
			//パレットが变化中かどうかを调べる
			if( palChageStatus == FALSE )
			{
				SubProcNo = 2;
			}
			break;
		//------------------------------------------------
		case 2:
			//入力を受け付けるようになる
			Inputflag = 0;
			break;
		//-------------------------------------------------
		case 3:

			//サーバーにCD-KEYを送る
			nrproto_PVUP_send(sockfd,shiftKey.buffer);

			RecvVerData = 128;
			SubProcNo = 4;


			break;

		case 4://サーバーからの受信データ待ち

			//サーバーから返ってきたデータを判断
			if(RecvVerData == 1){
				SubProcNo = 8;	//成功

			}else if(RecvVerData == 0){
				SubProcNo = 11;//失败
			}

			break;

		case 5:	//サーバーにつなげ、认证中の处理（ユーザーはなにもできない）

			strcpy(msg,"与服务器连接中");
			initCommonMsgWin();
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			SubProcNo = 6;
			break;

		case 6:
			if( commonMsgWin( msg ) )
			{
				// ＯＫボタンが押された
				SubProcNo = 7;
			}

			break;
		case 7:

			break;
		//-------------------------------------------------

		case 8:	//卒业が成功した场合にはここにくる


			strcpy( msg, "更新成功" );
			initCommonMsgWin();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );

			SubProcNo = 9;
			break;

		case 9:	//ウインドウ信息处理

			if( commonMsgWin( msg ) )
			{
				// ＯＫボタンが押された(前のプロセスに戾される)
				SubProcNo = 10;
			}

			break;

		case 10://设定が成功し、信息ウインドウでOKを押した场合

			if( palChageStatus == FALSE )
			{
				//CDKEYのセーブ
				setCdkey( shiftKey.buffer );
				// ネットワーク終了处理
				cleanupNetwork();

				ChangeProc2( PROC_ID_PASSWORD );
				// 黒パレットに变更
				PaletteChange( 19, 1 );
				// 返回音
				play_se( SE_NO_BACK, 320, 240 );
				// ＢＧＭフェードアウト开始
				//fade_out_bgm();
				SubProcNo = 0;
				NowPage = 1;
			}

			break;
		//------------------------------------------------------
		case 11://CD-Keyに误りがあった场合
			strcpy( msg, "账号有错。" );
			initCommonMsgWin();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );

			SubProcNo = 12;
			break;

		case 12://11の続き、信息ウインドウが出ている状态

			if( commonMsgWin( msg ) )
			{
				// ＯＫボタンが押された(前のプロセスに戾される)
				SubProcNo = 13;
				shiftKey.buffer[0] = '\0';
				shiftKey.cnt = 0;	shiftKey.cursorByte = 0;

				strcpy( msg, "请注意大小写和书号输入？" );
				initCommonMsgWin();

			}
			break;

		case 13://间违い补助ウインドウをあらわす

			if( commonMsgWin( msg ) )
			{
				// ＯＫボタンが押された(前のプロセスに戾される)
				SubProcNo = 2;

			}
			break;


		//--------------------------------------------------------
		case 100:
			initCommonMsgWin();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			SubProcNo = 101;
			break;

		case 101:
			if( commonMsgWin( msg ) )
			{
				// ＯＫボタンが押された(前のプロセスに戾される)
				SubProcNo = 1;
			}
			break;

		//--------------------------------------------------------
		case 102:	//障害が起きた场合
			strcpy( msg, "通信错误，请再次输入" );
			initCommonMsgWin();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			SubProcNo = 12;
			break;


	}


	//-----------------------------------------------------------------------------------

	//戾り值：0.入力中 | 1.入力完了 | 2.終了ボタン | -1.入力栏が空
	//每フレームチェック　OKボタン、」Quitボタンの描画、判定もここで行う
	//●入力制御关数へ
	ret = InputProbationKey(Inputflag);

	if( ret == 1 )
	{

		// 决定
		play_se( SE_NO_OK, 320, 240 );

		// つぎのステップに进めるようになる（入力受付后）
		SubProcNo = 3;

	}else
	if( ret == 2 )//Quit（返回）ボタンを押したときの处理
	{
		PaletteChange( 0, 0 );
		NowPage = 1;

		// 返回音
		play_se( SE_NO_BACK, 320, 240 );

		//キャラ选择画面に返回
		ChangeProc( PROC_CHAR_SELECT );

	}
	else
	// ID,PASSWORDが空の时のエラー信息
	if( ret < 0 )
	{
		SubProcNo = 100;
		strcpy( msg, "设定账号。" );
	}


	//-------------------------------------------------------------------------------------
	char otamesi[] =
"  这个画面中将会进行「新手版」到「普通版」|"
"的升级手续，。请输入「普通版」的|"
"账号，然后按OK进行确认。「新手版」|"
"的角色将能在「普通版」中正常使用。|"
"另外，没有「普通版」的用户将不能从「新手版」|"
"升级到「普通版」，请注意。|"
"| 下一页";


	char otamesi2[] =
"【注意事项】||"
"※更新到「普通版」，「新手版」的已经注册的|"
"   isao-ID会继承下来l。||"
"※更新到「普通版」，已经购入的|"
"  「新手版」剩下的时长会继承到「普通版」|"
"  中去。|"
"|| 下一页";


	char otamesi3[]=
"  ※更新到「普通版」之后，如果要再玩「新手版」|"
"     需要重新注册新的isao-ID。 ||"
"  ※一旦更新到了「普通版」，将无法降回|"
"     到「新手版」。 ||"
"                        下一页";

	char otamesi4[]=
"  ※从「新手版」升级到「普通版」只有一次|"
"     机会。||"
"  ※已经注册过的「普通版」CD-KEY|"
"    无法输入。";

	char StrNowPage[80] = "";


	char *Addless = otamesi;

	const char MaxPage = 4;


	sprintf(StrNowPage,"%d / %d",NowPage,MaxPage);

	// 左ボタン押したか？
	if(NowPage > 1 && pushGraBtnInfo1( &leftBtn ) & BTN_LEFT_CLICK_REP)
	{
		NowPage--;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// 右ボタン押したか？
	if(NowPage < MaxPage && pushGraBtnInfo1( &rightBtn ) & BTN_LEFT_CLICK_REP)
	{
		NowPage++;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}

	//--------------------------------------------------------------------------------------
	switch(NowPage){

		case 1:
			Addless = otamesi;		//文字列の先头ポインタを渡す
			break;
		case 2:
			Addless = otamesi2;		//文字列の先头ポインタを渡す
			break;
		case 3:
			Addless = otamesi3;
			break;
		case 4:
			Addless = otamesi4;
			break;
		default:
			Addless = otamesi;		//文字列の先头ポインタを渡す

	}


	StockFontBuffer( 289, 328, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,StrNowPage, 0, 0 );



	//●折り返し处理を入れた文字列の描画
	SetlapelString(Addless,100,94,100,18,FONT_PAL_WHITE);

	// 左ボタン表示
	if(NowPage != 1)
		drawGraBtnInfo1( &leftBtn, DISP_PRIO_CHAR, 0, 0, 0 );
	// 右ボタン表示
	if(NowPage != MaxPage)
		drawGraBtnInfo1( &rightBtn, DISP_PRIO_CHAR, 0, 0, 0 );

}


//******************************************************************
//[InputProbationKey]	卒业キーの入力关数
//											   H14.01.17	By Kadoya
//引数には　0か２が入っている？
//
//戾り值：	 0.入力中  1.入力完了  2.終了ボタン -1.入力栏が空
//******************************************************************
int InputProbationKey(int flag){

	//int selUseFlag;				//ボタンの选择条件
	static BOOL flag2 = FALSE;
	static int oldId = 0;
	int ret = 0;
	//int x1, y1, x2, y2;


	// ＯＫボタン
	GRA_BTN_INFO2 okBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		180, 427,
	 // 范围幅(w,h)
		70,  35,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_LEARN_PASS_OK
	};
	// ＢＡＣＫボタン
	GRA_BTN_INFO2 quitBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		336, 430,
	 // 范围幅(w,h)
		112,  34,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_LEARN_PASS_BACK
	};


	// ＯＫボタンが押された
	if( (pushGraBtnInfo2( &okBtn ) & BTN_LEFT_CLICK)
	 && !flag )
	{
		// ID??パスワード??CDキーが设定されているか
		if( strlen( shiftKey.buffer ) > 0)
		{
			//バッファの最后に終了マークを付ける
			shiftKey.buffer[shiftKey.cnt] = '\0';
			ret = 1;
		}
		else
		{
			// 设定されていない
			ret = -1;
		}

	}
	else
	// 終了ボタンが押された
	if( (pushGraBtnInfo2( &quitBtn ) & BTN_LEFT_CLICK) && !flag )
	{
		ret = 2;
	}


	//文字表示用意
	StockFontBuffer2( &shiftKey );


	//ボタンの入力受付状态のみ文字を表示する
	if(flag == 0)
	{

		// ＯＫボタン
		drawGraBtnInfo2( &okBtn, DISP_PRIO_BG, 0, 0, flag );

		// ＱＵＩＴボタン
		drawGraBtnInfo2( &quitBtn, DISP_PRIO_BG, 0, 0, flag );
	}

	// ＢＧ表示
	StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_LEARN_PASS_BASE, 0 );

	return ret;


}
*/




// 标准信息ウィンドウ（ＯＫボタンあり）
//
static short commonMsgWinProcNo = 0;

// 初期化
void initCommonMsgWin( void )
{
	commonMsgWinProcNo = 0;
}

// 标准信息ウィンドウ处理
//
//  引数：		msg ... 表示する信息
//
//  戾り值：	0 ... 处理中
//				1 ... OKボタンが押された
int commonMsgWin( char *msg )
{
	static int fontId[] = { -2 };
	static ACTION *ptActMenuWin = NULL;
	int id;
	int i;
	static int x, y, w, h;
	int ret = 0;
	char *okBtn = "ＯＫ";                                                  //MLHIDE
#ifdef PUK2_NEW_MENU
	int gyo;
	int maxw;
	char over = 0;
	char *bp, *p;
	char str[256];
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;
#endif

#ifdef PUK2
	maxw = 0;
	strcpy( str, msg );
	bp = str;
	for(gyo=1;;gyo++){
		p = strchr( bp, '\n' );

		if (!p){
			i = GetStrWidth( bp, FONT_KIND_MIDDLE );
			if ( i > maxw ) maxw = i;
			break;
		}

		p[0] = '\0';

		i = GetStrWidth( bp, FONT_KIND_MIDDLE );
		if ( i > maxw ) maxw = i;

		bp = p + 1;
	}
#endif

	// 初期化
	if( commonMsgWinProcNo == 0 )
	{
		commonMsgWinProcNo = 1;

		for( i = 0; i < sizeof( fontId )/sizeof( int ); i++ )
		{
			fontId[i] = -2;
		}

		// ウィンドウ作成
#ifdef PUK2
		w = maxw+32;
#else
		w = GetStrWidth( msg, FONT_KIND_MIDDLE )+32;
#endif
		w = (w+63)/64*64;
#ifdef PUK2
		h = 48*2 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2) * (gyo-1);
#else
		h = 48*2;
#endif
		//根据窗口分辨率计算无法连接服务器的文字显示位置
		x = SymOffsetX + (640 - w)/2;
		y = SymOffsetY + (480 - h)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w, h, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w, h, 1 );
#endif
	}

#ifdef PUK2_NEW_MENU
	i = (w-66)>>1;
	if ( MakeHitBox( x+i, y+61 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1),
		x+i+66, y+61 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1)+17, -1 ) ) over = 1;

	// 右键されてなければ終了
	id = -1;
	if( over && (mouse.onceState & MOUSE_LEFT_CRICK) ) id = 0;
#else
	// フォントの选择判定
	id = selFontId( fontId, sizeof( fontId )/sizeof( int ) );
#endif

	// ＯＫ
	if( id == 0 )
	{
		ret = 1;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}


	// 结果がわかったらウィンドウ关闭
	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
			return ret;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int xx;

#ifdef PUK2
			xx = (w - maxw)/2;
			p = str;
			for(i=0;i<gyo;i++){
				StockFontBuffer( x+xx, y+30 + i*(FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2),
					FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, p, 0, 0 );

				p += strlen(p) + 1;
			}
#else
			xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
			StockFontBuffer( x+xx, y+30, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
				msg, 0, 0 );
#endif

#ifdef PUK2_NEW_MENU
	#ifdef PUK2
			StockDispBuffer( x+(w>>1), y+61+8 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1),
				DISP_PRIO_WIN2, (over ? GID_BigOKButtonOver : GID_BigOKButtonOn ), 0, &bm );
	#else
			StockDispBuffer( x+(w>>1), y+61+8, DISP_PRIO_WIN2, (over ? GID_BigOKButtonOver : GID_BigOKButtonOn ), 0, &bm );
	#endif
#else
			xx = (w - GetStrWidth( okBtn, FONT_KIND_MIDDLE ))/2;
			fontId[0] =
				StockFontBuffer( x+xx, y+56,
					FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_YELLOW, okBtn, 2, BoxColor );
#endif
		}
	}

	return ret;
}


// 文字入力バッファを初期化する
//  引数:	INPUT_STR *pt;	... 文字列バッファ构造体のポインタ
//			int x, y;		... 表示座标
//			int len;		... 入力バッファのサイズ
//			int color;		... 表示色
//			int prio;		... 表示优先度
void initInputStr( INPUT_STR *pt, int x, int y, int len, int color, int prio )
{
#ifdef PUK2
	pt->dispWidth = 0;				// 通常は表示幅を无制限に
	pt->dispByte = 0;					// （概念上の）表示开始位置は文字列の先头から
#endif
	pt->x        = x;
	pt->y        = y;
	//pt->len      = len;
	pt->color    = color;
	pt->fontPrio = prio;
}

// 文字入力バッファを初期化する
//  引数:	INPUT_STR *pt;	... 文字列バッファ构造体のポインタ
//			int x, y;		... 表示座标
//			int len;		... 入力バッファのサイズ
//			int color;		... 表示色
//			int prio;		... 表示优先度
void initInputStrNewChat( INPUT_STR *pt, int x, int y, int color, int prio ,int Kind)
{
	pt->x        = x;
	pt->y        = y;
	pt->color    = color;
	pt->fontPrio = prio;
	pt->fontKind = Kind;
}


// グラフィックの选择判定
//   マウスで选择され右键された番号を返す
//  引数:	int *id;	... チェックするグラフィックＩＤ配列
//			int cnt;	... ＩＤの数
//  戾り值:	-1			... どれにもあたってない
int selGraId( int *id, int cnt )
{
	int i;

	// 右键されてなければ終了
	if( (mouse.onceState & MOUSE_LEFT_CRICK) == 0 )
		return -1;

	for( i = 0; i < cnt; i++ )
	{
		if( id[i] == HitDispNo )
		{
			return i;
		}
	}

	return -1;
}


// グラフィックの选择判定（リピート付き）
//   マウスで选择され右键された番号を返す
//  引数:	int *id;	... チェックするグラフィックＩＤ配列
//			int cnt;	... ＩＤの数
//  戾り值:	-1			... どれにもあたってない
int selRepGraId( int *id, int cnt )
{
	int i;

	// 右键されてなければ終了
	if( (mouse.autoState & MOUSE_LEFT_CRICK) == 0 )
		return -1;

	for( i = 0; i < cnt; i++ )
	{
		if( id[i] == HitDispNo )
		{
			return i;
		}
	}

	return -1;
}


// グラフィックの选择判定
//   マウスで选择され左ボタンが押されている番号を返す
//  引数:	int *id;	... チェックするグラフィックＩＤ配列
//			int cnt;	... ＩＤの数
//  戾り值:	-1			... どれにもあたってない
int pushGraId( int *id, int cnt )
{
	int i;

	// 左が押されてなければ終了
	if( (mouse.state & MOUSE_LEFT_CRICK) == 0 )
		return -1;

	for( i = 0; i < cnt; i++ )
	{
		if( id[i] == HitDispNo )
		{
			return i;
		}
	}

	return -1;
}


// フォントの选择判定
//   マウスで选择され右键された番号を返す
//  引数:	int *id;	... チェックするフォントＩＤ配列
//			int cnt;	... ＩＤの数
//  戾り值:	-1			... どれにもあたってない
int selFontId( int *id, int cnt )
{
	int i;

	// 右键されてなければ終了
	if( (mouse.onceState & MOUSE_LEFT_CRICK) == 0 )
		return -1;

	for( i = 0; i < cnt; i++ )
	{
		if( id[i] == HitFontNo )
		{
			return i;
		}
	}

	return -1;
}


// グラフィックのフォーカス判定
//   マウスカーソルのあたっている番号を返す
//  引数:	int *id;	... チェックするグラフィックＩＤ配列
//			int cnt;	... ＩＤの数
//  戾り值:	-1			... どれにもあたってない
int focusGraId( int *id, int cnt )
{
	int i;

	for( i = 0; i < cnt; i++ )
	{
		if( id[i] == HitDispNo )
		{
			return i;
		}
	}

	return -1;
}


// フォントのフォーカス判定
//   マウスカーソルのあたっている番号を返す
//  引数:	int *id;	... チェックするフォントＩＤ配列
//			int cnt;	... ＩＤの数
//  戾り值:	-1			... どれにもあたってない
int focusFontId( int *id, int cnt )
{
	int i;

	for( i = 0; i < cnt; i++ )
	{
		if( id[i] == HitFontNo )
		{
			return i;
		}
	}

	return -1;
}




// ユーザ认证失败信息ウィンドウ
//
static short userCertifyErrorMsgWinProcNo = 0;

// 初期化
void initUserCertifyErrorMsgWin( void )
{
	userCertifyErrorMsgWinProcNo = 0;
}

// ユーザ认证失败信息ウィンドウ处理
//
//  戾り值：	0 ... 处理中
//				1 ... OKボタンが押された
int userCertifyErrorMsgWin( void )
{
	static int fontId[] = { -2 };
	static ACTION *ptActMenuWin = NULL;
	int id;
	int i;
	static int x, y, w, h;
	int ret = 0;
	char *msg = ML_STRING(264, "验证用户失败。");
#ifdef PUK2_NEW_MENU
	char over = 0;
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;
#endif

	// 初期化
	if( userCertifyErrorMsgWinProcNo == 0 )
	{
		userCertifyErrorMsgWinProcNo = 1;

		for( i = 0; i < sizeof( fontId )/sizeof( int ); i++ )
		{
			fontId[i] = -2;
		}

		// ウィンドウ作成
		w = 8*64;
		h = 3*48;
		x = (640 - w)/2;
		y = (480 - h)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w, h, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w, h, 1 );
#endif
	}


#ifdef PUK2_NEW_MENU
	i = (w-66)>>1;
	if ( MakeHitBox( x+i, y+115, x+i+66, y+115+17, -1 ) ) over = 1;

	// 右键されてなければ終了
	id = -1;
	if( over && (mouse.onceState & MOUSE_LEFT_CRICK) ) id = 0;
#else
	// フォントの选择判定
	id = selFontId( fontId, sizeof( fontId )/sizeof( int ) );
#endif

	// ＯＫ
	if( id == 0 )
	{
		ret = 1;
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}


	// 结果がわかったらウィンドウ关闭
	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
			return ret;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
#if 1
			StockFontBuffer( x+140, y+20, FONT_PRIO_FRONT, FONT_PAL_RED, msg, 0 );
#else
			StockFontBuffer( x+140, y+20, FONT_PRIO_FRONT, FONT_PAL_RED, msg[0], 0 );
			StockFontBuffer( x+20, y+45, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg[1], 0 );
			StockFontBuffer( x+20, y+65, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg[2], 0 );
			StockFontBuffer( x+20, y+85, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg[3], 0 );
#endif

#ifdef PUK2_NEW_MENU
			StockDispBuffer( x+(w>>1), y+115+8, DISP_PRIO_WIN2, (over ? GID_BigOKButtonOver : GID_BigOKButtonOn ), 0, &bm );
#else
			fontId[0] =
				StockFontBuffer( x+239, y+110,
					FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_YELLOW,
					"ＯＫ", 2, BoxColor );                                             //MLHIDE
#endif
		}
	}

	return ret;
}








///////////////////////////////////////////////////////////////////////////
//
// タイトル表示
//
#ifdef VERSION_TW
#define SERVER_LINE_PAR_PAGE	10
int selectMainServer = -1;
int selectServerPage = 0;
#else

#define SERVER_LINE_PAR_PAGE	6
int selectServerPage = 0;
#endif
//-----------------------------------------------
// 服务器选择画面
//-----------------------------------------------
#ifdef PUK2
int aiTitleBgTbl[] = {
	CG_TITLE,
	CG_TITLE_EX,
	CG_TITLE_V2,
	PUK2_SERVERSELECT,
 };
#else
int aiTitleBgTbl[] = { CG_TITLE, CG_TITLE_EX, CG_TITLE_V2 };
#endif

	// サーバ名ボタン
	GRA_BTN_INFO2 serverNameBtn[] =
	{
		{
			502, 114, 392,  92,	220,  44, CG_SERVER_NAME_1_1, CG_SERVER_NAME_1_2
		},
		{
			502, 158, 392, 136,	220,  44, CG_SERVER_NAME_2_1, CG_SERVER_NAME_2_2
		},
		{
			502, 202, 392, 180,	220,  44, CG_SERVER_NAME_3_1, CG_SERVER_NAME_3_2
		},
		{
			502, 246, 392, 224,	220,  44, CG_SERVER_NAME_4_1, CG_SERVER_NAME_4_2
		},
		{
			502, 290, 392, 268,	220,  44, CG_SERVER_NAME_5_1, CG_SERVER_NAME_5_2
		},
		{
			502, 334, 392, 312,	220,  44, CG_SERVER_NAME_6_1, CG_SERVER_NAME_6_2
		},
		{
			502, 114, 392,  92,	220,  44, CG_SERVER_NAME_7_1, CG_SERVER_NAME_7_2
		},
		{
			502, 158, 392, 136,	220,  44, CG_SERVER_NAME_8_1, CG_SERVER_NAME_8_2
		},
		{
			502, 202, 392, 180,	220,  44, CG_SERVER_NAME_9_1, CG_SERVER_NAME_9_2
		},
		{
			502, 246, 392, 224,	220,  44, CG_SERVER_NAME_10_1, CG_SERVER_NAME_10_2
		},
		{
			502, 290, 392, 268,	220,  44, CG_SERVER_NAME_11_1, CG_SERVER_NAME_11_2
		},
		{
			502, 334, 392, 312,	220,  44, CG_SERVER_NAME_12_1, CG_SERVER_NAME_12_2
		}
	};
#define SSV2_SB_X	150
#define SSV2_SB_Y	120
#define SSV2_SB_SX	100
#define SSV2_SB_SY	40
#define SSV2_SB_EX	250
// サーバ名ボタン
GRA_BTN_INFO2 serverNameBtn_V2[] =
{
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*0,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Arzes_OFF, V2_Arzes_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*1,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Diomeer_OFF, V2_Diomeer_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*2,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Fanitis_OFF, V2_Fanitis_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*3,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Emeus_OFF, V2_Emeus_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*4,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Ornis_OFF, V2_Ornis_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*5,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Freat_OFF, V2_Freat_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*0,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Yeoskeer_OFF, V2_Yeoskeer_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*1,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Farkalt_OFF, V2_Farkalt_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*2,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Milliotice_OFF, V2_Milliotice_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*3,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Finia_OFF, V2_Finia_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*4,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Erenor_OFF, V2_Erenor_ON
	},
	{
		320, 240, SSV2_SB_X, SSV2_SB_Y+SSV2_SB_SY*5,
		SSV2_SB_SX,  SSV2_SB_SY, V2_Karen_OFF, V2_Karen_ON
	}
};

GRA_BTN_INFO2 exitBtn =
{// 表示座标(x,y)
	576, 418,
 // 范围始点座标(x,y)
	544, 400,
 // 范围幅(w,h)
	64, 36,
 // ボタン画像番号(凸画像,凹画像)
	CG_SERVER_SELECT_EXIT_1, CG_SERVER_SELECT_EXIT_2
};
GRA_BTN_INFO2 backBtn =
{// 表示座标(x,y)
	435, 418,
 // 范围始点座标(x,y)
	399, 400,
 // 范围幅(w,h)
	72, 36,
 // ボタン画像番号(凸画像,凹画像)
	CG_SERVER_SELECT_BACK_1, CG_SERVER_SELECT_BACK_2
};
GRA_BTN_INFO1 leftBtn =
{// 表示座标(x,y)
	448, 377,
 // 范围始点座标(x,y)
	438, 367,
 // 范围幅(w,h)
	20, 21,
 // ボタン画像番号(凸画像,凹画像)
	CG_SERVER_SELECT_LEFT_BTN_1, CG_SERVER_SELECT_LEFT_BTN_2
};
GRA_BTN_INFO1 rightBtn =
{// 表示座标(x,y)
	563, 377,
 // 范围始点座标(x,y)
	553, 367,
 // 范围幅(w,h)
	20, 21,
 // ボタン画像番号(凸画像,凹画像)
	CG_SERVER_SELECT_RIGHT_BTN_1, CG_SERVER_SELECT_RIGHT_BTN_2
};

GRA_BTN_INFO2 V2_exitBtn =
{// 表示座标(x,y)
	320, 240,
 // 范围始点座标(x,y)
	540, 16,
 // 范围幅(w,h)
	64, 16,
 // ボタン画像番号(凸画像,凹画像)
	V2_SERVER_SELECT_EXIT_1, V2_SERVER_SELECT_EXIT_2
};
GRA_BTN_INFO2 V2_backBtn =
{// 表示座标(x,y)
	320, 240,
 // 范围始点座标(x,y)
	540, 40,
 // 范围幅(w,h)
	64, 16,
 // ボタン画像番号(凸画像,凹画像)
	V2_SERVER_SELECT_BACK_1, V2_SERVER_SELECT_BACK_2
};
GRA_BTN_INFO1 V2_leftBtn =
{// 表示座标(x,y)
	123, 387,
 // 范围始点座标(x,y)
	123-9, 387-9,
 // 范围幅(w,h)
	15, 17,
 // ボタン画像番号(凸画像,凹画像)
	V2_SERVER_SELECT_LEFT_BTN_1, V2_SERVER_SELECT_LEFT_BTN_2
};
GRA_BTN_INFO1 V2_rightBtn =
{// 表示座标(x,y)
	225, 387,
 // 范围始点座标(x,y)
	225-9, 387-9,
 // 范围幅(w,h)
	15, 17,
 // ボタン画像番号(凸画像,凹画像)
	V2_SERVER_SELECT_RIGHT_BTN_1, V2_SERVER_SELECT_RIGHT_BTN_2
};


void titleProc( void )
{
	int ret = 0;
	int id, addNo;
	int no;
	int i;
	int serverSelFlag = 0;
	static int line;
	static char msg[256];
	GRA_BTN_INFO2	*pserverNameBtn = serverNameBtn,
		*pExitBtn = &exitBtn, *pBackBtn = &backBtn;
	GRA_BTN_INFO1 *pLeftBtn = &leftBtn, *pRightBtn = &rightBtn;

	// インストールしているバージョンを调べる
	giInstallVersion = GetInstallVersion( );
#ifdef MULTI_GRABIN

	if( giInstallVersion == 2 ){
		pserverNameBtn = serverNameBtn_V2;
		pExitBtn = &V2_exitBtn;
		pBackBtn = &V2_backBtn;
		pLeftBtn = &V2_leftBtn;
		pRightBtn = &V2_rightBtn;
	}

#endif
	// 初期化
	if( SubProcNo == 0 )
	{
		char str[128];
		SubProcNo++;
#ifndef _TAIKEN
		sprintf( str, "%s %s", DEF_APPNAME, NR_VERSION );                   //MLHIDE
#else
		sprintf( str, "%s %s [体验版]", DEF_APPNAME, NR_VERSION );             //MLHIDE
#endif
		SetWindowText( hWnd, str );
		networkDisconnectFlag = 0;
		ProduceInitFlag = TRUE;

		//selectServerPage = 0;
		line = -1;

#ifdef MULTI_GRABIN
	#ifdef PUK2
		if( giInstallVersion == 3 ){	// パレット初期化
			PaletteChange( 27, 0 );
		}else
	#endif
		if( giInstallVersion == 2 ){	// パレット初期化
			PaletteChange( 27, 6 );
		}else{
			// パレット初期化
			PaletteChange( 0, 6 );
		}
#else
		// パレット初期化
		PaletteChange( 0, 6 );
#endif
		play_bgm( BGM_TITLE );
	}

	// サーバ选择处理
	if( SubProcNo == 1 )
	{
		serverSelFlag = 1;

		// 左ボタン押したか？
		if( (pushGraBtnInfo1( pLeftBtn ) & BTN_LEFT_CLICK_REP)
		 && serverMaxNo >= SERVER_LINE_PAR_PAGE )
		{
			selectServerPage--;
			if( selectServerPage < 0 )
				selectServerPage = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else
		// 右ボタン押したか？
		if( (pushGraBtnInfo1( pRightBtn ) & BTN_LEFT_CLICK_REP)
		 && serverMaxNo >= SERVER_LINE_PAR_PAGE )
		{
			selectServerPage++;
			if( selectServerPage >= 2 )
				selectServerPage = 0;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else
		// EXIT押したか？
		if( (pushGraBtnInfo2( pExitBtn ) & BTN_LEFT_CLICK) )
		{
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
			// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			PostMessage( hWnd, WM_CLOSE, 0, 0L );
		}
		else
		// BACK押したか？
		if( (pushGraBtnInfo2( pBackBtn ) & BTN_LEFT_CLICK) )
		{
			ChangeProc2( PROC_ID_PASSWORD );
			if( giInstallVersion >= 1 ){
				// 白パレットに变更
				PaletteChange( 20, 1 );
			}else{
				// 黒パレットに变更
				PaletteChange( 19, 1 );
			}
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
			// ＢＧＭフェードアウト开始
			fade_out_bgm();
		}
		else
		{
			id = -1;
			addNo = selectServerPage*SERVER_LINE_PAR_PAGE;
			for( i = 0; i < SERVER_LINE_PAR_PAGE; i++ )
			{
				if( (pushGraBtnInfo2( &pserverNameBtn[addNo+i] ) & BTN_LEFT_CLICK)
				 && gmsv[addNo+i].useFlag )
				{
					// サーバ决定
					id = addNo + i;
					char title[256];

#ifdef _DEBUG
	#ifdef PUK2
					if ( !strcmp(gmsv[id].ipaddr, "sound") ){                        //MLHIDE
						sprintf( title, "%s %s [soundcheck]", DEF_APPNAME, NR_VERSION ); //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_SE_TEST;
						offlineFlag = TRUE;
						return;
					}
					if ( !strcmp(gmsv[id].ipaddr, "g_id") ){                         //MLHIDE
						sprintf( title, "%s %s [graphic_IDview]", DEF_APPNAME, NR_VERSION ); //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_G_ID_VIEW;
						offlineFlag = TRUE;
						return;
					}
					if ( !strcmp(gmsv[id].ipaddr, "spr") ){                          //MLHIDE
						sprintf( title, "%s %s [sprview]", DEF_APPNAME, NR_VERSION );   //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_SPR_VIEW;
						offlineFlag = TRUE;
						return;
					}
					if ( !strcmp(gmsv[id].ipaddr, "anim") ){                         //MLHIDE
						sprintf( title, "%s %s [animview]", DEF_APPNAME, NR_VERSION );  //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_ANIM_VIEW;
						offlineFlag = TRUE;
						return;
					}
		#ifdef PUK3_BATTLECHECK_SERVER
					if ( !strcmp(gmsv[id].ipaddr, "battle") ){                       //MLHIDE
						sprintf( title, "%s %s [battlecheck]", DEF_APPNAME, NR_VERSION ); //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_BATTLE;
	 					PaletteChange( 0, 0 );
						offlineFlag = TRUE;
						return;
					}
		#endif
	#endif

					sprintf( title, "%s %s [%s  %s:%s]", DEF_APPNAME, NR_VERSION,    //MLHIDE
						selectServerName[id], gmsv[id].ipaddr, gmsv[id].port );
#else
					sprintf( title, "%s %s [%s]", DEF_APPNAME, NR_VERSION, selectServerName[id] ); //MLHIDE
#endif
					SetWindowText( hWnd, title );

					selectServerIndex = id;
					line = i;

					// 决定ａ
					play_se( SE_NO_OK, 320, 240 );

#if 0
					certifyIdPasswordServerIndex = selectServerIndex;
#endif
					getId( userId );
					getPassword( userPassword );
					getCdkey( userCdKey );
					SubProcNo++;
					break;
				}
			}
		}
	}

	// ゲームサーバへ接続
	if( SubProcNo == 2 )
	{
		// 初期化
		initConnecGameServer();
		SubProcNo++;
	}
	if( SubProcNo == 3 )
	{
		// 接続处理
		ret = connecGameServer();
		// 接続完了
		if( ret == 1 )
		{
			ChangeProc( PROC_CHAR_SELECT );
		}
		else
		// ネットワークの准备が出来てない
		if( ret == -1 )
		{
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_112 );
#else
			strcpy( msg, NET_ERRMSG_SOCKLIBERROR );
#endif
			// ネットワーク初期化
			cleanupNetwork();
		}
		else
		// サーバに接続できなかった
		if( ret == -2 )
		{
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			switch( Err_clientLogin ){
			case 1234:
				// 人数满タン
				strcpy( msg, INFOMSG_29 );
				break;
			case AUTH_ERROR_CDKEY:
				// ＣＤキー又は、ＩＤ，パスワードが违う
				strcpy( msg, INFOMSG_27 );
				break;
			case AUTH_ERROR_EXPIRE:
				// 制品权利を购入していない又は期限が切れています。
				strcpy( msg, INFOMSG_28 );
				break;
			default:
				// その他のエラー。
				strcpy( msg, ERRMSG_113 );
				break;
			}
#else
			switch( Err_clientLogin ){
			case 1234:
				// 人数满タン
				strcpy( msg, NET_ERRMSG_SERVER_BUSY );
				break;
			case AUTH_ERROR_CDKEY:
				// ＣＤキー又は、ＩＤ，パスワードが违う
				strcpy( msg, NET_ERRMSG_ERR_CDKEY );
				break;
			case AUTH_ERROR_EXPIRE:
				// 制品权利を购入していない又は期限が切れています。
				strcpy( msg, NET_ERRMSG_ERR_EXPIRE );
				break;
			default:
				// その他のエラー。
				strcpy( msg, NET_ERRMSG_NOTCONNECT );
				break;
			}
#endif
			// ネットワーク初期化
			cleanupNetwork();
		}else
		// ニューエストが无い时
		if( ret == -3 ){
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_116 );
#else
			strcpy( msg, NET_ERRMSG_NEWEST_NON );
#endif
			// ネットワーク初期化
			cleanupNetwork();
		}
#ifdef PUK3_LOGIN_VERCHECK
		// クライアントのバージョン异常
		else if( ret == -4 ){
			SubProcNo = 100;
	#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_118 );
	#else
			strcpy( msg, NET_ERRMSG_CLIENTVER_DIFF );
	#endif
			// ネットワーク初期化
			cleanupNetwork();
		}
#endif
#ifdef PUK3_ERRORMESSAGE_NUM
		// ニューエストにサーバーが无い时
		else if( ret == -5 ){
			SubProcNo = 100;
			strcpy( msg, ERRMSG_117 );
			// ネットワーク初期化
			cleanupNetwork();
		}
#endif
	}


	// エラー信息
	if( SubProcNo == 100 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 101 )
	{
		if( commonMsgWin( msg ) )
		{

			if( Err_clientLogin == AUTH_ERROR_CDKEY){

				// ＯＫボタンが押された
				strcpy( msg, ML_STRING(265, "请注意大小写和书号输入？") );
				initCommonMsgWin();
				SubProcNo = 102;

			}else{

				SubProcNo = 1;

				char str[128];

#ifndef _TAIKEN
				sprintf( str, "%s %s", DEF_APPNAME, NR_VERSION );                 //MLHIDE
#else
				sprintf( str, "%s %s [体验版]", DEF_APPNAME, NR_VERSION );           //MLHIDE
#endif
				SetWindowText( hWnd, str );
			}


		}
	}

	if( SubProcNo == 102){

		if( commonMsgWin( msg ) )
		{
			SubProcNo = 1;

		}

		char str[128];

#ifndef _TAIKEN
			sprintf( str, "%s %s", DEF_APPNAME, NR_VERSION );                  //MLHIDE
#else
			sprintf( str, "%s %s [体验版]", DEF_APPNAME, NR_VERSION );            //MLHIDE
#endif
			SetWindowText( hWnd, str );
	}


	// ページ表示
	if( serverMaxNo >= SERVER_LINE_PAR_PAGE )
	{
#ifdef MULTI_GRABIN
	#ifdef PUK2
		if( giInstallVersion >= 2 )
	#else
		if( giInstallVersion == 2 )
	#endif
		{
//			StockDispBuffer( 140, 380, DISP_PRIO_BG, V2_SERVER_SELECT_PAGE1+selectServerPage, 0 );
			StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_SERVER_SELECT_PAGE1+selectServerPage, 0 );
		}else{
			StockDispBuffer( 509, 380, DISP_PRIO_BG, CG_SERVER_SELECT_PAGE1+selectServerPage, 0 );
		}
#else
		StockDispBuffer( 509, 380, DISP_PRIO_BG, CG_SERVER_SELECT_PAGE1+selectServerPage, 0 );
#endif
	}

	if( serverSelFlag )
	{
		// サーバ名ボタン表示
		for( i = 0; i < SERVER_LINE_PAR_PAGE; i++ )
		{
			no = selectServerPage*SERVER_LINE_PAR_PAGE+i;
			if( gmsv[no].useFlag )
			{
				drawGraBtnInfo2( &pserverNameBtn[no], DISP_PRIO_CHAR, 0, 0, 0 );
			}
		}

		if( serverMaxNo >= SERVER_LINE_PAR_PAGE )
		{
			// 左ボタン表示
			drawGraBtnInfo1( pLeftBtn, DISP_PRIO_CHAR, 0, 0, 0 );
			// 右ボタン表示
			drawGraBtnInfo1( pRightBtn, DISP_PRIO_CHAR, 0, 0, 0 );
		}

		// EXITボタン表示
		drawGraBtnInfo2( pExitBtn, DISP_PRIO_CHAR, 0, 0, 0 );
		// BACKボタン表示
		drawGraBtnInfo2( pBackBtn, DISP_PRIO_CHAR, 0, 0, 0 );
	}
	else
	{
		// サーバ名ボタン表示
		for( i = 0; i < SERVER_LINE_PAR_PAGE; i++ )
		{
			no = selectServerPage*SERVER_LINE_PAR_PAGE+i;
			if( gmsv[no].useFlag )
			{
				if( i != line )
				{
					drawGraBtnInfo2( &pserverNameBtn[selectServerPage*SERVER_LINE_PAR_PAGE+i],
						DISP_PRIO_CHAR, 0, 0, 1 );
				}
				else
				{
					drawGraBtnInfo2( &pserverNameBtn[selectServerPage*SERVER_LINE_PAR_PAGE+i],
						DISP_PRIO_CHAR, 0, 0, 2 );
				}
			}
		}

		if( serverMaxNo >= SERVER_LINE_PAR_PAGE )
		{
			// 左ボタン表示
			drawGraBtnInfo1( pLeftBtn, DISP_PRIO_CHAR, 0, 0, 1 );
			// 右ボタン表示
			drawGraBtnInfo1( pRightBtn, DISP_PRIO_CHAR, 0, 0, 1 );
		}

		// EXITボタン表示
		drawGraBtnInfo2( pExitBtn, DISP_PRIO_CHAR, 0, 0, 1 );
		// BACKボタン表示
		drawGraBtnInfo2( pBackBtn, DISP_PRIO_CHAR, 0, 0, 1 );
	}

	// タイトルＢＧ表示
	// ＢＧ表示
	{ int iTitleBgGraNo = aiTitleBgTbl[giInstallVersion];	// その画像で行こう
		StockDispBuffer( 320, 240, DISP_PRIO_BG, iTitleBgGraNo, 0 );
	}
#ifdef PUK2_NEWVER
	StockFontBuffer( 460, 424, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
#else
	StockFontBuffer( 540, 424, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
#endif


}




// サーバ接続处理初期化

static short connecGameServerProcNo = 0;
static short connecNewestServerProcNo = 0;

void initConnecGameServer( void )
{
	connecGameServerProcNo = 0;
	connecNewestServerProcNo = 0;
}


// サーバ接続处理
//
//  戾り值：	 0 ... 处理中
//				 1 ... 接続完了
//				-1 ... ネットワークの准备が出来ていない
//				-2 ... サーバに接続できなかった
//				-3 ... ニューエストが无かった
#ifdef PUK3_LOGIN_VERCHECK
//				-4 ... クライアントのバージョン异常
#endif
#ifdef PUK3_ERRORMESSAGE_NUM
//				-5 ... ニューエストにサーバーが无かった
#endif
int connecGameServer( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	int ret = 0;
	int ret2;
	static char msg[256];

	// 初期化
	if( connecGameServerProcNo == 0 )
	{

#ifdef VERSION_TW
		sprintf( msg, ML_STRING(266, "服务器连接中"));
#else
		sprintf( msg, ML_STRING(267, "%s 服务器连接中..."), selectServerName[selectServerIndex] );
#endif

		// ウィンドウ作成
		w = (GetStrWidth( msg, FONT_KIND_MIDDLE )+48+63)/64;
		if( w < 2 )
			w = 2;
		h = (16+47)/48;
		if( h < 2 )
			h = 2;
		//根据窗口分辨率计算选择服务器后提示文字的显示位置
		x = (DEF_APPSIZEX-w*64)/2;
		y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif

		// デバッグ版はニューエストチェックをしない
		connecGameServerProcNo = 2;

#ifdef PUK2_CUTNUWESTCHECK
		// Newest.txt を読みにいけないので、読みにいかない
		connecGameServerProcNo = 2;
#endif
#ifdef PUK2_NOCUTNUWESTCHECK
		// デバッグ版で、newestを読みに行きたいのでニューエストチェックをする
		connecGameServerProcNo = 1;
#endif
	}

	// ニューエストのチェック
	if( connecGameServerProcNo == 1 )
	{
		// 初期化等
		if( connecNewestServerProcNo == 0 ){

			// ニューエストネットワーク初期化
#ifdef PUK3_LOGIN_VERCHECK
			if ( NewestInitNetWork() < 0 ){
				ret = -4;
			}else
#else
			NewestInitNetWork();
#endif
			// ニューエスト接続处理
			if( NewestConnectServer() != 1 ){
				ret = -3;
			}else{
				// ニューエスト送信处理
				NewestSendMessege();
				connecNewestServerProcNo = 1;
			}
		}
		// 受信处理
		if( connecNewestServerProcNo == 1 ){
			int ret2;
			// ニューエスト受信处理
			ret2 = NewestReceiveMessege();
			// 受信できた时
			if( ret2 == 1 ){
				// 受信できたら次の处理へ
				connecNewestServerProcNo = 2;
			}else
			// 受信に失败した时
			if( ret2 == -1 ){
#ifdef PUK3_ERRORMESSAGE_NUM
				ret = -5;
#else
				ret = -3;
#endif
			}
#ifdef PUK3_LOGIN_VERCHECK
			// クライアントのバージョン异常
	#ifdef PUK3_ERRORMESSAGE_NUM
			else if( ret2 == -2 ){
	#else
			else if( ret2 == -1 ){
	#endif
				ret = -4;
			}
#endif
		}

		// 終了处理
		if( connecNewestServerProcNo == 2 ){

			// ニューエストネットワーク終了
			NewestEndNetWork();

			connecGameServerProcNo = 2;
		}
	}



	// ネットワーク初期化
	if( connecGameServerProcNo == 2 )
	{
		cleanupNetwork();
		if( initNet() )
		{
			connecGameServerProcNo = 3;
		}
		else
		{
			ret = -1;
		}
	}
	// サーバ接続处理初期化
	if( connecGameServerProcNo == 3 )
	{
		initConnectServer();
		connecGameServerProcNo = 4;
	}
	if( connecGameServerProcNo == 4 )
	{
		ret2 = connectServer();
		if( ret2 == 1 )
		{
			ret = 1;
		}
		else
		if( ret2 < 0 )
		{
			ret = -2;
		}
	}

	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int len;
			int xx, yy;

			len = GetStrWidth( msg, FONT_KIND_MIDDLE );
			xx = (w*64-len)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
				msg, 0, 0 );
		}
	}

	return ret;
}






//--------------------------------------------------
// キャラ选择用テキストボタン处理
//--------------------------------------------------
int T_TextButton( int x1, int y1, int x2, int y2, char *mesg ){
	int iRet = 0;
	// アップグレード表示
	StockFontBuffer( x1+6, y1+6, FONT_PRIO_BACK, FONT_PAL_WHITE, mesg, 0 );
	if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_MENU + 1 ) == TRUE
	&& (mouse.onceState & MOUSE_LEFT_CRICK) // マウスクリックされたら
	){
		iRet = 1;
	}
	return iRet;
}


#if 1

enum{
	//------- まだキャラ取得していない -------
	E_CHARLIST_INIT,		// 初期化
	E_CHARLIST_DOWNLOAD,	// 取得中??????

	//----  キャラ取得できなかった场合 ------

	E_CHARLIST_MAINTE,		// メンテナンス中
	E_CHARLIST_AUTHERR,		// 认证失败
	E_CHARLIST_LOAD_ERR,	// キャラリスト取得失败
	E_CHARLIST_LOAD_ERR_WAIT,// 取得失败の确认
	E_CHARLIST_MAINTE_WAIT,	// メンテナンス确认
	E_CHARLIST_AUTH_ERR,	// 认证失败
	E_CHARLIST_AUTH_ERR_WAIT,	// 认证失败确认

	// -------- キャラ取得できた场合 ---------
	E_CHARLIST_LOAD_OK,		// 取得完了
	E_CHARLIST_COMMAND_INIT,// 取得后のコマンド待ち
	E_CHARLIST_COMMAND_WAIT,// 取得后のコマンド待ち
	E_CHARLIST_DELETE_INIT,	// 削除处理初期化
	E_CHARLIST_DELETE_YESNO,// 削除处理确认
	E_CHARLIST_DELETE_GO,	// 削除处理实行
	E_CHARLIST_DELETE_ERR,	// 削除处理失败
	E_CHARLIST_DELETE_ERR_WAIT,	// 削除处理失败

	E_CHARLIST_VERUP_INIT,	// バージョンアップ初期化
	E_CHARLIST_VERUP_WAIT,	// バージョンアップ确认待ち
	E_CHARLIST_INSTALL_ERR,
	E_CHARLIST_INSTALL_ERR_WAIT,

#ifdef PUK2
	E_CHARLIST_GUILDMASTER_INIT,	// 家族マスターなので消せない
#endif
};

// ＵＰＧＡＲＡＤＥボタン情报
GRA_BTN_INFO2 upGradeBtn =
{// 表示座标(x,y)
	320, 240,
 // 范围始点座标(x,y)
	443, 440,
 // 范围幅(w,h)
	170,  34,
 // ボタン画像番号(凸画像,凹画像)
	CG_CHR_SEL_UPGRADE_BTN_1, CG_CHR_SEL_UPGRADE_BTN_2
};

// 卒业版からVER2へのＵＰＧＡＲＡＤＥボタン情报
GRA_BTN_INFO2 upGradeBtnTrial =
{// 表示座标(x,y)
	320, 240,
 // 范围始点座标(x,y)
	443, 440,
 // 范围幅(w,h)
	170,  34,
 // ボタン画像番号(凸画像,凹画像)
	CG_CHR_SEL_UPGRADE_BTN_TRIAL_1, CG_CHR_SEL_UPGRADE_BTN_TRIAL_2
};



	// ＢＡＣＫボタン情报
	GRA_BTN_INFO2 Clist_backBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		255, 440,
	 // 范围幅(w,h)
		115,  34,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_CHR_SEL_BACK_BTN_2
	};

	// ログインボタン情报
	GRA_BTN_INFO2 Clist_loginBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 65, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_LOGIN_LEFT_BTN_1, CG_CHR_SEL_LOGIN_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320+288, 240,
		 // 范围始点座标(x,y)
			 65+288, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_LOGIN_LEFT_BTN_1, CG_CHR_SEL_LOGIN_LEFT_BTN_2
		}
	};
	// 削除ボタン情报
	GRA_BTN_INFO2 Clist_delBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			191, 379,
		 // 范围幅(w,h)
			 93, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_DEL_LEFT_BTN_1, CG_CHR_SEL_DEL_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320+288, 240,
		 // 范围始点座标(x,y)
			191+288, 379,
		 // 范围幅(w,h)
			106, 41,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_DEL_LEFT_BTN_1, CG_CHR_SEL_DEL_LEFT_BTN_2
		}
	};
	// 新规ボタン情报
	GRA_BTN_INFO2 Clist_newBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_NEW_LEFT_BTN_1, CG_CHR_SEL_NEW_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320+288, 240,
		 // 范围始点座标(x,y)
			188+288, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_NEW_LEFT_BTN_1, CG_CHR_SEL_NEW_LEFT_BTN_2
		}
	};


	// ＢＡＣＫボタン情报
	GRA_BTN_INFO2 learn_backBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		255, 440,
	 // 范围幅(w,h)
		115,  34,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_LEARN_BACK_BTN
	};
	// ログインボタン情报
	GRA_BTN_INFO2 learn_loginBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 65, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_LOGIN_BTN_DOWN_L, CG_LEARN_LOGIN_BTN_L
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 65+288, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_LOGIN_BTN_DOWN_R, CG_LEARN_LOGIN_BTN_R
		}
	};
	// 削除ボタン情报
	GRA_BTN_INFO2 learn_delBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			191, 379,
		 // 范围幅(w,h)
			 93, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_DELETE_BTN_DOWN_L, CG_LEARN_DELETE_BTN_L
		},
		{// 表示座标(x,y)
			320 , 240,
		 // 范围始点座标(x,y)
			191+288, 379,
		 // 范围幅(w,h)
			93, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_DELETE_BTN_DOWN_R, CG_LEARN_DELETE_BTN_R
		}
	};
	// 新规ボタン情报
	GRA_BTN_INFO2 learn_newBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_CREATE_BTN_DOWN_L, CG_LEARN_CREATE_BTN_L
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188+288, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_CREATE_BTN_DOWN_R, CG_LEARN_CREATE_BTN_R
		}
	};

	// Ｖ２ＢＡＣＫボタン情报
	GRA_BTN_INFO2 V2_Clist_backBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		540, 40,
	 // 范围幅(w,h)
		74,  20,
	 // ボタン画像番号(凸画像,凹画像)
		0, V2_CHR_SEL_BACK_BTN_2
	};

	// 新规ボタン情报
	GRA_BTN_INFO2 V2_Clist_newBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188, 380,
		 // 范围幅(w,h)
			 74, 30,
		 // ボタン画像番号(凸画像,凹画像)
			V2_CREATE_BTN_DOWN_L, V2_CREATE_BTN_L
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188+288, 373,
		 // 范围幅(w,h)
			 74, 30,
		 // ボタン画像番号(凸画像,凹画像)
			V2_CREATE_BTN_DOWN_R, V2_CREATE_BTN_R
		}
	};

	// ログインボタン情报
	GRA_BTN_INFO2 V2_Clist_loginBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 90, 373,
		 // 范围幅(w,h)
			 70, 30,
		 // ボタン画像番号(凸画像,凹画像)
			V2_CHR_SEL_LOGIN_LEFT_BTN_1, V2_CHR_SEL_LOGIN_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 380, 373,
		 // 范围幅(w,h)
			 70, 30,
		 // ボタン画像番号(凸画像,凹画像)
			V2_CHR_SEL_LOGIN_RIGHT_BTN_1, V2_CHR_SEL_LOGIN_RIGHT_BTN_2
		}
	};

	// 削除ボタン情报
	GRA_BTN_INFO2 V2_Clist_delBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			184, 373,
		 // 范围幅(w,h)
			 74, 30,
		 // ボタン画像番号(凸画像,凹画像)
			V2_CHR_SEL_DEL_LEFT_BTN_1, V2_CHR_SEL_DEL_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			475, 373,
		 // 范围幅(w,h)
			74, 30,
		 // ボタン画像番号(凸画像,凹画像)
			V2_CHR_SEL_DEL_RIGHT_BTN_1, V2_CHR_SEL_DEL_RIGHT_BTN_2
		}
	};

#ifdef PUK2
	ACTION *pActSelGra[2]={0};
	void pActSelGra_init( int index );
	int GetFaceNum( int graNo );
#endif

///////////////////////////////////////////////////////////////////////////
//
// キャラ选择画面（削除も出来る）
//

void selectCharacterProc( void )
{
	int ret;
	int i, j;
	int login;
	char msg[256];
	int btnUseFlag = 1;	// ボタンを有效にするフラグ。０が有效。
	static int CanUpGradeFlag = 0;

	static ACTION *ptActMenuWin = NULL;
	int attrColor[4][2] =
	{
		{ SYSTEM_PAL_GREEN,  SYSTEM_PAL_GREEN2  },
		{ SYSTEM_PAL_AQUA,   SYSTEM_PAL_AQUA2   },
		{ SYSTEM_PAL_RED,    SYSTEM_PAL_RED2    },
		{ SYSTEM_PAL_YELLOW, SYSTEM_PAL_YELLOW2 }
	};
	int x1, y1, x2, y2;
	int len;
	GRA_BTN_INFO2 *pBackBtn = &Clist_backBtn, *pNewBtn = Clist_newBtn,
		*pLoginBtn = Clist_loginBtn, *pDelBtn = Clist_delBtn,
		*pUpGradeBtn = &upGradeBtn;

	if( PackageVer >= PV_FIRST_VER2 ){
		pBackBtn = &V2_Clist_backBtn;
		pNewBtn = V2_Clist_newBtn;
		pLoginBtn = V2_Clist_loginBtn;
		pDelBtn = V2_Clist_delBtn;
	}else
	if(PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
		pBackBtn = &learn_backBtn;
		pNewBtn = learn_newBtn;
		pLoginBtn = learn_loginBtn;
		pDelBtn = learn_delBtn;
		pUpGradeBtn = &upGradeBtnTrial;
	}

	// キャラクタリスト取得处理初期化
	switch( SubProcNo ){

	case  E_CHARLIST_INIT:
		SubProcNo = E_CHARLIST_DOWNLOAD;
		PackageVer = PV_NORMAL;	// パッケージバージョンを通常に初期化
		initDownloadCharList();	// キャラリスト取得中??????
		CanUpGradeFlag = 0;		// バージョンアップフラグ０
		PaletteChange( 0, 0 );
		break;

	// キャラクタリスト取得处理
	case E_CHARLIST_DOWNLOAD:
		ret = downloadCharList();
		if( ret == 1 ){
			SubProcNo = E_CHARLIST_LOAD_OK;	// 取得完了
			//play_bgm(2);
		}else
		if( ret == -3 ){
			SubProcNo = E_CHARLIST_MAINTE;	// メンテナンス中
			cleanupNetwork();	// ネットワーク初期化
		}else
		if( ret == -4 )	{
			SubProcNo = E_CHARLIST_AUTH_ERR;	// ユーザ认证失败
			cleanupNetwork();	// ネットワーク初期化
		}else
		if( ret < 0 ){
			SubProcNo = E_CHARLIST_LOAD_ERR;	// エラー発生
			cleanupNetwork();	// ネットワーク初期化
		}
		break;

	// キャラ取得ＯＫ。ここでサーバーのバージョンとインストールバージョンを
	// 比べる
	case E_CHARLIST_LOAD_OK:
#ifdef MULTI_GRABIN
		{
			// インストールしてあるバージョン调べる。
			giInstallVersion = GetInstallVersion( );

			if( PackageVer == 1 ){
				// 见习い版だったらアップグレードできない。
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// サーバーがＶ２なのにＶ２がインストールされてない场合。
			// サーバーがＥＸなのにＥＸがインストールされてない场合。
			if( ( giInstallVersion < 2 && PackageVer >= PV_FIRST_VER2 )
			||  ( giInstallVersion < 1 && PackageVer >= PV_FIRST_EX )
			){
				// 新しいのがインストールされてないよ。
				SubProcNo = E_CHARLIST_INSTALL_ERR;
//				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// ＥＸへのアップデート
			if( ( giInstallVersion == 1 && PackageVer == PV_NORMAL )
			||  ( giInstallVersion == 1 && PackageVer == PV_EQUAL )
			){
				// バージョンアップする表示を出す。
				CanUpGradeFlag = 1;
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// Ｖ２がインストールされているのにサーバーがＶ２未满の场合。
			// ＥＸへのアップデートはなくなりました
#ifdef PUK2
			if( ( giInstallVersion >= 2 && PackageVer == PV_NORMAL )
			||  ( giInstallVersion >= 2 && PackageVer == PV_EQUAL )
			){
#else
			if( ( giInstallVersion == 2 && PackageVer == PV_NORMAL )
			||  ( giInstallVersion == 2 && PackageVer == PV_EQUAL )
			){
#endif
				// バージョンアップする表示を出す。
				CanUpGradeFlag = 1;
				SubProcNo = E_CHARLIST_COMMAND_INIT;
#ifdef PUK2
			}else
			// PUK2がインストールされているのにサーバーがV2の场合。
			// ＥＸへのアップデートはなくなりました
			if( ( giInstallVersion == 3 && PackageVer >= PV_FIRST_VER2 && PackageVer <= PV_UP_VER2 ) ){
				// アップグレード画面へ。
				PaletteChange( 0, 0 );
				ChangeProc( PROC_UPGRADE );
#endif
			}else{
				// 正しい。コマンド受付に行ってよし。
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}
		}
#else
		SubProcNo = E_CHARLIST_COMMAND_INIT;
#endif
		break;

	// キャラ取得后のコマンド受付开始
	case E_CHARLIST_COMMAND_INIT:
		SubProcNo = E_CHARLIST_COMMAND_WAIT;
#ifdef PUK2
		if( PackageVer >= PV_PUK2 ){
			pActSelGra_init(0);
			pActSelGra_init(1);
		}
#endif
		break;

	// キャラ取得できたのでコマンド待ち状态である。
	case E_CHARLIST_COMMAND_WAIT:
		btnUseFlag = 0;	// このときはボタンが有效である。
		break;


	// 削除处理初期化
	case E_CHARLIST_DELETE_INIT:
		//ret = commonYesNoWindow( 320, 240 );	// ohta
		//ret = commonYesNoWindow( 320, 240, "要清除吗？", FONT_PAL_RED );
		ret = DeleteYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER );
		// はい
		if( ret == 1 ){
			SubProcNo = E_CHARLIST_DELETE_YESNO;
			//削除时にでる信息ウインドウを表示
#ifdef PUK2_NEW_MENU
			//按窗口分辨率计算显示位置(默认坐标130,300)
			ptActMenuWin = makeWindowDisp( SCREEN_WIDTH_CENTER - 190, SCREEN_HEIGHT_CENTER + 60, 340, 52, 4 );
#else
			ptActMenuWin = makeWindowDisp( SCREEN_WIDTH_CENTER - 190, SCREEN_HEIGHT_CENTER + 60, 340, 52, 1 );
#endif
		}else
		// いいえ
		if( ret == 2 ){
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}
		break;

	// 削除处理确认
	case E_CHARLIST_DELETE_YESNO:
		//ret = commonYesNoWindow( 320, 240 );	// ohta
		ret = commonYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER, ML_STRING(268, "真的要清除吗？"), FONT_PAL_WHITE );
		//ret = DeleteYesNoWindow( 320, 240 );

		if(ptActMenuWin->hp >= 1){
			strcpy( msg, ML_STRING(269, "请按住CTRL键　"));
			//按窗口分辨率计算显示位置(默认坐标166,326)
			StockFontBuffer( SCREEN_WIDTH_CENTER - 154, SCREEN_HEIGHT_CENTER + 86, FONT_PRIO_FRONT, FONT_PAL_YELLOW, msg, 0 );
#ifdef PUK2_NEW_MENU
			strcpy( msg, ML_STRING(270, "点击『是』。        "));
#else
			strcpy( msg, ML_STRING(270, "点击『是』。        "));
#endif
			//按窗口分辨率计算显示位置(默认坐标166,346)
			StockFontBuffer( SCREEN_WIDTH_CENTER - 154, SCREEN_HEIGHT_CENTER + 106, FONT_PRIO_FRONT, FONT_PAL_YELLOW, msg, 0 );
			// はい
			if( ret == 1 ){
				if(VK[VK_CONTROL] & KEY_ON){
					SubProcNo = E_CHARLIST_DELETE_GO;
					// 即死ＳＥ
					play_se( 284, 320, 240 );
					if( ptActMenuWin ){
						DeathAction( ptActMenuWin );
						ptActMenuWin = NULL;
					}
					// 削除处理开始
					initDeleteCharacter();
					SubProcNo = E_CHARLIST_DELETE_GO;
				}
			}else
			// いいえ
			if( ret == 2 ){
				SubProcNo = E_CHARLIST_COMMAND_INIT;	// 取得后に返回
				if( ptActMenuWin ){
					DeathAction( ptActMenuWin );
					ptActMenuWin = NULL;
				}
			}

		}
		break;


	// 削除处理实行
	case E_CHARLIST_DELETE_GO:
		ret = deleteCharacter();	// 削除中
		if( ret == 1 ){
			resetCharacterList( selectPcNo );
			clearUserSetting( selectPcNo );
			if( saveNowState() ){

			}
			if( maxPcNo > 0 )
				maxPcNo--;
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}else
		if( ret == -2 ){
			SubProcNo = E_CHARLIST_DELETE_ERR;
		}
		break;


	// キャラリスト取得失败
	case E_CHARLIST_LOAD_ERR:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_LOAD_ERR_WAIT;
		break;

	case E_CHARLIST_LOAD_ERR_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_16 ) ){
#else
		if( commonMsgWin( ML_STRING(271, "无法取得角色列表。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// メンテナンス中エラー
	case E_CHARLIST_MAINTE:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_MAINTE_WAIT;
		break;

	case E_CHARLIST_MAINTE_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_17 ) ){
#else
		if( commonMsgWin( ML_STRING(272, "现在服务器维护中。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// ユーザ认证失败处理
	case E_CHARLIST_AUTH_ERR:
		initUserCertifyErrorMsgWin();
		SubProcNo = E_CHARLIST_AUTH_ERR_WAIT;
		break;

	case E_CHARLIST_AUTH_ERR_WAIT:
		if( userCertifyErrorMsgWin() ){
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// キャラ削除失败处理
	case E_CHARLIST_DELETE_ERR:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_DELETE_ERR_WAIT;
		break;

	case E_CHARLIST_DELETE_ERR_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_18 ) ){
#else
		if( commonMsgWin( ML_STRING(273, "无法删除角色。") ) ){
#endif
			// ＯＫボタンが押された
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}
		break;

	// ただしくインストールされてません。
	case E_CHARLIST_INSTALL_ERR:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_INSTALL_ERR_WAIT;
		break;

	case E_CHARLIST_INSTALL_ERR_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_20 ) ){
#else
		if( commonMsgWin( ML_STRING(274, "游戏没有被正常安全。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

#ifdef MULTI_GRABIN
	// バージョンアップしますか。
	case E_CHARLIST_VERUP_INIT:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_VERUP_WAIT;
		break;

	case E_CHARLIST_VERUP_WAIT:
		ret = commonYesNoWindow2( 320, 240,
			ML_STRING(275, "升级吗？"),
			FONT_PAL_WHITE, 64*6, 48*2 );
		if( ret == 1 ){
			// はいが押された
			ChangeProc( PROC_UPGRADE );
		}else
		if( ret == 2 ){
			// いいえが押された
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}
		break;
#endif
	default:
		break;
	}


	// ボタン操作。キャラリスト取得时のみ。なにもボタンが押されていないとき
	if( !btnUseFlag && SubProcNo == E_CHARLIST_COMMAND_WAIT ){
		//返回按键を押したとき
		if( (pushGraBtnInfo2( pBackBtn ) & BTN_LEFT_CLICK) ){
			// ネットワーク終了设定
			cleanupNetwork();
			ChangeProc( PROC_TITLE_MENU );
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
#ifdef PUK2
			// アクション初期化
			DeathAllAction();
			if (pActSelGra[0]) pActSelGra[0]=NULL;
			if (pActSelGra[1]) pActSelGra[1]=NULL;
#endif
		}else{
			// キャラリストの数だけ处理する
			for( i = 0; i < MAXCHARACTER ; i++ ){
				// キャラがいる时のボタンのチェック
				if( existCharacterListEntry( i ) )//キャラリストにデータが设定されているか检查
				{
					// ログインボタン押された
					if( (pushGraBtnInfo2( &pLoginBtn[i] ) & BTN_LEFT_CLICK) )
					{
						char name[CHAR_NAME_LEN+1];

						// 使用キャラ决定
						selectPcNo = i;

						strcpy( name, chartable[selectPcNo].name );
						sjisStringToEucString( name );
						strcpy( gamestate_login_charname, name );

						// 初期化时にpc.faceGraNoに入れるため
						newCharacterFaceGraNo = chartable[selectPcNo].faceGraNo;
						//loginDp = chartable[selectPcNo].dp;

						ChangeProc( PROC_CHAR_LOGIN_START );
						// ログイン音
						play_se( SE_NO_LOGIN, 320, 240 );
						createCharFlag = 0;
						fade_out_bgm();
#ifdef PUK2
						// アクション初期化
						DeathAllAction();
						if (pActSelGra[0]) pActSelGra[0]=NULL;
						if (pActSelGra[1]) pActSelGra[1]=NULL;
#endif
						break;
					}else
					// 削除ボタン押された
					if( (pushGraBtnInfo2( &pDelBtn[i] ) & BTN_LEFT_CLICK) )
					{
						char name[CHAR_NAME_LEN+1];

						// 削除キャラ决定
						selectPcNo = i;

						strcpy( name, chartable[selectPcNo].name );
						sjisStringToEucString( name );
						strcpy( gamestate_deletechar_charname, name );
						SubProcNo = E_CHARLIST_DELETE_INIT;
						// 决定音c（文字等クリック时）
						//play_se( SE_NO_OK3, 320, 240 );
						play_se( SE_NO_NG, 320, 240 );
					}else
#ifdef PUK2
					if( PackageVer >= PV_PUK2 ){
						int iFacePosX = 66, iFacePosY = 65, iFaceXTime = 290;
						if( PackageVer >= PV_FIRST_VER2 ){
							iFacePosX = 202, iFacePosY = 152;
							iFaceXTime = 290;
						}
						// 切り替えれる画像で
						// そこを押していたら
						if ( !(chartable[i].faceGraNo==CG2_FACE_SHADOW_PCM || chartable[i].faceGraNo==CG2_FACE_SHADOW_PCF) ){
							if( MakeHitBox( iFacePosX+i*iFaceXTime, iFacePosY, iFacePosX+66+i*iFaceXTime, iFacePosY+75, DISP_PRIO_CHAR ) == TRUE
							&&  mouse.onceState & MOUSE_LEFT_CRICK
							){
								int graNo = chartable[i].faceGraNo;
								// 旧モンタージュ
								if( (CG_FACE_0 <= graNo) && (graNo < CG_FACE_17) ){
									chartable[i].faceGraNo = getReFaceGraphic( chartable[i].faceGraNo );
								}
								// １キャラPUKモンタージュ
								else if ( (RN_FACE_0<=graNo) && (graNo<RN_FACE_13+100) ){
									chartable[i].faceGraNo = getFaceGraphicToPUK2( chartable[i].faceGraNo );
								}
								// PUKキャラモンタージュ
								else if ( (V2_FACE_0<=graNo) && (graNo<V2_FACE_13+100) ){
									chartable[i].faceGraNo = getFaceGraphicToPUK2( chartable[i].faceGraNo );
								}
								// １キャラPUK2モンタージュ
								else if ( (PUK2_FACE_01<=graNo) && (graNo<PUK2_FACE_15) ){
									chartable[i].faceGraNo = getFaceGraphicFormPUK2( chartable[i].faceGraNo );
									chartable[i].faceGraNo = getOldFaceGraphic( chartable[i].faceGraNo );
								}
								// PUKキャラPUK2モンタージュ
								else if ( (PUK2_FACE_15<=graNo) && (graNo<=PUK2_FACE_28+100) ){
									chartable[i].faceGraNo = getFaceGraphicFormPUK2( chartable[i].faceGraNo );
								}
							}
							else if (pActSelGra[i]){
								if( pActSelGra[i]->hitDispNo == HitDispNo && pActSelGra[i]->atr & ACT_ATR_HIT_BOX ){
									if( mouse.onceState & MOUSE_LEFT_CRICK ){
										if (chartable[i].isRenewal==0) chartable[i].isRenewal=1;
										else if (chartable[i].isRenewal==1) chartable[i].isRenewal=0;
										giCreateRenewalFlg = chartable[i].isRenewal;
										pActSelGra_init(i);
									}
								}
							}
						}
					}else
#endif
					// 颜ボタンを押されたら旧＆リニューアルを入れ替える。ただし。
					// 自分がＶＥＲ２になっていて、旧キャラ使用时。
					if( PackageVer >= PV_FIRST_EX ){
#ifdef MULTI_GRABIN
						int iFacePosX = 66, iFacePosY = 65, iFaceXTime = 290;
						if( PackageVer >= PV_FIRST_VER2 ){
							iFacePosX = 202, iFacePosY = 152;
							iFaceXTime = 290;
						}
						// 切り替えれる画像で
						if( chartable[i].isRenewal == 0 || chartable[i].isRenewal == 1 ){
							// そこを押していたら
							if( MakeHitBox( iFacePosX+i*iFaceXTime, iFacePosY, iFacePosX+66+i*iFaceXTime, iFacePosY+75, DISP_PRIO_CHAR ) == TRUE
							&&  mouse.onceState & MOUSE_LEFT_CRICK
							){
								if( chartable[i].isRenewal == 0 ){	// 旧キャラはリニューアルに直す
									chartable[i].faceGraNo = getReFaceGraphic( chartable[i].faceGraNo );
									chartable[i].isRenewal = 1;	// 切り替え
									giCreateRenewalFlg = 1;
								}else
								if( chartable[i].isRenewal == 1 ){	// リニューアは旧キャラに直す
									chartable[i].faceGraNo = getOldFaceGraphic( chartable[i].faceGraNo );
									chartable[i].isRenewal = 0;	// 切り替え
									giCreateRenewalFlg = 0;
								}
							}
						}
#endif
					}
				}
				else
				{
					// 新规作成ボタン押された
					if( (pushGraBtnInfo2( &pNewBtn[i] ) & BTN_LEFT_CLICK) )
					{
						// 作成キャラ决定
						selectPcNo = i;
						// アルバムの削除
						delAlbum();
						// メールヒストリを削除
						delMailHistory();
						// 家族メールヒストリを削除
						delGuildMailHistory();
						// メールヒストリをファイルに保存
						writeMailFile();
#ifdef PUK2
						writeGuildMailFile();
#endif
						ChangeProc( PROC_CHAR_MAKE );
						// 决定ａ
						play_se( SE_NO_OK, 320, 240 );
#ifdef PUK2
						// アクション初期化
						DeathAllAction();
						if (pActSelGra[0]) pActSelGra[0]=NULL;
						if (pActSelGra[1]) pActSelGra[1]=NULL;
#endif
					}
				}
			}
		}
	}

	// 画面表示。キャラリスト取得后のみ。
	if( SubProcNo >= E_CHARLIST_COMMAND_WAIT
	&&  SubProcNo != E_CHARLIST_INSTALL_ERR
	&&  SubProcNo != E_CHARLIST_INSTALL_ERR_WAIT
	){
		int iH_Offset = 290+1;
		// パラメータの表示
		for( i = 0; i < MAXCHARACTER; i++ ){
			// キャラクターがいたら
			if( existCharacterListEntry( i ) == 0 ){
				// リストにキャラがいない时のボタンを表示
				// 新规ボタン表示
				drawGraBtnInfo2( &pNewBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
				continue;
			}
			//见习版とそれ以外で文字の色を变える
			BYTE  FontCol = 0;
			if( PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
				FontCol = FONT_PAL_BLUE;
			}else
			if( PackageVer >= PV_FIRST_VER2 ){
//				FontCol = FONT_PAL_GRAY;
				FontCol = FONT_PAL_BLUE2;
			}else{
				FontCol = FONT_PAL_WHITE;
			}

#ifdef PUK2
			if(PackageVer >= PV_PUK2){ // バージョン２
				// 颜画像
				StockDispBuffer( 235+i*iH_Offset, 189, DISP_PRIO_CHAR, chartable[i].faceGraNo, 0 );
				// 名称
				len = GetStrWidth( chartable[i].name, FONT_KIND_MIDDLE );
					StockFontBuffer( 175-len/2+i*290, 113,
					FONT_PRIO_BACK, FONT_KIND_MIDDLE, FontCol, chartable[i].name, 0, 0 );

				// 职业
				sprintf( msg, "%s", chartable[i].JobName );                       //MLHIDE
				StockFontBuffer( 134+i*290-strlen( chartable[i].JobName)/2, 233,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 等级
				sprintf( msg, "%4d", chartable[i].lv );                           //MLHIDE
				StockFontBuffer( 140+i*290, 160,FONT_PRIO_BACK, FontCol, msg, 0 );

				// ログイン回数表示
				login = chartable[i].login;
				if( login >= 10000 )login = 9999;	// カウンターストップ
				sprintf( msg, "%4d", login );                                     //MLHIDE
				StockFontBuffer( 140+i*290, 205,FONT_PRIO_BACK, FontCol, msg, 0 );

				if( pActSelGra[i] )
				{
					// キャラの表示
					pActSelGra[i]->anim_no = ANIM_WALK;
					pattern( pActSelGra[i], ANM_NOMAL_SPD, ANM_LOOP );

					// キャラにあたっている时
					if( pActSelGra[i]->hitDispNo == HitDispNo && pActSelGra[i]->atr & ACT_ATR_HIT_BOX ){
						// ボックス表示データをバッファに溜める
						StockBoxDispBuffer( pActSelGra[i]->x + pActSelGra[i]->anim_x - 2,
											pActSelGra[i]->y + pActSelGra[i]->anim_y - 2,
											pActSelGra[i]->x + pActSelGra[i]->anim_x + SpriteInfo[ pActSelGra[i]->bmpNo ].width + 2,
											pActSelGra[i]->y + pActSelGra[i]->anim_y + SpriteInfo[ pActSelGra[i]->bmpNo ].height + 2,
											DISP_PRIO_BOX, BoxColor, 0 );
					}
				}

			}else
#endif
			if( PackageVer >= PV_FIRST_VER2 ){
				// 颜画像
				StockDispBuffer( 235+i*iH_Offset, 189, DISP_PRIO_CHAR, chartable[i].faceGraNo, 0 );
				// 名称
				len = GetStrWidth( chartable[i].name, FONT_KIND_MIDDLE );
					StockFontBuffer( 175-len/2+i*290, 113,
					FONT_PRIO_BACK, FONT_KIND_MIDDLE, FontCol, chartable[i].name, 0, 0 );

				// 职业
				sprintf( msg, "%s", chartable[i].JobName );                       //MLHIDE
				StockFontBuffer( 134+i*290-strlen( chartable[i].JobName)/2, 233,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 等级
				sprintf( msg, "%4d", chartable[i].lv );                           //MLHIDE
				StockFontBuffer( 140+i*290, 160,FONT_PRIO_BACK, FontCol, msg, 0 );

				// ログイン回数表示
				login = chartable[i].login;
				if( login >= 10000 )login = 9999;	// カウンターストップ
				sprintf( msg, "%4d", login );                                     //MLHIDE
				StockFontBuffer( 140+i*290, 205,FONT_PRIO_BACK, FontCol, msg, 0 );


				// 属性のメータ
				for( j = 0; j < 4; j++ ){
					if( chartable[i].attr[j] < 0 )continue;
					x1 = 153+i*291;
					y1 = 288+j*20;
					x2 = x1 + chartable[i].attr[j] * 10-2;
//					if( chartable[i].attr[j] > 1 )x2 +=(chartable[i].attr[j]-1)*2;
					y2 = y1 + 12;
					StockBoxDispBuffer( x1+1, y1,   x2-1, y2,DISP_PRIO_IME2, attrColor[j][0], 1 );
					StockBoxDispBuffer( x1,   y1-1, x2,   y2+1,DISP_PRIO_IME1, attrColor[j][1], 0 );
//					StockBoxDispBuffer( x1+2, y1+2, x2+2, y2+2,DISP_PRIO_IME1, attrColor[j][1], 0 );
				}

			}else{
				// 颜画像
				StockDispBuffer( 99+i*290, 102, DISP_PRIO_CHAR, chartable[i].faceGraNo, 0 );
				// 名称
				len = GetStrWidth( chartable[i].name, FONT_KIND_MIDDLE );
					StockFontBuffer( 208-len/2+i*290, 70,
					FONT_PRIO_BACK, FONT_KIND_MIDDLE, FontCol, chartable[i].name, 0, 0 );

				// 等级
				sprintf( msg, "%3d", chartable[i].lv );                           //MLHIDE
				StockFontBuffer( 226+i*290, 122,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 体力表示(vit)
				sprintf( msg, "%3d", chartable[i].vit );                          //MLHIDE
				StockFontBuffer( 226+i*290, 148,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 攻击力表示(str)
				sprintf( msg, "%3d", chartable[i].str );                          //MLHIDE
				StockFontBuffer( 226+i*290, 170,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 防御力表示(tgh)
				sprintf( msg, "%3d", chartable[i].tgh );                          //MLHIDE
				StockFontBuffer( 226+i*290, 192,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 敏捷力表示(qui)
				sprintf( msg, "%3d", chartable[i].qui );                          //MLHIDE
				StockFontBuffer( 226+i*290, 214,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 魔法力表示(mgc)
				sprintf( msg, "%3d", chartable[i].mgc );                          //MLHIDE
				StockFontBuffer( 226+i*290, 236,FONT_PRIO_BACK, FontCol, msg, 0 );

				// 属性のメータ
				for( j = 0; j < 4; j++ ){
					if( chartable[i].attr[j] < 0 )continue;
					x1 = 195+i*290;
					y1 = 271+j*20;
					x2 = x1 + chartable[i].attr[j] * 10 - 3;
					y2 = y1 + 5;
					StockBoxDispBuffer( x1,   y1,   x2,   y2,DISP_PRIO_IME2, attrColor[j][0], 1 );
					StockBoxDispBuffer( x1+1, y1+1, x2+1, y2+1,DISP_PRIO_IME1, attrColor[j][1], 0 );
					StockBoxDispBuffer( x1+2, y1+2, x2+2, y2+2,DISP_PRIO_IME1, attrColor[j][1], 0 );
				}

				// ログイン回数表示
				login = chartable[i].login;
				if( login >= 10000 )login = 9999;	// カウンターストップ
				sprintf( msg, "%4d", login );                                     //MLHIDE
				StockFontBuffer( 226+i*290, 349,FONT_PRIO_BACK, FontCol, msg, 0 );

			}
			// リストにキャラがいる时のボタンを表示
			// ログインボタン表示
			drawGraBtnInfo2( &pLoginBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
			// 削除ボタン表示
			drawGraBtnInfo2( &pDelBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
		}

		// 返回按键表示
		drawGraBtnInfo2( pBackBtn, DISP_PRIO_CHAR, 0, 0, btnUseFlag );

		// ＢＧ表示
#ifdef PUK2
		if(PackageVer >= PV_PUK2){ // バージョン２
			StockDispBuffer( 320, 240, DISP_PRIO_BG, PUK2_LOGINCHRSEL, 0 );
		}else
#endif
		if(PackageVer == PV_TRIAL){
			StockDispBuffer( 320, 240, DISP_PRIO_BG,CG_LEARN_BASE , 0 );
		}else
		if(PackageVer == PV_EQUAL){
			StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_LEARN_BASE , 0 );
			StockDispBuffer( 320, 240, DISP_PRIO_PARTS, GG_LEARN_FINISHED, 0 );
		}else
		if(PackageVer >= PV_FIRST_VER2 ){ // バージョン２
			StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_CHR_SEL_BG, 0 );
		}else{
			// それ以外は通常と同じ
			StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_SEL_BG, 0 );
		}

		if(PackageVer == PV_FIRST_EX || PackageVer == PV_UP_EX ){ // EXバージョン
			StockDispBuffer( 320, 240, DISP_PRIO_BG+1, EX_CHR_SEL_ICO, 0 );
		}

#ifdef MULTI_GRABIN
		// バージョンアップできそうだったら
		if( CanUpGradeFlag ){
#define UPGB_X 20
#define UPGB_Y 440
#define UPGB_H 30
#define UPGB_W 140
			// アップグレードメニュー
//			if( T_TextButton( UPGB_X, UPGB_Y, UPGB_X+UPGB_W, UPGB_Y+UPGB_H, "ＵＰＧＲＡＤＥ" ) == 1 )
			// アップグレードメニュー
			drawGraBtnInfo2( pUpGradeBtn, DISP_PRIO_CHAR, 0, 0, btnUseFlag );
			// アップグレードメニュー
			if( (pushGraBtnInfo2( pUpGradeBtn ) & BTN_LEFT_CLICK) )
			{
				// アップグレードメニューへ
				ChangeProc( PROC_UPGRADE );
			}
		}
#endif
	}

}

#else
///////////////////////////////////////////////////////////////////////////
//
// キャラ选择画面（削除も出来る）
//
void selectCharacterProc( void )
{
	int ret;
	int i, j;
	int login;
	char msg[256];
	int btnUseFlag = 1;

	static ACTION *ptActMenuWin = NULL;

	int attrColor[4][2] =
	{
		{ SYSTEM_PAL_GREEN,  SYSTEM_PAL_GREEN2  },
		{ SYSTEM_PAL_AQUA,   SYSTEM_PAL_AQUA2   },
		{ SYSTEM_PAL_RED,    SYSTEM_PAL_RED2    },
		{ SYSTEM_PAL_YELLOW, SYSTEM_PAL_YELLOW2 }
	};
	int x1, y1, x2, y2;
	int len;



	// ＢＡＣＫボタン情报
	GRA_BTN_INFO2 backBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		255, 440,
	 // 范围幅(w,h)
		115,  34,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_CHR_SEL_BACK_BTN_2
	};

	// ログインボタン情报
	GRA_BTN_INFO2 loginBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 65, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_LOGIN_LEFT_BTN_1, CG_CHR_SEL_LOGIN_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320+288, 240,
		 // 范围始点座标(x,y)
			 65+288, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_LOGIN_LEFT_BTN_1, CG_CHR_SEL_LOGIN_LEFT_BTN_2
		}
	};
	// 削除ボタン情报
	GRA_BTN_INFO2 delBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			191, 379,
		 // 范围幅(w,h)
			 93, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_DEL_LEFT_BTN_1, CG_CHR_SEL_DEL_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320+288, 240,
		 // 范围始点座标(x,y)
			191+288, 379,
		 // 范围幅(w,h)
			106, 41,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_DEL_LEFT_BTN_1, CG_CHR_SEL_DEL_LEFT_BTN_2
		}
	};
	// 新规ボタン情报
	GRA_BTN_INFO2 newBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_NEW_LEFT_BTN_1, CG_CHR_SEL_NEW_LEFT_BTN_2
		},
		{// 表示座标(x,y)
			320+288, 240,
		 // 范围始点座标(x,y)
			188+288, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_CHR_SEL_NEW_LEFT_BTN_1, CG_CHR_SEL_NEW_LEFT_BTN_2
		}
	};


	// ＢＡＣＫボタン情报
	GRA_BTN_INFO2 learn_backBtn =
	{// 表示座标(x,y)
		320, 240,
	 // 范围始点座标(x,y)
		255, 440,
	 // 范围幅(w,h)
		115,  34,
	 // ボタン画像番号(凸画像,凹画像)
		0, CG_LEARN_BACK_BTN
	};
	// ログインボタン情报
	GRA_BTN_INFO2 learn_loginBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 65, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_LOGIN_BTN_DOWN_L, CG_LEARN_LOGIN_BTN_L
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			 65+288, 374,
		 // 范围幅(w,h)
			 88, 42,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_LOGIN_BTN_DOWN_R, CG_LEARN_LOGIN_BTN_R
		}
	};
	// 削除ボタン情报
	GRA_BTN_INFO2 learn_delBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			191, 379,
		 // 范围幅(w,h)
			 93, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_DELETE_BTN_DOWN_L, CG_LEARN_DELETE_BTN_L
		},
		{// 表示座标(x,y)
			320 , 240,
		 // 范围始点座标(x,y)
			191+288, 379,
		 // 范围幅(w,h)
			93, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_DELETE_BTN_DOWN_R, CG_LEARN_DELETE_BTN_R
		}
	};
	// 新规ボタン情报
	GRA_BTN_INFO2 learn_newBtn[] =
	{
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_CREATE_BTN_DOWN_L, CG_LEARN_CREATE_BTN_L
		},
		{// 表示座标(x,y)
			320, 240,
		 // 范围始点座标(x,y)
			188+288, 380,
		 // 范围幅(w,h)
			 94, 35,
		 // ボタン画像番号(凸画像,凹画像)
			CG_LEARN_CREATE_BTN_DOWN_R, CG_LEARN_CREATE_BTN_R
		}
	};



	// キャラクタリスト取得处理初期化
	if( SubProcNo == 0 )
	{
		SubProcNo++;

		PackageVer = PV_NORMAL;

		initDownloadCharList();
	}
	// キャラクタリスト取得处理
	if( SubProcNo == 1 )
	{
		ret = downloadCharList();
		if( ret == 1 )
		{
			// 取得完了
			SubProcNo = 10;
			//play_bgm(2);
		}
		else
		if( ret == -3 )
		{
			// メンテナンス中
			SubProcNo = 200;
			// ネットワーク初期化
			cleanupNetwork();
		}
		else
		if( ret == -4 )
		{
			// ユーザ认证失败
			SubProcNo = 400;
			// ネットワーク初期化
			cleanupNetwork();
		}
		else
		if( ret < 0 )
		{
			// エラー発生
			SubProcNo = 100;
			// ネットワーク初期化
			cleanupNetwork();
		}
	}

	if( SubProcNo == 10 )
	{
		SubProcNo++;
	}
	if( SubProcNo == 11 )
	{
		btnUseFlag = 0;
	}

	// 削除处理初期化
	if( SubProcNo == 19 )
	{
		//ret = commonYesNoWindow( 320, 240 );	// ohta
		//ret = commonYesNoWindow( 320, 240, "要清除吗？", FONT_PAL_RED );
		ret = DeleteYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER);
		// はい
		if( ret == 1 )
		{
			SubProcNo++;
			//削除时にでる信息ウインドウを表示
#ifdef PUK2_NEW_MENU
			//按窗口分辨率计算显示位置(默认坐标130,300)
			ptActMenuWin = makeWindowDisp( SCREEN_WIDTH_CENTER - 190, SCREEN_HEIGHT_CENTER + 60, 340, 52, 4 );
#else
			ptActMenuWin = makeWindowDisp( SCREEN_WIDTH_CENTER - 190, SCREEN_HEIGHT_CENTER + 60, 340, 52, 1 );
#endif
		}
		else
		// いいえ
		if( ret == 2 )
		{
			SubProcNo = 10;
		}
	}
	// 削除处理初期化
	if( SubProcNo == 20 )
	{
		//ret = commonYesNoWindow( 320, 240 );	// ohta
		ret = commonYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER, ML_STRING(268, "真的要清除吗？"), FONT_PAL_WHITE );
		//ret = DeleteYesNoWindow( 320, 240 );

		if(ptActMenuWin->hp >= 1){

			strcpy( msg, ML_STRING(269, "请按住CTRL键　"));
			//按窗口分辨率计算显示位置(默认坐标166,326)
			StockFontBuffer( SCREEN_WIDTH_CENTER - 154, SCREEN_HEIGHT_CENTER + 86, FONT_PRIO_FRONT, FONT_PAL_YELLOW, msg, 0 );

#ifdef PUK2_NEW_MENU
			strcpy( msg, ML_STRING(270, "点击『是』。        "));
#else
			strcpy( msg, ML_STRING(270, "点击『是』。        "));
#endif
			//按窗口分辨率计算显示位置(默认坐标166,346)
			StockFontBuffer( SCREEN_WIDTH_CENTER - 154, SCREEN_HEIGHT_CENTER + 106, FONT_PRIO_FRONT, FONT_PAL_YELLOW, msg, 0 );


			// はい
			if( ret == 1 )
			{
				if(VK[VK_CONTROL] & KEY_ON){
					SubProcNo++;
					// 即死ＳＥ
					play_se( 284, 320, 240 );
					if( ptActMenuWin )
					{
						DeathAction( ptActMenuWin );
						ptActMenuWin = NULL;
					}

				}
			}
			else
			// いいえ
			if( ret == 2 )
			{
				SubProcNo = 10;
				if( ptActMenuWin )
				{
					DeathAction( ptActMenuWin );
					ptActMenuWin = NULL;
				}
			}

		}


	}

	if( SubProcNo == 21 )
	{
		initDeleteCharacter();
		SubProcNo++;
	}
	// 削除处理
	if( SubProcNo == 22 )
	{
		ret = deleteCharacter();
		if( ret == 1 )
		{
			resetCharacterList( selectPcNo );
			clearUserSetting( selectPcNo );
			if( saveNowState() ){
			}
			if( maxPcNo > 0 )
				maxPcNo--;
			SubProcNo = 10;
		}
		else
		if( ret == -2 )
		{
			SubProcNo = 300;
		}
	}

	// エラー
	if( SubProcNo == 100 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 101 )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_16 ) )
#else
		if( commonMsgWin( ML_STRING(271, "无法取得角色列表。") ) )
#endif
		{
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
	}

	// メンテナンス中エラー
	if( SubProcNo == 200 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 201 )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_17 ) )
#else
		if( commonMsgWin( ML_STRING(272, "现在服务器维护中。") ) )
#endif
		{
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
	}

	// ユーザ认证失败处理
	if( SubProcNo == 400 )
	{
		initUserCertifyErrorMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 401 )
	{
		if( userCertifyErrorMsgWin() )
		{
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
	}

	// キャラ削除失败处理
	if( SubProcNo == 300 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 301 )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_18 ) )
#else
		if( commonMsgWin( ML_STRING(273, "无法删除角色。") ) )
#endif
		{
			// ＯＫボタンが押された
			SubProcNo = 10;
		}
	}



	if( !btnUseFlag )
	{
		//返回按键を押したとき
		if( (pushGraBtnInfo2( &backBtn ) & BTN_LEFT_CLICK) )
		{
			// ネットワーク終了设定
			cleanupNetwork();

			ChangeProc( PROC_TITLE_MENU );
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );

		}
		else
		{

			// キャラリストの数だけ处理する
			for( i = 0; i < MAXCHARACTER ; i++ )
			{
				// キャラがいる时のボタンのチェック
				if( existCharacterListEntry( i ) )//キャラリストにデータが设定されているか检查
				{
					// ログインボタン押された
					if( (pushGraBtnInfo2( &loginBtn[i] ) & BTN_LEFT_CLICK) )
					{
						char name[CHAR_NAME_LEN+1];

						// 使用キャラ决定
						selectPcNo = i;

						strcpy( name, chartable[selectPcNo].name );
						sjisStringToEucString( name );
						strcpy( gamestate_login_charname, name );

						// 初期化时にpc.faceGraNoに入れるため
						newCharacterFaceGraNo = chartable[selectPcNo].faceGraNo;
						//loginDp = chartable[selectPcNo].dp;

						ChangeProc( PROC_CHAR_LOGIN_START );
						// ログイン音
						play_se( SE_NO_LOGIN, 320, 240 );
						createCharFlag = 0;
						fade_out_bgm();
						break;
					}
					else
					// 削除ボタン押された
					if( (pushGraBtnInfo2( &delBtn[i] ) & BTN_LEFT_CLICK) )
					{
						char name[CHAR_NAME_LEN+1];

						// 削除キャラ决定
						selectPcNo = i;

						strcpy( name, chartable[selectPcNo].name );
						sjisStringToEucString( name );
						strcpy( gamestate_deletechar_charname, name );
						SubProcNo = 19;
						// 决定音c（文字等クリック时）
						//play_se( SE_NO_OK3, 320, 240 );
						play_se( SE_NO_NG, 320, 240 );
					}
				}
				else
				{
					// 新规作成ボタン押された
					if( (pushGraBtnInfo2( &newBtn[i] ) & BTN_LEFT_CLICK) )
					{
						// 作成キャラ决定
						selectPcNo = i;
						// アルバムの削除
						delAlbum();
						// メールヒストリを削除
						delMailHistory();
						// 家族メールヒストリを削除
						delGuildMailHistory();
						// メールヒストリをファイルに保存
						writeMailFile();
#ifdef PUK2
						writeGuildMailFile();
#endif
						ChangeProc( PROC_CHAR_MAKE );
						// 决定ａ
						play_se( SE_NO_OK, 320, 240 );
					}
				}
			}
		}
	}

	if( SubProcNo >= 10 )
	{
		// パラメータの表示
		for( i = 0; i < MAXCHARACTER; i++ )
		{
			if( existCharacterListEntry( i ) )
			{


				// 颜画像
				StockDispBuffer( 99+i*290, 102, DISP_PRIO_CHAR, chartable[i].faceGraNo, 0 );


				//见习版とそれ以外で文字の色を变える
				BYTE  FontCol = 0;

				if(PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
					FontCol = FONT_PAL_BLUE;
				}else{
					FontCol = FONT_PAL_WHITE;
				}

				// 名称
				len = GetStrWidth( chartable[i].name, FONT_KIND_MIDDLE );
				StockFontBuffer( 208-len/2+i*290, 70,
					FONT_PRIO_BACK, FONT_KIND_MIDDLE, FontCol, chartable[i].name, 0, 0 );


				// 等级
				sprintf( msg, "%3d", chartable[i].lv );                           //MLHIDE
				StockFontBuffer( 226+i*290, 122,
					FONT_PRIO_BACK, FontCol, msg, 0 );

				// 体力表示(vit)
				sprintf( msg, "%3d", chartable[i].vit );                          //MLHIDE
				StockFontBuffer( 226+i*290, 148,
					FONT_PRIO_BACK, FontCol, msg, 0 );

				// 攻击力表示(str)
				sprintf( msg, "%3d", chartable[i].str );                          //MLHIDE
				StockFontBuffer( 226+i*290, 170,
					FONT_PRIO_BACK, FontCol, msg, 0 );

				// 防御力表示(tgh)
				sprintf( msg, "%3d", chartable[i].tgh );                          //MLHIDE
				StockFontBuffer( 226+i*290, 192,
					FONT_PRIO_BACK, FontCol, msg, 0 );

				// 敏捷力表示(qui)
				sprintf( msg, "%3d", chartable[i].qui );                          //MLHIDE
				StockFontBuffer( 226+i*290, 214,
					FONT_PRIO_BACK, FontCol, msg, 0 );

				// 魔法力表示(mgc)
				sprintf( msg, "%3d", chartable[i].mgc );                          //MLHIDE
				StockFontBuffer( 226+i*290, 236,
					FONT_PRIO_BACK, FontCol, msg, 0 );


				// 属性のメータ
				for( j = 0; j < 4; j++ )
				{
					if( chartable[i].attr[j] > 0 )
					{
						x1 = 195+i*290;
						y1 = 271+j*20;
						x2 = x1 + chartable[i].attr[j] * 10 - 3;
						y2 = y1 + 5;
						StockBoxDispBuffer( x1,   y1,   x2,   y2,
							DISP_PRIO_IME2, attrColor[j][0], 1 );
						StockBoxDispBuffer( x1+1, y1+1, x2+1, y2+1,
							DISP_PRIO_IME1, attrColor[j][1], 0 );
						StockBoxDispBuffer( x1+2, y1+2, x2+2, y2+2,
							DISP_PRIO_IME1, attrColor[j][1], 0 );
					}
				}

				// ログイン回数表示
				login = chartable[i].login;
				if( login >= 10000 )
					login = 9999;	// カウンターストップ
				sprintf( msg, "%4d", login );                                     //MLHIDE
				StockFontBuffer( 226+i*290, 349,
					FONT_PRIO_BACK, FontCol, msg, 0 );

				// リストにキャラがいる时のボタンを表示

				if(PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
					// ログインボタン表示
					drawGraBtnInfo2( &learn_loginBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
					// 削除ボタン表示
					drawGraBtnInfo2( &learn_delBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
				}else{
					// ログインボタン表示
					drawGraBtnInfo2( &loginBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
					// 削除ボタン表示
					drawGraBtnInfo2( &delBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
				}
			}
			else
			if( SubProcNo < 100 || 300 <= SubProcNo )
			{

				// リストにキャラがいない时のボタンを表示
				if(PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
					// 新规ボタン表示
					drawGraBtnInfo2( &learn_newBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
				}else{
					// 新规ボタン表示
					drawGraBtnInfo2( &newBtn[i], DISP_PRIO_CHAR, 0, 0, btnUseFlag );
				}

			}
		}

		// パッケージバージョンが通常だったら
		if(PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
			// 返回按键表示
			drawGraBtnInfo2( &learn_backBtn, DISP_PRIO_CHAR, 0, 0, btnUseFlag );
		}else{
			// 返回按键表示
			drawGraBtnInfo2( &backBtn, DISP_PRIO_CHAR, 0, 0, btnUseFlag );
		}



		// ＢＧ表示
		if(PackageVer == PV_TRIAL)
		{
			StockDispBuffer( 320, 240, DISP_PRIO_BG,CG_LEARN_BASE , 0 );
		}
		else
		if(PackageVer == PV_EQUAL)
		{
			StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_LEARN_BASE , 0 );
			StockDispBuffer( 320, 240, DISP_PRIO_PARTS, GG_LEARN_FINISHED, 0 );

		}
		else
		{
			StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_SEL_BG, 0 );

		}


	}


}
#endif


// キャラクタ削除处理

static short deleteCharacterProcNo = 0;

// 初期化
void initDeleteCharacter( void )
{
	deleteCharacterProcNo = 0;
}

// メイン处理
//
//  戾り值：	 0 ... 削除中
//				 1 ... 削除完了
//				-1 ... タイムアウト
//				-2 ... エラー
int deleteCharacter( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	int ret = 0;
	static char* msg = ML_STRING(276, "删除角色中");

	// 初期化
	if( deleteCharacterProcNo == 0 )
	{
		deleteCharacterProcNo = 1;

		// ウィンドウ作成
		w = strlen( msg )*9/64+2;
		h = (16+47)/48;
		if( h < 2 )
			h = 2;
		x = (DEF_APPSIZEX-w*64)/2;
		y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
	}

	if( deleteCharacterProcNo == 1 )
	{
		// キャラ削除プロトコル送信
		delCharStart();
		deleteCharacterProcNo++;
	}
	else
	if( deleteCharacterProcNo == 2 )
	{
		// 结果待ち
		ret = delCharProc();
	}

	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int len;
			int xx, yy;

			len = strlen( msg );
			xx = (w*64-len*8)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg, 0 );
		}
	}

	return ret;
}



// キャラクタリスト取得处理

static short downloadCharListProcNo = 0;

// 初期化
void initDownloadCharList( void )
{
	downloadCharListProcNo = 0;
}

// メイン处理
//
//  戾り值：	 0 ... 受信中
//				 1 ... 受信完了
//				-1 ... タイムアウト
//				-2 ... エラー
//              -3 ... サーバメンテナンス
//              -4 ... ユーザ认证失败
int downloadCharList( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	int ret = 0;
	static char msg[256];

	// 初期化
	if( downloadCharListProcNo == 0 )
	{
		downloadCharListProcNo = 1;

		strcpy( msg, ML_STRING(277, "取得角色列表中") );

		// ウィンドウ作成
		w = strlen( msg )*8/64+2;
		h = (16+47)/48;
		if( h < 2 )
			h = 2;
		x = (DEF_APPSIZEX-w*64)/2;
		y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
	}

	// キャラリスト取得
	if( downloadCharListProcNo == 1 )
	{
		charListStart();
		downloadCharListProcNo++;
	}
	else
	if( downloadCharListProcNo == 2 )
	{
		ret = charListProc();
	}

	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int len;
			int xx, yy;

			len = strlen( msg );
			xx = (w*64-len*8)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg, 0 );
		}
	}

	return ret;
}


// はい、いいえ、选择ウィンドウ

ACTION *ptActCommonYesNoWindow = NULL;

// 初期化
void initCommonYesNoWindow( void )
{
	ptActCommonYesNoWindow = NULL;
}

//  引　数：    x, y ... 表示座标
//
//  戾り值：	0 ... 处理中
//				1 ... はいボタンが押された
//				2 ... 否按键が押された
int commonYesNoWindow2( int _x, int _y, char *msg, int fontColor, int xsize, int ysize )
{
	//char *msg = "可以吗？";
	int w = (xsize <= 0)?(64*3):(xsize);
	int h = (ysize <= 0)?(48*2):(ysize);
	int x = _x - w/2;
	int y = _y - h/2;
	int xx, yy;
	int ret = 0;
#ifdef PUK2_NEW_MENU
	int over = 0;
#endif
/*
	STR_BTN_INFO yesBtn =
	{
		x +30, y +54,
		x +28, y +54, 55, 18,  "是"
	};
	STR_BTN_INFO noBtn =
	{
		x+112, y +54,
		x+110, y +54, 55, 18,  "いいえ"
	};
*/
	STR_BTN_INFO yesBtn =
	{
		_x -66, y +54,
		_x -64, y +54, 55, 18,  ML_STRING(278, "是")
	};
	STR_BTN_INFO noBtn =
	{
		_x+16, y +54,
		_x+18, y +54, 55, 18,  "いいえ"                                        //MLHIDE
	};
	if( ptActCommonYesNoWindow == NULL )
	{
		ptActCommonYesNoWindow =
#ifdef PUK2_NEW_MENU
			makeWindowDisp( x, y, w, h, 4 );
#else
			makeWindowDisp( x, y, w, h, 1 );
#endif
		// ウィンドウ开く音
		//play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}
	// アクションできてないなら終わる
	if( ptActCommonYesNoWindow == NULL )
		return 0;
	// エフェクト中なら終わる
	if( ptActCommonYesNoWindow->hp <= 0 )
		return 0;

#ifdef PUK2_NEW_MENU
	if ( MakeHitBox( _x-40-33 , y+59, x+92+66, y+59+17, -1 ) ) over = 1;
	if ( MakeHitBox( _x+40-33, y+59, x+92+66, y+59+17, -1 ) ) over = 2;

	// 右键されてなければ終了
	if( over && (mouse.onceState & MOUSE_LEFT_CRICK) ) {
		ret = over;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}
#else
	// "是"ボタンが押されたかチェック
	if( (pushStrBtnInfo( &yesBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 1;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}
	else
	// "いいえ"ボタンが押されたかチェック
	if( (pushStrBtnInfo( &noBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 2;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}
#endif

	if( ret > 0 )
	{
		if( ptActCommonYesNoWindow )
		{
			DeathAction( ptActCommonYesNoWindow );
			ptActCommonYesNoWindow = NULL;
		}
	}

#ifdef PUK2_NEW_MENU
	// Yesを表示
	StockDispBuffer( _x-40, y+59+8, DISP_PRIO_WIN2, (over==1 ? GID_BigYesButtonOver : GID_BigYesButtonOn ), 0 );
	// Noを表示
	StockDispBuffer( _x+40, y+59+8, DISP_PRIO_WIN2, (over==2 ? GID_BigNoButtonOver : GID_BigNoButtonOn ), 0 );
#else
	// "是"を表示
	drawStrBtnInfo( &yesBtn, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
			1, BoxColor, DISP_PRIO_BOXFILL );

	// "いいえ"を表示
	drawStrBtnInfo( &noBtn, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
			1, BoxColor, DISP_PRIO_BOXFILL );
#endif

	// 信息表示
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy = 24;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, fontColor,
		msg, 0, 0 );

	return ret;

#if 0
	int i;
	int ret = 0;
	int id;
	static int btnGraId[] = { -2, -2 };

	id = selGraId( btnGraId, sizeof( btnGraId )/sizeof( int ) );
	// はいボタンが押された
	if( id == 0 )
	{
		ret = 1;
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}
	else
	// 否按键が押された
	if( id == 1 )
	{
		ret = 2;
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}

	btnGraId[0] = StockDispBuffer( x, y, DISP_PRIO_YES_NO_BTN, CG_COMMON_YES_BTN, 2 );
	btnGraId[1] = StockDispBuffer( x, y, DISP_PRIO_YES_NO_BTN, CG_COMMON_NO_BTN, 2 );

	StockDispBuffer( x, y, DISP_PRIO_YES_NO_WND, CG_COMMON_WIN_YORO, 0 );

	if( ret != 0 )
	{
		for( i = 0; i < sizeof( btnGraId )/sizeof( int ); i++ )
		{
			btnGraId[i] = -2;
		}
	}

	return ret;
#endif
}

int commonYesNoWindow( int _x, int _y, char *msg, int fontColor ){
	return commonYesNoWindow2( _x, _y, msg, fontColor, 0,0 );
}


// デリート专用确认ウィンドウ
int DeleteYesNoWindow( int _x, int _y )
{
	char msg[ 256 ];
	int w = 64*6;
	int h = 48*4;
	int x = _x - w/2;
	int y = _y - h/2;
	int xx, yy;
	int ret = 0;

	STR_BTN_INFO yesBtn =
	{
		x +74, y +144,
		x +72, y +144, 90, 18,  LANG_LOGIN_CPP_YES
	};
	STR_BTN_INFO noBtn =
	{
		x+220, y +144,
		x+218, y +144, 90, 18,  LANG_LOGIN_CPP_NO
	};

	if( ptActCommonYesNoWindow == NULL )
	{
		ptActCommonYesNoWindow =
#ifdef PUK2_NEW_MENU
			makeWindowDisp( x, y, w, h, 4 );
#else
			makeWindowDisp( x, y, w, h, 1 );
#endif
		// ウィンドウ开く音
		//play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}
	// アクションできてないなら終わる
	if( ptActCommonYesNoWindow == NULL )
		return 0;
	// エフェクト中なら終わる
	if( ptActCommonYesNoWindow->hp <= 0 )
		return 0;

	// "是"ボタンが押されたかチェック
	if( (pushStrBtnInfo( &yesBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 1;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}
	else
	// "いいえ"ボタンが押されたかチェック
	if( (pushStrBtnInfo( &noBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 2;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}

	if( ret > 0 )
	{
		if( ptActCommonYesNoWindow )
		{
			DeathAction( ptActCommonYesNoWindow );
			ptActCommonYesNoWindow = NULL;
		}
	}

	// "是"を表示
	drawStrBtnInfo( &yesBtn, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
			1, BoxColor, DISP_PRIO_BOXFILL );

	// "いいえ"を表示
	drawStrBtnInfo( &noBtn, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
			1, BoxColor, DISP_PRIO_BOXFILL );

	// 信息表示
	strcpy( msg, ML_STRING(281, "＊＊＊ 警告 ＊＊＊") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy = 20;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_YELLOW,
		msg, 0, 0 );

	strcpy( msg, ML_STRING(282, "所选择的人物一经删除。") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy += 32;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	strcpy( msg, ML_STRING(283, "将无法恢复") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy += 24;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	strcpy( msg, ML_STRING(284, "确定删除吗？") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy += 24;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	return ret;
}


#ifdef MULTI_GRABIN
//------------------------------------------------------------
//  アップグレードフォーム
//------------------------------------------------------------
#define TOEX_BUTTON_X	320 //243
#define TOEX_BUTTON_Y	240 //(427-32)

#define TOVER2_BUTTON_X	320	//390
#define TOVER2_BUTTON_Y	240 //(427-32)

#define TOCANCEL_BUTTON_X	320
#define TOCANCEL_BUTTON_Y	240//(427)
	// ＥＸボタン
	GRA_BTN_INFO2 ToExBtn ={
		TOEX_BUTTON_X, TOEX_BUTTON_Y,	// 表示座标(x,y)
		342, 251,	// 范围始点座标(x,y)
		290, 60,		// 范围幅(w,h)
		CG_CHAR_UPG_TOEX_UP, CG_CHAR_UPG_TOEX_DOWN	// ボタン画像番号(凸画像,凹画像)
	};
	// ＶＥＲ２ボタン
	GRA_BTN_INFO2 ToVer2Btn =	{
		TOVER2_BUTTON_X, TOVER2_BUTTON_Y,	// 表示座标(x,y)
		342, 316,	// 范围始点座标(x,y)
		290, 60,		// 范围幅(w,h)
		CG_CHAR_UPG_TOVER2_UP, CG_CHAR_UPG_TOVER2_DOWN	// ボタン画像番号(凸画像,凹画像)
	};
	// キャンセルボタン
	GRA_BTN_INFO2 ToCancelBtn =	{
		TOCANCEL_BUTTON_X, TOCANCEL_BUTTON_Y,	// 表示座标(x,y)
		292,426,	// 范围始点座标(x,y)
		72,  44,	// 范围幅(w,h)
		0, CG_CHAR_UPG_BACK	// ボタン画像番号(凸画像,凹画像)
	};

enum{
	E_UPGRADE_INIT,			// 初期化
	E_UPGRADE_COMMAND_INIT,	//
	E_UPGRADE_COMMAND_WAIT,	// 入力モード
	E_UPGRADE_VERUP_INIT,	// バージョンアップ
	E_UPGRADE_VERUP_DOING,	// バージョンアップ中


	E_UPGRADE_VERUP_OK = 100,	// バージョンアップＯＫ
	E_UPGRADE_VERUP_OK_WAIT,	// バージョンアップＯＫ确认
	E_UPGRADE_VERUP_ERR = 200,	// バージョンエラー
	E_UPGRADE_VERUP_ERR_WAIT,	// バージョンエラー确认

	E_UPGRADE_ALREADY,		// すでにそのバージョンです。
	E_UPGRADE_ALREADY_WAIT,	// すでにそのバージョンです。

};

char* UpGradeWaitMsg = ML_STRING(285, "升级中...");

INPUT_STR Upgrade_Product; // プロダクトキー入力バッファ
void UpGradeProc( void )
{
	static ACTION *ptActMenuWin = NULL;
	int flag = 0;
	int selUseFlag = 0;

	switch( SubProcNo ){

	case E_UPGRADE_INIT:
		ptActMenuWin = NULL;
		// 入力フォーム初期化
		InitInputStr( &Upgrade_Product, 372, 204, FONT_PRIO_FRONT, FONT_KIND_MIDDLE,
		FONT_PAL_BLUE, NULL, 1, 24, 0, 0 );
		SubProcNo = E_UPGRADE_COMMAND_WAIT;
		break;

	case E_UPGRADE_COMMAND_INIT:
		SubProcNo = E_UPGRADE_COMMAND_WAIT;
		break;
	case E_UPGRADE_COMMAND_WAIT:
		// フォーカス设定
		GetKeyInputFocus( &Upgrade_Product );
		// 文字列入力されてなかったらボタン押せなくする。
		if( strlen( Upgrade_Product.buffer ) <= 0 ){
			flag = 0;
		}else{
			flag = 1;
		}
		//----------- ボタン入力 ------------

		//バッファの最后に終了マークを付ける
		Upgrade_Product.buffer[Upgrade_Product.cnt] = '\0';

		// ＥＸへのバージョンアップはなくなりました。
		//----------------------
		// ＥＸボタンが押された
		//----------------------
		if(	PackageVer < PV_FIRST_EX // Ｖ２以上だったら表示しない。
		&&	giInstallVersion >= 1  // 自分がＥＸ入れてたら
		&& (pushGraBtnInfo2( &ToExBtn ) & BTN_LEFT_CLICK) && flag
//		&& T_TextButton( 100, 300, 180, 330, "ＴｏＥＸ" ) == 1 && flag
		){
			int kind = 0;
			// 自分が通常だったら
			if( PackageVer == PV_NORMAL || PackageVer == PV_EQUAL ){
				kind = 1; // 通常からＥＸへ
				nrproto_PVUP_send( sockfd, kind, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else{
				// それ以外は必要ないよ。
				SubProcNo = E_UPGRADE_ALREADY;
			}
		}else

		//----------------------
		// Ｖ２ボタンが押された
		//----------------------
		if(	giInstallVersion >= 2
		&& (pushGraBtnInfo2( &ToVer2Btn ) & BTN_LEFT_CLICK) && flag
//		&& T_TextButton( 400, 300, 475, 330, "ＴｏＶ２" ) == 1 && flag
		){
			int kind = 0;
#ifdef PUK2
			// 自分がＶ２だったら
			if( PackageVer == PV_FIRST_VER2 || PackageVer == PV_UP_VER2 ){
				// 通常からＶ２へ
				nrproto_PVUP_send( sockfd, 4, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else
#endif
			// 自分が通常または卒业济みだったら
			if( PackageVer == PV_NORMAL || PackageVer == PV_EQUAL
//			||  PackageVer == PV_FIRST_EX || PackageVer == PV_UP_EX
			){
				// 通常からＶ２へ
				nrproto_PVUP_send( sockfd, 2, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else{
				// それ以外は必要ないよ。
				SubProcNo = E_UPGRADE_ALREADY;
			}
		}else
		//----------------------
		// キャンセルされた
		//----------------------
		if( (pushGraBtnInfo2( &ToCancelBtn ) & BTN_LEFT_CLICK) )
//		if(	T_TextButton( 260, 400, 370, 430, "ＣＡＮＣＥＬ" ) == 1 )
		{
#ifdef PUK2
			if (PackageVer >= PV_FIRST_VER2){
				PaletteChange( 29, 0 );	// フェードイン
				ChangeProc( PROC_TITLE_MENU );
			}else ChangeProc( PROC_CHAR_SELECT );
#else
			// 必要ないよ。
			ChangeProc( PROC_CHAR_SELECT );
#endif
		}
		break;

	case E_UPGRADE_VERUP_INIT:	// バージョンアップ开始
		ptActMenuWin = NULL;
		{
			// ウインドウ表示
			int w, h, x, y;
			// ウィンドウ作成
			w = (GetStrWidth( UpGradeWaitMsg, FONT_KIND_MIDDLE )+48+63)/64;
			if( w < 2 )w = 2;
			h = (16+47)/48;
			if( h < 2 )h = 2;
			x = (DEF_APPSIZEX-w*64)/2;
			y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
			ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
			ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
		}
		SubProcNo = E_UPGRADE_VERUP_DOING;
		break;

	case E_UPGRADE_VERUP_DOING:	// バージョンアップ中??????
		// このままサーバーから应答がなかったらあきらめます。
		if( ptActMenuWin != NULL && ptActMenuWin->hp == 1 ){	// ウインドウが开いたら文字表示
			int xx, yy;
			xx = 320 - (GetStrWidth( UpGradeWaitMsg, FONT_KIND_MIDDLE ))/2;
			yy = 232;
			StockFontBuffer( xx, yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
			UpGradeWaitMsg, 0, 0 );
		}
		break;

	case E_UPGRADE_VERUP_ERR:	// バージョンアップ失败
		if( ptActMenuWin != NULL )DeathAction( ptActMenuWin );
#ifdef PUK3_CHECK_VALUE
		ptActMenuWin = NULL;
#endif

		initCommonMsgWin();
		SubProcNo = E_UPGRADE_VERUP_ERR_WAIT;
		break;
	case E_UPGRADE_VERUP_ERR_WAIT:	// バージョンアップ失败确认
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_20 ) ){
#else
		if( commonMsgWin( ML_STRING(286, "升级失败。") ) ){
#endif
			// ＯＫボタンが押された
			SubProcNo = E_UPGRADE_COMMAND_INIT;
		}
		break;

	case E_UPGRADE_VERUP_OK:	// バージョンアップ成功
		if( ptActMenuWin != NULL )DeathAction( ptActMenuWin );
#ifdef PUK3_CHECK_VALUE
		ptActMenuWin = NULL;
#endif

		SubProcNo = E_UPGRADE_VERUP_OK_WAIT;
		initCommonMsgWin();
		break;
	case E_UPGRADE_VERUP_OK_WAIT:	// バージョンアップ成功
		if( commonMsgWin( ML_STRING(287, "升级成功了。") ) ){
			// キャラリスト再取得
			ChangeProc( PROC_CHAR_SELECT );
		}
		break;


	// すでにそのバージョンです。
	case E_UPGRADE_ALREADY:
		SubProcNo = E_UPGRADE_ALREADY_WAIT;
		initCommonMsgWin();
		break;
	case E_UPGRADE_ALREADY_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_21 ) ){
#else
		if( commonMsgWin( ML_STRING(288, "无法从这个版本升级。") ) ){
#endif
			// ＯＫボタンが押された
			SubProcNo = E_UPGRADE_COMMAND_INIT;
		}
		break;

	default :
		break;
	}

	//----------------  画面表示 -------------------
	if(	PackageVer < PV_FIRST_EX 	// ＥＸ未满の人
	&&	PackageVer != PV_TRIAL 		// 见习いだったら表示しない。
	&&	giInstallVersion < 2		// インストールバージョンがＶ２未满
	){
		// ＥＸボタン
		drawGraBtnInfo2( &ToExBtn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
	}

	if(	PackageVer < PV_FIRST_VER2 	// Ｖ２未满の人
	&&	PackageVer != PV_TRIAL 		// 见习いだったら表示しない。
	&&	giInstallVersion >= 2 		// インストールバージョンがＶ２以上
	){
		// ＶＥＲ２ボタン
		drawGraBtnInfo2( &ToVer2Btn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
	}
#ifdef PUK2
	else
	if(	PV_FIRST_VER2<=PackageVer && PackageVer<=PV_UP_VER2 	// Ｖ２の人
	&&	giInstallVersion >= 3 		// インストールバージョンがPUK2以上
	){
		if( strlen( Upgrade_Product.buffer ) > 0 ){
			// ＶＥＲ２ボタン
			drawGraBtnInfo2( &ToVer2Btn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
		}
	}
#endif
	// キャンセルボタン
	drawGraBtnInfo2( &ToCancelBtn, DISP_PRIO_CHAR, 0, 0, selUseFlag );

	// 入力した文字列を表示
	StockFontBuffer2( &Upgrade_Product );
	// ＢＧ表示
	StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHAR_UPGRADE, 0 );
	//---------------------------------------------

}
#endif


///////////////////////////////////////////////////////////////////////////
//
// キャラ作成
//

// キャラ画像选择用

#define	MaxSelectChar  	14
ACTION *ptActSelChar[MaxSelectChar];
/*
// 昔の并び方。今はこのテーブルは使用しない
int selectGraNoNormalTbl[] =	// キャラの番号
{
	SPR_020em,	//  0 シン
	SPR_220em,	//  1 アミ
	SPR_030em,	//  2 トブ
	SPR_010em,	//  3 カズ
	SPR_260em,	//  4 エル
	SPR_060em,	//  5 ボグ
	SPR_250em,	//  6 ケイ
	SPR_210em,	//  7 モエ
	SPR_040em,	//  8 ゲン
	SPR_230em,	//  9 メグ
	SPR_000em,	// 10 バウ
	SPR_240em,	// 11 レイ
	SPR_050em,	// 12 ベイ
	SPR_200em,	// 13 ウル
};
*/
#ifdef MULTI_GRABIN
// ＥＸプログラムで使うＥＸ用新キャラの姿のテーブル
int V2_selectNewCharaTbl[] =
{
	SPR_600em,SPR_610em,SPR_620em,SPR_630em,SPR_640em,SPR_650em,SPR_660em,
	SPR_700em,SPR_710em,SPR_720em,SPR_730em,SPR_740em,SPR_750em,SPR_760em,
};
// Ｖ２プログラムで使う旧キャラのテーブル
int selectGraNoOldTbl[] =	// 昔のキャラの并びが违うやつ
{
	SPR_000em,	SPR_010em,	SPR_020em,	SPR_030em,	SPR_040em,	SPR_050em,	SPR_060em,
	SPR_200em,	SPR_210em,	SPR_220em,	SPR_230em,	SPR_240em,	SPR_250em,	SPR_260em,
};

// Ｖ２プログラムで使う旧キャラの颜テーブル
int V2_selectFaceOldTbl[] =	// 颜の番号
{
	CG_FACE_0,	CG_FACE_1,	CG_FACE_2,	CG_FACE_3,	CG_FACE_4,	CG_FACE_5,	CG_FACE_6,
	CG_FACE_10,	CG_FACE_11,	CG_FACE_12, CG_FACE_13,	CG_FACE_14,	CG_FACE_15,	CG_FACE_16,
};

// Ｖ２プログラムで使うリニューアルキャラのテーブル
int V2_selectRemakeCharTbl[] =	// リニューアル
{
	SPR_400em,	SPR_410em,	SPR_420em,	SPR_430em,	SPR_440em,	SPR_450em,	SPR_460em,
	SPR_500em,	SPR_510em,	SPR_520em,	SPR_530em,	SPR_540em,	SPR_550em,	SPR_560em,
};

int V2_selectNewFaceTbl[] =	// Ｖ２キャラ颜の番号
{
	V2_FACE_0,	V2_FACE_1,	V2_FACE_2,	V2_FACE_3,	V2_FACE_4,
	V2_FACE_5,	V2_FACE_6,	V2_FACE_7,	V2_FACE_8,	V2_FACE_9,
	V2_FACE_10,	V2_FACE_11,	V2_FACE_12,	V2_FACE_13
};

int V2_selectRemakeFaceTbl[] =	// リニューアルキャラ颜の番号
{
	RN_FACE_0,	RN_FACE_1,	RN_FACE_2,	RN_FACE_3,	RN_FACE_4,
	RN_FACE_5,	RN_FACE_6,	RN_FACE_7,	RN_FACE_8,	RN_FACE_9,
	RN_FACE_10,	RN_FACE_11,	RN_FACE_12,	RN_FACE_13
};

#ifdef PUK2

int PUK2_selectOldFaceTbl[] =	// １キャラ颜の番号
{
	PUK2_FACE_01,PUK2_FACE_02,PUK2_FACE_03,PUK2_FACE_04,PUK2_FACE_05,PUK2_FACE_06,PUK2_FACE_07,
	PUK2_FACE_08,PUK2_FACE_09,PUK2_FACE_10,PUK2_FACE_11,PUK2_FACE_12,PUK2_FACE_13,PUK2_FACE_14,
};

int PUK2_selectNewFaceTbl[] =	// ２キャラ颜の番号
{
	PUK2_FACE_15,PUK2_FACE_16,PUK2_FACE_17,PUK2_FACE_18,PUK2_FACE_19,PUK2_FACE_20,PUK2_FACE_21,
	PUK2_FACE_22,PUK2_FACE_23,PUK2_FACE_24,PUK2_FACE_25,PUK2_FACE_26,PUK2_FACE_27,PUK2_FACE_28,
};

#endif

#ifdef TW_CHARA
//台湾版选择人物界面中使用的新头像和新动画
int TW_selectCharTbl[] =
{
	TW_SPR_100em, TW_SPR_101em, TW_SPR_102em, TW_SPR_103em, TW_SPR_104em, TW_SPR_105em, TW_SPR_106em,
	100900, 100900, 100900, TW_SPR_107em, 100900, 100900, 100900,
};

int TW_selectFaceTbl[] =
{
	TW_FACE_01,TW_FACE_02,TW_FACE_03,TW_FACE_04,TW_FACE_05,TW_FACE_06,TW_FACE_07,
	33100,33100,33100,TW_FACE_11,33100,33100,33100,
};
#endif

#endif
/*
int selectFaceGraNoTbl[] =	// 颜の番号
{
	CG_FACE_2,	//  0 シン
	CG_FACE_12, //  1 アミ
	CG_FACE_4,	//  2 トブ
	CG_FACE_1,	//  3 カズ
	CG_FACE_16,	//  4 エル
	CG_FACE_6,	//  5 ボグ
	CG_FACE_15,	//  6 ケイ
	CG_FACE_11,	//  7 モエ
	CG_FACE_3,	//  8 ゲン
	CG_FACE_13,	//  9 メグ
	CG_FACE_0,	// 10 バウ
	CG_FACE_14,	// 11 レイ
	CG_FACE_5,	// 12 ベイ
	CG_FACE_10	// 13 ウル
};
*/
typedef struct {
	int x, y;
}SELGRAPOS;
/*
SELGRAPOS aSelGraPos[] = {	// 表示位置
	{ 239, 376 },	//  0 シン
	{ 239, 268 },	//  1 アミ
	{ 319, 376 },	//  2 トブ
	{ 159, 376 },	//  3 カズ
	{ 559, 268 },	//  4 エル
	{ 559, 376 },	//  5 ボグ
	{ 479, 268 },	//  6 ケイ
	{ 159, 268 },	//  7 モエ
	{ 399, 376 },	//  8 ゲン
	{ 399, 268 },	//  9 メグ
	{  79, 376 },	// 10 バウ
	{ 319, 268 },	// 11 レイ
	{ 479, 376 },	// 12 ベイ
	{  79, 268 }	// 13 ウル
};
*/

// ＥＸ专用
SELGRAPOS aSelGraPosEx[] = {	// 表示位置
	{  79, 376 },	// 10 バウ
	{ 159, 376 },	//  3 カズ
	{ 239, 376 },	//  0 シン
	{ 319, 376 },	//  2 トブ
	{ 399, 376 },	//  8 ゲン
	{ 479, 376 },	// 12 ベイ
	{ 559, 376 },	//  5 ボグ

	{  79, 268 },	// 13 ウル
	{ 159, 268 },	//  7 モエ
	{ 239, 268 },	//  1 アミ
	{ 319, 268 },	// 11 レイ
	{ 399, 268 },	//  9 メグ
	{ 479, 268 },	//  6 ケイ
	{ 559, 268 },	//  4 エル
/*
	{ 239+10, 376 },	//  0 シン
	{ 399-10, 376 },	//  8 ゲン
	{ 239+10, 268 },	//  1 アミ
	{ 399-10, 268 },	//  3 カズ
	// 使うのはここまで

	{ 559, 376 },	//  5 ボグ
	{ 319, 376 },	//  2 トブ
	{ 559, 268 },	//  4 エル
	{ 479, 268 },	//  6 ケイ
	{ 159, 268 },	//  7 モエ
	{ 399, 268 },	//  9 メグ
	{  79, 376 },	// 10 バウ
	{ 319, 268 },	// 11 レイ
	{ 479, 376 },	// 12 ベイ
	{  79, 268 }	// 13 ウル
*/
};

typedef struct {
	int *pSelectGraNoTbl;
	int *pSelectFaceGraNoTbl;
	SELGRAPOS *pSelectGraNoPos;
	int size;
}SELECTGRANO;


SELECTGRANO aSelGraNoTbl[] = {
#if 1
	// 0番 通常、ＥＸ、Ｖ２のどれにも使用する。
	{ (int *)selectGraNoOldTbl, (int *)V2_selectFaceOldTbl,
		(SELGRAPOS *)aSelGraPosEx, arraysizeof(selectGraNoOldTbl) },
#else
	// 0番 通常、ＥＸ、Ｖ２のどれにも使用する。
	{ (int *)selectGraNoNormalTbl, (int *)selectFaceGraNoTbl,
		(SELGRAPOS *)aSelGraPos, arraysizeof(selectGraNoNormalTbl) },
#endif
#ifdef MULTI_GRABIN
	// 1番  ＥＸ时に使用するページ
	{ (int *)V2_selectNewCharaTbl, (int *)V2_selectNewFaceTbl,
		(SELGRAPOS *)aSelGraPosEx, arraysizeof(V2_selectNewCharaTbl) },

	// ２番 Ｖ２のキャラに使用する。
	{ (int *)V2_selectNewCharaTbl, (int *)V2_selectFaceOldTbl,
		(SELGRAPOS *)aSelGraPosEx, arraysizeof(V2_selectNewCharaTbl) },

	// ３番 Ｖ２のリニューアルに使用する。
	{ (int *)V2_selectRemakeCharTbl, (int *)V2_selectRemakeFaceTbl,
		(SELGRAPOS *)aSelGraPosEx, arraysizeof(V2_selectRemakeCharTbl) }
#endif
#ifdef PUK2
	,
	// ４番
	{ 0, 0, 0, 0 },
	// ５番 Ｖ２のリニューアルに使用する。
	{ (int *)V2_selectNewCharaTbl, (int *)PUK2_selectNewFaceTbl,
		(SELGRAPOS *)aSelGraPosEx, arraysizeof(V2_selectNewCharaTbl) },

	// ６番 Ｖ２のキャラに使用する。
	{ (int *)V2_selectRemakeCharTbl, (int *)PUK2_selectOldFaceTbl,
		(SELGRAPOS *)aSelGraPosEx, arraysizeof(V2_selectRemakeCharTbl) },
#endif
};

enum{
	GRANO_NORMAL = 0,
	GRANO_EX = 1,
	GRANO_V2 = 2,
	GRANO_RENEW = 3,
};

static int giCreatePageNo = 0;	// キャラ作成画面のページその１
static int selCharDir[MaxSelectChar];	// キャラの向き
static int nowSelCharNo = 0;			// キャラの种类
static int nowSelCharMouthNo;		// キャラの口の形
static int nowSelCharEyeNo;			// キャラの目の形
static int nowSelCharStatusPoint;	// キャラの状态に割り振れるポイント
static int nowSelCharStatus[5];		// キャラの状态(体力、腕力、防御力、敏捷力、魔法力)
static int nowSelCharAttrPoint;		// キャラの属性に割り振れるポイント
static int nowSelCharAttr[4];		// キャラの属性(地水火风)
static int nowSelCharColor;			// キャラの色

// キャラの色变更ウィンドウ处理用
static int selCharGraColorWinProcNo;

// キャラクタのパラメータ设定用
static int editCharParamProcNo;
static INPUT_STR selCharName;
static int selCharNameBoxX, selCharNameBoxY;
static int selCharFaceNo;	// 决定した颜グラフィック
static int selCharFigureNo;	// 决定したキャラのグラフィック
static int selCharSubFigureNo;	// 决定したキャラのもうひとつのグラフィック
static int selCharNew_Pattern = 0;	// バージョン１以外のキャラなら１

// 新キャラ作成处理用
static int createCharProcNo;



void initMakeCharacter( void );

void V2_initSelCharGraNo( void );
void V2_initSelCharGraNo2( void );
int selCharGraNoV2( void );

void initSelCharGraNo( void );
void initSelCharGraNo2( void );
int selCharGraNo( void );
void initEditCharParam( void );
void initEditCharParam2( void );
int editCharParam( void );
void initCreateChar( void );
int createChar( void );

enum{
	V2_GRANO_NEW,		// 新キャラ选择
	V2_GRANO_NORMAL,	// リニューアルキャラ选择
	V2_GRANO_OLD,		// 昔キャラ选择

	// 以下 EXのみ
	V2_GRANO_EX_RENEW,	// ＥＸリニューアルキャラ选择
	V2_GRANO_EX,		// ＥＸ新キャラ选择

#ifdef PUK2
	PUK2_GRANO_NEW,		// 新キャラ选择
	PUK2_GRANO_OLD,		// 昔キャラ选择
#endif

#ifdef TW_CHARA
	TW_GRANO,
#endif
};




// キャラ作成メイン处理初期化
void initMakeCharacter( void )
{
}

// キャラ作成メイン处理
void makeCharacterProc( void )
{
	int ret;

	// 初期化
	if( SubProcNo == 0 )
	{
		giCreateRenewalFlg = 0;	// 最初は０旧キャラにする。
		giCreatePageNo = 0;	// 最初は０通常のキャラにする。
		initMakeCharacter();
		SubProcNo++;
		if( PackageVer >= PV_FIRST_VER2 ){
			nowSelCharNo = 0;	// 初期キャラ选择は０番目
		}else{
#ifdef USE_VERSION2_FRONTEND
			giCreatePageNo = V2_GRANO_OLD;	// 初期值は昔のもの
#endif
			nowSelCharNo = 2;	// 初期キャラ选择は２番目
		}

	}

	// キャラ画像选择の初期化
	if( SubProcNo == 1 )
	{
#ifdef USE_VERSION2_FRONTEND
		V2_initSelCharGraNo();	// バージョン２用
#else
		initSelCharGraNo();
#endif
		SubProcNo = 3;
	}
	else
	if( SubProcNo == 2 )
	{
#ifdef USE_VERSION2_FRONTEND
		V2_initSelCharGraNo2();	// バージョン２用
#else
		initSelCharGraNo2();
#endif
		SubProcNo++;
	}
	// キャラ画像选择处理
	if( SubProcNo == 3 )
	{
#ifdef USE_VERSION2_FRONTEND
		ret = selCharGraNoV2();	// バージョン２用
#else
		ret = selCharGraNo();
#endif
		// キャラ选择决定
		if( ret == 1 )
		{
			SubProcNo = 10;
		}
		else
		// 返回按键押された
		if( ret == 2 )
		{
			ChangeProc( PROC_CHAR_SELECT, 10 );
//			return;
		}
	}

	// キャラのパラメータ设定の初期化
	if( SubProcNo == 10 && ret == 1 )
	{
		initEditCharParam();
		SubProcNo = 12;
	}
	else
	if( SubProcNo == 11 )
	{
		initEditCharParam2();
		SubProcNo = 12;
	}
	if( SubProcNo == 12 )
	{
		ret = editCharParam();
		if( ret == 1 )
		{
			SubProcNo = 30;
			// 以前のセーブデータを初期化
			resetCharacterList( selectPcNo );
			clearUserSetting( selectPcNo );
			if( saveNowState() )
			{
			}

		}
		// キャンセル
		if( ret == 2 )
		{
			SubProcNo = 2;
		}
	}


	// キャラ作成プロトコルを送信
	if( SubProcNo == 30 )
	{
		initCreateChar();
		SubProcNo = 31;
	}
	if( SubProcNo == 31 )
	{
		ret = createChar();
		if( ret == 1 )
		{
			maxPcNo++;
			strcpy( gamestate_login_charname, newCharacterName );
			ChangeProc( PROC_CHAR_LOGIN_START );
			createCharFlag = 1;
			//return;
		}
		else
		if( ret == 2 )
		{
			SubProcNo = 100;
		}
//		selHomeTown();	// 画面作るため
	}

	// エラー
	if( SubProcNo == 100 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 101 )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_22 ) )
#else
		if( commonMsgWin( ML_STRING(189, "创建角色失败。") ) )
#endif
		{
			// ＯＫボタンが押された
			ChangeProc( PROC_CHAR_SELECT, 10 );
			//return;
		}
//		selHomeTown();	// 画面作るため
	}
}











// キャラ画像选择处理
//
//  戾り值：	0 ... 处理中
//				1 ... 决定
//				2 ... キャンセル

// ＯＫボタン情报
GRA_BTN_INFO2 okBtn_Ex =
{// 表示座标(x,y)  // 范围始点座标(x,y)  // 范围幅(w,h)
	320,240,/*164, 441,*/     120, 415,              88, 52,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_OK_BTN_2_EX
};
// ＢＡＣＫボタン情报
GRA_BTN_INFO2 backBtn_Ex =
{// 表示座标(x,y)  // 范围始点座标(x,y)  // 范围幅(w,h)
 320,240,/*458, 445,*/        386, 419,              144, 52,
 0, CG_CHR_MAKE_SEL_BACK_BTN_2_EX
};

// ＰＡＧＥ１ボタン情报
GRA_BTN_INFO2 page1Btn =
{// 表示座标(x,y)  // 范围始点座标(x,y)  // 范围幅(w,h)
	320,240,/*164, 441,*/     256, 67,              108, 42,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_PAGE1_BTN
};
// ＰＡＧＥ２ボタン情报
GRA_BTN_INFO2 page2Btn =
{// 表示座标(x,y)  // 范围始点座标(x,y)  // 范围幅(w,h)
 320,240,/*458, 445,*/        256, 109,              108, 42,
 0, CG_CHR_MAKE_SEL_PAGE2_BTN
};

// ＯＫボタン情报
GRA_BTN_INFO2 SelGra_okBtn =
{// 表示座标(x,y)
	164, 441,
 // 范围始点座标(x,y)
	120, 415,
 // 范围幅(w,h)
	88, 52,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_OK_BTN_2
};
// ＢＡＣＫボタン情报
GRA_BTN_INFO2 SelGra_backBtn =
{// 表示座标(x,y)
	458, 445,
 // 范围始点座标(x,y)
	386, 419,
 // 范围幅(w,h)
	144, 52,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_BACK_BTN_2
};
// ＲＯＴＡＴＥ左ボタン情报
GRA_BTN_INFO2 SelGra_rotateLeftBtn =

{// 表示座标(x,y)
	 95,  72,
 // 范围始点座标(x,y)
	 79,  58,
 // 范围幅(w,h)
	 32,  28,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_ROTATE_LEFT_BTN_2
};
// ＲＯＴＡＴＥ右ボタン情报
GRA_BTN_INFO2 SelGra_rotateRightBtn =
{// 表示座标(x,y)
	200,  72,
 // 范围始点座标(x,y)
	184,  58,
 // 范围幅(w,h)
	 32,  28,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_ROTATE_RIGHT_BTN_2
};
// ＣＯＬＯＲ左ボタン情报
GRA_BTN_INFO2 SelGra_colorLeftBtn =
{// 表示座标(x,y)
	 95, 112,
 // 范围始点座标(x,y)
	 79,  98,
 // 范围幅(w,h)
	 32,  28,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_COLOR_LEFT_BTN_2
};
// ＣＯＬＯＲ右ボタン情报
GRA_BTN_INFO2 SelGra_colorRightBtn =
{// 表示座标(x,y)
	200, 112,
 // 范围始点座标(x,y)
	184,  98,
 // 范围幅(w,h)
	 32,  28,
 // ボタン画像番号(凸画像,凹画像)
	0, CG_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2
};


#ifndef USE_VERSION2_FRONTEND	// バージョン２のフロントエンドを使わないなら

// キャラ画像选择の初期化
void initSelCharGraNo( void )
{
	int i;
	SELECTGRANO *pSelGraNoTbl;

	if( 0 <= nowSelCharNo && nowSelCharNo < MaxSelectChar ){
	}else{
		nowSelCharNo = 2;	// 变な值なら初期化
	}

	// キャラ画像选择の初期化
	nowSelCharMouthNo = 0;
	nowSelCharEyeNo = 0;
	nowSelCharColor = 0;

	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTbl[giCreatePageNo];

	// アクション初期化
	DeathAllAction();
	for( i = 0; i < MaxSelectChar; i++ ){
		ptActSelChar[i] = NULL;
	}

	for( i = 0; i < pSelGraNoTbl->size; i++ )
	{
		// キャラのアクション作成
		if( ptActSelChar[i] == NULL )
		{
			ptActSelChar[i] = GetAction( PRIO_CHR, 0 );
			if( ptActSelChar[i] != NULL )
			{
				ptActSelChar[i]->anim_chr_no = pSelGraNoTbl->pSelectGraNoTbl[i];
				ptActSelChar[i]->anim_no = ANIM_STAND;
				ptActSelChar[i]->dispPrio = DISP_PRIO_CHAR;
				ptActSelChar[i]->x = pSelGraNoTbl->pSelectGraNoPos[i].x;
				ptActSelChar[i]->y = pSelGraNoTbl->pSelectGraNoPos[i].y;
				ptActSelChar[i]->anim_ang = 5;
				pattern( ptActSelChar[i], ANM_NOMAL_SPD, ANM_LOOP );
			}
		}
	}
}

// キャラ画像选择の初期化
// （前の处理から戾ってきたときの初期化。パラメータを残しておくため）
void initSelCharGraNo2( void )
{
	int i;
	SELECTGRANO *pSelGraNoTbl;
	if( 0 <= nowSelCharNo && nowSelCharNo < MaxSelectChar ){
	}else{
		nowSelCharNo = 2;	// 变な值なら初期化
	}
	// キャラ画像选择の初期化
	nowSelCharMouthNo = 0;
	nowSelCharEyeNo = 0;
	nowSelCharColor = 0;

	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTbl[giCreatePageNo];

	for( i = 0; i < pSelGraNoTbl->size; i++ )
	{
		// キャラのアクション作成
		if( ptActSelChar[i] == NULL )
		{
			ptActSelChar[i] = GetAction( PRIO_CHR, 0 );
			if( ptActSelChar[i] != NULL )
			{
				ptActSelChar[i]->anim_chr_no = pSelGraNoTbl->pSelectGraNoTbl[i];
				ptActSelChar[i]->anim_no = ANIM_STAND;
				ptActSelChar[i]->dispPrio = DISP_PRIO_CHAR;
				ptActSelChar[i]->x = pSelGraNoTbl->pSelectGraNoPos[i].x;
				ptActSelChar[i]->y = pSelGraNoTbl->pSelectGraNoPos[i].y;
				ptActSelChar[i]->anim_ang = 5;
				pattern( ptActSelChar[i], ANM_NOMAL_SPD, ANM_LOOP );
			}
		}
	}
}

int selCharGraNo( void )
{
	int i;
	int ret = 0;
	int focusNo = -1;
	SELECTGRANO *pSelGraNoTbl;
	GRA_BTN_INFO2 *pOkBtn = &SelGra_okBtn, *pBackBtn = &SelGra_backBtn,
		*pRotateRightBtn = &SelGra_rotateRightBtn,
		*pRotateLeftBtn = &SelGra_rotateLeftBtn,
		*pColorLeftBtn = &SelGra_colorLeftBtn,
		*pColorRightBtn = &SelGra_colorRightBtn;



#ifdef MULTI_GRABIN
	// 认证されたバージョンによりボタンを变化
	if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
	}else
	if( PackageVer >= PV_FIRST_EX ){	// ＥＸ
		pOkBtn = &okBtn_Ex;
		pBackBtn = &backBtn_Ex;
	}
	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTbl[giCreatePageNo];
#else
	giCreatePageNo = 0;
	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTbl[giCreatePageNo];
#endif

	GetKeyInputFocus( NULL );

	// 选择颜画像にカーソルが合っているか判定
	for( i = 0; i < pSelGraNoTbl->size; i++ )
	{
		if( MakeHitBox(
				pSelGraNoTbl->pSelectGraNoPos[i].x-30,
				pSelGraNoTbl->pSelectGraNoPos[i].y-92,
				pSelGraNoTbl->pSelectGraNoPos[i].x-30+60,
				pSelGraNoTbl->pSelectGraNoPos[i].y-92+106, -1 ) )
		{
			focusNo = i;
			break;
		}
	}

	// キャラが选择されたか判定
	if( focusNo >= 0 && (mouse.autoState & MOUSE_LEFT_CRICK) )
	{
		if( nowSelCharNo != focusNo )
		{
			ptActSelChar[nowSelCharNo]->anim_no = ANIM_STAND;
			ptActSelChar[nowSelCharNo]->anim_ang = 5;
			// 色情报も初期化 // ohta
			nowSelCharColor = 0;
			ptActSelChar[nowSelCharNo]->anim_chr_no =
				pSelGraNoTbl->pSelectGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
			pattern( ptActSelChar[nowSelCharNo], ANM_NOMAL_SPD, ANM_LOOP );
		}
		nowSelCharNo = focusNo;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}
	else
	// ＯＫボタンを押したか判定
	if( (pushGraBtnInfo2( pOkBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 1;
		// 决定ａ
		play_se( SE_NO_OK, 320, 240 );
		// 姿と颜グラのデフォルトを决定してエディット画面へ持っていく
		selCharFigureNo = ptActSelChar[nowSelCharNo]->anim_chr_no;
		// 颜の决定
		selCharFaceNo = pSelGraNoTbl->pSelectFaceGraNoTbl[nowSelCharNo];
		if( giCreatePageNo == 0 ){
			selCharNew_Pattern = 0;	// 旧キャラだったら
		}else{
			selCharNew_Pattern = 1;	// 新キャラだったら
		}
	}
	else
	// ＢＡＣＫボタンを押したか判定
	if( (pushGraBtnInfo2( pBackBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 2;
		// 返回音
		play_se( SE_NO_BACK, 320, 240 );
	}
	else
	// ＲＯＴＡＴＥ左ボタンを押したか判定
	if( (pushGraBtnInfo2( pRotateLeftBtn ) & BTN_LEFT_CLICK_REP) )
	{
		ptActSelChar[nowSelCharNo]->anim_ang++;
		if( ptActSelChar[nowSelCharNo]->anim_ang >= 8 )
			ptActSelChar[nowSelCharNo]->anim_ang = 0;
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}
	else
	// ＲＯＴＡＴＥ右ボタンを押したか判定
	if( (pushGraBtnInfo2( pRotateRightBtn ) & BTN_LEFT_CLICK_REP) )
	{
		ptActSelChar[nowSelCharNo]->anim_ang--;
		if( ptActSelChar[nowSelCharNo]->anim_ang < 0 )
			ptActSelChar[nowSelCharNo]->anim_ang = 7;
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}
	else
	// ＣＯＬＯＲ左ボタンを押したか判定
	if( (pushGraBtnInfo2( pColorLeftBtn ) & BTN_LEFT_CLICK_REP) )
	{
		nowSelCharColor--;
		if( nowSelCharColor < 0 )
			nowSelCharColor = 3;
		if( ptActSelChar != NULL )
		{
			ptActSelChar[nowSelCharNo]->anim_chr_no =
				pSelGraNoTbl->pSelectGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
		}
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}
	else
	// ＣＯＬＯＲ右ボタンを押したか判定
	if( (pushGraBtnInfo2( pColorRightBtn ) & BTN_LEFT_CLICK_REP) )
	{
		nowSelCharColor++;
		if( nowSelCharColor >= 4 )
			nowSelCharColor = 0;
		if( ptActSelChar != NULL )
		{
			ptActSelChar[nowSelCharNo]->anim_chr_no =
				pSelGraNoTbl->pSelectGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
		}
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}

	// キャラのアクション消す
	if( ret != 0 )
	{
		for( i = 0; i < MaxSelectChar; i++ )
		{
			if( ptActSelChar[i] != NULL )
			{
				DeathAction( ptActSelChar[i] );
				ptActSelChar[i] = NULL;
			}
		}
	}

#ifdef MULTI_GRABIN
	// もしも次のページボタンを押されていたら
	if( PackageVer >= PV_FIRST_EX ){
		// 现在通常ＯＲリニューアルページ目なら
		if( giCreatePageNo == GRANO_NORMAL
		||  giCreatePageNo == GRANO_RENEW
		){
			// １ページ目のボタンを表示
			drawGraBtnInfo2( &page1Btn, DISP_PRIO_TILE, 0, 0, 2 );
			// 次のページのボタンを押されたら？
			if( pushGraBtnInfo2( &page2Btn ) & BTN_LEFT_CLICK ){
				if( PackageVer >= PV_FIRST_EX ){// ＥＸだったら
					giCreatePageNo = GRANO_EX; // ＥＸページへ
				}else
				if( PackageVer >= PV_FIRST_VER2 ){// Ｖ２だったら
					giCreatePageNo = GRANO_V2; // Ｖ２ページへ
				}
				SubProcNo = 1;	// ここで初期化へ返回。
			}
		}else
		// 现在新キャラページなら
		if( giCreatePageNo == GRANO_EX
		||  giCreatePageNo == GRANO_V2
		){
			// ２ページ目のボタンを表示
			drawGraBtnInfo2( &page2Btn, DISP_PRIO_TILE, 0, 0, 2 );
			// １ページのボタンを押されたら？
			if( pushGraBtnInfo2( &page1Btn ) & BTN_LEFT_CLICK ){
				if( giCreateRenewalFlg ){
					// リニューアルフラグ立ってるならリニューアルに。
					giCreatePageNo = GRANO_RENEW;
				}else{
					giCreatePageNo = GRANO_NORMAL;
				}
				SubProcNo = 1;	// ここで初期化へ返回。
			}
		}


		if( ( PackageVer == PV_FIRST_EX || PackageVer == PV_UP_EX )	// Ｖ２以上なら新旧切换ボタンを押す
		&&  ( giCreatePageNo == GRANO_NORMAL || giCreatePageNo == GRANO_RENEW )
		){
			if(	T_TextButton( 400, 120, 400+106, 120+30, ML_STRING(289, "新旧切换") ) == 1 ){
				SubProcNo = 1;	// ここで初期化へ返回。
				// 切り替え
				giCreatePageNo = (giCreatePageNo == GRANO_NORMAL )?(GRANO_RENEW):(GRANO_NORMAL );
				giCreateRenewalFlg ^= 1;
			}
		}

	}
#endif

	// ＲＯＴＡＴＥ左ボタン表示
	drawGraBtnInfo2( pRotateLeftBtn, DISP_PRIO_TILE, 0, 0, 0 );
	// ＲＯＴＡＴＥ右ボタン表示
	drawGraBtnInfo2( pRotateRightBtn, DISP_PRIO_TILE, 0, 0, 0 );

	// ＣＯＬＯＲ左ボタン表示
	drawGraBtnInfo2( pColorLeftBtn, DISP_PRIO_TILE, 0, 0, 0 );
	// ＣＯＬＯＲ右ボタン表示
	drawGraBtnInfo2( pColorRightBtn, DISP_PRIO_TILE, 0, 0, 0 );

	// ＯＫボタン表示
	drawGraBtnInfo2( pOkBtn, DISP_PRIO_TILE, 0, 0, 0 );

	// ＢＡＣＫボタン表示
	drawGraBtnInfo2( pBackBtn, DISP_PRIO_TILE, 0, 0, 0 );

	// キャラの表示
	if( ptActSelChar[nowSelCharNo] )
	{
		ptActSelChar[nowSelCharNo]->anim_no = ANIM_WALK;
		pattern( ptActSelChar[nowSelCharNo], ANM_NOMAL_SPD, ANM_LOOP );
	}

	// 选择されてるキャラに枠を表示
	StockBoxDispBuffer(
		pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].x-30,
		pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].y-92,
		pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].x-30+60,
		pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].y-92+106,
		DISP_PRIO_TILE, SYSTEM_PAL_RED, 0 );

	// カーソル位置に枠を表示
	if( focusNo >= 0 )
	{
		StockBoxDispBuffer(
			pSelGraNoTbl->pSelectGraNoPos[focusNo].x-30,
			pSelGraNoTbl->pSelectGraNoPos[focusNo].y-92,
			pSelGraNoTbl->pSelectGraNoPos[focusNo].x-30+60,
			pSelGraNoTbl->pSelectGraNoPos[focusNo].y-92+106,
			DISP_PRIO_TILE, BoxColor, 0 );
	}

#ifdef MULTI_GRABIN
	// 认证されたバージョンにより背景を变化
#ifdef PUK2
	if( PackageVer >= PV_PUK2 ){	// PUK2
		 StockDispBuffer( 320, 240, DISP_PRIO_BG, PUK2_CHARAMAKE, 0 );
	}else
#endif
	if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
		 StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_V2, 0 );
	}else
	if( PackageVer >= PV_FIRST_EX ){	// ＥＸ
		if( giCreatePageNo == GRANO_EX ){ // ＥＸページには别の絵
//			StockDispBuffer( 320, 240, DISP_PRIO_BG+1, CG_CHR_MAKE_SEL_WINDOW_EX_B, 0 );
		}else{
		}
		StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_EX, 0 );
	}else{
		StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW, 0 );
	}
#else

	StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW, 0 );
#endif

	return ret;
}




#else		//  USE_VERSION2_FRONTEND



#define S_G_F_XSIZE 70
#define S_G_F_XPOS	(75+S_G_F_XSIZE/2)

#define S_G_F_YSIZE 78
#define S_G_F_YPOS	(224+S_G_F_YSIZE/2)


#ifdef PUK2

// 旧キャラの颜アップ画像
int selectFaceUpNoTbl_PUK2[] =	// 颜のアップ番号
{
	PUK2_FACE_UP_15,PUK2_FACE_UP_16,PUK2_FACE_UP_17,PUK2_FACE_UP_18,PUK2_FACE_UP_19,PUK2_FACE_UP_20,PUK2_FACE_UP_21,
	PUK2_FACE_UP_22,PUK2_FACE_UP_23,PUK2_FACE_UP_24,PUK2_FACE_UP_25,PUK2_FACE_UP_26,PUK2_FACE_UP_27,PUK2_FACE_UP_28,
	PUK2_FACE_UP_01,PUK2_FACE_UP_02,PUK2_FACE_UP_03,PUK2_FACE_UP_04,PUK2_FACE_UP_05,PUK2_FACE_UP_06,PUK2_FACE_UP_07,
	PUK2_FACE_UP_08,PUK2_FACE_UP_09,PUK2_FACE_UP_10,PUK2_FACE_UP_11,PUK2_FACE_UP_12,PUK2_FACE_UP_13,PUK2_FACE_UP_14,
};

#endif

// 旧キャラの颜アップ画像
int selectFaceUpNoTbl[] =	// 颜のアップ番号
{
	CG_FACE_UP_0,CG_FACE_UP_1,CG_FACE_UP_2,CG_FACE_UP_3,CG_FACE_UP_4,CG_FACE_UP_5,CG_FACE_UP_6,
	CG_FACE_UP_7,CG_FACE_UP_8,CG_FACE_UP_9,CG_FACE_UP_10,CG_FACE_UP_11,CG_FACE_UP_12,CG_FACE_UP_13,
};

// 颜のアップの画像
int selectFaceUpNoTblV2[] = {
	V2_FACE_UP_0,V2_FACE_UP_1,V2_FACE_UP_2,V2_FACE_UP_3,V2_FACE_UP_4,V2_FACE_UP_5,V2_FACE_UP_6,
	V2_FACE_UP_7,V2_FACE_UP_8,V2_FACE_UP_9,V2_FACE_UP_10,V2_FACE_UP_11,V2_FACE_UP_12,V2_FACE_UP_13,
};

int *aiGraUpTbl[] =	// 颜の番号
{
	selectFaceUpNoTblV2,
	selectFaceUpNoTbl,
#ifdef PUK2
	selectFaceUpNoTbl_PUK2
#endif
};

// Ｖ２专用
SELGRAPOS aSelGraPosV2[] = {	// 表示位置
	{ S_G_F_XPOS+S_G_F_XSIZE*0, S_G_F_YPOS },	//  0 セディ
	{ S_G_F_XPOS+S_G_F_XSIZE*1, S_G_F_YPOS },	//  1 ピート
	{ S_G_F_XPOS+S_G_F_XSIZE*2, S_G_F_YPOS },	//  2 サイゾウ
	{ S_G_F_XPOS+S_G_F_XSIZE*3, S_G_F_YPOS },	//  3 ネルソン
	{ S_G_F_XPOS+S_G_F_XSIZE*4, S_G_F_YPOS },	//  4 ペティット
	{ S_G_F_XPOS+S_G_F_XSIZE*5, S_G_F_YPOS },	//  5 ランスロット
	{ S_G_F_XPOS+S_G_F_XSIZE*6, S_G_F_YPOS },	//  6 ウィスケルス

	{ S_G_F_XPOS+S_G_F_XSIZE*0, S_G_F_YPOS+S_G_F_YSIZE },	//  0 セディ
	{ S_G_F_XPOS+S_G_F_XSIZE*1, S_G_F_YPOS+S_G_F_YSIZE },	//  1 ピート
	{ S_G_F_XPOS+S_G_F_XSIZE*2, S_G_F_YPOS+S_G_F_YSIZE },	//  2 サイゾウ
	{ S_G_F_XPOS+S_G_F_XSIZE*3, S_G_F_YPOS+S_G_F_YSIZE },	//  3 ネルソン
	{ S_G_F_XPOS+S_G_F_XSIZE*4, S_G_F_YPOS+S_G_F_YSIZE },	//  4 ペティット
	{ S_G_F_XPOS+S_G_F_XSIZE*5, S_G_F_YPOS+S_G_F_YSIZE },	//  5 ランスロット
	{ S_G_F_XPOS+S_G_F_XSIZE*6, S_G_F_YPOS+S_G_F_YSIZE },	//  6 ウィスケルス
};


typedef struct {
	int *pSelectGraNoTbl;
	int *pSelectSubGraNoTbl;
	int *pSelectFaceGraNoTbl;
	SELGRAPOS *pSelectGraNoPos;
	int size;
}SELECTGRANO2;


SELECTGRANO2 aSelGraNoTblV2[] = {
	// ０番 Ｖ２のキャラに使用する。
	{ 	(int *)V2_selectNewCharaTbl,
	 	NULL,
		(int *)V2_selectNewFaceTbl,
		(SELGRAPOS *)aSelGraPosV2,
		arraysizeof(V2_selectNewCharaTbl) },

	// １番 Ｖ２のリニューアルに使用する。
	{ 	(int *)V2_selectRemakeCharTbl,
	 	(int *)selectGraNoOldTbl,
		(int *)V2_selectRemakeFaceTbl,
		(SELGRAPOS *)aSelGraPosV2,
		arraysizeof(V2_selectRemakeCharTbl) },

	// ２番 通常(OLD)、ＥＸ、Ｖ２のどれにも使用する。
	{ (int *)selectGraNoOldTbl,
		NULL,
		(int *)V2_selectFaceOldTbl,
		(SELGRAPOS *)aSelGraPosEx,
		arraysizeof(selectGraNoOldTbl) },

	// ３番 ＥＸのリニューアルに使用する。
	{ (int *)V2_selectRemakeCharTbl,
		NULL,
		(int *)V2_selectRemakeFaceTbl,
		(SELGRAPOS *)aSelGraPosV2,
		arraysizeof(V2_selectRemakeCharTbl)
	},

	// ４番  ＥＸ新キャラ时に使用するページ
	{ (int *)V2_selectNewCharaTbl,
	  NULL,
	  (int *)V2_selectNewFaceTbl,
		(SELGRAPOS *)aSelGraPosV2,
		arraysizeof(V2_selectNewCharaTbl)
	},

#ifdef PUK2
	// ５番 Ｖ２のリニューアルに使用する。
	{ (int *)V2_selectNewCharaTbl,
	  NULL,
	  (int *)PUK2_selectNewFaceTbl,
		(SELGRAPOS *)aSelGraPosV2,
		arraysizeof(V2_selectNewCharaTbl)
	},

	// ６番 Ｖ２のキャラに使用する。
	{ (int *)V2_selectRemakeCharTbl,
	  (int *)selectGraNoOldTbl,
	  (int *)PUK2_selectOldFaceTbl,
		(SELGRAPOS *)aSelGraPosV2,
		arraysizeof(V2_selectRemakeCharTbl)
	},
#endif

#ifdef TW_CHARA
	//台湾版选择人物界面中使用的新头像和新动画
	{ (int*)TW_selectCharTbl,
	  NULL,
	  (int*)TW_selectFaceTbl,
		(SELGRAPOS*)aSelGraPosV2,
		arraysizeof(TW_selectCharTbl)
	},
#endif

};



ACTION *ptActFaceUp = NULL;	// 画面アップのアクション
ACTION *ptActFigure[2] = { NULL, NULL };	// アクションするキャラのアクション

#define FACEUP_XPOS -100
#define FACEUP_YPOS 130

#define FACEUP_XPOS_END 200
#define FACEUP_FIRST_SPEED	(16)
#define FACEUP_DOWN_SPEED	(0.3)

#define FIGURE_XPOS 459		// アニメーションさせるときのキャラの表示位置
#define FIGURE_YPOS 163
#define FIGURE_X_SHIFT 70	// ２キャラ表示させるときにどれくらい寄せるか。
/*
enum{
	V2_GRANO_NEW,		// 新キャラ选择
	V2_GRANO_NORMAL,	// 旧キャラ选择
};
*/
typedef struct {
	int actNo;	// 行动
	int times;	// 回数
	int wepon;	// 武器の种类
}CHARSELACTSHELL;
CHARSELACTSHELL CharSelActTbl[] = {
	{ ANIM_WALK, 2, 0 }, 		// 歩き
	{ ANIM_B_WALK, 2, 0 }, // バトル歩き
	{ ANIM_ATTACK, 1, -2 },	// 攻击
	{ ANIM_ATTACK, 1, -1 },	// 攻击
	{ ANIM_ATTACK, 1, 0 },	// 攻击
	{ ANIM_ATTACK, 1, 1 },	// 攻击
	{ ANIM_ATTACK, 1, 2 },	// 攻击
	{ ANIM_ATTACK, 1, 3 },	// 攻击
	{ ANIM_MAGIC, 1, 0 }, // 魔法 magic ６
	{ ANIM_THROW, 1, 0  }, // 投掷 throw ７
	{ ANIM_DAMAGE, 1, 0  }, // 受伤 dmg ８
	{ ANIM_GUARD, 1, 0  }, // 防御 guard ９
	{ ANIM_DEAD, 1, 0  }, // 気絶 die １０
	{ ANIM_SIT, 1, 0  }, // 座る sit １１
	{ ANIM_HAND, 1, 0  }, // 挥手 bye １２
	{ ANIM_HAPPY, 1, 0 }, // 喜ぶ yoro １３

	{ ANIM_STAND, 60 } // 立つ
};

//----------------------------------------------------------------
// 次の行动をセットする。
//----------------------------------------------------------------
void SetCharSelNextAction( ACTION *pAction ){

	// 现在の行动をチェック
	if( pAction->actNo < 0 ){
		// 数が少なかったら０から
		pAction->actNo = 0;
	}else
	if( pAction->actNo >= arraysizeof( CharSelActTbl ) - 1){
		// 数がオーバーしてたら０から
		pAction->actNo = 0;
	}
	pAction->level++;	// 行动の回数を取得
	if( pAction->level < CharSelActTbl[pAction->actNo].times ){
		// まだ同じ行动する场合はそのまま
		pAction->anim_no_bak = -1;
		return;
	}

	// 行动回数超えたら次の行动へ
	pAction->level = 0;
	pAction->actNo++;
	if( pAction->actNo >= arraysizeof( CharSelActTbl ) - 1){
		// 数がオーバーしてたら０から
		pAction->actNo = 0;
	}
	// 行动に武器が入っていたらキャラ番号变更
	pAction->anim_chr_no = pAction->hp;
	if( CharSelActTbl[pAction->actNo].wepon ){
		pAction->anim_chr_no += CharSelActTbl[pAction->actNo].wepon;
	}
	// 次の行动セット
	pAction->anim_no = CharSelActTbl[pAction->actNo].actNo;
	pAction->anim_no_bak = -1;

}

//----------------------------------------------------------------
// キャラ选择の时に、选择されたキャラのアニメーションをセット
//----------------------------------------------------------------
void SetCharSelColor( int nowSelCharNo, int nowSelCharColor ){
	SELECTGRANO2 *pSelGraNoTbl;
	pSelGraNoTbl = &aSelGraNoTblV2[giCreatePageNo];

	if( ptActSelChar[nowSelCharNo] != NULL ){
		ptActSelChar[nowSelCharNo]->anim_chr_no =
			pSelGraNoTbl->pSelectGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
		// エディット画面に持っていく姿の番号
		selCharFigureNo = ptActSelChar[nowSelCharNo]->anim_chr_no;
		// エディット画面に持っていく姿の番号
		if( pSelGraNoTbl->pSelectSubGraNoTbl != NULL ){
			selCharSubFigureNo = pSelGraNoTbl->pSelectSubGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
		}else{
			selCharSubFigureNo = 0;
		}
		// エディット画面に持っていく颜の元番号
		selCharFaceNo = pSelGraNoTbl->pSelectFaceGraNoTbl[nowSelCharNo];// + nowSelCharColor*1000;
	}
}

void V2_SetCharSelColor( void ){
	int i;
	SELECTGRANO2 *pSelGraNoTbl;
	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTblV2[giCreatePageNo];

	for( i = 0; i < 2; i ++ ){
		if( i == 0 ){	//
			ptActFigure[i]->anim_chr_no = pSelGraNoTbl->pSelectGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
		}else{
			if( pSelGraNoTbl->pSelectSubGraNoTbl != NULL ){
				ptActFigure[i]->anim_chr_no = pSelGraNoTbl->pSelectSubGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
				ptActFigure[i]->atr &= ~ACT_ATR_HIDE;	// 表示
			}else{
				ptActFigure[i]->anim_chr_no = 0;
				ptActFigure[i]->atr |= ACT_ATR_HIDE;	// 非表示
			}
		}
		// キャラの番号バックアップ
		ptActFigure[i]->hp = ptActFigure[i]->anim_chr_no;
	}
	// エディット画面に持っていく姿の番号
	selCharFigureNo = ptActFigure[0]->anim_chr_no;
	// エディット画面に持っていく颜の元番号
	selCharFaceNo = pSelGraNoTbl->pSelectFaceGraNoTbl[nowSelCharNo];// + nowSelCharColor*6;

	// エディット画面に持っていく姿の番号
	if( pSelGraNoTbl->pSelectSubGraNoTbl != NULL ){
		selCharSubFigureNo = pSelGraNoTbl->pSelectSubGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
	}else{
		selCharSubFigureNo = 0;
	}

	selCharNew_Pattern = 1;	// 新キャラなので１


}
void SetCharSelAnimation( void ){
	int i;
	SELECTGRANO2 *pSelGraNoTbl;
	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTblV2[giCreatePageNo];
	// 色设定
	V2_SetCharSelColor( );

	for( i = 0; i < 2; i ++ ){
		ptActFigure[i]->actNo = 0;	// 行动は０から。
		ptActFigure[i]->level = 1;	// 回数は１から
		pattern( ptActFigure[i], ANM_NOMAL_SPD, ANM_NO_LOOP );
	}

	// ２キャラ表示时の位置を设定
	if( pSelGraNoTbl->pSelectSubGraNoTbl != NULL ){
		ptActFigure[0]->x = FIGURE_XPOS - 0;
		ptActFigure[0]->y = FIGURE_YPOS;
		ptActFigure[1]->x = FIGURE_XPOS + FIGURE_X_SHIFT;
		ptActFigure[1]->y = FIGURE_YPOS;
	}else{
		ptActFigure[0]->x = FIGURE_XPOS;
		ptActFigure[0]->y = FIGURE_YPOS;
	}

}

// キャラ画像选择の初期化
void V2_initSelCharGraNo_Body( void )
{
	int i;
	SELECTGRANO2 *pSelGraNoTbl;

#ifdef PUK2
	if( PackageVer >= PV_PUK2 ){
		if( giCreatePageNo == PUK2_GRANO_NEW
		|| giCreatePageNo == PUK2_GRANO_OLD
		){
		}else{
			// 新キャラをデフォルト
			giCreatePageNo = PUK2_GRANO_NEW;
		}
	}else
#endif
	if( PackageVer >= PV_FIRST_VER2 ){
		if( giCreatePageNo == V2_GRANO_NEW
		|| giCreatePageNo == V2_GRANO_NORMAL
		){
		}else{
			// 新キャラをデフォルト
			giCreatePageNo = V2_GRANO_NEW;
		}
	}else
	if( PackageVer >= PV_FIRST_EX ){
		if( giCreatePageNo == V2_GRANO_EX_RENEW
		|| giCreatePageNo == V2_GRANO_EX
		|| giCreatePageNo == V2_GRANO_OLD
		){
		}else{
			// 旧キャラをデフォルト
			giCreatePageNo = V2_GRANO_OLD;
		}
	}else{
		// 旧キャラのみ
		giCreatePageNo = V2_GRANO_OLD;
	}

	// キャラ画像选择の初期化
	if( 0 <= nowSelCharNo && nowSelCharNo < MaxSelectChar ){
	}else{
		nowSelCharNo = 0;	// 变な值なら初期化
	}
	nowSelCharMouthNo = 0;
	nowSelCharEyeNo = 0;
	nowSelCharColor = 0;

	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTblV2[giCreatePageNo];


	// 选择するキャラの数ループ
	for( i = 0; i < pSelGraNoTbl->size; i++ ){
		// キャラのアクション作成
		ptActSelChar[i] = GetAction( PRIO_CHR, 0 );
		if( ptActSelChar[i] == NULL )continue;

		if( PackageVer >= PV_FIRST_VER2 ){
			// バージョン２の场合は颜を表示
			ptActSelChar[i]->anim_chr_no = pSelGraNoTbl->pSelectFaceGraNoTbl[i];
			if( nowSelCharNo == i ){
			}else{
				// 选择されたもの意外は非表示
				ptActSelChar[i]->atr |= ACT_ATR_HIDE;	// 非表示
			}
		}else{
			ptActSelChar[i]->anim_chr_no = pSelGraNoTbl->pSelectGraNoTbl[i];
		}
		ptActSelChar[i]->anim_no = ANIM_STAND;
		ptActSelChar[i]->dispPrio = DISP_PRIO_CHAR;
		ptActSelChar[i]->x = pSelGraNoTbl->pSelectGraNoPos[i].x;
		ptActSelChar[i]->y = pSelGraNoTbl->pSelectGraNoPos[i].y;
		ptActSelChar[i]->anim_ang = 5;
		pattern( ptActSelChar[i], ANM_NOMAL_SPD, ANM_LOOP );
	}

	if( PackageVer >= PV_FIRST_VER2 ){
		// 颜アップのアクション
		ptActFaceUp = GetAction( PRIO_CHR, 0 );
		if( ptActFaceUp != NULL ){
			ptActFaceUp->anim_no = ANIM_STAND;
			ptActFaceUp->dispPrio = DISP_PRIO_CHAR-3;
			ptActFaceUp->x = FACEUP_XPOS;
			ptActFaceUp->y = FACEUP_YPOS;
			ptActFaceUp->fx = (int)FACEUP_XPOS;
			ptActFaceUp->fy = (int)FACEUP_YPOS;
			ptActFaceUp->dfx = FACEUP_FIRST_SPEED;
			ptActFaceUp->anim_ang = 5;
			// 初期选择
#ifdef PUK2
			if ( PackageVer >= PV_PUK2 ){
				ptActFaceUp->anim_chr_no = aiGraUpTbl[2][nowSelCharNo+(giCreatePageNo==PUK2_GRANO_NEW?14:0)];
			}else{
				ptActFaceUp->anim_chr_no = aiGraUpTbl[giCreatePageNo][nowSelCharNo];
			}
#else
			ptActFaceUp->anim_chr_no = aiGraUpTbl[giCreatePageNo][nowSelCharNo];
#endif
			pattern( ptActFaceUp, ANM_NOMAL_SPD, ANM_LOOP );
		}

		for( i = 0; i < 2; i ++ ){
			// アクションするキャラのアクション
			ptActFigure[i] = GetAction( PRIO_CHR, 0 );
			if( ptActFigure[i] != NULL ){
				ptActFigure[i]->dispPrio = DISP_PRIO_CHAR;
				ptActFigure[i]->anim_ang = 6;
				ptActFigure[i]->anim_no = CharSelActTbl[0].actNo;
				ptActFigure[i]->anim_no_bak = -1;

			}
		}
		// アニメーションと表示位置を决定
		SetCharSelAnimation( );
	}else{
		// 昔のバージョンの场合はこちら。
		SetCharSelColor( nowSelCharNo, nowSelCharColor );
	}

}
void V2_initSelCharGraNo( void ){
	int i;
	// アクション初期化
	DeathAllAction();
	for( i = 0; i < MaxSelectChar; i++ ){
		ptActSelChar[i] = NULL;
	}
	V2_initSelCharGraNo_Body( );
}

// キャラ画像选择の初期化
// （前の处理から戾ってきたときの初期化。パラメータを残しておくため）
void V2_initSelCharGraNo2( void ){
	int i;
	for( i = 0; i < MaxSelectChar; i++ ){
		ptActSelChar[i] = NULL;
	}
	V2_initSelCharGraNo_Body( );
}


//--------------------------------------------------
//  バージョン２用のキャラグラ选择画面
//--------------------------------------------------
GRA_BTN_INFO2 V2_SelGra_OkBtn =	{ // ＯＫボタン情报
	320, 240,		// 表示座标(x,y)
	370, 410,		// 范围始点座标(x,y)
	60, 35,			// 范围幅(w,h)
	0, V2_CHR_MAKE_SEL_OK // ボタン画像番号(凸画像,凹画像)
};

GRA_BTN_INFO2 V2_SelGra_backBtn =	{// ＢＡＣＫボタン情报
	320, 240,		// 表示座标(x,y)
	540, 40,		// 范围始点座标(x,y)
	74,  20,		// 范围幅(w,h)
	0, V2_CHR_MAKE_SEL_BACK		// ボタン画像番号(凸画像,凹画像)
};

GRA_BTN_INFO2 V2_SelGra_RotateLeftBtn =	{	// ＲＯＴＡＴＥ左ボタン情报
	 95,  72,		// 表示座标(x,y)
	 79,  58,		// 范围始点座标(x,y)
	 32,  28,		// 范围幅(w,h)
	0, CG_CHR_MAKE_SEL_ROTATE_LEFT_BTN_2		// ボタン画像番号(凸画像,凹画像)
};
// ＲＯＴＡＴＥ右ボタン情报
GRA_BTN_INFO2 V2_SelGra_RotateRightBtn = {
	414,  93,		// 表示座标(x,y)
	414,  93,		// 范围始点座标(x,y)
	 80,  75,		// 范围幅(w,h)
	0, 0		// ボタン画像番号(凸画像,凹画像)
};

// ＲＯＴＡＴＥ右ボタン情报
GRA_BTN_INFO2 V2_SelGra_RotateRightBtn_2 = {
	414,  93,		// 表示座标(x,y)
	414,  93,		// 范围始点座标(x,y)
	 150,  75,		// 范围幅(w,h)
	0, 0		// ボタン画像番号(凸画像,凹画像)
};

GRA_BTN_INFO2 V2_SelGra_ColorLeftBtn =	{	// ＣＯＬＯＲ左ボタン情报
	 417, 187,		// 表示座标(x,y)
	 410,  177,		// 范围始点座标(x,y)
	 16,  18,		// 范围幅(w,h)
	V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2		// ボタン画像番号(凸画像,凹画像)
};
GRA_BTN_INFO2 V2_SelGra_ColorRightBtn =	{	// ＣＯＬＯＲ右ボタン情报
	496, 187,		// 表示座标(x,y)
	488,  177,		// 范围始点座标(x,y)
	 16,  18,		// 范围幅(w,h)
	V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2		// ボタン画像番号(凸画像,凹画像)
};

GRA_BTN_INFO2 V2_SelGra_ChangeBtn =	{	// 变更ボタン
	320, 240,		// 表示座标(x,y)
	181,  410,		// 范围始点座标(x,y)
	110,  38,		// 范围幅(w,h)
	0, V2_CHR_MAKESEL_CHANGE		// ボタン画像番号(凸画像,凹画像)
};

int selCharGraNoV2( void )
{
	int i;
	int ret = 0;
	int focusNo = -1;
	SELECTGRANO2 *pSelGraNoTbl;
	GRA_BTN_INFO2 *pOkBtn = &SelGra_okBtn, *pBackBtn = &SelGra_backBtn,
		*pRotateRightBtn = &SelGra_rotateRightBtn,
		*pRotateLeftBtn = &SelGra_rotateLeftBtn,
		*pColorLeftBtn = &SelGra_colorLeftBtn,
		*pColorRightBtn = &SelGra_colorRightBtn;


	// 使用するテーブル
	pSelGraNoTbl = &aSelGraNoTblV2[giCreatePageNo];
	// 选择してる番号がオーバーしてたら０にする。
	if( nowSelCharNo >= pSelGraNoTbl->size || nowSelCharNo < 0 ){
		nowSelCharNo = 0;
	}

	// 认证されたバージョンによりボタンを变化
	if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
		pOkBtn = &V2_SelGra_OkBtn,
		pBackBtn = &V2_SelGra_backBtn,
		pRotateRightBtn = &V2_SelGra_RotateRightBtn,
		pRotateLeftBtn = &V2_SelGra_RotateLeftBtn,
		pColorLeftBtn = &V2_SelGra_ColorLeftBtn,
		pColorRightBtn = &V2_SelGra_ColorRightBtn;

		if( giCreatePageNo == V2_GRANO_NORMAL ){
			// ２人出てるときは回転のボタンを大きくする
			pRotateRightBtn = &V2_SelGra_RotateRightBtn_2;
		}
#ifdef PUK2
		if( giCreatePageNo == PUK2_GRANO_OLD ){
			// ２人出てるときは回転のボタンを大きくする
			pRotateRightBtn = &V2_SelGra_RotateRightBtn_2;
		}
#endif

	}else
	if( PackageVer >= PV_FIRST_EX ){	// ＥＸ
		pOkBtn = &okBtn_Ex;
		pBackBtn = &backBtn_Ex;
		// 昔のバージョンの场合はこちら。
		SetCharSelColor( nowSelCharNo, nowSelCharColor );
	}else{
		// 昔のバージョンの场合はこちら。
		SetCharSelColor( nowSelCharNo, nowSelCharColor );
	}

	GetKeyInputFocus( NULL );

	// 选择颜画像にカーソルが合っているか判定
	for( i = 0; i < pSelGraNoTbl->size; i++ ){
		int x1 = -30, x2 = -30+60, y1 = -92, y2 = -92+106;
		if( PackageVer >= PV_FIRST_VER2 ){
			x1 = -36, x2 = -36+72, y1 = -39, y2 = -39+78;
		}
		if( MakeHitBox(
				pSelGraNoTbl->pSelectGraNoPos[i].x+x1,
				pSelGraNoTbl->pSelectGraNoPos[i].y+y1,
				pSelGraNoTbl->pSelectGraNoPos[i].x+x2,
				pSelGraNoTbl->pSelectGraNoPos[i].y+y2, -1 ) )
		{
			focusNo = i;
			break;
		}
	}

#ifdef PUK2
	if( giCreatePageNo == PUK2_GRANO_OLD ){
		selCharNew_Pattern = 0;	// 旧キャラなので 0
	}else{
		selCharNew_Pattern = 1;	// 新キャラなので 0
	}
#endif
	if( giCreatePageNo == V2_GRANO_OLD ){
		selCharNew_Pattern = 0;	// 旧キャラなので 0
	}else{
		selCharNew_Pattern = 1;	// 新キャラなので 0
	}

	// キャラが选择されたか判定
	if( focusNo >= 0 && (mouse.autoState & MOUSE_LEFT_CRICK) ){
		if( nowSelCharNo != focusNo ){	// 违うのが选ばれた
			// 前回选ばれたものは初期化する。
			ptActSelChar[nowSelCharNo]->anim_no = ANIM_STAND;
			ptActSelChar[nowSelCharNo]->anim_ang = 5;
			// 色情报も初期化 // ohta
			nowSelCharColor = 0;
			if( PackageVer >= PV_FIRST_VER2 ){
				ptActSelChar[nowSelCharNo]->atr |= ACT_ATR_HIDE;	// 非表示
				// アップの位置を初期化
				ptActFaceUp->x = FACEUP_XPOS;
				ptActFaceUp->fx = FACEUP_XPOS;
				ptActFaceUp->dfx = FACEUP_FIRST_SPEED;
			}else{
				// 表示キャラを设定
				ptActSelChar[nowSelCharNo]->anim_chr_no =
					pSelGraNoTbl->pSelectGraNoTbl[nowSelCharNo] + nowSelCharColor*6;
				pattern( ptActSelChar[nowSelCharNo], ANM_NOMAL_SPD, ANM_LOOP );
			}
		}
		nowSelCharNo = focusNo;	// 选择した番号を决定

		if( PackageVer >= PV_FIRST_VER2 ){
			ptActSelChar[nowSelCharNo]->atr &= ~ACT_ATR_HIDE;	// 表示する
#ifdef PUK2
			if ( PackageVer >= PV_PUK2 ){
				ptActFaceUp->anim_chr_no = aiGraUpTbl[2][nowSelCharNo+(giCreatePageNo==PUK2_GRANO_NEW?14:0)];
			}else{
				ptActFaceUp->anim_chr_no = aiGraUpTbl[giCreatePageNo][nowSelCharNo];
			}
#else
			ptActFaceUp->anim_chr_no = aiGraUpTbl[giCreatePageNo][nowSelCharNo];
#endif
			pattern( ptActFaceUp, ANM_NOMAL_SPD, ANM_LOOP );
			// アニメーションと表示位置を决定
			SetCharSelAnimation( );
		}else{
			// 昔のバージョンの场合はこちら。
			SetCharSelColor( nowSelCharNo, nowSelCharColor );
		}
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}else
	// ＯＫボタンを押したか判定
	if( (pushGraBtnInfo2( pOkBtn ) & BTN_LEFT_CLICK) ){
		ret = 1;
		// 决定ａ
		play_se( SE_NO_OK, 320, 240 );
		// FACEUP アクション杀す
		DeathAction( ptActFaceUp ); ptActFaceUp = NULL;
		// Figure アクション杀す
		DeathAction( ptActFigure[0] );ptActFigure[0] = NULL;
		DeathAction( ptActFigure[1] );ptActFigure[1] = NULL;
	}else
	// ＢＡＣＫボタンを押したか判定
	if( (pushGraBtnInfo2( pBackBtn ) & BTN_LEFT_CLICK) ){
		ret = 2;
		// 返回音
		play_se( SE_NO_BACK, 320, 240 );
		// FACEUP アクション杀す
		DeathAction( ptActFaceUp ); ptActFaceUp = NULL;
		// Figure アクション杀す
		DeathAction( ptActFigure[0] );ptActFigure[0] = NULL;
		DeathAction( ptActFigure[1] );ptActFigure[1] = NULL;
	}else
	// ＲＯＴＡＴＥ左ボタンを押したか判定
	if( (pushGraBtnInfo2( pRotateLeftBtn ) & BTN_LEFT_CLICK_REP)
	// バージョン２の时は按鼠标右键もＯＫ
	|| ( PackageVer >= PV_FIRST_VER2 && pushGraBtnInfo2( pRotateRightBtn ) & BTN_RIGHT_CLICK_REP)
	){
		// バージョン２は回転するのはフィギュアキャラ
		if( PackageVer >= PV_FIRST_VER2 ){
			ptActFigure[0]->anim_ang++;// 角度变更
			ptActFigure[0]->anim_ang &= 7;
			ptActFigure[1]->anim_ang = ptActFigure[0]->anim_ang;
		}else{
			ptActSelChar[nowSelCharNo]->anim_ang++;	// 角度变更
			ptActSelChar[nowSelCharNo]->anim_ang &= 7;// ８方向固定
		}
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}else
	// ＲＯＴＡＴＥ右ボタンを押したか判定
	if( (pushGraBtnInfo2( pRotateRightBtn ) & BTN_LEFT_CLICK_REP) ){
		// バージョン２は回転するのはフィギュアキャラ
		if( PackageVer >= PV_FIRST_VER2 ){
			ptActFigure[0]->anim_ang--;// 角度变更
			ptActFigure[0]->anim_ang &= 7;
			ptActFigure[1]->anim_ang = ptActFigure[0]->anim_ang;
		}else{
			ptActSelChar[nowSelCharNo]->anim_ang--;	// 角度变更
			ptActSelChar[nowSelCharNo]->anim_ang &= 7;// ８方向固定
		}
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}else
	// ＣＯＬＯＲ左ボタンを押したか判定
	if( (pushGraBtnInfo2( pColorLeftBtn ) & BTN_LEFT_CLICK_REP) ){
		if( --nowSelCharColor < 0 )nowSelCharColor = 3;
		if( PackageVer >= PV_FIRST_VER2 ){
			V2_SetCharSelColor(  ); // 色变更
		}else{
			SetCharSelColor( nowSelCharNo, nowSelCharColor ); // 色变更
		}
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}else
	// ＣＯＬＯＲ右ボタンを押したか判定
	if( (pushGraBtnInfo2( pColorRightBtn ) & BTN_LEFT_CLICK_REP) ){
		if( ++nowSelCharColor >= 4 )nowSelCharColor = 0;
		if( PackageVer >= PV_FIRST_VER2 ){
			V2_SetCharSelColor(  );	// 色变更
		}else{
			SetCharSelColor( nowSelCharNo, nowSelCharColor ); // 色变更
		}
		play_se( SE_NO_CLICK, 320, 240 );	// クリック音
	}

	if( PackageVer >= PV_FIRST_VER2 ){
		if( ptActFaceUp != NULL ){	// 颜アップ画像处理
			if( ptActFaceUp->x < FACEUP_XPOS_END ){
				// 右に移动させる。
				ptActFaceUp->fx += ptActFaceUp->dfx;
				ptActFaceUp->x = (int)(ptActFaceUp->fx);
				// 一应减速？
				if( ptActFaceUp->dfx > 0.0 )ptActFaceUp->dfx -= (float)0.1;
			}else{
				ptActFaceUp->x = FACEUP_XPOS_END;	// 終点で止める
			}
		}
		// アニメーションキャラ处理
		for( i = 0; i < 2; i ++ ){
			if( ptActFigure[i] == NULL )continue;
			// アニメーションして終わったら
			if( pattern( ptActFigure[i], ANM_NOMAL_SPD, ANM_NO_LOOP ) ){
				// 次のアクションを设定する。
				SetCharSelNextAction( ptActFigure[i] );
				if( ptActFigure[1-i] == NULL )break;
				SetCharSelNextAction( ptActFigure[1-i] ); break;
			}
		}
	}

	// キャラのアクション消す
	if( ret != 0 ){
		for( i = 0; i < MaxSelectChar; i++ ){
			if( ptActSelChar[i] != NULL ){
				DeathAction( ptActSelChar[i] );
				ptActSelChar[i] = NULL;
			}
		}
	}

	if( PackageVer >= PV_FIRST_VER2 ){
		// ページ切り替えボタンを表示
		drawGraBtnInfo2( &V2_SelGra_ChangeBtn, DISP_PRIO_CHAR, 0, 0, 0 );
		// １ページのボタンを押されたら？
		if( pushGraBtnInfo2( &V2_SelGra_ChangeBtn ) & BTN_LEFT_CLICK ){
#ifdef PUK2
			if ( PackageVer >= PV_PUK2 ){
				if( giCreatePageNo == PUK2_GRANO_OLD ){
					giCreatePageNo = PUK2_GRANO_NEW;
				}else{
					giCreatePageNo = PUK2_GRANO_OLD;
				}
			}else{
				if( giCreatePageNo == V2_GRANO_NORMAL ){
					giCreatePageNo = V2_GRANO_NEW;
				}else{
					giCreatePageNo = V2_GRANO_NORMAL;
				}
			}
#else
			if( giCreatePageNo == V2_GRANO_NORMAL ){
				giCreatePageNo = V2_GRANO_NEW;
			}else{
				giCreatePageNo = V2_GRANO_NORMAL;
			}
#endif
			SubProcNo = 1;	// ここで初期化へ返回。
		}

		// 下敷きを表示
#ifdef PUK2
		if ( ( giCreatePageNo == V2_GRANO_NORMAL ) || ( giCreatePageNo == PUK2_GRANO_OLD ) ){
#else
		if( giCreatePageNo == V2_GRANO_NORMAL ){
#endif
			StockDispBuffer( 320, 240, DISP_PRIO_CHAR-2, V2_CHR_MAKE_SEL_UNDERSHEET_2, 0 );
		}else{
			StockDispBuffer( 320, 240, DISP_PRIO_CHAR-2, V2_CHR_MAKE_SEL_UNDERSHEET_1, 0 );
		}

	}else
	if( PackageVer >= PV_FIRST_EX ){
		// 现在通常ＯＲリニューアルページ目なら
		if( giCreatePageNo == V2_GRANO_EX_RENEW
		||  giCreatePageNo == V2_GRANO_OLD
		){
			// １ページ目のボタンを表示
			drawGraBtnInfo2( &page1Btn, DISP_PRIO_TILE, 0, 0, 2 );
			// 次のページのボタンを押されたら？
			if( pushGraBtnInfo2( &page2Btn ) & BTN_LEFT_CLICK ){
				giCreatePageNo = V2_GRANO_EX; // ＥＸページへ
				SubProcNo = 1;	// ここで初期化へ返回。
			}
		}else
		// 现在新キャラページなら
		if( giCreatePageNo == V2_GRANO_EX ){
			// ２ページ目のボタンを表示
			drawGraBtnInfo2( &page2Btn, DISP_PRIO_TILE, 0, 0, 2 );
			// １ページのボタンを押されたら？
			if( pushGraBtnInfo2( &page1Btn ) & BTN_LEFT_CLICK ){
				if( giCreateRenewalFlg ){
					// リニューアルフラグ立ってるならリニューアルに。
					giCreatePageNo = V2_GRANO_EX_RENEW;
				}else{
					giCreatePageNo = V2_GRANO_OLD;
				}
				SubProcNo = 1;	// ここで初期化へ返回。
			}
		}


		// Ｖ２以上なら新旧切换ボタンを押す
		if( ( PackageVer == PV_FIRST_EX || PackageVer == PV_UP_EX )
		&&  ( giCreatePageNo == V2_GRANO_OLD || giCreatePageNo == V2_GRANO_EX_RENEW )
		){
			if(	T_TextButton( 400, 120, 400+106, 120+30, ML_STRING(289, "新旧切换") ) == 1 ){
				SubProcNo = 1;	// ここで初期化へ返回。
				// 切り替え
				giCreatePageNo = (giCreatePageNo == V2_GRANO_OLD )?(V2_GRANO_EX_RENEW):(V2_GRANO_OLD );
				giCreateRenewalFlg ^= 1;
			}
		}

	}

	if( PackageVer >= PV_FIRST_VER2 ){
		// ローテートボタンは无い。ただしキャラ画像をクリックすると回る
		drawGraBtnInfo2( pRotateRightBtn, DISP_PRIO_TILE, 0, 0, 0 );
	}else{
		// ＲＯＴＡＴＥ左ボタン表示
		drawGraBtnInfo2( pRotateLeftBtn, DISP_PRIO_TILE, 0, 0, 0 );
		// ＲＯＴＡＴＥ右ボタン表示
		drawGraBtnInfo2( pRotateRightBtn, DISP_PRIO_TILE, 0, 0, 0 );
	}

	// ＣＯＬＯＲ左ボタン表示
	drawGraBtnInfo2( pColorLeftBtn, DISP_PRIO_CHAR+5, 0, 0, 0 );
	// ＣＯＬＯＲ右ボタン表示
	drawGraBtnInfo2( pColorRightBtn, DISP_PRIO_CHAR+5, 0, 0, 0 );

	// ＯＫボタン表示
	drawGraBtnInfo2( pOkBtn, DISP_PRIO_TILE, 0, 0, 0 );

	// ＢＡＣＫボタン表示
	drawGraBtnInfo2( pBackBtn, DISP_PRIO_TILE, 0, 0, 0 );

	// キャラの表示
	if( ptActSelChar[nowSelCharNo] ){
		ptActSelChar[nowSelCharNo]->anim_no = ANIM_WALK;
		pattern( ptActSelChar[nowSelCharNo], ANM_NOMAL_SPD, ANM_LOOP );
	}

	{ int iWakuX=-30, iWakuY=-92, iWakuX2=-30+60, iWakuY2 = -92+106;
		if( PackageVer >= PV_FIRST_VER2 ){
			iWakuX=-34, iWakuY=-38, iWakuX2=-34+67, iWakuY2 = -39+77;
		}
		// 选择されてるキャラに枠を表示
		StockBoxDispBuffer(
			pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].x+iWakuX,
			pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].y+iWakuY,
			pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].x+iWakuX2,
			pSelGraNoTbl->pSelectGraNoPos[nowSelCharNo].y+iWakuY2,
			DISP_PRIO_CHAR+1, SYSTEM_PAL_RED, 0 );

		// カーソル位置に枠を表示
		if( focusNo >= 0 ){
			StockBoxDispBuffer(
				pSelGraNoTbl->pSelectGraNoPos[focusNo].x+iWakuX,
				pSelGraNoTbl->pSelectGraNoPos[focusNo].y+iWakuY,
				pSelGraNoTbl->pSelectGraNoPos[focusNo].x+iWakuX2,
				pSelGraNoTbl->pSelectGraNoPos[focusNo].y+iWakuY2,
				DISP_PRIO_CHAR+1, BoxColor, 0 );
		}
	}

	// 认证されたバージョンにより背景を变化
	if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
#ifdef PUK2
		if ( PackageVer >= PV_PUK2 ){
			if( giCreatePageNo == PUK2_GRANO_OLD ){
				StockDispBuffer( 320, 240, DISP_PRIO_BG, PUK2_MAKECHRSELCG1, 0 );
			}else{
				StockDispBuffer( 320, 240, DISP_PRIO_BG, PUK2_MAKECHRSELCG2, 0 );
			}
		}else{
			if( giCreatePageNo == V2_GRANO_NORMAL ){
				StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_V2_N, 0 );
			}else{
				StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_V2, 0 );
			}
		}
#else
		if( giCreatePageNo == V2_GRANO_NORMAL ){
			StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_V2_N, 0 );
		}else{
			StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_V2, 0 );
		}
#endif
		StockDispBuffer( 320, 240, DISP_PRIO_CHAR-1, CG_FACE_UPUNDER, 0 );
	}else
	if( PackageVer >= PV_FIRST_EX ){	// ＥＸ
		if( giCreatePageNo == V2_GRANO_EX ){ // ＥＸページには别の絵
//			StockDispBuffer( 320, 240, DISP_PRIO_BG+1, CG_CHR_MAKE_SEL_WINDOW_EX_B, 0 );
		}else{
		}
		StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW_EX, 0 );
	}else{
		StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHR_MAKE_SEL_WINDOW, 0 );
	}

	return ret;
}

#endif // #ifdef USE_VERSION2_FRONTEND


//---------------------------------------------------------
//  文字列に縦棒 '|'や'\' が入っているかどうかチェック。
//---------------------------------------------------------
int VirticalCheck( unsigned char *pszBuffer ){
	int mode = 0;
	for( ; (*pszBuffer) != '\0'; pszBuffer++ ){
		// まず、汉字モードか？
		if( mode == 1 ){	// 汉字モードだったら
			mode = 0;		// 今回の文字は汉字の２バイト目なので关系ない。
			continue;		// 汉字モード解除して次へ。
		}
		// 汉字か？
		if( ( *pszBuffer ) >= 0x81 ){
			mode = 1;	// 汉字モードにして、次の文字へ。
			continue;
		}
		// 縦棒文字は 0x7c だ
		if( (*pszBuffer) == 0x7C ){
			return 1;
		}
		// \ 文字は 0x5c だ
		if( (*pszBuffer) == 0x5C ){
			return 1;
		}
	}
	return 0;
}





int giFaceChangeFlg = 0;	// リニューアル时の颜グラ变更フラグ

// キャラクタのパラメータ设定初期化
void initEditCharParam( void )
{
	int iFontColor = FONT_PAL_WHITE;
	editCharParamProcNo = 0;
	giFaceChangeFlg = 0;	// 颜グラ变更初期化

	// 名称入力处理の初期化
#ifdef MULTI_GRABIN
	// 名称入力处理の初期化
	if( PackageVer >= PV_FIRST_VER2 ){
		selCharNameBoxX = 84;
		selCharNameBoxY = 135;
//		iFontColor = FONT_PAL_GRAY;
		iFontColor = FONT_PAL_BLUE2;
	}else{
		selCharNameBoxX = 121;
		selCharNameBoxY = 92;
	}

#else
	selCharNameBoxX = 121;
	selCharNameBoxY = 92;
#endif
//	selCharNameBoxX = 121;
//	selCharNameBoxY = 94;
	selCharName.buffer[0] = '\0';
	selCharName.cnt = 0;
	selCharName.cursorByte = 0;
	InitInputStr( &selCharName, selCharNameBoxX, selCharNameBoxY,
		FONT_PRIO_BACK, FONT_KIND_MIDDLE, iFontColor, NULL, 1, CHAR_NAME_LEN, 0, 0 );
	GetKeyInputFocus( &selCharName );

	// 能力ポイント振り分け处理の初期化
	nowSelCharStatusPoint = 30;		// キャラの状态に割り振れるポイント
	nowSelCharStatus[0]   =  0;		// キャラの体力
	nowSelCharStatus[1]   =  0;		// キャラの腕力
	nowSelCharStatus[2]   =  0;		// キャラの防御力
	nowSelCharStatus[3]   =  0;		// キャラの敏捷力
	nowSelCharStatus[4]   =  0;		// キャラの魔法力
	nowSelCharAttrPoint   = 10;		// キャラの属性に割り振れるポイント
	nowSelCharAttr[0]     =  0;		// キャラの地属性
	nowSelCharAttr[1]     =  0;		// キャラの水属性
	nowSelCharAttr[2]     =  0;		// キャラの火属性
	nowSelCharAttr[3]     =  0;		// キャラの风属性


}

// キャラクタのパラメータ设定初期化２
void initEditCharParam2( void )
{
	int iFontColor = FONT_PAL_WHITE;
	editCharParamProcNo = 0;
#ifdef MULTI_GRABIN
	// 名称入力处理の初期化
	if( PackageVer >= PV_FIRST_VER2 ){
		selCharNameBoxX = 84;
		selCharNameBoxY = 135;
		iFontColor = FONT_PAL_GRAY;
	}else{
		selCharNameBoxX = 121;
		selCharNameBoxY = 92;
	}
#else
	selCharNameBoxX = 121;
	selCharNameBoxY = 92;
#endif
	selCharName.buffer[0] = '\0';
	selCharName.cnt = 0;
	selCharName.cursorByte = 0;
	InitInputStr( &selCharName, selCharNameBoxX, selCharNameBoxY,
		FONT_PRIO_BACK, FONT_KIND_MIDDLE, iFontColor, NULL, 1, CHAR_NAME_LEN, 0, 0 );
	GetKeyInputFocus( &selCharName );
}


// ＯＫボタンＥＸ
GRA_BTN_INFO2 CEdit_okBtn_Ex =
	{ 320, 240, 355, 403,  76,  42, 0, CG_CHAR_MAKE_OK_EX };
// ＱＵＩＴボタンＥＸ
GRA_BTN_INFO2 CEdit_backBtn_Ex =
		{ 320, 240, 462, 404, 126,  42, 0, CG_CHAR_MAKE_BACK_EX };

// ＯＫボタンＶ２
GRA_BTN_INFO2 CEdit_okBtn_V2 =
	{ 320, 240, 442, 413,  51,  28, 0, V2_CHAR_MAKE_OK };
// ＱＵＩＴボタンＶ２
GRA_BTN_INFO2 CEdit_backBtn_V2 =
		{ 320, 240, 542, 40, 68,  16, 0, V2_CHAR_MAKE_BACK };



int V2_attrLocate[][2] =
{
	{ 430-9, 125+28*0 },
	{ 430-9, 125+28*1 },
	{ 430-9, 125+28*2 },
	{ 430-9, 125+28*3 }
};

int V2_statusLocate[][2] =
{
	{ 180, 302+23*0-10 },
	{ 180, 302+23*1-10 },
	{ 180, 302+23*2-10 },
	{ 180, 302+23*3-10 },
	{ 180, 302+23*4-10 }
};

	// パラメータ变更ボタン
	GRA_BTN_INFO1 EditBtnList[] =
	{
		// 体力下降ボタン
		{ 218, 304, 206, 293,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 体力上升ボタン
		{ 254, 304, 243, 293,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 攻击力下降ボタン
		{ 218, 331, 206, 320,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 攻击力上升ボタン
		{ 254, 331, 242, 320,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 防御力下降ボタン
		{ 218, 359, 206, 348,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 防御力上升ボタン
		{ 254, 359, 242, 348,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 敏捷力下降ボタン
		{ 218, 385, 206, 374,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 敏捷力上升ボタン
		{ 254, 385, 242, 374,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 魔法力下降ボタン
		{ 218, 412, 206, 401,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 魔法力上升ボタン
		{ 254, 412, 242, 401,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },


		// 地属性下降ボタン
		{ 544, 173, 532, 162,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 地属性上升ボタン
		{ 580, 173, 568, 162,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 水属性下降ボタン
		{ 544, 214, 532, 203,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 水属性上升ボタン
		{ 580, 214, 568, 203,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 火属性下降ボタン
		{ 544, 254, 532, 243,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 火属性上升ボタン
		{ 580, 254, 568, 243,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 风属性下降ボタン
		{ 544, 294, 532, 283,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 风属性上升ボタン
		{ 580, 294, 568, 283,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },

		// 颜画像の目变更左ボタン
		{ 218, 184, 206, 174,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 颜画像の目变更右ボタン
		{ 254, 184, 242, 174,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN },
		// 颜画像の口变更左ボタン
		{ 218, 212, 206, 202,  24,  20, CG_CHAR_MAKE_LEFT_UP, CG_CHAR_MAKE_LEFT_DOWN },
		// 颜画像の口变更右ボタン
		{ 254, 212, 242, 202,  24,  20, CG_CHAR_MAKE_RIGHT_UP, CG_CHAR_MAKE_RIGHT_DOWN }
	};

#define V2_Y_OFFS 0
//#define V2_YAJI_LX (218+8)
#define V2_YAJI_LX (219)
//#define V2_YAJI_RX 243
#define V2_YAJI_RX 253
//#define V2_YAJI_Y  (294-10)
#define V2_YAJI_Y  (292)
#define V2_YAJI_Y_STEP 	23
	// パラメータ变更ボタン。范围指定は后でプログラムで指定
	GRA_BTN_INFO1 EditBtnList_V2[] =
	{
		// 体力下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*0+V2_Y_OFFS, 202, 294+22*0,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 体力上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*0+V2_Y_OFFS, 235, 294+22*0,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 攻击力下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*1+V2_Y_OFFS, 202, 294+22*1,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 攻击力上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*1+V2_Y_OFFS, 235, 294+22*1,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 防御力下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*2+V2_Y_OFFS, 202, 294+22*2,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 防御力上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*2+V2_Y_OFFS, 235, 294+22*2,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 敏捷力下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*3+V2_Y_OFFS, 202, 294+22*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 敏捷力上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*3+V2_Y_OFFS, 235, 294+22*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 魔法力下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*4+V2_Y_OFFS, 202, 294+22*4,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 魔法力上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*4+V2_Y_OFFS, 235, 294+22*4,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },

#undef V2_YAJI_LX
#undef V2_YAJI_RX
#undef V2_YAJI_Y
#undef V2_YAJI_Y_STEP
#undef V2_Y_OFFS

//#define V2_YAJI_LX 537
#define V2_YAJI_LX 531
//#define V2_YAJI_RX 553
#define V2_YAJI_RX 555
#define V2_YAJI_Y  (141-13)
//#define V2_YAJI_Y  (131)
#define V2_YAJI_Y_STEP 	28
#define V2_Y_OFFS 4
		// 地属性下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*0+V2_Y_OFFS, 520, 135+28*0,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 地属性上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*0+V2_Y_OFFS, 544, 135+28*0,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 水属性下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*1+V2_Y_OFFS, 520, 135+28*1,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 水属性上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*1+V2_Y_OFFS, 544, 135+28*1,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 火属性下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*2+V2_Y_OFFS, 520, 135+28*2,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 火属性上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*2+V2_Y_OFFS, 544, 135+28*2,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 风属性下降ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*3+V2_Y_OFFS, 520, 135+28*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 风属性上升ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*3+V2_Y_OFFS, 544, 135+28*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },

#undef V2_YAJI_LX
#undef V2_YAJI_RX
#undef V2_YAJI_Y
#undef V2_YAJI_Y_STEP
#undef V2_Y_OFFS

//#define V2_YAJI_LX (166)
#define V2_YAJI_LX (162)
//#define V2_YAJI_RX (184)
#define V2_YAJI_RX (186)
//#define V2_YAJI_Y  (201)
#define V2_YAJI_Y  (199)
#define V2_YAJI_Y_STEP 	28
#define V2_Y_OFFS 0

		// 颜画像の目变更左ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*0+V2_Y_OFFS, 520, 135+28*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 颜画像の目变更右ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*0+V2_Y_OFFS, 544, 135+28*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
		// 颜画像の口变更左ボタン
		{ V2_YAJI_LX, V2_YAJI_Y+V2_YAJI_Y_STEP*1+V2_Y_OFFS, 520, 135+28*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1, V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2 },
		// 颜画像の口变更右ボタン
		{ V2_YAJI_RX, V2_YAJI_Y+V2_YAJI_Y_STEP*1+V2_Y_OFFS, 544, 135+28*3,  14,  17, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1, V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2 },
	};

void V2_drawNumGra( int x, int y, int num );



// キャラクタのパラメータ设定
//
//  戾り值：	0 ... 处理中
//				1 ... 决定
//				2 ... キャンセル
int editCharParam( void )
{
	int ret = 0, ret2;
	int x1, y1, x2, y2;
	int i;
	int id, id2;
	int statusLocate[][2] =
	{
		{ 172, 302 },
		{ 172, 329 },
		{ 172, 356 },
		{ 172, 383 },
		{ 172, 410 }
	};
	int attrLocate[][2] =
	{
		{ 410, 156 },
		{ 410, 197 },
		{ 410, 238 },
		{ 410, 279 }
	};
	int attrColor[][2] =
	{
		{ SYSTEM_PAL_GREEN,  SYSTEM_PAL_GREEN2  },
		{ SYSTEM_PAL_AQUA,   SYSTEM_PAL_AQUA2   },
		{ SYSTEM_PAL_RED,    SYSTEM_PAL_RED2    },
		{ SYSTEM_PAL_YELLOW, SYSTEM_PAL_YELLOW2 }
	};
	int selUseFlag;
	int maxPoint;

	enum
	{
		BTN_OK,									// OKボタン
		BTN_BACK,								// BACKボタン

		BTN_VIT_DOWN,							// 体力下降ボタン
		BTN_VIT_UP,								// 体力上升ボタン
		BTN_STR_DOWN,							// 攻击力下降ボタン
		BTN_STR_UP,								// 攻击力上升ボタン
		BTN_TGH_DOWN,							// 防御力下降ボタン
		BTN_TGH_UP,								// 防御力上升ボタン
		BTN_QUI_DOWN,							// 敏捷力下降ボタン
		BTN_QUI_UP,								// 敏捷力上升ボタン
		BTN_MGC_DOWN,							// 魔法力下降ボタン
		BTN_MGC_UP,								// 魔法力上升ボタン

		BTN_EARTH_DOWN,							// 地属性下降ボタン
		BTN_EARTH_UP,							// 地属性上升ボタン
		BTN_WATER_DOWN,							// 水属性下降ボタン
		BTN_WATER_UP,							// 水属性上升ボタン
		BTN_FIRE_DOWN,							// 火属性下降ボタン
		BTN_FIRE_UP,							// 火属性上升ボタン
		BTN_WIND_DOWN,							// 风属性下降ボタン
		BTN_WIND_UP,							// 风属性上升ボタン

		BTN_EYE_LEFT,							// 颜画面 目变更左ボタン
		BTN_EYE_RIGHT,							// 颜画面 目变更右ボタン
		BTN_MOUTH_LEFT,							// 颜画面 口变更左ボタン
		BTN_MOUTH_RIGHT,						// 颜画面 口变更右ボタン

		BTN_MAX									// 最大ボタン数
	};

	// ＯＫボタン
	GRA_BTN_INFO2 okBtn =
		{ 320, 240, 355, 403,  76,  42, 0, CG_CHAR_MAKE_OK };
	// ＱＵＩＴボタン
	GRA_BTN_INFO2 backBtn =
		{ 320, 240, 462, 404, 126,  42, 0, CG_CHAR_MAKE_BACK };

	int attrInfoFlag[4], iBtnListSize = sizeof( EditBtnList )/sizeof( EditBtnList[0] );
	SELECTGRANO *pSelGraNoTbl;
	GRA_BTN_INFO2	*pOkBtn = &okBtn, *pBackBtn = &backBtn;
	GRA_BTN_INFO1	*pBtn = EditBtnList;

#ifdef MULTI_GRABIN
	if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
		int i;
		pOkBtn = &CEdit_okBtn_V2;
		pBackBtn = &CEdit_backBtn_V2;
		pBtn = EditBtnList_V2;
		iBtnListSize = sizeof( EditBtnList_V2 )/sizeof( EditBtnList_V2[0] );

		for( i = 0; i < iBtnListSize; i ++ ){
			EditBtnList_V2[i].x = EditBtnList_V2[i].cx-9;
			EditBtnList_V2[i].y = EditBtnList_V2[i].cy-9;
			EditBtnList_V2[i].w = 15;
			EditBtnList_V2[i].h = 17;
		}

	}else
	if( PackageVer >= PV_FIRST_EX ){	// ＥＸのＯＫボタン
		pOkBtn = &CEdit_okBtn_Ex;
		pBackBtn = &CEdit_backBtn_Ex;
		iBtnListSize = sizeof( EditBtnList )/sizeof( EditBtnList[0] );
	}
#endif
	// 最大割り振りポイントの计算
	maxPoint = (nowSelCharStatusPoint + nowSelCharStatus[0] + nowSelCharStatus[1]
		+ nowSelCharStatus[2] + nowSelCharStatus[3] + nowSelCharStatus[4])/2;


	selUseFlag = 0;		// ボタン选择可能に设定

	GetKeyInputFocus( NULL );

	id = -1;

	// 今回のキャラセレクト一覧
	pSelGraNoTbl = &aSelGraNoTbl[giCreatePageNo];


	// パラメータ设定处理
	if( editCharParamProcNo == 1 )
	{
		GetKeyInputFocus( &selCharName );

		// 颜にヒットしてるか？
		if( PackageVer >= PV_FIRST_VER2 ){
			if( MakeHitBox( 205, 179, 271,250, -1 )
			&& (mouse.autoState & MOUSE_LEFT_CRICK)
			){
#ifdef PUK2
				if (giFaceChangeFlg==0){
					if ( newCharacterFaceGraNo==getReFaceGraphic(newCharacterFaceGraNo) ) giFaceChangeFlg++;
				}
				giFaceChangeFlg++;
				if (giFaceChangeFlg>2) giFaceChangeFlg=0;
#else
				// 颜グラ变更フラグをたてる。
				giFaceChangeFlg ^= 1;
#endif
			}
		}

		// ボタン情报を取得
		if( pushGraBtnInfo2( pOkBtn ) & BTN_LEFT_CLICK )
		{
			id = BTN_OK;
		}
		else
		if( pushGraBtnInfo2( pBackBtn ) & BTN_LEFT_CLICK )
		{
			id = BTN_BACK;
		}
		else
		{
			for( i = 0; i < iBtnListSize; i++ )
			{
				if( pushGraBtnInfo1( &pBtn[i] ) & BTN_LEFT_CLICK_REP )
				{
					id = BTN_VIT_DOWN+i;
					break;
				}
			}
		}

		// 决定
		if( id == BTN_OK )
		{
			// 名称が入力されてない
			if( selCharName.cnt <= 0 )
			{
				editCharParamProcNo = 100;
			}
			else
			// 状态の振り分けポイントが残ってる
			if( nowSelCharStatusPoint > 0 )
			{
				editCharParamProcNo = 104;
			}
			else
			// 属性の振り分けポイントが残ってる
			if( nowSelCharAttrPoint > 0 )
			{
				editCharParamProcNo = 106;
			}
			else
			// 同じキャラ名がいる
			{
				strcpy( newCharacterName, selCharName.buffer );
				newCharacterName[selCharName.cnt] = '\0';

				if( cmpNameCharacterList( selCharName.buffer ) )
				{
					// 同じ名称があるのでダメ
					editCharParamProcNo = 102;
				}
				else
				// 空白文字が含まれている名称はダメ
				if( strstr( newCharacterName, " " ) != NULL                       //MLHIDE
				 || strstr( newCharacterName, "　" ) != NULL )                     //MLHIDE
				{
					editCharParamProcNo = 108;
				}
				else
				// 縦棒が含まれている名称はダメ
				if( VirticalCheck( (unsigned char *)newCharacterName ) == 1 )
				{
					editCharParamProcNo = 110;
				}
				else
				{
					// 决定
					editCharParamProcNo = 10;
					// 决定ａ
					play_se( SE_NO_OK, 320, 240 );
				}
			}
		}
		else
		// 返回
		if( id == BTN_BACK )
		{
			ret = 2;

			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
		}
		else
		// 状态パラメータ振り分け
		if( BTN_VIT_DOWN <= id && id <= BTN_MGC_UP )
		{
			id2 = id - BTN_VIT_DOWN;
			// パラメータを减らす
			if( (id2 % 2) == 0 )
			{
				if( nowSelCharStatus[id2/2] > 0 )
				{
					nowSelCharStatus[id2/2]--;
					nowSelCharStatusPoint++;
					play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				}
				else
				{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
			}
			else
			// パラメータを增やす
			{
				if( nowSelCharStatusPoint > 0
				 && maxPoint > nowSelCharStatus[id2/2] )
				{
					nowSelCharStatus[id2/2]++;
					nowSelCharStatusPoint--;
					play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				}
				else
				{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
			}
		}
		else
		// 属性の振り分け
		if( BTN_EARTH_DOWN <= id && id <= BTN_WIND_UP )
		{
			id2 = id - BTN_EARTH_DOWN;
			// パラメータを减らす
			if( (id2 % 2) == 0 )
			{
				if( nowSelCharAttr[id2/2] > 0 )
				{
					nowSelCharAttr[id2/2]--;
					nowSelCharAttrPoint++;
					play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				}
				else
				{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
			}
			else
			// パラメータを增やす
			{
				int no, no2;

				no = id2/2;
				if( no < 2 )
				{
					no2 = no+2;
				}
				else
				{
					no2 = no-2;
				}

				if( nowSelCharAttr[no2] == 0 )
				{
					if( nowSelCharAttrPoint > 0 )
					{
						nowSelCharAttr[no]++;
						nowSelCharAttrPoint--;
						play_se( SE_NO_CLICK, 320, 240 );	// クリック音
					}
					else
					// 振り分けポイントが０で增やすボタンを押したらほかのところから
					// 持ってくる
					{
						// 现在押されたボタン以外にパラメータが振り分けられているところを探す
						for( i = 0; i < 4; i++ )
						{
							if( i != no && nowSelCharAttr[i] > 0 )
							{
								break;
							}
						}
						if( i < 4 )
						{
							nowSelCharAttr[no]++;
							nowSelCharAttr[i]--;
							play_se( SE_NO_CLICK, 320, 240 );	// クリック音
						}
						else
						{
							// ＮＧ音（短い）
							play_se( SE_NO_NG, 320, 240 );
						}
					}
				}
			}
		}
		else
		// 颜画像の目を变更
		if( id == BTN_EYE_LEFT || id == BTN_EYE_RIGHT )
		{
			// パラメータを减らす
			if( id == BTN_EYE_LEFT )
			{
				if( nowSelCharEyeNo > 0 )
				{
					nowSelCharEyeNo--;
				}
				else
				{
					nowSelCharEyeNo = 4;
				}
			}
			else
			// パラメータを增やす
			{
				if( nowSelCharEyeNo < 4 )
				{
					nowSelCharEyeNo++;
				}
				else
				{
					nowSelCharEyeNo = 0;
				}
			}
			play_se( SE_NO_CLICK, 320, 240 );	// クリック音
		}
		else
		// 颜画像の口を变更
		if( id == BTN_MOUTH_LEFT || id == BTN_MOUTH_RIGHT )
		{
			// パラメータを减らす
			if( id == BTN_MOUTH_LEFT )
			{
				if( nowSelCharMouthNo > 0 )
				{
					nowSelCharMouthNo--;
				}
				else
				{
					nowSelCharMouthNo = 4;
				}
			}
			else
			// パラメータを增やす
			{
				if( nowSelCharMouthNo < 4 )
				{
					nowSelCharMouthNo++;
				}
				else
				{
					nowSelCharMouthNo = 0;
				}
			}
			play_se( SE_NO_CLICK, 320, 240 );	// クリック音
		}
	}


	// 初期化
	if( editCharParamProcNo == 0 )
	{
		editCharParamProcNo++;
	}


	// 决定确认处理
	if( editCharParamProcNo == 10 )
	{
		selUseFlag = 1;		// ボタン选择不可能に设定

		//ret2 = commonYesNoWindow( 320, 240 );	// ohta
		ret2 = commonYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER, ML_STRING(190, "可以吗？"), FONT_PAL_WHITE );
		// はい
		if( ret2 == 1 )
		{
			ret = 1;
			editCharParamProcNo = 11;	// ダミー
			sjisStringToEucString( newCharacterName );
			// キャラの姿の计算はなし。そのまま
			newCharacterGraNo = selCharFigureNo;

			newCharacterVit = nowSelCharStatus[0];
			newCharacterStr = nowSelCharStatus[1];
			newCharacterTgh = nowSelCharStatus[2];
			newCharacterQui = nowSelCharStatus[3];
			newCharacterMgc = nowSelCharStatus[4];
			newCharacterEarth = nowSelCharAttr[0];
			newCharacterWater = nowSelCharAttr[1];
			newCharacterFire  = nowSelCharAttr[2];
			newCharacterWind  = nowSelCharAttr[3];
			loginDp = 0;
			fade_out_bgm();
		}
		else
		// いいえ
		if( ret2 == 2 )
		{
			editCharParamProcNo = 1;
		}
	}


	// エラー信息
	// 名称入力がまだ
	if( editCharParamProcNo == 100 )
	{
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 101 )
	{
		selUseFlag = 1;		// ボタン选择不可能に设定
		if( commonMsgWin( ML_STRING(191, "没有输入名字！") ) )
		{
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 同一キャラ名
	if( editCharParamProcNo == 102 )
	{
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 103 )
	{
		selUseFlag = 1;		// ボタン选择不可能に设定
		if( commonMsgWin( ML_STRING(192, "已经有相同名字的角色了！") ) )
		{
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 状态の振り分けポイントが残ってる
	if( editCharParamProcNo == 104 )
	{
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 105 )
	{
		selUseFlag = 1;		// ボタン选择不可能に设定
		if( commonMsgWin( ML_STRING(193, "请分配好状态点数！") ) )
		{
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 属性の振り分けポイントが残ってる
	if( editCharParamProcNo == 106 )
	{
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音

	}
	if( editCharParamProcNo == 107 )
	{

		selUseFlag = 1;		// ボタン选择不可能に设定
		if( commonMsgWin( ML_STRING(194, "请分配好属性！") ) )
		{
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 名称が空白文字
	if( editCharParamProcNo == 108 )
	{
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音

	}
	if( editCharParamProcNo == 109 )
	{

		selUseFlag = 1;		// ボタン选择不可能に设定
		if( commonMsgWin( ML_STRING(195, "无法使用带空白文字的名字！") ) )
		{
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}

	// 名称が縦棒文字
	if( editCharParamProcNo == 110 )
	{
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音

	}
	if( editCharParamProcNo == 111 )
	{

		selUseFlag = 1;		// ボタン选择不可能に设定
		if( commonMsgWin( ML_STRING(196, " | 或者 \\ 无法使用在名字中！") ) )
		{
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}


	// 属性ボタンの表示??非表示の设定
	for( i = 0; i < 4; i++ )
	{
		if( i == 0 && nowSelCharAttr[i+2] == 0
		 || i == 1 && nowSelCharAttr[i+2] == 0
		 || i == 2 && nowSelCharAttr[i-2] == 0
		 || i == 3 && nowSelCharAttr[i-2] == 0 )
		{
			attrInfoFlag[i] = 1;
		}
		else
		{
			attrInfoFlag[i] = 0;
		}
	}


	id = -1;

	// 说明
	if( editCharParamProcNo == 1 )
	{
		// フォーカスがあったか？
		if( (pushGraBtnInfo2( pOkBtn ) & BTN_FOCUS_ON) )
		{
			id = BTN_OK;
		}
		else
		if( (pushGraBtnInfo2( pBackBtn ) & BTN_FOCUS_ON) )
		{
			id = BTN_BACK;
		}
		else
		{
			for( i = 0; i < iBtnListSize; i++ )
			{
				if( (pushGraBtnInfo1( &pBtn[i] ) & BTN_FOCUS_ON) )
				{
					id = i + BTN_VIT_DOWN;
					break;
				}
			}
		}


		if( id == BTN_OK )
		{
			strcpy( OneLineInfoStr, ML_STRING(290, "确定设定好的角色。") );
		}
		else
		if( id == BTN_BACK )
		{
			strcpy( OneLineInfoStr, ML_STRING(291, "回到前一画面。") );
		}
		else
		if( BTN_VIT_DOWN <= id && id <= BTN_MGC_UP )
		{
			id -= BTN_VIT_DOWN;
			id /= 2;
			if( id == 0 )
			{
				strcpy( OneLineInfoStr, ML_STRING(292, "主要与体力相关。") );
			}
			else
			if( id == 1 )
			{
				strcpy( OneLineInfoStr, ML_STRING(293, "主要与攻击力相关。") );
			}
			else
			if( id == 2 )
			{
				strcpy( OneLineInfoStr, ML_STRING(294, "主要与防御力相关。") );
			}
			else
			if( id == 3 )
			{
				strcpy( OneLineInfoStr, ML_STRING(295, "主要与敏捷相关。") );
			}
			else
			if( id == 4 )
			{
				strcpy( OneLineInfoStr, ML_STRING(296, "主要与精神相关。") );
			}
		}
		else
		if( BTN_EARTH_DOWN <= id && id <= BTN_WIND_UP
		 && attrInfoFlag[(id-BTN_EARTH_DOWN)/2] )
		{
			id -= BTN_EARTH_DOWN;
			id /= 2;
			if( id == 0 )
			{
				strcpy( OneLineInfoStr, ML_STRING(297, "地属性，对于与水属性的战斗有利。") );
			}
			else
			if( id == 1 )
			{
				strcpy( OneLineInfoStr, ML_STRING(298, "水属性，对于与火属性的战斗游戏。") );
			}
			else
			if( id == 2 )
			{
				strcpy( OneLineInfoStr, ML_STRING(299, "火属性，对于与风属性的战斗有利。") );
			}
			else
			if( id == 3 )
			{
				strcpy( OneLineInfoStr, ML_STRING(300, "风属性，对于与地属性的战斗有利。") );
			}
		}
		else
		if( id == BTN_EYE_LEFT || id == BTN_EYE_RIGHT )
		{
			strcpy( OneLineInfoStr, ML_STRING(301, "改变眼睛。") );
		}
		else
		if( id == BTN_MOUTH_LEFT || id == BTN_MOUTH_RIGHT )
		{
			strcpy( OneLineInfoStr, ML_STRING(302, "改变嘴型。") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(303, "请设定名字、脸、属性、元素属性。") );
		}
	}



	StockFontBuffer2( &selCharName );




	// 初期のキャラクターの场合の颜グラフィック计算
	// 颜グラ表示は通常と、ＥＸ／Ｖ２では法则が违う
	if( selCharNew_Pattern == 0 ){
		newCharacterFaceGraNo = selCharFaceNo
			+nowSelCharColor*1000
			+nowSelCharEyeNo*10
			+nowSelCharMouthNo;
	}else{
		newCharacterFaceGraNo = selCharFaceNo
			+nowSelCharColor*25
			+nowSelCharEyeNo*5
			+nowSelCharMouthNo;
	}




#ifdef MULTI_GRABIN
	// 颜グラフィック表示及び姿の变更
	{	int FaceX = 87, FaceY = 200, tmpDisplayFace = newCharacterFaceGraNo;

#ifdef PUK2
		if( PackageVer >= PV_PUK2 ){	// バージョン２
			FaceX = 237, FaceY = 214;
			// 颜グラ变更フラグが立っていたら
			switch( giFaceChangeFlg ){
			case 0:
				// 古い颜にする。
				tmpDisplayFace = getFaceGraphicFormPUK2(newCharacterFaceGraNo);
				tmpDisplayFace = getOldFaceGraphic(tmpDisplayFace);
				newCharacterFaceGraNo = tmpDisplayFace;
				// キャラの姿も古いにする。
				if( giFaceChangeFlg ) newCharacterGraNo = selCharSubFigureNo;
				// キャラの姿の计算はなし。そのまま
				else newCharacterGraNo = selCharFigureNo;
				giCreateRenewalFlg = 0;
				break;
			case 1:
				// 新しい颜にする。
				tmpDisplayFace = getFaceGraphicFormPUK2(newCharacterFaceGraNo);
				tmpDisplayFace = getReFaceGraphic(tmpDisplayFace);
				newCharacterFaceGraNo = tmpDisplayFace;
				// キャラの姿の计算はなし。そのまま
				newCharacterGraNo = selCharFigureNo;
				giCreateRenewalFlg = 1;
				break;
			case 2:
				// 新しい颜にする。
				tmpDisplayFace = getReFaceGraphic(newCharacterFaceGraNo);
				tmpDisplayFace = getFaceGraphicToPUK2(tmpDisplayFace);
				newCharacterFaceGraNo = tmpDisplayFace;
				// キャラの姿の计算はなし。そのまま
				newCharacterGraNo = selCharFigureNo;
				giCreateRenewalFlg = 2;
				break;
			}
		}else
#endif
		if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
			FaceX = 237, FaceY = 214;
			// 颜グラ变更フラグが立っていたら
			if( giFaceChangeFlg ){
				// 古い颜にする。
				tmpDisplayFace = getOldFaceGraphic(newCharacterFaceGraNo);
				newCharacterFaceGraNo = tmpDisplayFace;
				// キャラの姿も古いにする。
				newCharacterGraNo = selCharSubFigureNo;
			}else{
				// 新しい颜にする。
				tmpDisplayFace = getReFaceGraphic(newCharacterFaceGraNo);
				newCharacterFaceGraNo = tmpDisplayFace;
				// キャラの姿の计算はなし。そのまま
				newCharacterGraNo = selCharFigureNo;
			}
//#endif
		}
		// 颜グラフィック
		StockDispBuffer( FaceX, FaceY, DISP_PRIO_BG, tmpDisplayFace, 0 );
	}
	// リニューアルされてるかをセット(后でログインの时に使うから)
#ifndef PUK2
	#ifdef PUK2
		if( getOtherFaceGraphicType( getFaceGraphicFormPUK2( newCharacterFaceGraNo ) ) == CG_OTHER_RE ){
	#else
		if( getOtherFaceGraphicType( newCharacterFaceGraNo ) == CG_OTHER_RE ){
	#endif
			giCreateRenewalFlg = 1;
		}else{
			giCreateRenewalFlg = 0;
		}
#endif



#else
	// 颜グラフィック
	StockDispBuffer( 87, 200, DISP_PRIO_BG,newCharacterFaceGraNo, 0 );
#endif

#ifdef MULTI_GRABIN
	if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
		// キャラの状态に割り振れるポイント
		V2_drawNumGra( 230, 270, nowSelCharStatusPoint );
		// キャラの状态
		for( i = 0; i < sizeof( nowSelCharStatus )/sizeof( nowSelCharStatus[0] ); i++ )
		{
			V2_drawNumGra( V2_statusLocate[i][0], V2_statusLocate[i][1], nowSelCharStatus[i] );
		}
		// キャラの属性に割り振れるポイント
		V2_drawNumGra( 536, 98, nowSelCharAttrPoint );
	}else
#endif
	{
		// キャラの状态に割り振れるポイント
		drawNumGra( 256, 268, nowSelCharStatusPoint );
		// キャラの状态
		for( i = 0; i < sizeof( nowSelCharStatus )/sizeof( nowSelCharStatus[0] ); i++ )
		{
			drawNumGra( statusLocate[i][0], statusLocate[i][1], nowSelCharStatus[i] );
		}
		// キャラの属性に割り振れるポイント
		drawNumGra( 532, 114, nowSelCharAttrPoint );
	}

	// 属性のメータ
	for( i = 0; i < 4; i ++ )
	{
		if( nowSelCharAttr[i] > 0 )
		{
			if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
				x1 = V2_attrLocate[i][0];
				y1 = V2_attrLocate[i][1];
			}else{
				x1 = attrLocate[i][0];
				y1 = attrLocate[i][1];
			}
			if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
				x2 = x1 + nowSelCharAttr[i] * 9;
//				if( nowSelCharAttr[i] > 1 ) x2 += 2*(nowSelCharAttr[i]-1);
				y2 = y1 + 13;
				StockBoxDispBuffer( x1+1,y1,   x2-1, y2,   DISP_PRIO_IME2, attrColor[i][0], 1 );
				StockBoxDispBuffer( x1,  y1-1, x2,   y2+1, DISP_PRIO_IME1, attrColor[i][1], 0 );
//				StockBoxDispBuffer( x1+2, y1+2, x2+2, y2+2, DISP_PRIO_IME1, attrColor[i][1], 0 );
			}else{
				x2 = x1 + nowSelCharAttr[i] * 11;
				y2 = y1 + 11;
				StockBoxDispBuffer( x1,   y1,   x2,   y2,   DISP_PRIO_IME2, attrColor[i][0], 1 );
				StockBoxDispBuffer( x1+1, y1+1, x2+1, y2+1, DISP_PRIO_IME1, attrColor[i][1], 0 );
				StockBoxDispBuffer( x1+2, y1+2, x2+2, y2+2, DISP_PRIO_IME1, attrColor[i][1], 0 );
			}
		}
	}


	// ボタンを表示
	for( i = 0; i < iBtnListSize; i++ )
	{
		if( i < BTN_EARTH_DOWN-2 || BTN_WIND_UP-2 < i
		 || (BTN_EARTH_DOWN-2 <= i && i <= BTN_WIND_UP-2
		  && attrInfoFlag[(i-(BTN_EARTH_DOWN-2))/2] ) )
		{
			drawGraBtnInfo1( &pBtn[i], DISP_PRIO_CHAR, 1, BoxColor, selUseFlag );
		}
	}

	// ＯＫボタン
	drawGraBtnInfo2( pOkBtn, DISP_PRIO_CHAR, 0, 0, selUseFlag );

	// ＢＡＣＫボタン
	drawGraBtnInfo2( pBackBtn, DISP_PRIO_CHAR, 0, 0, selUseFlag );


	// ＢＧ表示
	{
#ifdef MULTI_GRABIN
		// 认证されたバージョンにより背景を变化
#ifdef PUK2
		if( PackageVer >= PV_PUK2 ){	// バージョン２
			 StockDispBuffer( 320, 240, DISP_PRIO_BG, PUK2_CHARAMAKE, 0 );
		}else
#endif
		if( PackageVer >= PV_FIRST_VER2 ){	// バージョン２
			 StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHAR_MAKE_WINDOW_V2, 0 );
		}else
		if( PackageVer >= PV_FIRST_EX ){	// ＥＸ
			 StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHAR_MAKE_WINDOW_EX, 0 );
		}else{
			 StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHAR_MAKE_WINDOW, 0 );
		}
#else
		 StockDispBuffer( 320, 240, DISP_PRIO_BG, CG_CHAR_MAKE_WINDOW, 0 );
#endif
	}

	return ret;
}


// グラフィックの数字を表示（２桁）
void drawNumGra( int x, int y, int num )
{
	if( (num/10) > 0 )
	{
		StockDispBuffer( x, y, DISP_PRIO_CHAR, CG_NUM_0+num/10, 0 );
	}
	StockDispBuffer( x+14, y, DISP_PRIO_CHAR, CG_NUM_0+num%10, 0 );
}

// グラフィックの数字を表示（２桁）
void V2_drawNumGra( int x, int y, int num )
{
	if( (num/10) > 0 )
	{
		StockDispBuffer( x, y, DISP_PRIO_CHAR, V2_NUM_0+num/10, 0 );
	}
	StockDispBuffer( x+12, y, DISP_PRIO_CHAR, V2_NUM_0+num%10, 0 );
}





// 新キャラ作成处理
// 初期化
void initCreateChar( void )
{
	createCharProcNo = 0;
#ifdef PUK3_PROF

	ClearProfilePcNo(selectPcNo);
	saveProfileDateFile();

#endif
}


// キャラ作成处理
//  戾り值：	0 ... 处理中
//				1 ... 完了
//				2 ... 失败
int createChar( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	int ret = 0;
	int ret2;
	char* msg = ML_STRING(304, "创建角色中");

	// 初期化
	if( createCharProcNo == 0 )
	{
		createCharProcNo++;

		// ウィンドウ作成
		w = (strlen( msg )*9+63)/64;
		if( w < 2 )
			w = 2;
		h = 2;
		x = (DEF_APPSIZEX-w*64)/2;
		y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
	}

	// 新キャラ作成开始
	if( createCharProcNo == 1 )
	{
		createNewCharStart();
		createCharProcNo++;
	}
	if( createCharProcNo == 2 )
	{
		ret2 = createNewCharProc();
		// 完了
		if( ret2 == 1 )
		{
			ret = 1;
		}
		else
		// 失败
		if( ret2 < 0 )
		{
			ret = 2;
		}
	}


	// 结果がわかったらウィンドウ关闭
	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int len;
			int xx, yy;

			len = strlen( msg )/2;
			xx = (w*64-len*17)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg, 0 );
		}
	}

	return ret;
}




///////////////////////////////////////////////////////////////////////////
//
// キャラログイン
//

void initCharLogin( void );
int charLogin( void );

void characterLoginProc( void )
{
	int ret;
	static char msg[256];

	if( SubProcNo == 0 ){
		// ログイン处理初期化
		initCharLogin();
#ifdef PUK2
		GuildInfoInit();
#endif
		SubProcNo++;
	}


	if( SubProcNo == 1 ){
		// ログイン处理
		ret = charLogin();
		if( ret == 1 ){
			// ログイン成功
#ifdef _TAIKEN
			if( createCharFlag ){
				// 强制的に称号を「体验版」にする
				char title[128] = ML_STRING(305, "体验版");
				// ＥＵＣに变换
				sjisStringToEucString( title );
				// 名称变更送信
				nrproto_FT_send( sockfd, title );
			}
#endif

#ifdef PUK3_PROF_START_SEND
			//プロフィール情报を送信
			sendProfileInLogin();
#endif
			ChangeProc( PROC_GAME );
		}else if( ret == -1 ){
			// タイムアウト
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_114 );
#else
			strcpy( msg, NET_ERRMSG_LOGINTIMEOUT );
#endif
		}else if( ret == -2 ){
			// ログイン失败
			SubProcNo = 100;
#if 1
	#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, INFOMSG_23 );
	#else
			strcpy( msg, ML_STRING(306, "登入失败。") );
	#endif
#else
			if( charLoginErrCode < 1 || charLoginErrCodeMax <= charLoginErrCode ){
				charLoginErrCode = charLoginErrCodeMax - 1;
			}
			strcpy( msg, charLoginErrMsg[charLoginErrCode] );
#endif
		}
	}

	// エラー表示
	if( SubProcNo == 100 ){
		initCommonMsgWin();
		SubProcNo++;
	}

	if( SubProcNo == 101 ){
		if( commonMsgWin( msg ) ){
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );	// タイトルへ
		}
	}

}


static short charLoginProcNo = 0;

// 初期化
void initCharLogin( void )
{
	charLoginProcNo = 0;
}

// メイン处理
//
//  戾り值：	 0 ... 登入中
//				 1 ... ログイン完了
//				-1 ... タイムアウト
//				-2 ... エラー
int charLogin( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	int ret = 0;
	static char* msg = ML_STRING(307, "登入中");

	// 初期化
	if( charLoginProcNo == 0 )
	{
		charLoginProcNo = 1;

		// ウィンドウ作成
		w = strlen( msg )*9/64+2;
		h = (16+47)/48;
		if( h < 2 )
			h = 2;
		x = (DEF_APPSIZEX-w*64)/2;
		y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
	}

	// キャラログイン开始
	if( charLoginProcNo == 1 )
	{
		charLoginStart();
		charLoginProcNo++;
	}
	else
	if( charLoginProcNo == 2 )
	{
		ret = charLoginProc();
	}

	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int len;
			int xx, yy;

			len = strlen( msg );
			xx = (w*64-len*8)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg, 0 );
		}
	}

	return ret;
}




static int produce_logout(void);
static int produce_vct_no = 0;

///////////////////////////////////////////////////////////////////////////
//
// キャラ登出
//

#ifdef PUK2
	extern short charLogoutStatus;
#endif
void initCharLogout( void );
int charLogout( void );

void characterLogoutProc( void )
{
	int ret;
	static char msg[256];

	if( SubProcNo == 0 )
	{
		logOutFlag = TRUE;

		if(!produce_logout()){		//演出中なら
			return;
		}

		// パラメータセーブ
		saveUserSetting();

		//ＶＣＴ番号クリア
		produce_vct_no = 0;

		// PCリセット
		resetPc();

		// キャラ管理テーブルリセット
		initCharObj();

		// アクション抹杀
		DeathAllAction();

		// ＢＧＭストップ
//		fade_out_bgm();

#ifdef _DEBUG
		if( !offlineFlag )
		{
			SubProcNo++;
		}
		else
		{
	#ifdef PUK3_BATTLECHECK_SERVER
			char szMoji[256];
			GetWindowText( hWnd, szMoji, 256 );
			// ウィンドウタイトルに"battlecheck"の文字列があったら、サーバ选择画面に返回
			if ( strstr( szMoji, "battlecheck" ) ) ChangeProc2( PROC_TITLE_MENU ); //MLHIDE
			// 无ければ、ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			else PostMessage( hWnd, WM_CLOSE, 0, 0L );
			offlineFlag = FALSE;
	#else
			// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			PostMessage( hWnd, WM_CLOSE, 0, 0L );
	#endif
		}
#else
		SubProcNo++;
#endif
	}

	if( SubProcNo == 1 )
	{
		initCharLogout();
		SubProcNo++;
	}
	else
	if( SubProcNo == 2 )
	{
		ret = charLogout();
		// 登出完了
		if( ret == 1 )
		{
			// ネットワーク初期化
			cleanupNetwork();
			// アクション抹杀
			DeathAllAction();
			ChangeProc( PROC_TITLE_MENU );
		}
		else
#ifdef PUK2
		// 强制登出完了
		if( ret == 2 )
		{
			SubProcNo = 200;
	#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, INFOMSG_24 );
	#else
			strcpy( msg,
				"为了维护良好的游戏环境\n"                                                   //MLHIDE
				"需要进行一时的停机维护。\n"                                                  //MLHIDE
				"请等待服务器再开放。" );                                                   //MLHIDE
	#endif
			// フラグ初期化
			charLogoutStatus = 0;
		}
		else
#endif
		// タイムアウト
		if( ret == -1 )
		{
			// ネットワーク初期化
			cleanupNetwork();
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_115 );
#else
			strcpy( msg, NET_ERRMSG_LOGOUTTIMEOUT );
#endif
		}
		else
		// エラー
		if( ret == -2 )
		{
			// ネットワーク初期化
			cleanupNetwork();
			SubProcNo = 100;
			strcpy( msg, ML_STRING(311, "登出失败。") );
		}
	}

	// エラー表示
	if( SubProcNo == 100 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 101 )
	{
		if( commonMsgWin( msg ) )
		{
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );	// タイトルへ
		}
	}
#ifdef PUK2
	// エラー表示
	if( SubProcNo == 200 )
	{
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 201 )
	{
		if( commonMsgWin( msg ) )
		{
			// ネットワーク初期化
			cleanupNetwork();
			// アクション抹杀
			DeathAllAction();
			ChangeProc( PROC_TITLE_MENU );
		}
	}
#endif
}


static int charLogoutProcNo;

// 登出处理初期化
void initCharLogout( void )
{
	charLogoutProcNo = 0;
}


// 登出处理
//  戾り值：	 0 ... 登出中
//				 1 ... ログイアウト完了
#ifdef PUK2
//				 2 ... 强制登出完了
#endif
//				-1 ... タイムアウト
//				-2 ... エラー
int charLogout( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	int ret = 0;
	static char* msg = ML_STRING(312, "登出中");

	// 初期化
	if( charLogoutProcNo == 0 )
	{
		charLogoutProcNo = 1;

		// ウィンドウ作成
		w = strlen( msg )*9/64+2;
		h = (16+47)/48;
		if( h < 2 )
			h = 2;
		x = (DEF_APPSIZEX-w*64)/2;
		y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
	}

	// キャラログイン开始
	if( charLogoutProcNo == 1 )
	{
#ifdef PUK2
		if ( charLogoutStatus != 2 ) charLogoutStart();
#else
		charLogoutStart();
#endif
		charLogoutProcNo++;
	}
	else
	if( charLogoutProcNo == 2 )
	{
		ret = charLogoutProc();
	}

	if( ret != 0 )
	{
		if( ptActMenuWin )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
	}

	if( ptActMenuWin != NULL )
	{
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 )
		{
			int len;
			int xx, yy;

			len = strlen( msg );
			xx = (w*64-len*8)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_PAL_WHITE, msg, 0 );
		}
	}

	return ret;
}


// 登出演出
int produce_logout()
{
	switch(produce_vct_no){
	case 0:
		// バトルサーフェスの画像作成
		CopyBackBuffer();
		// バックバッファー描画方法变更
		BackBufferDrawType = DRAW_BACK_PRODUCE;
		// ＢＧＭフェードアウト开始
		fade_out_bgm();
		// 现在の时间を记忆
		NowTime = GetTickCount();
#ifdef PUK2_FPS
		NowDrawTime = NowTime;
#endif
		// 演出フラグ初期化
		ProduceInitFlag = TRUE;
		produce_vct_no++;
		// メニュー初期化
		initMenu();
#ifdef PUK3_VEHICLE
		// 现存の乘り物を全て削除
		delVehicleAll();
#endif
		break;
	case 1:
		// 演出中
#ifdef PUK2
		if( DrawProduce( PRODUCE_UP_DOWN_LINE_ACCELE, 8, 2.0f ) == TRUE ){
#else
		if( DrawProduce( PRODUCE_UP_DOWN_LINE_ACCELE ) == TRUE ){
#endif
		//if( DrawProduce( PRODUCE_BRAN_SMALL ) == TRUE ){
			BackBufferDrawType = DRAW_BACK_NORMAL;
			// デフォルトパレットに戾す
			PaletteChange( DEF_PAL, 0 );
			return 1;		//演出終了
		}
		break;
	}
	return 0;		//演出中
}

#ifdef PUK2

struct BaseToFaceTbl{
	int BaseGraNo;
	int BaseFaceNo[3];
};

struct BaseToFaceTbl BtFtbl[]={
	// XG1
	{SPR_000em, CG_FACE_0, RN_FACE_0, PUK2_FACE_01},
	{SPR_010em, CG_FACE_1, RN_FACE_1, PUK2_FACE_02},
	{SPR_020em, CG_FACE_2, RN_FACE_2, PUK2_FACE_03},
	{SPR_030em, CG_FACE_3, RN_FACE_3, PUK2_FACE_04},
	{SPR_040em, CG_FACE_4, RN_FACE_4, PUK2_FACE_05},
	{SPR_050em, CG_FACE_5, RN_FACE_5, PUK2_FACE_06},
	{SPR_060em, CG_FACE_6, RN_FACE_6, PUK2_FACE_07},
	{SPR_200em, CG_FACE_10, RN_FACE_7, PUK2_FACE_08},
	{SPR_210em, CG_FACE_11, RN_FACE_8, PUK2_FACE_09},
	{SPR_220em, CG_FACE_12, RN_FACE_9, PUK2_FACE_10},
	{SPR_230em, CG_FACE_13, RN_FACE_10, PUK2_FACE_11},
	{SPR_240em, CG_FACE_14, RN_FACE_11, PUK2_FACE_12},
	{SPR_250em, CG_FACE_15, RN_FACE_12, PUK2_FACE_13},
	{SPR_260em, CG_FACE_16, RN_FACE_13, PUK2_FACE_14},
	// XG1Renewal
	{SPR_400em, CG_FACE_0, RN_FACE_0, PUK2_FACE_01},
	{SPR_410em, CG_FACE_1, RN_FACE_1, PUK2_FACE_02},
	{SPR_420em, CG_FACE_2, RN_FACE_2, PUK2_FACE_03},
	{SPR_430em, CG_FACE_3, RN_FACE_3, PUK2_FACE_04},
	{SPR_440em, CG_FACE_4, RN_FACE_4, PUK2_FACE_05},
	{SPR_450em, CG_FACE_5, RN_FACE_5, PUK2_FACE_06},
	{SPR_460em, CG_FACE_6, RN_FACE_6, PUK2_FACE_07},
	{SPR_500em, CG_FACE_10, RN_FACE_7, PUK2_FACE_08},
	{SPR_510em, CG_FACE_11, RN_FACE_8, PUK2_FACE_09},
	{SPR_520em, CG_FACE_12, RN_FACE_9, PUK2_FACE_10},
	{SPR_530em, CG_FACE_13, RN_FACE_10, PUK2_FACE_11},
	{SPR_540em, CG_FACE_14, RN_FACE_11, PUK2_FACE_12},
	{SPR_550em, CG_FACE_15, RN_FACE_12, PUK2_FACE_13},
	{SPR_560em, CG_FACE_16, RN_FACE_13, PUK2_FACE_14},
	// XG2
	{SPR_600em, V2_FACE_0, V2_FACE_0, PUK2_FACE_15},
	{SPR_610em, V2_FACE_1, V2_FACE_1, PUK2_FACE_16},
	{SPR_620em, V2_FACE_2, V2_FACE_2, PUK2_FACE_17},
	{SPR_630em, V2_FACE_3, V2_FACE_3, PUK2_FACE_18},
	{SPR_640em, V2_FACE_4, V2_FACE_4, PUK2_FACE_19},
	{SPR_650em, V2_FACE_5, V2_FACE_5, PUK2_FACE_20},
	{SPR_660em, V2_FACE_6, V2_FACE_6, PUK2_FACE_21},
	{SPR_700em, V2_FACE_7, V2_FACE_7, PUK2_FACE_22},
	{SPR_710em, V2_FACE_8, V2_FACE_8, PUK2_FACE_23},
	{SPR_720em, V2_FACE_9, V2_FACE_9, PUK2_FACE_24},
	{SPR_730em, V2_FACE_10, V2_FACE_10, PUK2_FACE_25},
	{SPR_740em, V2_FACE_11, V2_FACE_11, PUK2_FACE_26},
	{SPR_750em, V2_FACE_12, V2_FACE_12, PUK2_FACE_27},
	{SPR_760em, V2_FACE_13, V2_FACE_13, PUK2_FACE_28},
};


void pActSelGra_init( int index )
{
	int pageno;
	int j;
	int face;
	int charno;

	if( pActSelGra[index] != NULL ){
		DeathAction( pActSelGra[index] );
		pActSelGra[index] = NULL;
	}
	if ( !existCharacterListEntry( index ) ) return;

	// キャラのアクション作成
	if( pActSelGra[index] == NULL )
	{
		if (chartable[index].faceGraNo<30000) chartable[index].faceGraNo=CG2_FACE_SHADOW_PCM;
		face = getReFaceGraphic( chartable[index].faceGraNo );
		face = getFaceGraphicToPUK2(face);

		if (chartable[index].isRenewal<0){
			if (PUK2_FACE_01<=face && face<PUK2_FACE_15){
				chartable[index].isRenewal=1;
			}else{
				chartable[index].isRenewal=2;
			}
		}else{
			if (face>=PUK2_FACE_15){
				chartable[index].isRenewal=2;
			}
		}
		j=chartable[index].isRenewal;

		pageno=PUK2_GRANO_NEW;
		switch(j){
		case 0:
			pageno=PUK2_GRANO_OLD;
			break;
		case 1:
			pageno=PUK2_GRANO_OLD;
			break;
		case 2:
			pageno=PUK2_GRANO_NEW;
			break;
		}
		pActSelGra[index] = GetAction( PRIO_CHR, 0 );
		if( pActSelGra[index] != NULL )
		{
			if (face>=PUK2_FACE_15) charno=(face-PUK2_FACE_15)/100;
			else charno=(face-PUK2_FACE_01)/100;

			if (chartable[index].faceGraNo==CG2_FACE_SHADOW_PCM) pActSelGra[index]->anim_chr_no = SPR_shadow_pcm;
			else if (chartable[index].faceGraNo==CG2_FACE_SHADOW_PCF) pActSelGra[index]->anim_chr_no = SPR_shadow_pcf;
			else{
				if (j==0) pActSelGra[index]->anim_chr_no = aSelGraNoTblV2[pageno].pSelectSubGraNoTbl[charno];
				else pActSelGra[index]->anim_chr_no = aSelGraNoTblV2[pageno].pSelectGraNoTbl[charno];

				pActSelGra[index]->anim_chr_no += ( (face%100)/25 ) * 6;
				if (pageno==PUK2_GRANO_OLD) pActSelGra[index]->atr = ACT_ATR_HIT_BOX;
			}

			pActSelGra[index]->anim_no = ANIM_STAND;
			pActSelGra[index]->dispPrio = DISP_PRIO_CHAR;
			pActSelGra[index]->x = (index?463:170);
			pActSelGra[index]->y = 350;
			pActSelGra[index]->anim_ang = 5;
			pattern( pActSelGra[index], ANM_NOMAL_SPD, ANM_LOOP );
		}
	}
}
#endif
#ifdef PUK2
#include "../puk2/interface/loginTitle.cpp"
#include "../puk2/interface/loginCharaSel.cpp"
#endif
#ifdef PUK2_NEW_MENU
	#include "../puk2/interface/charamake_PUK2.cpp"
#endif
#ifdef PUK2_SERVERCHANGE
	#include "../puk2/interface/newlogin.cpp"
#endif

#ifdef PUK3
#include "../puk3/interface/loginTitlePuk3.cpp"
#endif

