﻿#include<stdio.h>

#include"../systeminc/system.h"
#include"../systeminc/action.h"
#include"../systeminc/pc.h"
#include"../systeminc/character.h"
#include"../systeminc/map.h"
#include"../systeminc/tool.h"
#include"../systeminc/anim_tbl.h"
#include"../systeminc/loadsprbin.h"
#include"../systeminc/netmain.h"
#include"../systeminc/nrproto_cli.h"
#include"../systeminc/savedata.h"
#include"../systeminc/menu.h"
#include"../systeminc/field.h"
#include"../systeminc/login.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/math2.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/pattern.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/mouse.h"
#include"../systeminc/mapGridCursol.h"

#ifdef _CG2_NEWGRAPHIC
#include"../systeminc/anim_tbl2.h"
#endif
#ifdef PUK2
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"
	#include "../systeminc/guild.h"
#define DELIMITER	'|'					// 区切り文字
#endif
#ifdef PUK3
	#include "../PUK3/anim_tbl_PUK3.h"
	#include "../PUK3/profile/profile.h"
	#include "../PUK3/account/account.h"
#endif

PC pc;					// プレイヤーキャラデータ
JOB_INFO job;			// 职业情报
CHAR_TITLE_INFO charTitleInfo[CHAR_TITLE_MAX];		// 称号情报
CHAR_TITLE_INFO sortCharTitleInfo[CHAR_TITLE_MAX];	// 称号情报（ソート后の结果）
short maxPcNo;			// キャラリストに登録されてるキャラの数
short selectPcNo;		// キャラ选择时に选择されたキャラリストの番号
short prSendMode = 0;	// ＰＲ送信モード 0 ... 除队要求 / 1 ... 仲间要求
short prSendFlag = 0;	// ＰＲプロトコル 1 ... 送信

short jbSendFlag;		// 参战プロトコル送信
short duelSendFlag;		// デュエル处理用
//short duelFlag;

int loginDp;			// pc初期化时に参照するデュエルポイント

short helpFlag = 0;		// 战闘中のヘルプボタンの状态

// ペットデータ
PET pet[MAX_PET];		// ペット情报
PET_SORT sortPet[MAX_PET];	// ペットソート情报
int petCnt;				// PCがつれているペットの数

// 仲间情报
PARTY party[MAX_PARTY];
short partyModeFlag = 0;	// 现在布ティモードか


// キャラリスト用
CHARLISTTABLE chartable[MAXCHARACTER];

// 战闘结果信息
BATTLE_RESULT_MSG battleResultMsg;

// アドレスブック
ADDRESS_BOOK_INFO addressBook[ADDRESS_BOOK];
ADDRESS_BOOK_SORT_TBL addressBookSortTbl[ADDRESS_BOOK];

MAIL_HISTORY mailHistory[MAXCHARACTER][ADDRESS_BOOK];
#ifdef PUK2
GUILD_MAIL_HISTORY mailGuildHistory[MAXCHARACTER][GUILD_MEMBER_MAX];
#endif


// 新キャラ作成
char newCharacterName[CHAR_NAME_LEN+1];	// 新キャラの名称
int newCharacterGraNo;				// 新キャラの画像番号
int newCharacterFaceGraNo;			// 新キャラの颜画像番号
int newCharacterVit;				// 新キャラの体力
int newCharacterStr;				// 新キャラの腕力
int newCharacterTgh;				// 新キャラの防御力
int newCharacterQui;				// 新キャラの敏捷力
int newCharacterMgc;				// 新キャラの魔法力
int newCharacterEarth;				// 新キャラの地属性
int newCharacterWater;				// 新キャラの水属性
int newCharacterFire;				// 新キャラの火属性
int newCharacterWind;				// 新キャラの风属性
int newCharacterHomeTown;			// 新キャラの出身地




void swapInteger( int *, int * );
void swapShort( short *, short * );
void swapString( char *, char * );

// アルバム
MONSTER_ALBUM album;
#define ALBUM_FILE_NAME	"album.dat"


#ifdef _DEBUG
int sortOffFlag = 0;
#endif


//とりあえずパッケージバージョン
#ifdef _CG2_NEWGRAPHIC
BYTE CG2PackageVer = 255;
#endif

//-------------------------------------------------------------------------//
// ペットのソート                                                          //
//-------------------------------------------------------------------------//
// 引　数: pet     ... ペット构造体
//         sortPet ... ソート构造体へのポインタ
//         cnt     ... ソートする个数
void petSort( PET *pet, PET_SORT *sortPet, int cnt )
{
	int i;

	// 必要なデータのコピー
	for( i = 0; i < cnt; i++ )
	{
		sortPet[i].useFlag = pet[i].useFlag;
		sortPet[i].index = i;
#ifdef PUK2_NOPETSORT
		sortPet[i].id = pet[i].hisId;
#else
#ifndef _DEBUG
		sortPet[i].id = pet[i].hisId;
#else
		if( !sortOffFlag )
		{
			sortPet[i].id = pet[i].hisId;
		}
		else
		{
			sortPet[i].id = i;
		}
#endif
#endif
	}

#ifdef PUK2_NOPETSORT
#else
	//クイックソート
	qsort(	sortPet,						// 构造体のアドレス
			cnt,							// 比较する个数
			sizeof( PET_SORT ), 			// 构造体のサイズ
			petSortFunc 					// 比较关数へのポインタ
	);
#endif
}

// ペットをソートする条件
int petSortFunc( const void *_pt1, const void *_pt2 )
{
	PET_SORT *pt1 = (PET_SORT *)_pt1;
	PET_SORT *pt2 = (PET_SORT *)_pt2;

	// pt2にデータが入ってないので何もしない
	if( !pt2->useFlag )
	{
		return -1;
	}
	else
	// pt1にデータが入ってないので何もしない
	if( !pt1->useFlag )
	{
		return 1;
	}
	else
	// pt1の履历番号が大きければ入れ替え
	if( pt1->id > pt2->id )
	{
		return 1;
	}

	return -1;
}




//-------------------------------------------------------------------------//
// ペットのスキル（テック）のソート                                        //
//-------------------------------------------------------------------------//
// 引　数: tech     ... スキル（テック）构造体へのポインタ
//         sortTech ... ソート情报を格纳するポインタ
//         cnt      ... ソートする个数
void petTechSort( PET_TECH_INFO *tech, PET_TECH_SORT_INFO *sortTech, int cnt )
{
	int i;

	// 必要なデータのコピー
	for( i = 0; i < cnt; i++ )
	{
		sortTech[i].useFlag = tech[i].useFlag;
		sortTech[i].index = i;
#ifndef _DEBUG
		sortTech[i].id = tech[i].hisId;
#else
		if( !sortOffFlag )
		{
			sortTech[i].id = tech[i].hisId;
		}
		else
		{
			sortTech[i].id = i;
		}
#endif
	}

#if 0	//2001/07/30 oft
	//クイックソート
	qsort(	sortTech,						// 构造体のアドレス
			cnt,							// 比较する个数
			sizeof( PET_TECH_SORT_INFO ), 	// 构造体のサイズ
			petTechSortFunc 				// 比较关数へのポインタ
	);
#endif
}

// スキルをソートする条件
int petTechSortFunc( const void *_pt1, const void *_pt2 )
{
	PET_TECH_SORT_INFO *pt1 = (PET_TECH_SORT_INFO *)_pt1;
	PET_TECH_SORT_INFO *pt2 = (PET_TECH_SORT_INFO *)_pt2;

	// pt2にデータが入ってないので何もしない
	if( !pt2->useFlag )
	{
		return -1;
	}
	else
	// pt1にデータが入ってないので何もしない
	if( !pt1->useFlag )
	{
		return 1;
	}
	else
	// pt1の履历番号が大きければ入れ替え
	if( pt1->id > pt2->id )
	{
		return 1;
	}

	return -1;
}




//-------------------------------------------------------------------------//
// スキルのソート                                                          //
//-------------------------------------------------------------------------//
// 引　数: skill     ... スキル构造体へのポインタ
//         sortSkill ... ソート情报を格纳するポインタ
//         cnt       ... ソートする个数
void skillSort( SKILL_INFO *skill, SKILL_SORT_INFO *sortSkill, int cnt )
{
	int i;

	// 必要なデータのコピー
	for( i = 0; i < cnt; i++ )
	{
		if( skill[i].name[0] != '\0' )
		{
			sortSkill[i].useFlag = 1;
		}
		else
		{
			sortSkill[i].useFlag = 0;
		}
		sortSkill[i].index = i;
#ifndef _DEBUG
		sortSkill[i].id = skill[i].hisId;
#else
		if( !sortOffFlag )
		{
			sortSkill[i].id = skill[i].hisId;
		}
		else
		{
			sortSkill[i].id = i;
		}
#endif
	}

	//クイックソート
	qsort(	sortSkill,					// 构造体のアドレス
			cnt,						// 比较する个数
			sizeof( SKILL_SORT_INFO ), 	// 构造体のサイズ
			skillSortFunc 				// 比较关数へのポインタ
	);
}

// スキルをソートする条件
int skillSortFunc( const void *_pt1, const void *_pt2 )
{
	SKILL_SORT_INFO *pt1 = (SKILL_SORT_INFO *)_pt1;
	SKILL_SORT_INFO *pt2 = (SKILL_SORT_INFO *)_pt2;

	// pt2にデータが入ってないので何もしない
	if( !pt2->useFlag )
	{
		return -1;
	}
	else
	// pt2にデータがありpt1にデータが入ってないなら入れ替え
	if( !pt1->useFlag )
	{
		return 1;
	}
	else
	// pt1の履历番号が大きければ入れ替え
	if( pt1->id > pt2->id )
	{
		return 1;
	}

	return -1;
}



//-------------------------------------------------------------------------//
// アルバムファイル书き込み                                                //
//-------------------------------------------------------------------------//
void writeAlbumFile( void )
{
	MONSTER_ALBUM writeAlbum;
	FILE *fp;
	char writebuffer[sizeof(album)*2];
	char szAlbumDataFile[128];
	int writebufferlen;

	sprintf( szAlbumDataFile, "%s\\%s", dataFolder, ALBUM_FILE_NAME );   //MLHIDE
	// ファイルを开く
	if( (fp = fopen( szAlbumDataFile, "wb" )) == NULL )                  //MLHIDE
	{
		return;
	}

	writeAlbum = album;

	// エンコード
	jEncode( (char *)(&writeAlbum), sizeof( writeAlbum ), 0,
		writebuffer, &writebufferlen, sizeof( writebuffer ) );

	if( fwrite( writebuffer, 1, writebufferlen, fp ) < (unsigned int)writebufferlen )
	{
		return;
	}
	fclose( fp );
}


//-------------------------------------------------------------------------//
// アルバムファイル読み込み                                                //
//-------------------------------------------------------------------------//
void readAlbumFile( void )
{
//	MONSTER_ALBUM readAlbum;	// decodedbuffer に展开する。
	FILE *fp;
	char readbuffer[sizeof(album)*2];
	char decodedbuffer[sizeof(album)*2];
	char szAlbumDataFile[128];
	int readbufferlen;
	int albumLen;

	sprintf( szAlbumDataFile, "%s\\%s", dataFolder, ALBUM_FILE_NAME );   //MLHIDE

	// ファイルを开く
	if( (fp = fopen( szAlbumDataFile, "rb" )) == NULL )                  //MLHIDE
	{
		// 开けない时はファイルが无いかも知れないので作る
		writeAlbumFile();
		return;
	}

	// セーブデータ読み込み
	readbufferlen = fread( readbuffer, 1, sizeof( readbuffer ), fp );
	if( ferror( fp ) ){
		// 読み込みエラー
		fclose( fp );
		return;
	}
	fclose( fp );
/*
	// アルバムサイズより大きかったら作り直し
	if( readbufferlen > sizeof( MONSTER_ALBUM ) ){
		// 坏れてるかも知れないので作り直す
		writeAlbumFile();
		return;
	}
*/
	// 违うバッファにデコード
//	jDecode( readbuffer, readbufferlen, 0, (char *)(&readAlbum), &albumLen );
	jDecode( readbuffer, readbufferlen, 0, decodedbuffer, &albumLen );

	// ファイルサイズのチェック
	if( albumLen != sizeof( album ) )
	{
		// 坏れてるかも知れないので作り直す
		writeAlbumFile();
		return;
	}

	// 正しいアルバムバッファにコピー
	memcpy( (&album), decodedbuffer, sizeof(MONSTER_ALBUM) );

//	album = readAlbum;
}


//-------------------------------------------------------------------------//
// アルバム情报を记忆                                                      //
//-------------------------------------------------------------------------//
void setAlbum( int albumNo, int graNo, int seal, int rare, int tribe,
		int vit, int str, int tgh, int qui, int mgc,
		int earth, int water, int fire, int wind, int maxTech, char *memo, char *name )
{
	int no;
	int i;
	char newFlag = 0;

	if( albumNo < 1 || ALBUM_MAX < albumNo )
		return;

	no = albumNo - 1;

	if( !album.status[selectPcNo][no].useFlag )
	{
		newFlag = 1;
	}

	album.status[selectPcNo][no].useFlag = 1;
	album.status[selectPcNo][no].no      = albumNo;
	album.status[selectPcNo][no].graNo   = graNo;
	album.status[selectPcNo][no].seal    = seal;
	album.status[selectPcNo][no].rare    = rare;
	album.status[selectPcNo][no].tribe   = tribe;
	album.status[selectPcNo][no].vit     = vit;
	album.status[selectPcNo][no].str     = str;
	album.status[selectPcNo][no].tgh     = tgh;
	album.status[selectPcNo][no].qui     = qui;
	album.status[selectPcNo][no].mgc     = mgc;
	album.status[selectPcNo][no].attr[0] = earth;
	album.status[selectPcNo][no].attr[1] = water;
	album.status[selectPcNo][no].attr[2] = fire;
	album.status[selectPcNo][no].attr[3] = wind;
	album.status[selectPcNo][no].maxTech = maxTech;

	makeRecvString( memo );
	for( i = 0; i < 2; i++ )
	{
		if( !getMemoLine( album.status[selectPcNo][no].memo[i],
				sizeof( album.status[0][0].memo[0] ), memo, i, 28 ) )
		{
			album.status[selectPcNo][no].memo[i][0] = '\0';
		}
	}
	makeRecvString( name );
	if( strlen( name ) <= PET_NAME_LEN )
	{
		strcpy( album.status[selectPcNo][no].name, name );
	}
	else
	{
		strcpy( album.status[selectPcNo][no].name, "???" );                 //MLHIDE
	}

	album.status[selectPcNo][no].newFlag = newFlag;
	
	// 每回サーバーに要求しないように确实に保存する。
	writeAlbumFile();
}


//-------------------------------------------------------------------------//
// 新规に覚えたアルバム情报                                                //
//-------------------------------------------------------------------------//
void checkAlbumBit( int version, char *data )
{
	int i, j, k, l;
#ifdef PUK2
	int loop;
#endif
	unsigned int mask;
	unsigned int val;

	j = 1;
	l = 0;
#ifdef PUK2
	loop=ALBUM_MAX>>5;
	if (ALBUM_MAX&32) loop++;
	for( i = 0; i < loop; i++ )
#else
	for( i = 0; i < 7; i++ )	// 210体/32ビット= 6.5625 = 7ループ
#endif
	{
		if( l >= ALBUM_MAX )break;
		if( getIntegerToken( data, '|', j++, (int *)&val ) > 1 )	
			break;

		mask = 1;
		for( k = 0; k < 32; k++, mask <<= 1 )
		{
			if( l >= ALBUM_MAX )break;
			if( (val & mask) )
			{
				if( album.status[selectPcNo][l].useFlag == 0
				 || album.version[selectPcNo] < version )
				{
					// アルバム情报がないので要求
					nrproto_ALI_send( sockfd, l+1 );
				}
			}else{
				// ２アカ对策、サーバーウソつかない。
				// クライアントキャッシュは残すが、メモリだけ变更して表示しないようにする。
				album.status[selectPcNo][l].useFlag = 0;
			}
			l++;
		}
	}
	album.version[selectPcNo] = version;
}


//-------------------------------------------------------------------------//
// アルバムの削除                                                          //
//-------------------------------------------------------------------------//
void delAlbum( void )
{
	album.version[selectPcNo] = 0;
	memset( &album.status[selectPcNo], 0, sizeof( album.status[0] ) );
	// 确实に保存する
	writeAlbumFile();
}


//-------------------------------------------------------------------------//
// アルバムの初期化                                                        //
//-------------------------------------------------------------------------//
void initAlbum( void )
{
}


//-------------------------------------------------------------------------//
// アルバムの初期化（起动时に一度だけ实行）                                //
//-------------------------------------------------------------------------//
void initAlbumStartUp( void )
{
	memset( &album, 0, sizeof( album ) );
	strcpy( album.fileId, "CCA" );                                       //MLHIDE
}




//-------------------------------------------------------------------------//
// アイテム栏に残り空き数を返す                                            //
//-------------------------------------------------------------------------//
int getItemEmpty( void )
{
	int i;
	int empty;

	empty = 0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ )
	{
		if( pc.item[MAX_EQUIP_ITEM+i].useFlag == 0 )
		{
			empty++;
		}
	}

	return empty;
}


//-------------------------------------------------------------------------//
// メール关连初期化                                                        //
//-------------------------------------------------------------------------//
void initMail( void )
{
	memset( &mailHistory, 0, sizeof( mailHistory ) );
	addressBookNewPage = 0;
#ifdef PUK2
	memset( &mailGuildHistory, 0, sizeof( mailGuildHistory ) );
#endif
}

//-------------------------------------------------------------------------//
// メールファイル読み込み                                                  //
//-------------------------------------------------------------------------//
void readMailFile( void )
{
	FILE *fp;
	char szMaildatFile[128];

#ifdef PUK3_ACCOUNT

	sprintf( szMaildatFile,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],MAIL_FILE_NAME); //MLHIDE
	if( (fp = fopen( szMaildatFile, "rb" )) == NULL ){                   //MLHIDE
		writeMailFile();
		return;
	}
	fread( &mailHistory, 1, sizeof( mailHistory ), fp );
	fclose( fp );


	sprintf( szMaildatFile,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],GUILDMAIL_FILE_NAME); //MLHIDE
	if( (fp = fopen( szMaildatFile, "rb" )) == NULL ){                   //MLHIDE
		writeGuildMailFile();
		return;
	}
	fread( &mailGuildHistory, 1, sizeof( mailGuildHistory ), fp );
	fclose( fp );

#else
	sprintf( szMaildatFile, "%s\\%s", dataFolder, MAIL_FILE_NAME );      //MLHIDE
	if( (fp = fopen( szMaildatFile, "rb" )) == NULL ){                   //MLHIDE
		writeMailFile();
		return;
	}

	fread( &mailHistory, 1, sizeof( mailHistory ), fp );

	fclose( fp );

#ifdef PUK2
	sprintf( szMaildatFile, "%s\\%s", dataFolder, GUILDMAIL_FILE_NAME ); //MLHIDE
	if( (fp = fopen( szMaildatFile, "rb" )) == NULL ){                   //MLHIDE
		writeGuildMailFile();
		return;
	}

	fread( &mailGuildHistory, 1, sizeof( mailGuildHistory ), fp );

	fclose( fp );
#endif

#endif
}

//-------------------------------------------------------------------------//
// 未読チェック                                                            //
//-------------------------------------------------------------------------//
int checkNoReadMail( void )
{
	int i, j;
	int historyNum;

	for( i = 0; i < ADDRESS_BOOK; i++ ){
		if( addressBook[addressBookSortTbl[i].index].useFlag ){
			for( j = 0; j < MAIL_HISTORY_CNT; j++ ){
				if( mailHistory[selectPcNo][addressBookSortTbl[i].index].mailInfo[j].readFlag ){
					mailLamp = 1;
					addressBookNewPage = i / ADDRESS_BOOK_LINE;
					return 1;
				}
			}
		}
	}
#ifdef PUK2
	for( i = 0; i < GUILD_MEMBER_MAX; i++ ){
		if(guildBook.member[i].address.useFlag){
			historyNum= getGuildMailHistoryNumFromID(guildBook.member[i].address.id);		
			if(historyNum!=-1){
				if( mailGuildHistory[selectPcNo][historyNum].useFlag ){
					for( j = 0; j < MAIL_HISTORY_CNT; j++ ){
						if( mailGuildHistory[selectPcNo][historyNum].mail.mailInfo[j].readFlag ){
							mailLamp = 2;
							addressBookNewPage = i / ADDRESS_BOOK_LINE;
							return 1;
						}
					}
				}
			}
		}
	}
#endif

#ifdef PUK3_PROF
	for( i = 0; i < MINI_MAIL_STOCK_MAX; i++ ){
		if( miniMailBook[i].useFlag ){
			for( j = 0; j < MAIL_HISTORY_CNT; j++ ){
				if( miniMailHistory[i].mailInfo[(MAIL_HISTORY_CNT-1)-j].readFlag ){
					mailLamp = 3;
					addressBookNewPage = i / ADDRESS_BOOK_LINE;
					return 1;
				}
			}
		}
	}
#endif

	mailLamp = 0;
	addressBookNewPage = addressBookPage;
	return 0;
}

#ifdef PUK2
//-------------------------------------------------------------------------//
// 未読取得		                                                            //
//-------------------------------------------------------------------------//
void GetNoReadMail( int *MailType,int *MailNo )
{
	int i, j,historyNum;

	// 通常メールチェック
	for( i = 0; i < ADDRESS_BOOK; i++ )
	{
		if( addressBook[addressBookSortTbl[i].index].useFlag )
		{
			for( j = 0; j < MAIL_HISTORY_CNT; j++ )
			{
				if( mailHistory[selectPcNo][addressBookSortTbl[i].index].mailInfo[j].readFlag )
				{
					*MailType=0;
					*MailNo=addressBookSortTbl[i].index;
					return;
				}
			}
		}
	}

	// 家族メールチェック
	for( i = 0; i < GUILD_MEMBER_MAX; i++ ){
		if(guildBook.member[i].address.useFlag){
			historyNum= getGuildMailHistoryNumFromID(guildBook.member[i].address.id);		
			if(historyNum!=-1){
				if( mailGuildHistory[selectPcNo][historyNum].useFlag){
					for( j = 0; j < MAIL_HISTORY_CNT; j++ ){
						if( mailGuildHistory[selectPcNo][historyNum].mail.mailInfo[j].readFlag ){
							*MailType=1;
							*MailNo=getGuildNumFromID(mailGuildHistory[selectPcNo][historyNum].ID);
							return;
						}			
					}
				}	
			}
		}
	}

#ifdef PUK3_PROF
	// 通常メールチェック
	for( i = 0; i < MINI_MAIL_STOCK_MAX; i++ )
	{
		if( miniMailBook[i].useFlag )
		{
			for( j = 0; j < MAIL_HISTORY_CNT; j++ )
			{
				if( miniMailHistory[i].mailInfo[j].readFlag )
				{
					*MailType=2;
					*MailNo=i;
					return;
				}
			}
		}
	}
#endif

	*MailType=-1;
	*MailNo=-1;

}
#endif

//-------------------------------------------------------------------------//
// 未読チェック（指定のキャラだけ）（アドレス用）                             //
//-------------------------------------------------------------------------//
int checkNoReadMailOnce( int index )
{
	int i;

	if( index < 0 || ADDRESS_BOOK <= index )
		return 0;

	if( addressBook[index].useFlag == 0 )
		return 0;

	for( i = 0; i < MAIL_HISTORY_CNT; i++ )
	{
		if( mailHistory[selectPcNo][index].mailInfo[i].readFlag )
		{
			return 1;
		}
	}

	return 0;
}

#ifdef PUK2
//-------------------------------------------------------------------------//
// 未読チェック（指定のキャラだけ）（家族用）                                        //
//-------------------------------------------------------------------------//
int checkNoReadGuildMailOnce( int index )
{
	int i,memberIndex,historyIndex;

	memberIndex=index;
	historyIndex=getGuildMailHistoryNumFromID(guildBook.member[index].address.id);

	if(historyIndex==-1 || memberIndex==-1)
		return 0;

	if( guildBook.member[memberIndex].address.useFlag == 0 )
		return 0;

	for( i = 0; i < MAIL_HISTORY_CNT; i++ ){
		if( mailGuildHistory[selectPcNo][historyIndex].mail.mailInfo[i].readFlag ){
			return 1;
		}
	}

	return 0;
}
#endif

#ifdef PUK3_PROF
//-------------------------------------------------------------------------//
// 未読チェック（指定のキャラだけ）（アドレス用）                             //
//-------------------------------------------------------------------------//
int checkNoReadMiniMailOnce( int index )
{
	int i;

	if( index < 0 || MINI_MAIL_STOCK_MAX <= index )
		return 0;

	if( miniMailBook[index].useFlag == 0 )
		return 0;

	for( i = 0; i < MAIL_HISTORY_CNT; i++ )
	{
		if( miniMailHistory[index].mailInfo[i].readFlag )
		{
			return 1;
		}
	}

	return 0;
}
#endif

//-------------------------------------------------------------------------//
// メール内容をバッファに登録                                              //
//-------------------------------------------------------------------------//
void setMailHistory( int index, int sendFlag, char *header, char *buf )
{
	int i;

	// キャラがいないのにメール着たら无视
	if( addressBook[index].useFlag == 0 ){
		return;
	}

	// 古いデータをずらす
	for( i = MAIL_HISTORY_CNT-1; i > 0; i-- ){
		memcpy(
			&mailHistory[selectPcNo][index].mailInfo[i],
			&mailHistory[selectPcNo][index].mailInfo[i-1],
			sizeof( MAIL_INFO ) );
	}

	if( sendFlag == 0 ){
		// 受信データなら未読フラグを立てる
		mailHistory[selectPcNo][index].mailInfo[0].readFlag = 1;
	}else{
		// 送信データは既読フラグを立てる
		mailHistory[selectPcNo][index].mailInfo[0].readFlag = 0;
	}
	mailHistory[selectPcNo][index].mailInfo[0].sendFlag = sendFlag;
	strcpy( mailHistory[selectPcNo][index].mailInfo[0].header, header );
	strcpy( mailHistory[selectPcNo][index].mailInfo[0].buf, buf );
}

//-------------------------------------------------------------------------//
// ヒストリ削除(选择されているキャラだけ)                                  //
//-------------------------------------------------------------------------//
void delMailHistory( void )
{
	memset( &mailHistory[selectPcNo], 0, sizeof( mailHistory[0] ) );
}

//-------------------------------------------------------------------------//
// ヒストリ削除(选择キャラの特定の相手)
//-------------------------------------------------------------------------//
void delMailHistoryOnce( int index )
{
	memset( &mailHistory[selectPcNo][index], 0, sizeof( mailHistory[0][0] ) );
}

//-------------------------------------------------------------------------//
// メールファイル书き込み                                                  //
//-------------------------------------------------------------------------//
void writeMailFile( void )
{
	FILE *fp;
	char szMaildatFile[128];

#ifdef PUK3_ACCOUNT

	//アドレスメール书き込み
	sprintf( szMaildatFile,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],MAIL_FILE_NAME); //MLHIDE

	if( (fp = fopen( szMaildatFile, "wb" )) == NULL )                    //MLHIDE
	{
		return;
	}

	fwrite( &mailHistory, 1, sizeof( mailHistory ), fp );

	fclose( fp );


#else

	//アドレスメール书き込み
	sprintf( szMaildatFile, "%s\\%s", dataFolder, MAIL_FILE_NAME );      //MLHIDE

	if( (fp = fopen( szMaildatFile, "wb" )) == NULL )                    //MLHIDE
	{
		return;
	}

	fwrite( &mailHistory, 1, sizeof( mailHistory ), fp );

	fclose( fp );

#endif
}

//-------------------------------------------------------------------------//
// 家族メールの内容をバッファに登録                                      //
//-------------------------------------------------------------------------//
void setGuildMailHistory( int index, int sendFlag, char *header, char *buf )
{
	int i,checkInex,memIndex;

	//来たメールが家族员のものであるかチェック-------------------------------
	//IDを持つメンバー情报がメモリのどこにあるかサーチ
	checkInex=getGuildNumFromID(index);

	//见つからなかったのでリターン
	if(checkInex==-1)
		return;

	//送ってきた相手用の领域が决まっているかチェック---------------------------
	//IDを持つ家族メールバッファを探す
	memIndex=getGuildMailHistoryNumFromID(index);

	if(memIndex==-1){
		//新规のメールの场合

		//空いているバッファを探す
		for(i=0;i<GUILD_MEMBER_MAX;i++){
			if(mailGuildHistory[selectPcNo][i].useFlag==0){
				//使用するメモリ位置决定
				memIndex=i;
				mailGuildHistory[selectPcNo][i].ID=index;
				mailGuildHistory[selectPcNo][i].useFlag=1;
				break;
			}
		}

		if(memIndex==-1){
			//ありえないはずですが满杯のようです
			return;
		}
	}else{
		//既存のものに追加するメールの场合

		// 古いデータをずらす
		for( i = MAIL_HISTORY_CNT-1; i > 0; i-- ){
			memcpy(
				&mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[i],
				&mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[i-1],
				sizeof(MAIL_INFO) );
		}
	}

	//最新のものを追加
	if( sendFlag == 0 ){
		// 受信データなら未読フラグを立てる
		mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[0].readFlag = 1;
	}else{
		// 送信データは既読フラグを立てる
		mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[0].readFlag = 0;
	}

	mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[0].sendFlag = sendFlag;
	strcpy( mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[0].header, header );
	strcpy( mailGuildHistory[selectPcNo][memIndex].mail.mailInfo[0].buf, buf );

}

//-------------------------------------------------------------------------//
// 家族ヒストリ削除(选择されているキャラだけ)                                  //
//-------------------------------------------------------------------------//
void delGuildMailHistory( void )
{
	memset( &mailGuildHistory[selectPcNo], 0, sizeof( mailGuildHistory[0] ) );
}

//-------------------------------------------------------------------------//
// 家族ヒストリ削除(选择キャラの特定の相手)
//-------------------------------------------------------------------------//
void delGuildMailHistoryOnce( int index ){

	int memberIndex;
	
	memberIndex=getGuildMailHistoryNumFromID(index);

	if(memberIndex==-1)
		return;

	mailGuildHistory[selectPcNo][memberIndex].useFlag=0;
	mailGuildHistory[selectPcNo][memberIndex].ID=0;
	memset( &mailGuildHistory[selectPcNo][memberIndex], 0, sizeof( mailGuildHistory[0][0] ) );
}

//-------------------------------------------------------------------------//
// 家族メールファイル书き込み                                            //
//-------------------------------------------------------------------------//
void writeGuildMailFile( void )
{
	FILE *fp;
	char szMaildatFile[128];

#ifdef PUK3_ACCOUNT
	sprintf( szMaildatFile,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],GUILDMAIL_FILE_NAME); //MLHIDE
	if( (fp = fopen( szMaildatFile, "wb" )) == NULL )                    //MLHIDE
	{
		return;
	}

	fwrite( &mailGuildHistory, 1, sizeof( mailGuildHistory ), fp );

	fclose( fp );
#else
	sprintf( szMaildatFile,"%s\\%s",dataFolder,GUILDMAIL_FILE_NAME);     //MLHIDE
	if( (fp = fopen( szMaildatFile, "wb" )) == NULL )                    //MLHIDE
	{
		return;
	}

	fwrite( &mailGuildHistory, 1, sizeof( mailGuildHistory ), fp );

	fclose( fp );
#endif

}

//-------------------------------------------------------------------------//
// 家族成员IDから家族情报の位置をサーチ                               //
//-------------------------------------------------------------------------//
int getGuildNumFromID(int ID){

	int i,index;

	for(i=0;i<GUILD_MEMBER_MAX;i++){
		if(guildBook.member[i].address.useFlag==1){		
			if(guildBook.member[i].address.id==ID){
				index=i;
				return i;
			}
		}
	}

	return -1;

}

//-------------------------------------------------------------------------//
// 家族成员IDからヒストリーの位置をサーチ                               //
//-------------------------------------------------------------------------//
int getGuildMailHistoryNumFromID(int ID){

	int i,index;

	index=-1;
	for(i=0;i<GUILD_MEMBER_MAX;i++){
		if(mailGuildHistory[selectPcNo][i].useFlag==1){
			if(mailGuildHistory[selectPcNo][i].ID==ID){
				index=i;
			}
		}
	}

	return index;

}

//-------------------------------------------------------------------------//
// レシピのページ数の计算                                                  //
//-------------------------------------------------------------------------//
int getRecipeMaxPage( int index )
{
	int i;

	for( i = 0; i < RECIPE_MAX; i++ )
	{
		if( job.skill[index].recipe[i].id < 0 )
			break;
	}

	if( i == 0 )
	{
		return 1;
	}
	else
	{
		return (i-1)/RECIPE_LINE+1;
	}
}


//-------------------------------------------------------------------------//
// 怪我等级から对应の色番号に变换                                        //
//-------------------------------------------------------------------------//
int getHelthColor( int lv )
{
	if( lv == 0 )
	{
		return 0;
	}
	else
	if( lv < 26 )
	{
		return 1;
	}
	else
	if( lv < 51 )
	{
		return 2;
	}
	else
	if( lv < 76 )
	{
		return 3;
	}
	else
	{
		return 4;
	}
}


//-------------------------------------------------------------------------//
// アイテムの说明文を行に分解し格纳してページ数を求める                    //
//-------------------------------------------------------------------------//
// 引　数: itemNo ... アイテム格纳番号
//         memo   ... 说明文のポインタ
void setItemMemo( int itemNo, char *memo )
{
	int line;
	int i;


	line = -1;
	for( i = 0; i < ITEM_MEMO_LINE*ITEM_MEMO_PAGE; i++ )
	{
		if( getMemoLine( pc.item[itemNo].memo[i], sizeof( pc.item[0].memo ),
			memo, i, ITEM_MEMO_LINE_LEN ) )
		{
			line = i;
		}
		else
		{
			pc.item[itemNo].memo[i][0] = '\0';
		}
	}

	if( line >= 0 )
	{
		pc.item[itemNo].memoPage = line/ITEM_MEMO_LINE+1;
	}
	else
	{
		pc.item[itemNo].memoPage = 0;
	}
}


//-------------------------------------------------------------------------//
// 指定のＩＤの称号が格纳されている场所番号を取得                          //
//-------------------------------------------------------------------------//
// 引　数: id     ... 称号のＩＤ
// 戾り值: 0 以上 ... 格纳场所番号
//         0 未满 ... 称号が存在しない
int getNoCharTitle( int id )
{
	int i;

	if( id < 0 )
		return -1;

	for( i = 0; i < CHAR_TITLE_MAX; i++ )
	{
		if( charTitleInfo[i].id == id )
		{
			return i;
		}
	}

	return -1;
}


//-------------------------------------------------------------------------//
// 称号の数を计算し何ページあるか返す                                      //
//-------------------------------------------------------------------------//
int getMaxPageCharTitle( void )
{
	int i;
	int cnt;

	for( i = 0, cnt = 0; i < CHAR_TITLE_MAX; i++ )
	{
		if( charTitleInfo[i].id >= 0 )
		{
			cnt++;
		}
	}

	return cnt/CHAR_TITLE_LINE+1;
}


//-------------------------------------------------------------------------//
// 称号をコピーする                                                        //
//-------------------------------------------------------------------------//
void charTitleCopy( CHAR_TITLE_INFO *ptDis, CHAR_TITLE_INFO *ptSrc, int cnt )
{
	int i;

	for( i = 0; i < cnt; i++ )
	{
		ptDis[i] = ptSrc[i];
	}
}


//-------------------------------------------------------------------------//
// 称号をソートする                                                        //
//-------------------------------------------------------------------------//
void charTitleSort( CHAR_TITLE_INFO *pt, int cnt )
{
	//クイックソート
	qsort(	pt,							// 构造体のアドレス
			cnt,						// 比较する个数
			sizeof( CHAR_TITLE_INFO ), 	// 构造体のサイズ
			charTitleSortFunc 			// 比较关数へのポインタ
	);
}

// 称号をソートする条件
int charTitleSortFunc( const void *_pt1, const void *_pt2 )
{
	CHAR_TITLE_INFO *pt1 = (CHAR_TITLE_INFO *)_pt1;
	CHAR_TITLE_INFO *pt2 = (CHAR_TITLE_INFO *)_pt2;

	// pt2にデータが入ってないので何もしない
	if( pt2->id < 0 )
	{
		return -1;
	}
	else
	// pt1にデータがないなら入れ替える
	if( pt1->id < 0 )
	{
		return 1;
	}
	// pt1の履历番号が大きければ入れ替え
	if( pt1->id > pt2->id )
	{
		return 1;
	}

	return -1;
}




//-------------------------------------------------------------------------//
// ペットの数を数える                                                      //
//-------------------------------------------------------------------------//
int calcPetCnt( void )
{
	int i;

	petCnt = 0;
	for( i = 0; i < MAX_PET; i++ )
	{
		if( pet[i].useFlag )
			petCnt++;
	}

	return petCnt;
}




//-------------------------------------------------------------------------//
// レシピ情报クリア                                                        //
//-------------------------------------------------------------------------//
void clearRecipe( int no )
{
	int i, j;

#ifdef PUK2
	// トランスしてない状态で设定
	job.skill[no].trn = 0;
#endif
	for( i = 0; i < RECIPE_MAX; i++ )
	{
		job.skill[no].recipe[i].id = -1;
		for( j = 0; j < RECIPE_MATERIAL_MAX; j++ )
		{
			job.skill[no].recipe[i].material[j].id = -1;
		}
	}
}



//-------------------------------------------------------------------------//
// PCの情报领域のクリア                                                    //
//-------------------------------------------------------------------------//
void initPcAll( void )
{
	int i;

	memset( &pc, 0, sizeof( pc ) );
	memset( &pet, 0, sizeof( pet ) );
	memset( &party, 0, sizeof( party ) );
	memset( &job, 0, sizeof( job ) );
	memset( &addressBook, 0, sizeof( addressBook ) );
#ifdef PUK2_PROF
	memset( &miniMailBook, 0, sizeof( miniMailBook[MINI_MAIL_STOCK_MAX] ) );
#endif
	memset( &sortPet, 0, sizeof( sortPet ) );	// ソート情报も初期化する。

	getUserSetting( selectPcNo );
	getUserChatOption( selectPcNo );
	// マップＢＧＭ番号を保存バッファから取ってくる
	getUserMapBgmNo( selectPcNo );

	partyModeFlag = 0;

	for( i = 0; i < MAX_PARTY; i++ )
	{
		party[i].useFlag = 0;
		party[i].id = 0;
		party[i].ptAct = NULL;
	}


#if 1
	pc.faceGraNo = newCharacterFaceGraNo;
	pc.dp = loginDp;
#endif

	for( i = 0; i < CHAR_TITLE_MAX; i++ )
	{
		charTitleInfo[i].id = -1;
	}


	for( i = 0; i < MAX_SKILL; i++ )
	{
		clearRecipe( i );
	}
#ifdef PUK3_LOGIN_DIR_0
	// 方向を読み直すように
	pc.dir = -1;
#endif
}


// PC情报初期化
void initPc( void )
{
	int walk = 0, height = 0;
	int i;
#ifdef PUK2_NEW_MENU
	char str[256];
	char msg[256];
#endif

#ifndef _DEBUG
	createPc( pc.graNo, mapGx, mapGy, pc.dir );
#else
	if( offlineFlag )
	{
		createPc( SPR_020sd, mapGx, mapGy, pc.dir );
	}
	else
	{
		createPc( pc.graNo, mapGx, mapGy, pc.dir );
	}
#endif

	if( (pc.status & CHR_STATUS_W) )
	{
		walk = 1;
	}
	if( (pc.status & CHR_STATUS_H) )
	{
		height = 1;
	}
#ifdef PUK2_NEW_MENU
	makeSendString( pc.freeName, str, sizeof( str ) );
	strcpy( msg, str );
	strcat( msg, "|" );                                                  //MLHIDE
	makeSendString( pc.guildName, str, sizeof( str ) );
	strcat( msg, str );
	strcat( msg, "|" );                                                  //MLHIDE
	makeSendString( pc.guildTitle, str, sizeof( str ) );
	strcat( msg, str );
	setPcParam( pc.name, msg, pc.lv, pc.nameColor, walk, height );
#else
	setPcParam( pc.name, pc.freeName, pc.lv, pc.nameColor, walk, height );
#endif

	if( pc.ptAct != NULL )
	{
		pc.ptAct->hp = pc.lp;
		pc.ptAct->maxHp = pc.maxLp;

		// 仲间情报があったらアクションポインタを更新
		for( i = 0; i < MAX_PARTY; i++ )
		{
			if( party[i].useFlag != 0 && party[i].id == pc.id )
			{
				party[i].ptAct = pc.ptAct;
				break;
			}
		}
	}


	prSendMode = 0;
	prSendFlag = 0;
	jbSendFlag = 0;
	duelSendFlag = 0;
	helpFlag = 0;
}


// PCキャラ作成
void createPc( int graNo, int gx, int gy, int dir )
{
	pc.graNo = graNo;
	pc.dir = dir;
	if( pc.ptAct == NULL )
	{
		pc.ptAct = createCharAction( graNo, gx, gy, dir );
		if( pc.ptAct != NULL )
		{
			pc.ptAct->atr |= ACT_ATR_TYPE_PC;
#ifdef PUK3_RIDE		// ライド移动テスト
			pc.ptAct->walkSpeed = pc.walkSpeed;
#endif
		}
	}
	else
	{
		setPcGraNo( graNo, dir );
		setPcWarpPoint( gx, gy );
		setPcPoint();
	}

#ifdef PUK2
//	CHAREXTRA *pYobi = ( CHAREXTRA *)pc.ptAct->pYobi;

//	// ペットがいる场合は一旦ペットを消す
//	if( pYobi->ptPet != NULL ){
//		DeathAction( pYobi->ptPet );
//		pYobi->ptPet = NULL;
//	}
//	pYobi->ptPet = createCharAction( 107100, gx, gy, dir );
#endif
}


// PCキャラリセット
void resetPc( void )
{
	CHAREXTRA *ext;

	if( pc.ptAct != NULL )
	{

		ext = (CHAREXTRA *)pc.ptAct->pYobi;
		if( ext != NULL )
		{
			if( ext->ptActLeaderMark != NULL )
			{
				DeathAction( ext->ptActLeaderMark );
				ext->ptActLeaderMark = NULL;
			}
			if( ext->ptActMagicEffect != NULL )
			{
				DeathAction( ext->ptActMagicEffect );
				ext->ptActMagicEffect = NULL;
			}
#ifdef PUK2
			if( ext->ptActLevelup != NULL )
			{
				DeathAction( ext->ptActLevelup );
				ext->ptActLevelup = NULL;
			}
			if( ext->ptActInjury != NULL )
			{
				DeathAction( ext->ptActInjury );
				ext->ptActInjury = NULL;
			}
			if( ext->ptPet != NULL )
			{
				DeathAction( ext->ptPet );
				ext->ptPet = NULL;
			}
#endif
#ifdef PUK3_CHANGE_ICONACTIONPOINTER
			if( ext->ptActIcon != NULL ){
				DeathAction( ext->ptActIcon );
				ext->ptActIcon = NULL;
			}
#endif
#ifdef PUK3_NOTFREE_CHAREX
	#ifdef PUK3_PUT_ON
			if ( ext->ptPuton ){
				DeathAction( ext->ptPuton );
				ext->ptPuton = NULL;
			}
	#endif
	#ifdef PUK3_RIDE
			if ( ext->ptRide ){
				DeathAction( ext->ptRide );
				ext->ptRide = NULL;
			}
	#endif
			if ( ext->msg ){
				free( ext->msg );
				ext->msg = NULL;
			}
#else
#ifdef PUK3_PUT_ON
			if ( !ext->ptPuton ){
				DeathAction( ext->ptPuton );
				ext->ptPuton = NULL;
			}
#endif
#ifdef PUK3_RIDE
			if ( !ext->ptRide ){
				DeathAction( ext->ptRide );
				ext->ptRide = NULL;
			}
#endif
#endif
		}


		DeathAction( pc.ptAct );
		pc.ptAct = NULL;
	}

	// リーダーマークは消しておく
	delPcLeader();
#ifdef PUK2
	// アイコンが出てるときも消す
	if ( GetHeadIcon() != HEADICON_DELETE ) SetHeadIcon( HEADICON_DELETE );
#endif
}


// PCキャラグラフィック番号设定
void setPcGraNo( int graNo, int dir )
{
	pc.graNo = graNo;
	pc.dir = dir;

	if( pc.ptAct == NULL )
		return;

	pc.ptAct->anim_chr_no = graNo;
	pc.ptAct->anim_ang = dir;
#ifdef PUK3_RIDE
	// ライド中なら
	if ( ( (CHAREXTRA *)pc.ptAct->pYobi )->ptRide ){
		pc.ptAct->anim_chr_no = getRiderCharaGra(pc.ptAct->anim_chr_no);
	}
#endif
}


// PCキャラIDを设定
void setPcId( int id )
{
	pc.id = id;
}


// PCキャラ移动先设定
void setPcMovePoint( int nextGx, int nextGy )
{
//	if( pc.ptAct == NULL )
//		return;

	setMoveMap( nextGx, nextGy );
}


// PCキャラワープ先设定
void setPcWarpPoint( int gx, int gy )
{
//	if( pc.ptAct == NULL )
//		return;

#if 0
	forceWarpMap( gx, gy );
#endif
	setWarpMap( gx, gy );
}


// PCキャラマップ位置に同期する
void setPcPoint( void )
{
	if( pc.ptAct == NULL )
		return;

	pc.ptAct->mx = mapX;
	pc.ptAct->my = mapY;

	pc.ptAct->gx = mapGx;
	pc.ptAct->gy = mapGy;

	pc.ptAct->vx = mapVx;
	pc.ptAct->vy = mapVy;

	pc.ptAct->nextGx = nextMapGx;
	pc.ptAct->nextGy = nextMapGy;
}


// PCキャラの向きを设定
void setPcDir( int dir )
{
	pc.dir = dir;

	if( pc.ptAct == NULL )
		return;

	pc.ptAct->anim_ang = dir;
}


// アニメーション设定
void setPcAction( int act )
{
	if( pc.ptAct == NULL )
		return;

#ifdef PUK3_RIDE
	if ( act == ANIM_WALK ){
		setRiderAnimeNo( pc.ptAct, act, FALSE );
	}else{
		setRiderAnimeNo( pc.ptAct, act, TRUE );
	}
#else
	pc.ptAct->anim_no = act;
	if( pc.ptAct->anim_no != ANIM_WALK )
	{
		pc.ptAct->anim_no_bak = -1;
	}
#endif
}


// アニメーションの状态取得
int getPcAction( void )
{
	if( pc.ptAct == NULL )
		return -1;

#ifdef PUK3_RIDE
	// ライド中ならペットのアクションを返す
	if ( ( (CHAREXTRA *)pc.ptAct->pYobi )->ptRide ){
		return ( (CHAREXTRA *)pc.ptAct->pYobi )->ptRide->anim_no;
	}
#endif
	return pc.ptAct->anim_no;
}


// PCキャラのパラメータ设定
void setPcParam( char *name, char *freeName, int level, int nameColor,
	int walk, int height )
{
	int nameLen;
#ifndef PUK2
	int freeNameLen;
#endif

	nameLen = strlen( name );
	if( nameLen <= CHAR_NAME_LEN )
	{
		strcpy( pc.name, name );
	}

#ifdef PUK2
	{
		char	free[256];
		char	guildname[256];
		char	guildtitle[256];

		getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
		makeRecvString( free );
		if( strlen( free ) <= CHAR_FREENAME_LEN ){
			strcpy( pc.freeName, free );
		}
		getStringToken( freeName, DELIMITER, 2, sizeof( guildname )-1, guildname );
		makeRecvString( guildname );
		if( strlen( guildname ) <= 32 ){
			strcpy( pc.guildName, guildname );
		}
		getStringToken( freeName, DELIMITER, 3, sizeof( guildtitle )-1, guildtitle );
		makeRecvString( guildtitle );
		if( strlen( guildtitle ) <= 32 ){
			strcpy( pc.guildTitle, guildtitle );
		}
	}
#else
	freeNameLen = strlen( freeName );
	if( freeNameLen <= CHAR_NAME_LEN )
	{
		strcpy( pc.freeName, freeName );
	}
#endif

	pc.lv = level;
	pc.nameColor = nameColor;
	if( walk != 0 )		// この上を步ける
	{
		pc.status |= CHR_STATUS_W;
	}
	if( height != 0 )	// 高さがある
	{
		pc.status |= CHR_STATUS_H;
	}

	if( pc.ptAct == NULL )
		return;

	if( nameLen <= CHAR_NAME_LEN )
	{
		strcpy( pc.ptAct->name, name );
	}
#ifdef PUK2
	{
		char	free[256];
		char	guildname[256];
		char	guildtitle[256];

		getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
		makeRecvString( free );
		if( strlen( free ) <= CHAR_FREENAME_LEN ){
			strcpy( pc.ptAct->freeName, free );
		}
		getStringToken( freeName, DELIMITER, 2, sizeof( guildname )-1, guildname );
		makeRecvString( guildname );
		if( strlen( guildname ) <= 32 ){
			strcpy( pc.ptAct->guildName, guildname );
		}
		getStringToken( freeName, DELIMITER, 3, sizeof( guildtitle )-1, guildtitle );
		makeRecvString( guildtitle );
		if( strlen( guildtitle ) <= 32 ){
			strcpy( pc.ptAct->guildTitle, guildtitle );
		}
	}
#else
	if( freeNameLen <= CHAR_FREENAME_LEN )
	{
		strcpy( pc.ptAct->freeName, freeName );
	}
#endif
	pc.ptAct->level = level;
	pc.ptAct->itemNameColor = nameColor;
}


// PCキャラの情报をアクションに设定しなおす
void updataPcAct( void )
{
	if( pc.ptAct == NULL )
		return;

	if( strlen( pc.name ) <= CHAR_NAME_LEN )
	{
		strcpy( pc.ptAct->name, pc.name );
	}
	if( strlen( pc.freeName ) <= CHAR_FREENAME_LEN )
	{
		strcpy( pc.ptAct->freeName, pc.freeName );
	}
	pc.ptAct->level = pc.lv;
	pc.ptAct->hp    = pc.lp;
	pc.ptAct->maxHp = pc.maxLp;
}


// PCキャラをリーダーにする
void setPcLeader( void )
{
	pc.status |= CHR_STATUS_LEADER;
}


// PCキャラをリーダーからやめさせる。
void delPcLeader( void )
{
	pc.status &= (~CHR_STATUS_LEADER);
}


// PCキャラを布ティーの一员にする
void setPcParty( void )
{
	pc.status |= CHR_STATUS_PARTY;
}


// PCキャラを布ティーからやめさせる。
void delPcParty( void )
{
	pc.status &= (~CHR_STATUS_PARTY);
}


// PCキャラを観战モードにする
void setPcWatch( void )
{
	pc.status |= CHR_STATUS_WATCH;
}


// PCキャラを観战モードから元にもどす
void delPcWatch( void )
{
	pc.status &= (~CHR_STATUS_WATCH);
}


// PCキャラをデュエルモードにする
void setPcDuel( void )
{
	pc.status |= CHR_STATUS_DUEL;
}


// PCキャラをデュエルモードから元にもどす
void delPcDuel( void )
{
	pc.status &= (~CHR_STATUS_DUEL);
}


// 现在步いてるフラグを立てる
//（歩きと立ちのアニメーションの切り替え时に参照）
void setPcWalkFlag( void )
{
	if( pc.ptAct == NULL )
		return;

	pc.ptAct->walkFlag = 1;
}

// 立ちアニメに变えたらフラグをおろす
//（歩きと立ちのアニメーションの切り替え时に参照）
void delPcWalkFlag( void )
{
	if( pc.ptAct == NULL )
		return;

	pc.ptAct->walkFlag = 0;
}


// 歩きフラグの参照
int checkPcWalkFlag( void )
{
	if( pc.ptAct == NULL )
		return 0;

	return (int)pc.ptAct->walkFlag;
}


// 咒术使用フラグを立てる
void setPcUseMagic( void )
{
	pc.status |= CHR_STATUS_USE_MAGIC;
}


// 咒术使用フラグをおろす
void delPcUseMagic( void )
{
	pc.status &= (~CHR_STATUS_USE_MAGIC);
}


#ifdef PUK2
// PCに吹き出しを出す
void setPcFukidashi( unsigned int offTime, unsigned char *msg, int color, int fontsize, int len )
{
	int i, off;
	CHAREXTRA *ext;

	if( pc.ptAct == NULL )
		return;

	pc.status |= CHR_STATUS_FUKIDASHI;
	ext = (CHAREXTRA *)pc.ptAct->pYobi;
	ext->drawFukidashiTime = offTime + GetTickCount();

	// 信息关系の登録
	if( ext->msg != NULL ){
#ifdef PUK2_MEMCHECK
		memlistrel( ext->msg, MEMLISTTYPE_PC_FUKIDASHI );
#endif
		free( ext->msg );
		ext->msg = NULL;
	}
	off = strlen( pc.ptAct->name ) + 2;		// 名称の长さ
#ifdef PUK2
	if (msg[0]=='['){
		for(i=1;i<len;i++){
			if (msg[i]==']'){ i++;	break; }
		}
		if (i<len) off += i;
	}
#endif
	len -= off;
#ifdef PUK2
	ext->msg = (char *)malloc( len + 3 );
#else
	ext->msg = (char *)malloc( len + 2 );
#endif
	if( ext->msg != NULL){
#ifdef PUK2_MEMCHECK
		memlistset( ext->msg, MEMLISTTYPE_PC_FUKIDASHI );
#endif
		ext->msg_col = color;
		ext->msg_num = len;
		ext->msg_kind = fontsize;
		
		// 信息の实态を登録（＋名称の切り分けと改行设定）
		if( len > 40 ){
#ifdef PUK2
			// ４０バイト目が全角文字の２バイト目でないなら
			if ( CheckLetterType( (char *)&msg[off], 40 )!=CHECKLETTERTYPE_FULL_TAIL ){
				memcpy( ext->msg, msg+off, 40 );
				ext->msg[40] = '\0';
				memcpy( ext->msg+41, msg+off+40, len-40 );
				len++;
			}
			// ４０バイト目が全角文字の２バイト目なら
			else{
				memcpy( ext->msg, msg+off, 39 );
				ext->msg[39] = '\0';
				memcpy( ext->msg+41, msg+off+39, len-39 );
				len+=2;
			}
#else
			ext->msg[ 40 ] = 0x00;
			for( i = 40 ; i < len ; i++ ){
				ext->msg[ i + 1 ] = msg[ i + off ];
			}
			len++;
			for( i = 0 ; i < 40 ; i++ ){
				ext->msg[ i ] = msg[ i + off ];
			}
#endif
		}else{
			for( i = 0 ; i < len ; i++ ){
				ext->msg[ i ] = msg[ i + off ];
			}
		}
		ext->msg[ len ] = 0x00;
	}
}
#else
// PCに吹き出しを出す
void setPcFukidashi( unsigned int offTime )
{
	CHAREXTRA *ext;

	if( pc.ptAct == NULL )
		return;

	pc.status |= CHR_STATUS_FUKIDASHI;
	ext = (CHAREXTRA *)pc.ptAct->pYobi;
	ext->drawFukidashiTime = offTime + GetTickCount();
}
#endif


// PCキャラ动作变更 /////////////////////////////////////////////////////////
#ifdef PUK3_RIDE		// ライドの移动テスト
void changePcAct( int x, int y, int dir, int action,
					int effectno, int param3 )
#else
void changePcAct( int x, int y, int dir, int action,
					int effectno )
#endif
{
	switch( action )
	{
		// 立ち止まる
		case 0:
			setPcAction( ANIM_STAND );
			break;

		// 步く
		case 1:

#ifdef PUK3_RIDE		// ライドの移动テスト
			if( effectno != -1 ){
				pc.walkSpeed = effectno;
			}else{
				pc.walkSpeed = 100;
			}
			pc.ptAct->walkSpeed = pc.walkSpeed;
#endif

			setPcAction( ANIM_WALK );
			break;

		// 走り始めるアクション
		case 2:
			setPcAction( ANIM_B_WALK_START );
			break;

		// 走り中アクション
		case 3:
			setPcAction( ANIM_B_WALK );
			break;

		// 走り終えるアクション
		case 4:
			setPcAction( ANIM_B_WALK_END );
			break;

		// 攻击アクション
		case 5:
			setPcDir( dir );
			setPcAction( ANIM_ATTACK );
			break;

		// 魔法咏唱
		case 6:
			setPcAction( ANIM_MAGIC );
			break;

		// 投掷アクション
		case 7:
			setPcDir( dir );
			setPcAction( ANIM_THROW );
			break;

		// 受伤アクション
		case 8:
			setPcDir( dir );
			setPcAction( ANIM_DAMAGE );
			break;

		// 防御アクション
		case 9:
			setPcDir( dir );
			setPcAction( ANIM_GUARD );
			break;

		// 倒下アクション
		case 10:
			setPcDir( dir );
			setPcAction( ANIM_DEAD );
			break;

		// 座るアクション
		case 11:
			setPcDir( dir );
			setPcAction( ANIM_SIT );
			break;

		// 挥手アクション
		case 12:
			setPcDir( dir );
			setPcAction( ANIM_HAND );
			break;

		// 喜ぶアクション
		case 13:
			setPcDir( dir );
			setPcAction( ANIM_HAPPY );
			break;

		// 怒るアクション
		case 14:
			setPcDir( dir );
			setPcAction( ANIM_ANGRY );
			break;

		// 悲伤アクション
		case 15:
			setPcDir( dir );
			setPcAction( ANIM_SAD );
			break;

		// 点头アクション
		case 16:
			setPcDir( dir );
			setPcAction( ANIM_NOD );
			break;

		// じゃんけんアクション　石头
		case 17:
			setPcAction( ANIM_GU );
			break;

		// じゃんけんアクション　剪刀
		case 18:
			setPcAction( ANIM_CHOKI );
			break;

		// じゃんけんアクション　布
		case 19:
			setPcAction( ANIM_PA );
			break;


		// 歩きアクション
		case 30:
			setPcDir( dir );
			setPcAction( ANIM_WALK );
			break;

		// 立ちアクション
		case 31:
			setPcDir( dir );
			setPcAction( ANIM_STAND );
			break;


		// 战闘情报＆マーク
		case 40:
			setPcWarpPoint( x, y );
			setPcDir( dir );
#ifdef PUK3_WHALE_SHIP_WARP
			// 座标がずれるとマップが崩れる场合があるのでしたを呼ぶ
			// マップ处理リセット
			resetMap();
#endif
			break;

		// リーダーマーク
		case 41:
			setPcWarpPoint( x, y );
			setPcDir( dir );
#ifdef PUK3_WHALE_SHIP_WARP
			// 座标がずれるとマップが崩れる场合があるのでしたを呼ぶ
			// マップ处理リセット
			resetMap();
#endif
			if( effectno == 1 )
			{
				// リーダーマーク表示
				setPcLeader();
			}
			else
			{
				// リーダーマーク消去
				delPcLeader();
			}
			break;

		// 観战マーク
		case 42:
			setPcWarpPoint( x, y );
			setPcDir( dir );
#ifdef PUK3_WHALE_SHIP_WARP
			// 座标がずれるとマップが崩れる场合があるのでしたを呼ぶ
			// マップ处理リセット
			resetMap();
#endif
			if( effectno == 1 )
			{
				setPcWatch();
			}
			else
			{
				delPcWatch();
			}
			break;

		// 名称の色情报
		case 43:
			break;

		// デュエルマーク
		case 44:
			setPcWarpPoint( x, y );
			setPcDir( dir );
#ifdef PUK3_WHALE_SHIP_WARP
			// 座标がずれるとマップが崩れる场合があるのでしたを呼ぶ
			// マップ处理リセット
			resetMap();
#endif
			if( effectno == 1 )
			{
				setPcDuel();
			}
			else
			{
				delPcDuel();
			}
			break;

#ifdef PUK2
		// 怪我マーク
		case 45:
			// 0:表示消す 1:表示
			if( effectno == 1 )
			{
				setPcHealth();
			}
			else
			{
				delPcHealth();
			}
			setPcDir( dir );
			break;

		// スキル使用中マーク
		case 46:
			// 0:表示消す 0以外:その番号のアイコン表示
			if( effectno )
			{
				setPcSkill(effectno);
			}
			else
			{
				delPcSkill();
			}
			setPcDir( dir );
			break;

		// 等级アップマーク
		case 47:
			// 0:表示消す 1:表示
			setPcLevelup();
			setPcDir( dir );
			break;

		// ペット怪我マーク
		case 48:
			// 0:表示消す 1:表示
			if( effectno == 1 )
			{
				setPcPetHealth();
			}
			else
			{
				delPcPetHealth();
			}
			setPcDir( dir );
			break;

		// ペット等级アップマーク
		case 49:
			// 0:表示消す 1:表示
			setPcPetLevelup();
			setPcDir( dir );
			break;
#endif

		// 方向転换
		case 50:
			setPcDir( dir );
			break;

		// ワープ
		case 51:
			setPcWarpPoint( x, y );
#ifdef PUK3_WHALE_SHIP_WARP
			// ワープに限りこの后に移动处理を行わない可能性があるため、
			// ここで呼んでおく
			// 普通は移动处理のときに呼ばれる
			setPcPoint();
#endif
			setPcDir( dir );
			setPcAction( ANIM_STAND );
#ifdef PUK3_WHALE_SHIP_WARP
			// 座标がずれるとマップが崩れる场合があるのでしたを呼ぶ
			// マップ处理リセット
			resetMap();
#endif
			break;

#ifdef PUK2
		// グラフィック番号变更
		case 60:
	#ifdef _CG2_NEWGRAPHIC
			pc.graNo = getNewGraphicNo( effectno );	// 新グラフィック番号代入
			pc.ptAct->anim_chr_no = getNewGraphicNo( effectno );	// 新グラフィック番号代入
	#else
			pc.graNo = effectno;	// 新グラフィック番号代入
			pc.ptAct->anim_chr_no = effectno;	// 新グラフィック番号代入
	#endif
	#ifdef PUK3_PUT_ON
			// 被り物あるなら
			if (param3){
				CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;
				if (!ext->ptPuton) ext->ptPuton = makePuton( param3 );
				if (ext->ptPuton){
	#ifdef _CG2_NEWGRAPHIC
					ext->ptPuton->anim_chr_no = getNewGraphicNo( param3 );
	#else
					ext->ptPuton->anim_chr_no = param3;
	#endif
				}
			}
			// 被り物ないなら
			else{
				CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;
				if (ext->ptPuton){
					DeathAction(ext->ptPuton);
					ext->ptPuton = NULL;
				}
			}
	#endif
			break;

		// リバース使用
		case 61:
////			rebirth=effectno;
			break;

		// リバース中止
		case 62:
////			rebirth=effectno;
			break;

		// スキル使用
		case 63:
			setPcSkill(effectno);
			setPcDir( dir );
			break;

		// スキル使用中止
		case 64:
			delPcSkill();
			setPcDir( dir );
			break;
#endif
#ifdef PUK3_RIDE
		// ペットライド
		case 70:
			// 乘るなら
			if ( effectno >= 0 ){
				CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;

				// モンスターがでてないなら作る
				if (!ext->ptRide){
					ext->ptRide = GetAction( PRIO_CHR, NULL );

	#ifdef PUK3_CHECK_VALUE
					if ( ext->ptRide ) {
	#endif
					// 实行关数
					ext->ptRide->func = NULL;
					// グラフィックの番号
					// 动作番号
					ext->ptRide->anim_no = ANIM_STAND;
					// 表示优先度
					ext->ptRide->dispPrio = DISP_PRIO_CHAR;
					// 1行インフォ表示フラグ
					ext->ptRide->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
#ifdef PUK3_RIDE_SOUND
					// ペット扱いで音鸣らさない
					ext->ptRide->atr |= ACT_ATR_TYPE_PET;
#endif
	#ifdef PUK3_CHECK_VALUE
					}
	#endif
				}

	#ifdef PUK3_CHECK_VALUE
				if ( ext->ptRide ) {
	#endif
				// グラフィックの番号
#ifdef _CG2_NEWGRAPHIC
				ext->ptRide->anim_chr_no = getNewGraphicNo(effectno);
#else
				ext->ptRide->anim_chr_no = effectno;
#endif
	
				pc.ptAct->anim_chr_no = getRiderCharaGra(pc.graNo);
				pc.ptAct->actNo = CHARACT_RIDE_ON;
				ext->actCnt = 0;

				// 残り步数
				pc.walkRest = param3;

				ext->ptRide->anim_no = ANIM_STAND;
				if ( pc.ptAct->anim_no == ANIM_WALK ){
					ext->ptRide->anim_no = ANIM_WALK;
				}
	#ifdef PUK3_CHECK_VALUE
				}
	#endif
			}
			// 降りるなら
			else{
				if ( pc.ptAct->actNo != CHARACT_GET_OFF ){
					CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;
	
					pc.ptAct->actNo = CHARACT_GET_OFF;
					ext->actCnt = 0;

					if (ext->ptRide){
						ext->ptRide->anim_no = ANIM_STAND;
						if ( pc.ptAct->anim_no == ANIM_WALK ){
							ext->ptRide->anim_no = ANIM_WALK;
						}
					}
	
					// 残り步数
					pc.walkRest = 0;
				}
			}
			break;
#endif
#ifdef PUK3_VEHICLE
		// 乘り物归还
		case 80:
			{
				CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;

				pc.ptAct->actNo = CHARACT_VEHICLE_RETURN;
	#ifdef _CG2_NEWGRAPHIC
				pc.ptAct->anim_chr_no = getNewGraphicNo( effectno );
	#else
				pc.ptAct->anim_chr_no = effectno;
	#endif
				ext->actCnt = 0;
			}
			break;
#endif
	}
}


// 仲间情报のクリア /////////////////////////////////////////////////////////
void clearPartyParam( void )
{
#if defined(PUK3_RAG_PARTY_BREAK_UP_1)
	int i, j, k;

	if ( party[0].ptAct ){
		// リーダーの后ついていけるように
		// 移动先を设定する
		for( i = 1; i < MAX_PARTY; i++ ){
			if( party[i].useFlag == 0 ) continue;

			if ( party[i].id == pc.id ){
				for( j = 0; j < party[0].ptAct->bufCount; j++ ){
					k = j - i;
					if ( k >= 0 ){
						// バッファが空いていたらためる
						if( party[i].ptAct->bufCount < sizeof( party[i].ptAct->bufGx )/sizeof( int ) ){
							stockCharMovePoint( party[i].ptAct,
								 party[0].ptAct->bufGx[k], party[0].ptAct->bufGy[k] );
						}else{
							if( pc.ptAct != NULL ) pc.ptAct->bufCount = 0;
							setPcWarpPoint( party[0].ptAct->bufGx[k], party[0].ptAct->bufGy[k] );
						}
					}else{
						// バッファが空いていたらためる
						if( party[i].ptAct->bufCount < sizeof( party[i].ptAct->bufGx )/sizeof( int ) ){
							stockCharMovePoint( party[i].ptAct,
								 party[-k-1].ptAct->nextGx, party[-k-1].ptAct->nextGy );
						}else{
							if( pc.ptAct != NULL ) pc.ptAct->bufCount = 0;
							setPcWarpPoint( party[-k-1].ptAct->nextGx, party[-k-1].ptAct->nextGy );
						}
					}
				}
			}else{
				for( j = 0; j < party[0].ptAct->bufCount; j++ ){
					k = j - i;
					if ( k >= 0 ){
						stockCharMovePoint( party[i].ptAct,
							 party[0].ptAct->bufGx[k], party[0].ptAct->bufGy[k] );
					}else{
						stockCharMovePoint( party[i].ptAct,
							 party[-k-1].ptAct->nextGx, party[-k-1].ptAct->nextGy );
					}
				}
			}
		}
	}

	// 先头のキャラのアクションがなかったとき
	if ( !party[0].ptAct ){
		for( i = 0; i < MAX_PARTY; i++ ){
			if( party[0].ptAct ) party[0].ptAct->bufCount = 0;
		}
	}

	for( i = 0; i < MAX_PARTY; i++ ){
		// さっきまで布ティだったキャラのフラグをおろす
		if ( party[i].useFlag != 0 ){
			if ( party[i].id == pc.id ){
				delPcParty();
			}
			else{
				delCharParty( party[i].ptAct );
			}
		}
		party[i].useFlag = 0;
		party[i].id = 0;
		party[i].ptAct = NULL;
	}

	delPcLeader();
#elif defined(PUK3_RAG_PARTY_BREAK_UP_2)
	int i, k;
	int x1, y1, x2, y2;

	if ( party[0].ptAct && party[0].ptAct->bufCount > 0 ){
		// リーダーの后ついていけるように
		// 移动先を设定する
		// キャラの座标を变更してしまうので、布ティーの最后から处理する
		for( i = MAX_PARTY - 1; i > 0; i-- ){
			if ( party[i].useFlag == 0 ) continue;
			if ( !party[0].ptAct ) continue;

			k = (party[0].ptAct->bufCount - 1 - i) - 1;
			if ( k >= 0 ){
				x2 = party[0].ptAct->bufGx[k];
				y2 = party[0].ptAct->bufGy[k];
			}else{
				if ( !party[-k-1].ptAct ) continue;
				x2 = party[-k-1].ptAct->nextGx;
				y2 = party[-k-1].ptAct->nextGy;
			}

			k = party[0].ptAct->bufCount - 1 - i;
			if ( k >= 0 ){
				x1 = party[0].ptAct->bufGx[k];
				y1 = party[0].ptAct->bufGy[k];
			}else{
				if ( !party[-k-1].ptAct ) continue;
				x1 = party[-k-1].ptAct->nextGx;
				y1 = party[-k-1].ptAct->nextGy;
			}

			if ( party[i].id == pc.id ){
				setPcWarpPoint( x1, y1 );
			}else{
				setCharWarpPoint( party[i].ptAct, x1, y1 );
			}
			// 方向の设定
			party[i].ptAct->anim_ang = ( getDirFromXY( (float)( x2 - x1 ), (float)( y2 - y1 ) ) + 4 ) % 8;
			if ( party[i].id == pc.id ) pc.dir = party[i].ptAct->anim_ang;
		}

		// リーダーをワープ
		if ( party[0].id == pc.id ){
			// ＰＣキャラがリーダーなら确实に移动終わっているので
		}else{
			k = (party[0].ptAct->bufCount - 1) - 1;
			if ( k >= 0 ){
				x2 = party[0].ptAct->bufGx[k];
				y2 = party[0].ptAct->bufGy[k];
			}else{
				if ( !party[-k-1].ptAct ){
					x2 = party[0].ptAct->nextGx;
					y2 = party[0].ptAct->nextGy;
				}else{
					x2 = party[-k-1].ptAct->nextGx;
					y2 = party[-k-1].ptAct->nextGy;
				}
			}

			k = party[0].ptAct->bufCount - 1;
			if ( k >= 0 ){
				x1 = party[0].ptAct->bufGx[k];
				y1 = party[0].ptAct->bufGy[k];
				setCharWarpPoint( party[0].ptAct,
					 x1, y1 );
			}
			// 方向の设定
			party[0].ptAct->anim_ang = ( getDirFromXY( (float)( x2 - x1 ), (float)( y2 - y1 ) ) + 4 ) % 8;
			if ( party[0].id == pc.id ) pc.dir = party[0].ptAct->anim_ang;
		}
	}else{
		// ２番目のキャラのバッファがたまってるとき
		if ( party[1].ptAct && party[1].ptAct->bufCount > 0 ){
			for( i = MAX_PARTY - 1; i > 1; i-- ){
				if ( party[i].useFlag == 0 ) continue;
				if ( !party[i].ptAct ) continue;
				if ( party[i-1].useFlag == 0 ) continue;
				if ( !party[i-1].ptAct ) continue;

				x2 = party[i].ptAct->nextGx;
				y2 = party[i].ptAct->nextGy;

				// 自分のひとつ前のところへワープ
				x1 = party[i-1].ptAct->nextGx;
				y1 = party[i-1].ptAct->nextGy;
				if ( party[i].id == pc.id ){
					setPcWarpPoint( x1, y1 );
				}else{
					setCharWarpPoint( party[i].ptAct, x1, y1 );
				}
				// 方向の设定
				party[i].ptAct->anim_ang = ( getDirFromXY( (float)( x2 - x1 ), (float)( y2 - y1 ) ) + 4 ) % 8;
				if ( party[i].id == pc.id ) pc.dir = party[i].ptAct->anim_ang;
			}

			// ２番目のキャラをワープ
			x2 = party[1].ptAct->nextGx;
			y2 = party[1].ptAct->nextGy;

			x1 = party[1].ptAct->bufGx[0];
			y1 = party[1].ptAct->bufGy[0];
			if ( party[i].id == pc.id ){
				setPcWarpPoint( x1, y1 );
			}else{
				setCharWarpPoint( party[i].ptAct, x1, y1 );
			}
			// 方向の设定
			party[i].ptAct->anim_ang = ( getDirFromXY( (float)( x2 - x1 ), (float)( y2 - y1 ) ) + 4 ) % 8;
			if ( party[i].id == pc.id ) pc.dir = party[i].ptAct->anim_ang;
		}
	}

	for( i = 0; i < MAX_PARTY; i++ ){
		// さっきまで布ティだったキャラのフラグをおろす
		if ( party[i].useFlag != 0 ){
			if(  party[i].id == pc.id ){
				// ＰＣの移动バッファをクリアする
				if( party[i].ptAct != NULL ) party[i].ptAct->bufCount = 0;
				delPcParty();
			}else{
				party[i].ptAct->bufCount = 0;
				delCharParty( party[i].ptAct );
			}
		}
		party[i].useFlag = 0;
		party[i].id = 0;
		party[i].ptAct = NULL;
	}

	delPcLeader();
#else
	int i;

	for( i = 0; i < MAX_PARTY; i++ )
	{
		// さっきまで布ティだったキャラのフラグをおろす
		if( party[i].useFlag != 0 )
		{
			if(  party[i].id == pc.id )
			{
				// ＰＣの移动バッファをクリアする
				if( party[i].ptAct != NULL )
				{
					party[i].ptAct->bufCount = 0;
				}
				delPcParty();
			}
			else
			{
				// ＰＣがリーダーの时は他のＰＣの移动バッファをクリア
				if( (pc.status & CHR_STATUS_LEADER) != 0
				 && party[i].ptAct != NULL )
				{
					party[i].ptAct->bufCount = 0;
				}
				delCharParty( party[i].ptAct );
			}
		}
		party[i].useFlag = 0;
		party[i].id = 0;
		party[i].ptAct = NULL;
	}

	delPcLeader();
#endif
}


// 仲间情报のptActにNULLを入れてやる
void clearPtActPartyParam( void )
{
	int i;

	for( i = 0; i < MAX_PARTY; i++ )
	{
		party[i].ptAct = NULL;
	}
}





// キャラリストにデータが设定されているか检查 /////////////////////////////
int existCharacterListEntry( int index )
{
	if( index < 0 || index >= MAXCHARACTER )
		return -1;

	if( chartable[index].name[0] != '\0' )
	{
		return 1;
	}
	else
	{
		return 0;
	}
}


// キャラリストに同一名があるかチェック
int cmpNameCharacterList( char *name )
{
	int i;

	for( i = 0; i < MAXCHARACTER; i++ )
	{
		if( strcmp( name, chartable[i].name ) == 0 )
			return 1;
	}

	return 0;
}


//----------------------------------------------------------------
// 旧キャラですか？リニューですか？新キャラですか？を颜グラで判断
//  0:旧   1:リニュー  2:新   -1:エラー
//----------------------------------------------------------------
int WhichTypeFaceGraphic( int graNo ){
	// 旧キャラです。
	if( 30000 <= graNo && graNo <= 97999) return 0;
	//リニューアルです。
	if( 202800 <= graNo && graNo <= 204199) return 1;
	// 新です。
	if( 200800 <= graNo && graNo <= 202799 )return 2;
	return -1;
}

// キャラリストへデータ设定（书き込むバッファは位置はデータの中に书いてある）
int setCharacterList( char *name, char *opt )
{
	int index;

	makeRecvString( name );
	makeRecvString( opt );

	index = getIntegerToken( opt, '|', 1 );

	if( index < 0 || index >= MAXCHARACTER )
		return -1;

	memset( &chartable[index], 0, sizeof( CHARLISTTABLE ) );

	if( strlen( name ) <= CHAR_NAME_LEN )
	{
		strcpy( chartable[index].name, name );
	}
	else
	{
		strcpy( chartable[index].name, "???" );                             //MLHIDE
	}
	chartable[index].faceGraNo	= getIntegerToken( opt, '|',  2 );		// 颜画像番号
	chartable[index].lv			= getIntegerToken( opt, '|',  3 );		// 等级
	chartable[index].vit		= getIntegerToken( opt, '|',  4 );		// 体力
	chartable[index].str		= getIntegerToken( opt, '|',  5 );		// 攻击力
	chartable[index].tgh		= getIntegerToken( opt, '|',  6 );		// 防御力
	chartable[index].qui		= getIntegerToken( opt, '|',  7 );		// 敏捷力
	chartable[index].mgc		= getIntegerToken( opt, '|',  8 );		// 魔法力
	chartable[index].attr[0]	= getIntegerToken( opt, '|',  9 )/10;	// 属性（地）
	chartable[index].attr[1]	= getIntegerToken( opt, '|', 10 )/10;	//     （水）
	chartable[index].attr[2]	= getIntegerToken( opt, '|', 11 )/10;	//     （火）
	chartable[index].attr[3]	= getIntegerToken( opt, '|', 12 )/10;	//     （风）
	chartable[index].login		= getIntegerToken( opt, '|', 13 );		// ログイン回数
	chartable[index].registnumber=getIntegerToken( opt, '|', 15 );      // キャラ登録番号 
#ifdef PUK2
	// キャラクタのリニューアル设定を取得する
	chartable[index].isRenewal = getIntegerToken( opt, '|', 17 );		// リニューアルグラフィックかどうか
	chartable[index].baseImgNo = getIntegerToken( opt, '|', 18 );		// ついでに基本グラフィックも受け取る
	chartable[index].guildTitle = getIntegerToken( opt, '|', 19 );		// 家族称号ＩＤを受け取る
#else
	// ここで颜グラがリニューアルになってたらリニューアル设定にする。
	chartable[index].isRenewal = WhichTypeFaceGraphic( chartable[index].faceGraNo );
#endif
	getStringToken( opt, '|', 16, sizeof( chartable[index].JobName ) - 1, chartable[index].JobName );
	return 0;
}


// キャラリストの内容をけす。
//   int index : どのキャラか。0  ~ 7
int resetCharacterList( int index )
{
	if( index < 0 || index >= MAXCHARACTER )
		return -1;

	memset( &chartable[index], 0, sizeof( CHARLISTTABLE ) );

	return 0;
}


// アイテムを拾う /////////////////////////////////////////////////////////
void getItem( void )
{
	float tmpX, tmpY;
	int dir;
	static unsigned int piSendTime = 0;

	// サーバから何かウィンドウを出せといわれている时は拾えない
	if( serverRequestWinWindowType != -1 )
		return;

	// 选择先が足元及び邻接するグリッド以外は处理しない
	if( ABS( mapGx - mouseMapGx ) > 1
	 || ABS( mapGy - mouseMapGy ) > 1 )
		return;

#ifdef PUK3_NOEXISTCHARA
	// 选择先に何かあるか调べてなければ处理しない
	// 不明キャラが居る场合は、ペットなどかもしれないので通す
	if( !checkCharObjPoint( mouseMapGx, mouseMapGy,
		CHAROBJ_TYPE_NPC|CHAROBJ_TYPE_ITEM|CHAROBJ_TYPE_MONEY|CHAROBJ_TYPE_UNKNOWN ) )
		return;
#else
	// 选择先に何かあるか调べてなければ处理しない
	if( !checkCharObjPoint( mouseMapGx, mouseMapGy,
		CHAROBJ_TYPE_NPC|CHAROBJ_TYPE_ITEM|CHAROBJ_TYPE_MONEY ) )
		return;
#endif

	// 现在のマウスの方向を调べる
	tmpX = (float)(mouseMapGx - mapGx);
	tmpY = (float)(mouseMapGy - mapGy);
	dir = getDirFromXY( tmpX, tmpY );

	// 连射抑制
	if( piSendTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )
	{
		// サーバに送る
#ifndef _DEBUG
		nrproto_PI_send( sockfd, mapGx, mapGy, dir );
#else
		if( !offlineFlag )
		{
			nrproto_PI_send( sockfd, mapGx, mapGy, dir );
		}
#endif
		piSendTime = GetTickCount();
	}
}


// アイテムの入れ替え /////////////////////////////////////////////////////
void swapItem( int from, int to )
{
	ITEM tmpItem;

	if( from < 0 || to < 0 )
		return;

#if 1
	tmpItem = pc.item[from];
	pc.item[from] = pc.item[to];
	pc.item[to] = tmpItem;
#else
	swapShort( &pc.item[from].useFlag, &pc.item[to].useFlag );
	swapString( pc.item[from].name, pc.item[to].name );
	swapString( pc.item[from].name2, pc.item[to].name2 );
	swapString( pc.item[from].memo, pc.item[to].memo );
	swapInteger( &pc.item[from].color, &pc.item[to].color );
	swapInteger( &pc.item[from].graNo, &pc.item[to].graNo );
	swapInteger( &pc.item[from].lv, &pc.item[to].lv );
	swapShort( &pc.item[from].field, &pc.item[to].field );
	swapShort( &pc.item[from].target, &pc.item[to].target );
	swapShort( &pc.item[from].deadTargetFlag, &pc.item[to].deadTargetFlag );
	swapShort( &pc.item[from].sendFlag, &pc.item[to].sendFlag );
#endif
#ifdef PUK2_LV_LIMIT_ITEM
	// 装备栏に移动した场合、フラグから装备可能情报を立てる
	if ( from < MAX_EQUIP_ITEM ) pc.item[from].flag |= ITEM_ETC_FLAG_VALIDEQUIP;
	if ( to < MAX_EQUIP_ITEM ) pc.item[to].flag |= ITEM_ETC_FLAG_VALIDEQUIP;
#endif
#ifdef PUK2_NEW_MENU
	// アイテムの位置が移动された时の处理
	swapItemRelation( from, to );
#endif
}


// 整数值の入れか
void swapInteger( int *a, int *b )
{
	int tmp;

	tmp = *a;
	*a = *b;
	*b = tmp;
}


void swapShort( short *a, short *b )
{
	short tmp;

	tmp = *a;
	*a = *b;
	*b = tmp;
}


// 文字列の入れ替え（ただし、255文字までの文字列）
void swapString( char *a, char *b )
{
	char tmp[256];

	if( strlen( a ) > 255 || strlen( b ) > 255 )
		return;

	strcpy( tmp, a );
	strcpy( a, b );
	strcpy( b, tmp );
}


// 邻接するグリッドを见る /////////////////////////////////////////////////
BOOL lookAtAround( void )
{
	float tmpX, tmpY;
	int dir;
	static unsigned int lSendTime = 0;

	// サーバからウィンドウを出せといわれてる时は见ないようにする
	if( serverRequestWinWindowType != -1 )
		return FALSE;

	// 选择先が足元及び邻接するグリッド以外は处理しない
	if( ABS( mapGx - mouseMapGx ) > 1
	 || ABS( mapGy - mouseMapGy ) > 1 )
		return FALSE;

	// 选择先に何かあるか调べてなければ处理しない
#ifdef PUK3_NOEXISTCHARA
	if( !checkCharObjPoint( mouseMapGx, mouseMapGy, CHAROBJ_TYPE_LOOKAT|CHAROBJ_TYPE_UNKNOWN ) )
		return FALSE;
#else
	if( !checkCharObjPoint( mouseMapGx, mouseMapGy, CHAROBJ_TYPE_LOOKAT ) )
		return FALSE;
#endif

	// 现在のマウスの方向を调べる
	tmpX = (float)(mouseMapGx - mapGx);
	tmpY = (float)(mouseMapGy - mapGy);
	if( tmpX == 0 && tmpY == 0 )	// 揭示板の上で自分を右键したら止める
		return FALSE;

	dir = getDirFromXY( tmpX, tmpY );

	// 连射抑制
	if( lSendTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )
	{
		// サーバに送る
#ifndef _DEBUG
		nrproto_L_send( sockfd, dir );
#else
		if( !offlineFlag )
		{
			nrproto_PI_send( sockfd, mapGx, mapGy, dir );
		}
#endif
		lSendTime = GetTickCount();
	}

	return TRUE;
}


//****									****//
//****   クロスゲートＥＸ，２关连处理   ****//
//****									****//


#ifdef _CG2_NEWGRAPHIC
//自分のパッケージ情报
enum{
	CG_MY_VERSION_1=0,
	CG_MY_VERSION_M,
	CG_MY_VERSION_1V,
	CG_MY_VERSION_EX,
	CG_MY_VERSION_EXV,
	CG_MY_VERSION_2,
	CG_MY_VERSION_2V,
#ifdef PUK2
	CG_MY_VERSION_PUK2,
#endif
#ifdef PUK3_UPGRADE
	CG_MY_VERSION_PUK3,
#endif
	CG_MY_VERSION_NUM
};

//************************************************
//相手のキャラグラの状态を画像番号で探す
//信用できるのか
//本当はサーバーから教えてもらうほうがいいかな
//とりあえずキャラ管理ＩＤ（ＨＴＭＬ）を信用する
//
//戾り值；クロゲバージョン上记enum参考
//************************************************
static int getOtherGraphicType(int graNo)
{
	int ret = CG_OTHER_1;

#if 1
	//クロゲ１
	if(graNo >= 100000 && graNo <= 103102){
		//クロゲ１キャラ
		ret = CG_OTHER_1;
	}else if(graNo >= 105000 && graNo <= 105423){
		//XG2用リニューアルXG1プレイヤーキャラクター
		ret = CG_OTHER_RE;
	}else if(graNo >= 106000 && graNo <= 106048){
		//XGEX用男性プレイヤーキャラクター（2种8体）
		ret = CG_OTHER_EX;
	}else if(graNo >= 106050 && graNo <= 106173){
		//XG2用男性プレイヤーキャラクター（5种20体）
		ret = CG_OTHER_2;
	}else if(graNo >= 106250 && graNo <= 106298){
		//XGEX用女性プレイヤーキャラクター（2种8体）
		ret = CG_OTHER_EX;
	}else if(graNo >= 106300 && graNo <= 106423){
		//XG2用女性プレイヤーキャラクター（5种20体）	
		ret = CG_OTHER_2;
	}else if(graNo >= 100900 && graNo <= 100902){
		//XG2用シャドウキャラクター(プレイヤー＆モンスター计3体）
		ret = CG_OTHER_2;
	}else if(graNo >= 107000 && graNo < 107999){
		//XG2用新モンスター
		ret = CG_OTHER_2;
	}else if(graNo >= 106425 && graNo < 106570){
		//XG2用男性プレイヤーキャラクター（5种20体）
		ret = CG_OTHER_2;
	}else if(graNo >= 106600 && graNo < 106755){
		//XG2用女性プレイヤーキャラクター（5种20体）	
		ret = CG_OTHER_2;
	}
#ifdef PUK2
	else if(graNo >= 110300 && graNo < 110350){
		//PUK2用新モンスター
		ret = CG_OTHER_PUK2_M;
	}else if(graNo >= 110376 && graNo < 110400){
		//PUK2用新モンスター
		ret = CG_OTHER_PUK2_M;
#ifdef PUK3_UPGRADE
	}else if(graNo >= 110505 && graNo < 110599){
		//PUK3用新モンスター
		ret = CG_OTHER_PUK3_M;
	}else if(graNo >= 110770 && graNo <= 110799){
		//PUK3用新モンスター
		ret = CG_OTHER_PUK3_M;
	}else if(graNo >= SPR_kabu_start && graNo <= SPR_kabu_end){
		//カブリモノ(必ず表示)
		ret = CG_OTHER_1;
#endif
	}else if(graNo >= 110504 && graNo < 114999){
		//PUK2用新モンスター
		ret = CG_OTHER_PUK2_M;
	}else{
		ret = CG_OTHER_1;
	}
#endif

#endif



	return ret;

}

//**************************************************
//影キャラ用に男か女をチェックする。
//
//戾り值：0:男　１:女
//
//**************************************************
static int getOtherSexCheck(int graNo)
{
	if(graNo >= 106000 && graNo <= 106048){
		//Ex男キャラ
		return SPR_shadow_pcm;
	}else if(graNo >= 106050 && graNo <= 106173){
		//２の男キャラ
		return SPR_shadow_pcm;
	}else if(graNo >= 106250 && graNo <= 106298){
		//Ex女キャラ
		return SPR_shadow_pcf;
	}else if(graNo >= 106300 && graNo <= 106423){
		//Ex女キャラ
		return SPR_shadow_pcf;
	}else if(graNo >= 107000 && graNo < 107999){
		//モンスターだ
		return SPR_shadow_mon;
	}else if(graNo >= 106425 && graNo < 106570){
		//管理男キャラ
		return SPR_shadow_pcm;
	}else if(graNo >= 106600 && graNo < 106755){
		//管理女キャラ
		return SPR_shadow_pcf;
	}
#ifdef PUK2
	else if(graNo >= 110300 && graNo < 110350){
		//PUK2用新モンスター
		return SPR_shadow_mon;
	}else if(graNo >= 110376 && graNo < 110400){
		//PUK2用新モンスター
		return SPR_shadow_mon;
#ifdef PUK3_UPGRADE
	}else if(graNo >= 110505 && graNo <= 110599){
		//PUK3用新モンスター
		return SPR_mon1998g;
	}else if(graNo >= 110770 && graNo < 110799){
		//PUK3用新モンスター
		return SPR_mon1998g;
#endif
	}else if(graNo >= 110504 && graNo < 114999){
		//PUK2用新モンスター
		return SPR_shadow_mon;
	}
#endif

	
	
	return -1;

}


//**********************************************
//クロゲ２、ＥＸ用の画像变更
//引数：
//	id :キャラクターのＩＤ
//	graNo:グラフィックＮＯ
// chartype:キャラの种类
//戾り值：新しいグラフィックＮＯ
//
//*********************************************
int getNewGraphicNo( int graNo)

{
	int NowVer;
	int otherVer;	
	int ret = graNo;

#ifdef _CG2_NEWGRAPHIC
	//现在の自分のバージョン
	NowVer = CG2PackageVer;
	//クライアント引数を优先する
	if(CG2PackageVer == 255){
		NowVer = PackageVer;
	}
#else
	NowVer = PackageVer;
#endif

	//相手はどのバージョンキャラか
	otherVer = getOtherGraphicType( graNo);
	
#ifdef PUK2
	//自分のバージョンでチェック
#ifdef PUK3_UPGRADE
	if ( NowVer >= CG_MY_VERSION_PUK3 ) NowVer = CG_OTHER_PUK3_M;
	else
#endif
	if ( NowVer >= CG_MY_VERSION_PUK2 ) NowVer = CG_OTHER_PUK2_M;
	else if ( NowVer >= CG_MY_VERSION_EX ) NowVer = CG_OTHER_RE;
	else NowVer = CG_OTHER_1;

	if (NowVer >= otherVer){
#else

	//自分のバージョンでチェック
	if(NowVer == CG_MY_VERSION_2 	// 自分がバージョン２の时は。
	|| NowVer == CG_MY_VERSION_2V 	// ＥＸはβ扱いなのでバージョン２と同じ处理
	|| NowVer == CG_MY_VERSION_EX
	|| NowVer == CG_MY_VERSION_EXV
#ifdef PUK2
	|| NowVer == CG_MY_VERSION_PUK2
#endif
#ifdef PUK3_UPGRADE
	|| NowVer == CG_MY_VERSION_PUK3
#endif
	){

#endif
		if(otherVer == CG_OTHER_1){
			ret = graNo;
		}else if(otherVer == CG_OTHER_EX){
			ret = graNo;
		}else if(otherVer == CG_OTHER_2){
			ret = graNo;
		}else if(otherVer == CG_OTHER_RE){
			ret = graNo;
		}
#ifdef PUK2
		else if(otherVer <= CG_OTHER_PUK2_1 && otherVer <= CG_OTHER_PUK2_M){
			ret = graNo;
		}
#endif
#ifdef PUK3_UPGRADE
		else if(otherVer == CG_OTHER_PUK3_M){
			ret = graNo;
		}
#endif
/*
	}else if(NowVer == CG_MY_VERSION_EX || NowVer == CG_MY_VERSION_EXV){
		if(otherVer == CG_OTHER_1){
			ret = graNo;
		}else if(otherVer == CG_OTHER_EX){
			ret = graNo;
		}else if(otherVer == CG_OTHER_2){
			//影キャラ用の男か女をチェックする
			ret = getOtherSexCheck(graNo);
			
		}else if(otherVer == CG_OTHER_RE){
			//旧キャラ不表示のため５０００引く
			//（ＩＤ管理が狂うとやヴぁくなる）
			ret = graNo - 5000;
		}
*/
	}else{
		if(otherVer == CG_OTHER_1){
			ret = graNo;
		}else if(otherVer == CG_OTHER_EX){
			//影キャラ用の男か女をチェック
			ret = getOtherSexCheck(graNo);
		}else if(otherVer == CG_OTHER_2){
			//影キャラ用の男か女をチェックする
			ret = getOtherSexCheck(graNo);
		}else if(otherVer == CG_OTHER_RE){
			//旧キャラ表示のため５０００引く
			//（ＩＤ管理が狂うとやヴぁくなる）
			ret = graNo - 5000;
		}
#ifdef PUK2
		else if(CG_OTHER_PUK2_1 <= otherVer && otherVer <= CG_OTHER_PUK2_M){
			ret = getOtherSexCheck(graNo);
		}
#endif
#ifdef PUK3_UPGRADE
		else if(otherVer == CG_OTHER_PUK3_M){
			ret = getOtherSexCheck(graNo);
		}
#endif
	}
	return ret;
}

//-----------------------------------------------------------------------
///これより以下モンタージュ关系
//-----------------------------------------------------------------------
//************************************************
//相手の颜グラからクロゲバージョンを判别
//信用できるのか
//本当はサーバーから教えてもらうほうがいいかな
//とりあえずキャラ管理ＩＤ（ＨＴＭＬ）を信用する
//
//戾り值；クロゲバージョン上记enum参考
//************************************************
int getOtherFaceGraphicType(int graNo)
{
	int ret = CG_OTHER_1;

	//クロゲ１
	if(graNo >= 30000 && graNo <= 98000){
		//クロゲ１キャラ颜
		ret = CG_OTHER_1;
		
	}else if(graNo >= 202800 && graNo <= 204199){
		//XG2用リニューアルXG1プレイヤーキャラクター颜
		ret = CG_OTHER_RE;

	}else if(graNo >= 200000 && graNo <= 200799){
		//XGEX用キャラクター颜
		ret = CG_OTHER_EX;
		
	}else if(graNo >= 200800 && graNo <= 202799){
		//XG2キャラクター颜
		ret = CG_OTHER_2;
	}
#ifdef PUK2
	else if(PUK2_FACE_01 <= graNo && graNo < PUK2_FACE_14+100){
		//PUK2キャラクター颜
		ret = CG_OTHER_PUK2_1;
	}
	else if(PUK2_FACE_15 <= graNo && graNo < PUK2_FACE_28+100){
		//PUK2キャラクター颜
		ret = CG_OTHER_PUK2_2;
	}
#endif

	return ret;

}
//**************************************************
//影モンタージュ用に男か女をチェックする。
//
//戾り值：0:男　１:女
//
//**************************************************
static int getOtherFaceSexCheck(int graNo)
{

	if(graNo >= 200000 && graNo <= 200399){
		//Ex男キャラ
		return CG2_FACE_SHADOW_PCM;
	}else if(graNo >= 200800 && graNo <= 201799){
		//２の男キャラ
		return CG2_FACE_SHADOW_PCM;
	}else if(graNo >= 200400 && graNo <= 200799){
		//Ex女キャラ
		return CG2_FACE_SHADOW_PCF;
	}else if(graNo >= 201800 && graNo <= 202799){
		//Ex女キャラ
		return CG2_FACE_SHADOW_PCF;
	}
#ifdef PUK2
	else if(PUK2_FACE_01 <= graNo && graNo < PUK2_FACE_07+100){
		//PUK2男キャラ
		return CG2_FACE_SHADOW_PCM;
	}
	else if(PUK2_FACE_08 <= graNo && graNo < PUK2_FACE_14+100){
		//PUK2女キャラ
		return CG2_FACE_SHADOW_PCF;
	}
	else if(PUK2_FACE_15 <= graNo && graNo < PUK2_FACE_21+100){
		//PUK2男キャラ
		return CG2_FACE_SHADOW_PCM;
	}
	else if(PUK2_FACE_22 <= graNo && graNo < PUK2_FACE_28+100){
		//PUK2女キャラ
		return CG2_FACE_SHADOW_PCF;
	}
#endif

	return -1;
}

//************************************************
//リニューアルモンタージュ → 旧キャラモンタジュ
// 变换
//この式でいけるはず
//int graNo 颜番号
//
//戾り值：旧モンタージュ番号
//
//************************************************
int getOldFaceGraphic(int graNo)
{
#if 0	
	int work;
	int BaseOldGraNo = 30000;
	int show, amari, amari2;
	int total = graNo;
	
	//リニューアル以外はリターン
	if(graNo < 202800 || graNo > 204199) return graNo;
	
	work = graNo - 202800;
	show = work / 25;	//商を得る
	amari = work % 25;	//余りを得る
	amari2 = amari % 5; //余りの余り
	
	//この式でリニューアルから旧キャラに变换
	total = BaseOldGraNo + (25 * show * 40) +( amari * 2)- amari2;

	//女キャラ12000プラス
	if(total >= 58000) total = total + 12000;
#else
	int work;
	int BaseOldGraNo = 30000;
	int base, tmpColor, tmpEye, tmpMouth;
	int total = graNo;
	
	//リニューアル以外はリターン
	if(graNo < 202800 || graNo > 204199) return graNo;
	
	work = graNo - 202800;
	base = work / 100;	//商を得る
		work = work % 100;	//余りを得る
	tmpColor = work / 25;	//商を得る
		work = work % 25;	//余りを得る
	tmpEye = work / 5;		 //余りの余り
	tmpMouth = work % 5;	//余りを得る
	
	// ゲンとトブの问题のために Exchange する
	if( base == 3 ){
		base = 4;
	}else
	if( base == 4 ){
		base = 3;
	}
	//この式でリニューアルから旧キャラに变换
	total = BaseOldGraNo + (base * 4000) +( tmpColor * 1000) + tmpEye * 10 + tmpMouth;

	//女キャラ12000プラス
	if(total >= 58000) total = total + 12000;
#endif
	return total;
}

//************************************************
// 旧キャラモンタジュ → リニューアルモンタジュ
// 变换
//この式でいけるはず
//int graNo 颜番号
//
//戾り值：リニューアルモンタージュ番号
//
//************************************************
int getReFaceGraphic(int graNo)
{
	
#if 0
	int work;
	int BaseReGraNo = 202800;
	int show, amari;
	int total;
	int j;
	int base;
	
	//旧キャラモンタ以外はそのままリターン
	if( !(graNo >= 30000 && graNo <= 97999) ) return graNo;
	
	//女モンタジュは１２０００引く
	if(graNo > 70000)  graNo = graNo - 12000;

	//变换计算
	work = graNo - 30000;
	base = work / 1000;	
	base = base * 1000;

	show = base / 40;	

	j = work % 1000;
	
	work = j / 10 ;
	work = (work *10) /2;
	amari = j % 10;
	
	work = show + work + amari;
	
	total = BaseReGraNo + work;
#else
	int tmpColor, tmpEye, tmpMouth;
	int work, base, total;
	int BaseReGraNo = 202800;

	//旧キャラモンタ以外はそのままリターン
	if( !(graNo >= 30000 && graNo <= 97999) ) return graNo;
	
	//女モンタジュは１２０００引く
	if(graNo >= 70000)  graNo = graNo - 12000;

	//变换计算
	work = graNo - 30000;	// OFFセット３万を引く
	base = work / 4000;		// ４千で割った商が人の番号
		work = work - base*4000;
	tmpColor = work/1000;	// さらに１０００で割ると色が出。
		work = work - tmpColor * 1000;
	tmpEye = work / 10;	// さらに１０割ると目が出。
	// 口が出
	tmpMouth = work - tmpEye * 10;

	// ゲンとトブの问题のために Exchange する
	if( base == 3 ){
		base = 4;
	}else
	if( base == 4 ){
		base = 3;
	}
	total = base * 100 + tmpColor*25 + tmpEye*5 + tmpMouth + BaseReGraNo;
#endif
	return 	total;
	
}

//************************************************
//モンタージュ画像を对应させる
//int graNo		モンタージュ画像番号
//
//戾り值：
//		新モンタージュ番号
//************************************************
int getNewFaceGraphicNo(int graNo)
{

	int NowVer;
	int otherVer;
	int ret = graNo;
	
	//现在の自分のバージョン
	NowVer = CG2PackageVer;

	//クライアント引数を优先する
	if(CG2PackageVer == 255){
		NowVer = PackageVer;
	}

	//颜画像番号からバージョンを判别
	otherVer = getOtherFaceGraphicType( graNo);

#ifdef PUK2
	//自分のバージョンでチェック
	if ( NowVer >= CG_MY_VERSION_PUK2 ) NowVer = CG_OTHER_PUK2_M;
	else if ( NowVer >= CG_MY_VERSION_EX ) NowVer = CG_OTHER_RE;
	else NowVer = CG_OTHER_1;

	if (NowVer >= otherVer){
#else

	//自分のバージョンでチェック
	if(NowVer == CG_MY_VERSION_2 
	|| NowVer == CG_MY_VERSION_2V 
	|| NowVer == CG_MY_VERSION_EX 
	|| NowVer == CG_MY_VERSION_EXV
#ifdef PUK2
	|| NowVer == CG_MY_VERSION_PUK2
#endif
	){

#endif
		if(otherVer == CG_OTHER_1){
			ret = graNo;
		}else if(otherVer == CG_OTHER_EX){
			ret = graNo;
		}else if(otherVer == CG_OTHER_2){
			ret = graNo;
		}else if(otherVer == CG_OTHER_RE){
			ret = graNo;
		}
#ifdef PUK2
		else if(otherVer <= CG_OTHER_PUK2_1 && otherVer <= CG_OTHER_PUK2_M){
			ret = graNo;
		}
#endif
/*
	}else if(NowVer == CG_MY_VERSION_EX || NowVer == CG_MY_VERSION_EXV){
		if(otherVer == CG_OTHER_1){
			ret = graNo;
		}else if(otherVer == CG_OTHER_EX){
			ret = graNo;
		}else if(otherVer == CG_OTHER_2){
			//影キャラ用の男か女をチェックする
			ret = getOtherFaceSexCheck(graNo);
			
		}else if(otherVer == CG_OTHER_RE){
			//旧キャラ表示の计算
			//（ＩＤ管理が狂うとやヴぁくなる）
			ret = getOldFaceGraphic( graNo);
		}
*/
	}else{
		if(otherVer == CG_OTHER_1){
			ret = graNo;
		}else if(otherVer == CG_OTHER_EX){
			//影キャラ用の男か女をチェック
			ret = getOtherFaceSexCheck(graNo);
		}else if(otherVer == CG_OTHER_2){
			//影キャラ用の男か女をチェックする
			ret = getOtherFaceSexCheck(graNo);
		}else if(otherVer == CG_OTHER_RE){
			//旧キャラ表示の计算
			//（ＩＤ管理が狂うとやヴぁくなる）
			ret = getOldFaceGraphic( graNo);
		}
#ifdef PUK2
		else if(otherVer == CG_OTHER_PUK2_1 ){
			if ( NowVer == CG_OTHER_1){
				ret = getOldFaceGraphic( getFaceGraphicFormPUK2(graNo) );
			}else{
				ret = getFaceGraphicFormPUK2(graNo);
			}
		}else if(otherVer == CG_OTHER_PUK2_2){
			if ( NowVer == CG_OTHER_1){
				ret = getOtherFaceSexCheck(graNo);
			}else{
				ret = getFaceGraphicFormPUK2(graNo);
			}
		}else if(otherVer == CG_OTHER_PUK2_M){
			ret = getOtherFaceSexCheck(graNo);
		}
#endif
	}
	return ret;
}


#endif

#ifdef PUK2
	#include "../puk2/newDraw/newpc.cpp"
#endif

