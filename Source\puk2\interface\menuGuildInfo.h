﻿#ifndef __MENU_GUILDINFO_H__
#define __MENU_GUILDINFO_H__

#include "../systeminc/guild.h"

GUILD_TITLE_INFO MenuWindowGuildInfoEditting[GUILD_TITLE_MAX + 2];

BOOL MenuWindowGuildInfo (int mouse);
BOOL MenuWindowGuildInfoDraw (int mouse);
BOOL MenuWindowGuildInfoClose (int no, unsigned int flag);
BOOL MenuWindowGuildInfoCheckBox (int no, unsigned int flag);
BOOL MenuWindowGuildInfoInputLine (int no, unsigned int flag);
BOOL MenuWindowGuildInfoScrollOneLine (int no, unsigned int flag);
BOOL MenuWindowGuildInfoSetInfo (int no, unsigned int flag);
BOOL MenuWindowGuildInfoScrollV (int no, unsigned int flag);
BOOL MenuWindowGuildInfoScrollWheel (int no, unsigned int flag);

#define GID_guildInfoCloseButtonOn 243000
#define GID_guildInfoCloseButtonOff 243001
#define GID_guildInfoCloseButtonOver 243002
#define GID_guildInfoSetButtonOn 243054
#define GID_guildInfoSetButtonOff 243055
#define GID_guildInfoSetButtonOver 243056
#define GID_guildInfoScrollUpArrowOn 243090
#define GID_guildInfoScrollUpArrowOff 243091
#define GID_guildInfoScrollUpArrowOver 243092
#define GID_guildInfoScrollTab 243096
#define GID_guildInfoScrollDownArrowOn 243093
#define GID_guildInfoScrollDownArrowOff 243094
#define GID_guildInfoScrollDownArrowOver 243095
#define GID_guildInfoTitleDeleteOn 244218
#define GID_guildInfoTitleDeleteOff 244219
#define GID_guildInfoTitleDeleteOver 244220
#define GID_guildInfoTitleRenameOn 244215
#define GID_guildInfoTitleRenameOff 244216
#define GID_guildInfoTitleRenameOver 244217
#define GID_guildInfoTitleBar 244212
#define GID_guildInfoMasterMark 244213
#define GID_guildInfoCheckBoxOn 244214
#define GID_guildInfoCheckBoxOff 244221
//#define GID_guildInfoCheckBoxOff GID_guildInfoCloseButtonOver 

#define GID_guildInfoFrame 244210
#define GID_guildInfoNet 244211

GRAPHIC_SWITCH MenuWindowGuildInfoGraph [] = {
	{GID_guildInfoCheckBoxOff, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoCloseButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoSetButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoScrollUpArrowOn, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoScrollTab, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoScrollDownArrowOn, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoTitleDeleteOn, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoTitleRenameOn, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoTitleBar, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoMasterMark, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoFrame, 0, 0, 0, 0, 0xffffffff},
	{GID_guildInfoNet, 0, 0, 0, 0, 0x80ffffff}};

TEXT_SWITCH MenuWindowGuildInfoText [] = {
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "guild"}};

BUTTON_SWITCH MenuWindowGuildInfoButton[] = {
	{0, 0}};
	

enum {
	EnumMenuWindowGuildInfoDlg0,		// guildName
	EnumMenuWindowGuildInfoDlg1,		// guildRoom

	EnumMenuWindowGuildInfoDlg2,
	EnumMenuWindowGuildInfoDlg3,
	EnumMenuWindowGuildInfoDlg4,
	EnumMenuWindowGuildInfoDlg5,
	EnumMenuWindowGuildInfoDlg6,
	EnumMenuWindowGuildInfoDlg7,
	EnumMenuWindowGuildInfoDlg8,

	EnumMenuWindowGuildInfoGuildName,
	EnumMenuWindowGuildInfoRoomName,

	EnumMenuWindowGuildInfoTitle0,
	EnumMenuWindowGuildInfoTitle1,
	EnumMenuWindowGuildInfoTitle2,
	EnumMenuWindowGuildInfoTitle3,
	EnumMenuWindowGuildInfoTitle4,
	EnumMenuWindowGuildInfoTitle5,
	EnumMenuWindowGuildInfoTitle6,

	EnumMenuWindowGuildInfoCheckBox0,
	EnumMenuWindowGuildInfoCheckBox1,
	EnumMenuWindowGuildInfoCheckBox2,
	EnumMenuWindowGuildInfoCheckBox3,
	EnumMenuWindowGuildInfoCheckBox4,
	EnumMenuWindowGuildInfoCheckBox5,
	EnumMenuWindowGuildInfoCheckBox6,
	EnumMenuWindowGuildInfoCheckBox7,
	EnumMenuWindowGuildInfoCheckBox8,
	EnumMenuWindowGuildInfoCheckBox9,
	EnumMenuWindowGuildInfoCheckBox10,
	EnumMenuWindowGuildInfoCheckBox11,
	EnumMenuWindowGuildInfoCheckBox12,
	EnumMenuWindowGuildInfoCheckBox13,
	EnumMenuWindowGuildInfoCheckBox14,
	EnumMenuWindowGuildInfoCheckBox15,
	EnumMenuWindowGuildInfoCheckBox16,
	EnumMenuWindowGuildInfoCheckBox17,
	EnumMenuWindowGuildInfoCheckBox18,
	EnumMenuWindowGuildInfoCheckBox19,
	EnumMenuWindowGuildInfoCheckBox20,
	EnumMenuWindowGuildInfoCheckBox21,
	EnumMenuWindowGuildInfoCheckBox22,
	EnumMenuWindowGuildInfoCheckBox23,
	EnumMenuWindowGuildInfoCheckBox24,
	EnumMenuWindowGuildInfoCheckBox25,
	EnumMenuWindowGuildInfoCheckBox26,
	EnumMenuWindowGuildInfoCheckBox27,
	EnumMenuWindowGuildInfoCheckBox28,
	EnumMenuWindowGuildInfoCheckBox29,
	EnumMenuWindowGuildInfoCheckBox30,
	EnumMenuWindowGuildInfoCheckBox31,
	EnumMenuWindowGuildInfoCheckBox32,
	EnumMenuWindowGuildInfoCheckBox33,
	EnumMenuWindowGuildInfoCheckBox34,

	EnumMenuWindowGuildInfoMasterMark, 

	EnumMenuWindowGuildInfoMonsterName0,
	EnumMenuWindowGuildInfoMonsterName1,
	EnumMenuWindowGuildInfoMonsterName2,

	EnumMenuWindowGuildInfoTitleBar0,
	EnumMenuWindowGuildInfoTitleBar1,
	EnumMenuWindowGuildInfoTitleBar2,
	EnumMenuWindowGuildInfoTitleBar3,
	EnumMenuWindowGuildInfoTitleBar4,
	EnumMenuWindowGuildInfoTitleBar5,
	EnumMenuWindowGuildInfoTitleBar6,

	EnumMenuWindowGuildInfoGuildNameSet,
	EnumMenuWindowGuildInfoGuildRoomSet,

	EnumMenuWindowGuildInfoScrollUpArrow,
	EnumMenuWindowGuildInfoScrollDownArrow,
	EnumMenuWindowGuildInfoScrollTab,
	EnumMenuWindowGuildInfoScrollBar,
	EnumMenuWindowGuildInfoScrollWheel,

	EnumMenuWindowGuildInfoCloseButton,
	EnumMenuWindowGuildInfoRenameButton,
	EnumMenuWindowGuildInfoFrame,
	EnumMenuWindowGuildInfoNet,

	EnumMenuWindowGuildInfoHit1,

	EnumMenuWindowGuildInfoEnd,
};

	

static SWITCH_DATA MenuWindowGuildInfoSwitch[] = {
	{SWITCH_DIALOG,141-25,  48-17, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG,141-25,  60-11, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 187-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 206-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 225-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 244-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 263-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 282-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_DIALOG, 60-22, 301-24, 12 * 12, 14, TRUE, NULL, MenuWindowGuildInfoInputLine},
	{SWITCH_TEXT,  141-25,  48-17, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,  141-25,  60-11, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 187-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 206-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 225-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 244-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 263-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 282-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,   60-22, 301-22, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},


	{SWITCH_GRAPHIC,  226-25, 189-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 189-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 189-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 189-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 189-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  226-25, 208-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 208-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 208-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 208-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 208-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  226-25, 227-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 227-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 227-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 227-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 227-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  226-25, 246-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 246-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 246-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 246-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 246-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  226-25, 265-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 265-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 265-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 265-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 265-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  226-25, 284-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 284-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 284-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 284-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 284-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  226-25, 303-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  245-25, 303-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  264-25, 303-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  283-25, 303-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,  302-25, 303-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,   40-22, 187-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[9], MenuSwitchNone},
	{SWITCH_TEXT,     141-25,  90-20, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,     141-25, 110-20, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_TEXT,     141-25, 130-20, 0, 0, TRUE, &MenuWindowGuildInfoText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 187-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 206-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 225-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 244-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 263-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 282-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,   38-22, 301-26, 0, 0, TRUE, &MenuWindowGuildInfoGraph[8], MenuSwitchNone},
	{SWITCH_GRAPHIC,  285-12,  43-15, 32, 17, FALSE, &MenuWindowGuildInfoGraph[2], MenuWindowGuildInfoSetInfo},
	{SWITCH_GRAPHIC,  285-12,  61-15, 32, 17, FALSE, &MenuWindowGuildInfoGraph[2], MenuWindowGuildInfoSetInfo},

	{SWITCH_GRAPHIC,  325-26, 190-27, 11, 11, TRUE, &MenuWindowGuildInfoGraph[3], MenuWindowGuildInfoScrollOneLine},
	{SWITCH_GRAPHIC,  325-26, 307-27, 11, 11, TRUE, &MenuWindowGuildInfoGraph[5], MenuWindowGuildInfoScrollOneLine},
	{SWITCH_GRAPHIC,  325-26, 200-27, 0, 0, TRUE, &MenuWindowGuildInfoGraph[4], MenuSwitchNone},
	{SWITCH_BUTTON,   324-26, 200-27, 10, 94, TRUE, &MenuWindowGuildInfoButton[0], MenuWindowGuildInfoScrollV},
	{SWITCH_NONE,	   0,  0, 317,325, TRUE, NULL, MenuWindowGuildInfoScrollWheel },								// マウスホイール判定
	{SWITCH_GRAPHIC,  337-38,   6+3, 11, 11, TRUE, &MenuWindowGuildInfoGraph[1], MenuWindowGuildInfoClose},
	{SWITCH_GRAPHIC,  315-50, 340-44, 57, 17, FALSE, &MenuWindowGuildInfoGraph[2], MenuWindowGuildInfoSetInfo},
	{SWITCH_GRAPHIC,  0, 0, 0, 0, TRUE, &MenuWindowGuildInfoGraph[10], MenuSwitchNone},
	{SWITCH_GRAPHIC,  12, 27, 0, 0, TRUE, &MenuWindowGuildInfoGraph[11], MenuSwitchNone},

	{ SWITCH_NONE  , 317, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ
};

const WINDOW_DATA WindowDataGuildInfo = {
	0, 
		4, 100, 60, 317, 325, 0x80010101, EnumMenuWindowGuildInfoEnd, MenuWindowGuildInfoSwitch, MenuWindowGuildInfo, MenuWindowGuildInfoDraw,MenuWindowDel
};

static WINDOW_DRAGMOVE DragMoveDataGuildInfo = {
	2,
	0, 0, 320, 25,
	317, 0, 20, 100
};
#endif 	

