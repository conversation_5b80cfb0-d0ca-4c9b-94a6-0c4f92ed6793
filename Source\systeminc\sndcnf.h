﻿#ifndef _SNDCNF_H_
#define _SNDCNF_H_

enum
{
	SE_NO_CLICK			= 51,					// クリック音
	SE_NO_CLICK2		= 52,					// クリック音２
	SE_NO_OK			= 53,					// 决定ａ
	SE_NO_BACK			= 54,					// 返回音
	SE_NO_NG			= 55,					// ＮＧ音（短い）
	SE_NO_OPEN_WINDOW	= 56,					// ウィンドウ开く音
	SE_NO_CLOSE_WINDOW	= 57,					// ウィンドウ闭まる音
	SE_NO_PAGE_CHANGE	= 58,					// ページ切り替え音
	SE_NO_LOGIN			= 59,					// ログイン音
	SE_NO_LOGOUT		= 60,					// 登出音
	SE_NO_WARP			= 61,					// ワープａ音
	SE_NO_WARP2			= 62,					// ワープｂ音
	SE_NO_MAIL_RECEIVE	= 63,					// メール受信音
	SE_NO_LEVEL_UP		= 64,					// 等级アップ
	SE_NO_DROP_GOLD		= 65,					// お金を落とす音_a
	SE_NO_DROP_GOLD2	= 66,					// お金を落とす音_b
	SE_NO_DROP_GOLD3	= 67,					// お金を落とす音_c
	SE_NO_TAKE_GOLD		= 68,					// お金を拾う音
	SE_NO_DROP_ITEM		= 69,					// アイテムを落とす音
	SE_NO_PARTY			= 71,					// 布ティに入る音
	SE_NO_BREAKUP		= 72,					// 布ティを拔ける音（解散）
	SE_NO_CARD_CHANGE	= 73,					// 名刺交换音
	SE_NO_CHAT_ROLL		= 74,					// チャットの表示时音
	SE_NO_TELEPHONE		= 75,					// 电话音
	SE_NO_TREASURE_OPEN	= 76,					// 宝箱を开けたときの音
	SE_NO_USE_ITEM		= 77,					// アイテム使用音
	SE_NO_OK2			= 78,					// 决定音b（ボタンクリック时）
	SE_NO_OK3			= 79					// 决定音c（文字等クリック时）
};

#endif


#ifdef USE_SOUNDCONFIG
//　番号、ファイル名、ボリューム、ピッチ（半音单位）、ループフラグ（环境音）、マップエディタ等で见るコメント
//（实装可能なものは、コメントの最初に『●』、修正要请中のものは『▲』、假实装のものは何もなし。）

SNDCONFIG sndconfig[] =
{
	// 环境SE　SE番号1～50
	//XG1用
	{    1, "se\\cgnat00.wav", 80, 0, 1 /*, "●川の流れる音（小川程度）"*/ },
	{    2, "se\\cgnat01.wav", 80, 0, 1 /*, "●川の流れる音（少し大きめ）"*/ },
	{    3, "se\\cgnat02.wav", 80, 0, 1 /*, "●海の音"*/ },
	{    4, "se\\cgnat03.wav", 80, 0, 1 /*, "●風が吹く音"*/ },
	{    5, "se\\cgnat04.wav", 85, 0, 1 /*, "●地面が揺れる音"*/ },
	{    6, "se\\cgnat05a.wav", 95, 0, 1 /*, "●水滴が落ちる音_a"*/ },
	{    7, "se\\cgnat05b.wav", 100, 0, 1 /*, "●水滴が落ちる音_b"*/ },
	{    8, "se\\cgnat06a.wav", 80, 0, 1 /*, "●虫が鳴く音_a"*/ },
	{    9, "se\\cgnat06b.wav", 90, 0, 1 /*, "●虫が鳴く音_b"*/ },
	{   10, "se\\cgnat07.wav", 80, 0, 1 /*, "●小鳥のさえずり音"*/ },
	{   11, "se\\cgnat08.wav", 80, 0, 1 /*, "●鳥が鳴く音（トンビ等）"*/ },
	{   12, "se\\cgnat09.wav", 70, 0, 1 /*, "●草が風に揺れる音"*/ },
	{   13, "se\\cgnat10.wav", 70, 0, 1 /*, "●焚き火の音"*/ },
	{   14, "se\\cgnat11.wav", 70, 0, 1 /*, "●鍋が煮えている音"*/ },
	// XG2用
	{   15, "se\\exnat00.wav", 100, 0, 1 /*, "船用波"*/ },
	{   16, "se\\v2nat00.wav", 100, 0, 1 /*, "雨雲音"*/ },
//9/5追加分
	{   17,"se\\34sand_clock.wav", 100, 0, 1 /*, "	龙の砂时计（机械っぽい念りの音）	*/ },	
	{   18,"se\\35sand_clock.wav", 100, 0, 1 /*, "	上宫（上の少し軽いもの）	*/ },		
	{   19,"se\\36wind.wav", 100, 0, 1 /*, "	ひょうたんが风になる音（カラカラ）	*/ },	
	{   20,"se\\37bird.wav", 100, 0, 1 /*, "	小鸟のさえずり	*/ },					
#ifdef PUK3
	{   21,"se\\puk3_Wind01.wav", 100, 0, 1 /**/ },
	{   22,"se\\puk3_Wind02.wav", 100, 0, 1 /**/ },
	{   23,"se\\puk3_Wind03.wav", 100, 0, 1 /**/ },
	{   24,"se\\puk3_gaya01.wav", 100, 0, 1 /**/ },

	{   25,"se\\puk3_drop01.wav", 100, 0, 1 /**/ },
	{   26,"se\\puk3_drop02.wav", 100, 0, 1 /**/ },
#endif
	// システムSE　SE番号51～100
	{   SE_NO_CLICK,		"se\\cgsys00.wav", 100, 0, 0 /*, "●クリック音（ボタンを押し下げる）"*/ },
	{   SE_NO_CLICK2,		"se\\cgsys01.wav", 110, 0, 0 /*, "●クリック音（ボタンを押し上げる）"*/ },
	{   SE_NO_OK,			"se\\cgsys02.wav", 80, 0, 0 /*, "●決定音a（キャラ作成画面）"*/ },
	{   SE_NO_BACK,			"se\\cgsys03.wav", 100, 0, 0 /*, "●戻る音（キャラ作成画面）"*/ },
	{   SE_NO_NG,			"se\\cgsys04.wav", 100, 0, 0 /*, "●警告音"*/ },
	{   SE_NO_OPEN_WINDOW,	"se\\cgsys05.wav", 105, 0, 0 /*, "●ウインドウを开启音"*/ },
	{   SE_NO_CLOSE_WINDOW,	"se\\cgsys06.wav", 117, 0, 0 /*, "●ウインドウを关闭音"*/ },
	{   SE_NO_PAGE_CHANGE,	"se\\cgsys07.wav", 115, 0, 0 /*, "●ウインドウのページ切り替え音"*/ },
	{   SE_NO_LOGIN,		"se\\cgsys08.wav", 100, 0, 0 /*, "●ログイン音"*/ },
	{   SE_NO_LOGOUT,		"se\\cgsys09.wav", 100, 0, 0 /*, "●登出音"*/ },
	{   SE_NO_WARP,			"se\\cgsys10a.wav", 127, 0, 0 /*, "●ワープするときの音_a"*/ },
	{   SE_NO_WARP2,		"se\\cgsys10b.wav", 127, 0, 0 /*, "●ワープするときの音_b"*/ },
	{   SE_NO_MAIL_RECEIVE,	"se\\cgsys11.wav",  90, 0, 0 /*, "●メール着信音"*/ },
	{   SE_NO_LEVEL_UP,		"se\\cgsys12.wav",  95, 0, 0 /*, "●等级アップ音"*/ },
	{   SE_NO_DROP_GOLD,	"se\\cgsys13a.wav", 100, 0, 0 /*, "●お金を落とす音_a"*/ },
	{   SE_NO_DROP_GOLD2,	"se\\cgsys13b.wav", 100, 0, 0 /*, "●お金を落とす音_b"*/ },
	{   SE_NO_DROP_GOLD3,	"se\\cgsys13c.wav", 105, 0, 0 /*, "●お金を落とす音_c"*/ },
	{   SE_NO_TAKE_GOLD,	"se\\cgsys14.wav", 115, 0, 0 /*, "●お金を拾う音"*/ },
	{   SE_NO_DROP_ITEM,	"se\\cgsys15.wav", 115, 0, 0 /*, "●アイテムを落とす音"*/ },
	
//仕样变更により不要	{   70, "datase\\cgsys16.wav", 123, 0, 0 /*, "アイテムを拾う音"*/ },
	{   SE_NO_PARTY,		"se\\cgsys17.wav",  95, 0, 0 /*, "●布ティに入る音"*/ },
	{   SE_NO_BREAKUP,		"se\\cgsys18.wav",  90, 0, 0 /*, "●布ティを抜ける音（解散）"*/ },
	{   SE_NO_CARD_CHANGE,	"se\\cgsys19.wav", 120, 0, 0 /*, "●名刺交換音"*/ },
	{   SE_NO_CHAT_ROLL,	"se\\cgsys20.wav", 110, 0, 0 /*, "●チャットの表示時音"*/ },
	{   SE_NO_TELEPHONE,	"se\\cgsys21.wav", 100, 0, 0 /*, "●電話音"*/ },
	{   SE_NO_TREASURE_OPEN,"se\\cgsys22.wav", 110, 0, 0 /*, "●宝箱を開けたときの音"*/ },
	{   SE_NO_USE_ITEM,		"se\\cgsys23.wav", 100, 0, 0 /*, "●アイテム使用音"*/ },
	{   SE_NO_OK2,			"se\\cgsys24.wav", 100, 0, 0 /*, "●決定音b（ボタンクリック時）"*/ },
	{   SE_NO_OK3,			"se\\cgsys25.wav",  90, 0, 0 /*, "●決定音c（文字等クリック時）"*/ },

	// プレイヤー用SE　SE番号101～150
		// 振り音　SE番号101～130
	{  101, "se\\cgply00a.wav", 100, 0, 0 /*, "●素式K瘠暌簦ǜ咭簦?*/ },
	{  102, "se\\cgply00b.wav", 100, 0, 0 /*, "●素式K瘠暌簦ǖ鸵簦?*/ },
	{  103,	"se\\cgply01a.wav", 100, 0, 0 /*, "●剣を振る音（高音）"*/ },
	{  104,	"se\\cgply01b.wav", 105, 0, 0 /*, "●剣を振る音（低音）"*/ },
	{  105,	"se\\cgply02a.wav", 110, 0, 0 /*, "●斧を振る音（高音）"*/ },
	{  106,	"se\\cgply02b.wav", 115, 0, 0 /*, "●斧を振る音（低音）"*/ },
	{  107,	"se\\cgply03a.wav",  85, 0, 0 /*, "●槍を振る音（高音）"*/ },
	{  108,	"se\\cgply03b.wav",  95, 0, 0 /*, "●槍を振る音（低音）"*/ },
	{  109,	"se\\cgply04a.wav", 115, 0, 0 /*, "●杖を振る音（高音）"*/ },
	{  110,	"se\\cgply04b.wav", 115, 0, 0 /*, "●杖を振る音（高音）"*/ },
	{  111,	"se\\cgply05a.wav", 100, 0, 0 /*, "●投掷音（高音）"*/ },
	{  112,	"se\\cgply05b.wav", 100, 0, 0 /*, "●投掷音（低音）"*/ },
	{  113,	"se\\cgply06a1.wav", 100, 0, 0 /*, "●弓をひく音（高音）"*/ },
	{  114,	"se\\cgply06b1.wav", 100, 0, 0 /*, "●弓をひく音（低音）"*/ },
	{  115,	"se\\cgply06a2.wav", 100, 0, 0 /*, "●弓矢を放つ音（高音）"*/ },
	{  116,	"se\\cgply06b2.wav", 100, 0, 0 /*, "●弓矢を放つ音（低音）"*/ },
	{  117,	"se\\cgply07a.wav", 100, 0, 0 /*, "●ブーメランを投掷音（高音）"*/ },
	{  118,	"se\\cgply07b.wav", 100, 0, 0 /*, "●ブーメランを投掷音（低音）"*/ },
		// 攻击HIT音　SE番号131～150
	{  131,	"se\\cgply10a.wav", 100, 0, 0 /*, "●素手攻撃HIT音（高音）"*/ },
	{  132,	"se\\cgply10b.wav",  95, 0, 0 /*, "●素手攻撃HIT音（低音）"*/ },
	{  133,	"se\\cgply11a.wav", 110, 0, 0 /*, "●剣攻撃HIT音（高音）"*/ },
	{  134,	"se\\cgply11b.wav", 110, 0, 0 /*, "●剣攻撃HIT音（低音）"*/ },
	{  135,	"se\\cgply12a.wav", 105, 0, 0 /*, "●斧攻撃HIT音（高音）"*/ },
	{  136,	"se\\cgply12b.wav", 110, 0, 0 /*, "●斧攻撃HIT音（低音）"*/ },
	{  137,	"se\\cgply13a.wav", 100, 0, 0 /*, "●槍攻撃HIT音（高音）"*/ },
	{  138,	"se\\cgply13b.wav",  95, 0, 0 /*, "●槍攻撃HIT音（低音）"*/ },
	{  139,	"se\\cgply14a.wav", 110, 0, 0 /*, "●杖攻撃HIT音（高音）"*/ },
	{  140,	"se\\cgply14b.wav", 105, 0, 0 /*, "●杖攻撃HIT音（低音）"*/ },
	{  141,	"se\\cgply15.wav", 105, 0, 0 /*, "●ナイフ攻撃HIT音"*/ },
	{  142,	"se\\cgply16.wav",  85, 0, 0 /*, "●弓攻撃HIT音"*/ },
	{  143,	"se\\cgply17.wav", 110, 0, 0 /*, "●ブーメラン攻撃HIT音"*/ },

	// モンスター用SE　SE番号151～200
	{  151,	"se\\cgmon00a.wav", 100, 0, 0 /*, "●ワーキャット（鳴き声）_a"*/ },
	{  152,	"se\\cgmon00b.wav", 100, 0, 0 /*, "●ワーキャット（鳴き声）_b"*/ },
	{  153,	"se\\cgmon01.wav", 115, 0, 0 /*, "●熊（吠える声）"*/ },
	{  154,	"se\\cgmon02a.wav", 85, 0, 0 /*, "●カーバンクル（鳴き声）_a"*/ },
	{  155,	"se\\cgmon02b.wav", 80, 0, 0 /*, "●カーバンクル（鳴き声）_b"*/ },
	{  156,	"se\\cgmon03b.wav", 100, 0, 0 /*, "●ケルベロス（遠吠え）"*/ },
	{  157,	"se\\cgmon10.wav", 100, 0, 0 /*, "●ゾンビ（うなり声）"*/ },
	{  158,	"se\\cgmon20.wav",  90, 0, 0 /*, "●リトルガーゴイル（鳴き声）"*/ },
	{  159,	"se\\cgmon24.wav",  90, 0, 0 /*, "●コウモリ（鳴き声）"*/ },
	{  160,	"se\\cgmon30.wav",  85, 0, 0 /*, "●スコーピON（移動音）"*/ },
	{  161,	"se\\cgmon31.wav",  85, 0, 0 /*, "●ホーネット（移動音）"*/ },
	{  162,	"se\\cgmon41.wav",  90, 0, 0 /*, "●マンドレイク（鞭を打つ音）"*/ },
	{  163,	"se\\cgmon43.wav",  90, 0, 0 /*, "●サボテン（マラカス音）"*/ },
	{  164,	"se\\cgmon50a.wav", 120, 0, 0 /*, "●スライム_a"*/ },
	{  165,	"se\\cgmon50b.wav", 100, 0, 0 /*, "●スライム_b"*/ },
	{  166,	"se\\cgmon51.wav", 100, 0, 0 /*, "●ウインディーネ（氷の弾ける音）"*/ },
	{  167,	"se\\cgmon52.wav", 100, 0, 0 /*, "●ウィルオーウィスプ"*/ },
	{  168,	"se\\cgmon60.wav",  95, 0, 0 /*, "●ブランディッシュ（剣を抜く音）"*/ },
	{  169,	"se\\cgmon61.wav", 100, 0, 0 /*, "●ミミック（箱の中に何かがあるような音）"*/ },
//仕样变更により不要	{  170,	"datase\\cgmon62.wav", 120, 0, 0 /*, "ゴーレム（うなり声）"*/ },
	{  171,	"se\\cgmon63.wav", 100, 0, 0 /*, "●クラブ"*/ },
	{  172,	"se\\cgmon90.wav", 105, 0, 0 /*, "●ドラゴン（うなり声）"*/ },
	{  173,	"se\\cgmon91.wav", 115, 0, 0 /*, "●ヴァンパイア"*/ },
	{  174,	"se\\cgmon92.wav", 110, 0, 0 /*, "●ミノタウルス（鼻息）"*/ },
	{  175,	"se\\cgmon93.wav", 100, 0, 0 /*, "●デュラハン"*/ },
	{  176,	"se\\cgmon03a.wav", 100, 0, 0 /*, "●ケルベロス（吠える声）"*/ },
	// ボス用音　SE番号180～187
	{  180,	"se\\cgmon_bs1.wav", 112, 0, 0 /*, "●ボス用素式K瘠暌?*/ },
	{  181,	"se\\cgmon_bs2.wav", 117, 0, 0 /*, "●ボス用剣を振る音"*/ },
	{  182,	"se\\cgmon_bs3.wav", 127, 0, 0 /*, "●ボス用斧を振る音"*/ },
	{  183,	"se\\cgmon_bs4.wav", 107, 0, 0 /*, "●ボス用槍を振る音"*/ },
	{  184,	"se\\cgmon_bh1.wav", 107, 0, 0 /*, "●ボス用素手攻撃HIT音"*/ },
	{  185,	"se\\cgmon_bh2.wav", 122, 0, 0 /*, "●ボス用剣攻撃HIT音"*/ },
	{  186,	"se\\cgmon_bh3.wav", 122, 0, 0 /*, "●ボス用斧攻撃HIT音"*/ },
	{  187,	"se\\cgmon_bh4.wav", 107, 0, 0 /*, "●ボス用槍攻撃HIT音"*/ },
	// 羽ばたき音　SE番号190～194
	{  190,	"se\\cgmon_m00.wav",  90, 0, 0 /*, "●羽ばたき音（軽い、早い）"*/ },
	{  191,	"se\\cgmon_m01.wav", 100, 0, 0 /*, "●羽ばたき音（中間）"*/ },
	{  192,	"se\\cgmon_m02.wav", 100, 0, 0 /*, "●羽ばたき音（重い、ゆっくり）"*/ },
	// モンスター用サンプルSE　SE番号198～200
	{  198,	"se\\cgmon_sample01.wav", 100, 0, 0 /*, "●サンプル_01"*/ },
	{  199,	"se\\cgmon_sample02.wav",  90, 0, 0 /*, "●サンプル_02"*/ },
	{  200,	"se\\cgmon_sample03.wav", 100, 0, 0 /*, "●サンプル_03"*/ },

	// バトルSE　SE番号201～250
	{  201,	"se\\cgbtl00.wav",  90, 0, 0 /*, "●エンカウント音"*/ },
	{  202,	"se\\cgbtl01.wav",  85, 0, 0 /*, "●時間のカウントダウン音"*/ },
//仕样变更により不要	{  203,	"datase\\cgbtl02.wav", 100, 0, 0 /*, "攻撃をかわす音"*/ },
	{  204,	"se\\cgbtl03.wav", 110, 0, 0 /*, "●モンスターを出す音"*/ },
	{  205,	"se\\cgbtl04.wav", 110, 0, 0 /*, "●モンスターを引っ込める音"*/ },
	{  206,	"se\\cgbtl05.wav",  80, 0, 0 /*, "●スキルの等级アップ音"*/ },
	{  207,	"se\\cgbtl06.wav", 100, 0, 0 /*, "●アルティメットでの吹っ飛び音_a"*/ },
	{  208,	"se\\cgbtl07.wav", 100, 0, 0 /*, "●アルティメットでの吹っ飛び音_b"*/ },
	{  209,	"se\\cgbtl08.wav", 100, 0, 0 /*, "●武器交換時の音"*/ },
	{  210,	"se\\cgbtl09.wav", 100, 0, 0 /*, "●ポジションチェンジ音"*/ },
	{  211,	"se\\cgbtl10.wav", 110, 0, 0 /*, "●逃げる成功時の音"*/ },
	{  212,	"se\\cgbtl11.wav", 127, 0, 0 /*, "●封印時カード音"*/ },
	{  213,	"se\\cgbtl12.wav", 100, 0, 0 /*, "●モンスター封印音"*/ },
	{  214,	"se\\cgbtl13.wav", 100, 0, 0 /*, "●装備破壊音"*/ },
	{  215,	"se\\cgbtl14.wav", 100, 0, 0 /*, "●気絶時の音"*/ },
	{  216,	"se\\cgbtl15.wav", 100, 0, 0 /*, "●ブーメランキャッチ音"*/ },
	{  217,	"se\\cgbtl16.wav", 100, 0, 0 /*, "●アイテム発動音"*/ },
	{  218,	"se\\cgbtl17.wav", 100, 0, 0 /*, "●敵消滅音"*/ },

	// エフェクトSE　SE番号251～295
	{  251,	"se\\cgefc00.wav", 100, 0, 0 /*, "●受伤スキル集光パターン音"*/ },
	{  252,	"se\\cgefc01.wav", 100, 0, 0 /*, "●護衛マーク音"*/ },
	{  253,	"se\\cgefc02.wav", 100, 0, 0 /*, "●クロスカウンター音"*/ },
	{  254,	"se\\cgefc03.wav", 100, 0, 0 /*, "●気功弾発射音"*/ },
	{  255,	"se\\cgefc04.wav", 100, 0, 0 /*, "●気功弾HIT音"*/ },
	{  256,	"se\\cgefc05.wav", 115, 0, 0 /*, "精神統一音"*/ },
	{  257,	"se\\cgefc06.wav", 100, 0, 0 /*, "●盗む成功音"*/ },
	{  258,	"se\\cgefc07.wav", 100, 0, 0 /*, "●盗む失敗音"*/ },
	{  259,	"se\\cgefc08.wav", 115, 0, 0 /*, "●攻撃系魔術集光パターン音"*/ },
	{  260,	"se\\cgefc09.wav",  90, 0, 0 /*, "●補助系魔術集光パターン音"*/ },
	{  261,	"se\\cgefc10.wav", 100, 0, 0 /*, "●回復系魔術集光パターン音"*/ },
	{  262,	"se\\cgefc11.wav", 100, 0, 0 /*, "●地属性攻撃魔術発射音"*/ },
	{  263,	"se\\cgefc12.wav", 100, 0, 0 /*, "●地属性攻撃魔術HIT音"*/ },
	{  264,	"se\\cgefc13.wav", 100, 0, 0 /*, "●水属性攻撃魔術発射音"*/ },
//仕样变更により不要	{  265,	"se\\cgefc14.wav", 100, 0, 0 /*, "水属性攻撃魔術HIT音"*/ },
	{  266,	"se\\cgefc15.wav", 110, 0, 0 /*, "●火属性攻撃魔術発射音"*/ },
	{  267,	"se\\cgefc16.wav", 115, 0, 0 /*, "●火属性攻撃魔術HIT音"*/ },
	{  268,	"se\\cgefc17.wav",  90, 0, 0 /*, "●風属性攻撃魔術発射音"*/ },
	{  269,	"se\\cgefc18.wav",  90, 0, 0 /*, "●風属性攻撃魔術HIT音"*/ },
	{  270,	"se\\cgefc19.wav",  95, 0, 0 /*, "●ドレイン（受伤）音"*/ },
	{  271,	"se\\cgefc20.wav",  90, 0, 0 /*, "●ドレイン（吸収）音"*/ },
	{  272,	"se\\cgefc21.wav", 120, 0, 0 /*, "●状态異常魔術音"*/ },
	{  273,	"se\\cgefc22.wav",  90, 0, 0 /*, "●毒受伤音"*/ },
	{  274,	"se\\cgefc23.wav",  90, 0, 0 /*, "●酔い受伤音"*/ },
	{  275,	"se\\cgefc24.wav", 110, 0, 0 /*, "●属性優遇音"*/ },
	{  276,	"se\\cgefc25.wav", 100, 0, 0 /*, "●属性反転音"*/ },
	{  277,	"se\\cgefc26.wav", 120, 0, 0 /*, "●防御系魔術実行音"*/ },
	{  278,	"se\\cgefc27.wav",  90, 0, 0 /*, "●体力回復音"*/ },
	{  279,	"se\\cgefc28.wav", 105, 0, 0 /*, "●体力再生音"*/ },
	{  280,	"se\\cgefc29.wav",  95, 0, 0 /*, "●気絶回復音"*/ },
	{  281,	"se\\cgefc30.wav",  80, 0, 0 /*, "●状态異常回復音"*/ },
	{  282,	"se\\cgefc31.wav", 100, 0, 0 /*, "●旧自爆音"*/ },
	{  283,	"se\\cgefc32.wav", 110, 0, 0 /*, "サクリファイス音"*/ },
	{  284,	"se\\cgefc33.wav", 100, 0, 0 /*, "●即死攻撃音"*/ },
	{  285,	"se\\cgefc34.wav", 100, 0, 0 /*, "●反射音"*/ },
	{  286,	"se\\cgefc35.wav", 100, 0, 0 /*, "●無効音"*/ },
	{  287,	"se\\cgefc36.wav", 110, 0, 0 /*, "●カウンター発動音"*/ },
	{  288,	"se\\cgefc37a.wav", 115, 0, 0 /*, "●自爆音（拡大時）"*/ },
	{  289,	"se\\cgefc37b.wav", 100, 0, 0 /*, "●自爆音（ビリビリ時）"*/ },
	{  290,	"se\\cgefc37c.wav", 110, 0, 0 /*, "●自爆音（爆発時）"*/ },
	{  291,	"se\\cgefc38.wav",  90, 0, 0 /*, "●再生魔術実行音"*/ },

	//XG2モンスター用SE　SE番号296～319
	{  296,	"se\\v2monex1.wav", 100, 0, 0 /*, "●剣チャージ音"*/ },
	{  297,	"se\\v2monex2.wav", 100, 0, 0 /*, "●ウマ（蹄）"*/ },
	{  298,	"se\\v2monex3.wav", 100, 0, 0 /*, "●盾倒下"*/ },

	{  300,	"se\\v2mon100.wav", 100, 0, 0 /*, "●ウマ（鳴き声）"*/ },
	{  301,	"se\\v2mon110.wav", 100, 0, 0 /*, "●死神（鎌音）"*/ },
	{  302,	"se\\v2mon111a.wav", 100, 0, 0 /*, "●顎骨（歯が擦りあう音a）"*/ },
	{  303,	"se\\v2mon111b.wav", 100, 0, 0 /*, "●顎骨（歯が擦りあう音b）"*/ },
	{  304,	"se\\v2mon120.wav", 100, 0, 0 /*, "●グリフォン（鳴き声）"*/ },
	{  305,	"se\\v2mon121a.wav", 100, 0, 0 /*, "●トリ（足音a）"*/ },
	{  306,	"se\\v2mon121b.wav", 100, 0, 0 /*, "●トリ（足音b）"*/ },
	{  307,	"se\\v2mon121c.wav", 100, 0, 0 /*, "●トリ（足音c）"*/ },
	{  308,	"se\\v2mon130.wav", 100, 0, 0 /*, "●クワガタ（はさみ音）"*/ },
	{  309,	"se\\v2mon140.wav", 100, 0, 0 /*, "●キノコ（胞子発射音）"*/ },
//020924现在未使用のためコメント	{  310,	"se\\v2mon150a.wav", 100, 0, 0 /*, "●雲（夕立前雷音）"*/ },
//020924现在未使用のためコメント	{  311,	"se\\v2mon150b.wav", 100, 0, 0 /*, "●雷（雷鳴）"*/ },
	{  312,	"se\\v2mon161.wav", 115, 0, 0 /*, "●岩（岩が転がる音）"*/ },
//020924现在未使用のためコメント	{  313,	"se\\v2mon170a.wav", 100, 0, 0 /*, "●ヒドラ（鳴き声a）"*/ },
	{  314,	"se\\v2mon170b.wav", 100, 0, 0 /*, "●ヒドラ（鳴き声b）"*/ },
	{  315,	"se\\v2mon171a.wav", 100, 0, 0 /*, "●パピー（鳴き声a)"*/ },
	{  316,	"se\\v2mon171b.wav", 100, 0, 0 /*, "●パピー（鳴き声b）"*/ },
	{  317,	"se\\v2mon190.wav", 100, 0, 0 /*, "●キメラ（ライON鳴き声）"*/ },
	{  318,	"se\\v2mon191.wav", 100, 0, 0 /*, "●悪魔（ビーム音）"*/ },
	{  319,	"se\\v2monex0.wav", 100, 0, 0 /*, "●ビリビリ音"*/ },

#ifdef PUK2
//ポンスビ社による追加
//小
{  400,"se\\01small_amae.wav", 100, 0, 0 /*, "	泣き声：甘え	*/ },
{  401,"se\\02small_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  402,"se\\03small_iyaiya.wav", 100, 0, 0 /*, "	泣き声：嫌がり	*/ },
//鱼
{  403,"se\\04fish_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  404,"se\\05fish_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
{  405,"se\\06fish_amae.wav", 100, 0, 0 /*, "	泣き声：甘え	*/ },
//龟
{  406,"se\\07kame_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  407,"se\\08kame_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
{  408,"se\\09kame_amae.wav", 100, 0, 0 /*, "泣き声：甘え	*/ },
//獣
{  409,"se\\11yagi_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  410,"se\\10yagi_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
{  411,"se\\12yagi_amae.wav", 100, 0, 0 /*, "	泣き声：甘え	*/ },
//鸟
{  412,"se\\13bird_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  413,"se\\14bird_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
{  414,"se\\15bird_amae.wav", 100, 0, 0 /*, "	泣き声：甘え	*/ },
//大鱼
{  415,"se\\16fish_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  416,"se\\17fish_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
//大龟
{  417,"se\\18kame_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  418,"se\\19kame_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
//大獣
{  419,"se\\20animal_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  420,"se\\21animal_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },
//大鸟
{  421,"se\\22bird_normal.wav", 100, 0, 0 /*, "	泣き声：通常	*/ },
{  422,"se\\23bird_shout.wav", 100, 0, 0 /*, "	泣き声：雄叫び	*/ },

{  423,"se\\24Monstor.wav", 100, 0, 0 /*, "	出现ジングル（曲、公会宠物が出现中の短いもの）	*/ },
{  424,"se\\25_1_off.wav", 100, 0, 0 /*, "	消失效果音	*/ },
{  425,"se\\26ground_on.wav", 100, 0, 0 /*, "	リバースon	*/ },
{  426,"se\\27ground_off.wav", 100, 0, 0 /*, "	リバースoff	*/ },
{  427,"se\\28_water_on.wav", 100, 0, 0 /*, "	リバースon	*/ },
{  428,"se\\29_water_of.wav", 100, 0, 0 /*, "	リバースoff	*/ },
{  429,"se\\30fire_on.wav", 100, 0, 0 /*, "	リバースon	*/ },
{  430,"se\\31fire_of.wav", 100, 0, 0 /*, "	リバースoff	*/ },
{  431,"se\\32_wind_on.wav", 100, 0, 0 /*, "	リバースon	*/ },
{  432,"se\\33_wind_off.wav", 100, 0, 0 /*, "	リバースoff	*/ },

{  433,"se\\34sand_clock.wav", 100, 0, 0 /*, "	龙の砂时计（机械っぽい念りの音）	*/ },	//ダミーです。实际は使用されません。
{  434,"se\\35sand_clock.wav", 100, 0, 0 /*, "	上宫（上の少し軽いもの）	*/ },			//ダミーです。实际は使用されません。
{  435,"se\\36wind.wav", 100, 0, 0 /*, "	ひょうたんが风になる音（カラカラ）	*/ },		//ダミーです。实际は使用されません。
{  436,"se\\37bird.wav", 100, 0, 0 /*, "	小鸟のさえずり	*/ },							//ダミーです。实际は使用されません。

{  437,"se\\38make_gild.wav", 100, 0, 0 /*, "	家族结成ジングル	*/ },
{  438,"se\\39levelup.wav", 100, 0, 0 /*, "	等级アップジングル	*/ },

//マスコさんがサービスで入れてくれた音
{  439,"se\\cloud0_amae.wav", 100, 0, 0 /**/ },
{  440,"se\\cloud0_normal.wav", 100, 0, 0 /**/ },
{  441,"se\\cloud0_shout.wav", 100, 0, 0 /**/ },
{  442,"se\\fish2_amae.wav", 100, 0, 0 /**/ },

{  443,"se\\fish2_normal.wav", 100, 0, 0 /**/ },		
{  444,"se\\fish2_shout.wav", 100, 0, 0 /**/ },			
{  445,"se\\32_wind_on2.wav", 100, 0, 0 /**/ },			
{  446,"se\\09kame_amae2.wav", 100, 0, 0 /**/ },		
{  447,"se\\08kame_shout2.wav", 100, 0, 0 /**/ },

{  448,"se\\01small_amae_new.wav", 100, 0, 0 /**/ },
{  449,"se\\02small_normal_new.wav", 100, 0, 0 /**/ },
{  450,"se\\03small_iyaiya_new.wav", 100, 0, 0 /**/ },
#endif

#ifdef PUK3
//ＰＵＫ３以降のＳＥは以下に追加。
{  451,"se\\puk3_Wind01.wav", 100, 0, 0 /**/ },
{  452,"se\\puk3_Wind02.wav", 100, 0, 0 /**/ },
{  453,"se\\puk3_Wind03.wav", 100, 0, 0 /**/ },
{  454,"se\\puk3_kujira01.wav", 100, 0, 0 /**/ },
{  455,"se\\puk3_kujira02.wav", 100, 0, 0 /**/ },
{  456,"se\\puk3_gaya01.wav", 100, 0, 0 /**/ },
{  457,"se\\puk3_landing01.wav", 100, 0, 0 /**/ },
{  458,"se\\puk3_landing02.wav", 100, 0, 0 /**/ },
{  459,"se\\puk3_landing03.wav", 100, 0, 0 /**/ },
{  460,"se\\puk3_landing04.wav", 100, 0, 0 /**/ },
{  461,"se\\puk3_landing05.wav", 100, 0, 0 /**/ },
{  462,"se\\puk3_landing06.wav", 100, 0, 0 /**/ },
{  463,"se\\puk3_jump01.wav", 100, 0, 0 /**/ },
{  464,"se\\puk3_jump02.wav", 100, 0, 0 /**/ },
{  465,"se\\puk3_jump03.wav", 100, 0, 0 /**/ },
{  466,"se\\puk3_jump04.wav", 100, 0, 0 /**/ },
{  467,"se\\puk3_drop01.wav", 100, 0, 0 /**/ },
{  468,"se\\puk3_drop02.wav", 100, 0, 0 /**/ },
#endif

	{ -1 }	// 終端记号（消さないように）
};

#endif
