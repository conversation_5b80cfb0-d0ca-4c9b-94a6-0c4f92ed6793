﻿#ifndef __MENUMAIL_H__
#define __MENUMAIL_H__

BOOL MenuWindowMailHistory (int mouse);
BOOL MenuWindowMailHistoryDraw (int mouse);
BOOL MenuWindowMailHistoryPrevPage (int no, unsigned int flag);
BOOL MenuWindowMailHistoryNextPage (int no, unsigned int flag);

BOOL MenuWindowMailHistorySend (int no, unsigned int flag);

BOOL MenuWindowMailHistoryBase (int no, unsigned int flag);

void MenuWindowMailHistoryRedrawContent ();
void MenuWindowMailHistoryRedraw (void);

#ifdef PetField
BOOL checkPetField(int);
#endif
#ifdef PUK3_MONSTER_HELPER_MMLOCK
BOOL checkPetHelp(int index);
#endif

#define GID_mailHistoryCloseOn 243000
#define GID_mailHistoryCloseOff 243001
#define GID_mailHistoryCloseOver 243002
#define GID_mailHistoryPagePrevOn 243030
#define GID_mailHistoryPagePrevOff 243031
#define GID_mailHistoryPagePrevOver 243032
#define GID_mailHistoryPageNextOn 243033
#define GID_mailHistoryPageNextOff 243034
#define GID_mailHistoryPageNextOver 243035
#define GID_mailHistorySendMailBack 244230
#define GID_mailHistorySendMailBase 244231
#define GID_mailHistoryPetButtonOn 244232
#define GID_mailHistoryPetButtonOff 244233
#define GID_mailHistoryPetButtonOver 244234
#define GID_mailHistorySendButtonOn 244251
#define GID_mailHistorySendButtonOff 244252
#define GID_mailHistorySendButtonOver 244253
#define GID_mailHistoryBackButtonOn 244242
#define GID_mailHistoryBackButtonOff 244243
#define GID_mailHistoryBackBUttonOver 244244
#define GID_mailHistoryPetMailBack 244237
#define GID_mailHistoryPetMailBase 244238
#define GID_mailHistoryPetCharBase 243139
#define GID_mailHistorySendMailContent 244235
#define GID_mailHistoryHistoryContent 244236

GRAPHIC_SWITCH MenuWindowMailHistoryGraph [] = {
	{GID_mailHistorySendButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_mailHistoryCloseOn, 0, 0, 0, 0, 0xffffffff},
	{GID_mailHistoryPagePrevOn, 0, 0, 0, 0, 0xffffffff},
	{GID_mailHistoryPageNextOn, 0, 0, 0, 0, 0xffffffff},
	{GID_mailHistoryHistoryContent, 0, 0, 0, 0, 0xffffffff},
	{GID_mailHistorySendMailBack, 0, 0, 0, 0, 0xffffffff},
	{GID_mailHistorySendMailBase, 0, 0, 0, 0, 0x80ffffff},
};

TEXT_SWITCH MenuWindowMailHistoryText [] = {
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "content"},                      //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "page"},                         //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "name"},                         //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "00/00 00:00"}};                 //MLHIDE

static SWITCH_DATA MenuWindowMailHistorySwitch[] = {

	//クローズ
	{SWITCH_GRAPHIC, 226-35, 9, 11, 11, TRUE, &MenuWindowMailHistoryGraph[1], MenuSwitchCloseButton},
	//センド
	{SWITCH_GRAPHIC, 169-15, 37-10, 49, 17, TRUE, &MenuWindowMailHistoryGraph[0], MenuWindowMailHistorySend},
	//←
	{SWITCH_GRAPHIC, 30-15, 190-10, 18, 18, TRUE, &MenuWindowMailHistoryGraph[2], MenuWindowMailHistoryPrevPage},
	//→
	{SWITCH_GRAPHIC, 92-15, 190-10, 18, 18, TRUE, &MenuWindowMailHistoryGraph[3], MenuWindowMailHistoryNextPage},

	//メールの内容
	{SWITCH_TEXT, 40-15, 62-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 74-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 86-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 98-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 110-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 122-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 134-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 146-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 158-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},
	{SWITCH_TEXT, 40-15, 170-10, 0, 0, TRUE, &MenuWindowMailHistoryText[0], MenuSwitchNone},

	//メールのナンバー
	{SWITCH_TEXT, 50-15, 193-10, 0, 0, TRUE, &MenuWindowMailHistoryText[1], MenuSwitchNone},

	//相手の名称
	{SWITCH_TEXT, 33-15, 40-10, 0, 0, TRUE, &MenuWindowMailHistoryText[2], MenuSwitchNone},

	//履历情报
	{SWITCH_TEXT, 130-15, 190-10, 0, 0, TRUE, &MenuWindowMailHistoryText[3], MenuSwitchNone},

#ifdef PUK3_MAIL_ETC
	{SWITCH_GRAPHIC, 31-15, 55-10, 0, 0, TRUE, &MenuWindowMailHistoryGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 230, 210, TRUE, &MenuWindowMailHistoryGraph[5], MenuWindowMailHistoryBase},
	{SWITCH_GRAPHIC, 13, 25, 0, 0, TRUE, &MenuWindowMailHistoryGraph[6], MenuSwitchNone},

	{SWITCH_NONE,   207,  0,  25,100, TRUE, NULL, MenuSwitchDelMouse },											// ドラッグ用
};
#else
	{SWITCH_GRAPHIC, 31-15, 55-10, 0, 0, TRUE, &MenuWindowMailHistoryGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 230, 210, TRUE, &MenuWindowMailHistoryGraph[5], MenuWindowMailHistoryBase},
	{SWITCH_GRAPHIC, 13, 25, 0, 0, TRUE, &MenuWindowMailHistoryGraph[6], MenuSwitchNone}};
#endif

enum {
	EnumGraphMailHistorySend,
	EnumGraphMailHistoryClose,
	EnumGraphMailHistoryPrevPage,
	EnumGraphMailHistoryNextPage,
	EnumGraphMailHistoryContent0,
	EnumGraphMailHistoryContent1,
	EnumGraphMailHistoryContent2,
	EnumGraphMailHistoryContent3,
	EnumGraphMailHistoryContent4,
	EnumGraphMailHistoryContent5,
	EnumGraphMailHistoryContent6,
	EnumGraphMailHistoryContent7,
	EnumGraphMailHistoryContent8,
	EnumGraphMailHistoryContent9,

	EnumGraphMailHistoryNumber,
	EnumGraphMailHistoryName,
	EnumGraphMailHistoryDate,

	EnumGraphMailHistoryWin,
	EnumGraphMailHistoryBack,
	EnumGraphMailHistoryBase,
#ifdef PUK3_MAIL_ETC
	EnumGraphMailHistoryDrag,
#endif

	EnumGraphMailHistoryEnd

};

const WINDOW_DATA WindowDataMenuMailHistory = {
	0,
#ifdef PUK3_MAIL_ETC
		4, 383, 71, 211, 206, 0x80010101, EnumGraphMailHistoryEnd, MenuWindowMailHistorySwitch, MenuWindowMailHistory, MenuWindowMailHistoryDraw,MenuWindowDel
#else
		4, 383, 71, 267, 223, 0x80010101, EnumGraphMailHistoryEnd, MenuWindowMailHistorySwitch, MenuWindowMailHistory, MenuWindowMailHistoryDraw,MenuWindowDel
#endif
};

static WINDOW_DRAGMOVE DragMoveDataHistoryMail = {
	2,
	0, 0, 267, 25,
#ifdef PUK3_MAIL_ETC
	207,  0,25, 100,
#else
	247, 0, 20, 100
#endif
};


//----------------------------------------------------------------------------------------------
//送信用ウインドウ（杉山）
//----------------------------------------------------------------------------------------------
void InitMenuWindowSendMailInLogin(void);
#ifdef PUK3_MAIL_ETC
	// 前回状况で起动
	void ReOpenMailWindow();
#endif

BOOL MenuWindowSendMail( int mouse );
BOOL MenuWindowSendMailDraw( int mouse );
BOOL MenuWindowSendPetMail( int mouse );
BOOL MenuWindowSendPetMailDraw( int mouse );
BOOL MenuWindowSendMailDel (void);

BOOL MenuSwitchSendMail( int no, unsigned int flag );
BOOL MenuSwitchSendMailDialog( int no, unsigned int flag );
BOOL MenuSwitchPetMailGetItem( int no, unsigned int flag );
BOOL MenuSwitchSendPetMail( int no, unsigned int flag );

struct MailStatusStruct{
	int Mode;
	int Index;
	int SendMailMonsterNo;
	int SendItem;
	int GroupMailFlag;
	char MailBuff[600];
#ifdef PUK3_MAIL_ETC
	BOOL ReOpenFlag;		// 画面切り替え时リオープンする场合にセットされる
							// メールウィンドウが开くときに使用され、初期化される
#endif
};

//通常メール

GRAPHIC_SWITCH MenuWindowSendMailWindowGraph[]={

	{GID_SendMailFrame,0,0,0,0,0xFFFFFFFF},				//フレーム
	{GID_SendMailWindow,0,0,0,0,0xFFFFFFFF},			//ウインドウ
	{GID_SendMailBack,0,0,0,0,0x80FFFFFF},				//バック

	{GID_SendMailFrame,0,0,0,0,0xFFFFFFFF},				//フレーム
	{GID_SendMailPetMailWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ
	{GID_SendMailPetMailBack,0,0,0,0,0x80FFFFFF},		//バック

	{GID_SendMailPetMailOn,0,0,0,0,0xFFFFFFFF},			//ペットメールへ
	{GID_SendMailSendOn,0,0,0,0,0xFFFFFFFF},			//メール送信
	{GID_SendMailPetMailBackOn,0,0,0,0,0xFFFFFFFF},		//バック

	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},			//クローズボタン
	{GID_SendMailPetMailItemPanel,0,0,0,0,0xFFFFFFFF},	//アイテムパネル

	{GID_SendMailPetMailDownOn,0,0,0,0,0xFFFFFFFF},	//<<
	{GID_SendMailPetMailUpOn,0,0,0,0,0xFFFFFFFF},	//>>

};

TEXT_SWITCH SendMailText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"てすとさん"},                          //MLHIDE
};
 
// スイッチ
static SWITCH_DATA SendMailWindowSwitch[] = {

{ SWITCH_GRAPHIC,225-34,  9,  11, 11, TRUE, &MenuWindowSendMailWindowGraph[ 9], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_DIALOG,  33-11, 84-14, 170,120, TRUE, NULL,	 MenuSwitchSendMailDialog },						//ダイアログ表示

{ SWITCH_GRAPHIC, 28-12, 39-11,  56, 16, TRUE, &MenuWindowSendMailWindowGraph[ 6], MenuSwitchSendMail },	//ペットメールへ
{ SWITCH_GRAPHIC,169-15, 39-11,  50, 18, TRUE, &MenuWindowSendMailWindowGraph[ 7], MenuSwitchSendMail },	//メール送信

{ SWITCH_TEXT	, 32-11, 61-13,   0,  0, TRUE, &SendMailText[ 0], MenuSwitchNone },							//相手の名称

	
{ SWITCH_GRAPHIC, 25-9, 60-14,   0,  0, TRUE, &MenuWindowSendMailWindowGraph[ 0], MenuSwitchNone },			//メール用フレーム
{ SWITCH_GRAPHIC,  0,  0,0, 0, TRUE, &MenuWindowSendMailWindowGraph[ 1], MenuSwitchNone },				//メール用ウインドウ
{ SWITCH_GRAPHIC, 10+4, 20+3,   0,  0, TRUE, &MenuWindowSendMailWindowGraph[ 2], MenuSwitchNone },			//メール用バック
#ifdef PUK3_MAIL_ETC
{ SWITCH_NONE,   207,  0,  25,100, TRUE, NULL, MenuSwitchDelMouse },										// ドラッグ用
#endif

};

enum{
	EnumGraphSendMailClose,

	EnumDialogSendMailMessage,

	EnumGraphSendMailToPetMail,
	EnumGraphSendMailSend,

	EnumTextSendMailToName,


	EnumGraphSendMailFrame,
	EnumGraphSendMailWindow,

	EnumGraphSendMailBack,
#ifdef PUK3_MAIL_ETC
	EnumGraphSendMailDrag,
#endif

	
	EnumSendMailEnd,
};


const WINDOW_DATA WindowDataSendMailWindow = {
 0,															// メニューウィンドウ
#ifdef PUK3_MAIL_ETC
     4,383, 81,211,210,0x80010101,EnumSendMailEnd,SendMailWindowSwitch, MenuWindowSendMail,MenuWindowSendMailDraw,MenuWindowSendMailDel 
#else
     4,383, 81,232,210,0x80010101,EnumSendMailEnd,SendMailWindowSwitch, MenuWindowSendMail,MenuWindowSendMailDraw,MenuWindowSendMailDel 
#endif
};

//ペットメール

// スイッチ
static SWITCH_DATA SendPetMailWindowSwitch[] = {
{ SWITCH_GRAPHIC,225-34,  9,  11, 11, TRUE, &MenuWindowSendMailWindowGraph[ 9], MenuSwitchCloseButton },//クローズボタン

{ SWITCH_DIALOG,  33, 84, 170,120, TRUE, NULL,	 MenuSwitchSendMailDialog },								//ダイアログ表示

{ SWITCH_GRAPHIC, 28-13, 39-10,  56, 16, TRUE, &MenuWindowSendMailWindowGraph[ 8], MenuSwitchSendPetMail },		//バック
{ SWITCH_GRAPHIC,169-15, 39-10,  50, 18, TRUE, &MenuWindowSendMailWindowGraph[ 7], MenuSwitchSendPetMail },		//メール送信

{ SWITCH_GRAPHIC,169-18,215-16,   0,  0, TRUE, &MenuWindowSendMailWindowGraph[10], MenuSwitchNone },				//添付するアイテム
{ SWITCH_GRAPHIC,169-18,215-16,  50, 50, TRUE, &MenuWindowSendMailWindowGraph[10], MenuSwitchPetMailGetItem },	//アイテムパネル

{ SWITCH_TEXT	, 32-11, 61-12,   0,  0, TRUE, &SendMailText[ 0], MenuSwitchNone },								//相手の名称

{ SWITCH_TEXT	, 28-12,215-16,   0,  0, TRUE, &SendMailText[ 0], MenuSwitchNone },								//モンスターの名称
{ SWITCH_GRAPHIC, 28-12,240-16,  13, 11, TRUE, &MenuWindowSendMailWindowGraph[11], MenuSwitchSendPetMail },		//←
{ SWITCH_GRAPHIC, 78-12,240-16,  13, 11, TRUE, &MenuWindowSendMailWindowGraph[12], MenuSwitchSendPetMail },		//→

	
{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowSendMailWindowGraph[ 4], MenuSwitchNone },				//ウインドウ
{ SWITCH_GRAPHIC, 13, 26,   0,  0, TRUE, &MenuWindowSendMailWindowGraph[ 5], MenuSwitchNone },				//バック
#ifdef PUK3_MAIL_ETC
{ SWITCH_NONE,   207,  0,  25,100, TRUE, NULL, MenuSwitchDelMouse },											// ドラッグ用
#endif

};

enum{
	EnumGraphSendPetMailClose,

	EnumDialogSendPetMailMessage,

	EnumGraphSendPetMailToSendMail,
	EnumGraphSendPetMailSend,

	EnumGraphSendPetMailSendItem,
	EnumGraphSendPetMailItemPanel,

	EnumTextSendPetMailToName,

	EnumTextSendPetMailMonsterName,
	EnumGraphSendPetMailDown,
	EnumGraphSendPetMailUp,

	EnumGraphSendPetMailWindow,
	EnumGraphSendPetMailBack,
#ifdef PUK3_MAIL_ETC
	EnumGraphSendPetMailDrag,
#endif
	
	EnumSendPetMailEnd,
};


const WINDOW_DATA WindowDataSendPetMailWindow = {
 0,															// メニューウィンドウ
#ifdef PUK3_MAIL_ETC
     4, 383, 81,211,259,0x80010101,EnumSendPetMailEnd,SendPetMailWindowSwitch, MenuWindowSendPetMail,MenuWindowSendPetMailDraw,MenuWindowSendMailDel 
#else
     4, 383, 81,232,259,0x80010101,EnumSendPetMailEnd,SendPetMailWindowSwitch, MenuWindowSendPetMail,MenuWindowSendPetMailDraw,MenuWindowSendMailDel 
#endif
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSendMailWindow={
	2,
	 0,  0,232, 30,
	207,  0,25, 100,
};

#endif
