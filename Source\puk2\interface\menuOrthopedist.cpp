﻿//======================================
// 整形外科医ウィンドウ
//======================================

static int Or_CardGra;
static int Or_CardVer;
static int Or_CardColor;
static int Or_CardEye;
static int Or_CardMouth;
static int Or_NPCGra;
static char Or_NPCName[50] = {"NPCNAME"};
static char Or_Message[2][46] = {"MESSAGE","MESSAGE"};

static int Or_CardVerMax;
static int Or_CardVerMin;

// サーバー情报
int Or_WinType;
int Or_SeqNo;
int Or_ObjIndex;

int GetCharaFaceGra( int l_base, int l_ver, int l_color, int l_eye, int l_mouth )
{
	int i;

	switch(l_ver){
	case 0:
		i = l_base;
		i =	getOldFaceGraphic( getFaceGraphicFormPUK2(i) );
		i += l_color * 1000 +
			 l_eye * 10 +
			 l_mouth;
		break;
	case 1:
		i = l_base;
		i = getFaceGraphicFormPUK2(i);
		i += l_color * 25 +
			 l_eye * 5 +
			 l_mouth;
		break;
	case 2:
		i = l_base;
		i += l_color * 25 +
			 l_eye * 5 +
			 l_mouth;
		break;
	}

	return i;
}

void GetCharaFaceGraData( int l_graNo, int *l_base, int *l_ver, int *l_color, int *l_eye, int *l_mouth )
{
	int i;
	int ver;

	// 颜画像番号からバージョンを判别
	ver = getOtherFaceGraphicType(l_graNo);
	(*l_ver) = 0;
	if ( ver >= CG_OTHER_EX ) (*l_ver) = 1;
	if ( ver >= CG_OTHER_PUK2_1 ) (*l_ver) = 2;

	// PUK2絵に变换
	i = getFaceGraphicToPUK2( getReFaceGraphic(l_graNo) );

	// 各データを取得
	(*l_mouth) = i % 5;
	i -= *l_mouth;
	(*l_eye) = ( i / 5 ) % 5;
	i -= (*l_eye) * 5;
	(*l_color) = ( i / 25 ) % 4;
	i -= (*l_color) * 25;

	(*l_base) = i;
}

//======================================
// ウィンドウ处理

ACTION *openOrthopedistWindow( int seqno, int objindex, char *data )
{
	char str[1024];
	int i,j;

	Or_WinType = WINDOW_MESSAGETYPE_ORTHOPEDIST;
	Or_SeqNo = seqno;
	Or_ObjIndex = objindex;

	j = 1;

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', j++ );

	// 店名
	getStringToken( data, '|', j++, 16, Or_NPCName );
	makeRecvString( Or_NPCName );

	// 信息
	getStringToken( data, '|', j++, sizeof(str) - 1 , str );
	makeRecvString(str);
	for(i=0;i<2;i++){
		getMemoLine( &Or_Message[i][0], sizeof( Or_Message[i] ), str, i, sizeof( Or_Message[i] )-1 );
	}

	return openMenuWindow( MENU_WINDOW_ORTHOPEDIST, OPENMENUWINDOW_HIT, 0 );
}

BOOL MenuWindowOrthopedistBf( int mouse )
{
	if ( mouse == WIN_INIT ){
		int NowVer;
		int puk2No;
		ACTION_SWITCH *pActSw = (ACTION_SWITCH *)wI->sw[EnumGraOrthopedistCharaGra].Switch;

		//现在の自分のバージョン
		NowVer = CG2PackageVer;
		if ( CG2PackageVer == 255 ) NowVer = PackageVer;

		puk2No = getFaceGraphicToPUK2( getReFaceGraphic(pc.faceGraNo) );
		Or_CardVerMin = 0;
		if ( puk2No >= PUK2_FACE_15 ) Or_CardVerMin = 1;
		Or_CardVerMax = 0;
		if ( NowVer >= PV_FIRST_VER2 ) Or_CardVerMax = 1;
		if ( NowVer >= PV_PUK2 ) Or_CardVerMax = 2;

		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraOrthopedistText1].Switch )->text, Or_Message[0] );
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraOrthopedistText2].Switch )->text, Or_Message[1] );

		wI->sw[EnumGraOrthopedistVerLeft].Enabled = FALSE;
		wI->sw[EnumGraOrthopedistVerText].Enabled = FALSE;
		wI->sw[EnumGraOrthopedistVerRight].Enabled = FALSE;
		if ( Or_CardVerMax-Or_CardVerMin > 0 ){
			wI->sw[EnumGraOrthopedistVerLeft].Enabled = TRUE;
			wI->sw[EnumGraOrthopedistVerText].Enabled = TRUE;
			wI->sw[EnumGraOrthopedistVerRight].Enabled = TRUE;
		}
		wI->sw[EnumGraOrthopedistEyesLeft].Enabled = TRUE;
		wI->sw[EnumGraOrthopedistEyesText].Enabled = TRUE;
		wI->sw[EnumGraOrthopedistEyesRight].Enabled = TRUE;
		wI->sw[EnumGraOrthopedistMouthLeft].Enabled = TRUE;
		wI->sw[EnumGraOrthopedistMouthText].Enabled = TRUE;
		wI->sw[EnumGraOrthopedistMouthRight].Enabled = TRUE;

		if ( Or_CardGra==CG2_FACE_SHADOW_PCM ||
			 Or_CardGra==CG2_FACE_SHADOW_PCF ){
			wI->sw[EnumGraOrthopedistVerLeft].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistVerText].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistVerRight].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistEyesLeft].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistEyesText].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistEyesRight].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistMouthLeft].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistMouthText].Enabled = FALSE;
			wI->sw[EnumGraOrthopedistMouthRight].Enabled = FALSE;
		}

		// 各部分のデータ取得
		GetCharaFaceGraData( pc.faceGraNo, &Or_CardGra,
			 &Or_CardVer, &Or_CardColor, &Or_CardEye, &Or_CardMouth );

		// 表示画像の设定
		( (GRAPHIC_SWITCH *)wI->sw[EnumGraOrthopedistCard].Switch )->graNo =
			 GetCharaFaceGra( Or_CardGra, Or_CardVer, Or_CardColor, Or_CardEye, Or_CardMouth );

		pActSw->ActionAdd->anim_chr_no = pc.graNo;
		pActSw->ActionAdd->anim_no = 0;
		pActSw->ActionAdd->anim_ang = 5;

		return TRUE;
	}

	return TRUE;
}

BOOL MenuWindowOrthopedistAf( int Mouse )
{
	// ウィンドウを开いた位置から数グリッド离れたら
	// 违うサーバーリクエストウィンドウが开いてるなら
	if ( checkMoveMapGridPos( 2, 2 ) ||
		 serverRequestWinWindowType != Or_WinType ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	displayMenuWindow();

	MenuWindowCommonDraw( GID_CommonWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );
	
	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

BOOL MenuWindowOrthopedistCl()
{
	if ( serverRequestWinWindowType == Or_WinType ){
		serverRequestWinWindowType = -1;
		Or_WinType = -1;
	}
	return TRUE;
}

//======================================
// ボタン处理

BOOL MenuSwitchOrthopedistOK( int no, unsigned int flag )
{
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	BOOL ReturnFlag = FALSE;

	if ( flag & MENU_MOUSE_LEFT ){
		char msg[50];

		// プロトコル送信
		sprintf( msg, "%d",
			 GetCharaFaceGra( Or_CardGra, Or_CardVer, Or_CardColor, Or_CardEye, Or_CardMouth ) 
			 );
		nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, GRL_PUSH_OK, msg );

		wI->flag |= WIN_INFO_DEL;

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BigOKButtonOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchOrthopedistVerChg( int no, unsigned int flag )
{
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	BOOL ReturnFlag = FALSE;

	if ( flag & MENU_MOUSE_LEFT ){
		if ( no < EnumGraOrthopedistVerText ) Or_CardVer--;
		else Or_CardVer++;

		if ( Or_CardVer < Or_CardVerMin ) Or_CardVer = Or_CardVerMax;
		if ( Or_CardVer > Or_CardVerMax ) Or_CardVer = Or_CardVerMin;

		( (GRAPHIC_SWITCH *)wI->sw[EnumGraOrthopedistCard].Switch )->graNo =
			 GetCharaFaceGra( Or_CardGra, Or_CardVer, Or_CardColor, Or_CardEye, Or_CardMouth );

		ReturnFlag=TRUE;
	}

	if ( no < EnumGraOrthopedistVerText ){
		Graph->graNo = GID_LeftButtonOn;
		if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
		if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;
	}else{
		Graph->graNo = GID_RightButtonOn;
		if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
		if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;
	}

	return ReturnFlag;
}

BOOL MenuSwitchOrthopedistEyeChg( int no, unsigned int flag )
{
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	BOOL ReturnFlag = FALSE;

	if ( flag & MENU_MOUSE_LEFT ){
		if ( no < EnumGraOrthopedistEyesText ) Or_CardEye--;
		else Or_CardEye++;

		if ( Or_CardEye < 0 ) Or_CardEye = 5 - 1;
		if ( Or_CardEye >= 5 ) Or_CardEye = 0;

		( (GRAPHIC_SWITCH *)wI->sw[EnumGraOrthopedistCard].Switch )->graNo =
			 GetCharaFaceGra( Or_CardGra, Or_CardVer, Or_CardColor, Or_CardEye, Or_CardMouth );

		ReturnFlag=TRUE;
	}

	if ( no < EnumGraOrthopedistEyesText ){
		Graph->graNo = GID_LeftButtonOn;
		if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
		if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;
	}else{
		Graph->graNo = GID_RightButtonOn;
		if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
		if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;
	}

	return ReturnFlag;
}

BOOL MenuSwitchOrthopedistMouthChg( int no, unsigned int flag )
{
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	BOOL ReturnFlag = FALSE;

	if ( flag & MENU_MOUSE_LEFT ){
		if ( no < EnumGraOrthopedistMouthText ) Or_CardMouth--;
		else Or_CardMouth++;

		if ( Or_CardMouth < 0 ) Or_CardMouth = 5 - 1;
		if ( Or_CardMouth >= 5 ) Or_CardMouth = 0;

		( (GRAPHIC_SWITCH *)wI->sw[EnumGraOrthopedistCard].Switch )->graNo =
			 GetCharaFaceGra( Or_CardGra, Or_CardVer, Or_CardColor, Or_CardEye, Or_CardMouth );

		ReturnFlag=TRUE;
	}

	if ( no < EnumGraOrthopedistMouthText ){
		Graph->graNo = GID_LeftButtonOn;
		if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
		if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;
	}else{
		Graph->graNo = GID_RightButtonOn;
		if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
		if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;
	}

	return ReturnFlag;
}

//====================================//
//		整形外科确认ウィンドウ		  //
//====================================//

struct ORCF_WIN{
	// サーバー情报
	int WinType;
	int SeqNo;
	int ObjIndex;

	int faceNo;

	// 泛用ウィンドウ情报
	char Msg[30][81];
} OrCfwin;

BOOL OrCf_ReturnFunc( int Button, int num, char **str, char linenum )
{
	char msg[256];
	char msg2[256];

	if ( Button==0 && num<0 ) serverRequestWinWindowType = -1;

	if ( Button != 0 ){
		sprintf( msg2, "%d", OrCfwin.faceNo );
		makeSendString( msg2, msg, sizeof( msg )-1 );
		nrproto_WN_send( sockfd, mapGx, mapGy, OrCfwin.SeqNo, OrCfwin.ObjIndex, Button, msg );

		serverRequestWinWindowType = -1;

		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	return TRUE;
}
ACTION *openOrCfWindow( int BottonType, int SeqNo, int ObjIndex, char *data )
{
	char str[1000];
	const int x = (DEF_APPSIZEX-448)>>1, y = (DEF_APPSIZEY-240-24)>>1;
	const int w = 448 , h = 240, Interval = 16;
	int i,j;

	int LineLen;
	int LineNum;
	int BtnAreaH;

	// アクションの初期化
	initGrl_Action();

	// サーバ情报保存
	OrCfwin.WinType = serverRequestWinWindowType;
	OrCfwin.SeqNo = SeqNo,	OrCfwin.ObjIndex = ObjIndex;

	j = 1;
	// 颜絵取得
	OrCfwin.faceNo = getIntegerToken( data, '|', j++ );
	// 信息取得
	getStringToken( data, '|', j++, sizeof(str), str );
	makeRecvString( str );

	// 信息生成
	BtnAreaH = 0;
	if( BottonType ) BtnAreaH = GENERAL_BUTTON_AREA_H;

	LineLen = GeneralWindowStrLen(w);
	LineNum = GeneralWindowStrLineNum( h, BottonType, Interval );
	OrCfwin.Msg[0][0] = '\0';
	OrCfwin.Msg[1][0] = '\0';
	OrCfwin.Msg[2][0] = '\0';
	for(i=3;i<LineNum;i++){
		memset( OrCfwin.Msg[i], ' ', 14 );
		getMemoLine( &OrCfwin.Msg[i][14], sizeof( OrCfwin.Msg[0] )-14, str, i-3, LineLen-14 );
	}
	addGrl_Action( 1, OrCfwin.faceNo, 0, 86, 98 );

	return openMenuSrGeneralWindow( x, y, w, h, GID_CommonWindow, BottonType,
		 OrCfwin.Msg, LineNum, Interval, 0,
		 0, NULL, LineLen, 2, GRL_OPT_NONE,
		 OrCf_ReturnFunc, NULL, OPENMENUWINDOW_HIT, 0 );
}
