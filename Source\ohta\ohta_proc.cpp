﻿/************************/
/*	ohta_proc.cpp		*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/map.h"
#include "../systeminc/menu.h"
#include "../systeminc/process.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/font.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/action.h"
#include "../systeminc/produce.h"
#include "../systeminc/field.h"
#include "ohta.h"
//#include "battle.h"

#if 0

// バトルプロセス サブプロセス番号
enum{
	BATTLE_PROC_INIT,				// ０：初始化
	BATTLE_PROC_IN_PRODUCE,			// １：进入战斗
	BATTLE_PROC_RECV_BC_DATA,		// ２：等到ＢＣ受信
	BATTLE_PROC_CHAR_APPEAR,		// ３：登场
	BATTLE_PROC_CMD_INPUT,			// ４：输入指令
	BATTLE_PROC_RECV_MOVIE_DATA,	// ５：等待动画受信
	BATTLE_PROC_MOVIE,				// ６：处理动画
	BATTLE_PROC_OUT_PRODUCE_INIT,	// ７：结束演出初始化
	BATTLE_PROC_OUT_PRODUCE,		// ８：演出结束
};

// 登场終了チェック *******************************************************************/
int BattleCheckAppear( void )
{
	int i;
	int appearFlag = FALSE;
	
	// 登场してないかチェック
	for( i = 0 ; i < BC_MAX ; i++ ){
		// ポインタ无い时
		if( pActBc[ i ] == NULL ) continue;
		//BC_YOBI *pYobi = ( BC_YOBI *)pActBc[ i ]->pYobi;
		// 登场フラグ立っていなかったら
		if( ( ( BC_YOBI * )pActBc[ i ]->pYobi )->appearFlag == FALSE ){
		//if( pYobi->appearFlag == FALSE ){
			// 登场へ
			pActBc[ i ]->actNo = BC_APPEAR;
			appearFlag = TRUE;
		}
	}
	// 登场終了したら
	if( appearFlag == FALSE ) return 1;
	
	return 0;
}

#endif

/* 太田プロセス处理 ********************************************************************/
void OhtaTestProc( void )
{

#if 0
	//int i;
	
	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
	
		case BATTLE_PROC_INIT: // 初期化 ***************************************/
		
			// バトル初期化
			InitBattle();
			// チャット初期化
			InitChat();
			// バトルマップ作成
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
			// バトルマップ読み込みとバトルサーフェスの画像作成 */
			ReadBattleMap( 10 );
			ChatProc();				// チャット处理
			//no++;
			//if( no >= BATTLE_MAP_FILES ) no = 0;
			//no = Rnd( 0, BATTLE_MAP_FILES - 1 );
			// バックサーフェスを黒でクリアー
			ClearBackSurface();	
			//SortDispBuffer(); 	// 表示バッファソート
			// ＢＭＰをバックサーフェスにセット
			PutBmp();
			// バトルサーフェスを初期化
			//ClearSurface( lpBattleSurface );
			// バックサーフェスからバトルサーフェスへコピー
			lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
			
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
			
			// 演出中
			DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE );
				
			// 点灭を无くす为
			MenuProc();				// メニュー处理
			ImeProc();				// ＩＭＥ关连处理
			// フィールドメニュー处理
			fieldProc2();
			
			// メニューフラグＯＮ 
			//battleMenuFlag2 = TRUE;
			
			// パラメータセーブ
			//saveUserSetting();
			
			
			// 次ぎのプロセスへ
			//SubProcNo = BATTLE_PROC_RECV_BC_DATA;
			SubProcNo++;
			
			break;
			
		case BATTLE_PROC_IN_PRODUCE: // バトルイン演出 *************************************/
			
			// 演出中
			if( DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE ) == TRUE ){
			//if( DrawProduce( PRODUCE_HAGARE_OCHI_IN ) == TRUE ){
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
				// バトルマップ読み込みとバトルサーフェスの画像作成 */
				ReadBattleMap( BattleMapNo );
				//no++;
				//if( no >= BATTLE_MAP_FILES ) no = 0;
				//no = Rnd( 0, BATTLE_MAP_FILES - 1 );
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
				//SortDispBuffer(); 	// 表示バッファソート
				// ＢＭＰをバックサーフェスにセット
				PutBmp();
				// バトルサーフェスを初期化
				//ClearSurface( lpBattleSurface );
				// バックサーフェスからバトルサーフェスへコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
				// 现在の时间を记忆
				NowTime = GetTickCount();
#ifdef PUK2_FPS
				NowDrawTime = NowTime;
#endif
				
				// バックバッファー描画方法变更
				BackBufferDrawType = DRAW_BACK_BATTLE; 
				
				// キーボード处理
				KeyBoardProc();			
				/* チャット处理 */
				ChatProc();
				// メニュー处理
				MenuProc();
				// ＩＭＥ关连处理
				ImeProc();
				// キーボードカーソル点灭处理
				FlashKeyboardCursor();
				// フィールドメニュー处理
				fieldProc2();
				// パレット处理
				//PaletteProc();
				
				SubProcNo++;
			}else{
				MenuProc();	// メニュー处理
				ImeProc();	// ＩＭＥ关连处理
				// フィールドメニュー处理
				fieldProc2();
			}
		
			break;
			
		case BATTLE_PROC_RECV_BC_DATA: // ＢＣデータ受信待ち ********************************************/
			
			// キーボード处理
			KeyBoardProc();		
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

			/* チャット处理 */
			ChatProc();
			// チャットバッファーをフォントバッファに溜める
			ChatBufferToFontBuffer(); 
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			MenuProc();
			// ＩＭＥ关连处理
			ImeProc();
			
			// バトルキャラクターデータの読み込み
			ReadBcData();
#if 0			
			// 登场してないかチェック
			for( i = 0 ; i < BC_MAX ; i++ ){
				// ポインタ无い时
				if( pActBc[ i ] == NULL ) continue;
				// 登场ブラグ立っていなかったら
				if( pAcctBc[ i ]->pYobi->appearFlag == FALSE ){
					// 登场へ
					pActBc[ i ]->actNo = BC_APPEAR;
				}
			}
			
#endif			
			// 次ぎのプロセスへ
			SubProcNo = BATTLE_PROC_CHAR_APPEAR;
			break;
		
		case BATTLE_PROC_CHAR_APPEAR: // 登场演出 ********************************************/
			
			// マウス等级がメニュー以下の时
			//if( mouse.level < DISP_PRIO_MENU && TaskBarFlag == FALSE ){
			//	drawGrid();		// グリッドカーソル表示
			//	moveProc();		// 移动处理
			//}
			
			// キーボード处理
			KeyBoardProc();		
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

			/* チャット处理 */
			ChatProc();
			// チャットバッファーをフォントバッファに溜める
			ChatBufferToFontBuffer(); 
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			MenuProc();
			// ＩＭＥ关连处理
			ImeProc();
#if 0
			// 登场終了チェック
			for( i = 0,  ; i < BC_MAX ; i++ ){
				// ポインタ无い时
				if( pActBc[ i ] == NULL ) continue;
				// 登场ブラグ立っていなかったら
				if( pAcctBc[ i ]->pYobi->appearFlag == FALSE ){
					// 登场へ
					pActBc[ i ]->actNo = BC_APPEAR;
				}
			}
#endif
			// 登场終了チェック
			if( BattleCheckAppear() == TRUE ) SubProcNo = BATTLE_PROC_MOVIE;
			
			break;
			
		case BATTLE_PROC_CMD_INPUT: // コマンド入力 ********************************************/
			break;
			
		case BATTLE_PROC_RECV_MOVIE_DATA: // ムービー受信待ち ********************************************/
			break;
			
		case BATTLE_PROC_MOVIE: // ムービー处理 ********************************************/
		
			// マウス等级がメニュー以下の时
			//if( mouse.level < DISP_PRIO_MENU && TaskBarFlag == FALSE ){
			//	drawGrid();		// グリッドカーソル表示
			//	moveProc();		// 移动处理
			//}
			
			// キーボード处理
			KeyBoardProc();		
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

			/* チャット处理 */
			ChatProc();
			// チャットバッファーをフォントバッファに溜める
			ChatBufferToFontBuffer(); 
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			MenuProc();
			// ＩＭＥ关连处理
			ImeProc();		
			// パレット处理
			//PaletteProc();
			// ＢＭＰをバックサーフェスにセット
			//PutBmp();	
			
			break;
			
		case BATTLE_PROC_OUT_PRODUCE_INIT: // 終了演出初期化 ********************************************/
			break;
			
		case BATTLE_PROC_OUT_PRODUCE: // 終了演出 ********************************************/
			
			break;
	}
#endif
}

