﻿#ifndef __HANDLETIME_H__
#define __HANDLETIME_H__


// ＮＲ时间构造体
typedef struct tagNRTIME
{
	int year;			// 年
	int month;			// 月
	int day;			// 日
	int hour;			// 时间
	int animeTime;		// 时间アニメ用
#ifdef PUK2
	int min;			// 分
#endif
} NRTIME;

typedef enum
{
	NR_NOON,		// 昼
	NR_EVENING,		// 夕
	NR_NIGHT,		// 夜
	NR_MORNING,		// 朝
	
} NRTIME_SECTION;


// 时间带设定
#define NIGHT_TO_MORNING	4	// 
#define MORNING_TO_NOON		7	// 
#define NOON_TO_EVENING		16	// 
#define EVENING_TO_NIGHT	19	// 

extern NRTIME nrTime;
extern long serverTime;
extern long clientTime;
extern NRTIME_SECTION nrTimeZoneNo;
extern BOOL TimeZonePalChangeFlag;
#ifdef PUK2
extern long adjustTime;
#endif


void realTimeToNRTime( NRTIME * );
NRTIME_SECTION getNRTime( NRTIME * );
void timeZoneProc( void );

#ifdef PUK2
void LocalBurstTimeProc(void);
#endif
#if defined(PUK2) && defined(_DEBUG)
	void addDebugNRTime( int year, int month, int day, int hour, int min, int sec );
	void clearDebugNRTime();
#endif

#endif
