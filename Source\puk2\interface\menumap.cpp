﻿//右コクピット＞マップ

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowMap( int mouse )
{
	if (mouse==WIN_INIT){
		( (TEXT_SWITCH *)wI->sw[EnumMapName].Switch )->text[0] = '\0';
		return TRUE;
	}
#ifdef PUK3_VEHICLE
	// 乘り物移动中なら
	if ( nowVehicleProc() ){
		// 何も表示しない
	}else{
		// マップ名と表示が违っていたら
		if ( ( (TEXT_SWITCH *)wI->sw[EnumMapName].Switch )->text[0] == '\0' ){
			int len;
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumMapName].Switch )->text, mapName );
			len = GetStrWidth( mapName, FONT_KIND_SIZE_12 );
			wI->sw[EnumMapName].ofx = ( (wI->sx-len)>>1 )+2;
		}
	}
#else
	// マップ名と表示が违っていたら
	if ( ( (TEXT_SWITCH *)wI->sw[EnumMapName].Switch )->text[0] == '\0' ){
		int len;
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumMapName].Switch )->text, mapName );
		len = GetStrWidth( mapName, FONT_KIND_SIZE_12 );
		wI->sw[EnumMapName].ofx = ( (wI->sx-len)>>1 )+2;
	}
#endif

	return TRUE;
}

BOOL MenuWindowMapDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

//--------------------------------------------------------
//ボタン处理
//--------------------------------------------------------
BOOL MenuSwitchMapDisp( int no, unsigned int flag )
{
#ifdef PUK3_VEHICLE
	// 乘り物移动中なら
	if ( nowVehicleProc() ){
		// 何も表示しない
	}else{
		StockAutoMapDispBuffer( wI->wx+wI->sw[no].ofx-14, wI->wy+wI->sw[no].ofy-64-53, DISP_PRIO_WIN2 );
	}
#else
	StockAutoMapDispBuffer( wI->wx+wI->sw[no].ofx-14, wI->wy+wI->sw[no].ofy-64-53, DISP_PRIO_WIN2 );
#endif
	return FALSE;
}

BOOL MenuSwitchEastText( int no, unsigned int flag )
{
	TEXT_SWITCH *Text=(TEXT_SWITCH *)wI->sw[no].Switch;

#ifdef PUK3_VEHICLE
	// 乘り物移动中なら
	if ( nowVehicleProc() ){
		Text->text[0] = '\0';
	}else{
		sprintf( Text->text, "%3d", mapGx );                                //MLHIDE
	}
#else
	sprintf( Text->text, "%3d", mapGx );                                 //MLHIDE
#endif

	return FALSE;
}

BOOL MenuSwitchSouthText( int no, unsigned int flag )
{
	TEXT_SWITCH *Text=(TEXT_SWITCH *)wI->sw[no].Switch;

#ifdef PUK3_VEHICLE
	// 乘り物移动中なら
	if ( nowVehicleProc() ){
		Text->text[0] = '\0';
	}else{
		sprintf( Text->text, "%3d", mapGy );                                //MLHIDE
	}
#else
	sprintf( Text->text, "%3d", mapGy );                                 //MLHIDE
#endif

	return FALSE;
}

//------------------------------------------------------------------------//
// ウィンドウオープンアニメーション											//
//------------------------------------------------------------------------//
ACTION *openMapMenuWindow( unsigned char flg, char opentype, ACTION **ret )
{
	int x, y, w, h;

	// ウィンドウの场所、サイズを取得
//	x = WindowData[MENU_WINDOW_MAP]->wx;
//	y = WindowData[MENU_WINDOW_MAP]->wy;
	w = 184;
	h = 159;

	// 以前开いたときの状态を、作成されたウィンドウに反映
	if ( WindowFlag[MENU_WINDOW_MAP].wininfo ){
		WindowFlag[MENU_WINDOW_MAP].wininfo->flag |= WIN_INFO_DEL;
		x = WindowFlag[MENU_WINDOW_MAP].wininfo->wx;
		y = WindowFlag[MENU_WINDOW_MAP].wininfo->wy;
//	}else if( WindowFlag[MENU_WINDOW_MAP].flag & 0x01 ){
	}else{	
		x = WindowFlag[MENU_WINDOW_MAP].wx;
		y = WindowFlag[MENU_WINDOW_MAP].wy;
	}

	return openMenuWindowPos( x, y, w, h, MENU_WINDOW_MAP, flg, opentype, ret );
}
