﻿//メニュー本体

#ifndef _MENUMENU_H_
#define _MENUMENU_H_

BOOL MenuWindowMenu( int mouse );
BOOL MenuWindowMenuDraw( int mouse );
BOOL MenuSwitchMenuSwitch( int no, unsigned int flag );

GRAPHIC_SWITCH MenuWindowMenuGraph[]={
	{GID_MenuStatusOff,0,0,0,0,0xFFFFFFFF},		//状态
	{GID_MenuSkillOff,0,0,0,0,0xFFFFFFFF},		//スキル
	{GID_MenuItemOff,0,0,0,0,0xFFFFFFFF},		//アイテム
	{GID_MenuMonsterOff,0,0,0,0,0xFFFFFFFF},	//モンスター
	{GID_MenuAddressOff,0,0,0,0,0xFFFFFFFF},	//アドレス
	{GID_MenuAlbumOff,0,0,0,0,0xFFFFFFFF},		//アルバム
	{GID_MenuSystemOff,0,0,0,0,0xFFFFFFFF},		//システム

	{GID_MenuSystemOff,0,0,0,0,0xFFFFFFFF},		//ＩＭＥ

	{GID_MenuBase00,0,0,0,0,0xFFFFFFFF},		//背景０１
	{GID_MenuBase01,0,0,0,0,0xFFFFFFFF},		//背景０２
};

BUTTON_SWITCH MenuWindowMenuButton[]={
	{0},									//状态
	{0},									//スキル
	{0},									//アイテム
	{0},									//モンスター
	{0},									//アドレス
	{0},									//アルバム
	{0},									//システム
};

TEXT_SWITCH MenuWindowMenuText[]={
	{FONT_PAL_WHITE,FONT_KIND_SMALL,ML_STRING(829, "信息")},				//信息
};

// メニューウィンドウ
static SWITCH_DATA MenuSwitch[] = {
{ SWITCH_NONE,   0,   0, 640, 30, TRUE, NULL, MenuSwitchMenuSwitch },		//マウスのチェック用

{ SWITCH_GRAPHIC,  89- 79,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 0], MenuSwitchMenuSwitch },//状态
{ SWITCH_GRAPHIC, 170- 84,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 1], MenuSwitchMenuSwitch },//スキル
{ SWITCH_GRAPHIC, 251- 88,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 2], MenuSwitchMenuSwitch },//アイテム
{ SWITCH_GRAPHIC, 333- 94,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 3], MenuSwitchMenuSwitch },//モンスター
{ SWITCH_GRAPHIC, 414- 98,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 4], MenuSwitchMenuSwitch },//アドレス
{ SWITCH_GRAPHIC, 496-104,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 5], MenuSwitchMenuSwitch },//アルバム
{ SWITCH_GRAPHIC, 575-111,   7,  60, 20, TRUE, &MenuWindowMenuGraph[ 6], MenuSwitchMenuSwitch },//システム

{ SWITCH_GRAPHIC, 558,  10,  60, 20, TRUE, &MenuWindowMenuGraph[ 7], MenuSwitchNone },	//ＩＭＥ　ＩＮＦＯ

{ SWITCH_GRAPHIC,   0,   0,   0,  0, TRUE, &MenuWindowMenuGraph[ 8], MenuSwitchNone },//背景１

{ SWITCH_TEXT	 , 110-89, 10,   0,  0, TRUE, &MenuWindowMenuText[ 0], MenuSwitchNone },	//一行ＩＮＦＯ
{ SWITCH_TEXT	 , 110-89+387, 10,   0,  0, TRUE, &MenuWindowMenuText[ 0], MenuSwitchNone },	//一行ＩＮＦＯSub

{ SWITCH_GRAPHIC,   0,   0,   0,  0, TRUE, &MenuWindowMenuGraph[ 9], MenuSwitchNone },//背景２

};

enum{
	EnumGraphMenuMouseOnCheck,			

	EnumGraphMenuStatus,		
	EnumGraphMenuSkill,			
	EnumGraphMenuItem,			
	EnumGraphMenuMonster,		
	EnumGraphMenuAddress,		
	EnumGraphMenuAlbum,			
	EnumGraphMenuSystem,		

	EnumGraphMenuIMEInfo,		

	EnumGraphMenuBase00,			

	EnumTextMenuOneLineInfo,			
	EnumTextMenuOneLineInfoSub,			

	EnumGraphMenuBase01,			

	EnumMenuEnd,
};


const WINDOW_DATA WindowDataMenuMenu = {
 MENU_ATTR_NOMOVE,															// メニューウィンドウ
     5,   0, 452, 640, 30, 0x80010101,   EnumMenuEnd,  MenuSwitch, MenuWindowMenu,MenuWindowMenuDraw,MenuWindowDel 
};


#endif
