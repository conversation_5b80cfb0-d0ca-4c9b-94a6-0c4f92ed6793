﻿/************************/
/*	font.h				*/
/************************/
#ifndef _FONT_H_
#define _FONT_H_

// フォントの论理高
//#define FONT_SIZE 16

// フォントパレットの种类
#ifdef PUK2
	#define FONT_PAL_MAX 12
#else
	#define FONT_PAL_MAX 10
#endif
// フォントパレット
#define FONT_PAL_WHITE 		0
#define FONT_PAL_AQUA 		1
#define FONT_PAL_PURPLE 	2
#define FONT_PAL_BLUE 		3
#define FONT_PAL_YELLOW 	4
#define FONT_PAL_GREEN 		5
#define FONT_PAL_RED 		6
#define FONT_PAL_GRAY 		7
#define FONT_PAL_BLUE2 		8
#define FONT_PAL_GREEN2 	9
#ifdef PUK2
	#define FONT_PAL_BLACK 		10
	#define FONT_PAL_GRAY2 		11
#endif
#ifdef PUK2
	#define FONT_PAL_SHADOW		0x7f

	// 影をなくすとき、フォントの色にこの值をＯＲ演算
	#define FONT_PAL_NOSHADOW	0x80

	// 色の本体部分のマスク
	#define FONT_PAL_PALLETE	0x7f
#endif

// フォント种类
#ifdef PUK2
	#define FONT_KIND_MAX 14
#else
	#define FONT_KIND_MAX 6
#endif

/* フォント表示优先顺位  fontPrio の值 ****************************************/
enum{
	FONT_PRIO_BACK, 		/* 后ろに表示 	*/
	FONT_PRIO_BACK2, 		/* 后ろに表示２ */
	FONT_PRIO_FRONT,		/* 前に表示 	*/
#ifdef PUK2
	FONT_PRIO_WIN,			/* ウィンドウ 	*/
	FONT_PRIO_FRONT2,		/* 前に表示２ 	*/
	FONT_PRIO_DRAG,			/* ドラッグ中 	*/
	FONT_PRIO_MAX			// 优先顺位の数
#else
	FONT_PRIO_FRONT2		/* 前に表示２ 	*/
#endif
};
/* フォント种类 ****************************************/
enum{
	// ゴシック体
	FONT_KIND_MIDDLE, 		/* 普通のフォント 	*/
	FONT_KIND_SMALL, 		/* 小さいフォント 	*/
	FONT_KIND_BIG, 			/* 大きいフォント 	*/
	// 明朝体
	FONT_KIND_MIDDLE2, 		/* 普通のフォント 	*/
	FONT_KIND_SMALL2, 		/* 小さいフォント 	*/
	FONT_KIND_BIG2, 		/* 大きいフォント 	*/
#ifdef PUK2
	FONT_KIND_BIG2__, 		/* 大きいフォント 	*/

	FONT_KIND_V_BIG, 		/* 特大フォント 	*/

	FONT_KIND_SIZE_11,		/* 11ポイントフォント		*/

	FONT_KIND_CHAT_S,		/* チャットスモール	*/
	FONT_KIND_CHAT_M,		/* チャットミドル	*/
	FONT_KIND_CHAT_L,		/* チャットラージ	*/

	FONT_KIND_SIZE_12,		/* 12ポイントフォント		*/
#endif
	FONT_KIND_BIG3,
};

// フォントの种类构造体
typedef struct{
	
	HFONT hFont;				// フォントハンドル
	int hankakuWidth;			// 半角文字の横幅（ドット数）
	int hankakuHeight;			// 半角文字の縦幅（ドット数）
	int zenkakuWidth;			// 全角文字の横幅（ドット数）
	int zenkakuHeight;			// 全角文字の縦幅（ドット数）
	
#ifdef PUK2
	char fontnum;				// 文字の种类の识别番号
	unsigned char siz;			// フォント作成时のサイズ
#endif

}FONT_KIND;

// フォントバッファー构造体
#ifdef PUK2
typedef struct ST_FONT_BUFFER
#else
typedef struct
#endif
{
	short x, y;					// 表示座标
	unsigned char color;		// 表示色
	unsigned char fontKind;		// 种类
	unsigned char fontPrio;		// 表示の优先顺位
	unsigned char hitFlag;		// 当たり判定するかフラグ
	unsigned char hitBoxColor;	// ボックスを表示するする时の色
	char  str[ 256 ]; 			// 文字列
#ifdef PUK2
	struct ST_FONT_BUFFER *next;
#endif
} FONT_BUFFER;

// フォントバッファー
extern FONT_BUFFER FontBuffer[];

// フォントの种类构造体
extern FONT_KIND FontKind[];

extern int chatFontSize;

// フォントカウンター
extern int FontCnt;

#ifdef PUK2
extern FONT_BUFFER *FntBufPointer[ FONT_PRIO_MAX ];
#endif

// フォント表示关数 ///////////////////////////////////////////////////////////
void FontPrint( char *moji, int x, int y, int dispPrio );

// 数字フォント表示关数 ///////////////////////////////////////////////////////
void FontPrintDec( char *moji, int x, int y, int dispPrio );

// フォントオブジェクトの作成 **************************************************/
void InitFont( int fontNo );

// フォントオブジェクトを开放 **************************************************/
void ReleaseFont( void );

// テキスト表示 ****************************************************************/
void PutFont( char fontPrio ); 
//新ウインドウ用
void NewWinPutFont(void);

/* フォント情报をバッファに溜める *********************************************/
int StockFontBuffer( int x, int y, char fontPrio, int color, char *str, BOOL hitFlag );

/* フォント情报をバッファに溜める（种类指定あり）********************************/
int StockFontBuffer( int x, int y, char fontPrio, int fontKind, int color, char *str, BOOL hitFlag );

int StockFontBuffer( short, short, unsigned char, unsigned char,
	unsigned char, char *, unsigned char, unsigned char );

/* フォント情报をバッファに溜める（构造体渡し）********************************/
void StockFontBuffer2( INPUT_STR *pInputStr );

#ifdef PUK2
	// フォントプライオリティ制御バッファの初期化
	void FontPrioInit();
	// フォント连続表示の中断命令を出す ++++
	void FontBufCut( char fontPrio );
	// フォントをバックサーフェスにセット ++++
	void PutFonts( void *FontBuffer );

#endif
#ifdef PUK2
	// フォント名
	extern char *exfontName[];
	// フォントパレットデータ
	extern unsigned char FontPal[ FONT_PAL_MAX ][3];
	// チャット履历(？)用フォントサイズ
	extern int htmlFontSizeTbl[ FONT_KIND_MAX ];
#endif

#endif
