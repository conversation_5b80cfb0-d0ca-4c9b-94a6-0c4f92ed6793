/***************************************
		dx9_detection.cpp
		DirectX 9 兼容性检测
		Created: DirectX 7→9 Upgrade Project
***************************************/

#include "../systeminc/system.h"

#ifdef DIRECTX9_UPGRADE

#include "../systeminc/dx9_compat.h"

//=============================================================================
// DirectX 9兼容性检测函数
//=============================================================================

/**
 * 检测系统是否支持DirectX 9
 * @return TRUE if DirectX 9 is available, FALSE otherwise
 */
BOOL IsDirectX9Available(void)
{
    LPDIRECT3D9 pD3D = NULL;
    
    // 尝试创建DirectX 9对象
    pD3D = Direct3DCreate9(D3D_SDK_VERSION);
    if (pD3D == NULL) {
        return FALSE;
    }
    
    // 检测适配器数量
    UINT adapterCount = pD3D->GetAdapterCount();
    if (adapterCount == 0) {
        pD3D->Release();
        return FALSE;
    }
    
    // 检测默认适配器的基本能力
    D3DCAPS9 caps;
    HRESULT hr = pD3D->GetDeviceCaps(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, &caps);
    if (FAILED(hr)) {
        // 尝试参考设备
        hr = pD3D->GetDeviceCaps(D3DADAPTER_DEFAULT, D3DDEVTYPE_REF, &caps);
        if (FAILED(hr)) {
            pD3D->Release();
            return FALSE;
        }
    }
    
    pD3D->Release();
    return TRUE;
}

/**
 * 检测设备能力是否满足要求
 * @return TRUE if device capabilities are sufficient, FALSE otherwise
 */
BOOL CheckDeviceCapabilities(void)
{
    LPDIRECT3D9 pD3D = NULL;
    D3DCAPS9 caps;
    BOOL result = FALSE;
    
    pD3D = Direct3DCreate9(D3D_SDK_VERSION);
    if (pD3D == NULL) {
        return FALSE;
    }
    
    // 获取设备能力
    HRESULT hr = pD3D->GetDeviceCaps(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, &caps);
    if (FAILED(hr)) {
        hr = pD3D->GetDeviceCaps(D3DADAPTER_DEFAULT, D3DDEVTYPE_REF, &caps);
        if (FAILED(hr)) {
            goto cleanup;
        }
    }
    
    // 检查必需的能力
    // 1. 检查纹理能力
    if (caps.MaxTextureWidth < 256 || caps.MaxTextureHeight < 256) {
        goto cleanup;
    }
    
    // 2. 检查混合能力
    if (!(caps.SrcBlendCaps & D3DPBLENDCAPS_SRCALPHA) ||
        !(caps.DestBlendCaps & D3DPBLENDCAPS_INVSRCALPHA)) {
        goto cleanup;
    }
    
    // 3. 检查图元能力
    if (!(caps.PrimitiveMiscCaps & D3DPMISCCAPS_CULLNONE)) {
        goto cleanup;
    }
    
    // 4. 检查纹理阶段能力
    if (caps.MaxTextureBlendStages < 1) {
        goto cleanup;
    }
    
    result = TRUE;
    
cleanup:
    if (pD3D) {
        pD3D->Release();
    }
    
    return result;
}

/**
 * 获取DirectX错误信息字符串
 * @param hr HRESULT错误码
 * @return 错误信息字符串
 */
const char* GetDirectXErrorString(HRESULT hr)
{
    switch (hr) {
        case D3D_OK:
            return "Success";
        case D3DERR_DEVICELOST:
            return "Device lost";
        case D3DERR_DEVICENOTRESET:
            return "Device not reset";
        case D3DERR_DRIVERINTERNALERROR:
            return "Driver internal error";
        case D3DERR_INVALIDCALL:
            return "Invalid call";
        case D3DERR_INVALIDDEVICE:
            return "Invalid device";
        case D3DERR_NOTAVAILABLE:
            return "Not available";
        case D3DERR_OUTOFVIDEOMEMORY:
            return "Out of video memory";
        case E_OUTOFMEMORY:
            return "Out of memory";
        default:
            return "Unknown DirectX error";
    }
}

/**
 * 检测最佳设备创建参数
 * @param pD3D DirectX 9对象指针
 * @param pPresentParams 输出的呈现参数
 * @param pBehaviorFlags 输出的行为标志
 * @return TRUE if successful, FALSE otherwise
 */
BOOL DetectBestDeviceSettings(LPDIRECT3D9 pD3D, D3DPRESENT_PARAMETERS* pPresentParams, DWORD* pBehaviorFlags)
{
    if (!pD3D || !pPresentParams || !pBehaviorFlags) {
        return FALSE;
    }
    
    D3DCAPS9 caps;
    HRESULT hr = pD3D->GetDeviceCaps(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, &caps);
    if (FAILED(hr)) {
        return FALSE;
    }
    
    // 设置默认的呈现参数
    ZeroMemory(pPresentParams, sizeof(D3DPRESENT_PARAMETERS));
    pPresentParams->BackBufferFormat = D3DFMT_UNKNOWN;
    pPresentParams->BackBufferCount = 1;
    pPresentParams->SwapEffect = D3DSWAPEFFECT_DISCARD;
    pPresentParams->EnableAutoDepthStencil = FALSE;
    pPresentParams->PresentationInterval = D3DPRESENT_INTERVAL_DEFAULT;
    
    // 检测硬件顶点处理能力
    if (caps.DevCaps & D3DDEVCAPS_HWTRANSFORMANDLIGHT) {
        *pBehaviorFlags = D3DCREATE_HARDWARE_VERTEXPROCESSING;
    } else {
        *pBehaviorFlags = D3DCREATE_SOFTWARE_VERTEXPROCESSING;
    }
    
    return TRUE;
}

#endif // DIRECTX9_UPGRADE
