﻿//メニュー＞モンスター＞状态、ディティール、スキル

#ifndef _MENUMONSTER_STATUS_H_
#define _MENUMONSTER_STATUS_H_

extern int MonsStatusNo;
extern int MonsStatusAng;

BOOL MenuWindowMonsterStatus( int mouse );
BOOL MenuWindowMonsterStatusDraw( int mouse );
BOOL MenuWindowMonsterDetail( int mouse );
BOOL MenuWindowMonsterDetailDraw( int mouse );
BOOL MenuWindowMonsterSkill( int mouse );
BOOL MenuWindowMonsterSkillDraw( int mouse );
BOOL MenuSwitchMonsterStatus( int no, unsigned int flag );
BOOL MenuSwitchMonsterDetail( int no, unsigned int flag );
BOOL MenuSwitchMonsterSkill( int no, unsigned int flag );
BOOL MenuSwitchMonsterChangeWindow( int no, unsigned int flag );
BOOL MenuSwitchMonsterSkillSwitch( int no, unsigned int flag );
BOOL MenuMonsterStatusNameDialog( int no, unsigned int flag );
#ifdef PUK3_WINDOW_OPEN_POINT
	BOOL MenuWindowMonsterStatusClose();
	BOOL MenuWindowMonsterDetailClose();
	BOOL MenuWindowMonsterSkillClose();
#endif

GRAPHIC_SWITCH MenuWindowMonsterStatusGraph[]={
	{GID_MonsterStatusWindow,0,0,0,0,0xFFFFFFFF},	//ウインドウ
	{GID_MonsterStatusBack,0,0,0,0,0x80FFFFFF},		//バック
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},		//クローズ
	{GID_MonsterNameSetOn,0,0,0,0,0xFFFFFFFF},		//セット
	{GID_MonsterStatusBase,0,0,0,0,0xFFFFFFFF},		//状态ベース
	{GID_MonsterDetailBase,0,0,0,0,0xFFFFFFFF},		//ディティールベース
	{GID_MonsterSkillBase,0,0,0,0,0xFFFFFFFF},		//スキルベース

	{GID_MonsterStatusOff,0,0,0,0,0xFFFFFFFF},		//状态ボタン
	{GID_MonsterDetailOff,0,0,0,0,0xFFFFFFFF},		//ディティールボタン
	{GID_MonsterSkillOff,0,0,0,0,0xFFFFFFFF},		//スキルボタン

	{GID_MonsterStatusEarthLeft,0,0,0,0,0xFFFFFFFF},		//土属性左
	{GID_MonsterStatusEarthCenter,0,0,0,0,0xFFFFFFFF},		//土属性中
	{GID_MonsterStatusEarthRight,0,0,0,0,0xFFFFFFFF},		//土属性右
	{GID_MonsterStatusEarthOnly,0,0,0,0,0xFFFFFFFF},		//土属性１

	{GID_MonsterStatusWaterLeft,0,0,0,0,0xFFFFFFFF},		//水属性左
	{GID_MonsterStatusWaterCenter,0,0,0,0,0xFFFFFFFF},		//水属性中
	{GID_MonsterStatusWaterRight,0,0,0,0,0xFFFFFFFF},		//水属性右
	{GID_MonsterStatusWaterOnly,0,0,0,0,0xFFFFFFFF},		//水属性１

	{GID_MonsterStatusFireLeft,0,0,0,0,0xFFFFFFFF},			//火属性左
	{GID_MonsterStatusFireCenter,0,0,0,0,0xFFFFFFFF},		//火属性中
	{GID_MonsterStatusFireRight,0,0,0,0,0xFFFFFFFF},		//火属性右
	{GID_MonsterStatusFireOnly,0,0,0,0,0xFFFFFFFF},			//火属性１

	{GID_MonsterStatusWindLeft,0,0,0,0,0xFFFFFFFF},			//风属性左
	{GID_MonsterStatusWindCenter,0,0,0,0,0xFFFFFFFF},		//风属性中
	{GID_MonsterStatusWindRight,0,0,0,0,0xFFFFFFFF},		//风属性右
	{GID_MonsterStatusWindOnly,0,0,0,0,0xFFFFFFFF},			//风属性１

	{GID_MonsterStatusNoDownOn,0,0,0,0,0xFFFFFFFF},			//モンスターナンバー←
	{GID_MonsterStatusNoUpOn,0,0,0,0,0xFFFFFFFF},			//モンスターナンバー→

	{GID_MonsterSkillPanelROn,0,0,0,0,0xFFFFFFFF},			//スキルパネル赤

	{GID_MonsterStatusReleaseOn,0,0,0,0,0xFFFFFFFF},		//リリース

	{GID_DetailStatusUpOn,0,0,0,0,0xFFFFFFFF},				//状态アップボタン

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},						//スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(下ボタン)

	{GID_StatusHealth0,0,0,0,0,0xFFFFFFFF},					//ヘルス画像

};

BUTTON_SWITCH MenuWindowMonsterStatusButton[]={
	{0},									//
};

TEXT_SWITCH MenuWindowMonsterStatusText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"名称"},				//名称
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"10000000/10000000"},	//ＥＸＰ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"100000/100000"},		//体力
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"100000/100000"},		//魔力
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"TYPE"},				//ＴＹＰＥ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"999"},					//ＨＭＣ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"99"},					//ＳＬＯＴ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"1/5"},					//ページ数
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"攻击"},				//スキル
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"100"},				//スキル使用魔力
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"こうげきする"},	//スキル说明

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"99"},				//等级
};

ACTION_SWITCH_INIT MenuWindowMonsterStatusAction[]={
	{100000},
};

//モンスター状态
// スイッチ
static SWITCH_DATA MonsterStatusSwitch[] = {

{ SWITCH_DIALOG,  66, 31, 12*8, 12, TRUE, NULL,	 MenuMonsterStatusNameDialog },				//ダイアログ表示

{ SWITCH_GRAPHIC,225,  9,  12, 12, TRUE, &MenuWindowMonsterStatusGraph[2], MenuSwitchCloseButton },		//クローズ

{ SWITCH_GRAPHIC, 80,275,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[7], MenuSwitchNone },				//状态
{ SWITCH_GRAPHIC,133,276,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[8], MenuSwitchMonsterChangeWindow },	//ディティール
{ SWITCH_GRAPHIC,186,275,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[9], MenuSwitchMonsterChangeWindow },	//スキル

{ SWITCH_GRAPHIC,187, 28,  32, 16, TRUE, &MenuWindowMonsterStatusGraph[3], MenuSwitchMonsterStatus },		//セット

{ SWITCH_GRAPHIC,175,253,  49, 15, TRUE, &MenuWindowMonsterStatusGraph[29], MenuSwitchMonsterStatus },			//リリース

{ SWITCH_GRAPHIC, 21,253,  13, 15, TRUE, &MenuWindowMonsterStatusGraph[26], MenuSwitchMonsterStatus },			//←
{ SWITCH_GRAPHIC, 65,253,  13, 15, TRUE, &MenuWindowMonsterStatusGraph[27], MenuSwitchMonsterStatus },			//→

{ SWITCH_TEXT,	  66, 31,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//名称
{ SWITCH_GRAPHIC,200,139,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[34], MenuSwitchNone },				//ヘルス
{ SWITCH_TEXT,	  63, 65,   0,  0, TRUE, &MenuWindowMonsterStatusText[1], MenuSwitchNone },				//ＥＸＰ
{ SWITCH_TEXT,	  48, 82,   0,  0, TRUE, &MenuWindowMonsterStatusText[2], MenuSwitchNone },				//体力
{ SWITCH_TEXT,	 142, 82,   0,  0, TRUE, &MenuWindowMonsterStatusText[3], MenuSwitchNone },				//魔力
{ SWITCH_TEXT,	  62, 99,   0,  0, TRUE, &MenuWindowMonsterStatusText[4], MenuSwitchNone },				//ＴＹＰＥ
{ SWITCH_TEXT,	 150, 99,   0,  0, TRUE, &MenuWindowMonsterStatusText[5], MenuSwitchNone },				//ＨＭＣ
{ SWITCH_TEXT,	 206, 99,   0,  0, TRUE, &MenuWindowMonsterStatusText[6], MenuSwitchNone },				//ＳＬＯＴ
{ SWITCH_TEXT,	  44,256,   0,  0, TRUE, &MenuWindowMonsterStatusText[7], MenuSwitchNone },				//ページ数

{ SWITCH_TEXT,	  63, 48,   0,  0, TRUE, &MenuWindowMonsterStatusText[11], MenuSwitchNone },				//等级

{ SWITCH_ACTION,  80,220, 0, 0,TRUE,&MenuWindowMonsterStatusAction[0], MenuSwitchNone},		//キャラクター画像（アクション）

{ SWITCH_GRAPHIC, 157+6*0, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//土04
{ SWITCH_GRAPHIC, 157+6*1, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*2, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*3, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*4, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*5, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*6, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*7, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*8, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*9, 166,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 157+6*0, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//水
{ SWITCH_GRAPHIC, 157+6*1, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*2, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*3, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*4, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*5, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*6, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*7, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*8, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*9, 182,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 157+6*0, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//火
{ SWITCH_GRAPHIC, 157+6*1, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*2, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*3, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*4, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*5, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*6, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*7, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*8, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*9, 198,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 157+6*0, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//风
{ SWITCH_GRAPHIC, 157+6*1, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*2, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*3, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*4, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*5, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*6, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*7, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*8, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 157+6*9, 214,   0,  0, TRUE, &MenuWindowMonsterGraph[10], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 23, 32,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[4], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[0], MenuSwitchNone },			//ウインドウ
{ SWITCH_GRAPHIC, 14, 25,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[1], MenuSwitchNone },			//バック

{ SWITCH_BUTTON ,  39-20,143-11,114,110, TRUE, &MenuWindowMonsterStatusButton[0], MenuSwitchMonsterStatus },	//キャラクタ回転ボタン

{ SWITCH_NONE  , 250, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ
};

enum{
	EnumDialogMonsterStatusName,	

	EnumGraphMonsterStatusClose,	

	EnumGraphMonsterStatusStatusBt,	
	EnumGraphMonsterStatusDetailBt,	
	EnumGraphMonsterStatusSkillBt,	
	
	EnumGraphMonsterStatusNameSet,	

	EnumGraphMonsterStatusRelease,	

	EnumGraphMonsterStatusNoDown,	
	EnumGraphMonsterStatusNoUp,	

	EnumTextMonsterStatusName,	
	EnumGraphMonsterStatusHealth,	
	EnumTextMonsterStatusExp,	
	EnumTextMonsterStatusLp,	
	EnumTextMonsterStatusFp,	
	EnumTextMonsterStatusType,	
	EnumTextMonsterStatusHMC,	
	EnumTextMonsterStatusSlot,	
	EnumTextMonsterStatusPage,	

	EnumTextMonsterStatusLevel,	

	EnumActionMonsterStatusChar,	

	EnumGraphMonsterStatusEarth00,		
	EnumGraphMonsterStatusEarth01,		
	EnumGraphMonsterStatusEarth02,		
	EnumGraphMonsterStatusEarth03,		
	EnumGraphMonsterStatusEarth04,		
	EnumGraphMonsterStatusEarth05,		
	EnumGraphMonsterStatusEarth06,		
	EnumGraphMonsterStatusEarth07,		
	EnumGraphMonsterStatusEarth08,		
	EnumGraphMonsterStatusEarth09,		
	
	EnumGraphMonsterStatusWater00,		
	EnumGraphMonsterStatusWater01,		
	EnumGraphMonsterStatusWater02,		
	EnumGraphMonsterStatusWater03,		
	EnumGraphMonsterStatusWater04,		
	EnumGraphMonsterStatusWater05,		
	EnumGraphMonsterStatusWater06,		
	EnumGraphMonsterStatusWater07,		
	EnumGraphMonsterStatusWater08,		
	EnumGraphMonsterStatusWater09,		

	EnumGraphMonsterStatusFire00,		
	EnumGraphMonsterStatusFire01,		
	EnumGraphMonsterStatusFire02,		
	EnumGraphMonsterStatusFire03,		
	EnumGraphMonsterStatusFire04,		
	EnumGraphMonsterStatusFire05,		
	EnumGraphMonsterStatusFire06,		
	EnumGraphMonsterStatusFire07,		
	EnumGraphMonsterStatusFire08,		
	EnumGraphMonsterStatusFire09,		

	EnumGraphMonsterStatusWind00,		
	EnumGraphMonsterStatusWind01,		
	EnumGraphMonsterStatusWind02,		
	EnumGraphMonsterStatusWind03,		
	EnumGraphMonsterStatusWind04,		
	EnumGraphMonsterStatusWind05,		
	EnumGraphMonsterStatusWind06,		
	EnumGraphMonsterStatusWind07,		
	EnumGraphMonsterStatusWind08,		
	EnumGraphMonsterStatusWind09,		

	EnumGraphMonsterStatusBase,	
	EnumGraphMonsterStatusWindow,	
	EnumGraphMonsterStatusBack,	

	EnumBtMenuStatusSwitchActionAng,

	EnumHitMonsterStatus1,
	
	EnumMonsterStatusEnd,
};

//モンスターディティール
// スイッチ
static SWITCH_DATA MonsterDetailSwitch[] = {

{ SWITCH_DIALOG,  66, 31, 12*8, 12, TRUE, NULL,	 MenuMonsterStatusNameDialog },				//ダイアログ表示

{ SWITCH_GRAPHIC,225,  9,  12, 12, TRUE, &MenuWindowMonsterStatusGraph[ 2], MenuSwitchCloseButton },	//クローズ

{ SWITCH_GRAPHIC, 80,275,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[7], MenuSwitchMonsterChangeWindow },	//状态
{ SWITCH_GRAPHIC,133,276,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[8], MenuSwitchNone },				//ディティール
{ SWITCH_GRAPHIC,186,275,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[9], MenuSwitchMonsterChangeWindow },	//スキル

{ SWITCH_GRAPHIC,187, 28,  32, 16, TRUE, &MenuWindowMonsterStatusGraph[3], MenuSwitchMonsterDetail },			//セット

{ SWITCH_GRAPHIC,175,253,  49, 15, TRUE, &MenuWindowMonsterStatusGraph[29], MenuSwitchMonsterDetail },	//リリース

{ SWITCH_GRAPHIC, 21,253,  13, 15, TRUE, &MenuWindowMonsterStatusGraph[26], MenuSwitchMonsterDetail },			//←
{ SWITCH_GRAPHIC, 65,253,  13, 15, TRUE, &MenuWindowMonsterStatusGraph[27], MenuSwitchMonsterDetail },			//→

{ SWITCH_GRAPHIC, 79-18,115-2,  10, 10, TRUE, &MenuWindowMonsterStatusGraph[30], MenuSwitchMonsterDetail },			//Vit状态アップ
{ SWITCH_GRAPHIC, 79-18,130-2,  10, 10, TRUE, &MenuWindowMonsterStatusGraph[30], MenuSwitchMonsterDetail },			//Str状态アップ
{ SWITCH_GRAPHIC, 79-18,145-2,  10, 10, TRUE, &MenuWindowMonsterStatusGraph[30], MenuSwitchMonsterDetail },			//Tgh状态アップ
{ SWITCH_GRAPHIC, 79-18,160-2,  10, 10, TRUE, &MenuWindowMonsterStatusGraph[30], MenuSwitchMonsterDetail },			//Qui状态アップ
{ SWITCH_GRAPHIC, 79-18,175-2,  10, 10, TRUE, &MenuWindowMonsterStatusGraph[30], MenuSwitchMonsterDetail },			//Mgc状态アップ

{ SWITCH_TEXT,	  66, 31,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//名称
{ SWITCH_TEXT,	  63, 48,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Level
#ifdef VERSION_TW
//台服客户端宠物属性显示位置
{ SWITCH_TEXT,	  50, 65,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//LP
{ SWITCH_TEXT,	 141, 65,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//FP

{ SWITCH_TEXT,	  44,256,   0,  0, TRUE, &MenuWindowMonsterStatusText[7], MenuSwitchNone },				//ページ数

{ SWITCH_TEXT,	  93, 98,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Bonus

{ SWITCH_TEXT,	  93,114,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Vit
{ SWITCH_TEXT,	  93,129,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Str
{ SWITCH_TEXT,	  93,144,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Tgh
{ SWITCH_TEXT,	  93,159,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Qui
{ SWITCH_TEXT,	  93,174,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Mgc

{ SWITCH_TEXT,	  93,197,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Hmc
{ SWITCH_TEXT,	  93,212,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Slot

{ SWITCH_TEXT,	 150, 97,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Cri
{ SWITCH_TEXT,	 205, 98,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Hit
{ SWITCH_TEXT,	 150,114,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Ctr
{ SWITCH_TEXT,	 205,113,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Avd

{ SWITCH_TEXT,	 150,144,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Poi
{ SWITCH_TEXT,	 205,144,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Itx
{ SWITCH_TEXT,	 150,159,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Slp
{ SWITCH_TEXT,	 205,159,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Cnf
{ SWITCH_TEXT,	 150,174,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Stn
{ SWITCH_TEXT,	 205,174,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Amn

{ SWITCH_TEXT,	 150,197,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Atk
{ SWITCH_TEXT,	 205,197,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Mnd
{ SWITCH_TEXT,	 150,212,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Def
{ SWITCH_TEXT,	 205,212,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Rcv
{ SWITCH_TEXT,	 150,227,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Agl
#else
{ SWITCH_TEXT,	  45, 65,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//LP
{ SWITCH_TEXT,	 140, 65,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//FP

{ SWITCH_TEXT,	  44,256,   0,  0, TRUE, &MenuWindowMonsterStatusText[7], MenuSwitchNone },				//ページ数

{ SWITCH_TEXT,	  93, 98,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Bonus

{ SWITCH_TEXT,	  93,114,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Vit
{ SWITCH_TEXT,	  93,129,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Str
{ SWITCH_TEXT,	  93,144,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Tgh
{ SWITCH_TEXT,	  93,159,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Qui
{ SWITCH_TEXT,	  93,174,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Mgc

{ SWITCH_TEXT,	  93,197,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Hmc
{ SWITCH_TEXT,	  93,212,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Slot

{ SWITCH_TEXT,	 157, 97,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Cri
{ SWITCH_TEXT,	 205, 98,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Hit
{ SWITCH_TEXT,	 157,114,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Ctr
{ SWITCH_TEXT,	 205,113,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Avd

{ SWITCH_TEXT,	 157,144,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Poi
{ SWITCH_TEXT,	 205,144,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Itx
{ SWITCH_TEXT,	 157,159,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Slp
{ SWITCH_TEXT,	 205,159,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Cnf
{ SWITCH_TEXT,	 157,174,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Stn
{ SWITCH_TEXT,	 205,174,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Amn

{ SWITCH_TEXT,	 157,197,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Atk
{ SWITCH_TEXT,	 205,197,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Mnd
{ SWITCH_TEXT,	 157,212,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Def
{ SWITCH_TEXT,	 205,212,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Rcv
{ SWITCH_TEXT,	 157,227,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//Agl
#endif

{ SWITCH_GRAPHIC, 18, 32,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[5], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[0], MenuSwitchNone },			//ウインドウ
{ SWITCH_GRAPHIC, 14, 25,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[1], MenuSwitchNone },			//バック

{ SWITCH_NONE  , 250, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ
};

enum{
	EnumDialogMonsterDetailName,	

	EnumGraphMonsterDetailClose,	

	EnumGraphMonsterDetailStatusBt,	
	EnumGraphMonsterDetailDetailBt,	
	EnumGraphMonsterDetailSkillBt,	

	EnumGraphMonsterDetailNameSet,	

	EnumGraphMonsterDetailRelease,	

	EnumGraphMonsterDetailNoDown,	
	EnumGraphMonsterDetailNoUp,	

	EnumGraphMonsterDetailVitUp,	
	EnumGraphMonsterDetailStrUp,	
	EnumGraphMonsterDetailTghUp,	
	EnumGraphMonsterDetailQuiUp,	
	EnumGraphMonsterDetailMgcUp,	

	EnumTextMonsterDetailName,	
	EnumTextMonsterDetailLevel,	
	EnumTextMonsterDetailLp,	
	EnumTextMonsterDetailFp,	

	EnumTextMonsterDetailPage,	

	EnumTextMonsterDetailBonus,	

	EnumTextMonsterDetailVit,	
	EnumTextMonsterDetailStr,	
	EnumTextMonsterDetailTgh,	
	EnumTextMonsterDetailQui,	
	EnumTextMonsterDetailMgc,	

	EnumTextMonsterDetailHmc,	
	EnumTextMonsterDetailSlot,	

	EnumTextMonsterDetailCri,	
	EnumTextMonsterDetailHit,	
	EnumTextMonsterDetailCtr,	
	EnumTextMonsterDetailAvd,	

	EnumTextMonsterDetailPoi,	
	EnumTextMonsterDetailItx,	
	EnumTextMonsterDetailSlp,	
	EnumTextMonsterDetailCnf,	
	EnumTextMonsterDetailStn,	
	EnumTextMonsterDetailAmn,	

	EnumTextMonsterDetailAtk,	
	EnumTextMonsterDetailMnd,	
	EnumTextMonsterDetailDef,	
	EnumTextMonsterDetailRcv,	
	EnumTextMonsterDetailAgl,	

	EnumGraphMonsterDetailBase,	
	EnumGraphMonsterDetailWindow,	
	EnumGraphMonsterDetailBack,	

	EnumHitMonsterDetail1,	

	
	EnumMonsterDetailEnd,
};


char MonsterSkillNum[10][11];

NUMBER_SWITCH MenuWindowMonsterSkillNum[] = {
	{ MonsterSkillNum[0], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[1], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[2], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[3], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[4], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[5], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[6], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[7], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[8], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ MonsterSkillNum[9], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
};


//モンスターススキル
// スイッチ
static SWITCH_DATA MonsterSkillSwitch[] = {

{ SWITCH_DIALOG,  66, 31, 12*8, 12, TRUE, NULL,	 MenuMonsterStatusNameDialog },				//ダイアログ表示

{ SWITCH_GRAPHIC,225,  9,  12, 12, TRUE, &MenuWindowMonsterStatusGraph[ 2], MenuSwitchCloseButton },	//クローズ

{ SWITCH_GRAPHIC, 80,275,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[7], MenuSwitchMonsterChangeWindow },	//状态
{ SWITCH_GRAPHIC,133,276,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[8], MenuSwitchMonsterChangeWindow },	//ディティール
{ SWITCH_GRAPHIC,186,275,  35, 25, TRUE, &MenuWindowMonsterStatusGraph[9], MenuSwitchNone },				//スキル

{ SWITCH_GRAPHIC,187, 28,  32, 16, TRUE, &MenuWindowMonsterStatusGraph[3], MenuSwitchMonsterSkill },			//セット

{ SWITCH_GRAPHIC,175,253,  49, 15, TRUE, &MenuWindowMonsterStatusGraph[29], MenuSwitchMonsterSkill },			//リリース

{ SWITCH_GRAPHIC, 21,253,  13, 15, TRUE, &MenuWindowMonsterStatusGraph[26], MenuSwitchMonsterSkill },			//←
{ SWITCH_GRAPHIC, 65,253,  13, 15, TRUE, &MenuWindowMonsterStatusGraph[27], MenuSwitchMonsterSkill },			//→
/*
{ SWITCH_GRAPHIC,240-18, 70-14,   0, 14, TRUE, &MenuWindowMonsterStatusGraph[31], MenuSwitchNone },				//スクロールバー(つまみ)
{ SWITCH_BUTTON, 240-18, 69-14,  11,141, TRUE, &MenuWindowMonsterStatusButton[0], MenuSwitchScrollBarV },			//スクロールバー(ドラッグ部分)
*/
{ SWITCH_GRAPHIC,240-15, 60-10,  11, 11, TRUE, &MenuWindowMonsterStatusGraph[32], MenuSwitchMonsterSkill },		//スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,240-15,209-14,  11, 11, TRUE, &MenuWindowMonsterStatusGraph[33], MenuSwitchMonsterSkill },		//スクロールバー(下ボタン)

{ SWITCH_TEXT,	  66, 31,   0,  0, TRUE, &MenuWindowMonsterStatusText[0], MenuSwitchNone },				//名称

{ SWITCH_TEXT,	  44,256,   0,  0, TRUE, &MenuWindowMonsterStatusText[7], MenuSwitchNone },				//ページ数


{ SWITCH_TEXT,	  20, 50+16*0,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称00
{ SWITCH_TEXT,	  20, 50+16*1,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称01
{ SWITCH_TEXT,	  20, 50+16*2,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称02
{ SWITCH_TEXT,	  20, 50+16*3,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称03
{ SWITCH_TEXT,	  20, 50+16*4,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称04
{ SWITCH_TEXT,	  20, 50+16*5,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称05
{ SWITCH_TEXT,	  20, 50+16*6,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称06
{ SWITCH_TEXT,	  20, 50+16*7,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称07
{ SWITCH_TEXT,	  20, 50+16*8,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称08
{ SWITCH_TEXT,	  20, 50+16*9,   0,  0, TRUE, &MenuWindowMonsterStatusText[8], MenuSwitchNone },		//スキル名称09

{ SWITCH_NUMBER, 201, 51+16*0,   0,  0, TRUE, &MenuWindowMonsterSkillNum[0], MenuSwitchNone },			//スキル使用魔力00
{ SWITCH_NUMBER, 201, 51+16*1,   0,  0, TRUE, &MenuWindowMonsterSkillNum[1], MenuSwitchNone },			//スキル使用魔力01
{ SWITCH_NUMBER, 201, 51+16*2,   0,  0, TRUE, &MenuWindowMonsterSkillNum[2], MenuSwitchNone },			//スキル使用魔力02
{ SWITCH_NUMBER, 201, 51+16*3,   0,  0, TRUE, &MenuWindowMonsterSkillNum[3], MenuSwitchNone },			//スキル使用魔力03
{ SWITCH_NUMBER, 201, 51+16*4,   0,  0, TRUE, &MenuWindowMonsterSkillNum[4], MenuSwitchNone },			//スキル使用魔力04
{ SWITCH_NUMBER, 201, 51+16*5,   0,  0, TRUE, &MenuWindowMonsterSkillNum[5], MenuSwitchNone },			//スキル使用魔力05
{ SWITCH_NUMBER, 201, 51+16*6,   0,  0, TRUE, &MenuWindowMonsterSkillNum[6], MenuSwitchNone },			//スキル使用魔力06
{ SWITCH_NUMBER, 201, 51+16*7,   0,  0, TRUE, &MenuWindowMonsterSkillNum[7], MenuSwitchNone },			//スキル使用魔力07
{ SWITCH_NUMBER, 201, 51+16*8,   0,  0, TRUE, &MenuWindowMonsterSkillNum[8], MenuSwitchNone },			//スキル使用魔力08
{ SWITCH_NUMBER, 201, 51+16*9,   0,  0, TRUE, &MenuWindowMonsterSkillNum[9], MenuSwitchNone },			//スキル使用魔力09

{ SWITCH_TEXT,	  25, 213+12*0,   0,  0, TRUE, &MenuWindowMonsterStatusText[10], MenuSwitchNone },		//スキル说明
{ SWITCH_TEXT,	  25, 213+12*1,   0,  0, TRUE, &MenuWindowMonsterStatusText[10], MenuSwitchNone },		//スキル说明
{ SWITCH_TEXT,	  25, 213+12*2,   0,  0, TRUE, &MenuWindowMonsterStatusText[10], MenuSwitchNone },		//スキル说明
{ SWITCH_TEXT,	  25, 213+12*3,   0,  0, TRUE, &MenuWindowMonsterStatusText[10], MenuSwitchNone },		//スキル说明

{ SWITCH_GRAPHIC, 17, 48+16*0, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル00
{ SWITCH_GRAPHIC, 17, 48+16*1, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル01
{ SWITCH_GRAPHIC, 17, 48+16*2, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル02
{ SWITCH_GRAPHIC, 17, 48+16*3, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル03
{ SWITCH_GRAPHIC, 17, 48+16*4, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル04
{ SWITCH_GRAPHIC, 17, 48+16*5, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル05
{ SWITCH_GRAPHIC, 17, 48+16*6, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル06
{ SWITCH_GRAPHIC, 17, 48+16*7, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル07
{ SWITCH_GRAPHIC, 17, 48+16*8, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル08
{ SWITCH_GRAPHIC, 17, 48+16*9, 206, 15, TRUE, &MenuWindowMonsterStatusGraph[28], MenuSwitchMonsterSkillSwitch },	//スキルパネル09

{ SWITCH_GRAPHIC, 17, 32,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[6], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[0], MenuSwitchNone },			//ウインドウ
{ SWITCH_GRAPHIC, 14, 25,   0,  0, TRUE, &MenuWindowMonsterStatusGraph[1], MenuSwitchNone },			//バック

{ SWITCH_NONE  , 250, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ
};

enum{
	EnumDialogMonsterSkillName,	

	EnumGraphMonsterSkillClose,	

	EnumGraphMonsterSkillStatusBt,	
	EnumGraphMonsterSkillDetailBt,	
	EnumGraphMonsterSkillSkillBt,	

	EnumGraphMonsterSkillNameSet,	

	EnumGraphMonsterSkillRelease,	

	EnumGraphMonsterSkillNoDown,	
	EnumGraphMonsterSkillNoUp,	
/*
	EnumGraphMenuMonsterSkillTumami,
	EnumBtMenuMonsterSkillDrag,
*/
	EnumGraphMenuMonsterSkillScrollUp,
	EnumGraphMenuMonsterSkillScrollDown,

	EnumTextMonsterSkillName,	
	EnumTextMonsterSkillPage,	

	EnumTextMonsterSkillName00,	
	EnumTextMonsterSkillName01,	
	EnumTextMonsterSkillName02,	
	EnumTextMonsterSkillName03,	
	EnumTextMonsterSkillName04,	
	EnumTextMonsterSkillName05,	
	EnumTextMonsterSkillName06,	
	EnumTextMonsterSkillName07,	
	EnumTextMonsterSkillName08,	
	EnumTextMonsterSkillName09,	

	EnumTextMonsterSkillFp00,	
	EnumTextMonsterSkillFp01,	
	EnumTextMonsterSkillFp02,	
	EnumTextMonsterSkillFp03,	
	EnumTextMonsterSkillFp04,	
	EnumTextMonsterSkillFp05,	
	EnumTextMonsterSkillFp06,	
	EnumTextMonsterSkillFp07,	
	EnumTextMonsterSkillFp08,	
	EnumTextMonsterSkillFp09,	

	EnumTextMonsterSkillMemo00,	
	EnumTextMonsterSkillMemo01,	
	EnumTextMonsterSkillMemo02,	
	EnumTextMonsterSkillMemo03,	

	EnumGraphMonsterSkillPanel00,	
	EnumGraphMonsterSkillPanel01,	
	EnumGraphMonsterSkillPanel02,	
	EnumGraphMonsterSkillPanel03,	
	EnumGraphMonsterSkillPanel04,	
	EnumGraphMonsterSkillPanel05,	
	EnumGraphMonsterSkillPanel06,	
	EnumGraphMonsterSkillPanel07,	
	EnumGraphMonsterSkillPanel08,	
	EnumGraphMonsterSkillPanel09,	

	EnumGraphMonsterSkillBase,	
	EnumGraphMonsterSkillWindow,	
	EnumGraphMonsterSkillBack,	

	EnumHitMonsterSkill1,	
	
	EnumMonsterSkillEnd,
};

#ifdef PUK3_MAIL_ETC
// ウィンドウ
const WINDOW_DATA WindowDataMonsterStatus = {
 0,															
#ifdef PUK3_WINDOW_OPEN_POINT
     4,365, 83,245,306,0x80010101,EnumMonsterStatusEnd,MonsterStatusSwitch, MenuWindowMonsterStatus,MenuWindowMonsterStatusDraw,MenuWindowMonsterStatusClose 
#else
     4,365, 83,245,306,0x80010101,EnumMonsterStatusEnd,MonsterStatusSwitch, MenuWindowMonsterStatus,MenuWindowMonsterStatusDraw,MenuWindowDel 
#endif
};

const WINDOW_DATA WindowDataMonsterDetail = {
 0,															
#ifdef PUK3_WINDOW_OPEN_POINT
     4,365, 83,245,306,0x80010101,EnumMonsterDetailEnd,MonsterDetailSwitch, MenuWindowMonsterDetail,MenuWindowMonsterDetailDraw,MenuWindowMonsterDetailClose 
#else
     4,365, 83,245,306,0x80010101,EnumMonsterDetailEnd,MonsterDetailSwitch, MenuWindowMonsterDetail,MenuWindowMonsterDetailDraw,MenuWindowDel 
#endif
};

const WINDOW_DATA WindowDataMonsterSkill = {
 0,															
#ifdef PUK3_WINDOW_OPEN_POINT
     4,365, 83,245,306,0x80010101,EnumMonsterSkillEnd,MonsterSkillSwitch, MenuWindowMonsterSkill,MenuWindowMonsterSkillDraw,MenuWindowMonsterSkillClose
#else
     4,365, 83,245,306,0x80010101,EnumMonsterSkillEnd,MonsterSkillSwitch, MenuWindowMonsterSkill,MenuWindowMonsterSkillDraw,MenuWindowDel 
#endif
};
#else
// ウィンドウ
const WINDOW_DATA WindowDataMonsterStatus = {
 0,															
     4,365, 83,250,306,0x80010101,EnumMonsterStatusEnd,MonsterStatusSwitch, MenuWindowMonsterStatus,MenuWindowMonsterStatusDraw,MenuWindowDel 
};

const WINDOW_DATA WindowDataMonsterDetail = {
 0,															
     4,365, 83,250,306,0x80010101,EnumMonsterDetailEnd,MonsterDetailSwitch, MenuWindowMonsterDetail,MenuWindowMonsterDetailDraw,MenuWindowDel 
};

const WINDOW_DATA WindowDataMonsterSkill = {
 0,															
     4,365, 83,250,306,0x80010101,EnumMonsterSkillEnd,MonsterSkillSwitch, MenuWindowMonsterSkill,MenuWindowMonsterSkillDraw,MenuWindowDel 
};
#endif

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateMonsterStatus={
	2,
	 0,  0,272, 30,
	 252,  0,20,100,
};

#endif