﻿/* generated by makedefs.perl */

#define PETSKILL_NONE ( 0 )
#define PETSKILL_NORMALATTACK ( 1 )
#define PETSKILL_NORMALGUARD ( 2 )
#define PETSKILL_GUARDBREAK ( 3 )
#define PETS<PERSON><PERSON>L_CONTINUATIONATTACK1 ( 10 )
#define PETSKILL_GUARDIAN ( 20 )
#define PETSKILL_MIGHTY1 ( 40 )
#define PETSKILL_POWERBALANCE1 ( 50 )
#define PETSKILL_POWERBALANCE2 ( 51 )
#define PETSKILL_POISON_ATTACK1 ( 60 )
#define PETSKILL_POISON_ATTACK2 ( 61 )
#define PETSKILL_STONE ( 80 )
#define PETSKILL_CONFUSION_ATTACK ( 90 )
#define PETSKILL_DRUNK_ATTACK ( 100 )
#define PETSKILL_CHARGE1 ( 30 )
#define PETSKILL_SLEEP_ATTACK ( 110 )
#define PETS<PERSON><PERSON>L_Abduct ( 130 )
#define PETSKILL_STEAL ( 140 )
#define PETSKILL_NOGUARD1 ( 150 )
#define PETSK<PERSON>L_MERGE ( 200 )
#define PETSKILL_EARTHROUND1 ( 120 )
#define PETSKILL_CONTINUATIONATTACK2 ( 11 )
#define PETSKILL_CONTINUATIONATTACK3 ( 12 )
#define PETSKILL_CONTINUATIONATTACK4 ( 13 )
#define PETSKILL_CHARGE2 ( 31 )
#define PETSKILL_MIGHTY2 ( 41 )
#define PETSKILL_NOGUARD2 ( 151 )
#define PETSKILL_NOGUARD3 ( 152 )
#define PETSKILL_POWERBALANCE3 ( 52 )
#define PETSKILL_MERGE2 ( 201 )

	
/* end of generated code */
