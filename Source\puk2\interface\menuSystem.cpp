﻿//メニュー＞システム

// メイン

static char SystemFrameFlag;
static int SystemFramePos;
static int SystemFrameAcc;

#define SYSWINSTARTLINE 16
#define SYSWINFRAMEACCEL 17

void LogoutFrameMove( int dif )
{
	WINDOW_INFO *wi = WindowFlag[MENU_WINDOW_SYSTEM].wininfo;
	GRAPHIC_SWITCH *Gra;

	wi->sw[EnumGraphSystemFrameLogOut].ofx = SYSWINSTARTLINE + dif + 0;
	Gra = (GRAPHIC_SWITCH *)wi->sw[EnumGraphSystemFrameLogOut].Switch;
	Gra->w = SYSWINSTARTLINE - wi->sw[EnumGraphSystemFrameLogOut].ofx;
	if (Gra->w>wi->sw[EnumGraphSystemFrameLogOut].sx) Gra->w = wi->sw[EnumGraphSystemFrameLogOut].sx;

	if (dif<=-146){
		wi->sw[EnumGraphSystemLoginGate].Enabled = TRUE;
		wi->sw[EnumGraphSystemServerSel].Enabled = TRUE;
	}else{
		wi->sw[EnumGraphSystemLoginGate].Enabled = FALSE;
		wi->sw[EnumGraphSystemServerSel].Enabled = FALSE;
	}
}

void OperationFrameMove( int dif )
{
	WINDOW_INFO *wi = WindowFlag[MENU_WINDOW_SYSTEM].wininfo;
	GRAPHIC_SWITCH *Gra;

	wi->sw[EnumGraphSystemFrameOperation].ofx = SYSWINSTARTLINE + dif + 0;
	Gra = (GRAPHIC_SWITCH *)wi->sw[EnumGraphSystemFrameOperation].Switch;
	Gra->w = SYSWINSTARTLINE - wi->sw[EnumGraphSystemFrameOperation].ofx;
	if (Gra->w>wi->sw[EnumGraphSystemFrameOperation].sx) Gra->w = wi->sw[EnumGraphSystemFrameOperation].sx;

	if (dif<=-146){
		wi->sw[EnumGraphSystem____Left].Enabled = TRUE;
		wi->sw[EnumGraphSystemMSmooth].Enabled = TRUE;
		wi->sw[EnumGraphSystemShortCutType].Enabled = TRUE;
	}else{
		wi->sw[EnumGraphSystem____Left].Enabled = FALSE;
		wi->sw[EnumGraphSystemMSmooth].Enabled = FALSE;
		wi->sw[EnumGraphSystemShortCutType].Enabled = FALSE;
	}
}

void SoundFrameMove( int dif )
{
	WINDOW_INFO *wi = WindowFlag[MENU_WINDOW_SYSTEM].wininfo;
	GRAPHIC_SWITCH *Gra;

	wi->sw[EnumGraphSystemFrameSoundSet].ofx = SYSWINSTARTLINE + dif + 0;
	Gra = (GRAPHIC_SWITCH *)wi->sw[EnumGraphSystemFrameSoundSet].Switch;
	Gra->w = SYSWINSTARTLINE - wi->sw[EnumGraphSystemFrameSoundSet].ofx;
	if (Gra->w>wi->sw[EnumGraphSystemFrameSoundSet].sx) Gra->w = wi->sw[EnumGraphSystemFrameSoundSet].sx;

	if (dif<=-146){
		wi->sw[EnumGraphBgmText].Enabled = TRUE;
		wi->sw[EnumGraphBgmText].ofx =
			SYSWINSTARTLINE + dif + 54 + ( ( 36-GraphicNumberWidth( SystemNum_BGM, G_NUM_SIZE__9_S )+1 )>>1 ) - 1;

		wi->sw[EnumGraphBgmLeft].Enabled = TRUE;
		wi->sw[EnumGraphBgmRight].Enabled = TRUE;

		wi->sw[EnumGraphSeText].Enabled = TRUE;
		wi->sw[EnumGraphSeText].ofx =
			SYSWINSTARTLINE + dif + 54 + ( ( 36-GraphicNumberWidth( SystemNum_Sound, G_NUM_SIZE__9_S )+1 )>>1 ) - 1;

		wi->sw[EnumGraphSeLeft].Enabled = TRUE;
		wi->sw[EnumGraphSeRight].Enabled = TRUE;

		wi->sw[EnumGraphSystemStereo].Enabled = TRUE;
		wi->sw[EnumGraphSystemChatSe].Enabled = TRUE;
	}else{
		wi->sw[EnumGraphBgmText].Enabled = FALSE;

		wi->sw[EnumGraphBgmLeft].Enabled = FALSE;
		wi->sw[EnumGraphBgmRight].Enabled = FALSE;

		wi->sw[EnumGraphSeText].Enabled = FALSE;

		wi->sw[EnumGraphSeLeft].Enabled = FALSE;
		wi->sw[EnumGraphSeRight].Enabled = FALSE;

		wi->sw[EnumGraphSystemStereo].Enabled = FALSE;
		wi->sw[EnumGraphSystemChatSe].Enabled = FALSE;
	}
}

void DrawSetFrameMove( int dif )
{
	WINDOW_INFO *wi = WindowFlag[MENU_WINDOW_SYSTEM].wininfo;
	GRAPHIC_SWITCH *Gra;

	wi->sw[EnumGraphSystemFrameDrawSet].ofx = SYSWINSTARTLINE + dif + 0;
	Gra = (GRAPHIC_SWITCH *)wi->sw[EnumGraphSystemFrameDrawSet].Switch;
	Gra->w = SYSWINSTARTLINE - wi->sw[EnumGraphSystemFrameDrawSet].ofx;
	if (Gra->w>wi->sw[EnumGraphSystemFrameDrawSet].sx) Gra->w = wi->sw[EnumGraphSystemFrameDrawSet].sx;

	wi->sw[EnumGraphSystemSynchro].Enabled = FALSE;
#ifdef PUK2_CHANGE_3DDEIVCE
	if (dif<=-146){
		wi->sw[EnumGraphSystemSynchro].Enabled = TRUE;
	#ifdef PUK2_FPS
		wi->sw[EnumGraphSystemChange3DDevece0].Enabled = TRUE;
	#else
		wi->sw[EnumGraphSystemChange3DDevece0].Enabled = FALSE;
	#endif
		wi->sw[EnumGraphSystemChange3DDevece1].Enabled = TRUE;
		wi->sw[EnumGraphSystemChange3DDevece2].Enabled = TRUE;
		wi->sw[EnumGraphSystemChange3DDevece3].Enabled = TRUE;
		wi->sw[EnumGraphSystemChange3DDevece4].Enabled = TRUE;
	}
#elif defined(PUK2_3DDEVICE_DISP)
	if (dif<=-146){
		wi->sw[EnumGraphSystemSynchro].Enabled = TRUE;
		wi->sw[EnumGraphSystemChange3DDevece0].Enabled = FALSE;
		wi->sw[EnumGraphSystemChange3DDevece1].Enabled = FALSE;
		wi->sw[EnumGraphSystemChange3DDevece2].Enabled = FALSE;
		wi->sw[EnumGraphSystemChange3DDevece3].Enabled = FALSE;
		wi->sw[EnumGraphSystemChange3DDevece4].Enabled = FALSE;

		wi->sw[EnumGraphSystemChange3DDevece0+getNow3D_mode()].Enabled = TRUE;
	}
#else
	if (dif<=-146) wi->sw[EnumGraphSystemSynchro].Enabled = TRUE;
#endif
}

void (*SystemFrameMove[4])( int dif ) = { LogoutFrameMove, OperationFrameMove, SoundFrameMove, DrawSetFrameMove };

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowSystem( int mouse )
{
	if (mouse==WIN_INIT){
		int i;

		// 全部非表示
		for(i=EnumGraphSystemLoginGate;i<EnumSystemDragBack;i++) wI->sw[i].Enabled = FALSE;

		SystemFrameFlag = -1;
		SystemFramePos = 0;
	}

	return TRUE;
}

BOOL MenuWindowSystemDraw( int mouse )
{
	if (SystemFrameFlag>=0 && SystemFramePos>-146 ){
		SystemFramePos -= SystemFrameAcc;
		SystemFrameAcc--;
		if (SystemFramePos<-146) SystemFramePos = -146;
		SystemFrameMove[SystemFrameFlag]( SystemFramePos );
	}

	displayMenuWindow();

	return TRUE;

}

BOOL closeMenuWindowSystem()
{
	// メニュー关闭瞬间に情报を保存
	setUserSoundOption();
	setUserInterfaceOption();
	setUserChatOption( selectPcNo );
	setUserChatRegStr();
	setUserMailSetting();
	saveNowState();

	// ウィンドウ关闭音
	play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

	return TRUE;
}

//--------------------------------------------------------
//ボタン处理
//--------------------------------------------------------
BOOL MenuSytemSwitchClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	//状态ボタン画像设定
	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		//重なってる
		strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchLogout( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	Graph->graNo = GID_SystemLogOutOn;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_SystemLogOutOver;
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_LOGOUT );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		BOOL flg = wI->sw[EnumGraphSystemFrameLogOut].Enabled;

		// 先ずは全部非表示
		for(i=EnumGraphSystemLoginGate;i<EnumSystemDragBack;i++) wI->sw[i].Enabled = FALSE;

		SystemFrameFlag = -1;
		SystemFramePos = 0;

		if (!flg){
			SystemFrameFlag = 0;
			SystemFrameAcc = SYSWINFRAMEACCEL;

			// 表示
			wI->sw[EnumGraphSystemLoginGate].Enabled = TRUE;
			wI->sw[EnumGraphSystemServerSel].Enabled = TRUE;

			wI->sw[EnumGraphSystemFrameLogOut].Enabled = TRUE;
		}

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (wI->sw[EnumGraphSystemFrameLogOut].Enabled) Graph->graNo = GID_SystemLogOutOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchOperation( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	Graph->graNo = GID_SystemOperationOn;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_SystemOperationOver;
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_OPERATION );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		BOOL flg = wI->sw[EnumGraphSystemFrameOperation].Enabled;

		// 先ずは全部非表示
		for(i=EnumGraphSystemLoginGate;i<EnumSystemDragBack;i++) wI->sw[i].Enabled = FALSE;

		SystemFrameFlag = -1;
		SystemFramePos = 0;

		if (!flg){
			SystemFrameFlag = 1;
			SystemFrameAcc = SYSWINFRAMEACCEL;

			// 表示
			wI->sw[EnumGraphSystem____Left].Enabled = TRUE;
			wI->sw[EnumGraphSystemMSmooth].Enabled = TRUE;
			wI->sw[EnumGraphSystemShortCutType].Enabled = TRUE;

			wI->sw[EnumGraphSystemFrameOperation].Enabled = TRUE;

			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphSystem____Left].Switch;
			Graph->graNo = GID_SystemCtrlLeftOn;
			if (ctrlSetting) Graph->graNo = GID_System____LeftOn;

			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphSystemMSmooth].Switch;
			Graph->graNo = GID_SystemMNomalOn;
			if (MouseCursorFlag) Graph->graNo = GID_SystemMSmoothOn;
		}

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (wI->sw[EnumGraphSystemFrameOperation].Enabled) Graph->graNo = GID_SystemOperationOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchSound( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	Graph->graNo = GID_SystemSoundSetOn;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_SystemSoundSetOver;
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SOUND );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		BOOL flg = wI->sw[EnumGraphSystemFrameSoundSet].Enabled;

		// 先ずは全部非表示
		for(i=EnumGraphSystemLoginGate;i<EnumSystemDragBack;i++) wI->sw[i].Enabled = FALSE;

		SystemFrameFlag = -1;
		SystemFramePos = 0;

		if (!flg){
			int Width;

			SystemFrameFlag = 2;
			SystemFrameAcc = SYSWINFRAMEACCEL;

			// 表示
			wI->sw[EnumGraphSystemStereo].Enabled = TRUE;

			wI->sw[EnumGraphBgmText].Enabled = TRUE;
			wI->sw[EnumGraphBgmLeft].Enabled = TRUE;
			wI->sw[EnumGraphBgmRight].Enabled = TRUE;

			wI->sw[EnumGraphSeText].Enabled = TRUE;
			wI->sw[EnumGraphSeLeft].Enabled = TRUE;
			wI->sw[EnumGraphSeRight].Enabled = TRUE;

			wI->sw[EnumGraphSystemFrameSoundSet].Enabled = TRUE;

			// 音量表示变更
			sprintf( SystemNum_BGM, "%d", t_music_bgm_volume );                //MLHIDE

			Width = GraphicNumberWidth( SystemNum_BGM, G_NUM_SIZE__9_S );
			wI->sw[EnumGraphBgmText].ofx = SYSWINSTARTLINE + SystemFramePos + 54 + ( (36-Width+1)>>1 ) - 1;

			// 音量表示变更
			sprintf( SystemNum_Sound, "%d", t_music_se_volume );               //MLHIDE

			Width = GraphicNumberWidth( SystemNum_Sound, G_NUM_SIZE__9_S );
			wI->sw[EnumGraphSeText].ofx = SYSWINSTARTLINE + SystemFramePos + 54 + ( (36-Width+1)>>1 ) - 1;
		}

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (wI->sw[EnumGraphSystemFrameSoundSet].Enabled) Graph->graNo = GID_SystemSoundSetOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchDrawSet( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	Graph->graNo = GID_SystemDrawSetOn;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_SystemDrawSetOver;
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_DRAWSET );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		BOOL flg = wI->sw[EnumGraphSystemFrameDrawSet].Enabled;

		// 先ずは全部非表示
		for(i=EnumGraphSystemLoginGate;i<EnumSystemDragBack;i++) wI->sw[i].Enabled = FALSE;

		SystemFrameFlag = -1;
		SystemFramePos = 0;

		if (!flg){
			SystemFrameFlag = 3;
			SystemFrameAcc = SYSWINFRAMEACCEL;

			// 表示
			wI->sw[EnumGraphSystemSynchro].Enabled = TRUE;

			wI->sw[EnumGraphSystemFrameDrawSet].Enabled = TRUE;

			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphSystemSynchro].Switch;
			Graph->graNo = GID_SystemSynchroOn;
			if (RasterNoWaitMode) Graph->graNo = GID_SystemAsynchroOn;
		}

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (wI->sw[EnumGraphSystemFrameDrawSet].Enabled) Graph->graNo = GID_SystemDrawSetOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchShortCut( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_SystemShortCutOn;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_SystemShortCutOver;
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SHORTCUT );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if(WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo==NULL){
			openMenuWindow( MENU_WINDOW_SYSTEMSHORTCUT, OPENMENUWINDOW_HIT, 0 );
		}else{
			WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}

		ReturnFlag = TRUE;
	}

	if ( WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo ) Graph->graNo = GID_SystemShortCutOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchLoginGate( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SYSTEM_LOGINGATE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

#ifdef PUK3_BATTLECHECK_SERVER
		if( !offlineFlag ){
			nrproto_CharLoginGate_send( sockfd );
		}
#else
		nrproto_CharLoginGate_send( sockfd );
#endif

		// 战闘中なら終了プロセスへ
		if( ProcNo == PROC_BATTLE ) SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;

		// 登出音
		play_se( SE_NO_LOGOUT, 320, 240 );

		// 简易登出フラグＯＮ
		SimpleLogoutFlag = TRUE;

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SystemLoginGateOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SystemLoginGateOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SystemLoginGateOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchServerSel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_SystemServerSelOver;
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SERVERSELECT );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

#ifdef PUK3_BATTLEEND_LOGOUT
		// 战闘中なら終了プロセスへ
		if( ProcNo == PROC_BATTLE ){
			SubProcNo = BATTLE_PROC_LOGOUT_INIT;
		}else{
			GameState = GAME_LOGIN;
			ChangeProc2( PROC_CHAR_LOGOUT );
		}
#else
		GameState = GAME_LOGIN;
		ChangeProc2( PROC_CHAR_LOGOUT );
#endif

		// 登出音
		play_se( SE_NO_LOGOUT, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SystemServerSelOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SystemServerSelOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SystemServerSelOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchClick( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_ITEMMOVE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		ctrlSetting++;
		ctrlSetting &= 1;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (ctrlSetting){
		Graph->graNo = GID_System____LeftOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_System____LeftOver;
	}else{
		Graph->graNo = GID_SystemCtrlLeftOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemCtrlLeftOver;
	}


	return ReturnFlag;
}

BOOL MenuSytemSwitchCursor( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_CURSOR );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// マウスカーソル变更
		MouseCursorFlag = !MouseCursorFlag;
#ifndef VERSION_TW
		//台服显示老版鼠标样式所以取消
		ShowCursor( MouseCursorFlag );
#endif

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (MouseCursorFlag){
		Graph->graNo = GID_SystemMSmoothOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemMSmoothOver;
#ifdef VERSION_TW
		//修复老版鼠标样式显示的颜色
		StockDispBuffer(
			mouse.nowPoint.x + 16,
			mouse.nowPoint.y + 16,
			DISP_PRIO_MOUSE, CG_MOUSE_CURSOR, 0, 0);
#endif
	}else{
		Graph->graNo = GID_SystemMNomalOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemMNomalOver;
	}

	return ReturnFlag;
}

const int SysShortCutTypeGraNo[][3]={
	{ GID_SystemShortCutPUK1On, GID_SystemShortCutPUK1Off, GID_SystemShortCutPUK1Over },
	{ GID_SystemShortCutPUK2On, GID_SystemShortCutPUK2Off, GID_SystemShortCutPUK2Over },
};
BOOL MenuSytemSwitchShortCutType( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, ML_STRING(908, "快捷方式变更。") );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// マウスカーソル变更
		ShortCutMode++;
		if (ShortCutMode==ShortCutModeEnd) ShortCutMode = ShortCutModeStart;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = SysShortCutTypeGraNo[ShortCutMode-ShortCutModeStart][0];
	if(flag & MENU_MOUSE_OVER) Graph->graNo = SysShortCutTypeGraNo[ShortCutMode-ShortCutModeStart][2];

	return ReturnFlag;
}

BOOL MenuSytemSwitchBgmLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		if( t_music_bgm_volume > 0 ) strcpy( OneLineInfoStr, MWONELINE_SYSTEM_BGMDOWN );
		else strcpy( OneLineInfoStr, MWONELINE_SYSTEM_BGMDOWN_MIN );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// 下限に达していないので音量减らす
		if( t_music_bgm_volume > 0 ){
			int Width;

			t_music_bgm_volume--;
			bgm_volume_change();

			// 音量表示变更
			sprintf( SystemNum_BGM, "%d", t_music_bgm_volume );                //MLHIDE

			Width = GraphicNumberWidth( SystemNum_BGM, G_NUM_SIZE__9_S );
			wI->sw[EnumGraphBgmText].ofx = SYSWINSTARTLINE + SystemFramePos + 54 + ( (36-Width+1)>>1 ) - 1;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		// 下限なのでＮＧ音
		else play_se( SE_NO_NG, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchBgmRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		if( t_music_bgm_volume < MAX_BGM_VOL ) strcpy( OneLineInfoStr, MWONELINE_SYSTEM_BGMUP );
		else strcpy( OneLineInfoStr, MWONELINE_SYSTEM_BGMUP_MAX );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// 上限に达していないので音量增やす
		if( t_music_bgm_volume < MAX_BGM_VOL ){
			int Width;

			t_music_bgm_volume++;
			bgm_volume_change();

			// 音量表示变更
			sprintf( SystemNum_BGM, "%d", t_music_bgm_volume );                //MLHIDE

			Width = GraphicNumberWidth( SystemNum_BGM, G_NUM_SIZE__9_S );
			wI->sw[EnumGraphBgmText].ofx = SYSWINSTARTLINE + SystemFramePos + 54 + ( (36-Width+1)>>1 ) - 1;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		// 上限なのでＮＧ音
		else play_se( SE_NO_NG, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchSeLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		if( t_music_se_volume > 0 ) strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SEDOWN );
		else strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SEDOWN_MIN );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// 下限に达していないので音量增やす
		if( t_music_se_volume > 0 ){
			int Width;

			t_music_se_volume--;
			bgm_volume_change();

			// 音量表示变更
			sprintf( SystemNum_Sound, "%d", t_music_se_volume );               //MLHIDE

			Width = GraphicNumberWidth( SystemNum_Sound, G_NUM_SIZE__9_S );
			wI->sw[EnumGraphSeText].ofx = SYSWINSTARTLINE + SystemFramePos + 54 + ( (36-Width+1)>>1 ) - 1;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		// 下限なのでＮＧ音
		else play_se( SE_NO_NG, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchSeRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		if( t_music_se_volume < MAX_SE_VOL ) strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SEUP );
		else strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SEUP_MAX );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// 上限に达していないので音量增やす
		if( t_music_se_volume < MAX_SE_VOL ){
			int Width;

			t_music_se_volume++;
			bgm_volume_change();

			// 音量表示变更
			sprintf( SystemNum_Sound, "%d", t_music_se_volume );               //MLHIDE

			Width = GraphicNumberWidth( SystemNum_Sound, G_NUM_SIZE__9_S );
			wI->sw[EnumGraphSeText].ofx = SYSWINSTARTLINE + SystemFramePos + 54 + ( (36-Width+1)>>1 ) - 1;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		// 上限なのでＮＧ音
		else play_se( SE_NO_NG, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSytemSwitchSoundMode( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SETYPE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		stereo_flg++;
		stereo_flg &= 1;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (stereo_flg){
		Graph->graNo = GID_SystemStereoOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemStereoOver;
	}else{
		Graph->graNo = GID_SystemMonoralOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemMonoralOver;
	}

	return ReturnFlag;
}

BOOL MenuSytemSwitchChatSe( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_SETYPE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		NowChatSound++;
		NowChatSound &= 1;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (NowChatSound){
		Graph->graNo = GID_SystemChatSeOnOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemChatSeOnOver;
	}else{
		Graph->graNo = GID_SystemChatSeOffOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemChatSeOffOver;
	}

	return ReturnFlag;
}

BOOL MenuSytemSwitchSynchro( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_DRAWSETBUTTON );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		RasterNoWaitMode++;
		RasterNoWaitMode &= 1;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	if (RasterNoWaitMode){
		Graph->graNo = GID_SystemAsynchroOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemAsynchroOver;
	}else{
		Graph->graNo = GID_SystemSynchroOn;
		if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_SystemSynchroOver;
	}

	return ReturnFlag;
}

#ifdef PUK2_CHANGE_3DDEIVCE
/*
const char *Change3DDeviceOneLineInfo[] = {
	"现在无法变更绘图方式。",
	MWONELINE_SYSTEM_3DDEVICEPARTY,
	MWONELINE_SYSTEM_3DDEVICEWATCH,
	MWONELINE_SYSTEM_3DDEVICEDUEL,
	"禁止绘图方式变更的窗口正在打开，无法变更。"
};
*/
BOOL MenuSytemSwitchChange3DDevice( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = no - EnumGraphSystemChange3DDevece0;
	int type = 0;

	// 布ティーを组んでいるなら
	if ( partyModeFlag || (pc.status & CHR_STATUS_LEADER) != 0 ) type = 1;
	// 战闘中
	else if ( ProcNo == PROC_BATTLE ){
		// 観战中なら
		if ( BattleMyNo >= BC_MAX ) type = 2;
		// デュエル中なら
		if ( DuelFlag == TRUE  ) type = 3;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	// 邪魔なウィンドウが开いている
	else if ( Lock3DChangeWindowCnt ) type = 4;
#endif

	// 布ティーを组んでいるなら
	if ( type > 0 ){
		//重なってるかチェック
		if(flag & MENU_MOUSE_OVER){
			// 一行インフォ
//			strcpy( OneLineInfoStr, Change3DDeviceOneLineInfo[type] );
			strcpy( OneLineInfoStr, MWONELINE_SYSTEM_3DDEVICENOCHG );
		}
		if( flag & MENU_MOUSE_LEFT ){
			// プライオリティを最上位に
			wI->flag |= WIN_INFO_PRIO;
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );

			ReturnFlag = TRUE;
		}
	}else{
		//重なってるかチェック
		if(flag & MENU_MOUSE_OVER){
			// 一行インフォ
			strcpy( OneLineInfoStr, MWONELINE_SYSTEM_3DDEVICECHANGE );
		}

		//押されたとき
		if ( getNow3D_mode() != Num ){
			if( flag & MENU_MOUSE_LEFT ){
				// プライオリティを最上位に
				wI->flag |= WIN_INFO_PRIO;

				device3D = Num;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag = TRUE;
			}
		}
	}

	Graph->graNo = GID_0ButtonOn + Num * 3;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_0ButtonOver + Num * 3;
	if ( getNow3D_mode() == Num ) Graph->graNo = GID_0ButtonOff + Num * 3;

	return ReturnFlag;
}
#elif defined(PUK2_3DDEVICE_DISP)
BOOL MenuSytemSwitchChange3DDevice( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = no - EnumGraphSystemChange3DDevece0;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SYSTEM_3DDEVICESHOW );
	}

	Graph->graNo = GID_0ButtonOn + Num * 3;

	return ReturnFlag;
}
#endif




//*******************************************************//

// ショートカット

static char NowInputChatShortCut;
static INIT_STR_STRUCT InitStrStructShortCut={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	NULL,  0,  0,FONT_PRIO_WIN,FONT_KIND_SIZE_11,FONT_PAL_WHITE,"",	  1,      MAX_CHAT_REG_STR_LEN,  0,     0
};

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowSystemShortCutBf( int mouse )
{
	if (mouse == WIN_INIT){
		struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)&WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT];
		int i;

		wm->DispStart = 0;

		wI->sw[EnumGraphSystemShortCutScroll].ofy = wI->sw[EnumBtSystemShortCutScroll].ofy - (wI->sw[EnumGraphSystemShortCutScroll].sy>>1);
		NowInputChatShortCut = -1;

		if ( wm->DispType == EnumShortCutStrChat ){
			for(i=EnumGraphSystemShortCutTextBox0;i<=EnumGraphSystemShortCutTextBox9;i++) wI->sw[i].func = MenuSwitchSystemShortCutPanelHit;
		}else{
			for(i=EnumGraphSystemShortCutTextBox0;i<=EnumGraphSystemShortCutTextBox9;i++) wI->sw[i].func = MenuSwitchNone;
		}
	}

	return TRUE;
}

BOOL MenuWindowSystemShortCutAf( int mouse )
{
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)&WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT];
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)wI->sw[EnumBtSystemShortCutScroll].Switch;
	int i;

	wI->sw[EnumGraphSystemShortCutScroll].Enabled = TRUE;
	if (ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType]-10<=0) wI->sw[EnumGraphSystemShortCutScroll].Enabled = FALSE;
	// つまみを移动中なら、表示开始位置を变更
	if (Bt->status&1){
		wm->DispStart = ScrollVPointToNum( &wI->sw[EnumBtSystemShortCutScroll], ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType]-10 );
	}

	for( i = 0; i < 10; i++ ){
		StockFontBuffer( wI->wx+SHORTCUTSTR_POSX, wI->wy+SHORTCUTSTR_POSY+i*SHORTCUTSTR_HIGHT,
			FONT_PRIO_WIN, FONT_KIND_SIZE_11, FONT_PAL_WHITE,
			ShortCutStr[ShortCutMode-ShortCutModeStart][wm->DispType][wm->DispStart+i], 0, BoxColor );
	}

	// チャット登録モードなら
	if ( wm->DispType == EnumShortCutStrChat ){
		// チャット登録の表示
		for(i=0;i<10;i++){
			if (wm->DispStart+i==NowInputChatShortCut){
				// 文字入力栏移动
				SetInputStr( &InitStrStructShortCut, wI->wx+SHORTCUTSTR_POSX+65, wI->wy+SHORTCUTSTR_POSY+i*SHORTCUTSTR_HIGHT, 2 );
			}else{
				StockFontBuffer( wI->wx+SHORTCUTSTR_POSX+65, wI->wy+SHORTCUTSTR_POSY+i*SHORTCUTSTR_HIGHT,
					FONT_PRIO_WIN, FONT_KIND_SIZE_11, FONT_PAL_WHITE,
					chatRegStr[wm->DispStart+i].buffer, 0, BoxColor );
			}
		}

		if ( NowInputChatShortCut >= 0 ){
			if ( wm->DispStart > NowInputChatShortCut || NowInputChatShortCut >= wm->DispStart+10 ){
				// 文字入力栏移动
				SetInputStr( &InitStrStructShortCut, 0, -100, 2 );
			}
		}
	}else{
		if ( &chatRegStr[0]<=pNowInputStr && pNowInputStr<=&chatRegStr[MAX_CHAT_REG-1] ){
			SetDialogMenuChat();
			NowInputChatShortCut = -1;
		}
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
//ボタン处理
//--------------------------------------------------------
BOOL MenuSwitchSystemShortCutClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	Graph->graNo=GID_WindowCloseOn;
	if(flag & MENU_MOUSE_OVER){
		//重なってる
		Graph->graNo=GID_WindowCloseOver;
		strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		//ウインドウ作成されてるので画像をＯＮに
		Graph->graNo=GID_WindowCloseOff;

		wI->flag |= WIN_INFO_DEL;

		setUserChatRegStr();

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (wm->DispStart>0){
			wm->DispStart--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSystemShortCutScroll], ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType]-10, wm->DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if ( wm->DispStart < ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType] - 10 ){
			wm->DispStart++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSystemShortCutScroll], ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType]-10, wm->DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (wm->DispStart>0){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		wm->DispStart-=10;
		if (wm->DispStart<0) wm->DispStart = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSystemShortCutScroll], ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType]-10, wm->DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if ( (ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType] - 10>0)
			&& (wm->DispStart<ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType] - 10) ){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		wm->DispStart+=10;
		if ( wm->DispStart > ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType] - 10 ){
			wm->DispStart = ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType] - 10;
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSystemShortCutScroll], ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType]-10, wm->DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		wm->DispStart = WheelToMove( &wI->sw[no-5], wm->DispStart,
			 ShortCutStrNum[ShortCutMode-ShortCutModeStart][wm->DispType] - 10,
			 mouse.wheel );
	}

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 重なってるなら一行インフォ
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SHORTCUT_WINDOW );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		int i;

		if ( wm->DispType != EnumShortCutStrWindow ){
			wm->DispStart = 0;
			wm->DispType = EnumShortCutStrWindow;

			wI->sw[EnumGraphSystemShortCutScroll].ofy = wI->sw[EnumBtSystemShortCutScroll].ofy - (wI->sw[EnumGraphSystemShortCutScroll].sy>>1);

			for(i=EnumGraphSystemShortCutTextBox0;i<=EnumGraphSystemShortCutTextBox9;i++){
				wI->sw[i].func = MenuSwitchNone;
				( (GRAPHIC_SWITCH *)wI->sw[i].Switch )->graNo = GID_TitlePanelOn;
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SystemShortCutBtWindowOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SystemShortCutBtWindowOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SystemShortCutBtWindowOff;
	if ( wm->DispType == EnumShortCutStrWindow ) Graph->graNo = GID_SystemShortCutBtWindowOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutSwitch( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 重なってるなら一行インフォ
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SHORTCUT_SWITCH );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		int i;

		if ( wm->DispType != EnumShortCutStrSwitch ){
			wm->DispStart = 0;
			wm->DispType = EnumShortCutStrSwitch;

			wI->sw[EnumGraphSystemShortCutScroll].ofy = wI->sw[EnumBtSystemShortCutScroll].ofy - (wI->sw[EnumGraphSystemShortCutScroll].sy>>1);

			for(i=EnumGraphSystemShortCutTextBox0;i<=EnumGraphSystemShortCutTextBox9;i++){
				wI->sw[i].func = MenuSwitchNone;
				( (GRAPHIC_SWITCH *)wI->sw[i].Switch )->graNo = GID_TitlePanelOn;
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SystemShortCutBtSwitchOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SystemShortCutBtSwitchOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SystemShortCutBtSwitchOff;
	if ( wm->DispType == EnumShortCutStrSwitch ) Graph->graNo = GID_SystemShortCutBtSwitchOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutAction( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 重なってるなら一行インフォ
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SHORTCUT_ACTION );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		int i;

		if ( wm->DispType != EnumShortCutStrAction ){
			wm->DispStart = 0;
			wm->DispType = EnumShortCutStrAction;

			wI->sw[EnumGraphSystemShortCutScroll].ofy = wI->sw[EnumBtSystemShortCutScroll].ofy - (wI->sw[EnumGraphSystemShortCutScroll].sy>>1);

			for(i=EnumGraphSystemShortCutTextBox0;i<=EnumGraphSystemShortCutTextBox9;i++){
				wI->sw[i].func = MenuSwitchNone;
				( (GRAPHIC_SWITCH *)wI->sw[i].Switch )->graNo = GID_TitlePanelOn;
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SystemShortCutBtActionOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SystemShortCutBtActionOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SystemShortCutBtActionOff;
	if ( wm->DispType == EnumShortCutStrAction ) Graph->graNo = GID_SystemShortCutBtActionOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutChat( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 重なってるなら一行インフォ
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SHORTCUT_CHAT );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		int i;

		if ( wm->DispType != EnumShortCutStrChat ){
			wm->DispStart = 0;
			wm->DispType = EnumShortCutStrChat;

			wI->sw[EnumGraphSystemShortCutScroll].ofy = wI->sw[EnumBtSystemShortCutScroll].ofy - (wI->sw[EnumGraphSystemShortCutScroll].sy>>1);

			for(i=EnumGraphSystemShortCutTextBox0;i<=EnumGraphSystemShortCutTextBox9;i++) wI->sw[i].func = MenuSwitchSystemShortCutPanelHit;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SystemShortCutBtChatOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SystemShortCutBtChatOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SystemShortCutBtChatOff;
	if ( wm->DispType == EnumShortCutStrChat ) Graph->graNo = GID_SystemShortCutBtChatOff;

	return ReturnFlag;
}

BOOL MenuSwitchSystemShortCutPanelHit( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct SYSTEMSHORTCUTMASTER *wm = (struct SYSTEMSHORTCUTMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = wm->DispStart + no - EnumGraphSystemShortCutTextBox0;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		char str[INPUT_STR_SIZE];

		NowInputChatShortCut = Num;

		strcpy( str, chatRegStr[NowInputChatShortCut].buffer );

		InitStrStructShortCut.inputStr = &chatRegStr[NowInputChatShortCut];
		// ダイアログ初期化
		SetInputStr( &InitStrStructShortCut, 0, 0, 0 );

		GetKeyInputFocus( &chatRegStr[NowInputChatShortCut] );

		// 入力初期化
		StrToNowInputStr( str );

		DiarogST.SwAdd = wI->sw[EnumSystemShortCutInputStr].Switch;
		( (DIALOG_SWITCH *)wI->sw[EnumSystemShortCutInputStr].Switch )->InpuStrAdd = &chatRegStr[NowInputChatShortCut];

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_TitlePanelOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_TitlePanelOver;
	if ( Num == NowInputChatShortCut ) Graph->graNo = GID_TitlePanelOff;

	return ReturnFlag;
}
