﻿//メニュー＞スキル

#ifndef _MENUSKILL_H_
#define _MENUSKILL_H_

//====================================//
//				メイン				  //
//====================================//

// ボタン处理关数 *********************//
BOOL MenuSwitchSkillClose( int no, unsigned int flag );

BOOL MenuSwitchSkillBack( int no, unsigned int flag );
BOOL MenuSwitchSkillRibirth( int no, unsigned int flag );

BOOL MenuSwitchSkillScrollUp( int no, unsigned int flag );
BOOL MenuSwitchSkillScrollDown( int no, unsigned int flag );
BOOL MenuSwitchSkillScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchSkillScrollRight( int no, unsigned int flag );
BOOL MenuSwitchSkillScrollWheel( int no, unsigned int flag );

BOOL MenuSwitchSkillSkillPanel( int no, unsigned int flag );
BOOL MenuSwitchSkillNamePanel( int no, unsigned int flag );
BOOL MenuSwitchRecipeNamePanel( int no, unsigned int flag );

BOOL MenuSwitchSkillBattleJobPanel( int no, unsigned int flag );
BOOL MenuSwitchSkillBattleNamePanel( int no, unsigned int flag );

BOOL MenuSwitchSkillBattlePetPanel( int no, unsigned int flag );
#ifdef PUK3_RIDE_BATTLE
	BOOL MenuSwitchSkillBattlePetRidePanel( int no, unsigned int flag );
#endif

BOOL MenuSwitchSkillBattlePetPass( int no, unsigned int flag );

BOOL MenuWindowSkillBf( int mouse );
BOOL MenuWindowSkillAf( int mouse );
BOOL closeSkillWindow();


GRAPHIC_SWITCH MenuWindowSkillGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	// クローズボタン

	{GID_SkillJobBase,0,0,0,0,0xFFFFFFFF},		// ベース
	{GID_SkillBack,0,0,0,0,0x80FFFFFF},			// 背景

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},			// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(下ボタン)

	{GID_CommonBackOn,0,0,0,0,0xFFFFFFFF},		// バックボタン
	{GID_CommonRebirthOn,0,0,0,0,0xFFFFFFFF},	// リバースボタン
	{GID_CommonRebirthOn,0,0,0,0,0xFFFFFFFF},	// パスボタン

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(右ボタン)
};

BUTTON_SWITCH MenuWindowSkillButton[]={
	{0,0,0},
};

TEXT_SWITCH MenuWindowSkillText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"????????"},				// ジョブタイトル        //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"10/10"},				// 现在のスロット使用数/ＭＡＸのスロット数 //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"Exp"},				// exp                  //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"????????"},				// スキル等级          //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"????????"},				// スキル名           //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"Fp"},				// Fp                    //MLHIDE
};

// スキルスイッチ
static SWITCH_DATA SkillSwitch[] = {
{ SWITCH_GRAPHIC,222,  9,  11, 11, TRUE, &MenuWindowSkillGraph[0], MenuSwitchSkillClose },			// クローズボタン

{ SWITCH_TEXT,	  82, 13,   0,  0, TRUE, &MenuWindowSkillText[0], MenuSwitchNone },					// ジョブタイトル
{ SWITCH_TEXT,	  87, 13,   0,  0, TRUE, &MenuWindowSkillText[4], MenuSwitchNone },					// スキル名

{ SWITCH_TEXT,	  66, 32,   0,  0, TRUE, &MenuWindowSkillText[1], MenuSwitchNone },					// 使用スロット

{ SWITCH_GRAPHIC, 16, 28,  50, 15, TRUE, &MenuWindowSkillGraph[6], MenuSwitchSkillBack },			// バックボタン

{ SWITCH_TEXT,	 103, 31,   0,  0, TRUE, &MenuWindowSkillText[2], MenuSwitchNone },					// スキル经验值
{ SWITCH_GRAPHIC,184, 28,  50, 15, TRUE, &MenuWindowSkillGraph[7], MenuSwitchSkillRibirth },		// リバースボタン

{ SWITCH_TEXT,	  95, 31,   0,  0, TRUE, &MenuWindowSkillText[5], MenuSwitchNone },					// Fp
{ SWITCH_GRAPHIC, 16, 28,  49, 17, TRUE, &MenuWindowSkillGraph[8], MenuSwitchSkillBattlePetPass },	// パスボタン

{ SWITCH_TEXT,	 190, 13,   0,  0, TRUE, &MenuWindowSkillText[3], MenuSwitchNone },					// 等级

{ SWITCH_GRAPHIC,223, 57,   0, 14, TRUE, &MenuWindowSkillGraph[3], MenuSwitchNone },				// スクロールバー(つまみ)
{ SWITCH_BUTTON, 223, 57,  11,140, TRUE, &MenuWindowSkillButton[0], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,223, 47,  11, 11, TRUE, &MenuWindowSkillGraph[4], MenuSwitchSkillScrollUp },		// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,223,196,  11, 11, TRUE, &MenuWindowSkillGraph[5], MenuSwitchSkillScrollDown },		// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,195, 28,  18, 18, TRUE, &MenuWindowSkillGraph[9], MenuSwitchSkillScrollLeft },		// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,215, 28,  18, 18, TRUE, &MenuWindowSkillGraph[10], MenuSwitchSkillScrollRight },	// スクロールバー(右ボタン)
{ SWITCH_NONE,	   0,  0, 241,258, TRUE, NULL, MenuSwitchSkillScrollWheel },						// マウスホイール判定

{ SWITCH_NONE,	  14, 46, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル0
{ SWITCH_NONE,	  14, 62, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル1
{ SWITCH_NONE,	  14, 78, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル2
{ SWITCH_NONE,	  14, 94, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル3
{ SWITCH_NONE,	  14,110, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル4
{ SWITCH_NONE,	  14,126, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル5
{ SWITCH_NONE,	  14,142, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル6
{ SWITCH_NONE,	  14,158, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル7
{ SWITCH_NONE,	  14,174, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル8
{ SWITCH_NONE,	  14,190, 207, 16, TRUE, NULL, MenuSwitchSkillSkillPanel },							// スキルパネル9

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowSkillGraph[1], MenuSwitchNone },				// ベース
{ SWITCH_GRAPHIC, 12, 27,   0,  0, TRUE, &MenuWindowSkillGraph[2], MenuSwitchNone },				// 背景

{ SWITCH_NONE,	  243, 5,  18, 79, TRUE, NULL, MenuSwitchDelMouse },								// ドラッグ用

};

enum{
	EnumGraphSkillClose,

	EnumGraphSkillJobTitle,
	EnumGraphSkillName,

	EnumGraphSkillSlotCount,

	EnumGraphSkillBack,

	EnumGraphSkillExp,
	EnumGraphSkillRebirth,

	EnumGraphSkillFp,
	EnumGraphSkillPass,

	EnumGraphSkillLv,

	EnumGraphSkillScroll,
	EnumBtSkillScroll,
	EnumBtSkillScrollUp,
	EnumBtSkillScrollDown,
	EnumBtSkillScrollLeft,
	EnumBtSkillScrollRight,
	EnumBtSkillScrollWheel,

	EnumGraphSkillSkillPanel0,
	EnumGraphSkillSkillPanel1,
	EnumGraphSkillSkillPanel2,
	EnumGraphSkillSkillPanel3,
	EnumGraphSkillSkillPanel4,
	EnumGraphSkillSkillPanel5,
	EnumGraphSkillSkillPanel6,
	EnumGraphSkillSkillPanel7,
	EnumGraphSkillSkillPanel8,
	EnumGraphSkillSkillPanel9,

	EnumGraphSkillBase,
	EnumGraphSkillWindow,

	EnumGraphSkillDragBack,

	EnumSkillEnd,
};


const WINDOW_DATA WindowDataMenuSkill = {
 0,															// メニューウィンドウ
     4,   25, 122,241,258, 0x80010101,  EnumSkillEnd,  SkillSwitch, MenuWindowSkillBf,MenuWindowSkillAf,closeSkillWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSkill={
	2,
	 10,  0,230, 33,
	 243, 5, 18, 79
};

// フィールド用スキルウィンドウ呼び出し关数
ACTION *openFeildSkillWindow();
// 战闘用スキルウィンドウ呼び出し关数
ACTION *openBattleSkillWindow( void (*lReturnFunc)( int lSkill, int lLevel ) );
BOOL BackSkillMode();
// 战闘用ペットスキルウィンドウ呼び出し关数
ACTION *openBattlePetSkillWindow( void (*PetReturnFunc)( int lSkill ) );
#ifdef PUK3_RIDE_BATTLE
	// 战闘用ペットライドキャラスキルウィンドウ呼び出し关数
	ACTION *openBattlePetRideSkillWindow( void (*lPetReturnFunc)( int lSkill ) );
#endif

void RebirthOKCntInit();
void RebirthOffCntInit();
void SkillMoveOffCntInit();

void regetRecipe();



//====================================//
//				クリエート			  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSwitchSkillCreateTry( int no, unsigned int flag );
BOOL MenuSwitchSkillCreateRetry( int no, unsigned int flag );
BOOL MenuSwitchSkillCreateEnd( int no, unsigned int flag );

BOOL MenuSwitchSkillCreateMyItem( int no, unsigned int flag );

BOOL MenuSwitchSkillCreateItemEntry( int no, unsigned int flag );
BOOL MenuSwitchSkillCreateItemEntryDisp( int no, unsigned int flag );

BOOL MenuSwitchSkillCreateFinishItemDisp( int no, unsigned int flag );

BOOL MenuSwitchSkillTargetItemEntry( int no, unsigned int flag );
BOOL MenuSwitchSkillTargetItemEntryDisp( int no, unsigned int flag );

BOOL MenuSwitchSkillItemRenaming( int no, unsigned int flag );

BOOL MenuSwitchSkillCreateClose( int no, unsigned int flag );

BOOL MenuSwitchSkillCreateInfo1( int no, unsigned int flag );
BOOL MenuSwitchSkillCreateInfo2( int no, unsigned int flag );


BOOL MenuWindowSkillCreateBf( int mouse );
BOOL MenuWindowSkillCreateAf( int mouse );
BOOL closeSkillCreate();


GRAPHIC_SWITCH MenuWindowSkillCreateGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_SkillCreateWindow,0,0,0,0,0xFFFFFFFF},			// ベース

	{GID_SkillMaterialList,0,0,0,0,0xFFFFFFFF},			// 文字表示下线

	{GID_SkillCreateItemPanel,0,0,0,0,0xFFFFFFFF},		// 素材登録场所

	{GID_TryButtonOn,0,0,0,0,0xFFFFFFFF},				// トライ
	{GID_RetryButtonOn,0,0,0,0,0xFFFFFFFF},				// リトライ
	{GID_EndButtonOn,0,0,0,0,0xFFFFFFFF},				// エンド

	{GID_SkillFinishItemPanel,0,0,0,0,0xFFFFFFFF},		// 素材登録场所

	{GID_ItemPanel,0,0,0,0,0xFFFFFFFF},					// アイテムパネル

	{GID_TitleSetOn,0,0,0,0,0xFFFFFFFF},				// セットボタン
};

BUTTON_SWITCH MenuWindowSkillCreateButton[]={
	{0,0,0},
};

TEXT_SWITCH MenuWindowSkillCreateText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"？？？？？？？？？？　　000"},	// 文字列        //MLHIDE
};

ACTION_SWITCH_INIT MenuWindowSkillCreateAction[]={
	{SPR_rm00_em},
#ifdef PUK3_MONSTER_HELPER
	{SPR_rm00_em},
#endif
};

// スキルスイッチ
static SWITCH_DATA SkillCreateSwitch[] = {
{ SWITCH_NONE,	   0,  0, 330,110, TRUE, NULL, MenuSwitchSkillCreateInfo1 },									// 警告ウィンドウ１
{ SWITCH_NONE,	   0,  0, 330,110, TRUE, NULL, MenuSwitchSkillCreateInfo2 },									// 警告ウィンドウ２

{ SWITCH_GRAPHIC,543,  8,  11, 11, TRUE, &MenuWindowSkillCreateGraph[0], MenuSwitchSkillCreateClose },			// クローズボタン

#ifdef PUK3_MONSTER_HELPER
{ SWITCH_ACTION, 103,225,   0,  0, TRUE, &MenuWindowSkillCreateAction[1], MenuSwitchNone },						// お手伝いモンス前
#endif
{ SWITCH_ACTION, 103,225,   0,  0, TRUE, &MenuWindowSkillCreateAction[0], MenuSwitchNone },						// 素手キャラ画像
#ifdef PUK3_MONSTER_HELPER
{ SWITCH_ACTION, 103,225,   0,  0, TRUE, &MenuWindowSkillCreateAction[1], MenuSwitchNone },						// お手伝いモンス后
#endif

{ SWITCH_NONE,	  20, 57, 150, 12, TRUE, NULL, MenuSwitchSkillItemRenaming },									// 名称入力栏のあたり判定
{ SWITCH_DIALOG,  20, 53,   0,  0, TRUE, NULL, MenuSwitchNone },												// 名称入力栏

{ SWITCH_GRAPHIC,190, 54,  49, 17, TRUE, &MenuWindowSkillCreateGraph[9], MenuSwitchSkillCreateTry },			// セット

{ SWITCH_TEXT,	  20, 29,   0,  0, TRUE, &MenuWindowSkillCreateText[0], MenuSwitchNone },						// 文字列１
{ SWITCH_TEXT,	  20, 43,   0,  0, TRUE, &MenuWindowSkillCreateText[0], MenuSwitchNone },						// 文字列２
{ SWITCH_TEXT,	  20, 57,   0,  0, TRUE, &MenuWindowSkillCreateText[0], MenuSwitchNone },						// 文字列３
{ SWITCH_TEXT,	  20, 71,   0,  0, TRUE, &MenuWindowSkillCreateText[0], MenuSwitchNone },						// 文字列４
{ SWITCH_TEXT,	  20, 85,   0,  0, TRUE, &MenuWindowSkillCreateText[0], MenuSwitchNone },						// 文字列５

{ SWITCH_GRAPHIC, 20, 29,   0,  0, TRUE, &MenuWindowSkillCreateGraph[2], MenuSwitchNone },						// 文字表示下线

{ SWITCH_NONE,	 190, 75,  48, 48, TRUE, NULL, MenuSwitchSkillCreateItemEntry },								// 素材登録场所１
{ SWITCH_NONE,	 190,125,  48, 48, TRUE, NULL, MenuSwitchSkillCreateItemEntry },								// 素材登録场所２
{ SWITCH_NONE,	 190,175,  48, 48, TRUE, NULL, MenuSwitchSkillCreateItemEntry },								// 素材登録场所３
{ SWITCH_NONE,	 243, 75,  48, 48, TRUE, NULL, MenuSwitchSkillCreateItemEntry },								// 素材登録场所４
{ SWITCH_NONE,	 243,125,  48, 48, TRUE, NULL, MenuSwitchSkillCreateItemEntry },								// 素材登録场所５
{ SWITCH_NONE,	 243,175,  48, 48, TRUE, NULL, MenuSwitchSkillCreateItemEntry },								// 素材登録场所６

{ SWITCH_GRAPHIC,231,175,  48, 48, TRUE, &MenuWindowSkillCreateGraph[8], MenuSwitchSkillTargetItemEntry },		// 素材登録场所７

{ SWITCH_GRAPHIC,190, 27,   0,  0, TRUE, &MenuWindowSkillCreateGraph[7], MenuSwitchSkillCreateFinishItemDisp },	// 完成品の絵１

{ SWITCH_GRAPHIC,231, 45,   0,  0, TRUE, &MenuWindowSkillCreateGraph[7], MenuSwitchSkillCreateFinishItemDisp },	// 完成品の絵２

{ SWITCH_GRAPHIC,188, 73,   0,  0, TRUE, &MenuWindowSkillCreateGraph[3], MenuSwitchNone },						// 素材登録场所の絵

{ SWITCH_GRAPHIC, 21,209,  49, 17, TRUE, &MenuWindowSkillCreateGraph[4], MenuSwitchSkillCreateTry },			// トライ
{ SWITCH_GRAPHIC, 21,209,  49, 17, TRUE, &MenuWindowSkillCreateGraph[5], MenuSwitchSkillCreateRetry },			// リトライ
{ SWITCH_GRAPHIC,126,209,  49, 17, TRUE, &MenuWindowSkillCreateGraph[6], MenuSwitchSkillCreateEnd },			// エンド


{ SWITCH_NONE,	 307, 28, 248,204, TRUE, NULL, MenuSwitchSkillCreateMyItem },									// 手持ちアイテム

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowSkillCreateGraph[1], MenuSwitchNone },						// ベース

{ SWITCH_NONE,	  566, 5,  18, 79, TRUE, NULL, MenuSwitchDelMouse },											// ドラッグ用

};

enum{
	EnumSkillCreateInfo1,
	EnumSkillCreateInfo2,

	EnumSkillCreateClose,

#ifdef PUK3_MONSTER_HELPER
	EnumSkillCreateMonsHelper_F,
#endif
	EnumSkillCreatePc,
#ifdef PUK3_MONSTER_HELPER
	EnumSkillCreateMonsHelper_B,
#endif

	EnumSkillCreateNameInputHit,
	EnumSkillCreateNameInput,

	EnumSkillCreateNameSet,

	EnumSkillCreateText1,
	EnumSkillCreateText2,
	EnumSkillCreateText3,
	EnumSkillCreateText4,
	EnumSkillCreateText5,

	EnumSkillCreateUnderBar,

	EnumSkillCreateItemEntry1,
	EnumSkillCreateItemEntry2,
	EnumSkillCreateItemEntry3,
	EnumSkillCreateItemEntry4,
	EnumSkillCreateItemEntry5,
	EnumSkillCreateItemEntry6,

	EnumSkillCreateItemEntry7,

	EnumSkillCreateItemFinishGra,

	EnumSkillCreateItemFinishGra2,

	EnumSkillCreateItemEntryGra,

	EnumSkillCreateTryBtn,
	EnumSkillCreateRetryBtn,
	EnumSkillCreateEndBtn,

	EnumSkillCreateMyItem,

	EnumSkillCreateWindow,

	EnumSkillCreateDragBack,

	EnumSkillCreateEnd,
};


const WINDOW_DATA WindowDataMenuSkillCreate = {
 0,															// メニューウィンドウ
     4,   37, 102,563,239, 0x80010101,  EnumSkillCreateEnd,  SkillCreateSwitch, MenuWindowSkillCreateBf,MenuWindowSkillCreateAf,closeSkillCreate
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSkillCreate={
	2,
	 10,  0,553, 27,
	 566, 5, 18, 79
};

ACTION *openSkillCreateWindow( int SkillNo, int RecipeNo, int SelTech );
void GiveResultCreate();
ACTION *openSkillRepAppWindow( int SkillNo, int SelTech );
void GiveResultRepApp();
ACTION *openSkillDecoWindow( int SkillNo, int SelTech );
#ifdef PUK3_MONSTER_HELPER_CANCEL
	void CancelRideSkillCreate();
#endif
#ifdef PUK3_MONSTER_HELPER_MMLOCK
	BOOL CheckRideSkillCreate();
#endif



//====================================//
//				采取				  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSwitchSkillGatherClose( int no, unsigned int flag );

BOOL MenuSwitchSkillGatherTry( int no, unsigned int flag );
BOOL MenuSwitchSkillGatherEnd( int no, unsigned int flag );

BOOL MenuSwitchSkillGatherItemDisp( int no, unsigned int flag );


BOOL MenuWindowSkillGatherBf( int mouse );
BOOL MenuWindowSkillGatherAf( int mouse );
BOOL closeSkillGather();


GRAPHIC_SWITCH MenuWindowSkillGatherGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_GatherWindow,0,0,0,0,0xFFFFFFFF},				// ベース

	{GID_GatheringPanel,0,0,0,0,0xFFFFFFFF},			// 文字表示

	{GID_ItemPanel,0,0,0,0,0xFFFFFFFF},					// アイテムボックス

	{GID_TryButtonOn,0,0,0,0,0xFFFFFFFF},				// トライ
	{GID_EndButtonOn,0,0,0,0,0xFFFFFFFF},				// エンド

	{GID_UnderBar4Line,0,0,0,0,0xFFFFFFFF},				// 文字表示下线
};

TEXT_SWITCH MenuWindowSkillGatherText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"？？？？？？？？？？"},	// 文字列             //MLHIDE
};

ACTION_SWITCH_INIT MenuWindowSkillGatherAction[]={
	{SPR_rm00_em},
#ifdef PUK3_MONSTER_HELPER
	{SPR_rm00_em},
#endif
};

// スキルスイッチ
static SWITCH_DATA SkillGatherSwitch[] = {

{ SWITCH_GRAPHIC,178,  9,  11, 11, TRUE, &MenuWindowSkillGatherGraph[0], MenuSwitchSkillGatherClose },			// クローズボタン

#ifdef PUK3_MONSTER_HELPER
{ SWITCH_ACTION, 107,204,   0,  0, FALSE, &MenuWindowSkillGatherAction[1], MenuSwitchNone },					// モンスお手伝い前
#endif
{ SWITCH_ACTION, 107,204,   0,  0, FALSE, &MenuWindowSkillGatherAction[0], MenuSwitchNone },					// キャラ画像
#ifdef PUK3_MONSTER_HELPER
{ SWITCH_ACTION, 107,204,   0,  0, FALSE, &MenuWindowSkillGatherAction[1], MenuSwitchNone },					// モンスお手伝い后
#endif

{ SWITCH_TEXT,	  16, 27,   0,  0, TRUE, &MenuWindowSkillGatherText[0], MenuSwitchNone },						// 文字列１
{ SWITCH_TEXT,	  16, 41,   0,  0, TRUE, &MenuWindowSkillGatherText[0], MenuSwitchNone },						// 文字列２
{ SWITCH_TEXT,	  16, 55,   0,  0, TRUE, &MenuWindowSkillGatherText[0], MenuSwitchNone },						// 文字列３
{ SWITCH_TEXT,	  16, 69,   0,  0, TRUE, &MenuWindowSkillGatherText[0], MenuSwitchNone },						// 文字列４

{ SWITCH_GRAPHIC, 16, 43,   0,  0, TRUE, &MenuWindowSkillGatherGraph[2], MenuSwitchNone },						// 文字表示
{ SWITCH_GRAPHIC, 15, 40,   0,  0, TRUE, &MenuWindowSkillGatherGraph[6], MenuSwitchNone },						// 文字表示下线

{ SWITCH_NONE,	 143, 29,   0,  0, TRUE, NULL, MenuSwitchSkillGatherItemDisp },									// アイテムボックス

{ SWITCH_GRAPHIC, 29,203,  49, 17, TRUE, &MenuWindowSkillGatherGraph[4], MenuSwitchSkillGatherTry },			// トライ
{ SWITCH_GRAPHIC,129,203,  49, 17, TRUE, &MenuWindowSkillGatherGraph[5], MenuSwitchSkillGatherEnd },			// エンド

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowSkillGatherGraph[1], MenuSwitchNone },						// ベース

{ SWITCH_NONE,	  201, 5,  18, 79, TRUE, NULL, MenuSwitchDelMouse },											// ドラッグ用

};

enum{
	EnumSkillGatherClose,

#ifdef PUK3_MONSTER_HELPER
	EnumSkillGatherMonsHelper_F,
#endif
	EnumSkillGatherPc,
#ifdef PUK3_MONSTER_HELPER
	EnumSkillGatherMonsHelper_B,
#endif

	EnumSkillGatherText1,
	EnumSkillGatherText2,
	EnumSkillGatherText3,
	EnumSkillGatherText4,

	EnumSkillGatherGraText,
	EnumSkillGatherUnderBar,

	EnumSkillGatherItemBox,

	EnumSkillGatherTryBtn,
	EnumSkillGatherEndBtn,

	EnumSkillGatherWindow,

	EnumSkillGatherDragBack,

	EnumSkillGatherEnd,
};


const WINDOW_DATA WindowDataMenuSkillGather = {
 0,															// メニューウィンドウ
     4,  383,118,198,232, 0x80010101,  EnumSkillGatherEnd,  SkillGatherSwitch, MenuWindowSkillGatherBf,MenuWindowSkillGatherAf,closeSkillGather
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSkillGather={
	2,
	 10,  0,288, 27,
	 201, 5, 18, 79
};

ACTION *openSkillGatherWindow( int SkillNo, int SelTech );
void GiveResultGather();


//====================================//
//				治疗				  //
//====================================//

// 对象选择１ウィンドウ呼び出し
ACTION *openCategoryC1Window();
// 对象选择２ウィンドウ呼び出し
ACTION *openCategoryC2Window();

// ボタン处理关数 *********************//

BOOL MenuSwitchSkillOthersClose( int no, unsigned int flag );

BOOL MenuSwitchSkillOthersRetry( int no, unsigned int flag );
BOOL MenuSwitchSkillOthersEnd( int no, unsigned int flag );

BOOL MenuSwitchSkillOthersItemDisp( int no, unsigned int flag );


BOOL MenuWindowSkillOthersBf( int mouse );
BOOL MenuWindowSkillOthersAf( int mouse );
BOOL closeSkillOthers();


GRAPHIC_SWITCH MenuWindowSkillOthersGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_OthersWindow,0,0,0,0,0xFFFFFFFF},				// ベース

	{GID_SkillCreateResultPanel2,0,0,0,0,0xFFFFFFFF},	// 文字表示下线

	{GID_RetryButtonOn,0,0,0,0,0xFFFFFFFF},				// トライ
	{GID_EndButtonOn,0,0,0,0,0xFFFFFFFF},				// エンド
};

TEXT_SWITCH MenuWindowSkillOthersText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"？？？？？？？？？？"},	// 文字列             //MLHIDE
};

ACTION_SWITCH_INIT MenuWindowSkillOthersAction[]={
	{SPR_rm00_em},
#ifdef PUK3_MONSTER_HELPER
	{SPR_rm00_em},
#endif
};

// スキルスイッチ
static SWITCH_DATA SkillOthersSwitch[] = {

{ SWITCH_GRAPHIC,178,  9,  11, 11, TRUE, &MenuWindowSkillOthersGraph[0], MenuSwitchSkillOthersClose },			// クローズボタン

#ifdef PUK3_MONSTER_HELPER
{ SWITCH_ACTION, 107,224,   0,  0, TRUE, &MenuWindowSkillOthersAction[1], MenuSwitchNone },						// モンスお手伝い
#endif
{ SWITCH_ACTION, 107,224,   0,  0, TRUE, &MenuWindowSkillOthersAction[0], MenuSwitchNone },						// キャラ画像
#ifdef PUK3_MONSTER_HELPER
{ SWITCH_ACTION, 107,224,   0,  0, TRUE, &MenuWindowSkillOthersAction[1], MenuSwitchNone },						// モンスお手伝い
#endif

{ SWITCH_TEXT,	  25, 28,   0,  0, TRUE, &MenuWindowSkillOthersText[0], MenuSwitchNone },						// 文字列１
{ SWITCH_TEXT,	  25, 42,   0,  0, TRUE, &MenuWindowSkillOthersText[0], MenuSwitchNone },						// 文字列２
{ SWITCH_TEXT,	  25, 56,   0,  0, TRUE, &MenuWindowSkillOthersText[0], MenuSwitchNone },						// 文字列３
{ SWITCH_TEXT,	  25, 70,   0,  0, TRUE, &MenuWindowSkillOthersText[0], MenuSwitchNone },						// 文字列４

{ SWITCH_GRAPHIC, 24, 28,   0,  0, TRUE, &MenuWindowSkillOthersGraph[2], MenuSwitchNone },						// 文字表示下线
{ SWITCH_GRAPHIC, 24, 55,   0,  0, TRUE, &MenuWindowSkillOthersGraph[2], MenuSwitchNone },						// 文字表示下线

{ SWITCH_GRAPHIC, 29,203,  49, 17, TRUE, &MenuWindowSkillOthersGraph[3], MenuSwitchSkillOthersRetry },			// リトライ
{ SWITCH_GRAPHIC,129,203,  49, 17, TRUE, &MenuWindowSkillOthersGraph[4], MenuSwitchSkillOthersEnd },			// エンド

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowSkillOthersGraph[1], MenuSwitchNone },						// ベース

{ SWITCH_NONE,	  201, 5,  18, 79, TRUE, NULL, MenuSwitchDelMouse },											// ドラッグ用

};

enum{
	EnumSkillOthersClose,

#ifdef PUK3_MONSTER_HELPER
	EnumSkillOthersMonsHelper_F,
#endif
	EnumSkillOthersPc,
#ifdef PUK3_MONSTER_HELPER
	EnumSkillOthersMonsHelper_B,
#endif

	EnumSkillOthersText1,
	EnumSkillOthersText2,
	EnumSkillOthersText3,
	EnumSkillOthersText4,

	EnumSkillOthersUnderBar1,
	EnumSkillOthersUnderBar2,

	EnumSkillOthersRetryBtn,
	EnumSkillOthersEndBtn,

	EnumSkillOthersWindow,

	EnumSkillOthersDragBack,

	EnumSkillOthersEnd,
};


const WINDOW_DATA WindowDataMenuSkillOthers = {
 0,															// メニューウィンドウ
     4,  383,118,198,232, 0x80010101,  EnumSkillOthersEnd,  SkillOthersSwitch, MenuWindowSkillOthersBf,MenuWindowSkillOthersAf,closeSkillOthers
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSkillOthers={
	2,
	 10,  0,288, 27,
	 201, 5, 18, 79
};

ACTION *openSkillOthersWindow();




void SkillWindowInit();

#endif