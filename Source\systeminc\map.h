﻿#ifndef _MAP_H_ 
#define _MAP_H_


#ifdef PUK2
	#include "../PUK2/newDraw/BLT_MEMBER.H"
#endif

//-------------------------------------------------------------------------//
// 构造体宣言                                                              //
//-------------------------------------------------------------------------//
#ifdef MAP_DATA_EMPTY_PROC

// マップ要求处理
typedef struct _MAP_DATA_EMPTY_INFO
{
	char  sendFlag;		// 要求したかのフラグ
	char  recvFlag;		// 要求に对し返答があったかフラグ
	char  areaCnt;		// 要求するエリアが何个か
	int   mapMode;		// マップモード 0 ... 固定マップ/ 1 ... 自动生成マップ / 2 ... 家
	int   mapNo;		// マップ番号
	short areaX1[2];	// 要求するマップの左上Ｘ座标（２つあるのは斜めに移动する时のため）
	short areaY1[2];	// 　　　　〃　　　左上Ｙ座标
	short areaX2[2];	// 　　　　〃　　　右下Ｘ座标
	short areaY2[2];	// 　　　　〃　　　右下Ｙ座标
	_MAP_DATA_EMPTY_INFO *pre;	// 前の构造体のポインタ
	_MAP_DATA_EMPTY_INFO *next;	// 后ろの构造体のポインタ
} MAP_DATA_EMPTY_INFO;

#endif

#ifdef PUK2
typedef struct struct_MAP_CHAR_PARTS_SORT_INFO
#else
typedef struct
#endif
{
	int graNo;			// 画像番号
	int no;				// 登録番号
	int insert;			// キャラ插入位置
	float mx, my;		// マップ座标
	short screenX;		// 画面表示座标
	short screenY;		//      〃
	char type;			// 种类  0bit ... PC

#ifdef PUK2
	struct BLT_MEMBER bm;				// 绘图设定
#endif
#ifdef PUK2_DRESS_UP
	struct struct_MAP_CHAR_PARTS_SORT_INFO *front;	// 自分より手前の着饰りへのポインタ
	struct struct_MAP_CHAR_PARTS_SORT_INFO *rear;	// 自分より奥の着饰りへのポインタ
#endif
} MAP_CHAR_PARTS_SORT_INFO;


//-------------------------------------------------------------------------//
// 定数宣言                                                                //
//-------------------------------------------------------------------------//

// 画面中心を基准にしたマップの表示范围（グリッド管理）
#define MAP_AREA_X1			-30
#define MAP_AREA_Y1			-20
#define MAP_AREA_X2			+21		// 端は含まない
#define MAP_AREA_Y2			+31		// 端は含まない

// マップ表示サイズ
#define MAP_AREA_X_SIZE	(MAP_AREA_X2 - MAP_AREA_X1)
#define MAP_AREA_Y_SIZE	(MAP_AREA_Y2 - MAP_AREA_Y1)

// グリッドのサイズ
#define GRID_SIZE		64

// 移动速度
#define MOVE_SPEED			4
#define MOVE_FRAME			(GRID_SIZE/MOVE_SPEED)

// イベント番号
enum
{
	EVENT_NONE,			// イベントは発生しない
	EVENT_NPC,			// 固定NPC
	EVENT_ENEMY,		// 固定敌エンカウント
	
	EVENT_WARP,			// ワープ
	EVENT_DOOR,			// ドア
	EVENT_ALTERRATIVE,	// 何かあるかも知れないのでマップの要求を必ずする
	EVENT_WARP_MONING,	// 朝限定ワープ
	EVENT_WARP_NOON,	// 昼限定ワープ
	EVENT_WARP_NIGHT,	// 夜限定ワープ
	EVENT_WARP_HOUSE,	// 家へのワープ
	EVENT_WARP_OBJECT,  // 普通のワープオブジェクト
	EVENT_ROOM_IN, 	 	// マンションの部屋へワープ
	EVENT_ROOM_OUT,	 	// マンションの部屋からワープ（ダミー）
#ifdef PUK2
	EVENT_GUILDROOM_IN,		// 家族ルームへワープ
	EVENT_GUILDROOM_OUT,	// 家族ルームからワープ（ダミー）
#endif
	
	EVENT_END		// イベント番号の最后
};

// マップ用フラグ
#define MAP_READ_FLAG	0x8000		// データがあるかのフラグ（βバージョン）
#define MAP_SEE_FLAG	0x4000		// キャラがその场所に行ったかのフラグ

// マップ名の长さ
#define MAP_NAME_LEN	24

// マップエフェクトの种类
enum
{
	MAP_EFFECT_TYPE_NONE,
	MAP_EFFECT_TYPE_RAIN,
	MAP_EFFECT_TYPE_SNOW,
	MAP_EFFECT_TYPE_STAR,
	MAP_EFFECT_TYPE_KAMIFUBUKI,
	MAP_EFFECT_TYPE_HOTARU
#ifdef PUK3_WHALE_SHIP
	,
	MAP_EFFECT_TYPE_CLOUD,
	MAP_EFFECT_TYPE_CLOUD2,
	MAP_EFFECT_TYPE_MONSTER,
#endif
};


#define MAP_ZOOM_MAX	3


#define MAP_BGM_NO_START	200		// ＢＧＭ番号の开始位置
#define MAP_BGM_NO_END		249		// ＢＧＭ番号の終了位置
#define MAP_ENV_NO_START	250		// 环境音番号の开始位置
#define MAP_ENV_NO_END		299		// 环境音番号の終了位置

#define MAP_FILE_HEADER		"MAP"	// マップファイルのヘッダ
#define MAP_FILE_VERSION	1		// マップファイルのバージョン

enum
{
	CHAR_MAPID_NORMAL=0,			// 通常マップ
	CHAR_MAPID_DUNGEON,				// 自动生成ダンジョン
	CHAR_MAPID_GUILDHOUSE,			// 家族家
	CHAR_MAPIDNUM,
};


//-------------------------------------------------------------------------//
// 外部变数宣言                                                            //
//-------------------------------------------------------------------------//

extern char mapName[];
extern int mapServerNo;
extern int mapKind;
extern int mapNo;
#ifdef PUK3_CONNDOWNWALK
	extern int mapWarpId;
#endif
extern int mapCfgId;
extern int mapSeqNo;
extern int mapGxSize, mapGySize;
extern short mapAreaX1, mapAreaY1;
extern short mapAreaX2, mapAreaY2;
extern short mapAreaWidth, mapAreaHeight;
extern short mapInsideFlag;


extern int mapGx, mapGy;
extern int nextMapGx, nextMapGy;
extern int oldMapGx, oldMapGy;
extern int oldNextMapGx, oldNextMapGy;
extern float mapX, mapY;
extern float mapVx, mapVy;
extern float mapSpdRate;

extern short moveAddTbl[8][2];
extern short moveRouteCnt;


extern char mapFolder[];


extern char highSpeedDrawSw;
extern int highSpeedDrawVarX, highSpeedDrawVarY;
extern int highSpeedDrawNowX, highSpeedDrawNowY;
extern int highSpeedDrawOldX, highSpeedDrawOldY;
extern int highSpeedDrawVarX2, highSpeedDrawVarY2;
extern int highSpeedDrawNowX2, highSpeedDrawNowY2;
extern int highSpeedDrawOldX2, highSpeedDrawOldY2;








extern BOOL mapEmptyFlag;

extern float viewPointX;
extern float viewPointY;



extern short nowEncountPercentage;
extern short nowEncountExtra;
extern short minEncountPercentage;
extern short maxEncountPercentage;
extern short sendEnFlag;
extern short encountNowFlag;

extern short eventWarpSendFlag;
extern short eventWarpSendId;
extern short eventEnemySendFlag;
extern short eventEnemySendId;
extern short eventEnemyFlag;

extern short vsLookFlag;

extern BOOL floorChangeFlag;

extern BOOL warpEffectFlag;
extern BOOL warpEffectStart;
extern BOOL warpMcFlag;		// マップ要求中フラグ
extern BOOL warpMnFlag;		// マップ名受信济みフラグ



extern BOOL autoMappingInitFlag;
extern short autoMapZoomFlag;



//-------------------------------------------------------------------------//
// 关数プロト种类宣言                                                    //
//-------------------------------------------------------------------------//

void initEvent( void );
void initMap( void );
void resetMap( void );

void updateMapAreaFromMapPos( void );
void checkAreaLimit( short *, short *, short *, short * );
void setMapAreaInfo( int, int, int, int, int, int, int, int);
void setMoveMap( int, int );
void setWarpMap( int, int );

void makeMapFileName( char *, int, int, int );
int createClearMapFile( char *, int, int, int, int );
int createMap( int, int, int, int, int, int, int );
int writeMap( int, int, int, int, int, int,
	unsigned short *, unsigned short *, unsigned short * );
int readMap( int, int, int, int, int, int,
	unsigned short *, unsigned short *, unsigned short * );

void initHighSpeedDraw( void );
void resetHighSpeedDraw( void );
void updateHighSpeedDraw( void );
void memoryHighSpeedDraw( void );




BOOL mapCheckSum( int, int, int, int, int, int, int, int, int );
void drawMap( void );
void drawMap2( void );
void drawMap3( void );
void drawTile( void );
void redrawMap( void );
void mapGridCursolProc( void );
void moveProc( void );
BOOL checkEmptyMap( int );
#ifdef PUK3_RIDE
void setMapMovePoint( int _nextGx, int _nextGy, int walkSpeed = 100 );
#else
void setMapMovePoint( int, int );
#endif
void setMapMovePoint2( int, int );
void mapMove2( void );

void forceWarpMap( int, int );
char cnvServDir( int, int );

void goFrontPartyCharacter( int, int, int );


void initCharPartsPrio( void );

#ifdef PUK2
#ifdef PUK2_DRESS_UP
int setMapChar( int graNo, float mx, float my, int screenX, int screenY, int pcFlag, struct BLT_MEMBER *bm=NULL );
#else
void setMapChar( int graNo, float mx, float my, int screenX, int screenY, int pcFlag, struct BLT_MEMBER *bm=NULL );
#endif
void setMapParts( int graNo, float mx, float my, int screenX, int screenY, struct BLT_MEMBER *bm=NULL );
#else
void setMapChar( int, float, float, int, int, int );
void setMapParts( int, float, float, int, int );
#endif
#ifdef PUK2_DRESS_UP
void setMapCharWith( int charNum, BOOL front, int graNo, int screenX, int screenY, struct BLT_MEMBER *bm );
#endif

void drawMapCharParts( void );
void sortMapCharParts( void );
void sortMapParts( void );
int cmpSortMapParts( const void *, const void * );
int cmpSortMapParts2( const void *, const void * );
int checkMapCharParts( MAP_CHAR_PARTS_SORT_INFO *, MAP_CHAR_PARTS_SORT_INFO * );
void sortMapChar( void );
int cmpSortMapChar( const void *, const void * );
void margeMapCharParts( void );



#if 0
BOOL checkNpcEvent( int, int, int, int );
#endif

void drawAutoMap( int x, int y );
void readAutoMapSeeFlag( void );
void writeAutoMapSeeFlag( void );

BOOL checkHitMap( int, int );

int getDirFromXY( float, float );

void camMapToGamen( float, float, float *, float * );
void camGamenToMap( float, float, float *, float * );





#ifdef PUK3_WHALE_SHIP
	void setMapEffect( int effect, int level, char *option );
#endif
void initMapEffect( void );
void drawMapEffect( void );
void mapEffectProc( void );
void mapEffectRain( void );
void mapEffectSnow( void );
void mapEffectStar( void );
void mapEffectFallingStar( void );
void mapEffectKamiFubuki( void );
#ifdef PUK3_WHALE_SHIP
	void mapEffectCloud();
	void delMapEffectCloud();
#endif

void mapEffectProc2( int );
void mapEffectRain2( int );
void mapEffectSnow2( int );


// マップ座标ハック对策チェック（变更前）
void CheckMapHackStart( void );
// マップ座标ハック对策チェック（变更后）
void CheckMapHackEnd( void );


#ifdef MAP_DATA_EMPTY_PROC

void initMapDataEmpty( void );
int setMapDataEmptyArea( MAP_AREA_INFO *, MAP_AREA_INFO * );
int delMapDataEmptyArea( MAP_AREA_INFO * );
void mapDataEmptyProc( void );
int getBufCntMapDataEmpty( void );

void addMapDataEmptyBuf( MAP_DATA_EMPTY_INFO *, MAP_DATA_EMPTY_INFO * );
void insertMapDataEmptyBuf( MAP_DATA_EMPTY_INFO *, MAP_DATA_EMPTY_INFO * );
void delMapDataEmptyBuf( MAP_DATA_EMPTY_INFO * );

#endif



#endif
