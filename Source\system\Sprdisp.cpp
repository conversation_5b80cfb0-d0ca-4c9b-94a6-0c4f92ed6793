﻿/************************/
/*	sprdisp.c			*/
/************************/
#include"../systeminc/system.h"
#include"../systeminc/loadrealbin.h"
#include"../systeminc/loadsprbin.h"
#include"../systeminc/anim_tbl.h"
#include"../systeminc/map.h"
#include"../systeminc/directDraw.h"
#include"../systeminc/action.h"
#include"../systeminc/sprdisp.h"
#include"../systeminc/sprmgr.h"
#include"../systeminc/chat.h"
#include"../systeminc/font.h"
#include"../systeminc/main.h"
#include"../systeminc/process.h"
#include"../systeminc/keyboard.h"
#include"../systeminc/menu.h"

// 种类フラグ
#define STOCK_DISP_BUFFER_NO_BMP	( 1 << 31 ) 	// ＢＭＰでない时
#define STOCK_DISP_BUFFER_LINE		( 1 << 30 ) 	// ライン
#define STOCK_DISP_BUFFER_BOX		( 1 << 29 ) 	// ボックス
#define STOCK_DISP_BUFFER_BOX_FILL	( 1 << 28 )		// ボックス涂りつぶし
#define STOCK_DISP_BUFFER_CIRCLE	( 1 << 27 ) 	// 圆
#ifdef PUK2
#define STOCK_DISP_BUFFER_ALPHA		( 1 << 26 ) 	// アルファブレンド
#endif

// 表示バッファ构造体
DISP_BUFFER DispBuffer;

// ＢＭＰのイメージデータまでのOFFセット
int BmpOffBits;
// ＢＭＰ読み込み用ワーク领域へのポインタ
LPBITMAPINFO lpBmpInfo;
// Realbin 読み込み用ワーク领域へのポインタ
char *pRealBinBits;
// 今回 Realbin から読み込むＢＭＰのサイズ
int RealBinWidth, RealBinHeight;
#ifdef PUK2
// ＢＭＰのパレットのサイズ
int RealBinPalSize;
#endif
// BitBltの时
HBITMAP	hBmp;

// サーフェスヴィジーフラグ
int SurfaceBusyFlag = 0;

// ウィンドウポインタ
extern ACTION* pActMenuWnd2;
// トグルフラグ
extern unsigned int MenuToggleFlag;

// ソート比较关数
int SortComp( DISP_SORT *pDisp1, DISP_SORT *pDisp2 );
typedef int CMPFUNC( const void *, const void * );
// 表示バッファーからボックスデータを取り出す
void GetBoxDispBuffer( DISP_INFO *pDispInfo, int bmpNo );

#ifdef PUK2
static char draw_sea_flg;
#endif

// 表示バッファソート ///////////////////////////////////////////////////////////
void SortDispBuffer( void )
{	
#ifdef PUK2
	DISP_SORT *pDispSort;
	DISP_INFO *pDispInfo;
	int i;

	i=3;
	if (draw_sea_flg) i++;

	// カウントオーバーするなら
	if( DispBuffer.DispCnt+i >= DISP_BUFFER_SIZE ) DispBuffer.DispCnt=DISP_BUFFER_SIZE-i;

	// 海用特殊处理タイミング
	if (draw_sea_flg){
		pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
		pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;

		// 表示优先ソート用构造体
		pDispSort->dispPrio = DISP_PRIO_SEA_END;
		pDispSort->no = DispBuffer.DispCnt;
		// 表示情报构造体（ ソートしない内容 ）
		pDispInfo->bmpNo = -1;
		pDispInfo->hitFlag = 0;
		pDispInfo->pAct = NULL;
		pDispInfo->type = DITYPE_SEA;
		// 表示カウンタープラス
		DispBuffer.DispCnt++;
	}

	// タイルの描画タイミング
	pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;

	// 表示优先ソート用构造体
	pDispSort->dispPrio = DISP_PRIO_TILE;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->bmpNo = -1;
	pDispInfo->hitFlag = 0;
	pDispInfo->pAct = NULL;
	pDispInfo->type = DITYPE_PUT_TILE;
	// 表示カウンタープラス
	DispBuffer.DispCnt++;

	// タイルの取得タイミング
	pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;

	// 表示优先ソート用构造体
	pDispSort->dispPrio = DISP_PRIO_TILE_END;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->bmpNo = -1;
	pDispInfo->hitFlag = 0;
	pDispInfo->pAct = NULL;
	pDispInfo->type = DITYPE_GET_TILE;
	// 表示カウンタープラス
	DispBuffer.DispCnt++;

	// マップエフェクトタイミング
	pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;

	// 表示优先ソート用构造体
	pDispSort->dispPrio = DISP_PRIO_RESERVE;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->bmpNo = -1;
	pDispInfo->hitFlag = 0;
	pDispInfo->pAct = NULL;
	pDispInfo->type = DITYPE_MAPEFFECT;
	// 表示カウンタープラス
	DispBuffer.DispCnt++;
#endif
	//クイックソート
	qsort( 	DispBuffer.DispSort,	// 构造体のアドレス
			DispBuffer.DispCnt,		// 比较する个数
			sizeof( DISP_SORT ), 	// 构造体のサイズ
			( CMPFUNC * )SortComp 	// 比较关数へのポインタ
		);				
}

#ifndef PUK2
static char draw_sea_flg;
#endif

#define	RASTER_CLEARANCE	8
#define	RASTER_TBL_SIZE		64
int raster_tbl[ RASTER_TBL_SIZE ]={
	0,1,2,3,4,5,6,7,8,9,10,10,11,11,11,12,
	12,12,11,11,11,10,9,9,8,7,6,5,4,3,2,1,
	0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-10,-11,-11,-11,-12,
	-12,-12,-11,-11,-11,-10,-9,-9,-8,-7,-6,-5,-4,-3,-2,-1,
};
int raster_point = 0;
static float raster_point_f = 0.0f;
static float raster_old_mapx = 0;
static float raster_old_mapy = 0;
int raster_bend = 0;
static int raster_map_no = 0;
//海マップかどうかのチェック
extern BOOL check_sea_map( void );

// 今回のマップ座标保存（海ラスタースクロールで使用）/////////////////////////////////////////////////////////////////
void set_raster_map( void )
{
	raster_old_mapx = mapX;
	raster_old_mapy = mapY;
}

// ラスターポインター更新/////////////////////////////////////////////////////////////////
void update_raster_pointer( void )
{
	//时间でポインター移动する
	raster_point_f += 0.25f;
}

// ラスターポインター修正 （ ０～６３ にする ）/////////////////////////////////////////////////////////////////
void AdjustRaster( float *p )
{
	if( *p >= 64.0 ){
		*p -= 64.0F;
		AdjustRaster( p );
	}else 
	if ( *p < 0.0 ){
		*p += 64.0F;
		AdjustRaster( p );
	}
}

// 今回のプレイヤースクロール取り出し/////////////////////////////////////////////////////////////////
static void get_scroll_pos( float *x, float *y )
{
	float dx,dy,dx2,dy2;

	camMapToGamen( raster_old_mapx, raster_old_mapy, &dx, &dy );
	camMapToGamen( mapX, mapY, &dx2, &dy2 );
	dx = dx - dx2;
	dy = dy - dy2;
	*x = dx / (float)RASTER_CLEARANCE;
	*y = dy / (float)RASTER_CLEARANCE;
}

// 特别描画 /////////////////////////////////////////////////////////////////
static void draw_special( void )
{
	RECT rect = { 0, 0, DEF_APPSIZEX, DEF_APPSIZEY };
	int line;
	int d7;
	float px, py;
	int bend;

	//战闘中か海マップでなければ
	if( encountNowFlag || check_sea_map() == FALSE ){
		//ラスタースクロールを滑らかに开始する
		raster_bend = 0;
		return;
	}
	//マップ番号变わったなら
	if( raster_map_no != mapNo ){
		raster_map_no = mapNo;
		//ラスタースクロールを滑らかに开始する
		raster_bend = 0;
	}

	// バックサーフェスからバトルサーフェスへコピー
	lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );

	//プレイヤーが移动した分ポインターずらす
	get_scroll_pos( &px, &py );
	raster_point_f -= py;
	// ラスターポインター修正 （ ０～６３ にする ）
	AdjustRaster( &raster_point_f );
	//グローバルにコピー
	raster_point = (int)raster_point_f;

	//０ライン目から
	line = 0;
	//左端
	rect.left = 0;
	//右端
	rect.right = 640;
	for( d7 = 0; d7 < 480; d7 += RASTER_CLEARANCE ){
		//上端
//		rect.top = d7 + raster_tbl[ (line + raster_point) & (RASTER_TBL_SIZE - 1) ] + 12;
		bend = raster_tbl[ (line + raster_point) & (RASTER_TBL_SIZE - 1) ] * raster_bend / 100;
		rect.top = d7 + bend + 12;
		//下端
		rect.bottom = rect.top + RASTER_CLEARANCE;
		// バックサーフェスへ高速転送
		lpDraw->lpBACKBUFFER->BltFast( 0, d7, lpBattleSurface, &rect, DDBLTFAST_WAIT );
		//次のラインへ
		line++;
	}

	//ラスタースクロールの幅变更
	if( raster_bend < 100 ){
		raster_bend += 2;
	}
}

#ifdef PUK2

	#include "../PUK2/newDraw/newSprdisp.cpp"

#endif

#ifndef PUK2
// ＢＭＰの描画 /////////////////////////////////////////////////////////////////
void PutBmp( void )
{
	SURFACE_INFO *lpSurfaceInfo;
	DISP_SORT 	*pDispSort = DispBuffer.DispSort;
	DISP_INFO 	*pDispInfo;
	int i;
	int bmpNo;
	UCHAR putFontFlag = 0; // フォントを描画したかフラグ
	unsigned char drawMapEffectFlag = 0;
	unsigned char drawFastTileFlag = 0;
	unsigned char drawFastTileFlag2 = 0;
	RECT src;
	int sx, sy;
#ifdef PUK2
	unsigned long bltf;

	palchgcnt=0;
#endif
	//海描画フラグクリア
	draw_sea_flg = 0;

#ifdef PUK2
	if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
#endif
	// 溜められた数だけループ
	for( i = 0; i < DispBuffer.DispCnt ; i++, pDispSort++ ){
		// 表示データの入っているアドレスにセット
		pDispInfo = DispBuffer.DispInfo + pDispSort->no;
		bmpNo = pDispInfo->bmpNo; // ＢＭＰ番号
		//海なら
		if( bmpNo >= 3096 && bmpNo <= 3096 + 9 ){
			//海描画前なら
			if( draw_sea_flg == 0 ){
			//海描画フラグセット
				draw_sea_flg = 1;
			}
		} else {
			//海描画后なら
			if( draw_sea_flg == 1){
				//特别描画へ
				draw_special();
				//特别描画終了
				draw_sea_flg = 2;
			}
		}

		// フォントの优先顺位で分岐
		// DISP_PRIO_IME1の前だったら
		if( putFontFlag == FONT_PRIO_BACK && pDispSort->dispPrio >= DISP_PRIO_IME1 ){
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
#endif
			// フォントをバックサーフェスにセット
			PutFont( FONT_PRIO_BACK );	
			// 次ぎの阶层へ
			putFontFlag++;
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
#endif
		}
		// メニューの前だったら
		if( putFontFlag == FONT_PRIO_BACK2 && pDispSort->dispPrio >= DISP_PRIO_MENU ){
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
#endif
			// フォントをバックサーフェスにセット
			PutFont( FONT_PRIO_BACK2 );	
			// 次ぎの阶层へ
			putFontFlag++;
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
#endif
		}
		// DISP_PRIO_IME3の前だったら
		if( putFontFlag == FONT_PRIO_FRONT && pDispSort->dispPrio >= DISP_PRIO_IME3 ){
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
#endif
			// フォントをバックサーフェスにセット
			PutFont( FONT_PRIO_FRONT );	
			// 次ぎの阶层へ
			putFontFlag++;
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
#endif
		}
		// はい、いいえウィンドウの前だったら
		if( putFontFlag == FONT_PRIO_FRONT2 && pDispSort->dispPrio >= DISP_PRIO_YES_NO_WND ){
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
#endif
			// フォントをバックサーフェスにセット
			PutFont( FONT_PRIO_FRONT2 );	
			// 次ぎの阶层へ
			putFontFlag++;
#ifdef PUK2
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
#endif
		}

		// メニュー书き終わったら
		if( pDispSort->dispPrio > DISP_PRIO_MENU ){
			// マップウィンドウある时
			if( checkMenuActiveNo( MENU_AUTOMAP ) )
			{
				// オートマップ描画
				//drawAutoMap( autoMapWinX, autoMapWinY );
				drawAutoMap( autoMapMenuWin.x, autoMapMenuWin.y );
			}
		}

		// マップのエフェクト
		if( pDispSort->dispPrio >= DISP_PRIO_RESERVE )
		{
			drawMapEffect();
			drawMapEffectFlag = 1;
		}

		// タイルの高速描画
		if( highSpeedDrawSw )	// 高速描画机能を使うか？
		{
			if( pDispSort->dispPrio >= DISP_PRIO_TILE
			 && drawFastTileFlag2 == 0 )
			{
				// バトルサーフェスからバックサーフェスにタイルデータをコピー
				src.top = 0;
				src.left = 0;
				src.right = DEF_APPSIZEX;
				src.bottom = DEF_APPSIZEY;
				sx = 0;
				sy = 0;

				if( highSpeedDrawVarX > 0 )
				{
					src.right -= highSpeedDrawVarX;
					sx += highSpeedDrawVarX;
				}
				else
				if( highSpeedDrawVarX < 0 )
				{
					src.left -= highSpeedDrawVarX;
				}

				if( highSpeedDrawVarY > 0 )
				{
					src.bottom -= highSpeedDrawVarY;
					sy += highSpeedDrawVarY;
				}
				else
				if( highSpeedDrawVarY < 0 )
				{
					src.top -= highSpeedDrawVarY;
				}

				lpDraw->lpBACKBUFFER->BltFast( sx, sy, lpBattleSurface, &src, DDBLTFAST_WAIT );
				drawFastTileFlag2 = 1;
			}
			if( pDispSort->dispPrio > DISP_PRIO_TILE
			 && drawFastTileFlag == 0 )
			{
				// バックサーフェスのタイルデータをバトルサーフェスにコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
				drawFastTileFlag = 1;
			}
		}

		// ＢＭＰ以外の时
		if( bmpNo & STOCK_DISP_BUFFER_NO_BMP ){
			// ボックス、またはラインの时
			if( bmpNo & ( STOCK_DISP_BUFFER_BOX | STOCK_DISP_BUFFER_BOX_FILL | STOCK_DISP_BUFFER_LINE ) ){
				// 表示バッファーからボックスデータを取り出す
				GetBoxDispBuffer( pDispInfo, bmpNo );
			}
#ifdef PUK2
			else if (bmpNo&STOCK_DISP_BUFFER_ALPHA) AlphaCall( pDispInfo );
#endif
			continue;
		}
		
		// ＢＭＰの时
		// 当たり判定のみする时（非表示）
		if( pDispInfo->pAct != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pDispInfo->pAct );
#endif
			if( pDispInfo->pAct->atr & ACT_ATR_HIDE2 ) continue;
		}
		
#ifdef PUK2
		if (pDispInfo->bm.BltVer==BLTVER_NOMAL){
			// ＶＲＡＭにいないときはハードディスクからロードする
			// ＢＭＰをロードする
			if( LoadBmp_VerNomal( pDispInfo ) == FALSE ) continue;

			bltf=pDispInfo->bm.bltf;
			// サーフェスリストの数だけループ
			for(lpSurfaceInfo = SpriteInfo[ bmpNo ].lpSurfaceInfo ;
				lpSurfaceInfo != NULL;
				lpSurfaceInfo = lpSurfaceInfo->pNext ){
					
				// クリッピング处理とOFFスクリーンサーフェースからバックサーフェスに高速転送
				//DrawSurfaceFast( 	pDispInfo->x + lpSurfaceInfo->offsetX, 
				//					pDispInfo->y + lpSurfaceInfo->offsetY, 
				//					lpSurfaceInfo->lpSurface );
				
				// ポインタあるときかつ、当倍でないとき
				if( pDispInfo->pAct != NULL && ( pDispInfo->pAct->scaleX != 1.0 || pDispInfo->pAct->scaleY != 1.0 ) ){
#ifdef PUK3_ACTION_CHECKRANGE
					CheckAction( pDispInfo->pAct );
#endif
					// 扩大缩小転送
					if( DrawSurfaceStretch( pDispInfo->x + ( int )( ( float )( (bltf&BLTF_MRR_X)?SpriteInfo[ bmpNo ].width-lpSurfaceInfo->offsetX-SurfaceSizeX:lpSurfaceInfo->offsetX ) * pDispInfo->pAct->scaleX ), 
											pDispInfo->y + ( int )( ( float )( (bltf&BLTF_MRR_Y)?SpriteInfo[ bmpNo ].height-lpSurfaceInfo->offsetY-SurfaceSizeY:lpSurfaceInfo->offsetY ) * pDispInfo->pAct->scaleY ), 
											pDispInfo->pAct->scaleX, 
											pDispInfo->pAct->scaleY,
											lpSurfaceInfo->lpSurface, bltf, pDispInfo->bm.rgba.rgba ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
				}else{
					// 当倍転送
					if( DrawSurfaceFast( 	pDispInfo->x + ( (bltf&BLTF_MRR_X)?SpriteInfo[ bmpNo ].width-lpSurfaceInfo->offsetX-SurfaceSizeX:lpSurfaceInfo->offsetX ), 
											pDispInfo->y + ( (bltf&BLTF_MRR_Y)?SpriteInfo[ bmpNo ].height-lpSurfaceInfo->offsetY-SurfaceSizeY:lpSurfaceInfo->offsetY ), 
											lpSurfaceInfo->lpSurface, bltf, pDispInfo->bm.rgba.rgba ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
				}
				
				// サーフェスを使用した日付を记忆
				lpSurfaceInfo->date = SurfaceDate;
			}
		}else if (pDispInfo->bm.BltVer==BLTVER_PUK2){
			if( LoadBmp_VerPUK2( pDispInfo ) == FALSE ) continue;
			DrawSurface_PUK2( &SpriteInfo[ pDispInfo->bmpNo ], pDispInfo );
		}
#else
		// ＶＲＡＭにいないときはハードディスクからロードする
		// ＢＭＰをロードする
		if( LoadBmp( bmpNo ) == FALSE ) continue;
		
		// サーフェスリストの数だけループ
		for(lpSurfaceInfo = SpriteInfo[ bmpNo ].lpSurfaceInfo ;
			lpSurfaceInfo != NULL;
			lpSurfaceInfo = lpSurfaceInfo->pNext ){
				
			// クリッピング处理とOFFスクリーンサーフェースからバックサーフェスに高速転送
			//DrawSurfaceFast( 	pDispInfo->x + lpSurfaceInfo->offsetX, 
			//					pDispInfo->y + lpSurfaceInfo->offsetY, 
			//					lpSurfaceInfo->lpSurface );

			// ポインタあるときかつ、当倍でないとき
			if( pDispInfo->pAct != NULL && ( pDispInfo->pAct->scaleX != 1.0 || pDispInfo->pAct->scaleY != 1.0 ) ){
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pDispInfo->pAct );
#endif
				// 扩大缩小転送
				if( DrawSurfaceStretch( pDispInfo->x + ( int )( ( float )lpSurfaceInfo->offsetX * pDispInfo->pAct->scaleX ), 
										pDispInfo->y + ( int )( ( float )lpSurfaceInfo->offsetY * pDispInfo->pAct->scaleY ), 
										pDispInfo->pAct->scaleX, 
										pDispInfo->pAct->scaleY,
										lpSurfaceInfo->lpSurface ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
			}else{
				// 当倍転送
				if( DrawSurfaceFast( 	pDispInfo->x + lpSurfaceInfo->offsetX, 
										pDispInfo->y + lpSurfaceInfo->offsetY, 
										lpSurfaceInfo->lpSurface ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
			}
			
			// サーフェスを使用した日付を记忆
			lpSurfaceInfo->date = SurfaceDate;
		}
#endif
	}


#ifdef PUK2
	if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
#endif
	
	// フォント表示もれ防止
	// 后ろに表示
	if( putFontFlag == FONT_PRIO_BACK ){
		// フォントをバックサーフェスにセット
		PutFont( FONT_PRIO_BACK );	
		// 次ぎの阶层へ
		putFontFlag++;	
	}
	// 后ろに表示２
	if( putFontFlag == FONT_PRIO_BACK2 ){
		// フォントをバックサーフェスにセット
		PutFont( FONT_PRIO_BACK2 );	
		// 次ぎの阶层へ
		putFontFlag++;	
	}
	// 前に表示
	if( putFontFlag == FONT_PRIO_FRONT ){
		// フォントをバックサーフェスにセット
		PutFont( FONT_PRIO_FRONT );	
		// 次ぎの阶层へ
		putFontFlag++;	
	}
	// 前に表示２
	if( putFontFlag == FONT_PRIO_FRONT2 ){
		// フォントをバックサーフェスにセット
		PutFont( FONT_PRIO_FRONT2 );	
	}
	
	// マップのエフェクト
	if( drawMapEffectFlag == 0 )
	{
		drawMapEffect();
	}
#ifdef PUK2
	if (!palchgcnt) nowchging=PalState.count;
	// SprPal解放管理
	SprPalDeathCheck();
#endif
}

#endif /* PUK2 */

//**************************************************************************/
// 	表示データをバッファに溜める（アクションタスク以外）
//**************************************************************************/
// 	引数：	int x, int y	：表示座标
//			UCHAR dispPrio	：表示のプライオリティ
//			int bmpNo		：ＢＭＰ番号
//			BOOL hitFlag	：当たり判定フラグ	０：判定无し
//												１：当たり判定のみ
//												２：当たり判定ボックスも表示
//	戾り值：自分の判定番号
//**************************************************************************/
#ifdef PUK2
int StockDispBuffer( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, struct BLT_MEMBER *bm )
#else
int StockDispBuffer( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag )
#endif
{	
	short dx,dy;
	int BmpNo;
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
	
	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return -2;
	
	// ＢＭＰがセットされてないとき
	if( -1 <= bmpNo && bmpNo <= CG_INVISIBLE ) return -2;
	
	// 通常表示のとき
	if( bmpNo > CG_INVISIBLE ){
		realGetNo( bmpNo , (U4 *)&BmpNo );
		realGetPos( BmpNo  , &dx, &dy);
	}else{
		// ボックス表示のときは变换しない
		dx = 0;
		dy = 0;
		BmpNo = bmpNo;
	}

	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->x = x + dx;
	pDispInfo->y = y + dy;
	pDispInfo->bmpNo = BmpNo;
	pDispInfo->hitFlag = hitFlag;
	pDispInfo->pAct = NULL;
#ifdef PUK2
	if (bm) pDispInfo->bm=*bm;
	else{
		pDispInfo->bm.u=0;
		pDispInfo->bm.v=0;
		pDispInfo->bm.w=0;
		pDispInfo->bm.h=0;
		pDispInfo->bm.rgba.rgba=0xffffffff;
		pDispInfo->bm.BltVer=0;
		pDispInfo->bm.bltf=0;
		pDispInfo->bm.PalNo=0;
	}
#endif
#ifdef PUK2
	pDispInfo->type=DITYPE_BMP_NOMAL;
	if (pDispInfo->bm.BltVer==BLTVER_PUK2) pDispInfo->type=DITYPE_BMP_PUK2;
	if( BmpNo >= 3096 && BmpNo <= 3096 + 9 ) draw_sea_flg=1;
#endif

	// 表示カウンタープラス
	return DispBuffer.DispCnt++;
}

//**************************************************************************/
// 	表示データをバッファに溜める（アクションタスク以外）
//**************************************************************************/
// 	引数：	int x, int y	：表示座标
//			UCHAR dispPrio	：表示のプライオリティ
//			int bmpNo		：ＢＭＰ番号
//			BOOL hitFlag	：当たり判定フラグ	０：判定无し
//												１：当たり判定のみ
//												２：当たり判定ボックスも表示
//	戾り值：自分の判定番号
//**************************************************************************/
#ifdef PUK2
int StockDispBufferEx( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, struct BLT_MEMBER *bm )
#else
int StockDispBufferEx( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag )
#endif
{	
	short dx,dy;
	int BmpNo;
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
	
	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return -2;
	
	// ＢＭＰがセットされてないとき
	//if( -1 <= bmpNo && bmpNo <= CG_INVISIBLE ) return -2;
	
	// 通常表示のとき
	if( bmpNo > CG_INVISIBLE ){
		realGetNo( bmpNo , (U4 *)&BmpNo );
		realGetPos( BmpNo  , &dx, &dy);
	}else{
		// ボックス表示のときは变换しない
		dx = 0;
		dy = 0;
		BmpNo = bmpNo;
	}

	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->x = x + dx;
	pDispInfo->y = y + dy;
	pDispInfo->bmpNo = BmpNo;
	pDispInfo->hitFlag = hitFlag;
	pDispInfo->pAct = NULL;
#ifdef PUK2
	if (bm) pDispInfo->bm=*bm;
	else{
		pDispInfo->bm.u=0;
		pDispInfo->bm.v=0;
		pDispInfo->bm.w=0;
		pDispInfo->bm.h=0;
		pDispInfo->bm.rgba.rgba=0xffffffff;
		pDispInfo->bm.BltVer=0;
		pDispInfo->bm.bltf=0;
		pDispInfo->bm.PalNo=0;
	}
#endif
#ifdef PUK2
	pDispInfo->type=DITYPE_BMP_NOMAL;
	if (pDispInfo->bm.BltVer==BLTVER_PUK2) pDispInfo->type=DITYPE_BMP_PUK2;
	if( BmpNo >= 3096 && BmpNo <= 3096 + 9 ) draw_sea_flg=1;
#endif

	// 表示カウンタープラス
	return DispBuffer.DispCnt++;
}

//**************************************************************************/
// 	表示データをバッファに溜める（アクションタスク以外）(realGetNo()をしない)
//**************************************************************************/
// 	引数：	int x, int y	：表示座标
//			UCHAR dispPrio	：表示のプライオリティ
//			int bmpNo：ＢＭＰ番号
//			BOOL hitFlag	：当たり判定フラグ	０：判定无し
//												１：当たり判定のみ
//												２：当たり判定ボックスも表示
//	戾り值：自分の判定番号
//**************************************************************************/
#ifdef PUK2
int StockDispBuffer2( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, struct BLT_MEMBER *bm )
#else
int StockDispBuffer2( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag )
#endif
{	
	short dx,dy;
	int BmpNo;
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
	
	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return -2;
	
	// ＢＭＰがセットされてないとき
	if( -1 <= bmpNo && bmpNo <= CG_INVISIBLE ) return -2;
	
	// 通常表示のとき
	if( bmpNo > CG_INVISIBLE ){
		BmpNo = bmpNo;
		realGetPos( BmpNo  , &dx, &dy);
	}else{
		// ボックス表示のときは变换しない
		dx = 0;
		dy = 0;
		BmpNo = bmpNo;
	}

	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->x = x + dx;
	pDispInfo->y = y + dy;
	pDispInfo->bmpNo = BmpNo;
	pDispInfo->hitFlag = hitFlag;
	pDispInfo->pAct = NULL;
#ifdef PUK2
	if (bm) pDispInfo->bm=*bm;
	else{
		pDispInfo->bm.u=0;
		pDispInfo->bm.v=0;
		pDispInfo->bm.w=0;
		pDispInfo->bm.h=0;
		pDispInfo->bm.rgba.rgba=0xffffffff;
		pDispInfo->bm.BltVer=0;
		pDispInfo->bm.bltf=0;
		pDispInfo->bm.PalNo=0;
	}
#endif
#ifdef PUK2
	pDispInfo->type=DITYPE_BMP_NOMAL;
	if (pDispInfo->bm.BltVer==BLTVER_PUK2) pDispInfo->type=DITYPE_BMP_PUK2;
	if( BmpNo >= 3096 && BmpNo <= 3096 + 9 ) draw_sea_flg=1;
#endif

	// 表示カウンタープラス
	return DispBuffer.DispCnt++;
}

//**************************************************************************/
// タスク表示データをバッファに溜める 
//**************************************************************************/
void StockTaskDispBuffer( void )
{
	ACTION *pActLoop; 	/* 先头のリストポインタ取得 */
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
#ifdef PUK2_3DDEVICECHANGE_BATTLE
	BOOL use3D = getUsable3D();
#endif
	
	/* 最后尾が来るまでループ */	
	for(pActLoop = pActTop->pNext ; 
		pActLoop != pActBtm ;
		/* 次のポインタをセット */
		pActLoop = pActLoop->pNext ){
		
#ifdef PUK2_3DDEVICECHANGE_BATTLE
		// ３Ｄが使用できて、无视が设定されているときはここは处理しない
		if ( use3D && (pActLoop->atr & ACT_ATR_3D_NOFLASH) );
		else{
			// 点灭フラグＯＮのとき拔ける
			if( pActLoop->atr & ACT_ATR_FLASH_0 && FlipCnt == 0 ){ 
				// マウスカーソル当たり判定番号记忆
				pActLoop->hitDispNo = -2;
				continue;
			}
			if( pActLoop->atr & ACT_ATR_FLASH_1 && FlipCnt == 1 ){ 
				// マウスカーソル当たり判定番号记忆
				pActLoop->hitDispNo = -2;
				continue;
			}
		}
#else
		// 点灭フラグＯＮのとき拔ける
		if( pActLoop->atr & ACT_ATR_FLASH_0 && FlipCnt == 0 ){ 
			// マウスカーソル当たり判定番号记忆
			pActLoop->hitDispNo = -2;
			continue;
		}
		if( pActLoop->atr & ACT_ATR_FLASH_1 && FlipCnt == 1 ){ 
			// マウスカーソル当たり判定番号记忆
			pActLoop->hitDispNo = -2;
			continue;
		}
#endif
		
		// 文字列があるとき
		if( pActLoop->fontStr != NULL ){
			// フォント情报をバッファに溜める（种类指定あり）
			StockFontBuffer( pActLoop->fontX,pActLoop->fontY, 
							pActLoop->fontPrio, pActLoop->fontKind, 
							pActLoop->fontColor, pActLoop->fontStr, pActLoop->fontHitFlag );
#ifdef PUK2
			pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
			pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
#endif
		}
		
		// カウントオーバーの时
		if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) break;
		
		// ＢＭＰがセットされてないとき
		if( -1 == pActLoop->bmpNo ) continue;
		
		// 死亡フラグが立っているとき
		if( pActLoop ->deathFlag == TRUE ) continue;
		
		/* 非表示なら拔ける */
		if( pActLoop->atr & ACT_ATR_HIDE ) continue;

		// 表示优先ソート用构造体
		pDispSort->dispPrio = pActLoop->dispPrio;
		pDispSort->no = DispBuffer.DispCnt;
		// 表示情报构造体（ ソートしない内容 ）
		pDispInfo->x = pActLoop->x + ( int )( ( float )pActLoop->anim_x * pActLoop->scaleX );
		pDispInfo->y = pActLoop->y + ( int )( ( float )pActLoop->anim_y * pActLoop->scaleY );
		pDispInfo->bmpNo = pActLoop->bmpNo;
#ifdef PUK2
		pDispInfo->bm = pActLoop->bm;
#endif
#ifdef PUK2
		if (pActLoop->rgbaon){													// プログラム中で色要素を变更する场合
			if (pActLoop->rgbaon&1) pDispInfo->bm.rgba.r=pActLoop->rgba.r;
			if (pActLoop->rgbaon&2) pDispInfo->bm.rgba.g=pActLoop->rgba.g;
			if (pActLoop->rgbaon&4) pDispInfo->bm.rgba.b=pActLoop->rgba.b;
			if (pActLoop->rgbaon&8) pDispInfo->bm.rgba.a=pActLoop->rgba.a;
		}
		if (pActLoop->bltfon){													// プログラム中で绘图设定を变更する场合
			pDispInfo->bm.bltf&=~pActLoop->bltfon;
			pDispInfo->bm.bltf|=pActLoop->bltf&pActLoop->bltfon;
		}
		if( pActLoop->atr & ACT_ATR_TRANCEPARENT ) pDispInfo->bm.rgba.a = 128;
#endif
		pDispInfo->pAct = pActLoop;
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pDispInfo->pAct );
#endif
		
		// 当たり判定するか？
		// 复数选择の时
		//if( pActLoop->atr & ACT_ATR_HIT_BOX_ALL3 ) pDispInfo->hitFlag = 5;
		//else
		//if( pActLoop->atr & ACT_ATR_HIT_BOX_ALL2 ) pDispInfo->hitFlag = 4;
		//else 
		//if( pActLoop->atr & ACT_ATR_HIT_BOX_ALL1 ) pDispInfo->hitFlag = 3;
		//else 
		// ボックス表示
		if( pActLoop->atr & ACT_ATR_HIT_BOX ) pDispInfo->hitFlag = 2;
		else // 判定のみ
		if( pActLoop->atr & ACT_ATR_HIT ) pDispInfo->hitFlag = 1;
		else pDispInfo->hitFlag = FALSE;
		// マウスカーソル当たり判定番号记忆
		pActLoop->hitDispNo = DispBuffer.DispCnt;
#ifdef PUK2
		pDispInfo->type=DITYPE_BMP_NOMAL;
		if (pDispInfo->bm.BltVer==BLTVER_PUK2) pDispInfo->type=DITYPE_BMP_PUK2;
		if (pDispInfo->pAct->atr & ACT_ATR_HIDE2) pDispInfo->type=DITYPE_NOTBLT;
		if( pDispInfo->bmpNo >= 3096 && pDispInfo->bmpNo <= 3096 + 9 ) draw_sea_flg=1;
#endif
		
		// 表示カウンタープラス
		DispBuffer.DispCnt++;
		// アドレスプラス
		pDispSort++;
		pDispInfo++;
	}
	
}

//**************************************************************************/
// タスク表示データをバッファに溜める 
// （ただしprio1からprio2までのものは处理しない）
//**************************************************************************/
//	引数：	int prio1	：表示の优先顺位始まり
//			int prio2	：表示の优先顺位終り
//**************************************************************************/
void StockTaskDispBuffer2( int prio1, int prio2 )
{
	ACTION *pActLoop; 	/* 先头のリストポインタ取得 */
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
	int tmp;
	
	if( prio1 > prio2 )
	{
		tmp = prio1;
		prio1 = prio2;
		prio2 = tmp;
	}
	
	/* 最后尾が来るまでループ */	
	//while( pActLoop != pActBtm ){
	for(pActLoop = pActTop->pNext ; 
		pActLoop != pActBtm ;
		/* 次のポインタをセット */
		pActLoop = pActLoop->pNext ){
		
		// 点灭フラグＯＮのとき拔ける
		if( pActLoop->atr & ACT_ATR_FLASH_0 && FlipCnt == 0 ) continue;
		if( pActLoop->atr & ACT_ATR_FLASH_1 && FlipCnt == 1 ) continue;
		
		// 文字列があるとき
		if( pActLoop->fontStr != NULL && !( pActLoop->atr & ACT_ATR_FONT_HIDE ) ){
			// フォント情报をバッファに溜める（种类指定あり）
			StockFontBuffer( pActLoop->fontX, pActLoop->fontY, 
							pActLoop->fontPrio, pActLoop->fontKind, 
							pActLoop->fontColor, pActLoop->fontStr, pActLoop->fontHitFlag );
#ifdef PUK2
			pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
			pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
#endif
		}
		
		// カウントオーバーの时
		if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) break;
		
		// prio1からprio2の指定范围は处理しない
		if( prio1 <= pActLoop->dispPrio && pActLoop->dispPrio <= prio2 )
			continue;
		
		// ＢＭＰがセットされてないとき
		if( -1 == pActLoop->bmpNo ) continue;
		
		// 死亡フラグが立っているとき
		if( pActLoop ->deathFlag == TRUE ) continue;
		
		/* 非表示なら拔ける */
		if( pActLoop->atr & ACT_ATR_HIDE ) continue;

		// 表示优先ソート用构造体
		pDispSort->dispPrio = pActLoop->dispPrio;
		pDispSort->no = DispBuffer.DispCnt;
		// 表示情报构造体（ ソートしない内容 ）
		pDispInfo->x = pActLoop->x + pActLoop->anim_x;
		pDispInfo->y = pActLoop->y + pActLoop->anim_y;
		pDispInfo->bmpNo = pActLoop->bmpNo;
#ifdef PUK2
		pDispInfo->bm = pActLoop->bm;
#endif
#ifdef PUK2
		if (pActLoop->rgbaon){													// プログラム中で色要素を变更する场合
			if (pActLoop->rgbaon&1) pDispInfo->bm.rgba.r=pActLoop->rgba.r;
			if (pActLoop->rgbaon&2) pDispInfo->bm.rgba.g=pActLoop->rgba.g;
			if (pActLoop->rgbaon&4) pDispInfo->bm.rgba.b=pActLoop->rgba.b;
			if (pActLoop->rgbaon&8) pDispInfo->bm.rgba.a=pActLoop->rgba.a;
		}
		if (pActLoop->bltfon){													// プログラム中で绘图设定を变更する场合
			pDispInfo->bm.bltf&=~pActLoop->bltfon;
			pDispInfo->bm.bltf|=pActLoop->bltf&pActLoop->bltfon;
		}
		if( pActLoop->atr & ACT_ATR_TRANCEPARENT ) pDispInfo->bm.rgba.a = 128;
#endif
		pDispInfo->pAct = pActLoop;
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pDispInfo->pAct );
#endif
		
		// 当たり判定するか？
		if( pActLoop->atr & ACT_ATR_HIT_BOX ) pDispInfo->hitFlag = 2;
		else 
		if( pActLoop->atr & ACT_ATR_HIT ) pDispInfo->hitFlag = 1;
		else pDispInfo->hitFlag = FALSE;
		// マウスカーソル当たり判定番号记忆
		pActLoop->hitDispNo = DispBuffer.DispCnt;
#ifdef PUK2
		pDispInfo->type=DITYPE_BMP_NOMAL;
		if (pDispInfo->bm.BltVer==BLTVER_PUK2) pDispInfo->type=DITYPE_BMP_PUK2;
		if (pDispInfo->pAct->atr & ACT_ATR_HIDE2) pDispInfo->type=DITYPE_NOTBLT;
		if( pDispInfo->bmpNo >= 3096 && pDispInfo->bmpNo <= 3096 + 9 ) draw_sea_flg=1;
#endif
		
		// 表示カウンタープラス
		DispBuffer.DispCnt++;
		// アドレスプラス
		pDispSort++;
		pDispInfo++;
	}
	
}

//****************************************************************************/
// ボックス表示データをバッファに溜める 
//****************************************************************************/
// 引数：	int x1, y1:		左上の座标
// 			int x2, y2;		右下の座标
// 			UCHAR dispPrio;	表示の优先顺位
// 			int color,		色（パレットから选ぶ）
// 			BOOL fill;		ボックスの种类　０：枠だけ　１：涂りつぶし
//****************************************************************************/
void StockBoxDispBuffer( int x1, int y1, int x2, int y2, UCHAR dispPrio, int color, BOOL fill )
{
	int col;	// 色の指定
	// int に short を２个いれる
	int x = ( ( unsigned short )x1 << 16 ) | ( unsigned short )x2; 	// 上位２バイトと下位２バイトにいれる
	int y = ( ( unsigned short )y1 << 16 ) | ( unsigned short )y2;	// 上位２バイトと下位２バイトにいれる
	
	// ボックスのとき
	if( fill == 0 ) col = color | STOCK_DISP_BUFFER_BOX | STOCK_DISP_BUFFER_NO_BMP ;
	// 涂りつぶしのとき
	else if( fill == 1 ) col = color | STOCK_DISP_BUFFER_BOX_FILL | STOCK_DISP_BUFFER_NO_BMP ;
	// ラインの时
	else if( fill == 2 ) col = color | STOCK_DISP_BUFFER_LINE | STOCK_DISP_BUFFER_NO_BMP ;
	
#ifdef PUK2
	// ボックスをバッファに溜める
	col=StockDispBuffer( x, y, dispPrio, col, 0 );

	DispBuffer.DispInfo[col].type = DITYPE_PAINT;
#else
	// ボックスをバッファに溜める
	StockDispBuffer( x, y, dispPrio, col, 0 );
#endif
}

// 表示バッファーからボックスデータを取り出す *********************************/
void GetBoxDispBuffer( DISP_INFO *pDispInfo, int bmpNo )
{
	// 领域のセット
	RECT rect;
	int color; // 色
	BOOL fill = FALSE; // 涂りつぶしフラグ
	int DispInfoX = pDispInfo->x;
	int DispInfoY = pDispInfo->y;
	
//	short 0xffff
	
//	int   0x0000 ffff
//	short 0xffff
	
	// 座标を取り出す
	rect.right =	( LONG )( (short)( pDispInfo->x & 0xffff ) );
	rect.bottom =	( LONG )( (short)( pDispInfo->y & 0xffff ) );
	
	//rect.left = 	( LONG )( ( pDispInfo->x >> 16 ) & 0xffff );
	//rect.top = 		( LONG )( ( pDispInfo->y >> 16 ) & 0xffff );
	
	// インラインアセンブラ
	_asm{
		// rect.left を取り出す
		mov		cl,16				// シフト数１６をセット
		mov		eax, [ DispInfoX ]
		sar		eax, cl				// 符号付右シフト
		mov		[ rect.left ], eax
		// rect.top を取り出す
		mov		eax, [ DispInfoY ]
		sar		eax, cl
		mov		[ rect.top ], eax
	}

	
#ifdef PUK2
	// ボックスのとき
	if( bmpNo & STOCK_DISP_BUFFER_BOX ){ 
		color = bmpNo & 0xffff; 	// 色データを取り出す
		DrawBox( &rect, color, 0 );	// ボックス描画
		
	// 涂りつぶしのとき
	}else if( bmpNo & STOCK_DISP_BUFFER_BOX_FILL ){ 
		color = color = bmpNo & 0xffff;	// 色指定
		DrawBox( &rect, color, 1 );	// ボックス描画
		
	}else if( bmpNo & STOCK_DISP_BUFFER_LINE ){ 
		color = color = bmpNo & 0xffff;	// 色指定
		DrawBox( &rect, color, 2 );	// ライン描画
	}
#else
	// ボックスのとき
	if( bmpNo & STOCK_DISP_BUFFER_BOX ){ 
		color = bmpNo & 0xff; 			// 色データを取り出す
		DrawBox( &rect, color, 0 );	// ボックス描画
		
	// 涂りつぶしのとき
	}else if( bmpNo & STOCK_DISP_BUFFER_BOX_FILL ){ 
		color = color = bmpNo & 0xff;	// 色指定
		DrawBox( &rect, color, 1 );	// ボックス描画
		
	}else if( bmpNo & STOCK_DISP_BUFFER_LINE ){ 
		color = color = bmpNo & 0xff;	// 色指定
		DrawBox( &rect, color, 2 );	// ライン描画
	}
#endif
}			

// ソート比较关数 //////////////////////////////////////////////////////////////
int SortComp( DISP_SORT *pDisp1, DISP_SORT *pDisp2 )
{
#if 1
	// pDisp1 の表示优先度の方が大きい时、入れ替え
	if( pDisp1->dispPrio > pDisp2->dispPrio ){
		return 1;
	}
	
	// pDisp2 の表示优先度の方が大きい时、そのまま
	if( pDisp1->dispPrio < pDisp2->dispPrio ){
		return -1;
	}
	// 等しい时は登録した顺
	// pDisp1 の方が早かった时、そのまま（のはず？）
	return pDisp2->no - pDisp1->no;
	//if( pDisp1->no > pDisp2->no ){
	//	return -1;
	//}
	// どれでもない时、入れ替え（のはず？）
	//return 1;
#else
	int cmpare;
	compare = pDisp1->dispPrio - pDisp2->dispPrio;
	if( compare != 0 ) return compare;
	
	return pDisp2->no - pDisp1->no;
	
#endif
}
