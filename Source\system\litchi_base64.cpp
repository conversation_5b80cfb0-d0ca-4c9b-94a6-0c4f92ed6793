﻿/*
 * $Id: base64.c,v 1.2 2004/02/04 02:17:32 hno Exp $
 */
#include <stdlib.h>
#include "..\systeminc\litchi_base64.h"

int base64_value[BASE64_VALUE_SZ];
char base64_code[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"; //MLHIDE
//char base64_code[] = "ghi0jk1noUVWXpq2rst3uvA4BC5DE6FGH7IJKL8M/NOP9QRSTYZabcdefw+xyzlm";

void Base64Init(){
    int i;

    for (i = 0; i < BASE64_VALUE_SZ; i++)
		base64_value[i] = -1;

    for (i = 0; i < 64; i++)
		base64_value[(int) base64_code[i]] = i;
    base64_value['='] = 0;
}

void Base64Decode(const char *srcString,char *destString){
    int j;
    int c;
    long val;
    if (!srcString)
		return;

    val = c = 0;
    for (j = 0; *srcString && j + 4 < BASE64_RESULT_SZ; srcString++) {
		unsigned char ch=((unsigned char) *srcString);
		if(ch=='/')		//修正部分一：当加密字符为'/'时，替换值为0
			ch=0;
		unsigned int k = ch % BASE64_VALUE_SZ;
		if (base64_value[k] < 0)
			continue;
		val <<= 6;
		val += base64_value[k];
		if (++c < 4)
			continue;
		/* One quantum of four encoding characters/24 bit */
		destString[j++] = (val >> 16) & 0xff;	/* High 8 bits */
		destString[j++] = (val >> 8) & 0xff;	/* Mid 8 bits */
		destString[j++] = val & 0xff;	/* Low 8 bits */
		val = c = 0;
    }
    destString[j] = 0;
    return;
}

/* adopted from http://ftp.sunet.se/pub2/gnu/vm/base64-encode.c with adjustments */
void Base64Encode(const char *srcString,char *destString){
    int bits = 0;
    int char_count = 0;
    int out_cnt = 0;
    int c;

    if (!srcString)
		return;

    while ((c = (unsigned char) *srcString++) && out_cnt < sizeof(destString) - 5) {
		bits += c;
		char_count++;
		if (char_count == 3) {//修正部分二：当操作结果为0时，用'/'替换
			char tmp=base64_code[bits >> 18];
			if(tmp==0)
				destString[out_cnt++] ='/' ;
			else
				destString[out_cnt++] =tmp;

			tmp=base64_code[(bits >> 12) & 0x3f];
			if(tmp==0)
				destString[out_cnt++] ='/' ;
			else
				destString[out_cnt++] =tmp;

			tmp=base64_code[(bits >> 6) & 0x3f];
			if(tmp==0)
				destString[out_cnt++] ='/' ;
			else
				destString[out_cnt++] =tmp;
			
			tmp=base64_code[bits & 0x3f];
			if(tmp==0)
				destString[out_cnt++] ='/' ;
			else
				destString[out_cnt++] =tmp;

			bits = 0;
			char_count = 0;
		} else {
			bits <<= 8;
		}
    }
    if (char_count != 0) {
		bits <<= 16 - (8 * char_count);
		destString[out_cnt++] = base64_code[bits >> 18];
		destString[out_cnt++] = base64_code[(bits >> 12) & 0x3f];
		if (char_count == 1) {
			destString[out_cnt++] = '=';
			destString[out_cnt++] = '=';
		} else {
			destString[out_cnt++] = base64_code[(bits >> 6) & 0x3f];
			destString[out_cnt++] = '=';
		}
    }
    destString[out_cnt] = '\0';	/* terminate */
    return;
}