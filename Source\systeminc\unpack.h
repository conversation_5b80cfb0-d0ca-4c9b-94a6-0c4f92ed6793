﻿// unpack.cpp ヘッダファイル
#ifndef _UNPACK_H_
#define _UNPACK_H_

typedef struct
{
	unsigned char id[2];
	unsigned char compressFlag;
	unsigned int width;
	unsigned int height;
	unsigned int size;
} RD_HEADER;

#ifdef PUK2
unsigned char *encoder( unsigned char *buf, unsigned char **disBuf,
	unsigned int width, unsigned int height, unsigned int *len, int cmpFlag, unsigned int palsize );
unsigned char *decoder( unsigned char *buf, unsigned char **disBuf,
	unsigned int *width1, unsigned int *height1, unsigned int *len, unsigned int *palsiz );
#else
unsigned char *encoder( unsigned char *, unsigned char **,
	unsigned int, unsigned int, unsigned int *, int );
unsigned char *decoder( unsigned char *, unsigned char **,
	unsigned int *, unsigned int *, unsigned int * );
#endif

#endif
