﻿//ＢＢＳ（揭示板）

#define MESSAGE_BOARD_LIST_COUNT	4		// ウィンドウ内に表示する人数

#define MESSAGE_BOARD_STR_COUNT		35*2	// 揭示板中での１ラインの文字数

extern MESSAGE_BOARD_INFO messageBoardList[MESSAGE_BOARD_LIST_COUNT];

void MenuWindowBBSClose( void );

struct BBSMessageFormat{

	long FaceGraphicNum;	//颜グラフィック
	time_t Time;			//书き込み时间
	char Name[256];			//名称
	char Message[3][256];	//信息

	int  Flag;

};

struct BBSFormat{

	int ButtonType;
	BBSMessageFormat BBSMeFor[MESSAGE_BOARD_LIST_COUNT];

};

BBSFormat BBSFor;

void InitMenuWindowBBS(void);

INPUT_STR MenuBBSInputStr;

INIT_STR_STRUCT InitStrStructBBS={
//  本体		ofx,ofy ,piro       ,Font           ,color         ,str ,<PERSON><PERSON><PERSON>,MAX<PERSON>en	,dist, flag
	&MenuBBSInputStr,33,368,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_WHITE,"",   MESSAGE_BOARD_LINE, MESSAGE_BOARD_LEN, 16,     0
};

//--------------------------------------------------------
//ログイン时の初期化
//--------------------------------------------------------
void InitMenuWindowBBSInLogin(void){

	int i;

	BBSFor.ButtonType=0;

	for(i=0;i<MESSAGE_BOARD_LIST_COUNT;i++){
		BBSFor.BBSMeFor[i].FaceGraphicNum=0;
		BBSFor.BBSMeFor[i].Flag=0;
		BBSFor.BBSMeFor[i].Message[0][0]='\0';
		BBSFor.BBSMeFor[i].Message[1][0]='\0';
		BBSFor.BBSMeFor[i].Message[2][0]='\0';
		BBSFor.BBSMeFor[i].Name[0]='\0';
		BBSFor.BBSMeFor[i].Time=0;
	}

}

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowBBS( int mouse )
{
	DIALOG_SWITCH *Dialog;

	//初期化
	if (mouse==WIN_INIT){
		InitStrStructBBS.inputStr->buffer[0] = '\0';

		//ダイアログ初期化
		SetInputStr(&InitStrStructBBS,wI->wx,wI->wy,0);
		//フォーカスを取る
		GetKeyInputFocus( InitStrStructBBS.inputStr );

		DiarogST.SwAdd=wI->sw[EnumBBSDialogInput].Switch;
		Dialog=(DIALOG_SWITCH *)wI->sw[EnumBBSDialogInput].Switch;
		Dialog->InpuStrAdd=InitStrStructBBS.inputStr;
	}

	InitMenuWindowBBS();

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 2, 2 ) )
	{
		MenuWindowBBSClose();
	}


	return TRUE;
}

BOOL MenuWindowBBSDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

BOOL MenuWindowBBSDel( void ){

	if(serverRequestWinWindowType==WINDOW_MESSAGETYPE_BOARD)
		serverRequestWinWindowType=-1;

	return TRUE;

}

// ダイアログ
BOOL MenuSwitchBBSDialog( int no, unsigned int flag ){

	DIALOG_SWITCH *Dialog;
	BOOL ReturnFlag=FALSE;	

	
	if( flag & MENU_MOUSE_OVER ){
		if( InitStrStructBBS.inputStr->buffer[0] != '\0' ){
			strcpy( OneLineInfoStr, MWONELINE_BBS_OK );
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		if(DiarogST.SwAdd!=wI->sw[no].Switch){
			//ダイアログ初期化
			SetInputStr(&InitStrStructBBS,wI->wx,wI->wy,0);
			//フォーカスを取る
			GetKeyInputFocus( InitStrStructBBS.inputStr );

			DiarogST.SwAdd=wI->sw[no].Switch;
			Dialog=(DIALOG_SWITCH *)wI->sw[no].Switch;
			Dialog->InpuStrAdd=InitStrStructBBS.inputStr;
		}

		ReturnFlag=TRUE;
	}

	return ReturnFlag;

}

//ＢＢＳスイッチ 
BOOL MenuSwitchBBS( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	char str[256];
	char msg[256];

	switch(no){
		case EnumGraphBBSDelete00:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_DELETE );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				sprintf( str, "%d", 0 );
				makeSendString( str, msg, sizeof( msg )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_DELETE, msg );
				MenuWindowBBSClose();

				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphBBSDelete01:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_DELETE );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				sprintf( str, "%d", 1 );
				makeSendString( str, msg, sizeof( msg )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_DELETE, msg );
				MenuWindowBBSClose();

				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphBBSDelete02:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_DELETE );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				sprintf( str, "%d", 2 );
				makeSendString( str, msg, sizeof( msg )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_DELETE, msg );
				MenuWindowBBSClose();

				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphBBSDelete03:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_DELETE );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				sprintf( str, "%d", 3 );
				makeSendString( str, msg, sizeof( msg )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_DELETE, msg );
				MenuWindowBBSClose();

				ReturnFlag=TRUE;
			}
			break;

		case EnumGraphBBSOK:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSOKOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_OK );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSOKOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if( InitStrStructBBS.inputStr->buffer[0] != '\0' )
				{
					strcpy(str,InitStrStructBBS.inputStr->buffer);
					// ここは信息をエスケープする。
					makeSendString( str, msg, sizeof( msg ) );
					nrproto_WN_send( sockfd, mapGx, mapGy,
						serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_OK, msg );
					InitStrStructBBS.inputStr->buffer[0] = '\0';

					MenuWindowBBSClose();
				}
				else
				{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}

				ReturnFlag=TRUE;
			}
			break;

		case EnumGraphBBSCancel:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSCancelOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_CANCEL );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSCancelOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				// このウィンドウを关闭
				MenuWindowBBSClose();
			}
			break;

		//ネクスト
		case EnumGraphBBSNextBt:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSNextOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_LEFT );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSNextOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){

				msg[0] = '\0';
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_NEXT, msg );
				
				// このウィンドウを关闭
				MenuWindowBBSClose();

				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSNextOff;
			}
			break;

		//バック
		case EnumGraphBBSBackBt:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSBackOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS_RIGHT );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSBackOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){

				msg[0] = '\0';
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_PRE, msg );
				
				// このウィンドウを关闭
				MenuWindowBBSClose();

				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_BBSBackOff;
			}
			break;

		case EnumGraphBBSClose:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_WindowCloseOver;
				strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );
				ReturnFlag=TRUE;
			}else{
				//重なってない
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_WindowCloseOff;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				MenuWindowBBSClose();
			}
			break;
			
	}

	return ReturnFlag;
}

void InitMenuWindowBBS(void){

	time_t t;
	struct tm *tt;
	char str[256];

	//Ｎｏ１
	((GRAPHIC_SWITCH *)wI->sw[EnumGraphBBSFace00].Switch)->graNo=BBSFor.BBSMeFor[0].FaceGraphicNum;
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSName00].Switch)->text,BBSFor.BBSMeFor[0].Name);
	if(BBSFor.BBSMeFor[0].Time>0){
		t = (time_t)BBSFor.BBSMeFor[0].Time;
		tt = localtime( &t );
		sprintf( str, "%4d/%02d/%02d/%02d:%02d",
			1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSTime00].Switch)->text,str);
	}else{
		wI->sw[EnumTextBBSTime00].Enabled=FALSE;
	}
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS00_00].Switch)->text,BBSFor.BBSMeFor[0].Message[0]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS00_01].Switch)->text,BBSFor.BBSMeFor[0].Message[1]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS00_02].Switch)->text,BBSFor.BBSMeFor[0].Message[2]);

	if(BBSFor.BBSMeFor[0].Flag)
		wI->sw[EnumGraphBBSDelete00].Enabled=TRUE;
	else
		wI->sw[EnumGraphBBSDelete00].Enabled=FALSE;

	//Ｎｏ２
	((GRAPHIC_SWITCH *)wI->sw[EnumGraphBBSFace01].Switch)->graNo=BBSFor.BBSMeFor[1].FaceGraphicNum;
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSName01].Switch)->text,BBSFor.BBSMeFor[1].Name);
	if(BBSFor.BBSMeFor[1].Time>0){
		t = (time_t)BBSFor.BBSMeFor[1].Time;
		tt = localtime( &t );
		sprintf( str, "%4d/%02d/%02d/%02d:%02d",
			1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSTime01].Switch)->text,str);
	}else{
		wI->sw[EnumTextBBSTime01].Enabled=FALSE;
	}
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS01_00].Switch)->text,BBSFor.BBSMeFor[1].Message[0]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS01_01].Switch)->text,BBSFor.BBSMeFor[1].Message[1]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS01_02].Switch)->text,BBSFor.BBSMeFor[1].Message[2]);

	if(BBSFor.BBSMeFor[1].Flag)
		wI->sw[EnumGraphBBSDelete01].Enabled=TRUE;
	else
		wI->sw[EnumGraphBBSDelete01].Enabled=FALSE;

	//Ｎｏ３
	((GRAPHIC_SWITCH *)wI->sw[EnumGraphBBSFace02].Switch)->graNo=BBSFor.BBSMeFor[2].FaceGraphicNum;
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSName02].Switch)->text,BBSFor.BBSMeFor[2].Name);
	if(BBSFor.BBSMeFor[2].Time>0){
		t = (time_t)BBSFor.BBSMeFor[2].Time;
		tt = localtime( &t );
		sprintf( str, "%4d/%02d/%02d/%02d:%02d",
			1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSTime02].Switch)->text,str);
	}else{
		wI->sw[EnumTextBBSTime02].Enabled=FALSE;
	}
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS02_00].Switch)->text,BBSFor.BBSMeFor[2].Message[0]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS02_01].Switch)->text,BBSFor.BBSMeFor[2].Message[1]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS02_02].Switch)->text,BBSFor.BBSMeFor[2].Message[2]);

	if(BBSFor.BBSMeFor[2].Flag)
		wI->sw[EnumGraphBBSDelete02].Enabled=TRUE;
	else
		wI->sw[EnumGraphBBSDelete02].Enabled=FALSE;

	//Ｎｏ４
	((GRAPHIC_SWITCH *)wI->sw[EnumGraphBBSFace03].Switch)->graNo=BBSFor.BBSMeFor[3].FaceGraphicNum;
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSName03].Switch)->text,BBSFor.BBSMeFor[3].Name);
	if(BBSFor.BBSMeFor[3].Time>0){
		t = (time_t)BBSFor.BBSMeFor[3].Time;
		tt = localtime( &t );
		sprintf( str, "%4d/%02d/%02d/%02d:%02d",
			1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBSTime03].Switch)->text,str);
	}else{
		wI->sw[EnumTextBBSTime03].Enabled=FALSE;
	}
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS03_00].Switch)->text,BBSFor.BBSMeFor[3].Message[0]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS03_01].Switch)->text,BBSFor.BBSMeFor[3].Message[1]);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextBBS03_02].Switch)->text,BBSFor.BBSMeFor[3].Message[2]);

	if(BBSFor.BBSMeFor[3].Flag)
		wI->sw[EnumGraphBBSDelete03].Enabled=TRUE;
	else
		wI->sw[EnumGraphBBSDelete03].Enabled=FALSE;

	//ネクストある时
	if(BBSFor.ButtonType & WINDOW_BUTTONTYPE_NEXT){
		wI->sw[EnumGraphBBSNextBt].Enabled=TRUE;
	}else{
		wI->sw[EnumGraphBBSNextBt].Enabled=FALSE;
	}

	//バックあるとき
	if(BBSFor.ButtonType & WINDOW_BUTTONTYPE_PRE){
		wI->sw[EnumGraphBBSBackBt].Enabled=TRUE;
	}else{
		wI->sw[EnumGraphBBSBackBt].Enabled=FALSE;
	}

	if(DiarogST.SwAdd==wI->sw[EnumBBSDialogInput].Switch){
		//自分のダイアログを使用しているとき
		SetInputStr(&InitStrStructBBS,wI->wx,wI->wy,2);
	}

}

// 揭示板ウィンドウの情报を取り出す
void openMessageNewBoardMenu( int ButtonType, char *data)
{
	char str[768];
	char MessageWork[256];
	int i, j,k,l;
	int StrCount,StrLineLen;

	//ボタンの种类
	BBSFor.ButtonType=ButtonType;

	//信息の解析と格纳
#ifdef BBS_ESCAPE	// ちょいと正常化のテスト
	makeStringFromEscaped( data );
#else
	makeRecvString( data );
#endif

	memset( &messageBoardList, 0, sizeof( messageBoardList ) );

	getStringToken( data, '|', 1, 16, serverRequestWinStr[0] );

	j = 2;
	for( i = 0; i < MESSAGE_BOARD_LIST_COUNT; i++ ){

		//颜画像
#ifdef _CG2_NEWGRAPHIC
		BBSFor.BBSMeFor[i].FaceGraphicNum=getNewFaceGraphicNo( getIntegerToken( data, '|', j++ ));
#else
		BBSFor.BBSMeFor[i].FaceGraphicNum=getIntegerToken( data, '|', j++ );
#endif

		//书き込み时间
		BBSFor.BBSMeFor[i].Time=(time_t)getIntegerToken( data, '|', j++ );
		//フラグ（デリート权限等）
		BBSFor.BBSMeFor[i].Flag=getIntegerToken( data, '|', j++ );

		//キャラクター名
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
#ifdef BBS_ESCAPE		// ここでも正常化のテスト
		//文字だけ S-Jis 变换＆エスケープ解除
		makeRecvString( str );
#endif
		if( strlen( str ) <= CHAR_NAME_LEN ){
			strcpy( BBSFor.BBSMeFor[i].Name, str );
		}else{
			strcpy( BBSFor.BBSMeFor[i].Name, "???" );
		}

		StrCount=0;
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );

#ifdef BBS_ESCAPE		// ここでも正常化のテスト
		//文字だけ S-Jis 变换＆エスケープ解除
		makeRecvString( str );
#endif

		strcpy(MessageWork,str);
		StrLineLen=strlen(str);
		if( strlen( str ) <= MESSAGE_BOARD_LINE*MESSAGE_BOARD_LEN ){
			for(k=0;k<3;k++){
				for(l=0;l<MESSAGE_BOARD_STR_COUNT;l++){				
					if(MessageWork[StrCount]==0x0d){
						//改行の时
						StrCount++;
						break;
					}
					BBSFor.BBSMeFor[i].Message[k][l]=MessageWork[StrCount];
					StrCount++;
					if(StrCount>StrLineLen)
						break;
				}
				// 終端记号を入れる
				BBSFor.BBSMeFor[i].Message[k][l] = NULL;
				// 最后の全角文字が分割されている时
				if( GetStrLastByte( BBSFor.BBSMeFor[i].Message[k] ) == 3 ){ 
					BBSFor.BBSMeFor[i].Message[k][l-1] = NULL;
					StrCount--;
				}
			}
		}else{
				//おかしいので消去
				strcpy( BBSFor.BBSMeFor[i].Message[0], "" );
				strcpy( BBSFor.BBSMeFor[i].Message[1], "" );
				strcpy( BBSFor.BBSMeFor[i].Message[2], "" );
		}

	}

}

//ＢＢＳウインドウを关闭际の处理
void MenuWindowBBSClose( void ){

	//serverRequestWinWindowTypeの初期化
	serverRequestWinWindowType=-1;
	// このウィンドウを关闭
	wI->flag |= WIN_INFO_DEL;
	// ウィンドウ关闭音
	play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

}


