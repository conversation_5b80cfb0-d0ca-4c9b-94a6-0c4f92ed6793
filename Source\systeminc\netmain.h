﻿#ifndef _NETMAIN_H_
#define _NETMAIN_H_

#include "language.h"

//-------------------------------------------------------------------------//
// 构造体定义                                                              //
//-------------------------------------------------------------------------//

// ゲームサーバーの管理用构造体
struct gameserver
{
	char ipaddr[128];		// IPアドレス
	char port[64];			// ポート
	char useFlag;			// 使用フラグ
};


//-------------------------------------------------------------------------//
// 定数定义                                                                //
//-------------------------------------------------------------------------//
#define NETBUFSIZ		(1024*16)	// 読み书きバッファサイズ(16kbyte)

#define TIMEOUT			(600*1000)	// 无通信状态になって切断と解釈するまでの时间(10分)
#define NEXT_SEND_TIME	(30*1000)	// 前回の送信からこれだけ时间がたったら何か送信する

#ifdef VERSION_TW
#define MAINSERVER_MAX	60
#endif

#define SUCCESSFULSTR	"successful"        // プロトコルで使う文字定数群
#define FAILEDSTR		"failed"
#define OKSTR           "ok"
#define BUSYSTR         "busy"


#ifndef MULTI_LANG
// エラー信息
#define NET_ERRMSG_SOCKLIBERROR			"网络还没有准备好。"
#define NET_ERRMSG_BADNAME          	"服务器名错误。"
#define NET_ERRMSG_SOCKETERROR      	"TCP生成失败。"
#define NET_ERRMSG_NOTGETADDR			"无法取得服务器IP。(%s)"
#define NET_ERRMSG_NOTCONNECT_S			"无法连接服务器。"
#define NET_ERRMSG_NOTCONNECT			"无法连接服务器。 "
#define NET_ERRMSG_CONNECTTIMEOUT		"连接服务器超时。" 
#define NET_ERRMSG_LOGINTIMEOUT			"登入处理超时。"
#define NET_ERRMSG_CHARLISTTIMEOUT		"取得角色列表超时。"
#define NET_ERRMSG_LOGOUTTIMEOUT		"登出超时。"
#define NET_ERRMSG_LOGINFAIL			"无法登入。"
#define NET_ERRMSG_CREATECHARTIMEOUT	"建立角色超时。"
#define NET_ERRMSG_DELETECHARTIMEOUT    "删除角色超时。"

#define NET_ERRMSG_ERR_CDKEY    		"输入有错。"
#define NET_ERRMSG_ERR_EXPIRE    		"没有足够的点数了。"

//#define NET_ERRMSG_NEWEST_NON    		"无法连接服务器．"
#define NET_ERRMSG_NEWEST_NON    		"检查服务器失败."
#ifdef PUK3_LOGIN_VERCHECK
	#define NET_ERRMSG_CLIENTVER_DIFF		"版本有误，请再次启动游戏。"
#endif

#define NET_ERRMSG_SERVER_BUSY    		"这个服务器相当繁忙。"
#endif


//-------------------------------------------------------------------------//
// 外部变数定义                                                            //
//-------------------------------------------------------------------------//
extern unsigned int sockfd;
extern char networkFlag;
extern char networkServerChooseFlag;

extern char networkWriteBuffer[];
extern char networkReadBufferL[];
extern int networkWriteBufferLen;
extern int networkReadBufferLen;

extern char networkDisconnectFlag;
#ifdef PUK3_CONNECTERROR
	extern int networkDisconnectErrorNum;
#endif

#ifdef VERSION_TW
	extern int MainServerLineNum[];	//主服务器中开启的线路数量
#endif

extern struct gameserver gmsv[];
extern int serverMaxNo;

#ifdef _LOG_MSG
#ifdef PUK3_RECVDATA
	extern char debugLogFileName_base[];
	extern char debugLogFileName[];
#else
extern char debugLogFileName[];
#endif
#endif


//-------------------------------------------------------------------------//
// プロト种类定义                                                        //
//-------------------------------------------------------------------------//
void ipAddressAnalyze( LPSTR );
int getServerMax( void );
int getServerInfo( int, char *, short * );


void networkLoop(void);
void networkMycharWalk( int gx , int gy , char *direction );

int initNet(void);
void cleanupNetwork( void );
int appendReadBuf( char *buf , int size );
int appendWriteBuf( int index , char *buf , int size );
int shiftReadBuf(  int size );
int shiftWriteBuf( int size );
int getLineFromReadBuf( char *output  , int maxlen );
int sendn( unsigned int s , char *buffer , int len );


int getServerInfoByServerName( char *servername , char *hostname , short *port );


#ifdef _DEBUG
int CheckNetErrror( void );
#endif


#endif
