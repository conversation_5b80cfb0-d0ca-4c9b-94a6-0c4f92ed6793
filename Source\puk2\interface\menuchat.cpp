﻿
#define ChatWindowRightMenuWSize 111
#define ChatWindowRightMenuHSize 25

INIT_STR_STRUCT InitStrStructChat={
//  本体		ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MyChatBuffer,8,5,FONT_PRIO_WIN,NEW_CHAT_FONT_KIND,FONT_PAL_WHITE,"",   1,      38*2,  0,     0
};

struct ChatWindowStatusStruct ChatWinST;

//チャットウインドウ
int DrawChatLine;

//チャットウインドウ描画フラグ
int ChatWindowView;

//チャットの新旧モード
int ChatMode;

void SetChatScrollVarTipPosY(void);

//--------------------------------------------------------
//初期化
//--------------------------------------------------------
void InitMenuWindowChatStartExe(void){

	//チャット初期状态。
	ChatWinST.DrawChatLine=0;
	ChatWinST.StockLine=0;
	ChatWinST.WidthSizeChange=0;
	ChatWinST.HeightSizeChange=0;

	//聊天范围
	NowMaxVoice=1;				

	//チャットカラー
	MyChatBuffer.color=FONT_PAL_WHITE;
}

void InitMenuWindowChatInLogin(void){

	ChatWinST.DrawChatLine=0;
	ChatWinST.WidthSizeChange=0;
	ChatWinST.StockLine=0;
	ChatWinST.HeightSizeChange=0;

}

BOOL MenuWindowChat( int mouse )
{

	DIALOG_SWITCH *Dialog;

	// 初期设定
	if( mouse == WIN_INIT ){
		//ダイアログ初期化してフォーカスを取る
		SetInputStr(&InitStrStructChat,wI->wx,wI->wy,3);
		DiarogST.SwAdd=wI->sw[EnumChatDialogInput].Switch;
		Dialog=(DIALOG_SWITCH *)wI->sw[EnumChatDialogInput].Switch;
		Dialog->InpuStrAdd=&MyChatBuffer;
		GetKeyInputFocus( &MyChatBuffer );

		return TRUE;
	}

	//背景描画の设定
	if(TRUE==getUsable3D()){
		wI->sw[EnumChatGraphBackTop].Enabled=TRUE;
		wI->sw[EnumChatGraphBack].Enabled=TRUE;
	}else{
		wI->sw[EnumChatGraphBackTop].Enabled=FALSE;
		wI->sw[EnumChatGraphBack].Enabled=FALSE;
	}

	//サイズの变更
	ChatReSize();

#ifdef PUK3_MAIL_ETC2
	//自分のダイアログを使用しているとき
	SetInputStr(&InitStrStructChat,wI->wx,wI->wy,2);
#else
	//ダイログ表示座标设定
	if(DiarogST.SwAdd==wI->sw[EnumChatDialogInput].Switch){
		//自分のダイアログを使用しているとき
		SetInputStr(&InitStrStructChat,wI->wx,wI->wy,2);
	}
#endif

	return TRUE;
}

BOOL MenuWindowChatDraw( int mouse ){

	if(ChatWindowView==0)
		return TRUE;

	displayMenuWindow();

	return TRUE;

}

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
// ダイアログ
BOOL MenuSwitchChatDialog( int no, unsigned int flag ){

	DIALOG_SWITCH *Dialog;
	BOOL ReturnFlag=FALSE;	

	if(ChatWindowView==0)
		return FALSE;

	if( flag & MENU_MOUSE_LEFT ){
		//ダイアログ初期化してフォーカスを取る
		SetInputStr(&InitStrStructChat,wI->wx,wI->wy,3);
		DiarogST.SwAdd=WindowFlag[MENU_CHAT_WINDOW].wininfo->sw[EnumChatDialogInput].Switch;
		Dialog=(DIALOG_SWITCH *)wI->sw[EnumChatDialogInput].Switch;
		Dialog->InpuStrAdd=&MyChatBuffer;
		GetKeyInputFocus( &MyChatBuffer );
		ReturnFlag=TRUE;
	}
#ifdef PUK3_MAIL_ETC
	// きれいではないけど、とりあえずここで
	if ( pNowInputStr != &MyChatBuffer ){
	#ifdef PUK3_MAIL_ETC2
		MenuChatStockFontBuffInput(&MyChatBuffer);
	#else
		StockFontBuffer2( &MyChatBuffer );
	#endif
	}
#endif

	return ReturnFlag;

}

// チャットメニュー
BOOL MenuSwitchChange( int no, unsigned int flag ){

	//ボタン画像设定
	GRAPHIC_SWITCH *Graph;
	BOOL ReturnFlag=FALSE;	

	if(ChatWindowView==0)
		return FALSE;

	switch(no){
		//縦幅变更
		case EnumChatGraphChangeHSize:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeHSize].Switch)->graNo=GID_ChatChangeHOn;
				strcpy( OneLineInfoStr, MWONELINE_CHAT_LINE );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeHSize].Switch)->graNo=GID_ChatChangeHOff;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				ChatWindowLineUp();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_RIGHTAUTO ){
				ChatWindowLineDown();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;

		//闻こえる范围
		case EnumChatGraphChangeListenSize:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeListenSize].Switch)->graNo=GID_ChatWidth01On+3*(NowMaxVoice-1);
				strcpy( OneLineInfoStr, MWONELINE_CHAT_AREA );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeListenSize].Switch)->graNo=GID_ChatWidth01Off+3*(NowMaxVoice-1);
			}
			if( flag & MENU_MOUSE_LEFT ){
				NowMaxVoice++;
				if( NowMaxVoice > MAX_VOICE )
					NowMaxVoice=1;				
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				// チャットエリア表示时间设定
				ChatAreaDispTime = 180;
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_RIGHT ){
				NowMaxVoice--;
				if( NowMaxVoice < 1 )
					NowMaxVoice=MAX_VOICE;				
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				// チャットエリア表示时间设定
				ChatAreaDispTime = 180;
				ReturnFlag=TRUE;
			}
			break;

		//文字サイズ变更
		case EnumChatGraphChangeFontSize:
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeFontSize].Switch;
			if( flag & MENU_MOUSE_OVER){
				strcpy( OneLineInfoStr, MWONELINE_CHAT_SIZE );
				ReturnFlag=TRUE;
				switch(chatFontSize){
					case 0:
						Graph->graNo=GID_ChatSizeMiddleOver;
						break;
					case 1:
						Graph->graNo=GID_ChatSizeSmallOver;
						break;
					case 2:
						Graph->graNo=GID_ChatSizeLargeOver;
						break;
				}
			}else{
				switch(chatFontSize){
					case 0:
						Graph->graNo=GID_ChatSizeMiddleOff;
						break;
					case 1:
						Graph->graNo=GID_ChatSizeSmallOff;
						break;
					case 2:
						Graph->graNo=GID_ChatSizeLargeOff;
						break;
				}
			}
			if( flag & MENU_MOUSE_LEFT ){
				ChatWindowChangeFontSize();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_RIGHT ){
				chatFontSize++;
				if( chatFontSize > 2 )
					chatFontSize = 0;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;

		//幅变更
		case EnumChatGraphChangeWSize:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeWSize].Switch)->graNo=GID_ChatChangeWOn;
				strcpy( OneLineInfoStr, MWONELINE_CHAT_DISP );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeWSize].Switch)->graNo=GID_ChatChangeWOff;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				ChatWindowStrUp();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_RIGHTAUTO ){
				ChatWindowStrDown();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;	

		//文字カラー变更
		case EnumChatGraphChangeColor:

			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_CHAT_COLOR );
				ReturnFlag=TRUE;
			}
			
			if( flag & MENU_MOUSE_LEFT ){
				MyChatBuffer.color++;
				if( MyChatBuffer.color > 9 )
					MyChatBuffer.color = 0;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_RIGHT ){
				MyChatBuffer.color--;
				if( MyChatBuffer.color < 0 )
					MyChatBuffer.color = 9;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;

		//スクロールバーTop
		case EnumChatGraphScrollBarTop:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBarTopBt].Switch)->graNo=GID_UpButtonOver;
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBarTopBt].Switch)->graNo=GID_UpButtonOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				ChatWinST.DrawChatLine++;

				if(ChatWinST.DrawChatLine>ChatWinST.StockLine){
					ChatWinST.DrawChatLine=ChatWinST.StockLine;
					((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBarTopBt].Switch)->graNo=GID_UpButtonOff;
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}

				//ツマミ描画
				SetChatScrollVarTipPosY();
				
				ReturnFlag=TRUE;
			}
			break;

		//スクロールバーUnder
		case EnumChatGraphScrollBarUnder:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBarUnderBt].Switch)->graNo=GID_DownButtonOver;
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBarUnderBt].Switch)->graNo=GID_DownButtonOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				ChatWinST.DrawChatLine--;
				
				if(ChatWinST.DrawChatLine<0){
					ChatWinST.DrawChatLine=0;
					((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBarUnderBt].Switch)->graNo=GID_DownButtonOff;
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}

				//ツマミ描画
				SetChatScrollVarTipPosY();
				
				ReturnFlag=TRUE;
			}
			break; 

		//スクロールバー
		case EnumChatGraphScrollBar:
			if( flag & MENU_MOUSE_OVER){
				ReturnFlag=TRUE;
			}
			break;

	}

	return ReturnFlag;

}


void MenuChatStockFontBuffInput(INPUT_STR *InputStrAdd){

	int strWidth;

	// 文字の背景を描画
	// ＩＭＥバッファーの文字列の横幅を求める
	strWidth = GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind );

	/* 表示座标の调整 */
	// 表示幅を设定
	InputStrAdd->dispWidth = ChatWinST.WidthSize * NEW_CHAT_FONT_SIZE;

	// カーソルが前に出すぎてる？
	if( InputStrAdd->cursorByte < InputStrAdd->dispByte ){
		InputStrAdd->dispByte = InputStrAdd->cursorByte;
	}else{
		// 表示座标が正常になるまでループ
		while( 1 ){
			int		cursor,length;

			// 表示上のカーソル位置算出
			cursor = InputStrAdd->cursorByte - InputStrAdd->dispByte;
			// 表示位置よりカーソルが前に行かないようにチェック
			if( cursor < 0 ){
				break;
			}
			// 表示位置からカーソルまでの表示幅算出
			length = GetStrWidth( &InputStrAdd->buffer[InputStrAdd->dispByte], InputStrAdd->fontKind, cursor );
			// で、チェック
			if( InputStrAdd->dispWidth >= length ){
				// 范围内に收まっているので问题なし
				break;
			}
			// 表示开始座标を一文字分后ろに
			if( GetCharByte( InputStrAdd->buffer[InputStrAdd->dispByte] ) != 2 ){
				// 全角文字以外は 1Byte 后ろに
				InputStrAdd->dispByte++;
			}else{
				// 全角文字以外は 2Byte 后ろに
				InputStrAdd->dispByte += 2;
			}
		}
	}
	// 表示处理
	StockFontBuffer2( InputStrAdd );

}

void MenuDialogStockFontBuffInput(INPUT_STR *InputStrAdd){

	int strWidth;

	// 文字の背景を描画
	// ＩＭＥバッファーの文字列の横幅を求める
	strWidth = GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind );

	/* 表示座标の调整 */
	// 表示幅を设定
	InputStrAdd->dispWidth = 1024;
	// カーソルが前に出すぎてる？
	if( InputStrAdd->cursorByte < InputStrAdd->dispByte ){
		InputStrAdd->dispByte = InputStrAdd->cursorByte;
	}else{
		// 表示座标が正常になるまでループ
		while( 1 ){
			int		cursor,length;

			// 表示上のカーソル位置算出
			cursor = InputStrAdd->cursorByte - InputStrAdd->dispByte;
			// 表示位置よりカーソルが前に行かないようにチェック
			if( cursor < 0 ){
				break;
			}
			// 表示位置からカーソルまでの表示幅算出
			length = GetStrWidth( &InputStrAdd->buffer[InputStrAdd->dispByte], InputStrAdd->fontKind, cursor );
			// で、チェック
			if( InputStrAdd->dispWidth >= length ){
				// 范围内に收まっているので问题なし
				break;
			}
			// 表示开始座标を一文字分后ろに
			if( GetCharByte( InputStrAdd->buffer[InputStrAdd->dispByte] ) != 2 ){
				// 全角文字以外は 1Byte 后ろに
				InputStrAdd->dispByte++;
			}else{
				// 全角文字以外は 2Byte 后ろに
				InputStrAdd->dispByte += 2;
			}
		}
	}
	// 表示处理
	StockFontBuffer2( InputStrAdd );

}

//チャットバッファに书き込んだ际カウントを足す
void CountAddNewChatStockLine(void){

	ChatWinST.StockLine++;
	if(ChatWinST.StockLine>MAX_CHAT_LINE)
		ChatWinST.StockLine=MAX_CHAT_LINE;

}

//カウントを初期化する
void InitCountNewChatStockLine(void){

	ChatWinST.StockLine=0;
	ChatWinST.DrawChatLine=0;

}

//チャットウインドウ制御
//行增やす
void ChatWindowLineUp(void){
	ChatWinST.HeightSizeChange=1;
}

//行减らす
void ChatWindowLineDown(void){
	ChatWinST.HeightSizeChange=-1;
}

//文字数增やす
void ChatWindowStrUp(void){
	ChatWinST.WidthSizeChange=1;
}

//文字数减らす
void ChatWindowStrDown(void){
	ChatWinST.WidthSizeChange=-1;
}

//フォントサイズ变更
void ChatWindowChangeFontSize(void){
	chatFontSize--;
	if( chatFontSize < 0 )
		chatFontSize = 2;
}

//バッファの内容を描画
void MenuChatStockFontBuff(int Xpos,int Ypos){

	int i,j;
	int x,y; 
	int work;
	int DrawLine;
	int DrawLineCount;
	char splitStr[ INPUT_STR_SIZE + 1 ];
	char splitStrWork[10][INPUT_STR_SIZE + 1 ];	//一つの文字列が１０行を越えないことを前提
	int StrLineLen;								//一行の文字数
	int	StrLineCount;							//一行を分割した际の行数
	int StrCount;					
	int ChatHeightCount;
	int DrawWidthSize;							//书いていいサイズ

	//初期化

	//书いた行数の総サイズ
	ChatHeightCount=0;

	//书いていい横幅ドット数
	if(ChatMode==1){
		//旧チャットモードのときは强制的にサイズMAX
		DrawWidthSize=(NEW_CHAT_MAX_W+7)*FontKind[FONT_KIND_CHAT_S].zenkakuWidth;
	}else{
		DrawWidthSize=(ChatWinST.WidthSize+7)*FontKind[FONT_KIND_CHAT_S].zenkakuWidth;
	}

	// チャットバッファ中での书き始める行の设定
	DrawLine = NowChatLine - 1 - ChatWinST.DrawChatLine;
	//书き込んだラインのカウント
	DrawLineCount=0;
	// リングバッファの最初へ
	if( DrawLine < 0 ) DrawLine = MAX_CHAT_LINE - 1;

	//表示座标（ウインドウ左下）
	x=Xpos;
	y=Ypos-FontKind[ChatBuffer[ DrawLine ].fontKind].zenkakuHeight;

	// 书き終わるまでループ
	while(1){
		// 何か文字があったら
		if( *ChatBuffer[ DrawLine ].buffer != NULL ){

			//一行をワークにコピペ
			strcpy(splitStr,( char *)ChatBuffer[ DrawLine ].buffer);

			// 分割された场合の数算出
			//文字列の総数はいくつか？
			StrLineLen=strlen(splitStr);
			
			//何行ひつようか？
			StrLineCount=GetStrWidth( splitStr, ChatBuffer[ DrawLine ].fontKind)/(DrawWidthSize);
			if(GetStrWidth( splitStr, ChatBuffer[ DrawLine ].fontKind)%DrawWidthSize){
				StrLineCount++;
			}

			StrCount=0;
			for(i=0;i<StrLineCount;i++){
				for(j=0;j<StrLineLen;j++){

					splitStrWork[i][j]=splitStr[StrCount];
					splitStrWork[i][j+1]='\0';
					splitStrWork[i][j+2]='\0';

					work=GetStrWidth(splitStrWork[i],ChatBuffer[ DrawLine ].fontKind);
					if(DrawWidthSize<work){
						// 最后の全角文字が分割されている时
						if( GetStrLastByte( splitStrWork[i] ) == 3 ){ 
							splitStrWork[i][j] = NULL;
							StrCount--;
						}
						i++;
						j=-1;
					}

					StrCount++;
					if(StrCount>StrLineLen)
						break;
				}
				if(StrCount>StrLineLen)
					break;
			}
			
			//文字列をワークの下から顺に描画
			for(i=0;i<StrLineCount;i++){

				//総高さがチャットサイズから越えそうなら終了
				ChatHeightCount+=FontKind[ChatBuffer[ DrawLine ].fontKind].zenkakuHeight;
				if(ChatHeightCount>ChatWinST.HeightSize*FontKind[FONT_KIND_CHAT_S].zenkakuHeight)
					return;
			
				//チャット文字描画
				if(splitStrWork[StrLineCount-i-1][0]!='\0')
					StockFontBuffer( x, y , FONT_PRIO_WIN, ChatBuffer[ DrawLine ].fontKind,
										ChatBuffer[ DrawLine ].color, splitStrWork[StrLineCount-i-1], 0 );

				// 次の座标（一段上）へ
				if(StrLineCount==i+1)
					y -= FontKind[ChatBuffer[ DrawLine-1 ].fontKind].zenkakuHeight;  
				else
					y -= FontKind[ChatBuffer[ DrawLine ].fontKind].zenkakuHeight;  
			}
		}else{
			//文字なければ終了
			return;	
		}
		// 次のバッファへ
		DrawLine--;
		// 书き込んだラインカウント
		DrawLineCount++;
		// リングバッファの最初に返回
		if( DrawLine < 0 ) DrawLine = MAX_CHAT_LINE - 1;
		
		if(DrawLineCount>=ChatWinST.StockLine)
			return;
	}
	

}

//ダイアログをチャットへ移动します
BOOL SetDialogMenuChat(void){

	switch(ChatMode){
	case 0:
		//通常チャット时
		if(WindowFlag[MENU_CHAT_WINDOW].wininfo!=NULL){
			//ダイアログ初期化	
			SetInputStr(&InitStrStructChat,WindowFlag[MENU_CHAT_WINDOW].wininfo->wx,WindowFlag[MENU_CHAT_WINDOW].wininfo->wy,3);
			DiarogST.SwAdd=WindowFlag[MENU_CHAT_WINDOW].wininfo->sw[EnumChatDialogInput].Switch;

			//フォーカスセット	
			GetKeyInputFocus( &MyChatBuffer );

			return TRUE;
		}else{
			return FALSE;
		}
		break;

	case 1:
		//旧版チャット时
		if(WindowFlag[MENU_OLD_CHAT_WINDOW].wininfo!=NULL){
			//ダイアログ初期化	
			SetInputStr(&InitStrStructChat,WindowFlag[MENU_OLD_CHAT_WINDOW].wininfo->wx,WindowFlag[MENU_OLD_CHAT_WINDOW].wininfo->wy,3);
			DiarogST.SwAdd=WindowFlag[MENU_OLD_CHAT_WINDOW].wininfo->sw[EnumOldChatDialogInput].Switch;

			//フォーカスセット	
			GetKeyInputFocus( &MyChatBuffer );

			return TRUE;
		}else{
			return FALSE;
		}
		break;
	}
	
	return FALSE;
}

void ChatReSize(void){

	GRAPHIC_SWITCH *Graph;
	int BarSizeWork;
	int WidthSizeWork;

	//ダイアログ部分----------------------------------------------------------------------
	//縦幅变更要求が来ている时縦幅变更
	ChatWinST.HeightSize+=ChatWinST.HeightSizeChange;		
	if(ChatWinST.HeightSize>NEW_CHAT_MAX_H)
		ChatWinST.HeightSize=NEW_CHAT_MIN_H;
	if(ChatWinST.HeightSize<NEW_CHAT_MIN_H)
		ChatWinST.HeightSize=NEW_CHAT_MAX_H;
	ChatWinST.HeightSizeChange=0;

	//横幅变更要求が来ている时横幅变更
	//入力栏の横幅ドット数は(ChatWinST.WidthSize * NEW_CHAT_FONT_SIZE)です
	ChatWinST.WidthSize+=ChatWinST.WidthSizeChange;		
	if(ChatWinST.WidthSize>NEW_CHAT_MAX_W){
		ChatWinST.WidthSize=NEW_CHAT_MIN_W;
		//横幅增やした时、增加分移动させます
		WindowFlag[MENU_CHAT_WINDOW].wininfo->wx+=NEW_CHAT_FONT_SIZE*(NEW_CHAT_MAX_W+1-NEW_CHAT_MIN_W);
	}
	if(ChatWinST.WidthSize<NEW_CHAT_MIN_W){
		ChatWinST.WidthSize=NEW_CHAT_MAX_W;
		//横幅增やした时、增加分移动させます
		WindowFlag[MENU_CHAT_WINDOW].wininfo->wx-=NEW_CHAT_FONT_SIZE*(NEW_CHAT_MAX_W+1-NEW_CHAT_MIN_W);
	}
	if(ChatWinST.WidthSizeChange==1)
		WindowFlag[MENU_CHAT_WINDOW].wininfo->wx-=NEW_CHAT_FONT_SIZE;
	if(ChatWinST.WidthSizeChange==-1)
		WindowFlag[MENU_CHAT_WINDOW].wininfo->wx+=NEW_CHAT_FONT_SIZE;
	ChatWinST.WidthSizeChange=0;

	WidthSizeWork=NEW_CHAT_FONT_SIZE*(ChatWinST.WidthSize+1);

	//チャットメニュー设定
	//左
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumChatGraphMenuLeft].Switch;
	Graph->w=WidthSizeWork;
	Graph->h=ChatWindowRightMenuHSize;

	//左バック
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumChatGraphDialogBack].Switch;
	Graph->w=WidthSizeWork+20;
	Graph->h=ChatWindowRightMenuHSize;

	//右
	wI->sw[EnumChatGraphMenuRight].ofx=WidthSizeWork;

	//ログ表示部分----------------------------------------------------------------------
	//Back画像设定（下）
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumChatGraphBack].Switch;
	Graph->w=WidthSizeWork+ChatWindowRightMenuWSize;
	Graph->h=NEW_CHAT_FONT_SIZE*(ChatWinST.HeightSize-1);
	wI->sw[EnumChatGraphBack].ofy=-Graph->h;

	//Back画像设定（上）
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumChatGraphBackTop].Switch;
	Graph->w=12*ChatWinST.WidthSize+ChatWindowRightMenuWSize;
	if(ChatWinST.HeightSize==0){
		Graph->h=0;
	}else{
		Graph->h=12;
	}
	wI->sw[EnumChatGraphBackTop].ofy=wI->sw[EnumChatGraphBack].ofy-12;

	//ホイールの范围
	wI->sw[EnumGraphChatScrollWheel].ofx=wI->sw[EnumChatGraphBackTop].ofx;
	wI->sw[EnumGraphChatScrollWheel].ofy=wI->sw[EnumChatGraphBackTop].ofy;
	wI->sw[EnumGraphChatScrollWheel].sx=WidthSizeWork+ChatWindowRightMenuWSize+14;
	wI->sw[EnumGraphChatScrollWheel].sy=NEW_CHAT_FONT_SIZE*(ChatWinST.HeightSize-1)+16;

	//スクロールバー表示部分----------------------------------------------------------------------
	wI->sw[EnumChatBtScrollBar].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14;
	wI->sw[EnumChatBtScrollBar].ofy=-NEW_CHAT_FONT_SIZE*(ChatWinST.HeightSize)+10+(14/2);
	wI->sw[EnumChatBtScrollBar].sx=14;
	wI->sw[EnumChatBtScrollBar].sy=NEW_CHAT_FONT_SIZE*ChatWinST.HeightSize-(11*2)-(14/2)+1;
	
	wI->sw[EnumChatGraphScrollBar].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14;
	wI->sw[EnumChatGraphScrollBar].ofy=-NEW_CHAT_FONT_SIZE*(ChatWinST.HeightSize)+10+(14/2);
	wI->sw[EnumChatGraphScrollBar].sx=14;
	wI->sw[EnumChatGraphScrollBar].sy=NEW_CHAT_FONT_SIZE*ChatWinST.HeightSize-(11*2)-(14/2)+1;
	((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBar].Switch)->w=14;
	((GRAPHIC_SWITCH *)wI->sw[EnumChatGraphScrollBar].Switch)->h=NEW_CHAT_FONT_SIZE*ChatWinST.HeightSize-(11*2)-(14/2)+1;

	wI->sw[EnumChatGraphScrollBarTopBt].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14;
	wI->sw[EnumChatGraphScrollBarTopBt].ofy=-NEW_CHAT_FONT_SIZE*(ChatWinST.HeightSize)+4;
	wI->sw[EnumChatGraphScrollBarUnderBt].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14;
	wI->sw[EnumChatGraphScrollBarUnderBt].ofy=-11-1;

	wI->sw[EnumChatGraphScrollBarTop].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14;
	wI->sw[EnumChatGraphScrollBarTop].ofy=-NEW_CHAT_FONT_SIZE*(ChatWinST.HeightSize);
	wI->sw[EnumChatGraphScrollBarUnder].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14;
	wI->sw[EnumChatGraphScrollBarUnder].ofy=-11;

	//ツマミ座标
	wI->sw[EnumChatGraphScrollBarTip].ofx=WidthSizeWork+ChatWindowRightMenuWSize-14+1;
	SetChatScrollVarTipPosY();

	BarSizeWork=NowChatLine;

	//ドラッグムーブする范围设定
	wI->DragMove.rect[0].w=WidthSizeWork+ChatWindowRightMenuWSize;

	//ウインドウとして判定される范围设定
	wI->sx=12*ChatWinST.WidthSize+ChatWindowRightMenuWSize+12;

	//ダイアログとして判定される范围设定
	wI->sw[EnumChatDialogInput].sx=WidthSizeWork;
	wI->sw[EnumChatDialogInput].sy=ChatWindowRightMenuHSize;	

	//ボタンの位置をウインドウにあわせて调整
	wI->sw[EnumChatGraphChangeWSize].ofx=		wI->sw[EnumChatGraphMenuRight].ofx+ChatSwitch[EnumChatGraphChangeWSize].ofx;
	wI->sw[EnumChatGraphChangeHSize].ofx=		wI->sw[EnumChatGraphMenuRight].ofx+ChatSwitch[EnumChatGraphChangeHSize].ofx;
	wI->sw[EnumChatGraphChangeListenSize].ofx=	wI->sw[EnumChatGraphMenuRight].ofx+ChatSwitch[EnumChatGraphChangeListenSize].ofx;
	wI->sw[EnumChatGraphChangeFontSize].ofx=	wI->sw[EnumChatGraphMenuRight].ofx+ChatSwitch[EnumChatGraphChangeFontSize].ofx;
	wI->sw[EnumChatGraphChangeColor].ofx=		wI->sw[EnumChatGraphMenuRight].ofx+ChatSwitch[EnumChatGraphChangeColor].ofx;
	//フォントカラー表示
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumChatGraphChangeColor].Switch;
	Graph->graNo=GID_ChatColor00+MyChatBuffer.color;

}

int GetChatFontWidthSize(void){
	return ChatWinST.WidthSize*2;
}


//スクロールバー
BOOL MenuSwitchChatScrollVar(int no, unsigned int flag ){

	BOOL ReturnFlag;	

	ReturnFlag=MenuSwitchScrollBarV(no,flag);

	if( flag & MENU_MOUSE_LEFTHOLD ){
		//sugi
		ChatWinST.DrawChatLine=ChatWinST.StockLine-ScrollVPointToNum(&wI->sw[EnumChatBtScrollBar],ChatWinST.StockLine);
	}	

	return ReturnFlag;
	
}

//スクロールバーのＹ座标セット
void SetChatScrollVarTipPosY(void){

	int WidthSizeWork;
	int BarSizeWork;
	WINDOW_INFO *WinInfoWork;

	WinInfoWork=getWindowBuffAddFromType(MENU_CHAT_WINDOW);
	
	if(WinInfoWork==NULL)
		return;

	NumToScrollVMove(&WinInfoWork->sw[EnumChatBtScrollBar],ChatWinST.StockLine,ChatWinST.DrawChatLine);

	WidthSizeWork=NEW_CHAT_FONT_SIZE*(ChatWinST.WidthSize+1);
	if(ChatWinST.StockLine==0){
		WinInfoWork->sw[EnumChatGraphScrollBarTip].ofy=WinInfoWork->sw[EnumChatGraphScrollBarUnder].ofy-14;
	}else{
		BarSizeWork=WinInfoWork->sw[EnumChatGraphScrollBarUnder].ofy-14;
		BarSizeWork=BarSizeWork-(int)((float)((float)ChatWinST.DrawChatLine/(float)ChatWinST.StockLine)*(WinInfoWork->sw[EnumChatGraphScrollBar].sy-14));
		WinInfoWork->sw[EnumChatGraphScrollBarTip].ofy=BarSizeWork;
	}

}

BOOL MenuSwitchChatScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	//　チャットが一番上のときだけスクロール可能
	if(!(wI==WindowBuff[0])){
		ReturnFlag=FALSE;
		return ReturnFlag;
	}

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		if(ChatWinST.StockLine==0){
			if(mouse.wheel!=0){
				wI->sw[EnumChatGraphScrollBarTip].ofy=wI->sw[EnumChatGraphScrollBarUnder].ofy-14;
			}
		}else{
			//	变更后の值=WheelToMove(スイッチアドレス,现在值,マックス值,mouse.wheel);
			// スクロールバー縦ホイール移动
			ChatWinST.DrawChatLine = ChatWinST.StockLine-WheelToMove( &wI->sw[EnumChatBtScrollBar],ChatWinST.StockLine-ChatWinST.DrawChatLine, ChatWinST.StockLine, mouse.wheel );
		}
	}

	return ReturnFlag;
}

//----------------------------------------------------------------------------------------------------
//旧版チャット
//----------------------------------------------------------------------------------------------------

void OldChatReSize(void);

BOOL MenuWindowOldChat( int mouse )
{

	DIALOG_SWITCH *Dialog;

	// 初期设定
	if( mouse == WIN_INIT ){
		//ダイアログ初期化してフォーカスを取る
		SetInputStr(&InitStrStructChat,wI->wx,wI->wy,3);
		DiarogST.SwAdd=wI->sw[EnumOldChatDialogInput].Switch;
		Dialog=(DIALOG_SWITCH *)wI->sw[EnumOldChatDialogInput].Switch;
		Dialog->InpuStrAdd=&MyChatBuffer;
		GetKeyInputFocus( &MyChatBuffer );

		return TRUE;
	}

#ifdef PUK3_MAIL_ETC2
	//自分のダイアログを使用しているとき
	SetInputStr(&InitStrStructChat,wI->wx,wI->wy,2);
#else
	//ダイログ表示座标设定
	if(DiarogST.SwAdd==wI->sw[EnumOldChatDialogInput].Switch){
		//自分のダイアログを使用しているとき
		SetInputStr(&InitStrStructChat,wI->wx,wI->wy,2);
	}
#endif

	// 初期设定
	if( mouse == WIN_INIT ){

		return TRUE;
	}

	OldChatReSize();
	
	return TRUE;
}

BOOL MenuWindowOldChatDraw( int mouse ){

	if(ChatWindowView==0)
		return TRUE;

	displayMenuWindow();

	return TRUE;

}

void OldChatReSize(void){

	//ダイアログ部分----------------------------------------------------------------------
	ChatWinST.HeightSize+=ChatWinST.HeightSizeChange;		
	if(ChatWinST.HeightSize>NEW_CHAT_MAX_H)
		ChatWinST.HeightSize=NEW_CHAT_MIN_H;
	if(ChatWinST.HeightSize<NEW_CHAT_MIN_H)
		ChatWinST.HeightSize=NEW_CHAT_MAX_H;
	ChatWinST.HeightSizeChange=0;

	//横幅变更要求が来ている时このモードのときはムシ
	ChatWinST.WidthSizeChange=0;

}

void ChangeChatMode(void){
	
	if(ChatMode==0){
		//旧チャットへ变更
		if(WindowFlag[MENU_CHAT_WINDOW].wininfo==NULL)
			return;

		WindowFlag[MENU_CHAT_WINDOW].wininfo->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_OLD_CHAT_WINDOW );
		ChatMode=1;

	}else{
		//新チャットへ变更
		if(WindowFlag[MENU_OLD_CHAT_WINDOW].wininfo==NULL)
			return;

		WindowFlag[MENU_OLD_CHAT_WINDOW].wininfo->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_CHAT_WINDOW );
		ChatMode=0;
	}

	// フラグＯＦＦ
	ChatHideFlag = FALSE;

}