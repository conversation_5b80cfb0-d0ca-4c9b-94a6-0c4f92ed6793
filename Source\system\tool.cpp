﻿#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <math.h>
/* WIN32_LEAN_AND_MEANをdefineするとWINDOWS.Hのうち
のあまり使われない部分がインクルードされなくなって
ちょっとだけコンパイルがはやくなる */
#define WIN32_LEAN_AND_MEAN 
#include <windows.h>

#include "../systeminc/tool.h"


static int copyStringUntilDelim( unsigned char *, char delim, int, unsigned char * );


// その文字列のワード位置までポインタをずらす
int wordchk( char **pp )
{
	char *p = *pp;

	while( *p != 0 )
	{
		switch( *p )
		{
			case '\t':
				p++;
				break;

			case ' ':
				p++;
				break;

			default:
				*pp = p;
				return 1;
		}
	}

	return 0;
}


// その文字列から１ワード分とって更にポインタを移动
int getword( char **pp, char *q )
{
	int i=0;
	char *p = *pp;

	wordchk( &p );

	for( i = 0; *p != ' ' && *p != '\t' && *p != '\0'; p++ , q++ , i++ )
	{
		*q = *p;
	}

	*q = '\0';
	*pp = p;

	return i;
}


#if 0
// 一行ロードルーチン
void freadline( char *ptbuf, FILE *fp )
{
	char buf;
	int i;

	for( i = 0; ; i++ )
	{
		if( feof( fp ) != 0 )
		{
			*ptbuf--;
			*ptbuf = '\0';
			break;
		}

		fread( &buf, 1, sizeof( unsigned char ), fp );
		if( buf == ' ' )
		{
			*ptbuf++ = buf;
		}else if (buf == '\t'){
			*ptbuf++ = buf;
		}else if (buf != 0x0d && buf != 0x0a){
			*ptbuf++ = buf;
		}else{
			*ptbuf++ = '\0';
			break;
		}
	}
	while ( feof(fp ) == 0 ){
		fread( &buf ,1 , sizeof(unsigned char) , fp );
		if (buf != 0x0d && buf != 0x0a ){
			fseek( fp, -1, SEEK_CUR);
			break;
		}
	}
}
#endif


// ｑ文字列と同じならその文字数を返し、ｐを动かす
int strstr2( char **pp , char *q )
{
	char *p = *pp;
	int i;

	wordchk( &p );

	for( i = 1; *p++ == *q++; i++ )
	{
		if( *q == 0 )
		{
			*pp = p;
			return i;
		}
	}

	return 0;
}


// その文字列から１ワード分とってそれを数值として返す。
int strint2( char **pp )
{
	char moji[1024] = "";
	char *p = *pp;

	getword( &p , moji );
	*pp = p;

	return atoi(moji);
}


// その文字列から１ワード分とってそれをlong型数值として返す。
long strlong2( char **pp )
{
	char moji[1024] = "";
	char *p = *pp;

	getword( &p , moji );
	*pp = p;

	return atol(moji);
}




/* by nakamura */
/*********************************************************************************
  SJISの文字列をEUCに变换する。
  buffer: SJISの文字列。このバッファを直接书き换えるので、文字列定数へのポインタ
  をいれたりしてはいけない。
**********************************************************************************/
void sjisStringToEucString( char *buffer )
{
    char tmp[4];

	while( *buffer != '\0' )
	{
		if( sjis2euc( (unsigned char *)buffer, (unsigned char *)tmp ) )
		{
			buffer[0] = tmp[0];
			buffer[1] = tmp[1];
			buffer += 2;
		}
		else
		{
			buffer += 1;
		}
	}
}


/*****************************************************************************
  SJISの1文字をEUCに变换する。
  sjis: SJISの文字
  euc:  EUCに变换された文字
  modify sjis code to chinese by LitChi 08/22/2011 0:48
  返り值：SJISでない文字(ASCII等)が渡されるとFALSE,SJISだったらTRUE.
*****************************************************************************/
int sjis2euc( unsigned char *sjis, unsigned char *euc )
{
	int word;

	euc[0] = sjis[0];
	euc[1] = sjis[1];

	if( !(sjis[0] & 0x80) ) {
		return FALSE;	// eucではなかった
	}

	// 全角のとき
	if( ( unsigned char )0x81 <= sjis[0] && sjis[0] <= ( unsigned char )0xfe ) {
		return TRUE;
	}
	else {
		return FALSE;	// 半角です
	}
//	if( !( (0x81 <= sjis[0] && sjis[0] <= 0x9f) || ( 0xc0 <= sjis[0] ) ) )
//		return FALSE;	// sjis ではなかった

#if 0
	/* シフトJISコードをEUCコードに变换   */
	/*（倪永茂氏のコードをお借りしました）*/

    unsigned int hib, lob;
    
    //hib = (sjis >> 8) & 0xff;
    hib = sjis[ 1 ];
    lob = sjis[ 0 ];
    
    hib -= (hib <= 0x9f) ? 0x71 : 0xb1;
    euc[ 0 ] = (hib << 1) + 1;
    if (lob >= 0x9e)
    {
        lob -= 0x7e;
        hib++;
    }
    else if (lob > 0x7f)
        lob -= 0x20;
    else
        lob -= 0x1f;
    
    euc[ 1 ] = lob;

    euc[ 0 ] |= 0x80;
    euc[ 1 ] |= 0x80;

#else
	return TRUE;

	word = sjis[0];
	if( word >= 0xa0 ) word-= 0x40;
	euc[0] = ((word-0x81) << 1) + 0x21;
	
	word = sjis[1];
	if( word>=0x7f )
		word--;

	word = word - 0x40 + 0x21;
	if( word >= 0x7f )
	{
		word = word - 0x7f + 0x21;
		euc[0]++;
	}

    euc[1] = word;
    euc[0] |= 0x80;
    euc[1] |= 0x80;

#endif

    return TRUE;
}


/*********************************************************************************
  EUCの文字列をSJISに变换する。
  buffer: EUCの文字列。このバッファを直接书き换えるので、文字列定数をいれたり
  してはいけない。
**********************************************************************************/
void eucStringToSjisString( char *buffer )
{
	while( *buffer != '\0' )
	{
		if( euc2sjis( (unsigned char *)buffer ) )
		{
			buffer +=2;
		}
		else
		{
			buffer +=1;
		}
	}
}

/*****************************************************************************
  EUCの1文字をSJISに变换する。
  euc: EUCの文字
  sjis:  SJISに变换された文字
  返り值：EUCでない文字(ASCII等)が渡されるとFALSE,EUCだったらTRUE.
*****************************************************************************/
int euc2sjis( unsigned char *c )
{
	if( !(c[0] & 0x80) )
		return FALSE;	// eucではなかった
	
	if( ( unsigned char )0x81 <= c[0] && c[0] <= ( unsigned char )0xfe ){
		return TRUE;
	}
	else {
		return FALSE;	// 半角です
	}

	//c[1] -= ((c[0] & 1) == 0) ? 2 : ( (c[1] < 0xe0) ? 0x61 : 0x60 );
	//c[0]  = ( (c[0] < 0xdf) ? c[0] + 0x61 : c[0] + 0xe1 ) >> 1;
	//c[0] |= 0x80;
	
	return TRUE;
}


// 区切り文字を探しその次のポインタを返す
//   戾り值：NULL以外 ... 区切り文字の次のポインタ
//           NULL     ... 文字列の終端に来た
unsigned char *searchDelimPoint( unsigned char *src, unsigned char delim )
{
	unsigned char *pt = src;

	while( 1 )
	{
		if( *pt == '\0' )
			return (unsigned char *)0;

		if( *pt < 0x80 )
		{
			// 1bayte文字の处理
			if( *pt == delim )
			{
				// 区切り文字あったのでその次のポインタを返す
				pt++;
				return pt;
			}
			pt++;
		}
		else
		{
			// 2byte文字の处理
			pt++;
			if( *pt == '\0' )
				return (unsigned char *)0;
			pt++;
		}
	}
}


/*
   おおきい文字列からトークンをとりだす。そのときに指定したデリミタ（１文字）を
   つかうことができる。

  char *src : もとの文字列
  char delim : でりみた
  int count : なんこめか。１なら最初のとーくん
  int maxlen : out にとりだす最大文字列ながさ
  char *out : 出力


  戾り值  0：デリミタで終わった
          1:文字列終端で終わった
*/
int getStringToken( char *src, char delim, int count, int maxlen, char *out )
{
	int c = 1;
	int i;
	unsigned char *pt;

	pt = (unsigned char *)src;
	for( i = 0; i < count-1; i++ )
	{
		if( pt == (unsigned char *)0 )
			break;

		pt = searchDelimPoint( pt, delim );
	}

	if( pt == (unsigned char *)0 )
	{
		out[0] = '\0';
		return 1;
	}

	return copyStringUntilDelim( pt, delim, maxlen, (unsigned char *)out );
}


/*
  文字列から文字列へ、文字列がおわるかデリミタがくるまでこぴーする

  char *src : 元の文字列
  char delim : デリミタ
  int maxlen : 最大コピーながさ
  char *out : 出力

  戾り值  0：デリミタで終わった
          1:文字列終端で終わった
*/
static int copyStringUntilDelim( unsigned char *src, char delim,
	int maxlen, unsigned char *out )
{
	int i;

	for( i = 0; i < maxlen; i++ )
	{
		if( src[i] < 0x80 )
		{
			// 1byte文字の处理

			if( src[i] == delim )
			{
				// 区切り文字なので終わる
				out[i] = '\0';
				return 0;
			}

			// ただの文字はコピー
			out[i] = src[i];

			// 終端文字でも終わる
			if( out[i] == '\0' )
				return 1;
		}
		else
		{
			// 2byte文字の处理

			// ただの文字はコピー
			out[i] = src[i];

			i++;
			if( i >= maxlen )	// バッファが一杯なので終わる
				break;

			// ただの文字はコピー
			out[i] = src[i];

			// 終端文字でも終わる（こんなことはありえないが）
			if( out[i] == '\0' )
				return 1;
		}
	}

	out[i] = '\0';

	return 1;
}


/*
  整数のトークンをint值でとりだす。

  char *src : もとの文字列
  char delim: デリミタになる文字列
  int count :何个目のトークンか １なら最初のとーくん

  return value : 值

*/
int getIntegerToken( char *src, char delim, int count )
{
	char s[128];

	getStringToken( src, delim, count, sizeof( s )-1, s );

	if( s[0] == '\0' )
		return -1;

	return atoi( s );
}


/*
  整数のトークンをint值でとりだす。

  char *src : もとの文字列
  char delim: デリミタになる文字列
  int count : 何个目のトークンか １なら最初のとーくん
  int *val:   值を返す变数へのポインタ

  return value : 0 ... デリミタで終わった
                 1 ... 終端で終わった
                 2 ... データ无い

*/
int getIntegerToken( char *src, char delim, int count, int *val )
{
	char s[128];
	int ret;

	ret = getStringToken( src, delim, count, sizeof( s )-1, s );

	if( s[0] == '\0' )
		return 2;

	*val = atoi( s );

	return ret;
}


/*
  doubleのトークンをとりだす。
  char *src : もとの文字列
  char delim: デリミタになる文字列
  int count :何个目のトークンか １なら最初のとーくん

  return value: 值
*/
double getDoubleToken( char *src, char delim, int count )
{
	char s[128];

	getStringToken( src, delim, count, sizeof( s )-1, s );

	return strtod( s , NULL );
}


/*
  chopする(UNIX形式の文字列)

  char *src : 元の文字列。  このバッファを直接编集するから注意。

*/
void chop( char *src )
{
	int i;

	for( i = 0; ; i++ )
	{
		if( src[i] == 0 )
			break;
		if( src[i] == '\n' && src[i+1] == '\0' )
		{
			src[i] = '\0';
			break;
		}
	}
}


/*
intの配列をシフトするのだ

  int *a : 配列のアドレス
  int siz :　配列のサイズ
  int count : いくつぶんシフトするか
　配列の最后には０をうめるぞ
*/
void shiftIntArray( int *a, int siz, int count )
{
	int i;

	for( i = 0; i < siz - count; i++ )
	{
		a[i] = a[i+count];
	}
	for( i = siz - count; i <siz; i++ )
	{
		a[i] = 0;
	}
}


// 62进文字列をintに变换する
// 0-9,a-z(10-35),A-Z(36-61)
int a62toi( char *a )
{
	int ret = 0;
	int fugo = 1;

	while( *a != NULL )
	{
		ret *= 62;
		if( '0' <= (*a) && (*a) <= '9' )
			ret += (*a)-'0';
		else
		if( 'a' <= (*a) && (*a) <= 'z' )
			ret += (*a)-'a'+10;
		else
		if( 'A' <= (*a) && (*a) <= 'Z' )
			ret += (*a)-'A'+36;
		else
		if( *a == '-' )
			fugo = -1;
		else
			return 0;
		a++;
	}
	return ret*fugo;
}


/*
  整数のトークンをint值でとりだす。

  char *src : もとの文字列
  char delim: デリミタになる文字列
  int count :何个目のトークンか １なら最初のとーくん

  return value : 值

*/
int getInteger62Token( char *src, char delim, int count )
{
	char  s[128];

	getStringToken( src, delim, count, sizeof( s )-1, s );
	if( s[0] == '\0' )
		return -1;

    return a62toi( s );
}


/*
  小文字、大文字に关系なく文字列を比较する。

  char *s1 : 文字列１
  char *s2 : 文字列２
  int len : 最大比较文字数。


*/
int strncmpi( char *s1, char *s2, int len )
{
	int i;
	int c1, c2;

	for( i = 0; i < len; i++ )
	{
		if( s1[i] == '\0' || s2[i] == '\0' )
			return 0;

		c1 = tolower( s1[i] );
		c2 = tolower( s2[i] );

		if( c1 != c2 )
			return 1;
	}

	return 0;
}




RECT intToRect( int left, int top, int right, int bottom )
{
	RECT rc = { left, top, right, bottom };

	return rc;
}




/*****************************************************************
  スペースだけの文字列だったら1、他の文字を含んでいたら
  0を返す。SJIS用。
******************************************************************/
int isOnlySpaceChars( char *data )
{
	int i = 0;
	int returnflag = 0;

	while( data[i] != '\0' )
	{
		returnflag = 0;
		if( (unsigned char)data[i] == ' ' )
			returnflag = 1;
		if( IsDBCSLeadByte( data[i] ) )
		{
			if( (unsigned char)data[i] == 0x81
			 && (unsigned char)data[i+1] == 0x40 )
			{
				returnflag = 1;
			}
			i++;
		}
		if( returnflag == 0 )
			return 0;
		i++;
	}

	if( i == 0 )
		return 0;

	return returnflag;
}


/****************************
  文字列をバッファに插入する。
  buffer: バッファ
  string: 插入する文字列
  whereToinsert: どこに插入するか
****************************/
void insertString( char *buffer, char *string, int whereToInsert )
{
	int stringLength, bufferLength, i;

	stringLength = strlen( string );
	bufferLength = strlen( buffer );

	for( i = 0; i <= bufferLength - whereToInsert; i++ )
	{
		buffer[bufferLength+stringLength-i] = buffer[bufferLength-i];
	}
	for( i = 0; i < stringLength; i++ )
	{
		buffer[whereToInsert+i] = string[i];
	}
}


/****************************
  文字をバッファに插入する。
  buffer: バッファ
  character: 插入する文字列
  whereToinsert: どこに插入するか
****************************/
void insertChar( char *buffer, char character, int whereToInsert )
{
	int bufferLength, i;

	bufferLength = strlen( buffer );

	for( i = 0; i <= bufferLength - whereToInsert; i++ )
	{
		buffer[bufferLength+1-i] = buffer[bufferLength-i];
	}
	buffer[whereToInsert] = character;
}


/*************************************************************
えすけーぷ（サーバからのばっちもん）
*************************************************************/
typedef struct tagEscapeChar
{
	char escapechar;
    char escapedchar;
} EscapeChar;

static EscapeChar escapeChar[] =
{
	{ '\n',   'n' },
	{ ',',    'c' },
	{ '|',    'z' },
	{ '\\',   'y' },
};

// エスケープ文字を通常文字に戾す
//   戾り值：通常文字(エスケープ文字じゃない时は元の文字を返す)
char makeCharFromEscaped( char c )
{
	int i;

	for( i = 0; i < sizeof( escapeChar )/sizeof( escapeChar[0] ); i++ )
	{
		if( escapeChar[i].escapedchar == c )
		{
			c = escapeChar[i].escapechar;
			break;
		}
	}

	return c;
}



/*----------------------------------------
 * makeEscapeStringで作成された文字列を元に戾す
 * 引数
 *  src             char*       元になる文字列。これが书き替る。
 * 返り值
 *  src    を返す。(关数を入れこにしやすいように)
 ----------------------------------------*/
char *makeStringFromEscaped( char *src )
{

	int i;
	int srclen = strlen( src );
	int searchindex = 0;


	for( i = 0; i < srclen; i ++ ){
		if( src[i] == '\\' ){
			// 次の文字に行く
			i++;
			src[searchindex++] = makeCharFromEscaped( src[i] );
		}else{
			src[searchindex++] = src[i];
		}
	}

	src[searchindex] = '\0';

	return src;

}


/*----------------------------------------
 * エスケープする。
 * 引数
 *  src             char*       元になる文字列
 *  dest            char*       エスケープされる文字列
 *  sizeofdest      int         dest の サイズ
 * 返り值
 *  dest    を返す。(关数を入れこにしやすいように)
 ----------------------------------------*/
#if 1
char *makeEscapeString( unsigned char *src, unsigned char *dest, int sizeofdest )
{
	int i, j;
	int srclen = strlen( (char *)src );
	int destindex = 0;
	BOOL dirty;
	char escapechar;

	for( i = 0; i < srclen; i ++ )
	{
		escapechar = '\0';
		dirty = FALSE;

		if( destindex + 1 >= sizeofdest )
			break;	// '\0'分が足りないのでここで終り

		if( src[i] < 0x80 )
		{
			// １バイト文字だけエスケープする
			for( j = 0; j < sizeof( escapeChar )/sizeof( escapeChar[0] ); j++ )
			{
				if( src[i] == escapeChar[j].escapechar )
				{
					dirty = TRUE;
					escapechar= escapeChar[j].escapedchar;
					break;
        	    }
			}

			if( dirty == TRUE )
			{
        	    // エスケープする文字だ
				if( destindex + 2 < sizeofdest )
				{
					// +2 というのは、 '\\' と 'n'の事だ
					// 残り文字は十分だ
					dest[destindex] = '\\';
					dest[destindex+1] = escapechar;
					destindex+=2;
					continue;	//  次の文字に进む
				}
				else
				{
					// 残りバッファが不足している
					dest[destindex] = '\0';
					return (char *)dest;
				}
			}
			else
			{
				dest[destindex] = src[i];
				destindex++;
			}
		}
		else
		{
			// ２バイト文字はエスケープしない
			if( destindex + 2 < sizeofdest )
			{
				dest[destindex] = src[i];
				destindex++;
				i++;
				dest[destindex] = src[i];
				destindex++;
			}
			else
			{
				// 残りバッファが不足している
				dest[destindex] = '\0';
				return (char *)dest;
			}
		}
	}

	dest[destindex] = '\0';

	return (char *)dest;
}

#else

char *makeEscapeString( char *src, char *dest, int sizeofdest )
{
	int i, j;
	int srclen = strlen( src );
	int destindex = 0;
	BOOL dirty;
	char escapechar;

	for( i = 0; i < srclen; i ++ )
	{
		escapechar = '\0';
		dirty = FALSE;

		if( destindex + 1 >= sizeofdest )
			break;	// '\0'分が足りないのでここで終り

		for( j = 0; j < sizeof( escapeChar )/sizeof( escapeChar[0] ); j++ )
		{
			if( src[i] == escapeChar[j].escapechar )
			{
				dirty = TRUE;
				escapechar= escapeChar[j].escapedchar;
				break;
            }
		}

		if( dirty == TRUE )
		{
            // エスケープする文字だ
			if( destindex + 2 < sizeofdest )
			{
				// +2 というのは、 '\\' と 'n'の事だ
				// 残り文字は十分だ
				dest[destindex] = '\\';
				dest[destindex+1] = escapechar;
				destindex+=2;
				continue;	//  次の文字に进む
			}
			else
			{
				// 残りバッファが不足している
				dest[destindex] = '\0';
				return dest;
			}
		}
		else
		{
			dest[destindex] = src[i];
			destindex++;
		}
	}

	dest[destindex] = '\0';

	return dest;
}

#endif

/*
	sjisをeucにしてエスケープかます
	char *src :もと
	char *dest : 出力
	int sizeofdest : 出力の最大サイズ
*/
void makeSendString( char *src, char *dest, int sizeofdest )
{
	sjisStringToEucString( src );
	makeEscapeString( (unsigned char *)src, (unsigned char *)dest, sizeofdest );
}


/*
	eucをsjisにしてエスケープ解除
	char *src :もと
	char *srcが直接书き换わる。
*/
char *makeRecvString( char *src )
{
	makeStringFromEscaped( src );
	eucStringToSjisString( src );

	return src;
}




/***************************************************************
	暗号化ルーチン关系 by Jun
***************************************************************/
/*
char *src	生のデータadress
int srclen	生のデータsrcの有效长さ
int keystring	エンコードするためのキー
char *encoded	エンコードされた文字列を格纳するメモリのアドレス
int *encodedlen	エンコードされた文字列の长さをいれるアドレス 
int maxencodedlen 最大长さはきめられている
*/
void jEncode( char *src, int srclen, int key,
	char *encoded, int *encodedlen, int maxencodedlen )
{
	char sum = 0;
	int i;

	if( srclen+1 > maxencodedlen ){
		// エンコード后は长さが 1 のびる。
		// マックスをこえたらエンコードせず单にコピーするだけ
		*encodedlen = maxencodedlen;
		for( i = 0; i < (*encodedlen); i++ ){
			encoded[i] = src[i];
		}
	}

	if( srclen+1 <= maxencodedlen ){
		// マックスこえないとき
		*encodedlen = srclen + 1;
		for( i = 0; i < srclen; i++ ){
			sum = sum + src[i];
			// 先头から7の倍数个目か2の倍数个目のところはビット反転
			if( ((key%7) == (i%5)) || ((key%2) == (i%2)) ){
				src[i] = ~src[i];
			}
		}
		for( i = 0; i < (*encodedlen); i++ ){
			if( abs( (key%srclen) ) > i ){
				// てきとうにたしてる
				encoded[i] = src[i] + sum*((i*i)%3);	
			}else if( abs( (key%srclen) ) == i ){
				// key%srclenの位置に全ビットをたしたものを置く
				encoded[i] = sum;
			}else if( abs( (key%srclen) ) < i ){
				// てきとうにたしてる
				encoded[i] = src[i-1] + sum*((i*i)%7);	
			}
		}
	}
}


/*
char *src	暗号化されたデータ
int srclen	暗号化されているデータの有效长さ
int key		元のデータのと同じキー
char *decoded	デコードされた文字列。出力
int *decodedlen	デコードされた文字列の长さ
*/
void jDecode( char *src, int srclen, int key, char *decoded, int *decodedlen )
{
	char sum;
	int i;

	*decodedlen = srclen - 1;
	sum = src[abs( key%(*decodedlen) )];

	for( i=0; i < srclen; i++ ){
		if( abs( (key%(*decodedlen)) ) > i ){
			decoded[i] = src[i] - sum*((i*i)%3);
		}

		if( abs( (key%(*decodedlen)) ) < i ){
			decoded[i-1] = src[i] - sum*((i*i)%7);
		}
	}

	for( i = 0; i < (*decodedlen); i++ ){
		if( ((key%7) == (i%5)) || ((key%2) == (i%2)) ){
			decoded[i] = ~decoded[i];
		}
	}
}


/*----------------------------------------
 * src から dels で指定した文字を取り除く
 * エスケープはない
 * 引数
 *  src    变更される文字列
 *  char*  削除する文字(文字列で指定可)
 * 返り值
 *  なし
 ----------------------------------------*/
void deleteCharFromStringNoEscape( char* src , char* dels )
{
    int index=0;    /* できあがりの文字列での index */
    int delength;   /* dels の长さを设定する(少しは速度アップの为 */
    int i=0,j;/* i は src をループする变数 j は dels をループする变数 */

    delength= strlen( dels );

    while( src[i] != '\0' ){
        for( j = 0 ; j < delength ; j ++ ){
            if( src[i] == dels[j] )
                /*
                 * 削除する文字だ。よって i に 1 をたす
                 */
                goto incrementi;
        }
        /*
         * 消す物に指定されていないのでコピーする。
         */
        src[index++] =  src[i];
    incrementi:
        i++;
    }

    src[index] = '\0';
}

#ifdef VERSION_TW
//子字符删除函数
void deleteSubString(char* src, const char* sub)
{
	if (src == NULL || sub == NULL)
		return;

	char* srctmp, * strpos;
	const char* subtmp = sub;

	strpos = src;
	while ((srctmp = strstr(src, sub)) != NULL) {
		src = srctmp;
		//如果源字符串和字串当前位置内容相同且都未处理结束
		while (*srctmp == *subtmp && srctmp != '\0' && subtmp != '\0')
		{
			++srctmp;
			++subtmp;
		}
		while (*srctmp != '\0')
			*src++ = *srctmp++;
		*src = '\0';
		src = strpos;
		subtmp = sub;
	}
	return;
}
#endif