﻿#ifndef _ACCOUNT_H_
#define _ACCOUNT_H_

//ＰＵＫ３アカウント关系ヘッダ

#define PUK3_ACCOUNT_MAX 10

typedef struct {

	//アカウントまでのパス名
	char accountPath[PUK3_ACCOUNT_MAX][256];

	//読み込んだアカウント本体前诘めしたもの
	SAVE_DATA saveDataTmp[PUK3_ACCOUNT_MAX];
	//使えるアカウントを诘めたテーブル
	BOOL use[PUK3_ACCOUNT_MAX];
	//存在するアカウントの数
	int usecount;
	//タブの数
	int tabMax;
	//新しく作成する候补のナンバ
	int NewTabNum;
	//使えるアカウントを前诘めしたもの
	int table[PUK3_ACCOUNT_MAX];
	//现在使用しているアカンウトナンバ
	int myAccountNum;

}PUK3ACCOUNTSYSTEM;

extern PUK3ACCOUNTSYSTEM Puk3AccountSystem;

BOOL makePuk3AccountSystemDir(void);
void setIdPuk3( char * ,int );
char *getIdPuk3( char * ,int);
void setPasswordPuk3( char * ,int );
char *getPasswordPuk3( char * ,int);
void setCdkeyPuk3( char *  ,int);
char *getCdkeyPuk3( char * ,int);
BOOL loadSaveFilePuk3Seq(void);
void saveTmpStatePuk3(void);
void setFromTmpSaveToSave(void);
void setFromSaveToTmpSave(void);
BOOL makeSaveFilePuk3(char *fileName);
BOOL loadSaveFilePuk3One(int index);
void deleteTmpSaveFilePuk3(int);
void InitSaveFileStatus(int num);
int getNouseAccountPuk3(void);

#endif