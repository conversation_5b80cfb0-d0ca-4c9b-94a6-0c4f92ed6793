﻿//#include "util.h"
#include "../systeminc/main.h"
#include "../systeminc/system.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/tool.h"

#include "../systeminc/pc.h"
#include "../systeminc/t_music.h"
#include "../systeminc/chat.h"
#include "../systeminc/menu.h"
#include "../systeminc/font.h"
#include "../systeminc/character.h"


#include "../systeminc/savedata.h"

#ifdef PUK3_ACCOUNT
#include "../puk3/account/account.h"
#endif

extern short RasterNoWaitMode;

/*
// セーブデータ构造体
typedef struct
{
	// バージョン１ -----------------------------------------------------------//
	unsigned int  version;					// セーブデータバージョン		(    4 byte )
	unsigned char id[51];					// ＩＤ番号                     (   51 byte )
	unsigned char password[51];				// パスワード					(   51 byte )
	unsigned char cdKey[51];				// CDキー						(   51 byte )
	unsigned char stereoSetting;			// 立体声??单声道の设定		(    1 byte )
	unsigned char seVol;					// ＳＥボリューム				(    1 byte )
	unsigned char bgmVol;					// ＢＧＭボリューム				(    1 byte )
	unsigned char bgmPitch[16];				// ＢＧＭピッチ					(   16 byte )
	unsigned char mouseCursorSetting;		// マウスカーソルの设定			(    1 byte )
	unsigned char fontSize;					// 文字サイズ					(    1 byte )
	char          chatRegStr[MAX_CHAT_REG][MAX_CHAT_REG_STR_LEN+1];
											// チャット登録文字列			( 24*25 = 600 byte)
	unsigned char chatColor[MAXCHARACTER];	// チャット文字色				(    2 byte ) user
	unsigned char chatLine[MAXCHARACTER];	// チャット行数					(    2 byte ) user
	unsigned char chatArea[MAXCHARACTER];	// チャットの闻こえる范围		(    2 byte ) user
	unsigned char chatSound[MAXCHARACTER];	// チャット流れる音				(    2 byte ) user
	unsigned char ctrlSetting;				// CTRLキーの设定               (    1 byte )
	unsigned char addressBookSortMode;		// アドレスブックのソートモード (    1 byte )
	unsigned char nameOverTheHeadFlag;		// キャラの头の上に名称を出す 	(    1 byte )
	unsigned short mapBgmNo[MAXCHARACTER];	// 现在のＢＧＭ番号（复归接続）	(    4 byte ) user
	unsigned char LasterNoWaitFlg;			// ラスターウエイトしないか		(    1 byte )
#ifdef _SYSTEMMENU_BTN_CONFIG
	unsigned char ChatAreaBtnFlg;			// チャットエリアボタンを使うか	(    1 byte )
	unsigned char bummy[228];				// ダミー						(  228 byte )
#else
	unsigned char bummy[229];				// ダミー						(  229 byte )
#endif
	//                                      	                       合计   1023 byte
	//-------------------------------------------------------------------------//

} SAVE_DATA;
*/

SAVE_DATA	saveData;								// セーブデータ

char saveDataName[128] = {'\0'};	// 可变セーブデータのファイル名

// エラーコード
unsigned char savedataErrorCode;
// loadNowStateエラーコード
//  1 ... セーブファイルが作成できない。
//  2 ... セーブファイルが开けない。
//  3 ... セーブデータの読み込みに失败。
//  4 ... セーブデータのサイズが违う。（坏れてる）
//  5 ... セーブデータのバージョン违い。



//-------------------------------------------------------------------------//
// ユーザ设定保存                                                          //
//-------------------------------------------------------------------------//
BOOL saveUserSetting( void )
{
	setUserSoundOption();
	setUserInterfaceOption();
	setUserSetting( selectPcNo );
	setUserChatOption( selectPcNo );
	setUserChatRegStr();
	setUserMailSetting();
	setUserMapBgmNo( selectPcNo );

	return saveNowState();
}

//-------------------------------------------------------------------------//
// ユーザ设定読み込み                                                      //
//-------------------------------------------------------------------------//
BOOL loadUserSetting( void )
{

	if( loadNowState() )
	{
		getUserSoundOption(&saveData);
		getUserInterfaceOption(&saveData);
		getUserChatRegStr(&saveData);
		getUserMailSetting(&saveData);

		return TRUE;
	}

	return FALSE;
}


//-------------------------------------------------------------------------//
// セーブデータ保存                                                        //
//-------------------------------------------------------------------------//
BOOL saveNowState( void )
{

	BOOL reflg=TRUE;
	SAVE_DATA saveDataBackup;
	FILE *fp;
	char writebuffer[sizeof(saveData)*2];
	int writebufferlen;
#ifdef PUK3_ACCOUNT
	char filePath[1024];
#endif

	saveDataBackup = saveData;

	// セーブデータの暗号化
	jEncode( (char *)(&saveDataBackup), sizeof(saveDataBackup), 0,
		writebuffer, &writebufferlen, sizeof( writebuffer ) );


#ifdef PUK3_ACCOUNT
	// セーブデータ保存
	sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],SAVEFILE_NAME); //MLHIDE
	if( (fp = fopen( filePath, "wb+" )) == NULL ){                       //MLHIDE
		reflg=FALSE;
	}
	if( fwrite( writebuffer, 1, writebufferlen, fp ) < (unsigned int)writebufferlen ){
		reflg=FALSE;
	}
	fclose( fp );

	//メモリ上のテンポラリも更新する
	setFromSaveToTmpSave();
#else
	// セーブデータ保存
	if( (fp = fopen( saveDataName, "wb+" )) == NULL ){                   //MLHIDE
		reflg=FALSE;
	}
	if( fwrite( writebuffer, 1, writebufferlen, fp ) < (unsigned int)writebufferlen ){
		reflg=FALSE;
	}
	fclose( fp );
#endif
	return reflg;
}


//-------------------------------------------------------------------------//
// セーブデータ読み込み（起动时に1度だけ）                                 //
//-------------------------------------------------------------------------//
BOOL loadNowState( void )
{

#ifdef PUK3_ACCOUNT

	return TRUE;
#endif

	FILE *fp;
	char readbuffer[sizeof(saveData)*2];
	int readbufferlen;
	int saveDataLen;

	savedataErrorCode = 0;

	// セーブファイルを开く
	if( (fp = fopen( saveDataName, "rb+" )) == NULL )                    //MLHIDE
	{
		// 开けない时はファイルが无いとみなし新规で作成する。
		if( createSaveFile() == FALSE )
		{
			savedataErrorCode = 1;
			return FALSE;
		}

		// 改めてセーブファイルを开く
		if( (fp = fopen( saveDataName, "rb+" ))==NULL )                     //MLHIDE
		{
			// 作ったファイルが开けない
			savedataErrorCode = 2;
			return FALSE;
		}
	}

	// セーブデータ読み込み
	readbufferlen = fread( readbuffer, 1, sizeof( readbuffer ), fp );
	if( ferror( fp ) )
	{
		// 読み込みエラー
		savedataErrorCode = 3;
		fclose( fp );
		return FALSE;
	}
	fclose( fp );

	jDecode( readbuffer, readbufferlen, 0, (char *)(&saveData), &saveDataLen );

	// ファイルサイズのチェック
	if( saveDataLen != SAVE_DATA_SIZE)
	{
		//ファイルが异常と判断し、作り直す
		if( createSaveFile() == FALSE )
		{
			//作り直すこともできない
			savedataErrorCode = 1;
			return FALSE;
		}

		// 改めてセーブファイルを开く
		if( (fp = fopen( saveDataName, "rb+" ))==NULL ){                    //MLHIDE
			// 作ったファイルが开けない
			savedataErrorCode = 2;
			return FALSE;
		}

		// セーブデータ読み込みなおし
		readbufferlen = fread( readbuffer, 1, sizeof( readbuffer ), fp );
		if( ferror( fp ) ){
			// 作ったファイルが読み込みエラー
			savedataErrorCode = 3;
			fclose( fp );
			return FALSE;
		}
		fclose( fp );

		jDecode( readbuffer, readbufferlen, 0, (char *)(&saveData), &saveDataLen );

		//サイズが异常でした
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_106,"确认",MB_OK );                          //MLHIDE
	#else
		MessageBox( hWnd, SAVE_ERRMSG_SIZE_ERR,"确认",MB_OK );                //MLHIDE
	#endif
	}

	// ファイルバージョンのチェック
	if(saveData.version != SAVE_DATA_VERSION){

		//ファイルが异常と判断し、作り直す
		if( createSaveFile() == FALSE )
		{
			//作り直すこともできない
			savedataErrorCode = 1;
			return FALSE;
		}
		
		// 改めてセーブファイルを开く
		if( (fp = fopen( saveDataName, "rb+" ))==NULL ){                    //MLHIDE
				// 作ったファイルが开けない
				savedataErrorCode = 2;
				return FALSE;
		}

		// セーブデータ読み込みなおし
		readbufferlen = fread( readbuffer, 1, sizeof( readbuffer ), fp );
		if( ferror( fp ) ){
			// 作ったファイルが読み込みエラー
			savedataErrorCode = 3;
			fclose( fp );
			return FALSE;
		}
		fclose( fp );

		jDecode( readbuffer, readbufferlen, 0, (char *)(&saveData), &saveDataLen );

		//バージョンが异常でした
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_107,"确认",MB_OK );                          //MLHIDE
	#else
		MessageBox( hWnd, SAVE_ERRMSG_VER_ERR,"确认",MB_OK );                 //MLHIDE
	#endif
	}

	return TRUE;
}


//-------------------------------------------------------------------------//
// 新规セーブファイル作成                                                  //
//-------------------------------------------------------------------------//
BOOL createSaveFile( void )
{
	SAVE_DATA saveDataBackup;
	FILE *fp;
	char writebuffer[sizeof( saveData )*2];
	int writebufferlen;
	int i;

	// セーブデータバッファクリア
	memset( &saveDataBackup, 0, sizeof( saveDataBackup ) );


	// バージョン１仕样 -------------------------------------------------------//

	// バージョン番号を入れる
	saveDataBackup.version = SAVE_DATA_VERSION;

	// 初期设定
	saveDataBackup.stereoSetting = (unsigned char)stereo_flg;
	saveDataBackup.seVol         = (unsigned char)t_music_se_volume;
	saveDataBackup.bgmVol        = (unsigned char)t_music_bgm_volume;
	for( i = 0; i < 16; i++ )
		saveDataBackup.bgmPitch[i] = t_music_bgm_pitch[i];
	saveDataBackup.mouseCursorSetting = 0;
	saveDataBackup.fontSize           = 0;
#ifdef DRAW_CONFIG
	saveDataBackup.LasterNoWaitFlg    = (unsigned char)RasterNoWaitMode;
#endif
	for( i = 0;	i < MAXCHARACTER; i++ )
	{
		saveDataBackup.chatLine[i]  = DEF_CHAT_LINE;
		saveDataBackup.chatArea[i]  = DEF_VOICE;
		saveDataBackup.chatSound[i] = DEF_SOUND;
	}

	for( i = 0; i < MAX_CHAT_REG; i++ )
	{
		saveDataBackup.chatRegStr[i][0] = '\0';
	}

	saveDataBackup.ctrlSetting = 0;
	saveDataBackup.addressBookSortMode = 0;
	saveDataBackup.nameOverTheHeadFlag = 0;		// キャラの头の上に名称を出す 	(    1 byte )

	//-------------------------------------------------------------------------//

	// セーブデータの暗号化
	jEncode( (char *)(&saveDataBackup), sizeof( saveDataBackup ), 0,
		writebuffer, &writebufferlen, sizeof( writebuffer ) );

	if( (fp = fopen( saveDataName, "wb+" )) == NULL )                    //MLHIDE
	{
		return FALSE;
	}
	if( fwrite( writebuffer, 1, writebufferlen, fp ) < (unsigned int)writebufferlen )
	{
		return FALSE;
	}

	fclose( fp );

	return TRUE;
}




//-------------------------------------------------------------------------//
// ＩＤ关连
//-------------------------------------------------------------------------//

// IDを保存用バッファに置く
void setId( char *id )
{
	int i;

	if( id == NULL )
		return;

	for( i = 0; i < sizeof( saveData.id ); i++ )
	{
		saveData.id[i] = id[i];
		if( id[i] == '\0' )
			break;
	}
}


// IDを保存用バッファから取る
char *getId( char *id )
{
	int i;

	if( id == NULL )
		return NULL;

	for( i = 0; i < sizeof( saveData.id ); i++ )
	{
		id[i] = saveData.id[i];
		if( id[i] == '\0' )
			break;
	}

	return id;
}


//-------------------------------------------------------------------------//
// パスワード处理                                                          //
//-------------------------------------------------------------------------//

// パスワードを保存用バッファに入れる
void setPassword( char *pass )
{
	int i;

	if( pass == NULL )
		return;

	for( i = 0; i < sizeof( saveData.password ); i++ )
	{
		saveData.password[i] = pass[i];
		if( pass[i] == '\0' )
			break;
	}
}


// パスワードを保存用バッファから取る
char *getPassword( char *pass )
{
	int i;

	if( pass == NULL )
		return NULL;

	for( i = 0; i < sizeof( saveData.password ); i++ )
	{
		pass[i] = saveData.password[i];
		if( pass[i] == '\0' )
			break;
	}

	return pass;
}


//-------------------------------------------------------------------------//
// ＣＤキー关连
//-------------------------------------------------------------------------//

// CDKEYを保存用バッファに置く
void setCdkey( char *cdkey )
{
	int i;

	if( cdkey == NULL )
		return;

	for( i = 0; i < sizeof( saveData.cdKey ); i++ )
	{
		saveData.cdKey[i] = cdkey[i];
		if( cdkey[i] == '\0' )
			break;
	}
}


// CDKEYを保存用バッファから取る
char *getCdkey( char *cdkey )
{
	int i;

	if( cdkey == NULL )
		return NULL;

	for( i = 0; i < sizeof( saveData.cdKey ); i++ )
	{
		cdkey[i] = saveData.cdKey[i];
		if( cdkey[i] == '\0' )
			break;
	}

	return cdkey;
}




//-------------------------------------------------------------------------//
// ユーザ设定                                                              //
//-------------------------------------------------------------------------//

// ユーザ设定を保存バッファに入れる
void setUserSetting( int no )
{
	if( no < 0 || MAXCHARACTER <= no )
		return;
}


// ユーザ设定を保存バッファから取る
void getUserSetting( int no )
{
	if( no < 0 || MAXCHARACTER <= no )
		return;
}


// ユーザ设定をクリアして保存バッファに入れる
void clearUserSetting( int no )
{
	if( no < 0 || MAXCHARACTER <= no )
		return;
}


// サウンドオプション设定を保存バッファに入れる
void setUserSoundOption( void )
{
	int i;

	saveData.stereoSetting = (unsigned char)stereo_flg;
	saveData.seVol         = (unsigned char)t_music_se_volume;
	saveData.bgmVol        = (unsigned char)t_music_bgm_volume;
	for( i = 0; i < 16; i++ )
		saveData.bgmPitch[i] = t_music_bgm_pitch[i];
}


// サウンドオプション设定を保存バッファから取る
void getUserSoundOption(SAVE_DATA *data )
{
	int i;

	stereo_flg         = (int)(data->stereoSetting);
	t_music_se_volume  = (int)(data->seVol);
	t_music_bgm_volume = (int)(data->bgmVol);
	for( i = 0; i < 16; i++ )
		t_music_bgm_pitch[i] = data->bgmPitch[i];
}


// チャットオプション设定を保存バッファに入れる
void setUserChatOption( int no )
{
	saveData.chatColor[no] = MyChatBuffer.color;
	saveData.chatLine[no]  = (unsigned char)NowMaxChatLine;
	saveData.chatArea[no]  = NowMaxVoice;
	saveData.chatSound[no] = NowChatSound;
}


// チャットオプション设定を保存バッファから取る
void getUserChatOption( int no )
{
	MyChatBuffer.color = saveData.chatColor[no];
	NowMaxChatLine     = (int)saveData.chatLine[no];
	NowMaxVoice        = saveData.chatArea[no];
	NowChatSound       = saveData.chatSound[no];
}


// ユーザインタフェースオプション设定を保存バッファに入れる
void setUserInterfaceOption( void )
{
	saveData.mouseCursorSetting = (unsigned char)MouseCursorFlag;
	saveData.fontSize           = (unsigned char)chatFontSize;
	saveData.ctrlSetting        = (unsigned char)ctrlSetting;
	saveData.nameOverTheHeadFlag = (unsigned char)nameOverTheHeadFlag;
#ifdef DRAW_CONFIG
	saveData.LasterNoWaitFlg = (unsigned char)RasterNoWaitMode;
#endif
#ifdef _SYSTEMMENU_BTN_CONFIG
	saveData.ChatAreaBtnFlg = (unsigned char)systemMenuBtnConfigSetting;
#endif
}


// ユーザインタフェースオプション设定を保存バッファから取る
void getUserInterfaceOption( SAVE_DATA *data )
{

	MouseCursorFlag = (int)(data->mouseCursorSetting);
	chatFontSize    = (int)(data->fontSize);
	ctrlSetting     = (int)(data->ctrlSetting);
	nameOverTheHeadFlag = (int)(data->nameOverTheHeadFlag);
#ifdef DRAW_CONFIG
	RasterNoWaitMode = (int)(data->LasterNoWaitFlg);
#endif
#ifdef _SYSTEMMENU_BTN_CONFIG
	systemMenuBtnConfigSetting = (int)(data->ChatAreaBtnFlg);
#endif
}


// 登録チャット文字列を保存バッファに入れる
void setUserChatRegStr( void )
{
	int i;

	for( i = 0; i < MAX_CHAT_REG; i++ )
	{
		strcpy( saveData.chatRegStr[i], chatRegStr[i].buffer );
	}
}


// 登録チャット文字列を保存バッフからとる
void getUserChatRegStr( SAVE_DATA *data )
{
	int i;

	for( i = 0; i < MAX_CHAT_REG; i++ )
	{
		strcpy( chatRegStr[i].buffer, data->chatRegStr[i] );
		chatRegStr[i].cnt = strlen( chatRegStr[i].buffer );
		chatRegStr[i].cursorByte = chatRegStr[i].cnt;
	}
}


// メール关连设定を保存バッファに入れる
void setUserMailSetting( void )
{
	saveData.addressBookSortMode = (unsigned char)addressBookSortMode;
}


// メール关连设定を保存バッファからとる
void getUserMailSetting( SAVE_DATA *data )
{
	addressBookSortMode = (int)(data->addressBookSortMode);
}

// マップＢＧＭ番号を保存バッファに入れる
void setUserMapBgmNo( int no )
{
	saveData.mapBgmNo[ no ] = (unsigned short)map_bgm_no;
}

// マップＢＧＭ番号を保存バッファから取ってくる
void getUserMapBgmNo( int no )
{
	map_bgm_no = saveData.mapBgmNo[ no ];
}



