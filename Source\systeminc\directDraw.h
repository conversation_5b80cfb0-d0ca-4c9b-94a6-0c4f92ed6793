// directDraw.cpp ヘッダファイル

#ifndef _DIRECT_DRAW_H_
#define _DIRECT_DRAW_H_

#ifdef PUK2
	#include <d3d.h>
#endif

// 最大パレット数
#ifdef PUK2
	#define MAX_PAL	(24+7+1+7)
#else
#define MAX_PAL	(24+7+1)
#endif
// デフォルトパレット
#define DEF_PAL	0	//昼パレット

#ifdef PUK2
	// パレットリスト数
	#define PALLIST_MAX 100 + 4
	// パレットリスト关连付け削除までの时间
	#define PALLIST_DETHCNT 1
#endif

// パレットの变更时间（一分で完了）
#define PAL_CHANGE_TIME			3600

// システムパレット
#define SYSTEM_PAL_WHITE 		255
#define SYSTEM_PAL_AQUA 		254
#define SYSTEM_PAL_PURPLE 		253
#define SYSTEM_PAL_BLUE 		252
#define SYSTEM_PAL_YELLOW 		251
#define SYSTEM_PAL_GREEN 		250
#define SYSTEM_PAL_RED 			249
#define SYSTEM_PAL_GRAY 		248
#define SYSTEM_PAL_BLUE3 		247
#define SYSTEM_PAL_GREEN3 		246

#define SYSTEM_PAL_WHITE2 		7
#define SYSTEM_PAL_AQUA2 		6
#define SYSTEM_PAL_PURPLE2 		5
#define SYSTEM_PAL_BLUE2 		4
#define SYSTEM_PAL_YELLOW2 		3
#define SYSTEM_PAL_GREEN2 		2
#define SYSTEM_PAL_RED2 		1
#define SYSTEM_PAL_BLACK 		0
#define SYSTEM_PAL_BLUE4 		8
#define SYSTEM_PAL_GREEN4 		9

#ifdef PUK2
	#define SYSTEM_PAL_BLUE5 		( 0x100 + 0 )
#endif

// バックバッファー描画方法
enum{
	DRAW_BACK_NORMAL,	// 通常
	DRAW_BACK_NON,		// 描画しない
	DRAW_BACK_PRODUCE,	// 演出
	DRAW_BACK_BATTLE,	// 战闘
};

//---------------------------------------------------------------------------//
// Direct Draw管理用构造体型宣言                                             //
//---------------------------------------------------------------------------//
typedef struct
{
	LPDIRECTDRAW			lpDD;				// DirectDrawオブジェクト
#ifdef PUK2
	LPDIRECTDRAW7			lpDD2;				// DirectDraw7オブジェクト
	LPDIRECTDRAWSURFACE7	lpFRONTBUFFER;		// 表バッファ
	LPDIRECTDRAWSURFACE7	lpBACKBUFFER;		// 里バッファ
#else
	LPDIRECTDRAW2			lpDD2;				// DirectDraw2オブジェクト
	LPDIRECTDRAWSURFACE		lpFRONTBUFFER;		// 表バッファ
	LPDIRECTDRAWSURFACE		lpBACKBUFFER;		// 里バッファ
#endif
	LPDIRECTDRAWCLIPPER		lpCLIPPER;			// クリッ布
#ifdef PUK2
	DDSURFACEDESC2			ddsd;				// プライマリサーフェス处理用构造体定义
#else
	DDSURFACEDESC			ddsd;				// プライマリサーフェス处理用构造体定义
#endif
	LPDIRECTDRAWPALETTE		lpPALETTE;			// パレット
	int						xSize, ySize;		// ＷＩＮＤＯＷサイズ

#ifdef PUK2
	LPDIRECT3D7				lpD3;				// Direct3Dオブジェクト
	LPDIRECT3DDEVICE7		lpD3DEVICE;			// デバイス
	GUID					deviceGUID;			// OFFスクリーンの作成に使用
	DWORD					dwTextureCaps;		// OFFスクリーンの作成に使用
	short					sx, sy;				// 作成するサーフェイスのサイズ
	BOOL					TRC;				// 透过色が使用できるかどうか
	unsigned short 			(*PalTbl)[3];		// １６ビット用パレット值取得用テーブル
#endif
} DIRECT_DRAW;

// パレット构造体
extern PALETTEENTRY			Palette[256];
#ifdef PUK2
extern PALETTEENTRY			DefPalette[256];
#endif

// パレット状态构造体
typedef struct{
	int palNo;		// パレット番号
	int time;		// 初期化时间
	int flag;		// 初期化フラグ
	int count;		// パレットチェンジ时のカウンター
}PALETTE_STATE;


#ifdef PUK2
	// ３Ｄ顶点データ用构造体と设定用マクロ
	typedef struct
	{
		float x,y,z;
		float rhw;
		D3DCOLOR clr;
		float tu,tv;
	} D3_BLTPOINT;
	#define D3DFVF_BLTPOINT (D3DFVF_XYZRHW|D3DFVF_DIFFUSE|D3DFVF_TEX1)


	struct PAL24BPP{
		unsigned char b,g,r;
	};
	union PALPOINTER{
		unsigned long *p4;
		struct PAL24BPP *p3;
		unsigned short *p2;
		unsigned char *p1;
	};
#endif

#ifdef PUK2
	typedef unsigned char PALDATA[3];				// パレットのポインタ

	#define MAXPALCHGQ 10
#endif

#ifdef PUK2
	struct PALLETE_LIST{
		int sprNo;			// スプライトインフォのインデックス
		int palNo;			// 使用しているパレットの番号
		int palcnt;			// 使用しているパレットのフェードカウント
		int cnt;			// 前回使用からのカウント
		unsigned long pal[256];
	};
#endif
// グローバル变数 /////////////////////////////////////////////////////////////
extern DIRECT_DRAW	*lpDraw;
// DirectDraw初期化フラグ
extern BOOL DDinitFlag;	
// パレット状态构造体
extern PALETTE_STATE PalState;	
// パレットチェンジフラグ
extern BOOL	PalChangeFlag;

// パレットチェンジ中か？
extern BOOL palChageStatus;

// バックバッファー描画方法
extern int BackBufferDrawType;

// フリップカウンター
extern int FlipCnt; 
// 画面色数
extern int displayBpp;

extern short RasterNoWaitMode;

extern BOOL ScreenShotFlg;

#ifdef PUK2
	extern unsigned char *PalchgTbl[3];	// パレットチェンジのときにあらかじめチェンジ后の值を作っておく

	// オートマップ作业用サーフェース
	extern LPDIRECTDRAWSURFACE7 lpAutoMapSurface; 	

	extern char device3D;		// ３Ｄデバイス选择フラグ
	extern char last3D_mode;	// 最終３Ｄデバイス
#endif
#ifdef PUK3_WINDOWMODE_BLACKOUT
	extern BOOL PrimaryLostFlag;
#endif

//---------------------------------------------------------------------------//
// 关数プロト种类宣言                                                      //
//---------------------------------------------------------------------------//
// DirectDraw初期化关数
#ifdef PUK2
char InitDirectDraw( void );
#else
BOOL InitDirectDraw( void );
#endif

// パレット初期化关数
BOOL InitPalette( void );

// バックサーフェスを黒でクリアー
void ClearBackSurface( void );

// サーフェスを黒でクリアー
#ifdef PUK2
void ClearSurface( LPDIRECTDRAWSURFACE7 lpSurface );
#else
void ClearSurface( LPDIRECTDRAWSURFACE lpSurface );
#endif

// ＢＭＰファイル読み込み关数
LPBITMAPINFO LoadDirectDrawBitmap( char *pFile );

// サーフェスの作成
#ifdef PUK2
LPDIRECTDRAWSURFACE7 CreateSurface( short sizeX, short sizeY, DWORD ColorKey, unsigned int VramOrSysram );
#else
LPDIRECTDRAWSURFACE CreateSurface( short sizeX, short sizeY, DWORD ColorKey, unsigned int VramOrSysram );
#endif
#ifdef PUK3_SCENE_CHANGE
	LPDIRECTDRAWSURFACE7 CreateWarkSurface( short bxsize, short bysize, DWORD ColorKey, unsigned int VramOrSysram );
#endif

#ifdef PUK2

// バックサーフェスへ転送
HRESULT DrawSurfaceFast( short bx, short by, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf=0, unsigned long rgba=0xffffffff );
// バックサーフェスへ高速転送
HRESULT DrawSurfaceFast2( short bx, short by, RECT *rect, LPDIRECTDRAWSURFACE7 lpSurface );
// バックサーフェスへ扩大缩小反転転送
HRESULT DrawSurfaceStretch( short bx, short by, float scaleX, float scaleY, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf=0, unsigned long rgba=0xffffffff );

// バックサーフェスへ転送
//HRESULT DrawSurface( short bx, short by, RECT *rect, LPDIRECTDRAWSURFACE7 lpSurface );

// ＢＭＰをサーフェスへ転送关数 （ StretchDIBits を使用 ）
void DrawBitmapToSurface( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, LPBITMAPINFO pInfo );
// ＢＭＰをサーフェスへ転送关数 （ memcpy を使用 ）
void DrawBitmapToSurface2( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo );

#ifdef PUK2_DIFFPAL_SURFACE
BOOL DrawBitmapToSurface4( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo, unsigned long *pPal );
#else
void DrawBitmapToSurface4( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo, unsigned long *pPal );
#endif

// 矩形范围にアルファブレンド ++++
HRESULT RectAlphaBlend( short l, short r, short t, short b, unsigned long rgba, unsigned char bltf );

// バックサーフェスへ矩形范围を転送 ++++
HRESULT DrawSurfaceFast_PUK2( short bx, short by, RECT rect, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf, unsigned long rgba );

// バックサーフェスへ矩形范围を扩大缩小転送 ++++
HRESULT DrawSurfaceStretch_PUK2( short bx, short by, RECT *rc, float scaleX, float scaleY, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf, unsigned long rgba );

#else

// バックサーフェスへ高速転送
HRESULT DrawSurfaceFast( short bx, short by, LPDIRECTDRAWSURFACE lpSurface );
// バックサーフェスへ高速転送
HRESULT DrawSurfaceFast2( short bx, short by, RECT *rect, LPDIRECTDRAWSURFACE lpSurface );
// バックサーフェスへ扩大缩小転送
HRESULT DrawSurfaceStretch( short bx, short by, float scaleX, float scaleY, LPDIRECTDRAWSURFACE lpSurface );

// バックサーフェスへ転送
//HRESULT DrawSurface( short bx, short by, RECT *rect, LPDIRECTDRAWSURFACE lpSurface );

// ＢＭＰをサーフェスへ転送关数 （ StretchDIBits を使用 ）
void DrawBitmapToSurface( LPDIRECTDRAWSURFACE lpSurface, int offsetX, int offsetY, LPBITMAPINFO pInfo );
// ＢＭＰをサーフェスへ転送关数 （ memcpy を使用 ）
void DrawBitmapToSurface2( LPDIRECTDRAWSURFACE lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo );

#endif

// サーフェスFLIP关数
void Flip( void );

#ifdef PUK3_WINDOWMODE_BLACKOUT
	// リストアができるかチェックする
	BOOL CheckRestore();
#endif
// サーフェスロストのチェック
BOOL CheckSurfaceLost( void );

// DirectDrawオブジェクトを开放する
void ReleaseDirectDraw( void );

// 处理にかかった时间を描画（ デバッグ用 ）
void DrawDebugLine( unsigned char color );

// ボックスを描画 */
#ifdef PUK2
void DrawBox( RECT *rect, unsigned short color, BOOL fill );
#else
void DrawBox( RECT *rect, unsigned char color, BOOL fill );
#endif

// パレット处理 ***************************************************************/
void PaletteProc( void );
void makeHighColorPalette( void );
void makeFullColorPalette( void );
void make24BitColorPalette( void );

// パレットチェンジ ***********************************************************/
void PaletteChange( int palNo, int time );

#ifdef PUK2
	//--------------------------------------
	// パレットリスト初期化关数
	//--------------------------------------
	void IniPalList();

	//--------------------------------------
	// パレットリスト初期化关数
	//--------------------------------------
	void RelPalList();

	//--------------------------------------
	// パレットリスト取得关数
	//--------------------------------------
	// 引数
	//	sprNo				パレットを取得したいスプライトインフォのインデックス
	//
	// 戾り值
	//	PALLETE_LIST *		取得したパレットリストへのアドレスを返す、失败した场合は NULL を返す
	//
	PALLETE_LIST *GetPalList( int sprNo );

	//--------------------------------------
	// パレットリストチェック关数
	//--------------------------------------
	void CheckPalList();

	//--------------------------------------
	// パレットチェンジのパレット取得关数
	//--------------------------------------
	// 引数
	//	sprNo				パレットを取得したいスプライトインフォのインデックス
	//
	// 戾り值
	//	PALLETE_LIST *			取得したパレットへのアドレスを返す、失败した场合は NULL を返す
	//
	/***	flgの值*/
	enum{
		GETPALDATA_OLDDEFPAL,			// 旧パレットの昼パレットをパレットチェンジ
		GETPALDATA_OLDNOWPAL,			// 旧パレットの现在のパレットをパレットチェンジ
		GETPALDATA_PUK2PAL,				// 引数のパレットのデータを使用
	};
	/***/
	PALLETE_LIST *MakePalList( int sprNo, char flg, char pchg, PALDATA *pld, RGBQUAD *pqd );

	// パレットチェンジを利用しないように设定 ++++
	void Palchg_not();
	// 时间ごとのパレットチェンジを利用するように设定 ++++
	void Palchg_auto();
	// 值を指定してパレットチェンジ ++++
	void Palchg_manual( unsigned char lr, unsigned char lg, unsigned char lb );

	// 现在の描画モード取得
	char getNow3D_mode();
	// 现在描画モードの３Ｄ使用可能状况を取得
	BOOL getUsable3D();
#endif

// オートマッピング表示
void DrawAutoMapping( int x, int y, unsigned char *autoMap, int w, int h, int zoom );

#ifdef PUK2
// 新マップ用オートマッピング表示
void DrawAutoMapping_Sugi( int x, int y, unsigned long *autoMap, int w, int h, int zoom );
#endif

// 画像の平均を取ってオートマップの色を作る
int getAutoMapColor( unsigned int GraphicNo, PALETTEENTRY *pPalette );
// 色数entryのパレットpaletteの中から、colorに一番近いもののindexを返す。
int getNearestColorIndex( COLORREF color, PALETTEENTRY *palette, int entry );

// スクリーンショットの保存
void screenShot( void );
// 画像をＢＭＰファイルに保存
BOOL saveBmpFile( const char *, BYTE *,	int, int, int, int, int, RGBQUAD *, int );



void DrawMapEffect( void );


#endif
