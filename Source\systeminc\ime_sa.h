﻿/************************/
/*	ime_sa.h				*/
/************************/
#ifndef _IME_SA_H_
#define _IME_SA_H_

// ＩＭＥ构造体
typedef struct{
	HIMC 	hImc;			// ＩＭＥのハンドル
	HIMC 	hImcBegin;
	char 	buffer[ 256 ];	// ＩＭＥバッファー
	char 	block[ 256 ];	// ＩＭＥ文节バッファー
	int 	cursorPos;		// カーソルの位置
	DWORD 	conversion;		// ＩＭＥ入力モード取得用
	DWORD 	sentence;		// ＩＭＥ入力モード取得用
	CANDIDATELIST *candList;// 变换候补构造体
}IME_INFO;

// ＩＭＥ构造体
extern IME_INFO ImeInfo;

// ＩＭＥバッファーバックアップ
extern char ImeBufferBak2;

// ＩＭＥ关连の初期化 *********************************************************/
void InitIme( void );

// ＩＭＥの終了 ***************************************************************/
void EndIme( void );

//*****************************************************************************/
// ＩＭＥのＯＮ、ＯＦＦ状态の确认 
// 戾り值　	ＯＮ　：TRUE
// 			ＯＦＦ：FALSE
//*****************************************************************************/
BOOL ImeOpenState( void );

//*****************************************************************************/
// ＷＩＮＤＯＷＳのＩＭＥバッファーをクリアする 
//*****************************************************************************/
void ClrImeCompositionString( void );

// WM_IME_COMPOSITION 信息の处理 ****************************************/
void ImeComposition( LPARAM lParam );

// ＩＭＥ关连处理 *************************************************************/
void ImeProc( void );

/** ＩＭＥ关连信息处理 **************************************************/
//void ImeMsgProc( UINT Message, WPARAM wParam, LPARAM lParam );

#endif
