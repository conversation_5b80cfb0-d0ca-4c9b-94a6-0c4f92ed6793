﻿
#define CSPUK2_CB_SX	315
typedef struct {
	int		sx,sy;
} offsetPos;



#if 0
struct BaseToFaceTbl{
	int BaseGraNo;
	int BaseFaceNo[3];
};

struct BaseToFaceTbl BtFtbl[]={
	// XG1
	{SPR_000em, CG_FACE_0, RN_FACE_0, PUK2_FACE_01},
	{SPR_010em, CG_FACE_1, RN_FACE_1, PUK2_FACE_02},
	{SPR_020em, CG_FACE_2, RN_FACE_2, PUK2_FACE_03},
	{SPR_030em, CG_FACE_3, RN_FACE_3, PUK2_FACE_04},
	{SPR_040em, CG_FACE_4, RN_FACE_4, PUK2_FACE_05},
	{SPR_050em, CG_FACE_5, RN_FACE_5, PUK2_FACE_06},
	{SPR_060em, CG_FACE_6, RN_FACE_6, PUK2_FACE_07},
	{SPR_200em, CG_FACE_10, RN_FACE_7, PUK2_FACE_08},
	{SPR_210em, CG_FACE_11, RN_FACE_8, PUK2_FACE_09},
	{SPR_220em, CG_FACE_12, RN_FACE_9, PUK2_FACE_10},
	{SPR_230em, CG_FACE_13, RN_FACE_10, PUK2_FACE_11},
	{SPR_240em, CG_FACE_14, RN_FACE_11, PUK2_FACE_12},
	{SPR_250em, CG_FACE_15, RN_FACE_12, PUK2_FACE_13},
	{SPR_260em, CG_FACE_16, RN_FACE_13, PUK2_FACE_14},
	// XG1Renewal
	{SPR_400em, CG_FACE_0, RN_FACE_0, PUK2_FACE_01},
	{SPR_410em, CG_FACE_1, RN_FACE_1, PUK2_FACE_02},
	{SPR_420em, CG_FACE_2, RN_FACE_2, PUK2_FACE_03},
	{SPR_430em, CG_FACE_3, RN_FACE_3, PUK2_FACE_04},
	{SPR_440em, CG_FACE_4, RN_FACE_4, PUK2_FACE_05},
	{SPR_450em, CG_FACE_5, RN_FACE_5, PUK2_FACE_06},
	{SPR_460em, CG_FACE_6, RN_FACE_6, PUK2_FACE_07},
	{SPR_500em, CG_FACE_10, RN_FACE_7, PUK2_FACE_08},
	{SPR_510em, CG_FACE_11, RN_FACE_8, PUK2_FACE_09},
	{SPR_520em, CG_FACE_12, RN_FACE_9, PUK2_FACE_10},
	{SPR_530em, CG_FACE_13, RN_FACE_10, PUK2_FACE_11},
	{SPR_540em, CG_FACE_14, RN_FACE_11, PUK2_FACE_12},
	{SPR_550em, CG_FACE_15, RN_FACE_12, PUK2_FACE_13},
	{SPR_560em, CG_FACE_16, RN_FACE_13, PUK2_FACE_14},
	// XG2
	{SPR_600em, V2_FACE_0, V2_FACE_0, PUK2_FACE_15},
	{SPR_610em, V2_FACE_1, V2_FACE_1, PUK2_FACE_16},
	{SPR_620em, V2_FACE_2, V2_FACE_2, PUK2_FACE_17},
	{SPR_630em, V2_FACE_3, V2_FACE_3, PUK2_FACE_18},
	{SPR_640em, V2_FACE_4, V2_FACE_4, PUK2_FACE_19},
	{SPR_650em, V2_FACE_5, V2_FACE_5, PUK2_FACE_20},
	{SPR_660em, V2_FACE_6, V2_FACE_6, PUK2_FACE_21},
	{SPR_700em, V2_FACE_7, V2_FACE_7, PUK2_FACE_22},
	{SPR_710em, V2_FACE_8, V2_FACE_8, PUK2_FACE_23},
	{SPR_720em, V2_FACE_9, V2_FACE_9, PUK2_FACE_24},
	{SPR_730em, V2_FACE_10, V2_FACE_10, PUK2_FACE_25},
	{SPR_740em, V2_FACE_11, V2_FACE_11, PUK2_FACE_26},
	{SPR_750em, V2_FACE_12, V2_FACE_12, PUK2_FACE_27},
	{SPR_760em, V2_FACE_13, V2_FACE_13, PUK2_FACE_28},
};
#endif

void PUK2_pActSelGra_init( int index )
{
	int		pageno;
	int		j;
	int		face;
	int		charno;

	if( pActSelGra[index] != NULL ){
		DeathAction( pActSelGra[index] );
		pActSelGra[index] = NULL;
	}
	if ( !existCharacterListEntry( index ) ) return;

	// キャラのアクション作成
	if( pActSelGra[index] == NULL ){
		if( chartable[index].faceGraNo < 30000 ){
			chartable[index].faceGraNo=CG2_FACE_SHADOW_PCM;
		}
		face = getReFaceGraphic( chartable[index].faceGraNo );
		face = getFaceGraphicToPUK2(face);

		if( chartable[index].isRenewal < 0 ){
			if( PUK2_FACE_01 <= face && face < PUK2_FACE_15 ){
				chartable[index].isRenewal=1;
			}else{
				chartable[index].isRenewal=2;
			}
		}else{
			if( face >= PUK2_FACE_15 ){
				chartable[index].isRenewal=2;
			}
#ifdef TW_CHARA
			if (face >= TW_FACE_01 && face <= TW_FACE_END_1 || face >= TW_FACE_07 && face <= TW_FACE_END_2) {
				chartable[index].isRenewal = 3;
			}
#endif
		}
		j=chartable[index].isRenewal;

		pageno=PUK2_GRANO_NEW;
		switch(j){
		case 0:
			pageno=PUK2_GRANO_OLD;
			break;
		case 1:
			pageno=PUK2_GRANO_OLD;
			break;
		case 2:
			pageno=PUK2_GRANO_NEW;
			break;
#ifdef TW_CHARA
		case 3:
			pageno = TW_GRANO;
			break;
#endif
		}
		pActSelGra[index] = GetAction( PRIO_CHR, 0 );
		if( pActSelGra[index] != NULL )
		{
#ifdef TW_CHARA
			if (face >= TW_FACE_07 && face <= TW_FACE_END_2) {
				charno = (face - TW_FACE_07) / 100;
				if (charno)
				{
					if (charno == 1)
						charno = 10;
				}
				else
				{
					charno = 6;
				}
			}else
			if (face >= TW_FACE_01 && face <= TW_FACE_END_1) {
				charno = (face - TW_FACE_01) / 100;
			}else
#endif
			if (face>=PUK2_FACE_15) charno=(face-PUK2_FACE_15)/100;
			else charno=(face-PUK2_FACE_01)/100;

			if (chartable[index].faceGraNo==CG2_FACE_SHADOW_PCM) pActSelGra[index]->anim_chr_no = SPR_shadow_pcm;
			else if (chartable[index].faceGraNo==CG2_FACE_SHADOW_PCF) pActSelGra[index]->anim_chr_no = SPR_shadow_pcf;
			else{
				if (j==0) pActSelGra[index]->anim_chr_no = aSelGraNoTblV2[pageno].pSelectSubGraNoTbl[charno];
				else pActSelGra[index]->anim_chr_no = aSelGraNoTblV2[pageno].pSelectGraNoTbl[charno];

				pActSelGra[index]->anim_chr_no += ( (face%100)/25 ) * 6;
//				if (pageno==PUK2_GRANO_OLD) pActSelGra[index]->atr = ACT_ATR_HIT_BOX;
			}

			pActSelGra[index]->anim_no = ANIM_STAND;
			pActSelGra[index]->dispPrio = DISP_PRIO_CHAR;
			pActSelGra[index]->x = SymOffsetX + 163 + index * CSPUK2_CB_SX;
			pActSelGra[index]->y = SymOffsetY + 292;
			pActSelGra[index]->anim_ang = 5;
			pattern( pActSelGra[index], ANM_NOMAL_SPD, ANM_LOOP );
		}
	}
}

static int PUK2selectCharacterDisp( int index, int inputDisable )
{
	int		i;
	int		basex, basey;
////	int		len;
	int		login;
	PUK2_BTN_INFO selButton[] = {
		// 新规ボタン情报
			{// 范围始点座标OFFセット(x,y)
				119, 270,
			 // 范围幅(w,h)
				 64,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_CREATE0, PUK2_BUTTON_CREATE2, PUK2_BUTTON_CREATE1
			},
		// ログインボタン情报
			{// 范围始点座标OFFセット(x,y)
				 82, 270,
			 // 范围幅(w,h)
				 64,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_LOGIN0, PUK2_BUTTON_LOGIN2, PUK2_BUTTON_LOGIN1
			},
		// 削除ボタン情报
			{// 范围始点座标OFFセット(x,y)
				156, 270,
			 // 范围幅(w,h)
				 64,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_DEL0, PUK2_BUTTON_DEL2, PUK2_BUTTON_DEL1
			},
		// イラスト选择矢印
			{// 范围始点座标OFFセット(x,y)
				161,  90,
			 // 范围幅(w,h)
				 12,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_SLEFT0, PUK2_BUTTON_SLEFT2, PUK2_BUTTON_SLEFT1
			},
			{// 范围始点座标OFFセット(x,y)
				238,  90,
			 // 范围幅(w,h)
				 12,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_SRIGHT0, PUK2_BUTTON_SRIGHT2, PUK2_BUTTON_SRIGHT1
			},
		// キャラクタ选择矢印
			{// 范围始点座标OFFセット(x,y)
				 55, 154,
			 // 范围幅(w,h)
				 12,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_SLEFT0, PUK2_BUTTON_SLEFT2, PUK2_BUTTON_SLEFT1
			},
			{// 范围始点座标OFFセット(x,y)
				240, 154,
			 // 范围幅(w,h)
				 12,  16,
			 // ボタン画像番号(凸画像,凹画像)
				PUK2_BUTTON_SRIGHT0, PUK2_BUTTON_SRIGHT2, PUK2_BUTTON_SRIGHT1
			},
		};
	offsetPos	csPos[] = {
			{  62, 48 },		// 名称
			{ 104, 88 },		// 等级
			{ 180,109 },		// ログインカウント
			{  98,127 },		// 职业
			{ 172, 30 },		// 颜グラフィック
			{ 153,241 },		// 立ちキャラ
			{  53, 27 },		// キャラクター表示
		};
	int		inputNum=-1;
	char	msg[256];

	// 下地の座标（＝キャラ表示の基准点）を计算
	basex = 10 + CSPUK2_CB_SX * index;
	basey = 51;
	// 指定された场所にあわせて座标に补正をかける
	for( i = 0 ; i < arraysizeof( selButton ) ; i++ ){
		selButton[i].cx += basex;
		selButton[i].cy += basey;
	}
	for( i = 0 ; i < arraysizeof( csPos ) ; i++ ){
		csPos[i].sx += SymOffsetX + basex;
		csPos[i].sy += SymOffsetY + basey;
	}

	// キャラクターがいない场合
	if( existCharacterListEntry( index ) == 0 ){
		// 作成ボタンの表示
		PUK2drawGraBtnInfo( &selButton[0], DISP_PRIO_BG+2, 0, 0, inputDisable );
		if( PUK2pushGraBtnInfo( &selButton[0] ) & BTN_LEFT_CLICK ){
			inputNum = 0;
		}

		// これ以降入力不可に
		inputDisable++;
	}else{
		// キャラクタがいるとき

		/* ログインと削除ボタンの、表示とチェック */
		for( i = 1 ; i < 3 ; i++ ){
			PUK2drawGraBtnInfo( &selButton[i], DISP_PRIO_BG+2, 0, 0, inputDisable );
			// 入力可能であれば
			if( !inputDisable ){
				if( PUK2pushGraBtnInfo( &selButton[i] ) & BTN_LEFT_CLICK ){
					inputNum = i;
				}
			}
		}

		// ついでにキャラクタ情报の表示も
		//见习版とそれ以外で文字の色を变える
		BYTE  FontCol = 0;
		if( PackageVer == PV_TRIAL || PackageVer == PV_EQUAL ){
			FontCol = FONT_PAL_BLUE;
		}else
		if( PackageVer >= PV_FIRST_VER2 ){
			FontCol = FONT_PAL_BLUE2;
		}else{
			FontCol = FONT_PAL_WHITE;
		}

		// 颜画像
		titleStockDisp( csPos[4].sx, csPos[4].sy, DISP_PRIO_CHAR, chartable[index].faceGraNo, 0, 0xffffffff );
		// 名称
		StockFontBuffer( csPos[0].sx, csPos[0].sy,
		FONT_PRIO_BACK, FONT_KIND_CHAT_S, FontCol, chartable[index].name, 0, 0 );
		// 等级
		sprintf( msg, "%4d", chartable[index].lv );                         //MLHIDE
		StockFontBuffer( csPos[1].sx, csPos[1].sy, FONT_PRIO_BACK, FONT_KIND_CHAT_S, FontCol, msg, 0 );
		// ログイン回数表示
		login = chartable[index].login;
		if( login >= 10000 )login = 9999;	// カウンターストップ
		sprintf( msg, "%4d", login );                                       //MLHIDE
		StockFontBuffer( csPos[2].sx, csPos[2].sy, FONT_PRIO_BACK, FONT_KIND_CHAT_S, FontCol, msg, 0 );
		// 职业
		sprintf( msg, "%s", chartable[index].JobName );                     //MLHIDE
		StockFontBuffer( csPos[3].sx, csPos[3].sy, FONT_PRIO_BACK, FONT_KIND_CHAT_S, FontCol, msg, 0 );

		if( pActSelGra[index] ){
			// キャラの表示
			pActSelGra[index]->anim_no = ANIM_WALK;
			pattern( pActSelGra[index], ANM_NOMAL_SPD, ANM_LOOP );
		}
	}

	/* 矢印ボタンの表示 */
	// ＰＵＫ以降がインストールされているか
#ifdef PUK2
	if( PackageVer >= PV_FIRST_VER2 && existCharacterListEntry( index ) != 0 ){
#else
	if( giInstallVersion >= 2 && existCharacterListEntry( index ) != 0 ){
#endif
		for( i = 3 ; i < 7 ; i++ ){
			// 颜グラフィック切り替えボタン？
			if( i < 5 ){
				// ＰＵＫキャラ？
				if( chartable[index].isRenewal != 0 && chartable[index].isRenewal != 1 ){
					// ＰＵＫ２が入ってる？
#ifdef PUK2
					if( PackageVer < PV_PUK2 ){
#else
					if( giInstallVersion < 3 ){
#endif
						// 切り替えられないので次へ
						continue;
					}
				}
			}
			// 画像切り替えボタン？
			if( i >= 5 ){
				// キャラ画像は切り替え可能？
				if( chartable[index].isRenewal != 0 && chartable[index].isRenewal != 1 ){
					// 切り替えられないので次へ
					continue;
				}
			}

			// ボタン表示
			PUK2drawGraBtnInfo( &selButton[i], DISP_PRIO_BG+2, 0, 0, inputDisable );
			// 入力可能であれば
			if( !inputDisable ){
				if( PUK2pushGraBtnInfo( &selButton[i] ) & BTN_LEFT_CLICK ){
					inputNum = i;
				}
			}
		}
	}

	// 最后に下地を表示
	titleStockDisp( csPos[6].sx, csPos[6].sy, DISP_PRIO_BG+1, PUK2_CHARASELDISP, 0, 0xffffffff );
	titleStockDisp( SymOffsetX + basex, SymOffsetY + basey, DISP_PRIO_BG+1, PUK2_CHARASELBASE, 0, 0xffffffff );

	// 入力の状况を返す
	return inputNum;
}


#ifndef PUK2

// ＢＡＣＫボタン情报
PUK2_BTN_INFO PUK2_Clist_backBtn =
	{// 范围始点座标(x,y)
		288, 438,
	 // 范围幅(w,h)
		64,   16,
	 // ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK2, PUK2_BUTTON_BACK1
	};

#else
	// ＢＡＣＫボタン情报
	PUK2_BTN_INFO PUK2_Clist_backBtn =
		{// 范围始点座标(x,y)
			534, 438,
		 // 范围幅(w,h)
			64,   16,
		 // ボタン画像番号(凸画像,凹画像)
			PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK2, PUK2_BUTTON_BACK1
		};

	// Ｕｐｄａｔｅボタン情报
	PUK2_BTN_INFO PUK2_Clist_updateBtn =
		{// 范围始点座标(x,y)
			296, 433,
		 // 范围幅(w,h)
			49,   17,
		 // ボタン画像番号(凸画像,凹画像)
			PUK2_BUTTON_OKs0, PUK2_BUTTON_OKs2, PUK2_BUTTON_OKs1
		};
#endif


// 家族マスターなので削除できないウィンドウ
static int GuildMasterWindow( int _x, int _y )
{
	char msg[ 256 ];
	int w = 64*6;
	int h = 48*4;
	int x = _x - w/2;
	int y = _y - h/2;
	int xx, yy;
	int ret = 0;

#if 1
	STR_BTN_INFO retBtn =
	{
		x+147+18, y+144,
		x+145+18, y+144, 54, 18,  ML_STRING(313, "返回")
	};
#else
	STR_BTN_INFO yesBtn =
	{
		x +74, y +144,
		x +72, y +144, 90, 18,  "消除"                                        //MLHIDE
	};
	STR_BTN_INFO noBtn =
	{
		x+220, y +144,
		x+218, y +144, 90, 18,  "不消除"                                       //MLHIDE
	};
#endif

	if( ptActCommonYesNoWindow == NULL )
	{
		ptActCommonYesNoWindow =
#ifdef PUK2_NEW_MENU
			makeWindowDisp( x, y, w, h, 4 );
#else
			makeWindowDisp( x, y, w, h, 1 );
#endif
		// ウィンドウ开く音
		//play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}
	// アクションできてないなら終わる
	if( ptActCommonYesNoWindow == NULL )
		return 0;
	// エフェクト中なら終わる
	if( ptActCommonYesNoWindow->hp <= 0 )
		return 0;

	// "是"ボタンが押されたかチェック
	if( (pushStrBtnInfo( &retBtn ) & BTN_LEFT_CLICK) )
	{
		ret = 1;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}

	if( ret > 0 )
	{
		if( ptActCommonYesNoWindow )
		{
			DeathAction( ptActCommonYesNoWindow );
			ptActCommonYesNoWindow = NULL;
		}
	}

	// "返回"を表示
	drawStrBtnInfo( &retBtn, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
			1, BoxColor, DISP_PRIO_BOXFILL );

	// 信息表示
	strcpy( msg, ML_STRING(314, "家族管理者无法清除。") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy = 20;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	strcpy( msg, ML_STRING(315, "解散家族、") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy += 32;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	strcpy( msg, ML_STRING(316, "退出家族、") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy += 24;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	strcpy( msg, ML_STRING(317, "请清除。") );
	xx = (w - GetStrWidth( msg, FONT_KIND_MIDDLE ))/2;
	yy += 24;
	StockFontBuffer( x+xx, y+yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		msg, 0, 0 );

	return ret;
}

void PUK2_selectCharacterProc( void )
{

	int ret;
	int i, j;
////	int login;
	char msg[256];
	int btnUseFlag = 1;	// ボタンを有效にするフラグ。０が有效。
	static int CanUpGradeFlag = 0;

	static ACTION *ptActMenuWin = NULL;
	int attrColor[4][2] =
	{
		{ SYSTEM_PAL_GREEN,  SYSTEM_PAL_GREEN2  },
		{ SYSTEM_PAL_AQUA,   SYSTEM_PAL_AQUA2   },
		{ SYSTEM_PAL_RED,    SYSTEM_PAL_RED2    },
		{ SYSTEM_PAL_YELLOW, SYSTEM_PAL_YELLOW2 }
	};
////	int x1, y1, x2, y2;
////	int len;
	PUK2_BTN_INFO *pBackBtn;
#ifdef PUK2
	PUK2_BTN_INFO *pUpdateBtn;

	pUpdateBtn = &PUK2_Clist_updateBtn;
#endif

	pBackBtn = &PUK2_Clist_backBtn;

	// キャラクタリスト取得处理初期化
	switch( SubProcNo ){

	case  E_CHARLIST_INIT:
		SubProcNo = E_CHARLIST_DOWNLOAD;
		PackageVer = PV_NORMAL;	// パッケージバージョンを通常に初期化
		initDownloadCharList();	// キャラリスト取得中??????
		CanUpGradeFlag = 0;		// バージョンアップフラグ０
		PaletteChange( 0, 0 );
		break;

	// キャラクタリスト取得处理
	case E_CHARLIST_DOWNLOAD:
		ret = downloadCharList();
		if( ret == 1 ){
			SubProcNo = E_CHARLIST_LOAD_OK;	// 取得完了
			//play_bgm(2);
		}else
		if( ret == -3 ){
			SubProcNo = E_CHARLIST_MAINTE;	// メンテナンス中
			cleanupNetwork();	// ネットワーク初期化
		}else
		if( ret == -4 )	{
			SubProcNo = E_CHARLIST_AUTH_ERR;	// ユーザ认证失败
			cleanupNetwork();	// ネットワーク初期化
		}else
		if( ret < 0 ){
			SubProcNo = E_CHARLIST_LOAD_ERR;	// エラー発生
			cleanupNetwork();	// ネットワーク初期化
		}
		break;

	// キャラ取得ＯＫ。ここでサーバーのバージョンとインストールバージョンを
	// 比べる
	case E_CHARLIST_LOAD_OK:
		{ 
			// インストールしてあるバージョン调べる。
			giInstallVersion = GetInstallVersion( );

			if( PackageVer == 1 ){
				// 见习い版だったらアップグレードできない。
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// サーバーがＶ２なのにＶ２がインストールされてない场合。
			// サーバーがＥＸなのにＥＸがインストールされてない场合。
			if( ( giInstallVersion < 2 && PackageVer >= PV_FIRST_VER2 )
			||  ( giInstallVersion < 1 && PackageVer >= PV_FIRST_EX )
#ifdef PUK2
			||  ( giInstallVersion < 3 && PackageVer >= PV_PUK2 )
#endif
#ifdef PUK3_UPGRADE
			||  ( giInstallVersion < 4 && PackageVer >= PV_PUK3 )
#endif
			){
				// 新しいのがインストールされてないよ。
				SubProcNo = E_CHARLIST_INSTALL_ERR;
//				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// ＥＸへのアップデート
			if( ( giInstallVersion == 1 && PackageVer == PV_NORMAL )
			||  ( giInstallVersion == 1 && PackageVer == PV_EQUAL )
			){
				// バージョンアップする表示を出す。
				CanUpGradeFlag = 1;
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// Ｖ２がインストールされているのにサーバーがＶ２未满の场合。
			// ＥＸへのアップデートはなくなりました
			if( ( giInstallVersion >= 2 && PackageVer == PV_NORMAL )
			||  ( giInstallVersion >= 2 && PackageVer == PV_EQUAL )
			){
				// バージョンアップする表示を出す。
				CanUpGradeFlag = 1;
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}else
			// PUK2がインストールされているのにサーバーがV2の场合。
			// ＥＸへのアップデートはなくなりました
#ifdef PUK3_UPGRADE
			if( ( giInstallVersion >= 3 && PackageVer >= PV_FIRST_VER2 && PackageVer <= PV_UP_VER2 ) ){
#else
			if( ( giInstallVersion == 3 && PackageVer >= PV_FIRST_VER2 && PackageVer <= PV_UP_VER2 ) ){
#endif
#ifdef PUK2
				// バージョンアップする表示を出す。
				CanUpGradeFlag = 1;
				SubProcNo = E_CHARLIST_COMMAND_INIT;
#else
				// アップグレード画面へ。
				PaletteChange( 0, 0 );
				ChangeProc( PROC_UPGRADE );
#endif
#ifdef PUK3_UPGRADE
			}else
			// PUK3がインストールされているのにサーバーがPUK2の场合。
			if( ( giInstallVersion == 4 && PackageVer == PV_PUK2 ) ){
				// バージョンアップする表示を出す。
				CanUpGradeFlag = 1;
				SubProcNo = E_CHARLIST_COMMAND_INIT;
#endif
			}else{
				// 正しい。コマンド受付に行ってよし。
				SubProcNo = E_CHARLIST_COMMAND_INIT;
			}
		}
		break;

	// キャラ取得后のコマンド受付开始
	case E_CHARLIST_COMMAND_INIT:
		SubProcNo = E_CHARLIST_COMMAND_WAIT;
#ifndef PUK2
		if( PackageVer >= PV_PUK2 ){
#endif
			PUK2_pActSelGra_init(0);
			PUK2_pActSelGra_init(1);
#ifndef PUK2
		}
#endif
		break;

	// キャラ取得できたのでコマンド待ち状态である。
	case E_CHARLIST_COMMAND_WAIT:
		btnUseFlag = 0;	// このときはボタンが有效である。
		break;


	// 削除处理初期化
	case E_CHARLIST_DELETE_INIT:
		//ret = commonYesNoWindow( 320, 240 );	// ohta
		//ret = commonYesNoWindow( 320, 240, "要清除吗？", FONT_PAL_RED );
		ret = DeleteYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER);
		// はい
		if( ret == 1 ){
			SubProcNo = E_CHARLIST_DELETE_YESNO;
			//削除时にでる信息ウインドウを表示
#ifdef PUK2_NEW_MENU
			//按窗口分辨率计算显示位置(默认坐标130,300)
			ptActMenuWin = makeWindowDisp( SCREEN_WIDTH_CENTER - 190, SCREEN_HEIGHT_CENTER + 60, 340, 52, 4 );
#else
			ptActMenuWin = makeWindowDisp( SCREEN_WIDTH_CENTER - 190, SCREEN_HEIGHT_CENTER + 60, 340, 52, 1 );
#endif
		}else
		// いいえ
		if( ret == 2 ){
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}
		break;

	// 削除处理确认
	case E_CHARLIST_DELETE_YESNO:
		//ret = commonYesNoWindow( 320, 240 );	// ohta
		ret = commonYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER, ML_STRING(318, "真的要删除吗？"), FONT_PAL_WHITE );
		//ret = DeleteYesNoWindow( 320, 240 );

		if(ptActMenuWin->hp >= 1){
			strcpy( msg, ML_STRING(319, "想要删除时，请按着 CTRL键不放　"));
			//按窗口分辨率计算显示位置(默认坐标166,326)
			StockFontBuffer( SCREEN_WIDTH_CENTER - 154, SCREEN_HEIGHT_CENTER + 86, FONT_PRIO_FRONT, FONT_PAL_YELLOW, msg, 0 );
#ifdef PUK2_NEW_MENU
			strcpy( msg, ML_STRING(320, "再点选‘是’。        "));
#else
			strcpy( msg, ML_STRING(320, "再点选‘是’。        "));
#endif
			//按窗口分辨率计算显示位置(默认坐标166,346)
			StockFontBuffer( SCREEN_WIDTH_CENTER - 154, SCREEN_HEIGHT_CENTER + 106, FONT_PRIO_FRONT, FONT_PAL_YELLOW, msg, 0 );
			// はい
			if( ret == 1 ){
				if(VK[VK_CONTROL] & KEY_ON){
					SubProcNo = E_CHARLIST_DELETE_GO;
					// 即死ＳＥ
					play_se( 284, 320, 240 );
					if( ptActMenuWin ){
						DeathAction( ptActMenuWin );
						ptActMenuWin = NULL;
					}
					// 削除处理开始
					initDeleteCharacter();
					SubProcNo = E_CHARLIST_DELETE_GO;
				}
			}else
			// いいえ
			if( ret == 2 ){
				SubProcNo = E_CHARLIST_COMMAND_INIT;	// 取得后に返回
				if( ptActMenuWin ){
					DeathAction( ptActMenuWin );
					ptActMenuWin = NULL;
				}
			}

		}
		break;


	// 削除处理实行
	case E_CHARLIST_DELETE_GO:
		ret = deleteCharacter();	// 削除中
		if( ret == 1 ){
			resetCharacterList( selectPcNo );
			clearUserSetting( selectPcNo );
			if( saveNowState() ){
			}
			if( maxPcNo > 0 )
				maxPcNo--;
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}else
		if( ret == -2 ){
			SubProcNo = E_CHARLIST_DELETE_ERR;
		}
		break;

	// 削除处理初期化
	case E_CHARLIST_GUILDMASTER_INIT:
		ret = GuildMasterWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER);
		if( ret == 1 ){
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}
		break;

	// キャラリスト取得失败
	case E_CHARLIST_LOAD_ERR:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_LOAD_ERR_WAIT;
		break;

	case E_CHARLIST_LOAD_ERR_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_16 ) ){
#else
		if( commonMsgWin( ML_STRING(271, "无法取得角色列表。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// メンテナンス中エラー
	case E_CHARLIST_MAINTE:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_MAINTE_WAIT;
		break;

	case E_CHARLIST_MAINTE_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_17 ) ){
#else
		if( commonMsgWin( ML_STRING(272, "现在服务器维护中。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// ユーザ认证失败处理
	case E_CHARLIST_AUTH_ERR:
		initUserCertifyErrorMsgWin();
		SubProcNo = E_CHARLIST_AUTH_ERR_WAIT;
		break;

	case E_CHARLIST_AUTH_ERR_WAIT:
		if( userCertifyErrorMsgWin() ){
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// キャラ削除失败处理
	case E_CHARLIST_DELETE_ERR:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_DELETE_ERR_WAIT;
		break;

	case E_CHARLIST_DELETE_ERR_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_18 ) ){
#else
		if( commonMsgWin( ML_STRING(273, "无法删除角色。") ) ){
#endif
			// ＯＫボタンが押されたら、キャラクターリストを再取得
			SubProcNo = E_CHARLIST_INIT;
		}
		break;

	// ただしくインストールされてません。
	case E_CHARLIST_INSTALL_ERR:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_INSTALL_ERR_WAIT;
		break;

	case E_CHARLIST_INSTALL_ERR_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_19 ) ){
#else
		if( commonMsgWin( ML_STRING(274, "游戏没有被正常安全。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_TITLE_MENU );
		}
		break;

	// バージョンアップしますか。
	case E_CHARLIST_VERUP_INIT:
		initCommonMsgWin();
		SubProcNo = E_CHARLIST_VERUP_WAIT;
		break;

	case E_CHARLIST_VERUP_WAIT:
		ret = commonYesNoWindow2( 320, 240, 
			ML_STRING(275, "升级吗？"), 
			FONT_PAL_WHITE, 64*6, 48*2 );
		if( ret == 1 ){
			// はいが押された
			ChangeProc( PROC_UPGRADE );
		}else
		if( ret == 2 ){
			// いいえが押された
			SubProcNo = E_CHARLIST_COMMAND_INIT;
		}
		break;
	default:
		break;
	}
#ifdef PUK2
	switch( SubProcNo ){
	//------- まだキャラ取得していない -------
	case E_CHARLIST_INIT:		// 初期化
	case E_CHARLIST_DOWNLOAD:	// 取得中??????
	//----  キャラ取得できなかった场合 ------
	case E_CHARLIST_MAINTE:		// メンテナンス中
	case E_CHARLIST_AUTHERR:		// 认证失败
	case E_CHARLIST_LOAD_ERR:	// キャラリスト取得失败
	case E_CHARLIST_LOAD_ERR_WAIT:// 取得失败の确认
	case E_CHARLIST_MAINTE_WAIT:	// メンテナンス确认
	case E_CHARLIST_AUTH_ERR:	// 认证失败
	case E_CHARLIST_AUTH_ERR_WAIT:	// 认证失败确认
	// -------- キャラ取得できた场合 ---------
	case E_CHARLIST_LOAD_OK:
	case E_CHARLIST_INSTALL_ERR:
	case E_CHARLIST_INSTALL_ERR_WAIT:
		// ＢＧ表示
		titleStockDisp( 0, 0, DISP_PRIO_BG, PUK2_CHARASELECT, 0, 0xffffffff );
		return;
	}
#endif
#ifndef PUK2
	// バージョンアップできそうだったら
	if( CanUpGradeFlag ){
		// 处理をアップグレードに移して終了
		ChangeProc( PROC_UPGRADE );
		return;
	}
#endif
	/* キャラのバッファ分表示处理と入力チェック */
	for( i = 0 ; i < MAXCHARACTER ; i++ ){
		char	name[CHAR_NAME_LEN+1];
		int		inputFlg = PUK2selectCharacterDisp( i, btnUseFlag );

		// 押されたボタンによって处理を分岐
		switch( inputFlg ){
			case 0:		// 新规ボタン
				// 作成キャラ决定
				selectPcNo = i;
				// アルバムの削除
				delAlbum();
				// メールヒストリを削除
				delMailHistory();
				// 家族メールヒストリを削除
				delGuildMailHistory();
				// メールヒストリをファイルに保存
				writeMailFile();
				// 家族メールヒストリをファイルに保存
				writeGuildMailFile();
				ChangeProc( PROC_CHAR_MAKE );
				// 决定ａ
				play_se( SE_NO_OK, 320, 240 );
				// アクション初期化
				DeathAllAction();
				if (pActSelGra[0]) pActSelGra[0]=NULL;
				if (pActSelGra[1]) pActSelGra[1]=NULL;
				break;

			case 1:		// ログインボタン
				// 使用キャラ决定
				selectPcNo = i;

				strcpy( name, chartable[selectPcNo].name );
				sjisStringToEucString( name );
				strcpy( gamestate_login_charname, name );

				// 初期化时にpc.faceGraNoに入れるため
				newCharacterFaceGraNo = chartable[selectPcNo].faceGraNo;
				//loginDp = chartable[selectPcNo].dp;

				ChangeProc( PROC_CHAR_LOGIN_START );
				// ログイン音
				play_se( SE_NO_LOGIN, 320, 240 );
				createCharFlag = 0;
				fade_out_bgm();
				// アクション初期化
				DeathAllAction();
				if (pActSelGra[0]) pActSelGra[0]=NULL;
				if (pActSelGra[1]) pActSelGra[1]=NULL;
				break;
			case 2:		// 削除ボタン
				// 削除キャラ决定
				selectPcNo = i;

				// 家族マスターかどうかをチェック
				if( chartable[selectPcNo].guildTitle == 0 ){
					SubProcNo = E_CHARLIST_GUILDMASTER_INIT;
				}else{
					strcpy( name, chartable[selectPcNo].name );
					sjisStringToEucString( name );
					strcpy( gamestate_deletechar_charname, name );
					SubProcNo = E_CHARLIST_DELETE_INIT;
					// 决定音c（文字等クリック时）
					//play_se( SE_NO_OK3, 320, 240 );
				}
				play_se( SE_NO_NG, 320, 240 );
				break;
			case 3:		// イラスト选择左ボタン
			case 4:		// イラスト选择右ボタン
				// ＰＵＫ２が入っている场合
				if( PackageVer >= PV_PUK2 ){
					// 切り替えられる画像で
					if ( !(chartable[i].faceGraNo==CG2_FACE_SHADOW_PCM || chartable[i].faceGraNo==CG2_FACE_SHADOW_PCF) ){
						int graNo = chartable[i].faceGraNo;

						// 旧モンタージュ
						if( (CG_FACE_0 <= graNo) && (graNo < CG_FACE_17) ){
							chartable[i].faceVer = 0;
						}
						// １キャラPUKモンタージュ
						else if ( (RN_FACE_0<=graNo) && (graNo<RN_FACE_13+100) ){
							chartable[i].faceVer = 1;
						}
						// PUKキャラモンタージュ
						else if ( (V2_FACE_0<=graNo) && (graNo<V2_FACE_13+100) ){
							chartable[i].faceVer = 1;
						}
						// １キャラPUK2モンタージュ
						else if ( (PUK2_FACE_01<=graNo) && (graNo<PUK2_FACE_15) ){
							chartable[i].faceVer = 2;
						}
						// PUKキャラPUK2モンタージュ
						else if ( (PUK2_FACE_15<=graNo) && (graNo<=PUK2_FACE_28+100) ){
							chartable[i].faceVer = 2;
						}
						j = chartable[i].faceVer + ( inputFlg - 3 ) * 2 - 1;

						// 现在のバージョンから目标のバージョンに动かす
						switch( chartable[i].faceVer ){
							case 0:			// 旧グラフィック
								if( j == 1 ){
									chartable[i].faceGraNo = getReFaceGraphic( chartable[i].faceGraNo );
								}else{
									chartable[i].faceGraNo = getReFaceGraphic( chartable[i].faceGraNo );
									chartable[i].faceGraNo = getFaceGraphicToPUK2( chartable[i].faceGraNo );
								}
								break;
							case 1:			// 新グラフィック
								if( j == 2 ){
									// ＰＵＫ２の方に移行する分には问题なし
									chartable[i].faceGraNo = getFaceGraphicToPUK2( chartable[i].faceGraNo );
								}else{
									if( j == 0 ){
										// ＰＵＫキャラ？
										if ( (V2_FACE_0<=graNo) && (graNo<V2_FACE_13+100) ){
											// ＰＵＫ２に
											chartable[i].faceGraNo = getFaceGraphicToPUK2( chartable[i].faceGraNo );
										}else{
											// 旧グラフィックに
											chartable[i].faceGraNo = getOldFaceGraphic( chartable[i].faceGraNo );

										}
									}
								}
								break;
							case 2:			// ＰＵＫ２グラフィック
								if( j == 3 ){
									// 旧キャラ？
									if ( (PUK2_FACE_01<=graNo) && (graNo<PUK2_FACE_15) ){
										chartable[i].faceGraNo = getFaceGraphicFormPUK2( chartable[i].faceGraNo );
										chartable[i].faceGraNo = getOldFaceGraphic( chartable[i].faceGraNo );
									}else{
										chartable[i].faceGraNo = getFaceGraphicFormPUK2( chartable[i].faceGraNo );
									}
								}else{
									chartable[i].faceGraNo = getFaceGraphicFormPUK2( chartable[i].faceGraNo );
								}
								break;
						}
					}
				}
#ifdef PUK2
				// ＰＵＫ２が入っている场合
				else if( PackageVer >= PV_FIRST_VER2 ){
					// 切り替えられる画像で
					if ( !(chartable[i].faceGraNo==CG2_FACE_SHADOW_PCM || chartable[i].faceGraNo==CG2_FACE_SHADOW_PCF) ){
						int graNo = chartable[i].faceGraNo;

						// 旧モンタージュ
						if( (CG_FACE_0 <= graNo) && (graNo < CG_FACE_17) ){
							chartable[i].faceVer = 0;
						}
						// １キャラPUKモンタージュ
						else if ( (RN_FACE_0<=graNo) && (graNo<RN_FACE_13+100) ){
							chartable[i].faceVer = 1;
						}
						j = chartable[i].faceVer + ( inputFlg - 3 ) * 2 - 1;

						// 现在のバージョンから目标のバージョンに动かす
						switch( chartable[i].faceVer ){
							case 0:			// 旧グラフィック
								chartable[i].faceGraNo = getReFaceGraphic( chartable[i].faceGraNo );
								break;
							case 1:			// 新グラフィック
								// 旧グラフィックに
								chartable[i].faceGraNo = getOldFaceGraphic( chartable[i].faceGraNo );
								break;
						}
					}
				}
#endif
				play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				break;
			case 5:		// キャラグラフィック选择左ボタン
			case 6:		// キャラグラフィック选择左ボタン
				if (chartable[i].isRenewal==0){
					chartable[i].isRenewal=1;
				}else if (chartable[i].isRenewal==1){
					chartable[i].isRenewal=0;
				}
				giCreateRenewalFlg = chartable[i].isRenewal;
				PUK2_pActSelGra_init(i);
				play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				break;
			default:
				break;
		}
	}

#ifdef PUK2
	if (CanUpGradeFlag){
		/* Ｕｐｄａｔｅキーのチェック */
		if( !btnUseFlag && SubProcNo == E_CHARLIST_COMMAND_WAIT ){
			//アップデートボタンを押したとき
			if( (PUK2pushGraBtnInfo( pUpdateBtn ) & BTN_LEFT_CLICK) ){
				// 处理をアップグレードに移す
				ChangeProc( PROC_UPGRADE );
				btnUseFlag = 1;
			}
		}
		// Ｕｐｄａｔｅキーの表示
		PUK2drawGraBtnInfo( pUpdateBtn, DISP_PRIO_BG+2, 0, 0, btnUseFlag );

		//根据分辨率计算资料片更新栏的显示坐标(默认252,392)
		titleStockDisp( SymOffsetX + 252, SymOffsetY + 392, DISP_PRIO_BG+1, PUK2_UPDATEBTNBASE, 0, 0xffffffff );
	}
#endif
	/* Ｂａｃｋキーのチェック */
	if( !btnUseFlag && SubProcNo == E_CHARLIST_COMMAND_WAIT ){
		//返回按键を押したとき
		if( (PUK2pushGraBtnInfo( pBackBtn ) & BTN_LEFT_CLICK) ){
#ifdef PUK2
			// アクション初期化
			DeathAllAction();
			if (pActSelGra[0]) pActSelGra[0]=NULL;
			if (pActSelGra[1]) pActSelGra[1]=NULL;
#endif
			// ネットワーク終了设定
			cleanupNetwork();
			ChangeProc( PROC_TITLE_MENU );
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
#ifndef PUK2
			// アクション初期化
			DeathAllAction();
			if (pActSelGra[0]) pActSelGra[0]=NULL;
			if (pActSelGra[1]) pActSelGra[1]=NULL;
#endif
		}
	}
	// Ｂａｃｋキーの表示
	PUK2drawGraBtnInfo( pBackBtn, DISP_PRIO_BG+2, 0, 0, btnUseFlag );

	// ＢＧ表示
	titleStockDisp( 0, 0, DISP_PRIO_BG, PUK2_CHARASELECT, 0, 0xffffffff );
}



//------------------------------------------------------------
//  アップグレードフォーム
//------------------------------------------------------------
#if 0
#define TOEX_BUTTON_X	320 //243
#define TOEX_BUTTON_Y	240 //(427-32)

#define TOVER2_BUTTON_X	320	//390
#define TOVER2_BUTTON_Y	240 //(427-32)

#define TOCANCEL_BUTTON_X	320
#define TOCANCEL_BUTTON_Y	240//(427)
	// ＥＸボタン
	GRA_BTN_INFO2 ToExBtn ={ 
		TOEX_BUTTON_X, TOEX_BUTTON_Y,	// 表示座标(x,y)
		342, 251,	// 范围始点座标(x,y)
		290, 60,		// 范围幅(w,h)
		CG_CHAR_UPG_TOEX_UP, CG_CHAR_UPG_TOEX_DOWN	// ボタン画像番号(凸画像,凹画像)
	};
	// ＶＥＲ２ボタン
	GRA_BTN_INFO2 ToVer2Btn =	{	
		TOVER2_BUTTON_X, TOVER2_BUTTON_Y,	// 表示座标(x,y)
		342, 316,	// 范围始点座标(x,y)
		290, 60,		// 范围幅(w,h)
		CG_CHAR_UPG_TOVER2_UP, CG_CHAR_UPG_TOVER2_DOWN	// ボタン画像番号(凸画像,凹画像)
	};
	// キャンセルボタン
	GRA_BTN_INFO2 ToCancelBtn =	{	
		TOCANCEL_BUTTON_X, TOCANCEL_BUTTON_Y,	// 表示座标(x,y)
		292,426,	// 范围始点座标(x,y)
		72,  44,	// 范围幅(w,h)
		0, CG_CHAR_UPG_BACK	// ボタン画像番号(凸画像,凹画像)
	};

enum{
	E_UPGRADE_INIT,			// 初期化
	E_UPGRADE_COMMAND_INIT,	// 
	E_UPGRADE_COMMAND_WAIT,	// 入力モード
	E_UPGRADE_VERUP_INIT,	// バージョンアップ
	E_UPGRADE_VERUP_DOING,	// バージョンアップ中


	E_UPGRADE_VERUP_OK = 100,	// バージョンアップＯＫ
	E_UPGRADE_VERUP_OK_WAIT,	// バージョンアップＯＫ确认
	E_UPGRADE_VERUP_ERR = 200,	// バージョンエラー
	E_UPGRADE_VERUP_ERR_WAIT,	// バージョンエラー确认

	E_UPGRADE_ALREADY,		// すでにそのバージョンです。
	E_UPGRADE_ALREADY_WAIT,	// すでにそのバージョンです。

};

char UpGradeWaitMsg[] = ML_STRING(285, "升级中...");

INPUT_STR Upgrade_Product; // プロダクトキー入力バッファ
#endif

// プロト种类宣言
#define COMMONCHIP_W 74
#define COMMONCHIP_H 54
void MenuWindowCommonDraw( int GraNo, short x, short y, short w, short h, int DispPrio, unsigned long frame_rgba, unsigned long back_rgba,
	char sizeX = COMMONCHIP_W, char sizeY = COMMONCHIP_H );



void PUK2_upgradeProc( void )
{
	int		i;
	static ACTION *ptActMenuWin = NULL;
	int flag = 0;
	int selUseFlag = 0;
	char	msg[256];

	PUK2_BTN_INFO pOkBtn =
	{// 范围始点座标(x,y)
		SCREEN_WIDTH_CENTER+74, SCREEN_HEIGHT_CENTER+54/2,
	 // 范围幅(w,h)
		64, 16,
	 // ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_OK0, PUK2_BUTTON_OK2, PUK2_BUTTON_OK1
	};
	PUK2_BTN_INFO pBackBtn =
	{// 范围始点座标(x,y)
		//根据分辨率计算显示坐标(默认256+74, 240+54/2)
		SCREEN_WIDTH_CENTER-64+74, SCREEN_HEIGHT_CENTER+54/2,
	 // 范围幅(w,h)
		64, 16,
	 // ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK2, PUK2_BUTTON_BACK1
	};


	switch( SubProcNo ){

	case E_UPGRADE_INIT:
		ptActMenuWin = NULL;
		// 入力フォーム初期化
		InitInputStr( &Upgrade_Product, SCREEN_WIDTH_CENTER-185+16+4, SCREEN_HEIGHT_CENTER-54+54+4, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
		FONT_PAL_WHITE, NULL, 1, 24, 0, 0 );
		SubProcNo = E_UPGRADE_COMMAND_WAIT;
		break;

	case E_UPGRADE_COMMAND_INIT:
		SubProcNo = E_UPGRADE_COMMAND_WAIT;
		break;
	case E_UPGRADE_COMMAND_WAIT:
		// フォーカス设定
		GetKeyInputFocus( &Upgrade_Product );
		// 文字列入力されてなかったらボタン押せなくする。
		if( strlen( Upgrade_Product.buffer ) <= 0 ){
			flag = 0;
		}else{
			flag = 1;
		}
		//----------- ボタン入力 ------------

		//バッファの最后に終了マークを付ける
		Upgrade_Product.buffer[Upgrade_Product.cnt] = '\0';

#if 0
		// ＥＸへのバージョンアップはなくなりました。
		//----------------------
		// ＥＸボタンが押された
		//----------------------
		if(	PackageVer < PV_FIRST_EX // Ｖ２以上だったら表示しない。
		&&	giInstallVersion >= 1  // 自分がＥＸ入れてたら
//		&& (pushGraBtnInfo2( &ToExBtn ) & BTN_LEFT_CLICK) && flag 
		&& (PUK2pushGraBtnInfo( &pOkBtn ) & BTN_LEFT_CLICK) && flag
//		&& T_TextButton( 100, 300, 180, 330, "ＴｏＥＸ" ) == 1 && flag 
		){
			int kind = 0;
			// 自分が通常だったら
			if( PackageVer == PV_NORMAL || PackageVer == PV_EQUAL ){
				kind = 1; // 通常からＥＸへ
				nrproto_PVUP_send( sockfd, kind, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else{
				// それ以外は必要ないよ。
				SubProcNo = E_UPGRADE_ALREADY;
			}
		}else
#endif
		//----------------------
		// Ｖ２ボタンが押された
		//----------------------
		if(	giInstallVersion >= 2 
//		&& (pushGraBtnInfo2( &ToVer2Btn ) & BTN_LEFT_CLICK) && flag 
		&& (PUK2pushGraBtnInfo( &pOkBtn ) & BTN_LEFT_CLICK) && flag
//		&& T_TextButton( 400, 300, 475, 330, "ＴｏＶ２" ) == 1 && flag 
		){
			int kind = 0;
#ifdef PUK3_UPGRADE
			// 自分がＰＵＫ２だったら
			if( PackageVer == PV_PUK2 ){
				// 通常からＶ２へ
				nrproto_PVUP_send( sockfd, 5, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else
#endif
#ifdef PUK2
			// 自分がＶ２だったら
			if( PackageVer == PV_FIRST_VER2 || PackageVer == PV_UP_VER2 ){
				// 通常からＶ２へ
				nrproto_PVUP_send( sockfd, 4, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else
#endif
			// 自分が通常または卒业济みだったら
			if( PackageVer == PV_NORMAL || PackageVer == PV_EQUAL 
//			||  PackageVer == PV_FIRST_EX || PackageVer == PV_UP_EX 
			){
				// 通常からＶ２へ
				nrproto_PVUP_send( sockfd, 2, Upgrade_Product.buffer );
				// バージョンアッププロトコル送る。。
				SubProcNo = E_UPGRADE_VERUP_INIT;
			}else{
				// それ以外は必要ないよ。
				SubProcNo = E_UPGRADE_ALREADY;
			}
		}else
		//----------------------
		// キャンセルされた
		//----------------------
//		if( (pushGraBtnInfo2( &ToCancelBtn ) & BTN_LEFT_CLICK) ) 
		if( PUK2pushGraBtnInfo( &pBackBtn ) & BTN_LEFT_CLICK )
		{
#ifdef PUK2
			// キャラクターセレクト画面へ返回
			ChangeProc( PROC_CHAR_SELECT );
			SubProcNo = E_CHARLIST_COMMAND_WAIT;
#else
			PaletteChange( 29, 0 );	// フェードイン
			ChangeProc( PROC_TITLE_MENU );
#endif
		}
		break;

	case E_UPGRADE_VERUP_INIT:	// バージョンアップ开始
		ptActMenuWin = NULL;
		{	
			// ウインドウ表示
			int w, h, x, y;
			// ウィンドウ作成
			w = (GetStrWidth( UpGradeWaitMsg, FONT_KIND_MIDDLE )+48+63)/64;
			if( w < 2 )w = 2;
			h = (16+47)/48;
			if( h < 2 )h = 2;
			x = (DEF_APPSIZEX-w*64)/2;
			y = (DEF_APPSIZEY-h*48)/2;
#ifdef PUK2_NEW_MENU
			ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 4 );
#else
			ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
#endif
		}
		SubProcNo = E_UPGRADE_VERUP_DOING;
		break;

	case E_UPGRADE_VERUP_DOING:	// バージョンアップ中??????
		// このままサーバーから应答がなかったらあきらめます。
		if( ptActMenuWin != NULL && ptActMenuWin->hp == 1 ){	// ウインドウが开いたら文字表示
			int xx, yy;
			//根据分辨率计算显示位置(默认320 232)
			xx = SCREEN_WIDTH_CENTER - (GetStrWidth( UpGradeWaitMsg, FONT_KIND_MIDDLE ))/2;
			yy = SCREEN_HEIGHT_CENTER - 8;
			StockFontBuffer( xx, yy, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
			UpGradeWaitMsg, 0, 0 );
		}
		break;

	case E_UPGRADE_VERUP_ERR:	// バージョンアップ失败
		if( ptActMenuWin != NULL )DeathAction( ptActMenuWin );
#ifdef PUK3_CHECK_VALUE
		ptActMenuWin = NULL;
#endif

		initCommonMsgWin();
		SubProcNo = E_UPGRADE_VERUP_ERR_WAIT;
		break;
	case E_UPGRADE_VERUP_ERR_WAIT:	// バージョンアップ失败确认
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_20 ) ){
#else
		if( commonMsgWin( ML_STRING(286, "升级失败。") ) ){
#endif
			// ＯＫボタンが押された
			SubProcNo = E_UPGRADE_COMMAND_INIT;
		}
		break;

	case E_UPGRADE_VERUP_OK:	// バージョンアップ成功
		if( ptActMenuWin != NULL )DeathAction( ptActMenuWin );
#ifdef PUK3_CHECK_VALUE
		ptActMenuWin = NULL;
#endif

		SubProcNo = E_UPGRADE_VERUP_OK_WAIT;
		initCommonMsgWin();
		break;
	case E_UPGRADE_VERUP_OK_WAIT:	// バージョンアップ成功
		if( commonMsgWin( ML_STRING(287, "升级成功了。") ) ){
#ifdef PUK2
			// アクション初期化
			DeathAllAction();
			if (pActSelGra[0]) pActSelGra[0]=NULL;
			if (pActSelGra[1]) pActSelGra[1]=NULL;
#endif
			// キャラリスト再取得
			ChangeProc( PROC_CHAR_SELECT );
		}
		break;


	// すでにそのバージョンです。
	case E_UPGRADE_ALREADY:
		SubProcNo = E_UPGRADE_ALREADY_WAIT;
		initCommonMsgWin();
		break;
	case E_UPGRADE_ALREADY_WAIT:
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_21 ) ){
#else
		if( commonMsgWin( ML_STRING(288, "无法从这个版本升级。") ) ){
#endif
			// ＯＫボタンが押された
			SubProcNo = E_UPGRADE_COMMAND_INIT;
		}
		break;

	default :
		break;
	}
	// 一旦ここでフォントのリンクをぶった切る
	FntBufPointer[FONT_PRIO_WIN] = NULL;

	// キャラのバッファ分表示处理
	for( i = 0 ; i < MAXCHARACTER ; i++ ){
		PUK2selectCharacterDisp( i, 1 );
	}

	//----------------  画面表示 -------------------
#if 0
	if(	PackageVer < PV_FIRST_EX 	// ＥＸ未满の人
	&&	PackageVer != PV_TRIAL 		// 见习いだったら表示しない。
	&&	giInstallVersion < 2		// インストールバージョンがＶ２未满
	){ 
		// ＥＸボタン
		drawGraBtnInfo2( &ToExBtn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
	}

	if(	PackageVer < PV_FIRST_VER2 	// Ｖ２未满の人
	&&	PackageVer != PV_TRIAL 		// 见习いだったら表示しない。
	&&	giInstallVersion >= 2 		// インストールバージョンがＶ２以上
	){
		// ＶＥＲ２ボタン
		drawGraBtnInfo2( &ToVer2Btn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
	}
#ifdef PUK2
	else
	if(	PV_FIRST_VER2<=PackageVer && PackageVer<=PV_UP_VER2 	// Ｖ２の人
	&&	giInstallVersion >= 3 		// インストールバージョンがPUK2以上
	){
		if( strlen( Upgrade_Product.buffer ) > 0 ){
			// ＶＥＲ２ボタン
			drawGraBtnInfo2( &ToVer2Btn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
		}
	}
#endif
	// キャンセルボタン
	drawGraBtnInfo2( &ToCancelBtn, DISP_PRIO_CHAR, 0, 0, selUseFlag );
#endif

	// 入力した文字列を表示
	StockFontBuffer2( &Upgrade_Product );
	//---------------------------------------------

	// 入力バッファ
	titleStockDisp( SCREEN_WIDTH_CENTER-185+16, SCREEN_HEIGHT_CENTER-54+54, DISP_PRIO_BOX, 243225, 0, 0xffffffff );
	// 枠表示
	MenuWindowCommonDraw( 245310, SCREEN_WIDTH_CENTER-74*5/2, SCREEN_HEIGHT_CENTER-54*2/2, 74*5, 54*2, DISP_PRIO_BOX, 0xffffffff, 0x98ffffff );

	// 说明文
	msg[0] = '\0';
	if(	PackageVer < PV_FIRST_VER2 	// Ｖ２未满の人
		&&	PackageVer != PV_TRIAL	// 见习いだったら表示しない。
		&&	giInstallVersion >= 2	// インストールバージョンがＶ２以上
	){
#ifdef PUK2
		sprintf( msg, "请输入UpgradeProductkey" );                             //MLHIDE
#else
		sprintf( msg, "请输入ＰＵＫ UpgradePass。" );                              //MLHIDE
#endif
	}else
	if( PV_FIRST_VER2<=PackageVer && PackageVer<=PV_UP_VER2 	// Ｖ２の人
		&&	giInstallVersion >= 3	// インストールバージョンがPUK2以上
	){
#ifdef PUK2
		sprintf( msg, "请输入PUK2 UpgradeProductkey" );                        //MLHIDE
#else
		sprintf( msg, "请输入ＰＵＫ２UpgradePass。" );                              //MLHIDE
#endif
	}
#ifdef PUK3_UPGRADE
	else if(  PackageVer == PV_PUK2 	// ＰＵＫ２の人
		&&	giInstallVersion >= 4	// インストールバージョンがPUK3以上
	){
		sprintf( msg, "请输入PUK3 UpgradeProductkey" );                        //MLHIDE
	}
#endif
	if( msg[0] != '\0' ){
		StockFontBuffer( SCREEN_WIDTH_CENTER-185+16, SCREEN_HEIGHT_CENTER-54+24, FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_WHITE, msg, 0 );
	}

	// ＯＫボタン
	PUK2drawGraBtnInfo( &pOkBtn, DISP_PRIO_BOX+1, 0, 0, selUseFlag );

	// ＢＡＣＫボタン
	PUK2drawGraBtnInfo( &pBackBtn, DISP_PRIO_BOX+1, 0, 0, selUseFlag );

	// ＢＧ表示
	titleStockDisp( 0, 0, DISP_PRIO_BG, PUK2_CHARASELECT, 0, 0xffffffff );
}

