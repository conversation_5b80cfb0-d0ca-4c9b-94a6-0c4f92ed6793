﻿/************************/
/*	action.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/main.h"
#include "../systeminc/pattern.h"
#if defined(PUK3_ACTION_CHECKSUM) || defined(PUK3_ACTION_CHECKRANGE)
#include "../systeminc/debug.h"
#endif

/* はじめと后のリストポインタ */
ACTION *pActTop;
ACTION *pActBtm;
#ifdef PUK3_ACTION_CHECKSUM
ACTION *pActDel;
#endif
#ifdef PUK3_ACTION_CHECKRANGE

#define _USESTATICBUF

#define ACTION_BUFFSIZE 30000
ACTION pActFreeTop;
ACTION pActFreeBtm;
#ifdef _USESTATICBUF
ACTION pActBuf[ACTION_BUFFSIZE];
#else
ACTION *pActBuf;
#endif

#endif

// アクションスローフラグ
int	ActSlowFlag = FALSE;
int ActSlowCnt = FALSE;

#ifdef _DEBUG		
/* アクション数カウント */
int ActCnt;
#endif

#ifdef PUK3_ACTION_CHECKSUM
void makeCheckSum( ACTION *pAct, int *pchecksum, int *ppYobichecksum )
{
	int checksum, pYobichecksum;
	char *p;
	int i;

	// 先ずはpYobiのチェックサム作成
	pYobichecksum = 0;
	p = (char *)pAct->pYobi;
	if ( p ){
		for( i = 0; i < pAct->db.pYobiSize; i++ ){
			pYobichecksum += p[i];
		}
	}

	// 次はpActのチェックサム作成
	checksum = 0;
	p = (char *)pAct;
	for( i = 0; i < sizeof(ACTION); i++ ){
		checksum += p[i];
	}

	// pPrev、pNext、dbのデータを除く
	// この３つに关しては、チェックサム生成后に变更する可能性あり
	p = (char *)&pAct->pPrev;
	for( i = 0; i < sizeof(pAct->pPrev); i++ ){
		checksum -= p[i];
	}
	p = (char *)&pAct->pNext;
	for( i = 0; i < sizeof(pAct->pNext); i++ ){
		checksum -= p[i];
	}
	p = (char *)&pAct->db;
	for( i = 0; i < sizeof(pAct->db); i++ ){
		checksum -= p[i];
	}

	// チェックサムを返す
	*pchecksum = checksum;
	*ppYobichecksum = pYobichecksum;
}
#endif
void AnimePartsInit(void);

/* アクションリスト初期化 *****************************************************/
void InitAction( void )
{
	/* 最初と最后のポインタ取得 */
	pActTop = ( ACTION * )calloc( 1, sizeof( ACTION ) );
#ifdef PUK2_MEMCHECK
	memlistset( pActTop, MEMLISTTYPE_ACTION );
#endif
	pActBtm = ( ACTION * )calloc( 1, sizeof( ACTION ) );
#ifdef PUK2_MEMCHECK
	memlistset( pActBtm, MEMLISTTYPE_ACTION );
#endif
#ifdef PUK3_ACTION_CHECKSUM
		pActDel = ( ACTION * )calloc( 1, sizeof( ACTION ) );
	#ifdef PUK2_MEMCHECK
		memlistset( pActDel, MEMLISTTYPE_ACTION );
	#endif
#endif

	pActTop->pPrev = NULL;	 	/* 前のリストは无し */
	pActTop->pNext = pActBtm;	/* 次のリストは最后尾 */
	pActTop->func  = NULL;		/* 实行关数は无し */
	pActTop->prio  = PRIO_TOP;	/* 优先顺位最大 */
	pActTop->bmpNo = -1;		/* ＢＭＰ番号初期化 */
	
	pActBtm->pPrev = pActTop;	/* 前のリストは先头 */
	pActBtm->pNext = NULL;		/* 次のリストは无し */
	pActBtm->func  = NULL;		/* 实行关数は无し */
	pActBtm->prio  = PRIO_BTM;	/* 优先顺位最低 */
	pActBtm->bmpNo = -1;		/* ＢＭＰ番号初期化 */
	
#ifdef PUK3_ACTION_CHECKSUM
	pActDel->pPrev = NULL;		/* 前のリストは无し */
	pActDel->pNext = NULL;		/* 次のリストは最后尾 */
	pActDel->func  = NULL;		/* 实行关数は无し */
	pActDel->prio  = PRIO_TOP;	/* 优先顺位最大 */
	pActDel->bmpNo = -1;		/* ＢＭＰ番号初期化 */
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	{
		int i;

		pActFreeTop.pPrev = NULL;	 	/* 前のリストは无し */
		pActFreeTop.pNext = &pActFreeBtm;	/* 次のリストは最后尾 */
		pActFreeTop.func  = NULL;		/* 实行关数は无し */
		pActFreeTop.prio  = PRIO_TOP;	/* 优先顺位最大 */
		pActFreeTop.bmpNo = -1;			/* ＢＭＰ番号初期化 */

		pActFreeBtm.pPrev = &pActFreeTop;	/* 前のリストは先头 */
		pActFreeBtm.pNext = NULL;		/* 次のリストは无し */
		pActFreeBtm.func  = NULL;		/* 实行关数は无し */
		pActFreeBtm.prio  = PRIO_BTM;	/* 优先顺位最低 */
		pActFreeBtm.bmpNo = -1;			/* ＢＭＰ番号初期化 */

#ifdef _USESTATICBUF
#else
		pActBuf = ( ACTION * )calloc( ACTION_BUFFSIZE, sizeof( ACTION ) );
#endif
		// 空リスト初期化
		for( i = 0; i < ACTION_BUFFSIZE; i++ ){
			pActBuf[i].pPrev = pActFreeBtm.pPrev;
			pActBuf[i].pNext = &pActFreeBtm;

			pActFreeBtm.pPrev->pNext = &pActBuf[i];
			pActFreeBtm.pPrev = &pActBuf[i];

			pActBuf[i].prio  = PRIO_CHR;
			pActBuf[i].db.deathState = eACTION_DEBUG_DEATH_STATE_FREE;
		}
	}
#endif
	//MessageBox( hWnd, "アクション初期化完了。", "确认", MB_OK );
}

/* アクションリストに登録 *****************************************************/
#if defined(PUK3_ACTION_CHECKSUM) || defined(PUK3_ACTION_CHECKRANGE)
	#ifdef PUK2_MEMCHECK
	ACTION *_GetAction( UCHAR prio, UINT yobiSize, char *file, int line, enum ACT_TYPE actType )
	#else
	ACTION *_GetAction( UCHAR prio, UINT yobiSize, char *file, int line )
	#endif
#else
#ifdef PUK2_MEMCHECK
ACTION *GetAction( UCHAR prio, UINT yobiSize, enum ACT_TYPE actType )
#else
ACTION *GetAction( UCHAR prio, UINT yobiSize )
#endif
#endif
{
	ACTION *pAct;
	ACTION *pActLoop;
	
#ifdef PUK3_ACTION_CHECKRANGE
	pAct = NULL;
	if ( pActFreeTop.pNext != &pActFreeBtm ){
		// 空リストから取ってくる
		pAct = pActFreeTop.pNext;

		pActFreeTop.pNext = pAct->pNext;
		pAct->pNext->pPrev = &pActFreeTop;

		// 内容をゼロで初期化
		memset( pAct, 0, sizeof(ACTION) );
	}
#else
	/* アクション构造体メモリ确保 */
	pAct = ( ACTION * )calloc( 1, sizeof( ACTION ) );
#endif
	if( pAct == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_57, "确认", MB_OK );                         //MLHIDE
		MessageBox( hWnd, ERRMSG_58, "GetAction Erorr", MB_OK );            //MLHIDE
#else
		MessageBox( hWnd, "GetAction Erorr!!", "确认", MB_OK );               //MLHIDE
		MessageBox( hWnd, "メモリの空き容量が足りません！pAct", "GetAction Erorr", MB_OK ); //MLHIDE
#endif
		return NULL;
	}
#ifdef PUK2_MEMCHECK
	memlistset( pAct, MEMLISTTYPE_ACTION );
#endif
	/* 予备构造体使う时 */
	if( yobiSize > 0 ){
		/* 予备构造体メモリ确保 */
		pAct->pYobi = calloc( 1, yobiSize );
		// 失败したら
		if( pAct->pYobi == NULL ){
#ifdef PUK2_MEMCHECK
			memlistrel( pAct, MEMLISTTYPE_ACTION );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
			// 空リストに戾す
			pAct->pPrev = pActFreeBtm.pPrev;
			pAct->pNext = &pActFreeBtm;

			pActFreeBtm.pPrev->pNext = pAct;
			pActFreeBtm.pPrev = pAct;

			pActFreeBtm.prio  = PRIO_CHR;
#else
			/* アクション构造体のメモリも解放 */
			free( pAct );
#endif
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_59, "GetYobi Erorr", MB_OK );             //MLHIDE
#else
			MessageBox( hWnd, "メモリの空き容量が足りません！yobi", "GetYobi Erorr", MB_OK ); //MLHIDE
#endif
			return NULL;
		}
#ifdef PUK2_MEMCHECK
		memlistset( pAct->pYobi, MEMLISTTYPE_ACTIONYOBI );
#endif
#ifdef PUK2
		memset( pAct->pYobi, 0, yobiSize );
#endif
	}
#ifdef PUK2_MEMCHECK
	pAct->actType = actType;
#endif
#ifdef PUK3_ACTION_CHECKSUM
	// pYobiのデータサイズ记忆
	// 解放时のチェックサム生成と确认に使用
	pAct->db.pYobiSize = yobiSize;
#endif
	
	/* 初期化 */
	pAct->func  = NULL;			/* 实行关数は无し */
	pAct->prio  = prio;			/* 优先顺位 */
	pAct->bmpNo = -1;			/* ＢＭＰ番号初期化 */
	pAct->hitDispNo = -2;		/* マウスカーソル当たり判定番号初期化 */
	pAct->anim_speed = ANM_NOMAL_SPD;	/* デフォルトアニメーションスピード */
	pAct->scaleX = 1.0;			// 扩大缩小スケールＸ（初期值１．０）
	pAct->scaleY = 1.0;			// 扩大缩小スケールＸ（初期值１．０）
#ifdef PUK2
	pAct->bm.u=0;				// u 值なし
	pAct->bm.v=0;				// v 值なし
	pAct->bm.w=0;				// w 值なし
	pAct->bm.h=0;				// h 值なし
	pAct->bm.rgba.rgba=0xffffffff;	// 色、透过设定なし
	pAct->bm.BltVer=0;			// マップバージョン指定なし
	pAct->bm.bltf=0;			// 绘图设定なし
	pAct->bm.PalNo=0;			// アクションパレット不使用
	pAct->rgbaon=0;				// プログラム中での色、透过设定变更をしない
	pAct->rgba.rgba=0xffffffff;	// プログラム中での色、透过设定なし
	pAct->bltfon=0;				// プログラム中での绘图设定变更をしない
	pAct->bltf=0;				// プログラム中での绘图设定なし
#endif

#ifdef PUK3_RIDE		// ライドの移动テスト
	pAct->walkSpeed = 100;
#endif

#ifdef PUK3_ACTION_CHECKRANGE
	pAct->db.deathState = eACTION_DEBUG_DEATH_STATE_ALIVE;
#endif
#if defined(PUK3_ACTION_CHECKSUM) || defined(PUK3_ACTION_CHECKRANGE)
	// 呼び出し元记忆
	strcpy( pAct->db.getPos_file, file );
	pAct->db.getPos_line = line;
#endif

	
	/* 优先顺に并べる */
	for( pActLoop = pActTop->pNext ; pActLoop != pActBtm->pNext ; 
		pActLoop = pActLoop->pNext ){
		
		/* 优先度が低い时 */
		if( pActLoop->prio > prio ){
			/* この上に追加 */
			/* 前后から教えてもらう */
			pAct->pPrev = pActLoop->pPrev;	/* 前のリスト */
			pAct->pNext = pActLoop;			/* 次のリスト */
			/* 前后に教える */
			pActLoop->pPrev->pNext = pAct;	/* 前のリスト */
			pActLoop->pPrev = pAct;			/* 次のリスト */
#ifdef _DEBUG		
			/* アクション数カウント */
   			ActCnt++;
#endif			
			break;
		}
	}
	
	return pAct;
}
#ifdef PUK3_ACTION_CHECKRANGE

BOOL _CheckAction( ACTION *pAct, char *file, int line )
{
	int dif;
	BOOL rangeFlag = FALSE;

	dif = (int)pAct - (int)&pActBuf[0];
	if ( 0 <= dif &&
		 dif % sizeof(pActBuf[0]) == 0 &&
		 dif / sizeof(pActBuf[0]) < ACTION_BUFFSIZE ){
		rangeFlag = TRUE;
		// 范围チェック終わってないポインタ参照はまずいのでこの位置でチェック
		if ( pAct->db.deathState == eACTION_DEBUG_DEATH_STATE_ALIVE ){
			return TRUE;
		}
	}

	// エラー处理
	{
		char str[256];

		if ( rangeFlag ){
			sprintf( str, "pAct 0x%08x [0x%08x < Buf < 0x%08x] : %-6d %s",     //MLHIDE
				 (int)pAct,
				 (int)&pActBuf[0], (int)&pActBuf[0] + ACTION_BUFFSIZE * sizeof(pActBuf[0]),
				 line, file );
			WriteSystemLogfile( str );

			sprintf( str, "\t %d %-6d %s",                                     //MLHIDE
				 pAct->db.deathState, pAct->db.getPos_line, pAct->db.getPos_file );
			WriteSystemLogfile( str );
		}else{
			sprintf( str, "pAct 0x%08x [0x%08x < Buf < 0x%08x] : %-6d %s",     //MLHIDE
				 (int)pAct,
				 (int)&pActBuf[0], (int)&pActBuf[0] + ACTION_BUFFSIZE * sizeof(pActBuf[0]),
				 line, file );
			WriteSystemLogfile( str );
		}

		MessageBox( hWnd, "不正なメモリをアクセスしようとしています。", "CrossGate", MB_OK );    //MLHIDE
	}
	return FALSE;
}

#endif

/* アクション走らせる *********************************************************/
void RunAction( void )
{
	ACTION *pActLoop = pActTop->pNext; 	/* 先头のリストポインタ取得 */
	ACTION *pActLoopBak;  /* 死亡时バックアップ用 */
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_RunAction );
#endif
#ifdef PUK2_MEMCHECK
	ACTION *pActLoop2;
	// 处理ループ	
	while( 1 ){
		/* 最后尾が来るまでループ */	
		if( pActLoop == pActBtm ) break;
		pActLoop2 = pActLoop->pNext;
		while(1){
			/* 最后尾が来るまでループ */	
			if( pActLoop2 == pActBtm ) break;
			if ( pActLoop2 == pActLoop ){
				pActLoop2 = pActLoop2;
			}
			if ( pActLoop->pYobi && pActLoop2->pYobi == pActLoop->pYobi ){
				pActLoop2 = pActLoop2;
			}
			/* 次のポインタをセット */
			pActLoop2 = pActLoop2->pNext;
		}
		/* 次のポインタをセット */
		pActLoop = pActLoop->pNext;
	}
	pActLoop = pActTop->pNext;
#endif
	
	// 处理ループ	
	while( 1 ){
	
		/* 最后尾が来るまでループ */	
		if( pActLoop == pActBtm ) break;

		/* 生きていたら */
		if( pActLoop->deathFlag == FALSE ){
		
			/* アドレスがあったら实行 */
			if( pActLoop->func != NULL ){
				// アクションスローフラグ立っている时
				if( pActLoop->atr & ACT_ATR_ACT_SLOW ){
					// スローフラグＯＮの时
					if( ActSlowFlag == TRUE ){
						// スローカウントが０の时
						if( ActSlowCnt == 0 ) pActLoop->func( pActLoop );
					// スローフラグＯＦＦの时は实行
					}else pActLoop->func( pActLoop );
				}else{
					pActLoop->func( pActLoop );
				}
			}
			
			/* 次のポインタをセット */
			pActLoop = pActLoop->pNext;
			
		}else{	/* 死んでいたら */
		
#ifdef PUK2_BATTLE_SLOW
			// こうでないとdeathFlagを参照してる奴が参照できない场合がある。
			// アクションスローフラグ立っている时
			if( pActLoop->atr & ACT_ATR_ACT_SLOW ){
				// スローフラグＯＮの时
				if( ActSlowFlag == TRUE ){
					// スローカウントが０の时
					if( ActSlowCnt == 0 );
					else{
						/* 次のポインタをセット */
						pActLoop = pActLoop->pNext;
						continue;
					}
				}
			}
#endif
			/* 前后に教える */
			pActLoop->pPrev->pNext = pActLoop->pNext;
			pActLoop->pNext->pPrev = pActLoop->pPrev;
			/* バックアップ */
			pActLoopBak = pActLoop->pNext;
			/* リストから抹杀 */
			ClearAction( pActLoop );
			/* バックアップ戾す */
			pActLoop = pActLoopBak;
		}
	}
	
	// アクションスローフラグ进める
	ActSlowCnt++;
	if( ActSlowCnt >= ACT_SLOW_WAIT ) ActSlowCnt = 0;
	
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

/* アクションリストから抹杀准备 ***********************************************/
void DeathAction( ACTION *pAct )
{
	if( pAct == NULL ) return;
#ifdef PUK3_ACTION_CHECKRANGE
	if( !CheckAction( pAct ) ) return;
#endif
	pAct->deathFlag = TRUE;
}

/* アクションリストから完全抹杀 ***********************************************/
#ifdef PUK3_ACTION_CHECKSUM
void ClearAction( ACTION *pAct )
{
	int checksum, pYobichecksum;
	ACTION *pPrev, *pNext;
	void *pYobi;
	ACTION_DEBUG db;

	// 解放リストへ追加
	pAct->pPrev = pActDel;
	pAct->pNext = pActDel->pNext;
	if ( pAct->pNext ){
		pAct->pNext->pPrev = pAct;
	}
	pActDel->pNext = pAct;
#ifdef PUK3_ACTION_CHECKRANGE
	pAct->db.deathState = eACTION_DEBUG_DEATH_STATE_DEATH;
#endif

	// データクリア
	pPrev = pAct->pPrev;
	pNext = pAct->pNext;
	pYobi = pAct->pYobi;
	db = pAct->db;
	memset( pAct, 0xdddddddd, sizeof(ACTION) );

	pAct->pPrev = pPrev;
	pAct->pNext = pNext;
	pAct->pYobi = pYobi;
	pAct->db = db;

	// チェックサム生成
	makeCheckSum( pAct, &checksum, &pYobichecksum );

	// チェックサムを记忆
	// checksum、pYobichecksumはチェックサムから除いてるのでそこに保存でＯＫ
	pAct->db.checksum = checksum;
	pAct->db.pYobichecksum = pYobichecksum;
}

void realClearAction( ACTION *pAct )
#else
void ClearAction( ACTION *pAct )
#endif
{
#ifdef PUK3_ACTION_CHECKRANGE
	pAct->db.deathState = eACTION_DEBUG_DEATH_STATE_FREE;
#endif
	// 予备构造体があるとき
	if( pAct->pYobi != NULL ){
#ifdef PUK2_MEMCHECK
		memlistrel( pAct->pYobi, MEMLISTTYPE_ACTIONYOBI );
#endif
		// 予备构造体を开放
		free( pAct->pYobi );
	}
#ifdef PUK2_MEMCHECK
	memlistrel( pAct, MEMLISTTYPE_ACTION );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	// 空リストに戾す
	pAct->pPrev = pActFreeBtm.pPrev;
	pAct->pNext = &pActFreeBtm;

	pActFreeBtm.pPrev->pNext = pAct;
	pActFreeBtm.pPrev = pAct;

	pActFreeBtm.prio  = PRIO_CHR;
#else
	// アクション构造体を开放
	free( pAct );
#endif
	
#ifdef _DEBUG		
	/* アクション数マイナス */
	ActCnt--;
#endif		

}

/* 全アクション抹杀 ***********************************************************/
void DeathAllAction( void )
{
	ACTION *pActLoop = pActTop->pNext;

#ifdef PUK3_ACTION_CHECKSUM
	ACTION *pAct;
	int checksum, pYobichecksum;
	char str[1024];
	BOOL flag = FALSE;

	WriteSystemLogfile( "DeathAllAction Check Sum" );                    //MLHIDE
	// チェックサムの确认とアクションの解放
	while( pAct = pActDel->pNext ){
		// リストから外す
		pActDel->pNext = pAct->pNext;

		// チェックサム生成
		makeCheckSum( pAct, &checksum, &pYobichecksum );

		// チェックサム确认
		if ( pAct->db.checksum != checksum ||
			 pAct->db.pYobichecksum != pYobichecksum ){
			// チェックサムが违ってたら
			sprintf( str, "pAct %s pYobi %s : %-6d %s",                        //MLHIDE
				 ( pAct->db.checksum != checksum ? "×" : "○" ),                   //MLHIDE
				 ( pAct->db.pYobichecksum != pYobichecksum ? "×" : "○" ),         //MLHIDE
				 pAct->db.getPos_line, pAct->db.getPos_file );
			WriteSystemLogfile( str );

			flag = TRUE;
		}

		// 解放
		realClearAction( pAct );
	}

	if ( flag ){
		MessageBox( hWnd, "メモリ破壊があったようです。", "CrossGate", MB_OK );           //MLHIDE
	}
#endif
	//先に新マップ用のアクション布ツを抹杀	
	AnimePartsInit();

	/* 最后まできたら終了 */
	while( pActLoop != pActBtm ){
		/* 死亡フラグ立てる */
		pActLoop->deathFlag = TRUE;
		/* 次のポインタへ */
		pActLoop = pActLoop->pNext;
	}
	//MessageBox( hWnd, "DeathAllAction OK!!", "确认", MB_OK );
}

/* アクション終了处理 *********************************************************/
void EndAction( void )
{
	// 全アクション抹杀
	DeathAllAction();
	//アクション走らせる（完全抹杀）
	RunAction();
#ifdef PUK3_ACTION_CHECKSUM
	// 上のRunActionで解放リストに移动したやつを解放
	DeathAllAction();
#endif
	
#ifdef PUK3_ACTION_CHECKRANGE
#ifdef _USESTATICBUF
#else
	free( pActBuf );
#endif
#endif
	/* 最初と最后のポインタ开放 */
	free( pActTop );
	free( pActBtm );
#ifdef PUK2_MEMCHECK
	memlistrel( pActTop, MEMLISTTYPE_ACTION );
	memlistrel( pActBtm, MEMLISTTYPE_ACTION );
#endif
#ifdef PUK3_ACTION_CHECKSUM
	free( pActDel );
#ifdef PUK2_MEMCHECK
	memlistrel( pActTop, MEMLISTTYPE_ACTION );
#endif
#endif
	
	//MessageBox( NULL, "アクション開放完了。", "确认", MB_OK );
}
