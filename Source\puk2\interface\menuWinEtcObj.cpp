﻿

//泛用スイッチ
BOOL MenuSwitchNone( int no, unsigned int flag )
{

	return FALSE;
}

//泛用Del关数
BOOL MenuWindowDel( void )
{

	return TRUE;
}

// クローズボタン
BOOL MenuSwitchCloseButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchDelMouse( int no, unsigned int flag )
{
	// クリックされたら、そのウィンドウを手前に表示
	if(flag & MENU_MOUSE_LEFT) wI->flag |= WIN_INFO_PRIO;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER) return TRUE;
	if(flag & MENU_MOUSE_DRAGOVER) return TRUE;
	return FALSE;
}

// スクロールバー縦
BOOL MenuSwitchScrollBarV( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)wI->sw[no].Switch;

	// 前回押されていなかったら、ドラッグ中のフラグを落とす
	if ( !(Bt->status&2) ) Bt->status = 0;

	// 押されているかチェック
	if( flag & MENU_MOUSE_LEFTHOLD ){
		Bt->status = 3;
		ReturnFlag=TRUE;
	}

	// 押されている、又は前回押されていた场合、つまみを移动
	if ( Bt->status & 2 ) wI->sw[no-1].ofy = (mouse.nowPoint.y-wI->wy) - (wI->sw[no-1].sy >> 1);

	if (wI->sw[no-1].ofy<wI->sw[no].ofy) wI->sw[no-1].ofy = wI->sw[no].ofy;
	if (wI->sw[no-1].ofy+wI->sw[no-1].sy>wI->sw[no].ofy+wI->sw[no].sy) wI->sw[no-1].ofy = wI->sw[no].ofy + wI->sw[no].sy - wI->sw[no-1].sy;

	Bt->y = wI->sw[no-1].ofy - wI->sw[no].ofy;

	// 押されているかチェック
	if( !(flag & MENU_MOUSE_LEFTHOLD) ) Bt->status &= ~2;

	return ReturnFlag;
}

// スクロールバー横
BOOL MenuSwitchScrollBarH( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)wI->sw[no].Switch;

	// 前回押されていなかったら、ドラッグ中のフラグを落とす
	if ( !(Bt->status&2) ) Bt->status = 0;

	// 押されているかチェック
	if( flag & MENU_MOUSE_LEFTHOLD ){
		Bt->status = 3;
		ReturnFlag=TRUE;
	}

	// 押されている、又は前回押されていた场合、つまみを移动
	if ( Bt->status & 2 ) wI->sw[no-1].ofx = (mouse.nowPoint.x-wI->wx) - (wI->sw[no-1].sx >> 1);

	if (wI->sw[no-1].ofx<wI->sw[no].ofx) wI->sw[no-1].ofx = wI->sw[no].ofx;
	if (wI->sw[no-1].ofx+wI->sw[no-1].sx>wI->sw[no].ofx+wI->sw[no].sx) wI->sw[no-1].ofx = wI->sw[no].ofx + wI->sw[no].sx - wI->sw[no-1].sx;

	Bt->x = wI->sw[no-1].ofx - wI->sw[no].ofx;

	// 押されているかチェック
	if( !(flag & MENU_MOUSE_LEFTHOLD) ) Bt->status &= ~2;

	return ReturnFlag;
}

// スクロールバー縦用戾り值变换关数
int ScrollVPointToNum( SWITCH_INFO *sw, int Max )
{
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)sw->Switch;
	if (Max<=0) return(0);
	return( ( Max * ( Bt->y + ( (sw->sy-(sw-1)->sy) / (Max<<1) ) ) ) / (sw->sy-(sw-1)->sy) );
}

// スクロールバー横用戾り值变换关数
int ScrollHPointToNum( SWITCH_INFO *sw, int Max )
{
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)sw->Switch;
	if (Max<=0) return(0);
	return( ( Max * ( Bt->x + ( (sw->sx-(sw-1)->sx) / (Max<<1) ) ) ) / (sw->sx-(sw-1)->sx) );
}

// 变换后戾り值を元に、つまみを移动、スクロールバー縦用
void NumToScrollVMove( SWITCH_INFO *sw, int Max, int Now )
{
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)sw->Switch;

	if (Max<=0){
		(sw-1)->ofy = sw->ofy;
		Bt->y = (sw-1)->ofy - sw->ofy;
		return;
	}
	(sw-1)->ofy = sw->ofy + ( ( Now * (sw->sy-(sw-1)->sy) ) / Max );
	Bt->y = (sw-1)->ofy - sw->ofy;
}

// 变换后戾り值を元に、つまみを移动、スクロールバー横用
void NumToScrollHMove( SWITCH_INFO *sw, int Max, int Now )
{
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)sw->Switch;

	if (Max<=0){
		(sw-1)->ofx = sw->ofx;
		Bt->x = (sw-1)->ofx - sw->ofx;
		return;
	}
	(sw-1)->ofx = sw->ofx + ( ( Now * (sw->sx-(sw-1)->sx) ) / Max );
	Bt->x = (sw-1)->ofx - sw->ofx;
}

// スクロールバー縦ホイール移动
int WheelToMove( SWITCH_INFO *sw, int Now, int Max, int wheel )
{
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)sw->Switch;
	float add;
	int nowY;
	int sub;

	if (!wheel) return Now;
	if ( Max < 0 ) Max = 0;

	// 现在位置保存
	nowY = (sw-1)->ofy;

	// 现在值の位置と、现在位置の差を取得
	NumToScrollVMove( sw, Max, Now );
	sub = nowY - (sw-1)->ofy;

	// どのくらい移动するかを取得
	add = (float)wheel / WHEEL_DELTA;

	// 移动后の现在值を取得
	Now += (int)add;
	if ( Now < 0 ){ Now = 0;	sub = 0; }
	if ( Now > Max ){ Now = Max;	sub = 0; }

	// どのくらい移动するかの小数部を取得
	add -= (int)add;

	// 移动
	NumToScrollVMove( sw, Max, Now );
	(sw-1)->ofy += sub;
	(sw-1)->ofy += (int)( ( add * (sw->sy-(sw-1)->sy) ) / Max );

	if ( (sw-1)->ofy<sw->ofy) (sw-1)->ofy = sw->ofy;
	if ( (sw-1)->ofy+(sw-1)->sy>sw->ofy+sw->sy) (sw-1)->ofy = sw->ofy + sw->sy - (sw-1)->sy;

	Bt->y = (sw-1)->ofy - sw->ofy;
	Bt->status |= 1;

	return Now;
}

// 电卓コントロール(表示处理込み)
const int CalcuGraTbl[14][3]={
	{ GID_0ButtonOn,	GID_0ButtonOff,		GID_0ButtonOver },
	{ GID_1ButtonOn,	GID_1ButtonOff,		GID_1ButtonOver },
	{ GID_2ButtonOn,	GID_2ButtonOff,		GID_2ButtonOver },
	{ GID_3ButtonOn,	GID_3ButtonOff,		GID_3ButtonOver },
	{ GID_4ButtonOn,	GID_4ButtonOff,		GID_4ButtonOver },
	{ GID_5ButtonOn,	GID_5ButtonOff,		GID_5ButtonOver },
	{ GID_6ButtonOn,	GID_6ButtonOff,		GID_6ButtonOver },
	{ GID_7ButtonOn,	GID_7ButtonOff,		GID_7ButtonOver },
	{ GID_8ButtonOn,	GID_8ButtonOff,		GID_8ButtonOver },
	{ GID_9ButtonOn,	GID_9ButtonOff,		GID_9ButtonOver },
	{ GID_BSButtonOn,	GID_BSButtonOff,	GID_BSButtonOver },
	{ GID_AllButtonOn,	GID_AllButtonOff,	GID_AllButtonOver },
	{ GID_ClrButtonOn,	GID_ClrButtonOff,	GID_ClrButtonOver },
	{ GID_OKButtonOn,	GID_OKButtonOff,	GID_OKButtonOver },
};

char CalculatorControl( WINDOW_INFO *wi, int no, unsigned int flag, int AllNum, int Prio, char **OneLineInfo, char option, unsigned long rgba )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	int i, j, a, b, c, d;
	int x = wi->wx+wi->sw[no].ofx, y = wi->wy+wi->sw[no].ofy;
	int Cri = -1;

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	// １～９とのあたり判定、描画
	d = 1;
	b = 38;
	for(j=0;j<3;j++){
		a = 0;
		for(i=0;i<3;i++){
			c = 0;
			if (x+a<=mouse.nowPoint.x){if (mouse.nowPoint.x<x+a+21){
				if (y+b<=mouse.nowPoint.y){if (mouse.nowPoint.y<y+b+19){
					strcpy( OneLineInfoStr, OneLineInfo[0] );
					c = 2;
					if (flag&MENU_MOUSE_LEFTHOLD) c = 1;
					if (flag&MENU_MOUSE_LEFT) Cri = d;
				}}
			}}
			StockDispBuffer( x+a+10, y+b+10, Prio, CalcuGraTbl[d][c], 0, &bm );
			d++;
			a += 24;
		}
		b -= 20;
	}

	// ０とのあたり判定、描画
	c = 0;
	if (x<=mouse.nowPoint.x){if (mouse.nowPoint.x<x+21){
		if (y+58<=mouse.nowPoint.y){if (mouse.nowPoint.y<y+58+19){
			strcpy( OneLineInfoStr, OneLineInfo[0] );
			c = 2;
			if (flag&MENU_MOUSE_LEFTHOLD) c = 1;
			if (flag&MENU_MOUSE_LEFT) Cri = 0;
		}}
	}}
	StockDispBuffer( x+10, y+58+10, Prio, CalcuGraTbl[0][c], 0, &bm );

	// OKとのあたり判定、描画
	c = 0;
	if (x+48<=mouse.nowPoint.x){if (mouse.nowPoint.x<x+48+21){
		if (y+58<=mouse.nowPoint.y){if (mouse.nowPoint.y<y+58+19){
			if (option&CALCULATOR_OPT_ZERO_OK){
				strcpy( OneLineInfoStr, OneLineInfo[1] );
				c = 2;
				if (flag&MENU_MOUSE_LEFTHOLD) c = 1;
				if (flag&MENU_MOUSE_LEFT) Cri = 13;
			}else{
				if (wi->sw[no].status>0) strcpy( OneLineInfoStr, OneLineInfo[1] );
				else  strcpy( OneLineInfoStr, OneLineInfo[2] );
				c = 2;
				if (flag&MENU_MOUSE_LEFTHOLD) c = 1;
				if (wi->sw[no].status>0){
					if (flag&MENU_MOUSE_LEFT) Cri = 13;
				}else{
					// ＮＧ音
					if (flag&MENU_MOUSE_LEFT) play_se( SE_NO_NG, 320, 240 );
				}
			}
		}}
	}}
	if ( !(option&CALCULATOR_OPT_ZERO_OK) ){
		if (Cri<0 && wi->sw[no].status<=0) c = 1;
	}
	StockDispBuffer( x+48+10, y+58+10, Prio, CalcuGraTbl[13][c], 0, &bm );

	d = 10;
	b = 18;
	for(j=0;j<3;j++){
		c = 0;
		if (x+75<=mouse.nowPoint.x){if (mouse.nowPoint.x<x+75+25){
			if (y+b<=mouse.nowPoint.y){if (mouse.nowPoint.y<y+b+19){
				if (j==0) strcpy( OneLineInfoStr, OneLineInfo[3] );
				if (j==1) strcpy( OneLineInfoStr, OneLineInfo[4] );
				if (j==2) strcpy( OneLineInfoStr, OneLineInfo[5] );
				c = 2;
				if (flag&MENU_MOUSE_LEFTHOLD) c = 1;
				if (flag&MENU_MOUSE_LEFT) Cri = d;
			}}
		}}
		StockDispBuffer( x+75+12, y+b+10, Prio, CalcuGraTbl[d][c], 0, &bm );
		d++;
		b += 20;
	}

	if (option&CALCULATOR_OPT_SHORTCUT){
		for( i = 0; i < 10; i++ ){
			if( keyOnOnce( VK_0+i ) || keyOnOnce( VK_NUMPAD0+i ) ){ Cri = i;	break; }
		}
	}
	if( keyOnOnce( VK_BACK ) ) Cri = 10;

	if (Cri>=0){
		// 数值なら
		if (Cri<=9){
			wi->sw[no].status = wi->sw[no].status*10;
			wi->sw[no].status += Cri;
			if (wi->sw[no].status>AllNum) wi->sw[no].status = AllNum;
		}
		// BSなら
		if (Cri==10) wi->sw[no].status = wi->sw[no].status/10;
		// ALLなら
		if (Cri==11) wi->sw[no].status = AllNum;
		// CLRなら
		if (Cri==12) wi->sw[no].status = 0;
	}

	// CLRなら
	if (Cri==13){
		play_se( SE_NO_CLICK, 320, 240 );
		return (CALCULATOR_OK|CALCULATOR_PUSH);
	}
	if (Cri>=0){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		return CALCULATOR_PUSH;
	}
	return 0;
}

//------------------------------------------------------------------------//
// ウィンドウオープンアニメーション											//
//------------------------------------------------------------------------//
void openWindowAnim( ACTION *ptAct )
{
	WIN_DISP *ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	StockBoxDispBuffer( ptWinDisp->cx - ptWinDisp->nx,
						ptWinDisp->cy - ptWinDisp->ny,
						ptWinDisp->cx + ptWinDisp->nx,
						ptWinDisp->cy + ptWinDisp->ny,
						DISP_PRIO_MENU, SYSTEM_PAL_BLACK, 0 );

	// 增分プラス
	ptWinDisp->nx += ptAct->dx;
	ptWinDisp->ny += ptAct->dy;

	// リミットチェック
	if( ptWinDisp->cnt >= WINDOW_CREATE_FRAME ){
		createMenuWindow( ptWinDisp->type );
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	if ( ptWinDisp->type > MENU_WINDOW_MENU ){
		switch(ptWinDisp->type){
		case MENU_WINDOW_SYSTEM:
		case MENU_WINDOW_SYSTEMSHORTCUT:
		case MENU_WINDOW_BATTLE:
		case MENU_WINDOW_SURPRISE:
		case MENU_WINDOW_MAP:
		case MENU_WINDOW_ACTION:
		case MENU_CHAT_WINDOW:
		case MENU_WINDOW_MAPNAME:
		case MENU_OLD_CHAT_WINDOW:
			break;
		default:
			Lock3DChangeWindowCnt--;
		}
	}
#endif
		DeathAction( ptAct );
		if (ptAct->level) *( (ACTION **)ptAct->level ) = NULL;
	}

	// カウンタープラス
	ptWinDisp->cnt++;
}

ACTION *openMenuWindowPos( short x, short y, short w, short h, int type, unsigned char flg, char opentype, ACTION **ret )
{
	ACTION *ptAct;
	WIN_DISP *ptWinDisp;

	// ウィンドウ开く音
	play_se( SE_NO_OPEN_WINDOW, 320, 240 );

	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ), ACT_T_WIN_DISP );
#else
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ) );
#endif

	// アクション取得に失败したら終わる
	if( ptAct == NULL ) {
		if (ret) *ret=NULL;
		createMenuWindow( type );
		return NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	if ( type > MENU_WINDOW_MENU ){
		switch(type){
		case MENU_WINDOW_SYSTEM:
		case MENU_WINDOW_SYSTEMSHORTCUT:
		case MENU_WINDOW_BATTLE:
		case MENU_WINDOW_SURPRISE:
		case MENU_WINDOW_MAP:
		case MENU_WINDOW_ACTION:
		case MENU_CHAT_WINDOW:
		case MENU_WINDOW_MAPNAME:
		case MENU_OLD_CHAT_WINDOW:
			break;
		default:
			Lock3DChangeWindowCnt++;
		}
	}
#endif

	// 予备构造体へのポインタ
	ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	// 实行关数
	ptAct->func = openWindowAnim;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_WIN2;
	// 当たり判定する
	ptAct->atr |= ACT_ATR_HIT;
	// 表示しない
	ptAct->atr |= ACT_ATR_HIDE;

	ptWinDisp->graNo = 0;
	ptWinDisp->hitFlag = 0;
	if( flg& OPENMENUWINDOW_HIT ) ptWinDisp->hitFlag = 1;

	// 座标を记忆
	ptAct->x = x,	ptAct->y = y;
	ptWinDisp->w = w,	ptWinDisp->h = h;

	// ウィンドウ种类
	ptWinDisp->type = type;

	// カウンタ
	ptWinDisp->cnt = 0;

	// 自分が死んだ事を伝えるためのポインタ保存
	ptAct->level = (unsigned long)ret;

	// ＮＲバージョンエフェクト
	if( opentype == 0 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = 0;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;

		ptWinDisp->nx = ptWinDisp->cx - x;
	}
	// ＳＡバージョンエフェクト
	if( opentype == 1 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = (ptWinDisp->cx - x) / WINDOW_CREATE_FRAME;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;
	}

	return ptAct;
}

ACTION *openMenuWindow( int type, unsigned char flg, char opentype, ACTION **ret )
{
	int x, y, w, h;

	// ウィンドウの场所、サイズを取得
//	x = WindowData[ type ]->wx;
//	y = WindowData[ type ]->wy;
	w = WindowData[ type ]->w;
	h = WindowData[ type ]->h;

	// 以前开いたときの状态を、作成されたウィンドウに反映
	if ( WindowFlag[ type ].wininfo ){
		WindowFlag[ type ].wininfo->flag |= WIN_INFO_DEL;
		x = WindowFlag[ type ].wininfo->wx;
		y = WindowFlag[ type ].wininfo->wy;
//	}else if( WindowFlag[ type ].flag & 0x01 ){
	}else{	
		x = WindowFlag[ type ].wx;
		y = WindowFlag[ type ].wy;
	}

	return openMenuWindowPos( x, y, w, h, type, flg, opentype, ret );
}

//------------------------------------------------------------------------//
// 画面から外れているウインドウの位置初期化									//
//------------------------------------------------------------------------//
void ResetWindowPos( void ){
	
	const WINDOW_DATA *mwd;
	int i;

	// ウィンドウバッファ全体をチェック
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		//作成されていなかったら无视
		if(WindowFlag[i].wininfo!=NULL){
/*　判定がキツイということなのであまめにする
		//画面から一定以上外れていたら
			if(WindowFlag[i].wininfo->wx+(WindowFlag[i].wininfo->sx/2)<0 ||
			   WindowFlag[i].wininfo->wx+(WindowFlag[i].wininfo->sx/2)>640 ||
			   WindowFlag[i].wininfo->wy+(WindowFlag[i].wininfo->sy/2)<0 ||
			   WindowFlag[i].wininfo->wy+(WindowFlag[i].wininfo->sy/2)>480
				){
*/
				//ウインドウを初期值へ
				mwd = WindowData[ i ];
				WindowFlag[i].wininfo->wx=mwd->wx;
				WindowFlag[i].wininfo->wy=mwd->wy;
//			}
		}
	}

}


//======================================//
//			ドラッグ??ドロップ			//
//======================================//

// ドラッグ??ドロップ元ウィンドウ取得关数
int WinDD_WinType()
{
	return WindowDD.WinType;
}

// ドラッグ??ドロップ元ボタン番号取得关数
int WinDD_ButtonNo()
{
	return WindowDD.SwitchNo;
}

// ドラッグ??ドロップデータ种类确认关数
enum WINDD_OBJECTTYPE WinDD_CheckObjType()
{
	return WindowDD.ObjType;
}

// ドロップ场所取得关数
int WinDD_DropX()
{
	return WindowDD.DropX;
}

// ドロップ场所取得关数
int WinDD_DropY()
{
	return WindowDD.DropY;
}

// ドロップデータ确认关数
void *WinDD_ObjData()
{
	return WindowDD.ObjData;
}

#ifdef PUK2_NEWDRAG
// ドラッグ开始关数
BOOL WinDD_DragStart( enum WINDD_OBJECTTYPE ObjType, void *ObjData, void (*DragFunc)( int ProcNo, unsigned int flag, void *ObjData ) )
{
	// 现在何かを处理中なら失败
	if ( WindowDD.ProcNo != WINDD_DONOT ) return FALSE;

	if ( ObjType == WINDD_NONE ) return FALSE;

	// 处理关数登録
	WindowDD.DragFunc = DragFunc;

	// ドラッグアイテム情报保存
	WindowDD.ObjType = ObjType;
	WindowDD.ObjData = ObjData;

	// ドラッグ元保存
	WindowDD.WinType = nowWinType;
	WindowDD.SwitchNo = nowBtnNo;

	// 状态变更
	WindowDD.ProcNo = WINDD_DRAGGING;

	return TRUE;
}
#endif
// ドラッグ开始关数
BOOL WinDD_DragStart( enum WINDD_OBJECTTYPE ObjType, void *ObjData )
{
	// 现在何かを处理中なら失败
	if ( WindowDD.ProcNo != WINDD_DONOT ) return FALSE;

	if ( ObjType == WINDD_NONE ) return FALSE;

	WindowDD.ObjType = ObjType;
	WindowDD.ObjData = ObjData;

	WindowDD.WinType = nowWinType;
	WindowDD.SwitchNo = nowBtnNo;

	WindowDD.ProcNo = WINDD_DRAGGING;

	return TRUE;
}

// ドラッグ者登録变更
BOOL WinDD_DragerChange( int WindowType, int ButtonNo )
{
	if ( WindowDD.ObjType == WINDD_NONE ) return FALSE;

	WindowDD.WinType = WindowType;
	WindowDD.SwitchNo = ButtonNo;

	return TRUE;
}

// ドラッグ終了关数
void WinDD_DragFinish()
{
	WindowDD.ObjType = WINDD_NONE;

	WindowDD.WinType = MENU_WINDOW_NONE;
	WindowDD.SwitchNo = -1;
#ifdef PUK2_NEWDRAG
	WindowDD.DragFunc = NULL;
#endif

	WindowDD.ProcNo = WINDD_DONOT;
}

#ifdef PUK2_NEWDRAG

// ドラッグキャンセル关数
void WinDD_DragCancel()
{
	if ( WindowDD.ProcNo == WINDD_DONOT ) return;

	// ドラッグ中ならドラッグキャンセル处理
	if ( WindowDD.ProcNo == WINDD_DRAGGING ){
		if (WindowDD.DragFunc) WindowDD.DragFunc( WINDDPROC_DRAGCANCEL, 0, WindowDD.ObjData );
	}
	// ドロップ中なら谁にも受け取られなかった处理
	else{
		if (WindowDD.DragFunc) WindowDD.DragFunc( WINDDPROC_DROP_NOACCEPT, 0, WindowDD.ObjData );
	}

	WindowDD.ObjType = WINDD_NONE;

	WindowDD.ObjData = NULL;
	WindowDD.DragFunc = NULL;
	WindowDD.DropX = 0;
	WindowDD.DropY = 0;

	WindowDD.WinType = MENU_WINDOW_NONE;
	WindowDD.SwitchNo = -1;

	// ドラッグ中ならドラッグキャンセル处理
	if ( WindowDD.ProcNo == WINDD_DRAGGING ){
		WindowDD.ProcNo = WINDD_DRAGCANCEL;
	}
	// ドロップ中なら谁にも受け取られなかった处理
	else{
		WindowDD.ProcNo = WINDD_DROPCANCEL;
	}
}

// ドロップ关数
BOOL WinDD_DropObject( enum WINDD_OBJECTTYPE ObjType, void *ObjData, short DropX, short DropY, void (*DragFunc)( int ProcNo, unsigned int flag, void *ObjData ) )
{
	// 现在何かを处理中なら失败
	if ( WindowDD.ProcNo != WINDD_DONOT ) return FALSE;

	if ( ObjType == WINDD_NONE ) return FALSE;

	WindowDD.ObjType = ObjType;
	WindowDD.ObjData = ObjData;
	WindowDD.DragFunc = DragFunc;
	WindowDD.DropX = DropX;
	WindowDD.DropY = DropY;

	WindowDD.WinType = nowWinType;
	WindowDD.SwitchNo = nowBtnNo;

	WindowDD.ProcNo = WINDD_DROPENTRY;

	return TRUE;
}

// ドロップデータ受け取り关数
void WinDD_AcceptObject()
{
	// ドロップ先を探していないなら終了
	if (WindowDD.ProcNo != WINDD_DROPSEEKING){
		if (WindowDD.ProcNo != WINDD_DROPRETURN) return;
	}

	// 受け取り先が见つかったので、后始末
#ifdef PUK2_NEWDRAG
	if (WindowDD.DragFunc) WindowDD.DragFunc( WINDDPROC_DROP_ACCEPT, 0, WindowDD.ObjData );
#else
	if (WindowDD.SettleDropItem) WindowDD.SettleDropItem();
#endif

	// 初期化
	WindowDD.ObjType = WINDD_NONE;
	WindowDD.ObjData = NULL;
	WindowDD.DragFunc = NULL;
	WindowDD.DropX = 0;
	WindowDD.DropY = 0;

	WindowDD.WinType = MENU_WINDOW_NONE;
	WindowDD.SwitchNo = -1;

	WindowDD.ProcNo = WINDD_DONOT;
}

#else

// ドロップ关数
BOOL WinDD_DropObject( enum WINDD_OBJECTTYPE ObjType, void *ObjData, void (*SettleDropItem)(), short DropX, short DropY )
{
	// 现在何かを处理中なら失败
	if ( WindowDD.ProcNo != WINDD_DONOT ) return FALSE;

	if ( ObjType == WINDD_NONE ) return FALSE;

	WindowDD.ObjType = ObjType;
	WindowDD.ObjData = ObjData;
	WindowDD.SettleDropItem = SettleDropItem;
	WindowDD.DropX = DropX;
	WindowDD.DropY = DropY;

	WindowDD.WinType = nowWinType;
	WindowDD.SwitchNo = nowBtnNo;

	WindowDD.ProcNo = WINDD_DROPENTRY;

	return TRUE;
}

// ドロップデータ受け取り关数
void *WinDD_GetObject()
{
	void *ret;

	// ドロップ先を探していないなら終了
	if (WindowDD.ProcNo != WINDD_DROPSEEKING){
		if (WindowDD.ProcNo != WINDD_DROPRETURN) return NULL;
	}

	ret = WindowDD.ObjData;

	// 受け取り先が见つかったので、后始末
	if (WindowDD.SettleDropItem) WindowDD.SettleDropItem();

	// 初期化
	WindowDD.ObjType = WINDD_NONE;
	WindowDD.ObjData = NULL;
	WindowDD.SettleDropItem = NULL;
	WindowDD.DropX = 0;
	WindowDD.DropY = 0;

	WindowDD.WinType = MENU_WINDOW_NONE;
	WindowDD.SwitchNo = -1;

	WindowDD.ProcNo = WINDD_DONOT;

	return ret;
}

#endif

//ダイアログ设定
void SetInputStr(INIT_STR_STRUCT *InitStrStruct,int x,int y,int flag){

	switch(flag){	
	case 0:
		//初期化（バッファクリア）
		InitInputStr( InitStrStruct->inputStr,
			x+InitStrStruct->ofx, y+InitStrStruct->ofy,
			InitStrStruct->fontPrio, InitStrStruct->fontKind,
			InitStrStruct->color,InitStrStruct->str,
			InitStrStruct->lineMax, InitStrStruct->lineLen,
			InitStrStruct->lineDist,InitStrStruct->blindFlag );
		break;
	case 1:
		//初期化（バッファ保持）
		initInputStr( InitStrStruct->inputStr, x+InitStrStruct->ofx, y+InitStrStruct->ofy,
			InitStrStruct->lineLen, InitStrStruct->color, InitStrStruct->fontPrio );
	case 2:
		//初期化（座标变更のみ）
		InitStrStruct->inputStr->x=x+InitStrStruct->ofx;
		InitStrStruct->inputStr->y=y+InitStrStruct->ofy;
		break;

	case 3:
		//初期化（バッファ保持、フォントカラー保持チャット用）
		initInputStrNewChat( InitStrStruct->inputStr, x+InitStrStruct->ofx, y+InitStrStruct->ofy,
			InitStrStruct->inputStr->color, InitStrStruct->fontPrio,InitStrStruct->fontKind );
		break;
	}

}


// 聊天范围处理 ********************************************************
void NewChatAreaProc( void )
{
	int startX;
	int startY;
	int i, j;
	
	// 自分のポインタ无かったら返回
	if( pc.ptAct == NULL ) return;
	
	startX = pc.ptAct->x - ( 64 * NowMaxVoice ) - 31;
	startY = pc.ptAct->y - 23;

	// 聊天范围表示フラグＯＮの时
	if( ChatAreaDispTime > 0 ){
		// 聊天范围描画
		for( i = 0 ; i < NowMaxVoice * 2 + 1 ; i++ ){
			for( j = 0 ; j < NowMaxVoice * 2 + 1 ; j++ ){
				StockDispBufferEx(
					startX + j * 32 + i * 32,
					startY - j * 24 + i * 24,
					DISP_PRIO_GRID, 3, 0 );
			}
		}
	}
	
	// 聊天范围表示时间マイナス
	ChatAreaDispTime--;
	// リミットチェック
	if( ChatAreaDispTime < 0 ) ChatAreaDispTime = 0;
}


// メニュー用キャラクタのアクション作成
ACTION *createMenuAction( int graNo, int gx, int gy )
{
	ACTION *ptAct;

	/* アクションリストに登録 */
	ptAct = GetAction( PRIO_CHR, 0 );
	if( ptAct == NULL )
		return NULL;

	// 实行关数
	ptAct->func = NULL;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	ptAct->anim_ang = 0;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_WIN2;
	// 1行インフォ表示フラグ
	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
	// 初期位置
	ptAct->vx = 0;						// 移动增分
	ptAct->vy = 0;

	// 画面表示位置
	ptAct->x = gx;
	ptAct->y = gy;

	return ptAct;
}

//情报として设定する文字列が正しいかチェック
//文字列がない时、または「 」と「　」でしか构成されていないときFALSEを返す
//また、以下の文字が含まれている场合もFALSEを返します
//「,」「|」「\」
BOOL CheckSendStr(char *str){

	int StrLen;
	int StrLenCount;

	//特定文字があった场合FALSEを返す
	StrLenCount=0;
	StrLen=strlen(str);

	//文字列无いとき
	if(StrLen==0)
		return FALSE;

	//特定文字コードチェック
	while(1){
		switch(GetCharByte(str[StrLenCount])){
			//半角
			case 1:

				//「,」
				if((unsigned char)str[StrLenCount]==0x2C)
					return FALSE;

				//「|」
				if((unsigned char)str[StrLenCount]==0x7C)
					return FALSE;

				//「\」
				if((unsigned char)str[StrLenCount]==0x5C)
					return FALSE;

				StrLenCount+=1;
			break;

			//全角
			case 2:
				StrLenCount+=2;
			break;
		}

		if(StrLen<=StrLenCount)
			break;
	}

	//文字列がスペースのみの场合FALSEを返す
	StrLenCount=0;
	StrLen=strlen(str);

	//スペースチェック
	while(1){
		switch(GetCharByte(str[StrLenCount])){
			//半角
			case 1:
				//半角スペース
				if((unsigned char)str[StrLenCount]!=0x20)
					return TRUE;

				StrLenCount+=1;
			break;

			//全角
			case 2:
				//全角スペース
//				if(!((unsigned char)str[StrLenCount]==0x81 && (unsigned char)str[StrLenCount+1]==0x40))
				if(!((unsigned char)str[StrLenCount]==0xa1 && (unsigned char)str[StrLenCount+1]==0xa1))
					return TRUE;

				StrLenCount+=2;
			break;
		}

		if(StrLen<=StrLenCount)
			break;
	}

	return FALSE;
}



const int ColorNumGraNoHead[G_NUM_SIZE_NUM][13] = {
	{ GID_Num_White_0, GID_Num_Aqua_0, GID_Num_Purple_0, GID_Num_Blue_0, GID_Num_Yellow_0, GID_Num_Green_0, GID_Num_Red_0, GID_Num_Grey_0, GID_Num_Blue2_0, GID_Num_Green2_0, GID_Num_Black_0, GID_Num_Black_0, GID_Num_Black_0 },
	{ GID_Num_Middle_White_0, GID_Num_Middle_Aqua_0, GID_Num_Middle_Purple_0, GID_Num_Middle_Blue_0, GID_Num_Middle_Yellow_0, GID_Num_Middle_Green_0, GID_Num_Middle_Red_0, GID_Num_Middle_Grey_0, GID_Num_Middle_Blue2_0, GID_Num_Middle_Green2_0, GID_Num_Middle_White_0, GID_Num_Middle_White_0, GID_Num_Middle_White_0 },
	{ GID_Num_Big_White_0, GID_Num_Big_Aqua_0, GID_Num_Big_Purple_0, GID_Num_Big_Blue_0, GID_Num_Big_Yellow_0, GID_Num_Big_Green_0, GID_Num_Big_Red_0, GID_Num_Big_Grey_0, GID_Num_Big_Blue2_0, GID_Num_Big_Green2_0, GID_Num_Big_White_0, GID_Num_Big_White_0, GID_Num_Big_White_0 },
	{ GID_Num_White_S_0, GID_Num_White_S_0, GID_Num_White_S_0, GID_Num_White_S_0, GID_Num_White_S_0, GID_Num_White_S_0, GID_Num_Red_S_0, GID_Num_Grey_S_0, GID_Num_White_S_0, GID_Num_White_S_0, GID_Num_Black_S_0, GID_Num_Black_S_0, GID_Num_Black_S_0 },
};
const int ColorMarkGraNoHead[G_NUM_SIZE_NUM][13] = {
	{ GID_Num_White_Plus, GID_Num_Aqua_Plus, GID_Num_Purple_Plus, GID_Num_Blue_Plus, GID_Num_Yellow_Plus, GID_Num_Green_Plus, GID_Num_Red_Plus, GID_Num_Grey_Plus, GID_Num_Blue2_Plus, GID_Num_Green2_Plus, GID_Num_Black_Plus, GID_Num_Black_Plus, GID_Num_Black_Plus },
	{ GID_Num_Middle_White_Plus, GID_Num_Middle_Aqua_Plus, GID_Num_Middle_Purple_Plus, GID_Num_Middle_Blue_Plus, GID_Num_Middle_Yellow_Plus, GID_Num_Middle_Green_Plus, GID_Num_Middle_Red_Plus, GID_Num_Middle_Grey_Plus, GID_Num_Middle_Blue2_Plus, GID_Num_Middle_Green2_Plus, GID_Num_Middle_White_Plus, GID_Num_Middle_White_Plus, GID_Num_Middle_White_Plus },
	{ GID_Num_Big_White_Plus, GID_Num_Big_Aqua_Plus, GID_Num_Big_Purple_Plus, GID_Num_Big_Blue_Plus, GID_Num_Big_Yellow_Plus, GID_Num_Big_Green_Plus, GID_Num_Big_Red_Plus, GID_Num_Big_Grey_Plus, GID_Num_Big_Blue2_Plus, GID_Num_Big_Green2_Plus, GID_Num_Big_White_Plus, GID_Num_Big_White_Plus, GID_Num_Big_White_Plus },
	{ GID_Num_White_S_Plus, GID_Num_White_S_Plus, GID_Num_White_S_Plus, GID_Num_White_S_Plus, GID_Num_White_S_Plus, GID_Num_White_S_Plus, GID_Num_Red_S_Plus, GID_Num_Grey_S_Plus, GID_Num_White_S_Plus, GID_Num_White_S_Plus, GID_Num_Black_S_Plus, GID_Num_Black_S_Plus, GID_Num_Black_S_Plus },
};
const int ColorNumGraDif[G_NUM_SIZE_NUM][3] = {
	// Ｘ左	Ｘ右	Ｙ
	{ 2,	2,		5 },
	{ 5,	4,		8 },
	{ 5,	5,		8 },
	{ 2,	3,		5 },
};
const int ColorNumGraSpace[G_NUM_SIZE_NUM] = { 2, 2, 2, 1 };
const int ColorNumGraInterval[G_NUM_SIZE_NUM] = { 5+ColorNumGraSpace[0], 10+ColorNumGraSpace[1], 11+ColorNumGraSpace[2], 7+ColorNumGraSpace[3] };

// グラフィック数值描画关数
void DrawGraphicNumber( int l_x, int l_y, char *l_str, char l_size, char l_color, unsigned char l_flag, int l_Prio )
{
	char *str;
	int x,y, ivl;
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;

	// 右揃えなら
	if ( l_flag & G_NUM_FLAG_RIGHT_JUSTIFIED ){
		x = l_x - ColorNumGraDif[l_size][1];
		ivl = -ColorNumGraInterval[l_size];
		y = l_y + ColorNumGraDif[l_size][2];

		for(str=l_str;str[0]!='\0';str++);
		for(str--;str>=l_str;str--){
			// 数字
			if ( '0'<=str[0] && str[0]<='9' ){
				StockDispBuffer( x, y, l_Prio, ColorNumGraNoHead[l_size][l_color]+(str[0]-'0'), 0, &bm );
			}
			// ＋
			else if ( str[0] == '+' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+0, 0, &bm );
			}
			// －
			else if ( str[0] == '-' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+1, 0, &bm );
			}
			// ：
			else if ( str[0] == ':' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+2, 0, &bm );
			}
			// ／
			else if ( str[0] == '/' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+3, 0, &bm );
			}
			x += ivl;
		}
	}
	// 左揃えなら
	else{
		x = l_x + ColorNumGraDif[l_size][0];
		ivl = ColorNumGraInterval[l_size];
		y = l_y + ColorNumGraDif[l_size][2];

		for(str=l_str;str[0]!='\0';str++){
			// 数字
			if ( '0'<=str[0] && str[0]<='9' ){
				StockDispBuffer( x, y, l_Prio, ColorNumGraNoHead[l_size][l_color]+(str[0]-'0'), 0, &bm );
			}
			// ＋
			else if ( str[0] == '+' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+0, 0, &bm );
			}
			// －
			else if ( str[0] == '-' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+1, 0, &bm );
			}
			// ：
			else if ( str[0] == ':' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+2, 0, &bm );
			}
			// ／
			else if ( str[0] == '/' ){
				StockDispBuffer( x, y, l_Prio, ColorMarkGraNoHead[l_size][l_color]+3, 0, &bm );
			}
			x += ivl;
		}
	}
}

// グラフィック数值横幅取得关数
int GraphicNumberWidth( char *l_str, char l_size )
{
	int len, wid;

	len = strlen(l_str);

	wid = 0;
	if ( len > 0 ){
		// 最初の一文字の幅
		wid += ColorNumGraInterval[l_size] - ColorNumGraSpace[l_size];
		// それ以降の分の幅
		wid += ColorNumGraInterval[l_size] * ( len - 1 );
	}

	return wid;
}
const int ColorNumGraHeight[G_NUM_SIZE_NUM] = { 9, 15, 16, 10 };
// グラフィック数值縦幅取得关数
int GraphicNumberHeight( char l_size )
{
	return ColorNumGraHeight[l_size];
}



WINDOW_INFO *getWindowBuffAddFromType(int type){

	int i;

	for(i=0;i<MENU_WINDOW_MAX;i++){
		if(WindowBuff[i]->type==type){
			return WindowBuff[i];
		}
	}

	return NULL;

}

