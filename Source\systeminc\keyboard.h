﻿/************************/
/*	keyboard.h			*/
/************************/
#ifndef _KEYBOARD_H_
#define _KEYBOARD_H_

/*----------　ジョイスティック　----------*/
// １Ｐ
#define	JOY_RIGHT	(1 << 15)	/* Right Key				*/
#define	JOY_LEFT	(1 << 14)	/*  Left Key				*/
#define	JOY_DOWN	(1 << 13)	/*  Down Key				*/
#define	JOY_UP		(1 << 12)	/*    Up Key				*/
#define	JOY_START	(1 << 11)	/* Start					*/
#define	JOY_A		(1 << 10)	/* A Trigger				*/
#define	JOY_C		(1 <<  9)	/* C Trigger				*/
#define	JOY_B		(1 <<  8)	/* B Trigger				*/
#define	JOY_R		(1 <<  7)	/* R Trigger				*/
#define	JOY_X		(1 <<  6)	/* X Trigger				*/
#define	JOY_DEL		(1 <<  5)	/* DELETE					*/
#define	JOY_INS		(1 <<  4)	/* INSERT					*/
#define	JOY_END		(1 <<  3)	/* END						*/
#define	JOY_HOME	(1 <<  2)	/* HOME						*/
#define	JOY_P_DOWN	(1 <<  1)	/* PAGE_UP					*/
#define	JOY_P_UP	(1 <<  0)	/* PAGE_DOWN				*/

#define	JOY_ESC		(1 << 31)	/* ESC Key					*/
#define	JOY_CTRL_M	(1 << 30)	/* Ctrl + M					*/
#define	JOY_CTRL_S	(1 << 29)	/* Ctrl + S					*/
#define	JOY_CTRL_P	(1 << 28)	/* Ctrl + P					*/
#define	JOY_CTRL_I	(1 << 27)	/* Ctrl + I					*/
#define	JOY_CTRL_E	(1 << 26)	/* Ctrl + E					*/
#define	JOY_CTRL_A	(1 << 25)	/* Ctrl + A					*/

#define	JOY_CTRL_C	(1 << 24)	/* Ctrl + C					*/
#define	JOY_CTRL_V	(1 << 23)	/* Ctrl + V					*/

#define	VK_MAX	256 // キーの种类の最大值

// キーコード(A～Z)
/* VK_A thru VK_Z are the same as ASCII 'A' thru 'Z' (0x41 - 0x5A) */
#define	VK_A			0x41
#define	VK_B			0x42
#define	VK_C			0x43
#define	VK_D			0x44
#define	VK_E			0x45
#define	VK_F			0x46
#define	VK_G			0x47
#define	VK_H			0x48
#define	VK_I			0x49
#define	VK_J			0x4A
#define	VK_K			0x4B
#define	VK_L			0x4C
#define	VK_M			0x4D
#define	VK_N			0x4E
#define	VK_O			0x4F
#define	VK_P			0x50
#define	VK_Q			0x51
#define	VK_R			0x52
#define	VK_S			0x53
#define	VK_T			0x54
#define	VK_U			0x55
#define	VK_V			0x56
#define	VK_W			0x57
#define	VK_X			0x58
#define	VK_Y			0x59
#define	VK_Z			0x5A

// キーコード(0～9)
/* VK_0 thru VK_9 are the same as ASCII '0' thru '9' (0x30 - 0x39) */
#define	VK_0			0x30
#define	VK_1			0x31
#define	VK_2			0x32
#define	VK_3			0x33
#define	VK_4			0x34
#define	VK_5			0x35
#define	VK_6			0x36
#define	VK_7			0x37
#define	VK_8			0x38
#define	VK_9			0x39

// 特殊なキー
#define VK_USER_YEN				0xDC	// \ マーク
#define VK_USER_TILT			0xDE	// ^ マーク
#define VK_USER_SUB				0xBD	// フルキーの - マーク
#define VK_USER_OPEN_BRACKET	0xDB	// [ マーク
#define VK_USER_CLOSE_BRACKET	0xDD	// ] マーク
#define VK_USER_COMMA			0xBC	// , マーク
#define VK_USER_PERIOD			0xBE	// . マーク
#define VK_USER_SLASH			0xBF	// フルキーの / マーク
#define VK_USER_BACK_SLASH		0xE2	// フルキー右下の \ マーク
#define VK_USER_AT_MARK			0xC0	// @ マーク
#define VK_USER_COLON			0xBA	// : マーク
#define VK_USER_SEMICOLON		0xBB	// ; マーク


// キーボードの状态
#define	KEY_OFF					( 1 << 0 )		// 离した瞬间
#define	KEY_ON					( 1 << 1 )		// 押しっぱなし
#define	KEY_ON_ONCE				( 1 << 2 )		// 押された瞬间
#define	KEY_ON_ONCE_FLAG		( 1 << 3 )		// KEY_ON_ONCE用フラグ
#define KEY_ON_REP				( 1 << 4 )		// 押された瞬间（キーリピート）


// ショートカットキーのコード
enum
{
	SHORT_CUT_KEY_ESC,							//   0 ESC
	SHORT_CUT_KEY_HOME,							//   1 HOME
#if 0
	SHORT_CUT_KEY_F1,							//   2 F1
	SHORT_CUT_KEY_F2,							//   3 F2
	SHORT_CUT_KEY_F3,							//   4 F3
	SHORT_CUT_KEY_F4,							//   5 F4
	SHORT_CUT_KEY_F5,							//   6 F5
	SHORT_CUT_KEY_F6,							//   7 F6
	SHORT_CUT_KEY_F7,							//   8 F7
	SHORT_CUT_KEY_F8,							//   9 F8
#endif
	SHORT_CUT_KEY_F9,							//  10 F9
	SHORT_CUT_KEY_F10,							//  11 F10
	SHORT_CUT_KEY_F11,							//  12 F11
	SHORT_CUT_KEY_F12,							//  13 F12
	SHORT_CUT_KEY_CTRL_A,						//  14 CTRL+A
	SHORT_CUT_KEY_CTRL_B,						//  15 CTRL+B
	SHORT_CUT_KEY_CTRL_C,						//  16 CTRL+C
	SHORT_CUT_KEY_CTRL_D,						//  17 CTRL+D
	SHORT_CUT_KEY_CTRL_E,						//  18 CTRL+E
	SHORT_CUT_KEY_CTRL_F,						//  19 CTRL+F
	SHORT_CUT_KEY_CTRL_G,						//  20 CTRL+G
	SHORT_CUT_KEY_CTRL_H,						//  21 CTRL+H
	SHORT_CUT_KEY_CTRL_I,						//  22 CTRL+I
	SHORT_CUT_KEY_CTRL_J,						//  23 CTRL+J
	SHORT_CUT_KEY_CTRL_K,						//  24 CTRL+K
	SHORT_CUT_KEY_CTRL_L,						//  25 CTRL+L
	SHORT_CUT_KEY_CTRL_M,						//  26 CTRL+M
	SHORT_CUT_KEY_CTRL_N,						//  27 CTRL+N
	SHORT_CUT_KEY_CTRL_O,						//  28 CTRL+O
	SHORT_CUT_KEY_CTRL_P,						//  29 CTRL+P
	SHORT_CUT_KEY_CTRL_Q,						//  30 CTRL+Q
	SHORT_CUT_KEY_CTRL_R,						//  31 CTRL+R
	SHORT_CUT_KEY_CTRL_S,						//  32 CTRL+S
	SHORT_CUT_KEY_CTRL_T,						//  33 CTRL+T
	SHORT_CUT_KEY_CTRL_U,						//  34 CTRL+U
	SHORT_CUT_KEY_CTRL_V,						//  35 CTRL+V
	SHORT_CUT_KEY_CTRL_W,						//  36 CTRL+W
	SHORT_CUT_KEY_CTRL_X,						//  37 CTRL+X
	SHORT_CUT_KEY_CTRL_Y,						//  38 CTRL+Y
	SHORT_CUT_KEY_CTRL_Z,						//  39 CTRL+Z
	SHORT_CUT_KEY_CTRL_1,						//  40 CTRL+1(フルキー)
	SHORT_CUT_KEY_CTRL_2,						//  41 CTRL+2(フルキー)
	SHORT_CUT_KEY_CTRL_3,						//  42 CTRL+3(フルキー)
	SHORT_CUT_KEY_CTRL_4,						//  43 CTRL+4(フルキー)
	SHORT_CUT_KEY_CTRL_5,						//  44 CTRL+5(フルキー)
	SHORT_CUT_KEY_CTRL_6,						//  45 CTRL+6(フルキー)
	SHORT_CUT_KEY_CTRL_7,						//  46 CTRL+7(フルキー)
	SHORT_CUT_KEY_CTRL_8,						//  47 CTRL+8(フルキー)
	SHORT_CUT_KEY_CTRL_9,						//  48 CTRL+9(フルキー)
	SHORT_CUT_KEY_CTRL_0,						//  49 CTRL+0(フルキー)
	SHORT_CUT_KEY_CTRL_SUB,						//  50 CTRL+'-'(フルキーの - )
	SHORT_CUT_KEY_CTRL_TILT,					//  51 CTRL+'^'(フルキーの ^ )
	SHORT_CUT_KEY_CTRL_YEN,						//  52 CTRL+'\'(フルキーの \ )
	SHORT_CUT_KEY_CTRL_DOT,						//  53 CTRL+'.'
	SHORT_CUT_KEY_CTRL_SEMICOLON,				//  54 CTRL+';'
	SHORT_CUT_KEY_CTRL_COMMA,					//  55 CTRL+','
	SHORT_CUT_KEY_CTRL_COLON,					//  56 CTRL+':'
	SHORT_CUT_KEY_CTRL_SLASH,					//  57 CTRL+'/'
	SHORT_CUT_KEY_CTRL_OPEN_BRACKET,			//  58 CTRL+'['
	SHORT_CUT_KEY_CTRL_CLOSE_BRACKET,			//  59 CTRL+']'
	SHORT_CUT_KEY_CTRL_AT_MARK,					//  60 CTRL+'@'
#if 0
	SHORT_CUT_KEY_CTRL_F1,						//  61 CTRL+F1
	SHORT_CUT_KEY_CTRL_F2,						//  62 CTRL+F2
	SHORT_CUT_KEY_CTRL_F3,						//  63 CTRL+F3
	SHORT_CUT_KEY_CTRL_F4,						//  64 CTRL+F4
	SHORT_CUT_KEY_CTRL_F5,						//  65 CTRL+F5
	SHORT_CUT_KEY_CTRL_F6,						//  66 CTRL+F6
	SHORT_CUT_KEY_CTRL_F7,						//  67 CTRL+F7
	SHORT_CUT_KEY_CTRL_F8,						//  68 CTRL+F8
#endif
	SHORT_CUT_KEY_CTRL_F9,						//  69 CTRL+F9
	SHORT_CUT_KEY_CTRL_F10,						//  70 CTRL+F10
	SHORT_CUT_KEY_CTRL_F11,						//  71 CTRL+F11
	SHORT_CUT_KEY_CTRL_F12,						//  72 CTRL+F12
#if 0
	SHORT_CUT_KEY_SHIFT_F1,						//  73 SHIFT+F1
	SHORT_CUT_KEY_SHIFT_F2,						//  74 SHIFT+F2
	SHORT_CUT_KEY_SHIFT_F3,						//  75 SHIFT+F3
	SHORT_CUT_KEY_SHIFT_F4,						//  76 SHIFT+F4
	SHORT_CUT_KEY_SHIFT_F5,						//  77 SHIFT+F5
	SHORT_CUT_KEY_SHIFT_F6,						//  78 SHIFT+F6
	SHORT_CUT_KEY_SHIFT_F7,						//  79 SHIFT+F7
	SHORT_CUT_KEY_SHIFT_F8,						//  80 SHIFT+F8
#endif
	SHORT_CUT_KEY_SHIFT_F9,						//  81 SHIFT+F9
	SHORT_CUT_KEY_SHIFT_F11,					//  82 SHIFT+F11
	SHORT_CUT_KEY_SHIFT_F12,					//  83 SHIFT+F12

#if 1
#ifdef _DEBUG
	SHORT_CUT_KEY_CTRL_NUM0,					//     CTRL+テンキー0
	SHORT_CUT_KEY_CTRL_NUM1,					//     CTRL+テンキー1
#endif
#endif

	SHORT_CUT_KEY_LAST							// 終端
};


// ショートカットキー情报构造体
typedef struct
{
	unsigned char additionalKey;				// 补助キー(Shift,Ctrlなど)
	unsigned char key;							// キー
	void (*func)( void );						// 呼び出す关数へのポインタ
} SHORT_CUT_KEY_INFO;


// キーボードの状态记忆配列
extern char VK[];

// キーボード处理 ***********************************************************/
void KeyBoardProc( void );
// キーボードクリア ***********************************************************/
void KeyBoardClear( void );
// キーボードクリア２ KEY_ON もクリアする******************************************/
void KeyBoardClear2( void );

int keyOnOnce( int );
int keyOnOnceWithCtrl( int );
int keyOnOnceWithShift( int );
int keyOnRep( int );
int keyOnRepWithCtrl( int );
int keyOnRepWithShift( int );
int shortCutKeyOnOnce( int );

void checkShortCutKey( void );

#endif
