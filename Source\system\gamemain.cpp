﻿/************************/
/*	gamemain.cpp		*/
/************************/
#include "../systeminc/version.h"
#include "../systeminc/system.h"
#include <winuser.h>
#include <winbase.h>
#include "../systeminc/directDraw.h"
#include "../systeminc/direct3D.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/process.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/netmain.h"
#include "../systeminc/savedata.h"
#include "../systeminc/produce.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/t_music.h"
#include "../systeminc/dmctrl.h"
#include "../systeminc/menu.h"
#include "../systeminc/handletime.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/mouse.h"
#include "../systeminc/math2.h"

#include "../systeminc/pc.h"
#include "../systeminc/map.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/debug.h"

#include "../systeminc/filetbl.h"

#ifdef PUK2
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"
	#include "../PUK2/interface/menuwin.h"
#endif

#define NO_DRAW_MAX_CNT 20	// 描画しない最大フレーム数
#define FRAME_SKIP	1		// コマ落ちシステム ＯＮ：１　ＯＦＦ：０
// 高速ハック对处ＯＮ
#define HIGH_SPEED_CHECK

//#ifndef _DEBUG
//	#define _DEBUG	// とりあえずデバッグモード
//#endif

#ifdef _DEBUG
static char no_wait_cnt = 0;
#endif


//---------------------------------------------------------------------------//
// グローバル变数定义                                                        //
//---------------------------------------------------------------------------//
int	GameState;					// ゲームの状态
//double ProcTime = 16.666667;	// 处理に使う时间、后は描画时间
double ProcTime = (1.0/60)*1000;	// 处理に使う时间、后は描画时间
//double ProcTime = 33.333333;				// 处理に使う时间、后は描画时间
//double SystemTime = 16.666667;	// １シンクは 16 msc
double SystemTime = (1.0/60)*1000;	// １シンクは 16 msc
//double SystemTime = 0;	// １シンクは 16 msc
//float SystemTime = 33.33333333;	// １シンクは 16 msc
double NowTime; 					// 现在の时间记忆
int	  NoDrawCnt = 1;			// 描画してない回数カウンター
int BackBufferDrawType;			// バックバッファー描画方法
#ifdef PUK3_ENCOUNT_NULL_GRA
	BOOL surelyDispDrawFlag = FALSE;
#endif

#ifdef PUK2_FPS
	double SystemDrawTime = (1.0/60)*1000;	// １シンクは 16 msc
	double NowDrawTime; 					// 现在の时间记忆
#endif

int nowLoadintGraNo = 0;

#ifdef PUK2
	void RecoverDirectDraw( void );
#endif

BOOL logoProc( void );
BOOL fadeInNowLoading( void );
BOOL fadeOutNowLoading( void );
// 高速ハック对处处理
void HighSpeedCheck( void );

// 今回のマップ座标保存（海ラスタースクロールで使用）
extern void set_raster_map( void );
//	かもめ作成处理
extern void create_bird( void );

//char szFaqUrl[] = "http://saru.square-enix.co.jp/crossgate/faq/bg.html#faq";
char szFaqUrl[] = "";
#ifdef PUK2
//char szV3FaqUrl[] = "http://saru.square-enix.co.jp/crossgate/faq/download.html";
char szV3FaqUrl[] = "";
#endif

extern int InitLine, tmpInitLine, FirstLine, LastLine, LineCount;
#ifdef PUK3_PROTO_RECVTIME
	int RunCnt = 0;
#endif

#ifdef PUK3_ENCOUNT_NULL_GRA
	// これを呼んだフレームは确实に画面の更新が行われるようになる
	void surelyDispDraw()
	{
		surelyDispDrawFlag = TRUE;
	}
#endif
//---------------------------------------------------------------------------//
// 概要 ：ウインドウ制御メイン处理关数                                       //
// 引数 ：HWND hWnd : ウィンドウの识别ハンドル                               //
//---------------------------------------------------------------------------//
BOOL GameMain( void )
{
	// ゲーム开始处理
	if( InitGame() == FALSE ) return FALSE;
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

	// メインループ
	while( 1 )
	{
#ifdef PUK3_PROTO_RECVTIME
		RunCnt++;
#endif
#ifdef PUK2_PROC_USE_TIME
		initProcUseTime();
#endif
#ifdef PUK3_GRAPH
		NextGraph();
#endif
#ifdef PUK3_MEMORYLEAK
	MemCheckTimeClear();			// メモリのチェック关数
#endif
#ifdef PUK2
		// サーフェスが LOST していたら
		if( device3D != last3D_mode ){
#ifdef PUK2_DEVICE_CHANGE
			// デバイス变更前の处理
			DeviceChangeProc();
#endif
			// ＤｉｒｅｃｔＤｒａｗ复活处理
			RecoverDirectDraw();
			// パレットオブジェクトがある时
			if( lpDraw->lpPALETTE != NULL ){
				// ウィンドウモードの时
				if( WindowMode ){
					// 作成しておいたパレットに变える
					lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
				}
			}
		}
#endif
#ifdef PUK3_WINDOWMODE_BLACKOUT
		// プライマリロスト对策
		if ( PrimaryLostFlag ){
			if ( CheckRestore() ){
				// ＤｉｒｅｃｔＤｒａｗ复活处理
				RecoverDirectDraw();
				// パレットオブジェクトがある时
				if( lpDraw->lpPALETTE != NULL ){
					// ウィンドウモードの时
					if( WindowMode ){
						// 作成しておいたパレットに变える
						lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
					}
				}
			}
		}
#endif
		// 信息ループコール
		if( SystemTask() == FALSE )
		{
			EndGame(); // ゲーム終了处理
			return FALSE;
		}
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_1 );
#endif
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif
#ifdef PUK3_RAG_MAKE
	//ＳＨＩＦＴ＋ＣＴＲＬを押してたら
	if( (VK[ VK_SHIFT ]&KEY_ON) && (VK[ VK_CONTROL ]&KEY_ON) ){
		// 何も处理しない
		continue;
	}
#endif

#ifdef HIGH_SPEED_CHECK

		// 高速ハック对处处理
		HighSpeedCheck();

#endif
		// ＮＲ时间を取得
		realTimeToNRTime( &nrTime );

		// 表示カウンター初期化（コマ落ちの时も初期化するためここでする）
		DispBuffer.DispCnt = 0;
		// フォントカウンター初期化
		FontCnt = 0;
#ifdef PUK2
		// フォントプライオリティ制御バッファの初期化
		FontPrioInit();
#endif
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_2 );
#endif

		// ネットワーク处理
#ifdef _DEBUG
		if( !offlineFlag )
	#ifdef PUK3_SEGMENTATION_FAULT
			ProcCall( networkLoop(), PROCSTACK_networkLoop );
	#else
			networkLoop();
	#endif
#else
	#ifdef PUK3_SEGMENTATION_FAULT
		ProcCall( networkLoop(), PROCSTACK_networkLoop );
	#else
		networkLoop();
	#endif
#endif
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_3 );
#endif

		// ＣＤＤＡ再生（ リピート ）
		cdda_play( cdda_no );
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_4 );
#endif
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

	#ifdef PUK3_SEGMENTATION_FAULT
		ProcCall( Process(), PROCSTACK_Process );		// タスク处理
	#else
		Process();		// タスク处理
	#endif
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_5 );
#endif
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

#ifdef PUK2_NOSHOW
		// ウィンドウが非アクティブなら处理しない
		if ( WinActive != WA_NOACTIVATE ){
			MouseProc();	// マウス处理

			SortDispBuffer(); 	// 表示バッファソート

			HitMouseCursor();	// マウスカーソルのあたり判定
		}
#else
		MouseProc();	// マウス处理

		SortDispBuffer(); 	// 表示バッファソート

		HitMouseCursor();	// マウスカーソルのあたり判定
#endif
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_6 );
#endif

		PaletteProc();		// パレット处理
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_7 );
#endif

#ifdef PUK2_NOSHOW
		// ウィンドウが非アクティブなら处理しない
		if ( WinActive != WA_NOACTIVATE ){
			//かもめ作成
			create_bird();
		}
#else
		//かもめ作成
		create_bird();
#endif

#ifdef _DEBUG
		// デバッグ处理
		DebugProc();
#endif

		KeyBoardClear();	// キーボード情报クリア
#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_8 );
#endif
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

#ifdef PUK3_ENCOUNT_NULL_GRA
	// 确实に描画するなら、スキップ处理を飞ばす
	if ( !surelyDispDrawFlag ){
	#ifdef PUK2_NOSHOW
			// ウィンドウが非アクティブなら描画しない
			if ( WinActive == WA_NOACTIVATE ){
				// 时间のずれを调整
				while( NowTime >= GetTickCount() ){
					Sleep( 1 );
					// パレットチェンジフラグＯＮの时
					if( PalChangeFlag == TRUE ){

						// パレットの中身を设定
						lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
						PalChangeFlag = FALSE; // フラグ初期化
					}
				}
		#ifdef PUK2_FPS
				// 描画レート
				NowDrawTime += SystemDrawTime;
		#endif
				// システムタイムプラス
				NowTime += SystemTime;
				// 描画してない回数初期化
				NoDrawCnt = 1;

		#ifdef _DEBUG
				// 一秒间に描画した枚数をカウント
				DrawFrameCnt++;
			#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
			#endif
		#endif
				continue;
			}

			if ( WinActive == WA_ACTIVE_JUST_AFTER ) WinActive = WA_ACTIVATE;
	#endif
	#if FRAME_SKIP		// 描画时间が残っていないときは描画しない ( すぐ处理に返回 )
		#ifdef PUK2_FPS
			#ifdef _DEBUG
			if( !(VK[ VK_SHIFT ]&KEY_ON) ){
			#endif
				if( NowDrawTime > GetTickCount() ){
					// 时间のずれを调整
					while( NowTime >= GetTickCount() ){
						Sleep( 1 );
					}

					// システムタイムプラス
					NowTime += SystemTime;
					// 描画してない回数初期化
					NoDrawCnt = 1;
			#if defined(_DEBUG) && defined(PUK2_FPS)
					ProcCnt++;
			#endif
					// 描画しなくてもいい时なら
					continue;
				}
			#ifdef _DEBUG
			}
			#endif
		#endif
			if( NowTime + ProcTime < GetTickCount() ){
				// 描画しなくてもいい时なら
				if( NoDrawCnt < NO_DRAW_MAX_CNT ){
					// システムタイムプラス（ 缲越 )
					NowTime += SystemTime;
					// 描画してない回数カウント
					NoDrawCnt++;
			#if defined(_DEBUG) && defined(PUK2_FPS)
					ProcCnt++;
			#endif
					continue;
				}
			}
	#endif
	}
	surelyDispDrawFlag = FALSE;
#else
#ifdef PUK2_NOSHOW
		// ウィンドウが非アクティブなら描画しない
		if ( WinActive == WA_NOACTIVATE ){
			// 时间のずれを调整
			while( NowTime >= GetTickCount() ){
				Sleep( 1 );
				// パレットチェンジフラグＯＮの时
				if( PalChangeFlag == TRUE ){

					// パレットの中身を设定
					lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
					PalChangeFlag = FALSE; // フラグ初期化
				}
			}
	#ifdef PUK2_FPS
			// 描画レート
			NowDrawTime += SystemDrawTime;
	#endif
			// システムタイムプラス
			NowTime += SystemTime;
			// 描画してない回数初期化
			NoDrawCnt = 1;

	#ifdef _DEBUG
			// 一秒间に描画した枚数をカウント
			DrawFrameCnt++;
		#if defined(_DEBUG) && defined(PUK2_FPS)
			ProcCnt++;
		#endif
	#endif
			continue;
		}

		if ( WinActive == WA_ACTIVE_JUST_AFTER ) WinActive = WA_ACTIVATE;
#endif
#if FRAME_SKIP		// 描画时间が残っていないときは描画しない ( すぐ处理に返回 )
	#ifdef PUK2_FPS
		#ifdef _DEBUG
		if( !(VK[ VK_SHIFT ]&KEY_ON) ){
		#endif
			if( NowDrawTime > GetTickCount() ){
				// 时间のずれを调整
				while( NowTime >= GetTickCount() ){
					Sleep( 1 );
				}

				// システムタイムプラス
				NowTime += SystemTime;
				// 描画してない回数初期化
				NoDrawCnt = 1;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				// 描画しなくてもいい时なら
				continue;
			}
		#ifdef _DEBUG
		}
		#endif
	#endif
		if( NowTime + ProcTime < GetTickCount() ){
			// 描画しなくてもいい时なら
			if( NoDrawCnt < NO_DRAW_MAX_CNT ){
				// システムタイムプラス（ 缲越 )
				NowTime += SystemTime;
				// 描画してない回数カウント
				NoDrawCnt++;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				continue;
			}
		}
#endif
#endif
#ifdef _DEBUG
		// 现在表示しているサーフェス数の初期化
		SurfaceDispCnt = 0;

		// 描画时间（緑）（ デバッグ用 ）
#ifdef PUK2
		if (CmdLineFlg&CMDLINE_DEBUGLINE) DrawDebugLine( 250 );
#else
		DrawDebugLine( 250 );
#endif

#endif

		#ifdef DIRECT3D_ON
		lpD3DViewport->Clear();				// ビューポートのクリア
		#endif

		// バックバッファー描画方法で分岐
		switch( BackBufferDrawType ){

			case DRAW_BACK_NON:		// 描画しない
				break;

			case DRAW_BACK_NORMAL:	// バックサーフェスを黒でクリアー

				ClearBackSurface();
				break;

			case DRAW_BACK_PRODUCE:	// 演出中
				//ClearBackSurface();
				break;

			case DRAW_BACK_BATTLE:	// バトルマップ描画

				DrawBattleMap();
				break;
		}


#ifdef _DEBUG
		no_wait_cnt++;
		no_wait_cnt&=7;
		//ＳＨＩＦＴを押してたら
		if( VK[ VK_SHIFT ] & KEY_ON ){
			if(!no_wait_cnt){
				PutBmp();	// ＢＭＰをバックサーフェスにセット
				// 高速描画处理の现在のマップ位置を记忆
				memoryHighSpeedDraw();

				#ifdef DIRECT3D_ON
				// シーンをレンダリングします
				//lpD3DViewport->Clear();				// ビューポートのクリア
				lpD3DViewport->Render( lpD3DScene );	// シーンのレンダリング
				#endif
			}
		} else {
			PutBmp();	// ＢＭＰをバックサーフェスにセット
			// 高速描画处理の现在のマップ位置を记忆
			memoryHighSpeedDraw();

			#ifdef DIRECT3D_ON
			// シーンをレンダリングします
			//lpD3DViewport->Clear();				// ビューポートのクリア
			lpD3DViewport->Render( lpD3DScene );	// シーンのレンダリング
			#endif
		}
#else

	#ifdef PUK3_SEGMENTATION_FAULT
		ProcCall( PutBmp(), PROCSTACK_PutBmp );	// ＢＭＰをバックサーフェスにセット
	#else
		PutBmp();	// ＢＭＰをバックサーフェスにセット
	#endif
		// 高速描画处理の现在のマップ位置を记忆
		memoryHighSpeedDraw();

		#ifdef DIRECT3D_ON
		// シーンをレンダリングします
		//lpD3DViewport->Clear();				// ビューポートのクリア
		lpD3DViewport->Render( lpD3DScene );	// シーンのレンダリング
		#endif

#endif

#ifdef _DEBUG

		// 余った时间（黒）（ デバッグ用 ）
#ifdef PUK2
		if (CmdLineFlg&CMDLINE_DEBUGLINE) DrawDebugLine( 0 );
#else
		DrawDebugLine( 0 );
#endif

#endif

#ifdef PUK2_PROC_USE_TIME
		SetProcPoint( PPCOLOR_9 );
		if (CmdLineFlg&CMDLINE_DEBUGLINE){
			Draw_ProcUseTime();
	#ifdef PUK3_GRAPH
			Draw_Graph();
	#endif
			// デバッグ描画データ描画
			_Debug_Draw_DrawFunc();
		}
#else
#ifdef PUK3_GRAPH
		if (CmdLineFlg&CMDLINE_DEBUGLINE){
			Draw_Graph();
			// デバッグ描画データ描画
			_Debug_Draw_DrawFunc();
		}
#endif
#endif
#ifdef _DEBUG
		//ＳＨＩＦＴを押してたら
		if( VK[ VK_SHIFT ] & KEY_ON ){
			if(!no_wait_cnt){
				Flip();	// 垂直同期线时の切り替え处理
			}
		} else {
			Flip();	// 垂直同期线时の切り替え处理
		}
#else
		Flip();	// 垂直同期线时の切り替え处理
#endif

#ifdef _DEBUG
		// 处理时间（赤）（ デバッグ用 ）
#ifdef PUK2
		if (CmdLineFlg&CMDLINE_DEBUGLINE) DrawDebugLine( 249 );
#else
		DrawDebugLine( 249 );
#endif

#endif


#if FRAME_SKIP
#ifdef _DEBUG
		//ＳＨＩＦＴを押してたら
		if( VK[ VK_SHIFT ] & KEY_ON ){
			// ノーウエイト
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
		} else {
			// 时间のずれを调整
			while( NowTime >= GetTickCount() ){
				Sleep( 1 );
			}
		}
#else
		// 时间のずれを调整
		while( NowTime >= GetTickCount() ){
			Sleep( 1 );
		}

#endif
#endif
		// パレットチェンジフラグＯＮの时
		if( PalChangeFlag == TRUE ){

			// パレットの中身を设定
			lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			PalChangeFlag = FALSE; // フラグ初期化
		}

#ifdef PUK2_FPS
		// 描画レート
		NowDrawTime += SystemDrawTime;
#endif
		// システムタイムプラス
		NowTime += SystemTime;
		// 描画してない回数初期化
		NoDrawCnt = 1;

#ifdef _DEBUG
		// 一秒间に描画した枚数をカウント
    	DrawFrameCnt++;
	#if defined(_DEBUG) && defined(PUK2_FPS)
		ProcCnt++;
	#endif
#endif
		// サーフェスを使用した日付を更新
		SurfaceDate++;
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

		//今回のマップ座标保存（海ラスタースクロールで使用）
		set_raster_map();
	}

	return FALSE;
}


// 固定バッファを确保する。
int AllocStaticBuffer( void ){
	int i, Count = 0;
	FILE *tmpFp;

	for( i = 0; i < BINMODE_MAX; i ++ ){
		tmpFp = NULL;
	}

	// BINがいくつあるか数える。
	for( i = 0; i < BINMODE_MAX; i ++ ){
		if((tmpFp = fopen(graphicInfoBinName[i], "rb"))==NULL){             //MLHIDE
			continue;
		}
		fclose( tmpFp );	// すぐ关闭。
		Count ++;	// オープンしたら数え??
	}

	// 扩张版になったので大きなバッファを确保。
	mxSPRITE = mxSPRITE_MULTI;
	maxBUFFER = maxBUFFER_MULTI;
	MAX_GRAPHICS = MAX_GRAPHICS_MULTI;
	// ついでにここでアルバムページも设定する。
	// アルバムモンスターの数
	ALBUM_MAX = ALBUM_MAX_MULTI;
	// 今は１５ページ
	ALBUM_LIST_PAGE = (ALBUM_MAX / ALBUM_LIST_LINE);

	// 实际に确保
	SpriteData = (SPRITEDATA *)malloc( sizeof( SPRITEDATA )* mxSPRITE );
	SpriteInfo = (SPRITE_INFO *)malloc( sizeof( SPRITE_INFO )* MAX_GRAPHICS );
	adrnbuff = (ADRNBIN *)malloc( sizeof( ADRNBIN ) * MAX_GRAPHICS );
#ifdef PUK2_MEMCHECK
	memlistset( SpriteData, MEMLISTTYPE_SPRITEDATA );
	memlistset( SpriteInfo, MEMLISTTYPE_SPRITE_INFO );
	memlistset( adrnbuff, MEMLISTTYPE_ADRNBIN );
#endif

	if( SpriteData == NULL )goto Err_End;
	if( SpriteInfo == NULL )goto Err_End;
	if( adrnbuff == NULL )goto Err_End;

	// ゼロクリア
	memset( SpriteData, 0, sizeof( SPRITEDATA )* mxSPRITE );
	memset( SpriteInfo, 0, sizeof( SPRITE_INFO )* MAX_GRAPHICS );
	memset( adrnbuff, 0, sizeof( ADRNBIN ) * MAX_GRAPHICS );

	return TRUE;

Err_End:;
#ifdef PUK2_MEMCHECK
	if( SpriteData != NULL )memlistrel( SpriteData, MEMLISTTYPE_SPRITEDATA );
	if( SpriteInfo != NULL )memlistrel( SpriteInfo, MEMLISTTYPE_SPRITE_INFO );
	if( adrnbuff != NULL ) memlistrel( adrnbuff, MEMLISTTYPE_ADRNBIN );
#endif
	if( SpriteData != NULL )free( SpriteData );
	if( SpriteInfo != NULL )free( SpriteInfo );
	if( adrnbuff != NULL ) free( adrnbuff );

	return FALSE;

}
#ifdef PUK3_NOTFREE_ANIMEBINBUF
void FreeStaticBuffer( void )
{
#ifdef PUK2_MEMCHECK
	if( SpriteData != NULL ) memlistrel( SpriteData, MEMLISTTYPE_SPRITEDATA );
	if( SpriteInfo != NULL ) memlistrel( SpriteInfo, MEMLISTTYPE_SPRITE_INFO );
	if( adrnbuff != NULL )   memlistrel( adrnbuff, MEMLISTTYPE_ADRNBIN );
#endif
	if( SpriteData != NULL ) free( SpriteData );
	if( SpriteInfo != NULL ) free( SpriteInfo );
	if( adrnbuff != NULL )   free( adrnbuff );
}
#endif

// ゲーム开始处理 //////////////////////////////////////////////////////////////
BOOL InitGame( void )
{
#ifdef PUK2
	int ret;
#endif

	// システムログファイル初期化
	InitSystemLogfile();
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル初期化
	InitMemoryLogfile();
#endif
	InitMousePointList( );

#ifdef _DEBUG
	// グラフィックビンサイズチェックフラグする时
	if( gracheckOffFlag == FALSE ){
#endif

//#ifndef MULTI_GRABIN
		if( strcmp( LANGAGE, "Chinese" ) == 1 ) {                           //MLHIDE
			if( AllCheckBinFile( ) == FALSE ) {
/* temp ingore the error by litchi
	#ifdef PUK3_ERRORMESSAGE_NUM
				if( MessageBox( hWnd, INFOMSG_12, "INFOMSG_12", MB_OKCANCEL | MB_ICONSTOP ) == IDOK ) {
	#else
				if( MessageBox( hWnd, "FAQ 12", "INFOMSG_12", MB_OKCANCEL | MB_ICONSTOP ) == IDOK ) {
	#endif
					ShellExecute(hWnd, NULL, szFaqUrl, NULL, NULL, SW_SHOWNORMAL);
					return FALSE;
				}
*/
			}
		}
//#endif
#ifdef _DEBUG
	}
#endif

	// DirectDraw 初期化
#ifdef PUK2
	switch( InitDirectDraw() ){
	case 1:
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_81, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "DDraw初始化失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
		break;
	case 2:
		// ウィンドウモードフラグ变更
		if( WindowMode == TRUE ) WindowMode = FALSE;
		else WindowMode = TRUE;
		if( InitDirectDraw() ){
	#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_82, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
			MessageBox( hWnd, "DDraw初始化失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
			// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			PostMessage( hWnd, WM_CLOSE, 0, 0L );
		}
		break;
	}
	#ifdef PUK2
		// 色变え制限をＯＮ状态に初期化
		LimiteLoadBmpFlag = TRUE;
	#endif
#else
	if( InitDirectDraw() == FALSE ){
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_83, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "DDraw初始化失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		return FALSE;
	}
#endif

	/* OFFスクリーンサーフェスの作成 */
	if( InitOffScreenSurface() == FALSE ){
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_84, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "Off Screen Surface生成失白。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		return FALSE;
	}

#ifdef PUK2
	// パレットリスト初期化
	IniPalList();
#endif

	// パレット初期化
	if( InitPalette() == FALSE ) return FALSE;

#ifdef DIRECT3D_ON
	// Direct3Dの保持モードを初期化します
	if( InitDirect3DRetainedMode() == FALSE ){
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_85, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "D3D初始化失败。", "????????", MB_OK | MB_ICONSTOP );   //MLHIDE
	#endif
		return FALSE;
	}
#endif

	// 固定バッファの确保
	if( AllocStaticBuffer( ) == FALSE ){
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_86, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "申请内存失败。", "????????", MB_OK | MB_ICONSTOP );     //MLHIDE
	#endif
		return FALSE;
	}
#ifdef PUK2
	InitSpriteInfo();
#endif

#ifdef PUK2
	// Realbin 关连の初期化
	ret = initRealbinFileOpen( graphicBinName, graphicInfoBinName );
	switch(ret){
	case 1:
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_87, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "Graphic.bin 开启失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		return FALSE;
	case 2:
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_88, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
		MessageBox( hWnd, INFOMSG_13, "信息", MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
		MessageBox( hWnd, "开启GraphicV3.bin失败", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
		MessageBox( hWnd, "文件已经损坏，需要新的客户端。按OK获得详细说明。", "信息", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		ShellExecute(hWnd, NULL, szV3FaqUrl, NULL, NULL, SW_SHOWNORMAL);
		return FALSE;
	}
#else

#ifdef MULTI_GRABIN
	// Realbin 关连の初期化
	if( initRealbinFileOpen( graphicBinName, graphicInfoBinName ) == FALSE ){
	//if( initRealbinFileOpen( "..\\real.bin", "..\\adrn.bin" ) == FALSE ) {
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_89, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "Graphic.bin 开启失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		//MessageBox( hWnd, "请通过SaUpadte.exe启动", "????????", MB_OK | MB_ICONSTOP );
		return FALSE;
	}
#else
	// Realbin 关连の初期化
	if( initRealbinFileOpen( graphicBinName, graphicInfoBinName ) == FALSE ){
	//if( initRealbinFileOpen( "..\\real.bin", "..\\adrn.bin" ) == FALSE ) {
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_90, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd, "Graphic.bin 开启失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		//MessageBox( hWnd, "请通过SaUpadte.exe启动", "????????", MB_OK | MB_ICONSTOP );
		return FALSE;
	}
#endif

#endif

#ifdef PUK2
	// SprPal初期化
	InitSprPal();
#endif
#ifdef PUK3_EXETYPE_LOG
	#if defined(_CGXL)
		WriteSystemLogfile( "ExeType CgXL" );                               //MLHIDE
	#elif defined(_CGL)
		WriteSystemLogfile( "ExeType CgL" );                                //MLHIDE
	#elif defined(_CGS)
		WriteSystemLogfile( "ExeType CgS" );                                //MLHIDE
	#elif defined(_RELDEB)
		WriteSystemLogfile( "ExeType CgRelDeb" );                           //MLHIDE
	#endif
	#if defined(_DEBUG)
		WriteSystemLogfile( "DebugExe" );                                   //MLHIDE
	#endif
#endif

	// 乱数系列初期化
	srand( ( unsigned int )GetTickCount() );

	// 乱数初期化
	initRand2();

	// マウス处理の初期化
	MouseInit();

	/* アクション初期化 */
	InitAction();

	// フォントオブジェクトの作成 */
	InitFont( 0 );
	//InitFont( 2 );

	// ＩＭＥ关连の初期化（ここでやらないと、ＭＥ＋ＩＭＥ２０００で落ちる）
	InitIme();

	// CDキー、パスワード読み込み
	if( loadUserSetting() == FALSE )
	{
		char msg[1024];
	#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( msg, ERRMSG_91, savedataErrorCode );
	#else
		sprintf( msg, SAVE_ERRMSG_loadNowState, savedataErrorCode );
	#endif
		MessageBox( hWnd, msg, "确认", MB_OK | MB_ICONSTOP );                 //MLHIDE
		return FALSE;
	}

	// 快适マウスでない时
	if( MouseCursorFlag == FALSE )
	{
		// マウスカーソルを隠す
		ShowCursor( FALSE );
	}
#ifdef VERSION_TW
	//修复显示老版鼠标
	else {
		ShowCursor(FALSE);
		StockDispBuffer(
			mouse.nowPoint.x + 16,
			mouse.nowPoint.y + 16,
			DISP_PRIO_MOUSE, CG_MOUSE_CURSOR, 0, 0);
	}
#endif

#ifdef PUK2
	LimiteLoadBmpFlag = FALSE;
#endif
#ifdef _DEBUG
	// ロゴＯＦＦモードの设定
	if( logoOffFlag == FALSE )
	{
		// ロゴ处理
		if( !logoProc() )
		{
			return FALSE;
		}
	}
#else
	// ロゴ处理
	if( !logoProc() )
	{
		return FALSE;
	}
#endif

	// システムログファイル书き込み
	WriteSystemLogfile( "fadeInNowLoading Start" );                      //MLHIDE

	// NOW LOADING の表示
	if( !fadeInNowLoading() )
	{
		return FALSE;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "initAutoMapColor Start" );                      //MLHIDE

	// オートマップ初期化处理
	initAutoMapColor( graphicInfoBinName );

	// システムログファイル书き込み
	WriteSystemLogfile( "InitSprBinFileOpen Start" );                    //MLHIDE


	// SprBin 关连の初期化
	{ int iSprBinSuccess = 0;
		iSprBinSuccess = InitSprBinFileOpen( animeBinName, animeInfoBinName );
#ifdef _DEBUG
		if( iSprBinSuccess == LOADSPRBIN_SUCCESS ){
			// 成功
		}else
		if( iSprBinSuccess == ERR_LOADSPRBIN_OVER_TBBUFFER ){	// もう一度メモリを确保しなおし。
			MessageBox( hWnd, "增加默认 TBbuffer。现在进行再申请。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
			iSprBinSuccess = InitSprBinFileOpen( animeBinName, animeInfoBinName );
			if( iSprBinSuccess != 0 ){
				MessageBox( hWnd, "Anime.bin 打开失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
				return FALSE;
			}
		}else{
			MessageBox( hWnd, "Anime.bin 打开失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
			return FALSE;
		}
#else
		if( iSprBinSuccess != LOADSPRBIN_SUCCESS ){
			// 失败
	#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_92, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
			MessageBox( hWnd, "Anime.bin 打开失败。", "????????", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
			return FALSE;
		}
#endif
#ifdef PUK3_RIDEBIN
		// 座标データビン使用の初期化
	#ifdef _DEBUG
		InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName, coordinateLogName );
	#else
		InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName );
	#endif
#endif
	}
	// システムログファイル书き込み
	WriteSystemLogfile( "t_music_init Start" );                          //MLHIDE

	// DirectSound 初期化
	t_music_init();

#ifdef DIRECT_MUSIC		//DirectMusicシステム
	// create DMObjectInstance
	dmc_InitDirectMusic( hWnd );
#endif

	// ＩＭＥ关连の初期化（ここでやると、ＭＥ＋ＩＭＥ２０００で落ちる）
	//InitIme();

	// システムログファイル书き込み
	WriteSystemLogfile( "initAlbumStartUp Start" );                      //MLHIDE

	// アルバムのページ数をセットする。
	// アルバム初期化
	initAlbumStartUp();


	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif

#ifdef _DEBUG
	DrawFrameTime = GetTickCount();
#endif


	ProcNo2 = -1;


	// システムログファイル书き込み
	WriteSystemLogfile( "fadeOutNowLoading Start" );                     //MLHIDE

	// NOW LOADING のフェードアウト
	if( !fadeOutNowLoading() )
	{
		return FALSE;
	}
#ifdef PUK2
	LimiteLoadBmpFlag = TRUE;
#endif

	// システムログファイル书き込み
	WriteSystemLogfile( "initGame OK" );                                 //MLHIDE

#ifdef PUK2
	//起动时一回だけのメニューの初期化
	MenuWindowStartExe();
#endif

	return TRUE;
}

// ゲーム終了处理 //////////////////////////////////////////////////////////////
void EndGame( void )
{
#ifdef PUK3_NOTFREE_WINDDOW
	// 終了时一回だけのメニューの处理
	MenuWindowEndExe();
#endif
#ifdef PUK2
	// SprPal解放
	RelSprPal();
#endif
#ifdef PUK2
	// パレットリスト解放
	RelPalList();
#endif
#ifdef DIRECT3D_ON
	// Direct3Dの保持モードを終了します
	ReleaseDirect3DRetainedMode();
#endif
	//DirectDraw 开放
	ReleaseDirectDraw();
#ifdef DIRECT_MUSIC		//DirectMusicシステム
	// release DMObjectInstance
	dmc_UninitDirectMusic();
#endif
	// DirectSound 开放
	t_music_end();
	// ＣＤＤＡ停止
	cdda_stop();
#ifdef PUK3_NOTFREE_ANIMEDATA
	CloseSprBinFileOpen();
#endif
	// Rialbin 关闭
	cleanupRealbin();
#ifdef PUK3_NOTFREE_ANIMEBINBUF
	FreeStaticBuffer();
#endif
	// フォントオブジェクトを开放
	ReleaseFont();
	// ＩＭＥの終了
	EndIme();
	// アクション終了处理
	EndAction();
	// ネットワーク終了处理
	cleanupNetwork();
#ifdef PUK3_NOTFREE_MOUSE
	// マウスの解放
	RelMousePointList();
#endif
	// ＩＭＥ变换候补メモリの开放
//	if( CandList != NULL ) free( CandList );
	// 同时起动チェックオブジェクト开放
	ReleaseMutex( hMutex );

	// Windows 2000 と Me のユーザー设定デスクトップパレットを元に戾す
	RestoreUserDeskTopColor();

	// タイマー終了
//	timeKillEvent(timerId);

	//{
		// ALT+TAB 有效にする
	//	int nOldVal;
	//	SystemParametersInfo (SPI_SCREENSAVERRUNNING, FALSE, &nOldVal, 0);
	//}
#ifdef PUK3_NOTFREE_WINDDOW
	// 呼び出し位置の移动
	// このままだとアクション解放してるのでメモリ破坏起こす
#else
#ifdef PUK2
	//終了时一回だけのメニューの处理
	MenuWindowEndExe();
#endif
#endif
}


#ifdef HIGH_SPEED_CHECK

// 高速ハック对处处理 ****************************************************/
void HighSpeedCheck( void )
{
#define HIGH_SPEED_CHECK_INTERVAL 3600 // 一分に一回
extern void nrproto_FC_send( int fd );

	static int 	bak = 0;		// バックアップ用
	static int	speedCnt = 0;	// メインループの处理回数カウント

	// ネットワークが初期化されてなかったら返回
	if( networkFlag == FALSE ) return;

	// カウンタープラス
	speedCnt++;

	// 高速ハック对处处理（メインループが指定回数まわると送信する）
	if( speedCnt - bak >= HIGH_SPEED_CHECK_INTERVAL ){
		// バックアップ更新
		bak = speedCnt;
		// 送信
		nrproto_FC_send( sockfd );
	}
}
#endif

#if 0
void endGame( void )
{
	EndGame();
	// パラメータセーブ
	saveUserSetting();
	// アルバムセーブ
	writeAlbumFile();

}
#endif

enum {
	LOGO_PROC_E_FIRST,	// 最初の黒パレット
#ifdef VERSION_TW
	LOGO_PROC_E_JOYPARK_SET,	// JOYPARKロゴセット
	LOGO_PROC_E_JOYPARK_FADEIN,	// JOYPARKロゴフェードイン
	LOGO_PROC_E_JOYPARK_KEEPING,// JOYPARKロゴ表示中
	LOGO_PROC_E_JOYPARK_FADEOUT,// JOYPARKロゴフェードアウト

	LOGO_PROC_E_SS_SET,			// SOFTSTARロゴセット
	LOGO_PROC_E_SS_FADEIN,		// SOFTSTARロゴフェードイン
	LOGO_PROC_E_SS_KEEPING,		// SOFTSTARロゴ表示中
	LOGO_PROC_E_SS_FADEOUT,		// SOFTSTARロゴフェードアウト
#endif
	LOGO_PROC_E_ENIX_SET,		// ENIXロゴセット
	LOGO_PROC_E_ENIX_FADEIN,	// ENIXロゴフェードイン
	LOGO_PROC_E_ENIX_KEEPING,	// ENIXロゴ表示中
	LOGO_PROC_E_ENIX_FADEOUT,	// ENIXロゴフェードアウト

	LOGO_PROC_E_DWANGO_SET,		// DWANGOロゴセット
	LOGO_PROC_E_DWANGO_FADEIN,	// DWANGOロゴフェードイン
	LOGO_PROC_E_DWANGO_KEEPING,	// DWANGOロゴ表示中
	LOGO_PROC_E_DWANGO_FADEOUT,	// DWANGOロゴフェードアウト

	LOGO_PROC_E_ZENER_SET,		// ZENERロゴセット
	LOGO_PROC_E_ZENER_FADEIN,	// ZENERロゴフェードイン
	LOGO_PROC_E_ZENER_KEEPING,	// ZENERロゴ表示中
	LOGO_PROC_E_ZENER_FADEOUT,	// ZENERロゴフェードアウト

	LOGO_PROC_E_SKIP_1,			// 表示をスキップ
	LOGO_PROC_E_SKIP_2,			//
	LOGO_PROC_E_SKIP_3,			//

	LOGO_PROC_INSTALL_ERR,		// インストールエラー
	LOGO_PROC_INSTALL_ERR_WAIT,	//

#ifdef PUK2
	LOGO_PROC_E_PONSBIC_SET,	// Ponsbicロゴセット
	LOGO_PROC_E_PONSBIC_FADEIN,	// Ponsbicロゴフェードイン
	LOGO_PROC_E_PONSBIC_KEEPING,// Ponsbicロゴ表示中
	LOGO_PROC_E_PONSBIC_FADEOUT,// Ponsbicロゴフェードアウト
#endif
#ifdef VERSION_TW
	LOGO_PROC_E_SE_SET,			// Square Enixロゴセット
	LOGO_PROC_E_SE_FADEIN,		// Square Enixロゴフェードイン
	LOGO_PROC_E_SE_KEEPING,		// Square Enixロゴ表示中
	LOGO_PROC_E_SE_FADEOUT,		// Square Enixロゴフェードアウト
#endif
};

int GetInstallVersion(void);
void initCommonMsgWin(void);
int commonMsgWin(char*);

#ifdef VERSION_TW
// ロゴ处理 ------------------------------------------------
BOOL logoProc(void)
{
	int LogoGraphic = 0;		// 最初はエニックスロゴ
	int proc = 0;
	int loopFlag = 1;
	unsigned int t;
#ifdef PUK2
	static BLT_MEMBER bm = { 0 };
#endif

	// ここでもインストールバージョンを决定しておく
	giInstallVersion = GetInstallVersion();

	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif

	// ループ
	while (loopFlag)
	{
		// 信息ループコール
		if (SystemTask() == FALSE)
		{
			EndGame();
			return FALSE;
		}

		// 表示カウンター初期化（コマ落ちの时も初期化するためここでする）
		DispBuffer.DispCnt = 0;
		// フォントカウンター初期化
		FontCnt = 0;
#ifdef PUK2
		// フォントプライオリティ制御バッファの初期化
		FontPrioInit();
#endif

		// マウスまたはキーが押されたらスキップ
		if ((mouse.onceState & MOUSE_LEFT_CRICK)
			|| (mouse.onceState & MOUSE_RIGHT_CRICK)
			|| (VK[VK_RETURN] & KEY_ON)
			|| (VK[VK_SPACE] & KEY_ON)
			|| (VK[VK_ESCAPE] & KEY_ON))
		{
			if (proc < LOGO_PROC_E_SKIP_1) {
				proc = LOGO_PROC_E_SKIP_1;
			}
#ifdef PUK2
			if (LOGO_PROC_E_SE_SET <= proc && proc <= LOGO_PROC_E_SE_FADEOUT) {
				proc = LOGO_PROC_E_SKIP_1;
			}
#endif
		}

		switch (proc)
		{

		case LOGO_PROC_E_FIRST:
#ifdef PUK2
			// バックサーフェースを黒で初期化
			ClearBackSurface();
			// 绘图设定の初期化
			bm.u = bm.v = bm.w = bm.h = 0;
			bm.rgba.r = 0;
			bm.rgba.g = 0;
			bm.rgba.b = 0;
			bm.rgba.a = 255;
			bm.BltVer = BLTVER_NOMAL;
			bm.bltf = 0;
			bm.PalNo = 0;
#endif
			// 黒パレットに变更
			PaletteChange(19, 1);
			proc = LOGO_PROC_E_JOYPARK_SET;	// ENIXロゴセットへ
			DeathAllAction();
			break;

		case LOGO_PROC_E_JOYPARK_SET:
			LogoGraphic = TW_CG_JOYPARK_LOGO;	// JOYPARKロゴ表示
			PaletteChange(23, 30);	// JOYPARKロゴパレットへ30フレームで变更
			proc = LOGO_PROC_E_SS_SET;
			break;

		case LOGO_PROC_E_JOYPARK_FADEIN:	// JOYPARKフェードイン中
			if (palChageStatus == FALSE) {
				t = GetTickCount();
				proc = LOGO_PROC_E_JOYPARK_KEEPING;
			}
			break;

		case LOGO_PROC_E_JOYPARK_KEEPING:	// JOYPARK 表示中
			// 240フレーム后、30フレームで黒パレットへ
			if (t + 4000 < GetTickCount()) {
				PaletteChange(19, 30);	// 黒パレットに变更
				proc = LOGO_PROC_E_JOYPARK_FADEOUT;
			}
			break;

		case LOGO_PROC_E_JOYPARK_FADEOUT:		// フェードアウト中
			if (palChageStatus == FALSE) {	// フェードアウト終わったら
				int BmpNo;
				// ここでポンスビックの画像があったらポンスビックロゴを表示に行く。
				// なかったらツェナワークスロゴへ行く
				realGetNo(CG_PONSBIC_LOGO, (U4*)&BmpNo);
				if (BmpNo < 1) {
					realGetNo(CG_ZENER_LOGO, (U4*)&BmpNo);
				}
				proc = LOGO_PROC_E_SS_SET;
			}
			break;

			//--------------  SOFTSTAR -----------------------
		case LOGO_PROC_E_SS_SET:
			LogoGraphic = TW_CG_SOFTSTAR_LOGO;	// SOFTSTARロゴ表示
			PaletteChange(23, 30);	// SOFTSTARロゴパレットへ30フレームで变更
			proc = LOGO_PROC_E_SS_FADEIN;
			break;

		case LOGO_PROC_E_SS_FADEIN:	// SOFTSTARフェードイン中
			if (palChageStatus == FALSE) {
				t = GetTickCount();
				proc = LOGO_PROC_E_SS_KEEPING;
			}
			break;

		case LOGO_PROC_E_SS_KEEPING:	// SOFTSTAR 表示中
			// 240フレーム后、30フレームで黒パレットへ
			if (t + 4000 < GetTickCount()) {
				PaletteChange(19, 30);	// 黒パレットに变更
				proc = LOGO_PROC_E_SS_FADEOUT;
			}
			break;

		case LOGO_PROC_E_SS_FADEOUT:		// フェードアウト中
			if (palChageStatus == FALSE) {	// フェードアウト終わったら
				proc = LOGO_PROC_E_ENIX_SET;
			}
			break;

			//--------------  ＳＱＵＡＲＥ－ＥＮＩＸロゴ -------------------
		case LOGO_PROC_E_ENIX_SET:
			LogoGraphic = CG_ENIX_LOGO;	// SQUARE-ENIXロゴ表示
			PaletteChange(34, 30);	// SQUARE-ENIXロゴパレットへ30フレームで变更
			proc = LOGO_PROC_E_ENIX_FADEIN;
			break;

		case LOGO_PROC_E_ENIX_FADEIN:	// ENIXフェードイン中
			if (palChageStatus == FALSE) {
				t = GetTickCount();
				proc = LOGO_PROC_E_ENIX_KEEPING;
			}
			break;

		case LOGO_PROC_E_ENIX_KEEPING:	// ENIX 表示中
			// 240フレーム后、30フレームで黒パレットへ
			if (t + 4000 < GetTickCount()) {
				PaletteChange(19, 30);	// 黒パレットに变更
				proc = LOGO_PROC_E_ENIX_FADEOUT;
			}
			break;

		case LOGO_PROC_E_ENIX_FADEOUT:		// フェードアウト中
			if (palChageStatus == FALSE) {	// フェードアウト終わったら
				int BmpNo;
#ifdef PUK2
				// ここでツェナワークスの画像があったらツェナを表示に行く。
				// なかったらドワンゴロゴへ行く
				realGetNo(TW_CG_ZENER_LOGO, (U4*)&BmpNo);
				if (BmpNo >= 1) {
					proc = LOGO_PROC_E_ZENER_SET;
				}
				else {
					proc = LOGO_PROC_E_DWANGO_SET;
				}

#else
				// ここでツェナワークスの画像があったらツェナを表示に行く。
				// なかったらドワンゴロゴへ行く
				realGetNo(CG_ZENER_LOGO, (U4*)&BmpNo);
				if (BmpNo >= 1) {
					proc = LOGO_PROC_E_ZENER_SET;
				}
				else {
					proc = LOGO_PROC_E_DWANGO_SET;
				}
#endif
				}
			break;

#ifdef PUK2
			//--------------  ポンスビックロゴ -------------------
		case LOGO_PROC_E_PONSBIC_SET:	// ポンスビック表示
			LogoGraphic = CG_PONSBIC_LOGO;
			if (getUsable3D()) {
				// 昼パレットに变更
				PaletteChange(0, 0);
				// 表示に使う色の度合いを０に设定する
				bm.rgba.r = bm.rgba.g = bm.rgba.b = 0;
			}
			// 昼パレットへ30フレームで变更
			else
			{
				PaletteChange(0, 30);
			}
			proc = LOGO_PROC_E_PONSBIC_FADEIN;
			break;

		case LOGO_PROC_E_PONSBIC_FADEIN:	// ポンスビックフェードイン
			if (getUsable3D()) {
				if (bm.rgba.r + 4 > 255) {
					bm.rgba.r = 255;
					bm.rgba.g = 255;
					bm.rgba.b = 255;
					t = GetTickCount();
					proc = LOGO_PROC_E_PONSBIC_KEEPING;
				}
				else {
					bm.rgba.r += 4;
					bm.rgba.g += 4;
					bm.rgba.b += 4;
				}
			}
			else
			{
				if (palChageStatus == FALSE) {
					t = GetTickCount();
					proc = LOGO_PROC_E_PONSBIC_KEEPING;
				}
			}
			break;

		case LOGO_PROC_E_PONSBIC_KEEPING:	// ポンスビック表示中
			// 120フレーム后、３Ｄ机能を使用しないなら、30フレームで黒パレットへ
			if (t + 2000 < GetTickCount()) {
				if (!getUsable3D())
				{
					PaletteChange(19, 30);	// 黒パレットに变更
				}
				proc = LOGO_PROC_E_PONSBIC_FADEOUT;
			}
			break;

		case LOGO_PROC_E_PONSBIC_FADEOUT:
			if (getUsable3D()) {
				if (bm.rgba.r != 0) {
					if (bm.rgba.r - 4 < 0) { bm.rgba.r = bm.rgba.g = bm.rgba.b = 0; }
					else { bm.rgba.r -= 4, bm.rgba.g -= 4, bm.rgba.b -= 4; }
				}
				else {
					if (getUsable3D()) PaletteChange(19, 0);	// 黒パレットに变更
					proc = LOGO_PROC_E_SE_SET;
				}
			}
			else
			{
				if (palChageStatus == FALSE) {	// パレットチェンジ末
					proc = LOGO_PROC_E_SE_SET;
				}
			}
			break;
#endif

			//--------------  ＳＱＵＡＲＥ－ＥＮＩＸロゴ -------------------
		case LOGO_PROC_E_SE_SET:
			mouse.flag = 1;		//显示鼠标
			LogoGraphic = TW_CG_SQUAREENIX_LOGO;	// SQUARE-ENIXロゴ表示
			PaletteChange(38, 60);	// SQUARE-ENIXロゴパレットへ30フレームで变更
			proc = LOGO_PROC_E_SE_FADEIN;
			break;

		case LOGO_PROC_E_SE_FADEIN:	// ENIXフェードイン中
			mouse.flag = 1;		//显示鼠标
			if (palChageStatus == FALSE) {
				t = GetTickCount();
				proc = LOGO_PROC_E_SE_KEEPING;
			}
			break;

		case LOGO_PROC_E_SE_KEEPING:	// ENIX 表示中
			mouse.flag = 1;		//显示鼠标
			// 240フレーム后、30フレームで黒パレットへ
			if (t + 2000 < GetTickCount()) {
				PaletteChange(19, 30);	// 黒パレットに变更
				proc = LOGO_PROC_E_SE_FADEOUT;
			}
			break;

		case LOGO_PROC_E_SE_FADEOUT:		// フェードアウト中
			PaletteChange(19, 0);	// 黒パレットに变更
			int BmpNo;
			// ここでツェナワークスの画像があったらツェナを表示に行く。
			// なかったらドワンゴロゴへ行く
			realGetNo(TW_CG_ZENER_LOGO, (U4*)&BmpNo);
			mouse.flag = 0;		//取消显示鼠标
			if (BmpNo >= 1) {
				proc = LOGO_PROC_E_ZENER_SET;
			}
			else {
				proc = LOGO_PROC_E_DWANGO_SET;
			}
		break;

			//--------------  ツェナロゴ -------------------
		case LOGO_PROC_E_ZENER_SET:		// ツェナ表示
			LogoGraphic = TW_CG_ZENER_LOGO;// ツェナロゴ表示
			PaletteChange(26, 60);	// ツェナロゴパレットへ60フレームで变更
			proc = LOGO_PROC_E_ZENER_FADEIN;
			break;

		case LOGO_PROC_E_ZENER_FADEIN:	// ツェナフェードイン
			if (palChageStatus == FALSE) {
				t = GetTickCount();
				proc = LOGO_PROC_E_ZENER_KEEPING;
			}
			break;

		case LOGO_PROC_E_ZENER_KEEPING:	// ツェナ表示中
			// 120フレーム后、60フレームで黒パレットへ
			if (t + 2000 < GetTickCount()) {
				// 黒パレットに变更
				PaletteChange(19, 60);
				proc = LOGO_PROC_E_ZENER_FADEOUT;
			}
			break;

		case LOGO_PROC_E_ZENER_FADEOUT:	// ツェナフェードアウト
			if (palChageStatus == FALSE) {
				proc = LOGO_PROC_E_DWANGO_SET;
			}
			break;

			//--------------  ドワンゴロゴ -------------------
		case LOGO_PROC_E_DWANGO_SET:	// ドワンゴ表示
			LogoGraphic = CG_DWANGO_LOGO;// ドワンゴ表示
			// DWANGOロゴパレットへ30フレームで变更
			PaletteChange(22, 30);
			proc = LOGO_PROC_E_DWANGO_FADEIN;
			break;

		case LOGO_PROC_E_DWANGO_FADEIN:	// ドワンゴフェードイン
			if (palChageStatus == FALSE) {
				t = GetTickCount();
				proc = LOGO_PROC_E_DWANGO_KEEPING;
			}
			break;

		case LOGO_PROC_E_DWANGO_KEEPING:	// ドワンゴ表示中
			// 120フレーム后、30フレームで黒パレットへ
			if (t + 2000 < GetTickCount()) {
				PaletteChange(19, 30);	// 黒パレットに变更
				proc = LOGO_PROC_E_DWANGO_FADEOUT;
			}
			break;

		case LOGO_PROC_E_DWANGO_FADEOUT:
			if (palChageStatus == FALSE) {	// パレットチェンジ末
				proc = LOGO_PROC_E_PONSBIC_SET;
			}
			break;


			//----------- 最后＆スキップしたとき -------------
		case LOGO_PROC_E_SKIP_1:
			LogoGraphic = 0;
			PaletteChange(0, 1);
			ClearBackSurface();	// バックサーフェスを黒でクリアー
			proc = LOGO_PROC_E_SKIP_2;

			// インストールしてあるバージョン调べる。
			if (GetInstallVersion() < 0) {
				// 途中のバージョンがインストールされてないよ。
				proc = LOGO_PROC_INSTALL_ERR;
				break;
			}
			break;

		case LOGO_PROC_E_SKIP_2:
			LogoGraphic = 0;	// 画像表示消す
			PaletteChange(19, 1);
			ClearBackSurface();	// バックサーフェスを黒でクリアー
			proc = LOGO_PROC_E_SKIP_3;
			break;

		case LOGO_PROC_E_SKIP_3:
			LogoGraphic = 0;	// 画像表示消す
			// 处理終了
			loopFlag = 0;

			break;

		case LOGO_PROC_INSTALL_ERR:	// インストール变な场合
			proc = LOGO_PROC_INSTALL_ERR_WAIT;
			initCommonMsgWin();
			break;

		case LOGO_PROC_INSTALL_ERR_WAIT:	// インストール变な场合
			if (commonMsgWin("游戏没有被没有正常安装正常") == 1) {                          //MLHIDE
				loopFlag = 0;	// 終了。
				return FALSE;
			}
			//			RunAction();			// アクション走らせる
			break;
			}

		RunAction();			// アクション走らせる

		//-------  ロゴ表示 ------------
		if (LogoGraphic != 0) { // 画像表示あるときだけ表示
#ifdef PUK2
			switch (proc) {
			case LOGO_PROC_E_PONSBIC_SET:		// ポンスビック表示
			case LOGO_PROC_E_PONSBIC_FADEIN:	// ポンスビックフェードイン
			case LOGO_PROC_E_PONSBIC_KEEPING:	// ポンスビック表示中
			case LOGO_PROC_E_PONSBIC_FADEOUT:
				// ポンスビックロゴの表示では、色要素を指定
				StockDispBuffer(320, 240, DISP_PRIO_TOP, LogoGraphic, 0, &bm);
				break;
			default:
				StockDispBuffer(320, 240, DISP_PRIO_TOP, LogoGraphic, 0);
			}
#else
			StockDispBuffer(320, 240, DISP_PRIO_TOP, LogoGraphic, 0);
#endif
		}

		//		StockTaskDispBuffer();	// タスク表示データをバッファに溜める

		MouseProc();	// マウス处理

		SortDispBuffer(); 	// 表示バッファソート

		HitMouseCursor();	// マウスカーソルのあたり判定

		PaletteProc();		// パレット处理

		KeyBoardClear();	// キーボード情报クリア

#if FRAME_SKIP		// 描画时间が残っていないときは描画しない ( すぐ处理に返回 )
#ifdef PUK2_FPS
#ifdef _DEBUG
		if (!(VK[VK_SHIFT] & KEY_ON)) {
#endif
			if (NowDrawTime > GetTickCount()) {
				// 时间のずれを调整
				while (NowTime >= GetTickCount()) {
					Sleep(1);
				}

				// システムタイムプラス
				NowTime += SystemTime;
				// 描画してない回数初期化
				NoDrawCnt = 1;
#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
#endif
				// 描画しなくてもいい时なら
				continue;
			}
#ifdef _DEBUG
		}
#endif
#endif
		if (NowTime + ProcTime < GetTickCount())
		{
			// 描画しなくてもいい时なら
			if (NoDrawCnt < NO_DRAW_MAX_CNT)
			{
				// システムタイムプラス（ 缲越 )
				NowTime += SystemTime;
				// 描画してない回数カウント
				NoDrawCnt++;
#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
#endif
				continue;
			}
		}
#endif
#ifdef PUK2
		if (!getUsable3D())
			PalState.count++;
#endif

#ifdef PUK2_NOSHOW
		if (WinActive == WA_ACTIVE_JUST_AFTER) WinActive = WA_ACTIVATE;
#endif
#ifndef PUK2
		// バックサーフェスを黒でクリアー
		ClearBackSurface();
#endif
		PutBmp();	// ＢＭＰをバックサーフェスにセット

		// パレットチェンジフラグＯＮの时
		if (PalChangeFlag == TRUE)
		{
			// パレットの中身を设定
			lpDraw->lpPALETTE->SetEntries(0, 0, 256, Palette);
			PalChangeFlag = FALSE; // フラグ初期化
		}

		Flip();	// 垂直同期线时の切り替え处理

#if FRAME_SKIP
		// 时间のずれを调整
		while (NowTime >= GetTickCount())
		{
			// ウィンドウモードなら
//			if( WindowMode ) Sleep( 1 );
			Sleep(1);
		}
#endif

		// システムタイムプラス
		NowTime += SystemTime;
		// 描画してない回数初期化
		NoDrawCnt = 1;
#ifdef _DEBUG
		// 一秒间に描画した枚数をカウント
		DrawFrameCnt++;
#if defined(_DEBUG) && defined(PUK2_FPS)
		ProcCnt++;
#endif
#endif

		// サーフェスを使用した日付を更新
		SurfaceDate++;
	}

	return TRUE;
	}

#else
// ロゴ处理 ------------------------------------------------
BOOL logoProc( void )
{
	int LogoGraphic = 0;		// 最初はエニックスロゴ
	int proc = 0;
	int loopFlag = 1;
	unsigned int t;
#ifdef PUK2
	static BLT_MEMBER bm={0};
#endif

	// ここでもインストールバージョンを决定しておく
	giInstallVersion = GetInstallVersion( );

	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif

	// ループ
	while( loopFlag )
	{
		// 信息ループコール
		if( SystemTask() == FALSE )
		{
			EndGame();
			return FALSE;
		}

		// 表示カウンター初期化（コマ落ちの时も初期化するためここでする）
		DispBuffer.DispCnt = 0;
		// フォントカウンター初期化
		FontCnt = 0;
#ifdef PUK2
		// フォントプライオリティ制御バッファの初期化
		FontPrioInit();
#endif

		// マウスまたはキーが押されたらスキップ
		if( (mouse.onceState & MOUSE_LEFT_CRICK)
		 || (mouse.onceState & MOUSE_RIGHT_CRICK)
		 || (VK[ VK_RETURN ] & KEY_ON)
		 || (VK[ VK_SPACE ] & KEY_ON)
		 || (VK[ VK_ESCAPE ] & KEY_ON) )
		{
			if( proc < LOGO_PROC_E_SKIP_1 ){
				proc = LOGO_PROC_E_SKIP_1;
			}
#ifdef PUK2
			if( LOGO_PROC_E_PONSBIC_SET <= proc && proc <= LOGO_PROC_E_PONSBIC_FADEOUT ){
				proc = LOGO_PROC_E_SKIP_1;
			}
#endif
		}

		switch( proc )
        {

		case LOGO_PROC_E_FIRST:
#ifdef PUK2
			// バックサーフェースを黒で初期化
			ClearBackSurface();
			// 绘图设定の初期化
			bm.u = bm.v = bm.w = bm.h = 0;
			bm.rgba.r = 0;
			bm.rgba.g = 0;
			bm.rgba.b = 0;
			bm.rgba.a = 255;
			bm.BltVer = BLTVER_NOMAL;
			bm.bltf = 0;
			bm.PalNo = 0;
#endif
			// 黒パレットに变更
			PaletteChange( 19, 1 );
			proc = LOGO_PROC_E_ENIX_SET;	// ENIXロゴセットへ
			DeathAllAction( );
			break;

		//--------------  ＳＱＵＡＲＥ－ＥＮＩＸロゴ -------------------
		case LOGO_PROC_E_ENIX_SET:
			LogoGraphic = CG_ENIX_LOGO;	// SQUARE-ENIXロゴ表示
			PaletteChange( 31, 30 );	// SQUARE-ENIXロゴパレットへ30フレームで变更
			proc = LOGO_PROC_E_ENIX_FADEIN;
			break;

		case LOGO_PROC_E_ENIX_FADEIN:	// ENIXフェードイン中
			if( palChageStatus == FALSE ){
				t = GetTickCount();
				proc = LOGO_PROC_E_ENIX_KEEPING;
			}
			break;

		case LOGO_PROC_E_ENIX_KEEPING:	// ENIX 表示中
			// 240フレーム后、30フレームで黒パレットへ
			if( t + 4000 < GetTickCount() ){
				PaletteChange( 19, 30 );	// 黒パレットに变更
				proc = LOGO_PROC_E_ENIX_FADEOUT;
			}
			break;

		case LOGO_PROC_E_ENIX_FADEOUT:		// フェードアウト中
			if( palChageStatus == FALSE ){	// フェードアウト終わったら
				int BmpNo;
#ifdef PUK2
				// ここでポンスビックの画像があったらポンスビックロゴを表示に行く。
				// なかったらツェナワークスロゴへ行く
				realGetNo( CG_PONSBIC_LOGO, (U4 *)&BmpNo );
				if( BmpNo >= 1 ){
					proc = LOGO_PROC_E_PONSBIC_SET;
				}else{
					// ここでツェナワークスの画像があったらツェナを表示に行く。
					// なかったらドワンゴロゴへ行く
					realGetNo( CG_ZENER_LOGO, (U4 *)&BmpNo );
					if( BmpNo >= 1 ){
						proc = LOGO_PROC_E_ZENER_SET;
					}else{
						proc = LOGO_PROC_E_DWANGO_SET;
					}
				}
#else
				// ここでツェナワークスの画像があったらツェナを表示に行く。
				// なかったらドワンゴロゴへ行く
				realGetNo( CG_ZENER_LOGO, (U4 *)&BmpNo );
				if( BmpNo >= 1 ){
					proc = LOGO_PROC_E_ZENER_SET;
				}else{
					proc = LOGO_PROC_E_DWANGO_SET;
				}
#endif
			}
			break;

#ifdef PUK2
		//--------------  ポンスビックロゴ -------------------
		case LOGO_PROC_E_PONSBIC_SET:	// ポンスビック表示
			LogoGraphic = CG_PONSBIC_LOGO;
			if ( getUsable3D() ){
				// 昼パレットに变更
				PaletteChange( 0, 0 );
				// 表示に使う色の度合いを０に设定する
				bm.rgba.r = bm.rgba.g = bm.rgba.b = 0;
			}
			// 昼パレットへ30フレームで变更
			else
			{
				PaletteChange( 0, 30 );
			}
			proc = LOGO_PROC_E_PONSBIC_FADEIN;
			break;

		case LOGO_PROC_E_PONSBIC_FADEIN:	// ポンスビックフェードイン
			if ( getUsable3D() ){
				if (bm.rgba.r + 4 > 255){
					bm.rgba.r = 255;
					bm.rgba.g = 255;
					bm.rgba.b = 255;
					t = GetTickCount();
					proc = LOGO_PROC_E_PONSBIC_KEEPING;
				}else{
					bm.rgba.r += 4;
					bm.rgba.g += 4;
					bm.rgba.b += 4;
				}
			}
			else
			{
				if( palChageStatus == FALSE ){
					t = GetTickCount();
					proc = LOGO_PROC_E_PONSBIC_KEEPING;
				}
			}
			break;

		case LOGO_PROC_E_PONSBIC_KEEPING:	// ポンスビック表示中
			// 120フレーム后、３Ｄ机能を使用しないなら、30フレームで黒パレットへ
			if( t + 2000 < GetTickCount() ){
				if ( !getUsable3D() )
				{
					PaletteChange( 19, 30 );	// 黒パレットに变更
				}
				proc = LOGO_PROC_E_PONSBIC_FADEOUT;
			}
			break;

		case LOGO_PROC_E_PONSBIC_FADEOUT:
			if ( getUsable3D() ){
				if (bm.rgba.r!=0){
					if (bm.rgba.r - 4 < 0){ bm.rgba.r = bm.rgba.g = bm.rgba.b = 0; }
					else{ bm.rgba.r -= 4,	bm.rgba.g -= 4,	bm.rgba.b -= 4; }
				}else{
					int BmpNo;
					if ( getUsable3D() ) PaletteChange( 19, 0 );	// 黒パレットに变更
					// ここでツェナワークスの画像があったらツェナを表示に行く。
					// なかったらドワンゴロゴへ行く
					realGetNo( CG_ZENER_LOGO, (U4 *)&BmpNo );
					if( BmpNo >= 1 ){
						proc = LOGO_PROC_E_ZENER_SET;
					}else{
						proc = LOGO_PROC_E_DWANGO_SET;
					}
				}
			}
			else
			{
				if( palChageStatus == FALSE ){	// パレットチェンジ末
					int BmpNo;
					// ここでツェナワークスの画像があったらツェナを表示に行く。
					// なかったらドワンゴロゴへ行く
					realGetNo( CG_ZENER_LOGO, (U4 *)&BmpNo );
					if( BmpNo >= 1 ){
						proc = LOGO_PROC_E_ZENER_SET;
					}else{
						proc = LOGO_PROC_E_DWANGO_SET;
					}
				}
			}
			break;
#endif

		//--------------  ツェナロゴ -------------------
		case LOGO_PROC_E_ZENER_SET:		// ツェナ表示
			LogoGraphic = CG_ZENER_LOGO;// ツェナロゴ表示
			PaletteChange( 23, 60 );	// ツェナロゴパレットへ60フレームで变更
			proc = LOGO_PROC_E_ZENER_FADEIN;
			break;

		case LOGO_PROC_E_ZENER_FADEIN:	// ツェナフェードイン
			if( palChageStatus == FALSE ){
				t = GetTickCount();
				proc = LOGO_PROC_E_ZENER_KEEPING;
			}
			break;

		case LOGO_PROC_E_ZENER_KEEPING:	// ツェナ表示中
			// 120フレーム后、60フレームで黒パレットへ
			if( t + 2000 < GetTickCount() ){
				// 黒パレットに变更
				PaletteChange( 19, 60 );
				proc = LOGO_PROC_E_ZENER_FADEOUT;
			}
			break;

		case LOGO_PROC_E_ZENER_FADEOUT:	// ツェナフェードアウト
			if( palChageStatus == FALSE ){
				proc = LOGO_PROC_E_DWANGO_SET;
			}
			break;

		//--------------  ドワンゴロゴ -------------------
		case LOGO_PROC_E_DWANGO_SET:	// ドワンゴ表示
			LogoGraphic = CG_DWANGO_LOGO;// ドワンゴ表示
			// DWANGOロゴパレットへ30フレームで变更
			PaletteChange( 22, 30 );
			proc = LOGO_PROC_E_DWANGO_FADEIN;
			break;

		case LOGO_PROC_E_DWANGO_FADEIN:	// ドワンゴフェードイン
			if( palChageStatus == FALSE ){
				t = GetTickCount();
				proc = LOGO_PROC_E_DWANGO_KEEPING;
			}
			break;

		case LOGO_PROC_E_DWANGO_KEEPING:	// ドワンゴ表示中
			// 120フレーム后、30フレームで黒パレットへ
			if( t + 2000 < GetTickCount() ){
				PaletteChange( 19, 30 );	// 黒パレットに变更
				proc = LOGO_PROC_E_DWANGO_FADEOUT;
			}
			break;

		case LOGO_PROC_E_DWANGO_FADEOUT:
			if( palChageStatus == FALSE ){	// パレットチェンジ末
				proc = LOGO_PROC_E_SKIP_1;
			}
			break;


		//----------- 最后＆スキップしたとき -------------
		case LOGO_PROC_E_SKIP_1:
			LogoGraphic = 0;
			PaletteChange( 0, 1 );
			ClearBackSurface();	// バックサーフェスを黒でクリアー
			proc = LOGO_PROC_E_SKIP_2;

			// インストールしてあるバージョン调べる。
			if( GetInstallVersion( ) < 0 ){
				// 途中のバージョンがインストールされてないよ。
				proc = LOGO_PROC_INSTALL_ERR;
				break;
			}
			break;

		case LOGO_PROC_E_SKIP_2:
			LogoGraphic = 0;	// 画像表示消す
			PaletteChange( 19, 1 );
			ClearBackSurface();	// バックサーフェスを黒でクリアー
			proc = LOGO_PROC_E_SKIP_3;
			break;

		case LOGO_PROC_E_SKIP_3:
			LogoGraphic = 0;	// 画像表示消す
			// 处理終了
			loopFlag = 0;

			break;

		case LOGO_PROC_INSTALL_ERR:	// インストール变な场合
			proc = LOGO_PROC_INSTALL_ERR_WAIT;
			initCommonMsgWin( );
			break;

		case LOGO_PROC_INSTALL_ERR_WAIT:	// インストール变な场合
			if( commonMsgWin( "游戏没有被没有正常安装正常" ) == 1 ){                        //MLHIDE
				loopFlag = 0;	// 終了。
				return FALSE;
			}
//			RunAction();			// アクション走らせる
			break;
		}

		RunAction();			// アクション走らせる

		//-------  ロゴ表示 ------------
		if( LogoGraphic != 0 ){ // 画像表示あるときだけ表示
#ifdef PUK2
			switch(proc){
			case LOGO_PROC_E_PONSBIC_SET:		// ポンスビック表示
			case LOGO_PROC_E_PONSBIC_FADEIN:	// ポンスビックフェードイン
			case LOGO_PROC_E_PONSBIC_KEEPING:	// ポンスビック表示中
			case LOGO_PROC_E_PONSBIC_FADEOUT:
				// ポンスビックロゴの表示では、色要素を指定
				StockDispBuffer( 320, 240, DISP_PRIO_TOP, LogoGraphic, 0, &bm );
				break;
			default:
				StockDispBuffer( 320, 240, DISP_PRIO_TOP, LogoGraphic, 0 );
			}
#else
			StockDispBuffer( 320, 240, DISP_PRIO_TOP, LogoGraphic, 0 );
#endif
		}

//		StockTaskDispBuffer();	// タスク表示データをバッファに溜める

		MouseProc();	// マウス处理

		SortDispBuffer(); 	// 表示バッファソート

		HitMouseCursor();	// マウスカーソルのあたり判定

		PaletteProc();		// パレット处理

		KeyBoardClear();	// キーボード情报クリア

#if FRAME_SKIP		// 描画时间が残っていないときは描画しない ( すぐ处理に返回 )
	#ifdef PUK2_FPS
		#ifdef _DEBUG
		if( !(VK[ VK_SHIFT ]&KEY_ON) ){
		#endif
			if( NowDrawTime > GetTickCount() ){
				// 时间のずれを调整
				while( NowTime >= GetTickCount() ){
					Sleep( 1 );
				}

				// システムタイムプラス
				NowTime += SystemTime;
				// 描画してない回数初期化
				NoDrawCnt = 1;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				// 描画しなくてもいい时なら
				continue;
			}
		#ifdef _DEBUG
		}
		#endif
	#endif
		if( NowTime + ProcTime < GetTickCount() )
		{
			// 描画しなくてもいい时なら
			if( NoDrawCnt < NO_DRAW_MAX_CNT )
			{
				// システムタイムプラス（ 缲越 )
				NowTime += SystemTime;
				// 描画してない回数カウント
				NoDrawCnt++;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				continue;
			}
		}
#endif
#ifdef PUK2
		if ( !getUsable3D() )
			PalState.count++;
#endif

#ifdef PUK2_NOSHOW
		if ( WinActive == WA_ACTIVE_JUST_AFTER ) WinActive = WA_ACTIVATE;
#endif
#ifndef PUK2
		// バックサーフェスを黒でクリアー
		ClearBackSurface();
#endif
		PutBmp();	// ＢＭＰをバックサーフェスにセット

		// パレットチェンジフラグＯＮの时
		if( PalChangeFlag == TRUE )
		{
			// パレットの中身を设定
			lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			PalChangeFlag = FALSE; // フラグ初期化
		}

		Flip();	// 垂直同期线时の切り替え处理

#if FRAME_SKIP
		// 时间のずれを调整
		while( NowTime >= GetTickCount() )
		{
			// ウィンドウモードなら
//			if( WindowMode ) Sleep( 1 );
			Sleep( 1 );
		}
#endif

		// システムタイムプラス
		NowTime += SystemTime;
		// 描画してない回数初期化
		NoDrawCnt = 1;
#ifdef _DEBUG
		// 一秒间に描画した枚数をカウント
    	DrawFrameCnt++;
	#if defined(_DEBUG) && defined(PUK2_FPS)
		ProcCnt++;
	#endif
#endif

		// サーフェスを使用した日付を更新
		SurfaceDate++;
	}

	return TRUE;
}
#endif

int aiLogoNoTbl[] = {
	CG_NOW_LOADINGV2,	// V2
	CG_NOW_LOADINGEX,	// EX
	CG_NOW_LOADING2		// 通常
};

// NOW LOADING の表示 --------------------------------------
BOOL fadeInNowLoading( void )
{
//	int BmpNo;
	int proc = 0;
	int loopFlag = 1, LoadPaletNo = 0;
	giInstallVersion = GetInstallVersion( );

	//nowLoadintGraNo = Rnd( CG_NOW_LOADING1, CG_NOW_LOADING3 );

	// 初期画像
	nowLoadintGraNo = CG_NOW_LOADING2;

#ifdef VERSION_TW
#ifdef PUK3_UPGRADE
	if (giInstallVersion == 4) {
		// PUK3bin使用者はCG_NOW_LOADINGPUK2がPUK3用に书き换わる
		nowLoadintGraNo = TW_CG_NOW_LOADING;
		LoadPaletNo = 26;
	}
#endif
#ifdef PUK2
	if (giInstallVersion == 3) {
		nowLoadintGraNo = TW_CG_NOW_LOADING;
		LoadPaletNo = 26;
	}
	else
#endif
		if (giInstallVersion == 2) {
			nowLoadintGraNo = CG_NOW_LOADINGV2;
			LoadPaletNo = 29;
		}
		else
			if (giInstallVersion == 1) {
				nowLoadintGraNo = CG_NOW_LOADINGV2;
				LoadPaletNo = 29;
			}
#else
#ifdef PUK3_UPGRADE
	if( giInstallVersion == 4 ){
		// PUK3bin使用者はCG_NOW_LOADINGPUK2がPUK3用に书き换わる
		nowLoadintGraNo = CG_NOW_LOADINGPUK2;
		LoadPaletNo = 26;
	}
#endif
#ifdef PUK2
	if( giInstallVersion == 3 ){
		nowLoadintGraNo = CG_NOW_LOADINGPUK2;
		LoadPaletNo = 26;
	}else
#endif
	if( giInstallVersion == 2 ){
		nowLoadintGraNo = CG_NOW_LOADINGV2;
		LoadPaletNo = 26;
	}else
	if( giInstallVersion == 1 ){
		nowLoadintGraNo = CG_NOW_LOADINGV2;
		LoadPaletNo = 26;
	}
#endif

/*
	// Ｖ２のローディング画面ある？
	for( i = 0; i < sizeof( aiLogoNoTbl ) / sizeof( aiLogoNoTbl[0] ); i ++ ){
		// その画像ある？
		realGetNo( aiLogoNoTbl[i], (U4 *)&BmpNo );
		if( BmpNo > 0 ){	// あるみたいだ。
			nowLoadintGraNo = aiLogoNoTbl[i];	// その画像で行こう
			break;
		}
	}
*/
	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif

	// ループ
	while( loopFlag )
	{
		// 信息ループコール
		if( SystemTask() == FALSE )
		{
			EndGame();
			return FALSE;
		}

		if( proc == 0 )
		{
			// パレット变更
			PaletteChange( LoadPaletNo, 30 );
			StockDispBuffer( 320, 240, DISP_PRIO_TOP, nowLoadintGraNo, 0 );
			proc = 1;
		}
		else
		if( proc == 1 )
		{
			StockDispBuffer( 320, 240, DISP_PRIO_TOP, nowLoadintGraNo, 0 );
			if( palChageStatus == FALSE )
			{
				loopFlag = 0;
			}
		}

		SortDispBuffer(); 	// 表示バッファソート

		PaletteProc();		// パレット处理

		KeyBoardClear();	// キーボード情报クリア

#if FRAME_SKIP		// 描画时间が残っていないときは描画しない ( すぐ处理に返回 )
	#ifdef PUK2_FPS
		#ifdef _DEBUG
		if( !(VK[ VK_SHIFT ]&KEY_ON) ){
		#endif
			if( NowDrawTime > GetTickCount() ){
				// 时间のずれを调整
				while( NowTime >= GetTickCount() ){
					Sleep( 1 );
				}

				// システムタイムプラス
				NowTime += SystemTime;
				// 描画してない回数初期化
				NoDrawCnt = 1;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				// 描画しなくてもいい时なら
				continue;
			}
		#ifdef _DEBUG
		}
		#endif
	#endif
		if( NowTime + ProcTime < GetTickCount() )
		{
			// 描画しなくてもいい时なら
			if( NoDrawCnt < NO_DRAW_MAX_CNT )
			{
				// システムタイムプラス（ 缲越 )
				NowTime += SystemTime;
				// 描画してない回数カウント
				NoDrawCnt++;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				continue;
			}
		}
#endif

#ifdef PUK2
		if ( !getUsable3D() )
			PalState.count++;
#endif
		// バックサーフェスを黒でクリアー
		ClearBackSurface();
		PutBmp();	// ＢＭＰをバックサーフェスにセット

		// パレットチェンジフラグＯＮの时
		if( PalChangeFlag == TRUE )
		{
			// パレットの中身を设定
			lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			PalChangeFlag = FALSE; // フラグ初期化
		}

		Flip();	// 垂直同期线时の切り替え处理

#if FRAME_SKIP
		// 时间のずれを调整
		while( NowTime >= GetTickCount() )
		{
			// ウィンドウモードなら
//			if( WindowMode ) Sleep( 1 );
			Sleep( 1 );
		}
#endif

		// システムタイムプラス
		NowTime += SystemTime;
		// 描画してない回数初期化
		NoDrawCnt = 1;
#ifdef _DEBUG
		// 一秒间に描画した枚数をカウント
    	DrawFrameCnt++;
	#if defined(_DEBUG) && defined(PUK2_FPS)
		ProcCnt++;
	#endif
#endif

		// サーフェスを使用した日付を更新
		SurfaceDate++;
	}

#ifdef PUK2
	StockDispBuffer( 320, 240, DISP_PRIO_TOP, nowLoadintGraNo, 0 );

	// バックサーフェスを黒でクリアー
	ClearBackSurface();
	PutBmp();	// ＢＭＰをバックサーフェスにセット

	Flip();	// 垂直同期线时の切り替え处理
#endif
	return TRUE;
}


// NOW LOADING のフェードアウト
BOOL fadeOutNowLoading( void )
{
	int proc = 0;
	int loopFlag = 1;

	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif

	// ループ
	while( loopFlag )
	{
		// 信息ループコール
		if( SystemTask() == FALSE )
		{
			EndGame();
			return FALSE;
		}

		if( proc == 0 )
		{
			StockDispBuffer( 320, 240, DISP_PRIO_TOP, nowLoadintGraNo, 0 );
			// 黒パレットに变更
			PaletteChange( 19, 30 );
			proc = 1;
		}
		else
		if( proc == 1 )
		{
			StockDispBuffer( 320, 240, DISP_PRIO_TOP, nowLoadintGraNo, 0 );
			if( palChageStatus == FALSE )
			{
				// 終了
				loopFlag = 0;
			}
		}

		SortDispBuffer(); 	// 表示バッファソート

		PaletteProc();		// パレット处理

		KeyBoardClear();	// キーボード情报クリア

#if FRAME_SKIP		// 描画时间が残っていないときは描画しない ( すぐ处理に返回 )
	#ifdef PUK2_FPS
		#ifdef _DEBUG
		if( !(VK[ VK_SHIFT ]&KEY_ON) ){
		#endif
			if( NowDrawTime > GetTickCount() ){
				// 时间のずれを调整
				while( NowTime >= GetTickCount() ){
					Sleep( 1 );
				}

				// システムタイムプラス
				NowTime += SystemTime;
				// 描画してない回数初期化
				NoDrawCnt = 1;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				// 描画しなくてもいい时なら
				continue;
			}
		#ifdef _DEBUG
		}
		#endif
	#endif
		if( NowTime + ProcTime < GetTickCount() )
		{
			// 描画しなくてもいい时なら
			if( NoDrawCnt < NO_DRAW_MAX_CNT )
			{
				// システムタイムプラス（ 缲越 )
				NowTime += SystemTime;
				// 描画してない回数カウント
				NoDrawCnt++;
		#if defined(_DEBUG) && defined(PUK2_FPS)
				ProcCnt++;
		#endif
				continue;
			}
		}
#endif

#ifdef PUK2
		if ( !getUsable3D() )
			PalState.count++;
#endif
		// バックサーフェスを黒でクリアー
		ClearBackSurface();
		PutBmp();	// ＢＭＰをバックサーフェスにセット

		// パレットチェンジ?榨楗埃希韦问?
		if( PalChangeFlag == TRUE )
		{
			// パレットの中身を设定
			lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			PalChangeFlag = FALSE; // フラグ初期化
		}

		Flip();	// 垂直同期线时の切り替え处理

#if FRAME_SKIP
		// 时间のずれを调整
		while( NowTime >= GetTickCount() )
		{
			// ウィンドウモードなら
//			if( WindowMode ) Sleep( 1 );
			Sleep( 1 );
		}
#endif

		// システムタイムプラス
		NowTime += SystemTime;
		// 描画してない回数初期化
		NoDrawCnt = 1;
#ifdef _DEBUG
		// 耽昼嶷纸肝方
    	DrawFrameCnt++;
	#if defined(_DEBUG) && defined(PUK2_FPS)
		ProcCnt++;
	#endif
#endif

		// サーフェスを使用した日付を更新
		SurfaceDate++;
	}

	return TRUE;
}


#ifdef PUK2_DEVICE_CHANGE

void MenuWindowCommonDraw( int GraNo, short x, short y, short w, short h, int DispPrio, unsigned long frame_rgba, unsigned long back_rgba,
	char sizeX, char sizeY );
// デバイス变更前に行うべき处理をこの关数で行う
void DeviceChangeProc()
{
	// 演出画像の作成
	// バッファ初期化
	DispBuffer.DispCnt = 0;
	FontCnt = 0;
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();

	StockFontBuffer( 220, 223, FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, "绘画功能变更", 0 );	//暂时的 //MLHIDE
	// 絵を描画する
	MenuWindowCommonDraw( GID_InfoWindow, 196, 180,  256, 96, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff, 74, 54 );

	SortDispBuffer(); 	// 表示バッファソート
	// バックサーフェスを黒でクリアー
	ClearBackSurface();

	// 色变え制限を一时的にＯＦＦ
	LimiteLoadBmpFlag = FALSE;
	// ＢＭＰをバックサーフェスにセット
	PutBmp();
	// 色变え制限をＯＮ
	LimiteLoadBmpFlag = TRUE;

	// バッファ初期化
	DispBuffer.DispCnt = 0;
	FontCnt = 0;
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();

	Flip();	// 垂直同期线时の切り替え处理
}

#endif