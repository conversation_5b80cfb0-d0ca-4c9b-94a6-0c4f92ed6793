﻿/* output by lsgen.perl 0.41 ( 1998 May)
 * made Sun Mar 14 20:54:34 2004
 * user <PERSON><PERSON>
 * host Saito2-Server
 * file /home/<USER>/tmp/nr2/bin/output/nrproto_cli.h
 * util output/nrproto_util.c , output/nrproto_util.h
 * src  /home/<USER>/tmp/nr2/bin/../doc/nrproto.html
 */
#ifndef _NRPROTOCLI_H_
#define _NRPROTOCLI_H_
#include "version.h"
#include "nrproto_util.h"

#ifdef MAXLSRPCARGS
#if ( MAXLSRPCARGS <= ( 17 + 1 ) )
#undef MAXLSRPCARGS
#define MAXLSRPCARGS ( 17 + 1 )
#endif
#else
#define MAXLSRPCARGS ( 17 + 1 )
#endif

#ifdef VERSION_TW
 //封包版本
enum {
	PACKET_VER_KR,
    PACKET_VER_TW5,
    PACKET_VER_TW7,
    PACKET_VER_ALLNUM
};

//客户端发送给gmsv的封包命令
enum {
    SEND_W,
    SEND_w,
    SEND_EV,
    SEND_M,
    SEND_EN,
    SEND_DU,
    SEND_EO,
    SEND_BU,
    SEND_JB,
    SEND_LB,
    SEND_B,
    SEND_ID,
    SEND_IDF,
    SEND_PI,
    SEND_DI,
    SEND_DG,
    SEND_DP,
    SEND_MI,
    SEND_IR,
    SEND_MSG,
    SEND_ALI,
    SEND_ALN,
    SEND_PMSG,
    SEND_DAB,
    SEND_AAB,
    SEND_GI,
    SEND_GT,
    SEND_GMR,
    SEND_BGT,
    SEND_AGM,
    SEND_LG,
    SEND_RGM,
    SEND_GML,
    SEND_PGML,
    SEND_GD,
    SEND_PRV,
    SEND_PRS,
    SEND_PRD,
    SEND_PRE,
    SEND_PRM,
    SEND_PRO,
    SEND_L,
    SEND_TK,
    SEND_FS,
    SEND_HL,
    SEND_PR,
    SEND_KS,
    SEND_MP,
    SEND_GFL,
    SEND_GPD,
    SEND_GFLI,
    SEND_GPDI,
    SEND_IH,
    SEND_AC,
    SEND_ACS,
    SEND_MU,
    SEND_TU,
    SEND_TRPL,
    SEND_TRS,
    SEND_TROP,
    SEND_TRCL,
    SEND_TROC,
    SEND_PS,
    SEND_ST,
    SEND_DT,
    SEND_FT,
    SEND_LVUP,
    SEND_PLVUP,
    SEND_SKSW,
    SEND_PSSW,
    SEND_POS,
    SEND_KN,
    SEND_WN,
    SEND_SP,
    SEND_ClientLogin,
    SEND_CreateNewChar,
    SEND_CharDelete,
    SEND_CharLogin,
    SEND_CharList,
    SEND_CharLogout,
    SEND_ProcGet,
    SEND_PlayerNumGet,
    SEND_Echo,
    SEND_Shutdown,
    SEND_FC,
    SEND_CH,
    SEND_CharLoginGate,
    SEND_PVUP,
    SEND_StallStart,            //台服5.5 摆摊相关的4个命令
    SEND_StallEnd,
    SEND_StallBrowse,
    SEND_StallBuy,
    SEND_ProduceTime,           //台服5.5 比韩服多的两个命令
    SEND_CheckCommand,
    SEND_rh,                    //台服7.1cg比5.5多的一个命令,双页银行
    SEND_ALLNUM
};

//客户端从gmsv接受的封包命令
enum {
    RECV_XYD,
    RECV_MC,
    RECV_M,
    RECV_EV,
    RECV_EP,
    RECV_EN,
    RECV_RS,
    RECV_RD,
    RECV_B,
    RECV_IA,
    RECV_I,
    RECV_LI,
    RECV_SI,
    RECV_IR,
    RECV_BT,
    RECV_MSG,
    RECV_AL,
    RECV_ALI,
    RECV_ALN,
    RECV_ALO,
    RECV_PME,
    RECV_AB,
    RECV_ABG,
    RECV_ABI,
    RECV_GC,
    RECV_GI,
    RECV_GT,
    RECV_GM,
    RECV_GMI,
    RECV_RGM,
    RECV_GML,
    RECV_GD,
    RECV_PRV,
    RECV_PRL,
    RECV_PRA,
    RECV_PRD,
    RECV_PRE,
    RECV_PRM,
    RECV_PRW,
    RECV_PRAD,
    RECV_TK,
    RECV_STK,
    RECV_CP,
    RECV_CP2,
    RECV_KP,
    RECV_KP2,
    RECV_PP,
    RECV_C,
    RECV_CN,
    RECV_TITLE,
    RECV_CA,
    RECV_CD,
    RECV_CJ,
    RECV_CS,
    RECV_CT,
    RECV_PT,
    RECV_S,
    RECV_FS,
    RECV_HL,
    RECV_PR,
    RECV_GFL,
    RECV_GPD,
    RECV_GFLI,
    RECV_GPDI,
    RECV_TU,
    RECV_TRPL,
    RECV_TRS,
    RECV_TROP,
    RECV_TRLI,
    RECV_TRLG,
    RECV_TRLP,
    RECV_TRLPS,
    RECV_TRCL,
    RECV_TROC,
    RECV_PS,
    RECV_LVUP,
    RECV_PLVUP,
    RECV_POS,
    RECV_WN,
    RECV_EF,
    RECV_SE,
    RECV_BGMW,
    RECV_PC,
    RECV_SH,
    RECV_PLAYSE,
    RECV_ES,
    RECV_MN,
    RECV_CC,
    RECV_ClientLogin,
    RECV_CreateNewChar,
    RECV_CharDelete,
    RECV_CharLogin,
    RECV_CharList,
    RECV_CharLogout,
    RECV_ProcGet,
    RECV_PlayerNumGet,
    RECV_Echo,
    RECV_IP,
    RECV_PV,
    RECV_PVUP,
    RECV_MAC,
    RECV_Expire,
    RECV_StallStart,                //摆摊相关的4个命令
    RECV_StallEnd,
    RECV_StallBrowse,
    RECV_StallBuy,
    RECV_iKe,                       //7.1cg比5.5多的2个命令
    RECV_kiG,
    RECV_ALLNUM
};
#endif

#ifdef PUK3_CONNDOWNWALK
void nrproto_W_send( int fd,int x,int y,char* direction,int warpid ) ; /* ../doc/nrproto.html */
#else
void nrproto_W_send( int fd,int x,int y,char* direction ) ; /* ../doc/nrproto.html */
#endif
void nrproto_w_send( int fd,int x,int y,char* direction ) ; /* ../doc/nrproto.html */
void nrproto_XYD_recv( int fd,int x,int y,int dir ) ; /* ../doc/nrproto.html */
void nrproto_EV_send( int fd,int event,int seqno,int x,int y,int dir ) ; /* ../doc/nrproto.html */
void nrproto_MC_recv( int fd,int id,int fl,int x1,int y1,int x2,int y2,int tilesum,int objsum,int eventsum ) ; /* ../doc/nrproto.html */
void nrproto_M_send( int fd,int id,int fl,int x1,int y1,int x2,int y2 ) ; /* ../doc/nrproto.html */
void nrproto_M_recv( int fd,int id,int fl,int x1,int y1,int x2,int y2,char* data ) ; /* ../doc/nrproto.html */
void nrproto_EV_recv( int fd,int seqno,int result ) ; /* ../doc/nrproto.html */
void nrproto_EN_send( int fd,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_DU_send( int fd,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_EP_recv( int fd,int min,int max ) ; /* ../doc/nrproto.html */
void nrproto_EN_recv( int fd,int result,int field ) ; /* ../doc/nrproto.html */
void nrproto_EO_send( int fd,int dummy ) ; /* ../doc/nrproto.html */
void nrproto_BU_send( int fd,int dummy ) ; /* ../doc/nrproto.html */
void nrproto_JB_send( int fd,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_LB_send( int fd,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_RS_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_RD_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_B_send( int fd,char* command ) ; /* ../doc/nrproto.html */
void nrproto_B_recv( int fd,char* command ) ; /* ../doc/nrproto.html */
void nrproto_ID_send( int fd,int x,int y,int haveitemindex,int toindex ) ; /* ../doc/nrproto.html */
void nrproto_IDF_send( int fd,int x,int y,int haveitemindex,int toindex ) ; /* ../doc/nrproto.html */
void nrproto_PI_send( int fd,int x,int y,int dir ) ; /* ../doc/nrproto.html */
void nrproto_DI_send( int fd,int x,int y,int itemindex ) ; /* ../doc/nrproto.html */
void nrproto_DG_send( int fd,int x,int y,int amount ) ; /* ../doc/nrproto.html */
void nrproto_DP_send( int fd,int x,int y,int petindex ) ; /* ../doc/nrproto.html */
void nrproto_IA_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_I_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_LI_recv( int fd,int index,int servertime,int servernumber,int adjusttime ) ; /* ../doc/nrproto.html */
void nrproto_MI_send( int fd,int fromindex,int toindex,int num ) ; /* ../doc/nrproto.html */
void nrproto_SI_recv( int fd,int fromindex,int toindex ) ; /* ../doc/nrproto.html */
void nrproto_IR_send( int fd,int haveskillindex ) ; /* ../doc/nrproto.html */
void nrproto_IR_recv( int fd,int haveskillindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_BT_recv( int fd,int time,int flag ) ; /* ../doc/nrproto.html */
void nrproto_MSG_send( int fd,int index,char* message,int color ) ; /* ../doc/nrproto.html */
void nrproto_MSG_recv( int fd,int aindex,char* text,int color ) ; /* ../doc/nrproto.html */
void nrproto_AL_recv( int fd,int albumversion,char* data ) ; /* ../doc/nrproto.html */
void nrproto_ALI_send( int fd,int albumid ) ; /* ../doc/nrproto.html */
void nrproto_ALI_recv( int fd,int albumid,int imgno,int catchflg,int rare,int type,int vital,int str,int tgh,int quick,int magic,int earth,int water,int fire,int wind,int slot,char* comment,char* name ) ; /* ../doc/nrproto.html */
void nrproto_ALN_send( int fd,int albumid ) ; /* ../doc/nrproto.html */
void nrproto_ALN_recv( int fd,int albumid,char* name ) ; /* ../doc/nrproto.html */
void nrproto_ALO_recv( int fd,int albumid ) ; /* ../doc/nrproto.html */
void nrproto_PMSG_send( int fd,int index,int petindex,int itemindex,char* message,int color ) ; /* ../doc/nrproto.html */
void nrproto_PME_recv( int fd,int objindex,int graphicsno,int x,int y,int dir,int flg,int no,char* cdata ) ; /* ../doc/nrproto.html */
void nrproto_AB_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_ABG_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_ABI_recv( int fd,int num,char* data ) ; /* ../doc/nrproto.html */
void nrproto_DAB_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_AAB_send( int fd,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_GC_recv( int fd ) ; /* ../doc/nrproto.html */
void nrproto_GI_send( int fd,int index,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GI_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GT_send( int fd,int index,int bitflag,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GT_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GMR_send( int fd,int time ) ; /* ../doc/nrproto.html */
void nrproto_GM_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GMI_recv( int fd,int num,char* data ) ; /* ../doc/nrproto.html */
void nrproto_BGT_send( int fd,int index,int titleID ) ; /* ../doc/nrproto.html */
void nrproto_AGM_send( int fd,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_LG_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_RGM_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_RGM_recv( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_GML_send( int fd,int index,char* message,int color ) ; /* ../doc/nrproto.html */
void nrproto_PGML_send( int fd,int index,int petindex,int itemindex,char* message,int color ) ; /* ../doc/nrproto.html */
void nrproto_GML_recv( int fd,int index,char* text,int color ) ; /* ../doc/nrproto.html */
void nrproto_GD_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_GD_recv( int fd,int index ) ; /* ../doc/nrproto.html */
#ifdef PUK3_PROF
void nrproto_PRV_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PRV_send( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_PRL_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PRA_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PRS_send( int fd,int sid,char* smsg,int bid,char* bmsg,int aid,char* amsg,char* pmsg ) ; /* ../doc/nrproto.html */
void nrproto_PRD_send( int fd,int flg,int index ) ; /* ../doc/nrproto.html */
void nrproto_PRD_recv( int fd,char* name,int lv,char* job,char* title,char* guildname,int graphic,int sid,char* smsg,int bid,char* bmsg,int aid,char* amsg,char* pmsg ) ; /* ../doc/nrproto.html */
void nrproto_PRE_send( int fd,int index,char* name ) ; /* ../doc/nrproto.html */
void nrproto_PRE_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PRM_send( int fd,char* data,int color ) ; /* ../doc/nrproto.html */
void nrproto_PRM_recv( int fd,int index,char* text,int color ) ; /* ../doc/nrproto.html */
void nrproto_PRO_send( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_PRW_recv( int fd,int windowtype,int buttontype,int seqno,int objindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PRAD_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
#endif
void nrproto_L_send( int fd,int dir ) ; /* ../doc/nrproto.html */
void nrproto_TK_send( int fd,int x,int y,char* message,int color,int area,int fontsize ) ; /* ../doc/nrproto.html */
void nrproto_TK_recv( int fd,int index,char* message,int color,int fontsize ) ; /* ../doc/nrproto.html */
void nrproto_STK_recv( int fd,char* message ) ; /* ../doc/nrproto.html */
void nrproto_CP_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CP2_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_KP_recv( int fd,int number,char* data ) ; /* ../doc/nrproto.html */
void nrproto_KP2_recv( int fd,int number,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PP_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_C_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CN_recv( int fd,int number,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TITLE_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CA_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CD_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CJ_recv( int fd,int jobassort,char* jobname ) ; /* ../doc/nrproto.html */
void nrproto_CS_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CT_recv( int fd,int skillindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PT_recv( int fd,int petindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_S_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_FS_send( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_FS_recv( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_HL_send( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_HL_recv( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_PR_send( int fd,int x,int y,int request ) ; /* ../doc/nrproto.html */
void nrproto_PR_recv( int fd,int request,int result ) ; /* ../doc/nrproto.html */
void nrproto_KS_send( int fd,int pet1,int pet2,int pet3,int pet4,int pet5 ) ; /* ../doc/nrproto.html */
void nrproto_MP_send( int fd,int fromindex,int toindex ) ; /* ../doc/nrproto.html */
void nrproto_GFL_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_GFL_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GPD_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_GPD_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GFLI_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_GFLI_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_GPDI_send( int fd,int index ) ; /* ../doc/nrproto.html */
void nrproto_GPDI_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_IH_send( int fd,int index,int hankoindex ) ; /* ../doc/nrproto.html */
void nrproto_AC_send( int fd,int x,int y,int actionno ) ; /* ../doc/nrproto.html */
void nrproto_ACS_send( int fd,int x,int y,int skillno ) ; /* ../doc/nrproto.html */
void nrproto_MU_send( int fd,int x,int y,int array,int toindex ) ; /* ../doc/nrproto.html */
void nrproto_TU_send( int fd,int haveskillindex,int havetechindex,int toindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TU_recv( int fd,int result,int type,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TRPL_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_TRPL_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TRS_send( int fd,int playernum ) ; /* ../doc/nrproto.html */
void nrproto_TRS_recv( int fd,char* name,int level ) ; /* ../doc/nrproto.html */
void nrproto_TROP_send( int fd,char* items,char* pets,int gold ) ; /* ../doc/nrproto.html */
void nrproto_TROP_recv( int fd ) ; /* ../doc/nrproto.html */
void nrproto_TRLI_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TRLG_recv( int fd,int gold ) ; /* ../doc/nrproto.html */
void nrproto_TRLP_recv( int fd,int petplace,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TRLPS_recv( int fd,int petplace,char* data ) ; /* ../doc/nrproto.html */
void nrproto_TRCL_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_TRCL_recv( int fd ) ; /* ../doc/nrproto.html */
void nrproto_TROC_recv( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_TROC_send( int fd,int flg ) ; /* ../doc/nrproto.html */
void nrproto_PS_send( int fd,int havepetindex,int havepetskill,int toindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PS_recv( int fd,int result,int havepetindex,int havepetskill,int toindex ) ; /* ../doc/nrproto.html */
void nrproto_ST_send( int fd,int titleindex ) ; /* ../doc/nrproto.html */
void nrproto_DT_send( int fd,int titleindex ) ; /* ../doc/nrproto.html */
void nrproto_FT_send( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_LVUP_recv( int fd,int point ) ; /* ../doc/nrproto.html */
void nrproto_LVUP_send( int fd,int param ) ; /* ../doc/nrproto.html */
void nrproto_PLVUP_recv( int fd,int pethave,int point ) ; /* ../doc/nrproto.html */
void nrproto_PLVUP_send( int fd,int pethave,int param ) ; /* ../doc/nrproto.html */
void nrproto_SKSW_send( int fd,int srcindex,int dstindex ) ; /* ../doc/nrproto.html */
void nrproto_PSSW_send( int fd,int havepetindex,int srcindex,int dstindex ) ; /* ../doc/nrproto.html */
void nrproto_POS_recv( int fd,int pos ) ; /* ../doc/nrproto.html */
void nrproto_POS_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_KN_send( int fd,int havepetindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_WN_recv( int fd,int windowtype,int buttontype,int seqno,int objindex,char* data ) ; /* ../doc/nrproto.html */
void nrproto_WN_send( int fd,int x,int y,int seqno,int objindex,int select,char* data ) ; /* ../doc/nrproto.html */
void nrproto_EF_recv( int fd,int effect,int level,char* option ) ; /* ../doc/nrproto.html */
void nrproto_SE_recv( int fd,int senumber ) ; /* ../doc/nrproto.html */
void nrproto_BGMW_recv( int fd,int sw ) ; /* ../doc/nrproto.html */
void nrproto_PC_recv( int fd,int palnumber,int frame_cnt ) ; /* ../doc/nrproto.html */
void nrproto_SH_recv( int fd,int action,int mapid,int floor,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_PLAYSE_recv( int fd,int seno,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_ES_recv( int fd,int seno,int x,int y ) ; /* ../doc/nrproto.html */
void nrproto_MN_recv( int fd,char* mapname ) ; /* ../doc/nrproto.html */
#ifdef PUK3_CONNDOWNWALK
void nrproto_CC_recv( int fd,int mapid,int floor,int maxx,int maxy,int x,int y,int cfgid,int seqno,int var,int bgm,int cut,int warpid ) ; /* ../doc/nrproto.html */
#else
void nrproto_CC_recv( int fd,int mapid,int floor,int maxx,int maxy,int x,int y,int cfgid,int seqno,int var,int bgm,int cut ) ; /* ../doc/nrproto.html */
#endif
void nrproto_SP_send( int fd,int x,int y,int dir ) ; /* ../doc/nrproto.html */
void nrproto_ClientLogin_send( int fd,char* acountid,char* passwd,char* cdkey ) ; /* ../doc/nrproto.html */
void nrproto_ClientLogin_recv( int fd,int result,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CreateNewChar_send( int fd,int dataplacenum,char* charname,int imgno,int faceimgno,int vital,int str,int tgh,int quick,int magic,int earth,int water,int fire,int wind ) ; /* ../doc/nrproto.html */
void nrproto_CreateNewChar_recv( int fd,char* result,int registnumber,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CharDelete_send( int fd,int registnumber ) ; /* ../doc/nrproto.html */
void nrproto_CharDelete_recv( int fd,char* result,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CharLogin_send( int fd,int registnumber,int renewal,int face ) ; /* ../doc/nrproto.html */
void nrproto_CharLogin_recv( int fd,char* result,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CharList_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_CharList_recv( int fd,int result,char* data ) ; /* ../doc/nrproto.html */
void nrproto_CharLogout_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_CharLogout_recv( int fd,char* result,char* data ) ; /* ../doc/nrproto.html */
void nrproto_ProcGet_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_ProcGet_recv( int fd,char* data ) ; /* ../doc/nrproto.html */
void nrproto_PlayerNumGet_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_PlayerNumGet_recv( int fd,int logincount,int player ) ; /* ../doc/nrproto.html */
void nrproto_Echo_send( int fd,char* test ) ; /* ../doc/nrproto.html */
void nrproto_Echo_recv( int fd,char* test ) ; /* ../doc/nrproto.html */
void nrproto_Shutdown_send( int fd,char* passwd,int min ) ; /* ../doc/nrproto.html */
void nrproto_FC_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_CH_send( int fd,int num ) ; /* ../doc/nrproto.html */
void nrproto_IP_recv( int fd,char* ip ) ; /* ../doc/nrproto.html */
void nrproto_CharLoginGate_send( int fd ) ; /* ../doc/nrproto.html */
void nrproto_PV_recv( int fd,int ver ) ; /* ../doc/nrproto.html */
void nrproto_PVUP_send( int fd,int kind,char* productkey ) ; /* ../doc/nrproto.html */
void nrproto_PVUP_recv( int fd,int result ) ; /* ../doc/nrproto.html */
void nrproto_MAC_recv( int fd,int listmax,int count ) ; /* ../doc/nrproto.html */
#ifdef VERSION_TW
//台服发送生产采集技能所需的时间
void nrproto_ProduceTime_send(int fd, int time);
#endif
int nrproto_InitClient( int(*)(int,char*,int)  , int bufsiz , int fd);
void nrproto_SetClientLogFiles( char *read , char *write);
#ifdef PUK2
void nrproto_SetLogCallback( void (*)(int,char *) );
#else
void nrproto_SetLogCallback( void (*)(char *) );
#endif
void nrproto_CleanupClient(void);
int nrproto_ClientDispatchMessage(int fd ,char*line);
#endif
/* end of the generated client header code */
