﻿#include "../systeminc/version.h"
#include "../systeminc/system.h"
#include "../systeminc/main.h"
#include "../systeminc/filetbl.h"


#ifdef MULTI_GRABIN
// 名称保存场所の实体
char graphicBinName_body[BINMODE_MAX][256];
char graphicInfoBinName_body[BINMODE_MAX][256];
char animeBinName_body[BINMODE_MAX][256];
char animeInfoBinName_body[BINMODE_MAX][256];
#ifdef PUK3_RIDEBIN
	char coordinateBinName_body[BINMODE_MAX][256];
	char coordinateInfoBinName_body[BINMODE_MAX][256];
	#ifdef _DEBUG
		// 座标binを作成した时のログファイル名
		char coordinateLogName_body[BINMODE_MAX][256];
	#endif
#endif

// 名称保存场所のポインタ（使うときはこちらを使う)
char* graphicBinName[BINMODE_MAX];
char* graphicInfoBinName[BINMODE_MAX];
char* animeBinName[BINMODE_MAX];
char* animeInfoBinName[BINMODE_MAX];
#ifdef PUK3_RIDEBIN
	char* coordinateBinName[BINMODE_MAX];
	char* coordinateInfoBinName[BINMODE_MAX];
	#ifdef _DEBUG
		// 座标binを作成した时のログファイル名
		char* coordinateLogName[BINMODE_MAX];
	#endif
#endif

// ここではデフォルトの名称が书いてあるが、
// 引数によってこの中身が书き换えられる
char _graphicBinName_plus[BINMODE_MAX][128]     = {
	//"Graphic.bin",		// Graphic.binの名称
	"Graphic_32.bin",                                                    //MLHIDE
	//"GraphicEx.bin",	// Ex
	"Graphic_Joy_20.bin",                                                //MLHIDE
	//"GraphicV2.bin"	// V2
	"GraphicV2_6.bin"                                                    //MLHIDE
#ifdef PUK2
	,
	//"GraphicV3.bin",	// V3
	"GraphicV3_22.bin",                                                  //MLHIDE
	//"PUK2\\Graphic_PUK2.bin",	// PUK2
	"PUK2\\Graphic_PUK2_2.bin",                                          //MLHIDE
#endif
#ifdef PUK3_BIN
	//"PUK3\\Graphic_PUK3.bin",	// PUK3
	"PUK3\\Graphic_PUK3_1.bin",                                          //MLHIDE
#endif
#ifdef CHINA_BIN
	"KW\\Graphic_KW.bin"	// KW                                           //MLHIDE
#endif
#ifdef VERSION_TW
	"Graphic_Joy_125.bin",                                               //MLHIDE
	"Graphic_Joy_EX_84.bin",                                             //MLHIDE
	"Graphic_Joy_CH1.bin"                                                //MLHIDE
#ifdef BIN_EXPAND
	,
	"cgmsv\\Graphic_UI_1.bin",                                           //MLHIDE
	"cgmsv\\Graphic_SA_1.bin",                                           //MLHIDE
	"cgmsv\\Graphic_EXP1_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP2_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP3_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP4_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP5_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP6_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP7_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP8_1.bin",                                         //MLHIDE
	"cgmsv\\Graphic_EXP9_1.bin"                                          //MLHIDE
#endif

#endif
};

char _graphicInfoBinName_plus[BINMODE_MAX][128] = {
	//"GraphicInfo.bin",		// GraphicInfo.binの名称
	"GraphicInfo_32.bin",                                                //MLHIDE
	//"GraphicInfoEx.bin",	// Ex
	"GraphicInfo_Joy_20.bin",                                            //MLHIDE
	//"GraphicInfoV2.bin"	// V2
	"GraphicInfoV2_6.bin"                                                //MLHIDE
#ifdef PUK2
	,
	//"GraphicInfoV3.bin",	// V3
	"GraphicInfoV3_22.bin",                                              //MLHIDE
	//"PUK2\\GraphicInfo_PUK2.bin",	// PUK2
	"PUK2\\GraphicInfo_PUK2_2.bin",                                      //MLHIDE
#endif
#ifdef PUK3_BIN
	//"PUK3\\GraphicInfo_PUK3.bin",	// PUK3
	"PUK3\\GraphicInfo_PUK3_1.bin",                                      //MLHIDE
#endif
#ifdef CHINA_BIN
	"KW\\GraphicInfo_KW.bin"// KW                                        //MLHIDE
#endif
#ifdef VERSION_TW
	"GraphicInfo_Joy_125.bin",                                           //MLHIDE
	"GraphicInfo_Joy_EX_84.bin",                                         //MLHIDE
	"GraphicInfo_Joy_CH1.bin"                                            //MLHIDE
#ifdef BIN_EXPAND
	,
	"cgmsv\\GraphicInfo_UI_1.bin",                                       //MLHIDE
	"cgmsv\\GraphicInfo_SA_1.bin",                                       //MLHIDE
	"cgmsv\\GraphicInfo_EXP1_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP2_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP3_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP4_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP5_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP6_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP7_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP8_1.bin",                                     //MLHIDE
	"cgmsv\\GraphicInfo_EXP9_1.bin"                                      //MLHIDE
#endif

#endif
};

char _animeBinName_plus[BINMODE_MAX][128] = {
	//"Anime.bin",		// Anime.binの名称
	"Anime_7.bin",                                                       //MLHIDE
	//"AnimeEx.bin",		// Ex
	"Anime_Joy_12.bin",                                                  //MLHIDE
	//"AnimeV2.bin"		// V2
	"AnimeV2_1.Bin"                                                      //MLHIDE
#ifdef PUK2
	,
	//"AnimeV3.bin",		// V3
	"AnimeV3_11.bin",                                                    //MLHIDE
	//"PUK2\\Anime_PUK2.bin",		// PUK2
	"PUK2\\Anime_PUK2_3.bin",                                            //MLHIDE
#endif
#ifdef PUK3_BIN
	//"PUK3\\Anime_PUK3.bin",		// PUK3
	"PUK3\\Anime_PUK3_2.bin",                                            //MLHIDE
#endif
#ifdef VERSION_TW
	"Anime_Joy_91.bin",                                                  //MLHIDE
	"Anime_Joy_EX_81.bin",                                               //MLHIDE
	"Anime_Joy_CH1.bin"                                                  //MLHIDE
#ifdef BIN_EXPAND
	,
	"cgmsv\\Anime_UI_1.bin",                                             //MLHIDE
	"cgmsv\\Anime_SA_1.bin",                                             //MLHIDE
	"cgmsv\\Anime_EXP1_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP2_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP3_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP4_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP5_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP6_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP7_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP8_1.bin",                                           //MLHIDE
	"cgmsv\\Anime_EXP9_1.bin"                                            //MLHIDE
#endif

#endif
};

char _animeInfoBinName_plus[BINMODE_MAX][128] = {
	//"AnimeInfo.bin",	// AnimenInfo.binの名称
	"AnimeInfo_7.bin",                                                   //MLHIDE
	//"AnimeInfoEx.bin",	// Ex
	"AnimeInfo_Joy_12.bin",                                              //MLHIDE
	//"AnimeInfoV2.bin"	// V2
	"AnimeInfoV2_1.Bin"                                                  //MLHIDE
#ifdef PUK2
	,
	//"AnimeInfoV3.bin",	// V3
	"AnimeInfoV3_11.bin",                                                //MLHIDE
	//"PUK2\\AnimeInfo_PUK2.bin",	// PUK2
	"PUK2\\AnimeInfo_PUK2_3.bin"                                         //MLHIDE
#endif
#ifdef PUK3_BIN
	//"PUK3\\AnimeInfo_PUK3.bin",	// PUK3
	"PUK3\\AnimeInfo_PUK3_2.bin",                                        //MLHIDE
#endif
#ifdef VERSION_TW
	"AnimeInfo_Joy_91.bin",                                              //MLHIDE
	"AnimeInfo_Joy_EX_81.bin",                                           //MLHIDE
	"AnimeInfo_Joy_CH1.Bin"                                              //MLHIDE
#ifdef BIN_EXPAND
	,
	"cgmsv\\AnimeInfo_UI_1.bin",                                         //MLHIDE
	"cgmsv\\AnimeInfo_SA_1.bin",                                         //MLHIDE
	"cgmsv\\AnimeInfo_EXP1_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP2_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP3_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP4_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP5_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP6_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP7_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP8_1.bin",                                       //MLHIDE
	"cgmsv\\AnimeInfo_EXP9_1.bin"                                        //MLHIDE
#endif

#endif
};
#ifdef PUK3_RIDEBIN
char _coordinateBinName_plus[BINMODE_MAX][128] = {
	// *数字でその数值のインデックスのbinを使用
	"*3",					// Coordinate.binの名称                                       //MLHIDE
	"*3",					// Ex                                                      //MLHIDE
	"*3",					// V2                                                      //MLHIDE
	"Crd.bin",				// V3                                                  //MLHIDE
	"*3",					// PUK2                                                    //MLHIDE
	"*3",					// PUK3                                                    //MLHIDE
	"*3",					// KW                                                      //MLHIDE
};

char _coordinateInfoBinName_plus[BINMODE_MAX][128] = {
	// *数字でその数值のインデックスのbinを使用
	"*3",					// CoordinateInfo.binの名称                                   //MLHIDE
	"*3",					// Ex                                                      //MLHIDE
	"*3",					// V2                                                      //MLHIDE
	"CrdInfo.bin",			// V3                                               //MLHIDE
	"*3",					// PUK2                                                    //MLHIDE
	"*3",					// PUK3                                                    //MLHIDE
	"*3",					// KW                                                      //MLHIDE
};

	#ifdef _DEBUG
		// 座标binを作成した时のログファイル名
		char coordinateLogName_plus[BINMODE_MAX][128] = {
			"*3",			// Coordinate.binのログファイルの名称                                //MLHIDE
			"*3",			// Ex                                                      //MLHIDE
			"*3",			// V2                                                      //MLHIDE
			"CrdLog.txt",	// V3                                                //MLHIDE
			"*3",			// PUK2                                                    //MLHIDE
			"*3",			// PUK3                                                    //MLHIDE
			"*3",			// KW                                                      //MLHIDE
		};
	#endif
#endif

#else

char graphicBinName[256];
char graphicInfoBinName[256];
char animeBinName[256];
char animeInfoBinName[256];
char _graphicBinName[128]     = "Graphic.bin";		// Graphic.binの名称     //MLHIDE
char _graphicInfoBinName[128] = "GraphicInfo.bin";	// GraphicInfo.binの名称 //MLHIDE
char _animeBinName[128]       = "Anime.bin";		// Anime.binの名称         //MLHIDE
char _animeInfoBinName[128]   = "AnimeInfo.bin";	// AnimenInfo.binの名称 //MLHIDE
#endif




// Graphic.BIN FILE
CHECKBINFILE aCheck_GraphicBin[] = {
	{ 622109548 }, 	// Graphic_1.bin
	{ 622394939 },	// Graphic_2.bin
	{ 622404189 },	// Graphic_3.bin
	{ 622484902 },	// Graphic_4.bin
	{ 622499951 },	// Graphic_5.bin
	{ 622509852 },	// Graphic_6.bin
	{ 622655658 },	// Graphic_7.bin
	{ 622683784 },	// Graphic_8.bin
	{ 622742293 },	// Graphic_9.bin
	{ 622764471 },	// Graphic_10.bin
	{ 622852245 },	// Graphic_11.bin
	{ 623311028 },	// Graphic_12.bin
	{ 623412571 },	// Graphic_13.bin
	{ 624632706 },	// Graphic_14.bin
	{ 625590208 },	// Graphic_15.bin
	{ 626022437 },	// Graphic_16.bin
	{ 626044341 },	// Graphic_17.bin
	{ 626046666 },	// Graphic_18.bin
	{ 626101490 },	// Graphic_19.bin
	{ 626149910 },	// Graphic_20.bin
	{ 626154055 },	// Graphic_21.bin
	{ 626508866 },	// Graphic_22.bin
	{ 626832083 },	// Graphic_23.bin
	{ 626834804 },	// Graphic_24.bin
	{ 626923107 },	// Graphic_25.bin
	{ 627059061 },	// Graphic_26.bin
	{ 627308906 },	// Graphic_27.bin
	{ 627365622 },	// Graphic_28.bin
	{ 627370798 },	// Graphic_29.bin
	{ 627375736 },	// Graphic_30.bin
	{ 627381837 },	// Graphic_31.bin
	{ 627391746 },	// Graphic_32.bin
			{ 0 }			// 最后の印
};
// GraphicInfo.BIN FILE
CHECKBINFILE aCheck_GraphicInfoBin[] = {
	{ 10019840 }, 	// GraphicInfo_1.bin
	{ 10021160 },	// GraphicInfo_2.bin
	{ 10021240 },	// GraphicInfo_3.bin
	{ 10021760 },	// GraphicInfo_4.bin
	{ 10022360 },	// GraphicInfo_5.bin
	{ 10022600 },	// GraphicInfo_6.bin
	{ 10024440 },	// GraphicInfo_7.bin
	{ 10024840 },	// GraphicInfo_8.bin
	{ 10025320 },	// GraphicInfo_9.bin
	{ 10025960 },	// GraphicInfo_10.bin
	{ 10026840 },	// GraphicInfo_11.bin
	{ 10027000 },	// GraphicInfo_12.bin
	{ 10027240 },	// GraphicInfo_13.bin
	{ 10028080 },	// GraphicInfo_14.bin
	{ 10028720 },	// GraphicInfo_15.bin
	{ 10029400 },	// GraphicInfo_16.bin
	{ 10030360 },	// GraphicInfo_17.bin
	{ 10030480 },	// GraphicInfo_18.bin
	{ 10030520 },	// GraphicInfo_19.bin
	{ 10030640 },	// GraphicInfo_20.bin
	{ 10030840 },	// GraphicInfo_21.bin
	{ 10037920 },	// GraphicInfo_22.bin
	{ 10045000 },	// GraphicInfo_23.bin
	{ 10045080 },	// GraphicInfo_24.bin
	{ 10047960 },	// GraphicInfo_25.bin
	{ 10053480 },	// GraphicInfo_26.bin
	{ 10054800 },	// GraphicInfo_27.bin
	{ 10055240 },	// GraphicInfo_28.bin
	{ 10055480 },	// GraphicInfo_29.bin
	{ 10055640 },	// GraphicInfo_30.bin
	{ 10055920 },	// GraphicInfo_31.bin
	{ 10055960 },	// GraphicInfo_32.bin
		{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_AnimeBin[] = {
	{ 27828994 }, // Anime_1.bin
	{ 27840226 }, // Anime_2.bin
	{ 27840268 }, // Anime_3.bin
	{ 27849372 }, // Anime_4.bin
	{ 27858476 }, // Anime_5.bin
	{ 27862300 }, // Anime_6.bin
	{ 27863072 }, // Anime_7.bin
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_AnimeInfoBin[] = {
	{ 38040 }, // AnimeInfo_1.bin
	{ 38100 }, // AnimeInfo_2.bin
	{ 38112 }, // AnimeInfo_3.bin
	{ 38124 }, // AnimeInfo_4.bin
	{ 38136 }, // AnimeInfo_5.bin
	{ 38148 }, // AnimeInfo_6.bin
	{ 38220 }, // AnimeInfo_7.bin
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_BattleBin[] = {
	{ 16284 }, // 1
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_BattleTxt[] = {
	{ 550 },	// 1
	{ 0 }		// 最后の印
};
CHECKBINFILE aCheck_SoundBin[] = {
	{ 8660332 }, // 1
	{ 0 }		// 最后の印
};
CHECKBINFILE aCheck_SoundAddrTxt[] = {
	{ 4649 }, // 1
	{ 0 }		// 最后の印
};



//-------- Ex Version -------------
CHECKBINFILE aCheck_Ex_GraphicBin[] = {
/*	{ 939949236 }, // GraphicEx_1.bin
	{ 939952972 }, // GraphicEx_2.bin
*/	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_Ex_GraphicInfoBin[] = {
	{ 13740240 }, // GraphicInfoEx_1.bin
	{ 13740320 }, // GraphicInfoEx_2.bin
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_Ex_AnimeBin[] = {
	{ 10089274 }, // AnimeEx_1.bin
	{ 10136634 }, // AnimeEx_2.bin
	{ 10274874 }, // AnimeEx_3.bin
	{ 10318010 }, // AnimeEx_4.bin
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_Ex_AnimeInfoBin[] = {
	{ 9924 }, // AnimeExInfo_1.bin
	{ 9972 }, // AnimeExInfo_2.bin
	{ 10116 }, // AnimeExInfo_3.bin
	{ 10164 }, // AnimeExInfo_4.bin
	{ 0 }	// 最后の印
};

//-------- Seccond Version -------------
CHECKBINFILE aCheck_Ver2_GraphicBin[] = {
	{ 940055765 }, // GraphicV2_1.bin
	{ 940232478 }, // GraphicV2_2.bin
	{ 940254766 }, // GraphicV2_3.bin
	{ 940277362 }, // GraphicV2_4.bin
	{ 940287444 }, // GraphicV2_5.bin
	{ 940296094 }, // GraphicV2_6.bin
	
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_Ver2_GraphicInfoBin[] = {
	{ 13740360 }, // GraphicInfoV2_1.bin
	{ 13740520 }, // GraphicInfoV2_2.bin
	{ 13740680 }, // GraphicInfoV2_3.bin
	{ 13741080 }, // GraphicInfoV2_4.bin
	{ 13741160 }, // GraphicInfoV2_5.bin
	{ 13741200 }, // GraphicInfoV2_6.bin
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_Ver2_AnimeBin[] = {
	{ 10086394 }, // AnimeV2_1.bin
	{ 0 }	// 最后の印
};
CHECKBINFILE aCheck_Ver2_AnimeInfoBin[] = {
	{ 9924 }, // AnimeInfoV2_1.bin
	{ 0 }	// 最后の印
};

#ifdef PUK2
//-------- V3 Version -------------

// GraphicV3.bin
CHECKBINFILE aCheck_Ver3_GraphicBin[] = {
	{ 23569578 },	// GraphicV3_1.bin
	{ 23579224 },	// GraphicV3_2.bin
	{ 23604578 },	// GraphicV3_3.bin
	{ 23695419 },	// GraphicV3_4.bin
	{ 23947739 },	// GraphicV3_5.bin
	{ 24338128 },	// GraphicV3_6.bin
	{ 24975573 },	// GraphicV3_7.bin
	{ 25003621 },	// GraphicV3_8.bin
	{ 25123986 },	// GraphicV3_9.bin
	{ 25149585 },	// GraphicV3_10.bin
	{ 31358086 },	// GraphicV3_11.bin
	{ 39425031 },	// GraphicV3_12.bin
	{ 47964559 },	// GraphicV3_13.bin
	{ 63485044 },	// GraphicV3_14.bin
	{ 67207850 },	// GraphicV3_15.bin
	{ 67287953 },	// GraphicV3_16.bin
	{ 67412334 },	// GraphicV3_17.bin
	{ 67667459 },	// GraphicV3_18.bin
	{ 67684291 },	// GraphicV3_19.bin
	{ 67798155 },	// GraphicV3_20.bin
	{ 68062022 },	// GraphicV3_21.bin
	{ 68115446 },	// GraphicV3_22.bin
	{ 68123590 },	// GraphicV3_23.bin
	{ 68321498 },	// GraphicV3_24.bin
	{ 68340045 },	// GraphicV3_25.bin
	{ 68385099 },	// GraphicV3_26.bin
	{ 68389066 },	// GraphicV3_27.bin
	{ 68406648 },	// GraphicV3_28.bin
	{ 68426734 },	// GraphicV3_29.bin
	{ 68430710 },	// GraphicV3_30.bin
	{ 73325422 },	// GraphicV3_31.bin
	{ 0 }			// 最后の印
};
// GraphicInfoV3.bin
CHECKBINFILE aCheck_Ver3_GraphicInfoBin[] = {
	{ 169560 },		// GraphicInfoV3_1.bin
	{ 172840 },		// GraphicInfoV3_2.bin
	{ 174880 },		// GraphicInfoV3_3.bin
	{ 175520 },		// GraphicInfoV3_4.bin
	{ 178080 },		// GraphicInfoV3_5.bin
	{ 178240 },		// GraphicInfoV3_6.bin
	{ 178560 },		// GraphicInfoV3_7.bin
	{ 179520 },		// GraphicInfoV3_8.bin
	{ 181080 },		// GraphicInfoV3_9.bin
	{ 181560 },		// GraphicInfoV3_10.bin
	{ 360760 },		// GraphicInfoV3_11.bin
	{ 540360 },		// GraphicInfoV3_12.bin
	{ 725960 },		// GraphicInfoV3_13.bin
	{ 751360 },		// GraphicInfoV3_14.bin
	{ 764600 },		// GraphicInfoV3_15.bin
	{ 765000 },		// GraphicInfoV3_16.bin
	{ 765560 },		// GraphicInfoV3_17.bin
	{ 776160 },		// GraphicInfoV3_18.bin
	{ 776240 },		// GraphicInfoV3_19.bin
	{ 781480 },		// GraphicInfoV3_20.bin
	{ 781560 },		// GraphicInfoV3_21.bin
	{ 781680 },		// GraphicInfoV3_22.bin
	{ 781800 },		// GraphicInfoV3_23.bin
	{ 781840 },		// GraphicInfoV3_24.bin
	{ 781880 },		// GraphicInfoV3_25.bin
	{ 782760 },		// GraphicInfoV3_26.bin
	{ 782800 },		// GraphicInfoV3_27.bin
	{ 782840 },		// GraphicInfoV3_28.bin
	{ 782880 },		// GraphicInfoV3_29.bin
	{ 782960 },		// GraphicInfoV3_30.bin
	{ 801280 },		// GraphicInfoV3_31.bin
	{ 0 }			// 最后の印
};
// AnimeV3.bin
CHECKBINFILE aCheck_Ver3_AnimeBin[] = {
	{ 244100 },		// AnimeV3_1.bin
	{ 247360 },		// AnimeV3_2.bin
	{ 247840 },		// AnimeV3_3.bin
	{ 250070 },		// AnimeV3_4.bin
	{ 417110 },		// AnimeV3_5.bin
	{ 582870 },		// AnimeV3_6.bin
	{ 752390 },		// AnimeV3_7.bin
	{ 795270 },		// AnimeV3_8.bin
	{ 810470 },		// AnimeV3_9.bin
	{ 818150 },		// AnimeV3_10.bin
	{ 823430 },		// AnimeV3_11.bin
	{ 823910 },		// AnimeV3_12.bin
	{ 830070 },		// AnimeV3_13.bin
	{ 0 }			// 最后の印
};
// AnimeInfoV3.bin
CHECKBINFILE aCheck_Ver3_AnimeInfoBin[] = {
	{ 912 },		// AnimeInfoV3_1.bin
	{ 1068 },		// AnimeInfoV3_2.bin
	{ 1080 },		// AnimeInfoV3_3.bin
	{ 1188 },		// AnimeInfoV3_4.bin
	{ 1860 },		// AnimeInfoV3_5.bin
	{ 2532 },		// AnimeInfoV3_6.bin
	{ 3228 },		// AnimeInfoV3_7.bin
	{ 3432 },		// AnimeInfoV3_8.bin
	{ 3804 },		// AnimeInfoV3_9.bin
	{ 3948 },		// AnimeInfoV3_10.bin
	{ 4080 },		// AnimeInfoV3_11.bin
	{ 4092 },		// AnimeInfoV3_12.bin
	{ 4104 },		// AnimeInfoV3_13.bin
	{ 0 }			// 最后の印
};
#ifdef PUK3_RIDEBIN
	// CoordinateV3.bin
	CHECKBINFILE aCheck_Ver3_CrdBin[] = {
		{ 5109272 },	// CoordinateV3_1.bin
		{ 5118688 },	// CoordinateV3_2.bin
		{ 5135328 },	// CoordinateV3_3.bin
		{ 0 }			// 最后の印
	};
	// CoordinateInfoV3.bin
	CHECKBINFILE aCheck_Ver3_CrdInfoBin[] = {
		{ 15141 },		// CoordinateInfoV3_1.bin
		{ 15191 },		// CoordinateInfoV3_2.bin
		{ 15231 },		// CoordinateInfoV3_3.bin
		{ 0 }			// 最后の印
	};
#endif

//-------- PUK2 Version -------------

// Graphic_PUK2.bin
CHECKBINFILE aCheck_PUK2_GraphicBin[] = {
	{ 243365265 },	// Graphic_PUK2_1.bin
	{ 250880199 },	// Graphic_PUK2_2.bin
	{ 0 }			// 最后の印
};
// GraphicInfo_PUK2.bin
CHECKBINFILE aCheck_PUK2_GraphicInfoBin[] = {
	{ 417840 },		// GraphicInfo_PUK2_1.bin
	{ 441320 },		// GraphicInfo_PUK2_2.bin
	{ 0 }			// 最后の印
};
// Anime_PUK2.bin
CHECKBINFILE aCheck_PUK2_AnimeBin[] = {
	{ 1316410 },	// Anime_PUK2_1.bin
	{ 1341130 },	// Anime_PUK2_2.bin
	{ 1471930 },	// Anime_PUK2_3.bin
	{ 0 }			// 最后の印
};
// AnimeInfo_PUK2.bin
CHECKBINFILE aCheck_PUK2_AnimeInfoBin[] = {
	{ 3888 },		// AnimeInfo_PUK2_1.bin
	{ 3936 },		// AnimeInfo_PUK2_2.bin
	{ 4116 },		// AnimeInfo_PUK2_3.bin
	{ 0 }			// 最后の印
};
#endif
#ifdef PUK3_BIN
//-------- PUK3 Version -------------

// Graphic_PUK3.bin
CHECKBINFILE aCheck_PUK3_GraphicBin[] = {
	{ 48504488 },	// Graphic_PUK3_1.bin
	{ 0 }			// 最后の印
};
// GraphicInfo_PUK3.bin
CHECKBINFILE aCheck_PUK3_GraphicInfoBin[] = {
	{ 183680 },		// GraphicInfo_PUK3_1.bin
	{ 0 }			// 最后の印
};
// Anime_PUK3.bin
CHECKBINFILE aCheck_PUK3_AnimeBin[] = {
	{ 675030 },		// Anime_PUK3_1.bin
	{ 710550 },		// Anime_PUK3_2.bin
	{ 0 }			// 最后の印
};
// AnimeInfo_PUK3.bin
CHECKBINFILE aCheck_PUK3_AnimeInfoBin[] = {
	{ 1788 },		// AnimeInfo_PUK3_1.bin
	{ 1860 },		// AnimeInfo_PUK3_2.bin
	{ 0 }			// 最后の印
};
#endif

// チェックするファイルの配列の配列
CHECKBINFILE *CheckBinFileTbl[] = {

	// ------- First Version -------
	aCheck_GraphicBin,		// Graphic.bin
	aCheck_GraphicInfoBin,	// GraphicInfo.Bin
	aCheck_AnimeBin, 		// Anime.Bin
	aCheck_AnimeInfoBin, 	// AnimeInfo.Bin
	aCheck_BattleBin, 		// Battle.Bin
	aCheck_BattleTxt, 		// BattleTxt.Txt
	aCheck_SoundBin, 		// Sound.Bin
	aCheck_SoundAddrTxt, 	// SoundAddr.Txt
#ifdef PUK3_RIDEBIN
	NULL,					// Coordinate.bin
	NULL,					// CoordinateInfo.bin
#endif

	// ------- Ex Version ----------
	aCheck_Ex_GraphicBin,	// Graphic.bin
	aCheck_Ex_GraphicInfoBin,// GraphicInfo.Bin
	aCheck_Ex_AnimeBin, 		// Anime.Bin
	aCheck_Ex_AnimeInfoBin, 	// AnimeInfo.Bin
#ifdef PUK3_RIDEBIN
	NULL,						// Coordinate.bin
	NULL,						// CoordinateInfo.bin
#endif

	// ------- Ver2 Version ----------
	aCheck_Ver2_GraphicBin,	// Graphic.bin
	aCheck_Ver2_GraphicInfoBin,// GraphicInfo.Bin
	aCheck_Ver2_AnimeBin, 		// Anime.Bin
	aCheck_Ver2_AnimeInfoBin, 	// AnimeInfo.Bin
#ifdef PUK3_RIDEBIN
	NULL,						// Coordinate.bin
	NULL,						// CoordinateInfo.bin
#endif

#ifdef PUK2
	//-------- Ver3 Version -------------
	aCheck_Ver3_GraphicBin,		// GraphicV3.bin
	aCheck_Ver3_GraphicInfoBin,	// GraphicInfoV3.bin
	aCheck_Ver3_AnimeBin,		// AnimeV3.bin
	aCheck_Ver3_AnimeInfoBin,	// AnimeInfoV3.bin
#ifdef PUK3_RIDEBIN
	aCheck_Ver3_CrdBin,			// CoordinateV3.bin
	aCheck_Ver3_CrdInfoBin,		// CoordinateInfoV3.bin
#endif

	//-------- PUK2 Version -------------
	aCheck_PUK2_GraphicBin,		// Graphic_PUK2.bin
	aCheck_PUK2_GraphicInfoBin,	// GraphicInfo_PUK2.bin
	aCheck_PUK2_AnimeBin,		// Anime_PUK2.bin
	aCheck_PUK2_AnimeInfoBin,	// AnimeInfo_PUK2.bin
#ifdef PUK3_RIDEBIN
	NULL,						// Coordinate_PUK2.bin
	NULL,						// CoordinateInfo_PUK2.bin
#endif
#endif
#ifdef PUK3_BIN
	//-------- PUK3 Version -------------
	aCheck_PUK3_GraphicBin,		// Graphic_PUK3.bin
	aCheck_PUK3_GraphicInfoBin,	// GraphicInfo_PUK3.bin
	aCheck_PUK3_AnimeBin,		// Anime_PUK3.bin
	aCheck_PUK3_AnimeInfoBin,	// AnimeInfo_PUK3.bin
#ifdef PUK3_RIDEBIN
	NULL,						// Coordinate_PUK3.bin
	NULL,						// CoordinateInfo_PUK3.bin
#endif
#endif

};

//--------------------------------------------------------
// 
//--------------------------------------------------------
void SetReadFileName( void ){
#ifdef MULTI_GRABIN
	// 読み込みファイル名の设定
	{ int i;
		// 扩张したグラビン、アニビン、などをバージョン别に设定
		for( i = 0; i < BINMODE_MAX; i ++ ){
			graphicBinName[i] = graphicBinName_body[i];
			graphicInfoBinName[i] = graphicInfoBinName_body[i];
			animeBinName[i] = animeBinName_body[i];
			animeInfoBinName[i] = animeInfoBinName_body[i];
	#ifdef PUK3_RIDEBIN
			coordinateBinName[i] = coordinateBinName_body[i];
			coordinateInfoBinName[i] = coordinateInfoBinName_body[i];
		#ifdef _DEBUG
			coordinateLogName[i] = coordinateLogName_body[i];
		#endif
	#endif
		}
		// 扩张したグラビン、アニビン、などをバージョン别に设定
		for( i = 0; i < BINMODE_MAX; i ++ ){
			sprintf( graphicBinName[i],     "%s\\%s", binFolder, _graphicBinName_plus[i] ); //MLHIDE
			sprintf( graphicInfoBinName[i], "%s\\%s", binFolder, _graphicInfoBinName_plus[i] ); //MLHIDE
			sprintf( animeBinName[i],       "%s\\%s", binFolder, _animeBinName_plus[i] ); //MLHIDE
			sprintf( animeInfoBinName[i],   "%s\\%s", binFolder, _animeInfoBinName_plus[i] ); //MLHIDE
	#ifdef PUK3_RIDEBIN
			sprintf( coordinateBinName[i],       "%s\\%s", binFolder, _coordinateBinName_plus[i] ); //MLHIDE
			sprintf( coordinateInfoBinName[i],   "%s\\%s", binFolder, _coordinateInfoBinName_plus[i] ); //MLHIDE
		#ifdef _DEBUG
			sprintf( coordinateLogName[i],   "%s\\%s", binFolder, coordinateLogName_plus[i] ); //MLHIDE
		#endif
	#endif
		}
	}
#else
	// 読み込みファイル名の设定
	sprintf( graphicBinName,     "%s\\%s", binFolder, _graphicBinName ); //MLHIDE
	sprintf( graphicInfoBinName, "%s\\%s", binFolder, _graphicInfoBinName ); //MLHIDE
	sprintf( animeBinName,       "%s\\%s", binFolder, _animeBinName );   //MLHIDE
	sprintf( animeInfoBinName,   "%s\\%s", binFolder, _animeInfoBinName ); //MLHIDE
	sprintf( graphicBinName,     "%s\\%s", binFolder, _graphicBinName ); //MLHIDE
	sprintf( graphicInfoBinName, "%s\\%s", binFolder, _graphicInfoBinName ); //MLHIDE
	sprintf( animeBinName,       "%s\\%s", binFolder, _animeBinName );   //MLHIDE
	sprintf( animeInfoBinName,   "%s\\%s", binFolder, _animeInfoBinName ); //MLHIDE
#endif

}



//------------------------------------------------
//  ファイル名からファイルのバージョンを调べるs
//
//  aaaaaa_10.bin とかだったら 10を返す。
//  ファイル名の最后から '_' までを调べてその后ろにある
//  数字を返す。もし无かったら 0 を返す
//------------------------------------------------
int getBinFileVersion( char *pFileName ){
	int iLen, i, iVersion = 0;
	// ファイル名の长さを返す
	iLen = strlen( pFileName );
	// ファイル名の后ろから检索
	for( i = iLen-1; i >= 1; i -- ){
		// '_' がくるまでループ
		if( pFileName[i-1] != '_' )continue;
		break;
	}
	// みつから无かったらバージョン０で返す
	if( i < 0 )return 0;

	// 取得してみる。
	if( sscanf( pFileName+i, "%d", &iVersion ) != 1 ){                   //MLHIDE
		// 取れなかった
		return 0;
	}
	// 取得したバージョンを返す。
	return iVersion;

}



//---------------------------------------------------
// グラフィックファイルのチェックをする。
//---------------------------------------------------
BOOL CheckBinFile( char *pFileName, int iType ){
	int iFinalVersion, iNowVersion, i;
	HANDLE hFile;
	unsigned int uNowSize, uFinalSize;
	CHECKBINFILE *pCheckFile;
	char szBuffer[512];

	// Graphic.bin ファイルをオープン
	if( ( hFile = CreateFile( 
			pFileName, 	// グラフィック Bin ファイル名
			GENERIC_READ, 		// 読み込みオープン
			FILE_SHARE_READ ,	// 他のプロセスが読み込んでいいよ。でも消すな。
			NULL,
			OPEN_EXISTING,	// 无かったら失败する。 
			FILE_ATTRIBUTE_NORMAL | FILE_FLAG_SEQUENTIAL_SCAN,
			NULL 
		) ) == INVALID_HANDLE_VALUE 
	){
		// 読み込み失败
		// チェック出来ない。
		return TRUE;
	}
	// ファイルサイズを取得する。
	uNowSize = GetFileSize( hFile, NULL );

	// ファイルをクローズ
	CloseHandle( hFile );

#ifdef PUK3
	// CheckBinFileTbl[iType]がＮＵＬＬならチェックしない
	if ( !CheckBinFileTbl[iType] ) return TRUE;
#endif
	iFinalVersion = 0;
	// 最終バージョンを调べる。
	pCheckFile = CheckBinFileTbl[iType];
	for( i = 0; ; i ++ ){
		if( pCheckFile[i].uSize == 0 ){
			break;
		}
	}
#ifdef PUK2
	// サイズデータがあるなら
	if ( i > 0 ){
		// 最終バージョン。１から数える。
		iFinalVersion = i ;
		iNowVersion = iFinalVersion;
		// 最終バージョンのファイルのサイズ
		uFinalSize = pCheckFile[iFinalVersion-1].uSize;
	}else{
		// 最終バージョン。１から数える。
		iFinalVersion = i ;
		iNowVersion = iFinalVersion;
		// 最終バージョンのファイルのサイズ
		uFinalSize = 0;
	}
#else
	// 最終バージョン。１から数える。
	iFinalVersion = i ;
	iNowVersion = iFinalVersion;
	// 最終バージョンのファイルのサイズ
	uFinalSize = pCheckFile[iFinalVersion-1].uSize;
#endif


	// ここでチェック
	// ファイル名に数字が入っているとそのバージョンのファイルサイズをチェックする。
	// 入ってない场合は最后のバージョンをチェックする。
	iNowVersion = getBinFileVersion( pFileName );

	if( iNowVersion <= 0 ){
		// ０が返って来たら最終バージョンだとする。
		iNowVersion = iFinalVersion;
	}else
	if( iNowVersion < iFinalVersion ){
		// 最終バージョンが取得できていない。
#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( szBuffer, ERRMSG_78, pFileName );
#else
		sprintf( szBuffer, "%s 不是最近的版本。", pFileName );                      //MLHIDE
#endif
		MessageBox( hWnd, szBuffer, "文件错误", MB_OK | MB_ICONSTOP );          //MLHIDE
		return FALSE;
	}else
	if( iNowVersion > iFinalVersion ){
		// 今のバージョンの方が新しい？
#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( szBuffer, ERRMSG_79, pFileName );
#else
		sprintf( szBuffer, "%s 的版本异常。", pFileName );                        //MLHIDE
#endif
		MessageBox( hWnd, szBuffer, "文件错误", MB_OK | MB_ICONSTOP );          //MLHIDE
		return FALSE;
	}

#if defined(PUK2) && defined(_DEBUG)
	// バージョンが同じだったときだけチェックする（バージョン０はチェックしない）
	if( iNowVersion > 0 && iNowVersion == iFinalVersion ){
#else
	// バージョンが同じだったときだけチェックする
	if( iNowVersion == iFinalVersion ){
#endif
		// ２つのファイルサイズが违うなら
		if( uFinalSize != uNowSize ){
#ifdef PUK3_ERRORMESSAGE_NUM
			sprintf( szBuffer, ERRMSG_80, pFileName, uNowSize, uFinalSize );
#else
			sprintf( szBuffer, "%s 的文件大小有错。%u != %u\n需要进行磁盘检查后再重新安装游戏",        //MLHIDE
				pFileName, uNowSize, uFinalSize );
#endif
			MessageBox( hWnd, szBuffer, "文件错误", MB_OK | MB_ICONSTOP );         //MLHIDE
			return FALSE;
		}
	}

	return TRUE;

}


//-------------------------------------------------------------
//  全部のファイルのチェックを行う
//-------------------------------------------------------------
int AllCheckBinFile( void ){

	int NgFlg = 0;
#ifdef MULTI_GRABIN
	//------------------------------------------------------------
	// 予测されるバージョンに应じてチェックするファイルを变更する。
	//------------------------------------------------------------

	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName[BINMODE_NORMAL], CHECK_GRAPHIC_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( graphicInfoBinName[BINMODE_NORMAL], CHECK_GRAPHICINFO_BIN ) == FALSE )NgFlg = 1;
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName[BINMODE_NORMAL], CHECK_ANIME_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( animeInfoBinName[BINMODE_NORMAL], CHECK_ANIMEINFO_BIN ) == FALSE )NgFlg = 1;
	#ifdef PUK3_RIDEBIN
		// 座标データのサイズをチェック
		if( CheckBinFile( coordinateBinName[BINMODE_NORMAL], CHECK_CRD_BIN ) == FALSE )NgFlg = 1;
		if( CheckBinFile( coordinateInfoBinName[BINMODE_NORMAL], CHECK_CRDINFO_BIN ) == FALSE )NgFlg = 1;
	#endif

	//----------------------------------
	// ここからは Ex Version
	//----------------------------------

	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName[BINMODE_EX], CHECK_EX_GRAPHIC_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( graphicInfoBinName[BINMODE_EX], CHECK_EX_GRAPHICINFO_BIN ) == FALSE )NgFlg = 1;
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName[BINMODE_EX], CHECK_EX_ANIME_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( animeInfoBinName[BINMODE_EX], CHECK_EX_ANIMEINFO_BIN ) == FALSE )NgFlg = 1;
	#ifdef PUK3_RIDEBIN
		// 座标データのサイズをチェック
		if( CheckBinFile( coordinateBinName[BINMODE_EX], CHECK_EX_CRD_BIN ) == FALSE )NgFlg = 1;
		if( CheckBinFile( coordinateInfoBinName[BINMODE_EX], CHECK_EX_CRDINFO_BIN ) == FALSE )NgFlg = 1;
	#endif

	//----------------------------------
	// ここからは Ver2 
	//----------------------------------

	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName[BINMODE_VER2], CHECK_VER2_GRAPHIC_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( graphicInfoBinName[BINMODE_VER2], CHECK_VER2_GRAPHICINFO_BIN ) == FALSE )NgFlg = 1;
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName[BINMODE_VER2], CHECK_VER2_ANIME_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( animeInfoBinName[BINMODE_VER2], CHECK_VER2_ANIMEINFO_BIN ) == FALSE )NgFlg = 1;
	#ifdef PUK3_RIDEBIN
		// 座标データのサイズをチェック
		if( CheckBinFile( coordinateBinName[BINMODE_VER2], CHECK_VER2_CRD_BIN ) == FALSE )NgFlg = 1;
		if( CheckBinFile( coordinateInfoBinName[BINMODE_VER2], CHECK_VER2_CRDINFO_BIN ) == FALSE )NgFlg = 1;
	#endif

#ifdef PUK2
	//----------------------------------
	// ここからは Ver3 
	//----------------------------------
/*
	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName[BINMODE_VER3], CHECK_VER3_GRAPHIC_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( graphicInfoBinName[BINMODE_VER3], CHECK_VER3_GRAPHICINFO_BIN ) == FALSE )NgFlg = 1;
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName[BINMODE_VER3], CHECK_VER3_ANIME_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( animeInfoBinName[BINMODE_VER3], CHECK_VER3_ANIMEINFO_BIN ) == FALSE )NgFlg = 1;
*/
	#ifdef PUK3_RIDEBIN
/*		// 座标データのサイズをチェック
		if( CheckBinFile( coordinateBinName[BINMODE_VER3], CHECK_VER3_CRD_BIN ) == FALSE )NgFlg = 1;
		if( CheckBinFile( coordinateInfoBinName[BINMODE_VER3], CHECK_VER3_CRDINFO_BIN ) == FALSE )NgFlg = 1;
*/	#endif

	//----------------------------------
	// ここからは PUK2 
	//----------------------------------

	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName[BINMODE_PUK2], CHECK_PUK2_GRAPHIC_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( graphicInfoBinName[BINMODE_PUK2], CHECK_PUK2_GRAPHICINFO_BIN ) == FALSE )NgFlg = 1;
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName[BINMODE_PUK2], CHECK_PUK2_ANIME_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( animeInfoBinName[BINMODE_PUK2], CHECK_PUK2_ANIMEINFO_BIN ) == FALSE )NgFlg = 1;
	#ifdef PUK3_RIDEBIN
		// 座标データのサイズをチェック
		if( CheckBinFile( coordinateBinName[BINMODE_PUK2], CHECK_PUK2_CRD_BIN ) == FALSE )NgFlg = 1;
		if( CheckBinFile( coordinateInfoBinName[BINMODE_PUK2], CHECK_PUK2_CRDINFO_BIN ) == FALSE )NgFlg = 1;
	#endif
#endif
#ifdef PUK3_BIN
	//----------------------------------
	// ここからは PUK3 
	//----------------------------------

	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName[BINMODE_PUK3], CHECK_PUK3_GRAPHIC_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( graphicInfoBinName[BINMODE_PUK3], CHECK_PUK3_GRAPHICINFO_BIN ) == FALSE )NgFlg = 1;
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName[BINMODE_PUK3], CHECK_PUK3_ANIME_BIN ) == FALSE )NgFlg = 1;
	if( CheckBinFile( animeInfoBinName[BINMODE_PUK3], CHECK_PUK3_ANIMEINFO_BIN ) == FALSE )NgFlg = 1;
	#ifdef PUK3_RIDEBIN
		// 座标データのサイズをチェック
		if( CheckBinFile( coordinateBinName[BINMODE_PUK3], CHECK_PUK3_CRD_BIN ) == FALSE )NgFlg = 1;
		if( CheckBinFile( coordinateInfoBinName[BINMODE_PUK3], CHECK_PUK3_CRDINFO_BIN ) == FALSE )NgFlg = 1;
	#endif
#endif

	// どれかに失败していたら
	if( NgFlg == 1 ){
		return FALSE;
	}else{
		return TRUE;
	}
#else
	// グラフィックファイルのサイズをチェック
	if( CheckBinFile( graphicBinName, CHECK_GRAPHIC_BIN ) == FALSE ){
		NgFlg = 1;
	}
	if( CheckBinFile( graphicInfoBinName, CHECK_GRAPHICINFO_BIN ) == FALSE ){
		NgFlg = 1;
	}
	// アニメファイルのサイズをチェック
	if( CheckBinFile( animeBinName, CHECK_ANIME_BIN ) == FALSE ){
		NgFlg = 1;
	}
	if( CheckBinFile( animeInfoBinName, CHECK_ANIMEINFO_BIN ) == FALSE ){
		NgFlg = 1;
	}
	// どれかに失败していたら
	if( NgFlg == 1 ){
		return FALSE;
	}else{
		return TRUE;
	}

#endif
}
