﻿#ifndef __MENUGROUPMAIL_H__
#define __MENUGROUPMAIL_H__

BOOL MenuWindowGroupMail (int mouse);
BOOL MenuWindowGroupMailDraw (int mouse);
BOOL MenuWindowGroupMailClose (int no, unsigned int flag);
BOOL MenuWindowGroupMailPageChange (int no, unsigned int flag);
BOOL MenuWindowGroupMailScrollOneLine (int no, unsigned int flag);
BOOL MenuGroupMailSetSwitch (int no, unsigned int flag);
BOOL MenuWindowGroupMailDel (void);
BOOL MenuWindowGroupMailScrollWheel (int no, unsigned int flag);

BOOL MenuGroupMailSendSwitch (int no, unsigned int flag);
BOOL MenuWindowGroupMailTypeChange (int no, unsigned int flag);
void InitGropuMailStatusFlag(void);
void SetGropuMailStatusFlag(int type);

void MenuWindowAddressBookSortFromVal (int mode);

#define GID_groupMailFrame 244245
#define GID_groupMailCloseOn 243000
#define GID_groupMailCloseOff 243001
#define GID_groupMailCloseOver 243002
#define GID_groupMailScrollUpArrowOn 243090
#define GID_groupMailScrollUpArrowOff 243091
#define GID_groupMailScrollUpArrowOver 243092
#define GID_groupMailScrollDownArrowOn 243093
#define GID_groupMailScrollDownArrowOff 243094
#define GID_groupMailScrollDownArrowOver 243095
#define GID_groupMailScrollTab 243096
#define GID_groupMailPrevPageOn 243030
#define GID_groupMailPrevPageOff 243031
#define GID_groupMailPrevPageOver 243032
#define GID_groupMailNextPageOn 243033
#define GID_groupMailNextPageOff 243034
#define GID_groupMailNextPageOver 243035
#define GID_goupMailNet 244246
#define GID_groupMailBar 244247
#define GID_groupMailBar2 244248
#define GID_groupMailMailSendOn 243012
#define GID_groupMailMailSendOff 243013
#define GID_groupMailMailSendOver 243014

#define GID_groupMailMailMenuOn 244254
#define GID_groupMailMailMenuOff 244255
#define GID_groupMailMailMenuOver 244256

#define GID_groupMailMailCheckOn 244257
#define GID_groupMailMailCheckOff 244258


#define MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE 7
//家族称号総数
#define AMOUNT_OF_GUILD_TITLE 32
//家族成员総数
#define AMOUNT_OF_GUILD_MEMBER GUILD_MEMBER_MAX
//名片成员総数
#define AMOUNT_OF_ADDRESS_MEMBER ADDRESS_BOOK
//最大数（今のところ家族の１００人）
#define AMOUNT_OF_GROUP_MAIL_MAX GUILD_MEMBER_MAX

struct GroupMailStatus{
	
	int MemberCount;
	int MemberType;
	int MemberSort[AMOUNT_OF_GROUP_MAIL_MAX];
	int MemberFlag[AMOUNT_OF_GROUP_MAIL_MAX];
#ifdef PUK3_MAIL_ETC
	BOOL ReOpenFlag;		// 群邮件のリオープンフラグ
							// メールウィンドウが开くときに设定され
							// 群邮件ウィンドウが开くときに使用、初期化される
#endif

};

enum{
	GroupMailAddressMode,
	GroupMailGuildMode,
	GroupMailGuildTitleMode,
};

extern BOOK_TYPE MenuHistoryWindowType;
extern GroupMailStatus GMST;

GRAPHIC_SWITCH MenuWindowGroupMailGraph[] = {
	{GID_groupMailFrame, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailCloseOn, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailScrollUpArrowOn, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailScrollTab, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailScrollDownArrowOn, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailPrevPageOn, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailNextPageOn, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailBar, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailMailSendOn, 0, 0, 0, 0, 0xffffffff},
	{GID_goupMailNet, 0, 0, 0, 0, 0x80ffffff},
	{GID_groupMailBar2, 0, 0, 0, 0, 0xffffffff},

	{GID_groupMailMailMenuOn, 0, 0, 0, 0, 0xffffffff},
	{GID_groupMailMailCheckOn, 0, 0, 0, 0, 0xffffffff},
};

TEXT_SWITCH MenuWindowGroupMailText[] = {
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "Address OnLine Member"},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "Guild OnLine Member"},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "Guild All Member"},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "Member"},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "名片成员"},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "家族成员"},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12, "家族称号"},
};

BUTTON_SWITCH MenuWindowGroupMailButton[] = {
	{0, 0}};
	
enum {
	EnumMenuWindowGroupMailCloseButton,
	EnumMenuWindowGroupMailScrollUp,
	EnumMenuWindowGroupMailScrollDown,
	EnumMenuWindowGroupMailScrollTab,
	EnumMenuWindowGroupMailScrollBar,
	EnumMenuWindowGroupMailScrollWheel,
	EnumMenuWindowGroupMailPrevPage,
	EnumMenuWindowGroupMailNextPage,
	EnumMenuWindowGroupMailPrevType,
	EnumMenuWindowGroupMailNextType,
	EnumMenuWindowGroupMailType,
	EnumMenuWindowGroupMailContent0,
	EnumMenuWindowGroupMailContent1,
	EnumMenuWindowGroupMailContent2,
	EnumMenuWindowGroupMailContent3,
	EnumMenuWindowGroupMailContent4,
	EnumMenuWindowGroupMailContent5,
	EnumMenuWindowGroupMailContent6,
	EnumMenuWindowGroupMailContent7,
	EnumMenuWindowGroupMailSendTo0,
	EnumMenuWindowGroupMailSendTo1,
	EnumMenuWindowGroupMailSendTo2,
	EnumMenuWindowGroupMailSendTo3,
	EnumMenuWindowGroupMailSendTo4,
	EnumMenuWindowGroupMailSendTo5,
	EnumMenuWindowGroupMailSendTo6,
	EnumMenuWindowGroupMailSendTo7,
	EnumMenuWindowGroupMailBar0,
	EnumMenuWindowGroupMailBar1,
	EnumMenuWindowGroupMailBar2,
	EnumMenuWindowGroupMailBar3,
	EnumMenuWindowGroupMailBar4,
	EnumMenuWindowGroupMailBar5,
	EnumMenuWindowGroupMailBar6,
	EnumMenuWindowGroupMailBar7,
	EnumMenuWindowGroupMailFrame,
	EnumMenuWindowGroupMailNet,
	EnumMenuWindowGroupMailEnd};


SWITCH_DATA MenuWindowGroupMailSwitch [] = {
	{SWITCH_GRAPHIC, 275-36, 7+2, 11, 11, TRUE, &MenuWindowGroupMailGraph[1], MenuSwitchCloseButton},
	{SWITCH_GRAPHIC, 239, 88, 11, 11, TRUE, &MenuWindowGroupMailGraph[2], MenuWindowGroupMailScrollOneLine},
	{SWITCH_GRAPHIC, 239,275, 11, 11, TRUE, &MenuWindowGroupMailGraph[4], MenuWindowGroupMailScrollOneLine},
	{SWITCH_GRAPHIC, 239, 99, 10, 14, TRUE, &MenuWindowGroupMailGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 239, 99, 10,177, TRUE, &MenuWindowGroupMailButton[0], MenuSwitchScrollBarV},
	{SWITCH_NONE,	   0,  0, 280,317, TRUE, NULL, MenuWindowGroupMailScrollWheel },								// マウスホイール判定
	{SWITCH_GRAPHIC, 20, 288, 18, 18, TRUE, &MenuWindowGroupMailGraph[5], MenuWindowGroupMailPageChange},
	{SWITCH_GRAPHIC, 60, 288, 18, 18, TRUE, &MenuWindowGroupMailGraph[6], MenuWindowGroupMailPageChange},
	{SWITCH_GRAPHIC,111,  30, 18, 18, TRUE, &MenuWindowGroupMailGraph[5], MenuWindowGroupMailTypeChange},
	{SWITCH_GRAPHIC,218,  30, 18, 18, TRUE, &MenuWindowGroupMailGraph[6], MenuWindowGroupMailTypeChange},
	{SWITCH_TEXT,   131,  33      , 0, 0, TRUE, &MenuWindowGroupMailText[4], &MenuSwitchNone},

	{SWITCH_TEXT,     23,  57     , 0, 0, TRUE, &MenuWindowGroupMailText[0], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*0, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*1, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*2, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*3, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*4, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*5, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},
	{SWITCH_TEXT,     23,  93+29*6, 0, 0, TRUE, &MenuWindowGroupMailText[3], &MenuSwitchNone},

	{SWITCH_GRAPHIC, 229-15, 55     ,19, 19, TRUE, &MenuWindowGroupMailGraph[11], MenuGroupMailSetSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*0,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*1,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*2,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*3,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*4,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*5,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC, 229-15, 91+29*6,19, 19, TRUE, &MenuWindowGroupMailGraph[12], MenuGroupMailSendSwitch},
	{SWITCH_GRAPHIC,  16,  50     , 0, 0, TRUE, &MenuWindowGroupMailGraph[10], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*0, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*1, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*2, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*3, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*4, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*5, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC,  16,  86+29*6, 0, 0, TRUE, &MenuWindowGroupMailGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowGroupMailGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 10, 25, 0, 0, TRUE, &MenuWindowGroupMailGraph[9], MenuSwitchNone}};

const WINDOW_DATA WindowDataMenuGroupMail = {
	0,
		4, 68,  84, 280, 317, 0x80010101, EnumMenuWindowGroupMailEnd, MenuWindowGroupMailSwitch, MenuWindowGroupMail, MenuWindowGroupMailDraw,MenuWindowGroupMailDel
};
	
static WINDOW_DRAGMOVE DragMoveDataGroupMail = {
	2,
#ifdef PUK3_MAIL_ETC
		0, 0, 280, 27,
#else
		0, 0, 280, 20,
#endif
		260, 0, 20, 120};

#endif
