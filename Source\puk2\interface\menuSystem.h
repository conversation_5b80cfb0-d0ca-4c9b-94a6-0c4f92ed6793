﻿//メニュー＞システム

#ifndef _MENUSYSTEM_H_
#define _MENUSYSTEM_H_

//====================================//
//				メイン				  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSytemSwitchClose( int no, unsigned int flag );

BOOL MenuSytemSwitchLogout( int no, unsigned int flag );
BOOL MenuSytemSwitchOperation( int no, unsigned int flag );
BOOL MenuSytemSwitchSound( int no, unsigned int flag );
BOOL MenuSytemSwitchDrawSet( int no, unsigned int flag );
BOOL MenuSytemSwitchShortCut( int no, unsigned int flag );

BOOL MenuSytemSwitchLoginGate( int no, unsigned int flag );
BOOL MenuSytemSwitchServerSel( int no, unsigned int flag );

BOOL MenuSytemSwitchClick( int no, unsigned int flag );
BOOL MenuSytemSwitchCursor( int no, unsigned int flag );
BOOL MenuSytemSwitchShortCutType( int no, unsigned int flag );

BOOL MenuSytemSwitchBgmLeft( int no, unsigned int flag );
BOOL MenuSytemSwitchBgmRight( int no, unsigned int flag );
BOOL MenuSytemSwitchSeLeft( int no, unsigned int flag );
BOOL MenuSytemSwitchSeRight( int no, unsigned int flag );
BOOL MenuSytemSwitchSoundMode( int no, unsigned int flag );
BOOL MenuSytemSwitchChatSe( int no, unsigned int flag );

BOOL MenuSytemSwitchSynchro( int no, unsigned int flag );
#if defined(PUK2_CHANGE_3DDEIVCE) || defined(PUK2_3DDEVICE_DISP)
	BOOL MenuSytemSwitchChange3DDevice( int no, unsigned int flag );
#endif


BOOL MenuWindowSystem( int mouse );
BOOL MenuWindowSystemDraw( int mouse );

BOOL closeMenuWindowSystem();

GRAPHIC_SWITCH MenuWindowSystemGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},				//クローズボタン

	{GID_SystemLogOutOn,0,0,0,0,0xFFFFFFFF},			//登出
	{GID_SystemOperationOn,0,0,0,0,0xFFFFFFFF},			//操作设定
	{GID_SystemSoundSetOn,0,0,0,0,0xFFFFFFFF},			//サウンド设定
	{GID_SystemDrawSetOn,0,0,0,0,0xFFFFFFFF},			//绘图设定
	{GID_SystemShortCutOn,0,0,0,0,0xFFFFFFFF},			//ショートカット

	{GID_SystemFrameLogOut,0,0,0,79,0xFFFFFFFF},		//登出枠
	{GID_SystemFrameOperation,0,0,0,128,0xFFFFFFFF},	//操作设定枠
	{GID_SystemFrameSound,0,0,0,203,0xFFFFFFFF},		//サウンド设定枠
#if defined(PUK2_CHANGE_3DDEIVCE) || defined(PUK2_3DDEVICE_DISP)
	{GID_SystemFrameDrawSet,0,0,0,90,0xFFFFFFFF},		//绘图设定枠
#else
	{GID_SystemFrameDrawSet,0,0,0,45,0xFFFFFFFF},		//绘图设定枠
#endif

	{GID_SystemLoginGateOn,0,0,0,0,0xFFFFFFFF},			//登入点へ
	{GID_SystemServerSelOn,0,0,0,0,0xFFFFFFFF},			//服务器选择花面へ

	{GID_System____LeftOn,0,0,0,0,0xFFFFFFFF},			//右键 or Ｃｔｒｌ+左键
	{GID_SystemMSmoothOn,0,0,0,0,0xFFFFFFFF},			//平滑 or 通常

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},				//左ボタン
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},				//右ボタン
	{GID_SystemStereoOn,0,0,0,0,0xFFFFFFFF},			//立体声

	{GID_SystemSynchroOn,0,0,0,0,0xFFFFFFFF},			//同期 or 非同期

	{GID_SystemBase,0,0,0,0,0xFFFFFFFF},				//ベース
	{GID_SystemBack,0,0,0,0,0x99FFFFFF},				//背景

	{GID_SystemShortCutPUK1On,0,0,0,0,0xFFFFFFFF},		//ショートカット种类
	{GID_SystemChatSeOffOn,0,0,0,0,0xFFFFFFFF},			//チャット音
#if defined(PUK2_CHANGE_3DDEIVCE) || defined(PUK2_3DDEVICE_DISP)
	{GID_0ButtonOn,0,0,0,0,0xFFFFFFFF},					// 描画モード０切替
	{GID_1ButtonOn,0,0,0,0,0xFFFFFFFF},					// 描画モード１切替
	{GID_2ButtonOn,0,0,0,0,0xFFFFFFFF},					// 描画モード２切替
	{GID_3ButtonOn,0,0,0,0,0xFFFFFFFF},					// 描画モード３切替
	{GID_4ButtonOn,0,0,0,0,0xFFFFFFFF},					// 描画モード４切替
#endif
};

TEXT_SWITCH SoundText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,""},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,""},
};

char SystemNum_Sound[11];
char SystemNum_BGM[11];

NUMBER_SWITCH MenuWindowSystemNum[] = {
	{ SystemNum_Sound, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ SystemNum_BGM, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_LEFT_JUSTIFIED },
};

// マップスイッチ
static SWITCH_DATA SystemSwitch[] = {

{ SWITCH_GRAPHIC,186,  9,  11, 11, TRUE, &MenuWindowSystemGraph[ 0], MenuSytemSwitchClose },		//クローズボタン

{ SWITCH_GRAPHIC, 69, 51,  70, 17, TRUE, &MenuWindowSystemGraph[ 1], MenuSytemSwitchLogout },		//登出
{ SWITCH_GRAPHIC, 73, 79,  59, 17, TRUE, &MenuWindowSystemGraph[ 2], MenuSytemSwitchOperation },	//操作设定
{ SWITCH_GRAPHIC, 81,109,  46, 17, TRUE, &MenuWindowSystemGraph[ 3], MenuSytemSwitchSound },		//サウンド设定
{ SWITCH_GRAPHIC, 75,142,  60, 17, TRUE, &MenuWindowSystemGraph[ 4], MenuSytemSwitchDrawSet },		//绘图设定
{ SWITCH_GRAPHIC, 51,174, 114, 17, TRUE, &MenuWindowSystemGraph[ 5], MenuSytemSwitchShortCut },		//ショートカット

{ SWITCH_GRAPHIC,  0,  0,  40, 11, TRUE, &MenuWindowSystemGraph[18], MenuSwitchNone },				//ベース
{ SWITCH_GRAPHIC, 13, 26,  40, 11, TRUE, &MenuWindowSystemGraph[19], MenuSwitchNone },				//背景

{ SWITCH_GRAPHIC,-111,30, 102, 18, TRUE, &MenuWindowSystemGraph[10], MenuSytemSwitchLoginGate },	//登入点へ
{ SWITCH_GRAPHIC,-111,58, 102, 18, TRUE, &MenuWindowSystemGraph[11], MenuSytemSwitchServerSel },	//服务器选择花面へ

{ SWITCH_GRAPHIC,-117,40,  88, 18, TRUE, &MenuWindowSystemGraph[12], MenuSytemSwitchClick },		//右键 or Ｃｔｒｌ+左键
{ SWITCH_GRAPHIC,-117,75,  88, 18, TRUE, &MenuWindowSystemGraph[13], MenuSytemSwitchCursor },		//平滑 or 通常
{ SWITCH_GRAPHIC,-117,110, 88, 18, TRUE, &MenuWindowSystemGraph[20], MenuSytemSwitchShortCutType },	//ショートカット种类

{ SWITCH_NUMBER, -72, 51,   0,  0, TRUE, &MenuWindowSystemNum[1], MenuSwitchNone },					//音量表示
{ SWITCH_GRAPHIC,-98, 47,  18, 18, TRUE, &MenuWindowSystemGraph[14], MenuSytemSwitchBgmLeft },		//左ボタン
{ SWITCH_GRAPHIC,-35, 47,  18, 18, TRUE, &MenuWindowSystemGraph[15], MenuSytemSwitchBgmRight },		//右ボタン

{ SWITCH_NUMBER, -72, 95,   0,  0, TRUE, &MenuWindowSystemNum[0], MenuSwitchNone },					//音量表示
{ SWITCH_GRAPHIC,-98, 91,  18, 18, TRUE, &MenuWindowSystemGraph[14], MenuSytemSwitchSeLeft },		//左ボタン
{ SWITCH_GRAPHIC,-35, 91,  18, 18, TRUE, &MenuWindowSystemGraph[15], MenuSytemSwitchSeRight },		//右ボタン

{ SWITCH_GRAPHIC,-88,141,  63, 18, TRUE, &MenuWindowSystemGraph[16], MenuSytemSwitchSoundMode },	//立体声
{ SWITCH_GRAPHIC,-88,185,  63, 18, TRUE, &MenuWindowSystemGraph[21], MenuSytemSwitchChatSe },		//チャット音

{ SWITCH_GRAPHIC, -45,25,  44, 18, TRUE, &MenuWindowSystemGraph[17], MenuSytemSwitchSynchro },		//同期 or 非同期
#if defined(PUK2_CHANGE_3DDEIVCE) || defined(PUK2_3DDEVICE_DISP)
	#ifdef PUK2_FPS
	{ SWITCH_GRAPHIC,-120+25*0,72,  22, 19, TRUE, &MenuWindowSystemGraph[22], MenuSytemSwitchChange3DDevice },		//描画モード０
	{ SWITCH_GRAPHIC,-120+25*1,72,  22, 19, TRUE, &MenuWindowSystemGraph[23], MenuSytemSwitchChange3DDevice },		//描画モード１
	{ SWITCH_GRAPHIC,-120+25*2,72,  22, 19, TRUE, &MenuWindowSystemGraph[24], MenuSytemSwitchChange3DDevice },		//描画モード２
	{ SWITCH_GRAPHIC,-120+25*3,72,  22, 19, TRUE, &MenuWindowSystemGraph[25], MenuSytemSwitchChange3DDevice },		//描画モード３
	{ SWITCH_GRAPHIC,-120+25*4,72,  22, 19, TRUE, &MenuWindowSystemGraph[26], MenuSytemSwitchChange3DDevice },		//描画モード４
	#else
	{ SWITCH_GRAPHIC,        0,72,  22, 19, FALSE, &MenuWindowSystemGraph[22], MenuSytemSwitchChange3DDevice },		//描画モード０
	{ SWITCH_GRAPHIC,-120+31*0,72,  22, 19, TRUE, &MenuWindowSystemGraph[23], MenuSytemSwitchChange3DDevice },		//描画モード１
	{ SWITCH_GRAPHIC,-120+31*1,72,  22, 19, TRUE, &MenuWindowSystemGraph[24], MenuSytemSwitchChange3DDevice },		//描画モード２
	{ SWITCH_GRAPHIC,-120+31*2,72,  22, 19, TRUE, &MenuWindowSystemGraph[25], MenuSytemSwitchChange3DDevice },		//描画モード３
	{ SWITCH_GRAPHIC,-120+31*3,72,  22, 19, TRUE, &MenuWindowSystemGraph[26], MenuSytemSwitchChange3DDevice },		//描画モード４
	#endif
#endif

{ SWITCH_GRAPHIC,-130,10, 150, 79, TRUE, &MenuWindowSystemGraph[ 6], MenuSwitchDelMouse },			//登出枠
{ SWITCH_GRAPHIC,-130,10, 150,128, TRUE, &MenuWindowSystemGraph[ 7], MenuSwitchDelMouse },			//操作设定枠
{ SWITCH_GRAPHIC,-130,10, 150,203, TRUE, &MenuWindowSystemGraph[ 8], MenuSwitchDelMouse },			//サウンド设定枠
#if defined(PUK2_CHANGE_3DDEIVCE) || defined(PUK2_3DDEVICE_DISP)
{ SWITCH_GRAPHIC,-130,10, 150, 90, TRUE, &MenuWindowSystemGraph[ 9], MenuSwitchDelMouse },			//绘图设定枠
#else
{ SWITCH_GRAPHIC,-130,10, 150, 45, TRUE, &MenuWindowSystemGraph[ 9], MenuSwitchDelMouse },			//绘图设定枠
#endif

{ SWITCH_NONE,	  209, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },								//ドラッグ用

};

enum{
	EnumGraphSystemClose,		

	EnumGraphSystemLogOut,		
	EnumGraphSystemOperation,		
	EnumGraphSystemSoundSet,		
	EnumGraphSystemDrawSet,		
	EnumGraphSystemShortCut,		

	EnumGraphSystemBase,
	EnumGraphSystemWindow,

	EnumGraphSystemLoginGate,
	EnumGraphSystemServerSel,

	EnumGraphSystem____Left,
	EnumGraphSystemMSmooth,
	EnumGraphSystemShortCutType,

	EnumGraphBgmText,
	EnumGraphBgmLeft,
	EnumGraphBgmRight,

	EnumGraphSeText,
	EnumGraphSeLeft,
	EnumGraphSeRight,

	EnumGraphSystemStereo,
	EnumGraphSystemChatSe,

	EnumGraphSystemSynchro,
#if defined(PUK2_CHANGE_3DDEIVCE) || defined(PUK2_3DDEVICE_DISP)
	EnumGraphSystemChange3DDevece0,
	EnumGraphSystemChange3DDevece1,
	EnumGraphSystemChange3DDevece2,
	EnumGraphSystemChange3DDevece3,
	EnumGraphSystemChange3DDevece4,
#endif

	EnumGraphSystemFrameLogOut,
	EnumGraphSystemFrameOperation,
	EnumGraphSystemFrameSoundSet,
	EnumGraphSystemFrameDrawSet,

	EnumSystemDragBack,

	EnumSystemEnd,
};


const WINDOW_DATA WindowDataMenuSystem = {
 0,															// メニューウィンドウ
     4,   409,100,207,222, 0x80010101,  EnumSystemEnd,  SystemSwitch, MenuWindowSystem,MenuWindowSystemDraw,closeMenuWindowSystem
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSystem={
	2,
	 16,  0,184, 30,
	209,  7, 18, 78,
};

extern int SimpleLogoutFlag;	// 简易登出フラグ



//====================================//
//			ショートカット			  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSwitchSystemShortCutClose( int no, unsigned int flag );

BOOL MenuSwitchSystemShortCutScrollUp( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutScrollDown( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutScrollRight( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutScrollWheel( int no, unsigned int flag );

BOOL MenuSwitchSystemShortCutWindow( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutSwitch( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutAction( int no, unsigned int flag );
BOOL MenuSwitchSystemShortCutChat( int no, unsigned int flag );

BOOL MenuSwitchSystemShortCutPanelHit( int no, unsigned int flag );


BOOL MenuWindowSystemShortCutBf( int mouse );
BOOL MenuWindowSystemShortCutAf( int mouse );

GRAPHIC_SWITCH MenuWindowSystemShortCutGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},				//クローズボタン

	{GID_SystemShortCutBase,0,0,0,0,0xFFFFFFFF},			//ベース

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},						//スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(下ボタン)

	{GID_SystemShortCutBtWindowOn ,0,0,0,0,0xFFFFFFFF},		//ウィンドウボタン
	{GID_SystemShortCutBtSwitchOn ,0,0,0,0,0xFFFFFFFF},		//スイッチボタン
	{GID_SystemShortCutBtActionOn ,0,0,0,0,0xFFFFFFFF},		//アクションボタン

	{GID_TitlePanelOn,0,0,0,0,0xFFFFFFFF},					//テキストボックス

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(右ボタン)
};

BUTTON_SWITCH MenuWindowSystemShortCutButton[]={
	{0,0,0},
};

// スイッチ
static SWITCH_DATA SystemShortCutSwitch[] = {

{ SWITCH_DIALOG,  80, 41,   0,  0, TRUE, NULL, MenuSwitchNone },													//チャット登録

{ SWITCH_GRAPHIC,265,  9,  11, 11, TRUE, &MenuWindowSystemShortCutGraph[0], MenuSwitchSystemShortCutClose },		//クローズボタン

{ SWITCH_GRAPHIC,253, 68,   0, 14, TRUE, &MenuWindowSystemShortCutGraph[2], MenuSwitchNone },						//スクロールバー(つまみ)
{ SWITCH_BUTTON, 254, 68,  11,137, TRUE, &MenuWindowSystemShortCutButton[0], MenuSwitchScrollBarV },				//スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,253, 58,  11, 11, TRUE, &MenuWindowSystemShortCutGraph[3], MenuSwitchSystemShortCutScrollUp },		//スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,253,204,  11, 11, TRUE, &MenuWindowSystemShortCutGraph[4], MenuSwitchSystemShortCutScrollDown },	//スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,227, 35,  18, 18, TRUE, &MenuWindowSystemShortCutGraph[9], MenuSwitchSystemShortCutScrollLeft },	//スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,245, 35,  18, 18, TRUE, &MenuWindowSystemShortCutGraph[10], MenuSwitchSystemShortCutScrollRight },	//スクロールバー(右ボタン)
{ SWITCH_NONE,	   0,  0, 286,233, TRUE, NULL, MenuSwitchSystemShortCutScrollWheel },								// マウスホイール判定

{ SWITCH_GRAPHIC, 25, 56, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25, 72, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25, 88, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,104, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,120, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,136, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,152, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,168, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,184, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス
{ SWITCH_GRAPHIC, 25,200, 216, 16, TRUE, &MenuWindowSystemShortCutGraph[8], MenuSwitchNone },						//テキストボックス

{ SWITCH_GRAPHIC, 25, 36,  49, 17, TRUE, &MenuWindowSystemShortCutGraph[5], MenuSwitchSystemShortCutWindow },		//ウィンドウボタン
{ SWITCH_GRAPHIC, 75, 36,  49, 17, TRUE, &MenuWindowSystemShortCutGraph[6], MenuSwitchSystemShortCutSwitch },		//スイッチボタン
{ SWITCH_GRAPHIC,125, 36,  49, 17, TRUE, &MenuWindowSystemShortCutGraph[7], MenuSwitchSystemShortCutAction },		//アクションボタン
{ SWITCH_GRAPHIC,175, 36,  49, 17, TRUE, &MenuWindowSystemShortCutGraph[7], MenuSwitchSystemShortCutChat },			//チャットボタン

{ SWITCH_GRAPHIC,  0,  0,  40, 11, TRUE, &MenuWindowSystemShortCutGraph[1], MenuSwitchNone },						//ベース

{ SWITCH_NONE,	  288, 5,  18, 79, TRUE, NULL, MenuSwitchDelMouse },												//ドラッグ用

};

enum{
	EnumSystemShortCutInputStr,

	EnumGraphSystemShortCutClose,

	EnumGraphSystemShortCutScroll,
	EnumBtSystemShortCutScroll,
	EnumBtSystemShortCutScrollUp,
	EnumBtSystemShortCutScrollDown,
	EnumBtSystemShortCutScrollLeft,
	EnumBtSystemShortCutScrollRight,
	EnumBtSystemShortCutScrollWheel,

	EnumGraphSystemShortCutTextBox0,
	EnumGraphSystemShortCutTextBox1,
	EnumGraphSystemShortCutTextBox2,
	EnumGraphSystemShortCutTextBox3,
	EnumGraphSystemShortCutTextBox4,
	EnumGraphSystemShortCutTextBox5,
	EnumGraphSystemShortCutTextBox6,
	EnumGraphSystemShortCutTextBox7,
	EnumGraphSystemShortCutTextBox8,
	EnumGraphSystemShortCutTextBox9,

	EnumGraphSystemShortCutWindow,
	EnumGraphSystemShortCutSwitch,
	EnumGraphSystemShortCutAction,
	EnumGraphSystemShortCutChat,

	EnumGraphSystemShortCutBase,

	EnumSystemDragShortCutBack,

	EnumSystemShortCutEnd,
};


const WINDOW_DATA WindowDataMenuSystemShortCut = {
 0,															// メニューウィンドウ
     4,  15,117,286,233, 0x80010101,  EnumSystemShortCutEnd,  SystemShortCutSwitch, MenuWindowSystemShortCutBf,MenuWindowSystemShortCutAf,MenuWindowDel
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSystemShortCut={
	2,
	 10,  0,275, 33,
	 288, 5, 18, 79
};

// ウィンドウ管理构造体
struct SYSTEMSHORTCUTMASTER{
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 

	int DispType;							// 现在の表示モード
	int DispStart;							// 现在の表示开始位置
};

char ShortCutStrWindowPUK1[][51]={
	LANG_MENUSYSTEM_H_WINDOWPUK1_1,
	LANG_MENUSYSTEM_H_WINDOWPUK1_2,
	LANG_MENUSYSTEM_H_WINDOWPUK1_3,
	LANG_MENUSYSTEM_H_WINDOWPUK1_4,
	LANG_MENUSYSTEM_H_WINDOWPUK1_5,
	LANG_MENUSYSTEM_H_WINDOWPUK1_6,
	LANG_MENUSYSTEM_H_WINDOWPUK1_7,
	LANG_MENUSYSTEM_H_WINDOWPUK1_8,
	LANG_MENUSYSTEM_H_WINDOWPUK1_9,
	LANG_MENUSYSTEM_H_WINDOWPUK1_10,
	LANG_MENUSYSTEM_H_WINDOWPUK1_11,
	LANG_MENUSYSTEM_H_WINDOWPUK1_12,
};
char ShortCutStrSwitchPUK1[][51]={
	LANG_MENUSYSTEM_H_SWITCHPUK1_1,
	LANG_MENUSYSTEM_H_SWITCHPUK1_2,
	LANG_MENUSYSTEM_H_SWITCHPUK1_3,
	LANG_MENUSYSTEM_H_SWITCHPUK1_4,
	LANG_MENUSYSTEM_H_SWITCHPUK1_5,
	LANG_MENUSYSTEM_H_SWITCHPUK1_6,
	LANG_MENUSYSTEM_H_SWITCHPUK1_7,
	LANG_MENUSYSTEM_H_SWITCHPUK1_8,
	LANG_MENUSYSTEM_H_SWITCHPUK1_9,
	LANG_MENUSYSTEM_H_SWITCHPUK1_10,
	LANG_MENUSYSTEM_H_SWITCHPUK1_11,
	LANG_MENUSYSTEM_H_SWITCHPUK1_12,
	LANG_MENUSYSTEM_H_SWITCHPUK1_13,
	LANG_MENUSYSTEM_H_SWITCHPUK1_14,
	LANG_MENUSYSTEM_H_SWITCHPUK1_15,
	LANG_MENUSYSTEM_H_SWITCHPUK1_16,
	LANG_MENUSYSTEM_H_SWITCHPUK1_17,
	LANG_MENUSYSTEM_H_SWITCHPUK1_18,
	LANG_MENUSYSTEM_H_SWITCHPUK1_19,
	LANG_MENUSYSTEM_H_SWITCHPUK1_20,
	LANG_MENUSYSTEM_H_SWITCHPUK1_21,
	LANG_MENUSYSTEM_H_SWITCHPUK1_22,
	LANG_MENUSYSTEM_H_SWITCHPUK1_23,
	LANG_MENUSYSTEM_H_SWITCHPUK1_24,
	LANG_MENUSYSTEM_H_SWITCHPUK1_25,
	LANG_MENUSYSTEM_H_SWITCHPUK1_26,
	LANG_MENUSYSTEM_H_SWITCHPUK1_27,
};
char ShortCutStrActionPUK1[][51]={
	LANG_MENUSYSTEM_H_ACTIONPUK1_1,
	LANG_MENUSYSTEM_H_ACTIONPUK1_2,
	LANG_MENUSYSTEM_H_ACTIONPUK1_3,
	LANG_MENUSYSTEM_H_ACTIONPUK1_4,
	LANG_MENUSYSTEM_H_ACTIONPUK1_5,
	LANG_MENUSYSTEM_H_ACTIONPUK1_6,
	LANG_MENUSYSTEM_H_ACTIONPUK1_7,
	LANG_MENUSYSTEM_H_ACTIONPUK1_8,
	LANG_MENUSYSTEM_H_ACTIONPUK1_9,
	LANG_MENUSYSTEM_H_ACTIONPUK1_10,
	LANG_MENUSYSTEM_H_ACTIONPUK1_11,
	LANG_MENUSYSTEM_H_ACTIONPUK1_12,
	LANG_MENUSYSTEM_H_ACTIONPUK1_13,
	LANG_MENUSYSTEM_H_ACTIONPUK1_14,
	LANG_MENUSYSTEM_H_ACTIONPUK1_15,
	LANG_MENUSYSTEM_H_ACTIONPUK1_16,
};

char ShortCutStrWindowPUK2[][51]={
	LANG_MENUSYSTEM_H_WINDOWPUK2_1,
	LANG_MENUSYSTEM_H_WINDOWPUK2_2,
	LANG_MENUSYSTEM_H_WINDOWPUK2_3,
	LANG_MENUSYSTEM_H_WINDOWPUK2_4,
	LANG_MENUSYSTEM_H_WINDOWPUK2_5,
	LANG_MENUSYSTEM_H_WINDOWPUK2_6,
	LANG_MENUSYSTEM_H_WINDOWPUK2_7,
	LANG_MENUSYSTEM_H_WINDOWPUK2_8,
	LANG_MENUSYSTEM_H_WINDOWPUK2_9,
	LANG_MENUSYSTEM_H_WINDOWPUK2_10,
	LANG_MENUSYSTEM_H_WINDOWPUK2_11,
	LANG_MENUSYSTEM_H_WINDOWPUK2_12,
};
char ShortCutStrSwitchPUK2[][51]={
	LANG_MENUSYSTEM_H_SWITCHPUK2_1,
	LANG_MENUSYSTEM_H_SWITCHPUK2_2,
	LANG_MENUSYSTEM_H_SWITCHPUK2_3,
	LANG_MENUSYSTEM_H_SWITCHPUK2_4,
	LANG_MENUSYSTEM_H_SWITCHPUK2_5,
	LANG_MENUSYSTEM_H_SWITCHPUK2_6,
	LANG_MENUSYSTEM_H_SWITCHPUK2_7,
	LANG_MENUSYSTEM_H_SWITCHPUK2_8,
	LANG_MENUSYSTEM_H_SWITCHPUK2_9,
	LANG_MENUSYSTEM_H_SWITCHPUK2_10,
	LANG_MENUSYSTEM_H_SWITCHPUK2_11,
	LANG_MENUSYSTEM_H_SWITCHPUK2_12,
	LANG_MENUSYSTEM_H_SWITCHPUK2_13,
	LANG_MENUSYSTEM_H_SWITCHPUK2_14,
	LANG_MENUSYSTEM_H_SWITCHPUK2_15,
	LANG_MENUSYSTEM_H_SWITCHPUK2_16,
	LANG_MENUSYSTEM_H_SWITCHPUK2_17,
	LANG_MENUSYSTEM_H_SWITCHPUK2_18,
	LANG_MENUSYSTEM_H_SWITCHPUK2_19,
	LANG_MENUSYSTEM_H_SWITCHPUK2_20,
	LANG_MENUSYSTEM_H_SWITCHPUK2_21,
	LANG_MENUSYSTEM_H_SWITCHPUK2_22,
	LANG_MENUSYSTEM_H_SWITCHPUK2_23,
	LANG_MENUSYSTEM_H_SWITCHPUK2_24,
	LANG_MENUSYSTEM_H_SWITCHPUK2_25,
	LANG_MENUSYSTEM_H_SWITCHPUK2_26,
	LANG_MENUSYSTEM_H_SWITCHPUK2_27,
};
char ShortCutStrActionPUK2[][51]={
	LANG_MENUSYSTEM_H_ACTIONPUK2_1,
	LANG_MENUSYSTEM_H_ACTIONPUK2_2,
	LANG_MENUSYSTEM_H_ACTIONPUK2_3,
	LANG_MENUSYSTEM_H_ACTIONPUK2_4,
	LANG_MENUSYSTEM_H_ACTIONPUK2_5,
	LANG_MENUSYSTEM_H_ACTIONPUK2_6,
	LANG_MENUSYSTEM_H_ACTIONPUK2_7,
	LANG_MENUSYSTEM_H_ACTIONPUK2_8,
	LANG_MENUSYSTEM_H_ACTIONPUK2_9,
	LANG_MENUSYSTEM_H_ACTIONPUK2_10,
	LANG_MENUSYSTEM_H_ACTIONPUK2_11,
	LANG_MENUSYSTEM_H_ACTIONPUK2_12,
	LANG_MENUSYSTEM_H_ACTIONPUK2_13,
	LANG_MENUSYSTEM_H_ACTIONPUK2_14,
	LANG_MENUSYSTEM_H_ACTIONPUK2_15,
	LANG_MENUSYSTEM_H_ACTIONPUK2_16,
};
char ShortCutStrChat[][51]={
	"F1      :",                                                         //MLHIDE
	"F2      :",                                                         //MLHIDE
	"F3      :",                                                         //MLHIDE
	"F4      :",                                                         //MLHIDE
	"F5      :",                                                         //MLHIDE
	"F6      :",                                                         //MLHIDE
	"F7      :",                                                         //MLHIDE
	"F8      :",                                                         //MLHIDE
	"ctrl+F1 :",                                                         //MLHIDE
	"ctrl+F2 :",                                                         //MLHIDE
	"ctrl+F3 :",                                                         //MLHIDE
	"ctrl+F4 :",                                                         //MLHIDE
	"ctrl+F5 :",                                                         //MLHIDE
	"ctrl+F6 :",                                                         //MLHIDE
	"ctrl+F7 :",                                                         //MLHIDE
	"ctrl+F8 :",                                                         //MLHIDE
	"shift+F1:",                                                         //MLHIDE
	"shift+F2:",                                                         //MLHIDE
	"shift+F3:",                                                         //MLHIDE
	"shift+F4:",                                                         //MLHIDE
	"shift+F5:",                                                         //MLHIDE
	"shift+F6:",                                                         //MLHIDE
	"shift+F7:",                                                         //MLHIDE
	"shift+F8:",                                                         //MLHIDE
};

char ShortCutStrNum[][4]={
	{ 12, 27, 16, 24 },
	{ 12, 27, 16, 24 },
};

char (*ShortCutStr[][4])[51]={
	{
		&ShortCutStrWindowPUK1[0],
		&ShortCutStrSwitchPUK1[0],
		&ShortCutStrActionPUK1[0],
		&ShortCutStrChat[0],
	},
	{
		&ShortCutStrWindowPUK2[0],
		&ShortCutStrSwitchPUK2[0],
		&ShortCutStrActionPUK2[0],
		&ShortCutStrChat[0],
	}
};

enum{
	EnumShortCutStrWindow,
	EnumShortCutStrSwitch,
	EnumShortCutStrAction,
	EnumShortCutStrChat,
};

#define SHORTCUTSTR_POSX 31
#define SHORTCUTSTR_POSY 58
#define SHORTCUTSTR_HIGHT 16

#endif
