﻿// エラー信息

// #define definename 文章 ファイル名
#define ERRMSG_1	"无法打开cut地图文件"                 		"(1)"	// /nrcli2\puk2\map\newmap.cpp
#define ERRMSG_2	"无法大概aut文件"                     			"(2)"	// /nrcli2\puk2\map\newmap.cpp
#define ERRMSG_3	"GlobalAllocPtr Error ( DIRECT_DRAW )"			"(3)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_4	"DirectDrawCreateEx Error"            					"(4)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_5	"SetCooperativeLevel Error "          					"(5)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_6	"主操作界面创建失败"                  		"(6)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_7	"无法生成剪切板"                      				"(7)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_8	"无法生成缓存"                        			"(8)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_9	"SetCooperativeLevel Error "          					"(9)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_10	"主操作界面创建失败"                  		"(10)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_11	"无法生成缓存"                        			"(11)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_12	"GlobalAllocPtr Error ( PALDATA )"    				"(12)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_13	"Direct3D 对象创建失败"               			"(13)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_14	"3D设备创建失败"                      				"(14)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_15	"操作界面无法锁定"                    				"(15)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_16	"操作界面无法解锁"                    			"(16)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_17	"操作界面无法锁定"                    				"(17)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_18	"操作界面无法解锁"                    			"(18)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_19	"操作界面无法锁定"                    				"(19)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_20	"操作界面无法解锁"                    			"(20)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_21	"操作界面无法锁定"                    				"(21)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_22	"操作界面无法解锁"                    			"(22)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_23	"操作界面无法锁定"                    				"(23)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_24	"操作界面无法解锁"                    			"(24)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_25	"操作界面无法锁定"                    				"(25)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_26	"操作界面无法解锁"                    			"(26)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_27	"操作界面无法锁定"                    				"(27)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_28	"操作界面无法解锁"                    			"(28)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_29	"操作界面无法锁定"                    				"(29)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_30	"操作界面无法解锁"                    			"(30)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_31	"操作界面无法锁定"                    				"(31)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_32	"操作界面无法解锁"                    			"(32)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_33	"操作界面无法锁定"                    				"(33)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_34	"操作界面无法解锁"                    			"(34)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_35	"操作界面无法锁定"                    				"(35)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_36	"操作界面无法解锁"                    			"(36)"	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define ERRMSG_37	"图形数据读取失败"                    			"(37)"	// /nrcli2\puk2\newDraw\newSprdisp.cpp
#define ERRMSG_38	"图形数据读取失败"                    			"(38)"	// /nrcli2\puk2\newDraw\newSprdisp.cpp
#define ERRMSG_39	"图形数据读取失败"                    			"(39)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_40	"显存不够"                            				"(40)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_41	"操作界面不够"                        					"(41)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_42	"操作界面不够"                        					"(42)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_43	"显存不够"                            				"(43)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_44	"操作界面不够"                        					"(44)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_45	"操作界面不够"                        					"(45)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_46	"图形数据读取失败"                    			"(46)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_47	"显存不够"                            				"(47)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_48	"操作界面不够"                        					"(48)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_49	"操作界面不够"                        					"(49)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_50	"图形数据读取失败"                    			"(50)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_51	"图形数据读取失败"                    			"(51)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_52	"图形数据读取失败"                    			"(52)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_53	"图形数据读取失败"                    			"(53)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_54	"显存不够"                            				"(54)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_55	"操作界面不够"                        					"(55)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_56	"操作界面不够"                        					"(56)"	// /nrcli2\puk2\newDraw\newsprmgr.cpp
#define ERRMSG_57	"GetAction Erorr!!"                   						"(57)"	// /nrcli2\system\action.cpp
#define ERRMSG_58	"内存不足！pAct"                      			"(58)"	// /nrcli2\system\action.cpp
#define ERRMSG_59	"内存不足！yobi"                      			"(59)"	// /nrcli2\system\action.cpp
#define ERRMSG_60	"%s 读取错误"                         				"(60)"	// /nrcli2\system\battleMap.cpp
#define ERRMSG_61	"GlobalAllocPtr Error ( DIRECT_DRAW )"			"(61)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_62	"DirectDrawCreate Error"              					"(62)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_63	"QueryInterface Error"                					"(63)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_64	"SetCooperativeLevel Error"           					"(64)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_65	"主操作界面创建失败"                  		"(65)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_66	"无法生成剪切板"                      				"(66)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_67	"无法生成缓存"                        			"(67)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_68	"SetCooperativeLevel Error"           					"(68)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_69	"主操作界面创建失败"                  		"(69)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_70	"无法读取色盘文件"                    			"(70)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_71	"无法创建色盘"                        					"(71)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_72	"添加色盘失败"                        					"(72)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_73	"无法读取色盘文件"                    			"(73)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_74	"无法读取色盘文件"                    			"(74)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_75	"无法创建色盘"                        					"(75)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_76	"无法读取色盘文件"                    			"(76)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_77	"无法操作位图区域"                    			"(77)"	// /nrcli2\system\directDraw.cpp
#define ERRMSG_78	"%s 不是最新版本。"                   			"(78)"	// /nrcli2\system\filetbl.cpp
#define ERRMSG_79	"%s 版本异常。"                       				"(79)"	// /nrcli2\system\filetbl.cpp
#define ERRMSG_80	"%s 的大小不对。%u != %u\n" \
					"需要重新安装客户端" \
																			"(82)"	// /nrcli2\system\filetbl.cpp
#define ERRMSG_81	"DDraw初始化失败。"			"(81)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_82	"DDraw初始化失败。"			"(82)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_83	"DDraw初始化失败。"			"(83)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_84	"附属屏幕界面创建失败。"		"(84)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_85	"D3D初始化失败。"				"(85)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_86	"无法操作内存。"							"(86)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_87	"Graphic.bin 无法打开。"				"(87)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_88	"GraphicV3.bin 无法打开。"				"(98)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_89	"Graphic.bin 无法打开。"				"(89)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_90	"Graphic.bin 无法打开。"				"(80)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_91	SAVE_ERRMSG_loadNowState      							"(91)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_92	"Anime.bin 无法打开。"					"(92)"	// /nrcli2\system\gamemain.cpp
#define ERRMSG_93	"MAX_GRAPHICS=%d,Need=%d"       								"(93)"	// /nrcli2\system\loadrealbin.cpp
#define ERRMSG_94	"i=%d,animsize=%d "             								"(94)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_95	"%d / 1000 "                    									"(95)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_96	"i=%d,animsize=%d"              								"(96)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_97	"i=%d,j=%d,frameCnt=%d"         							"(97)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_98	"MaxBuffer=%d Need=%d"          							"(98)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_99	"无法操作内存。"                							"(99)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_100	"MaxBuffer=%d Need=%d"          							"(100)"	// /nrcli2\system\loadsprbin.cpp
#define ERRMSG_101	"DDraw初始化失败。"			"(101)"	// /nrcli2\system\main.cpp
#define ERRMSG_102	"DDraw初始化失败。"			"(102)"	// /nrcli2\system\main.cpp
#define ERRMSG_103	"DDraw初始化失败。"			"(103)"	// /nrcli2\system\main.cpp
#define ERRMSG_104	"D3D初始化失败。"				"(104)"	// /nrcli2\system\main.cpp
#define ERRMSG_105	"newest.txt读入缓存。"				"(105)"	// /nrcli2\system\netproc.cpp
#define ERRMSG_106	SAVE_ERRMSG_SIZE_ERR          							"(106)"	// /nrcli2\system\savedata.cpp
#define ERRMSG_107	SAVE_ERRMSG_VER_ERR           								"(107)"	// /nrcli2\system\savedata.cpp
#define ERRMSG_108	"自动生成用的操作界面生成失败。"	"(108)"	// /nrcli2\system\sprmgr.cpp
#define ERRMSG_109	"战斗操作界面生成失败。"				"(109)"	// /nrcli2\system\sprmgr.cpp
#define ERRMSG_110	"系统界面生成失败。"			"(110)"	// /nrcli2\system\sprmgr.cpp
#define ERRMSG_111	"界面生成失败。"					"(111)"	// /nrcli2\system\sprmgr.cpp
                                                      
#define ERRMSG_112	NET_ERRMSG_SOCKLIBERROR       							"(112)"	// /nrcli2\system\login.cpp
#define ERRMSG_113	NET_ERRMSG_NOTCONNECT         							"(113)"	// /nrcli2\system\login.cpp
#define ERRMSG_114	NET_ERRMSG_LOGINTIMEOUT       							"(114)"	// /nrcli2\system\login.cpp
#define ERRMSG_115	NET_ERRMSG_LOGOUTTIMEOUT      							"(115)"	// /nrcli2\system\login.cpp
#define ERRMSG_116	NET_ERRMSG_NEWEST_NON         							"(116)"	// /nrcli2\system\login.cpp
#define ERRMSG_117	NET_ERRMSG_NEWEST_NON         							"(117)"	// /nrcli2\system\login.cpp
#define ERRMSG_118	NET_ERRMSG_CLIENTVER_DIFF     								"(118)"	// /nrcli2\system\login.cpp




// 信息信息

#define INFOMSG_1	"要打开FAQ吗？"                                             				// /nrcli2\puk2\map\newmap.cpp
#define INFOMSG_2	"要打开FAQ吗？"                                             				// /nrcli2\puk2\map\newmap.cpp
#define INFOMSG_3	"游戏只能在16位色模式下执行。"                              	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_4	"游戏只能在16位色模式下执行。"                              	// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_5	"无法通过设定的画图功能启动。\n" \
				                                                          					// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_6	"无法通过设定的画图功能启动。\n" \
				                                                          					// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_7	"在这个画面模式下无法正确绘制。\n" \
				                                                          				// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_8	"在这个画面模式下无法正确绘制。\n" \
				                                                          					// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_9	"在这个画面模式下无法正确绘制。\n" \
				                                                          					// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_10	"在这个画面模式下无法正确绘制。\n" \
				                                                          					// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_11	"在这个画面模式下无法正确绘制。\n" \
				                                                          					// /nrcli2\puk2\newDraw\directdraw3D.cpp
#define INFOMSG_12	"要打开FAQ吗？"                                             				// /nrcli2\system\gamemain.cpp
#define INFOMSG_13	"文件已经损坏，需要一个新的启动程序。按“OK”查看详细说明。"
				                                                          									// /nrcli2\system\gamemain.cpp
#define INFOMSG_14	"无法重复启动魔力宝贝。"                                    		// /nrcli2\system\main.cpp
#define INFOMSG_15	"请通过CrossGate.exe启动游戏。"                             	// /nrcli2\system\main.cpp
                                                          
#define INFOMSG_16	"无法取得人物列表。"                                        		// /nrcli2\puk2\interface\loginCharaSel.cpp
#define INFOMSG_17	"现在服务器正在维护中。"                                    		// /nrcli2\puk2\interface\loginCharaSel.cpp
#define INFOMSG_18	"无法删除角色。"                                            		// /nrcli2\puk2\interface\loginCharaSel.cpp
#define INFOMSG_19	"无法正常安装。"                                            		// /nrcli2\puk2\interface\loginCharaSel.cpp
#define INFOMSG_20	"无法更新。"                                                			// /nrcli2\system\login.cpp
#define INFOMSG_21	"无法更新。"                                                		// /nrcli2\system\login.cpp
#define INFOMSG_22	"无法创建角色。"                                            		// /nrcli2\system\login.cpp
#define INFOMSG_23	"无法登入。"                                                			// /nrcli2\system\login.cpp
#define INFOMSG_24	"对不起，为了更好的游戏环境\n" \
					"服务器正在停机\n" \
					"请耐心等待服务器开启。"				// /nrcli2\system\login.cpp
#define INFOMSG_25	"IP地址已经从[%s]变成了[%s]。"                              	// /nrcli2\system\login.cpp
#define INFOMSG_26	"可能需要重新连接网络。"                                    	// /nrcli2\system\login.cpp
#define INFOMSG_27	NET_ERRMSG_ERR_CDKEY                                      				// /nrcli2\system\login.cpp
#define INFOMSG_28	NET_ERRMSG_ERR_EXPIRE                                     				// /nrcli2\system\login.cpp
#define INFOMSG_29	NET_ERRMSG_SERVER_BUSY                                    				// /nrcli2\system\login.cpp
