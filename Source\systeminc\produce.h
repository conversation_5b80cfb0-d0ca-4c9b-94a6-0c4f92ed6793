﻿/************************/
/*	produce.h			*/
/************************/
#ifndef _PRODUCE_H_
#define _PRODUCE_H_

// バトルサーフェスの演出の种类
enum{
	PRODUCE_UP_ACCELE,			// 上加速移动
	PRODUCE_UP_BRAKE,			// 上减速移动
	
	PRODUCE_DOWN_ACCELE,		// 下加速移动
	PRODUCE_DOWN_BRAKE,			// 下减速移动
	
	PRODUCE_LEFT_ACCELE,		// 左加速移动
	PRODUCE_LEFT_BRAKE,			// 左减速移动
	
	PRODUCE_RIGHT_ACCELE,		// 右加速移动
	PRODUCE_RIGHT_BRAKE,		// 右减速移动
	
	PRODUCE_LEFT_RIGHT_ACCELE,	// 左右加速移动
	PRODUCE_LEFT_RIGHT_BRAKE,	// 左右减速移动
	
	PRODUCE_UP_DOWM_ACCELE,		// 上下加速移动
	PRODUCE_UP_DOWM_BRAKE,		// 上下减速移动
	
	PRODUCE_UNERI_ACCELE,		// うねり加速移动
	PRODUCE_UNERI_BRAKE,		// うねり减速移动
	
	PRODUCE_UP_DOWN_LINE_ACCELE,// 上下ライン加速移动
	PRODUCE_UP_DOWN_LINE_BRAKE,	// 上下ライン减速移动

	PRODUCE_LINE_HAGARE_OCHI_OUT,	// ラインはがれＯＵＴ
	PRODUCE_LINE_HAGARE_OCHI_IN,	// ラインはがれＩＮ
	
	PRODUCE_4WAY_OUT,			// 四方向移动画面外へ
	PRODUCE_4WAY_IN,			// 四方向移动画面内へ
	
	PRODUCE_HAGARE_OUT,			// はがれ处理（消える）
	PRODUCE_HAGARE_IN,			// はがれ处理（出现する）
	
	PRODUCE_HAGARE_OCHI_OUT,	// はがれ落ち（消える）
	PRODUCE_HAGARE_OCHI_IN,		// はがれ落ち（出现する）
	
	PRODUCE_BRAN_SMALL,			// フスマ缩小
	PRODUCE_BRAN_BIG,			// フスマ扩大

	PRODUCE_BREAK_UP1,			//折りたたみ上アウト
	PRODUCE_BREAK_UP2,			//折りたたみ上イン

	PRODUCE_BREAK_UP3,			//折りたたみ上アウト
	PRODUCE_BREAK_UP4,			//折りたたみ下イン

	PRODUCE_BREAK_UP5,			//折りたたみ下アウト
	PRODUCE_BREAK_UP6,			//折りたたみ下イン

	PRODUCE_BREAK_UP7,			//折りたたみ下アウト
	PRODUCE_BREAK_UP8,			//折りたたみ上イン

	PRODUCE_CENTER_PRESSIN,		// 中央折りたたみイン
	PRODUCE_CENTER_PRESSOUT,	// 中央折りたたみアウト

	PRODUCE_END,				// 終了

};

// 演出番号
extern int ProduceNo;
// 演出初期化フラグ
extern int ProduceInitFlag;

// バトルサーフェスの画像作成 **************************************************/
void CopyBackBuffer( void );

// 演出描画 ********************************************************************/
#ifdef PUK2
BOOL DrawProduce( int no, char ad=0, float ac=0 );
#else
BOOL DrawProduce( int no );
#endif

// タイトル演出 ********************************************************************/
void TitleProduce( void );

#endif
