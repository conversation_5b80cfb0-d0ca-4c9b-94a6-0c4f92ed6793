﻿#include<windows.h>

#include"../systeminc/system.h"
#include"../systeminc/map.h"
#include"../systeminc/anim_tbl.h"
#include"../systeminc/menu.h"
#include"../systeminc/battleMenu.h"
#include"../systeminc/main.h"
//#include"../oft/work.h"
#include"../systeminc/mouse.h"
#include"../systeminc/sprdisp.h"
#include"../systeminc/mapGridCursol.h"
#include"../systeminc/keyboard.h"
#include"../systeminc/field.h"

#ifdef PUK2
//#define GID_PUK2_GRID_CURSOR CG_GRID_CURSOR
#define GID_PUK2_GRID_CURSOR 245401
#endif

int mouseMapGx, mouseMapGy;
int mouseMapX, mouseMapY;
short mouseCursorMode = MOUSE_CURSOR_MODE_NORMAL;

// マップ画面でのマウスボタン处理
BOOL mouseLeftClick = FALSE;
BOOL mouseLeftOn = FALSE;
BOOL mouseRightClick = FALSE;
BOOL mouseRightOn = FALSE;
unsigned int mouseLeftPushTime;
unsigned int beforeMouseLeftPushTime;

int gridCursorFlag;					// グリッドカーソルの通常处理の有无
int gridCursorDrawFlag;				// グリッドカーソルの表示の有无
int leftBtnOffFlag;					// 移动处理で左ボタンを押してない状态にする
int rightBtnOffFlag;				// 移动处理で右ボタンを押してない状态にする


///////////////////////////////////////////////////////////////////////////
// マップ画面にグリッドカーソルを表示する
//
void mapGridCursolProc( void )
{
	float x, y;
	int xx, yy;


	camGamenToMap( (float)mouse.nowPoint.x, (float)mouse.nowPoint.y, &x, &y );
	mouseMapX = (int)(x+.5);
	mouseMapY = (int)(y+.5);
	mouseMapGx = (mouseMapX+GRID_SIZE/2)/GRID_SIZE;
	mouseMapGy = (mouseMapY+GRID_SIZE/2)/GRID_SIZE;
	xx = mouseMapGx*GRID_SIZE;
	yy = mouseMapGy*GRID_SIZE;
	camMapToGamen( (float)xx, (float)yy, &x, &y );

	// fieldProc(); moveProc(); で参照されるのでそれまでに实行する
	mouseLeftClick = FALSE;
	mouseLeftOn = FALSE;
	mouseRightClick = FALSE;
	mouseRightOn = FALSE;

	// マウスカーソルがメニュー等の上にあるなら表示しない
	//if( mouse.nowPoint.y < 456
	if( mouse.nowPoint.y < DEF_APPSIZEY - 24
	 && ((mouse.level < DISP_PRIO_MENU && takeOnItemNo < 0)
	  || gridCursorDrawFlag
	  || onCursorFieldWin
	  || onCursorFieldInfoWin) )
	{
		// グリッドカーソルの表示
		if( mouseCursorMode == MOUSE_CURSOR_MODE_NORMAL )
		{
			// 通常のグリッドカーソル
#ifdef PUK2
			BLT_MEMBER bm={0};

			bm.rgba.rgba=0xFFFFFFFF;
			bm.bltf=BLTF_NOCHG;
			StockDispBuffer( (int)(x+.5), (int)(y+.5), DISP_PRIO_GRID, GID_PUK2_GRID_CURSOR, 0, &bm);
#else
			StockDispBuffer( (int)(x+.5), (int)(y+.5), DISP_PRIO_GRID, CG_GRID_CURSOR, 0 );
#endif
		}
		else
		{
			// 移动モードのグリッドカーソル
			//StockDispBuffer( (int)(x+.5), (int)(y+.5), DISP_PRIO_GRID, 1610, 0 );
		}
	}


	// マウスの检知
	//if( mouse.nowPoint.y < 456
	if( mouse.nowPoint.y < DEF_APPSIZEY - 24
	 && ((mouse.level < DISP_PRIO_MENU && takeOnItemNo < 0)
	 || gridCursorFlag
	 || onCursorFieldWin
	 || onCursorFieldInfoWin) )
	{
		if( (mouse.onceState & MOUSE_LEFT_CRICK)
		 && !leftBtnOffFlag )
		{
			mouseLeftClick = TRUE;
			mouseLeftPushTime = 0;
			beforeMouseLeftPushTime = GetTickCount();
		}
		if( (mouse.state & MOUSE_LEFT_CRICK)
		 && !leftBtnOffFlag )
		{
			mouseLeftOn = TRUE;
			if( beforeMouseLeftPushTime > 0 )
			{
				mouseLeftPushTime = GetTickCount() - beforeMouseLeftPushTime;
			}
		}
		else
		{
			mouseLeftPushTime = 0;
			beforeMouseLeftPushTime = 0;
		}
		if( (mouse.onceState & MOUSE_RIGHT_CRICK)
		 && !mouseRightClick )
		{
			mouseRightClick = TRUE;
		}
		if( (mouse.state & MOUSE_RIGHT_CRICK)
		 && !mouseRightClick )
		{
			mouseRightOn = TRUE;
		}
	}
	else
	{
#if !defined(PUK2_NEW_MENU)
		mouseCursorMode = MOUSE_CURSOR_MODE_NORMAL;
#endif
		mouseLeftPushTime = 0;
		beforeMouseLeftPushTime = 0;
	}
}
