﻿#define PUK2

#include "../mlstring/MlString.h"

#ifdef PUK2
	// バージョン管理
	#if defined(_DEBUG)||defined(_RELDEB)
		#define PUK2_NEWVER
	#endif
	#ifdef PUK2_NEWVER
		#define PUK2_NR_VERSION2 ".1108302300"
	#else
		#define PUK2_NR_VERSION2 ""
	#endif
	#define PUK2_NR_VERSION "Ver 1.02.02"PUK2_NR_VERSION2

	// ドメインのＩＰアドレスの设定が违っていた
	#define PUK2_IP_MISS

	// newestの设定
	#define PUK2_NEWESTSIZE 16384

	#if defined(_CGS) || defined(_CGL) || defined(_CGXL) || defined(_RELDEB)
		#define PUK2_IPCHANGE
	#endif
	#ifdef PUK2_IPCHANGE
		// ポンスビック外向け
		#define PUK2_DEBUGIP "**************"
		// ポンスビック社内向け
//			#define PUK2_DEBUGIP "*************"
	#endif
	#if defined(_CGXL)
		#define PUK2_NEWEST_SIZE_TEST
	#elif defined(_CGL) || defined(_RELDEB)
		#define PUK2_CUTNUWESTCHECK
	#elif defined(_CGS)
		#define PUK2_NOCUTNUWESTCHECK
	#endif

	
	// LoadBmp_PUK2() の読み込み方法变更用

	// ファイルから読み込む时にその絵のパレットを使用するなら定义する
	#define USEFILEPAL
	// 1 なら bin から、0 ならファイルから絵を読み込む
	#define BINLOAD 1
	// ファイルから読み込む时に 0 番の絵を読み込む先のサーフェース番号
	#define DEBUGBMPID 300000

	// 新メニュー（杉
	#define PUK2_NEW_MENU

	// 公会宠物バトル
	#define PUK2_GUILMON_BATTLE

	// オープニングＢＧＭ
	//#define PUK2_TITLEBGM_1	// ＩＤ入力可能になったら
	//#define PUK2_TITLEBGM_2	// タイトル画面最初から
	#define PUK2_TITLEBGM_3		// ロゴがくっついたとき

	// チャット新旧切换モード
	#define NewOrOldChatMode

	// 新ペットソート
	#define PUK2_NOPETSORT

	// メモリーリーク解消プログラム
	#define PUK2_MEMLEAK_LOGINOUT

	// 新スキル
	#define PUK2_NEWSKILL
	#ifdef PUK2_NEWSKILL
		#define PUK2_NEWSKILL_KAIKYOUKAISOU		// 戒骄戒躁
		#define PUK2_NEWSKILL_ICHIGEKIHICCHU	// 一击必中
		#define PUK2_NEWSKILL_DOKUGEKI			// 毒击
		#define PUK2_NEWSKILL_ISSEKINICHO		// 一石二鸟
		#define PUK2_NEWSKILL_KISHINOHOMARE		// 骑士ノ誉
		#define PUK2_NEWSKILL_JINNSOKUKADANN	// 迅速果断
		#define PUK2_NEWSKILL_YOTOKUNIKU		// 羊头狗肉
		#define PUK2_NEWSKILL_KYOSHIIJO			// 虚死为上
		#define PUK2_NEWSKILL_INNGAOHO			// 因果应报
		#define PUK2_NEWSKILL_TOPPU				// 突风
		#define PUK2_NEWSKILL_ACID				// アシッド
		#define PUK2_NEWSKILL_BARRIER			// バリア
	#endif

	// ペットのフィールド置き
	#define PetField

	// フィールドのキャラのソート(自分と同じプライオリティで描画)(被り物等に使用)
	#define PUK2_DRESS_UP

	// 等级最大上限变更
	#define PUK2_MAXLEVEL_UP

	// 旧オートマップ用色作成をＰＵＫ２絵のとき无视する
	#define PUK2_OLDAUTOMAP_SKIP

	// 振り向かないＮＰＣ
	#define PUK2_NOFLIP_NPC

	// 称号扩张
	#define PUK2_TITLE48

	// アルバム320
	#define ADD_ALBUM

	// スキルスロット上限变更
	#define PUK2_MAXSKILLSLOT_UP

	// ショップで登録アイテム移动する际に枠が表示されない
	#define PUK2_SHOPITEMMOVE

	// ＢＢＳの变更
	#define BBS_ESCAPE

	// 一石二鸟后ろの奴が变な方向向くバグ
	#define PUK2_ISSEKINICHO_BACK_ANG

	// アシッド关连の纸ふぶきのバグ修正
	#define PUK2_ACID_PAPER_SNOWSTORM

	// 転职アイテム对应
//	#define PUK2_JOB_CHANGE
	#define PUK2_JOB_CHANGE2

	// newestV2.txtへの变更
	#define PUK2_NEWEST_CHANGE

	//サーバーウインドウのクローズリクエスト
	#define SERVER_WIN_CLOSE

	// 护卫で敌が选べる不具合
	#define PUK2_BODYGUARD_FOR_ENEMY


	// 描画しないで軽くするプログラム
	#define PUK2_NOSHOW

	// ＢＧＭ??效果音を无くして軽くするプログラム
	#define PUK2_NOSOUNDMUSIC

	// ペット连れ歩きプロトコル变更
	#define PUK2_NEW_PETTAKE

	// キャラの行动が完全に終わってから次のターンへ
	#define PUK2_BATTLE_END

	// 战闘高速化
	#define PUK2_HIGHSPEED_BATTLE

	//ＩＭＥのカーソルを一番下までいけるようにする
	#define IME_CURSOR_NEW

	// 新ドラッグ处理
	#define PUK2_NEWDRAG

	// 属性效果表示２
	#define PUK2_TITILEELEMENTEFFECT_2

	// 等级下げアイテム
	#define PUK2_LV_LIMIT_ITEM

	// 描画机能切り替え案内
	#define PUK2_DEVICE_CHANGE

	// 召唤バグ修正
	#define PUK2_SUMMON_HIT

	//ＷＮプロトコルのifelseをswitvhに
	#define WN_PROC_SWITCH

	// 护卫のカウンター
	#define PUK2_BODYGUARD_COUNTER

	// 装饰スキルの时间がバラバラバグ
	#define PUK2_DECORATION_TIME

	// 战闘结果スキル经验值表示
	#define PUK2_RESULTSKILLEXP

	// キャラ作成の＋－押せないときボタン消す
	#define PUK2_CHARMAKE_PS

	// 描画モードの表示
	#define PUK2_3DDEVICE_DISP

	// スクリーンショットDirectDrawＯＦＦ时の对策
	#define PUK2_SCREENSHOT_DDOFF

	// ロシェ系でスキルエフェクトがでない症状修正
	#define PUK2_MAGIC_HIT

	// 盗むと他スキルを入れ替えたときの不具合
	#define PUK2_STEAL_REPLASE

	// エネミーリスト未終了の修正
	#define PUK2_BATTLE_ENEMYLIST_NOTEND

	// pallistオーバー时パレット化けの修正
	#define PUK2_PALLISTOVER

	// 敌倒したときのスローモーションでの不具合
	#define PUK2_BATTLE_SLOW

	// 行动顺番がバラバラのバグ
	#define PUK2_BATTLE_ACT_TURN

	// newest.txtの読み込みバッファオーバー０（改めてdefineできる）
	#define PUK2_NEWEST_BUFF_OVER_0

	// newest.txtの読み込みバッファオーバー
	#define PUK2_NEWEST_BUFF_OVER

	// 敌倒したときのスローモーションが終わらない
	#define PUK2_BATTLE_SLOW_NOTEND

	// ブーメランが变な起动を描く
	#define PUK2_BOOMERANG_ORBIT

	// 突风を自分に击ったとき
	#define PUK2_TOPPU_TO_ME

	// 一击必中の轨道の修正
	#define PUK2_ICHIGEKI_ORBIT

	// ブーメランの新轨道制御
	#define PUK2_BOOMERANG_ORBIT_1

//======================================
// 长期のプログラム
#ifdef _CGL

	// 描画モード变更ボタン
	#define PUK2_CHANGE_3DDEIVCE

	// 描画机能设定失败时の信息ボックスをＯＫ押さなくてもいいようにする
	#define PUK2_3DDEVICECHANGE_MISS_MSG

	// 描画机能切り替えを普通のウィンドウが表示されているとき禁止する
	#define PUK2_3DDEVICECHANGESTOPWINDOW

	// 描画モード切替での不具合
	#define PUK2_3DDEVICECHANGE_BATTLE

	// 新スキル
//	#define PUK2_NEWSKILL
	#ifdef PUK2_NEWSKILL
////		#define PUK2_NEWSKILL_KAIKYOUKAISOU		// 戒骄戒躁
////		#define PUK2_NEWSKILL_ICHIGEKIHICCHU	// 一击必中
////		#define PUK2_NEWSKILL_DOKUGEKI			// 毒击
////		#define PUK2_NEWSKILL_ISSEKINICHO		// 一石二鸟
////		#define PUK2_NEWSKILL_KISHINOHOMARE		// 骑士ノ誉
////		#define PUK2_NEWSKILL_JINNSOKUKADANN	// 迅速果断
////		#define PUK2_NEWSKILL_YOTOKUNIKU		// 羊头狗肉
////		#define PUK2_NEWSKILL_KYOSHIIJO			// 虚死为上
//		#define PUK2_NEWSKILL_JINNBAITTAI		// 人马一体
////		#define PUK2_NEWSKILL_INNGAOHO			// 因果应报
////		#define PUK2_NEWSKILL_TOPPU				// 突风
////		#define PUK2_NEWSKILL_ACID				// アシッド
////		#define PUK2_NEWSKILL_BARRIER			// バリア
	#endif

	//======================================
	// 超长期のプログラム
	#ifdef _CGXL

		// 描画レートの制御
		#define PUK2_FPS

		// サメの变色问题对应、同じ絵のパレット违いを别のサーフェースに
//		#define PUK2_DIFFPAL_SURFACE

	#endif
#endif
#ifdef _DEBUG
	#define LOOP_ERRMESSAGE_BREAK

	// 战闘中行动終了时、エネミーリストが終わってないならエラー表示
	#define BATTLE_ENEMYLIST_NOTEND

	#ifdef _NOWMAKING
		//======================================
		// 作成中のプログラム
		#include "../puk2/c_option.h"

		//======================================
		// デバッグ用プログラム

		//ウインドウの范围等を描画するフラグです。
		//リリース时はＯＦＦです。
		#define PUK2_NEW_WIN_DEBUG

		// デバッグ用描画关数
		#define PUK2_DEBUG_DRAW

		// 动作状况表示
		#define PUK2_PROC_USE_TIME

		// 战闘时属性表示
		#define PUK2_ELEMENTDISP

		// pallistオーバー时のエラー表示
		#define PUK2_PALLISTOVERMSG

		// 战闘のバグチェック用
//		#define PUK2_BATTLE_CHECK

		// メモリーリーク问题
//		#define PUK2_MEMCHECK
	#endif

#endif

	// ＰＵＫ３关系
	#include "../puk3/puk3.h"

#endif
