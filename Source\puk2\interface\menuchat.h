﻿
//チャットウインドウ

#ifndef _MENUCHAT_H_
#define _MENUCHAT_H_

#define GID_DUMMY_
#define GID_DUMMY_

#define NEW_CHAT_MAX_H		26
#define NEW_CHAT_MIN_H		 4
#define NEW_CHAT_MAX_W		37
#define NEW_CHAT_MIN_W		12
#define NEW_CHAT_FONT_KIND FONT_KIND_CHAT_S
#define NEW_CHAT_FONT_SIZE	FontKind[NEW_CHAT_FONT_KIND].zenkakuWidth

struct ChatWindowStatusStruct{
	int WidthSize;				//チャット文字数
	int WidthSizeChange;		//サイズ变更予约
	int HeightSize;				//チャット行数
	int HeightSizeChange;		//サイズ变更予约
	int DrawChatLine;			//书くための位置
	int StockLine;				//书いてよい范围
};

void InitMenuWindowChatInLogin(void);

BOOL MenuWindowChat( int mouse );
BOOL MenuWindowChatDraw( int mouse );

BOOL MenuSwitchChatDialog( int no, unsigned int flag );
BOOL MenuSwitchChatTextNum( int no, unsigned int flag );
BOOL MenuSwitchChange( int no, unsigned int flag );
BOOL MenuSwitchScrollBar( int no, unsigned int flag );
BOOL MenuSwitchChatScrollVar(int no, unsigned int flag );

BOOL MenuWindowOldChat( int mouse );
BOOL MenuWindowOldChatDraw( int mouse );

BOOL MenuSwitchChatScrollWheel( int no, unsigned int flag );

void MenuChatStockFontBuffInput(INPUT_STR *InputStrAdd);
void MenuDialogStockFontBuffInput(INPUT_STR *InputStrAdd);
BOOL SetDialogMenuChat(void);
void ChatReSize(void);

void SetInputStr(INIT_STR_STRUCT *InitStrStruct,int x,int y,int flag);
//チャットウインドウ制御
void ChatWindowLineUp(void);
void ChatWindowLineDown(void);
void ChatWindowStrUp(void);
void ChatWindowStrDown(void);
void ChatWindowChangeFontSize(void);
void InitMenuWindowChatStartExe(void);

extern INIT_STR_STRUCT InitStrStructChat;
extern int ChatWindowView;
//チャットの新旧モード
extern int ChatMode;


// チャットウインドウ
GRAPHIC_SWITCH MenuWindowChatGraph[]={
	{GID_ChatInputBox,0,0,0,0,0xFFFFFFFF},			//メニューバー左
	{GID_ChatBase    ,0,0,0,0,0xFFFFFFFF},			//メニューバー右

	{GID_ChatChangeHOn,0,0,0,0,0xFFFFFFFF},			//縦サイズ变更
	{GID_ChatWidth01On,0,0,0,0,0xFFFFFFFF},			//闻こえる范围变更
	{GID_ChatSizeLargeOn,0,0,0,0,0xFFFFFFFF},		//文字サイズ变更
	{GID_ChatColor00,0,0,0,0,0xFFFFFFFF},			//文字カラー变更
	{GID_ChatChangeWOn,0,0,0,0,0xFFFFFFFF},			//横サイズ变更
	{GID_ChatMode00,0,0,0,0,0xFFFFFFFF},			//入力モード

	{GID_ChatBackLeftOld,0,0,0,0,0x30FFFFFF},		//チャットバック下画像
	{GID_ChatBackBaseTop,0,0,0,0,0x30FFFFFF},		//チャットバック上画像


	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},				//スクロールバーツマミ
	{GID_ChatScrollBar,0,0,0,0,0xFFFFFFFF},			//スクロールバー
	{GID_ChatScrollBarTop,0,0,0,0,0xFFFFFFFF},		//スクロールバー上ボタン（Base）
	{GID_ChatScrollBarUnder,0,0,0,0,0xFFFFFFFF},	//スクロールバー下ボタン（Base）

	{GID_ChatDialogBack,0,0,0,0,0xA0FFFFFF},		//入力部分のバック

	{GID_UpButtonOff,0,0,0,0,0xFFFFFFFF},			//スクロールバー上ボタン
	{GID_DownButtonOff,0,0,0,0,0xFFFFFFFF},			//スクロールバー下ボタン

};

BUTTON_SWITCH MenuWindowChatButton[]={
	{0},								
};

TEXT_SWITCH MenuWindowChatText[]={
	{FONT_PAL_WHITE,FONT_KIND_CHAT_S,ML_STRING(824, "Ｈ大小")},	
};

// スイッチ
static SWITCH_DATA ChatSwitch[] = {

{ SWITCH_DIALOG,   0,  0,  0,  0, TRUE, NULL,	 MenuSwitchChatDialog },				//ダイアログ表示
	
{ SWITCH_CHATSTR,  0,  0,  0,  0, TRUE, NULL,	 MenuSwitchNone },						//过去ログ表示

{ SWITCH_NONE,	   0,  0,  0,   0, TRUE, NULL, MenuSwitchChatScrollWheel },				// マウスホイール判定

{ SWITCH_GRAPHIC,  7,  4-1, 18, 16, TRUE, &MenuWindowChatGraph[6], MenuSwitchChange },	//横サイズ变更
{ SWITCH_GRAPHIC, 27,  4-1, 18, 16, TRUE, &MenuWindowChatGraph[2], MenuSwitchChange },	//縦サイズ变更
{ SWITCH_GRAPHIC, 48,  4-1, 18, 16, TRUE, &MenuWindowChatGraph[3], MenuSwitchChange },	//闻こえる范围变更
{ SWITCH_GRAPHIC, 68,  4-1, 18, 16, TRUE, &MenuWindowChatGraph[4], MenuSwitchChange },	//文字サイズ变更
{ SWITCH_GRAPHIC, 87,  4-1, 18, 16, TRUE, &MenuWindowChatGraph[5], MenuSwitchChange },	//文字カラー变更

{ SWITCH_GRAPHIC,  0,  0, 11,  14, TRUE, &MenuWindowChatGraph[10], MenuSwitchNone },	//スクロールバーつまみ
//{ SWITCH_GRAPHIC,  0,  0, 14, 534, TRUE, &MenuWindowChatGraph[11], MenuSwitchChange },	//スクロールバー
{ SWITCH_BUTTON, 252-13,118-14,  11,100, TRUE, &MenuWindowChatButton[0], MenuSwitchChatScrollVar },	//スクロールバー(ドラッグ部分)

{ SWITCH_GRAPHIC,  0,  0, 14, 534, TRUE, &MenuWindowChatGraph[11], MenuSwitchChange },		//スクロールバー（画像）
{ SWITCH_GRAPHIC,  0,  0, 14,  16, TRUE, &MenuWindowChatGraph[15], MenuSwitchChange },		//スクロールバー上
{ SWITCH_GRAPHIC,  0,  0, 14,  16, TRUE, &MenuWindowChatGraph[16], MenuSwitchChange },		//スクロールバー下

{ SWITCH_GRAPHIC,  0,  0, 14,  16, TRUE, &MenuWindowChatGraph[12], MenuSwitchChange },		//スクロールバー上（Base）
{ SWITCH_GRAPHIC,  0,  0, 14,  16, TRUE, &MenuWindowChatGraph[13], MenuSwitchChange },		//スクロールバー下（Base）

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowChatGraph[0], MenuSwitchNone },			//入力部分及びメニュー左
{ SWITCH_GRAPHIC, 640-186,  0,   0,  0, TRUE, &MenuWindowChatGraph[1], MenuSwitchNone },	//入力部分及びメニュー右

{ SWITCH_GRAPHIC, 0, -370,   0,  0, TRUE, &MenuWindowChatGraph[8], MenuSwitchNone },		//バック画像下
{ SWITCH_GRAPHIC, 0, -370-16,   0,  0, TRUE, &MenuWindowChatGraph[9], MenuSwitchNone },		//バック画像上

{ SWITCH_GRAPHIC,  1,  1,  0,  0, TRUE, &MenuWindowChatGraph[14], MenuSwitchChange },		//入力部分バック

};

enum{

	EnumChatDialogInput,

	EnumChatOldMessageText00,

	EnumGraphChatScrollWheel,

	EnumChatGraphChangeWSize,
	EnumChatGraphChangeHSize,
	EnumChatGraphChangeListenSize,
	EnumChatGraphChangeFontSize,
	EnumChatGraphChangeColor,

	EnumChatGraphScrollBarTip,
	EnumChatBtScrollBar,

	EnumChatGraphScrollBar,
	EnumChatGraphScrollBarTopBt,
	EnumChatGraphScrollBarUnderBt,
	EnumChatGraphScrollBarTop,
	EnumChatGraphScrollBarUnder,

	EnumChatGraphMenuLeft,
	EnumChatGraphMenuRight,

	EnumChatGraphBack,
	EnumChatGraphBackTop,

	EnumChatGraphDialogBack,

	EnumChatEnd,
};


const WINDOW_DATA WindowDataChat = {
 0,															
     4,364,428,640, 25,0x80010101,  EnumChatEnd,  ChatSwitch, MenuWindowChat,MenuWindowChatDraw,MenuWindowDel
};


// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateChat={
	1,
	0,0,100,25
};

//-----------------------------------------------------------------------------------------------
//旧版チャット
//-----------------------------------------------------------------------------------------------

// スイッチ
static SWITCH_DATA OldChatSwitch[] = {

{ SWITCH_DIALOG,   0,  0,  0,  0, TRUE, NULL,	 MenuSwitchChatDialog },				//ダイアログ表示
	
{ SWITCH_CHATSTR,  0,  0,  0,  0, TRUE, NULL,	 MenuSwitchNone },						//过去ログ表示

};

enum{

	EnumOldChatDialogInput,

	EnumOldChatOldMessageText00,

	EnumOldChatEnd,
};


const WINDOW_DATA WindowDataOldChat = {
 0,															
     4, 0,428, 0, 0,0x80010101,  EnumOldChatEnd,  OldChatSwitch, MenuWindowOldChat,MenuWindowOldChatDraw,MenuWindowDel
};


#endif