﻿/************************/
/*	gamemain.cpp		*/
/************************/
#include "../systeminc/system.h"
#ifdef PUK3_MEMALLOCLOG
	#include <time.h>
	#include <direct.h>
#endif
#include <winuser.h>
#include <winbase.h>
#include "../systeminc/directDraw.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/process.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/netmain.h"
#include "../systeminc/savedata.h"
#include "../systeminc/produce.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/t_music.h"
#include "../systeminc/menu.h"
#include "../systeminc/handletime.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/mouse.h"
#include "../systeminc/math2.h"

#include "../systeminc/pc.h"
#include "../systeminc/map.h"
#include "../systeminc/keyboard.h"
#ifdef PUK2
	#include "../puk2/interface/menuwin.h"
	#include "../systeminc/debugLogWin.h"
#endif
#if defined(PUK2_MEMCHECK)||defined(PUK2_PROC_USE_TIME)
	#include <time.h>
#endif
#ifdef PUK2_SERVERCHANGE
	#include "../systeminc/netproc.h"
#endif
#if defined(PUK3_PUT_ON)||defined(PUK3_RIDE)
	#include "../systeminc/character.h"
#endif
#ifdef PUK2_DEBUG_DRAW
	#include "../systeminc/debug.h"
#endif

#ifdef _DEBUG
// フレームレート计算用
int	  FrameRate;			// フレームレート
int	  DrawFrameCnt;			// 一秒间に何枚描画したかを记忆
#ifdef PUK2_FPS
	int ProcRate;
	int ProcCnt;
#endif
DWORD DrawFrameTime;		// 一秒间を数えるカウンター
int testCnt;				// ほげカウンター
static char szMoji[ 256 ];	// 文字表示用

// バトルプロセス サブプロセス番号
static char *battleStr[]={
	"０：初始化",                                                             //MLHIDE
	"１：进入战斗",                                                            //MLHIDE
	"２：等到ＢＣ受信",                                                          //MLHIDE
	"３：登场",                                                              //MLHIDE
	"４：输入指令",                                                            //MLHIDE
	"５：等待动画受信",                                                          //MLHIDE
	"６：处理动画",                                                            //MLHIDE
	"７：结束演出初始化",                                                         //MLHIDE
	"８：演出结束",                                                            //MLHIDE
};

// 时间带表示文字列
static char *timeZoneStr[] = { "白天", "黄昏", "夜晚", "早上" };              //MLHIDE

#ifdef PUK3_MEMALLOC_LIMIT
	#define ALLOC_LIMIT_FREE 0
	extern size_t allocLimit;
	extern size_t allocSize;
#endif
// 情报表示 *******************************************************************/
void DebugInfoDisp( void )
{
	int x = 8;
	int y = 8;
	char c = 0;

#if 0
	sprintf( szMoji,"ActionCnt        = %d", ActCnt );                   //MLHIDE
	// フォント情报をバッファに溜める
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"ActionStructSize = %d",sizeof( ACTION ) );          //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, FONT_KIND_SMALL2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"DispCnt          = %d", DispBuffer.DispCnt );       //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, FONT_KIND_BIG2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"DisplayBpp          = %d", displayBpp );            //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"SurfaceCnt       = %d",SurfaceCnt );                //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"VramSurfaceCnt   = %d",VramSurfaceCnt );            //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"SysramSurfaceCnt = %d",SysramSurfaceCnt );          //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"SurfaceUseCnt    = %d",SurfaceUseCnt );             //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"SearchPoint      = %d",SurfaceSearchPoint );        //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"SurfaceDispCnt   = %d",SurfaceDispCnt );            //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;c = 0;

	sprintf( szMoji,"SurfaceDate      = %d",SurfaceDate );               //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	//sprintf( szMoji,"FrameRate        = %d",FrameRate );
	//StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"HitDispNo        = %d",HitDispNo );                 //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"HitFontNo        = %d",HitFontNo );                 //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"MouseLevel       = %d",mouse.level );               //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"ProcNo           = %d",ProcNo );                    //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	// 战闘プロセスのとき
	if( ProcNo == PROC_BATTLE ){
		sprintf( szMoji,"SubProcNo        = %s",battleStr[ SubProcNo ] );   //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}else{
		sprintf( szMoji,"SubProcNo        = %d",SubProcNo );                //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}


	sprintf( szMoji,"MouseX           = %d",mouse.nowPoint.x );          //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"MouseY           = %d",mouse.nowPoint.y );          //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"TimeZone         = %d",nrTime.hour );               //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16; c = 0;

	sprintf( szMoji,"PalNo:MapPalNo   = %d",PalState.palNo, palNo );     //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"BattleMapNo      = %d",BattleMapNo );               //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"HogeCnt          = %d",testCnt );                   //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"EventEnemyFlag   = %d",eventEnemyFlag );            //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	if( pNowInputStr != NULL ){
		sprintf( szMoji,"MojiCnt          = %d",pNowInputStr->cnt );        //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

		sprintf( szMoji,"CursorByte       = %d",pNowInputStr->cursorByte ); //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}

	sprintf( szMoji,"CONTROL          = %d",VK[ VK_CONTROL ] );          //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;


	sprintf( szMoji,"BattleTurnNo     = %d",BattleCliTurnNo );           //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"BattleSvTurnNo   = %d",BattleSvTurnNo );            //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"BattleTurnRcvFlag= %d",BattleTurnReceiveFlag );     //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
#if 0
	// 観战の时
	if( BattleMyNo >= 20 ){
		sprintf( szMoji,"BattleTurnNo     = %d",BattleCliTurnNo );          //MLHIDE
	}else{
		sprintf( szMoji,"BattleTurnNo     = %d",BattleCliTurnNo + 1 );      //MLHIDE
	}
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
#endif
//	sprintf( szMoji,"selectPetNo[ 0 ] = %d",pc.selectPetNo[ 0 ] );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

//	sprintf( szMoji,"selectPetNo[ 1 ] = %d",pc.selectPetNo[ 1 ] );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

//	sprintf( szMoji,"selectPetNo[ 2 ] = %d",pc.selectPetNo[ 2 ] );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

//	sprintf( szMoji,"selectPetNo[ 3 ] = %d",pc.selectPetNo[ 3 ] );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

//	sprintf( szMoji,"selectPetNo[ 4 ] = %d",pc.selectPetNo[ 4 ] );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

//	sprintf( szMoji,"BattlePetStMenCnt= %d",BattlePetStMenCnt );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

//	sprintf( szMoji,"BattlePetReceiveFlag= %d",BattlePetReceiveFlag );
//	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
#else
	sprintf( szMoji,"アクション数　　　　　　　　= %d", ActCnt );                      //MLHIDE
	// フォント情报をバッファに溜める
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"アクション構造体サイズ　　　= %d",sizeof( ACTION ) );             //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"表示数　　　　　　　　　　　= %d", DispBuffer.DispCnt );          //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"画面モード　　　　　　　　　= %d", displayBpp );                  //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"Surface数　　　　　　　　= %d",SurfaceCnt );                 //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"ＶＲａｍSurface数　　　　= %d",VramSurfaceCnt );             //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"ＳｙｓＲａｍSurface数　　= %d",SysramSurfaceCnt );           //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"使用Surface数　　　　　　= %d",SurfaceUseCnt );              //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"表示Surface数　　　　　　= %d",SurfaceDispCnt );             //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"Surfaceキャッシュ位置　　= %d",SurfaceSearchPoint );         //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;c = 0;

	sprintf( szMoji,"Surface記憶時間　　　　　= %d",SurfaceDate );                //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	//sprintf( szMoji,"FrameRate        = %d",FrameRate );
	//StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"画像当たり判定番号　　　　　= %d",HitDispNo );                    //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"文字当たり判定番号　　　　　= %d",HitFontNo );                    //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"マウスの階層　　　　　　　　= %d",mouse.level );                  //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"プロセス番号　　　　　　　　= %d",ProcNo );                       //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	// 战闘プロセスのとき
	if( ProcNo == PROC_BATTLE ){
		sprintf( szMoji,"サブプロセス番号　　　　　　= %s",battleStr[ SubProcNo ] );      //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}else{
		sprintf( szMoji,"サブプロセス番号　　　　　　= %d",SubProcNo );                   //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}


	sprintf( szMoji,"マウスＸ座標　　　　　　　　= %d",mouse.nowPoint.x );             //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"マウスＹ座標　　　　　　　　= %d",mouse.nowPoint.y );             //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"ＮＲ時間　　　　　　　　　　= ＮＲ%d年%d月%d日%d時：%s",nrTime.year, nrTime.month, nrTime.day, nrTime.hour, timeZoneStr[ nrTimeZoneNo ] ); //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16; c = 0;

	//sprintf( szMoji,"パレット番号：マップパレット= %d",PalState.palNo );
	sprintf( szMoji,"MapPalNo：PalNo = %d：%d", palNo, PalState.palNo );   //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"バトルマップ番号　　　　　　= %d",BattleMapNo );                  //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	//sprintf( szMoji,"ホゲカウント　　　　　　　　= %d",testCnt );
	//StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"オートマップフラグ　　　　　= %d",autoMapOpenFlag );              //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"イベントプラグ　　　　　　　= %d",eventEnemyFlag );               //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	if( pNowInputStr != NULL ){
		sprintf( szMoji,"入力文字数　　　　　　　　　= %d",pNowInputStr->cnt );           //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

		sprintf( szMoji,"カーソル位置　　　　　　　　= %d",pNowInputStr->cursorByte );    //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}

	//sprintf( szMoji,"ＣＴＲＬキー　　　　　　　= %d",VK[ VK_CONTROL ] );
	//StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;


	sprintf( szMoji,"戦闘クライアントターン番号　= %d",BattleCliTurnNo );              //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"戦闘サーバーターン番号　　　= %d",BattleSvTurnNo );               //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"戦闘ターンレシーブフラグ　　= %d",BattleTurnReceiveFlag );        //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;

	sprintf( szMoji,"ＩＭＥハンドル：入力モード番号　= %d : %d", (DWORD)ImeInfo.hImc, ImeInfo.conversion ); //MLHIDE
	StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
#endif
#ifdef PUK3_MEMALLOC_LIMIT
	x = 320 + 8;
	y = 0 + 8;
	c = 1;

	if ( allocLimit == ALLOC_LIMIT_FREE ){
		sprintf( szMoji,"メモリ確保量= %10d/　　　free", allocSize );                //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}else{
		sprintf( szMoji,"メモリ確保量= %10d/%10d", allocSize, allocLimit );       //MLHIDE
		StockFontBuffer( x, y, FONT_PRIO_FRONT2, c++, szMoji, 0 );y += 16;
	}
#endif
}

// フレームレートの计算（外人が作成）
void DisplayFrameRate( void )
{
	// 一秒たったら更新
    if( GetTickCount() - DrawFrameTime >= 1000 ){

		// フレームレイトを记忆
        FrameRate = DrawFrameCnt;
#ifdef PUK2_FPS
		ProcRate = ProcCnt;
#endif
		// 今の时间を记忆
        DrawFrameTime = GetTickCount();
		// 一秒间に描画した枚数を初期化
        DrawFrameCnt = 0;
#ifdef PUK2_FPS
		ProcCnt = 0;
#endif
    }
}

// デバッグ用关数 **************************************************************/
void DebugProc( void )
{
void nrproto_IP_recv( int fd,char* ip ); /* ../doc/nrproto.html line 3420 */

static int palNo = 0;		// パレット番号
static int fontNo = 0;		// フォント番号

	// アニメーション确认プロセスの时拔ける
	if( ProcNo == PROC_ANIM_VIEW || ProcNo == PROC_SPR_VIEW ) return;
#ifdef PUK2
	if( ProcNo == PROC_G_ID_VIEW ) return;
#endif
#ifdef PUK3_RIDEBIN
	if( ProcNo == PROC_COORDINATE_MAKE ) return;
#endif

	// フォント变更
	if( VK[ VK_CONTROL ] & KEY_ON && VK[ VK_F9 ] & KEY_ON_ONCE ){
		fontNo++;
		if( fontNo >= 3 ) fontNo = 0;
		// フォントチェンジ
		InitFont( fontNo );
		// ＩＰアドレス受信プロトコル
		//nrproto_IP_recv( sockfd,"*************" );
		// アイテム使う音
		//play_se( 212, 320, 240 );
	}else
	// パレット变更
	if( VK[ VK_F9 ] & KEY_ON_REP ){
	//if( joy_trg[ 1 ] & JOY_F8 ){	/* プラス */
		palNo++;
		if( palNo >= MAX_PAL ) palNo = 0;
		// パレットチェンジ
		PaletteChange( palNo, 30 );
		// ＩＰアドレス受信プロトコル
		//nrproto_IP_recv( sockfd,"*************" );
		// アイテム使う音
		//play_se( 212, 320, 240 );
	}

	// ショートカットキー
	if( VK[ VK_HOME ] & KEY_ON_ONCE ) StrToNowInputStr( DebugKey0 );
	if( VK[ VK_END ] & KEY_ON_ONCE ) StrToNowInputStr( DebugKey1 );
	if( VK[ VK_INSERT ] & KEY_ON_ONCE ) StrToNowInputStr( DebugKey2 );

	// フレームレートの计算（外人が作成）
	DisplayFrameRate();

	// 情报表示
	{
	#ifdef PUK2
		static int flag = 0;
	#else
		static int flag = 2;
	#endif
		// PageUp 押した时情报表示（トグル）
		if( VK[ VK_PRIOR ] & KEY_ON_ONCE ){
			if( ++flag >= 4 ) flag = 0;
		}
		if( flag == 1 || flag == 3 ) DebugInfoDisp();
		if( flag == 2 || flag == 3 ){
	#ifdef PUK2_FPS
			sprintf( szMoji,"fps:%3d/%3d",FrameRate, ProcRate );               //MLHIDE
			StockFontBuffer( DEF_APPSIZEX - 90, DEF_APPSIZEY - 40, FONT_PRIO_FRONT2, FONT_KIND_SMALL, FONT_PAL_WHITE, szMoji, 2 );
	#else
			sprintf( szMoji,"fps:%2d",FrameRate );                             //MLHIDE
			StockFontBuffer( DEF_APPSIZEX - 60, DEF_APPSIZEY - 40, FONT_PRIO_FRONT2, FONT_KIND_SMALL, FONT_PAL_WHITE, szMoji, 2 );
	#endif
		}
	}
}

#endif


// システムログファイル名 ****************************************
#define SYSTEM_LOG_FILE_NAME "systemlog.txt"

// システムログファイル初期化 ********************
void InitSystemLogfile( void )
{
	FILE *fp;

	// ファイルの新规作成
	if( ( fp = fopen( SYSTEM_LOG_FILE_NAME, "w" ) ) != NULL ){           //MLHIDE
		// ファイル关闭
		fclose( fp );
	}
}

// システムログファイル书き込み *****************
void WriteSystemLogfile( char *str )
{
	FILE *fp;

	// 追加オープン
	if( ( fp = fopen( SYSTEM_LOG_FILE_NAME, "a" ) ) != NULL ){           //MLHIDE
		// 书き込み
		fprintf( fp, "%s\n", str );                                         //MLHIDE
		// ファイル关闭
		fclose( fp );
	}
}

#ifdef PUK3_MEMALLOCLOG

#define MEMORY_LOG_DIR_NAME "Mem"
#define MEMORY_LOG_FILE_NAME "%s\\memlog_%s_%03d.txt"

char memlogFile[MAX_PATH];

// メモリログファイル初期化 ********************
void InitMemoryLogfile( void )
{
	FILE *fp;
	struct tm *nowTime;
	time_t longTime;
	char date[11];
	int i;

	// 现在の日时を取得
	time( &longTime );
	nowTime = localtime( &longTime );
	sprintf( date, "%02d%02d%02d",                                       //MLHIDE
		 (nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday );

	// フォルダの作成
	_mkdir( MEMORY_LOG_DIR_NAME );

	for( i = 0; ; i++ ){
		// ファイルの存在チェック
		sprintf( memlogFile, MEMORY_LOG_FILE_NAME, MEMORY_LOG_DIR_NAME, date, i );

		// ファイルの新规作成
		if( ( fp = fopen( memlogFile, "r" ) ) != NULL ){                    //MLHIDE
			fclose( fp );
			// 存在してたので次へ
			continue;
		}

		// ファイルの新规作成
		if( ( fp = fopen( memlogFile, "w" ) ) != NULL ){                    //MLHIDE
			fprintf( fp, ">>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<\n" ); //MLHIDE
			fprintf( fp, ">> メモリログ 第 %2d 回起動 [ %02d/%02d/%02d ] <<\n",         //MLHIDE
				 i+1, (nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday );
			fprintf( fp, ">>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<\n" ); //MLHIDE
			// ファイル关闭
			fclose( fp );
		}
		break;
	}
}

// メモリログファイル书き込み *****************
void WriteMemoryLogfile( bool allocFlag,
	 void *pointer, int size, char *file, int line )
{
	FILE *fp;
	struct tm *nowTime;
	time_t longTime;
	char *p, *pbf, *psrch;

	// 现在の日时を取得
	time( &longTime );
	nowTime = localtime( &longTime );

	// ファイル名の取得
	pbf = file;
	for( psrch = pbf = file; p = strstr( psrch, "nrcli2" ); pbf = p, psrch = p + 1 ); //MLHIDE

	// 追加オープン
	if( ( fp = fopen( memlogFile, "a" ) ) != NULL ){                     //MLHIDE
		fprintf( fp,
			 " %02d:%02d:%02d　"                                                //MLHIDE
			 "%s %9s 0x%08x %10d %10d %s\n",                                   //MLHIDE
			 nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec,
			 ( pointer ? "○" : "×" ), ( allocFlag ? "++ALLOC++" : "--FREE--" ), //MLHIDE
			 (int)pointer, size,
			 line, pbf
			);
		// ファイル关闭
		fclose( fp );
	}
}
#endif
#ifdef PUK2

// 文字列の取得( chatDebug()用 ) ++++
void Getstrline( char *str, char *bf, short bfq, int *cntr, char *end )
/***	引数の说明
	char *str	:	取得元の文字列
	char *bf	:	取得先バッファ
	short bfq	:	取得先バッファの大きさ
	int *cntr	:	取得元の文字列の现在位置へのポインタ
					※	关数呼出し后、終端文字の次の位置を指す值になる
					※	NULL 指定可、NULL を指定した场合、开始位置は取得元の文字列の先头
	char *end	:	終端文字
					※	" \n\t" なら ' '、'\n'、'\t'、'\0' が終端文字になる
***/
{
	int i,j,k;

	if (!cntr){
		cntr=&k;
		*cntr=0;
	}
	// 文字列の取得
	for(i=0;i<bfq;i++,(*cntr)++){
		for(j=0;;j++){
			if (str[*cntr]==end[j]){ bf[i]='\0';	(*cntr)++;	return; }
			if (end[j]=='\0') break;
		}
		bf[i]=str[*cntr];
	}
}

#ifdef _DEBUG

#ifdef PUK2
	extern unsigned char Pchgdata[][3];
	extern unsigned char Palchg_a_data[3];
	extern PALDATA PDpal[256];
#endif

#include "../systeminc/character.h"

#ifdef PUK2
	extern char device3D;		// ３Ｄデバイス选择フラグ
#endif
BOOL chatDebug_Draw( char *str, int *cnt )
{
	char s[100];
	int i,j,k;
	unsigned char f;

	static int palno=-1;
	unsigned char r,g,b;

	k = i = *cnt;

	// 最初の文字(' ' は无视)が '{' だったら先へ
	for(i;;i++){
		if (str[i]=='\0') return FALSE;
		if (str[i]==' ') continue;
		if (str[i]=='{'){ i++;	break; }
	}

	*cnt = k = i - 1;

	if ( !strchr(&str[k],'}') ) return FALSE;

	// 文字列外の ' ' は无视
	for(;;i++){ if (str[i]!=' ') break; }

	// 文字列の取得
	Getstrline( str, s, 100, &i, ":" );                                  //MLHIDE
	if (str[i-1]=='\0') return FALSE;

	f=0;
	// "newpal" だったら
	if ( !strcmp( s, "newpal" ) ){                                       //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		if (str[i-1]=='}') i--;
		// "on" だったら
		if ( strstr( s, "on" ) ) f=2;                                       //MLHIDE
		// "off" だったら
		if ( strstr( s, "off" ) ) f=1;                                      //MLHIDE
	}else if ( !strcmp( s, "newpalchg" ) ){                              //MLHIDE
		f=3;
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
		if (j<0) j=0;
		if (j>=255) j=255;
		r=(unsigned char)j;
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
		if (j<0) j=0;
		if (j>=255) j=255;
		g=(unsigned char)j;
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
		if (j<0) j=0;
		if (j>=255) j=255;
		b=(unsigned char)j;
	}else if ( !strcmp( s, "oldpalchg" ) ){                              //MLHIDE
		f=4;
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
		if (j<0) j=0;
		if (j>=255) j=255;
		r=(unsigned char)j;
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
	}else if ( !strcmp( s, "3Ddevice" ) ){                               //MLHIDE
		f=5;
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
	}else if ( !strcmp( s, "now3Ddevice" ) ){                            //MLHIDE
		f=6;
	}else{
		return FALSE;
	}

	switch(f){
	case 1: Palchg_not();	break;
	case 2:
		j=PalState.palNo%6;
		// 整数パレット
		Palchg_a_data[0]=Pchgdata[j][0];
		Palchg_a_data[1]=Pchgdata[j][1];
		Palchg_a_data[2]=Pchgdata[j][2];
		Palchg_auto();
		break;
	case 3:
		Palchg_manual( r, g, b );
		break;
	case 4:
		PalState.palNo=r;
		PalState.time=j;
		break;
	case 5: device3D = j;	break;
	case 6:
		if (device3D==0) StockChatBufferLine( "  現在の描画機能は０、魔力Ｓ５です。", FONT_PAL_GREEN2, FONT_KIND_CHAT_S ); //MLHIDE
		if (device3D==1) StockChatBufferLine( "  現在の描画機能は１です。", FONT_PAL_GREEN2, FONT_KIND_CHAT_S ); //MLHIDE
		if (device3D==2) StockChatBufferLine( "  現在の描画機能は２です。", FONT_PAL_GREEN2, FONT_KIND_CHAT_S ); //MLHIDE
		if (device3D==3) StockChatBufferLine( "  現在の描画機能は３です。", FONT_PAL_GREEN2, FONT_KIND_CHAT_S ); //MLHIDE
		if (device3D==4) StockChatBufferLine( "  現在の描画機能は４です。", FONT_PAL_GREEN2, FONT_KIND_CHAT_S ); //MLHIDE
		break;
	}
	if (f>1){
		if (f>1){
			for(i=0;i<256;i++){
				PDpal[i][2]=Palette[i].peRed;
				PDpal[i][1]=Palette[i].peGreen;
				PDpal[i][0]=Palette[i].peBlue;
			}
		}
	}
	// 絶对パレットチェンジするようにする
	PalState.count =palno;
	palno--;

	*cnt += (int)strchr( &str[k], '}' ) - (int)&str[k] + 1;

	return TRUE;
}

void nrproto_AC_send( int fd,int x,int y,int actionno );
void nrproto_ACS_send( int fd,int x,int y,int skillno );
#ifdef PUK3_CHARALIST
	extern int tailCharObj;
	extern CHAROBJ charObj[];
	// キャラ管理テーブル
	enum
	{
		CHAROBJ_USE_FREE,
		CHAROBJ_USE_STAY,
		CHAROBJ_USE_VIEW
	};
#endif

BOOL chatDebug_Chara( char *str, int *cnt )
{
	char s[100];
	int i,j,k;
	unsigned char f;

	int no,ef;

	k = i = *cnt;

	// 最初の文字(' ' は无视)が '{' だったら先へ
	for(i;;i++){
		if (str[i]=='\0') return FALSE;
		if (str[i]==' ') continue;
		if (str[i]=='{'){ i++;	break; }
	}

	*cnt = k = i - 1;

	if ( !strchr(&str[k],'}') ) return FALSE;

	// 文字列外の ' ' は无视
	for(;;i++){ if (str[i]!=' ') break; }

	// 文字列の取得
	Getstrline( str, s, 100, &i, ":" );                                  //MLHIDE
	if (str[i-1]=='\0') return FALSE;

	f=0;
	// "act" だったら
	if ( !strcmp( s, "act" ) ){                                          //MLHIDE
		f=1;
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
		if (j<0) j=0;
		if (j>=255) j=255;
		no=j;
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		j=atoi(s);
		ef=j;
	}
	// "fukidashi" だったら
	else if ( !strcmp( s, "fukidashi" ) ){                               //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		// "on" だったら
		if ( strstr( s, "on" ) ) f=3;                                       //MLHIDE
		// "off" だったら
		if ( strstr( s, "off" ) ) f=2;                                      //MLHIDE
	}
	// "skillicon" だったら
	else if ( !strcmp( s, "skillicon" ) ){                               //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		ef=atoi(s);
		f = 4;
	}
#ifdef PUK3_PUT_ON
	// "puton" だったら
	else if ( !strcmp( s, "puton" ) ){                                   //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		ef=atoi(s);
		f = 5;
	}
#endif
#ifdef PUK3_RIDE
	// "petride" だったら
	else if ( !strcmp( s, "petride" ) ){                                 //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		ef=atoi(s);
		f = 6;
	}
#endif
#ifdef PUK3_VEHICLE
	// "ridevehicle" だったら
	else if ( !strcmp( s, "ridevehicle" ) ){                             //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		no=atoi(s);
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		ef=atoi(s);
		f = 7;
	}
#endif
	// "warp" だったら
	else if ( !strcmp( s, "warp" ) ){                                    //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		no=atoi(s);
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		ef=atoi(s);
		f = 8;
	}
#ifdef PUK3_CHARALIST
	// "charainfo" だったら
	else if ( !strcmp( s, "charainfo" ) ){                               //MLHIDE
		f = 9;
	}
#endif
#ifdef PUK3_NOEXISTCHARA
	// "charadel" だったら
	else if ( !strcmp( s, "charadel" ) ){                                //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		no=atoi(s);
		f = 10;
	}
	// "charadelact" だったら
	else if ( !strcmp( s, "charadelact" ) ){                             //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		no=atoi(s);
		f = 11;
	}
#endif
	else{
		return FALSE;
	}

	switch(f){
	case 1:
		if ( pc.ptAct == NULL ) break;
#ifdef PUK3_RIDE		// ライドの移动テスト
		changePcAct( mapGx, mapGy, (int)pc.ptAct->anim_ang, no, ef, 0 );
#else
		changePcAct( mapGx, mapGy, (int)pc.ptAct->anim_ang, no, ef );
#endif
		break;
	case 2:
		CharFukidashiFlag = FALSE;
		break;
	case 3:
		CharFukidashiFlag = TRUE;
		break;
	case 4:
		ef=atoi(s);
		nrproto_ACS_send( sockfd, mapGx, mapGy, ef );
		break;
#ifdef PUK3_PUT_ON
	case 5:
		{
			CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;

			if (ext->ptPuton) DeathAction(ext->ptPuton);
			ext->ptPuton = GetAction( PRIO_CHR, NULL );

			if( ext->ptPuton == NULL ) break;

			// 实行关数
			ext->ptPuton->func = NULL;
			// グラフィックの番号
			ext->ptPuton->anim_chr_no = ef;
			// 动作番号
			ext->ptPuton->anim_no = ANIM_STAND;
			// 表示优先度
			ext->ptPuton->dispPrio = DISP_PRIO_CHAR;
			// 1行インフォ表示フラグ
			ext->ptPuton->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
		}
		break;
#endif
#ifdef PUK3_RIDE
	case 6:
		if ( pc.ptAct == NULL ) break;
		{
			CHAREXTRA *pYobi = (CHAREXTRA *)pc.ptAct->pYobi;

			// モンスターの作成
			if (pYobi->ptRide){
				DeathAction(pYobi->ptRide);
				pYobi->ptRide = NULL;
			}
			pYobi->ptRide = GetAction( PRIO_CHR, NULL );

			// 实行关数
			pYobi->ptRide->func = NULL;
			// グラフィックの番号
			pYobi->ptRide->anim_chr_no = ef;
			// 动作番号
			pYobi->ptRide->anim_no = ANIM_STAND;
			// 表示优先度
			pYobi->ptRide->dispPrio = DISP_PRIO_CHAR;
			// 1行インフォ表示フラグ
			pYobi->ptRide->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
#ifdef PUK3_RIDE_SOUND
			// ペット扱いで音鸣らさない
			pYobi->ptRide->atr |= ACT_ATR_TYPE_PET;
#endif

			// キャラの絵をライドキャラの絵に变更
			pc.ptAct->anim_chr_no = getRiderCharaGra(pc.graNo);

			pc.ptAct->actNo = CHARACT_RIDE_ON;
			pYobi->actCnt = 0;
		}
		break;
#endif
#ifdef PUK3_VEHICLE
	case 7:
		ACTION *pActVh;
		pActVh = createTestVehicleAction( no, mapGx, mapGy, (pc.ptAct?pc.ptAct->anim_ang:0) );
		setVehicleRider( pActVh, (ef&(1<<0)?pc.id:0), (ef&(1<<1)?pc.id:0), (ef&(1<<2)?pc.id:0), (ef&(1<<3)?pc.id:0), (ef&(1<<4)?pc.id:0) );
		break;
#endif
	case 8:
		setPcWarpPoint( no, ef );
		oldMapGx = mapGx;
		oldMapGy = mapGy;
		break;
#ifdef PUK3_CHARALIST
	case 9:
		{
			char buf[256];
			ACTION *pAct;
			for( i = 0; i < tailCharObj; i++ ){
				if ( charObj[i].use != CHAROBJ_USE_FREE ){
					sprintf( buf, "  %10d graNo %10d dir %d (%4d,%4d) %s",           //MLHIDE
						 i, charObj[i].graNo, charObj[i].dir, charObj[i].gx, charObj[i].gy, charObj[i].name );
					StockChatBufferLine( buf, FONT_PAL_GREEN2, FONT_KIND_CHAT_S );
					if ( charObj[i].ptAct != NULL ){
						pAct = charObj[i].ptAct;
						sprintf( buf, "    graNo %10d dir %d (%4d,%4d) disp(%4d,%4d) atr %08x %s", //MLHIDE
							 pAct->anim_chr_no, pAct->anim_ang, pAct->gx, pAct->gy,
							 pAct->x, pAct->y, pAct->atr, pAct->name );
						StockChatBufferLine( buf, FONT_PAL_GREEN2, FONT_KIND_CHAT_S );
					}else{
						sprintf( buf, "    graNo ---------- dir - (----,----) disp(----,----) atr -------- ----" ); //MLHIDE
						StockChatBufferLine( buf, FONT_PAL_GREEN2, FONT_KIND_CHAT_S );
					}
				}
			}
		}
		break;
#endif
#ifdef PUK3_NOEXISTCHARA
	case 10:
		delCharObj( charObj[no].id );
		break;
	case 11:
		DeathAction( charObj[no].ptAct );
		charObj[no].ptAct = NULL;
		charObj[no].stockDir = -1;
		break;
#endif
	}

	*cnt += (int)strchr( &str[k], '}' ) - (int)&str[k] + 1;

	return TRUE;
}

BOOL chatDebug_Battle( char *str, int *cnt )
{
	char s[100];
	int i,k;
	unsigned char f;
	unsigned char a;

	k = i = *cnt;

	// 最初の文字(' ' は无视)が '{' だったら先へ
	for(i;;i++){
		if (str[i]=='\0') return FALSE;
		if (str[i]==' ') continue;
		if (str[i]=='{'){ i++;	break; }
	}

	*cnt = k = i - 1;

	if ( !strchr(&str[k],'}') ) return FALSE;

	// 文字列外の ' ' は无视
	for(;;i++){ if (str[i]!=' ') break; }

	// 文字列の取得
	Getstrline( str, s, 100, &i, ":" );                                  //MLHIDE
	if (str[i-1]=='\0') return FALSE;

	f=0;
	// "elmdisp" だったら
	if ( !strcmp( s, "elmdisp" ) ){                                      //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		// "on" だったら
		if ( strstr( s, "on" ) ) f=2;                                       //MLHIDE
		// "off" だったら
		if ( strstr( s, "off" ) ) f=1;                                      //MLHIDE
	}
	// "efficacydisp" だったら
	else if ( !strcmp( s, "efficacydisp" ) ){                            //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }

		f = 3;
		a = 0;
	#ifdef PUK2_TITILEELEMENTEFFECT_2
		a |= 0x80;
	#endif
		for(;;){
			// 文字列の取得
			Getstrline( str, s, 100, &i, " }" );                               //MLHIDE
			if (str[i-1]=='\0') return FALSE;

			// "nomal" だったら
			if ( !strcmp( s, "nomal" ) ) a |= EFFICACYDISP_NOMAL;              //MLHIDE
			// "duel" だったら
			if ( !strcmp( s, "duel" ) ) a |= EFFICACYDISP_DUEL;                //MLHIDE
			// "boss" だったら
			if ( !strcmp( s, "boss" ) ) a |= EFFICACYDISP_BOSS;                //MLHIDE
	#ifdef PUK2_TITILEELEMENTEFFECT_2
			// "off" だったら
			if ( !strcmp( s, "off" ) ) a &= ~0x80;                             //MLHIDE
	#endif

			if (str[i-1]=='}') break;
		}
	}
	else{
		return FALSE;
	}

	switch(f){
	case 1:	CmdLineFlg&=~CMDLINE_ELMDISP;	break;
	case 2: CmdLineFlg|=CMDLINE_ELMDISP;	break;
#ifdef PUK2_TITILEELEMENTEFFECT_2
	case 3:	efficacydisp_2 = a;	break;
#else
	case 3:	efficacydisp = a;	break;
#endif
	}

	*cnt += (int)strchr( &str[k], '}' ) - (int)&str[k] + 1;

	return TRUE;
}


BOOL chatDebug_Window( char *str, int *cnt )
{
	char s[100];
	int i,k;
	unsigned char f;

	k = i = *cnt;

	// 最初の文字(' ' は无视)が '{' だったら先へ
	for(i;;i++){
		if (str[i]=='\0') return FALSE;
		if (str[i]==' ') continue;
		if (str[i]=='{'){ i++;	break; }
	}

	*cnt = k = i - 1;

	if ( !strchr(&str[k],'}') ) return FALSE;

	// 文字列外の ' ' は无视
	for(;;i++){ if (str[i]!=' ') break; }

	// 文字列の取得
	Getstrline( str, s, 100, &i, ":" );                                  //MLHIDE
	if (str[i-1]=='\0') return FALSE;

	f=0;
	// "range_window" だったら
	if ( !strcmp( s, "range_window" ) ){                                 //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		// "on" だったら
		if ( strstr( s, "on" ) ) f=2;                                       //MLHIDE
		// "off" だったら
		if ( strstr( s, "off" ) ) f=1;                                      //MLHIDE
	}
	// "range_switch" だったら
	else if ( !strcmp( s, "range_switch" ) ){                            //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		// "on" だったら
		if ( strstr( s, "on" ) ) f=4;                                       //MLHIDE
		// "off" だったら
		if ( strstr( s, "off" ) ) f=3;                                      //MLHIDE
	}
	// "range_move" だったら
	else if ( !strcmp( s, "range_move" ) ){                              //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		// "on" だったら
		if ( strstr( s, "on" ) ) f=6;                                       //MLHIDE
		// "off" だったら
		if ( strstr( s, "off" ) ) f=5;                                      //MLHIDE
	}else{
		return FALSE;
	}

	switch(f){
	case 1:	NewWinDebugFlag&=~NEW_WIN_DEBUG_WN;		break;
	case 2: NewWinDebugFlag|=NEW_WIN_DEBUG_WN;		break;
	case 3:	NewWinDebugFlag&=~NEW_WIN_DEBUG_SW;		break;
	case 4: NewWinDebugFlag|=NEW_WIN_DEBUG_SW;		break;
	case 5:	NewWinDebugFlag&=~NEW_WIN_DEBUG_MV;		break;
	case 6: NewWinDebugFlag|=NEW_WIN_DEBUG_MV;		break;
	}

	*cnt += (int)strchr( &str[k], '}' ) - (int)&str[k] + 1;

	return TRUE;
}

extern BOOL debugLogWindow;
extern int setInstallVersion;
#ifdef PUK3_WINDOW_SIZE_DIFF
	extern int WindowSize[2];
#endif

#define MAN_SIZE FONT_KIND_CHAT_S
#define MAN_COLOR FONT_PAL_GREEN2
BOOL chatDebug_Operate( char *str, int *cnt )
{
	char s[256];
	int i,k;
	unsigned char f;
	int page;
	int year, month, day, hour, min, sec;

	k = i = *cnt;

	// 最初の文字(' ' は无视)が '{' だったら先へ
	for(i;;i++){
		if (str[i]=='\0') return FALSE;
		if (str[i]==' ') continue;
		if (str[i]=='{'){ i++;	break; }
	}

	*cnt = k = i - 1;

	if ( !strchr(&str[k],'}') ) return FALSE;

	// 文字列外の ' ' は无视
	for(;;i++){ if (str[i]!=' ') break; }

	// 文字列の取得
	Getstrline( str, s, 100, &i, ":" );                                  //MLHIDE
	if (str[i-1]=='\0') return FALSE;

	f=0;
	// "man" だったら
	if ( !strcmp( s, "man" ) ){                                          //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		f = 1;
		page = atoi(s);
	}
	// "manver" だったら
	else if ( !strcmp( s, "manver" ) ){                                  //MLHIDE
		f=2;
	}
	// "logwindow" だったら
	else if ( !strcmp( s, "logwindow" ) ){                               //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		if (str[i-1]=='}') i--;
		// "on" だったら
		if ( strstr( s, "on" ) ){                                           //MLHIDE
			page = 1;
			f = 3;
		}
		// "off" だったら
		if ( strstr( s, "off" ) ){                                          //MLHIDE
			page = 0;
			f = 3;
		}
	}
	// "setbinver" だったら
	else if ( !strcmp( s, "setbinver" ) ){                               //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		f = 4;
		page = atoi(s);
	}
	// "settrial" だったら
	else if ( !strcmp( s, "settrial" ) ){                                //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		f = 5;
		page = atoi(s);
	}
	// "sort" だったら
	else if ( !strcmp( s, "sort" ) ){                                    //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		if (str[i-1]=='}'){
			page = -1;
			f = 6;
		}
		// "on" だったら
		else if ( strstr( s, "on" ) ){                                      //MLHIDE
			page = 1;
			f = 6;
		}
		// "off" だったら
		else if ( strstr( s, "off" ) ){                                     //MLHIDE
			page = 0;
			f = 6;
		}
		else return FALSE;
	}
	// "addtime" だったら
	else if ( !strcmp( s, "addtime" ) ){                                 //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		year = atoi(s);

		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		month = atoi(s);

		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		day = atoi(s);

		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		hour = atoi(s);

		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		min = atoi(s);

		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		sec = atoi(s);

		f = 7;
	}
	// "resettime" だったら
	else if ( !strcmp( s, "resettime" ) ){                               //MLHIDE
		f = 8;
	}
#ifdef PUK2_SERVERCHANGE
	// "changeserver" だったら
	else if ( !strcmp( s, "changeserver" ) ){                            //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, "}" );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		page = atoi(s);

		f = 9;
	}
#endif
#ifdef PUK3_MOUSECURSOR
	// "mousetype" だったら
	else if ( !strcmp( s, "mousetype" ) ){                               //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, "}" );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		page = atoi(s);

		f = 10;
	}
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
	// "winsize" だったら
	else if ( !strcmp( s, "winsize" ) ){                                 //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " " );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		year = atoi(s);

		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, "}" );                                 //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		month = atoi(s);

		f = 11;
	}
#endif
	// "segfault" だったら
	else if ( !strcmp( s, "segfault" ) ){                                //MLHIDE
		f = 12;
	}
#ifdef PUK3_MEMALLOC_LIMIT
	// "allocmiss" だったら
	else if ( !strcmp( s, "allocmiss" ) ){                               //MLHIDE
		// 文字列外の ' ' は无视
		for(;;i++){ if (str[i]!=' ') break; }
		// 文字列の取得
		Getstrline( str, s, 100, &i, " }" );                                //MLHIDE
		if (str[i-1]=='\0') return FALSE;
		// "on" だったら
		if ( strstr( s, "on" ) ){                                           //MLHIDE
			page = 1;
			f = 13;
		}
		// "off" だったら
		else if ( strstr( s, "off" ) ){                                     //MLHIDE
			page = 0;
			f = 13;
		}
		else return FALSE;
	}
#endif
	else{
		return FALSE;
	}

	switch(f){
	case 1:
		switch(page){
		case 0:
			// チャットに文字列を送る
			StockChatBufferLine( "{ man: (数)page }", MAN_COLOR, MAN_SIZE );    //MLHIDE
			StockChatBufferLine( "  チャットでデバッグのコマンドの説明を出す", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ manver: }", MAN_COLOR, MAN_SIZE );         //MLHIDE
			StockChatBufferLine( "  チャットでデバッグのバージョンを表示する", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ setbinver: (数)num }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  使用できるbinを錯覚させ、アップデート可能上限を変化させます", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  －２以下で現在の使用状況表示、－１で設定解除、０以上で指定した使用状況に設定します", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  又、現在の使用状況表示時、各使用状況に対応した数値が表示されます", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ settrial: (数)num }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  ゲーム中のみアップデート状态を錯覚させますが、マップ移動が必要です", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  －２以下で現在のバージョン表示、－１で設定解除、０以上で指定したアップデート状态に設定します", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  又、現在のバージョン表示時、各アップデート状态に対応した数値が表示されます", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ newpal: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  新式パレットチェンジのＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ newpalchg: (数)red (数)green (数)blue }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  新式パレットチェンジの色味指定", MAN_COLOR, MAN_SIZE );   //MLHIDE
			StockChatBufferLine( "{ oldpalchg: (数)palno (数)interval }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  旧式パレットチェンジ", MAN_COLOR, MAN_SIZE );        //MLHIDE
			StockChatBufferLine( "{ 3Ddevice: (数)no }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  ３Ｄデバイスの選択", MAN_COLOR, MAN_SIZE );         //MLHIDE
			StockChatBufferLine( "                                        ▼", MAN_COLOR, MAN_SIZE ); //MLHIDE
			break;
		case 1:
			StockChatBufferLine( "{ now3Ddevice: }", MAN_COLOR, MAN_SIZE );    //MLHIDE
			StockChatBufferLine( "  現在の描画機能の表示", MAN_COLOR, MAN_SIZE );        //MLHIDE
			StockChatBufferLine( "{ act: (数)action (数)effectno }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  changePcAct()関数を使用する", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ elmdisp: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  戦闘中の属性表示のＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE );   //MLHIDE
			StockChatBufferLine( "{ range_window: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  ウィンドウの範囲表示のＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ range_switch: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  スイッチの範囲表示のＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE );  //MLHIDE
			StockChatBufferLine( "{ range_move: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  つまみの範囲表示のＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE );   //MLHIDE
			StockChatBufferLine( "{ logwindow: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  ログウィンドウ表示のＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE );  //MLHIDE
			StockChatBufferLine( "{ sort: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  スキル?ペット?ペットスキルソートのＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ sort: }", MAN_COLOR, MAN_SIZE );           //MLHIDE
			StockChatBufferLine( "  スキル?ペット?ペットスキルソートの現在状态表示", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "                                        ▼", MAN_COLOR, MAN_SIZE ); //MLHIDE
			break;
		case 2:
			StockChatBufferLine( "{ efficacydisp: [nomal,duel,boss]... }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  戦闘時の属性効果表示の設定、複数指定する場合間にスペースが必要", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  記入したものは表示し、記入しないものは表示しなくなります。", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  nomal 通常戦闘時 duel デュエル時 boss ボス戦時", MAN_COLOR, MAN_SIZE ); //MLHIDE
#ifdef PUK2_TITILEELEMENTEFFECT_2
			StockChatBufferLine( "  off を指定した場合、属性効果表示の設定を元に戻します", MAN_COLOR, MAN_SIZE ); //MLHIDE
#endif
			StockChatBufferLine( "                                        終", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ fukidashi: (字)[on,off] }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  チャット吹出し表示のＯＮ／ＯＦＦ", MAN_COLOR, MAN_SIZE );  //MLHIDE
			StockChatBufferLine( "{ addtime: (数)year (数)month (数)day (数)hour (数)min (数)sec }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  クライアントのみ時間を変更する、サーバーの時間は変更されない", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  クロスゲート内での時間区分は以下の様", MAN_COLOR, MAN_SIZE ); //MLHIDE
			sprintf( s, "　　朝　%d～%d　昼　%d～%d　夕　%d～%d　夜　%d～%d",                   //MLHIDE
				NIGHT_TO_MORNING, MORNING_TO_NOON-1,	MORNING_TO_NOON, NOON_TO_EVENING-1,
				NOON_TO_EVENING, EVENING_TO_NIGHT-1,	EVENING_TO_NIGHT, NIGHT_TO_MORNING-1 );
			StockChatBufferLine( s, MAN_COLOR, MAN_SIZE );
			StockChatBufferLine( "{ resettime: }", MAN_COLOR, MAN_SIZE );      //MLHIDE
			StockChatBufferLine( "  addtimeによって変更した時間を元に戻す", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "{ skillicon: (数)num }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  頭の上のアイコン表示を変更する", MAN_COLOR, MAN_SIZE );   //MLHIDE
			StockChatBufferLine( "{ puton: (数)num }", MAN_COLOR, MAN_SIZE );   //MLHIDE
			StockChatBufferLine( "  物を被る", MAN_COLOR, MAN_SIZE );              //MLHIDE
			StockChatBufferLine( "{ petride: (数)num }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  ペットに乗ることができる", MAN_COLOR, MAN_SIZE );      //MLHIDE
			StockChatBufferLine( "                                        ▼", MAN_COLOR, MAN_SIZE ); //MLHIDE
			break;
		case 3:
#ifdef PUK3_VEHICLE
			StockChatBufferLine( "{ ridevehicle: (数)graNo (数)flag }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  乗り物に乗った状态を確認するためのオブジェクトを作成できる", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  flagはどの位置にキャラが乗っているかを示すフラグ", MAN_COLOR, MAN_SIZE ); //MLHIDE
#endif
			StockChatBufferLine( "{ warp: (数)x (数)y }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  サーバーを介さずにマップ内での座標を変更できる", MAN_COLOR, MAN_SIZE ); //MLHIDE
#ifdef PUK3_WINDOW_SIZE_DIFF
			StockChatBufferLine( "{ winsize: (数)x (数)y }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  クライアントウィンドウのサイズを変更することができる", MAN_COLOR, MAN_SIZE ); //MLHIDE
#endif
#ifdef PUK3_CHARALIST
			StockChatBufferLine( "{ charainfo: }", MAN_COLOR, MAN_SIZE );      //MLHIDE
			StockChatBufferLine( "  画面中に存在するキャラのリストを表示する", MAN_COLOR, MAN_SIZE ); //MLHIDE
#endif
#ifdef PUK3_NOEXISTCHARA
			StockChatBufferLine( "{ charadel: (数)no }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  画面中に存在するキャラを削除する", MAN_COLOR, MAN_SIZE );  //MLHIDE
			StockChatBufferLine( "{ charadelact: (数)no }", MAN_COLOR, MAN_SIZE ); //MLHIDE
			StockChatBufferLine( "  画面中に存在するキャラのACTIONを削除する", MAN_COLOR, MAN_SIZE ); //MLHIDE
#endif
			StockChatBufferLine( "{ segfault: }", MAN_COLOR, MAN_SIZE );       //MLHIDE
			StockChatBufferLine( "  不正終了します", MAN_COLOR, MAN_SIZE );           //MLHIDE
			break;
		default:
			StockChatBufferLine( "  manは、０～３ページです。", MAN_COLOR, MAN_SIZE );    //MLHIDE
			break;
		}
		break;
	case 2:
		StockChatBufferLine( "  man ver 13", MAN_COLOR, MAN_SIZE );         //MLHIDE
		break;
	case 3:
		if (page){
			if (!debugLogWindow){
				openLogWindow();
				debugLogWindow = TRUE;
				loadLogFile();
			}
		}else{
			if (debugLogWindow){
				closeLogWindow();
				debugLogWindow = FALSE;
			}
		}
		break;
	case 4:
		if ( -1 <= page && page <= 3 ) setInstallVersion = page;
		else{
			char *str[]={
				"　现在擬似使用可能binは設定されていません。",                                        //MLHIDE
				"　現在の擬似使用可能binは普通版です。",                                           //MLHIDE
				"　現在の擬似使用可能binはＥＸ版です。",                                           //MLHIDE
				"　現在の擬似使用可能binはＶ２版です。",                                           //MLHIDE
				"　現在の擬似使用可能binはＰＵＫ２版です。",                                         //MLHIDE
				"　現在の擬似使用可能binはＰＵＫ３版です。",                                         //MLHIDE
			};
			if ( -1 <= setInstallVersion && setInstallVersion <= 4 ){
				StockChatBufferLine( str[ setInstallVersion + 1 ], MAN_COLOR, MAN_SIZE );
			}else{
				StockChatBufferLine( "　现在擬似使用可能binは設定が異常です。", MAN_COLOR, MAN_SIZE ); //MLHIDE
			}
			StockChatBufferLine( "　　-1　　設定解除", MAN_COLOR, MAN_SIZE );          //MLHIDE
			StockChatBufferLine( "　　 0　　普通版", MAN_COLOR, MAN_SIZE );           //MLHIDE
			StockChatBufferLine( "　　 1　　ＥＸ版", MAN_COLOR, MAN_SIZE );           //MLHIDE
			StockChatBufferLine( "　　 2　　Ｖ２版", MAN_COLOR, MAN_SIZE );           //MLHIDE
			StockChatBufferLine( "　　 3　　ＰＵＫ２版", MAN_COLOR, MAN_SIZE );         //MLHIDE
			StockChatBufferLine( "　　 4　　ＰＵＫ３版", MAN_COLOR, MAN_SIZE );         //MLHIDE
		}
		break;
	case 5:
		switch(page){
		case 0:	CG2PackageVer = 0;	break;
		case 1:	CG2PackageVer = 3;	break;
		case 2:	CG2PackageVer = 5;	break;
		case 3:	CG2PackageVer = 7;	break;
		case 4:	CG2PackageVer = 8;	break;
		default:
			if ( page == -1 ){
				CG2PackageVer = 255;
			}else{
				char *str[]={
					"　現在の擬似トライアルは普通版です。",                                            //MLHIDE
					"　現在の擬似トライアルは普通版です。",                                            //MLHIDE
					"　現在の擬似トライアルは普通版です。",                                            //MLHIDE
					"　現在の擬似トライアルはＥＸ版です。",                                            //MLHIDE
					"　現在の擬似トライアルはＥＸ版です。",                                            //MLHIDE
					"　現在の擬似トライアルはＶ２版です。",                                            //MLHIDE
					"　現在の擬似トライアルはＶ２版です。",                                            //MLHIDE
					"　現在の擬似トライアルはＰＵＫ２版です。",                                          //MLHIDE
					"　現在の擬似トライアルはＰＵＫ３版です。",                                          //MLHIDE
				};
				if ( 0 <= CG2PackageVer && CG2PackageVer <= 8 ){
					StockChatBufferLine( str[ CG2PackageVer ], MAN_COLOR, MAN_SIZE );
				}else if ( CG2PackageVer == 255 ){
					StockChatBufferLine( "　现在擬似トライアルは設定されていません。", MAN_COLOR, MAN_SIZE ); //MLHIDE
				}else{
					StockChatBufferLine( "　现在擬似トライアルの設定が異常です。", MAN_COLOR, MAN_SIZE ); //MLHIDE
				}
				StockChatBufferLine( "　　-1　　設定解除", MAN_COLOR, MAN_SIZE );         //MLHIDE
				StockChatBufferLine( "　　 0　　普通版", MAN_COLOR, MAN_SIZE );          //MLHIDE
				StockChatBufferLine( "　　 1　　ＥＸ版", MAN_COLOR, MAN_SIZE );          //MLHIDE
				StockChatBufferLine( "　　 2　　Ｖ２版", MAN_COLOR, MAN_SIZE );          //MLHIDE
				StockChatBufferLine( "　　 3　　ＰＵＫ２版", MAN_COLOR, MAN_SIZE );        //MLHIDE
				StockChatBufferLine( "　　 4　　ＰＵＫ３版", MAN_COLOR, MAN_SIZE );        //MLHIDE
			}
			break;
		}
		break;
	case 6:
#ifdef _DEBUG
		if ( page == 1 ) sortOffFlag = 0;
		else if ( page == 0 ) sortOffFlag = 1;
		else{
			if (sortOffFlag) StockChatBufferLine( "　现在ソートはＯＦＦに設定されています。", MAN_COLOR, MAN_SIZE ); //MLHIDE
			else StockChatBufferLine( "　现在ソートはＯＮに設定されています。", MAN_COLOR, MAN_SIZE ); //MLHIDE
		}
		if ( page == 0 || page == 1 ){
			skillSort( &job.skill[0], &job.sortSkill[0], MAX_SKILL );
			petSort( &pet[0], &sortPet[0], MAX_PET );
			for(i=0;i<MAX_PET;i++) petTechSort( &pet[i].tech[0], &pet[i].sortTech[0], MAX_PET_TECH );
		}
#endif
		break;
	case 7:
#ifdef _DEBUG
		addDebugNRTime( year, month, day, hour, min, sec );
#endif
		break;
	case 8:
#ifdef _DEBUG
		clearDebugNRTime();
#endif
		break;
#ifdef PUK2_SERVERCHANGE
	case 9:
		selectServerIndex = page;
		GameState = GAME_LOGIN;
		ChangeProc2( PROC_CHAR_SERVER_CHANGE );
		break;
#endif
#ifdef PUK3_MOUSECURSOR
	case 10:
		mouse.type = page;
		break;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
	case 11:
		WindowSize[0] = year;
		WindowSize[1] = month;
		SetWindowPos( hWnd, HWND_NOTOPMOST, 0, 0, 0, 0,
			 SWP_NOMOVE | SWP_NOZORDER | SWP_FRAMECHANGED );
		break;
#endif
	case 12:
		{
			int *pi = NULL;
			*pi = 0xffffffff;
		}
		break;
#ifdef PUK3_MEMALLOC_LIMIT
	case 13:
		{
			static size_t allocLimitBack = ALLOC_LIMIT_FREE;

			if ( page == 1 ){
				allocLimitBack = allocLimit;
				allocLimit = 1;
			}else if ( page == 0 ){
				allocLimit = allocLimitBack;
			}
		}
		break;
#endif
	}

	*cnt += (int)strchr( &str[k], '}' ) - (int)&str[k] + 1;

	return TRUE;
}

#endif

// チャットでデバッグ ****************************************
void chatDebug( char *str )
{
#ifndef _DEBUG
	return;
#else
	int i;

	i=0;

	for(;;){
		if ( chatDebug_Draw( str, &i ) ) continue;
		if ( chatDebug_Chara( str, &i ) ) continue;
		if ( chatDebug_Battle( str, &i ) ) continue;
		if ( chatDebug_Window( str, &i ) ) continue;
		if ( chatDebug_Operate( str, &i ) ) continue;

		if ( str[i] == '{' ){
			if ( !strchr( &str[i], '}' ) ) break;
			i += (int)strchr( &str[i], '}' ) - (int)&str[i] + 1;
		}else{
			break;
		}
	}
#endif
}

#ifdef PUK2_DEBUG_DRAW

struct STRUCT_DEBUG_DRAW DebugDrawData[DEBUG_DRAW_MAX]={0};
int DebugDrawDataNum = 0;

// 线を描画する关数(3D使用时のみ有效)
void _Debug_Draw_Line( int x1, int y1, int x2, int y2, unsigned long rgba, int time )
{
	int i;

	// バッファが足りないなら終了
	if ( DebugDrawDataNum >= DEBUG_DRAW_MAX ) return;
	// 描画时间がないなら終了
	if ( time <= 0 ) return;

	// 3Dが使えないなら終了
	if ( !getUsable3D() ) return;

	// 空きを探す
	for(i=0;i<DEBUG_DRAW_MAX;i++){
		// 描画时间がないなら使ってない
		if ( DebugDrawData[i].time <= 0 ) break;
	}
	if ( !( i < DEBUG_DRAW_MAX ) ) return;

	// 设定する
	DebugDrawData[i].x1 = x1;
	DebugDrawData[i].y1 = y1;
	DebugDrawData[i].x2 = x2;
	DebugDrawData[i].y2 = y2;
	DebugDrawData[i].str[0] = '\0';
	DebugDrawData[i].rgba.rgba = rgba;
	DebugDrawData[i].time = time;
	DebugDrawData[i].type = DDT_LINE;
}

// 四角を描画する关数(不透明度が0xff以外のとき3Dのみ有效)
void _Debug_Draw_Rect( int l, int r, int t, int b, unsigned long rgba, int time )
{
	int i;

	// バッファが足りないなら終了
	if ( DebugDrawDataNum >= DEBUG_DRAW_MAX ) return;
	// 描画时间がないなら終了
	if ( time <= 0 ) return;

	// 不透明度が0xff以外のときで、3Dが使えないなら終了
	if ( (rgba&0xff000000)!=0xff000000 && !getUsable3D() ) return;

	// 空きを探す
	for(i=0;i<DEBUG_DRAW_MAX;i++){
		// 描画时间がないなら使ってない
		if ( DebugDrawData[i].time <= 0 ) break;
	}
	if ( !( i < DEBUG_DRAW_MAX ) ) return;

	// 设定する
	DebugDrawData[i].x1 = l;
	DebugDrawData[i].y1 = t;
	DebugDrawData[i].x2 = r;
	DebugDrawData[i].y2 = b;
	DebugDrawData[i].str[0] = '\0';
	DebugDrawData[i].rgba.rgba = rgba;
	DebugDrawData[i].time = time;
	DebugDrawData[i].type = DDT_RECT;
}

// 文字列を描画する关数(255文字まで有效)
void _Debug_Draw_String( char *str, int font, int x, int y, int rgb, int time )
{
	int i;

	// バッファが足りないなら終了
	if ( DebugDrawDataNum >= DEBUG_DRAW_MAX ) return;
	// 描画时间がないなら終了
	if ( time <= 0 ) return;

	// 空きを探す
	for(i=0;i<DEBUG_DRAW_MAX;i++){
		// 描画时间がないなら使ってない
		if ( DebugDrawData[i].time <= 0 ) break;
	}
	if ( !( i < DEBUG_DRAW_MAX ) ) return;

	// 设定する
	DebugDrawData[i].x1 = x;
	DebugDrawData[i].y1 = y;
	DebugDrawData[i].x2 = font;
	DebugDrawData[i].y2 = 0;
	strcpy( DebugDrawData[i].str, str );
	DebugDrawData[i].rgba.rgba = rgb;
	DebugDrawData[i].time = time;
	DebugDrawData[i].type = DDT_STRING;
}


void _Debug_Draw_DrawFuncLine( int num );
void _Debug_Draw_DrawFuncRect( int num );
void _Debug_Draw_DrawFuncString( int num );
// デバッグ描画データを描画する关数
void _Debug_Draw_DrawFunc()
{
	int i;

	if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();

	// 空きを探す
	for(i=0;i<DEBUG_DRAW_MAX;i++){
		// 描画时间がないなら使ってない
		if ( DebugDrawData[i].time <= 0 ) continue;
		DebugDrawData[i].time--;

		switch(DebugDrawData[i].type){
		case DDT_LINE:	_Debug_Draw_DrawFuncLine(i);	break;
		case DDT_RECT:	_Debug_Draw_DrawFuncRect(i);	break;
		case DDT_STRING:
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
			_Debug_Draw_DrawFuncString(i);
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
			break;
		}
	}
	if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();
}

#endif
#ifdef PUK2_PROC_USE_TIME

#define PROCUSETIME_MAX 100
DWORD ProcUseTime[PROCUSETIME_MAX];
unsigned long ProcUseTimeCol[PROCUSETIME_MAX];
int ProcUseTimeCnt;

void initProcUseTime()
{
	ProcUseTimeCnt = 0;
	SetProcPoint(0);
}

void SetProcPoint( unsigned long color )
{
	ProcUseTime[ProcUseTimeCnt] = timeGetTime();
	ProcUseTimeCol[ProcUseTimeCnt] = color;
	ProcUseTimeCnt++;
}

#ifdef PUK3_MEMORYLEAK
extern int memchecktimebf;
#endif
void Draw_ProcUseTime()
{
	int i, lf = 0, ri, mxtime;
	char s[256];

#ifdef PUK3_MEMORYLEAK
	mxtime = ProcUseTime[ProcUseTimeCnt-1] - ProcUseTime[0];
	sprintf( s, "%10d - %10d = %10d", ProcUseTime[ProcUseTimeCnt-1], ProcUseTime[0], mxtime ); //MLHIDE
	_Debug_Draw_String( s, 0, 0, 10, RGB(0,0,255), 1 );
	sprintf( s, "%10d - %10d = %10d", mxtime, memchecktimebf, mxtime - memchecktimebf ); //MLHIDE
	_Debug_Draw_String( s, 0, 0, 30, RGB(0,0,255), 1 );

	for(i=1;i<ProcUseTimeCnt;i++){
		if (mxtime){
			ri = ( DEF_APPSIZEX * (ProcUseTime[i] - ProcUseTime[0]) ) / mxtime;
		}else{
			ri = lf + DEF_APPSIZEX / (ProcUseTimeCnt-1);
			if ( ri > DEF_APPSIZEX ) ri = DEF_APPSIZEX;
		}
		if ( lf < ri ) _Debug_Draw_Rect( lf, ri, 0, 10, ProcUseTimeCol[i], 1 );
		if ( ri > DEF_APPSIZEX ) ri = DEF_APPSIZEX;
		lf = ri;
	}
	if (mxtime){
		ri = ( DEF_APPSIZEX * memchecktimebf) / mxtime;
		if ( ri > DEF_APPSIZEX ) ri = DEF_APPSIZEX;
		_Debug_Draw_Rect( 0, ri, 50, 60, 0xffffffff, 1 );
	}
#else
	mxtime = ProcUseTime[ProcUseTimeCnt-1] - ProcUseTime[0];
	sprintf( s, "%10d - %10d = %10d", ProcUseTime[ProcUseTimeCnt-1], ProcUseTime[0], mxtime ); //MLHIDE
	_Debug_Draw_String( s, 0, 0, 10, RGB(128,128,128), 1 );
	for(i=1;i<ProcUseTimeCnt;i++){
		if (mxtime){
			ri = ( DEF_APPSIZEX * (ProcUseTime[i] - ProcUseTime[0]) ) / mxtime;
		}else{
			ri = lf + DEF_APPSIZEX / (ProcUseTimeCnt-1);
			if ( ri > DEF_APPSIZEX ) ri = DEF_APPSIZEX;
		}
		if ( lf < ri ) _Debug_Draw_Rect( lf, ri, 0, 10, ProcUseTimeCol[i], 1 );
		lf = ri;
	}
#endif
}

#endif
#ifdef PUK3_GRAPH

#define GRAPH_TYPE 10
#define GRAPH_WIDTH 640
int GraphNum[GRAPH_TYPE][GRAPH_WIDTH];
unsigned long GraphColor[GRAPH_TYPE] = { 0xffffffff, 0xff000000, 0xffff0000 };

int GraphStartPoint = 0;

void NextGraph()
{
	int j;

	GraphStartPoint++;
	if ( GraphStartPoint >= GRAPH_WIDTH ) GraphStartPoint = 0;
	for( j = 0; j < GRAPH_TYPE; j++ ){
		GraphNum[j][GraphStartPoint] = 0;
	}
}

void SetGraphNum( int index, int num )
{
	int i;

	if ( index < 0 || GRAPH_TYPE <= index ) return;

	i = GraphStartPoint - 1;
	if ( i < 0 ) i += GRAPH_WIDTH;
	GraphNum[index][i] = num;
}

int GetGraphNum( int index )
{
	int i;

	if ( index < 0 || GRAPH_TYPE <= index ) return 0;

	i = GraphStartPoint - 1;
	if ( i < 0 ) i += GRAPH_WIDTH;
	return GraphNum[index][i];
}

void Draw_Graph()
{
	int a, b, i, j;

	for( j = 0; j < GRAPH_TYPE; j++ ){
		for( i = 0; i < GRAPH_WIDTH - 1; i++ ){
			a = GraphStartPoint + i;
			if ( a >= GRAPH_WIDTH ) a -= GRAPH_WIDTH;
			b = a + 1;
			if ( b >= GRAPH_WIDTH ) b -= GRAPH_WIDTH;

			_Debug_Draw_Line( i, 240 - GraphNum[j][a],
				 i+1, 240 - GraphNum[j][b],  GraphColor[j], 1 );
		}
	}
}

#endif

#ifdef PUK2_MEMCHECK

static void *memlist[MEMCHECK_BUF_NUM] = {0};
static enum MEMLISTTYPE memlistuse[MEMCHECK_BUF_NUM] = {MEMLISTTYPE_NONE};
static time_t memlisttime[MEMCHECK_BUF_NUM] = {0};

const char *memlisttypestr[] = {
	"    MEMLISTTYPE_NONE",                                              //MLHIDE

	// アクション周り
	"MEMLISTTYPE_ACTION",                                                //MLHIDE
	"MEMLISTTYPE_ACTIONYOBI",                                            //MLHIDE
	"MEMLISTTYPE_ACTIONANIMLIST",                                        //MLHIDE
	"MEMLISTTYPE_ACTIONFRAMELIST",                                       //MLHIDE
	"MEMLISTTYPE_ACTIONCDLIST",                                          //MLHIDE

	// 通信关系
	"MEMLISTTYPE_NRPROTOSTRINGWRAPPER",                                  //MLHIDE
	"MEMLISTTYPE_NRPROTOSTRINGWRAPPERDATA",                              //MLHIDE
	"MEMLISTTYPE_STRUCT_NRPROTO",                                        //MLHIDE

	// キャラ关系
	"MEMLISTTYPE_PC_FUKIDASHI",                                          //MLHIDE
	"MEMLISTTYPE_CHARA_FUKIDASHI",                                       //MLHIDE

	// 描画关系
	"MEMLISTTYPE_STRUCT_DIRECT_DRAW",                                    //MLHIDE
	"MEMLISTTYPE_RGNDATAHEADER",                                         //MLHIDE
	"MEMLISTTYPE_16BPP_PALTABLE",                                        //MLHIDE
	"MEMLISTTYPE_BITMAPMEMORY",                                          //MLHIDE
	"MEMLISTTYPE_PALLIST",                                               //MLHIDE
	"MEMLISTTYPE_PALDATA",                                               //MLHIDE
	"MEMLISTTYPE_SPRPAL_MASTER",                                         //MLHIDE
	"MEMLISTTYPE_SPRPAL",                                                //MLHIDE
	"MEMLISTTYPE_SPRITEDATA",                                            //MLHIDE
	"MEMLISTTYPE_SPRITE_INFO",                                           //MLHIDE
	"MEMLISTTYPE_ADRNBIN",                                               //MLHIDE
	"MEMLISTTYPE_BMPNUMTABLE",                                           //MLHIDE
	"MEMLISTTYPE_SCREENSHOTSRF",                                         //MLHIDE
	"MEMLISTTYPE_SCREENSHOTBUF",                                         //MLHIDE

	// 战闘周り
	"MEMLISTTYPE_BATTLE_MBBOX",                                          //MLHIDE
	"MEMLISTTYPE_BATTLE_MBBOXTEXT",                                      //MLHIDE

	// ウィンドウ基本部分
	"MEMLISTTYPE_WINDOW_INFO",                                           //MLHIDE
	"MEMLISTTYPE_SWITCH_INFO",                                           //MLHIDE
	"MEMLISTTYPE_SWITCH_DATA",                                           //MLHIDE
	"MEMLISTTYPE_SWITCH_TEXT",                                           //MLHIDE
	"MEMLISTTYPE_SPFONTBUFFER",                                          //MLHIDE

	// 各ウィンドウ
	"MEMLISTTYPE_GeneralWindow",                                         //MLHIDE
	"MEMLISTTYPE_GuilmonStatusWindow",                                   //MLHIDE

	// その他
	"MEMLISTTYPE_IME",                                                   //MLHIDE
	"MEMLISTTYPE_MOUSE_POINTLIST",                                       //MLHIDE
	"MEMLISTTYPE_LOADLOGFILE",                                           //MLHIDE
	"MEMLISTTYPE_TESTVIEW",                                              //MLHIDE
	"MEMLISTTYPE_UNPACK",                                                //MLHIDE
};

void memlistset( void *pointer, enum MEMLISTTYPE num )
{
	int i;

	if (!pointer) return;

	// すでに登録されていないかを确认
	for(i=0;i<1000;i++){
		if ( memlist[i] == pointer ) break;
	}

	// 登録されているなら
	if (i<1000){
		memlistuse[i] = num;
		time( &memlisttime[i] );
		return;
	}

	// 空きを搜す
	for(i=0;i<1000;i++){
		if ( !memlist[i] ) break;
	}

	// 空きが见つかったなら
	if (i<1000){
		memlist[i] = pointer;
		memlistuse[i] = num;
		time( &memlisttime[i] );
		return;
	}

	// 见つからなかったなら
	return;
}

void memlistrel( void *pointer, enum MEMLISTTYPE num )
{
	int i;

	if (!pointer) return;

	// 登録されているかを确认
	for(i=0;i<1000;i++){
		if ( memlist[i] == pointer ) break;
	}

	// 登録されているなら
	if (i<1000){
		if (memlistuse[i]!=num){
			if (WindowMode){
				char s[256];
				sprintf( s, "メモリー种类は、\'%s\'です。", memlisttypestr[num] );           //MLHIDE
				MessageBox( hWnd, s, "メモリー解放エラー", MB_OK );                        //MLHIDE
			}
		}
		time( &memlisttime[i] );
		memlistuse[i] = MEMLISTTYPE_NONE;
		return;
	}
}

void writememlist()
{
	FILE *fp;
	char s[256], s2[256];
	time_t ltime;
	struct tm *ldate;
	int i,j;
	int max, defmax;

	time( &ltime );
	ldate = localtime( &ltime );
	strftime( s, sizeof(s), "log\\memlist_%Y%m%d%H%M%S.txt", ldate );    //MLHIDE

	fp = fopen( s, "wt" );                                               //MLHIDE
	if (!fp) return;

	defmax = strlen(" メモリ种类 ");                                          //MLHIDE

	// 最大文字数を调べる
	max = defmax;
	for(i=0;i<MEMLISTTYPE_MAX;i++){
		j = strlen(memlisttypestr[i]);
		if ( j > max ) max = j;
	}

	// コメント
	fprintf( fp, " メモリ种类 " );                                            //MLHIDE
	for(i=defmax;i<max;i++) fputc( ' ', fp );
	fprintf( fp, "| アドレス番号 | 更新日時\n" );                                  //MLHIDE

	for(i=0;i<max;i++) fputc( '-', fp );
	j = strlen("| アドレス番号 | 更新日時\n");                                     //MLHIDE
	for(i=0;i<j;i++) fputc( '-', fp );
	fputc( '\n', fp );

	sprintf( s, "%%-%ds| %%12d | ", max );                               //MLHIDE
	for(i=0;i<MEMCHECK_BUF_NUM;i++){
		if ( memlistuse[i] == MEMLISTTYPE_NONE && memlist[i] == NULL ) break;

		if ( memlistuse[i] < MEMLISTTYPE_MAX ){
			fprintf( fp, s, memlisttypestr[ memlistuse[i] ], memlist[i] );
			ldate = localtime( &memlisttime[i] );
			strftime( s2, sizeof(s2), "%Y/%m/%d %H:%M %S", ldate );            //MLHIDE
			fprintf( fp, "%s\n", s2 );                                         //MLHIDE
		}
		else{
			fputs( "????\n", fp );                                             //MLHIDE
		}
	}

	fclose(fp);
}

#endif

#endif

