﻿/***************************************************************************
 *
 *  Copyright (C) 1998-1999 Microsoft Corporation.  All Rights Reserved.
 *
 *  File:       rmxfguid.h
 *
 *  Content:    Defines GUIDs of D3DRM's templates.
 *
 ***************************************************************************/

#ifndef __RMXFGUID_H_
#define __RMXFGUID_H_

/* {2B957100-9E9A-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMInfo,
0x2b957100, 0x9e9a, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3D82AB44-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMMesh,
0x3d82ab44, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3D82AB5E-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMVector,
0x3d82ab5e, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3D82AB5F-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMMeshFace,
0x3d82ab5f, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3D82AB4D-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMMaterial,
0x3d82ab4d, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {35FF44E1-6C7C-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialArray,
0x35ff44e1, 0x6c7c, 0x11cf, 0x8F, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {3D82AB46-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMFrame,
0x3d82ab46, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {F6F23F41-7686-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMFrameTransformMatrix,
0xf6f23f41, 0x7686, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {F6F23F42-7686-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMeshMaterialList,
0xf6f23f42, 0x7686, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {F6F23F40-7686-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMeshTextureCoords,
0xf6f23f40, 0x7686, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {F6F23F43-7686-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMeshNormals,
0xf6f23f43, 0x7686, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {F6F23F44-7686-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMCoords2d,
0xf6f23f44, 0x7686, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {F6F23F45-7686-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMatrix4x4,
0xf6f23f45, 0x7686, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {3D82AB4F-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMAnimation,
0x3d82ab4f, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3D82AB50-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMAnimationSet,
0x3d82ab50, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {10DD46A8-775B-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMAnimationKey,
0x10dd46a8, 0x775b, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {10DD46A9-775B-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMFloatKeys,
0x10dd46a9, 0x775b, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {01411840-7786-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialAmbientColor,
0x01411840, 0x7786, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {01411841-7786-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialDiffuseColor,
0x01411841, 0x7786, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {01411842-7786-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialSpecularColor,
0x01411842, 0x7786, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {D3E16E80-7835-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialEmissiveColor,
0xd3e16e80, 0x7835, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {01411843-7786-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialPower,
0x01411843, 0x7786, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {35FF44E0-6C7C-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMColorRGBA,
0x35ff44e0, 0x6c7c, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xA3);

/* {D3E16E81-7835-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMColorRGB,
0xd3e16e81, 0x7835, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {A42790E0-7810-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMGuid,
0xa42790e0, 0x7810, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {A42790E1-7810-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMTextureFilename,
0xa42790e1, 0x7810, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {A42790E2-7810-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMTextureReference,
0xa42790e2, 0x7810, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {1630B820-7842-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMIndexedColor,
0x1630b820, 0x7842, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {1630B821-7842-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMeshVertexColors,
0x1630b821, 0x7842, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {4885AE60-78E8-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMMaterialWrap,
0x4885ae60, 0x78e8, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {537DA6A0-CA37-11d0-941C-0080C80CFA7B} */
DEFINE_GUID(TID_D3DRMBoolean,
0x537da6a0, 0xca37, 0x11d0, 0x94, 0x1c, 0x0, 0x80, 0xc8, 0xc, 0xfa, 0x7b);

/* {ED1EC5C0-C0A8-11d0-941C-0080C80CFA7B} */
DEFINE_GUID(TID_D3DRMMeshFaceWraps,
0xed1ec5c0, 0xc0a8, 0x11d0, 0x94, 0x1c, 0x0, 0x80, 0xc8, 0xc, 0xfa, 0x7b);

/* {4885AE63-78E8-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMBoolean2d,
0x4885ae63, 0x78e8, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {F406B180-7B3B-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMTimedFloatKeys,
0xf406b180, 0x7b3b, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {E2BF56C0-840F-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMAnimationOptions,
0xe2bf56c0, 0x840f, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {E2BF56C1-840F-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMFramePosition,
0xe2bf56c1, 0x840f, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {E2BF56C2-840F-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMFrameVelocity,
0xe2bf56c2, 0x840f, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {E2BF56C3-840F-11cf-8F52-0040333594A3} */
DEFINE_GUID(TID_D3DRMFrameRotation,
0xe2bf56c3, 0x840f, 0x11cf, 0x8f, 0x52, 0x0, 0x40, 0x33, 0x35, 0x94, 0xa3);

/* {3D82AB4A-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMLight,
0x3d82ab4a, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3D82AB51-62DA-11cf-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMCamera,
0x3d82ab51, 0x62da, 0x11cf, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {E5745280-B24F-11cf-9DD5-00AA00A71A2F} */
DEFINE_GUID(TID_D3DRMAppData,
0xe5745280, 0xb24f, 0x11cf, 0x9d, 0xd5, 0x0, 0xaa, 0x0, 0xa7, 0x1a, 0x2f);

/* {AED22740-B31F-11cf-9DD5-00AA00A71A2F} */
DEFINE_GUID(TID_D3DRMLightUmbra,
0xaed22740, 0xb31f, 0x11cf, 0x9d, 0xd5, 0x0, 0xaa, 0x0, 0xa7, 0x1a, 0x2f);

/* {AED22742-B31F-11cf-9DD5-00AA00A71A2F} */
DEFINE_GUID(TID_D3DRMLightRange,
0xaed22742, 0xb31f, 0x11cf, 0x9d, 0xd5, 0x0, 0xaa, 0x0, 0xa7, 0x1a, 0x2f);

/* {AED22741-B31F-11cf-9DD5-00AA00A71A2F} */
DEFINE_GUID(TID_D3DRMLightPenumbra,
0xaed22741, 0xb31f, 0x11cf, 0x9d, 0xd5, 0x0, 0xaa, 0x0, 0xa7, 0x1a, 0x2f);

/* {A8A98BA0-C5E5-11cf-B941-0080C80CFA7B} */
DEFINE_GUID(TID_D3DRMLightAttenuation,
0xa8a98ba0, 0xc5e5, 0x11cf, 0xb9, 0x41, 0x0, 0x80, 0xc8, 0xc, 0xfa, 0x7b);

/* {3A23EEA0-94B1-11d0-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMInlineData,
0x3a23eea0, 0x94b1, 0x11d0, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {3A23EEA1-94B1-11d0-AB39-0020AF71E433} */
DEFINE_GUID(TID_D3DRMUrl,
0x3a23eea1, 0x94b1, 0x11d0, 0xab, 0x39, 0x0, 0x20, 0xaf, 0x71, 0xe4, 0x33);

/* {8A63C360-997D-11d0-941C-0080C80CFA7B} */
DEFINE_GUID(TID_D3DRMProgressiveMesh,
0x8A63C360, 0x997D, 0x11d0, 0x94, 0x1C, 0x0, 0x80, 0xC8, 0x0C, 0xFA, 0x7B);

/* {98116AA0-BDBA-11d1-82C0-00A0C9697271} */
DEFINE_GUID(TID_D3DRMExternalVisual,
0x98116AA0, 0xBDBA, 0x11d1, 0x82, 0xC0, 0x00, 0xA0, 0xC9, 0x69, 0x72, 0x71);

/* {7F0F21E0-BFE1-11d1-82C0-00A0C9697271} */
DEFINE_GUID(TID_D3DRMStringProperty, 
0x7f0f21e0, 0xbfe1, 0x11d1, 0x82, 0xc0, 0x0, 0xa0, 0xc9, 0x69, 0x72, 0x71);

/* {7F0F21E1-BFE1-11d1-82C0-00A0C9697271} */
DEFINE_GUID(TID_D3DRMPropertyBag, 
0x7f0f21e1, 0xbfe1, 0x11d1, 0x82, 0xc0, 0x0, 0xa0, 0xc9, 0x69, 0x72, 0x71);

// {7F5D5EA0-D53A-11d1-82C0-00A0C9697271}
DEFINE_GUID(TID_D3DRMRightHanded, 
0x7f5d5ea0, 0xd53a, 0x11d1, 0x82, 0xc0, 0x0, 0xa0, 0xc9, 0x69, 0x72, 0x71);

#endif /* __RMXFGUID_H_ */

