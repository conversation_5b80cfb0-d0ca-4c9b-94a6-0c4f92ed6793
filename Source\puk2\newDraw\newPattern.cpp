﻿/***************************************
			newPattern.cpp
***************************************/

#ifdef PUK2

// StockTaskDispBuffer 系以外の关数でキャラを描画する场合、描画する前、描画座标が确定した时点で呼び出す必要がある ++++
int Blt_adjust( ACTION *pAct, char xy )
{
	short dx,dy;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	realGetPos( pAct->bmpNo, &dx, &dy );

	if (xy==0) return(pAct->anim_x-dx);
	if (xy==1) return(pAct->anim_y-dy);

	return(0);
}

// このＩＤがアニメーションかをチェックする
BOOL CheckAnimetion( int graNo )
{
	if ( graNo < SPRSTART || graNo >= SPREND ) return FALSE;
	return TRUE;
}

#endif