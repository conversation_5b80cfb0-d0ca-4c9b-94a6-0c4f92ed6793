﻿/************************/
/*	radar.h				*/
/************************/
#ifndef _RADAR_
#define _RADAR_

void radar(ACTION *,int *,int *);

/********************************************************************
*
*      レーダー
*
*      IN     pAct   自分のアクションポインタ
*             x   	目标Ｘ座标
*             y   	目标Ｙ座标
*             cnt   方向变换カウンター
*
*      OUT    d1     距离  ( 0 ～ ??? )
*
*      コース( 0 ～ 31 )は a0->crs に入る 
*
********************************************************************/
int radar2(ACTION *pAct,int x,int y, int cnt );

#endif
