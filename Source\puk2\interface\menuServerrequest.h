﻿// サーバリクエスト(共通ウィンドウ)

#ifndef _MENUSERVERREQUEST_H_
#define _MENUSERVERREQUEST_H_

//====================================//
//		会话??医者ウィンドウ		  //
//====================================//

ACTION *openConversationWindow( short x, short y, short w, short h, int GraNo, int BottonType, int SeqNo, int ObjIndex, char *data );
ACTION *openInjuryWindow( short x, short y, short w, short h, int GraNo, int BottonType, int SeqNo, int ObjIndex, char *data );


//====================================//
//		マップ名ウィンドウ			  //
//====================================//

BOOL MenuMapNameHitCheck( int no, unsigned int flag );


TEXT_SWITCH MenuWindowMapNameText[]={
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,""},	
};

// スイッチ设定
static SWITCH_DATA MapNameSwitch[] = {
//type         , ofx, ofy, sx, sy, SprNum, graNo, view , text   ,  func

//ボタン
{ SWITCH_TEXT  ,   0,  44,  0,  0, TRUE, &MenuWindowMapNameText[0], MenuSwitchNone },
};

// スイッチ管理用enum
enum{
	EnumMapNameText = 0,

	EnumMenuMapNameEND,
};

// ウィンドウ处理关数 ++++
BOOL MenuWindowMapNameBf( int mouse );
BOOL MenuWindowMapNameAf( int mouse );

// ウインドウ设定
const WINDOW_DATA WindowDataMapName = {
//flag
 MENU_ATTR_NOMOVE|MENU_ATTR_NOHIT,										
     3, 196, 180,  256, 96, 0x80010101, EnumMenuMapNameEND,  MapNameSwitch, MenuWindowMapNameBf,MenuWindowMapNameAf,MenuWindowDel
};

//========================================
// 对象选择
//========================================

// ボタン处理关数 *********************//
BOOL MenuSwitchTargetSelCancel( int no, unsigned int flag );
BOOL MenuSwitchTargetSelTextButton( int no, unsigned int flag );

BOOL MenuSwitchTargetSelScrollUp( int no, unsigned int flag );
BOOL MenuSwitchTargetSelScrollDown( int no, unsigned int flag );
BOOL MenuSwitchTargetSelScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchTargetSelScrollRight( int no, unsigned int flag );
BOOL MenuSwitchTargetSelScrollWheel( int no, unsigned int flag );


BOOL MenuWindowTargetSelBf( int mouse );
BOOL MenuWindowTargetSelAf( int mouse );


GRAPHIC_SWITCH MenuWindowTargetSelGraph[]={
	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},			// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(下ボタン)

	{GID_CancelButtonOn,0,0,0,0,0xFFFFFFFF},	// キャンセルボタン

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(右ボタン)
};

BUTTON_SWITCH MenuWindowTargetSelButton[]={ {0,0} };

TEXT_SWITCH MenuWindowTargetSelText[]={
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_YELLOW,FONT_KIND_SMALL,ML_STRING(490, "请选择对象。")},
};


// スイッチ
static SWITCH_DATA TargetSelSwitch[] = {

{ SWITCH_GRAPHIC,270, 60,   0, 14, TRUE, &MenuWindowTargetSelGraph[0], MenuSwitchNone },				// スクロールバー(つまみ)
{ SWITCH_BUTTON, 270, 60,  11, 85, TRUE, &MenuWindowTargetSelGraph[0], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,270, 50,  11, 11, TRUE, &MenuWindowTargetSelGraph[1], MenuSwitchTargetSelScrollUp },	// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,270,144,  11, 11, TRUE, &MenuWindowTargetSelGraph[2], MenuSwitchTargetSelScrollDown },	// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,282, 47,  18, 18, TRUE, &MenuWindowTargetSelGraph[4], MenuSwitchTargetSelScrollLeft },	// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,282,140,  18, 18, TRUE, &MenuWindowTargetSelGraph[5], MenuSwitchTargetSelScrollRight },// スクロールバー(右ボタン)
{ SWITCH_NONE,	   0,  0, 320,192, TRUE, NULL, MenuSwitchTargetSelScrollWheel },						// マウスホイール判定

{ SWITCH_GRAPHIC,135,159,  49, 17, TRUE, &MenuWindowTargetSelGraph[3], MenuSwitchTargetSelCancel },		// キャンセルボタン

{ SWITCH_TEXT,	  70, 25,   0,  0, TRUE, &MenuWindowTargetSelText[5], MenuSwitchNone },					// 信息

{ SWITCH_TEXT,	  40, 55,   0,  0, TRUE, &MenuWindowTargetSelText[0], MenuSwitchNone },					// 选择对象
{ SWITCH_TEXT,	  40, 75,   0,  0, TRUE, &MenuWindowTargetSelText[1], MenuSwitchNone },					// 选择对象
{ SWITCH_TEXT,	  40, 95,   0,  0, TRUE, &MenuWindowTargetSelText[2], MenuSwitchNone },					// 选择对象
{ SWITCH_TEXT,	  40,115,   0,  0, TRUE, &MenuWindowTargetSelText[3], MenuSwitchNone },					// 选择对象
{ SWITCH_TEXT,	  40,135,   0,  0, TRUE, &MenuWindowTargetSelText[4], MenuSwitchNone },					// 选择对象

{ SWITCH_NONE,	  40, 55, 200, 13, TRUE, NULL, MenuSwitchTargetSelTextButton },							// 选择对象
{ SWITCH_NONE,	  40, 75, 200, 13, TRUE, NULL, MenuSwitchTargetSelTextButton },							// 选择对象
{ SWITCH_NONE,	  40, 95, 200, 13, TRUE, NULL, MenuSwitchTargetSelTextButton },							// 选择对象
{ SWITCH_NONE,	  40,115, 200, 13, TRUE, NULL, MenuSwitchTargetSelTextButton },							// 选择对象
{ SWITCH_NONE,	  40,135, 200, 13, TRUE, NULL, MenuSwitchTargetSelTextButton },							// 选择对象

};

enum{
	EnumTargetSelScrollGra,
	EnumTargetSelScroll,
	EnumTargetSelScrollUp,
	EnumTargetSelScrollDown,
	EnumTargetSelScrollLeft,
	EnumTargetSelScrollRight,
	EnumTargetSelScrollWheel,

	EnumTargetSelCancel,

	EnumTargetSelTargetInfo,

	EnumTargetSelTargetText0,
	EnumTargetSelTargetText1,
	EnumTargetSelTargetText2,
	EnumTargetSelTargetText3,
	EnumTargetSelTargetText4,

	EnumTargetSelTargetTextButton0,
	EnumTargetSelTargetTextButton1,
	EnumTargetSelTargetTextButton2,
	EnumTargetSelTargetTextButton3,
	EnumTargetSelTargetTextButton4,

	EnumTargetSelEnd,
};

const WINDOW_DATA WindowDataMenuTargetSel = {
 0,															// メニューウィンドウ
     4,  160, 125,320,192, 0x80010101,  EnumTargetSelEnd,  TargetSelSwitch, MenuWindowTargetSelBf,MenuWindowTargetSelAf,MenuWindowDel
};

ACTION *openTargetSelWindow( char *lTargetSelData, void (*lTargetSelReturnFuc)( int ret, char *str ) );



//========================================
// 对象选择１
//========================================

// ボタン处理关数 *********************//
BOOL MenuSwitchTargetSel1Cancel( int no, unsigned int flag );
BOOL MenuSwitchTargetSel1TextButton( int no, unsigned int flag );

BOOL MenuSwitchTargetSel1ScrollUp( int no, unsigned int flag );
BOOL MenuSwitchTargetSel1ScrollDown( int no, unsigned int flag );
BOOL MenuSwitchTargetSel1ScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchTargetSel1ScrollRight( int no, unsigned int flag );
BOOL MenuSwitchTargetSel1ScrollWheel( int no, unsigned int flag );


BOOL MenuWindowTargetSel1Bf( int mouse );
BOOL MenuWindowTargetSel1Af( int mouse );
BOOL closeTargetSel1Window();


GRAPHIC_SWITCH MenuWindowTargetSel1Graph[]={
	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},			// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(下ボタン)

	{GID_CancelButtonOn,0,0,0,0,0xFFFFFFFF},	// キャンセルボタン

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(右ボタン)
};

BUTTON_SWITCH MenuWindowTargetSel1Button[]={ {0,0} };

TEXT_SWITCH MenuWindowTargetSel1Text[]={
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_YELLOW,FONT_KIND_SMALL,ML_STRING(490, "请选择对象。")},
};


// スイッチ
static SWITCH_DATA TargetSel1Switch[] = {

{ SWITCH_GRAPHIC,270, 60,   0, 14, TRUE, &MenuWindowTargetSel1Graph[0], MenuSwitchNone },						// スクロールバー(つまみ)
{ SWITCH_BUTTON, 270, 60,  11, 85, TRUE, &MenuWindowTargetSel1Graph[0], MenuSwitchScrollBarV },					// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,270, 50,  11, 11, TRUE, &MenuWindowTargetSel1Graph[1], MenuSwitchTargetSel1ScrollUp },			// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,270,144,  11, 11, TRUE, &MenuWindowTargetSel1Graph[2], MenuSwitchTargetSel1ScrollDown },		// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,282, 47,  18, 18, TRUE, &MenuWindowTargetSel1Graph[4], MenuSwitchTargetSel1ScrollLeft },		// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,282,140,  18, 18, TRUE, &MenuWindowTargetSel1Graph[5], MenuSwitchTargetSel1ScrollRight },		// スクロールバー(右ボタン)
{ SWITCH_NONE,	   0,  0, 320,192, TRUE, NULL, MenuSwitchTargetSel1ScrollWheel },								// マウスホイール判定

{ SWITCH_GRAPHIC,135,159,  49, 17, TRUE, &MenuWindowTargetSel1Graph[3], MenuSwitchTargetSel1Cancel },			// キャンセルボタン

{ SWITCH_TEXT,	  70, 25,   0,  0, TRUE, &MenuWindowTargetSel1Text[1], MenuSwitchNone },						// 信息

{ SWITCH_TEXT,	  40, 55,   0,  0, TRUE, &MenuWindowTargetSel1Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40, 75,   0,  0, TRUE, &MenuWindowTargetSel1Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40, 95,   0,  0, TRUE, &MenuWindowTargetSel1Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40,115,   0,  0, TRUE, &MenuWindowTargetSel1Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40,135,   0,  0, TRUE, &MenuWindowTargetSel1Text[0], MenuSwitchNone },						// 选择对象

{ SWITCH_NONE,	  40, 55, 200, 13, TRUE, NULL, MenuSwitchTargetSel1TextButton },								// 选择对象
{ SWITCH_NONE,	  40, 75, 200, 13, TRUE, NULL, MenuSwitchTargetSel1TextButton },								// 选择对象
{ SWITCH_NONE,	  40, 95, 200, 13, TRUE, NULL, MenuSwitchTargetSel1TextButton },								// 选择对象
{ SWITCH_NONE,	  40,115, 200, 13, TRUE, NULL, MenuSwitchTargetSel1TextButton },								// 选择对象
{ SWITCH_NONE,	  40,135, 200, 13, TRUE, NULL, MenuSwitchTargetSel1TextButton },								// 选择对象

};

enum{
	EnumTargetSel1ScrollGra,
	EnumTargetSel1Scroll,
	EnumTargetSel1ScrollUp,
	EnumTargetSel1ScrollDown,
	EnumTargetSel1ScrollLeft,
	EnumTargetSel1ScrollRight,
	EnumTargetSel1ScrollWheel,

	EnumTargetSel1Cancel,

	EnumTargetSel1TargetInfo,

	EnumTargetSel1TargetText0,
	EnumTargetSel1TargetText1,
	EnumTargetSel1TargetText2,
	EnumTargetSel1TargetText3,
	EnumTargetSel1TargetText4,

	EnumTargetSel1TargetTextButton0,
	EnumTargetSel1TargetTextButton1,
	EnumTargetSel1TargetTextButton2,
	EnumTargetSel1TargetTextButton3,
	EnumTargetSel1TargetTextButton4,

	EnumTargetSel1End,
};

const WINDOW_DATA WindowDataMenuTargetSel1 = {
 0,															// メニューウィンドウ
     4,  160, 125,320,192, 0x80010101,  EnumTargetSel1End,  TargetSel1Switch, MenuWindowTargetSel1Bf,MenuWindowTargetSel1Af,closeTargetSel1Window
};

enum{
	TARGETSEL1_RETURN_CLOSE = -2,
	TARGETSEL1_RETURN_DEATHFUNC,

	TARGETSEL1_RETURN_TEXT = 0,
};

ACTION *openTargetSel1Window( char **SelStr, char *SelColor, int Num, BOOL (*ReturnFunc)( int ret, char *str ) );


//========================================
// 对象选择２
//========================================

BOOL MenuSwitchTargetSel2Cancel( int no, unsigned int flag );
BOOL MenuSwitchTargetSel2Back( int no, unsigned int flag );
BOOL MenuSwitchTargetSel2TextButton( int no, unsigned int flag );

BOOL MenuSwitchTargetSel2ScrollUp( int no, unsigned int flag );
BOOL MenuSwitchTargetSel2ScrollDown( int no, unsigned int flag );


BOOL MenuWindowTargetSel2Bf( int mouse );
BOOL MenuWindowTargetSel2Af( int mouse );
BOOL closeTargetSel2Window();

GRAPHIC_SWITCH MenuWindowTargetSel2Graph[]={
	{GID_BigCancelButtonOn,0,0,0,0,0xFFFFFFFF},	// キャンセルボタン
	{GID_BigBackButtonOn,0,0,0,0,0xFFFFFFFF},	// バックボタン

	{GID_Health0,0,0,0,0,0xFFFFFFFF},			// 怪我表示
	{GID_Health0,0,0,0,0,0xFFFFFFFF},			// 怪我表示
	{GID_Health0,0,0,0,0,0xFFFFFFFF},			// 怪我表示
	{GID_Health0,0,0,0,0,0xFFFFFFFF},			// 怪我表示
	{GID_Health0,0,0,0,0,0xFFFFFFFF},			// 怪我表示
	{GID_Health0,0,0,0,0,0xFFFFFFFF},			// 怪我表示
};

BUTTON_SWITCH MenuWindowTargetSel2Button[]={ {0,0} };

TEXT_SWITCH MenuWindowTargetSel2Text[]={
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"■"},                                //MLHIDE
	{FONT_PAL_YELLOW,FONT_KIND_SMALL,ML_STRING(490, "请选择对象。")},
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Health  LV NAME             LP            FP"}, //MLHIDE
};


// スイッチ
static SWITCH_DATA TargetSel2Switch[] = {

{ SWITCH_GRAPHIC,151,200,  66, 17, TRUE, &MenuWindowTargetSel2Graph[0], MenuSwitchTargetSel2Cancel },			// キャンセルボタン
{ SWITCH_GRAPHIC,303,200,  66, 17, TRUE, &MenuWindowTargetSel2Graph[1], MenuSwitchTargetSel2Back },				// バックボタン

{ SWITCH_TEXT,	 180, 25,   0,  0, TRUE, &MenuWindowTargetSel2Text[1], MenuSwitchNone },						// 信息
{ SWITCH_TEXT,	  40, 45,   0,  0, TRUE, &MenuWindowTargetSel2Text[2], MenuSwitchNone },						// ヘッダ

{ SWITCH_GRAPHIC, 57, 72,   0,  0, TRUE, &MenuWindowTargetSel2Graph[2], MenuSwitchNone },						// Health
{ SWITCH_GRAPHIC, 57, 92,   0,  0, TRUE, &MenuWindowTargetSel2Graph[3], MenuSwitchNone },						// Health
{ SWITCH_GRAPHIC, 57,112,   0,  0, TRUE, &MenuWindowTargetSel2Graph[4], MenuSwitchNone },						// Health
{ SWITCH_GRAPHIC, 57,132,   0,  0, TRUE, &MenuWindowTargetSel2Graph[5], MenuSwitchNone },						// Health
{ SWITCH_GRAPHIC, 57,152,   0,  0, TRUE, &MenuWindowTargetSel2Graph[6], MenuSwitchNone },						// Health
{ SWITCH_GRAPHIC, 57,172,   0,  0, TRUE, &MenuWindowTargetSel2Graph[7], MenuSwitchNone },						// Health

{ SWITCH_TEXT,	  40, 70,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40, 90,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40,110,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40,130,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40,150,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// 选择对象
{ SWITCH_TEXT,	  40,170,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// 选择对象

{ SWITCH_NONE,	  40, 70, 441, 13, TRUE, NULL, MenuSwitchTargetSel2TextButton },								// 选择对象
{ SWITCH_NONE,	  40, 90, 441, 13, TRUE, NULL, MenuSwitchTargetSel2TextButton },								// 选择对象
{ SWITCH_NONE,	  40,110, 441, 13, TRUE, NULL, MenuSwitchTargetSel2TextButton },								// 选择对象
{ SWITCH_NONE,	  40,130, 441, 13, TRUE, NULL, MenuSwitchTargetSel2TextButton },								// 选择对象
{ SWITCH_NONE,	  40,150, 441, 13, TRUE, NULL, MenuSwitchTargetSel2TextButton },								// 选择对象
{ SWITCH_NONE,	  40,170, 441, 13, TRUE, NULL, MenuSwitchTargetSel2TextButton },								// 选择对象

{ SWITCH_TEXT,	  40, 70,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LV
{ SWITCH_TEXT,	  40, 90,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LV
{ SWITCH_TEXT,	  40,110,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LV
{ SWITCH_TEXT,	  40,130,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LV
{ SWITCH_TEXT,	  40,150,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LV
{ SWITCH_TEXT,	  40,170,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LV

{ SWITCH_TEXT,	  40, 70,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LP,FP
{ SWITCH_TEXT,	  40, 90,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LP,FP
{ SWITCH_TEXT,	  40,110,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LP,FP
{ SWITCH_TEXT,	  40,130,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LP,FP
{ SWITCH_TEXT,	  40,150,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LP,FP
{ SWITCH_TEXT,	  40,170,   0,  0, TRUE, &MenuWindowTargetSel2Text[0], MenuSwitchNone },						// LP,FP

};

enum{
	EnumTargetSel2Cancel,
	EnumTargetSel2Back,

	EnumTargetSel2TargetInfo,
	EnumTargetSel2TargetHeader,

	EnumTargetSel2TargetHealth0,
	EnumTargetSel2TargetHealth1,
	EnumTargetSel2TargetHealth2,
	EnumTargetSel2TargetHealth3,
	EnumTargetSel2TargetHealth4,
	EnumTargetSel2TargetHealth5,

	EnumTargetSel2TargetText0,
	EnumTargetSel2TargetText1,
	EnumTargetSel2TargetText2,
	EnumTargetSel2TargetText3,
	EnumTargetSel2TargetText4,
	EnumTargetSel2TargetText5,

	EnumTargetSel2TargetTextButton0,
	EnumTargetSel2TargetTextButton1,
	EnumTargetSel2TargetTextButton2,
	EnumTargetSel2TargetTextButton3,
	EnumTargetSel2TargetTextButton4,
	EnumTargetSel2TargetTextButton5,

	EnumTargetSel2Targetlv0,
	EnumTargetSel2Targetlv1,
	EnumTargetSel2Targetlv2,
	EnumTargetSel2Targetlv3,
	EnumTargetSel2Targetlv4,
	EnumTargetSel2Targetlv5,

	EnumTargetSel2Targetlpfp0,
	EnumTargetSel2Targetlpfp1,
	EnumTargetSel2Targetlpfp2,
	EnumTargetSel2Targetlpfp3,
	EnumTargetSel2Targetlpfp4,
	EnumTargetSel2Targetlpfp5,

	EnumTargetSel2End,
};

const WINDOW_DATA WindowDataMenuTargetSel2 = {
 0,															// メニューウィンドウ
     4,   60,105,520,243, 0x80010101,  EnumTargetSel2End,  TargetSel2Switch, MenuWindowTargetSel2Bf,MenuWindowTargetSel2Af,closeTargetSel2Window
};


struct STRUCT_TARGETSEL2{
	char *name;
	int lv;
	int lp,maxlp;
	int fp,maxfp;
	char health;
	char color;
};

enum{
	TARGETSEL2_RETURN_BACK = -3,
	TARGETSEL2_RETURN_CLOSE,
	TARGETSEL2_RETURN_DEATHFUNC,

	TARGETSEL2_RETURN_TEXT = 0,
};

ACTION *openTargetSel2Window( struct STRUCT_TARGETSEL2 *Data, int Num, BOOL (*ReturnFunc)( int ret, char *str ) );



//========================================
// ショップトップ
//========================================

// ボタン处理关数 *********************//

BOOL MenuWindowShopTopTextHit( int no, unsigned int flag );

BOOL MenuWindowShopTopBf( int mouse );
BOOL MenuWindowShopTopAf( int mouse );
BOOL MenuWindowShopTopClose();

TEXT_SWITCH MenuWindowShopTopText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,""},	
};

ACTION_SWITCH_INIT MenuWindowShopTopAction[]={
	{100000},
};

// スイッチ
static SWITCH_DATA ShopTopSwitch[] = {

{ SWITCH_TEXT,    40, 10, 0, 0, TRUE, &MenuWindowShopTopText, MenuSwitchNone },						// タイトル

{ SWITCH_ACTION,  40,135, 0, 0, TRUE, &MenuWindowShopTopAction[0], MenuSwitchNone },				// 店员画像

{ SWITCH_NONE,    99,142,108,0, TRUE, NULL, MenuWindowShopTopTextHit },								// テキスト判定

};

enum{
	EnumShopTopTxtTitle,

	EnumShopTopAnmChara,

	EnumShopTopTextHit,

	EnumShopTopEnd,
};

const WINDOW_DATA WindowDataMenuShopTop = {
 MENU_ATTR_NOMOVE,
     4,  209, 117,222,216, 0x80010101,  EnumShopTopEnd,  ShopTopSwitch, MenuWindowShopTopBf,MenuWindowShopTopAf,MenuWindowShopTopClose
};

ACTION *openShopTop( char *Title, int GraNo, char **Info, char InfoNum, char **Sel, char SelNum, char **OneLine, void (*ReturnFuc)( int Num ) );


//====================================//
//====================================//
/***
// ウィンドウ描画关数 ++++
#define COMMONCHIP_W 74
#define COMMONCHIP_H 54
void MenuWindowCommonDraw( int GraNo, short x, short y, short w, short h, int DispPrio, unsigned long frame_rgba, unsigned long back_rgba,
	char sizeX = COMMONCHIP_W, char sizeY = COMMONCHIP_H );
***/

#endif