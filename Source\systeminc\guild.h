﻿#ifndef _GUILD_H_
#define _GUILD_H_

#include"action.h"
#include"pc.h"

//-------------------------------------------------------------------------//
// 定数定义                                                                //
//-------------------------------------------------------------------------//

#define	GUILD_TITLE_LINE_D		5
#define	GUILD_TITLE_LINE_T		8

#define	GUILD_TITLE_MAX			32			// 家族称号の最大数

#define GUILD_BOOK_LINE			4			// １ページあたりの表示人数
//内部的に家族成员情报を200まで格纳できるようにしておきます。
#define GUILD_BOOK_PAGE			50			// 最大ページ数

#define	GUILD_MEMBER_MAX		(GUILD_BOOK_LINE*GUILD_BOOK_PAGE) // 家族の最大メンバー数

//この数值より少ないメンバーの时はこの数值までしかスクロールしません。
//この数值より大きくなった场合、その数值までスクロールします。
#define GUILD_MEMBER_OPEN_MAX	100

#define	GUILD_NAME_LEN			(20 * 2)	// 家族の名称关连の最大文字数
#define	GUILD_INFO_LEN			(120 * 2)	// 家族概要の最大文字数
#define GUILD_MONSTER_MAX		3
#define GUILD_MONSTER_NAME_LEN (20 * 2)


// 家族称号が持つ权利のフラグ设定
#define	GUILD_FLAG_INVITE		(1<<0)		// 家族に劝诱することが可能
#define	GUILD_FLAG_DISMISS		(1<<1)		// 家族から除名することが可能
#define	GUILD_FLAG_FEED			(1<<2)		// 家族モンスターにえさを与えることが可能
#define	GUILD_FLAG_ITEMBOX		(1<<3)		// 家族のアイテムボックスを操作することが可能
#define	GUILD_FLAG_BBS			(1<<4)		// 家族の揭示板を管理することが可能

#define	GUILD_FLAG_MASTER		(1<<7)		// 家族マスター

//-------------------------------------------------------------------------//
// 构造体宣言                                                              //
//-------------------------------------------------------------------------//

// 家族称号
typedef struct {
	unsigned int	flag;					// 家族称号の权限
	char			name[GUILD_NAME_LEN+1];	// 家族称号名
} GUILD_TITLE_INFO;

// 家族成员
typedef struct {
	int		titleId;						// 家族称号番号
	int		joinDate;						// 入会日时（ソートに使用）
	ADDRESS_BOOK_INFO	address;
} GUILD_MEMBER_INFO;

// 家族の情报
typedef struct {
	int		guildId;							// 家族ＩＤ
	int		serverNo;							// 家族が作られたサーバー番号

	int		pcGuildTitleId;						// ＰＣの家族称号ＩＤ
	int		pcAuthority;						// ＰＣの持つ权限（内容は GUILD_FLAG_～ 参照）
	int		pcMemberId;							// ＰＣの家族内管理番号

	char	guildName[GUILD_NAME_LEN+1];		// 家族の名称
	char	guildRoomName[GUILD_NAME_LEN+1];	// 家族ルームの名称

	int		memberCount;						// 现在の有效メンバー数

	GUILD_TITLE_INFO	title[GUILD_TITLE_MAX];		// 家族称号
	GUILD_MEMBER_INFO	member[GUILD_MEMBER_MAX];	// メンバー情报
	char monsterName[GUILD_MONSTER_MAX][GUILD_MONSTER_NAME_LEN + 1];	// monsterName
} GUILD_INFO;

//-------------------------------------------------------------------------//
//		确保
//-------------------------------------------------------------------------//

// 家族ブック
extern	GUILD_INFO				guildBook;
extern	ADDRESS_BOOK_SORT_TBL	guildBookSortTbl[GUILD_MEMBER_MAX];


extern	int mailGuildHistorySelectNo;
extern	int mailGuildHistoryPage;
extern	GUILD_MAIL_HISTORY mailGuildHistory[MAXCHARACTER][GUILD_MEMBER_MAX];


//关数
void G_sendItemSelectMenu( int status );
void G_mailPetEditMenu( int status );
void G_mailPetSelectMenu( int status );
void G_mailHistoryMenu( int status );
void G_mailEditMenu( int status );
void G_mailSelectMenu( int status );
void G_addressBookDelMenu( int status );
void G_addressBookSortMenu( int status );
void G_addressBookMenu( int status );
void G_detailBookMenu( int status );
void G_titleBookMenu( int status );

void GuildInfoInit(void);
void GuildInfoClear(void);


#endif
