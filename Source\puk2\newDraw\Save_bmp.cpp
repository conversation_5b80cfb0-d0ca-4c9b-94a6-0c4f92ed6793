﻿#ifdef PUK2

#define ERR_SBMP____OK 0
#define ERR_SBMP__OPEN 1
#define ERR_SBMP_WRITE 0xf

//*******************************************************************//
unsigned char Save_bmp( int bmpNo )
{
	BITMAPFILEHEADER bfh;	//ビットマップデータ
	BITMAPINFOHEADER bih;	//ビットマップデータ
	HANDLE	fp;				//ファイルハンドル
	DWORD get;				//データ书き込み量取得变数
	int i;				//ループカウンター
	unsigned char a;
	int palsiz;
	char c[256];

	if( realGetImage( bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &palsiz ) == FALSE ){
		RealBinBmpNo = -1;
		return 1;
	}
	RealBinBmpNo = bmpNo;

	// BITMAPFILEHEADER 作成
	bfh.bfType=0x4d42;					//ファイル种类"BM"
	bfh.bfSize=sizeof(BITMAPFILEHEADER)+sizeof(BITMAPINFOHEADER)+RealBinWidth*RealBinHeight;
										//ファイルのサイズ
	bfh.bfReserved1=0;					//必ず 0
	bfh.bfReserved2=0;					//必ず 0
	bfh.bfOffBits=sizeof(BITMAPFILEHEADER)+sizeof(BITMAPINFOHEADER);
										//ファイル先头からイメージデータまでのOFFセット

	// BITMAPINFOHEADER 作成
	bih.biSize=sizeof(BITMAPINFOHEADER);//この构造体のサイズ
	bih.biWidth=RealBinWidth;			//絵の幅
	bih.biHeight=RealBinHeight;			//絵の高さ
	bih.biPlanes=1;						//必ず 1
	bih.biBitCount=8;					//ＢＰＰ
	bih.biCompression=BI_RGB;			//压缩の种类(BI_RGB は无压缩)
	bih.biSizeImage=0;					//絵のサイズ(BI_RGB の场合 0 も可能)
	bih.biXPelsPerMeter=0;				//水平解像度(基本的に 0)
	bih.biYPelsPerMeter=0;				//垂直解像度(基本的に 0)
	bih.biClrUsed=palsiz/3;				//使用するカラーインデックス(基本的に 0)
	bih.biClrImportant=0;				//必须のカラーインデックス(基本的に 0)

	if (bih.biClrUsed){
		bfh.bfSize+=bih.biClrUsed<<2;
		bfh.bfOffBits+=bih.biClrUsed<<2;
	}else{
		bfh.bfSize+=256<<2;
		bfh.bfOffBits+=256<<2;
	}

	// ファイルオープン
	wsprintf( c, "cutspr/%d.bmp", bmpNo );                               //MLHIDE
	fp=CreateFile(c, GENERIC_WRITE, 0, NULL, OPEN_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
	if (fp==INVALID_HANDLE_VALUE) return(ERR_SBMP__OPEN);

	// BITMAPFILEHEADER の书き込み
	if ( !WriteFile(fp,&bfh,sizeof(BITMAPFILEHEADER),&get,NULL) ) goto falseend;
	if ( get!=sizeof(BITMAPFILEHEADER) ) goto falseend;

	// BITMAPINFOHEADER の书き込み
	if ( !WriteFile(fp,&bih,sizeof(BITMAPINFOHEADER),&get,NULL) ) goto falseend;
	if ( get!=sizeof(BITMAPINFOHEADER) ) goto falseend;

	// パレットの书き込み
	if (palsiz){
		a=0;
		for(i=0;i<palsiz;i+=3){
			if ( !WriteFile(fp,pRealBinBits+RealBinWidth*RealBinHeight+i,3,&get,NULL) ) goto falseend;
			if ( get!=3 ) goto falseend;
			if ( !WriteFile(fp,&a,1,&get,NULL) ) goto falseend;
			if ( get!=1 ) goto falseend;
		}
	}else{
		a=0;
		for(i=0;i<256;i++){
			if ( !WriteFile(fp,&Palette[i].peBlue,1,&get,NULL) ) goto falseend;
			if ( get!=1 ) goto falseend;
			if ( !WriteFile(fp,&Palette[i].peGreen,1,&get,NULL) ) goto falseend;
			if ( get!=1 ) goto falseend;
			if ( !WriteFile(fp,&Palette[i].peRed,1,&get,NULL) ) goto falseend;
			if ( get!=1 ) goto falseend;
			if ( !WriteFile(fp,&a,1,&get,NULL) ) goto falseend;
			if ( get!=1 ) goto falseend;
		}

	}

	// 絵の书き込み
	if ( !WriteFile(fp,pRealBinBits,RealBinWidth*RealBinHeight,&get,NULL) ) goto falseend;
	if ( get!=(DWORD)RealBinWidth*RealBinHeight ) goto falseend;

	// ファイルクローズ
	CloseHandle(fp);

	return(ERR_SBMP____OK);

falseend:

	CloseHandle(fp);

	return(ERR_SBMP_WRITE);
}

// 24bppビットマップ书き込み用关数 ++++
BOOL WriteBmp( unsigned char *l_bmp, int l_wid, int l_hei, const char *ls_filename )
{
	HANDLE fp;				// ファイルハンドル
	DWORD get;				// データ読み取り量取得变数
	BITMAPFILEHEADER bfh;	// ビットマップデータ
	BITMAPINFOHEADER bih;	// ビットマップデータ
	int Pitch = ( ( (l_wid*3)+3 )>>2 ) << 2;
							// 一行のバイト数
	int AbsHeight=(l_hei<0?-l_hei:l_hei);
							// 高さ絶对值

	// BITMAPFILEHEADER 作成
	bfh.bfType=0x4d42;									// ファイル种类"BM"
	bfh.bfSize=sizeof(BITMAPFILEHEADER)+sizeof(BITMAPINFOHEADER)+Pitch*AbsHeight;
														// ファイルのサイズ
	bfh.bfReserved1=0;									// 必ず 0
	bfh.bfReserved2=0;									// 必ず 0
	bfh.bfOffBits=sizeof(BITMAPFILEHEADER)+sizeof(BITMAPINFOHEADER);
														// ファイル先头からイメージデータまでのOFFセット
	// BITMAPINFOHEADER 作成
	bih.biSize=sizeof(BITMAPINFOHEADER);				// この构造体のサイズ
	bih.biWidth=l_wid;									// 絵の幅
	bih.biHeight=l_hei;									// 絵の高さ
	bih.biPlanes=1;										// 必ず 1
	bih.biBitCount=24;									// ＢＰＰ
	bih.biCompression=BI_RGB;							// 压缩の种类(BI_RGB は无压缩)
	bih.biSizeImage=0;									// 絵のサイズ(BI_RGB の场合 0 も可能)
	bih.biXPelsPerMeter=0;								// 水平解像度(基本的に 0)
	bih.biYPelsPerMeter=0;								// 垂直解像度(基本的に 0)
	bih.biClrUsed=0;									// 使用するカラーインデックス(基本的に 0)
	bih.biClrImportant=0;								// 必须のカラーインデックス(基本的に 0)

	// ファイルオープン
	fp=CreateFile( ls_filename, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL );
	if (fp==INVALID_HANDLE_VALUE) return FALSE;

	// BITMAPFILEHEADER の书き込み
	if ( !WriteFile( fp,&bfh,sizeof(BITMAPFILEHEADER),&get,NULL ) ) goto falseend;
	if ( get!=sizeof(BITMAPFILEHEADER) ) goto falseend;

	// BITMAPINFOHEADER の书き込み
	if ( !WriteFile( fp,&bih,sizeof(BITMAPINFOHEADER),&get,NULL ) ) goto falseend;
	if ( get!=sizeof(BITMAPINFOHEADER) ) goto falseend;

	// 絵の书き込み
	if ( !WriteFile( fp,l_bmp,bfh.bfSize-bfh.bfOffBits,&get,NULL ) ) goto falseend;
	if ( get!=bfh.bfSize-bfh.bfOffBits ) goto falseend;

	// ファイルを关闭
	CloseHandle(fp);
	
	return TRUE;

falseend://

	// ファイルを关闭
	CloseHandle(fp);

	return FALSE;
}


#endif