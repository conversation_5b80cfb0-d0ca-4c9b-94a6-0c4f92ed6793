﻿/************************/
/*	menuwin.h			*/
/************************/
#ifndef _MENUWINONELINEINFO_H_
#define _MENUWINONELINEINFO_H_

#include "../../systeminc/language.h"
#ifndef MULTI_LANG

	#define MWONELINE_COMMON_WINDOWCLOSE		"关闭这个窗口。"					// クローズ

//----------------------------------------
// 战闘
	// プレーヤーコマンド
		#define MWONELINE_BATTLE_ATTACK				"普通攻击。"						// Attack
		#define MWONELINE_BATTLE_GUARD				"物理防御。"						// Guard
		#define MWONELINE_BATTLE_SKILL				"使用技能。"							// Skill
		#define MWONELINE_BATTLE_ITEM				"变更物品使用和装备。"			// Item
		#define MWONELINE_BATTLE_MONSTER			"召唤、召还宠物。"			// Monster
		#define MWONELINE_BATTLE_POSITION			"变更位置。"						// Position
		#define MWONELINE_BATTLE_ESCAPE				"逃跑。"									// Escape
		#define MWONELINE_BATTLE_REBIRTH_ON			"精灵变身。"							// Re-birth（OFF状态）
		#define MWONELINE_BATTLE_REBIRTH_OFF		"解除精灵变身。"						// Re-birth（ON状态）
	// スキルウィンドウ
		// ※フィールドのスキルウィンドウに准据してください。

	// アイテムウィンドウ
		// ※フィールドのアイテムウィンドウに准据してください。

	// モンスターリストウィンドウ
		#define MWONELINE_BATTLEPETLIST_PANEL_ON	"召唤这个宠物。"						// パネル（召唤可能）
		#define MWONELINE_BATTLEPETLIST_PANEL_OFF	"无法召唤这个宠物。"					// パネル（召唤不可能）
		#define MWONELINE_BATTLEPETLIST_RETURN		"召还宠物。"						// Returanボタン

	// モンスターコマンド
		#define MWONELINE_BATTLEPET_SKILL_ON		"关闭技能列表窗口。"			// Skillボタン（ウィンドウが开いている）
		#define MWONELINE_BATTLEPET_SKILL_OFF		"打开技能列表窗口。"			// Skillボタン（ウィンドウが闭じている）

	// モンスタースキルウィンドウ
		// ※フィールドのスキルウィンドウに准据してください。

	// 観战
		#define MWONELINE_BATTLE_WATCH				"结束观战。"							// 观战结束ボタン

//----------------------------------------
// チャットウィンドウ
	#define MWONELINE_CHAT_LINE				"使用鼠标左键增加显示行数，使用右键减少。"	// 行ボタン
	#define MWONELINE_CHAT_AREA				"切换对话范围。"						// 聊天范围
	#define MWONELINE_CHAT_SIZE				"切换文字大小。"						// 文字サイズ
	#define MWONELINE_CHAT_COLOR			"切换文字颜色。"						// 文字カラー
	#define MWONELINE_CHAT_DISP				"使用鼠标左键增加显示文字数，使用右键减少。"	// 表示文字数

//----------------------------------------
// 左コックピット
	// ヘルスボタン
	#define MWONELINE_LCOCKPIT_HEALTH_0		"现在健康状态良好。"						// ヘルスボタン
	#define MWONELINE_LCOCKPIT_HEALTH_1		"现在受了皮外伤。"					// ヘルスボタン
	#define MWONELINE_LCOCKPIT_HEALTH_2		"现在受了轻伤。"						// ヘルスボタン
	#define MWONELINE_LCOCKPIT_HEALTH_3		"现在受了重伤。"								// ヘルスボタン
	#define MWONELINE_LCOCKPIT_HEALTH_4		"现在处于濒死状态。"								// ヘルスボタン

	#define MWONELINE_LCOCKPIT_PET_HEALTH_0	"现在%s的健康状态良好。"					// ヘルスボタン
	#define MWONELINE_LCOCKPIT_PET_HEALTH_1	"现在%s受了皮外伤。"				// ヘルスボタン
	#define MWONELINE_LCOCKPIT_PET_HEALTH_2	"现在%s受了轻伤。"					// ヘルスボタン
	#define MWONELINE_LCOCKPIT_PET_HEALTH_3	"现在%s受了重伤。"							// ヘルスボタン
	#define MWONELINE_LCOCKPIT_PET_HEALTH_4	"现在%s处于濒死状态。"							// ヘルスボタン

	// メールボタン
	#define MWONELINE_LCOCKPIT_MAIL_ON		"收到邮件。"							// メールボタン
	#define MWONELINE_LCOCKPIT_MAIL_OFF		"现在没收到邮件。"					// メールボタン

	// 等级アップボタン
	#define MWONELINE_LCOCKPIT_LVUP_ON		"现在点数还有%d。"				// 等级アップボタン(%d…ボーナスポイント值)
	#define MWONELINE_LCOCKPIT_LVUP_OFF		"现在没有点数了。"				// 等级アップボタン

	#define MWONELINE_LCOCKPIT_PET_LVUP_ON	"现在%s的点数还有%d。"			// 等级アップボタン(%d…ボーナスポイント值)
	#define MWONELINE_LCOCKPIT_PET_LVUP_OFF	"现在%s没有点数了。"			// 等级アップボタン

	// Ｄｕ
	#define MWONELINE_LCOCKPIT_DU_ON		"现在同意竞技。"			// Ｄｕ
	#define MWONELINE_LCOCKPIT_DU_OFF		"现在不同意竞技。"		// Ｄｕ
	// Ｃｈ
	#define MWONELINE_LCOCKPIT_CH_ON		"现在可以队伍频道对话。"	// Ｃｈ
	#define MWONELINE_LCOCKPIT_CH_OFF		"现在可以正常说话。"		// Ｃｈ
	// Ｇｐ
	#define MWONELINE_LCOCKPIT_GP_ON		"现在同意加入队伍。"		// Ｇｐ
	#define MWONELINE_LCOCKPIT_GP_OFF		"现在不同意加入队伍。"	// Ｇｐ
	// Ａｄ
	#define MWONELINE_LCOCKPIT_AD_ON		"现在同意交换名片。"			// Ａｄ
	#define MWONELINE_LCOCKPIT_AD_OFF		"现在不同意交换名片。"		// Ａｄ
	// Ｔｒ
	#define MWONELINE_LCOCKPIT_TR_ON		"现在同意交易。"			// Ｔｒ
	#define MWONELINE_LCOCKPIT_TR_OFF		"现在不同意交易。"		// Ｔｒ
	// Ｇｕ
	#define MWONELINE_LCOCKPIT_GU_ON		"现在同意邀请加入家族。"		// Ｇｕ
	#define MWONELINE_LCOCKPIT_GU_OFF		"现在不同意邀请加入家族。"		// Ｇｕ

	#define MWONELINE_LCOCKPIT_GU_NO		"无法使用家族功能"	// Ｇｕ

//----------------------------------------
// 右コックピット
	#define MWONELINE_RCOCKPIT_DUEL			"申请竞技。"						// デュエル
	#define MWONELINE_RCOCKPIT_WATCH		"观战。"									// 観战
	#define MWONELINE_RCOCKPIT_GROUP		"加入队伍。"							// グループ
	#define MWONELINE_RCOCKPIT_CARD			"交换名片。"							// 名刺交换
	#define MWONELINE_RCOCKPIT_TRADE		"交易。"								// トレード
	#define MWONELINE_RCOCKPIT_GUILD		"邀请加入家族。"							// 家族
	#define MWONELINE_RCOCKPIT_ACTION		"打开动作列表窗口。"		// アクション
	#define MWONELINE_RCOCKPIT_MAP			"打开地图窗口。"					// マップ

	#define MWONELINE_RCOCKPIT_GUILD_NO		"无法使用家族功能"		// 家族

//----------------------------------------
// アクションウィンドウ
	#define MWONELINE_ACTION_SIT			"做坐下。"				// 座る
	#define MWONELINE_ACTION_HAND			"做挥手。"			// 挥手
	#define MWONELINE_ACTION_NOD			"做点头动作　　　"			// 点头
	#define MWONELINE_ACTION_HAPPY			"做高兴动作　　　"				// 喜ぶ
	#define MWONELINE_ACTION_ANGRY			"做愤怒动作　 　　"				// 怒る
	#define MWONELINE_ACTION_SAD			"做悲伤动作　 　"			// 悲伤
	#define MWONELINE_ACTION_THROW			"做投掷动作。"			// 投掷
	#define MWONELINE_ACTION_CHOKI			"做剪刀动作。"			// 剪刀
	#define MWONELINE_ACTION_PA				"做布动作。"				// 布

	#define MWONELINE_ACTION_STAND			"做站立动作。"				// 立つ
	#define MWONELINE_ACTION_WALK			"做走动作。"				// 步く
	#define MWONELINE_ACTION_DEAD			"做倒下动作。"			// 倒下
	#define MWONELINE_ACTION_ATTACK			"做攻击动作。"				// 攻击
	#define MWONELINE_ACTION_GUARD			"做防御动作。"			// 防御
	#define MWONELINE_ACTION_DAMAGE			"做受伤动作。"			// 受伤
	#define MWONELINE_ACTION_MAGIC			"做魔法动作。"				// 魔法
	#define MWONELINE_ACTION_MOVE			"做移动动作。"				// 移动
	#define MWONELINE_ACTION_GU				"做石头动作。"				// 石头

//----------------------------------------
// マップウィンドウ

//----------------------------------------
// 状态ウィンドウ
	// 共通
		#define MWONELINE_STATUS_STATUS			"切换玩家状态窗口。"	// 状态ボタン
		#define MWONELINE_STATUS_DETAIL			"切换玩家详细窗口。"	// ディティールボタン
		#define MWONELINE_STATUS_TITLE			"切换玩家称号窗口。"				// タイトルボタン
#ifdef PUK3_PROF
		#define MWONELINE_STATUS_PROFILE		"切换玩家个人信息窗口。"			// プロフィールボタン
#endif
	// 状态
		#define MWONELINE_STATUS_FRONT			"战斗时的位置变更在前排。"			// 前卫ボタン
		#define MWONELINE_STATUS_BACK			"战斗时的位置变更在后排。"			// 后卫ボタン

	// ディティール
		// ＋ボタン
		#define MWONELINE_STATUS_PLUS_ON		"在这个属性上面分配点数。"			// ＋ボタン
		#define MWONELINE_STATUS_PLUS_OFF		"无法在分配点数。"		// ＋ボタン（ポイントを割り振れない时）

	// タイトル
		#define MWONELINE_STATUS_USERSET		"玩家称号设定。"								// UserTitleセットボタン
		#define MWONELINE_STATUS_REMOVE			"解除选择的称号。"					// SpecialTitle Removeボタン
		#define MWONELINE_STATUS_DELETE			"清除选择的称号。"					// SpecialTitle Deleteボタン
		#define MWONELINE_STATUS_SPECIALPANEL	"使用这个称号。"							// SpecialTitleパネル

#ifdef PUK3_PROF
	//プロフィール
		#define MWONELINE_CATEGORY_LIST			"打开分类窗口。"					// 分类リスト开く
		#define MWONELINE_SET_PROFILE			"决定个人信息。"						// プロフィールの决定
		#define MWONELINE_PROFILE_OPEN			"公开个人信息。"						// プロフィールのＯＮ
		#define MWONELINE_PROFILE_CLOSE			"不公开个人信息。"					// プロフィールのＯＦＦ
		#define MWONELINE_PROFILE_DIALOG		"请输入指令。"						// コメント入力

		#define MWONELINE_STATUS_PROFILE_NO		"无法使用个人信息窗口"		// プロフィールＮＯ
#endif


//----------------------------------------
// スキルウィンドウ
	// スキル
		#define MWONELINE_SKILL_SKILLPANEL		"打开这个技能的能力窗口。"		// パネル

	// スキルリスト
		#define MWONELINE_SKILL_LISTBACK		"回到技能列表窗口。"				// バックボタン
		#define MWONELINE_SKILL_REBIRTH_ON		"精灵变身。"									// リバースボタン
		#define MWONELINE_SKILL_REBIRTH_OFF		"解除精灵变身。"							// リバースボタン
		#define MWONELINE_SKILL_PASS			"这回合什么都不做。"							// パスボタン
		#define MWONELINE_SKILL_ON				"使用这个能力。"						// パネル（使用可能）
		#define MWONELINE_SKILL_OFF				"无法使用这个能力。"		// パネル（使用不可能）
		#define MWONELINE_SKILL_FP				"魔力不足。"								// パネル（FPが足りない）

	// レシピ
		#define MWONELINE_SKILL_RECIPEBACK		"回到能力窗口。"					// バックボタン
		#define MWONELINE_SKILL_RECIPEPANEL		"制作这个配方的物品。"				// パネル

//----------------------------------------
// 作成ウィンドウ　＆　修理ウィンドウ　＆　鉴定ウィンドウ　＆　刻印ウィンドウ
	#define MWONELINE_SKILLCREATE_MYITEM	"通过拖动选择物品。"	// Myアイテム
	#define MWONELINE_SKILLCREATE_SELECT	"在此放入材料。"		// 选择アイテム
	#define MWONELINE_SKILLCREATE_TRY		"执行。"										// TRYボタン
	#define MWONELINE_SKILLCREATE_RETRY		"继续执行。"								// ReTRYボタン
	#define MWONELINE_SKILLCREATE_END		"关闭这个窗口。"						// ENDボタン
	#define MWONELINE_SKILLCREATE_FP		"魔力不足。"								// FPが足りない

//----------------------------------------
// ギャザーウィンドウ
	#define MWONELINE_SKILLGATHER_ENDCLOSE	"关闭这个窗口。"						// ENDボタン
	#define MWONELINE_SKILLGATHER_ENDSTOP	"中止。"									// ENDボタン
	#define MWONELINE_SKILLGATHER_TRY		"再次执行。"										// TRYボタン
	#define MWONELINE_SKILLGATHER_FP		"魔力不足。"								// FPが足りない

//----------------------------------------
// 对象选择ウィンドウその１
	#define MWONELINE_TARGETSEL1_NAME		"选择这个角色。"					// 对象プレーヤー
	#define MWONELINE_TARGETSEL1_CANCEL		"关闭这个窗口。"						// キャンセル

//----------------------------------------
// 对象选择ウィンドウその２
	#define MWONELINE_TARGETSEL2_NAME		"选择这个角色。"					// 对象キャラクター
	#define MWONELINE_TARGETSEL2_CANCEL		"关闭这个窗口。"						// キャンセル
	#define MWONELINE_TARGETSEL2_BACK		"返回。"								// バック

//----------------------------------------
// アイテムウィンドウ
	// アイテム
		#define MWONELINE_ITEM_MYITEM			"在地面上放置金币。"				// PutMoneyボタン
		#define MWONELINE_ITEM_EQUIP_OFF		"打开装备窗口。"						// ＥＱＵＩＰボタン
		#define MWONELINE_ITEM_ABLEITEM			"这个物品可以使用。"						// 使用可能アイテム
		#define MWONELINE_ITEM_ABLEEQUIP		"这个物品可以装备。"						// 装备可能アイテム
		#define MWONELINE_ITEM_UNABLEITEM		"这个物品不能使用。"			// 使用不可アイテム
		#define MWONELINE_ITEM_UNABLEEQUIP		"这个物品不能装备。"			// 装备不可アイテム

	// ＥＱＵＩＰ
		#define MWONELINE_ITEM_EQUIP_ON			"关闭装备窗口。"						// ＥＱＵＩＰボタン
		#define MWONELINE_ITEM_ACCESS1			"装饰品的装备位。"							// Acces1
		#define MWONELINE_ITEM_ACCESS2			"装饰品的装备位。"							// Acces2
		#define MWONELINE_ITEM_HEAD				"头部防具装备位。"							// Head
		#define MWONELINE_ITEM_BODY				"身体防具装备位。"							// Body
		#define MWONELINE_ITEM_HAND				"武器和盾的装备位。"						// Hand
		#define MWONELINE_ITEM_FOOT				"脚步装备位。"							// Foot
		#define MWONELINE_ITEM_CRYSTAL			"水晶装备位。"						// Crystal

	// マネーウィンドウ　＆　スタックウィンドウ
		#define MWONELINE_CALCULATOR_NUM		"指定数字。"								// 0～9ボタン
		#define MWONELINE_CALCULATOR_OK_ON		"以指定数字确定。"							// ＯＫボタン
		#define MWONELINE_CALCULATOR_OK_OFF		"输入数字。"							// ＯＫボタン（押せない）
		#define MWONELINE_CALCULATOR_BS			"返回。"										// ＢＳボタン
		#define MWONELINE_CALCULATOR_ALL		"指定最大数。"								// ＡＬＬボタン
		#define MWONELINE_CALCULATOR_CLR		"将数字归０。"								// ＣＬＲボタン

		#define MWONELINE_CALCULATOR_Up			"增加数字。"						//　数字表示横の↑ボタン
		#define MWONELINE_CALCULATOR_Down		"减少数字。"						//　数字表示横の↓ボタン

//----------------------------------------
// 使い魔ウィンドウ
	#define MWONELINE_PET_PANEL				"打开宠物的状态窗口。"		// パネル
	#define MWONELINE_PET_BATTLE_OFF		"将宠物设为战斗状态。"					// Battleボタン（押せる）
	#define MWONELINE_PET_BATTLE			"这个宠物无法设为战斗状态。"				// Battleボタン（押せない）
	#define MWONELINE_PET_FIELD_OFF			"将宠物设为休息状态。"					// Fieldボタン（押せる）
	#define MWONELINE_PET_FIELD				"这个宠物无法设为休息状态。"				// Fieldボタン（押せない）
	#define MWONELINE_PET_WALK_OFF			"将宠物设为散步状态。"					// Walkボタン（押せる）
	#define MWONELINE_PET_WALK				"这个宠物无法设为散步状态。"				// Walkボタン（押せない）
	#define MWONELINE_PET_STANDBY_OFF		"将宠物设为待命状态。"					// Stand byボタン（押せる）
	#define MWONELINE_PET_STANDBY			"这个宠物无法设为待命状态。"				// Stand byボタン（押せない）

//----------------------------------------
// 使い魔详细ウィンドウ
	// 共通
		#define MWONELINE_PETSTATUS_SET			"无法变更宠物名。"					// Setボタン
		#define MWONELINE_PETSTATUS_RELEASE		"扔到这个宠物。"							// Releaseボタン
		#define MWONELINE_PETSTATUS_LEFT		"切换页面。"							// ←ボタン
		#define MWONELINE_PETSTATUS_RIGHT		"切换页面。"							// →ボタン
		#define MWONELINE_PETSTATUS_STATUS		"切换到宠物的状态窗口。"	// 状态ボタン
		#define MWONELINE_PETSTATUS_DETAIL		"切换到宠物的详细窗口。"	// ディティールボタン
		#define MWONELINE_PETSTATUS_SKILL		"切换到宠物的技能窗口。"		// スキルボタン

	// 状态
	// ディティール
		// ＋ボタン
		#define MWONELINE_PETSTATUS_PLUS_ON		"将点数分配到这个属性上。"		// ＋ボタン
		#define MWONELINE_PETSTATUS_PLUS_OFF	"无法在分配点数。"		// ＋ボタン（ポイントを割り振れない时）
	// スキル

//----------------------------------------
// 名刺ウィンドウ
	// 共通
		#define MWONELINE_CARD_LIST				"切换到列表。"						// リスト切替ボタン
		#define MWONELINE_CARD_DETAIL			"切换到详细。"							// ディティール切替ボタン
		#define MWONELINE_CARD_SORT				"名片排序方式变更。"					// ソート切り替えボタン
		#define MWONELINE_CARD_ADDRESS			"切换到名片窗口。"					// アドレスボタン
		#define MWONELINE_CARD_GUILD			"切换到家族列表。"				// 家族ボタン
		#define MWONELINE_CARD_GROUP			"切换到群邮件。"						// 群邮件ボタン
		#define MWONELINE_CARD_GUILDDETAIL		"切换到家族窗口。"					// 家族详细ボタン
		#define MWONELINE_CARD_LEFT				"切换页面。"							// ←ボタン
		#define MWONELINE_CARD_RIGHT			"切换页面。"							// →ボタン

	// 名刺一覧详细??名刺一覧リスト??家族一覧详细??家族一覧リスト
		#define MWONELINE_CARD_GROUPMAIL		"切换到群邮件。"						// 群邮件ボタン
		#define MWONELINE_CARD_HISTORY			"查看这个好友的邮件历史记录。"			// ヒストリーボタン
		#define MWONELINE_CARD_GUILDHISTORY		"查看这个家族成员的邮件历史记录。"		// ヒストリーボタン
		#define MWONELINE_CARD_PREVHISTORY		"前一个邮件"										//前一个邮件
		#define MWONELINE_CARD_NEXTHISTORY		"下一个邮件"										//下一个邮件
		#define MWONELINE_CARD_SEND				"送信给好友。"				// Ｓｅｎｄボタン
		#define MWONELINE_CARD_DELETE			"删除名片。"							// 削除ボタン
		#define MWONELINE_CARD_GUILD_SEND		"给这个家族成员发送邮件。"			// Ｓｅｎｄボタン
		#define MWONELINE_CARD_TITLE			"变更这个家族成员的称号。"		// タイトル变更ボタン
		#define MWONELINE_CARD_GUILDDELETE		"将这个家族成员除名。"					// 家族成员削除ボタン

//名刺ウィンドウ
		#define MWONELINE_GUILD_TITLE_OPEN	"打开家族称号窗口。"	//家族称号ウインドウ开く

//タイトルウィンドウ内
	#define MWONELINE_GUILD_TITLE_SELECT	"选择这个称号。"				//タイトル选择ボタン
	#define MWONELINE_GUILD_TITLE_SET		"设定选择的称号。"			//タイトル决定ボタン
	#define MWONELINE_GUILD_TITLE_CLOSE		"关闭这个窗口。"				//ウインドウ关闭ボタン


//----------------------------------------
// 家族详细ウィンドウ
	#define MWONELINE_GUILDDETAIL_SETGUILD	"变更家族名称。"								// ＳＥＴ家族名ボタン
	#define MWONELINE_GUILDDETAIL_SETHOUSE	"变更家族房间名称。"							// ＳＥＴ家族ハウス名ボタン
	#define MWONELINE_GUILDDETAIL_JOIN_ON	"有添加这个称号的权限。"				// 加入权限ボタン（チェック有り）
	#define MWONELINE_GUILDDETAIL_JOIN_OFF	"没有添加这个称号的权限。"				// 加入权限ボタン（チェック无し）
	#define MWONELINE_GUILDDETAIL_JOIN		"这个称号的添加权限。"						// 加入权限ボタン（押せない）
	#define MWONELINE_GUILDDETAIL_DEL_ON	"这个称号有除名权限。"				// 除名权限ボタン（チェック有り）
	#define MWONELINE_GUILDDETAIL_DEL_OFF	"这个称号没有除名权限。"				// 除名权限ボタン（チェック无し）
	#define MWONELINE_GUILDDETAIL_DEL		"这个称号的除名权限。"						// 除名权限ボタン（押せない）
	#define MWONELINE_GUILDDETAIL_FOOD_ON	"这个称号有给家族宠物喂食的权限。"	// エサ权限ボタン（チェック有り）
	#define MWONELINE_GUILDDETAIL_FOOD_OFF	"这个称号没有给家族宠物喂食的权限"	// エサ权限ボタン（チェック无し）
	#define MWONELINE_GUILDDETAIL_FOOD		"这个称号的给家族宠物喂食权限。"	// エサ权限ボタン（押せない）
	#define MWONELINE_GUILDDETAIL_ITEM_ON	"这个称号有使用物品箱的权限。"	//アイテム权限ボタン（チェック有り）
	#define MWONELINE_GUILDDETAIL_ITEM_OFF	"这个称号没有使用物品箱的权限。"	// アイテム权限ボタン（チェック无し）
	#define MWONELINE_GUILDDETAIL_ITEM		"这个称号的使用物品箱权限。"	// アイテム权限ボタン（押せない）
	#define MWONELINE_GUILDDETAIL_BBS_ON	"这个称号有删除留言板的权限。"		// 揭示板权限ボタン（チェック有り）
	#define MWONELINE_GUILDDETAIL_BBS_OFF	"这个称号没有删除留言板的权限。"		// 揭示板权限ボタン（チェック无し）
	#define MWONELINE_GUILDDETAIL_BBS		"这个称号的删除留言板权限。"				// 揭示板权限ボタン（押せない）
	#define MWONELINE_GUILDDETAIL_TITRENAME	"变更选择的称号名称。"						// タイトルリネームボタン
	#define MWONELINE_GUILDDETAIL_TITDELETE	"删除选择的称号名称。"						// タイトル削除ボタン

//----------------------------------------
// メール
	// 共通
		#define MWONELINE_MAIL_SEND				"送信给好友。"					// Ｓｅｎｄボタン

	// 送信
		#define MWONELINE_MAIL_PETMAIL			"用宠物邮件送信。"						// ペットメールボタン

	// 履历
		#define MWONELINE_MAIL_LEFT				"切换页面。"								// ←ボタン
		#define MWONELINE_MAIL_RIGHT			"切换页面。"								// →ボタン

		#define MWONELINE_MAIL_BACK				"用普通邮件送信。"								// Ｂａｃｋボタン
		#define MWONELINE_MAIL_PETCHANGE		"让不同的宠物送信。"						// ペット切り替えボタン
		#define MWONELINE_MAIL_NONE				"从物品窗口选择要拖动的物品。"	// カーソル未选择

	// 群邮件
		#define MWONELINE_MAIL_GROUPSEND		"送信给这个群。"				// Ｓｅｎｄボタン
		#define MWONELINE_MAIL_GROUPCHANGE		"切换群邮件。"						//
		#define MWONELINE_MAIL_GROUPSELECTON	"送信给这个好友。"						// チェックボタン
		#define MWONELINE_MAIL_GROUPSELECTOFF	"设定成送信给这个好友。"					// チェックボタン

	// 削除确认ウィンドウ
		#define MWONELINE_MAIL_YES				"消除。"											// Yes
		#define MWONELINE_MAIL_NO				"取消清除。"									// No

//----------------------------------------
// アルバムリストウィンドウ
	#define MWONELINE_ALBAM_PANEL				"打开这个宠物的图鉴窗口。"		// アルバム详细开く

//----------------------------------------
// アルバム详细ウィンドウ
	#define MWONELINE_ALBAMDETAIL_FORWARD	"切换页面。"									// ページ送り
	#define MWONELINE_ALBAMDETAIL_F_FORWARD	"发送10页。"								// ページ早送り
	#define MWONELINE_ALBAMDETAIL_BACK		"切换页面。"									// ページ戾し
	#define MWONELINE_ALBAMDETAIL_F_BACK	"发送10页。"								// ページ早戾し

//----------------------------------------
// システムウィンドウ
	#define MWONELINE_SYSTEM_LOGOUT			"登出。"										// 登出ボタン
	#define MWONELINE_SYSTEM_OPERATION		"鼠标和键盘设定。"						// 操作设定ボタン
	#define MWONELINE_SYSTEM_SOUND			"声音设定。"										// 音设定ボタン
	#define MWONELINE_SYSTEM_DRAWSET		"图形设定。"										// 绘图设定ボタン
	#define MWONELINE_SYSTEM_SHORTCUT		"快捷键总览。"							// ショートカットボタン

	// 登出ウィンドウ
		#define MWONELINE_SYSTEM_LOGINGATE		"回到登入点。"								// 登入点へ
		#define MWONELINE_SYSTEM_SERVERSELECT	"登出回到服务器选择画面。"					// サーバー选择へ

	// 操作设定ウィンドウ
		#define MWONELINE_SYSTEM_ITEMMOVE		"打开堆栈窗口。"					// アイテムの移动
		#define MWONELINE_SYSTEM_CURSOR			"光标的图标变更。"								// マウスカーソル

	// 音设定ウィンドウ
		#define MWONELINE_SYSTEM_BGMDOWN		"BGM的音量减少。"										// ＢＧＭ音量（←）
		#define MWONELINE_SYSTEM_BGMDOWN_MIN	"无法再降低音量了。"								// ＢＧＭ音量（←最小）
		#define MWONELINE_SYSTEM_BGMUP			"BGM的音量增加。"										// ＢＧＭ音量（→）
		#define MWONELINE_SYSTEM_BGMUP_MAX		"无法增加了。"								// ＢＧＭ音量（→最大）
		#define MWONELINE_SYSTEM_SEDOWN			"SE的音量减少。"										// ＳＥ音量（←）
		#define MWONELINE_SYSTEM_SEDOWN_MIN		"无法再降低音量了。"								// ＳＥ音量（←最小）
		#define MWONELINE_SYSTEM_SEUP			"SE的音量增加。"										// ＳＥ音量（→）
		#define MWONELINE_SYSTEM_SEUP_MAX		"无法增加了。"								// ＳＥ音量（→最大）
		#define MWONELINE_SYSTEM_SETYPE			"变更音声。"										// ＳＥ音源

	// 绘图设定ウィンドウ
		#define MWONELINE_SYSTEM_DRAWSETBUTTON	"绘图方式变更。"									// 垂直同步（同期）
#ifdef PUK2_3DDEVICE_DISP
		#define MWONELINE_SYSTEM_3DDEVICESHOW	"现在的绘图方式。"										// 描画机能表示ボタン
#endif
#ifdef PUK2_CHANGE_3DDEIVCE
		#define MWONELINE_SYSTEM_3DDEVICECHANGE	"变更绘图功能。"									// 描画机能变更ボタン
		#define MWONELINE_SYSTEM_3DDEVICENOCHG	"现在无法变更绘图方式。"						// 描画机能变更ボタン切り替えられないとき
/*
		#define MWONELINE_SYSTEM_3DDEVICEPARTY	"现在在队伍中，无法变更绘图方式。"	// 描画机能变更ボタン切り替えられないとき
		#define MWONELINE_SYSTEM_3DDEVICEDUEL	"现在在竞技中，无法变更绘图方式。"			// 描画机能变更ボタン切り替えられないとき
		#define MWONELINE_SYSTEM_3DDEVICEWATCH	"现在在观战中，无法变更绘图方式。"			// 描画机能变更ボタン切り替えられないとき
		#define MWONELINE_SYSTEM_3DDEVICEWATCH	"现在在观战中，无法变更绘图方式。"			// 描画机能变更ボタン切り替えられないとき
*/
#endif

	// ショートカットウィンドウ
		#define MWONELINE_SHORTCUT_WINDOW		"显示窗口相关的快捷键。"				// Windowボタン
		#define MWONELINE_SHORTCUT_SWITCH		"显示开关相关的快捷键。"				// Switchボタン
		#define MWONELINE_SHORTCUT_ACTION		"显示行动相关的快捷键。"				// Actionボタン
		#define MWONELINE_SHORTCUT_CHAT			"进行对话登录。"									// Chatボタン

//----------------------------------------
// スキルマスターNPC
	// 选择ウィンドウ
		#define MWONELINE_SKILLMASTER_LEARN		"从这个技能训练师学习技能。"				// スキルを学习
		#define MWONELINE_SKILLMASTER_FORGET	"忘记已经学习的技能。"						// スキルを忘れる
		#define MWONELINE_SKILLMASTER_SELCANCEL	"取消学习技能。"							// 取消

	// スキル习得ウィンドウ
		#define MWONELINE_SKILLMASTER_OK		"学习这个技能。"									// OKボタン
		#define MWONELINE_SKILLMASTER_LRNCANCEL	"取消学习。"									// キャンセルボタン

	// スキル排除ウィンドウ
		#define MWONELINE_SKILLMASTER_PANEL		"忘记这个技能。"									// スキルパネル
		#define MWONELINE_SKILLMASTER_FGTCANCEL	"取消忘记。"									// キャンセルボタン

	// スキル排除确认ウィンドウ
		#define MWONELINE_SKILLMASTER_YES		"忘记这个技能。"									// Yesボタン
		#define MWONELINE_SKILLMASTER_NO		"取消忘记这个技能。"						// Noボタン

//----------------------------------------
// モンスタースキルショップNPC
	// 贩卖スキルウィンドウ
		#define MWONELINE_PETSKILLMASTER_SKILL	"让宠物学习这个技能。"						// 贩卖スキルパネル
		#define MWONELINE_PETSKILLMASTER_PET	"让这个宠物学习技能。"						// 使い魔パネル
		#define MWONELINE_PETSKILLMASTER_SLOT	"用这个栏位学习技能。"						// スキルスロットパネル
		#define MWONELINE_PETSKILLMASTER_CANCEL	"返回。"										// キャンセル
		#define MWONELINE_PETSKILLMASTER_L_CANC	"取消让宠物学习技能。"				// キャンセル

//----------------------------------------
// ショップNPC
	// 卖り买い选择ウィンドウ
		#define MWONELINE_SHOP_BUYSELLTOPBUY	"购买物品。"							// 要买
		#define MWONELINE_SHOP_BUYSELLTOPSELL	"卖出物品。"							// 要卖
		#define MWONELINE_SHOP_BUYSELLTOPCANCEL	"取消买卖。"										// 取消

	// 购入ウィンドウ
		#define MWONELINE_SHOP_BUYITEMPANEL		"购买这个物品。"									// ショップアイテムパネル
		#define MWONELINE_SHOP_BUYPLUS			"增加物品的购入数。"						// ＋ボタン
		#define MWONELINE_SHOP_BUYPLUS_MAX		"已经无法增加了。"							// ＋ボタン（增やせない）
		#define MWONELINE_SHOP_BUYMINUS			"减少物品的购入数。"						// －ボタン
		#define MWONELINE_SHOP_BUYMINUS_MIN		"已经无法减少了。"							// －ボタン（减らせない）
		#define MWONELINE_SHOP_BUYOK			"购入指定的物品。"						// OKボタン
		#define MWONELINE_SHOP_BUYCANCEL		"取消购入。"											// キャンセルボタン

	// 购入确认ウィンドウ
		#define MWONELINE_SHOP_BUYYES			"购入。"												// Yesボタン
		#define MWONELINE_SHOP_BUYNO			"取消购入。"										// Noボタン

	// 卖却ウィンドウ
		#define MWONELINE_SHOP_SELLMYITEM		"拖动选择物品卖出物品。"		// Myアイテム
		#define MWONELINE_SHOP_SELLSHOPPANEL	"将要卖出的物品放在这里。"	// ショップパネル
		#define MWONELINE_SHOP_SELLITEMPANEL	"拖动取消物品选择。"		// ショップアイテムパネル
		#define MWONELINE_SHOP_SELLPLUS			"增加物品卖出个数。"						// ＋ボタン
		#define MWONELINE_SHOP_SELLPLUS_MAX		"已经无法增加了。"							// ＋ボタン（增やせない）
		#define MWONELINE_SHOP_SELLMINUS		"减少物品卖出个数。"						// －ボタン
		#define MWONELINE_SHOP_SELLMINUS_MIN	"已经无法减少了。"							// －ボタン（减らせない）
		#define MWONELINE_SHOP_SELLOK			"卖出指定的物品。"						// OKボタン
		#define MWONELINE_SHOP_SELLCANCEL		"取消卖出。"											// キャンセルボタン

	// 卖却确认ウィンドウ
		#define MWONELINE_SHOP_SELLYES			"卖。"												// Yesボタン
		#define MWONELINE_SHOP_SELLNO			"取消卖出。"										// Noボタン

	// 鉴定ウィンドウ
		#define MWONELINE_SHOP_APPMYITEM		"拖动选择要鉴定的物品。"		// Myアイテム
		#define MWONELINE_SHOP_APPSHOPPANEL		"在这里放入要鉴定的物品。"	// ショップパネル
		#define MWONELINE_SHOP_APPITEMPANEL		"拖动取消物品选择。"		// ショップアイテムパネル
		#define MWONELINE_SHOP_APPPLUS			"增加物品数量。"								// ＋ボタン
		#define MWONELINE_SHOP_APPPLUS_MAX		"已经无法增加了。"							// ＋ボタン（增やせない）
		#define MWONELINE_SHOP_APPMINUS			"减少物品数量。"								// －ボタン
		#define MWONELINE_SHOP_APPMINUS_MIN		"已经无法减少了。"							// －ボタン（减らせない）
		#define MWONELINE_SHOP_APPOK			"鉴定制定的物品。"						// OKボタン
		#define MWONELINE_SHOP_APPCANCEL		"取消鉴定。"											// キャンセルボタン

	// 鉴定确认
		#define MWONELINE_SHOP_APPYES			"鉴定。"												// Yesボタン
		#define MWONELINE_SHOP_APPNO			"取消鉴定。"										// Noボタン

	// 修理ウィンドウ
		#define MWONELINE_SHOP_REPAIRMYITEM		"拖动要修理的物品。"		// Myアイテム
		#define MWONELINE_SHOP_REPAIRSHOPPANEL	"在这里放入要修理的物品。"	// ショップパネル
		#define MWONELINE_SHOP_REPAIRITEMPANEL	"拖动取消物品选择。"		// ショップアイテムパネル
		#define MWONELINE_SHOP_REPAIRPLUS		"增加物品数量。"								// ＋ボタン
		#define MWONELINE_SHOP_REPAIRPLUS_MAX	"已经无法增加了。"							// ＋ボタン（增やせない）
		#define MWONELINE_SHOP_REPAIRMINUS		"减少物品数量。"								// －ボタン
		#define MWONELINE_SHOP_REPAIRMINUS_MIN	"已经无法减少了。"							// －ボタン（减らせない）
		#define MWONELINE_SHOP_REPAIROK			"修理指定的物品。"						// OKボタン
		#define MWONELINE_SHOP_REPAIRCANCEL		"取消修理。"											// キャンセルボタン

	// 修理确认
		#define MWONELINE_SHOP_REPAIRYES		"修理。"												// Yesボタン
		#define MWONELINE_SHOP_REPAIRNO			"取消修理。"										// Noボタン

	// 交换NPCスタートウィンドウ
		#define MWONELINE_SHOP_TRADETOPTRADE	"与这个商店交换物品。"							// 交换
		#define MWONELINE_SHOP_TRADETOPCANCEL	"取消物品交换。"									// 取消

	// 交换NPC
		#define MWONELINE_SHOP_TRADEMYITEM		"拖动要交换的物品。"		// Myアイテム
		#define MWONELINE_SHOP_TRADESHOPPANEL	"在这里放入要交换的物品。"	// ショップパネル
		#define MWONELINE_SHOP_TRADEITEMPANEL	"拖动取消物品选择。"		// ショップアイテムパネル
		#define MWONELINE_SHOP_TRADEPLUS		"增加物品数量。"								// ＋ボタン
		#define MWONELINE_SHOP_TRADEPLUS_MAX	"已经无法增加了。"							// ＋ボタン（增やせない）
		#define MWONELINE_SHOP_TRADEMINUS		"减少物品数量。"								// －ボタン
		#define MWONELINE_SHOP_TRADEMINUS_MIN	"已经无法减少了。"							// －ボタン（减らせない）
		#define MWONELINE_SHOP_TRADEOK			"交换指定的物品。"						// OKボタン
		#define MWONELINE_SHOP_TRADECANCEL		"取消交换。"											// キャンセルボタン

	// 交换确认
		#define MWONELINE_SHOP_TRADEYES			"交换。"												// Yesボタン
		#define MWONELINE_SHOP_TRADENO			"取消交换。"										// Noボタン

//----------------------------------------
// BBSウィンドウ
	#define MWONELINE_BBS_DELETE			"删除这个信息。"							// デリートボタン
	#define MWONELINE_BBS_RIGHT				"回到上一页。"								// ←ボタン
	#define MWONELINE_BBS_LEFT				"翻到下一页。"								// →ボタン
	#define MWONELINE_BBS_OK				"写入信息。"								// OKボタン
	#define MWONELINE_BBS_CANCEL			"取消写入。"									// キャンセルボタン

//----------------------------------------
// 银行NPC　＆　家族共用ITEMBOX
	// 共通
		#define MWONELINE_BANK_ITEM				"放入取出物品。"							// Itemボタン
		#define MWONELINE_BANK_MONSTER			"放入取出宠物。"							// モンスターボタン
		#define MWONELINE_BANK_DEPOSIT			"放入取出金币。"									// Moneyボタン（自分侧）
		#define MWONELINE_BANK_WITHDRAW			"取出金币。"									// Moneyボタン（相手侧）
		#define MWONELINE_BANK_OK				"关闭这个窗口。"							// OKボタン

	// 自分侧アイテム
		#define MWONELINE_BANK_MYITEM_ON		"拖动选择保存。"			// 预けられるアイテム
		#define MWONELINE_BANK_MYITEM_OFF		"这个物品无法保存。"				// 预けられないアイテム

	#define MWONELINE_BANK_MYMONSTER		"拖动保存宠物。"	// 自分侧モンスター

	// マネー
		#define MWONELINE_BANK_MYMONEYNUM		"指定金额。"									// 0～9ボタン
		#define MWONELINE_BANK_MYMONEYOK_ON		"存入指定金额。"								// ＯＫボタン
		#define MWONELINE_BANK_MYMONEYOK_OFF	"指定金额。"								// ＯＫボタン（押せない）
		#define MWONELINE_BANK_MYMONEYBS		"返回。"											// ＢＳボタン
		#define MWONELINE_BANK_MYMONEYALL		"指定全额。"										// ＡＬＬボタン
		#define MWONELINE_BANK_MYMONEYCLR		"将指定金额归0。"								// ＣＬＲボタン

	#define MWONELINE_BANK_BANKITEM			"拖动取出。"		// 银行侧アイテム

	#define MWONELINE_BANK_BANKMONSTER		"拖动取出宠物。"		// 银行侧モンスター

	// 银行侧マネー
		#define MWONELINE_BANK_BANKMONEYNUM		"指定金额。"									// 0～9ボタン
		#define MWONELINE_BANK_BANKMONEYOK_ON	"取出指定金额。"								// ＯＫボタン
		#define MWONELINE_BANK_BANKMONEYOK_OFF	"指定金额。"								// ＯＫボタン（押せない）
		#define MWONELINE_BANK_BANKMONEYBS		"返回。"											// ＢＳボタン
		#define MWONELINE_BANK_BANKMONEYALL		"指定全额。"										// ＡＬＬボタン
		#define MWONELINE_BANK_BANKMONEYCLR		"将指定金额归0。"								// ＣＬＲボタン

//----------------------------------------
// エサ箱
	// 共通
		#define MWONELINE_FOODBOX_OK			"关闭这个窗口。"							// OKボタン

	// 自分侧アイテム
		#define MWONELINE_FOODBOX_MYITEM_ON		"拖动移动喂食箱。"			// 与えられるアイテム
		#define MWONELINE_FOODBOX_MYITEM_OFF	"无法给与这个物品。"				// 与えられないアイテム

//----------------------------------------
// 家族モンスター状态
	#define MWONELINE_GUILMONSTATUS_SET		"家族宠物无法更名。"					// SETボタン
	#define MWONELINE_GUILMONSTATUS_GIVE	"往这个窗口拖放物品喂食。"	// エサをやるボタン
	#define MWONELINE_GUILMONSTATUS_NONE	"从物品窗口拖动物品喂食。"	// カーソル未选择


//----------------------------------------
// 怪我治疗NPC
	#define MWONELINE_DOCTOR_NAME_ON		"治疗这个角色。"			// 各キャラ（怪我有り）
	#define MWONELINE_DOCTOR_NAME_OFF		"这个角色没受伤。"				// 各キャラ（怪我无し）
	#define MWONELINE_DOCTOR_ALL_ON			"治疗所有受伤的角色。"	// まとめて治疗（怪我人有り）
	#define MWONELINE_DOCTOR_ALL_OFF		"没有受伤的角色。"				// まとめて治疗（怪我人无し）
	#define MWONELINE_DOCTOR_CANCEL			"取消治疗。"									// キャンセル

//----------------------------------------
// トレードウィンドウ
	#define MWONELINE_TRADE_MYITEM_ON		"拖放这个物品 进行交易。"	// トレード可能アイテム（MyItem侧）
	#define MWONELINE_TRADE_MYITEM_OFF		"这个物品无法交易"						// トレード不可能アイテム（MyItem侧）
	#define MWONELINE_TRADE_OPENITEM		"拖放这个物品取消交易。"	// アイテム（Open侧）
	#define MWONELINE_TRADE_TRADEITEM		"交易这个物品给对方。"		// アイテム（Trade侧）
	#define MWONELINE_TRADE_MYMONSTER		"拖放这个宠物进行交易。"	// モンスターパネル（MyItem侧）
	#define MWONELINE_TRADE_OPENMONSTER		"拖放这个宠物取消交易。"	// モンスターパネル（Open侧）
	#define MWONELINE_TRADE_TRADEMONSTER	"交易这个宠物给对方。"						// モンスターパネル（Trade侧）
	// マネー表示（MyMoney）
		// ※银行系に准据

	#define MWONELINE_TRADE_MYITEMBUTTON	"显示我的物品。"				// アイテムボタン（MyMonster＆MyMonster时）
	#define MWONELINE_TRADE_OPENITEMBUTTON	"显示我要交易的物品。"	// アイテムボタン（Open侧）
	#define MWONELINE_TRADE_TRADEITEMBUTTON	"显示对方要交易的物品。"	// アイテムボタン（Trade侧）
	#define MWONELINE_TRADE_MYMONSBUTTON	"显示我的宠物。"				// モンスターボタン（MyItem＆MyMoney时）
	#define MWONELINE_TRADE_OPENMONSBUTTON	"显示我要交易的宠物。"	// モンスターボタン（Open＆Trade侧）
	#define MWONELINE_TRADE_TRADEMONSBUTTON	"显示对方要交易的宠物。"	// モンスターボタン（Trade侧）
	#define MWONELINE_TRADE_MYGOLDBUTTON	"指定要交易的金额。"						// マネーボタン（MyItem＆MyMonster时）
	#define MWONELINE_TRADE_OPENGOLDBUTTON	"显示我要交易的金额。"		// マネーボタン（Open＆Trade侧）
	#define MWONELINE_TRADE_TRADEGOLDBUTTON	"显示对方要交易的金额。"		// マネーボタン（Trade侧）

	#define MWONELINE_TRADE_ITEMOPEN		"向对方公开。"							// ItemOpen
	#define MWONELINE_TRADE_CLOSE			"取消公开。"						// Close
	#define MWONELINE_TRADE_TRADEOK			"确认交易。"								// TradeOK

//----------------------------------------
// 取引ＢＢＳ
#ifdef PUK3_PROF

	#define MWONELINE_MINIMAIL_HISTORY			"看这个迷你邮件的历史记录。"				// ヒストリーボタン
	#define MWONELINE_MINIMAIL_SEND				"发送迷你邮件。"						// Ｓｅｎｄボタン
	#define MWONELINE_MINIMAIL_DELETE			"删除这个迷你邮件的名片。"			// 削除ボタン
	#define MWONELINE_MINIMAIL_MINIBOOK			"切换迷你邮件表。"				// 家族ボタン

	#define MWONELINE_MINIMAIL_SELL				"设定卖出的分类"				//
	#define MWONELINE_MINIMAIL_BUY				"设定买入的分类"					//
	#define MWONELINE_MINIMAIL_ABOUT			"设定关于的分类"				//

	//BBS1
	#define MWONELINE_BBS1_CATEGOEY			"搜索这个分类。"						// 分类のパネルボタン

	//BBS2
	#define MWONELINE_BBS2_USER				"查看这个用户的信息。"					// パネル
	#define MWONELINE_BBS2_NEXT				"下一页。"									// →
	#define MWONELINE_BBS2_PREV				"前一页。"									// ←
	#define MWONELINE_BBS2_SEARCH			"关键字搜索。"							//サーチ
	#define MWONELINE_BBS2_SEARCH_STR		"输入搜索的关键字。"				//ダイアログ

	//BBS3
	#define MWONELINE_BBS3_SEND_MINIMAIL	"发送迷你邮件将使用%d金币。"			//ミニメールセンド


#endif
#endif

#endif
