﻿/************************/
/*	battleMenu.cpp		*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"
#include "../systeminc/pc.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/t_music.h"
#include "../systeminc/menu.h"
#include "../systeminc/map.h"
#include "../systeminc/field.h"
#include "../systeminc/mouse.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/process.h"
#include "../systeminc/battle.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/sndcnf.h"
#ifdef PUK2
	#include "../systeminc/login.h"
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"
#endif

// 攻击种类
//enum{ 
//	BATTLE_ATTACK,
//	BATTLE_JUJUTU,
//	BATTLE_CAPTURE,
//	BATTLE_ITEM,
//	BATTLE_WAZA,
//};

// トグル
//#define BATTLE_MENU				( 1 << 0 )
//#define BATTLE_MENU_PET			( 1 << 1 )

// メニューの阶层
//#define BATTLE_MENU_SUU					9
//#define BATTLE_MENU_FONTS				9
//#define BATTLE_MENU_JUJUTSU_FONTS		6
//#define BATTLE_MENU_ITEM_FONTS			6
//#define BATTLE_MENU_PET_FONTS			7
//#define BATTLE_MENU_WAZA_FONTS			9

// バトルコマンドサイズ
//#define BATTLE_COMMAND_SIZE			4096

// バトルボタンの数
//#define BATTLE_BUTTON_SUU	9

// コマンド受付时间
//#define BATTLE_CNT_DOWN_TIME 300000	// 五分
#define BATTLE_CNT_DOWN_TIME  30000	// ３０秒
//#define BATTLE_CNT_DOWN_TIME  10000	// １０秒
//#define BATTLE_CNT_DOWN_TIME  100000	// 90秒
//#define BATTLE_CNT_DOWN_TIME 10000	// １０秒
//#define BATTLE_CNT_DOWN_TIME 3000	// ３秒

// 自分のＩＤ
int BattleMyNo = 0;
// 自分のＩＤバックアップ
int BattleMyNoBak = 0;
#ifdef PUK3_RIDE_BATTLE
	// その场に出ているペットのインデックス
	int BattleNowPetNo;
	// ライドペットのインデックス
	int BattleRidePetNo;
#endif

// ＢＰフラグ
int BattleBpFlag = FALSE;
// 战闘时の自分のＭＰ
int BattleMyMp;
// 使用できるスキルフラグ
int BattleUsableSkillFlag;
// ペットの使用できるスキルフラグ
int BattlePetUsableSkillFlag;
#ifdef PUK2
// リバースの等级（０ならリバース使用不可）
int BattleRebirthLevel;
#endif

// バトルキャラクターフィールドフラグ
int BattleBcFieldFlag;

// 战闘强制終了フラグ
int BattleEscFlag = FALSE;
// ペットスタメンカウンター
//int BattlePetStMenCnt;

// コマンド番号
//int BattleCmdNo = -1;
// 咒术番号
//int BattleJujutuNo;
// 技番号
//int BattleWazaNo;
// 参战ペット番号バックアップ
//int battlePetNoBak = -2;
// 参战ペット番号バックアップ２
//int battlePetNoBak2 = -2;
// アイテム番号
//int BattleItemNo;
//int BattleItemNosafsff;

// プレイヤー逃げたフラグ
int battlePlayerEscFlag = FALSE;

// コマンド入力济みフラグ
int BattleAnimFlag;
// ターン受信フラグ
BOOL BattleTurnReceiveFlag;
// 现在のクライアントターン番号
int BattleCliTurnNo;
// 现在のサーバーターン番号
int BattleSvTurnNo;

// 战闘结果ウィンドウフラグ
//BOOL BattleResultWndFlag = FALSE;

// カウントダウン
DWORD BattleCntDown;
int BattleCntDownBak;
// カウントダウンフラグチェック
BOOL BattleCntDownFlag = FALSE;

// ターゲット选择フラグ
static int battleTargetSelectFlag = FALSE;

// ウィンドウアクションポインタ
//static ACTION *pActInfoWnd;
//static ACTION *pActWnd = NULL;

// バトルボタンの座标
//static int buttonX, buttonY, buttonA;

// ターゲットフラグ记忆
int	BattleTargetFlag;

// バトルメニュー初期化 ********************************************************/
void InitBattleMenu( void )
{

	BattleEscFlag = FALSE;				// 战闘强制終了フラグ初期化
	// 战闘结果ウィンドウフラグ
	//BattleResultWndFlag = FALSE;
	// 信息ウィンドウ抹杀
	//DeathAction( pActInfoWnd );
	//pActInfoWnd = NULL;
	
	// 战闘ウィンドウアクションポインタ
	pActBattleWnd = NULL;
}

// コマンド入力济みフラグのチェック ********************************************/
void CheckBattleAnimFlag( void )
{
	int i;
	
#ifdef PUK3_CMD_END_SET
	// 战闘中以外でも呼び出される可能性があるため
	if ( ProcNo != PROC_BATTLE ) return;
#endif
	// クライアントとサーバーのターンが同违う时、见ない
	if( BattleCliTurnNo != BattleSvTurnNo ) return;

	// ２０匹分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// キャラがいるとき
		if( pActBc[ i ] != NULL ){
			// 最下位ビットを判定
			if( ( BattleAnimFlag >> i ) & 0x00000001 ) pActBc[ i ]->atr |= ACT_ATR_BTL_CMD_END;
		}
	}
}

// コマンド入力济みフラグの初期化 ********************************************/
void InitBattleAnimFlag( void )
{
	int i;
	
	// ２０匹分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// キャラがいるとき
		if( pActBc[ i ] != NULL ){
			// 最下位ビットを判定
			pActBc[ i ]->atr &= ~ACT_ATR_BTL_CMD_END;
		}
	}
}


// ペット所持数を调べる *******************************************************/
int CheckPetSuu( void )
{
	int i,cnt = 0;
	
	for( i = 0 ; i < 5 ; i++ ){
		if( pet[ i ].useFlag == TRUE ) cnt++;
	}
	return cnt;
}

// 当たり判定ボックス消す *****************************************************/
void ClearBoxFlag( void )
{
	int i;
	
	// 当たり判定ボックス消す
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 当たり判定ボックス表示フラグＯＦＦ
		pActBc[ i ]->atr &= ~ACT_ATR_HIT_BOX;
		//pActBc[ i ]->atr &= ~ACT_ATR_HIT_BOX_ALL1;
		//pActBc[ i ]->atr &= ~ACT_ATR_HIT_BOX_ALL2;
		//pActBc[ i ]->atr &= ~ACT_ATR_HIT_BOX_ALL3;
		pActBc[ i ]->atr |= ACT_ATR_HIT;
		// 当たり判定番号初期化
		pActBc[ i ]->hitDispNo = -2;
	}
}

// 追加ボックス处理 ***********************************************************
void BattleTargetAddBox( int no )
{
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( no );
#endif
	// ヒットボックスフラグたっている时
	if( pActBc[ no ] != NULL && pActBc[ no ]->atr & ACT_ATR_HIT_BOX ){
		// ボックス表示データをバッファに溜める
		StockBoxDispBuffer( pActBc[ no ]->x + pActBc[ no ]->anim_x - 2, 
							pActBc[ no ]->y + pActBc[ no ]->anim_y - 2, 
							pActBc[ no ]->x + pActBc[ no ]->anim_x + SpriteInfo[ pActBc[ no ]->bmpNo ].width + 2, 
							pActBc[ no ]->y + pActBc[ no ]->anim_y + SpriteInfo[ pActBc[ no ]->bmpNo ].height + 2, 
							DISP_PRIO_BOX, BoxColor, 0 );
	}
}

// ターゲットとの当たり判定 ***************************************************/
int CheckBattelTarget( void )
{
	int i, j;
	
	// 周辺管理テーブル
	int circumferenceTable[ 4 ][ 5 ] = {	{ 14, 12, 10, 11, 13 },
											{ 19, 17, 15, 16, 18 },
											{  9,  7,  5,  6,  8 },
											{  4,  2,  0,  1,  3 }
										};
	int x, y, x2, y2;
	
	// キャラクターの数だけループ
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 当たっていたら
		if( pActBc[ i ]->hitDispNo == HitDispNo && pActBc[ i ]->atr & ACT_ATR_HIT_BOX ){
			// 单体选择のとき
			if( BattleTargetFlag & TARGET_SORO ){ 
#if 0
				// ターゲットが武器で影响するとき
				if( BattleTargetFlag & TARGET_CHECK_WEAPON ){
					// 自分の场所求める
					for( y2 = 0 ; y2 < 4 ; y2++ ){
						for( x2 = 0 ; x2 < 5 ; x2++ ){
							if( circumferenceTable[ y2 ][ x2 ] == i ){
								x = x2;
								y = y2;
							}
						}
					}
					// ブーメランのとき
					if( BattleBpFlag & BP_FLAG_WEAPON_BOOMERANG ){
						// 横一列ループ
						for( j = 0 ; j < 5 ; j++ ){
							// 自分のとき返回
							if( j == i ) continue;
							// 追加ボックス作成
							BattleTargetAddBox( circumferenceTable[ y ][ j ] );
						}
					}else
					// ナイフのとき
					if( BattleBpFlag & BP_FLAG_WEAPON_KNIFE ){
						// １段目のとき
						if( y == 0 ){
							// 追加ボックス作成
							BattleTargetAddBox( circumferenceTable[ y + 1 ][ x ] );	// 下
						}else
						// ２段目のとき
						if( y == 1 ){
							// 追加ボックス作成
							BattleTargetAddBox( circumferenceTable[ y - 1 ][ x ] );	// 上
						}else
						// ３段目のとき
						if( y == 2 ){
							// 追加ボックス作成
							BattleTargetAddBox( circumferenceTable[ y + 1 ][ x ] );	// 下
						}else
						// ４段目のとき
						if( y == 3 ){
							// 追加ボックス作成
							BattleTargetAddBox( circumferenceTable[ y - 1 ][ x ] );	// 上
						}
					}
				}
#endif
				return i;
			}
			// 周辺のとき
			if( BattleTargetFlag & TARGET_CIRCUMFERENCE ){
				// 自分の场所求める
				for( y2 = 0 ; y2 < 4 ; y2++ ){
					for( x2 = 0 ; x2 < 5 ; x2++ ){
						if( circumferenceTable[ y2 ][ x2 ] == i ){
							x = x2;
							y = y2;
						}
					}
				}
				
				// １段目のとき
				if( y == 0 ){
					// 追加ボックス作成
					BattleTargetAddBox( circumferenceTable[ y + 1 ][ x ] );	// 下
					if( x + 1 <= 4 ) BattleTargetAddBox( circumferenceTable[ y ][ x + 1 ] );	// 右
					if( x - 1 >= 0 ) BattleTargetAddBox( circumferenceTable[ y ][ x - 1 ] );	// 左
				}else
				// ２段目のとき
				if( y == 1 ){
					// 追加ボックス作成
					BattleTargetAddBox( circumferenceTable[ y - 1 ][ x ] );	// 上
					if( x + 1 <= 4 ) BattleTargetAddBox( circumferenceTable[ y ][ x + 1 ] );	// 右
					if( x - 1 >= 0 ) BattleTargetAddBox( circumferenceTable[ y ][ x - 1 ] );	// 左
				}else
				// ３段目のとき
				if( y == 2 ){
					// 追加ボックス作成
					BattleTargetAddBox( circumferenceTable[ y + 1 ][ x ] );	// 下
					if( x + 1 <= 4 ) BattleTargetAddBox( circumferenceTable[ y ][ x + 1 ] );	// 右
					if( x - 1 >= 0 ) BattleTargetAddBox( circumferenceTable[ y ][ x - 1 ] );	// 左
				}else
				// ４段目のとき
				if( y == 3 ){
					// 追加ボックス作成
					BattleTargetAddBox( circumferenceTable[ y - 1 ][ x ] );	// 上
					if( x + 1 <= 4 ) BattleTargetAddBox( circumferenceTable[ y ][ x + 1 ] );	// 右
					if( x - 1 >= 0 ) BattleTargetAddBox( circumferenceTable[ y ][ x - 1 ] );	// 左
				}
				
				return i + 20;
			}
			// 片侧
			if( BattleTargetFlag & TARGET_SIDE ){ 
				// ターゲットが右下サイドのとき
				if( i < 10 ){ 
					// 右下片侧分ループ
					for( j = 0 ; j < 10 ; j++ ){
						// 自分のとき返回
						if( j == i ) continue;
						// 追加ボックス作成
						BattleTargetAddBox( j );
					}
					
					return 40;
				}
				// ターゲットが左上サイドのとき
				else{
					// 右下片侧分ループ
					for( j = 10 ; j < 20 ; j++ ){
						// 自分のとき返回
						if( j == i ) continue;
						// 追加ボックス作成
						BattleTargetAddBox( j );
					}
					return 41;
				}
			}
			// 全体
			if( BattleTargetFlag & TARGET_ALL ){ 
				// 全体分ループ
				for( j = 0 ; j < 20 ; j++ ){
					// 自分のとき返回
					if( j == i ) continue;
					// 追加ボックス作成
					BattleTargetAddBox( j );
				}
				
				return 42;
			}
		}
	}
	// 当たっていない时
	return -1;
}

#ifndef PUK2_NEW_MENU

// 耐久力メーター表示 ***********************************************************/
void HpMeterDisp( int no )
{
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( no );
#endif
	int meterX = pActBc[ no ]->x;
	int meterY = pActBc[ no ]->y - 80;
	int graNo;
	
	// 存在しない又は死んでる时、返回
	if( pActBc[ no ]->func == NULL || pActBc[ no ]->hp <= 0 ) return;
	// 旅行中の时
	if( pActBc[ no ]->atr & ACT_ATR_TRAVEL ) return;
	
	// 自分との时
	if( no == BattleMyNo ){
		// 表示しない时
		//if( CheckBattle1P2P() == 1 
		//	&& BattleBpFlag & BP_FLAG_PLAYER_MENU_NON 
		//	&& ( BattleBpFlag & BATTLE_BP_PET_MENU_NON || pActBc[ BattleMyNo + 5 ]->hp <= 0 ) ) return;
		
		// Ｐ表示
		//StockFontBuffer( meterX - 12, meterY - 8, FONT_PRIO_BACK, FONT_PAL_GREEN, "Player", 0 );
		//StockFontBuffer( meterX - 12 - 20, meterY - 8, FONT_PRIO_BACK, FONT_PAL_GREEN, "P", 0 );
		// 枠表示
		StockDispBuffer( meterX, meterY, DISP_PRIO_METER, CG_B_PLAYER_GAUGE, 0 );
		// 耐久力表示
		//StockBoxDispBuffer( meterX - 22,
		//					meterY - 2,
		//					meterX - 22 + (int)( ( (double)pActBc[ BattleMyNo ]->hp / (double)pActBc[ BattleMyNo ]->maxHp ) * 40.0 ),
		//					meterY - 2,
		//					DISP_PRIO_IME2, SYSTEM_PAL_GREEN, 2 );
		StockBoxDispBuffer( meterX - 22,
							meterY - 1,
							meterX - 22 + ( int )( ( ( float )pActBc[ BattleMyNo ]->hp / ( float )pActBc[ BattleMyNo ]->maxHp ) * 40.0 ),
							meterY - 1,
							DISP_PRIO_METER2, SYSTEM_PAL_GREEN, 2 );
		StockBoxDispBuffer( meterX - 22,
							meterY - 0,
							meterX - 22 + ( int )( ( ( float )pActBc[ BattleMyNo ]->hp / ( float )pActBc[ BattleMyNo ]->maxHp ) * 40.0 ),
							meterY - 0,
							DISP_PRIO_METER2, SYSTEM_PAL_GREEN2, 2 );
		// 气力表示
		//StockBoxDispBuffer( meterX - 22,
		//					meterY + 2,
		//					meterX - 22 + (int)( ( (float)pActBc[ BattleMyNo ]->fp / 1000.0 ) * 40.0 ),
		//					meterY + 2,
		//					DISP_PRIO_METER2, SYSTEM_PAL_YELLOW, 2 );
		StockBoxDispBuffer( meterX - 22,
							meterY + 3,
							meterX - 22 + ( int )( ( ( float )pActBc[ BattleMyNo ]->fp / ( float )pActBc[ BattleMyNo ]->maxFp ) * 40.0 ),
							meterY + 3,
							DISP_PRIO_METER2, SYSTEM_PAL_YELLOW, 2 );
		StockBoxDispBuffer( meterX - 22,
							meterY + 4,
							meterX - 22 + ( int )( ( ( float )pActBc[ BattleMyNo ]->fp / ( float )pActBc[ BattleMyNo ]->maxFp ) * 40.0 ),
							meterY + 4,
							DISP_PRIO_METER2, SYSTEM_PAL_YELLOW2, 2 );
	}else{	// 自分以外の时
		
		// 自分のペットの时かつ、観战じゃない时
		if( BattleMyNo < BC_MAX && no == BattleCheckPetId( BattleMyNo ) ) graNo = CG_B_PET_GAUGE;
		else graNo = CG_B_OTHER_GAUGE;
		// 枠表示
		StockDispBuffer( meterX , meterY, DISP_PRIO_METER, graNo, 0 );
		// 耐久力表示（１ライン目）
		StockBoxDispBuffer( meterX - 22,
							meterY - 1,
							meterX - 22 + ( int )( ( ( float )pActBc[ no ]->hp / ( float )pActBc[ no ]->maxHp ) * 40.0 ),
							meterY - 1,
							DISP_PRIO_METER2, SYSTEM_PAL_GREEN, 2 );
		// 耐久力表示（２ライン目）
		StockBoxDispBuffer( meterX - 22,
							meterY + 0,
							meterX - 22 + ( int )( ( ( float )pActBc[ no ]->hp / ( float )pActBc[ no ]->maxHp ) * 40.0 ),
							meterY + 0,
							DISP_PRIO_METER2, SYSTEM_PAL_GREEN2, 2 );
		// 气力表示
		StockBoxDispBuffer( meterX - 22,
							meterY + 3,
							meterX - 22 + ( int )( ( ( float )pActBc[ no ]->fp / ( float )pActBc[ no ]->maxFp ) * 40.0 ),
							meterY + 3,
							DISP_PRIO_METER2, SYSTEM_PAL_YELLOW, 2 );
		StockBoxDispBuffer( meterX - 22,
							meterY + 4,
							meterX - 22 + ( int )( ( ( float )pActBc[ no ]->fp / ( float )pActBc[ no ]->maxFp ) * 40.0 ),
							meterY + 4,
							DISP_PRIO_METER2, SYSTEM_PAL_YELLOW2, 2 );
	}
}

#endif

#ifdef PUK2_ELEMENTDISP
// 属性表示 ***********************************************************/
void ElementDisp()
{
	int i;

	for( i  = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// 属性表示
		if( pActBc[ i ] == NULL ) continue;

		// フォントバッファにためる
		if (pActBc[ i ]->earth) StockFontBuffer( pActBc[ i ]->x-30, pActBc[ i ]->y - 40, FONT_PRIO_BACK, 0, FONT_PAL_GREEN,	"地　　　", 0, 0 ); //MLHIDE
		if (pActBc[ i ]->water) StockFontBuffer( pActBc[ i ]->x-30, pActBc[ i ]->y - 40, FONT_PRIO_BACK, 0, FONT_PAL_AQUA,	"　水　　", 0, 0 ); //MLHIDE
		if (pActBc[ i ]->fire) StockFontBuffer( pActBc[ i ]->x-30, pActBc[ i ]->y - 40, FONT_PRIO_BACK, 0, FONT_PAL_RED,	"　　火　", 0, 0 ); //MLHIDE
		if (pActBc[ i ]->wind) StockFontBuffer( pActBc[ i ]->x-30, pActBc[ i ]->y - 40, FONT_PRIO_BACK, 0, FONT_PAL_YELLOW,	"　　　风", 0, 0 ); //MLHIDE
	}
}
#endif

// バトルメニュープロセス番号
B_MENU_PROC_NO BattleMenuProcNo;
// プレイヤーコマンド入力济みフラグ
int BattleCmdPlayerInputFlag;
// ペットコマンド入力济みフラグ
int BattleCmdPetInputFlag;

// バトルメニュー座标
int battleMenuX = 490;
//int battleMenuY = 50;
int battleMenuY = -104;
int battleMenuA = 17;

// 战闘ボタンの数
#ifdef PUK2
bool rebirthflg;

#define B_MENU_BUTTON_MAX 9
#else
#define B_MENU_BUTTON_MAX 8
#endif
// 战闘ボタンの当たり判定番号记忆用
int BattleMenuButtonHitDispNo[ B_MENU_BUTTON_MAX ];
// 战闘ボタンのフラグ记忆用
int BattleMenuButtonFlag[ B_MENU_BUTTON_MAX ];
// 战闘ボタンフラグバックアップ
int BattleMenuButtonFlagBak;
int BattleMenuButtonFlagBak1;
int BattleMenuButtonFlagBak2;
// ２アクション时の使用できないボタンフラグ
int BattleUnusableButtonFlag;

// 战闘ウィンドウアクションポインタ
ACTION *pActBattleWnd;

// バトル信息ウィンドウ *****************************************************
void BattleInfoWndDisp( void )
{
	int i;
	int flag = FALSE;
	
	// ウィンドウ加速中は表示しない
	if( battleMenuA != 0 ) return;
	
	// 选择できるかチェック
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 当たり判定ボックス表示フラグＯＮの时
		if( pActBc[ i ]->atr & ACT_ATR_HIT_BOX ){ 
			flag = TRUE;
			break;
		}
	}
	
	// ターゲット选择できるとき
	if( flag == TRUE ){
		
		int infoWndX = 366, infoWndY = 91;
		
		// モンスターメニューの时
		if( BattleMenuProcNo == B_MENU_PET ) infoWndY -= 3;
		
		// ウィンドウ表示
		StockDispBuffer( infoWndX + 136, infoWndY + 20, DISP_PRIO_MENU, CG_B_INFO_WND, 0 );
		// 文字表示
		StockFontBuffer( infoWndX + 17, infoWndY + 12, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(136, "请选择目标"), 0 );
		
		// 一行インフォ
		strcpy( OneLineInfoStr,ML_STRING(137, "右键取消。"));
	}
}

#if 0

// 战闘ボタンの当たり判定初期化 *********************************************************/
void InitBattleMenuButtonHitDispNo( void )
{
	int i;
	
	// ボタンの数だけループ
	for( i = 0 ; i < B_MENU_BUTTON_MAX ; i++ ){
		BattleMenuButtonHitDispNo[ i ] = -2;
	}
}
#endif

// 战闘ボタンのフラグ初期化 *************************************************************/
void InitBattleMenuButtonFlag( void )
{
	int i;
	
	// ボタンの数だけループ
	for( i = 0 ; i < B_MENU_BUTTON_MAX ; i++ ){
		BattleMenuButtonFlag[ i ] = 0;
	}
}

// 当たり判定初期化 *************************************************************/
void InitHitNo( int *array, int max )
{
	int i;
	
	// ボタンの数だけループ
	for( i = 0 ; i < max ; i++ ){
		array[ i ] = -2;
	}
}

// アイテム栏に何个空きがあるかチェック *******************************************/
//
// 戾り值：空きの数
//
// ********************************************************************************/
int CheckItemBlank( void ){
	
	int i,j = 0;
	
	// アイテム分ループ
	for( i = 8 ; i < MAX_ITEM ; i++ ){
		// アイテムがなかったらカウント
		if( pc.item[ i ].useFlag == FALSE ) j++;
	}
	
	return j;
}

// 战闘にでいてるペットの番号を调べる *********************************************/
//
// 戾り值：ペット栏の番号（出てないとき－１)
//
//*********************************************************************************/
int CheckBattlePet( void )
{
	int i;
	
	// ペットの数分ループ
	for( i = 0 ; i < MAX_PET ; i++ ){
		// この栏にペットがいる时
		if( pet[ i ].useFlag == TRUE ){
			// 战闘ペットだったら
			if( pet[ i ].battleSetting == PET_SETTING_BATTLE ) return i;
		}
	}
	
	return -1;
}

// ペットのバックアップ番号のクリア *****************************************************/
//void ClearBattlePetBakUp( void )
//{
	// ペットなしにする
//	BattlePetNoBak = -1;
//}

// ターン每のバトルメニュー初期化 *******************************************************/
void InitTurnBattleMenu( void )
{
	// カウントダウンフラグ初期化
	BattleCntDownFlag = TRUE;
	// コマンド受付时间初期化
	BattleCntDown = GetTickCount() + BATTLE_CNT_DOWN_TIME;
	BattleCntDownBak = 0;
	// バトルメニュープロセス番号初期化
	BattleMenuProcNo = B_MENU_PLAYER_INIT;
	// プレイヤーコマンド入力济みフラグ
	BattleCmdPlayerInputFlag = 0;
	// ペットコマンド入力济みフラグ
	BattleCmdPetInputFlag = 0;
	// コマンド入力济みフラグの初期化
	InitBattleAnimFlag();
	// コマンド入力济みフラグのチェック
	CheckBattleAnimFlag();
	// バトルメニュー座标
	battleMenuX = 493;
	//int battleMenuY = 50;
	battleMenuY = -106;
	battleMenuA = 17;
	// ２アクション时の使用できないボタンフラグ初期化
	BattleUnusableButtonFlag = FALSE;
	// 前回と违うペットの场合
	if( BattlePetNoBak != CheckBattlePet() ){
		// スキル番号バックアップ初期化
		BattlePetSkillSelectNo = -1;
	}
}
	

// カウントダウン处理 **********************************************************/
void BattleCntDownDisp( void )
{
	char moji[ 16 ];
	char *work = moji;
	int x = 320 - 16, y = 240, i;
	// 残り时间
	int BattleCntDownRest = BattleCntDown - GetTickCount();
	int flag = FALSE;
	int cntGraNo;
	
	// フラグチェック
	if( BattleCntDownFlag == FALSE ) return;
	
	// タイムオーバーの时
	if( BattleCntDownRest <= 0 ){
		// フラグ初期化
		BattleCntDownFlag = FALSE;
		BattleCntDownRest = 0;
		// 当たり判定ボックス消す
		ClearBoxFlag();
		// ウィンドウアクション抹杀
//		DeathAction( pActWnd );
		// ウィンドウポインタ初期化
//		pActWnd = NULL;
		// ボタンフラグ全初期化
//		ClearBattleButton();
		// このボタンだけＯＮ
		//battleButtonFlag[ 4 ] = TRUE;
		
		// ペットが参战しているとき
		if( BattleBpFlag & BP_FLAG_PET ){
		//if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
			// プレイヤーコマンド入力济みフラグ
			if( BattleCmdPlayerInputFlag == FALSE ){
				// メニュー无しの时、または観战の时
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ) ){
					// プレイヤー何もしない送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// 强制終了フラグＯＮ
					flag = TRUE;
				}
			}
			// ペットコマンド入力济みフラグ
			if( BattleCmdPetInputFlag == FALSE ){
				// ペット何もしない送信
				nrproto_B_send( sockfd, "W|FF" );                                 //MLHIDE
				// 强制終了フラグＯＮ
				flag = TRUE;
			}
		}else{
			// プレイヤーコマンド入力济みフラグ
			if( BattleCmdPlayerInputFlag == FALSE ){
				// メニュー无しの时、または観战の时
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ) ){
					// プレイヤー何もしない２回送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// 强制終了フラグＯＮ
					flag = TRUE;
				}
			}
			// プレイヤーコマンド入力济みフラグ
			if( BattleCmdPlayerInputFlag == TRUE ){
				// メニュー无しの时、または観战の时
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ) ){
					// プレイヤー何もしない送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// 强制終了フラグＯＮ
					flag = TRUE;
				}
			}
		}
#if 0		
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo + 5 );
#endif
		// プレイヤーの行动选择中の时、两方何も出来ない
		if( battleMenuFlag & BATTLE_MENU && battleMenuReturn == FALSE ){
			// プレイヤー何もしない送信
			nrproto_B_send( sockfd, "N" );                                     //MLHIDE
			
			// 参战してない时、又はペット死んでいる时
			if( battlePetNoBak == -1 || 
				pActBc[ BattleMyNo + 5 ]->hp <= 0 ){
				// 何も送らない
			}else{
				// ペット何もしないを送信
				nrproto_B_send( sockfd, "W|FF|FF" );                              //MLHIDE
			}
		}else{
			// ペットの行动选择中の时
			if( ( battleMenuFlag & BATTLE_MENU && battleMenuReturn == TRUE ) ||
				( battleMenuFlag & BATTLE_MENU_PET && battleMenuReturn == FALSE ) ){ 
				// 参战してない时、又はペット死んでいる时
				if( battlePetNoBak == -1 || 
					pActBc[ BattleMyNo + 5 ]->hp <= 0 ){
					// 何も送らない
				}else{
					// ペット何もしないを送信
					nrproto_B_send( sockfd, "W|FF|FF" );                             //MLHIDE
				}
			}
		}
#endif		
		// フラグ初期化
		//battleTargetSelectFlag = FALSE;
//		battleMenuReturn = TRUE;
		//battleMenuFlag = FALSE;
		
		// 强制終了フラグＯＮの时
		if( flag == TRUE ){
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
			// 次のプロセスへ
			SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
			// 信息ウィンドウ抹杀
//			DeathAction( pActInfoWnd );
//			pActInfoWnd = NULL;
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
		}
	}
	
	// １０秒より小さいとき
	if( BattleCntDownRest < 1000 * 10 ){ 
		cntGraNo = CG_B_CNT_DOWN2_0;
		if( BattleCntDownRest / 1000 != BattleCntDownBak ){
			// カウントダウンの音
			play_se( 202, 320, 240 );
			// バックアップする
			BattleCntDownBak = BattleCntDownRest / 1000;
		}
	}
	else cntGraNo = CG_B_CNT_DOWN_0;
	
	// 文字列に变换（二桁表示）
	sprintf( moji, "%2d", BattleCntDownRest / 1000 );                    //MLHIDE
	
	// 文字の数だけループ
	for( i = 0 ; i < 2 ; i++ ){
		// 空白でない时
		if( *work != 0x20 ){
			// 表示
			StockDispBuffer( x, y, DISP_PRIO_CNT_DOWN, *work - '0' + cntGraNo, 0 );
		}
		// Ｘ座标移动
		x += 32;
		// ポインタ进める
		work++;
	}
	//デュエルなら
	if( DuelFlag == TRUE ){
		// 文字列に变换（二桁表示）
		sprintf( moji, ML_STRING(138, "第 %d 回合"), BattleCliTurnNo + 1 );
		// 表示
		StockFontBuffer( 320 - 32, 240 - 60, FONT_PRIO_BACK, FONT_PAL_YELLOW, moji, 0 );
		
	}
}

// ターゲットボックス作成 ***************************************************************/
BOOL BattleSetTargetBox( int id, int flag )
{
	int i;
	int petId;
#ifdef PUK3_TARGET_FRIEND_PLAYER
	BOOL cflag;
#endif

	// ターゲットフラグ记忆
	BattleTargetFlag = flag;
	
	// ペットＩＤ求める
	petId = BattleCheckPetId( id );
	
	// 谁も选择しないとき返回
	if( !( flag & TARGET_WHO ) ) return FALSE;
	
	// ペット含むとき *****************************
	if( flag & TARGET_IN_PET ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( petId );
#endif
		// ポインタあったら
		if( pActBc[ petId ] != NULL ){
			// 死んでるとき
			if( pActBc[ petId ]->hp <= 0 ){
				// 死んでる人含むとき
				if( flag & TARGET_IN_DEAD ){
					// 当たり判定ボックス表示フラグＯＮ
					pActBc[ petId ]->atr |= ACT_ATR_HIT_BOX;
				}
			}else{
				// 当たり判定ボックス表示フラグＯＮ
				pActBc[ petId ]->atr |= ACT_ATR_HIT_BOX;
			}
		}
	}
	
	// 自分含むとき *******************************
	if( flag & TARGET_IN_ME ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( id );
#endif
		// ポインタあったら
		if( pActBc[ id ] != NULL ){
			// 死んでるとき
			if( pActBc[ id ]->hp <= 0 ){
				// 死んでる人含むとき
				if( flag & TARGET_IN_DEAD ){
					// 当たり判定ボックス表示フラグＯＮ
					pActBc[ id ]->atr |= ACT_ATR_HIT_BOX;
				}
			}else{
				// 当たり判定ボックス表示フラグＯＮ
				pActBc[ id ]->atr |= ACT_ATR_HIT_BOX;
			}
		}
	}
	
	// 味方（自分とペット以外）含むとき ***********
#ifdef PUK3_TARGET_FRIEND_PLAYER
	// 自分以外の味方プレイヤー ***********
	if( flag & (TARGET_IN_FRIEND|TARGET_IN_FRIENDPLAYER) ){
#else
	if( flag & TARGET_IN_FRIEND ){
#endif
		// 自分が右下サイドのとき
		if( id < 10 ){
			// 当たり判定ボックス表示
			for( i = 0 ; i < 10 ; i++ ){
				// 自分のＩＤのとき返回
				if( i == id ) continue;
				// ペットのＩＤのとき返回
				if( i == petId ) continue;
				
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタがない时返回
				if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_TARGET_FRIEND_PLAYER
				if ( !(flag & TARGET_IN_FRIEND) ){
					cflag = FALSE;
					if ( flag & TARGET_IN_FRIENDPLAYER ){
						// プレイヤーキャラのとき
						if( ( (BC_YOBI *)pActBc[ i ]->pYobi )->bcFlag & BC_FLAG_PLAYER ) cflag = TRUE;
					}
					if (!cflag) continue;
				}
#endif
				
				// 死んでるとき
				if( pActBc[ i ]->hp <= 0 ){
					// 死んでる人含むとき
					if( flag & TARGET_IN_DEAD ){
						// 当たり判定ボックス表示フラグＯＮ
						pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
					}
				}else{
					// 当たり判定ボックス表示フラグＯＮ
					pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
				}
			}
		}
		// 自分が左下サイドのとき
		else{
			// 当たり判定ボックス表示
			for( i = 10 ; i < 20 ; i++ ){
				// 自分のＩＤのとき返回
				if( i == id ) continue;
				// ペットのＩＤのとき返回
				if( i == petId ) continue;
				
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタがない时返回
				if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_TARGET_FRIEND_PLAYER
				if ( !(flag & TARGET_IN_FRIEND) ){
					cflag = FALSE;
					if ( flag & TARGET_IN_FRIENDPLAYER ){
						// プレイヤーキャラのとき
						if( ( (BC_YOBI *)pActBc[ i ]->pYobi )->bcFlag & BC_FLAG_PLAYER ) cflag = TRUE;
					}
					if (!cflag) continue;
				}
#endif
				
				// 死んでるとき
				if( pActBc[ i ]->hp <= 0 ){
					// 死んでる人含むとき
					if( flag & TARGET_IN_DEAD ){
						// 当たり判定ボックス表示フラグＯＮ
						pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
					}
				}else{
					// 当たり判定ボックス表示フラグＯＮ
					pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
				}
			}
		}
		
	}
	
	// 敌含むとき ********************************
	if( flag & TARGET_IN_ENEMY ){
		// 自分が右下サイドのとき
		if( id < 10 ){
			// 当たり判定ボックス表示
			for( i = 10 ; i < 20 ; i++ ){
				
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタがない时返回
				if( pActBc[ i ] == NULL ) continue;
				
				// 死んでるとき
				if( pActBc[ i ]->hp <= 0 ){
					// 死んでる人含むとき
					if( flag & TARGET_IN_DEAD ){
						// 当たり判定ボックス表示フラグＯＮ
						pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
					}
				}else{
					// 当たり判定ボックス表示フラグＯＮ
					pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
				}
			}
		}
		// 自分が左下サイドのとき
		else{
			// 当たり判定ボックス表示
			for( i = 0 ; i < 10 ; i++ ){
				
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタがない时返回
				if( pActBc[ i ] == NULL ) continue;
				
				// 死んでるとき
				if( pActBc[ i ]->hp <= 0 ){
					// 死んでる人含むとき
					if( flag & TARGET_IN_DEAD ){
						// 当たり判定ボックス表示フラグＯＮ
						pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
					}
				}else{
					// 当たり判定ボックス表示フラグＯＮ
					pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
				}
			}
		}
	}
	
	// 前卫后卫ターゲットの制限处理 **********************
	
	// 周辺管理テーブル
	int circumferenceTable[ 4 ][ 5 ] = {	{ 14, 12, 10, 11, 13 },
											{ 19, 17, 15, 16, 18 },
											{  9,  7,  5,  6,  8 },
											{  4,  2,  0,  1,  3 }
										};
	int x, y, x2, y2;
	
	// ターゲットが武器で影响するかつ、直接攻击のとき
	if( flag & TARGET_CHECK_WEAPON && BattleBpFlag & BP_FLAG_WEAPON_DIRECT || flag & TARGET_CHECK_WEAPON && id != BattleMyNo ){
	
		// 自分の场所求める
		for( y2 = 0 ; y2 < 4 ; y2++ ){
			for( x2 = 0 ; x2 < 5 ; x2++ ){
				if( circumferenceTable[ y2 ][ x2 ] == id ){
					x = x2;
					y = y2;
				}
			}
		}
		
		// １段目のとき
		if( y == 0 ){
			// 横ループ
			for( i = 0 ; i < 5 ; i++ ){	
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( circumferenceTable[ y + 1 ][ x ] );
				CheckIdRange( circumferenceTable[ y + 2 ][ i ] );
#endif
				// ２枚壁があるとき
				if( pActBc[ circumferenceTable[ y + 1 ][ x ] ] != NULL && pActBc[ circumferenceTable[ y + 1 ][ x ] ]->atr & ACT_ATR_HIT_BOX
					&& pActBc[ circumferenceTable[ y + 2 ][ i ] ] != NULL && pActBc[ circumferenceTable[ y + 2 ][ i ] ]->atr & ACT_ATR_HIT_BOX ){
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( circumferenceTable[ y + 3 ][ i ] );
#endif
					// ３枚目がいるとき
					if( pActBc[ circumferenceTable[ y + 3 ][ i ] ] != NULL ){
						// 三枚目は选べなくする
						pActBc[ circumferenceTable[ y + 3 ][ i ] ]->atr &= ~ACT_ATR_HIT_BOX;
					}
				}
			}
		}else
		// ４段目のとき
		if( y == 3 ){
			// 横ループ
			for( i = 0 ; i < 5 ; i++ ){	
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( circumferenceTable[ y - 1 ][ x ] );
				CheckIdRange( circumferenceTable[ y - 2 ][ i ] );
#endif
				// ２枚壁があるとき
				if( pActBc[ circumferenceTable[ y - 1 ][ x ] ] != NULL && pActBc[ circumferenceTable[ y - 1 ][ x ] ]->atr & ACT_ATR_HIT_BOX
					&& pActBc[ circumferenceTable[ y - 2 ][ i ] ] != NULL && pActBc[ circumferenceTable[ y - 2 ][ i ] ]->atr & ACT_ATR_HIT_BOX ){
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( circumferenceTable[ y - 3 ][ i ] );
#endif
					// ３枚目がいるとき
					if( pActBc[ circumferenceTable[ y - 3 ][ i ] ] != NULL ){
						// 三枚目は选べなくする
						pActBc[ circumferenceTable[ y - 3 ][ i ] ]->atr &= ~ACT_ATR_HIT_BOX;
					}
				}
			}
		}
	}
	
	return TRUE;
}



// 战闘スキル最大值
#define B_SKILL_WND_HIT_NO_MAX 11
// 战闘アビリティ最大值
#define B_ABILITY_WND_HIT_NO_MAX 11

// 战闘スキル选择番号
int BattleSelectSkillNo;
// 战闘アビリティ选择番号
int BattleSelectAbilityNo;
// 战闘スキルウィンドウプロセス番号
int BattleSkillWndProcNo = 0;
// 战闘スキル文字当たり判定番号
int BattleSkillHitFontNo[ B_SKILL_WND_HIT_NO_MAX ];
// 战闘アビリティ文字当たり判定番号
int BattleAbilityHitFontNo[ B_ABILITY_WND_HIT_NO_MAX ];

// コマンドインプットフラグ
int BattleMenuInputFlag;

// ●バトルスキルウィンドウ处理 *************************************************/
void BattleSkillWnd( void )
{
	int i;
	int bJobNo;
	char moji[ 256 ];
	
	// 战闘系スキル番号取得
	if( job.kind == 1 ) bJobNo = 0;
	else bJobNo = 1;
	
	// プロセス番号で分岐
	switch( BattleSkillWndProcNo ){
	
	case 0:	// スキルウィンドウのとき *******************************************/
	
		// ウィンドウがないとき
		if( pActBattleWnd == NULL ){
			// ウィンドウ表示タスク作成
			pActBattleWnd = makeWindowDisp( 364, 91, 272, 330, -2 );
			// 当たり判定初期化
			InitHitNo( BattleSkillHitFontNo, B_SKILL_WND_HIT_NO_MAX );
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
		}else{
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBattleWnd );
#endif
			// ウィンドウ出来上がっていたら
			if( pActBattleWnd->hp > 0 ){
				int restSlot = 10;	// 残りスロット数
				// スキルウィンドウ
				StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU, CG_B_SKILL_WND, 1 );
				// マウスの当たり判定が无い时
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(139, "右键关闭技能窗口。"));
				// スキル分ループ
				for( i = 0 ; i < B_SKILL_WND_HIT_NO_MAX - 1; i++ ){
					// ソート番号取り出し
					int index = job.sortSkill[ i ].index;
					// 文字表示
					if( BattleSkillHitFontNo[ index ] == HitFontNo ){
						// 使用できない时（灰色）
						if( !( BattleUsableSkillFlag & ( 1 << index ) ) ){
							// 一行インフォ
							strcpy( OneLineInfoStr,ML_STRING(140, "无法使用这个技能。"));
							
						}else{
							// 一行インフォ
							strcpy( OneLineInfoStr,ML_STRING(141, "打开这个技能的能力窗口。"));
						}
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// 使用できない时（灰色）
							if( !( BattleUsableSkillFlag & ( 1 << index ) ) ){

								if( job.skill[ index ].id == 72){
									if(CheckItemBlank() == 0){
										StockChatBufferLine( ML_STRING(142, "携带物品太多了，无法使用。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
									}
								}
								
								// ＮＧ音
								play_se( SE_NO_NG, 320, 240 );
							
							}else{
							
								// スキル番号决定
								BattleSelectSkillNo = index;
								// アビリティ选择ウィンドウへ
								BattleSkillWndProcNo = 1;
								
								// ウィンドウアクション抹杀
								DeathAction( pActBattleWnd );
								// ポインタ初期化
								pActBattleWnd = NULL;
								
								// ウィンドウ开く音
								play_se( SE_NO_OPEN_WINDOW, 320, 240 );
								
								break;
							}
						}
					}
				}
				// キャンセルボタンの时
				if( BattleSkillHitFontNo[ B_SKILL_WND_HIT_NO_MAX - 1 ] == HitDispNo ){
					// 一行インフォ
					strcpy( OneLineInfoStr,ML_STRING(143, "关闭这个窗口。"));
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// ウィンドウアクション抹杀
						DeathAction( pActBattleWnd );
						// ポインタ初期化
						pActBattleWnd = NULL;
						// アビリティ选择ウィンドウへ
						//BattleSkillWndProcNo = 0;
						// ボタンもどす
						BattleMenuButtonFlag[ 2 ] = 0;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 右クリックされた时
				if( mouse.onceState & MOUSE_RIGHT_CRICK ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// アビリティ选择ウィンドウへ
					//BattleSkillWndProcNo = 0;
					// ボタンもどす
					BattleMenuButtonFlag[ 2 ] = 0;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
					
				
				// ウィンドウがあるとき
				if( pActBattleWnd == NULL ) break;
				
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( BattleMyNo );
#endif
				// フォースポイント表示
				sprintf( moji, "%4d/%4d", pActBc[ BattleMyNo ]->fp, pActBc[ BattleMyNo ]->maxFp ); //MLHIDE
				//sprintf( moji, "%4d/9999", pActBc[ BattleMyNo ]->fp );
				//sprintf( moji, "9999" );
				StockFontBuffer( pActBattleWnd->x + 182, pActBattleWnd->y + 10, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
				
				// スキル分ループ
				for( i = 0 ; i < B_SKILL_WND_HIT_NO_MAX - 1; i++ ){
					// ソート番号取り出す
					int index = job.sortSkill[ i ].index;
					// 文字列あるとき
					if( job.skill[ index ].name[ 0 ] != NULL ){
						// 残りスロット计算
						restSlot -= job.skill[ index ].slot;
						// スキルベース表示
						StockDispBuffer( pActBattleWnd->x + 9 + 127, pActBattleWnd->y + 54 + i * 24 + 10, DISP_PRIO_MENU + 1, CG_B_SKILL_BASE, 0 );
						
						// 文字色
						int color = FONT_PAL_WHITE;
						// 使用できない时（赤）
						if( !( BattleUsableSkillFlag & ( 1 << index ) ) ) color = FONT_PAL_GRAY;
						// 文字列作成。
						sprintf( moji,"%-16s",job.skill[ index ].name );                //MLHIDE
						// 文字表示
						BattleSkillHitFontNo[ index ] = StockFontBuffer( pActBattleWnd->x + 33, pActBattleWnd->y + 55 + i * 24, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 2 );
						// ＬＶ表示
						// 文字列作成
						sprintf( moji, "%3d", job.skill[ index ].lv );                  //MLHIDE
						//sprintf( moji, "999" );
						StockFontBuffer( pActBattleWnd->x + 196, pActBattleWnd->y + 55 + i * 24, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 0 );
						// 消费スロット表示
						// 文字列作成
						sprintf( moji, "%1d", job.skill[ index ].slot );                //MLHIDE
						//sprintf( moji, "999" );
						StockFontBuffer( pActBattleWnd->x + 249, pActBattleWnd->y + 55 + i * 24, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 0 );
					}
				}
				// 残りのスキルスロット数
				//sprintf( moji, "%4/%10", pActBc[ BattleMyNo ]->fp );
				sprintf( moji, "%2d/10", restSlot );                              //MLHIDE
				//sprintf( moji, "9999" );
				StockFontBuffer( pActBattleWnd->x + 145, pActBattleWnd->y + 33, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
				// キャンセルボタン表示
				BattleSkillHitFontNo[ B_SKILL_WND_HIT_NO_MAX - 1 ] = StockDispBuffer( pActBattleWnd->x + 96 + 40, pActBattleWnd->y + 300 + 10, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
			}
		}
		
		break;
	
	case 1:	// アビリティウィンドウのとき *******************************************/
	
		// ウィンドウがないとき
		if( pActBattleWnd == NULL ){
			// ウィンドウ表示タスク作成
			pActBattleWnd = makeWindowDisp( 364, 91, 272, 366, -2 );
			// 当たり判定初期化
			InitHitNo( BattleAbilityHitFontNo, B_ABILITY_WND_HIT_NO_MAX );
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			
		}else{
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBattleWnd );
#endif
			// ウィンドウ出来上がっていたら
			if( pActBattleWnd->hp > 0 ){
				// スキルウィンドウ
				StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU, CG_B_ABILITY_WND, 1 );
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(144, "右键返回技能窗口。"));
				
				
				// キャンセルボタンの时
				if( BattleAbilityHitFontNo[ B_ABILITY_WND_HIT_NO_MAX - 1 ] == HitDispNo ){
					// 一行インフォ
					strcpy( OneLineInfoStr,ML_STRING(145, "返回技能窗口。"));
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// ウィンドウアクション抹杀
						DeathAction( pActBattleWnd );
						// ポインタ初期化
						pActBattleWnd = NULL;
						// アビリティ选择ウィンドウへ
						BattleSkillWndProcNo = 0;
						// ウィンドウ关闭音
						//play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 右クリックされた时
				if( mouse.onceState & MOUSE_RIGHT_CRICK ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// スキル选择ウィンドウへ
					BattleSkillWndProcNo = 0;
					// ウィンドウ关闭音
					//play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
				
				
				// アビリティ分ループ
				for( i = 0 ; i < B_ABILITY_WND_HIT_NO_MAX - 1; i++ ){
					// 文字表示
					if( BattleAbilityHitFontNo[ i ] == HitFontNo ){
#ifdef PUK3_PACTBC_CHECKRANGE
						CheckIdRange( BattleMyNo );
#endif
						// 实际の使用魔力を算出する
						int useFp = ( job.skill[ BattleSelectSkillNo ].tech[ i ].fp * job.skill[ BattleSelectSkillNo ].fpRate ) / 100;
						// 使用できない时（赤）
						//if( job.skill[ BattleSelectSkillNo ].tech[ i ].fp > pActBc[ BattleMyNo ]->fp ){
						
						// 使用できないとき（ＬＶ制限）
						if( job.skill[ BattleSelectSkillNo ].tech[ i ].usableFlag == FALSE ){
							// 一行インフォ
							strcpy( OneLineInfoStr,ML_STRING(146, "能力不足，无法使用技能。"));
						}else
						// 魔力不足のとき
						if( useFp > pActBc[ BattleMyNo ]->fp ){
							// 一行インフォ
							strcpy( OneLineInfoStr,ML_STRING(147, "魔力不足，无法使用技能。"));
							
						}else{
							// 一行インフォ
							strcpy( OneLineInfoStr,ML_STRING(148, "使用这个能力。"));
						}
						
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// 魔力不足のとき、または能力不足のとき
							if( useFp > pActBc[ BattleMyNo ]->fp
								|| job.skill[ BattleSelectSkillNo ].tech[ i ].usableFlag == FALSE ){
								// ＮＧ音
								play_se( SE_NO_NG, 320, 240 );
							
							}else{
							
								// アビリティ番号决定
								BattleSelectAbilityNo = i;
								
								// ターゲットボックス作成
								//BattleSetTargetBox( BattleMyNo, 125 );
								if( BattleSetTargetBox( BattleMyNo, job.skill[ BattleSelectSkillNo ].tech[ i ].target ) == FALSE ){
									// ターゲット选择无しのとき
									// 文字列作成
									sprintf( moji, "S|%X|%X|%X", BattleSelectSkillNo, BattleSelectAbilityNo, BattleMyNo ); //MLHIDE
									//sprintf( moji, "S|%X|%X|%X", BattleSelectSkillNo, BattleSelectAbilityNo, 20 );
									// スキル攻击送信
									nrproto_B_send( sockfd, moji );
									// 当たり判定ボックス消す
									ClearBoxFlag();
									// 入力济みフラグＯＮ
									//BattleMenuInputFlag = TRUE;
									// ウィンドウ关闭音
									play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
									// 入力されたら
									//if( BattleMenuInputFlag == TRUE ){
										// プレイヤーコマンド入力フラグＯＮ
										BattleCmdPlayerInputFlag++;
										// 次のプロセスへ
										BattleMenuProcNo = B_MENU_PLAYER_BACK;
										// 当たり判定ボックス消す
										ClearBoxFlag();
									//}
								}else{
									// ターゲット选择へ
									BattleSkillWndProcNo = 2;
								}
								
								// ウィンドウアクション抹杀
								DeathAction( pActBattleWnd );
								// ポインタ初期化
								pActBattleWnd = NULL;
								
								// 战闘ボタンのフラグ初期化
								//InitBattleMenuButtonFlag();
								
								// クリック音
								play_se( SE_NO_CLICK, 320, 240 );
								// ウィンドウ关闭音
								//play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
							
								break;
							}
						}
					}
				}
				
				// ウィンドウがないとき
				if( pActBattleWnd == NULL ) break;
				
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( BattleMyNo );
#endif
				// フォースポイント表示
				sprintf( moji, "%4d/%4d", pActBc[ BattleMyNo ]->fp, pActBc[ BattleMyNo ]->maxFp ); //MLHIDE
				//sprintf( moji, "%4d/9999", pActBc[ BattleMyNo ]->fp );
				//sprintf( moji, "9999" );
				StockFontBuffer( pActBattleWnd->x + 182, pActBattleWnd->y + 8, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
				
				// 说明文
				// アビリティ分ループ
				for( i = 0 ; i < B_ABILITY_WND_HIT_NO_MAX - 1; i++ ){
					// 文字表示
					if( BattleAbilityHitFontNo[ i ] == HitFontNo ){
						int j;
						// 行数分ループ
						for( j = 0 ; j < 4 ; j++ ){
							// 文字列取り出し
							if( getMemoLine( moji, sizeof( moji ), job.skill[ BattleSelectSkillNo ].tech[ i ].memo, j, 26 ) ){
							//if( getMemoLine( moji, sizeof( moji ), "显示测试。\n我叫太田哲生。\n请多多关照。\n。", j, 28 ) ){
								// 文字表示
								StockFontBuffer( pActBattleWnd->x + 10, pActBattleWnd->y + 256 + j * 19, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, moji, 0 );
								//StockFontBuffer( pActBattleWnd->x + 17, pActBattleWnd->y + 200 + j * 26, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, job.skill[ BattleSelectSkillNo ].tech[ i ].memo, 0 );
							}
						}
						//break;
					}
				}
				
				// スキル分ループ
				for( i = 0 ; i < B_ABILITY_WND_HIT_NO_MAX - 1; i++ ){
					// 文字列あるとき
					if( job.skill[ BattleSelectSkillNo ].tech[ i ].name[ 0 ] != NULL ){
						// 文字色
						int color = FONT_PAL_WHITE;
						// 实际の使用魔力を算出する
						int useFp = ( job.skill[ BattleSelectSkillNo ].tech[ i ].fp * job.skill[ BattleSelectSkillNo ].fpRate ) / 100;
						
						// 使用できない时（赤）
						//if( job.skill[ BattleSelectSkillNo ].tech[ i ].fp > pActBc[ BattleMyNo ]->fp ) color = FONT_PAL_RED;
						if( useFp > pActBc[ BattleMyNo ]->fp ) color = FONT_PAL_RED;
						// 使用できないとき（ＬＶ制限）灰色
						if( job.skill[ BattleSelectSkillNo ].tech[ i ].usableFlag == FALSE ) color = FONT_PAL_GRAY;
						
						// アビリティベース表示
						StockDispBuffer( pActBattleWnd->x + 16 + 120, pActBattleWnd->y + 27 + 10 + i * 22, DISP_PRIO_MENU + 1, CG_B_ABILITY_BASE, 0 );
						// 文字列作成。
						sprintf( moji,"%-16s",job.skill[ BattleSelectSkillNo ].tech[ i ].name ); //MLHIDE
						// 文字表示
						BattleAbilityHitFontNo[ i ] = StockFontBuffer( pActBattleWnd->x + 40, pActBattleWnd->y + 28 + i * 22, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 2 );
						// 魔力表示
						// 文字列作成
						sprintf( moji, "%4d", useFp );                                  //MLHIDE
						//sprintf( moji, "9999" );
						StockFontBuffer( pActBattleWnd->x + 216, pActBattleWnd->y + 28 + i * 22, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 0 );
					}
				}
				// キャンセルボタン表示
				BattleAbilityHitFontNo[ B_ABILITY_WND_HIT_NO_MAX - 1 ] = StockDispBuffer( pActBattleWnd->x + 96 + 40, pActBattleWnd->y + 337 + 10, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
			}
		}
		
		break;
		
	case 2:	// キャンセルするとき *******************************************/
		
		// 右クリックされた时
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// アビリティ选择ウィンドウへ
			BattleSkillWndProcNo = 1;
		}
		
		break;
	
	}
	
	
	// アビリティネーム
	//job.skill[ ].tech[ ].name
	
	
	// スキル分ループ
	//for( i = 0 ; i < B_ABILITY_WND_HIT_NO_MAX ; i++ ){
		
		// 文字表示
	//	BattleAbilityHitFontNo = StockFontBuffer( 450, 100 + i * 20, FONT_PRIO_FRONT, FONT_PAL_WHITE, moji, 2 );
	//}
}

// ペットウィンドウの当たり最大判定数
#define B_PET_WND_HIT_NO_MAX  5 + 2
// 战闘ペット文字当たり判定番号
int BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX ];

// ●バトルペットウィンドウ处理 *************************************************/
void BattlePetWnd( void )
{
	int i, j;
	char moji[ 256 ];
	int atrColor[ 4 ] = { SYSTEM_PAL_GREEN, SYSTEM_PAL_AQUA, SYSTEM_PAL_RED, SYSTEM_PAL_YELLOW };
	int atrColor2[ 4 ] = { SYSTEM_PAL_GREEN2, SYSTEM_PAL_AQUA2, SYSTEM_PAL_RED2, SYSTEM_PAL_YELLOW2 };
	
	// ウィンドウがないとき
	if( pActBattleWnd == NULL ){
		// ウィンドウ表示タスク作成
		pActBattleWnd = makeWindowDisp( 364, 91, 272, 320, -2 );
		// 当たり判定初期化
		InitHitNo( BattlePetHitFontNo, B_PET_WND_HIT_NO_MAX );
		// ウィンドウ开く音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}else{
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBattleWnd );
#endif
		// ウィンドウ出来上がっていたら
		if( pActBattleWnd->hp > 0 ){
			// スキルウィンドウ
			StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU, CG_B_MON_WND, 1 );
			
			// 一行インフォ
			strcpy( OneLineInfoStr,ML_STRING(149, "右键关闭宠物列表窗口。"));
			// ペット分ループ
			for( i = 0 ; i < B_PET_WND_HIT_NO_MAX - 2 ; i++ ){
				// ソート番号取り出し
				int index = sortPet[ i ].index;
				// 当たり判定
				if( BattlePetHitFontNo[ index ] == HitFontNo ){
					// 既に战闘に出ているペットの时
					if( pet[ index ].battleSetting == PET_SETTING_BATTLE ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(150, "这个宠物已经召唤出来了。"));
					}else
					// 死んでる时
					if( pet[ index ].lp <= 0 ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(151, "这个宠物无法战斗了。"));
					}else{
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(152, "召唤这个宠物。"));
					}
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// 死んでる时、返回
						if( pet[ index ].lp <= 0 ){
							// ＮＧ音
							play_se( SE_NO_NG, 320, 240 );
							continue;
						}
						// 既に战闘に出ているペットの时、返回
						if( pet[ index ].battleSetting == PET_SETTING_BATTLE ){
							// ＮＧ音
							play_se( SE_NO_NG, 320, 240 );
							continue;
						}
						// 文字列作成
						sprintf( moji, "M|%X", index );                                 //MLHIDE
						// 送信
						nrproto_B_send( sockfd, moji );
						
						// ウィンドウアクション抹杀
						DeathAction( pActBattleWnd );
						// ポインタ初期化
						pActBattleWnd = NULL;
						
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// クリック音
						//play_se( SE_NO_CLICK, 320, 240 );
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}
			// ペット戾すボタンの时
			if( BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX - 2 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(153, "召还宠物。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// ペットが战闘に出ているとき
					if( CheckBattlePet() != -1 ){
					
						// 文字列作成
						sprintf( moji, "M|%X", 255 );                                   //MLHIDE
						// 送信
						nrproto_B_send( sockfd, moji );
						
						// ウィンドウアクション抹杀
						DeathAction( pActBattleWnd );
						// ポインタ初期化
						pActBattleWnd = NULL;
						
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// クリック音
						//play_se( SE_NO_CLICK, 320, 240 );
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}
			// キャンセルボタンの时
			if( BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX - 1 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(143, "关闭这个窗口。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// アビリティ选择ウィンドウへ
					//BattleSkillWndProcNo = 0;
					// ボタンもどす
					BattleMenuButtonFlag[ 4 ] = 0;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			// 右クリックされた时
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// ウィンドウアクション抹杀
				DeathAction( pActBattleWnd );
				// ポインタ初期化
				pActBattleWnd = NULL;
				// アビリティ选择ウィンドウへ
				//BattleSkillWndProcNo = 0;
				// ボタンもどす
				BattleMenuButtonFlag[ 4 ] = 0;
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}
			// ウィンドウがある时
			if( pActBattleWnd != NULL ){;
				int cnt = 0;
				// ペット分ループ
				for( i = 0 ; i < B_PET_WND_HIT_NO_MAX - 2 ; i++ ){
					// ソート番号取り出し
					int index = sortPet[ i ].index;
					// この栏にペットがいる时かつ、待机の时
					if( pet[ index ].useFlag == TRUE 
						&& ( pet[ index ].battleSetting == PET_SETTING_STADBY
						|| pet[ index ].battleSetting == PET_SETTING_BATTLE ) ){
						
						int condition = 0;
						
						int nameColor = FONT_PAL_WHITE;	// デフォルト白文字
						
						// 战闘に出ているペットの时、黄色
						if( pet[ index ].battleSetting == PET_SETTING_BATTLE ) nameColor = FONT_PAL_YELLOW;
						// 死んでる时、赤文字表示
						if( pet[ index ].lp <= 0 ) nameColor = FONT_PAL_RED;
						
						// 称号が无い时
						if( pet[ index ].freeName[ 0 ] == '\0' ){
							// 名称表示
							CenteringStr( pet[ index ].name, moji, PET_NAME_LEN );
							BattlePetHitFontNo[ index ] = StockFontBuffer( pActBattleWnd->x + 20, pActBattleWnd->y + 32 + cnt * 86, FONT_PRIO_FRONT, nameColor, moji, 2 );
						}else{
							// 名称表示
							CenteringStr( pet[ index ].freeName, moji, PET_NAME_LEN );
							BattlePetHitFontNo[ index ] = StockFontBuffer( pActBattleWnd->x + 20, pActBattleWnd->y + 32 + cnt * 86, FONT_PRIO_FRONT, nameColor, moji, 2 );
						}
						
						// ＬＶ表示
						sprintf( moji, "%3d", pet[ index ].lv );                        //MLHIDE
						//sprintf( moji, "999" );
						StockFontBuffer( pActBattleWnd->x + 54, pActBattleWnd->y + 59 + cnt * 86, FONT_PRIO_FRONT, FONT_KIND_SMALL, nameColor, moji, 0 );
						
						// 体力表示
						sprintf( moji, "%4d/%4d", pet[ index ].lp, pet[ index ].maxLp ); //MLHIDE
						//sprintf( moji, "9999/9999" );
						StockFontBuffer( pActBattleWnd->x + 129, pActBattleWnd->y + 59 + cnt * 86, FONT_PRIO_FRONT, FONT_KIND_SMALL, nameColor, moji, 0 );
						
						// 魔力表示
						sprintf( moji, "%4d/%4d", pet[ index ].fp, pet[ index ].maxFp ); //MLHIDE
						//sprintf( moji, "9999/1000" );
						StockFontBuffer( pActBattleWnd->x + 68, pActBattleWnd->y + 81 + cnt * 86, FONT_PRIO_FRONT, FONT_KIND_SMALL, nameColor, moji, 0 );
						


#if 1
						//健康状态表示
						// コンディション表示
						if( pet[ index ].injuryLv == 0 ){
							condition = 0;
						}else

						if( pet[ index ].injuryLv <= 25 )
						{
							condition = 1;
						}
						else
						if( pet[ index ].injuryLv <= 50 )
						{
							condition = 2;
						}
						else
						if( pet[ index ].injuryLv <= 75 )
						{
							condition = 3;
						}
						else
						{
							condition = 4;
						}
						StockDispBuffer( pActBattleWnd->x + 240 + 6, pActBattleWnd->y + 37 + cnt * 86 + 6, DISP_PRIO_MENU + 1, CG_B_MONSTER_LIST_CONDITION_GREEN + condition, 0 );
						
#else						
						// 健康状态表示
						//StockDispBuffer( pActBattleWnd->x + 129, pActBattleWnd->y + 59 + cnt * 86, DISP_PRIO_MENU + 1, CG_B_MON_WND_PET_COME_BTN_UP, 2 );
						
						// コンディション表示
						if( pet[ index ].maxLp - pet[ index ].lp >= 30 )	// 受伤３０以上
						{
							condition = 4;
						}
						else
						if( pet[ index ].maxLp - pet[ index ].lp >= 20 )	// 受伤２０以上
						{
							condition = 3;
						}
						else
						if( pet[ index ].maxLp - pet[ index ].lp >= 10 )	// 受伤１０以上
						{
							condition = 2;
						}
						else
						if( pet[ index ].maxLp - pet[ index ].lp >= 1 )		// 受伤１以上
						{
							condition = 1;
						}
						else
						{
							condition = 0;
						}
						//StockDispBuffer( pActBattleWnd->x + 129, pActBattleWnd->y + 59 + cnt * 86, DISP_PRIO_MENU + 1, CG_B_MONSTER_LIST_CONDITION_GREEN + condition, 0 );
						StockDispBuffer( pActBattleWnd->x + 240 + 6, pActBattleWnd->y + 37 + cnt * 86 + 6, DISP_PRIO_MENU + 1, CG_B_MONSTER_LIST_CONDITION_GREEN + condition, 0 );
	
	
#endif	
	
						
						// 种族表示
						//StockDispBuffer( pActBattleWnd->x + 129, pActBattleWnd->y + 81 + cnt * 86, DISP_PRIO_MENU + 1, CG_B_MONSTER_LIST_TYPE_HUMAN + pet[ index ].tribe, 2 );
						StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy + cnt * 86, DISP_PRIO_MENU + 1, CG_B_MONSTER_LIST_TYPE_HUMAN + pet[ index ].tribe, 0 );
						
						// 属性值をバーで表示 218,59  42,19
						for( j = 0; j < 4; j++ ){
							
							// 属性无い时返回
							//if( pet[ index ].attr[ j ] <= 0 ) continue;
							
							int x1 = pActBattleWnd->x + 213;
							int y1 = pActBattleWnd->y + 59 + cnt * 86 + j * 4;
							int x2 = x1 + pet[ index ].attr[ j ] / 10 * 4;
							//int x2 = x1 + 100 / 10 * 4;
							int y2 = y1 + 3;
							//StockBoxDispBuffer( x1, y1, x2, y2, DISP_PRIO_MENU + 1, atrColor[ j ], 1 );
							StockBoxDispBuffer( x1, y1, x2, y1, DISP_PRIO_MENU + 1, atrColor[ j ], 2 );
							StockBoxDispBuffer( x1, y1 + 1, x2, y1 + 1, DISP_PRIO_MENU + 1, atrColor[ j ], 2 );
							StockBoxDispBuffer( x1, y1 + 2, x2, y1 + 2, DISP_PRIO_MENU + 1, atrColor2[ j ], 2 );
						}
						
						// 表示数カウント
						cnt++;
						if( cnt >= 3 ) break;
					}
				}
				// ペットが战闘に出ているとき
				if( CheckBattlePet() != -1 ){
					// ペット戾すボタン表示
					BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX - 2 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx - 57, pActBattleWnd->y + 297, DISP_PRIO_MENU + 1, CG_B_MON_WND_PET_COME_BTN_UP, 2 );
					// キャンセルボタン表示
					BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX - 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx + 57, pActBattleWnd->y + 297, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
				}else{
					// ペット戾すボタン初期化
					BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX - 2 ] = -2;
					// キャンセルボタン表示
					BattlePetHitFontNo[ B_PET_WND_HIT_NO_MAX - 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, pActBattleWnd->y + 297, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
				}
			
			}
		}
	}
}

// 当たり最大判定数
#define B_ITEM_WND_HIT_NO_MAX  3
// 战闘ペット文字当たり判定番号
int BattleItemHitFontNo[ B_ITEM_WND_HIT_NO_MAX ];
int BattleSelectItemNo;
// 战闘アイテムウィンドウプロセス番号
int BattleItemWndProcNo = 0;

// ●バトルアイテムウィンドウ处理 *************************************************/
void BattleItemWnd( void )
{
	int i, j, k;
	char moji[ 256 ];
	
	// プロセス番号で分岐
	switch( BattleItemWndProcNo ){
	
	case 0:	// アイテムウィンドウ
	
		// ウィンドウがないとき
		if( pActBattleWnd == NULL ){
			// ウィンドウ表示タスク作成
			pActBattleWnd = makeWindowDisp( 364, 91, 272, 300, -2 );
			// 当たり判定初期化
			InitHitNo( BattleItemHitFontNo, B_ITEM_WND_HIT_NO_MAX );
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
		}else{
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBattleWnd );
#endif
			// ウィンドウ出来上がっていたら
			if( pActBattleWnd->hp > 0 ){
				// アイテムウィンドウ
				StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU, CG_B_ITEM_WND, 1 );
				
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(154, "右键关闭物品窗口。"));
				// キャンセルボタンの时
				if( BattleItemHitFontNo[ 0 ] == HitDispNo ){
					// 一行インフォ
					strcpy( OneLineInfoStr,ML_STRING(143, "关闭这个窗口。"));
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// ウィンドウアクション抹杀
						DeathAction( pActBattleWnd );
						// ポインタ初期化
						pActBattleWnd = NULL;
						// ボタンもどす
						BattleMenuButtonFlag[ 3 ] = 0;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				
				// ウィンドウがある时
				if( pActBattleWnd != NULL ){
					// アイテム说明ウィンドページ番号
					static int itemInfoWindowPageNo = 0;
					static int hitItemNo = 0;
					// 枠に入ってるかフラグ
					int boxInFlag = FALSE;
					//for( i = MAX_ITEM -1 ; i >= 8 ; i-- ){
					for( i = 8, j = 0, k = 0 ; i < MAX_ITEM ; i++ ){
						// 枠内に入っていたら
						if( MakeHitBox( pActBattleWnd->x + 8 + j * 52, pActBattleWnd->y + 33 + k * 53,
										pActBattleWnd->x + 8 + 48 + j * 52, pActBattleWnd->y + 33 + 48 + k * 53, DISP_PRIO_MENU + 1 ) == TRUE ){
							// その栏にアイテムがあったら
							if( pc.item[ i ].useFlag == TRUE ){ 
								int color = FONT_PAL_WHITE;
								int infoY = 165;
								// 装备できるかチェック
								if( pc.lv < pc.item[ i ].lv ) color = FONT_PAL_RED;
								
								// 座标チェック
								if( i >= 18 ) infoY = - 76;
								// 右クリックされた时かつ、ボックスに入ってないとき
								if( mouse.onceState & MOUSE_RIGHT_CRICK && boxInFlag == FALSE ){
									// ページ切り替え
									if( itemInfoWindowPageNo == 0 ) itemInfoWindowPageNo = 1;
									else itemInfoWindowPageNo = 0;
								}
								
								// 前と违うボックスかチェック
								if( hitItemNo != i ) itemInfoWindowPageNo = 0;
								// ヒットしているアイテム番号学习
								hitItemNo = i;
								
								// 说明ウィンドウ表示
								itemInfoWindow( pActBattleWnd->x + 4, pActBattleWnd->y + infoY, i, itemInfoWindowPageNo );
								// 枠に入っているかフラグＯＮ
								boxInFlag = TRUE;
								
								// 使用可能アイテムの时
								if( pc.item[ i ].battle & ITEM_FLAG_USEABLE ){ 
									// 一行インフォ
									strcpy( OneLineInfoStr,ML_STRING(155, "使用这个物品。"));
								}else{
									// 一行インフォ
									strcpy( OneLineInfoStr,ML_STRING(156, "无法使用这个物品。"));
								}
								
								//刻印かハンコ
								if(strlen(pc.item[ i ].freeName) >= 1){
									if( pc.item[ i ].flag & ITEM_ETC_FLAG_INCUSE){
										//刻印
										strcpy( OneLineInfoStr, pc.item[ i ].name);
										strcat(OneLineInfoStr,ITEM_INCUSE_STRING);
									}else if( pc.item[ i ].flag & ITEM_ETC_FLAG_HANKO){
										//ハンコ
										strcpy( OneLineInfoStr,pc.item[ i ].freeName);
										strcat(OneLineInfoStr,ITEM_HANKO_STRING);
									}
								}
							}
							// 左ダブルクリックされた时
							if( mouse.onceState & MOUSE_LEFT_DBL_CRICK ){
								// その栏にアイテムがあたっら
								if( pc.item[ i ].useFlag == TRUE && pc.item[ i ].battle & ITEM_FLAG_USEABLE ){ 
									// 装备できるかチェック
									//if( pc.lv < pc.item[ i ].lv ){
										// 警告文字表示
									//	StockChatBufferLine( "没有足够能力使用！", FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
										// ＮＧ音
									//	play_se( SE_NO_NG, 320, 240 );
									//}else
									// 鉴定してるかチェック
									if( pc.item[ i ].checkFlag == FALSE ){
										// 警告文字表示
										StockChatBufferLine( ML_STRING(157, "无法使用未鉴定物品！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
										// ＮＧ音
										play_se( SE_NO_NG, 320, 240 );
									}else{
										// ウィンドウアクション抹杀
										DeathAction( pActBattleWnd );
										// ポインタ初期化
										pActBattleWnd = NULL;
										// ボタンもどす
										//BattleMenuButtonFlag[ 3 ] = 0;
										// ターゲット选择へ
										BattleItemWndProcNo = 1;
										
										// ターゲットボックス作成
										//BattleSetTargetBox( BattleMyNo, 125 );
										// ターゲットボックス作成
										BattleSetTargetBox( BattleMyNo, pc.item[ i ].target );
										
										// 选择アイテム番号记忆
										BattleSelectItemNo = i;
										// アイテム使用音
										play_se( SE_NO_USE_ITEM, 320, 240 );
										// ウィンドウ关闭音
										//play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
										break;
									}
								}else{
									// ＮＧ音
									play_se( SE_NO_NG, 320, 240 );
								}
							}
						}
						// ウィンドウがある时
						if( pActBattleWnd != NULL ){
							// その栏にアイテムがあたっら
							if( pc.item[ i ].useFlag == TRUE ){ 
								// アイテム表示
								StockDispBuffer( pActBattleWnd->x + 8 + 24 + j * 52, pActBattleWnd->y + 33 + 24 + k * 53, DISP_PRIO_MENU + 2, pc.item[ i ].graNo, 0 );
								// スタックがある时
								if( pc.item[ i ].num > 0 ){
									// スタック数表示
									sprintf( moji, "%3d", pc.item[ i ].num );                    //MLHIDE
									StockFontBuffer( pActBattleWnd->x + 8 + 21 + j * 52, pActBattleWnd->y + 33 + 31 + k * 53,
														FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0, 0 );
								}
								// 使えないアイテムの时
								if( !( pc.item[ i ].battle & ITEM_FLAG_USEABLE ) ){ 
									// 网表示
									StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx + j * 52, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy + k * 53, DISP_PRIO_BOX2, CG_B_UNUSE_ITEM_NET, 0 );
								}
							}
						}
						// 表示座标计算用
						if( ++j == 5 ){ 
							k++;
							j = 0;
						}
					}
					// 右クリックされた时かつ、ボックスに入ってないとき
					if( mouse.onceState & MOUSE_RIGHT_CRICK && boxInFlag == FALSE ){
						// ウィンドウアクション抹杀
						DeathAction( pActBattleWnd );
						// ポインタ初期化
						pActBattleWnd = NULL;
						// ボタンもどす
						BattleMenuButtonFlag[ 3 ] = 0;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					
					// ボックスに入ってないとき
					if( boxInFlag == FALSE ) itemInfoWindowPageNo = 0;
					
					// ウィンドウがある时
					if( pActBattleWnd != NULL ){
						// ページ切り替えボタン表示
						//if( BattleItemPage == 0 ){
						//	BattleItemHitFontNo[ 0 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU + 1, CG_B_ITEM_WND_PAGE_BTN_2, 2 );
						//	BattleItemHitFontNo[ 1 ] = -2;
						//}else{
						//	BattleItemHitFontNo[ 0 ] = -2;
						//	BattleItemHitFontNo[ 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU + 1, CG_B_ITEM_WND_PAGE_BTN_1, 2 );
						//}
						// キャンセルボタン表示
						BattleItemHitFontNo[ 0 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, pActBattleWnd->y + 270, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
					}
				}
			}
		}
		
		break;
	
	case 1:	// ターゲット选择时 *******************************************/
		
		// 右クリックされた时
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// アイテム选择ウィンドウへ
			BattleItemWndProcNo = 0;
		}
		
		break;
	}
}

// 当たり最大判定数
#define B_WEAPON_WND_HIT_NO_MAX  3
// 战闘装备变更文字当たり判定番号
int BattleWeaponHitFontNo[ B_WEAPON_WND_HIT_NO_MAX ];

// ●バトル装备变更ウィンドウ处理 *************************************************/
void BattleWeaponWnd( void )
{
	int i, j, k;
	char moji[ 256 ];
	
	// ウィンドウがないとき
	if( pActBattleWnd == NULL ){
		// ウィンドウ表示タスク作成
		pActBattleWnd = makeWindowDisp( 364, 91, 272, 300, -2 );
		// 当たり判定初期化
		InitHitNo( BattleWeaponHitFontNo, B_WEAPON_WND_HIT_NO_MAX );
		// ウィンドウ开く音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}else{
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBattleWnd );
#endif
		// ウィンドウ出来上がっていたら
		if( pActBattleWnd->hp > 0 ){
			// アイテムウィンドウ
			StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU, CG_B_WEAPON_CHANGE_WND, 1 );
			
			// 一行インフォ
			strcpy( OneLineInfoStr,ML_STRING(158, "右键关闭武器更换窗口。"));
			// キャンセルボタンの时
			if( BattleWeaponHitFontNo[ 0 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(143, "关闭这个窗口。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// ボタンもどす
					BattleMenuButtonFlag[ 5 ] = 0;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			// 素手ボタンの时
			if( BattleWeaponHitFontNo[ 1 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(159, "变成空手。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
				
					int handFlag = 0;	// 手にもっている数
					
					// 右手に持っているかチェック、盾の时はカウントに入れない。
					if( pc.item[ 2 ].useFlag == TRUE && pc.item[ 2 ].kind != 7 ) handFlag++;
					// 左手に持っているかチェック、盾の时はカウントに入れない。
					if( pc.item[ 3 ].useFlag == TRUE && pc.item[ 3 ].kind != 7 ) handFlag++;
					
					// 手にもっているアイテム数とアイテム栏の空きをチェック
					if( CheckItemBlank() >= handFlag ){
					
						// 何も装备してない时
						if( handFlag == 0 ){
						
							// 警告文字表示
							StockChatBufferLine( ML_STRING(160, "没有装备武器！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
							// ＮＧ音
							play_se( SE_NO_NG, 320, 240 );
							
						}else{
						
							// ウィンドウアクション抹杀
							DeathAction( pActBattleWnd );
							// ポインタ初期化
							pActBattleWnd = NULL;
							// ボタンもどす
							BattleMenuButtonFlag[ 5 ] = 0;
							// 文字列作成
							sprintf( moji, "Q|%X|", 255 );                                 //MLHIDE
							// 	装备变更を送信
							nrproto_B_send( sockfd, moji );
							// 入力济みフラグＯＮ
							BattleMenuInputFlag = TRUE;
							// ウィンドウ关闭音
							play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						}
					}else{
					
					// 手にもっているアイテム数とアイテム栏の空きをチェック
					//if( handFlag == 2 && CheckItemBlank() == 0 && pc.item[ i ].battle & ITEM_FLAG_TWO_HAND ){
						// 警告文字表示
						StockChatBufferLine( ML_STRING(161, "物品栏没有空位了！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
						// ＮＧ音
						play_se( SE_NO_NG, 320, 240 );
					}
				}
			}
			
			// ウィンドウがある时
			if( pActBattleWnd != NULL ){
				// アイテム说明ウィンドページ番号
				static int itemInfoWindowPageNo = 0;
				static int hitItemNo = 0;
				// 枠に入ってるかフラグ
				int boxInFlag = FALSE;
				//for( i = MAX_ITEM -1 ; i >= 8 ; i-- ){
				for( i = 8, j = 0, k = 0 ; i < MAX_ITEM ; i++ ){
					// 枠内に入っていたら
					if( MakeHitBox( pActBattleWnd->x + 8 + j * 52, pActBattleWnd->y + 33 + k * 53,
									pActBattleWnd->x + 8 + 48 + j * 52, pActBattleWnd->y + 33 + 48 + k * 53, DISP_PRIO_MENU + 1 ) == TRUE ){
						// その栏にアイテムがあったら
						if( pc.item[ i ].useFlag == TRUE ){ 
							//int color = FONT_PAL_WHITE;
							int infoY = 165;
								
							// 座标チェック
							if( i >= 18 ) infoY = - 76;
							// 右クリックされた时かつ、ボックスに入ってないとき
							if( mouse.onceState & MOUSE_RIGHT_CRICK && boxInFlag == FALSE ){
								// ページ切り替え
								if( itemInfoWindowPageNo == 0 ) itemInfoWindowPageNo = 1;
								else itemInfoWindowPageNo = 0;
							}
							
							// 前と违うボックスかチェック
							if( hitItemNo != i ) itemInfoWindowPageNo = 0;
							// ヒットしているアイテム番号学习
							hitItemNo = i;
							
							// 说明ウィンドウ表示
							itemInfoWindow( pActBattleWnd->x, pActBattleWnd->y + infoY, i, itemInfoWindowPageNo );
							// 枠に入っているかフラグＯＮ
							boxInFlag = TRUE;
							
							// 使用可能アイテムの时
							if( pc.item[ i ].kind >= 0 && pc.item[ i ].kind <= 7 ){ 
								// 一行インフォ
								strcpy( OneLineInfoStr,ML_STRING(162, "装备这个物品。"));
							}else{
								// 一行インフォ
								strcpy( OneLineInfoStr,ML_STRING(163, "无法装备这个物品。"));
							}
							
							//刻印かハンコ
							if(strlen(pc.item[ i ].freeName) >= 1){
								if( pc.item[ i ].flag & ITEM_ETC_FLAG_INCUSE){
									//刻印
									strcpy( OneLineInfoStr, pc.item[ i ].name);
									strcat(OneLineInfoStr,ITEM_INCUSE_STRING);
								}else if( pc.item[ i ].flag & ITEM_ETC_FLAG_HANKO){
									//ハンコ
									strcpy( OneLineInfoStr,pc.item[ i ].freeName);
									strcat(OneLineInfoStr,ITEM_HANKO_STRING);
								}
							}

							
						}
						// 左ダブルクリックされた时
						if( mouse.onceState & MOUSE_LEFT_DBL_CRICK ){
							// その栏にアイテムがある、かつ、武器または盾の时
							//if( pc.item[ i ].useFlag == TRUE && pc.item[ i ].battle & ITEM_FLAG_EQUIP ){ 
							if( pc.item[ i ].useFlag == TRUE && pc.item[ i ].kind >= 0 && pc.item[ i ].kind <= 7 ){ 
							//if( pc.item[ TRUE ){ 
							
								int handFlag = 0;	// 手にもっている数
							
								// 右手に持っているかチェック
								if( pc.item[ 2 ].useFlag == TRUE ) handFlag++;
								// 左手に持っているかチェック
								if( pc.item[ 3 ].useFlag == TRUE ) handFlag++;
								
								// 手にもっているアイテム数とアイテム栏の空きをチェック
								if( handFlag == 2 && CheckItemBlank() == 0 && pc.item[ i ].battle & ITEM_FLAG_TWO_HAND ){
									// 警告文字表示
									StockChatBufferLine( ML_STRING(161, "物品栏没有空位了！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
									// ＮＧ音
									play_se( SE_NO_NG, 320, 240 );
								}else
								// 装备できるかチェック
								//if( pc.lv < pc.item[ i ].lv ){
									// 警告文字表示
								//	StockChatBufferLine( "没有足够能力装备这个物品！", FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
									// ＮＧ音
								//	play_se( SE_NO_NG, 320, 240 );
								//}else
								// 鉴定してるかチェック
								if( pc.item[ i ].checkFlag == FALSE ){
									// 警告文字表示
									StockChatBufferLine( ML_STRING(164, "无法装备未鉴定物品！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
									// ＮＧ音
									play_se( SE_NO_NG, 320, 240 );
								}else{
								
									// ウィンドウアクション抹杀
									DeathAction( pActBattleWnd );
									// ポインタ初期化
									pActBattleWnd = NULL;
									// ボタンもどす
									BattleMenuButtonFlag[ 5 ] = 0;
									// 文字列作成
									sprintf( moji, "Q|%X|", i );                                 //MLHIDE
									// 	装备变更を送信
									nrproto_B_send( sockfd, moji );
									// 入力济みフラグＯＮ
									BattleMenuInputFlag = TRUE;
									// アイテム使用音
									play_se( SE_NO_USE_ITEM, 320, 240 );
									// ウィンドウ关闭音
									//play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
									break;
								}
								
							}else{
								// ＮＧ音
								play_se( SE_NO_NG, 320, 240 );
							}
						}
					}
					// ウィンドウがある时
					if( pActBattleWnd != NULL ){;
						// その栏にアイテムがあたっら
						if( pc.item[ i ].useFlag == TRUE ){ 
							// アイテム表示
							StockDispBuffer( pActBattleWnd->x + 8 + 24 + j * 52, pActBattleWnd->y + 33 + 24 + k * 53, DISP_PRIO_MENU + 2, pc.item[ i ].graNo, 0 );
							// スタックがある时
							if( pc.item[ i ].num > 0 ){
								// スタック数表示
								sprintf( moji, "%3d", pc.item[ i ].num );                     //MLHIDE
								StockFontBuffer( pActBattleWnd->x + 8 + 21 + j * 52, pActBattleWnd->y + 33 + 31 + k * 53,
													FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0, 0 );
							}
							// 装备できないアイテムの时
							//if( !( pc.item[ i ].battle & ITEM_FLAG_EQUIP ) ){ 
							if( !( pc.item[ i ].kind >= 0 && pc.item[ i ].kind <= 7 ) ){ 
								// 网表示
								StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx + j * 52, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy + k * 53, DISP_PRIO_BOX2, CG_B_UNUSE_ITEM_NET, 0 );
							}
						}
					}
					// 表示座标计算用
					if( ++j == 5 ){ 
						k++;
						j = 0;
					}
				}
				// 右クリックされた时かつ、ボックスに入ってないとき
				if( mouse.onceState & MOUSE_RIGHT_CRICK && boxInFlag == FALSE ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// ボタンもどす
					BattleMenuButtonFlag[ 5 ] = 0;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
				// ボックスに入ってないとき
				if( boxInFlag == FALSE ) itemInfoWindowPageNo = 0;
				
				// ウィンドウがある时
				if( pActBattleWnd != NULL ){
					// キャンセルボタン表示
					BattleWeaponHitFontNo[ 0 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx + 50, pActBattleWnd->y + 270, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
					
					{
						int handFlag = 0;	// 手にもっている数
					
						// 右手に持っているかチェック
						if( pc.item[ 2 ].useFlag == TRUE ) handFlag++;
						// 左手に持っているかチェック
						if( pc.item[ 3 ].useFlag == TRUE ) handFlag++;
						
						// 手にもっているアイテム数とアイテム栏の空きをチェック
						if( CheckItemBlank() >= handFlag ){
							// 何も装备してない时
							if( handFlag == 0 ){
								// 素手ボタン表示（使えない）
								BattleWeaponHitFontNo[ 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx - 50, pActBattleWnd->y + 270, DISP_PRIO_MENU + 1, CG_B_WEAPON_NO_WEAPON_BTN_UNUSE, 2 );
							}else{
								// 素手ボタン表示
								BattleWeaponHitFontNo[ 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx - 50, pActBattleWnd->y + 270, DISP_PRIO_MENU + 1, CG_B_WEAPON_NO_WEAPON_BTN_UP, 2 );
							}
						}else{
							// 素手ボタン表示（使えない）
							BattleWeaponHitFontNo[ 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx - 50, pActBattleWnd->y + 270, DISP_PRIO_MENU + 1, CG_B_WEAPON_NO_WEAPON_BTN_UNUSE, 2 );
						}
					}
				}
			}
		}
	}
}



// 当たり最大判定数
#define B_PET_SKILL_WND_HIT_NO_MAX  12
// 战闘ペット文字当たり判定番号
int BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX ];
// 前回のペットのスキル番号记忆用
int BattlePetSkillSelectNo = -1;

// 战闘ペットスキルウィンドウのボタンフラグ记忆用
int BattleMenuPetButtonFlag = 1;
// 战闘ペットベースウィンドウの当たり判定番号
int BattleMenuPetBaseHitFontNo = -1;
// 前回の使用したペットの番号记忆用
int BattlePetNoBak = -1;




// ●バトルペットスキルウィンドウ处理 *************************************************/
void BattlePetSkillWnd( void )
{
	int i;
	char moji[ 256 ];
	
	// ウィンドウがないとき
	if( pActBattleWnd == NULL ){
		// ウィンドウ表示タスク作成
		pActBattleWnd = makeWindowDisp( 364, 88, 272, 366, -2 );
		// 当たり判定初期化
		InitHitNo( BattlePetSkillHitFontNo, B_PET_SKILL_WND_HIT_NO_MAX );
		// ウィンドウ开く音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}else{
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBattleWnd );
#endif
		// ウィンドウ出来上がっていたら
		if( pActBattleWnd->hp > 0 ){
			// ペットスキルウィンドウ
			StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx, ( ( WIN_DISP *)pActBattleWnd->pYobi )->cy, DISP_PRIO_MENU, CG_B_MONSTER_SKILL_WND, 1 );
			
			// 一行インフォ
			strcpy( OneLineInfoStr,ML_STRING(165, "右键关闭宠物技能列表窗口。"));
			// スキル分ループ
			for( i = 0 ; i < B_PET_SKILL_WND_HIT_NO_MAX - 2 ; i++ ){
				// ソート番号取り出し
				int index = pet[ CheckBattlePet() ].sortTech[ i ].index;
				//for( i = 0 ; i < 1; i++ ){
				// 文字表示
				if( BattlePetSkillHitFontNo[ index ] == HitFontNo ){
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
					// 使用できない时
					if( !( BattlePetUsableSkillFlag & ( 1 << index ) ) ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(140, "无法使用这个技能。"));
					}else
					// 魔力不足のとき
					if( pet[ CheckBattlePet() ].tech[ index ].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(166, "魔力不足，无法使用。"));
					}else{
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(167, "使用这个技能。"));
					}
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// 使用できない时または、魔力不足のとき
						if( !( BattlePetUsableSkillFlag & ( 1 << index ) ) || pet[ CheckBattlePet() ].tech[ index ].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp ){
							// ＮＧ音
							play_se( SE_NO_NG, 320, 240 );
						}else{
						
							// スキル番号决定
							BattlePetSkillSelectNo = index;
							
							// ターゲットボックス作成
							//BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), 117 );
							//BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), 1141 );
							if( BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].target ) == FALSE ){
								// ターゲット选择无しのとき
								// 文字列作成
								sprintf( moji, "W|%X|%X", BattlePetSkillSelectNo, CheckBattlePet() ); //MLHIDE
								// 通常攻击
								nrproto_B_send( sockfd, moji );
								// 当たり判定ボックス消す
								ClearBoxFlag();
								// 入力济みフラグＯＮ
								//BattleMenuInputFlag = TRUE;
								// ウィンドウ关闭音
								play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
								// 入力されたら
								//if( BattleMenuInputFlag == TRUE ){
									// ペットコマンド入力フラグＯＮ
									BattleCmdPetInputFlag++;
									// 次のプロセスへ
									BattleMenuProcNo = B_MENU_PET_BACK;
									// このターンのペット番号を学习
									BattlePetNoBak = CheckBattlePet();
								//}
							}
							
							// ウィンドウアクション抹杀
							DeathAction( pActBattleWnd );
							// ポインタ初期化
							pActBattleWnd = NULL;
							
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
							// ペットスキルウィンドウ表示する时
							BattleMenuPetButtonFlag = 2;
							// ウィンドウ关闭音
							//play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						
							break;
						}
					}
				}
			}
			// キャンセルボタンの时
			if( BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX - 1 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(143, "关闭这个窗口。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// ボタンもどす
					BattleMenuPetButtonFlag = 0;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			// 何もしないボタンの时
			if( BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX - 2 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(168, "没有无法使用的技能，本回合跳过。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// ボタンもどす
					BattleMenuPetButtonFlag = 0;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					// 何もしない送信
					if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "W|FF" );     //MLHIDE
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 入力济みフラグＯＮ
					//BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					// ペットコマンド入力フラグＯＮ
					BattleCmdPetInputFlag++;
					// 次のプロセスへ
					BattleMenuProcNo = B_MENU_PET_BACK;
					// このターンのペット番号を学习
					BattlePetNoBak = CheckBattlePet();
				}
			}
			// 右クリックされた时
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// ウィンドウアクション抹杀
				DeathAction( pActBattleWnd );
				// ポインタ初期化
				pActBattleWnd = NULL;
				// ボタンもどす
				BattleMenuPetButtonFlag = 0;
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}
			// ウィンドウがある时
			if( pActBattleWnd != NULL ){
				// 战闘中のペット番号（０～４）を求める
				int battlePetNo = CheckBattlePet();
				int battlePetSkillCnt = 0;		// スキル数カウント
				int battlePetSkillPassCnt = 0;	// 使用できないスキル数カウント
				int battlePetPassFlag = FALSE;	// 全スキルが使用できないフラグ
				// 战闘ペットがいるとき
				if( battlePetNo != -1 ){
					// 说明文
					// アビリティ分ループ
					for( i = 0 ; i < B_PET_SKILL_WND_HIT_NO_MAX - 2 ; i++ ){
						// ソート番号取り出し
						int index = pet[ CheckBattlePet() ].sortTech[ i ].index;
						// 文字表示
						if( BattlePetSkillHitFontNo[ index ] == HitFontNo ){
							int j;
							// 行数分ループ
							for( j = 0 ; j < 4 ; j++ ){
								// 文字列取り出し
								if( getMemoLine( moji, sizeof( moji ), pet[ battlePetNo ].tech[ index ].memo, j, 26 ) ){
								//if( getMemoLine( moji, sizeof( moji ), "显示测试。\n我叫太田哲生。\n请多多关照。\n。", j, 28 ) ){
									// 文字表示
									StockFontBuffer( pActBattleWnd->x + 19, pActBattleWnd->y + 255 + j * 19, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, moji, 0 );
									//StockFontBuffer( pActBattleWnd->x + 17, pActBattleWnd->y + 200 + j * 26, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, job.skill[ BattleSelectSkillNo ].tech[ index ].memo, 0 );
								}
							}
								//break;
						}
					}
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
					// フォースポイント表示
					sprintf( moji, "%4d/%4d", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp ); //MLHIDE
					//sprintf( moji, "%4d/9999", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp );
					//sprintf( moji, "9999" );
					StockFontBuffer( pActBattleWnd->x + 182, pActBattleWnd->y + 10, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
					// スキル分ループ
					for( i = 0 ; i < pet[ battlePetNo ].maxTech; i++ ){
						// ソート番号取り出し
						int index = pet[ CheckBattlePet() ].sortTech[ i ].index;
						// スキルベース表示
						StockDispBuffer( pActBattleWnd->x + 15 + 121, pActBattleWnd->y + 29 + i * 22 + 10, DISP_PRIO_MENU + 1, CG_B_MONSTER_SKILL_BASE, 0 );
						// 文字列あるとき
						if( pet[ battlePetNo ].tech[ index ].name[ 0 ] != NULL ){
							// 文字色
							int color = FONT_PAL_WHITE;
							// 魔力不足の时（赤）
							if( pet[ battlePetNo ].tech[ index ].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp ) color = FONT_PAL_RED;
							// 使用できない时（灰色）
							if( !( BattlePetUsableSkillFlag & ( 1 << i ) ) ) color = FONT_PAL_GRAY;
							
							// 文字列作成。
							sprintf( moji,"%-16s",pet[ battlePetNo ].tech[ index ].name ); //MLHIDE
							// 文字表示
							BattlePetSkillHitFontNo[ index ] = StockFontBuffer( pActBattleWnd->x + 39, pActBattleWnd->y + 29 + i * 22, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 2 );
							//BattlePetSkillHitFontNo[ index ] = StockFontBuffer( pActBattleWnd->x + 39, pActBattleWnd->y + 30 + i * 25, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, "攻击　　　　　　", 2 );
							// 魔力表示
							// 文字列作成
							sprintf( moji, "%4d", pet[ battlePetNo ].tech[ index ].fp );   //MLHIDE
							//sprintf( moji, "9999" );
							StockFontBuffer( pActBattleWnd->x + 212, pActBattleWnd->y + 30 + i * 22, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, moji, 0 );
							
							// スキル数カウント
							battlePetSkillCnt++;
							// 使用できないスキル数カウント
							if( color == FONT_PAL_RED || color == FONT_PAL_GRAY ) battlePetSkillPassCnt++;
						}
					}
					
					// 全てのスキルが、忘却または魔力切れのとき
					if( battlePetSkillCnt <= battlePetSkillPassCnt ){
						
						// 何もしないボタン表示
						BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX - 2 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx - 57, pActBattleWnd->y + 336 + 10, DISP_PRIO_MENU + 1, CG_B_BUTTON_PASS, 2 );
						// キャンセルボタン表示
						BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX - 1 ] = StockDispBuffer( ( ( WIN_DISP *)pActBattleWnd->pYobi )->cx + 57, pActBattleWnd->y + 336 + 10, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
					}else{
						// キャンセルボタン表示
						BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX - 1 ] = StockDispBuffer( pActBattleWnd->x + 96 + 40, pActBattleWnd->y + 336 + 10, DISP_PRIO_MENU + 1, CG_B_GENERAL_CANCEL_BTN_UP, 2 );
						// 何もしないボタン表示クリア
						BattlePetSkillHitFontNo[ B_PET_SKILL_WND_HIT_NO_MAX - 2 ] = -2;
					}
				}
				
			}
		}
	}
}

// バトルメニュー处理 *********************************************************/
void BattleMenuProc( void )
{
	char moji[ 256 ];
	int targetNo;
	//int i;
	static int ButtonBaseGraNo; // ボタンベースグラフィック番号
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif
	
	// 信息ウィンドウ表示
	BattleInfoWndDisp();
	
	// バトルメニュープロセス番号で分岐
	switch( BattleMenuProcNo ){
	
		case B_MENU_PLAYER_INIT:	// プレイヤーメニュー初期化 ********************/
		
			// メニュー无しの时、または観战の时
			if( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ){
				//|| ( ( BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->bcFlag & BC_FLAG_DEATH ){
				// 何もしない送信
				if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "N" );         //MLHIDE
				// プレイヤーコマンド入力フラグＯＮ
				BattleCmdPlayerInputFlag++;
				// ペットが参加しているとき（２アクションじゃない时）
				if( BattleBpFlag & BP_FLAG_PET ){
					// 次のプロセスへ
					BattleMenuProcNo = B_MENU_PET_INIT;
				}else{
					// 何もしない送信（２アクション目）
					if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "N" );        //MLHIDE
					// プレイヤーコマンド入力フラグＯＮ
					BattleCmdPlayerInputFlag++;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
				}
			}else{
#ifdef PUK2
				// リバースフラグの初期化
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
					if( BattleCmdPlayerInputFlag == 0 ) rebirthflg=0;
				}else rebirthflg=0;
#endif
				// ボタンベース画像番号の决定
				// ２アクションの时
#ifdef PUK2
				if (PackageVer >= PV_PUK2 && !rebirthflg && pc.rebirthLevel>0){
					if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
						// メニューベース
						if( BattleCmdPlayerInputFlag == 0 ){
							ButtonBaseGraNo = PUK2_BATTLE_WINDW_1;
						}else{
							ButtonBaseGraNo = PUK2_BATTLE_WINDW_2;
						}
					}else{
						ButtonBaseGraNo = PUK2_BATTLE_WINDW_3;
					}
				}else{
					if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
						// メニューベース
						if( BattleCmdPlayerInputFlag == 0 ){
							ButtonBaseGraNo = CG_B_BUTTON_BASE_2;
						}else{
							ButtonBaseGraNo = CG_B_BUTTON_BASE_3;
						}
					}else{
						ButtonBaseGraNo = CG_B_BUTTON_BASE;
					}
				}
#else
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
					// メニューベース
					if( BattleCmdPlayerInputFlag == 0 ){
						ButtonBaseGraNo = CG_B_BUTTON_BASE_2;
					}else{
						ButtonBaseGraNo = CG_B_BUTTON_BASE_3;
					}
				}else{
					ButtonBaseGraNo = CG_B_BUTTON_BASE;
				}
#endif
				
				// バックアップから戾す
				// １アクション目の时
				if( BattleCmdPlayerInputFlag == 0 ){
					// １アクション目をバックアップから戾す
					BattleMenuButtonFlagBak = BattleMenuButtonFlagBak1;
				}else{
					// ２アクション目をバックアップから戾す
					BattleMenuButtonFlagBak = BattleMenuButtonFlagBak2;
				}
				
				// 战闘ボタンの当たり判定初期化
				//InitBattleMenuButtonHitDispNo();
				InitHitNo( BattleMenuButtonHitDispNo, B_MENU_BUTTON_MAX );
				// 战闘ボタンのフラグ初期化
				InitBattleMenuButtonFlag();
				// 初期へ込みボタンの设定
				// 攻击ボタンの时
				if( BattleMenuButtonFlagBak == 0 ){
					// ボタン凹ませる
					BattleMenuButtonFlag[ 0 ] = 1;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ターゲットボックス作成
					BattleSetTargetBox( BattleMyNo, 1141 );
				}
				// スキルボタンの时
				if( BattleMenuButtonFlagBak == 2 ){
					// ボタン凹ませる
					BattleMenuButtonFlag[ 2 ] = 1;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 战闘スキルウィンドウプロセス番号初期化
					BattleSkillWndProcNo = 0;
				}
				// アイテムボタンの时
				if( BattleMenuButtonFlagBak == 3 ){
					// ボタン凹ませる
					BattleMenuButtonFlag[ 3 ] = 1;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 战闘アイテムウィンドウプロセス番号初期化
					BattleItemWndProcNo = 0;
				}
				// ペットボタンの时
				if( BattleMenuButtonFlagBak == 4 ){
					// ボタン凹ませる
					BattleMenuButtonFlag[ 4 ] = 1;
					// 当たり判定ボックス消す
					ClearBoxFlag();
				}
				// 武器ボタンの时
				if( BattleMenuButtonFlagBak == 5 ){
					// ボタン凹ませる
					BattleMenuButtonFlag[ 5 ] = 1;
					// 当たり判定ボックス消す
					ClearBoxFlag();
				}
				// 配置ボタンの时
				//if( BattleMenuButtonFlagBak == 6 ){
					// ボタン凹ませる
				//	BattleMenuButtonFlag[ 6 ] = 1;
					// 当たり判定ボックス消す
				//	ClearBoxFlag();
				//}
				
				// 次のプロセスへ
				BattleMenuProcNo = B_MENU_PLAYER;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			}
			
			// インプットフラグ初期化
			BattleMenuInputFlag = FALSE;
			break;
			
			
		case B_MENU_PLAYER:	// プレイヤーメニュー处理 *******************************/
		
			// 攻击ボタンの时
			if( BattleMenuButtonFlag[ 0 ] == 1 ){
				// 右クリックされた时
				if( mouse.onceState & MOUSE_RIGHT_CRICK ){
					// ボタン初期化
					BattleMenuButtonFlag[ 0 ] = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					// 返回音
					//play_se( SE_NO_BACK, 320, 240 );
				}
			}
			
			// バトルスキルウィンドウ处理
			if( BattleMenuButtonFlag[ 2 ] == 1 ){
				BattleSkillWnd();
			}
			// アイテムウィンドウ处理
			if( BattleMenuButtonFlag[ 3 ] == 1 ){
				BattleItemWnd();
			}
			// ペットウィンドウ处理
			if( BattleMenuButtonFlag[ 4 ] == 1 ){
				BattlePetWnd();
			}
			// 武器变更ウィンドウ处理
			if( BattleMenuButtonFlag[ 5 ] == 1 ){
				BattleWeaponWnd();
			}
			
			
			// 攻击ボタンの时
			if( BattleMenuButtonHitDispNo[ 0 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(169, "普通攻击。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 0 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					
					// 今だけフラグＯＮ
					//BattleBpFlag |= BP_FLAG_WEAPON_DIRECT;
					// ターゲットボックス作成
					BattleSetTargetBox( BattleMyNo, 1141 );	// 单体
					//BattleSetTargetBox( BattleMyNo, 189 );	// 周辺
					//BattleSetTargetBox( BattleMyNo, 309 );	// 片侧
					//BattleSetTargetBox( BattleMyNo, 573 );	// 全体
					
					
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
			// 防御ボタンの时
			if( BattleMenuButtonHitDispNo[ 1 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(170, "物理防御。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 1 ] = 1;
					// バックアップ
					//BattleMenuButtonFlagBak = -1;
					BattleMenuButtonFlagBak = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 防御送信
					nrproto_B_send( sockfd, "G" );                                   //MLHIDE
					// 次のプロセスへ
					//BattleMenuProcNo = B_MENU_PET_INIT;
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
#ifdef PUK2
			//	リバースボタンの时
			if( BattleMenuButtonHitDispNo[ 8 ] == HitDispNo ){
				if (rebirthflg){
					strcpy( OneLineInfoStr,ML_STRING(171, "现在无法精灵变身。"));
				}else
				if (pc.rebirthLevel==0){
					strcpy( OneLineInfoStr,ML_STRING(171, "现在无法精灵变身。"));
				}else{
					if (pc.bt<=0){
						strcpy( OneLineInfoStr,ML_STRING(171, "现在无法精灵变身。"));
					}else{
#ifdef PUK3_PACTBC_CHECKRANGE
						CheckIdRange( BattleMyNo );
#endif
						// 一行インフォ
						if ( ( (BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->pActTrance ){
							strcpy( OneLineInfoStr,ML_STRING(172, "解除精灵变身。"));
						}else strcpy( OneLineInfoStr,ML_STRING(173, "精灵变身。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// 战闘ボタンのフラグ初期化
							InitBattleMenuButtonFlag();
							// ボタン凹ませる
							BattleMenuButtonFlag[ 8 ] = 1;
							// バックアップ
							//BattleMenuButtonFlagBak = -1;
							BattleMenuButtonFlagBak = 0;
							// 当たり判定ボックス消す
							ClearBoxFlag();
							// リバースＯＮ送信
							if ( ( (BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->pActTrance ){
								nrproto_B_send( sockfd, "R|0" );                              //MLHIDE
							}else nrproto_B_send( sockfd, "R|1" );                         //MLHIDE
							// リバースを使用したことを判别するためのフラグ
							rebirthflg=1;
							// 次のプロセスへ
							//BattleMenuProcNo = B_MENU_PET_INIT;
							// ウィンドウアクション抹杀
							DeathAction( pActBattleWnd );
							// ポインタ初期化
							pActBattleWnd = NULL;
							// 入力济みフラグＯＮ
							BattleMenuInputFlag = TRUE;
							// ウィンドウ关闭音
							play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						}
					}
				}
			}
#endif
			// スキルボタンの时
			if( BattleMenuButtonHitDispNo[ 2 ] == HitDispNo && BattleMenuButtonFlag[ 2 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(174, "使用技能。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 2 ] = 1;
					// 当たり判定ボックス消す
					// バックアップ
					BattleMenuButtonFlagBak = 2;
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 战闘スキルウィンドウプロセス番号初期化
					BattleSkillWndProcNo = 0;
				}
			}
			// アイテムボタンの时
			if( BattleMenuButtonHitDispNo[ 3 ] == HitDispNo && BattleMenuButtonFlag[ 3 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(175, "使用物品。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 3 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 3;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 战闘アイテムウィンドウプロセス番号初期化
					BattleItemWndProcNo = 0;
#ifdef _TEST_TECH_YUK
					// ここで爆弹のチェックを行う
					{
						for( int i = 8; i < MAX_ITEM; i++){
							if( pc.item[i].kind == ITEM_BOMB){
                                int j = 0;
								for( ; j < MAX_SKILL; j++){
									if( job.skill[j].id == 116) break;
								}
								if( j >= MAX_SKILL){
									pc.item[i].battle &= ~(ITEM_FLAG_USEABLE);
								}
								else {
									pc.item[i].battle |= ITEM_FLAG_USEABLE;
								}
							}
						}
					}
#endif /* _TEST_TECH_YUK */
				}
			}
			// ペットボタンの时
			if( BattleMenuButtonHitDispNo[ 4 ] == HitDispNo && BattleMenuButtonFlag[ 4 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(176, "召唤、召还宠物。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 4 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 4;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
				}
			}
			// 武器ボタンの时
			if( BattleMenuButtonHitDispNo[ 5 ] == HitDispNo && BattleMenuButtonFlag[ 5 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(177, "变更武器。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 5 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 5;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
				}
			}
			// ポジションチェンジボタンの时
			if( BattleMenuButtonHitDispNo[ 6 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(178, "变更位置。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹む
					BattleMenuButtonFlag[ 6 ] = 1;
					// バックアップ
					//BattleMenuButtonFlagBak = -1;
					BattleMenuButtonFlagBak = 6;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 防御送信
					nrproto_B_send( sockfd, "P" );                                   //MLHIDE
					// 次のプロセスへ
					//BattleMenuProcNo = B_MENU_PET_INIT;
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			// 逃げるボタンの时
			if( BattleMenuButtonHitDispNo[ 7 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,ML_STRING(179, "逃跑。"));
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 7 ] = 1;
					// バックアップ
					//BattleMenuButtonFlagBak = -1;
					BattleMenuButtonFlagBak = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 逃げる送信
					nrproto_B_send( sockfd, "E" );                                   //MLHIDE
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			
			// ヒットチェック **************************************************
			if( ( targetNo = CheckBattelTarget() ) != -1 ){

#if 0
#ifdef _DEBUG			
				sprintf( moji, "%X", targetNo );                                  //MLHIDE
				StockFontBuffer( 100, 100, FONT_PRIO_FRONT, FONT_KIND_BIG, FONT_PAL_AQUA, moji, 0 );
#endif
#endif
			
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 攻击ボタンのとき
					if( BattleMenuButtonFlagBak ==  0 ){
						// 文字列作成
						sprintf( moji, "H|%X", targetNo );                              //MLHIDE
						// 通常攻击
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					else
					// スキルボタンのとき
					if( BattleMenuButtonFlagBak ==  2 ){
						// 文字列作成
						sprintf( moji, "S|%X|%X|%X", BattleSelectSkillNo, BattleSelectAbilityNo, targetNo ); //MLHIDE
						//sprintf( moji, "S|%X|%X|%X", BattleSelectSkillNo, BattleSelectAbilityNo, 20 );
						// スキル攻击送信
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					else
					// アイテムボタンの时
					if( BattleMenuButtonFlagBak ==  3 ){
						// 文字列作成
						sprintf( moji, "I|%X|%X", BattleSelectItemNo, targetNo );       //MLHIDE
						// 	アイテム使用を送信
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}
			
			
			
			
			// 入力されたら
			if( BattleMenuInputFlag == TRUE ){
				// プレイヤーコマンド入力フラグＯＮ
				BattleCmdPlayerInputFlag++;
				// 次のプロセスへ
				BattleMenuProcNo = B_MENU_PLAYER_BACK;
				// 当たり判定ボックス消す
				ClearBoxFlag();
			}
			// プレイヤーメニュー出现处理
			if( battleMenuA > 0 ){
				battleMenuY += battleMenuA;
				battleMenuA -= 1;
			}
			
			//battleMenuWazaFontNo[ 7 ] = StockDispBuffer( ( ( WINDOW_DISP *)pActBattleWnd->pYobi )->mx, y, DISP_PRIO_IME3, CG_CLOSE_BTN, 2 );
			
			
#ifdef PUK2
			// メニュー画像
			if (PUK2_BATTLE_WINDW_1<=ButtonBaseGraNo && ButtonBaseGraNo<=PUK2_BATTLE_WINDW_3){
				StockDispBuffer( battleMenuX, battleMenuY-8, DISP_PRIO_MENU, ButtonBaseGraNo, 1, &bm );

				// ボタン
				BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX-72, battleMenuY-15, DISP_PRIO_IME3, PUK2_ATTACK_BTN_U	+ BattleMenuButtonFlag[ 0 ], 2, &bm );
				BattleMenuButtonHitDispNo[ 1 ] = StockDispBuffer( battleMenuX-72, battleMenuY+5, DISP_PRIO_IME3, PUK2_GUARD_BTN_U	+ BattleMenuButtonFlag[ 1 ], 2, &bm );
				BattleMenuButtonHitDispNo[ 8 ] = StockDispBuffer( battleMenuX-72, battleMenuY+25, DISP_PRIO_IME3, CG_REBIRTH_BTN_U	+ BattleMenuButtonFlag[ 8 ], 2, &bm );
				
				// 使用できないボタンの时
				if( BattleUnusableButtonFlag == TRUE ){
					BattleMenuButtonHitDispNo[ 2 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 3 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 4 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_PET_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 5 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 6 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_POS_UNUSE, 0 );
				}else{
					BattleMenuButtonHitDispNo[ 2 ] = StockDispBuffer( battleMenuX-8, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UP	+ BattleMenuButtonFlag[ 2 ], 2 );
					BattleMenuButtonHitDispNo[ 3 ] = StockDispBuffer( battleMenuX-8, battleMenuY+4, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UP		+ BattleMenuButtonFlag[ 3 ], 2 );
					BattleMenuButtonHitDispNo[ 4 ] = StockDispBuffer( battleMenuX-8, battleMenuY+3, DISP_PRIO_IME3, CG_B_BUTTON_PET_UP		+ BattleMenuButtonFlag[ 4 ], 2 );
					BattleMenuButtonHitDispNo[ 5 ] = StockDispBuffer( battleMenuX-5, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UP	+ BattleMenuButtonFlag[ 5 ], 2 );
					BattleMenuButtonHitDispNo[ 6 ] = StockDispBuffer( battleMenuX-5, battleMenuY+4, DISP_PRIO_IME3, CG_B_BUTTON_POS_UP		+ BattleMenuButtonFlag[ 6 ], 2 );
				}
				BattleMenuButtonHitDispNo[ 7 ] = StockDispBuffer( battleMenuX-5, battleMenuY+3, DISP_PRIO_IME3, CG_B_BUTTON_ESC_UP		+ BattleMenuButtonFlag[ 7 ], 2 );
			}else{
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, ButtonBaseGraNo, 1 );

				// ボタン
				BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ATTACK_UP	+ BattleMenuButtonFlag[ 0 ], 2 );
				BattleMenuButtonHitDispNo[ 1 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_GUARD_UP	+ BattleMenuButtonFlag[ 1 ], 2 );
			
				// 使用できないボタンの时
				if( BattleUnusableButtonFlag == TRUE ){
					BattleMenuButtonHitDispNo[ 2 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 3 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 4 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 5 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 6 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UNUSE, 0 );
				}else{
					BattleMenuButtonHitDispNo[ 2 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UP	+ BattleMenuButtonFlag[ 2 ], 2 );
					BattleMenuButtonHitDispNo[ 3 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UP		+ BattleMenuButtonFlag[ 3 ], 2 );
					BattleMenuButtonHitDispNo[ 4 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UP		+ BattleMenuButtonFlag[ 4 ], 2 );
					BattleMenuButtonHitDispNo[ 5 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UP	+ BattleMenuButtonFlag[ 5 ], 2 );
					BattleMenuButtonHitDispNo[ 6 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UP		+ BattleMenuButtonFlag[ 6 ], 2 );
				}
				BattleMenuButtonHitDispNo[ 7 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ESC_UP		+ BattleMenuButtonFlag[ 7 ], 2 );
			}
#else
			// メニュー画像
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, ButtonBaseGraNo, 1 );

			// ボタン
			BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ATTACK_UP	+ BattleMenuButtonFlag[ 0 ], 2 );
			BattleMenuButtonHitDispNo[ 1 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_GUARD_UP	+ BattleMenuButtonFlag[ 1 ], 2 );
			
			// 使用できないボタンの时
			if( BattleUnusableButtonFlag == TRUE ){
				BattleMenuButtonHitDispNo[ 2 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 3 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 4 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 5 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 6 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UNUSE, 0 );
			}else{
				BattleMenuButtonHitDispNo[ 2 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UP	+ BattleMenuButtonFlag[ 2 ], 2 );
				BattleMenuButtonHitDispNo[ 3 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UP		+ BattleMenuButtonFlag[ 3 ], 2 );
				BattleMenuButtonHitDispNo[ 4 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UP		+ BattleMenuButtonFlag[ 4 ], 2 );
				BattleMenuButtonHitDispNo[ 5 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UP	+ BattleMenuButtonFlag[ 5 ], 2 );
				BattleMenuButtonHitDispNo[ 6 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UP		+ BattleMenuButtonFlag[ 6 ], 2 );
			}
			BattleMenuButtonHitDispNo[ 7 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ESC_UP		+ BattleMenuButtonFlag[ 7 ], 2 );
#endif
#if 0			
			if( BattleCmdPlayerInputFlag == FALSE ){
				StockFontBuffer( 400,  80, FONT_PRIO_FRONT, FONT_PAL_YELLOW, "玩家行动①", 0 ); //MLHIDE
			}else{
				StockFontBuffer( 400,  80, FONT_PRIO_FRONT, FONT_PAL_YELLOW, "玩家行动②", 0 ); //MLHIDE
			}
			
			StockFontBuffer( 400, 100, FONT_PRIO_FRONT, FONT_PAL_WHITE, "选择攻击目标", 0 ); //MLHIDE
			StockFontBuffer( 400, 120, FONT_PRIO_FRONT, FONT_PAL_WHITE, "Ｆ１：防御", 0 ); //MLHIDE
			StockFontBuffer( 400, 140, FONT_PRIO_FRONT, FONT_PAL_WHITE, "Ｆ２：逃跑", 0 ); //MLHIDE
			StockFontBuffer( 400, 160, FONT_PRIO_FRONT, FONT_PAL_WHITE, "Ｆ３：技能", 0 ); //MLHIDE
#endif			
			
			break;
			
		case B_MENU_PLAYER_BACK:	// プレイヤーメニュー消える处理 *******************************/
		
			// プレイヤーメニュー消える处理
			if( battleMenuA <= 16 ){
				battleMenuA += 1;
				battleMenuY -= battleMenuA;
			}else{
				// 入力济みフラグＯＦＦ
				BattleMenuInputFlag = FALSE;
				
				// １アクション入力している时
				if( BattleCmdPlayerInputFlag == TRUE ){
					// ２アクションのとき
					if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
						// 次のプロセスへ
						BattleMenuProcNo = B_MENU_PLAYER_INIT;
						
						// １アクション目のボタン记忆
						BattleMenuButtonFlagBak1 = BattleMenuButtonFlagBak;
						// ポジションチェンジの时だけ、攻击にする
						if( BattleMenuButtonFlagBak1 == 6 ) BattleMenuButtonFlagBak1 = 0;
						
						// ２アクション时の使用できないボタンの决定
						if( BattleMenuButtonFlagBak >= 2 && BattleMenuButtonFlagBak <= 6 ){
							// 使えないボタンフラグＯＮ
							BattleUnusableButtonFlag = TRUE;
							// 攻击ボタンを凹ます
							BattleMenuButtonFlagBak2 = 0;
						}
					}else{
						// １アクション目のボタン记忆
						BattleMenuButtonFlagBak1 = BattleMenuButtonFlagBak;
						// ポジションチェンジの时だけ、攻击にする
						if( BattleMenuButtonFlagBak1 == 6 ) BattleMenuButtonFlagBak1 = 0;
						// 次のプロセスへ
						BattleMenuProcNo = B_MENU_PET_INIT;
					}
				}else{
					// ２アクション目の时
					BattleMenuButtonFlagBak2 = BattleMenuButtonFlagBak;
					// ポジションチェンジの时だけ、攻击にする
					if( BattleMenuButtonFlagBak2 == 6 ) BattleMenuButtonFlagBak2 = 0;
					// 次のプロセスへ
					//BattleMenuProcNo = B_MENU_PET_INIT;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
				}
			}
			
			
			//battleMenuWazaFontNo[ 7 ] = StockDispBuffer( ( ( WINDOW_DISP *)pActBattleWnd->pYobi )->mx, y, DISP_PRIO_IME3, CG_CLOSE_BTN, 2 );
			
#ifdef PUK2
			// メニュー画像
			if (PUK2_BATTLE_WINDW_1<=ButtonBaseGraNo && ButtonBaseGraNo<=PUK2_BATTLE_WINDW_3){
				StockDispBuffer( battleMenuX, battleMenuY-8, DISP_PRIO_MENU, ButtonBaseGraNo, 1, &bm );

				// ボタン
				BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX-72, battleMenuY-15, DISP_PRIO_IME3, PUK2_ATTACK_BTN_U	+ BattleMenuButtonFlag[ 0 ], 2, &bm );
				BattleMenuButtonHitDispNo[ 1 ] = StockDispBuffer( battleMenuX-72, battleMenuY+5, DISP_PRIO_IME3, PUK2_GUARD_BTN_U	+ BattleMenuButtonFlag[ 1 ], 2, &bm );
				BattleMenuButtonHitDispNo[ 8 ] = StockDispBuffer( battleMenuX-72, battleMenuY+25, DISP_PRIO_IME3, CG_REBIRTH_BTN_U	+ BattleMenuButtonFlag[ 8 ], 2, &bm );
				
				// 使用できないボタンの时
				if( BattleUnusableButtonFlag == TRUE ){
					BattleMenuButtonHitDispNo[ 2 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 3 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 4 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_PET_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 5 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 6 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_POS_UNUSE, 0 );
				}else{
					BattleMenuButtonHitDispNo[ 2 ] = StockDispBuffer( battleMenuX-8, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UP	+ BattleMenuButtonFlag[ 2 ], 2 );
					BattleMenuButtonHitDispNo[ 3 ] = StockDispBuffer( battleMenuX-8, battleMenuY+4, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UP		+ BattleMenuButtonFlag[ 3 ], 2 );
					BattleMenuButtonHitDispNo[ 4 ] = StockDispBuffer( battleMenuX-8, battleMenuY+3, DISP_PRIO_IME3, CG_B_BUTTON_PET_UP		+ BattleMenuButtonFlag[ 4 ], 2 );
					BattleMenuButtonHitDispNo[ 5 ] = StockDispBuffer( battleMenuX-5, battleMenuY+5, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UP	+ BattleMenuButtonFlag[ 5 ], 2 );
					BattleMenuButtonHitDispNo[ 6 ] = StockDispBuffer( battleMenuX-5, battleMenuY+4, DISP_PRIO_IME3, CG_B_BUTTON_POS_UP		+ BattleMenuButtonFlag[ 6 ], 2 );
				}
				BattleMenuButtonHitDispNo[ 7 ] = StockDispBuffer( battleMenuX-5, battleMenuY+3, DISP_PRIO_IME3, CG_B_BUTTON_ESC_UP		+ BattleMenuButtonFlag[ 7 ], 2 );
			}else{
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, ButtonBaseGraNo, 1 );
				
				BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ATTACK_UP	+ BattleMenuButtonFlag[ 0 ], 2 );
				BattleMenuButtonHitDispNo[ 1 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_GUARD_UP	+ BattleMenuButtonFlag[ 1 ], 2 );

				// 使用できないボタンの时
				if( BattleUnusableButtonFlag == TRUE ){
					BattleMenuButtonHitDispNo[ 2 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 3 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 4 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 5 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UNUSE, 0 );
					BattleMenuButtonHitDispNo[ 6 ] = -2;
					StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UNUSE, 0 );
				}else{
					BattleMenuButtonHitDispNo[ 2 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UP	+ BattleMenuButtonFlag[ 2 ], 2 );
					BattleMenuButtonHitDispNo[ 3 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UP		+ BattleMenuButtonFlag[ 3 ], 2 );
					BattleMenuButtonHitDispNo[ 4 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UP		+ BattleMenuButtonFlag[ 4 ], 2 );
					BattleMenuButtonHitDispNo[ 5 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UP	+ BattleMenuButtonFlag[ 5 ], 2 );
					BattleMenuButtonHitDispNo[ 6 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UP		+ BattleMenuButtonFlag[ 6 ], 2 );
				}
				BattleMenuButtonHitDispNo[ 7 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ESC_UP		+ BattleMenuButtonFlag[ 7 ], 2 );
			}
#else
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, ButtonBaseGraNo, 1 );
			
			BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ATTACK_UP	+ BattleMenuButtonFlag[ 0 ], 2 );
			BattleMenuButtonHitDispNo[ 1 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_GUARD_UP	+ BattleMenuButtonFlag[ 1 ], 2 );

			// 使用できないボタンの时
			if( BattleUnusableButtonFlag == TRUE ){
				BattleMenuButtonHitDispNo[ 2 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 3 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 4 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 5 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UNUSE, 0 );
				BattleMenuButtonHitDispNo[ 6 ] = -2;
				StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UNUSE, 0 );
			}else{
				BattleMenuButtonHitDispNo[ 2 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_SKILL_UP	+ BattleMenuButtonFlag[ 2 ], 2 );
				BattleMenuButtonHitDispNo[ 3 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ITEM_UP		+ BattleMenuButtonFlag[ 3 ], 2 );
				BattleMenuButtonHitDispNo[ 4 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_PET_UP		+ BattleMenuButtonFlag[ 4 ], 2 );
				BattleMenuButtonHitDispNo[ 5 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_EQUIP_UP	+ BattleMenuButtonFlag[ 5 ], 2 );
				BattleMenuButtonHitDispNo[ 6 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_POS_UP		+ BattleMenuButtonFlag[ 6 ], 2 );
			}
			BattleMenuButtonHitDispNo[ 7 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ESC_UP		+ BattleMenuButtonFlag[ 7 ], 2 );
#endif
			
			break;
			
			
		case B_MENU_PET_INIT:	// ペットメニュー初期化 ********************************/
		
			// ペットが参加しているとき
			if( BattleBpFlag & BP_FLAG_PET ){
			
				// メニュー无しの时、または観战の时
				if( BattleBpFlag & BP_FLAG_PET_MENU_NON ){
				
					//|| ( ( BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->bcFlag & BC_FLAG_DEATH ){
					// 何もしない送信
					if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "W|FF" );     //MLHIDE
					// ペットコマンド入力フラグＯＮ
					BattleCmdPetInputFlag++;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
					
				}else{
				
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
					// スキル番号バックアップがあるときかつ、そのスキルが使えるとき（忘却チェック）かつ、魔力切れじゃないとき
					if( BattlePetSkillSelectNo != -1
						&& BattlePetUsableSkillFlag & ( 1 << BattlePetSkillSelectNo ) 
						&& !( pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp )
						){
					
						// ターゲットボックス作成
						//BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), 1141 );
						//BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), pet[ BattlePetNoBak ].tech[ BattlePetSkillSelectNo ].target );
						// 谁も选择できないとき
						if( BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].target ) == FALSE ){
							// バックアップがないときは选择しなおし
							BattleMenuPetButtonFlag = 1;
						}
						
					}else{
						// バックアップがないときは选择しなおし
						BattleMenuPetButtonFlag = 1;
					}
					// 战闘ペットベースウィンドウの当たり判定番号初期化
					BattleMenuPetBaseHitFontNo = -2;
					// 次のプロセスへ
					BattleMenuProcNo = B_MENU_PET;
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					
				}
				
			}else{
			
				// 次のプロセスへ
				SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
				
			}
				
			break;
			
			
		case B_MENU_PET:	// ペットメニュー ****************************************/
			
			// ヒットチェック
			if( ( targetNo = CheckBattelTarget() ) != -1 ){
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 文字列作成
					sprintf( moji, "W|%X|%X", BattlePetSkillSelectNo, targetNo );    //MLHIDE
					// 通常攻击
					nrproto_B_send( sockfd, moji );
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			// ペットスキルウィンドウの状态で分岐
			switch( BattleMenuPetButtonFlag ){
			
				case 0:
				
					// 右クリックされた时
					if( mouse.onceState & MOUSE_RIGHT_CRICK ){
						// ボタンフラグ变更
						BattleMenuPetButtonFlag = 1;
					}
					
					// キャンセルボタン押した时
					if( BattleMenuPetBaseHitFontNo == HitDispNo ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(180, "打开技能列表窗口。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// ボタンフラグ变更
							BattleMenuPetButtonFlag = 1;
						}
					}
					
					// スキルリスト凸ボタン表示
					BattleMenuPetBaseHitFontNo = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_BUTTON_SKILL_LIST_UP, 2 );
					
				break;
					
				case 1:
				
					// ペットスキルウィンドウ表示处理
					BattlePetSkillWnd();
					
					// キャンセルボタン押した时
					if( BattleMenuPetBaseHitFontNo == HitDispNo ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(181, "关闭技能列表窗口。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// ウィンドウアクション抹杀
							DeathAction( pActBattleWnd );
							// ポインタ初期化
							pActBattleWnd = NULL;
							// ボタンもどす
							BattleMenuPetButtonFlag = 0;
							// ウィンドウ关闭音
							play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						}
					}
					
					// スキルリスト凹ボタン表示
					BattleMenuPetBaseHitFontNo = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_BUTTON_SKILL_LIST_DOWN, 2 );
					
				break;
					
				case 2:
				
					// 右クリックされた时
					if( mouse.onceState & MOUSE_RIGHT_CRICK ){
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// ボタンフラグ变更
						BattleMenuPetButtonFlag = 1;
						// ペットスキルバックアップ初期化
						BattlePetSkillSelectNo = -1;
					}
					// キャンセルボタン押した时
					if( BattleMenuPetBaseHitFontNo == HitDispNo ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(182, "回到技能列表窗口。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// 当たり判定ボックス消す
							ClearBoxFlag();
							// ボタンフラグ变更
							BattleMenuPetButtonFlag = 1;
							// ペットスキルバックアップ初期化
							BattlePetSkillSelectNo = -1;
						}
					}
					// ペットスキルバックアップがあるとき
					if( BattlePetSkillSelectNo != -1 ){
						// ペットスキル名表示
						//sprintf( moji, "%-16s", "０１２３４５６７８" );
						// 文字列作成。
						sprintf( moji,"%-16s",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].name ); //MLHIDE
						//sprintf( moji, "%-16s", "攻击" );
						//sprintf( moji, "9999" );
						StockFontBuffer( battleMenuX - 114, battleMenuY - 15, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, moji, 0 );
						// フォースポイント表示
						sprintf( moji,"%4d",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].fp ); //MLHIDE
						//sprintf( moji, "9999" );
						StockFontBuffer( battleMenuX + 90, battleMenuY - 12, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
					}
					// キャンセル凸ボタン表示
					BattleMenuPetBaseHitFontNo = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_M_BUTTON_CANCEL_UP, 2 );
				
				break;
			}
			
			// 入力されたら
			if( BattleMenuInputFlag == TRUE ){
				// ペットコマンド入力フラグＯＮ
				BattleCmdPetInputFlag++;
				// 次のプロセスへ
				BattleMenuProcNo = B_MENU_PET_BACK;
				// このターンのペット番号を学习
				BattlePetNoBak = CheckBattlePet();
			}
			
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
			// フォースポイント表示
			//sprintf( moji, "%4d", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp );
			sprintf( moji, "%4d/%4d", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp ); //MLHIDE
			//sprintf( moji, "9999" );
			StockFontBuffer( battleMenuX - 146 + 121, battleMenuY - 42 + 53, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
			// メニュー画像
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_MONSTER_BASE, 1 );
			// ボタン
			//BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ATTACK_UP	+ BattleMenuButtonFlag[ 0 ], 2 );
			
			// ペットメニュー出现处理
			if( battleMenuA > 0 ){
				battleMenuY += battleMenuA;
				battleMenuA -= 1;
			}
			
			break;
			
			
		case B_MENU_PET_BACK:	// ペットメニュー消える处理 *******************************/
		
			// ペットメニュー消える处理
			if( battleMenuA <= 16 ){
				battleMenuA += 1;
				battleMenuY -= battleMenuA;
			}else{
				// 次のプロセスへ
				SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
			}
			
			// 文字列作成。
			sprintf( moji,"%-16s",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].name ); //MLHIDE
			//sprintf( moji, "%-16s", "攻击" );
			//sprintf( moji, "9999" );
			StockFontBuffer( battleMenuX - 114, battleMenuY - 15, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, moji, 0 );
			// フォースポイント表示
			sprintf( moji,"%4d",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].fp ); //MLHIDE
			//sprintf( moji, "9999" );
			StockFontBuffer( battleMenuX + 90, battleMenuY - 12, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
			
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
			// フォースポイント表示
			sprintf( moji, "%4d/%4d", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp ); //MLHIDE
			//sprintf( moji, "9999" );
			StockFontBuffer( battleMenuX - 146 + 121, battleMenuY - 42 + 53, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
			//StockFontBuffer( battleMenuX + 12, battleMenuY + 12, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
			// キャンセル凸ボタン表示
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_M_BUTTON_CANCEL_UP, 2 );
			// メニュー画像
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_MONSTER_BASE, 1 );
			
			break;
	}
	
	
	// いずれかのメニューがあるとき
	if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON )
		|| !( BattleBpFlag & BP_FLAG_PET_MENU_NON )
		|| !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
		
		// カウントダウン表示とリミットチェック
		BattleCntDownDisp();
	}
	
}

// ヒットボックスＯＮ ***********************************************************/
void BattleHitBox( int myNo )
{
	int i;
	
	// 当たり判定ボックス表示
	for( i = 0 ; i < BC_MAX ; i++ ){
		// 自分なら返回
		if( i == myNo ) continue;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 死んでいる时
		//if( pActBc[ i ]->hp <= 0 ) continue;
		if( ( ( BC_YOBI *)pActBc[ i ]->pYobi )->bcFlag & BC_FLAG_DEATH ) continue;
		// 当たり判定ボックス表示フラグＯＮ
		pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
	}
}

// アイテムヒットボックスＯＮ ***********************************************************/
void BattleSetItemHitBox( int myNo )
{
	int i;
	
	// 当たり判定ボックス表示
	for( i = 0 ; i < BC_MAX ; i++ ){
		// 自分なら返回
		if( i == myNo ) continue;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 死んでいる时
		//if( pActBc[ i ]->hp <= 0 ) continue;
		if( ( ( BC_YOBI *)pActBc[ i ]->pYobi )->bcFlag & BC_FLAG_DEATH ) continue;
		// 当たり判定ボックス表示フラグＯＮ
		pActBc[ i ]->atr |= ACT_ATR_HIT_BOX;
	}
}

// 战闘コマンドバックアップ初期化关数 **************************************************/
void InitBattleBakUpCmd( void )
{
	// 战闘ボタンフラグバックアップ初期化
	BattleMenuButtonFlagBak1 = 0;
	BattleMenuButtonFlagBak2 = 0;
	// 前回の使用したペットの番号记忆用初期化
	BattlePetNoBak = -1;
	// ペットスキル番号バックアップ初期化
	BattlePetSkillSelectNo = -1;
}
