﻿#ifndef __INCLUDE_IDMP_H__
#define __INCLUDE_IDMP_H__

#include "../systeminc/system.h"
#ifdef DIRECT_MUSIC		//DirectMusicシステム

#include <windows.h>
#include <dmusicc.h>

#if defined( _WIN32 ) && !defined( _NO_COM)
DEFINE_GUID( GUID_NULL , 0,0,0, 0,0,0,0,0,0,0,0 );
#endif


int dmc_InitDirectMusic( HWND hwnd );
int dmc_UninitDirectMusic( void );

int dmc_OutShortMsg( unsigned long msg );

int dmc_LoadDLS( char *pFile );
int dmc_SetDLSInstrument( long patch );
void dmc_KillDLSInstrument( void );

int dmc_SetSynthsizerProperty( DWORD dwPeriod ,  DWORD dwLatency );

int dmc_OutShortMsg2( REFERENCE_TIME rt, unsigned long msg );
REFERENCE_TIME GetClockTime( void );

void create_semaphore( void );
void close_semaphore( void );
void start_semaphore( void );
void stop_semaphore( void );
void wait_semaphore( void );
void dmc_OutExclusiveMsg( int cnt, unsigned char *buf );

int InitDSound(void);
int dmc_LoadDLS2( char *pFile );
void set_reverb( int ReverbTime, int depth );
void dmc_releaseDMLoader( void );

void download_dls( char *path );
BOOL LoadMIDIFile(char *fname);
BOOL PlaySgt();
BOOL StopSgt();
BOOL IsPlayingSgt();
BOOL RepeatsSgt(DWORD cou);
void end_music( void );
BOOL check_sgt_fade( void );
BOOL start_sgt_fade_out( unsigned char spd );
BOOL PlaySMF( int no, int loop );

#endif	//DIRECT_MUSIC

#endif

