﻿#include "../../systeminc/system.h"
#include "../../systeminc/directDraw.h"
#include "../../systeminc/main.h"
#include "../../systeminc/gamemain.h"
#include "../../systeminc/sprmgr.h"
#include "../../systeminc/init.h"
#include "../../systeminc/process.h"
#include "../../systeminc/action.h"
#include "../../systeminc/sprdisp.h"
#include "../../systeminc/math2.h"
#include "../../systeminc/chat.h"
#include "../../systeminc/font.h"
#include "../../systeminc/mouse.h"
#include "../../systeminc/radar.h"
#include "../../systeminc/gemini.h"
#include "../../systeminc/pattern.h"
#include "../../systeminc/ime_sa.h"
#include "../../systeminc/menu.h"
#include "../../systeminc/pc.h"
#include "../../systeminc/character.h"
#include "../../systeminc/login.h"
#include "../../systeminc/netproc.h"
#include "../../systeminc/savedata.h"
#include "../../systeminc/testView.h"
#include "../../systeminc/battleProc.h"
#include "../../systeminc/produce.h"
#include "../../systeminc/nrproto_cli.h"
#include "../../systeminc/netmain.h"
#include "../../systeminc/battleMenu.h"
#include "../../systeminc/t_music.h"
#include "../../systeminc/field.h"
#include "../../systeminc/handletime.h"
#include "../../systeminc/map.h"
#include "../../systeminc/mapEffect.h"
#include "../../ohta/ohta.h"
#include "../../systeminc/keyboard.h"
#include "../../systeminc/direct3d.h"
#include "../../systeminc/sndcnf.h"
#include "../../systeminc/mapGridCursol.h"
#include "../../systeminc/tool.h"

#include "../../puk2/interface/menuwin.h"
#include "../../puk2/newDraw/Graphic_ID.h"

#ifdef PUK3_PROF

#include "profile.h"
#include "../account/account.h"

#define DEFAULT_STR	"未设定"
#define NOOPEN_STR	"非公开"

PROFILE_CATEGORY_SYSTEM categorySys;
PROFILE_STATUS MyProfile[MAXCHARACTER];
PROFILE_STATUS MyProfile_SendWork;
PROFILE_STATUS UserNpcProfile;
PROFILE_STATUS SearchProfile;
PROFILE_HIT_LIST ProfileHitList;
PROFILE_SENDMAIL ProfileSendMail;
int ProfileListMode;
int MiniMailBookCounter;
char ProfileSearchStr[256];

ADDRESS_BOOK_INFO miniMailBook[MINI_MAIL_STOCK_MAX];
MAIL_HISTORY miniMailHistory[MINI_MAIL_STOCK_MAX];
int miniMailBookSort[MINI_MAIL_STOCK_MAX];

void clearProfile(void){

	int i;

	//ミニメールブック初期化
	for(i=0;i<MINI_MAIL_STOCK_MAX;i++)
		miniMailBook[i].useFlag=FALSE;

	memset((void *)miniMailHistory,0,sizeof(MAIL_HISTORY)*MINI_MAIL_STOCK_MAX);

	//自分のプロフィール情报初期化
	memset((void *)MyProfile,0,sizeof(PROFILE_STATUS)*MAXCHARACTER);
	memset((void *)&MyProfile_SendWork,0,sizeof(PROFILE_STATUS));
}

void setDefaultProfile(void){

	int i;

	strcpy(MyProfile[0].name,"");
	MyProfile[0].lv=-1;
	MyProfile[0].graphID;
	MyProfile[0].open=FALSE;
	strcpy(MyProfile[0].titleName,"");
	strcpy(MyProfile[0].jobName,"");
	strcpy(MyProfile[0].guildName,"");

	MyProfile[0].SellID=0;
	MyProfile[0].SellGraphID=GID_ProfCategoryDef;
	strcpy(MyProfile[0].SellStr,DEFAULT_STR);

	MyProfile[0].BuyID=0;
	MyProfile[0].BuyGraphID=GID_ProfCategoryDef;
	strcpy(MyProfile[0].BuyStr,DEFAULT_STR);

	MyProfile[0].AboutID=0;
	MyProfile[0].AboutGraphID=GID_ProfCategoryDef;
	strcpy(MyProfile[0].AboutStr,DEFAULT_STR);
	
	strcpy(MyProfile[0].ProfileStr,DEFAULT_STR);

	for(i=1;i<MAXCHARACTER;i++){
		memcpy((void *)&MyProfile[i],(void *)&MyProfile[0],sizeof(PROFILE_STATUS));
	}

	memcpy((void *)&MyProfile_SendWork,(void *)&MyProfile[selectPcNo],sizeof(PROFILE_STATUS));
}

//ワークと送信用ワークの违いがあるかチェック
BOOL checkProfileSendWork(void){

	if(MyProfile[selectPcNo].SellID!=MyProfile_SendWork.SellID){
		return TRUE;
	}
	if(MyProfile[selectPcNo].BuyID!=MyProfile_SendWork.BuyID){
		return TRUE;
	}
	if(MyProfile[selectPcNo].AboutID!=MyProfile_SendWork.AboutID){
		return TRUE;
	}

	if(strcmp(MyProfile[selectPcNo].SellStr,MyProfile_SendWork.SellStr)!=0){
		return TRUE;
	}
	if(strcmp(MyProfile[selectPcNo].BuyStr,MyProfile_SendWork.BuyStr)!=0){
		return TRUE;
	}
	if(strcmp(MyProfile[selectPcNo].AboutStr,MyProfile_SendWork.AboutStr)!=0){
		return TRUE;
	}

	if(strcmp(MyProfile[selectPcNo].ProfileStr,MyProfile_SendWork.ProfileStr)!=0){
		return TRUE;
	}

	return FALSE;

}

//プロフィール情报送信
void sendProfile(int type){

	char sendWork00[256];
	char sendWork01[256];
	char sendWork02[256];
	char sendWork03[256];
	char makeSendWork[256];

	switch(type){
	//MyProfile_SendWorkを送信し、MyProfileにMyProfile_SendWorkをコピー
	case 0:

		if(strcmp(MyProfile_SendWork.SellStr,"")==0)
			strcpy(MyProfile_SendWork.SellStr,DEFAULT_STR);
		if(strcmp(MyProfile_SendWork.BuyStr,"")==0)
			strcpy(MyProfile_SendWork.BuyStr,DEFAULT_STR);
		if(strcmp(MyProfile_SendWork.AboutStr,"")==0)
			strcpy(MyProfile_SendWork.AboutStr,DEFAULT_STR);
		if(strcmp(MyProfile_SendWork.ProfileStr,"")==0)
			strcpy(MyProfile_SendWork.ProfileStr,DEFAULT_STR);
		
		if(MyProfile_SendWork.SellID==0){
			strcpy(makeSendWork,NOOPEN_STR);
		}else{
			strcpy(makeSendWork,MyProfile_SendWork.SellStr);
		}
		makeSendString( makeSendWork,		sendWork00, sizeof(sendWork00) );

		if(MyProfile_SendWork.BuyID==0){
			strcpy(makeSendWork,NOOPEN_STR);
		}else{
			strcpy(makeSendWork,MyProfile_SendWork.BuyStr);
		}
		makeSendString( makeSendWork,		sendWork01, sizeof(sendWork01) );

		if(MyProfile_SendWork.AboutID==0){
			strcpy(makeSendWork,NOOPEN_STR);
		}else{
			strcpy(makeSendWork,MyProfile_SendWork.AboutStr);
		}
		makeSendString( makeSendWork,	sendWork02, sizeof(sendWork02) );

		strcpy(makeSendWork,MyProfile_SendWork.ProfileStr);
		makeSendString( makeSendWork,	sendWork03, sizeof(sendWork03) );
				
		//送信
		nrproto_PRS_send( sockfd,
					MyProfile_SendWork.SellID,	sendWork00,
					MyProfile_SendWork.BuyID,	sendWork01,
					MyProfile_SendWork.AboutID,	sendWork02,
					sendWork03);			

		//この情报はサーバーからプロトコルの返信がないので
		//自前で处理
		strcpy(MyProfile[selectPcNo].name,MyProfile_SendWork.name);

		MyProfile[selectPcNo].SellID=MyProfile_SendWork.SellID;
		MyProfile[selectPcNo].SellGraphID=MyProfile_SendWork.SellGraphID;
		strcpy(MyProfile[selectPcNo].SellStr,MyProfile_SendWork.SellStr);

		MyProfile[selectPcNo].BuyID=MyProfile_SendWork.BuyID;
		MyProfile[selectPcNo].BuyGraphID=MyProfile_SendWork.BuyGraphID;
		strcpy(MyProfile[selectPcNo].BuyStr,MyProfile_SendWork.BuyStr);

		MyProfile[selectPcNo].AboutID=MyProfile_SendWork.AboutID;
		MyProfile[selectPcNo].AboutGraphID=MyProfile_SendWork.AboutGraphID;
		strcpy(MyProfile[selectPcNo].AboutStr,MyProfile_SendWork.AboutStr);
		
		strcpy(MyProfile[selectPcNo].ProfileStr,MyProfile_SendWork.ProfileStr);

		break;

	//MyProfileを送信する
	case 1:

		if(MyProfile_SendWork.SellID==0){
			strcpy(makeSendWork,NOOPEN_STR);
		}else{
			strcpy(makeSendWork,MyProfile[selectPcNo].SellStr);
		}
		makeSendString( makeSendWork,		sendWork00, sizeof(sendWork00) );

		if(MyProfile_SendWork.BuyID==0){
			strcpy(makeSendWork,NOOPEN_STR);
		}else{
			strcpy(makeSendWork,MyProfile[selectPcNo].BuyStr);
		}
		makeSendString( makeSendWork,		sendWork01, sizeof(sendWork01) );

		if(MyProfile_SendWork.AboutID==0){
			strcpy(makeSendWork,NOOPEN_STR);
		}else{
			strcpy(makeSendWork,MyProfile[selectPcNo].AboutStr);
		}
		makeSendString( makeSendWork,	sendWork02, sizeof(sendWork02) );

		strcpy(makeSendWork,MyProfile[selectPcNo].ProfileStr);
		makeSendString( makeSendWork,	sendWork03, sizeof(sendWork03) );
				
		nrproto_PRS_send( sockfd,
							MyProfile[selectPcNo].SellID,	sendWork00,
							MyProfile[selectPcNo].BuyID,	sendWork01,
							MyProfile[selectPcNo].AboutID,	sendWork02,
							sendWork03);			
		break;
	}

}

//ログイン时にサーバーへ情报を送る
void sendProfileInLogin(void){

	if(MyProfile[selectPcNo].open){
		sendProfile(1);
		nrproto_PRO_send(sockfd,1);
	}else{
		sendProfile(1);
		nrproto_PRO_send(sockfd,0);
	}
}

//ユーザーＮＰＣのプロフィールウインドウを开きます
void userNpcProfileWinOpen(void){

	createMenuWindow( MENU_WINDOW_PROFILE_USER_NPC );

}

//ＩＤから分类ーのグラフィック番号を返します
int getCategoryGraphicID(int id,int type){

	int i;

	switch(type){
		case PROFILE_CATEGORY_SELLBUY:
			for(i=0;i<PROFILE_MENU_MAX;i++){
				if(categorySys.Sell[i].use==TRUE){
					if(categorySys.Sell[i].ID==id)
						return categorySys.Sell[i].GraphNo;
				}
			}
			break;
		case PROFILE_CATEGORY_ABOUT:
			for(i=0;i<PROFILE_MENU_MAX;i++){
				if(categorySys.About[i].use==TRUE){
					if(categorySys.About[i].ID==id)
						return categorySys.About[i].GraphNo;
				}
			}
			break;
	}

	return -1;
}

//ＩＤからメモリ位置を取得
int getMiniMailMemIndexfromID(int id){

	int i;

	for(i=0;i<MINI_MAIL_STOCK_MAX;i++){
		if(miniMailBook[i].useFlag==TRUE){
			if(miniMailBook[i].id==id){
				return i;
			}
		}
	}

	return -1;
}

//空いてるミニメールアドレスの场所を探す
int getMiniMailNoUseMemIndex(void){

	int i;

	for(i=0;i<MINI_MAIL_STOCK_MAX;i++){
		if(miniMailBook[i].useFlag==FALSE){
			return i;
		}
	}

	return -1;

}

//ミニメールログをセット
void setMiniMailHistory( int index, int sendFlag, char *header, char *buf )
{
	int i;

	// キャラがいないのにメール着たら无视
	if( miniMailBook[index].useFlag == FALSE ){
		return;
	}

	// 古いデータをずらす
	for( i = MAIL_HISTORY_CNT-1; i > 0; i-- ){
		memcpy(
			&miniMailHistory[index].mailInfo[i],
			&miniMailHistory[index].mailInfo[i-1],
			sizeof( MAIL_INFO ) );
	}

	if( sendFlag == 0 ){
		// 受信データなら未読フラグを立てる
		miniMailHistory[index].mailInfo[0].readFlag = 1;
	}else{
		// 送信データは既読フラグを立てる
		miniMailHistory[index].mailInfo[0].readFlag = 0;
	}
	miniMailHistory[index].mailInfo[0].sendFlag = sendFlag;
	strcpy( miniMailHistory[index].mailInfo[0].header, header );
	strcpy( miniMailHistory[index].mailInfo[0].buf, buf );

}

//ミニメールブックから一人分消す
void deleteMiniMailBookMember(int id){

	int i;

	miniMailBook[id].useFlag=FALSE;

	for(i=0;i<MINI_MAIL_STOCK_MAX;i++){
		strcpy(miniMailHistory[id].mailInfo[i].buf,"");
		strcpy(miniMailHistory[id].mailInfo[i].header,"");
		miniMailHistory[id].mailInfo[i].readFlag=0;
		miniMailHistory[id].mailInfo[i].sendFlag=0;
	}	

}

//ローカルタイムを文字列で作成
void makeLocalTime(char *timeStr){

	time_t t;
	struct tm *tt;
	char header[256];

	time( &t );
	tt = localtime( &t );
	sprintf( header, "%4d/%2d/%2d %02d:%02d",                            //MLHIDE
		1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

	strcpy(timeStr,header);
}

//特定のselectPcNoのプロフィールをクリア
void ClearProfilePcNo(int select){

	strcpy(MyProfile[select].name,"");
	MyProfile[select].lv=-1;
	MyProfile[select].graphID;
	MyProfile[select].open=FALSE;
	strcpy(MyProfile[select].titleName,"");
	strcpy(MyProfile[select].jobName,"");
	strcpy(MyProfile[select].guildName,"");

	MyProfile[select].SellID=0;
	MyProfile[select].SellGraphID=GID_ProfCategoryDef;
	strcpy(MyProfile[select].SellStr,DEFAULT_STR);

	MyProfile[select].BuyID=0;
	MyProfile[select].BuyGraphID=GID_ProfCategoryDef;
	strcpy(MyProfile[select].BuyStr,DEFAULT_STR);

	MyProfile[select].AboutID=0;
	MyProfile[select].AboutGraphID=GID_ProfCategoryDef;
	strcpy(MyProfile[select].AboutStr,DEFAULT_STR);
	
	strcpy(MyProfile[select].ProfileStr,DEFAULT_STR);

	memcpy((void *)&MyProfile_SendWork,(void *)&MyProfile[selectPcNo],sizeof(PROFILE_STATUS));
}


//プロフィールデータをファイル保存
BOOL saveProfileDateFile(void){

	FILE *fp;
	char filename[1024];

	sprintf(filename,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],PROFILE_SAVE_FILENAME); //MLHIDE

	//ファイル开く
	if( (fp = fopen(filename, "wb+" )) != NULL ){                        //MLHIDE

		//ファイル书き込み
		fwrite( (void *)MyProfile, sizeof(PROFILE_STATUS)*MAXCHARACTER, 1, fp );

		fclose( fp );
	}else{
		return FALSE;
	}

	return TRUE;
}

//プロフィールデータをファイル読み込み
BOOL loadProfileDateFile(void){

	FILE *fp;
	char filename[1024];

	sprintf(filename,"%s\\%s",Puk3AccountSystem.accountPath[Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]],PROFILE_SAVE_FILENAME); //MLHIDE

	//ファイル开く
	if( (fp = fopen( filename, "rb+" )) != NULL ){                       //MLHIDE

		//ファイル読み込み
		fread( (void *)MyProfile,sizeof(PROFILE_STATUS)*MAXCHARACTER,1,fp);

		fclose( fp );
	}else{
		return FALSE;
	}

#ifdef PUK3_PROF_START_SEND
	//公开フラグが立っているならば公开にしておく（つまりそのまま）
#else
	//公开フラグはファイル読み込み时は必ずＯＦＦにする
	MyProfile[selectPcNo].open=FALSE;
#endif
	memcpy((void *)&MyProfile_SendWork,(void *)&MyProfile[selectPcNo],sizeof(PROFILE_STATUS));

	return TRUE;

}

//分类ーデータをファイル保存
BOOL saveCategoryDateFile(void){

	FILE *fp;
	char filename[1024];

	sprintf(filename,"bin\\%s",CATEGORY_SAVE_FILENAME);                  //MLHIDE

	//ファイル开く
	if( (fp = fopen(filename, "wb+" )) != NULL ){                        //MLHIDE

		//ファイル书き込み
		fwrite( (void *)&categorySys, sizeof(PROFILE_CATEGORY_SYSTEM), 1, fp );

		fclose( fp );
	}else{
		return FALSE;
	}

	return TRUE;
}

//分类ーデータをファイル読み込み
BOOL loadCategoryDateFile(void){

	FILE *fp;
	char filename[1024];

	sprintf(filename,"bin\\%s",CATEGORY_SAVE_FILENAME);                  //MLHIDE

	//ファイル开く
	if( (fp = fopen( filename, "rb+" )) != NULL ){                       //MLHIDE

		//ファイル読み込み
		fread( (void *)&categorySys, sizeof(PROFILE_CATEGORY_SYSTEM), 1, fp );

		fclose( fp );
	}else{
		return FALSE;
	}

	return TRUE;

}

#endif