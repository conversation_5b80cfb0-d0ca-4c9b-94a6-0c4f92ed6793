﻿/***************************************
				vehicle.cpp
***************************************/

#ifdef PUK3_VEHICLE

extern char ItemNoOpe[MAX_ITEM];		// アイテムの使用フラグ

const int vehicleRidePoint[][5][8][2] = {
	{
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
	},
	{	// 象	２　～　５　人乘り
		{ {-41, 15},{-44,  0},{-19, -9},{  0,-15},{ 28,-12},{ 44,  6},{ 33, 15},{  0, 20}, },
		{ { 19, -9},{ 44,  0},{ 41, 15},{  0, 20},{-33, 15},{-44,  6},{-28,-12},{  0,-15}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ { 29,  9},{  0, 24},{-29,  9},{-45, -9},{-31,-21},{  0,-18},{ 31,-21},{ 45, -9}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
	},
	{	// 鱼	３			人乘り
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ { 34, 15},{  0, 21},{-34, 15},{-47,  2},{-33,-11},{  0,-17},{ 33,-11},{ 47,  2}, },
		{ { 65, 27},{  0, 38},{-64, 27},{-97,  0},{-64,-31},{  0,-40},{ 64,-31},{ 97,  0}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
		{ {  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0},{  0,  0}, },
	},
/***
	{	// 鱼	５			人乘り
		{ {  0-12,  0+5},{  0-18,  0},{  0-12,  0-5},{  0,  0-10},{  0+12,  0-5},{  0+18,  0},{  0+12,  0+5},{  0,  0+10}, },
		{ {  0+12,  0-5},{  0+18,  0},{  0+12,  0+5},{  0,  0+10},{  0-12,  0+5},{  0-18,  0},{  0-12,  0-5},{  0,  0-10}, },
		{ { 34-12, 15+5},{  0-18, 21},{-34-12, 15-5},{-47,  2-10},{-33+12,-11-5},{  0+18,-17},{ 33+12,-11+5},{ 47,  2+10}, },
		{ { 34+12, 15-5},{  0+18, 21},{-34+12, 15+5},{-47,  2+10},{-33-12,-11+5},{  0-18,-17},{ 33-12,-11-5},{ 47,  2-10}, },
		{ { 65,    27},  {  0,    38},{-64,    27},  {-97,  0},   {-64,   -31},  {  0,   -40},{ 64,   -31},  { 97,  0}, },
	},
***/
};
// 座る位置を取得
static void GetVehicleRidePoint( int sprNo, int rideindex, int ang, int *x, int *y )
{
	int index = 0;

	if ( SPR_norimon01a <= sprNo && sprNo <= SPR_norimon01f )		index = 1;
	else if ( SPR_norimon02a <= sprNo && sprNo <= SPR_norimon02f )	index = 2;

	*x += vehicleRidePoint[index][rideindex][ang][0];
	*y += vehicleRidePoint[index][rideindex][ang][1];
}

#define PI 3.1415926535

// 乘り物用构造体
struct VEHICLEEXTRA{
	int rideId[5];			// 乘っているキャラのID
	ACTION *pActRider[5];	// 乘っているキャラのアクション
	int actCnt;
	ACTION *next;
};
enum{
	VEHICLE_STANDBY,
	// 出発
	VEHICLE_DEPARTURE_RIDE,
	VEHICLE_DEPARTURE_MOVE,
	// 到着
	VEHICLE_ARRIVAL_ARRIVAL,
	VEHICLE_ARRIVAL_GETOFF,
	VEHICLE_ARRIVAL_DEPARTURE,
};
// 乘り物リスト
ACTION *pVehicleList = NULL;

// 乘り物リストに追加
void addVehicleList( ACTION *pAct )
{
	ACTION *pActlp;
	struct VEHICLEEXTRA *pYobilp;

	// 乘り物リストに追加
	if ( pVehicleList ){
		for(pActlp=pVehicleList;pActlp;pActlp=pYobilp->next){
			pYobilp = (struct VEHICLEEXTRA *)pActlp->pYobi;
			if ( pYobilp->next == NULL ){
				pYobilp->next = pAct;
				break;
			}
		}
	}else{
		pVehicleList = pAct;
	}
}

// 乘り物リストから削除
void delVehicleList( ACTION *pAct )
{
	ACTION *pActlp;
	struct VEHICLEEXTRA *pYobilp;

	// 先头なら
	if ( pVehicleList == pAct ){
		pYobilp = (struct VEHICLEEXTRA *)pAct->pYobi;
		pVehicleList = pYobilp->next;
	}else{
		for(pActlp=pVehicleList;pActlp;pActlp=pYobilp->next){
			pYobilp = (struct VEHICLEEXTRA *)pActlp->pYobi;
			if ( pYobilp->next == pAct ){
				pYobilp->next = ( (struct VEHICLEEXTRA *)pYobilp->next->pYobi )->next;
				break;
			}
		}
	}
}

// 乘っている乘り物のアクションを取得
ACTION *getRideVehicle( int id )
{
	ACTION *pActlp, *pActNx;
	struct VEHICLEEXTRA *pYobilp;
	int i;

	for(pActlp=pVehicleList;pActlp;pActlp=pActNx){
		pYobilp = (struct VEHICLEEXTRA *)pActlp->pYobi;
		pActNx = pYobilp->next;

		for(i=0;i<5;i++){
			if ( pYobilp->rideId[i] == id ) return pActlp;
		}
	}

	return NULL;
}

// 乘り物リスト初期化
void VehicleInit()
{
	pVehicleList = NULL;
}
// 现存の乘り物を全て削除
void delVehicleAll()
{
	ACTION *pActlp, *pActNx;
	struct VEHICLEEXTRA *pYobilp;
	int i;

	for(pActlp=pVehicleList;pActlp;pActlp=pActNx){
		pYobilp = (struct VEHICLEEXTRA *)pActlp->pYobi;
		pActNx = pYobilp->next;

		for(i=0;i<5;i++){
			if ( !pYobilp->pActRider[i] ) continue;
			DeathAction(pYobilp->pActRider[i]);
		}
		DeathAction(pActlp);
	}

	pVehicleList = NULL;
}

static void DepartureRiderFunc( ACTION *pAct )
{
	ACTION *pActVh = (ACTION *)pAct->pOther;
	struct VEHICLEEXTRA *pYobiVh = (struct VEHICLEEXTRA *)pActVh->pYobi;
	int x, y, xx, yy, xxx, yyy, rx = 0, ry = 0;
	int cdx, cdy;
	ACTION *pActA;

	// 元キャラ取得
	if ( pYobiVh->rideId[pAct->level] == pc.id ){
		pActA = pc.ptAct;
	}else{
		pActA = getCharObjAct( pYobiVh->rideId[pAct->level] );
	}

	if (pActA){
		pAct->anim_chr_no = getRiderCharaGra(pActA->anim_chr_no);
		pAct->anim_ang = pActVh->anim_ang;
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );
	}else{
		pAct->bmpNo = -1;
	}
	// 场所の补正
	GetVehicleRidePoint( pActVh->anim_chr_no, pAct->level, pActVh->anim_ang, &rx, &ry );

	// 画面表示位置
	x = pActVh->x;
	y = pActVh->y;

	// 乘っかる先の座标
	cdx = pActVh->anim_cdx;
	cdy = pActVh->anim_cdy;

	// 乘客の动き
	switch(pAct->actNo){
	case VEHICLE_DEPARTURE_RIDE:
		xx = cdx + rx;
		if (pAct->status < RIDE_JMPCNT_HALF){
			xx = (xx * pAct->status) / RIDE_JMPCNT_HALF;
		}

		yy = pAct->status - RIDE_JMPCNT_HALF;
		yy *= yy;
		yy = RIDE_JMPCNT_HALF*RIDE_JMPCNT_HALF - yy;
		yy = - yy;

		if ( !pAct->status &&  RIDE_SE_GETON_START > 0 ) play_se( RIDE_SE_GETON_START, pAct->x, pAct->y );
		if ( pAct->status > RIDE_JMPCNT_HALF ){
			if ( cdy + ry <= yy ) yy = cdy + ry;
		}

		pAct->anim_no = ANIM_SIT;

		if ( pAct->status > RIDE_JMPCNT_HALF && cdy + ry <= yy ){
			pAct->actNo = VEHICLE_DEPARTURE_MOVE;
			pAct->status = 0;
			// 座りキャラなら
			if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
				pAct->anim_no = ANIM_B_WALK;
			}
			// 普通のキャラなら
			else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
			else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
			else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
			// モンスターなら
			else{
				pAct->anim_no = ANIM_STAND;
			}
			if ( RIDE_SE_GETON_END > 0 ) play_se( RIDE_SE_GETON_END, pAct->x, pAct->y );
		}
		break;
	case VEHICLE_DEPARTURE_MOVE:
		xx = cdx + rx;
		yy = cdy + ry;
		// 座りキャラなら
		if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
			pAct->anim_no = ANIM_B_WALK;
		}
		// 普通のキャラなら
		else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
		else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
		else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
		// モンスターなら
		else{
			pAct->anim_no = ANIM_STAND;
		}
		break;
	}
	if ( pActVh->damage >= 0 ) pAct->anim_no = pActVh->damage;

	if ( pYobiVh->rideId[pAct->level] ){
		if (pYobiVh->pActRider[pAct->level]){
			xxx = 0;
			yyy = 0;
			if ( pAct->actNo == VEHICLE_DEPARTURE_RIDE ){
				if ( pAct->status < RIDE_JMPCNT_HALF ){
					// 元居た位置から移动
					xxx = pAct->x - x;
					yyy = pAct->y - y;
					xxx = ( xxx * (RIDE_JMPCNT_HALF-pAct->status) ) / RIDE_JMPCNT_HALF;
					yyy = ( yyy * (RIDE_JMPCNT_HALF-pAct->status) ) / RIDE_JMPCNT_HALF;
				}
			}
			pAct->dx = x+Blt_adjust(pAct,0) + xx + xxx;
			pAct->dy = y+Blt_adjust(pAct,1) + yy + yyy;
		}
	}

	pAct->status++;
}

ACTION *createDepartureRiderAction( ACTION *pActVh, int index )
{
	ACTION *pAct;
	ACTION *pActC;
	int id;

	id = ( (struct VEHICLEEXTRA *)pActVh->pYobi )->rideId[index];

	if ( id == pc.id ) pActC = pc.ptAct;
	else pActC = getCharObjAct( id );

	if ( pActC == NULL ) return NULL;

	/* アクションリストに登録 */
	pAct = GetAction( PRIO_CHR+1, 0 );
	if( pAct == NULL )
		return NULL;

	// 实行关数
	pAct->func = NULL;
	// グラフィックの番号
	pAct->anim_chr_no = pActC->anim_chr_no;
	// 动作番号
	pAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = pActC->anim_ang;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 表示しない
	pAct->atr = ACT_ATR_HIDE;
	// 初期位置
	pAct->x = pActC->x;
	pAct->y = pActC->y;

	// インデックス记忆
	pAct->level = index;

	// キャラの行动
	pAct->actNo = VEHICLE_DEPARTURE_RIDE;

	// 乘り物のアクション记忆
	pAct->pOther = pActVh;

	return pAct;
}

static void vehicleDepartureFunc( ACTION *pAct )
{
	struct VEHICLEEXTRA *pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;
	int x, y;
	double dx, dy;
	float mx, my;
	int cnt;
	int i, j;
	char flg;
	short anim_angToang[8] = {  45,   0, 315, 270, 225, 180, 135,  90 };
	char sort[5], xchg;

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	x = (int)(mx+.5);
	y = (int)(my+.5);

	pAct->damage = -1;

	// 乘り物の动き
	switch(pAct->actNo){
	case VEHICLE_DEPARTURE_RIDE:
		// キャラのアニメーション
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );
		break;
	case VEHICLE_DEPARTURE_MOVE:
		if ( pYobi->actCnt < 0 ){
			pAct->anim_no = ANIM_B_WALK_START;
			// キャラのアニメーション
			if ( pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP, 0 ) == 1 ){
				pYobi->actCnt = 0;
			}
			pAct->damage = ANIM_STAND;
		}else{
			cnt = pYobi->actCnt;

			pAct->anim_no = ANIM_B_WALK;
			pAct->damage = ANIM_B_WALK;

			// 时间で上升する
			y -= (int)(cnt * 1.4f);

			// キャラの向きを设定
			pAct->anim_ang = pAct->delta - (int)( ( (cnt+8) % 120 ) / 15 );
			if ( pAct->anim_ang < 0 ) pAct->anim_ang += 8;

			// 60フレームで一回転
			dx =  cos( (double)( cnt*3 + anim_angToang[pAct->delta] ) * PI / 180 );
			dy = -sin( (double)( cnt*3 + anim_angToang[pAct->delta] ) * PI / 180 );

			x += (int)( dx * ( (float)cnt * 1.75f) );
			y += (int)( dy * ( (float)cnt * 0.6f) );

			// キャラのアニメーション
			pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );

			// PCが乘ってないなら
			if ( getRideVehicle( pc.id ) == NULL ){
				// ５秒経ったら
				if ( cnt > 60 * 5 ){
					// 画面外にでたら削除
					if ( x < -300 || DEF_APPSIZEX + 300 < x ){
						delVehicleList( pAct );
						for(i=0;i<5;i++){
							if ( !pYobi->pActRider[i] ) continue;
							DeathAction(pYobi->pActRider[i]);
						}
						DeathAction(pAct);
					}
				}
			}
		}
		break;
	}

	pAct->x = x;
	pAct->y = y;
	// キャラを描画
	pAct->status = setMapChar( pAct->bmpNo, pAct->mx, pAct->my,
		 pAct->x+Blt_adjust(pAct,0),
		 pAct->y+Blt_adjust(pAct,1), 1, &pAct->bm );

	// 乘客の管理
	if ( pAct->actNo == VEHICLE_DEPARTURE_RIDE ){
		flg = 0;
		for(i=0;i<5;i++){
			if ( !pYobi->rideId[i] ){
				flg++;
				continue;
			}

			if ( !pYobi->pActRider[i] ){
				pYobi->pActRider[i] = createDepartureRiderAction( pAct, i );
			}
			if ( pYobi->pActRider[i] ){
				if ( pYobi->pActRider[i]->actNo == VEHICLE_DEPARTURE_MOVE ){
					flg++;
				}
			}
		}

		// 念のため一定时间は次へ移らないようにする
		if ( pYobi->actCnt > 10 ){
			if ( flg >= 5 ){
				pAct->actNo = VEHICLE_DEPARTURE_MOVE;
				pYobi->actCnt = -1;
			}
		}
	}

	// 乘客の处理
	j = 0;
	for(i=0;i<5;i++){
		if ( !pYobi->pActRider[i] ) continue;
		DepartureRiderFunc( pYobi->pActRider[i] );
		sort[j] = i;
		j++;
	}
	for(;j<5;j++) sort[j] = -1;

	// 表示顺番の决定
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;
		for(j=i+1;j<5;j++){
			if ( sort[j] < 0 ) break;
			if ( pYobi->pActRider[ sort[i] ]->dy <
				 pYobi->pActRider[ sort[j] ]->dy ){
				xchg = sort[i];
				sort[i] = sort[j];
				sort[j] = xchg;
			}
		}
	}

	// 乘客の表示
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;

		if ( pYobi->pActRider[ sort[i] ]->bmpNo < 0 ) continue;
		// 乘客描画
		setMapCharWith( pAct->status, TRUE, pYobi->pActRider[ sort[i] ]->bmpNo,
			 pYobi->pActRider[ sort[i] ]->dx,
			 pYobi->pActRider[ sort[i] ]->dy,
			 &pYobi->pActRider[ sort[i] ]->bm );
	}

	if ( pYobi->actCnt >= 0 ) pYobi->actCnt++;
}

ACTION *createVehicleDepartureAction( int graNo, int gx, int gy, int dir, int time )
{
	ACTION *pAct;
	float mx, my;

	/* アクションリストに登録 */
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ), ACT_T_CHAREXTRA );
#else
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ) );
#endif
	if( pAct == NULL )
		return NULL;

	// 实行关数
	pAct->func = vehicleDepartureFunc;
	// グラフィックの番号
	pAct->anim_chr_no = graNo;
	// 动作番号
	pAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = dir;
	pAct->delta = dir;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 自分で表示するので隠すフラグ
	pAct->atr = ACT_ATR_HIDE2;
	// 初期位置
	pAct->nextGx = gx;					// マップグリッド座标（移动先）
	pAct->nextGy = gy;
	pAct->bufCount = 0;
	pAct->gx = gx;						// マップグリッド座标（现在地）
	pAct->gy = gy;
	pAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	pAct->my = (float)gy * GRID_SIZE;
	pAct->vx = 0;						// 移动增分
	pAct->vy = 0;

	// 移动时间
	pAct->hp = time;

	// キャラの行动
	pAct->actNo = VEHICLE_DEPARTURE_RIDE;

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	pAct->x = (int)(mx+.5);
	pAct->y = (int)(my+.5);

	// 乘り物リストに追加
	addVehicleList( pAct );

	return pAct;
}


static void ArrivalRiderFunc( ACTION *pAct )
{
	ACTION *pActVh = (ACTION *)pAct->pOther;
	struct VEHICLEEXTRA *pYobiVh = (struct VEHICLEEXTRA *)pActVh->pYobi;
	int x, y, xx, yy, xxx, yyy, rx = 0, ry = 0;
	int cdx, cdy;
	float mx, my;
	ACTION *pActA;

	// 元キャラ取得
	if ( pYobiVh->rideId[pAct->level] == pc.id ){
		pActA = pc.ptAct;
	}else{
		pActA = getCharObjAct( pYobiVh->rideId[pAct->level] );
	}

	if ( pActA ){
		pAct->anim_chr_no = getRiderCharaGra(pActA->anim_chr_no);
		pAct->anim_ang = pActVh->anim_ang;
		if ( pAct->actNo == VEHICLE_ARRIVAL_GETOFF ){
			pAct->anim_ang = pActA->anim_ang;
		}
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );
	}else{
		pAct->bmpNo = -1;
	}
	// 场所の补正
	GetVehicleRidePoint( pActVh->anim_chr_no, pAct->level, pActVh->anim_ang, &rx, &ry );

	// 画面表示位置
	x = pActVh->x;
	y = pActVh->y;

	// 乘っかる先の座标
	cdx = pActVh->anim_cdx;
	cdy = pActVh->anim_cdy;

	// 乘客の动き
	switch(pAct->actNo){
	case VEHICLE_ARRIVAL_ARRIVAL:
		xx = cdx + rx;
		yy = cdy + ry;
		// 座りキャラなら
		if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
			pAct->anim_no = ANIM_B_WALK;
		}
		// 普通のキャラなら
		else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
		else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
		else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
		// モンスターなら
		else{
			pAct->anim_no = ANIM_STAND;
		}
		break;
	case VEHICLE_ARRIVAL_GETOFF:
		// 画面表示位置
		camMapToGamen( pActVh->mx, pActVh->my, &mx, &my );
		x = (int)(mx+.5);
		y = (int)(my+.5);

		if ( pAct->state < RIDE_JMPCNT_HALF ){
			xx = cdx + rx;
			xx = xx - (xx * pAct->state) / RIDE_JMPCNT_HALF;
		}else{
			xx = 0;
		}

		if ( pAct->status == 0 ){
			// 高さ调整
			for(;pAct->status<RIDE_JMPCNT_HALF;pAct->status++){
				yy = pAct->status - RIDE_JMPCNT_HALF;
				yy *= yy;
				yy = RIDE_JMPCNT_HALF*RIDE_JMPCNT_HALF - yy;
				yy = - yy;
				if ( cdy + ry >= yy ) break;
			}
			if ( RIDE_SE_GETOFF_START > 0 ) play_se( RIDE_SE_GETOFF_START, pAct->x, pAct->y );
		}

		yy = pAct->status - RIDE_JMPCNT_HALF;
		yy *= yy;
		yy = RIDE_JMPCNT_HALF*RIDE_JMPCNT_HALF - yy;
		yy = - yy;
		if ( pAct->status > RIDE_JMPCNT_HALF ){
			if ( 0 <= yy ) yy = 0;
		}

		pAct->anim_no = ANIM_SIT;

		if ( pAct->status > RIDE_JMPCNT_HALF && 0 <= yy ){
			pAct->actNo = VEHICLE_ARRIVAL_DEPARTURE;
			pAct->anim_no = ANIM_STAND;

			if ( RIDE_SE_GETOFF_END > 0 ) play_se( RIDE_SE_GETOFF_END, pAct->x, pAct->y );

			// キャラを见えるようにする
			if ( pYobiVh->rideId[pAct->level] == pc.id ){
				delPcInvisible();
			}else{
				delCharInvisible( pActA );
			}
		}
		break;
	case VEHICLE_ARRIVAL_DEPARTURE:
		break;
	}

	if ( pAct->actNo < VEHICLE_ARRIVAL_DEPARTURE ){
		if ( pYobiVh->rideId[pAct->level] ){
			if (pYobiVh->pActRider[pAct->level]){
				xxx = 0;
				yyy = 0;
				if ( pActA ){
					if ( pAct->actNo == VEHICLE_ARRIVAL_GETOFF ){
						// 元居た位置から移动
						xxx = pActA->x - x;
						yyy = pActA->y - y;
						xxx = ( xxx * pAct->state ) / RIDE_JMPCNT_HALF;
						yyy = ( yyy * pAct->state ) / RIDE_JMPCNT_HALF;
					}
				}

				// 场所の补正
				GetVehicleRidePoint( pActVh->anim_chr_no, pAct->level, pActVh->anim_ang, &rx, &ry );

				// 乘客描画
				setMapCharWith( pActVh->status, TRUE, pAct->bmpNo,
					 x+Blt_adjust(pAct,0) + xx + xxx,
					 y+Blt_adjust(pAct,1) + yy + yyy,
					 &pAct->bm );
			}
		}

		if ( pAct->actNo == VEHICLE_ARRIVAL_GETOFF ){
			pAct->status++;
			pAct->state++;
		}
	}
}

ACTION *createArrivalRiderAction( ACTION *pActVh, int index )
{
	ACTION *pAct;
	ACTION *pActC;
	int id;

	id = ( (struct VEHICLEEXTRA *)pActVh->pYobi )->rideId[index];

	if ( id == pc.id ) pActC = pc.ptAct;
	else pActC = getCharObjAct( id );

	if ( pActC == NULL ) return NULL;

	/* アクションリストに登録 */
	pAct = GetAction( PRIO_CHR+1, 0 );
	if( pAct == NULL )
		return NULL;

	// 实行关数
	pAct->func = NULL;
	// グラフィックの番号
	pAct->anim_chr_no = pActC->anim_chr_no;
	// 动作番号
	pAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = pActC->anim_ang;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 表示しない
	pAct->atr = ACT_ATR_HIDE;
	// 初期位置
	pAct->x = pActC->x;
	pAct->y = pActC->y;

	// インデックス记忆
	pAct->level = index;

	// キャラの行动
	pAct->actNo = VEHICLE_ARRIVAL_ARRIVAL;

	// 乘り物のアクション记忆
	pAct->pOther = pActVh;

	pAct->status = 0;
	pAct->state = 0;

	return pAct;
}

static void vehicleArribalFunc( ACTION *pAct )
{
	struct VEHICLEEXTRA *pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;
	int x, y, dx, dy;
	float mx, my;
	int i, j;
	double angTodir[8]={ PI*3/4, PI*2/4, PI*1/4, PI*0/4, PI*7/4, PI*6/4, PI*5/4, PI*4/4 };
	char sort[5], xchg;

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	x = (int)(mx+.5);
	y = (int)(my+.5);

	// 乘り物の动き
	switch(pAct->actNo){
	case VEHICLE_ARRIVAL_ARRIVAL:
		if ( pYobi->actCnt > 50 ){
			pAct->actNo = VEHICLE_ARRIVAL_GETOFF;
			// 乘客の管理
			for(i=0;i<5;i++){
				if ( pYobi->pActRider[i] ){
					pYobi->pActRider[i]->actNo = VEHICLE_ARRIVAL_GETOFF;
				}
			}
		}
	case VEHICLE_ARRIVAL_GETOFF:
	case VEHICLE_ARRIVAL_DEPARTURE:
		dx = pYobi->actCnt * 10 - 500;
		dy = pYobi->actCnt * 10 - 500;
		dx = (int)( dx * cos( angTodir[pAct->anim_ang] ) );
		dy = (int)( dy * -sin( angTodir[pAct->anim_ang] ) );
		x += dx;
		y += dy;

		y -= (pYobi->actCnt-50)*(pYobi->actCnt-50) / 30;
		break;
	}
	if ( pAct->actNo == VEHICLE_ARRIVAL_GETOFF ){
		// 乘客の管理
		for(i=0;i<5;i++){
			if ( !pYobi->pActRider[i] ) continue;
			if ( pYobi->pActRider[i]->actNo != VEHICLE_ARRIVAL_DEPARTURE ) break;
		}
		if ( i >= 5 ){
			pAct->actNo = VEHICLE_ARRIVAL_DEPARTURE;
		}
	}
	if ( pAct->actNo == VEHICLE_ARRIVAL_DEPARTURE ){
		// 画面外にでたら削除
		if ( x < -300 || DEF_APPSIZEX + 300 < x || y < -300 || DEF_APPSIZEY + 300 < y ){
			delVehicleList( pAct );
			for(i=0;i<5;i++){
				if ( pYobi->pActRider[i] ) DeathAction(pYobi->pActRider[i]);
			}
			DeathAction(pAct);
		}
	}

	// キャラのアニメーション
	pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );

	pAct->x = x;
	pAct->y = y;
	// キャラを描画
	pAct->status = setMapChar( pAct->bmpNo, pAct->mx, pAct->my,
		 pAct->x+Blt_adjust(pAct,0),
		 pAct->y+Blt_adjust(pAct,1), 1, &pAct->bm );

	// 乘客の管理
	for(i=0;i<5;i++){
		if ( !pYobi->rideId[i] ) continue;

		if ( !pYobi->pActRider[i] ){
			pYobi->pActRider[i] = createArrivalRiderAction( pAct, i );
		}
	}

	// 乘客の处理
	j = 0;
	for(i=0;i<5;i++){
		if ( !pYobi->pActRider[i] ) continue;
		ArrivalRiderFunc( pYobi->pActRider[i] );
		sort[j] = i;
		j++;
	}
	for(;j<5;j++) sort[j] = -1;

	// 表示顺番の决定
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;
		for(j=i+1;j<5;j++){
			if ( sort[j] < 0 ) break;
			if ( pYobi->pActRider[ sort[i] ]->dy <
				 pYobi->pActRider[ sort[j] ]->dy ){
				xchg = sort[i];
				sort[i] = sort[j];
				sort[j] = xchg;
			}
		}
	}

	// 乘客の表示
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;

		if ( pYobi->pActRider[ sort[i] ]->bmpNo < 0 ) continue;
		// 乘客描画
		setMapCharWith( pAct->status, TRUE,
			 pYobi->pActRider[ sort[i] ]->bmpNo,
			 pYobi->pActRider[ sort[i] ]->dx,
			 pYobi->pActRider[ sort[i] ]->dy,
			 &pYobi->pActRider[ sort[i] ]->bm );
	}

	pYobi->actCnt++;
}

ACTION *createVehicleArrivalAction( int graNo, int gx, int gy, int dir )
{
	ACTION *pAct;
	float mx, my;

	/* アクションリストに登録 */
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ), ACT_T_CHAREXTRA );
#else
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ) );
#endif
	if( pAct == NULL )
		return NULL;

	// 实行关数
	pAct->func = vehicleArribalFunc;
	// グラフィックの番号
	pAct->anim_chr_no = graNo;
	// 动作番号
	pAct->anim_no = ANIM_B_WALK;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = dir;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 自分で表示するので隠すフラグ
	pAct->atr = ACT_ATR_HIDE2;
	// 初期位置
	pAct->nextGx = gx;					// マップグリッド座标（移动先）
	pAct->nextGy = gy;
	pAct->bufCount = 0;
	pAct->gx = gx;						// マップグリッド座标（现在地）
	pAct->gy = gy;
	pAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	pAct->my = (float)gy * GRID_SIZE;
	pAct->vx = 0;						// 移动增分
	pAct->vy = 0;

	// キャラの行动
	pAct->actNo = VEHICLE_ARRIVAL_ARRIVAL;

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	pAct->x = (int)(mx+.5);
	pAct->y = (int)(my+.5);

	// 乘り物リストに追加
	addVehicleList( pAct );

	return pAct;
}

static void vehicleTestFunc( ACTION *pAct )
{
	struct VEHICLEEXTRA *pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;
	int x, y;
	float mx, my;
	int i, j;
	char flg;
	short anim_angToang[8] = {  45,   0, 315, 270, 225, 180, 135,  90 };
	char sort[5], xchg;

	// キャラのアニメーション
	pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	x = (int)(mx+.5);
	y = (int)(my+.5);

	pAct->damage = -1;

	// 乘り物の动き

	pAct->x = x;
	pAct->y = y;
	// キャラを描画
	pAct->status = setMapChar( pAct->bmpNo, pAct->mx, pAct->my,
		 pAct->x+Blt_adjust(pAct,0),
		 pAct->y+Blt_adjust(pAct,1), 1, &pAct->bm );

	// 乘客の管理
	flg = 0;
	for(i=0;i<5;i++){
		if ( !pYobi->rideId[i] ){
			flg++;
			continue;
		}

		if ( !pYobi->pActRider[i] ){
			pYobi->pActRider[i] = createDepartureRiderAction( pAct, i );
		}
		if ( pYobi->pActRider[i] ){
			if ( pYobi->pActRider[i]->actNo == VEHICLE_DEPARTURE_MOVE ){
				flg++;
			}
		}
	}

	// 乘客の处理
	j = 0;
	for(i=0;i<5;i++){
		if ( !pYobi->pActRider[i] ) continue;
		DepartureRiderFunc( pYobi->pActRider[i] );
		sort[j] = i;
		j++;
	}
	for(;j<5;j++) sort[j] = -1;

	// 表示顺番の决定
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;
		for(j=i+1;j<5;j++){
			if ( sort[j] < 0 ) break;
			if ( pYobi->pActRider[ sort[i] ]->dy <
				 pYobi->pActRider[ sort[j] ]->dy ){
				xchg = sort[i];
				sort[i] = sort[j];
				sort[j] = xchg;
			}
		}
	}

	// 乘客の表示
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;

		if ( pYobi->pActRider[ sort[i] ]->bmpNo < 0 ) continue;
		// 乘客描画
		setMapCharWith( pAct->status, TRUE,
			 pYobi->pActRider[ sort[i] ]->bmpNo,
			 pYobi->pActRider[ sort[i] ]->dx,
			 pYobi->pActRider[ sort[i] ]->dy,
			 &pYobi->pActRider[ sort[i] ]->bm );
	}

	if ( pYobi->actCnt >= 0 ) pYobi->actCnt++;
}

ACTION *createTestVehicleAction( int graNo, int gx, int gy, int dir )
{
	ACTION *pAct;
	float mx, my;

	/* アクションリストに登録 */
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ), ACT_T_CHAREXTRA );
#else
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ) );
#endif
	if( pAct == NULL )
		return NULL;

	// 实行关数
	pAct->func = vehicleTestFunc;
	// グラフィックの番号
	pAct->anim_chr_no = graNo;
	// 动作番号
	pAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = dir;
	pAct->delta = dir;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 自分で表示するので隠すフラグ
	pAct->atr = ACT_ATR_HIDE2;
	// 初期位置
	pAct->nextGx = gx;					// マップグリッド座标（移动先）
	pAct->nextGy = gy;
	pAct->bufCount = 0;
	pAct->gx = gx;						// マップグリッド座标（现在地）
	pAct->gy = gy;
	pAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	pAct->my = (float)gy * GRID_SIZE;
	pAct->vx = 0;						// 移动增分
	pAct->vy = 0;

	// キャラの行动
	pAct->actNo = VEHICLE_DEPARTURE_MOVE;

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	pAct->x = (int)(mx+.5);
	pAct->y = (int)(my+.5);

	// 乘り物リストに追加
	addVehicleList( pAct );

	return pAct;
}


void setVehicleRider( ACTION *pAct,
	 int riderId1, int riderId2, int riderId3, int riderId4, int riderId5 )
{
	struct VEHICLEEXTRA *pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;
	int *riderId[5] = { &riderId1, &riderId2, &riderId3, &riderId4, &riderId5 };
	int i;

	for(i=0;i<5;i++){
		if ( *riderId[i] > 0 ){
			pYobi->rideId[i] = *riderId[i];
		}
	}
}


// 乘り物关系の动作变更
BOOL changeVehicleAct( int id, int x, int y, int dir, int action, char *data )
{
	ACTION *pAct;
	CHAREXTRA *ext;

	int opt[10];
	int i;

	ACTION *pActVhC, *pActVhA;

	// 乘り物关系の动作でないなら返回
	if ( action < 81 || 82 < action ) return FALSE;

	// キャラのアクション取得
	if ( id == pc.id ){
		pAct = pc.ptAct;
	}else{
		pAct = getCharObjAct( id );
	}
	if ( pAct == NULL ) return TRUE;
	ext = (CHAREXTRA *)pAct->pYobi;

	switch(action){
	// 乘り物移动
	case 81:
		// オプション取得
		for(i=0;i<7;i++) opt[i] = getIntegerToken( data, DELIMITER, 6 + i );

		// 乘り物のベースキャラのアクション取得
		pActVhC = getCharObjAct( opt[0] );
		if (pActVhC){
			ACTION *pAct;

			// 乘り物作成
			pActVhA = createVehicleDepartureAction( pActVhC->anim_chr_no, pActVhC->gx, pActVhC->gy, pActVhC->anim_ang, opt[1] );
			setVehicleRider( pActVhA, opt[2], opt[3], opt[4], opt[5], opt[6] );

			// 见えないフラグ设定
			for(i=0;i<5;i++){
				if ( opt[2+i] == pc.id ){
					int j;

					// ウィンドウを关闭
					CloseWindowSelectType(3);
					serverRequestWinWindowType = -1;

					// ＰＣを见えなくする
					setPcInvisible();
					// リ??バースの解除
					if ( skillrebirth ){
						skillrebirth = 0;
						ItemNoOpe[7]--;
					}
					// 装备品栏の操作禁止
					for( j = 0; j < MAX_EQUIP_ITEM; j++ ) ItemNoOpe[j]++;
				}else{
					pAct = getCharObjAct( opt[2+i] );
					setCharInvisible( pAct );
				}
			}

			// ベースになったキャラを消す
			pActVhC->anim_chr_no = 0;
		}
		break;
	// 乘り物到着
	case 82:
		// オプション取得
		for(i=0;i<7;i++) opt[i] = getIntegerToken( data, DELIMITER, 6 + i );

		// 乘り物作成
	#ifdef _CG2_NEWGRAPHIC
		pActVhA = createVehicleArrivalAction( getNewGraphicNo(opt[0]), x, y, opt[1] );
	#else
		pActVhA = createVehicleArrivalAction( opt[0], x, y, opt[1] );
	#endif
		setVehicleRider( pActVhA, opt[2], opt[3], opt[4], opt[5], opt[6] );

		{
			ACTION *pAct;
			// 见えないフラグ设定
			for(i=0;i<5;i++){
				if ( opt[2+i] == pc.id ){
					setPcInvisible();
				}else{
					pAct = getCharObjAct( opt[2+i] );
					setCharInvisible( pAct );
				}
			}
		}
		break;
	}

	return TRUE;
}





//--------------------------------------
// 乘り物移动中の处理
//--------------------------------------

static int vh_anim_chr_no, vh_anim_ang, vh_time;
static int rideId[5], rideGraNo[5];


// 现在乘り物移动中のプロセスかを取得
BOOL nowVehicleProc()
{
	if ( GAME_PROC_VEHICLE_MOVING_INIT <= SubProcNo &&
		 SubProcNo <= GAME_PROC_VEHICLE_END ) return TRUE;
	return FALSE;
}

static void vehicleMovingFunc( ACTION *pAct )
{
	struct VEHICLEEXTRA *pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;
	int x, y, dx, dy, rx = 0, ry = 0;
	int i, j;
	float f, f2;
	double angTodir[8]={ PI*3/4, PI*2/4, PI*1/4, PI*0/4, PI*7/4, PI*6/4, PI*5/4, PI*4/4 };
	char sort[5], xchg;
	int drawx[5], drawy[5];

	x = 320;
	y = 240;

	// 乘り物の动き
	f = 100 * (float)pYobi->actCnt / vh_time;
	f2 = ( f * 240 * 2 ) / 100 - 240;
	dx = (int)( f2 * cos( angTodir[pAct->anim_ang] ) );
	dy = (int)( f2 * -sin( angTodir[pAct->anim_ang] ) );
	x += dx;
	y += dy;

	y -= (int)( (f - 40)*(f - 40) / 30 );
	y += 50;

	// キャラのアニメーション
	pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );

	pAct->x = x;
	pAct->y = y;

	// 乘客の管理
	j = 0;
	for(i=0;i<5;i++){
		if ( !pYobi->pActRider[i] ) continue;

		rx = ry = 0;

		// キャラのアニメーション
		pattern( pYobi->pActRider[i], ANM_NOMAL_SPD, ANM_LOOP, 0 );

		// 场所の补正
		GetVehicleRidePoint( pAct->anim_chr_no,
			 pYobi->pActRider[i]->level, pYobi->pActRider[i]->anim_ang, &rx, &ry );

		// 表示座标の保存
		drawx[i] = pAct->x+pAct->anim_cdx + Blt_adjust(pYobi->pActRider[i],0) + rx;
		drawy[i] = pAct->y+pAct->anim_cdy + Blt_adjust(pYobi->pActRider[i],1) + ry;

		sort[j] = i;
		j++;
	}
	for(;j<5;j++) sort[j] = -1;

	// 表示顺番の决定
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;
		for(j=i+1;j<5;j++){
			if ( sort[j] < 0 ) break;
			if ( pYobi->pActRider[ sort[i] ]->dy <
				 pYobi->pActRider[ sort[j] ]->dy ){
				xchg = sort[i];
				sort[i] = sort[j];
				sort[j] = xchg;
			}
		}
	}

	// 乘客の表示
	for(i=0;i<5;i++){
		if ( sort[i] < 0 ) break;

		// 乘客描画
		StockDispBuffer2( drawx[ sort[i] ], drawy[ sort[i] ],
			 DISP_PRIO_CHAR, pYobi->pActRider[ sort[i] ]->bmpNo,
			 0, &pYobi->pActRider[ sort[i] ]->bm );
	}

	// キャラを描画
	StockDispBuffer2(
		 pAct->x+Blt_adjust(pAct,0), pAct->y+Blt_adjust(pAct,1),
		 DISP_PRIO_CHAR, pAct->bmpNo, 0, &pAct->bm );

	pYobi->actCnt++;
}

ACTION *createVehicleMovingAction()
{
	ACTION *pAct;
	struct VEHICLEEXTRA *pYobi;
	int i;

	/* アクションリストに登録 */
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ), ACT_T_CHAREXTRA );
#else
	pAct = GetAction( PRIO_CHR, sizeof( VEHICLEEXTRA ) );
#endif
	if( pAct == NULL )
		return NULL;

	pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;

	// 实行关数
	pAct->func = vehicleMovingFunc;
	// グラフィックの番号
	pAct->anim_chr_no = vh_anim_chr_no;
	// 动作番号
	pAct->anim_no = ANIM_B_WALK;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = vh_anim_ang;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 自分で表示するので隠すフラグ
	pAct->atr = ACT_ATR_HIDE2;

	pAct->x = -100;
	pAct->y = -100;

	// 乘り物リストに追加
	addVehicleList( pAct );

	// 乘客のアクション作成
	for(i=0;i<5;i++){
		pYobi->rideId[i] = 0;
		pYobi->pActRider[i] = NULL;

		if ( rideId[i] <= 0 ) continue;

		pYobi->rideId[i] = rideId[i];

		pYobi->pActRider[i] = GetAction( PRIO_CHR, 0 );
		if ( !pYobi->pActRider[i] ) continue;

		pYobi->pActRider[i]->anim_chr_no = rideGraNo[i];
		pYobi->pActRider[i]->anim_ang = vh_anim_ang;
		pYobi->pActRider[i]->anim_no = ANIM_B_WALK;

		// インデックス记忆
		pYobi->pActRider[i]->level = i;
	}

	return pAct;
}

// 乘り物移动中プロセスの初期化关数
void vehicleProcInit_in_warpEffectProc()
{
	ACTION *pAct;
	struct VEHICLEEXTRA *pYobi;
	int i;

	pAct = getRideVehicle( pc.id );
	if (!pAct) return;

	pYobi = (struct VEHICLEEXTRA *)pAct->pYobi;

	vh_anim_chr_no = pAct->anim_chr_no;
	vh_anim_ang = pAct->delta;

	vh_time = pAct->hp * 60;

	for(i=0;i<5;i++){
		rideId[i] = 0;
		rideGraNo[i] = 0;

		if ( pYobi->rideId[i] <= 0 ) continue;

		rideId[i] = pYobi->rideId[i];

		if (pYobi->pActRider[i]){
			rideGraNo[i] = pYobi->pActRider[i]->anim_chr_no;
		}
	}
}

// 乘り物移动中プロセスの初期化关数
int vehicleProcInit()
{
	createVehicleMovingAction();
	fade_out_bgm();

	return BGM_PUK3_KAME;
}

extern short mapEffectCloudLevel;
extern short mapEffectCloud2Level;
extern float mapEffectCloudSpeed;
extern short mapEffectCloudAngle;

// 乘り物移动中プロセス
void vehicleProc()
{
	int angTodir[8] = { 315, 270, 225, 180, 135,  90,  45,   0 };
	int i, j;
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.BltVer = BLTVER_PUK2;
	bm.u = 0   - 320;
	bm.v = 820 - 480;
	bm.w = 64;
	bm.h = 48;

	// 云エフェクトのみ流す
	mapEffectCloudLevel = 2;
	mapEffectCloud2Level = 2;
	mapEffectCloudSpeed = 6;
	mapEffectCloudAngle = angTodir[vh_anim_ang];

	mapEffectCloud();

	// 背景を青タイルで埋める
	for(j=0;j<10;j++){
		for(i=0;i<10;i++){
			StockDispBuffer_PUK2( i*64, j*48,  0, 14648, 0, 1, &bm );
		}
	}
}

// 乘り物移动中終了プロセス
void vehicleProcEnd()
{
}

#endif