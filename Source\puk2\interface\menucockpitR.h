﻿//右コクピットウインドウ

#ifndef _MENUCOCKPITR_H_
#define _MENUCOCKPITR_H_

BOOL MenuWindowRightLarge( int mouse );
BOOL MenuWindowRightDrawLarge( int mouse );
BOOL MenuWindowRightSmall( int mouse );
BOOL MenuWindowRightDrawSmall( int mouse );

BOOL MenuSwitchCockpitRightLargeToSmall( int no, unsigned int flag );
BOOL MenuSwitchCockpitRightSmallToLarge( int no, unsigned int flag );
BOOL MenuSwitchCockpitRightLarge( int no, unsigned int flag );

//サイズ管理フラグ
extern int MenuWindowRightSizeFlag;

GRAPHIC_SWITCH MenuWindowCockPitRightLargeGraph[]={
	{GID_CockpitRightLargeBase,0,0,0,0,0xFFFFFFFF},	//右コクピット大

	{GID_CockpitDuelOff,0,0,0,0,0xFFFFFFFF},		//デュエル
	{GID_CockpitWatchOff,0,0,0,0,0xFFFFFFFF},		//観战
	{GID_CockpitGroupOff,0,0,0,0,0xFFFFFFFF},		//グループ
	{GID_CockpitNameCardOff,0,0,0,0,0xFFFFFFFF},	//名刺
	{GID_CockpitTradeOff,0,0,0,0,0xFFFFFFFF},		//トレード
	{GID_CockpitGuildOff,0,0,0,0,0xFFFFFFFF},		//家族
	{GID_CockpitActionOff,0,0,0,0,0xFFFFFFFF},		//アクション
	{GID_CockpitMapOff,0,0,0,0,0xFFFFFFFF},			//マップ

};

GRAPHIC_SWITCH MenuWindowCockPitRightSmallGraph[]={
	{GID_CockpitRightSmallBase,0,0,0,0,0xFFFFFFFF},	//右コクピット小

};

//ボタンスイッチ
BUTTON_SWITCH MenuWindowCockPitButtonRight[]={
	{0},										//左大＞中
	{0},										//左中＞小
	{0},										//左小＞大
	{0},										//
};


//右コクピット大スイッチ
static SWITCH_DATA MenuCockpitRightLargeSwitch[] = {

//グラフィック	
{ SWITCH_GRAPHIC  , 14,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 1], MenuSwitchCockpitRightLarge },	//デュエル
{ SWITCH_GRAPHIC  , 35,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 2], MenuSwitchCockpitRightLarge },	//観战
{ SWITCH_GRAPHIC  , 56,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 3], MenuSwitchCockpitRightLarge },	//グループ
{ SWITCH_GRAPHIC  , 77,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 4], MenuSwitchCockpitRightLarge },	//名刺
{ SWITCH_GRAPHIC  , 98,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 5], MenuSwitchCockpitRightLarge },	//トレード
{ SWITCH_GRAPHIC  ,119,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 6], MenuSwitchCockpitRightLarge },	//家族
{ SWITCH_GRAPHIC  ,140,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 7], MenuSwitchCockpitRightLarge },	//アクション
{ SWITCH_GRAPHIC  ,161,  3,  20, 20, TRUE, &MenuWindowCockPitRightLargeGraph[ 8], MenuSwitchCockpitRightLarge },	//マップ

{ SWITCH_GRAPHIC  ,  0,  0,   0,  0, TRUE, &MenuWindowCockPitRightLargeGraph[ 0], MenuSwitchNone },	//右コクピット大

//ボタン	
{ SWITCH_BUTTON   ,  0,  0, 14, 33, TRUE, &MenuWindowCockPitButtonRight[0], MenuSwitchCockpitRightLargeToSmall },

};

enum{

	EnumGraphCockpitDuel,
	EnumGraphCockpitWatch,
	EnumGraphCockpitGroup,
	EnumGraphCockpitNameCard,
	EnumGraphCockpitTrade,
	EnumGraphCockpitGuild,
	EnumGraphCockpitAction,
	EnumGraphCockpitMap,

	EnumGraphCockpitRightLarge,
		
	EnumBtToSmallCockpitRightLarge,

	EnumCockpitRightLargeEND
};

//右コクピット小スイッチ
static SWITCH_DATA MenuCockpitRightSmallSwitch[] = {

//グラフィック	
{ SWITCH_GRAPHIC  ,  0,  0,   0,  0, TRUE, &MenuWindowCockPitRightSmallGraph[ 0], MenuSwitchNone },	//右コクピット小

//ボタン	
{ SWITCH_BUTTON   ,  0,  0, 14, 33, TRUE, &MenuWindowCockPitButtonRight[0], MenuSwitchCockpitRightSmallToLarge },

};

enum{
	EnumGraphCockpitRightSmall,
	EnumBtToLargeCockpitRightSmall,

	EnumCockpitRightSmallEND
};


//ウインドウ
// 右大
const WINDOW_DATA WindowDataMenuRightLarge = {
 0,																		
     5,456, 0,191, 28, 0x80000080,  EnumCockpitRightLargeEND, MenuCockpitRightLargeSwitch , MenuWindowRightLarge,MenuWindowRightDrawLarge,MenuWindowDel
};

// 右小
const WINDOW_DATA WindowDataMenuRightSmall = {
 0,																		
     5,626, 0, 14, 33, 0x80000080, EnumCockpitRightSmallEND, MenuCockpitRightSmallSwitch , MenuWindowRightSmall,MenuWindowRightDrawSmall,MenuWindowDel
};

#endif