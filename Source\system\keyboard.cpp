﻿/************************/
/*	keyboard.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/pc.h"
#include "../systeminc/menu.h"
#include "../systeminc/field.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/mouse.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/process.h"
#include "../systeminc/main.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/t_music.h"

// キーボードの状态记忆配列
char VK[ VK_MAX ];

// ショートカットキーの定义
SHORT_CUT_KEY_INFO shortCutKey[] =
{//   补助キー		キー
	{ 0,			VK_ESCAPE,		shortCutFuncSystemMenu	},	// ESC
#if 0
	{ 0,			VK_HOME,		shortCutFncDebugCommonWindow	},	// HOME
#else
	{ 0,			VK_HOME,		NULL	},	// HOME
#endif

#if 0
	{ 0,			VK_F1,			NULL	},	// F1
	{ 0,			VK_F2,			NULL	},	// F2
	{ 0,			VK_F3,			NULL	},	// F3
	{ 0,			VK_F4,			NULL	},	// F4
	{ 0,			VK_F5,			NULL	},	// F5
	{ 0,			VK_F6,			NULL	},	// F6
	{ 0,			VK_F7,			NULL	},	// F7
	{ 0,			VK_F8,			NULL	},	// F8
#endif
	{ 0,			VK_F9,			NULL	},	// F9
	{ 0,			VK_F10,			NULL	},	// F10
	{ 0,			VK_F11,			shortCutFncShowName		},	// F11
	{ 0,			VK_F12,			shortCutFncScreenShot	},	// F12
	{ VK_CONTROL,	VK_A,			shortCutFncMonsterList	},	// CTRL+A
	{ VK_CONTROL,	VK_B,			NULL	},	// CTRL+B
	{ VK_CONTROL,	VK_C,			NULL	},	// CTRL+C
	{ VK_CONTROL,	VK_D,			shortCutFncAlbumList	},	// CTRL+D
	{ VK_CONTROL,	VK_E,			shortCutFncSkill	},	// CTRL+E
	{ VK_CONTROL,	VK_F,			shortCutFncAction	},	// CTRL+F
#ifdef PUK2
	{ VK_CONTROL,	VK_G,			shortCutFncGuild	},	// CTRL+G
	{ VK_CONTROL,	VK_H,			shortCutFncGuildFlagChange	},	// CTRL+H
#else
	{ VK_CONTROL,	VK_G,			NULL	},	// CTRL+G
	{ VK_CONTROL,	VK_H,			NULL	},	// CTRL+H
#endif
	{ VK_CONTROL,	VK_I,			NULL	},	// CTRL+I
	{ VK_CONTROL,	VK_J,			NULL	},	// CTRL+J
	{ VK_CONTROL,	VK_K,			NULL	},	// CTRL+K
	{ VK_CONTROL,	VK_L,			shortCutFncMeishiFlagChange	},	// CTRL+L
	{ VK_CONTROL,	VK_M,			NULL	},	// CTRL+M
	{ VK_CONTROL,	VK_N,			NULL	},	// CTRL+N
	{ VK_CONTROL,	VK_O,			shortCutFncChangeFontSize	},	// CTRL+O
	{ VK_CONTROL,	VK_P,			shortCutFncDuelFlagChange	},	// CTRL+P
	{ VK_CONTROL,	VK_Q,			shortCutFncAutoMap	},	// CTRL+Q
	{ VK_CONTROL,	VK_R,			shortCutFncItem	},	// CTRL+R
	{ VK_CONTROL,	VK_S,			shortCutFncAddress	},	// CTRL+S
	{ VK_CONTROL,	VK_T,			NULL	},	// CTRL+T
	{ VK_CONTROL,	VK_U,			NULL	},	// CTRL+U
	{ VK_CONTROL,	VK_V,			NULL	},	// CTRL+V	// 贴り付け机能が动くので使わない
	{ VK_CONTROL,	VK_W,			shortCutFncPlayerStatusMenu	},	// CTRL+W
	{ VK_CONTROL,	VK_X,			NULL	},	// CTRL+X	// 使っちゃだめよ
#if _DEBUG
	{ VK_CONTROL,	VK_Y,			shortCutFncDebug	},	// CTRL+Y
#else
	{ VK_CONTROL,	VK_Y,			NULL	},	// CTRL+Y
#endif
	{ VK_CONTROL,	VK_Z,			NULL	},	// CTRL+Z	// 使っちゃだめよ
	{ VK_CONTROL,	VK_1,			shortCutFncActionSit	},	// CTRL+1(フルキー)
	{ VK_CONTROL,	VK_2,			shortCutFncActionHand	},	// CTRL+2(フルキー)
	{ VK_CONTROL,	VK_3,			shortCutFncActionNod	},	// CTRL+3(フルキー)
	{ VK_CONTROL,	VK_4,			shortCutFncActionHappy	},	// CTRL+4(フルキー)
	{ VK_CONTROL,	VK_5,			shortCutFncActionAngry	},	// CTRL+5(フルキー)
	{ VK_CONTROL,	VK_6,			shortCutFncActionSad	},	// CTRL+6(フルキー)
	{ VK_CONTROL,	VK_7,			shortCutFncActionThrow	},	// CTRL+7(フルキー)
	{ VK_CONTROL,	VK_8,			shortCutFncActionStand	},	// CTRL+8(フルキー)
	{ VK_CONTROL,	VK_9,			shortCutFncActionWalk	},	// CTRL+9(フルキー)
	{ VK_CONTROL,	VK_0,			shortCutFncActionDead	},	// CTRL+0(フルキー)
	{ VK_CONTROL,	VK_USER_SUB,	shortCutFncActionAttack	},	// CTRL+'-'(フルキーの - )
	{ VK_CONTROL,	VK_USER_TILT,	shortCutFncActionGuard	},	// CTRL+'^'(フルキーの ^ )
	{ VK_CONTROL,	VK_USER_YEN,	shortCutFncActionJanken	},	// CTRL+'\'(フルキーの \ )
	{ VK_CONTROL,	VK_USER_PERIOD,	shortCutFncGroupFlagChange	},	// CTRL+'.'
	{ VK_CONTROL,	VK_USER_SEMICOLON,	shortCutFncChatFlagChange	},	// CTRL+';'
	{ VK_CONTROL,	VK_USER_COMMA,	shortCutFncTradeFlagChange	},	// CTRL+','
	{ VK_CONTROL,	VK_USER_COLON,	shortCutFncSpectate	},	// CTRL+':'
	{ VK_CONTROL,	VK_USER_SLASH,	shortCutFncParty	},	// CTRL+'/'
	{ VK_CONTROL,	VK_USER_OPEN_BRACKET,	shortCutFncCard	},	// CTRL+'['
	{ VK_CONTROL,	VK_USER_CLOSE_BRACKET,	shortCutFncTrade	},	// CTRL+']'
	{ VK_CONTROL,	VK_USER_AT_MARK,	shortCutFncDuel	},	// CTRL+'@'
	{ VK_CONTROL,	VK_UP,			shortCutFncChatLineUp	},	// CTRL+'↑'
	{ VK_CONTROL,	VK_DOWN,		shortCutFncChatLineDown	},	// CTRL+'↓'
	{ VK_CONTROL,	VK_RIGHT,		shortCutFncChatVoiceUp	},	// CTRL+'→'
	{ VK_CONTROL,	VK_LEFT,		shortCutFncChatVoiceDown},	// CTRL+'←'
#if 0
	{ VK_CONTROL,	VK_F1,			NULL	},	// CTRL+F1
	{ VK_CONTROL,	VK_F2,			NULL	},	// CTRL+F2
	{ VK_CONTROL,	VK_F3,			NULL	},	// CTRL+F3
	{ VK_CONTROL,	VK_F4,			NULL	},	// CTRL+F4
	{ VK_CONTROL,	VK_F5,			NULL	},	// CTRL+F5
	{ VK_CONTROL,	VK_F6,			NULL	},	// CTRL+F6
	{ VK_CONTROL,	VK_F7,			NULL	},	// CTRL+F7
	{ VK_CONTROL,	VK_F8,			NULL	},	// CTRL+F8
#endif
	{ VK_CONTROL,	VK_F9,			NULL	},	// CTRL+F9
	{ VK_CONTROL,	VK_F10,			NULL	},	// CTRL+F10
	{ VK_CONTROL,	VK_F11,			NULL	},	// CTRL+F11
	{ VK_CONTROL,	VK_F12,			NULL	},	// CTRL+F12
#if 0
	{ VK_SHIFT,		VK_F1,			NULL	},	// SHIFT+F1
	{ VK_SHIFT,		VK_F2,			NULL	},	// SHIFT+F2
	{ VK_SHIFT,		VK_F3,			NULL	},	// SHIFT+F3
	{ VK_SHIFT,		VK_F4,			NULL	},	// SHIFT+F4
	{ VK_SHIFT,		VK_F5,			NULL	},	// SHIFT+F5
	{ VK_SHIFT,		VK_F6,			NULL	},	// SHIFT+F6
	{ VK_SHIFT,		VK_F7,			NULL	},	// SHIFT+F7
	{ VK_SHIFT,		VK_F8,			NULL	},	// SHIFT+F8
#endif
	{ VK_SHIFT,		VK_F9,			NULL	},	// SHIFT+F9
	{ VK_SHIFT,		VK_F11,			NULL	},	// SHIFT+F11
	{ VK_SHIFT,		VK_F12,			NULL	},	// SHIFT+F12
#if 1
#ifdef _DEBUG
	//{ VK_CONTROL,	VK_NUMPAD0,		shortCutFncDebug	},	// CTRL+NUM0
	{ 0,	VK_END,		shortCutFncDebug	},	// CTRL+NUM0
	{ VK_CONTROL,	VK_NUMPAD1,		shortCutFncDebug2	},	// CTRL+NUM1
#endif
#endif
};


// キーボード处理 ***********************************************************/
void KeyBoardProc( void )
{
	// コントロールキー押されてない时
	if( !( VK[VK_CONTROL] & KEY_ON ) ){
		//  かつシフトキー押されてない时
		if( !( VK[VK_SHIFT] & KEY_ON ) ){
			// 上キー押した时
			if( VK[ VK_UP ] & KEY_ON_REP )
				KeyboardUp();
			// 下キー押した时
			if( VK[ VK_DOWN ] & KEY_ON_REP )
				KeyboardDown();
			// 左キー押した时
			if( VK[ VK_LEFT ] & KEY_ON_REP )
				KeyboardLeft();
			// 右キー押した时
			if( VK[ VK_RIGHT ] & KEY_ON_REP )
				KeyboardRight();
		}
	}
	
	// ＢＳキー处理
	if( VK[ VK_BACK ] & KEY_ON_REP ) KeyboardBackSpace();
	// ＤＥＬＥＴＥキー处理
	if( VK[ VK_DELETE ] & KEY_ON_REP ) KeyboardDelete();
	// ＴＡＢキー处理
	if( VK[ VK_TAB ] & KEY_ON_REP ) KeyboardTab();
	// リターンキー处理
	if( VK[ VK_RETURN ] & KEY_ON_REP ) KeyboardReturn();
	
#ifdef _DEBUG
	
	// 登出キーＯＮなら
	if( logouttKeyFlag == TRUE ){
		// 登出なら
		if( VK[ VK_CONTROL ] & KEY_ON ){
			if( VK[ VK_C ] & KEY_ON_ONCE ){
				//GameState = GAME_LOGIN;
				ChangeProc2( PROC_CHAR_LOGOUT );
				// 登出音
				play_se( SE_NO_LOGOUT, 320, 240 );
			}
		}
	}
#endif

	
#ifdef _DEBUG
	// Ｃｔｒｌ＋Ｃ（クリップボードにコピー）
	if( VK[ VK_CONTROL ] & KEY_ON && VK[ VK_C ] & KEY_ON_ONCE ) SetClipboad();
#endif

	// Ｃｔｒｌ＋Ｖ（クリップボードから入力バッファにコピー）
	if( VK[ VK_CONTROL ] & KEY_ON && VK[ VK_V ] & KEY_ON_ONCE ) GetClipboad();
}

// キーボードクリア ***********************************************************/
void KeyBoardClear( void )
{
	int i;

	for( i = 0; i < VK_MAX; i++ )
	{
		// KEY_OFF_ONCE をＯＦＦにする
		VK[ i ] &= ~KEY_OFF;
		// KEY_ON_ONCE をＯＦＦにする
		VK[ i ] &= ~KEY_ON_ONCE;
		// KEY_ON_REP をＯＦＦにする
		VK[ i ] &= ~KEY_ON_REP;
	}
}

// キーボードクリア２ KEY_ON もクリアする******************************************/
void KeyBoardClear2( void )
{
	int i;

	for( i = 0; i < VK_MAX; i++ )
	{
		// KEY_ON をＯＦＦにする
		VK[ i ] &= ~KEY_ON;
		// KEY_OFF_ONCE をＯＦＦにする
		VK[ i ] &= ~KEY_OFF;
		// KEY_ON_ONCE をＯＦＦにする
		VK[ i ] &= ~KEY_ON_ONCE;
		// KEY_ON_REP をＯＦＦにする
		VK[ i ] &= ~KEY_ON_REP;
	}
}

//-------------------------------------------------------------------------//
// 各キーの押されたか判别（リピートなし。IME对应）                         //
//-------------------------------------------------------------------------//
int keyOnOnce( int key )
{
	return ( VK[key] & KEY_ON_ONCE );
}


//-------------------------------------------------------------------------//
// CTRL+各キーの押されたか判别（リピートなし。IME对应）                    //
//-------------------------------------------------------------------------//
int keyOnOnceWithCtrl( int key )
{
	return( (VK[VK_CONTROL] & KEY_ON) && (VK[key] & KEY_ON_ONCE) );
}


//-------------------------------------------------------------------------//
// SHIFT+各キーの押されたか判别（リピートなし。IME对应）                   //
//-------------------------------------------------------------------------//
int keyOnOnceWithShift( int key )
{
	return( (VK[VK_SHIFT] & KEY_ON) && (VK[key] & KEY_ON_ONCE) );
}


//-------------------------------------------------------------------------//
// 各キーの押されたか判别（リピートあり。IME对应）                         //
//-------------------------------------------------------------------------//
int keyOnRep( int key )
{
	return ( VK[key] & KEY_ON_REP );
}


//-------------------------------------------------------------------------//
// CTRL+各キーの押されたか判别（リピートあり。IME对应）                    //
//-------------------------------------------------------------------------//
int keyOnRepWithCtrl( int key )
{
	return( (VK[VK_CONTROL] & KEY_ON) && (VK[key] & KEY_ON_REP) );
}


//-------------------------------------------------------------------------//
// SHIFT+各キーの押されたか判别（リピートあり。IME对应）                   //
//-------------------------------------------------------------------------//
int keyOnRepWithShift( int key )
{
	return( (VK[VK_SHIFT] & KEY_ON) && (VK[key] & KEY_ON_REP) );
}


//-------------------------------------------------------------------------//
// ショートカットキーが押されたか判断                                      //
//-------------------------------------------------------------------------//
int shortCutKeyOnOnce( int shortCutKeyNo )
{
	if( shortCutKeyNo < 0 || shortCutKeyNo >= SHORT_CUT_KEY_LAST )
		return 0;

	if( shortCutKey[shortCutKeyNo].additionalKey == VK_CONTROL
	 || shortCutKey[shortCutKeyNo].additionalKey == VK_SHIFT )
	{
		return( (VK[shortCutKey[shortCutKeyNo].additionalKey] & KEY_ON)
			&& (VK[shortCutKey[shortCutKeyNo].key] & KEY_ON_ONCE) );
	}
	else
	{
		return ( VK[shortCutKey[shortCutKeyNo].key] & KEY_ON_ONCE );
	}
}


//-------------------------------------------------------------------------//
// ショートカットキーのチェック                                            //
//-------------------------------------------------------------------------//
void checkShortCutKey( void )
{
	int i;

	for( i = 0; i < sizeof( shortCutKey )/sizeof( shortCutKey[0] ); i++ )
	{
		if( shortCutKey[i].func != NULL )
		{
			if( shortCutKey[i].func == shortCutFncChatLineUp || shortCutKey[i].func == shortCutFncChatLineDown 
				||shortCutKey[i].func == shortCutFncChatVoiceUp || shortCutKey[i].func == shortCutFncChatVoiceDown )
			{
				if( VK[VK_CONTROL] & KEY_ON && VK[shortCutKey[ i ].key] & KEY_ON_REP )
				{
					shortCutKey[i].func();
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				
				}
				
			}else
			if( shortCutKeyOnOnce( i ) )
			{
				shortCutKey[i].func();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				
				break;
			}
		}
	}
}
