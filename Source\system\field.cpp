﻿#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include<direct.h>
#include<math.h>


#include"../systeminc/system.h"
#include"../systeminc/loadrealbin.h"
#include"../systeminc/loadsprbin.h"
#include"../systeminc/anim_tbl.h"
#include"../systeminc/login.h"
#include"../systeminc/menu.h"
#include"../systeminc/map.h"
#include"../systeminc/nrproto_cli.h"
#include"../systeminc/pc.h"
#include"../systeminc/netmain.h"
#include"../systeminc/handletime.h"
#include"../systeminc/character.h"
#include"../systeminc/ime_sa.h"
#include "../systeminc/t_music.h"
#include "../systeminc/netproc.h"
#include "../systeminc/math2.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/mouse.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/directDraw.h"
#include"../systeminc/main.h"
#include"../systeminc/keyboard.h"
#include"../systeminc/sndcnf.h"
#include"../systeminc/process.h"

#include "../systeminc/field.h"

#ifdef PUK2
	#include "../systeminc/guild.h"
	#ifdef PUK2_NEW_MENU
		#include "../puk2/interface/menuwin.h"
		#include "../puk2/newDraw/Graphic_ID.h"
	#endif
#endif


//#define	GUILDTEST	1
#define	GUILDTEST	0

// パネル??ボタン全体
short drawFieldButtonFlag = 1;
short drawTimeAnimeFlag = 0;

#ifdef PUK2
	int leftFieldBtnBaseW   = 144;					// 左上ボタンの上下幅
	int leftFieldBtnBaseH   = 64;
	int leftFieldBtnBaseX   = 8;					//      〃     台座の座标
	int leftFieldBtnBaseY   = -1;
#else
	int leftFieldBtnBaseW   = 144;					// 左上ボタンの上下幅
	int leftFieldBtnBaseH   = 64;
	int leftFieldBtnBaseX   = 0;					//      〃     台座の座标
	int leftFieldBtnBaseY   = 0;
#endif

#ifdef PUK2	//#30
	int rightFieldBtnBaseW  = 192;					// 右上ボタンの上下幅
	int rightFieldBtnBaseH  = 64;
	int rightFieldBtnBaseX  = DEF_APPSIZEX			//      〃     台座の座标
								- rightFieldBtnBaseW;
	int rightFieldBtnBaseY  = -1;
	
	#define PUK2_DISP_OFS	(-16)
	
#else
	int rightFieldBtnBaseW  = 160;					// 右上ボタンの上下幅
	int rightFieldBtnBaseH  = 64;
	int rightFieldBtnBaseX  = DEF_APPSIZEX			//      〃     台座の座标
								- rightFieldBtnBaseW;
	int rightFieldBtnBaseY  = 0;
#endif

// 对战ボタン
GRA_BTN_INFO1 duelBtnInfo =
{
#ifdef PUK2	//#30
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
	rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
	rightFieldBtnBaseX+8,  rightFieldBtnBaseY+5,
	27, 27,
	CG_FIELD_DUEL_BTN_1, CG_FIELD_DUEL_BTN_2
};
int duelBtn;
int duelShortCutKey;


// 観战ボタン
GRA_BTN_INFO1 spectateBtnInfo =
{
#ifdef PUK2	//#30
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
	rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
	rightFieldBtnBaseX+38,  rightFieldBtnBaseY+5,
	27, 27,
	CG_FIELD_SPECTATE_BTL_1, CG_FIELD_SPECTATE_BTL_2
};
int spectateBtn;
int spectateShortCutKey;


// 仲间ボタン
GRA_BTN_INFO1 partyBtnInfo =
{
#ifdef PUK2	//#30
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
	rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
	rightFieldBtnBaseX+68, rightFieldBtnBaseY+5,
	27, 27,
	CG_FIELD_GROUP_BTN_1, CG_FIELD_GROUP_BTN_2
};
int partyBtn;									// ボタンが押されたかの状态
short partyBtnEnableFlag = 1;					// ボタンが机能するかどうか
int partyShortCutKey;


// 名刺ボタン
GRA_BTN_INFO1 cardBtnInfo =
{
#ifdef PUK2	//#30
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
	rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
	rightFieldBtnBaseX+98, rightFieldBtnBaseY+5,
	27, 27,
	CG_FIELD_CARD_BTN_1, CG_FIELD_CARD_BTN_2
};
int cardBtn;
int cardShortCutKey;


// 取引ボタン
GRA_BTN_INFO1 tradeBtnInfo =
{
#ifdef PUK2	//#30
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
	rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
	rightFieldBtnBaseX+128, rightFieldBtnBaseY+5,
	27, 27,
	CG_FIELD_TRADE_BTN_1, CG_FIELD_TRADE_BTN_2
};
int tradeBtn = 0;
int tradeShortCutKey;

#ifdef PUK2	//#30
// 家族劝诱ボタン
GRA_BTN_INFO1 guildBtnInfo =
{
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS+30+62, rightFieldBtnBaseY+rightFieldBtnBaseH/2-13,
	rightFieldBtnBaseX+128+30, rightFieldBtnBaseY+5,
	27, 27,
	CG_FIELD_GUILD_BTN_1, CG_FIELD_GUILD_BTN_2
};
int guildBtn = 0;
int guildShortCutKey;
#endif

// アクションボタン
GRA_BTN_INFO1 actionBtnInfo =
{
#ifdef PUK2	//#30
	rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
	rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
	rightFieldBtnBaseX+90, rightFieldBtnBaseY+38,
	65, 17,
	CG_FIELD_ACT_BTN_1, CG_FIELD_ACT_BTN_2
};
int actionBtn;


// フォントサイズ变更ボタン
GRA_BTN_INFO1 chatFontSizeBtn[] =
{
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_LINE_INFO_FONT_M_1, CG_LINE_INFO_FONT_M_2
	},
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_LINE_INFO_FONT_S_1, CG_LINE_INFO_FONT_S_2
	},
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_LINE_INFO_FONT_L_1, CG_LINE_INFO_FONT_L_2
	}
};

#ifdef _SYSTEMMENU_BTN_CONFIG
//聊天范围变更ボタン
GRA_BTN_INFO1 chatAreaSizeBtn[] =
{
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_CHAT_AREA_BTN_1_1, CG_CHAT_AREA_BTN_1_2
	},
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_CHAT_AREA_BTN_2_1, CG_CHAT_AREA_BTN_2_2
	},
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_CHAT_AREA_BTN_3_1, CG_CHAT_AREA_BTN_3_2
	},
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_CHAT_AREA_BTN_4_1, CG_CHAT_AREA_BTN_4_2
	},
	{
#ifdef PUK2	//#30
		rightFieldBtnBaseX+rightFieldBtnBaseW/2+PUK2_DISP_OFS, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#else
		rightFieldBtnBaseX+rightFieldBtnBaseW/2, rightFieldBtnBaseY+rightFieldBtnBaseH/2,
#endif
		rightFieldBtnBaseX+71, rightFieldBtnBaseY+38,  16,  16,
		CG_CHAT_AREA_BTN_5_1, CG_CHAT_AREA_BTN_5_2
	},
};
#endif /* _SYSTEMMENU_BTN_CONFIG */

int fontSizeBtn;


// ランプの点灭用（同期させるためにランプの处理はこれを见る）
short fieldLampDrawFlag;
unsigned int fieldLampFlashTime;

// メールランプ
short mailLamp;
short mailLampInfo;		// メールランプにカーソルが合った时に表示する情报番号

// 等级アップランプ
short levelUpLamp;
short levelUpLampInfo;	// 等级アップランプにカーソルが合った时に表示する情报番号

// ヘルスランプ
short helthLamp;
short helthLampDrawFlag;
short helthLampInfo;	// ヘルスランプにカーソルが合った时に表示する情报番号


int pushEtcFlag;


// 朝??夜アニメ
int amPmAnimeX;
int amPmAnimeGraNoIndex0, amPmAnimeGraNoIndex1;
int amPmAnimeGraNo[] =
{
	CG_FIELD_TIME_ANIME_0,
	CG_FIELD_TIME_ANIME_1,
	CG_FIELD_TIME_ANIME_2,
	CG_FIELD_TIME_ANIME_3
};


// デュエルランプ
int duelOkLampInfo;
int duelFlagChange;

// チャットランプ
int chatModeLampInfo;
int chatFlagChange;

// グループランプ
int groupOkLampInfo;
int groupFlagChange;

// 名刺ランプ
int meishiOkLampInfo;
int meishiFlagChange;

// 取引ランプ
int tradeOkLampInfo;
int tradeFlagChange;

#ifdef PUK2
	// 取引ランプ
	int guildOkLampInfo;
	int guildFlagChange;
#endif

// 仲间??参战??对战ＯＫスイッチフラグ
short etcSwitchChangeFlag = 0;

// 左コックピットにカーソルがあたっているか？
int onCursorFieldWin;

// ワープ后情报表示ウィンドウにマウスカーソルが当っているか？
int onCursorFieldInfoWin;




// サーバへ何かのプロトコルを送った
short etcSendFlag = 0;


// フィールドボタンを押してプロトコルを送った时间
unsigned int fieldBtnPushTime = 0;

// ワープ时に现在の位置を表示
unsigned int fieldInfoTime = 0;


// プロト种类
void initCharActionAnimeChange( void );
int charActionAnimeChange( void );
void initEtcSwitch( void );
int  etcSwitch( void );



struct tm *serverAliveTime;
time_t serverAliveLongTime;


#define arraysizeof( x ) (sizeof(x)/sizeof(x[0]))	

//闘技场のフロアＩＤ
int duelpoint[]={
	1401,
	33221,
	43131,
#ifdef PUK3
	59553,
#endif
#ifdef PUK3_PAB_WATCH
	59545,
#endif
#ifdef PUK3_NEWARENA
	59972,
	59973,
	59974,
	59975,
	59976,
	59977,
	59978,
	59979,
	59980,
	59981,
#endif
};
//---------------------------
//闘技场かどうかのチェック
//---------------------------
BOOL duelPointCheck( void )
{
	int i;
	
	for( i = 0; i < (sizeof(duelpoint)/sizeof(duelpoint[0])) ; i++)
	{
		//一致するなら闘技场だ
		if(mapNo == duelpoint[ i]) return TRUE;
	}
	
	return FALSE;

}

//-------------------------------------------------------------------------//
// 对战ボタン处理                                                          //
//-------------------------------------------------------------------------//
void duelBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		duelShortCutKey = 0;
		return;
	}

	duelBtn = pushGraBtnInfo1( &duelBtnInfo );

	if( (duelBtn & BTN_LEFT_CLICK) || duelShortCutKey )
	{
		int dx, dy;
		int flag;

		duelShortCutKey = 0;

		// 布ティに入ってない时
		// または、リーダの时
		// 正面にキャラがいるかチェック
		dx = moveAddTbl[pc.dir][0];
		dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
#endif
		// 正面にキャラがいるので对战申し込み
		if( (partyModeFlag == 0
		 || (partyModeFlag == 1 && (pc.status & CHR_STATUS_LEADER) != 0))
		 && flag == TRUE
		 && eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 )
		{
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
			{
#if GUILDTEST
#ifndef _DEBUG
				nrproto_AGM_send( sockfd, mapGx, mapGy );
			//	duelSendFlag = 1;
			//	etcSendFlag = 1;
#else
				if( !offlineFlag )
				{
					nrproto_AGM_send( sockfd, mapGx, mapGy );
			//		duelSendFlag = 1;
			//		etcSendFlag = 1;
				}
#endif
#else
#ifndef _DEBUG
				nrproto_DU_send( sockfd, mapGx, mapGy );
			//	duelSendFlag = 1;
			//	etcSendFlag = 1;
#else
				if( !offlineFlag )
				{
					nrproto_DU_send( sockfd, mapGx, mapGy );
			//		duelSendFlag = 1;
			//		etcSendFlag = 1;
				}
#endif
#endif
				fieldBtnPushTime = GetTickCount();
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else
		{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
}


// ショートカットキー
void shortCutFncDuel( void )
{
	duelShortCutKey = 1;
}


//-------------------------------------------------------------------------//
// 对战ボタン表示                                                          //
//-------------------------------------------------------------------------//
void duelBtnDraw( void )
{
	// 对战ボタン
	drawGraBtnInfo1( &duelBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 对战ボタン说明(ボタンにフォーカスがあった时)
	if( (duelBtn & BTN_FOCUS_ON) )
	{
		// デュエルボタン说明
		strcpy( OneLineInfoStr, ML_STRING(197, "申请竞技。(Ctrl+@)") );
	}
}



//-------------------------------------------------------------------------//
// 観战ボタン处理                                                          //
//-------------------------------------------------------------------------//
void spectateBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		spectateShortCutKey = 0;
		return;
	}

	spectateBtn = pushGraBtnInfo1( &spectateBtnInfo );

	if( (spectateBtn & BTN_LEFT_CLICK) || spectateShortCutKey )
	{
		int dx, dy;
		int flag;

		spectateShortCutKey = 0;

		// 布ティに入ってない时
		// または、リーダの时
		// 正面に战闘をしているキャラがいるかチェック
		dx = moveAddTbl[pc.dir][0];
		dy = moveAddTbl[pc.dir][1];
		
		// 闘技场の时
		if(duelPointCheck() == TRUE){
//		if( mapNo == 1401 ){
#ifdef PUK3_NOEXISTCHARA
			flag = checkCharObjPointStatus( mapGx+dx, mapGy+dy,
				CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN, CHR_STATUS_BATTLE | CHR_STATUS_DUEL | CHR_STATUS_WATCH );
#else
			flag = checkCharObjPointStatus( mapGx+dx, mapGy+dy,
				CHAROBJ_TYPE_USER_NPC, CHR_STATUS_BATTLE | CHR_STATUS_DUEL | CHR_STATUS_WATCH );
#endif
		}else{
			flag = 0;
			
		//	flag = checkCharObjPointStatus( mapGx+dx, mapGy+dy,
		//		CHAROBJ_TYPE_USER_NPC, CHR_STATUS_BATTLE | CHR_STATUS_WATCH );
		}
		// 正面に战闘をしているキャラがいる
		if( (partyModeFlag == 0
		 || (partyModeFlag == 1 && (pc.status & CHR_STATUS_LEADER) != 0))
		 && flag == 1
		 && eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 )
		{
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
			{
				// 战闘中の相手に観战を送る
#ifndef _DEBUG
				nrproto_LB_send( sockfd, mapGx, mapGy );
			//	jbSendFlag = 1;
			//	etcSendFlag = 1;
#else
				if( !offlineFlag )
				{
					nrproto_LB_send( sockfd, mapGx, mapGy );
				//	jbSendFlag = 1;		// ohta
				//	etcSendFlag = 1;	// ohta
				}
#endif
				fieldBtnPushTime = GetTickCount();
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else
		{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
}


// ショートカットキー
void shortCutFncSpectate( void )
{
	spectateShortCutKey = 1;
}


//-------------------------------------------------------------------------//
// 観战ボタン表示                                                          //
//-------------------------------------------------------------------------//
void spectateBtnDraw( void )
{
	// 観战ボタン
	drawGraBtnInfo1( &spectateBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 観战ボタン说明(ボタンにフォーカスがあった时)
	if( (spectateBtn & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, ML_STRING(198, "观战。(Ctrl+:)") );
	}
}



//-------------------------------------------------------------------------//
// 布ティーボタン处理                                                    //
//-------------------------------------------------------------------------//
void partyBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		partyShortCutKey = 0;
		return;
	}

	// 特定のフロアでは仲间ボタンを使用できなくする
//	if( mapNo == 31706
//	 || mapNo == 10204
//	 || (10601 <= mapNo && mapNo <= 10605 )
//	 || mapNo == 10919 || mapNo == 10920
//	 || mapNo == 20711 || mapNo == 20712
//	 || mapNo == 1008 || mapNo == 1021
//	 || mapNo == 3008 || mapNo == 3021 )
//	{
//		partyBtnEnableFlag = 0;
//	}
//	else
//	{
		partyBtnEnableFlag = 1;
//	}

	// ボタン情报取得
	partyBtn = pushGraBtnInfo1( &partyBtnInfo );

	if( (partyBtn & BTN_LEFT_CLICK) || partyShortCutKey )
	{
		partyShortCutKey = 0;

		if( partyBtnEnableFlag == 0 )
		{
			// 特定のマップでは仲间になれない
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
		else
		{
			// 布ティに入ってない时の动作
			if( partyModeFlag == 0 )
			{
				int dx, dy;
				int flag;

				// 正面に战闘をしていないキャラがいるかチェック
				dx = moveAddTbl[pc.dir][0];
				dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
				flag = checkCharObjPointNotStatus( mapGx+dx, mapGy+dy,
					(CHAROBJ_TYPE_USER_NPC | CHAROBJ_TYPE_PARTY_OK)|CHAROBJ_TYPE_UNKNOWN, CHR_STATUS_BATTLE );
#else
				flag = checkCharObjPointNotStatus( mapGx+dx, mapGy+dy,
					(CHAROBJ_TYPE_USER_NPC | CHAROBJ_TYPE_PARTY_OK), CHR_STATUS_BATTLE );
#endif
				// 正面にキャラがいるので仲间要求
				if( flag == 1
				 && eventWarpSendFlag == 0
				 && eventEnemySendFlag == 0
				 && sendEnFlag == 0 )
				{
					if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
					{
						// 谁の仲间にもなってない时（自分がリーダーでも）、仲间要求
#ifndef _DEBUG
						nrproto_PR_send( sockfd, mapGx, mapGy, 1 );
#else
						if( !offlineFlag )
						{
							nrproto_PR_send( sockfd, mapGx, mapGy, 1 );
						}
#endif
					//	prSendMode = 1;
					//	prSendFlag = 1;
					//	etcSendFlag = 1;
						fieldBtnPushTime = GetTickCount();
					}
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				else
				{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
			}
			else
			// 布ティの时の动作
			{
				if( eventWarpSendFlag == 0
				 && eventEnemySendFlag == 0
				 && sendEnFlag == 0 )
				{
					if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
					{
						// 谁かの仲间の时、除队
#ifndef _DEBUG
						nrproto_PR_send( sockfd, mapGx, mapGy, 0 );
#else
						if( !offlineFlag )
						{
							nrproto_PR_send( sockfd, mapGx, mapGy, 0 );
						}
#endif
					//	prSendMode = 0;
					//	prSendFlag = 1;		// ohta
					//	etcSendFlag = 1;	// ohta
						fieldBtnPushTime = GetTickCount();
					}
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				else
				{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
			}
		}
	}
}


// ショートカットキー
void shortCutFncParty( void )
{
	partyShortCutKey = 1;
}


//-------------------------------------------------------------------------//
// 布ティーボタン表示                                                    //
//-------------------------------------------------------------------------//
void partyBtnDraw( void )
{
	// 仲间ボタンの表示
	if( partyBtnEnableFlag )
	{
		drawGraBtnInfo1( &partyBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	else
	{
		drawGraBtnInfo1( &partyBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 1 );
	}
	// 仲间ボタン说明(ボタンにフォーカスがあった时)
	if( (partyBtn & BTN_FOCUS_ON) )
	{
		if( partyBtnEnableFlag == 0 )
		{
			strcpy( OneLineInfoStr, ML_STRING(199, "在此无法加入队伍。(Ctrl+/)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(200, "加入队伍。(Ctrl+/)") );
		}
	}
}



//-------------------------------------------------------------------------//
// 名刺ボタン处理                                                          //
//-------------------------------------------------------------------------//
void cardBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		cardShortCutKey = 0;
		return;
	}

	cardBtn = pushGraBtnInfo1( &cardBtnInfo );

	if( (cardBtn & BTN_LEFT_CLICK) || cardShortCutKey )
	{
		int dx, dy;
		int flag;

		cardShortCutKey = 0;

		// 自分の正面にキャラがいるか调べる
		dx = moveAddTbl[pc.dir][0];
		dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
#endif
		// 布ティに入ってない时で正面にキャラがいる
		if(
			//	partyModeFlag == 0  && 
		 flag == TRUE
		 && eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 )
		{
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
			{
				// 名刺交换
#ifndef _DEBUG
				nrproto_AAB_send( sockfd, mapGx, mapGy );
#else
				if( !offlineFlag )
				{
					nrproto_AAB_send( sockfd, mapGx, mapGy );
				}
#endif
				fieldBtnPushTime = GetTickCount();
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else
		{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
}


// ショートカットキー
void shortCutFncCard( void )
{
	cardShortCutKey = 1;
}


//-------------------------------------------------------------------------//
// 名刺ボタン表示                                                          //
//-------------------------------------------------------------------------//
void cardBtnDraw( void )
{
	// 名刺ボタンの表示
	drawGraBtnInfo1( &cardBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 名刺ボタン说明(ボタンにフォーカスがあった时)
	if( (cardBtn & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, ML_STRING(201, "交换名片。(Ctrl+[)") );
	}
}



//-------------------------------------------------------------------------//
// 取引ボタン处理                                                          //
//-------------------------------------------------------------------------//
void tradeBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		tradeShortCutKey = 0;
		return;
	}

	tradeBtn = pushGraBtnInfo1( &tradeBtnInfo );

	if( (tradeBtn & BTN_LEFT_CLICK) || tradeShortCutKey )
	{
		int dx, dy;
		int flag, flag2;

		tradeShortCutKey = 0;

		// 自分の正面２マスまでにキャラがいるか调べる
		dx = moveAddTbl[pc.dir][0];
		dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
		flag2 = checkCharObjPoint( mapGx+dx*2, mapGy+dy*2, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
		flag2 = checkCharObjPoint( mapGx+dx*2, mapGy+dy*2, CHAROBJ_TYPE_USER_NPC );
#endif
		// 布ティがいるか、正面にキャラがいる
		if( partyModeFlag == 1 || flag == TRUE || flag2 == TRUE )
		{
			if( eventWarpSendFlag == 0
			 && eventEnemySendFlag == 0
			 && sendEnFlag == 0 )
			{
				if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
				{
					// トレード相手のリスト要求プロトコル送信
#ifndef _DEBUG
					nrproto_TRPL_send( sockfd );
#else
					if( !offlineFlag )
					{
						nrproto_TRPL_send( sockfd );
					}
#endif
					fieldBtnPushTime = GetTickCount();
				}
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else
		{
			StockChatBufferLine( ML_STRING(202, "没有可交易的对象！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE );
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
}


// ショートカットキー
void shortCutFncTrade( void )
{
	tradeShortCutKey = 1;
}


//-------------------------------------------------------------------------//
// 取引ボタン表示                                                          //
//-------------------------------------------------------------------------//
void tradeBtnDraw( void )
{
	// 取引ボタンの表示
	drawGraBtnInfo1( &tradeBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 取引ボタン说明(ボタンにフォーカスがあった时)
	if( (tradeBtn & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, ML_STRING(203, "交易。(Ctrl+])") );
	}
}






#ifdef PUK2
//-------------------------------------------------------------------------//
// 家族劝诱ボタン处理                                                    //
//-------------------------------------------------------------------------//
void guildBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		guildShortCutKey = 0;
		return;
	}

	guildBtn = pushGraBtnInfo1( &guildBtnInfo );

	if( (guildBtn & BTN_LEFT_CLICK) || guildShortCutKey )
	{
		int dx, dy;
		int flag;
		guildShortCutKey = 0;
		// 布ティに入ってない时
		// または、リーダの时
		// 正面にキャラがいるかチェック
		dx = moveAddTbl[pc.dir][0];
		dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
		flag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
#endif
		// 正面にキャラがいるので对战申し込み
		if( (partyModeFlag == 0
		 || (partyModeFlag == 1 && (pc.status & CHR_STATUS_LEADER) != 0))
		 && flag == TRUE
		 && eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 )
		{
			if(guildBook.title[guildBook.pcGuildTitleId].flag & GUILD_FLAG_INVITE){
				if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
				{
#if 1
#ifndef _DEBUG
					nrproto_AGM_send( sockfd, mapGx, mapGy );
				//	duelSendFlag = 1;
				//	etcSendFlag = 1;
#else
					if( !offlineFlag )
					{
						nrproto_AGM_send( sockfd, mapGx, mapGy );
				//		duelSendFlag = 1;
				//		etcSendFlag = 1;
					}
#endif
#else
#ifndef _DEBUG
					nrproto_DU_send( sockfd, mapGx, mapGy );
				//	duelSendFlag = 1;
				//	etcSendFlag = 1;
#else
					if( !offlineFlag )
					{
						nrproto_DU_send( sockfd, mapGx, mapGy );
				//		duelSendFlag = 1;
				//		etcSendFlag = 1;
					}
#endif
#endif
						fieldBtnPushTime = GetTickCount();
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			else{
				StockChatBufferLine( ML_STRING(204, "不同意邀请！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE );
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
		}
		else
		{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
}


// ショートカットキー
void shortCutFncGuild( void )
{
	guildShortCutKey = 1;
}


//-------------------------------------------------------------------------//
// 家族劝诱ボタン表示                                                    //
//-------------------------------------------------------------------------//
void guildBtnDraw( void )
{
	// 对战ボタン
	drawGraBtnInfo1( &guildBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 对战ボタン说明(ボタンにフォーカスがあった时)
	if( (guildBtn & BTN_FOCUS_ON) )
	{
		// 家族ボタン说明
		strcpy( OneLineInfoStr, ML_STRING(205, "邀请加入家族。(Ctrl+G)") );
	}
}
#endif








//-------------------------------------------------------------------------//
// アクションボタン处理                                                    //
//-------------------------------------------------------------------------//
void actionBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
		return;

	actionBtn = pushGraBtnInfo1( &actionBtnInfo );

	if( (actionBtn & BTN_LEFT_CLICK) )
	{
		menuOpen( MENU_ACTION );
	}
}


//-------------------------------------------------------------------------//
// アクションボタン表示                                                    //
//-------------------------------------------------------------------------//
void actionBtnDraw( void )
{
	// アクションボタンの表示
	drawGraBtnInfo1( &actionBtnInfo, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// アクションボタンの说明(ボタンにフォーカスがあった时)
	if( (actionBtn & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, ML_STRING(206, "打开动作列表。(Ctrl+F)") );
	}
}


//-------------------------------------------------------------------------//
// チャットフォントサイズボタンの处理                                      //
//-------------------------------------------------------------------------//
void fontSizeBtnProc( void )
{
	// ボタンが出てない时は何もしない
	if( !drawFieldButtonFlag )
	{
		return;
	}
	// サイズ变更ボタンが押されたか？
	fontSizeBtn = pushGraBtnInfo1( &chatFontSizeBtn[chatFontSize] );
	if( (fontSizeBtn & BTN_LEFT_CLICK) )
	{
#ifdef _SYSTEMMENU_BTN_CONFIG
		if( !systemMenuBtnConfigSetting){
			// 押されたらサイズ变更
			chatFontSize--;
			if( chatFontSize < 0 )
				chatFontSize = 2;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		else {
			NowMaxVoice++;
			if( NowMaxVoice > MAX_VOICE) NowMaxVoice = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			// チャットエリア表示时间设定
			ChatAreaDispTime = 180;
		}
#else
		// 押されたらサイズ变更
		chatFontSize--;
		if( chatFontSize < 0 )
			chatFontSize = 2;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
#endif /* _SYSTEMMENU_BTN_CONFIG */
	}
}


//-------------------------------------------------------------------------//
// チャットフォントサイズボタンの表示                                      //
//-------------------------------------------------------------------------//
void fontSizeBtnDraw( void )
{
#ifdef _SYSTEMMENU_BTN_CONFIG
	if( !systemMenuBtnConfigSetting){
		// サイズ变更ボタン表示
		drawGraBtnInfo1( &chatFontSizeBtn[chatFontSize], DISP_PRIO_BOX2, 1, BoxColor, 0 );
	}
	else {
		// 聊天范围ボタン表示
		drawGraBtnInfo1( &chatAreaSizeBtn[NowMaxVoice-1], DISP_PRIO_BOX2, 1, BoxColor, 0 );
	}
#else
	// サイズ变更ボタン表示
	drawGraBtnInfo1( &chatFontSizeBtn[chatFontSize], DISP_PRIO_BOX2, 1, BoxColor, 0 );
#endif /* _SYSTEMMENU_BTN_CONFIG */

	// 说明表示(ボタンにフォーカスがあった时)
	if( (fontSizeBtn & BTN_FOCUS_ON) )
	{
#ifdef _SYSTEMMENU_BTN_CONFIG
		if( !systemMenuBtnConfigSetting){
			strcpy( OneLineInfoStr, ML_STRING(207, "变更聊天文字大小。(Ctrl+O)") );
		}
		else {
			strcpy( OneLineInfoStr, ML_STRING(208, "变更聊天范围。(Ctrl+←→)") );
		}
#else
		strcpy( OneLineInfoStr, ML_STRING(207, "变更聊天文字大小。(Ctrl+O)") );
#endif /* _SYSTEMMENU_BTN_CONFIG */
	}
}



//-------------------------------------------------------------------------//
// メールランプ处理                                                        //
//-------------------------------------------------------------------------//
void mailLampProc( void )
{
#if 0
#ifdef _DEBUG
	mailLamp = 1;
#endif
#endif


	// ランプにカーソルがあったら情报表示
	mailLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+72-8, leftFieldBtnBaseY+21,
	 		leftFieldBtnBaseX+72+41-8, leftFieldBtnBaseY+21+9, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+72, leftFieldBtnBaseY+21,
	 		leftFieldBtnBaseX+72+41, leftFieldBtnBaseY+21+9, -1, 0, 0 ) )
#endif
	{
		onCursorFieldWin = 0;
		if( mailLamp )
		{
			// メールが届いてる时
			mailLampInfo = 1;
			if( (mouse.onceState & MOUSE_LEFT_CRICK) )
			{
				// アドレスブックウィンドウ开く
				menuOpen( MENU_ADDRESS_BOOK_WIN );
			}
		}
		else
		{
			// メールが届いてない时
			mailLampInfo = 2;
			// クリックしたら	// ohta
			//if( (mouse.onceState & MOUSE_LEFT_CRICK) )
			//{
				// アドレスブックウィンドウ开く
			//	menuOpen( MENU_ADDRESS_BOOK_WIN );
			//}
		}
	}
}


//-------------------------------------------------------------------------//
// メールランプ表示                                                        //
//-------------------------------------------------------------------------//
void mailLampDraw( void )
{
	// ランプの表示
	if( mailLamp && fieldLampDrawFlag )
	{
#ifdef PUK2
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_MAIL_LAMP, 0 );
#else
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_MAIL_LAMP, 0 );
#endif
	}

	// ランプにカーソルが当った时の情报表示
	if( mailLampInfo == 1 )
	{
		strcpy( OneLineInfoStr, ML_STRING(209, "收到邮件。") );
	}
	else
	if( mailLampInfo == 2 )
	{
		strcpy( OneLineInfoStr, ML_STRING(210, "现在没收到邮件。") );
	}
}




//-------------------------------------------------------------------------//
// 等级アップランプ处理                                                  //
//-------------------------------------------------------------------------//
void levelUpLampProc( void )
{
	// 自分とペットのボーナスポイントがあるかチェックする
	levelUpLamp = CheckBonusPoint();

#if 0	
	// 等级アップ时のボーナスポイントがある时ランプ点灭
	if( ret >= 0 || ret < 5 )
	{
		levelUpLamp = 2;
	}else
	if( ret == 5 )
	{
		levelUpLamp = 1;
	}
	else
	{
		levelUpLamp = 0;
	}
#endif

	// ランプにカーソルがあったら情报表示
	levelUpLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+53-8, leftFieldBtnBaseY+33,
	 		leftFieldBtnBaseX+53+41-8, leftFieldBtnBaseY+33+9, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+53, leftFieldBtnBaseY+33,
	 		leftFieldBtnBaseX+53+41, leftFieldBtnBaseY+33+9, -1, 0, 0 ) )
#endif
	{
		onCursorFieldWin = 0;
		if( levelUpLamp == 5 )
		{
			// 等级アップの振り分けポイントがある时
			levelUpLampInfo = 1;
			if( (mouse.onceState & MOUSE_LEFT_CRICK) )
			{
				// クリックされたのでディテール（状态）ウィンドウを开く
				menuOpen( MENU_STATUS );
				// ディテールウィンドウに切り替える
				statusMenuWin.winMode = 1;
			}
		}
		else
		if( levelUpLamp == 6 )
		{
			// 等级アップの振り分けポイントがない时
			levelUpLampInfo = 2;
		}
		else{	// ohta
			// 使い魔のＬＶアップポイントがあるとき
			levelUpLampInfo = 3;
			if( (mouse.onceState & MOUSE_LEFT_CRICK) )
			{
				// クリックされたのでディテール（状态）ウィンドウを开く
				menuOpen( MENU_MONSTER_STATUS );
				// ディテールウィンドウに切り替える
				mosterStatusMenuWin.winMode = 1;
				// アップしているモンスターのページ设定
				monsterStatusSelPetNo = levelUpLamp;
			}
		}
		
	}
}


//-------------------------------------------------------------------------//
// 等级アップランプ处理                                                  //
//-------------------------------------------------------------------------//
void levelUpLampDraw( void )
{
	//if( levelUpLamp && fieldLampDrawFlag ) // ohta
	if( ( levelUpLamp >= 0 && levelUpLamp <= 5 ) && fieldLampDrawFlag )
	{
#ifdef PUK2
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_LEVEL_UP_LAMP, 0 );
#else
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_LEVEL_UP_LAMP, 0 );
#endif
	}

	// ランプにカーソルが当った时の情报表示
	if( levelUpLampInfo == 1 )
	{
		sprintf( OneLineInfoStr, ML_STRING(211, "现在还有%d点点数。"), bonusPoint );
		//sprintf( OneLineInfoStr, "现在还有点数。" );
	}
	else
	if( levelUpLampInfo == 2 )
	{
		strcpy( OneLineInfoStr, ML_STRING(212, "现在没有点数了。") );
	}
	else
	if( levelUpLampInfo == 3 )
	{
		char name[ 256 ];
		
		int index = sortPet[ levelUpLamp ].index;
		
		// 本名かあだなかチェック
		if( pet[ index ].freeName[ 0 ] != '\0' ){
			strcpy( name, pet[ index ].freeName );
		}else{
			strcpy( name, pet[ index ].name );
		}		
		sprintf( OneLineInfoStr, ML_STRING(213, "现在%s的点数还有%d。"), name, pet[ index ].bonusPoint );
	}
}


//-------------------------------------------------------------------------//
// ヘルスランプ处理                                                        //
//-------------------------------------------------------------------------//
void helthLampProc( void )
{
	// 健康状态がよくない时にランプ点灯
	//if( pc.injuryLv )	// ohta
	// 健康状态がよくない时にランプ点灯、ペットの健康状态もチェック
	if( pc.injuryLv || CheckPetInjury() != -1 )
	{
		helthLamp = 1;
		helthLampDrawFlag = 1;
	}
	else
	{
		helthLamp = 0;
		helthLampDrawFlag = 0;
	}

	// ランプにカーソルがあったら情报表示
	helthLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+33-8, leftFieldBtnBaseY+45,
	 		leftFieldBtnBaseX+33+41-8, leftFieldBtnBaseY+45+9, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+33, leftFieldBtnBaseY+45,
	 		leftFieldBtnBaseX+33+41, leftFieldBtnBaseY+45+9, -1, 0, 0 ) )
#endif
	{
		onCursorFieldWin = 0;
		if( helthLamp )
		{
			// 健康状态がよくない时
			if( (mouse.onceState & MOUSE_LEFT_CRICK) )
			{
				// 健康状态がよくない时にランプ点灯
				if( pc.injuryLv ){
				
					// クリックされたのでコンディション（状态）ウィンドウを开く
					menuOpen( MENU_STATUS );
					// 状态ウィンドウに切り替える
					statusMenuWin.winMode = 0;
				}else{
					// ペットの状态が恶い时
					// モンスターリストウィンドウを开く
					menuOpen( MENU_MONSTER_LIST );
					
				}
			}
			else
			{
				helthLampInfo = 1;
			}
		}
		else
		{
			// 健康状态がいい时
			helthLampInfo = 2;
		}
	}
}


//-------------------------------------------------------------------------//
// ヘルスランプ处理                                                        //
//-------------------------------------------------------------------------//
void helthLampDraw( void )
{
	if( helthLampDrawFlag )
	{
#ifdef PUK2
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_HELTH_LAMP, 0 );
#else
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_HELTH_LAMP, 0 );
#endif
	}

	// ランプにカーソルが当った时の情报表示
	if( helthLampInfo == 1 )
	{
		if( pc.injuryLv != 0 ){
			if( pc.injuryLv <= 25 )
			{
				strcpy( OneLineInfoStr, ML_STRING(214, "现在受到了皮外伤。") );
			}
			else
			if( pc.injuryLv <= 50 )
			{
				strcpy( OneLineInfoStr, ML_STRING(215, "现在受到了轻伤。") );
			}
			else
			if( pc.injuryLv <= 75 )
			{
				strcpy( OneLineInfoStr, ML_STRING(216, "现在受到了重伤。") );
			}
			else
			{
				strcpy( OneLineInfoStr, ML_STRING(217, "现在处于濒死状态。") );
			}
		}else{
			int index = CheckPetInjury();
			char name[ 256 ];
			
			// 本名かあだなかチェック
			if( pet[ index ].freeName[ 0 ] != '\0' ){
				strcpy( name, pet[ index ].freeName );
			}else{
				strcpy( name, pet[ index ].name );
			}		
			if( pet[ index ].injuryLv <= 25 )
			{
				//strcpy( OneLineInfoStr, "现在受了皮外伤。" );
				sprintf( OneLineInfoStr, ML_STRING(218, "现在%s受了皮外伤。"), name );
			}
			else
			if( pet[ index ].injuryLv <= 50 )
			{
				sprintf( OneLineInfoStr, ML_STRING(219, "现在%s受了轻伤。"), name );
			}
			else
			if( pet[ index ].injuryLv <= 75 )
			{
				sprintf( OneLineInfoStr, ML_STRING(220, "现在%s受到了重伤。"), name );
			}
			else
			{
				sprintf( OneLineInfoStr, ML_STRING(221, "现在%s处于濒死状态。"), name );
			}
		}
	}
	else
	if( helthLampInfo == 2 )
	{
		strcpy( OneLineInfoStr, ML_STRING(222, "现在健康状态良好。") );
	}
}


//-------------------------------------------------------------------------//
// フィールドランプ点灭处理(メール、等级アップ)                          //
//-------------------------------------------------------------------------//
void fieldLampFlashProc( void )
{
	if( mailLamp
	 //|| levelUpLamp )	// ohta
	 || ( levelUpLamp >= 0 && levelUpLamp <= 5 ) )
	{
		// 初期化
		if( fieldLampFlashTime == 0 )
		{
			fieldLampDrawFlag = 1;
			fieldLampFlashTime = GetTickCount();
		}
		else
		if( fieldLampFlashTime+FIELD_LAMP_FLASH_TIME < GetTickCount() )
		{
			fieldLampDrawFlag++;
			fieldLampDrawFlag &= 1;
			fieldLampFlashTime = GetTickCount();
		}
	}
	else
	{
		fieldLampDrawFlag = 0;
		fieldLampFlashTime = 0;
	}
}


//-------------------------------------------------------------------------//
// デュエルＯＫランプ处理                                                  //
//-------------------------------------------------------------------------//
void duelOkLampProc( void )
{
	duelOkLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+57-8, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+57+15-8, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+57, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+57+15, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#endif
	{
		duelOkLampInfo = 1;
		onCursorFieldWin = 0;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) )
		{
			duelFlagChange = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	if( duelFlagChange )
	{
#if GUILDTEST
		if( pc.etcFlag & PC_ETCFLAG_GUILD )
		{
			pc.etcFlag &= ~PC_ETCFLAG_GUILD;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_GUILD;
		}
#else
		if( pc.etcFlag & PC_ETCFLAG_DUEL )
		{
			pc.etcFlag &= ~PC_ETCFLAG_DUEL;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_DUEL;
		}
#endif
		pushEtcFlag = 1;
		duelFlagChange = 0;
	}
}


// ショートカットキーでデュエルフラグの变更
void shortCutFncDuelFlagChange( void )
{
	// 战闘时は終了
	if( ProcNo == PROC_BATTLE )
		return;

	duelFlagChange = 1;
}


//-------------------------------------------------------------------------//
// デュエルＯＫランプ情报表示                                              //
//-------------------------------------------------------------------------//
void duelOkLampInfoDraw( void )
{
	if( duelOkLampInfo )
	{
#if GUILDTEST
		if( pc.etcFlag & PC_ETCFLAG_GUILD )
		{
			strcpy( OneLineInfoStr, ML_STRING(223, "现在同意加入家族。(Ctrl+P)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(224, "现在不同意进行竞技。(Ctrl+P)") );
		}
#else
		if( pc.etcFlag & PC_ETCFLAG_DUEL )
		{
			strcpy( OneLineInfoStr, ML_STRING(225, "现在同意进行竞技。(Ctrl+P)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(224, "现在不同意进行竞技。(Ctrl+P)") );
		}
#endif
	}
}


//-------------------------------------------------------------------------//
// チャットモードランプ处理                                                //
//-------------------------------------------------------------------------//
void chatModeLampProc( void )
{
	chatModeLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+74-8, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+74+15-8, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+74, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+74+15, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#endif
	{
		chatModeLampInfo = 1;
		onCursorFieldWin = 0;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) )
		{
			chatFlagChange = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	if( chatFlagChange )
	{
		if( pc.etcFlag & PC_ETCFLAG_CHAT_MODE )
		{
			pc.etcFlag &= ~PC_ETCFLAG_CHAT_MODE;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_CHAT_MODE;
		}
		pushEtcFlag = 1;
		chatFlagChange = 0;
	}
}


// ショートカットキーでチャットフラグの变更
void shortCutFncChatFlagChange( void )
{
	chatFlagChange = 1;
}


//-------------------------------------------------------------------------//
// チャットモードランプ情报表示                                            //
//-------------------------------------------------------------------------//
void chatModeLampInfoDraw( void )
{
	if( chatModeLampInfo )
	{
		if( pc.etcFlag & PC_ETCFLAG_CHAT_MODE )
		{
			strcpy( OneLineInfoStr, ML_STRING(226, "现在使用队伍聊天。(Ctrl+;)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(227, "现在使用正常聊天。(Ctrl+;)") );
		}
	}
}


//-------------------------------------------------------------------------//
// グループＯＫランプ处理                                                  //
//-------------------------------------------------------------------------//
void groupOkLampProc( void )
{
	groupOkLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+91-8, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+91+15-8, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+91, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+91+15, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#endif
	{
		groupOkLampInfo = 1;
		onCursorFieldWin = 0;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) )
		{
			groupFlagChange = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	if( groupFlagChange )
	{
		if( pc.etcFlag & PC_ETCFLAG_PARTY )
		{
			pc.etcFlag &= ~PC_ETCFLAG_PARTY;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_PARTY;
		}
		pushEtcFlag = 1;
		groupFlagChange = 0;
	}
}


// ショートカットキーでグループフラグの变更
void shortCutFncGroupFlagChange( void )
{
	// 战闘时は終了
	if( ProcNo == PROC_BATTLE )
		return;

	groupFlagChange = 1;
}


//-------------------------------------------------------------------------//
// グループランプ情报表示                                                  //
//-------------------------------------------------------------------------//
void groupOkLampInfoDraw( void )
{
	if( groupOkLampInfo )
	{
		if( pc.etcFlag & PC_ETCFLAG_PARTY )
		{
			strcpy( OneLineInfoStr, ML_STRING(228, "现在同意加入队伍。(Ctrl+.)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(229, "现在不同意加入队伍。(Ctrl+.)") );
		}
	}
}


//-------------------------------------------------------------------------//
// 名刺ＯＫランプ处理                                                      //
//-------------------------------------------------------------------------//
void meishiOkLampProc( void )
{
	meishiOkLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+108-8, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+108+15-8, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+108, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+108+15, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#endif
	{
		meishiOkLampInfo = 1;
		onCursorFieldWin = 0;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) )
		{
			meishiFlagChange = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	if( meishiFlagChange )
	{
		if( pc.etcFlag & PC_ETCFLAG_MAIL )
		{
			pc.etcFlag &= ~PC_ETCFLAG_MAIL;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_MAIL;
		}
		pushEtcFlag = 1;
		meishiFlagChange = 0;
	}
}


// ショートカットキーで名刺フラグの变更
void shortCutFncMeishiFlagChange( void )
{
	// 战闘时は終了
	if( ProcNo == PROC_BATTLE )
		return;

	meishiFlagChange = 1;
}


//-------------------------------------------------------------------------//
// 名刺ＯＫランプ情报表示                                                  //
//-------------------------------------------------------------------------//
void meishiOkLampInfoDraw( void )
{
	if( meishiOkLampInfo )
	{
		if( pc.etcFlag & PC_ETCFLAG_MAIL )
		{
			strcpy( OneLineInfoStr, ML_STRING(230, "现在同意名片交换。(Ctrl+L)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(231, "现在不同意进行名片交换。(Ctrl+L)") );
		}
	}
}


//-------------------------------------------------------------------------//
// 取引ＯＫランプ处理                                                      //
//-------------------------------------------------------------------------//
void tradeOkLampProc( void )
{
	tradeOkLampInfo = 0;
#ifdef PUK2
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+125-8, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+125+15-8, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#else
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+125, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+125+15, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
#endif
	{
		tradeOkLampInfo = 1;
		onCursorFieldWin = 0;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) )
		{
			tradeFlagChange = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	if( tradeFlagChange )
	{
		if( pc.etcFlag & PC_ETCFLAG_TRADE )
		{
			pc.etcFlag &= ~PC_ETCFLAG_TRADE;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_TRADE;
		}
		pushEtcFlag = 1;
		tradeFlagChange = 0;
	}
}


// ショートカットキーでトレードフラグの变更
void shortCutFncTradeFlagChange( void )
{
	// 战闘时は終了
	if( ProcNo == PROC_BATTLE )
		return;

	tradeFlagChange = 1;
}


//-------------------------------------------------------------------------//
// 取引ＯＫランプ情报表示                                                  //
//-------------------------------------------------------------------------//
void tradeOkLampInfoDraw( void )
{
	if( tradeOkLampInfo )
	{
		if( pc.etcFlag & PC_ETCFLAG_TRADE )
		{
			strcpy( OneLineInfoStr, ML_STRING(232, "现在同意交易。(Ctrl+,)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(233, "现在不同意交易。(Ctrl+,)") );
		}
	}
}









#ifdef PUK2
//-------------------------------------------------------------------------//
// 家族ＯＫランプ处理                                                      //
//-------------------------------------------------------------------------//
void guildOkLampProc( void )
{
	guildOkLampInfo = 0;
	if( drawFieldButtonFlag
	 && MakeHitBox( leftFieldBtnBaseX+142-8, leftFieldBtnBaseY+4,
	 		leftFieldBtnBaseX+142+15-8, leftFieldBtnBaseY+4+14, -1, 0, 0 ) )
	{
		guildOkLampInfo = 1;
		onCursorFieldWin = 0;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) )
		{
			guildFlagChange = 1;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	if( guildFlagChange )
	{
		if( pc.etcFlag & PC_ETCFLAG_GUILD )
		{
			pc.etcFlag &= ~PC_ETCFLAG_GUILD;
		}
		else
		{
			pc.etcFlag |= PC_ETCFLAG_GUILD;
		}
		pushEtcFlag = 1;
		guildFlagChange = 0;
	}
}


// ショートカットキーでトレードフラグの变更
void shortCutFncGuildFlagChange( void )
{
	// 战闘时は終了
	if( ProcNo == PROC_BATTLE )
		return;

	guildFlagChange = 1;
}


//-------------------------------------------------------------------------//
// 家族ＯＫランプ情报表示                                                  //
//-------------------------------------------------------------------------//
void guildOkLampInfoDraw( void )
{
	if( guildOkLampInfo )
	{
		if( pc.etcFlag & PC_ETCFLAG_GUILD )
		{
			strcpy( OneLineInfoStr, ML_STRING(234, "现在同意邀请加入家族。(Ctrl+H)") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(235, "现在不同意邀请加入家族。(Ctrl+H)") );
		}
	}
}
#endif









//-------------------------------------------------------------------------//
// フィールド上での处理の初期化                                            //
//-------------------------------------------------------------------------//
void initFieldProc( void )
{
	// フィールドボタン表示の有无
	drawFieldButtonFlag = 1;

	// メールランプの初期化
	// initPcAll()で味読を调べて设定してるのでここでは何もしない
	//mailLamp = 0;

#if 0
	// 等级アップランプの初期化
	levelUpLamp = 0;
#endif

	// ヘルスランプの初期化
	helthLamp = 0;
	helthLampDrawFlag = 0;

	// フィールドランプの点灭处理初期化
	fieldLampDrawFlag = 0;
	fieldLampFlashTime = 0;

	// 时间アニメの表示の有无
#if 1
	drawTimeAnimeFlag = 1;
#else
	drawTimeAnimeFlag = 0;
#endif

	// 布ティボタンの机能ＯＮ
	partyBtnEnableFlag = 1;

	etcSendFlag = 0;
	etcSwitchChangeFlag = 0;

	fieldInfoTime = 0;

	onCursorFieldWin = 0;

	duelFlagChange   = 0;
	chatFlagChange   = 0;
	groupFlagChange  = 0;
	meishiFlagChange = 0;
	tradeFlagChange  = 0;

#ifdef PUK2
	guildFlagChange  = 0;
#endif

	duelShortCutKey = 0;
	spectateShortCutKey = 0;
	partyShortCutKey = 0;
	cardShortCutKey = 0;
	tradeShortCutKey = 0;

#ifdef PUK2
	guildShortCutKey = 0;
#endif


}


//-------------------------------------------------------------------------//
// フィールド处理のリセット                                                //
//-------------------------------------------------------------------------//
void resetFieldProc( void )
{
	fieldInfoTime = 0;
}


// 聊天范围处理 ********************************************************
void ChatAreaProc( void )
{
	int startX;
	int startY;
	int i, j;
	
	// 自分のポインタ无かったら返回
	if( pc.ptAct == NULL ) return;
	
	startX = pc.ptAct->x - ( 64 * NowMaxVoice ) - 31;
	startY = pc.ptAct->y - 23;
	
#if 0	
	// ＥＮＤキー押していたら
	if( VK[ VK_END ] & KEY_ON_ONCE ){
		// ＯＮの时
		if( ChatAreaDispFlag == TRUE ){
			// ＯＦＦにする
			ChatAreaDispFlag = FALSE;
		}else{
			// ＯＮにする
			ChatAreaDispFlag = TRUE;
		}
	}
#endif

	// 聊天范围表示フラグＯＮの时
	//if( ChatAreaDispFlag == TRUE ){
	if( ChatAreaDispTime > 0 ){
		// 聊天范围描画
		for( i = 0 ; i < NowMaxVoice * 2 + 1 ; i++ ){
			for( j = 0 ; j < NowMaxVoice * 2 + 1 ; j++ ){
				StockDispBufferEx(
					startX + j * 32 + i * 32,
					startY - j * 24 + i * 24,
					DISP_PRIO_GRID, 3, 0 );
					//DISP_PRIO_GRID, CG_GRID_CURSOR, 0 );
			}
		}
	}
	
	// 聊天范围表示时间マイナス
	ChatAreaDispTime--;
	// リミットチェック
	if( ChatAreaDispTime < 0 ) ChatAreaDispTime = 0;
}

//-------------------------------------------------------------------------//
// フィールド上での处理                                                    //
//-------------------------------------------------------------------------//
void fieldProc( void )
{

	BOOL walkFlag = FALSE;

	pushEtcFlag = 0;
	onCursorFieldWin = 0;

	// 乱数かき回す
	rand2();

#ifdef PUK2_NEW_MENU
	
#else

	// 聊天范围处理
	ChatAreaProc();

#ifdef _DEBUG
#if 0
	// ペットメール演出テスト
	if( VK[ VK_USER_YEN ] & KEY_ON_ONCE )
	{
		int i;
		int graNo;
		static mode = 0;
		int dx, dy;
		short dir;

		graNo = -1;
		for( i = 0; i < MAX_PET; i++ )
		{
			if( pet[i].useFlag )
			{
				graNo = pet[i].graNo;
				break;
			}
		}
		if( graNo >= 0 && pc.ptAct != NULL )
		{
			if( mode == 0 )
			{
				createPetAction( graNo, mapGx, mapGy, pc.ptAct->anim_ang, 0, (rand() % 8), -1 );
			}
			else
			if( mode == 1 )
			{
				dx = moveAddTbl[pc.ptAct->anim_ang][0];
				dy = moveAddTbl[pc.ptAct->anim_ang][1];
				dir = pc.ptAct->anim_ang+4;
				ajustClientDir( &dir );
				createPetAction( graNo, mapGx+dx, mapGy+dy, dir, 1, 0, -1 );
			}
			else
			if( mode == 2 )
			{
				dx = moveAddTbl[pc.ptAct->anim_ang][0];
				dy = moveAddTbl[pc.ptAct->anim_ang][1];
				dir = pc.ptAct->anim_ang+4;
				ajustClientDir( &dir );
				createPetAction( graNo, mapGx+dx, mapGy+dy, dir, 2, 0, -1 );
			}
			else
			if( mode == 3 )
			{
				dx = moveAddTbl[pc.ptAct->anim_ang][0];
				dy = moveAddTbl[pc.ptAct->anim_ang][1];
				dir = pc.ptAct->anim_ang+4;
				ajustClientDir( &dir );
				createPetAction( graNo, mapGx+dx, mapGy+dy, dir, 3, 0, -1 );
			}
			mode++;
			if( mode > 3 )
				mode = 0;
		}
	}
#endif
#endif


	// メニューのフラグを变えようとして连射抑制に引っかかったので
	// 待ち时间が过ぎてから送る
	if( etcSwitchChangeFlag
	 && eventWarpSendFlag == 0
	 && eventEnemySendFlag == 0
	 && sendEnFlag == 0 )
	{
		if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
		{
#ifndef _DEBUG
			nrproto_FS_send( sockfd, pc.etcFlag );
#else
			if( !offlineFlag )
			{
				nrproto_FS_send( sockfd, pc.etcFlag );
			}
#endif
			fieldBtnPushTime = GetTickCount();
			etcSwitchChangeFlag = 0;
		}
	}


	// 他のメニューが出てるときは表示しない
	if( checkMenuOpen( MENU_AREA_ALL ) )
	{
		drawFieldButtonFlag = 0;
	}
	else
	{
		if( ProcNo != PROC_BATTLE )
		{
			drawFieldButtonFlag = 1;
		}
		else
		{
			// 战闘时は表示ボタンはＯＦＦ
			drawFieldButtonFlag = 0;
		}
#if 0
		// 左コックピットのボタン以外は押したら移动できるようにする

		// マウスカーソルがウィンドウに当っているか调べる
		if( MakeHitBox( leftFieldBtnBaseX, leftFieldBtnBaseY,
				leftFieldBtnBaseX+leftFieldBtnBaseW+1, leftFieldBtnBaseY+leftFieldBtnBaseH+1, -1 ) )
		{
			// 当ってたらフラグセット
			onCursorFieldWin = 1;
		}
#endif
	}


	// ＮＲ时间による朝??昼??夜アニメーション处理（ＮＲ时间1日は24时间）
	amPmAnimeGraNoIndex0 = nrTime.animeTime/48;
	amPmAnimeGraNoIndex1 = (amPmAnimeGraNoIndex0+1)%4;
	amPmAnimeX = (nrTime.animeTime % 48);


	// ＰＣが移动しているかチェック
	if( mapVx != 0 || mapVx != 0 )
		walkFlag = TRUE;

#if 0
	// サーバに送ったプロトコルの返事が返ってきたらフラグをリセット
	if( etcSendFlag )
	{
		if( prSendFlag == 0
		 && jbSendFlag == 0
		 && duelSendFlag == 0 )
		{
			etcSendFlag = 0;
		}
		else
		{
			etcSendFlag = 1;
		}
	}
#endif

#if 0
	// 何らかのプロトコルをサーバに送っている时は
	// 他の机能を实行できなくする
	// 步いてるときも
	if( etcSendFlag != 0 || (walkFlag != FALSE && selId != FIELD_FUNC_PARTY) )
	{
		selId = -1;
	}
#endif

	// 对战ボタン处理
	duelBtnProc();

	// 観战ボタン处理
	spectateBtnProc();

	// 仲间ボタン处理
	partyBtnProc();

	// 名刺交换ボタン处理
	cardBtnProc();

	// 取引ボタン处理
	tradeBtnProc();

#ifdef PUK2
	// 取引ボタン处理
	guildBtnProc();
#endif

	// アクションボタン处理
	actionBtnProc();

	// チャットフォントサイズボタン处理
	fontSizeBtnProc();

#if 0
// ランプのテスト
pc.etcFlag = PC_ETCFLAG_PARTY | PC_ETCFLAG_DUEL | PC_ETCFLAG_MAIL | PC_ETCFLAG_CHAT_MODE;
mailLamp = 1;
levelUpLamp = 1;
helthLamp = 1;
#endif


	// メールランプ处理
	mailLampProc();

	// 等级アップランプ处理
	levelUpLampProc();

	// ヘルスランプ处理
	helthLampProc();

	// フィールドランプ点灭处理
	fieldLampFlashProc();

	// デュエルＯＫランプ处理
	duelOkLampProc();

	// チャットモードランプ处理
	chatModeLampProc();

	// グループＯＫモードランプ处理
	groupOkLampProc();

	// 名刺ＯＫランプ处理
	meishiOkLampProc();

	// 取引ＯＫランプ处理
	tradeOkLampProc();


#ifdef PUK2
	// 取引ＯＫランプ处理
	guildOkLampProc();
#endif


	if( pushEtcFlag )
	{
		if( eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 )
		{
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )// 连射抑制
			{
#ifndef _DEBUG
				nrproto_FS_send( sockfd, pc.etcFlag );
#else
				if( !offlineFlag )
				{
					nrproto_FS_send( sockfd, pc.etcFlag );
				}
#endif
				fieldBtnPushTime = GetTickCount();
			}
			else
			{
				// フラグを变えようとしたが连射抑制に引っかかったので
				// 后で送る
				etcSwitchChangeFlag = 1;
			}
		}
	}
#endif
}

//-------------------------------------------------------------------------//
// 战闘处理で呼ばれる                                                      //
//-------------------------------------------------------------------------//
void fieldProc2( void )
{
}


//-------------------------------------------------------------------------//
// フィールド上での处理を反映して描画                                      //
//-------------------------------------------------------------------------//
void drawField( void )
{

#ifdef PUK2_NEW_MENU	//新メニュー

	/* 新メニューはここでは管理しません */

#else					//旧メニュー

#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif
#if 0
#ifdef _DEBUG_MSG

	char msg[256];

	// サーバからEchoの受信を行った时の时间を表示
	//  通常ゲームのプロトコルが送信されている间は更新されない
	sprintf( msg, "%s Server Alive -> %02d/%02d/%02d %02d:%02d:%02d",    //MLHIDE
		selectServerName2[selectServerIndex],
		(serverAliveTime->tm_year % 100), serverAliveTime->tm_mon+1, serverAliveTime->tm_mday,
		serverAliveTime->tm_hour, serverAliveTime->tm_min, serverAliveTime->tm_sec );
	StockFontBuffer( 108, 8, FONT_PRIO_FRONT, 0, msg, 0 );

#endif
#endif


	if( drawFieldButtonFlag )
	{
		// 对战ボタン表示
		duelBtnDraw();

		// 観战ボタン表示
		spectateBtnDraw();

		// 仲间ボタン表示
		partyBtnDraw();

		// 名刺ボタン表示
		cardBtnDraw();

		// 取引ボタン表示
		tradeBtnDraw();

#ifdef PUK2
		// 家族ボタン表示
		guildBtnDraw();
#endif

		// アクションボタン表示
		actionBtnDraw();

		// チャットフォントサイズボタン表示
		fontSizeBtnDraw();

		// メールランプ表示
		mailLampDraw();

		// 等级アップランプ表示
		levelUpLampDraw();

		// ヘルスランプ表示
		helthLampDraw();

		// デュエルＯＫランプ
#if GUILDTEST
		if( pc.etcFlag & PC_ETCFLAG_GUILD )
#else
		if( pc.etcFlag & PC_ETCFLAG_DUEL )
#endif
		{
			StockDispBuffer(
#ifdef	PUK2
				leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
#else
				leftFieldBtnBaseX+leftFieldBtnBaseW/2,
#endif
				leftFieldBtnBaseY+leftFieldBtnBaseH/2,
				DISP_PRIO_MENU, CG_FIELD_DUEL_OK_LAMP, 0 );
		}
		// チャットモードランプ
		if( pc.etcFlag & PC_ETCFLAG_CHAT_MODE )
		{
			StockDispBuffer(
#ifdef	PUK2
				leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
#else
				leftFieldBtnBaseX+leftFieldBtnBaseW/2,
#endif
				leftFieldBtnBaseY+leftFieldBtnBaseH/2,
				DISP_PRIO_MENU, CG_FIELD_CHAT_MODE_LAMP, 0 );
		}
		// グループＯＫランプ
		if( pc.etcFlag & PC_ETCFLAG_PARTY )
		{
			StockDispBuffer(
#ifdef	PUK2
				leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
#else
				leftFieldBtnBaseX+leftFieldBtnBaseW/2,
#endif
				leftFieldBtnBaseY+leftFieldBtnBaseH/2,
				DISP_PRIO_MENU, CG_FIELD_GROUP_OK_LAMP, 0 );
		}
		// 名刺交换ＯＫランプ
		if( pc.etcFlag & PC_ETCFLAG_MAIL )
		{
			StockDispBuffer(
#ifdef	PUK2
				leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
#else
				leftFieldBtnBaseX+leftFieldBtnBaseW/2,
#endif
				leftFieldBtnBaseY+leftFieldBtnBaseH/2,
				DISP_PRIO_MENU, CG_FIELD_MEISHI_OK_LAMP, 0 );
		}
		// 取引ＯＫランプ
		if( pc.etcFlag & PC_ETCFLAG_TRADE )
		{
			StockDispBuffer(
#ifdef	PUK2
				leftFieldBtnBaseX+leftFieldBtnBaseW/2-8,
#else
				leftFieldBtnBaseX+leftFieldBtnBaseW/2,
#endif
				leftFieldBtnBaseY+leftFieldBtnBaseH/2,
				DISP_PRIO_MENU, CG_FIELD_TRADE_OK_LAMP, 0 );
		}

#ifdef PUK2
		// 家族ＯＫランプ
		if( pc.etcFlag & PC_ETCFLAG_GUILD )
		{
#ifdef PUK2
			StockDispBuffer(
				leftFieldBtnBaseX+leftFieldBtnBaseW/2+72,
				leftFieldBtnBaseY+leftFieldBtnBaseH/2-18,
				DISP_PRIO_MENU, CG_FIELD_GUILD_OK_LAMP, 0, &bm );
#else
			StockDispBuffer(
				leftFieldBtnBaseX+leftFieldBtnBaseW/2+72,
				leftFieldBtnBaseY+leftFieldBtnBaseH/2-18,
				DISP_PRIO_MENU, CG_FIELD_GUILD_OK_LAMP, 0 );
#endif
		}
#endif

		// デュエルＯＫランプの情报表示
		duelOkLampInfoDraw();

		// チャットモードランプの情报表示
		chatModeLampInfoDraw();

		// グループＯＫランプの情报表示
		groupOkLampInfoDraw();

		// 名刺ＯＫランプ情报表示
		meishiOkLampInfoDraw();

		// 取引ＯＫボタン情报表示
		tradeOkLampInfoDraw();

#ifdef PUK2
		// 取引ＯＫボタン情报表示
		guildOkLampInfoDraw();
#endif

#ifdef PUK2
		// 左上ボタンの台座
	#ifdef PUK2
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_BTN_BASE_LEFT_PUK2, 1, &bm );
	#else
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_BTN_BASE_LEFT_PUK2, 1 );
	#endif
#else
		// 左上ボタンの台座
		StockDispBuffer(
			leftFieldBtnBaseX+leftFieldBtnBaseW/2,
			leftFieldBtnBaseY+leftFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_BTN_BASE_LEFT, 1 );
#endif

		// 右上ボタンの台座
#ifdef PUK2	//#30
	#ifdef PUK2
		StockDispBuffer(
			rightFieldBtnBaseX+rightFieldBtnBaseW/2,
			rightFieldBtnBaseY+rightFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_BTN_BASE_RIGHT_PUK2, 1, &bm );
	#else
		StockDispBuffer(
			rightFieldBtnBaseX+rightFieldBtnBaseW/2,
			rightFieldBtnBaseY+rightFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_BTN_BASE_RIGHT_PUK2, 1 );
	#endif
#else
		StockDispBuffer(
			rightFieldBtnBaseX+rightFieldBtnBaseW/2,
			rightFieldBtnBaseY+rightFieldBtnBaseH/2,
			DISP_PRIO_MENU, CG_FIELD_BTN_BASE_RIGHT, 1 );
#endif

		// 时间アニメ
		if( drawTimeAnimeFlag )
		{
#ifdef PUK2
			StockDispBuffer( leftFieldBtnBaseX+28-amPmAnimeX-8, leftFieldBtnBaseY+24+1,
				DISP_PRIO_MENU, amPmAnimeGraNo[amPmAnimeGraNoIndex0], 0 );
			StockDispBuffer( leftFieldBtnBaseX+76-amPmAnimeX-8, leftFieldBtnBaseY+24+1,
				DISP_PRIO_MENU, amPmAnimeGraNo[amPmAnimeGraNoIndex1], 0 );
#else
			StockDispBuffer( leftFieldBtnBaseX+28-amPmAnimeX, leftFieldBtnBaseY+24,
				DISP_PRIO_MENU, amPmAnimeGraNo[amPmAnimeGraNoIndex0], 0 );
			StockDispBuffer( leftFieldBtnBaseX+76-amPmAnimeX, leftFieldBtnBaseY+24,
				DISP_PRIO_MENU, amPmAnimeGraNo[amPmAnimeGraNoIndex1], 0 );
#endif
		}
		else
		{
			// 时间アニメを表示しない时（ダンジョンとか）はバック画像を出す
//			StockDispBuffer(
//				rightFieldBtnBaseX+rightFieldBtnBaseW/2,
//				rightFieldBtnBaseY+rightFieldBtnBaseH/2,
//				DISP_PRIO_MENU, FIELD_MENU_RIGHT_BACK, 1 );
		}
	}
#endif
}




//-------------------------------------------------------------------------//
// サーバとの接続が途切れた                                                //
//-------------------------------------------------------------------------//
//   戾り值： 0 ... 处理中
//            1 ... "はい"ボタンが押された
int disconnectServer( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
	static int btnId[1];
	int id = 0;
	char *msg[] =
	{
		ML_STRING(236, "与服务器断开连接。"),
		ML_STRING(237, "回到服务器选择画面。")
	};
#ifdef PUK3_CONNECTERROR
	char str[256];
#endif
	int i;
	int ret = 0;
	int xx, yy;
#ifdef PUK2_NEW_MENU
	char over = 0;
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;
#endif


	if( ptActMenuWin == NULL )
	{
		// 他のウィンドウが出てたら消す
		if( checkMenuOpen( MENU_AREA_ALL ) )
		{
			// 何かのメニューが出ていたら关闭
			menuClose( MENU_ALL );
		}


		for( i = 0; i < sizeof( btnId )/sizeof( int ); i++ )
		{
			btnId[i] = -2;
		}

		// ウィンドウ作成
#ifdef PUK3_CONNECTERROR
		w = 380;
#else
		w = 320;
#endif
		h = 144;
		//根据窗口分辨率计算断开服务器连接的显示文字位置
		x = SymOffsetX + (640 - w)/2;
		y = SymOffsetY + (456 - h)/2;

#ifdef PUK2_NEW_MENU
		ptActMenuWin = makeWindowDisp( x, y, w, h, 4 );
#else
		ptActMenuWin = makeWindowDisp( x, y, w, h, 1 );
#endif
	}

	if( ptActMenuWin != NULL )
	{
		id = -1;
		if( ptActMenuWin->hp >= 1 )
		{

#ifdef PUK2_NEW_MENU
			i = (w-66)>>1;
			if ( MakeHitBox( x+i, y+4*h/5+1, x+i+66, y+4*h/5+1+17, -1 ) ) over = 1;

			// 右键されてなければ終了
			id = -1;
			if( over && (mouse.onceState & MOUSE_LEFT_CRICK) ) id = 0;
#else
			// ボタンの选择判定
			id = selFontId( btnId, sizeof( btnId )/sizeof( int ) );
#endif
			if( id >= 0 )
			{
				DeathAction( ptActMenuWin );
				ptActMenuWin = NULL;
				ret = 1;
			}

			yy = h/5;
		//	for( i = 0; i < sizeof( msg )/sizeof( char * ); i++ )
		//	{
		//		xx = (w - GetStrWidth( msg[i], FONT_KIND_MIDDLE ))/2;
		//		StockFontBuffer( x+xx, y+(i+1)*yy,
		//			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, msg[i], 0, 0 );
		//	}

			xx = (w - GetStrWidth( selectServerName[selectServerIndex], FONT_KIND_BIG ))/2;
			StockFontBuffer( x+xx, y+1*yy - 4,
				FONT_PRIO_FRONT, FONT_KIND_BIG, FONT_PAL_WHITE, selectServerName[selectServerIndex], 0, 0 );
#ifdef PUK3_CONNECTERROR
			sprintf( str, ML_STRING(238, "与服务器断开连接。(%d)"), networkDisconnectErrorNum );
			xx = (w - GetStrWidth( str, FONT_KIND_MIDDLE ))/2;
			StockFontBuffer( x+xx, y+2*yy - 4,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
#else
			xx = (w - GetStrWidth( ML_STRING(236, "与服务器断开连接。"), FONT_KIND_MIDDLE ))/2;
			StockFontBuffer( x+xx, y+2*yy - 4,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(236, "与服务器断开连接。"), 0, 0 );
#endif
			xx = (w - GetStrWidth( ML_STRING(237, "回到服务器选择画面。"), FONT_KIND_MIDDLE ))/2;
			StockFontBuffer( x+xx, y+3*yy - 4,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(237, "回到服务器选择画面。"), 0, 0 );
			
#ifdef PUK2_NEW_MENU
			StockDispBuffer( x+(w>>1), y+4*yy+1+8, DISP_PRIO_WIN2, (over ? GID_BigOKButtonOver : GID_BigOKButtonOn ), 0, &bm );
#else
			xx = (w - GetStrWidth( "はい", FONT_KIND_MIDDLE ))/2;                //MLHIDE
			btnId[0] = StockFontBuffer( x+xx, y+4*yy - 4,
					FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_YELLOW,
					"はい", 2, BoxColor );                                             //MLHIDE
#endif
		}
	}

	return ret;
}


//-------------------------------------------------------------------------//
// ワープ后その场所の名称を表示                                            //
//-------------------------------------------------------------------------//
void drawFieldInfoWin( void )
{
	static ACTION *ptActMenuWin = NULL;
	static int x, y, w, h;
#ifdef PUK2_NEW_MENU
	static char ret = 0;
#else
	int xx, yy;
#endif


	// マウスカーソル当ってるかフラグ初期化
	onCursorFieldInfoWin = 0;

	if( fieldInfoTime == 0 )
	{
		if( ptActMenuWin != NULL )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
		}
		return;
	}

	// ワープ前にオートマップ出してたらここで出す	// ohta
	if( autoMapOpenFlag )
	{
		menuOpen( MENU_AUTOMAP );
		autoMapOpenFlag = 0;
		fieldInfoTime = 0;
		
		return;
	}
	
#ifdef PUK2_NEW_MENU
	if ( WindowFlag[MENU_WINDOW_MAPNAME].wininfo == NULL ){
		if (ptActMenuWin == NULL){
			ptActMenuWin = openMenuWindow( MENU_WINDOW_MAPNAME, 0, 0, &ptActMenuWin );
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );	// ウィンドウ开く音
		}
	}
#else
	if( ptActMenuWin == NULL )
	{
		// 他のウィンドウが出てたら消す
		if( checkMenuOpen( MENU_AREA_ALL ) )
		{
			initMenu();
		}

		// ウィンドウ作成
		w = 4;
		h = 2;
		x = (DEF_APPSIZEX     -w*64)/2;
		y = (DEF_APPSIZEY -24 -h*48)/2;
		
		//ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 1 );
		ptActMenuWin = makeWindowDisp( x, y, w*64, h*48, 3 );	// あたり判定无し	// ohta

		play_se( SE_NO_OPEN_WINDOW, 320, 240 );	// ウィンドウ开く音
	}

	if( ptActMenuWin != NULL )
	{
		if( fieldInfoTime+2000 <= GetTickCount()
		 || checkMenuOpen( MENU_AREA_ALL ) )
		{
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
			fieldInfoTime = 0;
			return;
		}
		if( ptActMenuWin->hp >= 1 )
		{
			xx = (w*64-strlen( mapName )/2*17)/2;
			yy = (h*48-16)/2;
			StockFontBuffer( x+xx, y+yy,
				FONT_PRIO_FRONT, FONT_PAL_WHITE, mapName, 0 );

			// マウスカーソルがウィンドウに当っているか调べる
			if( MakeHitBox( x, y, x+w*64, y+h*48, -1 ) )
			{
				// 当ってたらフラグセット
				onCursorFieldInfoWin = 1;
			}
		}
	}
#endif
}
