﻿/* output by lsgen.perl 0.41 ( 1998 May)
 * made <PERSON><PERSON> Jul 20 17:14:08 2004
 * user takugoto
 * host Goto
 * file /home/<USER>/xg/puk3/nr2/bin/output/nrproto_cli.c
 * util output/nrproto_util.c , output/nrproto_util.h
 * src  /home/<USER>/xg/puk3/nr2/bin/../doc/nrproto.html
 */
#define _NRPROTOCLI_C_
#include "..\systeminc\system.h"
#ifdef WIN32
#include "..\systeminc\nrproto_cli.h"
#else
#include "nrproto_cli.h"
#endif

#ifdef VERSION_TW
int PacketVer = 2;
#endif

char PacketSend[PACKET_VER_ALLNUM][SEND_ALLNUM][20] = {
	{
		"W",                                                                //MLHIDE
		"w",                                                                //MLHIDE
		"EV",                                                               //MLHIDE
		"M",                                                                //ML<PERSON><PERSON>E
		"EN",                                                               //MLH<PERSON>E
		"DU",                                                               //M<PERSON><PERSON><PERSON><PERSON>
		"EO",                                                               //ML<PERSON><PERSON><PERSON>
		"BU",                                                               //ML<PERSON><PERSON><PERSON>
		"JB",                                                               //ML<PERSON><PERSON>E
		"LB",                                                               //MLH<PERSON>E
		"B",                                                                //MLHIDE
		"ID",                                                               //MLHIDE
		"IDF",                                                              //MLHIDE
		"PI",                                                               //MLHIDE
		"DI",                                                               //MLHIDE
		"DG",                                                               //MLHIDE
		"DP",                                                               //MLHIDE
		"MI",                                                               //MLHIDE
		"IR",                                                               //MLHIDE
		"MSG",                                                              //MLHIDE
		"ALI",                                                              //MLHIDE
		"ALN",                                                              //MLHIDE
		"PMSG",                                                             //MLHIDE
		"DAB",                                                              //MLHIDE
		"AAB",                                                              //MLHIDE
		"GI",                                                               //MLHIDE
		"GT",                                                               //MLHIDE
		"GMR",                                                              //MLHIDE
		"BGT",                                                              //MLHIDE
		"AGM",                                                              //MLHIDE
		"LG",                                                               //MLHIDE
		"RGM",                                                              //MLHIDE
		"GML",                                                              //MLHIDE
		"PGML",                                                             //MLHIDE
		"GD",                                                               //MLHIDE
		"PRV",                                                              //MLHIDE
		"PRS",                                                              //MLHIDE
		"PRD",                                                              //MLHIDE
		"PRE",                                                              //MLHIDE
		"PRM",                                                              //MLHIDE
		"PRO",                                                              //MLHIDE
		"L",                                                                //MLHIDE
		"TK",                                                               //MLHIDE
		"FS",                                                               //MLHIDE
		"HL",                                                               //MLHIDE
		"PR",                                                               //MLHIDE
		"KS",                                                               //MLHIDE
		"MP",                                                               //MLHIDE
		"GFL",                                                              //MLHIDE
		"GPD",                                                              //MLHIDE
		"GFLI",                                                             //MLHIDE
		"GPDI",                                                             //MLHIDE
		"IH",                                                               //MLHIDE
		"AC",                                                               //MLHIDE
		"ACS",                                                              //MLHIDE
		"MU",                                                               //MLHIDE
		"TU",                                                               //MLHIDE
		"TRPL",                                                             //MLHIDE
		"TRS",                                                              //MLHIDE
		"TROP",                                                             //MLHIDE
		"TRCL",                                                             //MLHIDE
		"TROC",                                                             //MLHIDE
		"PS",                                                               //MLHIDE
		"ST",                                                               //MLHIDE
		"DT",                                                               //MLHIDE
		"FT",                                                               //MLHIDE
		"LVUP",                                                             //MLHIDE
		"PLVUP",                                                            //MLHIDE
		"SKSW",                                                             //MLHIDE
		"PSSW",                                                             //MLHIDE
		"POS",                                                              //MLHIDE
		"KN",                                                               //MLHIDE
		"WN",                                                               //MLHIDE
		"SP",                                                               //MLHIDE
		"ClientLogin",                                                      //MLHIDE
		"CreateNewChar",                                                    //MLHIDE
		"CharDelete",                                                       //MLHIDE
		"CharLogin",                                                        //MLHIDE
		"CharList",                                                         //MLHIDE
		"CharLogout",                                                       //MLHIDE
		"ProcGet",                                                          //MLHIDE
		"PlayerNumGet",                                                     //MLHIDE
		"Echo",                                                             //MLHIDE
		"Shutdown",                                                         //MLHIDE
		"FC",                                                               //MLHIDE
		"CH",                                                               //MLHIDE
		"CharLoginGate",                                                    //MLHIDE
		"PVUP",                                                             //MLHIDE
		"StallStart",           //台服5.5 摆摊相关的4个命令                           //MLHIDE
		"StallEnd",                                                         //MLHIDE
		"StallBrowse",                                                      //MLHIDE
		"StallBuy",                                                         //MLHIDE
		"ProduceTime",          //台服5.5 比韩服多的两个命令                           //MLHIDE
		"CheckCommand",                                                     //MLHIDE
		"rh"                    //台服7.1 出现的多页银行相关                           //MLHIDE
	},
	{
		"fhQB",                                                             //MLHIDE
		"sz",                                                               //MLHIDE
		"Bj",                                                               //MLHIDE
		"Kp",                                                               //MLHIDE
		"Dc",                                                               //MLHIDE
		"GN",                                                               //MLHIDE
		"OWp",                                                              //MLHIDE
		"NgMy",                                                             //MLHIDE
		"cEj",                                                              //MLHIDE
		"YK",                                                               //MLHIDE
		"ZWdB",                                                             //MLHIDE
		"pNG",                                                              //MLHIDE
		"rmU",                                                              //MLHIDE
		"xeDh",                                                             //MLHIDE
		"Or",                                                               //MLHIDE
		"HEVr",                                                             //MLHIDE
		"WQc",                                                              //MLHIDE
		"aet",                                                              //MLHIDE
		"WsTP",                                                             //MLHIDE
		"VUhO",                                                             //MLHIDE
		"Cp",                                                               //MLHIDE
		"LaR",                                                              //MLHIDE
		"EEk",                                                              //MLHIDE
		"LH",                                                               //MLHIDE
		"OAM",                                                              //MLHIDE
		"Mk",                                                               //MLHIDE
		"LzMi",                                                             //MLHIDE
		"CZ",                                                               //MLHIDE
		"QFLo",                                                             //MLHIDE
		"tMr",                                                              //MLHIDE
		"XIsR",                                                             //MLHIDE
		"SZJO",                                                             //MLHIDE
		"uaE",                                                              //MLHIDE
		"Uab",                                                              //MLHIDE
		"nkda",                                                             //MLHIDE
		"dSc",                                                              //MLHIDE
		"DD",                                                               //MLHIDE
		"yUe",                                                              //MLHIDE
		"rN",                                                               //MLHIDE
		"Gu",                                                               //MLHIDE
		"dDT",                                                              //MLHIDE
		"nB",                                                               //MLHIDE
		"TC",                                                               //MLHIDE
		"ytkA",                                                             //MLHIDE
		"FVm",                                                              //MLHIDE
		"yBsn",                                                             //MLHIDE
		"naXP",                                                             //MLHIDE
		"Vco",                                                              //MLHIDE
		"WwJ",                                                              //MLHIDE
		"QMH",                                                              //MLHIDE
		"Bt",                                                               //MLHIDE
		"Dhme",                                                             //MLHIDE
		"CCou",                                                             //MLHIDE
		"lru",                                                              //MLHIDE
		"EI",                                                               //MLHIDE
		"RXr",                                                              //MLHIDE
		"jmOh",                                                             //MLHIDE
		"yt",                                                               //MLHIDE
		"upU",                                                              //MLHIDE
		"OiJ",                                                              //MLHIDE
		"bLVH",                                                             //MLHIDE
		"Raht",                                                             //MLHIDE
		"Flvp",                                                             //MLHIDE
		"Rxm",                                                              //MLHIDE
		"bk",                                                               //MLHIDE
		"YBbl",                                                             //MLHIDE
		"BOUo",                                                             //MLHIDE
		"Datm",                                                             //MLHIDE
		"XkeK",                                                             //MLHIDE
		"Fe",                                                               //MLHIDE
		"ZX",                                                               //MLHIDE
		"wv",                                                               //MLHIDE
		"dnu",                                                              //MLHIDE
		"JlXw",                                                             //MLHIDE
		"Gbjq",                                                             //MLHIDE
		"cJ",                                                               //MLHIDE
		"ZV",                                                               //MLHIDE
		"MP",                                                               //MLHIDE
		"euw",                                                              //MLHIDE
		"olJ",                                                              //MLHIDE
		"yp",                                                               //MLHIDE
		"rvb",                                                              //MLHIDE
		"Ao",                                                               //MLHIDE
		"IcE",                                                              //MLHIDE
		"FJfT",                                                             //MLHIDE
		"tM",                                                               //MLHIDE
		"Hh",                                                               //MLHIDE
		"gcS",                                                              //MLHIDE
		"pkAe",                                                             //MLHIDE
		"pkAf",                                                             //MLHIDE
		"pkAg",                                                             //MLHIDE
		"pkAh"                                                              //MLHIDE
		"uHWw",             //台服5.5 比韩服多的两个命令 开始制作时发送的时间包                   //MLHIDE
		"CCL",                                                              //MLHIDE
		"rh"                //台服7.1 出现的多页银行相关                               //MLHIDE
	},
	{
		"zA",                                                               //MLHIDE
		"sz",                                                               //MLHIDE
		"pg",                                                               //MLHIDE
		"UUN",                                                              //MLHIDE
		"KeOX",                                                             //MLHIDE
		"ik",                                                               //MLHIDE
		"eL",                                                               //MLHIDE
		"NgMy",                                                             //MLHIDE
		"cEj",                                                              //MLHIDE
		"IMv",                                                              //MLHIDE
		"Rg",                                                               //MLHIDE
		"Ak",                                                               //MLHIDE
		"iVfo",                                                             //MLHIDE
		"fIM",                                                              //MLHIDE
		"QpfE",                                                             //MLHIDE
		"woz",                                                              //MLHIDE
		"FvWm",                                                             //MLHIDE
		"yi",                                                               //MLHIDE
		"fS",                                                               //MLHIDE
		"CV",                                                               //MLHIDE
		"kza",                                                              //MLHIDE
		"LaR",                                                              //MLHIDE
		"rMsv",                                                             //MLHIDE
		"FPkR",                                                             //MLHIDE
		"vcAz",                                                             //MLHIDE
		"Ufm",                                                              //MLHIDE
		"GY",                                                               //MLHIDE
		"CZ",                                                               //MLHIDE
		"uHb",                                                              //MLHIDE
		"npzi",                                                             //MLHIDE
		"XIsR",                                                             //MLHIDE
		"dbQC",                                                             //MLHIDE
		"uId",                                                              //MLHIDE
		"PfqV",                                                             //MLHIDE
		"nkda",                                                             //MLHIDE
		"HoW",                                                              //MLHIDE
		"Ot",                                                               //MLHIDE
		"TLKH",                                                             //MLHIDE
		"wD",                                                               //MLHIDE
		"GH",                                                               //MLHIDE
		"lz",                                                               //MLHIDE
		"nB",                                                               //MLHIDE
		"uSr",                                                              //MLHIDE
		"dcMv",                                                             //MLHIDE
		"FVm",                                                              //MLHIDE
		"zn",                                                               //MLHIDE
		"LKQy",                                                             //MLHIDE
		"Ukuy",                                                             //MLHIDE
		"iET",                                                              //MLHIDE
		"McVa",                                                             //MLHIDE
		"as",                                                               //MLHIDE
		"mjCv",                                                             //MLHIDE
		"BG",                                                               //MLHIDE
		"MIS",                                                              //MLHIDE
		"klF",                                                              //MLHIDE
		"RXr",                                                              //MLHIDE
		"sM",                                                               //MLHIDE
		"ixH",                                                              //MLHIDE
		"QeuC",                                                             //MLHIDE
		"aiAb",                                                             //MLHIDE
		"suI",                                                              //MLHIDE
		"OU",                                                               //MLHIDE
		"Flvp",                                                             //MLHIDE
		"zGp",                                                              //MLHIDE
		"tj",                                                               //MLHIDE
		"ACwB",                                                             //MLHIDE
		"IHw",                                                              //MLHIDE
		"kjSK",                                                             //MLHIDE
		"Noeb",                                                             //MLHIDE
		"HJQp",                                                             //MLHIDE
		"ih",                                                               //MLHIDE
		"ycX",                                                              //MLHIDE
		"xD",                                                               //MLHIDE
		"cZt",                                                              //MLHIDE
		"JFVf",                                                             //MLHIDE
		"aSEl",                                                             //MLHIDE
		"Godf",                                                             //MLHIDE
		"emRV",                                                             //MLHIDE
		"Gp",                                                               //MLHIDE
		"WA",                                                               //MLHIDE
		"yp",                                                               //MLHIDE
		"rvb",                                                              //MLHIDE
		"Or",                                                               //MLHIDE
		"IcE",                                                              //MLHIDE
		"DE",                                                               //MLHIDE
		"PGXE",                                                             //MLHIDE
		"lO",                                                               //MLHIDE
		"Xoee",                                                             //MLHIDE
		"ylGN",                                                             //MLHIDE
		"JMh",                                                              //MLHIDE
		"ElVN",                                                             //MLHIDE
		"wAo",                                                              //MLHIDE
		"IPy",          //开始制作时发送的时间                                        //MLHIDE
		"CCL",                                                              //MLHIDE
		"rh"            //台服7.1 出现的多页银行相关                                   //MLHIDE
	}

};



char PacketRecv[PACKET_VER_ALLNUM][RECV_ALLNUM][20] = {
	{
		"XYD",                                                              //MLHIDE
		"MC",                                                               //MLHIDE
		"M",                                                                //MLHIDE
		"EV",                                                               //MLHIDE
		"EP",                                                               //MLHIDE
		"EN",                                                               //MLHIDE
		"RS",                                                               //MLHIDE
		"RD",                                                               //MLHIDE
		"B",                                                                //MLHIDE
		"IA",                                                               //MLHIDE
		"I",                                                                //MLHIDE
		"LI",                                                               //MLHIDE
		"SI",                                                               //MLHIDE
		"IR",                                                               //MLHIDE
		"BT",                                                               //MLHIDE
		"MSG",                                                              //MLHIDE
		"AL",                                                               //MLHIDE
		"ALI",                                                              //MLHIDE
		"ALN",                                                              //MLHIDE
		"ALO",                                                              //MLHIDE
		"PME",                                                              //MLHIDE
		"AB",                                                               //MLHIDE
		"ABG",                                                              //MLHIDE
		"ABI",                                                              //MLHIDE
		"GC",                                                               //MLHIDE
		"GI",                                                               //MLHIDE
		"GT",                                                               //MLHIDE
		"GM",                                                               //MLHIDE
		"GMI",                                                              //MLHIDE
		"RGM",                                                              //MLHIDE
		"GML",                                                              //MLHIDE
		"GD",                                                               //MLHIDE
		"PRV",                                                              //MLHIDE
		"PRL",                                                              //MLHIDE
		"PRA",                                                              //MLHIDE
		"PRD",                                                              //MLHIDE
		"PRE",                                                              //MLHIDE
		"PRM",                                                              //MLHIDE
		"PRW",                                                              //MLHIDE
		"PRAD",                                                             //MLHIDE
		"TK",                                                               //MLHIDE
		"STK",                                                              //MLHIDE
		"CP",                                                               //MLHIDE
		"CP2",                                                              //MLHIDE
		"KP",                                                               //MLHIDE
		"KP2",                                                              //MLHIDE
		"PP",                                                               //MLHIDE
		"C",                                                                //MLHIDE
		"CN",                                                               //MLHIDE
		"TITLE",                                                            //MLHIDE
		"CA",                                                               //MLHIDE
		"CD",                                                               //MLHIDE
		"CJ",                                                               //MLHIDE
		"CS",                                                               //MLHIDE
		"CT",                                                               //MLHIDE
		"PT",                                                               //MLHIDE
		"S",                                                                //MLHIDE
		"FS",                                                               //MLHIDE
		"HL",                                                               //MLHIDE
		"PR",                                                               //MLHIDE
		"GFL",                                                              //MLHIDE
		"GPD",                                                              //MLHIDE
		"GFLI",                                                             //MLHIDE
		"GPDI",                                                             //MLHIDE
		"TU",                                                               //MLHIDE
		"TRPL",                                                             //MLHIDE
		"TRS",                                                              //MLHIDE
		"TROP",                                                             //MLHIDE
		"TRLI",                                                             //MLHIDE
		"TRLG",                                                             //MLHIDE
		"TRLP",                                                             //MLHIDE
		"TRLPS",                                                            //MLHIDE
		"TRCL",                                                             //MLHIDE
		"TROC",                                                             //MLHIDE
		"PS",                                                               //MLHIDE
		"LVUP",                                                             //MLHIDE
		"PLVUP",                                                            //MLHIDE
		"POS",                                                              //MLHIDE
		"WN",                                                               //MLHIDE
		"EF",                                                               //MLHIDE
		"SE",                                                               //MLHIDE
		"BGMW",                                                             //MLHIDE
		"PC",                                                               //MLHIDE
		"SH",                                                               //MLHIDE
		"PLAYSE",                                                           //MLHIDE
		"ES",                                                               //MLHIDE
		"MN",                                                               //MLHIDE
		"CC",                                                               //MLHIDE
		"ClientLogin",                                                      //MLHIDE
		"CreateNewChar",                                                    //MLHIDE
		"CharDelete",                                                       //MLHIDE
		"CharLogin",                                                        //MLHIDE
		"CharList",                                                         //MLHIDE
		"CharLogout",                                                       //MLHIDE
		"ProcGet",                                                          //MLHIDE
		"PlayerNumGet",                                                     //MLHIDE
		"Echo",                                                             //MLHIDE
		"IP",                                                               //MLHIDE
		"PV",                                                               //MLHIDE
		"PVUP",                                                             //MLHIDE
		"MAC",                                                              //MLHIDE
		"Expire",                                                           //MLHIDE
		"StallStart",                                                       //MLHIDE
		"StallEnd",                                                         //MLHIDE
		"StallBrowse",                                                      //MLHIDE
		"StallBuy"                                                          //MLHIDE
	},
	{
		"uL",                                                               //MLHIDE
		"uRhV",                                                             //MLHIDE
		"Uy",                                                               //MLHIDE
		"XM",                                                               //MLHIDE
		"Mu",                                                               //MLHIDE
		"pDe",                                                              //MLHIDE
		"dq",                                                               //MLHIDE
		"LdwJ",                                                             //MLHIDE
		"joLh",                                                             //MLHIDE
		"PY",                                                               //MLHIDE
		"CgNB",                                                             //MLHIDE
		"RQK",                                                              //MLHIDE
		"vs",                                                               //MLHIDE
		"vIkw",                                                             //MLHIDE
		"oFxZ",                                                             //MLHIDE
		"me",                                                               //MLHIDE
		"nJan",                                                             //MLHIDE
		"Ryfj",                                                             //MLHIDE
		"Ftps",                                                             //MLHIDE
		"IY",                                                               //MLHIDE
		"sO",                                                               //MLHIDE
		"xisQ",                                                             //MLHIDE
		"QZD",                                                              //MLHIDE
		"ho",                                                               //MLHIDE
		"GC",                                                               //MLHIDE
		"iV",                                                               //MLHIDE
		"sYRu",                                                             //MLHIDE
		"tN",                                                               //MLHIDE
		"Wt",                                                               //MLHIDE
		"Nlm",                                                              //MLHIDE
		"tLTB",                                                             //MLHIDE
		"zxMt",                                                             //MLHIDE
		"xgBR",                                                             //MLHIDE
		"cqg",                                                              //MLHIDE
		"ZE",                                                               //MLHIDE
		"oSEp",                                                             //MLHIDE
		"ec",                                                               //MLHIDE
		"GDBg",                                                             //MLHIDE
		"Tbo",                                                              //MLHIDE
		"xrNE",                                                             //MLHIDE
		"ijeb",                                                             //MLHIDE
		"OEu",                                                              //MLHIDE
		"yTeO",                                                             //MLHIDE
		"xwd",                                                              //MLHIDE
		"keoj",                                                             //MLHIDE
		"YNK",                                                              //MLHIDE
		"UW",                                                               //MLHIDE
		"Ak",                                                               //MLHIDE
		"boUU",                                                             //MLHIDE
		"nL",                                                               //MLHIDE
		"AF",                                                               //MLHIDE
		"Ic",                                                               //MLHIDE
		"TAj",                                                              //MLHIDE
		"qkd",                                                              //MLHIDE
		"Lcp",                                                              //MLHIDE
		"Orrz",                                                             //MLHIDE
		"OuX",                                                              //MLHIDE
		"FQ",                                                               //MLHIDE
		"MwCY",                                                             //MLHIDE
		"npgT",                                                             //MLHIDE
		"QQmo",                                                             //MLHIDE
		"beHf",                                                             //MLHIDE
		"YPV",                                                              //MLHIDE
		"CwyS",                                                             //MLHIDE
		"Fx",                                                               //MLHIDE
		"txV",                                                              //MLHIDE
		"tNjQ",                                                             //MLHIDE
		"QPfM",                                                             //MLHIDE
		"Xy",                                                               //MLHIDE
		"nkb",                                                              //MLHIDE
		"yUet",                                                             //MLHIDE
		"RG",                                                               //MLHIDE
		"sTT",                                                              //MLHIDE
		"Tglt",                                                             //MLHIDE
		"PlFa",                                                             //MLHIDE
		"dy",                                                               //MLHIDE
		"XtQZ",                                                             //MLHIDE
		"yP",                                                               //MLHIDE
		"ONoX",                                                             //MLHIDE
		"nTbg",                                                             //MLHIDE
		"bJn",                                                              //MLHIDE
		"MD",                                                               //MLHIDE
		"SVB",                                                              //MLHIDE
		"Hak",                                                              //MLHIDE
		"eTN",                                                              //MLHIDE
		"hgGk",                                                             //MLHIDE
		"MekK",                                                             //MLHIDE
		"it",                                                               //MLHIDE
		"jY",                                                               //MLHIDE
		"CMPt",                                                             //MLHIDE
		"VH",                                                               //MLHIDE
		"SUxh",                                                             //MLHIDE
		"tDg",                                                              //MLHIDE
		"weZ",                                                              //MLHIDE
		"ehR",                                                              //MLHIDE
		"fhfx",                                                             //MLHIDE
		"jE",                                                               //MLHIDE
		"nPP",                                                              //MLHIDE
		"SQzZ",                                                             //MLHIDE
		"gjE",                                                              //MLHIDE
		"hf",                                                               //MLHIDE
		"Expire",                                                           //MLHIDE
		"pkAa",                                                             //MLHIDE
		"pkAb",                                                             //MLHIDE
		"pkAc",                                                             //MLHIDE
		"pkAd"                                                              //MLHIDE
	},
	{
		"QBt",                                                              //MLHIDE
		"jmb",                                                              //MLHIDE
		"aBS",                                                              //MLHIDE
		"EZ",                                                               //MLHIDE
		"OaHi",                                                             //MLHIDE
		"dAgg",                                                             //MLHIDE
		"Gn",                                                               //MLHIDE
		"hB",                                                               //MLHIDE
		"LYIm",                                                             //MLHIDE
		"ELoy",                                                             //MLHIDE
		"rU",                                                               //MLHIDE
		"PudO",                                                             //MLHIDE
		"vWpn",                                                             //MLHIDE
		"xmgQ",                                                             //MLHIDE
		"MTg",                                                              //MLHIDE
		"Mhbt",                                                             //MLHIDE
		"Ilz",                                                              //MLHIDE
		"dM",                                                               //MLHIDE
		"Dn",                                                               //MLHIDE
		"IYo",                                                              //MLHIDE
		"lDuJ",                                                             //MLHIDE
		"gnxd",                                                             //MLHIDE
		"PhdQ",                                                             //MLHIDE
		"vI",                                                               //MLHIDE
		"GC",                                                               //MLHIDE
		"aDn",                                                              //MLHIDE
		"znv",                                                              //MLHIDE
		"yuD",                                                              //MLHIDE
		"Kka",                                                              //MLHIDE
		"oRut",                                                             //MLHIDE
		"Rll",                                                              //MLHIDE
		"frQp",                                                             //MLHIDE
		"HNP",                                                              //MLHIDE
		"eZT",                                                              //MLHIDE
		"IUn",                                                              //MLHIDE
		"DpSY",                                                             //MLHIDE
		"cNB",                                                              //MLHIDE
		"ow",                                                               //MLHIDE
		"DyAG",                                                             //MLHIDE
		"KyN",                                                              //MLHIDE
		"zdr",                                                              //MLHIDE
		"Zd",                                                               //MLHIDE
		"aPc",                                                              //MLHIDE
		"UU",                                                               //MLHIDE
		"IsSy",                                                             //MLHIDE
		"AU",                                                               //MLHIDE
		"TNT",                                                              //MLHIDE
		"nsY",                                                              //MLHIDE
		"JJQj",                                                             //MLHIDE
		"Sc",                                                               //MLHIDE
		"NI",                                                               //MLHIDE
		"uSB",                                                              //MLHIDE
		"jv",                                                               //MLHIDE
		"cNE",                                                              //MLHIDE
		"Rspn",                                                             //MLHIDE
		"OBee",                                                             //MLHIDE
		"Yo",                                                               //MLHIDE
		"SkBD",                                                             //MLHIDE
		"sZWu",                                                             //MLHIDE
		"bNY",                                                              //MLHIDE
		"ysVc",                                                             //MLHIDE
		"ND",                                                               //MLHIDE
		"LY",                                                               //MLHIDE
		"nFV",                                                              //MLHIDE
		"JgO",                                                              //MLHIDE
		"Cxo",                                                              //MLHIDE
		"ms",                                                               //MLHIDE
		"NdM",                                                              //MLHIDE
		"nUbJ",                                                             //MLHIDE
		"hJq",                                                              //MLHIDE
		"RwK",                                                              //MLHIDE
		"Yq",                                                               //MLHIDE
		"xEg",                                                              //MLHIDE
		"We",                                                               //MLHIDE
		"BHe",                                                              //MLHIDE
		"kg",                                                               //MLHIDE
		"sA",                                                               //MLHIDE
		"Ov",                                                               //MLHIDE
		"BZ",                                                               //MLHIDE
		"zzd",                                                              //MLHIDE
		"TFCu",                                                             //MLHIDE
		"wiB",                                                              //MLHIDE
		"bJg",                                                              //MLHIDE
		"PVfz",                                                             //MLHIDE
		"murD",                                                             //MLHIDE
		"ftaN",                                                             //MLHIDE
		"TeD",                                                              //MLHIDE
		"GmNM",                                                             //MLHIDE
		"whV",                                                              //MLHIDE
		"DZ",                                                               //MLHIDE
		"oy",                                                               //MLHIDE
		"hPUd",                                                             //MLHIDE
		"Daj",                                                              //MLHIDE
		"HMNy",                                                             //MLHIDE
		"cJzI",                                                             //MLHIDE
		"Bi",                                                               //MLHIDE
		"LOh",                                                              //MLHIDE
		"lSZ",                                                              //MLHIDE
		"PfM",                                                              //MLHIDE
		"ed",                                                               //MLHIDE
		"tq",                                                               //MLHIDE
		"Expire",                                                           //MLHIDE
		"Tmrx",                                                             //MLHIDE
		"KXhH",                                                             //MLHIDE
		"ft",                                                               //MLHIDE
		"Yx",                                                               //MLHIDE
		"iKe",                                                              //MLHIDE
		"kiG"                                                               //MLHIDE
	}

};

/*
  <LI><a name="CS_W"><font color=red>clienttoserver W( int x, int y, string direction);<br></font></a>
      (Walk)步く。向きをかえる。クライアントはユーザーが指定した経路を计算し，
      その歩きの経路を送信する。クライアントは既に受けたマップデータなどにより
      当たり判定などを行うが，これはサーバーと既に完全一致していることが前提と
      なる。サーバーはこのWによる应答はしない。サーバーはクライアントから受け
      取った座标を元に步く处理を行うが，それは他ユーザーに见えるための处理など
      を行う为だけに行う。
      また，サーバーは１步キャラが步く每にMCを送信する。また，クライアントはMC
      にてチェックサムが违えばマップをサーバーに要求しないといけないが，その际
      步いている状态などでは歩きの再计算が必要になるかもしれない。

      <br><br>
      <dl>
	<dt>int x,y
	<dd>歩き始めのプレイヤーのフロア，ｘ，ｙ座标。
	<dt>string direction
	<dd>あるく方向 a,b,c,d,e,f,g,hが场所が变化する移动で、
	    aが真上(dx,dy)=(0,-1)で、みぎまわりに hが左上(-1,-1)
	    A,B,C,D,E,F,G,H がその场回転。向きはそれぞれ真上か
	    らみぎまわり。右右上下とあるく场合は "ccae"という
	    文字列を送信し、その场で左に向くばあいは"G"と送信
	    することになる。かならず1步1文字に对应している。こ
	    の文字列はエスケープしない。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

#ifdef PUK3_CONNDOWNWALK
void nrproto_W_send( int fd,int x,int y,char* direction,int warpid )
#else
void nrproto_W_send( int fd,int x,int y,char* direction )
#endif
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_W] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( direction ) ,nrproto.workbufsize );
#ifdef PUK3_CONNDOWNWALK
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( warpid ) ,nrproto.workbufsize );
#endif
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_W2"><font color=red>clienttoserver w( int x, int y, string direction);<br></font></a>
      基本的にはＷと全く同じ。クライアントが，その１回のログイン（起动？）の内に，
      既にマップを読んでいたら（もしくは，步いてMCが来て，チェックがOKだった）场合，
      もうMCは必要ないので，步く时にMC要らない要求をだしながら步くのがこのwプロトコルである。
      このｗを送信する条件として，次に出现するマップのイベントオブジェクトが <a href="#CS_EV">CHAR_EVENT_ALTERRATIVE</a>
      だった场合には，このイベントオブジェクトが变化している可能性があるので普通のＷ，又はその部分をマップ要求して步く事。
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_w_send( int fd,int x,int y,char* direction )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_w]);
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( direction ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_EV"><font color=red>clienttoserver EV( int event,int seqno,int x, int y, int dir);<br></font></a>
	クライアントがイベントオブジェクトを踏んだ时に発生する。
	ワープや固定敌へのエンカウント等に使用される。
      <br><br>
      <dl>
	<dt>int event
	<dd>イベントの种类。char_base.hでは以下のように定义されている。<br>
	<pre>
typedef enum
{
	CHAR_EVENT_NONE,		イベントは発生しない
	CHAR_EVENT_NPC,			固定NPCである
	CHAR_EVENT_ENEMY,		固定敌エンカウントイベント
	CHAR_EVENT_WARP,		ワープイベント
	CHAR_EVENT_ALTERRATIVE,		何も无いが，变化するかもしれないもの。（マップ読込时に要求する事）
	CHAR_EVENTNUM,
}CHAR_EVENT;
	</pre>
	CHAR_EVENT_NPCの场合は，单なるNPCで，しかもぶつかって欲しいものなど。
	クライアントはEVを送らず，ぶつかったかを判断して手前で止まるようにする事。<br>
	<dt>int seqno
	<dd>イベントにシーケンスNoを打っておく。イベントが起こる度にインクリメントしていく事。
	    これはクライアントが管理する。
	    サーバーのEVの应答に对して使う。整合性を取るため。
	<dt>int x,y
	<dd>自分の位置
	<dt>int dir
	<dd>イベントのある向き。自分自身の位置とこの向きからイベントの场所を探す。自分自身と同じ场所の时は-1。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_EV_send( int fd,int event,int seqno,int x,int y,int dir )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_EV] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( event ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seqno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dir ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_M"><font color=red>clienttoserver M( int id, int fl , int x1 , int y1 , int x2, int y2 );<br></font></a>
      マップを要求する。このコマンドの返答はMコマンドでかえってくる。
      送信タイミングは，MCが来てチェックサムがあわなかった时と，
      歩きを计算した时に现れるマップが自分が持っていない时である。
      <dl>
        <dt>int id
        <dd>マップID
	<dt>int fl
	<dd>フロア番号
	<dt>int x1
	<dd>左上X
	<dt>int y1
	<dd>左上Y
	<dt>int x2
	<dd>右下X
	<dt>int y2
	<dd>右下Y
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_M_send( int fd,int id,int fl,int x1,int y1,int x2,int y2 )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_M] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( id ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fl ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x2 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y2 ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_EN"><font color=red>clienttoserver EN( int x, int y );<br></font></a>
	クライアントが（固定じゃない）敌とエンカウントしたい时に送信する。
	仲间歩きの时のリーダーじゃない人はこれを送信しないようにする事。
      <br><br>
      <dl>
	<dt>int x,y
	<dd>エンカウントしたプレイヤーのフロア，ｘ，ｙ座标。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_EN_send( int fd,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_EN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_DU"><font color=red>clienttoserver DU( int x, int y);<br></font></a>
	クライアントがプレイヤー同士のエンカウントを要求した场合に送信する。
	仲间歩きの时のリーダーじゃない人はこれを送信しないようにする事。
      <br><br>
      <dl>
	<dt>int x,y
	<dd>エンカウントしたプレイヤーのフロア，ｘ，ｙ座标。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_DU_send( int fd,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_DU] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_EO"><font color=red>clienttoserver EO( int dummy );<br></font></a>
	クライアントが最后のムービーを见終わったときに送る。
      <dl>
	<dt>int dummy
	<dd>ダミーデータ。今は何でも良い。
      </dL>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_EO_send( int fd,int dummy )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_EO] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dummy ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_BU"><font color=red>clienttoserver BU( int dummy );<br></font></a>
	クライアントがエンカウントを中断したい场合に送る。
      <dl>
	<dt>int dummy
	<dd>ダミーデータ。今は何でも良い。
      </dL>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_BU_send( int fd,int dummy )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_BU] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dummy ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_JB"><font color=red>clienttoserver JB( int x, int y );<br></font></a>
	他の战闘に参战したい场合に送る。失败、成功は EN で送り返す。
      <dl>
	<dt>int x, int y;
	<dd>クライアントの现在の座标。
      </dL>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_JB_send( int fd,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_JB] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_LB"><font color=red>clienttoserver LB( int x, int y );<br></font></a>
	他の战闘を観战したい场合に送る。失败、成功は EN で送り返す。
      <dl>
	<dt>int x, int y;
	<dd>クライアントの现在の座标。
      </dL>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_LB_send( int fd,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_LB] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_B"><font color=red>clienttoserver B( string command );<br></font></a>
      クライアントからプレイヤーの战闘时コマンドを指定する。ここで相手番号とは０～１９で指定すると个人を指し、２０だと右侧サイド全体を指定。
      ２１だと左侧サイド全体。２２だとフィールド全体を指す。<br><br>
      <ul>
	<li>H|相手番号(%X)(0～19)<br>
	    通常攻击。相手の番号を指定<br>
	    <br>
	<li>E<br>
	    逃げる。<br>
	    <br>
	<li>G<br>
	    防御。<br>
	    <br>
	<li>N<br>
	    何もしない。<br>
	    <br>
	<li>T|相手番号(%X)<br>
	    捕获。<br>
	    <br>
	<li>J|咒术の番号(%X)|相手番号(%X)。<br>
	    咒术を使う。ここで咒术の番号とはプレイヤーの咒术枠の番号を指す。よって(0～5)。<br>
	    <br>
	<li>W|ペット技番号(%X)|相手番号(%X)<br>
	    ペット技を使用する。ここでのペット技番号はペットの技枠の番号。よって(0～7)<br>
	    <br>
	<li>M|ペットの番号(%d)。<br>
	    ペット入れ替え<br>
	    <br>
	<li>S|スキルの番号(%X)|スキル技の番号(%X)|谁に(%X)<br>
	    スキル発动。スキル技番号はスキルの技枠の番号。<br>
	    <br>
	<li>P|プレイヤーの番号(%d)。<br>
	    前项列の入れ替えを行う。<br>
	    <br>
	<li>Q|アイテム枠番号( 8 - 27 ( 素手256 ))<br>
	    装备变更を行う。<br>
	    <br>
	<li>U<br>
	    观战结束<br>
	    <br>
	<li>I|アイテムの番号(%X)|相手番号(%X)。<br>
	    アイテムを使う。ここでアイテムの番号とはプレイヤーのアイテム枠の番号を指す。よって(5～19)。<br>
	    <br>
	<li>R|リバースの状态(%d)。<br>
	    リバースのＯｎ／Ｏｆｆを行う。状态が０ならＯｆｆ、１ならＯｎにする。<br>
	    <br>
      </ul>
      <br>
      <br>
      <br>

*/

void nrproto_B_send( int fd,char* command )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_B] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( command ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_ID"><font color=red>clienttoserver ID( int x, int y, int haveitemindex,int toindex);<br></font></a>
      (useItemDir)アイテムを使う。ほぼフィールド上からしか呼ばれない。バトル中は别プロトコルにてアイテム使用の
      プロトコルが飞んでくる。
      
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int haveitemindex
	<dd> どのアイテムを使ったか。アイテムの场所を送る。
	<dt>int toindex
	<dd> どんな对象に使用したか。
	<pre>
	  自分     = 0
	  ペット   = 1 ～5
	  仲间     = 6 ～10 （自分自身も含まれている）
	  アイテム = 11 ～ 37
	  不明なものについては-1を入れておく。
	</pre>
	<br>
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_ID_send( int fd,int x,int y,int haveitemindex,int toindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ID] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( haveitemindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_IDF"><font color=red>clienttoserver IDF( int x, int y, int haveitemindex,int toindex);<br></font></a>
      (useItemDirRecoveryFinal)最終的にアイテムを使う。（ウインドウがある场合）
      <BR>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int haveitemindex
	<dd> どのアイテムを使ったか。アイテムの场所を送る。
	<dt>int toindex
	<dd> 何に对して技を使用したか。<br>
		オペレーション分类ーCでは何番目のキャラに技を使用したか。
		<a href="#SC_GFLI">GFLI</a>または<a href="#SC_GPDI">GPDI</a>で贳ったキャラのリストの番号。<br>
		GFLIのindexはGPDIの时と同じように注意すること。<br>
		<br>
		---- begin comment out ----<br>
		谁に魔法を使用したか。これはオブジェクトやキャラのindexではない。以下の样になっている。
			<pre>
				  自分    = 0
			  	  ペット  = 1 ～5
			  	  仲间    = 6 ～10 （S N の0～4に对应。自分自身も含まれている）
		  	</pre>
				对象が全员，とか分からない，とかの场合は-1で送信する。<br>
		---- end comment out ----<br>
    	<br>
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_IDF_send( int fd,int x,int y,int haveitemindex,int toindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_IDF] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( haveitemindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PI"><font color=red>clienttoserver PI( int x, int y,int dir );<br></font></a>
      (PickupItem)
      アイテムを拾う。拾うのは足元をふくむ邻接する9マスである。<br><br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int dir
	<dd>拾う方向。その方向と、足元を见る。 负の时は足元だけ见る。
      </dl>
      <br>
      <hr>
      <br>
      
*/

void nrproto_PI_send( int fd,int x,int y,int dir )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dir ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_DI"><font color=red>clienttoserver DI( int x, int y, int itemindex);<br></font></a>
      (DropItem)
      アイテムを置く。置くのはドロップボタンに置くので、方向や位置の
      指定はない。itemindexのアイテムを置く。いつでも自分の前に置く。
      <br><br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int itemindex
	<dd>置きたいアイテムのインデックス。
      </dl>
      <br>
      <hr>
      <br>
      
*/

void nrproto_DI_send( int fd,int x,int y,int itemindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_DI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
#ifdef VERSION_TW
	//台服丢物品多出一个参数,功能未知待分析
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string("0"), nrproto.workbufsize); //MLHIDE
#endif
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( itemindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_DG"><font color=red>clienttoserver DG( int x, int y, int amount );<br></font></a>
      (DropGold)
      お金を置く。SA用に现在位置の座标を追加。<br><br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int amount
	<dd>置くお金の量。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_DG_send( int fd,int x,int y,int amount )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_DG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( amount ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_DP"><font color=red>clienttoserver DP( int x, int y,int petindex);<br></font></a>
      (DropPet)
      ペットを置く。方向や位置の指定はない。
      petindexのアイテムを置く。いつでも自分の前に置く。
      <br><br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int petindex
	<dd>置きたいペットのindex。プレイヤーが何番目に持っているペットか。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_DP_send( int fd,int x,int y,int petindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_DP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( petindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_MI"><font color=red>clienttoserver MI( int fromindex , int toindex, int num);<br></font></a>
      (MoveItem)<br>
      アイテムを移动/装备/装备をはずす。装备になるかどうかは
      移动の场所で决まる。<br><br>
      <dl>
	<dt>int fromindex
	<dd>移动するアイテムのインデックス。
	<dt>int toindex
	<dd>目的地のアイテムインデックス。
      目的地とは以下の通り。
      <ul>
	<li>0    兜を装备する所( 头 )
	<li>1    铠を装备する所( 体 )
	<li>2    攻击アイテムを装备する所(手)
	<li>3    装饰品を装备する所1
	<li>4    装饰品を装备する所2
	<li>それ以上。   アイテム栏
      </ul>
	<dt>int num
	<dd>动かす数。-1または持っている以上の数で、全ての分を动かすということ。
	移动先にアイテムがある时も全ての分を动かす。
    </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_MI_send( int fd,int fromindex,int toindex,int num )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_MI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fromindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( num ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_IR"><font color=red>clienttoserver IR( int haveskillindex);</font><br></a>
	(ItemRecipe)
	指定されたhaveskillindexの场所にあるスキルの、覚えているアイテムのレシピ情报一覧を要求する。<br>
	<br>
	<dl>
	<dt>int haveskillindex
	<dd>自分の持っているスキルのindex（トランス中は+100される）。この场所のスキルの、アイテムのレシピヘッダーを要求する。
	これが生产系スキルでなかったりした场合は当然返答は无い。
	</dl>
	<br>
	<hr>
	<br>
*/

void nrproto_IR_send( int fd,int haveskillindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_IR] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( haveskillindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_MSG"><font color=red>clienttoserver MSG( int index , string  message , int color );<br></font></a>
      (MeSsaGe)
      アドレスブックの相手に信息を送信。indexは自分のアドレスブックのインデックスである。<br><br>
      <dl>
	<dt>int index
	<dd>アドレスブックにいる相手のインデックス。<br>
	    群邮件の场合、10Bit を立てて送信すること。<br>
	    この时、下位の 9Bit が０の场合全员に、１の场合ONライン状态の相手全员に送信される。
	<dt>string message
	<dd>相手に送信する信息。EUCでサーバーに送信する。この文
	    字列はデリミタをふくまないので、エスケープする必要はない。
	<dt>int color
	<dd>信息の<a href="#coloring">色</a>。
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

void nrproto_MSG_send( int fd,int index,char* message,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_MSG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_ALI"><font color=red>clienttoserver ALI( int albumid );<br></font></a>
      (ALbumInfo)<br>
      クライアントからサーバーに对して、何番のアルバムIDについての情报をくれと要求する<br>
      トラフィックを考え、ユーザーがアルバムを见る时くらいにしか送信してはいけない。<br>
      <dl>
      <dt>int albumid<br>
      <dd>アルバムのID<br>
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_ALI_send( int fd,int albumid )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ALI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( albumid ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_ALN"><font color=red>clienttoserver ALN( int albumid );<br></font></a>
      (ALbumpetName)<br>
      クライアントからサーバーに对して、何番のアルバムIDについてのペットの名称情报をくれと要求する<br>
      <dl>
      <dt>int albumid<br>
      <dd>アルバムのID<br>
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_ALN_send( int fd,int albumid )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ALN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( albumid ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PMSG"><font color=red>clienttoserver PMSG( int index, int petindex, int itemindex, string  message , int color );<br></font></a>
      (PetMeSsaGe)
      アドレスブックの相手にペットで信息を送信。indexは自分のアドレスブッ
      クのインデックスである。アイテムも送信することが出来る。<br><br>
      <dl>
	<dt>int index
	<dd>アドレスブックにいる相手のインデックス。
	<dt>int petindex
	<dd>何番目のペットで送信するか
	<dt>int itemindex
	<dd>何番目のアイテムを送信するか。
	<dt>string message
	<dd>相手に送信する信息。EUCでサーバーに送信する。この文
	    字列はデリミタをふくまないので、エスケープする必要はない。
	<dt>int color
	<dd>信息の<a href="#coloring">色</a>。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_PMSG_send( int fd,int index,int petindex,int itemindex,char* message,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PMSG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( petindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( itemindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_DAB"><font color=red>clienttoserver DAB( int index );</font></a><br>
      (DeleteAddressBookitem)アドレスブックの项目を削除する。このコマ
      ンドによってアドレスブックの内容が变化するので、サーバーはABコマ
      ンドを送信して、クライアントに表示されているアドレスブックの内容
      を更新する必要がある。
      <dl>
	<dt>int index
	<dd>消したい项目のindex.最初の项目が0である。
      </dl>
      サーバーがこのコマンドのために特に返答をすることはない。<br>
      <br>
      <hr>
      <br>

*/

void nrproto_DAB_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_DAB] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_AAB"><font color=red>clienttoserver AAB( int x, int y );</font></a><br>
      (AddAddressBookitem)
      目の前にいるキャラクターをアドレスブックに加える。
      成功するとアドレスブックの内容が变化するので、
      サーバーはABコマンドを送信してクライアントの表示を更新する。
      <dl>
        <dt>int x,y
        <dd>自分のｘ，ｙ座标
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_AAB_send( int fd,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_AAB] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_GI"><font color=red>clienttoserver GI( int index, string data );</font></a><br>
      (GuildInfomation)
      サーバーに变更する家族情报を送る。
      <dl>
	<dt>int index
	<dd>变更する家族情报のインデックス。番号は SC_GI のビット番号に准据。
	    更新できるのは家族名と家族ルーム名、ソートの种类だけである。
	<dt>string data
	<dd>情报の详细。家族名と家族ルーム名はテキストデータ（エスケープすること）が送られる。
	    ソートの种类の场合、数字が一文字だけ入ることになる。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GI_send( int fd,int index,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_GT"><font color=red>clienttoserver GT( int index, int bitflag, string data );</font></a><br>
      (GuildTitle)
      サーバーに新规の、または变更した家族称号を送る。
      <dl>
	<dt>int index
	<dd>家族称号の番号。新规に设定するときには－１を、变更するときには
	变更するインデックス番号を入れる。
	<dt>int bitflag
	<dd>この家族称号が持つ权限。０～４ビットのみ使用される。この数字が 0xff のときは、
	    该当する家族称号を削除する。
	<dt>string data
	<dd>家族称号の文字列。<a href="#escaping">エスケープ</a>されている。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GT_send( int fd,int index,int bitflag,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( bitflag ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_GMR"><font color=red>clienttoserver GMR( int time );</font></a><br>
      サーバーに家族成员の情报を送るように要求する。
      クライアントはサーバーに以前情报を受け取った时间を送る。
      サーバーはそれから情报が更新されているときにのみ、データを送信する。
      <dl>
	<dt>int time
	<dd>クライアントが家族成员情报を受け取ったときのサーバー时间
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GMR_send( int fd,int time )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GMR] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( time ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_BGT"><font color=red>clienttoserver BGT( int index, int titleID );</font></a><br>
      (BestowGuildTitle)<br>
      メンバーに家族称号を与える。家族称号を与えることが出来るのは、家族マスターのみ。
      <dl>
        <dt>int index
        <dd>家族称号を与えるメンバーの管理番号。
        <dt>int titleID
        <dd>与える家族称号のＩＤ番号。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_BGT_send( int fd,int index,int titleID )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_BGT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( titleID ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_AGM"><font color=red>clienttoserver AGM( int x, int y );</font></a><br>
      (AddtoGuildMember)<br>
      目の前にいるキャラクターを家族成员に加える。
      <dl>
        <dt>int x,y
        <dd>自分のｘ，ｙ座标。目の前に复数キャラクタがいる场合は、ウィンドウで选择となる。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_AGM_send( int fd,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_AGM] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_LG"><font color=red>clienttoserver LG( int index );</font></a><br>
      (LeavefromGuild)
      家族成员から外れる。外れる场合は、自分が所属している家族のインデックス番号を送信する。
      <dl>
	<dt>int index
	<dd>家族のインデックス番号。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_LG_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_LG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_RGM"><font color=red>clienttoserver RGM( int index );</font></a><br>
      (RemovefromGuildMember)
      指定された番号の家族成员を、家族成员リストからはずす。
      <dl>
	<dt>int index
	<dd>はずしたいキャラクタの、家族成员管理番号。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_RGM_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_RGM] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_GML"><font color=red>clienttoserver GML( int index, string  message, int color );<br></font></a>
      (GuildMaiL)
      家族成员に信息を送信。indexは家族成员のインデックス。<br>
      <br>
      <dl>
	<dt>int index
	<dd>送信先の家族成员の管理番号。
	    群邮件の场合、10Bit(=0x400)を立てて送信すること。<br>
	    この场合、下位の 8Bit で指定されるタイトルＩＤを与えられた家族成员に信息を送信する。
	<dt>string message
	<dd>相手に送信する信息。EUCでサーバーに送信する。この文
	    字列はデリミタをふくまないので、エスケープする必要はない。
	<dt>int color
	<dd>信息の<a href="#coloring">色</a>。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GML_send( int fd,int index,char* message,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GML] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PGML"><font color=red>clienttoserver PGML( int index, int petindex, int itemindex, string  message, int color );<br></font></a>
      (PetGuildMaiL)
      家族成员にペットで信息を送信。アイテムも送信できる。indexは家族成员のインデックス。<br>
      <br>
      <dl>
	<dt>int index
	<dd>送信先の家族成员の管理番号。
	    群邮件の场合、10Bit(=0x400)を立てて送信すること。<br>
	    この时、下位の 8Bit が０の场合全员に、１の场合ONライン状态の相手全员に送信される。<br>
	    9Bit(=0x200)が立っている场合は、タイトルＩＤ别の群邮件となり、<br>
	    下位 8Bit のタイトルＩＤ番号を持つ全员に信息が送信される。
	<dt>int petindex
	<dd>何番目のペットで送信するか
	<dt>int itemindex
	<dd>何番目のアイテムを送信するか。
	<dt>string message
	<dd>相手に送信する信息。EUCでサーバーに送信する。この文
	    字列はデリミタをふくまないので、エスケープする必要はない。
	<dt>int color
	<dd>信息の<a href="#coloring">色</a>。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_PGML_send( int fd,int index,int petindex,int itemindex,char* message,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PGML] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( petindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( itemindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_GD"><font color=red>clienttoserver GD( int index );</font></a><br>
      (GuildDissolution)
      家族を解散する时に送信する。
      <dl>
	<dt>int index
	<dd>家族のインデックス番号。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GD_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PRV"><font color=red>clienttoserver PRV( int flg);</font><br></a>
  (ProfileViersion)
  最新バージョンのプロフィールファイルを取得する
	<dl>
	<dt>int flg
	<dd> リストファイルのバージョン	<br>	サーバから送信されたバージョンが、クライアントで保持しているバージョンと异なる场合のみ送信
	<br>
	<br>
	<dd><table border=1>
	    <tr><td>0 bit</td><td>0: 卖买リスト要求无し            </td><td>1: 卖买リスト要求有</td></tr>
	    <tr><td>1 bit</td><td>0: ABOUTリスト要求无し          </td><td>1: ABOUTリスト要求有 </td></tr>
	</table>

	</dl>

	<br>
	<hr>
	<br>

*/

void nrproto_PRV_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PRV] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PRS"><font color=red>clienttoserver PRS(int sid,string smsg,int bid,string bmsg,int aid,string amsg,string pmsg);</font><br></a>
  (ProfileList)
  プロフィール情报の设定要求を行う
	<dl>
	<br>
	<br>
	</dl>
	<dd><table border=1>
	    <tr><td>卖物ID</td><td>卖物の分类ID</td><td>0:指定无し 1:剑 2:斧 </td></tr>
	    <tr><td>卖物MSG</td><td>卖物の信息</td><td>&nbsp;</td></tr>
	    <tr><td>买物ID</td><td>买物の分类ID</td><td>0:指定无し 1:剑 2:斧 </td></tr>
	    <tr><td>买物MSG</td><td>买物の信息</td><td>&nbsp;</td></tr>
	    <tr><td>AboutID</td><td>Aboutの分类ID</td><td>0:指定无し 1:冒険仲间 .. </td></tr>
	    <tr><td>About信息</td><td>その他信息</td><td>　</td</tr>
	    <tr><td>プロフィール信息</td><td>プロフィール信息</td><td>　</td</tr>
	</table>
	<br>
	<hr>
	<br>


*/

void nrproto_PRS_send( int fd,int sid,char* smsg,int bid,char* bmsg,int aid,char* amsg,char* pmsg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PRS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( sid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( smsg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( bid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( bmsg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( aid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( amsg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( pmsg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PRD"><font color=red>clienttoserver PRD(int flg, int index);</font><br></a>
  (ProfileViersion)
  プロフィールの阅覧要求を行う
	<dl> 
	<dd><table border=1>
	    <tr><td>flg</td><td>要求元のデータ种类</td><td>0:アドレスブック 1:家族ブック 2:プロフィールリスト </td></tr>
	    <tr><td>index</td><td>各アドレスブックのインデックス</td><td>&nbsp;</td</tr>
	</table>
	<br>
	<br>
	</dl>
	<br>
	<hr>
	<br>


*/

void nrproto_PRD_send( int fd,int flg,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PRD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
 <LI><a name="CS_PRE"><font color=red>clienttoserver PRE(int index, string name );</font><br></a>
	プロフィール用名刺栏の削除要求を行う
	<dl>
	<dd>
	<dd><table border=1>
	    <tr><td>index</td><td>削除对象者のプロフィール管理番号 </td></tr>
	    <tr><td>name</td><td>削除对象者の名称</td</tr>
	</table>
	<br>
	<br>
	</dl>
	<br>
	<hr>
	<br>

*/

void nrproto_PRE_send( int fd,int index,char* name )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PRE] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( name ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PRM"><font color=red>clienttoserver PRM(string data, int color );<br></font></a>
      プロフィールリストのメンバーに信息を送信。<br>
      <br>
      <dl>
	<dt>string data
	<dd>信息の内容。index|name|msg となっていて、最初のトークンには送信相手のプロフィール管理番号の
		インデックスが入っている。次は送信相手の名称。次が实际の信息内容になっている。
		なお、nameはindexと并せ、サーバ侧での送信相手の特定に用いられる
	<dt>int color
	<dd>信息の<a href="#coloring">色</a>。
      </dl>
      <br>
      <hr>
      <br>


*/

void nrproto_PRM_send( int fd,char* data,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PRM] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PRO"><font color=red>clienttoserver PRO( int flg);</font><br></a>
  (ProfileList)
  プロフィール情报の公开要求
	<dl>
	<br>
	<br>
	</dl>
	<dd><table border=1>
	    <tr><td rowspan="2">flg</td><td>0</td><td>非公开</td></tr>
	    <tr><td>1</td><td>公开</td></tr>
	</table>
	<br>
	<hr>
	<br>




*/

void nrproto_PRO_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PRO] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_L"><font color=red>clienttoserver L( int dir);</font></a><br>
      (Look)
      dir は方向。その方向を见る。见るとサーバでは直接见るイベントを発
      生させる。クライアントでは、これ专用のボタンが用意されるなどして、
      どんなキャラクタでも、いつでも、このコマンドを使うことができるよ
      うにしておく。この操作は、宝箱をあける、ドアをあける、看板をみる、
      目の前をしらべる(透明なNPCにたいして)、など、目の前にいるNPCに对
      してアクセスする方法を提供するのが目的である。见たけっか、NPCが
      ある动作をするが、その结果を文字列でクライアントに知らせるには、
      TKコマンドをつかって送信する。<br>
      <br>
      <hr>
      <br>

*/

void nrproto_L_send( int fd,int dir )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_L] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dir ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_TK"><font color=red>clienttoserver TK( int x, int y, string message ,int color, int area, int fontsize);<br></font></a>
      (TalK)
      チャット用信息を送信する。返答はない。<br><br>
      <dl>
	<dt>int x,y
	<dd>自分のｘ，ｙ座标
	<dt>string message
	<dd>string の内容は, "|" で2つに区切られている。ひとつめのトー
	    クンの文字によって2つめのトークンの内容が变更される。2つめ
	    のトークンは<a href="#escaping">エスケープ</a>されている。
	    ひとつめのトークンは以下のパターンがある。
	    <ul>
	      <li>Pの时<br>
		  先头にC文字列の"P"がつく场合、それはプレイヤーが入
		  力したチャット信息である。クライアントプログラ
		  ムは、文字列の先头にかならずこの文字とデリミタをつ
		  けくわえて送信する。サーバーは文字列の先头に"P"を
		  检出したら声の当たり判定をして、闻こえる范围に送信す
		  る。そのときはTKコマンドをつかうが、TKコマンドで送信す
		  る文字列にはこの"P"をふくめてそのまま入れる。
		  <br>
		  文字列の内容の例：
		  <pre>
		  "P|今天天气不错!"
		  </pre>
		  <br>
		  チャット信息を送信したときに闻こえる范围を指定
		  するには、语尾に以下の文字列があるかをサーバが判定す
		  る。文字列の表记はCにおける表现である。'!' は全角文
		  字で入力される场合がままあるが、サーバは'!'だけに反
		  应する。クライアントプログラムが全角文字を半角文字に
		  なおして送信する。”!”一つおきにプレイヤーを中心に
		  １マス闻こえる范围が增える。
		  <table border>
		    <tr><td>"..."</td><td>话したキャラクタと自分だけ
		    </td></tr>
		  </table>
	      </ul>
	  <dt>int color
	  <dd>文字列の<a href="#coloring">色</a>。
	  <dt>int area
	  <dd>话した言叶が闻こえる范围（１～を指定する。）
	  <dt>int fontsize
	  <dd>话したフォントの大きさを指定する。0：普通 1：大きい 2；小さい
	</dl>
      <br>
      <hr>
      <br>

*/

void nrproto_TK_send( int fd,int x,int y,char* message,int color,int area,int fontsize )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TK] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( area ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fontsize ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_FS"><font color=red>clienttoserver FS( int flg);<br></font></a>
      (FlgSet)仲间を受け入れるか，DUELＯＫのフラグ，チャットモード，名刺交换のモードをOn/Offする。<br>
      <br>
      <dl>
	<dt>int flg
	<dd><table border=1>
	    <tr><td>0 bit</td><td>0: 仲间Off            </td><td>1: 仲间On</td></tr>
	    <tr><td>1 bit（现在未使用）</td><td>0: 战闘途中参加Off          </td><td>1: 战闘途中参加On </td></tr>
	    <tr><td>2 bit</td><td>0: DUEL Off           </td><td>1: DUEL On</td></tr>
	    <tr><td>3 bit</td><td>0: 通常チャットモード </td><td>1: 布ティチャットモード</td></tr>
	    <tr><td>4 bit</td><td>0: 名刺交换OK         </td><td>1: 名刺交换拒否</td></tr>
	    <tr><td>5 bit</td><td>0: トレードOK         </td><td>1: トレード拒否</td></tr>
	    <tr><td>6 bit</td><td>0: 家族加入OK       </td><td>1: 家族加入拒否</td></tr>
	</table>
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_FS_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_FS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_HL"><font color=red>clienttoserver HL( int flg);<br></font></a>
      (HeLp)お助けモードを设定する。これをONにすると，他の人が战闘に参加する事が出来る。<br><br>
      <dl>
	<dt>int flg
	<dd> 0: お助けモードOff                  1: お助けモードOn<br>
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_HL_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_HL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_PR"><font color=red>clienttoserver PR( int x, int y,int request);<br></font></a>
      (PartyRequest)仲间に入るor除队 を要求する。<br>
      <br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int request
	<dd>0: 除队する                          1: 仲间にしてくれ
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_PR_send( int fd,int x,int y,int request )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PR] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( request ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_KS"><font color=red>clienttoserver KS( int pet1, int pet2, int pet3, int pet4, int pet5);<br></font></a>
      (Kyoryu Select)ペットの战闘设定。<br>
      <br>
      <dl>
	<dt>int pet1 ???? pet5 
	<dd>ペット1～5の战闘设定。以下の值を取る。<br><br><br>
	  <table border="1">
	    <tr><td>0</td><td>休み(战闘に参加する事が出来ない</td></tr>
	    <tr><td>1</td><td>待机(战闘参加可能)</td></tr>
	    <tr><td>2</td><td>战闘に最初に出阵するペット</td></tr>
	  </table>
	  <br>
	  これに加えて、连れ步くペットをそれぞれの值に 0x10 を加えることで指定する。<br>
	  0は1～5匹、1は0～3匹(战闘参加ペットがいた场合は2匹迄)、2は0～1匹まで设定できる（连れ步けるペットは0～1匹）。<br>
	  一应クライアントでもチェックすること。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_KS_send( int fd,int pet1,int pet2,int pet3,int pet4,int pet5 )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_KS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pet1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pet2 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pet3 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pet4 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pet5 ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_MP"><font color=red>clienttoserver MP( int fromindex, int toindex );<br></font></a>
      (Move Pet)ペットの入れ替え。これに对する应答はKPで行われる。<br>
      <br>
      <dl>
	<dt>int fromindex
	<dd>ペットの移动元番号。
	<dt>int toindex
	<dd>ペットの移动先番号。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_MP_send( int fd,int fromindex,int toindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_MP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fromindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_GFL"><font color=red>clienttoserver GFL( int index);<br></font></a>
      (Get Frond List)<br>
      目の前キャラのリスト要求。主にオペレーション分类ーCの为に使う。
      最高９キャラ分が送られるが、仲间がいればその人数分少なく送られる。<br>
      <br>
      <dl>
	<dt>int index
	<dd>何番目のスキルを使用しようとしたか（变身、变装用のため）
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GFL_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GFL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_GPD"><font color=red>clienttoserver GPD( int index);<br></font></a>
      (Get Pet Data)<br>
      指定されたキャラと持っているペットの情报を要求する。<br>
      <br>
	<dt>int index
	<dd>何番目か。0:自分。1 ～ (? GFLを受け取った时の仲间の数の分)  ～ GFLで受け取ったデータのindexと続く。<br>
	<br>
	たとえば、GFLを要求した时、目の前にいるペットかプレイヤーはaho|bakaと返答があったとする。その时の、仲间は
	hogeとfooであった。<br>
	その时にahoの情报を知りたい时は、
	index を3で送信する。(自分(0) hoge(1) foo(2) aho(3) baka(4))<br>
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_GPD_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GPD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_GFLI"><font color=red>clienttoserver GFLI( void);<br></font></a>
      (Get Frond List Item)<br>
      目の前キャラのリスト要求。主にオペレーション分类ーCの为に使う。
      最高９キャラ分が送られるが、仲间がいればその人数分少なく送られる。<br>
      <br>
      <dl>
	引数 : なし<br>
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_GFLI_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GFLI] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_GPDI"><font color=red>clienttoserver GPDI( int index);<br></font></a>
      (Get Pet Data Item)<br>
      指定されたキャラと持っているペットの情报を要求する。<br>
      <br>
	<dt>int index
	<dd>何番目か。0:自分。1 ～ (? GFLを受け取った时の仲间の数の分)  ～ GFLで受け取ったデータのindexと続く。<br>
	<br>
	たとえば、GFLを要求した时、目の前にいるペットかプレイヤーはaho|bakaと返答があったとする。その时の、仲间は
	hogeとfooであった。<br>
	その时にahoの情报を知りたい时は、
	index を3で送信する。(自分(0) hoge(1) foo(2) aho(3) baka(4))<br>
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_GPDI_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_GPDI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_IH"><font color=red>clienttoserver IH( int index,int hankoindex);<br></font></a>
      (Item Hanko)<br>
      アイテムハンコを使った<br>
      <br>
	<dt>int index
	<dd>何番目のアイテムか<br>
	<dt>int index
	<dd>ハンコアイテム何番か<br>
	<br>
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_IH_send( int fd,int index,int hankoindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_IH] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( hankoindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_AC"><font color=red>clienttoserver AC( int x, int y, int actionno);<br></font></a>
      (ACtion)喜怒哀乐などを表现する。<br>
      <br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int actionno
	<dd>プレイヤーのとりたいアクションを送信する。サーバーはこれを受けとるとそのまま周りにアクション(CA)を送信する。
	    もしかしたら连打制御等をしないといけないかもしれない。番号とアクションの对应は，
	    <a href="#SC_CA">ＣＡ</a>の番号と同じだが，送っても处理されないモノがある。<br>
	    Battle,Leader,Watch,NameColor,Turn,Warp,Stand,Walkは送っても处理されない。
	    Stand, Walkは，代わりにActionStand,ActionWalkを使用する。<br>
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_AC_send( int fd,int x,int y,int actionno )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_AC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( actionno ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_ACS"><font color=red>clienttoserver ACS( int x, int y, int skillno);<br></font></a>
      (ACtionSkill)スキル使用中のアイコン表示。返答は（周围への）<a href="#SC_CA">ＣＡ</a>で行われる。<br>
      <br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int skillno
	<dd>プレイヤーがスキルを使用する际の、头の上に表示するアイコンの番号。<br>
	    この番号に０を入れると、アイコン表示を消去する。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_ACS_send( int fd,int x,int y,int skillno )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ACS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( skillno ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_MU"><font color=red>clienttoserver MU( int x, int y, int array, int toindex);<br></font></a>
      (magic use)魔法を使う。ほぼフィールド上からしか呼ばれない。バトル中は别プロトコルにて魔法使用の
      プロトコルが飞んでくる。<br>
      <br>
      <dl>
	<dt>int x,y
	<dd> 自分のx,y 座标
	<dt>int array
	<dd> どの魔法を使ったか。この数字は P の Jn(n は任意の数字)に对应しなければいけない。
	<dt>int toindex
	<dd> 谁に魔法を使用したか。これはオブジェクトやキャラのindexではない。以下の样になっている。
	<pre>
	  自分    = 0
	  ペット  = 1 ～5
	  仲间    = 6 ～10 （S N の0～4に对应。自分自身も含まれている）
	</pre>
	对象が全员，とか分からない，とかの场合は-1で送信する。<br>
	<br>
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_MU_send( int fd,int x,int y,int array,int toindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_MU] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( array ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_TU"><font color=red>clienttoserver TU( int haveskillindex, int havetechindex, int toindex, string data);<br></font></a>
      (Technique use )プレイヤーが技を使用する。ペットの技についてはPSを使用する事。<br>
      <br>
      <dl>
	<dt>int haveskillindex
	<dd>何番目のスキルを使用したか（トランス使用时は+100される）。
	<dt>int techindex
	<dd>何番目の技を使用したか。
	<dt>int toindex
	<dd> 何に对して技を使用したか。<br>
		オペレーション分类ーAでは何を入れていても无视される。<br>
		オペレーション分类ーBでは对象のアイテムの场所が入っている。<br>
		オペレーション分类ーCでは何番目のキャラに技を使用したか。
		<a href="#SC_GFL">GFL</a>または<a href="#SC_GPD">GPD</a>で贳ったキャラのリストの番号。<br>
		GFLのindexはGPDの时と同じように注意すること。<br>
		<br>
		---- begin comment out ----<br>
		谁に魔法を使用したか。これはオブジェクトやキャラのindexではない。以下の样になっている。
			<pre>
				  自分    = 0
			  	  ペット  = 1 ～5
			  	  仲间    = 6 ～10 （S N の0～4に对应。自分自身も含まれている）
		  	</pre>
				对象が全员，とか分からない，とかの场合は-1で送信する。<br>
		---- end comment out ----<br>
	<br>
	<dt>string data
	<dd>付加情报。他にサーバーが情报を必要な时に使用する。
	    アイテム合成の技の场合は，recipe|haveitemindex1|haveitemindex2|haveitemindex3|...<br>
	    と，合成するアイテムの番号が入っている。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_TU_send( int fd,int haveskillindex,int havetechindex,int toindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TU] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( haveskillindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havetechindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_TRPL"><font color=red>clienttoserver TRPL( void );<br></font></a>
      (TRade Player List ) トレード可能なプレイヤーのリストをクライアントが要求する。<br>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_TRPL_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TRPL] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_TRS"><font color=red>clienttoserver TRS( int playernum );<br></font></a>
      (TRade Start ) トレード可能なプレイヤーのリストを选んで、トレードを开始する。<br>
      <br>
      <dl>
	<dt>int playernum
	<dd>TRPLで贳ったプレイヤーリストの何番目を选んだか。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_TRS_send( int fd,int playernum )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TRS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( playernum ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_TROP"><font color=red>clienttoserver TROP( string items, string pets, int gold);<br></font></a>
      (TRade Open )トレードしたいアイテムなどのリストをサーバーに送信する。クライアントではOPENボタンを押した瞬间など。<a href="#CS_TROC">TROC</a>などを送った后、これを再送する时などでも、いつでも全ての分のリストを送らないといけない。<br>
      <dl>
	<dt>string items
	<dd>取り引きしたいアイテムの、持っている场所とクライアント侧でのウインドウ位置。デリミタ(|)でくくり、１つを｜アイテムの场所｜ウインドウでの位置｜として复数个送信出来る。无い时は长さ０の文字列。
	<dt>string pets
	<dd>取り引きしたいペットの持っている场所とクライアント侧でのウインドウ位置。デリミタ(|)でくくり、１つを｜アイテムの场所｜ウインドウでの位置｜として复数个送信出来る。无い时は长さ０の文字列。
	<dt>int gold
	<dd>取り引きしたいGold。无い时は0。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_TROP_send( int fd,char* items,char* pets,int gold )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TROP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( items ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( pets ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( gold ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_TRCL"><font color=red>clienttoserver TRCL( void);<br></font></a>
      (TRade Close) トレードの、アイテム选择しなおしをしたりした时にクライアントが送信する。
      トレード相手にはCloseと表示される。この时は自动的にTradeOFFとなる。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_TRCL_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TRCL] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_TROC"><font color=red>clienttoserver TROC( int flg);<br></font></a>
      (TRade Ok/Cancel) トレード承诺／キャンセル
      <br>
      <dl>
	<dt>int flg
	<dd>トレードの内容にユーザーが了承し、Tradeボタンが押された时に、TRUEで送信。
	ユーザー间でこれが两方ともTRUEで成立した瞬间、取り引きが発生し、終了する。
	cancelは、ウィンドウを闭じたりした时に発生する。クライアントはウィンドウを闭じなければならない。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_TROC_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_TROC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <li><a name="CS_PS"><font color=red>clienttoserver PS( int havepetindex, int havepetskill, int toindex, string data);<br></font></a>
      (PetSkill use )ペットの技を使う。フィールド上からしか呼ばれない。
      <dl>
	<dt>int havepetindex
	<dd>何番目のペットが使用したか。
	<dt>int havepetskill
	<dd>何番目の技を使用したか。
	<dt>int toindex
	<dd> 谁に魔法を使用したか。これはオブジェクトやキャラのindexではない。以下の样になっている。
	<pre>
	  自分    = 0
	  ペット  = 1 ～5
	  仲间    = 6 ～10 （S N の0～4に对应。自分自身も含まれている）
	</pre>
	对象が全员，とか分からない，とかの场合は-1で送信する。<br>
	<br>
	<dt>string data
	<dd>付加情报。アイテム合成の技の场合は，haveitemindex1|haveitemindex2|haveitemindex3|...<br>
	    と，合成するアイテムの番号が入っている。
      </dl>
      <br>
      <hr>
      <br>

*/

void nrproto_PS_send( int fd,int havepetindex,int havepetskill,int toindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havepetindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havepetskill ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_ST"><font color=red>clienttoserver ST( int titleindex );<br></font></a>
      (SelectTitle)
      称号をえらぶ。titleindex が -1 のときは称号をはずすという事にな
      る。<br><br>
      <dl>
	<dt>int titleindex
	<dd>何番目の称号か。
      </dl>
      结果は、Txt关数をつかって、「～のしょうごうをえらんだ！」のよう
      に通知される。
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_ST_send( int fd,int titleindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ST] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( titleindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_DT"><font color=red>clienttoserver DT( int titleindex );<br></font></a>
      (DeleteTitle)
      称号を削除する。<br><br>
      <dl>
	<dt>int titleindex
	<dd>削除する称号のインデックス。
      </dl>
      <br>
      结果は、Txt关数をつかって、「～のしょうごうをさくじょした！」のように通
      知される。<br>
      <br>
      <hr>
      <br>

*/

void nrproto_DT_send( int fd,int titleindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_DT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( titleindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_FT"><font color=red>clienttoserver FT( string data );<br></font></a>
      (inputFreeTitle)
      自分で入力する称号を入力した。<br><br>
      <dl>
	<dt>string data
	<dd>自分で入力した称号。EUCでサーバーに送信される。この称号は、
	    マウスカーソルをあわせたときに画面下の1行infoに表示され
	    るものである。この文字列はデリミタをふくまないので、
	    エスケープする必要はない。
      </dl>
      <br>
      この结果は、Txt关数を使って、文字列で通知される。これにともない、
      自分のCHARINDEXに对して、Chデータがふたたび送信されてくることに
      なる。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_FT_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_FT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_LVUP"><font color=red>clienttoserver LVUP( int param );<br></font></a>
      (LevelUP)
      等级アップボタンをおした。<br><br>
      <dl>
	<dt>int param
	<dd>どのパラメータをあげるか。0=体力, 1=腕力, 2=丈夫さ, 3=素早さ 4=魔力
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_LVUP_send( int fd,int param )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_LVUP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( param ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PLVUP"><font color=red>clienttoserver PLVUP(int pethave, int param );<br></font></a>
      (LevelUP)
      等级アップボタンをおした。（ペット用）<br>
      <br>
      <dl>
	<dt>int pethave
	<dd>どのペットか（0～4）
      </dl>
      <dl>
	<dt>int param
	<dd>どのパラメータをあげるか。0=体力, 1=腕力, 2=丈夫さ, 3=素早さ 4=魔力
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_PLVUP_send( int fd,int pethave,int param )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PLVUP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pethave ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( param ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_SKSW"><font color=red>clienttoserver SKSW( int srcindex, int dstindex );<br></font></a>
      (SKillSWap)
      スキルの顺番を入れ替える。选择したスキルより一つ新しいものと顺番を入れ替える。<br><br>
      <dl>
	<dt>int srcindex
	<dd>どのスキルの场所を入れ替えるか。これはサーバーが覚えてる场所を指定する。クライアントで表示する顺番では无い。
	<dt>int dstindex
	<dd>スキルを入れ替える先の场所。srcindexと同样に、サーバー侧が覚えている场所を指定する。<br>このデータが-1の时は手前のスキルと入れ替える（以前のSKSWと同样の动作）。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_SKSW_send( int fd,int srcindex,int dstindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_SKSW] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( srcindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dstindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PSSW"><font color=red>clienttoserver PSSW( int havepetindex, int srcindex, int dstindex );<br></font></a>
      (PetSkillSWap)
      ペットスキルの顺番を入れ替える。<br><br>
      <dl>
	<dt>int havepetindex
	<dd>どのペットのスキルを入れ替えるか。
	<dt>int srcindex
	<dd>どのスキルの场所を入れ替えるか。これはサーバーが覚えてる场所を指定する。クライアントで表示する顺番では无い。
	<dt>int dstindex
	<dd>スキルを入れ替える先の场所。srcindexと同样に、サーバー侧が覚えている场所を指定する。<br>このデータが-1の时は手前のスキルと入れ替える（以前のSKSWと同样の动作）。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_PSSW_send( int fd,int havepetindex,int srcindex,int dstindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PSSW] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havepetindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( srcindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dstindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_POS"><font color=red>clienttoserver POS(void  );<br></font></a>
      (POSition)
      前卫后卫ボタンを押した。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_POS_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_POS] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_KN"><font color=red>clienttoserver KN( int havepetindex, string data );<br></font></a>
      (inputKyoryuName)
      ペットの名称を入力した。<br>
      <br>
      <dl>
	<dt>int havepetindex
	<dd>ペットの番号。
	<dt>string data
	<dd>自分のペットにオリジナルの名称を与える。
      </dl>
      この结果は、Txt关数を使って、文字列で通知される。これにともない、
      ペットの状态が再び送られてくる。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_KN_send( int fd,int havepetindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_KN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havepetindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_WN"><font color=red>clienttoserver WN( int x, int y, int seqno, int objindex, int select, string data );<br></font></a>
      <br>
      ウィンドウ表示に对する返答<br>
      <br>
      <dl>
	<dt>int x,y
	<dd>キャラクターのx,y座标
	<dt>int seqno
	<dd>サーバーから来たseqnoをそのまま返す
	<dt>int objindex
	<dd>サーバーから来たobjindexをそのまま返す
	<dt>int select
	<dd>どのボタンを选择した，または何を选择したか。サーバーのWN のbuttontype のdefineと同じ形式で返す。
	<dt>string data
	<dd>１行入力ウィンドウがあれば，その入力したデータ。<br>
	    选择ウィンドウの场合は，その选择した番号。（1から）<br>
			削除ボタンが复数あるような场合は(伝言板など)では、その番号(０から)<br>
	    また，ペット，仲间选择ウィンドウでは，ペットが１～５，仲间が６～１０となる。
	    <br><br>??お店の场合??<br>
	    	买の时：选择したID|个数|...<br>
	    	卖の时：选择したアイテム栏の番号（１～？）|金额<br>
		その他	：买：1　卖：2　出：3
	<br><br><br>??ペットの技の场合??<br>
		どの技をえらんだか？(选择番号)｜どのペットか？(选择番号)｜どのスロットか？(选择番号)｜值段<br>

	<br><br><br>??NPC检索の场合??<br>
		分类ID｜アイテムのID｜检索条件文字列<br><br>
	　??分类ID<br>
	　　　　检索の分类<br>
	　　　　　　0:Sell  1:Buy  2:About <br><br>
	　??アイテムID<br>
	　　　　检索アイテムのID<br>
	　　　　　　0:未设定 1:剑  2:枪 ....<br>
	　　　　　　0:未设定 1:冒険仲间  2:家族 ....<br><br>
	　??检索条件文字列<br>
	　　　　检索条件<br>

	<br><br><br>??NPC检索のプロフィール表示の场合??<br>
	ユーザ识别子|ユーザ名<br>
	　??ユーザ识别子<br>
	　　　　サーバ侧での管理に使う识别子<br><br>
	　??ユーザ名<br>
	　　　　检索对象ユーザの名称。ユーザ识别子と并せてサーバ侧でのユーザ特定に使う<br><br>


      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

void nrproto_WN_send( int fd,int x,int y,int seqno,int objindex,int select,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_WN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seqno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( objindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( select ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_SP"><font color=red>clienttoserver SP( int x, int y, int dir );<br></font></a>
      (SetPosition)<br>
      クライアントが自分の场所を报告する。
      サーバーはこれに合わせて座标を更新する。<br>
      <br>
      <dl>
	<dt>int x,y, dir
	<dd>座标,むき。
      </dl>
      <br>
      <hr>
      <br>
      <!-- ここから下はキャラ作成などゲーム外の关数   -->

*/

void nrproto_SP_send( int fd,int x,int y,int dir )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_SP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dir ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CLIENTLOGIN"><font color=red>clienttoserver ClientLogin( string acountid, string passwd, string cdkey );<br></font></a>
      クライアントがサーバーにログインするときに使う。つまりアカウント
      のログインと考えればよい。ゲーム内容にタッチする种类の关数は、す
      べてこのログインをしないと实行されないようになっている。この关数
      はただ单にクライアントの情报をゲームサーバにたくわえるためだけの
      物である。また、パスワード变更したら、再度この关数を呼ぶ事。<br>
      <br>
      <dl>
	<dt>string acountid
	<dd>课金のＩＤ。ユーザーが课金する时につかうＩＤ。ゲーム内部ではつかわない。
	<dt>string passwd
	<dd>↓のアカウントＩＤに对するパスワード。この文字列はエスケープされない。
	<dt>string cdkey
	<dd>CDKEY。ゲームの１ユーザーに对应するもの。ゲーム内部でユーザーを识别するために使う。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_ClientLogin_send( int fd,char* acountid,char* passwd,char* cdkey )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ClientLogin] );
#ifdef VERSION_TW
	//等待补齐.多出的两个参数为cg特征码和MAC地址(或完善为玩家硬件特征)
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string("d43d504b83f30f4a774c5e42b3ac0e99"), nrproto.workbufsize); //MLHIDE
#ifndef VERSION_TW
	//台服已取消发送账号
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string(acountid), nrproto.workbufsize);
#endif
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string(passwd), nrproto.workbufsize);
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string(cdkey), nrproto.workbufsize);
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string("0013253e002a"), nrproto.workbufsize); //MLHIDE
#else
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string(acountid), nrproto.workbufsize);
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string(passwd), nrproto.workbufsize);
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string(cdkey), nrproto.workbufsize);
#endif
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CREATENEWCHAR"><font color=red>clienttoserver CreateNewChar(int dataplacenum, string charname,int imgno , int faceimgno, int vital, int str, int tgh, int quick, int magic, int earth, int water, int fire, int wind);<br></font></a>
      新しいキャラクターを作る。
      <br><br>       
      <dl>
	<dt>int dataplacenum
	<dd>キャラの番号。これによってクライアントはキャラクターリストのどの场所にこのキャラを表示すればいいかを判断する。
	<dt>string charname
	<dd>キャラ名(空白と改行がない、プレーンな文字列)。
	    この文字列はデリミタを使わないので、エスケープする必要はない。
	<dt>int imgno
	<dd>キャラクターの画像番号。
	<dt>int faceimgno
	<dd>颜の画像番号。名刺交换等に使用される。
	<dt>int vital, str,tgh, quick,magic
	<dd>各パラメータ。
	<dt>int earth, water, fire, wind
	<dd>各属性值
	<dt>string option
	</table>
	さらに、ゲーム内容的に、キャラ作成时に指定できるパラメータの值
	には限界がある。これは仕样书を参照すること。この文字列はエスケー
	プしない。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_CreateNewChar_send( int fd,int dataplacenum,char* charname,int imgno,int faceimgno,int vital,int str,int tgh,int quick,int magic,int earth,int water,int fire,int wind )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CreateNewChar] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dataplacenum ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( charname ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( imgno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( faceimgno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( vital ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( str ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( tgh ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( quick ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( magic ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( earth ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( water ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fire ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( wind ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CHARDELETE"><font color=red>clienttoserver CharDelete(int registnumber);<br></font></a>
      キャラを消す。<br>
      <br>
      <dl>
	<dt>int registnumber
	<dd>キャラの登録番号。charlistで取得したもの。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_CharDelete_send( int fd,int registnumber )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CharDelete] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( registnumber ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CHARLOGIN"><font color=red>clienttoserver CharLogin(int registnumber, int renewal, int face );<br></font></a>
      キャラクターがサーバーにログインするときに使う。<br>
      <br>
      <dl>
	<dt>int registnumber
	<dd>登録番号。
	<dt>int renewalflg
	<dd>リニューアルでログインするかどうか。
	<dt>int face
	<dd>颜絵の番号（０～２）。このトークンが无いときは旧クライアントからのアクセスと判定する。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_CharLogin_send( int fd,int registnumber,int renewal,int face )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CharLogin] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( registnumber ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( renewal ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( face ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CHARLIST"><font color=red>clienttoserver CharList( void );<br></font></a>
      キャラリストを得る。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_CharList_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CharList] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CHARLOGOUT"><font color=red>clienttoserver CharLogout(void);<br></font></a>
      キャラクターがサーバーから登出するときに使う。
      引数なし。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_CharLogout_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CharLogout] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PROCGET"><font color=red>clienttoserver ProcGet(void);<br></font></a>
      proc データを取得する。これによってログイン人数を调べたりする事が出来る。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_ProcGet_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_ProcGet] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PLAYERNUMGET"><font color=red>clienttoserver PlayerNumGet(void);<br></font></a>
      プレイヤーとログイン人数を取得する。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_PlayerNumGet_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PlayerNumGet] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_ECHO"><font color=red>clienttoserver Echo( string test);<br></font></a>
      サーバーが生きているかをテストしたり、テスト用に使う。<br>
      <br>
      <dl>
	<dt>string test
	<dd>エコー用文字列。エスケープしない。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_Echo_send( int fd,char* test )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_Echo] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( test ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_SHUTDOWN"><font color=red>clienttoserver Shutdown( string passwd, int min);<br></font></a>
      シャットダウン处理开始送信<br><br>
      メンテナンスを行いたい时に，自动的に「あとｎ分でメンテナンスを行います」の信息を１分每に
      流してくれる。最初にdenyaccept()を行うので，ユーザーはログイン出来なくなり，
      最后にcloseallsockets()を行ってキャラのセーブと强制登出を行う。<br>
      <br>
      <dl>
	<dt>string passwd
	<dd>やばいプロトコルの为，一应パスワードを设定しておく。
	<dt>int min
	<dd>「あとｎ分でメンテナンスを行います」のｎ分を设定する。
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_Shutdown_send( int fd,char* passwd,int min )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_Shutdown] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( passwd ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( min ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_FC"><font color=red>clienttoserver FC( void );<br></font></a>
	クライアントから定期的に送られてくるであろうプロトコル。サーバーで时间をチェックして予定外の时间に到着したプレイヤーは接続を切る。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_FC_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_FC] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CH"><font color=red>clienttoserver CH( int num );<br></font></a>
	クライアントからハック情报が送られてくる。<br>
      <br>
      <dl>
	<dt>int num
	<dd>ハック种类情报<br>
	<dd>0：マップ座标の变更通知
	<dd>1????
	<dd>2????
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_CH_send( int fd,int num )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CH] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( num ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_CHARLOGINGATE"><font color=red>clienttoserver CharLoginGate( void );<br></font></a>
      キャラクターが登入点に返回时に使う。<br>
      サーバーの返答は通常ワープで行われる。<br>
      <br>
      <hr>
      <br>
*/

void nrproto_CharLoginGate_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_CharLoginGate] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
/*
  <LI><a name="CS_PVUP"><font color=red>clienttoserver PVUP( int kind, string productkey );<br></font></a>
      パッケージのバージョンアップをサーバーに要求する。<br>
      <br>
      <dl>
	<dt>int kind
	<dd>バージョンアップの种类。<br>
	<dd>1:通常->EX<br>
	<dd>2:EX->Ver2<br>
	<dd>3:通常->Ver2<br>
	<dd>4:Ver2->PUK2 ????<br>
	<dt>string productkey
	<dd>バージョンアップに使用するプロダクトキー
      </dl>
      <br>
      <hr>
      <br>
*/

void nrproto_PVUP_send( int fd,int kind,char* productkey )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PacketVer][SEND_PVUP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( kind ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( productkey ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

#ifdef VERSION_TW
//台服发送生产采集技能所需的时间
void nrproto_ProduceTime_send(int fd, int time)
{
	nrproto_CreateHeader(nrproto.work, PacketSend[PacketVer][SEND_ProduceTime]);
	nrproto_strcatsafe(nrproto.work, nrproto_mkstr_int(time), nrproto.workbufsize);
	nrproto_Send(fd, nrproto.work);
}
#endif

int nrproto_ClientDispatchMessage(int fd ,char*line)
{
	int msgid;
	char funcname[1024];
	nrproto_strcpysafe( nrproto.work , line,nrproto.workbufsize );
	nrproto_splitString( nrproto.work);
	nrproto_GetMessageInfo( &msgid , funcname , sizeof(funcname),nrproto.token_list);
/*
  <LI><a name="SC_XYD"><font color=blue>servertoclient XYD( int x, int y, int dir );<br></font></a>
	主に战闘終了后にプレイヤーの位置を微调整するために使う。
      <br><br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_XYD] ) == 0 ){
		int x;
		int y;
		int dir;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		dir = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_XYD_recv( fd,x,y,dir);
		return 0;
	}
/*
  <LI><a name="SC_MC"><font color=blue>servertoclient MC(int id, int fl , int x1 , int y1 , int x2, int y2, int tilesum, int objsum, int eventsum);<br></font></a>
      マップを送る前にマップのチェックサムを送る。クライアントは自分のローカルのマップデータのチェックサムを计算し，
      これと比べて合っていなかったらMを送信する。合っている场合はMを送らずにクライアントのマップデータをそのまま使う。
      <dl>
	<dt>int id
	<dd>マップのID。フロア番号ではない！０～２。
	<dt>int fl
	<dd>フロア番号
	<dt>int x1
	<dd>左上X
	<dt>int y1
	<dd>左上Y
	<dt>int x2
	<dd>右下X
	<dt>int y2
	<dd>右下Y
	<dt>int timesum
	<dd>タイルのチェックサム
	<dt>int objsum
	<dd>オブジェクトのチェックサム
	<dt>int eventsum
	<dd>イベントオブジェクトのチェックサム。イベントオブジェクトはもしも重なっていたら１番上（マップリンクデータ上で）のものが使用される。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_MC] ) == 0 ){
		int id;
		int fl;
		int x1;
		int y1;
		int x2;
		int y2;
		int tilesum;
		int objsum;
		int eventsum;
		id = nrproto_demkstr_int( nrproto.token_list[1] );
		fl = nrproto_demkstr_int( nrproto.token_list[2] );
		x1 = nrproto_demkstr_int( nrproto.token_list[3] );
		y1 = nrproto_demkstr_int( nrproto.token_list[4] );
		x2 = nrproto_demkstr_int( nrproto.token_list[5] );
		y2 = nrproto_demkstr_int( nrproto.token_list[6] );
		tilesum = nrproto_demkstr_int( nrproto.token_list[7] );
		objsum = nrproto_demkstr_int( nrproto.token_list[8] );
		eventsum = nrproto_demkstr_int( nrproto.token_list[9] );
		nrproto_MC_recv( fd,id,fl,x1,y1,x2,y2,tilesum,objsum,eventsum);
		return 0;
	}
/*
  <LI><a name="SC_M"><font color=blue>servertoclient M(int id, int fl , int x1 , int y1 , int x2, int y2 ,string data );<br></font></a>
      マップを送信する。どんな矩形でも送信できる。もちろん最大サイズ
      はきまっていてよい。これはサーバーからのみ送信し、クライアント
      が要求することはない。送信のタイミングは、キャラにとってあたら
      しい部分が见えるようになった瞬间や、地形が变更された瞬间である。
      <br><br>
      <dl>
	<dt>int id
	<dd>マップID。以下の用に定义されている。
	<pre>
enum
{
	CHAR_MAPID_NORMAL,				// 通常マップ
	CHAR_MAPID_DUNGEON,				// 自动生成ダンジョン
	CHAR_MAPID_HOUSE,				// ????
	CHAR_MAPIDNUM,
};
	</pre>
	<dt>int fl
	<dd>キャラのいるフロア番号
	<dt>int x1
	<dd>フロアマップの中の絶对位置。左上X
	<dt>int y1
	<dd>フロアマップの中の絶对位置。左上Y
	<dt>int x2
	<dd>フロアマップの中の絶对位置。右下X
	<dt>int y2
	<dd>フロアマップの中の絶对位置。右下Y
	<dt>string data
	<dd>タイル|オブジェクト|イベントオブジェクト
	    になっている。
	    dataはマップタイル番号で"76,76,77,78,98,90,1,1,1,2"という
	    ように必要な要素数并んでいる。スペースでくぎるとエスケープ
	    の关系上、量がおおくなるので、コンマでくぎる。阶段などに进
	    んだ场合、全画面分を送信して、一步あるく场合は一步分だけお
	    くる。こういう判断はサーバーでやるしかない。クライアントは
	    この关数でマップをうけとると、それをディスクに保存して、オー
	    トマップ用の情报を蓄える。この文字列は<a
	    href="#escaping">エスケープ</a>する必要がある。
      	    もしもイベントオブジェクトが重なっていたら１番上（マップリンクデータ上で）のものが使用される。
      </dl>
      <br>
      <br>
      <hr>
      <br>


*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_M] ) == 0 ){
		int id;
		int fl;
		int x1;
		int y1;
		int x2;
		int y2;
		char* data;
		id = nrproto_demkstr_int( nrproto.token_list[1] );
		fl = nrproto_demkstr_int( nrproto.token_list[2] );
		x1 = nrproto_demkstr_int( nrproto.token_list[3] );
		y1 = nrproto_demkstr_int( nrproto.token_list[4] );
		x2 = nrproto_demkstr_int( nrproto.token_list[5] );
		y2 = nrproto_demkstr_int( nrproto.token_list[6] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[7] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[7] ));
		nrproto_M_recv( fd,id,fl,x1,y1,x2,y2,data);
		return 0;
	}
/*
  <LI><a name="SC_EV"><font color=blue>servertoclient EV(int seqno,int result);<br></font></a>
	クライアントのEVに对しての返答を行う。
	これを受け取るまでクライアントは再び步いたりすることが
	出来ない。
      <dl>
	<dt>int seqno
	<dd>クライアントEVのseqnoに对应する。
	<dt>int result
	<dd>结果。0:イベント实行不可。 1:成功。この后に色んなイベントに对する通信テキストが投げられてくるはず。
      </dL>	
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_EV] ) == 0 ){
		int seqno;
		int result;
		seqno = nrproto_demkstr_int( nrproto.token_list[1] );
		result = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_EV_recv( fd,seqno,result);
		return 0;
	}
/*
  <LI><a name="SC_EP"><font color=blue>servertoclient EP( int min, int max);<br></font></a>
	(Encount Probability)
	    エンカウント率を送信する。确率はn/100<br>
	    クライアントは，下限の值からスタートして，１步步く每に+1した确率でエンカウントするかを判断する。
	    ただし，上限の值を越えないようにする。
	    次回にこれが送られてくるまでこの确率でエンカウントを计算する事。
	<dl>
	  <dt>int min 
	  <dd>エンカウント确率（下限)
	  <dt>int max
	  <dd>エンカウント确率 (上限)
  	</dl>
  	<br>
  	<br>
  	<hr>
  	<br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_EP] ) == 0 ){
		int min;
		int max;
		min = nrproto_demkstr_int( nrproto.token_list[1] );
		max = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_EP_recv( fd,min,max);
		return 0;
	}
/*
  <LI><a name="SC_EN"><font color=blue>servertoclient EN(int result,int field);<br></font></a>
	クライアントのEN又はDUに对しての返答を行う。または，仲间歩き时に亲がエンカウントした场合に送られてくる。
      <dl>
	<dt>int result
	<dd>结果。0:エンカウント不可orエラー。 1:敌との战闘ＯＫ。2:对人との战闘ＯＫ。この后は战闘に关する通信	テキストが投げられてくるはず。
	<dt>int field
	<dd>バトルフィールド番号。
     </dL>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_EN] ) == 0 ){
		int result;
		int field;
		result = nrproto_demkstr_int( nrproto.token_list[1] );
		field = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_EN_recv( fd,result,field);
		return 0;
	}
/*
  <LI><a name="SC_RS"><font color=blue>servertoclient RS( string data );<br></font></a>
	战闘終了后、取得したＥＸＰ及びアイテムを通知。<br>
        RS|番号(10进)|等级アップフラグ(10进)|EXP(62进),番号|等级アップフラグ|EXP,??????,アイテムデータ|アイテムデータ|アイテムデータ,スキルＵＰフラグ|スキルＥＸＰ,????????<br><br>
　　　　番号～经验值は5人分。一人分の区切りは","。最初の一人目には必ずプレイヤーが来る。そのときの番号は-2。<br>
	2人目以降4人目まではペットの番号（0～4)ペットが经验值をもらわなかった场合は项目が无くなり、次の","が来る。<br>
	その次にアイテムのデータが"|"で区切られて送られてくる。アイテムデータは、<br>
	グラフィック番号|アイテム名<br>
	がエスケープされた状态で入っている（アイテム名はさらにエスケープされている）。最大３个で、无かった场合はすぐに次の"|"が来る。<br>
	最后にスキル经验值がくる。经验值取得が无ければすぐに","がくる。

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_RS] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_RS_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_RD"><font color=blue>servertoclient RD( string data );<br></font></a>
	ＤＵＥＬ終了后、取得又は失ったＤＵＥＬポイントを通知。<br>
        RD|得た(失った)DP(62进)|最終的なDP(62进)|<br><br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_RD] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_RD_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_B"><font color=blue>servertoclient B( string command );<br></font></a>
战闘时のコマンドを全て扱う。数值には全て 大文字16进を使う。<BR>
ここで[相手番号]とは、バトルの中での管理番号となる。
手前プレイヤーは０～４、手前ペットは５～９、向こう侧は１０～１９である。
	<br><br>
<h4>
ムービーデータ
</h4>
ムービーデータ部分は全てつながった文字列で处理される。文字のつなげ方は<BR>
マクロ( BATTLESTR_ADD( 文字列 ) )を使ってつなげる。つながった文字はターン終了后にクライアントにまとめて転送する。<BR>
ここではキーワード以外は全て数值データであり、数值以外は无视。また小文字英字も无视(コメントとして使用できる)<br>
ヘッダの后ろに"M|"と付け加える。これによりムービーデータだと认识させる。例）M|HIT|a0|dA|2|123|HIT|????????
<BR>
ムービー时のフラグの意味：ビット处理する。<BR>
---０ビット目：空振り<BR>
---１ビット目：通常ヒット<BR>
---２ビット目：会心の一击<BR>
---３ビット目：防御<BR>
---４ビット目：カウンター<BR>
---５ビット目：よけた<BR>
---６ビット目：アルティメットＫＯその１<BR>
---７ビット目：アルティメットＫＯその２<BR>
---８ビット目：防御ブレイク攻击した<BR>
---９ビット目：守护者がいる<BR>
<br>
<ul>
<li>BT|のとき、敌捕获ムービー<br>
BT|攻击侧番号|守备侧番号|フラグ|<br>
フラグが１だったら捕获成功。０だったら捕获失败。失败时にどのあたりまで敌を
引っ张ってくるかはクライアントがランダムで判定する。<br><br>
<li>ESC|のとき、逃亡ムービー<br>
BE|逃げる侧番号|守备侧番号|フラグ|<br>
フラグが１だったら捕获成功。０だったら捕获失败。失败时にどのあたりまで敌を
引っ张ってくるかはクライアントがランダムで判定する。プレイヤーキャラが逃げた
场合はペット用のBEは无しでペットも逃げるようにする。<br>
<br>
<li>bg|のとき、防御ムービー<br>
bg|防御する侧番号|<br>
これは小文字で送ること。キャラクタの动きは无い。クライアントも别にこの文字を处理せず、コメント扱い。そのための小文字。
だがわかりにくいため一应送っておく。<br>
<br>
<li>BD|のとき、ＨＰ、ＭＰ变更ムービー<br>
BD|变更されるキャラ番号|变更の种类|プラスかマイナスか|增减值|<br>
变更の种类は０がＨＰ。１がＭＰ。プラスかマイナスかは１がプラス０がマイナス<br>
<br>
<li>BF|のとき、ペットが后ろに隠れるムービー<br>
BF|隠れるキャラ番号|<br>
ペットのみが使用できて、クライアントではペットが背后に走っていっていなくなる
演出がある。地球一周等で使用<br>
<br>
<li>BJ|のとき、咒术、アイテム演出ムービー<br>
BJ|咒术使用キャラ番号|咒术使用侧エフェクト番号|咒术受け侧エフェクト番号|受けるキャラ番号|受けるキャラ番号|??????|FF|<br>
アイテム、咒术を使用した场合に使う。影响を受けるキャラ番号は连続して记述できるが最后はFFでしめる。
<br>
<br>
<br>
<li>BM|のとき、状态异常变化ムービー<br>
BM|状态变化するキャラ番号|どの状态异常か|<br>
状态异常番号は<br>
---０：状态异常无し<br>
---１：毒<br>
---２：麻痹<br>
---３：眠り<br>
---４：石化<br>
---５：酔っ拂い<br>
---６：混乱<br>
<br>
<li>MON|のとき、ペット出し入れムービー<br>
ペット戾すとき：MON|主人の番号|0|
ペット出すとき：MON|主人の番号|1|ペットの名称|ペットの画像番号|等级|Ｈｐ|ＭａｘＨｐ|Ｆｐ|ＭａｘＦｐ|フラグ|
フラグ０の时はペットが返回とき。その际はそれ以降后ろの文字は无视される<br>
フラグ１の时はペットが出とき。ペットの交换をしたい场合は戾してから出すというように２回BSコマンドを送ること。<br>
<li>BU|のとき、战闘から拔けるムービー<br>
引数无し。このコマンドはクライアントがネット等の状态で异常に遅延し、サーバーが待ちきれなくなった场合にそのクライアントを强引に战闘から拔けさせる处理を行った后に送信される。<br>
<br>
<li>BV|のとき、フィールド属性变更ムービー<br>
BV|变更をかけたキャラ番号|变更する属性番号|<br>
变更する属性番号は、<br>
---０：无属性<br>
---１：地属性<br>
---２：水属性<br>
---３：火属性<br>
---４：风属性<br>
<br>
<li>CMB|のとき、合体攻击ムービー<br>
BY|守备侧番号|攻击侧番号|フラグ|受伤|攻击侧番号|フラグ|受伤|????????と缲り返す。通常攻击と攻守が入れ替わった形。<br>
<br>
<li>CLR|のとき、撤退ムービー<br>
CLR|撤退者番号|撤退者番号|????????と缲り返す。书いた番号のキャラが逃げる<br>
<br>
<li>SKL|のとき、スキルムービー&通常攻击<br>
通常攻击、スキル攻击のプロトコルを同じとしてあつかうようにした。<br>
SKL|スキルＩＤ|消费フォースポイント|派生へ<br>
スキルＩＤで派生后<br>
<br>
通常攻击：｜武器ＩＤ｜派生へ<br>
武器ＩＤで派生后<br>
直接攻击　：｜自分のＩＤ｜相手のＩＤ｜フラグ｜受伤｜～｜自分のＩＤ｜相手のＩＤ｜フラグ｜受伤｜～｜ＦＦまで缲り返し｜
<br>ナイフ　　：｜自分のＩＤ｜相手のＩＤ｜フラグ｜受伤｜～｜相手のＩＤ｜フラグ｜受伤｜～｜ＦＦまで缲り返し｜
<br>ブーメラン：｜自分のＩＤ｜相手のＩＤ｜フラグ｜受伤｜～｜相手のＩＤ｜フラグ｜受伤｜～｜ＦＦまで缲り返し｜
<br>弓矢	　：｜自分のＩＤ｜相手のＩＤ｜フラグ｜受伤｜～｜相手のＩＤ｜フラグ｜受伤｜～｜ＦＦまで缲り返し｜
<br>
<br>
<li>SUP|のとき、スキルの等级ＵＰムービー<br>
SUP|スキル等级ＵＰ者|スキル等级ＵＰ者|????????と缲り返す。书いた番号のキャラの等级があがる<br>
<br>
<li>POS|のとき、前后のポジションを变える<br>
POS|变更するプレイヤの番号|????????と缲り返す。书いた番号の前列后列の入れ替えを行う<br>
<br>
<li>EQU|のとき、装备变更を行う。<br>
EQU|装备するプレイヤの番号|画像番号|????????装备变更を行う。<br>
<br>
<li>ITM|のとき、アイテムを使用する。<br>
ITM|自分の番号|アイテム演出で派生|????????アイテムを使用する。<br>
<br>
<li>REB|のとき、リバースを设定する。<br>
REB|リバースを设定するプレイヤの番号|フラグ|变身后のグラフィック番号|属性|????????リバースを设定する。<br>
リバースを停止する场合、属性は入らない。<br>
<br>
<li>BST|のとき、バースト时间を减らす。<br>
BST|减少するプレイヤの番号|减らす时间（秒单位）|????????バースト时间を减少させる。<br>
注：自分宛てに来たもの以外は无视する（处理のタイミングは信息と同样に行う）。<br>
<br>
<li>GMN|のとき、家族モンスター出现／撤退。<br>
GMN|バッファ番号|种类|プレイヤー番号|ＳＥ番号|ＢＧ番号|ペットの名称|ペットの画像番号|等级|Ｈｐ|ＭａｘＨｐ|Ｆｐ|ＭａｘＦｐ|フラグ|????????家族モンスターが出现する。<br>
　バッファ番号は50または51（50が手前侧、51が相手侧）。この数字が家族モンスターが行动するときの番号となる。<br>
　种类は１の场合中公会宠物、２の场合大公会宠物。引っ込むときは０が入る（以降のデータは无し）<br>
　プレイヤー番号は、家族モンスターを呼び出したプレイヤーのバッファ番号。<br>
　ＳＥ＆ＢＧ番号は、それぞれ出现时のＳＥとＢＧの番号となっている。<br>
　これ以降のパラメータはペットの出现处理とほぼ同样。<br>
<br>
</ul>
<h4>
每ターン最初に送られるデータ
</h4>
<ul>
<li>C|のとき、キャラクターデータ。これは每ターン全员に同じものが送られる。<br>
C|番号[0～19](%X)|キャラ名(%s)|画像番号(%X)|等级(%X)|HP(%X)|MAXHP(%X)|FP(%X)|フラグ(%X)|??????<BR><BR>
フラグの意味：ビット处理する。<BR>
---０ビット目：新规参加<BR>
---１ビット目：死亡<BR>
---２ビット目：プレイヤーかどうか<BR>
---３ビット目：毒<BR>
---４ビット目：麻痹<BR>
---５ビット目：眠り<BR>
---６ビット目：石化<BR>
---７ビット目：酔っ拂い<BR>
---８ビット目：混乱<BR>
<br>
<li>P|のとき、自分の个人データ。これは每ターン本人にのみ送られる。<br>
P|自分の番号[0～19](%X)|フラグ(%X)|使用可能スキルフラグ|ペットの使用可能スキルフラグ｜リバースの等级｜??????<BR><BR>
フラグの意味：ビット处理する。<BR>
---０ビット目：自分が新规参加<BR>
---１ビット目：自分が不意打ちを食らっている<BR>
---２ビット目：ブーメランを持っているか<BR>
使用可能スキルフラグ：ビット处理する<br>
各ビットはスキルの覚えてるいる场所<br>
リバースの等级が０の场合、リバースは使用不可<br>
</ul>
<h4>
每ターンコマンド决定までのカウントダウン中に送られるデータ
</h4>
<ul>
<li>BA|のとき、コマンド完了＆ターン数データ。これは谁かがコマンドをサーバーに送るたびに、全员に送られる。<br>
BA|コマンド完了ビット(%X)|ターン数(%X)|
<BR><BR>
コマンド完了ビットはビット单位で构成し、０番目のキャラがコマンドを完了したら、
０ビット目がＯＮになる。クライアントはビットがＯＮになっているキャラクタは
アニメーションさせ、ＯＦＦのキャラはアニメーションを停止させ、コマンドの完了したかどうかを区别する。<BR>
ターン数はムービーが全く开始されていない场合は０ターン目とし、ムービー
が开始された瞬间にインクリメントされる。
</ul>
      <br>
      <br>
      <hr>
      <br>
	
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_B] ) == 0 ){
		char* command;
		command = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_B_recv( fd,command);
		return 0;
	}
/*
  <LI><a name="SC_IA"><font color=blue>servertoclient IA( string data );<br></font></a>
      (Item All)<br>
      <br>
        アイテム情报。全てのアイテムを送る。<br><br>
	    (アイテム1)|(アイテム2)...(アイテムn)<br>
	    アイテムの内容は以下の方法でパックする。
	    ひとつのアイテムは、かならず以下の6つのトークンのセットで
	    送信される。ひとつひとつの内容は、<br>
	    <br>
	    名称|ステージ|アイテムの说明|アニメ番号|フィールドでの使用条件フラグ|战闘での使用条件フラグ|使用对象|アイテム等级|フラグ类|アイテムID|アイテムの种类|アイテムの个数|刻印の文字列<br>
	    <br>
	    <ul>
	      <li>アイテム名は识别等级で自动的に变更される。
	      <li>ステージは名称の色を替るのに使う。
	      <li>アイテムの说明。
	      <li>アニメ番号は、画像番号。
	      <li>フィールドでの使用条件フラグ。フィールドにおいて使用(装备アイテムの场合は装备)が出来るかどうかのフラグがセットされている。
	          これは，サーバーでは以下のように定义されている。<br>
		    <table border="1">
			  <tr><td>0bit</td><td>使用出来るかどうかのフラグ</td></tr>
			</table>
            <br>
	      <li>战闘での使用条件フラグ。战闘において使用(又は装备)が出来るかどうかのフラグがセットされている。
	          これは，サーバーでは以下のように定义されている。<br>
		    <table border="1">
			  <tr><td>0bit</td><td>使用出来る</td></tr>
			  <tr><td>1bit</td><td>装备出来る</td></tr>
			  <tr><td>2bit</td><td>片手持ち武器</td></tr>
			  <tr><td>3bit</td><td>两手持ち武器</td></tr>
			</table>
            <br>
	      <li>使用对象とは，このアイテムを使用することの出来る对象が入っている。设定については
		  <a href="#target">ここ</a>を参照の事。<br>
		</a>
  	        <font size=+1>この数字に100を足すと，死んでいる者も对象となる。</font><br>
	      <li>アイテム等级。この等级以上の者でないと装备出来ない。
	      <li>フラグ类。色んなフラグを送信する。各ビットの对应は以下の通り。<br>
	          <table border=1>
		    <tr><td>0bit目</td><td>ペットメールで送信可能なアイテムかどうか。送信可能だと１。０だと送信できない。</td></tr>
		    <tr><td>1Bit目</td><td>このアイテムが合成出来るかどうか。</td></tr>
		    <tr><td>2Bit目</td><td>このアイテムが料理かどうか。1だと料理である。</td></tr>
		    <tr><td>3Bit目</td><td>このアイテム识别されているかどうか。1だと识别されている。</td></tr>
		    <tr><td>4Bit目</td><td>このアイテムが装备していてかつ有效であるかどうか。1だと有效。装备しているアイテムで、これが０だとアイテムを赤く表示させないといけない。</td></tr>
			<tr><td>5Bit目</td><td>このアイテムが、落とされると消えるアイテムかどうか。1なら消える。0だと消えない。</td></tr>
			<tr><td>6Bit目</td><td>このアイテムが、登出时に落とすアイテムかどうか。1なら落とす。0だと落とさない。</td></tr>
			<tr><td>7Bit目</td><td>このアイテムが、そもそも装备出来るかどうか。</td></tr>
		  </table>
		<br>
		  <li>このアイテムのアイテムID。
		  <li>アイテムの种类。サーバーでは以下のように设定されている。<br>
		  <pre>
typedef enum
{
	ITEM_SWORD,			  ????
    ITEM_AXE,             ??à
    ITEM_SPEAR,           ????
    ITEM_STAFF,			  ??ó
    ITEM_BOW,             弓矢
	ITEM_KNIFE,			  ナイフ(投げ系)
    ITEM_BOOMERANG,		  ブーメラン(投げ系)
    ITEM_SHIELD,          ????
    ITEM_HELM,            ????
	ITEM_HAT,			  帽子
    ITEM_ARMOUR,          ????
	ITEM_CLOTHES,		  ????
	ITEM_ROBE,			  ローブ
    ITEM_BOOTS,			  ブーツ
    ITEM_SHOES,			  ????
	ITEM_BRACELET,		  ブレスレット
	ITEM_MUSIC,			  乐器
	ITEM_NECKLACE,		  首饰り
	ITEM_RING,			  指轮
	ITEM_BELT,			  ベルト
	ITEM_EARRING,		  イヤリング
	ITEM_AMULET,		  お守り
	ITEM_CRYSTAL,		  クリスタル
	ITEM_EQUIPCATEGORYNUM,
    ITEM_DISH = ITEM_EQUIPCATEGORYNUM,	  料理
	ITEM_FURNITURE,		  家具
	ITEM_MATERIAL,        その他装备品素材
    ITEM_OTHER,           それ以外
	ITEM_BOX,             宝箱
	ITEM_KEY, 			  ????
	ITEM_ORE,            鉱石
	ITEM_LUMBER,         木材
	ITEM_CLOTH,          ????
	ITEM_MEAT,           ??ù
	ITEM_FISH,           ????
	ITEM_VEGETABLE,      野菜
	ITEM_FOODINGREDIENT, その他食材
	ITEM_HERB,           ハーブ
	ITEM_MEDICINALPLANTS, 薬草
	ITEM_JEWEL,          宝石
	ITEM_MATERIAL_B,     その他素材B
	ITEM_SEALEDCARD,     封印カード
	ITEM_ETC_CARD,       その他のカード
	ITEM_FOOD,           食べ物
	ITEM_DRUG,           ????
	ITEM_BOOK,           ????
	ITEM_MAP,            地图
	ITEM_PILOT,          パイロット用アイテム
    ITEM_CATEGORYNUM,
}ITEM_CATEGORY;
		  </pre>
		  <li>このアイテムの个数。スタックアイテム。０の场合はスタック出来るアイテムではなく、个数表示しない。<br>
		  <li>刻印されていたらその文字列。
		<br>
	    </ul>
	    <br>
	    ここの中では<a href="#escaping">エスケープ</a>する。<br>
	    具体例( char の配列そのまま书く )<br>
	    <code>
	    Iあほ な 子|str+2|1|あほ\|いじ|10|2|0
	    </code>
	    ステージの值の意味は、整数值で
	    <table border>
	      <tr><td>1</td><td>ステージA</td></tr>
	      <tr><td>2</td><td>ステージB</td></tr>
	      <tr><td>3</td><td>ステージC</td></tr>
	      <tr><td>4</td><td>ステージD</td></tr>
	    </table>
	    とする。また、アイテム表の空の部分については、5个のトークンが
	    空で、つまりたて棒がならんだ状态で送信されてくる。
	<br>
	<br>
	<hr>
	<br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_IA] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_IA_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_I"><font color=blue>servertoclient I( string data );<br></font></a>
      (Item)<br>
      <br>
      アイテム情报。S I は全アイテムを送信するのに对してこれは指定されたアイテム番号のアイテムを
      复数个送信することが出来る。
      <dl>
        <dt>string data
        <dd>アイテム情报。
            アイテムの内容は以下の方法でパックする。
	    ひとつのアイテムは、かならずトークンのセットで
	    送信される。ほとんど，S Iのプロトコルと同じだが，先头のトークンにアイテムの场所が
	    付いています。これによって何番目のアイテムかを判断する。<br>
        </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_I] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_I_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_LI"><font color=blue>servertoclient LI( int index, int servertime, int servernumber, int adjusttime );<br></font></a>
	(Login Index)<br>
	<br>
	自分のキャラのオブジェクトindexと，サーバーの时间，サーバー番号を送る。
	クライアントは，この时间を使用して，朝昼夜のパレットを变化させたり，色々な事をする。<br>
	これはログイン时にしか送信されない。<br>
	时间は，よくある协定世界时间(UTC) 1970年1月1日 00:00:00 からの経过秒数である。<br>
	サーバー番号とは，サーバーが复数ある每に，サーバーに番号が振られていて，それが送信されてくる。
	主に、自动生成ダンジョン，家のマップ等の区别に使用する。<br>
	时间补正は、サーバーごとに时间をずらすために使用される（单位は秒单位）。<br>
      <br>
      <dl>
        <dt>int index
        <dd>自分のキャラのindex
        <dt>int servertime
        <dd>サーバーの时间
        <dt>int servernumber
        <dd>サーバーの番号
        <dt>int adjusttime
        <dd>サーバーごとの时间补正
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_LI] ) == 0 ){
		int index;
		int servertime;
		int servernumber;
		int adjusttime;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		servertime = nrproto_demkstr_int( nrproto.token_list[2] );
		servernumber = nrproto_demkstr_int( nrproto.token_list[3] );
		adjusttime = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_LI_recv( fd,index,servertime,servernumber,adjusttime);
		return 0;
	}
/*
  <LI><a name="SC_SI"><font color=blue>servertoclient SI( int fromindex, int toindex );</font><br></a>
	  (SwapItem)
	  アイテムをいれかえる。みかけ上の意味はMIと同じだが、
	  サーバーからクライアントへの通知であるということと、
	  使用する目的が异なる。SIは、サーバが胜手にアイテムの
	  状况を变化させたとき(典型的には、装备を变更したとき)に、
	  どのアイテムをどこに移动させたのかをクライアントに通知するために
	  使用する。この目的は、クライアントでアクションボタンをつかって
	  装备を变更することを可能にするためである。
	  装备を变更したらそのアイテムがどこにいったのかがわからないと
	  どのアイテムをアクションボタンに残せばいいのかがわからないからである。
	  サーバーは、装备を变更したときに、
	  何番から何番に变更したのかをクライアントに通知する。
      <dl>
	<dt>int fromindex
	<dd>移动するアイテムのインデックス。
	<dt>int toindex
	<dd>目的地のアイテムインデックス。
      </dl>
      目的地の意味は、MIと同じである。
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_SI] ) == 0 ){
		int fromindex;
		int toindex;
		fromindex = nrproto_demkstr_int( nrproto.token_list[1] );
		toindex = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_SI_recv( fd,fromindex,toindex);
		return 0;
	}
/*
  <LI><a name="SC_IR"><font color=blue>servertoclient IR( int haveskillindex, string data);</font><br></a>
  (ItemRecipeHeader)
  クライアントからのIRの返事。
  复数件送信する为に、stringで送信する。デリミタは"|"。复数トークンで１つのデータとする。
  １つのスキルに属する全件のレシピが送られる。クライアントは特に必要の无い限りこれらをキャッシュして通信を减らすようにする事。<br>
  各トークンの意味は次の通り。<br>
  文字列のようなデータ(アイテム名など)はエスケープされている。<br>
  <br>
  	<table border=1>
	<tr><td>レシピのID</td></tr>
	<tr><td>アイテムの名称</td></tr>
	<tr><td>消费FP</td></tr>
	<tr><td>レシピの素材アイテムID１</td></tr>
	<tr><td>レシピの素材アイテム名１</td></tr>
	<tr><td>レシピの素材アイテムの必要数量１</td></tr>
	<tr><td>...</td></tr>
	<tr><td>レシピの素材アイテムID５</td></tr>
	<tr><td>レシピの素材アイテム名５</td></tr>
	<tr><td>レシピの素材アイテムの必要数量５</td></tr>
	</table>
	レシピの素材アイテムのIDは、-1だと无いという事。その际アイテム名は０バイト。数量は-1になっている。<br>
	<br>
	<hr>
	<br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_IR] ) == 0 ){
		int haveskillindex;
		char* data;
		haveskillindex = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_IR_recv( fd,haveskillindex,data);
		return 0;
	}
/*
  <LI><a name="SC_BT"><font color=blue>servertoclient BT( int time, int flag);</font><br></a>
  (BurstTimeInfo)
  バースト时间に关する情报。
  バーストの状况が变化したとき（バーストのＯｎ／Ｏｆｆや、バースト时间の加减时）に、送信する。
      <dl>
	<dt>int time
	<dd> バースト时间の残り
	<dt>int flag
	<dd> バーストの使用状况（０：未使用、１：使用中）
      </dl>
	<br>
	<hr>
	<br>
	
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_BT] ) == 0 ){
		int time;
		int flag;
		time = nrproto_demkstr_int( nrproto.token_list[1] );
		flag = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_BT_recv( fd,time,flag);
		return 0;
	}
/*
  <LI><a name="SC_MSG"><font color=blue>servertoclient MSG( int aindex , string text , int color );</font></a><br>
      (MeSsaGe)
      サーバからクライアントにたいして、アドレスブックによる信息
      の送信をおこなう。通常のチャット信息とことなり、受信フラグ
      やキューイングなどの处理があるためTとは别のプロトコルとしている。
      <dl>
	<dt>int aindex
	<dd>アドレスブックのインデックス。このインデックスで特定される相手
	    からの信息であることをしめす。
	<dt>string text
	<dd>信息の内容。エスケープする必要はない。信息の内容は，mm/dd hh:mm|text...|graphicsno|lv|name|itemgraphicsno
	    となっていて最初のトークンは日付ヘッダが入っている。次は实际の信息の内容。次は画像番号。
	    通常メールは-1。この场合はこれ以降のトークンは存在しない。（例）mm/dd hh:mm|text|-1
	    ペットメールの场合は，运んできたペットの画像番号が格纳されている。
	    lvはメールを运んできたペットの等级。次は名称が格纳されている。これはエスケープされている。
	    itemgraphicsno は运んできたアイテムの画像番号が格纳されている。
	<dt>int color
	<dd><a href="#coloring">色</a>。
	<dt>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_MSG] ) == 0 ){
		int aindex;
		char* text;
		int color;
		aindex = nrproto_demkstr_int( nrproto.token_list[1] );
		text = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		color = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_MSG_recv( fd,aindex,text,color);
		return 0;
	}
/*
  <LI><a name="SC_AL"><font color=blue>servertoclient AL( int albumversion, string data );</font></a><br>
      (ALbum)<br>
      サーバーからクライアントに对して、アルバムの何番のIDを覚えているかの情报を送る。ログイン时やアルバム登録时などに送られる。<br>
      何番を持っているか、という事だけで、详细情报についてはない。情报については<a href="#SC_ALI">ALI</a>プロトコルを参照の事。<br>
      <dl>
      <dt>int albumversion
      <dd>アルバムのバージョンを示す数值。クライアントは、アルバム情报をローカルにキャッシュするが、このアルバムバージョンが变わったらキャッシュは舍ててまたサーバーにALIを送って情报を取り直さなければならない。<br>
      <dt>string data
      <dd>何番のIDを覚えているかの文字列。デリミタ"|"で复数个送られて来る。各トークンの数值を２进数に变换して、ビットの立っている场所がIDを示す。トークンが增える每に、32ビットなので32+されたIDになる。IDのカウントは１から始まるのに注意。<br>
      ex.<br>
      2147483649|1|3|<br>
      だと、<br>
      2147483649 = 80000001H = 10000000000000000000000000000001<br>
      1 = 1H = 00000000000000000000000000000001<br>
      3 = 3H = 00000000000000000000000000000011<br>
      となり、覚えているIDは、1,32,33,65,66となる。<br>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_AL] ) == 0 ){
		int albumversion;
		char* data;
		albumversion = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_AL_recv( fd,albumversion,data);
		return 0;
	}
/*
  <LI><a name="SC_ALI"><font color=blue>servertoclient ALI( int albumid, int imgno, int catchflg, int rare, int type, int vital, int str, int tgh, int quick, int magic, int earth, int water, int fire, int wind, int slot, string comment, string name );</font></a><br>
      (ALbumInfo)<br>
        サーバーからクライアントに对して、要求されたアルバムデータの内容を送る。<br>
      <dl>
      <dt>int albumid
      <dd>アルバムID
      </dl>
	imgno ペットの画像番号<br>
	catchflg 封印出来るかどうか。0なら出来ない 1なら出来る。<br>
	rare レアかどうか。0 普通 1 レア 2 超レア<br>
	type 种族<br>
	vital 体力 (0～9の★の数で送られる。详しくは仕样书を参考の事)<br>
	str 腕力 (0～9の★の数で送られる。详しくは仕样书を参考の事)<br>
	tgh タフ (0～9の★の数で送られる。详しくは仕样书を参考の事)<br>
	quick 素早さ (0～9の★の数で送られる。详しくは仕样书を参考の事)<br>
	magic 魔力 (0～9の★の数で送られる。详しくは仕样书を参考の事)<br>
	earth 地属性值<br>
	water 水属性值<br>
	fire 火属性值<br>
	wind 风属性值<br>
	slot スロットの数<br>
	comment 说明文字列<br>
	name ペットの名称<br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ALI] ) == 0 ){
		int albumid;
		int imgno;
		int catchflg;
		int rare;
		int type;
		int vital;
		int str;
		int tgh;
		int quick;
		int magic;
		int earth;
		int water;
		int fire;
		int wind;
		int slot;
		char* comment;
		char* name;
		albumid = nrproto_demkstr_int( nrproto.token_list[1] );
		imgno = nrproto_demkstr_int( nrproto.token_list[2] );
		catchflg = nrproto_demkstr_int( nrproto.token_list[3] );
		rare = nrproto_demkstr_int( nrproto.token_list[4] );
		type = nrproto_demkstr_int( nrproto.token_list[5] );
		vital = nrproto_demkstr_int( nrproto.token_list[6] );
		str = nrproto_demkstr_int( nrproto.token_list[7] );
		tgh = nrproto_demkstr_int( nrproto.token_list[8] );
		quick = nrproto_demkstr_int( nrproto.token_list[9] );
		magic = nrproto_demkstr_int( nrproto.token_list[10] );
		earth = nrproto_demkstr_int( nrproto.token_list[11] );
		water = nrproto_demkstr_int( nrproto.token_list[12] );
		fire = nrproto_demkstr_int( nrproto.token_list[13] );
		wind = nrproto_demkstr_int( nrproto.token_list[14] );
		slot = nrproto_demkstr_int( nrproto.token_list[15] );
		comment = nrproto_wrapStringAddr( nrproto_stringwrapper[16] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[16] ));
		name = nrproto_wrapStringAddr( nrproto_stringwrapper[17] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[17] ));
		nrproto_ALI_recv( fd,albumid,imgno,catchflg,rare,type,vital,str,tgh,quick,magic,earth,water,fire,wind,slot,comment,name);
		return 0;
	}
/*
  <LI><a name="SC_ALN"><font color=blue>servertoclient ALN( int albumid, string name );</font></a><br>
      (ALbumpetName)<br>
      サーバーからクライアントに对して、要求されたアルバムデータの内容を送る。<br>
      <dl>
      <dt>int albumid
      <dd>アルバムID
      <dt> string name
      <dd>ペットの名称
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ALN] ) == 0 ){
		int albumid;
		char* name;
		albumid = nrproto_demkstr_int( nrproto.token_list[1] );
		name = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_ALN_recv( fd,albumid,name);
		return 0;
	}
/*
  <LI><a name="SC_ALO"><font color=blue>servertoclient ALO( int albumid );</font></a><br>
      (ALbumOpen)<br>
      サーバーからクライアントに对して、要求されたアルバムIDのページのアルバムウィンドウを开けという。<br>
      <dl>
      <dt>int albumid
      <dd>アルバムID
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ALO] ) == 0 ){
		int albumid;
		albumid = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_ALO_recv( fd,albumid);
		return 0;
	}
/*
  <LI><a name="SC_PME"><font color=blue>servertoclient PME( int objindex, int graphicsno, int x, int y, int dir, int flg, int no, string cdata );<br></font></a>
      (PetMeSsaEffect)
      ペットメールでの，ペットの登场を表现する。<br><br>
      <dl>
	<dt>int objindex
	<dd>ペットのオブジェクト番号。
	<dt>int graphicsno
	<dd>登场するペットの画像番号
	<dt>int x,y
	<dd>どの位置に出现するか
	<dt>int dir;
	<dd>どの方向に向かっていくか。
	<dt>int flg
	<dd>行きしなか(0)，归りしなか(1)。
	<dt>int no
	<dd>演出番号。
	<dt>string cdata
	<dd>C のデータが入っている。归ってくる时のような演出の时には，そのままオブジェクトとなるので
	    これが必要。これを元にオブジェクトを作成する。このプロトコルが来た场合，
	    このキャラクタのC はこないものとする。
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PME] ) == 0 ){
		int objindex;
		int graphicsno;
		int x;
		int y;
		int dir;
		int flg;
		int no;
		char* cdata;
		objindex = nrproto_demkstr_int( nrproto.token_list[1] );
		graphicsno = nrproto_demkstr_int( nrproto.token_list[2] );
		x = nrproto_demkstr_int( nrproto.token_list[3] );
		y = nrproto_demkstr_int( nrproto.token_list[4] );
		dir = nrproto_demkstr_int( nrproto.token_list[5] );
		flg = nrproto_demkstr_int( nrproto.token_list[6] );
		no = nrproto_demkstr_int( nrproto.token_list[7] );
		cdata = nrproto_wrapStringAddr( nrproto_stringwrapper[8] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[8] ));
		nrproto_PME_recv( fd,objindex,graphicsno,x,y,dir,flg,no,cdata);
		return 0;
	}
/*
  <LI><a name="SC_AB"><font color=blue>servertoclient AB( string data );</font></a><br>
      (AddressBook)サーバーがクライアントに送信するアドレスブックの全内容。
      <dl>
	<dt>string data
	<dd>情报の内容。表示するために必要な情报は、有效か无效かのフラグ，相手のキャラの名称、
	    等级、デュエルポイント，ONラインかどうかのフラグ。
	    それが人数分ならんでいる。
	    データはその时点での最新である。
	    <code>一人目|二人目|...</code>人数に制限はない。
	    一人分の信息は以下の构成である。
	    <br>
	    <code>使用フラグ|名称(文字列)|ユーザー称号|登録番号|等级数值|フラグ|画像番号
	    </code>
	    <br>
	    したがって、 N人目の名称にアクセスするには 7*(N-1)+1番目の
	    トークンを、しらべればよいことになる。そのトークンが存在し
	    ないならば、それ以降のエントリは存在しない。名称文字列は<a
	    href="#escaping">エスケープ</a>してからたて棒でつなぐ。
	    フラグは0ならOFFライン、1以上ならONラインである。
	    この值はどのサーバの人がONラインかを见分ける为に，
	    setup.cfにて servernumber=n のnの值が送信される。
	    特别な机能として、名称のトークンが空の场合は、以前のAB
	    コマンドで受信した名称の情报を更新せずに等级とライフの
	    项目だけを更新する。というのは名称の情报はキャラ作成后
	    2度と更新されないからだ。
	    登録番号は、名刺登録した顺番にseqnoが振られている。これでソートして间を诘めたりするのだ。<br>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_AB] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_AB_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_ABG"><font color=blue>servertoclient ABG( string data );</font></a><br>
      (AddressBookGuildname)サーバーがクライアントに送信する、アドレスブック用の家族名。
      通常はABと同时に発信される。
      <dl>
	<dt>string data
	<dd>情报の内容。アルバムブックのキャラクタが所属する家族の名称とタイトル名が入っている。
	    それが人数分ならんでいる。データはその时点での最新である。
	    <code>一人目|二人目|...</code>人数に制限はない。
	    一人分の信息は以下の构成である。
	    <br>
	    <code>家族名|家族称号|
	    </code>
	    <br>
	    トークンが存在しない场合、それ以降のエントリーは存在しない。
	    家族名および家族称号は<a href="#escaping">エスケープ</a>されている。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ABG] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_ABG_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_ABI"><font color=blue>servertoclient ABI( int num, string data );</font></a><br>
      ABの单体版。num には自分のアドレスブックの何番目かと言う事が入っている。
      dataの中身はABの内容に加えて、末尾に家族名および家族称号が追加されている。<br>
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ABI] ) == 0 ){
		int num;
		char* data;
		num = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_ABI_recv( fd,num,data);
		return 0;
	}
/*
  <LI><a name="SC_GC"><font color=blue>servertoclient GC( void );</font></a><br>
      (GuildCreate)
      クライアントに、自分が家族に所属したことを伝える。
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GC] ) == 0 ){

		nrproto_GC_recv( fd);
		return 0;
	}
/*
  <LI><a name="SC_GI"><font color=blue>servertoclient GI( string data );</font></a><br>
      (GuildInfomation)
      クライアントに、自分が所属する家族の家族情报を送る。
      <dl>
	<dt>string data
	<dd>ビットフィールド｜…<br>
	情报の详细。ビットフィールドは各要素のビットである。情报がある要素に对应するビットに１が入っている。
	情报と、それに对应するビット番号は以下のテーブルを参照。なお、ＣＰと同样に0ビット目が立っている场合、
	全情报の送信となる。
	<table border>
	  <tr><td>ビット番号</td><td>内容</td><td>データの型</td></tr>
	  <tr><td> 0</td><td>bitfield</td><td>int</td></tr>
	  <tr><td> 1</td><td>家族ＩＤ（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td> 2</td><td>家族名</td><td>string</td></tr>
	  <tr><td> 3</td><td>家族ルーム名</td><td>string</td></tr>
	  <tr><td> 4</td><td>自分の家族称号番号（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td> 5</td><td>ＧＩの更新情报（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td> 6</td><td>ソートの种类</td><td>int</td></tr>
	  <tr><td> 7</td><td>家族が结成されたサーバー番号（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td> 8</td><td>自分のもつ权限（ビット情报）（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td> 9</td><td>公会宠物の名称??その１（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td>10</td><td>公会宠物の名称??その２（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td>11</td><td>公会宠物の名称??その３（ＳＣのみ）</td><td>int</td></tr>
	  <tr><td>12</td><td>自分の管理番号（ＳＣのみ）</td><td>int</td></tr>
	</table>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GI] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GI_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_GT"><font color=blue>servertoclient GT( string data );</font></a><br>
      (GuildTitle)
      クライアントに、家族称号情报を送る。
      <dl>
	<dt>string data
	<dd>番号｜权限｜タイトル名｜…<br>
	番号は家族称号のインデックス番号。权限はこの家族称号の持つ、
	家族に对する权限のフラグビットである。タイトル名には家族称号の文字列が
	エスケープされて入っている。<br>
	家族称号が登録されていない番号のデータは、权限、タイトルともにヌル文字列が送られてくる。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GT] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GT_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_GM"><font color=blue>servertoclient GM( string data );</font></a><br>
      サーバーがクライアントに送信する家族成员データの情报。
      <dl>
	<dt>string data
	<dd>情报の内容。表示するために必要な情报は、有效か无效かのフラグ，相手のキャラの名称、
	    等级、デュエルポイント，ONラインかどうかのフラグ。
	    それが人数分ならんでいる。
	    データはその时点での最新である。
	    <code>一人目|二人目|...</code>人数に制限はない。
	    一人分の信息は以下の构成である。<br>
	    <br>
	    <code>使用フラグ|管理番号|(名称(文字列)|玩家称号)|登録番号|等级数值|フラグ|画像番号|家族称号番号
	    </code>
	    <br>
	    したがって、 N人目の名称にアクセスするには 8*(N-1)+1番目のトークンを调べれば良い事になる。
	    そのトークンが存在しないならば、それ以降のエントリは存在しない。
	    名称文字列には“名称|玩家称号”という形でそれぞれ<a href="#escaping">エスケープ</a>された文字列が、
	    さらにエスケープされて连结されている。
	    フラグは0ならOFFライン、1以上ならONラインである。
	    この值はどのサーバの人がONラインかを见分ける为に、setup.cfにて servernumber=n の n の值が送信される。
	    管理番号と登録番号は一绪のものである。家族成员に对する操作を行うときには、
	    この番号を使用してアクセスを行うこと。<br>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GM] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GM_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_GMI"><font color=blue>servertoclient GMI( int num, string data );</font></a><br>
	GM の单体版。num には家族成员の管理番号が入っている。
	dataの中身は GM に准ずる。<br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GMI] ) == 0 ){
		int num;
		char* data;
		num = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_GMI_recv( fd,num,data);
		return 0;
	}
/*
  <LI><a name="SC_RGM"><font color=blue>servertoclient RGM( int index );</font></a><br>
      (RemovefromGuildMember)
      指定された番号の家族成员を、家族成员リストからはずす。
      LG や RGM の返答として以外に、キャラクタが消灭したときにも使用される。
      <dl>
	<dt>int index
	<dd>はずしたいキャラクタの、家族成员管理番号。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_RGM] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_RGM_recv( fd,index);
		return 0;
	}
/*
  <LI><a name="SC_GML"><font color=blue>servertoclient GML( int index, string text, int color );</font></a><br>
      (GuildMaiL)
      サーバからクライアントにたいして、家族成员による信息
      の送信をおこなう。通常のチャット信息とことなり、受信フラグ
      やキューイングなどの处理があるためTとは别のプロトコルとしている。
      <dl>
	<dt>int index
	<dd>家族成员の管理番号。このインデックスで特定される相手
	    からの信息であることをしめす。
	<dt>string text
	<dd>信息の内容。エスケープする必要はない。信息の内容は，mm/dd hh:mm|text...|graphicsno|lv|name|itemgraphicsno
	    となっていて最初のトークンは日付ヘッダが入っている。次は实际の信息の内容。次は画像番号。
	    通常メールは-1。この场合はこれ以降のトークンは存在しない。（例）mm/dd hh:mm|text|-1
	    ペットメールの场合は，运んできたペットの画像番号が格纳されている。
	    lvはメールを运んできたペットの等级。次は名称が格纳されている。これはエスケープされている。
	    itemgraphicsno は运んできたアイテムの画像番号が格纳されている。
	<dt>int color
	<dd><a href="#coloring">色</a>。
	<dt>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GML] ) == 0 ){
		int index;
		char* text;
		int color;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		text = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		color = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_GML_recv( fd,index,text,color);
		return 0;
	}
/*
  <LI><a name="SC_GD"><font color=blue>servertoclient GD( int index );</font></a><br>
      (GuildDissolution)
      家族が解散した时に送信される。
      <dl>
	<dt>int index
	<dd>家族のインデックス番号。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GD] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_GD_recv( fd,index);
		return 0;
	}
/*
  <LI><a name="SC_PRV"><font color=blue>servertoclient PRV( string data);</font><br></a>
  (ProfileViersion)
  ログイン时に、プロフィールファイルの现在のバージョンを送信する
	<dl>
	<dt>string data
	<dd>卖买リストのバージョン|その他リストのバージョン|NPCからのメール送信值段
	<br>
	<br>
	</dl>
	<br>
	<hr>
	<br>


*/
#ifdef PUK3_PROF
	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRV] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_PRV_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_PRL"><font color=blue>servertoclient PRL( string data);</font><br></a>
  (ProfileList)
  ログイン时に、プロフィール画面で选择可能なアイテムリスト情报を送信する
	<dl>
	<dt>string data
	<dd> 
		ID|画像番号|表示名|....
		上记を1つのデータとして、复数个送信される。
	<br>
	<br>
	</dl>
	<dd><table border=1>
	    <tr><td>ID</td><td>アイテムの分类ID</td><td>0:指定无し 1:剑 2:斧.. </td></tr>
	    <tr><td>画像番号</td><td>プロフィール画面に表示するアイコンの画像番号</td><td>　</td></tr>
	    <tr><td>表示名</td><td>プロフィール画面のリストウィンドウに表示するアイテムの名称</td><td>　</td</tr>
	</table>
	<br>
	<hr>
	<br>


*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRL] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_PRL_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_PRA"><font color=blue>servertoclient PRA( string data);</font><br></a>
  (ProfileList)
  ログイン时に、プロフィール画面で选择可能なABOUTリスト情报を送信する
	<dl>
	<dt>string data
	<dd> 
		ID|画像番号|表示名|....
		上记を1つのデータとして、复数个送信される。
	<br>
	<br>
	</dl>
	<dd><table border=1>
	    <tr><td>ID</td><td>Aboutの分类ID</td><td>0:指定无し 1:冒険仲间 2:家族... </td></tr>
	    <tr><td>画像番号</td><td>プロフィール画面に表示するアイコンの画像番号</td><td>　</td></tr>
	    <tr><td>表示名</td><td>プロフィール画面のリストウィンドウに表示するアイテムの名称</td><td>　</td</tr>
	</table>
	<br>
	<hr>
	<br>



*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRA] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_PRA_recv( fd,data);
		return 0;
	}
/*
/*
 <LI><a name="SC_PRD"><font color=blue>servertoclient PRD(string name,int lv,string job,string title,string guildname,int graphic,int sid,string smsg,int bid,string bmsg,int aid,string amsg,string pmsg);</font><br></a>
  (ProfileList)
  ??????ú????????????????????????????ò????????????????
	<dl>
	<br>

	<br>
	</dl>
	<dt>string name
	<dd>对象キャラクタ名
	<dt>int lv
	<dd>对象キャラクタLv
	<dt>string job
	<dd>对象キャラクタ职业
	<dt>string title
	<dd>??????????????罕鬣??????????????????????
	<dt>string guildname
	<dd>??????????????罕鬣????????????????????
	<dt>int graphic
	<dd>对象キャラクタ颜グラフィック番顷E
	<dt>int sid
	<dd>贩卖希望アイテムのID。IDは??前イン时にサーバから取得した、ΒE好肇佞．ぅ??良Dを参照
	<dt>string smsg
	<dd>贩卖希望用信息。
	<dt>int bid
	<dd>购入希望アイテムのID。IDは??前イン时にサーバから取得した、ΒE好肇佞．ぅ??良Dを参照
	<dt>string smsg
	<dd>购入希望用信息。
	<dt>int aid
	<dd>AboutアイテムのID。IDは??前イン时にサーバから取得した、ΒE好肇佞．ぅ??良Dを参照
	<dt>string smsg
	<dd>About用信息。
	<dt>string pmsg
	<dd>??????ú????????????????????皈??????????????????
	<br>
	<br>
	<dd><table border=1>
	    <tr><td>名称</td><td>キャラ名</td><td>&nbsp;</td></tr>
	    <tr><td>??????????/td><td>Lv</td><td>&nbsp;</td></tr>
	    <tr><td>职业</td><td>职业名</td><td>&nbsp;</td></tr>
	    <tr><td>????????/td><td>??译??????????????????????????????/td><td>&nbsp;</td></tr>
	    <tr><td>??????????????</td><td>????????????????????</td><td>&nbsp;</td></tr>
	    <tr><td>颜グラフィック</td><td>颜グラフィック番顷E/td><td>&nbsp;</td></tr>
	    <tr><td>贩卖希望アイテムID</td><td>贩卖アイテムの分类ID</td><td>0:指逐E气?1:权E2:腹E</td></tr>
	    <tr><td>贩卖希望用MSG</td><td>贩卖用の信息</td><td>&nbsp;</td></tr>
	    <tr><td>购入希望アイテムID</td><td>购入アイテムの分类ID</td><td>0:指逐E气?1:权E2:腹E</td></tr>
	    <tr><td>购入希望用MSG</td><td>购入用の信息</td><td>&nbsp;</td></tr>
	    <tr><td>AboutID</td><td>About??????????????????鶫D</td><td>0:?????????????? 1:??????????逻?? .. </td></tr>
	    <tr><td>About信息</td><td>その他信息</td><td>　</td</tr>
	    <tr><td>??????ú????????????????皈??????????????</td><td>??????ú????????????????皈??????????????</td><td>????</td</tr>
	</table>

	<br>
	<hr>
	<br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRD] ) == 0 ){
		char* name;
		int lv;
		char* job;
		char* title;
		char* guildname;
		int graphic;
		int sid;
		char* smsg;
		int bid;
		char* bmsg;
		int aid;
		char* amsg;
		char* pmsg;
		name = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		lv = nrproto_demkstr_int( nrproto.token_list[2] );
		job = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		title = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));
		guildname = nrproto_wrapStringAddr( nrproto_stringwrapper[5] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[5] ));
		graphic = nrproto_demkstr_int( nrproto.token_list[6] );
		sid = nrproto_demkstr_int( nrproto.token_list[7] );
		smsg = nrproto_wrapStringAddr( nrproto_stringwrapper[8] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[8] ));
		bid = nrproto_demkstr_int( nrproto.token_list[9] );
		bmsg = nrproto_wrapStringAddr( nrproto_stringwrapper[10] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[10] ));
		aid = nrproto_demkstr_int( nrproto.token_list[11] );
		amsg = nrproto_wrapStringAddr( nrproto_stringwrapper[12] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[12] ));
		pmsg = nrproto_wrapStringAddr( nrproto_stringwrapper[13] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[13] ));
		nrproto_PRD_recv( fd,name,lv,job,title,guildname,graphic,sid,smsg,bid,bmsg,aid,amsg,pmsg);
		return 0;
	}
/*
 <LI><a name="SC_PRE"><font color=blue>servertoclient PRE( string data );</font><br></a>
  (ProfileList)
  登出时、プロフィール用名刺栏の削除要求を行う
	<dl>
	<dt>string data
	<dd>
		削除对象のプロフィール管理番号|削除对象者名<br>
	<br>上记を1つのデータとして、送信される。
	<br>
	<br>
	</dl>
	<br>
	<hr>
	<br>


*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRE] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_PRE_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_PRM"><font color=blue>servertoclient PRM( int index, string text, int color );</font></a><br>
      サーバからクライアントにたいして、プロフィールリストメンバーによる信息
      の送信をおこなう。
      <dl>
	<dt>int index
	<dd>プロフィールメンバーの管理番号。このインデックスで特定される相手
	    からの信息であることをしめす。
	<dt>string text
	<dd>信息の内容。エスケープする必要はない。信息の内容は，mm/dd hh:mm|msg|graphicsno|lv|name|title
	    となっていて最初のトークンは日付ヘッダが入っている。次は实际の信息の内容。次は画像番号,Lv,名称、
	ユーザタイトルと続く
	<dt>int color
	<dd><a href="#coloring">色</a>。
	<dt>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRM] ) == 0 ){
		int index;
		char* text;
		int color;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		text = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		color = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_PRM_recv( fd,index,text,color);
		return 0;
	}

/*
  <LI><a name="SC_PRW"><font color=blue>servertoclient PRW( int windowtype, int buttontype, int seqno, int objindex, string data );<br></font></a>
      (Window)<br>
      ウィンドウを表示せよとサーバーがクライアントに通知す??E??br>
      <br>
      <dl>
	<dt>int windowtype
	<dd>ウィンドウ种类。どのような形式のウィンドウを表示す??E????海??扉uttontype の组み合??E擦?
	    ??????????ó??????????????霪熙????????<br>
	    サーバーでは以下の样に设定さ??E讨い????br>
	  <pre>
		typedef enum
		{
			WINDOW_MESSAGETYPE_MESSAGE,			信息のみ
			WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT,		??皈????????????????????????????????
			WINDOW_MESSAGETYPE_SELECT,			选择ウィンドウ
			WINDOW_MESSAGETYPE_PETSELECT,			ペット选择ウィンドウ
			WINDOW_MESSAGETYPE_PARTYSELECT,			仲间选择ウィンドウ
			WINDOW_MESSAGETYPE_PETANDPARTYSELECT,		ペット，仲间选择ウィンドウ
			WINDOW_MESSAGETYPE_ITEMSHOPMENU,		お店のメニューウインドウ
			WINDOW_MWSSAGETYPE_ITEMSHOPYBUY,		お店から买ウインドウ
			WINDOW_MWSSAGETYPE_ITEMSHOPYSELL,		??????????????荀??????????ó????????
			WINDOW_MESSAGETYPE_LIMITITEMSHOPMAIN,		??网????隍????逾????????????犁????????皈????ó??????????ó????????
			WINDOW_MESSAGETYPE_PETSKILLSHOP,		ペットの技屋さんウインドウ
			WINDOW_MESSAGETYPE_WIDEMESSAGE,			信息のみ（大きい方）
			WINDOW_MESSAGETYPE_WIDEMESSAGEANDLINEINPUT,	??皈??????????????????????????????????????遉??????????????
			WINDOW_MESSAGETYPE_POOLITEMSHOPMENU,		??????????????猩??????????????皈????蝪????????????ó????????
			WINDOW_MESSAGETYPE_POOLITEMSHOPMAIN,		??????????????猩??????????????皈????ó??????????ó????????????
			WINDOW_MESSAGETYPE_PLAYERANDPETSELECT,		自分とペット选择ウインドウ
			WINDOW_MESSAGETYPE_PETANDPARTYSELECT,		ペット，仲间选择ウィンドウ
			WINDOW_MESSAGETYPE_SKILLMASTER_SHOP,		????????????????????????????????????????ó????????
			WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_BUY,	????????????????????????????????网????????????ó????????
			WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_REMOVE,	????????????????????????????????????????????????ó????????
			WINDOW_MESSAGETYPE_INJURY_DOCTOR,			怪我医者ウィンドウ
			WINDOW_MESSAGETYPE_JUDGEMAN,				????????????????????ó????????
			WINDOW_MESSAGETYPE_BOARD,                   伝言板ウィンドウ
			WINDOW_MESSAGETYPE_GUILDMONSTER,            ????????????筵ó??????????????????????ó????????
			WINDOW_MESSAGETYPE_ITEMBOX,                 ????????筵ó????????爨????????????????争??????????????
			WINDOW_MESSAGETYPE_FOODBOX,                 ????????筵ó????????爨??????????????
			WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT,  信息と二行入力
			WINDOW_MESSAGETYPE_ORTHOPEDIST,             整形外科医ウィンドウ
			WINDOW_MESSAGETYPE_ORTHOPEDIST_CONFIRMATION,整形外科医、颜确认ウィンドウ 
			WINDOW_MESSAGETYPE_PROFILE,                 ??????ú????????????????魇??????????ó???????? ????
			WINDOW_MESSAGETYPE_PROFILELIST,             ??????ú??????????????????????????????????ó???????? ????
			WINDOW_MESSAGETYPE_PROFILEMAIL,             ??????ú????????????????癸??????????????ó???????? ????
			WINDOW_MESSAGETYPE_ALBUM_WINDOW,            ????????????争????????ó????????
		}WINDOW_MESSAGETYPE;
	  </pre>
	<dt>int buttontype
	<dd>??????????ó??????????????ò??????熙??????????????????????????????????????????????????????????????????????????????<br>
	  <pre>
		#define		WINDOW_BUTTONTYPE_NONE		(0)
		#define		WINDOW_BUTTONTYPE_OK		(1 << 0)
		#define		WINDOW_BUTTONTYPE_CANCEL	(1 << 1)
		#define		WINDOW_BUTTONTYPE_YES		(1 << 2)
		#define		WINDOW_BUTTONTYPE_NO		(1 << 3)
		#define		WINDOW_BUTTONTYPE_PREV		(1 << 4)
		#define		WINDOW_BUTTONTYPE_NEXT		(1 << 5)
		#define		WINDOW_BUTTONTYPE_DELETE	(1 << 6)
	  </pre>
	    ????????鬓????????????遉??????????÷????????????????网??????????YES??????????ó????NO??????????ó????????????????????????<br>
	    WINDOW_BUTTONTYPE_YES | WINDOW_BUTTONTYPE_NO   (=12)<br>
	    ??????÷??????
	<dt>int seqno
	<dd>このウィンドウの番号を示す。サーバーが管理す??E?
	    クライアントはWNにてこのウィンドウの操笹国E未鲲属垢里法い海粮峭坛鯏困┐栃崚悊垢???
	    ????????????隍??????????????????????????????????NPC????????????????????????????????????ó??????????????ò??????????????隍??隍????????????????
	<dt>int objindex
	<dd>このウィンドウを出せと言ったNPCなどのindexが格纳さ??E讨い???
	    システムが出せと言った准E腓廊1などが入ってい??E?
	    クライアントは，ウィンドウ入力后のWNプ??组コΒE任海凌??佑须修里泙淙属擦侘匹ぁ??br>
			<br>
	<dt>string data
	<dd>信息内容を示す。内容はエスケープす??E?
	    データ内は项目每に"｜"で区切ら??E讨い泙后?
	<br><br><dd>??膻魇??????????ó????????????br>
	??苌罕????鬣????????????|About??????鬣????????????<br><br>
	??????????苌罕????鬣????????????<br>
	　　　　アイテム１フラグ|アイテム２フラグ|アイテムnフラグ??（卖买ΒE好噺朕????<br><br>
	　　　　对象アイテムを、卖买いず??E??离??誊乾蝇棒渖蝇靴讨い??罅玺凶??减浇垢????隋′
	下记のようにBITを立てて送信す??E??br>
	　　　　　　アイテムフラグ: 0bit ON=卖??E1bit ON=买い<br><br>
	????????About??????鬣????????????<br>
	　　　　Aboutフラグ１|About２フラグ|About nフラグ??（AboutΒE好噺朕????<br><br>
	　　　　对象About项目を设定してい??E罅玺凶??减浇垢????隋??bitを立てて送信す??Ebr><br>

	<br><dd><br>??羣????÷??????????br>
	ユーザ识别子|ユーザ名|画蔵E峭唇对象信息<br>
	上记を１括りとして、1ページ表示可能データ数分缲??E属梗br>
	　??ユーザ识别子<br>
	　　　　サーバ侧での管理に使う识别子<br><br>
	??????????霖????????br>
	　　　　检索对象ユーザの画蔵E峭??br><br>
	　??ユーザ名<br>
	　　　　检索对象ユーザの名称。ユーザ识别子と并せてサーバ侧でのユーザ特定に使う<br><br>
	　??对象信息<br>
	　　　　检索对象ユーザが设定してい??E瓮夺察玺检２萍棉充┐忙箸Α??br><br>

	<br><dd><br>??罕????ú??????????????????????????br>
	????????|Lv|????????|??????谏??????????????|??霖??????谏Sell??????????????潜D|Sell??皈??????????????|Buy??????????????潜D
				|Buy信息|AboutアイテムID｜About信息
				|??????ú????????????????皈??????????????

	　??名称<br>
	　　　　キャラ名<br><br>
	　??Lv<br>
	??????????????????????罕鬣????????br><br>
	　??职业<br>
	　　　　职业名<br><br>
	????????????????br>
	　　　　自己入力タイトΒEbr><br>
	??????????????????????<br>
	????????????????????????????????<br><br>
	??????????霖????????br>
	　　　　颜グラフィック番顷Ebr><br>
	　??SellアイテムID<br>
	　　　　贩卖希望アイテムの分类ID<br><br>
	　??Sell信息<br>
	　　　　贩卖希望信息<br><br>
	　??BuyアイテムID<br>
	　　　　购入希望アイテムの分类ID<br><br>
	　??Buy信息<br>
	　　　　购入希望信息<br><br>
	　??AboutアイテムID<br>
	　　　　その他冒険、仲间に关す??E离??誊乾衂D<br><br>
	　??About信息<br>
	　　　　その他冒険、仲间に关す??E瓮夺察玺玄br><br>
	??????????????ú????????????????皈??????????????<br>
	??????????????????????ú??????????????????????????????皈??????????????

      </dl>
      <br>
      <br>
      <hr>
      <br>


*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRW] ) == 0 ){
		int windowtype;
		int buttontype;
		int seqno;
		int objindex;
		char* data;
		windowtype = nrproto_demkstr_int( nrproto.token_list[1] );
		buttontype = nrproto_demkstr_int( nrproto.token_list[2] );
		seqno = nrproto_demkstr_int( nrproto.token_list[3] );
		objindex = nrproto_demkstr_int( nrproto.token_list[4] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[5] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[5] ));
		nrproto_PRW_recv( fd,windowtype,buttontype,seqno,objindex,data);
		return 0;
	}

/*
  <LI><a name="SC_PRAD"><font color=blue>servertoclient PRAD(string data);</font></a><br>
      サーバからクライアントにたいして、プ??奏ィーΒE??好肇瓮鹀一筱媛团铭里鲆圆レ
      <dl>
	<dt>string text
	<dd>データ内容は，index|graphicsno|lv|name|titleとなってい??E??br><br>
	　??index<br>
	??????????????????????ú????????????????皈ó??????????????????????????译??????????????????????????????????????????????????br><br>
	　??graphicsno<br>
	　　　　颜グラフィック番顷Ebr><br>
	　??lv<br>
	??????????????????????罕鬣????????br><br>
	　??name<br>
	　　　　キャラ名<br><br>
	　??title<br>
	　　　　自己入力タイトΒEbr><br>
	<dt>
      </dl>
      <br>
      <hr>
      <br>


*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PRAD] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_PRAD_recv( fd,data);
		return 0;
	}

#endif
/*
    <LI><a name="SC_TK"><font color=blue>servertoclient TK( int index,string message ,int color, int fontsize);<br></font></a>
      <dl>
	<dt>index
	<dd>しゃべったキャラのインデックス。-1 の时は、ふきだしの必
	    要のないシステム信息<br>
	<dt>string
	<dd>string の内容は, "|" で2つに区切られている。ひとつめのトー
	    クンの文字によって2つめのトークンの内容が变更される。2つめ
	    のトークンは<a href="#escaping">エスケープ</a>されている。
	    ひとつめのトークンは以下のパターンがある。
	    <ul>
	      <li>"P" の时。<br>
		  チャット信息。自分の信息も含まれる。とり
		  あえずローカルエコーはなし。例は以下のとおり
		  <pre>
		  "P|哈哈哈"
		  </pre>
	      <li>"F"の时。<br>
		  <pre>
		  F|charaindex|信息
		  </pre>
		  吹き出し文字の信息。指定されたcharaindexに吹き出しが出て
		  信息を表示する。<br>
	    </ul>
	<dt>color
	<dd>キャプション色。色の定义は?
	<dt>fontsize
	<dd>フォントの色。定义はクライアントのTKプロトコルを参照。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TK] ) == 0 ){
		int index;
		char* message;
		int color;
		int fontsize;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		color = nrproto_demkstr_int( nrproto.token_list[3] );
		fontsize = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_TK_recv( fd,index,message,color,fontsize);
		return 0;
	}
/*
    <LI><a name="SC_STK"><font color=blue>servertoclient STK( string message);<br></font></a>
	システム信息。クライアントは、仕样书通り、フォントは明朝体、サイズは普通、黄色で表示する。<br>
      <dl>
	<dt>string
	<dd>喋った内容。 <a href="#escaping">エスケープ</a>されている。<br>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_STK] ) == 0 ){
		char* message;
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_STK_recv( fd,message);
		return 0;
	}
/*
  <LI><a name="SC_CP"><font color=blue>servertoclient CP( string data );<bR></font></a>
    (Character Parameter)<br>
    <br>
	    bitfield hp maxhp mp maxmp str tough exp maxexp exp 
	    level attackpower defencepower 
	    fixquick fixcharm fixluck fixfireat fixwaterat fixearthat fixwindat
	    gold 付けている
	    称号のindex 名称 自己称号 <br>
	    デリミタは '|' である。また名称と自己称号は、
	    <a href="#escaping">エスケープ</a>したものを
	    たて棒でつなげたものが通信される。<br>
	    それぞれの值の型は以下。<br>
	    bitfieldは，各要素のビットである。０ビット目だけ特别で，そこが立っていると
	    全てのパラメータを送信する事とする。
	    それ以外の时は，各ビットに对应するパラメータが送信される。
	    １ビット目以上にビットの立っているもののパラメータが顺番に入って送信される。
	    例えば，６が来たらhpとMaxhpが bitfield 以降のトークンに纳められている。<br>
	    色々な都合で(cnv10to62のせい)で、全部で31bitしか使えない。现在まんたん。<br>
	    <table border>
	      <tr><td>bitfield</td><td>int</td></tr>
	      <tr><td>Hp(体力)hp</td><td>int</td></tr>
	      <tr><td>MaxHp</td><td>int</td></tr>
	      <tr><td>ForcePoint</td><td>int</td></tr>
	      <tr><td>MaxForcePoint</td><td>int</td></tr>
	      <tr><td>Vital(体力)</td><td>int</td></tr>
	      <tr><td>Str(腕力)</td><td>int</td></tr>
	      <tr><td>Tough(丈夫さ)</td><td>int</td></tr>
	      <tr><td>Quick(素早さ)</td><td>int</td></tr>
	      <tr><td>Magic(魔法力)</td><td>int</td></tr>
	      <tr><td>fStamina(スタミナ。技术系パラメータ)</td><td>int</td></tr>
	      <tr><td>fDex(器用さ。技术系パラメータ)</td><td>int</td></tr>
	      <tr><td>fIntelligence(かしこさ。技术系パラメータ)</td><td>int</td></tr>
	      <tr><td>Exp(经验值)exp</td><td>int</td></tr>
	      <tr><td>NextExp(次までの经验值)</td><td>int</td></tr>
	      <tr><td>Level(等级)</td><td>int</td></tr>
	      <tr><td>Attack(攻击力)</td><td>int</td></tr>
	      <tr><td>Defense(守备力)</td><td>int</td></tr>
	      <tr><td>fAgility(最終素早さ)</td><td>int</td></tr>
	      <tr><td>fMagic(最終魔法力)</td><td>int</td></tr>
	      <tr><td>fRecovery(回复力)</td><td>int</td></tr>
	      <tr><td>fCharm(魅力)</td><td>int</td></tr>
	      <tr><td>fFame(名声)</td><td>int</td></tr>
	      <tr><td>fEarth(????)</td><td>int</td></tr>
	      <tr><td>fWater(????)</td><td>int</td></tr>
	      <tr><td>fFire(????)</td><td>int</td></tr>
	      <tr><td>fWid(??÷)</td><td>int</td></tr>
	      <tr><td>Gold(お金)</td><td>int</td></tr>
	      <tr><td>称号のindex</td><td>int</td></tr>
	      <tr><td>名称</td><td>文字列</td></tr>
	      <tr><td>自己称号</td><td>文字列</td></tr>
	    </table>
	    <br>
	    具体例( char の配列そのまま书く )<br>
	    <code>
	    P10|20|10|20|10|10|10|1|2|13|13|1|100|10|へんぱ|abc|def
	    </code>
	    <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CP] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_CP_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_CP2"><font color=blue>servertoclient CP2( string data );<bR></font></a>
    (Character Parameter2)<br>
	CPだけでは足りなかったので增えました。
    <br>
	    bitfield Crirical Counter Hitrate Avoid Poison Sleep Stone Drunk Confusion Amnesia
	    Duel Injury Tribe Stun RebirthLevel RebirthGraphic
	    <br>
	    デリミタは '|' である。また名称と自己称号は、
	    <a href="#escaping">エスケープ</a>したものを
	    たて棒でつなげたものが通信される。<br>
	    それぞれの值の型は以下。<br>
	    bitfieldは，各要素のビットである。０ビット目だけ特别で，そこが立っていると
	    全てのパラメータを送信する事とする。
	    それ以外の时は，各ビットに对应するパラメータが送信される。
	    １ビット目以上にビットの立っているもののパラメータが顺番に入って送信される。
	    例えば，６が来たらクリティカル修正とカウンター修正が bitfield 以降のトークンに纳められている。<br>
	    色々な都合で(cnv10to62のせい)で、全部で31bitしか使えない。<br>
	    <table border>
	      <tr><td>bitfield</td><td>int</td></tr>
	      <tr><td>クリティカル修正</td><td>int</td></tr>
	      <tr><td>カウンター修正</td><td>int</td></tr>
	      <tr><td>命中率修正</td><td>int</td></tr>
	      <tr><td>回避率修正</td><td>int</td></tr>
	      <tr><td>毒修正</td><td>int</td></tr>
	      <tr><td>眠り修正</td><td>int</td></tr>
	      <tr><td>石化修正</td><td>int</td></tr>
	      <tr><td>酔い修正</td><td>int</td></tr>
	      <tr><td>混乱修正</td><td>int</td></tr>
	      <tr><td>记忆丧失修正</td><td>int</td></tr>
	      <tr><td>デュエルポイント</td><td>int</td></tr>
	      <tr><td>ケガ(0～100)</td><td>int</td></tr>
	      <tr><td><a href="#tribe">种族</a></td><td>int</td></tr>
	      <tr><td>気絶ポイント(0～5)</td><td>int</td></tr>
	      <tr><td>リバース等级</td><td>int</td></tr>
	      <tr><td>リバースグラフィック番号</td><td>int</td></tr>
	      <tr><td>颜グラフィック番号</td><td>int</td></tr>
	      <tr><td>スキルスロット数</td><td>int</td></tr>
	    </table>
	    <br>
	    具体例( char の配列そのまま书く )<br>
	    <code>
	    P10|20|10|20|10|10|10|1|2|13|13|1|100|10|へんぱ|abc|def
	    </code>
	    <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CP2] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_CP2_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_KP"><font color=blue>servertoclient KP( int number,string data );<bR></font></a>
    (Kyouyuu Parameter)<br>
    <br>
	恐竜パラメータ<br>
	<br>
	    hp maxhp mp maxmp str tough exp 
	    level attackpower deffencepower 
	    fixquick fixcharm fixluck fixfireat fixwaterat fixearthat fixwindat
	    名称 status<br>
	    number を指定して、どの恐竜かを指定すること。
	    最初のトークンに0が来たらそのペット栏は无いと言う事。
	    ある场合は１である。1だと全パラメータ。
	    2以上だと，各ビットの立っているもののパラメータ（1bit 目 hp  2bit 目maxhpなど）
	    が送信される。<br>
	    デリミタは '|' である。また名称と自己称号は、
	    <a href="#escaping">エスケープ</a>したものを
	    たて棒でつなげたものが通信される。<br>
	    それぞれの值の型は以下。
	    <table border>
	      <tr><td>imagenumber(画像番号)</td><td>int</td></tr>
	      <tr><td><a href="#tribe">tribe(种族)</a></td><td>int</td></tr>
	      <tr><td>Hp(耐久力)</td><td>int</td></tr>
	      <tr><td>MaxHp(最大耐久力)</td><td>int</td></tr>
	      <tr><td>ForcePoint</td><td>int</td></tr>
	      <tr><td>MaxForcePoint</td><td>int</td></tr>
	      <tr><td>Exp(经验值)</td><td>int</td></tr>
	      <tr><td>MaxExp(次までの经验值)</td><td>int</td></tr>
	      <tr><td>Level(等级)</td><td>int</td></tr>
	      <tr><td>Attack(攻击力)</td><td>int</td></tr>
	      <tr><td>Defense(守备力)</td><td>int</td></tr>
	      <tr><td>Agility(素早さ)</td><td>int</td></tr>
	      <tr><td>Magic(魔力)</td><td>int</td></tr>
	      <tr><td>Recovery(回复力)</td><td>int</td></tr>
	      <tr><td>Loyalty(忠诚度)</td><td>int</td></tr>
	      <tr><td>fEarth(????)</td><td>int</td></tr>
	      <tr><td>fWater(????)</td><td>int</td></tr>
	      <tr><td>fFire(????)</td><td>int</td></tr>
	      <tr><td>fWid(??÷)</td><td>int</td></tr>
	      <tr><td>Slot(最大石版数)</td><td>int</td></tr>
	      <tr><td>名称变更许可フラグ</td><td>int</td></tr>
	      <tr><td>名称</td><td>文字列</td></tr>
	      <tr><td>ユーザーペット名</td><td>文字列</td></tr>
	      <tr><td>ペットの战闘参加状态</td><td>0～2</td></tr>
	      <tr><td>ペットの怪我(0～100)</td><td>int</td></tr>	  
	    </table>
	    <br>
	    名称变更许可フラグとは，このペットの名称を变更してよいかどうかのフラグで，
	    1 だと变更ＯＫ，０だと变更不可となる。<br>
	    ペットの战闘参加状态は、<a href="#CS_KS">KSプロトコル</a>を参考の事。<br>
	    <br>
		<hr>
		<br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_KP] ) == 0 ){
		int number;
		char* data;
		number = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_KP_recv( fd,number,data);
		return 0;
	}
/*
  <LI><a name="SC_KP2"><font color=blue>servertoclient KP2( int number,string data );<bR></font></a>
    (Kyouyuu Parameter)<br>
    <br>
	恐竜パラメータ２<br>
	<br>
	    vital str tough quick magic Crirical Counter Hitrate Avoid Poison Sleep Stone Drunk Confusion Amnesia
	<br>
	    名称 status<br>
	    number を指定して、どの恐竜かを指定すること。
	    最初のトークンに0が来たらそのペット栏は无いと言う事。
	    ある场合は１である。1だと全パラメータ。
	    2以上だと，各ビットの立っているもののパラメータ（1bit 目 hp  2bit 目maxhpなど）
	    が送信される。<br>
	    デリミタは '|' である。また名称と自己称号は、
	    <a href="#escaping">エスケープ</a>したものを
	    たて棒でつなげたものが通信される。<br>
	    それぞれの值の型は以下。
	    <table border>
	      <tr><td>Vital(体力)</td><td>int</td></tr>
	      <tr><td>Str(腕力)</td><td>int</td></tr>
	      <tr><td>Tough(丈夫さ)</td><td>int</td></tr>
	      <tr><td>Quick(素早さ)</td><td>int</td></tr>
	      <tr><td>Magic(魔法力)</td><td>int</td></tr>
	      <tr><td>クリティカル修正</td><td>int</td></tr>
	      <tr><td>カウンター修正</td><td>int</td></tr>
	      <tr><td>命中率修正</td><td>int</td></tr>
	      <tr><td>回避率修正</td><td>int</td></tr>
	      <tr><td>毒修正</td><td>int</td></tr>
	      <tr><td>眠り修正</td><td>int</td></tr>
	      <tr><td>石化修正</td><td>int</td></tr>
	      <tr><td>酔い修正</td><td>int</td></tr>
	      <tr><td>混乱修正</td><td>int</td></tr>
	      <tr><td>记忆丧失修正</td><td>int</td></tr>
	      <tr><td>登録番号</td><td>ペットを手に入れた顺番に、番号が割り振られる。クライアントはこの番号の大きい顺にペットを并べなおさないといけない。番号は、飞び飞びになっている场合もある。</td></tr>
	    </table>
	    <br>
	    名称变更许可フラグとは，このペットの名称を变更してよいかどうかのフラグで，
	    1 だと变更ＯＫ，０だと变更不可となる。<br>
	    ペットの战闘参加状态は、<a href="#CS_KS">KSプロトコル</a>を参考の事。<br>
	    <br>
		<hr>
		<br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_KP2] ) == 0 ){
		int number;
		char* data;
		number = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_KP2_recv( fd,number,data);
		return 0;
	}
/*
  <LI><a name="SC_PP"><font color=blue>servertoclient PP( string data );<br></font></a>
    (Party member Parameter)<br>
	体力バー表示用の布ティメンバーのパラメータ送信プロトコル。<br>
      <dl>
	<dt>string data
	<dd>objindex|LP|最大LP|FP|最大FP|??????このデータが布ティの先头から人数分入っている。<br>
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PP] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_PP_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_C"><font color=blue>servertoclient C( string data );<bR></font></a>
      (Characters)
	他のオブジェクトの情报。步いて现れる他のキャラ等の情报が送られてくる。
      <br><br>
      <dl>
	<dt>string data
	<dd>フォーマットは 以下の3种类の项目をコンマでならべた
	    ものである.それぞれの项目の中身はさらにたて棒'|'でくぎられ
	    ている。
	    <ul>
	      <li>たて棒でくぎられたトークンが15个の场合、このオブジェクトはキャラクターであるということである。<br>
		  トークンの内容は
		  <code>WHICHTYPE|CHARINDEX|X|Y|DIR|BASEIMG|LEVEL|NAMECOLOR|NAME|(SELFTITLE|GUILDNAME|GUILDTITLE)|WALKABLE|HEIGHT|POPUPNAMECOLOR|TITLE</code>
		  WHICHTYPE は，このキャラクターがどういった种类のもの
		  であるか。これはサーバーでは以下のように定义されている。<br>
		  <pre>
		typedef enum
		{
		    CHAR_TYPENONE,          何でもない
		    CHAR_TYPEPLAYER,        プレイヤー
		    CHAR_TYPEENEMY,         ????
		    CHAR_TYPEPET,           ペット
		    CHAR_TYPEDOOR,          ドア
		    CHAR_TYPEBOX ,          宝箱
		    CHAR_TYPEMSG ,          看板
		    CHAR_TYPEWARP ,         ワープゾーン
		    CHAR_TYPESHOP ,         ????
		    CHAR_TYPEHEALER ,       ヒーラー
		    CHAR_TYPEOLDMAN ,       长老
		    CHAR_TYPEROOMADMIN,     不动产屋
		    CHAR_TYPETOWNPEOPLE,    まちのひと
		    CHAR_TYPEDENGON,        伝言版
		    CHAR_TYPEADM,           伝言版
		    CHAR_TYPETEMPLE,        Temple master
		    CHAR_TYPESTORYTELLER,   语り部
		    CHAR_TYPERANKING,       不动产ランキング表示ＮＰＣ
		    CHAR_TYPEOTHERNPC,      その他の检索对象にならないNPC
		    CHAR_TYPEPRINTPASSMAN,  ドアのパスワード表示するNPC
		    CHAR_TYPENPCENEMY,      固定敌
		    CHAR_TYPEACTION,        アクションに反应するNPC
		    CHAR_TYPEWINDOWMAN,     ウィンドウ表示するNPC（テストかも)
		    CHAR_TYPESAVEPOINT,     セーブポイント
		    CHAR_TYPEWINDOWHEALER,  ウインドウ种类のヒーラー
		    CHAR_TYPEITEMSHOP,	    お店
		    CHAR_TYPESTONESHOP,	    石盘屋（ペットの技屋）
		    CHAR_TYPEDUELRANKING,   DUELランキングNPC
		    CHAR_TYPEWARPMAN,	    ワープマンNPC
		    CHAR_TYPEEVENT,	    イベントNPC
		    CHAR_TYPEMIC,	    イベントNPC
		    CHAR_TYPELUCKYMAN,	    イベントNPC
		    CHAR_TYPEBUS,	    マンモスバス
		    CHAR_TYPECHARM,	    イベントNPC
		    CHAR_TYPENUM,
		}CHAR_TYPE;
		  </pre>
		  それは他のプレイヤーやNPCや敌である。サーバーが送信
		  するときは见える范围について全部送信する。つま
		  りクライアントは、このパケットを受けとったときにこの
		  パケットに书かれていないキャラを持っていたら消してし
		  まってよいということである。また、マウスカーソルをあ
		  わせたときに表示する情报はこの情报のみに基いている。
		  だから、マウスカーソルをあわせたときに表示することが
		  变更された场合は、サーバーは、この关数を能动的に呼び
		  ださなければならない。 SELFTITLEについては、自分でつ
		  けたタイトル以外に家族名と家族称号が含まれて
		  おり、それぞれエスケープしたあとにデリミタであるたて
		  棒をはさんで连结したあと、さらにエスケープされている。
		  クライアントはアクションのコマンド(CA)がくるまでは立
		  ちで表示する。CHARINDEXサーバー内の一意にキャラを特
		  定できる番号、BASEIMGは表示のための番号、LEVELはキャ
		  ラの等级(0なら表示しない。この值はNPCなどに使う。)
		  WALKABLEは1のときその上を通过することができ、0なら通
		  过することができない。HEIGHTは高さをもつものかそうで
		  ないのかの指定。
		  キャラクターの名称と自由称号と称号は、<a href="#escaping">
		  エスケープ</a>されなければならない。'|'でトークンを
		  取りだしてからエスケープを解除する。エスケープすると'
		  |'がほかの文字におきかわるので、最初は单纯に'|'をデ
		  リミタとしてよい。送信する方も、名称と自由称号をエス
                  ケープしてからたて棒でつないでから送信する。
                  また，ペットの场合は自由称号の代わりにユーザーが设定
                  したペットの名称が送信されてくる。<br>
	      <li>キャラクタがペットを连れ步いていたり等、付加情报が追加される场合、トークンの数は16个以上になる。<br>
		  トークンの内容は
		  <code>WHICHTYPE|CHARINDEX|X|Y|DIR|BASEIMG|LEVEL|NAMECOLOR|NAME|(SELFTITLE|GUILDNAME|GUILDTITLE)|WALKABLE|HEIGHT|POPUPNAMECOLOR|TITLE|PETDATABIT|??????|PROFILEDATA</code><br>
		  PETDATABIT は、これ以降どのようなペット情报が入っているかのビットデータである。以下はビットデータと、对应する内容。
		  <table border>
		    <tr><td>ビット番号</td><td>トークン名</td><td>データの内容</td></tr>
		    <tr><td>0x01</td><td>HEADGEARIMG</td><td>被り物のグラフィック番号</td></tr>
		    <tr><td>0x02</td><td>RIDEPETIMG</td><td>ライドしているペットのグラフィック番号</td></tr>
		    <tr><td>0x04</td><td>PETIMG|PETDIR|PETLEVEL|PETNAMECOLOR|PETNAME</td><td>连れ歩きペットの情报</td></tr>
		    <tr><td></td><td></td><td></td></tr>
		</table>
		  これらのデータが、设定された数だけ连结して送られてくる。<br><br>
		  ▲PROFILEDATA は、下记のようなユーザのプロフィールデータである<br>
			<code>SELLFLG|SELLMSG|BUYFLG|BUYMSG|ABOUTFLG|ABOUTMSG|PROFILEMSG|BEGINNERFLG</code><br><br>

	      <Li>たて棒でくぎられたトークンが7个の场合<br>
		  トークンの内容は
		  <code>INDEX|X|Y|BASEIMG|LEVEL|ITEM1LINEINFO|ITEMNUM</code>
		  で地面に落ちているアイテムについての情报である。
		  INDEXはキャラのインデックスとかぶらないINDEXである。
		  そのアイテムを消す时に使用する。X,Yはアイテムのグロー
		  バル位置。BASEIMGは画像の番号。ITEM1LINEINFOは1行
		  infoに表示するための情报である。アイテムウインドウ内
		  の表示用の情报は别の方法で用意する。アイテムに关して
		  はCAは来ない。ITEM1LINEINFOは<a href="#escape">エスケー
		  プ</a>される。このエスケープの方法は上の项目を参照。
		  ITEMNUMはその个数である。0の场合は、スタックアイテムでは无いので
		  表示しない。
	      <Li>たて棒でくぎられたトークンが4个の场合<br>
		  トークンの内容は
		  <code>INDEX|X|Y|VALUE</code>
		  で地面に落ちているお金についての情报である。内容はす
		  べて数字。INDEX,X,Y はアイテムと同じ。VALUE はどれだ
		  けの量かという事である。アイテムについての情报である。
	      <li>たて棒でくぎられたトークンが1个の场合<br>
		  <code>INDEX</code>
		  このキャラのCは教えられない。
	    </ul>
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_C] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_C_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_CN"><font color=blue>servertoclient CN( int number, string data );<bR></font></a>
	<br>
	仲间になっている人の情报を送信する。<br>
	<dl>
	<dt>int number</dt>
	<dd>何番目の仲间か。０～３</dd>
	<dt>string data</dt>
	<dd>送信データ。
	    <code>kubun|objindex | level | maxhp | hp | fp | name</code>
	    <br>
	    kubun はその栏があるか无いか。０だと无い。それ以降にトークンすらない。１全パラメータ。<br>
	    2以上だと，各ビットの立っているもののパラメータ（1bit 目 level 2bit 目charaindexなど）
	    が送信される。<br>
	    objindex は仲间のobjindex<br>
	    levelはその人の等级。<br>
	    maxhpはその人のMAXHP<br>
	    hpはその人の现在のHP<br>
	    fpはその人の精神力<br>
	    name はその人の名称。<br>
	<br><br>
	</dl>
	<br><br><hr>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CN] ) == 0 ){
		int number;
		char* data;
		number = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CN_recv( fd,number,data);
		return 0;
	}
/*
  <LI><a name="SC_TITLE"><font color=blue>servertoclient TITLE( string data );<bR></font></a>
  <dl>
  <dt>string data
  <dd>称号<br><br>
	    (称号0)|id|titleID|(称号1)|id|titleID|(称号2)|id|titleID| ... (称号n)|id|titleID|<br><br>
	    称号のエントリが空の场合は '|' が连続するので、连続したも
	    のを省略してスキャンしてはならない。かならず最大个数分送信
	    する。<br>
	    ひとつ、ひとつの内容は、<br>
	    名称|ID|titleID<br>
	    である。<br>
		IDとは、１つ１つの称号を取得した时にindexを振っている。
		小さい顺に、古い顺に并んでいるが、サーバーから送信される称号の顺番は
		IDではソートされていなく、また间が空いている时もある。クライアントは
		この称号をIDにてソート、空白行削除などの并び替えなどをしなくてはならない。<br>
		titleID はそれぞれのタイトルに振られたＩＤ番号である。<br>
		タイトルを识别するときにはこのＩＤを使って识别する。<br>
		<br>
	    具体例( 送られてくる char の配列そのままである )<br>
	    <code>
	    あほ|1|16|よっぱらい|2|119|
	    </code>
	    これも<a href="#escaping">エスケープ</a>する。<br>
	<br>
	</dl>
	<br><br><hr>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TITLE] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_TITLE_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_CA"><font color=blue>servertoclient CA( string data );<br></font></a>
      (CharacterAction)
      见える范围にいるキャラのアクション状态を更新する。
      サーバーからクライアントに一方的に送信する。
      各キャラの1アクションごとに送信。サーバーはアクションをできるだ
      け压缩すること。<br><br>
      <dl>
	<dt>string data
	<dd>CHARINDEX|X|Y|ACTION|PARAM1|PARAM2|PARAM3|PARAM4|....をコ
	    ンマでくぎったものにする。PARAMはアクションごとに个数も
	    使いかたもことなる。以下はアクション一覧。X,Yは位置でど
	    のアクションでも、あたらしい位置を指定する。
	    この文字列はエスケープされない。
	    <table border>
	      <tr><td>ACTION</td><td>PARAM1</td><td>PARAM2</td>
	      <td>PARAM3</td><td>PARAM4</td></tr>
	      <tr><td>Stand:0</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>Walk:1</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>BeginRun:2（走りはじめ）</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>Running:3（走っている）</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>EndRun:4（走りおわり）</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>Attack:5</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>UseMagic:6</td><td>方向0~7</td></tr>
	      <tr><td>Throw:7</td><td>方向0~7</td><td></td><td></td><td></td></tr>
	      <tr><td>Damage:8</td><td>方向0~7</td><td></td><td></td></tr>
	      <tr><td>Guard:9 (防御)</td><td>方向0~7</td></tr>
	      <tr><td>Down:10 (倒下??気絶)</td><td>方向0~7</td></tr>
	      <tr><td>Sit:11 (座る)</td><td>方向0~7</td></tr>
	      <tr><td>Hand:12 (挥手)</td><td>方向0~7</td></tr>
	      <tr><td>Pleasure:13 (喜ぶ)</td><td>方向0~7</td></tr>
	      <tr><td>Angry:14 (怒る)</td><td>方向0~7</td></tr>
	      <tr><td>Sad:15 (悲伤)</td><td>方向0~7</td></tr>
	      <tr><td>nod:16 (点头)</td><td>方向0~7</td></tr>
	      <tr><td>Guu:17 (じゃんけん)</td><td>方向0~7</td></tr>
	      <tr><td>Choki:18 (じゃんけん)</td><td>方向0~7</td></tr>
	      <tr><td>Paa:19 (じゃんけん)</td><td>方向0~7</td></tr>
	      <tr><td>actionwalk:30 (アクション用歩き)</td><td>方向0~7</td></tr>
	      <tr><td>actionstand:31 (アクション用立ちポーズ)</td><td>方向0~7</td></tr>
	      <tr><td>Battle:40 (战闘情报)</td><td>方向0~7</td><td>BattleNo(-1 なら表示消す）</td><td>SideNo</td><td>HelpNo（１なら助けを呼ぶCA表示，０なら消す，または无し）</td></tr>
	      <tr><td>Leader:41 (リーダー情报)</td><td>方向0~7</td><td>0:表示消す 1:表示</td></tr>
	      <tr><td>Watch:42 (战闘観战)</td><td>方向0~7</td><td>0:表示消す 1:表示</td></tr>
	      <tr><td>namecolor:43 (名称の色情报)</td><td>方向0~7</td><td>名称の色番号</td></tr>
	      <tr><td>Duel:44 (DUELマーク)</td><td>方向0~7</td><td>0:表示消す 1:表示</td></tr>
	      <tr><td>Injury:45 (怪我マーク)</td><td>方向0~7</td><td>0:表示消す 1:表示</td></tr>
	      <tr><td>SkillIcon:46 (スキル使用中マーク)</td><td>方向0~7</td><td>0:表示消す 1～:スキルアイコン番号</td></tr>
	      <tr><td>LevelUp:47 (等级アップマーク)</td><td>方向0~7</td><td></td><td></td><td>注：一定时间后に自动で消去すること</td></tr>
	      <tr><td>PetInjury:48 (ペット怪我マーク)</td><td>方向0~7</td><td>0:表示消す 1:表示</td></tr>
	      <tr><td>PetLevelUp:49 (ペット等级アップマーク)</td><td>方向0~7</td><td></td><td></td><td>注：一定时间后に自动で消去すること</td></tr>
	      <tr><td>Turn:50 (方向变换)</td><td>方向0~7</td><td></td></tr>
	      <tr><td>Warp:51 (ワープ)</td><td>方向0~7</td><td></td></tr>
	      <tr><td>Graphic:60 (グラフィック番号变更)</td><td>方向0~7</td><td>新グラフィック番号</td></tr>
	      <tr><td>RebirthOn:61 (リバース使用)</td><td></td><td></td><td></td><td>注：ＡＣでのみ使用</td></tr>
	      <tr><td>RebirthOff:62 (リバース中止)</td><td></td><td></td><td></td><td>注：ＡＣでのみ使用</td></tr>
	      <tr><td>SkillUse:63 (スキル使用)</td><td></td><td></td><td></td><td>注：ＡＣでのみ使用</td></tr>
	      <tr><td>SkillUseStop:62 (スキル使用中止)</td><td></td><td></td><td></td><td>注：ＡＣでのみ使用</td></tr>
	      <tr><td>PetAction:100 (ペットアクション)</td><td>以下に、ペットのアクションが通常の CA と同じように入る。</td><td></td><td></td><td></td></tr>
	</table>
	ACTIONの值は整数で、内容は上の表の左端の项目である。
  </dl>
  <br>
  <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CA] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_CA_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_CD"><font color=blue>servertoclient CD( string data );<bR></font></a>
      (CharacterDelete)
	data はデリミタ（,）で区切られたインデックス。
	复数件送る事が出来る。
      このIDを持っているキャラクタが消えた时にサーバからクライアントへ
      と伝わる。
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CD] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_CD_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_CJ"><font color=blue>servertoclient CJ( int jobassort, string jobname,  );<br></font></a>
	(Character Job)
	プレーヤーのキャラクタの职业情报が送られてくる。
	<br>
	<dl>
	  <dt>int jobassort
	  <dd>职业の区分。0が何も无し。1：战闘系 2：生产系
	  <dt>string jobname
	  <dd>职业の名称
	</dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CJ] ) == 0 ){
		int jobassort;
		char* jobname;
		jobassort = nrproto_demkstr_int( nrproto.token_list[1] );
		jobname = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CJ_recv( fd,jobassort,jobname);
		return 0;
	}
/*
  <LI><a name="SC_CS"><font color=blue>servertoclient CS( string data );<br></font></a>
	(Character Skill)
	プレーヤーのキャラクタのスキル情报が送られてくる。
	复数データ送信する事が出来る。１データのトークンの数は决まっている为，
	デリミタは不必要である。
	１スキルのデータは，"|"で区切られている。フォーマットは以下のとおり。<br>
	なお，スキルの名称以下全てに何もデータが无い场合は，そのデータは存在しないという事で，
	クライアントはその个所の表示を消さなければならない。
	<br>
	<table border=1>
	<tr><td>スキルの场所</td><td>　</td></tr>
	<tr><td>スキルのID</td><td>　</td></tr>
	<tr><td>スキルの名称</td><td>　</td></tr>
	<tr><td>スキル等级</td><td>　</td></tr>
	<tr><td>スキル经验值</td><td>　</td></tr>
	<tr><td>MAXスキル等级</td><td>　</td></tr>
	<tr><td>等级アップする经验值</td><td>　</td></tr>
	<tr><td>必要スキルスロット</td><td>　</td></tr>
	<tr><td>オペレーション分类ー</td><td>スキルのオペレーションが入っている。<br>
	０:战闘。<br>
	１:生产系 オペレーション分类ーA<br>
	２:生产系 オペレーション分类ーB<br>
	３:生产系 オペレーション分类ーC<br>
	４:生产系 オペレーション分类ーD<br>
	????</td></tr>
	<tr><td>FP消费率</td><td>技の消费 FPにこれをn/100したものを挂けると最終消费FPとなる。</td></tr>
	<tr><td>登録番号</td><td>スキルを覚えた顺番に、番号が割り振られる。クライアントはこの番号の大きい顺にスキルを并べなおさないといけない。番号は、飞び飞びになっている场合もある。</td></tr>
	<tr><td>使用可能スキル等级</td><td>现在使用できるスキルの最大等级</td></tr>
	</table>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CS] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_CS_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_CT"><font color=blue>servertoclient CT( int skillindex, string data );<br></font></a>
	(Character Technique)
	プレーヤーの技情报が送られてくる。
	skillindexに属する技が全て送られる。技情报は，ログイン，等级アップ时等くらいしかあまり送られないのと，
	技一つ一つを送信する必要はそんなにないので，スキル每に送信するようにした。
	<dl>
	  <dt>int skillindex
	  <dd>スキルの场所を示す。
	  <dt>string data
	  <dd>技情报。デリミタ"|"で区切られている。各トークンの意味は以下のとおり。これが技の数だけ分存在する。<br>
	  <br>
	  <table border=1>
	    <tr><td>技名</td></tr>
	    <tr><td>技のコメント</td></tr>
	    <tr><td>消费フォースポイント</td></tr>
	    <tr><td>使える场所</td></tr>
	    <tr><td>选择出来るターゲット<a href="#target">ここ</a>を参考。<br>このデータの12Bit目から、技の等级情报が插入されている。</td></tr>
	    <tr><td>使用できるかどうかのフラグ</td></tr>
	  </table>
	</dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CT] ) == 0 ){
		int skillindex;
		char* data;
		skillindex = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CT_recv( fd,skillindex,data);
		return 0;
	}
/*
  <LI><a name="SC_PT"><font color=blue>servertoclient PT( int petindex, string data );<br></font></a>
	(Pet Technique)
	ペットの技情报が送られてくる。
	petindexの场所にいるペットに关する技が全て送られる。
	<dl>
	  <dt>int petindex
	  <dd>ペットの场所を示す。
	  <dt>string data
	  <dd>技情报。デリミタ"|"で区切られている。各トークンの意味は以下のとおり。これが技の数だけ分存在する。<br>
	  <br>
	  <table border=1>
	    <tr><td>技の场所</td></tr>
	    <tr><td>技のID</td></tr>
	    <tr><td>技名</td></tr>
	    <tr><td>技のコメント</td></tr>
	    <tr><td>消费フォースポイント</td></tr>
	    <tr><td>使える场所</td></tr>
	    <tr><td>选择出来るターゲット<a href="#target">ここ</a>を参考。<br>このデータの12Bit目から、技の等级情报が插入されている。</td></tr>
	    <tr><td>登録番号</td><td>ペットを手に入れた顺番に、番号が割り振られる。クライアントはこの番号の大きい顺にペットを并べなおさないといけない。番号は、飞び飞びになっている场合もある。</td></tr>
	  </table>
	</dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PT] ) == 0 ){
		int petindex;
		char* data;
		petindex = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_PT_recv( fd,petindex,data);
		return 0;
	}
/*
  <LI><a name="SC_S"><font color=blue>servertoclient S( string data );<br></font></a>
      (Status)
      キャラの状态を送信する。
      データは 分类记号文字(一文字)内容 となっている。つまり最初の
      1文字を见れば何の状态か分る。内容は2文字目からである。
      内容は以下のフォーマットにしたがう。たて棒记号'|'がデリミタである。
      2个目以降のトークンが内容である。<br>
      <br>
      <UL>
	<br><br>
	<li>J0 ～J6 使える咒术の内容<br><br>
	    使える咒术の内容を送信する。<br>
	    0 ～6 はそれぞれアイテムの装备个所に对应している。一部の装备个所（头，胴，手，装饰品）
	    しか装备出来ない予定だが，今后扩张の为に全ての装备个所の情报を送る<br>
	    <code>J0|kubun|mp|field|target|name|comment</code><br>
	    というフォーマットになっている。<br>
	    kubun はその栏があるか无いか。０だと无い。それ以降にトークンすらない。１だとある。
	    mpは消费气力を表す。<br>
	    fieldはどの场所で使えるか。サーバーでは以下の样に定义されている。<br>
	    <pre>
		typedef enum
		{
			MAGIC_FIELD_ALL,		すべての场所で使える
			MAGIC_FIELD_BATTLE,		战闘中のみ
			MAGIC_FIELD_MAP,		通常マップ上のみ
		}MAGIC_FIELDTYPE;
	    </pre>
	    targetはどれを对象に出来るか。サーバーでは以下のように定义されている。<br>
	    <font size=+1>この数字に100を足すと，死んでいる者も对象となる。</font><br>
	    <br>
	    nameは咒术名。<br>
	    commentはこの咒术の说明。<br>
	<br>
	<LI>W0～W4 ペットの技データ<br><br>
	  <code>W0|skillid|field|target|name|comment| x 7</code><br>
	  W0 ～ W4 はそれぞれのペットに对应している。<br>
	  petskillid は，ペットの技の番号。pet_skillinfo.hに定义されている。<br>
	  field はその技がどこで使用できるか。サーバーでは以下のように定义されている。<br>
	  <pre>
		typedef enum
		{
			PETSKILL_FIELD_ALL,		すべての场所で使える
			PETSKILL_FIELD_BATTLE,		战闘中のみ
			PETSKILL_FIELD_MAP,		通常マップ上のみ
		}PETSKILL_FIELDTYPE;
	  </pre>
	  target はその技の对象がどういうものか。<a href="#target">对象の设定</a>を参考の事。<br>
	  <br>
	  name は技の名称。<br>
	  commentはその技に对する说明。<br>
	  target|name|comment| は技の数の分１行で送られてくる。
	  技は一应７つまで。途中で技が拔けている（"|"のみが続く）场合
	  はクライアントで诘めて表示する事。<br>
	  <br><br>
      </ul>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_S] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_S_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_FS"><font color=blue>servertoclient FS( int flg);<br></font></a>
      (FlgSet)FSの应答。またはログイン时などに自分の状态として送られてくる。<br><br>
      <dl>
        <dt>int flg
	<dd><table border=1>
	    <tr><td>0 bit</td><td>0: 仲间Off            </td><td>1: 仲间On</td></tr>
	    <tr><td>1 bit（现在未使用）</td><td>0: 战闘途中参加Off          </td><td>1: 战闘途中参加On </td></tr>
	    <tr><td>2 bit</td><td>0: DUEL Off           </td><td>1: DUEL On</td></tr>
	    <tr><td>3 bit</td><td>0: 通常チャットモード </td><td>1: 布ティチャットモード</td></tr>
	    <tr><td>4 bit</td><td>0: 名刺交换OK         </td><td>1: 名刺交换拒否</td></tr>
	    <tr><td>5 bit</td><td>0: トレードOK         </td><td>1: トレード拒否</td></tr>
	    <tr><td>6 bit</td><td>0: 家族加入OK       </td><td>1: 家族加入拒否</td></tr>
	</table>
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_FS] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_FS_recv( fd,flg);
		return 0;
	}
/*
  <li><a name="SC_HL"><font color=blue>servertoclient HL( int flg);<br></font></a>
      (HeLp)HLのの应答。または布ティの仲间が战闘のお助けモードを变更した场合に送られてくる。<br><br>
      <dl>
	<dt>int flg
	<dd> 0: お助けモードOff                  1: お助けモードOn<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_HL] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_HL_recv( fd,flg);
		return 0;
	}
/*
  <li><a name="SC_PR"><font color=blue>servertoclient PR( int request, int result);<br></font></a>
      (PartyRequest)仲间要求の应答。PRをクライアントが送っていなくてもこれを受け取る时がある。
      布ティが突然解散（亲が拔けた）などの时。<br>
      <br>
      <dl>
	<dt>int request
	<dd>0: 除队 1:入队
	<dt>int result
	<dd>0: 失败 1: 成功
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PR] ) == 0 ){
		int request;
		int result;
		request = nrproto_demkstr_int( nrproto.token_list[1] );
		result = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PR_recv( fd,request,result);
		return 0;
	}
/*
  <li><a name="SC_GFL"><font color=blue>servertoclient GFL( string data);<br></font></a>
      (Get Frond List)<br>
      目の前キャラのリスト要求に对する返事。クライアントはこの情报をもとに、
      オペレーション分类ーCのリストウィンドウを作成する。
      <br>
      <dl>
	<dt>string data
	<dd>"|"で区切られた文字列。<br>
		キャラの名称|type
		が复数个送られる。
		キャラ名はエスケープされて入っている。type は、0がプレイヤー、1がペットである。<br>
		これらは目の前にいるプレイヤー、ペットのキャラの名称である。
		クライアントは、これよりも手前に、自分、とその仲间をウィンドウに表示しなければならない。
		また、GPDを送信する时、その自分と仲间分を考虑して(番号を＋して)送信する。<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GFL] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GFL_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_GPD"><font color=blue>servertoclient GPD( string data);<br></font></a>
      (Get Pet Data)<br>
      指定されたキャラと持っているペットの情报。
      <br>
      <dl>
	<dt>string data
	<dd>キャラとペットの情报。"|"をデリミタとしたトークン。<br>
	    名称(エスケープされている)|lv|ケガ|HP|MAXHP|FP|MAXFP<br>
	    が人数分送られる。
      </dl>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GPD] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GPD_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_GFLI"><font color=blue>servertoclient GFLI( string data);<br></font></a>
      (Get Frond List Item)<br>
      目の前キャラのリスト要求に对する返事。クライアントはこの情报をもとに、
      オペレーション分类ーCのリストウィンドウを作成する。
      <br>
      <dl>
	<dt>string data
	<dd>"|"で区切られた文字列。<br>
		キャラの名称|type
		が复数个送られる。
		キャラ名はエスケープされて入っている。type は、0がプレイヤー、1がペットである。<br>
		これらは目の前にいるプレイヤー、ペットのキャラの名称である。
		クライアントは、これよりも手前に、自分、とその仲间をウィンドウに表示しなければならない。
		また、GPDを送信する时、その自分と仲间分を考虑して(番号を＋して)送信する。<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GFLI] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GFLI_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_GPDI"><font color=blue>servertoclient GPDI( string data);<br></font></a>
      (Get Pet Data Item)<br>
      指定されたキャラと持っているペットの情报。
      <br>
      <dl>
	<dt>string data
	<dd>キャラとペットの情报。"|"をデリミタとしたトークン。<br>
	    名称(エスケープされている)|lv|ケガ|HP|MAXHP|FP|MAXFP<br>
	    が人数分送られる。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_GPDI] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_GPDI_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_TU"><font color=blue>servertoclient TU( int result, int type, string data);<br></font></a>
      プレイヤーが技を使用した际の、结果をサーバーが返すのに使われる。返さない时もあるので、クライアントはこれをTUの返事として絶对の期待をしてはならない。<br>
      <br>
      <dl>
      <dt>int result
	<dd>成否を返す。0なら失败、1なら成功
	<dt>int type
	<dd>オペレーションの种类。1～4ならオペレーション分类A～D、0ならそれ以外。
	<dt>string data
	<dd>付加情报。技によって内容が异なる。<br>
	オペレーション分类ーAの场合。<br>
	获得skillexp|取得名声值|スキル等级アップしたか( 0 or 1)|变化STM|变化DEX|变化INT|ケガしたかのフラグ(0 or 1)|作成したアイテムの画像番号|失った素材1|...|失った素材6<br>
	失った素材は、０～６つ入っている可能性がある。これらは、エスケープされている。<br>
	作成したアイテムの画像番号は、失败した时には-1が入っている.<br>
	オペレーション分类ーBの场合<br>
	获得skillexp|取得名声值|スキル等级アップしたか( 0 or 1)|变化STM|变化DEX|变化INT|ケガしたかのフラグ(0 or 1)<br>
	オペレーション分类ーCの场合<br>
	获得skillexp|取得名声值|スキル等级アップしたか( 0 or 1)|变化STM|变化DEX|变化INT|スキル效果变化量<br>
		スキル效果变化量とは、变身、变装ならその有效步数、手当てならLP回复量などである。<br>
		通常、治疗の场合はこの值は１が返ってくる。<br>
		ただし、すでに健康状态で合った场合には０が返ってくる。さらにその场合はresult は０ある。<br>
		加えて治疗のは失败で魂が拔ける事があるがその场合は２が返ってくる。<br>
		手当ての场合にもすでに体力が满タンの场合は０が返ってくる。その场合は result は０である。
	オペレーション分类ーDの场合<br>
	获得skillexp|取得名声值|スキル等级アップしたか( 0 or 1)|变化STM|变化DEX|变化INT|取得アイテム画像番号|取得アイテム名<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TU] ) == 0 ){
		int result;
		int type;
		char* data;
		result = nrproto_demkstr_int( nrproto.token_list[1] );
		type = nrproto_demkstr_int( nrproto.token_list[2] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		nrproto_TU_recv( fd,result,type,data);
		return 0;
	}
/*
  <li><a name="SC_TRPL"><font color=blue>servertoclient TRPL( string data);<br></font></a>
      (TRade Player List) トレード可能なプレイヤーのリスト。<br>
      <br>
      <dl>
	<dt>string data
	<dd>トレード可能なプレイヤーの名称がエスケープされ、复数人いる时はデリミタ(|)でくくられて送信されてくる。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRPL] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_TRPL_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_TRS"><font color=blue>servertoclient TRS( string name, int level);<br></font></a>
      (TRade Start) 他のプレイヤーがトレードしようと言ってきた。クライアントはトレードウィンドウを出さなければならない。<br>
      <br>
      <dl>
	<dt>string name
	<dd>トレードを要求してきたプレイヤーの名称がエスケープされて入っている。
	<dt>int level
	<dd>相手プレイヤーの等级。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRS] ) == 0 ){
		char* name;
		int level;
		name = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		level = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_TRS_recv( fd,name,level);
		return 0;
	}
/*
  <li><a name="SC_TROP"><font color=blue>servertoclient TROP( void);<br></font></a>
      (TRade Open) 相手がトレードしたいアイテムを选择して、Openした。これらは、取り引きしたいアイテムやペットの内容の后に送信される。クライアントは、相手のOpen 状态を选择状态にしなければならない。
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TROP] ) == 0 ){

		nrproto_TROP_recv( fd);
		return 0;
	}
/*
  <li><a name="SC_TRLI"><font color=blue>servertoclient TRLI( string data);<br></font></a>
      (TRade List Item) 相手のトレードしたいアイテムの内容。<br>
      <br>
      <dl>
	<dt>string data
	<dd>相手のトレードしたいアイテムの内容を示す。复数个が１つの文字列で送信されてくることもある。内容は<a href="#SC_I">I</a>プロトコルのものとほぼ同じ。先头のトークンが、アイテムの场所ではなく、トレードウィンドウで何番目かを示す数字が入っている。<br>
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRLI] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_TRLI_recv( fd,data);
		return 0;
	}
/*
  <li><a name="SC_TRLG"><font color=blue>servertoclient TRLG( int gold);<br></font></a>
      (TRade List Gold) 相手のトレードしたいお金の金额。<br>
      <br>
      <dl>
	<dt>int gold
	<dd>トレードしたいお金の金额を示す。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRLG] ) == 0 ){
		int gold;
		gold = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_TRLG_recv( fd,gold);
		return 0;
	}
/*
  <li><a name="SC_TRLP"><font color=blue>servertoclient TRLP( int petplace, string data);<br></font></a>
      (TRade List Item) 相手のトレードしたいペットの内容。<br>
      <br>
      <dl>
	<dt>int petplace
	<dd>トレードウィンドウで何番目のペットか。
	<dt>string data
	<dd>トレードしたいペットの内容を示す。以下の顺でデータが入っている。<br>名称|等级|种类|最大体力|最大魔力|Vital|Strength|Tough|Quick|Magic|忠诚度|スロット数|モンスター种族名|画像ナンバー|ATK|DEF|AGL|MND|RCV|属性地|属性水|属性火|属性风
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRLP] ) == 0 ){
		int petplace;
		char* data;
		petplace = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_TRLP_recv( fd,petplace,data);
		return 0;
	}
/*
  <li><a name="SC_TRLPS"><font color=blue>servertoclient TRLPS( int petplace, string data);<br></font></a>
      (TRade List Pet Skill) 相手のトレードしたいペットスキルの内容。このプロトコルは、TRLPより后に送信される。<br>
      <br>
      <dl>
	<dt>int petplace
	<dd>トレードウィンドウで何番目のペットか。
	<dt>string data
	<dd>トレードしたいペットのスキル内容を示す。持っているスキル数分、デリミタ(|)でくくられて送られてくる。一つ一つのトークンは、<a href="#escape">エスケープ</a>されている。<br>
	<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRLPS] ) == 0 ){
		int petplace;
		char* data;
		petplace = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_TRLPS_recv( fd,petplace,data);
		return 0;
	}
/*
  <li><a name="SC_TRCL"><font color=blue>servertoclient TRCL( void);<br></font></a>
      (TRade Close) 相手がトレードしたいアイテムを选择しなおした。相手がTradeOKの状态ならそれをクリア、アイテムやペット、お金の表示もクリアしOpen状态を解除しなければならない。<br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TRCL] ) == 0 ){

		nrproto_TRCL_recv( fd);
		return 0;
	}
/*
  <li><a name="SC_TROC"><font color=blue>servertoclient TROC( int flg);<br></font></a>
      (TRade Ok/Cancel) トレード承诺／キャンセル
      <br>
      <dl>
	<dt>int flg
	<dd>トレードの内容にユーザーが了承し、Tradeボタンが押された时に、TRUEで送信。
	ユーザー间でこれが两方ともTRUEで成立した瞬间、取り引きが発生し、終了する。
	cancelは、ウィンドウを闭じたりした时に発生する。クライアントはウィンドウを闭じなければならない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_TROC] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_TROC_recv( fd,flg);
		return 0;
	}
/*
  <li><a name="SC_PS"><font color=blue>servertoclient PS( int result, int havepetindex, int havepetskill, int toindex);<br></font></a>
      (PetSkill use result)ペットの技を使った结果クライアントのPSに对应して送られてくる。
      result 以外はクライアントのPSに对应している。フィールド上からしか呼ばれない。<br>
      <br>
      <dl>
	<dt>int result
	<dd>结果。0: 失败 1:成功
	<dt>int havepetindex
	<dd>何番目のペットが使用したか。
	<dt>int havepetskill
	<dd>何番目の技を使用したか。
	<dt>int toindex
	<dd> 谁に魔法を使用したか。これはオブジェクトやキャラのindexではない。以下の样になっている。
	<pre>
	  自分    = 0
	  ペット  = 1 ～5
	  仲间    = 6 ～10 （S N の0～4に对应。自分自身も含まれている）
	</pre>
	对象が全员，とか分からない，とかの场合は-1で送信する。<br>
	<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PS] ) == 0 ){
		int result;
		int havepetindex;
		int havepetskill;
		int toindex;
		result = nrproto_demkstr_int( nrproto.token_list[1] );
		havepetindex = nrproto_demkstr_int( nrproto.token_list[2] );
		havepetskill = nrproto_demkstr_int( nrproto.token_list[3] );
		toindex = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_PS_recv( fd,result,havepetindex,havepetskill,toindex);
		return 0;
	}
/*
  <LI><a name="SC_LVUP"><font color=blue>servertoclient LVUP( int point );<br></font></a>
      (LEVELUP)
      等级アップしてパラメータ割り振りができる事をサーバが通知する。いくつ上げられるかを指定する。<br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_LVUP] ) == 0 ){
		int point;
		point = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_LVUP_recv( fd,point);
		return 0;
	}
/*
  <LI><a name="SC_PLVUP"><font color=blue>servertoclient PLVUP(int pethave, int point );<br></font></a>
      (LEVELUP)
      <dl>
	<dt>int pethave
	<dd>どのペットか（0～4）
      </dl>
      <br>
      ペットの等级アップしてパラメータ割り振りができる事をサーバが通知する。いくつ上げられるかを指定する。<br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PLVUP] ) == 0 ){
		int pethave;
		int point;
		pethave = nrproto_demkstr_int( nrproto.token_list[1] );
		point = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PLVUP_recv( fd,pethave,point);
		return 0;
	}
/*
  <LI><a name="SC_POS"><font color=blue>servertoclient POS(int pos );<br></font></a>
      (POSition)
      前卫(1),后卫(0)を通知する。<br>
      <br>
      <dl>
	<dt>int pos
	<dd>前卫か后卫か。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_POS] ) == 0 ){
		int pos;
		pos = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_POS_recv( fd,pos);
		return 0;
	}
/*
  <LI><a name="SC_WN"><font color=blue>servertoclient WN( int windowtype, int buttontype, int seqno, int objindex, string data );<br></font></a>
      (Window)<br>
      ウィンドウを表示せよとサーバーがクライアントに通知する。<br>
      <br>
      <dl>
	<dt>int windowtype
	<dd>ウィンドウ种类。どのような形式のウィンドウを表示するか。これとbuttontype の组み合わせで
	    ウィンドウが决定する。<br>
	    サーバーでは以下の样に设定されている。<br>
	  <pre>
		typedef enum
		{
			WINDOW_MESSAGETYPE_MESSAGE,			信息のみ
			WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT,		信息と一行入力
			WINDOW_MESSAGETYPE_SELECT,			选择ウィンドウ
			WINDOW_MESSAGETYPE_PETSELECT,			ペット选择ウィンドウ
			WINDOW_MESSAGETYPE_PARTYSELECT,			仲间选择ウィンドウ
			WINDOW_MESSAGETYPE_PETANDPARTYSELECT,		ペット，仲间选择ウィンドウ
			WINDOW_MESSAGETYPE_ITEMSHOPMENU,		お店のメニューウインドウ
			WINDOW_MWSSAGETYPE_ITEMSHOPYBUY,		お店から买ウインドウ
			WINDOW_MWSSAGETYPE_ITEMSHOPYSELL,		お店に卖ウインドウ
			WINDOW_MESSAGETYPE_LIMITITEMSHOPMAIN,		买い取り专门アイテム屋のメインウインドウ
			WINDOW_MESSAGETYPE_PETSKILLSHOP,		ペットの技屋さんウインドウ
			WINDOW_MESSAGETYPE_WIDEMESSAGE,			信息のみ（大きい方）
			WINDOW_MESSAGETYPE_WIDEMESSAGEANDLINEINPUT,	信息と一行入力（大きい方）
			WINDOW_MESSAGETYPE_POOLITEMSHOPMENU,		アイテム预り屋のメニューウインドウ
			WINDOW_MESSAGETYPE_POOLITEMSHOPMAIN,		アイテム预り屋のメインウインドウウ
			WINDOW_MESSAGETYPE_PLAYERANDPETSELECT,		自分とペット选择ウインドウ
			WINDOW_MESSAGETYPE_PETANDPARTYSELECT,		ペット，仲间选择ウィンドウ
			WINDOW_MESSAGETYPE_SKILLMASTER_SHOP,		スキルマスター店ウィンドウ
			WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_BUY,	スキルマスター店买ウィンドウ
			WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_REMOVE,	スキルマスター店外すウィンドウ
			WINDOW_MESSAGETYPE_INJURY_DOCTOR,			怪我医者ウィンドウ
			WINDOW_MESSAGETYPE_JUDGEMAN,				鉴定屋ウィンドウ
			WINDOW_MESSAGETYPE_BOARD,                   伝言板ウィンドウ
			WINDOW_MESSAGETYPE_GUILDMONSTER,            家族モンスターウィンドウ
			WINDOW_MESSAGETYPE_ITEMBOX,                 公会宠物ルームのアイテムボックス
			WINDOW_MESSAGETYPE_FOODBOX,                 公会宠物ルームのえさ箱
			WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT,  信息と二行入力
			WINDOW_MESSAGETYPE_ORTHOPEDIST,             整形外科医ウィンドウ
			WINDOW_MESSAGETYPE_ORTHOPEDIST_CONFIRMATION,整形外科医、颜确认ウィンドウ 
			WINDOW_MESSAGETYPE_ALBUM_WINDOW,            アルバムウインドウ
			WINDOW_MESSAGETYPE_PROFILE,                 プロフィール初期ウィンドウ ◎
			WINDOW_MESSAGETYPE_PROFILELIST,             プロフィールリストウィンドウ ◎
			WINDOW_MESSAGETYPE_PROFILEMAIL,             プロフィールメールウィンドウ ◎
		}WINDOW_MESSAGETYPE;
	  </pre>
	<dt>int buttontype
	<dd>ボタンの形式を指定する。サーバーでは以下の用に定义されている。<br>
	  <pre>
		#define		WINDOW_BUTTONTYPE_NONE		(0)
		#define		WINDOW_BUTTONTYPE_OK		(1 << 0)
		#define		WINDOW_BUTTONTYPE_CANCEL	(1 << 1)
		#define		WINDOW_BUTTONTYPE_YES		(1 << 2)
		#define		WINDOW_BUTTONTYPE_NO		(1 << 3)
		#define		WINDOW_BUTTONTYPE_PREV		(1 << 4)
		#define		WINDOW_BUTTONTYPE_NEXT		(1 << 5)
		#define		WINDOW_BUTTONTYPE_DELETE	(1 << 6)
	  </pre>
	    これらの组み合わせで送信する。例えば，YESボタンとNOボタンが欲しい时は<br>
	    WINDOW_BUTTONTYPE_YES | WINDOW_BUTTONTYPE_NO   (=12)<br>
	    で送る。
	<dt>int seqno
	<dd>このウィンドウの番号を示す。サーバーが管理する。
	    クライアントはWNにてこのウィンドウの操作结果を返すのに，この番号を添えて返答する。
	    これによってサーバーはどのNPCのどの场面でのウィンドウかを判断出来るようにする。
	<dt>int objindex
	<dd>このウィンドウを出せと言ったNPCなどのindexが格纳されている。
	    システムが出せと言った场合は-1などが入っている。
	    クライアントは，ウィンドウ入力后のWNプロトコルでこの数值をそのまま返せば良い。<br>
			<br>
	<dt>string data
	<dd>信息内容を示す。内容はエスケープする。"\n"で区切ると改行という意味とする。
	    また，クライアントのウィンドウで表示出来る横幅を越えた际は自动的に改行される。
			文字列中の$n(nは数字)が入っていると、それ以降はその数字で<a href="#coloring">カラーリング</a>しなければいけない。
			$$は$自身を示すので、クライアントはエスケープしなければならない。<br>
	    选择肢のあるウィンドウでは，"\n"で区切られた最初のトークンが信息の行数
	    となり，次に信息のトークンが最初のトークンで指定された个数続き，
	    そのあとのトークン每が１つずつの选择肢となる。また前から顺に 1 から
	    番号を割り当て，选择した场合のWNでの返答の时に返す。<br>
			<br>
		<dt>string data(伝言板用)
		<dd>伝言板用文字列。先头のトークンのみこの伝言板のタイトル、デリミタ"|"のあと揭示板の信息行分送られて来る。<br>
		一行のデータは以下の通り<br>
		颜画像番号|书き込んだ时间(int)|削除出来るかのフラグ|キャラの名称|信息|
		となっている。キャラの名称と信息は<a href="#escape">エスケープ</a>されている。<br>
		クライアントは削除出来るかのフラグを见て削除ボタンの表示/非表示をしなければならない。<br>
		また、书き込んだ日付は、时间として数值でくるので、クライアントで变换して表示しなければならない。たまに、０が来る时があるがその时は空白にする。<br>
		颜画像番号も、０が来た时は空栏にする。<br>
		<br>
	<dt>string data(お店用）
	<dd>信息内容を示す。内容はエスケープする。"\n"で区切ると改行という意味とする。
	    また，クライアントのウィンドウで表示出来る横幅を越えた际は自动的に改行される。
	    データ内は项目每に"｜"で区切られています。
	<br><br><dd>??买??<br>
	买い卖フラグ（买０：卖１）｜店の名称｜通常信息｜装备で
	きない信息｜确认信息｜アイテム名｜ID|画像番号｜
		装备可能フラグ(0:できない 1:できる)|买える数量｜值段｜说明｜...
		<br><br>
			[アイテム名～说明]を1つのアイテムとして、复数个送信される。<br>
			IDは、その物を买时にそのIDと个数をサーバーに渡すのに使用する。
	<br><dd><br>??卖??<br>
	<br><br>
	<br><dd><br>??その他??<br>
		店の名称｜信息|NPCの画像番号
	<br><br>
		サムギルの道具屋|いらっしゃい、どるする？|100025
	<br><br><br>
	<dt>string data(ペットの技屋さん用）
	<dd>信息内容を示す。内容はエスケープする。"\n"で区切ると改行という意味とする。
	    また，クライアントのウィンドウで表示出来る横幅を越えた际は自动的に改行される。
	    データ内は项目每に"｜"で区切られています。
	<br><br><dd>??ペットの技取得??<br>
		前の情报使うかどうか？（使う：0　使わない：1）｜店の名称｜メイン信息｜
		技名｜值段｜技说明｜技名｜值段｜技说明
	<br><br>
		1｜サムギルの武器屋｜いらっしゃいまっせ。すばらしい技ばっかですよ｜
		背水の阵その①｜500｜攻击力３０％ＵＰ　防御力３０％ＤＯＷＮ｜
		地球一周｜1000｜新しい発见があるかも（适当）｜	
	<br><br>

	<dt>string data(NPC检索用）
	<dd>信息内容を示す。内容はエスケープする。
	    データ内は项目每に"｜"で区切られています。
	<br><br><dd>??初期ウィンドウ??<br>
	卖买フラグリスト|Aboutフラグリスト<br><br>
	　??卖买フラグリスト<br>
	　　　　アイテム１フラグ|アイテム２フラグ|アイテムnフラグ??（卖买リスト个数分)<br><br>
	　　　　对象アイテムを、卖买いずれかの分类に设定しているユーザが存在する场合、
	下记のようにBITを立てて送信する。<br>
	　　　　　　アイテムフラグ: 0bit ON=卖り 1bit ON=买い<br><br>
	　??Aboutフラグリスト<br>
	　　　　Aboutフラグ１|About２フラグ|About nフラグ??（Aboutリスト个数分)<br><br>
	　　　　对象About项目を设定しているユーザが存在する场合、0bitを立てて送信する<br><br>

	<br><dd><br>??检索结果??<br>
	ユーザ识别子|ユーザ名|画像番号|对象信息<br>
	上记を１括りとして、1ページ表示可能データ数分缲り返す<br>
	　??ユーザ识别子<br>
	　　　　サーバ侧での管理に使う识别子<br><br>
	　??画像番号<br>
	　　　　检索对象ユーザの画像番号<br><br>
	　??ユーザ名<br>
	　　　　检索对象ユーザの名称。ユーザ识别子と并せてサーバ侧でのユーザ特定に使う<br><br>
	　??对象信息<br>
	　　　　检索对象ユーザが设定している信息。画面表示に使う。<br><br>

	<br><dd><br>??プロフィール表示??<br>
	ユーザ名|SellアイテムID|Sell信息|BuyアイテムID
				|Buy信息|AboutアイテムID｜About信息
				|プロフィール信息





      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_WN] ) == 0 ){
		int windowtype;
		int buttontype;
		int seqno;
		int objindex;
		char* data;
		windowtype = nrproto_demkstr_int( nrproto.token_list[1] );
		buttontype = nrproto_demkstr_int( nrproto.token_list[2] );
		seqno = nrproto_demkstr_int( nrproto.token_list[3] );
		objindex = nrproto_demkstr_int( nrproto.token_list[4] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[5] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[5] ));
		nrproto_WN_recv( fd,windowtype,buttontype,seqno,objindex,data);
		return 0;
	}
/*
  <LI><a name="SC_EF"><font color=blue>servertoclient EF( int effect, int level, string option );<br></font></a>
      (EFfect)<br>
      雪や雨を降らしたり。クライアントに全体的な效果表现せよと送る。<br>
      <br>
      <dl>
	<dt>int effect
	<dd>效果番号。それぞれを足し算すると，两方ともの效果が得られます。３にすると雨と雪が降ったりとかします。
	  <ul>
	    <li>1:????
	    <li>2:????
	    <li>4:纸吹雪（予定）
	  </ul>
	<dt>int level
	<dd>效果の强さ。０は消す。１～５は表现の强さを表す。
	<dt>string option
	<dd>今后扩张用
      </dl>
      <br>
      <br>
      <hr>
      <br>

*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_EF] ) == 0 ){
		int effect;
		int level;
		char* option;
		effect = nrproto_demkstr_int( nrproto.token_list[1] );
		level = nrproto_demkstr_int( nrproto.token_list[2] );
		option = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		nrproto_EF_recv( fd,effect,level,option);
		return 0;
	}
/*
  <LI><a name="SC_SE"><font color=blue>servertoclient SE( int senumber );<br></font></a>
      (SoundEffect)<br>
	クライアントにSEを鸣らすように指示する。<br>
      <br>
      <dl>
	<dt>int senumber
	<dd>ＳＥの番号サーバーでは次のように设定されている。クライアントはその行动に对应したSEをならさなければならない。<br>
		<pre>
		typedef enum {
		  CHAR_SE_JOINPARTY,             仲间入る
		  CHAR_SE_DISCHARGEPARTY,        仲间拔ける
		  CHAR_SE_PICKUPITEM,            アイテム拾う
		  CHAR_SE_DROPITEM,              アイテム落す
		  CHAR_SE_PICKUPGOLD,            お金拾う
		  CHAR_SE_DROPGOLD1,             お金落す(1～999 GOLD)
		  CHAR_SE_DROPGOLD2,             お金落す(1000～99999 GOLD)
		  CHAR_SE_DROPGOLD3,             お金落す(100000～ GOLD)
		  CHAR_SE_EXCHANGECARD,          名刺交换
		}CHAR_SE;
		</pre>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_SE] ) == 0 ){
		int senumber;
		senumber = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_SE_recv( fd,senumber);
		return 0;
	}
/*
  <LI><a name="SC_BGMW"><font color=blue>servertoclient BGMW( int sw );<br></font></a>
      (SoundEffect)<br>
      クライアントに次のワープの后にこのＢＧＭを鸣らすように指示する。<br>
      <br>
      <dl>
	<dt>int sw
	<dd>音乐番号。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_BGMW] ) == 0 ){
		int sw;
		sw = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_BGMW_recv( fd,sw);
		return 0;
	}
/*
  <LI><a name="SC_PC"><font color=blue>servertoclient PC( int palnumber, int frame_cnt );<br></font></a>
      (PaletteChange)<br>
	クライアントにパレットを变更するように指示する。<br>
      <br>
      <dl>
	<dt>int palnumber
	<dd>パレット番号。
	<dt>int frame_cnt
	<dd>フェードするフレーム数（１が最速）。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PC] ) == 0 ){
		int palnumber;
		int frame_cnt;
		palnumber = nrproto_demkstr_int( nrproto.token_list[1] );
		frame_cnt = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PC_recv( fd,palnumber,frame_cnt);
		return 0;
	}
/*
  <LI><a name="SC_SH"><font color=blue>servertoclient SH( int action, int mapid, int floor, int x, int y );<br></font></a>
      (Ship)<br>
	クライアントに船がどういう状态かを指示する。<br>
      <br>
      <dl>
	<dt>int action
	<dd>船の状态。 1:船が入港してきた。 2:船が停止している。3:船が出発した。4:船が画面外に消えた。
	<dt>int mapid, int floor, int x, int y
	<dd>船の位置。
	</dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_SH] ) == 0 ){
		int action;
		int mapid;
		int floor;
		int x;
		int y;
		action = nrproto_demkstr_int( nrproto.token_list[1] );
		mapid = nrproto_demkstr_int( nrproto.token_list[2] );
		floor = nrproto_demkstr_int( nrproto.token_list[3] );
		x = nrproto_demkstr_int( nrproto.token_list[4] );
		y = nrproto_demkstr_int( nrproto.token_list[5] );
		nrproto_SH_recv( fd,action,mapid,floor,x,y);
		return 0;
	}
/*
  <LI><a name="SC_PLAYSE"><font color=blue>servertoclient PLAYSE( int seno, int x, int y );<br></font></a>
      (PlaySoundEffect)<br>
	SEを番号、座标を指定して鸣らす。<br>
      <br>
      <dl>
	<dt>int seno
	<dd>sound.cnfの番号
	<dt>int x, int y
	<dd>座标（中央は320,240）。
	</dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PLAYSE] ) == 0 ){
		int seno;
		int x;
		int y;
		seno = nrproto_demkstr_int( nrproto.token_list[1] );
		x = nrproto_demkstr_int( nrproto.token_list[2] );
		y = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_PLAYSE_recv( fd,seno,x,y);
		return 0;
	}
/*
  <LI><a name="SC_ES"><font color=blue>servertoclient ES( int seno, int x, int y );<br></font></a>
      (EnvironmentSound)<br>
	环境音を指定する。<br>
      <br>
      <dl>
	<dt>int seno
	<dd>sound.cnfの番号
	<dt>int x, int y
	<dd>グリッド座标。
	</dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ES] ) == 0 ){
		int seno;
		int x;
		int y;
		seno = nrproto_demkstr_int( nrproto.token_list[1] );
		x = nrproto_demkstr_int( nrproto.token_list[2] );
		y = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_ES_recv( fd,seno,x,y);
		return 0;
	}
/*
  <LI><a name="SC_MN"><font color=blue>servertoclient MN( string mapname);<br></font></a>
  (MapName)CCの后やや战闘終了后に送られる。マップの名称。エスケープされている。<br>
      <br>
      <dl>
	<dt>string mapname
	<dd>マップの名称。エスケープされている。
      </dl>
      <br>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_MN] ) == 0 ){
		char* mapname;
		mapname = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_MN_recv( fd,mapname);
		return 0;
	}
/*
  <LI><a name="SC_CC"><font color=blue>servertoclient CC( int mapid, int floor, int maxx, int maxy, int x, int y, int cfgid, int seqno, int var, int bgm, int cut);<br></font></a>
      (Character Coordinate)<br>
      自分のキャラクターの座标を送る。
      ワープ，ログイン时に送られる。
      cfgidは，通常マップ，家では０，自动生成ダンジョン，ではダンジョン设定番号が入る。
      seqnoは，通常マップ，家では０，自动生成ダンジョンでは，サーバーが作成した自动生成ダンジョンの连番が入る。
      クライアントは，自动生成ダンジョンでは，servernumber, mapid, floorが一致しても，cfgid, seqnoが一致しない场合は
      そのマップをクリアしなければならない。<br>
      新マップの场合、末尾にＢＧＭ番号が追加で送られてくる。
      <br>
      <dl>
	<dt>int mapid
	<dd>マップＩＤ。
	<dt>int floor
	<dd>フロアＩＤ
	<dt>int maxx, maxy
	<dd>マップサイズ
	<dt>int x,y
	<dd>x,y座标。
	<dt>int cfgid, int seqno
	<dd>自动生成ダンジョンでは，设定番号，通算作成番号が入る。
	<dt>int var ????
	<dd>マップのバージョン
	<dt>int bgm ????
	<dd>ＢＧＭ番号
	<dt>int cut ????
	<dd>使用するカットデータ。－１のときはフロアＩＤと同じものを使用する。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CC] ) == 0 ){
		int mapid;
		int floor;
		int maxx;
		int maxy;
		int x;
		int y;
		int cfgid;
		int seqno;
		int var;
		int bgm;
		int cut;
#ifdef PUK3_CONNDOWNWALK
		int warpid;
#endif
		mapid = nrproto_demkstr_int( nrproto.token_list[1] );
		floor = nrproto_demkstr_int( nrproto.token_list[2] );
		maxx = nrproto_demkstr_int( nrproto.token_list[3] );
		maxy = nrproto_demkstr_int( nrproto.token_list[4] );
		x = nrproto_demkstr_int( nrproto.token_list[5] );
		y = nrproto_demkstr_int( nrproto.token_list[6] );
		cfgid = nrproto_demkstr_int( nrproto.token_list[7] );
		seqno = nrproto_demkstr_int( nrproto.token_list[8] );
		var = nrproto_demkstr_int( nrproto.token_list[9] );
		bgm = nrproto_demkstr_int( nrproto.token_list[10] );
		cut = nrproto_demkstr_int( nrproto.token_list[11] );
#ifdef PUK3_CONNDOWNWALK
		warpid = nrproto_demkstr_int( nrproto.token_list[12] );
		nrproto_CC_recv( fd,mapid,floor,maxx,maxy,x,y,cfgid,seqno,var,bgm,cut,warpid);
#else
		nrproto_CC_recv( fd,mapid,floor,maxx,maxy,x,y,cfgid,seqno,var,bgm,cut);
#endif
		return 0;
	}
/*
  <LI><a name="SC_CLIENTLOGIN"><font color=blue>servertoclient ClientLogin( int result, string data);<br></font></a>
      ClientLoginの返答。<br>
      <br>
      <dl>
	<dt>int result
	<dd>认证の成否を示す数值。0が成功。それ以外はエラー。エラーコードは<a href="./nracproto.html#authcode">auth_code.h</a>に定义されている。クライアントはこのエラーコードに沿ったエラー信息を出力すること。
	<dt>string data
	<dd>エラーなどの时、その详细を示す文字列。ここに何かが入っている事は期待してはならない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ClientLogin] ) == 0 ){
		int result;
		char* data;
		result = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_ClientLogin_recv( fd,result,data);
		return 0;
	}
/*
  <LI><a name="SC_CREATENEWCHAR"><font color=blue>servertoclient CreateNewChar(string result,int registnumber, string data);<br></font></a>
      CreateNewCharの返答。<br>
      <br>
      <dl>
	<dt>string result
	<dd>"successful" か "failed" のいずれか。この文字列はエスケー
	    プしない。
	<dt>int registnumber
	<dd>キャラの登録番号。この番号でもってログインする。
	<dt>string data
	<dd>"failed" の时は理由を示す人间の见て分る文字
	    列である。アカウントサーバからの返答そのままである。
	    以下の文字列
	    <pre>
	    "failed bad parameter"
	    </pre>
	    の场合は、キャラ作成のときに规定のパラメータの范围を越えて
	    いるというとを意味する。これはゲームサーバーが出力するメッ
	    セージである。この文字列はエスケープしない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CreateNewChar] ) == 0 ){
		char* result;
		int registnumber;
		char* data;
		result = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		registnumber = nrproto_demkstr_int( nrproto.token_list[2] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		nrproto_CreateNewChar_recv( fd,result,registnumber,data);
		return 0;
	}
/*
  <LI><a name="SC_CHARDELETE"><font color=blue>servertoclient CharDelete(string result,string data);<br></font></a>
      CharDelete の返答。<br>
      <br>
      <dl>
	<dt>string result
	<dd>"successful" か "failed" のいずれか。エスケープしない。
	<dt>string data
	<dd>"failed" の时は理由を示す人间の见て分る文字
	    列である。アカウントサーバからの返答そのままである。
	    デリミタをふくまないので、エスケープしない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CharDelete] ) == 0 ){
		char* result;
		char* data;
		result = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CharDelete_recv( fd,result,data);
		return 0;
	}
/*
  <LI><a name="SC_CHARLOGIN"><font color=blue>servertoclient CharLogin(string result,string data);<br></font></a>
      CharaLoginの返答。<br>
      <br>
      <dl>
	<dt>string result
	<dd>"successful" か "failed" のいずれか。エスケープしない。
	<dt>string data
	<dd>"failed" の时は その理由の文字列。エスケープしない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CharLogin] ) == 0 ){
		char* result;
		char* data;
		result = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CharLogin_recv( fd,result,data);
		return 0;
	}
/*
  <LI><a name="SC_CHARLIST"><font color=blue>servertoclient CharList( int result,string data);<br></font></a>
      CharListの返答。<br>
      <br>
      <dl>
	<dt>int result
	<dd>charlistの成否。０が成功。それ以外はエラー。エラーコードは以下のとおり。<br>
	<pre>
	enum {
		CHARLIST_RESULT_SUCCESSFUL,  成功
		CHARLIST_RESULT_LOCKED,      ロックしていた(多重ログイン)
		CHARLIST_RESULT_NOTYETAUTH,  未认证
		CHARLIST_RESULT_ETC,         その他のエラー
	};
	</pre>
	<dt>string data
	<dd>resultが"successful"の时は、アカウントサーバーに保存されて
	    いるすべてのキャラの名称、オプションををスペースで区切った
	    一个の文字列。この文字列を作っているのは、アカウントサーバ。
	    result が "failed" の时は理由を示す人间の见て分る文字列で
	    ある。成功の时のオプションの中身は以下のとおりである。<br>
		dataplace|faceimg|level|vital|str|tgh|quick|magic|earth|water|fire|wind|login|name|registnumber|jobs|renewal|baseimg|guildtitleid<br>
	    <dl>
	      <dt>dataplace
	      <dd>セーブデータの何番目か。これでキャラクターリストの何番目かを决定する。
	      <dt>faceimage
	      <dd>颜の画像番号
	      <dt>level
	      <dd>キャラの等级
	      <dt>vital.str,tgh,quick,magic
	      <dd>各パラメータ。
	      <dt>earth.water,fire,wind
	      <dd>各属性值
	      <dt>logincount
	      <dd>ログインカウント
	      <dt>name
	      <dd>キャラの名称
	      <dt>registnumber
	      <dd>登録番号。この番号でもってログインする。
	      <dt>jobs
	      <dd>职业の名称
	      <dt>renewal
	      <dd>リニューアル
	      <dt>baseimg
	      <dd>基本グラフィック番号（颜グラフィックとの整合性チェックに使用）
	      <dt>guildtitleid
	      <dd>家族称号のＩＤ番号（家族マスターかどうかの识别に使用）
	    </dl>
	    "|" で区切られている。 それぞれの项目は、<a
	    href="#escaping">エスケープ</a>されている。そのあとたて棒
	    でつなげる。<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CharList] ) == 0 ){
		int result;
		char* data;
		result = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CharList_recv( fd,result,data);
		return 0;
	}
/*
  <LI><a name="SC_CHARLOGOUT"><font color=blue>servertoclient CharLogout(string result , string data);<br></font></a>
      Logoutに对する返答。<br>
      <br>
      <dl>
	<dt>string result
	<dd>"successful" か "failed" のいずれか。エスケープしない。
	<dt>string data
	<dd>"failed" の时にのみ意味があり、失败の理由(状态)を示す人间
	    の见て分る文字列である。エスケープしない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_CharLogout] ) == 0 ){
		char* result;
		char* data;
		result = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_CharLogout_recv( fd,result,data);
		return 0;
	}
/*
  <LI><a name="SC_PROCGET"><font color=blue>servertoclient ProcGet( string data);<br></font></a>
	ProcGetの返答。<br>
      <br>
      <dl>
	<dt>string data
	<dd>エスケープする。ゲームサーバーの内部情报を送信する。内容はlog/procファイルに书かれる内容と同じ。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_ProcGet] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_ProcGet_recv( fd,data);
		return 0;
	}
/*
  <LI><a name="SC_PLAYERNUMGET"><font color=blue>servertoclient PlayerNumGet( int logincount, int player);<br></font></a>
	PlayerNumGetの返答。<br>
      <br>
      <dl>
	<dt>int logincount,player
	<dd>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PlayerNumGet] ) == 0 ){
		int logincount;
		int player;
		logincount = nrproto_demkstr_int( nrproto.token_list[1] );
		player = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PlayerNumGet_recv( fd,logincount,player);
		return 0;
	}
/*
  <LI><a name="SC_ECHO"><font color=blue>servertoclient Echo( string test );<br></font></a>
      Echoに对する返答。<br>
      <br>
      <dl>
	<dt>string test
	<dd>さきほど入力された文字列。エスケープしない。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_Echo] ) == 0 ){
		char* test;
		test = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_Echo_recv( fd,test);
		return 0;
	}
/*
  <LI><a name="SC_IP"><font color=blue>servertoclient IP( string ip );<br></font></a>
      ログイン时にクライアントへクライアントのＩＰを送信する。　
      ＡＤＳＬなど气づかない间に再接続されている场合があるので、
      これを元に、自分のＩＰアドレスが变わったかどうかチェックする。
      变わった场合はチャットに表示する。
      ＩＰアドレス固定のプロバイダだと判断できない。<br>
      <br>
      <dl>
	<dt>string ip
	<dd>ＩＰアドレスの文字列。
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_IP] ) == 0 ){
		char* ip;
		ip = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_IP_recv( fd,ip);
		return 0;
	}
/*
  <LI><a name="SC_PV"><font color=blue>servertoclient PV( int ver );<br></font></a>
      ログイン时にクライアントへパッケージバージョンを送信する。<br>
      <br>
      <dl>
	<dt>int ver
	<dd>パッケージのバージョン<br>
	<pre>
	enum{
		PV_NORMAL,	//通常版
		PV_TRIAL,		//见习版
		PV_EQUAL,		//见习を卒业したバージョン
	};
	</pre>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PV] ) == 0 ){
		int ver;
		ver = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_PV_recv( fd,ver);
		return 0;
	}
/*
  <LI><a name="SC_PVUP"><font color=blue>servertoclient PVUP( int result );<br></font></a>
      パッケージのバージョンアップに答える。<br>
      <br>
      <dl>
	<dt>int result
	<dd>0:成功<br>
	<dd>1:あなたはすでにそのバージョンです。<br>
	<dd>2:バージョンアップしすぎです。できません。<br>
	<dd>3:ＣＤＫＥＹが违います。<br>
	<dd>4:プロダクトキーが违います。<br>
	<dd>5:ＤＢ読み込み时にエラー。<br>
	<dd>6:そのプロダクトキーはすでに使用されています。<br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_PVUP] ) == 0 ){
		int result;
		result = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_PVUP_recv( fd,result);
		return 0;
	}
/*
  <LI><a name="SC_MAC"><font color=blue>servertoclient MAC( int listmax, int count );<br></font></a>
      マクロチェックのコンフィグをクライアントに指示する。<br>
      <br>
      <dl>
	<dt>int listmax
	<dd>マウスポイントリストの数。デフォルトは１００<br>
	<dd><br>
	<dt>int count
	<dd>同じ场所を押されたらマクロとみなされる回数。デフォルト５０<br>
	<dd><br>
      </dl>
      <br>
      <hr>
      <br>
*/

	if( strcmp( funcname , PacketRecv[PacketVer][RECV_MAC] ) == 0 ){
		int listmax;
		int count;
		listmax = nrproto_demkstr_int( nrproto.token_list[1] );
		count = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_MAC_recv( fd,listmax,count);
		return 0;
	}
	return -1;
}
void nrproto_SetClientLogFiles( char *r , char *w )
{
	nrproto_strcpysafe( nrproto_readlogfilename , r , sizeof( nrproto_readlogfilename ) );
	nrproto_strcpysafe( nrproto_writelogfilename , w , sizeof( nrproto_writelogfilename ) );
}
#ifdef PUK2

int nrproto_InitClient( int (*writefunc)(int,char*,int) ,int bufsiz ,int fd)
{
	int i;
	if( (void*)writefunc == NULL){nrproto.write_func = nrproto_default_write_wrap;} else {nrproto.write_func = writefunc;}
	nrproto_AllocateCommonWork(bufsiz);
#ifdef PUK2_MEMCHECK
	memlistset( nrproto_stringwrapper, MEMLISTTYPE_NRPROTOSTRINGWRAPPER );
#endif
	nrproto_stringwrapper = (char**)malloc(sizeof(char*) * MAXLSRPCARGS);
	if(nrproto_stringwrapper ==NULL)return -1;
	memset( nrproto_stringwrapper , 0, sizeof(char*)*MAXLSRPCARGS);
	for(i=0;i<MAXLSRPCARGS;i++){
#ifdef PUK2_MEMCHECK
		memlistset( nrproto_stringwrapper[i], MEMLISTTYPE_NRPROTOSTRINGWRAPPERDATA );
#endif
		nrproto_stringwrapper[i] = (char*)malloc( bufsiz );
		if( nrproto_stringwrapper[i] == NULL){
#ifdef PUK2_MEMCHECK
			for(i=0;i<MAXLSRPCARGS;i++){
				memlistset( nrproto_stringwrapper[i], MEMLISTTYPE_NRPROTOSTRINGWRAPPERDATA );
				free( nrproto_stringwrapper[i]);
				return -1;
			}
#else
			for(i=0;i<MAXLSRPCARGS;i++){free( nrproto_stringwrapper[i]);return -1;}
#endif
		}
	}
	return 0;
}

void nrproto_SetLogCallback( void (*f)(int,char *) )
{
	nrproto.log_callback = f;
}

void nrproto_CleanupClient( void )
{
	int i;
#ifdef PUK2_MEMCHECK
	memlistrel( nrproto.work, MEMLISTTYPE_STRUCT_NRPROTO );
	memlistrel( nrproto.arraywork, MEMLISTTYPE_STRUCT_NRPROTO );
	memlistrel( nrproto.escapework, MEMLISTTYPE_STRUCT_NRPROTO );
	memlistrel( nrproto.val_str, MEMLISTTYPE_STRUCT_NRPROTO );
	memlistrel( nrproto.token_list, MEMLISTTYPE_STRUCT_NRPROTO );
	#ifdef PUK2_MEMLEAK_LOGINOUT
		memlistrel( nrproto.cryptwork, MEMLISTTYPE_STRUCT_NRPROTO );
		memlistrel( nrproto.jencodecopy, MEMLISTTYPE_STRUCT_NRPROTO );
		memlistrel( nrproto.jencodeout, MEMLISTTYPE_STRUCT_NRPROTO );
		memlistrel( nrproto.compresswork, MEMLISTTYPE_STRUCT_NRPROTO );
	#endif
	for(i=0;i<MAXLSRPCARGS;i++){
		memlistrel( nrproto_stringwrapper[i], MEMLISTTYPE_NRPROTOSTRINGWRAPPERDATA );
	}
	memlistrel( nrproto_stringwrapper, MEMLISTTYPE_NRPROTOSTRINGWRAPPER );
#endif
	free( nrproto.work );
	free( nrproto.arraywork);
	free( nrproto.escapework );
	free( nrproto.val_str);
	free( nrproto.token_list );
#ifdef PUK2_MEMLEAK_LOGINOUT
	free( nrproto.cryptwork );
	free( nrproto.jencodecopy);
	free( nrproto.jencodeout );
	free( nrproto.compresswork );
#endif
	for(i=0;i<MAXLSRPCARGS;i++){free( nrproto_stringwrapper[i]);}
	free( nrproto_stringwrapper );
}

#else

int nrproto_InitClient( int (*writefunc)(int,char*,int) ,int bufsiz ,int fd)
{
	int i;
	if( (void*)writefunc == NULL){nrproto.write_func = nrproto_default_write_wrap;} else {nrproto.write_func = writefunc;}
	nrproto_AllocateCommonWork(bufsiz);
	nrproto_stringwrapper = (char**)malloc(sizeof(char*) * MAXLSRPCARGS);
	if(nrproto_stringwrapper ==NULL)return -1;
	memset( nrproto_stringwrapper , 0, sizeof(char*)*MAXLSRPCARGS);
	for(i=0;i<MAXLSRPCARGS;i++){
		nrproto_stringwrapper[i] = (char*)malloc( bufsiz );
		if( nrproto_stringwrapper[i] == NULL){
			for(i=0;i<MAXLSRPCARGS;i++){free( nrproto_stringwrapper[i]);return -1;}
		}
	}
	return 0;
}

void nrproto_SetLogCallback( void (*f)(char *) )
{
	nrproto.log_callback = f;
}

void nrproto_CleanupClient( void )
{
	int i;
	free( nrproto.work );
	free( nrproto.arraywork);
	free( nrproto.escapework );
	free( nrproto.val_str);
	free( nrproto.token_list );
	for(i=0;i<MAXLSRPCARGS;i++){free( nrproto_stringwrapper[i]);}
	free( nrproto_stringwrapper );
}

#endif
/* end of the generated client code */
