﻿//プロフィールウインドウ

#ifndef _MENUPROFILE_H_
#define _MENUPROFILE_H_

void InitMenuWindowMenuProfileInLogin(void);
//----------
BOOL MenuWindowProfCategory( int mouse );
BOOL MenuWindowProfCategoryDraw( int mouse );

BOOL MenuSwitchProfCategorySwitch( int no, unsigned int flag );
BOOL MenuSwitchProfCategoryScrollSwitch( int no, unsigned int flag );
BOOL MenuSwitchProfCategoryScrollWheel( int no, unsigned int flag );
BOOL MenuSwitchProfCategoryPanel( int no, unsigned int flag );

//----------
BOOL MenuWindowProfUserNpc( int mouse );
BOOL MenuWindowProfUserNpcDraw( int mouse );
BOOL MenuSwitchProfUserNpcSwitch( int no, unsigned int flag );

//----------
BOOL MenuWindowProfileBBS1( int mouse );
BOOL MenuWindowProfileBBS1Draw( int mouse );
void MenuWindowProfileBBS1ReDraw( void );
void openMessageProfileBBS1( int ButtonType, char *data);
void MenuWindowProfileBBS1Init(void);
BOOL MenuWindowProfileBBS1Close( int no, unsigned int flag );
BOOL MenuWindowProfileBBS1SelectSwitch( int no, unsigned int flag );
BOOL MenuWindowProfileBBS1Del( void );

BOOL MenuWindowProfileBBS2( int mouse );
BOOL MenuWindowProfileBBS2Draw( int mouse );
void MenuWindowProfileBBS2ReDraw( void );
void openMessageProfileBBS2( int ButtonType, char *data);
void MenuWindowProfileBBS2Init(void);
BOOL MenuWindowProfileBBS2Close( int no, unsigned int flag );
BOOL MenuWindowProfileBBS2Switch( int no, unsigned int flag );
BOOL MenuWindowProfileBBS2SelectSwitch( int no, unsigned int flag );
BOOL MenuWindowProfileBBS2Del( void );
BOOL MenuSwitchProfileBBS2SearchDialog( int no, unsigned int flag );

BOOL MenuWindowProfileBBS3( int mouse );
BOOL MenuWindowProfileBBS3Draw( int mouse );
void MenuWindowProfileBBS3ReDraw( void );
void openMessageProfileBBS3( int ButtonType, char *data);
void MenuWindowProfileBBS3Init(void);
BOOL MenuWindowProfileBBS3Close( int no, unsigned int flag );
BOOL MenuWindowProfileBBS3Switch( int no, unsigned int flag );
BOOL MenuWindowProfileBBS3Del( void );

BOOL MenuWindowProfileBBS4( int mouse );
BOOL MenuWindowProfileBBS4Draw( int mouse );
BOOL MenuWindowProfileBBS4Del( void );
void MenuWindowProfileBBS4ReDraw( void );
BOOL MenuSwitchProfileBBS4Switch( int no, unsigned int flag );
BOOL MenuSwitchProfileBBS4Dialog( int no, unsigned int flag );

void closeAllProfileBBSWindow(void);
void closeProfileBBSWindowFromType(int type);

//---------------------------------------------------------------------------
//分类リスト
//---------------------------------------------------------------------------
GRAPHIC_SWITCH MenuWindowProfCategoryGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_AlbumWindow,0,0,0,0,0xFFFFFFFF},		//ベース
	{GID_AlbumBack,0,0,0,0,0x80FFFFFF},			//背景

	{GID_AlbumPanelOn,0,0,0,0,0xFFFFFFFF},		//枠
	{GID_Num_Blue2_0,0,0,0,0,0xFFFFFFFF},		//ナンバー

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},						//スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(下ボタン)

	{GID_AlbumPanelNew,0,0,0,0,0xFFFFFFFF},					//New

	{GID_AlbumDownOn,0,0,0,0,0xFFFFFFFF},		//<
	{GID_AlbumUpOn,0,0,0,0,0xFFFFFFFF},		//>

	{GID_ProfCategorySell,0,0,0,0,0xFFFFFFFF},		//分类名称
};

BUTTON_SWITCH MenuWindowProfCategoryButton[]={
	{0},
};

TEXT_SWITCH MenuWindowProfCategoryText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"物品"},	//名称
};

// スイッチ
static SWITCH_DATA ProfCategorySwitch[] = {
{ SWITCH_GRAPHIC,241-35,  9-1,  11, 11, TRUE, &MenuWindowProfCategoryGraph[ 0], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_GRAPHIC,224-18, 62-21,   0, 14, TRUE, &MenuWindowProfCategoryGraph[5], MenuSwitchNone },				//スクロールバー(つまみ)
{ SWITCH_BUTTON, 223-18, 62-21,  11,134, TRUE, &MenuWindowProfCategoryButton[0], MenuSwitchProfCategoryScrollSwitch },	//スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,224-18, 51-21,  11, 11, TRUE, &MenuWindowProfCategoryGraph[6], MenuSwitchProfCategorySwitch },			//スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,224-18,195-21,  11, 11, TRUE, &MenuWindowProfCategoryGraph[7], MenuSwitchProfCategorySwitch },			//スクロールバー(下ボタン)

{ SWITCH_GRAPHIC,  93,  7,  0,  0, TRUE, &MenuWindowProfCategoryGraph[11], MenuSwitchNone },					//分类の名称

{ SWITCH_GRAPHIC,  40, 189, 20, 20, TRUE, &MenuWindowProfCategoryGraph[ 9], MenuSwitchProfCategorySwitch },		//＜
{ SWITCH_GRAPHIC, 170, 189, 20, 20, TRUE, &MenuWindowProfCategoryGraph[10], MenuSwitchProfCategorySwitch },		//＞
{ SWITCH_NONE,	   0,  0, 225,220, TRUE, NULL, MenuSwitchProfCategoryScrollWheel },								//マウスホイール判定

{ SWITCH_TEXT	  ,  20, 52+16*0-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//分类の名称00
{ SWITCH_TEXT	  ,  20, 52+16*1-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//01
{ SWITCH_TEXT	  ,  20, 52+16*2-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//02
{ SWITCH_TEXT	  ,  20, 52+16*3-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//03
{ SWITCH_TEXT	  ,  20, 52+16*4-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//04
{ SWITCH_TEXT	  ,  20, 52+16*5-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//05
{ SWITCH_TEXT	  ,  20, 52+16*6-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//06
{ SWITCH_TEXT	  ,  20, 52+16*7-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//07
{ SWITCH_TEXT	  ,  20, 52+16*8-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//08
{ SWITCH_TEXT	  ,  20, 52+16*9-22,   0,  0, TRUE, &MenuWindowProfCategoryText[0], MenuSwitchNone },	//09

{ SWITCH_GRAPHIC, 28-13, 50+16*0-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//枠00
{ SWITCH_GRAPHIC, 28-13, 50+16*1-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//01
{ SWITCH_GRAPHIC, 28-13, 50+16*2-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//02
{ SWITCH_GRAPHIC, 28-13, 50+16*3-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//03
{ SWITCH_GRAPHIC, 28-13, 50+16*4-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//04
{ SWITCH_GRAPHIC, 28-13, 50+16*5-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//05
{ SWITCH_GRAPHIC, 28-13, 50+16*6-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//06
{ SWITCH_GRAPHIC, 28-13, 50+16*7-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//07
{ SWITCH_GRAPHIC, 28-13, 50+16*8-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//08
{ SWITCH_GRAPHIC, 28-13, 50+16*9-22, 186, 16, TRUE, &MenuWindowProfCategoryGraph[ 3], MenuSwitchProfCategoryPanel },	//09

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowProfCategoryGraph[ 1], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC, 12, 27,   0,  0, TRUE, &MenuWindowProfCategoryGraph[ 2], MenuSwitchNone },			//背景

{ SWITCH_NONE  , 225, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumGraphProfCategoryClose,		

	EnumGraphProfCategoryTumami,
	EnumBtProfCategoryDrag,
	EnumGraphProfCategoryScrollUp,
	EnumGraphProfCategoryScrollDown,

	EnumGraphProfCategoryName,

	EnumGraphProfCategoryUp,
	EnumGraphProfCategoryDown,
	EnumGraphProfCategoryScrollWheel,

	EnumTextProfCategoryPanel00,		
	EnumTextProfCategoryPanel01,		
	EnumTextProfCategoryPanel02,		
	EnumTextProfCategoryPanel03,		
	EnumTextProfCategoryPanel04,		
	EnumTextProfCategoryPanel05,		
	EnumTextProfCategoryPanel06,		
	EnumTextProfCategoryPanel07,		
	EnumTextProfCategoryPanel08,		
	EnumTextProfCategoryPanel09,		

	EnumGraphProfCategoryPanel00,		
	EnumGraphProfCategoryPanel01,		
	EnumGraphProfCategoryPanel02,		
	EnumGraphProfCategoryPanel03,		
	EnumGraphProfCategoryPanel04,		
	EnumGraphProfCategoryPanel05,		
	EnumGraphProfCategoryPanel06,		
	EnumGraphProfCategoryPanel07,		
	EnumGraphProfCategoryPanel08,		
	EnumGraphProfCategoryPanel09,		

	EnumGraphProfCategoryBase,		
	EnumGraphProfCategoryWindow,		

	EnumHitProfCategory1,		

	EnumProfCategoryEnd,
};

const WINDOW_DATA WindowDataMenuProfCategory = {
 0,															// メニューウィンドウ
     4,  38,  95,225,220, 0x80010101,	EnumProfCategoryEnd,ProfCategorySwitch,MenuWindowProfCategory,MenuWindowProfCategoryDraw,MenuWindowDel};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateProfCategory={
	2,
	0,  0,231,25,
	221,  0,30,100,
};

//---------------------------------------------------------------------------
//プロフィールＢＢＳ１（分类一覧）
//---------------------------------------------------------------------------

//ＢＢＳ１-------------------------------------------------------------
GRAPHIC_SWITCH MenuWindowProfileBBS1Graph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン

	{GID_ProfBBS1Win,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
	{GID_ProfBBS1Back,0,0,0,0,0x8FFFFFFF},			//バック画像

	{GID_ProfBBS1Select,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像

};

BUTTON_SWITCH MenuWindowProfileBBS1Button[]={
	{0},										//クローズボタン
};

TEXT_SWITCH MenuWindowProfileBBS1Text[]={
//	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"分类"},				//分类
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"分类"},				//分类
};



static SWITCH_DATA MenuSwitchProfileBBS1[] = {

//テキスト表示
{ SWITCH_TEXT	  ,  50, 78+20* 0,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 1,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 2,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 3,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 4,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 5,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 6,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 7,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 8,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20* 9,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20*10,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20*11,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20*12,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20*13,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20*14,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  ,  50, 78+20*15,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 0,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 1,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 2,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 3,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 4,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 5,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 6,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 7,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 8,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20* 9,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20*10,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20*11,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20*12,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20*13,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20*14,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 150, 78+20*15,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//

{ SWITCH_TEXT	  , 268, 78+20* 0,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 1,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 2,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 3,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 4,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 5,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 6,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 7,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 8,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20* 9,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20*10,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20*11,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20*12,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20*13,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20*14,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 268, 78+20*15,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 0,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 1,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 2,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 3,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 4,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 5,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 6,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 7,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 8,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20* 9,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20*10,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20*11,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20*12,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20*13,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20*14,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 368, 78+20*15,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//

{ SWITCH_TEXT	  , 484, 78+20* 0,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 1,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 2,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 3,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 4,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 5,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 6,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 7,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 8,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20* 9,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20*10,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20*11,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20*12,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20*13,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 484, 78+20*14,   0,   0,TRUE, &MenuWindowProfileBBS1Text[ 0], MenuSwitchNone },		//

//选择画像表示
{ SWITCH_GRAPHIC	  ,  48, 76+20* 0, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 1, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 2, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 3, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 4, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 5, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 6, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 7, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 8, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20* 9, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20*10, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20*11, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20*12, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20*13, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20*14, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  48, 76+20*15, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 0, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 1, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 2, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 3, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 4, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 5, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 6, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 7, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 8, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20* 9, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20*10, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20*11, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20*12, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20*13, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20*14, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 148, 76+20*15, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//

{ SWITCH_GRAPHIC	  , 266, 76+20* 0, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 1, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 2, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 3, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 4, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 5, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 6, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 7, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 8, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20* 9, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20*10, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20*11, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20*12, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20*13, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20*14, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 266, 76+20*15, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 0, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 1, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 2, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 3, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 4, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 5, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 6, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 7, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 8, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20* 9, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20*10, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20*11, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20*12, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20*13, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20*14, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 366, 76+20*15, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//

{ SWITCH_GRAPHIC	  , 482, 76+20* 0, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 1, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 2, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 3, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 4, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 5, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 6, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 7, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 8, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20* 9, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20*10, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20*11, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20*12, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20*13, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//
{ SWITCH_GRAPHIC	  , 482, 76+20*14, 90,  12,TRUE, &MenuWindowProfileBBS1Graph[ 3], MenuWindowProfileBBS1SelectSwitch },		//

//画像
{ SWITCH_GRAPHIC  , 593,   8,  12, 12, TRUE, &MenuWindowProfileBBS1Graph[ 0], MenuSwitchCloseButton },	//クローズ

//背景画像
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowProfileBBS1Graph[1], MenuSwitchNone },	//ウインドウ
{ SWITCH_GRAPHIC  ,  15,  30,   0,  0, TRUE, &MenuWindowProfileBBS1Graph[2], MenuSwitchNone },	//バック

{ SWITCH_NONE  , 600, 0, 34, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumTextMenuProfileBBS1Sell00,
	EnumTextMenuProfileBBS1Sell01,
	EnumTextMenuProfileBBS1Sell02,
	EnumTextMenuProfileBBS1Sell03,
	EnumTextMenuProfileBBS1Sell04,
	EnumTextMenuProfileBBS1Sell05,
	EnumTextMenuProfileBBS1Sell06,
	EnumTextMenuProfileBBS1Sell07,
	EnumTextMenuProfileBBS1Sell08,
	EnumTextMenuProfileBBS1Sell09,
	EnumTextMenuProfileBBS1Sell10,
	EnumTextMenuProfileBBS1Sell11,
	EnumTextMenuProfileBBS1Sell12,
	EnumTextMenuProfileBBS1Sell13,
	EnumTextMenuProfileBBS1Sell14,
	EnumTextMenuProfileBBS1Sell15,
	EnumTextMenuProfileBBS1Sell16,
	EnumTextMenuProfileBBS1Sell17,
	EnumTextMenuProfileBBS1Sell18,
	EnumTextMenuProfileBBS1Sell19,
	EnumTextMenuProfileBBS1Sell20,
	EnumTextMenuProfileBBS1Sell21,
	EnumTextMenuProfileBBS1Sell22,
	EnumTextMenuProfileBBS1Sell23,
	EnumTextMenuProfileBBS1Sell24,
	EnumTextMenuProfileBBS1Sell25,
	EnumTextMenuProfileBBS1Sell26,
	EnumTextMenuProfileBBS1Sell27,
	EnumTextMenuProfileBBS1Sell28,
	EnumTextMenuProfileBBS1Sell29,
	EnumTextMenuProfileBBS1Sell30,
	EnumTextMenuProfileBBS1Sell31,

	EnumTextMenuProfileBBS1Buy00,
	EnumTextMenuProfileBBS1Buy01,
	EnumTextMenuProfileBBS1Buy02,
	EnumTextMenuProfileBBS1Buy03,
	EnumTextMenuProfileBBS1Buy04,
	EnumTextMenuProfileBBS1Buy05,
	EnumTextMenuProfileBBS1Buy06,
	EnumTextMenuProfileBBS1Buy07,
	EnumTextMenuProfileBBS1Buy08,
	EnumTextMenuProfileBBS1Buy09,
	EnumTextMenuProfileBBS1Buy10,
	EnumTextMenuProfileBBS1Buy11,
	EnumTextMenuProfileBBS1Buy12,
	EnumTextMenuProfileBBS1Buy13,
	EnumTextMenuProfileBBS1Buy14,
	EnumTextMenuProfileBBS1Buy15,
	EnumTextMenuProfileBBS1Buy16,
	EnumTextMenuProfileBBS1Buy17,
	EnumTextMenuProfileBBS1Buy18,
	EnumTextMenuProfileBBS1Buy19,
	EnumTextMenuProfileBBS1Buy20,
	EnumTextMenuProfileBBS1Buy21,
	EnumTextMenuProfileBBS1Buy22,
	EnumTextMenuProfileBBS1Buy23,
	EnumTextMenuProfileBBS1Buy24,
	EnumTextMenuProfileBBS1Buy25,
	EnumTextMenuProfileBBS1Buy26,
	EnumTextMenuProfileBBS1Buy27,
	EnumTextMenuProfileBBS1Buy28,
	EnumTextMenuProfileBBS1Buy29,
	EnumTextMenuProfileBBS1Buy30,
	EnumTextMenuProfileBBS1Buy31,

	EnumTextMenuProfileBBS1About00,
	EnumTextMenuProfileBBS1About01,
	EnumTextMenuProfileBBS1About02,
	EnumTextMenuProfileBBS1About03,
	EnumTextMenuProfileBBS1About04,
	EnumTextMenuProfileBBS1About05,
	EnumTextMenuProfileBBS1About06,
	EnumTextMenuProfileBBS1About07,
	EnumTextMenuProfileBBS1About08,
	EnumTextMenuProfileBBS1About09,
	EnumTextMenuProfileBBS1About10,
	EnumTextMenuProfileBBS1About11,
	EnumTextMenuProfileBBS1About12,
	EnumTextMenuProfileBBS1About13,
	EnumTextMenuProfileBBS1About14,

	EnumGraphMenuProfileBBS1Sell00,
	EnumGraphMenuProfileBBS1Sell01,
	EnumGraphMenuProfileBBS1Sell02,
	EnumGraphMenuProfileBBS1Sell03,
	EnumGraphMenuProfileBBS1Sell04,
	EnumGraphMenuProfileBBS1Sell05,
	EnumGraphMenuProfileBBS1Sell06,
	EnumGraphMenuProfileBBS1Sell07,
	EnumGraphMenuProfileBBS1Sell08,
	EnumGraphMenuProfileBBS1Sell09,
	EnumGraphMenuProfileBBS1Sell10,
	EnumGraphMenuProfileBBS1Sell11,
	EnumGraphMenuProfileBBS1Sell12,
	EnumGraphMenuProfileBBS1Sell13,
	EnumGraphMenuProfileBBS1Sell14,
	EnumGraphMenuProfileBBS1Sell15,
	EnumGraphMenuProfileBBS1Sell16,
	EnumGraphMenuProfileBBS1Sell17,
	EnumGraphMenuProfileBBS1Sell18,
	EnumGraphMenuProfileBBS1Sell19,
	EnumGraphMenuProfileBBS1Sell20,
	EnumGraphMenuProfileBBS1Sell21,
	EnumGraphMenuProfileBBS1Sell22,
	EnumGraphMenuProfileBBS1Sell23,
	EnumGraphMenuProfileBBS1Sell24,
	EnumGraphMenuProfileBBS1Sell25,
	EnumGraphMenuProfileBBS1Sell26,
	EnumGraphMenuProfileBBS1Sell27,
	EnumGraphMenuProfileBBS1Sell28,
	EnumGraphMenuProfileBBS1Sell29,
	EnumGraphMenuProfileBBS1Sell30,
	EnumGraphMenuProfileBBS1Sell31,

	EnumGraphMenuProfileBBS1Buy00,
	EnumGraphMenuProfileBBS1Buy01,
	EnumGraphMenuProfileBBS1Buy02,
	EnumGraphMenuProfileBBS1Buy03,
	EnumGraphMenuProfileBBS1Buy04,
	EnumGraphMenuProfileBBS1Buy05,
	EnumGraphMenuProfileBBS1Buy06,
	EnumGraphMenuProfileBBS1Buy07,
	EnumGraphMenuProfileBBS1Buy08,
	EnumGraphMenuProfileBBS1Buy09,
	EnumGraphMenuProfileBBS1Buy10,
	EnumGraphMenuProfileBBS1Buy11,
	EnumGraphMenuProfileBBS1Buy12,
	EnumGraphMenuProfileBBS1Buy13,
	EnumGraphMenuProfileBBS1Buy14,
	EnumGraphMenuProfileBBS1Buy15,
	EnumGraphMenuProfileBBS1Buy16,
	EnumGraphMenuProfileBBS1Buy17,
	EnumGraphMenuProfileBBS1Buy18,
	EnumGraphMenuProfileBBS1Buy19,
	EnumGraphMenuProfileBBS1Buy20,
	EnumGraphMenuProfileBBS1Buy21,
	EnumGraphMenuProfileBBS1Buy22,
	EnumGraphMenuProfileBBS1Buy23,
	EnumGraphMenuProfileBBS1Buy24,
	EnumGraphMenuProfileBBS1Buy25,
	EnumGraphMenuProfileBBS1Buy26,
	EnumGraphMenuProfileBBS1Buy27,
	EnumGraphMenuProfileBBS1Buy28,
	EnumGraphMenuProfileBBS1Buy29,
	EnumGraphMenuProfileBBS1Buy30,
	EnumGraphMenuProfileBBS1Buy31,

	EnumGraphMenuProfileBBS1About00,
	EnumGraphMenuProfileBBS1About01,
	EnumGraphMenuProfileBBS1About02,
	EnumGraphMenuProfileBBS1About03,
	EnumGraphMenuProfileBBS1About04,
	EnumGraphMenuProfileBBS1About05,
	EnumGraphMenuProfileBBS1About06,
	EnumGraphMenuProfileBBS1About07,
	EnumGraphMenuProfileBBS1About08,
	EnumGraphMenuProfileBBS1About09,
	EnumGraphMenuProfileBBS1About10,
	EnumGraphMenuProfileBBS1About11,
	EnumGraphMenuProfileBBS1About12,
	EnumGraphMenuProfileBBS1About13,
	EnumGraphMenuProfileBBS1About14,

	EnumGraphMenuProfileBBS1Close,

	EnumGraphMenuProfileBBS1Window,
	EnumGraphMenuProfileBBS1Back,

	EnumGraphMenuProfileBBS1Hit,

	EnumMenuProfileBBS1END
};

//ＢＢＳ２-------------------------------------------------------------
GRAPHIC_SWITCH MenuWindowProfileBBS2Graph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_ProfBBS2Win,0,0,0,0,0xFFFFFFFF},	//ウインドウ
	{GID_ProfBBS2Back,0,0,0,0,0x8FFFFFFF},	//バック
	{GID_ProfBBS2SelectOn,0,0,0,0,0xFFFFFFFF},	//选择
	{GID_ProfBBS2CategorySell,0,0,0,0,0xFFFFFFFF},	//分类
	{GID_ProfBBS2LeftOn,0,0,0,0,0xFFFFFFFF},	//←
	{GID_ProfBBS2RightOn,0,0,0,0,0xFFFFFFFF},	//→
	{GID_ProfBBS2SearchOn,0,0,0,0,0xFFFFFFFF},	//サーチ
};



BUTTON_SWITCH MenuWindowProfileBBS2Button[]={
	{0},										//クローズボタン
};

TEXT_SWITCH MenuWindowProfileBBS2Text[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"分类"},				//分类
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"信息"},			//信息
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"名字"},				//名称
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"搜索文字"},				//サーチ文字
};

static SWITCH_DATA MenuSwitchProfileBBS2[] = {

{ SWITCH_GRAPHIC	  , 284, 9, 10, 10,TRUE, &MenuWindowProfileBBS2Graph[ 0], MenuSwitchCloseButton },		//

{ SWITCH_TEXT	  , 120, 30,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 0], MenuSwitchNone },		//
{ SWITCH_GRAPHIC	, 140, 8, 0, 0,TRUE, &MenuWindowProfileBBS2Graph[ 4], MenuSwitchNone },		//

{ SWITCH_GRAPHIC	, 190, 367, 55, 20,TRUE, &MenuWindowProfileBBS2Graph[ 7], MenuWindowProfileBBS2Switch },		//

{ SWITCH_GRAPHIC	, 250, 367, 20, 20,TRUE, &MenuWindowProfileBBS2Graph[ 5], MenuWindowProfileBBS2Switch },		//
{ SWITCH_GRAPHIC	, 270, 367, 20, 20,TRUE, &MenuWindowProfileBBS2Graph[ 6], MenuWindowProfileBBS2Switch },		//

{ SWITCH_TEXT,		20, 370,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 3], MenuSwitchNone },			//
{ SWITCH_DIALOG,	20, 370, 12*14, 14, TRUE, NULL,	 MenuSwitchProfileBBS2SearchDialog },				//ダイアログ

{ SWITCH_TEXT	  , 20, 47+16* 0,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 1,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 2,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 3,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 4,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 5,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 6,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 7,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 8,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16* 9,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*10,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*11,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*12,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*13,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*14,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*15,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*16,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*17,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*18,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 20, 47+16*19,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 1], MenuSwitchNone },		//

{ SWITCH_TEXT	  , 195, 47+16* 0,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 1,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 2,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 3,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 4,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 5,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 6,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 7,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 8,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16* 9,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*10,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*11,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*12,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*13,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*14,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*15,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*16,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*17,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*18,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//
{ SWITCH_TEXT	  , 195, 47+16*19,   0,   0,TRUE, &MenuWindowProfileBBS2Text[ 2], MenuSwitchNone },		//

{ SWITCH_GRAPHIC	  ,  15, 45+16* 0, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 1, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 2, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 3, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 4, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 5, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 6, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 7, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 8, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16* 9, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*10, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*11, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*12, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*13, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*14, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*15, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*16, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*17, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*18, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//
{ SWITCH_GRAPHIC	  ,  15, 45+16*19, 280, 16,TRUE, &MenuWindowProfileBBS2Graph[ 3], MenuWindowProfileBBS2SelectSwitch },		//

{ SWITCH_GRAPHIC	  ,  0,  0,  0,  0,TRUE, &MenuWindowProfileBBS2Graph[ 1], MenuSwitchNone },					//
{ SWITCH_GRAPHIC	  , 14, 27,  0,  0,TRUE, &MenuWindowProfileBBS2Graph[ 2], MenuSwitchNone },					//

{ SWITCH_NONE  , 300, 0, 30, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumMenuProfileBBS2Close,

	EnumMenuProfileBBS2Category,
	EnumMenuProfileBBS2CategoryGraph,

	EnumMenuProfileBBS2Search,

	EnumMenuProfileBBS2PageBack,
	EnumMenuProfileBBS2PageNext,

	EnumMenuProfileBBS2SearchText,
	EnumMenuProfileBBS2SearchDialog,

	EnumMenuProfileBBS2Message00,
	EnumMenuProfileBBS2Message01,
	EnumMenuProfileBBS2Message02,
	EnumMenuProfileBBS2Message03,
	EnumMenuProfileBBS2Message04,
	EnumMenuProfileBBS2Message05,
	EnumMenuProfileBBS2Message06,
	EnumMenuProfileBBS2Message07,
	EnumMenuProfileBBS2Message08,
	EnumMenuProfileBBS2Message09,
	EnumMenuProfileBBS2Message10,
	EnumMenuProfileBBS2Message11,
	EnumMenuProfileBBS2Message12,
	EnumMenuProfileBBS2Message13,
	EnumMenuProfileBBS2Message14,
	EnumMenuProfileBBS2Message15,
	EnumMenuProfileBBS2Message16,
	EnumMenuProfileBBS2Message17,
	EnumMenuProfileBBS2Message18,
	EnumMenuProfileBBS2Message19,

	EnumMenuProfileBBS2Name00,
	EnumMenuProfileBBS2Name01,
	EnumMenuProfileBBS2Name02,
	EnumMenuProfileBBS2Name03,
	EnumMenuProfileBBS2Name04,
	EnumMenuProfileBBS2Name05,
	EnumMenuProfileBBS2Name06,
	EnumMenuProfileBBS2Name07,
	EnumMenuProfileBBS2Name08,
	EnumMenuProfileBBS2Name09,
	EnumMenuProfileBBS2Name10,
	EnumMenuProfileBBS2Name11,
	EnumMenuProfileBBS2Name12,
	EnumMenuProfileBBS2Name13,
	EnumMenuProfileBBS2Name14,
	EnumMenuProfileBBS2Name15,
	EnumMenuProfileBBS2Name16,
	EnumMenuProfileBBS2Name17,
	EnumMenuProfileBBS2Name18,
	EnumMenuProfileBBS2Name19,

	EnumMenuProfileBBS2Select00,
	EnumMenuProfileBBS2Select01,
	EnumMenuProfileBBS2Select02,
	EnumMenuProfileBBS2Select03,
	EnumMenuProfileBBS2Select04,
	EnumMenuProfileBBS2Select05,
	EnumMenuProfileBBS2Select06,
	EnumMenuProfileBBS2Select07,
	EnumMenuProfileBBS2Select08,
	EnumMenuProfileBBS2Select09,
	EnumMenuProfileBBS2Select10,
	EnumMenuProfileBBS2Select11,
	EnumMenuProfileBBS2Select12,
	EnumMenuProfileBBS2Select13,
	EnumMenuProfileBBS2Select14,
	EnumMenuProfileBBS2Select15,
	EnumMenuProfileBBS2Select16,
	EnumMenuProfileBBS2Select17,
	EnumMenuProfileBBS2Select18,
	EnumMenuProfileBBS2Select19,

	EnumMenuProfileBBS2Win,
	EnumMenuProfileBBS2Back,

	EnumMenuProfileBBS2Hit,

	EnumMenuProfileBBS2END
};

//ＢＢＳ３-------------------------------------------------------------
GRAPHIC_SWITCH MenuWindowProfileBBS3Graph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン

	{GID_StatusBtStatusOn,0,0,0,0,0xFFFFFFFF},	//状态ボタン
	{GID_StatusBtDetailOn,0,0,0,0,0xFFFFFFFF},	//ディティールボタン
	{GID_StatusBtTitleOn,0,0,0,0,0xFFFFFFFF},	//タイトルボタン
	{GID_ProfButtonOff,0,0,0,0,0xFFFFFFFF},		//プロフィールボタン画像

	{GID_ProfBase,0,0,0,0,0xFFFFFFFF},			//ベース画像
	{GID_ProfViewWindow,0,0,0,0,0xFFFFFFFF},	//ウインドウ画像
	{GID_StatusBack,0,0,0,0,0x80808080},		//バック画像

	{GID_ProfButtonToListOn,0,0,0,0,0xFFFFFFFF},		//右矢印

	{GID_TitleSetOn,0,0,0,0,0xFFFFFFFF},			//セットボタン

	{GID_ProfCategoryDef,0,0,0,0,0xFFFFFFFF},			//分类画像デフォルト（未使用

	{GID_ProfBBS3SendOn,0,0,0,0,0xFFFFFFFF},	

};

BUTTON_SWITCH MenuWindowProfileBBS3Button[]={
	{0},										//クローズボタン
};

TEXT_SWITCH MenuWindowProfileBBS3Text[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"名称"},				//名称
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"等级"},			//等级
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"たいとる"},			//名称
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"しょくぎょう"},		//职上
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ぎるど"},			//等级
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"―――"},			//家族
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"せる"},			//Ｓｅｌｌ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ばい"},			//Ｂｕｙ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"あばうと"},			//Ａｂｏｕｔ
};

static SWITCH_DATA MenuSwitchProfileBBS3[] = {

{ SWITCH_GRAPHIC  , 257, 32,  40, 30, TRUE, &MenuWindowProfileBBS3Graph[11], MenuWindowProfileBBS3Switch },	//メール

//テキスト表示
{ SWITCH_TEXT	  ,  70,  10,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 0], MenuSwitchNone },		//名称
{ SWITCH_TEXT	  , 272,  11,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 1], MenuSwitchNone },	//等级

{ SWITCH_TEXT	  , 140,  39+20*0,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 2], MenuSwitchNone },	//タイトル
{ SWITCH_TEXT	  , 140,  39+20*1,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 3], MenuSwitchNone },	//职业
{ SWITCH_TEXT	  , 140,  39+20*2,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 4], MenuSwitchNone },	//家族

{ SWITCH_TEXT	  , 97,  108,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 6], MenuSwitchNone },	//Ｓｅｌｌ
{ SWITCH_TEXT	  , 97,  130,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 7], MenuSwitchNone },	//Ｂｕｙ
{ SWITCH_TEXT	  , 97,  152,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 8], MenuSwitchNone },	//Ａｂｏｕｔ
{ SWITCH_TEXT	  , 97,  170,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ１行
{ SWITCH_TEXT	  , 97,  184,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ２行
{ SWITCH_TEXT	  , 97,  198,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ３行
{ SWITCH_TEXT	  , 97,  212,   0,  0, TRUE, &MenuWindowProfileBBS3Text[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ４行

//画像
{ SWITCH_GRAPHIC  , 333-37,   9- 1,  12, 12, TRUE, &MenuWindowProfileBBS3Graph[ 0], MenuSwitchCloseButton },	//クローズ

{ SWITCH_GRAPHIC  ,  26-11,  42-13,   0,  0, TRUE, &MenuWindowProfileBBS3Graph[ 0], MenuSwitchNone },	//颜

{ SWITCH_GRAPHIC  ,  73, 102,  20, 20, TRUE, &MenuWindowProfileBBS3Graph[10], MenuSwitchNone },	//sellIcon
{ SWITCH_GRAPHIC  ,  73, 124,  20, 20, TRUE, &MenuWindowProfileBBS3Graph[10], MenuSwitchNone },	//buyIcon
{ SWITCH_GRAPHIC  ,  73, 146,  20, 20, TRUE, &MenuWindowProfileBBS3Graph[10], MenuSwitchNone },	//AboutIcon

//背景画像
{ SWITCH_GRAPHIC  ,  26-11,  36,   0,  0, TRUE, &MenuWindowProfileBBS3Graph[5], MenuSwitchNone },	//ベース
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowProfileBBS3Graph[6], MenuSwitchNone },	//ウインドウ
{ SWITCH_GRAPHIC  ,  12,  27,   0,  0, TRUE, &MenuWindowProfileBBS3Graph[7], MenuSwitchNone },	//バック

{ SWITCH_NONE  , 307, 0, 30, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumGraphMenuProfileBBS3Mail,

	EnumTextMenuProfileBBS3Name,
	EnumTextMenuProfileBBS3Level,

	EnumTextMenuProfileBBS3MyTitle,
	EnumTextMenuProfileBBS3MyJob,
	EnumTextMenuProfileBBS3MyGuild,

	EnumTextMenuProfileBBS3MySell,
	EnumTextMenuProfileBBS3MyBuy,
	EnumTextMenuProfileBBS3MyAbout,
	EnumTextMenuProfileBBS3MyProfile0,
	EnumTextMenuProfileBBS3MyProfile1,
	EnumTextMenuProfileBBS3MyProfile2,
	EnumTextMenuProfileBBS3MyProfile3,

	EnumGraphMenuProfileBBS3Close,

	EnumGraphMenuProfileBBS3Face,

	EnumGraphMenuProfileBBS3SellIcon,
	EnumGraphMenuProfileBBS3BuyIcon,
	EnumGraphMenuProfileBBS3AboutIcon,

	EnumGraphMenuProfileBBS3Base,
	EnumGraphMenuProfileBBS3Window,
	EnumGraphMenuProfileBBS3Back,

	EnumHitMenuProfileBBS3Hit,

	EnumMenuProfileBBS3END
};

//ＢＢＳ４-------------------------------------------------------------

BOOL MenuSwitchProfileBBS4( int mouse );
BOOL MenuWindowProfileBBS4Draw( int mouse );
BOOL MenuWindowProfileBBS4Del(void);

GRAPHIC_SWITCH MenuWindowProfileBBS4WindowGraph[]={

	{GID_SendMailFrame,0,0,0,0,0xFFFFFFFF},				//フレーム
	{GID_SendMailWindow,0,0,0,0,0xFFFFFFFF},			//ウインドウ
	{GID_SendMailBack,0,0,0,0,0x80FFFFFF},				//バック

	{GID_SendMailFrame,0,0,0,0,0xFFFFFFFF},				//フレーム
	{GID_SendMailPetMailWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ
	{GID_SendMailPetMailBack,0,0,0,0,0x80FFFFFF},		//バック

	{GID_SendMailPetMailOn,0,0,0,0,0xFFFFFFFF},			//ペットメールへ
	{GID_SendMailSendOn,0,0,0,0,0xFFFFFFFF},			//メール送信
	{GID_SendMailPetMailBackOn,0,0,0,0,0xFFFFFFFF},		//バック

	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},			//クローズボタン
	{GID_SendMailPetMailItemPanel,0,0,0,0,0xFFFFFFFF},	//アイテムパネル

	{GID_SendMailPetMailDownOn,0,0,0,0,0xFFFFFFFF},	//<<
	{GID_SendMailPetMailUpOn,0,0,0,0,0xFFFFFFFF},	//>>

	{GID_ProfBBS4StrBack,0,0,0,0,0xFFFFFFFF},	//值段枠

};

TEXT_SWITCH ProfileBBS4Text[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"てすとさん"},
};
 
// スイッチ
static SWITCH_DATA ProfileBBS4WindowSwitch[] = {

{ SWITCH_GRAPHIC,225-34,  9,  11, 11, TRUE, &MenuWindowProfileBBS4WindowGraph[ 9], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_DIALOG,  33-11, 84-14, 170,120, TRUE, NULL,MenuSwitchProfileBBS4Dialog },						//ダイアログ表示

{ SWITCH_GRAPHIC,169-15, 39-11,  50, 18, TRUE, &MenuWindowProfileBBS4WindowGraph[ 7], MenuSwitchProfileBBS4Switch },	//メール送信

{ SWITCH_GRAPHIC, 80-8*0, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*1, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*2, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*3, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*4, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*5, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*6, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック
{ SWITCH_GRAPHIC, 80-8*7, 30,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック

{ SWITCH_GRAPHIC, 20, 28,  0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[13], MenuSwitchProfileBBS4Switch },	//值段バック

{ SWITCH_TEXT	, 32-11, 61-13,   0,  0, TRUE, &ProfileBBS4Text[ 0], MenuSwitchNone },							//相手の名称

	
{ SWITCH_GRAPHIC, 25-9, 60-14,   0,  0, TRUE, &MenuWindowProfileBBS4WindowGraph[ 0], MenuSwitchNone },			//メール用フレーム
{ SWITCH_GRAPHIC,  0,  0,0, 0, TRUE, &MenuWindowProfileBBS4WindowGraph[ 1], MenuSwitchNone },				//メール用ウインドウ
{ SWITCH_GRAPHIC, 10+4, 20+3,   0,  0, TRUE, &MenuWindowProfileBBS4WindowGraph[ 2], MenuSwitchNone },			//メール用バック

};

enum{
	EnumProfileBBS4close,

	EnumDialogProfileBBS4Message,

	EnumGraphProfileBBS4Send,

	EnumGraphProfileBBS4Str1,
	EnumGraphProfileBBS4Str2,
	EnumGraphProfileBBS4Str3,
	EnumGraphProfileBBS4Str4,
	EnumGraphProfileBBS4Str5,
	EnumGraphProfileBBS4Str6,
	EnumGraphProfileBBS4Str7,
	EnumGraphProfileBBS4Str8,

	EnumGraphProfileBBS4StrBack,

	EnumTextProfileBBS4ToName,


	EnumGraphProfileBBS4Frame,
	EnumGraphProfileBBS4Window,

	EnumGraphProfileBBS4Back,
	
	EnumMenuProfileBBS4END,
};

//ウインドウ情报----------------------------------------------
const WINDOW_DATA WindowDataProfileBBS1 = {
 0,																		// メニューstatus
     4,5,20,634,424,0x80000080,EnumMenuProfileBBS1END,MenuSwitchProfileBBS1,MenuWindowProfileBBS1,MenuWindowProfileBBS1Draw,MenuWindowProfileBBS1Del
};
const WINDOW_DATA WindowDataProfileBBS2 = {
 0,																		// メニューstatus
     4,5,20,305,398,0x80000080,EnumMenuProfileBBS2END,MenuSwitchProfileBBS2,MenuWindowProfileBBS2,MenuWindowProfileBBS2Draw,MenuWindowProfileBBS2Del
};
const WINDOW_DATA WindowDataProfileBBS3 = {
 0,																		// メニューstatus
     4,5,20,317,250,0x80000080,EnumMenuProfileBBS3END,MenuSwitchProfileBBS3,MenuWindowProfileBBS3,MenuWindowProfileBBS3Draw,MenuWindowProfileBBS3Del
 
};
const WINDOW_DATA WindowDataProfileBBS4 = {
 0,															// メニューウィンドウ
     4,383, 81,232,210,0x80000080,EnumMenuProfileBBS4END,ProfileBBS4WindowSwitch,MenuSwitchProfileBBS4,MenuWindowProfileBBS4Draw,MenuWindowProfileBBS4Del
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateProfileBBS1={
	2,
	0,  0,634,35,
	600,0,34,130,
};
static WINDOW_DRAGMOVE DragMoveDateProfileBBS2={
	2,
	0,  0,305,25,
	300,0,30,130,
};
static WINDOW_DRAGMOVE DragMoveDateProfileBBS3={
	2,
	0,  0,317,25,
	307,0,30,130,
};

//---------------------------------------------------------------------------
//他人のプロフィール
//---------------------------------------------------------------------------

GRAPHIC_SWITCH MenuWindowProfUserNpcGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン

	{GID_StatusBtStatusOn,0,0,0,0,0xFFFFFFFF},	//状态ボタン
	{GID_StatusBtDetailOn,0,0,0,0,0xFFFFFFFF},	//ディティールボタン
	{GID_StatusBtTitleOn,0,0,0,0,0xFFFFFFFF},	//タイトルボタン
	{GID_ProfButtonOff,0,0,0,0,0xFFFFFFFF},		//プロフィールボタン画像

	{GID_ProfBase,0,0,0,0,0xFFFFFFFF},			//ベース画像
	{GID_ProfViewWindow,0,0,0,0,0xFFFFFFFF},	//ウインドウ画像
	{GID_StatusBack,0,0,0,0,0x80808080},		//バック画像

	{GID_ProfButtonToListOn,0,0,0,0,0xFFFFFFFF},		//右矢印

	{GID_TitleSetOn,0,0,0,0,0xFFFFFFFF},			//セットボタン

	{GID_ProfCategoryDef,0,0,0,0,0xFFFFFFFF},			//分类画像デフォルト（未使用

};

BUTTON_SWITCH MenuWindowProfUserNpcButton[]={
	{0},										//クローズボタン
	{0},										//状态ボタン
	{0},										//ディティールボタン
	{0},										//タイトルボタン
	{0},										//スクロールボタン
	{0},										//ウインドウ全体
};

TEXT_SWITCH MenuWindowProfUserNpcText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"名称"},				//名称
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"等级"},			//等级
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"たいとる"},			//名称
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"しょくぎょう"},		//职上
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ぎるど"},			//等级
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"―――"},			//家族
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"せる"},			//Ｓｅｌｌ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ばい"},			//Ｂｕｙ
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"あばうと"},			//Ａｂｏｕｔ
};


static SWITCH_DATA MenuProfUserNpcSwitch[] = {

//テキスト表示
{ SWITCH_TEXT	  ,  70,  10,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 0], MenuSwitchNone },		//名称
{ SWITCH_TEXT	  , 272,  11,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 1], MenuSwitchNone },	//等级

{ SWITCH_TEXT	  , 140,  39+20*0,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 2], MenuSwitchNone },	//タイトル
{ SWITCH_TEXT	  , 140,  39+20*1,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 3], MenuSwitchNone },	//职业
{ SWITCH_TEXT	  , 140,  39+20*2,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 4], MenuSwitchNone },	//家族

{ SWITCH_TEXT	  , 97,  108,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 6], MenuSwitchNone },	//Ｓｅｌｌ
{ SWITCH_TEXT	  , 97,  130,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 7], MenuSwitchNone },	//Ｂｕｙ
{ SWITCH_TEXT	  , 97,  152,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 8], MenuSwitchNone },	//Ａｂｏｕｔ
{ SWITCH_TEXT	  , 97,  170,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ１行
{ SWITCH_TEXT	  , 97,  184,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ２行
{ SWITCH_TEXT	  , 97,  198,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ３行
{ SWITCH_TEXT	  , 97,  212,   0,  0, TRUE, &MenuWindowProfUserNpcText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ４行

//画像
{ SWITCH_GRAPHIC  , 333-37,   9- 1,  12, 12, TRUE, &MenuWindowProfUserNpcGraph[ 0], MenuSwitchCloseButton },	//クローズ

{ SWITCH_GRAPHIC  ,  26-11,  42-13,   0,  0, TRUE, &MenuWindowProfUserNpcGraph[ 0], MenuSwitchNone },	//颜

{ SWITCH_GRAPHIC  ,  73, 102,  20, 20, TRUE, &MenuWindowProfUserNpcGraph[10], MenuSwitchSetProf },	//sellIcon
{ SWITCH_GRAPHIC  ,  73, 124,  20, 20, TRUE, &MenuWindowProfUserNpcGraph[10], MenuSwitchSetProf },	//buyIcon
{ SWITCH_GRAPHIC  ,  73, 146,  20, 20, TRUE, &MenuWindowProfUserNpcGraph[10], MenuSwitchSetProf },	//AboutIcon

//背景画像
{ SWITCH_GRAPHIC  ,  26-11,  36,   0,  0, TRUE, &MenuWindowProfUserNpcGraph[5], MenuSwitchNone },	//ベース
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowProfUserNpcGraph[6], MenuSwitchNone },	//ウインドウ
{ SWITCH_GRAPHIC  ,  12,  27,   0,  0, TRUE, &MenuWindowProfUserNpcGraph[7], MenuSwitchNone },	//バック

{ SWITCH_NONE  , 315, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumTextMenuProfUserNpcName,
	EnumTextMenuProfUserNpcLevel,

	EnumTextMenuProfUserNpcMyTitle,
	EnumTextMenuProfUserNpcMyJob,
	EnumTextMenuProfUserNpcMyGuild,

	EnumTextMenuProfUserNpcMySell,
	EnumTextMenuProfUserNpcMyBuy,
	EnumTextMenuProfUserNpcMyAbout,
	EnumTextMenuProfUserNpcMyProfile0,
	EnumTextMenuProfUserNpcMyProfile1,
	EnumTextMenuProfUserNpcMyProfile2,
	EnumTextMenuProfUserNpcMyProfile3,

	EnumGraphMenuProfUserNpcClose,

	EnumGraphMenuProfUserNpcFace,

	EnumGraphMenuProfUserNpcSellIcon,
	EnumGraphMenuProfUserNpcBuyIcon,
	EnumGraphMenuProfUserNpcAboutIcon,

	EnumGraphMenuProfUserNpcBase,
	EnumGraphMenuProfUserNpcWindow,
	EnumGraphMenuProfUserNpcBack,

	EnumHitMenuProfUserNpc1,

	EnumMenuProfUserNpcEND
};

const WINDOW_DATA WindowDataProfUserNpc = {
 0,																		// メニューstatus
     4,  15,  97,317,250, 0x80000080,  EnumMenuProfUserNpcEND, MenuProfUserNpcSwitch , MenuWindowProfUserNpc,MenuWindowProfUserNpcDraw,MenuWindowDel 
};

#endif