﻿#ifndef _MAP_EFFECT_H_ 
#define _MAP_EFFECT_H_

#define MAX_MAP_EFFECT_BUF	500

typedef struct _MAP_EFFECT
{
	int x, y;
	int dx, dy;
	int gx, gy;
	int ggx, ggy;
	int ex, ey;
	int w, h;
	int cnt;
	_MAP_EFFECT *pre;
	_MAP_EFFECT *next;
	short type;
	short type2;
	short mode;
} MAP_EFFECT;

extern MAP_EFFECT *useBufMapEffect;

extern short mapEffectRainLevel;
extern short oldMapEffectRainLevel;
extern short mapEffectSnowLevel;
extern short oldMapEffectSnowLevel;
extern short mapEffectMoveDir;
extern short mapEffectKamiFubukiLevel;
#ifdef PUK3_WHALE_SHIP
	extern short mapEffectCloudLevel;
	extern short mapEffectCloud2Level;
	extern float mapEffectCloudMoveX, mapEffectCloudMoveY;
	#define CLOUD_SIZE (400)
#endif
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	extern short battleEffectRainLevel;
	extern short battleEffectSnowLevel;
	extern short battleEffectKamiFubukiLevel;
	extern short battleEffectCloudLevel;
#endif

#endif
