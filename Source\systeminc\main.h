﻿// main.cpp ヘッダファイル

#ifndef _MAIN_H_
#define _MAIN_H_

#include <windows.h>
#include "language.h"

//---------------------------------------------------------------------------//
// 概要 ：マクロ（define)宣言                                                //
//---------------------------------------------------------------------------//
#ifndef MULTI_LANG
#define DEF_APPNAME		"魔力宝贝"			//タイトルバーの名称
#endif

//todo: 需加入一个参数如windowsize:1024,768来支持其他分辨率，或重新启用 winsize 参数。

#ifdef WIN_SIZE_DEF
extern int DEF_APPSIZEX;
extern int DEF_APPSIZEY;
#else
//#define DEF_APPSIZEX	1600							//作成するWINDOWSの横幅
//#define DEF_APPSIZEY	1200							//作成するWINDOWSの縦幅

//#define DEF_APPSIZEX	800								//作成するWINDOWSの横幅
//#define DEF_APPSIZEY	600								//作成するWINDOWSの縦幅

#define DEF_APPSIZEX	640								//作成するWINDOWSの横幅
#define DEF_APPSIZEY	480								//作成するWINDOWSの縦幅

//#define DEF_APPSIZEX	320								//作成するWINDOWSの横幅
//#define DEF_APPSIZEY	240								//作成するWINDOWSの縦幅
#endif

#define SCREEN_WIDTH_CENTER			DEF_APPSIZEX/2
#define SCREEN_HEIGHT_CENTER		DEF_APPSIZEY/2

#define DEF_APPSIZEX_SCALE ( DEF_APPSIZEX / 640.0 )
#define DEF_APPSIZEY_SCALE ( DEF_APPSIZEY / 480.0 )
#define DEF_APPSIZEX_SCALE_RINT( x ) ( RINT( ( x ) * DEF_APPSIZEX_SCALE ) )
#define DEF_APPSIZEY_SCALE_RINT( y ) ( RINT( ( y ) * DEF_APPSIZEY_SCALE ) )
//---------------------------------------------------------------------------//
// 概要 ：关数プロト种类宣言                                               //
//---------------------------------------------------------------------------//

int PASCAL WinMain( HINSTANCE 		hInstance, 	HINSTANCE 		hPrevInstance, 	LPSTR lpCmdLine, 	int nCmdShow );	// 初期化关数

LRESULT CALLBACK PASCAL WindMsgProc( HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam );				//メインプロシージャー
BOOL SystemTask( void );																		// 信息ループ关数
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
	// ＯＫ无し信息ボックス
	HWND MakeNonOKMessageBox( char *msg, char *title, int w, int h, int time );
#endif

// Windows 2000 と Me のユーザー设定デスクトップパレットを学习
void GetUserDeskTopClolor( void );
// Windows 2000 と Me のユーザー设定デスクトップパレットを强制的に变更する。
void SetUserDeskTopClolor( void );
// Windows 2000 と Me のユーザー设定デスクトップパレットを元に戾す
void RestoreUserDeskTopColor( void );

//#define P( a ) 	printf( #a "\n")
//#define Pd( a ) printf( #a " = %d\n",a )
//#define Pf( a ) printf( #a " = %f\n",a )
//#define Pp( a ) printf( #a " = %p\n",a )

// グローバル变数
extern HINSTANCE hInst;	// インスタンス
extern HWND hWnd;		// ウィンドウハンドル
extern BOOL WindowMode;		// ウィンドウモード
extern int ResoMode;
extern int LowResoCmdFlag;		// 解像度变更コマンドフラグ
extern int	CmdShow;	// WinMain关数の引数をグローバルにする
extern LPSTR CmdLine; 	// WinMain关数のコマンドライン引数をグローバルにする
extern HANDLE hMutex;	// 同时起动チェックオブジェクトのハンドル
extern COLORREF UserDeskTopColor;	// ユーザー设定デスクトップカラー记忆用（３２ビット）
#ifdef PUK2_NOSHOW
	extern int WinActive;
	enum{
		WA_NOACTIVATE,
		WA_ACTIVE_JUST_AFTER,
		WA_ACTIVATE,
	};
#endif

#ifdef PUK3_LOGIN_VERCHECK
	extern int XGVerNum[3];
#endif

#ifdef _DEBUG
extern BOOL offlineFlag;
extern BOOL logoOffFlag;
extern BOOL debugLogWindow;
extern BOOL logouttKeyFlag;
extern BOOL debugonFlag;	// オートデバッグモード
extern BOOL gracheckOffFlag;	// グラフィックビンサイズチェックフラグ
extern char DebugKey0[ 256 ];		// デバックキー０
extern char DebugKey1[ 256 ];		// デバックキー１
extern char DebugKey2[ 256 ];		// デバックキー２
#endif

#define LANGAGE	"Chinese"
extern char binFolder[128];				// binフォルダの名称
extern char dataFolder[128];			// dataフォルダの名称
extern int giInstallVersion;	// ここでは 0:通常  1:ＥＸ  2:ＶＥＲ２


#endif
