﻿/************************/	
/*	font.c				*/
/************************/
#include "../systeminc/language.h"
#include "../systeminc/system.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"

#define ASCII(a) a-'A'+10
#define ASCII_DEC(a) a-'0'+ 35

//#define FONT_BUFFER_SIZE 1024 	// フォントバッファサイズ
#define FONT_BUFFER_SIZE 4096 	// フォントバッファサイズ

#ifdef PUK2
	// フォントの名称
	char *exfontName[] = { FONT_SONGTI, FONT_SONGTI, FONT_SONGTI, FONT_SONGTI };
	int htmlFontSizeTbl[ FONT_KIND_MAX ] = { 3, 2, 4, 3, 2, 4 };
#endif

// フォントバッファー
FONT_BUFFER FontBuffer[ FONT_BUFFER_SIZE ];
#ifdef PUK2
FONT_BUFFER *FntBufPointer[ FONT_PRIO_MAX ];

int Prio_Font_To_Disp[FONT_PRIO_MAX]={
	DISP_PRIO_IME1,
	DISP_PRIO_MENU,
	DISP_PRIO_IME3,
	DISP_PRIO_WIN2,
	DISP_PRIO_YES_NO_WND,
	DISP_PRIO_DRAG,
};
#endif

// フォントカウンター
int FontCnt = 0;

// フォントの种类构造体
FONT_KIND FontKind[ FONT_KIND_MAX ] = {
	//	半角	全角
	//	横、縦　横、縦
	{ 0, 9,	17, 17, 17 },	// FONT_KIND_MIDDLE		/* 普通のフォント 	*/
	{ 0, 8,	15, 15, 15 },	// FONT_KIND_SMALL		/* 小さいフォント 	*/
	{ 0, 11, 21, 21, 21 },	// FONT_KIND_BIG		/* 大きいフォント 	*/
	
	{ 0, 9,	17, 17, 17 },	// FONT_KIND_MIDDLE2	/* 普通のフォント 	*/
	{ 0, 8,	15, 15, 15 },	// FONT_KIND_SMALL2		/* 小さいフォント 	*/
	{ 0, 11, 21, 21, 21 },	// FONT_KIND_BIG2		/* 大きいフォント 	*/
#ifdef PUK2
	{ 0, 11, 21, 21, 21, 1, 20 },	// FONT_KIND_BIG2__		/* 大きいフォント 	*/

	{ 0, 25, 49, 49, 49, -1, 48 },	// FONT_KIND_V_BIG		/* 特大フォント 	*/

	{ 0, 6,11,10,11, 0,12 },	// FONT_KIND_SIZE_11	/* 11ポイントフォント		*/

	{ 0, 6,12,12,12,-1,12 },	// FONT_KIND_CHAT_S	/* チャットスモール		*/
	{ 0, 8,16,16,16,-1,16 },	// FONT_KIND_CHAT_M	/* チャットミドル		*/
	{ 0,10,20,20,20,-1,20 },	// FONT_KIND_CHAT_L	/* チャットラージ		*/

	{ 0, 6,12,12,12, 0,12 },	// FONT_KIND_SIZE_12	/* 12ポイントフォント		*/
#endif
	{ 0, 7,13,13,13, 0,12 },	// FONT_KIND_BIG3
};

int chatFontSize = 0;			// チャットのフォントサイズ
								// 值は FONT_KIND_??? と同じ

#ifdef PUK2
	unsigned char FontPal[ FONT_PAL_MAX ][3]={
	//		赤		緑		青
		{	0xff,	0xff,	0xff	}, // 0:白
		{	0x00,	0xff,	0xff	}, // 1:水色
		{	0xff,	0x00,	0xff	}, // 2:紫
		{	0x00,	0x00,	0xff	}, // 3:青
		{	0xff,	0xff,	0x00	}, // 4:黄
		{	0x00,	0xff,	0x00	}, // 5:緑
		{	0xff,	0x00,	0x00	}, // 6:赤
		{	0xa0,	0xa0,	0xa4	}, // 7:灰色
		{	0xa6,	0xca,	0xf0	}, // 8:淡い青
		{	0xc0,	0xdc,	0xc0	}, // 9:淡い緑
		{	0x00,	0x00,	0x00	}, // 10:黒
		{	0xcd,	0xcd,	0xcd	}, // 11:明るい灰色
	};

	// フォントサイズ别チャット座标调整テーブル
	extern int DispChatCenterY[ FONT_KIND_MAX ];
	// フォントサイズ别チャット２行目以降の空白数テーブル
	extern int DispChatSpaceSize[ FONT_KIND_MAX ];
	// １行の文字の长さテーブル
	extern unsigned int DispChatOneLineLen[ FONT_KIND_MAX ];
#endif
	
// フォントオブジェクトの作成 *************************************************/
void InitFont( int fontNo )
{
	// フォントの名称
	char *fontName[] =  { FONT_SONGTI, FONT_SONGTI, FONT_SONGTI, FONT_SONGTI };
	int i;
	
	// フォントの种类でループ
	for( i = 0 ; i < FONT_KIND_MAX ; i++ ){
		// フォントがあったら
		if( FontKind[ i ].hFont != NULL ){
			// フォントオブジェクトを抹杀
			DeleteObject( FontKind[ i ].hFont );
		}
	}
	
	//フォントの横幅设定
	//FontZenkauWidth = FONT_KIND + 1;
	//FontHankakuWidth = FONT_KIND / 2 + 1;
	
	// MSゴシックにて指定サイズのフォントオブジェクトを作成
	// 普通のフォント
	FontKind[ 0 ].hFont = CreateFont( 
						//宋体字符宽度17
						16, 	/* フォントの论理高	*/ 
						//32, 	/* フォントの论理高	*/ 
						0, 		/* 文字の论理平均幅	*/ 
						0, 		/* テキスト行の角度	*/ 
						0,		/* ベース ラインとx轴の角度	*/ 

						/* フォントの太さ	*/ 
						//FW_DONTCARE,		// 0 
						//FW_THIN,			// 100 
						//FW_EXTRALIGHT,	// 200 
						//FW_LIGHT,			// 300 
						//FW_NORMAL,			// 400 
						//FW_MEDIUM,		// 500 
						//FW_SEMIBOLD,		// 600 
						FW_BOLD,			// 700 
						//FW_EXTRABOLD,		// 800 
						//FW_HEAVY,			// 900
					
						/* イタリック体属性のフラグ	*/ 
						FALSE, 					
						//TRUE,
						
						/* 下线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
						
						/* 打ち消し线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
					
						/* 文字セット识别子	*/ 
						//ANSI_CHARSET,		// 变な文字
						//UNICODE_CHARSET,	//定义されていない
						//SYMBOL_CHARSET,	// 变な文字
						//SHIFTJIS_CHARSET,	// 普通の文字
						//HANGEUL_CHARSET,	// 普通の文字
						//CHINESEBIG5_CHARSET,// 普通の文字
						//OEM_CHARSET,		// 变な文字
						DEFAULT_CHARSET, 	// 普通の文字
						
						/* 出力精度	*/ 
						OUT_DEFAULT_PRECIS, 
						//OUT_STRING_PRECIS,
						//OUT_CHARACTER_PRECIS, 
						//OUT_STROKE_PRECIS,
						//OUT_TT_PRECIS,
						//OUT_DEVICE_PRECIS,
						//OUT_RASTER_PRECIS,
						//OUT_TT_ONLY_PRECIS,
						//OUT_OUTLINE_PRECIS,
	
						/* クリッピング精度	*/ 
						CLIP_DEFAULT_PRECIS,
						//CLIP_CHARACTER_PRECIS,
						//CLIP_STROKE_PRECIS,
						//CLIP_MASK,
						//CLIP_LH_ANGLES,
						//CLIP_TT_ALWAYS,
						//CLIP_EMBEDDED,
	
						/* 出力品质	*/ 
						DEFAULT_QUALITY,	// フォントの外観は重要视されません。
						//DRAFT_QUALITY,		// フォントの外観は重要视されます。
						//PROOF_QUALITY,		// フォントの外観は最も重要视されます。
						
						
						/* ピッチ */ 
						FIXED_PITCH |
						
						//DEFAULT_PITCH,
						//VARIABLE_PITCH, 
						
						//| FF_ROMAN
						
						/* ファミリ	*/ 
						//FF_DECORATIVE,	// ○装饰付きフォントです。Old Englishなどがあります。
						//FF_DONTCARE,		// ×ファミリを特に指定しないフォント?? またはファミリが不明のフォントです。
						//FF_MODERN,		// ×固定ストローク幅を持つ?? セリフ付きまたはセリフなしのフォントです。Pica?? Elite?? Courier New(R) などがあります。
						FF_ROMAN,			// ○可变ストローク幅を持つ?? セリフ付きフォントです。MS(R) Serifなどがあります。
						//FF_SCRIPT,		// ○手书き风に表示されるフォントです。Script?? Cursiveなどがあります。
						//FF_SWISS,			// ○可变ストローク幅を持つ?? セリフなし
										
						/* 种类フェイス名文字列のアドレス	*/ 				
						//"MSGOTHIC");			
						//"Times New Roman"); 				
						//"MS Scan Serif");
						//"Small Fonts"); 				
						//"HG正楷書体-PRO"); 				
						//"ＭＳ 明朝"); 		//○		
						//"ＭＳ ゴシック"); 	//○			
						//"MS UI Gothic"); 				
						//"ＤＦPOP体"); 		//○		
						//"HG丸?????M-PRO"); 	//×				
						//"ＨＧ?????E-PRO"); 	//×			
						//"ＤＦ特太ゴシック体"); 	//○			
						fontName[ fontNo ] ); 
	// 小さいフォント
	FontKind[ 1 ].hFont = CreateFont( 
						14, 	/* フォントの论理高	*/ 
						0, 		/* 文字の论理平均幅	*/ 
						0, 		/* テキスト行の角度	*/ 
						0,		/* ベース ラインとx轴の角度	*/ 

						/* フォントの太さ	*/ 
						//FW_DONTCARE,		// 0 
						//FW_THIN,			// 100 
						//FW_EXTRALIGHT,	// 200 
						//FW_LIGHT,			// 300 
						//FW_NORMAL,			// 400 
						//FW_MEDIUM,		// 500 
						//FW_SEMIBOLD,		// 600 
						FW_BOLD,			// 700 
						//FW_EXTRABOLD,		// 800 
						//FW_HEAVY,			// 900
					
						/* イタリック体属性のフラグ	*/ 
						FALSE, 					
						//TRUE,
						
						/* 下线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
						
						/* 打ち消し线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
					
						/* 文字セット识别子	*/ 
						//ANSI_CHARSET,		// 变な文字
						//UNICODE_CHARSET,	//定义されていない
						//SYMBOL_CHARSET,	// 变な文字
						//SHIFTJIS_CHARSET,	// 普通の文字
						//HANGEUL_CHARSET,	// 普通の文字
						//CHINESEBIG5_CHARSET,// 普通の文字
						//OEM_CHARSET,		// 变な文字
						DEFAULT_CHARSET, 	// 普通の文字
						
						/* 出力精度	*/ 
						OUT_DEFAULT_PRECIS, 
						//OUT_STRING_PRECIS,
						//OUT_CHARACTER_PRECIS, 
						//OUT_STROKE_PRECIS,
						//OUT_TT_PRECIS,
						//OUT_DEVICE_PRECIS,
						//OUT_RASTER_PRECIS,
						//OUT_TT_ONLY_PRECIS,
						//OUT_OUTLINE_PRECIS,
	
						/* クリッピング精度	*/ 
						CLIP_DEFAULT_PRECIS,
						//CLIP_CHARACTER_PRECIS,
						//CLIP_STROKE_PRECIS,
						//CLIP_MASK,
						//CLIP_LH_ANGLES,
						//CLIP_TT_ALWAYS,
						//CLIP_EMBEDDED,
	
						/* 出力品质	*/ 
						DEFAULT_QUALITY,	// フォントの外観は重要视されません。
						//DRAFT_QUALITY,		// フォントの外観は重要视されます。
						//PROOF_QUALITY,		// フォントの外観は最も重要视されます。
						
						
						/* ピッチ */ 
						FIXED_PITCH |
						
						//DEFAULT_PITCH, 
						//VARIABLE_PITCH, 
						
						//| FF_ROMAN
						
						/* ファミリ	*/ 
						//FF_DECORATIVE,	// ○装饰付きフォントです。Old Englishなどがあります。
						//FF_DONTCARE,		// ×ファミリを特に指定しないフォント?? またはファミリが不明のフォントです。
						//FF_MODERN,		// ×固定ストローク幅を持つ?? セリフ付きまたはセリフなしのフォントです。Pica?? Elite?? Courier New(R) などがあります。
						FF_ROMAN,			// ○可变ストローク幅を持つ?? セリフ付きフォントです。MS(R) Serifなどがあります。
						//FF_SCRIPT,		// ○手书き风に表示されるフォントです。Script?? Cursiveなどがあります。
						//FF_SWISS,			// ○可变ストローク幅を持つ?? セリフなし
										
						/* 种类フェイス名文字列のアドレス	*/ 				
						//"MSGOTHIC");			
						//"Times New Roman"); 				
						//"MS Scan Serif");
						//"Small Fonts"); 				
						//"HG正楷書体-PRO"); 				
						//"ＭＳ 明朝"); 		//○		
						//"ＭＳ ゴシック"); 	//○			
						//"MS UI Gothic"); 				
						//"ＤＦPOP体"); 		//○		
						//"HG丸?????M-PRO"); 	//×				
						//"ＨＧ?????E-PRO"); 	//×			
						//"ＤＦ特太ゴシック体"); 	//○			
						fontName[ fontNo ] ); 
						
	// 大きいフォント
	FontKind[ 2 ].hFont = CreateFont( 
						20, 	/* フォントの论理高	*/ 
						0, 		/* 文字の论理平均幅	*/ 
						0, 		/* テキスト行の角度	*/ 
						0,		/* ベース ラインとx轴の角度	*/ 

						/* フォントの太さ	*/ 
						//FW_DONTCARE,		// 0 
						//FW_THIN,			// 100 
						//FW_EXTRALIGHT,	// 200 
						//FW_LIGHT,			// 300 
						//FW_NORMAL,			// 400 
						//FW_MEDIUM,		// 500 
						//FW_SEMIBOLD,		// 600 
						FW_BOLD,			// 700 
						//FW_EXTRABOLD,		// 800 
						//FW_HEAVY,			// 900
					
						/* イタリック体属性のフラグ	*/ 
						FALSE, 					
						//TRUE,
						
						/* 下线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
						
						/* 打ち消し线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
					
						/* 文字セット识别子	*/ 
						//ANSI_CHARSET,		// 变な文字
						//UNICODE_CHARSET,	//定义されていない
						//SYMBOL_CHARSET,	// 变な文字
						//SHIFTJIS_CHARSET,	// 普通の文字
						//HANGEUL_CHARSET,	// 普通の文字
						//CHINESEBIG5_CHARSET,// 普通の文字
						//OEM_CHARSET,		// 变な文字
						DEFAULT_CHARSET, 	// 普通の文字
						
						/* 出力精度	*/ 
						OUT_DEFAULT_PRECIS, 
						//OUT_STRING_PRECIS,
						//OUT_CHARACTER_PRECIS, 
						//OUT_STROKE_PRECIS,
						//OUT_TT_PRECIS,
						//OUT_DEVICE_PRECIS,
						//OUT_RASTER_PRECIS,
						//OUT_TT_ONLY_PRECIS,
						//OUT_OUTLINE_PRECIS,
	
						/* クリッピング精度	*/ 
						CLIP_DEFAULT_PRECIS,
						//CLIP_CHARACTER_PRECIS,
						//CLIP_STROKE_PRECIS,
						//CLIP_MASK,
						//CLIP_LH_ANGLES,
						//CLIP_TT_ALWAYS,
						//CLIP_EMBEDDED,
	
						/* 出力品质	*/ 
						DEFAULT_QUALITY,	// フォントの外観は重要视されません。
						//DRAFT_QUALITY,		// フォントの外観は重要视されます。
						//PROOF_QUALITY,		// フォントの外観は最も重要视されます。
						
						
						/* ピッチ */ 
						FIXED_PITCH |
						
						//DEFAULT_PITCH, 
						//VARIABLE_PITCH| 
						
						//| FF_ROMAN
						
						/* ファミリ	*/ 
						//FF_DECORATIVE,	// ○装饰付きフォントです。Old Englishなどがあります。
						//FF_DONTCARE,		// ×ファミリを特に指定しないフォント?? またはファミリが不明のフォントです。
						//FF_MODERN,		// ×固定ストローク幅を持つ?? セリフ付きまたはセリフなしのフォントです。Pica?? Elite?? Courier New(R) などがあります。
						FF_ROMAN,			// ○可变ストローク幅を持つ?? セリフ付きフォントです。MS(R) Serifなどがあります。
						//FF_SCRIPT,		// ○手书き风に表示されるフォントです。Script?? Cursiveなどがあります。
						//FF_SWISS,			// ○可变ストローク幅を持つ?? セリフなし
										
						/* 种类フェイス名文字列のアドレス	*/ 				
						//"MSGOTHIC");			
						//"Times New Roman"); 				
						//"MS Scan Serif");
						//"Small Fonts"); 				
						//"HG正楷書体-PRO"); 				
						//"ＭＳ 明朝"); 		//○		
						//"ＭＳ ゴシック"); 	//○			
						//"MS UI Gothic"); 				
						//"ＤＦPOP体"); 		//○		
						//"HG丸?????M-PRO"); 	//×				
						//"ＨＧ?????E-PRO"); 	//×			
						//"ＤＦ特太ゴシック体"); 	//○			
						fontName[ fontNo ] ); 

	// 普通のフォント
	FontKind[ 3 ].hFont = CreateFont( 
						//宋体字符宽度17
						16, 	/* フォントの论理高	*/ 
						//32, 	/* フォントの论理高	*/ 
						0, 		/* 文字の论理平均幅	*/ 
						0, 		/* テキスト行の角度	*/ 
						0,		/* ベース ラインとx轴の角度	*/ 

						/* フォントの太さ	*/ 
						//FW_DONTCARE,		// 0 
						//FW_THIN,			// 100 
						//FW_EXTRALIGHT,	// 200 
						//FW_LIGHT,			// 300 
						//FW_NORMAL,			// 400 
						//FW_MEDIUM,		// 500 
						//FW_SEMIBOLD,		// 600 
						FW_BOLD,			// 700 
						//FW_EXTRABOLD,		// 800 
						//FW_HEAVY,			// 900
					
						/* イタリック体属性のフラグ	*/ 
						FALSE, 					
						//TRUE,
						
						/* 下线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
						
						/* 打ち消し线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
					
						/* 文字セット识别子	*/ 
						//ANSI_CHARSET,		// 变な文字
						//UNICODE_CHARSET,	//定义されていない
						//SYMBOL_CHARSET,	// 变な文字
						//SHIFTJIS_CHARSET,	// 普通の文字
						//HANGEUL_CHARSET,	// 普通の文字
						//CHINESEBIG5_CHARSET,// 普通の文字
						//OEM_CHARSET,		// 变な文字
						DEFAULT_CHARSET, 	// 普通の文字
						
						/* 出力精度	*/ 
						OUT_DEFAULT_PRECIS, 
						//OUT_STRING_PRECIS,
						//OUT_CHARACTER_PRECIS, 
						//OUT_STROKE_PRECIS,
						//OUT_TT_PRECIS,
						//OUT_DEVICE_PRECIS,
						//OUT_RASTER_PRECIS,
						//OUT_TT_ONLY_PRECIS,
						//OUT_OUTLINE_PRECIS,
	
						/* クリッピング精度	*/ 
						CLIP_DEFAULT_PRECIS,
						//CLIP_CHARACTER_PRECIS,
						//CLIP_STROKE_PRECIS,
						//CLIP_MASK,
						//CLIP_LH_ANGLES,
						//CLIP_TT_ALWAYS,
						//CLIP_EMBEDDED,
	
						/* 出力品质	*/ 
						DEFAULT_QUALITY,	// フォントの外観は重要视されません。
						//DRAFT_QUALITY,		// フォントの外観は重要视されます。
						//PROOF_QUALITY,		// フォントの外観は最も重要视されます。
						
						
						/* ピッチ */ 
						FIXED_PITCH |
						
						//DEFAULT_PITCH,
						//VARIABLE_PITCH, 
						
						//| FF_ROMAN
						
						/* ファミリ	*/ 
						//FF_DECORATIVE,	// ○装饰付きフォントです。Old Englishなどがあります。
						//FF_DONTCARE,		// ×ファミリを特に指定しないフォント?? またはファミリが不明のフォントです。
						//FF_MODERN,		// ×固定ストローク幅を持つ?? セリフ付きまたはセリフなしのフォントです。Pica?? Elite?? Courier New(R) などがあります。
						FF_ROMAN,			// ○可变ストローク幅を持つ?? セリフ付きフォントです。MS(R) Serifなどがあります。
						//FF_SCRIPT,		// ○手书き风に表示されるフォントです。Script?? Cursiveなどがあります。
						//FF_SWISS,			// ○可变ストローク幅を持つ?? セリフなし
										
						/* 种类フェイス名文字列のアドレス	*/ 				
						//"MSGOTHIC");			
						//"Times New Roman"); 				
						//"MS Scan Serif");
						//"Small Fonts"); 				
						//"HG正楷書体-PRO"); 				
						//"ＭＳ 明朝"); 		//○		
						//"ＭＳ ゴシック"); 	//○			
						//"MS UI Gothic"); 				
						//"ＤＦPOP体"); 		//○		
						//"HG丸?????M-PRO"); 	//×				
						//"ＨＧ?????E-PRO"); 	//×			
						//"ＤＦ特太ゴシック体"); 	//○			
						fontName[ 1 ] ); 
	// 小さいフォント
	FontKind[ 4 ].hFont = CreateFont( 
						14, 	/* フォントの论理高	*/ 
						0, 		/* 文字の论理平均幅	*/ 
						0, 		/* テキスト行の角度	*/ 
						0,		/* ベース ラインとx轴の角度	*/ 

						/* フォントの太さ	*/ 
						//FW_DONTCARE,		// 0 
						//FW_THIN,			// 100 
						//FW_EXTRALIGHT,	// 200 
						//FW_LIGHT,			// 300 
						//FW_NORMAL,			// 400 
						//FW_MEDIUM,		// 500 
						//FW_SEMIBOLD,		// 600 
						FW_BOLD,			// 700 
						//FW_EXTRABOLD,		// 800 
						//FW_HEAVY,			// 900
					
						/* イタリック体属性のフラグ	*/ 
						FALSE, 					
						//TRUE,
						
						/* 下线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
						
						/* 打ち消し线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
					
						/* 文字セット识别子	*/ 
						//ANSI_CHARSET,		// 变な文字
						//UNICODE_CHARSET,	//定义されていない
						//SYMBOL_CHARSET,	// 变な文字
						//SHIFTJIS_CHARSET,	// 普通の文字
						//HANGEUL_CHARSET,	// 普通の文字
						//CHINESEBIG5_CHARSET,// 普通の文字
						//OEM_CHARSET,		// 变な文字
						DEFAULT_CHARSET, 	// 普通の文字
						
						/* 出力精度	*/ 
						OUT_DEFAULT_PRECIS, 
						//OUT_STRING_PRECIS,
						//OUT_CHARACTER_PRECIS, 
						//OUT_STROKE_PRECIS,
						//OUT_TT_PRECIS,
						//OUT_DEVICE_PRECIS,
						//OUT_RASTER_PRECIS,
						//OUT_TT_ONLY_PRECIS,
						//OUT_OUTLINE_PRECIS,
	
						/* クリッピング精度	*/ 
						CLIP_DEFAULT_PRECIS,
						//CLIP_CHARACTER_PRECIS,
						//CLIP_STROKE_PRECIS,
						//CLIP_MASK,
						//CLIP_LH_ANGLES,
						//CLIP_TT_ALWAYS,
						//CLIP_EMBEDDED,
	
						/* 出力品质	*/ 
						DEFAULT_QUALITY,	// フォントの外観は重要视されません。
						//DRAFT_QUALITY,		// フォントの外観は重要视されます。
						//PROOF_QUALITY,		// フォントの外観は最も重要视されます。
						
						
						/* ピッチ */ 
						FIXED_PITCH |
						
						//DEFAULT_PITCH, 
						//VARIABLE_PITCH, 
						
						//| FF_ROMAN
						
						/* ファミリ	*/ 
						//FF_DECORATIVE,	// ○装饰付きフォントです。Old Englishなどがあります。
						//FF_DONTCARE,		// ×ファミリを特に指定しないフォント?? またはファミリが不明のフォントです。
						//FF_MODERN,		// ×固定ストローク幅を持つ?? セリフ付きまたはセリフなしのフォントです。Pica?? Elite?? Courier New(R) などがあります。
						FF_ROMAN,			// ○可变ストローク幅を持つ?? セリフ付きフォントです。MS(R) Serifなどがあります。
						//FF_SCRIPT,		// ○手书き风に表示されるフォントです。Script?? Cursiveなどがあります。
						//FF_SWISS,			// ○可变ストローク幅を持つ?? セリフなし
										
						/* 种类フェイス名文字列のアドレス	*/ 				
						//"MSGOTHIC");			
						//"Times New Roman"); 				
						//"MS Scan Serif");
						//"Small Fonts"); 				
						//"HG正楷書体-PRO"); 				
						//"ＭＳ 明朝"); 		//○		
						//"ＭＳ ゴシック"); 	//○			
						//"MS UI Gothic"); 				
						//"ＤＦPOP体"); 		//○		
						//"HG丸?????M-PRO"); 	//×				
						//"ＨＧ?????E-PRO"); 	//×			
						//"ＤＦ特太ゴシック体"); 	//○			
						fontName[ 1 ] ); 
						
	// 大きいフォント
	FontKind[ 5 ].hFont = CreateFont( 
						20, 	/* フォントの论理高	*/ 
						0, 		/* 文字の论理平均幅	*/ 
						0, 		/* テキスト行の角度	*/ 
						0,		/* ベース ラインとx轴の角度	*/ 

						/* フォントの太さ	*/ 
						//FW_DONTCARE,		// 0 
						//FW_THIN,			// 100 
						//FW_EXTRALIGHT,	// 200 
						//FW_LIGHT,			// 300 
						//FW_NORMAL,			// 400 
						//FW_MEDIUM,		// 500 
						//FW_SEMIBOLD,		// 600 
						FW_BOLD,			// 700 
						//FW_EXTRABOLD,		// 800 
						//FW_HEAVY,			// 900
					
						/* イタリック体属性のフラグ	*/ 
						FALSE, 					
						//TRUE,
						
						/* 下线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
						
						/* 打ち消し线付き属性のフラグ	*/ 
						FALSE,					
						//TRUE,
					
						/* 文字セット识别子	*/ 
						//ANSI_CHARSET,		// 变な文字
						//UNICODE_CHARSET,	//定义されていない
						//SYMBOL_CHARSET,	// 变な文字
						//SHIFTJIS_CHARSET,	// 普通の文字
						//HANGEUL_CHARSET,	// 普通の文字
						//CHINESEBIG5_CHARSET,// 普通の文字
						//OEM_CHARSET,		// 变な文字
						DEFAULT_CHARSET, 	// 普通の文字
						
						/* 出力精度	*/ 
						OUT_DEFAULT_PRECIS, 
						//OUT_STRING_PRECIS,
						//OUT_CHARACTER_PRECIS, 
						//OUT_STROKE_PRECIS,
						//OUT_TT_PRECIS,
						//OUT_DEVICE_PRECIS,
						//OUT_RASTER_PRECIS,
						//OUT_TT_ONLY_PRECIS,
						//OUT_OUTLINE_PRECIS,
	
						/* クリッピング精度	*/ 
						CLIP_DEFAULT_PRECIS,
						//CLIP_CHARACTER_PRECIS,
						//CLIP_STROKE_PRECIS,
						//CLIP_MASK,
						//CLIP_LH_ANGLES,
						//CLIP_TT_ALWAYS,
						//CLIP_EMBEDDED,
	
						/* 出力品质	*/ 
						DEFAULT_QUALITY,	// フォントの外観は重要视されません。
						//DRAFT_QUALITY,		// フォントの外観は重要视されます。
						//PROOF_QUALITY,		// フォントの外観は最も重要视されます。
						
						
						/* ピッチ */ 
						FIXED_PITCH |
						
						//DEFAULT_PITCH, 
						//VARIABLE_PITCH| 
						
						//| FF_ROMAN
						
						/* ファミリ	*/ 
						//FF_DECORATIVE,	// ○装饰付きフォントです。Old Englishなどがあります。
						//FF_DONTCARE,		// ×ファミリを特に指定しないフォント?? またはファミリが不明のフォントです。
						//FF_MODERN,		// ×固定ストローク幅を持つ?? セリフ付きまたはセリフなしのフォントです。Pica?? Elite?? Courier New(R) などがあります。
						FF_ROMAN,			// ○可变ストローク幅を持つ?? セリフ付きフォントです。MS(R) Serifなどがあります。
						//FF_SCRIPT,		// ○手书き风に表示されるフォントです。Script?? Cursiveなどがあります。
						//FF_SWISS,			// ○可变ストローク幅を持つ?? セリフなし
										
						/* 种类フェイス名文字列のアドレス	*/ 				
						//"MSGOTHIC");			
						//"Times New Roman"); 				
						//"MS Scan Serif");
						//"Small Fonts"); 				
						//"HG正楷書体-PRO"); 				
						//"ＭＳ 明朝"); 		//○		
						//"ＭＳ ゴシック"); 	//○			
						//"MS UI Gothic"); 				
						//"ＤＦPOP体"); 		//○		
						//"HG丸?????M-PRO"); 	//×				
						//"ＨＧ?????E-PRO"); 	//×			
						//"ＤＦ特太ゴシック体"); 	//○			
						fontName[ 1 ] ); 
	
#ifdef PUK2
	i=7;

	// FONT_KIND_V_BIG ===================================
	// 特大フォント
	if (FontKind[ i ].fontnum<0) FontKind[ i ].fontnum=fontNo;
	FontKind[ i ].hFont = CreateFont( FontKind[ i ].siz,0,0,0,FW_BOLD,FALSE,FALSE,FALSE,DEFAULT_CHARSET,OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS,DEFAULT_QUALITY,FIXED_PITCH|FF_ROMAN,exfontName[ FontKind[ i ].fontnum ] );
	i++;

	// FONT_KIND_SIZE_11 ===================================
	// 11ポイントフォント
	if (FontKind[ i ].fontnum<0) FontKind[ i ].fontnum=fontNo;
	FontKind[ i ].hFont = CreateFont( FontKind[ i ].siz,FontKind[ i ].hankakuWidth,0,0,FW_NORMAL,FALSE,FALSE,FALSE,DEFAULT_CHARSET,OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS,DEFAULT_QUALITY,FIXED_PITCH|FF_ROMAN,exfontName[ FontKind[ i ].fontnum ] );
	i++;

	// FONT_KIND_CHAT_S ===================================
	// チャットスモール
	if (FontKind[ i ].fontnum<0) FontKind[ i ].fontnum=fontNo;
	FontKind[ i ].hFont = CreateFont( FontKind[ i ].siz,FontKind[ i ].hankakuWidth,0,0,FW_NORMAL,FALSE,FALSE,FALSE,DEFAULT_CHARSET,OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS,DEFAULT_QUALITY,FIXED_PITCH|FF_ROMAN,exfontName[ FontKind[ i ].fontnum ] );
	i++;
	// FONT_KIND_CHAT_M ===================================
	// チャットミドル
	if (FontKind[ i ].fontnum<0) FontKind[ i ].fontnum=fontNo;
	FontKind[ i ].hFont = CreateFont( FontKind[ i ].siz,FontKind[ i ].hankakuWidth,0,0,FW_NORMAL,FALSE,FALSE,FALSE,DEFAULT_CHARSET,OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS,DEFAULT_QUALITY,FIXED_PITCH|FF_ROMAN,exfontName[ FontKind[ i ].fontnum ] );
	i++;
	// FONT_KIND_CHAT_L ===================================
	// チャットラージ
	if (FontKind[ i ].fontnum<0) FontKind[ i ].fontnum=fontNo;
	FontKind[ i ].hFont = CreateFont( FontKind[ i ].siz,FontKind[ i ].hankakuWidth,0,0,FW_NORMAL,FALSE,FALSE,FALSE,DEFAULT_CHARSET,OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS,DEFAULT_QUALITY,FIXED_PITCH|FF_ROMAN,exfontName[ FontKind[ i ].fontnum ] );
	i++;

	// FONT_KIND_SIZE_12 ===================================
	// 12ポイントフォント
	if (FontKind[ i ].fontnum<0) FontKind[ i ].fontnum=fontNo;
	FontKind[ i ].hFont = CreateFont( FontKind[ i ].siz,FontKind[ i ].hankakuWidth,0,0,FW_NORMAL,FALSE,FALSE,FALSE,DEFAULT_CHARSET,OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS,DEFAULT_QUALITY,FIXED_PITCH|FF_ROMAN,exfontName[ FontKind[ i ].fontnum ] );
	i++;

	// FONT_KIND_BIG3 ===================================
	if (FontKind[i].fontnum < 0) FontKind[i].fontnum = fontNo;
	FontKind[i].hFont = CreateFont(FontKind[i].siz, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
		CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, FIXED_PITCH|FF_ROMAN, exfontName[FontKind[i].fontnum]);
	i++;

	// フォントのサイズテーブルを初期化
	for(i=6;i<FONT_KIND_MAX;i++) htmlFontSizeTbl[ i ]=(FontKind[ i ].siz/4)-1;

	for(i=6;i<FONT_KIND_MAX;i++){
		// フォントのサイズテーブルを初期化
		htmlFontSizeTbl[ i ]=(FontKind[ i ].siz/4)-1;

		// フォントサイズ别チャット座标调整テーブルを初期化
		DispChatCenterY[ i ]=(20-FontKind[ i ].siz);

		// フォントサイズ别チャット２行目以降の空白数テーブルを初期化
		DispChatOneLineLen[ i ]=25-FontKind[ i ].zenkakuWidth;
		if (DispChatOneLineLen[ i ]<1) DispChatOneLineLen[ i ]=1;
		
		// １行の文字の长さテーブルを初期化
		DispChatOneLineLen[ i ]=1980/FontKind[ i ].zenkakuWidth;
	}
#endif
}

// フォントオブジェクトを开放 **************************************************/
void ReleaseFont( void )
{
	int i;
	
	for( i = 0 ; i < FONT_KIND_MAX ; i++ ){
		// ハンドルが无かったら
		if( FontKind[ i ].hFont == NULL ) continue;
		// フォントオブジェクトを抹杀
		DeleteObject( FontKind[ i ].hFont );
	}
}

// フォントをバックサーフェスにセット ////////////////////////////////////////
void PutFont( char fontPrio )
{
	HDC  hDc;
	int i,color;
	BOOL colorFlag = FALSE;
	
#ifndef PUK2
	// フォントパレット
	static DWORD FontPal[]={
			RGB(0xff ,0xff, 0xff ), // 0:白
			RGB(0x00 ,0xff, 0xff ), // 1:水色
			RGB(0xff ,0x00, 0xff ), // 2:紫
			RGB(0x00 ,0x00, 0xff ), // 3:青
			RGB(0xff ,0xff, 0x00 ), // 4:黄
			RGB(0x00 ,0xff, 0x00 ), // 5:緑
			RGB(0xff ,0x00, 0x00 ), // 6:赤
			RGB(0xa0 ,0xa0, 0xa4 ), // 7:灰色
			RGB(0xa6 ,0xca, 0xf0 ), // 8:淡い青
			RGB(0xc0 ,0xdc, 0xc0 )  // 9:淡い緑
		};
#endif
		
	// 表示する文字がなかったら
	if( FontCnt == 0 ) return;
	
	// バックサーフェスのデバイスコンテキストを取得
	lpDraw->lpBACKBUFFER->GetDC( &hDc );
	// マッピングモードの设定 ( デバイスピクセルにマッピング )
	//nOldMap = SetMapMode( hDc, MM_TEXT );
	//SetMapMode( hDc, MM_TEXT ); // しなくていいかも？
	// 背景表示モードの设定 ( 背景色を无视する )
	SetBkMode( hDc, TRANSPARENT );
	
	//hFont = ( HFONT )GetStockObject(DEFAULT_GUI_FONT);
	
	//hOldFont = ( HFONT )SelectObject( hDc, hFont ); // 以前のフォントを代入
	//SelectObject( hDc, hFont ); // 以前のフォントを代入
	// フォントの选择
	//SelectObject( hDc, hFont[ 0 ] ); 
	
	
	// 一番下の改装の时
	if( fontPrio == FONT_PRIO_BACK ){
		// バッファー分ループ
		for( i = 0 ; i < FontCnt ; i++ ){
			// 前に表示か后ろに表示か
			if( FontBuffer[ i ].fontPrio == fontPrio && FontBuffer[ i ].str[ 0 ] != '\0' ){
				// 黒色をセット
				SetTextColor( hDc, 0 );
				// フォントの选择
				SelectObject( hDc, FontKind[ FontBuffer[ i ].fontKind ].hFont ); 
				// まず黒色の影を描画
				TextOut( hDc, FontBuffer[ i ].x + 1, FontBuffer[ i ].y + 1, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
			
			//	TextOut( hDc, FontBuffer[ i ].x + 1, FontBuffer[ i ].y - 0, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
			//	TextOut( hDc, FontBuffer[ i ].x - 1, FontBuffer[ i ].y + 0, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
			//	TextOut( hDc, FontBuffer[ i ].x + 0, FontBuffer[ i ].y - 1, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
			//	TextOut( hDc, FontBuffer[ i ].x - 0, FontBuffer[ i ].y + 1, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
				// 实际の文字を表示
#ifdef PUK2
				color=FontBuffer[ i ].color;
				SetTextColor( hDc, RGB( FontPal[color][0],FontPal[color][1],FontPal[color][2] ) );
#else
				SetTextColor( hDc, FontPal[ FontBuffer[ i ].color ] );
#endif
				// その上から指定された色で描画
				TextOut( hDc, FontBuffer[ i ].x, FontBuffer[ i ].y, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
				
			}
		}
		
	}else{	
	
		// 黒色をセット
		SetTextColor( hDc, 0 );
		// 溜めておいた文字をバックサーフェスに描画
		for( i = 0 ; i < FontCnt ; i++ ){
			// フォントの优先顺位を判别
			if( FontBuffer[ i ].fontPrio == fontPrio ){
				// フォントの选择
				SelectObject( hDc, FontKind[ FontBuffer[ i ].fontKind ].hFont ); 
				// まず黒色の影を描画
				TextOut( hDc, FontBuffer[ i ].x + 1, FontBuffer[ i ].y + 1, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
				//TextOut( hDc, FontBuffer[ i ].x - 1, FontBuffer[ i ].y - 1, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
			}
		}
		
		// 色别にループ（ SetTextColorを呼ぶ回数が减るので处理が速い ）
		for( color = 0 ; color < 10 ; color++ ){
			// バッファー分ループ
			for( i = 0 ; i < FontCnt ; i++ ){
				// 前に表示か后ろに表示か
				if( FontBuffer[ i ].fontPrio == fontPrio ){
					// color の色が使われていたら
					if( FontBuffer[ i ].color == color ){
						// まだこの色がセットされていなかったら
						if( colorFlag == FALSE ){
#ifdef PUK2
							SetTextColor( hDc, RGB( FontPal[color][0],FontPal[color][1],FontPal[color][2] ) );
#else
							SetTextColor( hDc, FontPal[ color ] );
#endif
							colorFlag = TRUE;
						}
						// フォントの选择
						SelectObject( hDc, FontKind[ FontBuffer[ i ].fontKind ].hFont ); 
						// その上から指定された色で描画
						TextOut( hDc, FontBuffer[ i ].x, FontBuffer[ i ].y, FontBuffer[ i ].str, ( int )strlen( FontBuffer[ i ].str ) ); 
					}
				}
			}
			colorFlag = FALSE;
		}
	}
	// マッピングモードを指定
	//SetMapMode( hDc, nOldMap );
	// 以前のフォントを指定
	//SelectObject( hDc ,hOldFont );
	// フォントオブジェクトを抹杀
	//DeleteObject( hFont );
	// デバイスコンテクストを开放
	lpDraw->lpBACKBUFFER->ReleaseDC( hDc );
}

//*****************************************************************************/
// フォント情报をバッファに溜める
//*****************************************************************************/
//	引数：	int x,y			：表示座标
//			char fontPrio	：表示优先顺位
//			int color		：色（フォントパレットから选ぶ）
//			char *str		：表示文字列
//			BOOL hitFlag	：当たり判定フラグ	０：判定无し
//												１：判定のみ
//												２：判定＋ボックス表示
//	戾り值：自分の判定番号
//*****************************************************************************/
int StockFontBuffer( int x, int y, char fontPrio, int color, char *str, BOOL hitFlag )
{
	// カウントオーバーの时
	if( FontCnt >= FONT_BUFFER_SIZE ) return -2;
	
	// フォントバッファーに情报を溜める
	FontBuffer[ FontCnt ].x = x;
	FontBuffer[ FontCnt ].y = y;
	FontBuffer[ FontCnt ].fontPrio = fontPrio;
	FontBuffer[ FontCnt ].color = color;
	FontBuffer[ FontCnt ].fontKind = FONT_KIND_MIDDLE;
	FontBuffer[ FontCnt ].hitFlag = hitFlag;
	FontBuffer[ FontCnt ].hitBoxColor = SYSTEM_PAL_RED;
	// 文字列コピー
	strcpy( FontBuffer[ FontCnt ].str, str );

#ifdef PUK2
	FontBuffer[ FontCnt ].next = NULL;

	if (!FntBufPointer[fontPrio]){
		FntBufPointer[fontPrio]=&FontBuffer[ FontCnt ];
		StockTextDispBuffer( &FontBuffer[ FontCnt ], Prio_Font_To_Disp[ fontPrio ] );
	}else{
		FntBufPointer[fontPrio]->next=&FontBuffer[ FontCnt ];
		FntBufPointer[fontPrio]=&FontBuffer[ FontCnt ];
	}
#endif
	// フォントカウンタープラス
	return FontCnt++;
	
}


//*****************************************************************************/
// フォント情报をバッファに溜める
//*****************************************************************************/
//	引数：	int x,y			：表示座标
//			char fontPrio	：表示优先顺位
//			int fontKind	：フォントの种类
//			int color		：色（フォントパレットから选ぶ）
//			char *str		：表示文字列
//			BOOL hitFlag	：当たり判定フラグ	０：判定无し
//												１：判定のみ
//												２：判定＋ボックス表示
//	戾り值：自分の判定番号
//*****************************************************************************/
int StockFontBuffer( int x, int y, char fontPrio, int fontKind, int color, char *str, BOOL hitFlag )
{
	// カウントオーバーの时
	if( FontCnt >= FONT_BUFFER_SIZE ) return -2;
	
	// フォントバッファーに情报を溜める
	FontBuffer[ FontCnt ].x = x;
	FontBuffer[ FontCnt ].y = y;
	FontBuffer[ FontCnt ].fontPrio = fontPrio;
	FontBuffer[ FontCnt ].color = color;
	FontBuffer[ FontCnt ].fontKind = fontKind;
	FontBuffer[ FontCnt ].hitFlag = hitFlag;
	FontBuffer[ FontCnt ].hitBoxColor = SYSTEM_PAL_RED;
	// 文字列コピー
	strcpy( FontBuffer[ FontCnt ].str, str );
	
#ifdef PUK2
	FontBuffer[ FontCnt ].next = NULL;

	if (!FntBufPointer[fontPrio]){
		FntBufPointer[fontPrio]=&FontBuffer[ FontCnt ];
		StockTextDispBuffer( &FontBuffer[ FontCnt ], Prio_Font_To_Disp[ fontPrio ] );
	}else{
		FntBufPointer[fontPrio]->next=&FontBuffer[ FontCnt ];
		FntBufPointer[fontPrio]=&FontBuffer[ FontCnt ];
	}
#endif
	// フォントカウンタープラス
	return FontCnt++;
	
}


//-------------------------------------------------------------------------//
// フォント情报をバッファに溜める                                          //
//-------------------------------------------------------------------------//
//
// 引数：	x,y			:表示座标
//			fontPrio	:表示优先顺位
//			fontKind	:フォントの种类
//			color		:色（フォントパレットから选ぶ）
//			str			:表示文字列
//			hitFlag		:当たり判定フラグ	０：判定无し
//											１：判定のみ
//											２：判定＋ボックス表示（枠のみ）
//											３：判定＋ボックス表示（涂りつぶし）
//			hitBoxColor	:ボックス（涂りつぶし）の色
//
// 戾り值：自分の判定番号
int StockFontBuffer( short x, short y, unsigned char fontPrio, unsigned char fontKind,
	unsigned char color, char *str, unsigned char hitFlag, unsigned char hitBoxColor )
{
	// カウントオーバーの时
	if( FontCnt >= FONT_BUFFER_SIZE ) return -2;
	
	// フォントバッファーに情报を溜める
	FontBuffer[ FontCnt ].x           = x;
	FontBuffer[ FontCnt ].y           = y;
	FontBuffer[ FontCnt ].fontPrio    = fontPrio;
	FontBuffer[ FontCnt ].color       = color;
	FontBuffer[ FontCnt ].fontKind    = fontKind;
	FontBuffer[ FontCnt ].hitFlag     = hitFlag;
	FontBuffer[ FontCnt ].hitBoxColor = hitBoxColor;
	// 文字列コピー
	strcpy( FontBuffer[ FontCnt ].str, str );
	
#ifdef PUK2
	FontBuffer[ FontCnt ].next = NULL;

	if (!FntBufPointer[fontPrio]){
		FntBufPointer[fontPrio]=&FontBuffer[ FontCnt ];
		StockTextDispBuffer( &FontBuffer[ FontCnt ], Prio_Font_To_Disp[ fontPrio ] );
	}else{
		FntBufPointer[fontPrio]->next=&FontBuffer[ FontCnt ];
		FntBufPointer[fontPrio]=&FontBuffer[ FontCnt ];
	}
#endif
	// フォントカウンタープラス
	return FontCnt++;
	
}


/* フォント情报をバッファに溜める（构造体渡し）********************************/
void StockFontBuffer2( INPUT_STR *pInputStr )
{
	int lineDist = 0; // 左上からのＹOFFセット座标
	int splitPoint = 0;
	char splitStr[ 256 ];
	int flag = FALSE;
	int flag2 = FALSE;
	int cursorSplitPoint = 0;
	int lineDistBak = 0;
	int splitPointBak = 0;
	int cnt;
	int lineCnt = 0;	// 行数カウンター
	
	// カウントオーバーの时
	if( FontCnt >= FONT_BUFFER_SIZE ) return;
	
	// 改行する时
	if( pInputStr->lineMax >= 2 ){
		while( 1 ){
			cnt = 0; flag = FALSE;
			// バックアップ
			splitPointBak = splitPoint;
			
			// 一行に改行コードがあるかチェック
			while( 1 ){
				// 文字の最后なら拔ける
				if( *( pInputStr->buffer + splitPoint + cnt ) == NULL ) break;
				// 改行コードが见つかった时
				if( *( pInputStr->buffer + splitPoint + cnt ) == 0x0d ){ 
					flag = TRUE;
					// 分割文字列作成
					strncpy( splitStr, pInputStr->buffer + splitPoint, cnt );
					// 終端记号を入れる
					*( splitStr + cnt ) = NULL;
					// 分割ポイントを进ませる
					splitPoint = splitPoint + cnt + 1;
					// 行数カウント
					lineCnt++;
					break;
				}
				// 次の文字へ
				cnt++;
				// リミットチェック
				if( cnt == pInputStr->lineLen ) break;
			}
			
			// 改行文字が无い时
			if( flag == FALSE ){
				// 一行に收まらないかチェック
				if( strlen( pInputStr->buffer + splitPoint  ) >= (UINT)pInputStr->lineLen ){
					// 分割文字列作成
					strncpy( splitStr, pInputStr->buffer + splitPoint, pInputStr->lineLen );
					// 終端记号を入れる
					*( splitStr + pInputStr->lineLen ) = NULL;
				
					// 最后の全角文字が分割されている时
					if( GetStrLastByte( splitStr ) == 3 ){ 
						// 一文字戾す
						splitPoint = pInputStr->lineLen - 1 + splitPoint;
						// 終端记号を入れる
						*( splitStr + pInputStr->lineLen - 1 ) = NULL; 
					}
					else splitPoint = pInputStr->lineLen + splitPoint;
					// 行数カウント
					lineCnt++;
				}else break;
				
			}
			
			// カーソルの位置情报を记忆する
			if( flag2 == FALSE ){
				// 最大行数の时
				if( pInputStr->lineMax <= lineCnt ){
					// 检索位置がカーソル位置を超えた时
					if( pInputStr->cursorByte <= splitPoint ){
						cursorSplitPoint = splitPointBak;	// バイト数を记忆
						lineDistBak = lineDist;				// Ｙ座标を记忆
						flag2 = TRUE;						// フラグＯＮ
					}
				}else{
					// 检索位置がカーソル位置を超えた时
					if( pInputStr->cursorByte < splitPoint ){
						// 最大行数の时
						//if( pInputStr->lineMax < lineCnt ){
							// ＭＡＸ文字のとき
							//if( pInputStr->cursorByte != pInputStr->
						//	cursorSplitPoint = splitPointBak;	// バイト数を记忆
						//	lineDistBak = lineDist;				// Ｙ座标を记忆
						//}else{
							cursorSplitPoint = splitPointBak;	// バイト数を记忆
							lineDistBak = lineDist;				// Ｙ座标を记忆
						//}
						flag2 = TRUE;						// フラグＯＮ
					}
				}
			}
			
			// 文字表示バッファに溜める
			StockFontBuffer( pInputStr->x, pInputStr->y + lineDist, pInputStr->fontPrio, pInputStr->fontKind, pInputStr->color, splitStr, 0 );

			
			// Ｙ座标更新
			lineDist += pInputStr->lineDist;
			
		}
		// カーソルの位置情报を记忆する
		if( flag2 == FALSE ){
			cursorSplitPoint = splitPoint;	// バイト数を记忆
			lineDistBak = lineDist;				// Ｙ座标を记忆
		}
		// 余った文字列を表示
		StockFontBuffer( pInputStr->x, pInputStr->y + lineDist, pInputStr->fontPrio, pInputStr->fontKind, pInputStr->color, pInputStr->buffer + splitPoint, 0 );

		
#if 0	
		// 文字列いっぱいの时かつ、最后の行がＮＵＬＬの时
		if( pInputStr->cnt >= pInputStr->len &&  *( pInputStr->buffer + splitPoint ) == NULL ){
			// ＩＭＥ文字列の表示开始座标を求める
			pInputStr->imeX = pInputStr->x + GetStrWidth( pInputStr->buffer + splitPoint - pInputStr->lineLen, pInputStr->fontKind );
			pInputStr->imeY = pInputStr->y + lineDist - pInputStr->lineDist;
		}else{
			// ＩＭＥ文字列の表示开始座标を求める
			pInputStr->imeX = pInputStr->x + GetStrWidth( pInputStr->buffer + splitPoint, pInputStr->fontKind );
			pInputStr->imeY = pInputStr->y + lineDist;
		}
#endif	
	
	}else{
	
		// フォントバッファーに情报を溜める
		FontBuffer[ FontCnt ].x = pInputStr->x;
		FontBuffer[ FontCnt ].y = pInputStr->y;
		FontBuffer[ FontCnt ].fontPrio = pInputStr->fontPrio;
		FontBuffer[ FontCnt ].color = pInputStr->color;
		FontBuffer[ FontCnt ].fontKind = pInputStr->fontKind;
		FontBuffer[ FontCnt ].hitFlag = 0;	// 当たり判定は絶对しない
		// 文字列见えなくする时
		if( pInputStr->blindFlag >= TRUE ){
			int i;
			// 文字列作成
			for( i = 0 ; i < ( pInputStr->cnt - pInputStr->dispByte ) ; i++ ){
				FontBuffer[ FontCnt ].str[ i ] = '*';
			}
			FontBuffer[ FontCnt ].str[ i ] = NULL;
			//sprintf( FontBuffer[ FontCnt ].str, "%*s", pInputStr->cnt, '*' );
		}else{
			// 文字列コピー
			strcpy( FontBuffer[ FontCnt ].str, &pInputStr->buffer[ pInputStr->dispByte ] );
		}
		// 表示幅が设定されている场合
		if( pInputStr->dispWidth ){
			int		cnt,width;
					
			// 表示范围内に收まるまでループ
			cnt  = strlen( FontBuffer[FontCnt].str );
			while( cnt > 0 ){
				width = GetStrWidth( FontBuffer[ FontCnt ].str, pInputStr->fontKind, cnt );
				// はみ出てる？
				if( width > pInputStr->dispWidth ){
					cnt--;
					// 全角文字だったらもう 1Byte 前に
					if( GetStrLastByte( &pInputStr->buffer[pInputStr->dispByte] ) == 3 ){
						cnt--;
					}
				}else{
					// 末尾にヌル文字をはさんで終了
					FontBuffer[FontCnt].str[cnt] = '\0';
					break;
				}
			}
		}

#ifdef PUK2
		FontBuffer[ FontCnt ].next = NULL;

		if (!FntBufPointer[pInputStr->fontPrio]){
			FntBufPointer[pInputStr->fontPrio]=&FontBuffer[ FontCnt ];
			StockTextDispBuffer( &FontBuffer[ FontCnt ], Prio_Font_To_Disp[pInputStr->fontPrio] );
		}else{
			FntBufPointer[pInputStr->fontPrio]->next=&FontBuffer[ FontCnt ];
			FntBufPointer[pInputStr->fontPrio]=&FontBuffer[ FontCnt ];
		}
#endif
#if 0	
		// ＩＭＥ文字列の表示开始座标を求める
		pInputStr->imeX = pInputStr->x + GetStrWidth( pInputStr->buffer, pInputStr->fontKind );
		pInputStr->imeY = pInputStr->y;
#endif
		// フォントカウンタープラス
		FontCnt++;
	}
	
#if 1
	// このダイアログは现在フォーカスがあっているものか？
	if( pNowInputStr == pInputStr ){
		int		cursor;

		// 改行する时
		if( pInputStr->lineLen > 0 ){
			// 表示行数が二行以上の场合
			if( pInputStr->lineMax >= 2 ){
				// 表示上のカーソル位置算出
				cursor = pInputStr->cursorByte - cursorSplitPoint;
				// カーソルの表示座标を求める
				pInputStr->imeX = pInputStr->x + GetStrWidth( &pInputStr->buffer[cursorSplitPoint], pInputStr->fontKind, cursor );
				pInputStr->imeY = pInputStr->y + lineDistBak;
			}else{
				// 表示上のカーソル位置算出
				cursor = pInputStr->cursorByte - pInputStr->dispByte;
				// 表示开始位置より前にならないかチェック
				if( cursor < 0 ){
					// いろいろ调整
					cursor = 0;
					pInputStr->dispByte = pInputStr->cursorByte;
				}
				pInputStr->imeX = pInputStr->x + GetStrWidth( &pInputStr->buffer[pInputStr->dispByte], pInputStr->fontKind, cursor );
				pInputStr->imeY = pInputStr->y;
			}
		}else{
			// 表示上のカーソル位置算出
			cursor = pInputStr->cursorByte - pInputStr->dispByte;
			// 表示开始位置より前にならないかチェック
			if( cursor < 0 ){
				// いろいろ调整
				cursor = 0;
				pInputStr->dispByte = pInputStr->cursorByte;
			}
			pInputStr->imeX = pInputStr->x + GetStrWidth( &pInputStr->buffer[pInputStr->dispByte], pInputStr->fontKind, cursor );
			pInputStr->imeY = pInputStr->y;
		}
	}
#endif
}

#if 0
// フォント表示关数 ///////////////////////////////////////////////////////////
void FontPrint( char *moji, int x, int y, int dispPrio )
{
	// 文字の数だけループ
	while( *moji ){
		// 空白のとき飞ばす
		if( *moji != 0x20 ){
			// 数字のとき
			if( '0' <= *moji && *moji <= '9' ){
				StockDispBuffer( x, y, dispPrio, ASCII_DEC( *moji ), 0 );
			}else 
			if( 'A' <= *moji && *moji <= 'Z' ){
				// アルファベットの时
		//		StockDispBuffer( dispPrio, x, y, 32, 0 );
			}
		}
		// ポインタ进める
		moji++;
		// 表示座标プラス
		x += 16;
	}
}
	
// 数字フォント表示关数 ///////////////////////////////////////////////////////
void FontPrintDec( char *moji, int x, int y, int dispPrio )
{
	// 文字の数だけループ
	while( *moji ){
		if( *moji != 0x20 ){ 
			// 一文字ずつバッファにためる
			StockDispBuffer( x, y, dispPrio, 32, 0 );
		}
		// ポインタ进める
		moji++;
		// 表示座标プラス
		x += 16;
	}
}
#endif

#ifdef PUK2

// フォントプライオリティ制御バッファの初期化
void FontPrioInit()
{
	int i;

	for(i=0;i<FONT_PRIO_MAX;i++) FntBufPointer[i]=NULL;
}

// フォント连続表示の中断命令を出す ++++
void FontBufCut( char fontPrio )
{
	FntBufPointer[fontPrio]=NULL;
}

// フォントをバックサーフェスにセット ++++
void PutFonts( void *FontBuffer )
{
	HDC  hDc;
	int color;
	BOOL colorFlag = FALSE;

	FONT_BUFFER *FntBff = (FONT_BUFFER *)FontBuffer;

	// バックサーフェスのデバイスコンテキストを取得
	lpDraw->lpBACKBUFFER->GetDC( &hDc );

	// 背景表示モードの设定 ( 背景色を无视する )
	SetBkMode( hDc, TRANSPARENT );

	// 一番下の改装の时
	if( FntBff->fontPrio == FONT_PRIO_BACK ){
		// バッファー分ループ
		for( FntBff=(FONT_BUFFER *)FontBuffer;FntBff;FntBff=FntBff->next ){
			if (FntBff->str[0]!='\0'){
#ifdef PUK2
				// フォントの选择
				SelectObject( hDc, FontKind[ FntBff->fontKind ].hFont ); 

				color = FntBff->color;
				if ( !(color&FONT_PAL_NOSHADOW) ){
					// 黒色をセット
					SetTextColor( hDc, 0 );
	
					// フォントの选择
					SelectObject( hDc, FontKind[ FntBff->fontKind ].hFont ); 
	
					// まず黒色の影を描画
					TextOut( hDc, FntBff->x + 1, FntBff->y + 1, FntBff->str, ( int )strlen( FntBff->str ) ); 
				}
				color &= FONT_PAL_PALLETE;
#else
				// 黒色をセット
				SetTextColor( hDc, 0 );

				// フォントの选择
				SelectObject( hDc, FontKind[ FntBff->fontKind ].hFont ); 

				// まず黒色の影を描画
				TextOut( hDc, FntBff->x + 1, FntBff->y + 1, FntBff->str, ( int )strlen( FntBff->str ) ); 
#endif

				// 实际の文字を表示
#ifndef PUK2
				color=FntBff->color;
#endif
				SetTextColor( hDc, RGB( FontPal[color][0],FontPal[color][1],FontPal[color][2] ) );

				// その上から指定された色で描画
				TextOut( hDc, FntBff->x, FntBff->y, FntBff->str, ( int )strlen( FntBff->str ) ); 
			}
		}
		
	}else{	
		// 黒色をセット
		SetTextColor( hDc, 0 );

		// 溜めておいた文字をバックサーフェスに描画
		for( FntBff=(FONT_BUFFER *)FontBuffer;FntBff;FntBff=FntBff->next ){
#ifdef PUK2
			if ( FntBff->color & FONT_PAL_NOSHADOW ) continue;
#endif
			// フォントの选择
			SelectObject( hDc, FontKind[ FntBff->fontKind ].hFont ); 
			// まず黒色の影を描画
			TextOut( hDc, FntBff->x + 1, FntBff->y + 1, FntBff->str, ( int )strlen( FntBff->str ) ); 
		}
		
		// 色别にループ（ SetTextColorを呼ぶ回数が减るので处理が速い ）
#ifdef PUK2
		for( color = 0 ; color < FONT_PAL_MAX ; color++ ){
#else
		for( color = 0 ; color < 10 ; color++ ){
#endif
			// バッファー分ループ
			for( FntBff=(FONT_BUFFER *)FontBuffer;FntBff;FntBff=FntBff->next ){
				// color の色が使われていたら
#ifdef PUK2
				if( (FntBff->color&FONT_PAL_PALLETE) == color ){
#else
				if( FntBff->color == color ){
#endif
					// まだこの色がセットされていなかったら
					if( colorFlag == FALSE ){
						SetTextColor( hDc, RGB( FontPal[color][0],FontPal[color][1],FontPal[color][2] ) );
						colorFlag = TRUE;
					}
					// フォントの选择
					SelectObject( hDc, FontKind[ FntBff->fontKind ].hFont ); 
					// その上から指定された色で描画
					TextOut( hDc, FntBff->x, FntBff->y, FntBff->str, ( int )strlen( FntBff->str ) ); 
				}
			}
			colorFlag = FALSE;
		}
	}
	// デバイスコンテクストを开放
	lpDraw->lpBACKBUFFER->ReleaseDC( hDc );
}

#ifdef PUK2_DEBUG_DRAW

void _Debug_Draw_DrawFuncString( int num )
{
	HDC  hDc;

	// バックサーフェスのデバイスコンテキストを取得
	lpDraw->lpBACKBUFFER->GetDC( &hDc );
	// 背景表示モードの设定 ( 背景色を无视する )
	SetBkMode( hDc, TRANSPARENT );

	// フォントの选择
	SelectObject( hDc, FontKind[ DebugDrawData[num].x2 ].hFont ); 

	// 色の设定
	SetTextColor( hDc, RGB(DebugDrawData[num].rgba.r,DebugDrawData[num].rgba.g,DebugDrawData[num].rgba.b) );

	// フォントの选择
	SelectObject( hDc, FontKind[ DebugDrawData[num].x2 ].hFont ); 

	// まず黒色の影を描画
	TextOut( hDc, DebugDrawData[num].x1, DebugDrawData[num].y1, DebugDrawData[num].str, ( int )strlen( DebugDrawData[num].str ) ); 

	// デバイスコンテクストを开放
	lpDraw->lpBACKBUFFER->ReleaseDC( hDc );
}

#endif

#endif

