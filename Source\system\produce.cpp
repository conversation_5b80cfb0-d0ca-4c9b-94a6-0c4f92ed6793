﻿/************************/
/*	produce.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/produce.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/main.h"
#include "../systeminc/math2.h"
#include "../systeminc/keyboard.h"

// 演出番号
int ProduceNo = 0;
// 演出初期化フラグ
int ProduceInitFlag = TRUE;

// バトルサーフェスの画像作成 **************************************************/
void CopyBackBuffer( void )
{
	// バックサーフェスからバトルサーフェスへコピー
	lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	// 演出番号ランダム（偶数のみ）
	ProduceNo = Rnd( 0, PRODUCE_END - 1 ) & 0xfffffffe;
	//ProduceNo += 2;
	//if( ProduceNo >= PRODUCE_END ) ProduceNo = 0;
	//ProduceNo = PRODUCE_BRAN_SMALL;
}	

#define PRODUCE_UNERI_SPEED 4	// うねりスピード
//#define PRODUCE_HAGARE_LINE_SPEED 15	// はがれ落ちスピード
#define PRODUCE_HAGARE_LINE_SPEED 100	// はがれ落ちスピード

#ifdef WIN_SIZE_DEF
//根据窗口分辨率显示切换效果
// うねり用变数
float workX[1000];
float workY[1000];
float workA[1000];

// 加速移动 *******************************************************************/
#ifdef PUK2
BOOL ProduceAccele(int mode, char ad, float ac)
#else
BOOL ProduceAccele(int mode)
#endif
{
	RECT rect = { 0, 0, DEF_APPSIZEX, DEF_APPSIZEY };
	int endFlag = FALSE;
	int i;
	static float x = 0, x2 = 0, y = 0, y2 = 0;
	static float a = 0;
	static float cnt = 0;

	// 初期化する时
	if (ProduceInitFlag == TRUE) {
		ProduceInitFlag = FALSE;
		endFlag = -1;
		x = 0; x2 = 0; y = 0; a = 0;
	}

	// 移动方向で分岐
	switch (mode) {

	case 0:	// 上加速移动
		y -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac != 0) a += ac;
		else a += 0.3F;
#else
		a += 0.3F;
#endif
		// リミットチェック
		if (y <= -DEF_APPSIZEY) endFlag = TRUE;
		break;

	case 1:	// 下加速移动
		y += a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac != 0) a += ac;
		else a += 0.3F;
#else
		a += 0.3F;
#endif
		// リミットチェック
		if (y >= DEF_APPSIZEY) endFlag = TRUE;
		break;

	case 2:	// 左加速移动
		x -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac != 0) a += ac;
		else a += 0.4F;
#else
		a += 0.4F;
#endif
		// リミットチェック
		if (x <= -DEF_APPSIZEX) endFlag = TRUE;
		break;

	case 3:	// 右加速移动
		x += a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac != 0) a += ac;
		else a += 0.4F;
#else
		a += 0.4F;
#endif
		// リミットチェック
		if (x >= DEF_APPSIZEX) endFlag = TRUE;
		break;

	case 4:	// 左右加速移动
		x += a;		// 移动
		x2 -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac != 0) a += ac;
		else a += 0.4F;
#else
		a += 0.4F;
#endif
		// リミットチェック
		if (x >= DEF_APPSIZEX) endFlag = TRUE;
		//if( x >=  ) endFlag = TRUE;
		//if( a >= 100 ) endFlag = TRUE;
		break;

	case 5:	// 上下加速移动
		y += a;	// 移动
		y2 -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac != 0) a += ac;
		else a += 0.3F;
#else
		a += 0.3F;
#endif
		// リミットチェック
		if (y >= DEF_APPSIZEY) endFlag = TRUE;
		break;

	case 6:	// うねり左右加速移动
		if (endFlag == -1) {
			int i;
			// うねり用Ｘ座标
			for (i = 0; i < DEF_APPSIZEY; i++) {
				workX[i] = 0;
				workA[i] = 0;
			}
#ifdef PUK2
			if (ac != 0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		}
		// 移动处理
		for (i = 0; i < cnt; i++) {
			// 奇数の时
			if (i & 1) {
				workA[i] += a;
				workX[i] += workA[i];

				workA[DEF_APPSIZEY - i] += a;
				workX[DEF_APPSIZEY - i] += workA[DEF_APPSIZEY - i];
			}
			else {
				workA[i] -= a;
				workX[i] += workA[i];

				workA[DEF_APPSIZEY - 2 - i] -= a;
				workX[DEF_APPSIZEY - 2 - i] += workA[DEF_APPSIZEY - 2 - i];
			}
		}
		// 演出时间调整
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt += PRODUCE_UNERI_SPEED;
		if (cnt > DEF_APPSIZEY / 2 + 1) cnt = float(DEF_APPSIZEY / 2 + 1);
#else
		if (cnt < 240) cnt += PRODUCE_UNERI_SPEED;
#endif
		// 終了条件
		if (workX[DEF_APPSIZEY / 2 + 1] < -DEF_APPSIZEX) endFlag = TRUE;
		if (workX[DEF_APPSIZEY / 2 + 1] > DEF_APPSIZEX) endFlag = TRUE;

		break;

	case 7:	// 上下ライン加速移动
		if (endFlag == -1) {
			int i;
			// うねり用Ｘ座标
			for (i = 0; i < DEF_APPSIZEY; i++) {
				workY[i] = (float)i;
				//workX[ i ] = 0;
				workA[i] = 0;
			}
#ifdef PUK2
			if (ac != 0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		}
		// 移动处理
		for (i = 0; i < cnt; i++) {
			if (i <= DEF_APPSIZEY / 2) {
				workA[i] -= a;
				workY[i] += workA[i];
			}
			if (DEF_APPSIZEY - i > DEF_APPSIZEY / 2) {
				workA[DEF_APPSIZEY - i] += a;
				workY[DEF_APPSIZEY - i] += workA[DEF_APPSIZEY - i];
			}
			// 奇数の时
			//if( i & 1 ){ 
			//	workX[ i ] += workA[ i ];
			//	workX[ 480 - i ] += workA[ 480 - i ];
			//}else{ 
			//	workX[ i ] -= workA[ i ];
			//	workX[ 478 - i ] -= workA[ 478 - i ];
			//}
		}
		// 演出时间调整
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt += PRODUCE_UNERI_SPEED;
		if (cnt > DEF_APPSIZEY / 2 + 1) cnt = float(DEF_APPSIZEY / 2 + 1);
#else
		if (cnt < 241) cnt += PRODUCE_UNERI_SPEED;
#endif
		//if( cnt < 241 ) cnt += 1;
		// 終了条件
		if (workY[DEF_APPSIZEY / 2 + 1] < -DEF_APPSIZEX) endFlag = TRUE;
		if (workY[DEF_APPSIZEY / 2 + 1] > DEF_APPSIZEX) endFlag = TRUE;

		break;

	case 8:	// ラインはがれＯＵＴ
		if (endFlag == -1) {
			int i;
			// うねり用Ｘ座标
			for (i = 0; i < DEF_APPSIZEY; i++) {
				workY[i] = (float)i;
				//workX[ i ] = 0;
				workA[i] = 0;
			}
#ifdef PUK2
			if (ac != 0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		}
		// 移动处理
		for (i = 0; i < cnt; i++) {
			workA[i] += a;
			if (workA[i] < PRODUCE_HAGARE_LINE_SPEED) workY[i] += workA[i];
			else workY[i] += PRODUCE_HAGARE_LINE_SPEED;
		}
		// 演出时间调整
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt += 16;
		if (cnt > DEF_APPSIZEY) cnt = float(DEF_APPSIZEY);
#else
		if (cnt < 480) cnt += 16;
		//if( cnt < 479 ) cnt += PRODUCE_UNERI_SPEED * 2;
		if (cnt > 480) cnt = 480;
#endif
		// 終了条件
		if (workY[DEF_APPSIZEY - 1] > DEF_APPSIZEY + 120) endFlag = TRUE;

		break;

	}

	// バックサーフェスを黒でクリアー
	ClearBackSurface();

	// 左右分裂の时
	if (mode == 4) {
		int i;
		// 高さ分ループ
		for (i = 0; i < DEF_APPSIZEY; i++) {
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			// 奇数の时
			if (i & 1) {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2((int)x, i, &rect, lpBattleSurface);
			}
			else {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2((int)x2, i, &rect, lpBattleSurface);
			}
		}
	}
	// 上下分裂の时
	else if (mode == 5) {
		int i;
		// 高さ分ループ
		for (i = 0; i < DEF_APPSIZEY; i++) {
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;

			// 奇数の时
			if (i & 1) {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2(0, i + (int)y, &rect, lpBattleSurface);
			}
			else {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2(0, i + (int)y2, &rect, lpBattleSurface);
			}
		}
	}
	// うねり分裂の时
	else if (mode == 6) {
		int i;
		//float workX = x, workX2 = x2, workA = a;
		// 高さ分ループ
		for (i = 0; i < DEF_APPSIZEY; i++) {

			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;

			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2((int)workX[i], i, &rect, lpBattleSurface);

		}
	}
	// 上下ライン加速移动
	else if (mode == 7 || mode == 8) {
		int i;
		//float workX = x, workX2 = x2, workA = a;
		// 高さ分ループ
		//for( i = 0 ; i < 480 ; i++ ){
		for (i = DEF_APPSIZEY - 1; i > 0; i--) {

			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;

			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2(0, (int)workY[i], &rect, lpBattleSurface);

		}
	}
	else {
		// バックバッファーに高速転送（矩形指定）
		DrawSurfaceFast2((int)x, (int)y, &rect, lpBattleSurface);
	}


	// バトルサーフェスの画像作成
	//CopyBackBuffer();
	if (endFlag == TRUE) {

		// 初期化
		x = 0;
		x2 = 0;
		y = 0;
		y2 = 0;
		a = 0;

		// ゲームの状态变更
		if (GameState == GAME_FIELD_TO_ENCOUNT) GameState = GAME_ENCOUNT_TO_BATTLE;
		else if (GameState == GAME_ENCOUNT_TO_BATTLE) GameState = GAME_BATTLE;

		// バトルサーフェスを黒でクリア
		//ClearSurface( lpBattleSurface );
		// バックサーフェスを黒でクリアー
		//ClearBackSurface();	
		return TRUE;
	}
	return FALSE;
}

// 减速移动 *******************************************************************/
#ifdef PUK2
BOOL ProduceBrake(int mode, char ad, float ac)
#else
BOOL ProduceBrake(int mode)
#endif
{
	RECT rect = { 0, 0, DEF_APPSIZEX, DEF_APPSIZEY };
	int i;
	static int endFlag = -1;
	static float x = 0, x2 = 0, y = 0, y2 = 0;
	static float a;
	static int cnt;

	// 初期化する时
	if (ProduceInitFlag == TRUE) {
		ProduceInitFlag = FALSE;
		endFlag = -1;
	}

	// 移动方向で分岐
	switch (mode) {

	case 0:	// 上减速移动
		if (endFlag == -1) {
			x = 0;
			//y = 480;
			y = 513.2F;
			a = 17.4F;
			endFlag = FALSE;
		}
		y -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac != 0) a -= ac;
		else a -= 0.3F;
#else
		a -= 0.3F;
#endif
		// リミットチェック
		if (a <= 0) endFlag = TRUE;
		break;

	case 1:	// 下减速移动
		if (endFlag == -1) {
			x = 0;
			//y = -480;
			y = -513.2F;
			a = 17.4F;
			endFlag = FALSE;
		}
		y += a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac != 0) a -= ac;
		else a -= 0.3F;
#else
		a -= 0.3F;
#endif
		// リミットチェック
		if (a <= 0) endFlag = TRUE;
		break;

	case 2:	// 左减速移动
		if (endFlag == -1) {
			//x = 640;
			x = 661;
			y = 0;
			a = 22.8F;
			endFlag = FALSE;
		}
		x -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac != 0) a -= ac;
		else a -= 0.4F;
#else
		a -= 0.4F;
#endif
		// リミットチェック
		if (a <= 0) endFlag = TRUE;
		break;

	case 3:	// 右减速移动
		if (endFlag == -1) {
			//x = -640;
			x = -661;
			y = 0;
			a = 22.8F;
			endFlag = FALSE;
		}
		x += a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac != 0) a -= ac;
		else a -= 0.4F;
#else
		a -= 0.4F;
#endif
		// リミットチェック
		if (a <= 0) endFlag = TRUE;
		break;

	case 4:	// 左右减速移动
		if (endFlag == -1) {
			//x = -640;
			x = -661;
			x2 = 661;
			y = 0;
			a = 22.8F;
			endFlag = FALSE;
		}
		x += a;		// 移动
		x2 -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac != 0) a -= ac;
		else a -= 0.4F;
#else
		a -= 0.4F;
#endif
		// リミットチェック
		if (a <= 0) endFlag = TRUE;
		break;

	case 5:	// 下减速移动
		if (endFlag == -1) {
			x = 0;
			//y = -480;
			y = -513.2F;
			y2 = 513.2F;
			a = 17.4F;
			endFlag = FALSE;
		}
		y += a;	// 移动
		y2 -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac != 0) a -= ac;
		else a -= 0.3F;
#else
		a -= 0.3F;
#endif
		// リミットチェック
		if (a <= 0) endFlag = TRUE;
		break;

	case 6:	// 左右うねり减速移动
		if (endFlag == -1) {
			int i;
			// うねり用Ｘ座标
			for (i = 0; i < DEF_APPSIZEY; i++) {
				workX[i] = 0;
				workA[i] = 0;
			}
#ifdef PUK2
			if (ac != 0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;

			while (1) {
				for (i = 0; i < cnt; i++) {
					// 奇数の时
					if (i & 1) {
						workA[i] += a;
						workX[i] += workA[i];

						workA[DEF_APPSIZEY - i] += a;
						workX[DEF_APPSIZEY - i] += workA[DEF_APPSIZEY - i];
					}
					else {
						workA[i] -= a;
						workX[i] += workA[i];

						workA[DEF_APPSIZEY - 2 - i] -= a;
						workX[DEF_APPSIZEY - 2 - i] += workA[DEF_APPSIZEY - 2 - i];
					}
				}
				// 演出时间调整
#ifdef PUK2
				if (ad) cnt += ad;
				else cnt += PRODUCE_UNERI_SPEED;
				if (cnt > DEF_APPSIZEY / 2) cnt = DEF_APPSIZEY / 2;
#else
				if (cnt < 240) cnt += PRODUCE_UNERI_SPEED;
#endif
				// 終了条件
				if (workX[DEF_APPSIZEY / 2 + 1] < -DEF_APPSIZEX) break;
				if (workX[DEF_APPSIZEY / 2 + 1] > DEF_APPSIZEX) break;
			}
		}
		// 移动处理
		for (i = 0; i < DEF_APPSIZEY / 2; i++) {
			// 奇数の时
			if (i & 1) {
				if (workX[i] <= 1) workX[i] = 0;
				else {
					workX[i] -= workA[i];
					workA[i] -= a;
				}

				if (workX[DEF_APPSIZEY - i] <= 1) workX[DEF_APPSIZEY - i] = 0;
				else {
					workX[DEF_APPSIZEY - i] -= workA[DEF_APPSIZEY - i];
					workA[DEF_APPSIZEY - i] -= a;
				}
			}
			else {
				if (workX[i] >= -1) workX[i] = 0;
				else {
					workX[i] -= workA[i];
					workA[i] += a;
				}

				if (workX[DEF_APPSIZEY - 2 - i] >= -1) workX[DEF_APPSIZEY - 2 - i] = 0;
				else {
					workX[DEF_APPSIZEY - 2 - i] -= workA[DEF_APPSIZEY - 2 - i];
					workA[DEF_APPSIZEY - 2 - i] += a;
				}
			}
		}
		//
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt++;
		if (cnt > DEF_APPSIZEY / 2) cnt = DEF_APPSIZEY / 2;
#else
		if (cnt < 240) cnt++;
#endif
		// 終了条件
		if (workX[0] == 0) endFlag = TRUE;

		break;


	case 7:	// 上下ライン减速移动

		if (endFlag == -1) {
			int i;
			// うねり用Ｘ座标
			for (i = 0; i < DEF_APPSIZEY; i++) {
				workY[i] = (float)i;
				workA[i] = 0;
			}
#ifdef PUK2
			if (ac != 0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;

			while (1) {
				// 移动处理
				for (i = 0; i < cnt; i++) {
					if (i <= DEF_APPSIZEY / 2) {
						workA[i] -= a;
						workY[i] += workA[i];
					}
					if (DEF_APPSIZEY - i > DEF_APPSIZEY / 2) {
						workA[DEF_APPSIZEY - i] += a;
						workY[DEF_APPSIZEY - i] += workA[DEF_APPSIZEY - i];
					}
				}
				// 演出时间调整
#ifdef PUK2
				if (cnt < DEF_APPSIZEY / 2 + 1) {
					if (ad) cnt += ad;
					else cnt += PRODUCE_UNERI_SPEED;
				}
#else
				if (cnt < 241) cnt += PRODUCE_UNERI_SPEED;
				//if( cnt < 241 ) cnt += 1;
#endif
				// 終了条件
				if (workY[DEF_APPSIZEY / 2 + 1] < -DEF_APPSIZEX) break;
				if (workY[DEF_APPSIZEY / 2 + 1] > DEF_APPSIZEX) break;
			}
		}

		// 移动处理
		for (i = 0; i < cnt; i++) {
			if (i <= DEF_APPSIZEY / 2) {
				workY[i] -= workA[i];
				workA[i] += a;
			}
			if (DEF_APPSIZEY - i > DEF_APPSIZEY / 2) {
				workY[DEF_APPSIZEY - i] -= workA[DEF_APPSIZEY - i];
				workA[DEF_APPSIZEY - i] -= a;
			}
		}
		if (workY[DEF_APPSIZEY / 2] >= DEF_APPSIZEY / 2) {
			// 演出时间调整
#ifdef PUK2
			if (ad) cnt -= ad;
			else cnt -= PRODUCE_UNERI_SPEED;
#else
			if (cnt > 0) cnt -= PRODUCE_UNERI_SPEED;
#endif
			if (cnt < 0) cnt = 0;
		}
		// 終了条件
		if (workY[0] >= 0) endFlag = TRUE;
		//if( workY[ 241 ] > 640 ) endFlag = TRUE;

		break;

	case 8:	// ラインはがれＩＮ

		//640*480分辨率以上使用32会导致画面出现黑色线条
		if(DEF_APPSIZEY > 480)
			ad = 40;

		if (endFlag == -1) {
			int i;
			// うねり用Ｘ座标
			for (i = 0; i < DEF_APPSIZEY; i++) {
				workY[i] = (float)i;
				workA[i] = 0;
			}
#ifdef PUK2
			if (ac != 0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;

			while (1) {
				// 移动处理
				for (i = DEF_APPSIZEY - 1; i >= DEF_APPSIZEY - 1 - cnt; i--) {
#ifdef PUK3_PRODUCE_MEMDESTORY
					if (i < 0) continue;
#endif
					workA[i] -= a;
					if (workA[i] > -PRODUCE_HAGARE_LINE_SPEED) workY[i] += workA[i];
					else workY[i] -= PRODUCE_HAGARE_LINE_SPEED;
				}
				// 演出时间调整
#ifdef PUK2
				// 演出时间调整
				if (ad) cnt += ad;
				else cnt += 16;
				if (cnt > DEF_APPSIZEY) cnt = DEF_APPSIZEY;
#else
				if (cnt < 480) cnt += 16;
				//if( cnt < 479 ) cnt += PRODUCE_UNERI_SPEED * 2;
				if (cnt > 480) cnt = 480;
#endif
				// 終了条件
				if (workY[0] < 0) break;
			}
		}

		// 移动处理
		for (i = DEF_APPSIZEY - 1; i >= DEF_APPSIZEY - 1 - cnt; i--) {
#ifdef PUK3_PRODUCE_MEMDESTORY
			if (i < 0) continue;
#endif
			if (workA[i] > -PRODUCE_HAGARE_LINE_SPEED) workY[i] -= workA[i];
			else workY[i] += PRODUCE_HAGARE_LINE_SPEED;
			workA[i] += a;
		}
		if (workY[0] >= 0) {
			// 演出时间调整
#ifdef PUK2
			if (ad) cnt -= ad;
			else cnt -= 16;
#else
			if (cnt > 0) cnt -= 16;
#endif
			if (cnt < 0) cnt = 0;
		}
		// 終了条件
		if (workY[DEF_APPSIZEY - 1] >= DEF_APPSIZEY - 1) endFlag = TRUE;
		//if( workY[ 241 ] > 640 ) endFlag = TRUE;

		break;

	}
	// バックサーフェスを黒でクリアー
	ClearBackSurface();
	// 左右分裂の时
	if (mode == 4) {
		int i;
		// 高さ分ループ
		for (i = 0; i < DEF_APPSIZEY; i++) {
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			// 奇数の时
			if (i & 1) {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2((int)x, i, &rect, lpBattleSurface);
			}
			else {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2((int)x2, i, &rect, lpBattleSurface);
			}
		}
	}
	// 左右分裂の时
	else if (mode == 5) {
		int i;
		// 高さ分ループ
		for (i = 0; i < DEF_APPSIZEY; i++) {
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			// 奇数の时
			if (i & 1) {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2(0, i + (int)y, &rect, lpBattleSurface);
			}
			else {
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2(0, i + (int)y2, &rect, lpBattleSurface);
			}
		}
	}
	// うねり分裂の时
	else if (mode == 6) {
		int i;
		for (i = 0; i < DEF_APPSIZEY; i++) {

			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;

			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2((int)workX[i], i, &rect, lpBattleSurface);

		}
	}
	// 上下ライン加速移动
	else if (mode == 7 || mode == 8) {
		int i;
		//float workX = x, workX2 = x2, workA = a;
		// 高さ分ループ
		for (i = 0; i < DEF_APPSIZEY; i++) {
			//for( i = 479 ; i > 0 ; i-- ){

			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;

			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2(0, (int)workY[i], &rect, lpBattleSurface);

		}
	}
	else {
		// バックバッファーに高速転送（矩形指定）
		DrawSurfaceFast2((int)x, (int)y, &rect, lpBattleSurface);
	}

	// バトルサーフェスの画像作成
	//CopyBackBuffer();
	if (endFlag == TRUE) {
		// 初期化

		// ゲームの状态变更
		if (GameState == GAME_FIELD_TO_ENCOUNT) GameState = GAME_ENCOUNT_TO_BATTLE;
		else if (GameState == GAME_ENCOUNT_TO_BATTLE) GameState = GAME_BATTLE;
		// フラグ初期化
		endFlag = -1;
		// バトルサーフェスを黒でクリア
		//ClearSurface( lpBattleSurface );
		// バックサーフェスを黒でクリアー
		//ClearBackSurface();	
		return TRUE;
	}
	return FALSE;
}
#else
// うねり用变数
float workX[ 480 ];
float workY[ 480 ];
float workA[ 480 ];

// 加速移动 *******************************************************************/
#ifdef PUK2
BOOL ProduceAccele( int mode, char ad, float ac )
#else
BOOL ProduceAccele( int mode )
#endif
{
	RECT rect = { 0, 0, DEF_APPSIZEX, DEF_APPSIZEY };
	int endFlag = FALSE;
	int i;
	static float x = 0, x2 = 0, y = 0, y2 = 0; 
	static float a = 0;
	static float cnt = 0;
	
	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		endFlag = -1;
		x = 0; x2 = 0; y = 0;a = 0;
	}
	
	// 移动方向で分岐
	switch( mode ){
	
	case 0:	// 上加速移动
		y -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac!=0) a += ac;
		else a += 0.3F;
#else
		a += 0.3F; 
#endif
		// リミットチェック
		if( y <= -480 ) endFlag = TRUE;
		break;
		
	case 1:	// 下加速移动
		y += a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac!=0) a += ac;
		else a += 0.3F;
#else
		a += 0.3F; 
#endif
		// リミットチェック
		if( y >= 480 ) endFlag = TRUE;
		break;
		
	case 2:	// 左加速移动
		x -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac!=0) a += ac;
		else a += 0.4F;
#else
		a += 0.4F; 
#endif
		// リミットチェック
		if( x <= -640 ) endFlag = TRUE;
		break;
		
	case 3:	// 右加速移动
		x += a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac!=0) a += ac;
		else a += 0.4F;
#else
		a += 0.4F; 
#endif
		// リミットチェック
		if( x >= 640 ) endFlag = TRUE;
		break;
		
	case 4:	// 左右加速移动
		x += a;		// 移动
		x2 -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac!=0) a += ac;
		else a += 0.4F;
#else
		a += 0.4F;
#endif
		// リミットチェック
		if( x >= 640 ) endFlag = TRUE;
		//if( x >=  ) endFlag = TRUE;
		//if( a >= 100 ) endFlag = TRUE;
		break;
		
	case 5:	// 上下加速移动
		y += a;	// 移动
		y2 -= a;	// 移动
		// 加速度プラス
#ifdef PUK2
		if (ac!=0) a += ac;
		else a += 0.3F;
#else
		a += 0.3F; 
#endif
		// リミットチェック
		if( y >= 480 ) endFlag = TRUE;
		break;
		
	case 6:	// うねり左右加速移动
		if( endFlag ==  -1 ){
			int i;
			// うねり用Ｘ座标
			for( i = 0 ; i < 480 ; i++ ){
				workX[ i ] = 0;
				workA[ i ] = 0;
			}
#ifdef PUK2
			if (ac!=0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		}
		// 移动处理
		for( i = 0 ; i < cnt ; i++ ){
			// 奇数の时
			if( i & 1 ){ 
				workA[ i ] += a;
				workX[ i ] += workA[ i ];
				
				workA[ 480 - i ] += a;
				workX[ 480 - i ] += workA[ 480 - i ];
			}else{ 
				workA[ i ] -= a;
				workX[ i ] += workA[ i ];
				
				workA[ 478 - i ] -= a;
				workX[ 478 - i ] += workA[ 478 - i ];
			}
		}
		// 演出时间调整
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt += PRODUCE_UNERI_SPEED;
		if( cnt > 241 ) cnt = 241;
#else
		if( cnt < 240 ) cnt += PRODUCE_UNERI_SPEED;
#endif
		// 終了条件
		if( workX[ 241 ] < -640 ) endFlag = TRUE;
		if( workX[ 241 ] > 640 ) endFlag = TRUE;
		
		break;
		
	case 7:	// 上下ライン加速移动
		if( endFlag ==  -1 ){
			int i;
			// うねり用Ｘ座标
			for( i = 0 ; i < 480 ; i++ ){
				workY[ i ] = ( float )i;
				//workX[ i ] = 0;
				workA[ i ] = 0;
			}
#ifdef PUK2
			if (ac!=0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		}
		// 移动处理
		for( i = 0 ; i < cnt; i++ ){
			if( i <= 240 ){
				workA[ i ] -= a;
				workY[ i ] += workA[ i ];
			}
			if( 480 - i > 240 ){
				workA[ 480 - i ] += a;
				workY[ 480 - i ] += workA[ 480 - i ];
			}
			// 奇数の时
			//if( i & 1 ){ 
			//	workX[ i ] += workA[ i ];
			//	workX[ 480 - i ] += workA[ 480 - i ];
			//}else{ 
			//	workX[ i ] -= workA[ i ];
			//	workX[ 478 - i ] -= workA[ 478 - i ];
			//}
		}
		// 演出时间调整
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt += PRODUCE_UNERI_SPEED;
		if( cnt > 241 ) cnt = 241;
#else
		if( cnt < 241 ) cnt += PRODUCE_UNERI_SPEED;
#endif
		//if( cnt < 241 ) cnt += 1;
		// 終了条件
		if( workY[ 241 ] < -640 ) endFlag = TRUE;
		if( workY[ 241 ] > 640 ) endFlag = TRUE;
		
		break;
		
	case 8:	// ラインはがれＯＵＴ
	
		if( endFlag ==  -1 ){
			int i;
			// うねり用Ｘ座标
			for( i = 0 ; i < 480 ; i++ ){
				workY[ i ] = ( float )i;
				//workX[ i ] = 0;
				workA[ i ] = 0;
			}
#ifdef PUK2
			if (ac!=0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		}
		// 移动处理
		for( i = 0 ; i < cnt; i++ ){
			workA[ i ] += a;
			if( workA[ i ] < PRODUCE_HAGARE_LINE_SPEED ) workY[ i ] += workA[ i ];
			else workY[ i ] += PRODUCE_HAGARE_LINE_SPEED;
		}
		// 演出时间调整
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt += 16;
		if( cnt > 480 ) cnt = 480;
#else
		if( cnt < 480 ) cnt += 16;
		//if( cnt < 479 ) cnt += PRODUCE_UNERI_SPEED * 2;
		if( cnt > 480 ) cnt = 480;
#endif
		// 終了条件
		if( workY[ 479 ] > 600 ) endFlag = TRUE;
		
		break;
		
	}
	
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	
	
	// 左右分裂の时
	if( mode == 4 ){
		int i;
		// 高さ分ループ
		for( i = 0 ; i < 480 ; i++ ){
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			// 奇数の时
			if( i & 1 ){
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( (int)x, i, &rect, lpBattleSurface );
			}else{
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( (int)x2, i, &rect, lpBattleSurface );
			}
		}
	}	
	// 上下分裂の时
	else if( mode == 5 ){
		int i;
		// 高さ分ループ
		for( i = 0 ; i < 480 ; i++ ){
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			
			// 奇数の时
			if( i & 1 ){
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( 0, i + (int)y, &rect, lpBattleSurface );
			}else{
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( 0, i + (int)y2, &rect, lpBattleSurface );
			}
		}
	}
	// うねり分裂の时
	else if( mode == 6 ){
		int i;
		//float workX = x, workX2 = x2, workA = a;
		// 高さ分ループ
		for( i = 0 ; i < 480 ; i++ ){
		
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			
			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2( (int)workX[ i ], i, &rect, lpBattleSurface );
		
		}
	}
	// 上下ライン加速移动
	else if( mode == 7 || mode == 8 ){
		int i;
		//float workX = x, workX2 = x2, workA = a;
		// 高さ分ループ
		//for( i = 0 ; i < 480 ; i++ ){
		for( i = 479 ; i > 0 ; i-- ){
		
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			
			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2( 0, ( int )workY[ i ], &rect, lpBattleSurface );
		
		}
	}else{
		// バックバッファーに高速転送（矩形指定）
		DrawSurfaceFast2( (int)x, (int)y, &rect, lpBattleSurface );
	}
	
	
	// バトルサーフェスの画像作成
	//CopyBackBuffer();
	if( endFlag == TRUE ){
		
		// 初期化
		x = 0;
		x2 = 0;
		y = 0; 
		y2 = 0; 
		a = 0;
		
		// ゲームの状态变更
		if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
		else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 

		// バトルサーフェスを黒でクリア
		//ClearSurface( lpBattleSurface );
		// バックサーフェスを黒でクリアー
		//ClearBackSurface();	
		return TRUE;
	}
	return FALSE;
}	
	
// 减速移动 *******************************************************************/
#ifdef PUK2
BOOL ProduceBrake( int mode, char ad, float ac )
#else
BOOL ProduceBrake( int mode )
#endif
{
	RECT rect = { 0, 0, DEF_APPSIZEX, DEF_APPSIZEY };
	int i;
	static int endFlag = -1;
	static float x = 0,x2 = 0, y = 0, y2 = 0; 
	static float a;
	static int cnt;
	
	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		endFlag = -1;
	}
	
	// 移动方向で分岐
	switch( mode ){
	
	case 0:	// 上减速移动
		if( endFlag == -1 ){
			x = 0;
			//y = 480;
			y = 513.2F;
			a = 17.4F;
			endFlag = FALSE;
		}
		y -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac!=0) a -= ac;
		else a -= 0.3F;
#else
		a -= 0.3F;
#endif
		// リミットチェック
		if( a <= 0 ) endFlag = TRUE;
		break;
		
	case 1:	// 下减速移动
		if( endFlag == -1 ){
			x = 0;
			//y = -480;
			y = -513.2F;
			a = 17.4F;
			endFlag = FALSE;
		}
		y += a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac!=0) a -= ac;
		else a -= 0.3F;
#else
		a -= 0.3F; 
#endif
		// リミットチェック
		if( a <= 0 ) endFlag = TRUE;
		break;
		
	case 2:	// 左减速移动
		if( endFlag == -1 ){
			//x = 640;
			x = 661;
			y = 0;
			a = 22.8F;
			endFlag = FALSE;
		}
		x -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac!=0) a -= ac;
		else a -= 0.4F;
#else
		a -= 0.4F; 
#endif
		// リミットチェック
		if( a <= 0 ) endFlag = TRUE;
		break;
		
	case 3:	// 右减速移动
		if( endFlag == -1 ){
			//x = -640;
			x = -661;
			y = 0;
			a = 22.8F;
			endFlag = FALSE;
		}
		x += a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac!=0) a -= ac;
		else a -= 0.4F;
#else
		a -= 0.4F; 
#endif
		// リミットチェック
		if( a <= 0 ) endFlag = TRUE;
		break;
		
	case 4:	// 左右减速移动
		if( endFlag == -1 ){
			//x = -640;
			x = -661;
			x2 = 661;
			y = 0;
			a = 22.8F;
			endFlag = FALSE;
		}
		x += a;		// 移动
		x2 -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac!=0) a -= ac;
		else a -= 0.4F;
#else
		a -= 0.4F; 
#endif
		// リミットチェック
		if( a <= 0 ) endFlag = TRUE;
		break;
		
	case 5:	// 下减速移动
		if( endFlag == -1 ){
			x = 0;
			//y = -480;
			y = -513.2F;
			y2 = 513.2F;
			a = 17.4F;
			endFlag = FALSE;
		}
		y += a;	// 移动
		y2 -= a;	// 移动
		// 减速度プラス
#ifdef PUK2
		if (ac!=0) a -= ac;
		else a -= 0.3F;
#else
		a -= 0.3F; 
#endif
		// リミットチェック
		if( a <= 0 ) endFlag = TRUE;
		break;
		
	case 6:	// 左右うねり减速移动
		if( endFlag ==  -1 ){
			int i;
			// うねり用Ｘ座标
			for( i = 0 ; i < 480 ; i++ ){
				workX[ i ] = 0;
				workA[ i ] = 0;
			}
#ifdef PUK2
			if (ac!=0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
			
			while( 1 ){
				for( i = 0 ; i < cnt ; i++ ){
					// 奇数の时
					if( i & 1 ){ 
						workA[ i ] += a;
						workX[ i ] += workA[ i ];
						
						workA[ 480 - i ] += a;
						workX[ 480 - i ] += workA[ 480 - i ];
					}else{ 
						workA[ i ] -= a;
						workX[ i ] += workA[ i ];
						
						workA[ 478 - i ] -= a;
						workX[ 478 - i ] += workA[ 478 - i ];
					}
				}
				// 演出时间调整
#ifdef PUK2
				if (ad) cnt += ad;
				else cnt += PRODUCE_UNERI_SPEED;
				if( cnt > 240 ) cnt = 240;
#else
				if( cnt < 240 ) cnt += PRODUCE_UNERI_SPEED;
#endif
				// 終了条件
				if( workX[ 241 ] < -640 ) break;
				if( workX[ 241 ] > 640 ) break;
			}
		}
		// 移动处理
		for( i = 0 ; i < 240 ; i++ ){
			// 奇数の时
			if( i & 1 ){ 
				if( workX[ i ] <= 1 ) workX[ i ] = 0;
				else{	
					workX[ i ] -= workA[ i ];
					workA[ i ] -= a;
				}
				
				if( workX[ 480 - i ] <= 1 ) workX[ 480 - i ] = 0;
				else{
					workX[ 480 - i ] -= workA[ 480 - i ];
					workA[ 480 - i ] -= a;
				}
			}else{ 
				if( workX[ i ] >= -1 ) workX[ i ] = 0;
				else{
					workX[ i ] -= workA[ i ];
					workA[ i ] += a;
				}
				
				if( workX[ 478 - i ] >= -1 ) workX[ 478 - i ] = 0;
				else{
					workX[ 478 - i ] -= workA[ 478 - i ];
					workA[ 478 - i ] += a;
				}
			}
		}
		//
#ifdef PUK2
		if (ad) cnt += ad;
		else cnt++;
		if( cnt > 240 ) cnt = 240;
#else
		if( cnt < 240 ) cnt++;
#endif
		// 終了条件
		if( workX[ 0 ] == 0 ) endFlag = TRUE;
		
		break;
		
		
	case 7:	// 上下ライン减速移动
	
		if( endFlag ==  -1 ){
			int i;
			// うねり用Ｘ座标
			for( i = 0 ; i < 480 ; i++ ){
				workY[ i ] = ( float )i;
				workA[ i ] = 0;
			}
#ifdef PUK2
			if (ac!=0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		
			while( 1 ){
				// 移动处理
				for( i = 0 ; i < cnt; i++ ){
					if( i <= 240 ){
						workA[ i ] -= a;
						workY[ i ] += workA[ i ];
					}
					if( 480 - i > 240 ){
						workA[ 480 - i ] += a;
						workY[ 480 - i ] += workA[ 480 - i ];
					}
				}
				// 演出时间调整
#ifdef PUK2
				if ( cnt < 241 ){
					if (ad) cnt += ad;
					else cnt += PRODUCE_UNERI_SPEED;
				}
#else
				if( cnt < 241 ) cnt += PRODUCE_UNERI_SPEED;
				//if( cnt < 241 ) cnt += 1;
#endif
				// 終了条件
				if( workY[ 241 ] < -640 ) break;
				if( workY[ 241 ] > 640 ) break;
			}
		}
		
		// 移动处理
		for( i = 0 ; i < cnt; i++ ){
			if( i <= 240 ){
				workY[ i ] -= workA[ i ];
				workA[ i ] += a;
			}
			if( 480 - i > 240 ){
				workY[ 480 - i ] -= workA[ 480 - i ];
				workA[ 480 - i ] -= a;
			}
		}
		if( workY[ 240 ] >= 240 ){
			// 演出时间调整
#ifdef PUK2
			if (ad) cnt -= ad;
			else cnt -= PRODUCE_UNERI_SPEED;
#else
			if( cnt > 0 ) cnt -= PRODUCE_UNERI_SPEED;
#endif
			if( cnt < 0 ) cnt = 0;
		}
		// 終了条件
		if( workY[ 0 ] >= 0 ) endFlag = TRUE;
		//if( workY[ 241 ] > 640 ) endFlag = TRUE;
		
		break;
		
	case 8:	// ラインはがれＩＮ
	
		if( endFlag ==  -1 ){
			int i;
			// うねり用Ｘ座标
			for( i = 0 ; i < 480 ; i++ ){
				workY[ i ] = ( float )i;
				workA[ i ] = 0;
			}
#ifdef PUK2
			if (ac!=0) a = ac;
			else a = 1.0F;
#else
			a = 1.0F;
#endif
			cnt = 0;
			endFlag = FALSE;
		
			while( 1 ){
				// 移动处理
				for( i = 479 ; i >= 479 - cnt; i-- ){
#ifdef PUK3_PRODUCE_MEMDESTORY
					if ( i < 0 ) continue;
#endif
					workA[ i ] -= a;
					if( workA[ i ] > -PRODUCE_HAGARE_LINE_SPEED ) workY[ i ] += workA[ i ];
					else workY[ i ] -= PRODUCE_HAGARE_LINE_SPEED;
				}
				// 演出时间调整
#ifdef PUK2
				// 演出时间调整
				if (ad) cnt += ad;
				else cnt += 16;
				if( cnt > 480 ) cnt = 480;
#else
				if( cnt < 480 ) cnt += 16;
				//if( cnt < 479 ) cnt += PRODUCE_UNERI_SPEED * 2;
				if( cnt > 480 ) cnt = 480;
#endif
				// 終了条件
				if( workY[ 0 ] < 0 ) break;
			}
		}
		
		// 移动处理
		for( i = 479 ; i >= 479 - cnt; i-- ){
#ifdef PUK3_PRODUCE_MEMDESTORY
			if ( i < 0 ) continue;
#endif
			if( workA[ i ] > -PRODUCE_HAGARE_LINE_SPEED ) workY[ i ] -= workA[ i ];
			else workY[ i ] += PRODUCE_HAGARE_LINE_SPEED;
			workA[ i ] += a;
		}
		if( workY[ 0 ] >= 0 ){ 
			// 演出时间调整
#ifdef PUK2
			if (ad) cnt -= ad;
			else cnt -= 16;
#else
			if( cnt > 0 ) cnt -= 16;
#endif
			if( cnt < 0 ) cnt = 0;
		}
		// 終了条件
		if( workY[ 479 ] >= 479 ) endFlag = TRUE;
		//if( workY[ 241 ] > 640 ) endFlag = TRUE;
		
		break;
		
	}
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	
	// 左右分裂の时
	if( mode == 4 ){
		int i;
		// 高さ分ループ
		for( i = 0 ; i < 480 ; i++ ){
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			// 奇数の时
			if( i & 1 ){
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( (int)x, i, &rect, lpBattleSurface );
			}else{
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( (int)x2, i, &rect, lpBattleSurface );
			}
		}
	}	
	// 左右分裂の时
	else if( mode == 5 ){
		int i;
		// 高さ分ループ
		for( i = 0 ; i < 480 ; i++ ){
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			// 奇数の时
			if( i & 1 ){
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( 0, i + (int)y, &rect, lpBattleSurface );
			}else{
				// バックバッファーに高速転送（矩形指定）
				DrawSurfaceFast2( 0, i + (int)y2, &rect, lpBattleSurface );
			}
		}
	}
	// うねり分裂の时
	else if( mode == 6 ){
		int i;
		for( i = 0 ; i < 480 ; i++ ){
		
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			
			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2( (int)workX[ i ], i, &rect, lpBattleSurface );
		
		}
	}
	// 上下ライン加速移动
	else if( mode == 7 || mode == 8 ){
		int i;
		//float workX = x, workX2 = x2, workA = a;
		// 高さ分ループ
		for( i = 0 ; i < 480 ; i++ ){
		//for( i = 479 ; i > 0 ; i-- ){
		
			rect.top = i;
			rect.bottom = i + 1;
			rect.left = 0;
			rect.right = DEF_APPSIZEX;
			
			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2( 0, (int)workY[ i ], &rect, lpBattleSurface );
		
		}
	}else{
		// バックバッファーに高速転送（矩形指定）
		DrawSurfaceFast2( (int)x, (int)y, &rect, lpBattleSurface );
	}
	
	// バトルサーフェスの画像作成
	//CopyBackBuffer();
	if( endFlag == TRUE ){
		// 初期化
		
		// ゲームの状态变更
		if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
		else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
		// フラグ初期化
		endFlag = -1;
		// バトルサーフェスを黒でクリア
		//ClearSurface( lpBattleSurface );
		// バックサーフェスを黒でクリアー
		//ClearBackSurface();	
		return TRUE;
	}
	return FALSE;
}
#endif

// ４方向移动 ******************************************************************/
#ifdef PUK2
BOOL Produce4Way( int mode, char ad, float )
#else
BOOL Produce4Way( int mode )
#endif
{
	RECT rect[ 4 ] = {	{ 0, 0, 320, 240 }, { 320, 0, 640, 240 },
						{ 0, 240, 320, 480 }, { 320, 240, 640, 480 }};
	static int endFlag = -1;
	static int x[ 4 ], y[ 4 ]; 
#ifdef PUK2
	int d = (ad?ad:4),i;
#else
	int d = 4,i;
#endif
	
	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		endFlag = -1;
	}
	
	// 移动方向で分岐
	switch( mode ){
	
	case 0:	// 画面外へ
		if( endFlag == -1 ){
			x[ 0 ] = 0;
			y[ 0 ] = 0;
			x[ 1 ] = 320;
			y[ 1 ] = 0;
			x[ 2 ] = 0;
			y[ 2 ] = 240;
			x[ 3 ] = 320;
			y[ 3 ] = 240;
			endFlag = FALSE;
		}
		// 移动
		x[ 0 ] -= d;
		y[ 0 ] -= d;
		x[ 1 ] += d;
		y[ 1 ] -= d;
		x[ 2 ] -= d;
		y[ 2 ] += d;
		x[ 3 ] += d;
		y[ 3 ] += d;
		// リミットチェック
		if( x[ 0 ] <= -320 ) endFlag = TRUE;
		break;
		
	case 1:	// 画面内へ
		if( endFlag == -1 ){
			x[ 0 ] = -280;
			y[ 0 ] = -280;
			x[ 1 ] = 320 + 280;
			y[ 1 ] = -280;
			x[ 2 ] = -280;
			y[ 2 ] = 240 + 280;
			x[ 3 ] = 320 + 280;
			y[ 3 ] = 240 + 280;
			endFlag = FALSE;
		}
		// 移动
		x[ 0 ] += d;
		y[ 0 ] += d;
		x[ 1 ] -= d;
		y[ 1 ] += d;
		x[ 2 ] += d;
		y[ 2 ] -= d;
		x[ 3 ] -= d;
		y[ 3 ] -= d;
#ifdef PUK2
		if (x[0]>0) x[0]=0;
		if (y[0]>0) y[0]=0;
		if (x[1]<320) x[1]=0;
		if (y[1]>0) y[1]=0;
		if (x[2]>0) x[2]=0;
		if (y[2]<240) y[2]=0;
		if (x[3]<320) x[3]=0;
		if (y[3]<240) y[3]=0;
#endif
		// リミットチェック
		if( x[ 0 ] >= 0 ) endFlag = TRUE;
		break;
	}
	
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	
	for( i = 0 ; i < 4 ; i++ ){
		// バックバッファーに高速転送（矩形指定）
		DrawSurfaceFast2( x[ i ], y[ i ], &rect[ i ], lpBattleSurface );
	}
	// バトルサーフェスの画像作成
	//CopyBackBuffer();
	if( endFlag == TRUE ){
		// 初期化
		
		// ゲームの状态变更
		if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
		else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
		// フラグ初期化
		endFlag = -1;
		// バトルサーフェスを黒でクリア
		//ClearSurface( lpBattleSurface );
		// バックサーフェスを黒でクリアー
		//ClearBackSurface();	
		return TRUE;
	}
	return FALSE;
}	

// はがれる ****************************************************************/
#ifdef PUK2
BOOL ProduceHagare( int mode, char ad, float ac )
#else
BOOL ProduceHagare( int mode )
#endif
{
	RECT rect;
	static int endFlag = -1;
	static int x[ 64 ], y[ 64 ], a[ 64 ], cnt,cnt2; 
	static int posX[ 64 ], posY[ 64 ]; 
	static BOOL flag[ 64 ]; 
	int i, j, nowX = 0, nowY = 0;
	int rnd = Rnd( 0, 63 );
	int no = 0;
#ifdef PUK2
	int acc=(int)ac;

	if (acc<1) acc=1;
	if (!ad) ad=1;
#endif
	
	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		endFlag = -1;
	}
	
	// 移动方向で分岐
	switch( mode ){
	
	case 0:	// 消える
		// 初期化
		if( endFlag == -1 ){
			for( i = 0 ; i < 8 ; i++ ){
				for( j = 0 ; j < 8 ; j++ ){
					x[ no ] = nowX;
					y[ no ] = nowY;
					posX[ no ] = nowX;
					posY[ no ] = nowY;
					a[ no ] = 0;
					flag[ no++ ] = TRUE;
					nowX += 80;
				}
				nowY += 60;
				nowX = 0;
			}
			endFlag = FALSE;
			cnt = 0;
		}
#ifdef PUK2
		for(i=0;i<ad;i++){
			while( flag[ rnd ] == FALSE ){ rnd = Rnd( 0, 63 ); }
			// ランダム消し
			flag[ rnd ] = FALSE;
			cnt++;
			// リミットチェック
			if( cnt >= 64 ){ endFlag = TRUE;	break; }
		}
#else
		while( flag[ rnd ] == FALSE ){
			rnd = Rnd( 0, 63 );
		}
		// ランダム消し
		flag[ rnd ] = FALSE;
		cnt++;
		// リミットチェック
		if( cnt >= 64 ) endFlag = TRUE;
#endif
		break;
		
	case 1:	// 出现する
		// 初期化
		if( endFlag == -1 ){
			for( i = 0 ; i < 8 ; i++ ){
				for( j = 0 ; j < 8 ; j++ ){
					x[ no ] = nowX;
					y[ no ] = nowY;
					posX[ no ] = nowX;
					posY[ no ] = nowY;
					a[ no ] = 0;
					flag[ no++ ] = FALSE;
					nowX += 80;
				}
				nowY += 60;
				nowX = 0;
			}
			endFlag = FALSE;
			cnt = 0;
		}
#ifdef PUK2
		for(i=0;i<ad;i++){
			while( flag[ rnd ] == TRUE ){ rnd = Rnd( 0, 63 ); }
			// ランダム出现
			flag[ rnd ] = TRUE;
			cnt++;
			// リミットチェック
			if( cnt >= 64 ){ endFlag = TRUE;	break; }
		}
#else
		while( flag[ rnd ] == TRUE ){
			rnd = Rnd( 0, 63 );
		}
		// ランダム出现
		flag[ rnd ] = TRUE;
		cnt++;
		// リミットチェック
		if( cnt >= 64 ) endFlag = TRUE;
#endif
		break;
		
	case 2:	// 消える（落ちる）
		// 初期化
		if( endFlag == -1 ){
			for( i = 0 ; i < 8 ; i++ ){
				for( j = 0 ; j < 8 ; j++ ){
					x[ no ] = nowX;
					y[ no ] = nowY;
					posX[ no ] = nowX;
					posY[ no ] = nowY;
					a[ no ] = 0;
					flag[ no++ ] = 2;
					nowX += 80;
				}
				nowY += 60;
				nowX = 0;
			}
			endFlag = FALSE;
			cnt = 0;
			cnt2 = 0;
		}
		// 落とすやつランダム
#ifdef PUK2
		for(i=0;i<ad;i++){
			if( cnt2 < 64 ){
				while( flag[ rnd ] <= 1 ){ rnd = Rnd( 0, 63 ); }
				// ランダム落とし
				flag[ rnd ] = 1;
				cnt2++;
			}else break;
		}
#else
		if( cnt2 < 64 ){
			while( flag[ rnd ] <= 1 ){
				rnd = Rnd( 0, 63 );
			}
			// ランダム落とし
			flag[ rnd ] = 1;
			cnt2++;
		}
#endif
		// 移动
		for( i = 0 ; i < 64 ; i++ ){
			if( flag[ i ] == 1 ){
#ifdef PUK2
				a[ i ]+=acc;
#else
				a[ i ]++;
#endif
				y[ i ] += a[ i ];
				if( y[ i ] >= 500 ){ 
					flag[ i ] = 0;
					cnt++;
				}
			}
		}
		// リミットチェック
		if( cnt >= 64 ) endFlag = TRUE;
		break;
		
	case 3:	// 出现（落ちる）
		// 初期化
		if( endFlag == -1 ){
			for( i = 0 ; i < 8 ; i++ ){
				for( j = 0 ; j < 8 ; j++ ){
					x[ no ] = nowX;
					y[ no ] = -60;
					posX[ no ] = nowX;
					posY[ no ] = nowY;
					a[ no ] = 0;
					flag[ no++ ] = FALSE;
					nowX += 80;
				}
				nowY += 60;
				nowX = 0;
			}
			endFlag = FALSE;
			cnt = 0;
			cnt2 = 0;
		}
		// 落とすやつランダム
#ifdef PUK2
		for(i=0;i<ad;i++){
			if( cnt2 < 64 ){
				while( flag[ rnd ] >= 1 ){ rnd = Rnd( 0, 63 ); }
				// ランダム落とし
				flag[ rnd ] = 1;
				cnt2++;
			}else break;
		}
#else
		if( cnt2 < 64 ){
			while( flag[ rnd ] >= 1 ){
				rnd = Rnd( 0, 63 );
			}
			// ランダム落とし
			flag[ rnd ] = 1;
			cnt2++;
		}
#endif
		// 移动
		for( i = 0 ; i < 64 ; i++ ){
			if( flag[ i ] == 1 ){
#ifdef PUK2
				a[ i ]+=acc;
#else
				a[ i ]++;
#endif
				y[ i ] += a[ i ];
				if( y[ i ] >= posY[ i ] ){ 
					y[ i ] = posY[ i ];
					flag[ i ] = 2;
					cnt++;
				}
			}
		}
		// リミットチェック
		if( cnt >= 64 ) endFlag = TRUE;
		break;
		
	}
	
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	
	for( i = 0 ; i < 64 ; i++ ){
		if( flag[ i ] == 2 ){
			rect.left = posX[ i ];
			rect.top = posY[ i ];
			rect.right = posX[ i ] + 80;
			rect.bottom = posY[ i ] + 60;
			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2( x[ i ], y[ i ], &rect, lpBattleSurface );
		}
	}
	for( i = 0 ; i < 64 ; i++ ){
		if( flag[ i ] == 1 ){
			rect.left = posX[ i ];
			rect.top = posY[ i ];
			rect.right = posX[ i ] + 80;
			rect.bottom = posY[ i ] + 60;
			// バックバッファーに高速転送（矩形指定）
			DrawSurfaceFast2( x[ i ], y[ i ], &rect, lpBattleSurface );
		}
	}
	// バトルサーフェスの画像作成
	//CopyBackBuffer();
	if( endFlag == TRUE ){
		// 初期化
		
		// ゲームの状态变更
		if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
		else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
		// フラグ初期化
		endFlag = -1;
		// バトルサーフェスを黒でクリア
		//ClearSurface( lpBattleSurface );
		// バックサーフェスを黒でクリアー
		//ClearBackSurface();	
		return TRUE;
	}
	return FALSE;
}
	

#define BRAN_SIZE_X 20
#define BRAN_SIZE_Y 16
static int bran_cnt;
static int pos_tbl[(480/BRAN_SIZE_Y)*(640/BRAN_SIZE_X)*2];
static int bran_flg = 0;
//static int bran_flg = 0;
// フスマ ******************************************************************/
#ifdef PUK2
BOOL Produce_bran_small( int ang, char, float )
#else
BOOL Produce_bran_small(int ang)
#endif
{
	RECT rect;
	int d0,d6,d7;
	int *a0;
	
	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		bran_flg = 0;
	}
	
	if(bran_flg == 0){		//最初なら
		bran_flg = 1;		//テーブル作成
		d0 = 0;
		for(d7=-240 + BRAN_SIZE_Y/2; d7<240 + BRAN_SIZE_Y/2; d7+=BRAN_SIZE_Y){
			for(d6=-320 + BRAN_SIZE_X/2; d6<320 + BRAN_SIZE_X/2; d6+=BRAN_SIZE_X){
				pos_tbl[d0++] = d6;
				pos_tbl[d0++] = d7;
			}
		}
		if(ang < 0){
			bran_cnt = 64;
		} else {
			bran_cnt = 0;
		}
	}
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	

	a0 = pos_tbl + (480/BRAN_SIZE_Y)*(640/BRAN_SIZE_X)*2 - 2;
	for(d7=480-BRAN_SIZE_Y; d7>=0; d7-=BRAN_SIZE_Y){
		for(d6=640-BRAN_SIZE_X; d6>=0; d6-=BRAN_SIZE_X){
			rect.left = d6;
			rect.right = (d6+BRAN_SIZE_X);
			rect.top = d7;
			rect.bottom = (d7+BRAN_SIZE_Y);
#if 0
			DrawSurfaceFast2( *a0*bran_cnt/64+320, *(a0+1)*bran_cnt/64+240, &rect, lpBattleSurface );
#else
			lpDraw->lpBACKBUFFER->BltFast( *a0*bran_cnt/64+320 - BRAN_SIZE_X/2, *(a0+1)*bran_cnt/64+240 - BRAN_SIZE_Y/2, lpBattleSurface, &rect, DDBLTFAST_WAIT );
//			lpDraw->lpBACKBUFFER->BltFast( *(a0++)*bran_cnt/64+320, *(a0++)*bran_cnt/64+240, lpBattleSurface, &rect, DDBLTFAST_WAIT );
#endif
			a0 -= 2;
		}
	}

	bran_cnt += ang;
	if(ang < 0){
//		if(!bran_cnt){
		if(bran_cnt < 0){
			// ゲームの状态变更
			if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
			else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
			bran_flg = 0;
			return TRUE;
		}
	} else {
//		if(bran_cnt == 64){
		if(bran_cnt > 64){
			// ゲームの状态变更
			if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
			else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
			bran_flg = 0;
			return TRUE;
		}
	}
	return FALSE;
}

#define BREAK_UP 8
//static int break_up_cnt = 480;
// 折りたたみ上 ******************************************************************/
#ifdef PUK2
BOOL Produce_break_up( int ang, char ad, float )
#else
BOOL Produce_break_up(int ang)
#endif
{
	RECT rect = { 0, 0, 640, 0 };
	int d7;

	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		bran_flg = 0;
	}
	
	if(bran_flg == 0){		//最初なら
		bran_flg = 1;		//变数初期化
		if(ang < 0){
			bran_cnt = 480;
		} else {
			bran_cnt = 0;
		}
	}
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	

	for(d7=0; d7<bran_cnt; d7++){
		rect.top = d7*480/bran_cnt;
		rect.bottom = rect.top+1;
		lpDraw->lpBACKBUFFER->BltFast( 0, d7, lpBattleSurface, &rect, DDBLTFAST_WAIT );
	}

	if(ang < 0){
#ifdef PUK2
		if (ad) bran_cnt -= ad;
		else bran_cnt -= BREAK_UP;
#else
		bran_cnt -= BREAK_UP;
#endif
//		if(bran_cnt == 0){
		if(bran_cnt < 0){
			// ゲームの状态变更
			if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
			else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
			bran_flg = 0;		//变数初期化
			return TRUE;
		}
	} else {
#ifdef PUK2
		if (ad) bran_cnt += ad;
		else bran_cnt += BREAK_UP;
#else
		bran_cnt += BREAK_UP;
#endif
//		if(bran_cnt == 480){
		if(bran_cnt > 480){
			// ゲームの状态变更
			if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
			else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
			bran_flg = 0;		//变数初期化
			return TRUE;
		}
	}
	return FALSE;
}

// 折りたたみ下 ******************************************************************/
#ifdef PUK2
BOOL Produce_break_up2( int ang, char ad, float )
#else
BOOL Produce_break_up2(int ang)
#endif
{
	RECT rect = { 0, 0, 640, 0 };
	int d7;

	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		bran_flg = 0;
	}
	
	if(bran_flg == 0){		//最初なら
		bran_flg = 1;		//变数初期化
		if(ang < 0){
			bran_cnt = 480;
		} else {
			bran_cnt = 0;
		}
	}
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	

	for(d7=0; d7<bran_cnt; d7++){
		rect.top = 480-d7*480/bran_cnt;
		rect.bottom = rect.top+1;
		lpDraw->lpBACKBUFFER->BltFast( 0, 480-d7, lpBattleSurface, &rect, DDBLTFAST_WAIT );
	}

	if(ang < 0){
#ifdef PUK2
		if (ad) bran_cnt -= ad;
		else bran_cnt -= BREAK_UP;
#else
		bran_cnt -= BREAK_UP;
#endif
//		if(bran_cnt == 0){
		if(bran_cnt < 0){
			// ゲームの状态变更
			if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
			else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
			bran_flg = 0;		//变数初期化
			return TRUE;
		}
	} else {
#ifdef PUK2
		if (ad) bran_cnt += ad;
		else bran_cnt += BREAK_UP;
#else
		bran_cnt += BREAK_UP;
#endif
//		if(bran_cnt == 480){
		if(bran_cnt > 480){
			// ゲームの状态变更
			if( GameState == GAME_FIELD_TO_ENCOUNT ) GameState = GAME_ENCOUNT_TO_BATTLE; 
			else if( GameState == GAME_ENCOUNT_TO_BATTLE ) GameState = GAME_BATTLE; 
			bran_flg = 0;		//变数初期化
			return TRUE;
		}
	}
	return FALSE;
}



// 中央压缩 ******************************************************************/
#ifdef PUK2
BOOL ProduceCenterPress( int mode, char ad, float )
#else
BOOL ProduceCenterPress( int mode )
#endif
{
	static int line;
	RECT rect1, rect2;
	int i;
	int h = 240;
#ifdef PUK2
	int spd = (ad?ad:8);
#else
	int spd = 8;
#endif

	// 初期化
	if( ProduceInitFlag == TRUE )
	{
		ProduceInitFlag = FALSE;
		if( mode == 0 )
		{
			line = 240;
		}
		else
		{
			line = 0;
		}
	}


	// バックサーフェスを黒でクリアー
	ClearBackSurface();	

	rect1.left  = 0;
	rect1.right = DEF_APPSIZEX;
	rect2.left  = 0;
	rect2.right = DEF_APPSIZEX;
	for( i = 0; i < line; i++ )
	{
		rect1.top = h-i*h/line;
		rect1.bottom = rect1.top + 1;
		lpDraw->lpBACKBUFFER->BltFast( 0, h-i, lpBattleSurface, &rect1, DDBLTFAST_WAIT );

		rect2.top = i*h/line+h;
		rect2.bottom = rect2.top + 1;
		lpDraw->lpBACKBUFFER->BltFast( 0, i+h, lpBattleSurface, &rect2, DDBLTFAST_WAIT );
	}

	if( mode == 0 )
	{
		line -= spd;
		if( line < 0 )
		{
			return TRUE;
		}
	}
	else
	{
		line += spd;
		if( line > h )
		{
			return TRUE;
		}
	}

	return FALSE;
}



// 演出描画 ********************************************************************/
#ifdef PUK2
BOOL DrawProduce( int no, char ad, float ac )
#else
BOOL DrawProduce( int no )
#endif
{
#ifdef _DEBUG		
	// ポーズ
	if( VK[ VK_UP ] & KEY_ON ){ 
		// 时间の遅れ忘れさせる
		NowTime = GetTickCount();
		return FALSE;
	}
#endif
	
	// 演出番号で分岐
#ifdef PUK2
	switch( no ){
	// 太田プロデュース
	// 加速移动
	case PRODUCE_UP_ACCELE:		// 上加速移动
		return ProduceAccele( 0, ad, ac );
	case PRODUCE_DOWN_ACCELE:	// 下加速移动
		return ProduceAccele( 1, ad, ac );
	case PRODUCE_LEFT_ACCELE:	// 左加速移动
		return ProduceAccele( 2, ad, ac );
	case PRODUCE_RIGHT_ACCELE:	// 右加速移动
		return ProduceAccele( 3, ad, ac );
	case PRODUCE_LEFT_RIGHT_ACCELE:	// 左右加速移动
		return ProduceAccele( 4, ad, ac );
	case PRODUCE_UP_DOWM_ACCELE:	// 上下加速移动
		return ProduceAccele( 5, ad, ac );
	case PRODUCE_UNERI_ACCELE:		// うねり加速移动
		return ProduceAccele( 6, ad, ac );
	case PRODUCE_UP_DOWN_LINE_ACCELE:		// 上下ライン加速移动
		return ProduceAccele( 7, ad, ac );
	case PRODUCE_LINE_HAGARE_OCHI_OUT:		// ラインはがれ落ちＯＵＴ
		return ProduceAccele( 8, ad, ac );

	// 减速移动
	case PRODUCE_UP_BRAKE:		// 上减速移动
		return ProduceBrake( 0, ad, ac );
	case PRODUCE_DOWN_BRAKE:	// 下减速移动
		return ProduceBrake( 1, ad, ac );
	case PRODUCE_LEFT_BRAKE:	// 左减速移动
		return ProduceBrake( 2, ad, ac );
	case PRODUCE_RIGHT_BRAKE:	// 右减速移动
		return ProduceBrake( 3, ad, ac );
	case PRODUCE_LEFT_RIGHT_BRAKE:	// 左右减速移动
		return ProduceBrake( 4, ad, ac );
	case PRODUCE_UP_DOWM_BRAKE:	// // 上下减速移动
		return ProduceBrake( 5, ad, ac );
	case PRODUCE_UNERI_BRAKE:		// うねり减速移动
		return ProduceBrake( 6, ad, ac );
	case PRODUCE_UP_DOWN_LINE_BRAKE:	// 上下ライン减速移动
		return ProduceBrake( 7, ad, ac );
	case PRODUCE_LINE_HAGARE_OCHI_IN:		// ラインはがれ落ちＩＮ
		return ProduceBrake( 8, ad, ac );

	case PRODUCE_4WAY_OUT:		// 四方向移动画面外へ
		return Produce4Way( 0, ad, 0 );
	case PRODUCE_4WAY_IN:		// 四方向移动画面内へ
		return Produce4Way( 1, ad, 0 );

	case PRODUCE_HAGARE_OUT:	// はがれ处理（消える）
		return ProduceHagare( 0, ad, 0 );
	case PRODUCE_HAGARE_IN:		// はがれ处理（出现する）
		return ProduceHagare( 1, ad, 0 );
	case PRODUCE_HAGARE_OCHI_OUT:	// はがれ落ち（消える）
		return ProduceHagare( 2, ad, ac );
	case PRODUCE_HAGARE_OCHI_IN:	// はがれ落ち（出现する）
		return ProduceHagare( 3, ad, ac );

	//ＯＦＴプロデューサー
	case PRODUCE_BRAN_SMALL:	// フスマ缩小
		return Produce_bran_small( (ad?ad:-1), 0, 0);
	case PRODUCE_BRAN_BIG:		// フスマ扩大
		return Produce_bran_small( (ad?ad:+1), 0, 0);

	case PRODUCE_BREAK_UP1:		//折りたたみ上アウト
		return Produce_break_up(-1, ad, 0);
	case PRODUCE_BREAK_UP2:		//折りたたみ上イン
		return Produce_break_up(1, ad, 0);
	case PRODUCE_BREAK_UP3:		//折りたたみ上アウト
		return Produce_break_up(-1, ad, 0);
	case PRODUCE_BREAK_UP4:		//折りたたみ下イン
		return Produce_break_up2(1, ad, 0);
	case PRODUCE_BREAK_UP5:		//折りたたみ下アウト
		return Produce_break_up2(-1, ad, 0);
	case PRODUCE_BREAK_UP6:		//折りたたみ下イン
		return Produce_break_up2(1, ad, 0);
	case PRODUCE_BREAK_UP7:		//折りたたみ下アウト
		return Produce_break_up2(-1, ad, 0);
	case PRODUCE_BREAK_UP8:		//折りたたみ上イン
		return Produce_break_up(1, ad, 0);

	// dwafプロデュース
	case PRODUCE_CENTER_PRESSIN:
		return ProduceCenterPress( 0, ad, 0 );
	case PRODUCE_CENTER_PRESSOUT:
		return ProduceCenterPress( 1, ad, 0 );
	}
#else
	switch( no ){
	
	// 太田プロデュース
	// 加速移动
	case PRODUCE_UP_ACCELE:		// 上加速移动
		return ProduceAccele( 0 );
		
	case PRODUCE_DOWN_ACCELE:	// 下加速移动
		return ProduceAccele( 1 );
		
	case PRODUCE_LEFT_ACCELE:	// 左加速移动
		return ProduceAccele( 2 );
		
	case PRODUCE_RIGHT_ACCELE:	// 右加速移动
		return ProduceAccele( 3 );
	
	case PRODUCE_LEFT_RIGHT_ACCELE:	// 左右加速移动
		return ProduceAccele( 4 );
		
	case PRODUCE_UP_DOWM_ACCELE:	// 上下加速移动
		return ProduceAccele( 5 );
		
	case PRODUCE_UNERI_ACCELE:		// うねり加速移动
		return ProduceAccele( 6 );
		
	case PRODUCE_UP_DOWN_LINE_ACCELE:		// 上下ライン加速移动
		return ProduceAccele( 7 );
		
	case PRODUCE_LINE_HAGARE_OCHI_OUT:		// ラインはがれ落ちＯＵＴ
		return ProduceAccele( 8 );
		
	// 减速移动
	case PRODUCE_UP_BRAKE:		// 上减速移动
		return ProduceBrake( 0 );
		
	case PRODUCE_DOWN_BRAKE:	// 下减速移动
		return ProduceBrake( 1 );
		
	case PRODUCE_LEFT_BRAKE:	// 左减速移动
		return ProduceBrake( 2 );
		
	case PRODUCE_RIGHT_BRAKE:	// 右减速移动
		return ProduceBrake( 3 );
		
	case PRODUCE_LEFT_RIGHT_BRAKE:	// 左右减速移动
		return ProduceBrake( 4 );
		
	case PRODUCE_UP_DOWM_BRAKE:	// // 上下减速移动
		return ProduceBrake( 5 );
		
	case PRODUCE_UNERI_BRAKE:		// うねり减速移动
		return ProduceBrake( 6 );
		
	case PRODUCE_UP_DOWN_LINE_BRAKE:	// 上下ライン减速移动
		return ProduceBrake( 7 );
		
	case PRODUCE_LINE_HAGARE_OCHI_IN:		// ラインはがれ落ちＩＮ
		return ProduceBrake( 8 );
		
	case PRODUCE_4WAY_OUT:		// 四方向移动画面外へ
		return Produce4Way( 0 );
		
	case PRODUCE_4WAY_IN:		// 四方向移动画面内へ
		return Produce4Way( 1 );
		
	case PRODUCE_HAGARE_OUT:	// はがれ处理（消える）
		return ProduceHagare( 0 );
		
	case PRODUCE_HAGARE_IN:		// はがれ处理（出现する）
		return ProduceHagare( 1 );
		
	case PRODUCE_HAGARE_OCHI_OUT:	// はがれ落ち（消える）
		return ProduceHagare( 2 );
		
	case PRODUCE_HAGARE_OCHI_IN:	// はがれ落ち（出现する）
		return ProduceHagare( 3 );
		
	//ＯＦＴプロデューサー
	case PRODUCE_BRAN_SMALL:	// フスマ缩小
		return Produce_bran_small(-1);

	case PRODUCE_BRAN_BIG:		// フスマ扩大
		return Produce_bran_small(1);

	case PRODUCE_BREAK_UP1:		//折りたたみ上アウト
		return Produce_break_up(-1);

	case PRODUCE_BREAK_UP2:		//折りたたみ上イン
		return Produce_break_up(1);

	case PRODUCE_BREAK_UP3:		//折りたたみ上アウト
		return Produce_break_up(-1);

	case PRODUCE_BREAK_UP4:		//折りたたみ下イン
		return Produce_break_up2(1);

	case PRODUCE_BREAK_UP5:		//折りたたみ下アウト
		return Produce_break_up2(-1);

	case PRODUCE_BREAK_UP6:		//折りたたみ下イン
		return Produce_break_up2(1);

	case PRODUCE_BREAK_UP7:		//折りたたみ下アウト
		return Produce_break_up2(-1);

	case PRODUCE_BREAK_UP8:		//折りたたみ上イン
		return Produce_break_up(1);

	// dwafプロデュース
	case PRODUCE_CENTER_PRESSIN:
		return ProduceCenterPress( 0 );
	case PRODUCE_CENTER_PRESSOUT:
		return ProduceCenterPress( 1 );

	}
#endif
	return TRUE;
}

// タイトル演出
void TitleProduce( void )
{
	static float x[ 10 ], y[ 10 ], a[ 10 ];
	static int flag = 0, cnt,cnt2, cnt3;
	static int fall[ 8 ];
	static int time;
	int i,bmpNo;
	
#ifdef _DEBUG		
	// ポーズ
	if( VK[ VK_UP ] & KEY_ON ){ 
		// 时间の遅れ忘れさせる
		NowTime = GetTickCount();
		return;
	}
#endif
	//if( time++ < 30 ) return;
	
	// 初期化する时
	if( ProduceInitFlag == TRUE ){
		ProduceInitFlag = FALSE;
		flag = 0;
	}
	
	// 初期化
	if( flag == 0 ){
		for( i = 0 ; i < 8 ; i++ ){
			x[ i ] = 320;
			y[ i ] = -100;
			a[ i ] = 1;
			fall[ i ] = 0;
			cnt = 0;
			cnt2 = 0;
			cnt3 = 0;
			time = 0;
			
		}
		// 社ロゴとドリーム
		x[ 9 ] = -319;
		y[ 9 ] = 410;
		a[ 9 ] = 25.5;
		x[ 8 ] = 959;
		y[ 8 ] = 444;
		a[ 8 ] = 25.5;
		
		// ＢＭＰをロードする
		for( bmpNo = CG_TITLE_NAME_S ; bmpNo <= CG_TITLE_DREAM_LOGO ; bmpNo++ ){
#ifdef PUK2
			LoadBmp2_PUK2_PAL( bmpNo, 1 );
#else
			LoadBmp2( bmpNo );
#endif
		}
		// 时间の遅れ忘れさせる
		NowTime = GetTickCount();
		
		flag = 1;
	}
	// 时间待ち
	if( flag == 1 ){
		//time++;
		//if( time >= 120 ) 
		flag = 2;
	}
	
	// 社ロゴ移动
	if( flag == 2 ){
		a[ 8 ] -= 0.5;
		x[ 8 ] -= a[ 8 ];
		//if( x[ 8 ] <= 320 ){
		if( a[ 8 ] == 0 ){
			x[ 8 ] = 320;
			flag = 3;
		}
		// ドリーム移动
		a[ 9 ] -= 0.5;
		x[ 9 ] += a[ 9 ];
		//if( x[ 9 ] >= 320 ){
		if( a[ 9 ] == 0 ){
			x[ 9 ] = 320;
			flag = 3;
		}
		
	}
	
	// 左から落とす
	if( cnt < 8 && flag == 3 ){
		if( cnt2 % 4 == 0 ){
			fall[ cnt ] = 1;
			cnt++;
		}
		cnt2++;
	}
	
	// 移动
	if( flag == 3 ){
		for( i = 0 ; i < 8 ; i++ ){
			if( fall[ i ] >= 1 && fall[ i ] <= 3 ){
				a[ i ] += 1.0;
				y[ i ] += a[ i ];
				if( y[ i ] >= 160 ){
					y[ i ] = 160;
					fall[ i ]++;
					if( fall[ i ] == 4 ){ 
						cnt3++;
						continue;
					}
					a[ i ] *= -0.5F;
					y[ i ] += a[ i ];
				}
			}
		}
	}
	
	// 社ロゴ??ドリーム表示
	StockDispBuffer( ( int )x[ 8 ], ( int )y[ 8 ], DISP_PRIO_CHAR, CG_TITLE_JSS_LOGO, 0 );
	StockDispBuffer( ( int )x[ 9 ], ( int )y[ 9 ], DISP_PRIO_CHAR, CG_TITLE_DREAM_LOGO, 0 );
	
	if( cnt3 < 8 ){
		// 表示
		for( i = 0 ; i < 8 ; i++ ){
			StockDispBuffer( ( int )x[ i ], ( int )y[ i ], DISP_PRIO_CHAR, CG_TITLE_NAME_S + i, 0 );
		}
	}else
	//if( cnt3 >= 8 && cnt3 < 28 ){
	if( cnt3 >= 8 && cnt3 < 31 ){
		if( cnt3 < 26 ){
			StockDispBuffer( ( int )x[ 0 ], ( int )y[ 0 ], DISP_PRIO_CHAR, CG_TITLE_NAME_FLASH, 0 );
		}else{
			StockDispBuffer( ( int )x[ 0 ], ( int )y[ 0 ], DISP_PRIO_CHAR, CG_TITLE_NAME_FLASH1 + cnt3 - 26 , 0 );
		}	
		cnt3++;
	}
	else StockDispBuffer( ( int )x[ 0 ], ( int )y[ 0 ], DISP_PRIO_CHAR, CG_TITLE_NAME, 0 );
	
	// 初期化キー
	//if( joy_trg[ 0 ] & JOY_HOME ) flag = 0;
}

