﻿/************************/
/*	chat.c				*/
/************************/
#include <time.h>
#include <direct.h>
#include "../systeminc/system.h"
#include "../systeminc/main.h"
#include "../systeminc/process.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/netproc.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/savedata.h"
#include "../systeminc/menu.h"
#include "../systeminc/t_music.h"
#include "../systeminc/tool.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/sndcnf.h"

#define MY_CHAT_LEN 78
#define DISP_CHAT_LEN 70
#define CHAT_ZOUBUN_Y 20
#define DISP_CHAT_X 8
#define DISP_CHAT_Y 400
#define CHAT_SPASE_STR " "	// チャットが改行される时の左のスペース文字列
#define DISP_CHINESE_FLAG 1

#ifdef PUK2_NEW_MENU
extern INPUT_STR MenuBBSInputStr;
void CountAddNewChatStockLine(void);
void InitCountNewChatStockLine(void);
int GetChatFontWidthSize(void);
#endif

#ifdef PUK3
#ifdef PUK3_PROF
extern INPUT_STR MenuProfMenuProfileInputStr;
extern INPUT_STR ProfileBBS4EditStr;
#endif
#endif

// フォントサイズ别チャット座标调整テーブル	中、小、大
int DispChatCenterY[ FONT_KIND_MAX ] = 		{ 2, 3, 0, 2, 3, 0 };
// フォントサイズ别チャット２行目以降の空白数テーブル
int DispChatSpaceSize[ FONT_KIND_MAX ] = 	{ 5, 6, 4, 5, 6, 4 };
// １行の文字の长さテーブル
unsigned int DispChatOneLineLen[ FONT_KIND_MAX ] = 	{ 70, 78, 57, 70, 78, 57 };

// チャットバッファー
CHAT_BUFFER ChatBuffer[ MAX_CHAT_LINE ];

// 自分の入力するバッファー
INPUT_STR MyChatBuffer;

// 现在の入力フォーカスバッファへのポインタ
INPUT_STR *pNowInputStr = NULL;

// チャット行カウンター
int NowChatLine = 0;
// 初期行数
int NowMaxChatLine = DEF_CHAT_LINE;
// 初期声の大きさ
int NowMaxVoice = DEF_VOICE;
// チャットが流れる时にサウンドを鸣らすか？
int NowChatSound = DEF_SOUND;
// カーソル点灭カウント
int CursorFlashCnt = 0;
// スムーズスクロール座标
int ChatLineSmoothY = 0;
// チャットの一时非表示フラグ
BOOL ChatHideFlag = FALSE;
// 聊天范围表示フラグ
BOOL ChatAreaDispFlag = FALSE;
// 聊天范围表示时间
int ChatAreaDispTime;

// チャットの履历ファイルネーム
#define CAHT_HISTORY_STR_FILE_NAME 	"chathis.dat"
// チャットのヒストリー构造体
CHAT_HISTORY ChatHistory;

// ＴＡＢキー处理
void KeyboardTab( void );

#ifdef _DEBUG
#define HTML_CHAT_LOG
#endif
#ifdef PUK3_USE_NOMALCHATLOG
	#undef HTML_CHAT_LOG
#endif

// ログファイル
FILE *chatLogFile = NULL;						// ログのファイルポインタ

#ifdef HTML_CHAT_LOG
char chatLogFileName[] = "chat_000000.html";	// ログファイル名               //MLHIDE
#else
char chatLogFileName[] = "chat_000000.txt";		// ログファイル名               //MLHIDE
#endif

char chatLogFolderName[128] = "Log";			// ログファイル保存フォルダ名               //MLHIDE
void openChatLogFile( void );

// 半角カナから全角カナ变换用文字列
char *ZenkakuKana = "。。「」、。ヲァィゥェォャュョッ"                                //MLHIDE
					"ーアイウエオカキクケコサシスセソ"                                               //MLHIDE
					"タチツテトナニヌネノハヒフヘホマ"                                               //MLHIDE
					"ミムメモヤユヨラリルレロワン゛゜";                                              //MLHIDE


// チャット文字列登録ファイル名
char *chatRegFileName = "chatReg.dat";                                //MLHIDE
// 登録ファイルが保存されるフォルダ
char chatRegFolderName[128] = "data";                                 //MLHIDE
// チャット文字列登録の入力バッファ
INPUT_STR chatRegStr[MAX_CHAT_REG];



//****************************************************************************/
// 入力文字列初期化
//****************************************************************************/
//	引数：	INPUT_STR *inputStr,// 文字列の座标
//			int x, int y,		// 文字列の座标
//			int fontPrio,		// 表示の优先度
//			int fontKind,		// 文字の种类
//			int color,			// 文字の色　
//			char *str, 			// 初期文字列（无しならＮＵＬＬ）
//			int lineMax, 		// 最大行数
//			int lineLen,		// 一行の文字の长さ
//			int lineDist,		// 行と行の间隔（Ｙサイズ）
//			BOOL blindFlag		// 文字を见えなくする（＊＊＊＊＊）
//****************************************************************************/
void InitInputStr( INPUT_STR *inputStr, int x, int y, int fontPrio, int fontKind, int color,
					char *str, int lineMax, int lineLen, int lineDist, BOOL blindFlag )
{
	INPUT_STR *inputStrBak;	// バックアップ用

	inputStr->x = x;                        // 文字列の座标
	inputStr->y = y;                        // 文字列の座标
	inputStr->fontPrio = fontPrio;			// 表示の优先度
	inputStr->fontKind = fontKind;			// 文字の种类
	inputStr->color = color;				// 文字の色
	inputStr->lineMax = lineMax;			// 最大行数
	inputStr->lineLen = lineLen;			// 一行の文字の长さ
	inputStr->lineDist = lineDist;			// 行と行の间隔さ（Ｙサイズ）
	inputStr->blindFlag = blindFlag;		// 文字を见えなくする（＊＊＊＊＊）
	inputStr->buffer[ 0 ] = '\0';				// 一应初期化しておく
	inputStr->cnt = 0;
	inputStr->cursorByte = 0;
#ifdef PUK2
	inputStr->dispWidth = 0;				// 通常は表示幅を无制限に
	inputStr->dispByte = 0;					// （概念上の）表示开始位置は文字列の先头から
#endif

	// 初期文字列がある时
	if( str != NULL ){
		inputStrBak = pNowInputStr;		// バックアップとる
		GetKeyInputFocus( inputStr );	// キーボードフォーカス变更
		StrToNowInputStr( str );		// 文字列を送る
		GetKeyInputFocus( inputStrBak );// キーボードフォーカス戾す
	}

}
/* チャット关连初期化 **********************************************************/
void InitChat( void )
{
	int i;

	// バッファークリア
	memset( ChatBuffer, 0, sizeof( CHAT_BUFFER ) * MAX_CHAT_LINE );
	memset( &MyChatBuffer, 0, sizeof( INPUT_STR ) );
//	void InitInputStr( INPUT_STR *inputStr, int x, int y, int fontPrio, int fontKind, UCHAR color,
//					char *str, UCHAR len, UCHAR lineLen, UCHAR lineDist, BOOL blindFlag )
	// チャット入力文字列初期化

	InitInputStr( &MyChatBuffer, 8, DEF_APPSIZEY- 40, FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_WHITE,
					NULL/*"我是太田。"*/, 1, MY_CHAT_LEN, 0, 0 );

#if 0
	// チャット入力バッファーの初期化
	//MyChatBuffer.len = MY_CHAT_LEN * 2;	// 文字列の长さ
	MyChatBuffer.len = MY_CHAT_LEN;	// 文字列の长さ
	//MyChatBuffer.lineLen = MY_CHAT_LEN;	// 文字列の长さ
	MyChatBuffer.lineDist = 16;	// 行の间隔
	//MyChatBuffer.color = 0; // 色

	MyChatBuffer.fontPrio = FONT_PRIO_BACK;	// 	表示の优先顺位

	MyChatBuffer.fontKind = FONT_KIND_SMALL;	// 78文字
	//MyChatBuffer.fontKind = FONT_KIND_MIDDLE;	// 70
	//MyChatBuffer.fontKind = FONT_KIND_BIG;	// 56

	MyChatBuffer.x = 8; 	// Ｘ座标
	MyChatBuffer.y = DEF_APPSIZEY - 48; // Ｙ座标
	MyChatBuffer.cursorByte = 0; // カーソルの场所
	//MyChatBuffer.x = 8; 	// Ｘ座标
	//MyChatBuffer.y = 200; 	// Ｙ座标
	//MyChatBuffer.lineLen = 25; 	// Ｘ座标
	//MyChatBuffer.lineDist = 16; 	// Ｙ座标
#endif
	// フラグ初期化
	for( i = 0 ; i < 19 ; i++ )	ChatBuffer[ i ].mojiFlag = TRUE;
	// 保存されているチャット情报を読みこむ
	//getUserChatOption( selectPcNo );	// ohta

	// 聊天范围表示フラグ
	ChatAreaDispFlag = FALSE;
	// 聊天范围表示时间
	ChatAreaDispTime = 0;

	// チャットバッファに送られた文字列をログファイルに出力
	openChatLogFile();

}

char path[256];

/* チャットログファイルのオープン **********************************************/
void openChatLogFile( void )
{
//	char path[256];

	if( chatLogFile == NULL )
	{
		_mkdir( chatLogFolderName );

		struct tm *nowTime;
		time_t longTime;

		// 现在の日时を取得
		time( &longTime );
		nowTime = localtime( &longTime );

#if defined(_DEBUG) && !defined(PUK3_USE_NOMALCHATLOG)
////#ifdef _DEBUG
		// 今は起动时に新规作成
		sprintf( path, "%s\\%s", chatLogFolderName, chatLogFileName );      //MLHIDE
		chatLogFile = fopen( path, "wt" );                                  //MLHIDE
/*
		// 今は起动时に新规作成
		sprintf( path, "%s\\%s", chatLogFolderName, chatLogFileName );

		// 开けるかどうか挑战する
		if( ( chatLogFile = fopen( path, "r" ) ) == NULL ){
			//if( ( chatLogFile = fopen( path, "a" ) ) == NULL ){
			// 开けなかったら新规作成
			chatLogFile = fopen( path, "a" );
*/
		if(chatLogFile){
	#ifdef HTML_CHAT_LOG

		fprintf( chatLogFile, "<html>\n" );                                 //MLHIDE
		fprintf( chatLogFile, "<head>\n\n" );                               //MLHIDE
		fprintf( chatLogFile, "<meta http-equiv=\"Content-Type\" content=\"text/html; charset=gbk\">\n" ); //MLHIDE
		// タイトル表示
		fprintf( chatLogFile, "<title>聊天记录 [ %02d/%02d/%02d ]</title>\n\n", (nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday ); //MLHIDE
		fprintf( chatLogFile, "</head>\n\n" );                              //MLHIDE
		fprintf( chatLogFile, "<body bgcolor=\"#000000\">\n\n" );           //MLHIDE
		fprintf( chatLogFile, "<font color=\"#FFFFFF\" size=\"6\" face=\"宋体"><b>日期[ %02d/%02d/%02d ]<b></font><br>\n", //MLHIDE
			(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday );

		// ログインした日时记録
		fprintf( chatLogFile, "<br><hr><font color=\"#FFFFFF\" size=\"5\" face=\"宋体"><b><em>时间 %02d/%02d/%02d %02d:%02d:%02d</em><b></font><br><hr>\n", //MLHIDE
			(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday,
			nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );
	#else
		fprintf( chatLogFile, ">>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<\n" ); //MLHIDE
		fprintf( chatLogFile, ">>  聊天记录 [ %02d/%02d/%02d ]  <<\n", (nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday ); //MLHIDE
		fprintf( chatLogFile, ">>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<\n" ); //MLHIDE
		fprintf( chatLogFile, "\n----------------------------------------------------------------------------------\n" ); //MLHIDE
		fprintf( chatLogFile, "时间 %02d/%02d/%02d %02d:%02d:%02d\n",         //MLHIDE
			(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday,
			nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );
		fprintf( chatLogFile, "----------------------------------------------------------------------------------\n" ); //MLHIDE
		//fprintf( chatLogFile, "-----------------------------------------\n" );
	#endif
		}
#else

	#ifdef HTML_CHAT_LOG
		sprintf( chatLogFileName, "chat_%02d%02d%02d.html",                 //MLHIDE
	#else
		sprintf( chatLogFileName, "chat_%02d%02d%02d.txt",                  //MLHIDE
	#endif
			(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday );

		// 今は起动时に新规作成
		sprintf( path, "%s\\%s", chatLogFolderName, chatLogFileName );      //MLHIDE

		// 开けるかどうか挑战する
		if( ( chatLogFile = fopen( path, "r" ) ) == NULL ){                 //MLHIDE
			//if( ( chatLogFile = fopen( path, "a" ) ) == NULL ){
			// 开けなかったら新规作成
			chatLogFile = fopen( path, "a" );                                  //MLHIDE
	#ifdef HTML_CHAT_LOG
			fprintf( chatLogFile, "<html>\n" );                                //MLHIDE
			fprintf( chatLogFile, "<head>\n\n" );                              //MLHIDE
			fprintf( chatLogFile, "<meta http-equiv=\"Content-Type\" content=\"text/html; charset=gbk\">\n" ); //MLHIDE
			// タイトル表示
			fprintf( chatLogFile, "<title>聊天记录 [ %02d/%02d/%02d ]</title>\n\n", (nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday ); //MLHIDE
			fprintf( chatLogFile, "</head>\n\n" );                             //MLHIDE
			fprintf( chatLogFile, "<body bgcolor=\"#000000\">\n\n" );          //MLHIDE
			fprintf( chatLogFile, "<font color=\"#FFFFFF\" size=\"6\" face=\"宋体\"><b>聊天记录[ %02d/%02d/%02d ]<b></font><br>\n", //MLHIDE
				(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday );

	#else

			fprintf( chatLogFile, ">>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<\n" ); //MLHIDE
			fprintf( chatLogFile, ">>  聊天记录  [ %02d/%02d/%02d ]  <<\n", (nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday ); //MLHIDE
			fprintf( chatLogFile, ">>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<\n" ); //MLHIDE

	#endif

		}else{
			// 开けたら一度关闭
			fclose( chatLogFile );
			chatLogFile = fopen( path, "a" );                                  //MLHIDE
		}

		if( chatLogFile )
		{

	#ifdef HTML_CHAT_LOG


			// ログインした日时记録
			fprintf( chatLogFile, "<br><hr><font color=\"#FFFFFF\" size=\"5\" face=\"宋体\"><b><em>时间 %02d/%02d/%02d %02d:%02d:%02d</em><b></font><br><hr>\n", //MLHIDE
				(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday,
				nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );

	#else
			fprintf( chatLogFile, "\n----------------------------------------------------------------------------------\n" ); //MLHIDE
			fprintf( chatLogFile, "时间 %02d/%02d/%02d %02d:%02d:%02d\n",        //MLHIDE
				(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday,
				nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );
			fprintf( chatLogFile, "----------------------------------------------------------------------------------\n" ); //MLHIDE
	#endif
		}
#endif
	}
}

// チャットの履历文字の保存 ****************************************************************/
BOOL SaveChatHistoryStr( int no )
{
	FILE *fp;
	char szChatHistoryName[128];

	// ファイル名作成
	sprintf( szChatHistoryName, "%s\\%s", dataFolder, CAHT_HISTORY_STR_FILE_NAME ); //MLHIDE

	// 书き込みファイルオープン
	if( ( fp = fopen( szChatHistoryName, "r+b" ) ) == NULL ){            //MLHIDE
		return FALSE;
	}

	// 今回书き込む场所までファイルポインタを进ませる
	fseek( fp, sizeof( ChatHistory.str[ 0 ] ) * no, SEEK_SET );
	// 文字列データ书き込み
	if( fwrite( &ChatHistory.str[ no ], sizeof( ChatHistory.str[ 0 ] ), 1, fp ) < 1 ){

		fclose( fp );// ファイルクローズ
		return FALSE;
	}

	// 最新の履历番号の场所までファイルポインタ进ませる
	fseek( fp, sizeof( ChatHistory.str[ 0 ] ) * MAX_CHAT_HISTORY, SEEK_SET );
	// 最新の履历番号の保存
	if( fwrite( &no, sizeof( int ), 1, fp ) < 1 ){

		fclose( fp );// ファイルクローズ
		return FALSE;
	}

	// ファイルクローズ
	fclose( fp );

	return TRUE;
}

// チャットの履历文字の読み込み ****************************************************************/
BOOL LoadChatHistoryStr( void )
{
	FILE *fp;
	char szChatHistoryName[128];

	// 现在のチャット履历番号の初期化
	ChatHistory.nowNo = -1;
	// ファイル名作成
	sprintf( szChatHistoryName, "%s\\%s", dataFolder, CAHT_HISTORY_STR_FILE_NAME ); //MLHIDE

	// 読み込みファイルオープン
	if( ( fp = fopen( szChatHistoryName, "rb" ) ) == NULL ){             //MLHIDE
		// ない时、强制的に作成
		if( ( fp = fopen( szChatHistoryName, "wb" ) ) != NULL ){            //MLHIDE
			// データ书き込み
			fwrite( &ChatHistory, sizeof( CHAT_HISTORY ) - sizeof( int ), 1, fp );
			fclose( fp );	// ファイルクローズ
		}

		// 最新のチャット履历番号の初期化
		ChatHistory.nowNo = MAX_CHAT_HISTORY - 1;

		return FALSE;
	}

	// データ読み込み
	if( fread( &ChatHistory, sizeof( CHAT_HISTORY ) - sizeof( int ), 1, fp ) < 1 ){

		fclose( fp );	// ファイルクローズ
		return FALSE;
	}
	// ファイルクローズ
	fclose( fp );

	return TRUE;
}

//******************************************************************************/
// 文字列を现在の入力文字列に送る
//******************************************************************************/
//	引数：	char *str	：送る文字列
//******************************************************************************/
void StrToNowInputStr( char *str )
{
	int strLen, i;

	// 入力场所が无い时返回
	if( pNowInputStr == NULL ) return;

	// 文字の长さの取得
	strLen = strlen( str );
	// リミットチェック
	if( strLen >= INPUT_STR_SIZE ) strLen = INPUT_STR_SIZE - 1;
	// 文字の数だけループ
	for( i = 0 ; i < strLen ; i++ ){
		// 文字バッファに溜める
		StockInputStrChar( str[ i ] );
	}
}

/* チャット关连处理 ************************************************************/
void ChatProc( void )
{
	int StrLen;
	char StrWork[256];

	// チャットバッファーをフォントバッファに溜める
	ChatBufferToFontBuffer();

	// 入力场所が无い时返回
	if( pNowInputStr != &MyChatBuffer ) return;

	// 入力フォーカスがチャットの时、ＩＭＥバッファに文字が无い时
	//if( pNowInputStr == &MyChatBuffer && ImeBufferBak2 == NULL ){
	if( pNowInputStr == &MyChatBuffer && ImeInfo.buffer[ 0 ] == NULL ){
		// 上キーを押した时
#ifdef PUK2
		if( !( VK[VK_CONTROL] & KEY_ON ) && !( VK[VK_SHIFT] & KEY_ON ) && VK[ VK_UP ] & KEY_ON_REP ){
#else
		if( !( VK[VK_CONTROL] & KEY_ON ) && VK[ VK_UP ] & KEY_ON_REP ){
#endif
			int bak = ChatHistory.nowNo;	// バックアップ
			// ヒストリーモードに入ってない时
			if( ChatHistory.nowNo == -1 ) ChatHistory.nowNo = ChatHistory.newNo;
			else ChatHistory.nowNo--;
			// リミットチェック
			if( ChatHistory.nowNo < 0 ) ChatHistory.nowNo = MAX_CHAT_HISTORY - 1;
			// ヒストリーがある时かつ、( 一周回ってない时又は、ヒストリーモードに入っていなかった时 ）
			if( ChatHistory.str[ ChatHistory.nowNo ][ 0 ] != 0 && ( ChatHistory.nowNo != ChatHistory.newNo || bak == -1 ) ){
				// 入力してある文字を初期化
				pNowInputStr->cnt = 0;
				pNowInputStr->buffer[ 0 ] = NULL;
				// カーソル位置を初期化
				pNowInputStr->cursorByte = 0;
				// ヒストリー文字列を入力バッファへ送る
				StrToNowInputStr( ChatHistory.str[ ChatHistory.nowNo ] );

				// カーソルの位置调整
				strcpy(StrWork,ChatHistory.str[ ChatHistory.nowNo ]);

				// 开始位置决定
				StrLen=strlen(StrWork);
				StrLen-=GetChatFontWidthSize();
				if(CheckLetterType(StrWork,StrLen)==CHECKLETTERTYPE_FULL_TAIL){
					StrLen++;
				}
				if(StrLen<0)
					StrLen=0;
				pNowInputStr->dispByte=StrLen;

				// カーソル位置决定
//				pNowInputStr->cursorByte=StrLen;
			}else{
				// バックアップから戾す
				ChatHistory.nowNo = bak;
			}
		}else
		// 下キーを押した时
		if( !( VK[VK_CONTROL] & KEY_ON ) && VK[ VK_DOWN ] & KEY_ON_REP ){
			// ヒストリーモードに入っていたら
			if( ChatHistory.nowNo != -1 ){
				// ヒストリーモードを拔ける时
				if( ChatHistory.nowNo == ChatHistory.newNo ){
					ChatHistory.nowNo = -1;
					// 入力してある文字を初期化
					pNowInputStr->cnt = 0;
					pNowInputStr->buffer[ 0 ] = NULL;
					// カーソル位置を初期化
					pNowInputStr->cursorByte = 0;
				}else{
					ChatHistory.nowNo++;
					// リミットチェック
					if( ChatHistory.nowNo >= MAX_CHAT_HISTORY ) ChatHistory.nowNo = 0;
					// 入力してある文字を初期化
					pNowInputStr->cnt = 0;
					pNowInputStr->buffer[ 0 ] = NULL;
					// カーソル位置を初期化
					pNowInputStr->cursorByte = 0;
					// ヒストリー文字列を入力バッファへ送る
					StrToNowInputStr( ChatHistory.str[ ChatHistory.nowNo ] );

					// カーソルの位置调整
					strcpy(StrWork,ChatHistory.str[ ChatHistory.nowNo ]);

					// 开始位置决定
					StrLen=strlen(StrWork);
					StrLen-=GetChatFontWidthSize();
					// 全角半角调整
					if(CheckLetterType(StrWork,StrLen)==CHECKLETTERTYPE_FULL_TAIL){
						StrLen++;
					}
					if(StrLen<0)
						StrLen=0;
					pNowInputStr->dispByte=StrLen;

					// カーソル位置决定
//					pNowInputStr->cursorByte=StrLen;
				}
			}
		}
	}

}

//チャットウインドウ描画フラグ
extern int ChatWindowView;

// キーボードカーソル点灭处理 **************************************************/
void FlashKeyboardCursor( void )
{

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;

#ifdef PUK2
	//ウインドウ非表示の时
	if(pNowInputStr==&MyChatBuffer){
		if(ChatWindowView==0){
			return;
		}
	}
#endif

	// カーソル点灭处理
	if( CursorFlashCnt >= 20 ){
		// ＩＭＥがＯＦＦの时
		if( ImeOpenState() == FALSE ){
			// カーソルをフォントバッファへ溜める
			StockFontBuffer( pNowInputStr->imeX - FontKind[ pNowInputStr->fontKind ].zenkakuWidth / 2,
								pNowInputStr->imeY, pNowInputStr->fontPrio + 1, pNowInputStr->fontKind, pNowInputStr->color, ML_STRING(993, "｜") , 0 );
		}else{ 	// ＩＭＥがＯＮの时
			// ＩＭＥバッファに文字が无い时
			//if( ImeBufferBak2 == NULL ){
			if( ImeInfo.buffer[ 0 ] == NULL ){
				// カーソルをフォントバッファへ溜める
				StockFontBuffer( pNowInputStr->imeX - FontKind[ pNowInputStr->fontKind ].zenkakuWidth / 2,
									pNowInputStr->imeY, pNowInputStr->fontPrio+1 , pNowInputStr->fontKind, pNowInputStr->color, ML_STRING(993, "｜") , 0 );
			}else{
				// 文节が无い时
				if( ImeInfo.block[ 0 ] == NULL ){
					// カーソルをフォントバッファへ溜める
					StockFontBuffer( pNowInputStr->imeX - FontKind[ pNowInputStr->fontKind ].zenkakuWidth / 2 + GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind, ImeInfo.cursorPos ),
										pNowInputStr->imeY, pNowInputStr->fontPrio+1 , pNowInputStr->fontKind, pNowInputStr->color, ML_STRING(993, "｜") , 0 );
				}
			}
		}
	}

	// カーソル点灭カウンタ
	CursorFlashCnt++;
	// リミットチェック
	if( CursorFlashCnt >= 40 ) CursorFlashCnt = 0;
}

// ＬＥＦＴキー处理 *********************************************************/
void KeyboardLeft( void )
{
	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;
	// ＩＭＥバッファに文字がある时
	//if( ImeBufferBak2 != NULL ) return;
	if( ImeInfo.buffer[ 0 ] != NULL ) return;
	// カーソル位置が文字列の１番左の时返回
	if( pNowInputStr->cursorByte <= 0 ) return;

	// 前の文字のバイト数分、左へ移动
	pNowInputStr->cursorByte -= GetStrLastByte( pNowInputStr->buffer, pNowInputStr->cursorByte );
	// カーソルカウンター戾す（点灯）
	CursorFlashCnt = 20;
}

// ＲＩＧＨＴキー处理 *********************************************************/
void KeyboardRight( void )
{
	int flag = FALSE;

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;
	// ＩＭＥバッファに文字がある时
	//if( ImeBufferBak2 != NULL ) return;
	if( ImeInfo.buffer[ 0 ] != NULL ) return;
	// カーソル位置が文字列の１番最后の时返回
	if( pNowInputStr->cursorByte >= pNowInputStr->cnt ) return;

	// 次ぎの文字のバイト数分、右へ移动
	pNowInputStr->cursorByte += GetCharByte( pNowInputStr->buffer[ pNowInputStr->cursorByte ] );
	// カーソルカウンター戾す（点灯）
	CursorFlashCnt = 20;
}

// ＵＰキー处理 *********************************************************/
void KeyboardUp( void )
{
	int lineDist = 0; // 左上からのＹOFFセット座标
	int splitPoint = 0;
	//char splitStr[ 256 ];
	int flag = FALSE;
	int flag2 = FALSE;
	int cursorSplitPoint = 0;
	int lineDistBak = 0;
	int splitPointBak = 0;
	int cnt;
	int lineCnt = 0;	// 行数カウンター
	int myLine = 0;		// カーソルのある行
	int myByte = 0;		// 横からカーソルまでのバイト数
	int backByte = 0;	// 	戾すバイト数
	int mojiCnt[ INPUT_STR_SIZE ];
	int i;

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;
	// ＩＭＥバッファに文字がある时
	//if( ImeBufferBak2 != NULL ) return;
	if( ImeInfo.buffer[ 0 ] != NULL ) return;

	// カーソルカウンター戾す（点灯）
	CursorFlashCnt = 20;

	// カーソルの位置初期化
//	pNowInputStr->cursorByte=0;
#ifdef PUK2
//	pNowInputStr->dispByte=0;
#endif
	// 改行文字列で无い时
	if( pNowInputStr->lineMax <= 1 ) return;

	// 行单位の文字数チェック
	while( 1 ){
		cnt = 0; flag = FALSE;
		// バックアップ
		splitPointBak = splitPoint;

		// 一行に改行コードがあるかチェック
		while( 1 ){
			// 文字の最后なら拔ける
			if( *( pNowInputStr->buffer + splitPoint + cnt ) == NULL ) break;
			// 改行コードが见つかった时
			if( *( pNowInputStr->buffer + splitPoint + cnt ) == 0x0d ){
				flag = TRUE;
				// 分割ポイントを进ませる
				splitPoint = splitPoint + cnt + 1;
				// 行单位の文字数チェック记忆
				mojiCnt[ lineCnt ] = splitPoint - splitPointBak;
				// 行数カウント
				lineCnt++;

				break;
			}
			// 次の文字へ
			cnt++;
			// リミットチェック
			if( cnt == pNowInputStr->lineLen ) break;
		}

		// 改行文字が无い时
		if( flag == FALSE ){
			// 一行に收まらないかチェック
			if( strlen( pNowInputStr->buffer + splitPoint  ) >= (UINT)pNowInputStr->lineLen ){
				// 最后の全角文字が分割されている时
				//if( GetCharByte( pNowInputStr->buffer[ splitPoint ] ) == 3 ){
				if( GetStrLastByte( pNowInputStr->buffer + splitPoint, pNowInputStr->lineLen ) == 3 ){
					// 一文字戾す
					splitPoint = pNowInputStr->lineLen - 1 + splitPoint;
				}
				else splitPoint = pNowInputStr->lineLen + splitPoint;

				// 行单位の文字数チェック记忆
				mojiCnt[ lineCnt ] = splitPoint - splitPointBak;
				// 行数カウント
				lineCnt++;

			}else break;
		}

		// カーソルの位置情报を记忆する
		if( flag2 == FALSE ){
			// 检索位置がカーソル位置を超えた时
			if( pNowInputStr->cursorByte < splitPoint ){
				// カーソルのある行を学习
				myLine = lineCnt - 1;
				// 左からカーソルまでのバイト数を学习
				myByte = pNowInputStr->cursorByte - splitPointBak;
				flag2 = TRUE; // フラグＯＮ
			}
		}
	}

	// カーソルの位置情报を记忆する
	if( flag2 == FALSE ){
		// 最大行数の时
		if( pNowInputStr->lineMax <= lineCnt ){
			// カーソルのある行を学习
			myLine = lineCnt;
			// カーソル１文字だけ戾す
			pNowInputStr->cursorByte -= 1;
			// 左からカーソルまでのバイト数を学习
			myByte = pNowInputStr->cursorByte - splitPointBak;
		}else{
			// カーソルのある行を学习
			myLine = lineCnt;
			// 左からカーソルまでのバイト数を学习
			myByte = pNowInputStr->cursorByte - splitPointBak;
		}
	}

	// カーソルが１番上の行の时拔ける
	if( myLine <= 0 ) return;

	// 分割ポイント别に戾すバイト数を计算
	if( mojiCnt[ myLine - 1 ] <= myByte ){
		// １行以内に改行コードが无いかチェック
		for( i = 0 ; i < pNowInputStr->lineLen ; i++ ){
			// １文字戾す
			pNowInputStr->cursorByte--;
			// 改行コードだった时
			if( pNowInputStr->buffer[ pNowInputStr->cursorByte ] == 0x0d ) break;
		}
	}else{
		// カーソル戾す
		pNowInputStr->cursorByte -= mojiCnt[ myLine - 1 ];
	}

	// 全角の半分かチェック
	if( GetStrLastByte( pNowInputStr->buffer, pNowInputStr->cursorByte ) == 3 ){
		pNowInputStr->cursorByte--;
	}

}

// ＤＯＷＮキー处理 *********************************************************/
void KeyboardDown( void )
{
	int lineDist = 0; // 左上からのＹOFFセット座标
	int splitPoint = 0;
	//char splitStr[ 256 ];
	int flag = FALSE;
	int flag2 = FALSE;
	int cursorSplitPoint = 0;
	int lineDistBak = 0;
	int splitPointBak = 0;
	int cnt;
	int lineCnt = 0;	// 行数カウンター
	int myLine = 0;		// カーソルのある行
	int myByte = 0;		// 横からカーソルまでのバイト数
	int backByte = 0;	// 	戾すバイト数
	int mojiCnt[ INPUT_STR_SIZE ];

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;
	// ＩＭＥバッファに文字がある时
	//if( ImeBufferBak2 != NULL ) return;
	if( ImeInfo.buffer[ 0 ] != NULL ) return;

	// カーソルカウンター戾す（点灯）
	CursorFlashCnt = 20;

	// 改行文字列で无い时
	if( pNowInputStr->lineMax <= 1 ) return;

	// 行单位の文字数チェック
	while( 1 ){
		cnt = 0; flag = FALSE;
		// バックアップ
		splitPointBak = splitPoint;

		// 一行に改行コードがあるかチェック
		while( 1 ){
			// 文字の最后なら拔ける
			if( *( pNowInputStr->buffer + splitPoint + cnt ) == NULL ) break;
			// 改行コードが见つかった时
			if( *( pNowInputStr->buffer + splitPoint + cnt ) == 0x0d ){
				flag = TRUE;
				// 分割ポイントを进ませる
				splitPoint = splitPoint + cnt + 1;
				// 行单位の文字数チェック记忆
				mojiCnt[ lineCnt ] = splitPoint - splitPointBak;
				// 行数カウント
				lineCnt++;

				break;
			}
			// 次の文字へ
			cnt++;
			// リミットチェック
			if( cnt == pNowInputStr->lineLen ) break;
		}

		// 改行文字が无い时
		if( flag == FALSE ){
			// 一行に收まらないかチェック
			if( strlen( pNowInputStr->buffer + splitPoint  ) >= (UINT)pNowInputStr->lineLen ){
				// 最后の全角文字が分割されている时
				//if( GetCharByte( pNowInputStr->buffer[ splitPoint ] ) == 3 ){
				if( GetStrLastByte( pNowInputStr->buffer + splitPoint, pNowInputStr->lineLen ) == 3 ){
					// 一文字戾す
					splitPoint = pNowInputStr->lineLen - 1 + splitPoint;
				}
				else splitPoint = pNowInputStr->lineLen + splitPoint;

				// 行单位の文字数チェック记忆
				mojiCnt[ lineCnt ] = splitPoint - splitPointBak;
				// 行数カウント
				lineCnt++;

			}else break;
		}

		// カーソルの位置情报を记忆する
		if( flag2 == FALSE ){
			// 检索位置がカーソル位置を超えた时
			if( pNowInputStr->cursorByte < splitPoint ){
				// カーソルのある行を学习
				myLine = lineCnt - 1;
				// 左からカーソルまでのバイト数を学习
				myByte = pNowInputStr->cursorByte - splitPointBak;
				flag2 = TRUE; // フラグＯＮ
			}
		}
	}

	// カーソルの位置情报を记忆する
	if( flag2 == FALSE ){
		// カーソルのある行を学习
		myLine = lineCnt;
		// 左からカーソルまでのバイト数を学习
		myByte = pNowInputStr->cursorByte - splitPointBak;
	}
	// 余った文字の长さ
	mojiCnt[ lineCnt ] = strlen( pNowInputStr->buffer + splitPoint ) + 1;

	// カーソルが１番下の行の时拔ける
#ifdef IME_CURSOR_NEW
	if( myLine >= lineCnt ) return;
#else
	if( myLine >= lineCnt - 1 ) return;
#endif

	// 分割ポイント别に戾すバイト数を计算
	if( myByte < mojiCnt[ myLine + 1 ] ){
		// カーソル进ませる
		pNowInputStr->cursorByte += mojiCnt[ myLine ];
	}else{
		// カーソル进ませる
		pNowInputStr->cursorByte += mojiCnt[ myLine ] - myByte + mojiCnt[ myLine + 1 ] - 1;
	}

	// 全角の半分かチェック
	if( GetStrLastByte( pNowInputStr->buffer, pNowInputStr->cursorByte ) == 3 ){
		pNowInputStr->cursorByte--;
	}
}


// ＢＳキー处理 ***************************************************************/
void KeyboardBackSpace( void )
{
	int byte = 0;
	int flag = FALSE;
	char work[ INPUT_STR_SIZE ];

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;

	// カーソル位置が文字列の１番左の时返回
	if( pNowInputStr->cursorByte <= 0 ) return;

	// カーソルの前の文字が全角か半角か调べる
	byte = GetStrLastByte( pNowInputStr->buffer, pNowInputStr->cursorByte );
	// 切り取った文字を一时保存
	strcpy( work, &pNowInputStr->buffer[ pNowInputStr->cursorByte ] );

	// 終端记号を入れる
	pNowInputStr->buffer[ pNowInputStr->cursorByte - byte ] = NULL;
	// 一时保存しておいた文字列と连结
	strcat( pNowInputStr->buffer, work );

	// バイト数分返回
	pNowInputStr->cnt -= byte;
	// バイト数分返回
	pNowInputStr->cursorByte -= byte;

	// リミットチェック
	if( pNowInputStr->cursorByte < 0 ) pNowInputStr->cursorByte = 0;
	// リミットチェック
	if( pNowInputStr->buffer[ 0 ] == NULL ) pNowInputStr->cnt = 0;

	// フラッシュカウンター戾す（点灯）
	CursorFlashCnt = 20;
}

// ＤＥＬＥＴＥキー处理 ***************************************************************/
void KeyboardDelete( void )
{
	int byte = 0;
	char work[ INPUT_STR_SIZE ];
	char cnt = 0;

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;

	// カーソル位置が文字列の１番最后の时返回
	if( pNowInputStr->cursorByte >= pNowInputStr->cnt ) return;

	// 次の文字が全角か半角か调べる
	byte = GetCharByte( pNowInputStr->buffer[ pNowInputStr->cursorByte ] );
	// 文字を切り取って一时保存
	strcpy( work, &pNowInputStr->buffer[ pNowInputStr->cursorByte + byte ] );

	// 一时保存しておいた文字列と连结
	strcpy( &pNowInputStr->buffer[ pNowInputStr->cursorByte ], work );

	// バイト数分返回
	pNowInputStr->cnt -= byte;

	// リミットチェック
	if( pNowInputStr->buffer[ 0 ] == NULL ) pNowInputStr->cnt = 0;

	// フラッシュカウンター戾す（点灯）
	CursorFlashCnt = 20;
}

// ＴＡＢキー处理 ***************************************************************/
void KeyboardTab( void )
{
//	int i,flag = 0;

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;

#if 0
	// 文字登録チェック *******************************
	for( i = 0 ; i < MAX_CHAT_REGISTY_STR ; i++ ){
		if( pNowInputStr == &chatRegistryStr[ i ] ){
			flag = TRUE;
			break;
		}
	}
	// 文字登録の时 *******************************
	if( flag == TRUE ){
		// シフトキーされていたら
		if( VK[ VK_SHIFT ] & KEY_ON ){
			i--;
			// リミットチェック
			if( i < 0 ) i = MAX_CHAT_REGISTY_STR - 1;
		}else{
			i++;
			// リミットチェック
			if( i >= MAX_CHAT_REGISTY_STR ) i = 0;
		}
		// 入力フォーカス移动
		GetKeyInputFocus( &chatRegistryStr[ i ] );
	}

	// フラッシュカウンター戾す（点灯）
	CursorFlashCnt = 20;
#endif
}

// リターンキー处理 ************************************************************/
void KeyboardReturn( void )
{
	char bakNo;

	// ＩＭＥバッファーに文字がある时、返回
	//if( ImeBufferBak2 != NULL ) return;
	if( ImeInfo.buffer[ 0 ] != NULL ) return;

	// チャット入力の时 *******************************
	if( pNowInputStr == &MyChatBuffer ){;

		// 空白行だと改行しない
		if( pNowInputStr->cnt == 0 ) return;

		// チャットの履历处理
		// バックアップ
		bakNo = ChatHistory.newNo;
		// 次のバッファ番号へ
		ChatHistory.newNo++;
		// リミットチェック
		if( ChatHistory.newNo >= MAX_CHAT_HISTORY ) ChatHistory.newNo = 0;
		// 前回と违う文字列の时
		if( strcmp( pNowInputStr->buffer, ChatHistory.str[ bakNo ] ) != 0 ){
			// 今回の文字列を学习
			strcpy( ChatHistory.str[ ChatHistory.newNo ], pNowInputStr->buffer );
			// チャットの履历文字の保存
			SaveChatHistoryStr( ChatHistory.newNo );
		}else{
			// バックアップから戾す
			ChatHistory.newNo = bakNo;
		}
		// ヒストリー番号初期化
		ChatHistory.nowNo = -1;

		// チャット文字列送信
		chatStrSendForServer( pNowInputStr->buffer, MyChatBuffer.color );

#ifdef _RELDEB
		//クライアントとサーバーのバージョン情报を表示します
		//これはクライアント内で制御する特殊な拟似的デバッグコマンドで、
		//リリースの际はかならず使用されません。
		//作成：杉山
		char DebugStrWork[256];
		DebugStrWork[0]='\0';

		strcpy(DebugStrWork,pNowInputStr->buffer);

		if(strcmp(DebugStrWork,"[ver]")==0){                                //MLHIDE
			//クライアントのバージョン
			sprintf(DebugStrWork,"ClientVer=%s",PUK2_NR_VERSION);              //MLHIDE
			StockChatBufferLine(DebugStrWork, FONT_PAL_WHITE, FONT_KIND_SMALL );
		}
#endif

		// 左端に戾す
		pNowInputStr->cnt = 0;
		// 左端に戾す
		pNowInputStr->cursorByte = 0;

		// 自分の入力するバッファーをクリア
		pNowInputStr->buffer[ 0 ] = NULL;
	}
	else
	// メールの时 **********************************
	if( pNowInputStr == &mailEditStr
		|| pNowInputStr == &mailPetEditStr )
	{
		// 改行コードを入れる
		StockInputStrChar( 0x0d );
	}
	else
	// 揭示板の时
	if( pNowInputStr == &messageBoardInputBuf )
	{
		// 改行コードを入れる
		StockInputStrChar( 0x0d );
	}
#ifdef PUK2_NEW_MENU
	else
	// 新揭示板の时
	if( pNowInputStr == &MenuBBSInputStr )
	{
		// 改行コードを入れる
		StockInputStrChar( 0x0d );
	}
#endif
#ifdef PUK3
#ifdef PUK3_PROF
	else
	// プロフィールウインドウの时
	if( pNowInputStr == &MenuProfMenuProfileInputStr )
	{
		// 改行コードを入れる
		StockInputStrChar( 0x0d );
	}
	else
	//
	if( pNowInputStr == &ProfileBBS4EditStr )
	{
		// 改行コードを入れる
		StockInputStrChar( 0x0d );
	}

#endif
#endif


#if 0
	else
	// 称号变更の时 **********************************
	if( pNowInputStr == &shougouChange ){
		// 入力フォーカス戾す
		GetKeyInputFocus( &MyChatBuffer );
		// ウィンドウ关闭音
		play_se( 203, 320, 240 );
		// アクションの称号も变更
		//strcpy( pc.ptAct->freeName, shougouChange.buffer );
		// ＥＵＣに变换
		sjisStringToEucString( shougouChange.buffer );
		// 名称变更送信
		nrproto_FT_send( sockfd, shougouChange.buffer ) ; /* ../doc/nrproto.html line 1792 */
	}else
	// 名称变更の时 **********************************
	if( pNowInputStr == &petNameChange ){
		// 入力フォーカス戾す
		GetKeyInputFocus( &MyChatBuffer );
		// ウィンドウ关闭音
		play_se( 203, 320, 240 );
		// ＥＵＣに变换
		sjisStringToEucString( petNameChange.buffer );
		// 名称变更送信
		nrproto_KN_send( sockfd, petStatusNo, petNameChange.buffer ) ; /* ../doc/nrproto.html line 1792 */
	}
#endif


	// フラッシュカウンター戾す（点灯）
	CursorFlashCnt = 20;

}

//******************************************************************************/
// 文字を入力バッファに溜める（１バイト）、全ての入力文字はここを通る
//******************************************************************************/
//	引数：		char c	：溜める文字
//******************************************************************************/
void StockInputStrChar( char c )
{
	static BOOL zenkakuFlag = FALSE; // 全角フラグ
	static BOOL zenkakuFlag2 = FALSE; // 全角フラグ２
	static BOOL nextCharDelFlag = FALSE; // 次の１文字を无视するか
	INPUT_STR inputBackup;
	char work[ INPUT_STR_SIZE + 1 ];
	//int len, len2;
	int lineCnt = 0;	// 行数カウンター
	int splitPoint = 0;
	int flag = FALSE;
	int feedFlag = FALSE;
	int cnt;

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;

	// 关系ない文字は飞ばす
	if( 0x01 <= c && c <= 0x1f  && c != 0x0d ) return;

	// 前回が全角でなかったら
	if( zenkakuFlag == FALSE ){

		// 谜の sjis コードの时（フィルター）
		//if( c == ( char )0x80 ) return;

		// 全角のとき
		if( GetCharByte( c ) == 2 ) zenkakuFlag = TRUE;
		// 半角の时
		else{
			// 半角カナの时、全角かなに变换する
			if( ( char )0xa0 <= c && c <= ( char )0xdf ){
				// 再归处理
				//StockInputStrChar( ZenkakuKana[ ( c - ( char )0xa0 ) * 2 ] );
				//StockInputStrChar( ZenkakuKana[ ( c - ( char )0xa0 ) * 2 + 1 ] );

				return;
			}
		}
	}
	// 全角フラグＯＦＦ
	else zenkakuFlag = FALSE;

	// 次の文字を无视するフラグが立っているとき
	if( nextCharDelFlag){
		nextCharDelFlag = FALSE;
		return;
	}

	// 后ろの文字列をバックアップ
	strncpy( work, &pNowInputStr->buffer[ pNowInputStr->cursorByte ] , sizeof( work));

	// バックアップを取る
	strncpy( inputBackup.buffer, pNowInputStr->buffer, sizeof( inputBackup.buffer));
	inputBackup.cnt = pNowInputStr->cnt;
	inputBackup.cursorByte = pNowInputStr->cursorByte;

	// バッファーに文字を溜める
	pNowInputStr->buffer[ pNowInputStr->cursorByte ] = c;
	// 終端记号を入れる
	pNowInputStr->buffer[ pNowInputStr->cursorByte + 1 ] = NULL;

	// 入力した文字数をカウント
	pNowInputStr->cnt++;
	// カーソルの位置を进める
	pNowInputStr->cursorByte++;

	// バックアップ文字列と连结
	strncat( pNowInputStr->buffer, work, sizeof( pNowInputStr->buffer) - strlen( pNowInputStr->buffer) - 1 );

	// 何行あるかチェック
	while( 1 ){
		cnt = 0; flag = FALSE;
		// 一行に改行コードがあるかチェック
		while( 1 ){
			// 文字の最后なら拔ける
			if( *( pNowInputStr->buffer + splitPoint + cnt ) == NULL ) break;
			// 改行コードが见つかった时
			if( *( pNowInputStr->buffer + splitPoint + cnt ) == 0x0d ){
				flag = TRUE; // 改行文字フラグＯＮ
				// 分割ポイントを进ませる
				splitPoint = splitPoint + cnt + 1;
				// 行数カウント
				lineCnt++;
				// 改行フラグＯＮ
				feedFlag = TRUE;
				break;
			}
			// 次の文字へ
			cnt++;
			// リミットチェック
			if( cnt == pNowInputStr->lineLen ) break;
		}

		// 改行文字が无い时
		if( flag == FALSE ){
			// 一行に收まらないかチェック
			if( strlen( pNowInputStr->buffer + splitPoint  ) > (UINT)pNowInputStr->lineLen ){
				// 最后の全角文字が分割されている时
				if( GetStrLastByte( pNowInputStr->buffer + splitPoint, pNowInputStr->lineLen ) == 3 ){
					// 分割ポイントを一文字戾す
					splitPoint = pNowInputStr->lineLen - 1 + splitPoint;
				}
				else splitPoint = pNowInputStr->lineLen + splitPoint;
				// 行数カウント
				lineCnt++;
				// 改行フラグＯＦＦ
				feedFlag = FALSE;
			}else break;

		}
		// 行数を超えたら拔ける
		if( lineCnt >= pNowInputStr->lineMax ) break;
	}

	// 文字数がオーバーしていたら切り取る
	if( lineCnt >= pNowInputStr->lineMax && !zenkakuFlag){
		// カウントを戾す
		pNowInputStr->cnt -= strlen( pNowInputStr->buffer + splitPoint - feedFlag );
		// 余分な文字を切り取る
		pNowInputStr->buffer[ splitPoint - feedFlag ] = NULL;
		// 最后の全角文字が分割されている时
		if( GetStrLastByte( pNowInputStr->buffer ) == 3 ){
			// カウントを戾す
			pNowInputStr->cnt--;
			// 終端记号を入れる
			*( pNowInputStr->buffer + pNowInputStr->cnt ) = NULL;
		}
		// カーソルのリミットチェック
		if( pNowInputStr->cnt < pNowInputStr->cursorByte ) pNowInputStr->cursorByte = pNowInputStr->cnt;
		//if( splitPoint < pNowInputStr->cursorByte ) pNowInputStr->cursorByte = pNowInputStr->len;
	}
	else
	// バッファの最后の１文字を全角文字で上书きしようとした时
	if( lineCnt >= pNowInputStr->lineMax && zenkakuFlag && pNowInputStr->cursorByte == pNowInputStr->cnt-1){
		// バックアップを复元
		strncpy( pNowInputStr->buffer, inputBackup.buffer, sizeof( pNowInputStr->buffer));
		pNowInputStr->cnt = inputBackup.cnt;
		pNowInputStr->cursorByte = inputBackup.cursorByte;
		// 次の文字を无视する
		nextCharDelFlag = TRUE;
	}

	// フラッシュカウンター戾す（点灯）
	CursorFlashCnt = 20;

}

#if 0
/* チャット文字をバッファに溜める（２バイト）***********************************/
void StockChatBufferWord( WORD w )
{
	INPUT_STR *chatBuffer = &ChatBuffer[ NowChatLine ];

	// カウントオーバーの时
//	if( ChatCnt >= CHAT_BUFFER_SIZE - 2 ) return;

	// チャット文字数バッファーに文字を溜める
	pNowInputStr->buffer[ pNowInputStr->cnt ] = w;
	// 終端记号を入れる
	pNowInputStr->buffer[ pNowInputStr->cnt + 2 ] = NULL;

	// 次に入力するバッファーへのポインタを进ませる
	pNowInputStr->cnt++;
}
#endif

//******************************************************************************/
// チャット文字をバッファに溜める（一行）
//******************************************************************************/
//	引数：		char *str			：调べる文字列
//				unsigned char color	：文字の色
//******************************************************************************/
void StockChatBufferLine( char *str, int color )
{
	int splitPoint = 0;
#ifndef PUK2_NEW_MENU
	char splitStr[ INPUT_STR_SIZE + 1 ];
#endif

	// フォントパレットリミットチェック
	if( color >= FONT_PAL_MAX ) return;

#ifndef PUK2_NEW_MENU
	// 一行に收まらない时
	if( strlen( str ) > DISP_CHAT_LEN ){
		// 分割文字列作成
		strncpy( splitStr, str, DISP_CHAT_LEN );
		// 終端记号を入れる
		*( splitStr + DISP_CHAT_LEN ) = NULL;
		// 最后の全角文字が分割されている时
		if( GetStrLastByte( splitStr ) == 3 ){
			// 一文字戾す
			splitPoint = DISP_CHAT_LEN - 1;
			// 終端记号を入れる
			*( splitStr + DISP_CHAT_LEN - 1 ) = NULL;
		}
		else splitPoint = DISP_CHAT_LEN;

		// 文字列をチャット行に送りこむ
		strncpy( ChatBuffer[ NowChatLine ].buffer, str, splitPoint );
		// 終端记号を入れる
		*( ChatBuffer[ NowChatLine ].buffer + splitPoint ) = NULL;
	}else{
		// 文字列をチャット行に送りこむ
		strcpy( ChatBuffer[ NowChatLine ].buffer, str );
	}
#else
		// 文字列をチャット行に送りこむ
		strcpy( ChatBuffer[ NowChatLine ].buffer, str );
#endif
	// 文字表示カウンター初期化
	ChatBuffer[ NowChatLine ].mojiFlag = FALSE;
	ChatBuffer[ NowChatLine ].mojiCnt = 0;

	// チャットログファイルに出力
	if( chatLogFile )
	{

		struct tm *nowTime;
		time_t longTime;

		// 现在の日时を取得
		time( &longTime );
		nowTime = localtime( &longTime );

	#ifdef HTML_CHAT_LOG
		#ifndef PUK2
			int htmlFontSizeTbl[ 6 ] = { 3, 2, 4, 3, 2, 4 };
			char *htmlFontKind[] = { "宋体", "宋体" };                             //MLHIDE
			// フォントパレット
			DWORD htmlFontColorTbl[]={
				0xffffff, // 0:白
				0x00ffff, // 1:水色
				0xff00ff, // 2:紫
				0x0000ff, // 3:青
				0xffff00, // 4:黄
				0x00ff00, // 5:緑
				0xff0000, // 6:赤
				0xa0a0a4, // 7:灰色
				0xa6caf0, // 8:淡い青
				0xc0dcc0  // 9:淡い緑
			};
		#endif


		// ログインした日时记録
		#ifdef PUK2
			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"4\" face=\"%s\">　%02d:%02d:%02d　", //MLHIDE
				( ( FontPal[color][0]<<16 )|( FontPal[color][1]<<8 )|( FontPal[color][2] ) ),
				exfontName[ 0 ], nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );

			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"%d\" face=\"%s\"><b>%s<b></font><br>\n", //MLHIDE
				( ( FontPal[color][0]<<16 )|( FontPal[color][1]<<8 )|( FontPal[color][2] ) ),
				exfontName[ 0 ], exfontName[ 0 ], ChatBuffer[ NowChatLine ].buffer );
		#else
			//fprintf( chatLogFile, "<font color=\"#FFFFFF\" size=\"4\" face=\"宋体">　%02d:%02d:%02d　",
			//fprintf( chatLogFile, "<font color=\"#FFFFFF\" size=\"4\" face=\"宋体">　%02d:%02d:%02d　",
			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"4\" face=\"%s\">　%02d:%02d:%02d　", //MLHIDE
				htmlFontColorTbl[ color ], htmlFontKind[ 0 ], nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );

			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"%d\" face=\"%s\"><b>%s<b></font><br>\n", //MLHIDE
				htmlFontColorTbl[ color ], htmlFontSizeTbl[ 0 ], htmlFontKind[ 0 ], ChatBuffer[ NowChatLine ].buffer );
		#endif
	#else
		fprintf( chatLogFile, " %02d:%02d:%02d　%s\n",                       //MLHIDE
			nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec, ChatBuffer[ NowChatLine ].buffer );
		//fprintf( chatLogFile, "%s\n", ChatBuffer[ NowChatLine ].buffer );
		// フラッシュ
		fflush( chatLogFile );
	#endif

	}
	// 色の指定
	ChatBuffer[ NowChatLine ].color = color;

	NowChatLine++;	// チャット改行
	// リミットチェック
	if( NowChatLine >= MAX_CHAT_LINE ) NowChatLine = 0;

	// 一行に收まらなかったら
	if( splitPoint != 0 ){
		// 空白分戾す
		//splitPoint -= 5;
		// 分割文字列作成
		//strncpy( str + splitPoint, "     ", 5 );
		// 次の行に送る文字列をチャット行に送りこむ（再归处理）
		//StockChatBufferLine( str + splitPoint, color );

		// 戾す文字数求める
		int len = strlen( CHAT_SPASE_STR );
		// 实际に戾す
		splitPoint -= len;
		strncpy( str + splitPoint, CHAT_SPASE_STR, len );
		// 次の行に送る文字列をチャット行に送りこむ（再归处理）
		StockChatBufferLine( str + splitPoint, color );
	}
	// スムーズスクロール座标
	//ChatLineSmoothY += 20;
	ChatLineSmoothY = CHAT_ZOUBUN_Y;

}

//******************************************************************************/
// チャット文字をバッファに溜める（一行）
//******************************************************************************/
//	引数：		char *str			：调べる文字列
//				unsigned char color	：文字の色
//				int fontKind		：文字の种类
//******************************************************************************/
void StockChatBufferLine( char *str, int color, int fontKind )
{
	int splitPoint = 0;
#ifndef PUK2_NEW_MENU
	char splitStr[ INPUT_STR_SIZE + 1 ];
#endif

	// フォントパレットリミットチェック
	if( color >= FONT_PAL_MAX ) return;
	// フォント种类リミットチェック
	if( fontKind >= FONT_KIND_MAX ) return;


#ifndef PUK2_NEW_MENU
	// 一行に收まらない时
	if( strlen( str ) > DispChatOneLineLen[ fontKind ] ){
		// 分割文字列作成
		strncpy( splitStr, str, DispChatOneLineLen[ fontKind ] );
		// 終端记号を入れる
		*( splitStr + DispChatOneLineLen[ fontKind ] ) = NULL;
		// 最后の全角文字が分割されている时
		if( GetStrLastByte( splitStr ) == 3 ){
			// 一文字戾す
			splitPoint = DispChatOneLineLen[ fontKind ] - 1;
			// 終端记号を入れる
			*( splitStr + DispChatOneLineLen[ fontKind ] - 1 ) = NULL;
		}
		else splitPoint = DispChatOneLineLen[ fontKind ];

		// 文字列をチャット行に送りこむ
		strncpy( ChatBuffer[ NowChatLine ].buffer, str, splitPoint );
		// 終端记号を入れる
		*( ChatBuffer[ NowChatLine ].buffer + splitPoint ) = NULL;
	}else{
		// 文字列をチャット行に送りこむ
		strcpy( ChatBuffer[ NowChatLine ].buffer, str );
	}
#else
		// 文字列をチャット行に送りこむ
		strcpy( ChatBuffer[ NowChatLine ].buffer, str );
#endif
	// チャットログファイルに出力
	if( chatLogFile )
	{

		struct tm *nowTime;
		time_t longTime;

		// 现在の日时を取得
		time( &longTime );
		nowTime = localtime( &longTime );

	#ifdef HTML_CHAT_LOG
		#ifndef PUK2
			int htmlFontSizeTbl[ 6 ] = { 3, 2, 4, 3, 2, 4 };
			char *htmlFontKind[] = { "宋体", "宋体" };                             //MLHIDE
			// フォントパレット
			DWORD htmlFontColorTbl[]={
				0xffffff, // 0:白
				0x00ffff, // 1:水色
				0xff00ff, // 2:紫
				0x0000ff, // 3:青
				0xffff00, // 4:黄
				0x00ff00, // 5:緑
				0xff0000, // 6:赤
				0xa0a0a4, // 7:灰色
				0xa6caf0, // 8:淡い青
				0xc0dcc0  // 9:淡い緑
			};
		#endif

		// ログインした日时记録
		#ifdef PUK2
			// ログインした日时记録
			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"4\" face=\"%s\">　%02d:%02d:%02d　", //MLHIDE
				( ( FontPal[color][0]<<16 )|( FontPal[color][1]<<8 )|( FontPal[color][2] ) ),
				exfontName[ FontKind[ fontKind ].fontnum ], nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );

			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"%d\" face=\"%s\"><b>%s<b></font><br>\n", //MLHIDE
				( ( FontPal[color][0]<<16 )|( FontPal[color][1]<<8 )|( FontPal[color][2] ) ),
				htmlFontSizeTbl[ fontKind ], exfontName[ FontKind[ fontKind ].fontnum ], ChatBuffer[ NowChatLine ].buffer );
		#else
			//fprintf( chatLogFile, "<font color=\"#FFFFFF\" size=\"4\" face=\"??????蔼">　%02d:%02d:%02d　",
			//fprintf( chatLogFile, "<font color=\"#FFFFFF\" size=\"4\" face=\"??????蔼">　%02d:%02d:%02d　",
			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"4\" face=\"%s\">　%02d:%02d:%02d　", //MLHIDE
				htmlFontColorTbl[ color ], htmlFontKind[ fontKind / 3 ], nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec );

			fprintf( chatLogFile, "<font color=\"#%06x\" size=\"%d\" face=\"%s\"><b>%s<b></font><br>\n", //MLHIDE
				htmlFontColorTbl[ color ], htmlFontSizeTbl[ fontKind ], htmlFontKind[ fontKind / 3 ], ChatBuffer[ NowChatLine ].buffer );
		#endif

//#ifdef _DEBUG
		// デバッグ版は每回フラッシュ
		fclose( chatLogFile );
		chatLogFile = fopen( path, "a" );                                   //MLHIDE
//		fflush( chatLogFile );
//#endif

	#else
		#ifdef PUK2
			fprintf( chatLogFile, " %02d:%02d:%02d　%s\n",                      //MLHIDE
				nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec, ChatBuffer[ NowChatLine ].buffer );
		#else
			fprintf( chatLogFile, " %02d:%02d:%02d　%s\n",                      //MLHIDE
				nowTime->tm_hour, nowTime->tm_min, nowTime->tm_sec, ChatBuffer[ NowChatLine ].buffer );
		#endif

//#ifdef _DEBUG
		// デバッグ版は每回フラッシュ
		fclose( chatLogFile );
		chatLogFile = fopen( path, "a" );                                   //MLHIDE
//		fflush( chatLogFile );
//#endif

	#endif

	}
	// 色の指定
	ChatBuffer[ NowChatLine ].color = color;
	// フォントの指定
	ChatBuffer[ NowChatLine ].fontKind = fontKind;
	// フラグＯＦＦ
	ChatBuffer[ NowChatLine ].mojiFlag = FALSE;

	NowChatLine++;	// チャット改行
	// リミットチェック
	if( NowChatLine >= MAX_CHAT_LINE ) NowChatLine = 0;

#ifdef PUK2_NEW_MENU
	//过去ログを参照できるように变更されたので、
	//参照してもいい数を作成します
	CountAddNewChatStockLine();

#endif

	// 一行に收まらなかったら
	if( splitPoint != 0 ){
		// 空白分戾す
		//splitPoint -= DispChatSpaceSize[ fontKind ];
		// 分割文字列作成
		//strncpy( str + splitPoint, "      ", DispChatSpaceSize[ fontKind ] );

		// 戾す文字数求める
		int len = strlen( CHAT_SPASE_STR );

		// 实际に戾す
		splitPoint -= len;
		strncpy( str + splitPoint, CHAT_SPASE_STR, len );
		// 次の行に送る文字列をチャット行に送りこむ（再归处理）
		StockChatBufferLine( str + splitPoint, color, fontKind );
	}
	// スムーズスクロール座标
	ChatLineSmoothY = 20;

}

/* チャット文字を全てクリアする ***********************************************/
void ClearChatBuffer( void )
{
	int i;

	// チャットの行数分ループ
	for( i = 0 ; i < MAX_CHAT_LINE ; i++ ){
		// 終端记号を入れる
		ChatBuffer[ i ].buffer[ 0 ] = NULL;
	}

#ifdef PUK2_NEW_MENU
	InitCountNewChatStockLine();
#endif

}

/* チャットバッファーをフォントバッファに溜める ********************************/
void ChatBufferToFontBuffer( void )
{
	int i, j, k = 0;
	int x = DISP_CHAT_X, y = DEF_APPSIZEY-80/*DISP_CHAT_Y*/; // 表示座标

#ifdef PUK2_NEW_MENU
	return;
#endif
	//static int smoothStr = 1;

	// 书き始める行の设定
	j = NowChatLine - 1;
	// リミットチェック
	if( j < 0 ) j = MAX_CHAT_LINE - 1;

	// スクロール中は一行余分に表示
	if( ChatLineSmoothY > 0 ) k = NowMaxChatLine + 1;
	else k = NowMaxChatLine;

	// リミットチェック
	if( k > MAX_CHAT_LINE ) k = MAX_CHAT_LINE;

	// チャットの行数分ループ
	for( i = 0 ; i < k ; i++ ){
	//for( i = 0 ; i < NowMaxChatLine; i++ ){
#ifdef PUK2
		if (y+ ChatLineSmoothY + DispChatCenterY[ ChatBuffer[ j ].fontKind ]<0) break;
#endif
		int work = j - 1;
		if( work < 0 ) work = MAX_CHAT_LINE - 1;
		// 何か文字があったら
		if( *ChatBuffer[ j ].buffer != NULL /*&& ( ChatBuffer[ work ].mojiFlag == TRUE || *ChatBuffer[ work ].buffer == NULL )*/ ){
#if 0
/*杉
			// 文字を最后まで表示したかフラグがＯＦＦの时
			if( ChatBuffer[ j ].mojiFlag == FALSE ){
				char moji[ INPUT_STR_SIZE + 1 ];
				// 文字进める
				ChatBuffer[ j ].mojiCnt += GetCharByte( ChatBuffer[ j ].buffer[ ChatBuffer[ j ].mojiCnt ] );
				// 今回の文字列作成
				strncpy( moji, ChatBuffer[ j ].buffer, ChatBuffer[ j ].mojiCnt );
				// 終端记号を入れる
				moji[ ChatBuffer[ j ].mojiCnt ] = NULL;
				// リミットチェック
				if( ( unsigned )ChatBuffer[ j ].mojiCnt >= strlen( ChatBuffer[ j ].buffer ) ) ChatBuffer[ j ].mojiFlag = TRUE;
				// フォントバッファへ溜める
				StockFontBuffer( x, y + ChatLineSmoothY + DispChatCenterY[ ChatBuffer[ j ].fontKind ], FONT_PRIO_BACK, ChatBuffer[ j ].fontKind, ChatBuffer[ j ].color, ( char *)moji, 0 );
				//smoothStr = ChatLineSmoothY * 32;
				// 文字扩大
				//if( ChatLineSmoothY > 14 ) smoothStr = 2;
				//if( ChatLineSmoothY > 16 ) smoothStr = 0;
				//if( ChatLineSmoothY > 18 ) smoothStr = 1;
			}else{
*/
#endif
				// 一时表示ＯＦＦじゃない时、フォントバッファへ溜める
				//杉）ログ表示
				if( ChatHideFlag == FALSE ) StockFontBuffer( x, y + ChatLineSmoothY + DispChatCenterY[ ChatBuffer[ j ].fontKind ], FONT_PRIO_BACK, ChatBuffer[ j ].fontKind, ChatBuffer[ j ].color, ( char *)ChatBuffer[ j ].buffer, 0 );
//			}
		}
		y -= CHAT_ZOUBUN_Y;  // 座标移动
#ifdef PUK2
		if (DispChatCenterY[ ChatBuffer[ j ].fontKind ]<0) y+=DispChatCenterY[ ChatBuffer[ j ].fontKind ];  // 座标移动
#endif

		// 次のバッファ]
		j--;
		// リミットチェック
		if( j < 0 ) j = MAX_CHAT_LINE - 1;
	}
	// スムーズスクロールの时
	if( ChatLineSmoothY > 0 ) ChatLineSmoothY--;

	// 何か文字があったら
	//if( *MyChatBuffer.buffer != NULL ){
		// 自分の入力をフォントバッファへ溜める
		//杉）自分の入力
			StockFontBuffer2( &MyChatBuffer );
		//StockFontBuffer( MyChatBuffer.x, MyChatBuffer.y, FONT_PRIO_BACK, MyChatBuffer.color, ( char *)MyChatBuffer.buffer, 0 );
	//}

}

//******************************************************************************/
// 文字列の最后が半角か全角かを调べる
//******************************************************************************/
//	引数：		char *str	：调べる文字列
// 	戾り值：　	半角：１
// 				全角：２
// 				例外：３
//******************************************************************************/
int GetStrLastByte( char *str )
{
	int byte = 0;

	// 文字列の最后までループ
	while( !( *str == NULL ) ){
	//		( *str == ( char )0x81 && *( str + 1 ) == ( char )0x51 ) ) ){

		// 全角のとき
		if( DISP_CHINESE_FLAG && (( char )0x81 <= *str && *str <= ( char )0xfe) ){

			// 全角の文字が分割されている时
			if( *( str + 1 ) == NULL ) return 3; // 例外

			str += 2; 	// ２バイト飞ばす
			byte = 2;	// 全角です

		}else{	// 半角のとき

			str ++; 	// １バイト飞ばす
			byte = 1;	// 半角です
		}
	}
	return byte;
}
//******************************************************************************/
// 文字列の最后が半角か全角かを调べる
//******************************************************************************/
//	引数：		char *str	：调べる文字列
//				int len		：文字列の何文字目を调べるか。
// 	戾り值：　	半角：１
// 				全角：２
// 				例外：３
//******************************************************************************/
int GetStrLastByte( char *str, int len )
{
	int byte = 0;
	int i;
	char work[ INPUT_STR_SIZE ];
	char *str2;

	// 文字列コピー
	strncpy( work, str, len );
	// 終端记号
	work[ len ] = NULL;
	str2 = work;

	// 文字列の长さ分ループ
	for( i = 0 ; i < len ; i++ ){

		// リミットチェック
		if( *str2 == NULL ) break;
	//		( *str2 == ( char )0x81 && *( str2 + 1 ) == ( char )0x51 ) ) ){

		// 全角のとき
		if( DISP_CHINESE_FLAG && (( char )0x81 <= *str2 && *str2 <= ( char )0xfe) ){

			// 全角の文字が分割されている时
			if( *( str2 + 1 ) == NULL ) return 3; // 例外

			str2 += 2; 	// ２バイト飞ばす
			byte = 2;	// 全角です
			i++; 		// ２バイト飞ばす

		}else{	// 半角のとき

			str2 ++; 	// １バイト飞ばす
			byte = 1;	// 半角です
		}
	}
	return byte;
}

//******************************************************************************/
// 文字が半角か全角かを调べる
//******************************************************************************/
//	引数：		char c	：调べる文字
// 	戾り值　	半角：１
// 				全角：２
//******************************************************************************/
int GetCharByte( char c )
{
	// 全角のとき
	if( DISP_CHINESE_FLAG &&  (( char )0x81 <= c && c <= ( char )0xfe) ){

		return 2;	// 全角です

	}else{	// 半角のとき

		return  1;	// 半角です
	}
}

//******************************************************************************/
// 文字列の横幅（ドット数）を调べる
/*******************************************************************************/
//	引数：	char *str	：调べる文字列
// 	戾り值：横幅ドット数
//******************************************************************************/
int GetStrWidth( char *str )
{
	int width = 0;

	// 文字列の最后までループ
	while( !( *str == NULL ) ){

		// 全角のとき
		//修复字符光标位置问题
		//if( DISP_CHINESE_FLAG && (( char )0x81 <= *str && *str <= ( char )0xfe) ){
		if ( *str < 0 ) {

			str += 2; 	// ２バイト飞ばす
			width += FontKind[ FONT_KIND_MIDDLE ].zenkakuWidth; // 全角サイズを足す

		}else{	// 半角のとき

			str ++; 	// １バイト飞ばす
			width += FontKind[ FONT_KIND_MIDDLE ].hankakuWidth; // 半角サイズを足す
		}
	}

	return width;
}

/*******************************************************************************/
// 文字列の横幅（ドット数）を调べる
/*******************************************************************************/
//	引数：	char *str	：调べる文字列
//			int kind	：フォントの种类
// 	戾り值：横幅ドット数
/*******************************************************************************/
int GetStrWidth( char *str ,int kind )
{
	int width = 0;

	// 文字列の最后までループ
	while( !( *str == NULL ) ){

		// 全角のとき
		//修复字符光标位置问题
		//if( DISP_CHINESE_FLAG && (( char )0x81 <= *str && *str <= ( char )0xfe) ){
		if (*str < 0) {

			str += 2; 	// ２バイト飞ばす
			width += FontKind[ kind ].zenkakuWidth; // 全角サイズを足す

		}else{	// 半角のとき

			str ++; 	// １バイト飞ばす
			width += FontKind[ kind ].hankakuWidth; // 半角サイズを足す
		}
	}

	return width;
}

/*******************************************************************************/
// 文字列の横幅（ドット数）を调べる
/*******************************************************************************/
//	引数：	char *str	：调べる文字列
//			int kind	：フォントの周类
//			int len		：文字列の何文字目まで调べるか。
// 	戾り值：横幅ドット数
/*******************************************************************************/
int GetStrWidth( char *str, int kind, int len )
{
	int width = 0;
	int i;

	// 文字列の最后までループ
	for( i = 0 ; i < len ; i++ ){

		// リミットチェック
		if( *str == NULL ) break;

		// 全角のとき
		//修复字符光标位置问题
		//if( DISP_CHINESE_FLAG && (( char )0x81 <= *str && *str <= ( char )0xfe) ){
		if (*str < 0) {

			str += 2; 	// ２バイト飞ばす
			i++;		// ２バイト飞ばす

			width += FontKind[ kind ].zenkakuWidth; // 全角サイズを足す

		}else{	// 半角のとき

			str ++; 	// １バイト飞ばす
			width += FontKind[ kind ].hankakuWidth; // 半角サイズを足す
		}

	}

	return width;
}

/*******************************************************************************/
/* 入力フォーカス取得
/*******************************************************************************/
void GetKeyInputFocus( INPUT_STR *pInputStr )
{

	// 入力场所がある时
	//if( pNowInputStr != NULL ){
		// カーソルが残っている时
	//	if(	pNowInputStr->buffer[ pNowInputStr->cnt ] != '\0' ){
			// 終端记号を入れる
	//		pNowInputStr->buffer[ pNowInputStr->cnt ] = '\0';
	//	}
	//}
	// ＩＭＥバッファーを初期化
	//*ImeBuffer = '\0';
	// 入力バッファポインタを变更
	pNowInputStr = pInputStr;
}

// クリップボードから入力バッファにコピー ********************************************/
void GetClipboad( void )
{
	HGLOBAL hMem;
	LPTSTR lpMem;

	/* クリップボードをオープン */
	OpenClipboard( hWnd );
	/* クリップボードから文字列のメモリオブジェクト取得 */
	hMem = GetClipboardData( CF_TEXT );
	/* テキストがなければ中止 */
	if( hMem == NULL ){
		CloseClipboard();
		return;
	}
	/* メモリオブジェクトをロック */
	lpMem = (LPTSTR)GlobalLock( hMem );
	/* エディタにロックしたバッファの内容を设定 */
//	SetWindowText( hwStrE, lpMem );
//	lstrcpy( chat_input_buf, lpMem );

	// 文字列を现在の入力文字列に送る
	StrToNowInputStr( lpMem );

	/* バッファをアンロック */
	GlobalUnlock( hMem );
	CloseClipboard();
}

// クリップボードにコピー ******************************************************/
void SetClipboad( void )
{
	HGLOBAL hMem;
	LPTSTR lpMem;

	// 入力场所がない时返回
	if( pNowInputStr == NULL ) return;
	// 文字が无いとき返回
	if( pNowInputStr->buffer[ 0 ] == NULL ) return;

	/* メモリオブジェクト确保 */
	hMem = GlobalAlloc( GHND, 512 );
	/* メモリオブジェクトをロックしてアドレスを取得 */
	lpMem = ( LPTSTR )GlobalLock( hMem );
//	lstrcpy( lpMem, chat_input_buf );
	/* メモリオブジェクトに文字列を书き込む */
	lstrcpy( lpMem, pNowInputStr->buffer );
	/* メモリオブジェクトをアンロック */
	GlobalUnlock( hMem );
	/* クリップボードをオープン */
	OpenClipboard( hWnd );
	/* クリップボードをクリア */
	EmptyClipboard();
	/* クリップボードにメモリオブジェクトの文字列をコピー */
	SetClipboardData( CF_TEXT, hMem );
	/* クリップボードをクローズ */
	CloseClipboard();
	/* メモリオブジェクト开放 */
	//GlobalFree( hMem );
}


// チャット登録文字列を入力バッファに送るショートカットキーの处理
void shortCutSendChatReg( void )
{
	int keyDim[] =
	{
		VK_F1,
		VK_F2,
		VK_F3,
		VK_F4,
		VK_F5,
		VK_F6,
		VK_F7,
		VK_F8,
	};
	int keyDimSize = sizeof( keyDim )/sizeof( keyDim[0] );
	int no = -1;
	int i;


	// SHIFT押されてる时
	if( (VK[VK_SHIFT] & KEY_ON) )
	{
		for( i = 0; i < keyDimSize; i++ )
		{
			if( keyOnRep( keyDim[i] ) )
			{
				no = i + 16;
			}
		}
	}
	else
	// CTRL押されてる时
	if( (VK[VK_CONTROL] & KEY_ON) )
	{
		for( i = 0; i < keyDimSize; i++ )
		{
			if( keyOnRep( keyDim[i] ) )
			{
				no = i + 8;
			}
		}
	}
	else
	{
		for( i = 0; i < keyDimSize; i++ )
		{
			if( keyOnRep( keyDim[i] ) )
			{
				no = i;
			}
		}
	}

	if( no >= 0 )
	{
		// 登録文字列を入力バッファに送る
		StrToNowInputStr( chatRegStr[no].buffer );
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
}

#ifdef PUK2

char CheckLetterType( char *str, short byte )
{
	unsigned char *u = (unsigned char *)str;
	int i;

	for(i=0;i<byte;){
		//文字の种类调查*/
		//if ( ((u[i]>=0x80) ) i+=2;
		if ( (0x81<=u[i])&&(u[i]<=0xfe) ) i+=2;
		//else if ( (0xe0<=u[i])&&(u[i]<=0xfc) ) i+=2;
		else i++;
	}
	if (i!=byte) return CHECKLETTERTYPE_FULL_TAIL;

	//if ( (0x81<=u[i])&&(u[i]<=0x9f) ) return CHECKLETTERTYPE_FULL_TOP;
	//if ( (0xe0<=u[i])&&(u[i]<=0xfc) ) return CHECKLETTERTYPE_FULL_TOP;

	return CHECKLETTERTYPE_HALF;
}

//--------------------------------------
// 文字检索(日本语对应型)
//--------------------------------------
// 引数
//	string			检索对象の文字列
//	c				检索する文字、文字列として指定、文字列最初の文字を检索する
//
// 戾り值
//	char *			!= NULL	---- 搜索文字が最初に见つかった位置を示すポインタ
//					== NULL	---- 搜索文字が见つからなかった
//
char *jstrchr( const char *string, const char *c )
{
	unsigned char *ret;
	char xc[2];
	char add;

	// 检索对象文字列の准备
	ret = (unsigned char *)string;
	add = 0;

	// 搜索文字の设定
	xc[0] = c[0];
	xc[1] = '\0';
	// 全角文字なら
	if ( (0x81<=c[0]) && (c[0]<=0xfe) ) xc[1] = c[1];
	//else if ( (0xe0<=c[0]) && (c[0]<=0xfc) ) xc[1] = c[1];

	// 检索する文字が半角なら
	if ( xc[1] == '\0' ){
		// 終端文字がくるまでループ
		for(;ret[0]!='\0';ret++){
			// 前のバイトが全角１バイト目なら
			if (add){ add--;	continue; }

			//全角文字なら
			if ( (0x81<=ret[0]) && (ret[0]<=0xfe) ) add++;
			//else if ( (0xe0<=ret[0]) && (ret[0]<=0xfc) ) add++;
			// 半角文字なら
			else{
				// 见つかったら終了
				if ( ret[0] == xc[0] ) return (char *)ret;
			}
		}
	}
	// 检索する文字が全角なら
	else{
		// 終端文字がくるまでループ
		for(;ret[0]!='\0';ret++){
			// 前のバイトが全角１バイト目なら
			if (add){
				// 见つかったら終了
				if ( (ret[-1]==xc[0]) && (ret[0]==xc[1]) ) return (char*)ret;
				add--;
				continue;
			}

			//全角文字なら
			if ( (0x81<=ret[0]) && (ret[0]<=0xfe) ) add++;
			//else if ( (0xe0<=ret[0]) && (ret[0]<=0xfc) ) add++;
		}
	}

	// 见つからなかったのでNULLを返す
	return NULL;
}

#endif

