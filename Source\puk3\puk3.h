﻿#define PUK3

#ifdef PUK3

	#define BATTLE_INTERVAL 22

	// ＰＵＫ３ｂｉｎ
	#define PUK3_BIN

	// ライドデータ
	#define PUK3_RIDEBIN

	// キャラの行动
	#define PUK3_CHARA_ACT

	// 被り物
	#define PUK3_PUT_ON

	// ペットライド
	#define PUK3_RIDE

	// アイコン表示用アクションポインタ变数变更
	#define PUK3_CHANGE_ICONACTIONPOINTER

	// マウスカーソル变更
	#define PUK3_MOUSECURSOR

	// 新ルート计算
	#define PUK3_GET_ROUTE

	// アップグレード
	#define PUK3_UPGRADE

	// 战闘中ライド
	#define PUK3_RIDE_BATTLE

	// 战闘中ライドの小修正
	#define PUK3_RIDE_BATTLE_2

	// 鲸船
	#define PUK3_WHALE_SHIP

	//名刺扩张（プロフィール）
	#define PUK3_PROF

	//ＰＵＫ３の音关系
	#define PUK3_SOUND

	//ＰＵＫ３のアカウント关系
	#define PUK3_ACCOUNT

	// ペットスキルショップ扩张
	#define PUK3_PETSKILLSHOPEX

	// 战闘时行动无い行动の待ち时间をなくす
	#define PUK3_BATTLE_NOWAIT

	// カウンター受けたとき返回の遅いの修正
	#define PUK3_COUNTER_SLOW

	// カブリモノ坏れ对应
	#define PUK3_WEPON_BREAK

	// リ??バースフラグ对应
	#define PUK3_REBIRTHFLAG

	// ＰＵＫ３用デュエル背景
	#define PUK3_BATTLEMAP

	// 鲸船がＰＣがワープ直后步こうとして表示がおかしくなるバグ
	#define PUK3_WHALE_SHIP_WARP

	// スキルがマックスまで等级アップしたとき表示がない
	#define PUK3_SKILLMAXLVUP

	// 乘り物
	#define PUK3_VEHICLE

	// 战闘突入时に空が表示される
	#define PUK3_ENCOUNT_NULL_GRA

	//登出时にプロフィール保持
	#define PUK3_PROF_START_SEND

	// サーバー接続时にクライアントバージョンの整合性チェック
	#define PUK3_LOGIN_VERCHECK

	// 战闘終了と同时に登出での不具合
	#define PUK3_BATTLEEND_LOGOUT

	// 鲸船でデュエルしているときに接岸、离岸の时间がくると栈桥が表示される
	#define PUK3_WHALESHIP_DUEL

	// ライドのペットのアクションで音を鸣らさない
	#define PUK3_RIDE_SOUND

	// Ｒ１０效果
	#define PUK3_R10
	#ifdef PUK3_R10
		#define PUK3_R10_KAIKYOUKAISOU
		#define PUK3_R10_ICHIGEKIHICCHU
		#define PUK3_R10_DOKUGEKI
		#define PUK3_R10_ISSEKINICHO
		#define PUK3_R10_JINNSOKUKADANN
		#define PUK3_R10_INNGAOHO
		#define PUK3_R10_COINSHOT
	#endif

	// サーバー接続时にクライアントバージョンの整合性チェックのバグ
	#define PUK3_LOGIN_VERCHECK_BAG

	// 战闘开始时の设定部分での落ちバグ修正
	#define PUK3_CMD_END_SET

	// 文字变换中にウィンドウを切り替えると、
	// 普通のアプリケーションで变换するときに表示されるウィンドウが表示される
	#define PUK3_WINCHNG_IMEWINDOW

	// ＡＴＯＫでＩＭＥのＯＮ/ＯＦＦがうまくいかない
	#define PUK3_IME_ATOK

	// 盗むの分捕る化
	#define PUK3_STEAL_ATTACK

	// ワープによるラグによる不具合
	#define PUK3_WARP_RAG

	// 布ティー入出による不具合
	#define PUK3_PARTY_INOUT

	// ラグでワープするときの不具合
	#define PUK3_RAG_WARP

	// 布ティーの中のやつが调べるをしたときの不具合
	#define PUK3_CHECK_IN_PARTY

	// ログインしたとき左上を向いてる
	#define PUK3_LOGIN_DIR_0

	// 盗むの成否が表示されない
	#define PUK3_STEAL_SF_NOSHOW

	// メール不具合色々
	#define PUK3_MAIL_ETC

	// 状态ウィンドウを开いたときの位置
	#define PUK3_WINDOW_OPEN_POINT

	// メール不具合色々
	#define PUK3_MAIL_ETC2

	// ＭＭ内存连接
	#define PUK3_MM_MEMLEAK

	// 公会宠物饵箱に入れられないアイテムが入れられてしまうバグ
	#define PUK3_FOODBOX

	// ウィンドウモードに切り替えたときに画面が黒いままになる
	#define PUK3_WINDOWMODE_BLACKOUT

	// 分捕るカウンターエフェクト连激
	#define PUK3_COUNTER_EFFECT

	// 银行のペットスロット１０のスキルが表示されない
	#define PUK3_BANK_PET_SKILL10

	// フリアの影战で、ＢＧＭが鸣らない
	#define PUK3_BGM_SILENCE

	// リ??バースの落ちバグ？
	#define PUK3_REBIRTH_BAG
	#define PUK3_REBIRTH_BAG2

	// 战闘ＢＧＭ追加
	#define PUK3_NEWBATTLEBGM

	// 画面切り替えが遅い
	#define PUK3_SCENE_CHANGE

	// 観战前にリ??バースしたキャラが死亡したときのバグ
	#define PUK3_REBIRTH_DEATH_HALFWAY

	// 新闘技场
	#define PUK3_NEWARENA

	// 新曲追加
	#define PUK3_NEWMUSIC

	// 因果应报の处理の修正
	#define PUK3_INNGAOHO_PROC

	// 飞び道具でリバースエフェクト时にクリティカル
	#define PUK3_REBIRTH_EFFECT_CRITICAL

	// 接続切れ中の移动による不具合
	#define PUK3_CONNDOWNWALK

	// ＩＤおらんエラー时の对处
	#define PUK3_NOIDERROR

	// 突风の戾りが遅い
	#define PUK3_TOPPUSLOW

	// 気絶中にスキルアップ
	#define PUK3_DEAD_SKILLUP

	// 自分に突风で落ち
	#define PUK3_TOPPU_TO_ME2

	// 羊头狗肉中に死亡でフリーズ
	#define PUK3_YOTOKUNIKU_DEATH_STOP

	// カウンターで反射死亡
	#define PUK3_COUNTER_REFLECT

	// 味方プレイヤーのみターゲット
	#define PUK3_TARGET_FRIEND_PLAYER

	// ＢＧＭのデータ参照不具合
	#define PUK3_BGM_REFER

	// サウンド周りチェック强化
	#define PUK3_SOUND_CHECK_VALUE

	// カウンター后元の位置返回の不具合
	#define PUK3_COUNTER_MOVEDEF

	// サーバーとの接続が切れたときのエラー番号表示
	#define PUK3_CONNECTERROR

	// 特定キャラが、アイテムを银行からダブルクリックで取り出せない
	#define PUK3_BANK_DBLCLICK

	// ワープ演出のメモリ破坏バグ
	#define PUK3_PRODUCE_MEMDESTORY

	// アイテムロック不具合
	#define PUK3_ITEM_LOCK


	// アクションの不正参照
	#define PUK3_ACTION_REF_ERROR_MON
	#define PUK3_ACTION_REF_ERROR_BOMB
	#define PUK3_ACTION_REF_ERROR_INNGA

	// 不正ＩＤでの参照
	#define PUK3_PACTBC_ID_ERROR_REB
	#define PUK3_PACTBC_ID_ERROR_TARGET_NULL
	#define PUK3_PACTBC_ID_ERROR_DRAIN_ME

	// モンスターお手伝い
	#define PUK3_MONSTER_HELPER
	#define PUK3_MONSTER_HELPER_CANCEL
	#define PUK3_MONSTER_HELPER_MMLOCK

	// サーバー接続时にクライアントバージョンの整合性チェックのバグ
	#define PUK3_LOGIN_VERCHECK_BAG2

	// 反射后变な方向を向く
	#define PUK3_REFLECT_DIR

	// 吹っ飞んだときにおかしな文字列が表示される
	#define PUK3_ULTIMATE_CHAT

	// メモリ解放忘れ
	#define PUK3_NOTFREE_GRAPHICBIN
	#define PUK3_NOTFREE_ANIMEBINBUF
	#define PUK3_NOTFREE_IME
	#define PUK3_NOTFREE_MOUSE
	#define PUK3_NOTFREE_ANIMEDATA
	#define PUK3_NOTFREE_CHAREX
	#define PUK3_NOTFREE_WINDDOW
	#define PUK3_NOTFREE_GENERALWINDDOW

	// リ??バース无敌时体力变更禁止
	#define PUK3_RIBIRTH_GUARD_NODAMAGE

	// サイブラスト表示位置计算修正
	#define PUK3_PSYBLAST_VANISH

	// 等级ダウン时　体力魔力更新
	#define PUK3_LVDOWN_LPFP_UPDATA

	// リ??バース无敌时の虚袭栗心不具合
	#define PUK3_REBIRTHGUARD_FORCECUT



	// 治疗等の魔力不足の表示
	#define PUK3_CURE_FP_LUCK

	// ウィンドウサイズがおかしい
	#define PUK3_WINDOW_SIZE_DIFF

	// バトル停止
	#define PUK3_BATTLEACT_STOP
	#ifdef PUK3_BATTLEACT_STOP
		// 战闘停止后、その行动を强制終了するまでの时间
		#define BATTLEACT_STOP_INTERVAL 2*60*60
	#endif

	// 新スキル
	#define PUK3_NEWSKILL
	#ifdef PUK3_NEWSKILL
		#define PUK3_NEWSKILL_BREAK				// ブレイク
		#define PUK3_NEWSKILL_LVDOWN			// 等级ダウン
		#define PUK3_NEWSKILL_PSYBLAST			// サイブラスト
	#endif



//======================================
// 长期のプログラム
#ifdef _CGL

	// デバッグ用サイブラストで战闘停止
//	#define PUK3_PSYBLAST_BATTLESTOP

	// 新スキル
////	#define PUK3_NEWSKILL
	#ifdef PUK3_NEWSKILL
////		#define PUK3_NEWSKILL_BREAK				// ブレイク
////		#define PUK3_NEWSKILL_COINSHOT			// コインショット

////		#define PUK3_NEWSKILL_PSYBLAST			// サイブラスト

////		#define PUK3_NEWSKILL_LVDOWN			// 等级ダウン
	#endif



	//======================================
	// 超长期のプログラム
	#ifdef _CGXL

		// ペットアイテム预け
		#define PUK3_PET_BANK

		// ラグ最中の移动中の解散
//		#define PUK3_RAG_PARTY_BREAK_UP_1	// 目的地まで步いて移动
//		#define PUK3_RAG_PARTY_BREAK_UP_2	// 目的地までワープして移动

		// パブコノハナサクヤで観战
		#define PUK3_PAB_WATCH

		// 盗むの分捕る化で、行动チェックのところに不备
		#define PUK3_STEAL_ATTACK_CS

		// エラー番号表示
		#define PUK3_ERRORMESSAGE_NUM

		// 存在しないキャラのアクション
//		#define PUK3_NOEXISTCHARA

		// チェック强化
		#define PUK3_CHECK_VALUE

		// 新スキル
		#define PUK3_NEWSKILL
		#ifdef PUK3_NEWSKILL
////			#define PUK3_NEWSKILL_BREAK				// ブレイク
			#define PUK3_NEWSKILL_COINSHOT			// コインショット
////			#define PUK3_NEWSKILL_PSYBLAST			// サイブラスト

			#define PUK3_NEWSKILL_JUMP				// ジャンプ
		#endif


		#if defined _DEBUG
			//MAPヒット表示
			#define HIT_VIEW
			#define PUK3_OLDMAP_HIT_VIEW
		#endif

	#endif
#endif
#ifdef _DEBUG
	// セーブデータ每にrecvdata作成
	#define PUK3_RECVDATA

	// バトルチェック时、コマンド入力の时少し待つ
	#define PUK3_BATTLE_CMDRECV
	#define BATTLECHECK_CMDRECV_INTERVAL 10

	// デバッグ时も普通のチャットログを残すようにする
	#define PUK3_USE_NOMALCHATLOG

	// アニメービューで表示を移动できるようにする
	#define PUK3_ANIMVIEW_MOVECHARA

	// バトルチェック背景の设定
	#define PUK3_BATTLE_BG

	// フィールド时キャラリスト表示
	#define PUK3_CHARALIST

	// プロトコル受信时间
	#define PUK3_PROTO_RECVTIME

	// バトルチェック属性表示
	#define PUK3_PROC_BATTLE_ELM

	// サーバー选择からバトルチェック
	#define PUK3_BATTLECHECK_SERVER

	// 内存连接チェック
//	#define PUK3_MEMORYLEAK

	// メモリ确保关数
	#define PUK3_ALLOC

	// exeの种类をログに吐き出し
	#define PUK3_EXETYPE_LOG

	// メモリ破坏の对应
	#define PUK3_SEGMENTATION_FAULT

	// ACTIONチェック用チェックサム
//	#define PUK3_ACTION_CHECKSUM

	// ACTION参照アドレス范围チェック
//	#define PUK3_ACTION_CHECKRANGE

	// pActBc参照范围チェック
	#define PUK3_PACTBC_CHECKRANGE

	// メモリ确保量制限
	#define PUK3_MEMALLOC_LIMIT

	// メモリログ
//	#define PUK3_MEMALLOCLOG

	// 超长期のプログラム
	#ifdef _CGXL
		// 故意にラグ発生
		#define PUK3_RAG_MAKE
	#endif

	#ifdef _NOWMAKING
		// グラフ描画
		#define PUK3_GRAPH
	#endif
#endif

#endif
