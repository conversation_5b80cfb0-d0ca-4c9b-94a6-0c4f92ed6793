﻿//メニュー＞モンスター

#ifndef _MENUMONSTER_H_
#define _MENUMONSTER_H_

void InitMenuWindowMonsterInLogin(void);
BOOL MenuWindowMonster( int mouse );
BOOL MenuWindowMonsterDraw( int mouse );
BOOL MenuSwitchMonsterSWitch( int no, unsigned int flag );
void SetBattleStatus(void);
void SendBattleStatus(void);

GRAPHIC_SWITCH MenuWindowMonsterGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_MonsterWindow,0,0,0,0,0xFFFFFFFF},		//ベース
	{GID_MonsterBack,0,0,0,0,0x80FFFFFF},		//背景

	{GID_MonsterListPanelOn,0,0,0,0,0xFFFFFFFF},	//枠

	{GID_MonsterStatusEarthLeft,0,0,0,0,0xFFFFFFFF},		//土属性左
	{GID_MonsterStatusEarthCenter,0,0,0,0,0xFFFFFFFF},		//土属性中
	{GID_MonsterStatusEarthRight,0,0,0,0,0xFFFFFFFF},		//土属性右
	{GID_MonsterStatusEarthOnly,0,0,0,0,0xFFFFFFFF},		//土属性１

	{GID_MonsterStatusWaterLeft,0,0,0,0,0xFFFFFFFF},		//水属性左
	{GID_MonsterStatusWaterCenter,0,0,0,0,0xFFFFFFFF},		//水属性中
	{GID_MonsterStatusWaterRight,0,0,0,0,0xFFFFFFFF},		//水属性右
	{GID_MonsterStatusWaterOnly,0,0,0,0,0xFFFFFFFF},		//水属性１

	{GID_MonsterStatusFireLeft,0,0,0,0,0xFFFFFFFF},			//火属性左
	{GID_MonsterStatusFireCenter,0,0,0,0,0xFFFFFFFF},		//火属性中
	{GID_MonsterStatusFireRight,0,0,0,0,0xFFFFFFFF},		//火属性右
	{GID_MonsterStatusFireOnly,0,0,0,0,0xFFFFFFFF},			//火属性１

	{GID_MonsterStatusWindLeft,0,0,0,0,0xFFFFFFFF},			//风属性左
	{GID_MonsterStatusWindCenter,0,0,0,0,0xFFFFFFFF},		//风属性中
	{GID_MonsterStatusWindRight,0,0,0,0,0xFFFFFFFF},		//风属性右
	{GID_MonsterStatusWindOnly,0,0,0,0,0xFFFFFFFF},			//风属性１

	{GID_MonsterWalkOn,0,0,0,0,0xFFFFFFFF},					//Walk
	{GID_MonsterStandByOn,0,0,0,0,0xFFFFFFFF},				//StandBy
	{GID_MonsterBattleOn,0,0,0,0,0xFFFFFFFF},				//Battle
	{GID_MonsterFieldOn,0,0,0,0,0xFFFFFFFF},				//Field

};


BUTTON_SWITCH MenuWindowMonsterButton[]={
	{0},									//状态
};

TEXT_SWITCH MenuWindowMonsterText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(831, "怪物名字")},	//名称
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"300"},	//等级                       //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"100000/100000"},	//体力             //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"100000/100000"},	//魔力             //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(832, "种族")},	//种族
};

// スイッチ
static SWITCH_DATA MonsterSwitch[] = {
{ SWITCH_GRAPHIC,357-55,  9-1,  11, 11, TRUE, &MenuWindowMonsterGraph[ 0], MenuSwitchCloseButton },			//クローズボタン

{ SWITCH_TEXT	  ,  46-23, 50+48*0-16,   0,  0, TRUE, &MenuWindowMonsterText[0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 179-23, 50+48*0-16,   0,  0, TRUE, &MenuWindowMonsterText[1], MenuSwitchNone },	//等级
{ SWITCH_TEXT	  ,  75-23, 63+48*0-16,   0,  0, TRUE, &MenuWindowMonsterText[2], MenuSwitchNone },	//体力
{ SWITCH_TEXT	  ,  75-23, 76+48*0-16,   0,  0, TRUE, &MenuWindowMonsterText[3], MenuSwitchNone },	//魔力
{ SWITCH_TEXT	  , 156-23, 65+48*0-16,   0,  0, TRUE, &MenuWindowMonsterText[4], MenuSwitchNone },	//种族

{ SWITCH_TEXT	  ,  46-23, 50+48*1-16,   0,  0, TRUE, &MenuWindowMonsterText[0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 179-23, 50+48*1-16,   0,  0, TRUE, &MenuWindowMonsterText[1], MenuSwitchNone },	//等级
{ SWITCH_TEXT	  ,  75-23, 63+48*1-16,   0,  0, TRUE, &MenuWindowMonsterText[2], MenuSwitchNone },	//体力
{ SWITCH_TEXT	  ,  75-23, 76+48*1-16,   0,  0, TRUE, &MenuWindowMonsterText[3], MenuSwitchNone },	//魔力
{ SWITCH_TEXT	  , 156-23, 65+48*1-16,   0,  0, TRUE, &MenuWindowMonsterText[4], MenuSwitchNone },	//种族

{ SWITCH_TEXT	  ,  46-23, 50+48*2-16,   0,  0, TRUE, &MenuWindowMonsterText[0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 179-23, 50+48*2-16,   0,  0, TRUE, &MenuWindowMonsterText[1], MenuSwitchNone },	//等级
{ SWITCH_TEXT	  ,  75-23, 63+48*2-16,   0,  0, TRUE, &MenuWindowMonsterText[2], MenuSwitchNone },	//体力
{ SWITCH_TEXT	  ,  75-23, 76+48*2-16,   0,  0, TRUE, &MenuWindowMonsterText[3], MenuSwitchNone },	//魔力
{ SWITCH_TEXT	  , 156-23, 65+48*2-16,   0,  0, TRUE, &MenuWindowMonsterText[4], MenuSwitchNone },	//种族

{ SWITCH_TEXT	  ,  46-23, 50+48*3-16,   0,  0, TRUE, &MenuWindowMonsterText[0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 179-23, 50+48*3-16,   0,  0, TRUE, &MenuWindowMonsterText[1], MenuSwitchNone },	//等级
{ SWITCH_TEXT	  ,  75-23, 63+48*3-16,   0,  0, TRUE, &MenuWindowMonsterText[2], MenuSwitchNone },	//体力
{ SWITCH_TEXT	  ,  75-23, 76+48*3-16,   0,  0, TRUE, &MenuWindowMonsterText[3], MenuSwitchNone },	//魔力
{ SWITCH_TEXT	  , 156-23, 65+48*3-16,   0,  0, TRUE, &MenuWindowMonsterText[4], MenuSwitchNone },	//种族

{ SWITCH_TEXT	  ,  46-23, 50+48*4-16,   0,  0, TRUE, &MenuWindowMonsterText[0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 179-23, 50+48*4-16,   0,  0, TRUE, &MenuWindowMonsterText[1], MenuSwitchNone },	//等级
{ SWITCH_TEXT	  ,  75-23, 63+48*4-16,   0,  0, TRUE, &MenuWindowMonsterText[2], MenuSwitchNone },	//体力
{ SWITCH_TEXT	  ,  75-23, 76+48*4-16,   0,  0, TRUE, &MenuWindowMonsterText[3], MenuSwitchNone },	//魔力
{ SWITCH_TEXT	  , 156-23, 65+48*4-16,   0,  0, TRUE, &MenuWindowMonsterText[4], MenuSwitchNone },	//种族

{ SWITCH_GRAPHIC, 213+6*0-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//土00
{ SWITCH_GRAPHIC, 213+6*1-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 49+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//水
{ SWITCH_GRAPHIC, 213+6*1-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 59+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//火
{ SWITCH_GRAPHIC, 213+6*1-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 69+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//风
{ SWITCH_GRAPHIC, 213+6*1-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 79+48*0-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//土01
{ SWITCH_GRAPHIC, 213+6*1-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 49+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//水
{ SWITCH_GRAPHIC, 213+6*1-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 59+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//火
{ SWITCH_GRAPHIC, 213+6*1-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 69+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//风
{ SWITCH_GRAPHIC, 213+6*1-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 79+48*1-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//


{ SWITCH_GRAPHIC, 213+6*0-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//土02
{ SWITCH_GRAPHIC, 213+6*1-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 49+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//水
{ SWITCH_GRAPHIC, 213+6*1-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 59+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//火
{ SWITCH_GRAPHIC, 213+6*1-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 69+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//风
{ SWITCH_GRAPHIC, 213+6*1-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 79+48*2-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//土03
{ SWITCH_GRAPHIC, 213+6*1-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 49+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//水
{ SWITCH_GRAPHIC, 213+6*1-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 59+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//火
{ SWITCH_GRAPHIC, 213+6*1-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 69+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//风
{ SWITCH_GRAPHIC, 213+6*1-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 79+48*3-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//土04
{ SWITCH_GRAPHIC, 213+6*1-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 49+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//水
{ SWITCH_GRAPHIC, 213+6*1-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 59+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//火
{ SWITCH_GRAPHIC, 213+6*1-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 69+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 213+6*0-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//风
{ SWITCH_GRAPHIC, 213+6*1-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*2-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*3-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*4-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*5-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*6-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*7-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*8-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 213+6*9-23, 79+48*4-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 4], MenuSwitchNone },			//

{ SWITCH_GRAPHIC, 282-23, 70+48*0-16, 23, 18, TRUE, &MenuWindowMonsterGraph[20], MenuSwitchMonsterSWitch },			//Walk00
{ SWITCH_GRAPHIC, 282-23, 70+48*1-16, 23, 18, TRUE, &MenuWindowMonsterGraph[20], MenuSwitchMonsterSWitch },			//Walk01
{ SWITCH_GRAPHIC, 282-23, 70+48*2-16, 23, 18, TRUE, &MenuWindowMonsterGraph[20], MenuSwitchMonsterSWitch },			//Walk02
{ SWITCH_GRAPHIC, 282-23, 70+48*3-16, 23, 18, TRUE, &MenuWindowMonsterGraph[20], MenuSwitchMonsterSWitch },			//Walk03
{ SWITCH_GRAPHIC, 282-23, 70+48*4-16, 23, 18, TRUE, &MenuWindowMonsterGraph[20], MenuSwitchMonsterSWitch },			//Walk04

{ SWITCH_GRAPHIC, 308-23, 70+48*0-16, 23, 18, TRUE, &MenuWindowMonsterGraph[21], MenuSwitchMonsterSWitch },			//StandBy
{ SWITCH_GRAPHIC, 308-23, 70+48*1-16, 23, 18, TRUE, &MenuWindowMonsterGraph[21], MenuSwitchMonsterSWitch },			//StandBy
{ SWITCH_GRAPHIC, 308-23, 70+48*2-16, 23, 18, TRUE, &MenuWindowMonsterGraph[21], MenuSwitchMonsterSWitch },			//StandBy
{ SWITCH_GRAPHIC, 308-23, 70+48*3-16, 23, 18, TRUE, &MenuWindowMonsterGraph[21], MenuSwitchMonsterSWitch },			//StandBy
{ SWITCH_GRAPHIC, 308-23, 70+48*4-16, 23, 18, TRUE, &MenuWindowMonsterGraph[21], MenuSwitchMonsterSWitch },			//StandBy

{ SWITCH_GRAPHIC, 282-23, 50+48*0-16, 23, 18, TRUE, &MenuWindowMonsterGraph[22], MenuSwitchMonsterSWitch },			//Battle
{ SWITCH_GRAPHIC, 282-23, 50+48*1-16, 23, 18, TRUE, &MenuWindowMonsterGraph[22], MenuSwitchMonsterSWitch },			//Battle
{ SWITCH_GRAPHIC, 282-23, 50+48*2-16, 23, 18, TRUE, &MenuWindowMonsterGraph[22], MenuSwitchMonsterSWitch },			//Battle
{ SWITCH_GRAPHIC, 282-23, 50+48*3-16, 23, 18, TRUE, &MenuWindowMonsterGraph[22], MenuSwitchMonsterSWitch },			//Battle
{ SWITCH_GRAPHIC, 282-23, 50+48*4-16, 23, 18, TRUE, &MenuWindowMonsterGraph[22], MenuSwitchMonsterSWitch },			//Battle

#ifdef PetField
{ SWITCH_GRAPHIC, 308-23, 50+48*0-16, 23, 18, TRUE, &MenuWindowMonsterGraph[23], MenuSwitchMonsterSWitch },			//Field
{ SWITCH_GRAPHIC, 308-23, 50+48*1-16, 23, 18, TRUE, &MenuWindowMonsterGraph[23], MenuSwitchMonsterSWitch },			//Field
{ SWITCH_GRAPHIC, 308-23, 50+48*2-16, 23, 18, TRUE, &MenuWindowMonsterGraph[23], MenuSwitchMonsterSWitch },			//Field
{ SWITCH_GRAPHIC, 308-23, 50+48*3-16, 23, 18, TRUE, &MenuWindowMonsterGraph[23], MenuSwitchMonsterSWitch },			//Field
{ SWITCH_GRAPHIC, 308-23, 50+48*4-16, 23, 18, TRUE, &MenuWindowMonsterGraph[23], MenuSwitchMonsterSWitch },			//Field
#endif

{ SWITCH_GRAPHIC, 38-23, 44-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 3], MenuSwitchNone },			//枠
{ SWITCH_GRAPHIC, 38-23, 92-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 3], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 38-23,140-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 3], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 38-23,188-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 3], MenuSwitchNone },			//
{ SWITCH_GRAPHIC, 38-23,236-16,   0,  0, TRUE, &MenuWindowMonsterGraph[ 3], MenuSwitchNone },			//

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowMonsterGraph[ 1], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC, 14, 26,   0,  0, TRUE, &MenuWindowMonsterGraph[ 2], MenuSwitchNone },			//背景

{ SWITCH_BUTTON   , 38-23, 44-16, 242, 47, TRUE, &MenuWindowMonsterButton[0], MenuSwitchMonsterSWitch },	//详细へ
{ SWITCH_BUTTON   , 38-23, 92-16, 242, 47, TRUE, &MenuWindowMonsterButton[0], MenuSwitchMonsterSWitch },	
{ SWITCH_BUTTON   , 38-23,140-16, 242, 47, TRUE, &MenuWindowMonsterButton[0], MenuSwitchMonsterSWitch },	
{ SWITCH_BUTTON   , 38-23,188-16, 242, 47, TRUE, &MenuWindowMonsterButton[0], MenuSwitchMonsterSWitch },	
{ SWITCH_BUTTON   , 38-23,236-16, 242, 47, TRUE, &MenuWindowMonsterButton[0], MenuSwitchMonsterSWitch },	

{ SWITCH_NONE  , 323, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{
	EnumGraphMonsterClose,		

	EnumTextMonsterName00,		
	EnumTextMonsterLevel00,		
	EnumTextMonsterLp00,		
	EnumTextMonsterFp00,		
	EnumTextMonsterType00,		

	EnumTextMonsterName01,		
	EnumTextMonsterLevel01,		
	EnumTextMonsterLp01,		
	EnumTextMonsterFp01,		
	EnumTextMonsterType01,		

	EnumTextMonsterName02,		
	EnumTextMonsterLevel02,		
	EnumTextMonsterLp02,		
	EnumTextMonsterFp02,		
	EnumTextMonsterType02,		

	EnumTextMonsterName03,		
	EnumTextMonsterLevel03,		
	EnumTextMonsterLp03,		
	EnumTextMonsterFp03,		
	EnumTextMonsterType03,		

	EnumTextMonsterName04,		
	EnumTextMonsterLevel04,		
	EnumTextMonsterLp04,		
	EnumTextMonsterFp04,		
	EnumTextMonsterType04,		

	EnumGraphMonsterEarth0000,		
	EnumGraphMonsterEarth0001,		
	EnumGraphMonsterEarth0002,		
	EnumGraphMonsterEarth0003,		
	EnumGraphMonsterEarth0004,		
	EnumGraphMonsterEarth0005,		
	EnumGraphMonsterEarth0006,		
	EnumGraphMonsterEarth0007,		
	EnumGraphMonsterEarth0008,		
	EnumGraphMonsterEarth0009,		
	
	EnumGraphMonsterWater0000,		
	EnumGraphMonsterWater0001,		
	EnumGraphMonsterWater0002,		
	EnumGraphMonsterWater0003,		
	EnumGraphMonsterWater0004,		
	EnumGraphMonsterWater0005,		
	EnumGraphMonsterWater0006,		
	EnumGraphMonsterWater0007,		
	EnumGraphMonsterWater0008,		
	EnumGraphMonsterWater0009,		

	EnumGraphMonsterFire0000,		
	EnumGraphMonsterFire0001,		
	EnumGraphMonsterFire0002,		
	EnumGraphMonsterFire0003,		
	EnumGraphMonsterFire0004,		
	EnumGraphMonsterFire0005,		
	EnumGraphMonsterFire0006,		
	EnumGraphMonsterFire0007,		
	EnumGraphMonsterFire0008,		
	EnumGraphMonsterFire0009,		

	EnumGraphMonsterWind0000,		
	EnumGraphMonsterWind0001,		
	EnumGraphMonsterWind0002,		
	EnumGraphMonsterWind0003,		
	EnumGraphMonsterWind0004,		
	EnumGraphMonsterWind0005,		
	EnumGraphMonsterWind0006,		
	EnumGraphMonsterWind0007,		
	EnumGraphMonsterWind0008,		
	EnumGraphMonsterWind0009,		


	EnumGraphMonsterEarth0100,		
	EnumGraphMonsterEarth0101,		
	EnumGraphMonsterEarth0102,		
	EnumGraphMonsterEarth0103,		
	EnumGraphMonsterEarth0104,		
	EnumGraphMonsterEarth0105,		
	EnumGraphMonsterEarth0106,		
	EnumGraphMonsterEarth0107,		
	EnumGraphMonsterEarth0108,		
	EnumGraphMonsterEarth0109,		
	
	EnumGraphMonsterWater0100,		
	EnumGraphMonsterWater0101,		
	EnumGraphMonsterWater0102,		
	EnumGraphMonsterWater0103,		
	EnumGraphMonsterWater0104,		
	EnumGraphMonsterWater0105,		
	EnumGraphMonsterWater0106,		
	EnumGraphMonsterWater0107,		
	EnumGraphMonsterWater0108,		
	EnumGraphMonsterWater0109,		

	EnumGraphMonsterFire0100,		
	EnumGraphMonsterFire0101,		
	EnumGraphMonsterFire0102,		
	EnumGraphMonsterFire0103,		
	EnumGraphMonsterFire0104,		
	EnumGraphMonsterFire0105,		
	EnumGraphMonsterFire0106,		
	EnumGraphMonsterFire0107,		
	EnumGraphMonsterFire0108,		
	EnumGraphMonsterFire0109,		

	EnumGraphMonsterWind0100,		
	EnumGraphMonsterWind0101,		
	EnumGraphMonsterWind0102,		
	EnumGraphMonsterWind0103,		
	EnumGraphMonsterWind0104,		
	EnumGraphMonsterWind0105,		
	EnumGraphMonsterWind0106,		
	EnumGraphMonsterWind0107,		
	EnumGraphMonsterWind0108,		
	EnumGraphMonsterWind0109,		


	EnumGraphMonsterEarth0200,		
	EnumGraphMonsterEarth0201,		
	EnumGraphMonsterEarth0202,		
	EnumGraphMonsterEarth0203,		
	EnumGraphMonsterEarth0204,		
	EnumGraphMonsterEarth0205,		
	EnumGraphMonsterEarth0206,		
	EnumGraphMonsterEarth0207,		
	EnumGraphMonsterEarth0208,		
	EnumGraphMonsterEarth0209,		
	
	EnumGraphMonsterWater0200,		
	EnumGraphMonsterWater0201,		
	EnumGraphMonsterWater0202,		
	EnumGraphMonsterWater0203,		
	EnumGraphMonsterWater0204,		
	EnumGraphMonsterWater0205,		
	EnumGraphMonsterWater0206,		
	EnumGraphMonsterWater0207,		
	EnumGraphMonsterWater0208,		
	EnumGraphMonsterWater0209,		

	EnumGraphMonsterFire0200,		
	EnumGraphMonsterFire0201,		
	EnumGraphMonsterFire0202,		
	EnumGraphMonsterFire0203,		
	EnumGraphMonsterFire0204,		
	EnumGraphMonsterFire0205,		
	EnumGraphMonsterFire0206,		
	EnumGraphMonsterFire0207,		
	EnumGraphMonsterFire0208,		
	EnumGraphMonsterFire0209,		

	EnumGraphMonsterWind0200,		
	EnumGraphMonsterWind0201,		
	EnumGraphMonsterWind0202,		
	EnumGraphMonsterWind0203,		
	EnumGraphMonsterWind0204,		
	EnumGraphMonsterWind0205,		
	EnumGraphMonsterWind0206,		
	EnumGraphMonsterWind0207,		
	EnumGraphMonsterWind0208,		
	EnumGraphMonsterWind0209,		


	EnumGraphMonsterEarth0300,		
	EnumGraphMonsterEarth0301,		
	EnumGraphMonsterEarth0302,		
	EnumGraphMonsterEarth0303,		
	EnumGraphMonsterEarth0304,		
	EnumGraphMonsterEarth0305,		
	EnumGraphMonsterEarth0306,		
	EnumGraphMonsterEarth0307,		
	EnumGraphMonsterEarth0308,		
	EnumGraphMonsterEarth0309,		
	
	EnumGraphMonsterWater0300,		
	EnumGraphMonsterWater0301,		
	EnumGraphMonsterWater0302,		
	EnumGraphMonsterWater0303,		
	EnumGraphMonsterWater0304,		
	EnumGraphMonsterWater0305,		
	EnumGraphMonsterWater0306,		
	EnumGraphMonsterWater0307,		
	EnumGraphMonsterWater0308,		
	EnumGraphMonsterWater0309,		

	EnumGraphMonsterFire0300,		
	EnumGraphMonsterFire0301,		
	EnumGraphMonsterFire0302,		
	EnumGraphMonsterFire0303,		
	EnumGraphMonsterFire0304,		
	EnumGraphMonsterFire0305,		
	EnumGraphMonsterFire0306,		
	EnumGraphMonsterFire0307,		
	EnumGraphMonsterFire0308,		
	EnumGraphMonsterFire0309,		

	EnumGraphMonsterWind0300,		
	EnumGraphMonsterWind0301,		
	EnumGraphMonsterWind0302,		
	EnumGraphMonsterWind0303,		
	EnumGraphMonsterWind0304,		
	EnumGraphMonsterWind0305,		
	EnumGraphMonsterWind0306,		
	EnumGraphMonsterWind0307,		
	EnumGraphMonsterWind0308,		
	EnumGraphMonsterWind0309,		


	EnumGraphMonsterEarth0400,		
	EnumGraphMonsterEarth0401,		
	EnumGraphMonsterEarth0402,		
	EnumGraphMonsterEarth0403,		
	EnumGraphMonsterEarth0404,		
	EnumGraphMonsterEarth0405,		
	EnumGraphMonsterEarth0406,		
	EnumGraphMonsterEarth0407,		
	EnumGraphMonsterEarth0408,		
	EnumGraphMonsterEarth0409,		
	
	EnumGraphMonsterWater0400,		
	EnumGraphMonsterWater0401,		
	EnumGraphMonsterWater0402,		
	EnumGraphMonsterWater0403,		
	EnumGraphMonsterWater0404,		
	EnumGraphMonsterWater0405,		
	EnumGraphMonsterWater0406,		
	EnumGraphMonsterWater0407,		
	EnumGraphMonsterWater0408,		
	EnumGraphMonsterWater0409,		

	EnumGraphMonsterFire0400,		
	EnumGraphMonsterFire0401,		
	EnumGraphMonsterFire0402,		
	EnumGraphMonsterFire0403,		
	EnumGraphMonsterFire0404,		
	EnumGraphMonsterFire0405,		
	EnumGraphMonsterFire0406,		
	EnumGraphMonsterFire0407,		
	EnumGraphMonsterFire0408,		
	EnumGraphMonsterFire0409,		

	EnumGraphMonsterWind0400,		
	EnumGraphMonsterWind0401,		
	EnumGraphMonsterWind0402,		
	EnumGraphMonsterWind0403,		
	EnumGraphMonsterWind0404,		
	EnumGraphMonsterWind0405,		
	EnumGraphMonsterWind0406,		
	EnumGraphMonsterWind0407,		
	EnumGraphMonsterWind0408,		
	EnumGraphMonsterWind0409,		
		
	EnumGraphMonsterWalk00,		
	EnumGraphMonsterWalk01,		
	EnumGraphMonsterWalk02,		
	EnumGraphMonsterWalk03,		
	EnumGraphMonsterWalk04,		

	EnumGraphMonsterStandBy00,		
	EnumGraphMonsterStandBy01,		
	EnumGraphMonsterStandBy02,		
	EnumGraphMonsterStandBy03,		
	EnumGraphMonsterStandBy04,		

	EnumGraphMonsterBattle00,		
	EnumGraphMonsterBattle01,		
	EnumGraphMonsterBattle02,		
	EnumGraphMonsterBattle03,		
	EnumGraphMonsterBattle04,		

#ifdef PetField
	EnumGraphMonsterField00,		
	EnumGraphMonsterField01,		
	EnumGraphMonsterField02,		
	EnumGraphMonsterField03,		
	EnumGraphMonsterField04,		
#endif

	EnumGraphMonsterWaku00,		
	EnumGraphMonsterWaku01,		
	EnumGraphMonsterWaku02,		
	EnumGraphMonsterWaku03,		
	EnumGraphMonsterWaku04,		

	EnumGraphMonsterWindow,		
	EnumGraphMonsterBack,		

	EnumBtMonsterToDetail00,
	EnumBtMonsterToDetail01,
	EnumBtMonsterToDetail02,
	EnumBtMonsterToDetail03,
	EnumBtMonsterToDetail04,

	EnumHitMonster1,

	EnumMonsterEnd,
};


const WINDOW_DATA WindowDataMenuMonster = {
 0,															// メニューウィンドウ
     4,   3,  85,323,281, 0x80010101,  EnumMonsterEnd,  MonsterSwitch, MenuWindowMonster,MenuWindowMonsterDraw,MenuWindowDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateMonster={
	2,
	0,  0,343, 30,
  323,  0, 20,122,
};

#endif