﻿/************************/
/*	battleEfect.c		*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/math2.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/pattern.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/mouse.h"
#include "../systeminc/t_music.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/battle.h"
#include "../systeminc/battleEffect.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/battleMaster.h"
#include "../systeminc/process.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/tool.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/sndcnf.h"
#if defined(PUK3_RIDE_BATTLE) || defined(PUK3_PUT_ON)
	#include "../systeminc/pc.h"
#endif
#include <math.h>

#ifdef PUK2
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"
	#include "../puk2/newBattle/battleTrance.cpp"

	const int ColorNumGraNo[][4] = {
		{ GID_Num_Big_White_0,	GID_Num_Big_White_Plus,	GID_Num_Big_White_Minus,	GID_Num_Big_White_Colon		},
		{ GID_Num_Big_Aqua_0,	GID_Num_Big_Aqua_Plus,	GID_Num_Big_Aqua_Minus,		GID_Num_Big_Aqua_Colon,		},
		{ GID_Num_Big_Purple_0,	GID_Num_Big_Purple_Plus,GID_Num_Big_Purple_Minus,	GID_Num_Big_Purple_Colon	},
		{ GID_Num_Big_Blue_0,	GID_Num_Big_Blue_Plus,	GID_Num_Big_Blue_Minus,		GID_Num_Big_Blue_Colon		},
		{ GID_Num_Big_Yellow_0,	GID_Num_Big_Yellow_Plus,GID_Num_Big_Yellow_Minus,	GID_Num_Big_Yellow_Colon	},
		{ GID_Num_Big_Green_0,	GID_Num_Big_Green_Plus,	GID_Num_Big_Green_Minus,	GID_Num_Big_Green_Colon		},
		{ GID_Num_Big_Red_0,	GID_Num_Big_Red_Plus,	GID_Num_Big_Red_Minus,		GID_Num_Big_Red_Colon		},
		{ GID_Num_Big_Grey_0,	GID_Num_Big_Grey_Plus,	GID_Num_Big_Grey_Minus,		GID_Num_Big_Grey_Colon		},
		{ GID_Num_Big_Blue2_0,	GID_Num_Big_Blue2_Plus,	GID_Num_Big_Blue2_Minus,	GID_Num_Big_Blue2_Colon		},
		{ GID_Num_Big_Green2_0,	GID_Num_Big_Green2_Plus,GID_Num_Big_Green2_Minus,	GID_Num_Big_Green2_Colon	},
	};
#endif

ACTION *pActAct = NULL;
		
#ifdef PUK2_NEWSKILL
// 突风エフェクト ***********************************************/
void BattleEffectHItMagicToppu( ACTION *pAct );
#endif

// 弓２作成（本体）**********************************************************************/
ACTION *MakeBattleAllow2( ACTION *pParent, float fx, float fy, float fx2, float fy2, int sprNo );

// ナイフ作成（今は气孔弹专用） ***********************************************************************/
ACTION *MakeBattleMissile( ACTION *pParent, BC_ENEMY_LIST *enemyList, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
#ifdef PUK3_WEPON_BREAK
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;
#endif
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIT;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIDE;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	pAct->anim_chr_no = 0;
	//pAct->anim_chr_no = 100500;
	// 步くアニメーション
	pAct->anim_no = 0;
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 10;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_MISSILE;
	/* 初期位置 */
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	//pAct->fy = pParent->fy - ( float )SpriteInfo[ pParent->bmpNo ].height / 2;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( enemyList->enemyId );
#endif
	// デフォルト位置设定z
	//pYobi->defX = ( int )pActBc[ enemyList->enemyId ]->fx;
	//pYobi->defY = ( int )pActBc[ enemyList->enemyId ]->fy;
	// 目的の方向に向ける
	ChangeDir( pAct, pActBc[ enemyList->enemyId ], FALSE, 0 );
	
	// 敌リストをセットする
	BattleSetEnemyList( pAct, enemyList->enemyId, 
						enemyList->bmFlag, enemyList->damage, enemyList->newGraNo );
#ifdef PUK3_WEPON_BREAK
	bmFlag = enemyList->bmFlag;
	// 装备坏れるか？
	if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
		// データの取得
		getnewGraNo( pParent,
			 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
		// 武器坏れデータ登録
		setnewGraNo( pAct, enemyId, bmFlag,
			 newGraNo, ridePetGraNo, putonGraNo );
	}
#endif
	// 行动番号
	pAct->actNo = BC_ACT_MSL;
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pParent->bmpNo );
	GetBmpSize( pActBc[ enemyList->enemyId ]->bmpNo );
#else
	LoadBmp( pParent->bmpNo );
	LoadBmp( pActBc[ enemyList->enemyId ]->bmpNo );
#endif

#ifdef PUK2
	// 投げた人のＩＤ设定
	pYobi->myId = ( ( BC_YOBI* )pParent->pYobi )->myId;
#endif
	// 弓２作成（本体）
	MakeBattleAllow2( pAct, pAct->fx, pAct->fy + ( float )pParent->anim_y + ( float )SpriteInfo[ pParent->bmpNo ].height / 2,
					pActBc[ enemyList->enemyId ]->fx, 
					pActBc[ enemyList->enemyId ]->fy + ( float )pActBc[ enemyList->enemyId ]->anim_y + ( float )SpriteInfo[ pActBc[ enemyList->enemyId ]->bmpNo ].height / 2, 
					sprNo );
	
	// 气孔弹のとき
	if( pAct->anim_chr_no == SPR_kikoudan ){
		// 残像フラグＯＮ
		//pYobi->actZanzouFlag = TRUE;
		//if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_FLASH_0;
		//else pAct->atr |= ACT_ATR_FLASH_1;
		
		//pAct->atr |= ACT_ATR_FLASH_1;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
	}
	
	// スキルＩＤ记忆
	pYobi->skillId = ( ( BC_YOBI *)pParent->pYobi )->skillId;
	// 武器ＩＤ记忆
	pYobi->weaponId = ( ( BC_YOBI *)pParent->pYobi )->weaponId;
#ifdef PUK3_R10
	pYobi->techId = ( ( BC_YOBI *)pParent->pYobi )->techId;
#endif
	
	// 目的地に移动する
	MoveDir( pAct );
	// 目的地に移动する
	MoveDir( pAct );
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	return pAct;
}

#ifdef TECH_BLASTWAVE
//追月
// ナイフ作成（今は追月专用） ***********************************************************************/
ACTION* MakeBattleBlastWaveMissile(ACTION* pParent, BC_ENEMY_LIST* enemyList, int sprNo)
{
	ACTION* pAct;
	BC_YOBI* pYobi;
#ifdef PUK3_WEPON_BREAK
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;
#endif

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction(pParent);
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction(PRIO_CHR, sizeof(BC_YOBI), ACT_T_BC_YOBI);
#else
	pAct = GetAction(PRIO_CHR, sizeof(BC_YOBI));
#endif
	if (pAct == NULL) return NULL;
	// 予备构造体
	pYobi = (BC_YOBI*)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIT;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIDE;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	pAct->anim_chr_no = 0;
	//pAct->anim_chr_no = 100500;
	// 步くアニメーション
	pAct->anim_no = 0;
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 10;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_MISSILE;
	/* 初期位置 */
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	//pAct->fy = pParent->fy - ( float )SpriteInfo[ pParent->bmpNo ].height / 2;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange(enemyList->enemyId);
#endif
	// デフォルト位置设定z
	//pYobi->defX = ( int )pActBc[ enemyList->enemyId ]->fx;
	//pYobi->defY = ( int )pActBc[ enemyList->enemyId ]->fy;
	// 目的の方向に向ける
	ChangeDir(pAct, pActBc[enemyList->enemyId], FALSE, 0);

	//追月和气功蛋不同的地方
	// 方向からグラフィック番号を求める
	pAct->anim_chr_no = (int)((pAct->dir + 11.25) / 22.5);
	// リミットチェック
	if (pAct->anim_chr_no < 0) pAct->anim_chr_no += 16;
	if (pAct->anim_chr_no >= 16) pAct->anim_chr_no -= 16;
	pAct->anim_chr_no += sprNo;

	// 敌リストをセットする
	BattleSetEnemyList(pAct, enemyList->enemyId,
		enemyList->bmFlag, enemyList->damage, enemyList->newGraNo);
#ifdef PUK3_WEPON_BREAK
	bmFlag = enemyList->bmFlag;
	// 装备坏れるか？
	if (bmFlag & BM_FLAG_MY_WEPON_BROKEN) {
		// データの取得
		getnewGraNo(pParent,
			&enemyId, &newGraNo, &ridePetGraNo, &putonGraNo);
		// 武器坏れデータ登録
		setnewGraNo(pAct, enemyId, bmFlag,
			newGraNo, ridePetGraNo, putonGraNo);
	}
#endif
	// 行动番号
	pAct->actNo = BC_ACT_MSL;

	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize(pParent->bmpNo);
	GetBmpSize(pActBc[enemyList->enemyId]->bmpNo);
#else
	LoadBmp(pParent->bmpNo);
	LoadBmp(pActBc[enemyList->enemyId]->bmpNo);
#endif

#ifdef PUK2
	// 投げた人のＩＤ设定
	pYobi->myId = ((BC_YOBI*)pParent->pYobi)->myId;
#endif
	//追月和气功蛋不同的地方
	// 弓２作成（本体）
	MakeBattleAllow2(pAct, pAct->fx, pAct->fy,
		pActBc[enemyList->enemyId]->fx,
		pActBc[enemyList->enemyId]->fy,
		pAct->anim_chr_no);

	// 气孔弹のとき
	if (pAct->anim_chr_no == SPR_kikoudan) {
		// 残像フラグＯＮ
		//pYobi->actZanzouFlag = TRUE;
		//if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_FLASH_0;
		//else pAct->atr |= ACT_ATR_FLASH_1;

		//pAct->atr |= ACT_ATR_FLASH_1;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
	}

	// スキルＩＤ记忆
	pYobi->skillId = ((BC_YOBI*)pParent->pYobi)->skillId;
	// 武器ＩＤ记忆
	pYobi->weaponId = ((BC_YOBI*)pParent->pYobi)->weaponId;
#ifdef PUK3_R10
	pYobi->techId = ((BC_YOBI*)pParent->pYobi)->techId;
#endif

	// 目的地に移动する
	MoveDir(pAct);
	// 目的地に移动する
	MoveDir(pAct);
	// アニメーション
	pattern(pAct, ANM_LOOP);

	// 表示座标に变换
	pAct->x = (int)pAct->fx;
	pAct->y = (int)pAct->fy;

	return pAct;
}
#endif

// 弓２处理（本体）*************************************************************************/
void BattleAllow2( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// スピード设定
	pAct->speed = 10;
	
	if( ( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->hitStopCnt == 0 ){
		// 目的地に移动する
		MoveDir( pAct );
	}
	// 到着チェック
	//if( CheckDistance( pAct, ( float )pYobi->defX, ( float )pYobi->defY ) < B_ATTACK_DIST ){
		// 抹杀
	//	DeathAction( pAct );
	//}
	// 終了チェック
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		// 抹杀
		DeathAction( pAct );
	}
	
	/* 表示优先度 */
	pAct->dispPrio = ( ( ACTION *)pAct->pOther )->dispPrio;
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// 弓２作成（本体）**********************************************************************/
ACTION *MakeBattleAllow2( ACTION *pParent, float fx, float fy, float fx2, float fy2, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleAllow2;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 步くアニメーション
	pAct->anim_no = 0;
	// 步くアニメーション
	pAct->anim_speed = 10;
	/* 表示优先度 */
	//pAct->dispPrio = DISP_PRIO_B_MISSILE;
	/* 表示优先度 */
	pAct->dispPrio = pParent->dispPrio;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	//pAct->anim_chr_no = 100051;
	// 亲を记忆
	pAct->pOther = pParent;
	// 中心座标
	pAct->fx = fx;
	pAct->fy = fy;
	// 目的地记忆
	pYobi->defX = ( int )fx2;
	pYobi->defY = ( int )fy2;
	// 目的の方向に向ける
	ChangeDir( pAct, ( float )pYobi->defX, ( float )pYobi->defY, FALSE, 0 );
	
	// 移动スピード
	pAct->speed = 10;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
#ifdef PUK2
	// 气孔弹のとき
	if( pAct->anim_chr_no == SPR_kikoudan ){
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
	}
#endif
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 气孔弹のとき
	if( pAct->anim_chr_no == SPR_kikoudan ){
		// 目的地に移动する
		MoveDir( pAct );
		// 目的地に移动する
		MoveDir( pAct );
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	return pAct;
}
#ifdef PUK3_NEWSKILL_PSYBLAST
// ホーミング弹处理（本体）*************************************************************************/
void BattleHomingMissile2( ACTION *pAct )
{
	ACTION *pActPa = ( ACTION *)pAct->pOther;
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 終了チェック
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		// 抹杀
		DeathAction( pAct );
	}

	/* 表示优先度 */
	pAct->dispPrio = pActPa->dispPrio;

	// アニメーション
	pattern( pAct, ANM_LOOP );

	// 残像フラグＯＮのとき
	if( pYobi->actZanzouFlag == TRUE ){
		// 残像カウンタープラス
		pYobi->actZanzouCnt++;
		// リミットチェック
		if( pYobi->actZanzouCnt >= 2 ) pYobi->actZanzouCnt = 0;

		// カウンターが１のとき
		if( pYobi->actZanzouCnt == 1 ){
			// 战闘残像作成
			MakeBattleZanzou( pAct );
		}
	}

	// 表示座标に变换
	pAct->x = ( int )pActPa->fx + pAct->dx;
	pAct->y = ( int )pActPa->fy + pAct->dy;
}

// ホーミング弹作成（本体）**********************************************************************/
ACTION *MakeBattleHomingMissile2( ACTION *pParent, int dffx, int dffy, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleHomingMissile2;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 步くアニメーション
	pAct->anim_no = 0;
	// 步くアニメーション
	pAct->anim_speed = 10;
	/* 表示优先度 */
	//pAct->dispPrio = DISP_PRIO_B_MISSILE;
	/* 表示优先度 */
	pAct->dispPrio = pParent->dispPrio;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	//pAct->anim_chr_no = 100051;
	// 亲を记忆
	pAct->pOther = pParent;
	// 影からの相对位置
	pAct->dx = dffx;
	pAct->dy = dffy;
	// 中心座标
	pAct->fx = pParent->fx + dffx;
	pAct->fy = pParent->fy + dffy;
	// 目的の方向に向ける
//	ChangeDir( pAct, ( float )pYobi->defX, ( float )pYobi->defY, FALSE, 0 );
	
	// 移动スピード
	pAct->speed = 10;
	// 表示优先度普通
	pAct->bufCount = 0;
	
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;

	// 残像あり
	pYobi->actZanzouFlag = TRUE;

	// アニメーション
	pattern( pAct, ANM_LOOP );

	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;

	return pAct;
}
// 放物线弹处理（本体）*************************************************************************/
void BattleParabolaMissile2( ACTION *pAct )
{
	ACTION *pActPa = ( ACTION *)pAct->pOther;
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	int y;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 終了チェック
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		// 抹杀
		DeathAction( pAct );
	}

	/* 表示优先度 */
	pAct->dispPrio = pActPa->dispPrio;

	// アニメーション
	pattern( pAct, ANM_LOOP );

	// 残像フラグＯＮのとき
	if( pYobi->actZanzouFlag == TRUE ){
		// 战闘残像作成
		MakeBattleZanzou( pAct );
	}

	// 表示座标に变换
	pAct->x = ( int )pActPa->fx + pAct->dx;
	pAct->y = ( int )pActPa->fy + pAct->dy;

	// 放物线の位置
	y = pAct->delta * pAct->delta;
	y = -y;
	y += pYobi->waitCnt * pYobi->waitCnt;

	// 回転
	pAct->anim_ang++;
	if ( pAct->anim_ang > 7 ) pAct->anim_ang = 0;

	// 放物线分
	pAct->x += (int)( CosT( pAct->dir ) * y );
	pAct->y += (int)( SinT( pAct->dir ) * y );

	pAct->delta++;
}

// 放物线弹作成（本体）**********************************************************************/
#ifdef PUK3_PSYBLAST_VANISH
ACTION *MakeBattleParabolaMissile2( ACTION *pParent, int dffx, int dffy, ACTION *pActAttacker, ACTION *pActTarget, int sprNo )
#else
ACTION *MakeBattleParabolaMissile2( ACTION *pParent, int dffx, int dffy, ACTION *pActTarget, int sprNo )
#endif
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleParabolaMissile2;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 步くアニメーション
	pAct->anim_no = 0;
	// 步くアニメーション
	pAct->anim_speed = 10;
	/* 表示优先度 */
	pAct->dispPrio = pParent->dispPrio;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	// 亲を记忆
	pAct->pOther = pParent;
	// 影からの相对位置
	pAct->dx = dffx;
	pAct->dy = dffy;
	// 中心座标
	pAct->fx = pParent->fx + dffx;
	pAct->fy = pParent->fy + dffy;
	// 目的地
	pYobi->defX = (int)pActTarget->fx + dffx;
	pYobi->defY = (int)pActTarget->fy + dffy;

	// 目的の方向を调べる
	ChangeDir( pAct, pActTarget, FALSE, 0 );

	// 角度
	pAct->dir = pAct->dir + (90 + 45) + ( rand() % 90 );
	pAct->dir = (float)( (int)pAct->dir % 360 );
	if ( pAct->dir < 0 ) pAct->dir += 360;

	// 到着までの时间の半分
#ifdef PUK3_PSYBLAST_VANISH
	pYobi->waitCnt = CheckDistance( pActAttacker, pActTarget );
	pYobi->waitCnt -= B_MISSILE_DIST;		// 消えるタイミングで收束するように
#else
	pYobi->waitCnt = CheckDistance( pAct, pActTarget );
#endif
	pYobi->waitCnt /= 10;
	pYobi->waitCnt /= 2;

	// カウンター
	pAct->delta = -pYobi->waitCnt + 2;

	// 移动スピード
	pAct->speed = 10;
	// 表示优先度普通
	pAct->bufCount = 0;

	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;

	// 残像あり
	pYobi->actZanzouFlag = TRUE;

	// アニメーション
	pattern( pAct, ANM_LOOP );

	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;

	return pAct;
}
#endif

// 矢作成（影）（ナイフと弓） ****************************************************************************/
ACTION *MakeBattleAllow( ACTION *pOther, BC_ENEMY_LIST *enemyList, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
#ifdef PUK3_WEPON_BREAK
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;
#endif
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pOther );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIT;
	// 步くアニメーション
	//pAct->anim_no = ANIM_WALK;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 步くアニメーション
	pAct->anim_speed = 10;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_MISSILE;
	// 中心座标
	pAct->fx = pOther->fx;
	pAct->fy = pOther->fy;
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( enemyList->enemyId );
#endif
	// 目的の方向に向ける
	ChangeDir( pAct, pActBc[ enemyList->enemyId ], TRUE, 0 );
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pOther->bmpNo );
	GetBmpSize( pActBc[ enemyList->enemyId ]->bmpNo );
#else
	LoadBmp( pOther->bmpNo );
	LoadBmp( pActBc[ enemyList->enemyId ]->bmpNo );
#endif
	
	// 発射座标を求める
	// キャラの横幅の半分の大きさを设定
	pAct->speed = ( float )SpriteInfo[ pOther->bmpNo ].width / 2;

	// 目的地に移动する
	MoveDir( pAct );
	
	// 移动スピード
	pAct->speed = 10;
	
#ifdef PUK3_NEWSKILL_COINSHOT
	if ( CG_GOLD_1 <= sprNo && sprNo <= CG_GOLD_10 ){
		pAct->anim_chr_no = sprNo;
		// コインは影无し
		pAct->atr |= ACT_ATR_HIDE;
	}else{
		// 方向からグラフィック番号を求める
		pAct->anim_chr_no = ( int )( ( pAct->dir + 11.25 ) / 22.5 );
		// リミットチェック
		if( pAct->anim_chr_no < 0 ) pAct->anim_chr_no += 16;
		if( pAct->anim_chr_no >= 16 ) pAct->anim_chr_no -= 16;
		pAct->anim_chr_no += sprNo + 16;
	}
#else
	// 方向からグラフィック番号を求める
	pAct->anim_chr_no = ( int )( ( pAct->dir + 11.25 ) / 22.5 );
	// リミットチェック
	if( pAct->anim_chr_no < 0 ) pAct->anim_chr_no += 16;
	if( pAct->anim_chr_no >= 16 ) pAct->anim_chr_no -= 16;
	pAct->anim_chr_no += sprNo + 16;
#endif
	
	// 敌リストをセットする
	BattleSetEnemyList( pAct, enemyList->enemyId, 
						enemyList->bmFlag, enemyList->damage, enemyList->newGraNo );
#ifdef PUK3_WEPON_BREAK
	// 装备坏れるか？
	bmFlag = enemyList->bmFlag;
	if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
		// データの取得
		getnewGraNo( pOther,
			 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
		// 武器坏れデータ登録
		setnewGraNo( pAct, enemyId, bmFlag,
			 newGraNo, ridePetGraNo, putonGraNo );
	}
#endif
	// 行动番号
	pAct->actNo = BC_ACT_MSL;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
		
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// スキルＩＤ记忆
	pYobi->skillId = ( ( BC_YOBI *)pOther->pYobi )->skillId;
	// 武器ＩＤ记忆
	pYobi->weaponId = ( ( BC_YOBI *)pOther->pYobi )->weaponId;
#ifdef PUK3_R10
	pYobi->techId = ( ( BC_YOBI *)pOther->pYobi )->techId;
#endif
	
	// 投げた人のＩＤ设定
	pYobi->myId = ( ( BC_YOBI* )pOther->pYobi )->myId;
	
#ifdef PUK3_NEWSKILL_COINSHOT
	if ( CG_GOLD_1 <= sprNo && sprNo <= CG_GOLD_10 ){
		// 弓２作成（本体）
		MakeBattleAllow2( pAct, pAct->fx, pAct->fy + ( float )pOther->anim_y + ( float )SpriteInfo[ pOther->bmpNo ].height / 2,
						pActBc[ enemyList->enemyId ]->fx, 
						pActBc[ enemyList->enemyId ]->fy + ( float )pActBc[ enemyList->enemyId ]->anim_y + ( float )SpriteInfo[ pActBc[ enemyList->enemyId ]->bmpNo ].height / 2, 
						sprNo );
	}else{
		// 弓２作成（本体）
		MakeBattleAllow2( pAct, pAct->fx, pAct->fy + ( float )pOther->anim_y + ( float )SpriteInfo[ pOther->bmpNo ].height / 2,
						pActBc[ enemyList->enemyId ]->fx, 
						pActBc[ enemyList->enemyId ]->fy + ( float )pActBc[ enemyList->enemyId ]->anim_y + ( float )SpriteInfo[ pActBc[ enemyList->enemyId ]->bmpNo ].height / 2, 
						pAct->anim_chr_no - 16 );
	}
#else
	// 弓２作成（本体）
	MakeBattleAllow2( pAct, pAct->fx, pAct->fy + ( float )pOther->anim_y + ( float )SpriteInfo[ pOther->bmpNo ].height / 2,
					pActBc[ enemyList->enemyId ]->fx, 
					pActBc[ enemyList->enemyId ]->fy + ( float )pActBc[ enemyList->enemyId ]->anim_y + ( float )SpriteInfo[ pActBc[ enemyList->enemyId ]->bmpNo ].height / 2, 
					pAct->anim_chr_no - 16 );
#endif
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	return pAct;
}

// ブーメラン作成 ****************************************************************************/
ACTION *MakeBattleBoomerang( ACTION *pOther )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pOther );
#endif
	ACTION *pAct;
	BC_YOBI *pYobi;
	BC_YOBI *pOtherYobi = ( BC_YOBI *)pOther->pYobi;
#ifdef PUK3_WEPON_BREAK
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;
#endif
#ifdef PUK2_BOOMERANG_ORBIT_1
	float chara_w;
#endif
	
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 步くアニメーション
	pAct->anim_no = 0;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_EFFECT3;
	// 亲のポインタ记忆
	pAct->pOther = pOther;
	// 中心座标
	pAct->fx = pOther->fx;
	pAct->fy = pOther->fy;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	// スプライト番号
	pAct->anim_chr_no = SPR_bm;
	//pAct->anim_chr_no = SPR_mon50a;

	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	// 投げた人のＩＤ设定
	pYobi->myId = pOtherYobi->myId;
	
	while( 1 ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
#ifdef PUK3_R10_ICHIGEKIHICCHU
		// ここでしか出来ないので、しょうがないのでここで
		if ( pOtherYobi->skillId == B_SKILL_ICHIGEKIHICCHU ){
			if ( pOtherYobi->techId == 9 ){
				// 照准表示タスク作成
				MakeBattleGraDisp( pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ], 0, 0, 30, 29106 );
			}else{
				// 照准表示タスク作成
				MakeBattleGraDisp( pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ], 0, 0, 30, 29105 );
			}
		}
#endif
		// 敌リストをセットする
		BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId, 
							pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
							pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
							pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
		// 装备坏れるか？
		bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
		if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
			// データの取得
			getnewGraNo( pOther,
				 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
			// 武器坏れデータ登録
			setnewGraNo( pAct, enemyId, bmFlag,
				 newGraNo, ridePetGraNo, putonGraNo );
		}
#endif
		// リミットチェック
		if( BattleNextEnemyList( pOther ) == 1 ) break;
	}
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
#endif
	// 初期ポイントの设定
	pYobi->defX = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->x;
	pYobi->defY = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y;
#ifdef PUK3_ACTION_REF_ERROR_INNGA
	pYobi->pActBmm = pOtherYobi->pActBmm;
#endif
	
#ifdef PUK2_BOOMERANG_ORBIT_1
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[0].enemyId );
#endif
	// 敌が左侧なら
	switch( CheckUpperLeftOrLowerRight( pAct->fx, pAct->fy,
		 pActBc[ pYobi->enemyList[0].enemyId ]->fx,
		 pActBc[ pYobi->enemyList[0].enemyId ]->fy, 5 ) ){
	case -1:
		// ターゲットが２匹以上なら
		if ( pYobi->enemySetListCnt >= 2 ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pYobi->enemyList[1].enemyId );
#endif
			// 右へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx <
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				pYobi->defY += 100;
				pYobi->defX += 20;
			}
			// 左へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx >
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				pYobi->defX += 100;
			}
		}
		break;
	case 1:
		// ターゲットが２匹以上なら
		if ( pYobi->enemySetListCnt >= 2 ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pYobi->enemyList[1].enemyId );
#endif
			// 左へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx >
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				pYobi->defY -= 100;
				pYobi->defX += 20;
			}
			// 右へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx <
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				pYobi->defX -= 100;
			}
		}
		break;
	case 0:
		// ターゲットが２匹以上なら
		if ( pYobi->enemySetListCnt >= 2 ){
			// 左へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx >
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				// 直接いけないなら
				if( pYobi->defX >= pAct->x ) pYobi->defX += 150;
			}
			// 右へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx <
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				// 直接いけないなら
				if( pYobi->defX <= pAct->x ) pYobi->defY += 150;
			}
		}
	}
#else
	// ターゲットが左上キャラクターの场合
	if( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId >= 10 ){
		// 自分が一番左の列のとき
		if( 10 <= pOtherYobi->myId && pOtherYobi->myId < 15 ){
			// 自分のＹ座标の方が小さかったらちょっと修正??
			if( pAct->y <= pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y ){
				pYobi->defX -= 100;
			}else{
				//pYobi->defY += 70;
				//pYobi->defX -= 70;
			}
		}else{
			// 自分のＹ座标の方が大きかったらちょっと修正??
			if( pAct->y > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y ){
				pYobi->defX += 100;
			}else{
				//pYobi->defY -= 70;
				//pYobi->defX += 70;
			}
		}
	}else{
		// 自分が一番右の列のとき
		if( 0 <= pOtherYobi->myId && pOtherYobi->myId < 5 ){
			// 自分のＹ座标の方が大きかったらちょっと修正??
			if( pAct->y >= pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y ){
				pYobi->defX += 100;
			}else{
				//pYobi->defY -= 70;
				//pYobi->defX += 70;
			}
		}else{
			// 自分のＹ座标の方が小さかったらちょっと修正??
			if( pAct->y < pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y ){
				pYobi->defX -= 100;
			}else{
				//pYobi->defY += 70;
				//pYobi->defX -= 70;
			}
		}
	}
#endif
#ifdef PUK2_BOOMERANG_ORBIT_1
		// 对象に近すぎる场合の处理
		// ターゲットが２匹以上なら
		if ( pYobi->enemySetListCnt >= 2 ){
			int a;
			char EnemyLRFlag, MoveLRFlag;

			// 敌が左侧なら
			EnemyLRFlag = CheckUpperLeftOrLowerRight( pAct->fx, pAct->fy,
				 pActBc[ pYobi->enemyList[0].enemyId ]->fx,
				 pActBc[ pYobi->enemyList[0].enemyId ]->fy, 5 );

			MoveLRFlag = TRUE;
			// 右へ移动
			if ( pActBc[ pYobi->enemyList[0].enemyId ]->fx <
				 pActBc[ pYobi->enemyList[1].enemyId ]->fx ){
				MoveLRFlag = FALSE;
			}

			// 左回りのとき
			if ( (EnemyLRFlag<0 && MoveLRFlag) || (EnemyLRFlag>0 && !MoveLRFlag) ){
				// 上方向に投掷とき
				if ( pYobi->defY < pAct->y ){
					if ( pYobi->defX > pAct->x ){
						a = pYobi->defX - pAct->x;
						a *= 100;
						a /= pAct->y - pYobi->defY;

						if ( a > 100 ) a = 100;

						if ( EnemyLRFlag < 0 ){
							pYobi->defY += a;
							pYobi->defX += a / 2;
						}
						if ( EnemyLRFlag > 0 ){
							pYobi->defY -= a;
							pYobi->defX -= a / 2;
						}
					}
				}else{
					if ( pYobi->defX < pAct->x ){
						a = pAct->x - pYobi->defX;
						a *= 100;
						a /= pYobi->defY - pAct->y;

						if ( a > 100 ) a = 100;

						if ( EnemyLRFlag < 0 ){
							pYobi->defY += a;
							pYobi->defX += a / 2;
						}
						if ( EnemyLRFlag > 0 ){
							pYobi->defY -= a;
							pYobi->defX -= a / 2;
						}
					}
				}
			}
			// 右回りのとき
			else{
				// 上方向に投掷とき
				if ( pYobi->defX < pAct->x ){
					if ( pYobi->defY < pAct->y ){
						a = pAct->y - pYobi->defY;
						a *= 100;
						a /= pAct->x - pYobi->defX;

						if ( a > 100 ) a = 100;

						if ( EnemyLRFlag < 0 ){
							pYobi->defX -= a;
							pYobi->defY += a / 2;
						}
						if ( EnemyLRFlag > 0 ){
							pYobi->defX += a;
							pYobi->defY -= a / 2;
						}
					}
				}else{
					if ( pYobi->defY > pAct->y ){
						a = pYobi->defY - pAct->y;
						a *= 100;
						a /= pYobi->defX - pAct->x;

						if ( a > 100 ) a = 100;

						if ( EnemyLRFlag < 0 ){
							pYobi->defX -= a;
							pYobi->defY += a / 2;
						}
						if ( EnemyLRFlag > 0 ){
							pYobi->defX += a;
							pYobi->defY -= a / 2;
						}
					}
				}
			}
		}
#else
#ifdef PUK2_NEWSKILL_ICHIGEKIHICCHU
	#ifdef PUK2_BOOMERANG_ORBIT
		// 对象に近すぎる场合の处理
		{
			int a;
			// 上方向に投掷とき
			if ( pYobi->defY < pAct->y ){
				if ( pYobi->defX > pAct->x ){
					a = pYobi->defX - pAct->x;
					a *= 100;
					a /= pAct->y - pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y;

					if ( a > 100 ) a = 100;
					pYobi->defY += a;
				}
			}else{
				if ( pYobi->defX < pAct->x ){
					a = pAct->x - pYobi->defX;
					a *= 100;
					a /= pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y - pAct->y;

					if ( a > 100 ) a = 100;
					pYobi->defY -= a;

					pYobi->defY -= pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y - pAct->y;
				}
			}
		}
	#else
	// 对象に近すぎる场合の处理
	{
		// 上方向に投掷とき
		if ( pYobi->defY < pAct->y ){
			if ( pYobi->defX + 15 > pAct->x ){
				pYobi->defY += pAct->y - pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y;
				pYobi->defY -= 10;
			}
		}else{
			if ( pYobi->defX - 15 < pAct->x ){
				pYobi->defY -= pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->y - pAct->y;
				pYobi->defY += 10;
			}
		}
	}
	#endif
#endif
#endif
	
	// 目的の方向に向ける
	ChangeDir( pAct, ( float )pYobi->defX, ( float )pYobi->defY, FALSE, 0 );
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pOther->bmpNo );
#else
	LoadBmp( pOther->bmpNo );
#endif

#ifdef PUK2_BOOMERANG_ORBIT_1
	// キャラ每に动作が违うとバグが出たとき分かりずらいので

	// 移动スピード
	pAct->speed = 10;
	// 行动番号
	pAct->actNo = BC_ACT_BOOMERANG_START;

	// 返回ときの曲がる大きさ
	pAct->dfx = 10;

	// 最初に曲がった方を记忆するための变数
	pAct->level = 0;

	// キャラの横幅の半分の大きさを设定
	chara_w = ( float )SpriteInfo[ pOther->bmpNo ].width / 2;

	// キャラの横幅の分移动するまで移动
	while(1){
		pAct->func(pAct);

		// ブーメランの行动が别のものになってたら移动終了
		if ( pAct->actNo != BC_ACT_BOOMERANG_START ) break;

		// キャラの横幅分移动してたら移动終了
		if ( CheckDistance( pAct, pOther ) >= chara_w ) break;
	}
#else
	// 発射座标を求める
	// キャラの横幅の半分の大きさを设定
	pAct->speed = ( float )SpriteInfo[ pOther->bmpNo ].width / 2;
#ifdef PUK2_ICHIGEKI_ORBIT
	// 到着予定位置を过ぎちゃうとき
	if ( CheckDistance( pAct, ( float )pYobi->defX, ( float )pYobi->defY ) < pAct->speed ){
		// しょうがないので到着予定位置に移动
		pAct->fx = ( float )pYobi->defX;
		pAct->fy = ( float )pYobi->defY;

		// 移动スピード
		pAct->speed = 10;

		// 行动番号
		pAct->actNo = BC_ACT_BOOMERANG;
	}else{
		// 目的地に移动する
		MoveDir( pAct );
		// 移动スピード
		pAct->speed = 10;
		// 行动番号
		pAct->actNo = BC_ACT_BOOMERANG_START;
	}

	// 左の方へ十分曲がったかを示すフラグ
	pAct->level = 0;
#else

	// 目的地に移动する
	MoveDir( pAct );
	
	// 移动スピード
	pAct->speed = 10;
							
	// 行动番号
	pAct->actNo = BC_ACT_BOOMERANG_START;
#endif
#endif
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// スキルＩＤ记忆
	pYobi->skillId = pOtherYobi->skillId;
	// 飞び道具フラグ记忆
	pYobi->weaponId = pOtherYobi->weaponId;
#ifdef PUK3_R10
	pYobi->techId = pOtherYobi->techId;
#endif
	
	// 表示座标に变换
	//pAct->x = ( int )pAct->fx;
	//pAct->y = ( int )pAct->fy;
	
	return pAct;
}
#ifdef PUK3_NEWSKILL_PSYBLAST
// ホーミング弹作成 ***********************************************************************/
ACTION *MakeBattlHomingeMissile( ACTION *pParent, BC_ENEMY_LIST *enemyList, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;

	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIDE;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = 0;
	// 步くアニメーション
	pAct->anim_no = 0;
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 10;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_MISSILE;
	/* 初期位置 */
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	// 表示优先度普通
	pAct->bufCount = 0;

	// 目的の方向に向ける
	pAct->dir = (float)( rand() % 360 );
//	ChangeDir( pAct, pActBc[ enemyList->enemyId ], FALSE, 0 );

	// 敌リストをセットする
	BattleSetEnemyList( pAct, enemyList->enemyId, 
						enemyList->bmFlag, enemyList->damage, enemyList->newGraNo );
#ifdef PUK3_WEPON_BREAK
	bmFlag = enemyList->bmFlag;
	// 装备坏れるか？
	if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
		// データの取得
		getnewGraNo( pParent,
			 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
		// 武器坏れデータ登録
		setnewGraNo( pAct, enemyId, bmFlag,
			 newGraNo, ridePetGraNo, putonGraNo );
	}
#endif
	// 行动番号
	pAct->actNo = BC_ACT_HOMING;

	// 影の絵の设定
	pAct->anim_chr_no = sprNo;
	// マインドブラストは影无し
	pAct->atr |= ACT_ATR_HIDE;

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( enemyList->enemyId );
#endif
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
	GetBmpSize( pParent->bmpNo );
	GetBmpSize( pActBc[ enemyList->enemyId ]->bmpNo );

	// 投げた人のＩＤ设定
	pYobi->myId = ( ( BC_YOBI* )pParent->pYobi )->myId;
	// 弓２作成（本体）
	MakeBattleHomingMissile2( pAct,
		 0,
		 pParent->anim_y + SpriteInfo[ pParent->bmpNo ].height / 2,
		 sprNo );

	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;

	// スキルＩＤ记忆
	pYobi->skillId = ( ( BC_YOBI *)pParent->pYobi )->skillId;
	// 武器ＩＤ记忆
	pYobi->weaponId = ( ( BC_YOBI *)pParent->pYobi )->weaponId;
#ifdef PUK3_R10
	pYobi->techId = ( ( BC_YOBI *)pParent->pYobi )->techId;
#endif

	// 目的地に移动する
	MoveDir( pAct );
	// 目的地に移动する
	MoveDir( pAct );
	// アニメーション
	pattern( pAct, ANM_LOOP );

	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;

	return pAct;
}
// 放物线弹作成 ***********************************************************************/
ACTION *MakeBattleParabolaMissile( ACTION *pParent, BC_ENEMY_LIST *enemyList, int sprNo, int Num, BOOL reflection )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;
	int i;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;

	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIDE;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = 0;
	// 步くアニメーション
	pAct->anim_no = 0;
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 10;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_MISSILE;
	/* 初期位置 */
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	// 表示优先度普通
	pAct->bufCount = 0;

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( enemyList->enemyId );
#endif
	// 目的の方向に向ける
	ChangeDir( pAct, pActBc[ enemyList->enemyId ], FALSE, 0 );

	// 敌リストをセットする
	BattleSetEnemyList( pAct, enemyList->enemyId, 
						enemyList->bmFlag, enemyList->damage, enemyList->newGraNo );
#ifdef PUK3_WEPON_BREAK
	bmFlag = enemyList->bmFlag;
	// 装备坏れるか？
	if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
		// データの取得
		getnewGraNo( pParent,
			 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
		// 武器坏れデータ登録
		setnewGraNo( pAct, enemyId, bmFlag,
			 newGraNo, ridePetGraNo, putonGraNo );
	}
#endif
	// 行动番号
	if ( reflection ) pAct->actNo = BC_ACT_MSL_REFLECTION;
	else pAct->actNo = BC_ACT_MSL;

	// 影の絵の设定
	pAct->anim_chr_no = sprNo;
	// マインドブラストは影无し
	pAct->atr |= ACT_ATR_HIDE;

	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
	GetBmpSize( pParent->bmpNo );
	GetBmpSize( pActBc[ enemyList->enemyId ]->bmpNo );

	// 投げた人のＩＤ设定
	pYobi->myId = ( ( BC_YOBI* )pParent->pYobi )->myId;
	for(i=0;i<Num;i++){
#ifdef PUK3_PSYBLAST_VANISH
		// 弓２作成（本体）
		MakeBattleParabolaMissile2( pAct,
			 0,
			 pParent->anim_y + SpriteInfo[ pParent->bmpNo ].height / 2,
			 pParent, pActBc[ enemyList->enemyId ],
			 sprNo );
#else
		// 弓２作成（本体）
		MakeBattleParabolaMissile2( pAct,
			 0,
			 pParent->anim_y + SpriteInfo[ pParent->bmpNo ].height / 2,
			 pActBc[ enemyList->enemyId ],
			 sprNo );
#endif
	}

	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;

	// スキルＩＤ记忆
	pYobi->skillId = ( ( BC_YOBI *)pParent->pYobi )->skillId;
	// 武器ＩＤ记忆
	pYobi->weaponId = ( ( BC_YOBI *)pParent->pYobi )->weaponId;
#ifdef PUK3_R10
	pYobi->techId = ( ( BC_YOBI *)pParent->pYobi )->techId;
#endif

	// 目的地に移动する
	MoveDir( pAct );
	// 目的地に移动する
	MoveDir( pAct );
	// アニメーション
	pattern( pAct, ANM_LOOP );

	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;

	return pAct;
}
#endif

// 战闘ヒットマーク处理 ***********************************************/
void BattleHitMark( ACTION *pAct )
{
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		DeathAction( pAct );
	}
}

// 战闘ヒットマークタスク作成 *******************************************************/
#ifdef PUK2_NEWSKILL
ACTION *MakeBattleHitMark( ACTION *pOther, int sprNo, int ang )
#else
ACTION *MakeBattleHitMark( ACTION *pOther, int sprNo )
#endif
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pOther );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleHitMark;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_FLASH_0 | ACT_ATR_TRANCEPARENT | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_1;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_1;
	#endif
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	// アニメーションで画像番号を取り出す。
	//pattern( pAct, ANM_NO_LOOP );
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pOther->bmpNo );
#else
	LoadBmp( pOther->bmpNo );
#endif
	
	// 中心座标记忆する
	//pAct->x = pOther->x + Rnd( 0, pOther->anim_x / 2 ) - pOther->anim_x / 4;
	pAct->x = pOther->x + Rnd( 0, SpriteInfo[ pOther->bmpNo ].width / 2 ) - SpriteInfo[ pOther->bmpNo ].width / 4;
	//pAct->y = pOther->y + pOther->anim_y / 2 + Rnd( 0, pOther->anim_y / 4 ) - pOther->anim_y / 8;
	//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2 + Rnd( 0, SpriteInfo[ pOther->bmpNo ].height / 4 ) - SpriteInfo[ pOther->bmpNo ].height / 8;
	
	//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 4 - Rnd( 0, SpriteInfo[ pOther->bmpNo ].height / 3 );
	pAct->y = pOther->y + pOther->anim_y / 4 - Rnd( 0, pOther->anim_y / 3 );
	
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
#ifdef PUK2_NEWSKILL
	pAct->anim_ang = ang;
#else
	pAct->anim_ang = 0;
#endif
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 战闘ヒットマークタスク作成 *******************************************************/
#ifdef PUK2_NEWSKILL
ACTION *MakeBattleHitMark( int x, int y, int sprNo, int ang )
#else
ACTION *MakeBattleHitMark( int x, int y, int sprNo )
#endif
{
	ACTION *pAct;
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleHitMark;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_FLASH_0 | ACT_ATR_TRANCEPARENT | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	pAct->x = x;
	pAct->y = y;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
#ifdef PUK2_NEWSKILL
	pAct->anim_ang = ang;
#else
	pAct->anim_ang = 0;
#endif
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 战闘エフェクトカウンター处理 ***********************************************/
void BattleEffectCounter( ACTION *pAct )
{
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		// 亲がいる时エフェクトフラグＯＦＦにする
		if( pAct->pOther != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( (ACTION *)pAct->pOther );
#endif
			// 終了待ちフラグＯＮの时
			if( ( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag != 0 ){
				// 終了にする
				( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag = 2;
			}
		}
		// 自分を抹杀
		DeathAction( pAct );
	}
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
}

// 战闘エフェクトループ处理 ***********************************************/
void BattleEffectLoop( ACTION *pAct )
{
	// 亲ポインタ知ってる时
	if( pAct->pOther != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( (ACTION *)pAct->pOther );
#endif
		// 終了チェック
		if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
			// 抹杀
			DeathAction( pAct );
		}
	}
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// 战闘エフェクト上へ处理 ***********************************************/
void BattleEffectUp( ACTION *pAct )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION * )pAct->pOther )->deathFlag == TRUE ){
		// 自害
		DeathAction( pAct );
		return;
	}
	
	// 加速
	pAct->dfy -= pAct->dfx;
	// 上升
	pAct->fy += pAct->dfy;
	// リミットチェック
	if( pAct->fy < ( float )pAct->actNo ){
		// 亲のカウンター教える
		( ( ACTION * )pAct->pOther )->delta++;
		// 自分を抹杀
		DeathAction( pAct );
	}
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// 封印カードエフェクト处理 ***********************************************/
void BattleEffectCard( ACTION *pAct )
{
	// 行动番号で分岐
	switch( pAct->actNo ){
	
	case 0:	// 出现扩大
		
		// 大きくする
		pAct->scaleX += 0.02F;
		pAct->scaleY += 0.02F;
		// リミットチェック
		if( pAct->scaleX > 1 ){
			pAct->scaleX = 1;
			pAct->scaleY = 1;
			// 次の行动へ
			pAct->actNo++;
			//pAct->actNo = 2;
		}
	
		break;
		
	case 1:	// 出现扩大
		
		// 何もしない
		break;
		
	case 2:	// 缩小終了
		
		// 小さくする
		pAct->scaleX -= 0.02F;
		pAct->scaleY -= 0.02F;
		// リミットチェック
		if( pAct->scaleX < 0 ){
			pAct->scaleX = 0;
			pAct->scaleY = 0;
			// 次の行动へ
			pAct->actNo++;
		}
		break;
		
	}
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
}

// 战闘死亡エフェクト处理 ***********************************************/
void BattleEffectDead( ACTION *pAct )
{
	ACTION *pAct2;
	int i;
	
	int tableX[ 16 ] = {  48,  44,  34,  18, 0, -18, -34, -44, 
						 -48, -44, -34, -18, 0,  18,  34,  44 };
						 
	int tableY[ 16 ] = { 0,  14,  26,  44,  47 , 33,  26,  14, 
						 0, -14, -26, -44, -47, -33, -26, -14 };
	

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION * )pAct->pOther )->deathFlag == TRUE ){
		// 自害
		DeathAction( pAct );
		return;
	}

	// リミットチェック
	if( pAct->actNo++ < 50 ){
		// 亲非表示
		if( pAct->actNo == 40 ){
			// 亲のカウンター教える
			( ( ACTION * )pAct->pOther )->atr |= ACT_ATR_HIDE;
		}
		// 场所ランダム
		i = Rnd( 0, 15 );
		
		// アクションポインタ取得
		pAct2 = GetAction( PRIO_CHR, NULL );
		
		// ポインタあるとき
		if( pAct2 != NULL ){
		
			// 实行关数
			pAct2->func = BattleEffectUp;
			
			// 点灭
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			if( Rnd( 0, 1 ) ) pAct2->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
			else pAct2->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_1 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct2->atr |= ACT_ATR_TRANCEPARENT;
			else{
				if( Rnd( 0, 1 ) ) pAct2->atr |= ACT_ATR_FLASH_0;
				else pAct2->atr |= ACT_ATR_FLASH_1;
			}
		#endif
	#else
			if( Rnd( 0, 1 ) ) pAct2->atr |= ACT_ATR_FLASH_0;
			else pAct2->atr |= ACT_ATR_FLASH_1;
	#endif
			
			// Ｙ座标で判别ソートするか判定
			//if( tableY[ i ] >= 0 ){
				// 表示优先度
				pAct2->dispPrio = DISP_PRIO_B_HIT_MARK;
				// アクションスローフラグＯＮ
				pAct2->atr |= ACT_ATR_ACT_SLOW;
			//}
			//else{
				// 表示优先度
			//	pAct2->dispPrio = DISP_PRIO_B_CHAR;
				// 战闘时のＹ座标ソート处理ＯＮ
			//	pAct->atr |= ACT_ATR_BATTLE_SORT;
			//}
			
			//	pAct2->dispPrio = DISP_PRIO_GRID;
			// 步くアニメーション
			pAct2->anim_speed = ANM_NOMAL_SPD;
			
			// anim_tbl.h の番号
			//pAct2->anim_chr_no = SPR_life01;
			pAct2->anim_chr_no = SPR_ultimate;
#ifdef PUK2
			pAct2->bltfon = BLTF_NOCHG;
			pAct2->bltf = BLTF_NOCHG;
#endif
			
			// アニメーションで画像番号を取り出す。
			//pattern( pAct2, ANM_NO_LOOP );
			
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			
			// 中心座标记忆する
			pAct2->fx = ( float )( pAct->x + tableX[ i ] );
			//pAct2->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct2->fy = ( float )( pAct->y + tableY[ i ] );
			pAct2->actNo = pAct->y + tableY[ i ] - 100;
			// 加速度
			pAct2->dfx = 0.2F;
			// 亲アクション学习
			pAct2->pOther = pAct;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
			
		}
	}	
	
	if( pAct->delta >= 50 ){
		// 亲がいる时エフェクトフラグＯＦＦにする
		if( pAct->pOther != NULL ){
			// 終了待ちフラグＯＮの时
			if( ( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag != 0 ){
				// 終了にする
				( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag = 2;
			}
		}
		// 自分を抹杀
		DeathAction( pAct );
	}
}

// 战闘エフェクト苏生２处理 ***********************************************/
void BattleEffectAnabiosis2( ACTION *pAct )
{
	ACTION *pAct2 = ( ACTION * )pAct->pOther;
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 上升
	pAct->fy += 3;
	// リミットチェック
	if( pAct->fy > ( float )pAct->fontY ){
		// 亲のカウンター教える
		( ( ACTION * )pAct->pOther )->delta++;
		// 自分を抹杀
		DeathAction( pAct );
	}
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// 战闘エフェクト苏生予备构造体
typedef struct{
	int cnt[ 32 ];
	int dirCnt;
}EFFECT_ANABIOSIS;


// 战闘エフェクト苏生处理 ***********************************************/
void BattleEffectAnabiosis( ACTION *pAct )
{
	ACTION *pAct2;
	EFFECT_ANABIOSIS *pYobi = ( EFFECT_ANABIOSIS *)pAct->pYobi;
	
	int i;
	
	int tableX[ 16 ] = {  48,  44,  34,  18, 0, -18, -34, -44, 
						 -48, -44, -34, -18, 0,  18,  34,  44 };
						 
	int tableY[ 16 ] = { 0,  14,  26,  44,  47 , 44,  26,  14, 
						 0, -14, -26, -44, -47, -44, -26, -14 };
	
	// リミットチェック
	if( pAct->actNo++ > 1 ){
		// 初期化
		pAct->actNo = 0;
	
		// 导火线ループ
		for( i = 0 ; i <= pYobi->dirCnt ; i++ ){
			
			if( pYobi->cnt[ i ] < 4 ){
				
				// アクションポインタ取得
				pAct2 = GetAction( PRIO_CHR, NULL );
				
				// ポインタあるとき
				if( pAct2 != NULL ){
				
					// 实行关数
					pAct2->func = BattleEffectAnabiosis2;
					
					// 表示优先度
					pAct2->dispPrio = DISP_PRIO_B_HIT_MARK;
					// 步くアニメーション
					pAct2->anim_speed = ANM_NOMAL_SPD;
					
					pAct2->anim_chr_no = SPR_sosei;
					//pAct2->anim_chr_no = SPR_life01;
					
					// アクションスローフラグＯＮ
					pAct2->atr |= ACT_ATR_ACT_SLOW;
#ifdef PUK2
					pAct2->bltfon = BLTF_NOCHG;
					pAct2->bltf = BLTF_NOCHG;
#endif
					
					// 中心座标记忆する
					pAct2->fx = ( float )( pAct->x + tableX[ 15 - i % 16 ] );
					//pAct2->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
					pAct2->fy = ( float )( pAct->y + tableY[ 15 - i % 16 ] ) - 100;
					pAct2->fontY = pAct->y + tableY[ 15 - i % 16 ] - 40;
					// 亲アクション学习
					pAct2->pOther = pAct;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
					// 表示优先度普通
					pAct->bufCount = 0;
#endif
					
					pYobi->cnt[ i ]++;
					
				}
			}
		}
		// 	导火线追加
		pYobi->dirCnt++;
		//if( pYobi->dirCnt >= 16 ) pYobi->dirCnt = 15;
		if( pYobi->dirCnt >= 32 ) pYobi->dirCnt = 31;
		
	}	
	
	// 終了チェック
	if( pAct->delta >= 128 ){
		// 亲がいる时エフェクトフラグＯＦＦにする
		if( pAct->pOther != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( (ACTION *)pAct->pOther );
#endif
			// 終了待ちフラグＯＮの时
			if( ( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag != 0 ){
				// 終了にする
				( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag = 2;
			}
		}
		// 自分を抹杀
		DeathAction( pAct );
	}
}

#if 0
// 战闘状态异常回复エフェクト处理 ***********************************************/
void BattleEffectAnabiosis2( ACTION *pAct )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 上升
	pAct->fy += 3;
	// リミットチェック
	if( pAct->fy > ( float )pAct->fontY ){
		// 亲のカウンター教える
		( ( ACTION * )pAct->pOther )->delta++;
		// 自分を抹杀
		DeathAction( pAct );
	}
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}
#endif

// 战闘状态异常回复エフェクト处理 ***********************************************/
void BattleEffectStatusRecover( ACTION *pAct )
{
	ACTION *pAct2;
	EFFECT_ANABIOSIS *pYobi = ( EFFECT_ANABIOSIS *)pAct->pYobi;
	
	int tableX[ 16 ] = {  48,  44,  34,  18, 0, -18, -34, -44, 
						 -48, -44, -34, -18, 0,  18,  34,  44 };
						 
	int tableY[ 16 ] = { 0,  14,  26,  44,  47 , 44,  26,  14, 
						 0, -14, -26, -44, -47, -44, -26, -14 };
	
	// リミットチェック
	if( pAct->actNo++ > 0 && pAct->fontX < 32 ){
		// 初期化
		pAct->actNo = 0;
			
		// アクションポインタ取得
		pAct2 = GetAction( PRIO_CHR, NULL );
		
		// ポインタあるとき
		if( pAct2 != NULL ){
		
			// 实行关数
			pAct2->func = BattleEffectUp;
			
			// 表示优先度
			pAct2->dispPrio = DISP_PRIO_B_HIT_MARK;
			// 步くアニメーション
			pAct2->anim_speed = ANM_NOMAL_SPD;
			
			//pAct2->anim_chr_no = SPR_sosei;
			pAct2->anim_chr_no = SPR_life01;
			// 点灭
			//if( Rnd( 0, 1 ) ) pAct2->atr |= ACT_ATR_FLASH_0;
			//else pAct2->atr |= ACT_ATR_FLASH_1;
			// アクションスローフラグＯＮ
			pAct2->atr |= ACT_ATR_ACT_SLOW;
#ifdef PUK2
			pAct2->bltfon = BLTF_NOCHG;
			pAct2->bltf = BLTF_NOCHG;
#endif

#if 0			
			// 中心座标记忆する
			pAct2->fx = ( float )( pAct->x + tableX[ 15 - pAct->fontX % 16 ] );
			//pAct2->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct2->fy = ( float )( pAct->y + tableY[ 15 - pAct->fontX % 16 ] );
			pAct2->actNo = pAct->y + tableY[ 15 - pAct->fontX % 16 ] - 100;
#else			
			// 中心座标记忆する
			pAct2->fx = ( float )( pAct->x + tableX[ pAct->fontX % 16 ] );
			//pAct2->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct2->fy = ( float )( pAct->y + tableY[ pAct->fontX % 16 ] );
			pAct2->actNo = pAct->y + tableY[ pAct->fontX % 16 ] - 100;
#endif			
			// 加速度
			pAct2->dfx = 0.1F;
			
			// 亲アクション学习
			pAct2->pOther = pAct;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}
		// 発射位置移动
		pAct->fontX++;
	}
	
	// 終了チェック
	if( pAct->delta >= 32 ){
		// 亲がいる时エフェクトフラグＯＦＦにする
		if( pAct->pOther != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( (ACTION *)pAct->pOther );
#endif
			// 終了待ちフラグＯＮの时
			if( ( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag != 0 ){
				// 終了にする
				( ( BC_YOBI *)( ( ACTION *)pAct->pOther )->pYobi )->effectFlag = 2;
			}
		}
		// 自分を抹杀
		DeathAction( pAct );
	}
}


// 战闘ドレインエフェクト处理 ***********************************************/
void BattleEffectHItMagicDrain( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	//int i;
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
#endif
	// 相手がスタンバイじゃないとき返回
	if( pAct->actNo == 0 && pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->actNo != BC_ACT_STANDBY ){
		return;
	}else{
		pAct->actNo = 1;
	}
	
	// 攻击判定ある时
	//if( pAct->anim_hit >= ANIM_HIT_BACK_NUM ){
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		DeathAction( pAct );
	
		// 相手の予备构造体
		ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];
		BC_YOBI *pEnemyYobi = ( BC_YOBI *)pActEnemy->pYobi;
		
		int hitSprNo;
		// 防御の时
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_GUARD ){
			hitSprNo = SPR_gard;
		}else
		// マジック防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_MAGIC_GUARD ){
			hitSprNo = SPR_m_gard;
		}else
		// スペシャル防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_SPECIAL_GUARD ){
			hitSprNo = SPR_s_gard;
		}else hitSprNo = SPR_hit01 + Rnd( 0, 2 );
		// 相手をこっちにの方向に向ける
		//ChangeDir( pActEnemy, pAct, TRUE, 0 );
		// デフォルトの方向へ向ける
		ChangeDefDir( pActEnemy );
		// 方向からアングルに变换する
		//DirToAngle( pActEnemy );
		// 自分ポインタを相手に伝える
		//pEnemyYobi->enemyList[ pEnemyYobi->enemyListCnt ].enemyId = pYobi->myId;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pYobi->myId );
#endif
		pActEnemy->pOther = pActBc[ pYobi->myId ];
		// 相手にムービーフラグを教える
		pEnemyYobi->bmFlag = pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag;
#if 0		
		// ヒットバックしない时、强制ヒットストップのとき
		if( pAct->anim_hit >= ANIM_HIT_STOP_NUM || pYobi->hitStopFlag == TRUE ){
		
			// 回避する时
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
				// 相手を回避へ
				pActEnemy->actNo = BC_ACT_AVOIDANCE; 
			}else{
				// 物理吸收のとき、または物理无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC 
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 何もしない
				}else
				// 受伤が０じゃないとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
					// 相手をデフォルト位置に戾す
					//pActEnemy->fx = ( float )pEnemyYobi->damageX;
					//pActEnemy->fy = ( float )pEnemyYobi->damageY;
					// クリティカルの时
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						//MakeBattleHitMark( pActEnemy, SPR_critica1 );
					}
					// 战闘ヒットマークタスク作成
					//MakeBattleHitMark( pActEnemy, hitSprNo );
				
					// 相手をヒットストップへ
					pActEnemy->actNo = BC_ACT_HIT_STOP;
					
					// 实はヒットバックのとき
					if( !( pAct->anim_hit >= ANIM_HIT_STOP_NUM ) ){
						// ヒット音
						//play_se( pAct->anim_hit - ANIM_HIT_BACK_NUM, pAct->x, pAct->y );
					}else{
						// ヒット音
						//play_se( pAct->anim_hit - ANIM_HIT_STOP_NUM, pAct->x, pAct->y );
					}
				}else{
					// 空振り音
					//play_se( 2, pAct->x, pAct->y );
				}
			}
		}else
#endif
		{
			// Ｂｍフラグにチャット文字列があるかチェック
#ifdef PUK3_WEPON_BREAK
			BattleCheckBmStrGraphic( pYobi->myId, pAct );
#else
			BattleCheckBmStrGraphic( pYobi->myId, &( pYobi->enemyList[ pYobi->enemyListCnt ] ) );
#endif
			// 回避する时
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
				// 相手を回避へ
				pActEnemy->actNo = BC_ACT_AVOIDANCE;
			}else{
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBm );
#endif
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
				CheckIdRange( pYobi->myId );
#endif
				// 魔法吸收のとき、または魔法无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC 
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 何もしない
				}else
				// 受伤が０じゃないとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
					// 相手をデフォルト位置に戾す
					//pActEnemy->fx = ( float )pEnemyYobi->damageX;
					//pActEnemy->fy = ( float )pEnemyYobi->damageY;
					
					// クリティカルの时
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						//MakeBattleHitMark( pActEnemy, SPR_critica1 );
					}
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, hitSprNo );
#ifdef PUK2
					MakeEffectRebirthHitMark( pActBc[ pYobi->myId ], pActEnemy );
#endif
					
					// 相手のＨＰをマナス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp -= pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp < 0 ) pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = 0;
					// 相手が気絶なら
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_DEATH ){
						// 相手を気絶させる时
						pEnemyYobi->bcFlag |= BC_FLAG_DEATH;
						
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AKO1 ){
							// アクションスローフラグＯＮ
							ActSlowFlag = TRUE;
							// 相手の自分のアクションスローフラグＯＮ
							pEnemyYobi->actSlowFlag = TRUE;
						}
					}
					// ヒット音
					//play_se( pAct->anim_hit - ANIM_HIT_BACK_NUM, 320, 240 );
				}
				// 魔法反射のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else
				// 魔法无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else
				// 魔法吸收のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC ){
					// 自分の行动次へ
					//BattleNextActList( pAct );
					// 相手の行动开始
					BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else{
						// 相手のＨＰをプラス
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
						// リミットチェック
						if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
							pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;

						// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
							pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
	#endif
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
							// 战闘ヒットマークタスク作成
							MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
						}
						MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
					}
#else
					// 相手のＨＰをプラス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;
					// ポイント表示タスク作成
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else
#endif
#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
#endif
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else{
					// お目覚めフラグＯＮのとき
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_WAKE_UP ){
						// マーク抹杀
						DeathAction( pEnemyYobi->pAbnormal );
						// ポインタ初期化
						pEnemyYobi->pAbnormal = NULL;
						// 眠りフラグ落とす
						pEnemyYobi->bcFlag &= ~BC_FLAG_ABNORMAL_SLEEP;
					}
					// 相手の行动开始
					BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else{
						// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
							pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
	#endif
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
							// 战闘ヒットマークタスク作成
							MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
						}
						MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
					}
#else
					// ポイント表示タスク作成
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else
#endif
#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
#endif
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
				}
					
			}
			
		}
		// 回避しない时または、攻击力が０じゃないとき
		if( !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE )
			&& pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
		
			// ヒットストップカウンターセット
			pYobi->hitStopCnt = BC_HIT_STOP_CNT * ( ( ( pEnemyYobi->bcFlag & BC_FLAG_DEATH ) ? 1 : 0 ) + 1 );
		}
		// 初期化
		pAct->anim_hit = 0;
	}
	
	// 行动时间のとき
	if( pAct->delta-- <= 0 ){
		// ヒットストップの时カウンターマイナス
		if( pYobi->hitStopCnt > 0 ) pYobi->hitStopCnt--;
		// ヒットストップじゃない时
		//if( pYobi->hitStopCnt == 0 ){
			// アニメーション終わったら抹杀
		//	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		//		DeathAction( pAct );
		//	}
		//}
	}
#if 0
	// エフェクトサイズで分岐
	if( SPR_bigice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_bigice3 ){
		// 战闘彗星作成
		//for( i = 0 ; i < 4 ; i++ ) 
		MakeBattleSuisei( pAct->fx + Rnd( -50, 50 ), pAct->fy + Rnd( -10, 0 ), SPR_ultimate4, FALSE );
	}else
	if( SPR_b_ice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_b_ice3 ){
		// 战闘彗星作成
		//for( i = 0 ; i < 3 ; i++ ) 
		MakeBattleSuisei( pAct->fx + Rnd( -40, 40 ), pAct->fy + Rnd( -8, 0 ), SPR_ultimate4, FALSE );
	}else
	if( SPR_m_ice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_m_ice3 ){
		// 战闘彗星作成
		//for( i = 0 ; i < 2 ; i++ ) 
		MakeBattleSuisei( pAct->fx + Rnd( -30, 30 ), pAct->fy + Rnd( -6, 0 ), SPR_ultimate3, FALSE );
	}else
	if( SPR_s_ice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_ice3 ){
		// 战闘彗星作成
		//for( i = 0 ; i < 5 ; i++ ) 
		MakeBattleSuisei( pAct->fx + Rnd( -20, 20 ), pAct->fy + Rnd( -4, 0 ), SPR_ultimate3, FALSE );
	}
#endif
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
}

// 战闘攻击魔法单体エフェクト处理 ***********************************************/
void BattleEffectHItMagicSingle( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	//int i;
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
#endif
	// 相手がスタンバイじゃないとき返回
	if( pAct->actNo == 0 && pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->actNo != BC_ACT_STANDBY ){
		return;
	}else{
		pAct->actNo = 1;
	}
	
	// 攻击判定ある时
	if( pAct->anim_hit >= ANIM_HIT_BACK_NUM ){
	
		// 相手の予备构造体
		ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];
		BC_YOBI *pEnemyYobi = ( BC_YOBI *)pActEnemy->pYobi;
		
		int hitSprNo;
		// 防御の时
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_GUARD ){
			hitSprNo = SPR_gard;
		}else
		// マジック防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_MAGIC_GUARD ){
			hitSprNo = SPR_m_gard;
		}else
		// スペシャル防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_SPECIAL_GUARD ){
			hitSprNo = SPR_s_gard;
		}else hitSprNo = SPR_hit01 + Rnd( 0, 2 );
								
		// 相手をこっちにの方向に向ける
		//ChangeDir( pActEnemy, pAct, TRUE, 0 );
		// デフォルトの方向へ向ける
		ChangeDefDir( pActEnemy );
		// 方向からアングルに变换する
		//DirToAngle( pActEnemy );
		// 自分ポインタを相手に伝える
		//pEnemyYobi->enemyList[ pEnemyYobi->enemyListCnt ].enemyId = pYobi->myId;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pYobi->myId );
#endif
		pActEnemy->pOther = pActBc[ pYobi->myId ];
		// 相手にムービーフラグを教える
		pEnemyYobi->bmFlag = pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag;
		
		// ヒットバックしない时、强制ヒットストップのとき
		if( pAct->anim_hit >= ANIM_HIT_STOP_NUM || pYobi->hitStopFlag == TRUE ){
		
			// 回避する时
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
				// 相手を回避へ
				pActEnemy->actNo = BC_ACT_AVOIDANCE; 
			}else{
				// 物理吸收のとき、または物理无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC 
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 何もしない
				}else
				// 受伤が０じゃないとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
					// 相手をデフォルト位置に戾す
					//pActEnemy->fx = ( float )pEnemyYobi->damageX;
					//pActEnemy->fy = ( float )pEnemyYobi->damageY;
					// クリティカルの时
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleHitMark( pActEnemy, SPR_critica1 );
					}
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, hitSprNo );
#ifdef PUK2
					MakeEffectRebirthHitMark( pActBc[ pYobi->myId ], pActEnemy );
#endif
				
					// 相手をヒットストップへ
					pActEnemy->actNo = BC_ACT_HIT_STOP;
					
					// 实はヒットバックのとき
					if( !( pAct->anim_hit >= ANIM_HIT_STOP_NUM ) ){
						// ヒット音
						play_se( pAct->anim_hit - ANIM_HIT_BACK_NUM, pAct->x, pAct->y );
					}else{
						// ヒット音
						play_se( pAct->anim_hit - ANIM_HIT_STOP_NUM, pAct->x, pAct->y );
					}
					// ヒットストップカウンターセット
					pYobi->hitStopCnt = BC_HIT_STOP_CNT * ( ( ( pEnemyYobi->bcFlag & BC_FLAG_DEATH ) ? 1 : 0 ) + 1 );
				}else{
					// 空振り音
					//play_se( 2, pAct->x, pAct->y );
				}
			}
		}else{
			// Ｂｍフラグにチャット文字列があるかチェック
#ifdef PUK3_WEPON_BREAK
			BattleCheckBmStrGraphic( pYobi->myId, pAct );
#else
			BattleCheckBmStrGraphic( pYobi->myId, &( pYobi->enemyList[ pYobi->enemyListCnt ] ) );
#endif
			// 回避する时
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
				// 相手を回避へ
				pActEnemy->actNo = BC_ACT_AVOIDANCE;
			}else{
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBm );
#endif
				// 魔法吸收のとき、または魔法无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC 
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 何もしない
				}else
				// 受伤が０じゃないとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
					// 相手をデフォルト位置に戾す
					//pActEnemy->fx = ( float )pEnemyYobi->damageX;
					//pActEnemy->fy = ( float )pEnemyYobi->damageY;
					
					// クリティカルの时
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleHitMark( pActEnemy, SPR_critica1 );
					}
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, hitSprNo );
#ifdef PUK2
					MakeEffectRebirthHitMark( pActBc[ pYobi->myId ], pActEnemy );
#endif
					
					// 相手のＨＰをマナス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp -= pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp < 0 ) pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = 0;
					// 相手が気絶なら
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_DEATH ){
						// 相手を気絶させる时
						pEnemyYobi->bcFlag |= BC_FLAG_DEATH;
						// アクションスローフラグＯＮ
						ActSlowFlag = TRUE;
						// 相手の自分のアクションスローフラグＯＮ
						pEnemyYobi->actSlowFlag = TRUE;
					}
					// ヒット音
					play_se( pAct->anim_hit - ANIM_HIT_BACK_NUM, 320, 240 );
				}
				// 魔法反射のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else
				// 魔法无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else
				// 魔法吸收のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC ){
					// 自分の行动次へ
					//BattleNextActList( pAct );
					// 相手の行动开始
					BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else{
						// 相手のＨＰをマナス
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
						// リミットチェック
						if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
							pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;

						// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
							pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
	#endif
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
							// 战闘ヒットマークタスク作成
							MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
						}
						MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
					}
#else
					// 相手のＨＰをマナス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;
					// ポイント表示タスク作成
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else
#endif
#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
#endif
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
#ifdef PUK2_NEWSKILL_BARRIER
				}else
				// バリアの时
				if ( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag &  BM_FLAG_BARRIER ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );

					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, SPR_efect06 );
#endif
				}else{
					// お目覚めフラグＯＮのとき
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_WAKE_UP ){
						// マーク抹杀
						DeathAction( pEnemyYobi->pAbnormal );
						// ポインタ初期化
						pEnemyYobi->pAbnormal = NULL;
						// 眠りフラグ落とす
						pEnemyYobi->bcFlag &= ~BC_FLAG_ABNORMAL_SLEEP;
					}
					// 相手の行动开始
					BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else{
						// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
							pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
	#endif
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
							// 战闘ヒットマークタスク作成
							MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
						}
						MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
					}
#else
					// ポイント表示タスク作成
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else
#endif
#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
#endif
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
						
				}
					
				// ヒットストップカウンターセット
				pYobi->hitStopCnt = BC_HIT_STOP_CNT * ( ( ( pEnemyYobi->bcFlag & BC_FLAG_DEATH ) ? 1 : 0 ) + 1 );
			}
			
		}
		// 回避しない时または、攻击力が０じゃないとき
		//if( !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE )
		//	&& pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
		
			// ヒットストップカウンターセット
		//	pYobi->hitStopCnt = BC_HIT_STOP_CNT * ( ( ( pEnemyYobi->bcFlag & BC_FLAG_DEATH ) ? 1 : 0 ) + 1 );
		//}
		// 初期化
		pAct->anim_hit = 0;
	}
	
	// 行动时间のときかつ
	if( pAct->delta-- <= 0 ){
		// ヒットストップの时カウンターマイナス
		if( pYobi->hitStopCnt > 0 ) pYobi->hitStopCnt--;
		// ヒットストップじゃない时
		if( pYobi->hitStopCnt == 0 ){
			// アニメーション終わったら抹杀
			if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
				// 火の时は即抹杀
				if( SPR_bigfire1 <= pAct->anim_chr_no && SPR_s_fire3 >= pAct->anim_chr_no ){ 
					DeathAction( pAct );
				}
				// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
				pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
				if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
				else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
				pAct->atr |= ACT_ATR_FLASH_0;
	#endif
				// 点灭カウンターＯＮ
				pAct->fontY++;
			}
			
			// 点灭させる
			if( pAct->fontY > 0 ){
				// カウンターアップ
				if( pAct->fontY++ >= 20 ) DeathAction( pAct );
			}
#if 0
				else{
				// エフェクトサイズで分岐
				if( SPR_bigice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_bigice3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 4 ; i++ ) 
					//if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -50, 50 ), pAct->fy + Rnd( -10, 0 ), SPR_ultimate4, FALSE, DISP_PRIO_B_EFFECT );
					if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -50, 50 ), pAct->fy + Rnd( -10, 0 ), SPR_ultimate2, TRUE, DISP_PRIO_B_EFFECT );
				}else
				if( SPR_b_ice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_b_ice3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 3 ; i++ ) 
					//if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -40, 40 ), pAct->fy + Rnd( -8, 0 ), SPR_ultimate4, FALSE, DISP_PRIO_B_EFFECT );
					if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -40, 40 ), pAct->fy + Rnd( -8, 0 ), SPR_ultimate2, TRUE, DISP_PRIO_B_EFFECT );
				}else
				if( SPR_m_ice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_m_ice3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 2 ; i++ ) 
					//if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -30, 30 ), pAct->fy + Rnd( -6, 0 ), SPR_ultimate3, FALSE, DISP_PRIO_B_EFFECT );
					if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -30, 30 ), pAct->fy + Rnd( -6, 0 ), SPR_ultimate2, TRUE, DISP_PRIO_B_EFFECT );
				}else
				if( SPR_s_ice1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_ice3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 5 ; i++ ) 
					//if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -20, 20 ), pAct->fy + Rnd( -4, 0 ), SPR_ultimate3, FALSE, DISP_PRIO_B_EFFECT );
					if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -20, 20 ), pAct->fy + Rnd( -4, 0 ), SPR_ultimate2, TRUE, DISP_PRIO_B_EFFECT );
				}
				// フラグＯＮ，ＯＦＦ
				if( pAct->fontX >= 1 ) pAct->fontX = 0;
				else pAct->fontX++;
			}
#endif
		}
	}
	

	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
}

// 战闘攻击魔法单体エフェクト落下处理（地） ***********************************************/
void BattleEffectHItMagicSingle2( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	//int i;
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
#endif
	// 相手がスタンバイじゃないとき返回
	if( pAct->actNo == 0 && pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->actNo != BC_ACT_STANDBY ){
		return;
	}else{
		pAct->actNo = 1;
	}
	
	// 相手の予备构造体
	ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];
	BC_YOBI *pEnemyYobi = ( BC_YOBI *)pActEnemy->pYobi;
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pActEnemy->bmpNo );
#else
	LoadBmp( pActEnemy->bmpNo );
#endif
	
#ifdef PUK2_NEWSKILL_BARRIER
	if ( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag &  BM_FLAG_BARRIER ){
		// 近くまできたら
		if( pActEnemy->fy + pActEnemy->anim_y + SpriteInfo[ pActEnemy->bmpNo ].height / 2 - 50 < pAct->fy && pAct->anim_hit == 0 ){
			// 位置を少しずらす
			pAct->fx += BattleMapX / 2;
			pAct->fy += BattleMapY / 2;

			// ここで消灭
			DeathAction( pAct );

			// 战闘ヒットマークタスク作成
			MakeBattleHitMark( pAct, SPR_efect01 );
			// 战闘ヒットマークタスク作成
			MakeBattleHitMark( pActEnemy, SPR_efect06 );

			// 相手の行动开始
			BattleNextActList( pActEnemy );
		}
	}
#endif
	// 地面に到着したら
	if( pActEnemy->fy + pActEnemy->anim_y + SpriteInfo[ pActEnemy->bmpNo ].height / 2 < pAct->fy && pAct->anim_hit == 0 ){
	//if( pActEnemy->fy + pActEnemy->anim_y + SpriteInfo[ pActEnemy->bmpNo ].height < pAct->fy && pAct->anim_hit == 0 ){
		int hitSprNo;
		// 防御の时
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_GUARD ){
			hitSprNo = SPR_gard;
		}else
		// マジック防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_MAGIC_GUARD ){
			hitSprNo = SPR_m_gard;
		}else
		// スペシャル防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_SPECIAL_GUARD ){
			hitSprNo = SPR_s_gard;
		}else hitSprNo = SPR_hit01 + Rnd( 0, 2 );
								
		// 相手をこっちにの方向に向ける
		//ChangeDir( pActEnemy, pAct, TRUE, 0 );
		// デフォルトの方向へ向ける
		ChangeDefDir( pActEnemy );
		// 方向からアングルに变换する
		//DirToAngle( pActEnemy );
		// 自分ポインタを相手に伝える
		//pEnemyYobi->enemyList[ pEnemyYobi->enemyListCnt ].enemyId = pYobi->myId;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pYobi->myId );
#endif
		pActEnemy->pOther = pActBc[ pYobi->myId ];
		// 相手にムービーフラグを教える
		pEnemyYobi->bmFlag = pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag;
#if 0		
		// ヒットバックしない时、强制ヒットストップのとき
		if( pAct->anim_hit >= ANIM_HIT_STOP_NUM || pYobi->hitStopFlag == TRUE ){
		
			// 回避する时
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
				// 相手を回避へ
				pActEnemy->actNo = BC_ACT_AVOIDANCE; 
			}else{
				// 受伤が０じゃないとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
					// 相手をデフォルト位置に戾す
					pActEnemy->fx = ( float )pEnemyYobi->damageX;
					pActEnemy->fy = ( float )pEnemyYobi->damageY;
					// クリティカルの时
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleHitMark( pActEnemy, SPR_critica1 );
					}
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, hitSprNo );
#ifdef PUK2
					MakeEffectRebirthHitMark( pActBc[ pYobi->myId ], pActEnemy );
#endif
				
					// 相手をヒットストップへ
					pActEnemy->actNo = BC_ACT_HIT_STOP;
					
					// 实はヒットバックのとき
					if( !( pAct->anim_hit >= ANIM_HIT_STOP_NUM ) ){
						// ヒット音
						play_se( pAct->anim_hit - ANIM_HIT_BACK_NUM, pAct->x, pAct->y );
					}else{
						// ヒット音
						play_se( pAct->anim_hit - ANIM_HIT_STOP_NUM, pAct->x, pAct->y );
					}
				}else{
					// 空振り音
					play_se( 2, pAct->x, pAct->y );
				}
			}
		}else
#endif
		{
			// Ｂｍフラグにチャット文字列があるかチェック
#ifdef PUK3_WEPON_BREAK
			BattleCheckBmStrGraphic( pYobi->myId, pAct );
#else
			BattleCheckBmStrGraphic( pYobi->myId, &( pYobi->enemyList[ pYobi->enemyListCnt ] ) );
#endif
			// 回避する时
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
				// 相手を回避へ
				pActEnemy->actNo = BC_ACT_AVOIDANCE;
			}else{
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBm );
#endif
				// 魔法吸收のとき、または魔法无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC 
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC
					|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 何もしない
				}else
				// 受伤が０じゃないとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
					// 相手をデフォルト位置に戾す
					//pActEnemy->fx = ( float )pEnemyYobi->damageX;
					//pActEnemy->fy = ( float )pEnemyYobi->damageY;
					
					// 相手のＨＰをマナス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp -= pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp < 0 ) pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = 0;
					// 相手が気絶なら
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_DEATH ){
						// 相手を気絶させる时
						pEnemyYobi->bcFlag |= BC_FLAG_DEATH;
						// アクションスローフラグＯＮ
						ActSlowFlag = TRUE;
						// 相手の自分のアクションスローフラグＯＮ
						pEnemyYobi->actSlowFlag = TRUE;
					}
				}
				// 魔法反射のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else
				// 魔法无效のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
					// 相手の行动开始
					BattleNextActList( pActEnemy );
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else
				// 魔法吸收のとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC ){
					// 自分の行动次へ
					//BattleNextActList( pAct );
					// 相手の行动开始
					BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else{
						// 相手のＨＰをマナス
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
						// リミットチェック
						if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
							pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;
	
						// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
							pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
	#endif
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
							// 战闘ヒットマークタスク作成
							MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
						}
						MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
					}
#else
					// 相手のＨＰをマナス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;
					// ポイント表示タスク作成
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else
#endif
#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
#endif
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
					// 次ぎの敌セット
					//BattleNextEnemyList( pAct );
					// 合体攻击のとき
					if( pActBm->actNo == BM_ACT_CMB ){
						// コンボマスターに伝える
						( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
						// 强制ヒットストップフラグＯＦＦ
						//pYobi->hitStopFlag = FALSE;
					}
				}else{
					// お目覚めフラグＯＮのとき
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_WAKE_UP ){
						// マーク抹杀
						DeathAction( pEnemyYobi->pAbnormal );
						// ポインタ初期化
						pEnemyYobi->pAbnormal = NULL;
						// 眠りフラグ落とす
						pEnemyYobi->bcFlag &= ~BC_FLAG_ABNORMAL_SLEEP;
					}
					// クリティカルの时
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleHitMark( pActEnemy, SPR_critica1 );
					}
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, hitSprNo );
#ifdef PUK2
					MakeEffectRebirthHitMark( pActBc[ pYobi->myId ], pActEnemy );
#endif
					// 相手の行动开始
					BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else{
						// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
							pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
						MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
							pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
	#endif
						if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
							// 战闘ヒットマークタスク作成
							MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
						}
						MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
					}
#else
					// ポイント表示タスク作成
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
						MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
					}else
#endif
#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
#endif
#ifdef PUK2
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
						
					// エフェクトサイズで分岐
					if( SPR_bigstone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_bigstone3 ){
						// ヒット音
						play_se( 263, pAct->x, pAct->y );
						// ヒット音
						play_se( 132, pAct->x, pAct->y );
					}else
					if( SPR_b_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_b_stone3 ){
						// ヒット音
						play_se( 263, pAct->x, pAct->y );
					}else
					if( SPR_m_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_m_stone3 ){
						// ヒット音
						play_se( 132, pAct->x, pAct->y );
					}else{
						// ヒット音
						play_se( 131, pAct->x, pAct->y );
					}
				}
			}
			
			// 回避しない时または、攻击力が０じゃないとき
			if( !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE )
				&& pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
				// ヒットストップカウンターセット
				pYobi->hitStopCnt = BC_HIT_STOP_CNT * ( ( ( pEnemyYobi->bcFlag & BC_FLAG_DEATH ) ? 1 : 0 ) + 1 );
			
			}
			// 初期化
			pAct->anim_hit = 1;
		}
	}
	
	// 行动时间のとき
	if( pAct->delta-- <= 0 ){
		
		// 初期化
		if( pAct->delta == -1 ){ 
			int i, work;
			// エフェクトサイズで分岐
			if( SPR_bigstone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_bigstone3 ){
				// 飞行音
				play_se( 262, pAct->x, pAct->y );
			}else
			if( SPR_b_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_b_stone3 ){
				// 飞行音
				play_se( 262, pAct->x, pAct->y );
			}else
			if( SPR_m_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_m_stone3 ){
				// 飞行音
				play_se( 262, pAct->x, pAct->y );
			}else{
				// 飞行音
				play_se( 262, pAct->x, pAct->y );
			}
			// アニメーションスタート位置ランダムにする
			work = Rnd( 0, 32 );
			// 終わるまでループ
			for( i = 0 ; i < work ; i++ ){
				// アニメーション
				pattern( pAct, ANM_LOOP );
			}
		}
		
		// ヒットストップの时カウンターマイナス
		if( pYobi->hitStopCnt > 0 ){ 
			pYobi->hitStopCnt--;
			// 魔法反射、魔法无效、魔法吸收じゃ无い时
			if( !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC ) 
				&& !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC )
				&& !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC ) ){
				// 地震フラグＯＮ
				BattleMapAcceleMax = pYobi->techId / 2 + 1;
				BattleMapQuakeFlag = TRUE;
			}
		}
		// ヒットストップじゃない时
		if( pYobi->hitStopCnt == 0 ){
			// アニメーション
			pattern( pAct, ANM_LOOP );
			
			// 目的の方向に向ける
			ChangeDir( pAct, pActEnemy->fx, pActEnemy->fy + pActEnemy->anim_y + SpriteInfo[ pActEnemy->bmpNo ].height / 2, FALSE, 0 );
			// 目的地に移动する
			MoveDir( pAct );
			pAct->speed += 0.5F;

#if 1		
			// まだ当たってない时
			if( pAct->anim_hit == 0 ){
				// エフェクトサイズで分岐
				if( SPR_bigstone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_bigstone3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 4 ; i++ ) MakeBattleSuisei( pAct->fx + Rnd( -30, 30 ), pAct->fy + Rnd( -60, 0 ), SPR_ultimate3, FALSE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx, pAct->fy, SPR_ultimate4, FALSE, DISP_PRIO_B_EFFECT );
				}else
				if( SPR_b_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_b_stone3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 3 ; i++ ) MakeBattleSuisei( pAct->fx + Rnd( -20, 20 ), pAct->fy + Rnd( -40, 0 ), SPR_ultimate3, FALSE );
					if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx, pAct->fy, SPR_ultimate4, FALSE, DISP_PRIO_B_EFFECT );
				}else
				if( SPR_m_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_m_stone3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 2 ; i++ ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -20, 0 ), SPR_ultimate3, FALSE );
					if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx, pAct->fy, SPR_ultimate3, FALSE, DISP_PRIO_B_EFFECT );
					pAct->fontX++;
				}
				//else
				//if( SPR_s_stone1 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_stone3 ){
					// 战闘彗星作成
					//for( i = 0 ; i < 5 ; i++ ) 
				//	if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx, pAct->fy - 30, SPR_ultimate3, FALSE, DISP_PRIO_B_EFFECT );
				//}
			}
			// フラグＯＮ，ＯＦＦ
			if( pAct->fontX >= 2 ) pAct->fontX = 0;
			else pAct->fontX++;
#endif

			// 战闘残像タスク作成
			//MakeBattleZanzou( pAct );

			// ヒットしてたら抹杀
			if( pAct->anim_hit == 1 ){ 
				DeathAction( pAct );
				// 地震フラグＯＮ
				BattleMapQuakeFlag = FALSE;
			}
		}
	}
	
	// 表示优先度は攻击对象と同じ
	pAct->dispPrio = pActEnemy->dispPrio + 20;
			
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
}

// 战闘攻击魔法エフェクト（风） ***********************************************/
void BattleEffectHItMagicWind( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	//int i;
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
#endif
	// 相手がスタンバイじゃないとき返回
	if( pAct->actNo == 0 && pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->actNo != BC_ACT_STANDBY ){
		return;
	}else{
		pAct->actNo = 1;
	}
	
#ifdef PUK2_NEWSKILL_BARRIER
	if ( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag &  BM_FLAG_BARRIER ){
		// 近くまできたら
		if( pAct->anim_hit == 0 && CheckDistance( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ] ) <= B_ATTACK_DIST ){
			// 相手の予备构造体
			ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];

			// 位置を少しずらす
			pAct->fx += BattleMapX;
			pAct->fy += BattleMapY;

			// ここで消灭
			DeathAction( pAct );

			// 战闘ヒットマークタスク作成
			MakeBattleHitMark( pAct, SPR_efect07 );
			// 战闘ヒットマークタスク作成
			MakeBattleHitMark( pActEnemy, SPR_efect06 );

			// 相手の行动开始
			BattleNextActList( pActEnemy );
		}
	}
#endif
	// 到着チェック
	if( pAct->anim_hit == 0 && CheckDistance( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ] ) <= B_NEAR_DIST ){
		
		// 相手の予备构造体
		ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];
		BC_YOBI *pEnemyYobi = ( BC_YOBI *)pActEnemy->pYobi;
	
		int hitSprNo;
		// 防御の时
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_GUARD ){
			hitSprNo = SPR_gard;
		}else
		// マジック防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_MAGIC_GUARD ){
			hitSprNo = SPR_m_gard;
		}else
		// スペシャル防御のとき
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_SPECIAL_GUARD ){
			hitSprNo = SPR_s_gard;
		}else hitSprNo = SPR_hit01 + Rnd( 0, 2 );
								
		// デフォルトの方向へ向ける
		//ChangeDefDir( pActEnemy );
		// 方向からアングルに变换する
		//DirToAngle( pActEnemy );
		// 自分ポインタを相手に伝える
		//pEnemyYobi->enemyList[ pEnemyYobi->enemyListCnt ].enemyId = pYobi->myId;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pYobi->myId );
#endif
		pActEnemy->pOther = pActBc[ pYobi->myId ];
		// 相手にムービーフラグを教える
		pEnemyYobi->bmFlag = pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag;
		
		// Ｂｍフラグにチャット文字列があるかチェック
#ifdef PUK3_WEPON_BREAK
		BattleCheckBmStrGraphic( pYobi->myId, pAct );
#else
		BattleCheckBmStrGraphic( pYobi->myId, &( pYobi->enemyList[ pYobi->enemyListCnt ] ) );
#endif
		// 回避する时
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
			// 相手を回避へ
			pActEnemy->actNo = BC_ACT_AVOIDANCE;
		}else{
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBm );
#endif
			// 魔法吸收のとき、または魔法无效のとき
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC 
				|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC
				|| pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
				// 何もしない
			}else
			// 受伤が０じゃないとき
			if( pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
				// 相手をデフォルト位置に戾す
				//pActEnemy->fx = ( float )pEnemyYobi->damageX;
				//pActEnemy->fy = ( float )pEnemyYobi->damageY;
				
				// 相手のＨＰをマナス
				pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp -= pYobi->enemyList[ pYobi->enemyListCnt ].damage;
				// リミットチェック
				if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp < 0 ) pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = 0;
				// 相手が気絶なら
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_DEATH ){
					// 相手を気絶させる时
					pEnemyYobi->bcFlag |= BC_FLAG_DEATH;
					// アクションスローフラグＯＮ
					ActSlowFlag = TRUE;
					// 相手の自分のアクションスローフラグＯＮ
					pEnemyYobi->actSlowFlag = TRUE;
				}
			}
			// 魔法反射のとき
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REFLECTION_MAGIC ){
				// 相手の行动开始
				BattleNextActList( pActEnemy );
				// 次ぎの敌セット
				//BattleNextEnemyList( pAct );
				// 合体攻击のとき
				if( pActBm->actNo == BM_ACT_CMB ){
					// コンボマスターに伝える
					( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
					// 强制ヒットストップフラグＯＦＦ
					//pYobi->hitStopFlag = FALSE;
				}
			}else
			// 魔法无效のとき
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
				// 相手の行动开始
				BattleNextActList( pActEnemy );
				// 次ぎの敌セット
				//BattleNextEnemyList( pAct );
				// 合体攻击のとき
				if( pActBm->actNo == BM_ACT_CMB ){
					// コンボマスターに伝える
					( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
					// 强制ヒットストップフラグＯＦＦ
					//pYobi->hitStopFlag = FALSE;
				}
			}else
			// 魔法吸收のとき
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_ABSORB_MAGIC ){
				// 自分の行动次へ
				//BattleNextActList( pAct );
				// 相手の行动开始
				BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
					MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
				}else{
					// 相手のＨＰをマナス
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
					// リミットチェック
					if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
						pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;

					// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
	#endif
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
				}
#else
				// 相手のＨＰをマナス
				pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp += pYobi->enemyList[ pYobi->enemyListCnt ].damage;
				// リミットチェック
				if( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp > pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp ) 
					pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->hp = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->maxHp;
				// ポイント表示タスク作成
#ifdef PUK2
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
					pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
					MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
				}else
#endif
#ifdef PUK3_MONSTER_HELPER
				MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
					pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG,
					pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
				MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
					pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_GREEN, FONT_KIND_BIG );
#endif
#ifdef PUK2
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
					// 战闘ヒットマークタスク作成
					MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
				}
				MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
				// 次ぎの敌セット
				//BattleNextEnemyList( pAct );
				// 合体攻击のとき
				if( pActBm->actNo == BM_ACT_CMB ){
					// コンボマスターに伝える
					( ( BM_YOBI *)pActBm->pYobi )->cmbCnt++;
					// 强制ヒットストップフラグＯＦＦ
					//pYobi->hitStopFlag = FALSE;
				}
			}else{
				// お目覚めフラグＯＮのとき
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_WAKE_UP ){
					// マーク抹杀
					DeathAction( pEnemyYobi->pAbnormal );
					// ポインタ初期化
					pEnemyYobi->pAbnormal = NULL;
					// 眠りフラグ落とす
					pEnemyYobi->bcFlag &= ~BC_FLAG_ABNORMAL_SLEEP;
				}
				// クリティカルの时
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pActEnemy, SPR_critica1 );
				}
				// 战闘ヒットマークタスク作成
				MakeBattleHitMark( pActEnemy, hitSprNo );
#ifdef PUK2
				MakeEffectRebirthHitMark( pActBc[ pYobi->myId ], pActEnemy );
#endif
				// 相手をこっちにの方向に向ける
				ChangeDir( pActEnemy, pAct, TRUE, 0 );
				// 相手の行动开始
				BattleNextActList( pActEnemy );
#ifdef PUK3_RIBIRTH_GUARD_NODAMAGE
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
					MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
				}else{
					// ポイント表示タスク作成
	#ifdef PUK3_MONSTER_HELPER
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
						pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
	#else
					MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
						pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
	#endif
					if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
						// 战闘ヒットマークタスク作成
						MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
					}
					MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
				}
#else
				// ポイント表示タスク作成
#ifdef PUK2
				if( pYobi->enemyList[ pYobi->enemyListCnt ].damage == 0 &&
					pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
					MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
				}else
#endif
#ifdef PUK3_MONSTER_HELPER
				MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
					pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG,
					pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag );
#else
				MakeBattlePointDisp( pActEnemy->x, pActEnemy->y - 80, 
					pYobi->enemyList[ pYobi->enemyListCnt ].damage, FONT_PAL_RED, FONT_KIND_BIG );
#endif
#ifdef PUK2
				if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_CRITICAL ){
					// 战闘ヒットマークタスク作成
					MakeBattleJumpDisp( pActEnemy, CG_CRITICAL, CRI_X, CRI_Y );
				}
				MakeElementWL( pActBc[ pYobi->myId ], pActEnemy );
#endif
#endif
				// エフェクトサイズで分岐
				if( pAct->anim_chr_no == SPR_bigwind1 ){
					// ヒット音
					play_se( 269, pAct->x, pAct->y );
				}else
				if( pAct->anim_chr_no == SPR_b_wind1 ){
					// ヒット音
					play_se( 269, pAct->x, pAct->y );
				}else
				if( pAct->anim_chr_no == SPR_m_wind1 ){
					// ヒット音
					play_se( 268, pAct->x, pAct->y );
				}else
				if( pAct->anim_chr_no == SPR_s_wind1 ){
					// ヒット音
					play_se( 268, pAct->x, pAct->y );
				}
			}
		}
		
		// 回避しない时または、攻击力が０じゃないとき
		if( !( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE )
			&& pYobi->enemyList[ pYobi->enemyListCnt ].damage != 0 ){
		
			// ヒットストップカウンターセット
			pYobi->hitStopCnt = BC_HIT_STOP_CNT * ( ( ( pEnemyYobi->bcFlag & BC_FLAG_DEATH ) ? 1 : 0 ) + 1 );
		}
		// 初期化
		pAct->anim_hit = 1;
	}
	
	// 行动时间のとき
	if( pAct->delta-- <= 0 ){
		// 初期化
		if( pAct->delta == -1 ){ 
			// エフェクトサイズで分岐
			if( pAct->anim_chr_no == SPR_bigwind1 ){
				// 発生音
				play_se( 269, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_b_wind1 ){
				// 発生音
				play_se( 268, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_m_wind1 ){
				// 発生音
				play_se( 268, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_s_wind1 ){
				// 発生音
				play_se( 268, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}
		}
		// 扩大
		//pAct->scaleX += 0.1F;
		//pAct->scaleY += 0.1F;
		// リミットチェック
		//if( pAct->scaleX > 1.0F ){
			// 调节
		//	pAct->scaleX = 1.0F;
		//	pAct->scaleY = 1.0F;
		//}
		// 飞行音
		//if( pAct->delta == -1 ) play_se( 11, pAct->x, pAct->y );
		
		// ヒットストップの时カウンターマイナス
		if( pYobi->hitStopCnt > 0 ){ 
			pYobi->hitStopCnt--;
			// 缩小
			//pAct->scaleX -= 0.1F;
			//pAct->scaleY -= 0.1F;
			// 小さくなったら抹杀
			//if( pAct->scaleX <= 0 ){
			//	DeathAction( pAct );
			//}
			// 地震フラグＯＮ
			//BattleMapAcceleMax = pYobi->techId / 2 + 1;
			//BattleMapQuakeFlag = TRUE;
		}
		// ヒットストップじゃない时
		if( pYobi->hitStopCnt == 0 ){
			// アニメーションスピード
			//pAct->anim_speed = 400;
			// アニメーション
			pattern( pAct, ANM_LOOP );
			
			// ヒットして无いとき
			if( pAct->anim_hit == 0 ){ 
			
				// ハンドリング设定
				pAct->fontdfX += 0.1F;
				
				// 目的の方向に向ける
				//ChangeDir( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], FALSE, pAct->speed );
				ChangeDir( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], FALSE, pAct->fontdfX );
				//pAct->speed += 0.1F;
			}else{
				// 缩小
				//pAct->scaleX -= 0.1F;
				//pAct->scaleY -= 0.1F;
				// 小さくなったら抹杀
				//if( pAct->scaleX <= 0 ){
				//	DeathAction( pAct );
				//}
				// 画面外に出たら
				if( pAct->fx < - 200 || pAct->fx > DEF_APPSIZEX + 200
					|| pAct->fy < - 200 || pAct->fy > DEF_APPSIZEY + 200 ){
					// 抹杀
					DeathAction( pAct );
				}
			}
			// 目的地に移动する
			MoveDir( pAct );

#if 1
			// エフェクトサイズで分岐
			if( pAct->anim_chr_no == SPR_bigwind1 ){
				// 烟作成
				//if( Rnd( 0, ( 9 - ( int )pAct->speed ) / 2 ) == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -10, 10 ), SPR_ultimate4, TRUE );
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -10, 10 ), SPR_ultimate4, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -10, 10 ), SPR_ultimate4, TRUE, DISP_PRIO_B_EFFECT2 );
			}else
			if( pAct->anim_chr_no == SPR_b_wind1 ){
				// 烟作成
				//if( Rnd( 0, 9 - pAct->speed ) == 0 ) 
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -6, 6 ), pAct->fy + Rnd( -6, 6 ), SPR_ultimate4, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -6, 6 ), pAct->fy + Rnd( -6, 6 ), SPR_ultimate4, TRUE, DISP_PRIO_B_EFFECT2 );
			}else
			if( pAct->anim_chr_no == SPR_m_wind1 ){
				// 烟作成
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -4, 4 ), pAct->fy + Rnd( -4, 4 ), SPR_ultimate3, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -4, 4 ), pAct->fy + Rnd( -4, 4 ), SPR_ultimate3, TRUE, DISP_PRIO_B_EFFECT2 );
				pAct->fontX++;
			}else
			if( pAct->anim_chr_no == SPR_s_wind1 ){
				// 烟作成
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -2, 2 ), pAct->fy + Rnd( -2, 2 ), SPR_ultimate3, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -2, 2 ), pAct->fy + Rnd( -2, 2 ), SPR_ultimate3, TRUE, DISP_PRIO_B_EFFECT2 );
			}
			// フラグＯＮ，ＯＦＦ
			if( pAct->fontX >= 2 ) pAct->fontX = 0;
			else pAct->fontX++;
#endif
		}
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
}


// 战闘エフェクト作成 *******************************************************/
// 	ACTION *pOther,	亲アクションポインタ
//	int offsetY, 	OFFセットＹ座标
//	int effectNo, 	エフェクト番号
//	int flashFlag, 	点灭フラグ		０：なし　１：あり
//	int waitFlag	終了待ちするかフラグ	０：待たない　１：待つ
// *************************************************************************/
ACTION *MakeBattleEffect( ACTION *pOther, int offsetY, int effectNo, int flashFlag, int waitFlag )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	ACTION *pActEnemy;
	BC_YOBI *pOtherYobi = ( BC_YOBI *)pOther->pYobi;
#ifdef PUK3_WEPON_BREAK
	int enemyId, bmFlag, newGraNo, ridePetGraNo, putonGraNo;
#endif
	
	// エフェクトサイズテーブル
	int effectSizeTbl[] = { 3, 3, 3, 2, 2, 2, 1, 1, 1, 0 };
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pOther );
#endif
	// エフェクト番号で分岐
	switch( effectNo ){
	
		case BATTLE_EFFECT_COUNTER:		// カウンターの时 *****************************
		case BATTLE_EFFECT_DRAIN:		// ドレイン实行时 *****************************
		case BATTLE_EFFECT_DRAIN_2:		// ドレイン吸收时 *****************************
		case BATTLE_EFFECT_LP_INCREASE:	// 体力回复エフェクト *************************
		//case BATTLE_EFFECT_LP_INCREASE_2:	// 体力回复エフェクト *************************
		case BATTLE_EFFECT_SKILL_GENERATE:	// スキル発生エフェクト *************************
		case BATTLE_EFFECT_PET_IN:			// ペット戾すエフェクト **********************************
		case BATTLE_EFFECT_PET_OUT:			// ペット出すエフェクト **********************************
		case BATTLE_EFFECT_ITEM_GENERATE:	// アイテム仕样エフェクト *********************************
		
		case BATTLE_EFFECT_CROSS_COUNTER:	// クロスカウンター ************************
		case BATTLE_EFFECT_ABNORMAL_STATUS:		// 状态异常实行 ************************
		case BATTLE_EFFECT_DEFENSE_MAGIC:		// 防御系魔法实行（反射、吸收、无效） ************************
		case BATTLE_EFFECT_REFLECTION_PHYSICS:		// 物理反射エフェクト ************************
		case BATTLE_EFFECT_ABSORB_PHYSICS:			// 物理吸收エフェクト ************************
		case BATTLE_EFFECT_INEFFECTIVE_PHYSICS:		// 物理无效エフェクト ************************
		case BATTLE_EFFECT_REFLECTION_MAGIC:			// 魔法反射エフェクト ************************
		case BATTLE_EFFECT_ABSORB_MAGIC:				// 魔法吸收エフェクト ************************
		case BATTLE_EFFECT_INEFFECTIVE_MAGIC:		// 魔法无效エフェクト ************************
		case BATTLE_EFFECT_CARD:					// 封印カードエフェクト
		case BATTLE_EFFECT_TERROR:					// 即死エフェクト
		case BATTLE_EFFECT_SACRIFICE:				// サクリファイス
		case BATTLE_EFFECT_LP_RECOVER:				// 体力再生エフェクト（継続时）
		case BATTLE_EFFECT_CONSENTRATION:			// 精神统一エフェクト
		case BATTLE_EFFECT_REVERSE_TYPE:			// 属性反転エフェクト（实行时）作成
		case BATTLE_EFFECT_PARAMETER:				// パラメータ变更エフェクト
#ifdef _TEST_TECH_YUK
		case BATTLE_EFFECT_SUPERSKILL:				// スー布スキル発生エフェクト
#endif /* _TEST_TECH_YUK */
#ifdef PUK2_NEWSKILL_BARRIER
		case BATTLE_EFFECT_BARRIER:					// バリア発生エフェクト
#endif
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) return NULL;
		
		// 实行关数
		pAct->func = BattleEffectCounter;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
		
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		
		// 中心座标记忆する
		pAct->fx = pOther->fx;
		//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
		pAct->fy = pOther->fy + ( float )offsetY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		
		// エフェクト番号で分岐
		// カウンターのとき
		if( effectNo == BATTLE_EFFECT_COUNTER ) pAct->anim_chr_no = SPR_counter;
		// ドレインの発生时
		else if( effectNo == BATTLE_EFFECT_DRAIN ) pAct->anim_chr_no = SPR_dorein_big1;
		// ドレインの吸收时
		else if( effectNo == BATTLE_EFFECT_DRAIN_2 ){
			pAct->anim_chr_no = SPR_dorein_big2 + effectSizeTbl[ pOtherYobi->techId ];
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
		}else
		// スキル発生エフェクト
		if( effectNo == BATTLE_EFFECT_SKILL_GENERATE ){
			pAct->anim_chr_no = SPR_sk_start_big + effectSizeTbl[ pOtherYobi->techId ];
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
		}else
		// ペット戾すエフェクト
		if( effectNo == BATTLE_EFFECT_PET_IN ){ 
			pAct->anim_chr_no = SPR_fuuin;
			// 实行关数
			pAct->func = BattleEffectLoop;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
		}else
		// ペット出すエフェクト
		if( effectNo == BATTLE_EFFECT_PET_OUT ){ 
			pAct->anim_chr_no = SPR_tukaima;
			// 实行关数
			pAct->func = BattleEffectLoop;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
		}else
		// アイテム仕样エフェクト
		if( effectNo == BATTLE_EFFECT_ITEM_GENERATE ){ 
			pAct->anim_chr_no = SPR_i_start;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
		}else
		// クロスカウンター
		if( effectNo == BATTLE_EFFECT_CROSS_COUNTER ){ 
			pAct->anim_chr_no = SPR_kurosu;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
		}else
		// 状态异常实行 ************************
		if( effectNo == BATTLE_EFFECT_ABNORMAL_STATUS ){ 
			pAct->anim_chr_no = SPR_state_big1 + effectSizeTbl[ pOtherYobi->techId ];
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
		}else
		// 防御系魔法实行（反射、吸收、无效） ************************
		if( effectNo == BATTLE_EFFECT_DEFENSE_MAGIC ){ 
			pAct->anim_chr_no = SPR_kyoujitu_big + effectSizeTbl[ pOtherYobi->techId ];
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
		}else
		// 物理反射エフェクト ************************
		if( effectNo == BATTLE_EFFECT_REFLECTION_PHYSICS ){ 
			pAct->anim_chr_no = SPR_bhansya_big;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
		}else
		// 物理吸收エフェクト ************************
		if( effectNo == BATTLE_EFFECT_ABSORB_PHYSICS ){ 
			pAct->anim_chr_no = SPR_bkyuu_big;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// 中心に表示
			pAct->fy = pOther->fy + pOther->anim_y / 2;
		}else
		// 物理无效エフェクト ************************
		if( effectNo == BATTLE_EFFECT_INEFFECTIVE_PHYSICS ){ 
			pAct->anim_chr_no = SPR_bmukou_big;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
		}else
		// 魔法反射エフェクト ************************
		if( effectNo == BATTLE_EFFECT_REFLECTION_MAGIC ){ 
			pAct->anim_chr_no = SPR_mhansya_big;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
		}else
		// 魔法吸收エフェクト ************************
		if( effectNo == BATTLE_EFFECT_ABSORB_MAGIC ){ 
			pAct->anim_chr_no = SPR_mkyuu_big;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// 中心に表示
			pAct->fy = pOther->fy + pOther->anim_y / 2;
		}else
		// 魔法无效エフェクト ************************
		if( effectNo == BATTLE_EFFECT_INEFFECTIVE_MAGIC ){ 
			pAct->anim_chr_no = SPR_mmukou_big;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
		}else
		// 封印カードエフェクト
		if( effectNo == BATTLE_EFFECT_CARD ){
			// アニメーション番号
			pAct->anim_chr_no = SPR_card;
			// 实行关数
			pAct->func = BattleEffectCard;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_B_EFFECT_BAK2;
			// スケール最小
			pAct->scaleX = 0;
			pAct->scaleY = 0;
			
		}else
		// 即死エフェクト
		if( effectNo == BATTLE_EFFECT_TERROR ){
			// アニメーション番号
			pAct->anim_chr_no = SPR_sokusi;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
			
		}else
		// サクリファイス
		if( effectNo == BATTLE_EFFECT_SACRIFICE ){
			// アニメーション番号
			pAct->anim_chr_no = SPR_sakuri;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			//pAct->fy = pOther->fy - ( SpriteInfo[ pOther->bmpNo ].height + pOther->anim_y ) / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
			//pAct->fy = pOther->fy;
			
		}else
		// 体力再生エフェクト（継続时）
		if( effectNo == BATTLE_EFFECT_LP_RECOVER ){
			//pAct->anim_chr_no = SPR_lp_big + effectSizeTbl[ pOtherYobi->techId ];
			pAct->anim_chr_no = SPR_lpsai_big + effectSizeTbl[ pOtherYobi->techId ];
			//pAct->anim_chr_no = SPR_lpsai_big + Rnd( 0, 3 );
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// 特大の时だけ
			if( pAct->anim_chr_no == SPR_lpsai_big ){
				// 体力回复补助エフェクト
				MakeBattleEffect( pAct, 0, BATTLE_EFFECT_LP_INCREASE_2, 1, 0 );
			}
		}else
		// 属性反転エフェクト（实行时）作成
		if( effectNo == BATTLE_EFFECT_REVERSE_TYPE ){
			pAct->anim_chr_no = SPR_zokuhan2_big + effectSizeTbl[ pOtherYobi->techId ];
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
		}else
		// 精神统一エフェクト
		if( effectNo == BATTLE_EFFECT_CONSENTRATION ){
			// アニメーション番号
			pAct->anim_chr_no = SPR_seisin_big + effectSizeTbl[ pOtherYobi->techId ];
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			// 中心に表示
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2;
			//pAct->fy = pOther->fy - ( SpriteInfo[ pOther->bmpNo ].height + pOther->anim_y ) / 2;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
			//pAct->fy = pOther->fy;
			
		}else
		// 体力回复エフェクト
		if( effectNo == BATTLE_EFFECT_LP_INCREASE ){
			pAct->anim_chr_no = SPR_lp_big + effectSizeTbl[ pOtherYobi->techId ];
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// 特大の时だけ
			if( pAct->anim_chr_no == SPR_lp_big ){
				// 体力回复补助エフェクト
				MakeBattleEffect( pAct, 0, BATTLE_EFFECT_LP_INCREASE_2, 1, 0 );
			}
		}else
		// 状态异常实行 ************************
		if( effectNo == BATTLE_EFFECT_PARAMETER ){ 
			pAct->anim_chr_no = SPR_nami ;
			// 实行关数
			pAct->func = BattleEffectCounter;
			// 点灭フラグＯＮ
//			pAct->atr |= ACT_ATR_FLASH_0;
#ifdef _TEST_TECH_YUK
		}else
		// スー布スキル発生エフェクト
		if( effectNo == BATTLE_EFFECT_SUPERSKILL ){ 
			pAct->anim_chr_no = SPR_life02;
			pAct->fy = pOther->fy + pOther->anim_y / 2;
#endif /* _TEST_TECH_YUK */
		}
#ifdef PUK2_NEWSKILL_BARRIER
		else
		// バリア発生エフェクト ************************
		if( effectNo == BATTLE_EFFECT_BARRIER ){ 
			pAct->anim_chr_no = SPR_efect06;
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// 中心位置をヒットマークと同じ计算で调整
			pAct->fx = (float)( pOther->x + Rnd( 0, SpriteInfo[ pOther->bmpNo ].width / 2 ) - SpriteInfo[ pOther->bmpNo ].width / 4 );
			pAct->fy = (float)( pOther->y + pOther->anim_y / 4 - Rnd( 0, pOther->anim_y / 3 ) );
		}
#endif
		// アニメーションで画像番号を取り出す。
		//pattern( pAct, ANM_NO_LOOP );
		
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
			
		break;
		
		
		case BATTLE_EFFECT_LP_RECOVER_GENERATE:	// 体力再生発生エフェクト
		
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectCounter;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		
		// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
		pAct->atr |= ACT_ATR_FLASH_0;
	#endif
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		// anim_tbl.h の番号
		//pAct->anim_chr_no = SPR_life00;
		pAct->anim_chr_no = SPR_life00_big + effectSizeTbl[ pOtherYobi->techId ];
		//pAct->anim_chr_no = SPR_life02;
		//pAct->anim_chr_no = SPR_life03;
		//pAct->anim_chr_no = SPR_life04;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif

		// アニメーションで画像番号を取り出す。
		//pattern( pAct, ANM_NO_LOOP );
		
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
		
		// 中心座标记忆する
		pAct->fx = pOther->fx;
		pAct->fy = pOther->fy + pOther->anim_y / 2;
		//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
		//pAct->fy = pOther->fy + ( float )offsetY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		
		case BATTLE_EFFECT_GENERATE_2:	// 発生エフェクト（回転）***************************
		
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectCounter;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		
		// anim_tbl.h の番号
		pAct->anim_chr_no = SPR_life02;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif

		// アニメーションで画像番号を取り出す。
		//pattern( pAct, ANM_NO_LOOP );
		
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
		
		// 中心座标记忆する
		pAct->fx = pOther->fx;
		//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
		pAct->fy = pOther->fy + ( float )offsetY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		
		case BATTLE_EFFECT_GENERATE_3:	// 発生エフェクト（波纹）****************************
		
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectCounter;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		
		// anim_tbl.h の番号
		pAct->anim_chr_no = SPR_life04;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif

		// アニメーションで画像番号を取り出す。
		//pattern( pAct, ANM_NO_LOOP );
		
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
		
		// 中心座标记忆する
		pAct->fx = pOther->fx;
		//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
		pAct->fy = pOther->fy + ( float )offsetY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		
		case BATTLE_EFFECT_GENERATE_4:	// 発生エフェクト（下から上へループ）************************
		
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectCounter;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		
		// anim_tbl.h の番号
		pAct->anim_chr_no = SPR_life03;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif

		// アニメーションで画像番号を取り出す。
		//pattern( pAct, ANM_NO_LOOP );
		
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
		
		// 中心座标记忆する
		pAct->fx = pOther->fx;
		//pAct->y = pOther->y - SpriteInfo[ pOther->bmpNo ].height / 2;
		pAct->fy = pOther->fy + ( float )offsetY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		
		case BATTLE_EFFECT_DEAD:	// 死亡エフェクト **************************************
		
		// アクションポインタ取得、タスク优先顺位も设定
		pAct = GetAction( PRIO_CHR + 10 * ( ( BC_YOBI* )pOther->pYobi )->myId - 1, NULL );
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectDead;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
		// 座标记忆する
		pAct->fx = ( float )( pOther->x + pOther->anim_x + SpriteInfo[ pOther->bmpNo ].width / 2 );
		pAct->fy = ( float )( pOther->y + offsetY );
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		case BATTLE_EFFECT_ANABIOSIS:	// 苏生エフェクト **************************************
		case BATTLE_EFFECT_STATUS_RECOVER:	// 状态异常回复エフェクト **************************************
		
		// アクションポインタ取得
#ifdef PUK2_MEMCHECK
		pAct = GetAction( PRIO_CHR, sizeof( EFFECT_ANABIOSIS ), ACT_T_EFFECT_ANABIOSIS );
#else
		pAct = GetAction( PRIO_CHR, sizeof( EFFECT_ANABIOSIS ) );
#endif
		if( pAct == NULL ) return NULL;
		
		// 苏生の时
		if( effectNo == BATTLE_EFFECT_ANABIOSIS ){
			// 発生音
			play_se( 280, pOther->x, pOther->y );
			// 实行关数
			pAct->func = BattleEffectAnabiosis;
		}else{
			// 発生音
			play_se( 281, pOther->x, pOther->y );
			// 实行关数
			pAct->func = BattleEffectStatusRecover;
		}
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
		// 座标记忆する
		pAct->fx = ( float )( pOther->x + pOther->anim_x + SpriteInfo[ pOther->bmpNo ].width / 2 );
		//pAct->x = pOther->x;
		pAct->fy = ( float )( pOther->y + offsetY );
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		case BATTLE_EFFECT_MAGIC_GENERATE:	// 魔法発生エフェクト **************************************
		
		// アクションポインタ取得
#ifdef PUK2_MEMCHECK
		pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
		pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
#ifdef PUK3_CHECK_VALUE
		if( pAct == NULL ) return NULL;
#endif
		
		pActAct = pAct;
		pAct->deathFlag = 0;
		
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectLoop;
		
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		
		// 战闘时のＹ座标ソート处理ＯＮ
		//pAct->atr |= ACT_ATR_BATTLE_SORT;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		// anim_tbl.h の番号
		pAct->anim_chr_no = pOtherYobi->effectNo;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
		
		// エフェクト番号でＳＥ鸣らす
		// 攻击系
		if( pOtherYobi->effectNo >= SPR_a_start_big && pOtherYobi->effectNo <= SPR_a_start_s ){
			// 発生音
			play_se( 259, pOther->x, pOther->y );
		}else
		// 补助系
		if( pOtherYobi->effectNo >= SPR_s_start_big && pOtherYobi->effectNo <= SPR_s_start_s ){
			// 発生音
			play_se( 260, pOther->x, pOther->y );
		}else
		// 回复系
		if( pOtherYobi->effectNo >= SPR_l_start_big	&& pOtherYobi->effectNo <= SPR_l_start_s ||
			pOtherYobi->effectNo >= SPR_sei_big && pOtherYobi->effectNo <= SPR_sei_s ){
			// 発生音
			play_se( 261, pOther->x, pOther->y );
		}
		//else{
			// 精神统一発生
		//	play_se( 256, pAct->x, pAct->y );
		//}
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
		// 座标记忆する
		pAct->fx = pOther->fx;// + pOther->anim_x + SpriteInfo[ pOther->bmpNo ].width / 2;
		// スプライト番号で分岐
		if( pOtherYobi->effectNo >= SPR_sei_big && pOtherYobi->effectNo <= SPR_sei_s ){
			//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 2 + offsetY;
			pAct->fy = pOther->fy + pOther->anim_y / 2 + offsetY;
		}else{
			pAct->fy = pOther->fy;
		}
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
		
		case BATTLE_EFFECT_HIT_MAGIC_SINGLE:	// 攻击魔法单体 **************************************
		
#ifdef PUK2_NEWSKILL_TOPPU
		// 突风のとき
		if( pOtherYobi->skillId == B_SKILL_TOPPU ){
			
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
			// ターゲットのアクションポインタ
			pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
			
			// アクションポインタ取得
#ifdef PUK2_MEMCHECK
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
			if( pAct == NULL ) return NULL;
			
			// 予备构造体
			pYobi = ( BC_YOBI *)pAct->pYobi;
			
			// 实行关数
			pAct->func = BattleEffectHItMagicToppu;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_B_HIT_MAGIC;
			
			// 点灭フラグＯＮ
			//if( Rnd( 0, 1 ) ){
				//pAct->atr |= ACT_ATR_FLASH_0;
			//}else{
			//	pAct->atr |= ACT_ATR_FLASH_1;
			//}
						
			// 战闘时のＹ座标ソート处理ＯＮ
			pAct->atr |= ACT_ATR_BATTLE_SORT;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			
			// 敌リストをセットする
			BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
			// 装备坏れるか？
			bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
			if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
				// データの取得
				getnewGraNo( pOther,
					 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
				// 武器坏れデータ登録
				setnewGraNo( pAct, enemyId, bmFlag,
					 newGraNo, ridePetGraNo, putonGraNo );
			}
#endif
			// アニメーションスピード设定
			pAct->anim_speed = ANM_NOMAL_SPD;
			
			// 一匹目のターゲットじゃないとき
			if( pOtherYobi->enemyListCnt != 0 ){
				// 発生タイムラグ
				pAct->delta = Rnd( 0, 30 );
			}
			
			// anim_tbl.h の番号
			pAct->anim_chr_no = pOtherYobi->attackEffectNo;
			//pAct->anim_chr_no = 103010;
#ifdef PUK2
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
#endif
			
			
			// 初期スピードランダム
			pAct->speed = ( float )Rnd( 4, 8 );
			
			// スキルＩＤ
			pYobi->skillId = pOtherYobi->skillId;
			// テックＩＤ
			pYobi->techId = pOtherYobi->techId;
			
			// 座标记忆する
			//pAct->fx = pOther->fx;
			//pAct->fy = pOther->fy;
			pAct->fx = 320;
			pAct->fy = 260;
			
			// 最小でスタート
			//pAct->scaleX = 0;
			//pAct->scaleY = 0;
			
			// 目的の方向に向ける
			ChangeDir( pAct, pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ], FALSE, 0 );
			
			// 方向ランダム
			pAct->dir +=  Rnd( -90, 90 );
			
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}else
#endif
		// ドレインのとき
		if( pOtherYobi->skillId == B_SKILL_DRAIN ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
			// ターゲットのアクションポインタ
			pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
			
			// アクションポインタ取得
#ifdef PUK2_MEMCHECK
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
			if( pAct == NULL ) return NULL;
			
			// 予备构造体
			pYobi = ( BC_YOBI *)pAct->pYobi;
			
			// 实行关数
			pAct->func = BattleEffectHItMagicDrain;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_B_HIT_MAGIC;
			
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			
			// 战闘时のＹ座标ソート处理ＯＮ
			//pAct->atr |= ACT_ATR_BATTLE_SORT;
			
			// 敌リストをセットする
			BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
			// 装备坏れるか？
			bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
			if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
				// データの取得
				getnewGraNo( pOther,
					 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
				// 武器坏れデータ登録
				setnewGraNo( pAct, enemyId, bmFlag,
					 newGraNo, ridePetGraNo, putonGraNo );
			}
#endif
			// 步くアニメーション
			pAct->anim_speed = ANM_NOMAL_SPD;
			
			// 一匹目のターゲットじゃないとき
			if( pOtherYobi->enemyListCnt != 0 ){
				// 発生タイムラグ
				pAct->delta = Rnd( 0, 30 );
			}
			
			// anim_tbl.h の番号
			//pAct->anim_chr_no = pOtherYobi->attackEffectNo + Rnd( 0, 2 );
			pAct->anim_chr_no = pOtherYobi->attackEffectNo;
			//pAct->anim_chr_no = 103010;
#ifdef PUK2
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
#endif
			
			// スキルＩＤ
			pYobi->skillId = pOtherYobi->skillId;
			// テックＩＤ
			pYobi->techId = pOtherYobi->techId;
			
			// 座标记忆する
			pAct->fx = pActEnemy->fx;
			pAct->fy = pActEnemy->fy;
			
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}else
		// 攻击魔法（地）のとき
		if( pOtherYobi->skillId == B_SKILL_MAGICATTACK_SORO_EARTH
			|| pOtherYobi->skillId == B_SKILL_MAGICATTACK_AREA_EARTH
			|| pOtherYobi->skillId == B_SKILL_MAGICATTACK_SIDE_EARTH ){
			
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
			// ターゲットのアクションポインタ
			pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
			
			// アクションポインタ取得
#ifdef PUK2_MEMCHECK
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
			if( pAct == NULL ) return NULL;
			
			// 予备构造体
			pYobi = ( BC_YOBI *)pAct->pYobi;
			
			// 实行关数
			pAct->func = BattleEffectHItMagicSingle2;
			// 表示优先度
			//pAct->dispPrio = DISP_PRIO_B_HIT_MAGIC;
			
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			
			// 战闘时のＹ座标ソート处理ＯＮ
			//pAct->atr |= ACT_ATR_BATTLE_SORT;
			
			// 敌リストをセットする
			// 敌リストをセットする
			BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
			// 装备坏れるか？
			bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
			if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
				// データの取得
				getnewGraNo( pOther,
					 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
				// 武器坏れデータ登録
				setnewGraNo( pAct, enemyId, bmFlag,
					 newGraNo, ridePetGraNo, putonGraNo );
			}
#endif
			// 步くアニメーション
			pAct->anim_speed = ANM_NOMAL_SPD;
			
			// 一匹目のターゲットじゃないとき
			if( pOtherYobi->enemyListCnt != 0 ){
				// 発生タイムラグ
				pAct->delta = Rnd( 0, 30 );
			}
			
			// anim_tbl.h の番号
			pAct->anim_chr_no = pOtherYobi->attackEffectNo + Rnd( 0, 2 );
			//pAct->anim_chr_no = 103010;
			
			// スキルＩＤ
			pYobi->skillId = pOtherYobi->skillId;
			// テックＩＤ
			pYobi->techId = pOtherYobi->techId;
			
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
			GetBmpSize( pActEnemy->bmpNo );
#else
			LoadBmp( pActEnemy->bmpNo );
#endif
			// 座标记忆する
			// ターゲットがＩＤが１０未满のとき
			if( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId < 10 ){
				pAct->fx = pActEnemy->fx - 100;
			}else{
				pAct->fx = pActEnemy->fx + 100;
			}
			pAct->fy = ( float )( pActEnemy->fy - 200 - SpriteInfo[ pActEnemy->bmpNo ].height / 2 );
			//pAct->fy = ( float )( pActEnemy->y - 200 );
			
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}else	
		// 攻击魔法（风）のとき
		if( pOtherYobi->skillId == B_SKILL_MAGICATTACK_SORO_WIND
			|| pOtherYobi->skillId == B_SKILL_MAGICATTACK_AREA_WIND
			|| pOtherYobi->skillId == B_SKILL_MAGICATTACK_SIDE_WIND ){
			
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
			// ターゲットのアクションポインタ
			pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
			
			// アクションポインタ取得
#ifdef PUK2_MEMCHECK
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
			if( pAct == NULL ) return NULL;
			
			// 予备构造体
			pYobi = ( BC_YOBI *)pAct->pYobi;
			
			// 实行关数
			pAct->func = BattleEffectHItMagicWind;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_B_HIT_MAGIC;
			
			// 点灭フラグＯＮ
			//if( Rnd( 0, 1 ) ){
				//pAct->atr |= ACT_ATR_FLASH_0;
			//}else{
			//	pAct->atr |= ACT_ATR_FLASH_1;
			//}
						
			// 战闘时のＹ座标ソート处理ＯＮ
			pAct->atr |= ACT_ATR_BATTLE_SORT;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			
			// 敌リストをセットする
			BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
			// 装备坏れるか？
			bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
			if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
				// データの取得
				getnewGraNo( pOther,
					 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
				// 武器坏れデータ登録
				setnewGraNo( pAct, enemyId, bmFlag,
					 newGraNo, ridePetGraNo, putonGraNo );
			}
#endif
			// アニメーションスピード设定
			pAct->anim_speed = ANM_NOMAL_SPD;
			
			// 一匹目のターゲットじゃないとき
			if( pOtherYobi->enemyListCnt != 0 ){
				// 発生タイムラグ
				pAct->delta = Rnd( 0, 30 );
			}
			
			// anim_tbl.h の番号
			pAct->anim_chr_no = pOtherYobi->attackEffectNo;
			//pAct->anim_chr_no = 103010;
#ifdef PUK2
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
#endif
			
			
			// 初期スピードランダム
			pAct->speed = ( float )Rnd( 4, 8 );
			
			// スキルＩＤ
			pYobi->skillId = pOtherYobi->skillId;
			// テックＩＤ
			pYobi->techId = pOtherYobi->techId;
			
			// 座标记忆する
			//pAct->fx = pOther->fx;
			//pAct->fy = pOther->fy;
			pAct->fx = 320;
			pAct->fy = 260;
			
			// 最小でスタート
			//pAct->scaleX = 0;
			//pAct->scaleY = 0;
			
			// 目的の方向に向ける
			ChangeDir( pAct, pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ], FALSE, 0 );
			
			// 方向ランダム
			pAct->dir +=  Rnd( -90, 90 );
			
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}else
		// 攻击魔法（火）のとき
		if( pOtherYobi->skillId == B_SKILL_MAGICATTACK_SORO_FIRE
			|| pOtherYobi->skillId == B_SKILL_MAGICATTACK_AREA_FIRE
			|| pOtherYobi->skillId == B_SKILL_MAGICATTACK_SIDE_FIRE ){
			
			// ターゲットのアクションポインタ
			pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
			
			// アクションポインタ取得
#ifdef PUK2_MEMCHECK
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
			if( pAct == NULL ) return NULL;
			// 实行关数
			pAct->func = BattleEffectHItMagicSingle;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_B_HIT_MAGIC;
			
			// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
			if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
			else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
			pAct->atr |= ACT_ATR_FLASH_0;
	#endif
			
			// 战闘时のＹ座标ソート处理ＯＮ
			//pAct->atr |= ACT_ATR_BATTLE_SORT;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			
			// 敌リストをセットする
			BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
			// 装备坏れるか？
			bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
			if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
				// データの取得
				getnewGraNo( pOther,
					 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
				// 武器坏れデータ登録
				setnewGraNo( pAct, enemyId, bmFlag,
					 newGraNo, ridePetGraNo, putonGraNo );
			}
#endif
			// 步くアニメーション
			pAct->anim_speed = ANM_NOMAL_SPD;
			
			// 一匹目のターゲットじゃないとき
			if( pOtherYobi->enemyListCnt != 0 ){
				// 発生タイムラグ
				pAct->delta = Rnd( 0, 30 );
			}
			
			// anim_tbl.h の番号（３种类でランダム）（地、火、风、统一する）
			pAct->anim_chr_no = pOtherYobi->attackEffectNo + Rnd( 0, 2 );
			//pAct->anim_chr_no = 103010;
#ifdef PUK2
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
#endif
			
			// 座标记忆する
			//pAct->fx = ( float )( pActEnemy->fx + pActEnemy->anim_x + SpriteInfo[ pActEnemy->bmpNo ].width / 2 );
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			//LoadBmp( pOther->bmpNo );
			pAct->fx = pActEnemy->fx;
			pAct->fy = pActEnemy->fy + pActEnemy->anim_y /2;
			
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}else{
			// 水のとき
			
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
			// ターゲットのアクションポインタ
			pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
			
			// アクションポインタ取得
#ifdef PUK2_MEMCHECK
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
			pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
			if( pAct == NULL ) return NULL;
			// 实行关数
			pAct->func = BattleEffectHItMagicSingle;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
			
			// 点灭フラグＯＮ
			//pAct->atr |= ACT_ATR_FLASH_0;
			
			// 战闘时のＹ座标ソート处理ＯＮ
			pAct->atr |= ACT_ATR_BATTLE_SORT;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			
			// 敌リストをセットする
			BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage,
								pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].newGraNo );
#ifdef PUK3_WEPON_BREAK
			// 装备坏れるか？
			bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
			if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
				// データの取得
				getnewGraNo( pOther,
					 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
				// 武器坏れデータ登録
				setnewGraNo( pAct, enemyId, bmFlag,
					 newGraNo, ridePetGraNo, putonGraNo );
			}
#endif
			// 步くアニメーション
			pAct->anim_speed = ANM_NOMAL_SPD;
			
			// 一匹目のターゲットじゃないとき
			if( pOtherYobi->enemyListCnt != 0 ){
				// 発生タイムラグ
				pAct->delta = Rnd( 0, 30 );
			}
			
			// anim_tbl.h の番号（３种类でランダム）（地、火、风、统一する）
			pAct->anim_chr_no = pOtherYobi->attackEffectNo + Rnd( 0, 2 );
			//pAct->anim_chr_no = CG_BIGICE_DUST_0 + Rnd( 0, 4 );
#ifdef PUK2
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
#endif
			
			// 座标记忆する
			//pAct->fx = ( float )( pActEnemy->fx + pActEnemy->anim_x + SpriteInfo[ pActEnemy->bmpNo ].width / 2 );
			pAct->fx = pActEnemy->fx;
			//pAct->y = pActEnemy->y + offsetY;
			// ターゲットが手前のとき里に魔法出す
			if( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId < 10 ) offsetY = -1;
			else offsetY = 1;
			//pAct->fy = ( float )( pActEnemy->y + offsetY );
			pAct->fy = ( float )( pActEnemy->y + 1 );
			
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
			// 表示优先度普通
			pAct->bufCount = 0;
#endif
		}
		
		break;
		
		case BATTLE_EFFECT_LP_INCREASE_2:	// 体力回复补助エフェクト **************************************
		
		// アクションポインタ取得
#ifdef PUK2_MEMCHECK
		pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
		pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectLoop;
		
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_MISSILE;
		
		// 战闘时のＹ座标ソート处理ＯＮ
		//pAct->atr |= ACT_ATR_BATTLE_SORT;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		
		// 亲のアクションポインタ学习
		pAct->pOther = pOther;
		
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		// anim_tbl.h の番号
		pAct->anim_chr_no = SPR_lp_fuzoku00;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
		
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( pOther->bmpNo );
		// 座标记忆する
		pAct->fx = pOther->fx;// + pOther->anim_x + SpriteInfo[ pOther->bmpNo ].width / 2;
		pAct->fy = pOther->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		
		break;
		
#if 0		
		case BATTLE_EFFECT_HIT_MAGIC_SINGLE_FALL:	// 攻击魔法单体落下（地）**************************************
		
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId );
#endif
		pActEnemy = pActBc[ pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId ];
		
		// アクションポインタ取得
#ifdef PUK2_MEMCHECK
		pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
		pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
		if( pAct == NULL ) return NULL;
		// 实行关数
		pAct->func = BattleEffectHItMagicSingle2;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
		
		// 点灭フラグＯＮ
		//pAct->atr |= ACT_ATR_FLASH_0;	
		
		// 战闘时のＹ座标ソート处理ＯＮ
		//pAct->atr |= ACT_ATR_BATTLE_SORT;
		
		// 敌リストをセットする
		BattleSetEnemyList( pAct, pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId,
							pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag, 
							pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].damage );
	#ifdef PUK3_WEPON_BREAK
		// 装备坏れるか？
		bmFlag = pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].bmFlag;
		if ( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
			// データの取得
			getnewGraNo( pOther,
				 &enemyId, &newGraNo, &ridePetGraNo, &putonGraNo );
			// 武器坏れデータ登録
			setnewGraNo( pAct, enemyId, bmFlag,
				 newGraNo, ridePetGraNo, putonGraNo );
		}
	#endif
		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		
		// 一匹目のターゲットじゃないとき
		if( pOtherYobi->enemyListCnt != 0 ){
			// 発生タイムラグ
			pAct->delta = Rnd( 0, 30 );
		}
		
		// anim_tbl.h の番号
		pAct->anim_chr_no = pOtherYobi->attackEffectNo;
		//pAct->anim_chr_no = 103010;
		
		// 座标记忆する
		pAct->fx = ( float )( pActEnemy->x + pActEnemy->anim_x + SpriteInfo[ pActEnemy->bmpNo ].width / 2 );
		//pAct->y = pActEnemy->y + offsetY;
		// ターゲットが手前のとき里に魔法出す
		//if( pOtherYobi->enemyList[ pOtherYobi->enemyListCnt ].enemyId < 10 ) offsetY = -1;
		//else offsetY = 1;
		//pAct->y = pActEnemy->y + offsetY;
		pAct->fy = ( float )( pActEnemy->y - 200 );
		
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
		break;
#endif		
	}
	
	// 点灭させる时
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			if( flashFlag == 1 ) pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
			else if( flashFlag == 2 ) pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_1 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ){
			if( flashFlag == 1 || flashFlag == 2 ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		}else{
			if( flashFlag == 1 ) pAct->atr |= ACT_ATR_FLASH_0;
			else if( flashFlag == 2 ) pAct->atr |= ACT_ATR_FLASH_1;
		}
		#endif
	#else
	if( flashFlag == 1 ) pAct->atr |= ACT_ATR_FLASH_0;
	else if( flashFlag == 2 ) pAct->atr |= ACT_ATR_FLASH_1;
	#endif
	
	// エフェクト終了待ちする时
	if( waitFlag == 1 ){
		// 亲のアクションポインタ学习
		pAct->pOther = pOther;
		// エフェクトフラグＯＮにする
		( ( BC_YOBI *)pOther->pYobi )->effectFlag = TRUE;
	}
	
	// アニメーション向き( ０～７ )( 下が５で右回り )
	pAct->anim_ang = 0;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;

#ifdef PUK2
	// エフェクト番号で分岐
	switch( effectNo ){
	case BATTLE_EFFECT_MAGIC_GENERATE:	// 魔法発生エフェクト **************************************
	case BATTLE_EFFECT_HIT_MAGIC_SINGLE:	// 攻击魔法单体 **************************************
	case BATTLE_EFFECT_LP_INCREASE_2:	// 体力回复补助エフェクト **************************************
		// pYobi の中身が BC_YOBI なので、亲のＩＤを学习
		if ( pAct->pYobi && pOther->pYobi ){
			( (BC_YOBI *)pAct->pYobi )->myId=( (BC_YOBI *)pOther->pYobi )->myId;
		}
		break;
	}
#endif
		
	return pAct;
}

// 战闘エフェクト作成 *******************************************************/
ACTION *MakeBattleEffect( int x, int y, int sprNo )
{
	ACTION *pAct;
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleEffectCounter;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	pAct->fx = ( float )x;
	pAct->fy = ( float )y;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
		
	return pAct;
}

// 战闘アルティメット处理 ***********************************************/
void BattleUltimateMark( ACTION *pAct )
{
	
	// 目的地に移动する
	MoveDir( pAct );
	
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		DeathAction( pAct );
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// 战闘アルティメットタスク作成 *******************************************************/
ACTION *MakeBattleUltimateMark( ACTION *pOther, int sprNo )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pOther );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleUltimateMark;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// アニメーションスピード
	pAct->anim_speed = Rnd( 10, 200 );
	//pAct->anim_speed = 100;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	//pAct->anim_ang = pOther->anim_ang;
	// 方向ランダム
	pAct->dir = ( float )Rnd( 0, 359 );
	// 速度ランダム
	pAct->speed = ( float )Rnd( 5, 20 );
	//pAct->speed = 15.0f;
	
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
	//LoadBmp( pOther->bmpNo );
	
	// 中心座标记忆する
	//pAct->x = pOther->x + Rnd( 0, pOther->anim_x / 2 ) - pOther->anim_x / 4;
	pAct->fx = pOther->fx;
	//pAct->y = pOther->y + pOther->anim_y / 2 + Rnd( 0, pOther->anim_y / 4 ) - pOther->anim_y / 8;
	//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 4;
	pAct->fy = pOther->fy + pOther->anim_y / 2;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	// アニメーション
	//pattern( pAct, ANM_NO_LOOP );
	
	return pAct;
}

// 战闘残像处理 ***********************************************/
void BattleZanzou( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// カウンタープラス
	pYobi->waitCnt++;
	// リミットチェック
	if( pYobi->waitCnt >= 12 ){
		DeathAction( pAct );
	}
	// 表示座标に变换
	//pAct->x = ( int )pAct->fx;
	//pAct->y = ( int )pAct->fy;
	
}
// 战闘残像タスク作成 *******************************************************/
ACTION *MakeBattleZanzou( ACTION *pParent )
{
	ACTION *pAct;
	BC_YOBI *pYobi;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクション构造体を复制する
	pAct = BattleActionCopy( pParent );
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleZanzou;
	// カウンター初期化
	pYobi->waitCnt = 0;
	// 表示优先度
	//pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	//pAct->anim_ang = 0;
	// 方向ランダム
	//pAct->dir = ( float )Rnd( 0, 359 );
	// 速度ランダム
	//pAct->speed = ( float )Rnd( 5, 20 );
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
	//LoadBmp( pOther->bmpNo );
	
	// 中心座标记忆する
	//pAct->x = pOther->x + Rnd( 0, pOther->anim_x / 2 ) - pOther->anim_x / 4;
	//pAct->fx = pOther->fx;
	//pAct->y = pOther->y + pOther->anim_y / 2 + Rnd( 0, pOther->anim_y / 4 ) - pOther->anim_y / 8;
	//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 4;
	
	// アニメーション
	//pattern( pAct, ANM_NO_LOOP );
	
	return pAct;
}

#if 0
// サクリファイス分身处理 ***********************************************/
void BattleSacrifice( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// カウンタープラス
	pYobi->waitCnt++;
	// リミットチェック
	if( pYobi->waitCnt >= 12 ){
		DeathAction( pAct );
	}
	// 表示座标に变换
	//pAct->x = ( int )pAct->fx;
	//pAct->y = ( int )pAct->fy;
	
}
// サクリファイス分身作成 *******************************************************/
ACTION *MakeBattleSacrifice( ACTION *pParent )
{
	ACTION *pAct;
	BC_YOBI *pYobi;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクション构造体を复制する
	pAct = BattleActionCopy( pParent );
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleSacrifice;
	// カウンター初期化
	pYobi->waitCnt = 0;
	// 表示优先度
	//pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	//pAct->anim_ang = 0;
	// 方向ランダム
	//pAct->dir = ( float )Rnd( 0, 359 );
	// 速度ランダム
	//pAct->speed = ( float )Rnd( 5, 20 );
	
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
	//LoadBmp( pOther->bmpNo );
	
	// 中心座标记忆する
	//pAct->x = pOther->x + Rnd( 0, pOther->anim_x / 2 ) - pOther->anim_x / 4;
	//pAct->fx = pOther->fx;
	//pAct->y = pOther->y + pOther->anim_y / 2 + Rnd( 0, pOther->anim_y / 4 ) - pOther->anim_y / 8;
	//pAct->fy = pOther->fy - SpriteInfo[ pOther->bmpNo ].height / 4;
	
	// アニメーション
	//pattern( pAct, ANM_NO_LOOP );
	
	return pAct;
}

#endif

// 战闘けむり处理 *******************************************************/
void BattleKemuri( ACTION *pAct )
{
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		DeathAction( pAct );
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// 战闘けむり作成 *******************************************************/
ACTION *MakeBattleKemuri( ACTION *pParent, int sprNo )
{
	ACTION *pAct;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleKemuri;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 战闘けむり作成 *******************************************************/
ACTION *MakeBattleKemuri( float fx, float fy, int sprNo )
{
	ACTION *pAct;

	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleKemuri;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	// 座标记忆する
	pAct->fx = fx;
	pAct->fy = fy;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 战闘彗星作成 *******************************************************/
ACTION *MakeBattleSuisei( float fx, float fy, int sprNo, int battleSortFlag, int dispPrio )
{
	ACTION *pAct;

	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleKemuri;
	// 表示优先度
	//pAct->dispPrio = DISP_PRIO_B_EFFECT2;
	//pAct->dispPrio = DISP_PRIO_B_EFFECT;
	pAct->dispPrio = dispPrio;
	// 步くアニメーション
	//pAct->anim_speed = ANM_NOMAL_SPD;
	pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
			else pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_1 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else{
			if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_FLASH_0;
			else pAct->atr |= ACT_ATR_FLASH_1;
		}
		#endif
	#else
	if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_FLASH_0;
	else pAct->atr |= ACT_ATR_FLASH_1;
	#endif
	//pAct->atr |= ACT_ATR_FLASH_1;
	
	// 战闘时のＹ座标ソート处理ＯＮ
	if( battleSortFlag == TRUE ){
		pAct->atr |= ACT_ATR_BATTLE_SORT;
	}
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	
	// 座标记忆する
	pAct->fx = fx;
	pAct->fy = fy;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}


#ifdef PUK3_R10
	int flashTable[][16] = 	{ 
							//{ 101,101,102,102,103,103,104,104,105,105,106,106,107,107,108,108 },		// 地
							{ 	
								CG_B_EFFECT_FLASH_NEW_GREEN,
								CG_B_EFFECT_FLASH_NEW_GREEN,
								CG_B_EFFECT_FLASH_NEW_GREEN + 1,
								CG_B_EFFECT_FLASH_NEW_GREEN + 1,
								CG_B_EFFECT_FLASH_NEW_GREEN + 2,
								CG_B_EFFECT_FLASH_NEW_GREEN + 2,
								CG_B_EFFECT_FLASH_NEW_GREEN + 3,
								CG_B_EFFECT_FLASH_NEW_GREEN + 3,
								CG_B_EFFECT_FLASH_NEW_GREEN + 4,
								CG_B_EFFECT_FLASH_NEW_GREEN + 4,
								CG_B_EFFECT_FLASH_NEW_GREEN + 5,
								CG_B_EFFECT_FLASH_NEW_GREEN + 5,
								CG_B_EFFECT_FLASH_NEW_GREEN + 6,
								CG_B_EFFECT_FLASH_NEW_GREEN + 6,
								CG_B_EFFECT_FLASH_NEW_GREEN + 7,
								CG_B_EFFECT_FLASH_NEW_GREEN + 7
							},
							//{ 131,131,132,132,133,133,134,134,135,135,136,136,137,137,138,138 },		// 水
							{ 	
								CG_B_EFFECT_FLASH_NEW_BLUE,
								CG_B_EFFECT_FLASH_NEW_BLUE,
								CG_B_EFFECT_FLASH_NEW_BLUE + 1,
								CG_B_EFFECT_FLASH_NEW_BLUE + 1,
								CG_B_EFFECT_FLASH_NEW_BLUE + 2,
								CG_B_EFFECT_FLASH_NEW_BLUE + 2,
								CG_B_EFFECT_FLASH_NEW_BLUE + 3,
								CG_B_EFFECT_FLASH_NEW_BLUE + 3,
								CG_B_EFFECT_FLASH_NEW_BLUE + 4,
								CG_B_EFFECT_FLASH_NEW_BLUE + 4,
								CG_B_EFFECT_FLASH_NEW_BLUE + 5,
								CG_B_EFFECT_FLASH_NEW_BLUE + 5,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_BLUE + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 7
							},
							//{ 222,222,223,223,224,224,225,225,226,226,227,227,228,228,229,229 },		// 火
							{ 	
								CG_B_EFFECT_FLASH_NEW_RED,
								CG_B_EFFECT_FLASH_NEW_RED,
								CG_B_EFFECT_FLASH_NEW_RED + 1,
								CG_B_EFFECT_FLASH_NEW_RED + 1,
								CG_B_EFFECT_FLASH_NEW_RED + 2,
								CG_B_EFFECT_FLASH_NEW_RED + 2,
								CG_B_EFFECT_FLASH_NEW_RED + 3,
								CG_B_EFFECT_FLASH_NEW_RED + 3,
								CG_B_EFFECT_FLASH_NEW_RED + 4,
								CG_B_EFFECT_FLASH_NEW_RED + 4,
								CG_B_EFFECT_FLASH_NEW_RED + 5,
								CG_B_EFFECT_FLASH_NEW_RED + 5,
								CG_B_EFFECT_FLASH_NEW_RED + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_RED + 7
							},
							//{ 74, 74, 75, 75, 76, 76, 77, 77, 78, 78, 79, 79, 80, 80, 81, 81  },		// 风
							{ 	
								CG_B_EFFECT_FLASH_NEW_YELLOW,
								CG_B_EFFECT_FLASH_NEW_YELLOW,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 1,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 1,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 2,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 2,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 3,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 3,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 4,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 4,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 5,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 5,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 6,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 6,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 7,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 7
							},
							//{ 137,230,137,230,137,230,137,230,137,230,137,230,137,230,137,230 },		// ドレイン
							{
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7
							},
							//{ 165,165,165,166,166,166,166,166,167,167,167,167,167,168,168,168 },		// 即死
							{
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3
							}
						};
#endif
// １フレーム画面フラッシュ *******************************************************/
void BattleEffectFlash( int sprNo )
{

	static int cnt = 0;
	static int flag = FALSE;
	int i, j;
		
#ifdef PUK3_R10
#else
	int table[][16] = 	{ 
							//{ 101,101,102,102,103,103,104,104,105,105,106,106,107,107,108,108 },		// 地
							{ 	
								CG_B_EFFECT_FLASH_NEW_GREEN,
								CG_B_EFFECT_FLASH_NEW_GREEN,
								CG_B_EFFECT_FLASH_NEW_GREEN + 1,
								CG_B_EFFECT_FLASH_NEW_GREEN + 1,
								CG_B_EFFECT_FLASH_NEW_GREEN + 2,
								CG_B_EFFECT_FLASH_NEW_GREEN + 2,
								CG_B_EFFECT_FLASH_NEW_GREEN + 3,
								CG_B_EFFECT_FLASH_NEW_GREEN + 3,
								CG_B_EFFECT_FLASH_NEW_GREEN + 4,
								CG_B_EFFECT_FLASH_NEW_GREEN + 4,
								CG_B_EFFECT_FLASH_NEW_GREEN + 5,
								CG_B_EFFECT_FLASH_NEW_GREEN + 5,
								CG_B_EFFECT_FLASH_NEW_GREEN + 6,
								CG_B_EFFECT_FLASH_NEW_GREEN + 6,
								CG_B_EFFECT_FLASH_NEW_GREEN + 7,
								CG_B_EFFECT_FLASH_NEW_GREEN + 7
							},
							//{ 131,131,132,132,133,133,134,134,135,135,136,136,137,137,138,138 },		// 水
							{ 	
								CG_B_EFFECT_FLASH_NEW_BLUE,
								CG_B_EFFECT_FLASH_NEW_BLUE,
								CG_B_EFFECT_FLASH_NEW_BLUE + 1,
								CG_B_EFFECT_FLASH_NEW_BLUE + 1,
								CG_B_EFFECT_FLASH_NEW_BLUE + 2,
								CG_B_EFFECT_FLASH_NEW_BLUE + 2,
								CG_B_EFFECT_FLASH_NEW_BLUE + 3,
								CG_B_EFFECT_FLASH_NEW_BLUE + 3,
								CG_B_EFFECT_FLASH_NEW_BLUE + 4,
								CG_B_EFFECT_FLASH_NEW_BLUE + 4,
								CG_B_EFFECT_FLASH_NEW_BLUE + 5,
								CG_B_EFFECT_FLASH_NEW_BLUE + 5,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_BLUE + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 7
							},
							//{ 222,222,223,223,224,224,225,225,226,226,227,227,228,228,229,229 },		// 火
							{ 	
								CG_B_EFFECT_FLASH_NEW_RED,
								CG_B_EFFECT_FLASH_NEW_RED,
								CG_B_EFFECT_FLASH_NEW_RED + 1,
								CG_B_EFFECT_FLASH_NEW_RED + 1,
								CG_B_EFFECT_FLASH_NEW_RED + 2,
								CG_B_EFFECT_FLASH_NEW_RED + 2,
								CG_B_EFFECT_FLASH_NEW_RED + 3,
								CG_B_EFFECT_FLASH_NEW_RED + 3,
								CG_B_EFFECT_FLASH_NEW_RED + 4,
								CG_B_EFFECT_FLASH_NEW_RED + 4,
								CG_B_EFFECT_FLASH_NEW_RED + 5,
								CG_B_EFFECT_FLASH_NEW_RED + 5,
								CG_B_EFFECT_FLASH_NEW_RED + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_RED + 7
							},
							//{ 74, 74, 75, 75, 76, 76, 77, 77, 78, 78, 79, 79, 80, 80, 81, 81  },		// 风
							{ 	
								CG_B_EFFECT_FLASH_NEW_YELLOW,
								CG_B_EFFECT_FLASH_NEW_YELLOW,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 1,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 1,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 2,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 2,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 3,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 3,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 4,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 4,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 5,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 5,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 6,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 6,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 7,
								CG_B_EFFECT_FLASH_NEW_YELLOW + 7
							},
							//{ 137,230,137,230,137,230,137,230,137,230,137,230,137,230,137,230 },		// ドレイン
							{
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7,
								CG_B_EFFECT_FLASH_NEW_BLUE + 6,
								CG_B_EFFECT_FLASH_NEW_RED + 7
							},
							//{ 165,165,165,166,166,166,166,166,167,167,167,167,167,168,168,168 },		// 即死
							{
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 1,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 2,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3,
								CG_B_EFFECT_FLASH_NEW_BLACK + 3
							}
						};
#endif
//	if( FlipCnt == 1 ){

		if( flag == FALSE ){
			cnt++;
			if( cnt >= 15 ) flag = TRUE;
		}else{
			cnt--;
			if( cnt <= 0 ) flag = FALSE;
		}
		
//	}
	
	// 画面分ループ
	for( i = 0 ; i < DEF_APPSIZEX ; i += 64 ){
		for( j = 0 ; j < DEF_APPSIZEY ; j += 48 ){
			//StockDispBuffer( i * 64 + 32, j * 48 + 24, DISP_PRIO_B_EFFECT, sprNo, 0 );
			//StockDispBuffer( i + 32, j + 24, DISP_PRIO_BG, sprNo, 0 );
#ifdef PUK3_R10
			StockDispBuffer( i + 32, j + 24, DISP_PRIO_BG, flashTable[ sprNo ][ cnt ], 0 );
#else
			StockDispBuffer( i + 32, j + 24, DISP_PRIO_BG, table[ sprNo ][ cnt ], 0 );
#endif
		}
	}
}
#ifdef PUK3_R10
// １フレーム画面フラッシュ *******************************************************/
void BattleEffectFlash_For_Action( ACTION *pAct )
{
	int i, j;

	if( pAct->status == FALSE ){
		pAct->delta++;
		if( pAct->delta >= 15 ) pAct->status = TRUE;
	}else{
		pAct->delta--;
		if( pAct->delta <= 0 ) pAct->status = FALSE;
	}

	// 画面分ループ
	for( i = 0 ; i < DEF_APPSIZEX ; i += 64 ){
		for( j = 0 ; j < DEF_APPSIZEY ; j += 48 ){
			StockDispBuffer( i + 32, j + 24, DISP_PRIO_BG, flashTable[ pAct->actNo ][ pAct->delta ], 0 );
		}
	}

	if ( pAct->hp >= pAct->maxHp ) DeathAction(pAct);
	pAct->hp++;
}
// 画面のフラッシュアクション(画面フラッシュ用关数を流用)
ACTION *BattleActionFlash( int type, int time )
{
	ACTION *pAct;

	if ( time <= 0 ) return NULL;

	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;

	// 实行关数
	pAct->func = BattleEffectFlash_For_Action;
	// 表示优先度
	pAct->dispPrio = 0;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// アクションスローフラグＯＮ
	pAct->atr = ACT_ATR_ACT_SLOW | ACT_ATR_HIDE;

	pAct->anim_chr_no = 0;
	pAct->anim_ang = 0;

	// 种类の记忆
	pAct->actNo = type;

	// 稼动时间の记忆
	pAct->maxHp = time;

	// カウンター等初期化
	pAct->hp = 0;
	pAct->status = 0;
	pAct->delta = 0;

	return pAct;
}
#endif

// アクション构造体を复制する *******************************************************/
ACTION *BattleActionCopy( ACTION *pParent )
{

	ACTION *pAct;
	BC_YOBI *pYobi;
	BC_YOBI *pParentYobi = ( BC_YOBI *)pParent->pYobi;
	ACTION *pPrev;
	ACTION *pNext;
	void *pYobiBack;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	
	// ポインターバックアップ
	pPrev = pAct->pPrev;
	pNext = pAct->pNext;
	pYobiBack = pAct->pYobi;
	
	// アクション构造体コピー
	*pAct = *pParent;
	
	// ポインタ戾す
	pAct->pPrev = pPrev;
	pAct->pNext = pNext;
	pAct->pYobi = pYobiBack;
	
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 予备构造体もコピー
	*pYobi = *pParentYobi;
	
	return pAct;
}

// 受伤表示处理 *******************************************************/
void BattlePointDisp( ACTION *pAct )
{
	ACTION *pActOther = ( ACTION * )pAct->pOther;
	
	// 行动番号で分岐
	switch( pAct->actNo ){
	
	case 0:	// 上升中
		
		// スタートウェイト
		if( pAct->delta > 0 ){ 
			pAct->delta--;
			break;
		}
		
#ifdef PUK2
		// 表示ＯＮ
		pAct->atr &= ~ACT_ATR_HIDE;

		// 座标プラス
		pAct->fontdfY += 0.8F;
		if( pAct->fontdfY >= 7.2F ) pAct->actNo++;
		
		// 移动处理
		pAct->y += (int)pAct->fontdfY;
#else
		// 表示ＯＮ
		pAct->atr &= ~ACT_ATR_FONT_HIDE;
		
		// 座标プラス
		pAct->fontdfY += 0.8F;
		if( pAct->fontdfY >= 7.2F ) pAct->actNo++;
		
		// 移动处理
		pAct->fontfY += pAct->fontdfY;
#endif
		break;
		
	case 1:	// 停止して表示
		
		// カウンタープラス
		pAct->dx++;
		if( pAct->dx >= 50 ) DeathAction( pAct );
		
		break;
		
	}
	
	// 表示座标に变换
	pAct->fontX = ( int )pAct->fontfX;
	pAct->fontY = ( int )pAct->fontfY;
	
}

// 受伤表示タスク作成 *******************************************************/
#ifdef PUK3_MONSTER_HELPER
ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind, int bmFlag )
#else
ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind )
#endif
{
	ACTION *pAct;
	char moji[ 256 ];
	int mojiCenter;	// センターリング座标
	int len;	// 文字の长さ
	int i;
#ifdef PUK2
	int bmpNo;
#endif
	
	// 文字列作成
#ifdef PUK3_MONSTER_HELPER
	sprintf( moji, "%d", point );                                        //MLHIDE
	// デュエル时は表示しない
	if ( DuelFlag );
	else{
		if ( bmFlag & BM_FLAG_DAMAGE_UP ) strcat( moji, "+" );              //MLHIDE
		if ( bmFlag & BM_FLAG_DAMAGE_DOWN ) strcat( moji, "-" );            //MLHIDE
	}
#else
	sprintf( moji, "%d", point );                                        //MLHIDE
#endif
	// センターリング座标
	mojiCenter = GetStrWidth( moji ) / 2;
	// 文字の长さ
	len = strlen( moji );
	
	// 文字分ループ
	for( i = 0 ; i < len ; i++ ){
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) break;
	
#ifdef PUK2
		short dx,dy;
		// 实行关数
		pAct->func = BattlePointDisp;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_METER;
		//种类セット
		pAct->gx = moji[ i ];
		//タイマーセット
		pAct->anim_chr_no = 0;
		//移动速度セット
		pAct->speed = 18;
		if( x<320 ) pAct->dir = 270;
		else pAct->dir = 90;
		//数字なら
		pAct->atr = ACT_ATR_HIDE;
		//グラフィック番号
	#ifdef PUK3_MONSTER_HELPER
		switch( moji[ i ] ){
		case '+':	bmpNo = ColorNumGraNo[color][1];	break;
		case '-':	bmpNo = ColorNumGraNo[color][2];	break;
		case ':':	bmpNo = ColorNumGraNo[color][3];	break;
		default:
			bmpNo = ColorNumGraNo[color][0] + moji[ i ] - '0';
		}
	#else
		bmpNo = ColorNumGraNo[color][0] + moji[ i ] - '0';
	#endif
		realGetNo( bmpNo , (U4 *)&pAct->bmpNo );
		// グラフィックの番号
		realGetPos( pAct->bmpNo, &dx, &dy );
		//初期位置セット
		pAct->fx = (float)dx + (float)x - mojiCenter + FontKind[ fontKind ].hankakuWidth * i; // 半角サイズを足す
		pAct->fy = (float)dy + (float)y;
		//初期位置セット
		pAct->x = (int)pAct->fx;
		pAct->y = (int)pAct->fy;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		// Ｙ增分
		pAct->fontdfY = -8;
		// スタートウェイト
		pAct->delta = i * 2;

		// パレットチェンジ对象外に设定
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#else
		// 座标记忆する
		pAct->fontfX = ( float )x - mojiCenter + FontKind[ fontKind ].hankakuWidth * i; // 半角サイズを足す
		pAct->fontfY = ( float )y;
		// 文字设定
		pAct->fontStr[ 0 ] = moji[ i ];
		// 終端记号
		pAct->fontStr[ 1 ] = NULL;
		// 表示色记忆
		pAct->fontColor = color;
		// フォントの种类
		pAct->fontKind = fontKind;
		// フォントの表示优先度
		pAct->fontPrio = FONT_PRIO_BACK;
		// 实行关数
		pAct->func = BattlePointDisp;
		// 非表示
		pAct->atr |= ACT_ATR_HIDE;
		// フォント表示ＯＦＦ
		pAct->atr |= ACT_ATR_FONT_HIDE;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		// Ｙ增分
		pAct->fontdfY = -8;
		// スタートウェイト
		pAct->delta = i * 2;
#endif	
		
		// 表示座标に变换
		pAct->fontX = ( int )pAct->fontfX;
		pAct->fontY = ( int )pAct->fontfY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
	}
#ifdef PUK3_MONSTER_HELPER
/*
	if ( bmFlag & BM_FLAG_DAMAGE_UP ){

		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct != NULL ){
			short dx,dy;
			// 实行关数
			pAct->func = BattlePointDisp;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_METER;
			//种类セット
			pAct->gx = moji[ i ];
			//タイマーセット
			pAct->anim_chr_no = 0;
			//移动速度セット
			pAct->speed = 18;
			if( x<320 ) pAct->dir = 270;
			else pAct->dir = 90;
			//数字なら
			pAct->atr = ACT_ATR_HIDE;
			//グラフィック番号
			bmpNo = ColorNumGraNo[color][1];
			realGetNo( bmpNo , (U4 *)&pAct->bmpNo );
			// グラフィックの番号
			realGetPos( pAct->bmpNo, &dx, &dy );
			//初期位置セット
			pAct->fx = (float)dx + (float)x;
			pAct->fy = (float)dy + (float)y - FontKind[ fontKind ].hankakuHeight;
			//初期位置セット
			pAct->x = (int)pAct->fx;
			pAct->y = (int)pAct->fy;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			// Ｙ增分
			pAct->fontdfY = -8;
			// スタートウェイト
			pAct->delta = i * 2;
	
			// パレットチェンジ对象外に设定
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
		}

	}
	if ( bmFlag & BM_FLAG_DAMAGE_DOWN ){
		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct != NULL ){
			short dx,dy;
			// 实行关数
			pAct->func = BattlePointDisp;
			// 表示优先度
			pAct->dispPrio = DISP_PRIO_METER;
			//种类セット
			pAct->gx = moji[ i ];
			//タイマーセット
			pAct->anim_chr_no = 0;
			//移动速度セット
			pAct->speed = 18;
			if( x<320 ) pAct->dir = 270;
			else pAct->dir = 90;
			//数字なら
			pAct->atr = ACT_ATR_HIDE;
			//グラフィック番号
			bmpNo = ColorNumGraNo[color][2];
			realGetNo( bmpNo , (U4 *)&pAct->bmpNo );
			// グラフィックの番号
			realGetPos( pAct->bmpNo, &dx, &dy );
			//初期位置セット
			pAct->fx = (float)dx + (float)x;
			pAct->fy = (float)dy + (float)y + FontKind[ fontKind ].hankakuHeight;
			//初期位置セット
			pAct->x = (int)pAct->fx;
			pAct->y = (int)pAct->fy;
			// アクションスローフラグＯＮ
			pAct->atr |= ACT_ATR_ACT_SLOW;
			// Ｙ增分
			pAct->fontdfY = -8;
			// スタートウェイト
			pAct->delta = i * 2;
	
			// パレットチェンジ对象外に设定
			pAct->bltfon = BLTF_NOCHG;
			pAct->bltf = BLTF_NOCHG;
		}
	}
*/
#endif
	
	return NULL;
}

// ジャンプグラフィック表示处理 *******************************************************/
void BattleJumpDisp( ACTION *pAct )
{
	// 行动番号で分岐
	switch( pAct->actNo ){
	
	case 0:	// 上升中
		
		// 座标プラス
		pAct->dfy += 0.8F;
		if( pAct->dfy >= 7.2F ) pAct->actNo++;
		
		// 移动处理
		pAct->fy += pAct->dfy;
		
		break;
		
	case 1:	// 停止して表示
		
		// カウンタープラス
		pAct->dx++;
		if( pAct->dx >= 60 ) DeathAction( pAct );
		
		break;
		
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// ジャンプグラフィック表示タスク作成 *******************************************************/
#ifdef PUK2
ACTION *MakeBattleJumpDisp( ACTION *pParent, int sprNo, int x, int y )
#else
ACTION *MakeBattleJumpDisp( ACTION *pParent, int sprNo )
#endif
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleJumpDisp;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
#ifdef PUK2
	pAct->fx = pParent->fx+(float)x;
	pAct->fy = pParent->fy+(float)y;
#else
	pAct->fx = pParent->fx;
	//pAct->fy = pParent->fy - 120;
	pAct->fy = pParent->fy;
#endif
	// Ｙ增分
	pAct->dfy = -8;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon=BLTF_NOCHG;
	pAct->bltf=BLTF_NOCHG;
#endif
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	return pAct;
}

// グラフィック表示处理 *******************************************************/
void BattleGraDisp( ACTION *pAct )
{
	// カウンタープラス
	pAct->dx++;
	if( pAct->dx >= pAct->dy ) DeathAction( pAct );
		
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// グラフィック表示タスク作成 *******************************************************/
ACTION *MakeBattleGraDisp( ACTION *pParent, int x, int y, int frame, int sprNo )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleGraDisp;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	pAct->fx = pParent->fx + ( float )x;
	//pAct->fy = pParent->fy - 120;
	pAct->fy = pParent->fy + ( float )y;
	// Ｙ增分
	pAct->dfy = -8;
	// 当たり判定する
	//pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// 表示时间ＭＡＸ
	pAct->dy = frame;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}


// 状态异常マーク处理 *******************************************************/
void BattleAbnormal( ACTION *pAct )
{
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_BattleAbnormal );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		
		// アニメーション
		pattern( pAct, ANM_LOOP );
		
		// 表示座标に变换
		pAct->fx = ( ( ACTION *)pAct->pOther )->fx;
		pAct->fy = ( ( ACTION *)pAct->pOther )->fy + 1;
		
	}
	
	// 亲のＨＩＤＥフラグと同じにする
	//if( ( ( ACTION *)pAct->pOther )->>atr & ACT_ATR_HIDE ){
	//}else{
	//}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// 状态异常マーク作成 *******************************************************/
ACTION *MakeBattleAbnormal( ACTION *pParent, int sprNo )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// 亲に状态异常ポインタあったら返回
	if( ( ( BC_YOBI *)pParent->pYobi )->pAbnormal != NULL ) return NULL;
	
	// 亲のＢＣフラグに反映させる
	if( sprNo == SPR_doku ){
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ABNORMAL_POISON;	// 0x0010：状态异常フラグ（毒）
	}else
	if( sprNo == SPR_doku + 1 ){
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ABNORMAL_SLEEP;	// 0x0020：状态异常フラグ（眠り）
	}else
	if( sprNo == SPR_doku + 2 ){
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ABNORMAL_STONE;	// 0x0040：状态异常フラグ（石化）
	}else
	if( sprNo == SPR_doku + 3 ){
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ABNORMAL_INEBRIETY;	// 0x0080：状态异常フラグ（酔い）
	}else
	if( sprNo == SPR_doku + 4 ){
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ABNORMAL_CONFUSION;	// 0x0100：状态异常フラグ（混乱）
	}else
	if( sprNo == SPR_doku + 5 ){
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ABNORMAL_FORGET;	// 0x0200：状态异常フラグ（忘却）
	}
	
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleAbnormal;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_CHAR;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// Ｙ座标ソートＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	
	// 座标记忆する
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy + 1;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->pAbnormal = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 属性反転エフェクト处理 *******************************************************/
void BattleReverseType( ACTION *pAct )
{
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_BattleReverseType );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		
		// アニメーション
		pattern( pAct, ANM_LOOP );
		
		// 表示座标に变换
		pAct->fx = ( ( ACTION *)pAct->pOther )->fx;
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		//LoadBmp( ( (  ACTION *)pAct->pOther )->bmpNo );
		//pAct->fy = ( ( ACTION *)pAct->pOther )->fy + ( ( ACTION *)pAct->pOther )->anim_y / 2;
		pAct->fy = ( ( ACTION *)pAct->pOther )->fy + 1;
		
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// 属性反転エフェクト作成 *******************************************************/
ACTION *MakeBattleReverseType( ACTION *pParent, int sprNo )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// 亲に状态异常ポインタあったら返回
	if( ( ( BC_YOBI *)pParent->pYobi )->pReverseType != NULL ) return NULL;
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleReverseType;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_EFFECT;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else pAct->atr |= ACT_ATR_FLASH_0;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
	//LoadBmp( pParent->bmpNo );
	// 座标记忆する
	pAct->fx = pParent->fx;
	//pAct->fy = pParent->fy + pParent->anim_y / 2;
	pAct->fy = pParent->fy + 1;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->pReverseType = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 気絶エフェクト处理 *******************************************************/
void BattleStun( ACTION *pAct )
{
	ACTION *pOther = (  ACTION *)pAct->pOther;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_BattleStun );
#endif
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		// アニメーション
		pattern( pAct, ANM_LOOP );
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
		GetBmpSize( pOther->bmpNo );
#else
		LoadBmp( pOther->bmpNo );
#endif
		// 表示座标に变换
		pAct->fx = pOther->fx + pOther->anim_x + SpriteInfo[ pOther->bmpNo ].width / 2;
		pAct->fy = pOther->fy + pOther->anim_y + SpriteInfo[ pOther->bmpNo ].height / 2;
		//pAct->fy = pOther->fy + 1;
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// 気絶エフェクト作成 *******************************************************/
ACTION *MakeBattleStun( ACTION *pParent, int sprNo )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// 亲に状态异常ポインタあったら返回
	if( ( ( BC_YOBI *)pParent->pYobi )->pStun != NULL ) return NULL;
	
	// アクションポインタ取得
#ifdef PUK3_RIDE_BATTLE
	pAct = GetAction( PRIO_CHR, NULL );
#else
	pAct = GetAction( PRIO_CHR, NULL );
#endif
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleStun;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_EFFECT;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// Ｙ座标ソートＯＮ
	//pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pParent->bmpNo );
#else
	LoadBmp( pParent->bmpNo );
#endif
	// 座标记忆する
	pAct->fx = pParent->fx + pParent->anim_x + SpriteInfo[ pParent->bmpNo ].width / 2;
	pAct->fy = pParent->fy + pParent->anim_y + SpriteInfo[ pParent->bmpNo ].height / 2;
	//pAct->fy = pParent->fy + 1;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->pStun = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// ２アクションマーク表示处理 *******************************************************/
void Battle2Action( ACTION *pAct )
{
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_Battle2Action );
#endif
#if 1
	ACTION *pOther = (  ACTION *)pAct->pOther;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		//パラメータマークがあった场合
		if(( ( BC_YOBI *)pOther->pYobi )->pParameterUpDown != NULL) {
			if(pAct->dx > -10) pAct->dx--;
		}else{
			if(pAct->dx < 0) pAct->dx++;
		}
	
		// アニメーション
		pattern( pAct, ANM_LOOP );
	
		// ムービー中のとき
		if( ProcNo == PROC_BATTLE && SubProcNo == BATTLE_PROC_MOVIE ){
			// 表示ＯＮ
			pAct->atr &= ~ACT_ATR_HIDE;
		}else{
			// 表示ＯＦＦ
			pAct->atr |= ACT_ATR_HIDE;
		}
#ifdef PUK2
		if ( (pAct->rgbaon&8) && pAct->rgba.a == 0 ) pAct->atr = ACT_ATR_HIDE;
#endif
		// 表示座标に变换
		pAct->fx = ( ( ACTION *)pAct->pOther )->fx + pAct->dx;
		pAct->fy = ( ( ACTION *)pAct->pOther )->fy;
		
	}
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
#else

	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		// ムービー中のとき
		if( ProcNo == PROC_BATTLE && SubProcNo == BATTLE_PROC_MOVIE ){
			// フォント表示ＯＮ
			pAct->atr &= ~ACT_ATR_FONT_HIDE;
		}else{
			// フォント表示ＯＦＦ
			pAct->atr |= ACT_ATR_FONT_HIDE;
		}
		// 表示座标に变换
		pAct->fontfX = ( ( ACTION *)pAct->pOther )->fx;
		pAct->fontfY = ( ( ACTION *)pAct->pOther )->fy - 8;
	}
	// 表示座标に变换
	pAct->fontX = ( int )pAct->fontfX + BattleMapX;
	pAct->fontY = ( int )pAct->fontfY + BattleMapY;
#endif

#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// ２アクションマーク作成 *******************************************************/
ACTION *MakeBattle2Action( ACTION *pParent, int no )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// 亲に２アクションポインタあったら返回
	if( ( ( BC_YOBI *)pParent->pYobi )->p2Action != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( ( ( BC_YOBI *)pParent->pYobi )->p2Action );
#endif
		// １アクション目のとき
		if( no == 1 ){
			( ( BC_YOBI *)pParent->pYobi )->p2Action->anim_chr_no = CG_B_ACT_1;
		}else{
			// ２アクション目に切り替え
			( ( BC_YOBI *)pParent->pYobi )->p2Action->anim_chr_no = CG_B_ACT_2;
		}
		
		// 文字列作成
		//sprintf( ( ( BC_YOBI *)pParent->pYobi )->p2Action->fontStr, "%d", no );
		
		return NULL;
	}
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = Battle2Action;

#if 1
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_EFFECT;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	// anim_tbl.h の番号
	// １アクション目のとき
	if( no == 1 ){
		pAct->anim_chr_no = CG_B_ACT_1;
	}else{
		pAct->anim_chr_no = CG_B_ACT_2;
	}
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->p2Action = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

#else
	
	
	// 文字列作成
	sprintf( pAct->fontStr, "%d", no );                                  //MLHIDE
	// 座标记忆する
	pAct->fontfX = pParent->fx;
	pAct->fontfY = pParent->fy - 8;
	
	// 表示色记忆
	pAct->fontColor = FONT_PAL_WHITE;
	// フォントの种类
	pAct->fontKind = FONT_KIND_SMALL;
	// フォントの表示优先度
	pAct->fontPrio = FONT_PRIO_BACK;
	// 非表示
	pAct->atr |= ACT_ATR_HIDE;
	
	// フォント表示ＯＦＦ
	pAct->atr |= ACT_ATR_FONT_HIDE;
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->p2Action = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->fontX = ( int )pAct->fontfX + BattleMapX;
	pAct->fontY = ( int )pAct->fontfY + BattleMapY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
#endif	
	
	return pAct;
}


// パラメーターアップダウンマーク处理 *******************************************************/
void BattleParameterUpDown( ACTION *pAct )
{

	ACTION *pOther = (  ACTION *)pAct->pOther;
	int count = 0;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_BattleParameterUpDown );
#endif
#if 1
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( (ACTION *)pAct->pOther );
#endif
	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		//２アクションマークがあった场合
		if(( ( BC_YOBI *)pOther->pYobi )->p2Action != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( ( ( BC_YOBI *)pOther->pYobi )->p2Action );
#endif
			//２アクションマークが表示ＯＦＦなら元に戾す
			if( (( BC_YOBI *)pOther->pYobi )->p2Action->atr & ACT_ATR_HIDE){
				if(pAct->dx > 0) pAct->dx--;
			}else{
				if(pAct->dx < 10) pAct->dx++;
			}
		}else{
			if(pAct->dx > 0) pAct->dx--;
		}
		if(pAct->dy != 0){
			pAct->scaleX += 0.04f;
			pAct->scaleY += 0.04f;
	
			if(pAct->scaleX > 1.0f) pAct->scaleX = 1.0f;
			if(pAct->scaleY > 1.0f) pAct->scaleY = 1.0f;
	
			if(pAct->scaleY >= 1.0f) {
				pAct->dfx += 1.0;
			}
	
	//		if(pAct->scaleY >= 1.0f) {
			if(pAct->dfx >= 30.0f) {
				pAct->dy += 7;
			}
			if(pAct->dy > 0) {
				pAct->dy = 0;
			}
		}
		// アニメーション
		pattern( pAct, ANM_LOOP );
		
		// 表示座标に变换
		pAct->fx = ( ( ACTION *)pAct->pOther )->fx + pAct->dx;
		pAct->fy = ( ( ACTION *)pAct->pOther )->fy - 5 + pAct->dy ;
		
	}
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
#else

	// 亲が死んだら自分も死ぬ
	if( ( ( ACTION *)pAct->pOther )->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		// 表示座标に变换
		pAct->fontfX = ( ( ACTION *)pAct->pOther )->fx;
		pAct->fontfY = ( ( ACTION *)pAct->pOther )->fy - 8;
	}
	// 表示座标に变换
	pAct->fontX = ( int )pAct->fontfX + BattleMapX;
	pAct->fontY = ( int )pAct->fontfY + BattleMapY;
#endif

#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// パラメーターアップダウンマーク作成 *******************************************************/
ACTION *MakeBattleParameterUpDown( ACTION *pParent, int sprNo ,int viewflg)
{
	ACTION *pAct;
	
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// 亲に２アクションポインタあったら（上书き仕样）
	if( ( ( BC_YOBI *)pParent->pYobi )->pParameterUpDown != NULL ) return NULL;
	
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleParameterUpDown;

	// 亲のＢＣフラグに反映させる
	if( sprNo == 0 ){
//		pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_DEF_UP;		// 0x00004000：防御力アップ中フラグ
	}else
	if( sprNo == 1 ){
		// anim_tbl.h の番号
		//pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_DEF_DOWN;		// 0x00008000：防御力ダウン中フラグ
	}else
	if( sprNo == 2 ){
		// anim_tbl.h の番号
		//pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ATK_UP;		// 0x00010000：攻击力アップ中フラグ
	}else
	if( sprNo == 3 ){
		// anim_tbl.h の番号
		//pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_ATK_DOWN;		// 0x00020000：攻击力ダウン中フラグ
	}else
	if( sprNo == 4 ){
		// anim_tbl.h の番号
		//pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_AGL_UP;		// 0x00020000：すばやさダウン中フラグ
	}else
	if( sprNo == 5 ){
		// anim_tbl.h の番号
		//pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_AGL_DOWN;		// 0x00020000：すばやさダウン中フラグ
#ifdef _TEST_TECH_YUK
	}else
	if( sprNo == 15 ){
		// anim_tbl.h の番号
		//pAct->anim_chr_no = CG_B_ACT_1;
		( ( BC_YOBI *)pParent->pYobi )->bcFlag |= BC_FLAG_RCV_UP;		// 0x00100000：回复力アップ中フラグ
		sprNo = 0;
#endif /* _TEST_TECH_YUK */
	}

	
#if 1
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_EFFECT;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	
	
	
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo + SPR_para_defup;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->pParameterUpDown = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

	//登场演出（観战なら无し）
	if(viewflg == 1){
		pAct->dy = -50;
		pAct->scaleX = 0.3f;
		pAct->scaleY = 0.3f;
	}else{
		pAct->dy = 0;
	}
	
#else

	// 文字列作成
	sprintf( pAct->fontStr, "%d", sprNo );                               //MLHIDE
	// 座标记忆する
	pAct->fontfX = pParent->fx;
	pAct->fontfY = pParent->fy - 8;
	
	// 表示色记忆
	pAct->fontColor = FONT_PAL_WHITE;
	// フォントの种类
	pAct->fontKind = FONT_KIND_SMALL;
	// フォントの表示优先度
	pAct->fontPrio = FONT_PRIO_BACK;
	// 非表示
	pAct->atr |= ACT_ATR_HIDE;
	
	// フォント表示ＯＦＦ
	pAct->atr |= ACT_ATR_FONT_HIDE;
	
	// 亲にポインタ教える
	( ( BC_YOBI *)pParent->pYobi )->pParameterUpDown = pAct;
	
	// 亲のポインタ学习
	pAct->pOther = pParent;
	
	// 表示座标に变换
	pAct->fontX = ( int )pAct->fontfX + BattleMapX;
	pAct->fontY = ( int )pAct->fontfY + BattleMapY;
	

	
#endif	
	
	return pAct;
}


// 属性优遇エフェクト处理 *******************************************************/
void BattleTreatType( ACTION *pAct )
{
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_BattleTreatType );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBm );
#endif
	// マスターが死んだら
	if( pActBm->deathFlag == TRUE ){
		DeathAction( pAct );
	}else{
		
		// アニメーション
		pattern( pAct, ANM_LOOP );
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// 属性优遇エフェクト作成 *******************************************************/
ACTION *MakeBattleTreatType( int sprNo )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBm );
#endif
	// すでに属性优遇魔法がかかっているとき
	if( ( ( BM_YOBI *)pActBm->pYobi )->pTreatType != NULL ){ 
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( ( ( BM_YOBI *)pActBm->pYobi )->pTreatType );
#endif
		// ＳＰＲ番号だけ变える
		( ( BM_YOBI *)pActBm->pYobi )->pTreatType->anim_chr_no = sprNo;
		return NULL;
	}
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleTreatType;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_EFFECT_BAK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	//pAct->anim_speed = 200;
	// 点灭フラグＯＮ
	//pAct->atr |= ACT_ATR_FLASH_0;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 座标记忆する
	//pAct->fx = 320.0;
	//pAct->fy = 260.0;
	pAct->fx = ( float )BcPos[ BcPosId[ 15 ] ].defX + ( ( float )BcPos[ BcPosId[ 5 ] ].defX - ( float )BcPos[ BcPosId[ 15 ] ].defX ) / 2.0f;
	pAct->fy = ( float )BcPos[ BcPosId[ 15 ] ].defY + ( ( float )BcPos[ BcPosId[ 5 ] ].defY - ( float )BcPos[ BcPosId[ 15 ] ].defY ) / 2.0f;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// １回アニメーション（表示させるため）
	pattern( pAct, ANM_NO_LOOP );
	
	// ポインタ学习
	( ( BM_YOBI *)pActBm->pYobi )->pTreatType = pAct;
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

/*-------------------------------------------
	その方向に移动する
--------------------------------------------*/
static void gemini2( ACTION *p )
{
	float d0;

	d0 = 270 - p->dir;
	AdjustDir( &d0 );
	// 移动
	p->fx -= CosT( d0 ) * p->speed;
	p->fy += SinT( d0 ) * p->speed;
	p->x = (int)p->fx;
	p->y = (int)p->fy;
}

// Ｘアニメ处理 ***********************************************/
static void combo_x_anime( ACTION *p )
{
	pattern( p, ANM_NO_LOOP );
	//終了なら
	if( !(--p->gx) ){
		DeathAction( p );
		// バトルマップ描画
		BackBufferDrawType = DRAW_BACK_BATTLE;
	}
}

// Ｘアニメセット处理 ***********************************************/
void set_combo_x_anime()
{
	ACTION *p;

	/* アクションリストに登録 */
	p = GetAction( PRIO_GAME_OVER, NULL );
	//作成失败なら
	if( p == NULL ){
		return;
	}
	// 实行关数
	p->func = combo_x_anime;
	// 表示优先度
	p->dispPrio = DISP_PRIO_B_EFFECT3;
	//タイマーセット
	p->gx = 90;
	//初期位置セット
	p->x = 320;
	p->y = 240;
	//アニメ番号
	p->anim_chr_no = 103017;
#ifdef PUK2
	p->bltfon = BLTF_NOCHG;
	p->bltf = BLTF_NOCHG;
#endif
	// バックサーフェスを黒でクリアー
	BackBufferDrawType = DRAW_BACK_NORMAL;
	//１０ヒットコンボ炸裂音
	play_se( SE_NO_LEVEL_UP, 320, 240 );
}

// コンボ数表示处理 ***********************************************/
static void disp_combo_num( ACTION *p )
{
//	char moji[256];

	switch( p->anim_chr_no ){
	case 0:
		//消灭カウンター更新
		p->damage++;
		//移动开始でなければ
		if( p->level ){
			//タイマー更新
			p->level--;
			break;
		}
		p->gy++;
		if( p->gy > 60 ){
			p->anim_chr_no++;
		}
		break;

	case 1:
		//消灭カウンター更新
		p->damage++;
		//出现
		gemini2( p );
		//减速
		p->speed -= 0.7f;
		//停止なら
		if( p->speed <= 0.0 ){
			p->anim_chr_no++;
			p->gy = 0;
		}
		break;

	case 2:
		//消灭カウンター更新
		p->damage++;
		//タイマー更新
		p->gy++;
		//数值かグレート表示开始なら
		if( p->atr ){
			//数值かグレートなら
			if( p->gy > 50 ){
				p->atr = 0;
				p->anim_chr_no++;
				//移动速度セット
				p->speed = 10.0f;
				p->dir = 0;
				//グレートなら
				if( p->gx == 9 ){
					//１０ヒットコンボ炸裂音
					play_se( SE_NO_LEVEL_UP, 320, 240 );
				}
			}
			break;
		}
		//終了なら
//		if( p->gy > 120 ){
		if( p->damage > 240 ){
			DeathAction( p );
			return;
		}
		break;

	case 3:
		//消灭カウンター更新
		p->damage++;
		gemini2( p );
		//タイマー更新
		p->gy++;
		//减速
		p->speed -= 1.0f;
		if( p->speed <= 0.0 ){
			//方向転换なら
			p->anim_chr_no++;
			p->dir = 180;
			break;
		}
		break;

	case 4:
		//消灭カウンター更新
		p->damage++;
		//タイマー更新
		p->gy++;
		//加速
		p->speed += 1.0f;
		//停止なら
		if( p->speed >= 10.0f ){
			p->anim_chr_no++;
		}
		gemini2( p );
		break;

	case 5:
		//消灭カウンター更新
		p->damage++;
		//タイマー更新
		p->gy++;
		//終了なら
//		if( p->gy > 120 ){
		if( p->damage > 240 ){
			DeathAction( p );
			return;
		}
		break;

	}
}

#define BMP_HIT_NUM		23140
#define BMP_HIT_COMBO	23158
static int combo_x_offs_tbl[]={ 0, 34, 54, 66, 88, 108, 131, 152, 171, 0 };
static int set_left_data[]={ 0, 8, 7, 6, 5, 4, 3, 2, 1, 9 };
// コンボ数表示セット处理 ***********************************************/
void set_combo_num( char cmbDispNum, int group )
{
	ACTION *p;
	U4 BmpNo;
	int d7;
	int x,y;
	int no;
	short dx,dy;
	char r_flg;
	float dir;
	int data;

	//１１以上なら
	if( cmbDispNum > 10 ){
		//１０にする
		cmbDispNum = 10;
	}

	//右侧なら
	if( group < 10 ){
		//右侧セット
		r_flg = 1;
		x = 640 + 32;
		dir = 270;
		y = 480 - 96;
	} else {
		//左侧セット
		r_flg = 0;
		x = 0 - 32 - 171;
		dir = 90;
		y = 96;
	}

	//１桁＋８文字
	for( d7 = 0; d7 < 1 + 8 + 1; d7++ ){
		//右侧なら
		if( r_flg ){
			//セットするデータ
			data = d7;
		} else {
			//セットするデータ
			data = set_left_data[ d7 ];
		}
		/* アクションリストに登録 */
		p = GetAction( PRIO_GAME_OVER, NULL );
		//作成失败なら
		if( p == NULL ){
			return;
		}
		// 实行关数
		p->func = disp_combo_num;
		// 表示优先度
		p->dispPrio = DISP_PRIO_B_EFFECT3;
		//种类セット
		p->gx = d7;
		//タイマーセット
		p->anim_chr_no = 0;
		//移动速度セット
		p->speed = 18;
		p->dir = dir;
		//数字なら
		if( data == 0 ){
			p->atr = ACT_ATR_HIDE;
			no = BMP_HIT_NUM - 2 + cmbDispNum;
		} else
		//最后のＯなら
		if( data == 8 ){
			// グラフィックの番号
			no = BMP_HIT_COMBO + 4;
		} else
		//グレイトなら
		if( data == 9 ){
			p->atr = ACT_ATR_HIDE;
			no = BMP_HIT_COMBO + 7;
		} else {
			no = BMP_HIT_COMBO - 1 + data;
		}
		// グラフィックの番号
		realGetNo( no, (U4 *)&BmpNo );
		realGetPos( BmpNo, &dx, &dy );
		//グレイトなら
		if( data == 9 ){
			//初期位置セット
			p->fx = (float)dx + (float)x + (float)combo_x_offs_tbl[ 4 ];
			p->fy = (float)dy + (float)y - 32;
		} else {
			//初期位置セット
			p->fx = (float)dx + (float)x + (float)combo_x_offs_tbl[ data ];
			p->fy = (float)dy + (float)y;
		}
		//初期位置セット
		p->x = (int)p->fx;
		p->y = (int)p->fy;
		//グラフィック番号
		p->bmpNo = BmpNo;
#ifdef PUK2
		p->bltfon = BLTF_NOCHG;
		p->bltf = BLTF_NOCHG;
#endif
	//移动开始タイマーセット
		p->level = d7 * 4;
		//消える时间
		p->damage = (8 - data) * 4;
		//１０ヒットでなければ
		if( cmbDispNum != 10 ){
			//終了なら
			if( d7 == 8 ){
				break;
			}
		}
	}
}

#ifdef _TEST_TECH_YUK
// 分身用アクション作成
ACTION *MakeBattleIllusionChar( ACTION *pParent)
{
	ACTION *pAct;
	BC_YOBI *pYobi;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 表示优先度
	pAct->dispPrio = pParent->dispPrio;
	// 画像番号
	pAct->anim_chr_no = pParent->anim_chr_no;
	// 亲を记忆
	pAct->pOther = pParent;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// 移动スピード
	pAct->speed = 0;
	// 方向をコピー
	pAct->dir = pParent->dir;
	pAct->anim_ang = pParent->anim_ang;

	// 座标をコピー
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

	// 分身の初期アクションを设定
	pAct->actNo = BC_ACT_STANDBY;

	// スキルＩＤ记忆
	pYobi->skillId = ( ( BC_YOBI *)pParent->pYobi )->skillId;
	// 武器ＩＤ记忆
	pYobi->weaponId = ( ( BC_YOBI *)pParent->pYobi )->weaponId;

	// 投げた人のＩＤ设定
	pYobi->myId = ( ( BC_YOBI* )pParent->pYobi )->myId;

	pattern( pAct, ANM_LOOP );

	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;

	return pAct;
}

#endif /* _TEST_TECH_YUK */

#ifdef PUK2

// 时间表示タスク作成 *******************************************************/
ACTION *MakeBattleTimeDisp( int x, int y, int time )
{
	ACTION *pAct;
	char moji[ 256 ];
	int mojiCenter;	// センターリング座标
	int len;	// 文字の长さ
	int i;
	int bmpNo;
	int xtime;

	xtime=(time<0?-time:time);
	// 文字列作成
	sprintf( moji, "%s%d:%02d", (time<0?"-":""), xtime/60, xtime%60 );   //MLHIDE
	// センターリング座标
	mojiCenter = GetStrWidth( moji ) / 2;
	// 文字の长さ
	len = strlen( moji );
	
	// 文字分ループ
	for( i = 0 ; i < len ; i++ ){
		//グラフィック番号
		bmpNo=0;
		if ('0'<=moji[i]&&moji[i]<='9') bmpNo = ColorNumGraNo[0][0] + moji[ i ] - '0';
		if (moji[i]=='-') bmpNo = ColorNumGraNo[0][2];
		if (moji[i]==':') bmpNo = ColorNumGraNo[0][3];
		if (!bmpNo) continue;

		// アクションポインタ取得
		pAct = GetAction( PRIO_CHR, NULL );
		if( pAct == NULL ) break;
	
		short dx,dy;
		// 实行关数
		pAct->func = BattlePointDisp;
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_METER;
		//种类セット
		pAct->gx = moji[ i ];
		//タイマーセット
		pAct->anim_chr_no = 0;
		//移动速度セット
		pAct->speed = 18;
		if( x<320 ) pAct->dir = 270;
		else pAct->dir = 90;
		//数字なら
		pAct->atr = ACT_ATR_HIDE;
		//グラフィック番号
		realGetNo( bmpNo , (U4 *)&pAct->bmpNo );
		// グラフィックの番号
		realGetPos( pAct->bmpNo, &dx, &dy );
		//初期位置セット
		pAct->fx = (float)dx + (float)x - mojiCenter + FontKind[ FONT_KIND_BIG ].hankakuWidth * i; // 半角サイズを足す
		pAct->fy = (float)dy + (float)y;
		//初期位置セット
		pAct->x = (int)pAct->fx;
		pAct->y = (int)pAct->fy;
		// アクションスローフラグＯＮ
		pAct->atr |= ACT_ATR_ACT_SLOW;
		// Ｙ增分
		pAct->fontdfY = -8;
		// スタートウェイト
		pAct->delta = i * 2;

		// パレットチェンジ对象外に设定
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
		
		// 表示座标に变换
		pAct->fontX = ( int )pAct->fontfX;
		pAct->fontY = ( int )pAct->fontfY;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
	}
	
	return NULL;
}

void MakeEffectRebirthHitMark( ACTION *pMather, ACTION *pEnemy )
{
	if (pMather==NULL) return;
	if (pEnemy==NULL) return;
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pMather );
	CheckAction( pEnemy );
#endif
	ACTION *Effect;
	BC_YOBI *pMatherYobi=(BC_YOBI *)pMather->pYobi;
	BC_YOBI *pEnemyYobi=(BC_YOBI *)pEnemy->pYobi;
	const int AtackEf[4]={ SPR_eff_earth_hit,SPR_eff_water_hit,SPR_eff_fire_hit,SPR_eff_wind_hit };
	const int DamageEf[4]={ SPR_eff_earth_dmg,SPR_eff_water_dmg,SPR_eff_fire_dmg,SPR_eff_wind_dmg };

	if (pMatherYobi->RbthElement){
		Effect=MakeBattleHitMark( pEnemy, AtackEf[pMatherYobi->RbthElement-1] );
		Effect->bm.rgba.a=0x96;
		Effect->bltfon=BLTF_NOCHG;
		Effect->bltf=BLTF_NOCHG;
	}
	if (pEnemyYobi->RbthElement){
		Effect=MakeBattleHitMark( pEnemy, DamageEf[pEnemyYobi->RbthElement-1] );
		Effect->bm.rgba.a=0x96;
		Effect->bltfon=BLTF_NOCHG;
		Effect->bltf=BLTF_NOCHG;
	}
}

unsigned char efficacydisp;
#ifdef PUK2_TITILEELEMENTEFFECT_2
	#ifdef _DEBUG
		unsigned char efficacydisp_2 = 0;
	#endif
#endif
/***
enum{
	EFFICACYDISP_NOMAL = 1,		// 通常战闘时表示
	EFFICACYDISP_DUEL = 2,		// デュエル时表示
	EFFICACYDISP_BOSS = 4,		// ボス战时表示
};
***/

unsigned char MakeElementWL( ACTION *pAt, ACTION *pDm )
{
	short a_et,a_wt,a_fi,a_wd;
	short d_et,d_wt,d_fi,d_wd;
	short x;			// 反転用
	unsigned char ret;
	int dif;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAt );
	CheckAction( pDm );
#endif
#ifdef PUK3_REBIRTHFLAG
	if ( ( ( (BC_YOBI *)pAt->pYobi )->bcFlagCl & BCCL_FLAG_ALBUM ) == 0 ||
		 ( ( (BC_YOBI *)pDm->pYobi )->bcFlagCl & BCCL_FLAG_ALBUM ) == 0 ){
		return 0;
	}
#else
	if ( ( ( (BC_YOBI *)pAt->pYobi )->bcFlag & BC_FLAG_ALBUM ) == 0 ||
		 ( ( (BC_YOBI *)pDm->pYobi )->bcFlag & BC_FLAG_ALBUM ) == 0 ){
		return 0;
	}
#endif
	// 通常战闘のとき出さない
	if ( !(efficacydisp&EFFICACYDISP_NOMAL) ){
		if ( EncountStatus!=5 && EncountStatus!=6 ) return 0;
	}
	// デュエルのとき出さない
	if ( !(efficacydisp&EFFICACYDISP_DUEL) ){
		if ( DuelFlag == TRUE  ) return 0;
	}
	// ボス战のとき出さない
	if ( !(efficacydisp&EFFICACYDISP_BOSS) ){
		if ( EncountStatus==5 || EncountStatus==6 ) return 0;
	}

	if (pAt==NULL) return 0;
	if (pDm==NULL) return 0;

	if (pAt->pYobi==NULL) return 0;
	if (pDm->pYobi==NULL) return 0;

	ret=0;
	a_et=a_wt=a_fi=a_wd=0;
	d_et=d_wt=d_fi=d_wd=0;

	switch( ( (BC_YOBI *)pAt->pYobi )->skillId ){
	case B_SKILL_MAGICATTACK_SORO_EARTH:	// 地属性单体攻击魔法
	case B_SKILL_MAGICATTACK_AREA_EARTH:	// 地属性周辺攻击魔法
	case B_SKILL_MAGICATTACK_SIDE_EARTH:	// 地属性片侧攻击魔法
		a_et=1;
		break;
	case B_SKILL_MAGICATTACK_SORO_WATER:	// 水属性单体攻击魔法
	case B_SKILL_MAGICATTACK_AREA_WATER:	// 水属性周辺攻击魔法
	case B_SKILL_MAGICATTACK_SIDE_WATER:	// 水属性片侧攻击魔法
		a_wt=1;
		break;
	case B_SKILL_MAGICATTACK_SORO_FIRE:		// 火属性单体攻击魔法
	case B_SKILL_MAGICATTACK_AREA_FIRE:		// 火属性周辺攻击魔法
	case B_SKILL_MAGICATTACK_SIDE_FIRE:		// 火属性片侧攻击魔法
		a_fi=1;
		break;
	case B_SKILL_MAGICATTACK_SORO_WIND:		// 风属性单体攻击魔法
	case B_SKILL_MAGICATTACK_AREA_WIND:		// 风属性周辺攻击魔法
	case B_SKILL_MAGICATTACK_SIDE_WIND:		// 风属性片侧攻击魔法
		a_wd=1;
		break;
	default:
		if (pAt->earth)	a_et=1;
		if (pAt->water)	a_wt=1;
		if (pAt->fire)	a_fi=1;
		if (pAt->wind)	a_wd=1;

		if ( ( (BC_YOBI *)pAt->pYobi )->bcFlag & BC_FLAG_REVERSE_TYPE ){
			x = a_et,	a_et = a_fi,	a_fi = x;
			x = a_wt,	a_wt = a_wd,	a_wd = x;
		}
	}

	// 受伤侧属性设定
	if (pDm->earth)	d_et=1;
	if (pDm->water)	d_wt=1;
	if (pDm->fire)	d_fi=1;
	if (pDm->wind)	d_wd=1;

	if ( ( (BC_YOBI *)pDm->pYobi )->bcFlag & BC_FLAG_REVERSE_TYPE ){
		x = d_et,	d_et = d_fi,	d_fi = x;
		x = d_wt,	d_wt = d_wd,	d_wd = x;
	}

	dif=-20;

	// 胜ち
	if (a_et){if (d_wt){ MakeBattleJumpDisp( pDm, CG_EARTH_WIN, 0, dif ),	ret|=0x01,	dif-=22; } }
	if (a_wt){if (d_fi){ MakeBattleJumpDisp( pDm, CG_WATER_WIN, 0, dif ),	ret|=0x02,	dif-=22; } }
	if (a_fi){if (d_wd){ MakeBattleJumpDisp( pDm, CG_FIRE_WIN, 0, dif ),	ret|=0x04,	dif-=22; } }
	if (a_wd){if (d_et){ MakeBattleJumpDisp( pDm, CG_WIND_WIN, 0, dif ),	ret|=0x08,	dif-=22; } }

	// 负け
	if (a_et){if (d_wd){ MakeBattleJumpDisp( pDm, CG_EARTH_LOSE, 0, dif ),	ret|=0x10,	dif-=22; } }
	if (a_wt){if (d_et){ MakeBattleJumpDisp( pDm, CG_WATER_LOSE, 0, dif ),	ret|=0x20,	dif-=22; } }
	if (a_fi){if (d_wt){ MakeBattleJumpDisp( pDm, CG_FIRE_LOSE, 0, dif ),	ret|=0x40,	dif-=22; } }
	if (a_wd){if (d_fi){ MakeBattleJumpDisp( pDm, CG_WIND_LOSE, 0, dif ),	ret|=0x80,	dif-=22; } }

	return ret;
}

ACTION *MakeBattleNewDeath( ACTION *pParent )
{
	int i;
	ACTION *pAct;

	const int tableX[ 16 ] = {  48,  44,  34,  18, 0, -18, -34, -44, 
						 -48, -44, -34, -18, 0,  18,  34,  44 };
						 
	const int tableY[ 16 ] = { 0,  14,  26,  44,  47 , 33,  26,  14, 
						 0, -14, -26, -44, -47, -33, -26, -14 };

	// 场所ランダム
	i = Rnd( 0, 15 );
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	
	// ポインタあるとき
	if( pAct != NULL ){
	
		// 实行关数
		pAct->func = BattleEffectUp;
		
		// 点灭
#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
			else pAct->atr |= ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_1 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() ) pAct->atr |= ACT_ATR_TRANCEPARENT;
		else{
			if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_FLASH_0;
			else pAct->atr |= ACT_ATR_FLASH_1;
		}
		#endif
#else
		if( Rnd( 0, 1 ) ) pAct->atr |= ACT_ATR_FLASH_0;
		else pAct->atr |= ACT_ATR_FLASH_1;
#endif
		
		// 表示优先度
		pAct->dispPrio = DISP_PRIO_B_HIT_MARK;

		// 步くアニメーション
		pAct->anim_speed = ANM_NOMAL_SPD;
		
		// anim_tbl.h の番号
		pAct->anim_chr_no = SPR_ultimate;
#ifdef PUK2
		pAct->bltfon = BLTF_NOCHG;
		pAct->bltf = BLTF_NOCHG;
#endif
		
		// 中心座标记忆する
		pAct->fx = ( float )( pParent->x + tableX[ i ] );
		pAct->fy = ( float )( pParent->y + tableY[ i ] );
		pAct->actNo = pParent->y + tableY[ i ] - 100;
		// 加速度
		pAct->dfx = 0.2F;
		// 亲アクション学习
		pAct->pOther = pParent;
		
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
		// 表示优先度普通
		pAct->bufCount = 0;
#endif
	}
	return(pAct);
}

#endif
#ifdef PUK2

void BattleEffectFade( ACTION *pAct, int a_ssti, int a_add, unsigned long atr_on, unsigned long atr_off )
{
	BC_YOBI *pYobi = (BC_YOBI *)pAct->pYobi;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	if (pYobi->pAbnormal){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pAbnormal );
#endif
		if ( a_ssti >=0 ) pYobi->pAbnormal->rgba.a = a_ssti;
		pYobi->pAbnormal->rgba.a += a_add;
		pYobi->pAbnormal->atr &= ~atr_off;
		pYobi->pAbnormal->atr |= atr_on;
		pYobi->pAbnormal->rgbaon = 0;
		if ( pYobi->pAbnormal->rgba.a < 255 ) pYobi->pAbnormal->rgbaon = 8;
		pYobi->pAbnormal->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pAbnormal->rgba.a == 0 ) pYobi->pAbnormal->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->p2Action){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->p2Action );
#endif
		if ( a_ssti >=0 ) pYobi->p2Action->rgba.a = a_ssti;
		pYobi->p2Action->rgba.a += a_add;
		pYobi->p2Action->atr &= ~atr_off;
		pYobi->p2Action->atr |= atr_on;
		pYobi->p2Action->rgbaon = 0;
		if ( pYobi->p2Action->rgba.a < 255 ) pYobi->p2Action->rgbaon = 8;
		pYobi->p2Action->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->p2Action->rgba.a == 0 ) pYobi->p2Action->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pReverseType){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pReverseType );
#endif
		if ( a_ssti >=0 ) pYobi->pReverseType->rgba.a = a_ssti;
		pYobi->pReverseType->rgba.a += a_add;
		pYobi->pReverseType->atr &= ~atr_off;
		pYobi->pReverseType->atr |= atr_on;
		pYobi->pReverseType->rgbaon = 0;
		if ( pYobi->pReverseType->rgba.a < 255 ) pYobi->pReverseType->rgbaon = 8;
		pYobi->pReverseType->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pReverseType->rgba.a == 0 ) pYobi->pReverseType->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pStun){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pStun );
#endif
		if ( a_ssti >=0 ) pYobi->pStun->rgba.a = a_ssti;
		pYobi->pStun->rgba.a += a_add;
		pYobi->pStun->atr &= ~atr_off;
		pYobi->pStun->atr |= atr_on;
		pYobi->pStun->rgbaon = 0;
		if ( pYobi->pStun->rgba.a < 255 ) pYobi->pStun->rgbaon = 8;
		pYobi->pStun->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pStun->rgba.a == 0 ) pYobi->pStun->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pParameterUpDown){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pParameterUpDown );
#endif
		if ( a_ssti >=0 ) pYobi->pParameterUpDown->rgba.a = a_ssti;
		pYobi->pParameterUpDown->rgba.a += a_add;
		pYobi->pParameterUpDown->atr &= ~atr_off;
		pYobi->pParameterUpDown->atr |= atr_on;
		pYobi->pParameterUpDown->rgbaon = 0;
		if ( pYobi->pParameterUpDown->rgba.a < 255 ) pYobi->pParameterUpDown->rgbaon = 8;
		pYobi->pParameterUpDown->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pParameterUpDown->rgba.a == 0 ) pYobi->pParameterUpDown->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pActTrance){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pActTrance );
#endif
		if ( a_ssti >=0 ) pYobi->pActTrance->rgba.a = a_ssti;
		pYobi->pActTrance->rgba.a += a_add;
		pYobi->pActTrance->atr &= ~atr_off;
		pYobi->pActTrance->atr |= atr_on;
		pYobi->pActTrance->rgbaon = 0;
		if ( pYobi->pActTrance->rgba.a < 255 ) pYobi->pActTrance->rgbaon = 8;
		pYobi->pActTrance->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pActTrance->rgba.a == 0 ) pYobi->pActTrance->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pActSubRbth[0]){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pActSubRbth[0] );
#endif
		if ( a_ssti >=0 ) pYobi->pActSubRbth[0]->rgba.a = a_ssti;
		pYobi->pActSubRbth[0]->rgba.a += a_add;
		pYobi->pActSubRbth[0]->atr &= ~atr_off;
		pYobi->pActSubRbth[0]->atr |= atr_on;
		pYobi->pActSubRbth[0]->rgbaon = 0;
		if ( pYobi->pActSubRbth[0]->rgba.a < 255 ) pYobi->pActSubRbth[0]->rgbaon = 8;
		pYobi->pActSubRbth[0]->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pActSubRbth[0]->rgba.a == 0 ) pYobi->pActSubRbth[0]->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pActSubRbth[1]){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pActSubRbth[1] );
#endif
		if ( a_ssti >=0 ) pYobi->pActSubRbth[1]->rgba.a = a_ssti;
		pYobi->pActSubRbth[1]->rgba.a += a_add;
		pYobi->pActSubRbth[1]->atr &= ~atr_off;
		pYobi->pActSubRbth[1]->atr |= atr_on;
		pYobi->pActSubRbth[1]->rgbaon = 0;
		if ( pYobi->pActSubRbth[1]->rgba.a < 255 ) pYobi->pActSubRbth[1]->rgbaon = 8;
		pYobi->pActSubRbth[1]->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pActSubRbth[1]->rgba.a == 0 ) pYobi->pActSubRbth[1]->atr |= ACT_ATR_HIDE;
	}
	if (pYobi->pActSubRbth[2]){
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pActSubRbth[2] );
#endif
		if ( a_ssti >=0 ) pYobi->pActSubRbth[2]->rgba.a = a_ssti;
		pYobi->pActSubRbth[2]->rgba.a += a_add;
		pYobi->pActSubRbth[2]->atr &= ~atr_off;
		pYobi->pActSubRbth[2]->atr |= atr_on;
		pYobi->pActSubRbth[2]->rgbaon = 0;
		if ( pYobi->pActSubRbth[2]->rgba.a < 255 ) pYobi->pActSubRbth[2]->rgbaon = 8;
		pYobi->pActSubRbth[2]->atr &= ~ACT_ATR_HIDE;
		if ( pYobi->pActSubRbth[2]->rgba.a == 0 ) pYobi->pActSubRbth[2]->atr |= ACT_ATR_HIDE;
	}
}

void BattleMakeBGBmp( ACTION *pAct )
{
	switch(pAct->actNo){
	case 0:
		if (pAct->rgba.a+pAct->delta<255){
			pAct->rgba.a+=pAct->delta;
			break;
		}
		else{
			pAct->rgba.a=255;
#ifdef PUK3_NEWSKILL_LVDOWN
			// ＢＧを变更するか
			if ( pAct->state ){
				BattleMapNo = pAct->anim_chr_no;
				pAct->actNo = 4;
			}
#endif
		}
	case 1:
		pAct->atr &= ~ACT_ATR_HIDE;
		pAct->rgbaon=8;
#ifdef PUK3_NEWSKILL_LVDOWN
		// ＢＧを变更するか
		if ( pAct->state ){
			BattleMapNo = pAct->anim_chr_no;
			pAct->actNo = 4;
		}
#endif
		break;

	case 2:
		pAct->rgbaon=8;
		if (pAct->rgba.a-pAct->delta>0){
			pAct->rgba.a-=pAct->delta;
			break;
		}
		else{
			pAct->rgba.a=0;
		}
	case 3:
		pAct->rgbaon=0;
		pAct->atr |= ACT_ATR_HIDE;
		DeathAction( pAct );
		break;
#ifdef PUK3_NEWSKILL_LVDOWN
	case 4:		// 背景の变更处理続き
		DeathAction( pAct );
		break;
#endif
	}
}

// 背景エフェクト作成
#ifdef PUK3_NEWSKILL_LVDOWN
ACTION *MakeBGBmp( int SprNo, unsigned char speed, BOOL changeFlag )
#else
ACTION *MakeBGBmp( int SprNo, unsigned char speed )
#endif
{
	ACTION *pAct;

	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, 0 );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleMakeBGBmp;

	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 效果のスプライト番号
#ifdef PUK3_NEWSKILL_LVDOWN
	pAct->anim_chr_no = SprNo;
#endif
	pAct->bmpNo = -1;
	if ( SprNo >= 0 ) realGetNo( SprNo, (U4 *)&pAct->bmpNo );
	pAct->anim_no = 0;		// デフォ
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 0;
	pAct->delta = speed;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_BG;
	/* 初期位置 */
	pAct->fx = 0;
	pAct->fy = 0;

	// 行动番号
	pAct->actNo = 0;
#ifdef PUK3_NEWSKILL_LVDOWN
	// ＢＧを变更するか
	pAct->state = changeFlag;
#endif

	pAct->rgbaon=8;
	pAct->rgba.a=0;
	
	// 表示座标に变换
	pAct->x = 0;
	pAct->y = 0;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	return pAct;
}

// 战闘フラッシュ
void BattleFlushlight( ACTION *pAct )
{
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		DeathAction( pAct );
	}
}

// 战闘フラッシュ作成
ACTION *MakeBattleFlushlight( int x, int y )
{
	ACTION *pAct;

	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleFlushlight;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 点灭フラグＯＮ
	#ifdef PUK2
		if ( !getUsable3D() ){
			pAct->bltfon = BLTF_ADDBLT;
			pAct->bltf = 0;
			pAct->atr |= ACT_ATR_FLASH_0;
		}
	#else
		pAct->atr |= ACT_ATR_FLASH_0;
	#endif
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = SPR_eff_guildmon_cam;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif
	
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 0;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

	// 表示座标
	pAct->x = x;
	pAct->y = y;

	return pAct;
}

// フラッシュエフェクト作成アクション
void BattleFlushlightMaker( ACTION *pAct )
{
	if ( !( pAct->actNo & 7 ) ) MakeBattleFlushlight( rand()%640, rand()%480 );

	pAct->actNo--;
	if ( pAct->actNo < 0 ) DeathAction( pAct );
}

// フラッシュエフェクト作成アクション作成
ACTION *MakeBattleFlushlightMaker( int cnt )
{
	ACTION *pAct;

	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, 0 );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleFlushlightMaker;

	// 非表示
	pAct->atr |= ACT_ATR_HIDE;
	// 效果のスプライト番号
	pAct->anim_no = 0;		// デフォ
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_BG;

	// 时间
	pAct->actNo = cnt;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

	return pAct;
}

struct BATTLEMESSAGEBOX{
	ACTION *pAct;
	char **Message;
	int num;
	int fonttype;
	int time;
#ifdef PUK2_NEW_MENU
	short defx, defy;
#endif
};
#ifdef PUK2_NEW_MENU
ACTION *makeWindowDisp( int, int, int, int, int, BOOL adjust = TRUE );
#else
ACTION *makeWindowDisp( int, int, int, int, int );
#endif

void BattleMassageBox( ACTION *pAct )
{
	BATTLEMESSAGEBOX *pYobi = (BATTLEMESSAGEBOX *)pAct->pYobi;
	int i;

	// ウィンドウ出来あがっていたら
	if( pYobi->pAct->hp > 0 ){
		for(i=0;i<pYobi->num;i++){
#ifdef PUK2_NEW_MENU
			StockFontBuffer( pYobi->pAct->x + 40, pYobi->pAct->y + 40, FONT_PRIO_FRONT, 0, pYobi->Message[i], 0 );
#else
			StockFontBuffer( pYobi->pAct->x + pYobi->defx + 40, pYobi->pAct->y + pYobi->defx + 40, FONT_PRIO_FRONT, 0, pYobi->Message[i], 0 );
#endif
		}

		pYobi->time--;
		if (pYobi->time<0){
			for(i=0;i<pYobi->num;i++){
#ifdef PUK2_MEMCHECK
				memlistrel( pYobi->Message[i], MEMLISTTYPE_BATTLE_MBBOXTEXT );
#endif
				free( pYobi->Message[i] );
			}
#ifdef PUK2_MEMCHECK
			memlistrel( pYobi->Message, MEMLISTTYPE_BATTLE_MBBOX );
#endif
			free( pYobi->Message );
			DeathAction( pYobi->pAct );
			DeathAction( pAct );
		}
	}
}

// 信息ボックス作成
ACTION *MakeBattleMassageBox( char type, char *Message, int fonttype, int time )
{
	ACTION *pAct;
	BATTLEMESSAGEBOX *pYobi;
	int i, len, maxlen = 0, maxlenline = -1;
	char *bp, *p;
	int w,h;
#ifndef PUK2_NEW_MENU
	int w2,h2;
#endif

	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof(BATTLEMESSAGEBOX), ACT_T_BATTLEMESSAGEBOX );
#else
	pAct = GetAction( PRIO_CHR, sizeof(BATTLEMESSAGEBOX) );
#endif
	if( pAct == NULL ) return NULL;

	pYobi = (BATTLEMESSAGEBOX *)pAct->pYobi;

	// 实行关数
	pAct->func = BattleMassageBox;

	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;

	// 表示时间
	pYobi->time = time;

	// フォント种类
	pYobi->fonttype = fonttype;

	// 信息登録
	bp = Message;
	for(pYobi->num=0;;pYobi->num++){
		p = strchr( bp, '\n' );
		if (!p){ pYobi->num++;	break; }
		bp = p + 1;
	}

	pYobi->Message = (char **)calloc( pYobi->num, sizeof(char *) );
#ifdef PUK2_MEMCHECK
	memlistset( pYobi->Message, MEMLISTTYPE_BATTLE_MBBOX );
#endif

	bp = Message;
	for(i=0;bp;i++){
		p = strchr( bp, '\n' );
		if (p) len = (int)p - (int)bp;
		else len = strlen(bp);

		if (maxlen<len){
			maxlen = len;
			maxlenline = i;
		}

		pYobi->Message[i] = (char *)calloc( len + 1, sizeof(char) );
#ifdef PUK2_MEMCHECK
		memlistset( pYobi->Message[i], MEMLISTTYPE_BATTLE_MBBOXTEXT );
#endif

		memcpy( pYobi->Message[i], bp, len );
		pYobi->Message[i][len] = '\0';

		if (!p) break;
		bp = p + 1;
	}

	w = 80;
	if (maxlenline>=0){
		w += GetStrWidth( pYobi->Message[maxlenline], pYobi->fonttype );
	}

	h = 80;
	if (pYobi->num>0) h += FontKind[pYobi->fonttype].hankakuHeight;
	if (pYobi->num>1) h += (FontKind[pYobi->fonttype].hankakuHeight+1)*pYobi->num;

#ifndef PUK2_NEW_MENU
	// サイズを泛用ウィンドウにあわせる
	if( (w % SURFACE_WIDTH) != 0 )
	{
		// 割り切れないなら割り切れる值まで足す
		w2 = (w / SURFACE_WIDTH + 1) * SURFACE_WIDTH;
	}
	if( (h % SURFACE_HEIGHT) != 0 )
	{
		// 割り切れないなら割り切れる值まで足す
		h2 = (h / SURFACE_HEIGHT + 1) * SURFACE_HEIGHT;
	}

	pYobi->defx = (w2 - w) >> 1;
	pYobi->defy = (h2 - h) >> 1;
#endif

	// ウィンドウの作成
#ifdef PUK2_NEW_MENU
	//按窗口分辨率计算战斗信息框的显示位置(默认640-w,480-h)
	pYobi->pAct = makeWindowDisp( (DEF_APPSIZEX -w)>>1, (DEF_APPSIZEY-h)>>1, w, h, type, FALSE );
#else
	pYobi->pAct = makeWindowDisp( (640-w2)>>1, (480-h2)>>1, w2, h2, type );
#endif
	// ウィンドウ开く音
	play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	
	return pAct;
}

#ifdef PUK2_NEWSKILL

// 突风エフェクト ***********************************************/
void BattleEffectHItMagicToppu( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	BOOL MoveEnemy = FALSE;

	//int i;
	
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId );
#endif
	// 相手がスタンバイじゃないとき返回
	if( pAct->actNo == 0 && pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ]->actNo != BC_ACT_STANDBY ){
		return;
	}else{
		pAct->actNo = 1;
	}
	
	// 到着チェック
	if( pAct->anim_hit == 0 && CheckDistance( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ] ) <= B_NEAR_DIST ){
		// 相手の予备构造体
		ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];
		BC_YOBI *pEnemyYobi = ( BC_YOBI *)pActEnemy->pYobi;

		// デフォルトの方向へ向ける
		//ChangeDefDir( pActEnemy );
		// 方向からアングルに变换する
		//DirToAngle( pActEnemy );
		// 自分ポインタを相手に伝える
		//pEnemyYobi->enemyList[ pEnemyYobi->enemyListCnt ].enemyId = pYobi->myId;
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( pYobi->myId );
#endif
		pActEnemy->pOther = pActBc[ pYobi->myId ];
		// 相手にムービーフラグを教える
		pEnemyYobi->bmFlag = pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag;
		
		// Ｂｍフラグにチャット文字列があるかチェック
#ifdef PUK3_WEPON_BREAK
		BattleCheckBmStrGraphic( pYobi->myId, pAct );
#else
		BattleCheckBmStrGraphic( pYobi->myId, &( pYobi->enemyList[ pYobi->enemyListCnt ] ) );
#endif
		// 回避する时
		if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_AVOIDANCE ){
			// 相手を回避へ
			pActEnemy->actNo = BC_ACT_AVOIDANCE;
		}else{
			// リバースエフェクト
			if( pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag & BM_FLAG_REBIRTH_GUARD ){
				MakeBattleJumpDisp( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], CG_REVERTH_LOSE );
			}
			// エフェクトサイズで分岐
			if( pAct->anim_chr_no == SPR_bigwind1 ){
				// ヒット音
				play_se( 269, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_b_wind1 ){
				// ヒット音
				play_se( 269, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_m_wind1 ){
				// ヒット音
				play_se( 268, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_s_wind1 ){
				// ヒット音
				play_se( 268, pAct->x, pAct->y );
			}
			// 当たっていないなら
			if( (pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag&BM_FLAG_REBIRTH_GUARD) ){
				// 相手の行动开始
				BattleNextActList( pActEnemy );
			}
		}
		// 初期化
		pAct->anim_hit = 1;
	}
	
	// 行动时间のとき
	if( pAct->delta-- <= 0 ){
		// 初期化
		if( pAct->delta == -1 ){ 
			// エフェクトサイズで分岐
			if( pAct->anim_chr_no == SPR_bigwind1 ){
				// 発生音
				play_se( 269, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_b_wind1 ){
				// 発生音
				play_se( 268, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_m_wind1 ){
				// 発生音
				play_se( 268, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}else
			if( pAct->anim_chr_no == SPR_s_wind1 ){
				// 発生音
				play_se( 268, pAct->x, pAct->y );
				//play_se( 4, pAct->x, pAct->y );
			}
		}
		// 扩大
		//pAct->scaleX += 0.1F;
		//pAct->scaleY += 0.1F;
		// リミットチェック
		//if( pAct->scaleX > 1.0F ){
			// 调节
		//	pAct->scaleX = 1.0F;
		//	pAct->scaleY = 1.0F;
		//}
		// 飞行音
		//if( pAct->delta == -1 ) play_se( 11, pAct->x, pAct->y );
		
		// ヒットストップの时カウンターマイナス
		if( pYobi->hitStopCnt > 0 ){ 
			pYobi->hitStopCnt--;
			// 缩小
			//pAct->scaleX -= 0.1F;
			//pAct->scaleY -= 0.1F;
			// 小さくなったら抹杀
			//if( pAct->scaleX <= 0 ){
			//	DeathAction( pAct );
			//}
			// 地震フラグＯＮ
			//BattleMapAcceleMax = pYobi->techId / 2 + 1;
			//BattleMapQuakeFlag = TRUE;
		}
		// ヒットストップじゃない时
		if( pYobi->hitStopCnt == 0 ){
			// アニメーションスピード
			//pAct->anim_speed = 400;
			// アニメーション
			pattern( pAct, ANM_LOOP );
			
			// ヒットして无いとき
			if( pAct->anim_hit == 0 ){ 
			
				// ハンドリング设定
				pAct->fontdfX += 0.1F;
				
				// 目的の方向に向ける
				//ChangeDir( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], FALSE, pAct->speed );
				ChangeDir( pAct, pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ], FALSE, pAct->fontdfX );
				//pAct->speed += 0.1F;
			}else{
				// 当たっていないなら
				if( (pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag&BM_FLAG_AVOIDANCE) ||
					(pYobi->enemyList[ pYobi->enemyListCnt ].bmFlag&BM_FLAG_REBIRTH_GUARD) );
				// 当たってるなら
				else MoveEnemy = TRUE;

				// 缩小
				//pAct->scaleX -= 0.1F;
				//pAct->scaleY -= 0.1F;
				// 小さくなったら抹杀
				//if( pAct->scaleX <= 0 ){
				//	DeathAction( pAct );
				//}
				// 画面外に出たら
				if( pAct->fx < - 200 || pAct->fx > DEF_APPSIZEX + 200
					|| pAct->fy < - 200 || pAct->fy > DEF_APPSIZEY + 200 ){
					if (MoveEnemy){
						// 相手の行动开始
						BattleNextActList( pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ] );
					}
					// 抹杀
					DeathAction( pAct );
				}
			}
			// 目的地に移动する
			MoveDir( pAct );

			if (MoveEnemy){
				ACTION *pActEnemy = pActBc[ pYobi->enemyList[ pYobi->enemyListCnt ].enemyId ];
				int df;

				// 相手に受伤アクションをさせる
				pActEnemy->anim_no = ANIM_DAMAGE;

				df = pAct->delta;
				df &= 0x1f;
				if ( df < 0x10 ){
					df -= 0x10 / 2;
					df = (df * df) / 2;
				}else{
					df -= 0x10;
					df -= 0x10 / 2;
					df = 64 - (df * df) / 2;
				}

				// 相手を连れ去る
				pActEnemy->fx = pAct->fx + BattleMapX;
				pActEnemy->fy = pAct->fy - 35 - df + BattleMapY;

				// 回転させる
				pActEnemy->anim_ang++;
				// リミットチェック
				if( pActEnemy->anim_ang >= 8 ) pActEnemy->anim_ang -= 8;
			}

#if 1
			// エフェクトサイズで分岐
			if( pAct->anim_chr_no == SPR_bigwind1 ){
				// 烟作成
				//if( Rnd( 0, ( 9 - ( int )pAct->speed ) / 2 ) == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -10, 10 ), SPR_ultimate4, TRUE );
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -10, 10 ), SPR_ultimate4, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -10, 10 ), pAct->fy + Rnd( -10, 10 ), SPR_ultimate4, TRUE, DISP_PRIO_B_EFFECT2 );
			}else
			if( pAct->anim_chr_no == SPR_b_wind1 ){
				// 烟作成
				//if( Rnd( 0, 9 - pAct->speed ) == 0 ) 
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -6, 6 ), pAct->fy + Rnd( -6, 6 ), SPR_ultimate4, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -6, 6 ), pAct->fy + Rnd( -6, 6 ), SPR_ultimate4, TRUE, DISP_PRIO_B_EFFECT2 );
			}else
			if( pAct->anim_chr_no == SPR_m_wind1 ){
				// 烟作成
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -4, 4 ), pAct->fy + Rnd( -4, 4 ), SPR_ultimate3, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -4, 4 ), pAct->fy + Rnd( -4, 4 ), SPR_ultimate3, TRUE, DISP_PRIO_B_EFFECT2 );
				pAct->fontX++;
			}else
			if( pAct->anim_chr_no == SPR_s_wind1 ){
				// 烟作成
				//if( Rnd( 0, 1 ) ) MakeBattleSuisei( pAct->fx + Rnd( -2, 2 ), pAct->fy + Rnd( -2, 2 ), SPR_ultimate3, TRUE );
				if( pAct->fontX == 0 ) MakeBattleSuisei( pAct->fx + Rnd( -2, 2 ), pAct->fy + Rnd( -2, 2 ), SPR_ultimate3, TRUE, DISP_PRIO_B_EFFECT2 );
			}
			// フラグＯＮ，ＯＦＦ
			if( pAct->fontX >= 2 ) pAct->fontX = 0;
			else pAct->fontX++;
#endif
		}
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx + BattleMapX;
	pAct->y = ( int )pAct->fy + BattleMapY;
	
}

#endif

#if defined(PUK3_RIDE_BATTLE) || defined(PUK3_PUT_ON)
void ParentDeathCheckFunc( ACTION *pAct )
{
	ACTION *pParent = (ACTION *)pAct->pOther;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_ParentDeathCheckFunc );
#endif

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
	CheckAction( (ACTION *)pParent );
#endif
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pAct->status );
#endif
	// 亲がいないなら削除
	if ( pActBc[pAct->status] != pParent ){
		// 抹杀
		DeathAction( pAct );
#ifdef PUK3_SEGMENTATION_FAULT
		ProcPop();
#endif
		return;
	}
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}
#endif
#ifdef PUK3_RIDE_BATTLE

void RideCharFunc( ACTION *pAct )
{
	ACTION *pParent = (ACTION *)pAct->pOther;
	BC_YOBI *pYobiParent = (BC_YOBI *)pParent->pYobi;
	BOOL animFlag = 0;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
	CheckAction( (ACTION *)pParent );
#endif
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pAct->status );
#endif
	// 亲がいないなら削除
	if ( pActBc[pAct->status] != pParent ){
		// 抹杀
		DeathAction( pAct );
		return;
	}

	// 行动番号0なら普通
	if ( pAct->actNo == 0 ){
		// ソート座标を设定する
		pAct->dx = pParent->x;
		pAct->dy = pParent->y;

		// アニメーションを设定する
		switch(pParent->anim_no){
		case ANIM_ATTACK:
			pAct->anim_no = ANIM_ATTACK;	break;
		case ANIM_B_WALK_START:case ANIM_B_WALK:case ANIM_B_WALK_END:
			pAct->anim_no = ANIM_B_WALK;	break;
		case ANIM_DEAD:
			pAct->anim_no = ANIM_DAMAGE;	break;
		case ANIM_DAMAGE:
			pAct->anim_no = ANIM_DAMAGE;	break;
		case ANIM_GUARD:
			pAct->anim_no = ANIM_GUARD;		break;
		case ANIM_MAGIC:
			pAct->anim_no = ANIM_ATTACK;	break;
		case ANIM_STAND:
			pAct->anim_no = ANIM_STAND;		break;
		case ANIM_WALK:
			pAct->anim_no = ANIM_WALK;		break;
		default:
			// 上记いずれにも当てはまらないなら立ちにする
			pAct->anim_no = ANIM_STAND;
		}

		if ( pParent->actNo == BC_ACT_STANDBY ){
			if( pAct->anim_no != ANIM_STAND ){
				// 立ちアニメーション
				pAct->anim_no = ANIM_STAND;
				// 眠りじゃないかつ、石化じゃない时
				if ( !( pYobiParent->bcFlag & BC_FLAG_ABNORMAL_SLEEP ) &&
					 !( pYobiParent->bcFlag & BC_FLAG_ABNORMAL_STONE ) ){
					// アニメーション
					animFlag = 1;
				}
			}else
			// アニメーションフラグ立っていたら
			if( pParent->atr & ACT_ATR_BTL_CMD_END ){
				// 眠りじゃないかつ、石化じゃない时
				if ( !( pYobiParent->bcFlag & BC_FLAG_ABNORMAL_SLEEP ) &&
					 !( pYobiParent->bcFlag & BC_FLAG_ABNORMAL_STONE ) ){
					// アニメーション
					animFlag = 1;
				}
			}
		}else{
			// アニメーション
			animFlag = 1;
		}
		if ( pAct->anim_ang != pParent->anim_ang ){
			pAct->anim_ang = pParent->anim_ang;
			animFlag = 1;
		}

		if ( animFlag ){
			// ライドキャラをアニメーションさせる、ループの设定は亲と同じ
			if ( pAct->anim_no == ANIM_ATTACK ) pattern( pAct, ANM_LOOP );
			else pattern( pAct, pParent->animLoop_bak );
		}

		pAct->atr &= ~(ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_FLASH_1);
		pAct->atr &= ~(ACT_ATR_ACT_SLOW | ACT_ATR_HIDE);
		pAct->atr |= ACT_ATR_BATTLE_SORT | ACT_ATR_BATTLE_SORT_D;
		pAct->atr |= pParent->atr & (ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_FLASH_1);
		pAct->atr |= pParent->atr & (ACT_ATR_ACT_SLOW | ACT_ATR_HIDE);
		pAct->rgbaon = pParent->rgbaon;
		pAct->rgba = pParent->rgba;
		pAct->bltfon = pParent->bltfon;
		pAct->bltf = pParent->bltf;

		// 表示座标を设定する
		pAct->x = pParent->x + (int)(pParent->anim_cdx * pParent->scaleX);
		pAct->y = pParent->y + (int)(pParent->anim_cdy * pParent->scaleY);
	}
	// それ以外なら外部で座标とアニメーションを设定する
	else{
		// 以下の设定はここで行っておく
		pAct->atr &= ~(ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_FLASH_1);
		pAct->atr &= ~(ACT_ATR_ACT_SLOW | ACT_ATR_HIDE);
		pAct->atr |= ACT_ATR_BATTLE_SORT | ACT_ATR_BATTLE_SORT_D;
		pAct->atr |= pParent->atr & (ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_FLASH_1);
		pAct->atr |= pParent->atr & (ACT_ATR_ACT_SLOW | ACT_ATR_HIDE);
		pAct->rgbaon = pParent->rgbaon;
		pAct->rgba = pParent->rgba;
		pAct->bltfon = pParent->bltfon;
		pAct->bltf = pParent->bltf;
	}
	// サイズは亲と同じにする
	pAct->scaleX = pParent->scaleX;
	pAct->scaleY = pParent->scaleY;
}

ACTION *MakeRideChar( ACTION *pParent, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, 0, ACT_T_WIN_NULL );
#else
	pAct = GetAction( PRIO_CHR, 0 );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = ParentDeathCheckFunc;
	// アニメーションフラグ立てる
	pAct->atr |= ACT_ATR_BTL_CMD_END;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT|ACT_ATR_BATTLE_SORT_D;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	// 步くアニメーション
	pAct->anim_no = ANIM_STAND;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 3;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = pParent->anim_ang;

	// 行动番号
	pAct->actNo = 0;

	
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_CHAR;
	// 行动番号
	pAct->actNo = BC_ACT_STANDBY;

	// 表示优先度少し前
	pAct->bufCount = BC_EQUAL_PRIO_RIDECHAR;

	// 亲へのポインタ
	pAct->pOther = pParent;
	// 行动番号
	pAct->status = ( (BC_YOBI *)pParent->pYobi )->myId;
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )((ACTION *)pAct->pOther)->fx + BattleMapX;
	pAct->y = ( int )((ACTION *)pAct->pOther)->fy + BattleMapY;

	return pAct;
}

void setBattleRide( ACTION *pAct, int rideCharGraNo, int ridePetGraNo )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	BC_YOBI *pYobi = (BC_YOBI *)pAct->pYobi;

	if ( ridePetGraNo > 0 ){
		// ライドしてないなら
		if (!pYobi->pActRideChar){
			// ライドキャラを作成
			pYobi->pActRideChar = MakeRideChar( pAct, getRiderCharaGra( rideCharGraNo ) );

#ifdef PUK3_CHECK_VALUE
		}
		// ライドキャラの絵を设定
		if (pYobi->pActRideChar){
	#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pYobi->pActRideChar );
	#endif
			pYobi->pActRideChar->actNo = 0;
			pYobi->pActRideChar->delta = 0;
			pYobi->pActRideChar->anim_no = ANIM_STAND;
			pYobi->pActRideChar->anim_chr_no = getRiderCharaGra( rideCharGraNo );
		}
#else
	#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pYobi->pActRideChar );
	#endif
			pYobi->pActRideChar->actNo = 0;
			pYobi->pActRideChar->delta = 0;
			pYobi->pActRideChar->anim_no = ANIM_STAND;
		}
		// ライドキャラの絵を设定
		if (pYobi->pActRideChar){
	#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pYobi->pActRideChar );
	#endif
			pYobi->pActRideChar->anim_chr_no = getRiderCharaGra( rideCharGraNo );
		}
#endif
		// ペットの絵を设定
		pAct->anim_chr_no = ridePetGraNo;
	}else{
		// ライド中なら
		if (pYobi->pActRideChar){
	#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pYobi->pActRideChar );
	#endif
			// ライドキャラ削除
			pYobi->pActRideChar->actNo = 0;
			pYobi->pActRideChar->delta = 0;

			DeathAction(pYobi->pActRideChar);
			pYobi->pActRideChar = NULL;
		}
		pAct->anim_chr_no = rideCharGraNo;
	}
}

// 状态异常アイコンなどを设定する
void SetIconFormFlag( ACTION *pAct )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	BC_YOBI *pYobi = (BC_YOBI *)pAct->pYobi;
	int flag = pYobi->bcFlag;

	// 毒
	if( flag & BC_FLAG_ABNORMAL_POISON ){
	//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_POISON;
		// マーク作成
		MakeBattleAbnormal( pAct, SPR_doku );
	}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_POISON;
	
	// 眠り
	if( flag & BC_FLAG_ABNORMAL_SLEEP ){
	//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_SLEEP;
		// マーク作成
		MakeBattleAbnormal( pAct, SPR_doku + 1 );
	}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_SLEEP;
	
	// 石化
	if( flag & BC_FLAG_ABNORMAL_STONE ){
	//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_STONE;
		// マーク作成
		MakeBattleAbnormal( pAct, SPR_doku + 2 );
	}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_STONE;
	
	// 酔い
	if( flag & BC_FLAG_ABNORMAL_INEBRIETY ){
	//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_INEBRIETY;
		// マーク作成
		MakeBattleAbnormal( pAct, SPR_doku + 3 );
	}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_INEBRIETY;
	
	// 混乱
	if( flag & BC_FLAG_ABNORMAL_CONFUSION ){
	//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_CONFUSION;
		// マーク作成
		MakeBattleAbnormal( pAct, SPR_doku + 4 );
	}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_CONFUSION;
	
	// 忘却
	if( flag & BC_FLAG_ABNORMAL_FORGET ){
	//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_FORGET;
		// マーク作成
		MakeBattleAbnormal( pAct, SPR_doku + 5 );
	}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_FORGET;
	
	// 受伤アップフラグ立っている时
	if( flag & BC_FLAG_DAMAGE_UP )
	{
		// スケール设定
		pAct->scaleX = 1.3f;
		pAct->scaleY = 1.3f;
	}
	else
	{
		// スケール设定
		pAct->scaleX = 1.0f;
		pAct->scaleY = 1.0f;
	}
	
	// ２アクションフラグ立っているとき
	if( flag & BC_FLAG_2ACT ){
		// ２アクションマーク作成
		MakeBattle2Action( pAct, 1 );
	}else{
		// ２アクションマーク抹杀
		DeathAction( ( ( BC_YOBI *)pAct->pYobi )->p2Action );
		// ポインタ初期化
		( ( BC_YOBI *)pAct->pYobi )->p2Action = NULL;
	}
	
	
	// 属性反転フラグ
	if( flag & BC_FLAG_REVERSE_TYPE ){
		pYobi->bcFlag |= BC_FLAG_REVERSE_TYPE;
		// マーク作成
		MakeBattleReverseType( pAct, SPR_zokuhan );
	}else{ 
		pYobi->bcFlag &= ~BC_FLAG_REVERSE_TYPE;
		// 属性反転エフェクト抹杀
		DeathAction( pYobi->pReverseType );
		// ポインタ初期化
		pYobi->pReverseType = NULL;
	}

	// 属性反転フラグ
	if( flag & BC_FLAG_MORPH ){
		pYobi->bcFlag |= BC_FLAG_MORPH;
	}
	
	
	// 防御力アップフラグ
	if( flag & BC_FLAG_DEF_UP ){
		// パラメーターアップダウンマーク作成
		MakeBattleParameterUpDown( pAct, 0, 0);
	}else pYobi->bcFlag &= ~BC_FLAG_DEF_UP;
	
	// 防御力ダウンフラグ
	if( flag & BC_FLAG_DEF_DOWN ){
		// パラメーターアップダウンマーク作成
		MakeBattleParameterUpDown( pAct, 1, 0);
	}else pYobi->bcFlag &= ~BC_FLAG_DEF_UP;
	
	// 攻击力アップフラグ
	if( flag & BC_FLAG_ATK_UP ){
		// パラメーターアップダウンマーク作成
		MakeBattleParameterUpDown( pAct, 2, 0);
	}else pYobi->bcFlag &= ~BC_FLAG_ATK_UP;
	
	// 攻击力ダウンフラグ
	if( flag & BC_FLAG_ATK_DOWN ){
		// パラメーターアップダウンマーク作成
		MakeBattleParameterUpDown( pAct, 3, 0);
	}else pYobi->bcFlag &= ~BC_FLAG_ATK_UP;
	
	// すばやさアップフラグ
	if( flag & BC_FLAG_AGL_UP ){
		// パラメーターアップダウンマーク作成
		MakeBattleParameterUpDown( pAct, 4, 0);
	}else pYobi->bcFlag &= ~BC_FLAG_AGL_UP;
	
	// すばやさダウンフラグ
	if( flag & BC_FLAG_AGL_DOWN ){
		// パラメーターアップダウンマーク作成
		MakeBattleParameterUpDown( pAct, 5, 0);
	}else pYobi->bcFlag &= ~BC_FLAG_AGL_UP;
}

#endif
#ifdef PUK3_PUT_ON

void PutOnFunc( ACTION *pAct )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	ACTION *pParent = (ACTION *)pAct->pOther;
	BC_YOBI *pYobi = (BC_YOBI *)pParent->pYobi;
	int atr = 0;
	BOOL animFlag = 0;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
	CheckAction( (ACTION *)pParent );
#endif
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( pAct->status );
#endif
	// 亲がいないなら削除
	if ( pActBc[pAct->status] != pParent ){
		// 抹杀
		DeathAction( pAct );
		return;
	}

	// ソート座标を设定する
	pAct->dx = pParent->x;
	pAct->dy = pParent->y;

		// アニメーションを设定する
	switch(pParent->anim_no){
	case ANIM_DAMAGE:
		pAct->anim_no = ANIM_B_WALK;
		break;
	default:
		// 上记いずれにも当てはまらないなら立ちにする
		pAct->anim_no = ANIM_STAND;
	}
//		atr = ACT_ATR_HIDE; なら表示しない
	// リバース中なら表示しない
#ifdef PUK3_REBIRTHFLAG
	if ( pYobi->bcFlag & BC_FLAG_REBIRTH ) atr = ACT_ATR_HIDE;
#else
	if ( pYobi->pActTrance ) atr = ACT_ATR_HIDE;
#endif
#ifdef PUK2_NEWSKILL_YOTOKUNIKU
	// 羊头狗肉で变身中なら表示しない
	if ( pYobi->metamoFlag ) atr = ACT_ATR_HIDE;
#endif
	// 变身、变装中なら表示しない
	if ( pYobi->bcFlag & BC_FLAG_MORPH ) atr = ACT_ATR_HIDE;


	if ( pParent->actNo == BC_ACT_STANDBY ){
		if( pAct->anim_no != ANIM_STAND ){
			// 立ちアニメーション
			pAct->anim_no = ANIM_STAND;
			// 眠りじゃないかつ、石化じゃない时
			if ( !( pYobi->bcFlag & BC_FLAG_ABNORMAL_SLEEP ) &&
				 !( pYobi->bcFlag & BC_FLAG_ABNORMAL_STONE ) ){
				// アニメーション
				animFlag = 1;
			}
		}else
		// アニメーションフラグ立っていたら
		if( pParent->atr & ACT_ATR_BTL_CMD_END ){
			// 眠りじゃないかつ、石化じゃない时
			if ( !( pYobi->bcFlag & BC_FLAG_ABNORMAL_SLEEP ) &&
				 !( pYobi->bcFlag & BC_FLAG_ABNORMAL_STONE ) ){
				// アニメーション
				animFlag = 1;
			}
		}
	}else{
		// アニメーション
		animFlag = 1;
	}
	if ( pAct->anim_ang != pParent->anim_ang ){
		pAct->anim_ang = pParent->anim_ang;
		animFlag = 1;
	}

	if ( animFlag ){
		if ( pattern( pAct, ANM_LOOP ) ) atr = ACT_ATR_HIDE;
	}

	pAct->atr &= ~(ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_FLASH_1);
	pAct->atr &= ~(ACT_ATR_ACT_SLOW | ACT_ATR_HIDE);
	pAct->atr |= ACT_ATR_BATTLE_SORT | ACT_ATR_BATTLE_SORT_D;
	pAct->atr |= pParent->atr & (ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0 | ACT_ATR_FLASH_1);
	pAct->atr |= pParent->atr & (ACT_ATR_ACT_SLOW | ACT_ATR_HIDE);
	pAct->atr |= atr;
	pAct->rgbaon = pParent->rgbaon;
	pAct->rgba = pParent->rgba;
	pAct->bltfon = pParent->bltfon;
	pAct->bltf = pParent->bltf;

	// 表示座标を设定する
#ifdef PUK3_RIDE_BATTLE
	// ライド中なら
	if ( pYobi->pActRideChar ){
	#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pYobi->pActRideChar );
	#endif
		pAct->x = pYobi->pActRideChar->x + (int)(pYobi->pActRideChar->anim_cdx * pParent->scaleX);
		pAct->y = pYobi->pActRideChar->y + (int)(pYobi->pActRideChar->anim_cdy * pParent->scaleX);
	}else{
#endif
		pAct->x = pParent->x + (int)(pParent->anim_cdx * pParent->scaleX);
		pAct->y = pParent->y + (int)(pParent->anim_cdy * pParent->scaleY);
#ifdef PUK3_RIDE_BATTLE
	}
#endif
/***	被り物は元のサイズのまま
	// サイズは亲と同じにする
	pAct->scaleX = pParent->scaleX;
	pAct->scaleY = pParent->scaleY;
***/
}

ACTION *MakePutOn( ACTION *pParent, int sprNo )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, 0, ACT_T_WIN_NULL );
#else
	pAct = GetAction( PRIO_CHR, 0 );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = ParentDeathCheckFunc;
	// アニメーションフラグ立てる
	pAct->atr |= ACT_ATR_BTL_CMD_END;
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT|ACT_ATR_BATTLE_SORT_D;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = sprNo;
	// 步くアニメーション
	pAct->anim_no = ANIM_STAND;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 3;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = pParent->anim_ang;

	// 行动番号
	pAct->actNo = 0;

	
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_CHAR;
	// 行动番号
	pAct->actNo = BC_ACT_STANDBY;

	// 表示优先度少し前
	pAct->bufCount = BC_EQUAL_PRIO_PUTON;

	// 亲へのポインタ
	pAct->pOther = pParent;
	// 行动番号
	pAct->status = ( (BC_YOBI *)pParent->pYobi )->myId;
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )((ACTION *)pAct->pOther)->fx + BattleMapX;
	pAct->y = ( int )((ACTION *)pAct->pOther)->fy + BattleMapY;

	return pAct;
}

void setBattlePuton( ACTION *pAct, int putonGraNo )
{
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	BC_YOBI *pYobi = (BC_YOBI *)pAct->pYobi;

	if ( putonGraNo > 0 ){
		// 被り物してないなら
		if (!pYobi->pActPuton){
			// 被り物を作成
			pYobi->pActPuton = MakePutOn( pAct, putonGraNo );
		}
		// 絵を设定
		if (pYobi->pActPuton){
	#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pYobi->pActPuton );
	#endif
			pYobi->pActPuton->anim_chr_no = putonGraNo;
		}
	}else{
		// 被り物中なら
		if (pYobi->pActPuton){
			DeathAction(pYobi->pActPuton);
			pYobi->pActPuton = NULL;
		}
	}
}

#endif

#endif
#ifdef PUK3_NEWSKILL_COINSHOT

// 战闘ヒットマーク处理 ***********************************************/
void BattleCoinHit( ACTION *pAct )
{
	pAct->bm.rgba.a -= 6;

	pAct->fx += pAct->dfx;
	pAct->fy += pAct->dfy;

	pAct->dfy += 0.05f;

	pAct->x = (int)pAct->fx;
	pAct->y = (int)pAct->fy;

	// 音鸣らす
	if ( pAct->status > 0 ){
		if ( rand() % (40 / pAct->status) == 0 ){
			play_se( 65, pAct->x, pAct->y );
		}
	}

	// 一定时间たったら
	if ( pAct->actNo >= 40 ) DeathAction( pAct );
	pAct->actNo++;
}

// 战闘ヒットマークタスク作成 *******************************************************/
ACTION *MakeBattleCoinHit( ACTION *pOther, int soundNum )
{
	ACTION *pAct;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pOther );
#endif
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR, NULL );
	if( pAct == NULL ) return NULL;
	// 实行关数
	pAct->func = BattleCoinHit;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 点灭フラグＯＮ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
			pAct->atr |= ACT_ATR_FLASH_0 | ACT_ATR_3D_NOFLASH;
		#else
		if ( getUsable3D() );
		else pAct->atr |= ACT_ATR_FLASH_1;
		#endif
	#else
	pAct->atr |= ACT_ATR_FLASH_1;
	#endif
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	pAct->anim_chr_no = CG_GOLD_1;
#ifdef PUK2
	pAct->bltfon = BLTF_NOCHG;
	pAct->bltf = BLTF_NOCHG;
#endif

	// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
	GetBmpSize( pOther->bmpNo );
#else
	LoadBmp( pOther->bmpNo );
#endif

	// 中心座标记忆する
	pAct->x = pOther->x + Rnd( 0, SpriteInfo[ pOther->bmpNo ].width / 2 ) - SpriteInfo[ pOther->bmpNo ].width / 4;
	pAct->y = pOther->y + pOther->anim_y / 4 - Rnd( 0, pOther->anim_y / 3 );
	pAct->fx = (float)pAct->x;
	pAct->fy = (float)pAct->y;

	// 速度设定
	pAct->dfx = (float)( ( rand() % 600 ) - 300 ) / 100;
	pAct->dfy = (float)( ( rand() % 600 ) - 300 ) / 100;

	// 音を鸣らす大体の回数
	pAct->status = soundNum;

	// カウンター初期化
	pAct->actNo = 0;

	// アニメーション向き( ０～７ )( 下が０で右回り )
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif

// アニメーション
	pattern( pAct, ANM_LOOP );

	return pAct;
}

#endif
