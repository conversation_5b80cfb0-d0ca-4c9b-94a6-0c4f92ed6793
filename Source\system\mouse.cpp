﻿/************************/
/*	mouse.c				*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/pc.h"
#include "../systeminc/character.h"
#include "../systeminc/menu.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/mouse.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/process.h"
#include "../systeminc/main.h"
#include "../systeminc/battle.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"


// マウスの当たり判定サイズ
#define MOUSE_HIT_SIZE_X 48
#define MOUSE_HIT_SIZE_Y 48

// マウスのオートリピートの间隔
#define MOUSE_AUTO_REPEATE_TIME 100

#ifdef PUK2
//#define GID_PUK2_MOUSE_CURSOR CG_MOUSE_CURSOR
#define GID_PUK2_MOUSE_CURSOR 245400
#endif

// マウス构造体
MOUSE mouse;
// マウスカーソルに当たったフォント番号（フォント用）
int	HitFontNo;
// マウスカーソルに当たった表示番号（ＢＭＰ用）
int HitDispNo;
// 一行インフォ文字列
char OneLineInfoStr[ 256 ];
// 一行インフォ文字色
#ifdef PUK2
int OneLineInfoColor;

// 一行インフォのサブ
char OneLineInfoStrSub[ 256 ];
int OneLineInfoStrSubColor;

#endif

// 表示ボックスの色
int BoxColor;

#ifdef PUK2
extern	int MouseNoHitFlag;
#endif

#ifdef PUK3_MOUSECURSOR
	// 实态はmain.cpp
	extern struct MOUSE_CURSOR_TYPE MouseCursorType[MOUSE_CURSOR_TYPE_MAX];
	extern HCURSOR MouseCursorType_HANDL[MOUSE_CURSOR_TYPE_MAX];
	extern int nowMouseType;
#endif



#define MOUSE_MACRO_COUNT  50	// これだけ押されたらマクロとする。
#define MOUSE_POINTLIST_MAX	100	// ポイントをキャッシュする个数

typedef struct _MOUSE_POINTLIST{
	int x;	// 座标
	int y;
	int lr;	// 左右
	int count;	// 回数
	struct _MOUSE_POINTLIST *pNext;	// 双方向リンクポインタ
	struct _MOUSE_POINTLIST *pPre;
}MOUSE_POINTLIST;

int giMouseMacroCount = MOUSE_MACRO_COUNT;	// デフォルトマウスカウント
int giMousePointListMax = MOUSE_POINTLIST_MAX;	// デフォルトマウスポイント配列数

// デフォルトのマウス配列
MOUSE_POINTLIST	aDummyMousePointList[MOUSE_POINTLIST_MAX];

// 配列  デフォルト配列を指す
MOUSE_POINTLIST *aMousePointList = aDummyMousePointList;

// リンクの先头と末尾
MOUSE_POINTLIST MousePointRoot, MousePointTail;
// こっちは空リンクの方
MOUSE_POINTLIST EmptyPointRoot, EmptyPointTail;

// 初期化。空リンクリスト作成
void InitMousePointList( void ){
	int i;

	// デフォルト配列が指定されていたら
	if( aMousePointList == aDummyMousePointList ){
		// 何もしない。
	}else
	// 一度别枠で确保されたことがあるなら解放
	if( aMousePointList != NULL ){
#ifdef PUK2_MEMCHECK
		memlistrel( aMousePointList, MEMLISTTYPE_MOUSE_POINTLIST );
#endif
		free( aMousePointList );
	}

	// マウスポイントリストの最大がデフォルトと违うものが指定されていたら
	if( giMousePointListMax != MOUSE_POINTLIST_MAX 
	&&  giMousePointListMax >= 10 	// 念のため１０以下は无视する。
	&&  giMousePointListMax < 5000 	// 念のため５０００以下とする。
	){
		// 配列确保
		aMousePointList = (MOUSE_POINTLIST *)malloc(sizeof( MOUSE_POINTLIST ) * giMousePointListMax );
		if( aMousePointList == NULL ){
			// 确保できなかったらデフォルトへ戾す
			aMousePointList = aDummyMousePointList;
			// 数もデフォルトへ戾す
			giMousePointListMax = MOUSE_POINTLIST_MAX;
		}
#ifdef PUK2_MEMCHECK
		memlistset( aMousePointList, MEMLISTTYPE_MOUSE_POINTLIST );
#endif
	}else{
		// デフォルトに戾す。
		aMousePointList = aDummyMousePointList;
		giMousePointListMax = MOUSE_POINTLIST_MAX;
	}
	// 全初期化
	memset( aMousePointList, 0, sizeof( MOUSE_POINTLIST ) * giMousePointListMax );

	// 一应回数もチェック５回以下はエラーとしてデフォルトにする。
	if( giMouseMacroCount < 5 )giMouseMacroCount = MOUSE_MACRO_COUNT;

	// ルート初期化
	MousePointRoot.pPre = NULL;	
	MousePointRoot.pNext = &MousePointTail;	
	// テール初期化
	MousePointTail.pNext = NULL;	
	MousePointTail.pPre = &MousePointRoot;	

	// ルート初期化
	EmptyPointRoot.pPre = NULL;	
	EmptyPointRoot.pNext = &EmptyPointTail;	
	// テール初期化
	EmptyPointTail.pNext = NULL;	
	EmptyPointTail.pPre = &EmptyPointRoot;	

	EmptyPointRoot.pNext = &aMousePointList[0];
	// 空リンクを初期化
	for( i = 0; i < giMousePointListMax; i ++ ){
		if( i == 0 ){
			aMousePointList[i].pPre = &EmptyPointRoot;
		}else{
			aMousePointList[i].pPre = &aMousePointList[i-1];
		}
		if( i == giMousePointListMax-1 ){
			aMousePointList[i].pNext = &EmptyPointTail;
		}else{
			aMousePointList[i].pNext = &aMousePointList[i+1];
		}
	}

}
#ifdef PUK3_NOTFREE_MOUSE
void RelMousePointList( void )
{
	// giMousePointListMaxをデフォルトに戾して初期化しなおす
	giMouseMacroCount = MOUSE_MACRO_COUNT;
	giMousePointListMax = MOUSE_POINTLIST_MAX;
	InitMousePointList();
}
#endif

// リンクにつなぐ
int LinkMousePoint( MOUSE_POINTLIST* pTop, MOUSE_POINTLIST* pNow ){
	MOUSE_POINTLIST *pNextTmp;

	// NULL なら失败
	if( pTop == NULL )return FALSE;
	if( pNow == NULL )return FALSE;
	// 次の奴のアドレス
	pNextTmp = pTop->pNext;	
	if( pNextTmp == NULL )return FALSE;

	// 次の奴の一个前は自分。
	pNextTmp->pPre = pNow;
	// 一个前の次は自分、
	pTop->pNext = pNow;
	// 自分の前と次
	pNow->pPre = pTop;
	pNow->pNext = pNextTmp;
	return TRUE;
}

// リンクからはずす
int UnlinkMousePoint( MOUSE_POINTLIST* pNow ){
	MOUSE_POINTLIST *pNextTmp, *pPreTmp;

	// NULL なら失败
	if( pNow == NULL )return FALSE;

	// 次の奴のアドレス
	pNextTmp = pNow->pNext;	
	if( pNextTmp == NULL )return FALSE;

	// 前の奴のアドレス
	pPreTmp = pNow->pPre;	
	if( pPreTmp == NULL )return FALSE;

	// 次の前は自分の前
	pNextTmp->pPre = pPreTmp;
	// 前の次は自分の次
	pPreTmp->pNext = pNextTmp;

	// 自分はクリア
	pNow->pPre = NULL;
	pNow->pNext = NULL;
	return TRUE;
}


// 空リンクから一つ取る。
MOUSE_POINTLIST* GetNewList( void ){
	MOUSE_POINTLIST *pRet = EmptyPointRoot.pNext;

	if( pRet == &EmptyPointTail )return NULL;	// 空きが无かった。
	// リストからはずす。
	UnlinkMousePoint( pRet );
	return pRet;
}

// 使ってる所のリストの最后を消して取ってくる
MOUSE_POINTLIST* GetLastList( void ){
	MOUSE_POINTLIST *pRet = MousePointTail.pPre;
	if( pRet == &MousePointRoot )return NULL;	// 空きが无かった。
	UnlinkMousePoint( pRet );	// リンクからはずす
	return pRet;
}


// マウスポイントを一个追加
int	AddMousePoint( int x, int y, int LR ){

	MOUSE_POINTLIST *pNow;
	// 空リンクから取る。
	pNow = GetNewList( );

	if( pNow == NULL ){ // すでに全部使いきった。
		// その场合は一番最下位を使う。
		pNow = GetLastList(  );
		// それでも无かったら失败。そんなはず无いけど。
		if( pNow == NULL )return FALSE;
	}

	pNow->x = x;	// 场所を登録
	pNow->y = y;
	pNow->lr = LR;
	pNow->count = 1;	// カウンターは１から
	// リストの先头に追加
	LinkMousePoint( &MousePointRoot, pNow );
	return TRUE;
}


// 数える。
int CountMousePoint( int x, int y, int LR ){

	MOUSE_POINTLIST* pNow;
	int iFindFlg = FALSE;
	int	iLeftFirstFlg;
	if( LR == 0 ){
		iLeftFirstFlg = TRUE;
	}else{
		iLeftFirstFlg = FALSE;
	}
	

	pNow = MousePointRoot.pNext;
	if( pNow == NULL )return 0;
	for( ;pNow->pNext; pNow= pNow->pNext ){
		// 同じか？
		if( pNow->x == x && pNow->y == y && pNow->lr == LR){
			iFindFlg = TRUE;
			break;
		}
		// 最初にみつけられなかったらフラグおろす
		iLeftFirstFlg = FALSE;
	}

	// 见つけたか？
	if( iFindFlg == FALSE ){
		// 无かったら新しく登録
		AddMousePoint( x, y, LR );
		return 1;
	}
	// 有ったら、でもさっきと同じじゃなかったら。
	if( iLeftFirstFlg == FALSE ){
		pNow->count ++;
	}

	//-----------------------------
	// 一番先头にまであげる。
	//-----------------------------
	// リストからはずす
	UnlinkMousePoint( pNow );
	// リストの先头に追加
	LinkMousePoint( &MousePointRoot, pNow );

	return pNow->count;
}





// マウス情报の初期化 ////////////////////////////////////////////////////////
void MouseInit( void )
{
	memset( &mouse, 0, sizeof( MOUSE ) );
	mouse.itemNo = -1; // アイテム番号初期化
}

// 现在のマウスの位置を记忆する //////////////////////////////////////////////
void MouseNowPoint( int x, int y )
{
	// 座标をセット
	mouse.nowPoint.x = x;
	mouse.nowPoint.y = y;
}

// マウスの左ボタンが押された时のマウスの位置を记忆する //////////////////////
void MouseCrickLeftDownPoint( int x, int y )
{
	// 状态をセット
	mouse.state |= MOUSE_LEFT_CRICK;
	mouse.onceState |= MOUSE_LEFT_CRICK;
	mouse.autoState |= MOUSE_LEFT_CRICK;
	// 座标をセット
	mouse.crickLeftDownPoint.x = x;
	mouse.crickLeftDownPoint.y = y;
	// ボタンが押されてからの経过时间を设定する前准备
	mouse.beforeLeftPushTime = GetTickCount();
	mouse.leftPushTime = 0;

	// マクロ测定する。
	if( CountMousePoint( x, y, 0 ) >= giMouseMacroCount ){
#ifdef _DEBUG
		MessageBox( hWnd, "外挂？", "确认", MB_OK | MB_ICONSTOP );               //MLHIDE
#endif
		// マクロだー。
		nrproto_CH_send( sockfd, 1 );
		// 全初期化
		InitMousePointList( );
	}
}

// マウスの左ボタン离された时のマウスの位置を记忆する ////////////////////////
void MouseCrickLeftUpPoint( int x, int y )
{
	// 状态をセット
	mouse.state &= (~MOUSE_LEFT_CRICK);
	mouse.onceState |= MOUSE_LEFT_CRICK_UP;
	mouse.autoState &= (~MOUSE_LEFT_CRICK);
	// 座标をセット
	mouse.crickLeftUpPoint.x = x;
	mouse.crickLeftUpPoint.y = y;
	// ボタンが离されたら时间をリセット
	mouse.beforeLeftPushTime = 0;
	mouse.leftPushTime = 0;
}

// 左ダブルクリックされた时のマウスの位置を记忆する //////////////////////////
void MouseDblCrickLeftUpPoint( int x, int y )
{
	// 状态をセット
	//mouse.onceState |= ( MOUSE_LEFT_DBL_CRICK | MOUSE_LEFT_CRICK );
	mouse.state |= MOUSE_LEFT_CRICK;
	mouse.onceState |= MOUSE_LEFT_DBL_CRICK;
	mouse.onceState |= MOUSE_LEFT_CRICK;
	mouse.autoState |= MOUSE_LEFT_CRICK;
	// 座标をセット
	mouse.crickLeftDownPoint.x = x;
	mouse.crickLeftDownPoint.y = y;
	// ボタンが押されてからの経过时间を设定する前准备
	mouse.beforeLeftPushTime = GetTickCount();
	mouse.leftPushTime = 0;
}

// マウスの右ボタン押された时のマウスの位置を记忆する ////////////////////////
void MouseCrickRightDownPoint( int x, int y )
{
	// 状态をセット
	mouse.state |= MOUSE_RIGHT_CRICK;
	mouse.onceState |= MOUSE_RIGHT_CRICK;
	mouse.autoState |= MOUSE_RIGHT_CRICK;
	// 座标をセット
	mouse.crickRightDownPoint.x = x;
	mouse.crickRightDownPoint.y = y;
	// ボタンが押されてからの経过时间を设定する前准备
	mouse.beforeRightPushTime = GetTickCount();
	mouse.rightPushTime = 0;

	// マクロ测定する。
	if( CountMousePoint( x, y, 1 ) >= giMouseMacroCount ){
#ifdef _DEBUG
		MessageBox( hWnd, "外挂？", "确认", MB_OK | MB_ICONSTOP );               //MLHIDE
#endif
		// マクロだー。
		nrproto_CH_send( sockfd, 1 );
		// 全初期化
		InitMousePointList( );
	}

}

// マウスの右ボタン离された时のマウスの位置を记忆する ////////////////////////
void MouseCrickRightUpPoint( int x, int y )
{
	// 状态をセット
	mouse.state &= (~MOUSE_RIGHT_CRICK);
	mouse.onceState |= MOUSE_RIGHT_CRICK_UP;
	mouse.autoState &= (~MOUSE_RIGHT_CRICK);
	// 座标をセット
	mouse.crickRightUpPoint.x = x;
	mouse.crickRightUpPoint.y = y;
	// ボタンが离されたら时间をリセット
	mouse.beforeRightPushTime = 0;
	mouse.rightPushTime = 0;
}

// 右ダブルクリックされた时のマウスの位置を记忆する //////////////////////////
void MouseDblCrickRightUpPoint( int x, int y )
{
	// 状态をセット
	mouse.state |= MOUSE_RIGHT_CRICK;
	mouse.onceState |= MOUSE_RIGHT_DBL_CRICK;
	mouse.onceState |= MOUSE_RIGHT_CRICK;
	mouse.autoState |= MOUSE_RIGHT_CRICK;
	// 座标をセット
	mouse.crickRightDownPoint.x = x;
	mouse.crickRightDownPoint.y = y;
	// ボタンが押されてからの経过时间を设定する前准备
	mouse.beforeRightPushTime = GetTickCount();
	mouse.rightPushTime = 0;
}

#ifdef PUK2

// マウスホイールの回転量を记忆
void MouseWheel( int wheel )
{
	mouse.wheel += wheel;
}

// マウスホイールの回転量记忆を初期化
void MouseWheelInit()
{
	mouse.wheel = 0;
}

#endif
#ifdef PUK3_MOUSECURSOR

// マウスのグラフィックを变更する（变えておきたい间每フレーム呼び出す必要有り）
void setMouseType( int type, BOOL always )
{
	if ( always || mouse.type == MOUSE_CURSOR_TYPE_NOMAL ) mouse.type = type;
}

#endif

// マウス处理　///////////////////////////////////////////////////////////////
void MouseProc( void )
{
	static UINT leftPushTimeBak;
	static UINT rightPushTimeBak;
	
	// 状态を初期化
	mouse.onceState = MOUSE_NO_CRICK;
	// 左ボタンを押してからの経过时间をセット
	if( mouse.beforeLeftPushTime > 0 )
	{
		mouse.leftPushTime = GetTickCount() - mouse.beforeLeftPushTime;
	}
	// 右ボタンを押してからの経过时间をセット
	if( mouse.beforeRightPushTime > 0 )
	{
		mouse.rightPushTime = GetTickCount() - mouse.beforeRightPushTime;
	}
	// オートリピート处理
	// 右键押されている时
	// ０．５秒でリピート开始
	if( mouse.leftPushTime > 500 ){
		// 最初の一回の时
		if( leftPushTimeBak == 0 ){
			mouse.autoState |= MOUSE_LEFT_CRICK; // ビットＯＮ
			leftPushTimeBak = mouse.leftPushTime;	// 时间を记忆
		}else
		// 时间がきたら缲り返し
		if( mouse.leftPushTime - leftPushTimeBak >= MOUSE_AUTO_REPEATE_TIME ){
			mouse.autoState |= MOUSE_LEFT_CRICK; // ビットＯＮ
			leftPushTimeBak = mouse.leftPushTime;	// 时间を记忆
		}else{	// すぐにＯＦＦにする
			mouse.autoState &= (~MOUSE_LEFT_CRICK);	// ビットＯＦＦ
		}
	}else{ 
		mouse.autoState &= (~MOUSE_LEFT_CRICK);	// ビットＯＦＦ
		leftPushTimeBak = 0;
	}
	
	// 右键押されている时
	// ０．５秒でリピート开始
	if( mouse.rightPushTime > 500 ){
		// 最初の一回の时
		if( rightPushTimeBak == 0 ){
			mouse.autoState |= MOUSE_RIGHT_CRICK; // ビットＯＮ
			rightPushTimeBak = mouse.rightPushTime;	// 时间を记忆
		}else
		// 时间がきたら缲り返し
		if( mouse.rightPushTime - rightPushTimeBak >= MOUSE_AUTO_REPEATE_TIME ){
			mouse.autoState |= MOUSE_RIGHT_CRICK; // ビットＯＮ
			rightPushTimeBak = mouse.rightPushTime;	// 时间を记忆
		}else{	// すぐにＯＦＦにする
			mouse.autoState &= (~MOUSE_RIGHT_CRICK);	// ビットＯＦＦ
		}
	}else{ 
		mouse.autoState &= (~MOUSE_RIGHT_CRICK);	// ビットＯＦＦ
		rightPushTimeBak = 0;
	}
	
	
	
	// 右クリック押されている时
	//if( mouse.rightPushTime > 0 ){
	//}else mouse.state &= (~MOUSE_RIGHT_AUTO_CRICK);
	// ウィンドウモードでない时
	//if( !WindowMode ){
	//if( 0 <= mouse.nowPoint.x && mouse.nowPoint.x < 640 &&
	//	0 <= mouse.nowPoint.y && mouse.nowPoint.y < 480 ){
	
#ifdef PUK3_MOUSECURSOR
	// 快适マウスでない时
	if( MouseCursorFlag == FALSE ){
		// マウスを表示するときなら
		if( mouse.flag == FALSE ){
			BLT_MEMBER bm={0};

			bm.rgba.rgba=0xFFFFFFFF;
			bm.bltf=BLTF_NOCHG;
			StockDispBuffer(
				 mouse.nowPoint.x + 16 + MouseCursorType[mouse.type].dfx,
				 mouse.nowPoint.y + 16 + MouseCursorType[mouse.type].dfy,
				 DISP_PRIO_MOUSE, MouseCursorType[mouse.type].GraNo, 0, &bm);
		}
	}else{
		static int MouseTypeB;

		if ( mouse.type != MouseTypeB ){
			POINT pt;
			RECT crrc;
	
			// クライアント领域でのマウス座标の取得
			GetCursorPos( &pt );
			ScreenToClient( hWnd, &pt );
	
			// クライアント领域の大きさ取得
			GetClientRect( hWnd, &crrc );
	
			if ( 0 <= pt.x && pt.x < crrc.right ){
				if ( 0 <= pt.y && pt.y < crrc.bottom ){
					SetCursor(MouseCursorType_HANDL[mouse.type]);
				}
			}
		}

		MouseTypeB = mouse.type;
#ifdef VERSION_TW
		//修复老版鼠标样式显示的颜色
		if (mouse.flag == FALSE) {
			StockDispBuffer(
				mouse.nowPoint.x + 16,
				mouse.nowPoint.y + 16,
				DISP_PRIO_MOUSE, CG_MOUSE_CURSOR, 0, 0);
		}
#endif
	}
	nowMouseType = mouse.type;
	// マウス种类を初期化
	mouse.type = MOUSE_CURSOR_TYPE_NOMAL;
#else
	// 快适マウスでない时
	if( MouseCursorFlag == FALSE )
	{
		if( mouse.flag == FALSE )
		{
			// マウスカーソルの表示（最优先表示）
			//StockDispBuffer( mouse.nowPoint.x + 16, mouse.nowPoint.y + 16,
			//	DISP_PRIO_MOUSE, CG_MOUSE_CURSOR, 0 );

#if 1
#ifdef PUK2
			BLT_MEMBER bm={0};

			bm.rgba.rgba=0xFFFFFFFF;
			bm.bltf=BLTF_NOCHG;
			StockDispBuffer( mouse.nowPoint.x + 16, mouse.nowPoint.y + 16, DISP_PRIO_MOUSE, GID_PUK2_MOUSE_CURSOR, 0, &bm);
#else
			// マウスカーソルの表示（最优先表示）
			StockDispBuffer( mouse.nowPoint.x + 16, mouse.nowPoint.y + 16,
				DISP_PRIO_MOUSE, CG_MOUSE_CURSOR, 0 );
#endif
#else			
			POINT point = getMousePosition();
			StockDispBuffer( point.x + 16, point.y + 16,
				DISP_PRIO_MOUSE, CG_MOUSE_CURSOR, 0 );
#endif
		}
	}
#endif
		
#ifdef PUK2
	// マウスホイールの回転量记忆を初期化
	MouseWheelInit();
#endif
}
// グループ选择チェック *********************************************************/
void CheckGroupSelect( int no )
{
	int i;
	DISP_INFO 	*pDispInfo;
	DISP_SORT 	*pDispSort;
	/* ＢＭＰ表示バッファから检索（表示の优先顺位が高い方から） */
	//for( i = DispBuffer.DispCnt - 1; i >= 0 ; i--, pDispSort-- ){
	for( i = 0 ; i < DispBuffer.DispCnt ; i++ ){
	
		//pDispInfo = DispBuffer.DispInfo[ i ];
		//pDispSort = DispBuffer.DispSort[ i ];
		pDispInfo = DispBuffer.DispInfo + i;
		pDispSort = DispBuffer.DispSort + i;
		
		// フラグが立っていたら
		if( pDispInfo->hitFlag == no ){
			// メニューより上だったら
			if( pDispSort->dispPrio >= DISP_PRIO_MENU ){
				// ボックス表示データをバッファに溜める
				StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
									pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width + 2, 
									pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height + 2, 
									DISP_PRIO_BOX2, BoxColor, 0 );
			}else{
				// ボックス表示データをバッファに溜める
				StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
									pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width + 2, 
									pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height + 2, 
									DISP_PRIO_BOX, BoxColor, 0 );
									//pDispSort->dispPrio, 250, 0 );
			}
		}
	}
}

#if 1
// 表示ボックスのカラーテーブル
UCHAR BoxColorTbl[] = { 	
						//255, 255, 255, 255, 255,
						//8,8,8,8,8,
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						250,250,250,250,250, 
						15,15,15,15,15,
						2,2,2,2,2, 
						15,15,15,15,15, 
						//250,250,250,250,250, 
						//8,8,8,8,8,
					};
#else
UCHAR BoxColorTbl[] = { 	
						255,255,255,255,255,255,255,255,255,255,
						255,255,255,255,255,255,255,255,255,255,
						7,7,7,7,7,7,7,7,7,7,
						248,248,248,248,248,248,248,248,248,248,
						0,0,0,0,0,0,0,0,0,0,
						248,248,248,248,248,248,248,248,248,248,
						7,7,7,7,7,7,7,7,7,7,
					};
#endif

/* マウスカーソルのあたり判定 **************************************************/
void HitMouseCursor( void )
{	
	int i;					// ループカウンタ
	int strWidth;			// 文字列のドット数
	int hitFlag = FALSE;	// 当たり判定フラグ
	static int cnt = 0;		// カウンター
	int itemNameColor = FONT_PAL_WHITE;	// アイテムの色
	
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt - 1;
	DISP_INFO 	*pDispInfo;
	//char moji[ 256 ];

	int hitYsize;
	//int hitXsize;
	
	// 表示ボックスの色变更
	if( cnt >= sizeof( BoxColorTbl ) - 1 ) cnt = 0;
	else cnt++;
	
	// 表示ボックスの色设定
	BoxColor = BoxColorTbl[ cnt ];
	
#ifdef PUK2
	HitDispNo = -1;
	if( MouseNoHitFlag ){
		MouseNoHitFlag = 0;
		return;
	}
#endif
	/* フォント表示バッファから检索（ソートされないのでどこから见ても同じ）*/
	for( i = 0 ; i < FontCnt ; i++ ){
	
		// 当たり判定しない时返回
		if( FontBuffer[ i ].hitFlag == 0 ) continue;
		
		// 文字列の横幅（ドット数）を调べる
		strWidth = GetStrWidth( FontBuffer[ i ].str, FontBuffer[ i ].fontKind );
		// 四角のあたり判定
		if( mouse.nowPoint.x <= FontBuffer[ i ].x + strWidth + 2 &&
			FontBuffer[ i ].x - 2 <= mouse.nowPoint.x &&
			mouse.nowPoint.y <= FontBuffer[ i ].y + FontKind[ FontBuffer[ i ].fontKind ].zenkakuHeight + 2 &&
			FontBuffer[ i ].y - 2 <= mouse.nowPoint.y ){
			
			// 当たった配列の番号を学习
			HitFontNo = i;
			
			// ボックスを表示する时
			if( FontBuffer[ i ].hitFlag == 2 ){
				// ボックス表示データをバッファに溜める
				StockBoxDispBuffer( 
					FontBuffer[i].x - 3, FontBuffer[i].y - 4,
					FontBuffer[i].x + strWidth + 2,
					FontBuffer[i].y + FontKind[ FontBuffer[i].fontKind].zenkakuHeight + 4,
					DISP_PRIO_BOX2, BoxColor, 0 );
				SortDispBuffer(); 	// 表示バッファソート
			}
			else
			// ボックス（涂りつぶし）を表示
			if( FontBuffer[ i ].hitFlag == 3 )
			{
				// ボックス表示データをバッファに溜める
				StockBoxDispBuffer( 
					FontBuffer[i].x - 3,
					FontBuffer[i].y - 4,
					FontBuffer[i].x + strWidth + 2,
					FontBuffer[i].y + FontKind[FontBuffer[i].fontKind].zenkakuHeight + 4,
					DISP_PRIO_BOXFILL, FontBuffer[i].hitBoxColor, 1 );
				SortDispBuffer(); 	// 表示バッファソート
			}					
			// 当たった配列の番号を初期化する
			HitDispNo = -1;
			// マウス等级
			mouse.level = DISP_PRIO_MENU;
			// タスクバーがない时
			if( taskBarStatus == 0 && taskBarDrawFlag )
			{
#ifdef PUK2_NEW_MENU
		OneLineInfoColor=itemNameColor;
#else
				// 一行インフォ表示
				StockFontBuffer( 8, 460, FONT_PRIO_FRONT, itemNameColor, OneLineInfoStr, 0 );
#endif
			}
#ifndef PUK2_NEW_MENU
			// 文字列初期化
			OneLineInfoStr[ 0 ] = NULL;
#endif
			return;		// フォントに当たった时ＢＭＰの判定はしない
		}
	}
	// 当たった配列の番号を初期化する
	HitFontNo = -1;
	
	/* ＢＭＰ表示バッファから检索（表示の优先顺位が高い方から） */
	for( i = DispBuffer.DispCnt - 1; i >= 0 ; i--, pDispSort-- ){
		// 表示データの入っているアドレスにセット
		pDispInfo = DispBuffer.DispInfo + pDispSort->no;
		
		// 当たり判定しない时返回
		if( pDispInfo->hitFlag == 0 ) continue;
		
		// 相手のＢＭＰがなかったら継続
		//if( SpriteInfo[ pDispInfo->bmpNo ].lpSurfaceInfo == NULL ) continue;
		// ＶＲＡＭにいないときはハードディスクからロードする
		// ＢＭＰをロードする
#ifdef PUK2
		if( GetBmpSize( pDispInfo->bmpNo ) == FALSE ) continue;
#else
		if( LoadBmp( pDispInfo->bmpNo ) == FALSE ) continue;
#endif
		
		// アクションでない时
		if( pDispInfo->pAct == NULL ){
			// 四角のあたり判定
			if( mouse.nowPoint.x <= pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width &&
				pDispInfo->x <= mouse.nowPoint.x &&
				mouse.nowPoint.y <= pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height &&
				pDispInfo->y <= mouse.nowPoint.y )
				
				hitFlag = TRUE;
		}else{
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pDispInfo->pAct );
#endif
			// Ｙサイズ计算
			// 战闘の时
#ifdef PUK2
			if( ProcNo == PROC_BATTLE || ProcNo == PROC_CHAR_SELECT ){
#else
			if( ProcNo == PROC_BATTLE ){
#endif
				//hitXsize = MOUSE_HIT_SIZE_X;
				hitYsize = MOUSE_HIT_SIZE_Y * 2;
				if( hitYsize > SpriteInfo[ pDispInfo->bmpNo ].height ) hitYsize = SpriteInfo[ pDispInfo->bmpNo ].height;
				// 四角のあたり判定（アクション用）
				//if( mouse.nowPoint.x <= pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width * 0.5 + MOUSE_HIT_SIZE_X * 0.5 &&
				//	pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width * 0.5 - MOUSE_HIT_SIZE_X * 0.5  <= mouse.nowPoint.x &&
				//	mouse.nowPoint.y <= pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height &&
					//pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height - MOUSE_HIT_SIZE_Y <= mouse.nowPoint.y )
				//	pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height - hitYsize <= mouse.nowPoint.y )
				
				if( mouse.nowPoint.x <= pDispInfo->x - pDispInfo->pAct->anim_x + MOUSE_HIT_SIZE_X * 0.5 &&
					pDispInfo->x - pDispInfo->pAct->anim_x - MOUSE_HIT_SIZE_X * 0.5  <= mouse.nowPoint.x &&
					mouse.nowPoint.y <= pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height &&
					//pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height - MOUSE_HIT_SIZE_Y <= mouse.nowPoint.y )
					pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height - hitYsize <= mouse.nowPoint.y )
					
					hitFlag = TRUE;
			}else{
				//hitXsize = MOUSE_HIT_SIZE_X;
				//hitYsize = MOUSE_HIT_SIZE_Y * 2;
				//if( hitYsize > SpriteInfo[ pDispInfo->bmpNo ].height ) hitYsize = SpriteInfo[ pDispInfo->bmpNo ].height;
				
				if( mouse.nowPoint.x <= pDispInfo->x - pDispInfo->pAct->anim_x + 16 &&
					pDispInfo->x - pDispInfo->pAct->anim_x - 16  <= mouse.nowPoint.x &&
					mouse.nowPoint.y <= pDispInfo->y - pDispInfo->pAct->anim_y + 12 &&
					//pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height - MOUSE_HIT_SIZE_Y <= mouse.nowPoint.y )
					pDispInfo->y - pDispInfo->pAct->anim_y - 12 <= mouse.nowPoint.y )
					
					hitFlag = TRUE;
				
			}	
		}
				
		// 当たっていたら
		if( hitFlag == TRUE ){
#ifndef PUK2_NEW_MENU
			// 文字表示バッファに溜める
//			StockFontBuffer( pDispInfo->x, pDispInfo->y, FONT_PRIO_FRONT, 0, "Hit", 1 );
#endif			
			// 当たった配列の番号を记忆
			HitDispNo = pDispSort->no;
			
			// ボックスを表示する时
			if( pDispInfo->hitFlag == 2 ){
				// ＹＥＳ、ＮＯウィンドウより上だったら
				if( pDispSort->dispPrio >= DISP_PRIO_YES_NO_WND ){
					// ボックス表示データをバッファに溜める
					StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
										pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width + 2, 
										pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height + 2, 
										//DISP_PRIO_BOX2, 250, 0 );
										DISP_PRIO_BOX3, BoxColor, 0 );
				}else
				// メニューより上だったら
				if( pDispSort->dispPrio >= DISP_PRIO_MENU ){
					// ボックス表示データをバッファに溜める
					StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
											pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width + 2, 
											pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height + 2, 
											//DISP_PRIO_BOX2, 250, 0 );
											DISP_PRIO_BOX2, BoxColor, 0 );
				}else{
					// アクションポインタある时
					if( pDispInfo->pAct != NULL )
					{
#ifdef PUK3_ACTION_CHECKRANGE
						CheckAction( pDispInfo->pAct );
#endif
						// スケール变更している时
						if( pDispInfo->pAct->scaleX != 1.0 || pDispInfo->pAct->scaleY != 1.0 )
						{ 
							// ボックス表示データをバッファに溜める
							StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
												pDispInfo->x + ( int )( SpriteInfo[ pDispInfo->bmpNo ].width * pDispInfo->pAct->scaleX )+ 2, 
												pDispInfo->y + ( int )( SpriteInfo[ pDispInfo->bmpNo ].height * pDispInfo->pAct->scaleY )+ 2, 
												//DISP_PRIO_BOX2, 250, 0 );
												DISP_PRIO_BOX, BoxColor, 0 );
						}else{
							// ボックス表示データをバッファに溜める
							StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
												pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width + 2, 
												pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height + 2, 
												//DISP_PRIO_BOX2, 250, 0 );
												DISP_PRIO_BOX, BoxColor, 0 );
						}
					}else{
						// ボックス表示データをバッファに溜める
						StockBoxDispBuffer( pDispInfo->x - 2, pDispInfo->y - 2, 
											pDispInfo->x + SpriteInfo[ pDispInfo->bmpNo ].width + 2, 
											pDispInfo->y + SpriteInfo[ pDispInfo->bmpNo ].height + 2, 
											//DISP_PRIO_BOX, 250, 0 );
											DISP_PRIO_BOX, BoxColor, 0 );
											//pDispSort->dispPrio, 250, 0 );
					}
				}
				
			}
			// 战闘时复数选择处理
			if( pDispInfo->hitFlag >= 3 ) CheckGroupSelect( pDispInfo->hitFlag );
				
			SortDispBuffer(); 	// 表示バッファソート
			
			// タスクバーがない时
			if( taskBarStatus == 0 && taskBarDrawFlag )
			{
				// アクションポインタがある时
				if( pDispInfo->pAct != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
					CheckAction( pDispInfo->pAct );
#endif
					// 一行インフォ表示ＯＮの时
					if( pDispInfo->pAct->atr & ACT_ATR_INFO ){
						itemNameColor = pDispInfo->pAct->itemNameColor;
#ifdef PUK2_NEW_MENU
						OneLineInfoColor=itemNameColor;
#endif
						// フィールド上の时
						if( ProcNo == PROC_GAME
						 && pDispInfo->pAct->anim_chr_no > 0 )
						{
							// プレイヤーの时
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_PC )
							{
								// 名称の色设定
								itemNameColor = pDispInfo->pAct->itemNameColor;
#ifdef PUK2_NEW_MENU
								OneLineInfoColor=itemNameColor;
#endif
								sprintf( OneLineInfoStr, "LV %-3d %-16s  LP %4d/%-4d  FP %4d/%-4d", //MLHIDE
									pDispInfo->pAct->level, pDispInfo->pAct->name,
									pc.lp, pc.maxLp, pc.fp, pc.maxFp );
							}
							else
#ifdef PUK2
							// 连れ歩きペットの时
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_TAKENPET )
							{
								// 名称の色设定
								itemNameColor = pDispInfo->pAct->itemNameColor;
#ifdef PUK2_NEW_MENU
								OneLineInfoColor=itemNameColor;
#endif
								sprintf(OneLineInfoStr,
									"LV %-3d %-16s",                                             //MLHIDE
									//参数个数不对,不知道正确显示情况,暂时按格式字符串的显示注释第三个参数
									//pDispInfo->pAct->level, pDispInfo->pAct->name,
									//pDispInfo->pAct->freeName );
									pDispInfo->pAct->level, pDispInfo->pAct->name);
#ifdef PUK2_NEW_MENU

#else
								// 一行インフォ表示
								StockFontBuffer( 8, 460, FONT_PRIO_FRONT,
									itemNameColor, OneLineInfoStr, 0 );
								// 文字列初期化
								OneLineInfoStr[0] = NULL;
#endif
							}
							else
#endif
							// 他のプレイヤーの时
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_OTHER_PC )
							{
								// 名称の色设定
								itemNameColor = pDispInfo->pAct->itemNameColor;
#ifdef PUK2_NEW_MENU
								OneLineInfoColor=itemNameColor;
#endif
								sprintf( OneLineInfoStr,
									"LV %-3d %-16s [%-16s]",                                     //MLHIDE
									pDispInfo->pAct->level,
									pDispInfo->pAct->name,
									pDispInfo->pAct->freeName );

#ifdef PUK2_NEW_MENU

#else								
								// 一行インフォ表示
								StockFontBuffer( 8, 460, FONT_PRIO_FRONT,
									itemNameColor, OneLineInfoStr, 0 );
								// 文字列初期化
								OneLineInfoStr[0] = NULL;
#endif

#ifdef PUK2_NEW_MENU
								if( strlen( pDispInfo->pAct->title ) > 0 )
								{
									sprintf( OneLineInfoStrSub,
										"%-16s", pDispInfo->pAct->title );                          //MLHIDE
									OneLineInfoStrSubColor=FONT_PAL_YELLOW;
								}
#else
								if( strlen( pDispInfo->pAct->title ) > 0 )
								{
									sprintf( OneLineInfoStr,
										"%-16s", pDispInfo->pAct->title );                          //MLHIDE

									// 一行インフォ表示
									StockFontBuffer( 395, 460, FONT_PRIO_FRONT,
										FONT_PAL_YELLOW, OneLineInfoStr, 0 );
									// 文字列初期化
									OneLineInfoStr[0] = NULL;
								}
#endif
							}
							else
							// ペットの时
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_PET )
							{
								// 称号ある时
								if( pDispInfo->pAct->freeName[ 0 ] != NULL )
								{
									sprintf( OneLineInfoStr,"LV %-3d %-16s",                     //MLHIDE
										pDispInfo->pAct->level, pDispInfo->pAct->freeName );
								}
								else
								{
									sprintf( OneLineInfoStr,"LV %-3d %-16s",                     //MLHIDE
										pDispInfo->pAct->level, pDispInfo->pAct->name );
								}
#ifdef PUK3_MOUSECURSOR
								// マウスカーソルの变更
								setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
							}
							else
							// アイテムの时
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_ITEM )
							{
								CHAREXTRA *ext;

								// 名称の色设定
								itemNameColor = pDispInfo->pAct->itemNameColor;
#ifdef PUK2_NEW_MENU
								OneLineInfoColor=itemNameColor;
#endif
								ext = (CHAREXTRA *)pDispInfo->pAct->pYobi;
								if( ext->num > 0 )
								{
									sprintf( OneLineInfoStr,"%s(%d个)", pDispInfo->pAct->name, ext->num ); //MLHIDE
								}
								else
								{	
//									sprintf( OneLineInfoStr,"%s(%s)", pDispInfo->pAct->name,pDispInfo->pAct->freeName );
									sprintf( OneLineInfoStr,"%s", pDispInfo->pAct->name );       //MLHIDE

								}
#ifdef PUK3_MOUSECURSOR
								// マウスカーソルの变更
								setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
							}
							else
							// お金の时
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_GOLD )
							{
								sprintf( OneLineInfoStr,"%s", pDispInfo->pAct->name );        //MLHIDE
#ifdef PUK3_MOUSECURSOR
								// マウスカーソルの变更
								setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
							}else
							// その他の时（ドア、町の人、看板）
							if( pDispInfo->pAct->atr & ACT_ATR_TYPE_OTHER )
							{
								sprintf( OneLineInfoStr,"%s", pDispInfo->pAct->name );        //MLHIDE
							}
						}
						else
						// 战闘の时
#ifdef _DEBUG
						if( ProcNo == PROC_BATTLE || ProcNo == PROC_OHTA_TEST )
#else						
						if( ProcNo == PROC_BATTLE )
#endif					
						{
							// ペットの时
							//if( pDispInfo->pAct->atr & ACT_ATR_TYPE_PET ){
							//}else
							// アイテムの时
							//if( pDispInfo->pAct->atr & ACT_ATR_TYPE_ITEM ){
								// 名称の色设定
								//itemNameColor = pDispInfo->pAct->itemNameColor;
							//	sprintf( OneLineInfoStr,"%s", pDispInfo->pAct->name );
							//}else
							// 観战モードじゃない时
							if( BattleMyNo < BC_MAX ){
								// 自分が右侧のとき
								if( BattleMyNo < BC_MAX / 2 ){
									// 味方のとき
									if( pDispInfo->pAct->pYobi != NULL 
									&& ( ( BC_YOBI* )pDispInfo->pAct->pYobi )->myId < BC_MAX / 2 
									){
										if((( BC_YOBI* )pDispInfo->pAct->pYobi )->bcFlag 
												& BC_FLAG_MORPH)
										{
											itemNameColor = FONT_PAL_YELLOW;																}
#ifdef PUK2_NEW_MENU
											OneLineInfoColor=itemNameColor;
#endif
//										itemNameColor = pDispInfo->pAct->itemNameColor;
										sprintf( OneLineInfoStr, "LV %-3d %-16s  LP %4d/%-4d  FP %4d/%-4d", //MLHIDE
											pDispInfo->pAct->level, pDispInfo->pAct->name,
											pDispInfo->pAct->hp, pDispInfo->pAct->maxHp, pDispInfo->pAct->fp, pDispInfo->pAct->maxFp );
									}
									// 敌のとき
									else{
										sprintf( OneLineInfoStr, "LV %-3d %-16s",                   //MLHIDE
										
											pDispInfo->pAct->level, pDispInfo->pAct->name );
									}
								}
								// 自分が左侧のとき
								else{
									// 味方のとき
									if( pDispInfo->pAct->pYobi != NULL 
									&&( ( BC_YOBI* )pDispInfo->pAct->pYobi )->myId < BC_MAX / 2 ){											if((( BC_YOBI* )pDispInfo->pAct->pYobi )->bcFlag 
										& BC_FLAG_MORPH)
										{
											itemNameColor = FONT_PAL_YELLOW;																}
#ifdef PUK2_NEW_MENU
											OneLineInfoColor=itemNameColor;
#endif
										sprintf( OneLineInfoStr, "LV %-3d %-16s",                   //MLHIDE
											pDispInfo->pAct->level, pDispInfo->pAct->name );
									}
									// 敌のとき
									else{
										sprintf( OneLineInfoStr, "LV %-3d %-16s  LP %4d/%-4d  FP %4d/%-4d", //MLHIDE
											pDispInfo->pAct->level, pDispInfo->pAct->name,
											pDispInfo->pAct->hp, pDispInfo->pAct->maxHp, pDispInfo->pAct->fp, pDispInfo->pAct->maxFp );
									}
								}
							}else{	// 観战モードの时
#ifdef _DEBUG
								// 离线模式の时
								if( offlineFlag == TRUE ){
									sprintf( OneLineInfoStr, "LV %-3d %-16s  LP %4d/%-4d  FP %4d/%-4d", //MLHIDE
										pDispInfo->pAct->level, pDispInfo->pAct->name,
										pDispInfo->pAct->hp, pDispInfo->pAct->maxHp, pDispInfo->pAct->fp, pDispInfo->pAct->maxFp );
								}else{
										if( pDispInfo->pAct->pYobi != NULL 
										&&( ( BC_YOBI* )pDispInfo->pAct->pYobi )->myId < BC_MAX / 2 ){											if((( BC_YOBI* )pDispInfo->pAct->pYobi )->bcFlag 
											& BC_FLAG_MORPH)
											{
												itemNameColor = FONT_PAL_YELLOW;																}
#ifdef PUK2_NEW_MENU
												OneLineInfoColor=itemNameColor;
#endif
									}
									//sprintf( OneLineInfoStr, "LV %-3d %-16s",
									//		pDispInfo->pAct->level, pDispInfo->pAct->name );
									sprintf( OneLineInfoStr, "Lv %-3d %-16s  LP %4d/%-4d  FP %4d/%-4d", //MLHIDE
										pDispInfo->pAct->level, pDispInfo->pAct->name,
										pDispInfo->pAct->hp, pDispInfo->pAct->maxHp, pDispInfo->pAct->fp, pDispInfo->pAct->maxFp );
								}
#else							
								if( pDispInfo->pAct->pYobi != NULL 
								&&( ( BC_YOBI* )pDispInfo->pAct->pYobi )->myId < BC_MAX / 2 ){											if((( BC_YOBI* )pDispInfo->pAct->pYobi )->bcFlag 
									& BC_FLAG_MORPH)
									{
										itemNameColor = FONT_PAL_YELLOW;																}
#ifdef PUK2_NEW_MENU
										OneLineInfoColor=itemNameColor;
#endif
								}
								sprintf( OneLineInfoStr, "LV %-3d %-16s",                     //MLHIDE
									pDispInfo->pAct->level, pDispInfo->pAct->name );
#endif							
							}
						}
					}
				}

#ifdef PUK2_NEW_MENU
				//ウインドウ关连(sano)
				// 一行インフォ表示
				if(strstr(OneLineInfoStr,ITEM_HANKO_STRING) != NULL){
					OneLineInfoColor=FONT_PAL_AQUA;
				}
				else if(strstr(OneLineInfoStr,ITEM_INCUSE_STRING) != NULL){
					OneLineInfoColor=FONT_PAL_GREEN;
					//刻印
				}	
#else
				//ウインドウ关连(sano)
				// 一行インフォ表示
				if(strstr(OneLineInfoStr,ITEM_HANKO_STRING) != NULL){
					if(OneLineInfoStr[0]) {
						int len = strlen(OneLineInfoStr);
						OneLineInfoStr[len -5] = 0;
					}
					//ハンコ
					StockFontBuffer( 8, DEF_APPSIZEY-20, FONT_PRIO_FRONT, FONT_PAL_AQUA, OneLineInfoStr, 0 );
				}
				
				else if(strstr(OneLineInfoStr,ITEM_INCUSE_STRING) != NULL){
					if(OneLineInfoStr[0]) {
						int len = strlen(OneLineInfoStr);
						OneLineInfoStr[len -5] = 0;
					}
					//刻印
					StockFontBuffer( 8, DEF_APPSIZEY-20, FONT_PRIO_FRONT, FONT_PAL_GREEN, OneLineInfoStr, 0 );				
				}else{
					//通常
					StockFontBuffer( 8, DEF_APPSIZEY-20, FONT_PRIO_FRONT, itemNameColor, OneLineInfoStr, 0 );
				}	
#endif
			}
#ifndef PUK2_NEW_MENU
			// 文字列初期化
			OneLineInfoStr[ 0 ] = NULL;
#endif			
			// マウス等级
			mouse.level = pDispSort->dispPrio;
			return;
		}
	}
	// マウス等级
	mouse.level = DISP_PRIO_TILE;
	// 当たった配列の番号を初期化する
	HitDispNo = -1;
	
#ifdef PUK2_NEW_MENU
		OneLineInfoColor=itemNameColor;
#else
	// タスクバーがない时
	if( taskBarStatus == 0 && taskBarDrawFlag )
	{
		// 一行インフォ表示
		StockFontBuffer( 8, 460, FONT_PRIO_FRONT, itemNameColor, OneLineInfoStr, 0 );
	}
#endif

#ifndef PUK2_NEW_MENU
	// 文字列初期化
	OneLineInfoStr[ 0 ] = NULL;
#endif	

}
