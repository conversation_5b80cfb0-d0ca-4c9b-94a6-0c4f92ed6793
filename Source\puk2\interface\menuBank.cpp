﻿//メニュー＞マップ

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------
/***************************************

银行ウィンドウを生成する场合、

WINDOW_INFO *createBankWindow( int WinType, int SeqNo, int ObjIndex, char *data )

を使用。

***************************************/
#define ITEM_ONE_SELL 6
#define MONSTER_ONE_SELL 28

extern char ItemNoOpe[MAX_ITEM];

void setBankItemMemo( int itemNo, int side, char *memo );

void PcMonsStatusWindow( short x, short y, char Num );
void BankMonsStatusWindow( short x, short y, char Num );

extern int bankDepositItemSelCntMax;
extern int bankDepositMonsterSelCntMax;
extern BANK_NPC bank[2];

struct NEW_BANKMONSTER bankMonster[5];
static int FirstBankMoney;
static int BankCharges;

// 假の入力栏
static INPUT_STR BankInputStr;
static INIT_STR_STRUCT InitStrStructBank={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	&BankInputStr,  0,  -100,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_SHADOW,"",	  1,      0,	  0,     0
};

const int BankGraTbl[][2] = {
	{ GID_BankBase, GID_GuildCommonBase },					// ベース

	{ GID_BankItemPanel, GID_GuildCommonItemPanel },		// 银行侧アイテムパネル
	{ GID_BankMonsterPanel, GID_GuildCommonMonsterPanel },	// 银行侧モンスターパネル
	{ GID_BankMoneyPanel, GID_GuildCommonMoneyPanel },		// 银行侧お金パネル
};

BOOL closeBankWindow();

// 共通ウィンドウ生成(ウィンドウがすでにある场合は、以前のものを书き换え) ++++
WINDOW_INFO *createBankWindow( int WinType, int SeqNo, int ObjIndex )
{
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];

	// ウィドウ种类取得
	wm->WinType=WinType;

	// ウィンドウが存在していなければ生成、死ぬ直前なら复活
	wm->wininfo = createMenuWindow( MENU_WINDOW_BANK );

	// サーバ情报保存
	wm->SeqNo=SeqNo,	wm->ObjIndex=ObjIndex;

	return wm->wininfo;
}

// ウィンドウ破弃 ++++
BOOL closeBankWindow()
{
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	char msg[256];

	if ( wm->WinType == BankType_Nomal ){
		if ( serverRequestWinWindowType == WINDOW_MESSAGETYPE_BANK ) serverRequestWinWindowType = -1;
	}else if ( wm->WinType == BankType_Guild ){
		if ( serverRequestWinWindowType == WINDOW_MESSAGETYPE_ITEMBOX ) serverRequestWinWindowType = -1;
	}

	makeSendString( "C", msg, sizeof( msg )-1 );                         //MLHIDE
	nrproto_WN_send( sockfd, mapGx, mapGy, wm->SeqNo, wm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );

	// 电卓ウィンドウを呼び出したのが自分の场合、电卓ウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_CALCULATOR].wininfo){
		struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
		if (wm->WinType == MENU_WINDOW_BANK){
			wm->wininfo->flag |= WIN_INFO_DEL;
		}
	}

	// キー入力のフォーカスを元に戾す
	if( checkInputFocus( NULL )  ) GetKeyInputFocus( &MyChatBuffer );

	return TRUE;
}

#ifdef VERSION_TW
//台服宠物装备多出的两个参数
#define NEW_MONSTER_ONE_SELL 35
#else
#define NEW_MONSTER_ONE_SELL 33
#endif

BOOL BankDataRenewal( char *data )
{
	char str[1024];
	int no;
	int i,j,k;
	int graNo;

	j = 1;

	if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) return FALSE;
	makeRecvString(str);

	if ( strlen(str)==1 ){
		switch(str[0]){
		case 'I':	// アイテム情报
			i = 0;
			for(i=0;;i++){
				j = i * (ITEM_ONE_SELL + 1) + 2;

				no = getIntegerToken( data, '|', j++ );
				if (no<0) break;

				memset( &bank[0].item[no], 0, sizeof(bank[0].item[no]) );

				graNo = getIntegerToken( data, '|', j++ );
				if( graNo <= 0 ) continue;

				bank[0].item[no].useFlag = 1;
				bank[0].item[no].graNo = graNo;
				bank[0].item[no].num = getIntegerToken( data, '|', j++ );
				bank[0].item[no].flag = getIntegerToken( data, '|', j++ );

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;

				makeRecvString(str);
				if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( bank[0].item[no].name, str );
				else strcpy( bank[0].item[no].name, "???" );                      //MLHIDE

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;

				makeRecvString(str);

				//刻印ハンコ
				if( strlen(str) >= 1 ) strcpy( bank[0].item[no].freeName, str );

				getStringToken( data, '|', j++, sizeof(str) - 1 , str );

				makeRecvString(str);

				setBankItemMemo( no, 0, str );
			}
			return FALSE;
		case 'P':	// ペット
			for(i=0;;i++){
				j = (NEW_MONSTER_ONE_SELL + 1) * i + 2;

				no = getIntegerToken( data, '|', j++ );
				if (no<0) break;

				memset( &bankMonster[no], 0, sizeof(bankMonster[no]) );

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;
				makeRecvString(str);

				if( strlen(str) <= PET_NAME_LEN ) strcpy( bankMonster[no].name, str );
				else strcpy( bankMonster[no].name, "???" );                       //MLHIDE

				bankMonster[no].lv      = getIntegerToken( data, '|', j++ );

				if (bankMonster[no].lv<0) continue;

				bankMonster[no].useFlag = 1;

				bankMonster[no].tribe   = getIntegerToken( data, '|', j++ );
				bankMonster[no].maxLp   = getIntegerToken( data, '|', j++ );
				bankMonster[no].maxFp   = getIntegerToken( data, '|', j++ );
				bankMonster[no].vit     = getIntegerToken( data, '|', j++ );
				bankMonster[no].str     = getIntegerToken( data, '|', j++ );
				bankMonster[no].tgh     = getIntegerToken( data, '|', j++ );
				bankMonster[no].qui     = getIntegerToken( data, '|', j++ );
				bankMonster[no].mgc     = getIntegerToken( data, '|', j++ );
				bankMonster[no].hmg     = getIntegerToken( data, '|', j++ );
				bankMonster[no].maxTech = getIntegerToken( data, '|', j++ );

				getStringToken( data, '|', j++, sizeof(str) - 1 , str );
				makeRecvString(str);
				strcpy( bankMonster[no].defname, str );

				bankMonster[no].graNo   = getIntegerToken( data, '|', j++ );

				bankMonster[no].atk     = getIntegerToken( data, '|', j++ );
				bankMonster[no].def     = getIntegerToken( data, '|', j++ );
				bankMonster[no].agi     = getIntegerToken( data, '|', j++ );
				bankMonster[no].mnd     = getIntegerToken( data, '|', j++ );
				bankMonster[no].rcv     = getIntegerToken( data, '|', j++ );

				bankMonster[no].attr[0] = getIntegerToken( data, '|', j++ );
				bankMonster[no].attr[1] = getIntegerToken( data, '|', j++ );
				bankMonster[no].attr[2] = getIntegerToken( data, '|', j++ );
				bankMonster[no].attr[3] = getIntegerToken( data, '|', j++ );

#ifdef VERSION_TW
				//台服宠物装备多出的两个参数
				getIntegerToken(data, '|', j++);
				getIntegerToken(data, '|', j++);
#endif

				for( k = 0; k < MAX_PET_TECH; k++ ){
#ifdef PUK3_BANK_PET_SKILL10
					getStringToken( data, '|', j++, sizeof( str ) - 1 , str );
#else
					if( getStringToken( data, '|', j++, sizeof( str ) - 1 , str ) != 0 ) continue;
#endif
					makeRecvString( str );
					if( strlen( str ) <= TECH_NAME_LEN ){
						strcpy( bankMonster[no].tech[k].name, str );
					}else{
						strcpy( bankMonster[no].tech[k].name, "???" );                  //MLHIDE
					}
				}
			}
			return FALSE;
		case 'G':
			bank[0].gold = getIntegerToken( data, '|', j++ );
			return FALSE;
		case 'A':
			memset( &bank[0].item, 0, sizeof(bank[0].item) );
			// 预けたアイテムのリスト作成
			for( i = 0, j = 1; i < MAX_DRAW_WIN_ITEM; i++ ){
				j = i * ITEM_ONE_SELL + 2;

				graNo = getIntegerToken( data, '|', j++ );
				if( graNo <= 0 ) continue;

				bank[0].item[i].useFlag = 1;
				bank[0].item[i].graNo = graNo;
				bank[0].item[i].num = getIntegerToken( data, '|', j++ );
				bank[0].item[i].flag = getIntegerToken( data, '|', j++ );

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;

				makeRecvString(str);
				if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( bank[0].item[i].name, str );
				else strcpy( bank[0].item[i].name, "???" );                       //MLHIDE

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;

				makeRecvString(str);

				//刻印ハンコ
				if( strlen(str) >= 1 ) strcpy( bank[0].item[i].freeName, str );

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;

				makeRecvString(str);

				setBankItemMemo( i, 0, str );
			}

			memset( &bankMonster, 0, sizeof(bankMonster) );
			// 预けたモンスターのリスト作成
			for( i = 0; i < MAX_PET; i++ ){
				j = MAX_DRAW_WIN_ITEM * ITEM_ONE_SELL + NEW_MONSTER_ONE_SELL * i + 2;

				if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) continue;
				makeRecvString(str);

				if( strlen(str) <= PET_NAME_LEN ) strcpy( bankMonster[i].name, str );
				else strcpy( bankMonster[i].name, "???" );                        //MLHIDE

				bankMonster[i].lv      = getIntegerToken( data, '|', j++ );

				if (bankMonster[i].lv<0) continue;

				bankMonster[i].useFlag = 1;

				bankMonster[i].tribe   = getIntegerToken( data, '|', j++ );
				bankMonster[i].maxLp   = getIntegerToken( data, '|', j++ );
				bankMonster[i].maxFp   = getIntegerToken( data, '|', j++ );
				bankMonster[i].vit     = getIntegerToken( data, '|', j++ );
				bankMonster[i].str     = getIntegerToken( data, '|', j++ );
				bankMonster[i].tgh     = getIntegerToken( data, '|', j++ );
				bankMonster[i].qui     = getIntegerToken( data, '|', j++ );
				bankMonster[i].mgc     = getIntegerToken( data, '|', j++ );
				bankMonster[i].hmg     = getIntegerToken( data, '|', j++ );
				bankMonster[i].maxTech = getIntegerToken( data, '|', j++ );

				getStringToken( data, '|', j++, sizeof(str) - 1 , str );
				makeRecvString(str);
				strcpy( bankMonster[i].defname, str );

				bankMonster[i].graNo   = getIntegerToken( data, '|', j++ );

				bankMonster[i].atk     = getIntegerToken( data, '|', j++ );
				bankMonster[i].def     = getIntegerToken( data, '|', j++ );
				bankMonster[i].agi     = getIntegerToken( data, '|', j++ );
				bankMonster[i].mnd     = getIntegerToken( data, '|', j++ );
				bankMonster[i].rcv     = getIntegerToken( data, '|', j++ );

				bankMonster[i].attr[0] = getIntegerToken( data, '|', j++ );
				bankMonster[i].attr[1] = getIntegerToken( data, '|', j++ );
				bankMonster[i].attr[2] = getIntegerToken( data, '|', j++ );
				bankMonster[i].attr[3] = getIntegerToken( data, '|', j++ );
#ifdef VERSION_TW
				//台服宠物装备多出的两个参数
				getIntegerToken(data, '|', j++);
				getIntegerToken(data, '|', j++);
#endif

				for( k = 0; k < MAX_PET_TECH; k++ ){
#ifdef PUK3_BANK_PET_SKILL10
					getStringToken( data, '|', j++, sizeof( str ) - 1 , str );
#else
					if( getStringToken( data, '|', j++, sizeof( str ) - 1 , str ) != 0 ) continue;
#endif
					makeRecvString( str );
					if( strlen( str ) <= TECH_NAME_LEN ){
						strcpy( bankMonster[i].tech[k].name, str );
					}else{
						strcpy( bankMonster[i].tech[k].name, "???" );                   //MLHIDE
					}
				}
			}

			j = MAX_DRAW_WIN_ITEM * ITEM_ONE_SELL + MAX_PET * NEW_MONSTER_ONE_SELL + 2;
			bank[0].gold = getIntegerToken( data, '|', j++ );
			return TRUE;
		}
	}
	return FALSE;
}

BOOL MenuWindowBankBf( int mouse )
{
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	int i;

	if (mouse==WIN_INIT){
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankWindow].Switch )->graNo = BankGraTbl[0][wm->WinType];

		wm->MonsterStart[0] = wm->MonsterStart[1] = 0;
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo = GID_My_Item;
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankBankPanel].Switch )->graNo = BankGraTbl[1][wm->WinType];
		for(i=EnumBankMyMonster1Panel;i<EnumBankMyPanel;i++) wI->sw[i].Enabled = FALSE;

/***
		wI->sw[EnumBankChargesText].Enabled = TRUE;
		if ( wm->WinType == BankType_Guild ) wI->sw[EnumBankChargesText].Enabled = FALSE;
***/
		wI->sw[EnumBankChargesText].Enabled = FALSE;
	}

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if ( checkMoveMapGridPos( 1, 1 ) ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
	if ( wm->WinType == BankType_Nomal ){
		if ( serverRequestWinWindowType != WINDOW_MESSAGETYPE_BANK ){
			wI->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}
	}
	if ( wm->WinType == BankType_Guild ){
		if ( serverRequestWinWindowType != WINDOW_MESSAGETYPE_ITEMBOX ){
			wI->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}
	}
	wm->itemInfoNo = -1;

	BankCharges = 0;

	return TRUE;
}

BOOL MenuWindowBankAf( int Mouse )
{
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	int x = wI->wx + 35;
	int y = wI->wy + 329;
	char str[15];

	sprintf( str, "%10d", BankCharges );                                 //MLHIDE
	strcpy( ( (TEXT_SWITCH *)wI->sw[EnumBankChargesText].Switch )->text, str );

	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wI->sw[EnumBankMyMonsterScroll].Switch )->status&1 ){
		wm->MonsterStart[0] = ScrollVPointToNum( &wI->sw[EnumBankMyMonsterScroll], 2 );
	}
	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wI->sw[EnumBankBankMonsterScroll].Switch )->status&1 ){
		wm->MonsterStart[1] = ScrollVPointToNum( &wI->sw[EnumBankBankMonsterScroll], 2 );
	}

	// アイテム信息
	if (wm->itemInfoNo>=0){
		// アイテム
		if (wm->itemInfoNo<200){
			// １００未满は手持ち
			if (wm->itemInfoNo<100) PcItemExplanationWindow( wm->itemInfoNo, wm->itemInfoPage );
			else BankItemExplanationWindow( wm->itemInfoNo-100, wm->itemInfoPage );
		}
		// モンスター
		else{
			wm->itemInfoNo -= 200;

			x = 0;
			if (mouse.nowPoint.x<320) x = 640 - 227;

			// １００未满は手持ち
			if (wm->itemInfoNo<100) PcMonsStatusWindow( x, 0, wm->itemInfoNo );
			else BankMonsStatusWindow( x, 0, wm->itemInfoNo-100 );
		}
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------
BOOL MenuBankGetKeyForcus( int no, unsigned int flag )
{
	if ( wI->sw[EnumBankBankCalculatorD].Enabled || wI->sw[EnumBankBankCalculatorW].Enabled ){
		if ( flag & MENU_MOUSE_LEFT ){
			WINDOW_INFO *wi = WindowFlag[MENU_WINDOW_BANK].wininfo;

			// 文字入力栏移动
			SetInputStr( &InitStrStructBank, 0, -100, 0 );

			// フォーカスを取る
			GetKeyInputFocus( &BankInputStr );

			DiarogST.SwAdd = wi->sw[EnumBankGetKeyForcus].Switch;
			( (DIALOG_SWITCH *)wi->sw[EnumBankGetKeyForcus].Switch )->InpuStrAdd = &BankInputStr;
		}
	}else{
		if (pNowInputStr==&BankInputStr) SetDialogMenuChat();
	}

	return FALSE;
}

BOOL MenuBankCloseButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	if ( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuBankOKButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_BANK_OK );

	if ( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	Graph->graNo = GID_BigOKButtonOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;

	return ReturnFlag;
}

BOOL MenuBankItemButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_BANK_ITEM );

	if ( flag & MENU_MOUSE_LEFT ){
		if ( ( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo == GID_My_Item ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else{
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}

		( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo = GID_My_Item;
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankBankPanel].Switch )->graNo = BankGraTbl[1][wm->WinType];

		for(i=EnumBankMyMonster1Panel;i<EnumBankMyPanel;i++) wI->sw[i].Enabled = FALSE;

		wI->sw[EnumBankMyPanel].func = MenuBankMyItemPanel;
		wI->sw[EnumBankBankPanel].func = MenuBankBankItemPanel;
	}

	Graph->graNo = GID_ItemChgOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ItemChgOver;
	if ( ( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo == GID_My_Item ){
		Graph->graNo = GID_ItemChgOff;
	}

	return ReturnFlag;
}

BOOL MenuBankMonsterButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_BANK_MONSTER );

	if ( flag & MENU_MOUSE_LEFT ){
		if ( ( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo == GID_My_Monster ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else{
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}

		( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo = GID_My_Monster;
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankBankPanel].Switch )->graNo = BankGraTbl[2][wm->WinType];;

		for(i=EnumBankMyMonster1Panel;i<EnumBankMyPanel;i++) wI->sw[i].Enabled = FALSE;

		for(i=EnumBankMyMonster1Panel;i<=EnumBankBankMonsterScrollDown;i++) wI->sw[i].Enabled = TRUE;

		wI->sw[EnumBankMyPanel].func = MenuSwitchNone;
		wI->sw[EnumBankBankPanel].func = MenuSwitchNone;
	}

	Graph->graNo = GID_MonsterChgOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_MonsterChgOver;
	if ( ( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo == GID_My_Monster ){
		Graph->graNo = GID_MonsterChgOff;
	}

	return ReturnFlag;
}

BOOL MenuBankDepositButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_BANK_DEPOSIT );

	if ( flag & MENU_MOUSE_LEFT ){
		if ( wI->sw[EnumBankBankCalculatorD].Enabled ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else{
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}

		( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo = GID_My_Gold;
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankBankPanel].Switch )->graNo = BankGraTbl[3][wm->WinType];;

		for(i=EnumBankMyMonster1Panel;i<EnumBankMyPanel;i++) wI->sw[i].Enabled = FALSE;

		wI->sw[EnumBankBankCalculatorD].Enabled = TRUE;

		wI->sw[EnumBankBankMyGoldText].Enabled = TRUE;
		wI->sw[EnumBankBankBankGoldText].Enabled = TRUE;

		wI->sw[EnumBankMyPanel].func = MenuSwitchNone;
		wI->sw[EnumBankBankPanel].func = MenuSwitchNone;

		FirstBankMoney = bank[0].gold;
		wI->sw[EnumBankBankCalculatorD].status = 0;

		MenuBankGetKeyForcus( 0, MENU_MOUSE_LEFT );
	}

	Graph->graNo = GID_MoneyChgOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_MoneyChgOver;
	if ( wI->sw[EnumBankBankCalculatorD].Enabled ){
		Graph->graNo = GID_MoneyChgOff;
	}

	return ReturnFlag;
}

BOOL MenuBankWithdrawButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	struct BANKWINDOWMASTER *wm=(BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_BANK_WITHDRAW );

	if ( flag & MENU_MOUSE_LEFT ){
		if ( wI->sw[EnumBankBankCalculatorW].Enabled ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else{
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}

		( (GRAPHIC_SWITCH *)wI->sw[EnumBankMyPanel].Switch )->graNo = GID_My_Gold;
		( (GRAPHIC_SWITCH *)wI->sw[EnumBankBankPanel].Switch )->graNo = BankGraTbl[3][wm->WinType];;

		for(i=EnumBankMyMonster1Panel;i<EnumBankMyPanel;i++) wI->sw[i].Enabled = FALSE;

		wI->sw[EnumBankBankCalculatorW].Enabled = TRUE;

		wI->sw[EnumBankBankMyGoldText].Enabled = TRUE;
		wI->sw[EnumBankBankBankGoldText].Enabled = TRUE;

		wI->sw[EnumBankMyPanel].func = MenuSwitchNone;
		wI->sw[EnumBankBankPanel].func = MenuSwitchNone;

		FirstBankMoney = bank[0].gold;
		wI->sw[EnumBankBankCalculatorW].status = 0;

		MenuBankGetKeyForcus( 0, MENU_MOUSE_LEFT );
	}

	Graph->graNo = GID_MoneyChgOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_MoneyChgOver;
	if ( wI->sw[EnumBankBankCalculatorW].Enabled ){
		Graph->graNo = GID_MoneyChgOff;
	}

	return ReturnFlag;
}

BOOL MenuBankTextDraw( int no, unsigned int flag )
{
	int i,j;
	char str[20];

	j=0;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		if ( pc.item[i+MAX_EQUIP_ITEM].useFlag ) j++;
	}
	sprintf( str, "%3d", j );                                            //MLHIDE
	DrawGraphicNumber( wI->wx+286+71, wI->wy+285, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );
	j=0;
	for(i=0;i<MAX_PET;i++){
		if ( pet[i].useFlag ) j++;
	}
	sprintf( str, "%3d", j );                                            //MLHIDE
	DrawGraphicNumber( wI->wx+286+156, wI->wy+285, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );
	sprintf( str, "%10d", pc.gold );                                     //MLHIDE
	DrawGraphicNumber( wI->wx+286+241, wI->wy+285, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );

	j=0;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		if ( bank[0].item[i].useFlag ) j++;
	}
	sprintf( str, "%3d", j );                                            //MLHIDE
	DrawGraphicNumber( wI->wx+14+71, wI->wy+285, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );
	j=0;
	for(i=0;i<5;i++){
		if ( bankMonster[i].useFlag ) j++;
	}
	sprintf( str, "%3d", j );                                            //MLHIDE
	DrawGraphicNumber( wI->wx+14+156, wI->wy+285, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );
	sprintf( str, "%10d", bank[0].gold );                                //MLHIDE
	DrawGraphicNumber( wI->wx+14+241, wI->wy+285, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );

	return FALSE;
}

//------------------------------------------------------------------------//
// アイテム																  //
//------------------------------------------------------------------------//
BOOL MenuBankMyItemPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[10];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]) continue;
			x = wI->wx + wI->sw[no].ofx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo+MAX_EQUIP_ITEM ) ItemMove( MENU_WINDOW_BANK, no, DropitemNo, itemNo+8 );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]) continue;
			x = wI->wx + wI->sw[no].ofx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		if ( itemNo>=0 && pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
////			if ( pc.gold >= 1 ){
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
					strcpy( OneLineInfoStr, MWONELINE_BANK_MYITEM_OFF );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_BANK_MYITEM_ON );
				}
////			}else{
////				strcpy( OneLineInfoStr, "所持金额币不够。" );
////			}
		}
	}

#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag &&
				 ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) ){
				itemNo = -1;
			}
		}
	}
#else
	if ( ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag &&
				 ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) ){
				itemNo = -1;
			}
		}
	}
#endif

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) wm->itemInfoPage = 0;
	olditemNo = itemNo;

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_BANK || WinDD_WinType()==MENU_WINDOW_NONE ){
			WinDD_DragFinish();
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				// アイテムがあるなら次へ
				if( bank[0].item[i].useFlag ) continue;
				break;
			}
			// 移动プロトコル送信
			if (i<MAX_DRAW_WIN_ITEM && pc.item[itemNo+MAX_EQUIP_ITEM].useFlag){
				ItemMove( MENU_WINDOW_BANK, no, itemNo+MAX_EQUIP_ITEM, i+100 );
			}
			else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
#ifdef PUK2_NEWDRAG
					DragItem( itemNo+MAX_EQUIP_ITEM, TRUE );
#else
					// ドラッグ开始
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+MAX_EQUIP_ITEM) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= pc.item[itemNo+MAX_EQUIP_ITEM].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_BANK && WinDD_ButtonNo()==no ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( !pc.item[DragitemNo].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_BANK ){
			if ( WinDD_ButtonNo()==no ){
				// 掴んだアイテムの表示
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
////				if ( pc.gold < 1 ) strcpy( OneLineInfoStr, "所持金额币不够。" );
				BankCharges++;
			}
		}
#endif

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo >= MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + 13 + ( ( (DragitemNo-MAX_EQUIP_ITEM)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 26 + ( ( (DragitemNo-MAX_EQUIP_ITEM)/ITEM_DRAW_COLUMN ) * 52 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( itemNo >= 0 ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx + 13 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 26 + ( (itemNo/ITEM_DRAW_COLUMN) * 52 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
			wm->itemInfoNo = itemNo+MAX_EQUIP_ITEM;

			// 银行侧のアイテムをドラッグ中なら
			if ( WinDD_CheckObjType()==WINDD_ITEM ){
				if ( (int)WinDD_ObjData() >= 100 ){
////					if ( pc.gold < 1 ) strcpy( OneLineInfoStr, "所持金额币不够。" );
					BankCharges++;
				}
			}
		}
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	bm.rgba.rgba=0xffffffff;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 37 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 51 + ( (i/ITEM_DRAW_COLUMN) * 52 );

			// 选择不可能なアイテムに禁止マークをつける
			if ( pc.item[i+8].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );
			}
			if (ItemNoOpe[i+8]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

			// 个数表示
			if( pc.item[i+8].num > 0 ){
				sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );             //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

BOOL MenuBankBankItemPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[10];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
#else
			WinDD_GetObject();
#endif
		}else
		// 装备品はだめ
		if ( (int)WinDD_ObjData() < MAX_EQUIP_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			x = wI->wx + wI->sw[no].ofx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo+100 ){
				if ( DropitemNo<100 && ( pc.item[DropitemNo].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) );
				else ItemMove( MENU_WINDOW_BANK, no, DropitemNo, itemNo+100 );
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			x = wI->wx + wI->sw[no].ofx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}
#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		DropitemNo = (int)WinDD_ObjData();
		if ( DropitemNo<100 && ( pc.item[DropitemNo].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) ){
			itemNo = -1;
		}
	}
#else
	if ( flag & MENU_MOUSE_DRAGOVER ){
		DropitemNo = (int)WinDD_ObjData();
		if ( DropitemNo<100 && ( pc.item[DropitemNo].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) ){
			itemNo = -1;
		}
	}
#endif

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		if ( itemNo>=0 && bank[0].item[itemNo].useFlag ) strcpy( OneLineInfoStr, MWONELINE_BANK_BANKITEM );
	}

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) wm->itemInfoPage = 0;
	olditemNo = itemNo;

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_BANK || WinDD_WinType()==MENU_WINDOW_NONE ){
			WinDD_DragFinish();
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				// アイテムがあるなら次へ
				if( pc.item[i+MAX_EQUIP_ITEM].useFlag ) continue;
				break;
			}
			// 移动プロトコル送信
			if ( i<20 && bank[0].item[itemNo].useFlag ) ItemMove( MENU_WINDOW_BANK, no, itemNo+100, i+8 );
			else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( bank[0].item[itemNo].useFlag ){
#ifdef PUK2_NEWDRAG
					DragItem( itemNo+100, FALSE );
#else
					// ドラッグ开始
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+100) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( bank[0].item[itemNo].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= bank[0].item[itemNo].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_BANK && WinDD_ButtonNo()==no ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( !bank[0].item[DragitemNo-100].useFlag ) WinDD_DragFinish();
			// 银行侧のアイテムなら
			if ( (int)WinDD_ObjData() >= 100 ){
				// 右键したらアイテムドロップ
				if ( mouse.onceState & MOUSE_LEFT_CRICK ){
					WinDD_DragFinish();
					WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
				}
				// 右クリックしたらドラッグ終了
				if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
			}
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();
		// 银行侧のアイテムなら
		if ( DragitemNo >= 100 ){
			DragitemNo-=100;

#ifdef PUK2_NEWDRAG
#else
			// ドラッグ元が自分なら
			if ( WinDD_WinType()==MENU_WINDOW_BANK ){
				if ( WinDD_ButtonNo()==no ){
					// 掴んだアイテムの表示
					StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, bank[0].item[DragitemNo].graNo, 0, &bm2 );
				}
			}
#endif

			// アイテムを掴んだ位置に枠表示
			x = wI->wx + wI->sw[no].ofx + 13 + ( ( (DragitemNo)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 26 + ( ( (DragitemNo)/ITEM_DRAW_COLUMN ) * 52 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( itemNo >= 0 ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx + 13 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 26 + ( (itemNo/ITEM_DRAW_COLUMN) * 52 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		if ( bank[0].item[itemNo].useFlag ) wm->itemInfoNo = itemNo+100;
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	bm.rgba.rgba=0xffffffff;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムがあるなら表示
		if( bank[0].item[i].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 37 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 51 + ( (i/ITEM_DRAW_COLUMN) * 52 );

			StockDispBuffer( x, y, DISP_PRIO_WIN2, bank[0].item[i].graNo, 0, &bm );

			// 个数表示
			if( bank[0].item[i].num > 0 ){
				sprintf( str, "%3d", bank[0].item[i].num );                       //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

//------------------------------------------------------------------------//
// モンスター															  //
//------------------------------------------------------------------------//
BOOL MenuSwitchBankScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = (no<EnumBankBankMonsterScrollGra?0:1);

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (wm->MonsterStart[Num]>0){
			wm->MonsterStart[Num]--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-1], 2, wm->MonsterStart[Num] );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchBankScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)wF;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = (no<EnumBankBankMonsterScrollGra?0:1);

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (wm->MonsterStart[Num]<2){
			wm->MonsterStart[Num]++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-2], 2, wm->MonsterStart[Num] );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchBankScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)wF;
	int Num = (no<EnumBankBankMonsterScrollGra?0:1);

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		wm->MonsterStart[Num] = WheelToMove( &wI->sw[no-3],
			 wm->MonsterStart[Num], 2, mouse.wheel );
	}

	return ReturnFlag;
}

// 自分侧モンスターの状态表示 ++++
void DrawMyMonsterStatus( short x, short y, int no, int Prio )
{
	char str[255];

	if (!pet[no].useFlag) return;

	// プライオリティの制御
	FontBufCut(Prio);
	StockFontBuffer( 0, 0, Prio, FONT_KIND_BIG3, FONT_PAL_WHITE, "", 0, 0 );

	// 名称表示
	if( strlen( pet[no].freeName ) == 0 ){
		StockFontBuffer( x+45, y+5, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, pet[no].name, 0, 0 );
	}else{
		StockFontBuffer( x+45, y+5, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, pet[no].freeName, 0, 0 );
	}
	// 等级表示
	sprintf( str, "%3d", pet[no].lv );                                   //MLHIDE
	StockFontBuffer( x+35, y+22, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// 种族表示
	if ( (pet[no].tribe>=0) && (pet[no].tribe< (sizeof(characterTribeStr)>>2) ) ){
		StockFontBuffer( x+100, y+22, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, characterTribeStr[pet[no].tribe], 0, 0 );
	}
	// LP表示
	sprintf( str, "%4d", pet[no].maxLp );                                //MLHIDE
	StockFontBuffer( x+210, y+5, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// FP表示
	sprintf( str, "%4d", pet[no].maxFp );                                //MLHIDE
	StockFontBuffer( x+210, y+22, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// VTL表示
	sprintf( str, "%3d", pet[no].vit );                                  //MLHIDE
	StockFontBuffer( x+95, y+40, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// STR表示
	sprintf( str, "%3d", pet[no].str );                                  //MLHIDE
	StockFontBuffer( x+155, y+40, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// TGH表示
	sprintf( str, "%3d", pet[no].tgh );                                  //MLHIDE
	StockFontBuffer( x+215, y+40, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// QUI表示
	sprintf( str, "%3d", pet[no].qui );                                  //MLHIDE
	StockFontBuffer( x+95, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// MGC表示
	sprintf( str, "%3d", pet[no].mgc );                                  //MLHIDE
	StockFontBuffer( x+155, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// HMC表示
	sprintf( str, "%3d", pet[no].hmg );                                  //MLHIDE
	StockFontBuffer( x+215, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// 技最大数表示
	sprintf( str, "%2d", pet[no].maxTech );                              //MLHIDE
	StockFontBuffer( x+45, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
}

BOOL MenuBankMyMonsterPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	int Num = sortPet[ no-EnumBankMyMonster1Panel+wm->MonsterStart[0] ].index;
	BLT_MEMBER bm={0};
	int DragMonsNo, DropMonsNo;
#ifdef PUK2_NOPETSORT
	int i;
#else
	int i,j, over;
#endif

#ifdef PUK2_NOPETSORT
	{
#else
	// 手持ちの最大数を超えた位置なら处理をしない
	j=0;
	for(i=0;i<MAX_PET;i++){
		if ( pet[i].useFlag ) j++;
	}
	over = j;
	if ( no-EnumBankMyMonster1Panel+wm->MonsterStart[0] > over ){
#endif
		bm.rgba.rgba = 0xffffffff;
		bm.bltf = BLTF_NOCHG;

#ifdef PUK2_NEWDRAG
#else
		// 他の处理はしなくても、ドラッグ处理はする
		if ( WinDD_CheckObjType()==WINDD_MONSTER ){
			// ドラッグ元が自分なら
			if ( ( WinDD_WinType()==MENU_WINDOW_BANK )&&( WinDD_ButtonNo()==no ) ){
				DragMonsNo = (int)WinDD_ObjData();
				// ドラッグ元にモンスターが无いならドラッグ終了
				if ( !pet[DragMonsNo].useFlag ) WinDD_DragFinish();
				// 右键したらアイテムドロップ
				if ( mouse.onceState & MOUSE_LEFT_CRICK ){
					WinDD_DragFinish();
					WinDD_DropObject( WINDD_MONSTER, (void *)(DragMonsNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
				}
				// 右クリックしたらドラッグ終了
				if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
////				if ( pc.gold >= pc.lv ) strcpy( OneLineInfoStr, "所持金额币不够。" );
			}
		}
#endif
		if ( WinDD_CheckObjType()==WINDD_MONSTER ){
			DragMonsNo = (int)WinDD_ObjData();
			// 自分侧のモンスターなら
			if ( DragMonsNo < 100 ){
				// ドラッグ元が自分なら
				if ( ( WinDD_WinType()==MENU_WINDOW_BANK )&&( WinDD_ButtonNo()==no ) ){
#ifdef PUK2_NEWDRAG
#else
					// 掴んだモンスターの表示
					DrawMyMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, DragMonsNo, FONT_PRIO_DRAG );
					bm.rgba.a=0x80;
					StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm );
#endif

					if ( getsortMonsPos(DragMonsNo) - wm->MonsterStart[0] >= 0 &&
						 getsortMonsPos(DragMonsNo) - wm->MonsterStart[0] < 3 ){
						i = ( getsortMonsPos(DragMonsNo) - wm->MonsterStart[0] ) + EnumBankMyMonster1Panel;
						// 掴んだ位置に枠表示
						StockBoxDispBuffer(
							wI->wx+wI->sw[i].ofx+2,
							wI->wy+wI->sw[i].ofy+2,
							wI->wx+wI->sw[i].ofx+wI->sw[i].sx-2,
							wI->wy+wI->sw[i].ofy+wI->sw[i].sy-2,
							DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
					}

					BankCharges+=pc.lv;
//					if ( pc.gold < pc.lv ) strcpy( OneLineInfoStr, "所持金额币不够。" );
				}
			}
		}
#ifdef PUK2_NOPETSORT
#else
		return FALSE;
#endif
	}

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_MONSTER ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// モンスターがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropMonsNo = (int)WinDD_ObjData();
#else
		DropMonsNo = (int)WinDD_GetObject();
#endif

		// 掴んだ位置と违うならプロトコル送信
		if( DropMonsNo != Num ){
			MonsMove( MENU_WINDOW_BANK, no, DropMonsNo, Num );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		if ( pet[Num].useFlag ){
			if ( pc.gold >= pc.lv ){
				strcpy( OneLineInfoStr, MWONELINE_BANK_MYMONSTER );
			}
////			else{
////				strcpy( OneLineInfoStr, "所持金额币不够。" );
////			}
		}
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( pet[Num].useFlag && ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_BANK || WinDD_WinType()==MENU_WINDOW_NONE ){
			int i;
			WinDD_DragFinish();
			for(i=0;i<MAX_PET;i++){
				// モンスターがいるなら次へ
				if( bankMonster[i].useFlag ) continue;
				break;
			}
			// 移动プロトコル送信
			if (i<MAX_PET){
				MonsMove( MENU_WINDOW_BANK, no, Num, i+100 );
			}
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// 右键したとき
		if( flag & MENU_MOUSE_LEFT ){
			// その场所にモンスターがいるなら
			if ( pet[Num].useFlag ){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragMons( Num, 1 );
#else
				WinDD_DragStart( WINDD_MONSTER, (void *)(Num) );
#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			ReturnFlag=TRUE;
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_MONSTER ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_BANK )&&( WinDD_ButtonNo()==no ) ){
			DragMonsNo = (int)WinDD_ObjData();
			// ドラッグ元にモンスターが无いならドラッグ終了
			if ( !pet[DragMonsNo].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_MONSTER, (void *)(DragMonsNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
////			if ( pc.gold >= pc.lv ) strcpy( OneLineInfoStr, "所持金额币不够。" );
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_MONSTER ){
		DragMonsNo = (int)WinDD_ObjData();
		// 自分侧のモンスターなら
		if ( DragMonsNo < 100 ){
			// ドラッグ元が自分なら
			if ( ( WinDD_WinType()==MENU_WINDOW_BANK )&&( WinDD_ButtonNo()==no ) ){
#ifdef PUK2_NEWDRAG
#else
				// 掴んだモンスターの表示
				DrawMyMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, DragMonsNo, FONT_PRIO_DRAG );
				bm.rgba.a=0x80;
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm );
#endif

				if ( getsortMonsPos(DragMonsNo) - wm->MonsterStart[0] >= 0 &&
					 getsortMonsPos(DragMonsNo) - wm->MonsterStart[0] < 3 ){
					i = ( getsortMonsPos(DragMonsNo) - wm->MonsterStart[0] ) + EnumBankMyMonster1Panel;
					// 掴んだ位置に枠表示
					StockBoxDispBuffer(
						wI->wx+wI->sw[i].ofx+2,
						wI->wy+wI->sw[i].ofy+2,
						wI->wx+wI->sw[i].ofx+wI->sw[i].sx-2,
						wI->wy+wI->sw[i].ofy+wI->sw[i].sy-2,
						DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
				}

				BankCharges+=pc.lv;
//				if ( pc.gold < pc.lv ) strcpy( OneLineInfoStr, "所持金额币不够。" );
			}
		}
	}

	// 重なってるなら枠
	if(flag & MENU_MOUSE_OVER){
#ifdef PUK2_NOPETSORT
		displaySwitchFrame( &wI->sw[no], 0x01, BoxColor );
		if (pet[Num].useFlag){
			wm->itemInfoNo = 200 + Num;
		}
#else
		if (pet[Num].useFlag){
			displaySwitchFrame( &wI->sw[no], 0x01, BoxColor );
			wm->itemInfoNo = 200 + Num;
		}
#endif
	}
	// 重なってるなら枠
	if(flag & MENU_MOUSE_DRAGOVER){
		displaySwitchFrame( &wI->sw[no], 0x01, BoxColor );
		// 银行侧のアイテムをドラッグ中なら
		if ( WinDD_CheckObjType()==WINDD_MONSTER ){
			if ( (int)WinDD_ObjData() >= 100 ){
////				if ( pc.gold < pc.lv ) strcpy( OneLineInfoStr, "所持金额币不够。" );
				BankCharges+=pc.lv;
			}
		}
	}

	DrawMyMonsterStatus( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, Num, FONT_PRIO_WIN );

	return ReturnFlag;
}

// 银行侧モンスターの状态表示 ++++
void DrawBankMonsterStatus( short x, short y, int no, int Prio )
{
	char str[255];

	if (!bankMonster[no].useFlag) return;

	// プライオリティの制御
	FontBufCut(Prio);
	StockFontBuffer( 0, 0, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, "", 0, 0 );

	// 名称表示
	StockFontBuffer( x+45, y+5, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, bankMonster[no].name, 0, 0 );
	// 等级表示
	sprintf( str, "%3d", bankMonster[no].lv );                           //MLHIDE
	StockFontBuffer( x+35, y+22, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// 种族表示
	if ( (bankMonster[no].tribe>=0) && (bankMonster[no].tribe< (sizeof(characterTribeStr)>>2) ) ){
		StockFontBuffer( x+100, y+22, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, characterTribeStr[bankMonster[no].tribe], 0, 0 );
	}
	// LP表示
	sprintf( str, "%4d", bankMonster[no].maxLp );                        //MLHIDE
	StockFontBuffer( x+210, y+5, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// FP表示
	sprintf( str, "%4d", bankMonster[no].maxFp );                        //MLHIDE
	StockFontBuffer( x+210, y+22, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// VTL表示
	sprintf( str, "%3d", bankMonster[no].vit );                          //MLHIDE
	StockFontBuffer( x+95, y+40, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// STR表示
	sprintf( str, "%3d", bankMonster[no].str );                          //MLHIDE
	StockFontBuffer( x+155, y+40, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// TGH表示
	sprintf( str, "%3d", bankMonster[no].tgh );                          //MLHIDE
	StockFontBuffer( x+215, y+40, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// QUI表示
	sprintf( str, "%3d", bankMonster[no].qui );                          //MLHIDE
	StockFontBuffer( x+95, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// MGC表示
	sprintf( str, "%3d", bankMonster[no].mgc );                          //MLHIDE
	StockFontBuffer( x+155, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// HMC表示
	sprintf( str, "%3d", bankMonster[no].hmg );                          //MLHIDE
	StockFontBuffer( x+215, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	// 技最大数表示
	sprintf( str, "%2d", bankMonster[no].maxTech );                      //MLHIDE
	StockFontBuffer( x+45, y+55, Prio, FONT_KIND_BIG3, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
}

BOOL MenuBankBankMonsterPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	int Num = no-EnumBankBankMonster1Panel+wm->MonsterStart[1];
	BLT_MEMBER bm={0};
	int DragMonsNo, DropMonsNo;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_MONSTER ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// モンスターがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropMonsNo = (int)WinDD_ObjData();
#else
		DropMonsNo = (int)WinDD_GetObject();
#endif

		// 掴んだ位置と违うならプロトコル送信
		if( DropMonsNo != Num+100 ){
			MonsMove( MENU_WINDOW_BANK, no, DropMonsNo, Num+100 );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		if ( bankMonster[Num].useFlag ) strcpy( OneLineInfoStr, MWONELINE_BANK_BANKMONSTER );
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( bankMonster[Num].useFlag && ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_BANK || WinDD_WinType()==MENU_WINDOW_NONE ){
			int i;
			WinDD_DragFinish();
			for(i=0;i<MAX_PET;i++){
				// モンスターがいるなら次へ
				if( pet[ sortPet[i].index ].useFlag ) continue;
				break;
			}
			// 移动プロトコル送信
			if (i<MAX_PET){
				char str[128], msg[256];
				sprintf( str, "P|%d|%d", Num+100, sortPet[i].index );             //MLHIDE
				makeSendString( str, msg, sizeof( msg )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy, wm->SeqNo, wm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );
			}
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// 右键したとき
		if( flag & MENU_MOUSE_LEFT ){
			// その场所にモンスターがいるなら
			if ( bankMonster[Num].useFlag ){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragMons( Num+100, 1 );
#else
				WinDD_DragStart( WINDD_MONSTER, (void *)(Num+100) );
#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			ReturnFlag=TRUE;
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_MONSTER ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_BANK )&&( WinDD_ButtonNo()==no ) ){
			DragMonsNo = (int)WinDD_ObjData();
			// ドラッグ元にモンスターが无いならドラッグ終了
			if ( !bankMonster[DragMonsNo-100].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_MONSTER, (void *)(DragMonsNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_MONSTER ){
		DragMonsNo = (int)WinDD_ObjData();
		// 银行侧のモンスターなら
		if ( DragMonsNo >= 100 ){
			DragMonsNo-=100;
			// ドラッグ元が自分なら
			if ( ( WinDD_WinType()==MENU_WINDOW_BANK )&&( WinDD_ButtonNo()==no ) ){
#ifdef PUK2_NEWDRAG
#else
				// 掴んだモンスターの表示
				DrawBankMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, DragMonsNo, FONT_PRIO_DRAG );
				bm.rgba.a=0x80;
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm );
#endif

				if ( DragMonsNo - wm->MonsterStart[1] >= 0 &&
					 DragMonsNo - wm->MonsterStart[1] < 3 ){
					int i = EnumBankBankMonster1Panel + (DragMonsNo - wm->MonsterStart[1]);
					// 掴んだ位置に枠表示
					StockBoxDispBuffer(
						wI->wx+wI->sw[i].ofx+2, wI->wy+wI->sw[i].ofy+2,
						wI->wx+wI->sw[i].ofx+wI->sw[i].sx-2, wI->wy+wI->sw[i].ofy+wI->sw[i].sy-2,
						DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
				}
			}
		}else{
			// 手续费の表示
			sprintf( ( (TEXT_SWITCH *)wI->sw[EnumBankChargesText].Switch )->text, "%10d", pc.lv ); //MLHIDE
		}
	}

	// 重なってるなら枠
	if(flag & MENU_MOUSE_OVER){
		if (bankMonster[Num].useFlag){
			displaySwitchFrame( &wI->sw[no], 0x01, BoxColor );
			wm->itemInfoNo = 300 + Num;
		}
	}
	// 重なってるなら枠
	if(flag & MENU_MOUSE_DRAGOVER){
		displaySwitchFrame( &wI->sw[no], 0x01, BoxColor );
	}

	DrawBankMonsterStatus( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, Num, FONT_PRIO_WIN );

	return ReturnFlag;
}

//------------------------------------------------------------------------//
// お金																	  //
//------------------------------------------------------------------------//
static char *BankMycalcuwinOneLineInfo[]={
	MWONELINE_BANK_MYMONEYNUM,
	MWONELINE_BANK_MYMONEYOK_ON,
	MWONELINE_BANK_MYMONEYOK_OFF,
	MWONELINE_BANK_MYMONEYBS,
	MWONELINE_BANK_MYMONEYALL,
	MWONELINE_BANK_MYMONEYCLR,
};
static char *BankBankcalcuwinOneLineInfo[]={
	MWONELINE_BANK_BANKMONEYNUM,
	MWONELINE_BANK_BANKMONEYOK_ON,
	MWONELINE_BANK_BANKMONEYOK_OFF,
	MWONELINE_BANK_BANKMONEYBS,
	MWONELINE_BANK_BANKMONEYALL,
	MWONELINE_BANK_BANKMONEYCLR,
};
BOOL MenuBankSwitchCalculator( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BANKWINDOWMASTER *wm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	char ret;
	int Max;

	if (no==EnumBankBankCalculatorD){
		Max = MAX_GOLD - bank[0].gold;
		if (Max>pc.gold) Max = pc.gold;
		ret = CalculatorControl( wI, no, flag, Max, DISP_PRIO_WIN2, BankMycalcuwinOneLineInfo,
			(pNowInputStr==&BankInputStr?CALCULATOR_OPT_SHORTCUT:0) );
	}
	if (no==EnumBankBankCalculatorW){
		Max = MAX_GOLD - pc.gold;
		if (Max>bank[0].gold) Max = bank[0].gold;
		ret = CalculatorControl( wI, no, flag, Max, DISP_PRIO_WIN2, BankBankcalcuwinOneLineInfo,
			(pNowInputStr==&BankInputStr?CALCULATOR_OPT_SHORTCUT:0) );
	}

	if ( ret&CALCULATOR_PUSH ){
		if ( wI->sw[no].status>Max ) wI->sw[no].status = Max;
		FirstBankMoney = bank[0].gold;
		ReturnFlag = TRUE;
	}
	if ( ret&CALCULATOR_OK ){
		char str[128], msg[256];
		if (no==EnumBankBankCalculatorD){
			sprintf( str, "G|%d", wI->sw[no].status );                         //MLHIDE
			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, wm->SeqNo, wm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );
		}
		if (no==EnumBankBankCalculatorW){
			sprintf( str, "D|%d", wI->sw[no].status );                         //MLHIDE
			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, wm->SeqNo, wm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );
		}
		wI->sw[no].status = 0;
	}

	if (no==EnumBankBankCalculatorD){
		sprintf( BankNum[0], "%9d", wI->sw[no].status );                    //MLHIDE
		sprintf( BankNum[1], "%9d", bank[0].gold - FirstBankMoney );        //MLHIDE
	}
	if (no==EnumBankBankCalculatorW){
		sprintf( BankNum[1], "%9d", wI->sw[no].status );                    //MLHIDE
		sprintf( BankNum[0], "%9d", FirstBankMoney - bank[0].gold );        //MLHIDE
	}

	return FALSE;
}

//------------------------------------------------------------------------//
// ウィンドウオープンアニメーション(银行ウィンドウ用)					  //
//------------------------------------------------------------------------//
void openBankWindowAnim( ACTION *ptAct )
{
	WIN_DISP *ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	StockBoxDispBuffer( ptWinDisp->cx - ptWinDisp->nx,
						ptWinDisp->cy - ptWinDisp->ny,
						ptWinDisp->cx + ptWinDisp->nx,
						ptWinDisp->cy + ptWinDisp->ny,
						DISP_PRIO_MENU, SYSTEM_PAL_BLACK, 0 );

	// 增分プラス
	ptWinDisp->nx += ptAct->dx;
	ptWinDisp->ny += ptAct->dy;

	// リミットチェック
	if( ptWinDisp->cnt >= WINDOW_CREATE_FRAME ){
		createBankWindow( ptWinDisp->type, ptAct->gx, ptAct->gy );
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
		Lock3DChangeWindowCnt--;
#endif
		DeathAction( ptAct );
	}

	// カウンタープラス
	ptWinDisp->cnt++;
}

ACTION *openBankMenuWindow( int WinType, int SeqNo, int ObjIndex, unsigned char flg, char opentype )
{
	ACTION *ptAct;
	WIN_DISP *ptWinDisp;
	int x, y, w, h;

	// ウィンドウ开く音
	play_se( SE_NO_OPEN_WINDOW, 320, 240 );

	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ), ACT_T_WIN_DISP );
#else
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ) );
#endif

	// アクション取得に失败したら終わる
	if( ptAct == NULL ) {
		createBankWindow( WinType, SeqNo, ObjIndex );
		return NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	Lock3DChangeWindowCnt++;
#endif

	// ウィンドウの场所、サイズを取得
	x = WindowData[ MENU_WINDOW_BANK ]->wx,	y = WindowData[ MENU_WINDOW_BANK ]->wy;
	w = WindowData[ MENU_WINDOW_BANK ]->w,	h = WindowData[ MENU_WINDOW_BANK ]->h;

	// 以前开いたときの状态を、作成されたウィンドウに反映
	if( WindowFlag[ MENU_WINDOW_BANK ].flag & 0x01 ){
		x = WindowFlag[ MENU_WINDOW_BANK ].wx;
		y = WindowFlag[ MENU_WINDOW_BANK ].wy;
	}

	// 予备构造体へのポインタ
	ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	// 实行关数
	ptAct->func = openBankWindowAnim;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_WIN2;
	// 当たり判定する
	ptAct->atr |= ACT_ATR_HIT;
	// 表示しない
	ptAct->atr |= ACT_ATR_HIDE;

	ptWinDisp->graNo = 0;
	ptWinDisp->hitFlag = 0;
	if( flg & OPENMENUWINDOW_HIT ) ptWinDisp->hitFlag = 1;

	// 座标を记忆
	ptAct->x = x,	ptAct->y = y;
	ptWinDisp->w = w,	ptWinDisp->h = h;

	// ウィンドウ种类
	ptWinDisp->type = WinType;

	// サーバ关系情报保存
	ptAct->gx = SeqNo;
	ptAct->gy = ObjIndex;

	// カウンタ
	ptWinDisp->cnt = 0;

	// ＮＲバージョンエフェクト
	if( opentype == 0 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = 0;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;

		ptWinDisp->nx = ptWinDisp->cx - x;
	}
	// ＳＡバージョンエフェクト
	if( opentype == 1 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = (ptWinDisp->cx - x) / WINDOW_CREATE_FRAME;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;
	}

	return ptAct;
}

