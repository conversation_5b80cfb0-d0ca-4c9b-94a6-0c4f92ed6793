﻿/***************************************
			newpc.cpp
***************************************/

#ifdef PUK2

#include "anim_tbl_PUK2.h"


// PCキャラに怪我情报を出す
void setPcHealth( void )
{
	pc.status2 |= CHR_STATUS2_HEALTH;
}

// PCキャラに怪我情报を消す
void delPcHealth( void )
{
	pc.status2  &= (~CHR_STATUS2_HEALTH);
}

// PCキャラをスキル使用中にする
void setPcSkill( int headNo )
{
	pc.status2 |= CHR_STATUS2_SKILL;
	pc.headNo = headNo;
}

// PCキャラをスキル使用中から元に戾す
void delPcSkill( void )
{
	pc.status2 &= (~CHR_STATUS2_SKILL);
	pc.headNo = 0;
}

// PCキャラに等级アップマークを表示
void setPcLevelup( void )
{
	pc.status2 |= CHR_STATUS2_LEVELUP | CHR_STATUS2_LEVELUPSE;
}

// PCキャラペットに怪我情报を出す
void setPcPetHealth( void )
{
	pc.status2 |= CHR_STATUS2_PETHEALTH;
}

// PCキャラペットに怪我情报を消す
void delPcPetHealth( void )
{
	pc.status2  &= (~CHR_STATUS2_PETHEALTH);
}

// PCキャラペットに等级アップマークを表示
void setPcPetLevelup( void )
{
	pc.status2 |= CHR_STATUS2_PETLEVELUP | CHR_STATUS2_PETLEVELUPSE;
}

extern int headIconTbl[];

// icon のアイコンを头に表示する
void SetHeadIcon( int icon )
{
	// 现在アイコンが出てないか、违うアイコンが出てるなら
	if ( !( pc.status2 & CHR_STATUS2_SKILL ) ||
		 pc.headNo != icon ){
		// プロトコル送信
		nrproto_ACS_send( sockfd, nextMapGx, nextMapGy, icon );
//		nrproto_ACS_send( sockfd, mapGx, mapGy, icon );
		pc.status2 |= CHR_STATUS2_SKILL;
		if ( icon == HEADICON_DELETE ) pc.status2 &= ~CHR_STATUS2_SKILL;
		pc.headNo = icon;
	}
}

// 现在出てる头の上のアイコン取得
int GetHeadIcon()
{
	if ( pc.status2 & CHR_STATUS2_SKILL ) return pc.headNo;
	return 0;
}
#ifdef PUK3_VEHICLE

// PCキャラを不可视にする
void setPcInvisible( void )
{
	pc.status2 |= CHR_STATUS2_INVISIBLE;
}

// PCキャラを不可视から元に戾す
void delPcInvisible( void )
{
	pc.status2  &= (~CHR_STATUS2_INVISIBLE);
}

#endif

//------------------------------------//

//************************************************
// PUK2モンタージュ → リニューアルモンタージュ??PUKモンタージュ
// 变换
//int graNo 颜番号
//
//戾り值：旧モンタージュ番号
//************************************************
int getFaceGraphicFormPUK2(int graNo)
{
	// １からのキャラのモンタージュを变换
	if ( (PUK2_FACE_01<=graNo) && (graNo<PUK2_FACE_15) ) return(graNo-37200);
	// PUKからのキャラのモンタージュを变换
	{
		// 番号がばらばらなので、テーブルを使う
		const long PUK_numtbl[14]={
			V2_FACE_0, V2_FACE_1, V2_FACE_2, V2_FACE_3, V2_FACE_4, V2_FACE_5, V2_FACE_6,
			V2_FACE_7, V2_FACE_8, V2_FACE_9, V2_FACE_10, V2_FACE_11, V2_FACE_12, V2_FACE_13
		};
		return( PUK_numtbl[(graNo-241400)/100]+(graNo%100) );
	}

	// 上の以外はそのまま返す
	return(graNo);
}

//************************************************
// リニューアルモンタージュ??PUKモンタージュ → PUK2モンタージュ
// 变换
//int graNo 颜番号
//
//戾り值：リニューアルモンタージュ番号
//************************************************
int getFaceGraphicToPUK2(int graNo)
{
	// １からのキャラのモンタージュを变换
	if ( (RN_FACE_0<=graNo) && (graNo<=RN_FACE_13+100) ) return(graNo+37200);
	// PUKからのキャラのモンタージュを变换
	{
		// 番号がばらばらなので、テーブルを使う
		// 实际の值を１００で割った值がテーブルに入っている
		const long PUK_numtbl[14]={
			2000, 2002, 2008, 2010, 2012, 2014, 2016,
			2004, 2006, 2018, 2020, 2022, 2024, 2026
		};
		int i;
		int xgraNo=graNo/100;
		for(i=0;i<14;i++){
			if (xgraNo==PUK_numtbl[i]){
				return( 241400+(i*100)+(graNo%100) );
			}
		}
	}

	// 上の以外はそのまま返す
	return(graNo);
}

#ifdef PUK3
	#include "../../puk3/character/ride_pc.cpp"
#endif

#endif
