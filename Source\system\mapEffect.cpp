﻿#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include<direct.h>
#include<math.h>

#include"../systeminc/system.h"
#include"../systeminc/map.h"
#include"../systeminc/anim_tbl.h"
#include"../systeminc/pc.h"
#include"../systeminc/netproc.h"
#include"../systeminc/character.h"
#include"../systeminc/loadrealbin.h"
#include"../systeminc/loadsprbin.h"
#include"../systeminc/netmain.h"
#include"../systeminc/nrproto_cli.h"
#include"../systeminc/battleProc.h"
#include"../systeminc/t_music.h"
#include"../systeminc/field.h"
#include"../systeminc/login.h"
#include"../systeminc/menu.h"
#include"../systeminc/battleMenu.h"
#include"../systeminc/handletime.h"
#include"../systeminc/directDraw.h"
#include"../systeminc/main.h"
#include"../systeminc/gamemain.h"
#include"../systeminc/sprmgr.h"
#include"../systeminc/mouse.h"
#include"../systeminc/process.h"
#include"../systeminc/math2.h"
#include"../systeminc/action.h"
#include"../systeminc/sprdisp.h"
#include"../systeminc/chat.h"
#include"../systeminc/font.h"
#include"../systeminc/mapGridCursol.h"
#include"../systeminc/mapEffect.h"
#ifdef PUK3_WHALE_SHIP
	#include"../systeminc/pattern.h"
	#include"../puk2/newDraw/anim_tbl_PUK2.h"
#endif


MAP_EFFECT masterBufMapEffect[MAX_MAP_EFFECT_BUF];
MAP_EFFECT *emptyBufMapEffect;
MAP_EFFECT *useBufMapEffect;

short mapEffectDrawFlag = 0;

short mapEffectRainLevel = 0;
short oldMapEffectRainLevel = 0;
short mapEffectRainCnt = 0;

short mapEffectSnowLevel = 0;
short oldMapEffectSnowLevel = 0;
short mapEffectSnowCnt = 0;

short mapEffectMoveDir = -1;

short mapEffectStarFlag = 0;
short mapEffectFallingStarFlag = 0;
int mapEffectFallingStarTime;

short mapEffectKamiFubukiLevel = 0;
short mapEffectKamiFubukiCnt = 0;

#ifdef PUK3_WHALE_SHIP
	short mapEffectCloudLevel = 0;
	short mapEffectCloud2Level = 0;
	short mapEffectCloudCnt = 0;
	float mapEffectCloudSpeed = 5;
	short mapEffectCloudAngle = 315;
	float mapEffectCloudMoveX = 0, mapEffectCloudMoveY = 0;

	short mapEffectMonsCnt = -1;
	short mapEffectMonsPoint = 0;
	short mapEffectMonsAng = 0;
	static ACTION *pActMons = NULL;
#endif

#ifdef PUK2_ACID_PAPER_SNOWSTORM
	short battleEffectRainLevel = 0;
	short battleEffectSnowLevel = 0;
	short battleEffectKamiFubukiLevel = 0;
	short battleEffectCloudLevel = 0;
#endif


#ifdef PUK3_WHALE_SHIP

// effect エフェクトの种类フラグ、level エフェクトの强さ、option 未使用
void setMapEffect( int effect, int level, char *option )
{
	// 全てのエフェクトを止める
	if( effect == 0 )
	{
		mapEffectCloudLevel = 0;
		mapEffectCloud2Level = 0;
		mapEffectMonsCnt = -1;
		// モンスいるなら
		if ( pActMons ){
			MAP_EFFECT *buf;
			DeathAction( pActMons );
			pActMons = NULL;
			// 普通の云に变える
			buf = useBufMapEffect;
			while( buf != (MAP_EFFECT *)NULL ){
				if( buf->type == MAP_EFFECT_TYPE_MONSTER ){
					buf->type = MAP_EFFECT_TYPE_CLOUD;
					break;
				}
				buf = buf->next;
			}
		}
		return;
	}
	// 云を流す
	if ( effect & (1<<3) ){
		mapEffectCloudLevel = level % 10;
		mapEffectCloudAngle = (level % 10000) / 10;
		mapEffectCloudSpeed = (float)( level / 10000 );
	}
	// 云を流す
	if ( effect & (1<<4) ){
		mapEffectCloud2Level = level % 10;
		mapEffectCloudAngle = (level % 10000) / 10;
		mapEffectCloudSpeed = (float)( level / 10000 );
	}
	// モンスターを流す
	if ( effect & (1<<5) ){
		// モンスいるなら
		if ( pActMons ){
			MAP_EFFECT *buf;
			DeathAction( pActMons );
			pActMons = NULL;
			// 普通の云に变える
			buf = useBufMapEffect;
			while( buf != (MAP_EFFECT *)NULL ){
				if( buf->type == MAP_EFFECT_TYPE_MONSTER ){
					buf->type = MAP_EFFECT_TYPE_CLOUD;
					break;
				}
				buf = buf->next;
			}
		}
		// 等级の值が-1なら流すのやめる
		if ( level == -1 ){
			mapEffectMonsCnt = -1;
		}else{
			mapEffectMonsCnt = (level / 100000) * 60;
			mapEffectMonsAng = ( (level / 10000) % 10 ) % 8;
			mapEffectMonsPoint  = level % 10000;
		}
	}
}

#endif

// マップエフェクトの初期化
void initMapEffect( void )
{
	int i;

	emptyBufMapEffect = &masterBufMapEffect[0];
	masterBufMapEffect[0].pre = (MAP_EFFECT *)NULL;
	for( i = 1; i < MAX_MAP_EFFECT_BUF; i++ )
	{
		masterBufMapEffect[i-1].next = &masterBufMapEffect[i];
		masterBufMapEffect[i].pre = &masterBufMapEffect[i-1];
	}
	masterBufMapEffect[i-1].next = (MAP_EFFECT *)NULL;

	useBufMapEffect = (MAP_EFFECT *)NULL;

	mapEffectRainLevel = 0;
	mapEffectRainCnt = 0;

	mapEffectSnowLevel = 0;
	mapEffectSnowCnt = 0;

	mapEffectMoveDir = -1;

	mapEffectStarFlag = 0;
	mapEffectFallingStarFlag = 1;

	mapEffectKamiFubukiLevel = 0;
	mapEffectKamiFubukiCnt = 0;
#ifdef PUK3_WHALE_SHIP
	mapEffectCloudLevel = 0;
	mapEffectCloud2Level = 0;
	mapEffectCloudCnt = 0;
	mapEffectCloudMoveX = 0;
	mapEffectCloudMoveY = 0;
#endif
}


// エフェクトバッファを取得し表示设定をする
MAP_EFFECT *getMapEffectBuf( void )
{
	MAP_EFFECT *buf;

	// バッファが无いのでNULLを返す
	if( emptyBufMapEffect == (MAP_EFFECT *)NULL )
		return (MAP_EFFECT *)NULL;

	buf = emptyBufMapEffect;
	emptyBufMapEffect = emptyBufMapEffect->next;
	if( emptyBufMapEffect != (MAP_EFFECT *)NULL )
	{
		emptyBufMapEffect->pre = (MAP_EFFECT *)NULL;
	}

	buf->next = useBufMapEffect;
	buf->pre  = (MAP_EFFECT *)NULL;
	if( useBufMapEffect != (MAP_EFFECT *)NULL )
	{
		useBufMapEffect->pre = buf;
	}
	useBufMapEffect = buf;

	return buf;
}


// エフェクトバッファを开放する
void delMapEffectBuf( MAP_EFFECT *buf )
{
	if( buf == (MAP_EFFECT *)NULL )
		return;


	if( buf->pre != (MAP_EFFECT *)NULL )
	{
		buf->pre->next = buf->next;
	}
	if( buf->next != (MAP_EFFECT *)NULL )
	{
		buf->next->pre = buf->pre;
	}
	if( useBufMapEffect == buf )
	{
		useBufMapEffect = buf->next;
	}


	buf->next = emptyBufMapEffect;
	buf->pre = (MAP_EFFECT *)NULL;
	emptyBufMapEffect = buf;
	buf->type = MAP_EFFECT_TYPE_NONE;
}


// 移动による补正		//01/07/19 oft
void mapEffectDirHosei( int *x, int *y )
{
	switch( mapEffectMoveDir ){
	case 0:		//左上
		(*x)++;
		(*y)++;
		break;

	case 1:		//上
		(*y) += 2;
		break;

	case 2:		//右上
		(*x)--;
		(*y)++;
		break;

	case 3:		//右
		(*x) -= 2;
		break;

	case 4:		//右下
		(*x)--;
		(*y)--;
		break;

	case 5:		//下
		(*y) -= 2;
		break;

	case 6:		//左下
		(*x)++;
		(*y)--;
		break;

	case 7:		//左
		(*x) += 2;
		break;
	}
}

// sprdisp.cpp内のPutBmp()で呼ばれる
void drawMapEffect( void )
{
	if( mapEffectDrawFlag )
	{
		DrawMapEffect();
		mapEffectDrawFlag = 0;
	}
}


// 各エフェクトの计算
void mapEffectProc( void )
{
	mapEffectRain();
	mapEffectSnow();
	mapEffectStar();
	mapEffectFallingStar();
#if 1
	mapEffectKamiFubuki();
#endif
#ifdef PUK3_WHALE_SHIP
	mapEffectCloud();
#endif
}


void mapEffectProc2( int n )
{
	mapEffectRain2( n );
	mapEffectSnow2( n );
}


// 雨のエフェクト
void mapEffectRain( void )
{
	short mapEffectRainWaitTime = 0;
	short mapEffectRainFrameMax = 0;
	int hosei1 = 840;
	int hosei2 = -200;
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	static unsigned int time = GetTickCount();
	MAP_EFFECT *buf;
	int i;
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	short RainLevel;

	if ( ProcNo != PROC_BATTLE ){
		RainLevel = mapEffectRainLevel;
	}else{
		RainLevel = battleEffectRainLevel;
	}
	
	// 雨発生
	if( RainLevel )
	{
		if( RainLevel < 3 )
		{
			mapEffectRainWaitTime = (51 - 25*RainLevel);
#else
	
	// 雨発生
	if( mapEffectRainLevel )
	{
		if( mapEffectRainLevel < 3 )
		{
			mapEffectRainWaitTime = (51 - 25*mapEffectRainLevel);
#endif
			if( time+mapEffectRainWaitTime < GetTickCount() )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_RAIN;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->dx = 2+(rand() % 2);
					buf->dy = 9;
					time = GetTickCount();
					mapEffectRainCnt++;
					buf->y = -buf->dy;		//01/07/19 oft
				}
			}
		}
		else
		{
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			mapEffectRainFrameMax = (RainLevel-2)*4;
#else
			mapEffectRainFrameMax = (mapEffectRainLevel-2)*4;
#endif
			
			for( i = 0; i < mapEffectRainFrameMax; i++ )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_RAIN;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->dx = 2+(rand() % 2);
					buf->dy = 9;
					time = GetTickCount();
					mapEffectRainCnt++;
					buf->y = -buf->dy;		//01/07/19 oft
				}
			}
		}
	}


	// 移动处理
	i = 0;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( buf->type == MAP_EFFECT_TYPE_RAIN )
		{
			buf->x += buf->dx;
			buf->y += buf->dy;
			mapEffectDirHosei( &buf->x, &buf->y );
			if( ww <= buf->x || hh <= buf->y )
			{
				// 移动終了
				MAP_EFFECT *buf2 = buf->next;
				delMapEffectBuf( buf );
				buf = buf2;
				mapEffectRainCnt--;
				continue;
			}
		}
		buf = buf->next;
		mapEffectDrawFlag = 1;
		i++;
	}

#if 0
	{
	char msg[256];
	sprintf( msg, "mapEffectRainCnt = %d / %d", mapEffectRainCnt, i );   //MLHIDE
	StockFontBuffer( 240, 54, FONT_PRIO_FRONT, 0, msg, 0 );
	}
#endif
}


// 雪のエフェクト
void mapEffectSnow( void )
{
	short mapEffectSnowWaitTime = 0;
	short mapEffectSnowFrameMax = 0;
	int hosei1 = 940;
	int hosei2 = -200;
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	static unsigned int time = GetTickCount();
	MAP_EFFECT *buf;
	int i, j;
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	short SnowLevel;

	if ( ProcNo != PROC_BATTLE ){
		SnowLevel = mapEffectSnowLevel;
	}else{
		SnowLevel = battleEffectSnowLevel;
	}
	
	// 雪発生
	if( SnowLevel )
	{
		if( SnowLevel < 3 )
		{
			mapEffectSnowWaitTime = (51 - 25*SnowLevel);
#else

	// 雪発生
	if( mapEffectSnowLevel )
	{
		if( mapEffectSnowLevel < 3 )
		{
			mapEffectSnowWaitTime = (51 - 25*mapEffectSnowLevel);
#endif
			if( time+mapEffectSnowWaitTime < GetTickCount() )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_SNOW;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->mode = (rand() % 2);
					j = (rand() % 2);
					if( j == 0 )
					{
						buf->dx = 0+(rand() % 2);
						buf->dy = 3+(rand() % 2);
					}
					else
					if( j == 1 )
					{
						buf->dx = 1;
						buf->dy = 4+(rand() % 2);
					}
					time = GetTickCount();
					mapEffectSnowCnt++;
					buf->y = -buf->dy;		//01/07/19 oft
				}
			}
		}
		else
		{
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			mapEffectSnowFrameMax = (SnowLevel-2)*2;
#else
			mapEffectSnowFrameMax = (mapEffectSnowLevel-2)*2;
#endif
			
			for( i = 0; i < mapEffectSnowFrameMax; i++ )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_SNOW;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->mode = (rand() % 2);
					j = (rand() % 2);
					if( j == 0 )
					{
						buf->dx = 0+(rand() % 2);
						buf->dy = 3+(rand() % 2);
					}
					else
					if( j == 1 )
					{
						buf->dx = 1;
						buf->dy = 4+(rand() % 2);
					}
					time = GetTickCount();
					mapEffectSnowCnt++;
					buf->y = -buf->dy;		//01/07/19 oft
				}
			}
		}
	}


	// 移动处理
	i = 0;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( buf->type == MAP_EFFECT_TYPE_SNOW )
		{
			buf->x += buf->dx;
			buf->y += buf->dy;
			mapEffectDirHosei( &buf->x, &buf->y );
			if( ww+100 <= buf->x || hh <= buf->y )
			{
				// 移动終了
				MAP_EFFECT *buf2 = buf->next;
				delMapEffectBuf( buf );
				buf = buf2;
				mapEffectSnowCnt--;
				continue;
			}
		}
		buf = buf->next;
		mapEffectDrawFlag = 1;
		i++;
	}

#if 0
	{
	char msg[256];
	sprintf( msg, "mapEffectSnowCnt = %d / %d", mapEffectSnowCnt, i );   //MLHIDE
	StockFontBuffer( 240, 54, FONT_PRIO_FRONT, 0, msg, 0 );
	}
#endif
}


// 星のエフェクト（地域限定）

void setEffectStar( MAP_EFFECT *buf, int gx, int gy, int ggx, int ggy, int type )
{
	if( buf )
	{
		buf->type = MAP_EFFECT_TYPE_STAR;
		buf->gx = gx;
		buf->gy = gy;
		buf->ggx = ggx;
		buf->ggy = ggy;
		buf->type2 = type;
	}
}

void calEffectStar( MAP_EFFECT *buf )
{
	float mx, my;

	// 画面表示位置
	camMapToGamen( (float)buf->gx*GRID_SIZE, (float)buf->gy*GRID_SIZE, &mx, &my );
	buf->x = (int)(mx+.5) + buf->ggx;
	buf->y = (int)(my+.5) + buf->ggy;
}


void mapEffectStar( void )
{
#if 0
	static MAP_EFFECT *buf[30];
	int i;

	if( (mapNo == 1200 || mapNo == 20105 || mapNo == 10920 || mapNo == 20406)
	 && NOON_TO_EVENING+10 <= nrTime.hour && nrTime.hour < NIGHT_TO_MORNING+80 )
	{
		if( mapNo == 1200 && mapEffectStarFlag == 0 )
		{
			for( i = 0; i < sizeof( buf )/sizeof( void *); i++ )
			{
				buf[i] = NULL;
			}
			buf[0] = getMapEffectBuf();
			setEffectStar( buf[0], 62, 28,   0,   0, 0 );
			buf[1] = getMapEffectBuf();
			setEffectStar( buf[1], 63, 27,  -5,  -5, 1 );
			buf[2] = getMapEffectBuf();
			setEffectStar( buf[2], 63, 28,  16, -10, 0 );
			buf[3] = getMapEffectBuf();
			setEffectStar( buf[3], 63, 29,  10,   5, 1 );
			buf[4] = getMapEffectBuf();
			setEffectStar( buf[4], 64, 29,   0,  -8, 1 );

			buf[5] = getMapEffectBuf();
			setEffectStar( buf[5], 65, 29,  -8,   5, 0 );
			buf[6] = getMapEffectBuf();
			setEffectStar( buf[6], 65, 30,  16,  10, 0 );
			buf[7] = getMapEffectBuf();
			setEffectStar( buf[7], 66, 31,   0,   0, 1 );
			buf[8] = getMapEffectBuf();
			setEffectStar( buf[8], 66, 30,   8, -12, 1 );
			buf[9] = getMapEffectBuf();
			setEffectStar( buf[9], 67, 31,  20,   0, 0 );

			buf[10] = getMapEffectBuf();
			setEffectStar( buf[10], 69, 34,  -8,  -8, 0 );
			buf[11] = getMapEffectBuf();
			setEffectStar( buf[11], 70, 34,   5,  10, 1 );
			buf[12] = getMapEffectBuf();
			setEffectStar( buf[12], 70, 35,   2,  24, 0 );
			buf[13] = getMapEffectBuf();
			setEffectStar( buf[13], 70, 36, -20, -16, 1 );
			buf[14] = getMapEffectBuf();
			setEffectStar( buf[14], 71, 36,   2,   4, 1 );

			buf[15] = getMapEffectBuf();
			setEffectStar( buf[15], 71, 37,   5, -10, 1 );
			buf[16] = getMapEffectBuf();
			setEffectStar( buf[16], 72, 37,   0,  12, 0 );


			mapEffectStarFlag = 1;
		}
		else
		if( mapNo == 20105 && mapEffectStarFlag == 0 )
		{
			for( i = 0; i < sizeof( buf )/sizeof( void *); i++ )
			{
				buf[i] = NULL;
			}
			buf[0] = getMapEffectBuf();
			setEffectStar( buf[0], 21,  8,   5,   0, 1 );
			buf[1] = getMapEffectBuf();
			setEffectStar( buf[1], 20, 10,  -5,   5, 1 );
			buf[2] = getMapEffectBuf();
			setEffectStar( buf[2], 18, 11,   5,  10, 0 );
			buf[3] = getMapEffectBuf();
			setEffectStar( buf[3], 20, 12,   0,  -5, 1 );
			buf[4] = getMapEffectBuf();
			setEffectStar( buf[4], 23, 11,   0,   0, 0 );

			buf[5] = getMapEffectBuf();
			setEffectStar( buf[5], 23, 14,   0,   0, 0 );
			buf[6] = getMapEffectBuf();
			setEffectStar( buf[6], 25, 13,   8,   6, 1 );
			buf[7] = getMapEffectBuf();
			setEffectStar( buf[7], 25, 16,   0,   0, 1 );
			buf[8] = getMapEffectBuf();
			setEffectStar( buf[8], 26, 18,   4,  -8, 0 );
			buf[9] = getMapEffectBuf();
			setEffectStar( buf[9], 28, 16, -16,   0, 0 );

			buf[10] = getMapEffectBuf();
			setEffectStar( buf[10], 21, 14,  -5,   0, 1 );
			buf[11] = getMapEffectBuf();
			setEffectStar( buf[11], 27, 14,   0,   0, 1 );
			buf[12] = getMapEffectBuf();
			setEffectStar( buf[12], 24, 15,   4, -10, 1 );
			buf[13] = getMapEffectBuf();
			setEffectStar( buf[13], 28, 12,   5,  -8, 0 );
			buf[14] = getMapEffectBuf();
			setEffectStar( buf[14], 32, 14,   0,   0, 0 );

			buf[15] = getMapEffectBuf();
			setEffectStar( buf[15], 30, 17,   0,   0, 0 );
			buf[16] = getMapEffectBuf();
			setEffectStar( buf[16], 32, 19,   0,   0, 1 );
			buf[17] = getMapEffectBuf();
			setEffectStar( buf[17], 36, 19,   5,   2, 1 );
			buf[18] = getMapEffectBuf();
			setEffectStar( buf[18], 34, 22,  -4,   4, 0 );
			buf[19] = getMapEffectBuf();
			setEffectStar( buf[19], 36, 25,   1,   1, 0 );

			buf[20] = getMapEffectBuf();
			setEffectStar( buf[20], 39, 23,   0,   0, 0 );
			buf[21] = getMapEffectBuf();
			setEffectStar( buf[21], 38, 25,  -8,   2, 1 );
			buf[22] = getMapEffectBuf();
			setEffectStar( buf[22], 40, 24,  -5,  -5, 1 );
			buf[23] = getMapEffectBuf();
			setEffectStar( buf[23], 39, 27,   0,   0, 0 );
			buf[24] = getMapEffectBuf();
			setEffectStar( buf[24], 39, 30,   0,   0, 1 );

			buf[25] = getMapEffectBuf();
			setEffectStar( buf[25], 43, 29,   0,   0, 0 );
			buf[26] = getMapEffectBuf();
			setEffectStar( buf[26], 42, 32,   2,   0, 0 );
			buf[27] = getMapEffectBuf();
			setEffectStar( buf[27], 44, 33,   0,   0, 1 );
			buf[28] = getMapEffectBuf();
			setEffectStar( buf[28], 43, 34,   8,   2, 0 );
			buf[29] = getMapEffectBuf();
			setEffectStar( buf[29], 45, 36,   0,   0, 0 );


			mapEffectStarFlag = 1;
		}
		else
		if( mapNo == 10920 && mapEffectStarFlag == 0 )
		{
			for( i = 0; i < sizeof( buf )/sizeof( void *); i++ )
			{
				buf[i] = NULL;
			}
			buf[0] = getMapEffectBuf();
			setEffectStar( buf[0], 71,  2,   0,   0, 1 );
			buf[1] = getMapEffectBuf();
			setEffectStar( buf[1], 71,  5,   0,   0, 0 );
			buf[2] = getMapEffectBuf();
			setEffectStar( buf[2], 70,  7,   0,   0, 1 );
			buf[3] = getMapEffectBuf();
			setEffectStar( buf[3], 73,  7,   0,   0, 1 );
			buf[4] = getMapEffectBuf();
			setEffectStar( buf[4], 75,  6,   0,   0, 0 );

			buf[5] = getMapEffectBuf();
			setEffectStar( buf[5], 75,  9,   0,   0, 0 );
			buf[6] = getMapEffectBuf();
			setEffectStar( buf[6], 75, 11,   0,   0, 1 );
			buf[7] = getMapEffectBuf();
			setEffectStar( buf[7], 77,  9,   0,   0, 1 );
			buf[8] = getMapEffectBuf();
			setEffectStar( buf[8], 76, 13,   0,   0, 0 );
			buf[9] = getMapEffectBuf();
			setEffectStar( buf[9], 79, 12,   0,   0, 0 );

			buf[10] = getMapEffectBuf();
			setEffectStar( buf[10], 78, 15,   0,   0, 1 );
			buf[11] = getMapEffectBuf();
			setEffectStar( buf[11], 80, 14,   0,   0, 0 );
			buf[12] = getMapEffectBuf();
			setEffectStar( buf[12], 79, 16,   0,   0, 1 );
			buf[13] = getMapEffectBuf();
			setEffectStar( buf[13], 80, 18,   0,   0, 0 );
			buf[14] = getMapEffectBuf();
			setEffectStar( buf[14], 83, 18,   0,   0, 0 );

			buf[15] = getMapEffectBuf();
			setEffectStar( buf[15], 83, 20,   0,   0, 1 );
			buf[16] = getMapEffectBuf();
			setEffectStar( buf[16], 84, 20,   0,   0, 1 );
			buf[17] = getMapEffectBuf();
			setEffectStar( buf[17], 84, 22,   0,   0, 0 );
			buf[18] = getMapEffectBuf();
			setEffectStar( buf[18], 85, 19,   0,   0, 0 );
			buf[19] = getMapEffectBuf();
			setEffectStar( buf[19], 87, 20,   0,   0, 1 );

			buf[20] = getMapEffectBuf();
			setEffectStar( buf[20], 89, 21,   0,   0, 0 );
			buf[21] = getMapEffectBuf();
			setEffectStar( buf[21], 89, 23,   0,   0, 1 );
			buf[22] = getMapEffectBuf();
			setEffectStar( buf[22], 89, 26,   0,   0, 1 );
			buf[23] = getMapEffectBuf();
			setEffectStar( buf[23], 91, 25,   0,   0, 0 );
			buf[24] = getMapEffectBuf();
			setEffectStar( buf[24], 91, 27,   0,   0, 1 );

			buf[25] = getMapEffectBuf();
			setEffectStar( buf[25], 93, 26,   0,   0, 1 );
			buf[26] = getMapEffectBuf();
			setEffectStar( buf[26], 93, 30,   0,   0, 0 );

			mapEffectStarFlag = 1;
		}
		else
		if( mapNo == 20406 && mapEffectStarFlag == 0 )
		{
			for( i = 0; i < sizeof( buf )/sizeof( void *); i++ )
			{
				buf[i] = NULL;
			}
			buf[0] = getMapEffectBuf();
			setEffectStar( buf[0], 16,  8,   0,   0, 1 );
			buf[1] = getMapEffectBuf();
			setEffectStar( buf[1], 16, 10,   0,   0, 0 );
			buf[2] = getMapEffectBuf();
			setEffectStar( buf[2], 18, 10,   0,   0, 0 );
			buf[3] = getMapEffectBuf();
			setEffectStar( buf[3], 19, 12,   0,   0, 1 );
			buf[4] = getMapEffectBuf();
			setEffectStar( buf[4], 20, 12,   0,   0, 1 );

			buf[5] = getMapEffectBuf();
			setEffectStar( buf[5], 21, 14,   0,   0, 0 );
			buf[6] = getMapEffectBuf();
			setEffectStar( buf[6], 22, 16,   0,   0, 0 );
			buf[7] = getMapEffectBuf();
			setEffectStar( buf[7], 23, 16,   0,   0, 1 );
			buf[8] = getMapEffectBuf();
			setEffectStar( buf[8], 22, 11,   0,   0, 0 );
			buf[9] = getMapEffectBuf();
			setEffectStar( buf[9], 26, 15,   0,   0, 0 );

			buf[10] = getMapEffectBuf();
			setEffectStar( buf[10], 25, 18,   0,   0, 1 );
			buf[11] = getMapEffectBuf();
			setEffectStar( buf[11], 26, 17,   0,   0, 1 );
			buf[12] = getMapEffectBuf();
			setEffectStar( buf[12], 27, 13,   0,   0, 0 );
			buf[13] = getMapEffectBuf();
			setEffectStar( buf[13], 29, 14,   0,   0, 1 );
			buf[14] = getMapEffectBuf();
			setEffectStar( buf[14], 29, 12,   0,   0, 1 );

			buf[15] = getMapEffectBuf();
			setEffectStar( buf[15], 31, 17,   0,   0, 0 );
			buf[16] = getMapEffectBuf();
			setEffectStar( buf[16], 31, 19,   0,   0, 1 );
			buf[17] = getMapEffectBuf();
			setEffectStar( buf[17], 34, 18,   0,   0, 1 );
			buf[18] = getMapEffectBuf();
			setEffectStar( buf[18], 33, 21,   0,   0, 1 );
			buf[19] = getMapEffectBuf();
			setEffectStar( buf[19], 34, 23,   0,   0, 0 );

			buf[20] = getMapEffectBuf();
			setEffectStar( buf[20], 38, 26,   0,   0, 0 );
			buf[21] = getMapEffectBuf();
			setEffectStar( buf[21], 41, 25,   0,   0, 1 );
			buf[22] = getMapEffectBuf();
			setEffectStar( buf[22], 41, 26,   0,   0, 1 );
			buf[23] = getMapEffectBuf();
			setEffectStar( buf[23], 41, 32,   0,   0, 0 );
			buf[24] = getMapEffectBuf();
			setEffectStar( buf[24], 44, 31,   0,   0, 0 );

			buf[25] = getMapEffectBuf();
			setEffectStar( buf[25], 44, 33,   0,   0, 0 );
			buf[26] = getMapEffectBuf();
			setEffectStar( buf[26], 42, 43,   0,   0, 1 );
			buf[27] = getMapEffectBuf();
			setEffectStar( buf[27], 45, 36,   0,   0, 0 );
			buf[28] = getMapEffectBuf();
			setEffectStar( buf[28], 38, 21,   0,   0, 0 );
			buf[29] = getMapEffectBuf();
			setEffectStar( buf[29], 37, 19,   0,   0, 1 );

			mapEffectStarFlag = 1;
		}


		for( i = 0; i < sizeof( buf )/sizeof( void * ); i++ )
		{
			if( buf[i] )
			{
				calEffectStar( buf[i] );
				// 昼～夕１
				// 夜～朝３
				if( nrTime.hour < NOON_TO_EVENING+20
				 || NIGHT_TO_MORNING+70 < nrTime.hour )
				{
					buf[i]->mode = 5;
				}
				else
				// 昼～夕２
				// 夜～朝２
				if( (NOON_TO_EVENING+20 <= nrTime.hour && nrTime.hour < NOON_TO_EVENING+30)
				 || NIGHT_TO_MORNING+60 < nrTime.hour )
				{
					buf[i]->mode = 6;
				}
				else
				// 昼～夕３
				// 夜～朝１
				if( (NOON_TO_EVENING+30 <= nrTime.hour && nrTime.hour < NOON_TO_EVENING+40)
				 || NIGHT_TO_MORNING+40 < nrTime.hour )
				{
					buf[i]->mode = 8;
				}
				else
				{
					if( buf[i]->type2 == 0 )
					{
						// 昼～夕：少し星が大きくなる
						// 夜～朝：さらに星が小さくなる
						if( nrTime.hour < NOON_TO_EVENING+50
						 || NIGHT_TO_MORNING+30 < nrTime.hour )
						{
							buf[i]->mode = 4;
						}
						else
						// 昼～夕：さらに星が大きくなる
						// 夜～朝：少し星が小さくなる
						if( nrTime.hour < NOON_TO_EVENING+80
						 || NIGHT_TO_MORNING+20 < nrTime.hour )
						{
							buf[i]->mode = 1;
						}
						else
						{
							buf[i]->mode = (rand() % 2);
						}
					}
					else
					if( buf[i]->type2 == 1 )
					{
						buf[i]->mode = 8;
					}
				}
			}
		}
		mapEffectDrawFlag = 1;
	}
	else
	{
		if( mapEffectStarFlag )
		{
			for( i = 0; i < sizeof( buf )/sizeof( void * ); i++ )
			{
				delMapEffectBuf( buf[i] );
				buf[i] = NULL;
			}
			mapEffectStarFlag = 0;
		}
	}
#endif
}


// 流れ星エフェクト
void mapEffectFallingStar( void )
{
#if 0
	static MAP_EFFECT *buf = NULL;

	if( mapEffectFallingStarFlag != 0
	 && (nrTime.hour < EVENING_TO_NIGHT || NIGHT_TO_MORNING < nrTime.hour) )
	{
		mapEffectFallingStarFlag = 0;
		mapEffectFallingStarTime = (rand() % 300)+400;
	}


#ifdef _DEBUG
//	if( (joy_trg[ 0 ] & JOY_RIGHT) && buf == NULL )
	if( !mapEffectFallingStarFlag && mapEffectFallingStarTime == nrTime.hour )
#else
	if( !mapEffectFallingStarFlag && mapEffectFallingStarTime == nrTime.hour )
#endif
	{
		if( mapNo == 10920 )
		{
			buf = getMapEffectBuf();

			if( buf )
			{
				int i = (rand() % 5);

				switch( i )
				{
					case 0:
						setEffectStar( buf, 76, 6, 0, 0, 0 );
						buf->dx = 10;
						buf->dy = 6;
						buf->ey = 120;
						break;

					case 1:
						setEffectStar( buf, 77, 7, 0, 0, 0 );
						buf->dx = 8;
						buf->dy = 7;
						buf->ey = 120;
						break;

					case 2:
						setEffectStar( buf, 80, 13, 0, 0, 0 );
						buf->dx = -10;
						buf->dy = 9;
						buf->ey = 120;
						break;

					case 3:
						setEffectStar( buf, 84, 19, 0, 0, 0 );
						buf->dx = 12;
						buf->dy = 7;
						buf->ey = 90;
						break;

					case 4:
						setEffectStar( buf, 95, 26, 0, 0, 0 );
						buf->dx = -10;
						buf->dy = 7;
						buf->ey = 120;
						break;
				}
				// 星の种类
				if( (rand() % 10) < 6 )
				{
					buf->mode = 8;
				}
				else
				{
					buf->mode = 1;
				}
			}
			mapEffectFallingStarFlag = 1;
		}
		else
		if( mapNo == 20105 )
		{
			buf = getMapEffectBuf();

			if( buf )
			{
				int i = (rand() % 8);
				i = 7;

				switch( i )
				{
					case 0:
						setEffectStar( buf, 22, 9, 0, 0, 0 );
						buf->dx = 10;
						buf->dy = 7;
						buf->ey = 120;
						break;

					case 1:
						setEffectStar( buf, 27, 15, 0, 0, 0 );
						buf->dx = -8;
						buf->dy = 9;
						buf->ey = 120;
						break;

					case 2:
						setEffectStar( buf, 31, 14, 0, 0, 0 );
						buf->dx = -10;
						buf->dy = 6;
						buf->ey = 120;
						break;

					case 3:
						setEffectStar( buf, 33, 18, 0, 0, 0 );
						buf->dx = 12;
						buf->dy = 8;
						buf->ey = 120;
						break;

					case 4:
						setEffectStar( buf, 38, 22, 0, 0, 0 );
						buf->dx = -11;
						buf->dy = 7;
						buf->ey = 120;
						break;

					case 5:
						setEffectStar( buf, 43, 27, 0, 0, 0 );
						buf->dx = -9;
						buf->dy = 6;
						buf->ey = 120;
						break;

					case 6:
						setEffectStar( buf, 41, 29, 0, 0, 0 );
						buf->dx = 12;
						buf->dy = 6;
						buf->ey = 120;
						break;

					case 7:
						setEffectStar( buf, 43, 33, 0, 0, 0 );
						buf->dx = 14;
						buf->dy = 8;
						buf->ey = 80;
						break;
				}
				// 星の种类
				if( (rand() % 10) < 6 )
				{
					buf->mode = 8;
				}
				else
				{
					buf->mode = 1;
				}
			}
			mapEffectFallingStarFlag = 1;
		}
		else
		if( mapNo == 1200 )
		{
			buf = getMapEffectBuf();

			if( buf )
			{
				int i = (rand() % 3);
				i = 2;

				switch( i )
				{
					case 0:
						setEffectStar( buf, 63, 26, 0, 0, 0 );
						buf->dx = 10;
						buf->dy = 7;
						buf->ey = 80;
						break;

					case 1:
						setEffectStar( buf, 66, 30, 0, 0, 0 );
						buf->dx = -10;
						buf->dy = 6;
						buf->ey = 70;
						break;

					case 2:
						setEffectStar( buf, 70, 35, 0, 0, 0 );
						buf->dx = 8;
						buf->dy = 6;
						buf->ey = 40;
						break;
				}
				// 星の种类
				if( (rand() % 10) < 6 )
				{
					buf->mode = 8;
				}
				else
				{
					buf->mode = 1;
				}
			}
			mapEffectFallingStarFlag = 1;
		}
		else
		if( mapNo == 20406 )
		{
			buf = getMapEffectBuf();

			if( buf )
			{
				int i = (rand() % 6);
				i = 5;

				switch( i )
				{
					case 0:
						setEffectStar( buf, 20, 11, 0, 0, 0 );
						buf->dx = -12;
						buf->dy = 9;
						buf->ey = 80;
						break;

					case 1:
						setEffectStar( buf, 23, 11, 0, 0, 0 );
						buf->dx = 10;
						buf->dy = 8;
						buf->ey = 120;
						break;

					case 2:
						setEffectStar( buf, 32, 14, 0, 0, 0 );
						buf->dx = -9;
						buf->dy = 6;
						buf->ey = 120;
						break;

					case 3:
						setEffectStar( buf, 36, 20, 0, 0, 0 );
						buf->dx = -10;
						buf->dy = 9;
						buf->ey = 120;
						break;

					case 4:
						setEffectStar( buf, 41, 24, 0, 0, 0 );
						buf->dx = 11;
						buf->dy = 7;
						buf->ey = 120;
						break;

					case 5:
						setEffectStar( buf, 44, 32, 0, 0, 0 );
						buf->dx = 10;
						buf->dy = 8;
						buf->ey = 120;
						break;
				}
				// 星の种类
				if( (rand() % 10) < 6 )
				{
					buf->mode = 8;
				}
				else
				{
					buf->mode = 1;
				}
			}
			mapEffectFallingStarFlag = 1;
		}
	}
	if( buf )
	{
		buf->ggx += buf->dx;
		buf->ggy += buf->dy;

		if( buf->ggy > buf->ey )
		{
			delMapEffectBuf( buf );
			buf = NULL;
		}
		else
		{
			calEffectStar( buf );
			mapEffectDrawFlag = 1;
		}
	}
#endif
}




// 雪のエフェクト
void mapEffectSnow2( int n )
{
	short mapEffectSnowWaitTime = 0;
	short mapEffectSnowFrameMax = 0;
	int hosei1 = 940;
	int hosei2 = -200;
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	int time = 0;
	MAP_EFFECT *buf;
	int i, j, k;
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	short SnowLevel;

	if ( ProcNo != PROC_BATTLE ){
		SnowLevel = mapEffectSnowLevel;
	}else{
		SnowLevel = battleEffectSnowLevel;
	}

	for( k = 0; k < n; k++ )
	{

	// 雪発生
	if( SnowLevel )
	{
		if( SnowLevel < 3 )
		{
			mapEffectSnowWaitTime = (2-SnowLevel)*2;
#else

	for( k = 0; k < n; k++ )
	{

	// 雪発生
	if( mapEffectSnowLevel )
	{
		if( mapEffectSnowLevel < 3 )
		{
			mapEffectSnowWaitTime = (2-mapEffectSnowLevel)*2;
#endif
			if( time >= mapEffectSnowWaitTime )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_SNOW;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->mode = (rand() % 2);
					j = (rand() % 2);
					if( j == 0 )
					{
						buf->dx = 0+(rand() % 2);
						buf->dy = 3+(rand() % 2);
					}
					else
					if( j == 1 )
					{
						buf->dx = 1;
						buf->dy = 4+(rand() % 2);
					}
					time = 0;
					mapEffectSnowCnt++;
				}
			}
		}
		else
		{
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			mapEffectSnowFrameMax = (SnowLevel-2)*2;
#else
			mapEffectSnowFrameMax = (mapEffectSnowLevel-2)*2;
#endif
			
			for( i = 0; i < mapEffectSnowFrameMax; i++ )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_SNOW;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->mode = (rand() % 2);
					j = (rand() % 2);
					if( j == 0 )
					{
						buf->dx = 0+(rand() % 2);
						buf->dy = 3+(rand() % 2);
					}
					else
					if( j == 1 )
					{
						buf->dx = 1;
						buf->dy = 4+(rand() % 2);
					}
					time = 0;
					mapEffectSnowCnt++;
				}
			}
		}
	}


	// 移动处理
	i = 0;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( buf->type == MAP_EFFECT_TYPE_SNOW )
		{
			buf->x += buf->dx;
			buf->y += buf->dy;
			mapEffectDirHosei( &buf->x, &buf->y );
			if( ww+100 <= buf->x || hh <= buf->y )
			{
				// 移动終了
				MAP_EFFECT *buf2 = buf->next;
				delMapEffectBuf( buf );
				buf = buf2;
				mapEffectSnowCnt--;
				continue;
			}
		}
		buf = buf->next;
		mapEffectDrawFlag = 1;
		i++;
	}

	time++;
	}
}


// 雨のエフェクト
void mapEffectRain2( int n )
{
	short mapEffectRainWaitTime = 0;
	short mapEffectRainFrameMax = 0;
	int hosei1 = 840;
	int hosei2 = -200;
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	int time = 0;
	MAP_EFFECT *buf;
	int i, k;
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	short RainLevel;

	if ( ProcNo != PROC_BATTLE ){
		RainLevel = mapEffectRainLevel;
	}else{
		RainLevel = battleEffectRainLevel;
	}

	for( k = 0; k < n; k++ )
	{

	// 雨発生
	if( RainLevel )
	{
		if( RainLevel < 3 )
		{
			mapEffectRainWaitTime = (2 - RainLevel)*2;
#else

	for( k = 0; k < n; k++ )
	{

	// 雨発生
	if( mapEffectRainLevel )
	{
		if( mapEffectRainLevel < 3 )
		{
			mapEffectRainWaitTime = (2 - mapEffectRainLevel)*2;
#endif
			if( time >= mapEffectRainWaitTime )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_RAIN;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->dx = 2+(rand() % 2);
					buf->dy = 9;
					time = 0;
					mapEffectRainCnt++;
				}
			}
		}
		else
		{
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			mapEffectRainFrameMax = (RainLevel-2)*4;
#else
			mapEffectRainFrameMax = (mapEffectRainLevel-2)*4;
#endif
			
			for( i = 0; i < mapEffectRainFrameMax; i++ )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_RAIN;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->dx = 2+(rand() % 2);
					buf->dy = 9;
					time = 0;
					mapEffectRainCnt++;
				}
			}
		}
	}


	// 移动处理
	i = 0;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( buf->type == MAP_EFFECT_TYPE_RAIN )
		{
			buf->x += buf->dx;
			buf->y += buf->dy;
			mapEffectDirHosei( &buf->x, &buf->y );
			if( ww <= buf->x || hh <= buf->y )
			{
				// 移动終了
				MAP_EFFECT *buf2 = buf->next;
				delMapEffectBuf( buf );
				buf = buf2;
				mapEffectRainCnt--;
				continue;
			}
		}
		buf = buf->next;
		mapEffectDrawFlag = 1;
		i++;
	}

	}
}


// 纸ふぶきのエフェクト
void mapEffectKamiFubuki( void )
{
	short mapEffectKamiFubukiWaitTime = 0;
	short mapEffectKamiFubukiFrameMax = 0;
	int hosei1 = 1240;
	int hosei2 = -100;
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	static unsigned int time = GetTickCount();
	MAP_EFFECT *buf;
	int i, j;
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	short KamiFubukiLevel;

	if ( ProcNo != PROC_BATTLE ){
		KamiFubukiLevel = mapEffectKamiFubukiLevel;
	}else{
		KamiFubukiLevel = battleEffectKamiFubukiLevel;
	}

	// 纸ふぶき発生
	if( KamiFubukiLevel )
	{
		if( KamiFubukiLevel < 3 )
		{
			mapEffectKamiFubukiWaitTime = (51 - 25*KamiFubukiLevel);
#else

	// 纸ふぶき発生
	if( mapEffectKamiFubukiLevel )
	{
		if( mapEffectKamiFubukiLevel < 3 )
		{
			mapEffectKamiFubukiWaitTime = (51 - 25*mapEffectKamiFubukiLevel);
#endif
			if( time+mapEffectKamiFubukiWaitTime < GetTickCount() )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_KAMIFUBUKI;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->mode = (rand()%4);
					buf->cnt = 4;
					j = (rand() % 2);
					if( j == 0 )
					{
						buf->dx = -1;
						buf->dy = 2;
					}
					else
					if( j == 1 )
					{
						buf->dx = -1;
						buf->dy = 3;
					}
					time = GetTickCount();
					mapEffectKamiFubukiCnt++;
					buf->y = -buf->dy;		//01/07/19 oft
				}
			}
		}
		else
		{
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			mapEffectKamiFubukiFrameMax = (KamiFubukiLevel-2)*2;
#else
			mapEffectKamiFubukiFrameMax = (mapEffectKamiFubukiLevel-2)*2;
#endif
			
			for( i = 0; i < mapEffectKamiFubukiFrameMax; i++ )
			{
				MAP_EFFECT *buf = getMapEffectBuf();
				if( buf != (MAP_EFFECT *)NULL )
				{
					buf->type = MAP_EFFECT_TYPE_KAMIFUBUKI;
					buf->x = (rand() % hosei1) + hosei2;
					buf->y = 0;
					buf->mode = (rand()%4);
					buf->cnt = 4;
					j = (rand() % 2);
					if( j == 0 )
					{
						buf->dx = -1;
						buf->dy = 2;
					}
					else
					if( j == 1 )
					{
						buf->dx = -1;
						buf->dy = 3;
					}
					time = GetTickCount();
					mapEffectKamiFubukiCnt++;
					buf->y = -buf->dy;		//01/07/19 oft
				}
			}
		}
	}


	// 移动处理
	i = 0;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( buf->type == MAP_EFFECT_TYPE_KAMIFUBUKI )
		{
			buf->x += (buf->dx+(rand()%3)-1);
			buf->y += (buf->dy+(rand()%3)-1);
			if( buf->cnt <= 0 )
			{
				buf->type2 = (rand()%3);
				buf->w = (rand()%8)+1;
				buf->h = (rand()%8)+1;
				buf->cnt = 4;
			}
			else
			{
				buf->cnt--;
			}
			mapEffectDirHosei( &buf->x, &buf->y );
			if( hh <= buf->y )
			{
				// 移动終了
				MAP_EFFECT *buf2 = buf->next;
				delMapEffectBuf( buf );
				buf = buf2;
				mapEffectKamiFubukiCnt--;
				continue;
			}
		}
		buf = buf->next;
		mapEffectDrawFlag = 1;
		i++;
	}
}
#ifdef PUK3_WHALE_SHIP

void mapEffectDirHosei2( int *x, int *y )
{
	static int oldX = -1, oldY = -1;
	int newX, newY;
	float mx, my;

	// 画面表示位置
	camMapToGamen( 0, 0, &mx, &my );
	newX = (int)(mx+.5);
	newY = (int)(my+.5);

	if ( oldX == -1 ){
		oldX = newX,	oldY = newY;
		return;
	}
	*x += newX - oldX;
	*y += newY - oldY;

	oldX = newX,	oldY = newY;
}

BLT_MEMBER clbm[6] = {
	{ 320 - 320, 240 - 480, 320, 290, {255,255,255,255}, BLTVER_PUK2, 0, 0 },
	{ 0   - 320, 544 - 480, 320, 270, {255,255,255,255}, BLTVER_PUK2, 0, 0 },
	{ 320 - 320, 544 - 480, 320, 370, {255,255,255,255}, BLTVER_PUK2, 0, 0 },

	{ 0   - 320, 0   - 480, 320, 290, {255,255,255,255}, BLTVER_PUK2, 0, 0 },
	{ 320 - 320, 0   - 480, 320, 240, {255,255,255,255}, BLTVER_PUK2, 0, 0 },
	{ 0   - 320, 299 - 480, 320, 240, {255,255,255,255}, BLTVER_PUK2, 0, 0 },
};
MAP_EFFECT *makeEffectCloud( int type, int sx, int sy, int point, int speed, int gra )
{
	MAP_EFFECT *buf = getMapEffectBuf();
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	int i;

	if( buf == (MAP_EFFECT *)NULL ) return NULL;

	buf->type = type;
	// 开始位置
	if ( sy == 0 ){
		i = point % (hh + CLOUD_SIZE) + ww + CLOUD_SIZE;
	}else
	if ( sx == 0 ){
		i = point % (ww + CLOUD_SIZE);
	}else{
		i = point % (ww+hh + CLOUD_SIZE*2);
	}

	buf->x = -CLOUD_SIZE;
	buf->y = -CLOUD_SIZE;
	if ( i < ww + CLOUD_SIZE ) buf->x += i;
	if ( i >= ww + CLOUD_SIZE ) buf->y += i - (ww + CLOUD_SIZE);

	if ( sx < 0 ) buf->x = ww-CLOUD_SIZE - buf->x;
	if ( sy < 0 ) buf->y = hh-CLOUD_SIZE - buf->y;

	buf->dx = buf->x << 16;
	buf->dy = buf->y << 16;
	// スピード
	buf->gx = speed;
	// くもの絵
	buf->gy = gra;
	mapEffectCloudCnt++;

	return buf;
}

void delMapEffectCloud()
{
	MAP_EFFECT *buf;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL ){
		if( buf->type == MAP_EFFECT_TYPE_CLOUD ||
			buf->type == MAP_EFFECT_TYPE_CLOUD2 ||
			buf->type == MAP_EFFECT_TYPE_MONSTER ){
			// モンスならアクション解放
			if ( buf->type == MAP_EFFECT_TYPE_MONSTER ){
#ifdef PUK3_CHECK_VALUE
				if ( pActMons ) DeathAction( pActMons );
				pActMons = NULL;
#else
				DeathAction( pActMons );
				pActMons = NULL;
#endif
			}
			// 移动終了
			MAP_EFFECT *buf2 = buf->next;
			delMapEffectBuf( buf );
			buf = buf2;
			mapEffectCloudCnt--;
			continue;
		}
		buf = buf->next;
	}
}

void mapEffectCloud()
{
	int ww = DEF_APPSIZEX;
	int hh = DEF_APPSIZEY;
	MAP_EFFECT *buf;
	int i;
	float f;
	double sx, sy;
	double cs;
	double ang;
	int x = 0,y = 0;
	int dx, dy;

#ifdef PUK2_ACID_PAPER_SNOWSTORM
	// 战闘中は处理しない
	if ( ProcNo == PROC_BATTLE ) return;
#endif
	ang = (double)mapEffectCloudAngle * 3.1415926535 / 180;
	sx = mapEffectCloudSpeed * cos(ang) + mapEffectCloudMoveX;
	sy = mapEffectCloudSpeed * -sin(ang) + mapEffectCloudMoveY;
	mapEffectDirHosei2( &x, &y );

	// 云の相对速度
	cs = sqrt( (sx+x)*(sx+x) + (sy+y)*(sy+y) );

	dx = 0;
	if ( sx+x > 0 ) dx = 1;
	if ( sx+x < 0 ) dx = -1;
	dy = 0;
	if ( sy+y > 0 ) dy = 1;
	if ( sy+y < 0 ) dy = -1;

	// モンスター発生
	if( mapEffectMonsCnt == 0 ){
		makeEffectCloud( MAP_EFFECT_TYPE_MONSTER, dx, dy,
			 mapEffectMonsPoint, 2000, mapEffectMonsPoint % 3 );

		// モンス作成
		pActMons = GetAction( PRIO_CHR + 1, NULL );
		if( pActMons ){
			pActMons->anim_chr_no = SPR_mon1001e;
			pActMons->anim_no = ANIM_B_WALK;
			pActMons->anim_ang = mapEffectMonsAng % 8;
			// 实行关数
			pActMons->func = NULL;
			// 表示优先度
			pActMons->dispPrio = DISP_PRIO_RESERVE+6;
		}
	}
	if ( mapEffectMonsCnt >= 0 ) mapEffectMonsCnt--;

	// 云(手前)発生
	if( mapEffectCloudLevel && mapEffectCloudSpeed ){
		f = (float)( pow( 1.25f, mapEffectCloudLevel-1 ) * cs );
		if ( f == 0 ) f = 0.0001f;
		i = (int)( 60 * 12 / f );
		if ( i == 0 ) i = 1;
		if ( rand() % i == 0 ){
			makeEffectCloud( MAP_EFFECT_TYPE_CLOUD, dx, dy,
				 rand(), (rand() % 1000) + 1000, rand() % 3 );
		}
	}

	// 云(奥)発生
	if( mapEffectCloud2Level && mapEffectCloudSpeed ){
		f = (float)( pow( 1.25f, mapEffectCloud2Level-1 ) * cs );
		if ( f == 0 ) f = 0.0001f;
		i = (int)( 60 * 12 / f );
		if ( i == 0 ) i = 1;
		if ( rand() % i == 0 ){
			makeEffectCloud( MAP_EFFECT_TYPE_CLOUD2, dx, dy,
				 rand(), (rand() % 1000) + 500, rand() % 3 );
		}
	}


	// 移动处理
	i = 0;
	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL ){
		if( buf->type == MAP_EFFECT_TYPE_CLOUD ||
			buf->type == MAP_EFFECT_TYPE_CLOUD2 ||
			buf->type == MAP_EFFECT_TYPE_MONSTER ){
			buf->dx += (int)( ( sx * ( (float)buf->gx / 1000 ) ) * (1<<16) );
			buf->dy += (int)( ( sy * ( (float)buf->gx / 1000 ) ) * (1<<16) );
			buf->dx += x << 16;
			buf->dy += y << 16;
			buf->x = buf->dx >> 16;
			buf->y = buf->dy >> 16;
			if ( buf->x < -CLOUD_SIZE || ww < buf->x ||
				 buf->y < -CLOUD_SIZE || hh < buf->y ){
				// モンスならアクション解放
				if ( buf->type == MAP_EFFECT_TYPE_MONSTER ){
#ifdef PUK3_CHECK_VALUE
					if ( pActMons ) DeathAction( pActMons );
					pActMons = NULL;
#else
					DeathAction( pActMons );
					pActMons = NULL;
#endif
				}
				// 移动終了
				MAP_EFFECT *buf2 = buf->next;
				delMapEffectBuf( buf );
				buf = buf2;
				mapEffectCloudCnt--;
				continue;
			}
			// 描画登録
			switch(buf->type){
			case MAP_EFFECT_TYPE_MONSTER:
				if (pActMons){
					pActMons->x = buf->x + 150;
					pActMons->y = buf->y + 150;
					pattern( pActMons, ANM_NOMAL_SPD, ANM_LOOP );
					StockDispBuffer( pActMons->x, pActMons->y,
						 0, pActMons->bmpNo, 0 );
				}
			case MAP_EFFECT_TYPE_CLOUD:
				StockDispBuffer_PUK2( buf->x, buf->y,
					 DISP_PRIO_RESERVE+ (buf->gx/200) - 5,
					 14648, 0, 1, &clbm[buf->gy] );
				break;
			case MAP_EFFECT_TYPE_CLOUD2:
				StockDispBuffer_PUK2( buf->x, buf->y, DISP_PRIO_SEA,
					 14648, 0, 1, &clbm[buf->gy+3] );
				break;
			}
		}
		buf = buf->next;
		mapEffectDrawFlag = 1;
		i++;
	}
}
#endif
