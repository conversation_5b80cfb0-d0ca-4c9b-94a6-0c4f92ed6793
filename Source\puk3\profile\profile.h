﻿
#ifndef _PROFILE_H_
#define _PROFILE_H_

#define PROFILE_MENU_MAX		100
#define PROFILE_HIT_LIST_MAX	20
#define MINI_MAIL_STOCK_MAX		20

#define PROFILE_SAVE_FILENAME	"profile.dat"
#define CATEGORY_SAVE_FILENAME	"category.dat"

typedef struct
{
	bool	use;
	int		ID;
	int		GraphNo;
	char	Name[256];
	bool	ok;

} PROFILE_CATEGORY;

typedef struct
{
	int					SellBuyver;
	int					Aboutver;
	int					SellBuyCount;
	int					AboutCount;
	int					mailMoney;
	PROFILE_CATEGORY	Sell[PROFILE_MENU_MAX];
	PROFILE_CATEGORY	Buy[PROFILE_MENU_MAX];
	PROFILE_CATEGORY	About[PROFILE_MENU_MAX];
} PROFILE_CATEGORY_SYSTEM;

typedef struct
{
	int userID;
	BOOL open;

	char name[256];
	int lv;
	int graphID;
	char title<PERSON>ame[256];
	char jobName[256];
	char guildName[256];

	int SellID;
	int SellGraphID;
	char SellStr[256];

	int BuyID;
	int BuyGraphID;
	char BuyStr[256];

	int AboutID;
	int AboutGraphID;
	char AboutStr[256];
	
	char ProfileStr[256];

} PROFILE_STATUS;

typedef struct
{

	BOOL use;
	char name[256];
	int userID;
	int graphID;
	char ProfileStr[256];

} PROFILE_HIT;

typedef struct
{

	int category;
	int id;
	PROFILE_HIT list[PROFILE_HIT_LIST_MAX];

} PROFILE_HIT_LIST;

typedef struct{
	int id;
	char name[256];
	char str[1024];
} PROFILE_SENDMAIL;

enum{
	ProfileListModeSell,
	ProfileListModeBuy,
	ProfileListModeAbout,
};

enum{
	PROFILE_CATEGORY_SELLBUY,
	PROFILE_CATEGORY_ABOUT,
};

enum{
	PROFILE_ADDRESS,
	PROFILE_GUILD,
	PROFILE_PROFILE,
	PROFILE_NPC_OBJ,
};

//分类ーのリスト
extern PROFILE_CATEGORY_SYSTEM categorySys;
//现在开いている分类リストのモード
extern int ProfileListMode;
//自分のプロフィール
extern PROFILE_STATUS MyProfile[MAXCHARACTER];
//自分のプロフィール送信用ワーク
extern PROFILE_STATUS MyProfile_SendWork;
//他人のプロフィール
extern PROFILE_STATUS UserNpcProfile;
//揭示板で检索した人のプロフィール
extern PROFILE_STATUS SearchProfile;
//检索结果、ヒットした情报リスト
extern PROFILE_HIT_LIST ProfileHitList;
//プロフィールメール送るワーク
extern PROFILE_SENDMAIL ProfileSendMail;
//ミニメール用のアドレスブック
extern ADDRESS_BOOK_INFO miniMailBook[MINI_MAIL_STOCK_MAX];
//ミニメール用のアドレスブック构造体
extern MAIL_HISTORY miniMailHistory[MINI_MAIL_STOCK_MAX];
//ミニメールアドレスブックのソート配列
extern int miniMailBookSort[MINI_MAIL_STOCK_MAX];
//ミニメールのカウンタ（追加されるごとにインクリメントされ、ソートに使用）
extern int MiniMailBookCounter;
//揭示板の搜索文字
extern char ProfileSearchStr[256];

void userNpcProfileWinOpen(void);
int getCategoryGraphicID(int id,int type);
void clearProfile(void);
void setDefaultProfile(void);
BOOL checkProfileSendWork(void);
void sendProfile(int type);
int getMiniMailMemIndexfromID(int id);
int getMiniMailNoUseMemIndex(void);
void setMiniMailHistory( int index, int sendFlag, char *header, char *buf );
void deleteMiniMailBookMember(int id);
void makeLocalTime(char *timeStr);
BOOL saveProfileDateFile(void);
BOOL loadProfileDateFile(void);
BOOL saveCategoryDateFile(void);
BOOL loadCategoryDateFile(void);
void ClearProfilePcNo(int select);
void sendProfileInLogin(void);


#endif