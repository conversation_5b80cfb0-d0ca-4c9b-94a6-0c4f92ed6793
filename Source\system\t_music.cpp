﻿#include <windows.h>
#include <dsound.h>
#include <stdio.h>
//#include <winuser.h>
//#include <winbase.h>
#include <math.h>

#include <tchar.h>
#include <crtdbg.h>
#include <mmsystem.h>

#include "../systeminc/main.h"
#include "../systeminc/filetbl.h"
#include "../systeminc/math2.h"
#include "../systeminc/system.h"
#include "../systeminc/process.h"
#include "../systeminc/t_music.h"
#include "../systeminc/handletime.h"
#include "../systeminc/debug.h"
#include"../systeminc/savedata.h"
#include"../systeminc/pc.h"
#include "../systeminc/map.h"


#define RELEASE(x) 	if(x){x->Release();x=NULL;}
#define	LOG(a,b)	(log((double)b)/log((double)a))


#define ACTIVE	1


#define MY_TIMER	1
#define VOICE_MAX	64
#define TRACK_MAX	32


/*和音ディレイ１ｍｓｅｃ*/
#define DEBUG_ON	1
#define THREAD_ON	1
#define MODE98		0
#define CLASS_UP	1
#define VOICE_KILL_ON	0

#define PAN_POINT	5000


int soundUseFlag = 0;	// サウンドを使用するかのフラグ
						//   0 ... サウンド使用
						//   1 ...    〃   不使用
#ifdef PUK2_NOSOUNDMUSIC
	int soundSoundFlag = 1;	// 效果音を鸣らすかのフラグ
							//   1 ... サウンド使用
							//   0 ...    〃   不使用
	int musicSoundFlag = 1;	// ＢＧＭを鸣らすかのフラグ
							//   1 ... サウンド使用
							//   0 ...    〃   不使用
#endif

LPDIRECTSOUND pDSound = NULL;					// DirectSound オブジェクト
LPDIRECTSOUNDBUFFER pDSPrimary = NULL;			// DirectSound プライマリサーフェス
LONG primaryVolume;
LONG primaryPan;
LONG primaryFrequency;

LPDIRECTSOUNDBUFFER pDSData[VOICE_MAX];			// データサーフェス
LPDIRECTSOUNDBUFFER pDSData_tone[TONE_MAX + 1];
#ifdef PUK3_BGM_REFER
LPBYTE streamData = NULL;
#else
LPDIRECTSOUNDBUFFER pDSData_stream;
#endif






int cdda_no = -1;
int stereo_flg = T_MUSIC_STEREO;
int t_music_se_volume = 15;
int t_music_bgm_volume = 15;
static MCI_OPEN_PARMS open  = {0};
static MCIERROR       dwRes = {0};
static int cdda_flg;
static int cdda_check_cnt = -1;


extern double NowTime;	// 现在の时间记忆
#ifdef PUK2_FPS
	extern double NowDrawTime;	// 现在の时间记忆
#endif



#ifdef PUK3_SOUND
	#define	MAX_ENV		ENV_SE_MAX
#else
#define	MAX_ENV		20
#endif
ENVIRONMENT_EQU env_tbl[MAX_ENV];


#ifdef PUK2
ENV_SE environmentSe[ENV_SE_MAX];
#endif


static int mes_flg=0;

#if THREAD_ON
int thread_flg=0;
#endif






int dataVolume[TRACK_MAX];
int dataPan[TRACK_MAX];
LONG dataFrequency[TRACK_MAX];
VOICE_EQU voice[VOICE_MAX];
TONE_EQU tone_tbl[TONE_MAX];
int tone_max_buf;

#if VOICE_KILL_ON
int voice_kill_buf[VOICE_MAX];
int voice_kill_point;
#endif

int t_music_playing_flg = 0;
int t_music_fade_flg = 0;
int t_music_fade_vol;
int draw_map_bgm_flg = 0;
static int map_bgm_vct_no = 0;
int map_bgm_no = 0;



volatile static char exit_thread_flg = 0;
static int thread_loop_start;
//static int thread_loop_end;
static int thread_stop_flg = 0;
static LPDIRECTSOUNDNOTIFY pDSNotify = NULL;
static char stream_flg = 0;

static int voice_seek_point=0;

#ifdef PUK2
int bgm_old;
#endif


#ifdef PUK2
int freq_tbl[]={
#else
static int freq_tbl[]={
#endif
	(int)(44100.0 / 261.63 * 277.18 / 32.0),	//b (o-1) 0	（未使用）
	2756,										//c (o0) 1
	(int)(44100.0 / 261.63 * 277.18 / 16.0),	//c+
	(int)(44100.0 / 261.63 * 293.67 / 16.0),	//d	
	(int)(44100.0 / 261.63 * 311.13 / 16.0),	//d+
	(int)(44100.0 / 261.63 * 329.63 / 16.0),	//e
	(int)(44100.0 / 261.63 * 349.23 / 16.0),	//f
	(int)(44100.0 / 261.63 * 366.99 / 16.0),	//f+
	(int)(44100.0 / 261.63 * 392.00 / 16.0),	//g
	(int)(44100.0 / 261.63 * 415.31 / 16.0),	//g+
	(int)(44100.0 / 261.63 * 440.00 / 16.0),	//a
	(int)(44100.0 / 261.63 * 466.16 / 16.0),	//a+
	(int)(44100.0 / 261.63 * 493.88 / 16.0),	//b
	5512,									//c (o1) 13
	(int)(44100.0 / 261.63 * 277.18 / 8.0),	//c+
	(int)(44100.0 / 261.63 * 293.67 / 8.0),	//d
	(int)(44100.0 / 261.63 * 311.13 / 8.0),	//d+
	(int)(44100.0 / 261.63 * 329.63 / 8.0),	//e
	(int)(44100.0 / 261.63 * 349.23 / 8.0),	//f
	(int)(44100.0 / 261.63 * 366.99 / 8.0),	//f+
	(int)(44100.0 / 261.63 * 392.00 / 8.0),	//g
	(int)(44100.0 / 261.63 * 415.31 / 8.0),	//g+
	(int)(44100.0 / 261.63 * 440.00 / 8.0),	//a
	(int)(44100.0 / 261.63 * 466.16 / 8.0),	//a+
	(int)(44100.0 / 261.63 * 493.88 / 8.0),	//b
	11025,									//c (o2) 25
	(int)(44100.0 / 261.63 * 277.18 / 4.0),	//c+
	(int)(44100.0 / 261.63 * 293.67 / 4.0),	//d
	(int)(44100.0 / 261.63 * 311.13 / 4.0),	//d+
	(int)(44100.0 / 261.63 * 329.63 / 4.0),	//e
	(int)(44100.0 / 261.63 * 349.23 / 4.0),	//f
	(int)(44100.0 / 261.63 * 366.99 / 4.0),	//f+
	(int)(44100.0 / 261.63 * 392.00 / 4.0),	//g
	(int)(44100.0 / 261.63 * 415.31 / 4.0),	//g+
	(int)(44100.0 / 261.63 * 440.00 / 4.0),	//a
	(int)(44100.0 / 261.63 * 466.16 / 4.0),	//a+
	(int)(44100.0 / 261.63 * 493.88 / 4.0),	//b
	22050,									//c (o3) 37
	(int)(44100.0 / 261.63 * 277.18 / 2.0),	//c+
	(int)(44100.0 / 261.63 * 293.67 / 2.0),	//d
	(int)(44100.0 / 261.63 * 311.13 / 2.0),	//d+
	(int)(44100.0 / 261.63 * 329.63 / 2.0),	//e
	(int)(44100.0 / 261.63 * 349.23 / 2.0),	//f
	(int)(44100.0 / 261.63 * 366.99 / 2.0),	//f+
	(int)(44100.0 / 261.63 * 392.00 / 2.0),	//g
	(int)(44100.0 / 261.63 * 415.31 / 2.0),	//g+
	(int)(44100.0 / 261.63 * 440.00 / 2.0),	//a
	(int)(44100.0 / 261.63 * 466.16 / 2.0),	//a+
	(int)(44100.0 / 261.63 * 493.88 / 2.0),	//b
	44100,									//c (o4) 49
	(int)(44100.0 / 261.63 * 277.18),		//c+
	(int)(44100.0 / 261.63 * 293.67),		//d
	(int)(44100.0 / 261.63 * 311.13),		//d+
	(int)(44100.0 / 261.63 * 329.63),		//e
	(int)(44100.0 / 261.63 * 349.23),		//f
	(int)(44100.0 / 261.63 * 366.99),		//f+
	(int)(44100.0 / 261.63 * 392.00),		//g
	(int)(44100.0 / 261.63 * 415.31),		//g+
	(int)(44100.0 / 261.63 * 440.00),		//a
	(int)(44100.0 / 261.63 * 466.16),		//a+
	(int)(44100.0 / 261.63 * 493.88),		//b
	88200,									//b+ (o5) 61
	(int)(44100.0 / 261.63 * 277.18 * 2.0),	//c+
	(int)(44100.0 / 261.63 * 293.67 * 2.0),	//d		（以下未使用）63
	(int)(44100.0 / 261.63 * 311.13 * 2.0),	//d+
	(int)(44100.0 / 261.63 * 329.63 * 2.0),	//e
	(int)(44100.0 / 261.63 * 349.23 * 2.0),	//f
	(int)(44100.0 / 261.63 * 366.99 * 2.0),	//f+
	(int)(44100.0 / 261.63 * 392.00 * 2.0),	//g
	(int)(44100.0 / 261.63 * 415.31 * 2.0),	//g+
	(int)(44100.0 / 261.63 * 440.00 * 2.0),	//a
	(int)(44100.0 / 261.63 * 466.16 * 2.0),	//a+
	(int)(44100.0 / 261.63 * 493.88 * 2.0),	//b
};
#ifdef PUK2
long volume_tbl[128];
#else
static long volume_tbl[128];
#endif
static long panpot_tbl[128];



T_MUSIC_BGM bgm_tbl2[] =
{
	{ "bgm\\cgbgm_m0.wav",		90, 2, 0 },	// 城の内部（リセリア城）                   //MLHIDE
	{ "bgm\\cgbgm_m1.wav",		90, 2, 0 },	// 城下町（ファンブルグの町）                 //MLHIDE
	{ "bgm\\cgbgm_m2.wav",		90, 2, 0 },	// 村（セレーネ岛）                      //MLHIDE
	{ "bgm\\cgbgm_m3.wav",		90, 2, 0 },	// 村（フレイア岛）                      //MLHIDE
	{ "bgm\\cgbgm_m4.wav",		90, 2, 0 },	// 村（ソルキア岛）                      //MLHIDE

	{ "bgm\\cgbgm_f0.wav",		90, 2, 0 },	// フィールド（セレーネ岛）风雪地带              //MLHIDE
	{ "bgm\\cgbgm_f1.wav",		90, 2, 0 },	// フィールド（フレイア岛）草原、平原             //MLHIDE
	{ "bgm\\cgbgm_f2.wav",		90, 2, 0 },	// フィールド（ソルキア岛）砂漠、干燥地带           //MLHIDE

	{ "bgm\\cgbgm_d0.wav",		90, 2, 0 },	// ダンジョン（人工洞窟）                   //MLHIDE
	{ "bgm\\cgbgm_d1.wav",		90, 2, 0 },	// ダンジョン（自然洞窟）                   //MLHIDE
	{ "bgm\\cgbgm_d2.wav",		90, 2, 0 },	// 自动生成ダンジョン（人工洞窟）               //MLHIDE
	{ "bgm\\cgbgm_d3.wav",		90, 2, 0 },	// 自动生成ダンジョン（自然洞窟）               //MLHIDE
	{ "bgm\\cgbgm_d4.wav",		90, 2, 0 },	// ラストダンジョン                      //MLHIDE

	{ "bgm\\cgbgm_b0.wav",		90, 2, 0 },	// 通常バトル                         //MLHIDE
	{ "bgm\\cgbgm_b1.wav",		90, 2, 0 },	// ボスバトル                         //MLHIDE
	{ "bgm\\cgbgm_b2.wav",		90, 2, 0 },	// ラスボトバトル                       //MLHIDE
	{ "bgm\\cgbgm_b3.wav",		90, 2, 0 },	// デュエル                          //MLHIDE

	{ "bgm\\cgbgm_t0.wav",		90, 2, 0 },	// タイトル画面、キャラクター作成画面             //MLHIDE

	{ "bgm\\exbgm_s0.wav",		90, 2, 0 },	// EX&V2船                        //MLHIDE

	{ "bgm\\exbgm_f0.wav",		90, 2, 0 },	// EXミネガル岛フィールド                  //MLHIDE
	{ "bgm\\exbgm_m0.wav",		90, 2, 0 },	// EXミネガル町/村                     //MLHIDE

	{ "bgm\\v2bgm_f0.wav",		90, 2, 0 },	// V2クルクス岛フィールド                  //MLHIDE
	{ "bgm\\v2bgm_m0.wav",		90, 2, 0 },	// V2クルクス町/村                     //MLHIDE

	{ "bgm\\v2bgm_ex.wav",		90, 2, 0 },	// EXTRABGM（ループあり）               //MLHIDE
	{ "bgm\\v2bgm_ex.wav",		90, 0, 0 },	// EXTRABGM（ループなし）               //MLHIDE

#ifdef PUK2
	{ "bgm\\puk2_battle1.wav",		90, 2, 0 },	// PUK2用バトルＢＧＭ（ザコ战）          //MLHIDE
	{ "bgm\\puk2_battle2.wav",		90, 2, 0 },	// PUK2用バトルＢＧＭ（ＢＯＳＳ战）        //MLHIDE

	{ "bgm\\puk2_field1.wav",		90, 2, 0 },	// PUK2用フィールドＢＧＭ              //MLHIDE

	{ "bgm\\puk2_mati.wav",			90, 2, 0 },	// PUK2用街ＢＧＭ                   //MLHIDE
	{ "bgm\\puk2_sinden.wav",		90, 2, 0 },	// PUK2用神殿ＢＧＭ                 //MLHIDE

	{ "bgm\\puk2_yama.wav",			90, 2, 0 },	// PUK2用山                      //MLHIDE
	{ "bgm\\puk2_haikyo.wav",		90, 2, 0 },	// PUK2用废墟                    //MLHIDE
	{ "bgm\\puk2_m_town.wav",		90, 2, 0 },	// PUK2用モンスタータウン              //MLHIDE
	{ "bgm\\puk2_OP.wav",			90, 2, 0 },	// PUK2用タイトルロゴＢＧＭ                //MLHIDE
#endif

#ifdef PUK3
	{ "bgm\\puk3_battle1.wav",		90, 2, 0 },	// PUK3用バトル１                 //MLHIDE
	{ "bgm\\puk3_battle2.wav",		90, 2, 0 },	// PUK3用バトル２                 //MLHIDE
	{ "bgm\\puk3_dungeon.wav",		90, 2, 0 },	// PUK3用ダンジョン                //MLHIDE
	{ "bgm\\puk3_kame.wav",			90, 2, 0 },	// PUK3用龟                      //MLHIDE
	{ "bgm\\puk3_kujira.wav",		90, 2, 0 },	// PUK3用鲸                     //MLHIDE
	{ "bgm\\puk3_kumo.wav",			90, 2, 0 },	// PUK3用云                      //MLHIDE
	{ "bgm\\puk3_love.wav",			90, 2, 0 },	// PUK3用爱                      //MLHIDE
	{ "bgm\\puk3_playerbattle.wav",	90, 2, 0 },	// PUK3用プレイヤーバトル         //MLHIDE
	{ "bgm\\PUK3_title.wav",		90, 2, 0 },	// PUK3用オープニング                 //MLHIDE
#endif
#ifdef PUK3_NEWMUSIC
	{ "bgm\\PUK3_orgel.wav",		90, 2, 0 },	// PUK3用オルゴール                  //MLHIDE
#endif

};

T_MUSIC_BGM bgm_tbl[sizeof(bgm_tbl2)/sizeof(bgm_tbl2[0])];


#ifdef _USE_RECENT_SOUND_LIST
static int recent_sound_list[MAX_RECENT_SOUND_LIST];
#endif /* _USE_RECENT_SOUND_LIST */






//-------------------------------------------------------------------------//
// T_MUSIC 初期化                                                          //
//-------------------------------------------------------------------------//
int t_music_init( void )
{
#ifdef PUK2_NOSOUNDMUSIC
	// 音无しの时
	if ( CmdLineFlg & CMDLINE_NOINITSOUND ){
		soundUseFlag = 1;	// サウンドを使用しない
		return FALSE;
	}
#endif
	// SHIFTキーが押されていたらサウンドを使用しない
	if( GetAsyncKeyState( 0x10 ) & 0x8000 )
	{
		soundUseFlag = 1;	// サウンドを使用しない
		return FALSE;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "	initDSound() Start" );                         //MLHIDE
	
	if( !initDSound() )
	{
		soundUseFlag = 1;	// サウンドを使用しない
		return FALSE;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "	music_init() Start" );                         //MLHIDE

	music_init();			//ドライバー初期化

	soundUseFlag = 0;		// サウンド使用

	// システムログファイル书き込み
	WriteSystemLogfile( "t_music_init OK" );                             //MLHIDE

#ifdef _USE_RECENT_SOUND_LIST
	// 最近使ったサウンド一覧テーブルを初期化
	memset( recent_sound_list, -1, sizeof( recent_sound_list));
#endif /* _USE_RECENT_SOUND_LIST */

	return TRUE;
}




//-------------------------------------------------------------------------//
// DirectSound 初期化                                                      //
//-------------------------------------------------------------------------//
int initDSound( void )
{
	#define USE_SOUNDCONFIG
	#include "../systeminc/sndcnf.h"

	DSBUFFERDESC dsbdesc;
	WAVEFORMATEX pcmwf;
	HRESULT ret;
	int samplingRate;
	int i, j;


	pDSound = NULL;
	pDSPrimary = NULL;

	// システムログファイル书き込み
	WriteSystemLogfile( "		pDSData Start" );                             //MLHIDE
	
	// データサーフェス初期化
	for( i = 0; i < sizeof( pDSData )/sizeof( pDSData[0] ); i++ )
		pDSData[i] = NULL;

	// システムログファイル书き込み
	WriteSystemLogfile( "		pDSData_tone Start" );                        //MLHIDE
	
	// 
	for( i = 0; i < sizeof( pDSData_tone )/sizeof( pDSData_tone[0] ); i++ )
		pDSData_tone[i] = NULL;

	// システムログファイル书き込み
	WriteSystemLogfile( "		DirectSoundCreate Start" );                   //MLHIDE
	
	// DirectSound オブジェクトの作成
	ret = DirectSoundCreate( NULL, &pDSound, NULL );
	if(ret != DS_OK)
	{
#ifdef _DEBUG
	    MessageBox( hWnd, "DirectSound Object生成失败", "Error", MB_OK );    //MLHIDE
#endif
		return FALSE;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "		pDSound->SetCooperativeLevel Start" );        //MLHIDE
	
	// 协调等级を设定
//	ret = pDSound->SetCooperativeLevel(hWnd, DSSCL_NORMAL);
	ret = pDSound->SetCooperativeLevel( hWnd, DSSCL_PRIORITY );
	if( ret != DS_OK )
	{
	    // 协调等级设定失败
		RELEASE( pDSound );
#ifdef _DEBUG
	    MessageBox( hWnd, "协调等级设定失败", "Error", MB_OK );                  //MLHIDE
#endif
		return FALSE;
	}


	/*
	// システムログファイル书き込み
	WriteSystemLogfile( "		pDSound->SetSpeakerConfig Start" );
	
	// スピーカの设定
	//pDSound->SetSpeakerConfig( DSSPEAKER_COMBINED( DSSPEAKER_STEREO, DSSPEAKER_GEOMETRY_WIDE ) );
	*/
	// プライマリサーフェスの作成の准备
	// DSBUFFERDESC构造体を设定
	ZeroMemory( &dsbdesc, sizeof( dsbdesc ) );
	dsbdesc.dwSize = sizeof( dsbdesc ); 
//	dsbdesc.dwFlags = DSBCAPS_CTRLVOLUME | DSBCAPS_CTRLPAN | DSBCAPS_PRIMARYBUFFER; 
	dsbdesc.dwFlags = DSBCAPS_PRIMARYBUFFER; 
	dsbdesc.dwBufferBytes = 0;
	dsbdesc.lpwfxFormat = NULL;

	// システムログファイル书き込み
	WriteSystemLogfile( "		pDSound->CreateSoundBuffer Start" );          //MLHIDE
	
	// サーフェスを作る
	ret = pDSound->CreateSoundBuffer( &dsbdesc, &pDSPrimary, NULL ); 
	if( ret != DS_OK )
	{ 
	    // サーフェスの作成に失败
#ifdef _DEBUG
	    MessageBox( hWnd, "主缓存生成失败", "Error", MB_OK );                   //MLHIDE
#endif
		return FALSE;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "		ZeroMemory Start" );                          //MLHIDE
	
	// プライマリサーフェスのWaveフォーマットを设定
	// 　优先协调等级以上の协调等级が设定されている必要があります
	ZeroMemory( &pcmwf, sizeof( pcmwf ) ); 
	pcmwf.wFormatTag = WAVE_FORMAT_PCM; 
	pcmwf.nChannels = 2;			// ２チャンネル（立体声）
//	pcmwf.nSamplesPerSec = 44100;	// サンプリング??レート　44.1kHz
	pcmwf.nSamplesPerSec = 22050;	// サンプリング??レート　22.05kHz
	pcmwf.nBlockAlign = 4;
	pcmwf.nAvgBytesPerSec = pcmwf.nSamplesPerSec * pcmwf.nBlockAlign; 
	pcmwf.wBitsPerSample = 16;		// 16ビット
	ret = pDSPrimary->SetFormat( &pcmwf );
	if( ret != DS_OK )
	{
#ifdef _DEBUG
	    MessageBox( hWnd, "主缓存格式化失败", "Error", MB_OK );                  //MLHIDE
#endif
		return FALSE;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "		GetVolume Start" );                           //MLHIDE
	
	// ボリュームなどの值を取得する
	pDSPrimary->GetVolume( &primaryVolume );

	// システムログファイル书き込み
	WriteSystemLogfile( "		GetPan Start" );                              //MLHIDE

	// パンポット
	pDSPrimary->GetPan( &primaryPan );

	// システムログファイル书き込み
	WriteSystemLogfile( "		GetFrequency Start" );                        //MLHIDE

	// 周波数
	pDSPrimary->GetFrequency( (DWORD*)&primaryFrequency );




	HMMIO hmmio;					// ファイルハンドル
#ifndef _USE_RECENT_SOUND_LIST
	WAVEFORMATEX	Wfmtx;			// WAVEファイルのフォーマットデータ
	DWORD			DataSize;		// バッファーのサイズ
	DWORD			WaveSize;		// Waveファイルのサイズ
#endif /* _USE_RECENT_SOUND_LIST */
	int program_no = 0;
	char fname[64];
	char errFlag = 1;

	hmmio = NULL;
	tone_max_buf = 0;


	// システムログファイル书き込み
	WriteSystemLogfile( "		tone_tbl Start" );                            //MLHIDE

	// 音色テーブル初期化
	for( i = 0; i < TONE_MAX; i++ )
	{
		tone_tbl[i].voice_place = -1;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "		sndconfig Start" );                           //MLHIDE

	//＝＝＝＝＝＝＝＝＝＝　音色定义ヘッダー解析　＝＝＝＝＝＝＝＝＝＝
	//SNDCONFIGカウンター初期化
	i = 0;
	while( 1 )
	{
		// 終端にきたら終わる
		if( sndconfig[ i ].no == -1 )
		{
			errFlag = 0;
			break;
		}
		program_no = sndconfig[i].no;	// 音色番号セット
		if( program_no > TONE_MAX )		// マックス以上ならエラー
		{
			break;
		}
		// 音色セット先保存
		tone_tbl[program_no].voice_place = tone_max_buf;
		//名称の取得
		sprintf( fname, "%s\\%s", binFolder, sndconfig[i].name );           //MLHIDE
		sprintf( tone_tbl[program_no].filename, "%s\\%s", binFolder, sndconfig[i].name ); //MLHIDE
		if( sndconfig[i].vol < 0 )		// ボリュームがマイナスならエラー
		{
#ifdef _DEBUG
			MessageBox( hWnd, "音色设定文件的音量设定有问题。",                               //MLHIDE
				"Error", MB_OK );                                                 //MLHIDE
#endif
			break;
		}
		// 音色ボリューム保存
		tone_tbl[program_no].voice_volume = sndconfig[i].vol;

		if( sndconfig[i].note > 60 )	// ノート番号エラーなら
		{
			break;
		}
		// ノート番号保存
		tone_tbl[program_no].voice_note = sndconfig[i].note;
		// ループフラグセット
		tone_tbl[program_no].voice_loop = sndconfig[i].loop_flg;


		// サンプリングレート取り出し
		FILE *fp;
		fp = fopen( fname, "rb" );                                          //MLHIDE
		if( fp == NULL )
		{
			i++;
			continue;	// ファイルを开けないので次の处理へ
		}
		j = 8;						// 'ＲＩＦＦ'８バイトスキップ
		j += 4;						// 'ＷＡＶＥ'４バイトスキップ
		j += 4;						// 'ｆｍｔ　'４バイトスキップ
		j += 8;						// ８バイトスキップ
		fseek( fp, j, SEEK_CUR );	// ファイルポインター移动
		samplingRate = fgetc(fp);			// サンプリングレート取り出し
		samplingRate |= fgetc(fp) << 8;		// サンプリングレート取り出し
		samplingRate |= fgetc(fp) << 16;	// サンプリングレート取り出し
		samplingRate |= fgetc(fp) << 24;	// サンプリングレート取り出し
		fclose( fp );
		switch( samplingRate )
		{
			case 11025:
				tone_tbl[program_no].voice_rate = 25;
				break;

			case 22050:
				tone_tbl[program_no].voice_rate = 37;
				break;

			case 44100:
				tone_tbl[program_no].voice_rate = 49;
				break;
		}

#ifndef _USE_RECENT_SOUND_LIST
		// ＷＡＶファイルのオープン
		if( (hmmio = mmioOpen( fname, NULL, MMIO_READ | MMIO_ALLOCBUF ) ) == NULL )
		{
#ifdef _DEBUG
		    MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                 //MLHIDE
#endif
			break;
		}
		//アトリビュートのロード
		if( WaveFormatRead( hmmio , &Wfmtx , &WaveSize ) == FALSE )
		{
#ifdef _DEBUG
		    MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                 //MLHIDE
#endif
			break;
		}
		DataSize = WaveSize;
		//サウンドサーフェースを起こす
		if( dwSoundInit( &Wfmtx, DataSize, &pDSData_tone[tone_max_buf] ) == FALSE )
		{
#ifdef _DEBUG
		    MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                 //MLHIDE
#endif
			break;
		}
		//データのロード
		if( WaveDataRead( hmmio, &DataSize, pDSData_tone[tone_max_buf] ) == FALSE )
		{
#ifdef _DEBUG
		    MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                 //MLHIDE
#endif
			break;
		}
		mmioClose( hmmio , 0 );
		hmmio = NULL;
#endif /* _USE_RECENT_SOUND_LIST */
		i++;					// SNDCONFIGカウンター更新
		tone_max_buf++;			// 音色セット先更新
	}


	// システムログファイル书き込み
	WriteSystemLogfile( "		errFlag Start" );                             //MLHIDE
	
	if( errFlag )
	{
		// エラー处理
		if( hmmio )
			mmioClose( hmmio, 0 );

		for( i = 0; i < tone_max_buf; i++ )
		{
			if( pDSData_tone[i] )
			{
				pDSData_tone[i]->Stop();
				RELEASE( pDSData_tone[i] );
			}
		}
		RELEASE( pDSPrimary );
		RELEASE( pDSound );
		return FALSE;
	}


	// システムログファイル书き込み
	WriteSystemLogfile( "		pDSound->DuplicateSoundBuffer Start" );       //MLHIDE

#ifndef _USE_RECENT_SOUND_LIST
	// ボイス数分音色番号０を复制する
	for( i = 0; i < VOICE_MAX; i++ )
	{
		if( pDSound->DuplicateSoundBuffer( pDSData_tone[0],&pDSData[i] ) != DS_OK )
		{
			int j;
			// システムログファイル书き込み
			WriteSystemLogfile( "		pDSound->DuplicateSoundBuffer DS_NG Start" ); //MLHIDE
			for( j = 0; j < tone_max_buf; j++ )
				RELEASE( pDSData_tone[j] );
			for( j = 0; j < i; j++ )
				RELEASE( pDSData[j] );
			RELEASE( pDSPrimary );
			RELEASE( pDSound );
			WriteSystemLogfile( "		pDSound->DuplicateSoundBuffer DS_NG End" ); //MLHIDE
			return FALSE;
		}
	}
#endif /* _USE_RECENT_SOUND_LIST */

	// システムログファイル书き込み
	WriteSystemLogfile( "		BGM表格复制 Start" );                             //MLHIDE

	// BGMテーブルコピー
	for( i = 0; i < sizeof( bgm_tbl2 )/sizeof( bgm_tbl2[0] ); i++ )
	{
		sprintf( bgm_tbl[i].fname, "%s\\%s", binFolder, bgm_tbl2[i].fname ); //MLHIDE
		bgm_tbl[i].volume = bgm_tbl2[i].volume;
		bgm_tbl[i].loop_flg = bgm_tbl2[i].loop_flg;
		bgm_tbl[i].loop_point = bgm_tbl2[i].loop_point;
	}

	// システムログファイル书き込み
	WriteSystemLogfile( "	initDSound OK" );                              //MLHIDE

	return TRUE;
}




//-------------------------------------------------------------------------//
// T_MUSIC 終了处理                                                        //
//-------------------------------------------------------------------------//
void t_music_end( void )
{
	int i;

	if( soundUseFlag )	// サウンド使用しなければ終わる
		return;

	//スレッド停止へ
	stop_thread();
	RELEASE( pDSData_tone[TONE_MAX] );

	//开放するなら
	if( stream_flg == 1 )
	{
		RELEASE( pDSNotify );
	}
	for( i = 0; i < VOICE_MAX; i++ )
	{
		if( pDSData[i] )
		{
			pDSData[i]->Stop();
			RELEASE( pDSData[i] );
		}
	}
	for( i = 0; i < tone_max_buf; i++ )
	{
		if( pDSData_tone[i] )
		{
			pDSData_tone[i]->Stop();
			RELEASE( pDSData_tone[i] );
		}
	}
	RELEASE( pDSPrimary );
	RELEASE( pDSound );
}












/*-------------------------------------------
	Waveリソースの読み込み
--------------------------------------------*/
BOOL LoadWave(
			  LPCTSTR lpName,	// Waveデータ名
			  WAVEFORMATEX* (&pWaveHeader),	// WAVEFORMATEX构造体へのポインタ
			  BYTE* (&pbWaveData),	// サウンド??データへのポインタ
			  DWORD &cbWaveSize)	// サウンド??データのサイズ
{
	pWaveHeader = NULL;	// WAVEFORMATEX构造体へのポインタ
	pbWaveData = NULL;	// サウンド??データへのポインタ
	cbWaveSize = 0;	// サウンド??データのサイズ
	const char c_szWAV[] = "WAVE";	// リソース内でのWaveファイルの形式は，"WAVE"とする      //MLHIDE

	// リソースからファイル??データを読み込む
	HRSRC hResInfo = FindResource(NULL, lpName, c_szWAV);
	if(hResInfo == NULL)
	    return FALSE;
	HGLOBAL hResData = LoadResource(NULL, hResInfo);
	if(hResData == NULL)
	    return FALSE;
	void *pvRes = LockResource(hResData);
	if(pvRes == NULL)
	    return FALSE;

	// 読み込んだリソース??データを解析し，必要なデータにアクセスできるポインタを取得する
	DWORD *pdw = (DWORD *)pvRes;
	DWORD *pdwEnd;
	DWORD dwRiff = *pdw++;
	DWORD dwLength = *pdw++;
	DWORD dwType = *pdw++;

	if (dwRiff != mmioFOURCC('R', 'I', 'F', 'F'))
	    return FALSE;      // このファイルはRIFFファイルではない．
	if (dwType != mmioFOURCC('W', 'A', 'V', 'E'))
	    return FALSE;      // このファイルはWave形式ファイルではない
	pdwEnd = (DWORD *)((BYTE *)pdw + dwLength-4);

	while (pdw < pdwEnd)
	{
	    dwType = *pdw++;
	    dwLength = *pdw++;

	    switch (dwType)
	    {
	    case mmioFOURCC('f', 'm', 't', ' '):
	        if (pWaveHeader == NULL)
	        {
	            if (dwLength < sizeof(WAVEFORMAT))
	                return FALSE;      // このファイルは正しいWave形式ファイルではない
	            pWaveHeader = (WAVEFORMATEX *)pdw;
	        }
	        break;
	    case mmioFOURCC('d', 'a', 't', 'a'):
	        if ((pbWaveData == NULL) || (!cbWaveSize))
	        {
	            pbWaveData = (LPBYTE)pdw;
	            cbWaveSize = dwLength;
	        }
	        break;
	    }
	    if(pWaveHeader && (pbWaveData != NULL) && cbWaveSize)
	        break;
	    pdw = (DWORD *)((BYTE *)pdw + ((dwLength+1)&~1));
	}
	if(pdwEnd <= pdw)
	    return FALSE;	// このファイルは正しいWave形式ファイルではない

	// Waveデータ取得成功

	return TRUE;
}

/*-------------------------------------------
	リソースを元にセカンダリ??バッファを作る
--------------------------------------------*/
BOOL CreateSoundData(LPCSTR pName, LPDIRECTSOUNDBUFFER &pDSData)
{
	// リソースからWaveデータを読み込む
	WAVEFORMATEX *pWaveHeader = NULL;	// WAVEFORMATEX构造体へのポインタ
	BYTE *pbWaveData = NULL;	// サウンド??データへのポインタ
	DWORD cbWaveSize = 0;	// サウンド??データのサイズ
	if(!LoadWave(pName, pWaveHeader, pbWaveData, cbWaveSize))
	{
#ifdef _DEBUG
	    MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                  //MLHIDE
#endif
		return FALSE;
	}

	// セカンダリ??バッファを作成する
	DSBUFFERDESC dsbdesc; 
	HRESULT hr; 
	// DSBUFFERDESC构造体を设定
	ZeroMemory(&dsbdesc, sizeof(DSBUFFERDESC));
	dsbdesc.dwSize = sizeof(DSBUFFERDESC); 
	// スタティック??バッファを作る
#if ACTIVE
	dsbdesc.dwFlags = DSBCAPS_STATIC | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
					| DSBCAPS_GETCURRENTPOSITION2 | DSBCAPS_GLOBALFOCUS; 
#else
	dsbdesc.dwFlags = DSBCAPS_STATIC | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
					| DSBCAPS_GETCURRENTPOSITION2; 
#endif
	dsbdesc.dwBufferBytes = cbWaveSize;
	dsbdesc.lpwfxFormat = pWaveHeader;
	// バッファを作る
	hr = pDSound->CreateSoundBuffer(&dsbdesc, &pDSData, NULL); 
	if(DS_OK != hr) { 
	    // バッファの作成に失败
		return FALSE;
	} 

	// セカンダリ??バッファにWaveデータを书き込む
	LPVOID lpvPtr1; 	// 最初のブロックのポインタ
	DWORD dwBytes1; 	// 最初のブロックのサイズ
	LPVOID lpvPtr2; 	// ２番目のブロックのポインタ
	DWORD dwBytes2; 	// ２番目のブロックのサイズ

	hr = pDSData->Lock(0, cbWaveSize, &lpvPtr1, &dwBytes1, &lpvPtr2, &dwBytes2, 0); 

	// DSERR_BUFFERLOSTが返された场合，Restoreメソッドを使ってバッファを复元する
	if(DSERR_BUFFERLOST == hr) { 
	    pDSData->Restore(); 
	    hr = pDSData->Lock(0, cbWaveSize, &lpvPtr1, &dwBytes1, &lpvPtr2, &dwBytes2, 0); 
	} 
	if(DS_OK == hr) { 
	    // ロック成功

	    // ここで，バッファに书き込む
		// バッファにデータをコピーする
		CopyMemory(lpvPtr1, pbWaveData, dwBytes1);
		if ( 0 != dwBytes2 )
		    CopyMemory(lpvPtr2, pbWaveData + dwBytes1, dwBytes2);

	   // 书き込みが終わったらすぐにUnlockする．
	    hr = pDSData->Unlock(lpvPtr1, dwBytes1, lpvPtr2, dwBytes2); 
	}

	return TRUE;
}

// ＷＡＶＥヘッダ情报読み込み
//
// 引　数：	hmmio     ... ファイルオープンハンドラ
//			pWfmtx    ... ヘッダ情报格纳领域ポインタ
//			pDataSize ... データサイズ
// 戾り值：	TRUE  ... 正常終了
//			FALSE ... 失败
BOOL WaveFormatRead( HMMIO hmmio, WAVEFORMATEX *pWfmtx, DWORD *pDataSize )
{
	MMCKINFO parent, child;

	parent.ckid = (FOURCC)0;
	parent.cksize = 0;
	parent.fccType = (FOURCC)0;
	parent.dwDataOffset = 0;
	parent.dwFlags = 0;
	child = parent;

	//WAVE检查
	parent.fccType = mmioFOURCC('W','A','V','E');
	if ( (int)mmioDescend(hmmio,&parent,NULL,MMIO_FINDRIFF) ){
//		ERRPRINT("WAVE检查");
		return FALSE;
	}

	//fmt检查
	child.ckid = mmioFOURCC('f', 'm', 't', ' ');
	if ( mmioDescend( hmmio , &child , &parent , MMIO_FINDCHUNK ) ){
//		ERRPRINT("fmt检查");
		return FALSE;
	}

	//格式化数据取得
	if (mmioRead( hmmio , (char *)pWfmtx , sizeof(WAVEFORMATEX) ) != sizeof(WAVEFORMATEX) ){
//		ERRPRINT("格式化数据取得");
		return FALSE;
	}

	//ＰＣＭフォーマットかどうかのチェック
	if (pWfmtx->wFormatTag != WAVE_FORMAT_PCM ){
//		ERRPRINT("ＰＣＭ没有格式化");
		return FALSE;
	}
	//跳出ｆｍｔ检查
	if (mmioAscend( hmmio , &child , 0 )){
//		ERRPRINT("跳出ｆｍｔ检查");
		return FALSE;
	}
	
	//data检查
	child.ckid = mmioFOURCC( 'd' , 'a' , 't' , 'a' );
	if ( mmioDescend( hmmio , &child , &parent , MMIO_FINDCHUNK ) ){
//		ERRPRINT("data检查");
		return FALSE;
	}

	*pDataSize = child.cksize;

	return TRUE;
}

static BYTE	*pDSbuffData;

//
// ＷＡＶＥデータ読み込み
//
// 引　数：	hmmio     ... ファイルオープンハンドラ
//			pDataSize ... 読み込むデータサイズ
//			pData     ... DirectSoundBuffer
// 戾り值：	TRUE  ... 正常終了
//			FALSE ... 失败
BOOL WaveDataRead( HMMIO hmmio , DWORD *pDataSize, LPDIRECTSOUNDBUFFER pData )
{
//	BYTE	*pDSbuffData;

	//サーフェースをロック
	if( pData->Lock(0 , *pDataSize , (LPVOID*)&pDSbuffData , pDataSize , NULL , 0, 0 ) != DS_OK )
		return FALSE;

	//そこにデータを流し込む
	*pDataSize = (DWORD)mmioRead(hmmio , (char*)pDSbuffData , *pDataSize);

#ifdef PUK3_BGM_REFER
	//ロック解除
	HRESULT hr = pData->Unlock( pDSbuffData , *pDataSize , NULL , 0 );
	pDSbuffData = NULL;

	return ( hr == DS_OK ? TRUE : FALSE );
#else
	//ロック解除
    if( pData->Unlock( pDSbuffData , *pDataSize , NULL , 0 ) != DS_OK )
		return FALSE;

	return TRUE;
#endif
}


//
// セカンダリバッファの取得
//
// 引　数：	pWfmtx ... バッファのフォーマット
//			DataSize ... バッファのサイズ
//			ppData ... 取得したセカンダリバッファのポインタ
// 戾り值：	TRUE  ... 正常終了
//			FALSE ... 失败
BOOL dwSoundInit( WAVEFORMATEX *pWfmtx, DWORD DataSize, LPDIRECTSOUNDBUFFER *ppData )
{
	DSBUFFERDESC  dsbufferdesc;

	//サウンドサーフェースを起こす
	memset( &dsbufferdesc , 0  , sizeof( DSBUFFERDESC ) );
	dsbufferdesc.dwSize = sizeof( DSBUFFERDESC );
//	dsbufferdesc.dwFlags = DSBCAPS_CTRLPOSITIONNOTIFY | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
//						| DSBCAPS_GETCURRENTPOSITION2;
#if ACTIVE
	dsbufferdesc.dwFlags = DSBCAPS_STATIC | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
						| DSBCAPS_GETCURRENTPOSITION2 | DSBCAPS_GLOBALFOCUS; 
#else
	dsbufferdesc.dwFlags = DSBCAPS_STATIC | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
						| DSBCAPS_GETCURRENTPOSITION2; 
#endif
    dsbufferdesc.dwBufferBytes = DataSize;
    dsbufferdesc.lpwfxFormat   = pWfmtx;
    if ( pDSound->CreateSoundBuffer( &dsbufferdesc , ppData , NULL ) != DS_OK )
		return FALSE;

	return TRUE;
}



/*-------------------------------------------
	スレッド停止处理
--------------------------------------------*/
void stop_thread( void )
{
	//スレッド停止なら
#ifdef PUK3_SOUND_CHECK_VALUE
	if ( thread_stop_flg && pDSData_tone[ TONE_MAX ] ){
#else
	if(thread_stop_flg){
#endif
		//ＢＧＭ停止
		pDSData_tone[ TONE_MAX ]->Stop();
//		pDSData_stream->Stop();
		while(1){
			if(exit_thread_flg == 1)
				break;
		}
		thread_stop_flg = 0;
		exit_thread_flg = 0;
#ifdef PUK3_BGM_REFER
		free(streamData);
		streamData = NULL;
#else
		RELEASE(pDSData_stream);
#endif
	}
	//センカダリバッファ开放
	RELEASE(pDSData_tone[TONE_MAX]);
}

/*-------------------------------------------
	ＢＧＭ停止处理
--------------------------------------------*/
void stop_bgm(void)
{
	//スレッド停止なら
	if(thread_stop_flg){
		//スレッド停止へ
		stop_thread();
	} else {
		//センカダリバッファ开放
		RELEASE(pDSData_tone[TONE_MAX]);
	}
	//フェードフラグクリア
	t_music_fade_flg = 0;
	//再生中フラグクリア
	t_music_playing_flg = 0;
	//ＢＧＭ变更チェック番号クリア
	map_bgm_vct_no = 0;
	// map_bgm_no = t_music_bgm_no;
}

/*-------------------------------------------
	ＢＧＭフェード处理
--------------------------------------------*/
void fade_out_bgm(void)
{
	if( t_music_bgm_no < 0
	 || t_music_bgm_no >= sizeof(bgm_tbl2)/sizeof(bgm_tbl2[0]) )
		return;

	//フェードアウト开始
	t_music_fade_flg = 1;
	//现在の音量保存
	t_music_fade_vol = bgm_tbl[t_music_bgm_no].volume * t_music_bgm_volume / 15;
}








/*-------------------------------------------
	ドライバー初期化处理
--------------------------------------------*/
void music_init( void )
{
	int i;
	int y;
	double d6;


	for( i = 0; i < VOICE_MAX; i++ )
	{
		voice[i].name = 0;
		voice[i].tone_no = -1;		//タスク每のプリセット音色番号
		voice[i].loop_flg = 0;
	}
	for( i = 0; i < TRACK_MAX; i++ )
	{
		dataVolume[i] = 0;
		dataPan[i] = 0;
		dataFrequency[i] = 0;
	}
	//环境音初期化
	for( i = 0; i < MAX_ENV; i++ )
	{
		env_tbl[i].voice_address = -1;
		env_tbl[i].count = 0;
	}


	//パンテーブル作成
	for( i = 64, d6 = 127; d6 >= 0; d6-- )
	{
		if( !((int)d6 & 1) )
			continue;
		y =(int)(LOG( 100,127 ) * PAN_POINT) - (int)(LOG( 100, d6 ) * PAN_POINT);
		if( y > PAN_POINT )
			y = 10000;
		panpot_tbl[i++] = y;
	}
	for( i = 64, d6 = 127; d6 >= 0; d6-- )
	{
		if( !((int)d6 & 1) )
			continue;
		y =(int)(LOG( 100, 127 ) * PAN_POINT) - (int)(LOG( 100, d6 ) * PAN_POINT);
		if( y > PAN_POINT )
			y = 10000;
		panpot_tbl[i--] = -y;
	}
	d6 = panpot_tbl[0] = -10000;
	d6 = panpot_tbl[127] = 10000;

	//ボリュームテーブル作成
	for( d6 = 127; d6 >= 0; d6-- )
	{
		y=(int)(LOG( 100, 127 ) * 10000) - (int)(LOG( 100, d6 ) * 10000);
		if( y >10000 )
			y = 10000;
		volume_tbl[(int)d6] = -y;
	}

#ifdef PUK2
	environmentSEClean ();
#endif

}








HANDLE hEvent[3];	// イベント??ハンドル
HANDLE  hThreadHandle;	// 作られたスレッドのハンドル
DWORD dwThreadID;	// 作られたスレッドのＩＤ
/*-------------------------------------------
	ストリーミング??バッファ再生用スレッド关数
--------------------------------------------*/
// スレッドが实行する关数に渡す构造体の形式
struct SoundData {
    LPDIRECTSOUNDBUFFER pDSBuffer;	// ストリーム形式のサウンド??バッファ
    HANDLE *phEvent;	// 再生カーソルが通知位置に来たときのイベント??ハンドルの配列へのポインタ
    DWORD dwEventNum;	// イベントの数
    DWORD dwStopEventNum;	// 再生停止时のイベントのハンドルのインデックス
    LPBYTE lpWaveData;	// 再生するサウンド??データ
    DWORD dwWaveSize;	// 再生するサウンド??データのサイズ
    DWORD dwBlock_size;	// サウンド??データの１ブロックの大きさ
} sd;


// データのコピー关数
void Block_Copy(LPDIRECTSOUNDBUFFER lpBuffer,
    DWORD blockadd, DWORD blocksize,
    LPBYTE lpWave, LONG &waveAdd, LONG waveSize)
{
    LPBYTE lpBlockAdd1, lpBlockAdd2;
    DWORD blockSize1, blockSize2;
	LONG ws = waveSize - waveAdd;

    // バッファをロック
    HRESULT hr = lpBuffer->Lock(blockadd, blocksize, (LPVOID*)&lpBlockAdd1, &blockSize1, (LPVOID*)&lpBlockAdd2, &blockSize2, 0);
    if(hr == DS_OK)
    {
        if(ws < (long)blockSize1)
        {
            CopyMemory(lpBlockAdd1, lpWave + waveAdd, ws);
			waveAdd = blockSize1 - ws;
            CopyMemory(lpBlockAdd1 + ws, lpWave + thread_loop_start, waveAdd);
            waveAdd += thread_loop_start;
        }
        else
        {
            CopyMemory(lpBlockAdd1, lpWave + waveAdd, blockSize1);
            waveAdd += blockSize1;
        }
        if(lpBlockAdd2)
        {
			ws = waveSize - waveAdd;
            if(ws < (long)blockSize2)
            {
                CopyMemory(lpBlockAdd1, lpWave + waveAdd, ws);
				waveAdd = blockSize2 - ws;
                CopyMemory(lpBlockAdd1 + ws, lpWave + thread_loop_start, waveAdd);
	            waveAdd += thread_loop_start;
            }
            else
            {
                CopyMemory(lpBlockAdd2, lpWave + waveAdd, blockSize2);
				waveAdd += blockSize2;
            }
        }
        lpBuffer->Unlock(lpBlockAdd1, blockSize1, lpBlockAdd2, blockSize2);
    }
}

static LONG wave_count = 0;
static LONG wave_address = 0;
static LONG buffer_address = 0;
volatile static int thread_start_flg = 0;

// スレッドが实行する关数
DWORD WINAPI MyThreadFunc(LPVOID param)
{
	LONG waveAdd = 0;
#ifdef PUK3_BGM_REFER
	// バッファの最初のブロックにデータを书き込む．
	if ( sd.lpWaveData ){
		Block_Copy(sd.pDSBuffer, 0, sd.dwBlock_size, sd.lpWaveData, waveAdd, sd.dwWaveSize);
	}
#else
    // バッファの最初のブロックにデータを书き込む．
    Block_Copy(sd.pDSBuffer, 0, sd.dwBlock_size, sd.lpWaveData, waveAdd, sd.dwWaveSize);
#endif
	thread_start_flg = 1;
    // データを书き込む无限ループに入る．
    while(TRUE)
    {
        // DirectSoundBufferからのイベントを待つ


        DWORD i = WaitForMultipleObjects(sd.dwEventNum, sd.phEvent, FALSE, INFINITE);
        // イベントがきたら，これから再生されるブロックの次のブロックに书き込む
		wave_count++;
        switch(i)
        {
            case WAIT_OBJECT_0 + 1:
                // １番目のブロックに书き込む
				buffer_address = 0; wave_address = waveAdd;
#ifdef PUK3_BGM_REFER
				if ( sd.lpWaveData ){
					Block_Copy(sd.pDSBuffer, 0, sd.dwBlock_size, sd.lpWaveData, waveAdd, sd.dwWaveSize);
				}
#else
                Block_Copy(sd.pDSBuffer, 0, sd.dwBlock_size, sd.lpWaveData, waveAdd, sd.dwWaveSize);
#endif
                break;
            case WAIT_OBJECT_0:
                // ２番目のブロックに书き込む
				buffer_address = sd.dwBlock_size; wave_address = waveAdd;
#ifdef PUK3_BGM_REFER
				if ( sd.lpWaveData ){
					Block_Copy(sd.pDSBuffer, sd.dwBlock_size, sd.dwBlock_size, sd.lpWaveData, waveAdd, sd.dwWaveSize);
				}
#else
                Block_Copy(sd.pDSBuffer, sd.dwBlock_size, sd.dwBlock_size, sd.lpWaveData, waveAdd, sd.dwWaveSize);
#endif
                break;
            case WAIT_OBJECT_0 + 2:
                 // 再生終了时に，スレッドを終了する
				exit_thread_flg = 1;
				ExitThread(TRUE);
            default:
                 // 再生終了时に，スレッドを終了する
				exit_thread_flg = 1;
				ExitThread(TRUE);
        }
//		InvalidateRect(hwndApp, NULL, TRUE);
    }
	return 0L;
}

//
// セカンダリバッファの取得
//
// 引　数：	pWfmtx ... バッファのフォーマット
//			DataSize ... バッファのサイズ
//			ppData ... 取得したセカンダリバッファのポインタ
// 戾り值：	TRUE  ... 正常終了
//			FALSE ... 失败
static BOOL dwSoundInit2( WAVEFORMATEX *pWfmtx, DWORD DataSize, LPDIRECTSOUNDBUFFER *ppData, HMMIO hmmio )
{
	// ストリーミング??セカンダリ??バッファを作る
	WAVEFORMATEX pcmwf = *pWfmtx;
#ifdef PUK3_BGM_REFER
	DWORD loadDataSize;
#else
	DSBUFFERDESC  dsbufferdesc;
#endif
	HRESULT ret;

	//开放するなら
	if(stream_flg == 1){
		RELEASE(pDSNotify);
		stream_flg = 0;
	}
#ifdef PUK3_BGM_REFER
	//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
	// 古いのが、アンロックしているセカンダリバッファのポインタを参照していたので修正
	//++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

	// サウンドデータ保存用バッファメモリ确保
	streamData = (LPBYTE)malloc( DataSize );
	if ( streamData == NULL ){
	#ifdef _DEBUG
		MessageBox( hWnd, "音乐流的缓存申请失败", "Error", MB_OK );                   //MLHIDE
	#endif
		return FALSE;
	}

	// 确保したバッファにサウンドデータを読込
	loadDataSize = (DWORD)mmioRead( hmmio, (char *)streamData, DataSize );
#else
	//サウンドサーフェースを起こす
	memset( &dsbufferdesc , 0  , sizeof( DSBUFFERDESC ) );
	dsbufferdesc.dwSize = sizeof( DSBUFFERDESC );
	dsbufferdesc.dwFlags = DSBCAPS_CTRLPOSITIONNOTIFY | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
						| DSBCAPS_GETCURRENTPOSITION2; 
//	dsbufferdesc.dwFlags = DSBCAPS_CTRLPOSITIONNOTIFY | DSBCAPS_STATIC 
//							| DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
//							| DSBCAPS_GETCURRENTPOSITION2 | DSBCAPS_GLOBALFOCUS; 
    dsbufferdesc.dwBufferBytes = DataSize;
    dsbufferdesc.lpwfxFormat   = pWfmtx;
    if ( pDSound->CreateSoundBuffer( &dsbufferdesc , ppData , NULL ) != DS_OK ){
#ifdef _DEBUG
	    MessageBox(hWnd, "二级缓存生成失败", "Error", MB_OK);                    //MLHIDE
#endif
		return FALSE;
	}


	//ＷＡＶＥデータロード
	if( WaveDataRead( hmmio, &DataSize, pDSData_stream ) == FALSE  ){
#ifdef _DEBUG
		MessageBox(hWnd, "ＷＡＶ文件打开失败", "Error", MB_OK);                      //MLHIDE
#endif
		return FALSE;
	}
#endif

	// ストリーミング??セカンダリ??バッファを作る
//	WAVEFORMATEX pcmwf = *pWaveHeader;
	DSBUFFERDESC dsbdesc; 
	// DSBUFFERDESC构造体を设定
	ZeroMemory(&dsbdesc, sizeof(DSBUFFERDESC));
	dsbdesc.dwSize = sizeof(DSBUFFERDESC); 
#if ACTIVE
	dsbdesc.dwFlags = DSBCAPS_CTRLPOSITIONNOTIFY | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
					| DSBCAPS_GETCURRENTPOSITION2 | DSBCAPS_GLOBALFOCUS; 
#else
	dsbdesc.dwFlags = DSBCAPS_CTRLPOSITIONNOTIFY | DSBCAPS_CTRLPAN | DSBCAPS_CTRLVOLUME
					| DSBCAPS_GETCURRENTPOSITION2; 
#endif
	// 上で设定したWaveフォーマットを持つ２秒のバッファを作る
	dsbdesc.dwBufferBytes = 2 * pcmwf.nAvgBytesPerSec; 
	dsbdesc.lpwfxFormat = &pcmwf; 
	ret = pDSound->CreateSoundBuffer(&dsbdesc, &pDSData_tone[ TONE_MAX ], NULL); 
	if(ret != DS_OK) { 
	    // バッファの作成に失败
//		RELEASE(pDSound);
#ifdef PUK3_BGM_REFER
		free( streamData );
#else
		RELEASE(pDSData_stream);
#endif
#ifdef _DEBUG
	    MessageBox(hWnd, "二级缓存生成失败", "Error", MB_OK);                    //MLHIDE
#endif
		return FALSE;
	}

	// DirectSoundNotifyオブジェクトを作成する
	ret = pDSData_tone[ TONE_MAX ]->QueryInterface(IID_IDirectSoundNotify, (LPVOID*)&pDSNotify);
	if(ret != DS_OK)
	{
//		RELEASE(pDSound);
#ifdef PUK3_BGM_REFER
		free( streamData );
#else
		RELEASE(pDSData_stream);
#endif
		RELEASE(pDSData_tone[ TONE_MAX ]);
#ifdef _DEBUG
	    MessageBox(hWnd, "DirectSoundNotify生成失败", "Error", MB_OK);       //MLHIDE
#endif
		return FALSE;
		//　作成に失败
	}

	// イベント??オブジェクトの作成
	hEvent[0] = CreateEvent(NULL, FALSE, FALSE, NULL);
	hEvent[1] = CreateEvent(NULL, FALSE, FALSE, NULL);
	hEvent[2] = CreateEvent(NULL, FALSE, FALSE, NULL);

	// DirectSoundNotifyオブジェクトを设定する
	// 构造体を初期化
	DSBPOSITIONNOTIFY  pn[3];
	pn[0].dwOffset = 0;	// ０秒目
	pn[0].hEventNotify = hEvent[0];
	pn[1].dwOffset = 1 * pcmwf.nAvgBytesPerSec;	// １秒目
	pn[1].hEventNotify = hEvent[1];
	pn[2].dwOffset = DSBPN_OFFSETSTOP;	// 再生停止
	pn[2].hEventNotify = hEvent[2];	// ２つ目のイベント??ハンドルを停止时のハンドルとして使う

	// 停止位置设定
	HRESULT hr;
	hr = pDSNotify->SetNotificationPositions(3, pn);
	if(hr != DS_OK)
	{
	    // 设定失败
		RELEASE(pDSNotify);
//		RELEASE(pDSound);
#ifdef PUK3_BGM_REFER
		free( streamData );
#else
		RELEASE(pDSData_stream);
#endif
		RELEASE(pDSData_tone[ TONE_MAX ]);
#ifdef _DEBUG
	    MessageBox(hWnd, "DirectSoundNotify设定失败", "Error", MB_OK);       //MLHIDE
#endif
		return FALSE;
	}

	stream_flg = 1;		//作成フラグセット

	// スレッドスタートフラグクリア
	thread_start_flg = 0;

	// 再生用スレッドを作る
	sd.pDSBuffer = pDSData_tone[ TONE_MAX ];	// ストリーミング再生するDirectSoundBufferオブジェクト
	sd.phEvent = hEvent;	// 4.4.で作ったイベント??ハンドルの配列
	sd.dwEventNum = 3;	// イベント??ハンドルは３つ
	sd.dwStopEventNum = 2;	// ３つ目のイベント??ハンドルが停止用イベント
#ifdef PUK3_BGM_REFER
	sd.lpWaveData = streamData;		// 再生するWaveデータのポインタを设定；
	sd.dwWaveSize = loadDataSize;	// 再生するWaveデータの大きさを设定；
#else
	sd.lpWaveData = pDSbuffData;	// 再生するWaveデータのポインタを设定；
	sd.dwWaveSize = DataSize;	// 再生するWaveデータの大きさを设定；
#endif
	sd.dwBlock_size = pcmwf.nAvgBytesPerSec;	// 44.1kHz，１６ビット??立体声

	// スレッドを作るコード
	hThreadHandle = CreateThread(NULL, 0, MyThreadFunc, &sd, 0, &dwThreadID);
	if(hThreadHandle == NULL)
	{
#ifdef _DEBUG
	    // スレッドの生成に失败
#ifdef _DEBUG
	    MessageBox(hWnd, "主题生成失败，请速联系程序工程师。", "Error", MB_OK);           //MLHIDE
#endif
#endif
		return FALSE;
	}

	// スレッドスタートまで待つ
	while(1){
		if(thread_start_flg)
			break;
	}

//	SetThreadPriority(hThreadHandle,THREAD_PRIORITY_HIGHEST);
//	SetThreadPriority(hThreadHandle,THREAD_PRIORITY_ABOVE_NORMAL);
//	pDSData_tone[ TONE_MAX ]->Play(0, 0, DSBPLAY_LOOPING);
	return TRUE;
}


int t_music_bgm_no = -1;
char t_music_bgm_pitch[16]={
	0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
};

//aaa
//＝＝＝＝＝＝＝＝＝＝　ＢＧＭ再生　＝＝＝＝＝＝＝＝＝＝
#define	BGM_AUTO_LOOP	1

int play_bgm(int bgm_no)
{
	HMMIO hmmio;			//ファイルハンドル
    WAVEFORMATEX	Wfmtx;			//WAVEファイルのフォーマットデータ
	DWORD			DataSize;		//バッファーのサイズ
	DWORD			WaveSize;		//Waveファイルのサイズ

//	bgm_no = 0;
#ifdef PUK2
#ifdef PUK3_BGM_SILENCE
	//同じ曲の时はリスタートさせません
	if(t_music_bgm_no == bgm_no){
		//フェードアウト中ならリスタートさせる
		if ( t_music_fade_flg == 0 ){
			return TRUE;
		}
	}
#else
	//同じ曲の时はリスタートさせません
	if(t_music_bgm_no == bgm_no)
		return TRUE;
#endif
#endif

	//フェードフラグクリア
	t_music_fade_flg = 0;

	if( soundUseFlag )
		return -1;

	//最初の一回目でなければ
	if(t_music_bgm_no != -1){
		//スレッド停止へ
		stop_thread();
	}
	
	// 无音の时返回
#ifdef PUK3_BGM_SILENCE
	if( bgm_no == -2 ){
		//再生中ＢＧＭ番号保存
		t_music_bgm_no = bgm_no;
		return -1;
	}
#else
	if( bgm_no == -2 ) return -1;
#endif
	// 鸣らせない音の时返回
	if( bgm_no >= sizeof(bgm_tbl2)/sizeof(bgm_tbl2[0]) )return -1;
	// 鸣らせない音の时返回
	if( bgm_no < 0 )return -1;
#ifdef PUK2_NOSOUNDMUSIC
	// ＢＧＭを鸣らさない时返回
	if ( !musicSoundFlag ) return -1;
#endif

	//ＷＡＶファイルのオープン
//	if( (hmmio = mmioOpen(fname[bgm_no], NULL, MMIO_READ | MMIO_ALLOCBUF )) == NULL){
	if( (hmmio = mmioOpen(bgm_tbl[bgm_no].fname, NULL, MMIO_READ | MMIO_ALLOCBUF )) == NULL){
#ifdef _DEBUG
	//	MessageBox(hWnd, "ＷＡＶ文件打开失败", "Error", MB_OK);
#endif
		return FALSE;
	}
	//アトリビュートのロード
	if( WaveFormatRead( hmmio , &Wfmtx , &WaveSize ) == FALSE ){
#ifdef _DEBUG
		MessageBox(hWnd, "属性读取失败", "Error", MB_OK);                         //MLHIDE
#endif
		return FALSE;
	}
	DataSize = WaveSize;
	//サウンドサーフェースを起こす
	if(bgm_tbl[bgm_no].loop_flg == 2){		//途中ループなら
		thread_stop_flg = 1;		//フラグON
		//ＢＧＭ情报セット
#if BGM_AUTO_LOOP
		FILE *fp;
		int d0,d1,d2;
		fp = fopen( bgm_tbl[bgm_no].fname, "rb" );                          //MLHIDE
		if( fp == NULL ){
#ifdef _DEBUG
		//	MessageBox(hWnd, "ＷＡＶ文件打开失败", "Error", MB_OK);
#endif
			return FALSE;
		}
		d2 = 8;							//’ＲＩＦＦ’８バイトスキップ
		d2 += 4;						//’ＷＡＶＥ’４バイトスキップ
		d2 += 4;						//’ｆｍｔ　’４バイトスキップ
		d2 += 16;						//１６バイトスキップ
		fseek(fp,d2,SEEK_CUR);			//ファイルポインター移动
		d0 = fgetc(fp);					//波形データの最小ブロック取り出し
		d0 |= fgetc(fp) << 8;			//
		d2 = 2;							//２バイトスキップ
		d2 += 12+DataSize;				//’ＤＡＴＡ’チャンク　＋　データサイズスキップ
		if(DataSize&1){					//サイズが奇数なら
			d2++;						//１バイト加算
		}
		d2 += 12*4;						//残り４８バイトスキップ
		fseek(fp,d2,SEEK_CUR);			//ファイルポインター移动

		//ループ开始位置
		d1 = fgetc(fp);					//ループ开始ポイント取り出し
		d1 |= fgetc(fp) << 8;			//
		d1 |= fgetc(fp) << 16;			//
		d1 |= fgetc(fp) << 24;			//
		thread_loop_start = d1 * d0;	//ループ位置セット
		//ループ終了位置
		d1 = fgetc(fp);
		d1 |= fgetc(fp) << 8;
		d1 |= fgetc(fp) << 16;
		d1 |= fgetc(fp) << 24;
//		thread_loop_end = d1 * d0;

		fclose(fp);
#else
		thread_loop_start = bgm_tbl[bgm_no].loop_point << 1;
#endif
//		thread_loop_start = bgm_tbl[bgm_no].loop_start;
//		thread_loop_end = bgm_tbl[bgm_no].loop_end;
#ifdef PUK3_BGM_REFER
		if( dwSoundInit2( &Wfmtx, DataSize, NULL, hmmio ) == FALSE ){
#else
//		if( dwSoundInit2( &Wfmtx, DataSize, &pDSData_tone[TONE_MAX], hmmio ) == FALSE ){
		if( dwSoundInit2( &Wfmtx, DataSize, &pDSData_stream, hmmio ) == FALSE ){
#endif
#ifdef _DEBUG
			MessageBox(hWnd, "二级缓存生成失败", "Error", MB_OK);                      //MLHIDE
#endif
			return FALSE;
		}
#if BGM_AUTO_LOOP
		sd.dwWaveSize = d1 * d0;		//ループ終了位置セット（もしかしたらいらんかも）
#endif
	} else {
		thread_stop_flg = 0;		//フラグOFF
		if( dwSoundInit( &Wfmtx, DataSize, &pDSData_tone[TONE_MAX] ) == FALSE ){
#ifdef _DEBUG
			MessageBox(hWnd, "二级缓存生成失败", "Error", MB_OK);                      //MLHIDE
#endif
			return FALSE;
		}
		//データのロード
		if( WaveDataRead( hmmio, &DataSize, pDSData_tone[TONE_MAX] ) == FALSE  ){
#ifdef _DEBUG
			MessageBox(hWnd, "ＷＡＶ文件打开失败", "Error", MB_OK);                     //MLHIDE
#endif
			return FALSE;
		}
	}
	mmioClose( hmmio , 0 );

	//ピッチセット
	pDSData_tone[ TONE_MAX ]->SetFrequency((DWORD)freq_tbl[36+1+t_music_bgm_pitch[bgm_no]]);
	//音量セット
	pDSData_tone[ TONE_MAX ]->SetVolume(volume_tbl[ bgm_tbl[bgm_no].volume * t_music_bgm_volume / 15 ]);		// ボリュームセット

	//ＢＧＭ再生开始
	if(bgm_tbl[bgm_no].loop_flg){		//ループなら
		pDSData_tone[ TONE_MAX ]->Play(0, 0, DSBPLAY_LOOPING);
	} else {
		pDSData_tone[ TONE_MAX ]->Play(0, 0, 0);
	}

	//再生中ＢＧＭ番号保存
	t_music_bgm_no = bgm_no;

	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif

	//再生中フラグセット
	t_music_playing_flg = 1;

	//再生中ＢＧＭ番号保存
//	map_bgm_no = t_music_bgm_no;

	return TRUE;
}

//＝＝＝＝＝＝＝＝＝＝　ＢＧＭ音量セット　＝＝＝＝＝＝＝＝＝＝
void bgm_volume_change(void)
{
	if( soundUseFlag
	 || t_music_bgm_no == -1 )
		return;
#ifdef PUK2_NOSOUNDMUSIC
	// ＢＧＭを鸣らさない时返回
	if ( !musicSoundFlag ) return;
#endif

	if(t_music_bgm_volume > 15){		//范围外なら
		t_music_bgm_volume = 15;
	}
	if(t_music_bgm_volume < 0){		//范围外なら
		t_music_bgm_volume = 0;
	}
	//音量セット
#ifdef PUK2
	if ( pDSData_tone[ TONE_MAX ] )
#endif
	pDSData_tone[ TONE_MAX ]->SetVolume(volume_tbl[ bgm_tbl[t_music_bgm_no].volume * t_music_bgm_volume / 15 ]);		// ボリュームセット
}

//＝＝＝＝＝＝＝＝＝＝　ＢＧＭピッチセット　＝＝＝＝＝＝＝＝＝＝
void set_bgm_pitch(void)
{
	if( soundUseFlag
	 || t_music_bgm_no == -1 )
		return;
#ifdef PUK2_NOSOUNDMUSIC
	// ＢＧＭを鸣らさない时返回
	if ( !musicSoundFlag ) return;
#endif

#ifdef PUK2
	if ( pDSData_tone[ TONE_MAX ] )
#endif
	//ピッチセット
	pDSData_tone[ TONE_MAX ]->SetFrequency((DWORD)freq_tbl[36+1+t_music_bgm_pitch[t_music_bgm_no]]);
}

int t_music_se_no = -1;
//＝＝＝＝＝＝＝＝＝＝　效果音再生　＝＝＝＝＝＝＝＝＝＝
//static voice_seek_point=0;
int play_se(int tone, int x, int y)
{
	int d0;
	int total_level = 127;
	t_music_se_no = tone;

	if( soundUseFlag )
		return -1;
#ifdef PUK2_NOSOUNDMUSIC
	// 效果音を鸣らさない时返回
	if ( !soundSoundFlag ) return -1;
#endif

#ifdef PUK3_SOUND_CHECK_VALUE
	// 范围外なら
	if ( tone < 0 || tone > TONE_MAX ){
#else
	if(tone > TONE_MAX){		//マックス以上なら
#endif
		return -1;
	}
	if( pDSound == NULL )return -1;

	if(tone_tbl[ tone ].voice_place == -1){		//未定义音色なら
		return -1;
	}

	if(tone_tbl[ tone ].play_time){		//既に再生中なら
		return 0;
	}

#ifdef _USE_RECENT_SOUND_LIST
	if( !add_recent_sound_list( tone)) return -1;
#endif /* _USE_RECENT_SOUND_LIST */

#ifdef PUK3_SOUND_CHECK_VALUE
	// 念のため范围修正
	voice_seek_point &= VOICE_MAX - 1;
#endif
	//前回の音色番号コピー
	d0 = voice[ voice_seek_point ].tone_no;
	if(voice[ voice_seek_point ].tone_no != tone){		//同じ音色番号でなければ
		voice[ voice_seek_point ].tone_no = tone;		//音色番号保存
		//セカンダリ??バッファの复制失败でなければ
		if(d0 != -2){
			RELEASE(pDSData[voice_seek_point]);		//开放
		}
		//音色コピー
		if( pDSound->DuplicateSoundBuffer(pDSData_tone[tone_tbl[ tone ].voice_place],&pDSData[voice_seek_point]) != DS_OK ){
#ifdef _DEBUG
			MessageBox(hWnd, "二级缓存复制失败", "Error", MB_OK);                      //MLHIDE
#endif
			//セカンダリ??バッファの复制失败フラグセット
			voice[ voice_seek_point ].tone_no = -2;
			return -1;
		}
	} else {
		pDSData[voice_seek_point]->SetCurrentPosition(0);	//再生カーソルリセット
	}
#if 0
	total_level = abs(y - 240);		//中心からのＹ座标の距离
	y = abs(x - 320) >> 1;		//中心からのＸ座标の距离
	if(total_level < y)		//远い方采用
		total_level = y;		//

	total_level = total_level << 4;		//ボリューム值に变换
	total_level /= 0x5a;				//

	if(total_level >= 127)		//范围外なら
		return 0;			//鸣らさない

	total_level = 127 - total_level;				//

#endif

	if(stereo_flg == T_MUSIC_MONO){		//单声道なら
		x = 64;
	} else {
		x /= 5;			//パンポット值に变换
		if(x < 0)		//
			x = 0;		//
		if(x > 127)		//
			x = 127;	//
	}

	//音色ボリューム
	total_level = 127 * tone_tbl[ tone ].voice_volume / 127;
//	total_level = total_level * tone_tbl[ tone ].voice_volume / 127;

	//マスターボリューム
	total_level = total_level * t_music_se_volume / 15;

	pDSData[ voice_seek_point ]->SetVolume(volume_tbl[ total_level ]);		// ボリュームセット
	pDSData[ voice_seek_point ]->SetPan(panpot_tbl[ x ]);			// パンポットセット
	if(tone_tbl[ tone ].voice_note){		//ノート番号指定なら
		pDSData[ voice_seek_point ]->SetFrequency((DWORD)freq_tbl[tone_tbl[ tone ].voice_note + tone_tbl[ tone ].voice_rate]);
	}

#if 0
	if(tone_tbl[ tone ].voice_loop == 0 ){		//ループ无しなら
		pDSData[ voice_seek_point ]->Play(0, 0, 0);			//キーON
	} else {
		voice[ voice_seek_point ].loop_flg = 1;		//ループ再生
		pDSData[ voice_seek_point ]->Play(0, 0, DSBPLAY_LOOPING);		//キーON
	}
#else
	pDSData[ voice_seek_point ]->Play(0, 0, 0);			//キーON
#endif
	while(1){
		voice_seek_point++;		//ポインター更新
		voice_seek_point&=VOICE_MAX-1;				//
		if(voice[ voice_seek_point ].loop_flg == 0){		//ループ再生中でなければ
			break;
		}
	}
	tone_tbl[ tone ].play_time = 5;		//再生中时间セット

	return 0;
}


//＝＝＝＝＝＝＝＝＝＝　环境音再生　＝＝＝＝＝＝＝＝＝＝
//int play_environment_tone(int tone, int distance, int voice_address, int panpot, int new_flg)
int play_environment_tone(int tbl_no, int new_flg)
{
	int d0;
//	int total_level;

//	t_music_se_no = tone;

	if( soundUseFlag )
		return -1;
#ifdef PUK2_NOSOUNDMUSIC
	// 效果音を鸣らさない时返回
	if ( !soundSoundFlag ) return -1;
#endif

#ifdef PUK3_SOUND_CHECK_VALUE
	// 念のため范围修正
	if ( tbl_no < 0 || MAX_ENV <= tbl_no ){
		return -1;
	}
	if ( tbl_no < 0 || env_tbl[tbl_no].tone > TONE_MAX ){		//范围外なら
#else
	if(env_tbl[tbl_no].tone > TONE_MAX){		//マックス以上なら
#endif
		return -1;
	}

	if(tone_tbl[ env_tbl[tbl_no].tone ].voice_place == -1){		//未定义音色なら
		return -1;
	}
#ifdef PUK3_SOUND_CHECK_VALUE
	if ( env_tbl[tbl_no].voice_address < 0 || env_tbl[tbl_no].voice_address > VOICE_MAX ){		//范围外なら
		return -1;
	}
#endif

#ifdef _USE_RECENT_SOUND_LIST
	if( !add_recent_sound_list( env_tbl[tbl_no].tone)) return -1;
#endif /* _USE_RECENT_SOUND_LIST */

//	if(tone_tbl[ env_tbl[tbl_no].tone ].play_time){		//既に再生中なら
//		return 0;
//	}

	env_tbl[tbl_no].distance = env_tbl[tbl_no].distance << 4;		//ボリューム值に变换
	env_tbl[tbl_no].distance /= 0x6a;				//

//	if(env_tbl[tbl_no].distance >= 127)				//范围外なら
//		return 0;					//鸣らさない

	if(env_tbl[tbl_no].distance >= 127)				//范围外なら
		env_tbl[tbl_no].distance = 127;					//鸣らさない

	if(new_flg){		//新规なら
		//前回の音色番号コピー
		d0 = voice[ env_tbl[tbl_no].voice_address ].tone_no;
		if(voice[ env_tbl[tbl_no].voice_address ].tone_no != env_tbl[tbl_no].tone){		//同じ音色番号でなければ
			voice[ env_tbl[tbl_no].voice_address ].tone_no = env_tbl[tbl_no].tone;		//音色番号保存
			//セカンダリ??バッファの复制失败でなければ
			if(d0 != -2){
				RELEASE(pDSData[env_tbl[tbl_no].voice_address]);		//开放
			}
			//音色コピー
			if( pDSound->DuplicateSoundBuffer(pDSData_tone[tone_tbl[ env_tbl[tbl_no].tone ].voice_place],&pDSData[env_tbl[tbl_no].voice_address]) != DS_OK ){
#ifdef _DEBUG
				MessageBox(hWnd, "二级缓存复制失败", "Error", MB_OK);                     //MLHIDE
#endif
				//セカンダリ??バッファの复制失败フラグセット
				voice[ env_tbl[tbl_no].voice_address ].tone_no = -2;
				return -1;
			}
		}
	}

	//音色ボリューム
//	total_level = (127 - env_tbl[tbl_no].volume) * tone_tbl[ env_tbl[tbl_no].tone ].voice_volume / 127;

	//マスターボリューム
//	total_level = total_level * t_music_se_volume / 15;

	//ボリュームが变わったなら
	if(env_tbl[tbl_no].volume != env_tbl[tbl_no].volume_old){
		// ボリュームセット
		pDSData[ env_tbl[tbl_no].voice_address ]->SetVolume(volume_tbl[ env_tbl[tbl_no].volume ]);
		env_tbl[tbl_no].volume_old = env_tbl[tbl_no].volume;
	}

	//パンポットが变わったなら
	if(env_tbl[tbl_no].panpot != env_tbl[tbl_no].panpot_old){
		// パンポットセット
		pDSData[ env_tbl[tbl_no].voice_address ]->SetPan(panpot_tbl[ env_tbl[tbl_no].panpot ]);
		env_tbl[tbl_no].panpot_old = env_tbl[tbl_no].panpot;
	}

	//ノート番号指定なら
	if(tone_tbl[ env_tbl[tbl_no].tone ].voice_note){
		pDSData[ env_tbl[tbl_no].voice_address ]->SetFrequency((DWORD)freq_tbl[tone_tbl[ env_tbl[tbl_no].tone ].voice_note + tone_tbl[ env_tbl[tbl_no].tone ].voice_rate]);
	}

	if(new_flg){		//新规なら
		pDSData[ env_tbl[tbl_no].voice_address ]->SetCurrentPosition(0);	//
		if(tone_tbl[ env_tbl[tbl_no].tone ].voice_loop == 0 ){		//ループ无しなら
			pDSData[ env_tbl[tbl_no].voice_address ]->Play(0, 0, 0);			//キーON
		} else {
			voice[ env_tbl[tbl_no].voice_address ].loop_flg = 1;		//ループ再生
			pDSData[ env_tbl[tbl_no].voice_address ]->Play(0, 0, DSBPLAY_LOOPING);		//キーON
		}

		while(1){
			voice_seek_point++;		//ポインター更新
			voice_seek_point&=VOICE_MAX-1;				//
			if(voice[ voice_seek_point ].loop_flg == 0){		//ループ再生中でなければ
				break;
			}
		}
	}
//	tone_tbl[ env_tbl[tbl_no].tone ].play_time = 8;		//再生中时间セット

	return 0;
}

//＝＝＝＝＝＝＝＝＝＝　マップＢＧＭチェック　＝＝＝＝＝＝＝＝＝＝
void play_map_bgm_check(void)
{
	switch(map_bgm_vct_no){
	case 0:		//ＢＧＭ变更チェック
		if(draw_map_bgm_flg){
			if(t_music_bgm_no != map_bgm_no){		//ＢＧＭ变更なら
				map_bgm_vct_no++;		//フェードアウト开始へ
				// マップＢＧＭ番号をバッファに入れる
				setUserMapBgmNo( selectPcNo );
				saveNowState();
			}
		}
		draw_map_bgm_flg = 0;
		break;

	case 1:		//ＢＧＭフェードアウト开始
		fade_out_bgm();
		map_bgm_vct_no++;		//フェードアウト終了待ちへ
		break;

	case 2:		//ＢＧＭフェードアウト終了待ち
		if(t_music_fade_flg == 0){		//フェードアウト終了なら
			map_bgm_vct_no++;		//ＢＧＭ再生へ
		}
		break;

	case 3:		//ＢＧＭ再生
		play_bgm(map_bgm_no);		//ＢＧＭ再生
		map_bgm_vct_no = 0;		//ＢＧＭ变更チェックへ
		break;
	}
}
//＝＝＝＝＝＝＝＝＝＝　マップＢＧＭ再生　＝＝＝＝＝＝＝＝＝＝
int play_map_bgm(int tone)
{
	switch( tone )
	{
		// 城の内部（リセリア城）
		case 200:
			map_bgm_no = BGM_CASTLE;
			break;
		// 城下町（ファンブルグの町）
		case 201:
			map_bgm_no = BGM_TOWN;
			break;
		// 村（セレーネ岛）
		case 202:
			map_bgm_no = BGM_VILLAGE1;
			break;
		// 村（フレイア岛）
		case 203:
			map_bgm_no = BGM_VILLAGE2;
			break;
		// 村（ソルキア岛）
		case 204:
			map_bgm_no = BGM_VILLAGE3;
			break;

		// 通常バトル
		case 205:
			map_bgm_no = BGM_BATTLE;
			break;
		// ボスバトル
		case 206:
			map_bgm_no = BGM_BATTLE_BOSS;
			break;
		// ラスボトバトル
		case 207:
			map_bgm_no = BGM_BATTLE_LAST_BOSS;
			break;
		// デュエル
		case 208:
			map_bgm_no = BGM_BATTLE_DUEL;
			break;

		// フィールド（セレーネ岛）风雪地带
		case 209:
			map_bgm_no = BGM_FIELD1;
			break;
		// フィールド（フレイア岛）草原、平原
		case 210:
			map_bgm_no = BGM_FIELD2;
			break;
		// フィールド（ソルキア岛）砂漠、干燥地带
		case 211:
			map_bgm_no = BGM_FIELD3;
			break;

		// ダンジョン（人工洞窟）
		case 212:
			map_bgm_no = BGM_DUNGEON1;
			break;
		// ダンジョン（自然洞窟）
		case 213:
			map_bgm_no = BGM_DUNGEON2;
			break;
		// 自动生成ダンジョン（人工洞窟）
		case 214:
			map_bgm_no = BGM_DUNGEON3;
			break;
		// 自动生成ダンジョン（自然洞窟）
		case 215:
			map_bgm_no = BGM_DUNGEON4;
			break;
		// ラストダンジョン
		case 216:
			map_bgm_no = BGM_DUNGEON5;
			break;

		// タイトル画面、キャラクター作成画面
		case 217:
			map_bgm_no = BGM_TITLE;
			break;
		
		// 无音の时
		case 218:
			map_bgm_no = -2;
			break;

		// 船
		case 219:
			map_bgm_no = BGM_SHIP;
			break;
		// ミネガル岛フィールド
		case 220:
			map_bgm_no = BGM_MINEGARU_FIELD;
			break;

		// ミネガル岛町/村
		case 221:
			map_bgm_no = BGM_MINEGARU_TOWN;
			break;

		// クルクス岛フィールド
		case 222:
			map_bgm_no = BGM_KURUKUSU_FIELD;
			break;

		// クルクス岛町/村
		case 223:
			map_bgm_no = BGM_KURUKUSU_TOWN;
			break;

		// EXTRABGM（ループあり）
		case 224:
			map_bgm_no = BGM_EXTRA_LOOP;
			break;

		// EXTRABGM（ループなし）
		case 225:
			map_bgm_no = BGM_EXTRA_NOLOOP;
			break;

#ifdef PUK2
		// PUK2用战闘ザコ战
		case 226:
			map_bgm_no = BGM_PUK2_BATTLE01;
			break;

		// PUK2用战闘ボス战
		case 227:
			map_bgm_no = BGM_PUK2_BATTLE02;
			break;

		// PUK2用フィールド
		case 228:
			map_bgm_no = BGM_PUK2_FIELD01;
			break;

		// PUK2用街
		case 229:
			map_bgm_no = BGM_PUK2_MATI01;
			break;

		// PUK2用神殿
		case 230:
			map_bgm_no = BGM_PUK2_SHINDEN01;
			break;
		// PUK2用山の曲
		case 231:
			map_bgm_no = BGM_PUK2_YAMA;
			break;
		// PUK2用废墟
		case 232:
			map_bgm_no = BGM_PUK2_HAIKYO;
			break;
		//PUK2用monsterの街
		case 233:
			map_bgm_no = BGM_PUK2_M_TOWN;
			break;
		//PUK2用タイトルロゴＢＧＭ
		case 234:
			map_bgm_no = BGM_PUK2_OP;
			break;
			
#endif

#ifdef PUK3
		//PUK3用バトル１
		case 235:
			map_bgm_no = BGM_PUK3_BATTLE1;
			break;
		//PUK3用バトル２
		case 236:
			map_bgm_no = BGM_PUK3_BATTLE2;
			break;
		//PUK3用ダンジョン
		case 237:
			map_bgm_no = BGM_PUK3_DUNGEON;
			break;
		//PUK3用龟
		case 238:
			map_bgm_no = BGM_PUK3_KAME;
			break;
		//PUK3用鲸
		case 239:
			map_bgm_no = BGM_PUK3_KUJIRA;
			break;
		//PUK3用云
		case 240:
			map_bgm_no = BGM_PUK3_KUMO;
			break;
		//PUK3用爱
		case 241:
			map_bgm_no = BGM_PUK3_LOVE;
			break;
		//PUK3用プレイヤーバトル
		case 242:
			map_bgm_no = BGM_PUK3_PLAYER_BATTLE;
			break;
		//PUK3用オープニング
		case 243:
			map_bgm_no = BGM_PUK3_OP;
			break;
#endif
#ifdef PUK3_NEWMUSIC
		//PUK3用オープニング
		case 244:
			map_bgm_no = BGM_PUK3_ORGEL;
			break;
#endif

	}
	
//	play_bgm(map_bgm_no);		//ＢＧＭ再生
	
	return 0;
}

//＝＝＝＝＝＝＝＝＝＝　环境音再生　＝＝＝＝＝＝＝＝＝＝
// 受け取る toneは BMP番号
int play_environment( int tone, int x, int y )
{
	int d0 = 0, d1 = 0;
	int distance,volume;
	float dx, dy;


	// 范围外なら何もしない
	if( tone < ENV_NO_START || ENV_NO_END < tone )
	{
		return 0;
	}

#if 0
	// 时间で鸣らす音を制御する
	switch( tone )
	{
		// 小鸟のさえずり
		case 259:
			if( nrTimeZoneNo != NR_MORNING )	// 朝でなければ鸣らさない
			{
				return 0;
			}
			break;

		// 鸟の鸣き声
		case 260:
			if( nrTimeZoneNo != NR_MORNING )	// 朝でなければ鸣らさない
			{
				return 0;
			}
			break;

		// 虫の鸣き声Ａ
		case 257:
			if( nrTimeZoneNo != NR_NIGHT )		// 夜でなければ鸣らさない
			{
				return 0;
			}
			break;

		// 虫の鸣き声Ｂ
		case 258:
			if( nrTimeZoneNo != NR_NIGHT )		// 夜でなければ鸣らさない
			{
				return 0;
			}
			break;
	}
#endif


	dx = (float)(x - 320);
	dy = (float)(y - 240);

	distance = (int)sqrt( (double)(dx*dx+dy*dy) );

	volume = distance << 4;		//ボリューム值に变换
	volume /= 0x6a;				//

	if( volume >= 127 )				//范围外なら
		return 0;					//鸣らさない

	d0 = tone - ENV_NO_START;
	tone = d0 + 1;	// SE番号に变换
	if(env_tbl[d0].count == 0){		//新规なら
		//距离代入
		env_tbl[d0].distance = distance;
		volume = (127 - volume) * tone_tbl[ tone ].voice_volume / 127;
		env_tbl[d0].volume = volume * t_music_se_volume / 15;
		d1 = 1;		//采用フラグセット
		if(env_tbl[d0].voice_address == -1){		//新规なら
			env_tbl[d0].tone = tone;
		}
	} else {
		//近い方の距离代入
		if(env_tbl[d0].distance > distance){
			env_tbl[d0].distance = distance;
			volume = (127 - volume) * tone_tbl[ tone ].voice_volume / 127;
			env_tbl[d0].volume = volume * t_music_se_volume / 15;
			d1 = 1;		//采用フラグセット
		}
	}
	//采用なら
	if(d1){
		env_tbl[d0].side = x;
	}
	env_tbl[d0].count++;
	return 0;
}

//＝＝＝＝＝＝＝＝＝＝　环境音チェック　＝＝＝＝＝＝＝＝＝＝
void play_environment_check(void)
{
	int d0,d1,d3,d7;
	for(d7=0; d7<MAX_ENV; d7++){
		if(env_tbl[d7].count == 0){		//音无しなら
			if(env_tbl[d7].voice_address != -1){		//消すなら
#ifdef PUK3_SOUND_CHECK_VALUE
				if ( 0 <= env_tbl[d7].voice_address && env_tbl[d7].voice_address < VOICE_MAX ){
					pDSData[ env_tbl[d7].voice_address ]->Stop();		//キーOFF
					voice[ env_tbl[d7].voice_address ].loop_flg = 0;		//ループ再生終了
				}
#else
				pDSData[ env_tbl[d7].voice_address ]->Stop();		//キーOFF
				voice[ env_tbl[d7].voice_address ].loop_flg = 0;		//ループ再生終了
#endif
				env_tbl[d7].voice_address = -1;
			}
			continue;
		}
		if(env_tbl[d7].voice_address == -1){		//新规なら
			env_tbl[d7].voice_address = voice_seek_point;
			env_tbl[d7].volume_old = -1;
			env_tbl[d7].panpot_old = -1;
			d0 = 1;
		} else {
			d0 = 0;
		}

		if(stereo_flg == T_MUSIC_MONO){		//单声道なら
			env_tbl[d7].panpot = d1 = 64;
		} else {
			d1 = env_tbl[d7].side / 5;			//パンポット值に变换
			if(d1 < 0)			//
				d1 = 0;			//
			if(d1 > 127)		//
				d1 = 127;		//
		}

		if(d0){		//新规なら
			env_tbl[d7].panpot = d1;
		} else {
			d3 = d1 - env_tbl[d7].panpot;
			if(d3 > 2)
				d3 = 2;
			if(d3 < -2)
				d3 = -2;
			//パンポット设定
			env_tbl[d7].panpot += d3;
		}
//		play_environment_tone(env_tbl[d7].tone, env_tbl[d7].distance, env_tbl[d7].voice_address, env_tbl[d7].panpot, d0);
		play_environment_tone(d7, d0);

		env_tbl[d7].count = 0;		//カウンタークリア
	}
}

//＝＝＝＝＝＝＝＝＝＝　ＢＧＭフェードチェック　＝＝＝＝＝＝＝＝＝＝
void bgm_fade_check(void)
{
	//再生中でなければ
	if(t_music_playing_flg == 0){
		t_music_fade_flg = 0;
		return;
	}
	//フェードアウトでなければ
	if(!t_music_fade_flg){
		return;
	}
	//フェードアウト終了なら
	if(!t_music_fade_vol){
		stop_bgm();
		t_music_bgm_no = -1;
	} else {
		// ボリューム下げる
		if( pDSData_tone[ TONE_MAX ] ){	// リリースされてるかもしれない。
			pDSData_tone[ TONE_MAX ]->SetVolume(volume_tbl[ --t_music_fade_vol ]);
		}else{
			stop_bgm();
			t_music_bgm_no = -1;
			return;
		}
	}
}

//static int test_h = 320;
//static int test_v = 140;

//＝＝＝＝＝＝＝＝＝＝　效果音チェック　＝＝＝＝＝＝＝＝＝＝
void check_se_loop(void){
	int d7;

	if( soundUseFlag )
		return;
#if 0
	if(joy_con[0]&JOY_UP)
		test_v-=4;
	if(joy_con[0]&JOY_DOWN)
		test_v+=4;
	if(joy_con[0]&JOY_RIGHT)
		test_h+=4;
	if(joy_con[0]&JOY_LEFT)
		test_h-=4;
	play_environment(1, test_h, test_v);
#endif

	for(d7=0; d7<TONE_MAX ;d7++){
		if(tone_tbl[ d7 ].play_time){		//再生时间更新
			tone_tbl[ d7 ].play_time--;
		}
	}

	//环境音チェック
	play_environment_check();

	//マップＢＧＭチェック
	play_map_bgm_check();

	//ＢＧＭフェードアウトチェックへ
	bgm_fade_check();
}

















/////////////////////////////////////////////////////////////////////
//  CD-DAトラック nを演奏する
//     成功すれば trueを返す。
//     失败すれば 信息ボックスを出して falseを返す。
bool cdda_open(int n)
{
	cdda_flg = 0;
// トラック数は 2以上であるはずだ
    _ASSERT( n > 1);

//    MCI_OPEN_PARMS open  = {0};
//  MCIERROR       dwRes = {0};

// デバイスを开く
    open.lpstrDeviceType  = "cdaudio";                                //MLHIDE
    dwRes = mciSendCommand( 0, MCI_OPEN, MCI_OPEN_TYPE, (DWORD)&open);
    if ( dwRes)
    {
#ifdef _DEBUG
//		MessageBox(hWnd, "ＣＤ－ＤＡ打开失败", "确认", MB_OK);
#endif
		cdda_flg = 1;
        return FALSE;
    }

// 时间フォーマットをトラック指定にする
    MCI_SET_PARMS set;
    set.dwTimeFormat = MCI_FORMAT_TMSF;
    dwRes = mciSendCommand( open.wDeviceID, MCI_SET, 
                            MCI_SET_TIME_FORMAT, (DWORD)&set); 
    if ( dwRes)
    {
#ifdef _DEBUG
//		MessageBox(hWnd, "ＣＤ－ＤＡ打开失败", "确认", MB_OK);
#endif
		cdda_flg = 1;
        return FALSE;
    }
	return TRUE;
}
/*--------------------------------------------
			ＣＤ＿ＤＡスタート
---------------------------------------------*/
bool cdda_start(int n)
{
// 再生する
    // トラック nを演奏
    MCI_PLAY_PARMS play;
    play.dwFrom = MCI_MAKE_TMSF( n,0,0,0);
    play.dwTo   = MCI_MAKE_TMSF( n+1,0,0,0);
    dwRes = mciSendCommand( open.wDeviceID, 
                            MCI_PLAY, MCI_FROM | MCI_TO,
                            (DWORD)&play);
    if ( dwRes)
    {
#ifdef _DEBUG
//		MessageBox(hWnd, "请放入游戏ＣＤ", "确认", MB_OK);
#endif
		cdda_flg = 2;		//ＣＤＤＡスタート失败
        return FALSE;
    }
	return TRUE;
}
/*--------------------------------------------
			ＣＤ＿ＤＡ停止
---------------------------------------------*/
bool cdda_stop(void)
{
	if(cdda_flg == 1)		//ＣＤ－ＲＯＭないなら
		return TRUE;

	mciSendCommand(open.wDeviceID, MCI_STOP, 0, NULL);

// デバイスを关闭
    dwRes = mciSendCommand( open.wDeviceID, MCI_CLOSE, 0, (DWORD)NULL);
    if ( dwRes)
    {
#ifdef _DEBUG
//	    MessageBox(hWnd, "请放入游戏ＣＤ", "确认", MB_OK);
#endif
        return FALSE;
    }
	return TRUE;
}
/*--------------------------------------------
			ＣＤ＿ＤＡ再生
---------------------------------------------*/
extern	double NowTime;

bool cdda_play(int n)
{
	check_se_loop();		//效果音チェックへ

		cdda_flg = 1;		//ＣＤ－ＲＯＭないなら
		return TRUE;



	if(cdda_flg == 1)		//ＣＤ－ＲＯＭないなら
		return TRUE;

	if(cdda_no != n ){		//曲が变わったら
		cdda_flg = 0;		//フラグクリア
		cdda_check_cnt = -1;
	}

	if(cdda_flg)		//エラー中なら
		return TRUE;

	cdda_check_cnt++;
	cdda_check_cnt&=63;
	if(cdda_check_cnt)
		return TRUE;

	if(cdda_no == n ){		//再生中なら
		MCI_STATUS_PARMS mcisp;	mcisp.dwItem = MCI_STATUS_MODE;
		if(mciSendCommand(open.wDeviceID, MCI_STATUS, MCI_STATUS_ITEM, (DWORD)&mcisp)){		//异常なら
#ifdef _DEBUG
//		    MessageBox(hWnd, "请放入游戏ＣＤ", "确认", MB_OK);
#endif
			cdda_flg = 3;		//ＣＤＤＡ状态异常
			return FALSE;
		}
		if(mcisp.dwReturn == MCI_MODE_PLAY){		//再生中なら
			return TRUE;
		} else {		//止まったなら
//			cdda_start( n );
			cdda_no = Rnd( 11, 34 );
			cdda_start( cdda_no );
			// 时间の遅れ忘れさせる
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
			return TRUE;
		}
	} else {
		cdda_no = n;		//再生する
		cdda_stop();
		cdda_open( n );
		cdda_start( n );
		// 时间の遅れ忘れさせる
		NowTime = GetTickCount();
#ifdef PUK2_FPS
		NowDrawTime = NowTime;
#endif
	}
	return TRUE;
}

#ifdef _USE_RECENT_SOUND_LIST
/*--------------------------------------
 * 最近使ったサウンドリストに登録されているかチェック
 *------------------------------------*/
int usecheck_recent_sound_list( int tone)
{
	int i;

	// リストをチェックする
	for( i = 0;i < MAX_RECENT_SOUND_LIST; i++){
		if( recent_sound_list[i] == tone) return i;	// 一致すればリストのインデックスを返す
	}

	// 见つからなかった
	return -1;
}

/*--------------------------------------
 * 指定したインデックスのサウンドをリストのトップに持ってくる
 * インデックス指定が-1の场合は新规追加
 *------------------------------------*/
BOOL settop_recent_sound_list( int index, int tone)
{
	int i;
	int tmp;

	if( index >= 0){
		// リストにすでに登録されている场合
		tmp = recent_sound_list[index];
		// 指定されたインデックスのサウンド番号を一番上にセットする
		for( i = index; i > 0; i--){
			recent_sound_list[i] = recent_sound_list[i-1];
		}
		recent_sound_list[0] = tmp;
	}
	else {
		// 新规追加の场合
		for( i = MAX_RECENT_SOUND_LIST-1; i > 0; i--){
			recent_sound_list[i] = recent_sound_list[i-1];
		}
		recent_sound_list[0] = tone;
	}

	return TRUE;
}

/*--------------------------------------
 * 最近使ったサウンドリストに追加する
 *------------------------------------*/
BOOL add_recent_sound_list( int tone)
{
	int index;
	HMMIO hmmio;					// ファイルハンドル
	WAVEFORMATEX	Wfmtx;			// WAVEファイルのフォーマットデータ
	DWORD			DataSize;		// バッファーのサイズ
	DWORD			WaveSize;		// Waveファイルのサイズ

	index = usecheck_recent_sound_list( tone);
	// 见つかった场合すでにそのサウンドは読み込まれている
	if( index >= 0){
		// 优先顺位を上げる
		settop_recent_sound_list( index, tone);
		return TRUE;
	}
	// 见つからなかった场合は、リストのトップに追加し、サウンドを読み込む
	// リストの最后のサウンドを先に开放する
	if( recent_sound_list[MAX_RECENT_SOUND_LIST-1] > 0){
		pDSData_tone[ tone_tbl[ recent_sound_list[ MAX_RECENT_SOUND_LIST-1 ] ].voice_place ]->Stop();
		RELEASE( pDSData_tone[ tone_tbl[ recent_sound_list[ MAX_RECENT_SOUND_LIST-1 ] ].voice_place ] );
	}
	settop_recent_sound_list( -1, tone);
	// ＷＡＶファイルのオープン
	if( (hmmio = mmioOpen( tone_tbl[tone].filename, NULL, MMIO_READ | MMIO_ALLOCBUF ) ) == NULL )
	{
#ifdef _DEBUG
		MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                     //MLHIDE
#endif
		return FALSE;
	}
	//アトリビュートのロード
	if( WaveFormatRead( hmmio , &Wfmtx , &WaveSize ) == FALSE )
	{
#ifdef _DEBUG
		MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                     //MLHIDE
#endif
		return FALSE;
	}
	DataSize = WaveSize;
	//サウンドサーフェースを起こす
	if( dwSoundInit( &Wfmtx, DataSize, &pDSData_tone[tone_tbl[tone].voice_place] ) == FALSE )
	{
#ifdef _DEBUG
		MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                     //MLHIDE
#endif
		return FALSE;
	}
	//データのロード
	if( WaveDataRead( hmmio, &DataSize, pDSData_tone[tone_tbl[tone].voice_place] ) == FALSE )
	{
#ifdef _DEBUG
		MessageBox(hWnd, "Wave文件打开失败", "Error", MB_OK);                     //MLHIDE
#endif
		return FALSE;
	}
	mmioClose( hmmio , 0 );
	hmmio = NULL;

	return TRUE;
}


#endif /* _USE_RECENT_SOUND_LIST */

#ifdef PUK2
/*
void playEnvironmentSE (int seno, int x, int y)
{
	float gx, gy;
	
	camMapToGamen ((float)x * GRID_SIZE, (float)y * GRID_SIZE, &gx, &gy);
	play_environment (16 + ENV_NO_START - 1, (int)gx, (int)gy);
//	play_environment (seno + ENV_NO_START - 1, (int)gx, (int)gy);

}
*/

void environmentSEClean ()
{
	int i;
	for (i = 0; i < ENV_SE_MAX; ++i) {
		environmentSe[i].seno = -1;
		environmentSe[i].isPlayed = TRUE;
	}
}

void playEnvironmentSE ()
{
	int i;
	int x, y;
	int vol;

	for (i = 0; i < ENV_SE_MAX; ++i) {
		if (environmentSe[i].seno == -1)
			continue;
	x = environmentSe[i].x;
	y = environmentSe[i].y;

	vol = ((int)sqrt( (double)(x * x + y * y)) << 4) / 0x6a;

	if ((vol >= 127) || (vol < 20))
		return;					//鸣らさない

		play_environment (environmentSe[i].seno + ENV_NO_START - 1, 
			environmentSe[i].x, environmentSe[i].y);
		environmentSe[i].isPlayed = TRUE;
	}
}

	
void setEnvironmentSE (int seno, int x, int y)
{
	float gx, gy;
	int distance;

	camMapToGamen ((float)x * GRID_SIZE, (float)y * GRID_SIZE, &gx, &gy);
	distance = (int)sqrt ((gx - 320) * (gx - 320) + (gy - 240) * (gy - 240));

	if (((!environmentSe[seno].isPlayed) && (environmentSe[seno].seno != -1)) && (distance >= environmentSe[seno].distance))
			return;

	environmentSe[seno].seno = seno;
	environmentSe[seno].x = (int) gx;
	environmentSe[seno].y = (int) gy;
	environmentSe[seno].distance = distance;
	environmentSe[seno].isPlayed = FALSE;


}


#endif	// PUK2
