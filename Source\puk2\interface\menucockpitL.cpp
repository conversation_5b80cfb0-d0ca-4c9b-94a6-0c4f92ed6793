﻿//左コクピットウインドウ

void MenuWindowLeftMiddleInit(void);
int GetSortNumFromIndex(int index);

//ボーナス残ってるかフラグ
int LevelUpEventFlag;
//健康状态フラグ
int HealthEventFlag;
//新着メールフラグ
int NewMailEventFlag;
//サイズ管理フラグ
int MenuWindowLeftSizeFlag;

//点灭カウントワーク
int LampCount;

//メール履历用ワーク
int MailType;
int MailNo;


extern BYTE PackageVer;

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowLeftLarge( int mouse )
{

	//バトル时は中、小ウィンドウしか表示しない
	if( ProcNo == PROC_BATTLE ){
		wI->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_MIDDLE );
	}

	MenuWindowLeftLargeInit();

	return TRUE;
}

BOOL MenuWindowLeftLargeDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;
}

BOOL MenuWindowLeftMiddle( int mouse )
{
	
	MenuWindowLeftMiddleInit();

	return TRUE;
}

BOOL MenuWindowLeftMiddleDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;
}

BOOL MenuWindowLeftSmall( int mouse )
{


	return TRUE;
}

BOOL MenuWindowLeftSmallDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;
}

void MenuWindowLeftLargeInit(void){

	GRAPHIC_SWITCH	*Graph;
	BLT_MEMBER bm={0};
	char StrWork[256];
	int TimeBar;
	int TimeWork[3];
	int work;

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( BattleMyNo );
#endif
#ifdef PUK2
	if( ProcNo == PROC_BATTLE && BattleMyNo < BC_MAX && pActBc[BattleMyNo]!=NULL){
#else
	if( ProcNo == PROC_BATTLE && pActBc[BattleMyNo]!=NULL){
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[BattleMyNo] );
#endif
#ifdef PUK3_RIDE_BATTLE
		//LP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->hp,pActBc[BattleMyNo]->maxHp);										 //MLHIDE
		strcpy(CockNum_Lp,StrWork);

		//FP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->fp,pActBc[BattleMyNo]->maxFp);										 //MLHIDE
		strcpy(CockNum_Fp,StrWork);
#else
		//LP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->hp,pc.maxLp);										 //MLHIDE
		strcpy(CockNum_Lp,StrWork);

		//FP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->fp,pc.maxFp);										 //MLHIDE
		strcpy(CockNum_Fp,StrWork);
#endif
	}else{
		//LP
		sprintf( StrWork, "%4d/%4d", pc.lp,pc.maxLp);										             //MLHIDE
		strcpy(CockNum_Lp,StrWork);

		//FP
		sprintf( StrWork, "%4d/%4d", pc.fp,pc.maxFp);										             //MLHIDE
		strcpy(CockNum_Fp,StrWork);
	}

	//タイムバー
	TimeBar=nrTime.hour*60+nrTime.min;
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeTimeBar].Switch;
	Graph->u=(int)((float)TimeBar*240.0/(24.0*60.0));
	Graph->v=0;
	Graph->w=100;
	Graph->h=9;

	//バーストタイム
	TimeWork[0]=(pc.bt/(60*60*1000));
	work=pc.bt-TimeWork[0]*60*60*1000;
	TimeWork[1]=(work/(60*1000)/10);
	TimeWork[2]=(work/(60*1000)%10);

	//ここだけ例外的に通常と异なる表示方法をとっています
	wI->sw[EnumGraphCockpitLeftLargeBt00].Enabled=FALSE;
	wI->sw[EnumGraphCockpitLeftLargeBt01].Enabled=FALSE;
	wI->sw[EnumGraphCockpitLeftLargeBt02].Enabled=FALSE;

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
	if(pc.btFlg){
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt00].ofx-6,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0 + ( TimeWork[0] / 10 ),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt00].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0 + ( TimeWork[0] % 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt01].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt01].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0+TimeWork[1],
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt02].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt02].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0+TimeWork[2],
						 0,&bm);
	}else{
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt00].ofx-6,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0 + (TimeWork[0] / 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt00].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0 + (TimeWork[0] % 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt01].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt01].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0+TimeWork[1],
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftLargeBt02].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftLargeBt02].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0+TimeWork[2],
						 0,&bm);
	}


	//等级アップイベントフラグ
	if(CheckBonusPoint()!=6){
		LevelUpEventFlag=1;
	}else{
		LevelUpEventFlag=0;
	}

	//健康状态异常イベントフラグ
	if(pc.injuryLv || CheckPetInjury() != -1){
		HealthEventFlag=1;
	}else{
		HealthEventFlag=0;
	}

	//新着メールイベントフラグ
	if(checkNoReadMail()){
		NewMailEventFlag=1;
	}else{
		NewMailEventFlag=0;
	}

	//点灭ランプカウント
	LampCount++;
	if(LampCount>60)
		LampCount=0;

	//等级アップボーナスランプ
	if(LevelUpEventFlag){
		if(LampCount<30)
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeLevelUp].Switch)->graNo=GID_CockpitLvUpOff;
		else
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeLevelUp].Switch)->graNo=GID_CockpitLvUpOn;
	}else{
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeLevelUp].Switch)->graNo=GID_CockpitLvUpOff;
	}
	//健康状态异常ランプ
	if(HealthEventFlag){
		if(LampCount<30){
			if(pc.injuryLv == 0){
				//健康状态
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth0Off;
			}else if( pc.injuryLv <= 25 ){
				//かすりキズ
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth1Off;
			}else if( pc.injuryLv <= 50 ){
				//軽伤
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth2Off;
			}else if( pc.injuryLv <= 75 ){
				//重症
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth3Off;
			}else{
				//濒死
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth4Off;
			}
		}else{
			if(pc.injuryLv == 0){
				//健康状态
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth0On;
			}else if( pc.injuryLv <= 25 ){
				//かすりキズ
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth1On;
			}else if( pc.injuryLv <= 50 ){
				//軽伤
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth2On;
			}else if( pc.injuryLv <= 75 ){
				//重症
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth3On;
			}else{
				//濒死
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth4On;
			}
		}
	}else{
		if(pc.injuryLv == 0){
			//健康状态
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth0Off;
		}else if( pc.injuryLv <= 25 ){
			//かすりキズ
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth1Off;
		}else if( pc.injuryLv <= 50 ){
			//軽伤
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth2Off;
		}else if( pc.injuryLv <= 75 ){
			//重症
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth3Off;
		}else{
			//濒死
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeHearth].Switch)->graNo=GID_CockpitHealth4Off;
		}
	}

	//新着メール有りランプ
	if(NewMailEventFlag){
		if(LampCount<30)
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeMeail].Switch)->graNo=GID_CockpitMailOff;
		else
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeMeail].Switch)->graNo=GID_CockpitMailOn;
	}else{
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeMeail].Switch)->graNo=GID_CockpitMailOff;
	}

}

void MenuWindowLeftMiddleInit(void){

	BLT_MEMBER bm={0};
	char StrWork[256];
	int TimeWork[3];
	int work;

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( BattleMyNo );
#endif
#ifdef PUK2
	if( ProcNo == PROC_BATTLE && BattleMyNo < BC_MAX && pActBc[BattleMyNo]!=NULL){
#else
	if( ProcNo == PROC_BATTLE && pActBc[BattleMyNo]!=NULL){
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[BattleMyNo] );
#endif
#ifdef PUK3_RIDE_BATTLE
		//LP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->hp,pActBc[BattleMyNo]->maxHp);										 //MLHIDE
		strcpy(CockNum_Lp,StrWork);

		//FP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->fp,pActBc[BattleMyNo]->maxFp);										 //MLHIDE
		strcpy(CockNum_Fp,StrWork);
#else
		//LP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->hp,pc.maxLp);										 //MLHIDE
		strcpy(CockNum_Lp,StrWork);

		//FP
		sprintf( StrWork, "%4d/%4d", pActBc[BattleMyNo]->fp,pc.maxFp);										 //MLHIDE
		strcpy(CockNum_Fp,StrWork);
#endif
	}else{
		//LP
		sprintf( StrWork, "%4d/%4d", pc.lp,pc.maxLp);										             //MLHIDE
		strcpy(CockNum_Lp,StrWork);

		//FP
		sprintf( StrWork, "%4d/%4d", pc.fp,pc.maxFp);										             //MLHIDE
		strcpy(CockNum_Fp,StrWork);
	}

	//バーストタイム
	TimeWork[0]=(pc.bt/(60*60*1000));
	work=pc.bt-TimeWork[0]*60*60*1000;
	TimeWork[1]=(work/(60*1000)/10);
	TimeWork[2]=(work/(60*1000)%10);

#if 0	
	if(pc.btFlg){
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftMiddleBt00].Switch)->graNo=GID_Num_Red_0+TimeWork[0];
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftMiddleBt01].Switch)->graNo=GID_Num_Red_0+TimeWork[1];
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftMiddleBt02].Switch)->graNo=GID_Num_Red_0+TimeWork[2];
	}else{
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftMiddleBt00].Switch)->graNo=GID_Num_White_0+TimeWork[0];
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftMiddleBt01].Switch)->graNo=GID_Num_White_0+TimeWork[1];
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftMiddleBt02].Switch)->graNo=GID_Num_White_0+TimeWork[2];
	}
#else
	//ここだけ例外的に通常と异なる表示方法をとっています
	//のちにシステムから见直します。
	wI->sw[EnumGraphCockpitLeftMiddleBt00].Enabled=FALSE;
	wI->sw[EnumGraphCockpitLeftMiddleBt01].Enabled=FALSE;
	wI->sw[EnumGraphCockpitLeftMiddleBt02].Enabled=FALSE;

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
	if(pc.btFlg){
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofx-6,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0 + (TimeWork[0] / 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0 + (TimeWork[0] % 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt01].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt01].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0+TimeWork[1],
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt02].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt02].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_Red_0+TimeWork[2],
						 0,&bm);
	}else{
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofx-6,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0 + (TimeWork[0] / 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt00].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0 + (TimeWork[0] % 10),
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt01].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt01].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0+TimeWork[1],
						 0,&bm);
		StockDispBuffer( wI->wx+wI->sw[EnumGraphCockpitLeftMiddleBt02].ofx+1,
						 wI->wy+wI->sw[EnumGraphCockpitLeftMiddleBt02].ofy+3,
						 DISP_PRIO_WIN2,
						 GID_Num_White_0+TimeWork[2],
						 0,&bm);
	}
#endif

}

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
// 大→中スイッチ
BOOL MenuSwitchCockpitLeftLargeToMiddle( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	

	// このスイッチが押されたときはウィンドウ切り替え
	if( flag & MENU_MOUSE_LEFT ){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		wI->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_MIDDLE );
		MenuWindowLeftSizeFlag=1;
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;
}

// 中→小スイッチ
BOOL MenuSwitchCockpitLeftMiddleToSmall( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	

	// このスイッチが押されたときはウィンドウ切り替え
	if( flag & MENU_MOUSE_LEFT ){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		wI->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_SMALL );
		MenuWindowLeftSizeFlag=2;
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;
}

// 小→大スイッチ
BOOL MenuSwitchCockpitLeftSmallToLarge( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	

	// このスイッチが押されたときはウィンドウ切り替え
	if( flag & MENU_MOUSE_LEFT ){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		if( ProcNo == PROC_BATTLE ){
			wI->flag |= WIN_INFO_DEL;
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_MIDDLE );
			MenuWindowLeftSizeFlag=1;
		}else{
			wI->flag |= WIN_INFO_DEL;
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_LARGE );
			MenuWindowLeftSizeFlag=0;
		}
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;
}

//大用当たり判定スイッチ
BOOL MenuSwitchLargeHit( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	

	if( flag & MENU_MOUSE_OVER ){
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;
}

// フラグ各种スイッチ
BOOL MenuSwitchCockpitLeftLarge( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph;

	//状态ボタン画像设定
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphCockpitLeftLargeDu+(no-EnumBtCockpitLeftLargeDu)].Switch;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		//重なってる
		Graph->graNo=GID_CockpitDuOver+3*(no-EnumBtCockpitLeftLargeDu);
		ReturnFlag=TRUE;
	}else{
		//重なってない
		Graph->graNo=GID_CockpitDuOff+3*(no-EnumBtCockpitLeftLargeDu);
	}

	switch(no){
		case EnumBtCockpitLeftLargeDu:
			if(flag & MENU_MOUSE_OVER){
				if( pc.etcFlag & PC_ETCFLAG_DUEL ){
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_DU_ON );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_DU_OFF );
				}
			}
			//ＯＮ状态
			if( pc.etcFlag & PC_ETCFLAG_DUEL ){
				Graph->graNo=GID_CockpitDuOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				ChangeEtcFlagDu();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
		break;

		case EnumBtCockpitLeftLargeCh:
			if(flag & MENU_MOUSE_OVER){
				if( pc.etcFlag & PC_ETCFLAG_CHAT_MODE ){
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_CH_ON );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_CH_OFF );
				}
			}
			//ＯＮ状态
			if( pc.etcFlag & PC_ETCFLAG_CHAT_MODE ){
				Graph->graNo=GID_CockpitChOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				ChangeEtcFlagCh();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
		break;

		case EnumBtCockpitLeftLargeGp:
			if(flag & MENU_MOUSE_OVER){
				if( pc.etcFlag & PC_ETCFLAG_PARTY ){
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_GP_ON );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_GP_OFF );
				}
			}
			//ＯＮ状态
			if( pc.etcFlag & PC_ETCFLAG_PARTY ){
				Graph->graNo=GID_CockpitGpOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				ChangeEtcFlagPa();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
		break;

		case EnumBtCockpitLeftLargeAd:
			if(flag & MENU_MOUSE_OVER){
				if( pc.etcFlag & PC_ETCFLAG_MAIL ){
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_AD_ON );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_AD_OFF );
				}
			}
			//ＯＮ状态
			if( pc.etcFlag & PC_ETCFLAG_MAIL ){
				Graph->graNo=GID_CockpitAdOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				ChangeEtcFlagAd();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
		break;

		case EnumBtCockpitLeftLargeTr:
			if(flag & MENU_MOUSE_OVER){
				if( pc.etcFlag & PC_ETCFLAG_TRADE ){
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_TR_ON );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_TR_OFF );
				}
			}
			//ＯＮ状态
			if( pc.etcFlag & PC_ETCFLAG_TRADE ){
				Graph->graNo=GID_CockpitTrOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				ChangeEtcFlagTr();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
		break;

		case EnumBtCockpitLeftLargeGu:
			if(PackageVer>=PV_PUK2){
			//PUK2以降のユーザーの时
				if(flag & MENU_MOUSE_OVER){
					if( pc.etcFlag & PC_ETCFLAG_GUILD ){
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_GU_ON );
					}else{
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_GU_OFF );
					}
				}
				//ＯＮ状态
				if( pc.etcFlag & PC_ETCFLAG_GUILD ){
					Graph->graNo=GID_CockpitGuOn;
				}

				//押されたとき
				if( flag & MENU_MOUSE_LEFT ){
					ChangeEtcFlagGu();
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
					ReturnFlag=TRUE;
				}
			}else{
			//PUK2以前のユーザーの时
				if(flag & MENU_MOUSE_OVER){
					Graph->graNo=GID_CockpitGuOff;
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_GU_NO );
					ReturnFlag=TRUE;
				}
			}
			break;
	}

	return ReturnFlag;
}

// コクピットランプスイッチ
BOOL MenuSwitchCockpitLeftLargeLamp( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	int NoWork;
	int InjuryFlag;
	char StrWork[256];
	int PetNum;
	
	switch(no){
		//ヘルス	
		case EnumGraphCockpitLeftLargeHearth:
			InjuryFlag=CheckInjury_PUK2();
			PetNum=InjuryFlag-10;

			if(flag & MENU_MOUSE_OVER){
				if(pc.injuryLv == 0){
				//プレイヤー怪我してない时
					if(InjuryFlag!=-1){
					//モンスター怪我してる时
						if( pet[PetNum].injuryLv <= 25 ){
							//かすりキズ
							if(pet[PetNum].freeName[0]=='\0')
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_1,pet[PetNum].name);
							else
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_1,pet[PetNum].freeName);
						}else if( pet[PetNum].injuryLv <= 50 ){
							//軽伤
							if(pet[PetNum].freeName[0]=='\0')
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_2,pet[PetNum].name);
							else
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_2,pet[PetNum].freeName);
						}else if( pet[PetNum].injuryLv <= 75 ){
							//重症
							if(pet[PetNum].freeName[0]=='\0')
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_3,pet[PetNum].name);
							else
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_3,pet[PetNum].freeName);
						}else{
							//濒死
							if(pet[PetNum].freeName[0]=='\0')
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_4,pet[PetNum].name);
							else
								sprintf(StrWork,MWONELINE_LCOCKPIT_PET_HEALTH_4,pet[PetNum].freeName);
						}
						strcpy( OneLineInfoStr, StrWork );
					}else{
					//モンスターも健康状态（全员健康状态）
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_HEALTH_0 );
					}
				}else{
				//プレイヤー怪我してる时
					if( pc.injuryLv <= 25 ){
						//かすりキズ
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_HEALTH_1 );
					}else if( pc.injuryLv <= 50 ){
						//軽伤
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_HEALTH_2 );
					}else if( pc.injuryLv <= 75 ){
						//重症
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_HEALTH_3 );
					}else{
						//濒死
						strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_HEALTH_4 );
					}
				}			
			}	
	
			//谁かが怪我してる时（ヘルスランプ点灯中）
			if(HealthEventFlag){
				if( flag & MENU_MOUSE_OVER ){
					if(pc.injuryLv == 0){
						//健康状态
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitHealth0Over;
					}else if( pc.injuryLv <= 25 ){
						//かすりキズ
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitHealth1Over;
					}else if( pc.injuryLv <= 50 ){
						//軽伤
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitHealth2Over;
					}else if( pc.injuryLv <= 75 ){
						//重症
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitHealth3Over;
					}else{
						//濒死
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitHealth4Over;
					}
					ReturnFlag=TRUE;
				}
				if( flag & MENU_MOUSE_LEFT ){
					if(InjuryFlag!=-1){
						//だれか怪我してたら
						if(InjuryFlag==0){
							//プレイヤー怪我
							//状态以外の状态ウインドウがあったら消す
							if(WindowFlag[MENU_WINDOW_DETAIL].wininfo!=NULL)
								WindowFlag[MENU_WINDOW_DETAIL].wininfo->flag |= WIN_INFO_DEL;
							if(WindowFlag[MENU_WINDOW_TITLE].wininfo!=NULL)
								WindowFlag[MENU_WINDOW_TITLE].wininfo->flag |= WIN_INFO_DEL;

							//状态画面オープン
							openMenuWindow( MENU_WINDOW_STATUS, OPENMENUWINDOW_HIT, 0 );
						}else{
							//モンスター怪我
							if(WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo!=NULL)
								WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->flag |= WIN_INFO_DEL;
							if(WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo!=NULL)
								WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->flag |= WIN_INFO_DEL;

							MonsStatusNo=GetSortNumFromIndex(PetNum);
							MonsStatusAng=5;
							openMenuWindow( MENU_WINDOW_MONSTER_STATUS, OPENMENUWINDOW_HIT, 0 );
						}

					}
					ReturnFlag=TRUE;
				}
			}
			break;

		//メール
		case EnumGraphCockpitLeftLargeMeail:
			if(flag & MENU_MOUSE_OVER){
				if(NewMailEventFlag){
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_MAIL_ON );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_LCOCKPIT_MAIL_OFF );
				}
			}
			if(NewMailEventFlag){
				if( flag & MENU_MOUSE_OVER ){
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitMailOver;
					ReturnFlag=TRUE;
				}
				if( flag & MENU_MOUSE_LEFT ){

					GetNoReadMail(&MailType,&MailNo);
					if(MailType!=-1){
						switch(MailType){
						case 0:
							//通常メール
							MenuHistoryWindowType=EnumMenuWindowAddressBookTypeAddressBook;
							mailHistorySelectNo = MailNo;
							MailHistoryST.type=EnumMenuWindowAddressBookTypeAddressBook;
							openMenuWindow( MENU_WINDOW_HISTORY, OPENMENUWINDOW_HIT, 0 );
						break;
						case 1:
							//家族メール
							MenuHistoryWindowType=EnumMenuWindowAddressBookTypeGuildBook;
							mailHistorySelectNo = MailNo;
							MailHistoryST.type=EnumMenuWindowAddressBookTypeGuildBook;
							openMenuWindow( MENU_WINDOW_HISTORY, OPENMENUWINDOW_HIT, 0 );
						break;
#ifdef PUK3_PROF
						case 2:
							//ミニメール
							MenuHistoryWindowType=EnumMenuWindowAddressBookTypeMiniMail;
							mailHistorySelectNo = MailNo;
							MailHistoryST.type=EnumMenuWindowAddressBookTypeMiniMail;
							openMenuWindow( MENU_WINDOW_HISTORY, OPENMENUWINDOW_HIT, 0 );
						break;
#endif
						}
					}

					ReturnFlag=TRUE;
				}
			}
			break;

		//等级
		case EnumGraphCockpitLeftLargeLevelUp:
			NoWork=CheckBonusPoint();
			if(flag & MENU_MOUSE_OVER){
				//だれか等级アップしてる	
				if(LevelUpEventFlag){
					if(NoWork<=4){
						if(pet[sortPet[NoWork].index].freeName[0]=='\0')
							sprintf(StrWork,MWONELINE_LCOCKPIT_PET_LVUP_ON,pet[sortPet[NoWork].index].name,pet[sortPet[NoWork].index].bonusPoint);
						else
							sprintf(StrWork,MWONELINE_LCOCKPIT_PET_LVUP_ON,pet[sortPet[NoWork].index].freeName,pet[sortPet[NoWork].index].bonusPoint);
					}else{
						sprintf(StrWork,MWONELINE_LCOCKPIT_LVUP_ON,bonusPoint);
					}
				}else{
					strcpy( StrWork, MWONELINE_LCOCKPIT_LVUP_OFF );
				}
				strcpy( OneLineInfoStr, StrWork );
			}
			if(LevelUpEventFlag){
				if( flag & MENU_MOUSE_OVER ){
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_CockpitLvUpOver;
					ReturnFlag=TRUE;
				}
				if( flag & MENU_MOUSE_LEFT ){
					if(NoWork<=4){
						//モンスター等级アップ
						//等级アップしたモンスターのソートＩＤ
						MonsStatusNo=NoWork;
						//ディティール以外の状态ウインドウがあったら消す
						if(WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo!=NULL)
							WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->flag |= WIN_INFO_DEL;
						if(WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo!=NULL)
							WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->flag |= WIN_INFO_DEL;

						openMenuWindow( MENU_WINDOW_MONSTER_DETAIL, OPENMENUWINDOW_HIT, 0 );
					}else{
						//プレイヤー等级アップ
						//ディティール以外の状态ウインドウがあったら消す
						if(WindowFlag[MENU_WINDOW_STATUS].wininfo!=NULL)
							WindowFlag[MENU_WINDOW_STATUS].wininfo->flag |= WIN_INFO_DEL;
						if(WindowFlag[MENU_WINDOW_TITLE].wininfo!=NULL)
							WindowFlag[MENU_WINDOW_TITLE].wininfo->flag |= WIN_INFO_DEL;

						openMenuWindow( MENU_WINDOW_DETAIL, OPENMENUWINDOW_HIT, 0 );
					}
					ReturnFlag=TRUE;

				}
			}
			break;
	}

	return ReturnFlag;
}

//ETCフラグ变更
//デュエルフラグ
void ChangeEtcFlagDu(void){

	if( pc.etcFlag & PC_ETCFLAG_DUEL ){
		//フラグ下げる
		pc.etcFlag &= ~PC_ETCFLAG_DUEL;
	}else{
		//フラグ立てる
		pc.etcFlag |= PC_ETCFLAG_DUEL;
	}

	//サーバーに变更通知
	ChangeEtcFlagSend();

}

//チャットフラグ
void ChangeEtcFlagCh(void){

	if( pc.etcFlag & PC_ETCFLAG_CHAT_MODE ){
		//フラグ下げる
		pc.etcFlag &= ~PC_ETCFLAG_CHAT_MODE;
	}else{
		//フラグ立てる
		pc.etcFlag |= PC_ETCFLAG_CHAT_MODE;
	}

	//サーバーに变更通知
	ChangeEtcFlagSend();

}

//布ティーフラグ
void ChangeEtcFlagPa(void){

	if( pc.etcFlag & PC_ETCFLAG_PARTY ){
		//フラグ下げる
		pc.etcFlag &= ~PC_ETCFLAG_PARTY;
	}else{
		//フラグ立てる
		pc.etcFlag |= PC_ETCFLAG_PARTY;
	}

	//サーバーに变更通知
	ChangeEtcFlagSend();

}

//名刺交换フラグ
void ChangeEtcFlagAd(void){

	if( pc.etcFlag & PC_ETCFLAG_MAIL ){
		//フラグ下げる
		pc.etcFlag &= ~PC_ETCFLAG_MAIL;
	}else{
		//フラグ立てる
		pc.etcFlag |= PC_ETCFLAG_MAIL;
	}

	//サーバーに变更通知
	ChangeEtcFlagSend();

}

//トレードフラグ
void ChangeEtcFlagTr(void){

	if( pc.etcFlag & PC_ETCFLAG_TRADE ){
		//フラグ下げる
		pc.etcFlag &= ~PC_ETCFLAG_TRADE;
	}else{
		//フラグ立てる
		pc.etcFlag |= PC_ETCFLAG_TRADE;
	}

	//サーバーに变更通知
	ChangeEtcFlagSend();

}

//家族フラグ
void ChangeEtcFlagGu(void){

	if( pc.etcFlag & PC_ETCFLAG_GUILD ){
		//フラグ下げる
		pc.etcFlag &= ~PC_ETCFLAG_GUILD;
	}else{
		//フラグ立てる
		pc.etcFlag |= PC_ETCFLAG_GUILD;
	}

	//サーバーに变更通知
	ChangeEtcFlagSend();

}

//送信する
void ChangeEtcFlagSend(void){

#ifndef _DEBUG
	nrproto_FS_send( sockfd, pc.etcFlag );
#else
	if( !offlineFlag )
		nrproto_FS_send( sockfd, pc.etcFlag );
#endif

}

//怪我状态チェック
int CheckInjury_PUK2(void){

	int InjuryFlag;

	if(pc.injuryLv){
		//プレイヤー怪我
		InjuryFlag=0;
	}else{
		InjuryFlag=CheckPetInjury();
		if(InjuryFlag!=-1){
			//モンスター怪我
			InjuryFlag+=10;
		}else{
			//だれも怪我してないや
			InjuryFlag=-1;
		}
	}

	return InjuryFlag;

}

int GetSortNumFromIndex(int index){

	int i;

	for(i=0;i<5;i++){
		if(index==sortPet[i].index){
			return i;
		}
	}

	return -1;

}


