﻿//右コクピットウインドウ

void CockpitRightLargeDuel(void);
void CockpitRightLargeWatch(void);
void CockpitRightLargeGroup(void);
void CockpitRightLargeNameCard(void);
void CockpitRightLargeTrade(void);
void CockpitRightLargeGuild(void);
BOOL CockpitRightLargeAction( int no, unsigned int flag );
BOOL CockpitRightLargeMap( int no, unsigned int flag );

BOOL duelPointCheck( void );

//サイズ管理フラグ
int MenuWindowRightSizeFlag;

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------

BOOL MenuWindowRightLarge( int mouse )
{

	//バトル时表示しない
	if( ProcNo == PROC_BATTLE ){
		wI->flag |= WIN_INFO_DEL;
	}

	return TRUE;
}

BOOL MenuWindowRightDrawLarge( int mouse )
{

	displayMenuWindow();

	return TRUE;
}

BOOL MenuWindowRightSmall( int mouse )
{

	//バトル时表示しない
	if( ProcNo == PROC_BATTLE ){
		wI->flag |= WIN_INFO_DEL;
	}

	return TRUE;
}

BOOL MenuWindowRightDrawSmall( int mouse )
{

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
// 右のサイズ变更スイッチ
BOOL MenuSwitchCockpitRightLargeToSmall( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	

	// このスイッチが押されたときはウィンドウ切り替え
	if( flag & MENU_MOUSE_LEFT ){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		wI->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_WINDOW_RIGHT_COCKPIT_SMALL );
		MenuWindowRightSizeFlag=1;
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;
}

// 右のサイズ变更スイッチ
BOOL MenuSwitchCockpitRightSmallToLarge( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	

	// このスイッチが押されたときはウィンドウ切り替え
	if( flag & MENU_MOUSE_LEFT ){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		wI->flag |= WIN_INFO_DEL;
		createMenuWindow( MENU_WINDOW_RIGHT_COCKPIT_LARGE );
		MenuWindowRightSizeFlag=0;
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;
}

// 右大コクピットスイッチ
BOOL MenuSwitchCockpitRightLarge( int no, unsigned int flag )
{

	GRAPHIC_SWITCH	*Graph;
	BOOL ReturnFlag=FALSE;	

	//状态ボタン画像设定
	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		//重なってる
		Graph->graNo=GID_CockpitDuelOver+3*(no-EnumGraphCockpitDuel);
		ReturnFlag=TRUE;
	}else{
		//重なってない
		Graph->graNo=GID_CockpitDuelOff+3*(no-EnumGraphCockpitDuel);
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_CockpitDuelOn+3*(no-EnumGraphCockpitDuel);
	}


	switch(no){
		case EnumGraphCockpitDuel:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_DUEL );
			}
			if( flag & MENU_MOUSE_LEFT ){
				CockpitRightLargeDuel();
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphCockpitWatch:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_WATCH );
			}
			if( flag & MENU_MOUSE_LEFT ){
				CockpitRightLargeWatch();
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphCockpitGroup:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_GROUP );
			}
			if( flag & MENU_MOUSE_LEFT ){
				CockpitRightLargeGroup();
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphCockpitNameCard:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_CARD );
			}
			if( flag & MENU_MOUSE_LEFT ){
				CockpitRightLargeNameCard();
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphCockpitTrade:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_TRADE );
			}
			if( flag & MENU_MOUSE_LEFT ){
				CockpitRightLargeTrade();
				ReturnFlag=TRUE;
			}
			break;

		case EnumGraphCockpitGuild:
			if(PackageVer>=PV_PUK2){
				if( flag & MENU_MOUSE_OVER ){
					strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_GUILD );
				}
				if( flag & MENU_MOUSE_LEFT ){
					CockpitRightLargeGuild();
					ReturnFlag=TRUE;
				}
			}else{
				if( flag & MENU_MOUSE_OVER ){
					//重なってない
					Graph->graNo=GID_CockpitGuildOff;
					strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_GUILD_NO );
					ReturnFlag=TRUE;
				}
			}
			break;
		case EnumGraphCockpitAction:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_ACTION );
			}
			ReturnFlag=CockpitRightLargeAction(no,flag);
			break;
		case EnumGraphCockpitMap:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_RCOCKPIT_MAP );
			}
			ReturnFlag=CockpitRightLargeMap(no,flag);
			break;
	}

	return ReturnFlag;


}


void CockpitRightLargeDuel(void){

	BOOL ReturnFlag=FALSE;	
	int dx, dy;
	int Workflag;

#ifdef PUK3_LOGIN_DIR_0
	// ＰＣのデータがまだなら
	if ( pc.dir < 0 ){
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
		return;
	}
#endif
	// 布ティに入ってない时
	// または、リーダの时
	// 正面にキャラがいるかチェック
	dx = moveAddTbl[pc.dir][0];
	dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
#endif
	// 正面にキャラがいるので对战申し込み
	if( (partyModeFlag == 0
	 || (partyModeFlag == 1 && (pc.status & CHR_STATUS_LEADER) != 0))
	 && Workflag == TRUE
	 && eventWarpSendFlag == 0
	 && eventEnemySendFlag == 0
	 && sendEnFlag == 0 ){
		// 连射抑制
		if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
#ifndef _DEBUG
			nrproto_DU_send( sockfd, mapGx, mapGy );
#else
			if( !offlineFlag )
				nrproto_DU_send( sockfd, mapGx, mapGy );
#endif
			fieldBtnPushTime = GetTickCount();
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}else{
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
	}

}

void CockpitRightLargeWatch(void){

	BOOL ReturnFlag=FALSE;	
	int dx, dy;
	int Workflag;

#ifdef PUK3_LOGIN_DIR_0
	// ＰＣのデータがまだなら
	if ( pc.dir < 0 ){
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
		return;
	}
#endif
	// 布ティに入ってない时
	// または、リーダの时
	// 正面に战闘をしているキャラがいるかチェック
	dx = moveAddTbl[pc.dir][0];
	dy = moveAddTbl[pc.dir][1];
		
	// 闘技场の时
	if(duelPointCheck() == TRUE){
#ifdef PUK3_NOEXISTCHARA
		Workflag = checkCharObjPointStatus( mapGx+dx, mapGy+dy,
			CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN,
			CHR_STATUS_BATTLE | CHR_STATUS_DUEL | CHR_STATUS_WATCH );
#else
		Workflag = checkCharObjPointStatus( mapGx+dx, mapGy+dy,
			CHAROBJ_TYPE_USER_NPC, CHR_STATUS_BATTLE | CHR_STATUS_DUEL | CHR_STATUS_WATCH );
#endif
	}else{
		Workflag = 0;
	}
	// 正面に战闘をしているキャラがいる
	if( (partyModeFlag == 0
	 || (partyModeFlag == 1 && (pc.status & CHR_STATUS_LEADER) != 0))
	 && Workflag == 1
	 && eventWarpSendFlag == 0
	 && eventEnemySendFlag == 0
	 && sendEnFlag == 0 ){
		// 连射抑制
		if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
			// 战闘中の相手に観战を送る
#ifndef _DEBUG
			nrproto_LB_send( sockfd, mapGx, mapGy );
#else
			if( !offlineFlag )
				nrproto_LB_send( sockfd, mapGx, mapGy );
#endif
			fieldBtnPushTime = GetTickCount();
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}else{
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
	}

}

void CockpitRightLargeGroup(void){

	BOOL ReturnFlag=FALSE;	
	int dx, dy;
	int Workflag;

	// 布ティに入ってない时の动作
	if( partyModeFlag == 0 ){
#ifdef PUK3_LOGIN_DIR_0
		// ＰＣのデータがまだなら
		if ( pc.dir < 0 ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
			return;
		}
#endif

		// 正面に战闘をしていないキャラがいるかチェック
		dx = moveAddTbl[pc.dir][0];
		dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
		Workflag = checkCharObjPointNotStatus( mapGx+dx, mapGy+dy,
			(CHAROBJ_TYPE_USER_NPC | CHAROBJ_TYPE_PARTY_OK) | CHAROBJ_TYPE_UNKNOWN,
			CHR_STATUS_BATTLE );
#else
		Workflag = checkCharObjPointNotStatus( mapGx+dx, mapGy+dy,
			(CHAROBJ_TYPE_USER_NPC | CHAROBJ_TYPE_PARTY_OK), CHR_STATUS_BATTLE );
#endif
		// 正面にキャラがいるので仲间要求
		if( Workflag == 1
		 && eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 ){
			// 连射抑制
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
				// 谁の仲间にもなってない时（自分がリーダーでも）、仲间要求
#ifndef _DEBUG
				nrproto_PR_send( sockfd, mapGx, mapGy, 1 );
#else
				if( !offlineFlag )
					nrproto_PR_send( sockfd, mapGx, mapGy, 1 );
#endif
				fieldBtnPushTime = GetTickCount();
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}else{
	// 布ティの时の动作
#if defined(PUK3_RAG_PARTY_BREAK_UP_1) || defined(PUK3_RAG_PARTY_BREAK_UP_2)
		// 自分がリーダーで、步いてる最中なら
		if ( (pc.status & CHR_STATUS_LEADER) &&
			 (mapVx != 0 || mapVy != 0 ) ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else
		// 自分リーダーじゃないけどリーダーが步いてるなら
		if ( party[0].ptAct->bufCount != 0 ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else
#endif
		if( eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 ){
			// 连射抑制
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
				// 谁かの仲间の时、除队
#ifndef _DEBUG
				nrproto_PR_send( sockfd, mapGx, mapGy, 0 );
#else
				if( !offlineFlag )
					nrproto_PR_send( sockfd, mapGx, mapGy, 0 );
#endif
				fieldBtnPushTime = GetTickCount();
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}

}

void CockpitRightLargeNameCard(void){

	BOOL ReturnFlag=FALSE;	
	int dx, dy;
	int Workflag;

#ifdef PUK3_LOGIN_DIR_0
	// ＰＣのデータがまだなら
	if ( pc.dir < 0 ){
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
		return;
	}
#endif
	// 自分の正面にキャラがいるか调べる
	dx = moveAddTbl[pc.dir][0];
	dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
#endif
	// 布ティに入ってない时で正面にキャラがいる
	if(
	 Workflag == TRUE
	 && eventWarpSendFlag == 0
	 && eventEnemySendFlag == 0
	 && sendEnFlag == 0 ){
		// 连射抑制
		if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
			// 名刺交换
#ifndef _DEBUG
			nrproto_AAB_send( sockfd, mapGx, mapGy );
#else
			if( !offlineFlag )
				nrproto_AAB_send( sockfd, mapGx, mapGy );
#endif
			fieldBtnPushTime = GetTickCount();
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}else{
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
	}

}

void CockpitRightLargeTrade(void){

	BOOL ReturnFlag=FALSE;	
	int dx, dy;
	int Workflag, Workflag2;

#ifdef PUK3_LOGIN_DIR_0
	// ＰＣのデータがまだなら
	if ( pc.dir < 0 ){
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
		return;
	}
#endif
	// 自分の正面２マスまでにキャラがいるか调べる
	dx = moveAddTbl[pc.dir][0];
	dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
	Workflag2 = checkCharObjPoint( mapGx+dx*2, mapGy+dy*2, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
	Workflag2 = checkCharObjPoint( mapGx+dx*2, mapGy+dy*2, CHAROBJ_TYPE_USER_NPC );
#endif
	// 布ティがいるか、正面にキャラがいる
	if( partyModeFlag == 1 || Workflag == TRUE || Workflag2 == TRUE ){
		if( eventWarpSendFlag == 0
		 && eventEnemySendFlag == 0
		 && sendEnFlag == 0 ){
			// 连射抑制
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
				// トレード相手のリスト要求プロトコル送信
#ifndef _DEBUG
				nrproto_TRPL_send( sockfd );
#else
				if( !offlineFlag )
					nrproto_TRPL_send( sockfd );
#endif
				fieldBtnPushTime = GetTickCount();
			}
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}else{
		StockChatBufferLine( ML_STRING(202, "没有可交易的对象！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE );
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
	}

}

void CockpitRightLargeGuild(void){

	BOOL ReturnFlag=FALSE;	
	int dx, dy;
	int Workflag;

#ifdef PUK3_LOGIN_DIR_0
	// ＰＣのデータがまだなら
	if ( pc.dir < 0 ){
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
		return;
	}
#endif
	// 布ティに入ってない时
	// または、リーダの时
	// 正面にキャラがいるかチェック
	dx = moveAddTbl[pc.dir][0];
	dy = moveAddTbl[pc.dir][1];
#ifdef PUK3_NOEXISTCHARA
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC|CHAROBJ_TYPE_UNKNOWN );
#else
	Workflag = checkCharObjPoint( mapGx+dx, mapGy+dy, CHAROBJ_TYPE_USER_NPC );
#endif
	// 正面にキャラがいるので对战申し込み

	if( Workflag == TRUE
	 && eventWarpSendFlag == 0
	 && eventEnemySendFlag == 0
	 && sendEnFlag == 0 ){
		if(guildBook.title[guildBook.pcGuildTitleId].flag & GUILD_FLAG_INVITE){
			// 连射抑制
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
#ifndef _DEBUG
				nrproto_AGM_send( sockfd, mapGx, mapGy );
#else
				if( !offlineFlag )
					nrproto_AGM_send( sockfd, mapGx, mapGy );
#endif
					fieldBtnPushTime = GetTickCount();
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			StockChatBufferLine( ML_STRING(204, "不同意邀请！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE );
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}else{
		// ＮＧ音（短い）
		play_se( SE_NO_NG, 320, 240 );
	}
}


BOOL CockpitRightLargeAction( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//ウインドウの有无からボタンの画像变更
	if(WindowFlag[MENU_WINDOW_ACTION].wininfo!=NULL){
		//ウインドウ作成されてるので画像をＯＮに
		Graph->graNo=GID_CockpitActionOn;	
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if(WindowFlag[MENU_WINDOW_ACTION].wininfo==NULL){
			//ないので作る
			openMenuWindow( MENU_WINDOW_ACTION, OPENMENUWINDOW_HIT, 0 );
		}else{
			//あるので消す
			WindowFlag[MENU_WINDOW_ACTION].wininfo->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}
		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}

BOOL CockpitRightLargeMap( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	//ウインドウの有无からボタンの画像变更
	if(WindowFlag[MENU_WINDOW_MAP].wininfo!=NULL){
		//ウインドウ作成されてるので画像をＯＮに
		Graph->graNo=GID_CockpitMapOn;	
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if(WindowFlag[MENU_WINDOW_MAP].wininfo==NULL){
			//ないので作る
			openMapMenuWindow( 0, 0 );
		}else{
			//あるので消す
			WindowFlag[MENU_WINDOW_MAP].wininfo->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}
		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}
