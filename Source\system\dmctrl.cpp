﻿#include "../systeminc/system.h"
#ifdef DIRECT_MUSIC		//DirectMusicシステム

//#define INITGUID
#define REV_TIME	25
#define REV_DEPTH	6

#include <windows.h>
#include <math.h>
#include <stdio.h>
  #include <dmusicc.h>
#include <dmusici.h>
  #include <dmusicf.h>
#include <dmksctrl.h>
#include <dsound.h>

#include "../systeminc/t_music.h"
#include "../systeminc/dmctrl.h"
#include "../systeminc/smf_data.h"
#include "../systeminc/getdxver.h"

  #include <direct.h>

// 手动定义DirectMusic GUID (因为新版dxguid.lib不包含这些高级接口GUID)
const GUID IID_IDirectMusicLoader = {0x2ffaaca2, 0x5dca, 0x11d2, {0xaf, 0xa6, 0x0, 0xaa, 0x0, 0x24, 0xd8, 0xb6}};
const GUID IID_IDirectMusicPerformance = {0x7d43d03, 0x6523, 0x11d2, {0x87, 0x1d, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID IID_IDirectMusicSegment = {0xf96029a2, 0x4282, 0x11d2, {0x87, 0x17, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID CLSID_DirectMusicLoader = {0xd2ac2892, 0xb39b, 0x11d1, {0x87, 0x4, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID CLSID_DirectMusicPerformance = {0xd2ac2881, 0xb39b, 0x11d1, {0x87, 0x4, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID CLSID_DirectMusicSegment = {0xd2ac2882, 0xb39b, 0x11d1, {0x87, 0x4, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID GUID_PerfMasterVolume = {0xd2ac28b1, 0xb39b, 0x11d1, {0x87, 0x4, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID GUID_DefaultGMCollection = {0xf17e8673, 0xc3b4, 0x11d1, {0x87, 0xb, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};
const GUID GUID_Unload = {0xd2ac28a8, 0xb39b, 0x11d1, {0x87, 0x4, 0x0, 0x60, 0x8, 0x93, 0xb1, 0xbd}};

static IDirectMusic						*pDMusic;
static IDirectMusicPort					*pOutPort;
static IDirectMusicLoader				*pDMLoader;
static IDirectMusicCollection			*pDLSCollection;
static IDirectMusicDownloadedInstrument *pDlInst;
static IDirectMusicBuffer				*pDMBuffer;
static IReferenceClock					*pLClock;

#define RELEASE(x) 	if(x){x->Release();x=NULL;}
extern LPDIRECTSOUND pDSound;
static IDirectMusicPerformance *pPerf;
static IDirectMusicSegment *g_pMIDIseg = NULL;

static unsigned char bgm_fade_flg;
static unsigned char bgm_fade_spd;
static unsigned char bgm_fade_cnt;
static BYTE bgm_fade_data[] = {0xf0,0x7f,0x7f,4,1,0,127,0xf7};
static long dm_volume_tbl[128];
#define	LOG(a,b)	(log((double)b)/log((double)a))

// create DMObjectInstance
int dmc_InitDirectMusic( HWND hwnd )
{
	DMUS_PORTPARAMS dmpparam;
	DMUS_BUFFERDESC desc;
	GUID guidPort;
	HRESULT hr;

	//DirectXバージョン低いなら
	if( check_dx_version() ){
		return 1;
	}

	dmc_UninitDirectMusic();	// cleanup

	CoInitialize(NULL);

	hr = CoCreateInstance( CLSID_DirectMusicPerformance, NULL, CLSCTX_INPROC, IID_IDirectMusicPerformance, (void**)&pPerf );
	if( FAILED( hr ) )	return 1;

	pPerf->Init( &pDMusic, pDSound, hwnd );

	hr = CoCreateInstance( CLSID_DirectMusicLoader , NULL , CLSCTX_INPROC_SERVER , IID_IDirectMusicLoader , ( LPVOID* )&pDMLoader );
	if( FAILED( hr ) )	return 2;

	pDMLoader->EnableCache(CLSID_DirectMusicSegment, FALSE);

	hr = pDMusic->SetDirectSound( pDSound , hwnd );
	if( FAILED( hr ) )	return 3;

	ZeroMemory( &dmpparam , sizeof( DMUS_PORTPARAMS ) );
	dmpparam.dwSize = sizeof( DMUS_PORTPARAMS );

	dmpparam.dwValidParams = DMUS_PORTPARAMS_VOICES | DMUS_PORTPARAMS_SAMPLERATE;
	dmpparam.dwVoices = 64;
	dmpparam.dwSampleRate = 22050;

	hr = pDMusic->GetDefaultPort( &guidPort );
	if( FAILED( hr ) )	return 4;

	hr = pDMusic->CreatePort( guidPort , &dmpparam , &pOutPort , NULL ); 
	if( FAILED( hr ) )	return 5;

	if(FAILED(pPerf->AddPort( pOutPort ))) return FALSE;

	hr = pPerf->AssignPChannelBlock( 0, pOutPort, 1 );
	if( FAILED( hr ) )	return 6;

	hr = pOutPort->Activate( TRUE );
	if( FAILED( hr ) )	return 6;

	desc.dwSize		= sizeof( DMUS_BUFFERDESC );
	desc.guidBufferFormat = GUID_NULL;
	desc.cbBuffer	= 32;	// 28byteHeader + 4byteMIDIArea
	desc.dwFlags	= 0;

	hr = pDMusic->CreateMusicBuffer( &desc , &pDMBuffer , NULL ); 
	if( FAILED( hr ) )	return 7;

	hr = pOutPort->GetLatencyClock( &pLClock ); 
	if( FAILED( hr ) )	return 8;

	//ボリュームテーブル作成
	for( int d7 = 127; d7 >= 0; d7-- ){
		int y;
		y = (int)(LOG(100,127)*10000) - (int)(LOG(100,d7)*10000);
		if( y > 10000 )
			y = 10000;
		dm_volume_tbl[(int)d7] = -y;
	}

	//GM.DLSダウンロード
	dmc_LoadDLS2( NULL );
//	download_dls( "bin\\dls" );

	//リバーブ设定
	set_reverb( REV_TIME, REV_DEPTH );

	return 0;
}

// release DMObjectInstance
int dmc_UninitDirectMusic( void )
{
	//DMLoader开放
	dmc_releaseDMLoader();

	if( g_pMIDIseg ){
	  	if(pPerf){
			g_pMIDIseg->SetParam(GUID_Unload,-1,0,0,(void*)pPerf);
		}
		g_pMIDIseg->Release();
		g_pMIDIseg=NULL;
	}

	if( pDMBuffer != NULL )
	{
		pDMBuffer->Release();
		pDMBuffer = NULL;
	}

	if( pLClock != NULL )
	{
		pLClock->Release();
		pLClock = NULL;
	}

	if( pDlInst != NULL )
	{
		pDlInst->Release();
		pDlInst = NULL;
	}
	
	if( pDLSCollection != NULL )
	{
		pDLSCollection->Release();
		pDLSCollection = NULL;
	}

	if( pDMLoader != NULL )
	{
		pDMLoader->Release();
		pDMLoader = NULL;
	}

	if( pOutPort != NULL )
	{
		pOutPort->Activate( FALSE );
		pOutPort->Release();
		pOutPort = NULL;
	}

	if( pDMusic != NULL )
	{
		pDMusic->Release();
		pDMusic = NULL;
	}

	if( pPerf != NULL ){
		pPerf->Release();
		pPerf = NULL;
	}

	return 0;
}

//＝＝＝＝＝＝＝＝＝＝　ドライバー終了　＝＝＝＝＝＝＝＝＝＝
void end_music( void )
{
	// cleanup
	dmc_KillDLSInstrument();
	dmc_UninitDirectMusic();
}

//-------------------------------
//MIDI Device エクスクルーシブの送信
void dmc_OutExclusiveMsg( int cnt, unsigned char *buf )
{
	REFERENCE_TIME refTime;
	pLClock->GetTime( &refTime );

	IDirectMusicBuffer* pDirectMusicBufferLong;
	DMUS_BUFFERDESC descBuff;
	descBuff.dwSize = sizeof( DMUS_BUFFERDESC );
	descBuff.dwFlags = 0;
	descBuff.guidBufferFormat = GUID_NULL;
//	descBuff.cbBuffer = sizeof(DMUS_EVENTHEADER) + cnt;
	descBuff.cbBuffer = 32 + cnt;
	pDMusic->CreateMusicBuffer(&descBuff,(IDirectMusicBuffer**)&pDirectMusicBufferLong,NULL); 
	pDirectMusicBufferLong->PackUnstructured(refTime,1,cnt,buf); 
	pOutPort->PlayBuffer(pDirectMusicBufferLong);
	pDirectMusicBufferLong->Flush(); 
	pDirectMusicBufferLong->Release();
}

// send MIDIData to OutputPortDevice
//  msg ... Packed MIDI EventMessage  ( 3rdByte << 16 | 2ndByte | 1stByte )
int dmc_OutShortMsg( unsigned long msg )
{
	REFERENCE_TIME rt;
	HRESULT hr;

	hr = pLClock->GetTime( &rt );
	hr = pDMBuffer->PackStructured( rt , 1 , msg ); 
	hr = pOutPort->PlayBuffer( pDMBuffer );
	hr = pDMBuffer->Flush();
	return 0;
}

// 10000000
// send MIDIData to OutputPortDevice
//  msg ... Packed MIDI EventMessage  ( 3rdByte << 16 | 2ndByte | 1stByte )
int dmc_OutShortMsg2( REFERENCE_TIME rt, unsigned long msg )
{
	HRESULT hr;

	hr = pDMBuffer->PackStructured( rt , 1 , msg ); 
	hr = pOutPort->PlayBuffer( pDMBuffer );
	hr = pDMBuffer->Flush();
	return 0;
}

REFERENCE_TIME GetClockTime( void )
{
	HRESULT hr;
	REFERENCE_TIME rt;

	hr = pLClock->GetTime( &rt );
	return rt;
}


// Load DLSFile -> DMCollection
//
// pFile = xxx.DLS Filename
// pFile == NULL  GM.DLS Load
int dmc_LoadDLS( char *pFile )
{
	DMUS_OBJECTDESC desc;
	HRESULT hr;

	if( pDLSCollection != NULL )
	{
		pDLSCollection->Release();
		pDLSCollection = NULL;
	}

	ZeroMemory( &desc , sizeof( DMUS_OBJECTDESC ) );
	desc.dwSize		= sizeof( DMUS_OBJECTDESC );
	desc.guidClass	= CLSID_DirectMusicCollection;

	if( pFile == NULL )
	{
		desc.guidObject		= GUID_DefaultGMCollection;
		desc.dwValidData	= DMUS_OBJ_CLASS | DMUS_OBJ_OBJECT;
	}
	else
	{
		mbstowcs( desc.wszFileName , pFile , _MAX_PATH );
		desc.dwValidData	= DMUS_OBJ_CLASS | DMUS_OBJ_FILENAME | DMUS_OBJ_FULLPATH;
	}

	hr = pDMLoader->GetObject( &desc , IID_IDirectMusicCollection , ( void ** )&pDLSCollection );
	if( FAILED( hr ) )	return 1;

	return 0;
}

// download Instrument to IDirectMusicPort in pDLSCollection 
//
// patch ... MIDIPatchNumber ( PRGNum | ( LSB << 8 ) | ( MSB << 16 ) )
int dmc_SetDLSInstrument( long patch )
{
	IDirectMusicInstrument *pDMInst;
	HRESULT hr;

#if 0
	if( pDlInst != NULL )
	{
		pOutPort->UnloadInstrument( pDlInst );
		pDlInst->Release();
		pDlInst = NULL;
	}
#endif

	hr = pDLSCollection->GetInstrument( patch , &pDMInst );
	if( FAILED( hr ) )	return 1;

	hr = pOutPort->DownloadInstrument( pDMInst , &pDlInst , NULL , 0 );
	if( FAILED( hr ) )	return 2;

	pDMInst->Release();

	return 0;
}

void dmc_KillDLSInstrument( void )
{
	if( pDlInst )
	{
		pOutPort->UnloadInstrument( pDlInst );
		pDlInst->Release();
		pDlInst = NULL;
	}
}

// setup synthesizer render property
//   render period and render latency (ms)
int dmc_SetSynthsizerProperty( DWORD dwPeriod ,  DWORD dwLatency )
{
#if 0
	IKsControl *pCtrl;
	KSPROPERTY ksp1 , ksp2;
	ULONG cb;
	HRESULT hr;

	ZeroMemory( &ksp1 , sizeof( KSPROPERTY ) );
	ksp1.Set	= GUID_DMUS_PROP_WriteLatency;
	ksp1.Id		= 0;
	ksp1.Flags	= KSPROPERTY_TYPE_SET;

	ZeroMemory( &ksp2 , sizeof( KSPROPERTY ) );
	ksp2.Set	= GUID_DMUS_PROP_WritePeriod;
	ksp2.Id		= 0;
	ksp2.Flags	= KSPROPERTY_TYPE_SET;

	hr = pOutPort->QueryInterface( IID_IKsControl , ( void** )&pCtrl );
	if( FAILED( hr ) )	return 1;

	hr = pCtrl->KsProperty( &ksp1 , sizeof( KSPROPERTY ) , ( LPVOID )&dwLatency , sizeof( DWORD ) , &cb );
	hr = pCtrl->KsProperty( &ksp2 , sizeof( KSPROPERTY ) , ( LPVOID )&dwPeriod , sizeof( DWORD ) , &cb );

	pCtrl->Release();
#else
	DWORD dwEffects = 0;
	IKsControl *pControl;
	HRESULT hr = pOutPort->QueryInterface(IID_IKsControl,
	        (void**)&pControl);
	if (SUCCEEDED(hr)){
		KSPROPERTY ksp;
		ULONG cb;

		ZeroMemory(&ksp, sizeof(ksp));
		ksp.Set   = GUID_DMUS_PROP_Effects;
		ksp.Id    = 0;
		ksp.Flags = KSPROPERTY_TYPE_GET;

		pControl->KsProperty(&ksp,
		           sizeof(ksp),
		           (LPVOID)&dwEffects,
		           sizeof(dwEffects),
		           &cb);

		ZeroMemory(&ksp, sizeof(ksp));
		dwEffects = dwEffects & ~DMUS_EFFECT_REVERB;
		ksp.Set   = GUID_DMUS_PROP_Effects;
		ksp.Id    = 0;
		ksp.Flags = KSPROPERTY_TYPE_SET;

		pControl->KsProperty(&ksp,
		           sizeof(ksp),
		           (LPVOID)&dwEffects,
		           sizeof(dwEffects),
		           &cb);

		pControl->Release();
	}

#endif
	return 0;
}

//＝＝＝＝＝＝＝＝＝＝　リバーブ设定　＝＝＝＝＝＝＝＝＝＝
//fInGain 
//入力ゲイン。dB 单位で指定する (出力オーバーフローを防ぐため)。デフォルト值は 0である。

//fReverbMix 
//リバーブ ミックス。dB 单位で指定する。值が 0 の场合、100% ウェットなリバーブであることを
//意味する (ダイレクト シグナルはない)。负の值を指定すると、シグナルのウェット感は弱まる。
//系数が计算され、リバーブ ミックスの量に关系なく、全体的な出力等级がほぼ一定に保たれる。
//デフォルト值は -10.0 である。

//fReverbTime 
//リバーブ ディケイ时间。ミリ秒 (ms) 单位で指定する。デフォルト值は 1,000である。 

//fHighFreqRTRatio 
//高周波数とグローバル リバーブ时间の比率。特に明了なリバーブが必要でない限り、
//1 未满の值に设定すべきである。たとえば、fReverbTime が 1000 ms で、
//dHighFreqRTRatio が 0.1 の场合、高周波数のディケイ时间は 100 ms である。デフォルト值は 0.001。
void set_reverb( int ReverbTime, int depth )
{
	DMUS_WAVES_REVERB_PARAMS Params;

	ReverbTime *= 100;
	depth = -16 + depth;

	Params.fInGain = 0.0f;
	Params.fReverbMix = (float)depth;
	Params.fReverbTime = (float)ReverbTime;
	Params.fHighFreqRTRatio = 0.001f;

	IKsControl *pControl;
	if( pOutPort )
	{
	    // IKsControl インターフェイスのクエリ。
	    HRESULT hr = pOutPort->QueryInterface(IID_IKsControl,
	            (void**)&pControl);
	    if (SUCCEEDED(hr)) 
	    {
	        KSPROPERTY ksp;
	        ULONG cb;

	        ZeroMemory(&ksp, sizeof(ksp));
	        ksp.Set   = GUID_DMUS_PROP_WavesReverb;
	        ksp.Id    = 0;
	        ksp.Flags = KSPROPERTY_TYPE_SET;

	        pControl->KsProperty(&ksp,
	                             sizeof(ksp),
	                             (LPVOID)&Params,
	                             sizeof(Params),
	                             &cb);
	        pControl->Release();
	    }
	}
}

/*-------------------------------------------
	DLS自动设定
--------------------------------------------*/
// Load DLSFile -> DMCollection
//
// pFile = xxx.DLS Filename
// pFile == NULL  GM.DLS Load
int dmc_LoadDLS2( char *pFile )
{
	DMUS_OBJECTDESC desc;
	HRESULT hr;
	DWORD dwPatch;
	WCHAR wszName[MAX_PATH];
	DWORD dwIndex;

	if( pDLSCollection != NULL )
	{
		pDLSCollection->Release();
		pDLSCollection = NULL;
	}

	ZeroMemory( &desc , sizeof( DMUS_OBJECTDESC ) );
	desc.dwSize		= sizeof( DMUS_OBJECTDESC );
	desc.guidClass	= CLSID_DirectMusicCollection;

	if( pFile == NULL )
	{
		desc.guidObject		= GUID_DefaultGMCollection;
		desc.dwValidData	= DMUS_OBJ_CLASS | DMUS_OBJ_OBJECT;
	}
	else
	{
		mbstowcs( desc.wszFileName , pFile , _MAX_PATH );
		desc.dwValidData	= DMUS_OBJ_CLASS | DMUS_OBJ_FILENAME | DMUS_OBJ_FULLPATH;
	}

	hr = pDMLoader->GetObject( &desc , IID_IDirectMusicCollection , ( void ** )&pDLSCollection );
	if( FAILED( hr ) )	return 1;

	for (dwIndex = 0;pDLSCollection->EnumInstrument(dwIndex, &dwPatch, wszName, MAX_PATH)==S_OK ; dwIndex++)
	{
		if( dmc_SetDLSInstrument( dwPatch ) ){
			return 1;
		}
	}
	pDLSCollection->Release();
	pDLSCollection = NULL;
	return 0;
}

/*-------------------------------------------
	DMLoader开放
--------------------------------------------*/
void dmc_releaseDMLoader( void )
{
	RELEASE(pDMLoader);
}

/*--------------------------------------------
指定のディレクトリにあるＤＬＳダウンロード
--------------------------------------------*/
void download_dls( char *path )
{
	WIN32_FIND_DATA FindData;
	HANDLE fp = NULL;
	char moji[MAX_PATH];
	char f_name[MAX_PATH];

	//ファイル名作成
	strcpy( f_name, path );
	strcat( f_name, "\\*.dls" );                                         //MLHIDE
	//ファイル存在チェック
	if ( ( fp = FindFirstFile( f_name, &FindData ) ) != INVALID_HANDLE_VALUE &&
		( FindData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY ) 
		!= FILE_ATTRIBUTE_DIRECTORY ){
	} else {
		//DLS无し
		goto download_dls_900;
	}
	strcpy( moji, path );
	strcat( moji, "\\" );                                                //MLHIDE
	strcat( moji, FindData.cFileName );

	//全DLSダウンロード
	while( 1 ){
		dmc_LoadDLS2( moji );
		if( FindNextFile( fp, &FindData ) == TRUE &&
			( FindData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY ) 
			!= FILE_ATTRIBUTE_DIRECTORY ){
			strcpy( moji, path );
			strcat( moji, "\\" );                                              //MLHIDE
			strcat( moji, FindData.cFileName );
		} else {
			//終了
			break;
		}
	}

download_dls_900:
	if( fp ){
		FindClose( fp );
	}
}

//＝＝＝＝＝＝＝＝＝＝　ループポイント设定　＝＝＝＝＝＝＝＝＝＝
static void set_loop_point( int start, int end )
{
	if( g_pMIDIseg ){
		g_pMIDIseg->SetLoopPoints( start * 384, end * 384 );
	}
}

#define MULTI_TO_WIDE( x,y )  MultiByteToWideChar( CP_ACP, \
        MB_PRECOMPOSED, y, -1, x, 255 );


//--------------------------//
//	MIDIファイルの読み込み	//
//--------------------------//
BOOL LoadMIDIFile(char *fname)
{
    DMUS_OBJECTDESC ObjDesc;

	WCHAR szFileName[256];
	FILE *fp;
	char moji[256];
	int reverb_time = 30;
	int reverb_depth = 7;
	int loop_start;
	int loop_end = 0;

	if(pDMLoader==NULL) return FALSE;

	StopSgt();	//演奏の停止
	if(g_pMIDIseg){
//	  	if(pPerf) g_pMIDIseg->SetParam(GUID_Unload,-1,0,0,(void*)pPerf);
		g_pMIDIseg->Release();
		g_pMIDIseg=NULL;
	}

    MULTI_TO_WIDE(szFileName, fname);
	//MIDIファイルの読み込み
    ObjDesc.guidClass = CLSID_DirectMusicSegment;
    ObjDesc.dwSize = sizeof(DMUS_OBJECTDESC);
    wcscpy(ObjDesc.wszFileName,szFileName);
    ObjDesc.dwValidData = DMUS_OBJ_CLASS | DMUS_OBJ_FILENAME;

	HRESULT hr=pDMLoader->GetObject(&ObjDesc,
		IID_IDirectMusicSegment,(void **)&g_pMIDIseg);

    if(FAILED(hr)) return FALSE;
	if(g_pMIDIseg==NULL) return FALSE;

	//パラメータの初期化
//    g_pMIDIseg->SetParam(GUID_StandardMIDIFile,
//		-1, 0, 0, (void*)pPerf);

//    g_pMIDIseg->SetParam(GUID_Download,
//		-1, 0, 0, (void*)pPerf);

//    g_pMIDIseg->SetParam(GUID_Enable_Auto_Download,
//		-1, 0, 0, (void*)pPerf);

	//TwmParam読み込み
	if( ( fp = fopen( fname, "rb" ) ) ){                                 //MLHIDE
		fseek( fp, 0x1a ,SEEK_SET );
		//ヘッダー読み込み
		fread( moji, 1, 9, fp );
		moji[ 9 ] = 0;
		//パラメータあるなら
		if( strcmp( moji, "TwmParam(" ) == 0 ){                             //MLHIDE
			//ヘッダー読み込み
			fread( moji, 1, 32, fp );
			moji[ 32 ] = 0;
			sscanf( moji, "%d,%d,%d,%d", &reverb_time, &reverb_depth, &loop_start, &loop_end ); //MLHIDE
		}
		fclose( fp );
	}
	//リバーブ设定
	set_reverb( reverb_time, reverb_depth );
	//ループ指定あるなら
	if( loop_end ){
		//ループポイント设定
		set_loop_point( loop_start, loop_end );
	}

	//演奏缲り返し设定
	RepeatsSgt( -1 );
	//演奏开始
	PlaySgt();

	return TRUE;
}

//------------------------------//
//	メモリ内のMIDIファイル再生	//
//------------------------------//
BOOL PlaySMF( int no, int loop )
{
    DMUS_OBJECTDESC ObjDesc;

	int reverb_time = 30;
	int reverb_depth = 7;

	if(pDMLoader==NULL) return FALSE;

	StopSgt();	//演奏の停止
	if(g_pMIDIseg){
//	  	if(pPerf) g_pMIDIseg->SetParam(GUID_Unload,-1,0,0,(void*)pPerf);
		g_pMIDIseg->Release();
		g_pMIDIseg=NULL;
	}

	//MIDIファイルの読み込み
    ObjDesc.guidClass = CLSID_DirectMusicSegment;
    ObjDesc.dwSize = sizeof(DMUS_OBJECTDESC);
	switch( no ){
	case 0:
		ObjDesc.llMemLength = sizeof(smf_data00);
    	ObjDesc.pbMemData = smf_data00;
		break;
	case 1:
		ObjDesc.llMemLength = sizeof(smf_data01);
    	ObjDesc.pbMemData = smf_data01;
		break;
	default:
		return FALSE;
	}
    ObjDesc.dwValidData = DMUS_OBJ_CLASS | DMUS_OBJ_MEMORY;

	HRESULT hr=pDMLoader->GetObject(&ObjDesc,
		IID_IDirectMusicSegment,(void **)&g_pMIDIseg);

    if(FAILED(hr)) return FALSE;
	if(g_pMIDIseg==NULL) return FALSE;

	//パラメータの初期化
//    g_pMIDIseg->SetParam(GUID_StandardMIDIFile,
//		-1, 0, 0, (void*)pPerf);

//    g_pMIDIseg->SetParam(GUID_Download,
//		-1, 0, 0, (void*)pPerf);

//    g_pMIDIseg->SetParam(GUID_Enable_Auto_Download,
//		-1, 0, 0, (void*)pPerf);

	//リバーブ设定
	set_reverb( reverb_time, reverb_depth );

	//演奏缲り返し设定
	RepeatsSgt( loop );
	//演奏开始
	PlaySgt();

	return TRUE;
}

//＝＝＝＝＝＝＝＝＝＝　全体ボリュームセット　＝＝＝＝＝＝＝＝＝＝
static void set_all_volume( int vol )
{
	//全体ボリュームセット
	vol = vol * t_music_bgm_volume / 15;

	if( vol >= 0 && vol <= 127 ){
		vol = (int)dm_volume_tbl[ vol ];
		pPerf->SetGlobalParam(GUID_PerfMasterVolume, (void*)&vol, sizeof(vol) );
	}
}

//演奏开始
BOOL PlaySgt()
{
	if(pPerf==NULL) return FALSE;
	if(g_pMIDIseg==NULL) return FALSE;

	bgm_fade_flg = 0;

	//全体ボリュームセット
	set_all_volume( 127 );

	HRESULT ret=pPerf->PlaySegment(g_pMIDIseg,DMUS_SEGF_BEAT,0,NULL);
	if(FAILED(ret)) return FALSE;

	return TRUE;
}

//演奏の停止
BOOL StopSgt()
{
	if(pPerf==NULL) return FALSE;

	pPerf->Stop(NULL, NULL, 0, 0);

	return TRUE;
}

//演奏中かどうか判定
BOOL IsPlayingSgt()
{
	if(pPerf==NULL) return FALSE;
	if(g_pMIDIseg==NULL) return FALSE;

	HRESULT ret=pPerf->IsPlaying(g_pMIDIseg,NULL);

	if(ret==S_OK) return TRUE;

	return FALSE;
}

//演奏缲り返し设定
//cou=-1で、ほぼ无限（严密には65535回のループ）
BOOL RepeatsSgt(DWORD cou)
{
	if(g_pMIDIseg==NULL) return FALSE;

	g_pMIDIseg->SetRepeats( cou );

	return TRUE;
}

//＝＝＝＝＝＝＝＝＝＝　ＢＧＭフェードチェック　＝＝＝＝＝＝＝＝＝＝
BOOL check_sgt_fade( void )
{
	//フェードなら
	if( bgm_fade_flg ){
		//音量下げる时间なら
		bgm_fade_cnt--;
		if( !bgm_fade_cnt ){
			bgm_fade_cnt = bgm_fade_spd;
			bgm_fade_flg--;
			//終了なら
			if( !bgm_fade_flg ){
				StopSgt();
				return TRUE;
			} else {
				//全体ボリュームセット
				set_all_volume( bgm_fade_flg );
			}
		}
	}
	return FALSE;
}

//＝＝＝＝＝＝＝＝＝＝　ＢＧＭフェードアウトスタート　＝＝＝＝＝＝＝＝＝＝
BOOL start_sgt_fade_out( unsigned char spd )
{
	//再生中でなければ
	if( IsPlayingSgt() == FALSE ){
		return FALSE;
	}
	//フェードスタート
	bgm_fade_flg = 127;
	bgm_fade_spd = spd;
	bgm_fade_cnt = spd;
	return TRUE;
}

#endif	//DIRECT_MUSIC
