﻿#ifndef _ANIM_TBL_H_
#define _ANIM_TBL_H_

//---------------------------------------------------------
// このファイルはコンバータも通る
//
//	コンバーターは #define SPR_xxxxx の行を见て
//　  xxxxxファイルを読み込んで处理をする。
//　(例 SPR_abc は abc.sprを处理する)
//---------------------------------------------------------
//#include "anim_tbl2.h"

#ifdef MULTI_GRABIN
// アニメーション番号 -------------------------------------
#define SPRSTART			100000		// SPRデータの始まりの番号
#define SPREND				200000		// SPRデータの終わりくらいの番号
//#define SPRSTART2			300000		// SPRデータの始まりの番号
//#define SPR2OFFS			(SPRSTART2-12000)		// SPRデータの始まりの番号
//#define CHRNO_TO_SPR( a )   (( (a)>=SPRSTART2 )?((a)-SPR2OFFS):((a)-SPRSTART) )
#else
// アニメーション番号 -------------------------------------
#define SPRSTART			100000		// SPRデータの始まりの番号
#define SPREND				200000		// SPRデータの始まりの番号
#endif

#define SPRPC_START			100000		// ＰＣキャラ开始番号

// ＰＣキャラクタアニメーション番号 -----------------------
#define SPR_000ax			100000		//	斧バウ_1	○
#define SPR_000bw			100001		//	弓バウ
#define SPR_000em			100002		//	素手バウ
#define SPR_000sd			100003		//	剑バウ
#define SPR_000sf			100004		//	杖バウ
#define SPR_000sp			100005		//	枪バウ
#define SPR_001ax			100006		//	斧バウ_2	○
#define SPR_001bw			100007		//	弓バウ
#define SPR_001em			100008		//	素手バウ
#define SPR_001sd			100009		//	剑バウ
#define SPR_001sf			100010		//	杖バウ
#define SPR_001sp			100011		//	枪バウ
#define SPR_002ax			100012		//	斧バウ_3	○
#define SPR_002bw			100013		//	弓バウ
#define SPR_002em			100014		//	素手バウ
#define SPR_002sd			100015		//	剑バウ
#define SPR_002sf			100016		//	杖バウ
#define SPR_002sp			100017		//	枪バウ
#define SPR_003ax			100018		//	斧バウ_4	○
#define SPR_003bw			100019		//	弓バウ
#define SPR_003em			100020		//	素手バウ
#define SPR_003sd			100021		//	剑バウ
#define SPR_003sf			100022		//	杖バウ
#define SPR_003sp			100023		//	枪バウ

#define SPR_010ax			100025		//	斧カズ_1	○
#define SPR_010bw			100026		//	弓カズ
#define SPR_010em			100027		//	素手カズ
#define SPR_010sd			100028		//	剑カズ
#define SPR_010sf			100029		//	杖カズ
#define SPR_010sp			100030		//	枪カズ
#define SPR_011ax			100031		//	斧カズ_2	○
#define SPR_011bw			100032		//	弓カズ
#define SPR_011em			100033		//	素手カズ
#define SPR_011sd			100034		//	剑カズ
#define SPR_011sf			100035		//	杖カズ
#define SPR_011sp			100036		//	枪カズ
#define SPR_012ax			100037		//	斧カズ_3	○
#define SPR_012bw			100038		//	弓カズ
#define SPR_012em			100039		//	素手カズ
#define SPR_012sd			100040		//	剑カズ
#define SPR_012sf			100041		//	杖カズ
#define SPR_012sp			100042		//	枪カズ
#define SPR_013ax			100043		//	斧カズ_4	○
#define SPR_013bw			100044		//	弓カズ
#define SPR_013em			100045		//	素手カズ
#define SPR_013sd			100046		//	剑カズ
#define SPR_013sf			100047		//	杖カズ
#define SPR_013sp			100048		//	枪カズ

#define SPR_020ax			100050		//	斧シン＿１	○
#define SPR_020bw			100051		//	弓シン１
#define SPR_020em			100052		//	素手シン１
#define SPR_020sd			100053		//	剑シン１
#define SPR_020sf			100054		//	杖シン１
#define SPR_020sp			100055		//	枪シン１
#define SPR_021ax			100056		//	斧シン＿２	○
#define SPR_021bw			100057		//	弓シン２
#define SPR_021em			100058		//	素手シン２
#define SPR_021sd			100059		//	剑シン２
#define SPR_021sf			100060		//	杖シン２
#define SPR_021sp			100061		//	枪シン２
#define SPR_022ax			100062		//	斧シン＿３	○
#define SPR_022bw			100063		//	弓シン３
#define SPR_022em			100064		//	素手シン３
#define SPR_022sd			100065		//	剑シン３
#define SPR_022sf			100066		//	杖シン３
#define SPR_022sp			100067		//	枪シン３
#define SPR_023ax			100068		//	斧シン＿４	○
#define SPR_023bw			100069		//	弓シン４
#define SPR_023em			100070		//	素手シン４
#define SPR_023sd			100071		//	剑シン４
#define SPR_023sf			100072		//	杖シン４
#define SPR_023sp			100073		//	枪シン４

#define SPR_030ax			100075		//	斧トブ_1	○
#define SPR_030bw			100076		//	弓トブ
#define SPR_030em			100077		//	素手トブ
#define SPR_030sd			100078		//	剑トブ
#define SPR_030sf			100079		//	杖トブ
#define SPR_030sp			100080		//	枪トブ
#define SPR_031ax			100081		//	斧トブ_2	○
#define SPR_031bw			100082		//	弓トブ
#define SPR_031em			100083		//	素手トブ
#define SPR_031sd			100084		//	剑トブ
#define SPR_031sf			100085		//	杖トブ
#define SPR_031sp			100086		//	枪トブ
#define SPR_032ax			100087		//	斧トブ_3	○
#define SPR_032bw			100088		//	弓トブ
#define SPR_032em			100089		//	素手トブ
#define SPR_032sd			100090		//	剑トブ
#define SPR_032sf			100091		//	杖トブ
#define SPR_032sp			100092		//	枪トブ
#define SPR_033ax			100093		//	斧トブ_4	○
#define SPR_033bw			100094		//	弓トブ
#define SPR_033em			100095		//	素手トブ
#define SPR_033sd			100096		//	剑トブ
#define SPR_033sf			100097		//	杖トブ
#define SPR_033sp			100098		//	枪トブ

#define SPR_040ax			100100		//	斧ゲン_1	○
#define SPR_040bw			100101		//	弓ゲン
#define SPR_040em			100102		//	素手ゲン
#define SPR_040sd			100103		//	剑ゲン
#define SPR_040sf			100104		//	杖ゲン
#define SPR_040sp			100105		//	枪ゲン
#define SPR_041ax			100106		//	斧ゲン_2	○
#define SPR_041bw			100107		//	弓ゲン
#define SPR_041em			100108		//	素手ゲン
#define SPR_041sd			100109		//	剑ゲン
#define SPR_041sf			100110		//	杖ゲン
#define SPR_041sp			100111		//	枪ゲン
#define SPR_042ax			100112		//	斧ゲン_3	○
#define SPR_042bw			100113		//	弓ゲン
#define SPR_042em			100114		//	素手ゲン
#define SPR_042sd			100115		//	剑ゲン
#define SPR_042sf			100116		//	杖ゲン
#define SPR_042sp			100117		//	枪ゲン
#define SPR_043ax			100118		//	斧ゲン_4	○
#define SPR_043bw			100119		//	弓ゲン
#define SPR_043em			100120		//	素手ゲン
#define SPR_043sd			100121		//	剑ゲン
#define SPR_043sf			100122		//	杖ゲン
#define SPR_043sp			100123		//	枪ゲン

#define SPR_050ax			100125		//	斧ベイ_1	○
#define SPR_050bw			100126		//	弓ベイ
#define SPR_050em			100127		//	素手ベイ
#define SPR_050sd			100128		//	剑ベイ
#define SPR_050sf			100129		//	杖ベイ
#define SPR_050sp			100130		//	枪ベイ
#define SPR_051ax			100131		//	斧ベイ_2	○
#define SPR_051bw			100132		//	弓ベイ
#define SPR_051em			100133		//	素手ベイ
#define SPR_051sd			100134		//	剑ベイ
#define SPR_051sf			100135		//	杖ベイ
#define SPR_051sp			100136		//	枪ベイ
#define SPR_052ax			100137		//	斧ベイ_3	○
#define SPR_052bw			100138		//	弓ベイ
#define SPR_052em			100139		//	素手ベイ
#define SPR_052sd			100140		//	剑ベイ
#define SPR_052sf			100141		//	杖ベイ
#define SPR_052sp			100142		//	枪ベイ
#define SPR_053ax			100143		//	斧ベイ_4	○
#define SPR_053bw			100144		//	弓ベイ
#define SPR_053em			100145		//	素手ベイ
#define SPR_053sd			100146		//	剑ベイ
#define SPR_053sf			100147		//	杖ベイ
#define SPR_053sp			100148		//	枪ベイ

#define SPR_060ax			100150		//	斧ボグ_1	○
#define SPR_060bw			100151		//	弓ボグ
#define SPR_060em			100152		//	素手ボグ
#define SPR_060sd			100153		//	剑ボグ
#define SPR_060sf			100154		//	杖ボグ
#define SPR_060sp			100155		//	枪ボグ
#define SPR_061ax			100156		//	斧ボグ_2	○
#define SPR_061bw			100157		//	弓ボグ
#define SPR_061em			100158		//	素手ボグ
#define SPR_061sd			100159		//	剑ボグ
#define SPR_061sf			100160		//	杖ボグ
#define SPR_061sp			100161		//	枪ボグ
#define SPR_062ax			100162		//	斧ボグ_3	○
#define SPR_062bw			100163		//	弓ボグ
#define SPR_062em			100164		//	素手ボグ
#define SPR_062sd			100165		//	剑ボグ
#define SPR_062sf			100166		//	杖ボグ
#define SPR_062sp			100167		//	枪ボグ
#define SPR_063ax			100168		//	斧ボグ_4	○
#define SPR_063bw			100169		//	弓ボグ
#define SPR_063em			100170		//	素手ボグ
#define SPR_063sd			100171		//	剑ボグ
#define SPR_063sf			100172		//	杖ボグ
#define SPR_063sp			100173		//	枪ボグ

#define SPR_200ax			100250		//	斧ウル_1	○
#define SPR_200bw			100251		//	弓ウル
#define SPR_200em			100252		//	素手ウル
#define SPR_200sd			100253		//	剑ウル
#define SPR_200sf			100254		//	杖ウル
#define SPR_200sp			100255		//	枪ウル
#define SPR_201ax			100256		//	斧ウル_2	○
#define SPR_201bw			100257		//	弓ウル
#define SPR_201em			100258		//	素手ウル
#define SPR_201sd			100259		//	剑ウル
#define SPR_201sf			100260		//	杖ウル
#define SPR_201sp			100261		//	枪ウル
#define SPR_202ax			100262		//	斧ウル_3	○
#define SPR_202bw			100263		//	弓ウル
#define SPR_202em			100264		//	素手ウル
#define SPR_202sd			100265		//	剑ウル
#define SPR_202sf			100266		//	杖ウル
#define SPR_202sp			100267		//	枪ウル
#define SPR_203ax			100268		//	斧ウル_4	○
#define SPR_203bw			100269		//	弓ウル
#define SPR_203em			100270		//	素手ウル
#define SPR_203sd			100271		//	剑ウル
#define SPR_203sf			100272		//	杖ウル
#define SPR_203sp			100273		//	枪ウル

#define SPR_210ax			100275		//	斧モエ_1	○
#define SPR_210bw			100276		//	弓モエ_1
#define SPR_210em			100277		//	素手モエ_1
#define SPR_210sd			100278		//	剑モエ_1
#define SPR_210sf			100279		//	杖モエ_1
#define SPR_210sp			100280		//	枪モエ_1
#define SPR_211ax			100281		//	斧モエ_2	○
#define SPR_211bw			100282		//	弓モエ_2
#define SPR_211em			100283		//	素手モエ_2
#define SPR_211sd			100284		//	剑モエ_2
#define SPR_211sf			100285		//	杖モエ_2
#define SPR_211sp			100286		//	枪モエ_2
#define SPR_212ax			100287		//	斧モエ_3	○
#define SPR_212bw			100288		//	弓モエ_3
#define SPR_212em			100289		//	素手モエ_3
#define SPR_212sd			100290		//	剑モエ_3
#define SPR_212sf			100291		//	杖モエ_3
#define SPR_212sp			100292		//	枪モエ_3
#define SPR_213ax			100293		//	斧モエ_4	○
#define SPR_213bw			100294		//	弓モエ_4
#define SPR_213em			100295		//	素手モエ_4
#define SPR_213sd			100296		//	剑モエ_4
#define SPR_213sf			100297		//	杖モエ_4
#define SPR_213sp			100298		//	枪モエ_4

#define SPR_220ax			100300		//	斧アミ＿１	○
#define SPR_220bw			100301		//	弓アミ
#define SPR_220em			100302		//	素手アミ
#define SPR_220sd			100303		//	剑アミ
#define SPR_220sf			100304		//	杖アミ
#define SPR_220sp			100305		//	枪アミ
#define SPR_221ax			100306		//	斧アミ＿２	○
#define SPR_221bw			100307		//	弓アミ
#define SPR_221em			100308		//	素手アミ
#define SPR_221sd			100309		//	剑アミ
#define SPR_221sf			100310		//	杖アミ
#define SPR_221sp			100311		//	枪アミ
#define SPR_222ax			100312		//	斧アミ＿３	○
#define SPR_222bw			100313		//	弓アミ
#define SPR_222em			100314		//	素手アミ
#define SPR_222sd			100315		//	剑アミ
#define SPR_222sf			100316		//	杖アミ
#define SPR_222sp			100317		//	枪アミ
#define SPR_223ax			100318		//	斧アミ＿４	○
#define SPR_223bw			100319		//	弓アミ
#define SPR_223em			100320		//	素手アミ
#define SPR_223sd			100321		//	剑アミ
#define SPR_223sf			100322		//	杖アミ
#define SPR_223sp			100323		//	枪アミ

#define SPR_230ax			100325		//	斧メグ＿１	○
#define SPR_230bw			100326		//	弓メグ
#define SPR_230em			100327		//	素手メグ
#define SPR_230sd			100328		//	剑メグ
#define SPR_230sf			100329		//	杖メグ
#define SPR_230sp			100330		//	枪メグ
#define SPR_231ax			100331		//	斧メグ＿２	○
#define SPR_231bw			100332		//	弓メグ
#define SPR_231em			100333		//	素手メグ
#define SPR_231sd			100334		//	剑メグ
#define SPR_231sf			100335		//	杖メグ
#define SPR_231sp			100336		//	枪メグ
#define SPR_232ax			100337		//	斧メグ＿３	○
#define SPR_232bw			100338		//	弓メグ
#define SPR_232em			100339		//	素手メグ
#define SPR_232sd			100340		//	剑メグ
#define SPR_232sf			100341		//	杖メグ
#define SPR_232sp			100342		//	枪メグ
#define SPR_233ax			100343		//	斧メグ＿４	○
#define SPR_233bw			100344		//	弓メグ
#define SPR_233em			100345		//	素手メグ
#define SPR_233sd			100346		//	剑メグ
#define SPR_233sf			100347		//	杖メグ
#define SPR_233sp			100348		//	枪メグ

#define SPR_240ax			100350		//	斧レイ＿１	○
#define SPR_240bw			100351		//	弓レイ
#define SPR_240em			100352		//	素手レイ
#define SPR_240sd			100353		//	剑レイ
#define SPR_240sf			100354		//	杖レイ
#define SPR_240sp			100355		//	枪レイ
#define SPR_241ax			100356		//	斧レイ＿２	○
#define SPR_241bw			100357		//	弓レイ
#define SPR_241em			100358		//	素手レイ
#define SPR_241sd			100359		//	剑レイ
#define SPR_241sf			100360		//	杖レイ
#define SPR_241sp			100361		//	枪レイ
#define SPR_242ax			100362		//	斧レイ＿３	○
#define SPR_242bw			100363		//	弓レイ
#define SPR_242em			100364		//	素手レイ
#define SPR_242sd			100365		//	剑レイ
#define SPR_242sf			100366		//	杖レイ
#define SPR_242sp			100367		//	枪レイ
#define SPR_243ax			100368		//	斧レイ＿４	○
#define SPR_243bw			100369		//	弓レイ
#define SPR_243em			100370		//	素手レイ
#define SPR_243sd			100371		//	剑レイ
#define SPR_243sf			100372		//	杖レイ
#define SPR_243sp			100373		//	枪レイ

#define SPR_250ax			100375		//	斧ケイ_1	○
#define SPR_250bw			100376		//	弓ケイ
#define SPR_250em			100377		//	素手ケイ
#define SPR_250sd			100378		//	剑ケイ
#define SPR_250sf			100379		//	杖ケイ
#define SPR_250sp			100380		//	枪ケイ
#define SPR_251ax			100381		//	斧ケイ_2	○
#define SPR_251bw			100382		//	弓ケイ
#define SPR_251em			100383		//	素手ケイ
#define SPR_251sd			100384		//	剑ケイ
#define SPR_251sf			100385		//	杖ケイ
#define SPR_251sp			100386		//	枪ケイ
#define SPR_252ax			100387		//	斧ケイ_3	○
#define SPR_252bw			100388		//	弓ケイ
#define SPR_252em			100389		//	素手ケイ
#define SPR_252sd			100390		//	剑ケイ
#define SPR_252sf			100391		//	杖ケイ
#define SPR_252sp			100392		//	枪ケイ
#define SPR_253ax			100393		//	斧ケイ_4	○
#define SPR_253bw			100394		//	弓ケイ
#define SPR_253em			100395		//	素手ケイ
#define SPR_253sd			100396		//	剑ケイ
#define SPR_253sf			100397		//	杖ケイ
#define SPR_253sp			100398		//	枪ケイ

#define SPR_260ax			100400		//	斧エル_1	○
#define SPR_260bw			100401		//	弓エル
#define SPR_260em			100402		//	素手エル
#define SPR_260sd			100403		//	剑エル
#define SPR_260sf			100404		//	杖エル
#define SPR_260sp			100405		//	枪エル
#define SPR_261ax			100406		//	斧エル_2	○
#define SPR_261bw			100407		//	弓エル
#define SPR_261em			100408		//	素手エル
#define SPR_261sd			100409		//	剑エル
#define SPR_261sf			100410		//	杖エル
#define SPR_261sp			100411		//	枪エル
#define SPR_262ax			100412		//	斧エル_3	○
#define SPR_262bw			100413		//	弓エル
#define SPR_262em			100414		//	素手エル
#define SPR_262sd			100415		//	剑エル
#define SPR_262sf			100416		//	杖エル
#define SPR_262sp			100417		//	枪エル
#define SPR_263ax			100418		//	斧エル_4	○
#define SPR_263bw			100419		//	弓エル
#define SPR_263em			100420		//	素手エル
#define SPR_263sd			100421		//	剑エル
#define SPR_263sf			100422		//	杖エル
#define SPR_263sp			100423		//	枪エル

#define SPRPC_END			100999		// ＰＣキャラ終了番号

// モンスターアニメーション番号 ---------------------------
#define SPR_mon00a			101000		//	黄トラ柄ネコマタ
#define SPR_mon00b			101001		//	青ヒョウ柄ネコマタ
#define SPR_mon00c			101002		//	赤トラ柄ネコマタ
#define SPR_mon00d			101003		//	黄ヒョウ柄ネコマタ
#define SPR_mon00e			101004		//	ヒョウビキニネコマタ
#define SPR_mon00f			101005		//	黒ビキニネコマタ
#define SPR_mon10a			101100		//	右手ゾンビ
#define SPR_mon10b			101101		//	两手ゾンビ
#define SPR_mon10c			101102		//	左手ゾンビ
#define SPR_mon10d			101103		//	緑两手ゾンビ
#define SPR_mon12a			101120		//	青ゴースト
#define SPR_mon12b			101121		//	赤ゴースト
#define SPR_mon12c			101122		//	白ゴースト
#define SPR_mon12d			101123		//	緑ゴースト
#define SPR_mon20a			101200		//	石プッチガゴ。
#define SPR_mon20b			101201		//	炎プッチガゴ
#define SPR_mon20c			101202		//	水プッチガゴ
#define SPR_mon20d			101203		//	緑プッチガゴ
#define SPR_mon20e			101204		//	金プッチガゴ
#define SPR_mon20f			101205		//	プッチガゴ天使
#define SPR_mon21a			101210		//	石ガーゴイル
#define SPR_mon21b			101211		//	炎ガーゴイル
#define SPR_mon21c			101212		//	水ガーゴイル
#define SPR_mon21d			101213		//	黄白ガーゴイル
#define SPR_mon21e			101214		//	ボスガーゴイル
#define SPR_mon23a			101230		//	茶ハーピー
#define SPR_mon23b			101231		//	青ハーピー
#define SPR_mon23c			101232		//	赤ハーピー
#define SPR_mon23d			101233		//　白ハーピー
#define SPR_mon23e			101234		//	大黒ハーピー
#define SPR_mon40a			101400		//	木人
#define SPR_mon40b			101401		//	石木人
#define SPR_mon40c			101402		//	金木人
#define SPR_mon40d			101403		//	白桦木人
#define SPR_mon40e			101404		//	青色木人
#define SPR_mon40f			101405		//	こけ木人
#define SPR_mon40g			101406		//	ボステスト木人
#define SPR_mon50a			101500		//	緑スライム
#define SPR_mon50b			101501		//	青スライム
#define SPR_mon50c			101502		//	赤スライム
#define SPR_mon50d			101503		//	黄スライム
#define SPR_mon51a			101510		//	火エレメント
#define SPR_mon51b			101511		//	风エレメント
#define SPR_mon51c			101512		//	水エレメント
#define SPR_mon51d			101513		//	地エレメント
#define SPR_mon80a			101800		//	五部轮
#define SPR_mon80b			101801		//	广岛ゴブリンズ
#define SPR_mon80c			101802		//	赤鬼ゴブリン
#define SPR_mon80d			101803		//	ドカヘルゴブリン


// エフェクトアニメーション番号 ---------------------------
#define SPR_bm  			103000		//	ブーメラン

#define SPR_gard			110000		//	防御
#define SPR_hit01  			110001		//	ヒット1
#define SPR_hit02  			110002		//	ヒット2
#define SPR_hit03  			110003		//	ヒット3

#define SPR_ultimate		110004  	//	アルティメット
#define SPR_ultimate2		110005  	//	アルティメット(縦效果）
#define SPR_ultimate3		110006  	//	アルティメット(土烟小）
#define SPR_ultimate4		110007  	//	アルティメット(土烟大）
#define SPR_critica1		110008  	//	クリティカル
#define SPR_counter			110009  	//	カウンター

//#define SPR_life00			110010  	//	回复発生种类１旧縦线
#define SPR_life01			110011  	//	回复発生种类２玉
#define SPR_life02			110012  	//	回复発生种类３回転
#define SPR_life03			110013  	//	回复発生种类４新縦线
#define SPR_life04			110014  	//	回复発生种类５波纹

#define SPR_kikoudan		110016  	//	气孔弹
#define SPR_blastwave		104222  	//	追月

#define SPR_bigice1			110020  	//	冰の魔法（特大1）
#define SPR_bigice2			110021  	//	冰の魔法（特大2）
#define SPR_bigice3			110022  	//	冰の魔法（特大3）

#define SPR_b_ice1			110023  	//	冰の魔法（大1）
#define SPR_b_ice2			110024  	//	冰の魔法（大2）
#define SPR_b_ice3			110025  	//	冰の魔法（大3）

#define SPR_m_ice1			110026  	//	冰の魔法（中1）
#define SPR_m_ice2			110027  	//	冰の魔法（中2）
#define SPR_m_ice3			110028  	//	冰の魔法（中3）

#define SPR_s_ice1			110029  	//	冰の魔法（小1）
#define SPR_s_ice2			110030  	//	冰の魔法（小2）
#define SPR_s_ice3			110031  	//	冰の魔法（小3）

#define SPR_bigstone1		110032  	//	土の魔法（特大１）
#define SPR_bigstone2		110033  	//	土の魔法（特大２）
#define SPR_bigstone3		110034  	//	土の魔法（特大３）

#define SPR_b_stone1		110035  	//	土の魔法（大１）
#define SPR_b_stone2		110036  	//	土の魔法（大２）
#define SPR_b_stone3		110037  	//	土の魔法（大３）

#define SPR_m_stone1		110038 		//	土の魔法（中１）
#define SPR_m_stone2		110039 		//	土の魔法（中２）
#define SPR_m_stone3		110040 		//	土の魔法（中３）

#define SPR_s_stone1		110041  	//	土の魔法（小１）
#define SPR_s_stone2		110042  	//	土の魔法（小２）
#define SPR_s_stone3		110043  	//	土の魔法（小３）

#define SPR_bigwind1		110044  	//	风の魔法（特大）
#define SPR_b_wind1			110045  	//	风の魔法（大）
#define SPR_m_wind1			110046  	//	风の魔法（中）
#define SPR_s_wind1			110047  	//	风の魔法（小）

#define SPR_sosei			110060  	//	复活（明るい星）
#define SPR_sosei2			110061  	//	复活(暗い星）
#define SPR_hadou			110062  	//	波动充填　テスト

#define SPR_a_start_big		110052  	//	攻击用発动（特大）
#define SPR_a_start_b		110053  	//	攻击用発动（大）
#define SPR_a_start_m		110054  	//	攻击用発动（中）
#define SPR_a_start_s		110055  	//	攻击用発动（小）

#define SPR_l_start_big		110081  	//	回复用発动（特大）
#define SPR_l_start_b		110082  	//	回复用発动（大）
#define SPR_l_start_m		110083  	//	回复用発动（中）
#define SPR_l_start_s		110084  	//	回复用発动（小）

#define SPR_s_start_big		110056  	//	补助用発动（特大）
#define SPR_s_start_b		110057  	//	补助用発动（大）
#define SPR_s_start_m		110058 		//	补助用発动（中）
#define SPR_s_start_s		110059  	//	补助用発动（小）

#define SPR_sosei			110060  	//	复活（明るい星）
#define SPR_sosei2			110061  	//	复活(暗い星）
#define SPR_hadou			110062  	//	波动充填　テスト

#define SPR_dorein_big1		110063  	//	ドレイン实行时（特大）
#define SPR_dorein_b1		110064  	//	ドレイン实行时（大）
#define SPR_dorein_m1		110065 		//	ドレイン实行时（中）
#define SPR_dorein_s1		110066  	//	ドレイン实行时（小）

#define SPR_dorein_big2		110067  	//	ドレイン吸收时（特大）
#define SPR_dorein_b2		110068  	//	ドレイン吸收时（大）
#define SPR_dorein_m2		110069 		//	ドレイン吸收时（中）
#define SPR_dorein_s2		110070  	//	ドレイン吸收时（小）

#define SPR_state_big1		110071  	//	状态异常实行时（特大）
#define SPR_state_b1		110072  	//	状态异常实行时（大）
#define SPR_state_m1		110073 		//	状态异常实行时（中）
#define SPR_state_s1		110074  	//	状态异常实行时（小）

#define SPR_lp_big			110085  	//	LP回复用实行（特大）
#define SPR_lp_b			110086  	//	LP回复用实行（大）
#define SPR_lp_m			110087  	//	LP回复用实行（中）
#define SPR_lp_s			110088  	//	LP回复用实行（小）

#define SPR_mizu_zoku		110089  	//	水属性优遇（实行）
#define SPR_hi_zoku			110090  	//	火属性优遇（实行）
#define SPR_kaze_zoku		110091  	//	风属性优遇（实行）
#define SPR_tuci_zoku		110092  	//	土属性优遇（实行）

#define SPR_doku			110075  	//	毒　　　　异常（継続时）
#define SPR_nemuri			110076  	//	眠り　　　异常（継続时）
#define SPR_sekika			110077 		//	石化　　　异常（継続时）
#define SPR_yoi				110078  	//	酔い　　　异常（継続时）
#define SPR_konran			110079  	//	混乱　　　异常（継続时）
#define SPR_sousitu			110080  	//	记忆丧失　异常（継続时）

#define SPR_hansya			110093  	//	反射
#define SPR_mukou			110094  	//	无效
#define SPR_card			110095  	//	カード点灭（使い魔）
#define SPR_tukaima			110096  	//	呼び出し集光（使い魔）
#define SPR_fuuin			110097  	//	封印集光

#define SPR_s_gard			110098  	//	スペシャル防御
#define SPR_m_gard			110099  	//	マジック防御
#define SPR_sk_start_big	110100  	//	スキル発动（特大）
#define SPR_sk_start_b		110101  	//	スキル発动（大）
#define SPR_sk_start_m		110102 		//	スキル発动（中）
#define SPR_sk_start_s		110103  	//	スキル発动（小）

#define SPR_i_start			110104 		//	アイテム発动


#define SPR_kyoujitu_big	110105  	//	物理魔术实行　反射??吸收??无效（特大）
#define SPR_kyoujitu_b		110106  	//	物理魔术实行　反射??吸收??无效（大）
#define SPR_kyoujitu_m		110107 		//	物理魔术实行　反射??吸收??无效（中）
#define SPR_kyoujitu_s		110108  	//	物理魔术实行　反射??吸收??无效（小）

#define SPR_bhansya_big		110109  	//	物理反射（特大）
#define SPR_bhansya_b		110110  	//	物理反射（大）
#define SPR_bhansya_m		110111 		//	物理反射（中）
#define SPR_bhansya_s		110112  	//	物理反射（小）

#define SPR_mhansya_big		110113  	//	魔术反射（特大）
#define SPR_mhansya_b		110114  	//	魔术反射（大）
#define SPR_mhansya_m		110115 		//	魔术反射（中）
#define SPR_mhansya_s		110116  	//	魔术反射（小）

#define SPR_bkyuu_big		110117  	//	物理吸收（特大）
#define SPR_bkyuu_b			110118  	//	物理吸收（大）
#define SPR_bkyuu_m			110119 		//	物理吸收（中）
#define SPR_bkyuu_s			110120  	//	物理吸收（小）

#define SPR_mkyuu_big		110121  	//	魔术吸收（特大）
#define SPR_mkyuu_b			110122  	//	魔术吸收（大）
#define SPR_mkyuu_m			110123 		//	魔术吸收（中）
#define SPR_mkyuu_s			110124  	//	魔术吸收（小）

#define SPR_bmukou_big		110125  	//	物理无效（特大）
#define SPR_bmukou_b		110126  	//	物理无效（大）
#define SPR_bmukou_m		110127 		//	物理无效（中）
#define SPR_bmukou_s		110128  	//	物理无效（小）

#define SPR_mmukou_big		110129  	//	魔术无效（特大）
#define SPR_mmukou_b		110130  	//	魔术无效（大）
#define SPR_mmukou_m		110131 		//	魔术无效（中）
#define SPR_mmukou_s		110132  	//	魔术无效（小）

#define SPR_ren_hit			110133  	//	连続攻击スキルＨＩＴマーク（）
#define SPR_eji_hit			110134  	//	エッジＨＩＴマーク（）
#define SPR_chr_hit			110135  	//	チャージアタック（）
#define SPR_bur_hit			110136  	//	バーストアタック（）
#define SPR_ger_hit			110137  	//	防御ブレイク（）
#define SPR_mik_hit			110138  	//	みかわし（）
#define SPR_fos_hit			110139  	//	フォースカット（）

#define SPR_bigfire1		110140  	//	火の魔法（特大）
#define SPR_bigfire2		110141  	//	火の魔法（特大）
#define SPR_bigfire3		110142  	//	火の魔法（特大）

#define SPR_b_fire1			110143  	//	火の魔法（大）
#define SPR_b_fire2			110144  	//	火の魔法（大）
#define SPR_b_fire3			110145  	//	火の魔法（大）

#define SPR_m_fire1			110146  	//	火の魔法（中）
#define SPR_m_fire2			110147  	//	火の魔法（中）
#define SPR_m_fire3			110148  	//	火の魔法（中）

#define SPR_s_fire1			110149  	//	火の魔法（小）
#define SPR_s_fire2			110150  	//	火の魔法（小）
#define SPR_s_fire3			110151  	//	火の魔法（小）

#define SPR_zokuhan			110152  	//	属性反転（継続时）
#define SPR_kurosu			110153  	//	クロスカウンター

#define SPR_jibaku			110154  	//	自爆
#define SPR_seijyaku		110155  	//	魔法封じ
#define SPR_sokusi			110156  	//	即死
#define SPR_sakuri			110157  	//	サクリファイス（発动）
#define SPR_yubaku			110158  	//	自爆の游爆
#define SPR_souhakai		110159  	//	装备破坏
#define SPR_coinhit			110160  	//	コインＨＩＴ

#define SPR_zokuhan2_big	110161  	//	属性反転(实行时）
#define SPR_zokuhan2_b		110162  	//	属性反転(实行时）
#define SPR_zokuhan2_m		110163  	//	属性反転(实行时）
#define SPR_zokuhan2_s		110164 		//	属性反転(实行时）

#define SPR_lpsai_big		110165  	//	LP再生用（特大）
#define SPR_lpsai_b			110166  	//	LP再生用（大）
#define SPR_lpsai_m			110167 		//	LP再生用（中）
#define SPR_lpsai_s			110168  	//	LP再生用（小）

#define SPR_piyo			110169  	//	ぴよぴよ気絶

#define SPR_seisin_big		110170  	//	精神集中
#define SPR_seisin_b		110171  	//	精神集中
#define SPR_seisin_m		110172  	//	精神集中
#define SPR_seisin_s		110173  	//	精神集中

#define SPR_kyuhit			110174  	//	吸血HIT
#define SPR_enadorehit		110175  	//	エナドレHIT

#define SPR_life00_big		110176  	//	LP再生实行种类縦线（特大
#define SPR_life00_b		110177  	//	LP再生实行种类縦线（大
#define SPR_life00_m		110178  	//	LP再生实行种类縦线（中
#define SPR_life00_s		110179  	//	LP再生实行种类縦线（小

#define SPR_sei_big			110180  	//　精神统一特大（発生）
#define SPR_sei_b 			110181  	//　精神统一大（発生）	
#define SPR_sei_m			110182 		//　精神统一中（発生）	
#define SPR_sei_s			110183 		//　精神统一小（発生）	

#define SPR_lp_fuzoku00		110184 		//　LP回复（特大用效果线）

#define SPR_hit04		110185 	//（旧HITまーく??1）	
#define SPR_hit05		110186 	//（旧HITまーく??2）	
#define SPR_hit06		110187 	//（旧HITまーく??3）	
#define SPR_mikan		110188 	//（みかんボール）	
#define SPR_nami		110189 	//（旧共实??波）	


#define SPR_para_defup		103018		//防御力ＵＰ中
#define SPR_para_defdown	103019		//防御力ＤＯＷＮ中
#define SPR_para_atkup		103020		//攻击力ＵＰ中
#define SPR_para_atkdown	103021		//攻击力ＤＯＷＮ中
#define SPR_para_aglup		103022		//すばやさＵＰ中
#define SPR_para_agldown	103023		//すばやさＤＯＷＮ中


// システムアニメーション番号 -----------------------------
#define SPR_leader			103100		//	リーダーマークアニメーション
#define SPR_mail			103101		//	メール着信アニメーション


// のちに抹消するもの -------------------------------------

//#define SPR_001em			100000		//	子供男１	素手
//#define SPR_114bw			100239

// ペット（敌）キャラ
#define SPR_pet211			100335		//	トリノプス

//各种アニメーション
#define SPR_effect01		100600		//	咒术アニメーション
#define SPR_hoshi			100605		//	咒术をかけられたときのアニメーション
#define SPR_difence			100610		//	防御系咒术をかけられたときのアニメーション
//#define SPR_mail			100507		//	メール着信アニメーション





// 「ＣｒｏｓｓＧａｔｅ」のグラフィック番号 ---------------

#define CG_INVISIBLE							999		// これ以下は画面に描画されない

// システム关连 -------------------------------------------

// マウスカーソル-------------------------------------------------
#define CG_MOUSE_CURSOR							22000	// マウスカーソル
#define CG_GRID_CURSOR							22001	// グリッドカーソル


// ローディング画面关连 -----------------------------------
#define CG_NOW_LOADING1							22024	// ローディング１
#define CG_NOW_LOADING2							22025	// ローディング２
#define CG_NOW_LOADING3							22026	// ローディング３

#define CG_NOW_LOADINGEX						22210	// ＥＸローディング
#define CG_NOW_LOADINGV2						22211	// Ｖ２ローディング

//台湾版LOGO
#define TW_CG_SOFTSTAR_LOGO						99103
#define TW_CG_SQUAREENIX_LOGO					99304
#define TW_CG_JOYPARK_LOGO						22029
#define TW_CG_ZENER_LOGO						22049
#define TW_CG_NOW_LOADING						98979
// ロゴ关连 -----------------------------------------------
#define CG_ENIX_LOGO							22027
#define CG_DWANGO_LOGO							22028
#define CG_ZENER_LOGO							22029

// ID & PASS 入力关连 -------------------------------------
#define CG_ID_PASS_WINDOW						22002	// ID & PASS 入力画面
#define CG_ID_PASS_OK							22003	// OKボタン
#define CG_ID_PASS_QUIT							22004	// QUITボタン



// キャラ作成关连 -----------------------------------------

// 新规キャラ选择
#define CG_CHR_MAKE_SEL_WINDOW					22200	// 新规キャラ选择画面
#define CG_CHR_MAKE_SEL_WINDOW_EX				22218	// 新规キャラ选择画面ＥＸ
#define CG_CHR_MAKE_SEL_OK_BTN_2				22201	// OKボタン凹
#define CG_CHR_MAKE_SEL_BACK_BTN_2				22202	// BACKボタン凹
#define CG_CHR_MAKE_SEL_OK_BTN_2_EX				22226	// OKボタン凹 ＥＸ
#define CG_CHR_MAKE_SEL_BACK_BTN_2_EX			22227	// BACKボタン凹 ＥＸ
#define CG_CHR_MAKE_SEL_ROTATE_COLOR_WIN		22205	// 向き＆色换えウィンドウ
#define CG_CHR_MAKE_SEL_ROTATE_LEFT_BTN_2		22206	// 向き换え左ボタン凹
#define CG_CHR_MAKE_SEL_ROTATE_RIGHT_BTN_2		22207	// 向き换え右ボタン凹
#define CG_CHR_MAKE_SEL_COLOR_LEFT_BTN_2		22206	// 色换え左ボタン凹
#define CG_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2		22207	// 色换え右ボタン凹

#define CG_CHR_MAKE_SEL_PAGE1_BTN				22223	// ページ切り替えボタン
#define CG_CHR_MAKE_SEL_PAGE2_BTN				22224	// ページ切り替えボタン
#define CG_CHR_MAKE_SEL_WINDOW_EX_B				22235	// 新キャラ选择画面ＥＸ２ページ目




// アップグレードフォーム
#define CG_CHAR_UPGRADE							22222	// アップグレード
#define CG_CHAR_UPG_TOEX_UP						22228	// ＥＸへアップグレード
#define CG_CHAR_UPG_TOEX_DOWN					22229	// ＥＸへアップグレード
#define CG_CHAR_UPG_TOVER2_UP					22230	// ＶＥＲ２へアップグレード
#define CG_CHAR_UPG_TOVER2_DOWN					22231	// ＶＥＲ２へアップグレード
#define CG_CHAR_UPG_BACK						22232	// 返回按键

// 新规キャラパラメータ振り分け
#define CG_CHAR_MAKE_WINDOW						22005	// キャラ作成画面
#define CG_CHAR_MAKE_WINDOW_EX					22220	// キャラ作成画面 EX
#define CG_CHAR_MAKE_WINDOW_V2					22221	// キャラ作成画面 V2
#define CG_CHAR_MAKE_OK							22006	// OKボタン
#define CG_CHAR_MAKE_BACK						22007	// BACKボタン
#define CG_CHAR_MAKE_OK_EX						22233	// OKボタン
#define CG_CHAR_MAKE_BACK_EX					22234	// BACKボタン

#define CG_CHAR_MAKE_LEFT_UP					22008	// 左矢印ボタン凸
#define CG_CHAR_MAKE_RIGHT_UP					22009	// 右矢印ボタン凸
#define CG_CHAR_MAKE_LEFT_DOWN					22021	// 左矢印ボタン凹
#define CG_CHAR_MAKE_RIGHT_DOWN					22022	// 右矢印ボタン凹

#define CG_NUM_0								22010	// パラメータを表示する数字画像(0)
#define CG_NUM_1								22011	//             〃              (1)
#define CG_NUM_2								22012	//             〃              (2)
#define CG_NUM_3								22013	//             〃              (3)
#define CG_NUM_4								22014	//             〃              (4)
#define CG_NUM_5								22015	//             〃              (5)
#define CG_NUM_6								22016	//             〃              (6)
#define CG_NUM_7								22017	//             〃              (7)
#define CG_NUM_8								22018	//             〃              (8)
#define CG_NUM_9								22019	//             〃              (9)


#define V2_NUM_0								22180	// パラメータを表示する数字画像(0)
#define V2_NUM_1								22181	//             〃              (1)
#define V2_NUM_2								22182	//             〃              (2)
#define V2_NUM_3								22183	//             〃              (3)
#define V2_NUM_4								22184	//             〃              (4)
#define V2_NUM_5								22185	//             〃              (5)
#define V2_NUM_6								22186	//             〃              (6)
#define V2_NUM_7								22187	//             〃              (7)
#define V2_NUM_8								22188	//             〃              (8)
#define V2_NUM_9								22189	//             〃              (9)


#define V2_CHAR_MAKE_OK							22298	// OKボタン
#define V2_CHAR_MAKE_BACK						22297	// BACKボタン

#define V2_CHAR_MAKE_LEFT_UP					22244	// 左矢印ボタン凸
#define V2_CHAR_MAKE_LEFT_DOWN					22245	// 左矢印ボタン凹
#define V2_CHAR_MAKE_RIGHT_UP					22246	// 右矢印ボタン凸
#define V2_CHAR_MAKE_RIGHT_DOWN					22247	// 右矢印ボタン凹


//台服版服务器名图档
#define TW_CG_MAIN_SERVER_NAME_1_1					98822	// サーバ名１ボタン凸
#define TW_CG_MAIN_SERVER_NAME_1_2					98823	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_2_1					30151	// 　　　　２ボタン凸
#define TW_CG_MAIN_SERVER_NAME_2_2					30152	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_3_1					30201	// 　　　　３ボタン凸
#define TW_CG_MAIN_SERVER_NAME_3_2					30202	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_4_1					30701	// 　　　　４ボタン凸
#define TW_CG_MAIN_SERVER_NAME_4_2					30702	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_5_1					30301	// 　　　　５ボタン凸
#define TW_CG_MAIN_SERVER_NAME_5_2					30302	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_6_1					30351	// 　　　　６ボタン凸
#define TW_CG_MAIN_SERVER_NAME_6_2					30352	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_7_1					30401	// 　　　　７ボタン凸
#define TW_CG_MAIN_SERVER_NAME_7_2					30402	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_8_1					98800	// 　　　　８ボタン凸
#define TW_CG_MAIN_SERVER_NAME_8_2					98801	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_9_1					99615	// 　　　　９ボタン凸
#define TW_CG_MAIN_SERVER_NAME_9_2					99616	// 　　　〃　　　　凹
#define TW_CG_MAIN_SERVER_NAME_10_1					30551	// 　　　　１０ボタン凸
#define TW_CG_MAIN_SERVER_NAME_10_2					30552	// 　　　〃　　　　　凹
#define TW_CG_MAIN_SERVER_NAME_11_1					30601	// 　　　　１１ボタン凸
#define TW_CG_MAIN_SERVER_NAME_11_2					30602	// 　　　〃　　　　　凹
#define TW_CG_MAIN_SERVER_NAME_12_1					30651	// 　　　　１２ボタン凸
#define TW_CG_MAIN_SERVER_NAME_12_2					30652	// 　　　〃　　　　　凹
#define TW_CG_MAIN_SERVER_NAME_13_1					30751	// 　　　　１０ボタン凸
#define TW_CG_MAIN_SERVER_NAME_13_2					30752	// 　　　〃　　　　　凹
#define TW_CG_MAIN_SERVER_NAME_14_1					30801	// 　　　　１１ボタン凸
#define TW_CG_MAIN_SERVER_NAME_14_2					30802	// 　　　〃　　　　　凹
#define TW_CG_MAIN_SERVER_NAME_15_1					30851	// 　　　　１２ボタン凸
#define TW_CG_MAIN_SERVER_NAME_15_2					30852	// 　　　〃　　　　　凹
#define TW_CG_MAIN_SERVER_NAME_16_1					30901	// 　　　　１０ボタン凸
#define TW_CG_MAIN_SERVER_NAME_16_2					30902	// 　　　〃　　　　　凹

#define TW_CG_SERVER_NAME_1_1					30151
#define TW_CG_SERVER_NAME_1_2					30161
#define TW_CG_SERVER_NAME_2_1					30152
#define TW_CG_SERVER_NAME_2_2					30162
#define TW_CG_SERVER_NAME_3_1					30153
#define TW_CG_SERVER_NAME_3_2					30163
#define TW_CG_SERVER_NAME_4_1					30154
#define TW_CG_SERVER_NAME_4_2					30164
#define TW_CG_SERVER_NAME_5_1					30155
#define TW_CG_SERVER_NAME_5_2					30165
#define TW_CG_SERVER_NAME_6_1					30156
#define TW_CG_SERVER_NAME_6_2					30166
#define TW_CG_SERVER_NAME_7_1					30157
#define TW_CG_SERVER_NAME_7_2					30167
#define TW_CG_SERVER_NAME_8_1					30158
#define TW_CG_SERVER_NAME_8_2					30168
#define TW_CG_SERVER_NAME_9_1					30159
#define TW_CG_SERVER_NAME_9_2					30169
#define TW_CG_SERVER_NAME_10_1					30160
#define TW_CG_SERVER_NAME_10_2					30170
// サーバ选择 ---------------------------------------------
#define CG_TITLE								22023	// タイトル画面
#define CG_TITLE_EX								22214	// タイトル画面 EX
#define CG_TITLE_V2								22215	// タイトル画面 V2
#define CG_SERVER_NAME_1_1						30101	// サーバ名１ボタン凸
#define CG_SERVER_NAME_1_2						30102	// 　　　〃　　　　凹
#define CG_SERVER_NAME_2_1						30151	// 　　　　２ボタン凸
#define CG_SERVER_NAME_2_2						30152	// 　　　〃　　　　凹
#define CG_SERVER_NAME_3_1						30201	// 　　　　３ボタン凸
#define CG_SERVER_NAME_3_2						30202	// 　　　〃　　　　凹
#define CG_SERVER_NAME_4_1						30251	// 　　　　４ボタン凸
#define CG_SERVER_NAME_4_2						30252	// 　　　〃　　　　凹
#define CG_SERVER_NAME_5_1						30301	// 　　　　５ボタン凸
#define CG_SERVER_NAME_5_2						30302	// 　　　〃　　　　凹
#define CG_SERVER_NAME_6_1						30351	// 　　　　６ボタン凸
#define CG_SERVER_NAME_6_2						30352	// 　　　〃　　　　凹
#define CG_SERVER_NAME_7_1						22256	// 　　　　７ボタン凸
#define CG_SERVER_NAME_7_2						22269	// 　　　〃　　　　凹
#define CG_SERVER_NAME_8_1						22257	// 　　　　８ボタン凸
#define CG_SERVER_NAME_8_2						22270	// 　　　〃　　　　凹
#define CG_SERVER_NAME_9_1						22258	// 　　　　９ボタン凸
#define CG_SERVER_NAME_9_2						22271	// 　　　〃　　　　凹
#define CG_SERVER_NAME_10_1						22259	// 　　　　１０ボタン凸
#define CG_SERVER_NAME_10_2						22272	// 　　　〃　　　　　凹
#define CG_SERVER_NAME_11_1						22260	// 　　　　１１ボタン凸
#define CG_SERVER_NAME_11_2						22273	// 　　　〃　　　　　凹
#define CG_SERVER_NAME_12_1						22261	// 　　　　１２ボタン凸
#define CG_SERVER_NAME_12_2						22274	// 　　　〃　　　　　凹
#define CG_SERVER_SELECT_EXIT_1					22262	// 終了ボタン凸
#define CG_SERVER_SELECT_EXIT_2					22275	// 終了ボタン凹
#define CG_SERVER_SELECT_PAGE1					22276	// ページ表示（１ページ目）
#define CG_SERVER_SELECT_PAGE2					22277	// ページ表示（２ページ目）
#define CG_SERVER_SELECT_LEFT_BTN_1				22008	// 左ボタン凸
#define CG_SERVER_SELECT_LEFT_BTN_2				22021	// 左ボタン凹
#define CG_SERVER_SELECT_RIGHT_BTN_1			22009	// 右ボタン凸
#define CG_SERVER_SELECT_RIGHT_BTN_2			22022	// 右ボタン凹
#define CG_SERVER_SELECT_BACK_1					22278	// ＩＤ入力に返回按键(デフォルト)
#define CG_SERVER_SELECT_BACK_2					22279	// 　　　　　　　　　　(フォーカス)


#define V2_Arzes_OFF		30101		//Arzes		（通常）
#define V2_Diomeer_OFF		30151		//Diomeer	（通常）
#define V2_Fanitis_OFF		22778		//Fanitis	（通常）
#define V2_Emeus_OFF		22779		//Emeus		（通常）
#define V2_Ornis_OFF		22780		//Ornis		（通常）
#define V2_Freat_OFF		22781		//Freat		（通常）

#define V2_Yeoskeer_OFF		22788		//Yeoskeer	（通常）
#define V2_Farkalt_OFF		22789		//Farkalt	（通常）
#define V2_Milliotice_OFF	22790		//Milliotice（通常）
#define V2_Finia_OFF		22791		//Finia		（通常）
#define V2_Erenor_OFF		22792		//Erenor	（通常）
#define V2_Karen_OFF		22793		//Karen		（通常）

#define V2_Arzes_ON			30102		//Arzes		（选择）
#define V2_Diomeer_ON		30152		//Diomeer	（选择）
#define V2_Fanitis_ON		22784		//Fanitis	（选择）
#define V2_Emeus_ON			22785		//Emeus		（选择）
#define V2_Ornis_ON			22786		//Ornis		（选择）
#define V2_Freat_ON			22787		//Freat		（选择）

#define V2_Yeoskeer_ON		22794		//Yeoskeer	（选择）
#define V2_Farkalt_ON		22795		//Farkalt	（选择）
#define V2_Milliotice_ON	22796		//Milliotice（选择）
#define V2_Finia_ON			22797		//Finia		（选择）
#define V2_Erenor_ON		22798		//Erenor	（选择）
#define V2_Karen_ON			22799		//Karen		（选择）

#define V2_SERVER_SELECT_EXIT_1					22240	// 終了ボタン凸
#define V2_SERVER_SELECT_EXIT_2					22242	// 終了ボタン凹
#define V2_SERVER_SELECT_PAGE1					22642	// ページ表示（１ページ目）
#define V2_SERVER_SELECT_PAGE2					22643	// ページ表示（２ページ目）
#define V2_SERVER_SELECT_LEFT_BTN_1				22244	// 左ボタン凸
#define V2_SERVER_SELECT_LEFT_BTN_2				22245	// 左ボタン凹
#define V2_SERVER_SELECT_RIGHT_BTN_1			22246	// 右ボタン凸
#define V2_SERVER_SELECT_RIGHT_BTN_2			22247	// 右ボタン凹
#define V2_SERVER_SELECT_BACK_1					22241	// ＩＤ入力に返回按键(デフォルト)
#define V2_SERVER_SELECT_BACK_2					22243	// 　　　　　　　　　　(フォーカス)


#define V2_EDIT_SELECT_PAGE1					22642	// ページ表示（１ページ目）
#define V2_EDIT_SELECT_PAGE2					22643	// ページ表示（２ページ目）

// ログイン时のキャラ选择 ---------------------------------

#define CG_CHR_SEL_BG							22030	// キャラクタ选择画面
#define CG_CHR_SEL_LOGIN_LEFT_BTN_1				22034	// ログインボタン凸(左)
#define CG_CHR_SEL_LOGIN_LEFT_BTN_2				22031	// ログインボタン凹(左)
#define CG_CHR_SEL_DEL_LEFT_BTN_1				22035	// 削除ボタン凸(左)
#define CG_CHR_SEL_DEL_LEFT_BTN_2				22032	// 削除ボタン凹(左)
#define CG_CHR_SEL_NEW_LEFT_BTN_1				22037	// 新规ボタン凸(左)
#define CG_CHR_SEL_NEW_LEFT_BTN_2				22036	// 新规ボタン凹(左)

#define CG_CHR_SEL_LOGIN_RIGHT_BTN_1			22034	// ログインボタン凸(右)
#define CG_CHR_SEL_LOGIN_RIGHT_BTN_2			22031	// ログインボタン凹(右)
#define CG_CHR_SEL_DEL_RIGHT_BTN_1				22035	// 削除ボタン凸(右)
#define CG_CHR_SEL_DEL_RIGHT_BTN_2				22032	// 削除ボタン凹(右)
#define CG_CHR_SEL_NEW_RIGHT_BTN_1				22037	// 新规ボタン凸(右)
#define CG_CHR_SEL_NEW_RIGHT_BTN_2				22036	// 新规ボタン凹(右)

#define CG_CHR_SEL_BACK_BTN_2					22033	// 返回按键凹
#define CG_CHR_SEL_UPGRADE_BTN_1				22237	// アップグレードボタン凸
#define CG_CHR_SEL_UPGRADE_BTN_2				22236	// アップグレードボタン凸
#define CG_CHR_SEL_UPGRADE_BTN_TRIAL_1			22208	// アップグレードボタン凸
#define CG_CHR_SEL_UPGRADE_BTN_TRIAL_2			22209	// アップグレードボタン凸


#define V2_CREATE_BTN_DOWN_L	22282		//(クリエイト左くらい)
#define V2_CREATE_BTN_L			22285		//(クリエイト左明るい）

#define V2_CREATE_BTN_DOWN_R	22288		//(クリエイト右くらい)
#define V2_CREATE_BTN_R			22291		//(クリエイト左明るい)

#define V2_CHR_SEL_BG			22217		// キャラクタ选择画面

#define V2_CHR_SEL_LOGIN_LEFT_BTN_1				22280	// ログインボタン凸(左)
#define V2_CHR_SEL_LOGIN_LEFT_BTN_2				22283	// ログインボタン凹(左)
#define V2_CHR_SEL_LOGIN_RIGHT_BTN_1			22286	// ログインボタン凸(右)
#define V2_CHR_SEL_LOGIN_RIGHT_BTN_2			22289	// ログインボタン凹(右)
#define V2_CHR_SEL_DEL_LEFT_BTN_1				22281	// 削除ボタン凸(左)
#define V2_CHR_SEL_DEL_LEFT_BTN_2				22284	// 削除ボタン凹(左)
#define V2_CHR_SEL_DEL_RIGHT_BTN_1				22287	// 削除ボタン凸(右)
#define V2_CHR_SEL_DEL_RIGHT_BTN_2				22290	// 削除ボタン凹(右)

#define V2_CHR_SEL_BACK_BTN_2					22248	// 返回按键凹

// メニュー关连 -------------------------------------------
#define CG_WIN_CLOSE_1							22509	// Ｘボタン凸
#define CG_WIN_CLOSE_2							22510	// Ｘボタン凹

// オートマップメウィンドウ -------------------------------
#define CG_AUTOMAP_WINDOW						22535	// オートマップウィンドウ
#define CG_AUTOMAP_ZOOM_UP_1					22536	// ズームアップボタン凸
#define CG_AUTOMAP_ZOOM_UP_2					22537	// ズームアップボタン凹
#define CG_AUTOMAP_ZOOM_DOWN_1					22538	// ズームダウンボタン凸
#define CG_AUTOMAP_ZOOM_DOWN_2					22539	// ズームダウンボタン凹

// 状态メニュー -------------------------------------
#define CG_STATUS_MENU_WINDOW1					22501	// 状态ウィンドウ
#define CG_STATUS_MENU_WINDOW2					22515	// ディテールウィンドウ
#define CG_STATUS_MENU_WINDOW3					22980	// タイトルチェンジウィンドウ
#define CG_STATUS_MENU_TAB1						22505	// 状态ウィンドウに重ねるタグの文字1
#define CG_STATUS_MENU_TAB2						22506	// 　　　　　　　　　　〃　　　　　　　　2
#define CG_STATUS_MENU_TAB3						22507	// 　　　　　　　　　　〃　　　　　　　　3
#define CG_STATUS_MENU_TAB4						22508	// 　　　　　　　　　　〃　　　　　　　　4
#define CG_STATUS_MENU_BONUS					22516	// ボーナスポイント点灭用
#define CG_STATUS_MENU_ADD_POINT_1				22517	// ボーナスポイント割り振りボタン凸
#define CG_STATUS_MENU_ADD_POINT_2				22518	// ボーナスポイント割り振りボタン凹
#define CG_STATUS_MENU_TITLE_CHG_1				22502	// 称号变更ボタン凸
#define CG_STATUS_MENU_TITLE_CHG_2				22503	// 称号变更ボタン凹
#define CG_STATUS_MENU_BTL_CHG_FRONT_1			22511	// 战闘位置を前に设定するボタン凸
#define CG_STATUS_MENU_BTL_CHG_FRONT_2			22512	// 战闘位置を前に设定するボタン凹
#define CG_STATUS_MENU_BTL_CHG_BACK_1			22513	// 战闘位置を后ろに设定するボタン凸
#define CG_STATUS_MENU_BTL_CHG_BACK_2			22514	// 战闘位置を后ろに设定するボタン凹
#define CG_STATUS_MENU_STUN_ICON				22527	// 気絶ポイントマーク

// 称号入力ウィンドウ -------------------------------------
#define CG_TITLE_INPUT_SET_BTN_1				22981	// 决定ボタン凸
#define CG_TITLE_INPUT_SET_BTN_2				22982	// 　　〃　　凹
#define CG_TITLE_INPUT_REMOVE_BTN_1				22983	// REMOVEボタン凸
#define CG_TITLE_INPUT_REMOVE_BTN_2				22984	//      〃     凹
#define CG_TITLE_INPUT_DELETE_BTN_1				22985	// DELETEボタン凸
#define CG_TITLE_INPUT_DELETE_BTN_2				22986	//      〃     凹

// システムメニュー----------------------------------------
#define CG_SYSTEM_MENU_WINDOW					22500	// システムウィンドウ
#define CG_SYSTEM_MENU_LOGOUT_WINDOW			22504	// 登出ウィンドウ
#define CG_SYSTEM_MENU_CHAT_WINDOW				22960	// 对话设定ウィンドウ
#define CG_SYSTEM_MENU_BGM_WINDOW				22961	// 设定ＢＧＭウィンドウ
#define CG_SYSTEM_MENU_SE_WINDOW				22962	// 设定ＳＥウィンドウ
#define CG_SYSTEM_MENU_MOUSE_WINDOW				22963	// 设定鼠标ウィンドウ
#define CG_SYSTEM_MENU_KEY_WINDOW				22964	// 设定键盘ウィンドウ
#define CG_SYSTEM_MENU_DRAW_WINDOW				22965	// 绘图设定メニューウィンドウ

#ifdef _SYSTEMMENU_BTN_CONFIG
#define CG_SYSTEM_MENU_BTN_WINDOW				22549	// 设定键位メニューウィンドウ
#endif /* _SYSTEMMENU_BTN_CONFIG */

// アイテムウィンドウ -------------------------------------
#define CG_ITEM_MENU_WINDOW						22540	// アイテムウィンドウ
#define CG_ITEM_MENU_INFO_WINDOW				22541	// アイテム说明ウィンドウ
#define CG_ITEM_MENU_MONEY_PUT_1				22545	// お金を置くボタン凸
#define CG_ITEM_MENU_MONEY_PUT_2				22546	// お金を置くボタン凹

// お金を置くウィンドウ -----------------------------------
#define CG_ITEM_MENU_MONEY_PUT_WINDOW			22930	// お金を置くウィンドウ
#define CG_ITEM_MENU_MONEY_PUT_BS_BTN_2			22931	// １桁消すボタン凹
#define CG_ITEM_MENU_MONEY_PUT_ALL_BTN_2		22932	// 全额ボタン凹
#define CG_ITEM_MENU_MONEY_PUT_CLEAR_BTN_2		22933	// 0 gold にするボタン凹
#define CG_ITEM_MENU_MONEY_PUT_OK_BTN_1			22934	// ＯＫボタン凸（未入力）
#define CG_ITEM_MENU_MONEY_PUT_ACTIVE_OK_BTN_2	22935	// ＯＫボタン凹
#define CG_ITEM_MENU_MONEY_PUT_CANCEL_BTN_2		22936	// キャンセルボタン凹
#define CG_ITEM_MENU_MONEY_PUT_0_BTN_2			22940	// ０ボタン
#define CG_ITEM_MENU_MONEY_PUT_1_BTN_2			22941	// １ボタン
#define CG_ITEM_MENU_MONEY_PUT_2_BTN_2			22942	// ２ボタン
#define CG_ITEM_MENU_MONEY_PUT_3_BTN_2			22943	// ３ボタン
#define CG_ITEM_MENU_MONEY_PUT_4_BTN_2			22944	// ４ボタン
#define CG_ITEM_MENU_MONEY_PUT_5_BTN_2			22945	// ５ボタン
#define CG_ITEM_MENU_MONEY_PUT_6_BTN_2			22946	// ６ボタン
#define CG_ITEM_MENU_MONEY_PUT_7_BTN_2			22947	// ７ボタン
#define CG_ITEM_MENU_MONEY_PUT_8_BTN_2			22948	// ８ボタン
#define CG_ITEM_MENU_MONEY_PUT_9_BTN_2			22949	// ９ボタン

// スキルウィンドウ ---------------------------------------
#define CG_SKILL_WIN							22750	// スキルウィンドウ
#define CG_SKILL_WIN_BAR						22756	// バー

// アビリティウィンドウ -----------------------------------
//#define CG_ABILITY_MENU_WINDOW					22760	// アビリティウィンドウ
#define CG_ABILITY_MENU_WINDOW					22770	// アビリティウィンドウ
#define CG_ABILITY_MENU_WINDOW_BAR				22761	// バー
#define CG_ABILITY_MENU_SKILL_BTN_1				22766	// スキルボタン凸
#define CG_ABILITY_MENU_SKILL_BTN_2				22767	// スキルボタン凹
#define CG_ABILITY_MENU_LEFT_BTN_1				22762	// ←ボタン凸
#define CG_ABILITY_MENU_LEFT_BTN_2				22763	// ←ボタン凹
#define CG_ABILITY_MENU_RIGHT_BTN_1				22764	// →ボタン凸
#define CG_ABILITY_MENU_RIGHT_BTN_2				22765	// →ボタン凹

// モンスターリストウィンドウ -----------------------------
#define CG_MONSTER_LIST_WINDOW					22840	// モンスターリスト
#define CG_MONSTER_LIST_BATTLE_BTN_1			22841	// 战闘参加ボタン凸
#define CG_MONSTER_LIST_BATTLE_BTN_2			22842	// 战闘参加ボタン凹
#define CG_MONSTER_LIST_STANDBY_BTN_1			22843	// 战闘准备ボタン凸
#define CG_MONSTER_LIST_STANDBY_BTN_2			22844	// 战闘准备ボタン凹
#define CG_MONSTER_LIST_REST_BTN_1				22845	// 待机ボタン凸
#define CG_MONSTER_LIST_REST_BTN_2				22846	// 待机ボタン凹

#define CG_MONSTER_LIST_CONDITION_GREEN			22520	// モンスターコンディション(グリーン)
#define CG_MONSTER_LIST_CONDITION_BLUE			22521	//              〃         (ブルー)
#define CG_MONSTER_LIST_CONDITION_YELLOW		22522	//              〃         (イエロー)
#define CG_MONSTER_LIST_CONDITION_PINK			22523	//              〃         (ピンク)
#define CG_MONSTER_LIST_CONDITION_RED			22524	//              〃         (レッド)

#define CG_MONSTER_LIST_TYPE_HUMAN				22865	// 种族(人间)
#define CG_MONSTER_LIST_TYPE_DRAGON				22866	//  〃 (ドラゴン)
#define CG_MONSTER_LIST_TYPE_UNDEAD				22867	//  〃 (アンデッド)
#define CG_MONSTER_LIST_TYPE_FLY				22868	//  〃 (飞行)
#define CG_MONSTER_LIST_TYPE_INCECT				22869	//  〃 (昆虫)
#define CG_MONSTER_LIST_TYPE_PLANT				22870	//  〃 (植物)
#define CG_MONSTER_LIST_TYPE_BEAST				22871	//  〃 (獣)
#define CG_MONSTER_LIST_TYPE_AMORPHAS			22872	//  〃 (无形)
#define CG_MONSTER_LIST_TYPE_METAL				22873	//  〃 (金属)
#define CG_MONSTER_LIST_TYPE_EVIL				22874	//  〃 (魔族)

#define CG_MONSTER_LIST_SETTING_WINDOW			22910	// 战闘时の设定を行うウィンドウ
#define CG_MONSTER_LIST_SETTING_BATTLE_BTN_1	22911	// 战闘参加ボタン凸
#define CG_MONSTER_LIST_SETTING_BATTLE_BTN_2	22912	// 战闘参加ボタン凹
#define CG_MONSTER_LIST_SETTING_STANDBY_BTN_1	22913	// 战闘准备ボタン凸
#define CG_MONSTER_LIST_SETTING_STANDBY_BTN_2	22914	// 战闘准备ボタン凹
#define CG_MONSTER_LIST_SETTING_REST_BTN_1		22915	// 待机ボタン凸
#define CG_MONSTER_LIST_SETTING_REST_BTN_2		22916	// 待机ボタン凹
#define CG_MONSTER_LIST_SETTING_SETUP_BTN_1		22920	// 决定ボタン凸
#define CG_MONSTER_LIST_SETTING_SETUP_BTN_2		22921	// 决定ボタン凹
#define CG_MONSTER_LIST_SETTING_CANCEL_BTN_1	22922	// キャンセルボタン凸
#define CG_MONSTER_LIST_SETTING_CANCEL_BTN_2	22923	// キャンセルボタン凹

// モンスター状态ウィンドウ -------------------------
#define CG_MONSTER_WINDOW1						22800	// モンスターウィンドウ (Status)
#define CG_MONSTER_WINDOW2						22810	//           〃         (Detail)
#define CG_MONSTER_WINDOW3						22824	//           〃         (Skill)
#define CG_MONSTER_WINDOW_TAB1					22801	// タブボタン (Status)
#define CG_MONSTER_WINDOW_TAB2					22802	// タブボタン (Detail)
#define CG_MONSTER_WINDOW_TAB3					22804	// タブボタン (Skill)
#define CG_MONSTER_WINDOW_BONUS					22811	// ボーナスポイント点灭用
#define CG_MONSTER_WINDOW_ADD_POINT_1			22812	// ボーナスポイント割り振りボタン凸
#define CG_MONSTER_WINDOW_ADD_POINT_2			22813	// ボーナスポイント割り振りボタン凹
#define CG_MONSTER_WINDOW_LIST_BTN_1			22830	// モンスターリストを表示するボタン凸
#define CG_MONSTER_WINDOW_LIST_BTN_2			22831	//                〃               凹
#define CG_MONSTER_WINDOW_LEFT_BTN_1			22832	// モンスター状态ウィンドウ左ボタン凸
#define CG_MONSTER_WINDOW_LEFT_BTN_2			22833	//                   〃                  凹
#define CG_MONSTER_WINDOW_RIGHT_BTN_1			22834	// モンスター状态ウィンドウ右ボタン凸
#define CG_MONSTER_WINDOW_RIGHT_BTN_2			22835	//                   〃                  凹
#define CG_MONSTER_WINDOW_PHOTO_BTN_1			22805	// 写真ボタン凸
#define CG_MONSTER_WINDOW_PHOTO_BTN_2			22806	//     〃    凹
#define CG_MONSTER_WINDOW_RENAME_BTN_1			22807	// 名称变更ボタン凸
#define CG_MONSTER_WINDOW_RENAME_BTN_2			22808	//       〃      凹
#define CG_MONSTER_WINDOW_SKILL_BAR				22825	// バー

// 怪物名字チェンジウィンドウ ---------------------
#define CG_MONSTER_NAME_CHANGE_WIN				22809	// 怪物名字チェンジウィンドウ

// レシピウィンドウ ---------------------------------------
#define CG_RECIPE_WIN							23350	// レシピウィンドウ

// クリエイトウィンドウ -----------------------------------
#define CG_CREATE_WIN							23351	// クリエイトウィンドウ
#define CG_CREATE_EXECUTE_BTN_1					23360	// EXECUTEボタン凸
#define CG_CREATE_EXECUTE_BTN_2					23362	// EXECUTEボタン凹
#define CG_CREATE_EXECUTE_BTN_3					23361	// EXECUTEボタン凸（押せないボタン）

// マテリアル选择ウィンドウ -------------------------------
#define CG_MATERIAL_SELECT_WIN					23352	// マテリアル选择ウィンドウ
#define CG_MATERIAL_SELECT_REGISTER_BTN_1		23363	// REGISTERボタン凸
#define CG_MATERIAL_SELECT_REGISTER_BTN_2		23365	// REGISTERボタン凹
#define CG_MATERIAL_SELECT_REGISTER_BTN_3		23364	// REGISTERボタン凸（押せないボタン）

// クラフト结果ウィンドウ ---------------------------------
#define CG_CRAFT_RESULT_WIN						23353	// クラフト结果ウィンドウ
#define CG_CRAFT_RESULT_WIN2					23356	// クラフト结果ウィンドウ２

// ターゲットアイテム选择 ---------------------------------
#define CG_TARGET_ITEM_SEL						23354	// ターゲットアイテム选择ウィンドウ
#define CG_TARGET_ITEM_SEL_REPAIR_1				23366	// 修理ボタン凸
#define CG_TARGET_ITEM_SEL_REPAIR_2				23368	// 修理ボタン凹
#define CG_TARGET_ITEM_SEL_REPAIR_3				23367	// 修理ボタン凸(クリックできない)

#define CG_TARGET_ITEM_SEL_APPRAISAL_1			23369	// 鉴定ボタン凸
#define CG_TARGET_ITEM_SEL_APPRAISAL_2			23371	// 鉴定ボタン凹
#define CG_TARGET_ITEM_SEL_APPRAISAL_3			23370	// 鉴定ボタン凸(クリックできない)

#define CG_TARGET_ITEM_SEL_INCUSE_1				23372	// 刻印ボタン凸
#define CG_TARGET_ITEM_SEL_INCUSE_2				23374	// 刻印ボタン凹
#define CG_TARGET_ITEM_SEL_INCUSE_3				23373	// 刻印ボタン凸(クリックできない)

// 刻印ウィンドウ -----------------------------------------
#define CG_TARGET_ITEM_SEL_INCUSE_WIN			23355	// 刻印ウィンドウ

// スタックアイテムウィンドウ -----------------------------
#define CG_STACK_ITEM_WIN						23310	// スタックアイテムウィンドウ
#define CG_STACK_ITEM_BS_BTN_2					23311	// １桁消すボタン凹
#define CG_STACK_ITEM_ALL_BTN_2					23312	// 全额ボタン凹
#define CG_STACK_ITEM_CLEAR_BTN_2				23313	// 0 gold にするボタン凹
#define CG_STACK_ITEM_OK_BTN_1					23314	// ＯＫボタン凸（未入力）
#define CG_STACK_ITEM_ACTIVE_OK_BTN_2			23315	// ＯＫボタン凹
#define CG_STACK_ITEM_CANCEL_BTN_2				23316	// キャンセルボタン凹
#define CG_STACK_ITEM_0_BTN_2					23320	// ０ボタン
#define CG_STACK_ITEM_1_BTN_2					23321	// １ボタン
#define CG_STACK_ITEM_2_BTN_2					23322	// ２ボタン
#define CG_STACK_ITEM_3_BTN_2					23323	// ３ボタン
#define CG_STACK_ITEM_4_BTN_2					23324	// ４ボタン
#define CG_STACK_ITEM_5_BTN_2					23325	// ５ボタン
#define CG_STACK_ITEM_6_BTN_2					23326	// ６ボタン
#define CG_STACK_ITEM_7_BTN_2					23327	// ７ボタン
#define CG_STACK_ITEM_8_BTN_2					23328	// ８ボタン
#define CG_STACK_ITEM_9_BTN_2					23329	// ９ボタン

// 战闘リザルトウィンドウ ---------------------------------
#define CG_BTL_RESULT_WIN						23380	// 战闘リザルトウィンドウ
#define CG_BTL_RESULT_EXP_BAR					23381	// スキルＥＸＰ表示バー
#define CG_BTL_RESULT_LVUP_ICON					23382	// 等级アップアイコン

// デュエルリザルトウィンドウ -----------------------------
#define CG_DUEL_RESULT_WIN						23385	// デュエルリザルトウィンドウ

// メール关连 ---------------------------------------------
// アドレズブックウィンドウ -------------------------------
#define CG_MAIL_ADDRESS_BOOK_WIN				23100	// アドレスブック
#define CG_MAIL_ADDRESS_BOOK_SEND_BTN_1			23101	// Sendボタン凸
#define CG_MAIL_ADDRESS_BOOK_SEND_BTN_2			23102	// Sendボタン凹
#define CG_MAIL_ADDRESS_BOOK_SEND_BTN_3			23109	// Sendボタン凸（押せないボタン）
#define CG_MAIL_ADDRESS_BOOK_HISTORY_BTN_1		23103	// Historyボタン凸
#define CG_MAIL_ADDRESS_BOOK_HISTORY_BTN_2		23104	// Historyボタン凹
#define CG_MAIL_ADDRESS_BOOK_DEL_BTN_1			23105	// Delボタン凸
#define CG_MAIL_ADDRESS_BOOK_DEL_BTN_2			23106	// Delボタン凹
#define CG_MAIL_ADDRESS_BOOK_SORT_BTN_1			23107	// Sortボタン凸
#define CG_MAIL_ADDRESS_BOOK_SORT_BTN_2			23108	// Sortボタン凹
#define CG_MAIL_ADDRESS_BOOK_LEFT_1				22832	// 左ボタン凸
#define CG_MAIL_ADDRESS_BOOK_LEFT_2				22833	// 左ボタン凹
#define CG_MAIL_ADDRESS_BOOK_RIGHT_1			22834	// 右ボタン凸
#define CG_MAIL_ADDRESS_BOOK_RIGHT_2			22835	// 右ボタン凹
#define CG_MAIL_ADDRESS_BOOK_OFFLINE_LAMP		23110	// OFFラインランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_1_LAMP		23120	// サーバ１ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_2_LAMP		23121	// サーバ２ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_3_LAMP		23122	// サーバ３ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_4_LAMP		23123	// サーバ４ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_5_LAMP		23124	// サーバ５ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_6_LAMP		23125	// サーバ６ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_7_LAMP		23126	// サーバ７ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_8_LAMP		23127	// サーバ８ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_9_LAMP		23128	// サーバ９ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_10_LAMP		23129	// サーバ１０ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_11_LAMP		23130	// サーバ１１ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_12_LAMP		23131	// サーバ１２ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_13_LAMP		23132	// サーバ１３ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_14_LAMP		23133	// サーバ１４ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_15_LAMP		23134	// サーバ１５ランプ
#define CG_MAIL_ADDRESS_BOOK_SERVER_16_LAMP		23135	// サーバ１６ランプ

#ifdef PUK2		//####toda
	#define	CG_MAIL_ADDRESS_BOOK_WIN_P2			238521	//アドレスブック PUK2
	//
	#define	CG_MAIL_GUILD_BOOK_WIN_P2			238522	//家族ブック PUK2
	#define	CG_MAIL_GUILD_BOOK_SEND_BTN_1		238523	//
	#define	CG_MAIL_GUILD_BOOK_SEND_BTN_2		238524	//
	#define	CG_MAIL_GUILD_BOOK_HISTORY_BTN_1	238525	//
	#define	CG_MAIL_GUILD_BOOK_HISTORY_BTN_2	238526	//
	#define	CG_MAIL_GUILD_BOOK_DEL_BTN_1		238527	//
	#define	CG_MAIL_GUILD_BOOK_DEL_BTN_2		238528	//
	#define	CG_MAIL_GUILD_BOOK_GUTITLE_BTN_1	238529	//
	#define	CG_MAIL_GUILD_BOOK_GUTITLE_BTN_2	238530	//
	#define	CG_MAIL_GUILD_BOOK_SORT_BTN_1		238531	//
	#define	CG_MAIL_GUILD_BOOK_SORT_BTN_2		238532	//
	#define	CG_MAIL_GUILD_BOOK_LEFT_1			238515	//
	#define	CG_MAIL_GUILD_BOOK_LEFT_2			238516	//
	#define	CG_MAIL_GUILD_BOOK_RIGHT_1			238513	//
	#define	CG_MAIL_GUILD_BOOK_RIGHT_2			238514	//
	#define	CG_MAIL_GUILD_BOOK_DETAIL_1			238533	//
	#define	CG_MAIL_GUILD_BOOK_DETAIL_2			238534	//
	//新规
	#define	CG_DETAIL_GUILD_BOOK_WIN			238535	//
	#define	CG_DETAIL_GUILD_BOOK_SET_1			22981	//
	#define	CG_DETAIL_GUILD_BOOK_SET_2			22982	//
	#define	CG_DETAIL_GUILD_BOOK_LEFT_1			238538	//
	#define	CG_DETAIL_GUILD_BOOK_LEFT_2			238539	//
	#define	CG_DETAIL_GUILD_BOOK_RIGHT_1		238536	//
	#define	CG_DETAIL_GUILD_BOOK_RIGHT_2		238537	//
	#define	CG_DETAIL_GUILD_BOOK_ON				238540	//
	//title
	#define	CG_TITLE_GUILD_BOOK_WIN				238556	//
	#define	CG_TITLE_GUILD_BOOK_LEFT_1			23022	//
	#define	CG_TITLE_GUILD_BOOK_LEFT_2			23026	//
	#define	CG_TITLE_GUILD_BOOK_RIGHT_1			23023	//
	#define	CG_TITLE_GUILD_BOOK_RIGHT_2			23027	//
	#define	CG_TITLE_GUILD_BOOK_REM_1			22983	//
	#define	CG_TITLE_GUILD_BOOK_REM_2			22984	//
	#define	CG_TITLE_GUILD_BOOK_SET_1			22981	//
	#define	CG_TITLE_GUILD_BOOK_SET_2			22982	//
	
#endif

// アドレズブックウィンドウソート设定ウィンドウ -----------
#define CG_MAIL_ADDRESS_BOOK_SORT_WIN			23170	// ソート设定ウィンドウ
// メールセレクトウィンドウ -------------------------------
#define CG_MAIL_SELECT_WIN						23184	// メールセレクトウィンドウ
#define CG_MAIL_SELECT_NORMAL_MAIL_BTN_1		23185	// 「通常メール」ボタン凸
#define CG_MAIL_SELECT_NORMAL_MAIL_BTN_2		23186	// 「通常メール」ボタン凹
#define CG_MAIL_SELECT_MONSTER_MAIL_BTN_1		23187	// 「モンスターメール」ボタン凸
#define CG_MAIL_SELECT_MONSTER_MAIL_BTN_2		23188	// 「モンスターメール」ボタン凹
// メールエディットウィンドウ -----------------------------
#define CG_MAIL_EDIT_WIN						23195	// メールエディットウィンドウ
#define CG_MAIL_EDIT_SEND_BTN_1					23196	// Sendボタン凸
#define CG_MAIL_EDIT_SEND_BTN_2					23197	// Sendボタン凹
#define CG_MAIL_EDIT_LEFT_BTN_1					22832	// 左ボタン凸
#define CG_MAIL_EDIT_LEFT_BTN_2					22833	// 左ボタン凹
#define CG_MAIL_EDIT_RIGHT_BTN_1				22834	// 右ボタン凸
#define CG_MAIL_EDIT_RIGHT_BTN_2				22835	// 右ボタン凹
// メールヒストリーウィンドウ -----------------------------
#define CG_MAIL_HISTORY_WIN						23190	// メールヒストリーウィンドウ
#define CG_MAIL_HISTORY_LEFT_BTN_1				22832	// 左ボタン凸
#define CG_MAIL_HISTORY_LEFT_BTN_2				22833	// 左ボタン凹
#define CG_MAIL_HISTORY_RIGHT_BTN_1				22834	// 右ボタン凸
#define CG_MAIL_HISTORY_RIGHT_BTN_2				22835	// 右ボタン凹
#define CG_MAIL_HISTORY_BACK_1					22714	// BACKボタン凸
#define CG_MAIL_HISTORY_BACK_2					22715	// BACKボタン凹
// メールペットセレクトウィンドウ -------------------------
#define CG_MAIL_PET_SELECT_WIN					23189	// メールペットセレクトウィンドウ
#define CG_MAIL_PET_SELECT_CANCEL_BTN_1			22710	// キャンセルボタン凸
#define CG_MAIL_PET_SELECT_CANCEL_BTN_2			22711	// キャンセルボタン凹
// メールペットエディットウィンドウ -----------------------
#define CG_MAIL_PET_EDIT_WIN					23200	// メールペットエディットウィンドウ
#define CG_MAIL_PET_EDIT_SEND_BTN_1				23196	// Sendボタン凸
#define CG_MAIL_PET_EDIT_SEND_BTN_2				23197	// Sendボタン凹
#define CG_MAIL_PET_EDIT_CANCEL_BTN_1			22710	// Cancelボタン凸
#define CG_MAIL_PET_EDIT_CANCEL_BTN_2			22711	// Cancelボタン凹
// センドアイテムセレクトウィンドウ -----------------------
#define CG_SEND_ITEM_SELECT_WIN					23205	// センドアイテムセレクトウィンドウ
#define CG_SEND_ITEM_SELECT_MASK				23040	// マスク(48x48)
// トレードウィンドウ -------------------------------------
#define CG_TRADE_WIN							23230	// トレードウィンドウ
#define CG_TRADE_OPPONENT_ITEM_WIN				23231	// 相手侧アイテムページ
#define CG_TRADE_OPPONENT_MONSTER_WIN			23233	// 相手侧モンスターページ
#define CG_TRADE_OPPONENT_GOLD_WIN				23234	// 相手侧お金ページ
#define CG_TRADE_OWNER_ITEM_WIN					23241	// 自分侧アイテムページ
#define CG_TRADE_OWNER_MONSTER_WIN				23243	// 自分侧モンスターページ
#define CG_TRADE_OWNER_GOLD_WIN					23244	// 自分侧お金ページ
#define CG_TRADE_0_BTN_2						23250	// お金入力用 0ボタン凹
#define CG_TRADE_1_BTN_2						23251	// お金入力用 1ボタン凹
#define CG_TRADE_2_BTN_2						23252	// お金入力用 2ボタン凹
#define CG_TRADE_3_BTN_2						23253	// お金入力用 3ボタン凹
#define CG_TRADE_4_BTN_2						23254	// お金入力用 4ボタン凹
#define CG_TRADE_5_BTN_2						23255	// お金入力用 5ボタン凹
#define CG_TRADE_6_BTN_2						23256	// お金入力用 6ボタン凹
#define CG_TRADE_7_BTN_2						23257	// お金入力用 7ボタン凹
#define CG_TRADE_8_BTN_2						23258	// お金入力用 8ボタン凹
#define CG_TRADE_9_BTN_2						23259	// お金入力用 9ボタン凹
#define CG_TRADE_BS_BTN_2						23260	// お金入力用 BSボタン凹
#define CG_TRADE_ALL_BTN_2						23261	// お金入力用 ALLボタン凹
#define CG_TRADE_CLEAR_BTN_2					23262	// お金入力用 CLEARボタン凹
#define CG_TRADE_OPEN_BTN_1						23264	// OPENボタン凸
#define CG_TRADE_OPEN_BTN_2						23265	// OPENボタン凹
#define CG_TRADE_TRADE_OK_1						23270	// TRADEボタン凸
#define CG_TRADE_TRADE_OK_2						23271	// TRADEボタン凹
#define CG_TRADE_CANCEL_1						23272	// CANCELボタン凸
#define CG_TRADE_CANCEL_2						23273	// CANCELボタン凹
#define CG_TRADE_ITEM_SELECT_MASK_RED			23040	// 选择禁止マーク（赤）
#define CG_TRADE_ITEM_SELECT_MASK_YELLOW		23041	// 选择禁止マーク（黄）
#define CG_TRADE_MONSTER_INFO_WIN				23277	// モンスター情报ウィンドウ
#define CG_TRADE_MONSTER_SKILL_INFO				23280	// モンスタースキル情报ウィンドウ
#define CG_TRADE_MONSTER_SKILL_BAR				23281	// スキル表示栏


// アクションウィンドウ -----------------------------------
#define CG_ACTION_WINDOW						22640	// アクションウィンドウ

// モンスターアルバムリストウィンドウ ---------------------
#define CG_MONSTER_ALBUM_LIST_WINDOW			22880	// モンスターアルバムリストウィンドウ
#define CG_MONSTER_ALBUM_LIST_LEFT_BTN_1		22881	// 左ボタン凸
#define CG_MONSTER_ALBUM_LIST_LEFT_BTN_2		22882	// 左ボタン凹
#define CG_MONSTER_ALBUM_LIST_RIGHT_BTN_1		22883	// 右ボタン凸
#define CG_MONSTER_ALBUM_LIST_RIGHT_BTN_2		22884	// 右ボタン凹
#define CG_MONSTER_ALBUM_LIST_NEW_ICON			22885	// ＮＥＷアイコン

// モンスターアルバムウィンドウ ---------------------------
#define CG_MONSTER_ALBUM_WINDOW					22890	// モンスターアルバムウィンドウ
#define CG_MONSTER_ALBUM_LIST_BTN_1				22891	// LISTボタン凸
#define CG_MONSTER_ALBUM_LIST_BTN_2				22892	// LISTボタン凹
#define CG_MONSTER_ALBUM_L_LEFT_BTN_1			22893	// 左送りボタン凸
#define CG_MONSTER_ALBUM_L_LEFT_BTN_2			22894	// 左送りボタン凹
#define CG_MONSTER_ALBUM_LEFT_BTN_1				22895	// 左ボタン凸
#define CG_MONSTER_ALBUM_LEFT_BTN_2				22896	// 左ボタン凹
#define CG_MONSTER_ALBUM_RIGHT_BTN_1			22897	// 右ボタン凸
#define CG_MONSTER_ALBUM_RIGHT_BTN_2			22898	// 右ボタン凹
#define CG_MONSTER_ALBUM_R_RIGHT_BTN_1			22899	// 右送りボタン凸
#define CG_MONSTER_ALBUM_R_RIGHT_BTN_2			22900	// 右送りボタン凹
#define CG_MONSTER_ALBUM_L_STAR					22901	// 左半分の星
#define CG_MONSTER_ALBUM_R_STAR					22902	// 右半分の星
#define CG_MONSTER_ALBUM_SEAL_OK				22903	// 封印マーク
#define CG_MONSTER_ALBUM_SEAL_NG				22904	// 封印不可マーク
#define CG_MONSTER_ALBUM_RARE					22905	// レアマーク
#define CG_MONSTER_ALBUM_VERY_RARE				22906	// 超レアマーク

// 文字登録ウィンドウ -------------------------------------
#define CG_CHAT_STR_SET_WINDOW1					22950	// チャット文字登録ウィンドウ１
#define CG_CHAT_STR_SET_WINDOW2					22951	// チャット文字登録ウィンドウ２
#define CG_CHAT_STR_SET_WINDOW3					22952	// チャット文字登録ウィンドウ３
#define CG_CHAT_STR_SET_WINDOW_TAB1				22953	// ウィンドウ１タブ
#define CG_CHAT_STR_SET_WINDOW_TAB2				22954	// ウィンドウ２タブ
#define CG_CHAT_STR_SET_WINDOW_TAB3				22955	// ウィンドウ３タブ

// お金画像番号 -------------------------------------------
#define CG_GOLD_1								27400	// お金画像１金货
#define CG_GOLD_2								27401	//    〃   ３金货
#define CG_GOLD_3								27402	//    〃   复数
#define CG_GOLD_4								27403	//    〃   小袋
#define CG_GOLD_5								27404	//    〃   中袋
#define CG_GOLD_6								27405	//    〃   大袋
#define CG_GOLD_7								27406	//    〃   大袋３个
#define CG_GOLD_8								27407	//    〃   小箱
#define CG_GOLD_9								27408	//    〃   中箱
#define CG_GOLD_10								27409	//    〃   大箱


// スキルショップ -----------------------------------------
#define CG_SKILL_SHOP_WINDOW_1					23050	// スキルショップウィンドウ「最初」
#define CG_SKILL_SHOP_WINDOW_2					23301	// 　　　　　〃　　　　　　「ＰＣが买」
#define CG_SKILL_SHOP_WINDOW_3					23300	// 　　　　　〃　　　　　　「はずす」
#define CG_SKILL_SHOP_LIST_BAR					22703	// リストバー

// モンスタースキルショップ -------------------------------
#define CG_MONSTER_SKILL_SHOP_WIN1				23090	// 贩卖ウィンドウ
#define CG_MONSTER_SKILL_SHOP_WIN2				23091	// モンスター选择ウィンドウ
#define CG_MONSTER_SKILL_SHOP_WIN3				23092	// 记忆位置选择ウィンドウ
#define CG_MONSTER_SKILL_CANCEL_BTN_1			23076	// CANCELボタン凸
#define CG_MONSTER_SKILL_CANCEL_BTN_2			23077	// CANCELボタン凹
#define CG_MONSTER_SKILL_LEFT_BTN_1				23022	// 左ボタン凸
#define CG_MONSTER_SKILL_LEFT_BTN_2				23026	// 左ボタン凹
#define CG_MONSTER_SKILL_RIGHT_BTN_1			23023	// 右ボタン凸
#define CG_MONSTER_SKILL_RIGHT_BTN_2			23027	// 右ボタン凹
#define CG_MONSTER_SKILL_PLATE					23093	// スキル表示栏

// アイテムショップ ---------------------------------------
#define CG_ITEM_SHOP_TOP_WIN					23050	// アイテムウィンドウのトップ
#define CG_ITEM_SHOP_SELL_WIN					23055	// アイテム买取ウィンドウ
#define CG_ITEM_SHOP_BUY_WIN					23055	// アイテム贩卖ウィンドウ
#define CG_ITEM_SHOP_SELL_BTN_1					23070	// SELLボタン凸
#define CG_ITEM_SHOP_SELL_BTN_2					23071	// SELLボタン凹
#define CG_ITEM_SHOP_SELL_BTN_3					23072	// SELLボタン凸(待机)
#define CG_ITEM_SHOP_BUY_BTN_1					23073	// BUYボタン凸
#define CG_ITEM_SHOP_BUY_BTN_2					23075	// BUYボタン凹
#define CG_ITEM_SHOP_BUY_BTN_3					23074	// BUYボタン凸(待机)
#define CG_ITEM_SHOP_CANCEL_BTN_1				23076	// CANCELボタン凸
#define CG_ITEM_SHOP_CANCEL_BTN_2				23077	// CANCELボタン凹

#ifdef _FISHING_WINDOW
// 釣り饵选择ウインドウ -----------------------------------
#define CG_FISHING_FEED_MENU_WIN				23354	// 釣り饵选择ウインドウ
#define CG_FISHING_FEED_MENU_BTN_1				23363	// 饵选择ボタン凸
#define CG_FISHING_FEED_MENU_BTN_2				23365	// 饵选择ボタン凹
#define CG_FISHING_FEED_MENU_BTN_3				23364	// 饵选择ボタン凸(クリックできない)
#endif /* _FISHING_WINDOW */

// ギャザーウィンドウ -------------------------------------
#define CG_GATHER_WIN							23357	// ギャザーウィンドウ

// 鉴定ＮＰＣウィンドウ -----------------------------------
#define CG_APPRAISAL_WIN						23055	// 鉴定ＮＰＣウィンドウ
#define CG_APPRAISAL_WIN_BTN_1					23083	// APPRAISALボタン凸	80*32　黄色
#define CG_APPRAISAL_WIN_BTN_2					23085	// APPRAISALボタン凹	80*32　暗い黄色
#define CG_APPRAISAL_WIN_BTN_3					23084	// APPRAISALボタン凸（待机）80*32　オレンジ
#define CG_APPRAISAL_WIN_CANCEL_BTN_1			23076	// CANCELボタン凸
#define CG_APPRAISAL_WIN_CANCEL_BTN_2			23077	// CANCELボタン凹
// 修理ＮＰＣウィンドウ -----------------------------------
#define CG_REPAIR_WIN							23055	// 修理ＮＰＣウィンドウ
#define CG_REPAIR_WIN_BTN_1						23080	// REPAIRボタン凸	80*32 黄色
#define CG_REPAIR_WIN_BTN_2						23082	// REPAIRボタン凹	80*32 暗い黄色
#define CG_REPAIR_WIN_BTN_3						23081	// REPAIRボタン凸（待机）80*32　オレンジ
#define CG_REPAIR_WIN_CANCEL_BTN_1				23076	// CANCELボタン凸
#define CG_REPAIR_WIN_CANCEL_BTN_2				23077	// CANCELボタン凹
// 揭示板ウィンドウ ---------------------------------------
#define CG_MESSAGE_BOARD_WIN					23224	// 揭示板ウィンドウ
#define CG_MESSAGE_BOARD_OK_BTN_1				23216	// OKボタン凸
#define CG_MESSAGE_BOARD_OK_BTN_2				23217	// OKボタン凹
#define CG_MESSAGE_BOARD_DELETE_BTN_1			23218	// DELETEボタン凸
#define CG_MESSAGE_BOARD_DELETE_BTN_2			23219	// DELETEボタン凹
#define CG_MESSAGE_BOARD_CANCEL_BTN_1			23214	// CANCELボタン凸
#define CG_MESSAGE_BOARD_CANCEL_BTN_2			23215	// CANCELボタン凹
#define CG_MESSAGE_BOARD_BACK_BTN_1				23225	// BACKボタン凸
#define CG_MESSAGE_BOARD_BACK_BTN_2				23226	// BACKボタン凹
#define CG_MESSAGE_BOARD_NEXT_BTN_1				23227	// NEXTボタン凸
#define CG_MESSAGE_BOARD_NEXT_BTN_2				23228	// NEXTボタン凹

// 银行ウィンドウ -----------------------------------------
#define CG_BANK_WIN								23340	// 银行ＮＰＣウィンドウ
#define CG_BANK_ITEM_WIN						23341	// アイテムページ
#define CG_BANK_MONSTER_WIN						23342	// モンスターページ
#define CG_BANK_DEPOSIT_WIN						23343	// 所持金额预けページ
#define CG_BANK_EDUCE_WIN						23344	// 所持金额引出しページ
#define CG_BANK_OK_BTN_1						23345	// ＯＫボタン凸
#define CG_BANK_OK_BTN_2						23346	// ＯＫボタン凹
#define CG_BANK_CANCEL_1						23272	// CANCELボタン凸
#define CG_BANK_CANCEL_2						23273	// CANCELボタン凹
#define CG_BANK_0_BTN_2							23250	// 数值入力用 0ボタン凹
#define CG_BANK_1_BTN_2							23251	// 数值入力用 1ボタン凹
#define CG_BANK_2_BTN_2							23252	// 数值入力用 2ボタン凹
#define CG_BANK_3_BTN_2							23253	// 数值入力用 3ボタン凹
#define CG_BANK_4_BTN_2							23254	// 数值入力用 4ボタン凹
#define CG_BANK_5_BTN_2							23255	// 数值入力用 5ボタン凹
#define CG_BANK_6_BTN_2							23256	// 数值入力用 6ボタン凹
#define CG_BANK_7_BTN_2							23257	// 数值入力用 7ボタン凹
#define CG_BANK_8_BTN_2							23258	// 数值入力用 8ボタン凹
#define CG_BANK_9_BTN_2							23259	// 数值入力用 9ボタン凹
#define CG_BANK_BS_BTN_2						23260	// 数值入力用 BSボタン凹
#define CG_BANK_ALL_BTN_2						23261	// 数值入力用 ALLボタン凹
#define CG_BANK_CLEAR_BTN_2						23262	// 数值入力用 CLEARボタン凹
#define CG_BANK_INPUT_OK_BTN_1					22712	// 入力ＯＫボタン凸
#define CG_BANK_INPUT_OK_BTN_2					22713	// 入力ＯＫボタン凹
#define CG_BANK_ITEM_SELECT_MASK_RED			23040	// アイテム选择禁止マーク（赤）
#define CG_BANK_ITEM_SELECT_MASK_YELLOW			23041	// アイテム选择禁止マーク（黄）
#define CG_BANK_MONSTER_SELECT_MASK_YELLOW		23348	// モンスター选择禁止マーク（黄）

// 交换ＮＰＣ ---------------------------------------------
#define CG_TRADE_NPC_TOP_WIN					23050	// 交换ＮＰＣウィンドウのトップ
#define CG_TRADE_NPC_WIN						23330	// 交换ＮＰＣウィンドウのメイン
#define CG_TRADE_NPC_LEFT_BTN_1					23022	// 左ボタン凸
#define CG_TRADE_NPC_LEFT_BTN_2					23026	// 左ボタン凹
#define CG_TRADE_NPC_RIGHT_BTN_1				23023	// 右ボタン凸
#define CG_TRADE_NPC_RIGHT_BTN_2				23027	// 右ボタン凹
#define CG_TRADE_NPC_TRADE_BTN_1				23331	// TRADEボタン凸
#define CG_TRADE_NPC_TRADE_BTN_2				23332	// TRADEボタン凹
#define CG_TRADE_NPC_TRADE_BTN_3				23333	// TRADEボタン凸(待机)
#define CG_TRADE_NPC_CANCEL_BTN_1				23076	// CANCELボタン凸
#define CG_TRADE_NPC_CANCEL_BTN_2				23077	// CANCELボタン凹

// アイテムカウントNPC ------------------------------------
#define CG_ITEMCOUNT_NPC_TOP_WIN					23050	// アイテムカウントNPCトップウインドウ
#define CG_ITEMCOUNT_NPC_MAIN_WIN					23330	// アイテムカウントNPCメインウインドウ
#define CG_ITEMCOUNT_NPC_LEFT_BTN_1					23022	// 左ボタン凸
#define CG_ITEMCOUNT_NPC_LEFT_BTN_2					23026	// 左ボタン凹
#define CG_ITEMCOUNT_NPC_RIGHT_BTN_1				23023	// 右ボタン凸
#define CG_ITEMCOUNT_NPC_RIGHT_BTN_2				23027	// 右ボタン凹
#define CG_ITEMCOUNT_NPC_TRADE_BTN_1				23331	// TRADEボタン凸
#define CG_ITEMCOUNT_NPC_TRADE_BTN_2				23332	// TRADEボタン凹
#define CG_ITEMCOUNT_NPC_TRADE_BTN_3				23333	// TRADEボタン凸(待机)
#define CG_ITEMCOUNT_NPC_CANCEL_BTN_1				23076	// CANCELボタン凸
#define CG_ITEMCOUNT_NPC_CANCEL_BTN_2				23077	// CANCELボタン凹

// タスクバー关连 -----------------------------------------
#define CG_TASK_BAR_BASE_WINDOW					22550	// タスクバーのベース
#define CG_TASK_BAR_MAP_BTN_2					22551	// マップボタン凹
#define CG_TASK_BAR_STATUS_BTN_2				22552	// 状态ボタン凹
#define CG_TASK_BAR_JOB_BTN_2					22553	// ジョブボタン凹
#define CG_TASK_BAR_ITEM_BTN_2					22554	// アイテムボタン凹
#define CG_TASK_BAR_MONSTER_BTN_2				22555	// モンスターボタン凹
#define CG_TASK_BAR_MAIL_BTN_2					22556	// メールボタン凹
#define CG_TASK_BAR_ALBUM_BTN_2					22557	// アルバムボタン凹
#define CG_TASK_BAR_SYSTEM_BTN_2				22558	// システムボタン凹

// １行インフォ关连 ---------------------------------------
#define CG_LINE_INFO_BAR						22560	// １行インフォのバー
#define CG_LINE_INFO_FONT_S_1					22561	// フォントサイズＳボタン凸
#define CG_LINE_INFO_FONT_S_2					22562	// フォントサイズＳボタン凹
#define CG_LINE_INFO_FONT_M_1					22563	// フォントサイズＭボタン凸
#define CG_LINE_INFO_FONT_M_2					22564	// フォントサイズＭボタン凹
#define CG_LINE_INFO_FONT_L_1					22565	// フォントサイズＭボタン凸
#define CG_LINE_INFO_FONT_L_2					22566	// フォントサイズＭボタン凹

// フィールド画面ボタン类 ---------------------------------
#define CG_FIELD_BTN_BASE_LEFT					22570	// 左上台座
#define CG_FIELD_MAIL_LAMP						22571	// メールランプ
#define CG_FIELD_LEVEL_UP_LAMP					22572	// 等级アップランプ
#define CG_FIELD_HELTH_LAMP						22573	// 健康状态ランプ
#define CG_FIELD_DUEL_OK_LAMP					22574	// デュエルＯＫランプ
#define CG_FIELD_CHAT_MODE_LAMP					22575	// チャットモードランプ
#define CG_FIELD_GROUP_OK_LAMP					22576	// グループＯＫランプ
#define CG_FIELD_MEISHI_OK_LAMP					22577	// 名刺交换ＯＫランプ
#define CG_FIELD_TRADE_OK_LAMP					22578	// 取引ＯＫランプ
#define CG_FIELD_BTN_BASE_RIGHT					22580	// 右上台座
#define CG_FIELD_DUEL_BTN_1						22581	// 对战ボタン凸
#define CG_FIELD_DUEL_BTN_2						22582	// 对战ボタン凹
#define CG_FIELD_SPECTATE_BTL_1					22583	// 観战ボタン凸
#define CG_FIELD_SPECTATE_BTL_2					22584	// 観战ボタン凹
#define CG_FIELD_GROUP_BTN_1					22585	// グループボタン凸
#define CG_FIELD_GROUP_BTN_2					22586	// グループボタン凹
#define CG_FIELD_CARD_BTN_1						22587	// 名刺ボタン凸
#define CG_FIELD_CARD_BTN_2						22588	// 名刺ボタン凹
#define CG_FIELD_TRADE_BTN_1					22589	// 取引ボタン凸
#define CG_FIELD_TRADE_BTN_2					22590	// 取引ボタン凸
#define CG_FIELD_ACT_BTN_1						22591	// アクションボタン凸
#define CG_FIELD_ACT_BTN_2						22592	// アクションボタン凹

#define CG_FIELD_TIME_ANIME_0					22596	// 时间アニメ（ 0～ 6 时）
#define CG_FIELD_TIME_ANIME_1					22597	//     〃    （ 6～12 时）
#define CG_FIELD_TIME_ANIME_2					22598	//     〃    （12～18 时）
#define CG_FIELD_TIME_ANIME_3					22599	//     〃    （18～24 时）

#define CG_FIELD_BTN_BASE_CENTER				22600	// 中央台座
#define CG_FIELD_DUEL_SET_BTN_1					22601	// デュエルＯＫ设定ボタン凸
#define CG_FIELD_DUEL_SET_BTN_2					22602	// デュエルＯＫ设定ボタン凹
#define CG_FIELD_CHAT_SET_BTN_1					22603	// チャットモード设定ボタン凸
#define CG_FIELD_CHAT_SET_BTN_2					22604	// チャットモード设定ボタン凹
#define CG_FIELD_GROUP_SET_BTN_1				22605	// グループＯＫ设定ボタン凸
#define CG_FIELD_GROUP_SET_BTN_2				22606	// グループＯＫ设定ボタン凹
#define CG_FIELD_MEISHI_SET_BTN_1				22607	// 名刺交换ＯＫ设定ボタン凸
#define CG_FIELD_MEISHI_SET_BTN_2				22608	// 名刺交换ＯＫ设定ボタン凹
#define CG_FIELD_TRADE_SET_BTN_1				22609	// 取引设定ボタン凸
#define CG_FIELD_TRADE_SET_BTN_2				22610	// 取引设定ボタン凹


#ifdef PUK2		//#30
	#define CG_FIELD_BTN_BASE_RIGHT_PUK2		238543	// 右上台座
	#define CG_FIELD_GUILD_BTN_1				238544
	#define CG_FIELD_GUILD_BTN_2				238545
	#define	CG_FIELD_GUILD_OK_LAMP				238542
	#define	CG_FIELD_BTN_BASE_LEFT_PUK2			238541
#endif


// 泛用ウィンドウ -----------------------------------------
#define CG_COMMON_WINDOW1_1						22620	// 泛用ウィンドウ（左上）
#define CG_COMMON_WINDOW1_2						22621	//       〃      （中央上）
#define CG_COMMON_WINDOW1_3						22622	//       〃      （右上）
#define CG_COMMON_WINDOW1_4						22623	//       〃      （左中）
#define CG_COMMON_WINDOW1_5						22624	//       〃      （中央中）
#define CG_COMMON_WINDOW1_6						22625	//       〃      （右中）
#define CG_COMMON_WINDOW1_7						22626	//       〃      （左下）
#define CG_COMMON_WINDOW1_8						22627	//       〃      （中央下）
#define CG_COMMON_WINDOW1_9						22628	//       〃      （右下）

#define CG_COMMON_WINDOW2_1						22630	// 泛用ウィンドウ２（左上）
#define CG_COMMON_WINDOW2_2						22631	//        〃       （中央上）
#define CG_COMMON_WINDOW2_3						22632	//        〃       （右上）
#define CG_COMMON_WINDOW2_4						22633	//        〃       （左中）
#define CG_COMMON_WINDOW2_5						22634	//        〃       （中央中）
#define CG_COMMON_WINDOW2_6						22635	//        〃       （右中）
#define CG_COMMON_WINDOW2_7						22636	//        〃       （左下）
#define CG_COMMON_WINDOW2_8						22637	//        〃       （中央下）
#define CG_COMMON_WINDOW2_9						22638	//        〃       （右下）

#ifdef _SYSTEMMENU_BTN_CONFIG
// 聊天范围用ボタン -----------------------------------
#define CG_CHAT_AREA_BTN_1_1					22990	// 聊天范围１凸
#define CG_CHAT_AREA_BTN_1_2					22991	// 聊天范围１凹
#define CG_CHAT_AREA_BTN_2_1					22992	// 聊天范围２凸
#define CG_CHAT_AREA_BTN_2_2					22993	// 聊天范围２凹
#define CG_CHAT_AREA_BTN_3_1					22994	// 聊天范围３凸
#define CG_CHAT_AREA_BTN_3_2					22995	// 聊天范围３凹
#define CG_CHAT_AREA_BTN_4_1					22996	// 聊天范围４凸
#define CG_CHAT_AREA_BTN_4_2					22997	// 聊天范围４凹
#define CG_CHAT_AREA_BTN_5_1					22998	// 聊天范围５凸
#define CG_CHAT_AREA_BTN_5_2					22999	// 聊天范围５凹
#endif /* _SYSTEMMENU_BTN_CONFIG */

// 泛用ボタン ---------------------------------------------
#define CG_COMMON_OK_BTN_1						23000	// 泛用ＯＫボタン凸
#define CG_COMMON_OK_BTN_2						23002	// 泛用ＯＫボタン凹
#define CG_COMMON_OK_BTN_3						23001	// 泛用ＯＫボタン凸待机
#define CG_COMMON_CANCEL_BTN_1					23003	// 泛用ＣＡＮＣＥＬボタン凸
#define CG_COMMON_CANCEL_BTN_2					23004	// 泛用ＣＡＮＣＥＬボタン凹
#define CG_COMMON_BACK_BTN_1					23005	// 泛用ＢＡＣＫボタン凸
#define CG_COMMON_BACK_BTN_2					23006	// 泛用ＢＡＣＫボタン凹

#define CG_COMMON_UP_BTN_1						23020	// 泛用↑ボタン凸
#define CG_COMMON_UP_BTN_2						23024	// 泛用↑ボタン凹
#define CG_COMMON_DOWN_BTN_1					23021	// 泛用↓ボタン凸
#define CG_COMMON_DOWN_BTN_2					23025	// 泛用↓ボタン凹
#define CG_COMMON_LEFT_BTN_1					23022	// 泛用←ボタン凸
#define CG_COMMON_LEFT_BTN_2					23026	// 泛用←ボタン凹
#define CG_COMMON_RIGHT_BTN_1					23023	// 泛用→ボタン凸
#define CG_COMMON_RIGHT_BTN_2					23027	// 泛用→ボタン凹

#define CG_COMMON_SMALL_CANCEL_BTN_1			22710	// 泛用キャンセルボタン（小）凸
#define CG_COMMON_SMALL_CANCEL_BTN_2			22711	// 　　　　〃　　　　　　　　凹
#define CG_COMMON_SMALL_OK_BTN_1				22712	// 泛用ＯＫボタン（小）凸
#define CG_COMMON_SMALL_OK_BTN_2				22713	// 　　　　〃　　　　　凹
#define CG_COMMON_SMALL_OK_BTN_3				22717	// 　　　　〃　　　　　凸
#define CG_COMMON_SMALL_BACK_BTN_1				22714	// 泛用バックボタン（小）凸
#define CG_COMMON_SMALL_BACK_BTN_2				22715	// 　　　　〃　　　　　　凹

#define CG_COMMON_RETRY_UP_BTN					23390	// リトライボタン凸
#define CG_COMMON_RETRY_DOWN_BTN				23391	// リトライボタン凹
#define CG_COMMON_STOP_UP_BTN					23395	// ストップボタン凸	80*20
#define CG_COMMON_STOP_DOWN_BTN					23396	// ストップボタン凹	80*20


//ハンコウインドウ --------------------------------------------
#define CG_HANKO_WINDOW							23358	//ハンコ用のウインドウ
#define CG_SHOVEL_WINDOW						CG_HANKO_WINDOW		//スコップ用のウインドウ


//---------------------------------------------------------


// キャラクタ颜グラフィック番号 ---------------------------
#define CG_FACE_0					30000	// 颜グラフィック（バウ）
#define CG_FACE_1					34000	//       〃      （カズ）
#define CG_FACE_2					38000	//       〃      （シン）
#define CG_FACE_3					46000	//       〃      （トブ）
#define CG_FACE_4					42000	//       〃      （ゲン）
#define CG_FACE_5					50000	//       〃      （ベイ）
#define CG_FACE_6					54000	//       〃      （ボグ）
#define CG_FACE_7					58000	//       〃      （）
#define CG_FACE_8					62000	//       〃      （）
#define CG_FACE_9					66000	//       〃      （）
#define CG_FACE_10					70000	//       〃      （ウル）
#define CG_FACE_11					74000	//       〃      （モエ）
#define CG_FACE_12					78000	//       〃      （アミ）
#define CG_FACE_13					82000	//       〃      （メグ）
#define CG_FACE_14					86000	//       〃      （レイ）
#define CG_FACE_15					90000	//       〃      （ケイ）
#define CG_FACE_16					94000	//       〃      （エル）
#define CG_FACE_17					98000	//       〃      （）


// プレイヤーのバトルボタン
#define CG_BTL_BUTTON_ATTACK_UP		25100
#define CG_BTL_BUTTON_ATTACK_DOWN	25101
#define CG_BTL_BUTTON_JUJUTU_UP		25102
#define CG_BTL_BUTTON_JUJUTU_DOWN	25103
#define CG_BTL_BUTTON_CAPTURE_UP	25104
#define CG_BTL_BUTTON_CAPTURE_DOWN	25105
#define CG_BTL_BUTTON_HELP_UP		25106
#define CG_BTL_BUTTON_HELP_DOWN		25107
#define CG_BTL_BUTTON_GUARD_UP		25108
#define CG_BTL_BUTTON_GUARD_DOWN	25109
#define CG_BTL_BUTTON_ITEM_UP		25110
#define CG_BTL_BUTTON_ITEM_DOWN		25111
#define CG_BTL_BUTTON_PET_UP		25112
#define CG_BTL_BUTTON_PET_DOWN		25113
#define CG_BTL_BUTTON_ESCAPE_UP		25114
#define CG_BTL_BUTTON_ESCAPE_DOWN	25115
#define CG_BTL_BUTTON_BASE			25116	// ボタンの土台
#define CG_BTL_BUTTON_CROSS			25117	// ×ボタン

// 属性アイコン
#define CG_ATR_ICON_EARTH_BIG		25120	// 「地」大
#define CG_ATR_ICON_EARTH_MDL		25121	// 「地」中
#define CG_ATR_ICON_EARTH_SML		25122	// 「地」小
#define CG_ATR_ICON_WATER_BIG		25123	// 「水」大
#define CG_ATR_ICON_WATER_MDL		25124	// 「水」中
#define CG_ATR_ICON_WATER_SML		25125	// 「水」小
#define CG_ATR_ICON_FIRE_BIG		25126	// 「火」大
#define CG_ATR_ICON_FIRE_MDL		25127	// 「火」中
#define CG_ATR_ICON_FIRE_SML		25128	// 「火」小
#define CG_ATR_ICON_WIND_BIG		25129	// 「风」大
#define CG_ATR_ICON_WIND_MDL		25130	// 「风」中
#define CG_ATR_ICON_WIND_SML		25131	// 「风」小

// 战闘中属性アイコン
#define CG_ATR_ICON_EARTH_BATTLE	25132	// 「地」
#define CG_ATR_ICON_WATER_BATTLE	25133	// 「水」
#define CG_ATR_ICON_FIRE_BATTLE		25134	// 「火」
#define CG_ATR_ICON_WIND_BATTLE		25135	// 「风」

// ペットのバトルボタン
#define CG_PET_BTL_BUTTON_BASE		25140	// ボタンの土台
#define CG_PET_BTL_BUTTON_WAZA_UP	25141	// 技ボタン凸
#define CG_PET_BTL_BUTTON_WAZA_DOWN	25142	// 技ボタン凹
#define CG_PET_BTL_BUTTON_CANCEL_UP	25143	// キャンセルボタン凸

// ヒットマーク
#define CG_HIT_MARK_00				25500	// 星大
#define CG_HIT_MARK_01				25501	// 星小

#define CG_HIT_MARK_10				25502	// 青色
#define CG_HIT_MARK_11				25503
#define CG_HIT_MARK_12				25504

#define CG_HIT_MARK_20				25505	// 赤色
#define CG_HIT_MARK_21				25506
#define CG_HIT_MARK_22				25507

#define CG_HIT_MARK_30				25508	// 水色
#define CG_HIT_MARK_31				25509
#define CG_HIT_MARK_32				25510

#define CG_HIT_MARK_40				25511	// 黄色
#define CG_HIT_MARK_41				25512
#define CG_HIT_MARK_42				25513

// 台词
#define CG_SPEECH_BTL_OK			25520
#define CG_SPEECH_CHANGE			25521
#define CG_SPEECH_GROUP				25522
#define CG_SPEECH_SUCCESS			25523
#define CG_SPEECH_YATTA				25524
#define CG_SPEECH_HELP				25525

// エンカウントマーク
#define CG_VS_MARK_1A				25610
#define CG_VS_MARK_1B				25611
#define CG_VS_MARK_2A				25612
#define CG_VS_MARK_2B				25613
#define CG_VS_MARK_3A				25614
#define CG_VS_MARK_3B				25615
#define CG_VS_MARK_4A				25616
#define CG_VS_MARK_4B				25617
#define CG_VS_MARK_5A				25618
#define CG_VS_MARK_5B				25619
#define CG_VS_MARK_6A				25620
#define CG_VS_MARK_6B				25621
#define CG_VS_MARK_7A				25622
#define CG_VS_MARK_7B				25623
#define CG_VS_MARK_8A				25624
#define CG_VS_MARK_8B				25625
#define CG_VS_MARK_9A				25626
#define CG_VS_MARK_9B				25627
#define CG_VS_MARK_10A				25628
#define CG_VS_MARK_10B				25629

#if 0
// カウントダウン数字
#define CG_CNT_DOWN_0				25900
#define CG_CNT_DOWN_1				25901
#define CG_CNT_DOWN_2				25902
#define CG_CNT_DOWN_3				25903
#define CG_CNT_DOWN_4				25904
#define CG_CNT_DOWN_5				25905
#define CG_CNT_DOWN_6				25906
#define CG_CNT_DOWN_7				25907
#define CG_CNT_DOWN_8				25908
#define CG_CNT_DOWN_9   			25909

#endif

// 泛用ウィンドウ
#define CG_WND_G_0			26001
#define CG_WND_G_1			26002
#define CG_WND_G_2			26003
#define CG_WND_G_3			26004
#define CG_WND_G_4			26005
#define CG_WND_G_5			26006
#define CG_WND_G_6			26007
#define CG_WND_G_7			26008
#define CG_WND_G_8			26009

// ウィンドウタイトル
#define CG_WND_TITLE_SYSTEM	26010
#define CG_WND_TITLE_LOGOUT	26011
#define CG_WND_TITLE_CHAT	26015
#define CG_WND_TITLE_BGM	26016
#define CG_WND_TITLE_SE		26017
#define CG_WND_TITLE_RESULT	26018
#define CG_WND_TITLE_MOUSE	26030

// タスクバー
#define CG_TASK_BAR_BACK		26012

// 战闘时のＨＰとＭＰバー
#define CG_BATTLE_BAR_PLAYER		26013	// プレイヤー
#define CG_BATTLE_BAR_PLAYER_2		26019	// プレイヤー
#define CG_BATTLE_BAR_PET			26014	// 他の人
#define CG_BATTLE_BAR_PET_2			26020	// プレイヤーのペット

// 泛用ウィンドウ２
#define CG_WND2_G_0			26021
#define CG_WND2_G_1			26022
#define CG_WND2_G_2			26023
#define CG_WND2_G_3			26024
#define CG_WND2_G_4			26025
#define CG_WND2_G_5			26026
#define CG_WND2_G_6			26027
#define CG_WND2_G_7			26028
#define CG_WND2_G_8			26029

// 泛用ウィンドウ３
#define CG_WND3_G_7			26037	// 下のみ、文字を打ち込める种类
#define CG_WND3_G_8			26038
#define CG_WND3_G_9			26039

// 本物ウィンドウ
#define CG_BTL_PET_CHANGE_WND	26040	// 战闘时のペット入れ替えウィンドウ
#define CG_BTL_PET_RETURN_BTN	26041	// 战闘时のペット戾すボタン（泛用）

// 泛用ボタン
#define CG_CLOSE_BTN			26042	// 关闭ボタン（泛用）
#define CG_RETURN_BTN			26043	// 返回按键（泛用）
#define CG_OK_BTN				26093	// ＯＫボタン（泛用）
#define CG_CANCEL_BTN			26050	// キャンセルボタン（泛用）

#define CG_YES_BTN				26094	// "はい"ボタン
#define CG_NO_BTN				26095	// "いいえ"ボタン
#define CG_EXIT_BTN				26096	// "出"ボタン
#define CG_SEAL_BTN				26097	// "卖"ボタン
#define CG_BUY_BTN				26098	// "买"ボタン



// 文字入り泛用ウィンドウ
#define CG_COMMON_WIN_YORO				26090	// "可以吗"と书かれたウィンドウ
#define CG_COMMON_YES_BTN				26091	// はいボタン
#define CG_COMMON_NO_BTN				26092	// 否按键


// ペットの技ウィンドウ
#define CG_PET_WAZA_WND		26130
#define CG_PET_WAZA_BAR_1	26131
#define CG_PET_WAZA_BAR_2	26132
#define CG_PET_WAZA_BAR_3	26133
#define CG_PET_WAZA_BAR_4	26134
#define CG_PET_WAZA_BAR_5	26135
#define CG_PET_WAZA_BAR_6	26136
#define CG_PET_WAZA_BAR_7	26137

// タスクバーボタン
#define CG_TASK_BAR_MAP_UP		26150
#define CG_TASK_BAR_MAP_DOWM	26151
#define CG_TASK_BAR_STATUS_UP	26152
#define CG_TASK_BAR_STATUS_DOWN	26153
#define CG_TASK_BAR_PET_UP		26154
#define CG_TASK_BAR_PET_DOWN	26155
#define CG_TASK_BAR_ITEM_UP		26156
#define CG_TASK_BAR_ITEM_DOWN	26157
#define CG_TASK_BAR_MAIL_UP		26158
#define CG_TASK_BAR_MAIL_DOWN	26159
#define CG_TASK_BAR_ALBUM_UP	26160
#define CG_TASK_BAR_ALBUM_DOWN	26161
#define CG_TASK_BAR_SYSTEM_UP	26162
#define CG_TASK_BAR_SYSTEM_DOWN	26163

// 前后ボタン
#define CG_PREV_BTN				26180	// ＜ボタン凸
#define CG_PREV_BTN_DOWN		26181	// ＜ボタン凹
#define CG_NEXT_BTN				26182	// ＞ボタン凸
#define CG_NEXT_BTN_DOWN		26183	// ＞ボタン凹

#define CG_PREV_BTN2			26184	// ??ボタン凸
#define CG_PREV_BTN2_DOWN		26185	// ??ボタン凹
#define CG_NEXT_BTN2			26186	// ??ボタン凸
#define CG_NEXT_BTN2_DOWN		26187	// ??ボタン凹

#define CG_UP_BTN				26188	// ▲ボタン凸
#define CG_UP_BTN_DOWN			26189	// ▲ボタン凹
#define CG_DOWN_BTN				26190	// ▼ボタン凸
#define CG_DOWN_BTN_DOWN		26191	// ▼ボタン凹


// アイコン
#define CG_ICON_FUKIDASI		22335	// チャット入力时后の吹き出し
#define CG_ICON_ENCOUNT			22364	// 通常战闘
#define CG_ICON_DUEL			22345	// デュエル
#define CG_ICON_WATCHING		22384	// 観战


#define CG_ICON_COME_ON			26501	// 「Come On!」
#define CG_ICON_GO				26502	// 「Go!」
#define CG_ICON_MISS			26504	// 「Miss...」
#define CG_ICON_FAIL			26505	// 「Fail...」
#define CG_ICON_GET				26506	// 「Get!!」
#define CG_ICON_COUNTER			26507	// 「Counter!」
#define CG_ICON_DANGER			26508	// 「Danger!」
#define CG_ICON_NO				26509	// 「No!」
#define CG_ICON_GUARD			26510	// 「Guard!」
#define CG_ICON_ESCAPE  		26511	// 「Escape!」
#define CG_ICON_CAPTURE 		26512	// 「Capture!」
#define CG_ICON_LEAVE			26513	// 「Leave!」
#define CG_ICON_GUARD_BREAK 	26514	// 「Guard Break!」
#define CG_ICON_CRUSH		 	26515	// 「Crush!」
#define CG_ICON_CAPTURE_UP	 	26516	// 「Capture Up!」

// ペットの颜无しグラフィック
#define CG_NOW_PAINTING			28999

// タイトル
#define CG_LOGO				29000
#define CG_TITLE_NAME_S		29002
#define CG_TITLE_NAME_T		29003
#define CG_TITLE_NAME_O 	29004
#define CG_TITLE_NAME_N		29005
#define CG_TITLE_NAME_E		29006
#define CG_TITLE_NAME_A		29007
#define CG_TITLE_NAME_G		29008
#define CG_TITLE_NAME_E2	29009
#define CG_TITLE_NAME		29010
#define CG_TITLE_NAME_FLASH		29011
#define CG_TITLE_NAME_FLASH1	29012
#define CG_TITLE_NAME_FLASH2	29013
#define CG_TITLE_NAME_FLASH3	29014
#define CG_TITLE_NAME_FLASH4	29015
#define CG_TITLE_NAME_FLASH5	29016
#define CG_TITLE_JSS_LOGO		29017
#define CG_TITLE_DREAM_LOGO		29018
#define CG_TITLE_NOW_LOADING	29019
#define CG_TITLE_ID_PASS		29020
#define CG_TITLE_ID_PASS_OK		29021
#define CG_TITLE_ID_PASS_QUIT	29022


//***********************************************
//见习板グラフィック
//***********************************************
#define CG_LEARN_BASE				23400		//(见习版1枚絵UPGRADEボタン付き）

#define	CG_LEARN_BACK_BTN			23401		//(バックボタン）
#define GG_LEARN_FINISHED           23402		//(见习卒业济1枚絵）

#define CG_LEARN_LOGIN_BTN_DOWN_L	23403		//(ろぐいん左暗い)
#define CG_LEARN_LOGIN_BTN_L		23404		//(ろぐいん左明るい)
#define CG_LEARN_DELETE_BTN_DOWN_L	23405		//(デリート左くらい)
#define	CG_LEARN_DELETE_BTN_L		23406		//(デリート右明るい)

#define CG_LEARN_LOGIN_BTN_DOWN_R	23407		//(ろぐいん右暗い)
#define CG_LEARN_LOGIN_BTN_R		23408		//(ろぐいん右明るい）
#define CG_LEARN_DELETE_BTN_DOWN_R	23409		//(デリート左くらい)
#define	CG_LEARN_DELETE_BTN_R		23410		//(デリート右明るい)

#define CG_LEARN_CREATE_BTN_DOWN_L	23411		//(クリエイト左くらい)
#define CG_LEARN_CREATE_BTN_L		23412		//(クリエイト左明るい）

#define CG_LEARN_CREATE_BTN_DOWN_R	23413		//(クリエイト右くらい)
#define CG_LEARN_CREATE_BTN_R		23414		//(クリエイト左明るい)


/*
#define CG_LEARN_PASS_BASE			23415
#define CG_LEARN_PASS_OK			23416
#define CG_LEARN_PASS_BACK			23417

#define CG_LEARN_ALLOW_DOWN_L		23418
#define CG_LEARN_ALLOW_L			23419

#define CG_LEARN_ALLOW_DOWN_R		23420
#define CG_LEARN_ALLOW_R			23421
*/
//**********************************************************************************/
// 战闘关连グラフィック																/
//**********************************************************************************/

// 战闘メニュー *******************************************
#define CG_B_BUTTON_BASE			22650	// プレイヤー用战闘ボタンベース	292x84
#define CG_B_BUTTON_ATTACK_UP		22651	// 攻击ボタン凸	292x84(90x21)
#define CG_B_BUTTON_ATTACK_DOWN		22652	// 攻击ボタン凹	292x84
#define CG_B_BUTTON_ATTACK_UNUSE	22653	// 攻击ボタン使用不可凹	292x84
#define CG_B_BUTTON_GUARD_UP		22654	// 防御ボタン凸	292x84(90x21)
#define CG_B_BUTTON_GUARD_DOWN		22655	// 防御ボタン凹	292x84
#define CG_B_BUTTON_GUARD_UNUSE		22656	// 防御ボタン使用不可凹	292x84
#define CG_B_BUTTON_SKILL_UP		22657	// スキルボタン凸	292x84(73x18)
#define CG_B_BUTTON_SKILL_DOWN		22658	// スキルボタン凹	292x84
#define CG_B_BUTTON_SKILL_UNUSE		22659	// スキルボタン使用不可凹	292x84
#define CG_B_BUTTON_ITEM_UP			22660	// アイテムボタン凸	292x84(73x18)
#define CG_B_BUTTON_ITEM_DOWN		22661	// アイテムボタン凹	292x84
#define CG_B_BUTTON_ITEM_UNUSE		22662	// アイテムボタン使用不可凹	292x84
#define CG_B_BUTTON_PET_UP			22663	// モンスターボタン凸	292x84(73x18)
#define CG_B_BUTTON_PET_DOWN		22664	// モンスターボタン凹	292x84
#define CG_B_BUTTON_PET_UNUSE		22665	// モンスターボタン使用不可凹	292x84
#define CG_B_BUTTON_EQUIP_UP		22666	// 武器ボタン凸	292x84(73x18)
#define CG_B_BUTTON_EQUIP_DOWN		22667	// 武器ボタン凹	292x84
#define CG_B_BUTTON_EQUIP_UNUSE		22668	// 武器ボタン使用不可凹	292x84
#define CG_B_BUTTON_POS_UP			22669	// 前卫后卫交代ボタン凸	292x84(73x18)
#define CG_B_BUTTON_POS_DOWN		22670	// 前卫后卫交代ボタン凹	292x84
#define CG_B_BUTTON_POS_UNUSE		22671	// 前卫后卫交代ボタン使用不可凹	292x84
#define CG_B_BUTTON_ESC_UP			22672	// 逃げるボタン凸	292x84(73x18)
#define CG_B_BUTTON_ESC_DOWN		22673	// 逃げるボタン凹	292x84
#define CG_B_BUTTON_ESC_UNUSE		22674	// 逃げるボタン使用不可凹	292x84
//#define CG_B_BUTTON_BASE			22675	//	
#define CG_B_BUTTON_BASE_2			22676	// プレイヤー用战闘ボタンベース２	292x84
#define CG_B_BUTTON_BASE_3			22677	// プライヤー用战闘ボタンベース３	292x84
//#define CG_B_BUTTON_BASE			22678	//	
//#define CG_B_BUTTON_BASE			22679	//	

#define CG_B_MONSTER_BASE			22680	// モンスター用战闘ボタンベース	292x84
#define CG_B_M_BUTTON_CANCEL_UP		22681	// キャンセルボタン凸	292x84
#define CG_B_M_BUTTON_CANCEL_DOWN	22682	// キャンセルボタン凹	292x84
#define CG_B_BUTTON_SKILL_LIST_UP	22683	// スキルリスト凸	同上
#define CG_B_BUTTON_SKILL_LIST_DOWN	22684	// スキルリスト凹	同上
#define CG_B_BUTTON_PASS			22685	// パスボタン


#define CG_B_MON_WND_PET_COME_BTN_UP	22690	// モンスターを引き戾すボタン凸	80x20	モンスターウインドウ内の下に

#define CG_B_SKILL_WND				22700	// バトル用スキルウインドウ	272x330
#define CG_B_SKILL_BASE				22703	// バトル用スキルベース

//#define CG_B_ABILITY_WND			22701	// バトル用アビリティウインドウ	272x332
#define CG_B_ABILITY_WND			22702	// バトル用アビリティウインドウ	272x366
#define CG_B_ABILITY_BASE			22704	// バトル用アビリティベース	248x20

#define CG_B_GENERAL_CANCEL_BTN_UP		22710	// （泛用）キャンセルボタン凸
#define CG_B_GENERAL_CANCEL_BTN_DOWN	22711	// （泛用）キャンセルボタン凹

#define CG_B_MON_WND					22720	// バトル用モンスターチェンジウインドウ	272x320
#define CG_B_MONSTER_LIST_CONDITION_GREEN		22721	// モンスター健康状态（緑）	272x320
#define CG_B_MONSTER_LIST_CONDITION_BLUE		22722	// モンスター健康状态（青）	272x320
#define CG_B_MONSTER_LIST_CONDITION_YELLOW		22723	// モンスター健康状态（黄色）	272x320
#define CG_B_MONSTER_LIST_CONDITION_ORANGE		22724	// モンスター健康状态（オレンジ）	272x320
#define CG_B_MONSTER_LIST_CONDITION_RED			22725	// モンスター健康状态（赤）	272x320

#define CG_B_MONSTER_LIST_TYPE_HUMAN			22726	// 种族表示(Human)	272x320
#define CG_B_MONSTER_LIST_TYPE_DRAGON			22727	// 种族表示(Dragon)	272x320
#define CG_B_MONSTER_LIST_TYPE_UNDEAD			22728	// 种族表示(Undead)	272x320
#define CG_B_MONSTER_LIST_TYPE_FLAY				22729	// 种族表示(Fly)	272x320
#define CG_B_MONSTER_LIST_TYPE_INCECT			22730	// 种族表示(Incect)	272x320
#define CG_B_MONSTER_LIST_TYPE_PLANT			22731	// 种族表示(Plant)	272x320
#define CG_B_MONSTER_LIST_TYPE_BEAST			22732	// 种族表示(Beast)	272x320
#define CG_B_MONSTER_LIST_TYPE_AMORPHAS			22733	// 种族表示(amorphaus)	272x320
#define CG_B_MONSTER_LIST_TYPE_METAL			22734	// 种族表示(Metal)	272x320
#define CG_B_MONSTER_LIST_TYPE_EVIL				22735	// 种族表示(Evil)	272x320

#define CG_B_WEAPON_CHANGE_WND			22736	// 武器交换ウインドウ	272x300
#define CG_B_ITEM_WND					22737	// アイテムウインドウ	272x320
#define CG_B_UNUSE_ITEM_NET				22738	// 使用不可アイテム用网	272x320

#define CG_B_WEAPON_NO_WEAPON_BTN_UP	22740	// 素手变更ボタン凸	80x20
#define CG_B_WEAPON_NO_WEAPON_BTN_DOWN	22741	// 素手变更ボタン凹	80x20
#define CG_B_WEAPON_NO_WEAPON_BTN_UNUSE	22742	// 素手变更ボタン凸（使用不可）	80x20

#define CG_B_MONSTER_SKILL_WND			22745	// バトル用モンスタースキルウインドウ	272x360
#define CG_B_MONSTER_SKILL_BASE			22746	// バトル用モンスタースキルベース		242,20

#define CG_B_PLAYER_GAUGE			22150	// バトル用プレイヤーゲージ	48x24
#define CG_B_PET_GAUGE				22151	// モンスターゲージ	48x24
#define CG_B_OTHER_GAUGE_VITAL		22152	// 他プレイヤー＆モンスターゲージ（体力のみ）	48x24
#define CG_B_OTHER_GAUGE			22153	// 他プレイヤー＆モンスターゲージ（气力付き）	48x24

#define CG_B_INFO_WND				22705	// 信息ウィンドウ

#define CG_BIGICE_DUST_0			29178	// 冰の魔法（全体）の破片01
#define CG_BIGICE_DUST_1			29179	// 冰の魔法（全体）の破片02
#define CG_BIGICE_DUST_2			29180	// 冰の魔法（全体）の破片03
#define CG_BIGICE_DUST_3			29181	// 冰の魔法（全体）の破片04
#define CG_BIGICE_DUST_4			29182	// 冰の魔法（全体）の破片05


#define CG_ARROW_00					22050	// 弓矢（22050～)
#define CG_KNIFE_00					22090	// ナイフ(22082～）

// 画面フラッシュ
#define CG_B_EFFECT_FLASH_WHITE		29227	// 画面フラッシュ（白）
#define CG_B_EFFECT_FLASH_AQUA		29228	// 画面フラッシュ（水色）
#define CG_B_EFFECT_FLASH_PURPLE	29229	// 画面フラッシュ（マゼンダ）
#define CG_B_EFFECT_FLASH_BLUE 		29230	// 画面フラッシュ（青）
#define CG_B_EFFECT_FLASH_YELLOW 	29231	// 画面フラッシュ（黄）
#define CG_B_EFFECT_FLASH_GREEN 	29232	// 画面フラッシュ（黄緑）
#define CG_B_EFFECT_FLASH_RED 		29233	// 画面フラッシュ（赤）
#define CG_B_EFFECT_FLASH_BLACK		29234	// 画面フラッシュ（黒）

// 新画面フラッシュ演出
#define CG_B_EFFECT_FLASH_NEW_GREEN		28881	// 画面フラッシュ（緑）
#define CG_B_EFFECT_FLASH_NEW_BLUE		28889	// 画面フラッシュ（青）
#define CG_B_EFFECT_FLASH_NEW_RED		28897	// 画面フラッシュ（赤）
#define CG_B_EFFECT_FLASH_NEW_YELLOW	28905	// 画面フラッシュ（黄）
#define CG_B_EFFECT_FLASH_NEW_BLACK		28913	// 画面フラッシュ（黒）



// カウントダウン数字     
#define CG_B_CNT_DOWN_0				22160
#define CG_B_CNT_DOWN2_0			22170
// 战闘吹き出し关连
#define CG_B_STR_ESCAPE				22320	// 逃げる文字
#define CG_B_STR_WEAPON				22321	// 武器チェンジ文字
#define CG_B_STR_POSITION			22322	// ポジションチェンジ
#define CG_B_STR_SKILL_UP			22323	// スキルアップ文字
#define CG_B_STR_LEAVE				22324	// ペット逃走文字

#define CG_B_ACT_1					22326	// ２アクション１
#define CG_B_ACT_2					22327	// ２アクション２

#define CG_B_STR_WEAPON_BREAK		22328	// 武器坏れた
#define CG_B_STR_INJURY				22329	// 怪我をした
#define CG_B_STR_WEAPON_DANGER		22330	// 武器坏れそうだ

#define CG_B_STR_MISS				22331	// ミス

#define CG_B_STR_STEAL_SUCCESS		22332	// 盗む成功
#define CG_B_STR_STEAL_MISS			22333	// 盗む失败

#define CG_B_STR_PET_NO				22334	// ペット嫌がる

#define CG_B_STR_PET_SUMMON			22338	// ペット出す
#define CG_B_STR_PET_RETURN			22339	// ペット戾す


#define CG_B_STR_GUARD_BREAK		22341	// 防御ブレイク
#define CG_B_STR_GOLD_ATTACK		22342	// ゴールド攻击
#define CG_B_STR_ENERGY_DRAIN		22343	// エナジードレイン

#define CG_B_STR_SURPRISAL			22344	// ビックリマーク

#define CG_B_STR_KILL				22403	// 暗杀ＫＩＬＬマーク

#define CG_B_STR_POW_UP				22404	// 踊り受伤アップ

#define CG_B_STR_SEAL				28880	// カード封印成功







//************ クロスゲート２用 **********************

#define SPR_shadow_pcm			100900		//	影キャラ男
#define SPR_shadow_pcf			100901		//	影キャラ女
#define SPR_shadow_mon			100902		//	影キャラモンスター


//モンタージュ用(今は假のＩＤ） 5/27

//影モンタージュ
#define CG2_FACE_SHADOW_PCM		33100
#define CG2_FACE_SHADOW_PCF		33101





#endif
