﻿#ifndef _ANIM_TBLEx_H_
#define _ANIM_TBLEx_H_

//---------------------------------------------------------
// このファイルはコンバータも通る
//
//	コンバーターは #define SPR_xxxxx の行を见て
//　  xxxxxファイルを読み込んで处理をする。
//　(例 SPR_abc は abc.sprを处理する)
//---------------------------------------------------------
#if 0

#ifdef MULTI_GRABIN
// アニメーション番号 -------------------------------------
#define SPRSTART			100000		// SPRデータの始まりの番号
#define SPREND				200000		// SPRデータの終わりくらいの番号
//#define SPRSTART2			300000		// SPRデータの始まりの番号
//#define SPR2OFFS			(SPRSTART2-12000)		// SPRデータの始まりの番号
//#define CHRNO_TO_SPR( a )   (( (a)>=SPRSTART2 )?((a)-SPR2OFFS):((a)-SPRSTART) )
#else
// アニメーション番号 -------------------------------------
#define SPRSTART			100000		// SPRデータの始まりの番号
#define SPREND				200000		// SPRデータの始まりの番号
#endif

#define SPRPC_START			100000		// ＰＣキャラ开始番号
#endif

//ＸＧＥＸ新キャラ
#define SPR_600ax			106000		//	人间A男1斧
#define SPR_600bw			106001		//	人间A男1弓
#define SPR_600em			106002		//	人间A男1素手
#define SPR_600sd			106003		//	人间A男1剑
#define SPR_600sf			106004		//	人间A男1杖
#define SPR_600sp			106005		//	人间A男1枪

#define SPR_601ax			106006		//	人间A男1斧
#define SPR_601bw			106007		//	人间A男1弓
#define SPR_601em			106008		//	人间A男1素手
#define SPR_601sd			106009		//	人间A男1剑
#define SPR_601sf			106010		//	人间A男1杖
#define SPR_601sp			106011		//	人间A男1枪

#define SPR_602ax			106012		//	人间A男1斧
#define SPR_602bw			106013		//	人间A男1弓
#define SPR_602em			106014		//	人间A男1素手
#define SPR_602sd			106015		//	人间A男1剑
#define SPR_602sf			106016		//	人间A男1杖
#define SPR_602sp			106017		//	人间A男1枪

#define SPR_603ax			106018		//	人间A男1斧
#define SPR_603bw			106019		//	人间A男1弓
#define SPR_603em			106020		//	人间A男1素手
#define SPR_603sd			106021		//	人间A男1剑
#define SPR_603sf			106022		//	人间A男1杖
#define SPR_603sp			106023		//	人间A男1枪


#define SPR_610ax			106025		//	人间B男1斧
#define SPR_610bw			106026		//	人间B男1弓
#define SPR_610em			106027		//	人间B男1素手
#define SPR_610sd			106028		//	人间B男1剑
#define SPR_610sf			106029		//	人间B男1杖
#define SPR_610sp			106030		//	人间B男1枪

#define SPR_611ax			106031		//	人间B男1斧
#define SPR_611bw			106032		//	人间B男1弓
#define SPR_611em			106033		//	人间B男1素手
#define SPR_611sd			106034		//	人间B男1剑
#define SPR_611sf			106035		//	人间B男1杖
#define SPR_611sp			106036		//	人间B男1枪

#define SPR_612ax			106037		//	人间B男1斧
#define SPR_612bw			106038		//	人间B男1弓
#define SPR_612em			106039		//	人间B男1素手
#define SPR_612sd			106040		//	人间B男1剑
#define SPR_612sf			106041		//	人间B男1杖
#define SPR_612sp			106042		//	人间B男1枪

#define SPR_613ax			106043		//	人间B男1斧
#define SPR_613bw			106044		//	人间B男1弓
#define SPR_613em			106045		//	人间B男1素手
#define SPR_613sd			106046		//	人间B男1剑
#define SPR_613sf			106047		//	人间B男1杖
#define SPR_613sp			106048		//	人间B男1枪


//XGEX新キャラ
#define SPR_700ax			106250		//	人间A女1斧
#define SPR_700bw			106251		//	人间A女1弓
#define SPR_700em			106252		//	人间A女1素手
#define SPR_700sd			106253		//	人间A女1剑
#define SPR_700sf			106254		//	人间A女1杖
#define SPR_700sp			106255		//	人间A女1枪

#define SPR_701ax			106256		//	人间A女1斧
#define SPR_701bw			106257		//	人间A女1弓
#define SPR_701em			106258		//	人间A女1素手
#define SPR_701sd			106259		//	人间A女1剑
#define SPR_701sf			106260		//	人间A女1杖
#define SPR_701sp			106261		//	人间A女1枪

#define SPR_702ax			106262		//	人间A女1斧
#define SPR_702bw			106263		//	人间A女1弓
#define SPR_702em			106264		//	人间A女1素手
#define SPR_702sd			106265		//	人间A女1剑
#define SPR_702sf			106266		//	人间A女1杖
#define SPR_702sp			106267		//	人间A女1枪

#define SPR_703ax			106268		//	人间A女1斧
#define SPR_703bw			106269		//	人间A女1弓
#define SPR_703em			106270		//	人间A女1素手
#define SPR_703sd			106271		//	人间A女1剑
#define SPR_703sf			106272		//	人间A女1杖
#define SPR_703sp			106273		//	人间A女1枪


#define SPR_710ax			106275		//	人间B女1斧
#define SPR_710bw			106276		//	人间B女1弓
#define SPR_710em			106277		//	人间B女1素手
#define SPR_710sd			106278		//	人间B女1剑
#define SPR_710sf			106279		//	人间B女1杖
#define SPR_710sp			106280		//	人间B女1枪

#define SPR_711ax			106281		//	人间B女1斧
#define SPR_711bw			106282		//	人间B女1弓
#define SPR_711em			106283		//	人间B女1素手
#define SPR_711sd			106284		//	人间B女1剑
#define SPR_711sf			106285		//	人间B女1杖
#define SPR_711sp			106286		//	人间B女1枪

#define SPR_712ax			106287		//	人间B女1斧
#define SPR_712bw			106288		//	人间B女1弓
#define SPR_712em			106289		//	人间B女1素手
#define SPR_712sd			106290		//	人间B女1剑
#define SPR_712sf			106291		//	人间B女1杖
#define SPR_712sp			106292		//	人间B女1枪

#define SPR_713ax			106293		//	人间B女1斧
#define SPR_713bw			106294		//	人间B女1弓
#define SPR_713em			106295		//	人间B女1素手
#define SPR_713sd			106296		//	人间B女1剑
#define SPR_713sf			106297		//	人间B女1杖
#define SPR_713sp			106298		//	人间B女1枪


// キャラクタ颜グラフィック番号 ---------------------------
#define CGEX_FACE_0					200000	// 颜グラフィック（バウ）
#define CGEX_FACE_1					200200	//       〃      （カズ）
#define CGEX_FACE_2					200400	//       〃      （シン）
#define CGEX_FACE_3					200600	//       〃      （ゲン）



#endif
