﻿//メニュー＞状态

void MenuWindowMenuStatusInit(void);
void MenuWindowMenuDetailInit(void);
void MenuWindowMenuTitleInit(void);
void MenuWindowMenuProfReDraw(void );

INPUT_STR MenuTitleInputStr;

//タイトル
INIT_STR_STRUCT InitStrStructTitle={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuTitleInputStr,123-18,47-16,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   1,      16,  0,     0
};

#ifdef PUK3
#ifdef PUK3_PROF
INPUT_STR MenuProfMenuSellInputStr;
INPUT_STR MenuProfMenuBuyInputStr;
INPUT_STR MenuProfMenuAboutInputStr;
INPUT_STR MenuProfMenuProfileInputStr;

INIT_STR_STRUCT InitStrStructProfMenuSell={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuProfMenuSellInputStr, 97,108,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   1,      26,  0,     0
};
INIT_STR_STRUCT InitStrStructProfMenuBuy={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuProfMenuBuyInputStr, 97,130,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   1,      26,  0,     0
};
INIT_STR_STRUCT InitStrStructProfMenuAbout={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuProfMenuAboutInputStr, 97,152,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   1,      26,  0,     0
};
INIT_STR_STRUCT InitStrStructProfMenuProf={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuProfMenuProfileInputStr, 97,170,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   4,      28,  14,     0
};
#endif
#endif



char UserTitleSendWork[256];

struct NewMenuTitleStatus{
	int mode;
	int ListPoint;
}TitleST;

//--------------------------------------------------------
//ログイン时の初期化
//--------------------------------------------------------
void InitMenuWindowMenuStatusInLogin(void){

	TitleST.mode=0;
	TitleST.ListPoint=0;

}
#ifdef PUK3_WINDOW_OPEN_POINT

// 状态ウィンドウ系のウィンドウクローズデストラクタ关数
BOOL MenuWindowMenuStatusCloseStatus()
{
	if ( !WindowFlag[MENU_WINDOW_STATUS].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_DETAIL].wx = WindowFlag[MENU_WINDOW_STATUS].wininfo->wx;
	WindowFlag[MENU_WINDOW_DETAIL].wy = WindowFlag[MENU_WINDOW_STATUS].wininfo->wy;
	WindowFlag[MENU_WINDOW_TITLE].wx = WindowFlag[MENU_WINDOW_STATUS].wininfo->wx;
	WindowFlag[MENU_WINDOW_TITLE].wy = WindowFlag[MENU_WINDOW_STATUS].wininfo->wy;
	WindowFlag[MENU_WINDOW_PROFILE].wx = WindowFlag[MENU_WINDOW_STATUS].wininfo->wx;
	WindowFlag[MENU_WINDOW_PROFILE].wy = WindowFlag[MENU_WINDOW_STATUS].wininfo->wy;

	return TRUE;
}
BOOL MenuWindowMenuStatusCloseDetail()
{
	if ( !WindowFlag[MENU_WINDOW_DETAIL].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_STATUS].wx = WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx;
	WindowFlag[MENU_WINDOW_STATUS].wy = WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy;
	WindowFlag[MENU_WINDOW_TITLE].wx = WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx;
	WindowFlag[MENU_WINDOW_TITLE].wy = WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy;
	WindowFlag[MENU_WINDOW_PROFILE].wx = WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx;
	WindowFlag[MENU_WINDOW_PROFILE].wy = WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy;

	return TRUE;
}
BOOL MenuWindowMenuStatusCloseTitle()
{
	if ( !WindowFlag[MENU_WINDOW_TITLE].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_STATUS].wx = WindowFlag[MENU_WINDOW_TITLE].wininfo->wx;
	WindowFlag[MENU_WINDOW_STATUS].wy = WindowFlag[MENU_WINDOW_TITLE].wininfo->wy;
	WindowFlag[MENU_WINDOW_DETAIL].wx = WindowFlag[MENU_WINDOW_TITLE].wininfo->wx;
	WindowFlag[MENU_WINDOW_DETAIL].wy = WindowFlag[MENU_WINDOW_TITLE].wininfo->wy;
	WindowFlag[MENU_WINDOW_PROFILE].wx = WindowFlag[MENU_WINDOW_TITLE].wininfo->wx;
	WindowFlag[MENU_WINDOW_PROFILE].wy = WindowFlag[MENU_WINDOW_TITLE].wininfo->wy;

	return TRUE;
}
BOOL MenuWindowMenuStatusCloseProfile()
{
	if ( !WindowFlag[MENU_WINDOW_TITLE].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_STATUS].wx = WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx;
	WindowFlag[MENU_WINDOW_STATUS].wy = WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy;
	WindowFlag[MENU_WINDOW_DETAIL].wx = WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx;
	WindowFlag[MENU_WINDOW_DETAIL].wy = WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy;
	WindowFlag[MENU_WINDOW_TITLE].wx = WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx;
	WindowFlag[MENU_WINDOW_TITLE].wy = WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy;

	return TRUE;
}

#endif

//状态------------------------------------------------------------------------------------------------
BOOL MenuWindowMenuStatus( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
	}

	//内容书き换え
	MenuWindowMenuStatusInit();

	return TRUE;
}

BOOL MenuWindowMenuStatusDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

//状态スイッチ
BOOL MenuSwitchStatus( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph;

	switch(no){
		//ディティールへ
		case EnumGraphMenuStatusDetail:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtDetailOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_DETAIL );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtDetailOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_DETAIL].wininfo==NULL){
					wI->flag |= WIN_INFO_DEL;
					createMenuWindow( MENU_WINDOW_DETAIL );
					WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx=wI->wx;
					WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy=wI->wy;
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					ReturnFlag=TRUE;	
				}
			}
			break;

		//タイトルへ
		case EnumGraphMenuStatusTitle:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtTitleOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_TITLE );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtTitleOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL){
					wI->flag |= WIN_INFO_DEL;
					createMenuWindow( MENU_WINDOW_TITLE );
					WindowFlag[MENU_WINDOW_TITLE].wininfo->wx=wI->wx;
					WindowFlag[MENU_WINDOW_TITLE].wininfo->wy=wI->wy;
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					ReturnFlag=TRUE;	
				}
			}
			break;

#ifdef PUK3_PROF
		//プロフィールへ
		case EnumGraphMenuStatusProfile:
			if(PackageVer>=PV_PUK3){
				if( flag & MENU_MOUSE_OVER ){
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOver;
					strcpy( OneLineInfoStr, MWONELINE_STATUS_PROFILE );
					ReturnFlag=TRUE;	
				}else{
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOn;
				}
				if( flag & MENU_MOUSE_LEFT ){
					if(WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL){
						wI->flag |= WIN_INFO_DEL;
						createMenuWindow( MENU_WINDOW_PROFILE );
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx=wI->wx;
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy=wI->wy;
						// ウィンドウ开く音
						play_se( SE_NO_OPEN_WINDOW, 320, 240 );
						ReturnFlag=TRUE;	
					}
				}
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOn;
			}
			break;
#endif

		//战闘位置を前へ
		case EnumNoMenuStatusPosFront:	
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumNoMenuStatusPosFront].Switch;
				Graph->graNo=GID_StatusPosFrontOver;	
				strcpy( OneLineInfoStr, MWONELINE_STATUS_FRONT );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumNoMenuStatusPosFront].Switch;
				Graph->graNo=GID_StatusPosFrontOff;	
			}
			//ボタン处理
			if( flag & MENU_MOUSE_LEFT ){
				if(pc.battlePositionFlag==0){
					//ポジション变更
					nrproto_POS_send( sockfd );
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
			break;	

		//战闘位置を后ろへ
		case EnumNoMenuStatusPosBack:
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumNoMenuStatusPosBack].Switch;
				Graph->graNo=GID_StatusPosBackOver;	
				strcpy( OneLineInfoStr, MWONELINE_STATUS_BACK );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumNoMenuStatusPosBack].Switch;
				Graph->graNo=GID_StatusPosBackOff;	
			}

			//ボタン处理
			if( flag & MENU_MOUSE_LEFT ){
				if(pc.battlePositionFlag==1){
					//ポジション变更
					nrproto_POS_send( sockfd );
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			break;	
		}
	}	

	return ReturnFlag;
}

//状态初期化
void MenuWindowMenuStatusInit(void ){

	char StrWork[256];
	int i,j;
	GRAPHIC_SWITCH	*Graph;


	//フラフィック关连初期化
	//颜グラフィック指定
	Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusFace].Switch;
	Graph->graNo=pc.faceGraNo;

	//属性表示
	//地
	for (i=0;i<10;i++)
		wI->sw[EnumGraphMenuStatusEarth0+i].Enabled=FALSE;
	j=pc.attr[0]/10;
	if(j==1){	//属性が１０のときは专用画像
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusEarth0].Switch;
		Graph->graNo=GID_StatusEarthOnly;
		wI->sw[EnumGraphMenuStatusEarth0].Enabled=TRUE;
	}else{
		for(i=0;i<j;i++){
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusEarth0+i].Switch;
			Graph->graNo=GID_StatusEarthCenter;
			wI->sw[EnumGraphMenuStatusEarth0+i].Enabled=TRUE;
			if(i==0){	//头画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusEarth0+i].Switch;
				Graph->graNo=GID_StatusEarthLeft;
				wI->sw[EnumGraphMenuStatusEarth0+i].Enabled=TRUE;
			}
			if(i==j-1){	//后ろ画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusEarth0+i].Switch;
				Graph->graNo=GID_StatusEarthRight;
				wI->sw[EnumGraphMenuStatusEarth0+i].Enabled=TRUE;
			}
		}
	}

	//水
	for (i=0;i<10;i++)
		wI->sw[EnumGraphMenuStatusWater0+i].Enabled=FALSE;
	j=pc.attr[1]/10;
	if(j==1){	//属性が１０のときは专用画像
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWater0].Switch;
		Graph->graNo=GID_StatusWaterOnly;
		wI->sw[EnumGraphMenuStatusWater0].Enabled=TRUE;
	}else{
		for(i=0;i<j;i++){
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWater0+i].Switch;
			Graph->graNo=GID_StatusWaterCenter;
			wI->sw[EnumGraphMenuStatusWater0+i].Enabled=TRUE;
			if(i==0){	//头画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWater0+i].Switch;
				Graph->graNo=GID_StatusWaterLeft;
				wI->sw[EnumGraphMenuStatusWater0+i].Enabled=TRUE;
			}
			if(i==j-1){	//后ろ画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWater0+i].Switch;
				Graph->graNo=GID_StatusWaterRight;
				wI->sw[EnumGraphMenuStatusWater0+i].Enabled=TRUE;
			}
		}
	}

	//火
	for (i=0;i<10;i++)
		wI->sw[EnumGraphMenuStatusFire0+i].Enabled=FALSE;
	j=pc.attr[2]/10;
	if(j==1){	//属性が１０のときは专用画像
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusFire0].Switch;
		Graph->graNo=GID_StatusFireOnly;
		wI->sw[EnumGraphMenuStatusFire0].Enabled=TRUE;
	}else{
		for(i=0;i<j;i++){
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusFire0+i].Switch;
			Graph->graNo=GID_StatusFireCenter;
			wI->sw[EnumGraphMenuStatusFire0+i].Enabled=TRUE;
			if(i==0){	//头画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusFire0+i].Switch;
				Graph->graNo=GID_StatusFireLeft;
				wI->sw[EnumGraphMenuStatusFire0+i].Enabled=TRUE;
			}
			if(i==j-1){	//后ろ画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusFire0+i].Switch;
				Graph->graNo=GID_StatusFireRight;
				wI->sw[EnumGraphMenuStatusFire0+i].Enabled=TRUE;
			}
		}
	}

	//风
	for (i=0;i<10;i++)
		wI->sw[EnumGraphMenuStatusWind0+i].Enabled=FALSE;
	j=pc.attr[3]/10;
	if(j==1){	//属性が１０のときは专用画像
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWind0].Switch;
		Graph->graNo=GID_StatusWindOnly;
		wI->sw[EnumGraphMenuStatusWind0].Enabled=TRUE;
	}else{
		for(i=0;i<j;i++){
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWind0+i].Switch;
			Graph->graNo=GID_StatusWindCenter;
			wI->sw[EnumGraphMenuStatusWind0+i].Enabled=TRUE;
			if(i==0){	//头画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWind0+i].Switch;
				Graph->graNo=GID_StatusWindLeft;
				wI->sw[EnumGraphMenuStatusWind0+i].Enabled=TRUE;
			}
			if(i==j-1){	//后ろ画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusWind0+i].Switch;
				Graph->graNo=GID_StatusWindRight;
				wI->sw[EnumGraphMenuStatusWind0+i].Enabled=TRUE;
			}
		}
	}

	//テキスト关连初期化
	//名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusName].Switch)->text,pc.name);
	//等级
	sprintf( StrWork, "%3d", pc.lv );										                          //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusLevel].Switch)->text,StrWork);
	//职业
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusJob].Switch)->text,job.name);
	//家族
	if(guildBook.pcGuildTitleId!=-1){
		//家族名
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusGuild].Switch)->text,guildBook.guildName);
		//家族称号
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusGuildTitle].Switch)->text,guildBook.title[guildBook.pcGuildTitleId].name);
	}else{
		//家族名
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusGuild].Switch)->text,"");
		//家族称号
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusGuildTitle].Switch)->text,"");
	}
	//称号
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusSpecialTitle].Switch)->text,charTitleInfo[pc.titleId].title);
	//自称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusUserTitle].Switch)->text,pc.freeName);
	//种族
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusType].Switch)->text,characterTribeStr[pc.tribe]);
	//经验值
	sprintf( StrWork, "%9d", pc.exp );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusExp].Switch)->text,StrWork);
	//体力（ライフポイント）
	sprintf( StrWork, "%4d/%4d", pc.lp,pc.maxLp);										              //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusLP].Switch)->text,StrWork);
	//魔力（フィジカルポイント）
	sprintf( StrWork, "%4d/%4d", pc.fp,pc.maxFp );										             //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusFP].Switch)->text,StrWork);
	//次までの经验值
	if(pc.nextExp==-1){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusNext].Switch)->text,"        -"); //MLHIDE
	}else{
		sprintf( StrWork, "%9d", pc.nextExp );										                    //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusNext].Switch)->text,StrWork);
	}
	//デュエルポイント
	sprintf( StrWork, "%9d", pc.dp );										                          //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusDP].Switch)->text,StrWork);

	//ポジション
	if(pc.battlePositionFlag==1){
		wI->sw[EnumNoMenuStatusPosChar].ofx=235+10-18;		
		wI->sw[EnumNoMenuStatusPosChar].ofy=213+3-13;		
	}else{
		wI->sw[EnumNoMenuStatusPosChar].ofx=271+10-18;		
		wI->sw[EnumNoMenuStatusPosChar].ofy=213+3-13;		
	}

	//健康状态（ヘルス）
	if(pc.injuryLv == 0){
		//健康状态
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusHealth].Switch;
		Graph->graNo=GID_StatusHealth0;
	}else if( pc.injuryLv <= 25 ){
		//かすりキズ
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusHealth].Switch;
		Graph->graNo=GID_StatusHealth1;
	}else if( pc.injuryLv <= 50 ){
		//軽伤
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusHealth].Switch;
		Graph->graNo=GID_StatusHealth2;
	}else if( pc.injuryLv <= 75 ){
		//重症
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusHealth].Switch;
		Graph->graNo=GID_StatusHealth3;
	}else{
		//濒死
		Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatusHealth].Switch;
		Graph->graNo=GID_StatusHealth4;
	}

	if(pc.stunPoint>0){
		//失われている时
		sprintf( StrWork, "x%d", pc.stunPoint);										                   //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuStatusStun].Switch)->text,StrWork);
		wI->sw[EnumTextMenuStatusStun].Enabled=TRUE;
		wI->sw[EnumGraphMenuStatusSprit].Enabled=TRUE;
	}else{
		//一つも失われていないときは非表示
		wI->sw[EnumTextMenuStatusStun].Enabled=FALSE;
		wI->sw[EnumGraphMenuStatusSprit].Enabled=FALSE;
	}
	

}


//ディティール----------------------------------------------------------------------------------------------
BOOL MenuWindowMenuDetail( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
	}

	//内容书き换え
	MenuWindowMenuDetailInit();

	return TRUE;
}

BOOL MenuWindowMenuDetailDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;
}

//ディティールスイッチ
BOOL MenuSwitchDetail( int no, unsigned int flag ){

	int i;
	int maxPoint;
	int point[6];
	GRAPHIC_SWITCH	*Graph;
	BOOL ReturnFlag=FALSE;	

	//状态チェック
	point[0] = pc.vtl;
	point[1] = pc.str;
	point[2] = pc.tgh;
	point[3] = pc.qui;
	point[4] = pc.mgc;
	point[5] = bonusPoint;
	maxPoint = 0;
	for( i = 0; i < 6; i++ )
	{
		maxPoint += point[i];
	}
	maxPoint /= 2;

	switch(no){
		//状态へ
		case EnumGraphMenuDetailStatus:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtStatusOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_STATUS );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtStatusOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_STATUS].wininfo==NULL){
					wI->flag |= WIN_INFO_DEL;
					createMenuWindow( MENU_WINDOW_STATUS );
					WindowFlag[MENU_WINDOW_STATUS].wininfo->wx=wI->wx;
					WindowFlag[MENU_WINDOW_STATUS].wininfo->wy=wI->wy;
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					ReturnFlag=TRUE;	
				}			
			}
			break;

		//タイトルへ
		case EnumGraphMenuDetailTitle:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtTitleOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_TITLE );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtTitleOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL){
					wI->flag |= WIN_INFO_DEL;
					createMenuWindow( MENU_WINDOW_TITLE );
					WindowFlag[MENU_WINDOW_TITLE].wininfo->wx=wI->wx;
					WindowFlag[MENU_WINDOW_TITLE].wininfo->wy=wI->wy;
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					ReturnFlag=TRUE;	
				}
			}
			break;

#ifdef PUK3_PROF
		//プロフィールへ
		case EnumGraphMenuDetailProfile:
			if(PackageVer>=PV_PUK3){
				if( flag & MENU_MOUSE_OVER ){
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOver;
					strcpy( OneLineInfoStr, MWONELINE_STATUS_PROFILE );
					ReturnFlag=TRUE;	
				}else{
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOn;
				}
				if( flag & MENU_MOUSE_LEFT ){
					if(WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL){
						wI->flag |= WIN_INFO_DEL;
						createMenuWindow( MENU_WINDOW_PROFILE );
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx=wI->wx;
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy=wI->wy;
						// ウィンドウ开く音
						play_se( SE_NO_OPEN_WINDOW, 320, 240 );
						ReturnFlag=TRUE;	
					}
				}
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOn;
			}
			break;
#endif

		case EnumGraphMenuDetailVITUp:
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_PLUS_ON );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(point[0] < maxPoint){
					// 振り分けた场所をサーバに知らせる
					nrproto_LVUP_send( sockfd, 0 );
					// クリック音
					play_se( SE_NO_OK2, 320, 240 );

				}
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphMenuDetailSTRUp:
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_PLUS_ON );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(point[1] < maxPoint){
					// 振り分けた场所をサーバに知らせる
					nrproto_LVUP_send( sockfd, 1 );
					// クリック音
					play_se( SE_NO_OK2, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphMenuDetailTGHUp:
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_PLUS_ON );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(point[2] < maxPoint){
					// 振り分けた场所をサーバに知らせる
					nrproto_LVUP_send( sockfd, 2 );
					// クリック音
					play_se( SE_NO_OK2, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphMenuDetailQUIUp:
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_PLUS_ON );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(point[3] < maxPoint){
					// 振り分けた场所をサーバに知らせる
					nrproto_LVUP_send( sockfd, 3 );
					// クリック音
					play_se( SE_NO_OK2, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphMenuDetailMGCUp:
			if( flag & MENU_MOUSE_OVER ){
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_STATUS_PLUS_ON );
				ReturnFlag=TRUE;
			}else{
				Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
				Graph->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(point[4] < maxPoint){
					// 振り分けた场所をサーバに知らせる
					nrproto_LVUP_send( sockfd, 4 );
					// クリック音
					play_se( SE_NO_OK2, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			break;
	
	}

	return ReturnFlag; 
}

//ディティール初期化
void MenuWindowMenuDetailInit(void ){

	char StrWork[256];
	int LVUpPointWork;

	//テキスト关连初期化
	//名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailName].Switch)->text,pc.name);
	//等级
	sprintf( StrWork, "%3d", pc.lv );										                          //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailLevel].Switch)->text,StrWork);
	//BONUS
	sprintf( StrWork, "%5d", bonusPoint );										                     //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailBonus].Switch)->text,StrWork);
	//VIT
	sprintf( StrWork, "%5d", pc.vtl );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailVIT].Switch)->text,StrWork);
	//STR
	sprintf( StrWork, "%5d", pc.str );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailSTR].Switch)->text,StrWork);
	//TGH
	sprintf( StrWork, "%5d", pc.tgh );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailTGH].Switch)->text,StrWork);
	//QUI
	sprintf( StrWork, "%5d", pc.qui );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailQUI].Switch)->text,StrWork);
	//MGC
	sprintf( StrWork, "%5d", pc.mgc );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailMGC].Switch)->text,StrWork);

	//ATK
	sprintf( StrWork, "%5d", pc.atk );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailATK].Switch)->text,StrWork);
	//DEF
	sprintf( StrWork, "%5d", pc.def );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailDEF].Switch)->text,StrWork);
	//AGL
	sprintf( StrWork, "%5d", pc.agi );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailAGL].Switch)->text,StrWork);
	//MND
	sprintf( StrWork, "%5d", pc.mnd );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailMND].Switch)->text,StrWork);
	//RCV
	sprintf( StrWork, "%5d", pc.rcv );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailRCV].Switch)->text,StrWork);
	//CHM
	sprintf( StrWork, "%5d", pc.chm );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailCHM].Switch)->text,StrWork);
#ifdef VERSION_TW
	//台服客户端新增的魔攻和魔防
	//ADM
	sprintf(StrWork, "%5d", pc.adm);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailADM].Switch)->text, StrWork);
	//RSS
	sprintf(StrWork, "%5d", pc.rss);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailRSS].Switch)->text, StrWork);

	//修正为台服客户端显示
	//POI
	sprintf(StrWork, "%4d", pc.poi);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailPOI].Switch)->text, StrWork);
	//SLP
	sprintf(StrWork, "%4d", pc.slp);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailSLP].Switch)->text, StrWork);
	//STN
	sprintf(StrWork, "%4d", pc.stn);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailSTN].Switch)->text, StrWork);
	//ITX
	sprintf(StrWork, "%4d", pc.itx);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailITX].Switch)->text, StrWork);
	//CNF
	sprintf(StrWork, "%4d", pc.cnf);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailCNF].Switch)->text, StrWork);
	//AMN
	sprintf(StrWork, "%4d", pc.amn);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailAMN].Switch)->text, StrWork);

	//CRI
	sprintf(StrWork, "%4d", pc.cri);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailCRI].Switch)->text, StrWork);
	//CTR
	sprintf(StrWork, "%4d", pc.ctr);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailCTR].Switch)->text, StrWork);
	//HIT
	sprintf(StrWork, "%4d", pc.hit);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailHIT].Switch)->text, StrWork);
	//AVD
	sprintf(StrWork, "%4d", pc.avd);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailAVD].Switch)->text, StrWork);

	//STM
	sprintf(StrWork, "%4d", pc.stm);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailSTM].Switch)->text, StrWork);
	//DEX
	sprintf(StrWork, "%4d", pc.dex);                                     //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailDEX].Switch)->text, StrWork);
	//INT
	sprintf(StrWork, "%4d", pc.inte);                                    //MLHIDE
	strcpy(((TEXT_SWITCH*)wI->sw[EnumTextMenuDetailINT].Switch)->text, StrWork);
#else

	//POI
	sprintf( StrWork, "+%4d", pc.poi );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailPOI].Switch)->text,StrWork);
	//SLP
	sprintf( StrWork, "+%4d", pc.slp );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailSLP].Switch)->text,StrWork);
	//STN
	sprintf( StrWork, "+%4d", pc.stn );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailSTN].Switch)->text,StrWork);
	//ITX
	sprintf( StrWork, "+%4d", pc.itx );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailITX].Switch)->text,StrWork);
	//CNF
	sprintf( StrWork, "+%4d", pc.cnf );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailCNF].Switch)->text,StrWork);
	//AMN
	sprintf( StrWork, "+%4d", pc.amn );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailAMN].Switch)->text,StrWork);

	//CRI
	sprintf( StrWork, "+%4d", pc.cri );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailCRI].Switch)->text,StrWork);
	//CTR
	sprintf( StrWork, "+%4d", pc.ctr );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailCTR].Switch)->text,StrWork);
	//HIT
	sprintf( StrWork, "+%4d", pc.hit );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailHIT].Switch)->text,StrWork);
	//AVD
	sprintf( StrWork, "+%4d", pc.avd );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailAVD].Switch)->text,StrWork);

	//STM
	sprintf( StrWork, "%5d", pc.stm );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailSTM].Switch)->text,StrWork);
	//DEX
	sprintf( StrWork, "%5d", pc.dex );										                         //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailDEX].Switch)->text,StrWork);
	//INT
	sprintf( StrWork, "%5d", pc.inte );										                        //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuDetailINT].Switch)->text,StrWork);
#endif

	//状态アップボタン画像のＯＮ??ＯＦＦ
	if(bonusPoint>0){
		LVUpPointWork=pc.vtl+pc.str+pc.tgh+pc.qui+pc.mgc+bonusPoint;
		LVUpPointWork/=2;
		if(pc.vtl < LVUpPointWork){
			wI->sw[EnumGraphMenuDetailVITUp].Enabled=TRUE;
		}else{
			wI->sw[EnumGraphMenuDetailVITUp].Enabled=FALSE;
		}
		if(pc.str < LVUpPointWork){
			wI->sw[EnumGraphMenuDetailSTRUp].Enabled=TRUE;
		}else{
			wI->sw[EnumGraphMenuDetailSTRUp].Enabled=FALSE;
		}
		if(pc.tgh < LVUpPointWork){
			wI->sw[EnumGraphMenuDetailTGHUp].Enabled=TRUE;
		}else{
			wI->sw[EnumGraphMenuDetailTGHUp].Enabled=FALSE;
		}
		if(pc.qui < LVUpPointWork){
			wI->sw[EnumGraphMenuDetailQUIUp].Enabled=TRUE;
		}else{
			wI->sw[EnumGraphMenuDetailQUIUp].Enabled=FALSE;
		}
		if(pc.mgc < LVUpPointWork){
			wI->sw[EnumGraphMenuDetailMGCUp].Enabled=TRUE;
		}else{
			wI->sw[EnumGraphMenuDetailMGCUp].Enabled=FALSE;
		}
	}else{
		wI->sw[EnumGraphMenuDetailVITUp].Enabled=FALSE;
		wI->sw[EnumGraphMenuDetailSTRUp].Enabled=FALSE;
		wI->sw[EnumGraphMenuDetailTGHUp].Enabled=FALSE;
		wI->sw[EnumGraphMenuDetailQUIUp].Enabled=FALSE;
		wI->sw[EnumGraphMenuDetailMGCUp].Enabled=FALSE;
	}
	
}

//タイトル--------------------------------------------------------------------------------------------------
BOOL MenuWindowMenuTitle( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
		TitleST.ListPoint=0;
		strcpy(UserTitleSendWork,pc.freeName);
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuUserTitle].Switch)->text,pc.freeName);
	}

	//内容书き换え	
	MenuWindowMenuTitleInit();

	return TRUE;
}

BOOL MenuWindowMenuTitleDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

//タイトル入力ダイアログ
BOOL MenuSwitchTitleUserName( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	DIALOG_SWITCH *Dialog;

	if( flag & MENU_MOUSE_OVER ){
		strcpy( OneLineInfoStr, MWONELINE_STATUS_USERSET );
	}

	if( flag & MENU_MOUSE_LEFT ){
		if(DiarogST.SwAdd!=wI->sw[EnumDialogUserName].Switch){

			strcpy(InitStrStructTitle.str,UserTitleSendWork);
			//ダイアログ初期化
			SetInputStr(&InitStrStructTitle,wI->wx,wI->wy,0);
			//フォーカスを取る
			GetKeyInputFocus( &MenuTitleInputStr );

			DiarogST.SwAdd=wI->sw[EnumDialogUserName].Switch;
			Dialog=(DIALOG_SWITCH *)wI->sw[EnumDialogUserName].Switch;
			Dialog->InpuStrAdd=&MenuTitleInputStr;
		}
			
		ReturnFlag=TRUE;
	}


	return ReturnFlag;

}

//リスト选择
BOOL MenuSwitchSetTitleSelectList( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	int ListNo;

	if( flag & MENU_MOUSE_OVER ){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePanelOver;
		strcpy( OneLineInfoStr, MWONELINE_STATUS_SPECIALPANEL );
		ReturnFlag=TRUE;
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePanelOff;
	}

	if( flag & MENU_MOUSE_LEFT ){
		switch(TitleST.mode){
		//特殊称号をリストから选择し、セット
		case 0:
			// 选择した栏に情报がある时
			if( sortCharTitleInfo[no-EnumGraphMenuTitleList00+TitleST.ListPoint].id >= 0 ){
					ListNo = getNoCharTitle( sortCharTitleInfo[no-EnumGraphMenuTitleList00+TitleST.ListPoint].id );
					if( ListNo >= 0 ){
							// 称号选择プロトコル送信
							nrproto_ST_send( sockfd, ListNo );
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
					}
			}
			break;
		//特殊称号を削除
		case 1:
			ListNo = getNoCharTitle( sortCharTitleInfo[no-EnumGraphMenuTitleList00+TitleST.ListPoint].id );
			if( ListNo >= 0 ){
				// 称号削除プロトコル送信
				nrproto_DT_send( sockfd, ListNo );
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			break;
		}
		ReturnFlag=TRUE;
	}
	
	
	return ReturnFlag;

}


//タイトルスイッチ
BOOL MenuSwitchSetTitle( int no, unsigned int flag ){

	// 自由称号の作业バッファ
	char freeName[128];
	char szSendBuffer[128];
	BOOL ReturnFlag=FALSE;	

	switch(no){
	//状态へ
	case EnumGraphMenuTitleStatus:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtStatusOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_STATUS );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtStatusOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_STATUS].wininfo==NULL){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_STATUS );
				WindowFlag[MENU_WINDOW_STATUS].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_STATUS].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}			
		}
		break;

	//ディティールへ
	case EnumGraphMenuTitleDetail:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtDetailOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_DETAIL );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtDetailOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_DETAIL].wininfo==NULL){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_DETAIL );
				WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}
		}
		break;

#ifdef PUK3_PROF
		//プロフィールへ
		case EnumGraphMenuTitleProfile:
			if(PackageVer>=PV_PUK3){
				if( flag & MENU_MOUSE_OVER ){
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOver;
					strcpy( OneLineInfoStr, MWONELINE_STATUS_PROFILE );
					ReturnFlag=TRUE;	
				}else{
					((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOn;
				}
				if( flag & MENU_MOUSE_LEFT ){
					if(WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL){
						wI->flag |= WIN_INFO_DEL;
						createMenuWindow( MENU_WINDOW_PROFILE );
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx=wI->wx;
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy=wI->wy;
						// ウィンドウ开く音
						play_se( SE_NO_OPEN_WINDOW, 320, 240 );
						ReturnFlag=TRUE;	
					}
				}
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonOn;
			}
			break;
#endif

	//セット
	case EnumGraphMenuTitleSetButton:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleSetOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_SPECIALPANEL );
			ReturnFlag=TRUE;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleSetOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			strcpy( freeName, UserTitleSendWork );
			// ここは S-JIS から EUC に变换して信息をエスケープする。
			makeSendString( freeName, szSendBuffer, sizeof( szSendBuffer ) );
			// 设定した称号をサーバに送信
			nrproto_FT_send( sockfd, szSendBuffer );
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			//ダイアログをチャットに变更
			SetDialogMenuChat();
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFTHOLD ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleSetOff;
		}
		break;
	//リムーブ
	case EnumGraphMenuTitleRemoveButton:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleRemoveOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_REMOVE );
			ReturnFlag=TRUE;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleRemoveOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			// 称号外すプロトコル送信
			nrproto_ST_send( sockfd, -1 );
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		if( flag & MENU_MOUSE_LEFTHOLD ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleRemoveOff;
		}
		break;

	//デリート
	case EnumGraphMenuTitleDeleteButton:
		if(TitleST.mode==1){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOff;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_DELETE );
		}
		if( flag & MENU_MOUSE_OVER ){
			if(TitleST.mode==0){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOver;
			}
			ReturnFlag=TRUE;
		}else{
			if(TitleST.mode==0){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleDeleteOn;
			}
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(TitleST.mode==1){
				TitleST.mode=0;
			}else{
				TitleST.mode=1;
			}
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		break;

	//スクロールアップボタン
	case EnumGraphMenuTitleScrollUp:

		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOver;
			ReturnFlag=TRUE;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOn;
		}
		
		if( flag & MENU_MOUSE_LEFTAUTO ){
			if(TitleST.ListPoint>0){
				TitleST.ListPoint--;
				NumToScrollVMove(&wI->sw[EnumBtMenuTitleDrag],CHAR_TITLE_MAX-6,TitleST.ListPoint);
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
			}
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFTHOLD ){
			ReturnFlag=TRUE;
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOff;
			ReturnFlag=TRUE;
		}
		break;

	//スクロールダウンボタン
	case EnumGraphMenuTitleScrollDown:

		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOver;
			ReturnFlag=TRUE;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOn;
		}

		if( flag & MENU_MOUSE_LEFTAUTO ){
			if(TitleST.ListPoint<CHAR_TITLE_MAX-6){
				TitleST.ListPoint++;
				NumToScrollVMove(&wI->sw[EnumBtMenuTitleDrag],CHAR_TITLE_MAX-6,TitleST.ListPoint);
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
			}
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFTHOLD ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOff;
			ReturnFlag=TRUE;
		}
		break;
		
	//ページアップ
	case EnumGraphMenuTitlePageUpButton:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePageUpOver;
			ReturnFlag=TRUE;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePageUpOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			TitleST.ListPoint+=6;
			if(TitleST.ListPoint>CHAR_TITLE_MAX-6)
				TitleST.ListPoint=CHAR_TITLE_MAX-6;
			NumToScrollVMove(&wI->sw[EnumBtMenuTitleDrag],CHAR_TITLE_MAX-6,TitleST.ListPoint);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		if( flag & MENU_MOUSE_LEFTHOLD ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePageUpOff;
		}
		break;

	//ページダウン
	case EnumGraphMenuTitlePageDownButton:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePageDownOver;
			ReturnFlag=TRUE;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePageDownOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			TitleST.ListPoint-=6;
			if(TitleST.ListPoint<0)
				TitleST.ListPoint=0;
			NumToScrollVMove(&wI->sw[EnumBtMenuTitleDrag],CHAR_TITLE_MAX-6,TitleST.ListPoint);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		if( flag & MENU_MOUSE_LEFTHOLD ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitlePageDownOff;
		}
		break;

	//ウインドウ上でボタンの无いところ
	case EnumBtMenuTitleWindow:
		if( flag & MENU_MOUSE_LEFT ){
			//デリートモード解除
			TitleST.mode=0;
			ReturnFlag=TRUE;
		}
		break;
	}

	return ReturnFlag;
}

//タイトル初期化
void MenuWindowMenuTitleInit(void ){

	char StrWork[256];
	int ListPoint;
	int i;

	//テキスト关连初期化
	//名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuTitleName].Switch)->text,pc.name);

	//等级
	sprintf( StrWork, "%3d", pc.lv );										                          //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuTitleLevel].Switch)->text,StrWork);

	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuSpecialTitle].Switch)->text,charTitleInfo[pc.titleId].title);

	//ダイアログ表示でない时はテキストのほうを表示
	if(	pNowInputStr != &MenuTitleInputStr){
		wI->sw[EnumTextMenuUserTitle].Enabled=TRUE;
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuUserTitle].Switch)->text,UserTitleSendWork);

		if(strcmp(UserTitleSendWork,pc.freeName)==0){
			((TEXT_SWITCH *)wI->sw[EnumTextMenuUserTitle].Switch)->color=FONT_PAL_WHITE;
		}else{
			((TEXT_SWITCH *)wI->sw[EnumTextMenuUserTitle].Switch)->color=FONT_PAL_RED;
		}
	}else{
		strcpy(UserTitleSendWork,InitStrStructTitle.inputStr->buffer);
		
		wI->sw[EnumTextMenuUserTitle].Enabled=FALSE;
		//ダイアログ位置
		SetInputStr(&InitStrStructTitle,wI->wx,wI->wy,1);
	}

	if(strcmp(UserTitleSendWork,pc.freeName)==0){
		wI->sw[EnumGraphMenuTitleSetButton].Enabled=FALSE;
	}else{
		wI->sw[EnumGraphMenuTitleSetButton].Enabled=TRUE;
	}

	//リストに文字列表示
//	TitleST.ListPoint=ScrollVPointToNum(&wI->sw[EnumBtMenuTitleDrag],CHAR_TITLE_MAX-6);
	ListPoint=TitleST.ListPoint;
	for(i=0;i<6;i++){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuTitleList00+i].Switch)->text,sortCharTitleInfo[ListPoint+i].title);
	}

}

BOOL MenuSwitchTitleScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		TitleST.ListPoint = WheelToMove( &wI->sw[EnumBtMenuTitleDrag],
			 TitleST.ListPoint, CHAR_TITLE_MAX-6, mouse.wheel );
	}

	return ReturnFlag;
}

BOOL MenuSwitchTitleScrollV( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	ReturnFlag=MenuSwitchScrollBarV(no,flag);

	if(ReturnFlag)
		TitleST.ListPoint=ScrollVPointToNum(&wI->sw[EnumBtMenuTitleDrag],CHAR_TITLE_MAX-6);


	return ReturnFlag;

}

#ifdef PUK3_PROF
//プロフィール--------------------------------------------------------------------------------------------------
BOOL MenuWindowMenuProf( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
//		strcpy(UserTitleSendWork,pc.freeName);
//		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuUserTitle].Switch)->text,pc.freeName);
	}

	//内容书き换え	
	MenuWindowMenuProfReDraw();

	return TRUE;
}

BOOL MenuWindowMenuProfDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

//タイトルスイッチ
BOOL MenuSwitchSetProf( int no, unsigned int flag ){

	// 自由称号の作业バッファ
//	char freeName[128];
//	char szSendBuffer[128];
	BOOL ReturnFlag=FALSE;	
	WINDOW_FLAG *wf;

	switch(no){
	//状态へ
	case EnumGraphMenuProfStatus:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtStatusOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_STATUS );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtStatusOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_STATUS].wininfo==NULL){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_STATUS );
				WindowFlag[MENU_WINDOW_STATUS].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_STATUS].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}			
		}
		break;

	//ディティールへ
	case EnumGraphMenuProfDetail:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtDetailOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_DETAIL );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtDetailOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_DETAIL].wininfo==NULL){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_DETAIL );
				WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}
		}
		break;

	//タイトルへ
	case EnumGraphMenuProfTitle:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtTitleOver;
			strcpy( OneLineInfoStr, MWONELINE_STATUS_TITLE );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_StatusBtTitleOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_TITLE );
				WindowFlag[MENU_WINDOW_TITLE].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_TITLE].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}
		}
		break;

	//Ｓｅｌｌリスト
	case EnumGraphMenuProfSellList:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonToListOver;
			strcpy( OneLineInfoStr, MWONELINE_CATEGORY_LIST );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonToListOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL){
				ProfileListMode=ProfileListModeSell;
				openMenuWindow( MENU_WINDOW_PROFILE_CATEGORY, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}else{
				wf=&WindowFlag[MENU_WINDOW_PROFILE_CATEGORY];
				if(wf!=NULL){
					wf->flag |= WIN_INFO_DEL;
				}

				ProfileListMode=ProfileListModeSell;
				openMenuWindow( MENU_WINDOW_PROFILE_CATEGORY, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}
		}

		if(WindowFlag[MENU_WINDOW_PROFILE_CATEGORY].wininfo!=NULL && ProfileListMode==ProfileListModeSell){
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfSellList].Switch))->graNo=GID_ProfButtonToListOff;
		}

		break;

	//Ｂｕｙリスト
	case EnumGraphMenuProfBuyList:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonToListOver;
			strcpy( OneLineInfoStr, MWONELINE_CATEGORY_LIST );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonToListOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL){
				ProfileListMode=ProfileListModeBuy;
				openMenuWindow( MENU_WINDOW_PROFILE_CATEGORY, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}else{
				wf=&WindowFlag[MENU_WINDOW_PROFILE_CATEGORY];
				if(wf!=NULL){
					wf->flag |= WIN_INFO_DEL;
				}

				ProfileListMode=ProfileListModeBuy;
				openMenuWindow( MENU_WINDOW_PROFILE_CATEGORY, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}
		}

		if(WindowFlag[MENU_WINDOW_PROFILE_CATEGORY].wininfo!=NULL && ProfileListMode==ProfileListModeBuy){
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfBuyList].Switch))->graNo=GID_ProfButtonToListOff;
		}

		break;

	//Ａｂｏｕｔリスト
	case EnumGraphMenuProfAboutList:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonToListOver;
			strcpy( OneLineInfoStr, MWONELINE_CATEGORY_LIST );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ProfButtonToListOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			if(WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL){
				ProfileListMode=ProfileListModeAbout;
				openMenuWindow( MENU_WINDOW_PROFILE_CATEGORY, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}else{
				wf=&WindowFlag[MENU_WINDOW_PROFILE_CATEGORY];
				if(wf!=NULL){
					wf->flag |= WIN_INFO_DEL;
				}

				ProfileListMode=ProfileListModeAbout;
				openMenuWindow( MENU_WINDOW_PROFILE_CATEGORY, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;	
			}
		}

		if(WindowFlag[MENU_WINDOW_PROFILE_CATEGORY].wininfo!=NULL && ProfileListMode==ProfileListModeAbout){
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfAboutList].Switch))->graNo=GID_ProfButtonToListOff;
		}

		break;

	//セットボタン
	case EnumGraphMenuProfSet:
		if( flag & MENU_MOUSE_OVER ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleSetOver;
			strcpy( OneLineInfoStr, MWONELINE_SET_PROFILE );
			ReturnFlag=TRUE;	
		}else{
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_TitleSetOn;
		}
		if( flag & MENU_MOUSE_LEFT ){
			//ダイアログをチャットに变更
			SetDialogMenuChat();
			//送信
			sendProfile(0);
			ReturnFlag=TRUE;	
		}
		break;

	//公开非公开
	case EnumGraphMenuProfFaceOverOnOff:

		if(MyProfile[selectPcNo].open){
		//公开时
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PROFILE_CLOSE );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFT ){
				nrproto_PRO_send(sockfd,0);
				MyProfile[selectPcNo].open=FALSE;
			}
		}else{
		//非公开时
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PROFILE_OPEN );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFT ){
				nrproto_PRO_send(sockfd,1);
				sendProfile(1);
				MyProfile[selectPcNo].open=TRUE;
			}
		}
		break;
	}

	return ReturnFlag;
}


//プロフィール描画
void MenuWindowMenuProfReDraw(void ){

	char StrWork[256];
//	int ListPoint;
//	int i;
	GRAPHIC_SWITCH *graph;
	char ProfileBuff[1024];

	//颜グラフィック指定
	graph=(GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfFace].Switch);
	graph->graNo=pc.faceGraNo;

	//公开非公开か
	if(MyProfile[selectPcNo].open){
		graph=(GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfFaceOverOnOff].Switch);
		graph->graNo=0;
	}else{
		graph=(GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfFaceOverOnOff].Switch);
		graph->graNo=GID_ProfOpenOnOff;
	}

	//テキスト关连
	//名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfName].Switch)->text,pc.name);
	//等级
	sprintf( StrWork, "%3d", pc.lv );										                          //MLHIDE
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfLevel].Switch)->text,StrWork);

	//枠内
	//タイトル
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyTitle].Switch)->text,pc.freeName);
	//职业
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyJob].Switch)->text,job.name);
	//家族
	if(guildBook.pcGuildTitleId!=-1){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyGuild].Switch)->text,guildBook.guildName);
	}else{
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyGuild].Switch)->text,"");
	}

	//アイコン画像
	graph=(GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfSellIcon].Switch);
	graph->graNo=MyProfile_SendWork.SellGraphID;

	graph=(GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfBuyIcon].Switch);
	graph->graNo=MyProfile_SendWork.BuyGraphID;

	graph=(GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfAboutIcon].Switch);
	graph->graNo=MyProfile_SendWork.AboutGraphID;

	//Sell
	if(	pNowInputStr != &MenuProfMenuSellInputStr){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMySell].Switch)->text,MyProfile_SendWork.SellStr);

		wI->sw[EnumTextMenuProfMySell].Enabled=TRUE;
		if(strcmp(MyProfile[selectPcNo].SellStr,MyProfile_SendWork.SellStr)==0 && (MyProfile[selectPcNo].SellID==MyProfile_SendWork.SellID)){
			if(MyProfile[selectPcNo].open && MyProfile_SendWork.SellID!=0){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMySell].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMySell].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMySell].Switch)->color=FONT_PAL_RED;
		}
	}else{
		wI->sw[EnumTextMenuProfMySell].Enabled=FALSE;
		//ダイアログ位置
		SetInputStr(&InitStrStructProfMenuSell,wI->wx,wI->wy,1);

		strcpy(MyProfile_SendWork.SellStr,InitStrStructProfMenuSell.inputStr->buffer);
	}

	//Buy
	if(	pNowInputStr != &MenuProfMenuBuyInputStr){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyBuy].Switch)->text,MyProfile_SendWork.BuyStr);

		wI->sw[EnumTextMenuProfMyBuy].Enabled=TRUE;
		if(strcmp(MyProfile[selectPcNo].BuyStr,MyProfile_SendWork.BuyStr)==0 && (MyProfile[selectPcNo].BuyID==MyProfile_SendWork.BuyID)){
			if(MyProfile[selectPcNo].open && MyProfile_SendWork.BuyID!=0){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyBuy].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyBuy].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyBuy].Switch)->color=FONT_PAL_RED;
		}
	}else{
		wI->sw[EnumTextMenuProfMyBuy].Enabled=FALSE;
		//ダイアログ位置
		SetInputStr(&InitStrStructProfMenuBuy,wI->wx,wI->wy,1);

		strcpy(MyProfile_SendWork.BuyStr,InitStrStructProfMenuBuy.inputStr->buffer);
	}

	//About
	if(	pNowInputStr != &MenuProfMenuAboutInputStr){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyAbout].Switch)->text,MyProfile_SendWork.AboutStr);

		wI->sw[EnumTextMenuProfMyAbout].Enabled=TRUE;
		if(strcmp(MyProfile[selectPcNo].AboutStr,MyProfile_SendWork.AboutStr)==0 && (MyProfile[selectPcNo].AboutID==MyProfile_SendWork.AboutID)){
			if(MyProfile[selectPcNo].open && MyProfile_SendWork.AboutID!=0){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyAbout].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyAbout].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyAbout].Switch)->color=FONT_PAL_RED;
		}
	}else{
		wI->sw[EnumTextMenuProfMyAbout].Enabled=FALSE;
		//ダイアログ位置
		SetInputStr(&InitStrStructProfMenuAbout,wI->wx,wI->wy,1);

		strcpy(MyProfile_SendWork.AboutStr,InitStrStructProfMenuAbout.inputStr->buffer);
	}

	//profile
	if(	pNowInputStr != &MenuProfMenuProfileInputStr){
		getMemoLine( ProfileBuff, sizeof( ProfileBuff ),MyProfile_SendWork.ProfileStr, 0, 28 );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile0].Switch)->text,ProfileBuff);
		getMemoLine( ProfileBuff, sizeof( ProfileBuff ),MyProfile_SendWork.ProfileStr, 1, 28 );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile1].Switch)->text,ProfileBuff);
		getMemoLine( ProfileBuff, sizeof( ProfileBuff ),MyProfile_SendWork.ProfileStr, 2, 28 );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile2].Switch)->text,ProfileBuff);
		getMemoLine( ProfileBuff, sizeof( ProfileBuff ),MyProfile_SendWork.ProfileStr, 3, 28 );
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile3].Switch)->text,ProfileBuff);

		wI->sw[EnumTextMenuProfMyProfile0].Enabled=TRUE;
		wI->sw[EnumTextMenuProfMyProfile1].Enabled=TRUE;
		wI->sw[EnumTextMenuProfMyProfile2].Enabled=TRUE;
		wI->sw[EnumTextMenuProfMyProfile3].Enabled=TRUE;
		if(strcmp(MyProfile[selectPcNo].ProfileStr,MyProfile_SendWork.ProfileStr)==0){
			if(MyProfile[selectPcNo].open){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile0].Switch)->color=FONT_PAL_WHITE;
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile1].Switch)->color=FONT_PAL_WHITE;
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile2].Switch)->color=FONT_PAL_WHITE;
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile3].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile0].Switch)->color=FONT_PAL_GRAY;
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile1].Switch)->color=FONT_PAL_GRAY;
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile2].Switch)->color=FONT_PAL_GRAY;
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile3].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile0].Switch)->color=FONT_PAL_RED;
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile1].Switch)->color=FONT_PAL_RED;
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile2].Switch)->color=FONT_PAL_RED;
			((TEXT_SWITCH *)wI->sw[EnumTextMenuProfMyProfile3].Switch)->color=FONT_PAL_RED;
		}
	}else{
		wI->sw[EnumTextMenuProfMyProfile0].Enabled=FALSE;
		wI->sw[EnumTextMenuProfMyProfile1].Enabled=FALSE;
		wI->sw[EnumTextMenuProfMyProfile2].Enabled=FALSE;
		wI->sw[EnumTextMenuProfMyProfile3].Enabled=FALSE;
		//ダイアログ位置
		SetInputStr(&InitStrStructProfMenuProf,wI->wx,wI->wy,1);

		strcpy(MyProfile_SendWork.ProfileStr,InitStrStructProfMenuProf.inputStr->buffer);
	}

	//现在の状况とＳｅｎｄ用の内容が违うならば送信ボタン表示
	if(checkProfileSendWork()){
		wI->sw[EnumGraphMenuProfSet].Enabled=TRUE;
	}else{
		wI->sw[EnumGraphMenuProfSet].Enabled=FALSE;
	}

}

//タイトル入力ダイアログ
BOOL MenuSwitchProfMenuUserName( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	DIALOG_SWITCH *Dialog;

	switch(no){
		case EnumDialogMenuProfSell:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PROFILE_DIALOG );
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(DiarogST.SwAdd!=wI->sw[EnumDialogMenuProfSell].Switch){

					strcpy(InitStrStructProfMenuSell.str,MyProfile_SendWork.SellStr);
					//ダイアログ初期化
					SetInputStr(&InitStrStructProfMenuSell,wI->wx,wI->wy,0);
					//フォーカスを取る
					GetKeyInputFocus( &MenuProfMenuSellInputStr );

					DiarogST.SwAdd=wI->sw[EnumDialogMenuProfSell].Switch;
					Dialog=(DIALOG_SWITCH *)wI->sw[EnumDialogMenuProfSell].Switch;
					Dialog->InpuStrAdd=&MenuProfMenuSellInputStr;
				}					
				ReturnFlag=TRUE;
			}
		break;

		case EnumDialogMenuProfBuy:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PROFILE_DIALOG );
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(DiarogST.SwAdd!=wI->sw[EnumDialogMenuProfBuy].Switch){

					strcpy(InitStrStructProfMenuBuy.str,MyProfile_SendWork.BuyStr);
					//ダイアログ初期化
					SetInputStr(&InitStrStructProfMenuBuy,wI->wx,wI->wy,0);
					//フォーカスを取る
					GetKeyInputFocus( &MenuProfMenuBuyInputStr );

					DiarogST.SwAdd=wI->sw[EnumDialogMenuProfBuy].Switch;
					Dialog=(DIALOG_SWITCH *)wI->sw[EnumDialogMenuProfBuy].Switch;
					Dialog->InpuStrAdd=&MenuProfMenuBuyInputStr;
				}					
				ReturnFlag=TRUE;
			}
		break;

		case EnumDialogMenuProfAbout:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PROFILE_DIALOG );
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(DiarogST.SwAdd!=wI->sw[EnumDialogMenuProfAbout].Switch){

					strcpy(InitStrStructProfMenuAbout.str,MyProfile_SendWork.AboutStr);
					//ダイアログ初期化
					SetInputStr(&InitStrStructProfMenuAbout,wI->wx,wI->wy,0);
					//フォーカスを取る
					GetKeyInputFocus( &MenuProfMenuAboutInputStr );

					DiarogST.SwAdd=wI->sw[EnumDialogMenuProfAbout].Switch;
					Dialog=(DIALOG_SWITCH *)wI->sw[EnumDialogMenuProfAbout].Switch;
					Dialog->InpuStrAdd=&MenuProfMenuAboutInputStr;
				}					
				ReturnFlag=TRUE;
			}
		break;

		case EnumDialogMenuProfProfile:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PROFILE_DIALOG );
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(DiarogST.SwAdd!=wI->sw[EnumDialogMenuProfProfile].Switch){

					strcpy(InitStrStructProfMenuProf.str,MyProfile_SendWork.ProfileStr);
					//ダイアログ初期化
					SetInputStr(&InitStrStructProfMenuProf,wI->wx,wI->wy,0);
					//フォーカスを取る
					GetKeyInputFocus( &MenuProfMenuProfileInputStr );

					DiarogST.SwAdd=wI->sw[EnumDialogMenuProfProfile].Switch;
					Dialog=(DIALOG_SWITCH *)wI->sw[EnumDialogMenuProfProfile].Switch;
					Dialog->InpuStrAdd=&MenuProfMenuProfileInputStr;
				}					
				ReturnFlag=TRUE;
			}
		break;

	}
	return ReturnFlag;

}

#endif