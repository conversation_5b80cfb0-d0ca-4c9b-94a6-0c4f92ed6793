﻿#include <stdio.h>
#include <stdlib.h>

/* WIN32_LEAN_AND_MEANをdefineするとWINDOWS.Hのうち
のあまり使われない部分がインクルードされなくなって
ちょっとだけコンパイルがはやくなる */
#define WIN32_LEAN_AND_MEAN 
#include <windows.h>
#include <math.h>

#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/map.h"
#include "../systeminc/pattern.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/math2.h"
#include"../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/handletime.h"

#include "../systeminc/character.h"
#include "../systeminc/pc.h"
#include "../systeminc/menu.h"
#include "../systeminc/main.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/anim_tbl2.h"
#include "../systeminc/sprdisp.h"
#ifdef PUK2
	#include "../systeminc/t_music.h"
	#include "../puk2/map/newmap.h"
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"

#include "../systeminc/tool.h"
#define DELIMITER	'|'					// 区切り文字
#endif

#ifdef PUK3
	#include "../PUK3/anim_tbl_PUK3.h"
#endif
#if defined(PUK3_WHALE_SHIP) || defined(PUK3_VEHICLE)
	#include "../systeminc/process.h"
#endif
#ifdef PUK3_VEHICLE
	#include "../puk2/interface/menuwin.h"
#endif

//海マップテーブル
static int sea_map_tbl[] = {
	59000,59001,59100,
};
//かもめマップテーブル
static int bird_map_tbl[] = {
	59000,59001,
};

// キャラ管理テーブル
enum
{
	CHAROBJ_USE_FREE,
	CHAROBJ_USE_STAY,
	CHAROBJ_USE_VIEW
};
#define MAX_CHAROBJ	1000
CHAROBJ charObj[MAX_CHAROBJ];
int maxCharObj;		// 登録数
int tailCharObj;	// テーブルの最后尾位置
int freeCharObj;	// 空きテーブル位置
int searchCharObj;	// 检索位置


// キャラごとにマークの高さを变える
int charIconOffsetY[] =
{
	-78,	//  0 バウ			100000
	-88,	//  1 カズ			100025
	-88,	//  2 シン			100050
	-81,	//  3 トブ			100075
	-88,	//  4 ゲン			100100
	-90,	//  5 ベイ			100125
	-99,	//  6 ボグ			100150
	-88,	//  7 				100175
	-88,	//  8 				100200
	-88,	//  9 				100225
	-79,	// 10 ウル			100250
	-86,	// 11 モエ			100275
	-88,	// 12 アミ			100300
	-85,	// 13 メグ			100325
	-88,	// 14 レイ			100350
	-88,	// 15 ケイ			100375
	-96,	// 16 エル			100400

	-78,	// 17 管理用バウ	100425～100449　（100425～100430）
	-88,	// 18 管理用シン	100450～100474　（100450～100455）
	-86,	// 19 管理用モエ	100475～100499　（100475～100480）
	-88,	// 20 管理用アミ	100500～100524　（100500～100505）
	-88,	// 21 NPC用カズ		100525～100549　（100525～100530）
	-81,	// 22 NPC用トブ		100550～100574　（100550～100555）
	-88,	// 23 NPC用ゲン		100575～100599　（100575～100580）
	-90,	// 24 NPC用ベイ		100600～100624　（100600～100605）
	-99,	// 25 NPC用ボグ		100625～100649　（100625～100630）
	-79,	// 26 NPC用ウル		100650～100674　（100650～100655）
	-85,	// 27 NPC用メグ		100675～100699　（100675～100680）
	-88,	// 28 NPC用レイ		100700～100724　（100700～100705）
	-88,	// 29 NPC用ケイ		100725～100749　（100725～100730）
	-96,	// 30 NPC用エル		100750～100774　（100750～100755）
	-99,	// 31 NPC用ボグ		100800～100824
	-88,	// 32 
	-88,	// 33 
	-88,	// 34 
	-88,	// 35 
	-88,	// 36 
	-88,	// 37 
	-88,	// 38 
	-88		// 39 
};

// バトルアイコン
int battleIconTbl[][2] =
{
	{  CG_VS_MARK_1A,  CG_VS_MARK_1B },
	{  CG_VS_MARK_2A,  CG_VS_MARK_2B },
	{  CG_VS_MARK_3A,  CG_VS_MARK_3B },
	{  CG_VS_MARK_4A,  CG_VS_MARK_4B },
	{  CG_VS_MARK_5A,  CG_VS_MARK_5B },
	{  CG_VS_MARK_6A,  CG_VS_MARK_6B },
	{  CG_VS_MARK_7A,  CG_VS_MARK_7B },
	{  CG_VS_MARK_8A,  CG_VS_MARK_8B },
	{  CG_VS_MARK_9A,  CG_VS_MARK_9B },
	{ CG_VS_MARK_10A, CG_VS_MARK_10B }
};
#ifdef PUK2
int headIconTbl[]={
	0,
	SPR_leader,	SPR_icn_2,	SPR_icn_3,	SPR_icn_4,	SPR_icn_5,
	SPR_icn_6,	SPR_icn_7,	SPR_icn_8,	SPR_icn_9,	SPR_icn_10,
	SPR_icn_11,	SPR_icn_12,	SPR_icn_13,	SPR_icn_14,	SPR_icn_15,
};
#endif

short nameOverTheHeadFlag = FALSE;

void charProc( ACTION * );

#ifdef PUK2
	#include "../PUK2/newDraw/newcharacter.cpp"
	int pattern( ACTION *pAct, int anim_spd, int loop_flg, char flg );
	char skillrebirth=0;
#endif


//-------------------------------------------------------------------------//
// 指定のキャラのアイコン表示のためのＹ方向OFFセット值を取得する          //
//-------------------------------------------------------------------------//
int getCharIconOffsetY( int graNo )
{
	if( graNo < SPRPC_START || SPRPC_END < graNo )
		return -88;

	return charIconOffsetY[(graNo-SPRPC_START)/25];
}


// キャラクタアクションのデフォルト
void charProc( ACTION *ptAct )
{
	float mx, my;
	int animLoop;
	int no;
	CHAREXTRA *ext;
	int pFlag;
#ifdef PUK2_DRESS_UP
	int charNum;
#endif

#ifdef _DEBUG
	//调试模式中pFlag没有初始化导致进游戏报错,Release版不会
	pFlag = 1;
#endif

#ifdef PUK2
	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
#endif
#ifdef PUK3_VEHICLE
	// 乘り物プロセスの最中なら終了
	if ( nowVehicleProc() ) return;

	// 不可视フラグが立っているなら終了
	if ( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
		if ( pc.status2 & CHR_STATUS2_INVISIBLE ) return;
	}else{
		if ( charObj[no].status2 & CHR_STATUS2_INVISIBLE ) return;
	}
#endif
#ifdef PUK3_CHARA_ACT
	if ( charAction(ptAct) ){
		return;
	}
#endif
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_charProc );
#endif

	// 移动处理
	// PC以外のキャラは自前で移动处理
	// PCはマップ移动の结果を入れてもらって移动する
	if( pc.ptAct != ptAct )
	{
#ifndef PUK2
		ext = (CHAREXTRA *)ptAct->pYobi;
		no = ext->charObjTblId;
#endif

		// 布ティに入っていないキャラだけ处理する
		if( (charObj[no].status & CHR_STATUS_PARTY) == 0 )
		{
			charMove( ptAct );
			pFlag = 0;
		}
		else
		{
			pFlag = 1;
		}
#if 1
		// 移动后の向き换え
		if( ptAct->vx == 0 && ptAct->vy == 0
		 && charObj[no].stockDir != -1
		 && charObj[no].stockDirX == ptAct->gx && charObj[no].stockDirY == ptAct->gy )
		{
			ptAct->anim_ang = charObj[no].stockDir;
			charObj[no].stockDir = -1;
			charObj[no].stockDirX = 0;
			charObj[no].stockDirY = 0;
		}
#endif
	}

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);


#ifdef PUK2_DRESS_UP
	// まだ自分を表示してないので、表示后に呼び出す
#else
	// 状态エフェクト表示
	drawCharStatus( ptAct );
#endif

#ifdef _FISHING_WINDOW
	// 船に乘ってるか判断するのに假でdirCntを使っています
	if( ptAct->dirCnt){
		ptAct->anim_no = ANIM_SIT;
	}
#endif /* _FISHING_WINDOW */

#ifdef PUK2
	// 公会宠物なら
	if ( pc.ptAct != ptAct && (charObj[no].type&CHAROBJ_TYPE_GUILMON) ){
		// アニメーション处理
		if(	ptAct->anim_no == ANIM_B_WALK
		 || ptAct->anim_no == ANIM_STAND
		 || ptAct->anim_no == ANIM_WALK
		 || ptAct->anim_no == ANIM_CHOKI
		 || ptAct->anim_no == ANIM_PA
		 || ptAct->anim_no == ANIM_HAPPY
		 || ptAct->anim_no == ANIM_SAD
		 || ptAct->anim_no == ANIM_SIT )
		{
			animLoop = ANM_LOOP;
		}else{
			animLoop = ANM_NO_LOOP;
		}
	}else{
#ifdef PUK3_RIDE
		// ライド中なら
		if ( ext->ptRide ){
			// アニメーション处理
			if( ext->ptRide->anim_no == ANIM_HAND
			 || ext->ptRide->anim_no == ANIM_HAPPY
			 || ext->ptRide->anim_no == ANIM_ANGRY
			 || ext->ptRide->anim_no == ANIM_SAD
			 || ext->ptRide->anim_no == ANIM_WALK
			 || ext->ptRide->anim_no == ANIM_STAND
			 || ext->ptRide->anim_no == ANIM_NOD
			 || ext->ptRide->anim_no == ANIM_B_WALK
			 || ext->ptRide->anim_no == ANIM_MAGIC )
			{
				animLoop = ANM_LOOP;
			}else{
				animLoop = ANM_NO_LOOP;
			}
		}else
#endif
		// アニメーション处理
		if( ptAct->anim_no == ANIM_HAND
		 || ptAct->anim_no == ANIM_HAPPY
		 || ptAct->anim_no == ANIM_ANGRY
		 || ptAct->anim_no == ANIM_SAD
		 || ptAct->anim_no == ANIM_WALK
		 || ptAct->anim_no == ANIM_STAND
		 || ptAct->anim_no == ANIM_NOD
		 || ptAct->anim_no == ANIM_B_WALK
		 || ptAct->anim_no == ANIM_MAGIC )
		{
			animLoop = ANM_LOOP;
		}else{
			animLoop = ANM_NO_LOOP;
		}
	}
#else
	// アニメーション处理
	if( ptAct->anim_no == ANIM_HAND
	 || ptAct->anim_no == ANIM_HAPPY
	 || ptAct->anim_no == ANIM_ANGRY
	 || ptAct->anim_no == ANIM_SAD
	 || ptAct->anim_no == ANIM_WALK
	 || ptAct->anim_no == ANIM_STAND
	 || ptAct->anim_no == ANIM_NOD
	 || ptAct->anim_no == ANIM_B_WALK
	 || ptAct->anim_no == ANIM_MAGIC )
	{
		animLoop = ANM_LOOP;
	}
	else
	{
		animLoop = ANM_NO_LOOP;
	}
#endif
#ifdef PUK2
	char flg;

	flg=0;
	// 新マップのみ
	if( SugiMapStat.MapVer ==1 ){
	#ifndef PUK2
		ext = (CHAREXTRA *)ptAct->pYobi;
		no = ext->charObjTblId;
	#endif
		if( pc.ptAct != ptAct ){
			if( charObj[no].charType&ACT_ATR_TYPE_OTHER ){
#ifdef PUK2_NOFLIP_NPC
				if ( !(charObj[no].charType&ACT_ATR_NOFLIP) ){
					if( !( charObj[no].charType&(ACT_ATR_TYPE_PC|ACT_ATR_TYPE_OTHER_PC|ACT_ATR_TYPE_PET) ) ) flg=1;
				}
#else
				if( !( charObj[no].charType&(ACT_ATR_TYPE_PC|ACT_ATR_TYPE_OTHER_PC|ACT_ATR_TYPE_PET) ) ) flg=1;
#endif
			}
		}
	}
	// 新マップのみキャラの向きを变える
	pattern( ptAct, ANM_NOMAL_SPD, animLoop, flg );
	if( pFlag )
	{
		if( mapSpdRate >= 1.2F )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop, flg );
		}
		if( mapSpdRate >= 1.6F )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop, flg );
		}
	}
	else
	{
		if( ptAct->bufCount >= 2 )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop, flg );
		}
		if( ptAct->bufCount >= 4 )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop, flg );
		}
	}
#else
	pattern( ptAct, ANM_NOMAL_SPD, animLoop );
	if( pFlag )
	{
		if( mapSpdRate >= 1.2F )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop );
		}
		if( mapSpdRate >= 1.6F )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop );
		}
	}
	else
	{
		if( ptAct->bufCount >= 2 )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop );
		}
		if( ptAct->bufCount >= 4 )
		{
			pattern( ptAct, ANM_NOMAL_SPD, animLoop );
		}
	}
#endif
#ifdef PUK2
	if ( SPR_rt00_ax<=ptAct->anim_chr_no && ptAct->anim_chr_no<=SPR_rk00_sp ) ptAct->bm.bltf|=BLTF_NOCHG;
	else ptAct->bm.bltf&=~BLTF_NOCHG;
#endif


#if 0
	if( pc.ptAct != ptAct )
	{
		if( charObj[no].newFoundFlag )
		{
			S2 xx, yy, ww, hh;

			// 画面内なら处理する
			realGetPos( ptAct->bmpNo, &xx, &yy );
			realGetWH( ptAct->bmpNo, &ww, &hh );
			xx += ptAct->x;
			yy += ptAct->y;
			if( 0 <= xx && xx+ww <= DEF_APPSIZEX
			 && 0 <= yy && yy+hh <= DEF_APPSIZEY )
			{
				CheckNewPet( ptAct->anim_chr_no );
				charObj[no].newFoundFlag = 0;
			}
		}
	}
#endif


#ifdef PUK3_RIDE
	// ライド中なら
	if ( ext->ptRide ){
		ext->ptRide->anim_ang = ptAct->anim_ang;

		// ペットのアニメーション
		pattern( ext->ptRide, ANM_NOMAL_SPD, animLoop, 0 );

		// キャラを描画
#ifdef PUK2_DRESS_UP
			charNum =
#endif
	#ifdef PUK2
		setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my,
			 ptAct->x+Blt_adjust(ptAct,0) + ext->ptRide->anim_cdx,
			 ptAct->y+Blt_adjust(ptAct,1) + ext->ptRide->anim_cdy, 1, &ptAct->bm );
	#else
		setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my,
			 ptAct->x + ext->ptRide->anim_cdx,
			 ptAct->y + ext->ptRide->anim_cdy, 1 );
	#endif

		// ペットを描画
#ifdef PUK2_DRESS_UP
		setMapCharWith( charNum, FALSE, ext->ptRide->bmpNo,
			 ptAct->x+Blt_adjust(ext->ptRide,0),
			 ptAct->y+Blt_adjust(ext->ptRide,1), &ext->ptRide->bm );
#else
	#ifdef PUK2
		setMapChar( ext->ptRide->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ext->ptRide,0), ptAct->y+Blt_adjust(ext->ptRide,1), 1, &ext->ptRide->bm );
	#else
		setMapChar( ext->ptRide->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 1 );
	#endif
#endif
	}else
#endif
	if( (ptAct->atr & ACT_ATR_TYPE_ITEM) )
	{
		// アイテム

		if( pc.ptAct != ptAct )
		{
			// 范围外に出たらかってに消す
			if( ptAct->gx < mapGx-16 || mapGx+16 < ptAct->gx
			 || ptAct->gy < mapGy-16 || mapGy+16 < ptAct->gy )
			{
				delCharObj( charObj[no].id );
#ifdef PUK3_SEGMENTATION_FAULT
				ProcPop();
#endif
				return;
			}
		}

		// 同じ位置にある时は表示しない
		if( !itemOverlapCheck( ptAct->bmpNo, ptAct->gx, ptAct->gy ) )
		{
			// オリジナルソート用にストック
#ifdef PUK2_DRESS_UP
			charNum =
#endif
#ifdef PUK2
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), 0, &ptAct->bm );
#else
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#endif
			ptAct->atr &= (~ACT_ATR_HIDE);
		}
		else
		{
			ptAct->atr |= ACT_ATR_HIDE;
		}
	}
	else
	{
		// アイテムじゃない
		// オリジナルソート用にストック
		if(  pc.ptAct == ptAct )
		{
#ifdef PUK2_DRESS_UP
			charNum =
#endif
#ifdef PUK2
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), 1, &ptAct->bm );
#else
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 1 );
#endif
		}
		else
		{
#ifdef PUK2_DRESS_UP
			charNum =
#endif
#ifdef PUK2
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), 0, &ptAct->bm );
#else
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#endif
		}
	}
#ifdef PUK2_DRESS_UP
	// すでに自分を表示しているので状态の关数呼び出し

	// 状态エフェクト表示
	drawCharStatus( ptAct, charNum );
#endif
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

// キャラクタのアクション作成
ACTION *createCharAction( int graNo, int gx, int gy, int dir )
{
	ACTION *ptAct;
	float mx, my;

	/* アクションリストに登録 */
#ifdef PUK2_MEMCHECK
	ptAct = GetAction( PRIO_CHR, sizeof( CHAREXTRA ), ACT_T_CHAREXTRA );
#else
	ptAct = GetAction( PRIO_CHR, sizeof( CHAREXTRA ) );
#endif
	if( ptAct == NULL )
		return NULL;

	// 实行关数
	ptAct->func = charProc;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	ptAct->anim_ang = dir;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_CHAR;
	// 1行インフォ表示フラグ
	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
	// 初期位置
	ptAct->nextGx = gx;					// マップグリッド座标（移动先）
	ptAct->nextGy = gy;
	ptAct->bufCount = 0;
	ptAct->gx = gx;						// マップグリッド座标（现在地）
	ptAct->gy = gy;
	ptAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	ptAct->my = (float)gy * GRID_SIZE;
	ptAct->vx = 0;						// 移动增分
	ptAct->vy = 0;
#ifdef PUK3_CHARA_ACT
	// キャラの行动
	ptAct->actNo = CHARACT_NOMAL;
#endif

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	return ptAct;
}

// キャラの移动先をバッファにためる
void stockCharMovePoint( ACTION *ptAct, int nextGx, int nextGy )
{
	if( ptAct == NULL )
		return;

	// バッファが空いていたらためる
	if( ptAct->bufCount < sizeof( ptAct->bufGx )/sizeof( int ) )
	{
		ptAct->bufGx[ptAct->bufCount] = nextGx;
		ptAct->bufGy[ptAct->bufCount] = nextGy;
		ptAct->bufCount++;
	}
	// バッファが一杯なのでワープさせる
	else
	{
		ptAct->bufCount = 0;
		setCharWarpPoint( ptAct, nextGx, nextGy );
	}
}


// キャラの移动先をバッファにため、补正の必要があれば补正する
//（たまにサーバから１グリッド飞ばした值が来るのを补正する）
void correctCharMovePoint( ACTION *ptAct, int nextGx, int nextGy )
{
	int dx, dy;
	int nGx[2], nGy[2], nCnt = 0;
	int i;
	int preCnt;
	int nextGx2, nextGy2;

	if( ptAct == NULL )
		return;

	// 补正が必要かチェック
	// バッファが空のときはキャラのnextと比较する
	if( ptAct->bufCount <= 0 )
	{
		nextGx2 = ptAct->nextGx;
		nextGy2 = ptAct->nextGy;
	}
	else
	{
		preCnt = ptAct->bufCount - 1;
		nextGx2 = ptAct->bufGx[preCnt];
		nextGy2 = ptAct->bufGy[preCnt];
	}

	dx = nextGx - nextGx2;
	dy = nextGy - nextGy2;

	if( ABS( dx ) == 2 && ABS( dy ) == 2 )
	{
		nGx[nCnt] = nextGx2+dx/2;
		nGy[nCnt] = nextGy2+dy/2;
		nCnt++;
	}
	else
	if( ABS( dx ) == 2 )
	{
		nGx[nCnt] = nextGx2+dx/2;
		nGy[nCnt] = nextGy;
		nCnt++;
	}
	else
	if( ABS( dy ) == 2 )
	{
		nGx[nCnt] = nextGx;
		nGy[nCnt] = nextGy2+dy/2;
		nCnt++;
	}

	nGx[nCnt] = nextGx;
	nGy[nCnt] = nextGy;
	nCnt++;


	// バッファが空いていたらためる
	if( ptAct->bufCount+nCnt <= sizeof( ptAct->bufGx )/sizeof( int ) )
	{
		for( i = 0; i < nCnt; i++ )
		{
			ptAct->bufGx[ptAct->bufCount] = nGx[i];
			ptAct->bufGy[ptAct->bufCount] = nGy[i];
			ptAct->bufCount++;
		}
	}
	// バッファが一杯なのでワープさせる
	else
	{
		ptAct->bufCount = 0;
		nCnt--;
		setCharWarpPoint( ptAct, nGx[nCnt], nGy[nCnt] );
	}
}


// キャラの移动先设定
#ifdef PUK3_RIDE
float setCharMovePoint( ACTION *ptAct, int nextGx, int nextGy, int walkSpeed, float l_rate )
#else
void setCharMovePoint( ACTION *ptAct, int nextGx, int nextGy )
#endif
{
	int dir;
	float dx, dy;
	float len;
	float rate = 1.0F;

	if( ptAct == NULL )
#ifdef PUK3_RIDE
		return 0;
#else
		return;
#endif

#ifdef PUK3_RIDE
	// レートの指定があるとき
	if ( l_rate ) rate = l_rate;
	// レートの指定が无いとき
	else{
		if( ptAct->bufCount > 5 )		rate = 2.0F;
		else if( ptAct->bufCount >= 4 )	rate = 1.6F;
		else if( ptAct->bufCount >= 2 )	rate = 1.2F;
		rate *= (float)walkSpeed / 100;
	}
#else
	if( ptAct->bufCount > 5 )
	{
		rate = 2.0F;
	}
	else
	if( ptAct->bufCount >= 4 )
	{
		rate = 1.6F;
	}
	else
	if( ptAct->bufCount >= 2 )
	{
		rate = 1.2F;
	}
#endif

	dx = nextGx*GRID_SIZE-ptAct->mx;
	dy = nextGy*GRID_SIZE-ptAct->my;
	len = (float)sqrt( (double)(dx*dx+dy*dy) );

	if( len > 0 )
	{
		dx /= len;
		dy /= len;
	}
	else
	{
		dx = 0;
		dy = 0;
	}

	ptAct->vx = dx * MOVE_SPEED * rate;
	ptAct->vy = dy * MOVE_SPEED * rate;

	ptAct->nextGx = nextGx;
	ptAct->nextGy = nextGy;

	if( dx != 0 || dy != 0 )
	{
		dir = getDirFromXY( dx, dy );

		ptAct->anim_ang = dir;
		ptAct->walkFlag = 1;
	}
#ifdef PUK3_RIDE
	return rate;
#endif
}


#ifdef PUK3_RIDE
void _setCharMovePoint( ACTION *ptAct, int nextGx, int nextGy, int walkSpeed )
#else
void _setCharMovePoint( ACTION *ptAct, int nextGx, int nextGy )
#endif
{
	int dir;
	float dx, dy;
	float len;

	if( ptAct == NULL )
		return;

	dx = nextGx*GRID_SIZE-ptAct->mx;
	dy = nextGy*GRID_SIZE-ptAct->my;
	len = (float)sqrt( (double)(dx*dx+dy*dy) );

	if( len > 0 )
	{
		dx /= len;
		dy /= len;
	}
	else
	{
		dx = 0;
		dy = 0;
	}

#ifdef PUK3_RIDE
	ptAct->vx = (dx * MOVE_SPEED * walkSpeed) / 100;
	ptAct->vy = (dy * MOVE_SPEED * walkSpeed) / 100;
#else
	ptAct->vx = dx * MOVE_SPEED;
	ptAct->vy = dy * MOVE_SPEED;
#endif

	ptAct->nextGx = nextGx;
	ptAct->nextGy = nextGy;

	if( dx != 0 || dy != 0 )
	{
		dir = getDirFromXY( dx, dy );

		ptAct->anim_ang = dir;
		ptAct->walkFlag = 1;
	}
}


// 次の座标バッファをシフトする
void shiftBufCount( ACTION *ptAct )
{
	int i;

	if( ptAct == NULL )
		return;

	if( ptAct->bufCount > 0 )
		ptAct->bufCount--;

	for( i = 0; i < ptAct->bufCount; i++ )
	{
		ptAct->bufGx[i] = ptAct->bufGx[i+1];
		ptAct->bufGy[i] = ptAct->bufGy[i+1];
	}
}


// キャラ移动处理
void charMove( ACTION *ptAct )
{
	float mx, my;

	if( ptAct == NULL )
		return;

	mx = (float)ptAct->nextGx*GRID_SIZE;
	my = (float)ptAct->nextGy*GRID_SIZE;

	// グリッドの中心に来たら次の移动先を设定する
	if( mx == ptAct->mx && my == ptAct->my )
	{
		// バッファに次の座标があるので设定
		if( ptAct->bufCount > 0 )
		{
#ifdef PUK3_RIDE
			int gx, gy;
			float rate;

			// 今いる位置の保存
			gx = ptAct->gx,	gy = ptAct->gy;

			// キャラの移动
			rate = setCharMovePoint( ptAct, ptAct->bufGx[0], ptAct->bufGy[0], ptAct->walkSpeed );
			shiftBufCount( ptAct );

			// ペットの移动
			if ( !(ptAct->atr&ACT_ATR_TYPE_MONSTERMAIL) ){
				CHAREXTRA *pYobi = ( CHAREXTRA *)ptAct->pYobi;

				if( pYobi->ptPet != NULL ){
					// ペットを自分がいる位置に动かす
					setCharMovePoint( pYobi->ptPet, gx, gy, pYobi->ptPet->walkSpeed, rate );
				}
			}
#else
	#ifdef PUK2
			if ( !(ptAct->atr&ACT_ATR_TYPE_MONSTERMAIL) ){
				CHAREXTRA *pYobi = ( CHAREXTRA *)ptAct->pYobi;

				if( pYobi->ptPet != NULL ){
					// ペットを自分がいる位置に动かす
					setCharMovePoint( pYobi->ptPet, ptAct->gx, ptAct->gy );
				}
			}
	#endif
			setCharMovePoint( ptAct, ptAct->bufGx[0], ptAct->bufGy[0] );
			shiftBufCount( ptAct );
#endif
		}
	}

	mx = (float)ptAct->nextGx*GRID_SIZE;
	my = (float)ptAct->nextGy*GRID_SIZE;

	// 移动中か？
	if( ptAct->vx != 0 || ptAct->vy != 0 )
	{
		// 目的地に着いたらパラメータ初期化
		if( pointLen2( ptAct->mx, ptAct->my, mx, my )
			<= ptAct->vx*ptAct->vx+ptAct->vy*ptAct->vy )
		{
			ptAct->mx = mx;
			ptAct->my = my;
			ptAct->vx = 0;
			ptAct->vy = 0;
		}
		// 移动
		else
		{
			ptAct->mx += ptAct->vx;
			ptAct->my += ptAct->vy;
		}
		// 移动アニメ
#ifdef PUK3_RIDE
		setRiderAnimeNo( ptAct, ANIM_WALK, FALSE );
#else
		ptAct->anim_no = ANIM_WALK;
#endif
	}
	else
	{
		// 歩き动作を立ち状态へ
#ifdef PUK3_RIDE
		if( ptAct->walkFlag != 0 )
			setRiderAnimeNo( ptAct, ANIM_STAND, FALSE );
#else
		if( ptAct->walkFlag != 0 )
			ptAct->anim_no = ANIM_STAND;
#endif
		ptAct->walkFlag = 0;
	}

	ptAct->gx = (int)(ptAct->mx/GRID_SIZE);
	ptAct->gy = (int)(ptAct->my/GRID_SIZE);
}


// キャラ移动处理（移动处理だけ）
void charMove2( ACTION *ptAct )
{
	float mx, my;

	if( ptAct == NULL )
		return;

	mx = (float)ptAct->nextGx*GRID_SIZE;
	my = (float)ptAct->nextGy*GRID_SIZE;

	// 移动中か？
	if( ptAct->vx != 0 || ptAct->vy != 0 )
	{
		// 目的地に着いたらパラメータ初期化
		if( pointLen2( ptAct->mx, ptAct->my, mx, my )
			<= ptAct->vx*ptAct->vx+ptAct->vy*ptAct->vy )
		{
			ptAct->mx = mx;
			ptAct->my = my;
			ptAct->vx = 0;
			ptAct->vy = 0;
		}
		// 移动
		else
		{
			ptAct->mx += ptAct->vx;
			ptAct->my += ptAct->vy;
		}
		// 移动アニメ
#ifdef PUK3_RIDE
		setRiderAnimeNo( ptAct, ANIM_WALK, FALSE );
#else
		ptAct->anim_no = ANIM_WALK;
#endif
	}
	else
	{
		// 歩き动作を立ち状态へ
#ifdef PUK3_RIDE
		if( ptAct->walkFlag != 0 )
			setRiderAnimeNo( ptAct, ANIM_STAND, FALSE );
#else
		if( ptAct->walkFlag != 0 )
			ptAct->anim_no = ANIM_STAND;
#endif
		ptAct->walkFlag = 0;
	}

	ptAct->gx = (int)(ptAct->mx/GRID_SIZE);
	ptAct->gy = (int)(ptAct->my/GRID_SIZE);
}


// キャラ移动处理（移动处理だけ）
void _charMove( ACTION *ptAct )
{
	float mx, my;
	float vx, vy;

	if( ptAct == NULL )
		return;

	mx = (float)ptAct->nextGx*GRID_SIZE;
	my = (float)ptAct->nextGy*GRID_SIZE;

	// 移动中か？
	if( ptAct->vx != 0 || ptAct->vy != 0 )
	{
		vx = ptAct->vx * mapSpdRate;
		vy = ptAct->vy * mapSpdRate;

		// 目的地に着いたらパラメータ初期化
		if( pointLen2( ptAct->mx, ptAct->my, mx, my )
			<= vx*vx+vy*vy )
		{
			ptAct->mx = mx;
			ptAct->my = my;
			ptAct->vx = 0;
			ptAct->vy = 0;
		}
		// 移动
		else
		{
			ptAct->mx += vx;
			ptAct->my += vy;
		}
		// 移动アニメ
#ifdef PUK3_RIDE
		setRiderAnimeNo( ptAct, ANIM_WALK, FALSE );
#else
		ptAct->anim_no = ANIM_WALK;
#endif
	}
	else
	{
		// 歩き动作を立ち状态へ
#ifdef PUK3_RIDE
		if( ptAct->walkFlag != 0 )
			setRiderAnimeNo( ptAct, ANIM_STAND, FALSE );
#else
		if( ptAct->walkFlag != 0 )
			ptAct->anim_no = ANIM_STAND;
#endif
		ptAct->walkFlag = 0;
	}

	ptAct->gx = (int)(ptAct->mx/GRID_SIZE);
	ptAct->gy = (int)(ptAct->my/GRID_SIZE);
}


// キャラのワープ先设定
void setCharWarpPoint( ACTION *ptAct, int gx, int gy )
{
	if( ptAct == NULL )
		return;

	ptAct->gx = gx;
	ptAct->gy = gy;

	ptAct->nextGx = gx;
	ptAct->nextGy = gy;

	ptAct->mx = (float)gx*GRID_SIZE;
	ptAct->my = (float)gy*GRID_SIZE;

	ptAct->vx = 0;
	ptAct->vy = 0;
}


// 状态ビットの设定
//
//   status : 状态ビットのポインタ
//   smsg   : 状态情报文字列
//
//  smsgを解析しstatusに设定する
//
void setCharStatus( unsigned short *status, char *smsg )
{
	// 毒
	if( strstr( smsg, "P" ) )                                            //MLHIDE
	{
		*status |= CHR_STATUS_P;
	}
	else
	{
		*status &= (~CHR_STATUS_P);
	}
	// 麻痹
	if( strstr( smsg, "N" ) )                                            //MLHIDE
	{
		*status |= CHR_STATUS_N;
	}
	else
	{
		*status &= (~CHR_STATUS_N);
	}
	// 沈黙
	if( strstr( smsg, "Q" ) )                                            //MLHIDE
	{
		*status |= CHR_STATUS_Q;
	}
	else
	{
		*status &= (~CHR_STATUS_Q);
	}
	// 石化
	if( strstr( smsg, "S" ) )                                            //MLHIDE
	{
		*status |= CHR_STATUS_S;
	}
	else
	{
		*status &= (~CHR_STATUS_S);
	}
	// 暗闇
	if( strstr( smsg, "D" ) )                                            //MLHIDE
	{
		*status |= CHR_STATUS_D;
	}
	else
	{
		*status &= (~CHR_STATUS_D);
	}
	// 混乱
	if( strstr( smsg, "C" ) )                                            //MLHIDE
	{
		*status |= CHR_STATUS_C;
	}
	else
	{
		*status &= (~CHR_STATUS_C);
	}
}


// キャラをリーダーにする
void setCharLeader( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_LEADER;
}


// キャラをリーダーからやめさせる。
void delCharLeader( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status &= (~CHR_STATUS_LEADER);
}


// キャラを布ティーの一员にする
void setCharParty( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_PARTY;
}


// キャラを布ティーからやめさせる。
void delCharParty( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

#ifdef PUK3_VEHICLE
	// 乘り物移动中プロセスならこの处理をしない
	// ＣＤプロトコルでキャラが削除されていないためここを通るが
	// 上记プロセスの前にアクションを初期化するためキャラが存在しておらず
	// ワープ終了后には新たにキャラのデータが送られてくるので
	// ここでは处理しない
	if ( nowVehicleProc() ) return;
#endif
	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;

	no = ext->charObjTblId;
	charObj[no].status &= (~CHR_STATUS_PARTY);
}


// キャラを観战状态にする
void setCharWatch( ACTION *ptAct, int battleNo )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_WATCH;
	
	charObj[no].battleNo = battleNo;
}


// キャラを観战状态から元にもどす
void delCharWatch( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status &= (~CHR_STATUS_WATCH);
}


// キャラをデュエル状态にする
void setCharDuel( ACTION *ptAct, int battleNo )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_DUEL;
	
	charObj[no].battleNo = battleNo;
}


// キャラをデュエル状态から元にもどす
void delCharDuel( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status &= (~CHR_STATUS_DUEL);
	
}


// キャラが战闘中
void setCharBattle( ACTION *ptAct, int battleNo )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_BATTLE;

	charObj[no].battleNo = battleNo;

}


// キャラが战闘終了
void delCharBattle( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status &= (~(CHR_STATUS_BATTLE | CHR_STATUS_HELP));
}


// キャラが咒术エフェクトを出す
void setCharUseMagic( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_USE_MAGIC;
}


// キャラが咒术エフェクトを出したらすぐにこれで
// フラグをおろす
void delCharUseMagic( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status &= (~CHR_STATUS_USE_MAGIC);
}


#ifdef PUK2
// キャラに吹き出しを出す
void setCharFukidashi( ACTION *ptAct, unsigned int offTime, unsigned char *msg, int color, int fontsize, int len )
{
	int i ,off;
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_FUKIDASHI;
	ext->drawFukidashiTime = offTime + GetTickCount();

	// ＮＰＣをＰＣの方へ向かせる
	changeNpcCharDir( ptAct );

	// 信息关系の登録
#ifdef PUK2_MEMCHECK
	memlistrel( ext->msg, MEMLISTTYPE_CHARA_FUKIDASHI );
#endif
	if( ext->msg != NULL ){
		free( ext->msg );
		ext->msg = NULL;
	}
	off = strlen( ptAct->name ) + 2;		// 名称の长さ
#ifdef PUK2
	if (msg[0]=='['){
		for(i=1;i<len;i++){
			if (msg[i]==']'){ i++;	break; }
		}
		if (i<len) off += i;
	}
#endif
	len -= off;
#ifdef PUK2
	ext->msg = (char *)malloc( len + 3 );
#else
	ext->msg = (char *)malloc( len + 2 );
#endif
	if ( ext->msg != NULL ){
#ifdef PUK2_MEMCHECK
		memlistset( ext->msg, MEMLISTTYPE_CHARA_FUKIDASHI );
#endif
		ext->msg_col = color;
		ext->msg_num = len;
		ext->msg_kind = fontsize;
		
		// 信息の实态を登録（＋名称の切り分けと改行设定）
		if( len > 40 ){
#ifdef PUK2
			// ４０バイト目が全角文字の２バイト目でないなら
			if ( CheckLetterType( (char *)&msg[off], 40 )!=CHECKLETTERTYPE_FULL_TAIL ){
				memcpy( ext->msg, msg+off, 40 );
				ext->msg[40] = '\0';
				memcpy( ext->msg+41, msg+off+40, len-40 );
				len++;
			}
			// ４０バイト目が全角文字の２バイト目なら
			else{
				memcpy( ext->msg, msg+off, 39 );
				ext->msg[39] = '\0';
				memcpy( ext->msg+41, msg+off+39, len-39 );
				len+=2;
			}
#else
			ext->msg[ 40 ] = 0x00;
			for( i = 40 ; i < len ; i++ ){
				ext->msg[ i + 1 ] = msg[ i + off ];
			}
			len++;
			for( i = 0 ; i < 40 ; i++ ){
				ext->msg[ i ] = msg[ i + off ];
			}
#endif
		}else{
			for( i = 0 ; i < len ; i++ ){
				ext->msg[ i ] = msg[ i + off ];
			}
		}
		ext->msg[ len ] = 0x00;
	}
}
#else
// キャラに吹き出しを出す
void setCharFukidashi( ACTION *ptAct, unsigned int offTime )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status |= CHR_STATUS_FUKIDASHI;
	ext->drawFukidashiTime = offTime + GetTickCount();
}
#endif

#ifdef PUK2_NEW_MENU
#define COMMONCHIP_W 74
#define COMMONCHIP_H 54
void MenuWindowCommonDraw( int GraNo, short x, short y, short w, short h, int DispPrio, unsigned long frame_rgba, unsigned long back_rgba,
	char sizeX = COMMONCHIP_W, char sizeY = COMMONCHIP_H );
#endif
// 状态の表示
#ifdef PUK2_DRESS_UP
void drawCharStatus( ACTION *ptAct, int charNum )
#else
void drawCharStatus( ACTION *ptAct )
#endif
{
	CHAREXTRA *ext;
	int no;
	unsigned short status;
#ifdef PUK2
	unsigned short status2;
	char lpfpDisp = 0;
	int headNo;
#endif
	int battleNo;
	int sideNo;
	int helpMode;
	char str[256];
#ifdef PUK3_PUT_ON
	int cdx, cdy;
#endif

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
#ifdef PUK3_PUT_ON
	// 头の上の座标
	cdx = ptAct->anim_cdx,	cdy = ptAct->anim_cdy;
	#ifdef PUK3_RIDE
		// ライド中ならその分も加算
		if (ext->ptRide){
			cdx += ext->ptRide->anim_cdx;
			cdy += ext->ptRide->anim_cdy;
		}
	#endif
#endif

	if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 )
	{
		// ＰＣキャラの时
		status = pc.status;
#ifdef PUK2
		status2 = pc.status2;
		headNo = pc.headNo;
#endif
		battleNo = 0;
		sideNo = 0;
		helpMode = 0;
#ifdef PUK2
		if (ptAct->atr&ACT_ATR_TYPE_TAKENPET){
			status &= CHR_STATUS_BATTLE | CHR_STATUS_DUEL;
			status2=0;
			if (pc.status2&CHR_STATUS2_PETHEALTH) status2|=CHR_STATUS2_HEALTH;
			if (pc.status2&CHR_STATUS2_PETLEVELUP) status2|=CHR_STATUS2_LEVELUP;
			if (pc.status2&CHR_STATUS2_PETLEVELUPSE) status2|=CHR_STATUS2_LEVELUPSE;

			for(int i=0;i<MAX_PET;i++){
				if ( !pet[i].useFlag ) continue;
				if ( !pet[i].Walk ) continue;

				ptAct->hp = pet[i].lp;
				ptAct->maxHp = pet[i].maxLp;
				ptAct->fp = pet[i].fp;
				ptAct->maxFp = pet[i].maxFp;
				lpfpDisp = 1;
				break;
			}
		}
#endif
#ifdef PUK2_NEW_MENU
		else{
			ptAct->hp    = pc.lp;
			ptAct->maxHp = pc.maxLp;
			ptAct->fp    = pc.fp;
			ptAct->maxFp = pc.maxFp;
			lpfpDisp = 1;
		}
#endif
	}
	else
	{
		status = charObj[no].status;
#ifdef PUK2
		status2 = charObj[no].status2;
		headNo = charObj[no].headNo;
#endif
		battleNo = charObj[no].battleNo;
		sideNo = charObj[no].sideNo;
		helpMode = charObj[no].helpMode;
#ifdef PUK2
		if (ptAct->atr&ACT_ATR_TYPE_TAKENPET){
			status &= CHR_STATUS_BATTLE | CHR_STATUS_DUEL;
			status2=0;
			if (charObj[no].status2&CHR_STATUS2_PETHEALTH) status2|=CHR_STATUS2_HEALTH;
			if (charObj[no].status2&CHR_STATUS2_PETLEVELUP) status2|=CHR_STATUS2_LEVELUP;
			if (charObj[no].status2&CHR_STATUS2_PETLEVELUPSE) status2|=CHR_STATUS2_LEVELUPSE;
		}
#endif
#ifdef PUK2_NEW_MENU
		if ( charObj[no].status & CHR_STATUS_PARTY ) lpfpDisp = 1;
#endif
	}

#ifdef PUK2_NEW_MENU
	if (!CharHPLPStatusFlag) lpfpDisp = 0;
#endif

#ifdef PUK2
#else
	// バトルマーク
	if( (status & CHR_STATUS_BATTLE) != 0 )
	{
		U4 bmpNo;

		realGetNo( CG_ICON_ENCOUNT + battleNo % 19 /*+ sideNo * 8/*battleIconTbl[(battleNo%10)][(sideNo%1)]*/, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
	}

	// デュエルマーク
	if( (status & CHR_STATUS_DUEL) != 0 )
	{
		U4 bmpNo;

		realGetNo( CG_ICON_DUEL + battleNo % 19 /*+ sideNo * 8*/, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
	}

	// 観战マーク（透明キャラは表示しない）// ohta
	if( (status & CHR_STATUS_WATCH) != 0 && ptAct->anim_chr_no != 0 )
	{
		U4 bmpNo;

		realGetNo( CG_ICON_WATCHING + battleNo % 19 /*+ sideNo * 8*/, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
	}

#if 0 // 处理がダブっている气がするので止めておく
	// デュエルマーク
	if( (status & CHR_STATUS_DUEL) != 0 )
	{
		U4 bmpNo;

		realGetNo( CG_ICON_DUEL, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
	}
#endif

#if 0	// 画像ないから止めておく
	// ヘルプマーク
	if( (status & CHR_STATUS_HELP) != 0 )
	{
		U4 bmpNo;

		realGetNo( CG_SPEECH_HELP, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
	}
#endif

#endif

	// リーダーマーク
	//（バトルマーク出时は消す）
	//（観战マーク出时は消す）
	if( (status & CHR_STATUS_LEADER) != 0
	 && ext->ptActLeaderMark == NULL
	 && ((status & CHR_STATUS_BATTLE) == 0 || (status & CHR_STATUS_WATCH) == 0
	  || (status & CHR_STATUS_DUEL)) )
	{
		// リーダーマーク表示用アクション作成
		ext->ptActLeaderMark =
			createCommmonEffectAction( SPR_leader, ptAct->gx, ptAct->gy, 0, 0, DISP_PRIO_CHAR );
	}
	else
	if( ((status & CHR_STATUS_LEADER) == 0 || (status & CHR_STATUS_BATTLE) != 0)
	 && ext->ptActLeaderMark != NULL )
	{
		// リーダーマーク表示用アクション抹消
		DeathAction( ext->ptActLeaderMark );
		ext->ptActLeaderMark = NULL;
	}
	else
	if( (status & CHR_STATUS_LEADER) != 0
	 && ext->ptActLeaderMark != NULL )
	{
		// リーダーマークのアニメーション
		ext->ptActLeaderMark->x  = ptAct->x;
		ext->ptActLeaderMark->y  = ptAct->y;
		ext->ptActLeaderMark->mx = ptAct->mx;
		ext->ptActLeaderMark->my = ptAct->my;

		// アニメーション处理
		pattern( ext->ptActLeaderMark, ANM_NOMAL_SPD, ANM_LOOP );
		// オリジナルソート用にストック
#if defined(PUK3_RIDE) && defined(PUK2_DRESS_UP)
		setMapCharWith( charNum, TRUE, ext->ptActLeaderMark->bmpNo,
			 ptAct->x+Blt_adjust(ext->ptActLeaderMark,0),
			 ptAct->y+Blt_adjust(ext->ptActLeaderMark,1)+getCharIconOffsetY( ptAct->anim_chr_no ),
			 NULL );
#else
#ifdef PUK2
		setMapChar( ext->ptActLeaderMark->bmpNo, ptAct->mx, ptAct->my,
			ptAct->x+Blt_adjust(ext->ptActLeaderMark,0), ptAct->y+Blt_adjust(ext->ptActLeaderMark,1)+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
#else
		setMapChar( ext->ptActLeaderMark->bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
#endif
#endif
	}

#ifndef PUK2

#if 0	// 画像ないから止めておく
	// 咒术エフェクト
	if( (status & CHR_STATUS_USE_MAGIC) != 0
	 && ext->ptActMagicEffect == NULL )
	{
		// 咒术エフェクト表示用アクション作成
		ext->ptActMagicEffect =
			createCommmonEffectAction( SPR_effect01, ptAct->gx, ptAct->gy+1, 0, 0, DISP_PRIO_CHAR );
		if( pc.ptAct != NULL && pc.ptAct == ptAct )
		{
			delPcUseMagic();
		}
		else
		{
			delCharUseMagic( ptAct );
		}
	}
	else
	if( ext->ptActMagicEffect != NULL )
	{
		// 咒术エフェクトのアニメーション
		ext->ptActMagicEffect->x  = ptAct->x;
		ext->ptActMagicEffect->y  = ptAct->y;
		ext->ptActMagicEffect->mx = ptAct->mx;
		ext->ptActMagicEffect->my = ptAct->my;

		// アニメーション处理
		if( pattern( ext->ptActMagicEffect, ANM_NOMAL_SPD, ANM_NO_LOOP ) == 0 )
		{
			// オリジナルソート用にストック
#ifdef PUK2
			setMapChar( ext->ptActLeaderMark->bmpNo, ptAct->mx, ptAct->my,
				ptAct->x+Blt_adjust(ext->ptActLeaderMark,0), ptAct->y+Blt_adjust(ext->ptActLeaderMark,1), 0 );
#else
			setMapChar( ext->ptActLeaderMark->bmpNo, ptAct->mx, ptAct->my,
				ptAct->x, ptAct->y, 0 );
#endif
		}
		else
		{
			// アニメーションループ終わり
			DeathAction( ext->ptActMagicEffect );
			ext->ptActMagicEffect = NULL;
		}
	}
#endif

#endif

	// 吹き出しマーク
	if( (status & CHR_STATUS_FUKIDASHI) != 0 )
	{
		if( ext->drawFukidashiTime > GetTickCount() )
		{
//			U4 bmpNo;

//			realGetNo( CG_ICON_FUKIDASI, &bmpNo );

#ifdef PUK2_NEW_MENU
	#ifdef PUK3_ITEM_LOCK
			if( CharFukidashiFlag && (ptAct->atr&(ACT_ATR_TYPE_PC|ACT_ATR_TYPE_OTHER_PC)) &&
				ext->msg ){
	#else
			if( CharFukidashiFlag && (ptAct->atr&(ACT_ATR_TYPE_PC|ACT_ATR_TYPE_OTHER_PC)) ){
	#endif
#else
			if( (SPRPC_START <= ptAct->anim_chr_no && ptAct->anim_chr_no <= SPRPC_END ) 
#ifdef _CG2_NEWGRAPHIC
			||(SPRPC_START_V2 <= ptAct->anim_chr_no && ptAct->anim_chr_no <= SPRPC_END_V2 )
#endif
#ifdef PUK2
			||( SPR_rt00_ax <= ptAct->anim_chr_no && ptAct->anim_chr_no <= SPR_rk00_sp )
#endif
			){
#endif
				// プレイヤーキャラならこっち
#ifdef PUK2
				if( no = ext->msg_num ){
					int fw = GetStrWidth( ext->msg, ext->msg_kind );
					int l,r,u,d;

					u = ptAct->y + getCharIconOffsetY( ptAct->anim_chr_no ) - 10 - 4 - 18;
					d = u + FontKind[ ext->msg_kind ].zenkakuHeight + 8;
					// 二行になるかどうかチェック
					if( no > 40 ){
						int fw2 = GetStrWidth( &ext->msg[ 41 ], ext->msg_kind );

						if( fw < fw2 ){
							fw = fw2;
						}

						// フォントバッファにためる
						StockFontBuffer( ptAct->x - fw / 2, u + 4/* - 20*/,
							FONT_PRIO_BACK, ext->msg_kind, ext->msg_col, &ext->msg[ 41 ], 0, 0 );

						u -= FontKind[ ext->msg_kind ].zenkakuHeight;
					}

					// フォントバッファにためる
					StockFontBuffer( ptAct->x - fw / 2, u + 4,
						FONT_PRIO_BACK, ext->msg_kind, ext->msg_col, ext->msg, 0, 0 );

					// 半透明の板の表示
					l = ptAct->x - fw / 2 - 4;
					r = l + fw + 8;
#ifdef PUK2_NEW_MENU
					MenuWindowCommonDraw( GID_SpeakWindow, l-1, u-1, (r+1)-(l-1), (d+1)-(u-1), DISP_PRIO_BOX, 0xffffffff, 0x80ffffff );
#else
	#ifdef PUK2
					StockAlphaDispBuffer( l, u, fw + 8, d - u + 1, DISP_PRIO_BOX, 0x80010101 );
	#endif
					StockBoxDispBuffer( l-1, u-1, r+1, d+1, DISP_PRIO_BOX, SYSTEM_PAL_AQUA, 0 );
#endif
				}
#else

#if 0
				// オリジナルソート用にストック
				setMapChar( bmpNo, ptAct->mx, ptAct->my,
					ptAct->x-20, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0 );
#else
				StockDispBuffer( ptAct->x-20, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ),
					DISP_PRIO_BOX, CG_ICON_FUKIDASI, 0 );
#endif

#endif // PUK2
			}
			else
			{
#if 0
				// オリジナルソート用にストック
				setMapChar( bmpNo, ptAct->mx, ptAct->my, ptAct->x-20, ptAct->y-84, 0 );
#else
				StockDispBuffer( ptAct->x-20, ptAct->y-84, DISP_PRIO_BOX, CG_ICON_FUKIDASI, 0 );
#endif
			}
		}
		else
		{
			status &= (~CHR_STATUS_FUKIDASHI);
#ifdef PUK2
	#ifdef PUK2_MEMCHECK
			if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
				memlistrel( ext->msg, MEMLISTTYPE_PC_FUKIDASHI );
			}else{
				memlistrel( ext->msg, MEMLISTTYPE_CHARA_FUKIDASHI );
			}
	#endif
			if( ext->msg != NULL ){
				free( ext->msg );
				ext->msg = NULL;
			}
#endif
		}
	}

	// キャラの头の上に名称を出す。
#ifdef PUK2_NEW_MENU
	if( nameOverTheHeadFlag > 0 )
#else
	if( nameOverTheHeadFlag > 0 && ptAct != pc.ptAct )
#endif
	{
#ifdef PUK2_NEW_MENU
		if( ptAct == pc.ptAct ){
			if( nameOverTheHeadFlag == 1 ){
				// 名称を表示
				sprintf( str, "%s", pc.name );                                    //MLHIDE
				// フォントバッファにためる
				StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
					FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_AQUA, str, 0, 0 );
			}else if( nameOverTheHeadFlag == 2 ){
				// ユーザー称号表示
				sprintf( str, "%s", pc.freeName );                                //MLHIDE
				// フォントバッファにためる
				StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
					FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_YELLOW, str, 0, 0 );
			}else if( nameOverTheHeadFlag == 3 ){
				// スペシャル称号表示
				sprintf( str, "%s", charTitleInfo[pc.titleId].title );            //MLHIDE
				// フォントバッファにためる
				StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
					FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_GREEN, str, 0, 0 );
			}else if( nameOverTheHeadFlag == 4 ){
				// 家族名表示
				sprintf( str, "%s", pc.guildName );                               //MLHIDE
				// フォントバッファにためる
				StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
					FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_PURPLE, str, 0, 0 );
			}else if( nameOverTheHeadFlag == 5 ){
				// 家族称号表示
				sprintf( str, "%s", pc.guildTitle );                              //MLHIDE
				// フォントバッファにためる
				StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
					FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );
			}
		}else
#endif
		// 自分以外のＰＣ
#ifdef PUK2
		if( (ptAct->atr&ACT_ATR_TYPE_OTHER_PC) && !(ptAct->atr&ACT_ATR_TYPE_TAKENPET) )
#else
		if( ptAct->atr & ACT_ATR_TYPE_OTHER_PC )
#endif
		{
			// グラフィック番号が０じゃない时（Ｆ１１对策、０番は完全透明人间）
			if( ptAct->anim_chr_no != 0 ){
				if( nameOverTheHeadFlag == 1 )
				{
					// 名称を表示
					sprintf( str, "%s", ptAct->name );                               //MLHIDE
					// フォントバッファにためる
					StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
						//FONT_PRIO_BACK, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
						FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );
					//	FONT_PRIO_BACK, FONT_KIND_MIDDLE, ptAct->itemNameColor, str, 0, 0 );
				}
				else
				if( nameOverTheHeadFlag == 2 )
				{
					// ユーザー称号表示
					sprintf( str, "%s", ptAct->freeName );                           //MLHIDE
					// フォントバッファにためる
					StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
						FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_AQUA, str, 0, 0 );
				}
				else
				if( nameOverTheHeadFlag == 3 )
				{
					// スペシャル称号表示
					sprintf( str, "%s", ptAct->title );                              //MLHIDE
					// フォントバッファにためる
					StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
						FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_YELLOW, str, 0, 0 );
				}
#ifdef PUK2
				else
				if( nameOverTheHeadFlag == 4 )
				{
					// 家族名表示
					sprintf( str, "%s", ptAct->guildName );                          //MLHIDE
					// フォントバッファにためる
					StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
						FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_GREEN, str, 0, 0 );
				}
				else
				if( nameOverTheHeadFlag == 5 )
				{
					// 家族称号表示
					sprintf( str, "%s", ptAct->guildTitle );                         //MLHIDE
					// フォントバッファにためる
					StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
						FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_PURPLE, str, 0, 0 );
				}
#endif
			}
		}
		else
		// ペット
#ifdef PUK2
		if( ptAct->atr & (ACT_ATR_TYPE_PET|ACT_ATR_TYPE_TAKENPET) )
#else
		if( ptAct->atr & ACT_ATR_TYPE_PET )
#endif
		{
			if( (ptAct->atr & ACT_ATR_TYPE_PET) && strlen( ptAct->freeName ) > 0 )
			{
				// ペットの时は付けられた名称があればそちらを使う
				sprintf( str, "%s", ptAct->freeName );                            //MLHIDE
			}
			else
			{
				sprintf( str, "%s", ptAct->name );                                //MLHIDE
			}
			StockFontBuffer( ptAct->x-GetStrWidth( str, FONT_KIND_SMALL )/2, ptAct->y-98,
				//FONT_PRIO_BACK, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
				FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );
		}
#ifdef NPC_SHOW_NAME
		if ( (ptAct->atr & ACT_ATR_TYPE_OTHER) && ( ptAct->anim_chr_no > 0) ) {
			sprintf(str, "%s", ptAct->name);                                   //MLHIDE
			StockFontBuffer(ptAct->x - GetStrWidth(str, FONT_KIND_SMALL) / 2, ptAct->y - 98,
				FONT_PRIO_BACK, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0);
		}
#endif
	}

#if 0
	if( nameOverTheHeadFlag == 1
	 && ptAct != pc.ptAct
	 && (ptAct->atr & (ACT_ATR_TYPE_PC | ACT_ATR_TYPE_OTHER_PC | ACT_ATR_TYPE_PET))
	 && ptAct->anim_chr_no != 999 )
	{
		if( (ptAct->atr & ACT_ATR_TYPE_PET) && strlen( ptAct->freeName ) > 0 )
		{
			// ペットの时は付けられた名称があればそちらを使う
			sprintf( str, "%s", ptAct->freeName );                             //MLHIDE
		}
		else
		{
			sprintf( str, "%s", ptAct->name );                                 //MLHIDE
		}
		StockFontBuffer( ptAct->x-GetStrWidth( str )/2, ptAct->y-98,
			FONT_PRIO_BACK, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
	}
#endif
#ifdef PUK2
	#ifdef PUK3_CHANGE_ICONACTIONPOINTER
	//--------------------------------------
	// 战闘??デュエル??観战??スキル系

	// 出すとき
	if ( ( status&(CHR_STATUS_BATTLE|CHR_STATUS_WATCH|CHR_STATUS_DUEL) ) ||
		 (status2&CHR_STATUS2_SKILL) ){
		int mark;

		if ( status2 & CHR_STATUS2_SKILL ) mark = headIconTbl[headNo];
		if ( status & CHR_STATUS_BATTLE ) mark = SPR_icn_2;
		if ( status & CHR_STATUS_DUEL ) mark = SPR_icn_3;
		if ( status & CHR_STATUS_WATCH ) mark = SPR_icn_4;

		// 出てないなら
		if ( !ext->ptActIcon ){
			// マーク表示用アクション作成
			ext->ptActIcon =
				createCommmonEffectAction( mark, ptAct->gx, ptAct->gy, 0, 0, DISP_PRIO_CHAR );
	
			ext->ptActIcon->bltfon = BLTF_NOCHG;
			ext->ptActIcon->bltf = BLTF_NOCHG;
		}
		// 出てるなら
		if ( ext->ptActIcon ){
			// マークのアニメーション
			ext->ptActIcon->anim_chr_no = mark;
			ext->ptActIcon->x  = ptAct->x + Blt_adjust(ext->ptActIcon,0);
			ext->ptActIcon->y  = ptAct->y + Blt_adjust(ext->ptActIcon,1) + getCharIconOffsetY( ptAct->anim_chr_no ) - 20;
			ext->ptActIcon->mx = ptAct->mx + Blt_adjust(ext->ptActIcon,0);
			ext->ptActIcon->my = ptAct->my + Blt_adjust(ext->ptActIcon,1) + 0.1f;
			if ( (status2&CHR_STATUS2_HEALTH) &&
				 !( status & (CHR_STATUS_BATTLE|CHR_STATUS_WATCH|CHR_STATUS_DUEL) ) ){
				ext->ptActIcon->x  -= 17;
			}
	
			// アニメーション处理
			pattern( ext->ptActIcon, ANM_NOMAL_SPD, ANM_LOOP );
			ext->ptActIcon->bm.bltf |= BLTF_NOCHG;

			// オリジナルソート用にストック
#ifdef PUK2_DRESS_UP
			setMapCharWith( charNum, TRUE, ext->ptActIcon->bmpNo,
				 ext->ptActIcon->x, ext->ptActIcon->y, &ext->ptActIcon->bm );
#else
			setMapChar( ext->ptActIcon->bmpNo, ext->ptActIcon->mx, ext->ptActIcon->my,
				ext->ptActIcon->x, ext->ptActIcon->y, 0, &ext->ptActIcon->bm );
#endif
		}
	}
	// 出ないとき
	else{
		// マーク出てるなら
		if( ext->ptActIcon ){
			// マーク表示用アクション抹消
			DeathAction( ext->ptActIcon );
			ext->ptActIcon = NULL;
		}
	}
	#else
	//--------------------------------------
	// 战闘??デュエル??観战??スキル系

	// 出すとき
	if ( ( status&(CHR_STATUS_BATTLE|CHR_STATUS_WATCH|CHR_STATUS_DUEL) ) ||
		 (status2&CHR_STATUS2_SKILL) ){
		int mark;

		if ( status2 & CHR_STATUS2_SKILL ) mark = headIconTbl[headNo];
		if ( status & CHR_STATUS_BATTLE ) mark = SPR_icn_2;
		if ( status & CHR_STATUS_DUEL ) mark = SPR_icn_3;
		if ( status & CHR_STATUS_WATCH ) mark = SPR_icn_4;

		// 出てないなら
		if ( !ext->ptActMagicEffect ){
			// マーク表示用アクション作成
			ext->ptActMagicEffect =
				createCommmonEffectAction( mark, ptAct->gx, ptAct->gy, 0, 0, DISP_PRIO_CHAR );
	
			ext->ptActMagicEffect->bltfon = BLTF_NOCHG;
			ext->ptActMagicEffect->bltf = BLTF_NOCHG;
		}
		// 出てるなら
		if ( ext->ptActMagicEffect ){
			// マークのアニメーション
			ext->ptActMagicEffect->anim_chr_no = mark;
			ext->ptActMagicEffect->x  = ptAct->x + Blt_adjust(ext->ptActMagicEffect,0);
			ext->ptActMagicEffect->y  = ptAct->y + Blt_adjust(ext->ptActMagicEffect,1) + getCharIconOffsetY( ptAct->anim_chr_no ) - 20;
			ext->ptActMagicEffect->mx = ptAct->mx + Blt_adjust(ext->ptActMagicEffect,0);
			ext->ptActMagicEffect->my = ptAct->my + Blt_adjust(ext->ptActMagicEffect,1) + 0.1f;
			if ( (status2&CHR_STATUS2_HEALTH) &&
				 !( status & (CHR_STATUS_BATTLE|CHR_STATUS_WATCH|CHR_STATUS_DUEL) ) ){
				ext->ptActMagicEffect->x  -= 17;
			}
	
			// アニメーション处理
			pattern( ext->ptActMagicEffect, ANM_NOMAL_SPD, ANM_LOOP );
			ext->ptActMagicEffect->bm.bltf |= BLTF_NOCHG;

			// オリジナルソート用にストック
#ifdef PUK2_DRESS_UP
			setMapCharWith( charNum, TRUE, ext->ptActMagicEffect->bmpNo,
				 ext->ptActMagicEffect->x, ext->ptActMagicEffect->y, &ext->ptActMagicEffect->bm );
#else
			setMapChar( ext->ptActMagicEffect->bmpNo, ext->ptActMagicEffect->mx, ext->ptActMagicEffect->my,
				ext->ptActMagicEffect->x, ext->ptActMagicEffect->y, 0, &ext->ptActMagicEffect->bm );
#endif
		}
	}
	// 出ないとき
	else{
		// マーク出てるなら
		if( ext->ptActMagicEffect ){
			// マーク表示用アクション抹消
			DeathAction( ext->ptActMagicEffect );
			ext->ptActMagicEffect = NULL;
		}
	}
	#endif

	//--------------------------------------
	// 等级アップ(战闘??デュエル??観战??スキル系出时は消す)

	// 等级アップ出すとき
	if ( status2 & CHR_STATUS2_LEVELUP ){
	#ifdef PUK3_CHANGE_ICONACTIONPOINTER
		if ( !ext->ptActIcon ){
	#else
		if ( !ext->ptActMagicEffect ){
	#endif
			// 等级アップ出てないなら
			if ( !ext->ptActLevelup ){
				// 等级アップマーク表示用アクション作成
				ext->ptActLevelup =
					createCommmonEffectAction( SPR_icn_14, ptAct->gx, ptAct->gy, 0, 0, DISP_PRIO_CHAR );
		
				// 等级アップジングル
				if ( status2 & CHR_STATUS2_LEVELUPSE ){
					// ペットなら
					if ( ptAct->atr & ACT_ATR_TYPE_TAKENPET ){
						if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
							pc.status2 &= (~CHR_STATUS2_PETLEVELUPSE);
						}else{
							charObj[no].status2 &= (~CHR_STATUS2_PETLEVELUPSE);
						}
					}
					// プレーヤーキャラなら
					else{
						if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
							pc.status2 &= (~CHR_STATUS2_LEVELUPSE);
						}else{
							charObj[no].status2 &= (~CHR_STATUS2_LEVELUPSE);
						}
					}
					play_se( 437, ptAct->x, ptAct->y );
				}
		
				ext->ptActLevelup->damage=0;
		
				ext->ptActLevelup->bltfon = BLTF_NOCHG;
				ext->ptActLevelup->bltf = BLTF_NOCHG;
			}
			// 出てるなら
			if ( ext->ptActLevelup ){
				ext->ptActLevelup->x  = ptAct->x + Blt_adjust(ext->ptActLevelup,0);
				ext->ptActLevelup->y  = ptAct->y + Blt_adjust(ext->ptActLevelup,1) + getCharIconOffsetY( ptAct->anim_chr_no ) - 20;;
				ext->ptActLevelup->mx = ptAct->mx + Blt_adjust(ext->ptActLevelup,0);
				ext->ptActLevelup->my = ptAct->my + Blt_adjust(ext->ptActLevelup,1) + 0.1f;

				// アニメーション处理
				pattern( ext->ptActLevelup, ANM_NOMAL_SPD, ANM_LOOP );
				ext->ptActLevelup->bm.bltf |= BLTF_NOCHG;
				// オリジナルソート用にストック
#ifdef PUK2_DRESS_UP
				setMapCharWith( charNum, TRUE, ext->ptActLevelup->bmpNo,
					 ext->ptActLevelup->x, ext->ptActLevelup->y, &ext->ptActLevelup->bm );
#else
				setMapChar( ext->ptActLevelup->bmpNo, ext->ptActLevelup->mx, ext->ptActLevelup->my,
					ext->ptActLevelup->x, ext->ptActLevelup->y, 0, &ext->ptActLevelup->bm );
#endif
			}
		}
		ext->ptActLevelup->damage++;
		// フラグ消す处理
		if ( ext->ptActLevelup->damage > 600 ){
			// ペットなら
			if (ptAct->atr&ACT_ATR_TYPE_TAKENPET){
				if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
					pc.status2 &= (~CHR_STATUS2_PETLEVELUP);
				}else{
					charObj[no].status2 &= (~CHR_STATUS2_PETLEVELUP);
				}
			}
			// プレーヤーキャラなら
			else{
				if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
					pc.status2 &= (~CHR_STATUS2_LEVELUP);
				}else{
					charObj[no].status2 &= (~CHR_STATUS2_LEVELUP);
				}
			}
		}
	}
	// 等级アップ出ないとき
	else{
		// マーク出てるなら
		if( ext->ptActLevelup ){
			// 等级アップマーク表示用アクション抹消
			DeathAction( ext->ptActLevelup );
			ext->ptActLevelup = NULL;
		}
	}

	//--------------------------------------
	// 怪我(战闘??デュエル??観战出时は消す)

	// 怪我出すとき
	if ( (status2&CHR_STATUS2_HEALTH) && 
		 !( status & (CHR_STATUS_BATTLE|CHR_STATUS_WATCH|CHR_STATUS_DUEL) ) ){
		// 怪我出てないなら
		if ( !ext->ptActInjury ){
			// 怪我マーク表示用アクション作成
			ext->ptActInjury =
				createCommmonEffectAction( SPR_icn_15, ptAct->gx, ptAct->gy, 0, 0, DISP_PRIO_CHAR );
	
			ext->ptActInjury->bltfon = BLTF_NOCHG;
			ext->ptActInjury->bltf = BLTF_NOCHG;
		}
		// 出てるなら
		if ( ext->ptActInjury ){
			// 怪我マークのアニメーション
			ext->ptActInjury->x  = ptAct->x + Blt_adjust(ext->ptActInjury,0);
			ext->ptActInjury->y  = ptAct->y + Blt_adjust(ext->ptActInjury,1) + getCharIconOffsetY( ptAct->anim_chr_no ) - 20;
			ext->ptActInjury->mx = ptAct->mx + Blt_adjust(ext->ptActInjury,0);
			ext->ptActInjury->my = ptAct->my + Blt_adjust(ext->ptActInjury,1) + 0.1f;
	#ifdef PUK3_CHANGE_ICONACTIONPOINTER
			if ( ext->ptActIcon ){
	#else
			if ( ext->ptActMagicEffect ){
	#endif
				ext->ptActInjury->x  += 17;
			}
	
			// アニメーション处理
			pattern( ext->ptActInjury, ANM_NOMAL_SPD, ANM_LOOP );
			ext->ptActInjury->bm.bltf |= BLTF_NOCHG;
			// オリジナルソート用にストック
#ifdef PUK2_DRESS_UP
			setMapCharWith( charNum, TRUE, ext->ptActInjury->bmpNo,
				 ext->ptActInjury->x, ext->ptActInjury->y, &ext->ptActInjury->bm );
#else
			setMapChar( ext->ptActInjury->bmpNo, ext->ptActInjury->mx, ext->ptActInjury->my,
				ext->ptActInjury->x, ext->ptActInjury->y, 0, &ext->ptActInjury->bm );
#endif
		}
	}
	// 怪我出ないとき
	else{
		// マーク出てるなら
		if( ext->ptActInjury ){
			// 怪我マーク表示用アクション抹消
			DeathAction( ext->ptActInjury );
			ext->ptActInjury = NULL;
		}
	}

#ifdef PUK3_PUT_ON
	// 物を被っているなら
	if ( ext->ptPuton ){
		char flg = 1;
		// アニメーション处理
		switch(ptAct->anim_no){
		case ANIM_DAMAGE:
			ext->ptPuton->anim_no = ANIM_B_WALK;
			break;
		default:
			// 上记いずれにも当てはまらないなら立ちにする
			ext->ptPuton->anim_no = ANIM_STAND;
		}
//			flg == 0 なら表示しない
//			flg = 0;
		ext->ptPuton->anim_ang = ptAct->anim_ang;
		// 被り物のアニメーション
		if ( pattern( ext->ptPuton, ANM_NOMAL_SPD, ANM_LOOP, 0 ) ) flg = 0;

		if (flg){
			// 被り物を描画
	#ifdef PUK2_DRESS_UP
		setMapCharWith( charNum, TRUE, ext->ptPuton->bmpNo,
				 ptAct->x+Blt_adjust(ext->ptPuton,0) + cdx,
				 ptAct->y+Blt_adjust(ext->ptPuton,1) + cdy, &ext->ptPuton->bm );
	#else
		#ifdef PUK2
			setMapChar( ext->ptPuton->bmpNo, ptAct->mx, ptAct->my+0.002f,
				 ptAct->x+Blt_adjust(ext->ptPuton,0) + cdx,
				 ptAct->y+Blt_adjust(ext->ptPuton,1) + cdy, 1, &ext->ptPuton->bm );
		#else
			setMapChar( ext->ptPuton->bmpNo, ptAct->mx, ptAct->my+0.002f,
				 ptAct->x + cdx, ptAct->y + cdx, 1 );
		#endif
	#endif
		}
	}
#endif

	// 体力??魔力表示
	if (lpfpDisp) CharaStatusDisp( ptAct, -10, 3 );
#else

#ifdef PUK2
	// 怪我マーク
	if( (status2 & CHR_STATUS2_HEALTH) != 0
	 && ((status & CHR_STATUS_BATTLE) == 0 || (status & CHR_STATUS_WATCH) == 0
	  || (status & CHR_STATUS_DUEL)==0 ) )
	{
		U4 bmpNo;
		struct BLT_MEMBER bm={0};

		bm.u = bm.v = bm.w = bm.h = 0;
		bm.rgba.rgba = 0xffffffff;
		bm.BltVer = BLTVER_NOMAL;
		bm.bltf = BLTF_NOCHG;
		bm.PalNo = 0;

		realGetNo( CG_B_STR_INJURY, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no )+90-20, 0, &bm );
	}
	
	/***#if 0

	// スキル使用中マーク
	if( (status2 & CHR_STATUS2_SKILL) != 0
	 && ((status & CHR_STATUS_BATTLE) == 0 || (status & CHR_STATUS_WATCH) == 0
	  || (status & CHR_STATUS_DUEL)==0 ) )
	{
		U4 bmpNo;
		struct BLT_MEMBER bm={0};

		bm.u = bm.v = bm.w = bm.h = 0;
		bm.rgba.rgba = 0xffffffff;
		bm.BltVer = BLTVER_NOMAL;
		bm.bltf = BLTF_NOCHG;
		bm.PalNo = 0;

		realGetNo( 22355, &bmpNo );

		// オリジナルソート用にストック
		setMapChar( bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0, &bm );
	}
	#endif***/

	// 等级アップマーク
	//（バトルマーク出时は消す）
	//（観战マーク出时は消す）
	if( (status2 & CHR_STATUS2_LEVELUP) != 0
	 && ext->ptActLevelup == NULL )
	{
		// 等级アップマーク表示用アクション作成
		ext->ptActLevelup =
			createCommmonEffectAction( SPR_icn_14, ptAct->gx, ptAct->gy, 0, 0, DISP_PRIO_CHAR );

		// 等级アップジングル
		if ( status2 & CHR_STATUS2_LEVELUPSE ){
			// ペットなら
			if (ptAct->atr&ACT_ATR_TYPE_TAKENPET){
				if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
					pc.status2 &= (~CHR_STATUS2_PETLEVELUPSE);
				}else{
					charObj[no].status2 &= (~CHR_STATUS2_PETLEVELUPSE);
				}
			}
			// プレーヤーキャラなら
			else{
				if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
					pc.status2 &= (~CHR_STATUS2_LEVELUPSE);
				}else{
					charObj[no].status2 &= (~CHR_STATUS2_LEVELUPSE);
				}
			}
			play_se( 437, ptAct->x, ptAct->y );
		}

		ext->ptActLevelup->damage=0;

		ext->ptActLevelup->bltfon = BLTF_NOCHG;
		ext->ptActLevelup->bltf = BLTF_NOCHG;
	}
	else
	if( ((status2 & CHR_STATUS2_LEVELUP) == 0)
	 && ext->ptActLevelup != NULL )
	{
		// 等级アップマーク表示用アクション抹消
		DeathAction( ext->ptActLevelup );
		ext->ptActLeaderMark = NULL;
		// ペットなら
		if (ptAct->atr&ACT_ATR_TYPE_TAKENPET){
			if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
				pc.status2 &= (~CHR_STATUS2_PETLEVELUP);
			}else{
				charObj[no].status2 &= (~CHR_STATUS2_PETLEVELUP);
			}
		}
		// プレーヤーキャラなら
		else{
			if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
				pc.status2 &= (~CHR_STATUS2_LEVELUP);
			}else{
				charObj[no].status2 &= (~CHR_STATUS2_LEVELUP);
			}
		}
	}
	else
	if( (status2 & CHR_STATUS2_LEVELUP) != 0
	 && ext->ptActLevelup != NULL )
	{
		// 等级アップマークのアニメーション
		ext->ptActLevelup->x  = ptAct->x;
		ext->ptActLevelup->y  = ptAct->y;
		ext->ptActLevelup->mx = ptAct->mx;
		ext->ptActLevelup->my = ptAct->my;

		// アニメーション处理
		pattern( ext->ptActLevelup, ANM_NOMAL_SPD, ANM_LOOP );
		ext->ptActLevelup->bm.bltf |= BLTF_NOCHG;
		// オリジナルソート用にストック
	#ifdef PUK2
		setMapChar( ext->ptActLevelup->bmpNo, ptAct->mx, ptAct->my,
			ptAct->x+Blt_adjust(ext->ptActLevelup,0), ptAct->y+Blt_adjust(ext->ptActLevelup,1)+getCharIconOffsetY( ptAct->anim_chr_no )-20, 0, &ext->ptActLevelup->bm );
	#else
		setMapChar( ext->ptActLevelup->bmpNo, ptAct->mx, ptAct->my,
			ptAct->x, ptAct->y+getCharIconOffsetY( ptAct->anim_chr_no ), 0, &ext->ptActLevelup->bm );
	#endif
		ext->ptActLevelup->damage++;
		if (ext->ptActLevelup->damage>600){
			// 等级アップマーク表示用アクション抹消
			DeathAction( ext->ptActLevelup );
			ext->ptActLevelup = NULL;
			// ペットなら
			if (ptAct->atr&ACT_ATR_TYPE_TAKENPET){
				if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
					pc.status2 &= (~CHR_STATUS2_PETLEVELUP);
				}else{
					charObj[no].status2 &= (~CHR_STATUS2_PETLEVELUP);
				}
			}
			// プレーヤーキャラなら
			else{
				if( ( pc.ptAct != NULL && pc.ptAct == ptAct ) || no == -1 ){
					pc.status2 &= (~CHR_STATUS2_LEVELUP);
				}else{
					charObj[no].status2 &= (~CHR_STATUS2_LEVELUP);
				}
			}
		}
	}

	// 体力??魔力表示
	if (lpfpDisp) CharaStatusDisp( ptAct, -10, 3 );
#endif

#endif
}


// 基本エフェクト表示处理 /////////////////////////////////////////////////
ACTION *createCommmonEffectAction( int graNo, int gx, int gy, int anim, int dir, int prio )
{
	ACTION *ptAct;
	float mx, my;

	/* アクションリストに登録 */
	ptAct = GetAction( PRIO_CHR, 0 );
	if( ptAct == NULL )
		return NULL;

	// 实行关数
//	ptAct->func = charProc;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = anim;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	ptAct->anim_ang = dir;
	// 表示优先度
	ptAct->dispPrio = prio;
	// 1行インフォ表示フラグ
	ptAct->atr = ACT_ATR_HIDE2;
	// 初期位置
	ptAct->nextGx = gx;					// マップグリッド座标（移动先）
	ptAct->nextGy = gy;
	ptAct->gx = gx;						// マップグリッド座标（现在地）
	ptAct->gy = gy;
	ptAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	ptAct->my = (float)gy * GRID_SIZE;
	ptAct->vx = 0;						// 移动增分
	ptAct->vy = 0;

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	return ptAct;
}

// 基本エフェクト表示处理（ループしない） /////////////////////////////////
// アニメーションを１ループしたらアクションを終了する
void commmonEffectNoLoop( ACTION *ptAct )
{
	float mx, my;

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	if( pattern( ptAct, ANM_NOMAL_SPD, ANM_NO_LOOP ) == 0 )
	{
		// オリジナルソート用にストック
#ifdef PUK2
		setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), 0, &ptAct->bm );
#else
		setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#endif
	}
	else
	{
#ifdef PUK3_MM_MEMLEAK
		if (ptAct->pOther) *( (ACTION **)ptAct->pOther ) = NULL;
#endif
		// アニメーション終わったのでアクション終わる
		DeathAction( ptAct );
		ptAct = NULL;
	}

}

#ifdef PUK3_MM_MEMLEAK
ACTION *createCommmonEffectNoLoop( int graNo, int gx, int gy, int anim, int dir, int prio, ACTION **pActParent )
#else
ACTION *createCommmonEffectNoLoop( int graNo, int gx, int gy, int anim, int dir, int prio )
#endif
{
	ACTION *ptAct;
	float mx, my;

	/* アクションリストに登録 */
	ptAct = GetAction( PRIO_CHR, 0 );
	if( ptAct == NULL )
		return NULL;

	// 实行关数
	ptAct->func = commmonEffectNoLoop;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = anim;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	ptAct->anim_ang = dir;
	// 表示优先度
	ptAct->dispPrio = prio;
	// 1行インフォ表示フラグ
	ptAct->atr = ACT_ATR_HIDE2;
	// 初期位置
	ptAct->nextGx = gx;					// マップグリッド座标（移动先）
	ptAct->nextGy = gy;
	ptAct->gx = gx;						// マップグリッド座标（现在地）
	ptAct->gy = gy;
	ptAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	ptAct->my = (float)gy * GRID_SIZE;
	ptAct->vx = 0;						// 移动增分
	ptAct->vy = 0;

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);
#ifdef PUK3_MM_MEMLEAK
	// 終了时、これが示すACTIONのポインタ变数にNULLを代入する
	ptAct->pOther = pActParent;
#endif

	return ptAct;
}


// キャラ动作变更 /////////////////////////////////////////////////////////
#ifdef PUK3_PUT_ON
void changeCharAct( ACTION *ptAct, int x, int y, int dir, int action,
								int effectno, int param3 )
#else
void changeCharAct( ACTION *ptAct, int x, int y, int dir, int action,
								int effectno )
#endif
{
	CHAREXTRA *ext;
	int no;
	int i;


	if( ptAct == NULL )
		return;

#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_changeCharAct );
#endif
	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;

	switch( action )
	{
		// 立ち止まる
		case 0:
			ptAct->bufCount = 0;
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_STAND, TRUE );
#else
			ptAct->anim_no = ANIM_STAND;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 方向転换
		case 50:
			// 现在地と座标が违うなら步かせる
			charObj[no].stockDir = dir;
			charObj[no].stockDirX = x;
			charObj[no].stockDirY = y;
			if( (x == ptAct->nextGx && y == ptAct->nextGy)
			 || ABS( ABS( x - ptAct->nextGx ) - ABS( y - ptAct->nextGy ) ) > 1 )
			{
				break;
			}
#ifdef PUK3_CHECK_IN_PARTY
			// このキャラが布ティーに入っているなら
			// たとえ座标が违っても移动せずに方向だけ变更
			if( (charObj[no].status & CHR_STATUS_PARTY) != 0 ){
				break;
			}
#endif

		// 步く
		case 1:
			// NPCがリーダの时は移动场所をスタックに入れて处理する
			if( (charObj[no].status & CHR_STATUS_PARTY) != 0
			 && (charObj[no].status & CHR_STATUS_LEADER) != 0 )
			{
				// バッファが空いていたらためる
				if( ptAct->bufCount < sizeof( ptAct->bufGx )/sizeof( int ) )
				{
					correctCharMovePoint( ptAct, x, y );
				}
				else
				{
#ifdef PUK3_RAG_WARP
					// 处理の都合上下へ移动
#else
					// バッファが一杯の时は离れすぎているのでワープさせる
					stockCharMovePoint( ptAct, x, y );
					charObj[no].stockDir = -1;
#endif

					// 仲间も一绪にワープ
					for( i = 1; i < MAX_PARTY; i++ )
					{
						if( party[i].useFlag != 0 )
						{
							if( party[i].id != pc.id )
							{
								// ＮＰＣの时
								party[i].ptAct->bufCount = 0;
#ifdef PUK3_RAG_WARP
								// 移动
								setCharWarpPoint( party[i].ptAct,
									 ptAct->bufGx[ ptAct->bufCount - i ],
									 ptAct->bufGy[ ptAct->bufCount - i ] );
								// 方向の设定
								party[i].ptAct->anim_ang = ( getDirFromXY(
																 (float)( ptAct->bufGx[ ptAct->bufCount - i - 1 ]
																 - ptAct->bufGx[ ptAct->bufCount - i ] ),
																 (float)( ptAct->bufGy[ ptAct->bufCount - i - 1 ]
																 - ptAct->bufGy[ ptAct->bufCount - i ] )
																 ) + 4 ) % 8;
#else
								setCharWarpPoint( party[i].ptAct, x, y );
#endif
							}
							else
							{
								// ＰＣの时
								if( pc.ptAct != NULL )
								{
									pc.ptAct->bufCount = 0;
								}
#ifdef PUK3_RAG_WARP
								// 移动
								setPcWarpPoint(
									 ptAct->bufGx[ ptAct->bufCount - i ],
									 ptAct->bufGy[ ptAct->bufCount - i ] );
								// 方向の设定
								if( pc.ptAct != NULL ){
									party[i].ptAct->anim_ang = ( getDirFromXY(
																 (float)( ptAct->bufGx[ ptAct->bufCount - i - 1 ]
																 - ptAct->bufGx[ ptAct->bufCount - i ] ),
																 (float)( ptAct->bufGy[ ptAct->bufCount - i - 1 ]
																 - ptAct->bufGy[ ptAct->bufCount - i ] )
																 ) + 4 ) % 8;
									pc.dir = party[i].ptAct->anim_ang;
								}
#else
								setPcWarpPoint( x, y );
#endif
#ifdef PUK3_WARP_RAG
								// ワープに限りこの后に移动处理を行わない可能性があるため、
								// ここで呼んでおく
								// 普通は移动处理のときに呼ばれる
								setPcPoint();
								// 座标がずれるとマップが崩れる场合があるのでしたを呼ぶ
								// マップ处理リセット
								resetMap();
#endif
							}
						}
					}
#ifdef PUK3_RAG_WARP
					// 处理の都合上こっちへ移动
					// バッファが一杯の时は离れすぎているのでワープさせる
					stockCharMovePoint( ptAct, x, y );
					charObj[no].stockDir = -1;
#endif
				}
			}
			else
			{
				stockCharMovePoint( ptAct, x, y );
			}
#ifdef PUK3_RIDE
			// 移动速度を设定(步くの时だけ、方向転换でもここ通る可能性があるので)
			if ( action == 1 ) ptAct->walkSpeed = effectno;

			setRiderAnimeNo( ptAct, ANIM_WALK, FALSE );
#else
			ptAct->anim_no = ANIM_WALK;
#endif
			break;

		// 走り始めるアクション
		case 2:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_B_WALK_START, TRUE );
#else
			ptAct->anim_no = ANIM_B_WALK_START;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 走り中アクション
		case 3:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_B_WALK, TRUE );
#else
			ptAct->anim_no = ANIM_B_WALK;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 走り終えるアクション
		case 4:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_B_WALK_END, TRUE );
#else
			ptAct->anim_no = ANIM_B_WALK_END;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 攻击アクション
		case 5:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_ATTACK, TRUE );
#else
			ptAct->anim_no = ANIM_ATTACK;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 魔法咏唱
		case 6:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_MAGIC, TRUE );
#else
			ptAct->anim_no = ANIM_MAGIC;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 投掷アクション
		case 7:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_THROW, TRUE );
#else
			ptAct->anim_no = ANIM_THROW;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 受伤アクション
		case 8:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_DAMAGE, TRUE );
#else
			ptAct->anim_no = ANIM_DAMAGE;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 防御アクション
		case 9:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_GUARD, TRUE );
#else
			ptAct->anim_no = ANIM_GUARD;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 倒下アクション
		case 10:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_DEAD, TRUE );
#else
			ptAct->anim_no = ANIM_DEAD;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 座るアクション
		case 11:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_SIT, TRUE );
#else
			ptAct->anim_no = ANIM_SIT;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 挥手アクション
		case 12:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_HAND, TRUE );
#else
			ptAct->anim_no = ANIM_HAND;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 喜ぶアクション
		case 13:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_HAPPY, TRUE );
#else
			ptAct->anim_no = ANIM_HAPPY;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 怒るアクション
		case 14:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_ANGRY, TRUE );
#else
			ptAct->anim_no = ANIM_ANGRY;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 悲伤アクション
		case 15:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_SAD, TRUE );
#else
			ptAct->anim_no = ANIM_SAD;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 点头アクション
		case 16:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_NOD, TRUE );
#else
			ptAct->anim_no = ANIM_NOD;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// じゃんけんアクション　石头
		case 17:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_GU, TRUE );
#else
			ptAct->anim_no = ANIM_GU;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// じゃんけんアクション　剪刀
		case 18:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_CHOKI, TRUE );
#else
			ptAct->anim_no = ANIM_CHOKI;
#endif
			ptAct->anim_no_bak = -1;
			ptAct->anim_ang = dir;
			break;

		// じゃんけんアクション　布
		case 19:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_PA, TRUE );
#else
			ptAct->anim_no = ANIM_PA;
#endif
			ptAct->anim_no_bak = -1;
			ptAct->anim_ang = dir;
			break;


		// 歩きアクション
		case 30:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_WALK, TRUE );
#else
			ptAct->anim_no = ANIM_WALK;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;

		// 立ちアクション
		case 31:
#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_STAND, TRUE );
#else
			ptAct->anim_no = ANIM_STAND;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			break;


		// 战闘情报＆マーク
		case 40:
			if( effectno >= 0 )
			{
				setCharBattle( ptAct, effectno );
				// 立ちアニメにかえる
#ifdef PUK3_RIDE
				setRiderAnimeNo( ptAct, ANIM_STAND, TRUE );
#else
				ptAct->anim_no = ANIM_STAND;
				ptAct->anim_no_bak = -1;
#endif
			}
			else
			{
				delCharBattle( ptAct );
			}
			setCharWarpPoint( ptAct, x, y );
			ptAct->anim_ang = dir;
			break;

		// リーダーマーク
		case 41:
			if( effectno == 1 )
			{
				setCharLeader( ptAct );
			}
			else
			{
				delCharLeader( ptAct );
			}
			//setCharWarpPoint( ptAct, x, y );
			ptAct->anim_ang = dir;
			break;

		// 観战マーク
		case 42:
			if( effectno >= 0 )
			{
				setCharWatch( ptAct, effectno );
			}
			else
			{
				delCharWatch( ptAct );
			}
			setCharWarpPoint( ptAct, x, y );
			ptAct->anim_ang = dir;
			break;

		// 名称の色情报
		case 43:
			break;

		// デュエルマーク
		case 44:
			if( effectno >= 0 )
			{
				setCharDuel( ptAct, effectno );
			}
			else
			{
				delCharDuel( ptAct );
			}
			setCharWarpPoint( ptAct, x, y );
			ptAct->anim_ang = dir;
			break;

#ifdef PUK2
		// 怪我マーク
		case 45:
			// 0:表示消す 1:表示
			if( effectno == 1 )
			{
				setCharHealth( ptAct );
			}
			else
			{
				delCharHealth( ptAct );
			}
			ptAct->anim_ang = dir;
			break;

		// スキル使用中マーク
		case 46:
			// 0:表示消す 0以外:その番号のアイコン表示
			if( effectno )
			{
				setCharSkill( ptAct, effectno );
			}
			else
			{
				delCharSkill( ptAct );
			}
			ptAct->anim_ang = dir;
			break;

		// 等级アップマーク
		case 47:
			// 0:表示消す 1:表示
			setCharLevelup( ptAct );
			ptAct->anim_ang = dir;
			break;

		// ペット怪我マーク
		case 48:
			// 0:表示消す 1:表示
			if( effectno == 1 )
			{
				setCharPetHealth( ptAct );
			}
			else
			{
				delCharPetHealth( ptAct );
			}
			ptAct->anim_ang = dir;
			break;

		// ペット等级アップマーク
		case 49:
			// 0:表示消す 1:表示
			setCharPetLevelup( ptAct );
			ptAct->anim_ang = dir;
			break;
#endif

		// ワープ
		case 51:
			setCharWarpPoint( ptAct, x, y );
			ptAct->bufCount = 0;

#ifdef PUK3_RIDE
			setRiderAnimeNo( ptAct, ANIM_STAND, TRUE );
#else
			ptAct->anim_no = ANIM_STAND;
			ptAct->anim_no_bak = -1;
#endif
			ptAct->anim_ang = dir;
			charObj[no].stockDir = -1;
			break;

#ifdef PUK2
		// グラフィック番号变更
		case 60:
	#ifdef _CG2_NEWGRAPHIC
			ptAct->anim_chr_no = getNewGraphicNo( effectno );	// 新グラフィック番号代入
	#else
			ptAct->anim_chr_no = effectno;	// 新グラフィック番号代入
	#endif
	#if defined(PUK3_RIDE) || defined(PUK3_PUT_ON)
		#ifdef _CG2_NEWGRAPHIC
			charObj[ext->charObjTblId].graNo = getNewGraphicNo( effectno );	// 新グラフィック番号代入
		#else
			charObj[ext->charObjTblId].graNo = effectno;	// 新グラフィック番号代入
		#endif
	#endif
	#ifdef PUK3_PUT_ON
			// 被り物あるなら
			if (param3){
				if (!ext->ptPuton) ext->ptPuton = makePuton( param3 );
				if (ext->ptPuton){
	#ifdef _CG2_NEWGRAPHIC
					ext->ptPuton->anim_chr_no = getNewGraphicNo( param3 );
	#else
					ext->ptPuton->anim_chr_no = param3;
	#endif
				}
			}
			// 被り物ないなら
			else{
				if (ext->ptPuton){
					DeathAction(ext->ptPuton);
					ext->ptPuton = NULL;
				}
			}
	#endif
			break;

		// リバース使用
		case 61:
			break;

		// リバース中止
		case 62:
			break;

		// スキル使用
		case 63:
			setCharSkill( ptAct, effectno );
			ptAct->anim_ang = dir;
			break;

		// スキル使用中止
		case 64:
			delCharSkill( ptAct );
			ptAct->anim_ang = dir;
			break;
#endif
#ifdef PUK3_RIDE
		// ペットライド
		case 70:
			// 乘るなら
			if ( effectno >= 0 ){
				// モンスターがでてないなら作る
				if (!ext->ptRide){
					ext->ptRide = GetAction( PRIO_CHR, NULL );
				
					// 实行关数
					ext->ptRide->func = NULL;
					// 动作番号
					ext->ptRide->anim_no = ANIM_STAND;
					// 表示优先度
					ext->ptRide->dispPrio = DISP_PRIO_CHAR;
					// 1行インフォ表示フラグ
					ext->ptRide->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
#ifdef PUK3_RIDE_SOUND
					// ペット扱いで音鸣らさない
					ext->ptRide->atr |= ACT_ATR_TYPE_PET;
#endif
				}

				// グラフィックの番号
#ifdef _CG2_NEWGRAPHIC
				ext->ptRide->anim_chr_no = getNewGraphicNo(effectno);
#else
				ext->ptRide->anim_chr_no = effectno;
#endif
	
				ptAct->anim_chr_no = getRiderCharaGra(charObj[ext->charObjTblId].graNo);
				ptAct->actNo = CHARACT_RIDE_ON;
				ext->actCnt = 0;

				ext->ptRide->anim_no = ANIM_STAND;
				if ( ptAct->anim_no == ANIM_WALK ){
					ext->ptRide->anim_no = ANIM_WALK;
				}
			}
			// 降りるなら
			else{
				ptAct->actNo = CHARACT_GET_OFF;
				ext->actCnt = 0;

				if (ext->ptRide){
					ext->ptRide->anim_no = ANIM_STAND;
					if ( ptAct->anim_no == ANIM_WALK ){
						ext->ptRide->anim_no = ANIM_WALK;
					}
				}
			}
			break;
#endif
#ifdef PUK3_VEHICLE
		// 乘り物归还
		case 80:
			ptAct->actNo = CHARACT_VEHICLE_RETURN;
	#ifdef _CG2_NEWGRAPHIC
			ptAct->anim_chr_no = getNewGraphicNo( effectno );
	#else
			ptAct->anim_chr_no = effectno;
	#endif
			ext->actCnt = 0;
			break;
#endif
	}
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}




// キャラ管理テーブル处理 /////////////////////////////////////////////////
void charObjProc( void )
{
#if 0
	int i;

	for( i = 0; i < tailCharObj; i++ )
	{
		// 表示されていないなら表示する
		if( charObj[i].use == CHAROBJ_USE_STAY )
		{
			charObj[i].ptAct = createCharAction( charObj[i].graNo, charObj[i].gx, charObj[i].gy, 0 );
			if( charObj[i].ptAct != NULL )
			{
				charObj[i].use = CHAROBJ_USE_VIEW;
			}
		}
	}
#endif
}


// ID检索
// 戾り值： 0以上 ... IDのある位置 / -1 ... 无かった
int searchCharObjId( int id )
{
	int i;
	int no = -1;

	for( i = searchCharObj; i < tailCharObj; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE
		 && charObj[i].id == id )
		{
			no = i;
			searchCharObj = i;
			break;
		}
	}
	if( no < 0 && searchCharObj > 0 )
	{
		for( i = 0; i < searchCharObj; i++ )
		{
			if( charObj[i].use != CHAROBJ_USE_FREE
			 && charObj[i].id == id )
			{
				no = i;
				searchCharObj = i;
				break;
			}
		}
	}
	return no;
}


// 座标检索
// 戾り值：TRUE ... そこに何かある
BOOL checkCharObjPoint( int gx, int gy, short type )
{
	int i;
	int no = -1;

	for( i = 0; i < tailCharObj; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE && charObj[i].ptAct != NULL )
		{
			if( charObj[i].ptAct->gx == gx && charObj[i].ptAct->gy == gy
			 && (charObj[i].type & type) != 0 )
			{
				return TRUE;
			}
		}
	}

	return FALSE;
}

#ifdef PUK3
// 座标检索
// 戾り值：キャラクターのＩＤ
int getUserNpcID( int gx, int gy, short type )
{
	int i;
	int no = -1;

	for( i = 0; i < tailCharObj; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE && charObj[i].ptAct != NULL )
		{
			if( charObj[i].ptAct->gx == gx && charObj[i].ptAct->gy == gy
			 && (charObj[i].type & type) != 0 )
			{
				return charObj[i].id;
			}
		}
	}

	return -1;
}
#endif


// 座标＆状态检索
// 戾り值：1 ... そこに何かありその状态
//         0 ... 何もない
#ifdef PUK2
int checkCharObjPointStatus( int gx, int gy, short type, unsigned short status, unsigned short status2 )
#else
int checkCharObjPointStatus( int gx, int gy, short type, unsigned short status )
#endif
{
	int i;
	int no = -1;

	for( i = 0; i < tailCharObj; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE && charObj[i].ptAct != NULL )
		{
			if( charObj[i].ptAct->gx == gx && charObj[i].ptAct->gy == gy
			 && (charObj[i].type & type) != 0 )
			{
#ifdef PUK2
				if( (status==0) || ( (charObj[i].status & status ) != 0 ) )
				{
					if( (status2==0) || ( (charObj[i].status2 & status2 ) != 0 ) )
					{
						return 1;
					}
				}
#else
				if( (charObj[i].status & status ) != 0 )
				{
					return 1;
				}
#endif
			}
		}
	}

	return 0;
}


// 座标＆状态检索
// 戾り值：1 ... その状态じゃない何かがある
//         0 ... 何もない
#ifdef PUK2
int checkCharObjPointNotStatus( int gx, int gy, short type, unsigned short status, unsigned short status2 )
#else
int checkCharObjPointNotStatus( int gx, int gy, short type, unsigned short status )
#endif
{
	int i;
	int no = -1;

	for( i = 0; i < tailCharObj; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE && charObj[i].ptAct != NULL )
		{
			if( charObj[i].ptAct->gx == gx && charObj[i].ptAct->gy == gy
			 && (charObj[i].type & type) != 0 )
			{
				if( (charObj[i].status & status ) == 0 )
				{
#ifdef PUK2
					if( (charObj[i].status2 & status2 ) == 0 )
					{
						return 1;
					}
#else
					return 1;
#endif
				}
			}
		}
	}

	return 0;
}

// キャラ管理テーブルに登録
//
//   NPC用
//
void setNpcCharObj( int id, int graNo, int gx, int gy, int dir,
	char *name, char *freeName, char *title,
	int level, int nameColor, int walk, int height, int charType )
{
	int no;
	BOOL existFlag = FALSE;
	CHAREXTRA *ext;

	// 登録されているかチェック
	if( (no = searchCharObjId( id )) >= 0 )
	{
		existFlag = TRUE;
	}

	// まだ登録されていないなら登録
	if( !existFlag )
	{
		no = getCharObjBuf();
		if( no < 0 )
			return;	// テーブルが满タンなら終わる

#ifdef PUK2
		// 方向の无いものは方向を固定
		if( charType > CHAR_TYPEPET ){
			// 新マップの场合は方向が设定されているのでスキップ
			if( SugiMapStat.MapVer == 0 ){
				if ( !CheckAnimetion( graNo ) ) dir = 0;
			}
		}
#endif

		charObj[no].ptAct = createCharAction( graNo, gx, gy, dir );
		if( charObj[no].ptAct == NULL )
		{
			// 登録できないなら終わる（本当はあっては行けない）
			return;
		}

		charObj[no].use = CHAROBJ_USE_VIEW;
		ext = (CHAREXTRA *)charObj[no].ptAct->pYobi;
		ext->charObjTblId = no;

		charObj[no].type      = getAtrCharObjType( charType );
		charObj[no].id        = id;
		charObj[no].stockDir  = -1;
	}
	else
	// 登録されていたら变更
	{
#ifdef PUK3_NOEXISTCHARA
		// ACTIONが无いなら作ってやる
		if ( charObj[no].ptAct == NULL ){
			charObj[no].ptAct = createCharAction( graNo, gx, gy, dir );
			// 作れなかったら今回はあきらめる
			if ( charObj[no].ptAct == NULL ){
				return;
			}
		}

		// 假キャラだったら再设定
		if ( charObj[no].type == CHAROBJ_TYPE_UNKNOWN ){
			charObj[no].type      = getAtrCharObjType( charType );
		}
#endif
		// 移动させる
#if 0
		stockCharMovePoint( charObj[no].ptAct, gx, gy );
		charObj[no].ptAct->anim_chr_no = graNo;
		charObj[no].ptAct->anim_ang = dir;
#endif
	}

	charObj[no].graNo     = graNo;
	charObj[no].nameColor = nameColor;
	charObj[no].level     = level;
	charObj[no].gx = gx;
	charObj[no].gy = gy;
	charObj[no].dir = dir;
	if( walk != 0 )
	{
		charObj[no].status |= CHR_STATUS_W;
	}
	if( height != 0 )
	{
		charObj[no].status |= CHR_STATUS_H;
	}

	if( strlen( name ) <= CHAR_NAME_LEN )
	{
		strcpy( charObj[no].name, name );
	}
	if( charType != CHAR_TYPEPET )
	{
#ifdef PUK2
		char	free[256];
		char	guildname[256];
		char	guildtitle[256];

		getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
		makeRecvString( free );
		if( strlen( free ) <= CHAR_FREENAME_LEN ){
			strcpy( charObj[no].freeName, free );
		}
		getStringToken( freeName, DELIMITER, 2, sizeof( guildname )-1, guildname );
		makeRecvString( guildname );
		if( strlen( guildname ) <= 32 ){
			strcpy( charObj[no].guildName, guildname );
		}
		getStringToken( freeName, DELIMITER, 3, sizeof( guildtitle )-1, guildtitle );
		makeRecvString( guildtitle );
		if( strlen( guildtitle ) <= 32 ){
			strcpy( charObj[no].guildTitle, guildtitle );
		}
#else
		if( strlen( freeName ) <= CHAR_FREENAME_LEN )
		{
			strcpy( charObj[no].freeName, freeName );
		}
#endif
		if( strlen( title ) <= CHAR_TITLE_LEN )
		{
			strcpy( charObj[no].title, title );
		}
	}
	else
	{
#ifdef PUK2
		char	free[256];

		getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
		makeRecvString( free );
		if( strlen( free ) <= CHAR_FREENAME_LEN ){
			strcpy( charObj[no].freeName, free );
		}
#else
		if( strlen( freeName ) <= PET_FREENAME_LEN )
		{
			strcpy( charObj[no].freeName, freeName );
		}
#endif
		charObj[no].title[0] = '\0';
	}

	charObj[no].charType = getAtrCharType( charType );
#ifdef PUK2
	// ＮＰＣの场合、フラグをチェック
	if( charObj[no].charType & ACT_ATR_TYPE_OTHER ){
		// level がフラグ代わりに使用されているかチェック
		if( level & 0x100 ){
			// 振り向き无效？
			if( level & 0x40 ){
				charObj[no].charType |= ACT_ATR_NOFLIP;
			}
		}
	}
#endif

	charObj[no].newFoundFlag = 1;	// 新発见チェックをする

	// アクションがある时だけ值を入れる
	if( charObj[no].ptAct == NULL )
		return;

	// アクションがある时にＣプロトコルが呼ばれたら
	// X,Y,DIRは无视する
	charObj[no].ptAct->anim_chr_no = graNo;
	charObj[no].ptAct->level = level;
	charObj[no].ptAct->atr |= charObj[no].charType;
	if( strlen( name ) <= CHAR_NAME_LEN )
	{
		strcpy( charObj[no].ptAct->name, name );
	}
	if( charType != CHAR_TYPEPET )
	{
#ifdef PUK2
		char	free[256];
		char	guildname[256];
		char	guildtitle[256];

		getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
		makeRecvString( free );
		if( strlen( free ) <= CHAR_FREENAME_LEN ){
			strcpy( charObj[no].ptAct->freeName, free );
		}
		getStringToken( freeName, DELIMITER, 2, sizeof( guildname )-1, guildname );
		makeRecvString( guildname );
		if( strlen( guildname ) <= 32 ){
			strcpy( charObj[no].ptAct->guildName, guildname );
		}
		getStringToken( freeName, DELIMITER, 3, sizeof( guildtitle )-1, guildtitle );
		makeRecvString( guildtitle );
		if( strlen( guildtitle ) <= 32 ){
			strcpy( charObj[no].ptAct->guildTitle, guildtitle );
		}
#else
		if( strlen( freeName ) <= CHAR_FREENAME_LEN )
		{
			strcpy( charObj[no].ptAct->freeName, freeName );
		}
#endif
		if( strlen( title ) <= CHAR_TITLE_LEN )
		{
			strcpy( charObj[no].ptAct->title, title );
		}
	}
	else
	{
#ifdef PUK2
		char	free[256];

		getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
		makeRecvString( free );
		if( strlen( free ) <= CHAR_FREENAME_LEN ){
			strcpy( charObj[no].ptAct->freeName, free );
		}
#else
		if( strlen( freeName ) <= PET_FREENAME_LEN )
		{
			strcpy( charObj[no].ptAct->freeName, freeName );
		}
		charObj[no].ptAct->title[0] = '\0';
#endif
	}
	charObj[no].ptAct->itemNameColor = nameColor;
}




//
// ペットメールでペットが戾ってくる处理用
//
BOOL setReturnPetObj( int id, int graNo, int gx, int gy, int dir, 
char *name, char *freeName, int level, int nameColor, int walk, int height, int charType )
{
	int no;

	// 登録されているかチェック
	if( (no = searchCharObjId( id )) >= 0 )
	{
		// いたら終わる
		return FALSE;
	}

	// まだ登録されていないなら登録
	no = getCharObjBuf();
	if( no < 0 )
		return FALSE;	// テーブルが满タンなら終わる

	charObj[no].ptAct = NULL;
	charObj[no].use = CHAROBJ_USE_VIEW;
	charObj[no].type      = getAtrCharObjType( charType );
	charObj[no].id        = id;

	charObj[no].graNo     = graNo;
	charObj[no].nameColor = nameColor;
	charObj[no].level     = level;
	charObj[no].gx = gx;
	charObj[no].gy = gy;
	charObj[no].dir = dir;
	if( walk != 0 )
	{
		charObj[no].status |= CHR_STATUS_W;
	}
	if( height != 0 )
	{
		charObj[no].status |= CHR_STATUS_H;
	}

	if( strlen( name ) <= CHAR_NAME_LEN )
	{
		strcpy( charObj[no].name, name );
	}
#ifdef PUK2
	char	free[256];
	char	guildname[256];
	char	guildtitle[256];

	getStringToken( freeName, DELIMITER, 1, sizeof( free )-1, free );
//  文字列变换する必要なし
//	makeRecvString( free );
	if( strlen( free ) <= CHAR_FREENAME_LEN ){
		strcpy( charObj[no].freeName, free );
	}
	getStringToken( freeName, DELIMITER, 2, sizeof( guildname )-1, guildname );
	makeRecvString( guildname );
	if( strlen( guildname ) <= 32 ){
		strcpy( charObj[no].guildName, guildname );
	}
	getStringToken( freeName, DELIMITER, 3, sizeof( guildtitle )-1, guildtitle );
	makeRecvString( guildtitle );
	if( strlen( guildtitle ) <= 32 ){
		strcpy( charObj[no].guildTitle, guildtitle );
	}
#else
	if( strlen( freeName ) <= CHAR_FREENAME_LEN )
	{
		strcpy( charObj[no].freeName, freeName );
	}
#endif

	charObj[no].charType = getAtrCharType( charType );

	return TRUE;
}



//
//   アイテム用
//
void setItemCharObj( int id, int graNo, int gx, int gy, int dir, int classNo, char *info, int num )
{
	int no;
	BOOL existFlag = FALSE;
	CHAREXTRA *ext;

	// 登録されているかチェック
	if( (no = searchCharObjId( id )) >= 0 )
	{
		existFlag = TRUE;
	}

	// まだ登録されていないなら登録
	if( !existFlag )
	{
		no = getCharObjBuf();
		if( no < 0 )
			return;	// テーブルが满タンなら終わる

		charObj[no].ptAct = createCharAction( graNo, gx, gy, dir );
		if( charObj[no].ptAct == NULL )
		{
			// 登録できないなら終わる（本当はあっては行けない）
			return;
		}

		charObj[no].use = CHAROBJ_USE_VIEW;
		ext = (CHAREXTRA *)charObj[no].ptAct->pYobi;
		ext->charObjTblId = no;

		charObj[no].type      = CHAROBJ_TYPE_ITEM;
		charObj[no].id        = id;
		charObj[no].stockDir  = -1;
	}
	else
	// 登録されていたら变更
	{
#if 0
		// 移动させる
		stockCharMovePoint( charObj[no].ptAct, gx, gy );
		charObj[no].ptAct->anim_ang = dir;
#endif
	}

	charObj[no].graNo   = graNo;
	charObj[no].classNo = classNo;
	charObj[no].gx = gx;
	charObj[no].gy = gy;
	charObj[no].dir = dir;
	if( strlen( info ) <= 60 )
	{
		strcpy( charObj[no].info, info );
	}
	charObj[no].charType = ACT_ATR_TYPE_ITEM;
	charObj[no].newFoundFlag = 0;	// 新発见チェックをしない
	charObj[no].num = num;

	// アクションがある时だけ值を入れる
	if( charObj[no].ptAct == NULL )
		return;

	charObj[no].ptAct->anim_chr_no = graNo;
	// 1行インフォに出すためにnameに入れる
	if( strlen( info ) <= ITEM_NAME_LEN )
	{
		strcpy( charObj[no].ptAct->name, info );
	}
	else
	{
		strcpy( charObj[no].ptAct->name, "???" );                           //MLHIDE
	}
	charObj[no].ptAct->atr |= ACT_ATR_TYPE_ITEM;
	charObj[no].ptAct->itemNameColor = classNo;

	ext = (CHAREXTRA *)charObj[no].ptAct->pYobi;
	ext->num = num;
}


//
//   お金用
//
void setMoneyCharObj( int id, int graNo, int gx, int gy, int dir, int money, char *info )
{
	int no;
	BOOL existFlag = FALSE;
	CHAREXTRA *ext;

	// 登録されているかチェック
	if( (no = searchCharObjId( id )) >= 0 )
	{
		existFlag = TRUE;
	}

	// まだ登録されていないなら登録
	if( !existFlag )
	{
		no = getCharObjBuf();
		if( no < 0 )
			return;	// テーブルが满タンなら終わる


		charObj[no].ptAct = createCharAction( graNo, gx, gy, dir );
		if( charObj[no].ptAct == NULL )
		{
			// 登録できないなら終わる（本当はあっては行けない）
			return;
		}

		charObj[no].use = CHAROBJ_USE_VIEW;
		ext = (CHAREXTRA *)charObj[no].ptAct->pYobi;
		ext->charObjTblId = no;

		charObj[no].type      = CHAROBJ_TYPE_MONEY;
		charObj[no].id        = id;
		charObj[no].stockDir  = -1;
	}
	else
	// 登録されていたら变更
	{
#if 0
		// 移动させる
		stockCharMovePoint( charObj[no].ptAct, gx, gy );
		charObj[no].ptAct->anim_ang = dir;
#endif
	}

	charObj[no].graNo = graNo;
	charObj[no].money = money;
	charObj[no].gx = gx;
	charObj[no].gy = gy;
	charObj[no].dir = dir;
	if( strlen( info ) <= 60 )
	{
		strcpy( charObj[no].info, info );
	}
	charObj[no].charType = ACT_ATR_TYPE_GOLD;
	charObj[no].newFoundFlag = 0;	// 新発见チェックをしない

	// アクションがある时だけ值を入れる
	if( charObj[no].ptAct == NULL )
		return;

	charObj[no].ptAct->anim_chr_no = graNo;
	// 1行インフォに出すためにnameに入れる
	if( strlen( info ) <= CHAR_NAME_LEN )
	{
		strcpy( charObj[no].ptAct->name, info );
	}
	else
	{
		strcpy( charObj[no].ptAct->name, "???" );                           //MLHIDE
	}
	charObj[no].ptAct->atr |= ACT_ATR_TYPE_GOLD;
}



// キャラ管理バッファの取得
//  戾り值：charObj配列の添え时
//          -1 ... バッファがない
int getCharObjBuf( void )
{
	int i;
	int ret;

	// テーブルが满タンなら終わる
	if( maxCharObj >= MAX_CHAROBJ )
		return -1;

	ret = freeCharObj;

	// 追加したら最大值更新
	maxCharObj++;

	// 最后尾の登録位置
	if( freeCharObj+1 > tailCharObj )
		tailCharObj = freeCharObj+1;

	// 次の空きを见つけておく
	for( i = freeCharObj+1; i < MAX_CHAROBJ; i++ )
	{
		if( charObj[i].use == CHAROBJ_USE_FREE )
		{
			freeCharObj = i;
			break;
		}
	}
	if( freeCharObj > 0 && i >= MAX_CHAROBJ )
	{
		for( i = 0; i < freeCharObj; i++ )
		{
			if( charObj[i].use == CHAROBJ_USE_FREE )
			{
				freeCharObj = i;
				break;
			}
		}
	}

	return ret;
}



// キャラの种类から属性用のパラメータを作る
//  引  数：type ... サーバで管理されている种类番号
//  戾り值：アクションのatrに设定する属性值
int getAtrCharType( int type )
{
	switch( type )
	{
		// ＮＰＣ
		case CHAR_TYPEPLAYER:
		case CHAR_TYPEBUS:
			return ACT_ATR_TYPE_OTHER_PC;

		// ペット
		case CHAR_TYPEPET:
			return ACT_ATR_TYPE_PET;

		// その他
		default:
			return ACT_ATR_TYPE_OTHER;
	}

	return 0;
}


// キャラの种类からキャラ管理用の种类を取り出す
//  引  数：type ... サーバで管理されている种类番号
//  戾り值：charObjのtypeに设定する属性值
int getAtrCharObjType( int type )
{
	int ret;


	switch( type )
	{
		case CHAR_TYPEPLAYER:
			ret = CHAROBJ_TYPE_USER_NPC;
			break;

		case CHAR_TYPEDENGON:
		case CHAR_TYPEDUELRANKING:
		case CHAR_TYPEMSG:
			ret = CHAROBJ_TYPE_NPC | CHAROBJ_TYPE_LOOKAT;
			break;

		case CHAR_TYPEBUS:
			ret = CHAROBJ_TYPE_NPC | CHAROBJ_TYPE_PARTY_OK;
			break;

#ifdef PUK2
		case CHAR_TYPEGUILDMONSTER:
			ret = CHAROBJ_TYPE_NPC | CHAROBJ_TYPE_GUILMON;
			break;
#endif
		default:
			ret = CHAROBJ_TYPE_NPC;
			break;
	}
#ifdef PUK3_NOEXISTCHARA
	if ( type < 0 || CHAR_TYPENUM <= type ){
		ret = CHAROBJ_TYPE_UNKNOWN;
	}
#endif

	return ret;
}


// IDをみてアクションポインタを返す
// それ以外はNULL
ACTION *getCharObjAct( int id )
{
	int no;

	if( (no = searchCharObjId( id )) < 0 )
		return NULL;

	if( charObj[no].ptAct == NULL )
		return NULL;

	return charObj[no].ptAct;
}


// キャラ管理テーブルから抹消
void delCharObj( int id )
{
	int i;
	int no;
	CHAREXTRA *ext;

	if( pc.id == id )
	{
		resetPc();
		return;
	}

	no = searchCharObjId( id );

	// ID见つけたので抹消
	if( no >= 0 )
	{
		if( charObj[no].ptAct != NULL )
		{
			ext = (CHAREXTRA *)charObj[no].ptAct->pYobi;
			if( ext != NULL )
			{
					if( ext->ptActLeaderMark != NULL )
				{
					DeathAction( ext->ptActLeaderMark );
					ext->ptActLeaderMark = NULL;
				}
				if( ext->ptActMagicEffect != NULL )
				{
					DeathAction( ext->ptActMagicEffect );
					ext->ptActMagicEffect = NULL;
				}
#ifdef PUK2
				if( ext->ptActLevelup != NULL )
				{
					DeathAction( ext->ptActLevelup );
					ext->ptActLevelup = NULL;
				}
				if( ext->ptActInjury != NULL )
				{
					DeathAction( ext->ptActInjury );
					ext->ptActInjury = NULL;
				}
				if( ext->ptPet != NULL )
				{
					DeathAction( ext->ptPet );
					ext->ptPet = NULL;
				}
#endif
#ifdef PUK3_CHANGE_ICONACTIONPOINTER
				if( ext->ptActIcon != NULL ){
					DeathAction( ext->ptActIcon );
					ext->ptActIcon = NULL;
				}
#endif
#ifdef PUK3_NOTFREE_CHAREX
	#ifdef PUK3_PUT_ON
					if ( ext->ptPuton ){
						DeathAction( ext->ptPuton );
						ext->ptPuton = NULL;
					}
	#endif
	#ifdef PUK3_RIDE
					if ( ext->ptRide ){
						DeathAction( ext->ptRide );
						ext->ptRide = NULL;
					}
	#endif
					if ( ext->msg ){
						free( ext->msg );
						ext->msg = NULL;
					}
#else
#ifdef PUK3_PUT_ON
				if ( !ext->ptPuton ){
					DeathAction( ext->ptPuton );
					ext->ptPuton = NULL;
				}
#endif
#ifdef PUK3_RIDE
				if ( !ext->ptRide ){
					DeathAction( ext->ptRide );
					ext->ptRide = NULL;
				}
#endif
#endif
			}
			DeathAction( charObj[no].ptAct );
			charObj[no].ptAct = NULL;
		}
		charObj[no].use = CHAROBJ_USE_FREE;
		charObj[no].status = 0;
#ifdef PUK2
		charObj[no].status2 = 0;
#endif
		charObj[no].stockDir = -1;

		// 登録数更新
		if( maxCharObj > 0 )
			maxCharObj--;
		// 今の空き位置より前の时はこちらをあき位置にする
		if( freeCharObj > no )
			freeCharObj = no;
#if 1
		// 登録されている最后を探す
		for( i = tailCharObj; i > 0; i-- )
		{
			if( charObj[i-1].use != CHAROBJ_USE_FREE )
			{
				tailCharObj = i;
				break;
			}
		}
#endif
	}
}


// キャラ管理テーブル初期化
void initCharObj( void )
{
	int i;

	maxCharObj = 0;
	tailCharObj = 0;
	freeCharObj = 0;
	for( i = 0; i < MAX_CHAROBJ; i++ )
	{
		charObj[i].use = CHAROBJ_USE_FREE;
		charObj[i].ptAct = NULL;
		charObj[i].status = 0;
#ifdef PUK2
		charObj[i].status2 = 0;
#endif
		charObj[i].id = 0;
		charObj[i].stockDir = -1;
		charObj[i].name[0] = '\0';
	}

	searchCharObj = 0;
}


// キャラ管理テーブルリセット
void resetCharObj( void )
{
	int i;
	CHAREXTRA *ext;

	maxCharObj = 0;
	tailCharObj = 0;
	freeCharObj = 0;
	for( i = 0; i < MAX_CHAROBJ; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE )
		{
			if( charObj[i].ptAct != NULL )
			{
				ext = (CHAREXTRA *)charObj[i].ptAct->pYobi;
				if( ext != NULL )
				{
					if( ext->ptActLeaderMark != NULL )
					{
						DeathAction( ext->ptActLeaderMark );
						ext->ptActLeaderMark = NULL;
					}
					if( ext->ptActMagicEffect != NULL )
					{
						DeathAction( ext->ptActMagicEffect );
						ext->ptActMagicEffect = NULL;
					}
#ifdef PUK2
					if( ext->ptActLevelup != NULL )
					{
						DeathAction( ext->ptActLevelup );
						ext->ptActLevelup = NULL;
					}
					if( ext->ptActInjury != NULL )
					{
						DeathAction( ext->ptActInjury );
						ext->ptActInjury = NULL;
					}
					if( ext->ptPet != NULL )
					{
						DeathAction( ext->ptPet );
						ext->ptPet = NULL;
					}
#endif
#ifdef PUK3_CHANGE_ICONACTIONPOINTER
					if( ext->ptActIcon != NULL ){
						DeathAction( ext->ptActIcon );
						ext->ptActIcon = NULL;
					}
#endif
#ifdef PUK3_NOTFREE_CHAREX
	#ifdef PUK3_PUT_ON
					if ( ext->ptPuton ){
						DeathAction( ext->ptPuton );
						ext->ptPuton = NULL;
					}
	#endif
	#ifdef PUK3_RIDE
					if ( ext->ptRide ){
						DeathAction( ext->ptRide );
						ext->ptRide = NULL;
					}
	#endif
					if ( ext->msg ){
						free( ext->msg );
						ext->msg = NULL;
					}
#else
#ifdef PUK3_PUT_ON
					if ( !ext->ptPuton ){
						DeathAction( ext->ptPuton );
						ext->ptPuton = NULL;
					}
#endif
#ifdef PUK3_RIDE
					if ( !ext->ptRide ){
						DeathAction( ext->ptRide );
						ext->ptRide = NULL;
					}
#endif
#endif
				}
				DeathAction( charObj[i].ptAct );
				charObj[i].ptAct = NULL;
			}
			charObj[i].use = CHAROBJ_USE_FREE;
			charObj[i].status = 0;
#ifdef PUK2
			charObj[i].status2 = 0;
#endif
			charObj[i].id = 0;
			charObj[i].stockDir = -1;
		}
	}

	searchCharObj = 0;
}


// キャラ管理テーブルのアクションポインタを抹消する
void clearPtActCharObj( void )
{
	int i;

	for( i = 0; i < MAX_CHAROBJ; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE )
		{
			charObj[i].ptAct = NULL;
			charObj[i].stockDir = -1;
		}
	}
}


// キャラ管理テーブルのアクションポインタを全て复活させる
void restorePtActCharObjAll( void )
{
	int i;
	CHAREXTRA *ext;

	for( i = 0; i < MAX_CHAROBJ; i++ )
	{
		if( charObj[i].use != CHAROBJ_USE_FREE
		 && charObj[i].ptAct == NULL )
		{
			charObj[i].ptAct =
				createCharAction( charObj[i].graNo, charObj[i].gx, charObj[i].gy, charObj[i].dir );
			if( charObj[i].ptAct == NULL )
			{
				continue;
			}

			charObj[i].use = CHAROBJ_USE_VIEW;

			ext = (CHAREXTRA *)charObj[i].ptAct->pYobi;
			ext->charObjTblId = i;

			// NPCのアクション复活
			// 他のPCのアクション复活
			// 伝言板
			if( charObj[i].type == CHAROBJ_TYPE_NPC
			 || charObj[i].type == CHAROBJ_TYPE_USER_NPC )
			{
				charObj[i].ptAct->level = charObj[i].level;
				strcpy( charObj[i].ptAct->name, charObj[i].name );
				charObj[i].ptAct->atr |= charObj[i].charType;
			}
			else
			// Itemのアクション复活
			if( charObj[i].type == CHAROBJ_TYPE_ITEM )
			{
				// 1行インフォに出すためにnameに入れる
				if( strlen( charObj[i].info ) <= ITEM_NAME_LEN )
				{
					strcpy( charObj[i].ptAct->name, charObj[i].info );
				}
				else
				{
					strcpy( charObj[i].ptAct->name, "???" );                         //MLHIDE
				}
				charObj[i].ptAct->atr |= charObj[i].charType;
			}
			else
			// お金のアクション复活
			if( charObj[i].type == CHAROBJ_TYPE_MONEY )
			{
				// 1行インフォに出すためにnameに入れる
				if( strlen( charObj[i].info ) <= CHAR_NAME_LEN )
				{
					strcpy( charObj[i].ptAct->name, charObj[i].info );
				}
				else
				{
					strcpy( charObj[i].ptAct->name, "???" );                         //MLHIDE
				}
				charObj[i].ptAct->atr |= charObj[i].charType;
			}
		}
	}
}


// キャラ管理テーブルのアクションポインタを复活させる
void restorePtActCharObj( int id )
{
	int no;
	CHAREXTRA *ext;

	if( id < 0 )
		return;

	// 登録されているかチェック
	if( (no = searchCharObjId( id )) < 0 )
	{
		// 无かったら終わり
		return;
	}

	if( charObj[no].use != CHAROBJ_USE_FREE
	 && charObj[no].ptAct == NULL )
	{
		charObj[no].ptAct =
			createCharAction( charObj[no].graNo, charObj[no].gx, charObj[no].gy, charObj[no].dir );
		if( charObj[no].ptAct == NULL )
		{
			// アクション作れなかったので終わり
			return;
		}

		charObj[no].use = CHAROBJ_USE_VIEW;

		ext = (CHAREXTRA *)charObj[no].ptAct->pYobi;
		ext->charObjTblId = no;

		charObj[no].ptAct->level = charObj[no].level;
		strcpy( charObj[no].ptAct->name, charObj[no].name );
		strcpy( charObj[no].ptAct->freeName, charObj[no].freeName );
		charObj[no].ptAct->atr |= charObj[no].charType;
	}
}


// キャラ位置更新
void setMovePointCharObj( int id, int nextGx, int nextGy )
{
	int no;

	no = searchCharObjId( id );
	if( no >= 0 )
	{
		stockCharMovePoint( charObj[no].ptAct, nextGx, nextGy );
	}
}




///////////////////////////////////////////////////////////////////////////
// 同一アイテムが同じ座标にたくさん出た时１回しか表示しないための处理

typedef struct
{
	unsigned int bmpNo;
	int gx;
	int gy;
} ITEM_OVERLAP_CHECK;

#define MAX_ITEM_OVERLAP	100
ITEM_OVERLAP_CHECK itemOverlapTbl[MAX_ITEM_OVERLAP];
int itemOverlapCheckCnt;

void initItemOverlapCheck( void )
{
	itemOverlapCheckCnt = 0;
}


// 戾り值： TRUE  ... 同じ物がある
//          FALSE ... ない
BOOL itemOverlapCheck( unsigned int bmpNo, int gx, int gy )
{
	int i;
	BOOL flag = FALSE;

	for( i = 0; i < itemOverlapCheckCnt; i++ )
	{
		if( itemOverlapTbl[i].bmpNo == bmpNo
		 && itemOverlapTbl[i].gx == gx
		 && itemOverlapTbl[i].gy == gy )
		{
			flag = TRUE;
			break;
		}
	}

	if( !flag )
	{
		if( itemOverlapCheckCnt < MAX_ITEM_OVERLAP )
		{
			itemOverlapTbl[itemOverlapCheckCnt].bmpNo = bmpNo;
			itemOverlapTbl[itemOverlapCheckCnt].gx    = gx;
			itemOverlapTbl[itemOverlapCheckCnt].gy    = gy;
			itemOverlapCheckCnt++;
		}
	}

	return flag;
}






///////////////////////////////////////////////////////////////////////////
// ペットメールの演出


// 向きがグリッドの斜め方向なら修正する
void limitCantClientDir( short *dir )
{
	// dirが偶数なら斜めなので奇数にする
	if( ((*dir) % 2) == 0 )
	{
		(*dir)++;
	}
}

// dir值が 0 から 7 になるよう修正する
void ajustClientDir( short *dir )
{
	if( *dir < 0 )
	{
		do
		{
			(*dir) += 8;
		} while( *dir < 0 );
	}
	else
	if( *dir > 7 )
	{
		do
		{
			(*dir) -= 8;
		} while( *dir > 7 );
	}
}


void getPetRoute( ACTION *ptAct )
{
	short dir;
	int i;
	int dx, dy;
	int gx, gy;
	PETEXTRA *pe;

	if( ptAct->bufCount > 0 )
		return;

	// 当たり判定がずれるのでやめる
	if( mapGx != oldMapGx || mapGy != oldMapGy )
		return;

	pe = (PETEXTRA *)ptAct->pYobi;

	dir = pe->moveDir;

	for( i = 0; i < 4; i++ )
	{
		dx = moveAddTbl[dir][0];
		dy = moveAddTbl[dir][1];
		gx = ptAct->gx+dx;
		gy = ptAct->gy+dy;
		if( (gx != pe->preGx || gy != pe->preGy)
		 && checkHitMap( gx, gy ) == FALSE )
		{
			break;
		}
		dir += (2*(i+1));
		ajustClientDir( &dir );
	}

	if( i >= 4 )
	{
		pe->preGx = -1;
		pe->preGy = -1;
		pe->moveDir -= 2;
		ajustClientDir( &pe->moveDir );
		return;
	}

	if( pe->dirCnt >= 2 )
	{
		pe->dirCnt = 0;
		pe->moveDir -= 6;
		ajustClientDir( &pe->moveDir );
	}

	if( dir != pe->preDir )
	{
		pe->dirCnt++;
	}
	else
	{
		pe->dirCnt = 0;
	}

	pe->preDir = dir;

	ptAct->bufGx[ptAct->bufCount] = gx;
	ptAct->bufGy[ptAct->bufCount] = gy;
	ptAct->bufCount++;

	pe->preGx = ptAct->gx;
	pe->preGy = ptAct->gy;

	return;
}


// 直进のみ
void getPetRoute2( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;

	if( ptAct->bufCount > 0 )
		return;

	// 当たり判定がずれるのでやめる
	if( mapGx != oldMapGx || mapGy != oldMapGy )
		return;

	ptAct->bufGx[ptAct->bufCount] = ptAct->gx+moveAddTbl[pe->moveDir][0];
	ptAct->bufGy[ptAct->bufCount] = ptAct->gy+moveAddTbl[pe->moveDir][1];
	ptAct->bufCount++;

	pe->preGx = ptAct->gx;
	pe->preGy = ptAct->gy;

	return;
}


// ＰＣの邻接位置に移动
void getPetRoute3( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	int x = 0, y = 0;

	if( ptAct->bufCount > 0 )
		return;

	// 当たり判定がずれるのでやめる
	if( mapGx != oldMapGx || mapGy != oldMapGy )
		return;

	if( ABS( mapGx - ptAct->gx ) <= 1
	 && ABS( mapGy - ptAct->gy ) <= 1 )
	{
		return;
	}

	if( mapGx - ptAct->gx > 0 )
	{
		x = 1;
	}
	else
	if( mapGx - ptAct->gx < 0 )
	{
		x = -1;
	}

	if( mapGy - ptAct->gy > 0 )
	{
		y = 1;
	}
	else
	if( mapGy - ptAct->gy < 0 )
	{
		y = -1;
	}

	ptAct->bufGx[ptAct->bufCount] = ptAct->gx+x;
	ptAct->bufGy[ptAct->bufCount] = ptAct->gy+y;
	ptAct->bufCount++;

	pe->preGx = ptAct->gx;
	pe->preGy = ptAct->gy;

	return;
}


// 移动处理
BOOL petMoveProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;

	// 范围外に出たらかってに消す
	// 作成から8秒以上たったら消す
	if( ABS( ptAct->gx - mapGx ) >= 13
	 || ABS( ptAct->gy - mapGy ) >= 13
	 || (pe->createTime+8000 < GetTickCount() && ptAct->vx == 0 && ptAct->vy == 0) )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}

#ifdef PUK3_MM_MEMLEAK
	if ( !(pe->flag&PE_FLAG_USE_PTACT1) &&
		 pe->ptAct == NULL && pe->createTime+7200 < GetTickCount() )
	{
		pe->ptAct = createCommmonEffectNoLoop( SPR_difence, ptAct->gx, ptAct->gy,
			0, 0, ptAct->dispPrio, &pe->ptAct );
		pe->flag |= PE_FLAG_USE_PTACT1;
	}
	if( !(pe->flag&PE_FLAG_USE_PTACT2) &&
		 pe->ptAct2 == NULL && pe->createTime+7500 < GetTickCount() )
	{
		pe->ptAct2 = createCommmonEffectNoLoop( SPR_difence, ptAct->gx, ptAct->gy,
			0, 0, ptAct->dispPrio, &pe->ptAct2 );
		pe->flag |= PE_FLAG_USE_PTACT2;
	}
#else
	if( pe->ptAct == NULL && pe->createTime+7200 < GetTickCount() )
	{
		pe->ptAct = createCommmonEffectNoLoop( SPR_difence, ptAct->gx, ptAct->gy,
			0, 0, ptAct->dispPrio );
	}
	if( pe->ptAct2 == NULL && pe->createTime+7500 < GetTickCount() )
	{
		pe->ptAct2 = createCommmonEffectNoLoop( SPR_difence, ptAct->gx, ptAct->gy,
			0, 0, ptAct->dispPrio );
	}
#endif


	if( ptAct->vx == 0 && ptAct->vy == 0 )
	{
		// 移动が終わったら次の位置を检索
		getPetRoute( ptAct );
	}
	charMove( ptAct );

	if( pe->ptAct != NULL )
	{
		pe->ptAct->mx = ptAct->mx;
		pe->ptAct->my = ptAct->my;
	}
	if( pe->ptAct2 != NULL )
	{
		pe->ptAct2->mx = ptAct->mx;
		pe->ptAct2->my = ptAct->my;
	}

	return TRUE;
}


// 到着处理
BOOL uprisePetProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;

	if( pe->ptAct == NULL && pe->createTime < GetTickCount() )
	{
		pe->ptAct = createCommmonEffectNoLoop( SPR_difence, ptAct->gx, ptAct->gy,
			0, 0, ptAct->dispPrio );
	}
	if( pe->ptAct2 == NULL && pe->createTime+500 < GetTickCount() )
	{
		pe->ptAct2 = createCommmonEffectNoLoop( SPR_difence, ptAct->gx, ptAct->gy,
			0, 0, ptAct->dispPrio );
	}

	// 数秒间はペットを消しておく
	if( pe->createTime+1200 < GetTickCount() )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
	}

	return FALSE;
}

// 回転处理（外へ广がる）
BOOL petCircleOutProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;
	float dx, dy;
	float angle;
	int i;

	
	if( pe->ptAct == NULL )
	{
		pe->ptAct = (ACTION *)1;
		pe->angle = 0.0F;
		pe->r = 0;
	//	createCommmonEffectNoLoop( SPR_hoshi, ptAct->gx, ptAct->gy,
	//		0, 0, ptAct->dispPrio );
	}

	if( pe->r > 800 )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}

	if( pe->ptAct != NULL && pe->createTime+1000 < GetTickCount() )
	{
		pe->r += 4;
		pe->angle += 6.0F;
	}

	angle = pe->angle;
	for( i = 0; i < 3; i++ )
	{
		dx = (float)pe->r * CosT( angle );
		dy = (float)pe->r * SinT( angle );

		// 画面表示位置
		camMapToGamen( ptAct->mx+dx, ptAct->my+dy, &mx, &my );
		ptAct->x = (int)(mx+.5);
		ptAct->y = (int)(my+.5);

		// アニメーション处理
		pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );
		
		// 回転前
		if( pe->r == 0 ){
			// オリジナルソート用にストック
#ifdef PUK2
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), 0, &ptAct->bm );
#else
			setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#endif
		}else{
			// フリップカウントが１の时のみ残像表示
			if( FlipCnt == 1 ){
				// オリジナルソート用にストック
				//setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#ifdef PUK2
				StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
				StockDispBuffer2( ptAct->x, ptAct->y, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
			}
		}
		
		angle += 120;
		AdjustDir( &angle );
	}

	return FALSE;
}


// 回転处理（内に狭まる）
BOOL petCircleInProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;
	float dx, dy;
	float angle;
	int i;

	if( pe->ptAct == NULL )
	{
		pe->ptAct  = (ACTION *)1;
		pe->ptAct2 = (ACTION *)NULL;
		pe->angle = 0.0F;
		pe->r = 800;
	}
	else
	{
		if( pe->r <= 0 )
		{
			restorePtActCharObj( pe->id );
			DeathAction( ptAct );
			ptAct = NULL;
			return TRUE;
		}
	}

	if( pe->ptAct != NULL )
	{
	
#if 0
		if( pe->ptAct2 == NULL && pe->createTime+2300 < GetTickCount() )
		{
			pe->ptAct2 = createCommmonEffectNoLoop( SPR_hoshi, ptAct->gx, ptAct->gy,
				0, 0, ptAct->dispPrio );
		}
#endif
		pe->r -= 4;
		pe->angle += 6.0F;

		angle = pe->angle;
		for( i = 0; i < 3; i++ )
		{
			dx = (float)pe->r * CosT( angle );
			dy = (float)pe->r * SinT( angle );

			// 画面表示位置
			camMapToGamen( ptAct->mx+dx, ptAct->my+dy, &mx, &my );
			ptAct->x = (int)(mx+.5);
			ptAct->y = (int)(my+.5);

			// アニメーション处理
			pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

			// フリップカウントが１の时のみ残像表示
			if( FlipCnt == 1 ){
				// オリジナルソート用にストック
				//setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#ifdef PUK2
				StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
				StockDispBuffer2( ptAct->x, ptAct->y, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
			}
			angle += 120;
			AdjustDir( &angle );
		}
	}

	return FALSE;
}


// 点灭で消えていく
BOOL petFlashOutProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;

	if( pe->ptAct == NULL )
	{
		pe->ptAct  = (ACTION *)1;
		pe->drawFlag = 1;
		pe->flipCnt = (FlipCnt ^ 1);
		pe->flashCnt = 20;
		pe->flashWaitCnt = pe->flashCnt/2;
	}

	if( pe->flashCnt <= -20 )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}

	if( FlipCnt != pe->flipCnt )
	{
		pe->flashWaitCnt--;
	}
	if( pe->flashWaitCnt <= 0 )
	{
		pe->drawFlag ^= 1;
		pe->flashWaitCnt = pe->flashCnt/2;
		if( pe->flashWaitCnt < 1 )
		{
			pe->flashWaitCnt = 1;
		}
		pe->flashCnt--;
	}

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

	if( pe->drawFlag )
	{
#ifdef PUK2
		StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
		StockDispBuffer2( ptAct->x, ptAct->y, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
	}

	pe->flipCnt = FlipCnt;

	return FALSE;
}


// 点灭で现れる
BOOL petFlashInProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;

	if( pe->ptAct == NULL )
	{
		pe->ptAct  = (ACTION *)1;
		pe->drawFlag = 1;
		pe->flipCnt = (FlipCnt ^ 1);
		pe->flashCnt = -10;
		pe->flashWaitCnt = 1;
	}

	if( pe->flashCnt > 30 )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}

	if( FlipCnt != pe->flipCnt )
	{
		pe->flashWaitCnt--;
	}
	if( pe->flashWaitCnt <= 0 )
	{
		pe->drawFlag ^= 1;
		pe->flashWaitCnt = pe->flashCnt/2;
		if( pe->flashWaitCnt < 1 )
		{
			pe->flashWaitCnt = 1;
		}
		pe->flashCnt++;
	}

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

	if( pe->drawFlag )
	{
#ifdef PUK2
		StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
		StockDispBuffer2( ptAct->x, ptAct->y, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
	}

	pe->flipCnt = FlipCnt;

	return FALSE;
}


// 点灭移动处理(送信时)
BOOL petFlashMoveOutProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;

	if( pe->ptAct == NULL )
	{
		pe->ptAct  = (ACTION *)1;
		pe->drawFlag = 1;
		pe->flipCnt = (FlipCnt ^ 1);
		pe->flashCnt = 20;
		pe->flashWaitCnt = pe->flashCnt/2;
	}

	// 范围外に出たらかってに消す
	// 作成から6秒以上たったら消す
	if( ABS( ptAct->gx - mapGx ) >= 13
	 || ABS( ptAct->gy - mapGy ) >= 13
	 || (pe->createTime+6000 < GetTickCount() && ptAct->vx == 0 && ptAct->vy == 0) )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}

	if( ptAct->vx == 0 && ptAct->vy == 0 )
	{
		// 移动が終わったら次の位置を检索
		getPetRoute2( ptAct );
	}
	charMove( ptAct );


	if( FlipCnt != pe->flipCnt )
	{
		pe->flashWaitCnt--;
	}
	if( pe->flashWaitCnt <= 0 )
	{
		pe->drawFlag ^= 1;
		pe->flashWaitCnt = pe->flashCnt/2;
		if( pe->flashWaitCnt < 1 )
		{
			pe->flashWaitCnt = 1;
		}
		pe->flashCnt--;
	}

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

	if( pe->drawFlag )
	{
#ifdef PUK2
		StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
		StockDispBuffer2( ptAct->x, ptAct->y, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
	}

	pe->flipCnt = FlipCnt;

	return FALSE;
}


// 点灭移动处理(受信时)
BOOL petFlashMoveInProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;

	if( pe->ptAct == NULL )
	{
		pe->ptAct  = (ACTION *)1;
		pe->drawFlag = 1;
		pe->flipCnt = (FlipCnt ^ 1);
		pe->flashCnt = -10;
		pe->flashWaitCnt = 1;
	}

	// 作成から6秒以上たったら消す
	if( (pe->createTime+6000 < GetTickCount() && ptAct->vx == 0 && ptAct->vy == 0)
	/* || (ABS( ptAct->gx - mapGx ) <= 1 && ABS( ptAct->gy - mapGy ) <= 1)*/ )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}

	if( ptAct->vx == 0 && ptAct->vy == 0
	 && (ABS( ptAct->gx - mapGx ) > 1 || ABS( ptAct->gy - mapGy ) > 1) )
	{
		// 移动が終わったら次の位置を检索
		getPetRoute3( ptAct );
	}
	charMove( ptAct );


	if( FlipCnt != pe->flipCnt )
	{
		pe->flashWaitCnt--;
	}
	if( pe->flashWaitCnt <= 0 )
	{
		pe->drawFlag ^= 1;
		pe->flashWaitCnt = pe->flashCnt/2;
		if( pe->flashWaitCnt < 1 )
		{
			pe->flashWaitCnt = 1;
		}
		pe->flashCnt++;
	}

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

	if( pe->drawFlag )
	{
#ifdef PUK2
		StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
		StockDispBuffer2( ptAct->x, ptAct->y, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
	}

	pe->flipCnt = FlipCnt;

	return FALSE;
}


// 残像法物线(送信时)
BOOL petParabolaMoveOutProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;
	int i;

	if( pe->ptAct == NULL )
	{
		pe->ptAct = (ACTION *)1;
		pe->walkCnt = 0;
	//	ptAct->vx = (ptAct->mx - (mapGx*GRID_SIZE))/30;
	//	ptAct->vy = (ptAct->my - (mapGy*GRID_SIZE))/30;
	//	ptAct->mx = (float)mapGx*GRID_SIZE;
	//	ptAct->my = (float)mapGy*GRID_SIZE;
	
		ptAct->vx = 0;
		ptAct->vy = 0;
	//	ptAct->mx = (float)mapGx*GRID_SIZE;
	//	ptAct->my = (float)mapGy*GRID_SIZE;
	
		pe->h = 0;
		pe->th = -15;
		for( i = 0; i < 10; i++ )
		{
			pe->bmpNo[i] = -1;
		}
	}

	if( pe->walkCnt >= 40 )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}
	else
	if( pe->walkCnt >= 30 )
	{
		pe->h = 0;
	}
	else
	{
		ptAct->mx += ptAct->vx;
		ptAct->my += ptAct->vy;

		pe->h += pe->th;
		pe->th++;
	}


	pe->walkCnt++;

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

	if( (ptAct->atr & ACT_ATR_HIDE) == 0 )
	{
#ifdef PUK2
		StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+pe->h+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
		StockDispBuffer2( ptAct->x, ptAct->y+pe->h, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
	}

	// フリップカウントが１の时のみ残像表示
	if( FlipCnt == 1 ){
		for( i = 0; i < 10; i++ )
		{
			if( pe->bmpNo[i] >= 0 )
			{
#ifdef PUK2
				StockDispBuffer2( pe->x[i]+Blt_adjust(ptAct,0), pe->y[i]+Blt_adjust(ptAct,1), DISP_PRIO_CHAR+1, pe->bmpNo[i], 0, 0 );
#else
				StockDispBuffer2( pe->x[i], pe->y[i], DISP_PRIO_CHAR+1, pe->bmpNo[i], 0 );
#endif
			}
		}
	}

	for( i = 9; i > 0; i-- )
	{
		pe->bmpNo[i] = pe->bmpNo[i-1];
		pe->x[i] = pe->x[i-1];
		pe->y[i] = pe->y[i-1];
		pe->mx[i] = pe->mx[i-1];
		pe->my[i] = pe->my[i-1];
	}

	if( pe->walkCnt <= 30 )
	{
		pe->bmpNo[0] = ptAct->bmpNo;
		pe->x[0] = ptAct->x;
		pe->y[0] = ptAct->y+pe->h;
		pe->mx[0] = ptAct->mx;
		pe->my[0] = ptAct->my;
	}
	else
	{
		pe->bmpNo[0] = -1;
	}

	return FALSE;
}


// 残像法物线(受信时)
BOOL petParabolaMoveInProc( ACTION *ptAct )
{
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;
	float mx, my;
	int i;

	if( pe->ptAct == NULL )
	{
		pe->ptAct = (ACTION *)1;
		pe->walkCnt = 0;
	//	ptAct->vx = ((mapGx*GRID_SIZE) - ptAct->mx)/30;
	//	ptAct->vy = ((mapGy*GRID_SIZE) - ptAct->my)/30;
	//	ptAct->vx = ((ptAct->nextGx*GRID_SIZE) - ptAct->mx)/30;
	//	ptAct->vy = ((ptAct->nextGx*GRID_SIZE) - ptAct->my)/30;
		ptAct->vx = 0;
		ptAct->vy = 0;
		pe->h = 0;
		pe->th = -15;
		for( i = 0; i < 10; i++ )
		{
			pe->bmpNo[i] = -1;
		}
	}

	if( pe->walkCnt >= 40 )
	{
		restorePtActCharObj( pe->id );
		DeathAction( ptAct );
		ptAct = NULL;
		return FALSE;
	}
	else
	if( pe->walkCnt >= 30 )
	{
		pe->h = 0;
	}
	else
	{
		ptAct->mx += ptAct->vx;
		ptAct->my += ptAct->vy;

		pe->h += pe->th;
		pe->th++;
	}


	pe->walkCnt++;

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP );

	if( (ptAct->atr & ACT_ATR_HIDE) == 0 )
	{
#ifdef PUK2
		StockDispBuffer2( ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,0)+pe->h, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0, &ptAct->bm );
#else
		StockDispBuffer2( ptAct->x, ptAct->y+pe->h, DISP_PRIO_CHAR+1, ptAct->bmpNo, 0 );
#endif
	}

	// フリップカウントが１の时のみ残像表示
	if( FlipCnt == 1 ){
		for( i = 0; i < 10; i++ )
		{
			if( pe->bmpNo[i] >= 0 )
			{
#ifdef PUK2
				StockDispBuffer2( pe->x[i]+Blt_adjust(ptAct,0), pe->y[i]+Blt_adjust(ptAct,0), DISP_PRIO_CHAR+1, pe->bmpNo[i], 0, 0 );
#else
				StockDispBuffer2( pe->x[i], pe->y[i], DISP_PRIO_CHAR+1, pe->bmpNo[i], 0 );
#endif
			}
		}
	}

	for( i = 9; i > 0; i-- )
	{
		pe->bmpNo[i] = pe->bmpNo[i-1];
		pe->x[i] = pe->x[i-1];
		pe->y[i] = pe->y[i-1];
		pe->mx[i] = pe->mx[i-1];
		pe->my[i] = pe->my[i-1];
	}

	if( pe->walkCnt <= 30 )
	{
		pe->bmpNo[0] = ptAct->bmpNo;
		pe->x[0] = ptAct->x;
		pe->y[0] = ptAct->y+pe->h;
		pe->mx[0] = ptAct->mx;
		pe->my[0] = ptAct->my;
	}
	else
	{
		pe->bmpNo[0] = -1;
	}

	return FALSE;
}


// ペットアクションのデフォルト
void petProc( ACTION *ptAct )
{
	float mx, my;
	int animLoop;
	PETEXTRA *pe = (PETEXTRA *)ptAct->pYobi;

	switch( pe->mode )
	{
		// メール送信时：步くー＞消える
		case 0:
			if( !petMoveProc( ptAct ) )
			{
				return;
			}
			break;

		// メール着信时：
		// ペットが戾って来たとき：出现
		case 1:
			if( !uprisePetProc( ptAct ) )
			{
				return;
			}
			break;

		// メール送信时：分身回転ー＞消える
		case 2:
			if( !petCircleOutProc( ptAct ) )
			{
				return;
			}
			break;

		// メール受信时：分身回転ー＞现れる
		case 3:
			if( !petCircleInProc( ptAct ) )
			{
				return;
			}
			break;

		// メール送信时：点灭－＞消える
		case 4:
			if( !petFlashOutProc( ptAct ) )
			{
				return;
			}
			break;

		// メール受信时：点灭－＞实态化
		case 5:
			if( !petFlashInProc( ptAct ) )
			{
				return;
			}
			break;

		// メール送信时：点灭移动－＞消える
		case 6:
			if( !petFlashMoveOutProc( ptAct ) )
			{
				return;
			}
			break;

		// メール受信时：点灭移动－＞实态化
		case 7:
			if( !petFlashMoveInProc( ptAct ) )
			{
				return;
			}
			break;

		// メール送信时：残像法物线－＞消える
		case 8:
			if( !petParabolaMoveOutProc( ptAct ) )
			{
				return;
			}
			break;

		// メール送信时：残像法物线－＞消える
		case 9:
			if( !petParabolaMoveInProc( ptAct ) )
			{
				return;
			}
			break;
	}

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	// アニメーション处理
	if( ptAct->anim_no == ANIM_HAND
	 || ptAct->anim_no == ANIM_HAPPY
	 || ptAct->anim_no == ANIM_ANGRY
	 || ptAct->anim_no == ANIM_SAD
	 || ptAct->anim_no == ANIM_WALK
	 || ptAct->anim_no == ANIM_STAND
	 || ptAct->anim_no == ANIM_NOD )
	{
		animLoop = ANM_LOOP;
	}
	else
	{
		animLoop = ANM_NO_LOOP;
	}
	pattern( ptAct, ANM_NOMAL_SPD, animLoop );

	// オリジナルソート用にストック
	if( (ptAct->atr & ACT_ATR_HIDE) == 0 )
	{
#ifdef PUK2
		setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x+Blt_adjust(ptAct,0), ptAct->y+Blt_adjust(ptAct,1), 0, &ptAct->bm );
#else
		setMapChar( ptAct->bmpNo, ptAct->mx, ptAct->my, ptAct->x, ptAct->y, 0 );
#endif
	}
}

// ペットのアクション作成
ACTION *createPetAction( int graNo, int gx, int gy, int dir, int mode, int moveDir, int id )
{
	ACTION *ptAct;
	float mx, my;
	PETEXTRA *pe;

	/* アクションリストに登録 */
#ifdef PUK2_MEMCHECK
	ptAct = GetAction( PRIO_CHR, sizeof( PETEXTRA ), ACT_T_PETEXTRA );
#else
	ptAct = GetAction( PRIO_CHR, sizeof( PETEXTRA ) );
#endif
	if( ptAct == NULL )
		return NULL;

	// 实行关数
	ptAct->func = petProc;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	ptAct->anim_ang = dir;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_CHAR;
#ifdef PUK2
	// 1行インフォ表示??ペットメールフラグ
	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2 | ACT_ATR_TYPE_MONSTERMAIL;
#else
	// 1行インフォ表示フラグ
	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
#endif
	// 初期位置
	ptAct->nextGx = gx;					// マップグリッド座标（移动先）
	ptAct->nextGy = gy;
	ptAct->bufCount = 0;
	ptAct->gx = gx;						// マップグリッド座标（现在地）
	ptAct->gy = gy;
	ptAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	ptAct->my = (float)gy * GRID_SIZE;
	ptAct->vx = 0;						// 移动增分
	ptAct->vy = 0;
	
	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	pe = (PETEXTRA *)ptAct->pYobi;
	pe->mode = mode;
	pe->moveDir = moveDir;
	limitCantClientDir( &pe->moveDir );
	pe->preDir = pe->moveDir;
	pe->dirCnt = 0;
	pe->preGx = gx;
	pe->preGy = gy;
	pe->walkCnt = 0;
	pe->createTime = GetTickCount();
	pe->ptAct  = NULL;
	pe->ptAct2 = NULL;
	pe->angle = 0.0F;
	pe->r = 0;
#ifdef PUK3_MM_MEMLEAK
	pe->flag = 0;
#endif

	pe->id = id;

	return ptAct;
}


//＝＝＝＝＝＝＝＝＝＝　アニメーションデータ　＝＝＝＝＝＝＝＝＝＝

/*----------　船エフェクト　----------*/
#define C	232532
#define T	24
static int anm00_000[]={		/*停泊中船底*/
		T,C+0,
		T,C+1,
		T,C+2,
		T,C+3,
		T,C+4,
		T,C+5,
		T,C+6,
		T,C+7,
		T,C+8,
		T,C+9,
		T,C+10,
		T,C+11,
		T,C+12,
		T,C+13,
		T,C+14,
		T,C+15,
		-2,0,
};

#undef T
#define T	16
static int anm00_001[]={		/*移动中船底*/
		T,C+0,
		T,C+1,
		T,C+2,
		T,C+3,
		T,C+4,
		T,C+5,
		T,C+6,
		T,C+7,
		T,C+8,
		T,C+9,
		T,C+10,
		T,C+11,
		T,C+12,
		T,C+13,
		T,C+14,
		T,C+15,
		-2,0,
};

static int *anm00_tbl[]={
		anm00_000,anm00_001,
};

/*----------　かもめエフェクト　----------*/
#undef C
#undef T
#define C	232508
#define T	8
static int anm01_000[]={		/*左下移动*/
		T,C+0,
		T,C+1,
		T,C+2,
		T,C+3,
		4,C+4,
		4,C+5,
		-2,0,
};

#undef C
#define C	232520
static int anm01_001[]={		/*右上移动*/
		T,C+0,
		T,C+1,
		T,C+2,
		T,C+3,
		4,C+4,
		4,C+5,
		-2,0,
};

#undef C
#define C	232514
static int anm01_002[]={		/*右下移动*/
		T,C+0,
		T,C+1,
		T,C+2,
		T,C+3,
		4,C+4,
		4,C+5,
		-2,0,
};

#undef C
#define C	232526
static int anm01_003[]={		/*左上移动*/
		T,C+0,
		T,C+1,
		T,C+2,
		T,C+3,
		4,C+4,
		4,C+5,
		-2,0,
};

/*----------　シャドーエフェクト　----------*/
#undef C
#undef T
#define C	227334
#define T	8
static int anm01_004[]={		/*左下移动*/
		240,C+2,
		T,C+1,
		30,C+0,
		T,C+1,
		-2,0,
};

static int *anm01_tbl[]={
		anm01_000,anm01_001,anm01_002,anm01_003,anm01_004,
};

//アニメーションテーブル
static int **anm_tbl[]={
		anm00_tbl,		//船エフェクト
		anm01_tbl,		//かもめ、シャドー
};
/*----------　アニメーション处理　----------*/
static int old_pattern( ACTION *a0, int d0 )
{
	int d1;
	int *a1;
	U4 BmpNo;
	short dx,dy;

	d1 = a0->anim_chr_no;		/*新しいアニメーションか*/
	if( a0->anim_chr_no_bak != d1 ){
		a0->anim_chr_no_bak = a0->anim_chr_no;		/*新しいアニメーションだ*/
		a0->anim_frame_cnt = 0;		/*アニメーションの时间クリア*/
		a0->anim_cnt = 0;		/*パターンのクリア*/
	}
	if( a0->anim_frame_cnt ){		/*パターン变更の时间か*/
		a0->anim_frame_cnt--;
		return 0;		/*アニメーションは続く*/
	}

	a1 = anm_tbl[ d0 ][ a0->anim_chr_no ] + (a0->anim_cnt * 2);
	if( a1[0] == -1 ){		/*アニメーション終わりか*/
		return 1;		/*アニメーション終了*/
	}
	if( a1[0] == -2 ){		/*アニメーションループか*/
		a0->anim_cnt = (int)(a1[1]);
		a1 = anm_tbl[ d0 ][ a0->anim_chr_no ] + (a0->anim_cnt * 2);
	}
	a0->anim_frame_cnt = (int)(a1[0]);		/*アニメーションの时间クリア*/
	//シャドーなら
	if( d0 == 1 && a0->anim_chr_no == 4 ){
		// グラフィックの番号
		BmpNo = a1[1];
	} else {
		// グラフィックの番号
		realGetNo( a1[1], (U4 *)&BmpNo );
	}
	realGetPos( BmpNo, &dx, &dy );
	// ＢＭＰ番号セット
	a0->bmpNo = BmpNo;
	a0->anim_x = dx;		// Ｘ座标OFFセットセット
	a0->anim_y = dy;		// Ｙ座标OFFセットセット
	//カウンター更新
	a0->anim_cnt++;
	a0->anim_frame_cnt--;
	return 0;		/*アニメーションは続く*/
}

/*-------------------------------------------
	その方向に移动する
--------------------------------------------*/
static void gemini2( ACTION *p )
{
	float d0;

	d0 = 270 - p->dir;
	AdjustDir( &d0 );
	// 移动
	p->fx -= CosT( d0 ) * p->speed;
	p->fy += SinT( d0 ) * p->speed;
}

extern int raster_tbl[];
extern int raster_point;
extern int raster_bend;
/*-------------------------------------------
	ラスターポジションセット处理
--------------------------------------------*/
static void set_raster_pos(ACTION *a0)
{
	int d0;

	d0 = a0->y >> 3;
	d0 += raster_point;
	d0 &= 63;
	a0->y -= raster_tbl[d0] * raster_bend / 100;
}

/*-------------------------------------------
	海マップかどうかのチェック
--------------------------------------------*/
BOOL check_sea_map( void )
{
	int ii;

	//船マップなら
	if( mapNo >= 41000 && mapNo <= 41999 ){
		return TRUE;
	}
	//港マップなら
	if( mapNo >= 40000 && mapNo <= 40999 ){
		return TRUE;
	}
	for( ii = 0; ii < sizeof(sea_map_tbl) / sizeof(sea_map_tbl[0]); ii++ ){
		//海マップなら
		if( sea_map_tbl[ ii ] == mapNo ){
			return TRUE;
		}
	}
	return FALSE;
}

/*-------------------------------------------
	かもめマップかどうかのチェック
--------------------------------------------*/
static BOOL check_bird_map( void )
{
	int ii;

	//港マップなら
	if( mapNo >= 40000 && mapNo <= 40999 ){
		return TRUE;
	}
	for( ii = 0; ii < sizeof(bird_map_tbl) / sizeof(bird_map_tbl[0]); ii++ ){
		//海マップなら
		if( bird_map_tbl[ ii ] == mapNo ){
			return TRUE;
		}
	}
	return FALSE;
}

static void ship( ACTION *p );
static void ship_wave( ACTION *p );
/*-------------------------------------------
	船タスクがいるかどうか
--------------------------------------------*/
static ACTION *check_ship_task( void *name )
{
	/* 先头のリストポインタ取得 */
	ACTION *pActLoop = pActTop->pNext;
	// 处理ループ
	while( 1 ){
		/* 最后尾が来るまでループ */
		if( pActLoop == pActBtm ){
			 break;
		}
		/* このタスクなら */
		if( pActLoop->func == name ){
			/* 生きていたら */
			if( pActLoop->deathFlag == FALSE ){
				return pActLoop;
			}
		}
		/* 次のポインタをセット */
		pActLoop = pActLoop->pNext;
	}
	return NULL;
}
#ifdef PUK3_WHALE_SHIP
	#include "../puk3/character/WhaleShip.cpp"
#endif

#define GRAPHIC_SHIP	230058
//今回のプレイヤースクロール取り出し
extern void get_scroll_pos( int *x, int *y );
/*-------------------------------------------
	かもめ移动处理
--------------------------------------------*/
static void move_bird( ACTION *p )
{
	float mx, my;

	//かもめマップでなければ
//	if( check_bird_map() == FALSE ){
	//海マップでないかフロアが变わったなら
	if( check_sea_map() == FALSE || p->gy != mapNo ){
		//抹杀
		DeathAction( p );
		return;
	}

	//移动
	gemini2( p );
	//古いアニメーション
	old_pattern( p, 1 );
	// 画面表示位置
	camMapToGamen( p->mx, p->my, &mx, &my );
	p->x = (int)(mx+.5) + (int)(p->fx+.5);
	p->y = (int)(my+.5) + (int)(p->fy+.5);
	//シャドーなら
	if( p->anim_chr_no == 4 ){
		//ラスターポジションセットへ
		set_raster_pos( p );
	}
	//画面外チェック
	switch( p->anim_chr_no & 3 ){
	//左下移动
	case 0:
		//画面外なら
		if( p->x < 0 - 150 || p->y > 480 + 150 ){
			//抹杀
			DeathAction( p );
		}
		break;

	//右上移动
	case 1:
		//画面外なら
		if( p->x > 640 + 150 || p->y < 0 - 150 ){
			//抹杀
			DeathAction( p );
		}
		break;

	//右下移动
	case 2:
		//画面外なら
		if( p->x > 640 + 150 || p->y > 480 + 150 ){
			//抹杀
			DeathAction( p );
		}
		break;

	//左上移动
	case 3:
		//画面外なら
		if( p->x < 0 - 150 || p->y < 0 - 150 ){
			//抹杀
			DeathAction( p );
		}
		break;

	}
}

// ラスターポインター更新/////////////////////////////////////////////////////////////////
extern void update_raster_pointer( void );
static unsigned char bird_timer = 0;
/*-------------------------------------------
	かもめ作成处理
--------------------------------------------*/
void create_bird( void )
{
	ACTION *p;
	float spd;
	int pos;
	char shadow_flg = 0;

	//プレイヤーがいないなら
	if( pc.ptAct == NULL ){
		return;
	}
#ifdef PUK3_WHALE_SHIP
	create_WhaleShip_Master();
#endif

	//ラスターポインター更新
	update_raster_pointer();

	//かもめマップでなければ
	if( check_bird_map() == FALSE ){
		//シャドー出现の时间（夜）でなければ
//		if( nrTimeZoneNo != 2 ){
//			return;
//		}
		//シャドー出现
//		shadow_flg = 1;
		return;
	}

	bird_timer++;
	if( bird_timer & 63 ){
		return;
	}

	/* アクションリストに登録 */
	p = GetAction( PRIO_CHR + 1, NULL );
	//作成失败なら
	if( p == NULL ){
		return;
	}
	// 实行关数
	p->func = move_bird;
	// 表示优先度
	p->dispPrio = DISP_PRIO_JIKI;
	//フロア番号保存
	p->gy = mapNo;
	//动作番号セット
//	p->anim_chr_no = Rnd( 0, 3 );
	//上下の时间なら
	if( nrTimeZoneNo & 1 ){
		//上下に移动
		p->anim_chr_no = Rnd( 0, 1 );
	} else {
		//左右に移动
		p->anim_chr_no = Rnd( 2, 3 );
	}
	//移动速度セット
	spd = (float)Rnd( 0, 200 );
	spd /= 100;
	p->speed = 2 + spd;
	//シャドーなら
	if( shadow_flg ){
		// 表示优先度
		p->dispPrio = DISP_PRIO_BG - 1;
		//スピード落とす
		p->speed /= 4;
		//シャドー左下移动
		p->anim_chr_no = 4;
	}
	//初期位置ずれ
	pos = Rnd( 0, 2048 ) - 1024;
	switch( p->anim_chr_no & 3 ){
	//左下移动
	case 0:
		//シャドーなら
		if( shadow_flg ){
			//移动方向セット
			p->dir = (float)(225 + 8);
		} else {
			//移动方向セット
			p->dir = (float)(225 + 8 + Rnd( 0, 40 ) - 20);
		}
		//初期位置セット
		p->mx = pc.ptAct->mx + 400 + 150;
		p->my = pc.ptAct->my + pos;
		break;

	//右上移动
	case 1:
		//移动方向セット
		p->dir = (float)(45 + 8 + Rnd( 0, 40 ) - 20);
		//初期位置セット
		p->mx = pc.ptAct->mx - 400 - 150;
		p->my = pc.ptAct->my + pos;
		break;

	//右下移动
	case 2:
		//移动方向セット
		p->dir = (float)(135 - 8 + Rnd( 0, 40 ) - 20);
		//初期位置セット
		p->mx = pc.ptAct->mx + pos;
		p->my = pc.ptAct->my - 400 - 150;
		break;

	//左上移动
	case 3:
		//移动方向セット
		p->dir = (float)(315 - 8 + Rnd( 0, 40 ) - 20);
		//初期位置セット
		p->mx = pc.ptAct->mx + pos;
		p->my = pc.ptAct->my + 400 + 150;
		break;

	}
}

/*-------------------------------------------
	船底の波处理
--------------------------------------------*/
static void ship_wave( ACTION *p )
{
	ACTION *p_ship;

	//船がいないなら
	if( ( p_ship = check_ship_task( ship ) ) == NULL ){
		//船底の波抹杀
		DeathAction( p );
		return;
	}
	//海マップでなければ
	if( check_sea_map() == FALSE ){
		//抹杀
		DeathAction( p );
		return;
	}

	//船停止中なら
	if( p_ship->anim_no == 1 ){
		//停止中アニメセット
		p->anim_chr_no = 0;
	} else {
		//移动中アニメセット
		p->anim_chr_no = 1;
	}
	//古いアニメーション
	old_pattern( p, 0 );
	//船の座标コピー
	p->x = p_ship->x;
	p->y = p_ship->y;
}

/*-------------------------------------------
	船メイン
--------------------------------------------*/
static void ship( ACTION *p )
{
	float mx, my;

	//海マップでないかフロアが变わったかプレイヤーがいないなら
	if( check_sea_map() == FALSE || p->gy != mapNo || pc.ptAct == NULL ){
		//抹杀
		DeathAction( p );
		return;
	}

	//移动
	switch( p->anim_no ){
	//入港
	case 1:
		p->mx -= 0.75;
		p->my += 0.75;
		//到着なら
		if( p->nextGx >= (int)p->mx && p->nextGy <= (int)p->my ){
			p->mx = (float)p->nextGx;
			p->my = (float)p->nextGy;
			//停止へ
			p->anim_no++;
		}
		break;
	//停止
	case 2:
		break;
	//出港
	case 3:
		p->mx -= 0.5;
		p->my -= 0.5;
#if 0
		//到着なら
		if( p->nextGx == (int)p->mx && p->nextGy == (int)p->my ){
			//船抹杀
			DeathAction( p );
			return;
		}
#endif
		break;
	}
#if 0
	//上下に摇らす
	//上升中なら
	if( p->dir == 0 ){
		//スピードダウン中なら
		if( p->delta == 0 ){
			p->speed -= 0.005f;
			//方向転换なら
			if( p->speed <= 0.0f ){
				p->dir = 180;
				p->delta = 1;
			}
		} else {
			p->speed += 0.005f;
			//スピードダウンなら
			if( p->speed >= 0.50f ){
				p->delta = 0;
			}
		}
	} else {
		//スピードダウン中なら
		if( p->delta == 0.0f ){
			p->speed -= 0.005f;
			//方向転换なら
			if( p->speed <= 0 ){
				p->dir = 0;
				p->delta = 1;
			}
		} else {
			p->speed += 0.005f;
			//スピードダウンなら
			if( p->speed >= 0.50f ){
				p->delta = 0;
			}
		}
	}
	gemini2( p );
#endif
	// 画面表示位置
	camMapToGamen( p->mx, p->my, &mx, &my );
	p->x = (int)(mx+.5) + (int)(p->fx+.5);
	p->y = (int)(my+.5) + (int)(p->fy+.5);
	//新规作成なら
	if( p->gx == 0 ){
		p->gx = 1;
	} else {
		//ラスターポジションセットへ
		set_raster_pos( p );
	}

}

/*-------------------------------------------
	船底の波のアクション作成
--------------------------------------------*/
static void create_ship_wave( void )
{
	ACTION *p;
	ACTION *p_ship;

	//船底の波アクションあるなら
	if( check_ship_task( ship_wave ) ){
		return;
	}
	//船のアクション无いなら
	if( ( p_ship = check_ship_task( ship ) ) == NULL ){
		return;
	}
	//船底の波アクション无いなら
//	if( !p_ship_wave ){
		/* アクションリストに登録 */
		p = GetAction( p_ship->prio + 1, NULL );
		//作成失败なら
		if( p == NULL ){
			return;
		}
//	}

	// 实行关数
	p->func = ship_wave;
	// 表示优先度
	p->dispPrio = p_ship->dispPrio + 1;
}

/*-------------------------------------------
	船のアクション作成
--------------------------------------------*/
void create_ship( int kind, int map, int floor, int gx, int gy )
{
//	float mx, my;
	U4 BmpNo;
	short dx,dy;
	int create_flg = 0;
	ACTION *p;

#ifdef PUK3_WHALE_SHIP
	if ( kind >= 100 ){
		create_whaleship( kind % 100, map, floor, gx, gy );
		return;
	}
	if ( kind < 0 ){
		create_whalepier( kind, map, floor, gx, gy );
		return;
	}

	// 航行时间が送られてきたなら
	if ( kind == 0 ) return;
#endif
	//画面外消すなら
	if( kind == 4 ){
		//船がいるなら
		if( p = check_ship_task( ship ) ){
			//船抹杀
			DeathAction( p );
			return;
		}
	}

	//船のアクション无いなら
	if( ( p = check_ship_task( ship ) ) == NULL ){
		create_flg = 1;
		/* アクションリストに登録 */
		p = GetAction( PRIO_CHR + 1, NULL );
		//作成失败なら
		if( p == NULL ){
			return;
		}
	}

	// 实行关数
	p->func = ship;
	// グラフィックの番号
	realGetNo( GRAPHIC_SHIP, (U4 *)&BmpNo );
	realGetPos( BmpNo, &dx, &dy );
	p->bmpNo = BmpNo;		// ＢＭＰ番号セット
	p->anim_x = dx;		// Ｘ座标OFFセットセット
	p->anim_y = dy;		// Ｙ座标OFFセットセット
	//作成されたなら
	if( create_flg ){
		p->fx = 0;
		p->fy = 0;
		p->speed = 0.25f;
		p->dir = 0;
		p->delta = 0;
	}

	// 动作番号
	p->anim_no = kind;
	// アニメーション向き( ０～７ )( 下が０で右回り )
//	p->anim_ang = dir;
	// 表示优先度
	p->dispPrio = DISP_PRIO_BG;
	// 1行インフォ表示フラグ
//	p->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
	//新规作成フラグ
	p->gx = 0;
	//フロア番号保存
	p->gy = floor;
	//动作别设定
	switch( kind ){
	//入港
	case 1:
		// マップグリッド座标（移动先）
		p->nextGx = gx * GRID_SIZE;
		p->nextGy = gy * GRID_SIZE;
//		p->bufCount = 0;
		// マップグリッド座标（现在地）
		gx += 10;
		gy -= 10;
		// マップ座标
		p->mx = (float)gx * GRID_SIZE;
		p->my = (float)gy * GRID_SIZE;
		// 移动增分
//		p->vx = 0;
//		p->vy = 0;
		break;
	//停止
	case 2:
		// マップ座标
		p->mx = (float)gx * GRID_SIZE;
		p->my = (float)gy * GRID_SIZE;
		break;
	//出港
	case 3:
		// マップグリッド座标（移动先）
		p->nextGx = (gx - 10) * GRID_SIZE;
		p->nextGy = (gy - 10) * GRID_SIZE;
		// マップ座标
		p->mx = (float)gx * GRID_SIZE;
		p->my = (float)gy * GRID_SIZE;
		break;
	//エラー
	default:
		//船抹杀
		DeathAction( p );
		p = NULL;
		return;
	}
	// 画面表示位置
//	camMapToGamen( p->mx, p->my, &mx, &my );
//	p->x = (int)(mx+.5);
//	p->y = (int)(my+.5);

	// 船底の波のアクション作成
	create_ship_wave();

}

