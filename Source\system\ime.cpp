﻿/************************/
/*	ime.cpp				*/
/************************/
#include "../systeminc/system.h"
#include <winnls32.h>
#include "../systeminc/ime_sa.h"
#include "../systeminc/menu.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/main.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/directDraw.h"


// ＩＭＥ入力モード文字列
static char *ImeModeStr[] = { 
								"        /abc","","",                                         //MLHIDE
								"        /输入法1","","","","",                                  //MLHIDE
								"        /输入法2",                                              //MLHIDE
								"        /输入法3","",                                           //MLHIDE
								"        /输入法4","","","","",                                  //MLHIDE
								"        /输入法5","","",                                        //MLHIDE
								"        /输入法6","","",                                        //MLHIDE
								"        /输入法7","","",                                        //MLHIDE
								"        /输入法8","","",                                        //MLHIDE
								"        /输入法9","","",                                        //MLHIDE
								};
// ＩＭＥ入力モード文字列（Ｃａｐｓ用）
static char *ImeModeStrCaps[] = { 
								"        /ABC","","",                                         //MLHIDE
								"        /输入法11","","","","",                                 //MLHIDE
								"        /输入法12",                                             //MLHIDE
								"        /输入法13","",                                          //MLHIDE
								"        /输入法14","","","","",                                 //MLHIDE
								"        /输入法15","","",                                       //MLHIDE
								"        /输入法16","","",                                       //MLHIDE
								"        /输入法17","","",                                       //MLHIDE
								"        /输入法18","","",                                       //MLHIDE
								"        /输入法19","","",                                       //MLHIDE
								};

char ImeBufferBak2;

int imeModeStrPosX = DEF_APPSIZEX-98;
int imeModeStrPosY = DEF_APPSIZEY-19;

// ＩＭＥ构造体
IME_INFO ImeInfo;

// ＩＭＥ关连の初期化 *********************************************************/
void InitIme( void )
{
	// バッファー初期化
	ImeInfo.buffer[ 0 ] = NULL;
	ImeInfo.block[ 0 ] = NULL;
	ImeBufferBak2 = NULL;
	
	// 以前にＩＭＥがあったら消す。
	if( ImeInfo.hImc != NULL ){
		// ＩＭＥコンテキスト开放
		EndIme();
	}
	//ImeInfo.hImc = ImmGetContext( hWnd );
	
	// ＩＭＥコンテキストの取得
	ImeInfo.hImc = ImmCreateContext();
	
#ifdef _DEBUG
	// 失败した时
	if( ( ImeInfo.hImc = ImmCreateContext() ) == NULL ){
		MessageBox( hWnd, "IME Init failed.", "OK", MB_OK | MB_ICONSTOP );  //MLHIDE
	}
#endif
	
	// ＩＭＥをこのウィンドウに关连付ける
	ImeInfo.hImcBegin = ImmAssociateContext( hWnd , ImeInfo.hImc );
	
	// ＩＭＥ入力モードの取得
	ImmGetConversionStatus(  ImeInfo.hImc, &ImeInfo.conversion, &ImeInfo.sentence  );
	
	// ＩＭＥウィンドウ消す
    //WINNLSEnableIME( hWnd, FALSE );
   	//WINNLSEnableIME( NULL, TRUE );

}

// ＩＭＥの終了 ***************************************************************/
void EndIme( void )
{
	// ＩＭＥコンテキスト开放
	ImmReleaseContext( hWnd, ImeInfo.hImc );
	ImmAssociateContext( hWnd, ImeInfo.hImcBegin );
	// ＩＭＥウィンドウ消す
    //WINNLSEnableIME( NULL, FALSE );
#ifdef PUK3_NOTFREE_IME
	// 候补リスト解放
	if( ImeInfo.candList != NULL ){ free( ImeInfo.candList );	ImeInfo.candList = NULL; }
#endif
}

//*****************************************************************************/
// ＩＭＥのＯＮ、ＯＦＦ状态の确认 
// 戾り值　	ＯＮ　：TRUE
// 			ＯＦＦ：FALSE
//*****************************************************************************/
BOOL ImeOpenState( void )
{
    return ImmGetOpenStatus( ImeInfo.hImc );
}
//*****************************************************************************/
// ＷＩＮＤＯＷＳのＩＭＥバッファーをクリアする（ＯＦＦにしてＯＮにするだけ）
//*****************************************************************************/
void ClrImeCompositionString( void )
{
	// ＩＭＥがＯＮの时
    if( ImeOpenState() == TRUE ){
		// ＩＭＥＯＦＦにする
    	ImmSetOpenStatus( ImeInfo.hImc, FALSE );
		// ＩＭＥＯＮにする
    	ImmSetOpenStatus( ImeInfo.hImc, TRUE );
		// ＯＦＦにしてＯＮにするだけ
	}
}

// ＩＭＥ关连处理 *************************************************************/
void ImeProc( void )
{
	
	//int strWidth;	// 文字列の横幅（ドット数）
	int strWidth;	// 文字列の横幅（ドット数）
	int blockXcnt = 0; 				// 文节ボックスの座标のカウンター
	int blockX1 = 0, blockX2 = 0; 	// 文节ボックスの座标
	int blockFlag = FALSE; 			// 文节フラグ
	char *ImeBufferBak = ImeInfo.buffer;	// 作业用
	char *ImeBlockBak = ImeInfo.block;	// 作业用
	BOOL imeOpenFlag;				// ＩＭＥのＯＮ、ＯＦＦフラグ
	BOOL capsLockFlag;				// capsLock のＯＮ、ＯＦＦフラグ
	char keyBoardState[ 256 ] = { '\0' };		// キーボードの状态
	UCHAR imePrio;					// ＩＭＥボックスの表示优先度
	unsigned int i;
	
	// バックアップ
	ImeBufferBak2 = ImeInfo.buffer[ 0 ];
	// ＩＭＥの状态を记忆（　ＯＮ、ＯＦＦ　）
	imeOpenFlag = ImeOpenState();
	
	// capsLock の判定
	// キーボードの状态をとってくる（全てのキー）
	GetKeyboardState( ( BYTE *) keyBoardState );
	// capsLock 状态（　ＯＮ、ＯＦＦ　）
	capsLockFlag = keyBoardState[ VK_CAPITAL ];

#ifndef PUK2_NEW_MENU	
	// ＩＭＥ入力モード文字列の表示
	// タスクバーが非表示の时
	if( taskBarStatus == 0 ){
		// ＩＭＥがＯＮの时
		if( imeOpenFlag )
		{
			// CapsLock の时
			if( capsLockFlag ){
				// 例外の时
				if( ImeInfo.conversion >= 28 ){
					// 文字表示バッファに溜める
					StockFontBuffer( imeModeStrPosX, imeModeStrPosY,
						FONT_PRIO_FRONT, FONT_PAL_AQUA, "       ABC", 0 );              //MLHIDE
				}else{
					// 文字表示バッファに溜める
					StockFontBuffer( imeModeStrPosX+3, imeModeStrPosY,
						FONT_PRIO_FRONT, FONT_PAL_AQUA, ImeModeStrCaps[ ImeInfo.conversion ], 0 );
				}
			}else{
				// 例外の时
				if( ImeInfo.conversion >= 28 ){
					// 文字表示バッファに溜める
					StockFontBuffer( imeModeStrPosX, imeModeStrPosY,
						FONT_PRIO_FRONT, FONT_PAL_AQUA, "        abc", 0 );             //MLHIDE
				}else{
					// 文字表示バッファに溜める
					StockFontBuffer( imeModeStrPosX+3, imeModeStrPosY,
						FONT_PRIO_FRONT, FONT_PAL_AQUA, ImeModeStr[ ImeInfo.conversion ], 0 );
				}
			}
		}
		// ＩＭＥがＯＦＦの时
		else
		{	
			// CapsLock の时
			if( capsLockFlag ){
				// 文字表示バッファに溜める
				StockFontBuffer( imeModeStrPosX, imeModeStrPosY,
					FONT_PRIO_FRONT, FONT_PAL_WHITE, "        ABC", 0 );             //MLHIDE
			}else{
				// 文字表示バッファに溜める
				StockFontBuffer( imeModeStrPosX, imeModeStrPosY,
					FONT_PRIO_FRONT, FONT_PAL_WHITE, "        abc", 0 );             //MLHIDE
			}
		}
	}
#endif	
	// ＩＭＥ文字列の表示
	// ＩＭＥがＯＮの时かつＩＭＥバッファに文字があるとき
	if( imeOpenFlag && *ImeInfo.buffer != NULL ){
		
		// 入力场所がない时返回
		if( pNowInputStr == NULL ) return;


		// 入力文字の表示优先度からＩＭＥボックスの表示优先度を决める
		if( pNowInputStr->fontPrio == FONT_PRIO_BACK ){
			imePrio = DISP_PRIO_IME1;
		}else{
#ifdef PUK2
			imePrio = DISP_PRIO_NEW_IME1;
			imePrio = DISP_PRIO_IME3;
#else
			imePrio = DISP_PRIO_IME3;
#endif
		}

		// 文字表示バッファに溜める
		StockFontBuffer( pNowInputStr->imeX - 1, pNowInputStr->imeY - 1, 
						pNowInputStr->fontPrio + 1, pNowInputStr->fontKind, 0, ImeInfo.buffer, 0 );
						//FONT_PRIO_FRONT2, pNowInputStr->fontKind, 0, ImeInfo.buffer, 0 );
	
		// 文字の背景を描画
		// ＩＭＥバッファーの文字列の横幅を求める
		strWidth = GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind );

		// ボックス表示データをバッファに溜める
		StockBoxDispBuffer( pNowInputStr->imeX - 1, 
							pNowInputStr->imeY - 1, 
							pNowInputStr->imeX + strWidth, 
							pNowInputStr->imeY - 1, 
#ifdef PUK2
							imePrio, SYSTEM_PAL_BLACK, 1 );
#else		
							imePrio, SYSTEM_PAL_AQUA2, 1 );
#endif

		// 文节の座标の取得
		while( 1 ){
			// 文节の始まりだったら
			if( ( *ImeBlockBak == 1 || *ImeBlockBak == 3 ) && blockFlag == FALSE ){
				// 文节の始まりの座标
				blockX1 = blockXcnt;
				// 文节フラグＯＮ
				blockFlag = TRUE;
				//blockFlag = *ImeBlockBak;
			}
			
			// 文节終わりだったら
			if( ( *ImeBlockBak == 2 || *ImeBlockBak == NULL ) && blockFlag >= TRUE ){
				// 文节の終わりの座标
				blockX2 = blockXcnt;
				break;
			}
			
			// 終了条件
			if( *ImeBlockBak == NULL ) break;
			
			// アドレスと座标の更新
			// 文字が全角の时
			if( GetCharByte( *ImeBufferBak ) == 2 ){
			 
				ImeBufferBak += 2;	// ２バイト进める
				ImeBlockBak += 2;	// ２バイト进める
				blockXcnt += FontKind[ pNowInputStr->fontKind ].zenkakuWidth; // 全角ドット分足す
				
			}else{ // 半角のとき
			
				ImeBufferBak += 1;	// １バイト进める
				ImeBlockBak += 1;	// １バイト进める
				blockXcnt += FontKind[ pNowInputStr->fontKind ].hankakuWidth; // 半角ドット分足す
			}
		}
		
		//文节があるとき
		if( blockFlag >= TRUE ){
				// ボックス表示データをバッファに溜める
				StockBoxDispBuffer( pNowInputStr->imeX + blockX1 - 1, 
								pNowInputStr->imeY - 1, 
								pNowInputStr->imeX + blockX2, 
								pNowInputStr->imeY + FontKind[ pNowInputStr->fontKind ].zenkakuHeight, 
								imePrio + 1, SYSTEM_PAL_BLUE, 1 );
		}
		// 变换候补がある时
		// todo: 这里需要把输入法改成横向提示，同时右侧可以提示输入法类型，如：QQ输入法。SEC官方客户端现在已经改了。
		if( ImeInfo.candList != NULL ){
			// 候补の数だけループ
			for( i = 0 ; i < ImeInfo.candList->dwCount ; i++ ){
				// 现在表示中の文字じゃない时
				//if( ImeInfo.candList->dwSelection != i )
				{
					int selectionY = i + 1;
					
					// 文字表示バッファに溜める
					StockFontBuffer( pNowInputStr->imeX + blockX1, pNowInputStr->imeY + selectionY * FontKind[ pNowInputStr->fontKind ].zenkakuHeight, 
										//pNowInputStr->fontPrio + 1, pNowInputStr->fontKind, 0, (char *)ImeInfo.candList + ImeInfo.candList->dwOffset[i], 0 );
										FONT_PRIO_FRONT2, pNowInputStr->fontKind, 0, (char *)ImeInfo.candList + ImeInfo.candList->dwOffset[i], 0 );
					
					// 文字の背景を描画
					// 变换候补文字列の横幅を求める
					strWidth = GetStrWidth( (char *)ImeInfo.candList + ImeInfo.candList->dwOffset[i], pNowInputStr->fontKind );
					
					// ボックス表示データをバッファに溜める
					StockBoxDispBuffer( pNowInputStr->imeX + blockX1 - 1,
										pNowInputStr->imeY + selectionY * FontKind[ pNowInputStr->fontKind ].zenkakuHeight - 1,
										pNowInputStr->imeX + blockX1 + strWidth,
										pNowInputStr->imeY + (selectionY + 1) * FontKind[ pNowInputStr->fontKind ].zenkakuHeight - 1,
										imePrio, SYSTEM_PAL_BLACK, 1 );
										//DISP_PRIO_IME4, SYSTEM_PAL_RED2, 1 );
				}
			}
		}
	}
}
	
	
// WM_IME_COMPOSITION 信息の处理 ****************************************/
void ImeComposition( LPARAM lParam )
{
	int strLen;
	char str[ 256 ];
	int i;
	BOOL zenkakuFlag = FALSE; // 全角フラグ
	int candListByte;

	// 变换候补のサイズを取得
	candListByte = ImmGetCandidateList( ImeInfo.hImc, 0, 0, 0 );
	// メモリ确保していたら开放
#ifdef PUK2_MEMCHECK
	memlistrel( ImeInfo.candList, MEMLISTTYPE_IME );
#endif
	if( ImeInfo.candList != NULL ) free( ImeInfo.candList );
	// 构造体のサイズが０でない时
	if( candListByte != 0 ){
		// 变换候补の长さ分メモリ确保
		ImeInfo.candList = ( CANDIDATELIST * )calloc( 1, candListByte );
#ifdef PUK2_MEMCHECK
		memlistset( ImeInfo.candList, MEMLISTTYPE_IME );
#endif
		// 变换候补を取得する
		ImmGetCandidateList( ImeInfo.hImc, 0, ImeInfo.candList, candListByte );
	}else{ 
		// ポインタ初期化
		ImeInfo.candList = NULL;
	}
	
	// 文字确定したとき
	if( lParam & GCS_RESULTSTR ){
		// 文字の长さの取得
		strLen = ImmGetCompositionString( ImeInfo.hImc, GCS_RESULTSTR, NULL, 0 );
		// リミットチェック
		if( strLen >= 256 ) strLen = 255;
		// 文字列を取ってくる
		ImmGetCompositionString( ImeInfo.hImc, GCS_RESULTSTR, str, strLen );
		// 終端记号
		str[ strLen ] = NULL;
		// 半角カナがある时、全角カナに变换する
		//HanKanaToZenKana( str );
		// 变换后の文字の长さの取得
		strLen = strlen( str );
		// リミットチェック
		if( strLen >= 256 ) strLen = 255;
		// 文字の数だけループ
		for( i = 0 ; i < strLen ; i++ ){
			// 文字バッファに溜める
			StockInputStrChar( str[ i ] );
		}
	}
	
	// 表示されている文字（确定前）
	if( lParam & GCS_COMPSTR ){
		// 文字の长さの取得
		strLen = ImmGetCompositionString( ImeInfo.hImc, GCS_COMPSTR, NULL, 0 );
		// リミットチェック
		if( strLen >= 256 ) strLen = 255;
		// 文字列を取ってくる
		ImmGetCompositionString( ImeInfo.hImc, GCS_COMPSTR, ImeInfo.buffer, strLen );
		// 終端记号
		ImeInfo.buffer[ strLen ] = NULL;
	}
	
	// 文节を取ってくる
	if( lParam & GCS_COMPATTR ){
		// 文字の长さの取得
		strLen = ImmGetCompositionString( ImeInfo.hImc, GCS_COMPATTR, NULL, 0 );
		// リミットチェック
		if( strLen >= 256 ) strLen = 255;
		// 文字列を取ってくる
		ImmGetCompositionString( ImeInfo.hImc, GCS_COMPATTR, ImeInfo.block, strLen );
		// 終端记号
		ImeInfo.block[ strLen ] = NULL;
	}
	
	// カーソルの位置を取ってくる
	if( lParam & GCS_CURSORPOS ){
		// 文字の长さの取得
		ImeInfo.cursorPos = ImmGetCompositionString( ImeInfo.hImc, GCS_CURSORPOS, NULL, 0 );
		// カーソルカウンター戾す（点灯）
		CursorFlashCnt = 20;
	}
	
#if 0	
	// 振り假名を取得
	if( lParam & GCS_COMPREADSTR ){
		// 文字の长さの取得
		strLen = ImmGetCompositionString( ImeInfo.hImc, GCS_COMPREADSTR, NULL, 0 );
		// 文字列を取ってくる
		ImmGetCompositionString( ImeInfo.hImc, GCS_COMPREADSTR, str, strLen );
	}
	
	if( lParam & GCS_COMPCLAUSE ){
		// 終端记号
		str[ 0 ] = NULL;
		// 文字の长さの取得
		strLen = ImmGetCompositionString( ImeInfo.hImc, GCS_COMPCLAUSE, NULL, 0 );
		// 文字列を取ってくる
		ImmGetCompositionString( ImeInfo.hImc, GCS_COMPCLAUSE, str, strLen );
	}
	
	if( lParam & GCS_COMPREADCLAUSE ){
		// 文字の长さの取得
		strLen = ImmGetCompositionString( ImeInfo.hImc, GCS_COMPREADCLAUSE, NULL, 0 );
		// 文字列を取ってくる
		ImmGetCompositionString( ImeInfo.hImc, GCS_COMPREADCLAUSE, str, strLen );
	}
#endif
}
	
#if 0	
/* WM_IME_NOTIFY 信息の处理 *********************************************/
void ImeNotify( WPARAM wParam ,LPARAM lParam )
{
	switch ( wParam ){
	
	case IMN_OPENCANDIDATE:
		// この信息は、变换候补が入っているちっちゃいウインドウが开くタイミングで来る。
	case IMN_CHANGECANDIDATE:// この信息は、变换候补を变えるたびに来る。
		break;
		
	case IMN_CLOSECANDIDATE:
		// この信息は、变换候补が入っているちっちゃいウインドウが消えるタイミングでくる。
		break;
		
	case IMN_SETOPENSTATUS:// IMEを有效にしたり无效にしたりしたとき来る。
		break;
    }
}
#endif

#if 0
/** ＩＭＥ关连信息处理 **************************************************/
void ImeMsgProc( UINT Message, WPARAM wParam, LPARAM lParam )
{
	// 信息で分岐
	switch( Message ){
	
		case WM_IME_COMPOSITION: // ＩＭＥのちっちゃいウィンドウに变化がある时
	
			// WM_IME_COMPOSITION 信息の处理
			ImeComposition( lParam );
			break;
		case WM_IME_ENDCOMPOSITION: // 文字を确定した直后に来る。变换中の文字を全部BSでけしたときもくる。
			// ＩＭＥバッファーを空にする
			*ImeInfo.buffer = NULL;
			break;
		
		case WM_IME_NOTIFY:
			/* WM_IME_NOTIFY 信息の处理 */
			//ImeNotify( wParam ,lParam );
			// ここにDEFWINDOWPROCがあると、变换候补ウインドウを出すようだ。
			break;
		case WM_IME_SETCONTEXT:
			//初期化したとき来てるのかな？フォーカスが移ったときも来るようだ。
			break;
#if 0
		case WM_IME_STARTCOMPOSITION:// IMEがONの状态で、文字を入力した直后に来る。
			break;
		case WM_IME_CONTROL:
			break;
		case WM_IME_SELECT: // IMEをえらんだとき来るらしい。バッファを初期化とかすればいいのかな？
			break;
		case WM_IME_KEYDOWN:   // IMEのキーコード
			// ここにDefWindowProcがあると、WM_KEYDOWNを生成する
			break;
		case WM_IME_CHAR:
			// ここにDefWindowProcがあると、WM_CHARを生成する
			break;
#endif
	}
}
#endif
