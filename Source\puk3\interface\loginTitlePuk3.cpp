﻿#ifdef <PERSON>UK3_ACCOUNT
#include "../../systeminc/version.h"
#include "../../systeminc/system.h"
#include "../../systeminc/directDraw.h"
#include "../../systeminc/main.h"
#include "../../systeminc/gamemain.h"
#include "../../systeminc/sprmgr.h"
#include "../../systeminc/init.h"
#include "../../systeminc/process.h"
#include "../../systeminc/action.h"
#include "../../systeminc/sprdisp.h"
#include "../../systeminc/math2.h"
#include "../../systeminc/chat.h"
#include "../../systeminc/font.h"
#include "../../systeminc/mouse.h"
#include "../../systeminc/radar.h"
#include "../../systeminc/gemini.h"
#include "../../systeminc/pattern.h"
#include "../../systeminc/ime_sa.h"
#include "../../systeminc/menu.h"
#include "../../systeminc/pc.h"
#include "../../systeminc/character.h"
#include "../../systeminc/login.h"
#include "../../systeminc/netproc.h"
#include "../../systeminc/savedata.h"
#include "../../systeminc/testView.h"
#include "../../systeminc/battleProc.h"
#include "../../systeminc/produce.h"
#include "../../systeminc/nrproto_cli.h"
#include "../../systeminc/netmain.h"
#include "../../systeminc/battleMenu.h"
#include "../../systeminc/t_music.h"
#include "../../systeminc/field.h"
#include "../../systeminc/handletime.h"
#include "../../systeminc/map.h"
#include "../../systeminc/mapEffect.h"
#include "../../ohta/ohta.h"
#include "../../systeminc/keyboard.h"
#include "../../systeminc/direct3d.h"
#include "../../systeminc/sndcnf.h"
#include "../../systeminc/mapGridCursol.h"
#include "../../systeminc/tool.h"

#include "../../puk2/interface/menuwin.h"
#include "../../puk2/newDraw/Graphic_ID.h"

//ログインタイトルＰＵＫ３
#include "loginTitlePuk3.h"
#include "../account/account.h"

int Puk3LoginTitle_count;
int Puk3LoginTitle_subcount;
int Puk3LoginTitle_subflag;
int Puk3LoginTitleDialogNum;

int commonMsgWinProcNo2;

static PUK3_TITLE_TAB_POS Puk3TitleTabPos[PUK3_ACCOUNT_MAX]={
#ifdef VERSION_TW
	//台服版帐号切换按钮的位置
	{80 + 44 * 0,335,44,20},
	{80 + 44 * 1,335,44,20},
	{80 + 44 * 2,335,44,20},
	{80 + 44 * 3,335,44,20},
	{80 + 44 * 4,335,44,20},
	{80 + 44 * 5,335,44,20},
	{80 + 44 * 6,335,44,20},
	{80 + 44 * 7,335,44,20},
	{80 + 44 * 8,335,44,20},
	{80 + 44 * 9,335,44,20}
#else
	{150+44*0,335,44,20},
	{150+44*1,335,44,20},
	{150+44*2,335,44,20},
	{150+44*3,335,44,20},
	{150+44*4,335,44,20},
	{150+44*5,335,44,20},
	{150+44*6,335,44,20},
	{150+44*7,335,44,20},
	{150+44*8,335,44,20},
	{150+44*9,335,44,20}
#endif
};

// 入力处理??画面表示
//
//  戾り值：	 0 ... 入力中
//				 1 ... 入力完了
//				 2 ... 終了ボタン
//				-1 ... 入力栏の一部が空
//				-2 ... 入力栏のすべてが空
static int PUK3InputIdPassword()
{
	int ret = 0;
	int x1, y1, x2, y2;
	int strWidth;

	// ＯＫボタン
	PUK2_BTN_INFO pOkBtn ={
		// 范围始点座标(x,y)
#ifdef VERSION_TW
//台服账号密码输入界面按钮位置
		240, 421,
#else
		240, 438+14,
#endif
		// 范围幅(w,h)
		64, 16,
		// ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_OK0, PUK2_BUTTON_OK2, PUK2_BUTTON_OK1
	};

	// ＱＵＩＴボタン
	PUK2_BTN_INFO pQuitBtn ={
		// 范围始点座标(x,y)
#ifdef VERSION_TW
//台服账号密码输入界面按钮位置
		332, 421,
#else
		332, 438+14,
#endif
		// 范围幅(w,h)
		64, 16,
		// ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_QUIT0, PUK2_BUTTON_QUIT2, PUK2_BUTTON_QUIT1
	};

#ifdef WIN_SIZE_DEF
	//根据窗口分辨率计算账号输入界面退出按钮的显示位置
	//修正高分辨率下登陆界面按钮需要偏下
	pOkBtn.cy += (ScaleOffsetY - SymOffsetY);
	pQuitBtn.cy += (ScaleOffsetY - SymOffsetY);
#endif

	//ボタンチェック
	if( (PUK2pushGraBtnInfo( &pOkBtn ) & BTN_LEFT_CLICK)){
	// ＯＫボタンが押された

		// 押されたときのグラフィックに切り替え
		pOkBtn.graNo1 = PUK2_BUTTON_OK1;

		// ID??パスワード??CDキーが设定されているか
//		if( strlen( idKey.buffer ) > 0 && strlen( passwd.buffer ) > 0 && strlen( cdKey.buffer ) > 0 ){
		if( strlen( passwd.buffer ) > 0 && strlen( cdKey.buffer ) > 0 ){
			idKey.buffer[idKey.cnt] = '\0';
			passwd.buffer[passwd.cnt] = '\0';
			cdKey.buffer[cdKey.cnt] = '\0';
			ret = 1;
		}else{
//			if( strlen( idKey.buffer ) == 0 && strlen( passwd.buffer ) == 0 && strlen( cdKey.buffer ) == 0 ){
			if( strlen( passwd.buffer ) == 0 && strlen( cdKey.buffer ) == 0 ){
				// すべて设定されていない
				ret = -2;
			}else{
				// どれか设定されていない
				ret = -1;
			}
			
		}

	}else if( (PUK2pushGraBtnInfo( &pQuitBtn ) & BTN_LEFT_CLICK)){
	// 終了ボタンが押された

		// 押されたときのグラフィックに切り替え
		pQuitBtn.graNo1 = PUK2_BUTTON_QUIT1;

		ret = 2;
	}

	// 文字の背景を描画
	// ＩＭＥバッファーの文字列の横幅を求める
	strWidth = GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind );

	// ボックス表示データをバッファに溜める
	StockBoxDispBuffer( pNowInputStr->imeX - 1, 
						pNowInputStr->imeY - 1, 
						pNowInputStr->imeX + strWidth, 
						pNowInputStr->imeY + FontKind[ pNowInputStr->fontKind ].zenkakuHeight, 
						DISP_PRIO_IME2, SYSTEM_PAL_BLACK, 1 );
		
	//旧版登录时需要ID,PASSWORD,CDKEY,韩服与台服版本都已取消ID输入框
	//StockFontBuffer2( &idKey );
	StockFontBuffer2( &passwd );
	StockFontBuffer2( &cdKey );

	// 入力フォーカス变更
	//タブキー
	if( keyOnRep( VK_TAB ) || keyOnRep( VK_RETURN ) ){
#ifdef VERSION_TW
		if (idPasswordFocusSw) {
			if (keyOnRep(VK_RETURN)) {
				if (strlen(passwd.buffer) > 0
					&& strlen(cdKey.buffer) > 0)
					//非法字符检测,未完成
					//&& !sub_404060(passwd.buffer)
					//&& !sub_404060(cdKey.buffer)))
				{
					passwd.buffer[passwd.cnt] = '\0';
					cdKey.buffer[cdKey.cnt] = '\0';
					ret = 1;
				}
				else {
					idPasswordFocusSw = 0;
				}
			}
			else {
				idPasswordFocusSw = 0;
			}
		}
		else {
			idPasswordFocusSw = 1;
		}
#else
		idPasswordFocusSw++;
		//旧版登录时需要ID,PASSWORD,CDKEY,韩服与台服版本都已取消ID输入框,所以最大值为2
		//if (idPasswordFocusSw == 3)
		if (idPasswordFocusSw==2)
			idPasswordFocusSw=0;
#endif
	}

	/* 台服韩服版本均已取消idKey输入框
	//ＩＳＡＯＩＤでクリック
	if( idKeyBoxX < mouse.nowPoint.x && mouse.nowPoint.x < idKeyBoxX+372 &&
		idKeyBoxY < mouse.nowPoint.y && mouse.nowPoint.y < idKeyBoxY+26){
		if(mouse.onceState & MOUSE_LEFT_CRICK){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			idPasswordFocusSw=0;
		}
	}
	*/

	//パスワードでクリック
	if( passwdBoxX < mouse.nowPoint.x && mouse.nowPoint.x < passwdBoxX+372 &&
		passwdBoxY < mouse.nowPoint.y && mouse.nowPoint.y < passwdBoxY+26){
		if(mouse.onceState & MOUSE_LEFT_CRICK){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			idPasswordFocusSw=1;
		}
	}

	//ＣＤＫＥＹでクリック
	if( cdKeyBoxX < mouse.nowPoint.x && mouse.nowPoint.x < cdKeyBoxX+372 &&
		cdKeyBoxY < mouse.nowPoint.y && mouse.nowPoint.y < cdKeyBoxY+26){
		if(mouse.onceState & MOUSE_LEFT_CRICK){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			//旧版登录时需要ID,PASSWORD,CDKEY,韩服与台服版本都已取消ID输入框,这里替换ID编号为0
			//idPasswordFocusSw=2;
			idPasswordFocusSw=0;
		}
	}

	GetKeyInputFocus( idPasswordFocus[idPasswordFocusSw] );

	/*旧版登录时需要ID,PASSWORD,CDKEY,韩服与台服版本都已取消ID输入框
	x1 = idKeyBoxX-5;
	y1 = idKeyBoxY-4;
	x2 = x1 + 372;
	y2 = y1 + 26;
	MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX );
	*/

	x1 = passwdBoxX-5;
	y1 = passwdBoxY-4;
	x2 = x1 + 372;
	y2 = y1 + 26;
	MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX );

	x1 = cdKeyBoxX-5;
	y1 = cdKeyBoxY-4;
	x2 = x1 + 372;
	y2 = y1 + 26;
	MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX );

	// ＯＫボタン
	PUK2drawGraBtnInfo( &pOkBtn, DISP_PRIO_BOX+1, 0, 0, 0 );

	// ＱＵＩＴボタン
	PUK2drawGraBtnInfo( &pQuitBtn, DISP_PRIO_BOX+1, 0, 0, 0);

	// バージョン表示
#ifdef _TAIKEN
	StockFontBuffer( 480, 424, FONT_PRIO_BACK, FONT_PAL_BLUE, "闷刮井", 0 ); //MLHIDE
#endif
	//根据窗口分辨率计算账号密码输入界面右下角版本号的显示位置(640*480分辨率下4个默认坐标全为460,462)
#ifdef PUK2_NEWVER
	if( giInstallVersion >= 1 ){
		StockFontBuffer( DEF_APPSIZEX - 180, DEF_APPSIZEY - 18, FONT_PRIO_BACK, FONT_PAL_BLUE, NR_VERSION, 0 );
	}else{
		StockFontBuffer( DEF_APPSIZEX - 180, DEF_APPSIZEY - 18, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
	}
#else
	if( giInstallVersion >= 1 ){
		StockFontBuffer( DEF_APPSIZEX - 180, DEF_APPSIZEY - 18, FONT_PRIO_BACK, FONT_PAL_BLUE, NR_VERSION, 0 );
	}else{
		StockFontBuffer( DEF_APPSIZEX - 180, DEF_APPSIZEY - 18, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
	}
#endif
	
	return ret;

}

// 初期化
void initCommonMsgWin2( void )
{
	commonMsgWinProcNo2 = 0;
}

// 标准信息ウィンドウ处理（扩张版）
//
//  引数：		msg ... 表示する信息
//
//  戾り值：	0 ... 处理中
//				1 ... OKボタンが押された
//				2 ... NOボタンが押された
int commonMsgWin2( char *msg )
{
	static int fontId[] = { -2 };
	static ACTION *ptActMenuWin = NULL;
	int click;
	int i;
	static int x, y, w, h;
	int ret = 0;
	char *okBtn = "OK";                                                  //MLHIDE
	int gyo;
	int maxw;
	char over1 = 0;
	char over2 = 0;
	char *bp, *p;
	char str[256];
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	maxw = 0;
	strcpy( str, msg );
	bp = str;
	for(gyo=1;;gyo++){
		p = strchr( bp, '\n' );

		if (!p){
			i = GetStrWidth( bp, FONT_KIND_MIDDLE );
			if ( i > maxw ) maxw = i;
			break;
		}

		p[0] = '\0';

		i = GetStrWidth( bp, FONT_KIND_MIDDLE );
		if ( i > maxw ) maxw = i;

		bp = p + 1;
	}

	// 初期化
	if( commonMsgWinProcNo2 == 0 ){
		commonMsgWinProcNo2 = 1;

		for( i = 0; i < sizeof( fontId )/sizeof( int ); i++ )
		{
			fontId[i] = -2;
		}

		// ウィンドウ作成
		w = maxw+32;
		w = (w+63)/64*64;
		h = 48*2 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2) * (gyo-1);
		x = (640 - w)/2;
		y = (480 - h)/2;
		ptActMenuWin = makeWindowDisp( x, y, w, h, 4 );
	}

	if(MakeHitBox( x+w/3-33, y+61 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1),
		x+w/3+33, y+61 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1)+17, -1 ) )
			over1 = 1;

	if(MakeHitBox( x+w*2/3-33, y+61 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1),
		x+w*2/3+33, y+61 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1)+17, -1 ) )
			over2 = 1;

	// 右键されてなければ終了
	click = -1;
	if( over1 && (mouse.onceState & MOUSE_LEFT_CRICK) ) click = 0;
	if( over2 && (mouse.onceState & MOUSE_LEFT_CRICK) ) click = 1;

	if(click!=-1){
		switch(click){
		case 0:
			ret = 1;
			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
			break;
		case 1:
			ret = 2;
			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
			break;
		}

	}

	// 结果がわかったらウィンドウ关闭
	if( ret != 0 ){
		if( ptActMenuWin ){
			DeathAction( ptActMenuWin );
			ptActMenuWin = NULL;
			return ret;
		}
	}

	if( ptActMenuWin != NULL ){
		// ウィンドウ表示
		if( ptActMenuWin->hp >= 1 ){
			int xx;

			xx = (w - maxw)/2;
			p = str;
			for(i=0;i<gyo;i++){
				StockFontBuffer( x+xx, y+30 + i*(FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2),
					FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, p, 0, 0 );

				p += strlen(p) + 1;
			}

			StockDispBuffer( x+w/3, y+61+8 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1),
				DISP_PRIO_WIN2, (over1 ? GID_BigOKButtonOver : GID_BigOKButtonOn ), 0, &bm );

			StockDispBuffer( x+w*2/3, y+61+8 + (FontKind[ FONT_KIND_MIDDLE ].zenkakuHeight+2)*(gyo-1),
				DISP_PRIO_WIN2, (over2 ? GID_BigNoButtonOver : GID_BigNoButtonOn ), 0, &bm );
		}
	}

	return ret;
}

// ＩＤ??パスワード入力バッファ初期化
static void initPUK3InputIdPassword(void)
{
	char str[256] = {0};

	// インストールしているバージョンを调べる
	giInstallVersion = GetInstallVersion( );

	// ID入力バッファ初期化
	idKeyBoxX = 195+4+58;
	idKeyBoxY = 339+5+14;
	idKey.buffer[0] = '\0';
	idKey.cnt = 0;
	idKey.cursorByte = 0;
	//todo: 输入框未解决问题：输入字符后侧会有半个空格，帐号字符5个以上时很明显。另外tab切换焦点时目前也还有问题，会多切换一次。
#ifdef _DEBUG
	getIdPuk3( str,Puk3AccountSystem.myAccountNum );
	//InitInputStr( &idKey, idKeyBoxX+2, idKeyBoxY+2, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
	//InitInputStr( &idKey, 0, 0, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
	//	FONT_PAL_BLUE,str, 0, 0, 0, 0 );
#endif
	// パスワード入力バッファ初期化
#ifdef VERSION_TW
	//根据窗口分辨率计算密码输入框的显示位置
	//账号密码输入栏Y坐标使用对称偏移显示位置偏上所以x,y坐标分辨使用不同偏移方法
	passwdBoxX = SymOffsetX + 125 + 4 + 58;
	passwdBoxY = ScaleOffsetY + 370 + 5 + 14;
#else
	passwdBoxX = SymOffsetX + 195 + 4 + 58;
	passwdBoxY = ScaleOffsetY + 370 + 5 + 14;
#endif
	passwd.buffer[0] = '\0';
	passwd.cnt = 0;
	passwd.cursorByte = 0;
#ifdef _DEBUG
	getPasswordPuk3( str,Puk3AccountSystem.myAccountNum );
#endif
	InitInputStr( &passwd, passwdBoxX+2, passwdBoxY+2, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
		FONT_PAL_BLUE, str, 1, 20, 0, TRUE );

	// ＣＤキー入力バッファ初期化
#ifdef VERSION_TW
	//根据窗口分辨率计算账号输入框的显示位置
	//账号密码输入栏Y坐标使用对称偏移显示位置偏上所以x,y坐标分辨使用不同偏移方法
	cdKeyBoxX = SymOffsetX + 125 + 4 + 58;
	cdKeyBoxY = ScaleOffsetY + 339 + 5 + 14;
#else
	cdKeyBoxX = SymOffsetX + 195 + 4 + 58;
	cdKeyBoxY = ScaleOffsetY + 339 + 5 + 14;
#endif
	cdKey.buffer[0] = '\0';
	cdKey.cnt = 0;
	cdKey.cursorByte = 0;
#ifdef _DEBUG
	getCdkeyPuk3( str,Puk3AccountSystem.myAccountNum );
#endif
	InitInputStr( &cdKey, cdKeyBoxX+2, cdKeyBoxY+2, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
		FONT_PAL_BLUE, str, 1, 23, 0, 0 );

	// フォーカス设定
	idPasswordFocusSw = 0;
	GetKeyInputFocus( idPasswordFocus[idPasswordFocusSw] );

	// 入力の表示设定
	PUK2_inputFade = 0xff;

}

//アカウントタブのチェンジ
static void Puk3ChangeAccount(int num){

	//现在の状况をセーブ
	//入力している文字をバッファに保存
	setIdPuk3( idKey.buffer,Puk3AccountSystem.myAccountNum );
	setPasswordPuk3( passwd.buffer,Puk3AccountSystem.myAccountNum );
	setCdkeyPuk3( cdKey.buffer,Puk3AccountSystem.myAccountNum );

	//とりあえずファイルセーブ
	saveTmpStatePuk3();

	//仕样アカウントチェンジ
	Puk3AccountSystem.myAccountNum=num;

	//新しいもので初期化
	initPUK3InputIdPassword();

}

//アカウントタブの描画およびインターフェイス的处理
static void Puk3TitleAccountTab(void){

	int i,num,overnum;

	int fonton=GID_Num_Black_S_0;
	int fontoff=GID_Num_Black_S_0;
	int fontover=GID_Num_Red_S_0;

	//オーバーチェック
	overnum=-1;
	for(i=0;i<Puk3AccountSystem.tabMax;i++){
		if(SymOffsetX + Puk3TitleTabPos[i].x < mouse.nowPoint.x &&  mouse.nowPoint.x < SymOffsetX + Puk3TitleTabPos[i].x + Puk3TitleTabPos[i].w  ){
			if(ScaleOffsetY + Puk3TitleTabPos[i].y < mouse.nowPoint.y &&  mouse.nowPoint.y < ScaleOffsetY + Puk3TitleTabPos[i].y + Puk3TitleTabPos[i].h  ){
				//オーバーされてる
				overnum=i;
				if( (mouse.onceState & MOUSE_LEFT_CRICK) ){
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
					//さらにクリックされてる
					Puk3ChangeAccount(i);
				}
			}
		}
	}

	for(i=0;i<Puk3AccountSystem.tabMax;i++){
		num=i+1;
		if(i==Puk3AccountSystem.myAccountNum){
			//选择中のタブ
			if(num>=10){
				titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+15, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
									DISP_PRIO_BOX+1, fonton+num/10, 0, 0xffffffff );
				titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+21, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
									DISP_PRIO_BOX+1, fonton+num%10, 0, 0xffffffff );
				titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x, ScaleOffsetY + Puk3TitleTabPos[i].y,
									DISP_PRIO_BOX+1, GID_Puk3TitleAccountBordTabOn, 0, 0xffffffff );
			}else{
				titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+18, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
									DISP_PRIO_BOX+1, fonton+num, 0, 0xffffffff );
				titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x, ScaleOffsetY + Puk3TitleTabPos[i].y,
									DISP_PRIO_BOX+1, GID_Puk3TitleAccountBordTabOn, 0, 0xffffffff );
			}
		}else{
			//选择されてないタブ
			if(i==overnum){
				//オーバー中のタブ
				if(num>=10){
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+15, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
										DISP_PRIO_BOX+1, fontover+num/10, 0, 0xffffffff );
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+21, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
										DISP_PRIO_BOX+1, fontover+num%10, 0, 0xffffffff );
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x, ScaleOffsetY + Puk3TitleTabPos[i].y,
										DISP_PRIO_BOX, GID_Puk3TitleAccountBordTabOver, 0, 0xffffffff );
				}else{
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+18, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
										DISP_PRIO_BOX+1, fontover+num, 0, 0xffffffff );
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x, ScaleOffsetY + Puk3TitleTabPos[i].y,
										DISP_PRIO_BOX, GID_Puk3TitleAccountBordTabOver, 0, 0xffffffff );
				}
			}else{
				//オーバーされてないタブ
				if(num>=10){
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+15, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
										DISP_PRIO_BOX+1, fontoff+num/10, 0, 0xffffffff );
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+21, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
										DISP_PRIO_BOX+1, fontoff+num%10, 0, 0xffffffff );
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x, ScaleOffsetY + Puk3TitleTabPos[i].y,
										DISP_PRIO_BOX, GID_Puk3TitleAccountBordTabOff, 0, 0xffffffff );
				}else{
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x+18, ScaleOffsetY + Puk3TitleTabPos[i].y+3,
										DISP_PRIO_BOX+1, fontoff+num, 0, 0xffffffff );
					titleStockDisp( SymOffsetX + Puk3TitleTabPos[i].x, ScaleOffsetY + Puk3TitleTabPos[i].y,
										DISP_PRIO_BOX, GID_Puk3TitleAccountBordTabOff, 0, 0xffffffff );
				}
			}
		}
	}
}

//ＣＤＫＥＹ，ＰＡＳＳＷＯＲＤのメインプロセス
void PUK3_idPasswordProc(void){

	int		ret;
	static char	msg[256];
#ifdef PUK3_MOUSECURSOR
	BOOL	MouseCursorFlagBackUp;
#endif

	switch( SubProcNo ){
		case enumPuk3Title_Init:
			//初期化

			// フラグがFALSEなら入力できなくする
			GetKeyInputFocus( NULL );
			//カウント0
			Puk3LoginTitle_count=0;
			Puk3LoginTitle_subcount=0;
			Puk3LoginTitle_subflag=0;
			//次へ
			SubProcNo=enumPuk3Title_Opening;
			play_bgm( BGM_PUK3_OP );
			break;

		case enumPuk3Title_Opening:
			//オープニングアニメ中
			Puk3LoginTitle_count++;

			switch(Puk3LoginTitle_subflag){
			case 0:
				titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening0, 0, 0x00ffffff+Puk3LoginTitle_subcount*0x01000000 );
				Puk3LoginTitle_subcount+=6;
				if(Puk3LoginTitle_subcount>=255){
					Puk3LoginTitle_subflag=1;
					Puk3LoginTitle_subcount=0;
				}
					break;
			case 1:
				titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening0, 0, 0xffffffff );
				titleStockDisp(   0,   0, DISP_PRIO_BG+1, GID_Puk3TitleOpening1, 0, 0x00ffffff+Puk3LoginTitle_subcount*0x01000000 );
				Puk3LoginTitle_subcount+=6;
				if(Puk3LoginTitle_subcount>=255){
					Puk3LoginTitle_subflag=2;
					Puk3LoginTitle_subcount=0;
				}
				break;
			case 2:
				titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening0, 0, 0xffffffff );
				titleStockDisp(   0,   0, DISP_PRIO_BG+1, GID_Puk3TitleOpening1, 0, 0xffffffff );
				titleStockDisp(   0,   0, DISP_PRIO_BG+2, GID_Puk3TitleOpening2, 0, 0x00ffffff+Puk3LoginTitle_subcount*0x01000000 );
				Puk3LoginTitle_subcount+=6;
				if(Puk3LoginTitle_subcount>=255){
					Puk3LoginTitle_subflag=3;
					Puk3LoginTitle_subcount=0;
				}
				break;
			case 3:
				titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening0, 0, 0xffffffff );
				titleStockDisp(   0,   0, DISP_PRIO_BG+1, GID_Puk3TitleOpening1, 0, 0xffffffff );
				titleStockDisp(   0,   0, DISP_PRIO_BG+2, GID_Puk3TitleOpening2, 0, 0xffffffff );
				titleStockDisp(   0,   0, DISP_PRIO_BG+3, GID_Puk3TitleOpening3, 0, 0x00ffffff+Puk3LoginTitle_subcount*0x01000000 );
				Puk3LoginTitle_subcount+=6;
				if(Puk3LoginTitle_subcount>=255){
					//次へ
					SubProcNo=enumPuk3Title_IDInputInit;
				}
				break;
			}

			if( (mouse.onceState & MOUSE_LEFT_CRICK) ){
				//右键でオープニングスキップ
				SubProcNo = enumPuk3Title_IDInputInit;	// スキップ
			}
			break;

		case enumPuk3Title_IDInputInit:
			//ＩＤ、パスワード入力画面初期化

			//カウント0
			Puk3LoginTitle_count=0;
			Puk3LoginTitleDialogNum=0;

			//セーブファイル読み直し
			loadSaveFilePuk3Seq();

			//ダイアログ设定
			initPUK3InputIdPassword();

			//次へ
			SubProcNo=enumPuk3Title_IDInput;

			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			break;

		case enumPuk3Title_IDInput:
			//ＩＤ、パスワード入力画面

			Puk3LoginTitle_count++;
			if(Puk3LoginTitle_count>256)
				Puk3LoginTitle_count=0;

			ret = PUK3InputIdPassword();
			switch(ret){
				case 0:
					break;
				case 1:
					//きちんと入力されていてＯＫが押された

					// 入力されたID,PASSWDをとりあえずファイルに保存
					//入力している文字をバッファに保存
#ifdef _DEBUG
					setIdPuk3( idKey.buffer,Puk3AccountSystem.myAccountNum );
#endif
					setIdPuk3( cdKey.buffer,Puk3AccountSystem.myAccountNum );
					setPasswordPuk3( passwd.buffer,Puk3AccountSystem.myAccountNum );
					setCdkeyPuk3( cdKey.buffer,Puk3AccountSystem.myAccountNum );

					//ファイルセーブ
					saveTmpStatePuk3();

					// 决定ａ
					play_se( SE_NO_OK, 320, 240 );
					// 决定时のパレットチェンジ
					PaletteChange( 30, 30 );	// ＥＸ用
					SubProcNo = enumPuk3Title_ToServerSelect;	// 次へ
					break;
				case 2:
					// 終了ボタンが押された

					// 返回音
					play_se( SE_NO_BACK, 320, 240 );
					// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
					PostMessage( hWnd, WM_CLOSE, 0, 0L );
					break;

				case -1:
					//どれかひとつか空の状态でＯＫが押された
					//エラー
					SubProcNo = enumPuk3Title_ERRInit;
					break;

				case -2:
					//なにも入力されていない状态でＯＫが押された
					if(Puk3AccountSystem.myAccountNum==0){
						//未入力なのでエラー
						SubProcNo = enumPuk3Title_ERRInit;
					}else{
						//ファイル作成候补のアカウントか？
						if(Puk3AccountSystem.NewTabNum==Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]){
							//未入力なのでエラー
							SubProcNo = enumPuk3Title_ERRInit;
						}else{
							//アカウントファイルを削除するか确认
							SubProcNo = enumPuk3Title_DELETEWinInit;
						}
					}
					break;
			}

			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			//インプット板
#ifdef VERSION_TW
			//根据窗口分辨率计算账号密码输入框背景的显示位置
			//账号密码输入栏Y坐标使用对称偏移显示位置偏上所以x,y坐标分辨使用不同偏移方法(640*480分辨率下默认坐标台服为69,350,韩服139,350)
			titleStockDisp( SymOffsetX + 69, ScaleOffsetY + 350, DISP_PRIO_BOX, GID_Puk3TitleAccountBord, 0, 0xffffffff);
			//台服版帐号切换按钮,韩服没有,所以下方else后没有
			//タブ
			Puk3TitleAccountTab();
#else
			titleStockDisp( SymOffsetX + 139, ScaleOffsetY + 350, DISP_PRIO_BOX, GID_Puk3TitleAccountBord, 0, 0xffffffff );
#endif
			break;

		case enumPuk3Title_ToServerSelect:
#ifdef PUK3_MOUSECURSOR
			// セーブデータ読み込み前の状态を保存
			MouseCursorFlagBackUp = MouseCursorFlag;
#endif
			//服务器选择花面へ
			//选ばれたセーブデータをコピー
			setFromTmpSaveToSave();
			//その内容のものをメモリコピー
			getUserSoundOption(&saveData);
			getUserInterfaceOption(&saveData);
			getUserChatRegStr(&saveData);
			getUserMailSetting(&saveData);
#ifdef PUK3_MOUSECURSOR
			// カーソルの设定が变わったら
			if ( MouseCursorFlag != MouseCursorFlagBackUp ) ShowCursor( MouseCursorFlag );
#endif
#ifdef PUK3_RECVDATA
			// recvdata.txtの名称を决定
			sprintf( debugLogFileName, debugLogFileName_base, Puk3AccountSystem.myAccountNum + 1 );
#endif

			ChangeProc( PROC_TITLE_MENU );
			break;

		case enumPuk3Title_ERRInit:			
			// エラーの时初期化

			initCommonMsgWin();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			SubProcNo = enumPuk3Title_ERR;
			break;

		case enumPuk3Title_ERR:
			// エラー表示中
			strcpy( msg, ML_STRING(263, "请输入帐号和密码") );
			if( commonMsgWin( msg ) ){
				// ＯＫボタンが押された
				SubProcNo=enumPuk3Title_IDInputInit;
			}
			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			break;

		case enumPuk3Title_DELETEWinInit:			
			// ファイル削除の时初期化

			initCommonMsgWin2();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			SubProcNo = enumPuk3Title_DELETEWin;
			break;

		case enumPuk3Title_DELETEWin:
			// ファイル削除の表示中
			strcpy( msg, ML_STRING(322, "确定要删除这帐号记录吗?") );
			switch(commonMsgWin2( msg )){
				case 1:
					// ＯＫボタンが押された
					SubProcNo=enumPuk3Title_DELETEInit;
					break;

				case 2:
					// ＮＯボタンが押された
					SubProcNo=enumPuk3Title_IDInputInit;
					break;
			}
			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			break;

		case enumPuk3Title_DELETEInit:			
			// 削除の初期化

			//ここで削除
			deleteTmpSaveFilePuk3(Puk3AccountSystem.table[Puk3AccountSystem.myAccountNum]);

			initCommonMsgWin();
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			SubProcNo = enumPuk3Title_DELETE;
			break;

		case enumPuk3Title_DELETE:
			// 削除の表示中
			strcpy( msg, ML_STRING(323, "帐号记录已成功删除") );
			if( commonMsgWin( msg ) ){
				// ＯＫボタンが押された
				SubProcNo=enumPuk3Title_IDInputInit;
			}
			//最終画面
			titleStockDisp(   0,   0, DISP_PRIO_BG, GID_Puk3TitleOpening4, 0, 0xffffffff );
			break;
	}

}

#endif