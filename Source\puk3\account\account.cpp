﻿//ＰＵＫ３アカウント管理关系

#include <direct.h>
#include <stdlib.h>
#include <stdio.h>

#include "../../systeminc/system.h"
#include <winnls32.h>
#include <ime.h>
#include <imm.h>
#include "../../resource.h"
#include "../../systeminc/ime_sa.h"
#include "../../systeminc/netmain.h"
#include "../../systeminc/battleMap.h"
#include "../../systeminc/battleProc.h"
#include "../../systeminc/map.h"
#include "../../systeminc/main.h"
#include "../../systeminc/process.h"
#include "../../systeminc/action.h"
#include "../../systeminc/sprdisp.h"
#include "../../systeminc/sprmgr.h"
#include "../../systeminc/directDraw.h"
#include "../../systeminc/direct3D.h"
#include "../../systeminc/gamemain.h"
#include "../../systeminc/chat.h"
#include "../../systeminc/font.h"
#include "../../systeminc/mouse.h"
#include "../../systeminc/netproc.h"
#include "../../systeminc/keyboard.h"
#include "../../systeminc/debugLogWin.h"
#include "../../systeminc/pc.h"
#include "../../systeminc/savedata.h"
#include "../../systeminc/tool.h"
#include "../../systeminc/filetbl.h"
#include "../../systeminc/savedata.h"
#include "../../systeminc/t_music.h"
#include "../../systeminc/pc.h"

#ifdef PUK3_ACCOUNT

#include "account.h"

char accountPath[PUK3_ACCOUNT_MAX][256];
PUK3ACCOUNTSYSTEM Puk3AccountSystem;

//ディレクトリ作成
BOOL makePuk3AccountSystemDir(void){

	int i;
	char path[256];

	_chdir("data");                                                      //MLHIDE

	for(i=0;i<PUK3_ACCOUNT_MAX;i++){
		sprintf(path,"%d",i);                                               //MLHIDE
		_mkdir(path);
	}

	_chdir("..");                                                        //MLHIDE

	return TRUE;
}

//アカウントの初期化
static void initPuk3AccountFileSystem(void){

	int i;

	Puk3AccountSystem.tabMax=0;
	Puk3AccountSystem.myAccountNum=0;
	Puk3AccountSystem.usecount=0;
	Puk3AccountSystem.NewTabNum=-1;

	for(i=0;i<PUK3_ACCOUNT_MAX;i++){
		sprintf(Puk3AccountSystem.accountPath[i],"data\\%d",i);             //MLHIDE
		Puk3AccountSystem.use[i]=FALSE;
		Puk3AccountSystem.table[i]=-1;
		memset(Puk3AccountSystem.saveDataTmp,0,sizeof(SAVE_DATA));
	}

}

//起动したとき、どんな状况なのかファイルの有无からチェック
static int checkPuk3AccountFileSystem(void){
	
	int i;
	FILE *fp;
	char filePath[1024];

	//新パスにひとつでも存在するか调べる
	for(i=0;i<PUK3_ACCOUNT_MAX;i++){
		sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[i],SAVEFILE_NAME); //MLHIDE
		if( (fp = fopen( filePath, "rb+" )) != NULL ){                      //MLHIDE
			fclose( fp );
			//なにかあった
			return 0;
		}
	}

	//旧パスにファイルが存在するか调べる	
	if( (fp = fopen( "data\\save.dat", "rb+" )) != NULL ){               //MLHIDE
	fclose( fp );
		//あった
		return 1;
	}	

	//なにもなかった
	return 2;
}

//ファイルひとつ分ロード
BOOL loadSaveFilePuk3One(int index){

	FILE *fp;
	char readbuffer[sizeof(saveData)*2];
	int readbufferlen;
	int saveDataLen;
	char filePath[1024];

	savedataErrorCode = 0;

	sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[index],SAVEFILE_NAME); //MLHIDE

	// セーブファイルを开く
	if( (fp = fopen( filePath, "rb+" )) == NULL ){                       //MLHIDE
		// 开けない时はファイルが无い。
		return FALSE;
	}

	// セーブデータ読み込み
	readbufferlen = fread( readbuffer, 1, sizeof( readbuffer ), fp );
	if( ferror( fp ) )
	{
		// 読み込みエラー
		fclose( fp );
		return FALSE;
	}
	fclose( fp );

	jDecode( readbuffer, readbufferlen, 0, (char *)(&Puk3AccountSystem.saveDataTmp[index]), &saveDataLen );

	// ファイルサイズのチェック
	if( saveDataLen != SAVE_DATA_SIZE){
		return FALSE;
	}

	// ファイルバージョンのチェック
	if(Puk3AccountSystem.saveDataTmp[index].version != SAVE_DATA_VERSION){
		return FALSE;
	}
		

	return TRUE;
}

//新保存先からファイル読み込み
static BOOL loadSavefilePuk3(void){

	int i,count=0;
	int emptyNum;

	for(i=0;i<PUK3_ACCOUNT_MAX;i++){
		if(loadSaveFilePuk3One(i)){
			//成功时
			Puk3AccountSystem.table[Puk3AccountSystem.usecount]=i;
			Puk3AccountSystem.use[i]=TRUE;
			Puk3AccountSystem.usecount++;
			Puk3AccountSystem.tabMax++;
		}else{
			Puk3AccountSystem.use[i]=FALSE;
		}
	}

	//１０未满なら次候补を准备しておく
	if(Puk3AccountSystem.usecount<=10){
		emptyNum=getNouseAccountPuk3();
		if(emptyNum!=-1){
			Puk3AccountSystem.table[Puk3AccountSystem.usecount]=emptyNum;
			Puk3AccountSystem.use[emptyNum]=TRUE;
			//内容初期化
			InitSaveFileStatus(emptyNum);
			Puk3AccountSystem.NewTabNum=emptyNum;
			Puk3AccountSystem.usecount++;
			Puk3AccountSystem.tabMax++;
		}
	}

	if(Puk3AccountSystem.usecount<=0){
		//ファイルが存在しないのはありえないはずだけど
		return FALSE;
	}else{
		return TRUE;
	}
}

//旧保存先から新保存先へコピー
static void CopySaveFilePuk3(){

	CopyFile("data\\save.dat","data\\0\\save.dat",FALSE);                //MLHIDE
	CopyFile("data\\mail.dat","data\\0\\mail.dat",FALSE);                //MLHIDE
	CopyFile("data\\guildmail.dat","data\\0\\guildmail.dat",FALSE);      //MLHIDE

}

//新しくファイル作成
BOOL makeSaveFilePuk3(char *fileName){

	SAVE_DATA saveDataBackup;
	FILE *fp;
	char writebuffer[sizeof( saveData )*2];
	int writebufferlen;
	int i;

	// セーブデータバッファクリア
	memset( &saveDataBackup, 0, sizeof( saveDataBackup ) );

	// バージョン１仕样 -------------------------------------------------------//

	// バージョン番号を入れる
	saveDataBackup.version = SAVE_DATA_VERSION;

	//文字列クリア
	strcpy((char *)saveDataBackup.cdKey,"");
	strcpy((char *)saveDataBackup.password,"");
	strcpy((char *)saveDataBackup.id,"");

	// 初期设定
	saveDataBackup.stereoSetting = (unsigned char)stereo_flg;
	saveDataBackup.seVol         = (unsigned char)t_music_se_volume;
	saveDataBackup.bgmVol        = (unsigned char)t_music_bgm_volume;
	for( i = 0; i < 16; i++ )
		saveDataBackup.bgmPitch[i] = t_music_bgm_pitch[i];
	saveDataBackup.mouseCursorSetting = 0;
	saveDataBackup.fontSize           = 0;
#ifdef DRAW_CONFIG
	saveDataBackup.LasterNoWaitFlg    = (unsigned char)RasterNoWaitMode;
#endif
	for( i = 0;	i < MAXCHARACTER; i++ )
	{
		saveDataBackup.chatLine[i]  = DEF_CHAT_LINE;
		saveDataBackup.chatArea[i]  = DEF_VOICE;
		saveDataBackup.chatSound[i] = DEF_SOUND;
	}

	for( i = 0; i < MAX_CHAT_REG; i++ )
	{
		saveDataBackup.chatRegStr[i][0] = '\0';
	}

	saveDataBackup.ctrlSetting = 0;
	saveDataBackup.addressBookSortMode = 0;
	saveDataBackup.nameOverTheHeadFlag = 0;		// キャラの头の上に名称を出す 	(    1 byte )

	//-------------------------------------------------------------------------//

	// セーブデータの暗号化
	jEncode( (char *)(&saveDataBackup), sizeof( saveDataBackup ), 0,
		writebuffer, &writebufferlen, sizeof( writebuffer ) );

	if( (fp = fopen( fileName, "wb+" )) == NULL )                        //MLHIDE
	{
		return FALSE;
	}
	if( fwrite( writebuffer, 1, writebufferlen, fp ) < (unsigned int)writebufferlen )
	{
		return FALSE;
	}

	fclose( fp );

	return TRUE;

}

void InitSaveFileStatus(int num){

	int i;

	// セーブデータバッファクリア
	memset( &Puk3AccountSystem.saveDataTmp[num], 0, sizeof( Puk3AccountSystem.saveDataTmp[num] ) );

	// バージョン１仕样 -------------------------------------------------------//

	// バージョン番号を入れる
	Puk3AccountSystem.saveDataTmp[num].version = SAVE_DATA_VERSION;

	//文字列クリア
	strcpy((char *)Puk3AccountSystem.saveDataTmp[num].cdKey,"");
	strcpy((char *)Puk3AccountSystem.saveDataTmp[num].password,"");
	strcpy((char *)Puk3AccountSystem.saveDataTmp[num].id,"");

	// 初期设定
	Puk3AccountSystem.saveDataTmp[num].stereoSetting = (unsigned char)stereo_flg;
	Puk3AccountSystem.saveDataTmp[num].seVol         = (unsigned char)t_music_se_volume;
	Puk3AccountSystem.saveDataTmp[num].bgmVol        = (unsigned char)t_music_bgm_volume;
	for( i = 0; i < 16; i++ )
		Puk3AccountSystem.saveDataTmp[num].bgmPitch[i] = t_music_bgm_pitch[i];
	Puk3AccountSystem.saveDataTmp[num].mouseCursorSetting = 0;
	Puk3AccountSystem.saveDataTmp[num].fontSize           = 0;
#ifdef DRAW_CONFIG
	Puk3AccountSystem.saveDataTmp[num].LasterNoWaitFlg    = (unsigned char)RasterNoWaitMode;
#endif
	for( i = 0;	i < MAXCHARACTER; i++ )
	{
		Puk3AccountSystem.saveDataTmp[num].chatLine[i]  = DEF_CHAT_LINE;
		Puk3AccountSystem.saveDataTmp[num].chatArea[i]  = DEF_VOICE;
		Puk3AccountSystem.saveDataTmp[num].chatSound[i] = DEF_SOUND;
	}

	for( i = 0; i < MAX_CHAT_REG; i++ )
	{
		Puk3AccountSystem.saveDataTmp[num].chatRegStr[i][0] = '\0';
	}

	Puk3AccountSystem.saveDataTmp[num].ctrlSetting = 0;
	Puk3AccountSystem.saveDataTmp[num].addressBookSortMode = 0;
	Puk3AccountSystem.saveDataTmp[num].nameOverTheHeadFlag = 0;		// キャラの头の上に名称を出す 	(    1 byte )

}

//现在のファイルの状况を取得
BOOL loadSaveFilePuk3Seq(void){

	int mode;

	initPuk3AccountFileSystem();
	mode=checkPuk3AccountFileSystem();

	switch(mode){
	//新位置にどれかあるなら
	case 0:
		loadSavefilePuk3();
		break;
	//新位置に无くて旧位置にファイルがあるなら
	case 1:
		CopySaveFilePuk3();
		loadSavefilePuk3();
		break;
	//新位置にも旧位置にもなにもないならインスコ直后なので０から作成
	case 2:
		makeSaveFilePuk3("data\\0\\save.dat");                              //MLHIDE
		loadSavefilePuk3();
		break;
	}

	//デフォルトとして最初に见つかったのをいれときます
	memcpy((void *)&saveData,(void *)&Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[0]],sizeof(SAVE_DATA));

	return TRUE;
}

// ＩＤ关连
// IDを保存用バッファに置く
void setIdPuk3( char *id,int num )
{
	int i;

	if( id == NULL )
		return;

	for( i = 0; i < sizeof( Puk3AccountSystem.saveDataTmp[num].id ); i++ ){
		Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[num]].id[i] = id[i];
		if( id[i] == '\0' )
			break;
	}
}

// IDを保存用バッファから取る
char *getIdPuk3( char *id,int num )
{
	int i;

	if( id == NULL )
		return NULL;

	if(Puk3AccountSystem.table[num]==-1){
		id[0]='\0';
		return id;
	}

	for( i = 0; i < sizeof( Puk3AccountSystem.saveDataTmp[num].id ); i++ ){
		id[i] = Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[num]].id[i];
		if( id[i] == '\0' )
			break;
	}

	return id;
}

// パスワード处理                                                          //
// パスワードを保存用バッファに入れる
void setPasswordPuk3( char *pass,int num )
{
	int i;

	if( pass == NULL )
		return;

	for( i = 0; i < sizeof( Puk3AccountSystem.saveDataTmp[num].password ); i++ ){
		Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[num]].password[i] = pass[i];
		if( pass[i] == '\0' )
			break;
	}
}

// パスワードを保存用バッファから取る
char *getPasswordPuk3( char *pass,int num )
{
	int i;

	if( pass == NULL )
		return NULL;

	if(Puk3AccountSystem.table[num]==-1){
		pass[0]='\0';
		return pass;
	}

	for( i = 0; i < sizeof( Puk3AccountSystem.saveDataTmp[num].password ); i++ ){
		pass[i] = Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[num]].password[i];
		if( pass[i] == '\0' )
			break;
	}

	return pass;
}

// ＣＤキー关连
// CDKEYを保存用バッファに置く
void setCdkeyPuk3( char *cdkey,int num )
{
	int i;

	if( cdkey == NULL )
		return;

	for( i = 0; i < sizeof( Puk3AccountSystem.saveDataTmp[num].cdKey ); i++ ){
		Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[num]].cdKey[i] = cdkey[i];
		if( cdkey[i] == '\0' )
			break;
	}
}

// CDKEYを保存用バッファから取る
char *getCdkeyPuk3( char *cdkey,int num )
{
	int i;

	if( cdkey == NULL )
		return NULL;

	if(Puk3AccountSystem.table[num]==-1){
		cdkey[0]='\0';
		return cdkey;
	}

	for( i = 0; i < sizeof( Puk3AccountSystem.saveDataTmp[num].cdKey ); i++ ){
		cdkey[i] = Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.table[num]].cdKey[i];
		if( cdkey[i] == '\0' )
			break;
	}

	return cdkey;
}

//saveDataに使用するアカウントをコピー
void setFromTmpSaveToSave(void){

	memcpy((void *)&saveData,(void *)&Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.myAccountNum],sizeof(SAVE_DATA));

}

//使用しているアカウントにsaveDataの内容コピー
void setFromSaveToTmpSave(void){

	memcpy((void *)&Puk3AccountSystem.saveDataTmp[Puk3AccountSystem.myAccountNum],(void *)&saveData,sizeof(SAVE_DATA));

}

//TmpSaveFile内容を保存
void saveTmpStatePuk3(void){

	int i;
	SAVE_DATA saveDataBackup;
	FILE *fp;
	char writebuffer[sizeof(saveData)*2];
	int writebufferlen;
	char filePath[1024];

	for(i=0;i<PUK3_ACCOUNT_MAX;i++){
		if(Puk3AccountSystem.use[i]==TRUE){
			if(Puk3AccountSystem.saveDataTmp[i].cdKey[0]!='\0' &&
				Puk3AccountSystem.saveDataTmp[i].password[0]!='\0' && 
				Puk3AccountSystem.saveDataTmp[i].id[0]!='\0' 
			){
				saveDataBackup = Puk3AccountSystem.saveDataTmp[i];

				// セーブデータの暗号化
				jEncode( (char *)(&saveDataBackup), sizeof(saveDataBackup), 0,
					writebuffer, &writebufferlen, sizeof( writebuffer ) );

				sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[i],SAVEFILE_NAME); //MLHIDE

				// セーブデータ保存
				if( (fp = fopen( filePath, "wb+" )) == NULL ){                    //MLHIDE
					break;
				}
				if( fwrite( writebuffer, 1, writebufferlen, fp ) < (unsigned int)writebufferlen ){
					fclose( fp );
					break;
				}
				fclose( fp );
			}
		}
	}

}

void deleteTmpSaveFilePuk3(int num){

	char filePath[1024];

	//セーブファイル削除
	sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[num],SAVEFILE_NAME); //MLHIDE
	DeleteFile(filePath);

	//メールファイル削除
	sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[num],MAIL_FILE_NAME); //MLHIDE
	DeleteFile(filePath);

	//家族メールファイル削除
	sprintf(filePath,"%s\\%s",Puk3AccountSystem.accountPath[num],GUILDMAIL_FILE_NAME); //MLHIDE
	DeleteFile(filePath);


}

//空いてるアカウント枠を取得
int getNouseAccountPuk3(void){

	int i=0;

	for(i=0;i<PUK3_ACCOUNT_MAX;i++){
		if(Puk3AccountSystem.use[i]==FALSE){
			return i;
		}
	}

	return -1;

}

#endif