﻿#ifndef _SAVEDATA_H_
#define _SAVEDATA_H_

#include "chat.h"
#include "pc.h"

// エラー信息
#define SAVE_ERRMSG_loadNowState	"读取data失败。(code = %d)"
#define SAVE_ERRMSG_SIZE_ERR		"data文件读取失败，重新生成。"
#define SAVE_ERRMSG_VER_ERR			"data文件异常，重新生成。"
// セーブファイル名
#define SAVEFILE_NAME	"save.dat"

#if 0
#define SAVE_DATA_VERSION		0x0001				// セーブデータバージョン
#else
// β版セーブデータバージョン
#define SAVE_DATA_VERSION		0x7002				// セーブデータバージョン
#endif

// セーブデータ构造体
typedef struct
{
	// バージョン１ -----------------------------------------------------------//
	unsigned int  version;					// セーブデータバージョン		(    4 byte )
	unsigned char id[51];					// ＩＤ番号                     (   51 byte )
	unsigned char password[51];				// パスワード					(   51 byte )
	unsigned char cdKey[51];				// CDキー						(   51 byte )
	unsigned char stereoSetting;			// 立体声??单声道の设定		(    1 byte )
	unsigned char seVol;					// ＳＥボリューム				(    1 byte )
	unsigned char bgmVol;					// ＢＧＭボリューム				(    1 byte )
	unsigned char bgmPitch[16];				// ＢＧＭピッチ					(   16 byte )
	unsigned char mouseCursorSetting;		// マウスカーソルの设定			(    1 byte )
	unsigned char fontSize;					// 文字サイズ					(    1 byte )
	char          chatRegStr[MAX_CHAT_REG][MAX_CHAT_REG_STR_LEN+1];
											// チャット登録文字列			( 24*25 = 600 byte)
	unsigned char chatColor[MAXCHARACTER];	// チャット文字色				(    2 byte ) user
	unsigned char chatLine[MAXCHARACTER];	// チャット行数					(    2 byte ) user
	unsigned char chatArea[MAXCHARACTER];	// チャットの闻こえる范围		(    2 byte ) user
	unsigned char chatSound[MAXCHARACTER];	// チャット流れる音				(    2 byte ) user
	unsigned char ctrlSetting;				// CTRLキーの设定               (    1 byte )
	unsigned char addressBookSortMode;		// アドレスブックのソートモード (    1 byte )
	unsigned char nameOverTheHeadFlag;		// キャラの头の上に名称を出す 	(    1 byte )
	unsigned short mapBgmNo[MAXCHARACTER];	// 现在のＢＧＭ番号（复归接続）	(    4 byte ) user
	unsigned char LasterNoWaitFlg;			// ラスターウエイトしないか		(    1 byte )
#ifdef _SYSTEMMENU_BTN_CONFIG
	unsigned char ChatAreaBtnFlg;			// チャットエリアボタンを使うか	(    1 byte )
	unsigned char bummy[228];				// ダミー						(  228 byte )
#else
	unsigned char bummy[229];				// ダミー						(  229 byte )
#endif
	//                                      	                       合计   1023 byte
	//-------------------------------------------------------------------------//

} SAVE_DATA;

#define SAVE_DATA_SIZE			(sizeof(SAVE_DATA))	// セーブデータサイズ

BOOL saveUserSetting( void );
BOOL loadUserSetting( void );
void setUserSetting( int );
void getUserSetting( int );
void clearUserSetting( int );
void setUserSoundOption( void );
void getUserSoundOption( SAVE_DATA * );
void setUserChatOption( int );
void getUserChatOption( int );
void setUserInterfaceOption( void );
void getUserInterfaceOption( SAVE_DATA * );
void setUserChatRegStr( void );
void getUserChatRegStr( SAVE_DATA * );
void setUserMailSetting( void );
void getUserMailSetting( SAVE_DATA * );
// マップＢＧＭ番号を保存バッファに入れる
void setUserMapBgmNo( int no );
// マップＢＧＭ番号を保存バッファから取ってくる
void getUserMapBgmNo( int no );

BOOL saveNowState( void );
BOOL loadNowState( void );

BOOL createSaveFile( void );

void setId( char * );
char *getId( char * );
void setPassword( char * );
char *getPassword( char * );
void setCdkey( char * );
char *getCdkey( char * );

extern unsigned char savedataErrorCode;
extern SAVE_DATA saveData;	// セーブデータ
extern char saveDataName[128];	// 可变セーブデータのファイル名

#endif // _SAVEDATA_H_
