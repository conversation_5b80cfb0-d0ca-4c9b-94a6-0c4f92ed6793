﻿
#define MENU_WINDOW_ADDRESSBOOK_FLASH_TIME 16
#include <limits.h>

static VIEW_MODE menuWindowAddressBookViewMode = EnumMenuWindowAddressBookViewModeDetail;
static int menuWindowAddressBookSortKey = 0;
#ifdef PUK3_MAIL_ETC
	static int menuWindowDetailViewLine[3] = {0};
	static int menuWindowListViewLine[3]  = {0};
#else
static int menuWindowDetailViewLine = 0;
static int menuWindowListViewLine  = 0;
#endif
static BOOK_TYPE menuWindowAddressBookType = EnumMenuWindowAddressBookTypeAddressBook;
static int menuWindowAddressBookFlashCounter = 0;
static int menuWindowAddressBookFlashMode = 0;

extern int addressBookDelNo;

static int MenuAddressBookGetTargetNumber (int no);
static void MenuWindowAddressBookScrollBarTest (int mouse);
static void MenuWindowAddressBookSyncViewAndTab ();
static void MenuAddressBookSetNewTitle (int no);
static BOOL MenuAddressBookIsMeGuildMaster ();
static BOOL MenuAddressBookGetMyCommition (int commition);
static BOOL MenuAddressBookGetYourCommition (int titleID, int commition);
static void MenuAddressBookDeleteFromAddressBook (int delNo);
static void MenuAddressBookDeleteFromGuildMember (int delNo);
static BOOL MenuWindowAddressBookMailSignalMode ();

static BOOK_TYPE MenuHistoryWindowType=EnumMenuWindowAddressBookTypeAddressBook;

extern MailStatusStruct MailStatusST;

MailHistoryStatus MailHistoryST;

//家族称号のターゲット
int ChangeGuildTitleTargetNum;
int ChangeGuildTitleTargetID;
//选择中の家族称号
int ChangeGuildTitle;

//画像ナンバーリスト
static const int sortPanel [2][5] = {
	{GID_addressBookSortKeyAlphabetical, GID_addressBookSortKeyGuild,
	 GID_addressBookSortKeyLevel, GID_addressBookSortKeyRegist,
	 GID_addressBookSortKeyServer},
	{GID_addressBookSortKeyAlphabetical, GID_addressBookSortKeyTitle, 
	 GID_addressBookSortKeyLevel, GID_addressBookSortKeyRegist,
	 GID_addressBookSortKeyServer}
};

#ifdef PUK3_MAIL_ETC
void AddressBookInit()
{
	//アドレスブック种类设定
	menuWindowAddressBookType = EnumMenuWindowAddressBookTypeAddressBook;

	memset( &menuWindowDetailViewLine, 0, sizeof(menuWindowDetailViewLine) );
	memset( &menuWindowListViewLine, 0, sizeof(menuWindowListViewLine) );
}
#endif
//ウインドウ处理
BOOL MenuWindowAddressBook (int mouse)
{

	//初期化
	if (mouse == WIN_INIT) {
	#ifdef PUK3_MAIL_ETC
	#else
		//アドレスブック种类设定
		menuWindowAddressBookType = EnumMenuWindowAddressBookTypeAddressBook;
	#endif
		//ソートモード设定
		menuWindowAddressBookSortKey = addressBookSortMode;

		ChangeBookType(menuWindowAddressBookViewMode,menuWindowAddressBookType);
	
		//未読メールチェック
		checkNoReadMail ();
		MenuWindowAddressBookSyncViewAndTab ();
	}
	return TRUE;
}

//ウインドウ描画
BOOL MenuWindowAddressBookDraw (int mouse)
{

	//スクロールバーの值取得
	MenuWindowAddressBookScrollBarTest (mouse);

	//点灭处理
	MenuWindowAddressBookMailSignalMode ();

	//绘图设定
	MenuWindowAddressBookRedraw ();

	//描画
	displayMenuWindow ();

	return TRUE;
}

//绘图设定
void MenuWindowAddressBookRedraw (){

	GRAPHIC_SWITCH* key = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookSortKey].Switch;
	key->graNo = sortPanel[menuWindowAddressBookType][menuWindowAddressBookSortKey];

	MenuWindowAddressBookSort ();
	if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
		//ディティール时の描画
		menuWindowAddressBookToDetail ();
	}else{
		//リスト时の描画
		menuWindowAddressBookToList ();
	}
}

//ディティールモード时の描画
void menuWindowAddressBookToDetail ()
{
	GRAPHIC_SWITCH *graph;
	TEXT_SWITCH* text;
	int i;
	int top;
	char tmp[256];
	int sub;
	ADDRESS_BOOK_INFO* ai;
	BOOL cc;
	BLT_MEMBER bm={0};

	addressBookPage = addressBookNewPage;

	switch(menuWindowAddressBookType){
		//アドレス时
		case EnumMenuWindowAddressBookTypeAddressBook:
			break;

		//家族时
		case EnumMenuWindowAddressBookTypeGuildBook:
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookRecvSub0; ++i){
				if(MenuAddressBookIsMeGuildMaster ()){
					//自分が家族マスターの时
					wI->sw[i].Enabled=TRUE;
				}else{
					wI->sw[i].Enabled=FALSE;
				}
			}

			//マスター以外はタイトル变更アイコン（＜、＞）非表示
			if(MenuAddressBookIsMeGuildMaster ()){
				for(i=0;i<3;i++){
					//マスターは出さない
#ifdef PUK3_MAIL_ETC
					if(guildBook.title[guildBook.member[MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+i].index].titleId].flag & GUILD_FLAG_MASTER){
#else
					if(guildBook.title[guildBook.member[MenuAddressBookSortTable[menuWindowDetailViewLine+i].index].titleId].flag & GUILD_FLAG_MASTER){
#endif
						wI->sw[EnumGraphAddressBookChangeTitle0+i*2+0].Enabled=FALSE;	
						wI->sw[EnumGraphAddressBookChangeTitle0+i*2+1].Enabled=FALSE;	
					}else{
					//マスター以外は左矢印のみ表示
						wI->sw[EnumGraphAddressBookChangeTitle0+i*2+0].Enabled=FALSE;	
						wI->sw[EnumGraphAddressBookChangeTitle0+i*2+1].Enabled=TRUE;	
					}
				}
			}else{
				for(i=0;i<3;i++){
					wI->sw[EnumGraphAddressBookChangeTitle0+i*2+0].Enabled=FALSE;	
					wI->sw[EnumGraphAddressBookChangeTitle0+i*2+1].Enabled=FALSE;	
				}
			}

			for (i = EnumGraphAddressBookDeleteSub0; i < EnumGraphAddressBookName2; i += 8){
				if(MenuAddressBookGetMyCommition (GUILD_FLAG_DISMISS)){
					wI->sw[i].Enabled=TRUE;
				}else{
					wI->sw[i].Enabled=FALSE;
				}
			}

			//家族の所属するサーバー名表示
			if(guildBook.serverNo!=-1){
				wI->sw[EnumGraphAddressBookGuildServer].Enabled=TRUE;

				text = (TEXT_SWITCH*) wI->sw[EnumGraphAddressBookGuildServer].Switch;
				if(guildBook.serverNo<=12){
					strcpy (text->text, GuildServerNameList[guildBook.serverNo]);
					text->color=GuildServerColorList[guildBook.serverNo];
				}else{
					strcpy (text->text, GuildServerNameList[0]);
					text->color=GuildServerColorList[0];
				}
			}else{
				wI->sw[EnumGraphAddressBookGuildServer].Enabled=FALSE;
			}

			break;

#ifdef PUK2_PROF
		//ミニメール时
		case EnumMenuWindowAddressBookTypeMiniMail:	

			break;
#endif
	}

	//个人情报描画
	for (i = 0; i < AMOUNT_OF_DETAIL_VIEW; ++i){
#ifdef PUK3_MAIL_ETC
		top = MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType] + i].index;
#else
		top = MenuAddressBookSortTable[menuWindowDetailViewLine + i].index;
#endif
		sub = EnumGraphAddressBookName0 + 8 * i;

		wI->sw[EnumGraphAddressBookRecvSub0 + i * 8].Enabled
		= wI->sw[EnumGraphAddressBookSendSub0 + i * 8].Enabled
		= wI->sw[EnumGraphAddressBookDeleteSub0 + i * 8].Enabled 
		= TRUE;

		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook:
				ai =&addressBook[top];  
				((GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookDetail0+i].Switch)->graNo=GID_addressBookDetailViewBase;
				break;
			case EnumMenuWindowAddressBookTypeGuildBook:
				ai =&guildBook.member[top].address;  
				((GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookDetail0+i].Switch)->graNo=GID_addressBookDetailViewBaseGuild;
				break;
#ifdef PUK3_PROF
			case EnumMenuWindowAddressBookTypeMiniMail:
				ai =&miniMailBook[top];  
				((GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookDetail0+i].Switch)->graNo=GID_addressBookDetailViewBase;
				break;
#endif
		}


		if (ai->useFlag) {
			//颜描画
			bm.rgba.rgba=0xffffffff;
			bm.bltf=BLTF_NOCHG;
			StockDispBuffer (detailFace[i].x + wI->wx-7, detailFace[i].y + wI->wy-5, DISP_PRIO_WIN2, 
				getNewFaceGraphicNo (ai->graNo), 0,&bm);

			//名称
			strcpy(((TEXT_SWITCH*)wI->sw[sub + 0].Switch)->text,ai->name);
			//等级
			sprintf (tmp, "%3d", ai->lv);                                      //MLHIDE
			strcpy(((TEXT_SWITCH*)wI->sw[sub + 1].Switch)->text,tmp);

			//家族（もしくは家族称号）
			switch(menuWindowAddressBookType){
				case EnumMenuWindowAddressBookTypeAddressBook:
					strcpy(((TEXT_SWITCH*)wI->sw[sub + 2].Switch)->text,ai->guildName);
					break;
				case EnumMenuWindowAddressBookTypeGuildBook:
					cc = MenuAddressBookGetYourCommition (guildBook.member[top].titleId, GUILD_FLAG_MASTER);
					if(guildBook.member[top].titleId != -1){
						strcpy(((TEXT_SWITCH*)wI->sw[sub + 2].Switch)->text,guildBook.title[guildBook.member[top].titleId].name);
					}else{
						strcpy(((TEXT_SWITCH*)wI->sw[sub + 2].Switch)->text,"");
					}
					break;
#ifdef PUK3_PROF
				case EnumMenuWindowAddressBookTypeMiniMail:
					strcpy(((TEXT_SWITCH*)wI->sw[sub + 2].Switch)->text,ai->guildName);
					break;
#endif
			}
			//タイトル
			strcpy(((TEXT_SWITCH*)wI->sw[sub + 3].Switch)->text,ai->freeName);
			//接続サーバー
			text = (TEXT_SWITCH*) wI->sw[sub + 4].Switch;
			if (ai->onlineFlag != -1 && ai->onlineFlag<=12){
				strcpy (text->text, gameServerNameList[ai->onlineFlag]);
				text->color=gameServerColorList[ai->onlineFlag];
			}else{
				strcpy (text->text, gameServerNameList[0]);
				text->color=gameServerColorList[0];
			}

			switch(menuWindowAddressBookType){
				case EnumMenuWindowAddressBookTypeAddressBook:

					//未読メールアニメ
					if (checkNoReadMailOnce (top)){
						graph = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookRecvSub0 + i * 8].Switch;
						if(menuWindowAddressBookFlashMode == 0){
							graph->graNo = GID_addressBookReceiveOn;
						}else{
							graph->graNo = GID_addressBookReceiveOver;
						}
					}
					break;
				case EnumMenuWindowAddressBookTypeGuildBook:

					//自分に除名权限があるか？
					if(MenuAddressBookGetMyCommition (GUILD_FLAG_DISMISS)){
						//あってもマスターは消せません
						if(cc){
							wI->sw[EnumGraphAddressBookDeleteSub0 + i * 8].Enabled=FALSE;
						}else{
							wI->sw[EnumGraphAddressBookDeleteSub0 + i * 8].Enabled=TRUE;
						}
					}else{
						//なくても自分だけは除名（脱退）できます
						if(guildBook.member[top].address.id==guildBook.pcMemberId){
							wI->sw[EnumGraphAddressBookDeleteSub0 + i * 8].Enabled=TRUE;
						}else{
							wI->sw[EnumGraphAddressBookDeleteSub0 + i * 8].Enabled=FALSE;
						}
					}

					//未読メールアニメ
					if (checkNoReadGuildMailOnce(top)){
						graph = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookRecvSub0 + i * 8].Switch;
						if(menuWindowAddressBookFlashMode == 0){
							graph->graNo = GID_addressBookReceiveOn;
						}else{
							graph->graNo = GID_addressBookReceiveOver;
						}
					}
					break;
#ifdef PUK3_PROF
				case EnumMenuWindowAddressBookTypeMiniMail:
					//未読メールアニメ
					if (checkNoReadMiniMailOnce (top)){
						graph = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookRecvSub0 + i * 8].Switch;
						if(menuWindowAddressBookFlashMode == 0){
							graph->graNo = GID_addressBookReceiveOn;
						}else{
							graph->graNo = GID_addressBookReceiveOver;
						}
					}


					break;
#endif
			}
		}else{
			wI->sw[EnumGraphAddressBookRecvSub0 + i * 8].Enabled
				= wI->sw[EnumGraphAddressBookSendSub0 + i * 8].Enabled
				= wI->sw[EnumGraphAddressBookDeleteSub0 + i * 8].Enabled 
				= FALSE;

			text = (TEXT_SWITCH*) wI->sw[sub + 0].Switch;
			strcpy (text->text, "");
			text = (TEXT_SWITCH*) wI->sw[sub + 1].Switch;
			strcpy (text->text, "");
			text = (TEXT_SWITCH*) wI->sw[sub + 2].Switch;
			strcpy (text->text, "");
			text = (TEXT_SWITCH*) wI->sw[sub + 3].Switch;
			strcpy (text->text, "");
			text = (TEXT_SWITCH*) wI->sw[sub + 4].Switch;
			strcpy (text->text, "");
		}
	}

}

//リストモード时の描画
void menuWindowAddressBookToList ()
{
	GRAPHIC_SWITCH *graph;
	TEXT_SWITCH* text;
	int i;
	int top, sub;
	ADDRESS_BOOK_INFO* ai;

	if (menuWindowAddressBookType == EnumMenuWindowAddressBookTypeGuildBook) {
		//家族の所属するサーバー名表示
		wI->sw[EnumGraphAddressBookGuildServer].Enabled=TRUE;

		text = (TEXT_SWITCH*) wI->sw[EnumGraphAddressBookGuildServer].Switch;
		if(guildBook.serverNo<=12){
			strcpy (text->text, GuildServerNameList[guildBook.serverNo]);
			text->color=GuildServerColorList[guildBook.serverNo];
		}else{
			strcpy (text->text, GuildServerNameList[0]);
			text->color=GuildServerColorList[0];
		}
	}else{
		//家族の所属するサーバー名非表示
		wI->sw[EnumGraphAddressBookGuildServer].Enabled=FALSE;
	}

	for (i = 0; i < AMOUNT_OF_LIST_VIEW; ++i) {
#ifdef PUK3_MAIL_ETC
		top = MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType] + i].index;
#else
		top = MenuAddressBookSortTable[menuWindowListViewLine + i].index;
#endif
		sub = EnumGraphAddressBookListName0 +	5 * i;
		wI->sw[EnumGraphAddressBookListRecvSub0 + i * 5].Enabled
			= wI->sw[EnumGraphAddressBookListSendSub0 + i * 5].Enabled
			= wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled
			= TRUE;

		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook:
				ai = &addressBook[top];
				wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled = TRUE;
				break;

			case EnumMenuWindowAddressBookTypeGuildBook:
				ai = &guildBook.member[top].address;

				//自分に除名权限があるか？
				if(MenuAddressBookGetMyCommition (GUILD_FLAG_DISMISS)){
					//あってもマスターは消せません
					if(MenuAddressBookGetYourCommition (guildBook.member[top].titleId, GUILD_FLAG_MASTER)){
						wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled=FALSE;
					}else{
						wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled=TRUE;
					}
				}else{
					//なくても自分だけは除名（脱退）できます
					if(guildBook.member[top].address.id==guildBook.pcMemberId){
						wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled=TRUE;
					}else{
						wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled=FALSE;
					}
				}
				break;
#ifdef PUK3_PROF
			case EnumMenuWindowAddressBookTypeMiniMail:
				ai = &miniMailBook[top];
				//wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled = TRUE;
				break;
#endif
		}

		if (ai->useFlag) {
			text = (TEXT_SWITCH*) wI->sw[sub + 0].Switch;
			strcpy (text->text, ai->name);
			text = (TEXT_SWITCH*) wI->sw[sub + 1].Switch;

			if (ai->onlineFlag != -1 && ai->onlineFlag<=12){
				strcpy (text->text, gameServerNameList[ai->onlineFlag]);
				text->color=gameServerColorList[ai->onlineFlag];
			}else{
				strcpy (text->text, gameServerNameList[0]);
				text->color=gameServerColorList[0];
			}

			switch(menuWindowAddressBookType){
				case EnumMenuWindowAddressBookTypeAddressBook:
				//アドレス
					if (checkNoReadMailOnce (top)) {
						graph = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookListRecvSub0 + i * 5].Switch;
						if(menuWindowAddressBookFlashMode == 0) {
							graph->graNo = GID_addressBookReceiveOn;
						}else{
							graph->graNo = GID_addressBookReceiveOver;
						}
					}
					break;
				case EnumMenuWindowAddressBookTypeGuildBook:
				//家族
					if (checkNoReadGuildMailOnce(top)){
						graph = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookListRecvSub0 + i * 5].Switch;
						if(menuWindowAddressBookFlashMode == 0) {
							graph->graNo = GID_addressBookReceiveOn;
						}else{
							graph->graNo = GID_addressBookReceiveOver;
						}
					}
					break;
#ifdef PUK3_PROF
				case EnumMenuWindowAddressBookTypeMiniMail:
				//アドレス
					if (checkNoReadMiniMailOnce (top)) {
						graph = (GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookListRecvSub0 + i * 5].Switch;
						if(menuWindowAddressBookFlashMode == 0) {
							graph->graNo = GID_addressBookReceiveOn;
						}else{
							graph->graNo = GID_addressBookReceiveOver;
						}
					}
					break;
#endif
			}
		
			
		}else{
			wI->sw[EnumGraphAddressBookListRecvSub0 + i * 5].Enabled
				= wI->sw[EnumGraphAddressBookListSendSub0 + i * 5].Enabled
				= wI->sw[EnumGraphAddressBookListDeleteSub0 + i * 5].Enabled
				= FALSE;

			text = (TEXT_SWITCH*) wI->sw[sub + 0].Switch;
			strcpy (text->text, "");
			text = (TEXT_SWITCH*) wI->sw[sub + 1].Switch;
			strcpy (text->text, "");
		}
	}
}

//モードにあわせて设定
void ChangeBookType(int ViewType,int BookType){

	//Ｖｉｅｗモード初期化
	ChangeViewModeInit(ViewType);
	//ブック种类初期化
	ChangeBookTypeInit(BookType);

}

//ディティール、リスト变更に关する初期化
void ChangeViewModeInit(int mode){

	int i;

	for (i = EnumGraphAddressBookGuildSetting; i < EnumGraphAddressBookGuildServer+1; ++i)
		wI->sw[i].Enabled = TRUE;

	switch(mode){
		case EnumMenuWindowAddressBookViewModeDetail:
			((GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookViewMode].Switch)->graNo = GID_addressBookDetailView;
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookDetail2+1; ++i)
				wI->sw[i].Enabled = TRUE;
			for (i = EnumGraphAddressBookListRecvSub0; i < EnumGraphAddressBookList8+1; ++i)
				wI->sw[i].Enabled = FALSE;
			break;

		case EnumMenuWindowAddressBookViewModeList:
			((GRAPHIC_SWITCH*) wI->sw[EnumGraphAddressBookViewMode].Switch)->graNo=GID_addressBookListView;
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookDetail2+1; ++i)
				wI->sw[i].Enabled = FALSE;
			for (i = EnumGraphAddressBookListRecvSub0; i < EnumGraphAddressBookList8+1; ++i)
				wI->sw[i].Enabled = TRUE;
			break;
	}

}

//アドレス、家族、ミニメール变更に关する初期化
void ChangeBookTypeInit(int type){

	int i;

	switch(type){
		case EnumMenuWindowAddressBookTypeAddressBook:
			//家族称号变更矢印は消す
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookRecvSub0; ++i)
				wI->sw[i].Enabled = FALSE;
#ifdef PUK3_PROF
			wI->sw[EnumGraphAddressBookMiniMailBar].Enabled = FALSE;
#endif
			//家族の所属するサーバー名非表示
			wI->sw[EnumGraphAddressBookGuildServer].Enabled=FALSE;

			//ここのスイッチだけ名刺、家族时に座标が违います
			wI->sw[EnumGraphAddressBookGuild0].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx-24;
			wI->sw[EnumGraphAddressBookGuild1].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx-24;
			wI->sw[EnumGraphAddressBookGuild2].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx-24;

			((GRAPHIC_SWITCH *) wI->sw[EnumGraphAddressBookAddressBook].Switch)->graNo=GID_addressBookAddressOff;

			//家族称号变更矢印は消す
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookRecvSub0; ++i)
				wI->sw[i].Enabled = FALSE;

			//ここのスイッチだけミニメール时に座标が违います
			wI->sw[EnumGraphAddressBookViewModePrev].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModePrev].ofx;
			wI->sw[EnumGraphAddressBookViewModePrev].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModePrev].ofy;
			wI->sw[EnumGraphAddressBookViewModeNext].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModeNext].ofx;
			wI->sw[EnumGraphAddressBookViewModeNext].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModeNext].ofy;
			wI->sw[EnumGraphAddressBookViewMode].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewMode].ofx;
			wI->sw[EnumGraphAddressBookViewMode].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewMode].ofy;
			break;

		case EnumMenuWindowAddressBookTypeGuildBook:

			((GRAPHIC_SWITCH *) wI->sw[EnumGraphAddressBookGuildBook].Switch)->graNo=GID_addressBookGuildOff;
#ifdef PUK3_PROF
			wI->sw[EnumGraphAddressBookMiniMailBar].Enabled = FALSE;
#endif
			//ここのスイッチだけ名刺、家族时に座标が违います
			wI->sw[EnumGraphAddressBookGuild0].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx;
			wI->sw[EnumGraphAddressBookGuild1].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx;
			wI->sw[EnumGraphAddressBookGuild2].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx;

			//ここのスイッチだけミニメール时に座标が违います
			wI->sw[EnumGraphAddressBookViewModePrev].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModePrev].ofx;
			wI->sw[EnumGraphAddressBookViewModePrev].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModePrev].ofy;
			wI->sw[EnumGraphAddressBookViewModeNext].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModeNext].ofx;
			wI->sw[EnumGraphAddressBookViewModeNext].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModeNext].ofy;
			wI->sw[EnumGraphAddressBookViewMode].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewMode].ofx;
			wI->sw[EnumGraphAddressBookViewMode].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewMode].ofy;
			break;

#ifdef PUK3_PROF
		case EnumMenuWindowAddressBookTypeMiniMail:
			//家族称号变更矢印は消す
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookRecvSub0; ++i)
				wI->sw[i].Enabled = FALSE;

			wI->sw[EnumGraphAddressBookMiniMailBar].Enabled = TRUE;

			wI->sw[EnumGraphAddressBookServer0].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookServer1].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookServer2].Enabled = FALSE;

			wI->sw[EnumGraphAddressBookListServer0].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer1].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer2].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer3].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer4].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer5].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer6].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer7].Enabled = FALSE;
			wI->sw[EnumGraphAddressBookListServer8].Enabled = FALSE;

			//家族の所属するサーバー名非表示
			wI->sw[EnumGraphAddressBookGuildServer].Enabled=FALSE;

			((GRAPHIC_SWITCH *) wI->sw[EnumGraphAddressBookMiniMail].Switch)->graNo=GID_AddressBookMiniMailButtonOff;

			//ここのスイッチだけ名刺、家族时に座标が违います
			wI->sw[EnumGraphAddressBookGuild0].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx-24;
			wI->sw[EnumGraphAddressBookGuild1].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx-24;
			wI->sw[EnumGraphAddressBookGuild2].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookGuild0].ofx-24;

			wI->sw[	EnumGraphAddressBookServer0].Enabled=FALSE;
			wI->sw[	EnumGraphAddressBookServer1].Enabled=FALSE;
			wI->sw[	EnumGraphAddressBookServer2].Enabled=FALSE;

			wI->sw[EnumGraphAddressBookGuildSetting].Enabled=FALSE;
			wI->sw[EnumGraphAddressBookSortKeyPrev].Enabled=FALSE;
			wI->sw[EnumGraphAddressBookSortKeyNext].Enabled=FALSE;
			wI->sw[EnumGraphAddressBookSortKey].Enabled=FALSE;
			wI->sw[EnumGraphAddressBookGroup].Enabled=FALSE;
			wI->sw[EnumGraphAddressBookGuildServer].Enabled=FALSE;

			wI->sw[EnumGraphAddressBookPrevPage].Enabled=TRUE;
			wI->sw[EnumGraphAddressBookNextPage].Enabled=TRUE;

			//家族称号变更矢印は消す
			for (i = EnumGraphAddressBookChangeTitle0; i < EnumGraphAddressBookRecvSub0; ++i)
				wI->sw[i].Enabled = FALSE;

			//ここのスイッチだけミニメール时に座标が违います
			wI->sw[EnumGraphAddressBookViewModePrev].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModePrev].ofx+110;
			wI->sw[EnumGraphAddressBookViewModePrev].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModePrev].ofy+17;
			wI->sw[EnumGraphAddressBookViewModeNext].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModeNext].ofx+110;
			wI->sw[EnumGraphAddressBookViewModeNext].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewModeNext].ofy+17;
			wI->sw[EnumGraphAddressBookViewMode].ofx=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewMode].ofx+110;
			wI->sw[EnumGraphAddressBookViewMode].ofy=MenuWindowAddressBookSwitch[EnumGraphAddressBookViewMode].ofy+17;
			break;
#endif
	}

}

//スクロール
BOOL MenuAddressBookScrollOneLine (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;
	int bottom;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if(no == EnumGraphAddressBookScrollUp){
		Graph->graNo =GID_addressBookScrollUpArrowOn;
	}else{
		Graph->graNo =GID_addressBookScrollDownArrowOn;
	}
	if(flag & MENU_MOUSE_OVER){
		if(no == EnumGraphAddressBookScrollUp){
			Graph->graNo =GID_addressBookScrollUpArrowOver;
		}else{
			Graph->graNo =GID_addressBookScrollDownArrowOver;
		}
	}
	if(flag & MENU_MOUSE_LEFTHOLD){
		if(no == EnumGraphAddressBookScrollUp){
			Graph->graNo =GID_UpButtonOff;
		}else{
			Graph->graNo =GID_DownButtonOff;
		}
	}
	if( flag & MENU_MOUSE_LEFTAUTO ){

#ifdef PUK3_MAIL_ETC
		if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){ 
			if(no == EnumGraphAddressBookScrollUp){
				menuWindowDetailViewLine[menuWindowAddressBookType] += -1;
			}else{
				menuWindowDetailViewLine[menuWindowAddressBookType] += 1;
			}
		}else{ 
			if(no == EnumGraphAddressBookScrollUp){
				menuWindowListViewLine[menuWindowAddressBookType]+=-1;
			}else{
				menuWindowListViewLine[menuWindowAddressBookType]+=1;
			}
		}
#else
		if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){ 
			if(no == EnumGraphAddressBookScrollUp){
				menuWindowDetailViewLine += -1;
			}else{
				menuWindowDetailViewLine += 1;
			}
		}else{ 
			if(no == EnumGraphAddressBookScrollUp){
				menuWindowListViewLine+=-1;
			}else{
				menuWindowListViewLine+=1;
			}
		}
#endif

		//音鸣らすためにここに
		//スクロールスイッチの时のみここで一回处理します。
		switch(menuWindowAddressBookViewMode){
			//ディティールモード
			case EnumMenuWindowAddressBookViewModeDetail:
				switch(menuWindowAddressBookType){
					//アドレスブック时
					case EnumMenuWindowAddressBookTypeAddressBook:
						bottom = ADDRESS_BOOK - AMOUNT_OF_DETAIL_VIEW;

#ifdef PUK3_MAIL_ETC
						if (menuWindowDetailViewLine[menuWindowAddressBookType] < 0){
							menuWindowDetailViewLine[menuWindowAddressBookType] = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowDetailViewLine[menuWindowAddressBookType] > bottom){
							menuWindowDetailViewLine[menuWindowAddressBookType] = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#else
						if (menuWindowDetailViewLine < 0){
							menuWindowDetailViewLine = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowDetailViewLine > bottom){
							menuWindowDetailViewLine = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#endif
						break;
					//家族ブック时
					case EnumMenuWindowAddressBookTypeGuildBook:
						if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
							bottom = guildBook.memberCount - AMOUNT_OF_DETAIL_VIEW;
						}else{
							bottom = GUILD_MEMBER_OPEN_MAX - AMOUNT_OF_DETAIL_VIEW;
						}

#ifdef PUK3_MAIL_ETC
						if (menuWindowDetailViewLine[menuWindowAddressBookType] < 0){
							menuWindowDetailViewLine[menuWindowAddressBookType] = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowDetailViewLine[menuWindowAddressBookType] > bottom){
							menuWindowDetailViewLine[menuWindowAddressBookType] = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#else
						if (menuWindowDetailViewLine < 0){
							menuWindowDetailViewLine = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowDetailViewLine > bottom){
							menuWindowDetailViewLine = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#endif
						break;
#ifdef PUK3_PROF
					//ミニメール时
					case EnumMenuWindowAddressBookTypeMiniMail:
						bottom = MINI_MAIL_STOCK_MAX - AMOUNT_OF_DETAIL_VIEW;

	#ifdef PUK3_MAIL_ETC
						if (menuWindowDetailViewLine[menuWindowAddressBookType] < 0){
							menuWindowDetailViewLine[menuWindowAddressBookType] = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowDetailViewLine[menuWindowAddressBookType] > bottom){
							menuWindowDetailViewLine[menuWindowAddressBookType] = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
	#else
						if (menuWindowDetailViewLine < 0){
							menuWindowDetailViewLine = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowDetailViewLine > bottom){
							menuWindowDetailViewLine = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
	#endif
						break;
#endif
				}
			break;

			//リストモード
			case EnumMenuWindowAddressBookViewModeList:
				switch(menuWindowAddressBookType){
					//アドレスブック时
					case EnumMenuWindowAddressBookTypeAddressBook:
						bottom = ADDRESS_BOOK - AMOUNT_OF_LIST_VIEW;

#ifdef PUK3_MAIL_ETC
						if (menuWindowListViewLine[menuWindowAddressBookType] < 0){
							menuWindowListViewLine[menuWindowAddressBookType] = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowListViewLine[menuWindowAddressBookType] > bottom){
							menuWindowListViewLine[menuWindowAddressBookType] = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#else
						if (menuWindowListViewLine < 0){
							menuWindowListViewLine = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowListViewLine > bottom){
							menuWindowListViewLine = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#endif
						break;
					//家族ブック时
					case EnumMenuWindowAddressBookTypeGuildBook:
						if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
							bottom = guildBook.memberCount - AMOUNT_OF_LIST_VIEW;
						}else{
							bottom = GUILD_MEMBER_OPEN_MAX - AMOUNT_OF_LIST_VIEW;
						}

#ifdef PUK3_MAIL_ETC
						if (menuWindowListViewLine[menuWindowAddressBookType] < 0){
							menuWindowListViewLine[menuWindowAddressBookType] = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowListViewLine[menuWindowAddressBookType] > bottom){
							menuWindowListViewLine[menuWindowAddressBookType] = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#else
						if (menuWindowListViewLine < 0){
							menuWindowListViewLine = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowListViewLine > bottom){
							menuWindowListViewLine = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
#endif
						break;
#ifdef PUK3_PROF
					//ミニメール时
					case EnumMenuWindowAddressBookTypeMiniMail:
						bottom = MINI_MAIL_STOCK_MAX - AMOUNT_OF_LIST_VIEW;

	#ifdef PUK3_MAIL_ETC
						if (menuWindowListViewLine[menuWindowAddressBookType] < 0){
							menuWindowListViewLine[menuWindowAddressBookType] = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowListViewLine[menuWindowAddressBookType] > bottom){
							menuWindowListViewLine[menuWindowAddressBookType] = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
	#else
						if (menuWindowListViewLine < 0){
							menuWindowListViewLine = 0;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else if (menuWindowListViewLine > bottom){
							menuWindowListViewLine = bottom;
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							play_se( SE_NO_CLICK, 320, 240 );
						}
	#endif
						break;
#endif
				}
			break;
		}

		MenuWindowAddressBookSyncViewAndTab ();
		saveNowState ();

		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//ボタン群
BOOL MenuAddressBookButton (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
#ifdef PUK3_PROF
	int BookTypeFlag,index;
#endif

	switch(no){
		//ソート种类变更←
		case EnumGraphAddressBookSortKeyPrev:
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPagePrevOver;
				strcpy( OneLineInfoStr, MWONELINE_CARD_SORT );
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPagePrevOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				menuWindowAddressBookSortKey+=-1;
				menuWindowAddressBookSortKey = (menuWindowAddressBookSortKey + 5) % 5;
				addressBookSortMode = menuWindowAddressBookSortKey;
				saveNowState ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPagePrevOff;
			}			
			break;

		//ソート种类变更→
		case EnumGraphAddressBookSortKeyNext:
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPageNextOver;
				strcpy( OneLineInfoStr, MWONELINE_CARD_SORT );
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPageNextOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				menuWindowAddressBookSortKey+=1;
				menuWindowAddressBookSortKey = (menuWindowAddressBookSortKey + 5) % 5;
				addressBookSortMode = menuWindowAddressBookSortKey;
				saveNowState ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPageNextOff;
			}			
			break;

		//VIEWモード变更←
		case EnumGraphAddressBookViewModePrev:
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo = GID_addressBookPagePrevOver;
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) 
					strcpy( OneLineInfoStr, MWONELINE_CARD_LIST );
				else 
					strcpy( OneLineInfoStr, MWONELINE_CARD_DETAIL );
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPagePrevOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) {
					menuWindowAddressBookViewMode	= EnumMenuWindowAddressBookViewModeList;
				}else{
					menuWindowAddressBookViewMode	= EnumMenuWindowAddressBookViewModeDetail;
				}
				ChangeBookType(menuWindowAddressBookViewMode,menuWindowAddressBookType);

				//スクロール位置初期化
	#ifdef PUK3_MAIL_ETC
				menuWindowListViewLine[menuWindowAddressBookType]=0;
				menuWindowDetailViewLine[menuWindowAddressBookType]=0;
	#else
				menuWindowListViewLine=0;
				menuWindowDetailViewLine=0;
	#endif
				MenuWindowAddressBookSyncViewAndTab();
				saveNowState ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =  GID_addressBookPagePrevOff;
			}
			break;

		//VIEWモード变更→
		case EnumGraphAddressBookViewModeNext:
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo = GID_addressBookPageNextOver;
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) 
					strcpy( OneLineInfoStr, MWONELINE_CARD_LIST );
				else 
					strcpy( OneLineInfoStr, MWONELINE_CARD_DETAIL );
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookPageNextOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) {
					menuWindowAddressBookViewMode	= EnumMenuWindowAddressBookViewModeList;
				}else{
					menuWindowAddressBookViewMode	= EnumMenuWindowAddressBookViewModeDetail;
				}
				ChangeBookType(menuWindowAddressBookViewMode,menuWindowAddressBookType);

				//スクロール位置初期化
	#ifdef PUK3_MAIL_ETC
				menuWindowListViewLine[menuWindowAddressBookType]=0;
				menuWindowDetailViewLine[menuWindowAddressBookType]=0;
	#else
				menuWindowListViewLine=0;
				menuWindowDetailViewLine=0;
	#endif
				MenuWindowAddressBookSyncViewAndTab();
				saveNowState ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =  GID_addressBookPageNextOff;
			}
			break;

		//ページ←
		case EnumGraphAddressBookPrevPage:	
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =GID_addressBookPagePrevOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =GID_addressBookPagePrevOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
	#ifdef PUK3_MAIL_ETC
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) {
					menuWindowDetailViewLine[menuWindowAddressBookType] +=-AMOUNT_OF_DETAIL_VIEW;
				}else{ 
					menuWindowListViewLine[menuWindowAddressBookType] +=-AMOUNT_OF_LIST_VIEW;
				}
	#else
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) {
					menuWindowDetailViewLine +=-AMOUNT_OF_DETAIL_VIEW;
				}else{ 
					menuWindowListViewLine +=-AMOUNT_OF_LIST_VIEW;
				}
	#endif
				MenuWindowAddressBookSyncViewAndTab ();
				saveNowState ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =  GID_addressBookPagePrevOff;
			}
		break;

		//ページ→
		case EnumGraphAddressBookNextPage:	
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =GID_addressBookPageNextOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =GID_addressBookPageNextOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
	#ifdef PUK3_MAIL_ETC
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) {
					menuWindowDetailViewLine[menuWindowAddressBookType] +=AMOUNT_OF_DETAIL_VIEW;
				}else{ 
					menuWindowListViewLine[menuWindowAddressBookType] +=AMOUNT_OF_LIST_VIEW;
				}
	#else
				if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) {
					menuWindowDetailViewLine +=AMOUNT_OF_DETAIL_VIEW;
				}else{ 
					menuWindowListViewLine +=AMOUNT_OF_LIST_VIEW;
				}
	#endif
				MenuWindowAddressBookSyncViewAndTab ();
				saveNowState ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo =  GID_addressBookPageNextOff;
			}
		break;

#ifdef PUK3_PROF
		//プロフィール表示（ディティール时）
		case EnumGraphAddressBookDetail0:
		case EnumGraphAddressBookDetail1:
		case EnumGraphAddressBookDetail2:

			if( flag & MENU_MOUSE_RIGHT ){
				switch(menuWindowAddressBookType){
					case EnumMenuWindowAddressBookTypeAddressBook:
						BookTypeFlag=PROFILE_ADDRESS;
						index=MenuAddressBookGetTargetNumberFromPanel(no);					
						if(addressBook[index].useFlag==FALSE)
							return TRUE;
						nrproto_PRD_send(sockfd,BookTypeFlag,index);
						break;

					case EnumMenuWindowAddressBookTypeGuildBook:
						BookTypeFlag=PROFILE_GUILD;
						index=MenuAddressBookGetTargetNumberFromPanel(no);				
						if(guildBook.member[index].address.useFlag==FALSE)
							return TRUE;
						nrproto_PRD_send(sockfd,BookTypeFlag,guildBook.member[index].address.id);
						break;
					case EnumMenuWindowAddressBookTypeMiniMail:
						BookTypeFlag=PROFILE_PROFILE;
						index=MenuAddressBookGetTargetNumberFromPanel(no);				
						if(!miniMailBook[index].useFlag)
							return TRUE;
						nrproto_PRD_send(sockfd,BookTypeFlag,miniMailBook[index].id);
						break;
				}
			}
			break;
#endif
	}

	return ReturnFlag;
}

//家族成员のタイトル变更ボタン
BOOL MenuAddressBookChangeTitle (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;
	int buttonType = (no - EnumGraphAddressBookChangeTitle0) % 2;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	if(buttonType == 0){
		Graph->graNo=GID_addressBookPagePrevOn;
	}else{
		Graph->graNo=GID_addressBookPageNextOn;
	}

	if(flag & MENU_MOUSE_OVER){
		if(buttonType == 0){
			Graph->graNo=GID_addressBookPagePrevOver;
		}else{
			Graph->graNo=GID_addressBookPageNextOver;
		}
		strcpy( OneLineInfoStr, MWONELINE_CARD_TITLE );
	}

	if( flag & MENU_MOUSE_LEFTAUTO ){
		if(buttonType == 0){
			Graph->graNo=GID_addressBookPagePrevOff;
		}else{
			Graph->graNo=GID_addressBookPageNextOff;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		MenuAddressBookSetNewTitle (no);
		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		if(buttonType == 0){
			Graph->graNo =  GID_addressBookPagePrevOff;
		}else{
			Graph->graNo =  GID_addressBookPageNextOff;
		}
	}

	return ReturnFlag;
}

//群邮件送信
BOOL MenuAddressBookGroupMail (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_addressBookGroupOn;

	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_addressBookGroupOver;
		strcpy( OneLineInfoStr, MWONELINE_CARD_GROUP );
	}
	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo = GID_addressBookGroupOff;
		//すでに开いている时はなにもしない
		if(WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo==NULL){
			// メール送信ウインドウ
			MailStatusST.GroupMailFlag=1;
			MailStatusST.Mode=EnumMenuWindowAddressBookTypeAddressBook;
			openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			play_se( SE_NO_CLICK, 320, 240 );
		}
		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo = GID_addressBookGroupOff;
	}
	
	return ReturnFlag;
}

//设定值の调整
static void MenuWindowAddressBookSyncViewAndTab ()
{
	int tab, bottom;

	switch(menuWindowAddressBookViewMode){
		//ディティールモード
		case EnumMenuWindowAddressBookViewModeDetail:
	#ifdef PUK3_MAIL_ETC
			if (menuWindowDetailViewLine[menuWindowAddressBookType] < 0)
				menuWindowDetailViewLine[menuWindowAddressBookType] = 0;
	#else
			if (menuWindowDetailViewLine < 0)
				menuWindowDetailViewLine = 0;
	#endif

			switch(menuWindowAddressBookType){
				//アドレスブック时
				case EnumMenuWindowAddressBookTypeAddressBook:
					bottom = ADDRESS_BOOK - AMOUNT_OF_DETAIL_VIEW;
	#ifdef PUK3_MAIL_ETC
					if (menuWindowDetailViewLine[menuWindowAddressBookType] > bottom)
						menuWindowDetailViewLine[menuWindowAddressBookType] = bottom;
	#else
					if (menuWindowDetailViewLine > bottom)
						menuWindowDetailViewLine = bottom;
	#endif
					break;
				//家族ブック时
				case EnumMenuWindowAddressBookTypeGuildBook:
					if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
						bottom = guildBook.memberCount - AMOUNT_OF_DETAIL_VIEW;
					}else{
						bottom = GUILD_MEMBER_OPEN_MAX - AMOUNT_OF_DETAIL_VIEW;
					}
	#ifdef PUK3_MAIL_ETC
					if (menuWindowDetailViewLine[menuWindowAddressBookType] > bottom)
						menuWindowDetailViewLine[menuWindowAddressBookType] = bottom;
	#else
					if (menuWindowDetailViewLine > bottom)
						menuWindowDetailViewLine = bottom;
	#endif
					break;
#ifdef PUK3_PROF
				//ミニメール时
				case EnumMenuWindowAddressBookTypeMiniMail:
					bottom = MINI_MAIL_STOCK_MAX - AMOUNT_OF_DETAIL_VIEW;
	#ifdef PUK3_MAIL_ETC
					if (menuWindowDetailViewLine[menuWindowAddressBookType] > bottom)
						menuWindowDetailViewLine[menuWindowAddressBookType] = bottom;
	#else
					if (menuWindowDetailViewLine > bottom)
						menuWindowDetailViewLine = bottom;
	#endif
					break;
#endif
			}
			
	#ifdef PUK3_MAIL_ETC
			tab = menuWindowDetailViewLine[menuWindowAddressBookType];
	#else
			tab = menuWindowDetailViewLine;
	#endif
			break;

		//リストモード
		case EnumMenuWindowAddressBookViewModeList:
	#ifdef PUK3_MAIL_ETC
			if (menuWindowListViewLine[menuWindowAddressBookType] < 0)
				menuWindowListViewLine[menuWindowAddressBookType] = 0;
	#else
			if (menuWindowListViewLine < 0)
				menuWindowListViewLine = 0;
	#endif
			
			switch(menuWindowAddressBookType){
				//アドレスブック时
				case EnumMenuWindowAddressBookTypeAddressBook:
					bottom = ADDRESS_BOOK - AMOUNT_OF_LIST_VIEW;
	#ifdef PUK3_MAIL_ETC
					if (menuWindowListViewLine[menuWindowAddressBookType] > bottom)
						menuWindowListViewLine[menuWindowAddressBookType] = bottom;
	#else
					if (menuWindowListViewLine > bottom)
						menuWindowListViewLine = bottom;
	#endif
					break;
				//家族ブック时
				case EnumMenuWindowAddressBookTypeGuildBook:
					if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
						bottom = guildBook.memberCount - AMOUNT_OF_LIST_VIEW;
					}else{
						bottom = GUILD_MEMBER_OPEN_MAX - AMOUNT_OF_LIST_VIEW;
					}
	#ifdef PUK3_MAIL_ETC
					if (menuWindowListViewLine[menuWindowAddressBookType] > bottom)
						menuWindowListViewLine[menuWindowAddressBookType] = bottom;
	#else
					if (menuWindowListViewLine > bottom)
						menuWindowListViewLine = bottom;
	#endif
					break;
#ifdef PUK3_PROF
				//ミニメール时
				case EnumMenuWindowAddressBookTypeMiniMail:
					bottom = MINI_MAIL_STOCK_MAX - AMOUNT_OF_LIST_VIEW;
	#ifdef PUK3_MAIL_ETC
					if (menuWindowListViewLine[menuWindowAddressBookType] > bottom)
						menuWindowListViewLine[menuWindowAddressBookType] = bottom;
	#else
					if (menuWindowListViewLine > bottom)
						menuWindowListViewLine = bottom;
	#endif
					break;
#endif
			}
			
	#ifdef PUK3_MAIL_ETC
			tab = menuWindowListViewLine[menuWindowAddressBookType];
	#else
			tab = menuWindowListViewLine;
	#endif
			break;
	}

	NumToScrollVMove (&wI->sw[EnumGraphAddressBookScrollBar], bottom, tab);
}

//名刺or家族の切り替えスイッチ
BOOL MenuAddressBookTypeChange (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	switch(no){
		case EnumGraphAddressBookAddressBook:

			if(menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook)
				return FALSE;

			Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
			if(flag & MENU_MOUSE_OVER){
				Graph->graNo = GID_addressBookAddressOver;
				strcpy (OneLineInfoStr, MWONELINE_CARD_ADDRESS);
				ReturnFlag=TRUE;
			}else{
				Graph->graNo = GID_addressBookAddressOn;
			}

			Graph = (GRAPHIC_SWITCH *) wI->sw[EnumGraphAddressBookBack].Switch;
			if(flag & MENU_MOUSE_LEFT){
				Graph->graNo =GID_addressBookBack;
				wI->sw[EnumGraphAddressBookGuildSetting].Enabled = FALSE;
				menuWindowAddressBookType = EnumMenuWindowAddressBookTypeAddressBook;
				//スクロール位置初期化
	#ifdef PUK3_MAIL_ETC
	#else
				menuWindowListViewLine=0;
				menuWindowDetailViewLine=0;
	#endif
				MenuWindowAddressBookSyncViewAndTab();
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );

				ChangeBookType(menuWindowAddressBookViewMode,menuWindowAddressBookType);

				ReturnFlag=TRUE;
			}
			break;
		case EnumGraphAddressBookGuildBook:

			if(menuWindowAddressBookType == EnumMenuWindowAddressBookTypeGuildBook)
				return FALSE;

			Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
			if(flag & MENU_MOUSE_OVER){
				Graph->graNo = GID_addressBookGuildOver;
				strcpy (OneLineInfoStr, MWONELINE_CARD_GUILD);
				ReturnFlag=TRUE;
			}else{
				Graph->graNo = GID_addressBookGuildOn;
			}

			Graph = (GRAPHIC_SWITCH *) wI->sw[EnumGraphAddressBookBack].Switch;
			if(flag & MENU_MOUSE_LEFT){
				Graph->graNo = GID_addressBookGuildBack;
				wI->sw[EnumGraphAddressBookGuildSetting].Enabled = TRUE;
				menuWindowAddressBookType = EnumMenuWindowAddressBookTypeGuildBook;
				//スクロール位置初期化
	#ifdef PUK3_MAIL_ETC
	#else
				menuWindowListViewLine=0;
				menuWindowDetailViewLine=0;
	#endif
				MenuWindowAddressBookSyncViewAndTab();
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );

				ChangeBookType(menuWindowAddressBookViewMode,menuWindowAddressBookType);

				ReturnFlag=TRUE;
			}
			break;

#ifdef PUK3_PROF
		case EnumGraphAddressBookMiniMail:
			if(menuWindowAddressBookType == EnumMenuWindowAddressBookTypeMiniMail)
				return FALSE;

			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo = GID_AddressBookMiniMailButtonOver;
				strcpy (OneLineInfoStr, MWONELINE_MINIMAIL_MINIBOOK);
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo = GID_AddressBookMiniMailButtonOn;
			}

			if(flag & MENU_MOUSE_LEFT){

				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo = GID_AddressBookMiniMailButtonOff;
				wI->sw[EnumGraphAddressBookGuildSetting].Enabled = FALSE;
				menuWindowAddressBookType = EnumMenuWindowAddressBookTypeMiniMail;

				//スクロール位置初期化
	#ifdef PUK3_MAIL_ETC
	#else
				menuWindowListViewLine=0;
				menuWindowDetailViewLine=0;
	#endif
				MenuWindowAddressBookSyncViewAndTab();
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );

				ChangeBookType(menuWindowAddressBookViewMode,menuWindowAddressBookType);

				ReturnFlag=TRUE;
			}
			break;
#endif
	}
	return ReturnFlag;
}

//ヒストリー开く
BOOL MenuAddressBookHistory (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_addressBookReceiveOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo=GID_addressBookReceiveOver;
		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook:
					strcpy( OneLineInfoStr, MWONELINE_CARD_HISTORY );
					break;

			case EnumMenuWindowAddressBookTypeGuildBook:
					strcpy( OneLineInfoStr, MWONELINE_CARD_GUILDHISTORY );
					break;
#ifdef PUK3_PROF
				case EnumMenuWindowAddressBookTypeMiniMail:
					strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_HISTORY );
					break;
#endif
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_addressBookReceiveOff;

		mailHistorySelectNo = MenuAddressBookGetTargetNumber (no);
		MenuHistoryWindowType=menuWindowAddressBookType;

		MailHistoryST.type=menuWindowAddressBookType;
		openMenuWindow( MENU_WINDOW_HISTORY, OPENMENUWINDOW_HIT, 0 );

		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_addressBookReceiveOff;
	}

	return ReturnFlag;
}

//家族详细ウインドウ开く
BOOL MenuAddressBookGuildSettingOpen (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_addressBookGuildSettingOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo=GID_addressBookGuildSettingOver;
		strcpy( OneLineInfoStr, MWONELINE_CARD_GUILDDETAIL );
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_addressBookGuildSettingOff;

		openMenuWindow( MENU_WINDOW_GUILDINFO, OPENMENUWINDOW_HIT, 0 );

		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_addressBookGuildSettingOff;
	}

	return ReturnFlag;
}

void MenuWindowAddressBookSort ()
{
	int i;
	int len;
	
	for (i = 0; i < GUILD_MEMBER_MAX; ++i)
		MenuAddressBookSortTable[i].index = i;

	switch(menuWindowAddressBookType){
		case EnumMenuWindowAddressBookTypeAddressBook:
			len = ADDRESS_BOOK;
			break;
		case EnumMenuWindowAddressBookTypeGuildBook:
			len = GUILD_MEMBER_MAX;
			break;
#ifdef PUK3_PROF
		case EnumMenuWindowAddressBookTypeMiniMail:
			len = MINI_MAIL_STOCK_MAX;
			break;
#endif
	}

	qsort (MenuAddressBookSortTable, len, sizeof (ADDRESS_BOOK_SORT_TBL), 
		MenuWindowAddressBookCompare);
}

int MenuWindowAddressBookCompare (const void* _pt1, const void* _pt2)
{
	ADDRESS_BOOK_SORT_TBL* pt1 = (ADDRESS_BOOK_SORT_TBL*) _pt1;
	ADDRESS_BOOK_SORT_TBL* pt2 = (ADDRESS_BOOK_SORT_TBL*) _pt2;

	switch(menuWindowAddressBookType){
		case EnumMenuWindowAddressBookTypeAddressBook:
			if (addressBook[pt2->index].useFlag == 0)
				return -1;
			if (addressBook[pt1->index].useFlag == 0)
				return 1;
			break;
		case EnumMenuWindowAddressBookTypeGuildBook:
			if (guildBook.member[pt2->index].address.useFlag == 0)
				return -1;
			if (guildBook.member[pt1->index].address.useFlag == 0)
				return 1;
			break;
#ifdef PUK3_PROF
		case EnumMenuWindowAddressBookTypeMiniMail:
			if (miniMailBook[pt2->index].useFlag == 0)
				return -1;
			if (miniMailBook[pt1->index].useFlag == 0)
				return 1;
			break;
#endif
	}

#ifdef PUK3_PROF
	if(menuWindowAddressBookType==EnumMenuWindowAddressBookTypeMiniMail){
		return sortKeyForMiniMail(&miniMailBook[pt1->index], &miniMailBook[pt2->index]);
	}else{
#endif
		switch (addressBookSortMode) {
			case 0 :
				return (menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook)
					? sortKeyAlphabet (&addressBook[pt1->index], &addressBook[pt2->index])
					: sortKeyAlphabet (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			case 1 :
				return (menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook)
					?	sortKeyGuild (&addressBook[pt1->index], &addressBook[pt2->index])
					: sortKeyTitle (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			case 2 :
				return (menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook)
					? sortKeyLevel (&addressBook[pt1->index], &addressBook[pt2->index])
					: sortKeyLevel (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			case 3 :
				return (menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook)
					? sortKeyRegist (&addressBook[pt1->index], &addressBook[pt2->index])
					: sortKeyRegist (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			case 4 :
				return (menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook)
					? sortKeyServer (&addressBook[pt1->index], &addressBook[pt2->index])
					: sortKeyServer (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);

			}
#ifdef PUK3_PROF
	}
#endif

	return 0;
}

//ソート处理
int sortKeyAlphabet (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2){
	int cc = strcmp (a1->name, a2->name);
	
	return (cc != 0) ? cc : (a1->id > a2->id) ? 1 : -1;
}

int sortKeyAlphabet (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2){
	int cc = strcmp (a1->address.name, a2->address.name);

	return (cc != 0) ? cc : (a1->joinDate > a2->joinDate) ? 1 : -1;
}

int sortKeyTitle (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2){
	int cc = (a1->titleId - a2->titleId);

	return (cc != 0) ? cc : (a1->joinDate > a2->joinDate) ? 1 : -1;
}

int sortKeyGuild (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2){
	int cc = strcmp (a1->guildName, a2->guildName);

	return (cc != 0) ? cc : (a1->id > a2->id) ? 1 : -1;
}

int sortKeyLevel (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2){
	int cc = (a1->lv - a2->lv);

	return (cc != 0) ? cc : (a1->id > a2->id) ? 1 : -1;
}

int sortKeyLevel (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2){
	int cc = (a1->address.lv - a2->address.lv);

	return (cc != 0) ? cc : (a1->joinDate > a2->joinDate) ? 1 : -1;
}

int sortKeyRegist (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2){
	return (a1->id > a2->id) ? 1 : -1;
}

int sortKeyRegist (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2){
	return (a1->joinDate > a2->joinDate) ? 1 : -1;
}

int sortKeyServer (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2){
	int s1, s2;
	int cc;

	s1 = (a1->onlineFlag == 0) ? INT_MAX : a1->onlineFlag;
	s2 = (a2->onlineFlag == 0) ? INT_MAX : a2->onlineFlag;
	cc = s1 - s2;

	return (cc != 0) ? cc : (a1->id > a2->id) ? 1 : -1;
}

#ifdef PUK3_PROF
int sortKeyForMiniMail (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2){
	int cc = (a1->onlineFlag - a2->onlineFlag);

	return (cc != 0) ? cc : (a1->id > a2->id) ? 1 : -1;
}
#endif

int sortKeyServer (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2){
	int s1, s2;
	int cc;

	s1 = (a1->address.onlineFlag == 0) ? INT_MAX : a1->address.onlineFlag;
	s2 = (a2->address.onlineFlag == 0) ? INT_MAX : a2->address.onlineFlag;
	cc = s1 - s2;

	return (cc != 0) ? cc : (a1->joinDate > a2->joinDate) ? 1 : -1;
}

static BOOL MenuWindowAddressBookMailSignalMode ()
{
	menuWindowAddressBookFlashCounter 
		= (menuWindowAddressBookFlashCounter + 1) % MENU_WINDOW_ADDRESSBOOK_FLASH_TIME;
	
	if (menuWindowAddressBookFlashCounter == 0)
		menuWindowAddressBookFlashMode ^= 1;

	return menuWindowAddressBookFlashMode;
}

//削除ボタン
BOOL MenuAddressBookDelete (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_addressBookDeleteOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo=GID_addressBookDeleteOver;

		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook: 
				strcpy( OneLineInfoStr, MWONELINE_CARD_DELETE );
				break;

			case EnumMenuWindowAddressBookTypeGuildBook: 
				strcpy( OneLineInfoStr, MWONELINE_CARD_GUILDDELETE );
				break;
#ifdef PUK3_PROF
			case EnumMenuWindowAddressBookTypeMiniMail: 
				strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_DELETE );
				break;
#endif
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		addressBookDelNo = MenuAddressBookGetTargetNumber (no);
		
		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook: 
				MenuAddressBookDeleteFromAddressBook (addressBookDelNo);
				break;

			case EnumMenuWindowAddressBookTypeGuildBook: 
				MenuAddressBookDeleteFromGuildMember (addressBookDelNo);
				break;
#ifdef PUK3_PROF
			case EnumMenuWindowAddressBookTypeMiniMail: 
				MenuAddressBookDeleteFromMiniMailMember (addressBookDelNo);
				break;
#endif
		}
		
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_addressBookDeleteOff;
	}
	return ReturnFlag;
}

static void MenuAddressBookDeleteFromAddressBook (int delNo){

	ADDRESS_BOOK_INFO* ai = &addressBook[delNo];
	char text[1024];

	multiPurposeWindowInfo.type = EnumWithYesNoButton;
	multiPurposeWindowInfo.size = EnumMultiPurposeTiny;
	multiPurposeWindowInfo.x = multiPurposeWindowInfo.y = 0;
	multiPurposeWindowInfo.funcInitialize = NULL;
	multiPurposeWindowInfo.funcOk = multiPurposeWindowInfo.funcCancel 
		= multiPurposeWindowInfo.funcPrev = multiPurposeWindowInfo.funcNext = NULL;
	multiPurposeWindowInfo.funcYes = MenuAddressBookDeleteOk;
	multiPurposeWindowInfo.funcNo = MenuAddressBookDeleteCancel;
	sprintf (text, ML_STRING(817, " \n 从好友列表删除\n　\n　 %s\n　\n　　\n按住CTRL\n 点击『是』\n　"), ai->name);
	strcpy (multiPurposeWindowInfo.content, text);
	openMenuWindow( MENU_WINDOW_MULTIPURPOSE, OPENMENUWINDOW_HIT, 0 );
}

static void MenuAddressBookDeleteFromGuildMember (int delNo){

	ADDRESS_BOOK_INFO* ai = &guildBook.member[delNo].address;
	char text[1024];

	multiPurposeWindowInfo.type = EnumWithYesNoButton;
	multiPurposeWindowInfo.size = EnumMultiPurposeTiny;
	multiPurposeWindowInfo.x = multiPurposeWindowInfo.y = 0;
	multiPurposeWindowInfo.funcInitialize = NULL;
	multiPurposeWindowInfo.funcOk = multiPurposeWindowInfo.funcCancel 
		= multiPurposeWindowInfo.funcPrev = multiPurposeWindowInfo.funcNext = NULL;
	multiPurposeWindowInfo.funcYes = MenuAddressBookDeleteFromGuild;
	multiPurposeWindowInfo.funcNo = MenuAddressBookDeleteCancel;
	sprintf (text, ML_STRING(818, " \n 从家族名单删除\n　\n　 %s\n　\n　　\n按住CTRL\n 点击『是』\n　"), ai->name);
	strcpy (multiPurposeWindowInfo.content, text);
	openMenuWindow( MENU_WINDOW_MULTIPURPOSE, OPENMENUWINDOW_HIT, 0 );
}

#ifdef PUK3_PROF
static void MenuAddressBookDeleteFromMiniMailMember (int delNo){

	ADDRESS_BOOK_INFO* ai = &miniMailBook[delNo];
	char text[1024];

	multiPurposeWindowInfo.type = EnumWithYesNoButton;
	multiPurposeWindowInfo.size = EnumMultiPurposeTiny;
	multiPurposeWindowInfo.x = multiPurposeWindowInfo.y = 0;
	multiPurposeWindowInfo.funcInitialize = NULL;
	multiPurposeWindowInfo.funcOk = multiPurposeWindowInfo.funcCancel 
		= multiPurposeWindowInfo.funcPrev = multiPurposeWindowInfo.funcNext = NULL;
	multiPurposeWindowInfo.funcYes = MenuAddressBookDeleteFromMiniMail;
	multiPurposeWindowInfo.funcNo = MenuAddressBookDeleteCancel;
	sprintf (text, ML_STRING(819, " \n 从迷你邮件删除\n　\n　 %s\n　\n　　\n按住CTRL\n 点击『是』\n　"), ai->name);
	strcpy (multiPurposeWindowInfo.content, text);
	openMenuWindow( MENU_WINDOW_MULTIPURPOSE, OPENMENUWINDOW_HIT, 0 );
}
#endif
//アドレスの削除
BOOL MenuAddressBookDeleteOk (int no, unsigned int flag){

	// アドレス削除プロトコル送信
	nrproto_DAB_send( sockfd, addressBookDelNo );
	// 一人分のヒストリを削除
	delMailHistoryOnce( addressBookDelNo );
	// メールヒストリをファイルに保存
	writeMailFile();
	// 家族メールヒストリをファイルに保存
	writeGuildMailFile();
	// 决定音b（ボタンクリック时）
	play_se( SE_NO_OK2, 320, 240 );

	return TRUE;
}

//家族成员の削除
BOOL MenuAddressBookDeleteFromGuild (int no, unsigned int flag){

	GUILD_MEMBER_INFO	*member;
	int Index;
	member = guildBook.member;

	// 家族成员削除プロトコル送信
	Index=member[addressBookDelNo].address.id;
	nrproto_RGM_send (sockfd, Index);

	// メールヒストリをファイルに保存
	writeMailFile();
	// 家族メールヒストリをファイルに保存
	writeGuildMailFile();
	play_se (SE_NO_OK2, 320, 240);

	return TRUE;
}

#ifdef PUK3_PROF
//プロフィールの削除
BOOL MenuAddressBookDeleteFromMiniMail (int no, unsigned int flag){

	int id;
	char name[256];
	char sendName[256];

	id=miniMailBook[addressBookDelNo].id;
	strcpy(name,miniMailBook[addressBookDelNo].name);
	makeSendString( name, sendName, sizeof( sendName )-1 );

	//鲭へ削除要求プロトコル
	nrproto_PRE_send(sockfd,id,sendName);

	//ミニメールブックから削除
	deleteMiniMailBookMember(addressBookDelNo);

	play_se (SE_NO_OK2, 320, 240);
	
	return TRUE;
}
#endif

BOOL MenuAddressBookDeleteCancel (int no, unsigned int flag){

	return TRUE;
}

static int MenuAddressBookGetTargetNumber (int no){

	int button = (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail)
		? (no - EnumGraphAddressBookRecvSub0) / 8
		: (no - EnumGraphAddressBookListRecvSub0) / 5;

#ifdef PUK3_MAIL_ETC
	return (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) 
			? MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType] + button].index
			: MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType] + button].index;
#else
	return (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail) 
			? MenuAddressBookSortTable[menuWindowDetailViewLine + button].index
			: MenuAddressBookSortTable[menuWindowListViewLine + button].index;
#endif
}

static int MenuAddressBookGetTargetNumberFromPanel (int no){

	int num;

#ifdef PUK3_MAIL_ETC
	if(menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
		num=no-EnumGraphAddressBookDetail0;
		return MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType] + num].index;
	}else{
		num=no-EnumGraphAddressBookList0;
		return MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType] + num].index;
	}
#else
	if(menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
		num=no-EnumGraphAddressBookDetail0;
		return MenuAddressBookSortTable[menuWindowDetailViewLine + num].index;
	}else{
		num=no-EnumGraphAddressBookList0;
		return MenuAddressBookSortTable[menuWindowListViewLine + num].index;
	}
#endif
}

//スクロールバーの表示位置を格纳
static void MenuWindowAddressBookScrollBarTest (int mouse){

	int max;

	switch(menuWindowAddressBookType){
		case EnumMenuWindowAddressBookTypeAddressBook:
			max=ADDRESS_BOOK;
			break;
		case EnumMenuWindowAddressBookTypeGuildBook:
			if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
				max = guildBook.memberCount;
			}else{
				max = GUILD_MEMBER_OPEN_MAX;
			}
			break;
#ifdef PUK3_PROF
		case EnumMenuWindowAddressBookTypeMiniMail:
			max=MINI_MAIL_STOCK_MAX;
			break;
#endif
	}
	
#ifdef PUK3_MAIL_ETC
	if (((BUTTON_SWITCH*) wI->sw[EnumGraphAddressBookScrollBar].Switch)->status & 1) {
		if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
			menuWindowDetailViewLine[menuWindowAddressBookType] = ScrollVPointToNum (&wI->sw[EnumGraphAddressBookScrollBar], max-AMOUNT_OF_DETAIL_VIEW);
		}else{
			menuWindowListViewLine[menuWindowAddressBookType] = ScrollVPointToNum (&wI->sw[EnumGraphAddressBookScrollBar], max-AMOUNT_OF_LIST_VIEW);
		}
	}
#else
	if (((BUTTON_SWITCH*) wI->sw[EnumGraphAddressBookScrollBar].Switch)->status & 1) {
		if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
			menuWindowDetailViewLine = ScrollVPointToNum (&wI->sw[EnumGraphAddressBookScrollBar], max-AMOUNT_OF_DETAIL_VIEW);
		}else{
			menuWindowListViewLine = ScrollVPointToNum (&wI->sw[EnumGraphAddressBookScrollBar], max-AMOUNT_OF_LIST_VIEW);
		}
	}
#endif
}

//家族称号变更（マスターのみ）		
static void MenuAddressBookSetNewTitle (int no){

	int button = (no - EnumGraphAddressBookChangeTitle0) / 2;
	int direction = (no - EnumGraphAddressBookChangeTitle0) % 2;

#ifdef PUK3_MAIL_ETC
	ChangeGuildTitleTargetNum=getGuildNumFromID(guildBook.member[MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType] + button].index].address.id);
	ChangeGuildTitleTargetID=guildBook.member[MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType] + button].index].address.id;
	ChangeGuildTitle=guildBook.member[ChangeGuildTitleTargetNum].titleId-1;
#else
	ChangeGuildTitleTargetNum=getGuildNumFromID(guildBook.member[MenuAddressBookSortTable[menuWindowDetailViewLine + button].index].address.id);
	ChangeGuildTitleTargetID=guildBook.member[MenuAddressBookSortTable[menuWindowDetailViewLine + button].index].address.id;
	ChangeGuildTitle=guildBook.member[ChangeGuildTitleTargetNum].titleId-1;
#endif

	createMenuWindow( MENU_WINDOW_CHANGE_GUILD_TITLE );
}

static BOOL MenuAddressBookIsMeGuildMaster (){

	return MenuAddressBookGetMyCommition (GUILD_FLAG_MASTER);
}

static BOOL MenuAddressBookGetMyCommition (int commition){

	if (guildBook.pcGuildTitleId == -1)
		return FALSE;

	return ((guildBook.title[guildBook.pcGuildTitleId].flag & commition) != 0);
}

static BOOL MenuAddressBookGetYourCommition (int titleID, int commition){

	if (guildBook.pcGuildTitleId == -1)
		return FALSE;
	return ((guildBook.title[titleID].flag & commition) != 0);
}

BOOL MenuAddressBookListPanel (int no, unsigned int flag){

	ADDRESS_BOOK_INFO *ai;
	BLT_MEMBER bm={0};
	int No;
	BOOL RetuenFlag=FALSE;
	int target;
#ifdef PUK3_PROF
	int BookTypeFlag,index;
#endif

	No=no-EnumGraphAddressBookList0;
#ifdef PUK3_MAIL_ETC
	target=menuWindowListViewLine[menuWindowAddressBookType] + No;
#else
	target=menuWindowListViewLine + No;
#endif

	switch(menuWindowAddressBookType){

		case EnumMenuWindowAddressBookTypeAddressBook:
			if(!(0<=target && target<=ADDRESS_BOOK-1))
				return RetuenFlag;

			if(flag & MENU_MOUSE_OVER){
				ai =&addressBook[MenuAddressBookSortTable[target].index];  
				if(ai->useFlag){
						//颜描画
						bm.rgba.rgba=0xffffffff;
						bm.bltf=BLTF_NOCHG;
						//枠
						StockDispBuffer (wI->wx+wI->sw[no].ofx+150+64/2,
										 wI->wy+wI->sw[no].ofy+ 19+72/2,
							DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
						//颜
						StockDispBuffer (wI->wx+wI->sw[no].ofx+150+64/2,
										 wI->wy+wI->sw[no].ofy+ 19+72/2,
							DISP_PRIO_WIN2,getNewFaceGraphicNo (ai->graNo), 0,&bm);
						RetuenFlag=TRUE;
				}
			}
			break;

		case EnumMenuWindowAddressBookTypeGuildBook:
			if(!(0<=target && target<=guildBook.memberCount-1))
				return RetuenFlag;

			if(flag & MENU_MOUSE_OVER){
				ai =&guildBook.member[MenuAddressBookSortTable[target].index].address;  
				if(ai->useFlag){
						//颜描画
						bm.rgba.rgba=0xffffffff;
						bm.bltf=BLTF_NOCHG;
						//枠
						StockDispBuffer (wI->wx+wI->sw[no].ofx+150+64/2,
										 wI->wy+wI->sw[no].ofy+ 19+72/2,
							DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
						//颜
						StockDispBuffer (wI->wx+wI->sw[no].ofx+150+64/2,
										 wI->wy+wI->sw[no].ofy+ 19+72/2,
							DISP_PRIO_WIN2,getNewFaceGraphicNo (ai->graNo), 0,&bm);
						RetuenFlag=TRUE;
				}
			}
			break;

#ifdef PUK3_PROF
		case EnumMenuWindowAddressBookTypeMiniMail:
			if(flag & MENU_MOUSE_OVER){
				ai =&miniMailBook[MenuAddressBookSortTable[target].index];  
				if(ai->useFlag){
						//颜描画
						bm.rgba.rgba=0xffffffff;
						bm.bltf=BLTF_NOCHG;
						//枠
						StockDispBuffer (wI->wx+wI->sw[no].ofx+150+64/2,
										 wI->wy+wI->sw[no].ofy+ 19+72/2,
							DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
						//颜
						StockDispBuffer (wI->wx+wI->sw[no].ofx+150+64/2,
										 wI->wy+wI->sw[no].ofy+ 19+72/2,
							DISP_PRIO_WIN2,getNewFaceGraphicNo (ai->graNo), 0,&bm);
						RetuenFlag=TRUE;
				}
			}
			break;
#endif
	}

#ifdef PUK3_PROF
	if( flag & MENU_MOUSE_RIGHT ){
		//プロフィール表示（リスト时）
		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook:
				BookTypeFlag=PROFILE_ADDRESS;
				index=MenuAddressBookGetTargetNumberFromPanel(no);				
				if(addressBook[index].useFlag==FALSE)
					return TRUE;
				nrproto_PRD_send(sockfd,BookTypeFlag,index);
				break;
			case EnumMenuWindowAddressBookTypeGuildBook:
				BookTypeFlag=PROFILE_GUILD;
				index=MenuAddressBookGetTargetNumberFromPanel(no);				
				if(guildBook.member[index].address.useFlag==FALSE)
					return TRUE;
				nrproto_PRD_send(sockfd,BookTypeFlag,guildBook.member[index].address.id);
				break;
			case EnumMenuWindowAddressBookTypeMiniMail:
				BookTypeFlag=PROFILE_PROFILE;
				index=MenuAddressBookGetTargetNumberFromPanel(no);				
				if(miniMailBook[index].useFlag==FALSE)
					return TRUE;
				nrproto_PRD_send(sockfd,BookTypeFlag,miniMailBook[index].id);
				break;
		}
	}
#endif

	return RetuenFlag;
}

//-------------------------------------------------------------------------
//杉山
//-------------------------------------------------------------------------

extern struct MailStatusStruct MailStatusST;

//通常メール（アドレスメール、家族メール）
BOOL MenuAddressBookSendSwitch (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;	
	
	//重なり判定
	if(flag & MENU_MOUSE_OVER){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookSendOver;
		ReturnFlag=TRUE;
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_addressBookSendOn;
	}

	switch(no){
		//ディティールの时
		case EnumGraphAddressBookSendSub0:
		case EnumGraphAddressBookSendSub1:
		case EnumGraphAddressBookSendSub2:
			switch(menuWindowAddressBookType){
				case EnumMenuWindowAddressBookTypeAddressBook:

					if( flag & MENU_MOUSE_OVER ){
						strcpy( OneLineInfoStr, MWONELINE_CARD_SEND );
					}

					if( flag & MENU_MOUSE_LEFT ){
						//群邮件でない
						MailStatusST.GroupMailFlag=0;
						//通常メール
						MailStatusST.Mode=EnumMenuWindowAddressBookTypeAddressBook;
						//相手のIndex
						switch(no){
							case EnumGraphAddressBookSendSub0:
#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+0].index;
#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+0].index;
#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
							case EnumGraphAddressBookSendSub1:
#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+1].index;
#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+1].index;
#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
							case EnumGraphAddressBookSendSub2:
#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+2].index;
#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+2].index;
#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
						}
					}
				break;
				case EnumMenuWindowAddressBookTypeGuildBook:

					if( flag & MENU_MOUSE_OVER ){
						strcpy( OneLineInfoStr, MWONELINE_CARD_GUILD_SEND );
					}

					if( flag & MENU_MOUSE_LEFT ){
						//群邮件でない
						MailStatusST.GroupMailFlag=0;
						//家族メール
						MailStatusST.Mode=EnumMenuWindowAddressBookTypeGuildBook;
						//相手のIndex
						switch(no){
							case EnumGraphAddressBookSendSub0:
#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+0].index;
#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+0].index;
#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
							case EnumGraphAddressBookSendSub1:
#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+1].index;
#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+1].index;
#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
							case EnumGraphAddressBookSendSub2:
#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+2].index;
#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+2].index;
#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
						}
					}
				break;
#ifdef PUK3_PROF
				case EnumMenuWindowAddressBookTypeMiniMail:
					if( flag & MENU_MOUSE_OVER ){
						strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_SEND );
					}
					if( flag & MENU_MOUSE_LEFT ){
						//群邮件でない
						MailStatusST.GroupMailFlag=0;
						//ミニメール
						MailStatusST.Mode=EnumMenuWindowAddressBookTypeMiniMail;
						//相手のIndex
						switch(no){
							case EnumGraphAddressBookSendSub0:
	#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+0].index;
	#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+0].index;
	#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
							case EnumGraphAddressBookSendSub1:
	#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+1].index;
	#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+1].index;
	#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
							case EnumGraphAddressBookSendSub2:
	#ifdef PUK3_MAIL_ETC
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine[menuWindowAddressBookType]+2].index;
	#else
								MailStatusST.Index=MenuAddressBookSortTable[menuWindowDetailViewLine+2].index;
	#endif
								// メール送信ウインドウ
								openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
								ReturnFlag=TRUE;
								break;
						}
					}

					
				break;
#endif			
			}
			break;

		//リストの时
		case EnumGraphAddressBookListSendSub0:
		case EnumGraphAddressBookListSendSub1:
		case EnumGraphAddressBookListSendSub2:
		case EnumGraphAddressBookListSendSub3:
		case EnumGraphAddressBookListSendSub4:
		case EnumGraphAddressBookListSendSub5:
		case EnumGraphAddressBookListSendSub6:
		case EnumGraphAddressBookListSendSub7:
		case EnumGraphAddressBookListSendSub8:
			if( flag & MENU_MOUSE_OVER ){
				switch(menuWindowAddressBookType){
					case EnumMenuWindowAddressBookTypeAddressBook:
						strcpy( OneLineInfoStr, MWONELINE_CARD_SEND );
					break;

					case EnumMenuWindowAddressBookTypeGuildBook:
						strcpy( OneLineInfoStr, MWONELINE_CARD_GUILD_SEND );
					break;
#ifdef PUK3_PROF
					case EnumMenuWindowAddressBookTypeMiniMail:
						strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_SEND );
					break;
#endif
				}
			}

			if( flag & MENU_MOUSE_LEFT ){
				//群邮件でない
				MailStatusST.GroupMailFlag=0;
				//通常メールと家族メールのフラグ
				MailStatusST.Mode=menuWindowAddressBookType;
				//相手のIndex
				switch(no){
					case EnumGraphAddressBookListSendSub0:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+0].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+0].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub1:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+1].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+1].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub2:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+2].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+2].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub3:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+3].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+3].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub4:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+4].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+4].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub5:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+5].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+5].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub6:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+6].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+6].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub7:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+7].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+7].index;
#endif

						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
					case EnumGraphAddressBookListSendSub8:
#ifdef PUK3_MAIL_ETC
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine[menuWindowAddressBookType]+8].index;
#else
						MailStatusST.Index=MenuAddressBookSortTable[menuWindowListViewLine+8].index;
#endif
						// メール送信ウインドウ
						openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
						ReturnFlag=TRUE;
						break;
				}
			}
		break;
	}

	return ReturnFlag;
}

//ホイール处理
BOOL MenuAddressBookScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		int max;
		

		switch(menuWindowAddressBookType){
			case EnumMenuWindowAddressBookTypeAddressBook:
				max=ADDRESS_BOOK;
				break;

			case EnumMenuWindowAddressBookTypeGuildBook:
				if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
					max=guildBook.memberCount;
				}else{
					max=GUILD_MEMBER_OPEN_MAX;
				}
				break;

#ifdef PUK3_PROF
			case EnumMenuWindowAddressBookTypeMiniMail:
				max=MINI_MAIL_STOCK_MAX;
				break;
#endif

		}
#if 0		
		if(menuWindowAddressBookType == EnumMenuWindowAddressBookTypeAddressBook){
			max=ADDRESS_BOOK;
		}else{
			if(guildBook.memberCount>=GUILD_MEMBER_OPEN_MAX){
				max=guildBook.memberCount;
			}else{
				max=GUILD_MEMBER_OPEN_MAX;
			}
		}
#endif
	
#ifdef PUK3_MAIL_ETC
		// スクロールバー縦ホイール移动
		if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
			menuWindowDetailViewLine[menuWindowAddressBookType] = WheelToMove( &wI->sw[EnumGraphAddressBookScrollBar],
				 menuWindowDetailViewLine[menuWindowAddressBookType], max-AMOUNT_OF_DETAIL_VIEW, mouse.wheel );
		}else{
			menuWindowListViewLine[menuWindowAddressBookType] = WheelToMove( &wI->sw[EnumGraphAddressBookScrollBar],
				 menuWindowListViewLine[menuWindowAddressBookType], max-AMOUNT_OF_LIST_VIEW, mouse.wheel );
		}
#else
		// スクロールバー縦ホイール移动
		if (menuWindowAddressBookViewMode == EnumMenuWindowAddressBookViewModeDetail){
			menuWindowDetailViewLine = WheelToMove( &wI->sw[EnumGraphAddressBookScrollBar],
				 menuWindowDetailViewLine, max-AMOUNT_OF_DETAIL_VIEW, mouse.wheel );
		}else{
			menuWindowListViewLine = WheelToMove( &wI->sw[EnumGraphAddressBookScrollBar],
				 menuWindowListViewLine, max-AMOUNT_OF_LIST_VIEW, mouse.wheel );
		}
#endif
	}

	return ReturnFlag;
}

//---------------------------------------------------------------------------------
//家族称号の变更ウインドウ
//---------------------------------------------------------------------------------
//スクロールバーワーク
int ChangeGuildTitleBar;

//ウインドウ再描画
void MenuWindowChangeGuildTitleRedraw (void);

//ウインドウ处理
BOOL MenuWindowChangeGuildTitle (int mouse)
{

	//初期化
	if (mouse == WIN_INIT) {
		ChangeGuildTitleBar=0;
	}

	return TRUE;
}

//ウインドウ描画
BOOL MenuWindowChangeGuildTitleDraw (int mouse)
{

	MenuWindowChangeGuildTitleRedraw ();
	
	displayMenuWindow ();

	ChangeGuildTitleBar=ScrollVPointToNum(&wI->sw[EnumScrollChangeGuildTitle02],GUILD_TITLE_MAX-11);

	MenuWindowCommonDraw( GID_InfoWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

BOOL MenuWindowChangeGuildTitleDel(void){


	return TRUE;
}

//家族称号の选择
BOOL MenudChangeGuildTitleSelect (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;	
	int SwitchNum;

	SwitchNum=no-EnumGraphChangeGuildTitle00;

	if( flag & MENU_MOUSE_OVER ){
		strcpy( OneLineInfoStr, MWONELINE_GUILD_TITLE_SELECT );
		ReturnFlag=TRUE;	
	}

	if( flag & MENU_MOUSE_LEFT ){
		ChangeGuildTitle=SwitchNum+ChangeGuildTitleBar;
		ReturnFlag=TRUE;	
	}

	if(ChangeGuildTitle==SwitchNum+ChangeGuildTitleBar){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleTitleBackOff;
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleTitleBackOn;
	}


	return ReturnFlag;
}

//ウインドウのクローズ
BOOL MenudChangeGuildTitleClose (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_GUILD_TITLE_CLOSE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_ChangeGuildTitleCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_ChangeGuildTitleCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_ChangeGuildTitleCloseOff;

	return ReturnFlag;
}

//家族称号の决定
BOOL MenudChangeGuildTitleSet (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;	

	if( flag & MENU_MOUSE_OVER ){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleSetOver;
		strcpy( OneLineInfoStr, MWONELINE_GUILD_TITLE_SET );
		ReturnFlag=TRUE;	
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleSetOn;
	}

	if( flag & MENU_MOUSE_LEFT ){

		nrproto_BGT_send (sockfd, ChangeGuildTitleTargetID, ChangeGuildTitle+1);

		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}


	return ReturnFlag;
}

//スクロールバーのＴＯＰとＤＯＷＮ
BOOL MenudChangeGuildTitleScrollTopDown (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;	

	switch(no){
		//TOP
		case EnumScrollChangeGuildTitle03:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleScrollUPOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleScrollUPOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeGuildTitleBar--;
				if(ChangeGuildTitleBar<0)
					ChangeGuildTitleBar=0;
				//スクロールの位置修正
				NumToScrollVMove(&wI->sw[EnumScrollChangeGuildTitle02],GUILD_TITLE_MAX-11,ChangeGuildTitleBar);
			}
			break;

		//DOWN
		case EnumScrollChangeGuildTitle04:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleScrollDownOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_ChangeGuildTitleScrollDownOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeGuildTitleBar++;
				if(ChangeGuildTitleBar>GUILD_TITLE_MAX-11)
					ChangeGuildTitleBar=GUILD_TITLE_MAX-11;
				//スクロールの位置修正
				NumToScrollVMove(&wI->sw[EnumScrollChangeGuildTitle02],GUILD_TITLE_MAX-11,ChangeGuildTitleBar);
			}
			break;
	}

	return ReturnFlag;
}

//再描画
void MenuWindowChangeGuildTitleRedraw (void){

	int i;

	//家族称号の表示
	for(i=0;i<10;i++){
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextChangeGuildTitle00+i].Switch)->text,guildBook.title[i+ChangeGuildTitleBar+1].name);
	}

	//ターゲットの名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextChangeGuildTitleName].Switch)->text,
		guildBook.member[ChangeGuildTitleTargetNum].address.name);
}

BOOL MenuChangeGuildTitleScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) && mouse.wheel!=0){
		int max;

		max=GUILD_TITLE_MAX-11;
		// スクロールバー縦ホイール移动
		ChangeGuildTitleBar = WheelToMove( &wI->sw[EnumScrollChangeGuildTitle02],
			ChangeGuildTitleBar, max, mouse.wheel );
	}

	return ReturnFlag;
}
