﻿//メニュー＞システム

//====================================//
//		アイテムを扱うショップ		  //
//====================================//

extern char ItemNoOpe[MAX_ITEM];

static int ItemShopItemInfoNo=-1;
static int ItemShopInfoNo=-1;
static int ItemShopInfoPage=0;

static int ItemBoxOldPage[5];

static char ItemShopMode;
static int ItemShopDispStart;

static int ItemShoptotalPrice;
static int ItemShoptotalNum;

static int ItemShopNpcGra;

static int ItemShopEnpty;

static int ItemShopWindowType;

static char PcToItemShop[MAX_DRAW_WIN_ITEM];		// 对应表
static unsigned long PcSellTarget;					// アイテム栏のアイテムが、卖ものとして登録されているかを示すフラグ
static char ItemShopSellBuf[MAX_DRAW_WIN_ITEM];		// 卖もの

static struct ITEMSHOPINFO_DISPITEMDATA{
	int graNo;
	int num;
	int stack;
	int pal;
	char name[31];
	char useFlag;
} ItemShopInfoDispItem[MAX_DRAW_WIN_ITEM];

static struct ITEMSHOPINFO_DISPNUMDATA{
	int num;
	short x,y;
	BOOL useflag;
} ItemShopInfoDispNum[2];

static char ItemShopInfoDispStr1[100];
static char ItemShopInfoDispStr2[100];
static char ItemShopInfoDispOneLineYes[256];
static char ItemShopInfoDispOneLineNo[256];
static void (*ItemShopInfoFunc)( char Button );

extern int itemShopSellNpcGraNo;
extern ITEM_SHOP_INFO itemShopList[MAX_DRAW_WIN_ITEM];
extern int itemShopSellCount;
extern int itemShopSellPage;
extern int itemShopSellMaxPage;
extern int itemShopSellMsgNo;
extern int itemShopBuyCount;

extern int tradeItemId;	// 交换アイテムＩＤ
extern int tradeItemCnt;
extern char tradeItemName[ ITEM_NAME_LEN + 1 ];	// 交换アイテム名

extern ITEMCOUNT_NPC_INFO itemCountInfo;
extern int itemCountNpcMainGraNo;
extern int itemCountNpcMainType;
extern int itemCountNpcMsgNo;
extern int itemCountTotal;
extern int itemCountMaxCnt;

extern int itemInfoColor;

#define BER_STACK_NUM 20

#ifdef PUK2_NEWDRAG

void ItemShopSettleDropItemShop();

void DragShopItemFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};
	WINDOW_INFO *wininfo;

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// ドラッグ元にアイテムが无いならドラッグ終了
		if ( !itemShopList[ ItemShopSellBuf[itemNo] ].useFlag ) WinDD_DragFinish();
	
		// 右键したらアイテムドロップ
		if ( mouse.onceState & MOUSE_LEFT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_ITEMSHOP, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragShopItemFunc );
		}
		// 右クリックしたらドラッグ終了
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragCancel();
	
#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, itemShopList[ ItemShopSellBuf[itemNo] ].graNo, 0, &bm );
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		wininfo = wI;
		wI = WindowFlag[MENU_WINDOW_ITEMSHOP].wininfo;
		ItemShopSettleDropItemShop();
		wI = wininfo;
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
		break;
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
		break;
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		wininfo = wI;
		wI = WindowFlag[MENU_WINDOW_ITEMSHOP].wininfo;
		ItemShopSettleDropItemShop();
		wI = wininfo;
		break;
	}
}

void DragShopItem( int itemNo )
{
	// ドラッグ开始
	WinDD_DragStart( WINDD_ITEMSHOP, (void *)itemNo, DragShopItemFunc );
}

#endif

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

BOOL closeItemShopWindow()
{
	if ( ItemShopWindowType == serverRequestWinWindowType ) serverRequestWinWindowType = -1;
	if (ItemShopMode>0) for(int i=0;i<MAX_ITEM;i++) ItemNoOpe[i]--;

	return TRUE;
}

//==============================================
// 贩卖

ACTION *openItemShopBuyWindow( char *data )
{
	char str[768];
	int i, j, k;

	// ウィンドウモードを购入に设定
	ItemShopMode = 0;

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	// お金が足りないときの警告文
	getStringToken( data, '|', 4, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[5+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	// アイテム栏に空きがないときの警告文
	getStringToken( data, '|', 5, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[9+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	// 贩卖品のデータ
	memset( &itemShopList, 0, sizeof( itemShopList ) );

	itemShopSellCount = 0;
	j = 6;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 ) break;

		itemShopList[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( itemShopList[i].name, str );
		else strcpy( itemShopList[i].name, "???" );                         //MLHIDE

		itemShopList[i].graNo = getIntegerToken( data, '|', j++ );
		itemShopList[i].price = getIntegerToken( data, '|', j++ );
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );
		for( k = 0; k < ITEM_MEMO_LINE*ITEM_MEMO_PAGE; k++ ){
			if( getMemoLine( itemShopList[i].memo[k], sizeof( itemShopList[0].memo ), str, k, ITEM_MEMO_LINE_LEN ) == 0 ){
				itemShopList[i].memo[k][0] = '\0';
			}
		}

		// 装备可能フラグ
		itemShopList[i].buyOkFlag = getIntegerToken( data, '|', j++ );

		// 最大スタック数
		itemShopList[i].stack_max = getIntegerToken( data, '|', j++ );

		itemShopSellCount++;
	}

	// 强调表示に使用するので初期化
	tradeItemId = -1;

	// ウィンドウ种类记忆
	ItemShopWindowType = serverRequestWinWindowType;

	return openMenuWindow( MENU_WINDOW_ITEMSHOP, OPENMENUWINDOW_HIT, 0 );
}

//==============================================
// 买取

ACTION *openItemShopSellWindow( char *data )
{
	char str[768];
	int i, j, k;

	// ウィンドウモードを买取に设定
	ItemShopMode = 1;

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	strcpy( serverRequestWinStr[5], ML_STRING(421, "　已经无法") );
	strcpy( serverRequestWinStr[6], ML_STRING(422, "　在获得金币了") );
	strcpy( serverRequestWinStr[7], "" );
	strcpy( serverRequestWinStr[8], "" );

	memset( &itemShopList, 0, sizeof( itemShopList ) );

	itemShopBuyCount = 0;
	j = 4;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ )
	{
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 ) break;

		itemShopList[i].useFlag = 1;

		makeRecvString( str );

		if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( itemShopList[i].name, str );
		else strcpy( itemShopList[i].name, "???" );                         //MLHIDE
		itemShopList[i].stack_num = getIntegerToken( data, '|', j++);
		itemShopList[i].graNo = getIntegerToken( data, '|', j++ );
		itemShopList[i].price = getIntegerToken( data, '|', j++ );
		itemShopList[i].place = getIntegerToken( data, '|', j++ );
		itemShopList[i].otherflg = getIntegerToken( data, '|', j++ );

		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );

		//刻印ハンコ
		if(strlen(str) >= 1) strcpy(itemShopList[i].freeName, str );
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );

		for( k = 0; k < 20; k++ ){
			if( getMemoLine( itemShopList[i].memo[k], sizeof( itemShopList[0].memo ), str, k, ITEM_MEMO_LINE_LEN ) == 0 ){
				itemShopList[i].memo[k][0] = '\0';
			}
		}
		itemShopList[i].buyOkFlag = getIntegerToken( data, '|', j++ );
		itemShopList[i].stack_max = itemShopList[i].buyOkFlag;

		itemShopList[i].sellunit = getIntegerToken( data, '|', j++ );

		itemShopBuyCount++;
	}

	// 对应表作成
	for(j=0;j<MAX_DRAW_WIN_ITEM;j++){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( itemShopList[i].useFlag ){
				if ( itemShopList[i].place == j+8 ) break;
			}
		}
		PcToItemShop[j] = -1;
		if (i<MAX_DRAW_WIN_ITEM) PcToItemShop[j] = i;
	}
	PcSellTarget = 0;

	// 卖ものの登録バッファ初期化
	memset( ItemShopSellBuf, -1, 20 * sizeof(char) );

	// スクロールバーの稼动范围にするので初期化
	itemShopSellCount = 20;

	// 强调表示に使用するので初期化
	tradeItemId = -1;

	for(i=0;i<MAX_ITEM;i++) ItemNoOpe[i]++;

	// ウィンドウ种类记忆
	ItemShopWindowType = serverRequestWinWindowType;

	return openMenuWindow( MENU_WINDOW_ITEMSHOP, OPENMENUWINDOW_HIT, 0 );
}

//==============================================
// 交换

ACTION *openItemTradeWindow( char *data )
{
	char str[768];
	int i, j, k;

	// ウィンドウモードを交换に设定
	ItemShopMode = 2;

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 4, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[5+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 5, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[9+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}


	// 交换するアイテムＩＤ取り出し（たわしＩＤ等）
	tradeItemId = getIntegerToken( data, '|', 6 );
	// 交换するアイテム名取り出し（たわし等）
	getStringToken( data, '|', 7, sizeof( tradeItemName )-1, tradeItemName );
	makeRecvString( tradeItemName );

	// メモリクリア
	memset( &itemShopList, 0, sizeof( itemShopList ) );

	itemShopSellCount = 0;
	j = 8;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 ) break;

		itemShopList[i].useFlag = 1;
		itemShopList[i].buyOkFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( itemShopList[i].name, str );
		else strcpy( itemShopList[i].name, "???" );                         //MLHIDE
		itemShopList[i].graNo = getIntegerToken( data, '|', j++ );
		itemShopList[i].price = getIntegerToken( data, '|', j++ );
		// 交换后のアイテムの最大スタック数
		itemShopList[i].stack_max = getIntegerToken( data, '|', j++ );
		if ( itemShopList[i].stack_max <= 0 ) itemShopList[i].stack_max = 1;
		// 交换数に对する必要栏数
		itemShopList[i].stack_num = 0;
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );
		for( k = 0; k < ITEM_MEMO_LINE*ITEM_MEMO_PAGE; k++ ){
			if( getMemoLine( itemShopList[i].memo[k], sizeof( itemShopList[0].memo ), str, k, ITEM_MEMO_LINE_LEN ) == 0 ){
				itemShopList[i].memo[k][0] = '\0';
			}
		}

		itemShopSellCount++;
	}

	for(i=0;i<MAX_ITEM;i++) ItemNoOpe[i]++;

	// ウィンドウ种类记忆
	ItemShopWindowType = serverRequestWinWindowType;

	return openMenuWindow( MENU_WINDOW_ITEMSHOP, OPENMENUWINDOW_HIT, 0 );
}

//==============================================
// カウント

ACTION *openItemCountWindow( char *data )
{
	char str[768];
	int i;

	// ウィンドウモードをカウントに设定
	ItemShopMode = 3;

	// メモリクリア
	ZeroMemory( &itemCountInfo, sizeof( ITEMCOUNT_NPC_INFO));

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// ＮＰＣの种类
	itemCountNpcMainType = getIntegerToken( data, '|', 3);

	// 信息
	getStringToken( data, '|', 4, sizeof( str )-1, str);
	makeRecvString( str);
	for( i = 0; i < 4; i++){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20);
	}

	getStringToken( data, '|', 5, sizeof( str )-1, str);
	makeRecvString( str);
	for( i = 0; i < 4; i++){
		getMemoLine( serverRequestWinStr[5+i], sizeof( serverRequestWinStr[0] ), str, i, 20);
	}

	getStringToken( data, '|', 6, sizeof( str )-1, str);
	makeRecvString( str);
	for( i = 0; i < 4; i++){
		getMemoLine( serverRequestWinStr[9+i], sizeof( serverRequestWinStr[0] ), str, i, 20);
	}

	getStringToken( data, '|', 7, sizeof( str )-1, str);
	makeRecvString( str);
	for( i = 0; i < 4; i++){
		getMemoLine( serverRequestWinStr[13+i], sizeof( serverRequestWinStr[0] ), str, i, 20);
	}

	// お金
	itemCountInfo.gold = getIntegerToken( data, '|', 8);
	// アイテムＩＤ
	itemCountInfo.itemid = getIntegerToken( data, '|', 9);

	getStringToken( data, '|', 10, sizeof( str), str);
	makeRecvString( str);
	if( strlen( str) <= ITEM_NAME_LEN) strncpy( itemCountInfo.name, str, sizeof(itemCountInfo.name) );
	else strncpy( itemCountInfo.name, "???", sizeof(itemCountInfo.name) ); //MLHIDE

	getStringToken( data, '|', 11, sizeof( str )-1, str);
	makeRecvString( str);
	for( i = 0; i < ITEM_MEMO_LINE*ITEM_MEMO_PAGE; i++){
		if( getMemoLine( itemCountInfo.memo[i], sizeof( itemCountInfo.memo), str, i, ITEM_MEMO_LINE_LEN) == 0){
			itemCountInfo.memo[i][0] = '\0';
		}
	}

	itemCountInfo.stack = getIntegerToken( data, '|', 12);
	itemCountInfo.graNo = getIntegerToken( data, '|', 13);
	itemCountInfo.total = getIntegerToken( data, '|', 14);
	itemCountInfo.count = getIntegerToken( data, '|', 15);

	itemCountTotal = 0;
	itemCountNpcMsgNo = 0;

	// 强调表示に使用するので初期化
	tradeItemId = itemCountInfo.itemid;

	for(i=0;i<MAX_ITEM;i++) ItemNoOpe[i]++;

	// ウィンドウ种类记忆
	ItemShopWindowType = serverRequestWinWindowType;

	return openMenuWindow( MENU_WINDOW_ITEMSHOP, OPENMENUWINDOW_HIT, 0 );
}

//==============================================
// 鉴定

ACTION *openItemAppraisalWindow( char *data )
{
	char str[768];
	int i, j, k;

	// ウィンドウモードを鉴定に设定
	ItemShopMode = 4;

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	strcpy( serverRequestWinStr[1], ML_STRING(439, "欢迎。") );
	strcpy( serverRequestWinStr[2], ML_STRING(449, "要鉴定什么？") );
	strcpy( serverRequestWinStr[3], "" );
	strcpy( serverRequestWinStr[4], "" );

	strcpy( serverRequestWinStr[5], ML_STRING(441, "所持金额比不足！") );
	strcpy( serverRequestWinStr[6], "" );
	strcpy( serverRequestWinStr[7], "" );
	strcpy( serverRequestWinStr[8], "" );

	memset( &itemShopList, 0, sizeof( itemShopList ) );

	itemShopBuyCount = 0;
	j = 3;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 )
			break;

		itemShopList[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( itemShopList[i].name, str );
		else strcpy( itemShopList[i].name, "???" );                         //MLHIDE
		itemShopList[i].graNo = getIntegerToken( data, '|', j++ );
		itemShopList[i].price = getIntegerToken( data, '|', j++ );
		itemShopList[i].place = getIntegerToken( data, '|', j++ );
		itemShopList[i].otherflg = getIntegerToken( data, '|', j++ );

		itemShopList[i].stack_max = 1;
		itemShopList[i].buyOkFlag = 1;

		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );

		//刻印ハンコ
		if(strlen( str) >= 1) strcpy( itemShopList[i].freeName, str );

		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );


		for( k = 0; k < ITEM_MEMO_LINE*ITEM_MEMO_PAGE; k++ ){
			if( getMemoLine( itemShopList[i].memo[k], sizeof( itemShopList[0].memo ), str, k, ITEM_MEMO_LINE_LEN ) == 0 ){
				itemShopList[i].memo[k][0] = '\0';
			}
		}
		itemShopBuyCount++;
	}

	// 对应表作成
	for(j=0;j<MAX_DRAW_WIN_ITEM;j++){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( itemShopList[i].useFlag ){
				if ( itemShopList[i].place == j+MAX_EQUIP_ITEM ) break;
			}
		}
		PcToItemShop[j] = -1;
		if (pc.item[j+MAX_EQUIP_ITEM].useFlag) PcToItemShop[j] = 20;
		if (i<MAX_DRAW_WIN_ITEM) PcToItemShop[j] = i;
	}
	PcSellTarget = 0;

	// 卖ものの登録バッファ初期化
	memset( ItemShopSellBuf, -1, MAX_DRAW_WIN_ITEM * sizeof(char) );

	// スクロールバーの稼动范围にするので初期化
	itemShopSellCount = MAX_DRAW_WIN_ITEM;

	// 强调表示に使用するので初期化
	tradeItemId = itemCountInfo.itemid;

	for(i=0;i<MAX_ITEM;i++) ItemNoOpe[i]++;

	// ウィンドウ种类记忆
	ItemShopWindowType = serverRequestWinWindowType;

	return openMenuWindow( MENU_WINDOW_ITEMSHOP, OPENMENUWINDOW_HIT, 0 );
}

//==============================================
// 修理

ACTION *openItemRepairWindow( char *data )
{
	char str[768];
	int i, j, k;

	// ウィンドウモードを修理に设定
	ItemShopMode = 5;

	// 店员の絵
	ItemShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	strcpy( serverRequestWinStr[1], ML_STRING(439, "欢迎。") );
	strcpy( serverRequestWinStr[2], ML_STRING(440, "要修理什么？") );
	strcpy( serverRequestWinStr[3], "" );
	strcpy( serverRequestWinStr[4], "" );

	strcpy( serverRequestWinStr[5], ML_STRING(441, "所持金额比不足！") );
	strcpy( serverRequestWinStr[6], "" );
	strcpy( serverRequestWinStr[7], "" );
	strcpy( serverRequestWinStr[8], "" );

	memset( &itemShopList, 0, sizeof( itemShopList ) );

	itemShopBuyCount = 0;
	j = 3;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 )
			break;

		itemShopList[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= ITEM_NAME_LEN ) strcpy( itemShopList[i].name, str );
		else strcpy( itemShopList[i].name, "???" );                         //MLHIDE
		itemShopList[i].graNo = getIntegerToken( data, '|', j++ );
		itemShopList[i].price = getIntegerToken( data, '|', j++ );
		itemShopList[i].place = getIntegerToken( data, '|', j++ );
		itemShopList[i].otherflg = getIntegerToken( data, '|', j++ );

		itemShopList[i].stack_max = 1;
		itemShopList[i].buyOkFlag = 1;

		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );

		//刻印ハンコ
		if(strlen( str) >= 1) strcpy( itemShopList[i].freeName, str );

		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );


		for( k = 0; k < ITEM_MEMO_LINE*ITEM_MEMO_PAGE; k++ ){
			if( getMemoLine( itemShopList[i].memo[k], sizeof( itemShopList[0].memo ), str, k, ITEM_MEMO_LINE_LEN ) == 0 ){
				itemShopList[i].memo[k][0] = '\0';
			}
		}
		itemShopBuyCount++;
	}

	// 对应表作成
	for(j=0;j<MAX_DRAW_WIN_ITEM;j++){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( itemShopList[i].useFlag ){
				if ( itemShopList[i].place == j+MAX_EQUIP_ITEM ) break;
			}
		}
		PcToItemShop[j] = -1;
		if (pc.item[j+MAX_EQUIP_ITEM].useFlag) PcToItemShop[j] = 20;
		if (i<MAX_DRAW_WIN_ITEM) PcToItemShop[j] = i;
	}
	PcSellTarget = 0;

	// 卖ものの登録バッファ初期化
	memset( ItemShopSellBuf, -1, MAX_DRAW_WIN_ITEM * sizeof(char) );

	// スクロールバーの稼动范围にするので初期化
	itemShopSellCount = MAX_DRAW_WIN_ITEM;

	// 强调表示に使用するので初期化
	tradeItemId = itemCountInfo.itemid;

	for(i=0;i<MAX_ITEM;i++) ItemNoOpe[i]++;

	// ウィンドウ种类记忆
	ItemShopWindowType = serverRequestWinWindowType;

	return openMenuWindow( MENU_WINDOW_ITEMSHOP, OPENMENUWINDOW_HIT, 0 );
}

//===============================================
// 贩卖
void ChangeItemShopPriceBuy()
{
	int i;
	char s[100];

	// 贩卖价格表示
	for(i=0;i<5;i++){
		ShopNum_Price[i][0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text[0] = '\0';
		wI->sw[EnumGraphItemShopWarning1+i].Enabled = FALSE;

		if ( !itemShopList[ItemShopDispStart+i].useFlag ) continue;
		sprintf( ShopNum_Price[i], "%10d", itemShopList[ItemShopDispStart+i].price ); //MLHIDE

		//刻印
		if(itemShopList[ItemShopDispStart+i].otherflg & ITEM_ETC_FLAG_INCUSE){
			strcpy( s, itemShopList[ItemShopDispStart+i].freeName );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_GREEN;
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_GREEN;
		}else{
			if( itemShopList[ItemShopDispStart+i].stack_num > 1){
				_snprintf( s, /*ITEM_NAME_LEN*/sizeof(s), "%s(%d)", itemShopList[ItemShopDispStart+i].name, itemShopList[ItemShopDispStart+i].stack_num); //MLHIDE
			}else{
				strcpy( s, itemShopList[ItemShopDispStart+i].name );
			}
			if (itemShopList[ItemShopDispStart+i].otherflg&ITEM_ETC_FLAG_HANKO){
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_AQUA;
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_AQUA;
			}else{
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_WHITE;
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_WHITE;
			}
		}
		if ( strlen(s)>20 ){
			// ２０バイト目が全角文字の２バイト目でないなら
			if ( CheckLetterType( s, 20 )!=CHECKLETTERTYPE_FULL_TAIL ){
				memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s, 20 );
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[21] = '\0';
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text, s+20 );
			}
			// ２０バイト目が全角文字の２バイト目なら
			else{
				memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s, 19 );
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[20] = '\0';
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text, s+19 );
			}
		}else{
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s );
		}

		// 值段表示の色变更
		if( !itemShopList[ItemShopDispStart+i].buyOkFlag ){
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_YELLOW;
			wI->sw[EnumGraphItemShopWarning1+i].Enabled = TRUE;
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopWarning1+i].Switch )->text, ML_STRING(416, "＊现在无法装备") );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopWarning1+i].Switch )->color = FONT_PAL_YELLOW;
		}else{
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_WHITE;
		}
	}
}

void ChangeItemShopNumBuy()
{
	int i;

	// 购入数表示
	for(i=0;i<5;i++){
		ShopNum_Number[i][0] = '\0';

		if ( !itemShopList[ItemShopDispStart+i].useFlag ) continue;

		sprintf( ShopNum_Number[i], "%2d", itemShopList[ItemShopDispStart+i].num ); //MLHIDE
	}
}

void ChangeItemShopAllPriceBuy()
{
	int i;

	// 合计购入金额の计算
	ItemShoptotalPrice = 0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( itemShopList[i].useFlag ) ItemShoptotalPrice += itemShopList[i].num * itemShopList[i].price;
	}
	sprintf( ShopNum_BuyNum, "%10d", ItemShoptotalPrice );               //MLHIDE

	// 合计购入金额表示の色变更
	if( ItemShoptotalPrice > pc.gold ){
		( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopAllPrice].Switch )->color = FONT_PAL_RED;
	}else{
		( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopAllPrice].Switch )->color = FONT_PAL_WHITE;
	}
}

void ChangeItemShopBuyAllNumBuy()
{
	int i;
	int stack_max, num;

	// 购入数の计算
	ItemShoptotalNum = 0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( itemShopList[i].useFlag ){
			num = itemShopList[i].num;
			stack_max = itemShopList[i].stack_max;
			if( stack_max <= 0 ) stack_max = 2;
			num += stack_max - 1;
			num /= stack_max;
			ItemShoptotalNum += num;
		}
	}
}

//===============================================
// 买取
void ChangeItemShopPriceSell()
{
	int i;
	char s[100];

	// 贩卖价格表示
	for(i=0;i<5;i++){
		ShopNum_Price[i][0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text[0] = '\0';
		wI->sw[EnumGraphItemShopWarning1+i].Enabled = FALSE;

		if ( ItemShopSellBuf[ItemShopDispStart+i]<0 ) continue;

		sprintf( ShopNum_Price[i], "%10d", itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].price ); //MLHIDE

		//刻印
		if(itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].otherflg & ITEM_ETC_FLAG_INCUSE){
			strcpy( s, itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].freeName );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_GREEN;
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_GREEN;
		}else{
			if( itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].stack_num > 1){
				_snprintf( s, /*ITEM_NAME_LEN*/sizeof(s), "%s(%d)",               //MLHIDE
					itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].name,
					itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].stack_num);
			}else{
				strcpy( s, itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].name );
			}
			if (itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].otherflg&ITEM_ETC_FLAG_HANKO){
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_AQUA;
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_AQUA;
			}else{
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_WHITE;
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_WHITE;
			}
		}
		if ( strlen(s)>20 ){
			// ２０バイト目が全角文字の２バイト目でないなら
			if ( CheckLetterType( s, 20 )!=CHECKLETTERTYPE_FULL_TAIL ){
				memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s, 20 );
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[21] = '\0';
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text, s+20 );
			}
			// ２０バイト目が全角文字の２バイト目なら
			else{
				memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s, 19 );
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[20] = '\0';
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text, s+19 );
			}
		}else{
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s );
		}

		// 值段表示の色变更
		if( !itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].buyOkFlag ){
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_RED;
			wI->sw[EnumGraphItemShopWarning1+i].Enabled = TRUE;
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopWarning1+i].Switch )->text, ML_STRING(839, "＊无法卖出") );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopWarning1+i].Switch )->color = FONT_PAL_RED;
		}else if( itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].num ){
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_WHITE;
		}else{
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_GRAY;
		}
	}
}

void ChangeItemShopNumSell()
{
	int i;

	// 购入数表示
	for(i=0;i<5;i++){
		ShopNum_Number[i][0] = '\0';

		wI->sw[EnumGraphItemShopMinus1+i].Enabled = FALSE;
		wI->sw[EnumGraphItemShopPlus1+i].Enabled = FALSE;

		if ( ItemShopSellBuf[ItemShopDispStart+i]<0 ) continue;

		sprintf( ShopNum_Number[i], "%2d", itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].num ); //MLHIDE

		if (itemShopList[ ItemShopSellBuf[ItemShopDispStart+i] ].stack_max>1){
			wI->sw[EnumGraphItemShopMinus1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopPlus1+i].Enabled = TRUE;
		}
	}
}

void ChangeItemShopAllPriceSell()
{
	int i;

	// 合计购入金额の计算
	ItemShoptotalPrice = 0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( ItemShopSellBuf[i]<0 ) continue;

		ItemShoptotalPrice += itemShopList[ ItemShopSellBuf[i] ].num
			* itemShopList[ ItemShopSellBuf[i] ].price;
	}
	sprintf( ShopNum_BuyNum, "%10d", ItemShoptotalPrice );               //MLHIDE

	// 合计购入金额表示の色变更
	( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopAllPrice].Switch )->color = FONT_PAL_WHITE;
}

void ChangeItemShopBuyAllNumSell()
{
	int i;

	// 购入数の计算
	ItemShoptotalNum = 0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( ItemShopSellBuf[i]<0 ) continue;

		ItemShoptotalNum += itemShopList[ ItemShopSellBuf[i] ].num;
	}
}

//===============================================
// 交换
void ChangeItemShopPriceTrade()
{
	int i;
	char s[100];

	// 交换单位个数表示
	for(i=0;i<5;i++){
		ShopNum_Price[i][0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text[0] = '\0';
		wI->sw[EnumGraphItemShopWarning1+i].Enabled = FALSE;

		if ( !itemShopList[ItemShopDispStart+i].useFlag ) continue;
		sprintf( ShopNum_Price[i], "%10d", itemShopList[ItemShopDispStart+i].price ); //MLHIDE

		//刻印
		if(itemShopList[ItemShopDispStart+i].otherflg & ITEM_ETC_FLAG_INCUSE){
			strcpy( s, itemShopList[ItemShopDispStart+i].freeName );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_GREEN;
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_GREEN;
		}else{
			if( itemShopList[ItemShopDispStart+i].stack_num > 1){
				_snprintf( s, /*ITEM_NAME_LEN*/sizeof(s), "%s(%d)", itemShopList[ItemShopDispStart+i].name, itemShopList[ItemShopDispStart+i].stack_num); //MLHIDE
			}else{
				strcpy( s, itemShopList[ItemShopDispStart+i].name );
			}
			if (itemShopList[ItemShopDispStart+i].otherflg&ITEM_ETC_FLAG_HANKO){
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_AQUA;
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_AQUA;
			}else{
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->color = FONT_PAL_WHITE;
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->color = FONT_PAL_WHITE;
			}
		}
		if ( strlen(s)>20 ){
			// ２０バイト目が全角文字の２バイト目でないなら
			if ( CheckLetterType( s, 20 )!=CHECKLETTERTYPE_FULL_TAIL ){
				memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s, 20 );
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[21] = '\0';
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text, s+20 );
			}
			// ２０バイト目が全角文字の２バイト目なら
			else{
				memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s, 19 );
				( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text[20] = '\0';
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2+i].Switch )->text, s+19 );
			}
		}else{
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1+i].Switch )->text, s );
		}

		// 个数表示の色变更
		if( tradeItemCnt >= ItemShoptotalPrice + itemShopList[ItemShopDispStart+i].price ){
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_WHITE;
		}else{
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopPrice1+i].Switch )->color = FONT_PAL_GRAY;
			wI->sw[EnumGraphItemShopWarning1+i].Enabled = TRUE;
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopWarning1+i].Switch )->text, ML_STRING(840, "＊数量不够") );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopWarning1+i].Switch )->color = FONT_PAL_YELLOW;
		}
	}
}

void ChangeItemShopNumTrade()
{
	int i;

	// 购入数表示
	for(i=0;i<5;i++){
		ShopNum_Number[i][0] = '\0';
		wI->sw[EnumGraphItemShopMinus1+i].Enabled = FALSE;
		wI->sw[EnumGraphItemShopPlus1+i].Enabled = FALSE;

		if ( !itemShopList[ItemShopDispStart+i].useFlag ) continue;
		sprintf( ShopNum_Number[i], "%2d", itemShopList[ItemShopDispStart+i].num ); //MLHIDE

		wI->sw[EnumGraphItemShopMinus1+i].Enabled = TRUE;
		wI->sw[EnumGraphItemShopPlus1+i].Enabled = TRUE;
	}
}

void ChangeItemShopAllPriceTrade()
{
	int i;

	// 合计交换个数の计算
	ItemShoptotalPrice = 0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if( itemShopList[i].useFlag ) ItemShoptotalPrice += itemShopList[i].num * itemShopList[i].price;
	}
	sprintf( ShopNum_HaveNum, "%10d", ItemShoptotalPrice );              //MLHIDE
}

void ChangeItemShopBuyAllNumTrade()
{
	int i;

	// 交换数の计算
	ItemShoptotalNum = 0;
	for( i = 0; i < 20; i++ ){
		if( itemShopList[i].useFlag ){
			ItemShoptotalNum += itemShopList[i].num;
		}
	}
}

//===============================================
// カウント
void ChangeItemShopPriceCount()
{
	// 必要个数
	sprintf( ShopNum_Price[0], "%10d", itemCountInfo.count );            //MLHIDE

	if ( strlen(itemCountInfo.name)>20 ){
		// ２０バイト目が全角文字の２バイト目でないなら
		if ( CheckLetterType( itemCountInfo.name, 20 )!=CHECKLETTERTYPE_FULL_TAIL ){
			memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1].Switch )->text, itemCountInfo.name, 20 );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1].Switch )->text[21] = '\0';
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2].Switch )->text, itemCountInfo.name+20 );
		}
		// ２０バイト目が全角文字の２バイト目なら
		else{
			memcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1].Switch )->text, itemCountInfo.name, 19 );
			( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1].Switch )->text[20] = '\0';
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2].Switch )->text, itemCountInfo.name+19 );
		}
	}else{
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1].Switch )->text, itemCountInfo.name );
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopName1_2].Switch )->text, "" );
	}
}

void ChangeItemShopNumCount()
{
	// 渡す/受け取る个数表示
	sprintf( ShopNum_Number[0], "%2d", itemCountTotal );                 //MLHIDE
	sprintf( ShopNum_HaveNum, "%10d", itemCountTotal );                  //MLHIDE
	ItemShoptotalPrice = itemCountTotal * itemCountInfo.gold;
}

void ChangeItemShopTargetNumCount()
{
	int i;

	ItemShoptotalNum = 0;
	// 取引するアイテムの数を计算
	if( itemCountNpcMainType == 0){		// 给予物品场合
		// プレイヤーの持っているアイテム数
		ItemShoptotalNum = itemCountInfo.stack;
	}else{								// 获得物品场合
		// すでに对象のアイテムを持っている场合は、その数を记録しておく
		for( i = 0; i < MAX_ITEM; i++){
			if( pc.item[i].useFlag && pc.item[i].id == itemCountInfo.itemid){
				ItemShoptotalNum += pc.item[i].num;
			}
		}
	}
}

//===============================================
//
BOOL MenuWindowItemShopBf( int mouse )
{
	int i;

	if (mouse==WIN_INIT){
		ItemShopDispStart = 0;
		tradeItemCnt = -1;

		wI->sw[EnumItemShopInfo].Enabled = FALSE;

		for(i=0;i<5;i++){
			wI->sw[EnumGraphItemShopName1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopName1_2+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopWarning1+i].Enabled = FALSE;
			wI->sw[EnumGraphItemShopPrice1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopNumber1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopMinus1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopPlus1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopItemBox1+i].Enabled = TRUE;
			wI->sw[EnumGraphItemShopItemPanel1+i].Enabled = TRUE;
		}

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			ChangeItemShopAllPriceBuy();
			ChangeItemShopBuyAllNumBuy();

			( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopPanel].Switch )->graNo = GID_ShopPanel;
			for(i=0;i<5;i++){
				wI->sw[EnumGraphItemShopItemBox1+i].Enabled = TRUE;
				wI->sw[EnumGraphItemShopItemBox1+i].func = MenuSwitchItemShopItemBoxBuy;
				( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopItemPanel1+i].Switch )->graNo = GID_ShopItemPanel;
			}
			wI->sw[EnumGraphItemShopMyPrice].Enabled = FALSE;
			wI->sw[EnumGraphItemShopAll].Enabled = FALSE;
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			ChangeItemShopAllPriceSell();
			ChangeItemShopBuyAllNumSell();

			( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopPanel].Switch )->graNo = GID_ShopPanel;
			for(i=0;i<5;i++){
				wI->sw[EnumGraphItemShopItemBox1+i].Enabled = TRUE;
				wI->sw[EnumGraphItemShopItemBox1+i].func = MenuSwitchItemShopItemBoxSell;
				( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopItemPanel1+i].Switch )->graNo = GID_ShopItemPanel;
			}
			wI->sw[EnumGraphItemShopMyPrice].Enabled = FALSE;
			wI->sw[EnumGraphItemShopAll].Enabled = TRUE;
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			ChangeItemShopAllPriceTrade();
			ChangeItemShopBuyAllNumTrade();

			( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopPanel].Switch )->graNo = GID_ShopPanel2;
			for(i=0;i<5;i++){
				wI->sw[EnumGraphItemShopItemBox1+i].Enabled = TRUE;
				wI->sw[EnumGraphItemShopItemBox1+i].func = MenuSwitchItemShopItemBoxBuy;
				( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopItemPanel1+i].Switch )->graNo = GID_ShopItemPanel2;
			}
			wI->sw[EnumGraphItemShopMyPrice].Enabled = TRUE;
			wI->sw[EnumGraphItemShopAll].Enabled = FALSE;
			break;
		case 3:
			ChangeItemShopPriceCount();
			ChangeItemShopNumCount();

			for(i=1;i<5;i++){
				wI->sw[EnumGraphItemShopName1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopName1_2+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopWarning1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopPrice1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopNumber1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopMinus1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopPlus1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopItemBox1+i].Enabled = FALSE;
				wI->sw[EnumGraphItemShopItemPanel1+i].Enabled = FALSE;
			}

			( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopPanel].Switch )->graNo = GID_ShopPanel2;

			wI->sw[EnumGraphItemShopName1].Enabled = TRUE;
			wI->sw[EnumGraphItemShopName1_2].Enabled = TRUE;
			wI->sw[EnumGraphItemShopWarning1].Enabled = FALSE;
			wI->sw[EnumGraphItemShopPrice1].Enabled = TRUE;
			wI->sw[EnumGraphItemShopNumber1].Enabled = TRUE;
			wI->sw[EnumGraphItemShopMinus1].Enabled = TRUE;
			wI->sw[EnumGraphItemShopPlus1].Enabled = TRUE;
			wI->sw[EnumGraphItemShopItemBox1].Enabled = TRUE;
			wI->sw[EnumGraphItemShopItemPanel1].Enabled = TRUE;

			wI->sw[EnumGraphItemShopItemBox1].func = MenuSwitchItemShopItemBoxCount;
			( (GRAPHIC_SWITCH *)wI->sw[EnumGraphItemShopItemPanel1].Switch )->graNo = GID_ShopItemPanel2;
			
			wI->sw[EnumGraphItemShopMyPrice].Enabled = TRUE;
			wI->sw[EnumGraphItemShopAll].Enabled = FALSE;
			break;
		}
		ItemShopItemInfoNo=-1;
		ItemShopInfoNo=-1;
		ItemShopInfoPage=0;

		for(i=0;i<5;i++) ItemBoxOldPage[i] = -2;

		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphItemShopTitle].Switch )->text, serverRequestWinStr[0] );

		( (ACTION_SWITCH *)wI->sw[EnumGraphItemShopChara].Switch )->ActionAdd->anim_chr_no = ItemShopNpcGra;
		( (ACTION_SWITCH *)wI->sw[EnumGraphItemShopChara].Switch )->ActionAdd->anim_ang = 5;
	}

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if( checkMoveMapGridPos( 2, 2 ) ){
		wI->flag |= WIN_INFO_DEL;
	}

	// ウィンドウ种类が违うならウィンドウを关闭
	if (ItemShopWindowType != serverRequestWinWindowType){
		wI->flag |= WIN_INFO_DEL;
	}

	wI->sw[EnumGraphItemShopScroll].Enabled = TRUE;
	if (itemShopSellCount-5<=0) wI->sw[EnumGraphItemShopScroll].Enabled = FALSE;
	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wI->sw[EnumBtItemShopScroll].Switch )->status&1 ){
		ItemShopDispStart = ScrollVPointToNum( &wI->sw[EnumBtItemShopScroll], itemShopSellCount-5 );

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		}
	}

	// 手持ち金表示
	sprintf( ShopNum_PCGold, "%10d", pc.gold );                          //MLHIDE

	if (ItemShopMode==0){
		// 合计购入金额表示の色变更
		if( ItemShoptotalPrice > pc.gold ){
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopAllPrice].Switch )->color = FONT_PAL_RED;
		}else{
			( (NUMBER_SWITCH *)wI->sw[EnumGraphItemShopAllPrice].Switch )->color = FONT_PAL_WHITE;
		}
	}

	if ( ItemShopMode == 2 ){
		int tradeItemCntBck = tradeItemCnt;
		// 交换对象のアイテムの所持数计算
		tradeItemCnt = 0;
		for( i = 0; i < MAX_ITEM; i++ ){
			if( pc.item[i].useFlag && pc.item[i].id == tradeItemId ){ // 可变アイテムＩＤ
				// 自分が持っている个数の计算
				tradeItemCnt += pc.item[i].num;
			}
		}

		if ( tradeItemCnt != tradeItemCntBck ){
			ChangeItemShopPriceTrade();
			sprintf( ShopNum_BuyNum, "%10d", tradeItemCnt );                   //MLHIDE
		}
	}


	if (ItemShopInfoNo>=0) ItemShopInfoNo = -1;
	ItemShopItemInfoNo = -1;

	if ( ItemShopMode == 3 ){
		ChangeItemShopTargetNumCount();

		if( itemCountNpcMainType == 0){
			sprintf( ShopNum_BuyNum, "%10d", itemCountInfo.stack );            //MLHIDE
		}else{
			sprintf( ShopNum_BuyNum, "%10d", ItemShoptotalNum );               //MLHIDE
		}
	}

	return TRUE;
}

BOOL MenuWindowItemShopAf( int mouse )
{
	int x = wI->wx + 298;
	int y = wI->wy + 50;
	int i, j;

	if (ItemShopMode==1){
		//最大所持金额超えるなら
		if( pc.gold + ItemShoptotalPrice > MAX_GOLD ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
			ItemShopInfoNo = -2;
		}
	}

	if ( ItemShopMode == 3 ){
		// 给予物品时、对象のアイテムを持っていない时
		if( itemCountNpcMainType == 0 && ItemShoptotalNum <= 0){
			ItemShopInfoNo = -4;
		}
	}

	// アイテム信息
	if (ItemShopItemInfoNo>=0){
		if (ItemShopItemInfoNo<100){
			PcItemExplanationWindow( ItemShopItemInfoNo, ItemShopInfoPage );
		}else if (ItemShopItemInfoNo<200){
			ShopItemExplanationWindow( ItemShopItemInfoNo-100, ItemShopInfoPage, ItemShopMode == 0 );
		}else{
			CountItemExplanationWindow( ItemShopInfoPage );
		}
	}
	{
		j = -ItemShopInfoNo-1;

		// 信息表示
		itemInfoColor = FONT_PAL_WHITE;
		for( i = 0; i < 4; i++ ){
			if( strlen( serverRequestWinStr[(j<<2)+1+i] ) > 0 ){
				stockFontBuffer( x, y+i*10, FONT_PRIO_WIN, FONT_KIND_SIZE_11, FONT_PAL_WHITE,
					serverRequestWinStr[(j<<2)+1+i], 0, 0 );
			}
		}
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuSwitchItemShopClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_WindowCloseOff;

	return ReturnFlag;
}

void ForItemShopInfoFunc( char Button )
{
	if (Button==0){
		char msg[256];
		char str[256];
		char str2[32];
		int i,j;

		switch(ItemShopMode){
		case 0:
			str[0] = '\0';
			for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
				if( itemShopList[i].useFlag && itemShopList[i].num ){
					if( j > 0 ) strcat( str, "|" );                                  //MLHIDE
					sprintf( str2, "%d|%d", i, itemShopList[i].num );                //MLHIDE
					strcat( str, str2 );
					j++;
				}
			}

			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;
			break;
		case 1:
			str[0] = '\0';
			for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
				if( itemShopList[i].useFlag && itemShopList[i].num ){
					if( j > 0 ) strcat( str, "|" );                                  //MLHIDE
					sprintf( str2, "%d|%d", itemShopList[i].place, itemShopList[i].num ); //MLHIDE
					strcat( str, str2 );
					j++;
				}
			}

			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;
			break;
		case 2:
			str[0] = '\0';
			for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
				if( itemShopList[i].useFlag && itemShopList[i].num ){
					if( j > 0 ) strcat( str, "|" );                                  //MLHIDE
					sprintf( str2, "%d|%d", i, itemShopList[i].num );                //MLHIDE
					strcat( str, str2 );
					j++;
				}
			}

			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;
			break;
		case 3:
			sprintf( str, "1|%d", itemCountTotal);                             //MLHIDE
			makeSendString( str, msg, sizeof(msg) );
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;
			break;
		case 4:case 5:
			str[0] = '\0';
			for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
				if( itemShopList[i].useFlag && itemShopList[i].num ){
					if( j > 0 ) strcat( str, "|" );                                  //MLHIDE
					sprintf( str2, "%d", itemShopList[i].place );                    //MLHIDE
					strcat( str, str2 );
					j++;
				}
			}

			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;
			break;
		}
	}
}

BOOL MenuSwitchItemShopOK( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		switch(ItemShopMode){
		case 0:	strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYOK );		break;
		case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLOK );	break;
		case 2:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADEOK );	break;
		case 3:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADEOK );	break;
		case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPOK );		break;
		case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIROK );	break;
		}
	}

	if ( ItemShoptotalNum > 0 ){
		//押されたとき
		if( flag & MENU_MOUSE_LEFT ){
			int i, j;
			switch(ItemShopMode){
			case 0:
				// お金が足りないとき
				if( pc.gold < ItemShoptotalPrice ){
					ItemShopInfoNo = -2;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				// 手持ちに空きがないとき
				else if( ItemShoptotalNum > getItemEmpty() ){
					ItemShopInfoNo = -3;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				// 买えるとき
				else{
					ItemShopInfoFunc = ForItemShopInfoFunc;
					for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
						if( itemShopList[i].useFlag && itemShopList[i].num ){
							ItemShopInfoDispItem[j].useFlag = 1;

							strcpy( ItemShopInfoDispItem[j].name, itemShopList[i].name );
							ItemShopInfoDispItem[j].pal = FONT_PAL_WHITE;
							ItemShopInfoDispItem[j].graNo = itemShopList[i].graNo;
							ItemShopInfoDispItem[j].num = itemShopList[i].num;
							ItemShopInfoDispItem[j].stack = 0;

							j++;
						}
					}
					for(;j<MAX_DRAW_WIN_ITEM;j++) ItemShopInfoDispItem[j].useFlag = 0;

					ItemShopInfoDispNum[0].useflag = TRUE;
					ItemShopInfoDispNum[0].x = 283,	ItemShopInfoDispNum[0].y = 343;
					ItemShopInfoDispNum[0].num = pc.gold;

					ItemShopInfoDispNum[1].useflag = TRUE;
					ItemShopInfoDispNum[1].x = 433,	ItemShopInfoDispNum[1].y = 343;
					ItemShopInfoDispNum[1].num = ItemShoptotalPrice;

					sprintf( ItemShopInfoDispStr1, ML_STRING(841, "持有金额         金币    购买金额         金币") );
					sprintf( ItemShopInfoDispStr2, ML_STRING(842, "购买这些物品。可以吗？") );

					strcpy( ItemShopInfoDispOneLineYes, MWONELINE_SHOP_BUYYES );
					strcpy( ItemShopInfoDispOneLineNo, MWONELINE_SHOP_BUYNO );

					// 确认ウィンドウ表示
					wI->sw[EnumItemShopInfo].Enabled = TRUE;
					// ウィンドウの移动不可能に
					wI->flag |= MENU_ATTR_NOMOVE;

					// 决定音b（ボタンクリック时）
					play_se( SE_NO_OK2, 320, 240 );
				}
				break;
			case 1:
				ItemShopInfoFunc = ForItemShopInfoFunc;
				for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
					if( ItemShopSellBuf[i]<0 ) continue;

					ItemShopInfoDispItem[j].useFlag = 1;

					if (itemShopList[ ItemShopSellBuf[i] ].otherflg&ITEM_ETC_FLAG_INCUSE){
						strcpy( ItemShopInfoDispItem[j].name, itemShopList[ ItemShopSellBuf[i] ].freeName );
						ItemShopInfoDispItem[j].pal = FONT_PAL_GREEN;
					}else if (itemShopList[ ItemShopSellBuf[i] ].otherflg&ITEM_ETC_FLAG_HANKO){
						strcpy( ItemShopInfoDispItem[j].name, itemShopList[ ItemShopSellBuf[i] ].name );
						ItemShopInfoDispItem[j].pal = FONT_PAL_AQUA;
					}else{
						strcpy( ItemShopInfoDispItem[j].name, itemShopList[ ItemShopSellBuf[i] ].name );
						ItemShopInfoDispItem[j].pal = FONT_PAL_WHITE;
					}
					ItemShopInfoDispItem[j].graNo = itemShopList[ ItemShopSellBuf[i] ].graNo;
					ItemShopInfoDispItem[j].num = itemShopList[ ItemShopSellBuf[i] ].num;
					ItemShopInfoDispItem[j].stack = 0;
					if( itemShopList[ ItemShopSellBuf[i] ].stack_num > 1){
						ItemShopInfoDispItem[j].stack = itemShopList[ ItemShopSellBuf[i] ].sellunit;
					}
					j++;
				}
				for(;j<MAX_DRAW_WIN_ITEM;j++) ItemShopInfoDispItem[j].useFlag = 0;

				ItemShopInfoDispNum[0].useflag = TRUE;
				ItemShopInfoDispNum[0].x = 283,	ItemShopInfoDispNum[0].y = 343;
				ItemShopInfoDispNum[0].num = pc.gold;

				ItemShopInfoDispNum[1].useflag = TRUE;
				ItemShopInfoDispNum[1].x = 433,	ItemShopInfoDispNum[1].y = 343;
				ItemShopInfoDispNum[1].num = ItemShoptotalPrice;

				sprintf( ItemShopInfoDispStr1, ML_STRING(843, "持有金额         金币    卖出金额         金币") );
				sprintf( ItemShopInfoDispStr2, ML_STRING(428, "卖出以上物品。可以吗？") );

				strcpy( ItemShopInfoDispOneLineYes, MWONELINE_SHOP_SELLYES );
				strcpy( ItemShopInfoDispOneLineNo, MWONELINE_SHOP_SELLNO );

				// 确认ウィンドウ表示
				wI->sw[EnumItemShopInfo].Enabled = TRUE;
				// ウィンドウの移动不可能に
				wI->flag |= MENU_ATTR_NOMOVE;

				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				break;
			case 2:
				ItemShopInfoFunc = ForItemShopInfoFunc;
				for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
					if( itemShopList[i].useFlag && itemShopList[i].num ){
						ItemShopInfoDispItem[j].useFlag = 1;

						strcpy( ItemShopInfoDispItem[j].name, itemShopList[i].name );
						ItemShopInfoDispItem[j].pal = FONT_PAL_WHITE;
						ItemShopInfoDispItem[j].graNo = itemShopList[i].graNo;
						ItemShopInfoDispItem[j].num = itemShopList[i].num;
						ItemShopInfoDispItem[j].stack = 0;

						j++;
					}
				}
				for(;j<MAX_DRAW_WIN_ITEM;j++) ItemShopInfoDispItem[j].useFlag = 0;

				ItemShopInfoDispNum[0].useflag = TRUE;
				ItemShopInfoDispNum[0].x = 354,	ItemShopInfoDispNum[0].y = 343;
				ItemShopInfoDispNum[0].num = tradeItemCnt;

				if ( ItemShoptotalPrice >= 1000 ){
					ItemShopInfoDispNum[1].useflag = TRUE;
					ItemShopInfoDispNum[1].x = 450,	ItemShopInfoDispNum[1].y = 343;
					ItemShopInfoDispNum[1].num = ItemShoptotalPrice;

					sprintf( ItemShopInfoDispStr1, ML_STRING(844, "%-24s    个  交换数     个"), tradeItemName );
				}else{
					ItemShopInfoDispNum[1].useflag = TRUE;
					ItemShopInfoDispNum[1].x = 438,	ItemShopInfoDispNum[1].y = 343;
					ItemShopInfoDispNum[1].num = ItemShoptotalPrice;

					sprintf( ItemShopInfoDispStr1, ML_STRING(844, "%-24s    个  交换数     个"), tradeItemName );
				}
				sprintf( ItemShopInfoDispStr2, ML_STRING(845, "交换这些物品。可以吗？") );

				strcpy( ItemShopInfoDispOneLineYes, MWONELINE_SHOP_TRADEYES );
				strcpy( ItemShopInfoDispOneLineNo, MWONELINE_SHOP_TRADENO );

				// 确认ウィンドウ表示
				wI->sw[EnumItemShopInfo].Enabled = TRUE;
				// ウィンドウの移动不可能に
				wI->flag |= MENU_ATTR_NOMOVE;

				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				break;
			case 3:
				ItemShopInfoFunc = ForItemShopInfoFunc;
				ItemShopInfoDispItem[0].useFlag = 1;

				strcpy( ItemShopInfoDispItem[0].name, itemCountInfo.name );
				ItemShopInfoDispItem[0].pal = FONT_PAL_WHITE;
				ItemShopInfoDispItem[0].graNo = itemCountInfo.graNo;
				ItemShopInfoDispItem[0].num = itemCountTotal;
				ItemShopInfoDispItem[0].stack = 0;

				for(j=1;j<MAX_DRAW_WIN_ITEM;j++) ItemShopInfoDispItem[j].useFlag = 0;

				ItemShopInfoDispNum[0].useflag = TRUE;
				ItemShopInfoDispNum[0].x = 283,	ItemShopInfoDispNum[0].y = 343;
				ItemShopInfoDispNum[0].num = ItemShoptotalNum;

				ItemShopInfoDispNum[1].useflag = TRUE;
				ItemShopInfoDispNum[1].x = 433,	ItemShopInfoDispNum[1].y = 343;
				ItemShopInfoDispNum[1].num = itemCountTotal;

				sprintf( ItemShopInfoDispStr1, ML_STRING(846, "手持数           个    给予个数           个") );
				sprintf( ItemShopInfoDispStr2, ML_STRING(847, "给予这些物品。可以吗？") );

				strcpy( ItemShopInfoDispOneLineYes, MWONELINE_SHOP_TRADEYES );
				strcpy( ItemShopInfoDispOneLineNo, MWONELINE_SHOP_TRADENO );

				// 确认ウィンドウ表示
				wI->sw[EnumItemShopInfo].Enabled = TRUE;
				// ウィンドウの移动不可能に
				wI->flag |= MENU_ATTR_NOMOVE;

				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				break;
			case 4:case 5:
				if ( pc.gold < ItemShoptotalPrice ){
					ItemShopInfoNo = -2;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}else{
					ItemShopInfoFunc = ForItemShopInfoFunc;
					for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
						if( ItemShopSellBuf[i]<0 ) continue;

						ItemShopInfoDispItem[j].useFlag = 1;

						strcpy( ItemShopInfoDispItem[j].name, itemShopList[ ItemShopSellBuf[i] ].name );
						ItemShopInfoDispItem[j].pal = FONT_PAL_WHITE;
						if (itemShopList[j].otherflg&ITEM_ETC_FLAG_HANKO) ItemShopInfoDispItem[j].pal = FONT_PAL_AQUA;
						ItemShopInfoDispItem[j].graNo = itemShopList[ ItemShopSellBuf[i] ].graNo;
						ItemShopInfoDispItem[j].num = itemShopList[ ItemShopSellBuf[i] ].num;
						ItemShopInfoDispItem[j].stack = 0;
						j++;
					}
					for(;j<MAX_DRAW_WIN_ITEM;j++) ItemShopInfoDispItem[j].useFlag = 0;

					ItemShopInfoDispNum[0].useflag = TRUE;
					ItemShopInfoDispNum[0].x = 283,	ItemShopInfoDispNum[0].y = 343;
					ItemShopInfoDispNum[0].num = pc.gold;
	
					ItemShopInfoDispNum[1].useflag = TRUE;
					ItemShopInfoDispNum[1].x = 433,	ItemShopInfoDispNum[1].y = 343;
					ItemShopInfoDispNum[1].num = ItemShoptotalPrice;
					if (ItemShopMode==4){
						sprintf( ItemShopInfoDispStr1, ML_STRING(848, "持有金额         金币    鉴定金额         金币") );
						sprintf( ItemShopInfoDispStr2, ML_STRING(849, "鉴定这些物品。可以吗？") );

						strcpy( ItemShopInfoDispOneLineYes, MWONELINE_SHOP_APPYES );
						strcpy( ItemShopInfoDispOneLineNo, MWONELINE_SHOP_APPNO );
					}else{
						sprintf( ItemShopInfoDispStr1, ML_STRING(850, "持有金额         金币    修理金额         金币") );
						sprintf( ItemShopInfoDispStr2, ML_STRING(851, "修理这些物品。可以吗？") );

						strcpy( ItemShopInfoDispOneLineYes, MWONELINE_SHOP_REPAIRYES );
						strcpy( ItemShopInfoDispOneLineNo, MWONELINE_SHOP_REPAIRNO );
					}

					// 确认ウィンドウ表示
					wI->sw[EnumItemShopInfo].Enabled = TRUE;
					// ウィンドウの移动不可能に
					wI->flag |= MENU_ATTR_NOMOVE;

					// 决定音b（ボタンクリック时）
					play_se( SE_NO_OK2, 320, 240 );
				}
				break;
			}
			ReturnFlag = TRUE;
		}
	}else{
		//押されたとき
		if( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	Graph->graNo = GID_BigOKButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;

	if ( ItemShoptotalNum <= 0 ) Graph->graNo = GID_BigOKButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopCancel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		switch(ItemShopMode){
		case 0:	strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYCANCEL );		break;
		case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLCANCEL );	break;
		case 2:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADECANCEL );	break;
		case 3:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADECANCEL );	break;
		case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPCANCEL );		break;
		case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRCANCEL );	break;
		}
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// キャンセル押した事を送信
		nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_CANCEL, "" );

		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_BigCancelButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigCancelButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigCancelButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopAll( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i,j;
	BOOL Usable = FALSE;

	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// まだ登録されてなくて卖れるなら
		if ( !( PcSellTarget&(1<<i) ) && itemShopList[ PcToItemShop[i] ].buyOkFlag ){
			break;
		}
	}
	if ( i < MAX_DRAW_WIN_ITEM ) Usable = TRUE;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		if (Usable){
			switch(ItemShopMode){
			case 0:	strcpy( OneLineInfoStr, "" );		break;
			case 1:	strcpy( OneLineInfoStr, ML_STRING(852, "提交所有可以卖出的物品") );	break;
			case 2:	strcpy( OneLineInfoStr, "" );	break;
			case 3:	strcpy( OneLineInfoStr, "" );	break;
			case 4:	strcpy( OneLineInfoStr, ML_STRING(853, "提交所有可以鉴定的物品") );	break;
			case 5:	strcpy( OneLineInfoStr, ML_STRING(854, "提交所有可以修理的物品") );	break;
			}
		}else{
			strcpy( OneLineInfoStr, ML_STRING(855, "没有可以提交的物品") );
		}
	}

	if (!Usable){
		Graph->graNo = GID_AllButtonOff;
		if( flag & MENU_MOUSE_LEFT ){
			ReturnFlag = TRUE;
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
		return ReturnFlag;
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (0<=PcToItemShop[i]&&PcToItemShop[i]<20){
				// まだ登録されてなくて卖れるなら
				if ( !( PcSellTarget&(1<<i) ) && itemShopList[ PcToItemShop[i] ].buyOkFlag ){
					// 空き栏を搜す
					for(j=0;j<MAX_DRAW_WIN_ITEM;j++){
						if (ItemShopSellBuf[j]<0) break;
					}
					if (j<MAX_DRAW_WIN_ITEM){
						//最大所持金额超えるなら
						if( ItemShopMode==1 && pc.gold + ItemShoptotalPrice + itemShopList[PcToItemShop[i]].price > MAX_GOLD ){
							// ＮＧ音（短い）
							play_se( SE_NO_NG, 320, 240 );
							ItemShopInfoNo = -2;
						}else{
							ItemShopSellBuf[j] = PcToItemShop[i];
							itemShopList[ PcToItemShop[i] ].num = itemShopList[ PcToItemShop[i] ].stack_max;
							if ( itemShopList[ PcToItemShop[i] ].num <= 0 )
								itemShopList[ PcToItemShop[i] ].num = 1;
////								itemShopList[ PcToItemShop[i] ].num = 1;

							PcSellTarget|=(1<<i);

							// つまみを移动
							NumToScrollVMove( &wI->sw[EnumBtItemShopScroll], itemShopSellCount-5, ItemShopDispStart );

							// 表示を更新
							ChangeItemShopPriceSell();
							ChangeItemShopNumSell();
							ChangeItemShopAllPriceSell();
							ChangeItemShopBuyAllNumSell();
						}
					}
				}
			}
			WinDD_DragFinish();

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_AllButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_AllButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_AllButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (ItemShopDispStart>0){
			ItemShopDispStart--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-1], itemShopSellCount-5, ItemShopDispStart );

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (ItemShopDispStart<itemShopSellCount-5){
			ItemShopDispStart++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-2], itemShopSellCount-5, ItemShopDispStart );

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (ItemShopDispStart>0){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		ItemShopDispStart-=5;
		if (ItemShopDispStart<0) ItemShopDispStart=0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-3], itemShopSellCount-5, ItemShopDispStart );

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if ( (itemShopSellCount-5>0) && (ItemShopDispStart<itemShopSellCount-5) ){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		ItemShopDispStart+=5;
		if (ItemShopDispStart>itemShopSellCount-5) ItemShopDispStart = itemShopSellCount-5;
		if (ItemShopDispStart<0) ItemShopDispStart = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-4], itemShopSellCount-5, ItemShopDispStart );

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		ItemShopDispStart = WheelToMove( &wI->sw[no-5],
			 ItemShopDispStart, itemShopSellCount-5, mouse.wheel );

		switch(ItemShopMode){
		case 0:
			ChangeItemShopPriceBuy();
			ChangeItemShopNumBuy();
			break;
		case 1:case 4:case 5:
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			break;
		case 2:
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		}
	}

	return ReturnFlag;
}

BOOL MenuSwitchItemShopNumMinus( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = ItemShopDispStart + (no-EnumGraphItemShopMinus1);
	char okflag;
	int sub;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		okflag = 0;
		switch(ItemShopMode){
		case 0:
			if (itemShopList[Num].num>0) okflag = 1;
			break;
		case 2:
			okflag = 0;
			if (itemShopList[Num].num>0){
				int TotalPriceBak = ItemShoptotalPrice - itemShopList[Num].price;
				int itemEmpty = 0;
				int i;

				// ボタン押した时、交换后のアイテムの空き数を求める
				for( i = MAX_EQUIP_ITEM; i < MAX_ITEM; i++ ){
					// 交换アイテムの时
					if( pc.item[i].useFlag && pc.item[i].id == tradeItemId ){
						// トータル数から引いても残る时
						if( TotalPriceBak - pc.item[i].num >= 0 ){
							// 实际に引く
							TotalPriceBak -= pc.item[i].num;
							// アイテムの空きプラス
							itemEmpty++;
						}
						else break;
					}
				}
				// 空いているアイテム栏を足す
				itemEmpty += getItemEmpty();

				// 交换予定の必要アイテム栏分を除く
				for(i=0;i<MAX_DRAW_WIN_ITEM;i++) itemEmpty -= itemShopList[i].stack_num;

				// 空き栏が足りないなら
				if ( itemEmpty < 0 ){
					// 必要アイテム栏数を一つ减らすのに几つ减らせば良いかを求める
					i = itemShopList[Num].num % itemShopList[Num].stack_max;
					if (!i) i = itemShopList[Num].stack_max;
	
					// 交换予定数より多くないか
					if ( itemShopList[Num].num >= i ){
						int j = i, k = 0;
						int TotalPriceBak = ItemShoptotalPrice - itemShopList[Num].price*j;

						itemEmpty = 0;
						// ボタン押した时、交换后のアイテムの空き数を求める
						for( i = MAX_EQUIP_ITEM; i < MAX_ITEM; i++ ){
							// 交换アイテムの时
							if( pc.item[i].useFlag && pc.item[i].id == tradeItemId ){
								// トータル数から引いても残る时
								if( TotalPriceBak - pc.item[i].num >= 0 ){
									// 实际に引く
									TotalPriceBak -= pc.item[i].num;
									// アイテムの空きプラス
									itemEmpty++;
								}
								else break;
							}
						}
						// 空いているアイテム栏を足す
						itemEmpty += getItemEmpty();

						for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
							k += itemShopList[i].stack_num;
						}
						i = (itemShopList[Num].num-j+itemShopList[Num].stack_max-1) /
							 itemShopList[Num].stack_max;
						k -= itemShopList[Num].stack_num - i;

						// 空き栏が足るなら
						if ( itemEmpty >= k ){
							sub = j;
							okflag = 1;
						}
					}
				}else{
					sub = 1;
					okflag = 1;
				}
			}
			break;
		case 1:case 4:case 5:
			if (itemShopList[ ItemShopSellBuf[Num] ].num>1) okflag = 1;
			break;
		case 3:
			if( itemCountTotal > 0 ) okflag = 1;
			break;
		}
		if (okflag){
			switch(ItemShopMode){
			case 0:	strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYMINUS );		break;
			case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLMINUS );		break;
			case 2:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADEMINUS );	break;
			case 3:	strcpy( OneLineInfoStr, ML_STRING(856, "减少物品交出的数目。") );	break;
			case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPMINUS );		break;
			case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRMINUS );	break;
			}
		}else{
			switch(ItemShopMode){
			case 0:	strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYMINUS_MIN );		break;
			case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLMINUS_MIN );		break;
			case 2:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADEMINUS_MIN );	break;
			case 3:	strcpy( OneLineInfoStr, ML_STRING(348, "已经无法减少了。") );	break;
			case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPMINUS_MIN );		break;
			case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRMINUS_MIN );	break;
			}
		}
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		switch(ItemShopMode){
		case 0:
			if (itemShopList[Num].num>0){
				itemShopList[Num].num--;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			ItemShopInfoNo = -1;

			ChangeItemShopNumBuy();
			ChangeItemShopAllPriceBuy();
			ChangeItemShopBuyAllNumBuy();
			break;
		case 1:case 4:case 5:
			if (itemShopList[ ItemShopSellBuf[Num] ].num>1){
				itemShopList[ ItemShopSellBuf[Num] ].num--;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			ItemShopInfoNo = -1;

			ChangeItemShopNumSell();
			ChangeItemShopAllPriceSell();
			ChangeItemShopBuyAllNumSell();
			break;
		case 2:
			if (okflag){
				itemShopList[Num].num -= sub;
				itemShopList[Num].stack_num =
					 (itemShopList[Num].num+itemShopList[Num].stack_max-1) /
					 itemShopList[Num].stack_max;
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			ItemShopInfoNo = -1;

			ChangeItemShopAllPriceTrade();
			ChangeItemShopBuyAllNumTrade();
			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			break;
		case 3:
			if( itemCountTotal > 0){
				itemCountNpcMsgNo = 0;
				itemCountTotal--;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else {
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
				itemCountTotal = min( itemCountInfo.total - itemCountInfo.count, ItemShoptotalNum );
				itemCountTotal = min( itemCountTotal, itemCountMaxCnt );
			}
			ChangeItemShopPriceCount();
			ChangeItemShopNumCount();
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_MinusButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_MinusButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_MinusButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopNumPlus( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = ItemShopDispStart + (no-EnumGraphItemShopPlus1);
	char okflag;
	int add;
	int Info;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		okflag = 0;
		switch(ItemShopMode){
		case 0:
			if ( !itemShopList[Num].useFlag ) break;
			if (itemShopList[Num].num<20) okflag = 1;
			break;
		case 1:case 4:case 5:
			if ( ItemShopSellBuf[Num]<0 ) break;
			if (itemShopList[ ItemShopSellBuf[Num] ].num<itemShopList[ ItemShopSellBuf[Num] ].stack_max) okflag = 1;
			//最大所持金额超えるなら
			if( pc.gold + ItemShoptotalPrice  + itemShopList[Num].price > MAX_GOLD ) okflag = 0;
			break;
		case 2:
			if ( !itemShopList[Num].useFlag ) break;
			Info = -1;
			// 交换アイテムが足りない时はボタン押せなくする
			if( tradeItemCnt < ItemShoptotalPrice + itemShopList[Num].price ){
				Info = -2;
			}else{
				int TotalPriceBak = ItemShoptotalPrice + itemShopList[Num].price;
				int itemEmpty = 0;
				int i,j;

				// ボタン押した时、交换后のアイテムの空き数を求める
				for( j = MAX_EQUIP_ITEM; j < MAX_ITEM; j++ ){
					// 交换アイテムの时
					if( pc.item[j].useFlag && pc.item[j].id == tradeItemId ){
						// トータル数から引いても残る时
						if( TotalPriceBak - pc.item[j].num >= 0 ){
							// 实际に引く
							TotalPriceBak -= pc.item[j].num;
							// アイテムの空きプラス
							itemEmpty++;
						}
						else break;
					}
				}
				// 空いているアイテム栏を足す
				itemEmpty += getItemEmpty();

				// 交换予定の必要アイテム栏分を除く
				for(i=0;i<MAX_DRAW_WIN_ITEM;i++) itemEmpty -= itemShopList[i].stack_num;

				// 交换数追加で必要アイテム栏增えるなら
				i = (itemShopList[Num].num+1+itemShopList[Num].stack_max-1) /
					 itemShopList[Num].stack_max;
				if ( i > itemShopList[Num].stack_num ) itemEmpty--;

				// 空き栏がないなら
				if ( itemEmpty < 0 ){
					int k = 0, l;

					itemEmpty = -itemEmpty;
					// ボタン押した时、交换后のアイテムの空き数を求める
					for( ; j < MAX_ITEM; j++ ){
						// 交换アイテムの时
						if( pc.item[j].useFlag && pc.item[j].id == tradeItemId );
						else continue;
						// アイテム栏に空きを作るために、几つ交换すれば良いかを求める
						i = ( (pc.item[j].num-TotalPriceBak) + (itemShopList[Num].price-1) ) / itemShopList[Num].price;
						i++;
	
						// 手持ち数が足りるか确认
						if( tradeItemCnt < ItemShoptotalPrice + (itemShopList[Num].price*i) ){
							Info = -3;
							break;
						}
						// 足りるなら
						else{
							// 交换增加数保存
							k += i;
							// アイテム栏几つ消费するかを计算
							l = (itemShopList[Num].num+k+itemShopList[Num].stack_max-1) /
								 itemShopList[Num].stack_max;

							if ( l-itemShopList[Num].stack_num<=itemEmpty ){
								add = k;
								okflag = 1;
								break;
							}
							itemEmpty++;
						}
					}
					if ( Info==-1 && !(j<MAX_ITEM) ) Info = -3;
				}else{
					add = 1;
					okflag = 1;
				}
			}
			break;
		case 3:
			if ( Num!=0 ) break;
			if( itemCountNpcMainType == 0){
				if( ItemShoptotalNum - itemCountTotal > 0 && itemCountTotal + itemCountInfo.count < itemCountInfo.total &&
					itemCountTotal < itemCountMaxCnt){
					okflag = 1;
				}
			}else{
				if( itemCountTotal < itemCountInfo.stack && itemCountInfo.count - itemCountTotal > 0 &&
					ItemShoptotalPrice < pc.gold && itemCountTotal < itemCountMaxCnt){
					okflag = 1;
				}
			}
			break;
		}
		if (okflag){
			switch(ItemShopMode){
			case 0:	strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYPLUS );		break;
			case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLPLUS );		break;
			case 2:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADEPLUS );		break;
			case 3:	strcpy( OneLineInfoStr, ML_STRING(857, "增加物品交出的数目。") );		break;
			case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPPLUS );		break;
			case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRPLUS );	break;
			}
		}else{
			switch(ItemShopMode){
			case 0:	strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYPLUS_MAX );		break;
			case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLPLUS_MAX );		break;
			case 2:	strcpy( OneLineInfoStr, MWONELINE_SHOP_TRADEPLUS_MAX );		break;
			case 3:	strcpy( OneLineInfoStr, ML_STRING(345, "已经无法增加了。") );	break;
			case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPPLUS_MAX );		break;
			case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRPLUS_MAX );	break;
			}
		}
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		switch(ItemShopMode){
		case 0:
			if ( !itemShopList[Num].useFlag ) break;
			if (itemShopList[Num].num<20){
				itemShopList[Num].num++;
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			ChangeItemShopNumBuy();
			ChangeItemShopAllPriceBuy();
			ChangeItemShopBuyAllNumBuy();
			break;
		case 1:case 4:case 5:
			if ( ItemShopSellBuf[Num]<0 ) break;
			if (itemShopList[ ItemShopSellBuf[Num] ].num<itemShopList[ ItemShopSellBuf[Num] ].stack_max){
				//最大所持金额超えるなら
				if( pc.gold + ItemShoptotalPrice  + itemShopList[Num].price > MAX_GOLD ){
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}else{
					itemShopList[ ItemShopSellBuf[Num] ].num++;
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			ChangeItemShopNumSell();
			ChangeItemShopAllPriceSell();
			ChangeItemShopBuyAllNumSell();

			//最大所持金额超えるなら
			if( pc.gold + ItemShoptotalPrice > MAX_GOLD ){
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
				ItemShopInfoNo = -2;
			}
			break;
		case 2:
			if ( !itemShopList[Num].useFlag ) break;
			ItemShopInfoNo = Info;
			if (okflag){
				itemShopList[Num].num += add;
				// アイテム栏几つ消费するかを保存
				itemShopList[Num].stack_num =
					 (itemShopList[Num].num+itemShopList[Num].stack_max-1) /
					 itemShopList[Num].stack_max;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			ChangeItemShopPriceTrade();
			ChangeItemShopNumTrade();
			ChangeItemShopAllPriceTrade();
			ChangeItemShopBuyAllNumTrade();
			break;
		case 3:
			if ( Num!=0 ) break;
			if( itemCountNpcMainType == 0){
				if( ItemShoptotalNum - itemCountTotal > 0 && itemCountTotal + itemCountInfo.count < itemCountInfo.total &&
					itemCountTotal < itemCountMaxCnt){
					ItemShopInfoNo = -1;
					itemCountTotal++;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else if( ItemShoptotalNum - itemCountTotal <= 0){
					ItemShopInfoNo = -2;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}else if( itemCountTotal + itemCountInfo.count >= itemCountInfo.total){
					ItemShopInfoNo = -3;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}
			}else {
				if( itemCountTotal < itemCountInfo.stack && itemCountInfo.count - itemCountTotal > 0 &&
					ItemShoptotalPrice < pc.gold && itemCountTotal < itemCountMaxCnt){
					ItemShopInfoNo = -1;
					itemCountTotal++;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else if( itemCountTotal >= itemCountInfo.stack){
					ItemShopInfoNo = -2;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}else if( itemCountInfo.count - itemCountTotal <= 0){
					ItemShopInfoNo = -3;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}else if( ItemShoptotalPrice >= pc.gold){
					ItemShopInfoNo = -4;
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
					itemCountTotal = 0;
				}
			}
			ChangeItemShopPriceCount();
			ChangeItemShopNumCount();
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_PlusButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_PlusButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_PlusButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchItemShopItemBoxBuy( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};
	int Num = ItemShopDispStart + (no-EnumGraphItemShopItemBox1);

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// 重なってるなら枠表示
	if ( flag & MENU_MOUSE_OVER ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
////		strcpy( OneLineInfoStr, MWONELINE_SHOP_BUYITEMPANEL );
	}

	if( !itemShopList[Num].useFlag ) return ReturnFlag;

	if ( flag & MENU_MOUSE_OVER ){
		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( ItemBoxOldPage[no-EnumGraphItemShopItemBox1] != Num ) ItemShopInfoPage = 0;
		ItemBoxOldPage[no-EnumGraphItemShopItemBox1] = Num;

		// アイテムがあり、右クリックしたら说明ページを进める
		if( flag & MENU_MOUSE_RIGHT ){
			ItemShopInfoPage++;
			if( ItemShopInfoPage >= ITEM_MEMO_PAGE ) ItemShopInfoPage = 0;

			ReturnFlag=TRUE;
		}

		// アイテム说明をしてもらうため登録
		ItemShopItemInfoNo = Num + 100;
	}

	// 装备不可能なら
	if( !itemShopList[Num].buyOkFlag ){
		//フォント用バッファの区切り
		FontBufCut(FONT_PRIO_WIN);
		StockFontBuffer( wI->wx+wI->sw[no].ofx+1, wI->wy+wI->sw[no].ofy+31, FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_RED, "×", 0, 0 ); //MLHIDE
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, itemShopList[Num].graNo, 0, &bm );

	return ReturnFlag;
}

BOOL MenuSwitchItemShopItemBoxCount( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};
	int Num = ItemShopDispStart + (no-EnumGraphItemShopItemBox1);

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// アイテムがあり、右クリックしたら说明ページを进める
	if( flag & MENU_MOUSE_RIGHT ){
		ItemShopInfoPage++;
		if( ItemShopInfoPage >= ITEM_MEMO_PAGE ) ItemShopInfoPage = 0;

		ReturnFlag=TRUE;
	}

	// 重なってるなら枠表示
	if ( flag & MENU_MOUSE_OVER ){
		// アイテム说明をしてもらうため登録
		ItemShopItemInfoNo = 200;

		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, itemCountInfo.graNo, 0, &bm );

	return ReturnFlag;
}

char ItemShopSettleDropItemShopFlag = 0;

void ItemShopSettleDropItemShop()
{
////	int i,j;

	if (ItemShopSettleDropItemShopFlag){
		ItemShopSettleDropItemShopFlag = 0;
		return;
	}

	itemShopList[ ItemShopSellBuf[ (int)WinDD_ObjData() ] ].num = 0;
	PcSellTarget&=~( 1<<( itemShopList[ ItemShopSellBuf[ (int)WinDD_ObjData() ] ].place-8) );

	ItemShopSellBuf[ (int)WinDD_ObjData() ]=-1;
/***
	itemShopSellCount--;
	j=0;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		if (ItemShopSellBuf[i]<0) continue;

		ItemShopSellBuf[j]=ItemShopSellBuf[i];
		j++;
	}
	for(;j<MAX_DRAW_WIN_ITEM;j++) ItemShopSellBuf[j]=-1;
***/
	if (itemShopSellCount-5<ItemShopDispStart) ItemShopDispStart = itemShopSellCount-5;
	if (ItemShopDispStart<0) ItemShopDispStart = 0;
	// つまみを移动
	NumToScrollVMove( &wI->sw[EnumBtItemShopScroll], itemShopSellCount-5, ItemShopDispStart );

	// 表示を更新
	ChangeItemShopPriceSell();
	ChangeItemShopNumSell();
	ChangeItemShopAllPriceSell();
	ChangeItemShopBuyAllNumSell();

	ItemShopInfoNo = -1;
}

BOOL MenuSwitchItemShopItemBoxSell( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};
	struct BLT_MEMBER bm2={0};
	int DragitemNo, DropitemNo;
	int Num = ItemShopDispStart + (no-EnumGraphItemShopItemBox1);
	char str[256];

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( ItemBoxOldPage[no-EnumGraphItemShopItemBox1] != Num ) ItemShopInfoPage = 0;
	ItemBoxOldPage[no-EnumGraphItemShopItemBox1] = Num;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( ( WinDD_CheckObjType() != WINDD_ITEMSHOP ) && ( WinDD_CheckObjType() != WINDD_ITEM ) ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		if ( WinDD_CheckObjType() == WINDD_ITEMSHOP ){
#ifdef PUK2_NEWDRAG
			DropitemNo = (int)WinDD_ObjData();
#else
			ItemShopSettleDropItemShopFlag = 1;
			DropitemNo = (int)WinDD_GetObject();
#endif

			if ( DropitemNo != Num ){
				char xchg;
				xchg = ItemShopSellBuf[Num];
				ItemShopSellBuf[Num] = ItemShopSellBuf[DropitemNo];
				ItemShopSellBuf[DropitemNo] = xchg;

				// 表示を更新
				ChangeItemShopPriceSell();
				ChangeItemShopNumSell();
			}
#ifdef PUK2_NEWDRAG
			WinDD_AcceptObject();
#endif
		}
	}

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if ( ItemShopSellBuf[Num] >= 0 ){
			switch(ItemShopMode){
			case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLITEMPANEL );		break;
			case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPITEMPANEL );		break;
			case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRITEMPANEL );	break;
			}
		}
	}

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// アイテム栏を左ダブルクリックしたとき
		if ( mouse.onceState & MOUSE_LEFT_DBL_CRICK ){
			WinDD_DragFinish();

			itemShopList[ ItemShopSellBuf[Num] ].num = 0;
			PcSellTarget&=~( 1<<( itemShopList[ ItemShopSellBuf[Num] ].place-8) );
	
			ItemShopSellBuf[Num]=-1;
	
			// 表示を更新
			ChangeItemShopPriceSell();
			ChangeItemShopNumSell();
			ChangeItemShopAllPriceSell();
			ChangeItemShopBuyAllNumSell();
	
			ItemShopInfoNo = -1;
		}
	}
	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// アイテムがあるとき
		if ( ItemShopSellBuf[Num] >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragShopItem( Num );
#else
				WinDD_DragStart( WINDD_ITEMSHOP, (void *)(Num) );
#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEMSHOP ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_ITEMSHOP ) && ( WinDD_ButtonNo()==no ) ){
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				// 自分の上でないなら
				if ( !(flag&MENU_MOUSE_DRAGOVER) ){
					DragitemNo = (int)WinDD_ObjData();
					WinDD_DragFinish();
					WinDD_DropObject( WINDD_ITEMSHOP, (void *)(DragitemNo), ItemShopSettleDropItemShop, mouse.nowPoint.x, mouse.nowPoint.y );
				}
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEMSHOP ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_ITEMSHOP ) && ( WinDD_ButtonNo()==no ) ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, itemShopList[ ItemShopSellBuf[DragitemNo] ].graNo, 0, &bm2 );
		}
#endif

		// ドラッグ中のアイテムを自分が表示しているなら
		if (DragitemNo==Num){
			// 枠表示
			StockBoxDispBuffer( wI->wx+wI->sw[no].ofx+2, wI->wy+wI->sw[no].ofy+2,
				wI->wx+wI->sw[no].ofx+46, wI->wy+wI->sw[no].ofy+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	// 重なってるなら枠表示
	if ( flag & MENU_MOUSE_OVER ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}
	if ( flag & MENU_MOUSE_DRAGOVER ){
		// アイテムがあるとき
#ifdef PUK2_SHOPITEMMOVE
		if ( WinDD_CheckObjType() == WINDD_ITEMSHOP ){
#else
		if ( ItemShopSellBuf[Num] >= 0 ){
#endif
			StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
				wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
		}
	}

	if( ItemShopSellBuf[Num]<0 ) return FALSE;

	if ( flag & MENU_MOUSE_OVER ){
		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( ItemBoxOldPage[no-EnumGraphItemShopItemBox1] != Num ) ItemShopInfoPage = 0;
		ItemBoxOldPage[no-EnumGraphItemShopItemBox1] = Num;

		// アイテムがあり、右クリックしたら说明ページを进める
		if( flag & MENU_MOUSE_RIGHT ){
			ItemShopInfoPage++;
			if( ItemShopInfoPage >= ITEM_MEMO_PAGE ) ItemShopInfoPage = 0;

			ReturnFlag=TRUE;
		}

		// アイテム说明をしてもらうため登録
		ItemShopItemInfoNo = ItemShopSellBuf[Num] + 100;

		// 刻印されていたら１行インフォに表示
		if( (strlen( itemShopList[ ItemShopSellBuf[Num] ].freeName ) > 0 ) &&
			(itemShopList[ ItemShopSellBuf[Num] ].otherflg & ITEM_ETC_FLAG_INCUSE) ){
			sprintf( str, "%s" ITEM_INCUSE_STRING, itemShopList[ ItemShopSellBuf[Num] ].name ); //MLHIDE
			strcpy( OneLineInfoStr, str );
		}else if( itemShopList[ ItemShopSellBuf[Num] ].otherflg & ITEM_ETC_FLAG_HANKO ){
			//ハンコ
			sprintf( str, "%s" ITEM_HANKO_STRING, itemShopList[ ItemShopSellBuf[Num] ].freeName ); //MLHIDE
			strcpy( OneLineInfoStr, str );
		}
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, itemShopList[ ItemShopSellBuf[Num] ].graNo, 0, &bm );

	return ReturnFlag;
}

BOOL MenuSwitchItemShopItemPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	int i, x, y;
	int itemNo, DragitemNo;
	char str[256];
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			x = wI->wx + wI->sw[no].ofx + 2 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 2 + ( (i/ITEM_DRAW_COLUMN) * 50 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if ( itemNo >= 0 ){
			switch(ItemShopMode){
			case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLMYITEM );	break;
			case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPMYITEM );		break;
			case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRMYITEM );	break;
			}
		}
	}

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) ItemShopInfoPage = 0;
	olditemNo = itemNo;

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		switch(ItemShopMode){
		case 1:case 4:case 5:
			// 自分のウィンドウがドラッグ元の时
			if ( WinDD_WinType()==MENU_WINDOW_ITEMSHOP || WinDD_WinType()==MENU_WINDOW_NONE ){
				if (0<=PcToItemShop[itemNo]&&PcToItemShop[itemNo]<MAX_DRAW_WIN_ITEM){
					// まだ登録されてなくて卖れるなら
					if ( !( PcSellTarget&(1<<itemNo) ) && itemShopList[ PcToItemShop[itemNo] ].buyOkFlag ){
						// 空き栏を搜す
						for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
							if (ItemShopSellBuf[i]<0) break;
						}
						if (i<MAX_DRAW_WIN_ITEM){
							//最大所持金额超えるなら
							if( ItemShopMode==1 && pc.gold + ItemShoptotalPrice + itemShopList[PcToItemShop[itemNo]].price > MAX_GOLD ){
								// ＮＧ音（短い）
								play_se( SE_NO_NG, 320, 240 );
								ItemShopInfoNo = -2;
							}else{
								ItemShopSellBuf[i] = PcToItemShop[itemNo];
								itemShopList[ PcToItemShop[itemNo] ].num = itemShopList[ PcToItemShop[itemNo] ].stack_max;
								if ( itemShopList[ PcToItemShop[itemNo] ].num <= 0 )
									itemShopList[ PcToItemShop[itemNo] ].num = 1;
////								itemShopList[ PcToItemShop[itemNo] ].num = 1;
	
								PcSellTarget|=(1<<itemNo);
	
								// つまみを移动
								NumToScrollVMove( &wI->sw[EnumBtItemShopScroll], itemShopSellCount-5, ItemShopDispStart );
	
								// 表示を更新
								ChangeItemShopPriceSell();
								ChangeItemShopNumSell();
								ChangeItemShopAllPriceSell();
								ChangeItemShopBuyAllNumSell();
							}
						}
						else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
					}
					else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}
				else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				WinDD_DragFinish();
			}
			ReturnFlag=TRUE;
			break;
		default:
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
			break;
		}
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// ドラッグできるモードのとき
			switch(ItemShopMode){
			case 1:case 4:case 5:
				// 右键したとき
				if( flag & MENU_MOUSE_LEFT ){
					// その场所にアイテムがあって、 まだ登録されてないなら
					if ( 0<=PcToItemShop[itemNo] && PcToItemShop[itemNo]<MAX_DRAW_WIN_ITEM && !( PcSellTarget&(1<<itemNo) ) ){
						// 卖れるなら
						if( itemShopList[ PcToItemShop[i] ].buyOkFlag ){
							// ドラッグ开始
#ifdef PUK2_NEWDRAG
							DragItem( itemNo+MAX_EQUIP_ITEM, FALSE );
#else
							WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+8) );
#endif
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						}
					}
					ReturnFlag=TRUE;
				}
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( flag & MENU_MOUSE_RIGHT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					ItemShopInfoPage++;
					if( ItemShopInfoPage >= pc.item[itemNo+MAX_EQUIP_ITEM].memoPage ) ItemShopInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_ITEMSHOP ){
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				DragitemNo = (int)WinDD_ObjData();
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_ITEMSHOP ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
		}
#endif

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo >= MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + 2 + ( ( (DragitemNo-MAX_EQUIP_ITEM)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 2 + ( ( (DragitemNo-MAX_EQUIP_ITEM)/ITEM_DRAW_COLUMN ) * 50 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	// アイテム选择枠
	if( itemNo >= 0 ){
		x = wI->wx + wI->sw[no].ofx + 2 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 2 + ( (itemNo/ITEM_DRAW_COLUMN) * 50 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
			if ( (ItemShopMode==1)||(ItemShopMode==4)||(ItemShopMode==5) ){
				if ( PcToItemShop[i]>=0 ){
					// アイテム说明をしてもらうため登録
					ItemShopItemInfoNo = itemNo + MAX_EQUIP_ITEM;
				}
			}else{
				ItemShopItemInfoNo = itemNo + MAX_EQUIP_ITEM;
			}
			// 刻印されていたら１行インフォに表示
			if ( (strlen( pc.item[itemNo+MAX_EQUIP_ITEM].freeName ) > 0 ) &&
				 (pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_INCUSE) ){
				sprintf( str, "%s" ITEM_INCUSE_STRING, pc.item[itemNo+MAX_EQUIP_ITEM].name ); //MLHIDE
				strcpy( OneLineInfoStr, str );
			}else if( pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_HANKO ){
				//ハンコ
				sprintf( str, "%s" ITEM_HANKO_STRING, pc.item[itemNo+MAX_EQUIP_ITEM].freeName ); //MLHIDE
				strcpy( OneLineInfoStr, str );
			}
		}
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	switch(ItemShopMode){
	case 0:case 2:case 3:
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			// アイテムがあるなら表示
			if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
				x = wI->wx + wI->sw[no].ofx + 26 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = wI->wy + wI->sw[no].ofy + 26 + ( (i/ITEM_DRAW_COLUMN) * 50 );

				if ( pc.item[i+MAX_EQUIP_ITEM].id == tradeItemId ){
					StockBoxDispBuffer( x-22, y-22, x+22, y+22, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
				}
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

				// 个数表示
				if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
					sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );            //MLHIDE
					StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
				}
			}
		}
		break;
	case 1:case 4:case 5:
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( PcToItemShop[i]<0 ) continue;
			// アイテムがあるなら表示
			if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
				x = wI->wx + wI->sw[no].ofx + 26 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = wI->wy + wI->sw[no].ofy + 26 + ( (i/ITEM_DRAW_COLUMN) * 50 );

				// 卖ものに登録されているなら
				if ( PcSellTarget&(1<<i) ){
					StockBoxDispBuffer( x-22, y-22, x+22, y+22, DISP_PRIO_WIN2, SYSTEM_PAL_BLUE5, 0 );
				}
				// 卖れないなら
				if( !itemShopList[ PcToItemShop[i] ].buyOkFlag ){
					StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );
				}
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

				// 个数表示
				if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
					sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );            //MLHIDE
					StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
				}
			}
		}
		break;
	}

	return ReturnFlag;
}

BOOL MenuSwitchItemShopShopPanel( int no, unsigned int flag )
{
	int DropitemNo;

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		switch(ItemShopMode){
		case 1:	strcpy( OneLineInfoStr, MWONELINE_SHOP_SELLSHOPPANEL );		break;
		case 4:	strcpy( OneLineInfoStr, MWONELINE_SHOP_APPSHOPPANEL );		break;
		case 5:	strcpy( OneLineInfoStr, MWONELINE_SHOP_REPAIRSHOPPANEL );	break;
		default: return FALSE;
		}
	}

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData()-MAX_EQUIP_ITEM;
#else
		DropitemNo = (int)WinDD_GetObject()-MAX_EQUIP_ITEM;
#endif

		if (DropitemNo>=0){
			if (0<=PcToItemShop[DropitemNo]&&PcToItemShop[DropitemNo]<MAX_DRAW_WIN_ITEM){
				// まだ登録されてないなら
				if ( !( PcSellTarget&(1<<DropitemNo) ) ){
					// 卖れるなら
					if( itemShopList[ PcToItemShop[DropitemNo] ].buyOkFlag ){
						int i;
						// 空き栏を搜す
						for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
							if (ItemShopSellBuf[i]<0) break;
						}
						if (i<MAX_DRAW_WIN_ITEM){
							//最大所持金额超えるなら
							if( ItemShopMode == 1 && pc.gold + ItemShoptotalPrice + itemShopList[PcToItemShop[DropitemNo]].price > MAX_GOLD ){
								// ＮＧ音（短い）
								play_se( SE_NO_NG, 320, 240 );
								ItemShopInfoNo = -2;
							}else{
								ItemShopSellBuf[i] = PcToItemShop[DropitemNo];
								itemShopList[ PcToItemShop[DropitemNo] ].num = itemShopList[ PcToItemShop[DropitemNo] ].stack_max;
								if ( itemShopList[ PcToItemShop[DropitemNo] ].num <= 0 )
									itemShopList[ PcToItemShop[DropitemNo] ].num = 1;
////								itemShopList[ PcToItemShop[DropitemNo] ].num = 1;
	
								PcSellTarget|=(1<<DropitemNo);
	
								// つまみを移动
								NumToScrollVMove( &wI->sw[EnumBtItemShopScroll], itemShopSellCount-5, ItemShopDispStart );
	
								// 表示を更新
								ChangeItemShopPriceSell();
								ChangeItemShopNumSell();
								ChangeItemShopAllPriceSell();
								ChangeItemShopBuyAllNumSell();
							}
						}
					}
				}
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	// 重なってるなら枠表示
#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx+3, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy, DISP_PRIO_WIN2, BoxColor, 0 );
	}
#else
	if ( flag & MENU_MOUSE_DRAGOVER ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx+3, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy, DISP_PRIO_WIN2, BoxColor, 0 );
	}
#endif

	return FALSE;
}

BOOL MenuSwitchItemShopInfo( int no, unsigned int flag )
{
	//const short x = 0, y = 35, w = 640, h = 383;
	const short x = 0, y = 35, w = 640, h = 383;
	const short Yx = 108, Yy = 346, Yw = 66, Yh = 17;
	const short Nx = 471, Ny = 346, Nw = 66, Nh = 17;
	int GraNo1, GraNo2;
	struct BLT_MEMBER bm={0};
	char Yflg = 0, Nflg = 0;
	int i,j,k;
	int Ix, Iy;
	char str[51];

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// Ｙｅｓ、Ｎｏボタンのあたり判定
	if ( MakeHitBox( x+Yx, y+Yy, x+Yx+Yw, y+Yy+Yh, -1 ) ) Yflg = 1;
	if ( MakeHitBox( x+Nx, y+Ny, x+Nx+Nw, y+Ny+Nh, -1 ) ) Nflg = 1;

	// Ｙｅｓ、Ｎｏボタンの处理
	GraNo1 = GID_BigYesButtonOn;
	if (Yflg){
		GraNo1 = GID_BigYesButtonOver;
		// 一行インフォ
		strcpy( OneLineInfoStr, ItemShopInfoDispOneLineYes );
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			wI->sw[no].Enabled = FALSE;

			if (ItemShopInfoFunc) ItemShopInfoFunc(0);

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			GraNo1 = GID_BigYesButtonOff;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
	}
	GraNo2 = GID_BigNoButtonOn;
	if (Nflg){
		GraNo2 = GID_BigNoButtonOver;
		// 一行インフォ
		strcpy( OneLineInfoStr, ItemShopInfoDispOneLineNo );
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			wI->sw[no].Enabled = FALSE;

			if (ItemShopInfoFunc) ItemShopInfoFunc(1);

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			GraNo2 = GID_BigNoButtonOff;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
	}

	// Ｙｅｓ、Ｎｏボタンの描画
	StockDispBuffer_PUK2( x+Yx, y+Yy, DISP_PRIO_YES_NO_WND, GraNo1, 0, 1, &bm );
	StockDispBuffer_PUK2( x+Nx, y+Ny, DISP_PRIO_YES_NO_WND, GraNo2, 0, 1, &bm );

	// アイテム等の描画
	for(i=0, k=0;i<4;i++){
		for(j=0;j<5;j++, k++){
			Ix = x+18+i*152, Iy = y+12+j*65;
			if (ItemShopInfoDispItem[k].useFlag){
				StockDispBuffer( Ix+24, Iy+24, DISP_PRIO_YES_NO_WND, ItemShopInfoDispItem[k].graNo, 0, &bm );

				if (ItemShopInfoDispItem[k].stack){
					sprintf( str, "%2d X %2d", ItemShopInfoDispItem[k].stack, ItemShopInfoDispItem[k].num ); //MLHIDE
				}else{
					sprintf( str, "X %2d", ItemShopInfoDispItem[k].num );            //MLHIDE
				}
				StockFontBuffer( Ix+57, Iy+33, FONT_PRIO_FRONT2, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, BoxColor );
				StockFontBuffer( Ix, Iy+50, FONT_PRIO_FRONT2, FONT_KIND_SIZE_12, ItemShopInfoDispItem[k].pal, ItemShopInfoDispItem[k].name, 0, BoxColor );
			}
			StockDispBuffer( Ix+24, Iy+24, DISP_PRIO_YES_NO_WND, GID_ItemPanel, 0, &bm );
		}
	}

	StockFontBuffer( x+190, y+342, FONT_PRIO_FRONT2, FONT_KIND_SIZE_12, FONT_PAL_WHITE, ItemShopInfoDispStr1, 0, BoxColor );
	StockFontBuffer( x+190, y+358, FONT_PRIO_FRONT2, FONT_KIND_SIZE_12, FONT_PAL_WHITE, ItemShopInfoDispStr2, 0, BoxColor );
	if (ItemShopInfoDispNum[0].useflag){
		sprintf( str, "%d", ItemShopInfoDispNum[0].num );                   //MLHIDE
		DrawGraphicNumber( x+ItemShopInfoDispNum[0].x, y+ItemShopInfoDispNum[0].y,
			str, G_NUM_SIZE__9, FONT_PAL_YELLOW, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_YES_NO_WND );
	}

	if (ItemShopInfoDispNum[1].useflag){
		sprintf( str, "%d", ItemShopInfoDispNum[1].num );                   //MLHIDE
		DrawGraphicNumber( x+ItemShopInfoDispNum[1].x, y+ItemShopInfoDispNum[1].y,
			str, G_NUM_SIZE__9, FONT_PAL_YELLOW, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_YES_NO_WND );
	}

	MenuWindowCommonDraw( GID_CommonWindow, x, y, w, h, DISP_PRIO_YES_NO_WND, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}










//====================================//
//		スキルを扱うショップ		  //
//====================================//

static int SkillShopWindowType;
static char SkillShopMode;
static int SkillShopDispStart1;
static int SkillShopDispStart2;
static int SkillShopScrollMax1;
static int SkillShopScrollMax2;

#ifdef PUK3_PETSKILLSHOPEX
static int SkillShopTribe;
#endif
static int SkillShopNpcGra;

static int SkillShopEnpty;

static int SkillShopInfoNo;
static int SkillShopBeforeNo;

static int SkillSelectNo;
static int PetSelectNo;
static int PosSelectNo;

static int SkillShopBackFlag;

static MONSTER_SKILL_SHOP_SELL PetSkillShopSell[16];

static char sortSp[MAX_SKILL];
static char SSpetPos[MAX_PET];

extern int skillUseSlot;
extern int skillCost;
extern int skillLv[MAX_SKILL];
extern int skillSs[MAX_SKILL];
char skillName[MAX_SKILL][81];

extern MONSTER_SKILL_SHOP_SELECT_PET monsterSkillShopSelPet[MAX_PET];
extern MONSTER_SKILL_SHOP_SELECT_POS monsterSkillShopSelPos[MAX_PET_TECH];

void ResetSellPanel( WINDOW_INFO *wi, int start );
void ResetSkillPanel( WINDOW_INFO *wi, int start );
void ResetBuyPCSkillPanel( WINDOW_INFO *wi, int start, int max );
void ResetForgetPCSkillPanel( WINDOW_INFO *wi, int start, int max );
void ChangePetSkillSkillSelectMode( WINDOW_INFO *wi );
void ChangePetSkillPetSelectMode( WINDOW_INFO *wi );
void ChangePetSkillPosSelectMode( WINDOW_INFO *wi );

BOOL closeSkillShopWindow()
{
	if ( SkillShopBackFlag != 2 ){
		if (SkillShopWindowType==serverRequestWinWindowType) serverRequestWinWindowType = -1;
	}
	SkillShopBackFlag = 0;

	return TRUE;
}
//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openPlayerBuySkillShopWindow( char *data )
{
	char str[512];
	int i;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 21, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 5; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 21 );
	}

	// スキル名
	getStringToken( data, '|', 4, sizeof( serverRequestWinStr[0] )-1, serverRequestWinStr[9] );
	makeRecvString( serverRequestWinStr[9] );

	// スキルの说明
	getStringToken( data, '|', 5, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 8; i++ ){
		getMemoLine( serverRequestWinStr[10+i], sizeof( serverRequestWinStr[0] ), str, i, 32 );
	}

	// 使用スキルスロット
	skillUseSlot = getIntegerToken( data, '|', 6 );

	// 费用
	skillCost = getIntegerToken( data, '|', 7 );

	SkillShopScrollMax1 = 0;
	SkillShopScrollMax2 = 0;

	SkillShopMode = 0;

	return openMenuWindow( MENU_WINDOW_SKILLSHOP, OPENMENUWINDOW_HIT, 0 );
}

ACTION *openPlayerForgetSkillShopWindow( char *data )
{
	char str[512];
	int i;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 21, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 5; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 21 );
	}

	for( i = 0; i < MAX_SKILL; i++ ){
		getStringToken( data, '|', 4+i*3+0, 21, serverRequestWinStr[10+i] );
		makeRecvString( serverRequestWinStr[10+i] );
		strcpy( skillName[i], serverRequestWinStr[10+i] );

		if( serverRequestWinStr[10+i][0]!='\0' ){
			skillLv[i] = getIntegerToken( data, '|', 4+i*3+1 );
			skillSs[i] = getIntegerToken( data, '|', 4+i*3+2 );
		}else{
			skillLv[i] = -1;
			skillSs[i] = -1;
		}
	}

	SkillShopScrollMax1 = 0;
	SkillShopScrollMax2 = 0;

	SkillShopMode = 1;

	return openMenuWindow( MENU_WINDOW_SKILLSHOP, OPENMENUWINDOW_HIT, 0 );
}

ACTION *openPlayerPetSkillShopWindow( char *data )
{
	char str[256];
	int i, k, l;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 4, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[5+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 5, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[9+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	memset( &PetSkillShopSell, 0, sizeof( PetSkillShopSell ) );


	k = 6;
	SkillShopScrollMax2 = 0;
	for( i = 0; i < 16; i++ ){
		if( getStringToken( data, '|', k++, sizeof( str ) - 1, str ) == 1 ) break;

		PetSkillShopSell[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= TECH_NAME_LEN ) strcpy( PetSkillShopSell[i].name, str );
		else strcpy( PetSkillShopSell[i].name, "???" );                     //MLHIDE

		PetSkillShopSell[i].fp    = getIntegerToken( data, '|', k++ );
		PetSkillShopSell[i].price = getIntegerToken( data, '|', k++ );

		getStringToken( data, '|', k++, sizeof( str )-1, str );
		makeRecvString( str );
		for( l = 0; l < 2; l++ ){
			getMemoLine( PetSkillShopSell[i].memo[l], sizeof( PetSkillShopSell[0].memo[0] ), str, l, 28 );
		}
		SkillShopScrollMax2++;
	}

	SkillShopScrollMax1 = 0;

	SkillShopMode = 2;

	if (WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo){
		if ( SkillShopBackFlag ){
			SkillShopBackFlag = 0;
			ChangePetSkillSkillSelectMode(WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo);
			return NULL;
		}else{
			WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo->flag |= WIN_INFO_DEL;

			SkillShopBackFlag = 2;
		}
	}else SkillShopBackFlag = 0;

	return openMenuWindow( MENU_WINDOW_SKILLSHOP, OPENMENUWINDOW_HIT, 0 );
}
#ifdef PUK3_PETSKILLSHOPEX
ACTION *openPlayerPetSkillShopWindowEx( char *data )
{
	char str[256];
	int i, k, l;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 4, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[5+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 5, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[9+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	memset( &PetSkillShopSell, 0, sizeof( PetSkillShopSell ) );


	k = 6;
	SkillShopScrollMax2 = 0;
	for( i = 0; i < 16; i++ ){
		if( getStringToken( data, '|', k++, sizeof( str ) - 1, str ) == 1 ) break;

		PetSkillShopSell[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= TECH_NAME_LEN ) strcpy( PetSkillShopSell[i].name, str );
		else strcpy( PetSkillShopSell[i].name, "???" );                     //MLHIDE

		PetSkillShopSell[i].fp    = getIntegerToken( data, '|', k++ );
		PetSkillShopSell[i].price = getIntegerToken( data, '|', k++ );

		getStringToken( data, '|', k++, sizeof( str )-1, str );
		makeRecvString( str );
		for( l = 0; l < 2; l++ ){
			getMemoLine( PetSkillShopSell[i].memo[l], sizeof( PetSkillShopSell[0].memo[0] ), str, l, 28 );
		}
		SkillShopScrollMax2++;
	}

	SkillShopScrollMax1 = 0;

	SkillShopMode = 12;

	if (WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo){
		if ( SkillShopBackFlag ){
			SkillShopBackFlag = 0;
			ChangePetSkillSkillSelectMode(WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo);
			return NULL;
		}else{
			WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo->flag |= WIN_INFO_DEL;

			SkillShopBackFlag = 2;
		}
	}else SkillShopBackFlag = 0;

	return openMenuWindow( MENU_WINDOW_SKILLSHOP, OPENMENUWINDOW_HIT, 0 );
}
#endif

void changePetSkillWindowPetSelect( char *data )
{
	char str[256];
	int equip_level;
	int i,j;

	if (!WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo) return;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	memset( &monsterSkillShopSelPet, 0, sizeof( monsterSkillShopSelPet ) );

	// 装备可能等级取り出し
	equip_level = getIntegerToken( data, '|', 4 );

	for( i = 0, j = 5; i < MAX_PET; i++ ){
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 ) break;

		monsterSkillShopSelPet[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= PET_NAME_LEN ) strcpy( monsterSkillShopSelPet[i].name, str );
		else strcpy( monsterSkillShopSelPet[i].name, "???" );               //MLHIDE

		monsterSkillShopSelPet[i].lv = getIntegerToken( data, '|', j++ );
		monsterSkillShopSelPet[i].maxTech = getIntegerToken( data, '|', j++ );
#ifdef PUK3_PETSKILLSHOPEX
		monsterSkillShopSelPet[i].tribe = -1;
		monsterSkillShopSelPet[i].rideflg = TRUE;
#endif
		// 装备可能なら
		if( equip_level <= monsterSkillShopSelPet[i].lv ) monsterSkillShopSelPet[i].canSetFlg = 1;
	}
#ifdef PUK3_PETSKILLSHOPEX
	// 店が卖对象の种族
	SkillShopTribe = -1;
#endif

	SkillShopScrollMax1 = 0;
	SkillShopScrollMax2 = 0;

	SkillShopMode = 3;

	SkillShopBackFlag = 0;

	ChangePetSkillPetSelectMode( WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo );
}
#ifdef PUK3_PETSKILLSHOPEX
void changePetSkillWindowPetSelectEx( char *data )
{
	char str[256];
	int equip_level;
	int i,j;

	if (!WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo) return;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 16, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// ?畔?
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 4, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[5+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	getStringToken( data, '|', 5, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 4; i++ ){
		getMemoLine( serverRequestWinStr[9+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
	}

	memset( &monsterSkillShopSelPet, 0, sizeof( monsterSkillShopSelPet ) );

	// 装备可能等级取り出し
	equip_level = getIntegerToken( data, '|', 6 );

	// 店が卖对象の种族
	SkillShopTribe = getIntegerToken( data, '|', 7 );

	for( i = 0, j = 8; i < MAX_PET; i++ ){
		if( getStringToken( data, '|', j++, sizeof( str ) - 1, str ) == 1 ) break;

		monsterSkillShopSelPet[i].useFlag = 1;

		makeRecvString( str );
		if( strlen( str ) <= PET_NAME_LEN ) strcpy( monsterSkillShopSelPet[i].name, str );
		else strcpy( monsterSkillShopSelPet[i].name, "???" );               //MLHIDE

		monsterSkillShopSelPet[i].lv = getIntegerToken( data, '|', j++ );
		monsterSkillShopSelPet[i].maxTech = getIntegerToken( data, '|', j++ );
		monsterSkillShopSelPet[i].tribe = getIntegerToken( data, '|', j++ );
		monsterSkillShopSelPet[i].rideflg = getIntegerToken( data, '|', j++ );
		// 装备可能なら
		if( equip_level <= monsterSkillShopSelPet[i].lv ) monsterSkillShopSelPet[i].canSetFlg = 1;
	}

	SkillShopScrollMax1 = 0;
	SkillShopScrollMax2 = 0;

	SkillShopMode = 13;

	SkillShopBackFlag = 0;

	ChangePetSkillPetSelectMode( WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo );
}
#endif

void changePetSkillWindowPosSelect( char *data )
{
	char str[512];
	int i,j,l;

	if (!WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo) return;

	SkillShopWindowType = serverRequestWinWindowType;

	// 店员の絵
	SkillShopNpcGra = getIntegerToken( data, '|', 1 );

	// 店名
	getStringToken( data, '|', 2, 21, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	// 信息
	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 5; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 21 );
	}

	memset( &monsterSkillShopSelPos, 0, sizeof( monsterSkillShopSelPos ) );

	for( i = 0, j = 4; i < MAX_PET_TECH; i++ ){
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );

		//技名が无ければ
		if( str[0] == '\0' ){
			monsterSkillShopSelPos[i].useFlag = 0;
			//何も表示しない
			j += 2;
			continue;
		}

		monsterSkillShopSelPos[i].useFlag = 1;

		if( strlen( str ) <= PET_NAME_LEN ) strcpy( monsterSkillShopSelPos[i].name, str );
		else strcpy( monsterSkillShopSelPos[i].name, "???" );               //MLHIDE

		monsterSkillShopSelPos[i].fp = getIntegerToken( data, '|', j++ );
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );
		for( l = 0; l < 2; l++ ){
			getMemoLine( monsterSkillShopSelPos[i].memo[l], sizeof( monsterSkillShopSelPos[0].memo[0] ), str, l, 28 );
		}
	}

	SkillShopScrollMax1 = 0;
	SkillShopScrollMax2 = 0;

	SkillShopMode = 4;

	SkillShopBackFlag = 0;

	ChangePetSkillPosSelectMode( WindowFlag[MENU_WINDOW_SKILLSHOP].wininfo );
}

//===============================================
//

void ChangePlayerBuyMode( WINDOW_INFO *wi )
{
	int i, j;
	int empty;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillShopPanel].Switch )->graNo = GID_SkillShopPanel;

	wi->sw[EnumGraphSkillShopOK].Enabled = TRUE;

	wi->sw[EnumGraphSkillShopSlotNum].Enabled = TRUE;
	// スロット
	SkillShopScrollMax1 = 0;
	j = 0;
	for(i=0;i<MAX_SKILL;i++){
		if ( job.sortSkill[i].useFlag ){
			j += job.skill[ job.sortSkill[i].index ].slot;
			SkillShopScrollMax1++;
		}
	}
#ifdef PUK2_MAXSKILLSLOT_UP
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "%2d/%2d", j, pc.skillSlot ); //MLHIDE
	empty = pc.skillSlot - j;
	if ( empty < 0 ) empty = 0;
#else
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "%2d/%2d", j, MAX_SKILL ); //MLHIDE
	empty = MAX_SKILL - j;
#endif
	SkillShopScrollMax1 += empty;

	if ( SkillShopScrollMax1 > 10 ){
		for(i=EnumGraphSkillShopScroll;i<=EnumGraphSkillShopScrollBase;i++) wi->sw[i].Enabled = TRUE;
	}else{
		for(i=EnumGraphSkillShopScroll;i<=EnumGraphSkillShopScrollBase;i++) wi->sw[i].Enabled = FALSE;
	}

	wi->sw[EnumGraphSkillShopName].Enabled = TRUE;
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopName].Switch )->text, serverRequestWinStr[9] );

	for(i=0;i<8;i++){
		wi->sw[EnumGraphSkillShopText1+i].Enabled = TRUE;
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText1+i].Switch )->text, serverRequestWinStr[10+i] );
	}

	wi->sw[EnumGraphSkillShopNeedSlot].Enabled = TRUE;
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopNeedSlot].Switch )->text,
		ML_STRING(858, "必要技能栏位数：%2d栏"), skillUseSlot );

	wi->sw[EnumGraphSkillShopPrice].Enabled = TRUE;
	sprintf( SkillShopNum_Price, "%10d", skillCost );                    //MLHIDE

	for(i=0;i<10;i++) wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchNone;

	ResetBuyPCSkillPanel( wi, SkillShopDispStart1, SkillShopScrollMax1 );

	for(i=EnumGraphSkillShopScroll2;i<=EnumGraphSkillShopScrollWheel2;i++) wi->sw[i].Enabled = FALSE;
	for(i=EnumGraphSkillShopSkillSellName1;i<=EnumGraphSkillShopSkillSellGold5;i++) wi->sw[i].Enabled = FALSE;
}

void ChangePlayerForgetMode( WINDOW_INFO *wi )
{
	int i, j, k;
	int empty;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillShopPanel].Switch )->graNo = GID_SkillShopPanel;

	wi->sw[EnumGraphSkillShopOK].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopSlotNum].Enabled = TRUE;
	// スロット
	for(i=0;i<MAX_SKILL;i++){
		if (job.sortSkill[i].useFlag){
			k = 0;
			for(j=0;j<job.sortSkill[i].index;j++){
				if ( job.skill[j].name[0]=='\0' ) k++;
			}
			sortSp[i] = job.sortSkill[i].index - k;
		}else{
			sortSp[i] = -1;
		}
	}

	SkillShopScrollMax1 = 0;
	j = 0;
	for(i=0;i<MAX_SKILL;i++){
		if ( job.sortSkill[i].useFlag ){
			j += job.skill[ job.sortSkill[i].index ].slot;
			SkillShopScrollMax1++;
		}
	}
#ifdef PUK2_MAXSKILLSLOT_UP
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "%2d/%2d", j, pc.skillSlot ); //MLHIDE
	empty = pc.skillSlot - j;
	if ( empty < 0 ) empty = 0;
#else
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "%2d/%2d", j, MAX_SKILL ); //MLHIDE
	empty = MAX_SKILL - j;
#endif
	SkillShopScrollMax1 += empty;

	if ( SkillShopScrollMax1 > 10 ){
		for(i=EnumGraphSkillShopScroll;i<=EnumGraphSkillShopScrollBase;i++) wi->sw[i].Enabled = TRUE;
	}else{
		for(i=EnumGraphSkillShopScroll;i<=EnumGraphSkillShopScrollBase;i++) wi->sw[i].Enabled = FALSE;
	}

	wi->sw[EnumGraphSkillShopName].Enabled = FALSE;

	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText1].Switch )->text, ML_STRING(859, "请指定要忘记的") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText2].Switch )->text, ML_STRING(860, "技能") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText4].Switch )->text, ML_STRING(861, "＊＊＊＊＊＊　注意　＊＊＊＊＊＊") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText5].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText6].Switch )->text, ML_STRING(862, "　技能一旦忘记") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText7].Switch )->text, ML_STRING(863, "　将无法恢复。") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopText8].Switch )->text, ML_STRING(864, "　请斟酌。") );
	for(i=0;i<8;i++) wi->sw[EnumGraphSkillShopText1+i].Enabled = TRUE;

	wi->sw[EnumGraphSkillShopNeedSlot].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopPrice].Enabled = TRUE;
	sprintf( SkillShopNum_Price, "%10d", 0 );                            //MLHIDE

	for(i=0;i<10;i++) wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchSkillShopPlayerFogetPanel;

	ResetForgetPCSkillPanel( wi, SkillShopDispStart1, SkillShopScrollMax1 );

	for(i=EnumGraphSkillShopScroll2;i<=EnumGraphSkillShopScrollWheel2;i++) wi->sw[i].Enabled = FALSE;
	for(i=EnumGraphSkillShopSkillSellName1;i<=EnumGraphSkillShopSkillSellGold5;i++) wi->sw[i].Enabled = FALSE;
}

void ChangePetSkillSkillSelectMode( WINDOW_INFO *wi )
{
	int i,j;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillShopPanel].Switch )->graNo = GID_SkillShopPanelScroll;

	wi->sw[EnumGraphSkillShopOK].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopSlotNum].Enabled = TRUE;
	// スロット
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "" );

	wi->sw[EnumGraphSkillShopScroll].Enabled = FALSE;
	wi->sw[EnumBtSkillShopScroll].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollUp].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollDown].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollLeft].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollRight].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollWheel].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollBase].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopName].Enabled = FALSE;

	for(i=0;i<8;i++) wi->sw[EnumGraphSkillShopText1+i].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopNeedSlot].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopPrice].Enabled = TRUE;
	sprintf( SkillShopNum_Price, "%10d", 0 );                            //MLHIDE

	for(i=0;i<5;i++){
		j = sortPet[i].index;

		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchNone;
		( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillShopSkillPanel1+i].Switch )->graNo = GID_TitlePanelOn;

		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = TRUE;
#ifdef PUK3_PETSKILLSHOPEX
		wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = FALSE;
		if (SkillShopMode==12) wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = TRUE;
#endif
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = TRUE;
		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text[0] = '\0';
#ifdef PUK3_PETSKILLSHOPEX
		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillTribe1+i].Switch )->text[0] = '\0';
#endif
		SkillShopNum_SkillFp[i][0] = '\0';
		SkillShopNum_SkillSlot[i][0] = '\0';

		if ( !pet[j].useFlag ) continue;

		if (pet[j].freeName[0]=='\0') strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, pet[j].name );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, pet[j].freeName );
#ifdef PUK3_PETSKILLSHOPEX
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillTribe1+i].Switch )->text, characterTribeStr[ pet[j].tribe ] );
#endif
		j++;
	}
	for(i=5;i<10;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = FALSE;
	}

	for(i=EnumGraphSkillShopScroll2;i<=EnumGraphSkillShopScrollWheel2;i++) wi->sw[i].Enabled = TRUE;
	ResetSellPanel( wi, SkillShopDispStart2 );
}

void ChangePetSkillPetSelectMode( WINDOW_INFO *wi )
{
	int i, j, k;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillShopPanel].Switch )->graNo = GID_SkillShopPanelScroll;

	wi->sw[EnumGraphSkillShopOK].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopSlotNum].Enabled = TRUE;
	// スロット
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "" );

	wi->sw[EnumGraphSkillShopScroll].Enabled = FALSE;
	wi->sw[EnumBtSkillShopScroll].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollUp].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollDown].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollLeft].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollRight].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollWheel].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollBase].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopName].Enabled = FALSE;

	for(i=0;i<8;i++) wi->sw[EnumGraphSkillShopText1+i].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopNeedSlot].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopPrice].Enabled = TRUE;
	sprintf( SkillShopNum_Price, "%10d", PetSkillShopSell[SkillSelectNo].price ); //MLHIDE

	for(i=0;i<MAX_PET;i++){
		if (sortPet[i].useFlag){
			k = 0;
			for(j=0;j<sortPet[i].index;j++){
				if ( !pet[j].useFlag ) k++;
			}
			SSpetPos[i] = sortPet[i].index - k;
		}else{
			SSpetPos[i] = -1;
		}
	}

	for(i=0;i<5;i++){
		k = SSpetPos[i];
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchSkillShopPetSelectPanel;

		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = TRUE;
#ifdef PUK3_PETSKILLSHOPEX
		wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = FALSE;
		if (SkillShopMode==13) wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = TRUE;
#endif
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = TRUE;
		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text[0] = '\0';
#ifdef PUK3_PETSKILLSHOPEX
		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillTribe1+i].Switch )->text[0] = '\0';
#endif
		SkillShopNum_SkillFp[i][0] = '\0';
		SkillShopNum_SkillSlot[i][0] = '\0';

		if (k<0||!monsterSkillShopSelPet[k].useFlag) continue;

		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, monsterSkillShopSelPet[k].name );
#ifdef PUK3_PETSKILLSHOPEX
		if ( monsterSkillShopSelPet[k].tribe >= 0 ){
			strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillTribe1+i].Switch )->text, characterTribeStr[ monsterSkillShopSelPet[k].tribe ] );
		}
#endif
	}
	for(i=5;i<10;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = FALSE;
	}

	for(i=EnumGraphSkillShopScroll2;i<=EnumGraphSkillShopScrollWheel2;i++) wi->sw[i].Enabled = TRUE;
	ResetSellPanel( wi, SkillShopDispStart2 );
}

void ChangePetSkillPosSelectMode( WINDOW_INFO *wi )
{
	int i,j;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillShopPanel].Switch )->graNo = GID_SkillShopPanelScroll;

	wi->sw[EnumGraphSkillShopOK].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopSlotNum].Enabled = TRUE;
	// スロット
	j = 0;
	for(i=0;i<monsterSkillShopSelPet[PetSelectNo].maxTech;i++){
		if (!monsterSkillShopSelPos[i].useFlag) continue;
		j++;
	}
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSlotNum].Switch )->text, "%2d/%2d", j, monsterSkillShopSelPet[PetSelectNo].maxTech ); //MLHIDE

	wi->sw[EnumGraphSkillShopScroll].Enabled = FALSE;
	wi->sw[EnumBtSkillShopScroll].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollUp].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollDown].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollLeft].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollRight].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollWheel].Enabled = FALSE;
	wi->sw[EnumGraphSkillShopScrollBase].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopName].Enabled = FALSE;

	for(i=0;i<8;i++) wi->sw[EnumGraphSkillShopText1+i].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopNeedSlot].Enabled = FALSE;

	wi->sw[EnumGraphSkillShopPrice].Enabled = TRUE;
	sprintf( SkillShopNum_Price, "%10d", PetSkillShopSell[SkillSelectNo].price ); //MLHIDE

	for(i=0;i<5;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchSkillShopPetSelectPanel;

		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text[0] = '\0';
#ifdef PUK3_PETSKILLSHOPEX
		wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = FALSE;
#endif
		SkillShopNum_SkillFp[i][0] = '\0';
		SkillShopNum_SkillSlot[i][0] = '\0';

		if (!monsterSkillShopSelPet[i].useFlag) continue;
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, monsterSkillShopSelPet[i].name );
	}
	for(i=5;i<10;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = FALSE;
	}

	for(i=EnumGraphSkillShopScroll2;i<=EnumGraphSkillShopScrollWheel2;i++) wi->sw[i].Enabled = TRUE;
	ResetSellPanel( wi, SkillShopDispStart2 );
	ResetSkillPanel( wi, SkillShopDispStart1 );
}

//===============================================
//

void ResetBuyPCSkillPanel( WINDOW_INFO *wi, int start, int max )
{
	int i, j, k;

	j = 0;
	for(i=0;i<10;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = FALSE;
		SkillShopNum_SkillFp[i][0] = '\0';
		SkillShopNum_SkillSlot[i][0] = '\0';

		if ( start+i >= max ) continue;

		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = TRUE;
		if ( !job.sortSkill[start+i].useFlag ){
			j++;
			continue;
		}

		k = job.sortSkill[start+i].index;

		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = TRUE;

		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, job.skill[k].name );
		sprintf( SkillShopNum_SkillFp[i], "%2d", job.skill[k].lv );         //MLHIDE
		sprintf( SkillShopNum_SkillSlot[i], "%2d", job.skill[k].slot );     //MLHIDE
	}
#ifdef PUK3_PETSKILLSHOPEX
	for(i=0;i<5;i++){
		wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = FALSE;
	}
#endif
}

void ResetForgetPCSkillPanel( WINDOW_INFO *wi, int start, int max )
{
	int i, j, k;

	j = 0;
	for(i=0;i<10;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = FALSE;
		SkillShopNum_SkillFp[i][0] = '\0';
		SkillShopNum_SkillSlot[i][0] = '\0';

		if ( start+i >= max ) continue;

		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = TRUE;

		k = sortSp[start+i]/*job.sortSkill[start+i].index*/;
		if ( k<0 || skillLv[k]<0 ){
			j++;
			continue;
		}

		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillLv1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = TRUE;

		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, skillName[k] );
		sprintf( SkillShopNum_SkillFp[i], "%2d", skillLv[k] );              //MLHIDE
		sprintf( SkillShopNum_SkillSlot[i], "%2d", skillSs[k] );            //MLHIDE
	}
#ifdef PUK3_PETSKILLSHOPEX
	for(i=0;i<5;i++){
		wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = FALSE;
	}
#endif
}

void ResetSellPanel( WINDOW_INFO *wi, int start )
{
	int i;

	for(i=0;i<5;i++){
		wi->sw[EnumGraphSkillShopSkillSellPanel1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSellName1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSellFp1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSellPrice1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSellGold1+i].Enabled = TRUE;

		if (SkillShopMode==2) wi->sw[EnumGraphSkillShopSkillSellGold1+i].func = MenuSwitchSkillShopSellPanel;
		if (SkillShopMode==3) wi->sw[EnumGraphSkillShopSkillSellGold1+i].func = MenuSwitchSkillShopSellPanel_CantSelect;
#ifdef PUK3_PETSKILLSHOPEX
		if (SkillShopMode==12) wi->sw[EnumGraphSkillShopSkillSellGold1+i].func = MenuSwitchSkillShopSellPanel;
		if (SkillShopMode==13) wi->sw[EnumGraphSkillShopSkillSellGold1+i].func = MenuSwitchSkillShopSellPanel_CantSelect;
#endif

		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillSellName1+i].Switch )->text[0] = '\0';
		SkillShopNum_SellFp[i][0] = '\0';
		SkillShopNum_SellPrice[i][0] = '\0';

		if (PetSkillShopSell[i].name[0]=='\0') continue;

		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillSellName1+i].Switch )->text, PetSkillShopSell[start+i].name );
		sprintf( SkillShopNum_SellFp[i], "%5d", PetSkillShopSell[start+i].fp ); //MLHIDE
		sprintf( SkillShopNum_SellPrice[i], "%10d", PetSkillShopSell[start+i].price ); //MLHIDE

		if( pc.gold < PetSkillShopSell[start+i].price ){
			( (NUMBER_SWITCH *)wi->sw[EnumGraphSkillShopSkillSellPrice1+i].Switch )->color = FONT_PAL_RED;
		}else{
			( (NUMBER_SWITCH *)wi->sw[EnumGraphSkillShopSkillSellPrice1+i].Switch )->color = FONT_PAL_WHITE;
		}
	}
}

void ResetSkillPanel( WINDOW_INFO *wi, int start )
{
	int i;

	for(i=0;i<monsterSkillShopSelPet[PetSelectNo].maxTech-start;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = TRUE;
		wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchSkillShopPosSelectPanel;


		( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text[0] = '\0';
		SkillShopNum_SkillFp[i][0] = '\0';
		SkillShopNum_SkillSlot[i][0] = '\0';

		if (!monsterSkillShopSelPos[start+i].useFlag) continue;

		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillShopSkillName1+i].Switch )->text, monsterSkillShopSelPos[start+i].name );
		sprintf( SkillShopNum_SkillSlot[i], "%3d", monsterSkillShopSelPos[start+i].fp ); //MLHIDE
	}
	for(;i<10;i++){
		wi->sw[EnumGraphSkillShopSkillPanel1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillName1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillSlot1+i].Enabled = FALSE;
		wi->sw[EnumGraphSkillShopSkillPanel1+i].func = MenuSwitchNone;
	}
#ifdef PUK3_PETSKILLSHOPEX
	for(i=0;i<5;i++){
		wi->sw[EnumGraphSkillShopSkillTribe1+i].Enabled = FALSE;
	}
#endif
}

//===============================================
//
BOOL MenuWindowSkillShopBf( int mouse )
{
	if (mouse==WIN_INIT){
		wI->sw[EnumSkillShopInfo].Enabled = FALSE;

		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillShopTitle].Switch )->text, serverRequestWinStr[0] );

		( (ACTION_SWITCH *)wI->sw[EnumGraphSkillShopChara].Switch )->ActionAdd->anim_chr_no = SkillShopNpcGra;
		( (ACTION_SWITCH *)wI->sw[EnumGraphSkillShopChara].Switch )->ActionAdd->anim_ang = 5;

		SkillShopDispStart1 = 0;
		SkillShopDispStart2 = 0;

		switch(SkillShopMode){
		case 0:	ChangePlayerBuyMode(wI);	break;
		case 1: ChangePlayerForgetMode(wI);	break;
		case 2: ChangePetSkillSkillSelectMode(wI);	break;
#ifdef PUK3_PETSKILLSHOPEX
		case 12: ChangePetSkillSkillSelectMode(wI);	break;
#endif
		}

		SkillShopInfoNo = -1;
		SkillShopBeforeNo = -1;
	}

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if( checkMoveMapGridPos( 2, 2 ) ){
		wI->flag |= WIN_INFO_DEL;
	}
	if (serverRequestWinWindowType!=-2){
		// ウィンドウ种类が违うならウィンドウを关闭
		if (SkillShopWindowType != serverRequestWinWindowType){
			wI->flag |= WIN_INFO_DEL;
		}
	}

	// 手持ち金表示
	sprintf( SkillShopNum_PCGold, "%10d", pc.gold );                     //MLHIDE

	if (SkillShopInfoNo>=0) SkillShopInfoNo = -1;

	return TRUE;
}

BOOL MenuWindowSkillShopAf( int mouse )
{
	int x = wI->wx + 297;
	int y = wI->wy + 52;
	int i, j;

	switch(SkillShopMode){
	case 0:case 1:
		if (SkillShopScrollMax1-10<=0) wI->sw[EnumGraphSkillShopScroll].Enabled = FALSE;
		// つまみを移动中なら、表示开始位置を变更
		if ( ( (BUTTON_SWITCH *)wI->sw[EnumBtSkillShopScroll].Switch )->status&1 ){
			SkillShopDispStart1 = ScrollVPointToNum( &wI->sw[EnumBtSkillShopScroll], SkillShopScrollMax1-10 );
			if (SkillShopMode==0){
				ResetBuyPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}else{
				ResetForgetPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}
		}
		break;
	case 2:case 3:case 4:
#ifdef PUK3_PETSKILLSHOPEX
	case 12:case 13:
#endif
		wI->sw[EnumGraphSkillShopScroll].Enabled = TRUE;
		if (SkillShopScrollMax1-10<=0) wI->sw[EnumGraphSkillShopScroll].Enabled = FALSE;
		// つまみを移动中なら、表示开始位置を变更
		if ( ( (BUTTON_SWITCH *)wI->sw[EnumBtSkillShopScroll].Switch )->status&1 ){
			SkillShopDispStart1 = ScrollVPointToNum( &wI->sw[EnumBtSkillShopScroll], SkillShopScrollMax1-10 );
			ResetSkillPanel( wI, SkillShopDispStart1 );
		}
		wI->sw[EnumGraphSkillShopScroll2].Enabled = TRUE;
		if (SkillShopScrollMax2-5<=0) wI->sw[EnumGraphSkillShopScroll2].Enabled = FALSE;
		// つまみを移动中なら、表示开始位置を变更
		if ( ( (BUTTON_SWITCH *)wI->sw[EnumBtSkillShopScroll2].Switch )->status&1 ){
			SkillShopDispStart2 = ScrollVPointToNum( &wI->sw[EnumBtSkillShopScroll2], SkillShopScrollMax2-5 );
			ResetSellPanel( wI, SkillShopDispStart2 );
		}
		break;
	}

	// アイテム信息
	if (SkillShopInfoNo<0){
		j = -SkillShopInfoNo-1;

		// 信息表示
		itemInfoColor = FONT_PAL_WHITE;
		for( i = 0; i < 4; i++ ){
			if( strlen( serverRequestWinStr[(j<<2)+1+i] ) > 0 ){
				stockFontBuffer( x, y+i*10, FONT_PRIO_WIN, FONT_KIND_SIZE_11, FONT_PAL_WHITE,
					serverRequestWinStr[(j<<2)+1+i], 0, 0 );
			}
		}
	}else{
		// スキル说明表示
		itemInfoColor = FONT_PAL_WHITE;
		for( i = 0; i < 2; i++ ){
			stockFontBuffer( x, y+i*10, FONT_PRIO_WIN, FONT_KIND_SIZE_11, FONT_PAL_WHITE,
				PetSkillShopSell[SkillShopInfoNo].memo[i], 0, 0 );
		}
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuSwitchSkillShopClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopOK( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SKILLMASTER_OK );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		switch(SkillShopMode){
		case 0:
			char msg[16];
			makeSendString( "1", msg, sizeof( msg )-1 );                       //MLHIDE
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );

			break;
		}
		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_BigOKButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopCancel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER){
		switch(SkillShopMode){
		case 0:	strcpy( OneLineInfoStr, MWONELINE_SKILLMASTER_LRNCANCEL );	break;
		case 1:	strcpy( OneLineInfoStr, MWONELINE_SKILLMASTER_FGTCANCEL );	break;
		case 2:	strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_L_CANC );	break;
		case 3:	strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_CANCEL );	break;
		case 4:	strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_CANCEL );	break;
#ifdef PUK3_PETSKILLSHOPEX
		case 12:	strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_L_CANC );	break;
		case 13:strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_CANCEL );	break;
#endif
		}
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		char msg[16], msg2[16];

		switch(SkillShopMode){
		case 0:
			makeSendString( "2", msg, sizeof( msg )-1 );                       //MLHIDE
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			wI->flag |= WIN_INFO_DEL;
			break;
		case 1:
			sprintf( msg, "%d", 99 );                                          //MLHIDE
			makeSendString( msg, msg2, sizeof( msg2 )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );

			wI->flag |= WIN_INFO_DEL;
			break;
		case 2:
#ifdef PUK3_PETSKILLSHOPEX
		case 12:
#endif
			wI->flag |= WIN_INFO_DEL;
			break;
		case 3:
#ifdef PUK3_PETSKILLSHOPEX
		case 13:
#endif
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_CANCEL, "" );
			serverRequestWinWindowType = -2;
			break;
		case 4:
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_CANCEL, "" );
			serverRequestWinWindowType = -2;
			break;
		}

		SkillShopBackFlag = 1;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag = TRUE;
	}

	switch(SkillShopMode){
	case 0:case 1:case 2:
#ifdef PUK3_PETSKILLSHOPEX
	case 12:
#endif
		Graph->graNo = GID_BigCancelButtonOn;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigCancelButtonOver;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigCancelButtonOff;
		break;
	case 3:case 4:
#ifdef PUK3_PETSKILLSHOPEX
	case 13:
#endif
		Graph->graNo = GID_BigBackButtonOn;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigBackButtonOver;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigBackButtonOff;
		break;
	}

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		switch(SkillShopMode){
		case 0:case 1:
			if (SkillShopDispStart1>0){
				SkillShopDispStart1--;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
			// つまみを移动
			NumToScrollVMove( &wI->sw[no-1], SkillShopScrollMax1-10, SkillShopDispStart1 );
			if ( SkillShopMode == 0 ){
				ResetBuyPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}else{
				ResetForgetPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}
			break;
		case 2:case 3:case 4:
#ifdef PUK3_PETSKILLSHOPEX
		case 12:case 13:
#endif
			if (no<=EnumGraphSkillShopScroll2){
				if (SkillShopDispStart1>0){
					SkillShopDispStart1--;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-1], SkillShopScrollMax1-10, SkillShopDispStart1 );
				ResetSkillPanel( wI, SkillShopDispStart1 );
			}else{
				if (SkillShopDispStart2>0){
					SkillShopDispStart2--;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-1], SkillShopScrollMax2-5, SkillShopDispStart2 );
				ResetSellPanel( wI, SkillShopDispStart2 );
			}
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		switch(SkillShopMode){
		case 0:case 1:
			if (SkillShopDispStart1<SkillShopScrollMax1-10){
				SkillShopDispStart1++;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
			// つまみを移动
			NumToScrollVMove( &wI->sw[no-2], SkillShopScrollMax1-10, SkillShopDispStart1 );
			if ( SkillShopMode == 0 ){
				ResetBuyPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}else{
				ResetForgetPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}
			break;
		case 2:case 3:case 4:
			if (no<=EnumGraphSkillShopScroll2){
#ifdef PUK3_PETSKILLSHOPEX
		case 12:case 13:
#endif
				if (SkillShopDispStart1<SkillShopScrollMax1-10){
					SkillShopDispStart1++;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-1], SkillShopScrollMax1-10, SkillShopDispStart1 );
				ResetSkillPanel( wI, SkillShopDispStart1 );
			}else{
				if (SkillShopDispStart2<SkillShopScrollMax2-5){
					SkillShopDispStart2++;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-2], SkillShopScrollMax2-5, SkillShopDispStart2 );
				ResetSellPanel( wI, SkillShopDispStart2 );
			}
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		switch(SkillShopMode){
		case 0:case 1:
			if (SkillShopDispStart1>0){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			SkillShopDispStart1-=10;
			if (SkillShopDispStart1<0) SkillShopDispStart1 = 0;
			// つまみを移动
			NumToScrollVMove( &wI->sw[no-3], SkillShopScrollMax1-10, SkillShopDispStart1 );
			if ( SkillShopMode == 0 ){
				ResetBuyPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}else{
				ResetForgetPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}
			break;
		case 2:case 3:case 4:
#ifdef PUK3_PETSKILLSHOPEX
		case 12:case 13:
#endif
			if (no<=EnumGraphSkillShopScroll2){
				if (SkillShopDispStart1>0){
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				SkillShopDispStart1-=10;
				if (SkillShopDispStart1<0) SkillShopDispStart1 = 0;
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-3], SkillShopScrollMax1-10, SkillShopDispStart1 );
				ResetSkillPanel( wI, SkillShopDispStart1 );
			}else{
				if (SkillShopDispStart2>0){
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				SkillShopDispStart2-=5;
				if (SkillShopDispStart2<0) SkillShopDispStart2 = 0;
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-3], SkillShopScrollMax2-5, SkillShopDispStart2 );
				ResetSellPanel( wI, SkillShopDispStart2 );
			}
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		switch(SkillShopMode){
		case 0:case 1:
			if ( (SkillShopScrollMax1-10>0) && (SkillShopDispStart1<SkillShopScrollMax1-10) ){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}

			SkillShopDispStart1+=10;
			if (SkillShopDispStart1>SkillShopScrollMax1-10) SkillShopDispStart1 = SkillShopScrollMax1-10;
			if (SkillShopDispStart1<0) SkillShopDispStart1 = 0;
			// つまみを移动
			NumToScrollVMove( &wI->sw[no-4], SkillShopScrollMax1-10, SkillShopDispStart1 );
			if ( SkillShopMode == 0 ){
				ResetBuyPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}else{
				ResetForgetPCSkillPanel( wI, SkillShopDispStart1, SkillShopScrollMax1 );
			}
			break;
		case 2:case 3:case 4:
#ifdef PUK3_PETSKILLSHOPEX
		case 12:case 13:
#endif
			if (no<=EnumGraphSkillShopScroll2){
				if ( (SkillShopScrollMax1-10>0) && (SkillShopDispStart1<SkillShopScrollMax1-10) ){
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}

				SkillShopDispStart1+=10;
				if (SkillShopDispStart1>SkillShopScrollMax1-10) SkillShopDispStart1 = SkillShopScrollMax1-10;
				if (SkillShopDispStart1<0) SkillShopDispStart1 = 0;
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-4], SkillShopScrollMax1-10, SkillShopDispStart1 );
				ResetSkillPanel( wI, SkillShopDispStart1 );
			}else{
				if ( (SkillShopScrollMax2-5>0) && (SkillShopDispStart2<SkillShopScrollMax2-5) ){
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}

				SkillShopDispStart2+=5;
				if (SkillShopDispStart2>SkillShopScrollMax2-5) SkillShopDispStart2 = SkillShopScrollMax2-5;
				if (SkillShopDispStart2<0) SkillShopDispStart2 = 0;
				// つまみを移动
				NumToScrollVMove( &wI->sw[no-4], SkillShopScrollMax2-5, SkillShopDispStart2 );
				ResetSellPanel( wI, SkillShopDispStart2 );
			}
			break;
		}

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		switch(SkillShopMode){
		case 0:case 1:
			// スクロールバー縦ホイール移动
			SkillShopDispStart1 = WheelToMove( &wI->sw[no-5],
				 SkillShopDispStart1, SkillShopScrollMax1-10, mouse.wheel );
			break;
		case 2:case 3:case 4:
#ifdef PUK3_PETSKILLSHOPEX
		case 12:case 13:
#endif
			if (no<=EnumGraphSkillShopScroll2){
				// スクロールバー縦ホイール移动
				SkillShopDispStart1 = WheelToMove( &wI->sw[no-5],
					 SkillShopDispStart1, SkillShopScrollMax1-10, mouse.wheel );
				ResetSkillPanel( wI, SkillShopDispStart1 );
			}else{
				// スクロールバー縦ホイール移动
				SkillShopDispStart2 = WheelToMove( &wI->sw[no-5],
					 SkillShopDispStart2, SkillShopScrollMax2-5, mouse.wheel );
				ResetSellPanel( wI, SkillShopDispStart2 );
			}
			break;
		}
	}

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopPlayerFogetPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = SkillShopDispStart1 + no-EnumGraphSkillShopSkillPanel1;

	if( sortSp[Num]<0 || skillLv[ sortSp[Num] ]<0 ){
		Graph->graNo = GID_SkillSlotPanelOff;
		return ReturnFlag;
	}

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_SKILLMASTER_PANEL );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		char msg[16], msg2[16];
		sprintf( msg, "%d", sortSp[Num]+1 );                                //MLHIDE

		makeSendString( msg, msg2, sizeof( msg2 )-1 );
		nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );

		wI->flag |= WIN_INFO_DEL;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SkillSlotPanelOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SkillSlotPanelOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SkillSlotPanelOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopPetSelectPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = SSpetPos[SkillShopDispStart1 + no-EnumGraphSkillShopSkillPanel1];

#ifdef PUK3_PETSKILLSHOPEX
	if ( Num == 0 ) SkillShopInfoNo = -1;

	if( !monsterSkillShopSelPet[Num].useFlag || !monsterSkillShopSelPet[Num].canSetFlg ){
		Graph->graNo = GID_TitlePanelOff;
		return ReturnFlag;
	}
	else
	// 卖种族が限定されていて、その种族でないなら
	if ( SkillShopTribe >= 0 &&
		 monsterSkillShopSelPet[Num].tribe != SkillShopTribe ){
		if(flag & MENU_MOUSE_OVER) SkillShopInfoNo = -2;
		Graph->graNo = GID_TitlePanelOff;
		return ReturnFlag;
	}else
	// ライド出来ないモンスターの场合
	// 扩张版でない场合 rideflg は必ず TRUE が入ってくる
	if ( !monsterSkillShopSelPet[Num].rideflg ){
		if(flag & MENU_MOUSE_OVER) SkillShopInfoNo = -3;
	}

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_PET );
#else
	if( !monsterSkillShopSelPet[Num].useFlag || !monsterSkillShopSelPet[Num].canSetFlg ){
		Graph->graNo = GID_TitlePanelOff;
		return ReturnFlag;
	}

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_PET );
#endif

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		char msg[16], msg2[16];

		PetSelectNo = Num;

		sprintf( msg, "%d", PetSelectNo );                                  //MLHIDE
		makeSendString( msg, msg2, sizeof( msg2 )-1 );
		nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );
		serverRequestWinWindowType = -2;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_TitlePanelOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_TitlePanelOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_TitlePanelOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopPosSelectPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = SkillShopDispStart1+no-EnumGraphSkillShopSkillPanel1;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_SLOT );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		PosSelectNo = Num;

		// 确认ウィンドウ表示
		wI->sw[EnumSkillShopInfo].Enabled = TRUE;
		// ウィンドウの移动不可能に
		wI->flag |= MENU_ATTR_NOMOVE;

		// 决定音b（ボタンクリック时）
		play_se( SE_NO_OK2, 320, 240 );
/***
		char msg[16];
		sprintf( msg, "%d", Num );
		nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
***/

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_SkillRecipePanelROn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_SkillRecipePanelROver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_SkillRecipePanelROff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopSellPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = SkillShopDispStart2+no-EnumGraphSkillShopSkillSellGold1;

	if( PetSkillShopSell[Num].name[0] == '\0' ) return ReturnFlag;

	if( flag & MENU_MOUSE_OVER ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
		if ( !( SkillShopInfoNo < -1 && SkillShopBeforeNo == Num ) ) SkillShopInfoNo = Num;
		SkillShopBeforeNo = Num;
		// 一行インフォ表示
		strcpy( OneLineInfoStr, MWONELINE_PETSKILLMASTER_SKILL );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// 所持金额がないので警告
		if( pc.gold < PetSkillShopSell[Num].price ){
			SkillShopInfoNo = -2;
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
		// 使い魔がいないので警告
		else{
			int i, j;

			j=0;
			for(i=0;i<5;i++){
				if (pet[i].useFlag) j++;
			}
			if( j == 0 ){
				SkillShopInfoNo = -3;
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
			// スキルを选择
			else{
				char msg[16], msg2[16];

				SkillSelectNo = Num;
				sprintf( msg, "%d", SkillSelectNo );                              //MLHIDE
				makeSendString( msg, msg2, sizeof( msg2 )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );
				serverRequestWinWindowType = -2;

				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}
		}

		ReturnFlag = TRUE;
	}

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopSellPanel_CantSelect( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num = SkillShopDispStart2+no-EnumGraphSkillShopSkillSellGold1;

	if( PetSkillShopSell[Num].name[0] == '\0' ) return ReturnFlag;

	if( SkillSelectNo==Num ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx+2, wI->wy+wI->sw[no].ofy+2,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx-2, wI->wy+wI->sw[no].ofy+wI->sw[no].sy-2,
			DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
//		SkillShopInfoNo = Num;
	}

	return ReturnFlag;
}

BOOL MenuSwitchSkillShopInfo( int no, unsigned int flag )
{
	const short x = 160, y = 110, w = 320, h = 240;
	const short Yx = 80, Yy = 200, Yw = 66, Yh = 17;
	const short Nx = 174, Ny = 200, Nw = 66, Nh = 17;
	int GraNo1, GraNo2;
	struct BLT_MEMBER bm={0};
	char Yflg = 0, Nflg = 0;
	char str[256];
	int len;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// Ｙｅｓ、Ｎｏボタンのあたり判定
	if ( MakeHitBox( x+Yx, y+Yy, x+Yx+Yw, y+Yy+Yh, -1 ) ) Yflg = 1;
	if ( MakeHitBox( x+Nx, y+Ny, x+Nx+Nw, y+Ny+Nh, -1 ) ) Nflg = 1;

	// Ｙｅｓ、Ｎｏボタンの处理
	GraNo1 = GID_BigYesButtonOn;
	if (Yflg){
		GraNo1 = GID_BigYesButtonOver;
		// 一行インフォ
		strcpy( OneLineInfoStr, ItemShopInfoDispOneLineYes );
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			char msg[16];

			wI->sw[no].Enabled = FALSE;

			sprintf( msg, "%d", PosSelectNo );                                 //MLHIDE
			nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			GraNo1 = GID_BigYesButtonOff;

			wI->flag |= WIN_INFO_DEL;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
	}
	GraNo2 = GID_BigNoButtonOn;
	if (Nflg){
		GraNo2 = GID_BigNoButtonOver;
		// 一行インフォ
		strcpy( OneLineInfoStr, ItemShopInfoDispOneLineNo );
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			wI->sw[no].Enabled = FALSE;

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			GraNo2 = GID_BigNoButtonOff;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
	}

	// Ｙｅｓ、Ｎｏボタンの描画
	StockDispBuffer_PUK2( x+Yx, y+Yy, DISP_PRIO_YES_NO_WND, GraNo1, 0, 1, &bm );
	StockDispBuffer_PUK2( x+Nx, y+Ny, DISP_PRIO_YES_NO_WND, GraNo2, 0, 1, &bm );

	sprintf( str, "『%s』", PetSkillShopSell[SkillSelectNo].name );        //MLHIDE
	len = GetStrWidth( str, FONT_KIND_MIDDLE );
	StockFontBuffer( x+160-len/2, y+20, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );

	sprintf( str, ML_STRING(400, "『%s』的"), monsterSkillShopSelPet[PetSelectNo].name );
	len = GetStrWidth( "『1234567890123456』的", FONT_KIND_MIDDLE );        //MLHIDE
	StockFontBuffer( x+160-len/2, y+44, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );

	if( monsterSkillShopSelPos[PosSelectNo].useFlag ){
		sprintf( str, "『%s』的位置に", monsterSkillShopSelPos[PosSelectNo].name ); //MLHIDE
	}else{
		strcpy( str, ML_STRING(402, "『空栏位』的位置") );
	}
	len = GetStrWidth( str, FONT_KIND_MIDDLE );
	StockFontBuffer( x+160-len/2, y+68, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );

	len = GetStrWidth( ML_STRING(403, "学习。"), FONT_KIND_MIDDLE );
	StockFontBuffer( x+160-len/2, y+88, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(403, "学习。"), 0, 0 );

	sprintf( str, ML_STRING(404, "所持金币　 %7dG"), pc.gold );
	StockFontBuffer( x+85, y+120, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );

	sprintf( str, ML_STRING(405, "购入金额 %7dG"), PetSkillShopSell[SkillSelectNo].price );
	StockFontBuffer( x+85, y+140, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );

	StockFontBuffer( x+92, y+172, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(190, "可以吗？"), 0, 0 );

	MenuWindowCommonDraw( GID_CommonWindow, x, y, w, h, DISP_PRIO_YES_NO_WND, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}
