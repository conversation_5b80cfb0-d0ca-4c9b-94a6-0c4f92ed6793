﻿/***************************************
			ride_character.cpp
***************************************/

#ifdef PUK3

#ifdef PUK3_RIDE

// キャラのアニメーションを设定する
void setRiderAnimeNo( ACTION *pAct, int anim_no, BOOL InitAct )
{
	CHAREXTRA *ext = (CHAREXTRA *)pAct->pYobi;

	if ( ext->ptRide ){
		// ペットのアクションを设定する
		ext->ptRide->anim_no = anim_no;

		// 座りキャラなら
		if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
			// ペットのアクションを元にキャラのアクションを设定する
			switch(ext->ptRide->anim_no){
			case ANIM_ATTACK:
				pAct->anim_no = ANIM_ATTACK;	break;
			case ANIM_B_WALK_START:case ANIM_B_WALK:case ANIM_B_WALK_END:
				pAct->anim_no = ANIM_B_WALK;	break;
			case ANIM_DEAD:
				pAct->anim_no = ANIM_DAMAGE;	break;
			case ANIM_DAMAGE:
				pAct->anim_no = ANIM_DAMAGE;	break;
			case ANIM_GUARD:
				pAct->anim_no = ANIM_GUARD;		break;
			case ANIM_MAGIC:
				pAct->anim_no = ANIM_ATTACK;	break;
			case ANIM_STAND:
				pAct->anim_no = ANIM_STAND;		break;
			case ANIM_WALK:
				pAct->anim_no = ANIM_WALK;		break;
			default:
				// 上记いずれにも当てはまらないなら立ちにする
				pAct->anim_no = ANIM_STAND;
				ext->ptRide->anim_no = ANIM_STAND;
			}
		}
		// 普通のキャラなら
		else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
		else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
		else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
		else if ( pAct->anim_chr_no == SPR_shadow_pcm || pAct->anim_chr_no == SPR_shadow_pcf ) pAct->anim_no = ANIM_SIT;
		// モンスターなら
		else{
			pAct->anim_no = ANIM_STAND;
		}
	}else{
		// キャラのアニメーションを设定する
		pAct->anim_no = anim_no;
	}

	// アクションを初期化するなら
	if (InitAct){
		pAct->anim_no_bak = -1;
		if ( ext->ptRide ) ext->ptRide->anim_no_bak = -1;
	}
}

#endif

#endif