﻿/***************************************
			directdraw3D.cpp
***************************************/

#ifdef PUK2

	unsigned char Pchgdata[][3]={
	//		青		緑		赤
		{	0x80,	0x80,	0x80	},	// 昼	0
		{	0x5a,	0x78,	0x96	},	// 夕方	1
		{	0x80,	0x56,	0x56	},	// 夜	2
		{	0x80,	0x78,	0x78	},	// 朝	3

		{	0x80,	0xa0,	0x80	},	// 緑	4
		{	0xa0,	0xa0,	0x80	},	// 水色	5
		{	0xa0,	0x80,	0x80	},	// 青	6
		{	0xa0,	0x80,	0xa0	},	// 紫	7
		{	0x80,	0x80,	0xa0	},	// 赤	8
		{	0x60,	0x90,	0xa0	},	// 橙	9
		{	0x80,	0xa0,	0xa0	},	// 黄	10
		{	0x80,	0xa0,	0x90	},	// 黄緑	11
		{	0x70,	0x70,	0x70	},	// 黒	12
		{	0x60,	0x60,	0x60	},	// 暗闇	13
		{	0x80,	0xc0,	0xff	},	// セピア	14
		{	0xff,	0xff,	0xff	},	// モノクロ	15

		// この下が无いと、オープニングでポンスビックロゴのフェードイン??フェードアウトが出来なくなるため
		{	0x80,	0xc0,	0xff	},	// オープニング　セピア	16
		{	0x80,	0x80,	0x80	},	// オープニング　火	17
		{	0x80,	0x80,	0x80	},	// オープニング　ロゴだけ	18
		{	0x00,	0x00,	0x00	},	// 黒	19
		{	0xff,	0xff,	0xff	},	// 白	20
		{	0x80,	0x80,	0x80	},	// エニックスメーカーロゴ	21
		{	0x80,	0x80,	0x80	},	// dwango メーカーロゴ	22
		{	0x80,	0x80,	0x80	},	// ツェナ メーカーロゴ	23
		{	0x80,	0x80,	0x80	},	// ＥＸオープニング	24
		{	0x80,	0x80,	0x80	},	// ＥＸオープニング火	25
		{	0x80,	0x80,	0x80	},	// ＥＸ/Ｖ２ Now Loading	26
		{	0x80,	0x80,	0x80	},	// Ｖ２ サーバー选择	27
		{	0x80,	0x80,	0x80	},	// ＥＸ タイトルロゴ	28
		{	0x80,	0x80,	0x80	},	// Ｖ２ タイトルロゴ	29
		{	0x80,	0x80,	0x80	},	// Ｖ２ タイトルロゴ	30
		{	0x80,	0x80,	0x80	},	// スクウェアエニックスロゴ	31

		{	0x80,	0xa0,	0xa0	},	// 黄	32
		{	0xa0,	0x80,	0x80	},	// 青	33
		{	0x80,	0x80,	0xa0	},	// 赤	34
		{	0x80,	0xa0,	0x80	},	// 緑	35

		{	0x80,	0xc0,	0xff	},	// セピア	36
		{	0xff,	0xff,	0xff	},	// モノクロ	37

		{	0x80,	0x80,	0x80	},	// スクウェアエニックスロゴ	38
	};
	unsigned char Palchg_m_data[3]={ 0x80, 0x80, 0x80 };
	unsigned char Palchg_a_data[3]={ 0x80, 0x80, 0x80 };
	unsigned char *Palchg=Palchg_a_data;

	unsigned char PalchgTblR[256];	// パレットチェンジのときにあらかじめチェンジ后の值を作っておく
	unsigned char PalchgTblG[256];	// パレットチェンジのときにあらかじめチェンジ后の值を作っておく
	unsigned char PalchgTblB[256];	// パレットチェンジのときにあらかじめチェンジ后の值を作っておく
	unsigned char *PalchgTbl[3]={ PalchgTblB, PalchgTblG, PalchgTblR };	// パレットチェンジのときにあらかじめチェンジ后の值を作っておく

	// 赤
	#define PPLR 2
	// 緑
	#define PPLG 1
	// 青
	#define PPLB 0

	char device3D;
	char last3D_mode;

	int useTextureStage;

	// オートマップ作业用サーフェスのポインタ
	LPDIRECTDRAWSURFACE7 lpAutoMapSurface;

#ifdef PUK2_FPS
	// FPS制御用
	extern double SystemDrawTime;
#endif
#ifdef PUK3_WINDOWMODE_BLACKOUT
	BOOL PrimaryLostFlag = FALSE;
#endif

//----------------------------------------
// 现在の描画モード取得
char getNow3D_mode()
{
	return last3D_mode;
}

//----------------------------------------
// 现在描画モードの３Ｄ使用可能状况を取得
BOOL getUsable3D()
{
	return( lpDraw->lpD3 != NULL );
}

#ifdef PUK2_FPS

void resetFrameRate()
{
	switch(device3D){
	case 0:		SystemDrawTime = (1.0/5)*1000;	break;
	default:	SystemDrawTime = (1.0/100)*1000;	break;
	}
}

#endif

/* DirectDraw??Direct3D 初期化 **********************************************************/
char  InitDirectDraw( void )
{
	DDSCAPS2 ddscaps={0};	// ハードウェア能力构造体
	D3DDEVICEDESC7 d4={0};
	D3DVIEWPORT7 vw={0};
	D3DDEVICEDESC7 d3dd={0};

#ifdef PUK3_WINDOWMODE_BLACKOUT
	// プライマリサーフェースロストフラグ初期化
	PrimaryLostFlag = FALSE;
#endif
	// DirectDraw の初期化 ----------------

	#ifdef SUPPORT_16BIT
		// 画面解像度（BPP）を调べる
		HDC hDcDest = GetDC( hWnd );
		displayBpp = GetDeviceCaps( hDcDest, BITSPIXEL ); // ビットの深さを取得
		ReleaseDC( hWnd, hDcDest );
	#endif

#ifndef VERSION_TW
		if ( CmdLineFlg&CMDLINE_FULLDISPMODE ) {
		// １６Ｂｐｐ未满は切り舍て
		if (displayBpp<16) {
			//todo: 32位色下阴影会透明，和d3d中很多函数有关，改动太多，目前只改为提示，以后完全支持32位色的效果后将去掉提示。
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, INFOMSG_3, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "本游戏只能在16位色下运行。", "确认", MB_OK | MB_ICONSTOP );   //MLHIDE
#endif
			return 1;
		}
	}else{
		// １６Ｂｐｐ以外は切り舍て
		if( WindowMode && displayBpp!=16 ) {
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, INFOMSG_4, "确认", MB_OK | MB_ICONSTOP );
			if(MessageBox(hWnd, "本游戏在16位色下运行才能获得最佳显示效果，是否继续？","确认",MB_OKCANCEL)==IDOK) //MLHIDE
			{
				
			}
			else 
			{
				MessageBox( hWnd, "请切换监视器显示为16位色后重新运行。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
				PostMessage( hWnd, WM_CLOSE, 0, 0L );
				return 1;
			}
#else
			//MessageBox( hWnd, "本游戏只能在16位色下运行。", "确认", MB_OK | MB_ICONSTOP );
#endif
		}
	}
#endif

	// DirectDraw管理构造体实体确保
	lpDraw = ( DIRECT_DRAW *)GlobalAllocPtr( GPTR, ( DWORD )sizeof( DIRECT_DRAW ) );
	if( lpDraw == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_3, "确认", MB_OK | MB_ICONSTOP );            //MLHIDE
#else
		MessageBox( hWnd, "GlobalAllocPtr Error ( DIRECT_DRAW )", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
		return 1;
	}
#ifdef PUK2_MEMCHECK
	memlistset( lpDraw, MEMLISTTYPE_STRUCT_DIRECT_DRAW );
#endif
	lpDraw->lpD3=NULL;
	lpDraw->lpD3DEVICE=NULL;
	
	// ウィンドウサイズ
	lpDraw->xSize = DEF_APPSIZEX;
	lpDraw->ySize = DEF_APPSIZEY;
	
	// サーフェスサイズ
	SurfaceSizeX = SURFACE_WIDTH;
	SurfaceSizeY = SURFACE_HEIGHT;
	
	// DirectDraw7の作成
	// DirectDrawの初期化处理 失败したら NULL を返す
	if( DirectDrawCreateEx( NULL, (void **)&lpDraw->lpDD2, IID_IDirectDraw7, NULL ) != DD_OK ){
		if( DirectDrawCreateEx( (GUID *)DDCREATE_EMULATIONONLY, (void **)&lpDraw->lpDD2, IID_IDirectDraw7, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_4, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
			MessageBox( hWnd, "DirectDrawCreateEx Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
			return 1;
		}
	}

////// ウィンドウモードの时 //////////////////////////////////////////////////
	if( WindowMode ){
		// このアプリケーションの权限等级を设定(ウィンドウモード）
		if( lpDraw->lpDD2->SetCooperativeLevel( hWnd ,DDSCL_NORMAL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_5, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
			MessageBox( hWnd, "SetCooperativeLevel Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
			return 1;
		}
		
		// ディスプレイ表示モードを变更しない
		
		// 表(プライマリ)バッファの初期化
		ZeroMemory( &lpDraw->ddsd, sizeof( lpDraw->ddsd ) ); 
		lpDraw->ddsd.dwSize = sizeof( lpDraw->ddsd );

		// ddsCaps构造体を有效化 & dwBackBufferCount变数を有效
		lpDraw->ddsd.dwFlags = DDSD_CAPS;
		
		// プライマリサーフェス(表画面)として设定 ,このサーフェスがFlipを使用できる。,复数のサーフェスの记述を有效 
		lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE | DDSCAPS_3DDEVICE;
		
		// プライマリーサーフェスの作成
		if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpDraw->lpFRONTBUFFER, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_6, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
			MessageBox( hWnd, "主Surface生成失败。", "确认", MB_OK | MB_ICONSTOP );    //MLHIDE
#endif
			return 1;
		}
		
		// クリッ布の作成
		if( lpDraw->lpDD2->CreateClipper( 0,&lpDraw->lpCLIPPER, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_7, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
			MessageBox( hWnd, "Clipper生成失败。", "确认", MB_OK | MB_ICONSTOP );     //MLHIDE
#endif
			return 1;
		}
		// クリッ布にウィンドウのハンドルをセットする
		lpDraw->lpCLIPPER->SetHWnd( 0, hWnd );
		// プライマリサーフェスにクリッ布を关连付ける
		lpDraw->lpFRONTBUFFER->SetClipper( lpDraw->lpCLIPPER );
		
		// 里(バック)バッファの初期化
		ZeroMemory( &lpDraw->ddsd, sizeof( lpDraw->ddsd ) ); 
		lpDraw->ddsd.dwSize = sizeof( lpDraw->ddsd );
        lpDraw->ddsd.dwFlags           = DDSD_CAPS | DDSD_WIDTH | DDSD_HEIGHT;
   		lpDraw->ddsd.ddsCaps.dwCaps    = DDSCAPS_OFFSCREENPLAIN | DDSCAPS_3DDEVICE;

       	lpDraw->ddsd.dwWidth           = lpDraw->xSize;
       	lpDraw->ddsd.dwHeight          = lpDraw->ySize;
       	if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpDraw->lpBACKBUFFER, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_8, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
			MessageBox( hWnd, "BackBuffer生成失败。", "确认", MB_OK | MB_ICONSTOP );  //MLHIDE
#endif
			return 1;
		}
////// フルスクリーンモードの时 //////////////////////////////////////////////
	}else{
		// このアプリケーションの权限等级を设定(排他等级 & フルスクリーン画面）
		if( lpDraw->lpDD2->SetCooperativeLevel( hWnd, DDSCL_EXCLUSIVE | DDSCL_FULLSCREEN | DDSCL_ALLOWMODEX ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_9, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
			MessageBox( hWnd, "SetCooperativeLevel Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
			return 1;
		}
		// ディスプレイ表示モードを变更
		/* 修复全屏启动
		displayBpp = 16;
		lpDraw->lpDD2->SetDisplayMode( lpDraw->xSize, lpDraw->ySize, 16 ,0 ,0 );
		*/
		lpDraw->lpDD2->SetDisplayMode(lpDraw->xSize, lpDraw->ySize, displayBpp, 0, 0);
		
		// 表(プライマリ)バッファの初期化
		// ddsd 构造体の内容をゼロクリアする
		ZeroMemory( &lpDraw->ddsd, sizeof( lpDraw->ddsd ) );
		
		// ddsd 构造体に作成するサーフェスの情报を格纳する
		// ddsd 构造体のサイズの指定（ バージョンアップに对应させるため ）
		lpDraw->ddsd.dwSize = sizeof( lpDraw->ddsd );
		
		// 格纳するデータの种类の设定
		// ddsCaps构造体を有效化 & dwBackBufferCount变数を有效にする
		lpDraw->ddsd.dwFlags = DDSD_CAPS | DDSD_BACKBUFFERCOUNT;
		
		// プライマリサーフェス(表画面)として设定 ,このサーフェスがFlipを使用できる。,复数のサーフェスの记述を有效
		lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE | DDSCAPS_FLIP | DDSCAPS_COMPLEX | DDSCAPS_3DDEVICE;

		// バックバッファ数を１つセット
		lpDraw->ddsd.dwBackBufferCount = 1;
		
		// プライマリーサーフェスの实作成处理
		if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpDraw->lpFRONTBUFFER, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_10, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "主Surface生成失败。", "确认", MB_OK | MB_ICONSTOP );    //MLHIDE
#endif
			return 1;
		}
		
		// 里バッファの初期化
		ddscaps.dwCaps = DDSCAPS_BACKBUFFER;
		// 里バッファをプライマリサーフェスに接続
		if (lpDraw->lpFRONTBUFFER->GetAttachedSurface( &ddscaps, &lpDraw->lpBACKBUFFER )!=DD_OK){
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_11, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "BackBuffer生成失白。", "确认", MB_OK | MB_ICONSTOP );  //MLHIDE
#endif
			return 1;
		}
	}

	#ifdef SUPPORT_16BIT
		
		// １６ビットカラーのＲＧＢビット情报を取得する
		if( displayBpp != 8 )
		{
			// サーフェスポインタがNULLの时返回
			if( lpDraw->lpBACKBUFFER == NULL ) return 1;

			// 构造体の初期化
			ZeroMemory( &lpDraw->ddsd.ddpfPixelFormat, sizeof( DDPIXELFORMAT ) );
			lpDraw->ddsd.ddpfPixelFormat.dwSize = sizeof( DDPIXELFORMAT );
			//ddPixelFormat.dwFlags = DDPF_RGB;

			if( lpDraw->lpBACKBUFFER->GetPixelFormat( &lpDraw->ddsd.ddpfPixelFormat ) != DD_OK ){
				return 1;
			}

			rBitMask = lpDraw->ddsd.ddpfPixelFormat.dwRBitMask;
			gBitMask = lpDraw->ddsd.ddpfPixelFormat.dwGBitMask;
			bBitMask = lpDraw->ddsd.ddpfPixelFormat.dwBBitMask;

			rBitRShift = 8 - getBitCount( rBitMask );	// Ｒビットの右シフト值
			gBitRShift = 8 - getBitCount( gBitMask );	// Ｇビットの右シフト值
			bBitRShift = 8 - getBitCount( bBitMask );	// Ｂビットの右シフト值

			rBitLShift = getBitCount( bBitMask )+getBitCount( gBitMask );	// Ｒビットの右シフト值
			gBitLShift = getBitCount( bBitMask );							// Ｇビットの右シフト值
			bBitLShift = 0;													// Ｂビットの右シフト值
		}

		// １６ビット用パレットテーブル作成
		if( displayBpp == 16 ){
			int i;	// ループカウンタ

			lpDraw->PalTbl=(unsigned short (*)[3])GlobalAllocPtr( GPTR, (256)*sizeof( unsigned short [3] ) );
			if(lpDraw->PalTbl==NULL){
#ifdef PUK3_ERRORMESSAGE_NUM
				MessageBox( hWnd, ERRMSG_12, "确认", MB_OK | MB_ICONSTOP );         //MLHIDE
#else
				MessageBox( hWnd, "GlobalAllocPtr Error ( PALDATA )", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
				return 1;
			}
#ifdef PUK2_MEMCHECK
			memlistset( lpDraw->PalTbl, MEMLISTTYPE_16BPP_PALTABLE );
#endif

			for(i=0;i<256;i++){
				lpDraw->PalTbl[i][PPLR]=( (i>>rBitRShift)<<rBitLShift );
				lpDraw->PalTbl[i][PPLG]=( (i>>gBitRShift)<<gBitLShift );
				lpDraw->PalTbl[i][PPLB]=( (i>>bBitRShift)<<bBitLShift );
			}
		}else lpDraw->PalTbl=NULL;
	#endif

	// DirectDraw初期化フラグをTRUEにする
	DDinitFlag = TRUE;


	if ( device3D <= 1 ){
		last3D_mode = device3D;
#ifdef PUK2_FPS
		if ( device3D < -3 ){
#else
		if ( device3D < 1 ){
#endif
#ifdef PUK3_ERRORMESSAGE_NUM
	#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
			MakeNonOKMessageBox( INFOMSG_5, "确认", 400, 100, 2000 );            //MLHIDE
	#else
			MessageBox( hWnd, INFOMSG_5, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
	#endif
#else
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
			MakeNonOKMessageBox( "设定的绘图模式无法启动。\n以绘图模式　１启动。", "确认", 400, 100, 2000 ); //MLHIDE
#else
			MessageBox( hWnd, "设定的绘图模式无法启动。\n以绘图模式　１启动。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
#endif
			device3D = last3D_mode = 1;
		}
	}

	if (CmdLineFlg&CMDLINE_3D_OFF){
		device3D = last3D_mode = 1;
	}

	{
		char flg=0;
		// この画面モードでDirect3Dが使用できるかをチェック
		if ( lpDraw->ddsd.ddpfPixelFormat.dwFlags&(DDPF_LUMINANCE|DDPF_BUMPLUMINANCE|DDPF_BUMPDUDV|DDPF_ALPHAPIXELS) ) flg=1;
		if ( lpDraw->ddsd.ddpfPixelFormat.dwFourCC!=0 ) flg=1;
		if ( (int)lpDraw->ddsd.ddpfPixelFormat.dwRGBBitCount!=displayBpp ) flg=1;
		if (flg){
#ifdef PUK3_ERRORMESSAGE_NUM
	#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
			MakeNonOKMessageBox( INFOMSG_6, "确认", 400, 100, 2000 );            //MLHIDE
	#else
			MessageBox( hWnd, INFOMSG_6, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
	#endif
////				MessageBox( hWnd, INFOMSG_7, "确认", MB_OK | MB_ICONSTOP );
#else
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
			MakeNonOKMessageBox( "设定的绘图模式无法启动。\n以绘图模式　１启动。", "确认", 400, 100, 2000 ); //MLHIDE
#else
			MessageBox( hWnd, "设定的绘图模式无法启动。\n以绘图模式　１启动。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
////				MessageBox( hWnd, "此画面模式无法进行绘图。\n因此将会有部分显示变更。", "确认", MB_OK | MB_ICONSTOP );
#endif
			device3D = last3D_mode = 1;
		}
	}

#ifdef PUK2_FPS
	resetFrameRate();
#endif

	if ( device3D <= 1 ) return 0;

	// Direct3D の初期化 ----------------
	
	// Direct3D オブジェクトの生成
	if ( lpDraw->lpDD2->QueryInterface( IID_IDirect3D7, (void **)&lpDraw->lpD3 )!=S_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_13, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
		MessageBox( hWnd, "Direct3D Object生成失白。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
		goto REL;
	}

	// デバイスの生成
	if ( device3D>=4 && lpDraw->lpD3->CreateDevice( IID_IDirect3DTnLHalDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
		last3D_mode = 4;
	}else{
		if ( device3D>=3 && lpDraw->lpD3->CreateDevice( IID_IDirect3DHALDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
			last3D_mode = 3;
		}else{
			if ( device3D>=2 && lpDraw->lpD3->CreateDevice( IID_IDirect3DRGBDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
				last3D_mode = 2;
			}else{
				goto REL;
			}
		}
	}
/***
		if ( device3D>=5 && lpDraw->lpD3->CreateDevice( IID_IDirect3DTnLHalDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
			last3D_mode=5;
		}else{
			if ( device3D>=4 && lpDraw->lpD3->CreateDevice( IID_IDirect3DHALDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
				last3D_mode=4;
			}else{
				if ( device3D>=3 && lpDraw->lpD3->CreateDevice( IID_IDirect3DMMXDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
					last3D_mode=3;
				}else{
					if ( device3D>=2 && lpDraw->lpD3->CreateDevice( IID_IDirect3DRGBDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )==D3D_OK ){
						last3D_mode=2;
					}else{
						goto REL;
					}
				}
			}
		}
***/
/***
		if ( device3D>0 || lpDraw->lpD3->CreateDevice( IID_IDirect3DTnLHalDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )!=D3D_OK ){
			if ( device3D>1 || lpDraw->lpD3->CreateDevice( IID_IDirect3DHALDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )!=D3D_OK ){
				if ( device3D>2 || lpDraw->lpD3->CreateDevice( IID_IDirect3DMMXDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )!=D3D_OK ){
					if ( lpDraw->lpD3->CreateDevice( IID_IDirect3DRGBDevice, lpDraw->lpBACKBUFFER, &lpDraw->lpD3DEVICE )!=D3D_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
						MessageBox( hWnd, ERRMSG_14, "确认", MB_OK | MB_ICONSTOP );
#else
						MessageBox( hWnd, "３Ｄ设备生成失白。", "确认", MB_OK | MB_ICONSTOP );
#endif
						goto REL;
					}
				}
			}
		}
***/

	// 透过处理使用の设定
	if ( lpDraw->lpD3DEVICE->GetCaps(&d3dd)!=D3D_OK ) goto REL;
	lpDraw->TRC=FALSE;
	if ( d3dd.dwDevCaps&D3DDEVCAPS_DRAWPRIMTLVERTEX ){
		if ( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_COLORKEYENABLE, TRUE )!=D3D_OK ) goto REL;
		else lpDraw->TRC=TRUE;
	}

	// デバイスの初期设定
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_CLIPPING, FALSE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_LIGHTING, FALSE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_LASTPIXEL, FALSE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_ZENABLE, D3DZB_FALSE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_TEXTUREPERSPECTIVE, FALSE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_ZWRITEENABLE, FALSE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_ALPHABLENDENABLE, TRUE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_CULLMODE, D3DCULL_NONE )!=D3D_OK ) goto REL;

	if( lpDraw->lpD3DEVICE->SetTextureStageState( 0, D3DTSS_ALPHAOP, D3DTOP_MODULATE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetTextureStageState( 0, D3DTSS_ALPHAARG1, D3DTA_TEXTURE )!=D3D_OK ) goto REL;
	if( lpDraw->lpD3DEVICE->SetTextureStageState( 0, D3DTSS_ALPHAARG2, D3DTA_DIFFUSE )!=D3D_OK ) goto REL;

	// ビューポートの设定
	vw.dwX=0,					vw.dwY=0;
	vw.dwWidth=lpDraw->xSize,	vw.dwHeight=lpDraw->ySize;
	vw.dvMinZ=0.0f,				vw.dvMaxZ=1.0f;

	if ( lpDraw->lpD3DEVICE->SetViewport(&vw)!=D3D_OK ) goto REL;

	// OFFスクリーンサーフェイス作成用情报の取得
	if ( FAILED( lpDraw->lpD3DEVICE->GetCaps(&d4) ) ) goto REL;
	lpDraw->deviceGUID=d4.deviceGUID;
	lpDraw->dwTextureCaps=d4.dpcTriCaps.dwTextureCaps;

	lpDraw->sx=SurfaceSizeX+2,	lpDraw->sy=SurfaceSizeY+2;
	{
		int wd = lpDraw->sx, hi = lpDraw->sy;
		// ドライバの必要に应じて幅と高さを调整
		if ( lpDraw->dwTextureCaps&D3DPTEXTURECAPS_POW2){
			for(lpDraw->sx=1;wd>lpDraw->sx;lpDraw->sx<<=1);
			for(lpDraw->sy=1;hi>lpDraw->sy;lpDraw->sy<<=1);
		}
		if ( lpDraw->dwTextureCaps&D3DPTEXTURECAPS_SQUAREONLY ){
			if (lpDraw->sx>lpDraw->sy) lpDraw->sy=lpDraw->sx;
			else lpDraw->sx=lpDraw->sy;
		}
	}

	// 实际にビットマップを设定できるかを确认
	{
		LPDIRECTDRAWSURFACE7 xsf=0, asf=0;
		int i;

		xsf=CreateSurface( 256, 256, 0, DDSCAPS_SYSTEMMEMORY );
		if (xsf){
			for(i=0;i<8;i++){
				if ( lpDraw->lpD3DEVICE->SetTexture( 0, xsf )!=D3D_OK ){
					xsf->Release();
					xsf=NULL;
					goto REL;
				}
				if ( lpDraw->lpD3DEVICE->GetTexture( 0, &asf )!=D3D_OK ){
					xsf->Release();
					xsf=NULL;
					goto REL;
				}
				if (asf!=NULL){ asf->Release();	break; }
				asf->Release();
			}
			xsf->Release();
			xsf=NULL;
			if (i>=8) goto REL;
			useTextureStage=i;
		}
	}

	if (last3D_mode!=device3D){
#ifdef PUK3_ERRORMESSAGE_NUM
	#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
/***
		if (last3D_mode==4) MakeNonOKMessageBox( INFOMSG_8, "确认", 400, 100, 2000 );
***/
		if (last3D_mode==3) MakeNonOKMessageBox( INFOMSG_9, "确认", 400, 100, 2000 ); //MLHIDE
		if (last3D_mode==2) MakeNonOKMessageBox( INFOMSG_10, "确认", 400, 100, 2000 ); //MLHIDE
	#else
/***
		if (last3D_mode==4) MessageBox( hWnd, INFOMSG_8, "确认", MB_OK | MB_ICONSTOP );
***/
		if (last3D_mode==3) MessageBox( hWnd, INFOMSG_9, "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
		if (last3D_mode==2) MessageBox( hWnd, INFOMSG_10, "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
#else
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
/***
		if (last3D_mode==4) MakeNonOKMessageBox( "设定的绘图模式无法启动。\n以绘图模式　４启动。", "确认", 400, 100, 2000 );
***/
		if (last3D_mode==3) MakeNonOKMessageBox( "设定的绘图模式无法启动。\n以绘图模式　３启动。", "确认", 400, 100, 2000 ); //MLHIDE
		if (last3D_mode==2) MakeNonOKMessageBox( "设定的绘图模式无法启动。\n以绘图模式　２启动。", "确认", 400, 100, 2000 ); //MLHIDE
#else
/***
		if (last3D_mode==4) MessageBox( hWnd, "设定的绘图模式无法启动。\n以绘图模式　４启动。", "确认", MB_OK | MB_ICONSTOP );
***/
		if (last3D_mode==3) MessageBox( hWnd, "设定的绘图模式无法启动。\n以绘图模式　３启动。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
		if (last3D_mode==2) MessageBox( hWnd, "设定的绘图模式无法启动。\n以绘图模式　２启动。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
#endif

		// 描画モードを保存
		{
			HANDLE fp;
			DWORD get;
			char c[11];

			// ファイルオープン
			fp=CreateFile( "data\\update.dat", GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL ); //MLHIDE
			if (fp!=INVALID_HANDLE_VALUE){
				sprintf( c, "%d", last3D_mode );                                  //MLHIDE
				WriteFile( fp,c,strlen(c),&get,NULL );

				CloseHandle(fp);
			}
		}
		device3D = last3D_mode;
	}

#ifdef PUK2_FPS
	resetFrameRate();
#endif
	
	return 0;

REL:
	// デバイス解放
	if( lpDraw->lpD3DEVICE != NULL ){
		lpDraw->lpD3DEVICE->Release();
		lpDraw->lpD3DEVICE = NULL;
	}
	// Direct3Dの解放
	if( lpDraw->lpD3 != NULL ){
		lpDraw->lpD3->Release();
		lpDraw->lpD3 = NULL;
	}
	lpDraw->TRC=FALSE;

	// 描画モードを保存
	{
		HANDLE fp;
		DWORD get;
		char c[11]="1";                                                     //MLHIDE

		// ファイルオープン
		fp=CreateFile( "data\\update.dat", GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL ); //MLHIDE
		if (fp!=INVALID_HANDLE_VALUE){
			WriteFile( fp,c,strlen(c),&get,NULL );
			CloseHandle(fp);
		}
	}

#ifdef PUK3_ERRORMESSAGE_NUM
	#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
		MakeNonOKMessageBox( INFOMSG_11, "确认", 400, 100, 2000 );            //MLHIDE
	#else
		MessageBox( hWnd, INFOMSG_11, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
	#endif
#else
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
	MakeNonOKMessageBox( "设定的绘图模式无法启动。\n以绘图模式　１启动。", "确认", 400, 100, 2000 ); //MLHIDE
#else
	MessageBox( hWnd, "设定的绘图模式无法启动。\n以绘图模式　１启动。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
#endif

	device3D = last3D_mode = 1;

#ifdef PUK2_FPS
	resetFrameRate();
#endif
	return 0;
}

/* オブジェクトを开放する ****************************************************/
void ReleaseDirectDraw( void )
{
	int i;
	
	// １６ビット用パレットテーブル解放
	if ( lpDraw != NULL){
		if ( lpDraw->PalTbl != NULL ){
#ifdef PUK2_MEMCHECK
			memlistrel( lpDraw->PalTbl, MEMLISTTYPE_16BPP_PALTABLE );
#endif
			GlobalFreePtr( lpDraw->PalTbl );
			lpDraw->PalTbl = NULL;
		}
	}
	// 全てのOFFスクリーンサーフェスを开放
	for( i = 0 ; i < SurfaceCnt ; i++ ){
		// サーフェスがあったら
		if( SurfaceInfo[ i ].lpSurface != NULL ){
			// リリース
			SurfaceInfo[ i ].lpSurface->Release();
			SurfaceInfo[ i ].lpSurface = NULL;
		}
	}
	// バトルサーフェスの开放
	if( lpBattleSurface != NULL ){
		// リリース
		lpBattleSurface->Release();
		lpBattleSurface = NULL;
	}
	// オートマップ作业用サーフェスの开放
	if( lpAutoMapSurface != NULL ){
		// リリース
		lpAutoMapSurface->Release();
		lpAutoMapSurface= NULL;
	}
	
	// デバイス解放
	if( lpDraw->lpD3DEVICE != NULL ){
		lpDraw->lpD3DEVICE->Release();
		lpDraw->lpD3DEVICE = NULL;
	}
	// Direct3Dの解放
	if( lpDraw->lpD3 != NULL ){
		lpDraw->lpD3->Release();
		lpDraw->lpD3 = NULL;
	}
	// パレット解放
	if( lpDraw->lpPALETTE != NULL ){
		lpDraw->lpPALETTE->Release();
		lpDraw->lpPALETTE = NULL;
	}
	// クリッ布开放
	if( lpDraw->lpCLIPPER != NULL ){
		lpDraw->lpCLIPPER->Release();
		lpDraw->lpCLIPPER = NULL;
	}
	// バックサーフェス解放
	if( lpDraw->lpBACKBUFFER != NULL ){
		lpDraw->lpBACKBUFFER->Release();
		lpDraw->lpBACKBUFFER = NULL;
	}
	// プライマリサーフェス解放
	if( lpDraw->lpFRONTBUFFER != NULL ){
		lpDraw->lpFRONTBUFFER->Release();
		lpDraw->lpFRONTBUFFER = NULL;
	}
	// DirectDrawの开放
	if( lpDraw->lpDD2 != NULL){ 
		lpDraw->lpDD2->Release();
		lpDraw->lpDD2 = NULL;
	}
	// DIRECT_DRAW_3D 构造体开放
	if( lpDraw != NULL){ 
#ifdef PUK2_MEMCHECK
		memlistrel( lpDraw, MEMLISTTYPE_STRUCT_DIRECT_DRAW );
#endif
		GlobalFreePtr( lpDraw );
		lpDraw = NULL;
	}
	
	// DirectDraw 初期化フラグを FALSE にする
	DDinitFlag = FALSE;	
}

//---------------------------------------------------------------------------//
// 概要 ：垂直同期线中にバックサーフェスからフロントサーフェスへコピー       //
// 引数 ：なし                         										 //
// 戾值 ：なし                                                               //
//---------------------------------------------------------------------------//
void Flip( void )
{
	// ウィンドウモードの时
	if( WindowMode == TRUE ){
		RECT 	clientRect;  // クライアント领域の矩形领域を格纳
		POINT 	clientPoint; // クライアント领域の左上のスクリーン座标位置を格纳
		
		// クライアント领域の左上座标の设定
		clientPoint.x = clientPoint.y = 0;
		// クライアント座标から左上のスクリーン座标を取得
		ClientToScreen( hWnd, &clientPoint ); 
		// クライアント领域のサイズを取得
		GetClientRect( hWnd, &clientRect );
		// クライアント领域全体のスクリーン座标を求める ( clientRect に格纳される )
		OffsetRect( &clientRect, clientPoint.x, clientPoint.y );
		// 転送元の矩形领域をセット
		//SetRect( &moveRect, 0, 0, lpDraw->xSize, lpDraw->ySize );

		HRESULT ddreturn;    
		DWORD scanLine;
		int cnt = 0;

		// 描画するタイミングまでループ
		while( 1 )
		{
			// ラスターウエイトしないなら即ブレイク
			if( RasterNoWaitMode )break;

			// スキャンライン取得
			ddreturn = lpDraw->lpDD2->GetScanLine( &scanLine );

			// 描画するタイミングのとき
			if( ddreturn == DDERR_VERTICALBLANKINPROGRESS  ) break;
			// 垂直归线中のとき（省电力モードなどでかえってこなくなる）
			else if( ddreturn != DD_OK ){
				cnt++;
				// 待ったら拔ける
				if( cnt >= 10000 ) break;
			}
		}

		// プライマリーサーフェスへ一発転送 ( これがウィンドウモードのフリップ )
#ifdef PUK3_WINDOWMODE_BLACKOUT
		if ( lpDraw->lpFRONTBUFFER->Blt( &clientRect, lpDraw->lpBACKBUFFER, NULL, DDBLT_WAIT, NULL )
			 != DD_OK ){
			PrimaryLostFlag = TRUE;
		}
#else
		lpDraw->lpFRONTBUFFER->Blt( &clientRect, lpDraw->lpBACKBUFFER, NULL, DDBLT_WAIT, NULL );
#endif
	}else{  // フルスクリーンモードの时
		// ３Ｄ使用时
		if ( getUsable3D() ){
			HRESULT ddreturn;    
			DWORD scanLine;
			int cnt = 0;
	
			// 描画するタイミングまでループ
			while( 1 )
			{
				// スキャンライン取得
				ddreturn = lpDraw->lpDD2->GetScanLine( &scanLine );
	
				// 描画するタイミングのとき
				if( ddreturn == DDERR_VERTICALBLANKINPROGRESS  ) break;
				// 垂直归线中のとき（省电力モードなどでかえってこなくなる）
				else if( ddreturn != DD_OK ){
					cnt++;
					// 待ったら拔ける
					if( cnt >= 10000 ) break;
				}
			}
#ifdef PUK3_WINDOWMODE_BLACKOUT
			if ( lpDraw->lpFRONTBUFFER->Blt( NULL, lpDraw->lpBACKBUFFER, NULL, DDBLT_WAIT, NULL )
				 != DD_OK ){
				PrimaryLostFlag = TRUE;
			}
		}else{
			if ( lpDraw->lpFRONTBUFFER->Flip( NULL, DDFLIP_WAIT ) != DD_OK ){
				PrimaryLostFlag = TRUE;
			}
		}
#else
			lpDraw->lpFRONTBUFFER->Blt( NULL, lpDraw->lpBACKBUFFER, NULL, DDBLT_WAIT, NULL );
		}
		// 画面のフリップ
		else lpDraw->lpFRONTBUFFER->Flip( NULL, DDFLIP_WAIT );
#endif
	}

	// フリップカウンター变更
	if( FlipCnt == 0 ) FlipCnt = 1;
	else FlipCnt = 0;
	
	return;
}
#ifdef PUK3_WINDOWMODE_BLACKOUT

// リストアができるかチェックする
BOOL CheckRestore()
{
	return( lpDraw->lpDD2->TestCooperativeLevel() == DD_OK );
}

#endif

// ゴミが表示されるのを防ぐため、絵を切り取る位置を微妙にずらす
////	#define D3_SrcRcDiff 0.4f
#define D3_SrcRcDiffL (0.05f)
#define D3_SrcRcDiffR (0.0f)

//---------------------------------------------------------------------------//
// 概要 ：パターン用サーフェスの作成                                         //
// 引数 ：short bxsize           : 作成??????????横幅(????????)                       //
//        short bysize           : 作成??????????縦幅(????????)                       //
//        DWORD ColorKey         : 透明色とする色番号(0～255)                //
// 戾り值：正常終了 ... サーフェスのアドレス / 失败 ... NULL                 //
//---------------------------------------------------------------------------//
LPDIRECTDRAWSURFACE7 CreateSurface( short bxsize, short bysize, DWORD ColorKey, unsigned int VramOrSysram )
{
	DDCOLORKEY ddck;
	LPDIRECTDRAWSURFACE7 lpSurface;
	unsigned long wd=bxsize,hi=bysize;

	if ( getUsable3D() ){
		bxsize += 2,	bysize += 2;
		wd=bxsize,		hi=bysize;
	}

	// パターン用バッファの生成
	// ddsCaps构造体を  有效化 & dwWidth,dwHeight指定有效
	lpDraw->ddsd.dwFlags = DDSD_CAPS | DDSD_WIDTH | DDSD_HEIGHT;
	if ( getUsable3D() ) lpDraw->ddsd.dwFlags |= DDSD_PIXELFORMAT|DDSD_TEXTURESTAGE|DDSD_CKSRCBLT;
	// この、サーフェスはフロントでもバックでもない、サーフェスであることを指定
	if ( getUsable3D() ) lpDraw->ddsd.ddsCaps.dwCaps=DDSCAPS_TEXTURE;
	else lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_OFFSCREENPLAIN | VramOrSysram;
	// サーフェスの幅、高さを指定
	lpDraw->ddsd.dwWidth = bxsize;
	lpDraw->ddsd.dwHeight = bysize;

	// Direct3D 使用时
	if ( getUsable3D() ){
		// ハードウェアデバイスのテクスチャ管理を有效にする
		if ( lpDraw->deviceGUID==IID_IDirect3DHALDevice ) lpDraw->ddsd.ddsCaps.dwCaps2=DDSCAPS2_TEXTUREMANAGE;
		else if ( lpDraw->deviceGUID==IID_IDirect3DTnLHalDevice ) lpDraw->ddsd.ddsCaps.dwCaps2=DDSCAPS2_TEXTUREMANAGE;
		else lpDraw->ddsd.ddsCaps.dwCaps=DDSCAPS_TEXTURE|DDSCAPS_SYSTEMMEMORY;

		// ドライバの必要に应じて幅と高さを调整
		if ( lpDraw->dwTextureCaps&D3DPTEXTURECAPS_POW2){
			for(lpDraw->ddsd.dwWidth=1;wd>lpDraw->ddsd.dwWidth;lpDraw->ddsd.dwWidth<<=1);
			for(lpDraw->ddsd.dwHeight=1;hi>lpDraw->ddsd.dwHeight;lpDraw->ddsd.dwHeight<<=1);
		}
		if ( lpDraw->dwTextureCaps&D3DPTEXTURECAPS_SQUAREONLY ){
			if (lpDraw->ddsd.dwWidth>lpDraw->ddsd.dwHeight) lpDraw->ddsd.dwHeight=lpDraw->ddsd.dwWidth;
			else lpDraw->ddsd.dwWidth=lpDraw->ddsd.dwHeight;
		}

		// ピクセルフォーマットの确认
		if (lpDraw->ddsd.ddpfPixelFormat.dwRGBBitCount==0L) return (LPDIRECTDRAWSURFACE7)NULL;
	}

	// パターン用サーフェスの实定义
	if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpSurface, NULL ) != DD_OK )
		return (LPDIRECTDRAWSURFACE7)NULL;

	// 透明色カラーキーの设定
	ddck.dwColorSpaceLowValue =  ColorKey;
	ddck.dwColorSpaceHighValue = ColorKey;
	lpSurface->SetColorKey( DDCKEY_SRCBLT, &ddck );

	return lpSurface;
}
#ifdef PUK3_SCENE_CHANGE
LPDIRECTDRAWSURFACE7 CreateWarkSurface( short bxsize, short bysize, DWORD ColorKey, unsigned int VramOrSysram )
{
	DDSURFACEDESC2 ddsd = {0};
	DDCOLORKEY ddck;
	LPDIRECTDRAWSURFACE7 lpSurface;

	if ( getUsable3D() ){
		ddsd.dwSize = sizeof( lpDraw->ddsd );
		ddsd.dwFlags = DDSD_CAPS | DDSD_WIDTH | DDSD_HEIGHT;
		ddsd.ddsCaps.dwCaps = DDSCAPS_OFFSCREENPLAIN | DDSCAPS_3DDEVICE;
		ddsd.dwWidth = bxsize;
		ddsd.dwHeight = bysize;
	}else{
		ddsd = lpDraw->ddsd;

		// パターン用バッファの生成
		// ddsCaps构造体を  有效化 & dwWidth,dwHeight指定有效
		ddsd.dwFlags = DDSD_CAPS | DDSD_WIDTH | DDSD_HEIGHT;
		// この、サーフェスはフロントでもバックでもない、サーフェスであることを指定
		ddsd.ddsCaps.dwCaps = DDSCAPS_OFFSCREENPLAIN | VramOrSysram;
		// サーフェスの幅、高さを指定
		ddsd.dwWidth = bxsize;
		ddsd.dwHeight = bysize;
	}

	// パターン用サーフェスの实定义
	if( lpDraw->lpDD2->CreateSurface( &ddsd, &lpSurface, NULL ) != DD_OK )
		return (LPDIRECTDRAWSURFACE7)NULL;

	// 透明色カラーキーの设定
	ddck.dwColorSpaceLowValue =  ColorKey;
	ddck.dwColorSpaceHighValue = ColorKey;
	lpSurface->SetColorKey( DDCKEY_SRCBLT, &ddck );

	return lpSurface;
}
#endif

//----------------------------------//

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE7 lpSurface : 描画元サーフェス                  //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
HRESULT DrawSurfaceFast( short bx, short by, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf, unsigned long rgba )
{
	short x0, y0;
	long w, h;
	
	// 転送领域のセット
	RECT rect = { 0, 0, SurfaceSizeX, SurfaceSizeY };

	x0 = bx;
	y0 = by;
	w = rect.right - rect.left;
	h = rect.bottom - rect.top;

	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}

	if (bltf&BLTF_MRR_X){
		// 左端のチェック
		if( x0 < 0 ){ rect.right+=x0;	x0=0; }
		// 右端のチェック
		if( x0 + (rect.right - rect.left) > lpDraw->xSize ) rect.left+=x0+(rect.right - rect.left)-lpDraw->xSize;
	}else{
		// 左端のチェック
		if( x0 < 0 ){ rect.left-=x0;	x0=0; }
		// 右端のチェック
		if( x0 + (rect.right - rect.left) > lpDraw->xSize ) rect.right-=x0+(rect.right - rect.left)-lpDraw->xSize;
	}
	if (bltf&BLTF_MRR_Y){
		// 上端のチェック
		if( y0 < 0 ){ rect.bottom+=y0;	y0=0; }
		// 下端のチェック
		if( y0 + (rect.bottom - rect.top) > lpDraw->ySize ) rect.top+=y0+(rect.bottom - rect.top)-lpDraw->ySize;
	}else{
		// 上端のチェック
		if( y0 < 0 ){ rect.top-=y0;		y0=0; }
		// 下端のチェック
		if( y0 + (rect.bottom - rect.top) > lpDraw->ySize ) rect.bottom-=y0+(rect.bottom - rect.top)-lpDraw->ySize;
	}

#ifdef _DEBUG
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif

	if ( getUsable3D() &&lpDraw->TRC){
		D3_BLTPOINT a[4];
		HRESULT hr;
		float l,r,t,b;

		w=rect.right-rect.left;
		h=rect.bottom-rect.top;

		l=( (float)( (bltf&BLTF_MRR_X)?rect.right-1:rect.left)+D3_SrcRcDiffL )/lpDraw->sx;
		r=( (float)( (bltf&BLTF_MRR_X)?rect.left-1:rect.right)-D3_SrcRcDiffR )/lpDraw->sx;
		t=( (float)( (bltf&BLTF_MRR_Y)?rect.bottom-1:rect.top)+D3_SrcRcDiffL )/lpDraw->sy;
		b=( (float)( (bltf&BLTF_MRR_Y)?rect.top-1:rect.bottom)-D3_SrcRcDiffR )/lpDraw->sy;

		// 左上
		a[0].x=(float)(x0),		a[0].y=(float)(y0),		a[0].z=0,	a[0].rhw=1,	a[0].clr=rgba;
			a[0].tu=l,		a[0].tv=t;
		// 右上
		a[1].x=(float)(x0+w),	a[1].y=a[0].y,			a[1].z=0,	a[1].rhw=1,	a[1].clr=rgba;
			a[1].tu=r,		a[1].tv=a[0].tv;
		// 右下
		a[2].x=a[1].x,			a[2].y=(float)(y0+h),	a[2].z=0,	a[2].rhw=1,	a[2].clr=rgba;
			a[2].tu=a[1].tu,							a[2].tv=b;
		// 左下
		a[3].x=a[0].x,			a[3].y=a[2].y,			a[3].z=0,	a[3].rhw=1,	a[3].clr=rgba;
			a[3].tu=a[0].tu,							a[3].tv=a[2].tv;

		// 透过处理の设定
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ){
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ONE );
		}else{
			if ( (rgba&0xff000000)==0xff000000 ){
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ZERO );
			}else{
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_SRCALPHA );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_INVSRCALPHA );
			}
		}

		// テクスチャを选择する
		lpDraw->lpD3DEVICE->SetTexture( useTextureStage, lpSurface );

		hr=lpDraw->lpD3DEVICE->DrawPrimitive( D3DPT_TRIANGLEFAN, D3DFVF_BLTPOINT, a, 4, 0 );

		return hr;
	}else
	{
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ) return DD_OK;

		if ( bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
			RECT rect2={ x0,y0,x0+rect.right-rect.left,y0+rect.bottom-rect.top };
			DDBLTFX ddbf={0};
			ddbf.dwSize=sizeof(ddbf);
			ddbf.dwDDFX=( (bltf&BLTF_MRR_X)?DDBLTFX_MIRRORLEFTRIGHT:0 )|( (bltf&BLTF_MRR_Y)?DDBLTFX_MIRRORUPDOWN:0 );
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_DDFX | DDBLT_KEYSRC | DDBLT_WAIT, &ddbf );
		}else{
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
		}
	}	
}

//----------------------------------//

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE7 lpSurface : 描画元サーフェス                  //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
HRESULT DrawSurfaceStretch( short bx, short by, float scaleX, float scaleY, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf, unsigned long rgba )
{
	float x0, y0;
	float w, h;
	float top = 0, left = 0;
	float right = ( ( float )SurfaceSizeX ) * scaleX;
	float bottom = ( ( float )SurfaceSizeY ) * scaleY;
	
	// 転送领域のセット
	x0 = ( float )bx;
	y0 = ( float )by;
	w = right - left;
	h = bottom - top;

	// クリッピング处理
	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}
	
	if (bltf&BLTF_MRR_X){
		// 左端のチェック
		if( x0 < 0 ){ right+=( float )x0;	x0=0; }
		// 右端のチェック
		if( ( float )x0 + (right - left) > ( float )lpDraw->xSize ) left+=( float )x0+(right - left)-( float )lpDraw->xSize;
	}else{
		// 左端のチェック
		if( x0 < 0 ){ left-=( float )bx;	x0=0; }
		// 右端のチェック
		if( ( float )x0 + (right - left) > ( float )lpDraw->xSize ) right-=( float )x0+(right - left)-( float )lpDraw->xSize;
	}
	if (bltf&BLTF_MRR_Y){
		// 上端のチェック
		if( y0 < 0 ){ bottom+=( float )y0;	y0=0; }
		// 下端のチェック
		if( ( float )y0 + (bottom - top) > ( float )lpDraw->ySize ) top-=( float )y0+(bottom - top)-( float )lpDraw->ySize;
	}else{
		// 上端のチェック
		if( y0 < 0 ){ top-=( float )y0;	y0=0; }
		// 下端のチェック
		if( ( float )y0 + (bottom - top) > ( float )lpDraw->ySize ) bottom-=( float )y0+(bottom - top)-( float )lpDraw->ySize;
	}
	
#ifdef _DEBUG		
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif
	
	RECT rect;
	// 転送もとの矩形をセット
	SetRect( &rect, ( int )( left / scaleX ), ( int )( top / scaleY ), ( int )( right / scaleX ), ( int )( bottom / scaleY ) );
	
	RECT rect2;
	
	// 隙间をなくすための处理
	// 右端のチェック
	if( x0 + right - left < ( float )lpDraw->xSize ) right += 1;
	
	// 下端のチェック
	if( y0 + bottom - top < ( float )lpDraw->ySize ) bottom += 1;
	
	// 転送先の矩形をセット
	SetRect( &rect2, ( int )x0, ( int )y0, ( int )( x0 + right - left ), ( int )( y0 + bottom - top ) );
	
	if ( getUsable3D() &&lpDraw->TRC){
		D3_BLTPOINT a[4];
		HRESULT hr;
		float l,r,t,b;

		l=( (float)( (bltf&BLTF_MRR_X)?rect.right:rect.left)+D3_SrcRcDiffL )/lpDraw->sx;
		r=( (float)( (bltf&BLTF_MRR_X)?rect.left:rect.right)-D3_SrcRcDiffR )/lpDraw->sx;
		t=( (float)( (bltf&BLTF_MRR_Y)?rect.bottom:rect.top)+D3_SrcRcDiffL )/lpDraw->sy;
		b=( (float)( (bltf&BLTF_MRR_Y)?rect.top:rect.bottom)-D3_SrcRcDiffR )/lpDraw->sy;

		// 左上
		a[0].x=(float)rect2.left,	a[0].y=(float)rect2.top,		a[0].z=0,	a[0].rhw=1,	a[0].clr=rgba;
			a[0].tu=l,		a[0].tv=t;
		// 右上
		a[1].x=(float)rect2.right,	a[1].y=a[0].y,			a[1].z=0,	a[1].rhw=1,	a[1].clr=rgba;
			a[1].tu=r,		a[1].tv=a[0].tv;
		// 右下
		a[2].x=a[1].x,				a[2].y=(float)rect2.bottom,	a[2].z=0,	a[2].rhw=1,	a[2].clr=rgba;
			a[2].tu=a[1].tu,							a[2].tv=b;
		// 左下
		a[3].x=a[0].x,				a[3].y=a[2].y,			a[3].z=0,	a[3].rhw=1,	a[3].clr=rgba;
			a[3].tu=a[0].tu,							a[3].tv=a[2].tv;

		// 透过处理の设定
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ){
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ONE );
		}else{
			if ( (rgba&0xff000000)==0xff000000 ){
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ZERO );
			}else{
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_SRCALPHA );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_INVSRCALPHA );
			}
		}

		// テクスチャを选择する
		lpDraw->lpD3DEVICE->SetTexture( useTextureStage, lpSurface );

		// 四角形を描画する
		hr=lpDraw->lpD3DEVICE->DrawPrimitive( D3DPT_TRIANGLEFAN, D3DFVF_BLTPOINT, a, 4, 0 );

		return hr;
	}else
	{
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ) return DD_OK;

		if ( bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
			DDBLTFX ddbf={0};
			ddbf.dwSize=sizeof(ddbf);
			ddbf.dwDDFX=( (bltf&BLTF_MRR_X)?DDBLTFX_MIRRORLEFTRIGHT:0 )|( (bltf&BLTF_MRR_Y)?DDBLTFX_MIRRORUPDOWN:0 );
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_DDFX | DDBLT_KEYSRC | DDBLT_WAIT, &ddbf );
		}else{
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_KEYSRC | DDBLT_WAIT, NULL );
		}
	}	
}

//----------------------------------//

// 矩形范围にアルファブレンド ++++
HRESULT RectAlphaBlend( short l, short r, short t, short b, unsigned long rgba, unsigned char bltf )
{
	// Direct3D が使用できないなら終了
	if ( !getUsable3D() ) return DD_OK;

	// 全く表示する部分が无ければ返回
	if( l >= lpDraw->xSize || r <= 0 || t >= lpDraw->ySize || b <= 0 ){
		return DD_OK;
	}

	// 左端のチェック
	if( l < 0 ) l = 0;
	// 右端のチェック
	if( r > lpDraw->xSize ) r = lpDraw->xSize;
	// 上端のチェック
	if( t < 0 ) t = 0;
	// 下端のチェック
	if( b > lpDraw->ySize ) b = lpDraw->ySize;

#ifdef _DEBUG
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif

	if ( getUsable3D() &&lpDraw->TRC){
		D3_BLTPOINT a[4];
		HRESULT hr;

		// 左上
		a[0].x=(float)(l),		a[0].y=(float)(t),	a[0].z=0,	a[0].rhw=1,	a[0].clr=rgba;
			a[0].tu=0,	a[0].tv=0;
		// 右上
		a[1].x=(float)(r),	a[1].y=a[0].y,			a[1].z=0,	a[1].rhw=1,	a[1].clr=rgba;
			a[1].tu=0,	a[1].tv=0;
		// 右下
		a[2].x=a[1].x,			a[2].y=(float)(b),	a[2].z=0,	a[2].rhw=1,	a[2].clr=rgba;
			a[2].tu=0,	a[2].tv=0;
		// 左下
		a[3].x=a[0].x,			a[3].y=a[2].y,		a[3].z=0,	a[3].rhw=1,	a[3].clr=rgba;
			a[3].tu=0,	a[3].tv=0;

		// 透过处理の设定
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ){
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ONE );
		}else{
			if ( (rgba&0xff000000)==0xff000000 ){
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ZERO );
			}else{
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_SRCALPHA );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_INVSRCALPHA );
			}
		}

		// テクスチャを选择する
		lpDraw->lpD3DEVICE->SetTexture( useTextureStage, NULL );

		hr=lpDraw->lpD3DEVICE->DrawPrimitive( D3DPT_TRIANGLEFAN, D3DFVF_BLTPOINT, a, 4, 0 );

		return hr;
	}	
	return DD_OK;
}

//----------------------------------//

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE7 lpSurface : 描画元サーフェス                  //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
HRESULT DrawSurfaceFast_PUK2( short bx, short by, RECT rect, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf, unsigned long rgba )
{
	short x0, y0;
	long w, h;
	
	x0 = bx;
	y0 = by;
	w = rect.right - rect.left;
	h = rect.bottom - rect.top;

	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}

	if (bltf&BLTF_MRR_X){
		// 左端のチェック
		if( x0 < 0 ){ rect.right+=x0;	x0=0; }
		// 右端のチェック
		if( x0 + (rect.right - rect.left) > lpDraw->xSize ) rect.left+=x0+(rect.right - rect.left)-lpDraw->xSize;
	}else{
		// 左端のチェック
		if( x0 < 0 ){ rect.left-=x0;	x0=0; }
		// 右端のチェック
		if( x0 + (rect.right - rect.left) > lpDraw->xSize ) rect.right-=x0+(rect.right - rect.left)-lpDraw->xSize;
	}
	if (bltf&BLTF_MRR_Y){
		// 上端のチェック
		if( y0 < 0 ){ rect.bottom+=y0;	y0=0; }
		// 下端のチェック
		if( y0 + (rect.bottom - rect.top) > lpDraw->ySize ) rect.top+=y0+(rect.bottom - rect.top)-lpDraw->ySize;
	}else{
		// 上端のチェック
		if( y0 < 0 ){ rect.top-=y0;		y0=0; }
		// 下端のチェック
		if( y0 + (rect.bottom - rect.top) > lpDraw->ySize ) rect.bottom-=y0+(rect.bottom - rect.top)-lpDraw->ySize;
	}

#ifdef _DEBUG
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif

	if ( getUsable3D() &&lpDraw->TRC){
		D3_BLTPOINT a[4];
		HRESULT hr;
		float l,r,t,b;

		w=rect.right-rect.left;
		h=rect.bottom-rect.top;

		l=( (float)( (bltf&BLTF_MRR_X)?rect.right-1:rect.left)+D3_SrcRcDiffL )/lpDraw->sx;
		r=( (float)( (bltf&BLTF_MRR_X)?rect.left-1:rect.right)-D3_SrcRcDiffR )/lpDraw->sx;
		t=( (float)( (bltf&BLTF_MRR_Y)?rect.bottom-1:rect.top)+D3_SrcRcDiffL )/lpDraw->sy;
		b=( (float)( (bltf&BLTF_MRR_Y)?rect.top-1:rect.bottom)-D3_SrcRcDiffR )/lpDraw->sy;

		// 左上
		a[0].x=(float)(x0),		a[0].y=(float)(y0),		a[0].z=0,	a[0].rhw=1,	a[0].clr=rgba;
			a[0].tu=l,		a[0].tv=t;
		// 右上
		a[1].x=(float)(x0+w),	a[1].y=a[0].y,			a[1].z=0,	a[1].rhw=1,	a[1].clr=rgba;
			a[1].tu=r,		a[1].tv=a[0].tv;
		// 右下
		a[2].x=a[1].x,			a[2].y=(float)(y0+h),	a[2].z=0,	a[2].rhw=1,	a[2].clr=rgba;
			a[2].tu=a[1].tu,							a[2].tv=b;
		// 左下
		a[3].x=a[0].x,			a[3].y=a[2].y,			a[3].z=0,	a[3].rhw=1,	a[3].clr=rgba;
			a[3].tu=a[0].tu,							a[3].tv=a[2].tv;

		// 透过处理の设定
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ){
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ONE );
		}else{
			if ( (rgba&0xff000000)==0xff000000 ){
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ZERO );
			}else{
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_SRCALPHA );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_INVSRCALPHA );
			}
		}

		// テクスチャを选择する
		lpDraw->lpD3DEVICE->SetTexture( useTextureStage, lpSurface );

		hr=lpDraw->lpD3DEVICE->DrawPrimitive( D3DPT_TRIANGLEFAN, D3DFVF_BLTPOINT, a, 4, 0 );

		return hr;
	}else
	{
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ) return DD_OK;

		if ( bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
			RECT rect2={ x0,y0,x0+rect.right-rect.left,y0+rect.bottom-rect.top };
			DDBLTFX ddbf={0};
			ddbf.dwSize=sizeof(ddbf);
			ddbf.dwDDFX=( (bltf&BLTF_MRR_X)?DDBLTFX_MIRRORLEFTRIGHT:0 )|( (bltf&BLTF_MRR_Y)?DDBLTFX_MIRRORUPDOWN:0 );
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_DDFX | DDBLT_KEYSRC | DDBLT_WAIT, &ddbf );
		}else{
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
		}
	}	
}

//----------------------------------//

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE7 lpSurface : 描画元サーフェス                  //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
HRESULT DrawSurfaceStretch_PUK2( short bx, short by, RECT *rc, float scaleX, float scaleY, LPDIRECTDRAWSURFACE7 lpSurface, unsigned long bltf, unsigned long rgba )
{
	float x0, y0;
	float w, h;
	float top = (float)rc->top * scaleY, left = (float)rc->left * scaleX;
	float right = ( ( float )rc->right ) * scaleX;
	float bottom = ( ( float )rc->bottom ) * scaleY;
	
	// 転送领域のセット
	x0 = ( float )bx;
	y0 = ( float )by;
	w = right - left;
	h = bottom - top;

	// クリッピング处理
	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}
	
	if (bltf&BLTF_MRR_X){
		// 左端のチェック
		if( x0 < 0 ){ right+=( float )x0;	x0=0; }
		// 右端のチェック
		if( ( float )x0 + (right - left) > ( float )lpDraw->xSize ) left+=( float )x0+(right - left)-( float )lpDraw->xSize;
	}else{
		// 左端のチェック
		if( x0 < 0 ){ left-=( float )bx;	x0=0; }
		// 右端のチェック
		if( ( float )x0 + (right - left) > ( float )lpDraw->xSize ) right-=( float )x0+(right - left)-( float )lpDraw->xSize;
	}
	if (bltf&BLTF_MRR_Y){
		// 上端のチェック
		if( y0 < 0 ){ bottom+=( float )y0;	y0=0; }
		// 下端のチェック
		if( ( float )y0 + (bottom - top) > ( float )lpDraw->ySize ) top-=( float )y0+(bottom - top)-( float )lpDraw->ySize;
	}else{
		// 上端のチェック
		if( y0 < 0 ){ top-=( float )y0;	y0=0; }
		// 下端のチェック
		if( ( float )y0 + (bottom - top) > ( float )lpDraw->ySize ) bottom-=( float )y0+(bottom - top)-( float )lpDraw->ySize;
	}
	
#ifdef _DEBUG		
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif
	
	RECT rect;
	// 転送もとの矩形をセット
	SetRect( &rect, ( int )( left / scaleX ), ( int )( top / scaleY ), ( int )( right / scaleX ), ( int )( bottom / scaleY ) );
	
	RECT rect2;
	
	// 隙间をなくすための处理
	// 右端のチェック
	if( x0 + right - left < ( float )lpDraw->xSize ) right += 1;
	
	// 下端のチェック
	if( y0 + bottom - top < ( float )lpDraw->ySize ) bottom += 1;
	
	// 転送先の矩形をセット
	SetRect( &rect2, ( int )x0, ( int )y0, ( int )( x0 + right - left ), ( int )( y0 + bottom - top ) );

	if ( getUsable3D() &&lpDraw->TRC){
		D3_BLTPOINT a[4];
		HRESULT hr;
		float l,r,t,b;

		l=( (float)( (bltf&BLTF_MRR_X)?rect.right:rect.left)+D3_SrcRcDiffL )/lpDraw->sx;
		r=( (float)( (bltf&BLTF_MRR_X)?rect.left:rect.right)-D3_SrcRcDiffR )/lpDraw->sx;
		t=( (float)( (bltf&BLTF_MRR_Y)?rect.bottom:rect.top)+D3_SrcRcDiffL )/lpDraw->sy;
		b=( (float)( (bltf&BLTF_MRR_Y)?rect.top:rect.bottom)-D3_SrcRcDiffR )/lpDraw->sy;

		// 左上
		a[0].x=(float)rect2.left,	a[0].y=(float)rect2.top,		a[0].z=0,	a[0].rhw=1,	a[0].clr=rgba;
			a[0].tu=l,		a[0].tv=t;
		// 右上
		a[1].x=(float)rect2.right,	a[1].y=a[0].y,			a[1].z=0,	a[1].rhw=1,	a[1].clr=rgba;
			a[1].tu=r,		a[1].tv=a[0].tv;
		// 右下
		a[2].x=a[1].x,				a[2].y=(float)rect2.bottom,	a[2].z=0,	a[2].rhw=1,	a[2].clr=rgba;
			a[2].tu=a[1].tu,							a[2].tv=b;
		// 左下
		a[3].x=a[0].x,				a[3].y=a[2].y,			a[3].z=0,	a[3].rhw=1,	a[3].clr=rgba;
			a[3].tu=a[0].tu,							a[3].tv=a[2].tv;

		// 透过处理の设定
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ){
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
			lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ONE );
		}else{
			if ( (rgba&0xff000000)==0xff000000 ){
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ZERO );
			}else{
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_SRCALPHA );
				lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_INVSRCALPHA );
			}
		}

		// テクスチャを选择する
		lpDraw->lpD3DEVICE->SetTexture( useTextureStage, lpSurface );

		// 四角形を描画する
		hr=lpDraw->lpD3DEVICE->DrawPrimitive( D3DPT_TRIANGLEFAN, D3DFVF_BLTPOINT, a, 4, 0 );

		return hr;
	}else
	{
		if ( (bltf&BLTF_3DBLTTYPE)==BLTF_ADDBLT ) return DD_OK;

		if ( bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
			DDBLTFX ddbf={0};
			ddbf.dwSize=sizeof(ddbf);
			ddbf.dwDDFX=( (bltf&BLTF_MRR_X)?DDBLTFX_MIRRORLEFTRIGHT:0 )|( (bltf&BLTF_MRR_Y)?DDBLTFX_MIRRORUPDOWN:0 );
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_DDFX | DDBLT_KEYSRC | DDBLT_WAIT, &ddbf );
		}else{
			// バックサーフェスへ高速転送
			return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_KEYSRC | DDBLT_WAIT, NULL );
		}
	}	
}

//----------------------------------//

struct NEWRGBQUAD{
  BYTE    b; 
  BYTE    g; 
  BYTE    r; 
  BYTE    rs; 
}; 

// ＢＭＰをサーフェスへ転送关数 （ memcpy を使用 ）****************************/
void DrawBitmapToSurface3( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo, PALDATA *pld, char pchg )
{

	DDSURFACEDESC2 ddsd;	// サーフェス构造体

    char *pDest;		// 転送先のポインタ
	short *pDest2;		// 転送先のポインタ（ワード型）
	long *pDest3;		// 転送先のポインタ（ロング型）
	COL24BIT *pDest24;	// 転送先のポインタ（２４ＢＩＴ）
    unsigned char *pSource; 		// 転送元のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int bmpWidth;		// ＢＭＰの横幅记忆
	int i,j;			// ループカウンタ
	unsigned char n;	// 転送もとの值（pSource[j] と书くと长いから）
	short px2;		// 転送先のポインタ（ワード型）
	long px4;		// 転送先のポインタ（ロング型）
	COL24BIT px3;	// 転送先のポインタ（２４ＢＩＴ）

	NEWRGBQUAD *ppl=(NEWRGBQUAD *)( (int)pBmpInfo+sizeof(BITMAPINFOHEADER) );
	
	// サーフェスポインタがNULLの时返回
	if( lpSurface == NULL ) return;
	
	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( ddsd ) );
	ddsd.dwSize = sizeof( ddsd );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
		//MessageBox( hWnd, ERRMSG_15, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}	

	// サーフェスの左上隅のアドレス
	pDest = ( char *)( ddsd.lpSurface );

	if (pBmpInfo){	// ＢＭＰファイルから読み込むとき
		// 転送元のアドレス
		pSource = (unsigned char *)pBmpInfo + BmpOffBits
					+ offsetY * pBmpInfo->bmiHeader.biWidth + offsetX;
	}else{			// Realbin から読み込むとき
		// 転送元のアドレス
		pSource = (unsigned char *)(pRealBinBits + offsetY * RealBinWidth + offsetX);
	}

				
	// サーフェスの横幅记忆
	surfacePitch = ddsd.lPitch;

	if (pBmpInfo){	// ＢＭＰファイルから読み込むとき
		// ＢＭＰの横幅记忆
		bmpWidth = pBmpInfo->bmiHeader.biWidth;
	}else{			// Realbin から読み込むとき
		// ＢＭＰの横幅记忆
		bmpWidth = RealBinWidth;
	}

	if (pld){
		// 使用するパレットが与えられている时
		switch(displayBpp){
		case 8:		// ８ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				memcpy( pDest, pSource, sizeX );
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		case 16:		// １６ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				pDest2 = (short *)pDest;

				for( j= 0; j < sizeX; j++ ){
					if( pSource[j] == DEF_COLORKEY ) px2 = 0;
					else{
						n=pSource[j];
						px2 = lpDraw->PalTbl[ pld[n][PPLR] ][PPLR]|lpDraw->PalTbl[ pld[n][PPLG] ][PPLG]|lpDraw->PalTbl[ pld[n][PPLB] ][PPLB];
						if (!px2) px2 = 1;
					}
					pDest2[j] = px2;
				}
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		case 24:		// ２４ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				pDest24 = (COL24BIT *)pDest;

				for( j= 0; j < sizeX; j++ ){
					if( pSource[j] == DEF_COLORKEY ){ px3.r = px3.g = px3.b = 0; }
					else{
						n=pSource[j];
						if( pld[n][PPLR] | pld[n][PPLG] | pld[n][PPLB] ){
							px3.r=pld[n][PPLR],	px3.g=pld[n][PPLG],	px3.b=pld[n][PPLB];
						}else{
							px3.r=0,			px3.g=0,			px3.b=1;
						}
						pDest24[j] = px3;
					}
				}
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		case 32:		// ３２ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				pDest3 = (long *)pDest;

				for( j= 0; j < sizeX; j++ ){
					if( pSource[j] == DEF_COLORKEY ) px4 = 0;
					else{
						n=pSource[j];
						px4 = (unsigned long)pld[n][PPLB] +
							 ((unsigned long)pld[n][PPLG]<<8) + 
							 ((unsigned long)pld[n][PPLR]<<16);
						if (!px4) px4 = 1;
					}
					pDest3[j] = px4;
				}
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		}
	}else{
		// 使用するパレットが与えられていない时(ＸＧデフォルトのパレット使用)
		switch(displayBpp){
		case 8:		// ８ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				memcpy( pDest, pSource, sizeX );
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		case 16:		// １６ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				pDest2 = (short *)pDest;

				for( j= 0; j < sizeX; j++ ){
					if( pSource[j] == DEF_COLORKEY ) px2 = 0;
					else{
						px2 = highColorPalette[(unsigned char)pSource[j]];
						if (!px2) px2 = 1;
					}
					pDest2[j] = px2;
				}
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		case 24:		// ２４ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				pDest24 = (COL24BIT *)pDest;

				for( j= 0; j < sizeX; j++ ){
					if( pSource[j] == DEF_COLORKEY ){ px3.r = px3.g = px3.b = 0; }
					else{
						px3 = Color24Palette[(unsigned char)pSource[j]];
						if( !( px3.r | px3.g | px3.b ) ) px3.b = 1;
					}
					pDest24[j] = px3;
				}
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		case 32:		// ３２ＢＰＰ
			// 縦のサイズ分ループする
			for( i = 0 ; i < sizeY ; i++ ){
				pDest3 = (long *)pDest;

				for( j= 0; j < sizeX; j++ ){
					if( pSource[j] == DEF_COLORKEY ) px4 = 0;
					else{
						px4 = fullColorPalette[(unsigned char)pSource[j]];
						if (!px4) px4 = 1;
					}
					pDest3[j] = px4;
				}
				// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
				pSource -= bmpWidth;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
			}
			break;
		}
	}

	// アクセスするサーフェスをアンロックする
	if( lpSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
		//MessageBox( hWnd, ERRMSG_16, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}	
	
	return;
}

//**************************************
// パレットチェンジ关系プログラム
//**************************************

// パレットリスト予约
enum{
	OLDPAL_DEF_N = 0,					// 旧デフォルトパレットの新パレットチェンジ未使用
	OLDPAL_NOW_N,						// 旧现在パレットの新パレットチェンジ未使用
	SPALLIST_NOCHG_MAX,
	OLDPAL_DEF_U = SPALLIST_NOCHG_MAX,	// 旧デフォルトパレットの新パレットチェンジ使用
	OLDPAL_NOW_U,						// 旧现在パレットの新パレットチェンジ使用
	SPALLIST_CHG_MAX,
	SPALLIST_MAX = SPALLIST_CHG_MAX
};

struct PALLETE_LIST *PalList = NULL;
struct PALLETE_LIST SPalList[SPALLIST_MAX];

//--------------------------------------
// パレットリスト初期化关数
//--------------------------------------
void IniPalList()
{
	int i;

	// パレットリスト领域确保
	PalList = (PALLETE_LIST *)GlobalAllocPtr( GPTR, (PALLIST_MAX)*sizeof(PALLETE_LIST) );
#ifdef PUK2_MEMCHECK
	memlistset( PalList, MEMLISTTYPE_PALLIST );
#endif

	// パレットリストの初期化
	for(i=0;i<PALLIST_MAX;i++) PalList[i].sprNo = -1;
	for(i=0;i<SPALLIST_MAX;i++) SPalList[i].palNo = -1;
}

//--------------------------------------
// パレットリスト初期化关数
//--------------------------------------
void RelPalList()
{
#ifdef PUK2_MEMCHECK
	memlistrel( PalList, MEMLISTTYPE_PALLIST );
#endif
	GlobalFreePtr(PalList);
	PalList = NULL;
}

//--------------------------------------
// パレットリスト取得关数
//--------------------------------------
// 引数
//	sprNo				パレットを取得したいスプライトインフォのインデックス
//
// 戾り值
//	PALLETE_LIST *		取得したパレットリストへのアドレスを返す、失败した场合は NULL を返す
//
PALLETE_LIST *GetPalList( int sprNo )
{
	int i;
#ifdef PUK2_PALLISTOVER
	#ifdef PUK2_PALLISTOVERMSG
		#ifdef _DEBUG
			static int overcnt = 0;
		#endif
	#endif
#endif

	// 未使用のパレットを探す
	for(i=0;i<PALLIST_MAX;i++){
		if ( PalList[i].sprNo < 0 ) break;
	}
	// 未使用のパレットがあったら
	if ( i < PALLIST_MAX ){
		// スプライトインフォに关连付けて返す
		PalList[i].sprNo = sprNo;
		PalList[i].cnt = 0;
#ifdef PUK2_PALLISTOVER
	#ifdef PUK2_PALLISTOVERMSG
		#ifdef _DEBUG
			if ( overcnt ){
				char s[256];
				sprintf( s, "over:	%d", overcnt );                                //MLHIDE
				MessageBox( hWnd, s, "pallist Over", MB_OK );                     //MLHIDE
			}
			overcnt = 0;
		#endif
	#endif
#endif
		return &PalList[i];
	}

	// 全部使ってるので、前回使ってないパレットを探す
	for(i=0;i<PALLIST_MAX;i++){
		if ( PalList[i].cnt > 0 ) break;
	}
	// 前回使ってないパレットがあったら
	if ( i < PALLIST_MAX ){
		// 先ずパレットとスプライトインフォの关连を切る
		SpriteInfo[ PalList[i].sprNo ].lpPalList = NULL;
		PalList[i].sprNo = -1;

		// スプライトインフォに关连付けて返す
		PalList[i].sprNo = sprNo;
		PalList[i].cnt = 0;
#ifdef PUK2_PALLISTOVER
	#ifdef PUK2_PALLISTOVERMSG
		#ifdef _DEBUG
			if ( overcnt ){
				char s[256];
				sprintf( s, "over:	%d", overcnt );                                //MLHIDE
				MessageBox( hWnd, s, "pallist Over", MB_OK );                     //MLHIDE
			}
			overcnt = 0;
		#endif
	#endif
#endif
		return &PalList[i];
	}
#ifdef PUK2_PALLISTOVER
	#ifdef PUK2_PALLISTOVERMSG
		#ifdef _DEBUG
			overcnt++;
		#endif
	#endif

	// 全部埋っていたので、０番目を使う
	i = 0;
	// 先ずパレットとスプライトインフォの关连を切る
	SpriteInfo[ PalList[i].sprNo ].lpPalList = NULL;
	PalList[i].sprNo = -1;

	// スプライトインフォに关连付けて返す
	PalList[i].sprNo = sprNo;
	PalList[i].cnt = 0;

	return &PalList[i];
#else

	// パレットを取得できないので NULL を返す
	return NULL;
#endif
}

//--------------------------------------
// パレットリストチェック关数
//--------------------------------------
void CheckPalList()
{
	int i;

	for(i=0;i<PALLIST_MAX;i++){
		// 使用してないなら次へ
		if ( PalList[i].sprNo < 0 ) continue;

		// 前回使用からの経过时间を确认
		if ( PalList[i].cnt >= PALLIST_DETHCNT ){
			// 一定时间过ぎてたら、使用してないものとして关连付けを切る
			SpriteInfo[ PalList[i].sprNo ].lpPalList = NULL;
			PalList[i].sprNo = -1;
		}

		// カウントを增やす
		PalList[i].cnt++;
	}
}

//--------------------------------------
// パレットチェンジのパレット取得关数
//--------------------------------------
// 引数
//	sprNo				パレットを取得したいスプライトインフォのインデックス
//
// 戾り值
//	PALLETE_LIST *			取得したパレットへのアドレスを返す、失败した场合は NULL を返す
//
/***	flgの值
enum{
	GETPALDATA_OLDDEFPAL,			// 旧パレットの昼パレットをパレットチェンジ
	GETPALDATA_OLDNOWPAL,			// 旧パレットの现在のパレットをパレットチェンジ
	GETPALDATA_PUK2PAL,				// 引数のパレットのデータを使用
};
***/
PALLETE_LIST *MakePalList( int sprNo, char flg, char pchg, PALDATA *pld, RGBQUAD *pqd )
{
	int i;
	PALLETE_LIST *ret;
	union PALPOINTER npl = {0};
	unsigned char *xPalchg=0;
	unsigned char xPalchgdata[3]={ 128,128,128 };
	PALETTEENTRY *usePalette = Palette;
	int spallistNo;

	// パレットのデータが无かったら
	if ( (pld==NULL) && (pqd==NULL) ) flg = GETPALDATA_OLDDEFPAL;

	// パレットチェンジの指定が无かったら
	if (Palchg) xPalchg=Palchg;
	else xPalchg=xPalchgdata;

	// パレットチェンジをしないなら
	if (!pchg) xPalchg=xPalchgdata;

	// 昼パレットベースなら昼パレットセット
	if ( flg == GETPALDATA_OLDDEFPAL ) usePalette = DefPalette;

	spallistNo = -1;
	// 旧パレットは、共用パレットリストを使用
	if ( flg == GETPALDATA_OLDDEFPAL ){
		if ( (xPalchg[PPLR]==128) && (xPalchg[PPLG]==128) && (xPalchg[PPLB]==128) ){
			spallistNo = OLDPAL_DEF_N;
		}else{
			spallistNo = OLDPAL_DEF_U;
		}
	}else if ( flg == GETPALDATA_OLDNOWPAL ){
		if ( (xPalchg[PPLR]==128) && (xPalchg[PPLG]==128) && (xPalchg[PPLB]==128) ){
			spallistNo = OLDPAL_NOW_N;
		}else{
			spallistNo = OLDPAL_NOW_U;
		}
	}

	// 旧パレットなら
	if ( spallistNo >= 0 ){
		ret = &SPalList[spallistNo];
		npl.p4 = ret->pal;
		if ( spallistNo == OLDPAL_DEF_N ){
			if ( ret->palNo == 0 ) return ret;
		}else{
			if ( ret->palNo == PalState.palNo &&
				 ret->palcnt == PalState.count ) return ret;
		}
		if ( spallistNo == OLDPAL_DEF_N ){
			ret->palNo = 0;
		}else{
			ret->palNo = PalState.palNo;
		}
		ret->palcnt = PalState.count;
	}
	// 新パレットなら
	else{
		// パレットの领域を取得
		ret = GetPalList( sprNo );
		if ( ret == NULL ) return NULL;
		npl.p4 = ret->pal;
		ret->palNo = 0;
		ret->palcnt = 0;
		if (pchg){
			ret->palNo = PalState.palNo;
			ret->palcnt = PalState.count;
		}
	}

	// システムパレットを代入
	if ( (flg==GETPALDATA_OLDDEFPAL) || (flg==GETPALDATA_OLDNOWPAL) ){
		switch(displayBpp){
		case 16:
			for(i=0;i<16;i++){
				npl.p2[i] =
					 lpDraw->PalTbl[ usePalette[i].peRed ][PPLR] |
					 lpDraw->PalTbl[ usePalette[i].peGreen ][PPLG] |
					 lpDraw->PalTbl[ usePalette[i].peBlue ][PPLB];
				if (!npl.p2[i]) npl.p2[i] = 1;
			}
			break;
		case 24:
			for(i=0;i<16;i++){
				npl.p3[i].r = usePalette[i].peRed;
				npl.p3[i].g = usePalette[i].peGreen;
				npl.p3[i].b = usePalette[i].peBlue;
				if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
			}
			break;
		case 32:
			for(i=0;i<16;i++){
				npl.p4[i] = RGB_MAKE( usePalette[i].peRed, usePalette[i].peGreen, usePalette[i].peBlue );
				if (!npl.p4[i]) npl.p4[i] = 1;
			}
			break;
		}
	}

	// パレットチェンジをするなら
	if (pchg){
		if ( (flg==GETPALDATA_OLDDEFPAL) || (flg==GETPALDATA_OLDNOWPAL) ){
			switch(displayBpp){
			case 16:
				for(i=16;i<240;i++){
					npl.p2[i] =
						 lpDraw->PalTbl[ PalchgTbl[PPLR][ usePalette[i].peRed ] ][PPLR] |
						 lpDraw->PalTbl[ PalchgTbl[PPLG][ usePalette[i].peGreen ] ][PPLG] |
						 lpDraw->PalTbl[ PalchgTbl[PPLB][ usePalette[i].peBlue ] ][PPLB];
					if (!npl.p2[i]) npl.p2[i] = 1;
				}
				break;
			case 24:
				for(i=16;i<240;i++){
					npl.p3[i].r = PalchgTbl[PPLR][ usePalette[i].peRed ];
					npl.p3[i].g = PalchgTbl[PPLG][ usePalette[i].peGreen ];
					npl.p3[i].b = PalchgTbl[PPLB][ usePalette[i].peBlue ];
					if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
				}
				break;
			case 32:
				for(i=16;i<240;i++){
					npl.p4[i] = RGB_MAKE(
						 PalchgTbl[PPLR][ usePalette[i].peRed ],
						 PalchgTbl[PPLG][ usePalette[i].peGreen ],
						 PalchgTbl[PPLB][ usePalette[i].peBlue ] );
					if (!npl.p4[i]) npl.p4[i] = 1;
				}
				break;
			}
		}else{
			if (pld){
				// pld が渡されていた时
				switch(displayBpp){
				case 16:
					for(i=0;i<256;i++){
						npl.p2[i] =
							 lpDraw->PalTbl[ PalchgTbl[PPLR][ pld[i][PPLR] ] ][PPLR] |
							 lpDraw->PalTbl[ PalchgTbl[PPLG][ pld[i][PPLG] ] ][PPLG] |
							 lpDraw->PalTbl[ PalchgTbl[PPLB][ pld[i][PPLB] ] ][PPLB];
						if (!npl.p2[i]) npl.p2[i] = 1;
					}
					break;
				case 24:
					for(i=0;i<256;i++){
						npl.p3[i].r = PalchgTbl[PPLR][ pld[i][PPLR] ];
						npl.p3[i].g = PalchgTbl[PPLG][ pld[i][PPLG] ];
						npl.p3[i].b = PalchgTbl[PPLB][ pld[i][PPLB] ];
						if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
					}
					break;
				case 32:
					for(i=0;i<256;i++){
						npl.p4[i] = RGB_MAKE(
							 PalchgTbl[PPLR][ pld[i][PPLR] ],
							 PalchgTbl[PPLG][ pld[i][PPLG] ],
							 PalchgTbl[PPLB][ pld[i][PPLB] ] );
						if (!npl.p4[i]) npl.p4[i] = 1;
					}
					break;
				}
			}else{
				// pld が渡されていなくて、pqd が渡されている时
				switch(displayBpp){
				case 16:
					for(i=0;i<256;i++){
						npl.p2[i] =
							 lpDraw->PalTbl[ PalchgTbl[PPLR][ pqd[i].rgbRed ] ][PPLR] |
							 lpDraw->PalTbl[ PalchgTbl[PPLG][ pqd[i].rgbGreen ] ][PPLG] |
							 lpDraw->PalTbl[ PalchgTbl[PPLB][ pqd[i].rgbBlue ] ][PPLB];
					}
					break;
				case 24:
					for(i=0;i<256;i++){
						npl.p3[i].r = PalchgTbl[PPLR][ pqd[i].rgbRed ];
						npl.p3[i].g = PalchgTbl[PPLG][ pqd[i].rgbGreen ];
						npl.p3[i].b = PalchgTbl[PPLB][ pqd[i].rgbBlue ];
						if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
					}
					break;
				case 32:
					for(i=0;i<256;i++){
						npl.p4[i] = RGB_MAKE(
							 PalchgTbl[PPLR][ pqd[i].rgbRed ],
							 PalchgTbl[PPLG][ pqd[i].rgbGreen ],
							 PalchgTbl[PPLB][ pqd[i].rgbBlue ] );
						if (!npl.p4[i]) npl.p4[i] = 1;
					}
					break;
				}
			}
		}
	}
	// パレットチェンジをしないなら
	else{
		if ( (flg==GETPALDATA_OLDDEFPAL) || (flg==GETPALDATA_OLDNOWPAL) ){
			switch(displayBpp){
			case 16:
				for(i=16;i<240;i++){
					npl.p2[i] =
						 lpDraw->PalTbl[ usePalette[i].peRed ][PPLR] |
						 lpDraw->PalTbl[ usePalette[i].peGreen ][PPLG] |
						 lpDraw->PalTbl[ usePalette[i].peBlue ][PPLB];
					if (!npl.p2[i]) npl.p2[i] = 1;
				}
				break;
			case 24:
				for(i=16;i<240;i++){
					npl.p3[i].r = usePalette[i].peRed;
					npl.p3[i].g = usePalette[i].peGreen;
					npl.p3[i].b = usePalette[i].peBlue;
					if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
				}
				break;
			case 32:
				for(i=16;i<240;i++){
					npl.p4[i] = RGB_MAKE( usePalette[i].peRed, usePalette[i].peGreen, usePalette[i].peBlue );
					if (!npl.p4[i]) npl.p4[i] = 1;
				}
				break;
			}
		}else{
			if (pld){
				// pld が渡されていた时
				switch(displayBpp){
				case 16:
					for(i=0;i<256;i++){
						npl.p2[i] =
							 lpDraw->PalTbl[ pld[i][PPLR] ][PPLR] |
							 lpDraw->PalTbl[ pld[i][PPLG] ][PPLG] |
							 lpDraw->PalTbl[ pld[i][PPLB] ][PPLB];
						if (!npl.p2[i]) npl.p2[i] = 1;
					}
					break;
				case 24:
					for(i=0;i<256;i++){
						npl.p3[i].r = pld[i][PPLR];
						npl.p3[i].g = pld[i][PPLG];
						npl.p3[i].b = pld[i][PPLB];
						if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
					}
					break;
				case 32:
					for(i=0;i<256;i++){
						npl.p4[i] = RGB_MAKE(
							 pld[i][PPLR],
							 pld[i][PPLG],
							 pld[i][PPLB] );
						if (!npl.p4[i]) npl.p4[i] = 1;
					}
					break;
				}
			}else{
				// pld が渡されていなくて、pqd が渡されている时
				switch(displayBpp){
				case 16:
					for(i=0;i<256;i++){
						npl.p2[i] =
							 lpDraw->PalTbl[ pqd[i].rgbRed ][PPLR] |
							 lpDraw->PalTbl[ pqd[i].rgbGreen ][PPLG] |
							 lpDraw->PalTbl[ pqd[i].rgbBlue ][PPLB];
						if (!npl.p2[i]) npl.p2[i] = 1;
					}
					break;
				case 24:
					for(i=0;i<256;i++){
						npl.p3[i].r = pqd[i].rgbRed;
						npl.p3[i].g = pqd[i].rgbGreen;
						npl.p3[i].b = pqd[i].rgbBlue;
						if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
					}
					break;
				case 32:
					for(i=0;i<256;i++){
						npl.p4[i] = RGB_MAKE( pqd[i].rgbRed, pqd[i].rgbGreen, pqd[i].rgbBlue );
						if (!npl.p4[i]) npl.p4[i] = 1;
					}
					break;
				}
			}
		}
	}

	// システムパレットを代入
	if ( (flg==GETPALDATA_OLDDEFPAL) || (flg==GETPALDATA_OLDNOWPAL) ){
		switch(displayBpp){
		case 16:
			for(i=240;i<256;i++){
				npl.p2[i] = lpDraw->PalTbl[ usePalette[i].peRed ][PPLR] |
					 lpDraw->PalTbl[ usePalette[i].peGreen ][PPLG] |
					 lpDraw->PalTbl[ usePalette[i].peBlue ][PPLB];
				if (!npl.p2[i]) npl.p2[i] = 1;
			}
			break;
		case 24:
			for(i=240;i<256;i++){
				npl.p3[i].r = usePalette[i].peRed;
				npl.p3[i].g = usePalette[i].peGreen;
				npl.p3[i].b = usePalette[i].peBlue;
				if ( npl.p3[i].r | npl.p3[i].g | npl.p3[i].b ) npl.p3[i].b = 1;
			}
			break;
		case 32:
			for(i=240;i<256;i++){
				npl.p4[i] = RGB_MAKE( usePalette[i].peRed, usePalette[i].peGreen, usePalette[i].peBlue );
				if (!npl.p4[i]) npl.p4[i] = 1;
			}
			break;
		}
	}

	// 透过色の设定
	switch(displayBpp){
	case 16:
		npl.p2[DEF_COLORKEY] = 0;
		break;
	case 24:
		npl.p3[DEF_COLORKEY].r = 0;
		npl.p3[DEF_COLORKEY].g = 0;
		npl.p3[DEF_COLORKEY].b = 0;
		break;
	case 32:
		npl.p4[DEF_COLORKEY] = 0;
		break;
	}

	return ret;
}

//----------------------------------//

// パレットチェンジを利用しないように设定 ++++
void Palchg_not()
{
	Palchg=NULL;
}

//-----------------------------------//

// 时间ごとのパレットチェンジを利用するように设定 ++++
void Palchg_auto()
{
	Palchg=Palchg_a_data;
}

//-----------------------------------//

// 值を指定してパレットチェンジ ++++
void Palchg_manual( unsigned char lr, unsigned char lg, unsigned char lb )
{
	Palchg_m_data[PPLR]=lr,	Palchg_m_data[PPLG]=lg,	Palchg_m_data[PPLB]=lb;
	Palchg=Palchg_m_data;
}

//-----------------------------------//

// ＢＭＰをサーフェスへ転送关数 （ memcpy を使用 ）****************************/
void DrawBitmapToSurface2_PUK2_PAL( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, PALDATA *ppl, char pchg )
{

	DDSURFACEDESC2 ddsd;	// サーフェス构造体

    char *pDest;		// 転送先のポインタ
	short *pDest2;		// 転送先のポインタ（ワード型）
	long *pDest3;		// 転送先のポインタ（ロング型）
	COL24BIT *pDest24;	// 転送先のポインタ（２４ＢＩＴ）
    char *pSource; 		// 転送元のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int bmpWidth;		// ＢＭＰの横幅记忆
	int i,j;			// ループカウンタ
	unsigned char n;	// 転送もとの值（pSource[j] と书くと长いから）
	PALDATA *pal;		// 使用するパレット
	
	// サーフェスポインタがNULLの时返回
	if( lpSurface == NULL ) return;
	
	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( ddsd ) );
	ddsd.dwSize = sizeof( ddsd );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_17, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}
	pal=ppl;

	// サーフェスの左上隅のアドレス
	pDest = ( char *)( ddsd.lpSurface );
	
	// Realbin から読み込むとき
	// 転送元のアドレス
	pSource = pRealBinBits
				+ offsetY * RealBinWidth + offsetX;
				
	// サーフェスの横幅记忆
	surfacePitch = ddsd.lPitch;

	// Realbin 読み込むとき
	// ＢＭＰの横幅记忆
	bmpWidth = RealBinWidth;

	switch(displayBpp){
	case 8:		// ８ＢＰＰ
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			memcpy( pDest, pSource, sizeX );
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	case 16:		// １６ＢＰＰ
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			pDest2 = (short *)pDest;

			for( j= 0; j < sizeX; j++ ){
				if( pSource[j] == DEF_COLORKEY ) pDest2[j] = 0;
				else{
					n=pSource[j];
					pDest2[j]=lpDraw->PalTbl[ pal[n][PPLR] ][PPLR]|lpDraw->PalTbl[ pal[n][PPLG] ][PPLG]|lpDraw->PalTbl[ pal[n][PPLB] ][PPLB];
					if (!pDest2[j]) pDest2[j]=1;
				}
			}
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	case 24:		// ２４ＢＰＰ
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			pDest24 = (COL24BIT *)pDest;

			for( j= 0; j < sizeX; j++ ){
				if( pSource[j] == DEF_COLORKEY ){ pDest24[j].r = pDest24[j].g = pDest24[j].b = 0; }
				else{
					n=pSource[j];
					if( pal[n][PPLR] | pal[n][PPLG] | pal[n][PPLB] ){
						pDest24[j].r=pal[n][PPLR],	pDest24[j].g=pal[n][PPLG],	pDest24[j].b=pal[n][PPLB];
					}else{
						pDest24[j].r=0,			pDest24[j].g=0,			pDest24[j].b=1;
					}
				}
			}
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	case 32:		// ３２ＢＰＰ
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			pDest3 = (long *)pDest;

			for( j= 0; j < sizeX; j++ ){
				if( pSource[j] == DEF_COLORKEY ) pDest3[j] = 0;
				else{
					n=pSource[j];
					pDest3[j]=(unsigned long)pal[n][PPLB]+((unsigned long)pal[n][PPLG]<<8)+((unsigned long)pal[n][PPLR]<<16);
					if (!pDest3[j]) pDest3[j]=1;
				}
			}
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	}

	// アクセスするサーフェスをアンロックする
	if( lpSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_18, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}	
	
	return;
}


// ＢＭＰをサーフェスへ転送关数 （ memcpy を使用 ）****************************/
void DrawBitmapToSurface_AnimPal( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo, int PalNo, char pchg )
{

	DDSURFACEDESC2 ddsd;	// サーフェス构造体

    char *pDest;				// 転送先のポインタ
	unsigned short *pDest2;		// 転送先のポインタ（ワード型）
	unsigned long *pDest3;		// 転送先のポインタ（ロング型）
	COL24BIT *pDest24;			// 転送先のポインタ（２４ＢＩＴ）
    unsigned char *pSource; 	// 転送元のポインタ
	int surfacePitch;			// サーフェスの横幅记忆
	int bmpWidth;				// ＢＭＰの横幅记忆
	int i,j;					// ループカウンタ
	unsigned long *pPal=0;		// パレット参照用
	
	// サーフェスポインタがNULLの时返回
	if( lpSurface == NULL ) return;
	
	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( ddsd ) );
	ddsd.dwSize = sizeof( ddsd );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_19, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}	

	// サーフェスの左上隅のアドレス
	pDest = ( char *)( ddsd.lpSurface );

	if (pBmpInfo){	// ＢＭＰファイルから読み込むとき
		// 転送元のアドレス
		pSource = (unsigned char *)pBmpInfo + BmpOffBits
					+ offsetY * pBmpInfo->bmiHeader.biWidth + offsetX;
	}else{			// Realbin から読み込むとき
		// 転送元のアドレス
		pSource = (unsigned char *)(pRealBinBits + offsetY * RealBinWidth + offsetX);
	}

				
	// サーフェスの横幅记忆
	surfacePitch = ddsd.lPitch;

	if (pBmpInfo){	// ＢＭＰファイルから読み込むとき
		// ＢＭＰの横幅记忆
		bmpWidth = pBmpInfo->bmiHeader.biWidth;
	}else{			// Realbin から読み込むとき
		// ＢＭＰの横幅记忆
		bmpWidth = RealBinWidth;
	}

	// パレットの指定
	if (SprPal[PalNo].dt==NULL) return;

	pPal=SprPal[PalNo].dt->ChgBf;
	if (pchg) pPal=SprPal[PalNo].dt->ChgAf;

	switch(displayBpp){
	case 16:
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			pDest2 = (unsigned short *)pDest;
			for( j= 0; j < sizeX; j++ ){
				pDest2[j] = (unsigned short)pPal[ pSource[j] ];
			}
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	case 24:
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			pDest24 = (COL24BIT *)pDest;
			for( j= 0; j < sizeX; j++ ){
				pDest24[j].r = (unsigned char)( (pPal[ pSource[j] ]>>16)&0xff );
				pDest24[j].g = (unsigned char)( (pPal[ pSource[j] ]>>8)&0xff );
				pDest24[j].b = (unsigned char)( (pPal[ pSource[j] ])&0xff );
			}
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	case 32:
		// 縦のサイズ分ループする
		for( i = 0 ; i < sizeY ; i++ ){
			pDest3 = (unsigned long *)pDest;
			for( j= 0; j < sizeX; j++ ){
				pDest3[j] = pPal[ pSource[j] ];
			}
			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSource -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
		}
		break;
	}

	// アクセスするサーフェスをアンロックする
	if( lpSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_20, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}	
	
	return;
}








//---------------------------------------------------------------------------//
// オートマッピングを描画                                                    //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned char *autoMap;			マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping16( int x, int y, unsigned char *autoMap, int w, int h, int zoom )
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	int color, color2;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;
	RECT rc;
	DDBLTFX ddbf={0};
	ddbf.dwSize=sizeof(ddbf);

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 作业用サーフェースの初期化
	ddbf.dwFillColor = DEF_COLORKEY;

	rc.left = 0;
	rc.right = ( (hh+ww)<<1 )+2;
	rc.top = 0;
	rc.bottom = hh+ww+2;
	lpAutoMapSurface->Blt( &rc, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbf );

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpAutoMapSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_21, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	short *ptDest;		// 転送先のポインタ
	short *tmpPtDest;	// ワーク
	short *tmpPtDest2;	// ワーク２

	// サーフェスの横幅记忆
	surfacePitch = (ddsd.lPitch >> 1);

	ptDest = (short *)(ddsd.lpSurface) + (ww+1) * surfacePitch + 1;
	tmpPtDest  = ptDest;
	tmpPtDest2 = ptDest;
	for( i = 0; i < hh; i++ ){
		ptDest = tmpPtDest;
		for( j = 0; j < ww; j++ ){

			color2 = autoMap[j];
			if( color2 != DEF_COLORKEY ) color = highColorPalette[color2];
			else color = 1;
			if (color==0) color = 1;

			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
			ptDest -= (surfacePitch-2);
		}
		tmpPtDest += (surfacePitch+2);
		autoMap += w;
	}

	// ＰＣキャラの表示（マップ中央で白黒点灭）
	if( pcFlush ){
		color = 0xFFFF;
	}else{
		color = 1;
	}
	if( pcFlushTime+1000 <= GetTickCount() ){
		pcFlushTime = GetTickCount();
		pcFlush++;
		pcFlush &= 1;
	}

	ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
	*(ptDest-1)				= color;
	*(ptDest)				= color;
	*(ptDest+1)				= color;
	*(ptDest-surfacePitch)	= color;

	// アクセスするサーフェスをアンロックする
	if( lpAutoMapSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_22, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	x += xx-1;
	y += yy-ww-1;

	if (x<0){ rc.left -= x;	x = 0; }
	if ( x+(rc.right-rc.left)>DEF_APPSIZEX ) rc.right = rc.left + (DEF_APPSIZEX-x);

	if (y<0){ rc.top -= y;	y = 0; }
	if ( y+(rc.bottom-rc.top)>DEF_APPSIZEY ) rc.bottom = rc.top + (DEF_APPSIZEY-y);

	// バックバッファに书き込み
	lpDraw->lpBACKBUFFER->BltFast( x, y, lpAutoMapSurface, &rc, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );

	return;
}

//---------------------------------------------------------------------------//
// オートマッピングを描画                                                    //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned char *autoMap;			マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping24( int x, int y, unsigned char *autoMap, int w, int h, int zoom )
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	COL24BIT color;
	int color2;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;
	RECT rc;
	DDBLTFX ddbf={0};
	ddbf.dwSize=sizeof(ddbf);

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 作业用サーフェースの初期化
	ddbf.dwFillColor = DEF_COLORKEY;

	rc.left = 0;
	rc.right = ( (hh+ww)<<1 )+2;
	rc.top = 0;
	rc.bottom = hh+ww+2;
	lpAutoMapSurface->Blt( &rc, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbf );

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpAutoMapSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_23, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	COL24BIT *ptDest;				// 転送先のポインタ
	COL24BIT *tmpPtDest;			// ワーク
	COL24BIT *tmpPtDest2;			// ワーク２

	// サーフェスの横幅记忆
	surfacePitch = (ddsd.lPitch/3);

	ptDest = (COL24BIT *)(ddsd.lpSurface) + (ww+1) * surfacePitch + 1;
	tmpPtDest  = ptDest;
	tmpPtDest2 = ptDest;
	for( i = 0; i < hh; i++ ){
		ptDest = tmpPtDest;
		for( j = 0; j < ww; j++ ){

			color2 = autoMap[j];
			if( color2 != DEF_COLORKEY ) color = Color24Palette[color2];
			else color.r = 0,	color.g = 0,	color.b = 1;
			if ( (color.r|color.g|color.b)==0 ) color.b = 1;

			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
			ptDest -= (surfacePitch-2);
		}
		tmpPtDest += (surfacePitch+2);
		autoMap += w;
	}

	// ＰＣキャラの表示（マップ中央で白黒点灭）
	if( pcFlush ){
		color.r = 0xFF;
		color.g = 0xFF;
		color.b = 0xFF;
	}else{
		color.r = 0;
		color.g = 0;
		color.b = 1;
	}
	if( pcFlushTime+1000 <= GetTickCount() ){
		pcFlushTime = GetTickCount();
		pcFlush++;
		pcFlush &= 1;
	}

	ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
	*(ptDest-1)				= color;
	*(ptDest)				= color;
	*(ptDest+1)				= color;
	*(ptDest-surfacePitch)	= color;

	// アクセスするサーフェスをアンロックする
	if( lpAutoMapSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_24, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	x += xx-1;
	y += yy-ww-1;

	if (x<0){ rc.left -= x;	x = 0; }
	if ( x+(rc.right-rc.left)>DEF_APPSIZEX ) rc.right = rc.left + (DEF_APPSIZEX-x);

	if (y<0){ rc.top -= y;	y = 0; }
	if ( y+(rc.bottom-rc.top)>DEF_APPSIZEY ) rc.bottom = rc.top + (DEF_APPSIZEY-y);

	// バックバッファに书き込み
	lpDraw->lpBACKBUFFER->BltFast( x, y, lpAutoMapSurface, &rc, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );

	return;
}

//---------------------------------------------------------------------------//
// オートマッピングを描画                                                    //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned char *autoMap;			マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping32( int x, int y, unsigned char *autoMap, int w, int h, int zoom )
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	unsigned long color;
	int color2;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;
	RECT rc;
	DDBLTFX ddbf={0};
	ddbf.dwSize=sizeof(ddbf);

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 作业用サーフェースの初期化
	ddbf.dwFillColor = DEF_COLORKEY;

	rc.left = 0;
	rc.right = ( (hh+ww)<<1 )+2;
	rc.top = 0;
	rc.bottom = hh+ww+2;
	lpAutoMapSurface->Blt( &rc, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbf );

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpAutoMapSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_25, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	unsigned long *ptDest;				// 転送先のポインタ
	unsigned long *tmpPtDest;			// ワーク
	unsigned long *tmpPtDest2;			// ワーク２

	// サーフェスの横幅记忆
	surfacePitch = (ddsd.lPitch>>2);

	ptDest = (unsigned long *)(ddsd.lpSurface) + (ww+1) * surfacePitch + 1;
	tmpPtDest  = ptDest;
	tmpPtDest2 = ptDest;
	for( i = 0; i < hh; i++ ){
		ptDest = tmpPtDest;
		for( j = 0; j < ww; j++ ){

			color2 = autoMap[j];
			if( color2 != DEF_COLORKEY ) color = fullColorPalette[color2];
			else color = 1;
			if (color==0) color = 1;

			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
			ptDest -= (surfacePitch-2);
		}
		tmpPtDest += (surfacePitch+2);
		autoMap += w;
	}

	// ＰＣキャラの表示（マップ中央で白黒点灭）
	if( pcFlush ){
		color = 0x00FFFFFF;
	}else{
		color = 1;
	}
	if( pcFlushTime+1000 <= GetTickCount() ){
		pcFlushTime = GetTickCount();
		pcFlush++;
		pcFlush &= 1;
	}

	ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
	*(ptDest-1)				= color;
	*(ptDest)				= color;
	*(ptDest+1)				= color;
	*(ptDest-surfacePitch)	= color;

	// アクセスするサーフェスをアンロックする
	if( lpAutoMapSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_26, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	x += xx-1;
	y += yy-ww-1;

	if (x<0){ rc.left -= x;	x = 0; }
	if ( x+(rc.right-rc.left)>DEF_APPSIZEX ) rc.right = rc.left + (DEF_APPSIZEX-x);

	if (y<0){ rc.top -= y;	y = 0; }
	if ( y+(rc.bottom-rc.top)>DEF_APPSIZEY ) rc.bottom = rc.top + (DEF_APPSIZEY-y);

	// バックバッファに书き込み
	lpDraw->lpBACKBUFFER->BltFast( x, y, lpAutoMapSurface, &rc, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );

	return;
}

//---------------------------------------------------------------------------//
// オートマッピングを描画                                                    //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned char *autoMap;			マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping( int x, int y, unsigned char *autoMap, int w, int h, int zoom )
{
	switch( displayBpp ){
	case 16:	DrawAutoMapping16( x, y, autoMap, w, h, zoom );	break;
	case 24:	DrawAutoMapping24( x, y, autoMap, w, h, zoom );	break;
	case 32:	DrawAutoMapping32( x, y, autoMap, w, h, zoom );	break;
	}
}


//---------------------------------------------------------------------------//
// 新マップ用オートマッピングを描画                                          //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned short *autoMap;		マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping_Sugi16( int x, int y, unsigned long *autoMap, int w, int h, int zoom )
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	int color;
	unsigned char *color2;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;
	RECT rc;
	DDBLTFX ddbf={0};
	ddbf.dwSize=sizeof(ddbf);

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 作业用サーフェースの初期化
	ddbf.dwFillColor = DEF_COLORKEY;

	rc.left = 0;
	rc.right = ( (hh+ww)<<1 )+2;
	rc.top = 0;
	rc.bottom = hh+ww+2;
	lpAutoMapSurface->Blt( &rc, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbf );

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpAutoMapSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_27, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	short *ptDest;		// 転送先のポインタ
	short *tmpPtDest;	// ワーク
	short *tmpPtDest2;	// ワーク２

	// サーフェスの横幅记忆
	surfacePitch = (ddsd.lPitch >> 1);

	ptDest = (short *)(ddsd.lpSurface) + (ww+1) * surfacePitch + 1;
	tmpPtDest  = ptDest;
	tmpPtDest2 = ptDest;
	for( i = 0; i < hh; i++ ){
		ptDest = tmpPtDest;
		for( j = 0; j < ww; j++ ){

			color2 = (unsigned char *)&autoMap[j];
			color = lpDraw->PalTbl[ color2[2] ][PPLR]|lpDraw->PalTbl[ color2[1] ][PPLG]|lpDraw->PalTbl[ color2[0] ][PPLB];
			if( color == DEF_COLORKEY ) color = 1;

			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
			ptDest -= (surfacePitch-2);
		}
		tmpPtDest += (surfacePitch+2);
		autoMap += w;
	}

	// ＰＣキャラの表示（マップ中央で白黒点灭）
	if( pcFlush ){
		color = 0xFFFF;
	}else{
		color = 1;
	}
	if( pcFlushTime+1000 <= GetTickCount() ){
		pcFlushTime = GetTickCount();
		pcFlush++;
		pcFlush &= 1;
	}

	ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
	*(ptDest-1)				= color;
	*(ptDest)				= color;
	*(ptDest+1)				= color;
	*(ptDest-surfacePitch)	= color;

	// アクセスするサーフェスをアンロックする
	if( lpAutoMapSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_28, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	x += xx-1;
	y += yy-ww-1;

	if (x<0){ rc.left -= x;	x = 0; }
	if ( x+(rc.right-rc.left)>DEF_APPSIZEX ) rc.right = rc.left + (DEF_APPSIZEX-x);

	if (y<0){ rc.top -= y;	y = 0; }
	if ( y+(rc.bottom-rc.top)>DEF_APPSIZEY ) rc.bottom = rc.top + (DEF_APPSIZEY-y);

	// バックバッファに书き込み
	lpDraw->lpBACKBUFFER->BltFast( x, y, lpAutoMapSurface, &rc, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );

	return;
}

//---------------------------------------------------------------------------//
// 新マップ用オートマッピングを描画                                          //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned short *autoMap;		マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping_Sugi24( int x, int y, unsigned long *autoMap, int w, int h, int zoom )
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	COL24BIT color;
	unsigned char *color2;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;
	RECT rc;
	DDBLTFX ddbf={0};
	ddbf.dwSize=sizeof(ddbf);

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 作业用サーフェースの初期化
	ddbf.dwFillColor = DEF_COLORKEY;

	rc.left = 0;
	rc.right = ( (hh+ww)<<1 )+2;
	rc.top = 0;
	rc.bottom = hh+ww+2;
	lpAutoMapSurface->Blt( &rc, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbf );

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpAutoMapSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_29, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	COL24BIT *ptDest;				// 転送先のポインタ
	COL24BIT *tmpPtDest;			// ワーク
	COL24BIT *tmpPtDest2;			// ワーク２

	// サーフェスの横幅记忆
	surfacePitch = (ddsd.lPitch/3);

	ptDest = (COL24BIT *)(ddsd.lpSurface) + (ww+1) * surfacePitch + 1;
	tmpPtDest  = ptDest;
	tmpPtDest2 = ptDest;
	for( i = 0; i < hh; i++ ){
		ptDest = tmpPtDest;
		for( j = 0; j < ww; j++ ){

			color2 = (unsigned char *)&autoMap[j];

			color.r = color2[2];
			color.g = color2[1];
			color.b = color2[0];
			if( (color.r|color.g|color.b) == 0 ) color.b = 1;

			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
			ptDest -= (surfacePitch-2);
		}
		tmpPtDest += (surfacePitch+2);
		autoMap += w;
	}

	// ＰＣキャラの表示（マップ中央で白黒点灭）
	if( pcFlush ){
		color.r = 0xFF;
		color.g = 0xFF;
		color.b = 0xFF;
	}else{
		color.r = 0;
		color.g = 0;
		color.b = 1;
	}
	if( pcFlushTime+1000 <= GetTickCount() ){
		pcFlushTime = GetTickCount();
		pcFlush++;
		pcFlush &= 1;
	}

	ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
	*(ptDest-1)				= color;
	*(ptDest)				= color;
	*(ptDest+1)				= color;
	*(ptDest-surfacePitch)	= color;

	// アクセスするサーフェスをアンロックする
	if( lpAutoMapSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_30, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	x += xx-1;
	y += yy-ww-1;

	if (x<0){ rc.left -= x;	x = 0; }
	if ( x+(rc.right-rc.left)>DEF_APPSIZEX ) rc.right = rc.left + (DEF_APPSIZEX-x);

	if (y<0){ rc.top -= y;	y = 0; }
	if ( y+(rc.bottom-rc.top)>DEF_APPSIZEY ) rc.bottom = rc.top + (DEF_APPSIZEY-y);

	// バックバッファに书き込み
	lpDraw->lpBACKBUFFER->BltFast( x, y, lpAutoMapSurface, &rc, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );

	return;
}

//---------------------------------------------------------------------------//
// 新マップ用オートマッピングを描画                                          //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned short *autoMap;		マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping_Sugi32( int x, int y, unsigned long *autoMap, int w, int h, int zoom )
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	unsigned long color;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;
	RECT rc;
	DDBLTFX ddbf={0};
	ddbf.dwSize=sizeof(ddbf);

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 作业用サーフェースの初期化
	ddbf.dwFillColor = DEF_COLORKEY;

	rc.left = 0;
	rc.right = ( (hh+ww)<<1 )+2;
	rc.top = 0;
	rc.bottom = hh+ww+2;
	lpAutoMapSurface->Blt( &rc, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbf );

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpAutoMapSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_31, "确认", MB_OK | MB_ICONSTOP );]
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );]
#endif
		return; 
	}

	unsigned long *ptDest;				// 転送先のポインタ
	unsigned long *tmpPtDest;			// ワーク
	unsigned long *tmpPtDest2;			// ワーク２

	// サーフェスの横幅记忆
	surfacePitch = (ddsd.lPitch>>2);

	ptDest = (unsigned long *)(ddsd.lpSurface) + (ww+1) * surfacePitch + 1;
	tmpPtDest  = ptDest;
	tmpPtDest2 = ptDest;
	for( i = 0; i < hh; i++ ){
		ptDest = tmpPtDest;
		for( j = 0; j < ww; j++ ){


			color = autoMap[j];
			if( color == DEF_COLORKEY ) color = 1;

			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
			ptDest -= (surfacePitch-2);
		}
		tmpPtDest += (surfacePitch+2);
		autoMap += w;
	}

	// ＰＣキャラの表示（マップ中央で白黒点灭）
	if( pcFlush ){
		color = 0x00FFFFFF;
	}else{
		color = 1;
	}
	if( pcFlushTime+1000 <= GetTickCount() ){
		pcFlushTime = GetTickCount();
		pcFlush++;
		pcFlush &= 1;
	}

	ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
	*(ptDest-1)				= color;
	*(ptDest)				= color;
	*(ptDest+1)				= color;
	*(ptDest-surfacePitch)	= color;

	// アクセスするサーフェスをアンロックする
	if( lpAutoMapSurface->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_32, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return; 
	}

	x += xx-1;
	y += yy-ww-1;

	if (x<0){ rc.left -= x;	x = 0; }
	if ( x+(rc.right-rc.left)>DEF_APPSIZEX ) rc.right = rc.left + (DEF_APPSIZEX-x);

	if (y<0){ rc.top -= y;	y = 0; }
	if ( y+(rc.bottom-rc.top)>DEF_APPSIZEY ) rc.bottom = rc.top + (DEF_APPSIZEY-y);

	// バックバッファに书き込み
	lpDraw->lpBACKBUFFER->BltFast( x, y, lpAutoMapSurface, &rc, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );

	return;
}

//---------------------------------------------------------------------------//
// 新マップ用オートマッピングを描画                                          //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned short *autoMap;		マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大（现在未对应）
void DrawAutoMapping_Sugi( int x, int y, unsigned long *autoMap, int w, int h, int zoom )
{
	switch( displayBpp ){
	case 16:	DrawAutoMapping_Sugi16( x, y, autoMap, w, h, zoom );	break;
	case 24:	DrawAutoMapping_Sugi24( x, y, autoMap, w, h, zoom );	break;
	case 32:	DrawAutoMapping_Sugi32( x, y, autoMap, w, h, zoom );	break;
	}
}

#endif

#ifdef PUK2_DIFFPAL_SURFACE
BOOL DrawBitmapToSurface_Def( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo )
#else
void DrawBitmapToSurface_Def( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo )
#endif
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	long *ppx4;						// 転送用变数(32BPP用)
	COL24BIT *ppx3;					// 転送用变数(24BPP用)
	short *ppx2;					// 転送用变数(16BPP用)
	unsigned char *pDst;			// 転送先のポインタ
	unsigned char *pSrc;			// 転送元のポインタ
	int srfPitch;					// サーフェスの横幅记忆
	int bmpWidth;					// ＢＭＰの横幅记忆
	int i,j;						// ループカウンタ

	// サーフェスポインタがNULLの时返回
#ifdef PUK2_DIFFPAL_SURFACE
	if ( lpSrf == NULL ) return FALSE;
#else
	if ( lpSrf == NULL ) return;
#endif

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( ddsd ) );
	ddsd.dwSize = sizeof( ddsd );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if ( lpSrf->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_33, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
#ifdef PUK2_DIFFPAL_SURFACE
		return FALSE;
#else
		return;
#endif
	}	

	// サーフェスの左上隅のアドレス
	pDst = (unsigned char *)ddsd.lpSurface;

	// ＢＭＰファイルから読み込むとき
	if (pBmpInfo){
		// 転送元のアドレス
		pSrc = (unsigned char *)pBmpInfo + BmpOffBits +
			 offY * pBmpInfo->bmiHeader.biWidth + offX;

		// ＢＭＰの横幅记忆
		bmpWidth = pBmpInfo->bmiHeader.biWidth;
	}
	// Realbin から読み込むとき
	else{
		// 転送元のアドレス
		pSrc = (unsigned char *)(pRealBinBits + offY * RealBinWidth + offX);

		// ＢＭＰの横幅记忆
		bmpWidth = RealBinWidth;
	}

	// サーフェスの横幅记忆
	srfPitch = ddsd.lPitch;

	// 使用するパレットが与えられていない时(ＸＧデフォルトのパレット使用)
	switch(displayBpp){
	case 8:		// ８ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			memcpy( pDst, pSrc, sizX );

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	case 16:		// １６ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			ppx2 = (short *)pDst;

			for(j=0;j<sizX;j++) ppx2[j] = highColorPalette[ pSrc[j] ];

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	case 24:		// ２４ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			ppx3 = (COL24BIT *)pDst;

			for(j=0;j<sizX;j++) ppx3[j] = Color24Palette[ pSrc[j] ];

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	case 32:		// ３２ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			ppx4 = (long *)pDst;

			for(j=0;j<sizX;j++) ppx4[j] = fullColorPalette[ pSrc[j] ];

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	}

#ifdef PUK2_DIFFPAL_SURFACE
	return TRUE;
#else
	// アクセスするサーフェスをアンロックする
	if( lpSrf->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_34, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
		return;
	}	
#endif
}

#ifdef PUK2_DIFFPAL_SURFACE
BOOL DrawBitmapToSurface_Pal( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo, unsigned long *pPal )
#else
void DrawBitmapToSurface_Pal( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo, unsigned long *pPal )
#endif
{
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
	unsigned long *ppx4;			// 転送用变数(32BPP用)
	struct PAL24BPP *ppx3;			// 転送用变数(24BPP用)
	unsigned short *ppx2;			// 転送用变数(16BPP用)
	unsigned char *pDst;			// 転送先のポインタ
	unsigned char *pSrc;			// 転送元のポインタ
	int srfPitch;					// サーフェスの横幅记忆
	int bmpWidth;					// ＢＭＰの横幅记忆
	int i,j;						// ループカウンタ
	union PALPOINTER Palp = {pPal};

	// サーフェスポインタがNULLの时返回
#ifdef PUK2_DIFFPAL_SURFACE
	if ( lpSrf == NULL ) return FALSE;
#else
	if ( lpSrf == NULL ) return;
#endif

	// 构造体の初期化
	ZeroMemory( &ddsd, sizeof( ddsd ) );
	ddsd.dwSize = sizeof( ddsd );

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if ( lpSrf->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_35, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
#endif
#ifdef PUK2_DIFFPAL_SURFACE
		return FALSE;
#else
		return; 
#endif
	}	

	// サーフェスの左上隅のアドレス
	pDst = (unsigned char *)ddsd.lpSurface;

	// ＢＭＰファイルから読み込むとき
	if (pBmpInfo){
		// 転送元のアドレス
		pSrc = (unsigned char *)pBmpInfo + BmpOffBits +
			 offY * pBmpInfo->bmiHeader.biWidth + offX;

		// ＢＭＰの横幅记忆
		bmpWidth = pBmpInfo->bmiHeader.biWidth;
	}
	// Realbin から読み込むとき
	else{
		// 転送元のアドレス
		pSrc = (unsigned char *)(pRealBinBits + offY * RealBinWidth + offX);

		// ＢＭＰの横幅记忆
		bmpWidth = RealBinWidth;
	}

	// サーフェスの横幅记忆
	srfPitch = ddsd.lPitch;

	// 使用するパレットが与えられていない时(ＸＧデフォルトのパレット使用)
	switch(displayBpp){
	case 8:		// ８ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			memcpy( pDst, pSrc, sizX );

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	case 16:		// １６ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			ppx2 = (unsigned short *)pDst;

			for(j=0;j<sizX;j++) ppx2[j] = Palp.p2[ pSrc[j] ];

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	case 24:		// ２４ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			ppx3 = (PAL24BPP *)pDst;

			for(j=0;j<sizX;j++) ppx3[j] = Palp.p3[ pSrc[j] ];

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	case 32:		// ３２ＢＰＰ
		// 縦のサイズ分ループする
		for(i=0;i<sizY;i++){
			ppx4 = (unsigned long *)pDst;

			for(j=0;j<sizX;j++) ppx4[j] = Palp.p4[ pSrc[j] ];

			// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
			pSrc -= bmpWidth;
			// サーフェスの横幅分アドレスを进める
			pDst += srfPitch;
		}
		break;
	}
#ifdef PUK2_DIFFPAL_SURFACE
	return TRUE;
#endif
}

#ifdef PUK2_DIFFPAL_SURFACE
BOOL DrawBitmapToSurface4( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo, unsigned long *pPal )
#else
void DrawBitmapToSurface4( LPDIRECTDRAWSURFACE7 lpSrf,
	 int offX, int offY, int sizX, int sizY, LPBITMAPINFO pBmpInfo, unsigned long *pPal )
#endif
{
	// パレットが渡されているなら
	if (pPal){
#ifdef PUK2_DIFFPAL_SURFACE
		if ( !DrawBitmapToSurface_Pal( lpSrf, offX, offY, sizX, sizY, pBmpInfo, pPal ) ){
			return FALSE;
		}
#else
		DrawBitmapToSurface_Pal( lpSrf, offX, offY, sizX, sizY, pBmpInfo, pPal );
#endif
	}
	// パレットが渡されていないなら
	else{
#ifdef PUK2_DIFFPAL_SURFACE
		if ( !DrawBitmapToSurface_Def( lpSrf, offX, offY, sizX, sizY, pBmpInfo ) ){
			return FALSE;
		}
#else
		DrawBitmapToSurface_Def( lpSrf, offX, offY, sizX, sizY, pBmpInfo );
#endif
	}

	// アクセスするサーフェスをアンロックする
	if( lpSrf->Unlock( NULL ) != DD_OK ){
#ifdef PUK3_ERRORMESSAGE_NUM
		//MessageBox( hWnd, ERRMSG_36, "确认", MB_OK | MB_ICONSTOP );
#else
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
#endif
#ifdef PUK2_DIFFPAL_SURFACE
		return FALSE;
#else
		return;
#endif
	}	
#ifdef PUK2_DIFFPAL_SURFACE
		return TRUE;
#endif
}

#ifdef PUK2_DEBUG_DRAW

void _Debug_Draw_DrawFuncLine( int num )
{
	D3_BLTPOINT a[2];

	a[0].x=(float)(DebugDrawData[num].x1),	a[0].y=(float)(DebugDrawData[num].y1);
	a[0].z=0,	a[0].rhw=1,	a[0].clr=DebugDrawData[num].rgba.rgba,	a[0].tu=0,	a[0].tv=0;

	a[1].x=(float)(DebugDrawData[num].x2),	a[1].y=(float)(DebugDrawData[num].y2);
	a[1].z=0,	a[1].rhw=1,	a[1].clr=DebugDrawData[num].rgba.rgba,	a[1].tu=0,	a[1].tv=0;

	if ( (DebugDrawData[num].rgba.rgba&0xff000000)==0xff000000 ){
		lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_ONE );
		lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_ZERO );
	}else{
		lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_SRCBLEND, D3DBLEND_SRCALPHA );
		lpDraw->lpD3DEVICE->SetRenderState( D3DRENDERSTATE_DESTBLEND, D3DBLEND_INVSRCALPHA );
	}

	// テクスチャを选择する
	lpDraw->lpD3DEVICE->SetTexture( useTextureStage, NULL );

	lpDraw->lpD3DEVICE->DrawPrimitive( D3DPT_LINELIST, D3DFVF_BLTPOINT, a, 2, 0 );
}

void _Debug_Draw_DrawFuncRect( int num )
{
	// 不透明なら
	if ( (DebugDrawData[num].rgba.rgba&0xff000000) == 0xff000000 ){
		RECT rect={
			DebugDrawData[num].x1, DebugDrawData[num].y1,
			DebugDrawData[num].x2, DebugDrawData[num].y2,
		};
		DDBLTFX ddbf={0};
		ddbf.dwSize=sizeof(ddbf);
		if ( displayBpp == 16 ){
			ddbf.dwFillColor =
				 lpDraw->PalTbl[ DebugDrawData[num].rgba.r ][PPLR] |
				 lpDraw->PalTbl[ DebugDrawData[num].rgba.g ][PPLG] |
				 lpDraw->PalTbl[ DebugDrawData[num].rgba.b ][PPLB];
		}else{
			ddbf.dwFillColor = DebugDrawData[num].rgba.rgba & 0xff000000;
		}
		// バックサーフェスへ転送
		lpDraw->lpBACKBUFFER->Blt( &rect, NULL, NULL, DDBLT_COLORFILL|DDBLT_WAIT, &ddbf );
	}else{
		RectAlphaBlend( DebugDrawData[num].x1, DebugDrawData[num].x2,
			 DebugDrawData[num].y1, DebugDrawData[num].y2,
			 DebugDrawData[num].rgba.rgba, 0 );
	}

}

#endif
