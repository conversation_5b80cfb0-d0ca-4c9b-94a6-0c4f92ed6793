﻿#ifndef _LOADREALBIN_H_
#define _LOADREALBIN_H_

#include <stdio.h>
//#include "common.h"

typedef unsigned char MOJI;
typedef unsigned char U1;
typedef          char S1;
typedef unsigned short U2;
typedef          short S2;
typedef unsigned long U4;
typedef          long S4;
typedef float  F4;
typedef double F8;

#ifdef PUK2
BOOL realGetImage(int graphicNo, unsigned char **bmpdata, int *width, int *height, int *palsiz=NULL);
#else
int realGetImage(int graphicNo, unsigned char **bmpdata, int *width, int *height);
#endif
int Linear(void);

#ifdef MULTI_GRABIN
int initRealbinFileOpen(char* realbinfilename[], char* addrbinfilename[]);
void initAutoMapColor( char* [] );
#else
int initRealbinFileOpen(char *realbinfilename, char *addrbinfilename);
void initAutoMapColor( char * );
#endif
void cleanupRealbin(void);

void makeAutoMapColor( void );
#ifdef MULTI_GRABIN
int writeAutoMapColor( char *, char *[] );
int readAutoMapColor( char *, char *[] );
#else
int writeAutoMapColor( char *, char * );
int readAutoMapColor( char *, char * );
#endif

extern unsigned char autoMapColorTbl[];

//extern	PBITMAPINFO         PBiInfo;
//extern	PBYTE               PBits;
//extern	BITMAPFILEHEADER    header;

//グラフィックナンバーより立ち位置をもらう
//	戾值 -1:失败 or 以外成功
int realGetPos(U4 GraphicNo , S2 *x , S2 *y);

//グラフィックナンバーからグラフィック幅、高さを返す
//	戾值 -1:失败 or 以外成功
int realGetWH(U4 GraphicNo , S2 *w , S2 *h);

//グラフィックナンバーより当たりサイズを求める。
//	戾值 -1:失败 or 以外成功
int realGetHitPoints(U4 GraphicNo , S2 *HitX , S2 *HitY);

//グラフィックナンバーより当たり判定を求める。
//	戾值 -1:失败 or 以外成功
int realGetHitFlag(U4 GraphicNo , S2 *Hit);

//グラフィックナンバーより描画优先顺位决定法を取り出す。
//	戾值 -1:失败 or 以外成功
BOOL realGetPrioType(U4 GraphicNo , S2 *prioType);

//グラフィックナンバーより高さの有无を求める。
//	戾值 -1:失败 or 以外成功
int realGetHeightFlag(U4 GraphicNo , S2 *Height);

//引数にビットマップファイル番号(ファイル名の数字のみ)を与えてグラフィックナンバー（通し番号）を返す
//	戾值 -1:失败 or 以外成功
int realGetNo( U4 CharAction , U4 *GraphicNo );

//グラフィックナンバーより环境音を
//	戾り值　:　0 设定ない
int realGetSoundEffect(U4 GraphicNo);

//グラフィックナンバーより步行音を
//	戾り值　:　0 设定ない
int realGetWalkSoundEffect(U4 GraphicNo);

//引数にグラフィックナンバー（通し番号）を与えてビットマップファイブ番号を返す
int realGetBitmapNo( int num );


///////////////////////////////////////////////////////////////////////////////

typedef struct {
	unsigned char atari_x,atari_y;	//大きさ
	unsigned short hit;				//步けるか
//	short height;				//高さあるか
//	short broken;				//坏れるか
	//short indamage;				//入HP受伤
	//short outdamage;			//出受伤
//	short inpoison;				//入毒
//	short innumb;				//入しびれ
//	short inquiet;				//入沈黙
//	short instone;				//入石化
//	short indark;				//入暗闇
//	short inconfuse;			//入混乱
//	short outpoison;			//出毒
//	short outnumb;				//出痹れ
//	short outquiet;				//出沈黙
//	short outstone;				//出石化
//	short outdark;				//出暗闇
//	short outconfuse;			//出混乱
//	short effect1;				//エフェクト1（画面に见える时音、画面效果など）
//	short effect2;				//エフェクト2（入ったとき音、画面效果など）
//	unsigned short damy_a;
//	unsigned short damy_b;
//	unsigned short damy_c;
	unsigned int bmpnumber;		//画像番号
} MAP_ATTR;

struct ADRNBIN{
	unsigned long	bitmapno;
	unsigned long	adder;
	unsigned long	size;

	short	xoffset;
	short	yoffset;
	unsigned short width;
	unsigned short height;

#ifdef MULTI_GRABIN
	short	BinMode;
#endif

	MAP_ATTR attr;

};

extern ADRNBIN *adrnbuff;

#define TILEPARTS_EX_START	220000	// 扩张タイル番号开始
#define TILEPARTS_EX_END	265535	// 扩张タイル番号終了
#define TILEPARTS_EX_OFFSET	200000	// 扩张タイル番号のOFFセット

#define TILEPARTS_NORMAL_START	1000	// タイル番号开始
#define TILEPARTS_NORMAL_END	19999	// タイル番号終了

#endif /*_LOADREALBIN_H_*/
