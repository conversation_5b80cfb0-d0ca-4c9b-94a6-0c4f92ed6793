﻿//メニュー＞システム

#ifndef _MENUSHOP_H_
#define _MENUSHOP_H_

//====================================//
//		アイテムを扱うショップ		  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSwitchItemShopClose( int no, unsigned int flag );

BOOL MenuSwitchItemShopOK( int no, unsigned int flag );
BOOL MenuSwitchItemShopCancel( int no, unsigned int flag );
BOOL MenuSwitchItemShopAll( int no, unsigned int flag );

BOOL MenuSwitchItemShopScrollUp( int no, unsigned int flag );
BOOL MenuSwitchItemShopScrollDown( int no, unsigned int flag );
BOOL MenuSwitchItemShopScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchItemShopScrollRight( int no, unsigned int flag );
BOOL MenuSwitchItemShopScrollWheel( int no, unsigned int flag );

BOOL MenuSwitchItemShopNumMinus( int no, unsigned int flag );
BOOL MenuSwitchItemShopNumPlus( int no, unsigned int flag );

BOOL MenuSwitchItemShopItemBoxBuy( int no, unsigned int flag );
BOOL MenuSwitchItemShopItemBoxCount( int no, unsigned int flag );
BOOL MenuSwitchItemShopItemBoxSell( int no, unsigned int flag );

BOOL MenuSwitchItemShopItemPanel( int no, unsigned int flag );
BOOL MenuSwitchItemShopShopPanel( int no, unsigned int flag );

BOOL MenuSwitchItemShopInfo( int no, unsigned int flag );
BOOL MenuSwitchItemShopInfo2( int no, unsigned int flag );


BOOL MenuWindowItemShopBf( int mouse );
BOOL MenuWindowItemShopAf( int mouse );
BOOL closeItemShopWindow();


GRAPHIC_SWITCH MenuWindowItemShopGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_ShopWindow,0,0,0,0,0xFFFFFFFF},			// ベース
	{GID_ShopPanel,0,0,0,0,0xFFFFFFFF},				// ショップパネル
	{GID_ShopItemPanel,0,0,0,0,0xFFFFFFFF},			// アイテムパネル

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},				// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(下ボタン)

	{GID_MinusButtonOn,0,0,0,0,0xFFFFFFFF},			// マイナスボタン１
	{GID_MinusButtonOn,0,0,0,0,0xFFFFFFFF},			// マイナスボタン２
	{GID_MinusButtonOn,0,0,0,0,0xFFFFFFFF},			// マイナスボタン３
	{GID_MinusButtonOn,0,0,0,0,0xFFFFFFFF},			// マイナスボタン４
	{GID_MinusButtonOn,0,0,0,0,0xFFFFFFFF},			// マイナスボタン５

	{GID_PlusButtonOn,0,0,0,0,0xFFFFFFFF},			// プラスボタン１
	{GID_PlusButtonOn,0,0,0,0,0xFFFFFFFF},			// プラスボタン２
	{GID_PlusButtonOn,0,0,0,0,0xFFFFFFFF},			// プラスボタン３
	{GID_PlusButtonOn,0,0,0,0,0xFFFFFFFF},			// プラスボタン４
	{GID_PlusButtonOn,0,0,0,0,0xFFFFFFFF},			// プラスボタン５

	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},			// ＯＫ
	{GID_BigCancelButtonOn,0,0,0,0,0xFFFFFFFF},		// Ｃａｎｃｅｌ

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(右ボタン)
};

TEXT_SWITCH MenuWindowItemShopText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"0000000000"},				// お金            //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"00"},						// 个数                  //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_11,"？？？？"},				// 店名                 //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,"？？？？"},				// 品名                  //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,ML_STRING(416, "＊现在无法装备")},	// 警告
};

BUTTON_SWITCH MenuWindowItemShopButton[]={
	{0,0,0},
};

ACTION_SWITCH_INIT MenuWindowItemShopAction[]={
	{100000},
};

char ShopNum_PCGold[11];
char ShopNum_HaveNum[11];
char ShopNum_BuyNum[11];
char ShopNum_Price[5][11];
char ShopNum_Number[5][11];

NUMBER_SWITCH MenuWindowShopNum[] = {
	{ ShopNum_PCGold, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_HaveNum, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_BuyNum, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Price[0], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Price[1], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Price[2], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Price[3], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Price[4], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Number[0], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Number[1], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Number[2], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Number[3], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ShopNum_Number[4], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
};

// スイッチ
static SWITCH_DATA ItemShopSwitch[] = {

{ SWITCH_NONE,	   0,  0, 640,383, TRUE, NULL, MenuSwitchItemShopInfo },								// 确认ウィンドウ

{ SWITCH_GRAPHIC,528,  9,  11, 11, TRUE, &MenuWindowItemShopGraph[0], MenuSwitchItemShopClose },		// クローズボタン

{ SWITCH_GRAPHIC,294,333,  66, 17, TRUE, &MenuWindowItemShopGraph[17], MenuSwitchItemShopOK },			// ＯＫ
{ SWITCH_GRAPHIC,467,333,  66, 17, TRUE, &MenuWindowItemShopGraph[18], MenuSwitchItemShopCancel },		// Ｃａｎｃｅｌ
{ SWITCH_GRAPHIC,371,333,  25, 19, TRUE, &MenuWindowItemShopGraph[18], MenuSwitchItemShopAll },			// Ａｌｌ

{ SWITCH_TEXT,	 412, 44,   0, 14, TRUE, &MenuWindowItemShopText[2], MenuSwitchNone },					// 店名

{ SWITCH_NUMBER, 124, 37,   0,  0, TRUE, &MenuWindowShopNum[1], MenuSwitchNone },					// 现在の所持数

{ SWITCH_NUMBER, 255, 37,   0,  0, TRUE, &MenuWindowShopNum[2], MenuSwitchNone },					// 购入予定额

{ SWITCH_NUMBER, 389, 30,   0,  0, TRUE, &MenuWindowShopNum[0], MenuSwitchNone },					// 手持ち金

{ SWITCH_ACTION, 508,105,   0,  0, TRUE, &MenuWindowItemShopAction[0], MenuSwitchNone },				// 店员画像

{ SWITCH_GRAPHIC,267, 85,   0, 14, TRUE, &MenuWindowItemShopGraph[4], MenuSwitchNone },					// スクロールバー(つまみ)
{ SWITCH_BUTTON, 267, 85,  11,243, TRUE, &MenuWindowItemShopButton[0], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,267, 75,  11, 11, TRUE, &MenuWindowItemShopGraph[5], MenuSwitchItemShopScrollUp },		// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,267,327,  11, 11, TRUE, &MenuWindowItemShopGraph[6], MenuSwitchItemShopScrollDown },	// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,226, 55,  18, 18, TRUE, &MenuWindowItemShopGraph[19], MenuSwitchItemShopScrollLeft },	// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,246, 55,  18, 18, TRUE, &MenuWindowItemShopGraph[20], MenuSwitchItemShopScrollRight },	// スクロールバー(右ボタン)
{ SWITCH_NONE,	  15, 28, 274,325, TRUE, NULL, MenuSwitchItemShopScrollWheel },							// マウスホイール判定

{ SWITCH_TEXT,	 154, 90,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名１
{ SWITCH_TEXT,	 154,145,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名２
{ SWITCH_TEXT,	 154,200,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名３
{ SWITCH_TEXT,	 154,255,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名４
{ SWITCH_TEXT,	 154,310,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名５

{ SWITCH_TEXT,	 154,102,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名１-２行目
{ SWITCH_TEXT,	 154,157,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名２-２行目
{ SWITCH_TEXT,	 154,212,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名３-２行目
{ SWITCH_TEXT,	 154,267,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名４-２行目
{ SWITCH_TEXT,	 154,322,   0, 14, TRUE, &MenuWindowItemShopText[3], MenuSwitchNone },					// 品名５-２行目

{ SWITCH_TEXT,	 154,120,   0, 14, TRUE, &MenuWindowItemShopText[4], MenuSwitchNone },					// 警告１
{ SWITCH_TEXT,	 154,175,   0, 14, TRUE, &MenuWindowItemShopText[4], MenuSwitchNone },					// 警告２
{ SWITCH_TEXT,	 154,230,   0, 14, TRUE, &MenuWindowItemShopText[4], MenuSwitchNone },					// 警告３
{ SWITCH_TEXT,	 154,285,   0, 14, TRUE, &MenuWindowItemShopText[4], MenuSwitchNone },					// 警告４
{ SWITCH_TEXT,	 154,340,   0, 14, TRUE, &MenuWindowItemShopText[4], MenuSwitchNone },					// 警告５

{ SWITCH_NUMBER, 129,108,   0,  0, TRUE, &MenuWindowShopNum[3], MenuSwitchNone },						// 单价１
{ SWITCH_NUMBER, 129,163,   0,  0, TRUE, &MenuWindowShopNum[4], MenuSwitchNone },						// 单价２
{ SWITCH_NUMBER, 129,218,   0,  0, TRUE, &MenuWindowShopNum[5], MenuSwitchNone },						// 单价３
{ SWITCH_NUMBER, 129,273,   0,  0, TRUE, &MenuWindowShopNum[6], MenuSwitchNone },						// 单价４
{ SWITCH_NUMBER, 129,328,   0,  0, TRUE, &MenuWindowShopNum[7], MenuSwitchNone },						// 单价５

{ SWITCH_NUMBER, 132, 90,   0,  0, TRUE, &MenuWindowShopNum[8], MenuSwitchNone },						// 购入个数１
{ SWITCH_NUMBER, 132,145,   0,  0, TRUE, &MenuWindowShopNum[9], MenuSwitchNone },						// 购入个数２
{ SWITCH_NUMBER, 132,200,   0,  0, TRUE, &MenuWindowShopNum[10], MenuSwitchNone },						// 购入个数３
{ SWITCH_NUMBER, 132,255,   0,  0, TRUE, &MenuWindowShopNum[11], MenuSwitchNone },						// 购入个数４
{ SWITCH_NUMBER, 132,310,   0,  0, TRUE, &MenuWindowShopNum[12], MenuSwitchNone },						// 购入个数５

{ SWITCH_GRAPHIC, 80, 90,  11, 11, TRUE, &MenuWindowItemShopGraph[ 7], MenuSwitchItemShopNumMinus },	// マイナスボタン１
{ SWITCH_GRAPHIC, 80,145,  11, 11, TRUE, &MenuWindowItemShopGraph[ 8], MenuSwitchItemShopNumMinus },	// マイナスボタン２
{ SWITCH_GRAPHIC, 80,200,  11, 11, TRUE, &MenuWindowItemShopGraph[ 9], MenuSwitchItemShopNumMinus },	// マイナスボタン３
{ SWITCH_GRAPHIC, 80,255,  11, 11, TRUE, &MenuWindowItemShopGraph[10], MenuSwitchItemShopNumMinus },	// マイナスボタン４
{ SWITCH_GRAPHIC, 80,310,  11, 11, TRUE, &MenuWindowItemShopGraph[11], MenuSwitchItemShopNumMinus },	// マイナスボタン５

{ SWITCH_GRAPHIC, 95, 90,  11, 11, TRUE, &MenuWindowItemShopGraph[12], MenuSwitchItemShopNumPlus },		// プラスボタン１
{ SWITCH_GRAPHIC, 95,145,  11, 11, TRUE, &MenuWindowItemShopGraph[13], MenuSwitchItemShopNumPlus },		// プラスボタン２
{ SWITCH_GRAPHIC, 95,200,  11, 11, TRUE, &MenuWindowItemShopGraph[14], MenuSwitchItemShopNumPlus },		// プラスボタン３
{ SWITCH_GRAPHIC, 95,255,  11, 11, TRUE, &MenuWindowItemShopGraph[15], MenuSwitchItemShopNumPlus },		// プラスボタン４
{ SWITCH_GRAPHIC, 95,310,  11, 11, TRUE, &MenuWindowItemShopGraph[16], MenuSwitchItemShopNumPlus },		// プラスボタン５

{ SWITCH_NONE,	  27, 73,  48, 48, TRUE, NULL, MenuSwitchItemShopItemBoxBuy },							// アイテムボックス
{ SWITCH_NONE,	  27,128,  48, 48, TRUE, NULL, MenuSwitchItemShopItemBoxBuy },							// アイテムボックス
{ SWITCH_NONE,	  27,183,  48, 48, TRUE, NULL, MenuSwitchItemShopItemBoxBuy },							// アイテムボックス
{ SWITCH_NONE,	  27,238,  48, 48, TRUE, NULL, MenuSwitchItemShopItemBoxBuy },							// アイテムボックス
{ SWITCH_NONE,	  27,293,  48, 48, TRUE, NULL, MenuSwitchItemShopItemBoxBuy },							// アイテムボックス

{ SWITCH_GRAPHIC, 27, 70,  40, 11, TRUE, &MenuWindowItemShopGraph[3], MenuSwitchNone },					// アイテムパネル
{ SWITCH_GRAPHIC, 27,125,  40, 11, TRUE, &MenuWindowItemShopGraph[3], MenuSwitchNone },					// アイテムパネル
{ SWITCH_GRAPHIC, 27,180,  40, 11, TRUE, &MenuWindowItemShopGraph[3], MenuSwitchNone },					// アイテムパネル
{ SWITCH_GRAPHIC, 27,235,  40, 11, TRUE, &MenuWindowItemShopGraph[3], MenuSwitchNone },					// アイテムパネル
{ SWITCH_GRAPHIC, 27,290,  40, 11, TRUE, &MenuWindowItemShopGraph[3], MenuSwitchNone },					// アイテムパネル

{ SWITCH_NONE,	 290,125, 252,202, TRUE, NULL, MenuSwitchItemShopItemPanel },							// アイテム
{ SWITCH_GRAPHIC, 15, 28, 274,325, TRUE, &MenuWindowItemShopGraph[2], MenuSwitchItemShopShopPanel },	// ショップパネル
{ SWITCH_GRAPHIC,  0,  0,  40, 11, TRUE, &MenuWindowItemShopGraph[1], MenuSwitchNone },					// ベース

{ SWITCH_NONE,	  552, 5,  18, 80, TRUE, NULL, MenuSwitchDelMouse },									// ドラッグ用

};

enum{
	EnumItemShopInfo,

	EnumGraphItemShopClose,		

	EnumGraphItemShopOK,
	EnumGraphItemShopCancel,
	EnumGraphItemShopAll,

	EnumGraphItemShopTitle,

	EnumGraphItemShopMyPrice,
	EnumGraphItemShopAllPrice,
	EnumGraphItemShopMoney,

	EnumGraphItemShopChara,

	EnumGraphItemShopScroll,
	EnumBtItemShopScroll,
	EnumGraphItemShopScrollUp,
	EnumGraphItemShopScrollDown,
	EnumGraphItemShopScrollLeft,
	EnumGraphItemShopScrollRight,
	EnumGraphItemShopScrollWheel,

	EnumGraphItemShopName1,
	EnumGraphItemShopName2,
	EnumGraphItemShopName3,
	EnumGraphItemShopName4,
	EnumGraphItemShopName5,

	EnumGraphItemShopName1_2,
	EnumGraphItemShopName2_2,
	EnumGraphItemShopName3_2,
	EnumGraphItemShopName4_2,
	EnumGraphItemShopName5_2,

	EnumGraphItemShopWarning1,
	EnumGraphItemShopWarning2,
	EnumGraphItemShopWarning3,
	EnumGraphItemShopWarning4,
	EnumGraphItemShopWarning5,

	EnumGraphItemShopPrice1,
	EnumGraphItemShopPrice2,
	EnumGraphItemShopPrice3,
	EnumGraphItemShopPrice4,
	EnumGraphItemShopPrice5,

	EnumGraphItemShopNumber1,
	EnumGraphItemShopNumber2,
	EnumGraphItemShopNumber3,
	EnumGraphItemShopNumber4,
	EnumGraphItemShopNumber5,

	EnumGraphItemShopMinus1,
	EnumGraphItemShopMinus2,
	EnumGraphItemShopMinus3,
	EnumGraphItemShopMinus4,
	EnumGraphItemShopMinus5,

	EnumGraphItemShopPlus1,
	EnumGraphItemShopPlus2,
	EnumGraphItemShopPlus3,
	EnumGraphItemShopPlus4,
	EnumGraphItemShopPlus5,

	EnumGraphItemShopItemBox1,
	EnumGraphItemShopItemBox2,
	EnumGraphItemShopItemBox3,
	EnumGraphItemShopItemBox4,
	EnumGraphItemShopItemBox5,

	EnumGraphItemShopItemPanel1,
	EnumGraphItemShopItemPanel2,
	EnumGraphItemShopItemPanel3,
	EnumGraphItemShopItemPanel4,
	EnumGraphItemShopItemPanel5,

	EnumGraphItemShopMyItemPanel,
	EnumGraphItemShopPanel,
	EnumGraphItemShopBase,

	EnumItemShopDragBack,

	EnumItemShopEnd,
};


const WINDOW_DATA WindowDataMenuItemShop = {
 0,															// メニューウィンドウ
     4,   10, 30,549,366, 0x80010101,  EnumItemShopEnd,  ItemShopSwitch, MenuWindowItemShopBf,MenuWindowItemShopAf,closeItemShopWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateItemShop={
	2,
	 16,  0,533, 27,
	552,  5, 18, 80,
};

ACTION *openItemShopBuyWindow( char *data );
ACTION *openItemShopSellWindow( char *data );
ACTION *openItemTradeWindow( char *data );


//====================================//
//		スキルを扱うショップ		  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSwitchSkillShopClose( int no, unsigned int flag );

BOOL MenuSwitchSkillShopOK( int no, unsigned int flag );
BOOL MenuSwitchSkillShopCancel( int no, unsigned int flag );

BOOL MenuSwitchSkillShopScrollUp( int no, unsigned int flag );
BOOL MenuSwitchSkillShopScrollDown( int no, unsigned int flag );
BOOL MenuSwitchSkillShopScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchSkillShopScrollRight( int no, unsigned int flag );
BOOL MenuSwitchSkillShopScrollWheel( int no, unsigned int flag );

BOOL MenuSwitchSkillShopPlayerFogetPanel( int no, unsigned int flag );
BOOL MenuSwitchSkillShopPetSelectPanel( int no, unsigned int flag );
BOOL MenuSwitchSkillShopPosSelectPanel( int no, unsigned int flag );

BOOL MenuSwitchSkillShopSellPanel( int no, unsigned int flag );
BOOL MenuSwitchSkillShopSellPanel_CantSelect( int no, unsigned int flag );

BOOL MenuSwitchSkillShopInfo( int no, unsigned int flag );

BOOL MenuWindowSkillShopBf( int mouse );
BOOL MenuWindowSkillShopAf( int mouse );
BOOL closeSkillShopWindow();


GRAPHIC_SWITCH MenuWindowSkillShopGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_SkillShopWindow,0,0,0,0,0xFFFFFFFF},		// ベース
	{GID_SkillShopPanel,0,0,0,0,0xFFFFFFFF},		// スキルショップパネル

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},				// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(下ボタン)

	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},			// ＯＫ
	{GID_BigCancelButtonOn,0,0,0,0,0xFFFFFFFF},		// Ｃａｎｃｅｌ

	{GID_SkillShopScrollBase,0,0,0,0,0xFFFFFFFF},	// スクロールバー(ベース)

	{GID_SkillSlotPanelOn,0,0,0,0,0xFFFFFFFF},		// スキルパネル

	{GID_SkillShopGoldPanel,0,0,0,0,0xFFFFFFFF},	// 贩卖スキルパネル

	{GID_SkillRecipePanelROn,0,0,0,0,0xFFFFFFFF},	// 贩卖スキル名パネル

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},			// スクロールバー(右ボタン)
};

TEXT_SWITCH MenuWindowSkillShopText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,"0000000000"},					// お金           //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_11,"？？？？"},						// 店名               //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"？？？？"},						// スキル名              //MLHIDE
	{FONT_PAL_BLACK|FONT_PAL_NOSHADOW,FONT_KIND_SMALL,"？？？？"},		// 说明    //MLHIDE
	{FONT_PAL_BLACK|FONT_PAL_NOSHADOW,FONT_KIND_SIZE_11,"0000000000"},	// スロット数 //MLHIDE
};

BUTTON_SWITCH MenuWindowSkillShopButton[]={
	{0,0,0},
};

ACTION_SWITCH_INIT MenuWindowSkillShopAction[]={
	{100000},
};

char SkillShopNum_PCGold[11];
char SkillShopNum_Price[11];
char SkillShopNum_SellFp[5][11];
char SkillShopNum_SellPrice[5][11];
char SkillShopNum_SkillFp[10][11];
char SkillShopNum_SkillSlot[10][11];

NUMBER_SWITCH MenuWindowSkillShopNum[] = {
	{ SkillShopNum_PCGold, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_Price, FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },

	{ SkillShopNum_SellFp[0], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellFp[1], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellFp[2], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellFp[3], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellFp[4], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },

	{ SkillShopNum_SellPrice[0], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellPrice[1], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellPrice[2], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellPrice[3], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SellPrice[4], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },

	{ SkillShopNum_SkillFp[0], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[1], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[2], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[3], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[4], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[5], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[6], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[7], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[8], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillFp[9], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },

	{ SkillShopNum_SkillSlot[0], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[1], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[2], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[3], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[4], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[5], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[6], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[7], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[8], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ SkillShopNum_SkillSlot[9], FONT_PAL_WHITE, G_NUM_SIZE__9, G_NUM_FLAG_RIGHT_JUSTIFIED },
};

// スイッチ
static SWITCH_DATA SkillShopSwitch[] = {

{ SWITCH_NONE,	   0,  0, 640,383, TRUE, NULL, MenuSwitchSkillShopInfo },								// 确认ウィンドウ

{ SWITCH_GRAPHIC,528,  9,  11, 11, TRUE, &MenuWindowSkillShopGraph[0], MenuSwitchSkillShopClose },		// クローズボタン

{ SWITCH_GRAPHIC,203,326,  66, 17, TRUE, &MenuWindowSkillShopGraph[6], MenuSwitchSkillShopOK },			// ＯＫ
{ SWITCH_GRAPHIC,467,326,  66, 17, TRUE, &MenuWindowSkillShopGraph[7], MenuSwitchSkillShopCancel },		// Ｃａｎｃｅｌ

{ SWITCH_TEXT,	 412, 44,   0, 14, TRUE, &MenuWindowSkillShopText[1], MenuSwitchNone },					// 店名

{ SWITCH_TEXT,	  78, 52,   0, 14, TRUE, &MenuWindowSkillShopText[4], MenuSwitchNone },					// スロット数

{ SWITCH_NUMBER, 262, 37,   0, 14, TRUE, &MenuWindowSkillShopNum[1], MenuSwitchNone },					// 购入予定额

{ SWITCH_NUMBER, 389, 30,   0, 14, TRUE, &MenuWindowSkillShopNum[0], MenuSwitchNone },					// 手持ち金

{ SWITCH_ACTION, 508,105,   0,  0, TRUE, &MenuWindowSkillShopAction[0], MenuSwitchNone },				// 店员画像

{ SWITCH_GRAPHIC,502,146,   0, 14, TRUE, &MenuWindowSkillShopGraph[3], MenuSwitchNone },				// スクロールバー(つまみ)
{ SWITCH_BUTTON, 502,146,  11,137, TRUE, &MenuWindowSkillShopButton[0], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,502,136,  11, 11, TRUE, &MenuWindowSkillShopGraph[4], MenuSwitchSkillShopScrollUp },	// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,502,282,  11, 11, TRUE, &MenuWindowSkillShopGraph[5], MenuSwitchSkillShopScrollDown },	// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,472,300,  18, 18, TRUE, &MenuWindowSkillShopGraph[12], MenuSwitchSkillShopScrollLeft },// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,498,300,  18, 18, TRUE, &MenuWindowSkillShopGraph[13], MenuSwitchSkillShopScrollRight },// スクロールバー(右ボタン)
{ SWITCH_NONE,	 286, 27, 273,280, TRUE, NULL, MenuSwitchSkillShopScrollWheel },							// 自分侧パネルマウスホイール判定
{ SWITCH_GRAPHIC,499,132,  11, 11, TRUE, &MenuWindowSkillShopGraph[8], MenuSwitchNone },				// スクロールバー(ベース)

{ SWITCH_GRAPHIC,269, 85,   0, 14, TRUE, &MenuWindowSkillShopGraph[3], MenuSwitchNone },				// スクロールバー(つまみ)
{ SWITCH_BUTTON, 269, 85,  11,244, TRUE, &MenuWindowSkillShopButton[0], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,269, 75,  11, 11, TRUE, &MenuWindowSkillShopGraph[4], MenuSwitchSkillShopScrollUp },	// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,269,327,  11, 11, TRUE, &MenuWindowSkillShopGraph[5], MenuSwitchSkillShopScrollDown },	// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,226, 55,  18, 18, TRUE, &MenuWindowSkillShopGraph[12], MenuSwitchSkillShopScrollLeft },// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,246, 55,  18, 18, TRUE, &MenuWindowSkillShopGraph[13], MenuSwitchSkillShopScrollRight },// スクロールバー(右ボタン)
{ SWITCH_NONE,	  17, 28, 274,325, TRUE, NULL, MenuSwitchSkillShopScrollWheel },							// 自分侧パネルマウスホイール判定

{ SWITCH_TEXT,	  33, 87,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの名称

{ SWITCH_TEXT,	  33,127,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明１
{ SWITCH_TEXT,	  33,147,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明２
{ SWITCH_TEXT,	  33,167,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明３
{ SWITCH_TEXT,	  33,187,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明４
{ SWITCH_TEXT,	  33,207,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明５
{ SWITCH_TEXT,	  33,227,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明６
{ SWITCH_TEXT,	  33,247,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明７
{ SWITCH_TEXT,	  33,267,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// スキルの说明８

{ SWITCH_TEXT,	  33,287,   0,  0, TRUE, &MenuWindowSkillShopText[3], MenuSwitchNone },					// 必要スロット数

{ SWITCH_TEXT,	 296,135,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名１
{ SWITCH_TEXT,	 296,151,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名２
{ SWITCH_TEXT,	 296,167,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名３
{ SWITCH_TEXT,	 296,183,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名４
{ SWITCH_TEXT,	 296,199,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名５
{ SWITCH_TEXT,	 296,215,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名６
{ SWITCH_TEXT,	 296,231,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名７
{ SWITCH_TEXT,	 296,247,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名８
{ SWITCH_TEXT,	 296,263,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名９
{ SWITCH_TEXT,	 296,279,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名10
#ifdef PUK3_PETSKILLSHOPEX
{ SWITCH_TEXT,	 296+150,135,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },				// 种族１
{ SWITCH_TEXT,	 296+150,151,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },				// 种族２
{ SWITCH_TEXT,	 296+150,167,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },				// 种族３
{ SWITCH_TEXT,	 296+150,183,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },				// 种族４
{ SWITCH_TEXT,	 296+150,199,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },				// 种族５
#endif

{ SWITCH_NUMBER, 454,136,   0,  0, TRUE, &MenuWindowSkillShopNum[12], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,152,   0,  0, TRUE, &MenuWindowSkillShopNum[13], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,168,   0,  0, TRUE, &MenuWindowSkillShopNum[14], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,184,   0,  0, TRUE, &MenuWindowSkillShopNum[15], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,200,   0,  0, TRUE, &MenuWindowSkillShopNum[16], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,216,   0,  0, TRUE, &MenuWindowSkillShopNum[17], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,232,   0,  0, TRUE, &MenuWindowSkillShopNum[18], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,248,   0,  0, TRUE, &MenuWindowSkillShopNum[19], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,264,   0,  0, TRUE, &MenuWindowSkillShopNum[20], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 454,280,   0,  0, TRUE, &MenuWindowSkillShopNum[21], MenuSwitchNone },					// スキル等级１

{ SWITCH_NUMBER, 492,136,   0,  0, TRUE, &MenuWindowSkillShopNum[22], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,152,   0,  0, TRUE, &MenuWindowSkillShopNum[23], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,168,   0,  0, TRUE, &MenuWindowSkillShopNum[24], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,184,   0,  0, TRUE, &MenuWindowSkillShopNum[25], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,200,   0,  0, TRUE, &MenuWindowSkillShopNum[26], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,216,   0,  0, TRUE, &MenuWindowSkillShopNum[27], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,232,   0,  0, TRUE, &MenuWindowSkillShopNum[28], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,248,   0,  0, TRUE, &MenuWindowSkillShopNum[29], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,264,   0,  0, TRUE, &MenuWindowSkillShopNum[30], MenuSwitchNone },					// スキル等级１
{ SWITCH_NUMBER, 492,280,   0,  0, TRUE, &MenuWindowSkillShopNum[31], MenuSwitchNone },					// スキル等级１

{ SWITCH_GRAPHIC,290,133, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル１
{ SWITCH_GRAPHIC,290,149, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル２
{ SWITCH_GRAPHIC,290,165, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル３
{ SWITCH_GRAPHIC,290,181, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル４
{ SWITCH_GRAPHIC,290,197, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル５
{ SWITCH_GRAPHIC,290,213, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル６
{ SWITCH_GRAPHIC,290,229, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル７
{ SWITCH_GRAPHIC,290,245, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル８
{ SWITCH_GRAPHIC,290,261, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル９
{ SWITCH_GRAPHIC,290,277, 207, 16, TRUE, &MenuWindowSkillShopGraph[9], MenuSwitchNone },				// スキルパネル10

{ SWITCH_TEXT,	  41, 82,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名１
{ SWITCH_TEXT,	  41,137,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名２
{ SWITCH_TEXT,	  41,192,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名３
{ SWITCH_TEXT,	  41,247,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名４
{ SWITCH_TEXT,	  41,302,   0,  0, TRUE, &MenuWindowSkillShopText[2], MenuSwitchNone },					// スキル名５

{ SWITCH_NUMBER, 239, 83,   0, 14, TRUE, &MenuWindowSkillShopNum[2], MenuSwitchNone },					// 魔力１
{ SWITCH_NUMBER, 239,138,   0, 14, TRUE, &MenuWindowSkillShopNum[3], MenuSwitchNone },					// 魔力２
{ SWITCH_NUMBER, 239,193,   0, 14, TRUE, &MenuWindowSkillShopNum[4], MenuSwitchNone },					// 魔力３
{ SWITCH_NUMBER, 239,248,   0, 14, TRUE, &MenuWindowSkillShopNum[5], MenuSwitchNone },					// 魔力４
{ SWITCH_NUMBER, 239,303,   0, 14, TRUE, &MenuWindowSkillShopNum[6], MenuSwitchNone },					// 魔力５

{ SWITCH_NUMBER, 233,108,   0, 14, TRUE, &MenuWindowSkillShopNum[7], MenuSwitchNone },					// 价格１
{ SWITCH_NUMBER, 233,163,   0, 14, TRUE, &MenuWindowSkillShopNum[8], MenuSwitchNone },					// 价格２
{ SWITCH_NUMBER, 233,218,   0, 14, TRUE, &MenuWindowSkillShopNum[9], MenuSwitchNone },					// 价格３
{ SWITCH_NUMBER, 233,273,   0, 14, TRUE, &MenuWindowSkillShopNum[10], MenuSwitchNone },					// 价格４
{ SWITCH_NUMBER, 233,328,   0, 14, TRUE, &MenuWindowSkillShopNum[11], MenuSwitchNone },					// 价格５

{ SWITCH_GRAPHIC, 37, 80, 207, 16, TRUE, &MenuWindowSkillShopGraph[11], MenuSwitchNone },				// スキルパネル１
{ SWITCH_GRAPHIC, 37,135, 207, 16, TRUE, &MenuWindowSkillShopGraph[11], MenuSwitchNone },				// スキルパネル２
{ SWITCH_GRAPHIC, 37,190, 207, 16, TRUE, &MenuWindowSkillShopGraph[11], MenuSwitchNone },				// スキルパネル３
{ SWITCH_GRAPHIC, 37,245, 207, 16, TRUE, &MenuWindowSkillShopGraph[11], MenuSwitchNone },				// スキルパネル４
{ SWITCH_GRAPHIC, 37,300, 207, 16, TRUE, &MenuWindowSkillShopGraph[11], MenuSwitchNone },				// スキルパネル５

{ SWITCH_GRAPHIC, 27, 70, 241, 55, TRUE, &MenuWindowSkillShopGraph[10], MenuSwitchNone },				// スキルお金パネル１
{ SWITCH_GRAPHIC, 27,125, 241, 55, TRUE, &MenuWindowSkillShopGraph[10], MenuSwitchNone },				// スキルお金パネル２
{ SWITCH_GRAPHIC, 27,180, 241, 55, TRUE, &MenuWindowSkillShopGraph[10], MenuSwitchNone },				// スキルお金パネル３
{ SWITCH_GRAPHIC, 27,235, 241, 55, TRUE, &MenuWindowSkillShopGraph[10], MenuSwitchNone },				// スキルお金パネル４
{ SWITCH_GRAPHIC, 27,290, 241, 55, TRUE, &MenuWindowSkillShopGraph[10], MenuSwitchNone },				// スキルお金パネル５

{ SWITCH_GRAPHIC, 17, 28, 274,325, TRUE, &MenuWindowSkillShopGraph[2], MenuSwitchNone },				// スキルショップパネル
{ SWITCH_GRAPHIC,  0,  0,  40, 11, TRUE, &MenuWindowSkillShopGraph[1], MenuSwitchNone },				// ベース

{ SWITCH_NONE,	  552, 5,  18, 80, TRUE, NULL, MenuSwitchDelMouse },									// ドラッグ用

};

enum{
	EnumSkillShopInfo,

	EnumGraphSkillShopClose,

	EnumGraphSkillShopOK,
	EnumGraphSkillShopCancel,

	EnumGraphSkillShopTitle,
	EnumGraphSkillShopSlotNum,
	EnumGraphSkillShopPrice,
	EnumGraphSkillShopMoney,

	EnumGraphSkillShopChara,

	EnumGraphSkillShopScroll,
	EnumBtSkillShopScroll,
	EnumGraphSkillShopScrollUp,
	EnumGraphSkillShopScrollDown,
	EnumGraphSkillShopScrollLeft,
	EnumGraphSkillShopScrollRight,
	EnumGraphSkillShopScrollWheel,
	EnumGraphSkillShopScrollBase,

	EnumGraphSkillShopScroll2,
	EnumBtSkillShopScroll2,
	EnumGraphSkillShopScrollUp2,
	EnumGraphSkillShopScrollDown2,
	EnumGraphSkillShopScrollLeft2,
	EnumGraphSkillShopScrollRight2,
	EnumGraphSkillShopScrollWheel2,

	EnumGraphSkillShopName,

	EnumGraphSkillShopText1,
	EnumGraphSkillShopText2,
	EnumGraphSkillShopText3,
	EnumGraphSkillShopText4,
	EnumGraphSkillShopText5,
	EnumGraphSkillShopText6,
	EnumGraphSkillShopText7,
	EnumGraphSkillShopText8,

	EnumGraphSkillShopNeedSlot,

	EnumGraphSkillShopSkillName1,
	EnumGraphSkillShopSkillName2,
	EnumGraphSkillShopSkillName3,
	EnumGraphSkillShopSkillName4,
	EnumGraphSkillShopSkillName5,
	EnumGraphSkillShopSkillName6,
	EnumGraphSkillShopSkillName7,
	EnumGraphSkillShopSkillName8,
	EnumGraphSkillShopSkillName9,
	EnumGraphSkillShopSkillName10,
#ifdef PUK3_PETSKILLSHOPEX
	EnumGraphSkillShopSkillTribe1,
	EnumGraphSkillShopSkillTribe2,
	EnumGraphSkillShopSkillTribe3,
	EnumGraphSkillShopSkillTribe4,
	EnumGraphSkillShopSkillTribe5,
#endif

	EnumGraphSkillShopSkillLv1,
	EnumGraphSkillShopSkillLv2,
	EnumGraphSkillShopSkillLv3,
	EnumGraphSkillShopSkillLv4,
	EnumGraphSkillShopSkillLv5,
	EnumGraphSkillShopSkillLv6,
	EnumGraphSkillShopSkillLv7,
	EnumGraphSkillShopSkillLv8,
	EnumGraphSkillShopSkillLv9,
	EnumGraphSkillShopSkillLv10,

	EnumGraphSkillShopSkillSlot1,
	EnumGraphSkillShopSkillSlot2,
	EnumGraphSkillShopSkillSlot3,
	EnumGraphSkillShopSkillSlot4,
	EnumGraphSkillShopSkillSlot5,
	EnumGraphSkillShopSkillSlot6,
	EnumGraphSkillShopSkillSlot7,
	EnumGraphSkillShopSkillSlot8,
	EnumGraphSkillShopSkillSlot9,
	EnumGraphSkillShopSkillSlot10,

	EnumGraphSkillShopSkillPanel1,
	EnumGraphSkillShopSkillPanel2,
	EnumGraphSkillShopSkillPanel3,
	EnumGraphSkillShopSkillPanel4,
	EnumGraphSkillShopSkillPanel5,
	EnumGraphSkillShopSkillPanel6,
	EnumGraphSkillShopSkillPanel7,
	EnumGraphSkillShopSkillPanel8,
	EnumGraphSkillShopSkillPanel9,
	EnumGraphSkillShopSkillPanel10,

	EnumGraphSkillShopSkillSellName1,
	EnumGraphSkillShopSkillSellName2,
	EnumGraphSkillShopSkillSellName3,
	EnumGraphSkillShopSkillSellName4,
	EnumGraphSkillShopSkillSellName5,

	EnumGraphSkillShopSkillSellFp1,
	EnumGraphSkillShopSkillSellFp2,
	EnumGraphSkillShopSkillSellFp3,
	EnumGraphSkillShopSkillSellFp4,
	EnumGraphSkillShopSkillSellFp5,

	EnumGraphSkillShopSkillSellPrice1,
	EnumGraphSkillShopSkillSellPrice2,
	EnumGraphSkillShopSkillSellPrice3,
	EnumGraphSkillShopSkillSellPrice4,
	EnumGraphSkillShopSkillSellPrice5,

	EnumGraphSkillShopSkillSellPanel1,
	EnumGraphSkillShopSkillSellPanel2,
	EnumGraphSkillShopSkillSellPanel3,
	EnumGraphSkillShopSkillSellPanel4,
	EnumGraphSkillShopSkillSellPanel5,

	EnumGraphSkillShopSkillSellGold1,
	EnumGraphSkillShopSkillSellGold2,
	EnumGraphSkillShopSkillSellGold3,
	EnumGraphSkillShopSkillSellGold4,
	EnumGraphSkillShopSkillSellGold5,

	EnumGraphSkillShopPanel,
	EnumGraphSkillShopBase,

	EnumSkillShopDragBack,

	EnumSkillShopEnd,
};


const WINDOW_DATA WindowDataMenuSkillShop = {
 0,															// メニューウィンドウ
     4,   10, 30,549,366, 0x80010101,  EnumSkillShopEnd,  SkillShopSwitch, MenuWindowSkillShopBf,MenuWindowSkillShopAf,closeSkillShopWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateSkillShop={
	2,
	 16,  0,533, 27,
	552,  5, 18, 80,
};

ACTION *openPlayerBuySkillShopWindow( char *data );
ACTION *openPlayerForgetSkillShopWindow( char *data );
ACTION *openPlayerPetSkillShopWindow( char *data );
void changePetSkillWindowPetSelect( char *data );
void changePetSkillWindowPosSelect( char *data );
#ifdef PUK3_PETSKILLSHOPEX
	ACTION *openPlayerPetSkillShopWindowEx( char *data );
	void changePetSkillWindowPetSelectEx( char *data );
#endif

#endif
