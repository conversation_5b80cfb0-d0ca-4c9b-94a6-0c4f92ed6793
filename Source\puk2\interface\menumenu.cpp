﻿//メニュー本体

// ＩＭＥ入力モード画像
static long ImeModeGraphicNum[] = { 
	GID_ChatMode00,0,0,
	GID_ChatMode01,0,0,0,0,
	GID_ChatMode02,
	GID_ChatMode03,0,
	GID_ChatMode04,0,0,0,0,
	GID_ChatMode05,0,0,
	GID_ChatMode06,0,0,0,0,
	GID_ChatMode07,
	GID_ChatMode08,0,
	GID_ChatMode09,

								};
// ＩＭＥ入力モード画像（Ｃａｐｓ用）
static long ImeModeGraphicNumCaps[] = { 
	GID_ChatMode10,0,0,
	GID_ChatMode11,0,0,0,0,
	GID_ChatMode12,
	GID_ChatMode13,0,
	GID_ChatMode14,0,0,0,0,
	GID_ChatMode15,0,0,
	GID_ChatMode16,0,0,0,0,
	GID_ChatMode17,
	GID_ChatMode18,0,
	GID_ChatMode19,
								};


BO<PERSON> closeMenuWindowSystem();

int CheckOnMenuWindow;

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowMenu( int mouse )
{

	BOOL imeOpenFlag;
	BOOL capsLockFlag;
	char keyBoardState[ 256 ];		// キーボードの状态
	TEXT_SWITCH *Text;

	//OneLineInfo关系处理

	if(CheckOnMenuWindow==1){
		//スイッチＯＮ（表示にする）
		wI->sw[EnumGraphMenuStatus].Enabled=TRUE;
		wI->sw[EnumGraphMenuSkill].Enabled=TRUE;
		wI->sw[EnumGraphMenuItem].Enabled=TRUE;
		wI->sw[EnumGraphMenuMonster].Enabled=TRUE;
		wI->sw[EnumGraphMenuAddress].Enabled=TRUE;
		wI->sw[EnumGraphMenuAlbum].Enabled=TRUE;
		wI->sw[EnumGraphMenuSystem].Enabled=TRUE;
		wI->sw[EnumGraphMenuBase00].Enabled=TRUE;

		wI->sw[EnumTextMenuOneLineInfo].Enabled=FALSE;
		wI->sw[EnumTextMenuOneLineInfoSub].Enabled=FALSE;
	}else if(OneLineInfoStr[ 0 ] != '\0'){
		//INFOがあったら
		if(strstr(OneLineInfoStr,ITEM_HANKO_STRING) != NULL){
			//ハンコ
			if(OneLineInfoStr[0]) {
				int len = strlen(OneLineInfoStr);
				OneLineInfoStr[len -5] = 0;
			}
			OneLineInfoColor=FONT_PAL_AQUA;
		} else if(strstr(OneLineInfoStr,ITEM_INCUSE_STRING) != NULL){
			if(OneLineInfoStr[0]) {
			//刻印
				int len = strlen(OneLineInfoStr);
				OneLineInfoStr[len -5] = 0;
			}
			OneLineInfoColor=FONT_PAL_GREEN;
		}	
		Text=(TEXT_SWITCH *)wI->sw[EnumTextMenuOneLineInfo].Switch;
		strcpy(Text->text,OneLineInfoStr);
		Text->color=OneLineInfoColor;

		//INFOSubがあったら
		if( strlen( OneLineInfoStrSub ) > 0 ){
			wI->sw[EnumTextMenuOneLineInfoSub].Enabled=TRUE;
		}
		Text=(TEXT_SWITCH *)wI->sw[EnumTextMenuOneLineInfoSub].Switch;
		strcpy(Text->text,OneLineInfoStrSub);
		Text->color=OneLineInfoStrSubColor;

		//スイッチＯＦＦ（非表示にする）
		wI->sw[EnumGraphMenuStatus].Enabled=FALSE;
		wI->sw[EnumGraphMenuSkill].Enabled=FALSE;
		wI->sw[EnumGraphMenuItem].Enabled=FALSE;
		wI->sw[EnumGraphMenuMonster].Enabled=FALSE;
		wI->sw[EnumGraphMenuAddress].Enabled=FALSE;
		wI->sw[EnumGraphMenuAlbum].Enabled=FALSE;
		wI->sw[EnumGraphMenuSystem].Enabled=FALSE;
		wI->sw[EnumGraphMenuBase00].Enabled=FALSE;

		wI->sw[EnumTextMenuOneLineInfo].Enabled=TRUE;
	}else{
		//スイッチＯＮ（表示にする）
		wI->sw[EnumGraphMenuStatus].Enabled=TRUE;
		wI->sw[EnumGraphMenuSkill].Enabled=TRUE;
		wI->sw[EnumGraphMenuItem].Enabled=TRUE;
		wI->sw[EnumGraphMenuMonster].Enabled=TRUE;
		wI->sw[EnumGraphMenuAddress].Enabled=TRUE;
		wI->sw[EnumGraphMenuAlbum].Enabled=TRUE;
		wI->sw[EnumGraphMenuSystem].Enabled=TRUE;
		wI->sw[EnumGraphMenuBase00].Enabled=TRUE;

		wI->sw[EnumTextMenuOneLineInfo].Enabled=FALSE;
		wI->sw[EnumTextMenuOneLineInfoSub].Enabled=FALSE;
	}

/*
	if(OneLineInfoStr[ 0 ] != '\0'){
		//INFOがあったら
		if(strstr(OneLineInfoStr,ITEM_HANKO_STRING) != NULL){
			//ハンコ
			if(OneLineInfoStr[0]) {
				int len = strlen(OneLineInfoStr);
				OneLineInfoStr[len -5] = 0;
			}
			OneLineInfoColor=FONT_PAL_AQUA;
		} else if(strstr(OneLineInfoStr,ITEM_INCUSE_STRING) != NULL){
			if(OneLineInfoStr[0]) {
			//刻印
				int len = strlen(OneLineInfoStr);
				OneLineInfoStr[len -5] = 0;
			}
			OneLineInfoColor=FONT_PAL_GREEN;
		}	
		Text=(TEXT_SWITCH *)wI->sw[EnumTextMenuOneLineInfo].Switch;
		strcpy(Text->text,OneLineInfoStr);
		Text->clolor=OneLineInfoColor;

		//INFOSubがあったら
		if( strlen( OneLineInfoStrSub ) > 0 ){
			wI->sw[EnumTextMenuOneLineInfoSub].Enabled=TRUE;
		}
		Text=(TEXT_SWITCH *)wI->sw[EnumTextMenuOneLineInfoSub].Switch;
		strcpy(Text->text,OneLineInfoStrSub);
		Text->clolor=OneLineInfoStrSubColor;

		//スイッチＯＦＦ（非表示にする）
		wI->sw[EnumGraphMenuStatus].Enabled=FALSE;
		wI->sw[EnumGraphMenuSkill].Enabled=FALSE;
		wI->sw[EnumGraphMenuItem].Enabled=FALSE;
		wI->sw[EnumGraphMenuMonster].Enabled=FALSE;
		wI->sw[EnumGraphMenuAddress].Enabled=FALSE;
		wI->sw[EnumGraphMenuAlbum].Enabled=FALSE;
		wI->sw[EnumGraphMenuSystem].Enabled=FALSE;
		wI->sw[EnumGraphMenuBase00].Enabled=FALSE;

		wI->sw[EnumTextMenuOneLineInfo].Enabled=TRUE;
	}else{
		//INFOがなかったら
		//スイッチＯＮ（表示にする）
		wI->sw[EnumGraphMenuStatus].Enabled=TRUE;
		wI->sw[EnumGraphMenuSkill].Enabled=TRUE;
		wI->sw[EnumGraphMenuItem].Enabled=TRUE;
		wI->sw[EnumGraphMenuMonster].Enabled=TRUE;
		wI->sw[EnumGraphMenuAddress].Enabled=TRUE;
		wI->sw[EnumGraphMenuAlbum].Enabled=TRUE;
		wI->sw[EnumGraphMenuSystem].Enabled=TRUE;
		wI->sw[EnumGraphMenuBase00].Enabled=TRUE;

		wI->sw[EnumTextMenuOneLineInfo].Enabled=FALSE;
		wI->sw[EnumTextMenuOneLineInfoSub].Enabled=FALSE;
	}
*/

	// ＩＭＥの状态を记忆（　ＯＮ、ＯＦＦ　）
	imeOpenFlag = ImeOpenState();
	// capsLock の判定
	// キーボードの状态をとってくる（全てのキー）
	GetKeyboardState( ( BYTE *) keyBoardState );
	// capsLock 状态（　ＯＮ、ＯＦＦ　）
	capsLockFlag = keyBoardState[ VK_CAPITAL ];

	//入力モード表示
// ＩＭＥがＯＮの时
	if( imeOpenFlag ){
		// CapsLock の时
		if( capsLockFlag ){
			// 例外の时
			if( ImeInfo.conversion >= 28 ){
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuIMEInfo].Switch)->graNo=GID_ChatMode21;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuIMEInfo].Switch)->graNo=ImeModeGraphicNumCaps[ImeInfo.conversion];
			}
		}else{
			// 例外の时
			if( ImeInfo.conversion >= 28 ){
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuIMEInfo].Switch)->graNo=GID_ChatMode20;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuIMEInfo].Switch)->graNo=ImeModeGraphicNum[ImeInfo.conversion];
			}
		}
	}else{	
// ＩＭＥがＯＦＦの时
		// CapsLock の时
		if( capsLockFlag ){
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuIMEInfo].Switch)->graNo=GID_ChatMode21;
		}else{
				((GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuIMEInfo].Switch)->graNo=GID_ChatMode20;
		}
	}
	
	
	//OneLineInfoをここで初期化
	OneLineInfoStr[ 0 ] = '\0';
	OneLineInfoColor = FONT_PAL_WHITE;

	OneLineInfoStrSub[0]='\0';
	OneLineInfoStrSubColor=FONT_PAL_WHITE;

	return TRUE;
}

BOOL MenuWindowMenuDraw( int mouse )
{


	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
BOOL MenuSwitchMenuSwitch( int no, unsigned int flag ){

	GRAPHIC_SWITCH	*Graph;
	int x,y;
	BOOL ReturnFlag=FALSE;

	switch(no){

		//マウスチェック
		case EnumGraphMenuMouseOnCheck:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				CheckOnMenuWindow=1;
			}else{
				CheckOnMenuWindow=0;
			}
			//ここは必ずFALSE
			ReturnFlag=FALSE;

			break;

		//状态
		case EnumGraphMenuStatus:
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuStatus].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuStatusOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuStatusOff;
			}

			//ウインドウの有无からボタンの画像变更
			if(WindowFlag[MENU_WINDOW_STATUS].wininfo!=NULL ||
				WindowFlag[MENU_WINDOW_DETAIL].wininfo!=NULL ||
#ifdef PUK3_PROF
				WindowFlag[MENU_WINDOW_PROFILE].wininfo!=NULL ||
#endif
				WindowFlag[MENU_WINDOW_TITLE].wininfo!=NULL
				){
				//ウインドウ作成されてるので画像をＯＮに
				Graph->graNo=GID_MenuStatusOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_STATUS].wininfo==NULL &&
					WindowFlag[MENU_WINDOW_DETAIL].wininfo==NULL &&
#ifdef PUK3_PROF
					WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL &&
#endif
					WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL
				){
					//ないので作る
					openMenuWindow( MENU_WINDOW_STATUS, OPENMENUWINDOW_HIT, 0 );
					ReturnFlag=TRUE;
				}else{
					if(WindowFlag[MENU_WINDOW_STATUS].wininfo!=NULL){
						//あるので消す
						WindowFlag[MENU_WINDOW_STATUS].wininfo->flag |= WIN_INFO_DEL;
						x=WindowFlag[MENU_WINDOW_STATUS].wininfo->wx;
						y=WindowFlag[MENU_WINDOW_STATUS].wininfo->wy;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}

					if(WindowFlag[MENU_WINDOW_DETAIL].wininfo!=NULL){
						//あるので消す
						WindowFlag[MENU_WINDOW_DETAIL].wininfo->flag |= WIN_INFO_DEL;
						x=WindowFlag[MENU_WINDOW_DETAIL].wininfo->wx;
						y=WindowFlag[MENU_WINDOW_DETAIL].wininfo->wy;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}

					if(WindowFlag[MENU_WINDOW_TITLE].wininfo!=NULL){
						//あるので消す
						WindowFlag[MENU_WINDOW_TITLE].wininfo->flag |= WIN_INFO_DEL;
						x=WindowFlag[MENU_WINDOW_TITLE].wininfo->wx;
						y=WindowFlag[MENU_WINDOW_TITLE].wininfo->wy;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
#ifdef PUK3_PROF
					if(WindowFlag[MENU_WINDOW_PROFILE].wininfo!=NULL){
						//あるので消す
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->flag |= WIN_INFO_DEL;
						x=WindowFlag[MENU_WINDOW_PROFILE].wininfo->wx;
						y=WindowFlag[MENU_WINDOW_PROFILE].wininfo->wy;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
#endif
					//３つのウインドウの复归时の座标を合わせる
					WindowFlag[MENU_WINDOW_STATUS].wx=x;
					WindowFlag[MENU_WINDOW_STATUS].wy=y;
					WindowFlag[MENU_WINDOW_DETAIL].wx=x;
					WindowFlag[MENU_WINDOW_DETAIL].wy=y;
					WindowFlag[MENU_WINDOW_TITLE].wx=x;
					WindowFlag[MENU_WINDOW_TITLE].wy=y;
#ifdef PUK3_PROF
					WindowFlag[MENU_WINDOW_PROFILE].wx=x;
					WindowFlag[MENU_WINDOW_PROFILE].wy=y;
#endif
				}

				ReturnFlag=TRUE;
			}
			break;		

		//スキル
		case EnumGraphMenuSkill:
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuSkill].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuSkillOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuSkillOff;
			}
			
			if( ProcNo != PROC_BATTLE ){
				//ウインドウの有无からボタンの画像变更
				if(WindowFlag[MENU_WINDOW_SKILL].wininfo!=NULL){
					//ウインドウ作成されてるので画像をＯＮに
					Graph->graNo=GID_MenuSkillOn;	
				}
			}else{
				switch( BattleTaskSkillButtonStatus() ){
				case 0:	Graph->graNo=GID_MenuSkillOn;	break;
				case 1:	Graph->graNo=GID_MenuSkillOff;	break;
				case 2:	Graph->graNo=GID_MenuSkillOver;	break;
				}
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_SKILL].wininfo==NULL){
						//ないので作る
						openFeildSkillWindow();
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskSkillButton();
				ReturnFlag=TRUE;
			}
			break;

		//アイテム
		case EnumGraphMenuItem:
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuItem].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuItemOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuItemOff;
			}

			if( ProcNo != PROC_BATTLE ){
				//ウインドウの有无からボタンの画像变更
				if(WindowFlag[MENU_WINDOW_ITEM].wininfo!=NULL){
					//ウインドウ作成されてるので画像をＯＮに
					Graph->graNo=GID_MenuItemOn;	
				}
			}else{
				switch( BattleTaskItemButtonStatus() ){
				case 0:	Graph->graNo=GID_MenuItemOn;	break;
				case 1:	Graph->graNo=GID_MenuItemOff;	break;
				case 2:	Graph->graNo=GID_MenuItemOver;	break;
				}
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_ITEM].wininfo==NULL){
						//ないので作る
						openMenuWindow( MENU_WINDOW_ITEM, OPENMENUWINDOW_HIT, 0 );
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_ITEM].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskItemButton();
				ReturnFlag=TRUE;
			}
			break;

		//モンスター
		case EnumGraphMenuMonster:
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuMonster].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuMonsterOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuMonsterOff;
			}

			if( ProcNo != PROC_BATTLE ){
				//ウインドウの有无からボタンの画像变更
				if(WindowFlag[MENU_WINDOW_MONSTER].wininfo!=NULL){
					//ウインドウ作成されてるので画像をＯＮに
					Graph->graNo=GID_MenuMonsterOn;	
				}
			}else{
				switch( BattleTaskMonsterButtonStatus() ){
				case 0:	Graph->graNo=GID_MenuMonsterOn;		break;
				case 1:	Graph->graNo=GID_MenuMonsterOff;	break;
				case 2:	Graph->graNo=GID_MenuMonsterOver;	break;
				}
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_MONSTER].wininfo==NULL){
						//ないので作る
						openMenuWindow( MENU_WINDOW_MONSTER, OPENMENUWINDOW_HIT, 0 );
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_MONSTER].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskMonsterButton();
				ReturnFlag=TRUE;
			}
			break;

		//アドレス
		case EnumGraphMenuAddress:
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuAddress].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuAddressOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuAddressOff;
			}

			//ウインドウの有无からボタンの画像变更
			if(WindowFlag[MENU_WINDOW_ADDRESS].wininfo!=NULL){
				//ウインドウ作成されてるので画像をＯＮに
				Graph->graNo=GID_MenuAddressOn;	
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_ADDRESS].wininfo==NULL){
					//ないので作る
					openMenuWindow( MENU_WINDOW_ADDRESS, OPENMENUWINDOW_HIT, 0 );
				}else{
					//あるので消す
					WindowFlag[MENU_WINDOW_ADDRESS].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			break;

		//アルバム
		case EnumGraphMenuAlbum:
			//状态ボタン画像设定
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuAlbum].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuAlbumOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuAlbumOff;
			}

			//ウインドウの有无からボタンの画像变更
			if(WindowFlag[MENU_WINDOW_ALBUM].wininfo!=NULL){
				//ウインドウ作成されてるので画像をＯＮに
				Graph->graNo=GID_MenuAlbumOn;	
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_ALBUM].wininfo==NULL){
					//ないので作る
					openMenuWindow( MENU_WINDOW_ALBUM, OPENMENUWINDOW_HIT, 0 );
				}else{
					//あるので消す
					WindowFlag[MENU_WINDOW_ALBUM].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			break;

		//システム
		case EnumGraphMenuSystem:
			//状态ボタン画像设定
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMenuSystem].Switch;
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				//重なってる
				Graph->graNo=GID_MenuSystemOver;
				ReturnFlag=TRUE;
			}else{
				//重なってない
				Graph->graNo=GID_MenuSystemOff;
			}

			//ウインドウの有无からボタンの画像变更
			if(WindowFlag[MENU_WINDOW_SYSTEM].wininfo!=NULL){
				//ウインドウ作成されてるので画像をＯＮに
				Graph->graNo=GID_MenuSystemOn;	
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if(WindowFlag[MENU_WINDOW_SYSTEM].wininfo==NULL){
					//ないので作る
					openMenuWindow( MENU_WINDOW_SYSTEM, OPENMENUWINDOW_HIT, 0 );
				}else{
					//あるので消す
					WindowFlag[MENU_WINDOW_SYSTEM].wininfo->flag |= WIN_INFO_DEL;
				}
				ReturnFlag=TRUE;
			}
			break;
	}

	return ReturnFlag;

}

