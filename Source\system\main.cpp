﻿/**** SYSTEM INCLUDE ****/
#include "../systeminc/version.h"
#include "../systeminc/system.h"
#include <winnls32.h>
#include <ime.h>
#include <imm.h>
#include "../resource.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/netmain.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/map.h"
#include "../systeminc/main.h"
#include "../systeminc/process.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/direct3D.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/mouse.h"
#include "../systeminc/netproc.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/debugLogWin.h"
#include "../systeminc/pc.h"
#include "../systeminc/savedata.h"
#include "../systeminc/tool.h"
#include "../systeminc/filetbl.h"
#ifdef PUK2_NOSOUNDMUSIC
	#include "../systeminc/t_music.h"
#endif
#ifdef PUK3_MOUSECURSOR
	#include "../systeminc/anim_tbl.h"

	#define GID_PUK2_MOUSE_CURSOR	245400
	#define GID_PUK2_MOUSE_HAND		245403
	#define GID_PUK2_MOUSE_BATTLE	245404

	#define GID_PUK3_MOUSE_DIR_1	246689
	#define GID_PUK3_MOUSE_DIR_2	246690
	#define GID_PUK3_MOUSE_DIR_3	246691
	#define GID_PUK3_MOUSE_DIR_4	246692
	#define GID_PUK3_MOUSE_DIR_5	246693
	#define GID_PUK3_MOUSE_DIR_6	246694
	#define GID_PUK3_MOUSE_DIR_7	246695
	#define GID_PUK3_MOUSE_DIR_0	246696
#endif
#ifdef PUK3_ACCOUNT
#include "../puk3/account/account.h"
#include "../puk2/map/newmap.h"
#endif
#if defined(PUK3_SEGMENTATION_FAULT) || defined(PUK3_ALLOC)
#include "../systeminc/debug.h"
#endif

#ifdef WIN_SIZE_DEF
int DEF_APPSIZEX = 640;
int DEF_APPSIZEY = 480;

//按窗口分辨率对称偏移
int SymOffsetX = (DEF_APPSIZEX - 640) / 2;
int SymOffsetY = (DEF_APPSIZEY - 480) / 2;

//按窗口分辨率比例偏移
int ScaleOffsetX = ((DEF_APPSIZEX / 160) - 4) * 100;
int ScaleOffsetY = ((DEF_APPSIZEY / 120) - 4) * 100;
#else
//按窗口分辨率对称偏移
int SymOffsetX = 0;
int SymOffsetY = 0;

//按窗口分辨率比例偏移
int ScaleOffsetX = 0;
int ScaleOffsetY = 0;
#endif

//---------------------------------------------------------------------------//
// 概要 ：グローバル变数定义                                                 //
//---------------------------------------------------------------------------//
HINSTANCE	hInst;			// インスタンスハンドル定义
HWND hWnd;					// ウィンドウハンドル
int	CmdShow;				// WinMain关数の引数をグローバルにする
LPSTR CmdLine;				// WinMain关数のコマンドライン引数をグローバルにする
HANDLE hMutex;				// 同时起动チェックオブジェクトのハンドル

//BOOL WindowMode = TRUE;		// ウィンドウモード
BOOL WindowMode = FALSE;	// フルスクリーンモード
							//	TRUE  : WindowMode
							//	FALSE : FullScreen
BOOL NoDelay = TRUE;		// TCP_NODELAY を使うかどうか。add by ringo
BOOL WinSleepFlg = FALSE;		// ウインドウ作成时にスリープするかどうか。
BOOL FirstSleepFlg = TRUE;		// 最初のスリープか？
#ifdef PUK2_NOSHOW
	int WinActive = WA_ACTIVATE;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
	int WindowSize[2] = { DEF_APPSIZEX, DEF_APPSIZEY }, nowWindowSize[2];
#endif

int winX = 0;	// ウィンドウの初期位置设定用Ｘ
int winY = 0;	// ウィンドウの初期位置设定用Ｙ

COLORREF 	UserDeskTopColor;			// ユーザー设定デスクトップカラー记忆用（３２ビット）

#ifdef _DEBUG
BOOL debugLogWindow = FALSE;// ログ出力ウィンドウを出すかどうか？
#endif

#ifdef PUK3_LOGIN_VERCHECK
	int XGVerNum[3] = {0};
#endif
#ifdef _DEBUG
BOOL offlineFlag = FALSE;	// OFFラインフラグ
BOOL logoOffFlag = TRUE;	// ロゴＯＦＦフラグ
BOOL logouttKeyFlag = FALSE;// 登出キーフラグ
BOOL debugonFlag = FALSE;	// オートデバッグモード
BOOL gracheckOffFlag = FALSE;	// グラフィックビンサイズチェックフラグ

char DebugKey0[ 256 ];		// デバックキー０
char DebugKey1[ 256 ];		// デバックキー１
char DebugKey2[ 256 ];		// デバックキー２
#endif
#ifdef VERSION_TW
	//台服读取配置文件中的部分启动参数
	void CGSetupRead();
	//台服配置文件中读取的画质
	int CGSetupDevice3D;
#endif
#ifdef PUK3_LOGIN_VERCHECK
	// ＸＧのバージョン番号を取得
	void getXGVerNum();
#endif
/* コマンドライン解析 */
void AnalyzeCmdLine( void );
// ウィンドウモード切り替え关数宣言
void ChangeWindowMode( void );

int giInstallVersion = 0;	// ここでは 0:通常  1:ＥＸ  2:ＶＥＲ２
#if defined(PUK2)&&defined(_DEBUG)
	int setInstallVersion = -1;
#endif

#ifdef PUK3_MOUSECURSOR
	// マウスのグラフィック
	struct MOUSE_CURSOR_TYPE MouseCursorType[MOUSE_CURSOR_TYPE_MAX] = {
		{ GID_PUK2_MOUSE_CURSOR,	0,		0,		IDC_CURSOR2 },
		{ GID_PUK2_MOUSE_HAND,		-20,	-30,	IDC_CURSOR3 },
//		{ GID_PUK2_MOUSE_BATTLE,	-10,	-10,	IDC_CURSOR4 },
		{ GID_PUK2_MOUSE_BATTLE,	0,		0,		IDC_CURSOR4 },
		{ GID_PUK3_MOUSE_DIR_0,		-23,	-22,	IDC_CURSORDIR0 },
		{ GID_PUK3_MOUSE_DIR_1,		-16,	-25,	IDC_CURSORDIR1 },
		{ GID_PUK3_MOUSE_DIR_2,		-9,		-22,	IDC_CURSORDIR2 },
		{ GID_PUK3_MOUSE_DIR_3,		-7,		-16,	IDC_CURSORDIR3 },
		{ GID_PUK3_MOUSE_DIR_4,		-9,		-10,	IDC_CURSORDIR4 },
		{ GID_PUK3_MOUSE_DIR_5,		-16,	-7,		IDC_CURSORDIR5 },
		{ GID_PUK3_MOUSE_DIR_6,		-23,	-10,	IDC_CURSORDIR6 },
		{ GID_PUK3_MOUSE_DIR_7,		-25,	-16,	IDC_CURSORDIR7 },
	};
	HCURSOR MouseCursorB;
	HCURSOR MouseCursorType_HANDL[MOUSE_CURSOR_TYPE_MAX];
	int nowMouseType;
#endif

char binFolder[128]           = "Bin";				// bin フォルダの名称              //MLHIDE
char dataFolder[128]          = "data";				// dataフォルダの名称             //MLHIDE


// ウィンドウクラス构造体
WNDCLASS wndclass;

// Windowsの信息をフックする
//HRESULT CALLBACK HookProc( int nCode, WPARAM wParam, LPARAM lParam );				//メインプロシージャー
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
	WNDCLASS msgwndclass;
	LRESULT CALLBACK msgWindMsgProc( HWND hWnd, UINT Message, WPARAM wParam, LPARAM lParam );
#endif
#ifdef PUK3_SEGMENTATION_FAULT
	int SegmentationFaultFunc( unsigned int code );
#endif

//---------------------------------------------------------------------------//
// 概要 ：メインループ处理关数                                               //
// 引数 ：HANDLE hInstance                                                   //
//        HANDLE hPrevInstance                                               //
//        LPSTR lpCmdLine                                                    //
//        int   nCmdShow                                                     //
//---------------------------------------------------------------------------//
int PASCAL WinMain( HINSTANCE hInstance ,HINSTANCE hPrevInstance ,LPSTR lpCmdLine ,int nCmdShow )

{
	hInst = hInstance;		// ??????????????でもらった??????????????????????を????????????????に代入

	CmdShow = nCmdShow;		// WinMain关数の引数をグローバルにする
	CmdLine = lpCmdLine;	// WinMain关数のコマンドライン引数をグローバルにする

#ifdef CGMSV_DLL
	//使用dll处理封包
	HMODULE hDll;
	hDll = GetModuleHandle("cgmsv.dll");                                 //MLHIDE
	if (!hDll)
	{
		hDll = LoadLibrary("cgmsv.dll");                                    //MLHIDE
		if (!hDll) {
			FreeLibrary(hDll);
			MessageBox(hWnd, "ERR1:无法正常启动游戏,请联系管理员", "确定", MB_OK);             //MLHIDE
			exit(0);
		}
	}
	SendPacketProcessFun = (lpPacketProcessFun)GetProcAddress(hDll, "SendPacketProcess"); //MLHIDE
	if (SendPacketProcessFun == NULL)
	{
		FreeLibrary(hDll);
		MessageBox(hWnd, "ERR2:无法正常启动游戏,请联系管理员.", "确定", MB_OK);             //MLHIDE
		exit(0);
	}
	RecvPacketProcessFun = (lpPacketProcessFun)GetProcAddress(hDll, "RecvPacketProcess"); //MLHIDE
	if (RecvPacketProcessFun == NULL)
	{
		MessageBox(hWnd, "ERR3:无法正常启动游戏,请联系管理员.", "确定", MB_OK);             //MLHIDE
		exit(0);
	}
#endif

#ifdef VERSION_TW
	FILE* fp;
	char s[1024];
	char FileName[64] = { 0 };
	char tmpCmdLine[4096] = { 0 };
	int CmdLineLen = 0;

	if( strstr( lpCmdLine, "CGstart" ) ){                                //MLHIDE

		sprintf( FileName, "%s", ".\\data\\argument.ini" );                 //MLHIDE
		fp = fopen( FileName, "r" );                                        //MLHIDE
		if ( fp ) {
			do {
				memset( s, 0, sizeof(s) );
				fgets( s, 1024, fp );
				CmdLineLen += strlen(s);
				if( CmdLineLen >= 4096 )
					break;
				strcat( tmpCmdLine, s );
			} while (!feof(fp));
			fclose(fp);
		}
		CmdLine = tmpCmdLine;
	}
#endif

#ifdef _DEBUG
	// 同时起动チェックオブジェクト作成
	hMutex = CreateMutex( NULL, TRUE, DEF_APPNAME );
	// 同时に二つ立ち上げた时
	if( GetLastError() == ERROR_ALREADY_EXISTS ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, INFOMSG_14, "确定", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
		MessageBox( hWnd, "魔力宝贝不能多开。", "确定", MB_OK | MB_ICONSTOP );         //MLHIDE
#endif
		return FALSE;
	}
#endif
	// アップデートから实行したかチェック
	if( !strstr( CmdLine, "updated" ) ){                                 //MLHIDE
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, INFOMSG_15, "确定", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
		MessageBox( hWnd, ML_STRING(324, "请通过登陆器启动游戏。"), ML_STRING(325, "确定"), MB_OK | MB_ICONSTOP );
#endif
		return FALSE;
	}

#ifdef PUK3_MEMORYLEAK
	_RPTF0( _CRT_WARN, "\n\n\n\n\n\n开始\n\n" );                           //MLHIDE
#endif

    //*** ウインドウクラスの生成 ***
    if( !hPrevInstance )	// もし他のインスタンスが起动中なら初期化省略
	{
		wndclass.style = CS_BYTEALIGNCLIENT | CS_DBLCLKS;	// クラススタイル
		wndclass.lpfnWndProc = WindMsgProc;		// ウインドウプロシージャーアドレス定义
		wndclass.cbClsExtra = 0;				// クラス每の补足定义なし
		wndclass.cbWndExtra = 0;				// ウインドウ每の补足データ无し
		wndclass.hInstance = hInstance;			// インスタンスハンドル定義
//		wndclass.hIcon = LoadIcon(hInstance ,"ICON1");	// ????????からICON1をロード
	    wndclass.hIcon = LoadIcon( hInstance, MAKEINTRESOURCE(IDI_ICON2));
		//wndclass.hIcon = LoadIcon( NULL, IDI_APPLICATION);

#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの読込
		for(int i=0;i<MOUSE_CURSOR_TYPE_MAX;i++){
			MouseCursorType_HANDL[i] = LoadCursor( hInstance, MAKEINTRESOURCE(MouseCursorType[i].ResNo) );
		}
		wndclass.hCursor = NULL;
		// ??????????????を设定
		nowMouseType = mouse.type = 0;
#else
		wndclass.hCursor = LoadCursor(hInstance ,MAKEINTRESOURCE(IDC_CURSOR1));	// ??????????????を矢印に设定
#endif
		//wndclass.hCursor = LoadCursor(hInstance ,MAKEINTRESOURCE(IDC_POINTER));	// ??????????????を矢印に设定
		//wndclass.hCursor = NULL;	// ??????????????を矢印に设定
		//SetCursor( wndclass.hCursor );
		//SetCursor( LoadCursor( hInstance, MAKEINTRESOURCE( SA_MOUSE ) ) );
		//wndclass.hCursor = LoadCursor(NULL ,IDC_ARROW);	// ??????????????を矢印に设定

		wndclass.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH);// ウィンドウの背景色
		wndclass.lpszMenuName= NULL;			// メニューなし
		wndclass.lpszClassName = DEF_APPNAME;	// タイトルバー文字设定
		if( !RegisterClass(&wndclass) )
			return FALSE;						// 作成に失败したら終了する
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
		msgwndclass.style = 0;						// クラススタイル
		msgwndclass.lpfnWndProc = msgWindMsgProc;	// ウインドウプロシージャーアドレス定义
		msgwndclass.cbClsExtra = 0;				// クラス每の补足定义なし
		msgwndclass.cbWndExtra = 0;				// ウインドウ每の补足データ无し
		msgwndclass.hInstance = hInstance;			// インスタンスハンドル定义z
		msgwndclass.hIcon = NULL;
		msgwndclass.hCursor = LoadCursor(NULL ,IDC_ARROW);	// ??????????????を矢印に设定
		msgwndclass.hbrBackground = (HBRUSH)COLOR_INACTIVEBORDER;// ウィンドウの背景色
		msgwndclass.lpszMenuName= NULL;			// メニューなし
		msgwndclass.lpszClassName = "CROSSGATE NONOKMSGBOX";	// タイトルバー文字设定  //MLHIDE
		if( !RegisterClass(&msgwndclass) )
			return FALSE;						// 作成に失败したら終了する
#endif
	}
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

	// Windows 2000 と Me のユーザー设定デスクトップパレットを学习
	GetUserDeskTopClolor();

#ifdef VERSION_TW
	//台服读取配置文件中的部分启动参数
	CGSetupRead();
#endif
#ifdef PUK3_LOGIN_VERCHECK
	// ＸＧのバージョン番号を取得
	getXGVerNum();
#endif
#ifdef PUK3_ACCOUNT
	//アカウント用フォルダの作成
	makePuk3AccountSystemDir();

	//初期化とセーブファイルのデフォルト読み込み
	loadSaveFilePuk3Seq();
#endif
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif
	//コマンドライン解析
	AnalyzeCmdLine();

	// グラフィックファイル名の设定
	SetReadFileName( );
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

	// ウィンドウの作成
	ChangeWindowMode();

	// 全てのウィンドウからキーボード入力を夺う
	//SetFocus( hWnd );

	//{
		// ALT+TAB 无效にする
	//	int nOldVal;
	//	SystemParametersInfo (SPI_SCREENSAVERRUNNING, TRUE, &nOldVal, 0);
	//}

	// Windowsの信息をフックプロジージャをセットする
	///SetWindowsHookEx( WH_SYSMSGFILTER, HookProc, hInstance, 0 );
	//SetWindowsHookEx( WH_MSGFILTER, HookProc, hInstance, 0 )r;
	//SetWindowsHookEx( WH_KEYBOARD, HookProc, hInstance, 0 );
	//SetWindowsHookEx( WH_CALLWNDPROC, HookProc, hInstance, 0 );
	//SetWindowsHookEx( WH_CBT, HookProc, hInstance, 0 );
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif

#ifdef PUK3_SEGMENTATION_FAULT
	__try{
		// ゲームメイン处理开始
		ProcCall( GameMain(), PROCSTACK_GameMain );
	}__except( SegmentationFaultFunc( GetExceptionCode() ) ){
	}
#else
	// ゲームメイン处理开始
	GameMain();
#endif

	return FALSE;
}


#ifdef PUK3_LOGIN_VERCHECK
// ＸＧのバージョン番号を取得
void getXGVerNum()
{
	// クライアントバージョン番号取得
	sscanf( PUK2_NR_VERSION, "Ver %d.%d.%d", &XGVerNum[0], &XGVerNum[1], &XGVerNum[2] ); //MLHIDE
}
#endif
// Windows 2000 と Me のユーザー设定デスクトップパレットを学习 ******************************
void GetUserDeskTopClolor( void )
{
	int design = COLOR_BACKGROUND;	// 何处を变えるか配列リスト（今回は１个なので配列ではない）

	// ユーザー设定デスクトップカラーを取ってきて学习
	UserDeskTopColor = GetSysColor( COLOR_BACKGROUND );
}


// Windows 2000 と Me のユーザー设定デスクトップパレットを强制的に变更する。 *******************
void SetUserDeskTopClolor( void )
{
	//DWORD 	UserDeskTopColor;			// ユーザー设定システムカラー记忆用
	COLORREF 	CrossGateDeskTopColor;		// クロスゲートで设定するデスクトップカラー（３２ビット）
	int 		design = COLOR_BACKGROUND;	// 何处を变えるか配列リスト（今回は１个なので配列ではない）

	// 灰色をセット
	CrossGateDeskTopColor = RGB( 160,160,164 );
	// 强制的にセットする
	SetSysColors( 1, &design, &CrossGateDeskTopColor );
}


// Windows 2000 と Me のユーザー设定デスクトップパレットを元に戾す *****************************
void RestoreUserDeskTopColor( void )
{
	int design = COLOR_BACKGROUND;	// 何处を变えるか配列リスト（今回は１个なので配列ではない）

	// ユーザー设定デスクトップカラーを元に戾す。
	SetSysColors( 1, &design, &UserDeskTopColor );
}

#ifdef VERSION_TW
//台服读取配置文件中的部分启动参数
void CGSetupRead() 
{
	char FileName[256] = { 0 };
	char Buffer[256] = { 0 };
	char WinMode[128] = { 0 };
	char Activemode[256] = { 0 };
	char EffectMode[256] = { 0 };
	char tmpBuffer[4096] = { 0 };
	char* ptmpBuffer;

	GetCurrentDirectoryA(256, Buffer);
	if (Buffer[strlen(Buffer)] != '\\') {
		strcat(Buffer, "\\");                                               //MLHIDE
	}

	sprintf(FileName, "%s%s\\%s", Buffer, "data", "CG-Setup.ini");       //MLHIDE
	GetPrivateProfileStringA("DISPLAY", "ScreenMode", 0, WinMode, 128, FileName); //MLHIDE
	GetPrivateProfileStringA("DISPLAY", "Effect", 0, EffectMode, 256, FileName); //MLHIDE
	GetPrivateProfileStringA("DISPLAY", "ActiveMode", 0, Activemode, 256, FileName); //MLHIDE

	if (!strcmp(WinMode, "NORMAL") && !strstr(CmdLine, "windowmode")) {  //MLHIDE
		sprintf(tmpBuffer, "windowmode %s", CmdLine);                       //MLHIDE
		strcpy(CmdLine, tmpBuffer);
	}
	else if (!strcmp(WinMode, "FULLSCREEN") && strstr(CmdLine, "windowmode")) { //MLHIDE
		strcpy(tmpBuffer, CmdLine);
		ptmpBuffer = &tmpBuffer[strlen("windowmode ")];                     //MLHIDE
		strcpy(CmdLine, ptmpBuffer);
	}
	if (!strcmp(Activemode, "ONLYACTIVE") && !strstr(CmdLine, "onlyactive")) { //MLHIDE
		sprintf(tmpBuffer, "onlyactive %s", CmdLine);                       //MLHIDE
		strcpy(CmdLine, tmpBuffer);
	}
	else if (!strcmp(Activemode, "NOACTIVE") && strstr(CmdLine, "onlyactive")) { //MLHIDE
		strcpy(tmpBuffer, CmdLine);
		ptmpBuffer = &tmpBuffer[strlen("onlyactive ")];                     //MLHIDE
		strcpy(CmdLine, ptmpBuffer);
	}
	CGSetupDevice3D = 0;
	if (strstr(EffectMode, "GENERALEFF"))                                //MLHIDE
	{
		CGSetupDevice3D = 1;
	}
	else if (strstr(EffectMode, "SIMULATEEFF"))                          //MLHIDE
	{
		CGSetupDevice3D = 2;
	}
	else if (strstr(EffectMode, "ALPHAEFF"))                             //MLHIDE
	{
		CGSetupDevice3D = 3;
	}
	else if (strstr(EffectMode, "HARDEFF"))                              //MLHIDE
	{
		CGSetupDevice3D = 4;
	}
}
#endif

#ifdef _DEBUG

// デバックキー解析 ***********************************************************/
void DebugKey( char *str )
{
	char *work;
	int i = 0;

	// コマンドラインオプションに key0: があるとき
	if( ( work = strstr( str, "KEY0:" ) ) )                              //MLHIDE
	{
		work += 5;	// 文字列のあるところまでポインタ进める
		// : がくるまでループ
		while( *work != ':' ){
			DebugKey0[ i ] = *work++;
			i++;
			if( i == 255 ){
				i = 0;
				break;
			}
		}
	}
	DebugKey0[ i ] = NULL;	// 終端记号
	i = 0;
	// コマンドラインオプションに key1: があるとき
	if( ( work = strstr( str, "KEY1:" ) ) )                              //MLHIDE
	{
		work += 5;	// 文字列のあるところまでポインタ进める
		// : がくるまでループ
		while( *work != ':' ){
			DebugKey1[ i ] = *work++;
			i++;
			if( i == 255 ){
				i = 0;
				break;
			}
		}
	}
	DebugKey1[ i ] = NULL;	// 終端记号
	i = 0;
	// コマンドラインオプションに key1: があるとき
	if( ( work = strstr( str, "KEY2:" ) ) )                              //MLHIDE
	{
		work += 5;	// 文字列のあるところまでポインタ进める
		// : がくるまでループ
		while( *work != ':' ){
			DebugKey2[ i ] = *work++;
			i++;
			if( i == 255 ){
				i = 0;
				break;
			}
		}
	}
	DebugKey2[ i ] = NULL;	// 終端记号
}

#endif
#ifdef PUK2
	unsigned long CmdLineFlg;	// コマンドラインからの指定保管用フラグ
#endif
/* コマンドライン解析 *********************************************************/
void AnalyzeCmdLine( void )
{
	char tmpSaveDataName[128] = {0};
	char *addr;
	int no;

	// savedata ファイル名设定
	if( addr = strstr( CmdLine, "newest:" ) )                            //MLHIDE
	{
		sscanf( addr+strlen( "newest:" ), "%s", newestFileName );           //MLHIDE
	}
	// savedata ファイル名设定
	if( addr = strstr( CmdLine, "savedata:" ) )                          //MLHIDE
	{
		sscanf( addr+strlen( "savedata:" ), "%s", tmpSaveDataName );        //MLHIDE
	}
	// dataフォルダ变更
	if( addr = strstr( CmdLine, "datafolder:" ) )                        //MLHIDE
	{
		sscanf( addr+strlen( "datafolder:" ), "%s", dataFolder );           //MLHIDE
	}
	// bin フォルダ变更
	if( addr = strstr( CmdLine, "binfolder:" ) )                         //MLHIDE
	{
		sscanf( addr+strlen( "binfolder:" ), "%s", binFolder );             //MLHIDE
	}
	// マップフォルダ
	if( addr = strstr( CmdLine, "mapfolder:" ) )                         //MLHIDE
	{
		sscanf( addr+strlen( "mapfolder:" ), "%s", mapFolder );             //MLHIDE
	}

	// ここでセーブデータ名を决定しておく
#ifdef PUK3_ACCOUNT
	sprintf( saveDataName, "data\\0\\%s", SAVEFILE_NAME );               //MLHIDE
#else
	if( tmpSaveDataName[0] != '\0' ){
		sprintf( saveDataName, "%s\\%s", dataFolder, tmpSaveDataName );     //MLHIDE
	}else{
		sprintf( saveDataName, "%s\\%s", dataFolder, SAVEFILE_NAME );       //MLHIDE
	}
#endif

#ifdef MULTI_GRABIN
#ifdef VERSION_TW
#ifdef BIN_EXPAND
	char* aszModyfiGrabinCmd[BINMODE_MAX] = { "graphicbin:","graphicbinex:","graphicbiv2x:","graphicbinv3:","graphicbin_puk2:","graphicbin_puk3:","graphicbin_joy:","graphicbin_joy_ex:","graphicbin_joy_ch:" //MLHIDE
											,"graphicbin_ui:","graphicbin_sa:","graphicbin_exp1:","graphicbin_exp2:","graphicbin_exp3:","graphicbin_exp4:","graphicbin_exp5:","graphicbin_exp6:","graphicbin_exp7:","graphicbin_exp8:","graphicbin_exp9:" //MLHIDE
	};
	char* aszGrabinCmd[BINMODE_MAX] = { "graphicbin:","graphicbinex:","graphicbinv2:","graphicbinv3:","graphicbin_puk2:","graphicbin_puk3:","graphicbin_joy:","graphicbin_joy_ex:","graphicbin_joy_ch:" //MLHIDE
											,"graphicbin_ui:","graphicbin_sa:","graphicbin_exp1:","graphicbin_exp2:","graphicbin_exp3:","graphicbin_exp4:","graphicbin_exp5:","graphicbin_exp6:","graphicbin_exp7:","graphicbin_exp8:","graphicbin_exp9:" //MLHIDE
	};
	char* aszGraInfobinCmd[BINMODE_MAX] = { "graphicinfobin:","graphicinfobinex:","graphicinfobinv2:","graphicinfobinv3:","graphicinfobin_puk2:","graphicinfobin_puk3:","graphicinfobin_joy:","graphicinfobin_joy_ex:","graphicinfobin_joy_ch:" //MLHIDE
											,"graphicinfobin_ui:","graphicinfobin_sa:","graphicinfobin_exp1:","graphicinfobin_exp2:","graphicinfobin_exp3:","graphicinfobin_exp4:","graphicinfobin_exp5:","graphicinfobin_exp6:","graphicinfobin_exp7:","graphicinfobin_exp8:","graphicinfobin_exp9:" //MLHIDE
	};
	char* aszGrabinTmpName[BINMODE_MAX] = { "graphic_%d.bin","graphicex_%d.bin","graphicv2_%d.bin","graphicv3_%d.bin","PUK2\\graphic_puk2_%d.bin","PUK3\\graphic_puk3_%d.bin","graphic_joy_%d.bin","graphic_joy_ex_%d.bin","graphic_joy_ch%d.bin" //MLHIDE
											,"cgmsv\\graphic_ui_%d.bin","cgmsv\\graphic_sa_%d.bin","cgmsv\\graphic_exp1_%d.bin","cgmsv\\graphic_exp2_%d.bin","cgmsv\\graphic_exp3_%d.bin","cgmsv\\graphic_exp4_%d.bin","cgmsv\\graphic_exp5_%d.bin","cgmsv\\graphic_exp6_%d.bin","cgmsv\\graphic_exp7_%d.bin","cgmsv\\graphic_exp8_%d.bin","cgmsv\\graphic_exp9_%d.bin" //MLHIDE
	};
	char* aszGraInfoTmpName[BINMODE_MAX] = { "graphicInfo_%d.bin","graphicinfoex_%d.bin","graphicinfov2_%d.bin","graphicinfov3_%d.bin","PUK2\\graphicinfo_puk2_%d.bin","PUK3\\graphicinfo_puk3_%d.bin","graphicinfo_joy_%d.bin","graphicinfo_joy_ex_%d.bin","graphicinfo_joy_ch%d.bin" //MLHIDE
											,"cgmsv\\graphicinfo_ui_%d.bin","cgmsv\\graphicinfo_sa_%d.bin","cgmsv\\graphicinfo_exp1_%d.bin","cgmsv\\graphicinfo_exp2_%d.bin","cgmsv\\graphicinfo_exp3_%d.bin","cgmsv\\graphicinfo_exp4_%d.bin","cgmsv\\graphicinfo_exp5_%d.bin","cgmsv\\graphicinfo_exp6_%d.bin","cgmsv\\graphicinfo_exp7_%d.bin","cgmsv\\graphicinfo_exp8_%d.bin","cgmsv\\graphicinfo_exp9_%d.bin" //MLHIDE
	};

	char* aszAnibinCmd[BINMODE_MAX] = { "animebin:","animebinex:","animebinv2:","animebinv3:","animebin_puk2:","animebin_puk3:","animebin_joy:","animebin_joy_ex:","animebin_joy_ch:" //MLHIDE
											,"animebin_ui:","animebin_sa:","animebin_exp1:","animebin_exp2:","animebin_exp3:","animebin_exp4:","animebin_exp5:","animebin_exp6:","animebin_exp7:","animebin_exp8:","animebin_exp9:" //MLHIDE
	};
	char* aszAniInfobinCmd[BINMODE_MAX] = { "animeinfobin:","animeinfobinex:","animeinfobinv2:","animeinfobinv3:","animeinfobin_puk2:","animeinfobin_puk3:","animeinfobin_joy:","animeinfobin_joy_ex:","animeinfobin_joy_ch:" //MLHIDE
											,"animeinfobin_ui:","animeinfobin_sa:","animeinfobin_exp1:","animeinfobin_exp2:","animeinfobin_exp3:","animeinfobin_exp4:","animeinfobin_exp5:","animeinfobin_exp6:","animeinfobin_exp7:","animeinfobin_exp8:","animeinfobin_exp9:" //MLHIDE
	};
	char* aszAnibinTmpName[BINMODE_MAX] = { "anime_%d.bin","animeex_%d.bin","animev2_%d.bin","animev3_%d.bin","PUK2\\anime_puk2_%d.bin","PUK3\\anime_puk3_%d.bin","anime_joy_%d.bin","anime_joy_ex_%d.bin","anime_joy_ch%d.bin" //MLHIDE
											,"cgmsv\\anime_ui_%d.bin","cgmsv\\anime_sa_%d.bin","cgmsv\\anime_exp1_%d.bin","cgmsv\\anime_exp2_%d.bin","cgmsv\\anime_exp3_%d.bin","cgmsv\\anime_exp4_%d.bin","cgmsv\\anime_exp5_%d.bin","cgmsv\\anime_exp6_%d.bin","cgmsv\\anime_exp7_%d.bin","cgmsv\\anime_exp8_%d.bin","cgmsv\\anime_exp9_%d.bin" //MLHIDE
	};
	char* aszAniInfoTmpName[BINMODE_MAX] = { "animeinfo_%d.bin","animeinfoex_%d.bin","animeinfov2_%d.bin","animeinfov3_%d.bin","PUK2\\animeinfo_puk2_%d.bin","PUK3\\animeinfo_puk3_%d.bin","animeinfo_joy_%d.bin","animeinfo_joy_ex_%d.bin","animeinfo_joy_ch%d.bin" //MLHIDE
											,"cgmsv\\animeinfo_ui_%d.bin","cgmsv\\animeinfo_sa_%d.bin","cgmsv\\animeinfo_exp1_%d.bin","cgmsv\\animeinfo_exp2_%d.bin","cgmsv\\animeinfo_exp3_%d.bin","cgmsv\\animeinfo_exp4_%d.bin","cgmsv\\animeinfo_exp5_%d.bin","cgmsv\\animeinfo_exp6_%d.bin","cgmsv\\animeinfo_exp7_%d.bin","cgmsv\\animeinfo_exp8_%d.bin","cgmsv\\animeinfo_exp9_%d.bin" //MLHIDE
	};

#ifdef PUK3_RIDEBIN
	char* aszCrdbinCmd[BINMODE_MAX] = { "coordinatebin:","coordinatebinex:","coordinatebinv2:","coordinatebinv3:","coordinatebin_puk2:","coordinatebin_puk3:","coordinatebin_joy:","coordinatebin_joy_ex:","coordinatebin_joy_ch:" //MLHIDE
											,"coordinatebin_ui:","coordinatebin_sa:","coordinatebin_exp1:","coordinatebin_exp2:","coordinatebin_exp3:","coordinatebin_exp4:","coordinatebin_exp5:","coordinatebin_exp6:","coordinatebin_exp7:","coordinatebin_exp8:","coordinatebin_exp9:" //MLHIDE
	};
	char* aszCrdInfobinCmd[BINMODE_MAX] = { "coordinateinfobin:","coordinateinfobinex:","coordinateinfobinv2:","coordinateinfobinv3:","coordinateinfobin_puk2:","coordinateinfobin_puk3:","coordinateinfobin_joy:","coordinateinfobin_joy_ex:","coordinateinfobin_joy_ch:" //MLHIDE
											,"coordinateinfobin_ui:","coordinateinfobin_sa:","coordinateinfobin_exp1:","coordinateinfobin_exp2:","coordinateinfobin_exp3:","coordinateinfobin_exp4:","coordinateinfobin_exp5:","coordinateinfobin_exp6:","coordinateinfobin_exp7:","coordinateinfobin_exp8:","coordinateinfobin_exp9:" //MLHIDE
	};
	char* aszCrdbinTmpName[BINMODE_MAX] = { "coordinate_%d.bin","coordinateex_%d.bin","coordinatev2_%d.bin","coordinatev3_%d.bin","PUK2\\coordinate_puk2_%d.bin","PUK3\\coordinate_puk3_%d.bin","coordinate_joy_%d.bin","coordinate_joy_ex_%d.bin","coordinate_joy_ch%d.bin" //MLHIDE
											,"cgmsv\\coordinate_ui_%d.bin","cgmsv\\coordinate_sa_%d.bin","cgmsv\\coordinate_exp1_%d.bin","cgmsv\\coordinate_exp2_%d.bin:","cgmsv\\coordinate_exp3_%d.bin","cgmsv\\coordinate_exp1_%4.bin","cgmsv\\coordinate_exp5_%d.bin","cgmsv\\coordinate_exp6_%d.bin","cgmsv\\coordinate_exp7_%d.bin","cgmsv\\coordinate_exp8_%d.bin","cgmsv\\coordinate_exp9_%d.bin" //MLHIDE
	};
	char* aszCrdInfoTmpName[BINMODE_MAX] = { "coordinateinfo_%d.bin","coordinateinfoex_%d.bin","coordinateinfov2_%d.bin","coordinateinfov3_%d.bin","PUK2\\coordinateinfo_puk2_%d.bin","PUK3\\coordinateinfo_puk3_%d.bin","coordinateinfo_joy_%d.bin","coordinateinfo_joy_ex_%d.bin","coordinateinfo_joy_ch%d.bin" //MLHIDE
											,"cgmsv\\coordinateinfo_ui_%d.bin","cgmsv\\coordinateinfo_sa_%d.bin","cgmsv\\coordinateinfo_exp1_%d.bin","cgmsv\\coordinateinfo_exp2_%d.bin:","cgmsv\\coordinateinfo_exp3_%d.bin","cgmsv\\coordinateinfo_exp1_%4.bin","cgmsv\\coordinateinfo_exp5_%d.bin","cgmsv\\coordinateinfo_exp6_%d.bin","cgmsv\\coordinateinfo_exp7_%d.bin","cgmsv\\coordinateinfo_exp8_%d.bin","cgmsv\\coordinateinfo_exp9_%d.bin" //MLHIDE
	};
#endif
#else
	char* aszModyfiGrabinCmd[BINMODE_MAX] = { "graphicbin:","graphicbinex:","graphicbiv2x:","graphicbinv3:","graphicbin_puk2:","graphicbin_puk3:","graphicbin_joy:","graphicbin_joy_ex:","graphicbin_joy_ch:"}; //MLHIDE
	char* aszGrabinCmd[BINMODE_MAX] = { "graphicbin:","graphicbinex:","graphicbinv2:","graphicbinv3:","graphicbin_puk2:","graphicbin_puk3:","graphicbin_joy:","graphicbin_joy_ex:","graphicbin_joy_ch:"}; //MLHIDE
	char* aszGraInfobinCmd[BINMODE_MAX] = { "graphicinfobin:","graphicinfobinex:","graphicinfobinv2:","graphicinfobinv3:","graphicinfobin_puk2:","graphicinfobin_puk3:","graphicinfobin_joy:","graphicinfobin_joy_ex:","graphicinfobin_joy_ch:"}; //MLHIDE
	char* aszGrabinTmpName[BINMODE_MAX] = { "graphic_%d.bin","graphicex_%d.bin","graphicv2_%d.bin","graphicv3_%d.bin","PUK2\\graphic_puk2_%d.bin","PUK3\\graphic_puk3_%d.bin","graphic_joy_%d.bin","graphic_joy_ex_%d.bin","graphic_joy_ch%d.bin"}; //MLHIDE
	char* aszGraInfoTmpName[BINMODE_MAX] = { "graphicInfo_%d.bin","graphicinfoex_%d.bin","graphicinfov2_%d.bin","graphicinfov3_%d.bin","PUK2\\graphicinfo_puk2_%d.bin","PUK3\\graphicinfo_puk3_%d.bin","graphicinfo_joy_%d.bin","graphicinfo_joy_ex_%d.bin","graphicinfo_joy_ch%d.bin"}; //MLHIDE

	char* aszAnibinCmd[BINMODE_MAX] = { "animebin:","animebinex:","animebinv2:","animebinv3:","animebin_puk2:","animebin_puk3:","animebin_joy:","animebin_joy_ex:","animebin_joy_ch:"}; //MLHIDE
	char* aszAniInfobinCmd[BINMODE_MAX] = { "animeinfobin:","animeinfobinex:","animeinfobinv2:","animeinfobinv3:","animeinfobin_puk2:","animeinfobin_puk3:","animeinfobin_joy:","animeinfobin_joy_ex:","animeinfobin_joy_ch:"}; //MLHIDE
	char* aszAnibinTmpName[BINMODE_MAX] = { "anime_%d.bin","animeex_%d.bin","animev2_%d.bin","animev3_%d.bin","PUK2\\anime_puk2_%d.bin","PUK3\\anime_puk3_%d.bin","anime_joy_%d.bin","anime_joy_ex_%d.bin","anime_joy_ch%d.bin"}; //MLHIDE
	char* aszAniInfoTmpName[BINMODE_MAX] = { "animeinfo_%d.bin","animeinfoex_%d.bin","animeinfov2_%d.bin","animeinfov3_%d.bin","PUK2\\animeinfo_puk2_%d.bin","PUK3\\animeinfo_puk3_%d.bin","animeinfo_joy_%d.bin","animeinfo_joy_ex_%d.bin","animeinfo_joy_ch%d.bin"}; //MLHIDE

#ifdef PUK3_RIDEBIN
	char* aszCrdbinCmd[BINMODE_MAX] = { "coordinatebin:","coordinatebinex:","coordinatebinv2:","coordinatebinv3:","coordinatebin_puk2:","coordinatebin_puk3:","coordinatebin_joy:","coordinatebin_joy_ex:","coordinatebin_joy_ch:"}; //MLHIDE
	char* aszCrdInfobinCmd[BINMODE_MAX] = { "coordinateinfobin:","coordinateinfobinex:","coordinateinfobinv2:","coordinateinfobinv3:","coordinateinfobin_puk2:","coordinateinfobin_puk3:","coordinateinfobin_joy:","coordinateinfobin_joy_ex:","coordinateinfobin_joy_ch:"}; //MLHIDE
	char* aszCrdbinTmpName[BINMODE_MAX] = { "coordinate_%d.bin","coordinateex_%d.bin","coordinatev2_%d.bin","coordinatev3_%d.bin","PUK2\\coordinate_puk2_%d.bin","PUK3\\coordinate_puk3_%d.bin","coordinate_joy_%d.bin","coordinate_joy_ex_%d.bin","coordinate_joy_ch%d.bin"}; //MLHIDE
	char* aszCrdInfoTmpName[BINMODE_MAX] = { "coordinateinfo_%d.bin","coordinateinfoex_%d.bin","coordinateinfov2_%d.bin","coordinateinfov3_%d.bin","PUK2\\coordinateinfo_puk2_%d.bin","PUK3\\coordinateinfo_puk3_%d.bin","coordinateinfo_joy_%d.bin","coordinateinfo_joy_ex_%d.bin","coordinateinfo_joy_ch%d.bin"}; //MLHIDE
#endif
#endif
	// グラビン、アニビンの読み込みファイル名设定
//#ifdef PUK3_BIN
#elif defined(PUK3_BIN)
	char *aszModyfiGrabinCmd[BINMODE_MAX] = {"graphicbin:","graphicbinex:","graphicbiv2x:","graphicbinv3:","graphicbin_puk2:","graphicbin_puk3:","graphicbin_kw:"}; //MLHIDE
	char *aszGrabinCmd[BINMODE_MAX] = {"graphicbin:","graphicbinex:","graphicbinv2:","graphicbinv3:","graphicbin_puk2:","graphicbin_puk3:","graphicbin_kw:"}; //MLHIDE
	char *aszGraInfobinCmd[BINMODE_MAX] = {"graphicinfobin:","graphicinfobinex:","graphicinfobinv2:","graphicinfobinv3:","graphicinfobin_puk2:","graphicinfobin_puk3:","graphicinfobin_kw:"}; //MLHIDE
	char *aszGrabinTmpName[BINMODE_MAX] = {"graphic_%d.bin","graphicex_%d.bin","graphicv2_%d.bin","graphicv3_%d.bin","PUK2\\graphic_puk2_%d.bin","PUK3\\graphic_puk3_%d.bin","KW\\graphic_kw_%d.bin"}; //MLHIDE
	char *aszGraInfoTmpName[BINMODE_MAX] = {"graphicInfo_%d.bin","graphicinfoex_%d.bin","graphicinfov2_%d.bin","graphicinfov3_%d.bin","PUK2\\graphicinfo_puk2_%d.bin","PUK3\\graphicinfo_puk3_%d.bin","KW\\graphicinfo_kw_%d.bin"}; //MLHIDE

	char *aszAnibinCmd[BINMODE_MAX] = {"animebin:","animebinex:","animebinv2:","animebinv3:","animebin_puk2:","animebin_puk3:","animebin_kw:"}; //MLHIDE
	char *aszAniInfobinCmd[BINMODE_MAX] = {"animeinfobin:","animeinfobinex:","animeinfobinv2:","animeinfobinv3:","animeinfobin_puk2:","animeinfobin_puk3:","animeinfobin_kw:"}; //MLHIDE
	char *aszAnibinTmpName[BINMODE_MAX] = {"anime_%d.bin","animeex_%d.bin","animev2_%d.bin","animev3_%d.bin","PUK2\\anime_puk2_%d.bin","PUK3\\anime_puk3_%d.bin","KW\\anime_kw_%d.bin"}; //MLHIDE
	char *aszAniInfoTmpName[BINMODE_MAX] = {"animeinfo_%d.bin","animeinfoex_%d.bin","animeinfov2_%d.bin","animeinfov3_%d.bin","PUK2\\animeinfo_puk2_%d.bin","PUK3\\animeinfo_puk3_%d.bin","KW\\animeinfo_kw_%d.bin"}; //MLHIDE

	#ifdef PUK3_RIDEBIN
		char *aszCrdbinCmd[BINMODE_MAX] = {"coordinatebin:","coordinatebinex:","coordinatebinv2:","coordinatebinv3:","coordinatebin_puk2:","coordinatebin_puk3:","coordinatebin_kw:"}; //MLHIDE
		char *aszCrdInfobinCmd[BINMODE_MAX] = {"coordinateinfobin:","coordinateinfobinex:","coordinateinfobinv2:","coordinateinfobinv3:","coordinateinfobin_puk2:","coordinateinfobin_puk3:","coordinateinfobin_kw:"}; //MLHIDE
		char *aszCrdbinTmpName[BINMODE_MAX] = {"coordinate_%d.bin","coordinateex_%d.bin","coordinatev2_%d.bin","coordinatev3_%d.bin","PUK2\\coordinate_puk2_%d.bin","PUK3\\coordinate_puk3_%d.bin","KW\\coordinate_kw_%d.bin"}; //MLHIDE
		char *aszCrdInfoTmpName[BINMODE_MAX] = {"coordinateinfo_%d.bin","coordinateinfoex_%d.bin","coordinateinfov2_%d.bin","coordinateinfov3_%d.bin","PUK2\\coordinateinfo_puk2_%d.bin","PUK3\\coordinateinfo_puk3_%d.bin","KW\\coordinateinfo_kw_%d.bin"}; //MLHIDE
	#endif
#elif defined(PUK2)
	char *aszModyfiGrabinCmd[BINMODE_MAX] = {"graphicbin:","graphicbinex:","graphicbiv2x:","graphicbinv3:","graphicbin_puk2:"}; //MLHIDE
	char *aszGrabinCmd[BINMODE_MAX] = {"graphicbin:","graphicbinex:","graphicbinv2:","graphicbinv3:","graphicbin_puk2:"}; //MLHIDE
	char *aszGraInfobinCmd[BINMODE_MAX] = {"graphicinfobin:","graphicinfobinex:","graphicinfobinv2:","graphicinfobinv3:","graphicinfobin_puk2:"}; //MLHIDE
	char *aszGrabinTmpName[BINMODE_MAX] = {"graphic_%d.bin","graphicex_%d.bin","graphicv2_%d.bin","graphicv3_%d.bin","PUK2\\graphic_puk2_%d.bin"}; //MLHIDE
	char *aszGraInfoTmpName[BINMODE_MAX] = {"graphicInfo_%d.bin","graphicinfoex_%d.bin","graphicinfov2_%d.bin","graphicinfov3_%d.bin","PUK2\\graphicinfo_puk2_%d.bin"}; //MLHIDE

	char *aszAnibinCmd[BINMODE_MAX] = {"animebin:","animebinex:","animebinv2:","animebinv3:","animebin_puk2:"}; //MLHIDE
	char *aszAniInfobinCmd[BINMODE_MAX] = {"animeinfobin:","animeinfobinex:","animeinfobinv2:","animeinfobinv3:","animeinfobin_puk2:"}; //MLHIDE
	char *aszAnibinTmpName[BINMODE_MAX] = {"anime_%d.bin","animeex_%d.bin","animev2_%d.bin","animev3_%d.bin","PUK2\\anime_puk2_%d.bin"}; //MLHIDE
	char *aszAniInfoTmpName[BINMODE_MAX] = {"animeinfo_%d.bin","animeinfoex_%d.bin","animeinfov2_%d.bin","animeinfov3_%d.bin","PUK2\\animeinfo_puk2_%d.bin"}; //MLHIDE
#else
	char *aszModyfiGrabinCmd[BINMODE_MAX] = {"graphicbin:","graphicbinex:","graphicbiv2x:"}; //MLHIDE
	char *aszGrabinCmd[BINMODE_MAX] = {"graphicbin:","graphicbinex:","graphicbinv2:"}; //MLHIDE
	char *aszGraInfobinCmd[BINMODE_MAX] = {"graphicinfobin:","graphicinfobinex:","graphicinfobinv2:"}; //MLHIDE
	char *aszGrabinTmpName[BINMODE_MAX] = {"graphic_%d.bin","graphicex_%d.bin","graphicv2_%d.bin"}; //MLHIDE
	char *aszGraInfoTmpName[BINMODE_MAX] = {"graphicInfo_%d.bin","graphicinfoex_%d.bin","graphicinfov2_%d.bin"}; //MLHIDE

	char *aszAnibinCmd[BINMODE_MAX] = {"animebin:","animebinex:","animebinv2:"}; //MLHIDE
	char *aszAniInfobinCmd[BINMODE_MAX] = {"animeinfobin:","animeinfobinex:","animeinfobinv2:"}; //MLHIDE
	char *aszAnibinTmpName[BINMODE_MAX] = {"anime_%d.bin","animeex_%d.bin","animev2_%d.bin"}; //MLHIDE
	char *aszAniInfoTmpName[BINMODE_MAX] = {"animeinfo_%d.bin","animeinfoex_%d.bin","animeinfov2_%d.bin"}; //MLHIDE
#endif


	int i;
	// 扩张したグラビン、アニビン、などをバージョン别に设定
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// 読み込みファイル名の变更
		if( addr = strstr( CmdLine, aszGrabinCmd[i] ) ){	// キーワード检索
			sscanf( addr+strlen( aszGrabinCmd[i] ), "%d", &no );	// バージョン番号检索  //MLHIDE
			sprintf( _graphicBinName_plus[i], aszGrabinTmpName[i], no );	// ファイル名设定
		}
		// 読み込みファイル名の变更
		if( addr = strstr( CmdLine, aszModyfiGrabinCmd[i] ) ){	// キーワード检索
			sscanf( addr+strlen( aszModyfiGrabinCmd[i] ), "%d", &no );	// バージョン番号检索 //MLHIDE
			sprintf( _graphicBinName_plus[i], aszGrabinTmpName[i], no );	// ファイル名设定
		}
		if( addr = strstr( CmdLine, aszGraInfobinCmd[i] ) ){
			sscanf( addr+strlen( aszGraInfobinCmd[i] ), "%d", &no );           //MLHIDE
			sprintf( _graphicInfoBinName_plus[i], aszGraInfoTmpName[i], no );
		}

		if( addr = strstr( CmdLine, aszAnibinCmd[i] ) ){
			sscanf( addr+strlen( aszAnibinCmd[i] ), "%d", &no );               //MLHIDE
			sprintf( _animeBinName_plus[i], aszAnibinTmpName[i], no );
		}
		if( addr = strstr( CmdLine, aszAniInfobinCmd[i] ) )	{
			sscanf( addr+strlen( aszAniInfobinCmd[i] ), "%d", &no );           //MLHIDE
			sprintf( _animeInfoBinName_plus[i], aszAniInfoTmpName[i], no );
		}
	}
	#ifdef PUK3_RIDEBIN
		// 座标binは共用できるのでちょっと特殊
		for( i = 0; i < BINMODE_MAX; i ++ ){
			// デフォルトに'*'が含まれてない事を????????
			if( !strchr( _coordinateBinName_plus[i], '*' ) ){
				if( addr = strstr( CmdLine, aszCrdbinCmd[i] ) ){
					sscanf( addr+strlen( aszCrdbinCmd[i] ), "%d", &no );             //MLHIDE
					sprintf( _coordinateBinName_plus[i], aszCrdbinTmpName[i], no );
				}
			}
			// デフォルトに'*'が含まれてない事を????????
			if( !strchr( _coordinateInfoBinName_plus[i], '*' ) ){
				if( addr = strstr( CmdLine, aszCrdInfobinCmd[i] ) )	{
					sscanf( addr+strlen( aszCrdInfobinCmd[i] ), "%d", &no );         //MLHIDE
					sprintf( _coordinateInfoBinName_plus[i], aszCrdInfoTmpName[i], no );
				}
			}
		}

		// 共有先のファイル名をコピー
		for( i = 0; i < BINMODE_MAX; i ++ ){
			// デフォルトに'*'が含まれている事を????????
			if( strchr( _coordinateBinName_plus[i], '*' ) ){
				sscanf( _coordinateBinName_plus[i], "*%d", &no );                 //MLHIDE
				strcpy( _coordinateBinName_plus[i], _coordinateBinName_plus[no] );
			}
			// デフォルトに'*'が含まれている事を????????
			if( strchr( _coordinateInfoBinName_plus[i], '*' ) ){
				sscanf( _coordinateInfoBinName_plus[i], "*%d", &no );             //MLHIDE
				strcpy( _coordinateInfoBinName_plus[i], _coordinateInfoBinName_plus[no] );
			}
		#ifdef _DEBUG
			// デフォルトに'*'が含まれている事を????????
			if( strchr( coordinateLogName_plus[i], '*' ) ){
				sscanf( coordinateLogName_plus[i], "*%d", &no );                  //MLHIDE
				strcpy( coordinateLogName_plus[i], coordinateLogName_plus[no] );
			}
		#endif
		}
	#endif
#else
	// 読み込みファイル名の变更
	if( addr = strstr( CmdLine, "graphicbin:" ) )                        //MLHIDE
	{
		sscanf( addr+strlen( "graphicbin:" ), "%d", &no );                  //MLHIDE
		sprintf( _graphicBinName, "Graphic_%d.bin", no );                   //MLHIDE
	}
	if( addr = strstr( CmdLine, "graphicinfobin:" ) )                    //MLHIDE
	{
		sscanf( addr+strlen( "graphicinfobin:" ), "%d", &no );              //MLHIDE
		sprintf( _graphicInfoBinName, "GraphicInfo_%d.bin", no );           //MLHIDE
	}
	if( addr = strstr( CmdLine, "animebin:" ) )                          //MLHIDE
	{
		sscanf( addr+strlen( "animebin:" ), "%d", &no );                    //MLHIDE
		sprintf( _animeBinName, "Anime_%d.bin", no );                       //MLHIDE
	}
	if( addr = strstr( CmdLine, "animeinfobin:" ) )                      //MLHIDE
	{
		sscanf( addr+strlen( "animeinfobin:" ), "%d", &no );                //MLHIDE
		sprintf( _animeInfoBinName, "AnimeInfo_%d.bin", no );               //MLHIDE
	}

#endif

	// コマンドラインオプションに windowmode があるとき
	if( strstr( CmdLine, "windowmode" ) ) WindowMode = TRUE;             //MLHIDE

	// nodelay オプションの设定。（通信待ち０．４秒ありにする时）
	if( strstr( CmdLine, "nodelay" ) ) NoDelay = FALSE;                  //MLHIDE

	// ウィンドウ表示位置变更
	if( addr = strstr( CmdLine, "winpos:" ) )                            //MLHIDE
	{
		int x, y;
		sscanf( addr+strlen( "winpos:" ), "%d,%d", &x, &y );                //MLHIDE
		if( x >= 0 && y >= 0 )
		{
			winX = x;
			winY = y;
		}
	}

#ifdef PUK2
	CmdLineFlg=0;

	// ３Ｄデバイスの選択
	device3D=1;
	if( addr = strstr( CmdLine, "3Ddevice:" ) ) sscanf( addr+strlen( "3Ddevice:" ), "%hhd", &device3D ); //MLHIDE

#ifdef VERSION_TW
	//如果成功从台服配置文件中读取到画质设置则覆盖掉命令行读取的画质
	if (CGSetupDevice3D)
		device3D = CGSetupDevice3D;
#endif

#ifdef PUK2_NOSOUNDMUSIC
	// 效果音ＯＦＦの时
	if( strstr( CmdLine, "soundoff" ) ) soundSoundFlag = 0;              //MLHIDE
	// ＢＧＭＯＦＦの时
	if( strstr( CmdLine, "musicoff" ) ) musicSoundFlag = 0;              //MLHIDE
#endif

	#ifdef PUK2_TITILEELEMENTEFFECT_2
	#else
	// 属性效果表示初期化
	efficacydisp = EFFICACYDISP_NOMAL|EFFICACYDISP_BOSS;
	#endif
#endif
#ifdef WIN_SIZE_DEF
	if (addr = strstr(CmdLine, "windowsize:")) {                         //MLHIDE
		int size;

		if (sscanf(addr + strlen("windowsize:"), "%d", &size) <= 0)         //MLHIDE
			size = 1;

		if (size == 1) {
			WindowSize[0] = 640;
			WindowSize[1] = 480;
			DEF_APPSIZEX = 640;
			DEF_APPSIZEY = 480;
		}
		else if (size == 2) {
			WindowSize[0] = 800;
			WindowSize[1] = 600;
			DEF_APPSIZEX = 800;
			DEF_APPSIZEY = 600;
		}
		else if (size == 3) {
			WindowSize[0] = 960;
			WindowSize[1] = 720;
			DEF_APPSIZEX = 960;
			DEF_APPSIZEY = 720;
		}
		else {
			WindowSize[0] = 640;
			WindowSize[1] = 480;
			DEF_APPSIZEX = 640;
			DEF_APPSIZEY = 480;
		}

		SymOffsetX = (DEF_APPSIZEX - 640) / 2;
		SymOffsetY = (DEF_APPSIZEY - 480) / 2;
		ScaleOffsetX = ((DEF_APPSIZEX / 160) - 4) * 100;
		ScaleOffsetY = ((DEF_APPSIZEY / 120) - 4) * 100;
	}
#endif

#ifdef PUK2_NOSHOW
	// アクティブ时のみ描画の时
	if( strstr( CmdLine, "onlyactive" ) ) CmdLineFlg |= CMDLINE_ONLYACTIVEBLT; //MLHIDE
#endif
#ifdef _DEBUG
#ifdef PUK2_NOSOUNDMUSIC
	// 音无しの时
	if( strstr( CmdLine, "noinitsound" ) ) CmdLineFlg |= CMDLINE_NOINITSOUND; //MLHIDE
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
	// ウィンドウ表示位置变更
	if ( addr = strstr( CmdLine, "winsize:" ) ){                         //MLHIDE
		int x, y;
		sscanf( addr+strlen( "winsize:" ), "%d,%d", &x, &y );               //MLHIDE
		if ( x >= 0 && y >= 0 ){
			WindowSize[0] = x;
			WindowSize[1] = y;
		}
	}
#endif
	// グラフィックチェックＯＦＦの时
	if( strstr( CmdLine, "gracheckoff" ) )                               //MLHIDE
	{
		gracheckOffFlag = TRUE;
	}
	else
	{
		gracheckOffFlag = FALSE;
	}
	// オートデバッグモードの时
	if( strstr( CmdLine, "debugon" ) )                                   //MLHIDE
	{
		debugonFlag = TRUE;
	}
	else
	{
		debugonFlag = FALSE;
	}
	// 登出キー使うとき
	if( strstr( CmdLine, "logoutkeyon" ) )                               //MLHIDE
	{
		logouttKeyFlag = TRUE;
	}
	else
	{
		logouttKeyFlag = FALSE;
	}

	// スキル、ペット、ペットテックのソートの设定
	if( strstr( CmdLine, "sortoff" ) )                                   //MLHIDE
	{
		sortOffFlag = 1;
	}
	else
	{
		sortOffFlag = 0;
	}

	// ロゴＯＦＦモードの设定
	if( strstr( CmdLine, "logooff" ) )                                   //MLHIDE
	{
		logoOffFlag = TRUE;
	}
	else
	{
		logoOffFlag = FALSE;
	}

	// ログウィンドウ表示位置变更
	if( addr = strstr( CmdLine, "logpos:" ) )                            //MLHIDE
	{
		int x, y;
		sscanf( addr+strlen( "logpos:" ), "%d,%d", &x, &y );                //MLHIDE
		if( x >= 0 && y >= 0 )
		{
			logWinX = x;
			logWinY = y;
		}
	}
	// 离线模式の设定
	if( strstr( CmdLine, "offline" ) )                                   //MLHIDE
	{
		offlineFlag = TRUE;
	}
	else
	{
		offlineFlag = FALSE;
	}
	if( offlineFlag )
	{
		ProcNo = PROC_GAME;
	}
	else
	{
		ProcNo = PROC_ID_PASSWORD;
	}
#ifdef PUK2
	// 拟似インストール状态の设定
	setInstallVersion = -1;
	if( addr = strstr( CmdLine, "installver:" ) ) sscanf( addr+strlen( "installver:" ), "%d", &setInstallVersion ); //MLHIDE

		// 属性效果表示
	#ifdef PUK2_TITILEELEMENTEFFECT_2
		efficacydisp_2 = 0;
		if( addr = strstr( CmdLine, "efficacydisp:" ) ) sscanf( addr+strlen( "efficacydisp:" ), "%hhu", &efficacydisp_2 ); //MLHIDE
		if ( efficacydisp_2 ) efficacydisp_2 |= 0x80;
	#else
		if( addr = strstr( CmdLine, "efficacydisp:" ) ) sscanf( addr+strlen( "efficacydisp:" ), "%hhu", &efficacydisp ); //MLHIDE
	#endif

	// ３Ｄ使用状况の设定
	if( strstr( CmdLine, "3D_off" ) ) CmdLineFlg|=CMDLINE_3D_OFF;        //MLHIDE
	// 战闘时属性表示の设定
	if( strstr( CmdLine, "elmdisp" ) ) CmdLineFlg|=CMDLINE_ELMDISP;      //MLHIDE
	// デバッグラインの表示の设定
	if( strstr( CmdLine, "debugline" ) ) CmdLineFlg|=CMDLINE_DEBUGLINE;  //MLHIDE
	// １６ＢＰＰ以外使用の设定
	if( strstr( CmdLine, "fulldispmode" ) ) CmdLineFlg|=CMDLINE_FULLDISPMODE; //MLHIDE
#endif
	if( strstr( CmdLine, "PROC_OHTA_TEST" ) )                            //MLHIDE
	{
		ProcNo = PROC_OHTA_TEST;
		offlineFlag = TRUE;
	}
	if( strstr( CmdLine, "PROC_TAKE_TEST" ) )                            //MLHIDE
	{
	 	ProcNo = PROC_TAKE_TEST;
		offlineFlag = TRUE;
	 }
	if( strstr( CmdLine, "PROC_DWAF_TEST" ) )                            //MLHIDE
	{
	 	ProcNo = PROC_DWAF_TEST;
		offlineFlag = TRUE;
	 }
	if( strstr( CmdLine, "PROC_BATTLE" ) )                               //MLHIDE
	{
		ProcNo = PROC_BATTLE;
		offlineFlag = TRUE;
	}
#ifdef PUK2
	if( strstr( CmdLine, "g_idview" ) )                                  //MLHIDE
	{
	 	ProcNo = PROC_G_ID_VIEW;
		offlineFlag = TRUE;
	}
#endif
	if( strstr( CmdLine, "sprview" ) )                                   //MLHIDE
	{
	 	ProcNo = PROC_SPR_VIEW;
		offlineFlag = TRUE;
	}
	if( strstr( CmdLine, "animview" ) )                                  //MLHIDE
	{
	 	ProcNo = PROC_ANIM_VIEW;
		offlineFlag = TRUE;
	}
	if( strstr( CmdLine, "setest" ) )                                    //MLHIDE
	{
	 	ProcNo = PROC_SE_TEST;
		offlineFlag = TRUE;
	}
	if( strstr( CmdLine, "encountoff" ) )                                //MLHIDE
	{
		EncountOffFlag = TRUE;
	}
	if( strstr( CmdLine, "logwindow" ) )                                 //MLHIDE
	{
		debugLogWindow = TRUE;
	}
	if( strstr( CmdLine, "D3dTestProc" ) )                               //MLHIDE
	{
	 	ProcNo = PROC_D3D_TEST;
		offlineFlag = TRUE;
	}
	if( strstr( CmdLine, "sleep" ) )                                     //MLHIDE
	{
		WinSleepFlg = TRUE;	// Window生成时にスリープを入れる。
	}

#ifdef _CG2_NEWGRAPHIC
	// 読み込みファイル名の变更
	if( addr = strstr( CmdLine, "version:" ) )                           //MLHIDE
	{
		sscanf( addr+strlen( "version:" ), "%hhu", &CG2PackageVer );        //MLHIDE
	#ifdef PUK2
		switch(CG2PackageVer){
		case 0:	CG2PackageVer = 0;	break;
		case 1:	CG2PackageVer = 3;	break;
		case 2:	CG2PackageVer = 5;	break;
		case 3:	CG2PackageVer = 7;	break;
		#ifdef PUK3_UPGRADE
			case 4:	CG2PackageVer = 8;	break;
		#endif
		}
	#else
		if(CG2PackageVer == 1) CG2PackageVer = 3;
		if(CG2PackageVer == 2) CG2PackageVer = 5;
	#endif
	}
#endif

#ifdef _LOG_MSG
	if( strstr( CmdLine, "RECVDATA:" ) )                                 //MLHIDE
	{
		char *pt = strstr( CmdLine, "RECVDATA:" )+9;                        //MLHIDE
		char *name;
		int i;

	#ifdef PUK3_RECVDATA
		name = debugLogFileName_base;
	#else
		name = debugLogFileName;
	#endif
		for( i = 0; i < 255; i++ )
		{
			if( *pt != ' ' && *pt != '\0' && *pt != '\t' )
			{
				*name++ = *pt++;
			}
			else
			{
				break;
			}
		}
		*name = '\0';
	}
	#ifdef PUK3_RECVDATA
		// recvdata.txtの名称を决定
		sprintf( debugLogFileName, debugLogFileName_base, 1 );
	#endif
#endif
	// デバックキー解析
	DebugKey( CmdLine );
#else
	// デフォルトの场合はこれ
	ProcNo = PROC_ID_PASSWORD;
#endif
	// コマンドラインのIPアドレス解析
	ipAddressAnalyze( CmdLine );
}

/* ウィンドウモード切り替え ***************************************************/
void ChangeWindowMode( void )
{
	RECT clientRect;		// クライアントの表示领域
	DWORD windowStyle;		// ウィンドウスタイル

	// ウィンドウモードの时
	if( WindowMode )
	{
		// ウィンドウスタイル
		windowStyle = WS_MINIMIZEBOX | WS_SYSMENU |
						//WS_THICKFRAME |
						WS_CAPTION | WS_OVERLAPPED;

		// ウィンドウ作成时でなかったら
		if( DDinitFlag )
		{
			// マウスカーソルを表示
			//ShowCursor( TRUE );
		}

		// Windows 2000 と Me のユーザー设定デスクトップパレットを强制的に变更する。
		SetUserDeskTopClolor();

	}
	// フルスクリーンの时
	else
	{
		// ウィンドウスタイル
		windowStyle = WS_VISIBLE | WS_POPUP;
		// マウスカーソルを隠す
		//ShowCursor( FALSE );
	}

	// クライアント领域の设定
#ifdef PUK3_WINDOW_SIZE_DIFF
	if( WindowMode ){
		SetRect( &clientRect, 0, 0, WindowSize[0], WindowSize[1] );
	}else{
		SetRect( &clientRect, 0, 0, DEF_APPSIZEX, DEF_APPSIZEY );
	}
#else
	SetRect( &clientRect, 0, 0, DEF_APPSIZEX, DEF_APPSIZEY );
#endif
	// クライアント领域のサイズからウィンドウサイズを计算し clientRect へ入れる
	AdjustWindowRectEx( &clientRect, windowStyle, FALSE, NULL );
	// ウィンドウを作成するとき
	//if( !DDinitFlag )	// DirectDrawが初期化されてないとき
	if( hWnd == NULL )	// DirectDrawが初期化されてないとき
	{
		if( WindowMode )
		{
			// ウインドウの生成
			hWnd = CreateWindowEx( 	NULL,
								DEF_APPNAME,
								DEF_APPNAME,
								windowStyle,
								//CW_USEDEFAULT,
								//CW_USEDEFAULT,
								//0,
								//0,
								winX,
								winY,
								clientRect.right - clientRect.left,
								clientRect.bottom - clientRect.top,
								NULL,
								NULL,
								hInst,
								NULL );
		}
		else
		{
			// ウインドウの生成
			hWnd = CreateWindowEx(	NULL,
									DEF_APPNAME,
									DEF_APPNAME,
									windowStyle,
									//CW_USEDEFAULT,
									//CW_USEDEFAULT,
									0,
									0,
									clientRect.right - clientRect.left,
									clientRect.bottom - clientRect.top,
									NULL,
									NULL,
									hInst,
									NULL );
		}
	}
	// ウィンドウモードを变更するとき
	else
	{
		// ウィンドウスタイルの变更
		SetWindowLong( hWnd, GWL_STYLE, windowStyle );
		// 作成したウインドウを表示
		ShowWindow( hWnd, CmdShow );
		if( WindowMode )
		{
			// ウィンドウの位置とサイズの变更
			SetWindowPos(	hWnd,
							HWND_NOTOPMOST,
							//0,
							//0,
							winX,
							winY,
							clientRect.right - clientRect.left,
							clientRect.bottom - clientRect.top,
							//SWP_SHOWWINDOW );
							SWP_FRAMECHANGED );
		}
		else
		{
			// ウィンドウの位置とサイズの变更
			SetWindowPos(	hWnd,
							HWND_NOTOPMOST,
							0,
							0,
							clientRect.right - clientRect.left,
							clientRect.bottom - clientRect.top,
							//SWP_SHOWWINDOW );
							SWP_FRAMECHANGED );
		}
	}
#ifdef _DEBUG
	if( WinSleepFlg && FirstSleepFlg ){
		Sleep( 2000 );
		FirstSleepFlg = FALSE;
	}
#endif
	// 作成したウインドウを表示
	ShowWindow( hWnd, CmdShow );

	// ウィンドウを表示更新
	UpdateWindow( hWnd );
}
#ifdef PUK2_3DDEVICECHANGE_MISS_MSG

HWND msghWnd = NULL;
UINT_PTR msgTimerId;
LRESULT CALLBACK msgWindMsgProc( HWND hWnd, UINT Message, WPARAM wParam, LPARAM lParam )
{

	switch( Message ){
	case WM_TIMER:
		DestroyWindow(hWnd);
		break;
	case WM_DESTROY:
		KillTimer( hWnd,  msgTimerId );
		msgTimerId = 0;
		DefWindowProc( hWnd, Message, wParam, lParam );
		break;
	default:
		// 未处理信息をデフォルト处理关数へ渡して处理させる
		return DefWindowProc( hWnd, Message, wParam, lParam );
	}
	return 0;
}

HWND MakeNonOKMessageBox( char *msg, char *title, int w, int h, int time )
{
	HWND hwndc1;
	HDC hdc;
	RECT rc;
	HGDIOBJ hobj;
	int x, y;
	SIZE size = {0};
	char *p, *bp;

	if (msghWnd) DestroyWindow( msghWnd );

	// ウィンドウの真中を取得
	GetWindowRect( hWnd, &rc );
	x = rc.left + (rc.right-rc.left)/2;
	y = rc.top + (rc.bottom-rc.top)/2;

	// ウインドウの生成
	msghWnd = CreateWindowEx(	NULL,
							"CROSSGATE NONOKMSGBOX",                                       //MLHIDE
							title,
							WS_CAPTION | WS_OVERLAPPED,
							0, 0, w, h, NULL, NULL, hInst, NULL );
	if ( msghWnd == NULL ) return msghWnd;

	// 文字列の高さと幅の取得
	hdc = GetDC(msghWnd);

	hobj = SelectObject( hdc, GetStockObject(DEFAULT_GUI_FONT) );

	GetTextExtentExPoint( hdc, msg, strlen(msg), 0, NULL, NULL, &size );

	w = 0;
	h = 13 + 15;

	// 幅がうまく取得できないので
	bp = msg;
	while( p = strchr( bp , '\n' ) ){
		size.cx = size.cy = 0;
		GetTextExtentPoint32( hdc, bp, (int)p - (int)bp, &size );
		if ( size.cx > w ) w = size.cx;
		h += size.cy;
		bp = p + 1;
	}
	GetTextExtentPoint32( hdc, bp, strlen(bp), &size );
	if ( size.cx > w ) w = size.cx;
	h += size.cy;
	w = w + 10 * 2;

	SelectObject( hdc, hobj );

	ReleaseDC( msghWnd, hdc );

	// スタティックコントロールの作成
	hwndc1 = CreateWindow( "STATIC", title, WS_CHILD|WS_VISIBLE|SS_NOPREFIX, //MLHIDE
		 10, 13, w, h, msghWnd, (HMENU)100, hInst, NULL );
	// フォントの设定
	SendMessage( hwndc1, WM_SETFONT, (WPARAM)GetStockObject(DEFAULT_GUI_FONT), FALSE );
	if ( !hwndc1 ){
		DestroyWindow( msghWnd );
		msghWnd = NULL;
		return msghWnd;
	}

	// 信息の设定
	SetWindowText( hwndc1, msg );

	GetWindowRect( hwndc1, &rc );

	rc.left = x - w/2;
	rc.top = y - h/2;
	rc.right = rc.left + w;
	rc.bottom = rc.top + h;
	// クライアント领域のサイズからウィンドウサイズを计算し clientRect へ入れる
	AdjustWindowRectEx( &rc, WS_CAPTION | WS_OVERLAPPED, FALSE, NULL );

	SetWindowPos( msghWnd, HWND_NOTOPMOST,
		 rc.left, rc.top, rc.right-rc.left, rc.bottom-rc.top,
		 SWP_ASYNCWINDOWPOS );

	msgTimerId = SetTimer( msghWnd, 1, time, NULL );

	// 作成したウインドウを表示
	ShowWindow( msghWnd, SW_SHOW );
	// ウィンドウを表示更新
	UpdateWindow( msghWnd );

	return msghWnd;
}

#endif

#if 0
// Windowsの信息をフックする
HRESULT CALLBACK HookProc( int nCode, WPARAM wParam, LPARAM lParam )
{
	int a = 0;

	switch( nCode ){
	//switch( wParam ){
		case MSGF_NEXTWINDOW:
		//case HCBT_SYSCOMMAND:
			a++;
			break;

		case HCBT_ACTIVATE:
			a++;
			break;
	}
	return 0;
}
#endif

// ＤｉｒｅｃｔＤｒａｗ复活处理 **********************************************************/
void RecoverDirectDraw( void )
{
	#ifdef PUK2
		BOOL LimiteLoadBmpFlagBack = LimiteLoadBmpFlag;
	#endif
	#ifdef DIRECT3D_ON
	// Direct3Dの保持モードを終了します
	ReleaseDirect3DRetainedMode();
	#endif
	// DirectDraw 开放
	ReleaseDirectDraw();
	// DirectDraw 初期化
#ifdef PUK2
	switch( InitDirectDraw() ){
	case 1:
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_101, "确定", MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
		MessageBox( hWnd, "DirectDraw初始化失败", "确定", MB_OK | MB_ICONSTOP );   //MLHIDE
	#endif
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
		break;
	case 2:
		// ウィンドウモードフラグ变更
		if( WindowMode == TRUE ) WindowMode = FALSE;
		else WindowMode = TRUE;
		if( InitDirectDraw() ){
	#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_102, "确定", MB_OK | MB_ICONSTOP );         //MLHIDE
	#else
			MessageBox( hWnd, "DirectDraw初始化失败", "确定", MB_OK | MB_ICONSTOP );  //MLHIDE
	#endif
			// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			PostMessage( hWnd, WM_CLOSE, 0, 0L );
		}
		break;
	}
#else
	if( InitDirectDraw() == FALSE ){
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_103, "确定", MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
		MessageBox( hWnd, "DirectDraw初始化失败", "确定", MB_OK | MB_ICONSTOP );   //MLHIDE
	#endif
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
	}
#endif
	// ウィンドウモード切り替え
	//ChangeWindowMode();
	// OFFスクリーンサーフェスの作成
	InitOffScreenSurface();
	// パレット初期化
	if( InitPalette() == FALSE ){
		//PostMessage( hWnd, WM_CLOSE, 0, 0L );
		PostMessage( hWnd, WM_SYSKEYDOWN, VK_RETURN, 0L );
	}
	// バトルマップ読み込みとバトルサーフェスの画像作成
	if( ProcNo == PROC_BATTLE ){
		// 演出画像の作成
		// バッファ初期化
		DispBuffer.DispCnt = 0;
		FontCnt = 0;
#ifdef PUK2
		// フォントプライオリティ制御バッファの初期化
		FontPrioInit();
#endif

		//StockTaskDispBuffer();	// タスク表示データをバッファに溜める

		initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
				// 背景
#ifdef PUK3_BATTLEMAP
				drawMap_Sugi(FALSE);
#else
				drawMap3();				//	マップ表示
#endif

		// 背景
		StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, BattleMapNo, 0 );
		// グリッド枠
		//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18028, 0 );
		// グリッド（灰色）
		//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18009, 0 );
		// メッシュ（セピア暗）
		//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18013, 0 );


		SortDispBuffer(); 	// 表示バッファソート
		// バックサーフェスを黒でクリアー
		ClearBackSurface();
	#ifdef PUK2
		// 色变え制限を一时的にＯＦＦ
		LimiteLoadBmpFlag = FALSE;
	#endif
		// ＢＭＰをバックサーフェスにセット
		PutBmp();
		// バックサーフェスからバトルサーフェスへコピー
		lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
		// 色变え制限を戾す
		LimiteLoadBmpFlag = LimiteLoadBmpFlagBack;
	#endif
		// バッファ初期化
		DispBuffer.DispCnt = 0;
		FontCnt = 0;
#ifdef PUK2
		// フォントプライオリティ制御バッファの初期化
		FontPrioInit();
#endif

		// 现在の时间を记忆
		NowTime = GetTickCount();
#ifdef PUK2_FPS
		NowDrawTime = NowTime;
#endif
	}
	else
	if( ProcNo == PROC_GAME )
	{
		repairMap();	// マップ画面の修复
	}
	// パレットオブジェクトがある时
	if( lpDraw->lpPALETTE != NULL ){
		// ウィンドウモードの时
		if( WindowMode ){
			// 作成しておいたパレットに变える
			lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
		}
	}

	#ifdef DIRECT3D_ON
	// Direct3Dの保持モードを初期化します
	if( InitDirect3DRetainedMode() == FALSE ) {
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_104, "确定", MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
		MessageBox( hWnd, "DirectDraw初始化失败", "确定", MB_OK | MB_ICONSTOP );   //MLHIDE
	#endif
		//return FALSE;
	}
	#endif

#ifdef PUK2
	// SprPal解放
	RelSprPal();
	// SprPal初期化
	InitSprPal();
#endif

	// マウスクリック入力の初期化
	mouse.state = MOUSE_NO_CRICK;
	mouse.onceState = MOUSE_NO_CRICK;
	// 时间の遅れ忘れさせる
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif
}
//---------------------------------------------------------------------------//
// 概要 ：信息ループ处理关数                                                     //
// 引数 ：なし                                                               //
//---------------------------------------------------------------------------//
BOOL SystemTask( void )
{
	MSG msg;

	// ☆★☆★☆ 信息ループ ☆★☆★☆
	// 信息がある间ループ
	while( PeekMessage( &msg, NULL, 0, 0, PM_REMOVE )){
#ifndef _DEBUG
		// 信息を受け取る、WM_QUIT を受け取るとFALSEが返ってきて終了
		//if( GetMessage( &msg, NULL, 0, 0 ) == FALSE ) return FALSE;
		// WM_QUIT を受け取ると終了
		if( msg.message == WM_QUIT ) return FALSE;

		TranslateMessage(&msg);		// 变换が必要な信息は变换する

		DispatchMessage(&msg);		// ウィンドウプロシージャへ信息を送る
#else
		if( debugLogWindow )
		{
			if( !hLogWindow || !IsDialogMessage( hLogWindow, &msg ) )
			{
				if( msg.message == WM_QUIT ) return FALSE;
				TranslateMessage(&msg);		// 变换が必要な信息は变换する
				DispatchMessage(&msg);		// ウィンドウプロシージャへ信息を送る
			}
		}
		else
		{
			if( msg.message == WM_QUIT ) return FALSE;
			TranslateMessage(&msg);		// 变换が必要な信息は变换する
			DispatchMessage(&msg);		// ウィンドウプロシージャへ信息を送る
		}
#endif
	}
#ifdef PUK2_MEMCHECK
	if ( keyOnOnceWithCtrl(VK_NUMPAD0) ) writememlist();
#endif
	return TRUE;
}

//---------------------------------------------------------------------------//
// 概要 ：ウインドウ信息处理关数                                       //
// 引数 ：HWND hWnd:     ウィンドウの识别ハンドル                            //
//        UINT Message:  处理する制御信息                              //
//        WPARAM wParam: 处理する制御信息补足情报１					 //
//        LPARAM lParam: 处理する制御信息补足情报２                    //
//---------------------------------------------------------------------------//
LRESULT CALLBACK WindMsgProc( HWND hWnd, UINT Message, WPARAM wParam, LPARAM lParam )
{
#ifdef PUK3_WINDOW_SIZE_DIFF
	static int sizeChangeCnt = 0;
#endif

	switch( Message ){
		//case MSGF_NEXTWINDOW: // ALT+TAB 押したとき
			//SetActiveWindow( hWnd );
		//	{
		//		int a;
		//		a++;
		//	}
		//	break;


/** システム关连信息 *****************************************************/

#ifdef _DEBUG
		case WM_COMMAND:

			// キーボードクリア２ KEY_ON もクリアする
			KeyBoardClear2();


			break;
#endif
#ifdef _DEBUG
		case WM_CREATE:
			// リアルタイムログウィンドウを表示
			if( debugLogWindow )
				openLogWindow();
			break;
#endif

		case WM_ACTIVATE:	// このアプリケーションがアクティブ又は非アクティブになった时

			// キーボードクリア２ KEY_ON もクリアする
			KeyBoardClear2();

			// ウィンドウがアクティブにされるとき
			if( ( wParam == WA_ACTIVE || wParam == WA_CLICKACTIVE ) && lpDraw != NULL){

#ifdef PUK3_WINDOWMODE_BLACKOUT
				// サーフェスが LOST していたら
				if( CheckSurfaceLost() == TRUE ){
					// この场でDirectDrawの复归作业をすると、
					// デバイスが准备できていなくて
					// プライマリサーフェースが作成できない恐れがあるため
					PrimaryLostFlag = TRUE;
				}
#else
				// サーフェスが LOST していたら
				if( CheckSurfaceLost() == TRUE ){
					// ＤｉｒｅｃｔＤｒａｗ复活处理
					RecoverDirectDraw();
				}
#endif
				// パレットオブジェクトがある时
				if( lpDraw->lpPALETTE != NULL ){
					// ウィンドウモードの时
					if( WindowMode ){
						// 作成しておいたパレットに变える
						lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
					}
				}
			}
#ifdef PUK2_NOSHOW
			if ( CmdLineFlg & CMDLINE_ONLYACTIVEBLT ){
				WinActive = WA_NOACTIVATE;
				// ウィンドウがアクティブにされるとき
				if ( wParam==WA_ACTIVE || wParam==WA_CLICKACTIVE ) WinActive = WA_ACTIVE_JUST_AFTER;
			}

			// 现在の时间を记忆
			NowTime = GetTickCount();
	#ifdef PUK2_FPS
			NowDrawTime = NowTime;
	#endif
#endif



			break;
#ifdef PUK2_NOSHOW
		case WM_SIZE:	// ウィンドウのサイズが变わったとき(ウィンドウが最小化された、解除されたとき)

			WinActive = WA_ACTIVATE;
			// ウィンドウが最小化したとき
			if ( wParam == SIZE_MINIMIZED ) WinActive = WA_NOACTIVATE;

			// 现在の时间を记忆
			NowTime = GetTickCount();
	#ifdef PUK2_FPS
			NowDrawTime = NowTime;
	#endif
	#ifdef PUK3_WINDOW_SIZE_DIFF
			nowWindowSize[0] = LOWORD(lParam);
			nowWindowSize[1] = HIWORD(lParam);
			if ( WindowMode ){
				// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
				if ( nowWindowSize[0] != WindowSize[0] ||
					 nowWindowSize[1] != WindowSize[1] ){
					if ( sizeChangeCnt == 0 ){
						RECT rc;

						sizeChangeCnt++;

						GetWindowRect( hWnd, &rc );
						SetWindowPos( hWnd, HWND_NOTOPMOST, 0, 0,
							 (rc.right - rc.left) + (WindowSize[0] - nowWindowSize[0]),
							 (rc.bottom - rc.top) + (WindowSize[1] - nowWindowSize[1]),
							 SWP_NOMOVE | SWP_NOZORDER | SWP_FRAMECHANGED );
					}else if ( sizeChangeCnt == 1 ){
						RECT clientRect;
						DWORD windowStyle;

						sizeChangeCnt++;

						windowStyle = GetWindowLong( hWnd, GWL_STYLE );
						if( WindowMode ){
							SetRect( &clientRect, 0, 0, WindowSize[0], WindowSize[1] );
						}else{
							SetRect( &clientRect, 0, 0, DEF_APPSIZEX, DEF_APPSIZEY );
						}
						// クライアント领域のサイズからウィンドウサイズを计算し clientRect へ入れる
						AdjustWindowRectEx( &clientRect, windowStyle, FALSE, NULL );

						SetWindowPos( hWnd, HWND_NOTOPMOST, 0, 0,
							 clientRect.right - clientRect.left,
							 clientRect.bottom - clientRect.top,
							 SWP_NOMOVE | SWP_NOZORDER | SWP_FRAMECHANGED );
					}
				}else{
					sizeChangeCnt = 0;
				}
			}
	#endif
			break;
#endif

//		case WM_ACTIVATEAPP: // 制御がこのアプリケーションに移った时

			// ALT+TAB切り替えにてLOSTした情报を修复
			//if( ( wParam ) && lpDraw != NULL ){
			//break;

		case WM_CLOSE:				// ウィンドウ关闭とき

#ifdef _DEBUG
			if( debugLogWindow )
				closeLogWindow();
#endif
			DestroyWindow( hWnd );	// ウィンドウを破弃する、同时に
									// WM_DESTROY 信息が送られる
			break;

		case WM_DESTROY:			// ウィンドウが破弃されたとき

#ifdef PUK2_3DDEVICECHANGE_MISS_MSG
			if ( msghWnd ) DestroyWindow( msghWnd );
#endif
			PostQuitMessage( 0 );	// WM_QUIT 信息を送る ( 处理の終了 )
			break;

#if 0
		case WM_PALETTECHANGED:		// パレットが变更されたとき
			// DirectDrawオブジェクトがない时
			if( lpDraw == NULL ) break;
			// パレットオブジェクトがない时
			if( lpDraw->lpPALETTE == NULL ) break;
			// ウィンドウモードの时
			//if( WindowMode ){
				// 作成しておいたパレットに变える
				lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			//}
			break;
			//if( (HWND)wParam == hWnd )  break;

		case WM_PALETTEISCHANGING:		// パレットが变更されたとき
			// DirectDrawオブジェクトがない时
			if( lpDraw == NULL ) break;
			// パレットオブジェクトがない时
			if( lpDraw->lpPALETTE == NULL ) break;
			// ウィンドウモードの时
			//if( WindowMode ){
				// 作成しておいたパレットに变える
				lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			//}
			break;

		case WM_QUERYNEWPALETTE:	// パレット变更できるとき（ウィンドウモードの时のみ）

			// DirectDrawオブジェクトがない时
			if( lpDraw == NULL ) break;
			// パレットオブジェクトがない时
			if( lpDraw->lpPALETTE == NULL ) break;

			// ウィンドウモードの时
			//if( WindowMode ){
				// 作成しておいたパレットに变える
				lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
			//}

			break;
#endif

/** キー入力信息 *****************************************************/

      	case WM_KEYDOWN:	// キー入力处理

			// サーフェスヴィジーの时
			if( SurfaceBusyFlag == TRUE ){
				SurfaceBusyFlag = FALSE;
				//MessageBox( hWnd, "修复损坏的图像。", "????????", MB_OK | MB_ICONSTOP );

				// ＤｉｒｅｃｔＤｒａｗ复活处理
				RecoverDirectDraw();
			}
#ifdef PUK2_NOSHOW
			if ( WinActive != WA_ACTIVATE ) break;
#endif
			VK[ wParam ] |= KEY_ON;			// 押しっぱなしＯＮ
			if( !(VK[ wParam ] & KEY_ON_ONCE_FLAG) )
			{
				// 一度离すまでKEY_ON_REPはＯＮにならない
				VK[ wParam ] |= (KEY_ON_ONCE | KEY_ON_ONCE_FLAG);	// 押した时だけＯＮ
			}
			VK[ wParam ] |= KEY_ON_REP;		// 押した时だけＯＮ（リピート）

		break;

      	case WM_KEYUP:	// キー入力处理

			// サーフェスヴィジーの时
			if( SurfaceBusyFlag == TRUE ){
				SurfaceBusyFlag = FALSE;
				//MessageBox( hWnd, "修复损坏的图像。", "????????", MB_OK | MB_ICONSTOP );

				// ＤｉｒｅｃｔＤｒａｗ复活处理
				RecoverDirectDraw();
			}
			VK[ wParam ] &= ~KEY_ON;	// 押しっぱなしＯＦＦ
			VK[ wParam ] |= KEY_OFF;	// 押しっぱなしＯＦＦ
			VK[ wParam ] &= ~KEY_ON_ONCE_FLAG;

		break;

#ifndef _DEBUG
		case WM_SYSCOMMAND:	// システムコマンド受け取り

			// コマンドの种类で分岐
			switch( wParam ){

				case SC_CLOSE:	// 关闭ボタン押した时（ＡＬＴ＋Ｆ４も）

					// ウィンドウモードの时だけ、終了????????する。
					if( WindowMode == TRUE ){
						if( MessageBox( hWnd, ML_STRING(992, "是否结束魔力宝贝游戏？"), ML_STRING(325, "确定"), MB_OKCANCEL ) == IDCANCEL ){
							// 现在の时间を记忆
							NowTime = GetTickCount();
#ifdef PUK2_FPS
							NowDrawTime = NowTime;
#endif
							// この信息は无かった事にする
							return 0;
						}
					}
				break;
			}
			// 未处理信息をデフォルト处理关数へ渡して处理させる
			return DefWindowProc( hWnd, Message, wParam, lParam );
		break;
#endif

		case WM_SYSKEYDOWN:	// ALTキー押されたとき

			// サーフェスヴィジーの时
			if( SurfaceBusyFlag == TRUE ){
				SurfaceBusyFlag = FALSE;
				//MessageBox( hWnd, "修复损坏的图像。", "????????", MB_OK | MB_ICONSTOP );

				// ＤｉｒｅｃｔＤｒａｗ复活处理
				RecoverDirectDraw();
			}

			switch( wParam ){

//#ifdef _DEBUG
				case VK_RETURN:	// ALT+RETURN でウィンドウモード变更

					// 演出中は拔ける
					if( BackBufferDrawType == DRAW_BACK_PRODUCE ) break;

					// DirectDrawオブジェクトがない时
					if( lpDraw == NULL ) break;

					// ウィンドウモードフラグ变更
					if( WindowMode == TRUE ) WindowMode = FALSE;
					else WindowMode = TRUE;

					// 全ウィンドウに再描画要求
					//SendMessage( HWND_BROADCAST, WM_PAINT, NULL, NULL );

					// ＤｉｒｅｃｔＤｒａｗ复活处理
					RecoverDirectDraw();

					// ウィンドウモード切り替え
					ChangeWindowMode();

					// 时间の遅れ忘れさせる
					NowTime = GetTickCount();
#ifdef PUK2_FPS
					NowDrawTime = NowTime;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
					sizeChangeCnt = 0;
#endif

					break;
//#endif


				case VK_F10:
					VK[ wParam ] |= KEY_ON;			// 押しっぱなしＯＮ
					if( !(VK[ wParam ] & KEY_ON_ONCE_FLAG) )
					{
						// 一度离すまでKEY_ON_REPはＯＮにならない
						VK[ wParam ] |= (KEY_ON_ONCE | KEY_ON_ONCE_FLAG);	// 押した时だけＯＮ
					}
					VK[ wParam ] |= KEY_ON_REP;		// 押した时だけＯＮ（リピート）
					break;

				default:
					// 未处理信息をデフォルト处理关数へ渡して处理させる
					return DefWindowProc( hWnd, Message, wParam, lParam );
			}
			break;

      	case WM_CHAR:	// 文字入力处理

				// 改行コードの时拔ける
				if( ( char )wParam == 0x0d ) break;
				// 文字バッファに溜める
				StockInputStrChar( ( char )wParam );

			break;

/** マウス入力信息 *****************************************************/

#ifdef PUK3_MOUSECURSOR
		case WM_SETCURSOR:
			if ( LOWORD(lParam) == HTCLIENT ){
				SetCursor(MouseCursorType_HANDL[nowMouseType]);
				return TRUE;
			}else{
				return DefWindowProc( hWnd, Message, wParam, lParam );
			}
			break;
#endif
		//case WM_SETCURSOR:		// マウスカーソルセットする时

			//SetCursor( wndclass.hCursor );
		//	break;

		case WM_MOUSEMOVE:		// マウスを动かした时

			//SetCursor( wndclass.hCursor );
#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 现在のマウスの位置を记忆する
				MouseNowPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 现在のマウスの位置を记忆する
				MouseNowPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 现在のマウスの位置を记忆する
			MouseNowPoint( LOWORD(lParam), HIWORD(lParam) );
#endif
			if( mouse.flag == TRUE ){
				ShowCursor( FALSE ); // マウスカーソルを消す
				mouse.flag = FALSE;
			}
			// サーフェスヴィジーの时
			if( SurfaceBusyFlag == TRUE ){
				SurfaceBusyFlag = FALSE;
				//MessageBox( hWnd, "修复损坏的图像。", "????????", MB_OK | MB_ICONSTOP );

				// ＤｉｒｅｃｔＤｒａｗ复活处理
				RecoverDirectDraw();
			}

			break;

		case WM_NCMOUSEMOVE:	// マウスがウィンドウからはみ出た时

			// マウスクリック入力の初期化
			mouse.state = MOUSE_NO_CRICK;
			mouse.onceState = MOUSE_NO_CRICK;
			if( mouse.flag == FALSE ){
				ShowCursor( TRUE ); // マウスカーソルを表示
				mouse.flag = TRUE;
			}

			break;

		case WM_LBUTTONDOWN:	// 右键(押した时)

#ifdef PUK2_NOSHOW
			if ( WinActive != WA_ACTIVATE ) break;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 右键押した时のマウスの位置を记忆する
				MouseCrickLeftDownPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 右键押した时のマウスの位置を记忆する
				MouseCrickLeftDownPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 右键押した时のマウスの位置を记忆する
			MouseCrickLeftDownPoint( LOWORD(lParam), HIWORD(lParam) );
#endif

			break;

		case WM_LBUTTONUP:		// 右键(离した时)

#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 右键离した时のマウスの位置を记忆する
				MouseCrickLeftUpPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 右键离した时のマウスの位置を记忆する
				MouseCrickLeftUpPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 右键离した时のマウスの位置を记忆する
			MouseCrickLeftUpPoint( LOWORD(lParam), HIWORD(lParam) );
#endif

			break;

		case WM_LBUTTONDBLCLK:		// 左ダブルクリック

#ifdef PUK2_NOSHOW
			if ( WinActive != WA_ACTIVATE ) break;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 左ダブルクリックされた时のマウスの位置を记忆する
				MouseDblCrickLeftUpPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 左ダブルクリックされた时のマウスの位置を记忆する
				MouseDblCrickLeftUpPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 左ダブルクリックされた时のマウスの位置を记忆する
			MouseDblCrickLeftUpPoint( LOWORD(lParam), HIWORD(lParam) );
#endif

			break;

		case WM_RBUTTONDOWN:	// 右クリック(押した时)

#ifdef PUK2_NOSHOW
			if ( WinActive != WA_ACTIVATE ) break;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 右クリック押した时のマウスの位置を记忆する
				MouseCrickRightDownPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 右クリック押した时のマウスの位置を记忆する
				MouseCrickRightDownPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 右クリック押した时のマウスの位置を记忆する
			MouseCrickRightDownPoint( LOWORD(lParam), HIWORD(lParam) );
#endif

			break;

		case WM_RBUTTONUP:		// 右クリック(离した时)

#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 右クリック离した时のマウスの位置を记忆する
				MouseCrickRightUpPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 右クリック离した时のマウスの位置を记忆する
				MouseCrickRightUpPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 右クリック离した时のマウスの位置を记忆する
			MouseCrickRightUpPoint( LOWORD(lParam), HIWORD(lParam) );
#endif

			break;

		case WM_RBUTTONDBLCLK:		// 右ダブルクリック

#ifdef PUK2_NOSHOW
			if ( WinActive != WA_ACTIVATE ) break;
#endif
#ifdef PUK3_WINDOW_SIZE_DIFF
			// ウィンドウのサイズチェック(ウィンドウモードのときのみ)
			if ( nowWindowSize[0] != DEF_APPSIZEX ||
				 nowWindowSize[1] != DEF_APPSIZEY ){
				// 右ダブルクリックされた时のマウスの位置を记忆する
				MouseDblCrickRightUpPoint(
					 (int)( LOWORD(lParam) * (float)DEF_APPSIZEX / nowWindowSize[0] ),
					 (int)( HIWORD(lParam) * (float)DEF_APPSIZEY / nowWindowSize[1] ) );
			}else{
				// 右ダブルクリックされた时のマウスの位置を记忆する
				MouseDblCrickRightUpPoint( LOWORD(lParam), HIWORD(lParam) );
			}
#else
			// 右ダブルクリックされた时のマウスの位置を记忆する
			MouseDblCrickRightUpPoint( LOWORD(lParam), HIWORD(lParam) );
#endif

			break;

		case WM_MBUTTONDOWN:	// 中クリック(押した时)
			break;
#ifdef PUK2
		case WM_MOUSEWHEEL:		// マウスホイール
#ifdef PUK2_NOSHOW
			if ( WinActive != WA_ACTIVATE ) break;
#endif
			// この关数は、ホイールを奥に回すのが负の数值なので
			MouseWheel( -( (short)HIWORD(wParam) ) );
			break;
#endif

/** ＩＭＥ关连信息处理 **************************************************/

		case WM_IME_STARTCOMPOSITION: // ＩＭＥのちっちゃいウィンドウが出时

			break;

		case WM_IME_COMPOSITION: // ＩＭＥのちっちゃいウィンドウに变化がある时

			// WM_IME_COMPOSITION 信息の处理
			ImeComposition( lParam );

			break;

		case WM_IME_ENDCOMPOSITION: // 文字を确定した直后に来る。变换中の文字を全部BSでけしたときもくる。
			// ＩＭＥバッファーを空にする
			ImeInfo.buffer[ 0 ] = NULL;
			break;

		case WM_IME_NOTIFY:
			/* WM_IME_NOTIFY 信息の处理 */
#ifdef PUK3_IME_ATOK
			switch(wParam){
			case IMN_SETCONVERSIONMODE:			// ＩＭＥの变换モードの变更
			case IMN_SETOPENSTATUS:				// ＩＭＥのＯＮ／ＯＦＦ变更
			case IMN_SETSENTENCEMODE:			// ＩＭＥの变换モードの变更
			case IMN_SETSTATUSWINDOWPOS:		// ＩＭＥウィンドウの状态变更
			case IMN_OPENSTATUSWINDOW:			// ＩＭＥが状态ウィンドウを表示しようとしている
			case IMN_CLOSESTATUSWINDOW:			// 状态ウィンドウを关闭
			case IMN_SETCOMPOSITIONFONT:		// 入力文字のフォント变更
			case IMN_SETCOMPOSITIONWINDOW:		// 构成ウィンドウの变更
			case IMN_GUIDELINE:					// ＩＭＥがエラー信息か他の情报を表示したがっている
				return DefWindowProc( hWnd, Message, wParam, lParam );

			case IMN_CHANGECANDIDATE:			// 候补ウィンドウの内容变更
			case IMN_CLOSECANDIDATE:			// 候补ウィンドウを关闭
			case IMN_OPENCANDIDATE:				// ＩＭＥが候补ウィンドウを表示しようとしている
			case IMN_SETCANDIDATEPOS:			// ＩＭＥが候补ウィンドウを移动しようとしている
				return 0;

			// これら以外にも情报が送られている模样
			case IMN_PRIVATE:
				// ＡＴＯＫで"半角/全角"キーのみでＩＭＥのＯＮ/ＯＦＦができなかったのは
				// ここを处理していなかったのが原因の模样
			default:
				return DefWindowProc( hWnd, Message, wParam, lParam );
			}
#else
			//ImeNotify( wParam ,lParam );
			// ここにDEFWINDOWPROCがあると、变换候补ウインドウを出すようだ。
			// ＩＭＥ入力モードの取得

			// ＩＭＥが初期化されているとき
			if( ImeInfo.hImc != 0 ){
				ImmGetConversionStatus(  ImeInfo.hImc, &ImeInfo.conversion, &ImeInfo.sentence  );
			}
			//ActCnt++;
			//{
			//	char moji[ 256 ];
			//	sprintf( moji, "变换模式编号：%d",ImeInfo.sentence );
			//	MessageBox( hWnd, moji, "确定", MB_OK | MB_ICONSTOP );
			//}
#endif

			break;
#ifdef PUK3_WINCHNG_IMEWINDOW
		case WM_IME_SETCONTEXT:		// ウィンドウが非アクティブからアクティブになって、IMEの变换ウィンドウが生成されるとき
			// IMEウィンドウを表示させたくないので
			lParam &= ~ISC_SHOWUICOMPOSITIONWINDOW;
			lParam &= ~ISC_SHOWUICANDIDATEWINDOW;
			lParam &= ~ISC_SHOWUICANDIDATEWINDOW << 1;
			lParam &= ~ISC_SHOWUICANDIDATEWINDOW << 2;
			lParam &= ~ISC_SHOWUICANDIDATEWINDOW << 3;
			return DefWindowProc( hWnd, Message, wParam, lParam );
#endif

#if 0
		case WM_IME_CHAR:
			// ここにDefWindowProcがあると、WM_CHARを生成する
			break;
		//case WM_IME_SETCONTEXT:
			//初期化したとき来てるのかな？フォーカスが移ったときも来るようだ。
			//DefWindowProc( hWnd, Message, wParam, lParam );
		//	break;
		case WM_IME_STARTCOMPOSITION:// IMEがONの状态で、文字を入力した直后に来る。
		//	DefWindowProc( hWnd, Message, wParam, lParam );
			break;
		case WM_IME_CONTROL:
			break;
		case WM_IME_SELECT: // IMEをえらんだとき来るらしい。バッファを初期化とかすればいいのかな？
			break;
		case WM_IME_KEYDOWN:   // IMEのキーコード
			// ここにDefWindowProcがあると、WM_KEYDOWNを生成する
			break;
#endif

		default:
			// 未处理信息をデフォルト处理关数へ渡して处理させる
			return DefWindowProc( hWnd, Message, wParam, lParam );
	}

	return 0;
}

#ifdef LOOP_ERRMESSAGE_BREAK

// エラー专用の信息ボックス
// 信息ボックスが戾り值としてexitIdを返した场合その场でアプリケーションを終了する
int ErrMessageBox( HWND hWnd, LPCTSTR lpText, LPCTSTR lpCaption, UINT uType, int exitId )
{
	int ret;

	// 信息ボックスを表示
	ret = MessageBox( hWnd, lpText, lpCaption, uType );

	// 戾り值が終了する值なら終了
	if ( ret == exitId ){
		EndGame(); // ゲーム終了处理
		exit(EXIT_FAILURE);
	}

	return ret;
}

#endif
#ifdef PUK3_MEMORYLEAK

int memchecktime;
int memchecktimebf;

void MemCheckTimeClear()
{
	if ( memchecktime >= 50 ){
		ErrMessageBox( hWnd,
			 "内存连接感知程序效率低，1帧需要0.5秒以上。"                                         //MLHIDE
			 "\n点［是］结束程序。",                                                    //MLHIDE
			 "内存连接", MB_YESNO | MB_ICONSTOP, IDYES );                          //MLHIDE

	}
	memchecktimebf = memchecktime;
	memchecktime = 0;
}

void MemCheck()
{
	static BOOL flag = FALSE;
	int time = timeGetTime();

	if ( !flag ){
		if ( !_CrtCheckMemory() ){
			_RPTF1( _CRT_WARN, "内存损坏！！\n\n", 0 );                              //MLHIDE
			ErrMessageBox( hWnd,
				 "感知内存"                                                           //MLHIDE
				 "\n点［是］结束程序。",                                                   //MLHIDE
				 "内存连接", MB_YESNO | MB_ICONSTOP, IDYES );                         //MLHIDE
			flag = TRUE;
		}
		memchecktime += timeGetTime() - time;
	}
}

#endif
#ifdef PUK3_ALLOC

#ifdef PUK3_MEMALLOC_LIMIT
	#define ALLOC_LIMIT_FREE 0
	size_t allocLimit = ALLOC_LIMIT_FREE;
	size_t allocSize = 0;
#endif
void *_rc_malloc( size_t size, char *file, int line )
{
	void *ret;
#ifdef PUK3_MEMALLOC_LIMIT
	if ( allocLimit != ALLOC_LIMIT_FREE &&
		 allocSize + size > allocLimit ){
		return NULL;
	}
	size += sizeof(size_t);
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	ret = _malloc_dbg( size, _NORMAL_BLOCK, file, line );
	memchecktime += timeGetTime() - time;
#else
	ret = _malloc_dbg( size, _NORMAL_BLOCK, file, line );
#endif
	if ( ret == NULL ){
		char str[1024];
		sprintf( str, "  !! allocmiss %10d %s", line, file );               //MLHIDE
		WriteSystemLogfile( str );
	}
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( true, ret, size, file, line );
#endif
#ifdef PUK3_MEMALLOC_LIMIT
	if ( ret ){
		size_t *pi = (size_t *)ret;
		allocSize += size;
		*pi = size;
		return pi + 1;
	}
#endif
	return ret;
}
void *_rc_calloc( size_t num, size_t size, char *file, int line )
{
	void *ret;
#ifdef PUK3_MEMALLOC_LIMIT
	if ( allocLimit != ALLOC_LIMIT_FREE &&
		 allocSize + size > allocLimit ){
		return NULL;
	}
	size += sizeof(size_t);
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	ret = _calloc_dbg( num, size, _NORMAL_BLOCK, file, line );
	memchecktime += timeGetTime() - time;
#else
	ret = _calloc_dbg( num, size, _NORMAL_BLOCK, file, line );
#endif
	if ( ret == NULL ){
		char str[1024];
		sprintf( str, "  !! allocmiss %10d %s", line, file );               //MLHIDE
		WriteSystemLogfile( str );
	}
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( true, ret, size, file, line );
#endif
#ifdef PUK3_MEMALLOC_LIMIT
	if ( ret ){
		size_t *pi = (size_t *)ret;
		allocSize += size;
		*pi = size;
		return pi + 1;
	}
#endif
	return ret;
}

void _rc_free( void *memblock, char *file, int line )
{
	int size = 0;
#ifdef PUK3_MEMALLOC_LIMIT
	size_t *pi = (size_t *)memblock;
	if ( pi ){
		size = *( pi - 1 );
		allocSize -= size;
		memblock = (void *)( pi - 1 );
	}
#endif
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( false, memblock, size, file, line );
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	_free_dbg( memblock, _NORMAL_BLOCK );
	memchecktime += timeGetTime() - time;
#else
	_free_dbg( memblock, _NORMAL_BLOCK );
#endif
}

#ifdef PUK3_MEMALLOCLOG

#undef GlobalAlloc
#undef GlobalAllocPtr
#undef GlobalFree
#undef GlobalFreePtr

#ifndef _MAC
#define     GlobalAllocPtr(flags, cb)        \
                (GlobalLock(GlobalAlloc((flags), (cb))))
#else
#define     GlobalAllocPtr(flags, cb)        \
                (GlobalLock(GlobalAlloc((flags) | GMEM_PMODELOCKSTRATEGY, (cb))))
#endif
#define     GlobalFreePtr(lp)                \
                (GlobalUnlockPtr(lp), (BOOL)GlobalFree(GlobalPtrHandle(lp)))

void *_rc_GlobalAlloc( UINT uFlags, SIZE_T dwBytes, char *file, int line )
{
	void *ret;
#ifdef PUK3_MEMALLOC_LIMIT
	if ( allocLimit != ALLOC_LIMIT_FREE &&
		 allocSize + dwBytes > allocLimit ){
		return NULL;
	}
	dwBytes += sizeof(size_t);
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	ret = GlobalAlloc( uFlags, dwBytes );
	memchecktime += timeGetTime() - time;
#else
	ret = GlobalAlloc( uFlags, dwBytes );
#endif
	if ( ret == NULL ){
		char str[1024];
		sprintf( str, "  !! allocmiss %10d %s", line, file );               //MLHIDE
		WriteSystemLogfile( str );
	}
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( true, ret, dwBytes, file, line );
#endif
#ifdef PUK3_MEMALLOC_LIMIT
	if ( ret ){
		size_t *pi = (size_t *)ret;
		allocSize += dwBytes;
		*pi = dwBytes;
		return pi + 1;
	}
#endif
	return ret;
}

void *_rc_GlobalAllocPtr( UINT uFlags, SIZE_T dwBytes, char *file, int line )
{
	void *ret;
#ifdef PUK3_MEMALLOC_LIMIT
	if ( allocLimit != ALLOC_LIMIT_FREE &&
		 allocSize + dwBytes > allocLimit ){
		return NULL;
	}
	dwBytes += sizeof(size_t);
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	ret = GlobalAllocPtr( uFlags, dwBytes );
	memchecktime += timeGetTime() - time;
#else
	ret = GlobalAllocPtr( uFlags, dwBytes );
#endif
	if ( ret == NULL ){
		char str[1024];
		sprintf( str, "  !! allocmiss %10d %s", line, file );               //MLHIDE
		WriteSystemLogfile( str );
	}
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( true, ret, dwBytes, file, line );
#endif
#ifdef PUK3_MEMALLOC_LIMIT
	if ( ret ){
		size_t *pi = (size_t *)ret;
		allocSize += dwBytes;
		*pi = dwBytes;
		return pi + 1;
	}
#endif
	return ret;
}

void _rc_GlobalFree( void *memblock, char *file, int line )
{
	int size = 0;
#ifdef PUK3_MEMALLOC_LIMIT
	size_t *pi = (size_t *)memblock;
	if ( pi ){
		size = *( pi - 1 );
		allocSize -= size;
		memblock = (void *)( pi - 1 );
	}
#endif
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( false, memblock, size, file, line );
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	GlobalFree( memblock );
	memchecktime += timeGetTime() - time;
#else
	GlobalFree( memblock );
#endif
}

void _rc_GlobalFreePtr( void *memblock, char *file, int line )
{
	int size = 0;
#ifdef PUK3_MEMALLOC_LIMIT
	size_t *pi = (size_t *)memblock;
	if ( pi ){
		size = *( pi - 1 );
		allocSize -= size;
		memblock = (void *)( pi - 1 );
	}
#endif
#ifdef PUK3_MEMALLOCLOG
	// メモリログファイル书き込み
	WriteMemoryLogfile( false, memblock, size, file, line );
#endif
#ifdef PUK3_MEMORYLEAK
	int time = timeGetTime();
	GlobalFreePtr( memblock );
	memchecktime += timeGetTime() - time;
#else
	GlobalFreePtr( memblock );
#endif
}

#endif

#endif
#ifdef PUK3_SEGMENTATION_FAULT

#define PROCSTACK_MAX 1000

int procstack[PROCSTACK_MAX];
int procstackNum = 0;

char *procName[PROCSTACK_MAX] = {
	"GameMain",                                                          //MLHIDE
	"Process",                                                           //MLHIDE
	"RunAction",                                                         //MLHIDE
	"BattleMaster",                                                      //MLHIDE
	"CheckSequence",                                                     //MLHIDE
	"BattleChar",                                                        //MLHIDE
	"networkLoop",                                                       //MLHIDE
	"menuProc",                                                          //MLHIDE
	"PutBmp",                                                            //MLHIDE
	"charProc",                                                          //MLHIDE
	"ParentDeathCheckFunc",                                              //MLHIDE
	"BattleAbnormal",                                                    //MLHIDE
	"BattleReverseType",                                                 //MLHIDE
	"BattleStun",                                                        //MLHIDE
	"Battle2Action",                                                     //MLHIDE
	"BattleParameterUpDown",                                             //MLHIDE
	"BattleTreatType",                                                   //MLHIDE
	"mapCheckSum",                                                       //MLHIDE
	"changeCharAct",                                                     //MLHIDE
};

#if 0
void ProcStack( int proc )
{
	procstack[procstackNum] = proc;
	procstackNum++;
}

void ProcPop()
{
	procstackNum--;
}
#endif

int SegmentationFaultFunc( unsigned int code )
{
	int i;
	char str[512];

	// 现在位置をrecvdata.txtに出力
	for( i = 0; i < procstackNum; i++ ){
		if ( procstack[i] >= PROCSTACK_GameMain ){
			sprintf( str, "  !! %s", procName[ procstack[i]-PROCSTACK_GameMain ] ); //MLHIDE
		}else{
			sprintf( str, "  !! 0x%04x", procstack[i] );                       //MLHIDE
		}
		writeLogFile( str );
		WriteSystemLogfile( str );
	}

	strcpy( str, "" );

	switch (code) {
	case EXCEPTION_ACCESS_VIOLATION:
		strcpy( str, "  !! 主题是对于没有访问权限的地址进行读取和写入。" );                       //MLHIDE
		break;
	case EXCEPTION_FLT_DIVIDE_BY_ZERO:
		strcpy( str, "  !! 主题是将浮点数0作为浮点数除数进行计划。" );                         //MLHIDE
		break;
	case EXCEPTION_INT_DIVIDE_BY_ZERO:
		strcpy( str, "  !! 主题是将整数值0作为整数除数进行计划。" );                          //MLHIDE
		break;
	}
	writeLogFile( str );
	WriteSystemLogfile( str );

	// EXCEPTION_ACCESS_VIOLATION:			スレッドが适切なアクセス权を持たない假想アドレスに对して、読み取りまたは书き込みを试みました。
	// EXCEPTION_DATATYPE_MISALIGNMENT:		境界整列をサポートしないハードウェア上で、スレッドが境界に整列していないデータの読み取りまたは书き込みを试みました。たとえば、16 ビットの值は 2 バイトの境界线上に、32 ビットの值は 4 バイトの境界线上に整列していなければなりません。
	// EXCEPTION_ARRAY_BOUNDS_EXCEEDED:		スレッドが范围外の配列要素にアクセスしようとしました。使用中のハードウェアは境界チェックをサポートしています。
	// EXCEPTION_FLT_DENORMAL_OPERAND:		浮动小数点演算のオペランドの 1 つが不正规化值です。不正规化值とは、小さすぎて标准の浮动小数点值として表现できない值です。
	// EXCEPTION_FLT_DIVIDE_BY_ZERO:		スレッドが浮动小数点值を 0 の浮动小数点除数で除算しようとしました。
	// EXCEPTION_FLT_INEXACT_RESULT:		浮动小数点演算の结果を 10 进小数で正确に表现できません。
	// EXCEPTION_FLT_INVALID_OPERATION:		このリストに含まれていない例外が発生しました。
	// EXCEPTION_FLT_OVERFLOW:				浮动小数点演算の指数が大きく、对应する型によって表现できません。
	// EXCEPTION_FLT_STACK_CHECK:			浮动小数点演算の结果、スタックのオーバーフローまたはアンダーフローが発生しました。
	// EXCEPTION_FLT_UNDERFLOW:				浮动小数点演算の指数が小さく、对应する型によって表现できません。
	// EXCEPTION_INT_DIVIDE_BY_ZERO:		スレッドが整数值を 0 の整数除数で除算しようとしました。
	// EXCEPTION_INT_OVERFLOW:				整数演算结果の最上位ビットが缲り上がりました。
	// EXCEPTION_PRIV_INSTRUCTION:			现在のマシンモードで许可されていない演算を行う命令をスレッドが实行しようとしました。
	// EXCEPTION_NONCONTINUABLE_EXCEPTION:	継続不可能な例外が発生した后、スレッドが实行を継続しようとしました。

	return EXCEPTION_CONTINUE_SEARCH;
	// EXCEPTION_CONTINUE_SEARCH:	ウィンドウズ标准のエラー处理に引き渡す
	// EXCEPTION_EXECUTE_HANDLER:	エラーを自前で处理し、实行を継続する
}
#endif


