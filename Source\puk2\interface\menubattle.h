﻿//バトルメニュー

#ifndef _MENUBATTLE_H_
#define _MENUBATTLE_H_

//====================================//
//				コクピット			  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuWindowBattleAttack( int no, unsigned int flag );
BOOL MenuWindowBattleGuard( int no, unsigned int flag );
BOOL MenuWindowBattleSkill( int no, unsigned int flag );
BOOL MenuWindowBattleItem( int no, unsigned int flag );
BOOL MenuWindowBattleMonster( int no, unsigned int flag );
BOOL MenuWindowBattlePosition( int no, unsigned int flag );
BOOL MenuWindowBattleEscape( int no, unsigned int flag );
BOOL MenuWindowBattleReBirth( int no, unsigned int flag );

BOOL MenuWindowBattlePetSkill( int no, unsigned int flag );
#ifdef PUK3_RIDE_BATTLE
BOOL MenuWindowBattleRideEscape( int no, unsigned int flag );
#endif

GRAPHIC_SWITCH MenuWindowBattleGraph[]={
	{GID_BattlePlayerBase,0,0,0,0,0xFFFFFFFF},	//ウインドウ画像

	{GID_BattleAttackOff,0,0,0,0,0xFFFFFFFF},	//アタックボタン
	{GID_BattleGuardOff,0,0,0,0,0xFFFFFFFF},	//防御ボタン
	{GID_BattleSkillOff,0,0,0,0,0xFFFFFFFF},	//スキルボタン
	{GID_BattleItemOff,0,0,0,0,0xFFFFFFFF},		//アイテムボタン
	{GID_BattleMonsterOff,0,0,0,0,0xFFFFFFFF},	//モンスターボタン
	{GID_BattlePositionOff,0,0,0,0,0xFFFFFFFF},	//ポジションボタン
	{GID_BattleEscapeOff,0,0,0,0,0xFFFFFFFF},	//エスケープボタン
	{GID_BattleReBirthOff,0,0,0,0,0xFFFFFFFF},	//リバースボタン
};

TEXT_SWITCH MenuWindowBattleText[]={
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Attack"},	                          //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Guard"},	                           //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Skill"},	                           //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Item"},	                            //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Monster"},	                         //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Position"},	                        //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Escape"},	                          //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"Re-Birth"},	                        //MLHIDE

	{FONT_PAL_SHADOW,FONT_KIND_SMALL,"Name"},                            //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SMALL,"Fp"},                              //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SMALL,"Skill"},                           //MLHIDE
};

// スイッチ设定
static SWITCH_DATA BattleSwitch[] = {
//type         , ofx, ofy, sx, sy, SprNum, graNo, view , text   ,  func

//ボタン
{ SWITCH_GRAPHIC  ,  13,  20,  64, 20, TRUE, &MenuWindowBattleGraph[1], MenuWindowBattleAttack },
{ SWITCH_GRAPHIC  ,  13,  45,  64, 20, TRUE, &MenuWindowBattleGraph[2], MenuWindowBattleGuard },
{ SWITCH_GRAPHIC  ,  83,  20,  64, 20, TRUE, &MenuWindowBattleGraph[3], MenuWindowBattleSkill },
{ SWITCH_GRAPHIC  ,  83,  45,  64, 20, TRUE, &MenuWindowBattleGraph[4], MenuWindowBattleItem },
{ SWITCH_GRAPHIC  , 153,  20,  64, 20, TRUE, &MenuWindowBattleGraph[5], MenuWindowBattleMonster },
{ SWITCH_GRAPHIC  , 153,  45,  64, 20, TRUE, &MenuWindowBattleGraph[6], MenuWindowBattlePosition },
{ SWITCH_GRAPHIC  , 223,  45,  64, 20, TRUE, &MenuWindowBattleGraph[7], MenuWindowBattleEscape },
{ SWITCH_GRAPHIC  , 223,  20,  64, 20, TRUE, &MenuWindowBattleGraph[8], MenuWindowBattleReBirth },

{ SWITCH_GRAPHIC  , 216,  46,  64, 20, TRUE, &MenuWindowBattleGraph[3], MenuWindowBattlePetSkill },
#ifdef PUK3_RIDE_BATTLE
{ SWITCH_GRAPHIC  , 216,  46,  64, 20, TRUE, &MenuWindowBattleGraph[7], MenuWindowBattleRideEscape },
#endif

{ SWITCH_TEXT	  ,  20,  24,   0,  0, TRUE, &MenuWindowBattleText[8], MenuSwitchNone },
{ SWITCH_TEXT	  , 215,  24,   0,  0, TRUE, &MenuWindowBattleText[9], MenuSwitchNone },
{ SWITCH_TEXT	  ,  20,  48,   0,  0, TRUE, &MenuWindowBattleText[10], MenuSwitchNone },

//画像
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowBattleGraph[0], MenuSwitchNone },
};

// スイッチ管理用enum
enum{
	EnumBtBattleAtack,
	EnumBtBattleGuard,
	EnumBtBattleSkill,
	EnumBtBattleItem,
	EnumBtBattleMonster,
	EnumBtBattlePosition,
	EnumBtBattleEscape,
	EnumBtBattleReBirth,

	EnumBtBattlePetSkill,
#ifdef PUK3_RIDE_BATTLE
	EnumBtBattleRideEscape,
#endif

	EnumTxtBattlePetName,
	EnumTxtBattlePetFp,
	EnumTxtBattlePetSkill,

	EnumGraphBattleWindowFrame,

	EnumMenuBattleEND,
};

// ウィンドウ处理关数 ++++
BOOL MenuWindowBattle( int mouse );
BOOL MenuWindowBattleDraw( int mouse );

//根据窗口分辨率计算战斗菜单栏显示位置(默认341,0)
#define DEFBATTLEWINPOSX DEF_APPSIZEX - 299 - ( SymOffsetX + 3 ) / 3
#define DEFBATTLEWINPOSY ( SymOffsetY + 3 ) / 3

// ウインドウ设定
const WINDOW_DATA WindowDataBattle = {
//flag
 MENU_ATTR_NOMOVE,										
     5, 100, 100,  299, 69, 0x80010101, EnumMenuBattleEND,  BattleSwitch, MenuWindowBattle,MenuWindowBattleDraw,MenuWindowDel
};

// バトルウィンドウ管理构造体
struct BATTLEWINDOWMASTER{
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 

	int difY;								// ズレ
	int accY;								// ズレの加速度
	int procNo;								// 现在の处理

	char WinType;							// ウィンドウの种类
};

// 处理の种类
enum{
	BWMPROC_NOMAL = 0,	// 通常处理时
	BWMPROC_APPEAR,		// 登场处理时
	BWMPROC_DISAPP,		// 退散处理时
	BWMPROC_CLOSE,		// ウィンドウ破弃后
};

// ウィンドウの种类
enum{
	BWMPROC_Player_ = 0,	// 通常プレーヤーウィンドウ
	BWMPROC_Player1,		// プレーヤー行动１回目
	BWMPROC_Player2,		// プレーヤー行动２回目
	BWMPROC_Monster,		// モンスターウィンドウ
#ifdef PUK3_RIDE_BATTLE
	BWMPROC_PetRide,		// ライドウィンドウ
#endif
};

int BattleTaskSkillButtonStatus();
void BattleTaskSkillButton();

int BattleTaskItemButtonStatus();
void BattleTaskItemButton();

int BattleTaskMonsterButtonStatus();
void BattleTaskMonsterButton();

//====================================//
//				観战				  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuSwitchWatchEnd( int no, unsigned int flag );

// ウィンドウ处理关数 ++++
BOOL MenuWindowWatchBf( int mouse );
BOOL MenuWindowWatchAf( int mouse );


TEXT_SWITCH MenuWindowWatchText[]={
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,ML_STRING(822, "退出观战")},	
};

// スイッチ设定
static SWITCH_DATA WatchSwitch[] = {
//type         , ofx, ofy, sx, sy, SprNum, graNo, view , text   ,  func

{ SWITCH_TEXT  ,  32,  46,   0,  0, TRUE, &MenuWindowWatchText[0], MenuSwitchNone },
{ SWITCH_NONE  ,  32,  46,  83, 16, TRUE, NULL, MenuSwitchWatchEnd },

};

// スイッチ管理用enum
enum{
	EnumWatchText,
	EnumWatchButton,

	EnumMenuWatchEND,
};

// ウインドウ设定
const WINDOW_DATA WindowDataWatch = {
//flag
 MENU_ATTR_NOMOVE,										
     3, 492,  0,  148, 108, 0x80010101, EnumMenuWatchEND,  WatchSwitch, MenuWindowWatchBf,MenuWindowWatchAf,MenuWindowDel
};


//====================================//
//				リザルト			  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuWindowResultOK( int no, unsigned int flag );

BOOL MenuWindowResultBf( int mouse );
BOOL MenuWindowResultAf( int mouse );

GRAPHIC_SWITCH MenuWindowResultGraph[]={
	{GID_ResultBase,0,0,0,0,0xFFFFFFFF},			//ウインドウ画像
	{GID_ResultBack,0,0,0,0,0x80FFFFFF},			//ウインドウ下地

	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},			//ＯＫボタン

	{GID_Health0,0,0,0,0,0xFFFFFFFF},				//Helth表示
	{GID_ResultLevelUp,0,0,0,0,0xFFFFFFFF},			//等级アップ
};

TEXT_SWITCH MenuWindowResultText[]={
	//韩服默认字体FONT_KIND_SIZE_11,台服FONT_KIND_SIZE_12
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"？？？？？？？？"},                       //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"+000000"},                        //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,"？？？？？？？？"},                        //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"？？？？？？？？"},                       //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"+000"},                           //MLHIDE
};

// スイッチ设定
static SWITCH_DATA ResultSwitch[] = {

#ifdef PUK2
{ SWITCH_NONE,	    0,  27, 490,274, TRUE, NULL, MenuWindowResultOK },			// ＯＫボタン
#else
{ SWITCH_GRAPHIC, 385, 265,  66, 17, TRUE, &MenuWindowResultGraph[2], MenuWindowResultOK },			// ＯＫボタン
#endif

{ SWITCH_GRAPHIC, 268,  40,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ１
{ SWITCH_GRAPHIC, 268,  59,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ２
{ SWITCH_GRAPHIC, 268,  78,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ３
{ SWITCH_GRAPHIC, 268,  97,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ４

{ SWITCH_GRAPHIC,  72,  47,   0,  0, TRUE, &MenuWindowResultGraph[3], MenuSwitchNone },				// Health1
{ SWITCH_GRAPHIC,  72,  66,   0,  0, TRUE, &MenuWindowResultGraph[3], MenuSwitchNone },				// Health2
{ SWITCH_GRAPHIC,  72,  85,   0,  0, TRUE, &MenuWindowResultGraph[3], MenuSwitchNone },				// Health3
{ SWITCH_GRAPHIC,  72, 104,   0,  0, TRUE, &MenuWindowResultGraph[3], MenuSwitchNone },				// Health4

{ SWITCH_TEXT  ,   86,  46,   0,  0, TRUE, &MenuWindowResultText[0], MenuSwitchNone },				// 名称１
{ SWITCH_TEXT  ,   86,  65,   0,  0, TRUE, &MenuWindowResultText[0], MenuSwitchNone },				// 名称２
{ SWITCH_TEXT  ,   86,  84,   0,  0, TRUE, &MenuWindowResultText[0], MenuSwitchNone },				// 名称３
{ SWITCH_TEXT  ,   86, 103,   0,  0, TRUE, &MenuWindowResultText[0], MenuSwitchNone },				// 名称４

{ SWITCH_TEXT  ,  226,  46,   0,  0, TRUE, &MenuWindowResultText[1], MenuSwitchNone },				// Exp１
{ SWITCH_TEXT  ,  226,  65,   0,  0, TRUE, &MenuWindowResultText[1], MenuSwitchNone },				// Exp２
{ SWITCH_TEXT  ,  226,  84,   0,  0, TRUE, &MenuWindowResultText[1], MenuSwitchNone },				// Exp３
{ SWITCH_TEXT  ,  226, 103,   0,  0, TRUE, &MenuWindowResultText[1], MenuSwitchNone },				// Exp４

{ SWITCH_GRAPHIC, 468, 44+16* 0,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ１
{ SWITCH_GRAPHIC, 468, 44+16* 1,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ２
{ SWITCH_GRAPHIC, 468, 44+16* 2,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ３
{ SWITCH_GRAPHIC, 468, 44+16* 3,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ４
{ SWITCH_GRAPHIC, 468, 44+16* 4,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ５
{ SWITCH_GRAPHIC, 468, 44+16* 5,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ６
{ SWITCH_GRAPHIC, 468, 44+16* 6,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ７
{ SWITCH_GRAPHIC, 468, 44+16* 7,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ８
{ SWITCH_GRAPHIC, 468, 44+16* 8,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ９
{ SWITCH_GRAPHIC, 468, 44+16* 9,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ10
#ifdef PUK2_MAXSKILLSLOT_UP
{ SWITCH_GRAPHIC, 468, 44+16*10,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ11
{ SWITCH_GRAPHIC, 468, 44+16*11,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ12
{ SWITCH_GRAPHIC, 468, 44+16*12,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ13
{ SWITCH_GRAPHIC, 468, 44+16*13,   0,  0, TRUE, &MenuWindowResultGraph[4], MenuSwitchNone },				// 等级アップ14
#endif

{ SWITCH_TEXT  ,  283, 51+16* 0,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称１
{ SWITCH_TEXT  ,  283, 51+16* 1,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称２
{ SWITCH_TEXT  ,  283, 51+16* 2,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称３
{ SWITCH_TEXT  ,  283, 51+16* 3,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称４
{ SWITCH_TEXT  ,  283, 51+16* 4,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称５
{ SWITCH_TEXT  ,  283, 51+16* 5,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称６
{ SWITCH_TEXT  ,  283, 51+16* 6,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称７
{ SWITCH_TEXT  ,  283, 51+16* 7,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称８
{ SWITCH_TEXT  ,  283, 51+16* 8,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称９
{ SWITCH_TEXT  ,  283, 51+16* 9,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称10
#ifdef PUK2_MAXSKILLSLOT_UP
{ SWITCH_TEXT  ,  283, 51+16*10,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称11
{ SWITCH_TEXT  ,  283, 51+16*11,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称12
{ SWITCH_TEXT  ,  283, 51+16*12,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称13
{ SWITCH_TEXT  ,  283, 51+16*13,   0,  0, TRUE, &MenuWindowResultText[3], MenuSwitchNone },				// 名称14
#endif

{ SWITCH_TEXT  ,  448, 51+16* 0,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp１
{ SWITCH_TEXT  ,  448, 51+16* 1,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp２
{ SWITCH_TEXT  ,  448, 51+16* 2,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp３
{ SWITCH_TEXT  ,  448, 51+16* 3,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp４
{ SWITCH_TEXT  ,  448, 51+16* 4,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp５
{ SWITCH_TEXT  ,  448, 51+16* 5,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp６
{ SWITCH_TEXT  ,  448, 51+16* 6,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp７
{ SWITCH_TEXT  ,  448, 51+16* 7,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp８
{ SWITCH_TEXT  ,  448, 51+16* 8,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp９
{ SWITCH_TEXT  ,  448, 51+16* 9,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp10
#ifdef PUK2_MAXSKILLSLOT_UP
{ SWITCH_TEXT  ,  448, 51+16*10,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp11
{ SWITCH_TEXT  ,  448, 51+16*11,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp12
{ SWITCH_TEXT  ,  448, 51+16*12,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp13
{ SWITCH_TEXT  ,  448, 51+16*13,   0,  0, TRUE, &MenuWindowResultText[4], MenuSwitchNone },				// Exp14
#endif

{ SWITCH_TEXT  ,   72, 171,   0,  0, TRUE, &MenuWindowResultText[2], MenuSwitchNone },				// アイテム名１
{ SWITCH_TEXT  ,   72, 220,   0,  0, TRUE, &MenuWindowResultText[2], MenuSwitchNone },				// アイテム名２
{ SWITCH_TEXT  ,   72, 269,   0,  0, TRUE, &MenuWindowResultText[2], MenuSwitchNone },				// アイテム名３

{ SWITCH_GRAPHIC,   0,   0,   0,  0, TRUE, &MenuWindowResultGraph[0], MenuSwitchNone },
{ SWITCH_GRAPHIC,  15,  27,   0,  0, TRUE, &MenuWindowResultGraph[1], MenuSwitchNone },

{ SWITCH_NONE,	    9,   0, 481, 27, TRUE, NULL, MenuSwitchDelMouse },
{ SWITCH_NONE,	  493,   7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },

};

// スイッチ管理用enum
enum{
	EnumGraphResultOK,

	EnumGraphLvup1,
	EnumGraphLvup2,
	EnumGraphLvup3,
	EnumGraphLvup4,

	EnumGraphHealth1,
	EnumGraphHealth2,
	EnumGraphHealth3,
	EnumGraphHealth4,

	EnumGraphName1,
	EnumGraphName2,
	EnumGraphName3,
	EnumGraphName4,

	EnumGraphExp1,
	EnumGraphExp2,
	EnumGraphExp3,
	EnumGraphExp4,

	EnumGraphSkillLvup1,
	EnumGraphSkillLvup2,
	EnumGraphSkillLvup3,
	EnumGraphSkillLvup4,
	EnumGraphSkillLvup5,
	EnumGraphSkillLvup6,
	EnumGraphSkillLvup7,
	EnumGraphSkillLvup8,
	EnumGraphSkillLvup9,
	EnumGraphSkillLvup10,
#ifdef PUK2_MAXSKILLSLOT_UP
	EnumGraphSkillLvup11,
	EnumGraphSkillLvup12,
	EnumGraphSkillLvup13,
	EnumGraphSkillLvup14,
#endif

	EnumGraphSkillName1,
	EnumGraphSkillName2,
	EnumGraphSkillName3,
	EnumGraphSkillName4,
	EnumGraphSkillName5,
	EnumGraphSkillName6,
	EnumGraphSkillName7,
	EnumGraphSkillName8,
	EnumGraphSkillName9,
	EnumGraphSkillName10,
#ifdef PUK2_MAXSKILLSLOT_UP
	EnumGraphSkillName11,
	EnumGraphSkillName12,
	EnumGraphSkillName13,
	EnumGraphSkillName14,
#endif

	EnumGraphSkillExp1,
	EnumGraphSkillExp2,
	EnumGraphSkillExp3,
	EnumGraphSkillExp4,
	EnumGraphSkillExp5,
	EnumGraphSkillExp6,
	EnumGraphSkillExp7,
	EnumGraphSkillExp8,
	EnumGraphSkillExp9,
	EnumGraphSkillExp10,
#ifdef PUK2_MAXSKILLSLOT_UP
	EnumGraphSkillExp11,
	EnumGraphSkillExp12,
	EnumGraphSkillExp13,
	EnumGraphSkillExp14,
#endif

	EnumGraphSkillItemName1,
	EnumGraphSkillItemName2,
	EnumGraphSkillItemName3,

	EnumGraphResultWindowBase,
	EnumGraphResultWindowBack,

	EnumGraphResultWindowDragBack1,
	EnumGraphResultWindowDragBack2,

	EnumMenuResultEND,
};

// ウインドウ设定
const WINDOW_DATA WindowDataResult = {
//flag
 MENU_ATTR_NOHIT,										
     3,  40,  40,  490, 301, 0x80010101, EnumMenuResultEND,  ResultSwitch, MenuWindowResultBf,MenuWindowResultAf,MenuWindowDel
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateResult={
	2,
	 9,  0,481, 27,
	 493, 7,18,  78,
};

//====================================//
//			デュエルリザルト		  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuWindowDuelResultOK( int no, unsigned int flag );

BOOL MenuWindowDuelResultBf( int mouse );
BOOL MenuWindowDuelResultAf( int mouse );

GRAPHIC_SWITCH MenuWindowDuelResultGraph[]={
	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},			//ＯＫボタン
};

TEXT_SWITCH MenuWindowDuelResultText[]={
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"RESULT"},                           //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SMALL,"?????  +000000"},                   //MLHIDE
};

// スイッチ设定
static SWITCH_DATA DuelResultSwitch[] = {

{ SWITCH_GRAPHIC,  42, 106,  66, 17, TRUE, &MenuWindowDuelResultGraph[0], MenuWindowDuelResultOK },		// ＯＫボタン

{ SWITCH_TEXT,     20,  10,   0,  0, TRUE, &MenuWindowDuelResultText[0], MenuSwitchNone },				// タイトル

{ SWITCH_TEXT,     20,  40,   0,  0, TRUE, &MenuWindowDuelResultText[1], MenuSwitchNone },				// DP
{ SWITCH_TEXT,     20,  65,   0,  0, TRUE, &MenuWindowDuelResultText[1], MenuSwitchNone },				// Total

};

// スイッチ管理用enum
enum{
	EnumGraphDuelOK,

	EnumGraphDuelTitle,

	EnumGraphDuelDp,
	EnumGraphDuelTotal,

	EnumMenuDuelResultEND,
};

// ウインドウ设定
const WINDOW_DATA WindowDataDuelResult = {
//flag
 MENU_ATTR_NOMOVE|MENU_ATTR_NOHIT,										
     3, 245, 170,  150, 140, 0x80010101, EnumMenuDuelResultEND,  DuelResultSwitch, MenuWindowDuelResultBf,MenuWindowDuelResultAf,MenuWindowDel
};

//======================================//
// 战闘时アイテムウィンドウ				//
//======================================//

// ボタン处理关数 *********************//
BOOL MenuBtlItemSwitchGold( int no, unsigned int flag );

BOOL MenuBtlItemSwitchEquipButton( int no, unsigned int flag );

BOOL MenuBtlItemSwitchItemWindow( int no, unsigned int flag );
BOOL MenuBtlItemSwitchEquipWindow( int no, unsigned int flag );


BOOL MenuWindowBtlItemBf( int mouse );
BOOL MenuWindowBtlItemAf( int mouse );


GRAPHIC_SWITCH MenuWindowBtlItemGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},		// クローズボタン
	{GID_ItemWindow,0,0,0,0,0xFFFFFFFF},		// ベース

	{GID_EquipButtonOn,0,0,0,0,0xFFFFFFFF},		// 装备ボタン

	{GID_EquipWindow,0,0,0,0,0xFFFFFFFF},		// 装备ウィンドウ
};

TEXT_SWITCH MenuWindowBtlItemText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,""},
};

ACTION_SWITCH_INIT MenuWindowBtlItemAction[]={
	{100000},
};

char BtlItemNum_PCGold[11];

NUMBER_SWITCH MenuWindowBtlItemNum[1] = {
	{ BtlItemNum_PCGold, FONT_PAL_BLACK, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
};

// スイッチ
static SWITCH_DATA BtlItemSwitch[] = {
{ SWITCH_GRAPHIC,258,  8,  11, 11, TRUE, &MenuWindowBtlItemGraph[0], MenuSwitchCloseButton },			// クローズボタン
#ifdef VERSION_TW
//台服客户端战斗中道具窗口的金币显示位置
{ SWITCH_NUMBER, 198, 31,   0,  0, TRUE, &MenuWindowBtlItemNum[0], MenuBtlItemSwitchGold },								//ドラッグ用
#else
{ SWITCH_NUMBER, 249, 31,   0,  0, TRUE, &MenuWindowBtlItemNum[0], MenuBtlItemSwitchGold },								//ドラッグ用
#endif

{ SWITCH_GRAPHIC,  8,  0, 276,253, TRUE, &MenuWindowBtlItemGraph[1], MenuBtlItemSwitchItemWindow },		// ベース

{ SWITCH_ACTION,  -81,163,  0,  0, TRUE, &MenuWindowBtlItemAction[0], MenuSwitchNone },					// ＰＣ画像

{ SWITCH_GRAPHIC,  2, 17,  19, 46, TRUE, &MenuWindowBtlItemGraph[2], MenuBtlItemSwitchEquipButton },	// 装备ボタン

{ SWITCH_GRAPHIC,-197,17, 212,210, TRUE, &MenuWindowBtlItemGraph[3], MenuBtlItemSwitchEquipWindow },	// 装备ウィンドウ

{ SWITCH_NONE,	  320, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },									// ドラッグ用

};

enum{
	EnumGraphBtlItemClose,

	EnumTextBtlMoneyDisp,

	EnumGraphBtlItemWindow,

	EnumActionBtlItemPc,

	EnumGraphBtlEquipButton,

	EnumGraphBtlEquipWindow,

	EnumBtlItemDragBack,

	EnumBtlItemEnd,
};


const WINDOW_DATA WindowDataMenuBtlItem = {
 0,															// メニューウィンドウ
     4,   100, 100,276,253, 0x80010101,  EnumBtlItemEnd,  BtlItemSwitch, MenuWindowBtlItemBf,MenuWindowBtlItemAf,MenuWindowDel
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateBtlItem={
	2,
	 17,  0,300, 33,
	 320, 7,18,  78,
};

extern char BtlitemFrameFlag;

//======================================//
// 战闘时使い魔ウィンドウ				//
//======================================//

// ボタン处理关数 *********************//
BOOL MenuWindowBattlePetReturn( int no, unsigned int flag );

BOOL MenuWindowBattlePetPanel( int no, unsigned int flag );

BOOL MenuWindowBtlPetBf( int mouse );
BOOL MenuWindowBtlPetAf( int mouse );


GRAPHIC_SWITCH MenuWindowBtlPetGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_BattlePetWindow,0,0,0,0,0xFFFFFFFF},		// ベース
	{GID_BattlePetWindowBack,0,0,0,0,0x80FFFFFF},	// 下地

	{GID_ReturnOn,0,0,0,0,0xFFFFFFFF},				// リターンボタン
};

TEXT_SWITCH MenuWindowBtlPetText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_11,""},
};


// スイッチ
static SWITCH_DATA BtlPetSwitch[] = {
{ SWITCH_GRAPHIC,302,  9,  11, 11, TRUE, &MenuWindowBtlPetGraph[0], MenuSwitchCloseButton },			// クローズボタン

{ SWITCH_GRAPHIC,258,173,  57, 17, TRUE, &MenuWindowBtlPetGraph[3], MenuWindowBattlePetReturn },		// リターンボタン

{ SWITCH_NONE,	  15, 28, 300, 48, TRUE, NULL, MenuWindowBattlePetPanel },								// 使い魔パネル１
{ SWITCH_NONE,	  15, 76, 300, 48, TRUE, NULL, MenuWindowBattlePetPanel },								// 使い魔パネル２
{ SWITCH_NONE,	  15,124, 300, 48, TRUE, NULL, MenuWindowBattlePetPanel },								// 使い魔パネル３

{ SWITCH_GRAPHIC,  0,  0, 320,353, TRUE, &MenuWindowBtlPetGraph[1], MenuSwitchNone },					// ベース
{ SWITCH_GRAPHIC,  12, 25, 320,353, TRUE, &MenuWindowBtlPetGraph[2], MenuSwitchNone },					// 下地

{ SWITCH_NONE,	  320, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },									// ドラッグ用

};

enum{
	EnumGraphBtlPetClose,

	EnumGraphBtlPetReturn,

	EnumGraphBtlPetPanel1,
	EnumGraphBtlPetPanel2,
	EnumGraphBtlPetPanel3,

	EnumGraphBtlPetWindow,
	EnumGraphBtlPetWindowBack,

	EnumBtlPetDragBack,

	EnumBtlPetEnd,
};


const WINDOW_DATA WindowDataMenuBtlPet = {
 0,															// メニューウィンドウ
     4,   100, 100,356,223, 0x80010101,  EnumBtlPetEnd,  BtlPetSwitch, MenuWindowBtlPetBf,MenuWindowBtlPetAf,MenuWindowDel
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateBtlPet={
	2,
	 17,  0,339, 33,
	 359, 7,18,  78,
};


//====================================//
//				不意打ち			  //
//====================================//

// ボタン处理关数 *********************//

// ウィンドウ处理关数 ++++
BOOL MenuWindowSurpriseBf( int mouse );
BOOL MenuWindowSurpriseAf( int mouse );


TEXT_SWITCH MenuWindowSurpriseText[]={
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,ML_STRING(823, "偷袭")},
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,ML_STRING(184, "被偷袭")},
};

// スイッチ设定
static SWITCH_DATA SurpriseSwitch[] = {
//type         , ofx, ofy, sx, sy, SprNum, graNo, view , text   ,  func
//默认台服78,40,韩服38,40
{ SWITCH_TEXT  ,  78,  40,   0,  0, TRUE, &MenuWindowSurpriseText[0], MenuSwitchNone },
{ SWITCH_TEXT  ,  78,  40,   0,  0, TRUE, &MenuWindowSurpriseText[1], MenuSwitchNone },

};

// スイッチ管理用enum
enum{
	EnumSurpriseText1,
	EnumSurpriseText2,

	EnumMenuSurpriseEND,
};

// ウインドウ设定
const WINDOW_DATA WindowDataSurprise = {
//flag
 MENU_ATTR_NOMOVE,										
     3, 224,162,  192,  96, 0x80010101, EnumMenuSurpriseEND,  SurpriseSwitch, MenuWindowSurpriseBf,MenuWindowSurpriseAf,MenuWindowDel
};

#endif