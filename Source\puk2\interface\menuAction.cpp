﻿///右コクピット＞アクション

char MenuWindowActionFlag[ACTION_MAX];


//ウインドウ处理
BOOL MenuWindowAction( int mouse )
{

	// キャラが移动したらクリア
	if( checkMoveMapGridPos( 1, 1 ) ){
		InitMenuWindowActionFlag();
	}

	return TRUE;
}

//ウインドウ描画处理
BOOL MenuWindowActionDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

//ウインドウ削除处理
BOOL MenuWindowActionDel( void ){

	return TRUE;
}

void SetMenuWindowActionFlag(int Num){

	int i;

	for(i=0;i<ACTION_MAX;i++)
		MenuWindowActionFlag[i]=0;

	MenuWindowActionFlag[Num]=1;

	memoryMapGridPos( mapGx, mapGy );
}

void InitMenuWindowActionFlag(void){

	int i;

	for(i=0;i<ACTION_MAX;i++)
		MenuWindowActionFlag[i]=0;

}

//アクションスイッチ 
BOOL MenuSwitchAction( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph;

	//状态ボタン画像设定
	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	
	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		//重なってる
		Graph->graNo=GID_Action00Over+3*(no - EnumGraphAction00);
		ReturnFlag=TRUE;
	}else{
		//实行中かチェック
		if(MenuWindowActionFlag[no]==1){
			Graph->graNo=GID_Action00Off+3*(no - EnumGraphAction00);
		}else{
			//实行中でもなく重なってもない
			Graph->graNo=GID_Action00On+3*(no - EnumGraphAction00);
		}
	}

//	setPcDir( (int)pc.ptAct->anim_ang );
	switch(no){
	//座る
	case EnumGraphAction00:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_SIT );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(0);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//挥手
	case EnumGraphAction01:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_HAND );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(2);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//点头
	case EnumGraphAction02:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_NOD );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(4);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//喜ぶ
	case EnumGraphAction03:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_HAPPY );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(6);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//怒る
	case EnumGraphAction04:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_ANGRY );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(8);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//悲伤
	case EnumGraphAction05:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_SAD );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(10);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//投掷
	case EnumGraphAction06:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_THROW );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(12);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//剪刀
	case EnumGraphAction07:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_CHOKI );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(14);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//布
	case EnumGraphAction08:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_PA );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(16);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//立つ
	case EnumGraphAction09:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_STAND );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(1) ;
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//步く
	case EnumGraphAction10:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_WALK );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(3);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//倒下
	case EnumGraphAction11:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_DEAD );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(5);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//攻击
	case EnumGraphAction12:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_ATTACK );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(7) ;
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//防御
	case EnumGraphAction13:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_GUARD );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(9);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//受伤
	case EnumGraphAction14:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_DAMAGE );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(11);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//魔法
	case EnumGraphAction15:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_MAGIC );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(13);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//バトル中での步く
	case EnumGraphAction16:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_MOVE );
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(15);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;

	//石头
	case EnumGraphAction17:
		if( flag & MENU_MOUSE_OVER ){
			strcpy( OneLineInfoStr, MWONELINE_ACTION_GU);
			ReturnFlag=TRUE;
		}
		if( flag & MENU_MOUSE_LEFT ){
			changeAction(17);
			SetMenuWindowActionFlag(no);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
			ReturnFlag=TRUE;
		}
		break;
	}

	return ReturnFlag;
}
