﻿#define sprVERSION	3				//SPR の　对应バージョン
#include 	"../systeminc/system.h"
#include 	"../systeminc/loadsprbin.h"
#include 	"../systeminc/anim_tbl.h"
#include 	"../systeminc/main.h"
#include 	"../systeminc/filetbl.h"
//#include	"../oft/vg410.h"

#define ERRPRINT(a);

int mxSPRITE = 0;
int maxBUFFER = 0;

//#define maxBUFFER	0x9fffff
//#define maxBUFFER	10485759
//#define maxBUFFER	 4250000
//#define maxBUFFER	 4350000
//#define maxBUFFER	 5000000

//U2 TBbuffer[maxBUFFER];
U2 *TBbuffer = NULL;
int TBBufSize = maxBUFFER;

SPRITEDATA *SpriteData = NULL;
//SPRITEDATA SpriteData[4000];

#ifdef MULTI_GRABIN
extern int BinOffsetTbl[BINMODE_MAX];
#define arraysizeof( a ) ( sizeof(a)/sizeof(a[0]) )
int giAllChrNum = 0;



#ifdef MULTI_GRABIN

// バイナリツリー检索
int Spr_Number2TblSub( unsigned int bmp, int top, int tail ){
	int nowpos, len;
	// 前后の关系が続くまでやる。
	while( top <= tail ){
		len = tail-top;
		if( len < 10 ){	
			// 差が１０以下だったら素直に检索
			for( nowpos = top; nowpos <= tail; nowpos ++ ){
				if( SpriteData[nowpos].sprNo != bmp )continue;
				return nowpos;
			}
			// 発见できなかった
			return -1;
		}else{
			// 检索位置は约中间地点
			nowpos = top + ( len ) / 2;
			// ターゲットより大きかったら。
			if( SpriteData[nowpos].sprNo > bmp ){
				tail = nowpos-1;	// 自分の前を最后尾にする。
				continue;
			}else
			// ターゲットより小さかったら。
			if( SpriteData[nowpos].sprNo < bmp ){
				top = nowpos+1;	// 自分の次を先头にする。
				continue;
			}else{
				// ターゲットと同じだったら。発见
				return nowpos;
			}
		}
	}

	// 発见できず。そんな事ないはずだが。
	return -1;
}

int Spr_Number2Tbl( int chrNo ){
	int sprNo = Spr_Number2TblSub( (unsigned int )chrNo, 0, giAllChrNum );
	if( sprNo < 0 )return -1;
	return sprNo;
}
#endif

// バイナリサーチするために画像番号でクイックソート。
// そのための比较关数
typedef int (*FUNC)( const void *a, const void *b );

int SprTblCmp( const void *x, const void *y ){
	SPRITEDATA *a, *b;
	a = (SPRITEDATA *)x;
	b = (SPRITEDATA *)y;
	// ビットマップ番号で比较
	if( a->sprNo < b->sprNo )return -1;
	if( a->sprNo > b->sprNo )return 1;
	// 同じだったら
	return 0;
}




	#ifdef ANIMATION_BUFFER_LESS	// アニメーションバッファを少なくする。
//--------------------------------------------------
// アニメーションバッファ管理
//--------------------------------------------------
#define  ANIMMAN_TBL_MAX	256
#include "..\systeminc\chat.h"
#include "..\systeminc\font.h"
ANIMMAN	aAnimManTbl[ANIMMAN_TBL_MAX];
ANIMMAN AnimManTop,AnimManTail;	// 最初と最后

int giAnimTblCount = 0;
int giAnimTblSize = 0;

//------------------------------------------
// フリーする。本当にしたら１
//------------------------------------------
int AnimManTailFree( ANIMMAN *pAnimMan ){
	// そいつがアニメを持っていたら开放する
	if( pAnimMan->iSpriteIndex >= 0 ){
#ifdef PUK3_NOTFREE_ANIMEDATA
		int i;
		// フレームデータ开放
		for( i = 0; i < SpriteData[pAnimMan->iSpriteIndex].animSize; i ++ ){
#ifdef PUK2_MEMCHECK
			memlistrel( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist, MEMLISTTYPE_ACTIONFRAMELIST );
#endif
			if ( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist == NULL ) continue;
			free( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist );
			// NULL で消しとく
			SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist = NULL;
		}
		// アニメーションデータ开放
#ifdef PUK2_MEMCHECK
		memlistrel( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist, MEMLISTTYPE_ACTIONANIMLIST );
#endif
		if ( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist ){
			free( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist );
		}
		// NULL で消しとく
		SpriteData[pAnimMan->iSpriteIndex].ptAnimlist = NULL;
		SpriteData[pAnimMan->iSpriteIndex].pAnimMan = NULL;
#ifdef PUK3_RIDEBIN
	#ifdef PUK2_MEMCHECK
		memlistrel( SpriteData[pAnimMan->iSpriteIndex].ptCdlist, MEMLISTTYPE_ACTIONCDLIST );
	#endif
		if ( SpriteData[pAnimMan->iSpriteIndex].ptCdlist ){
			free( SpriteData[pAnimMan->iSpriteIndex].ptCdlist );
		}
		SpriteData[pAnimMan->iSpriteIndex].ptCdlist = NULL;
#endif
		// マイナス１で消しとく
		pAnimMan->iSpriteIndex = -1;
#else
		int i;
		// フレームデータ开放
		for( i = 0; i < SpriteData[pAnimMan->iSpriteIndex].animSize; i ++ ){
#ifdef PUK2_MEMCHECK
			memlistrel( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist, MEMLISTTYPE_ACTIONFRAMELIST );
#endif
			free( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist );
			// NULL で消しとく
			SpriteData[pAnimMan->iSpriteIndex].ptAnimlist[i].ptFramelist = NULL;
		}
		// アニメーションデータ开放
#ifdef PUK2_MEMCHECK
		memlistrel( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist, MEMLISTTYPE_ACTIONANIMLIST );
#endif
		free( SpriteData[pAnimMan->iSpriteIndex].ptAnimlist );
		// NULL で消しとく
		SpriteData[pAnimMan->iSpriteIndex].ptAnimlist = NULL;
		SpriteData[pAnimMan->iSpriteIndex].pAnimMan = NULL;
#ifdef PUK3_RIDEBIN
	#ifdef PUK2_MEMCHECK
		memlistrel( SpriteData[pAnimMan->iSpriteIndex].ptCdlist, MEMLISTTYPE_ACTIONCDLIST );
	#endif
		free( SpriteData[pAnimMan->iSpriteIndex].ptCdlist );
		SpriteData[pAnimMan->iSpriteIndex].ptCdlist = NULL;
#endif
		// マイナス１で消しとく
		pAnimMan->iSpriteIndex = -1;
#endif
		return 1;
	}else{
		return 0;
	}
}

//------------------------------------------
// 最后尾から取る。中身は开放する。
//------------------------------------------
ANIMMAN* AnimManTailRemove( void ){
	ANIMMAN *pTarget;

	pTarget = AnimManTail.pPre;	// １个前を学习。これを拔き取る。
	if( pTarget == &AnimManTop )return NULL;	// 先头なので无理だ

	pTarget->pPre->pNext = &AnimManTail;	// ２个前の次は最后尾
	AnimManTail.pPre = pTarget->pPre;		// 最后尾の前は自分の前

	// データを开放する。
	if( AnimManTailFree( pTarget ) ){
//		char szBuffer[256];
		// サイズカウント
		giAnimTblCount --;
		giAnimTblSize -= pTarget->iAllocSize;
		// デバッグ用に出力してみる。
//		sprintf( szBuffer, "Delete Anim %d/%d %d/%d", 
//			giAnimTblCount, ANIMMAN_TBL_MAX, pTarget->iAllocSize, giAnimTblSize );
		// デバッグ用に出力してみる。
//		StockChatBufferLine( szBuffer, FONT_PAL_WHITE );
	}

	return pTarget;	// 拔き取ったので返す。
}


//------------------------------------------
// 先头につなげる
//------------------------------------------
int AnimManAddTop( ANIMMAN *pAnimMan ){
	ANIMMAN *pNext;

	pNext = AnimManTop.pNext;	// 学习
	pAnimMan->pPre = &AnimManTop;	// トップの次に入れる
	pAnimMan->pNext = pNext;

	pNext->pPre = pAnimMan;			// 次の前は自分
	AnimManTop.pNext = pAnimMan;	// トップの次は自分

	return 0;
}

//------------------------------------------
// 最后尾につなげる
//------------------------------------------
int AnimManAddTail( ANIMMAN *pAnimMan ){
	ANIMMAN *pPre;

	pPre = AnimManTail.pPre;	// 学习
	pAnimMan->pNext = &AnimManTail;	// 最后の前に入れる
	pAnimMan->pPre = pPre;

	pPre->pNext = pAnimMan;			// 前の次は自分
	AnimManTail.pPre = pAnimMan;	// 最后の前は自分

	return 0;
}


//------------------------------------------
// 配列からはずして先头につなげる
//------------------------------------------
int AnimManMoveTop( ANIMMAN *pAnimMan ){
	ANIMMAN *pNext;
	ANIMMAN *pPre;

	pPre = pAnimMan->pPre;
	pNext = pAnimMan->pNext;

	pNext->pPre = pPre;
	pPre->pNext = pNext;

	AnimManAddTop( pAnimMan );	// 先头につける

	return 0;
}


int InitAnimListBuffer( void ){
	int i;
	// 全クリア
	memset( aAnimManTbl, 0, sizeof(aAnimManTbl) );
	// 先头
	AnimManTop.pPre = NULL;
	AnimManTop.pNext = &AnimManTail;
	AnimManTail.pPre = &AnimManTop;
	AnimManTail.pNext = NULL;
	// 数珠繋ぎにする。
	for( i = 0; i < ANIMMAN_TBL_MAX; i ++ ){
		aAnimManTbl[i].iSpriteIndex = -1;	// 初期化
		AnimManAddTop( &aAnimManTbl[i] );	// つなぐ
	}
	return 0;
}
#ifdef PUK3_NOTFREE_ANIMEDATA
void RelAnimListBuffer( void )
{
	int i;
	// 数珠繋ぎにする。
	for( i = 0; i < ANIMMAN_TBL_MAX; i ++ ){
		// 解放
		// 确保されてないときははじかれるので问题なし
		AnimManTailFree( &aAnimManTbl[i] );
	}
}
#endif

#ifdef PUK3_RIDEBIN
	BOOL ReadCoordinateData( int sprNo );
#endif

//-------------------------------------------------
// スプライトのアニメーションデータを読む
//-------------------------------------------------
FILE *fpSpriteBin[BINMODE_MAX];

int ReadAnimationData( int i ){
	ANIMMAN *pAnimMan;
	U2 buf[1000];
	U2 *ptBuf;
	int iBinMode = SpriteData[i].BinMode, j, offset;
	U4 k;

	// すでに読み込んでいる。
	if( SpriteData[i].ptAnimlist != NULL ){
		// 先头にもどす。
		AnimManMoveTop( SpriteData[i].pAnimMan );
		return 1;
	}

	// 空きを取得する。
	pAnimMan = AnimManTailRemove( );
	if( pAnimMan == NULL )return 0;

	// ファイルポインタを読み込み位置にセットする
	fseek( fpSpriteBin[iBinMode], SpriteData[i].offset, SEEK_SET );

	offset = BinOffsetTbl[iBinMode];	// ビンのモードのOFFセット
	if( SpriteData[i].animSize <= 0 )return 0;
	// アニメーションのサイズ分メモリ确保
	SpriteData[i].ptAnimlist = (ANIMLIST *)malloc( sizeof(ANIMLIST)* SpriteData[i].animSize);
	// 确保失败したらエラー。
	if( SpriteData[i].ptAnimlist == NULL ){
		goto Err_Label;
	}else{
		// 配列を学习
		pAnimMan->iSpriteIndex = i;
		// ポインタを学习
		SpriteData[i].pAnimMan = pAnimMan;
		// 成功したら先头につける
		AnimManAddTop( pAnimMan );
		pAnimMan->iAllocSize = sizeof(ANIMLIST) * SpriteData[i].animSize;
	}
#ifdef PUK2_MEMCHECK
	memlistset( SpriteData[i].ptAnimlist, MEMLISTTYPE_ACTIONANIMLIST );
#endif
#ifdef PUK3_NOTFREE_ANIMEDATA
	// フレームリストの取得に失败したときのためにゼロクリア
	memset( SpriteData[i].ptAnimlist, 0, sizeof(ANIMLIST) * SpriteData[i].animSize );
#endif

	// サイズがでかすぎたらエラー
	if( SpriteData[i].animSize > 10000 ){
		goto Err_Label;
	}

	// アニメーションサイズ(动作の数)分ループ
	for( j = 0; j < SpriteData[i].animSize; j++ ){
		ANIMLIST *ptA;
	// アニメーションリスト构造体一つ方Kiみ込む（フレームリストポインタ以外）
#ifdef PUK2
		if (SpriteData[i].format>=1) fread( buf, 20, 1, fpSpriteBin[iBinMode] );
		else fread( buf, 12, 1, fpSpriteBin[iBinMode] );
#else
		fread( buf, 12, 1, fpSpriteBin[iBinMode] );
#endif
		ptBuf = buf;
		ptA = &(SpriteData[i].ptAnimlist[j]);

		// Animlist の情报取り出し
		ptA->dir = *ptBuf++;	// 向き
		ptA->no = *ptBuf++;	// どんなアニメーションかをあらわす番号
		ptA->dtAnim = *ptBuf++;
		ptA->dtAnim |= ((*ptBuf)<<16); ptBuf++;
		ptA->frameCnt = *ptBuf++;
		ptA->frameCnt |= ((*ptBuf)<<16);	ptBuf++;
		//１枚の表示割り込み数计算
		if( ptA->frameCnt ){
			ptA->dtAnim = ptA->dtAnim / (ptA->frameCnt << 4);
			// １フレーム以下は１にする。
			if( ptA->dtAnim < 1 ) ptA->dtAnim = 1;
		}
#ifdef PUK2
		if (SpriteData[i].format>=1){
			ptA->PalNo = *ptBuf++;						// アニメーションパレット番号
			ptA->bltf = *ptBuf++;						// 描画フラグ
			ptA->rgba = *ptBuf++;						// 色要素指定
			ptA->rgba |= ((*ptBuf)<<16);	ptBuf++;	// 色要素指定
		}else{
			ptA->PalNo = 0;								// アニメーションパレット番号
			ptA->bltf = 0;								// 描画フラグ
			ptA->rgba = 0xffffffff;						// 色要素指定
		}
#endif

		// エラーチェック
		if( ptA->frameCnt >= 10000 ){
			goto Err_Label;
		}

		ptA->ptFramelist = (FRAMELIST *) malloc( sizeof( FRAMELIST ) * ptA->frameCnt );
#ifdef PUK3_NOTFREE_ANIMEDATA
		if ( ptA->ptFramelist == NULL ) goto Err_Label;
#endif
#ifdef PUK2_MEMCHECK
		memlistset( ptA->ptFramelist, MEMLISTTYPE_ACTIONFRAMELIST );
#endif
		// アロケートサイズを加算
		pAnimMan->iAllocSize += sizeof( FRAMELIST ) * ptA->frameCnt;
		// 10バイトＸ枚数分をロード
		fread( buf, 10 * (ptA->frameCnt), 1, fpSpriteBin[iBinMode] );
		ptBuf = buf;

		// その动作のアニメの枚数分ループ。
		for( k = 0; k < ptA->frameCnt; k++ ){
			ptA->ptFramelist[k].BmpNo   = *ptBuf++;
			ptA->ptFramelist[k].BmpNo   |= ((*ptBuf)<<16);ptBuf++;
			ptA->ptFramelist[k].BmpNo += offset;
			ptA->ptFramelist[k].PosX    = *((S2 *)ptBuf); ptBuf++;
			ptA->ptFramelist[k].PosY    = *((S2 *)ptBuf); ptBuf++;
			ptA->ptFramelist[k].SoundNo = *ptBuf++;
		}
	}
#ifdef PUK3_RIDEBIN
	if ( !ReadCoordinateData(i) ) goto Err_Label;
#endif
#ifdef PUK3_NOTFREE_ANIMEDATA
	// アロケートサイズを加算
	pAnimMan->iAllocSize += SpriteData[i].cdSize * sizeof(ANIMCOORDINATE);
#endif

	{
//		char szBuffer[256];
		// カウントアップ
		giAnimTblSize += pAnimMan->iAllocSize;
		giAnimTblCount ++;

		// デバッグ用に出力してみる。
//		sprintf( szBuffer, "Add Anim %d/%d %d/%d", 
//			giAnimTblCount, ANIMMAN_TBL_MAX, pAnimMan->iAllocSize, giAnimTblSize );
		// デバッグ用に出力してみる。
//		StockChatBufferLine( szBuffer, FONT_PAL_WHITE );
	}
	return 1;

Err_Label:		// 失败したとき
	// 失败したらここで配列を返す。
	AnimManTailFree( pAnimMan );
	return 0;
}

//
// アニメーションファイル読み込み ANIMATION_BUFFER_LESS バージョン
//
int InitSprBinFileOpen( char* SPR_BIN[], char* SPRADRN_BIN[] )
{
	char BinName[BINMODE_MAX][256];
	char InfoBinName[BINMODE_MAX][256];
	int	 useFlag[mxSPRITE_MULTI];
	SPRADRN spradrn[10000];
	SPRADRN _spradrn;
	FILE  *fp2;
	int i, sprcount = 0, OverFlg = FALSE, iErr = 0;
	int iBinMode, iReadCount = 0, index = 0, iSprNo, AliveInfo[BINMODE_MAX];

	// アニメーションバッファリスト初期化
	InitAnimListBuffer(  );

	for( i = 0; i < BINMODE_MAX; i ++ ){ 
		strcpy( InfoBinName[i], SPRADRN_BIN[i] );
		strcpy( BinName[i], SPR_BIN[i] );
	}

	memset( spradrn, 0, sizeof( spradrn ) );
	memset( useFlag, 0xFF, sizeof( useFlag ) );
	memset( AliveInfo, 0, sizeof(AliveInfo ) );

	// インフォを読み込む。昔の名残で一旦别のバッファに入れる。
	for( i = 0; i < BINMODE_MAX; i ++ ){ 

		// オープンする。
		if( (fp2 = fopen( InfoBinName[i], "rb" )) == NULL ){                //MLHIDE
			// 初期型のファイルがないとエラー
			if( i == BINMODE_NORMAL )return ERR_LOADSPRBIN_FILENOTFOUND;	
			// それ以外は失败したら无视
			continue;	
		}

		// スプライトインフォビン読み込み

		for( ; ; ){	// なくなるまで无限ループ

			// １个読み込む
#ifdef PUK2
			// １个のデータサイズは１２バイト
			fread( &_spradrn, 12, 1, fp2 );
#else
			fread( &_spradrn, sizeof( _spradrn ), 1, fp2 );
#endif

			// バッファをオーバーしたらエラー？
			if( iReadCount >= mxSPRITE ){
				fclose( fp2 );
				return ERR_LOADSPRBIN_OVER_MAXSPRITE;
			}
			// 最后まで読んだら終了
			if( feof( fp2 ) != 0 ) break;
			// キャラの番号は１０万番を引いておく
			iSprNo = _spradrn.sprNo - SPRSTART;

			if( useFlag[iSprNo] != -1 ){	// すでに使っていたら
				index = useFlag[iSprNo];	// 使った场所に上书き
				if( iSprNo == 0 ){
					index = useFlag[iSprNo];	// 使った场所に上书き
				}
			}else{
				index = iReadCount;			// 次の场所に书く
				iReadCount ++;
			}
			spradrn[index].sprNo = iSprNo; // キャラの番号
			spradrn[index].offset = _spradrn.offset;	// アニメーションの位置
			spradrn[index].animSize = _spradrn.animSize; // 动作の数
#ifdef PUK2
			spradrn[index].format = _spradrn.format; // フォーマット
/**/			if (spradrn[index].format<0) spradrn[index].format=0;
/**/			if (spradrn[index].format>1) spradrn[index].format=0;
#endif
			spradrn[index].BinMode = i;			// モード
			useFlag[iSprNo] = index;		// 使った印

			if( _spradrn.animSize > 10000 ){
				char szBuffer[256];
#ifdef PUK3_ERRORMESSAGE_NUM
				sprintf( szBuffer, ERRMSG_94, i, _spradrn.animSize );
#else
				sprintf( szBuffer, "i=%d,animsize=%d", i, _spradrn.animSize );    //MLHIDE
#endif
				MessageBox( hWnd, szBuffer, "动画文件破损。", MB_OK );                   //MLHIDE
				iErr = ERR_LOADSPRBIN_BROKEN_FILE;
				fclose( fp2 );
				goto InitSprBinFileOpen_Err;
			}
		}
		// このファイルはあった。
		AliveInfo[i] = 1;
		fclose( fp2 );
	}

	//---------------------------------
	// ビンのファイルを全部オープン
	//---------------------------------
	// まず初期化
	for( i = 0; i < BINMODE_MAX; i ++ )fpSpriteBin[i]=NULL;
	// 全部あけてみる。この关数からリターンするまであけっぱなし。
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// インフォのファイルがなかったらオープンしない。
		if( AliveInfo[i] != 1 )continue;
		if( (fpSpriteBin[i] = fopen( BinName[i], "rb" )) == NULL ){         //MLHIDE
			// 初期型のファイルがなかったらエラー
			if( i == BINMODE_NORMAL ){
				iErr = ERR_LOADSPRBIN_FILENOTFOUND;
				goto InitSprBinFileOpen_Err;
			}
			// それ以外は失败したら无视
			continue;
		}
	}

	// キャラの数ループ
	for( i = 0; i < iReadCount; i++ ){
		// sugioka ＢＩＮの位置(OFFセット)记忆
		SpriteData[i].offset = spradrn[i].offset;
		SpriteData[i].BinMode = spradrn[i].BinMode;
		// サイズ记忆
		SpriteData[i].sprNo = spradrn[i].sprNo;
		SpriteData[i].animSize = spradrn[i].animSize;
#ifdef PUK2
		// フォーマット记忆
		SpriteData[i].format = spradrn[i].format;
#endif
#ifdef PUK3_RIDEBIN
		SpriteData[i].cdoffset = -1;
		SpriteData[i].cdSize = 0;
#endif
		// アニメーションリストがあるポインタを记忆
		SpriteData[i].ptAnimlist = NULL;
		SpriteData[i].pAnimMan = NULL;

		iBinMode = spradrn[i].BinMode;	// ビンモード记忆
		// ファイルがあいてなかったらキャンセル
		if( fpSpriteBin[iBinMode] == NULL ){
			continue; 
		}
	}

	// 全部のキャラの数を学习
	giAllChrNum = iReadCount;

	// bitmapnumbertable をクイックソート
	qsort( SpriteData, giAllChrNum, sizeof( SPRITEDATA ), (FUNC )SprTblCmp );

	return LOADSPRBIN_SUCCESS;


InitSprBinFileOpen_Err:	// エラー处理。全部クローズとか后始末。
#ifdef PUK2_MEMCHECK
	if( TBbuffer != NULL ) memlistrel( TBbuffer, MEMLISTTYPE_ACTIONANIMLIST );
#endif
	if( TBbuffer != NULL ) free( TBbuffer );
	for( i = 0; i < BINMODE_MAX; i ++ ){
		if( fpSpriteBin[i] != NULL ){
			fclose( fpSpriteBin[i] );
			fpSpriteBin[i] = NULL;
		}
	}
	return iErr;
}
#ifdef PUK3_NOTFREE_ANIMEDATA
void CloseSprBinFileOpen()
{
	int i;

	for( i = 0; i < BINMODE_MAX; i ++ ){
		if( fpSpriteBin[i] != NULL ){
			fclose( fpSpriteBin[i] );
			fpSpriteBin[i] = NULL;
		}
	}
	RelAnimListBuffer();
}
#endif

	#ifdef PUK3_RIDEBIN

	// 座标データ初期化( InitSprBinFileOpen()の处理が終わってから呼び出す )
	BOOL InitCoordinateBinFileOpen( FILE **datafp, char *Crddata, char *CrdInfo )
	{
		FILE *fp;
		unsigned char format;
		unsigned long bfu4[2];
		unsigned short bfu2[2];
		int chrNo, tmpNo;

		// 先ずはDataを开く
		*datafp = fopen( Crddata, "rb" );                                   //MLHIDE
		if ( !(*datafp) ) return FALSE;

		// Infoを読み込む
		fp = fopen( CrdInfo, "rb" );                                        //MLHIDE
		if (!fp) return FALSE;

		fread( &format, sizeof(format), 1, fp );

		if ( format == 1 ){
			// データの読み取り
			for(;!feof(fp);){

				// １つ分のデータをバッファに読み込む
				fread( bfu4, sizeof(bfu4[0]), 2, fp );
				fread( bfu2, sizeof(bfu2[0]), 1, fp );

				chrNo = bfu4[0] - SPRSTART;
#ifdef MULTI_GRABIN
				tmpNo = Spr_Number2Tbl( chrNo );
				if( tmpNo < 0 ) continue;
				chrNo = (UINT)(tmpNo);
#endif
				SpriteData[chrNo].cdoffset = bfu4[1];
				SpriteData[chrNo].cdSize = bfu2[0];
			}
		}else{
			return FALSE;
		}

		fclose(fp);

		return TRUE;
	}

	FILE *CrdBinfp[BINMODE_MAX];
#ifdef _DEBUG
	void InitCoordinateBinFilesOpen( char *Crddata[], char *CrdInfo[], char *CrdLog[] )
#else
	void InitCoordinateBinFilesOpen( char *Crddata[], char *CrdInfo[] )
#endif
	{
		int i,j;

		// ファイルポインタを初期化
		for(i=0;i<BINMODE_MAX;i++) CrdBinfp[i] = NULL;

		// ファイルを开く
		for(i=0;i<BINMODE_MAX;i++){
			// すでに开かれていたら次へ
			if ( CrdBinfp[i] ) continue;

			// まだなら开く
			InitCoordinateBinFileOpen(
				 &CrdBinfp[i], Crddata[i], CrdInfo[i] );

			// 同じファイルを开くバージョンがないか调べる
			for(j=i+1;j<BINMODE_MAX;j++){
				// 违ったら次へ
				if ( _stricmp( Crddata[j], Crddata[i] ) ) continue;

				// 同じだったら、ツールの事も考えて文字列をコピーする
				strcpy( Crddata[j], Crddata[i] );
				strcpy( CrdInfo[j], CrdInfo[i] );
#ifdef _DEBUG
				strcpy( CrdLog[j], CrdLog[i] );
#endif

				// ファイルポインタをコピーする
				CrdBinfp[j] = CrdBinfp[i];
			}
		}
	}

	void CloseCoordinateBinFiles()
	{
		int i,j;

		for(i=0;i<BINMODE_MAX;i++){
			// すでに闭じているなら次へ
			if ( !CrdBinfp[i] ) continue;

			// 同じファイルを开いているものがないか调べる
			for(j=i+1;j<BINMODE_MAX;j++){
				// 违うなら次へ
				if ( CrdBinfp[j] != CrdBinfp[i] ) continue;

				// 同じなら闭じた事にする
				CrdBinfp[j] = NULL;
			}

			// ファイルを关闭
			fclose(CrdBinfp[i]);
			CrdBinfp[i] = NULL;
		}
	}

	// 座标データ読み込み关数
	BOOL ReadCoordinateData( int sprNo )
	{
		short buf[1000*( sizeof(ANIMCOORDINATE) / 2)];
		short *ptBf2;
		long *ptBf4;
		int i;
		ANIMCOORDINATE *cd;
		int offset;
		int BinMode = SpriteData[sprNo].BinMode;

		// binが开けてないなら
		if ( !CrdBinfp[ BinMode ] ) return TRUE;

		// アニメーションデータがないなら終了
		if (!SpriteData[sprNo].ptAnimlist) return TRUE;
		// すでに読み込んでいるなら終了
		if ( SpriteData[sprNo].ptCdlist ) return TRUE;

		if( SpriteData[sprNo].cdSize == 0 ) return TRUE;
		if( SpriteData[sprNo].cdSize > 1000 ){
			char str[256];
#ifdef PUK3_ERRORMESSAGE_NUM
			sprintf( str, ERRMSG_95, SpriteData[sprNo].cdSize );
#else
			sprintf( str, "%d / 1000", SpriteData[sprNo].cdSize );             //MLHIDE
#endif
			MessageBox( hWnd, str, "坐标异常。", MB_OK );                           //MLHIDE
			return FALSE;
		}

		// ビンのモードのOFFセット
		offset = BinOffsetTbl[ BinMode ];

		// ファイルポインタを読み込み位置にセットする
		fseek( CrdBinfp[ BinMode ], SpriteData[sprNo].cdoffset, SEEK_SET );

		// サイズ分メモリ确保
		SpriteData[sprNo].ptCdlist = (ANIMCOORDINATE *)malloc( SpriteData[sprNo].cdSize * sizeof(ANIMCOORDINATE) );
		if( !SpriteData[sprNo].ptCdlist ) return FALSE;
#ifdef PUK2_MEMCHECK
		memlistset( SpriteData[sprNo].ptCdlist, MEMLISTTYPE_ACTIONCDLIST );
#endif

		fread( buf, sizeof(ANIMCOORDINATE), SpriteData[sprNo].cdSize, CrdBinfp[ BinMode ] );

		ptBf2 = buf;
		// 数分ループ
		for(i=0;i<SpriteData[sprNo].cdSize;i++){
			cd = &SpriteData[sprNo].ptCdlist[i];

			// 絵の番号取得
			ptBf4 = (long *)ptBf2;
			cd->graNo = *ptBf4 + offset;
			ptBf2 += 2;

			cd->x = *ptBf2;		ptBf2++;
			cd->y = *ptBf2;		ptBf2++;
		}

		return TRUE;
	}

	#endif

#else		// ANIMATION_BUFFER_LESS

//
// アニメーションファイル読み込み
//
int InitSprBinFileOpen( char* SPR_BIN[], char* SPRADRN_BIN[] )
{
	char BinName[BINMODE_MAX][256];
	char InfoBinName[BINMODE_MAX][256];
	int	 useFlag[mxSPRITE_MULTI];
	SPRADRN spradrn[10000];
	SPRADRN _spradrn;
	U2 buf[30000];
	U2 *ptBuf;
	U2 *ptAnim, *ptTailAnim;
	FILE  *fpBin[BINMODE_MAX], *fp2;
	int i, j, sprcount = 0, OverFlg = FALSE, iErr = 0;
	unsigned int k;
	int size;
	int iBinMode, iReadCount = 0, index = 0, offset, iSprNo, AliveInfo[BINMODE_MAX];

	for( i = 0; i < BINMODE_MAX; i ++ ){ 
		strcpy( InfoBinName[i], SPRADRN_BIN[i] );
		strcpy( BinName[i], SPR_BIN[i] );
	}

//	memset( SpriteData, 0, sizeof( SpriteData ) );

	memset( spradrn, 0, sizeof( spradrn ) );
	memset( useFlag, 0xFF, sizeof( useFlag ) );
	memset( AliveInfo, 0, sizeof(AliveInfo ) );

	// インフォを読み込む
	for( i = 0; i < BINMODE_MAX; i ++ ){ 

		// オープンする。
		if( (fp2 = fopen( InfoBinName[i], "rb" )) == NULL ){                //MLHIDE
			// 初期型のファイルがないとエラー
			if( i == BINMODE_NORMAL )return ERR_LOADSPRBIN_FILENOTFOUND;	
			// それ以外は失败したら无视
			continue;	
		}

		// スプライトインフォビン読み込み

		for( ; ; ){	// なくなるまで无限ループ

			// １个読み込む
			fread( &_spradrn, sizeof( _spradrn ), 1, fp2 );

			// バッファをオーバーしたらエラー？
			if( iReadCount >= mxSPRITE ){
				fclose( fp2 );
				return ERR_LOADSPRBIN_OVER_MAXSPRITE;
			}
			// 最后まで読んだら終了
			if( feof( fp2 ) != 0 ) break;
			// キャラの番号は１０万番を引いておく
			iSprNo = _spradrn.sprNo - SPRSTART;

			if( useFlag[iSprNo] != -1 ){	// すでに使っていたら
				index = useFlag[iSprNo];	// 使った场所に上书き
				if( iSprNo == 0 ){
					index = useFlag[iSprNo];	// 使った场所に上书き
				}
			}else{
				index = iReadCount;			// 次の场所に书く
				iReadCount ++;
			}
			spradrn[index].sprNo = iSprNo; // キャラの番号
			spradrn[index].offset = _spradrn.offset;	// アニメーションの位置
			spradrn[index].animSize = _spradrn.animSize; // 动作の数
			spradrn[index].BinMode = i;			// モード
			useFlag[iSprNo] = index;		// 使った印
		}
		// このファイルはあった。
		AliveInfo[i] = 1;
		fclose( fp2 );
	}

	//---------------------------------
	// ビンのファイルを全部オープン
	//---------------------------------
	// まず初期化
	for( i = 0; i < BINMODE_MAX; i ++ )fpBin[i]=NULL;
	// 全部あけてみる。この关数からリターンするまであけっぱなし。
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// インフォのファイルがなかったらオープンしない。
		if( AliveInfo[i] != 1 )continue;
		if( (fpBin[i] = fopen( BinName[i], "rb" )) == NULL ){               //MLHIDE
			// 初期型のファイルがなかったらエラー
			if( i == BINMODE_NORMAL ){
				iErr = ERR_LOADSPRBIN_FILENOTFOUND;
				goto InitSprBinFileOpen_Err;
			}
			// それ以外は失败したら无视
			continue;
		}
	}

	// サイズ设定
	TBBufSize = maxBUFFER;

	// バッファ确保
	TBbuffer = NULL;
	TBbuffer = (U2 *)malloc( sizeof(U2) * TBBufSize );
#ifdef PUK2_MEMCHECK
	memlistset( TBbuffer, MEMLISTTYPE_ACTIONANIMLIST );
#endif
	ptAnim = TBbuffer;					// 最初の位置
	ptTailAnim = TBbuffer + TBBufSize;	// 最后の位置
	sprcount = 0;
	// キャラの数ループ
	for( i = 0; i < iReadCount; i++ ){
		// 使ってなかったら次へ
//		if( useFlag[i] == -1 )continue;
		// サイズ记忆
		SpriteData[i].sprNo = spradrn[i].sprNo;
		SpriteData[i].animSize = spradrn[i].animSize;
		// アニメーションリストがあるポインタを记忆
		SpriteData[i].ptAnimlist = (ANIMLIST *)ptAnim;

		iBinMode = spradrn[i].BinMode;	// ビンモード记忆
		// ファイルがあいてなかったらキャンセル
		if( fpBin[iBinMode] == NULL ){
			continue; 
		}

		// アニメーションリスト分ポインタ进める
		ptAnim   += sizeof( ANIMLIST ) /  sizeof( U2 ) * SpriteData[i].animSize;
		sprcount += sizeof( ANIMLIST ) /  sizeof( U2 ) * SpriteData[i].animSize ;

		if( SpriteData[i].animSize > 10000 ){
			char szBuffer[256];
#ifdef PUK3_ERRORMESSAGE_NUM
			sprintf( szBuffer, ERRMSG_96, i,SpriteData[i].animSize );
#else
			sprintf( szBuffer, "i=%d,animsize=%d", i,SpriteData[i].animSize ); //MLHIDE
#endif
			MessageBox( hWnd, szBuffer, "动画文件破损。", MB_OK );                    //MLHIDE
			iErr = ERR_LOADSPRBIN_BROKEN_FILE;
			goto InitSprBinFileOpen_Err;
		}

		// リミットチェック
		if( ptAnim >= ptTailAnim ){
			OverFlg = TRUE;
			ptAnim = TBbuffer;
/*
			char szBuffer[256];
			sprintf( szBuffer, "chr=%d count=%d", i, iReadCount );
 			MessageBox( hWnd, szBuffer, "ANIMLIST", MB_OK );
 			MessageBox( hWnd, "SPR Buffer溢出", "ANIMLIST", MB_OK );
			goto InitSprBinFileOpen_Err;
*/
		}

		// ファイルポインタを読み込み位置にセットする
		fseek( fpBin[iBinMode], spradrn[i].offset, SEEK_SET );

		offset = BinOffsetTbl[iBinMode];	// ビンのモードのOFFセット

		// アニメーションサイズ(动作の数)分ループ
		for( j = 0; j < SpriteData[i].animSize; j++ ){
			ANIMLIST *ptA;
		// アニメーションリスト构造体一つ方Kiみ込む（フレームリストポインタ以外）
			fread( buf, 12, 1, fpBin[iBinMode] );
//			fread( buf, 12, 1, fp1 );
			//fread( buf, 1, 12, fp1 );
			ptBuf = buf;
			ptA = &(SpriteData[i].ptAnimlist[j]);

			// Animlist の情报取り出し
			ptA->dir = *ptBuf++;	// 向き
			ptA->no = *ptBuf++;	// どんなアニメーションかをあらわす番号
			ptA->dtAnim = *ptBuf++;
			ptA->dtAnim |= ((*ptBuf)<<16); ptBuf++;
			ptA->frameCnt = *ptBuf++;
			ptA->frameCnt |= ((*ptBuf)<<16);	ptBuf++;
			//１枚の表示割り込み数计算
			if( ptA->frameCnt ){
				ptA->dtAnim = ptA->dtAnim / (ptA->frameCnt << 4);
				// １フレーム以下は１にする。
				if( ptA->dtAnim < 1 ) ptA->dtAnim = 1;
			}

			// フレームリストがあるポインタを记忆
			ptA->ptFramelist = (FRAMELIST *)ptAnim;

			// 次のアニメーションリストの场所までポインタ进める
			sprcount += ptA->frameCnt * sizeof( FRAMELIST ) / sizeof( U2 );
			ptAnim   += ptA->frameCnt * sizeof( FRAMELIST ) / sizeof( U2 );
			if( ptA->frameCnt > 10000 ){
				char szBuffer[256];
#ifdef PUK3_ERRORMESSAGE_NUM
				sprintf( szBuffer, ERRMSG_97, i,j,ptA->frameCnt );
#else
				sprintf( szBuffer, "i=%d,j=%d,frameCnt=%d", i,j,ptA->frameCnt );  //MLHIDE
#endif
				MessageBox( hWnd, szBuffer, "动画文件破损", MB_OK );                    //MLHIDE
				iErr = ERR_LOADSPRBIN_BROKEN_FILE;
				goto InitSprBinFileOpen_Err;
			}

			// リミットチェック
			if( ptAnim >= ptTailAnim ){
				OverFlg = TRUE;
				ptAnim = TBbuffer;
/*
				char szBuffer[256];
				sprintf( szBuffer, "chr=%d count=%d", i, iReadCount );
				MessageBox( hWnd, szBuffer, "FRAMELIST", MB_OK );
				goto InitSprBinFileOpen_Err;
*/
			}
				
#if 1				
			size = 10 * (ptA->frameCnt);
			//fread( buf, 1, size*sizeof( U2 ), fp1 );
			fread( buf, size, 1, fpBin[iBinMode] );
//			fread( buf, size, 1, fp1 );
			ptBuf = buf;
#else				
			// 読み込みサイズ决定
			size = sizeof( FRAMELIST ) * ptA->frameCnt;
			// バッファに読み込み
			fread( buf, size, 1, fp1 );
			ptBuf = buf;
#endif
			if( OverFlg == FALSE ){
			// その动作のアニメの枚数分ループ。

			for( k = 0; k < ptA->frameCnt; k++ ){
				ptA->ptFramelist[k].BmpNo   = *ptBuf++;
				ptA->ptFramelist[k].BmpNo   |= ((*ptBuf)<<16);ptBuf++;
				ptA->ptFramelist[k].BmpNo += offset;
				ptA->ptFramelist[k].PosX    = *((S2 *)ptBuf); ptBuf++;
				ptA->ptFramelist[k].PosY    = *((S2 *)ptBuf); ptBuf++;
				ptA->ptFramelist[k].SoundNo = *ptBuf++;
			}
			}
		}
	}

	// 全部クローズ
	for( i = 0; i < BINMODE_MAX; i ++ ){
		if( fpBin[i] != NULL ){
			fclose( fpBin[i] );
			fpBin[i] = NULL;
		}
	}

	if( OverFlg == TRUE ){
		char szBuffer[256];
#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( szBuffer, ERRMSG_98, TBBufSize, sprcount );
#else
		sprintf( szBuffer, "MaxBuffer=%d Need=%d", TBBufSize, sprcount );   //MLHIDE
#endif
		MessageBox( hWnd, szBuffer, "缓存不足", MB_OK );                        //MLHIDE
		TBBufSize = sprcount+ 100;	// サイズ调整
		iErr = ERR_LOADSPRBIN_OVER_TBBUFFER;
		goto InitSprBinFileOpen_Err;
	}

	// 全部のキャラの数を学习
	giAllChrNum = iReadCount;

	// bitmapnumbertable をクイックソート
	qsort( SpriteData, giAllChrNum, sizeof( SPRITEDATA ), (FUNC )SprTblCmp );

	return LOADSPRBIN_SUCCESS;


InitSprBinFileOpen_Err:	// エラー处理。全部クローズとか后始末。
#ifdef PUK2_MEMCHECK
	if( TBbuffer != NULL ) memlistrel( TBbuffer, MEMLISTTYPE_ACTIONANIMLIST );
#endif
	if( TBbuffer != NULL ) free( TBbuffer );
	for( i = 0; i < BINMODE_MAX; i ++ ){
		if( fpBin[i] != NULL ){
			fclose( fpBin[i] );
			fpBin[i] = NULL;
		}
	}
	return iErr;
}
#endif

#else

//
// アニメーションファイル読み込み
//
int InitSprBinFileOpen( char *SPR_BIN, char *SPRADRN_BIN )
{
	char useFlag[mxSPRITE_SINGLE];
	SPRADRN spradrn[mxSPRITE_SINGLE];
	SPRADRN _spradrn;
	U2 buf[30000];
	U2 *ptBuf;
	U2 *ptAnim, *ptTailAnim;
	FILE *fp1, *fp2;
	int i, j, sprcount = 0, OverFlg = FALSE, iErr = 0;
	unsigned int k;
	int size;

	if( (fp1 = fopen( SPR_BIN, "rb" )) == NULL )                         //MLHIDE
	{
		return FALSE;
	}

	if( (fp2 = fopen( SPRADRN_BIN, "rb" )) == NULL )                     //MLHIDE
	{
		return FALSE;
	}

	//memset( TBbuffer, 0, sizeof( TBbuffer ) );
	//memset( SpriteData, 0, sizeof( SpriteData ) );
	TBbuffer = NULL;
	TBbuffer = (U2 *)malloc( sizeof(U2) * TBBufSize );
#ifdef PUK2_MEMCHECK
	memlistset( TBbuffer, MEMLISTTYPE_ACTIONANIMLIST );
#endif
	if( TBbuffer == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_99, "ANIMLIST", MB_OK );                   //MLHIDE
#else
		MessageBox( hWnd, "无法申请内存", "ANIMLIST", MB_OK );                    //MLHIDE
#endif
		return ERR_LOADSPRBIN_MEMORY;
	}
	memset( spradrn, 0, sizeof( spradrn ) );
	memset( useFlag, 0, sizeof( useFlag ) );
	// スプライトインフォビン読み込み
	for( i = 0; i < mxSPRITE; i++ )
	{
		//fread( &_spradrn, 1, sizeof( _spradrn ), fp2 );
		fread( &_spradrn, sizeof( _spradrn ), 1, fp2 );
		if( feof( fp2 ) != 0 ) break;
		spradrn[_spradrn.sprNo - SPRSTART].sprNo = _spradrn.sprNo - SPRSTART;
		spradrn[_spradrn.sprNo - SPRSTART].offset = _spradrn.offset;
		spradrn[_spradrn.sprNo - SPRSTART].animSize = _spradrn.animSize;
		useFlag[_spradrn.sprNo - SPRSTART] = 1;
	}
	
	ptAnim = TBbuffer;
	ptTailAnim = TBbuffer + TBBufSize;	// 最后の位置
	// アニメーション种类ループ
	for( i = 0; i < mxSPRITE; i++ )
	{
		if( useFlag[i] != 0 )
		{
//			if( i == 10059 ) 
//				i = 10059;
				
			// サイズ记忆
			SpriteData[i].animSize = spradrn[i].animSize;
			// アニメーションリストがあるポインタを记忆
			SpriteData[i].ptAnimlist = (ANIMLIST *)ptAnim;
			
			// アニメーションリスト分ポインタ进める
			ptAnim += ( sizeof( ANIMLIST ) / sizeof( U2 ) )* SpriteData[i].animSize;
			sprcount += ( sizeof( ANIMLIST ) / sizeof( U2 ) )* SpriteData[i].animSize;
			// リミットチェック
			if( ptAnim >= ptTailAnim ){
				OverFlg = TRUE;
				ptAnim = TBbuffer;
// 				MessageBox( hWnd, "SPR Buffer溢出", "ANIMLIST", MB_OK );
//				return FALSE;
			}
			
			// ファイルポインタを読み込み位置にセットする
			fseek( fp1, spradrn[i].offset, SEEK_SET );
			
			// アニメーションサイズの数分ループ
			for( j = 0; j < SpriteData[i].animSize; j++ )
			{
				// アニメーションリスト构造体一つ方Kiみ込む（フレームリストポインタ以外）
				fread( buf, 12, 1, fp1 );
				//fread( buf, 1, 12, fp1 );
				ptBuf = buf;
				
				// Animlist の情报取り出し
				SpriteData[i].ptAnimlist[j].dir = *ptBuf++;	// 向き
				SpriteData[i].ptAnimlist[j].no = *ptBuf++;	// どんなアニメーションかをあらわす番号
				SpriteData[i].ptAnimlist[j].dtAnim = *ptBuf++;
				SpriteData[i].ptAnimlist[j].dtAnim |= ((*ptBuf)<<16);
				ptBuf++;
				SpriteData[i].ptAnimlist[j].frameCnt = *ptBuf++;
				SpriteData[i].ptAnimlist[j].frameCnt |= ((*ptBuf)<<16);
				ptBuf++;

				//１枚の表示割り込み数计算
				if( SpriteData[i].ptAnimlist[j].frameCnt )
				{
					SpriteData[i].ptAnimlist[j].dtAnim =
						SpriteData[i].ptAnimlist[j].dtAnim /
							(SpriteData[i].ptAnimlist[j].frameCnt << 4);
					if( SpriteData[i].ptAnimlist[j].dtAnim < 1 )
						SpriteData[i].ptAnimlist[j].dtAnim = 1;
				}
				
				// フレームリストがあるポインタを记忆
				SpriteData[i].ptAnimlist[j].ptFramelist = (FRAMELIST *)ptAnim;
				
				// 次のアニメーションリストの场所までポインタ进める
				ptAnim += ( sizeof( FRAMELIST ) / sizeof( U2 ) ) * SpriteData[i].ptAnimlist[j].frameCnt;
				sprcount += ( sizeof( FRAMELIST ) / sizeof( U2 ) ) * SpriteData[i].ptAnimlist[j].frameCnt;
				// リミットチェック
				if( ptAnim >= ptTailAnim ){
		 			OverFlg = TRUE;
					ptAnim = TBbuffer;
//					MessageBox( hWnd, "SPR Buffer溢出", "FRAMELIST", MB_OK );
//					return FALSE;
				}
				
#if 1				
				size = 10 * SpriteData[i].ptAnimlist[j].frameCnt;
				//fread( buf, 1, size*sizeof( U2 ), fp1 );
				fread( buf, size, 1, fp1 );
				ptBuf = buf;
#else				
				
				
				// 読み込みサイズ决定
				size = sizeof( FRAMELIST ) * SpriteData[i].ptAnimlist[j].frameCnt;
				// バッファに読み込み
				fread( buf, size, 1, fp1 );
				ptBuf = buf;
#endif
				if( OverFlg == FALSE ){
					for( k = 0; k < SpriteData[i].ptAnimlist[j].frameCnt; k++ )
					{
						SpriteData[i].ptAnimlist[j].ptFramelist[k].BmpNo   = *ptBuf++;
						SpriteData[i].ptAnimlist[j].ptFramelist[k].BmpNo   |= ((*ptBuf)<<16);
						ptBuf++;
						SpriteData[i].ptAnimlist[j].ptFramelist[k].PosX    = *((S2 *)ptBuf);
						ptBuf++;
						SpriteData[i].ptAnimlist[j].ptFramelist[k].PosY    = *((S2 *)ptBuf);
						ptBuf++;
						SpriteData[i].ptAnimlist[j].ptFramelist[k].SoundNo = *ptBuf++;
					}
				}
			}
		}
	}
	fclose( fp1 );
	fclose( fp2 );

	if( OverFlg == TRUE ){
		char szBuffer[256];
#ifdef PUK2_MEMCHECK
		if( TBbuffer != NULL ) memlistrel( TBbuffer, MEMLISTTYPE_ACTIONANIMLIST );
#endif
		if( TBbuffer != NULL ) free( TBbuffer );
#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( szBuffer, ERRMSG_100, TBBufSize, sprcount );
#else
		sprintf( szBuffer, "MaxBuffer=%d Need=%d", TBBufSize, sprcount );   //MLHIDE
#endif
		MessageBox( hWnd, szBuffer, "缓存不足", MB_OK );                        //MLHIDE
		TBBufSize = sprcount+ 100;	// サイズ调整
		return ERR_LOADSPRBIN_OVER_TBBUFFER;
	}

	return LOADSPRBIN_SUCCESS;
}

#endif

