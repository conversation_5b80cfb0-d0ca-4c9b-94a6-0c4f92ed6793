﻿/************************/
/*	battleMap.h			*/
/************************/
#ifndef _BATTLE_MAP_H_
#define _BATTLE_MAP_H_

// バトルマップファイルの数
#define BATTLE_MAP_FILES 218

// 现在のバトルマップ番号
extern int BattleMapNo;

// バトルマップ座标
extern int BattleMapX;
extern int BattleMapY;

// 地震フラグ
extern BOOL BattleMapQuakeFlag;
extern int BattleMapAccele;
extern int BattleMapAcceleMax;

// バトルマップ読み込みとバトルサーフェスの画像作成 ***************************/
BOOL ReadBattleMap( int no );

// バトルマップ描画 ***********************************************************/
void DrawBattleMap( void );

#endif
