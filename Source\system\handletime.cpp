﻿#define __HANDLETIME_C__

#include <time.h>
#include "../systeminc/system.h"
#include "../systeminc/process.h"
#include "../systeminc/handletime.h"
#include "../systeminc/directDraw.h"
#ifdef PUK2
#include "../systeminc/pc.h"
#endif

// 秒单位
//#define NRTIME_HOUR		10						// NR１时间の秒数（ＮＲ１时间は５分）
#if 0		// 时间变动を高速化
#define NRTIME_HOUR		30						// NR１时间の秒数（ＮＲ１时间は５分）
#else
#define NRTIME_HOUR		300						// NR１时间の秒数（ＮＲ１时间は５分）
#endif
#define NRTIME_DAY		( NRTIME_HOUR * 24 )	// NR１日の秒数（ＮＲ１日は２时间）
#define NRTIME_MONTH	( NRTIME_DAY * 30 )		// NR１ヶ月の秒数（ＮＲ１ヶ月は６０时间（二日半））
#define NRTIME_YEAR		( NRTIME_MONTH * 12 )	// NR１年の秒数（ＮＲ１年は７２０时间（３０日））
#ifdef PUK2
#define NRTIME_MIN		( NRTIME_HOUR / 60 )	// NR１分の秒数（ＮＲ１分は５秒）
#endif

NRTIME nrTime;				// NRの时间管理
long serverTime;			// サーバの时间
long clientTime;			// クライアントの时间
NRTIME_SECTION nrTimeZoneNo;// 现在の时间带
BOOL TimeZonePalChangeFlag; // 时间带でパレットチェンジするフラグ
#ifdef PUK2
long adjustTime;			// サーバーごとの时间补正
	#ifdef _DEBUG
		long debugTime = 0;
	#endif
#endif

// NR の 纪元を决定
// ２００１年４月２６日２０时２５分ぐらい
static long era = ( long )988284391;


//-------------------------------------------------------------------------//
// 实时间をNR时间に变换する                                                //
//-------------------------------------------------------------------------//
// 引　数: nrtime ... NRTIME构造体へのポインタ
//
void realTimeToNRTime( NRTIME *nrtime )
{
	unsigned long nrSec;		// NR元年からの秒数
	unsigned long amariYear;	// NR元年からあまり秒数
	unsigned long amariMonth;	// NR月からのあまり秒数
	unsigned long amariDay;		// NR日からのあまり秒数
#ifdef PUK2
	unsigned long amariHour;	// NR时からのあまり秒数
#endif
	
	// NR元年からの秒数
	nrSec = ( (unsigned long)time( NULL ) - clientTime ) + ( serverTime  - era );
#ifdef PUK2
	nrSec += adjustTime;
	#ifdef _DEBUG
		nrSec += debugTime;
	#endif
#endif
	// 何年
	nrtime->year = ( int )( nrSec / NRTIME_YEAR );
	//nrtime->year = ( ( int )nrSec / NRTIME_YEAR );
	//nrtime->year = ( int )nrSec / 2592000;
	amariYear = nrSec % NRTIME_YEAR;
	// 何月
	nrtime->month = ( int )( amariYear / NRTIME_MONTH );
	amariMonth = amariYear % NRTIME_MONTH;
	// 何日
	nrtime->day = ( int )( amariMonth / NRTIME_DAY );
	amariDay = amariMonth % NRTIME_DAY;
	// 何时
	nrtime->hour = ( int )( amariDay / NRTIME_HOUR );
#ifdef PUK2
	amariHour = amariDay % NRTIME_HOUR;
	// 何时
#if NRTIME_HOUR >= 60
	nrtime->min = ( int )( amariHour / NRTIME_MIN );
#else
	nrtime->min = ( int )( ( amariHour * 60 ) / NRTIME_HOUR );
#endif
#endif

	// 时间アニメの表示画像计算用(192はアニメ画像の横幅)
	nrtime->animeTime = ( nrSec % NRTIME_DAY ) * 192 / NRTIME_DAY;
	nrtime->animeTime = ( nrtime->animeTime + 168 ) % 192;

	return;
}

//-------------------------------------------------------------------------//
// NR时间で今の时间区分を得る
//-------------------------------------------------------------------------//
//	引　数: nrtime ... NRTIME构造体へのポインタ
//	戾り值: 0 ... NR_NIGHT
//          1 ... NR_MORNING
//          2 ... NR_NOON
//          3 ... NR_EVENING
//
NRTIME_SECTION getNRTime( NRTIME *nrtime )
{
	if( ( EVENING_TO_NIGHT <= nrtime->hour && nrtime->hour < 24 )
		|| ( 0 <= nrtime->hour && nrtime->hour < NIGHT_TO_MORNING ) )
		return NR_NIGHT;
	else
	if( nrtime->hour < MORNING_TO_NOON )
		return NR_MORNING;
	else
	if( nrtime->hour < NOON_TO_EVENING )
		return NR_NOON;
	else
		return NR_EVENING;
}


//-------------------------------------------------------------------------//
// 时间带处理とパレットチェンジ处理                                        //
//-------------------------------------------------------------------------//
void timeZoneProc( void )
{
	NRTIME_SECTION timeZoneNo;

	// ＮＲ时间で今の时间区分を得る
	timeZoneNo = getNRTime ( &nrTime );

	// 时间带が变わった时
	if( nrTimeZoneNo != timeZoneNo )
	{
		// 时间带更新
		nrTimeZoneNo = timeZoneNo;
		// 时间带でパレットチェンジする时
		if( TimeZonePalChangeFlag == TRUE )
		{
			// パレットチェンジ
			PaletteChange( nrTimeZoneNo, PAL_CHANGE_TIME );
		}
	}

#ifdef PUK2
	LocalBurstTimeProc();
#endif

}

#ifdef PUK2
//-------------------------------------------------------------------------//
// 内部バーストタイムのカウント			                                      //
//-------------------------------------------------------------------------//
void LocalBurstTimeProc(void){

	int nowTime;

	/* プロセス分岐 */
	switch( ProcNo ){
		//フィールド中	
		case PROC_GAME:
			// ＰＣが存在しているなら、バースト时间をカウント
			if( pc.ptAct != NULL ){
				nowTime = GetTickCount();

				// バースト起动中か
				if( pc.btFlg ){
					pc.bt -= ( nowTime - pc.btLast );
					if( pc.bt < 0 ){
						pc.bt = 0;
						pc.btFlg = 0;
					}
				}
				pc.btLast = nowTime;
			}
			break;

		//バトル中	
		case PROC_BATTLE:
			nowTime = GetTickCount();
			// バースト起动中か
			if( pc.btFlg ){
				pc.bt -= ( nowTime - pc.btLast );
				if( pc.bt < 0 ){
					pc.bt = 0;
					pc.btFlg = 0;
				}
			}
			pc.btLast = nowTime;
			break;
	}


}

#endif

#if defined(PUK2) && defined(_DEBUG)

void addDebugNRTime( int year, int month, int day, int hour, int min, int sec )
{
	debugTime += year * NRTIME_YEAR;
	debugTime += month * NRTIME_MONTH;
	debugTime += day * NRTIME_DAY;
	debugTime += hour * NRTIME_HOUR;
	debugTime += min * NRTIME_MIN;
	debugTime += sec;
}

void clearDebugNRTime()
{
	debugTime = 0;
}

#endif