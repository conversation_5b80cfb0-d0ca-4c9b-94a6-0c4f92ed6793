﻿
static ADDRESS_BOOK_SORT_TBL menuWindowGroupMailGuildSortTbl[ADDRESS_BOOK];
static int menuWindowGroupMailViewLine = 0;

static void MenuWindowGroupMailShowGroupMember ();
static void MenuWindowGroupMailShowGuildMember ();
static void MenuWindowGroupMailSort ();
static int  MenuWindowGroupMailSortGroup (const void* _pt1, const void* _pt2);
static void MenuWindowGroupMailSyncViewAndTab ();
static void MenuWindowGroupMailScrollBarTest (int mouse);
void MenuWindowGroupMailReDraw(void);

int MenuWindowAddressBookCompareFromVal (const void* _pt1, const void* _pt2);

GroupMailStatus GMST;
int AddressBookSortModeVal;

ADDRESS_BOOK_SORT_TBL MenuGroupSortTable[GUILD_MEMBER_MAX];

//ウインドウ处理
BOOL MenuWindowGroupMail (int mouse)
{

	if (mouse == WIN_INIT){
#ifdef PUK3_MAIL_ETC
		if ( GMST.ReOpenFlag ){
		}else{
			menuWindowGroupMailViewLine = 0;
			InitGropuMailStatusFlag();
			GMST.MemberType=GroupMailAddressMode;
			SetGropuMailStatusFlag(GMST.MemberType);

			wI->sw[EnumMenuWindowGroupMailSendTo0].Enabled=TRUE;
		}
		GMST.ReOpenFlag = FALSE;
#else
		menuWindowGroupMailViewLine = 0;
		InitGropuMailStatusFlag();
		GMST.MemberType=GroupMailAddressMode;
		SetGropuMailStatusFlag(GMST.MemberType);

		wI->sw[EnumMenuWindowGroupMailSendTo0].Enabled=TRUE;
#endif

	}else{
		if(WindowFlag[MENU_WINDOW_ADDRESS_SENDMAIL].wininfo==NULL){
			//群邮件ウインドウはメールウインドウが先に作られている前提のウインドウです。
			WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo->flag |= WIN_INFO_DEL;
		}
	}

//	仕样が变わったのでいらないのですが残しておきます。
//	MenuWindowGroupMailSort ();

	return TRUE;
}

BOOL MenuWindowGroupMailDel (void){

	//メールウインドウを闭じます
	if(WindowFlag[MENU_WINDOW_ADDRESS_SENDMAIL].wininfo!=NULL && MailStatusST.GroupMailFlag==1){
		WindowFlag[MENU_WINDOW_ADDRESS_SENDMAIL].wininfo->flag |= WIN_INFO_DEL;
	}

	return TRUE;
}


//ウインドウ描画
BOOL MenuWindowGroupMailDraw (int mouse)
{

	MenuWindowGroupMailScrollBarTest (mouse);

	MenuWindowGroupMailReDraw();
	
	displayMenuWindow ();

	return TRUE;
}

//ページ变更スイッチ
BOOL MenuWindowGroupMailPageChange (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	switch(no){
		//←
		case EnumMenuWindowGroupMailPrevPage:
			Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
			Graph->graNo=GID_groupMailPrevPageOn;

			if(flag & MENU_MOUSE_OVER){
				Graph->graNo=GID_groupMailPrevPageOver; 
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFT ){
				Graph->graNo = GID_groupMailPrevPageOff;
				menuWindowGroupMailViewLine -= MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE;
				MenuWindowGroupMailSyncViewAndTab ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				Graph->graNo=GID_groupMailPrevPageOff;
			}
			break;

		//→
		case EnumMenuWindowGroupMailNextPage:
			Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
			Graph->graNo=GID_groupMailNextPageOn;

			if(flag & MENU_MOUSE_OVER){
				Graph->graNo=GID_groupMailNextPageOver; 
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFT ){
				Graph->graNo = GID_groupMailNextPageOff;
				menuWindowGroupMailViewLine += MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE;
				MenuWindowGroupMailSyncViewAndTab ();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				Graph->graNo=GID_groupMailNextPageOff; 
			}

			break;
	}

	return ReturnFlag;
}

//スクロールバースイッチ
BOOL MenuWindowGroupMailScrollOneLine (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;
	int bottom;

	switch(no){
		//↑
		case EnumMenuWindowGroupMailScrollUp:
			Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
			Graph->graNo=GID_groupMailScrollUpArrowOn;

			if(flag & MENU_MOUSE_OVER){
				Graph->graNo=GID_groupMailScrollUpArrowOver;
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTAUTO ){
				Graph->graNo = GID_groupMailScrollUpArrowOff;
				--menuWindowGroupMailViewLine;
				if (menuWindowGroupMailViewLine < 0){
					menuWindowGroupMailViewLine = 0;
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );	
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				MenuWindowGroupMailSyncViewAndTab ();
				ReturnFlag=TRUE;
			}

			break;

		//↓
		case EnumMenuWindowGroupMailScrollDown:
			Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
			Graph->graNo=GID_groupMailScrollDownArrowOn;

			if(flag & MENU_MOUSE_OVER){
				Graph->graNo=GID_groupMailScrollDownArrowOver;
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTAUTO ){
				Graph->graNo = GID_groupMailScrollDownArrowOff;
				++menuWindowGroupMailViewLine;
				bottom = GMST.MemberCount - MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE;
				if (menuWindowGroupMailViewLine > bottom){
					menuWindowGroupMailViewLine = bottom;
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				MenuWindowGroupMailSyncViewAndTab ();
				ReturnFlag=TRUE;
			}

			break;
	}
	return ReturnFlag;
}

//スクロールバーの值の调整
static void MenuWindowGroupMailSyncViewAndTab ()
{
	int tab, bottom;

	if (menuWindowGroupMailViewLine < 0)
		menuWindowGroupMailViewLine = 0;

	bottom = GMST.MemberCount - MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE;
	if (menuWindowGroupMailViewLine > bottom)
		menuWindowGroupMailViewLine = bottom;

	tab = menuWindowGroupMailViewLine;

	NumToScrollVMove (&wI->sw[EnumMenuWindowGroupMailScrollBar], bottom, tab);
}

//スクロールバーのつまみから值を取得
static void MenuWindowGroupMailScrollBarTest (int mouse)
{
	int max;

	if(GMST.MemberCount-MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE<0){
		max=0;
	}else{
		max=GMST.MemberCount-MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE;
	}

	menuWindowGroupMailViewLine = ScrollVPointToNum(
		&wI->sw[EnumMenuWindowGroupMailScrollBar], 
		max);

}

//テーブルソート（现状未使用）
static void MenuWindowGroupMailSort ()
{
	ADDRESS_BOOK_SORT_TBL guildSortTbl[ADDRESS_BOOK];
	int i, j;
	char name[256];
	
	for (i = 0; i < ADDRESS_BOOK; ++i) {
		guildSortTbl[i].index = i;
		menuWindowGroupMailGuildSortTbl[i].index = -1;
	}
	
	qsort (guildSortTbl, ADDRESS_BOOK, sizeof (ADDRESS_BOOK_SORT_TBL), 
		MenuWindowGroupMailSortGroup);
	
	strcpy (name, addressBook[guildSortTbl[0].index].guildName);

	j = 0;
	menuWindowGroupMailGuildSortTbl[j++].index = 0;
	for (i = 1; i < ADDRESS_BOOK; ++i) {
		if (strcmp (name,
			addressBook[guildSortTbl[i].index].guildName) == 0)
				continue;
		strcpy (name, addressBook[guildSortTbl[i].index].guildName);
		menuWindowGroupMailGuildSortTbl[j++].index = i;
	}
}

//ソート基准（现状未使用）
static int MenuWindowGroupMailSortGroup (const void* _pt1, const void* _pt2)
{
	ADDRESS_BOOK_SORT_TBL* pt1 = (ADDRESS_BOOK_SORT_TBL*) _pt1;
	ADDRESS_BOOK_SORT_TBL* pt2 = (ADDRESS_BOOK_SORT_TBL*) _pt2;

	if (addressBook[pt2->index].useFlag == 0)
		return -1;
	if (addressBook[pt1->index].useFlag == 0)
		return 1;

	return sortKeyGuild (&addressBook[pt1->index], &addressBook[pt2->index]);
}

//绘图设定
void MenuWindowGroupMailReDraw(void){

	int i;

	switch(GMST.MemberType){
		//アドレスメンバー
		case GroupMailAddressMode:
			//项目			
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGroupMailType].Switch)->text,MenuWindowGroupMailText[4].text);
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGroupMailContent0].Switch)->text,MenuWindowGroupMailText[0].text);
			//リスト			
			for (i = 0; i < 7; ++i) {
				if (GMST.MemberSort[menuWindowGroupMailViewLine+i]!=-1){
					strcpy(((TEXT_SWITCH*)wI->sw[EnumMenuWindowGroupMailContent1 + i].Switch)->text,
						addressBook[GMST.MemberSort[menuWindowGroupMailViewLine+i]].name);
					wI->sw[EnumMenuWindowGroupMailContent1 + i].Enabled=TRUE;
					wI->sw[EnumMenuWindowGroupMailSendTo1+i].Enabled=TRUE;
					wI->sw[EnumMenuWindowGroupMailBar1+i].Enabled=TRUE;
				}else{
					//ない
					wI->sw[EnumMenuWindowGroupMailContent1 + i].Enabled=FALSE;
					wI->sw[EnumMenuWindowGroupMailSendTo1+i].Enabled=FALSE;
					wI->sw[EnumMenuWindowGroupMailBar1+i].Enabled=FALSE;
				}
			}
			break;

		//家族成员
		case GroupMailGuildMode:
			strcpy(((TEXT_SWITCH*)wI->sw[EnumMenuWindowGroupMailType].Switch)->text,MenuWindowGroupMailText[5].text);
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGroupMailContent0].Switch)->text,MenuWindowGroupMailText[1].text);
			//リスト			
			for (i = 0; i < 7; ++i) {
				if (GMST.MemberSort[menuWindowGroupMailViewLine+i]!=-1){
					strcpy(((TEXT_SWITCH*)wI->sw[EnumMenuWindowGroupMailContent1 + i].Switch)->text,
						guildBook.member[GMST.MemberSort[menuWindowGroupMailViewLine+i]].address.name);
					wI->sw[EnumMenuWindowGroupMailContent1 + i].Enabled=TRUE;
					wI->sw[EnumMenuWindowGroupMailSendTo1+i].Enabled=TRUE;
					wI->sw[EnumMenuWindowGroupMailBar1+i].Enabled=TRUE;
				}else{
					//ない
					wI->sw[EnumMenuWindowGroupMailContent1 + i].Enabled=FALSE;
					wI->sw[EnumMenuWindowGroupMailSendTo1+i].Enabled=FALSE;
					wI->sw[EnumMenuWindowGroupMailBar1+i].Enabled=FALSE;
				}
			}
			break;

		//家族称号
		case GroupMailGuildTitleMode:
			strcpy(((TEXT_SWITCH*)wI->sw[EnumMenuWindowGroupMailType].Switch)->text,MenuWindowGroupMailText[6].text);
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGroupMailContent0].Switch)->text,MenuWindowGroupMailText[2].text);
			//リスト			
			for (i = 0; i < 7; ++i) {
				if (GMST.MemberSort[menuWindowGroupMailViewLine+i]!=-1){
					strcpy(((TEXT_SWITCH*)wI->sw[EnumMenuWindowGroupMailContent1 + i].Switch)->text,
						guildBook.title[menuWindowGroupMailViewLine+i].name);
					wI->sw[EnumMenuWindowGroupMailContent1 + i].Enabled=TRUE;
					wI->sw[EnumMenuWindowGroupMailSendTo1+i].Enabled=TRUE;
					wI->sw[EnumMenuWindowGroupMailBar1+i].Enabled=TRUE;
				}else{
					//ない
					wI->sw[EnumMenuWindowGroupMailContent1 + i].Enabled=FALSE;
					wI->sw[EnumMenuWindowGroupMailSendTo1+i].Enabled=FALSE;
					wI->sw[EnumMenuWindowGroupMailBar1+i].Enabled=FALSE;
				}
			}
			break;
	}
}

//群邮件种类チェンジボタン
BOOL MenuWindowGroupMailTypeChange (int no, unsigned int flag)
{

	BOOL ReturnFlag=FALSE;

	switch(no){
		case EnumMenuWindowGroupMailPrevType:

			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailPrevPageOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPCHANGE );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailPrevPageOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				GMST.MemberType--;
				if(GMST.MemberType<0)
					GMST.MemberType=2;	

				SetGropuMailStatusFlag(GMST.MemberType);
				menuWindowGroupMailViewLine=0;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailPrevPageOff;
			}

			break;

		case EnumMenuWindowGroupMailNextType:
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailNextPageOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPCHANGE );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailNextPageOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				GMST.MemberType++;
				if(GMST.MemberType>2)
					GMST.MemberType=0;	

				SetGropuMailStatusFlag(GMST.MemberType);
				menuWindowGroupMailViewLine=0;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailNextPageOff;
			}
			break;
	}
	
	return ReturnFlag;

}
		
//-------------------------------------------------------------------------
//杉山
//-------------------------------------------------------------------------

//群邮件スイッチ
BOOL MenuGroupMailSetSwitch (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;	
	int i;

	if(flag & MENU_MOUSE_OVER){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailMailMenuOver;
		//送るメンバー全员のフラグ立てる
		switch(GMST.MemberType){
			//アドレスメンバー（OnLineの人）
			case GroupMailAddressMode:
				strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPSEND );
			break;
			//家族成员（OnLineの人）
			case GroupMailGuildMode:
				strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPSEND );
			break;
			//家族称号（全员）
			case GroupMailGuildTitleMode:
				strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPSEND );
			break;
		}

		ReturnFlag=TRUE;
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailMailMenuOn;
	}

	if( flag & MENU_MOUSE_LEFT ){
		//送るメンバー全员のフラグ立てる
		switch(GMST.MemberType){
			//アドレスメンバー（OnLineの人）
			case GroupMailAddressMode:
				for(i=0;i<GMST.MemberCount;i++){
					if(GMST.MemberSort[i]!=-1){
						if(addressBook[MenuGroupSortTable[i].index].onlineFlag){
							GMST.MemberFlag[i]=1;
						}
					}
				}
				play_se( SE_NO_CLICK, 320, 240 );
				break;

			//家族成员（OnLineの人）
			case GroupMailGuildMode:
				for(i=0;i<GMST.MemberCount;i++){
					if(GMST.MemberSort[i]!=-1){
						if(guildBook.member[GMST.MemberSort[i]].address.onlineFlag){
							GMST.MemberFlag[i]=1;
						}
					}
				}
				play_se( SE_NO_CLICK, 320, 240 );
				break;

			//家族称号（全员）
			case GroupMailGuildTitleMode:
				for(i=0;i<GMST.MemberCount;i++){
					if(GMST.MemberSort[i]!=-1){
						GMST.MemberFlag[i]=1;
					}
				}
				play_se( SE_NO_CLICK, 320, 240 );
				break;
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailMailMenuOff;
	}

	return ReturnFlag;

}

//群邮件スイッチ
BOOL MenuGroupMailSendSwitch (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;	

	if(GMST.MemberFlag[no-EnumMenuWindowGroupMailSendTo1+menuWindowGroupMailViewLine]==1){
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailMailCheckOn;
		if(flag & MENU_MOUSE_OVER){
			strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPSELECTON );
			ReturnFlag=TRUE;
		}
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_groupMailMailCheckOff;
		if(flag & MENU_MOUSE_OVER){
			strcpy( OneLineInfoStr, MWONELINE_MAIL_GROUPSELECTOFF );
			ReturnFlag=TRUE;
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		//送る人のフラグ立て
		if(GMST.MemberFlag[no-EnumMenuWindowGroupMailSendTo1+menuWindowGroupMailViewLine]){
			GMST.MemberFlag[no-EnumMenuWindowGroupMailSendTo1+menuWindowGroupMailViewLine]=0;
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}else{
			GMST.MemberFlag[no-EnumMenuWindowGroupMailSendTo1+menuWindowGroupMailViewLine]=1;
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}

	return ReturnFlag;
}

//データを初期化
void InitGropuMailStatusFlag(void){

	int i;

	GMST.MemberCount=0;
	GMST.MemberType=0;

	for(i=0;i<AMOUNT_OF_GROUP_MAIL_MAX;i++){
		GMST.MemberSort[i]=0;
		GMST.MemberFlag[i]=0;
	}
}

//モードからデータをセット
void SetGropuMailStatusFlag(int type){

	int i;

	GMST.MemberType=type;
	GMST.MemberCount=0;

	for(i=0;i<AMOUNT_OF_GROUP_MAIL_MAX;i++){
		GMST.MemberSort[i]=-1;
		GMST.MemberFlag[i]=0;
	}


	switch(type){
		//アドレスメンバー
		case GroupMailAddressMode:
			//ソートテーブル作成
			MenuWindowAddressBookSortFromVal(EnumMenuWindowAddressBookTypeAddressBook);
			
			for(i=0;i<AMOUNT_OF_ADDRESS_MEMBER;i++){
				if(addressBook[MenuGroupSortTable[i].index].useFlag==1){
					GMST.MemberSort[i]=MenuGroupSortTable[i].index;
					GMST.MemberCount++;
				}
			}
			break;

		//家族成员
		case GroupMailGuildMode:
			//ソートテーブル作成
			MenuWindowAddressBookSortFromVal(EnumMenuWindowAddressBookTypeGuildBook);

			for(i=0;i<AMOUNT_OF_GROUP_MAIL_MAX;i++){
				if(guildBook.member[MenuGroupSortTable[i].index].address.useFlag==1){
					GMST.MemberSort[i]=MenuGroupSortTable[i].index;
					GMST.MemberCount++;
				}
			}
			break;

		//家族称号
		case GroupMailGuildTitleMode:
			for(i=0;i<GUILD_TITLE_MAX;i++){
				if(guildBook.title[i].flag){
					GMST.MemberSort[i]=i;
					GMST.MemberCount++;
				}
			}
			break;
	}

}

//群邮件用ソートテーブル作成
void MenuWindowAddressBookSortFromVal (int mode)
{
	int i;
	int len;

	AddressBookSortModeVal=mode;	

	for (i = 0; i < GUILD_MEMBER_MAX; ++i)
		MenuGroupSortTable[i].index = i;
	
	if(AddressBookSortModeVal==EnumMenuWindowAddressBookTypeAddressBook){
		len = ADDRESS_BOOK;
	}else{
		len = GUILD_MEMBER_MAX;
	}

	qsort (MenuGroupSortTable, len, sizeof (ADDRESS_BOOK_SORT_TBL), 
		MenuWindowAddressBookCompareFromVal);
}

int MenuWindowAddressBookCompareFromVal (const void* _pt1, const void* _pt2)
{
	ADDRESS_BOOK_SORT_TBL* pt1 = (ADDRESS_BOOK_SORT_TBL*) _pt1;
	ADDRESS_BOOK_SORT_TBL* pt2 = (ADDRESS_BOOK_SORT_TBL*) _pt2;

	if (AddressBookSortModeVal == EnumMenuWindowAddressBookTypeAddressBook) {
		if (addressBook[pt2->index].useFlag == 0)
			return -1;
		if (addressBook[pt1->index].useFlag == 0)
			return 1;
	}
	else {
		if (guildBook.member[pt2->index].address.useFlag == 0)
			return -1;
		if (guildBook.member[pt1->index].address.useFlag == 0)
			return 1;
	}

	switch (addressBookSortMode) {
		case 0 :
			if(AddressBookSortModeVal == EnumMenuWindowAddressBookTypeAddressBook){
				return sortKeyAlphabet (&addressBook[pt1->index], &addressBook[pt2->index]);
			}else{
				return sortKeyAlphabet (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			}
		case 1 :
			if(AddressBookSortModeVal == EnumMenuWindowAddressBookTypeAddressBook){
				return sortKeyGuild (&addressBook[pt1->index], &addressBook[pt2->index]);
			}else{
				return sortKeyTitle (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			}
		case 2 :
			if(AddressBookSortModeVal == EnumMenuWindowAddressBookTypeAddressBook){
				return sortKeyLevel (&addressBook[pt1->index], &addressBook[pt2->index]);
			}else{
				return sortKeyLevel (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			}
		case 3 :
			if(AddressBookSortModeVal == EnumMenuWindowAddressBookTypeAddressBook){
				return sortKeyRegist (&addressBook[pt1->index], &addressBook[pt2->index]);
			}else{
				return sortKeyRegist (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			}
		case 4 :
			if(AddressBookSortModeVal == EnumMenuWindowAddressBookTypeAddressBook){
				return sortKeyServer (&addressBook[pt1->index], &addressBook[pt2->index]);
			}else{
				return sortKeyServer (&guildBook.member[pt1->index], &guildBook.member[pt2->index]);
			}

		}
	return 0;
}

BOOL MenuWindowGroupMailScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		int max;
	
		if(GMST.MemberCount-MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE<0){
			max=0;
		}else{
			max=GMST.MemberCount-MENU_WINDOW_GROUP_MAIL_LINE_PER_PAGE;
		}
		// スクロールバー縦ホイール移动
		menuWindowGroupMailViewLine = WheelToMove( &wI->sw[EnumMenuWindowGroupMailScrollBar],
			 menuWindowGroupMailViewLine, max, mouse.wheel );
	}

	return ReturnFlag;
}


