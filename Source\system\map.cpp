﻿#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include<direct.h>
#include<math.h>

#include"../systeminc/system.h"
#include"../systeminc/map.h"
#include"../systeminc/anim_tbl.h"
#include"../systeminc/pc.h"
#include"../systeminc/netproc.h"
#include"../systeminc/character.h"
#include"../systeminc/loadrealbin.h"
#include"../systeminc/loadsprbin.h"
#include"../systeminc/netmain.h"
#include"../systeminc/nrproto_cli.h"
#include"../systeminc/battleProc.h"
#include"../systeminc/t_music.h"
#include"../systeminc/field.h"
#include"../systeminc/login.h"
#include"../systeminc/menu.h"
#include"../systeminc/battleMenu.h"
#include"../systeminc/handletime.h"
#include"../systeminc/directDraw.h"
#include"../systeminc/main.h"
#include"../systeminc/gamemain.h"
#include"../systeminc/sprmgr.h"
#include"../systeminc/mouse.h"
#include"../systeminc/process.h"
#include"../systeminc/math2.h"
#include"../systeminc/action.h"
#include"../systeminc/sprdisp.h"
#include"../systeminc/chat.h"
#include"../systeminc/font.h"
#include"../systeminc/mapGridCursol.h"
#include"../systeminc/mapEffect.h"
#include"../systeminc/keyboard.h"

#include"../systeminc/debugLogWin.h"
#include"../systeminc/tool.h"

#ifdef PUK2
#include"../puk2/map/newmap.h"
#endif

#ifdef PUK3_PROF
#include "../puk3/profile/profile.h"
#endif

//-------------------------------------------------------------------------//
// 现在のマップの范围                                                      //
//-------------------------------------------------------------------------//
char mapName[MAP_NAME_LEN+1];				// 现在のマップ名
int mapServerNo;							// サーバー番号
int mapKind;								// 现在のマップの种类
											// 0 ... 通常マップ
											// 1 ... 自动生成マップ
											// 2 ... 家
int mapNo;									// 现在のマップ番号
#ifdef PUK3_CONNDOWNWALK
	int mapWarpId;								// マップワープ每に变更されるサーバーとクラアントのマップチェック用コード
#endif
int mapCfgId;								// サーバでのマップの管理コード
int mapSeqNo;								// 　　　　　　〃
int mapGxSize, mapGySize;					// 现在のマップのサイズ
short mapAreaX1, mapAreaY1;					// マップの范围左上
short mapAreaX2, mapAreaY2;					//       〃    右下
short mapAreaWidth, mapAreaHeight;			// マップの范围の幅
short mapInsideFlag;						// 现在の表示エリア内のマップ情报がきた

unsigned short mapTile[MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE];	// タイル情报
unsigned short mapParts[MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE];	// 布ツ情报
unsigned short mapEvent[MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE];	// イベント情报
unsigned short mapHit[MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE];	// あたり判定情报




#ifdef EXPAND_GRAPHICID

//-------------------------------------------
// タイル番号コンバートテーブル
//-------------------------------------------
int TileConvertTbl[65536];
int TileNoConvert( int no );
void MakeTileConvertTable( void );

#define TILENO_CONVERT( a ) ( TileNoConvert( (a) ) )

#else
#define TILENO_CONVERT( a ) ( a )
#endif

//-------------------------------------------------------------------------//
// マップ移动关连                                                          //
//-------------------------------------------------------------------------//
int mapGx, mapGy;							// 现在の画面中心になるマップグリッド座标
int mapHackCheck;							// マップ座标ハック对策チェック
int nextMapGx, nextMapGy;					// 移动先マップグリッド座标
int oldMapGx, oldMapGy;						// 前フレームのマップグリッド座标
int oldNextMapGx, oldNextMapGy;				// 前回の移动先マップグリッド座标
float mapX, mapY;							// 现在の画面中心になるマップ座标（グリッドｘ64）
float mapVx, mapVy;							// マップ座标の１フレーム当たりの移动量
float mapSpdRate;							// 上记の变化量

#define MOVE_CLICK_WAIT_TIME	250			// 移动先决定のクリックを受け付ける间隔（ミリ秒）
short moveAddTbl[8][2] =
{
	{  0, -1 }, // 0
	{  1, -1 }, // 1
	{  1,  0 }, // 2
	{  1,  1 }, // 3
	{  0,  1 }, // 4
	{ -1,  1 }, // 5
	{ -1,  0 }, // 6
	{ -1, -1 }  // 7
};
BOOL moveStackFlag = FALSE;
int moveStackGx, moveStackGy;
#define MOVE_MAX	100
short moveRoute[MOVE_MAX];
short moveRouteCnt = 0;
short moveRouteGx[MOVE_MAX];
short moveRouteGy[MOVE_MAX];
char moveRouteDir[MOVE_MAX];

#define MOVE_MAX2	2			// サーバに送る移动経路の最大值
short moveRouteCnt2 = 0;
short moveRoute2[MOVE_MAX2];

short moveLastDir = -1;


//-------------------------------------------------------------------------//
// マップファイル关连                                                      //
//-------------------------------------------------------------------------//
char mapFolder[128] = "map";				// マップファイルを作成するフォルダ                   //MLHIDE


//-------------------------------------------------------------------------//
// タイルの高速描画处理                                                    //
//   予备のサーフェスに前フレームのタイルを记忆しておき、                  //
//   そこからバックサーフェスにタイルを送る。                              //
//   足りない部分を补った后、バックサーフェスから                          //
//   予备サーフェスへ记忆しなおす。                                        //
//-------------------------------------------------------------------------//
char highSpeedDrawSw = 0;					// PutBmp()关数で高速描画机能を使うか？
											// 1 ... 使用 / 0 ... 不使用
int highSpeedDrawVarX, highSpeedDrawVarY;	// 前回书き换え时からの移动量
int highSpeedDrawNowX, highSpeedDrawNowY;	// 现在のマップの位置
int highSpeedDrawOldX, highSpeedDrawOldY;	// 前回のマップの位置
int highSpeedDrawVarX2, highSpeedDrawVarY2;	// １フレームあたりの移动量
int highSpeedDrawNowX2, highSpeedDrawNowY2;	// 现在フレームのマップの位置
int highSpeedDrawOldX2, highSpeedDrawOldY2;	// １フレーム前のマップの位置








// マップデータ无し时の移动遅延处理用
#define SEARCH_AREA		11		// 空マップのチェック范围（キャラからの距离）
BOOL mapEmptyFlag;
short mapEmptyDir;
int mapEmptyGx, mapEmptyGy;
short getMapAreaX1[2], getMapAreaY1[2], getMapAreaX2[2], getMapAreaY2[2];
short getMapAreaCnt;
unsigned int mapEmptyStartTime;


// フロアが变更されたか
BOOL floorChangeFlag = FALSE;



// ワープエフェクトを2重で实行しないためのフラグ
BOOL warpEffectFlag = FALSE;
BOOL warpEffectStart = FALSE;	// ワープエフェクト开始
BOOL warpMcFlag = FALSE;		// マップ要求中フラグ
BOOL warpMnFlag = FALSE;		// マップ名受信济みフラグ

// マップの注视点等
float viewPointX;
float viewPointY;







// エンカウント处理用
short nowEncountPercentage;	// エンカウント率
short nowEncountExtra;		// エンカウント率补正用
short minEncountPercentage;	// 最小エンカウント率
short maxEncountPercentage;	// 最大エンカウント率
short sendEnFlag;			// ENプロトコル送ったフラグ
short encountNowFlag;		// エンカウント中か

// イベント处理用
int eventId = 0;			// イベント管理ID
short eventWarpSendFlag;	// ワープイベント送信フラグ
short eventWarpSendId;		// ワープイベント送信时のID
short eventEnemySendFlag;	// 固定エンカウントイベント送信フラグ
short eventEnemySendId;		// 固定エンカウントイベント送信时のID
short eventEnemyFlag;		// 固定敌にエンカウントしたら１


short etcEventFlag = 0;


short vsLookFlag;			// 観战中フラグ


// オートマッピング用
#define AUTO_MAPPING_W			108		// オートマッピングで表示できる最大横幅
#define AUTO_MAPPING_H			108		// 　　　　　　　〃　　　　　　　　縦幅
BOOL autoMappingInitFlag = TRUE;		// マップを読み込みオートマップを作成するかのフラグ
unsigned char autoMappingBuf[AUTO_MAPPING_H*AUTO_MAPPING_W];	// オートマップ作成バッファ

#ifdef PUK2
//unsigned short NewAutoMappingBuf[AUTO_MAPPING_H*AUTO_MAPPING_W];	// 新マップ用オートマップ作成バッファ
unsigned long NewAutoMappingBuf[AUTO_MAPPING_H*AUTO_MAPPING_W];	// 新マップ用オートマップ作成バッファ
#endif

#define AUTO_MAPPING_SEE_W		30		// PCを中心にこの范围物体はオートマップで见たことにする
#define AUTO_MAPPING_SEE_H		30		// （ちなみにこの场合はPCを中心に±15の范围に入ったら
										//   オートマップで见たことにする）
unsigned int readMapAfterFrame = 10000;		// マップ読み込み后の経过フレーム数
unsigned short autoMapSeeFlagBuf[AUTO_MAPPING_SEE_H*AUTO_MAPPING_SEE_W];
BOOL autoMapSeeFlag = FALSE;
int autoMapSeeFloor;
int autoMapSeeGx, autoMapSeeGy;

short autoMapZoomFlag = 1;				// デフォルトはミドルサイズ
int autoMapSize[3][2] =
{
	{ AUTO_MAPPING_W-1, AUTO_MAPPING_H-1 },
	{ AUTO_MAPPING_W/2, AUTO_MAPPING_H/2 },
	{ AUTO_MAPPING_W/4, AUTO_MAPPING_H/4 }
};






//-------------------------------------------------------------------------//
// 关数プロト种类宣言                                                    //
//-------------------------------------------------------------------------//

// 移动处理关连
void onceMoveProc( void );
void partyMoveProc( void );
void getPartyTbl( void );

// マップ管理情报关连
void setEventMemory( int x, int y, unsigned short ev );


void readHitMap( int, int, int, int,
	unsigned short *, unsigned short *, unsigned short *, unsigned short * );
void getRouteMap( void );
void shiftRouteMap( void );
void shiftRouteMap2( void );
int getDirData( int, int, int, int );
void turnAround( void );
void turnAround2( int );


BOOL createAutoMap( int, int, int );





//-------------------------------------------------------------------------//

// マップキャッシュ处理
//#define MAP_CACHE_PROC

#ifdef MAP_CACHE_PROC

#define MAX_MAP_CACHE_SIZE	3		// キャッシュする最大フロア数
#define MAP_CACHE_X_SIZE	800		// １フロアのＸ幅最大数
#define MAP_CACHE_Y_SIZE	1200		// １フロアのＹ幅最大数
#define MAP_CACHE_X_BYTE	((MAP_CACHE_X_SIZE+7)/8)
#define MAP_CACHE_Y_BYTE	MAP_CACHE_Y_SIZE
int mapCacheFloorNo[MAX_MAP_CACHE_SIZE];
int mapCacheFloorGxSize[MAX_MAP_CACHE_SIZE];
int mapCacheFloorGySize[MAX_MAP_CACHE_SIZE];
unsigned char mapCacheFlag[MAX_MAP_CACHE_SIZE][MAP_CACHE_X_BYTE*MAP_CACHE_Y_BYTE];
int mapCacheUse;
unsigned int mapCacheLastTime[MAX_MAP_CACHE_SIZE];

void initMapCache( void );
BOOL checkMapCache( int, int, short *, int, int, int, int );
void clearMapCacheFlag( int );
BOOL checkMapCacheFlag( int, int, int, int, int, int, int );
BOOL checkMapCacheEvent( int, int, int, int );

#endif











///////////////////
// マップが无い时の要求处理テスト
#ifdef MAP_DATA_EMPTY_PROC

#define MAX_MAP_DATA_EMPTY_BUF	20

MAP_DATA_EMPTY_INFO mapDataEmptyBufTop;		// マップ要求した时の情报を登録するバッファの最初
MAP_DATA_EMPTY_INFO *mapDataEmptyBufLast;	// マップ要求した时の情报を登録するバッファの最后
MAP_DATA_EMPTY_INFO mapDataEmptyBuf;
MAP_DATA_EMPTY_INFO _mapDataEmptyBuf[MAX_MAP_DATA_EMPTY_BUF];


int cntMapDataEmptyBuf = 0;		// マップ要求している数


// マップが无い时の要求处理を初期化
void initMapDataEmpty( void )
{
	int i;

	mapDataEmptyBufTop.sendFlag = 0;
	mapDataEmptyBufTop.recvFlag = 0;
	mapDataEmptyBufTop.areaCnt  = 0;
	mapDataEmptyBufTop.pre      = NULL;
	mapDataEmptyBufTop.next     = NULL;

	mapDataEmptyBufLast = &mapDataEmptyBufTop;

	mapDataEmptyBuf.sendFlag = 0;
	mapDataEmptyBuf.recvFlag = 0;
	mapDataEmptyBuf.areaCnt  = 0;
	mapDataEmptyBuf.pre      = NULL;
	mapDataEmptyBuf.next     = NULL;

	for( i = 0; i < MAX_MAP_DATA_EMPTY_BUF; i++ )
	{
		_mapDataEmptyBuf[i].areaCnt = 0;
		_mapDataEmptyBuf[i].pre  = NULL;
		_mapDataEmptyBuf[i].next = NULL;
		addMapDataEmptyBuf( &mapDataEmptyBuf, &_mapDataEmptyBuf[i] );
	}

	cntMapDataEmptyBuf = 0;
}


// マップを要求するエリアを设定
int setMapDataEmptyArea( MAP_AREA_INFO *pt1, MAP_AREA_INFO *pt2 )
{
	MAP_DATA_EMPTY_INFO *ptBuf;

	// 设定するデータが无いなら終わる
	if( pt1 == NULL && pt2 == NULL )
		return 0;

	// 设定が１つならpt1に诘めて处理する
	if( pt1 == NULL && pt2 != NULL )
	{
		pt1 = pt2;
		pt2 = NULL;
	}

	// 设定するバッファ无い时も終わる
	if( mapDataEmptyBuf.next == NULL )
		return 0;

	ptBuf = mapDataEmptyBuf.next;				// バッファのポインタ取得
	delMapDataEmptyBuf( mapDataEmptyBuf.next );	// テンポラリバッファから削除

	// エリア设定
	ptBuf->kind      = pt1->kind;
	ptBuf->no        = pt1->no;
	ptBuf->areaCnt   = 1;
	ptBuf->areaX1[0] = pt1->areaX1;
	ptBuf->areaY1[0] = pt1->areaY1;
	ptBuf->areaX2[0] = pt1->areaX2;
	ptBuf->areaY2[0] = pt1->areaY2;

	if( pt2 != NULL )
	{
		// ２つ目のエリアがあれば设定
		ptBuf->areaCnt++;
		ptBuf->areaX1[1] = pt2->areaX1;
		ptBuf->areaY1[1] = pt2->areaY1;
		ptBuf->areaX2[1] = pt2->areaX2;
		ptBuf->areaY2[1] = pt2->areaY2;
	}

	// 要求バッファに设定
	addMapDataEmptyBuf( mapDataEmptyBufLast, ptBuf );
	// 最后に设定したバッファのポインタを学习
	mapDataEmptyBufLast = ptBuf;

	return 1;
}


// 要求したマップが来たらバッファから消す
int delMapDataEmptyArea( MAP_AREA_INFO *pt1 )
{
	MAP_DATA_EMPTY_INFO *ptBuf;
	char cmpFlag;
	char delFlag;

	// エリアが指定されて无いなら終わる
	if( pt1 == NULL )
		return 0;

	// 指定されたエリアを含むバッファを检索
	cmpFlag = 0;
	ptBuf = mapDataEmptyBufTop.next;
	while( ptBuf )
	{
		if( ptBuf->kind      == pt1->kind
		 && ptBuf->no        == pt1->no
		 && ptBuf->areaX1[0] == pt1->areaX1
		 && ptBuf->areaY1[0] == pt1->areaY1
		 && ptBuf->areaX2[0] == pt1->areaX2
		 && ptBuf->areaY2[0] == pt1->areaY2 )
		{
			cmpFlag = 1;
			ptBuf->recvFlag |= 0x01;
			break;
		}
		else
		if( ptBuf->areaCnt == 2
		 && ptBuf->kind      == pt1->kind
		 && ptBuf->no        == pt1->no
		 && ptBuf->areaX1[1] == pt1->areaX1
		 && ptBuf->areaY1[1] == pt1->areaY1
		 && ptBuf->areaX2[1] == pt1->areaX2
		 && ptBuf->areaY2[1] == pt1->areaY2 )
		{
			cmpFlag = 2;
			ptBuf->recvFlag |= 0x02;
			break;
		}
		ptBuf = ptBuf->next;
	}

	// バッファが见つかったら处理する
	if( cmpFlag )
	{
		// 斜め移动でエリアが２つある时は两方揃うまで待つ
		delFlag = 0;
		if( (ptBuf->areaCnt == 1 && ptBuf->recvFlag == 0x01)
		 || (ptBuf->areaCnt == 2 && ptBuf->recvFlag == 0x03) )
			delFlag = 1;

		if( delFlag )
		{
			// 要求したマップが全部来たので抹消
			if( mapDataEmptyBufLast == ptBuf )
			{
				// 最后のポインタを消すならひとつ前のポインタを最后のポインタに登録
				mapDataEmptyBufLast = ptBuf->pre;
			}
			// 要求バッファから削除
			delMapDataEmptyBuf( ptBuf );
			// パラメータ初期化
			ptBuf->sendFlag = 0;
			ptBuf->recvFlag = 0;
			ptBuf->areaCnt  = 0;
			ptBuf->pre      = NULL;
			ptBuf->next     = NULL;
			// テンポラリバッファに戾す
			addMapDataEmptyBuf( &mapDataEmptyBuf, ptBuf );
			return 1;
		}
	}

	return 0;
}


// マップデータ要求处理（每フレーム１度实行）
void mapDataEmptyProc( void )
{
	MAP_DATA_EMPTY_INFO *ptBuf;
	int i;

	i = 0;
	ptBuf = mapDataEmptyBufTop.next;
	while( ptBuf )
	{
		i++;
		ptBuf = ptBuf->next;
	}

	cntMapDataEmptyBuf = i;
}


// マップ要求待ちの数を返す
int getBufCntMapDataEmpty( void )
{
	return cntMapDataEmptyBuf;
}


// pt1の后ろにpt2を追加する
void addMapDataEmptyBuf( MAP_DATA_EMPTY_INFO *pt1, MAP_DATA_EMPTY_INFO *pt2 )
{
	if( pt1 == NULL || pt2 == NULL )
		return;

	pt2->pre = pt1;
	pt2->next = pt1->next;

	if( pt1->next != NULL )
	{
		(pt1->next)->pre = pt2;
	}
	pt1->next = pt2;
}


// pt1の场所にpt2を插入する
void insertMapDataEmptyBuf( MAP_DATA_EMPTY_INFO *pt1, MAP_DATA_EMPTY_INFO *pt2 )
{
	if( pt1 == NULL || pt2 == NULL )
		return;

	pt2->pre = pt1->pre;
	pt2->next = pt1;

	if( pt1->pre != NULL )
	{
		(pt1->pre)->next = pt2;
	}
	pt1->pre = pt2;
}


// pt1を削除
void delMapDataEmptyBuf( MAP_DATA_EMPTY_INFO *pt1 )
{
	if( pt1 == NULL )
		return;

	// 先头のバッファは削除できない
	if( pt1->pre == NULL )
		return;

	(pt1->pre)->next = pt1->next;
	if( pt1->next != NULL )
	{
		(pt1->next)->pre = pt1->pre;
	}
}

#endif
//-------------------------------------------------------------------------//








//-------------------------------------------------------------------------//
// マップ处理初期化（ログイン开始时に１度だけ实行）                        //
//-------------------------------------------------------------------------//
void initMap( void )
{
	// マップ管理データ初期化
	setMapAreaInfo( 0, 0, 0, 0, 0, 0, 0, 0 );
	// マップ位置初期化
	setWarpMap( 0, 0 );
	oldMapGx = 0, oldMapGy = 0;
	oldNextMapGx = 0, oldNextMapGy = 0;

	loginFlag = LOGIN_FLAG_WAIT_FOR_MAPDATA | LOGIN_FLAG_WAIT_FOR_PC_PARAM;

#ifdef _DEBUG
	if( offlineFlag )
	{
		// 离线模式の时は存在するマップ番号を设定する
		strcpy( mapName, "离线模式" );                                          //MLHIDE
//		setMapAreaInfo( 0, 777, 1000, 1000, 0, 0, 0, 0);
		setMapAreaInfo( 0, 2810, 100, 100, 0, 0, 0, 0 );
		setWarpMap( 129, 64 );
		loginFlag = LOGIN_FLAG_ALL_OFF;
	}
#endif

	updateMapAreaFromMapPos();

	moveRouteCnt = 0;
	moveRouteCnt2 = 0;
	moveStackFlag = FALSE;

	mapEmptyFlag = FALSE;

	mouseCursorMode = MOUSE_CURSOR_MODE_NORMAL;

	nowEncountPercentage = 0;
	nowEncountExtra = 0;
	sendEnFlag = 0;
	encountNowFlag = 0;

	eventWarpSendFlag = 0;
	eventEnemySendFlag = 0;
	eventEnemyFlag = 0;

	etcEventFlag = 0;

	mouseLeftPushTime = 0;
	beforeMouseLeftPushTime = 0;

	warpEffectFlag = FALSE;
	warpEffectStart = FALSE;
 	warpMcFlag = FALSE;	
	warpMnFlag = FALSE;		// マップ名受信济みフラグ

	autoMapSeeFlag = FALSE;

	eventWarpSendId = -1;
	eventEnemySendId = -1;

	moveLastDir = -1;

	// 高速描画处理初期化
	initHighSpeedDraw();

	vsLookFlag = 0;

	mapInsideFlag = 0;

#ifdef MAP_DATA_EMPTY_PROC
	initMapDataEmpty();
#endif

#ifdef MAP_CACHE_PROC
	initMapCache();
#endif

#ifdef EXPAND_GRAPHICID
	MakeTileConvertTable(  );
#endif
}


//-------------------------------------------------------------------------//
// マップ处理リセット                                                      //
//-------------------------------------------------------------------------//
void resetMap( void )
{
	// 移动中なら移动先にいることにする
#ifdef PUK2
	setWarpMap( nextMapGx, nextMapGy );
#else
	setWarpMap( (int)(mapX/GRID_SIZE), (int)(mapY/GRID_SIZE) );
#endif

	updateMapAreaFromMapPos();

	moveRouteCnt = 0;
	moveRouteCnt2 = 0;
	moveStackFlag = FALSE;

//	mouseCursorMode = MOUSE_CURSOR_MODE_NORMAL;		//01/07/14 oft

	mouseLeftPushTime = 0;
	beforeMouseLeftPushTime = 0;

	autoMapSeeFlag = FALSE;
}


//-------------------------------------------------------------------------//
// 现在のマップ位置から范围と幅を设定                                      //
//-------------------------------------------------------------------------//
void updateMapAreaFromMapPos( void )
{
	mapAreaX1 = mapGx+MAP_AREA_X1;
	mapAreaY1 = mapGy+MAP_AREA_Y1;
	mapAreaX2 = mapGx+MAP_AREA_X2;
	mapAreaY2 = mapGy+MAP_AREA_Y2;

	checkAreaLimit( &mapAreaX1, &mapAreaY1, &mapAreaX2, &mapAreaY2 );

	mapAreaWidth  = mapAreaX2 - mapAreaX1;
	mapAreaHeight = mapAreaY2 - mapAreaY1;
}


//-------------------------------------------------------------------------//
// 范围のリミットチェック                                                  //
//-------------------------------------------------------------------------//
void checkAreaLimit( short *x1, short *y1, short *x2, short *y2 )
{
	if( *x1 < 0 )
		*x1 = 0;
	if( *y1 < 0 )
		*y1 = 0;
	if( *x2 > mapGxSize )
		*x2 = mapGxSize;
	if( *y2 > mapGySize )
		*y2 = mapGySize;
}


//-------------------------------------------------------------------------//
// マップ范围情报の设定
//-------------------------------------------------------------------------//
void setMapAreaInfo( int kind, int no, int gxSize, int gySize, int cfgid, int seqno, int var, int cut )
{
	mapKind   = kind;
	mapNo     = no;
	mapCfgId  = cfgid;
	mapSeqNo  = seqno;
	mapGxSize = gxSize;
	mapGySize = gySize;

#ifdef PUK2
	//マップ状态セット
	MapStatusSet(kind,no,cut,var);
#endif

}


//-------------------------------------------------------------------------//
// マップ位置移动变更                                                      //
//-------------------------------------------------------------------------//
void setMoveMap( int gx, int gy )
{
	// 现在地に移动する（移动とは言わんが）なら何もしない。
	if( mapGx == gx && mapGy == gy )
		return;

	moveStackGx = gx;
	moveStackGy = gy;
	moveStackFlag = TRUE;
}


//-------------------------------------------------------------------------//
// マップ位置ワープ变更                                                    //
//-------------------------------------------------------------------------//
void setWarpMap( int gx, int gy )
{

	// マップ座标ハック对策チェック（变更前）
	CheckMapHackStart();

	mapGx = gx;
	mapGy = gy;
	
	// マップ座标ハック对策チェック（变更后）
	CheckMapHackEnd();
	
	mapX = (float)mapGx*GRID_SIZE;
	mapY = (float)mapGy*GRID_SIZE;
	nextMapGx = mapGx;
	nextMapGy = mapGy;
	mapVx = 0;
	mapVy = 0;
	mapSpdRate = 1;

	oldMapGx = -1;
	oldMapGy = -1;
	oldNextMapGx = -1;
	oldNextMapGy = -1;

	viewPointX = mapX;
	viewPointY = mapY;

//	menuClose( MENU_ALL );
}




//-------------------------------------------------------------------------//
// マップファイルの名称を作成                                              //
//-------------------------------------------------------------------------//
void makeMapFileName( char *buf, int kind, int serverNo, int no )
{
	if( kind == 0 )
	{
		// 固定マップの时のファイル名
		sprintf( buf, "%s\\%d\\%d.dat", mapFolder, kind, no );              //MLHIDE
	}
	else
	{
		// 自动生成マップの时のファイル名
		sprintf( buf, "%s\\%d\\%d\\%d.dat", mapFolder, kind, serverNo, no ); //MLHIDE
	}
}


//-------------------------------------------------------------------------//
// 新规マップファイルの作成。                                              //
// （ただし、すでにファイルがある场合サーバ管理コードかサイズが违うときは  //
//   作り直す。）                                                          //
//-------------------------------------------------------------------------//
//
//   戾り值： 1 ... 新规作成
//            0 ... 既存ファイルがあった
//           -1 ... エラー
//
int createMap( int serverNo, int kind, int no, int gxSize, int gySize, int cfgid, int seqno )
{
	FILE *fp;
	char filename[256];
	short l = 0;
	int ox, oy;
	int oCfgid, oSeqno;
	char head[4];
	char fileVersion;

	// マップ番号０は存在しないとし終わる
	if( no == 0 )
		return 0;

	// マップファイル名作成
	makeMapFileName( filename, kind, serverNo, no );

	// ファイルを开く
	if( (fp = fopen( filename, "rb" )) == NULL ){                        //MLHIDE
#if 0
#ifdef _DEBUG_MSG
		{
			char msg[256];
			sprintf( msg, "制作新的地图文件。[地图:%d/x:%d/y:%d/kind:%d/server:%d]",      //MLHIDE
				no, gxSize, gySize, kind, serverNo );
			StockChatBufferLine( msg, FONT_PAL_GRAY );
		}
#endif
#endif

		// ファイルが无いので作成する
		_mkdir( mapFolder );
		{
			// 中のフォルダも作る
			char folderName[256];

			sprintf( folderName, "%s\\%d", mapFolder, kind );                  //MLHIDE
			_mkdir( folderName );
			if( kind > 0 ){
				// 自动生成マップならサーバ番号のフォルダを作る
				sprintf( folderName, "%s\\%d\\%d", mapFolder, kind, serverNo );   //MLHIDE
				_mkdir( folderName );
			}
		}

		// 空マップファイル作成
		if( createClearMapFile( filename, cfgid, seqno, gxSize, gySize ) < 0 ){
			// 失败
			return -1;
		}

		return 1;
	}else{
		// ファイルがある时サーバ管理コードかサイズが违うならマップが变わってる
		// また、家族部屋の场合は每回消す
		fseek( fp, 0, SEEK_SET );
		head[0] = fgetc( fp );
		head[1] = fgetc( fp );
		head[2] = fgetc( fp );
		head[3] = '\0';
		fileVersion = fgetc( fp );
		fread( &oCfgid, sizeof( int ), 1, fp );
		fread( &oSeqno, sizeof( int ), 1, fp );
		fread( &ox, sizeof( int ), 1, fp );
		fread( &oy, sizeof( int ), 1, fp );
		fclose ( fp );

		if( cfgid != oCfgid || seqno != oSeqno || gxSize != ox || gySize != oy 
		 || strcmp( head, MAP_FILE_HEADER ) != 0 || fileVersion != MAP_FILE_VERSION || kind == CHAR_MAPID_GUILDHOUSE){
#if 0
#ifdef _DEBUG_MSG
			{
				char msg[256];
				sprintf( msg, "地图大小变化。[地图:%d/x:%d->%d/y:%d->%d/kind:%d/server:%d]", //MLHIDE
					no, ox, gxSize, oy, gySize, kind, serverNo );
				StockChatBufferLine( msg, FONT_PAL_GRAY );
			}
#endif
#endif

			// 空マップファイル作成
			if( createClearMapFile( filename, cfgid, seqno, gxSize, gySize ) < 0 ){
				// 失败
				return -1;
			}
		}
		return 0;
	}
}


//-------------------------------------------------------------------------//
// gxSize x gySize の空のファイルを作成する                                //
//-------------------------------------------------------------------------//
//
// 戾り值： 0 ... 成功
//         -1 ... 失败
//
int createClearMapFile( char *filename, int cfgid, int seqno, int gxSize, int gySize )
{
	FILE *fp;
	int i, j, k;
	short data = 0;
	char *head = MAP_FILE_HEADER;
	int fileVersion = MAP_FILE_VERSION;

	// ファイルを开く
	if( (fp = fopen( filename, "wb" )) == NULL )                         //MLHIDE
	{
		// ダメならあきらめる
		return -1;
	}

	// データを书きこむ
	fputc( head[0], fp );
	fputc( head[1], fp );
	fputc( head[2], fp );
	fputc( fileVersion, fp );
	fwrite( &cfgid,  sizeof( cfgid ),  1, fp );
	fwrite( &seqno,  sizeof( seqno ),  1, fp );
	fwrite( &gxSize, sizeof( gxSize ), 1, fp );
	fwrite( &gySize, sizeof( gySize ), 1, fp );

	for( i = 0; i < 3; i++ )
	{
		for( j = 0; j < gxSize; j++ )
		{
			for( k = 0; k < gySize; k++ )
			{
				fwrite( &data, sizeof(short), 1, fp );
			}
		}
	}
	fclose( fp );

	return 0;
}


//-------------------------------------------------------------------------//
// マップファイルへデータの书き込み                                        //
//   ??グリッド座标(x1,y1)-(x2,y2)の范围のマップデータを书き込む           //
//   ??tile, parts, event は线形メモリ构造になっている                     //
//-------------------------------------------------------------------------//
// 引　数:  mapKind        ... マップの种类(0:固定マップ/1:自动生成/2:家)
//          mapNo          ... マップ番号
//          x1, y1, x2, y2 ... マップの范围
//          tile           ... タイルデータへのポインタ
//          parts          ... 布ツデータへのポインタ
//          event          ... イベントデータへのポインタ
//
// 戾り值:  0 ... 成功
//         -1 ... 失败
//

//（杉ローカルにあるファイルに书き込むだけです。

int writeMap( int mapKind, int mapNo, int x1, int y1, int x2, int y2,
	unsigned short *tile , unsigned short *parts, unsigned short *event )
{
	FILE *fp;
	char filename[256];
	int fWidth, fHeight, fOffset;
	int mWidth;
	int width, height;
	int fx, fy;
	int mx, my;
	int len, len2;
	int i, j;
	int cfgid, seqno;
	char head[4];
	int fileVersion;

#if 0
#ifdef _DEBUG_MSG
	{
		char msg[256];
		sprintf( msg, "地图文件写入。[地图:%d/(%d,%d)-(%d,%d)]",                     //MLHIDE
			mapNo, x1, y1, x2, y2 );
		StockChatBufferLine( msg, FONT_PAL_GRAY );
	}
#endif
#endif


	// マップファイル名作成
	makeMapFileName( filename, mapKind, mapServerNo, mapNo );

	// ファイルオープン
	if( (fp = fopen( filename, "rb+" )) == NULL )                        //MLHIDE
	{
		return -1;
	}

	head[0] = fgetc( fp );
	head[1] = fgetc( fp );
	head[2] = fgetc( fp );
	head[3] = '\0';
	fileVersion = fgetc( fp );
	fread( &cfgid, sizeof( int ), 1, fp );
	fread( &seqno, sizeof( int ), 1, fp );
	fread( &fWidth,  sizeof( int), 1, fp );
	fread( &fHeight, sizeof( int ), 1, fp );

	mWidth = x2 - x1;
	width = mWidth;
	height = y2 - y1;

	mx = 0;
	fx = x1;
	if( x1 < 0 )
	{
		width += x1;
		fx = 0;
		mx -= x1;
	}
	if( x2 > fWidth )
	{
		width -= (x2 - fWidth);
	}
	my = 0;
	fy = y1;
	if( y1 < 0 )
	{
		height += y1;
		fy = 0;
		my -= y1;
	}
	if( y2 > fHeight )
	{
		height -= (y2 - fHeight);
	}


	fOffset = sizeof( int )*4 + sizeof( char )*4;
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fwrite( &tile[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fOffset += sizeof( short ) * (fWidth * fHeight);
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fwrite( &parts[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fOffset += sizeof( short ) * (fWidth * fHeight);
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		// 読み込み??见たフラグを立てる
		for( j = 0; j < width; j++ )
		{
			event[len2+j] |= (MAP_SEE_FLAG | MAP_READ_FLAG);
			if( ::mapNo == mapNo
			 && (mapAreaX1 <= x1+j && x1+j < mapAreaX2
			  && mapAreaY1 <= y1+i && y1+i < mapAreaY2) )
			{
				// 画面内のエベントが再送されてきたらメモリも书きかえる
				setEventMemory( x1+j, y1+i, event[len2+j] );
			}
		}
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fwrite( &event[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fclose (fp);

	return 0;
}


//-------------------------------------------------------------------------//
// 画面内のエベントが再送されてきたらメモリも书きかえる                    //
//-------------------------------------------------------------------------//
void setEventMemory( int x, int y, unsigned short ev )
{
	mapEvent[(y-mapAreaY1)*mapAreaWidth+(x-mapAreaX1)] = ev;
}


//-------------------------------------------------------------------------//
// マップファイルからデータの読み込み                                      //
//   ??グリッド座标(x1,y1)-(x2,y2)の范围のマップデータを読み込む           //
//   ??tile, parts, event は线形メモリ构造になっている                     //
//-------------------------------------------------------------------------//
// 引　数:  mapKind        ... マップの种类(0:固定マップ/1:自动生成/2:家)
//          mapNo          ... フロア番号
//          x1, y1, x2, y2 ... マップの范围
//          tile           ... タイルデータへのポインタ
//          parts          ... 布ツデータへのポインタ
//          event          ... イベントデータへのポインタ
//
// 戾り值:  0 ... 成功
//         -1 ... 失败
//

//（杉ローカルにあるファイルから読み込むだけです。

int readMap( int mapKind, int mapNo, int x1, int y1, int x2, int y2,
	unsigned short *tile, unsigned short *parts, unsigned short *event )
{
	FILE *fp;
	char filename[256];
	int fWidth, fHeight, fOffset;
	int mWidth;
	int width, height;
	int fx, fy;
	int mx, my;
	int len, len2;
	int i;
	int cfgid, seqno;
	char head[4];
	int fileVersion;

#if 0
#ifdef _DEBUG_MSG
	{
		char msg[256];
		sprintf( msg, "地图文件读取。[地图:%d/(%d,%d)-(%d,%d)]",                     //MLHIDE
			mapNo, x1, y1, x2, y2 );
		StockChatBufferLine( msg, FONT_PAL_GRAY );
	}
#endif
#endif


	// マップファイル名作成
	makeMapFileName( filename, mapKind, mapServerNo, mapNo );

	// ファイルオープン
	if( (fp = fopen( filename, "rb" ))==NULL )                           //MLHIDE
	{
		return -1;
	}

	memset( tile, 0, MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE * sizeof( short ) );
	memset( parts, 0, MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE * sizeof( short ) );
	memset( event, 0, MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE * sizeof( short ) );


	head[0] = fgetc( fp );
	head[1] = fgetc( fp );
	head[2] = fgetc( fp );
	head[3] = '\0';
	fileVersion = fgetc( fp );
	fread( &cfgid, sizeof( int ), 1, fp );
	fread( &seqno, sizeof( int ), 1, fp );
	fread( &fWidth,  sizeof( int ), 1, fp );
	fread( &fHeight, sizeof( int ), 1, fp );

	mWidth = x2 - x1;
	width = mWidth;
	height = y2 - y1;

	//要求された范围を设定しなおしている
	mx = 0;
	fx = x1;
	if( x1 < 0 )
	{
		width += x1;
		fx = 0;
		mx -= x1;
	}
	if( x2 > fWidth )
	{
		width -= (x2 - fWidth);
	}
	my = 0;
	fy = y1;
	if( y1 < 0 )
	{
		height += y1;
		fy = 0;
		my -= y1;
	}
	if( y2 > fHeight )
	{
		height -= (y2 - fHeight);
	}


	fOffset = sizeof( int )*4 + sizeof( char )*4;
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &tile[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fOffset += sizeof( short ) * (fWidth * fHeight);
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &parts[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fOffset += sizeof( short ) * (fWidth * fHeight);
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &event[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fclose (fp);

	return 0;
}



//-------------------------------------------------------------------------//
// 高速描画处理の初期化                                                    //
//-------------------------------------------------------------------------//
void initHighSpeedDraw( void )
{
	highSpeedDrawVarX = 0;		// 前回からの移动量
	highSpeedDrawVarY = 0;
	highSpeedDrawNowX = 0;		// 现在のマップの位置
	highSpeedDrawNowY = 0;
	highSpeedDrawOldX = 0;		// 前回のマップの位置
	highSpeedDrawOldY = 0;
	highSpeedDrawVarX2 = 0;		// １フレームあたりの移动量
	highSpeedDrawVarY2 = 0;
	highSpeedDrawNowX2 = 0;		// 现在フレームのマップの位置
	highSpeedDrawNowY2 = 0;
	highSpeedDrawOldX2 = 0;		// １フレーム前のマップの位置
	highSpeedDrawOldY2 = 0;
}


//-------------------------------------------------------------------------//
// 高速描画处理のリセット                                                  //
//-------------------------------------------------------------------------//
void resetHighSpeedDraw( void )
{
	float dx, dy;

	camMapToGamen( 0.0, 0.0, &dx, &dy );

	highSpeedDrawNowX = (int)(dx+.5);
	highSpeedDrawNowY = (int)(dy+.5);

	highSpeedDrawNowX2 = highSpeedDrawNowX;
	highSpeedDrawNowY2 = highSpeedDrawNowY;

	highSpeedDrawOldX = highSpeedDrawNowX;
	highSpeedDrawOldY = highSpeedDrawNowY;

	highSpeedDrawVarX = 0;
	highSpeedDrawVarY = 0;
}


//-------------------------------------------------------------------------//
// 前回の位置との变化量を求める                                            //
//-------------------------------------------------------------------------//
void updateHighSpeedDraw( void )
{
	float dx, dy;

	camMapToGamen( 0.0, 0.0, &dx, &dy );
	highSpeedDrawNowX = (int)(dx+.5);
	highSpeedDrawNowY = (int)(dy+.5);

	highSpeedDrawNowX2 = highSpeedDrawNowX;
	highSpeedDrawNowY2 = highSpeedDrawNowY;

	highSpeedDrawVarX = highSpeedDrawNowX - highSpeedDrawOldX;
	highSpeedDrawVarY = highSpeedDrawNowY - highSpeedDrawOldY;

	highSpeedDrawVarX2 = highSpeedDrawNowX2 - highSpeedDrawOldX2;
	highSpeedDrawVarY2 = highSpeedDrawNowY2 - highSpeedDrawOldY2;
}


//-------------------------------------------------------------------------//
// 高速描画处理の现在のマップ位置を记忆                                    //
//-------------------------------------------------------------------------//
void memoryHighSpeedDraw( void )
{
	highSpeedDrawOldX = highSpeedDrawNowX;
	highSpeedDrawOldY = highSpeedDrawNowY;
	highSpeedDrawOldX2 = highSpeedDrawNowX2;
	highSpeedDrawOldY2 = highSpeedDrawNowY2;
}





//
// マップ表示
//
void drawMap( void )
{

#ifdef PUK2
	drawMap_Sugi(TRUE);
#else
	int i, j;
	int ti, tj, iTileGraNo, iPartsGraNo,pos;
	int x, y, tx, ty;
	S2 xx, yy, ww, hh;
	U4 bmpNo;

	draw_map_bgm_flg = 0;

	readMapAfterFrame++;

	// 现在のグリッド位置が变わったらファイルから読み込む
	if( mapGx != oldMapGx || mapGy != oldMapGy
	 || mapInsideFlag )
	{
		if( readMap( mapKind, mapNo, mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0] ) >= 0 )
		{
			// hitMap[]にあたり判定データを设定する
			readHitMap( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0], &mapHit[0] );

			if( mapEmptyFlag )
			{
				if( !checkEmptyMap( mapEmptyDir ) )
				{
					// 空マップのフラグをはずす drawMap()
					mapEmptyFlag = FALSE;
#if 0
#ifdef _DEBUG_MSG
					{
					char msg[256];
					sprintf( msg, "收到数据，允许移动。" );                                    //MLHIDE
					StockChatBufferLine( msg, FONT_PAL_GRAY );
					}
#endif
#endif
					autoMappingInitFlag = TRUE;	// オートマップ読み込み
				}
				else
				{
#if 0
#ifdef _DEBUG_MSG
					{
					char msg[256];
					sprintf( msg, "等待数据中。" );                                        //MLHIDE
					StockChatBufferLine( msg, FONT_PAL_GRAY );
					}
#endif
#endif
				}
			}
			else
			{
				autoMappingInitFlag = TRUE;	// オートマップ読み込み
			}

			readMapAfterFrame = 0;	// マップデータを読み込んでからの経过フレームをクリア
			mapInsideFlag = 0;
		}
		else
		{
#if 0
#ifdef _DEBUG_MSG
			{
				char msg[256];
				sprintf( msg, "地图读取失败。" );                                        //MLHIDE
				StockChatBufferLine( msg, FONT_PAL_GRAY );
			}
#endif
#endif
			return;
		}
	}

#if 0
	if( ProcNo == PROC_GAME && SubProcNo == 3 )
	{
		if( readMapAfterFrame == 4 )	// マップを読みこんで４フレーム后（处理を分散するため）
		{
			// 行った所のフラグを立てる
			readAutoMapSeeFlag();
		}
		else
		if( readMapAfterFrame == 8 )
		{
			// 立てたフラグを书き込む
			writeAutoMapSeeFlag();
		}
	}
#endif

	resetHighSpeedDraw();

	tx = highSpeedDrawNowX2 + (mapAreaX1+mapAreaY2-1) * SURFACE_WIDTH/2;
	ty = highSpeedDrawNowY2 + (-mapAreaX1+mapAreaY2-1) * SURFACE_HEIGHT/2;

	// 处理の顺番が以下のように处理する
	//
	// [map]
	//               16
	//            15    14
	//         13    12    11
	//      10     9     8     7
	//          6     5     4
	//             3     2
	//                1

	ti = mapAreaHeight-1;
	tj = 0;

	

	while( ti >= 0 )
	{
		i = ti;
		j = tj;

		x = tx;
		y = ty;

		while( i >= 0 && j >= 0 )
		{
			pos = i*mapAreaWidth+j;
			// タイル番号（０～６５５３５）を画像番号にコンバート
			iTileGraNo = TILENO_CONVERT( (int)(mapTile[pos]) );

			// タイル表示
			if( iTileGraNo > CG_INVISIBLE )
			{
				// 画面内なら处理する
				if( x >= -SURFACE_WIDTH/2
				 && x < DEF_APPSIZEX+SURFACE_WIDTH/2
				 && y >= -SURFACE_HEIGHT/2
				 && y < DEF_APPSIZEY+SURFACE_HEIGHT/2 )
				{
					//海タイルなら
					if( iTileGraNo >= 5000 && iTileGraNo <= 5000 + 9 ){
						StockDispBuffer( x, y, DISP_PRIO_SEA, iTileGraNo, 0 );
					} else {
						StockDispBuffer( x, y, DISP_PRIO_TILE, iTileGraNo, 0 );
					}
				}
			}
			else
			{
				// 特殊な布ツ（音とか）

				// 环境音布ツなので鸣らす
				if( MAP_ENV_NO_START <= iTileGraNo && iTileGraNo <= MAP_ENV_NO_END )
				{
					play_environment( iTileGraNo, x, y );
				}
				else
				// ＢＧＭ布ツなので鸣らす
				if( MAP_BGM_NO_START <= iTileGraNo && iTileGraNo <= MAP_BGM_NO_END )
				{
					play_map_bgm( iTileGraNo );
					draw_map_bgm_flg = 1;
				}
			}


			// タイル番号（０～６５５３５）を画像番号にコンバート
			iPartsGraNo = TILENO_CONVERT( (int)(mapParts[pos]) );

			// 布ツ表示
			if( iPartsGraNo > CG_INVISIBLE )
			{
				// 通番を取る
				realGetNo( iPartsGraNo, &bmpNo );
				// 画面内なら处理する
				realGetPos( bmpNo, &xx, &yy );
				realGetWH( bmpNo, &ww, &hh );
				xx += x;
				yy += y;
				if( xx < DEF_APPSIZEX && xx+ww-1 >= 0
				 && yy < DEF_APPSIZEY && yy+hh-1 >= 0 )
				{
					// 表示优先度决定のために登録する
					setMapParts( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y );
				}
			}
			else
			{
				// 特殊な布ツ（音とか）

				realGetNo( iPartsGraNo, &bmpNo );

				// ソートで使う布ツ
				if( iPartsGraNo == 1 )
				{
					setMapChar( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y, 0 );
				}
				else
				// 环境音布ツなので鸣らす
				if( MAP_ENV_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_ENV_NO_END )
				{
					play_environment( mapParts[i*mapAreaWidth+j], x, y );
				}
				else
				// ＢＧＭ布ツなので鸣らす
				if( MAP_BGM_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_BGM_NO_END )
				{
					// 例外无音ＢＧＭ布ツ处理
			//		if( mapParts[i*mapAreaWidth+j] == 218 ){
						//ＢＧＭフェードアウト
						//fade_out_bgm();
			//			stop_bgm();
			//		}else{
						play_map_bgm( iPartsGraNo );
						draw_map_bgm_flg = 1;
			//		}
				}
			}

			i--;
			j--;
			x -= SURFACE_WIDTH;
		}

		if( tj < mapAreaWidth - 1 )
		{
			tj++;
			tx += SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
		else
		{
			ti--;
			tx -= SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
	}

#if 0
	// 自动生成ダンジョンはとりあえずこっちでＢＧＭを鸣らす
	if( mapKind == 1 )
	{
		play_map_bgm( 212 );
		draw_map_bgm_flg = 1;
	}
#endif

#ifdef PUK2
	playEnvironmentSE ();
#endif
	// キャラと布ツをマージする
	sortMapCharParts();
	// バッファの内容を描画（描画バッファに贮める）
	drawMapCharParts();

	oldMapGx = mapGx;
	oldMapGy = mapGy;

#endif	//PUK2

}


// タイルの高速描画用
void drawMap2( void )
{
	int i, j;
	int ti, tj;
	int x, y, tx, ty, iTileGraNo, iPartsGraNo, pos;
	S2 xx, yy, ww, hh;
	U4 bmpNo;
	short tileDrawFlag;


	draw_map_bgm_flg = 0;

	readMapAfterFrame++;

	// 现在のグリッド位置が变わったらファイルから読み込む
	if( mapGx != oldMapGx || mapGy != oldMapGy
	 || mapInsideFlag )
	{
		if( readMap( mapKind, mapNo, mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0] ) >= 0 )
		{
			// hitMap[]にあたり判定データを设定する
			readHitMap( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0], &mapHit[0] );

			if( mapEmptyFlag )
			{
				if( !checkEmptyMap( mapEmptyDir ) )
				{
					// 空マップフラグはずす drawMap2()
					mapEmptyFlag = FALSE;	
#if 0
#ifdef _DEBUG_MSG
					{
					char msg[256];
					sprintf( msg, "收到数据，允许移动。" );                                    //MLHIDE
					StockChatBufferLine( msg, FONT_PAL_GRAY );
					}
#endif
#endif
					autoMappingInitFlag = TRUE;	// オートマップ読み込み
				}
				else
				{
#if 0
#ifdef _DEBUG_MSG
					{
					char msg[256];
					sprintf( msg, "等待数据中。" );                                        //MLHIDE
					StockChatBufferLine( msg, FONT_PAL_GRAY );
					}
#endif
#endif
				}
			}
			else
			{
				autoMappingInitFlag = TRUE;	// オートマップ読み込み
			}

			readMapAfterFrame = 0;	// マップデータを読み込んでからの経过フレームをクリア
			mapInsideFlag = 0;
		}
		else
		{
#if 0
#ifdef _DEBUG_MSG
			{
				char msg[256];
				sprintf( msg, "地图读取失败。" );                                        //MLHIDE
				StockChatBufferLine( msg, FONT_PAL_GRAY );
			}
#endif
#endif
			return;
		}
	}

#if 0
	if( ProcNo == PROC_GAME && SubProcNo == 3 )
	{
		if( readMapAfterFrame == 4 )	// マップを読みこんで４フレーム后（处理を分散するため）
		{
			// 行った所のフラグを立てる
			readAutoMapSeeFlag();
			// 立てたフラグを书き込む
			writeAutoMapSeeFlag();
		}
		else
		if( readMapAfterFrame == 8 )
		{
			// 立てたフラグを书き込む
		//	writeAutoMapSeeFlag();
		}
	}
#endif

	// 高速描画处理の前回との变化量を求める
	updateHighSpeedDraw();

	tx = highSpeedDrawNowX2 + (mapAreaX1+mapAreaY2-1) * SURFACE_WIDTH/2;
	ty = highSpeedDrawNowY2 + (-mapAreaX1+mapAreaY2-1) * SURFACE_HEIGHT/2;

	// 处理の顺番が以下のように处理する
	//
	// [map]
	//               16
	//            15    14
	//         13    12    11
	//      10     9     8     7
	//          6     5     4
	//             3     2
	//                1


	ti = mapAreaHeight-1;
	tj = 0;

	

	while( ti >= 0 )
	{
		i = ti;
		j = tj;

		x = tx;
		y = ty;

		while( i >= 0 && j >= 0 )
		{
			pos = i*mapAreaWidth+j;
			iTileGraNo = TILENO_CONVERT( (int)(mapTile[pos]) );

			// タイル表示
			if( iTileGraNo > CG_INVISIBLE )
			{
				if( highSpeedDrawVarX2 != 0 || highSpeedDrawVarY2 != 0 )
				{
					// 画面内に入りきってないものを处理する
					if( -SURFACE_WIDTH/2 < x
					 && x < DEF_APPSIZEX+SURFACE_WIDTH/2
					 && -SURFACE_HEIGHT/2 < y
					 && y < DEF_APPSIZEY+SURFACE_HEIGHT/2 )
					{
						tileDrawFlag = 0;
						if( highSpeedDrawVarX2 > 0 )
						{
							if( (x-highSpeedDrawVarX2) <= SURFACE_WIDTH/2 )
								tileDrawFlag = 1;
						}
						else
						if( highSpeedDrawVarX2 < 0 )
						{
							if( DEF_APPSIZEX-SURFACE_WIDTH/2 <= (x-highSpeedDrawVarX2) )
								tileDrawFlag = 1;
						}
						if( highSpeedDrawVarY2 > 0 )
						{
							if( (y-highSpeedDrawVarY2) <= SURFACE_HEIGHT/2 )
								tileDrawFlag = 1;
						}
						else
						if( highSpeedDrawVarY2 < 0 )
						{
							if( DEF_APPSIZEY-SURFACE_HEIGHT/2 <= (y-highSpeedDrawVarY2) )
								tileDrawFlag = 1;
						}
						if( tileDrawFlag )
						{
							StockDispBuffer( x, y, DISP_PRIO_TILE, iTileGraNo, 0 );
						}
					}
				}
			}
			else
			{
				// 特殊な布ツ（音とか）

				// 环境音布ツなので鸣らす
				if( MAP_ENV_NO_START <= iTileGraNo && iTileGraNo <= MAP_ENV_NO_END )
				{
					play_environment( iTileGraNo, x, y );
				}
				else
				// ＢＧＭ布ツなので鸣らす
				if( MAP_BGM_NO_START <= iTileGraNo && iTileGraNo <= MAP_BGM_NO_END )
				{
					play_map_bgm( iTileGraNo );
					draw_map_bgm_flg = 1;
				}
			}


			iPartsGraNo = TILENO_CONVERT( (int)(mapParts[pos]) );

			// 布ツ表示
			if( iPartsGraNo > CG_INVISIBLE )
			{
				realGetNo( iPartsGraNo, &bmpNo );
				// 画面内なら处理する
				realGetPos( bmpNo, &xx, &yy );
				realGetWH( bmpNo, &ww, &hh );
				xx += x;
				yy += y;
				if( xx < DEF_APPSIZEX && xx+ww-1 >= 0
				 && yy < DEF_APPSIZEY && yy+hh-1 >= 0 )
				{
					// 表示优先度决定のために登録する
					setMapParts( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y );
				}
			}
			else
			{
				// 特殊な布ツ（音とか）

				realGetNo( iPartsGraNo, &bmpNo );

				// ソートで使う布ツ
				if( iPartsGraNo == 1 )
				{
					setMapChar( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y, 0 );
				}
				else
				// 环境音布ツなので鸣らす
				if( MAP_ENV_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_ENV_NO_END )
				{
					play_environment( iPartsGraNo, x, y );
				}
				else
				// ＢＧＭ布ツなので鸣らす
				if( MAP_BGM_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_BGM_NO_END )
				{
					// 例外无音ＢＧＭ布ツ处理
			//		if( mapParts[i*mapAreaWidth+j] == 218 ){
						//ＢＧＭフェードアウト
						//fade_out_bgm();
			//			stop_bgm();
			//		}else{
						play_map_bgm( iPartsGraNo );
						draw_map_bgm_flg = 1;
			//		}
				//	play_map_bgm( mapParts[i*mapAreaWidth+j] );
				//	draw_map_bgm_flg = 1;
				}
			}

			i--;
			j--;
			x -= SURFACE_WIDTH;
		}

		if( tj < mapAreaWidth - 1 )
		{
			tj++;
			tx += SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
		else
		{
			ti--;
			tx -= SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
	}

#if 0
	// 自动生成ダンジョンはとりあえずこっちでＢＧＭを鸣らす
	if( mapKind == 1 )
	{
		play_map_bgm( 212 );
		draw_map_bgm_flg = 1;
	}
#endif

#ifdef PUK2
	playEnvironmentSE ();
#endif

	// キャラと布ツをマージする
	sortMapCharParts();
	// バッファの内容を描画（描画バッファに贮める）
	drawMapCharParts();

	oldMapGx = mapGx;
	oldMapGy = mapGy;
}


// マップ画像作成用
void drawMap3( void )
{
	int i, j;
	int ti, tj;
	int x, y, tx, ty, iTileGraNo, iPartsGraNo, pos;;
	S2 xx, yy, ww, hh;
	U4 bmpNo;

	// ファイルから読み込む
	if( readMap( mapKind, mapNo, mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
			&mapTile[0], &mapParts[0], &mapEvent[0] ) >= 0 )
	{
		// hitMap[]にあたり判定データを设定する
		readHitMap( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
			&mapTile[0], &mapParts[0], &mapEvent[0], &mapHit[0] );
	}
	else
	{
		return;
	}

	resetHighSpeedDraw();

	tx = highSpeedDrawNowX2 + (mapAreaX1+mapAreaY2-1) * SURFACE_WIDTH/2;
	ty = highSpeedDrawNowY2 + (-mapAreaX1+mapAreaY2-1) * SURFACE_HEIGHT/2;

	// 处理の顺番が以下のように处理する
	//
	// [map]
	//               16
	//            15    14
	//         13    12    11
	//      10     9     8     7
	//          6     5     4
	//             3     2
	//                1

	ti = mapAreaHeight-1;
	tj = 0;

	

	while( ti >= 0 )
	{
		i = ti;
		j = tj;

		x = tx;
		y = ty;

		while( i >= 0 && j >= 0 )
		{
			pos = i*mapAreaWidth+j;
			iTileGraNo = TILENO_CONVERT( (int)mapTile[pos] );
			// タイル表示
			if( iTileGraNo > CG_INVISIBLE )
			{
				// 画面内なら处理する
				if( x >= -SURFACE_WIDTH/2
				 && x < DEF_APPSIZEX+SURFACE_WIDTH/2
				 && y >= -SURFACE_HEIGHT/2
				 && y < DEF_APPSIZEY+SURFACE_HEIGHT/2 )
				{
					StockDispBuffer( x, y, DISP_PRIO_TILE, iTileGraNo, 0 );
				}
			}

			iPartsGraNo = TILENO_CONVERT( (int)mapParts[pos] );
			// 布ツ表示
			if( iPartsGraNo > CG_INVISIBLE )
			{
				realGetNo( iPartsGraNo, &bmpNo );
				// 画面内なら处理する
				realGetPos( bmpNo, &xx, &yy );
				realGetWH( bmpNo, &ww, &hh );
				xx += x;
				yy += y;
				if( xx < DEF_APPSIZEX && xx+ww-1 >= 0
				 && yy < DEF_APPSIZEY && yy+hh-1 >= 0 )
				{
					// 表示优先度决定のために登録する
					setMapParts( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y );
				}
			}
			else
			{
				realGetNo( iPartsGraNo, &bmpNo );

				// ソートで使う布ツ
				if( iPartsGraNo == 1 )
				{
					setMapChar( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y, 0 );
				}
			}

			i--;
			j--;
			x -= SURFACE_WIDTH;
		}

		if( tj < mapAreaWidth - 1 )
		{
			tj++;
			tx += SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
		else
		{
			ti--;
			tx -= SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
	}

	// キャラと布ツをマージする
	sortMapCharParts();
	// バッファの内容を描画（描画バッファに贮める）
	drawMapCharParts();
}


//　タイルだけ表示
void drawTile( void )
{
	int i, j, iTileGraNo, pos;
	int x, y, tx, ty;

	// ファイルから読み込む
	if( readMap( mapKind, mapNo, mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
					&mapTile[0], &mapParts[0], &mapEvent[0] ) >= 0 )
	{
#ifdef PUK2
		// hitMap[]にあたり判定データを设定する
		readHitMap_PUK2( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
			&mapTile[0], &mapParts[0], &mapEvent[0], &mapHit[0] );
#else
		// hitMap[]にあたり判定データを设定する
		readHitMap( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
			&mapTile[0], &mapParts[0], &mapEvent[0], &mapHit[0] );
#endif
	}

	tx = highSpeedDrawNowX2 + (mapAreaX1+mapAreaY2-1) * SURFACE_WIDTH/2;
	ty = highSpeedDrawNowY2 + (-mapAreaX1+mapAreaY2-1) * SURFACE_HEIGHT/2;

	// 处理の顺番が以下のように处理する
	//
	// [map]
	//               16
	//            15    14
	//         13    12    11
	//      10     9     8     7
	//          6     5     4
	//             3     2
	//                1

	int ti, tj;

	ti = mapAreaHeight-1;
	tj = 0;

	

	while( ti >= 0 )
	{
		i = ti;
		j = tj;

		x = tx;
		y = ty;

		while( i >= 0 && j >= 0 )
		{

			pos = i*mapAreaWidth+j;
			iTileGraNo = TILENO_CONVERT( (int)(mapTile[pos]) );
			// タイル表示
			if( iTileGraNo > CG_INVISIBLE )
			{
				// 画面内なら处理する
				if( x >= -SURFACE_WIDTH/2
				 && x < DEF_APPSIZEX+SURFACE_WIDTH/2
				 && y >= -SURFACE_HEIGHT/2
				 && y < DEF_APPSIZEY+SURFACE_HEIGHT/2 )
				{
					StockDispBuffer( x, y, DISP_PRIO_TILE, iTileGraNo, 0 );
				}
			}

			i--;
			j--;
			x -= SURFACE_WIDTH;
		}

		if( tj < mapAreaWidth - 1 )
		{
			tj++;
			tx += SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
		else
		{
			ti--;
			tx -= SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
	}
}




// マップファイルを読み込みなおす
//   实际の表示は、drawMapで行われる
void redrawMap( void )
{
	oldMapGx = -1;
	oldMapGy = -1;
}


// マップファイルのチェックサムを调べる
//   戾り值：同じならTRUE / 违うならFALSE
static unsigned char BitTable[] =	/*	ビットの并び顺を逆にするテーブル	*/
{
		0x00 , 0x80 , 0x40 , 0xC0 , 0x20 , 0xA0 , 0x60 , 0xE0 , 
		0x10 , 0x90 , 0x50 , 0xD0 , 0x30 , 0xB0 , 0x70 , 0xF0 , 
		0x08 , 0x88 , 0x48 , 0xC8 , 0x28 , 0xA8 , 0x68 , 0xE8 , 
		0x18 , 0x98 , 0x58 , 0xD8 , 0x38 , 0xB8 , 0x78 , 0xF8 , 
		0x04 , 0x84 , 0x44 , 0xC4 , 0x24 , 0xA4 , 0x64 , 0xE4 , 
		0x14 , 0x94 , 0x54 , 0xD4 , 0x34 , 0xB4 , 0x74 , 0xF4 , 
		0x0C , 0x8C , 0x4C , 0xCC , 0x2C , 0xAC , 0x6C , 0xEC , 
		0x1C , 0x9C , 0x5C , 0xDC , 0x3C , 0xBC , 0x7C , 0xFC , 
		0x02 , 0x82 , 0x42 , 0xC2 , 0x22 , 0xA2 , 0x62 , 0xE2 , 
		0x12 , 0x92 , 0x52 , 0xD2 , 0x32 , 0xB2 , 0x72 , 0xF2 , 
		0x0A , 0x8A , 0x4A , 0xCA , 0x2A , 0xAA , 0x6A , 0xEA , 
		0x1A , 0x9A , 0x5A , 0xDA , 0x3A , 0xBA , 0x7A , 0xFA , 
		0x06 , 0x86 , 0x46 , 0xC6 , 0x26 , 0xA6 , 0x66 , 0xE6 , 
		0x16 , 0x96 , 0x56 , 0xD6 , 0x36 , 0xB6 , 0x76 , 0xF6 , 
		0x0E , 0x8E , 0x4E , 0xCE , 0x2E , 0xAE , 0x6E , 0xEE , 
		0x1E , 0x9E , 0x5E , 0xDE , 0x3E , 0xBE , 0x7E , 0xFE , 
		0x01 , 0x81 , 0x41 , 0xC1 , 0x21 , 0xA1 , 0x61 , 0xE1 , 
		0x11 , 0x91 , 0x51 , 0xD1 , 0x31 , 0xB1 , 0x71 , 0xF1 , 
		0x09 , 0x89 , 0x49 , 0xC9 , 0x29 , 0xA9 , 0x69 , 0xE9 , 
		0x19 , 0x99 , 0x59 , 0xD9 , 0x39 , 0xB9 , 0x79 , 0xF9 , 
		0x05 , 0x85 , 0x45 , 0xC5 , 0x25 , 0xA5 , 0x65 , 0xE5 , 
		0x15 , 0x95 , 0x55 , 0xD5 , 0x35 , 0xB5 , 0x75 , 0xF5 , 
		0x0D , 0x8D , 0x4D , 0xCD , 0x2D , 0xAD , 0x6D , 0xED , 
		0x1D , 0x9D , 0x5D , 0xDD , 0x3D , 0xBD , 0x7D , 0xFD , 
		0x03 , 0x83 , 0x43 , 0xC3 , 0x23 , 0xA3 , 0x63 , 0xE3 , 
		0x13 , 0x93 , 0x53 , 0xD3 , 0x33 , 0xB3 , 0x73 , 0xF3 , 
		0x0B , 0x8B , 0x4B , 0xCB , 0x2B , 0xAB , 0x6B , 0xEB , 
		0x1B , 0x9B , 0x5B , 0xDB , 0x3B , 0xBB , 0x7B , 0xFB , 
		0x07 , 0x87 , 0x47 , 0xC7 , 0x27 , 0xA7 , 0x67 , 0xE7 , 
		0x17 , 0x97 , 0x57 , 0xD7 , 0x37 , 0xB7 , 0x77 , 0xF7 , 
		0x0F , 0x8F , 0x4F , 0xCF , 0x2F , 0xAF , 0x6F , 0xEF , 
		0x1F , 0x9F , 0x5F , 0xDF , 0x3F , 0xBF , 0x7F , 0xFF
};
static unsigned short crctab16[] =	/*	crc　の计算テーブル		*/
{
		0x0000,  0x1021,  0x2042,  0x3063,  0x4084,  0x50a5,  0x60c6,  0x70e7,
		0x8108,  0x9129,  0xa14a,  0xb16b,  0xc18c,  0xd1ad,  0xe1ce,  0xf1ef,
		0x1231,  0x0210,  0x3273,  0x2252,  0x52b5,  0x4294,  0x72f7,  0x62d6,
		0x9339,  0x8318,  0xb37b,  0xa35a,  0xd3bd,  0xc39c,  0xf3ff,  0xe3de,
		0x2462,  0x3443,  0x0420,  0x1401,  0x64e6,  0x74c7,  0x44a4,  0x5485,
		0xa56a,  0xb54b,  0x8528,  0x9509,  0xe5ee,  0xf5cf,  0xc5ac,  0xd58d,
		0x3653,  0x2672,  0x1611,  0x0630,  0x76d7,  0x66f6,  0x5695,  0x46b4,
		0xb75b,  0xa77a,  0x9719,  0x8738,  0xf7df,  0xe7fe,  0xd79d,  0xc7bc,
		0x48c4,  0x58e5,  0x6886,  0x78a7,  0x0840,  0x1861,  0x2802,  0x3823,
		0xc9cc,  0xd9ed,  0xe98e,  0xf9af,  0x8948,  0x9969,  0xa90a,  0xb92b,
		0x5af5,  0x4ad4,  0x7ab7,  0x6a96,  0x1a71,  0x0a50,  0x3a33,  0x2a12,
		0xdbfd,  0xcbdc,  0xfbbf,  0xeb9e,  0x9b79,  0x8b58,  0xbb3b,  0xab1a,
		0x6ca6,  0x7c87,  0x4ce4,  0x5cc5,  0x2c22,  0x3c03,  0x0c60,  0x1c41,
		0xedae,  0xfd8f,  0xcdec,  0xddcd,  0xad2a,  0xbd0b,  0x8d68,  0x9d49,
		0x7e97,  0x6eb6,  0x5ed5,  0x4ef4,  0x3e13,  0x2e32,  0x1e51,  0x0e70,
		0xff9f,  0xefbe,  0xdfdd,  0xcffc,  0xbf1b,  0xaf3a,  0x9f59,  0x8f78,
		0x9188,  0x81a9,  0xb1ca,  0xa1eb,  0xd10c,  0xc12d,  0xf14e,  0xe16f,
		0x1080,  0x00a1,  0x30c2,  0x20e3,  0x5004,  0x4025,  0x7046,  0x6067,
		0x83b9,  0x9398,  0xa3fb,  0xb3da,  0xc33d,  0xd31c,  0xe37f,  0xf35e,
		0x02b1,  0x1290,  0x22f3,  0x32d2,  0x4235,  0x5214,  0x6277,  0x7256,
		0xb5ea,  0xa5cb,  0x95a8,  0x8589,  0xf56e,  0xe54f,  0xd52c,  0xc50d,
		0x34e2,  0x24c3,  0x14a0,  0x0481,  0x7466,  0x6447,  0x5424,  0x4405,
		0xa7db,  0xb7fa,  0x8799,  0x97b8,  0xe75f,  0xf77e,  0xc71d,  0xd73c,
		0x26d3,  0x36f2,  0x0691,  0x16b0,  0x6657,  0x7676,  0x4615,  0x5634,
		0xd94c,  0xc96d,  0xf90e,  0xe92f,  0x99c8,  0x89e9,  0xb98a,  0xa9ab,
		0x5844,  0x4865,  0x7806,  0x6827,  0x18c0,  0x08e1,  0x3882,  0x28a3,
		0xcb7d,  0xdb5c,  0xeb3f,  0xfb1e,  0x8bf9,  0x9bd8,  0xabbb,  0xbb9a,
		0x4a75,  0x5a54,  0x6a37,  0x7a16,  0x0af1,  0x1ad0,  0x2ab3,  0x3a92,
		0xfd2e,  0xed0f,  0xdd6c,  0xcd4d,  0xbdaa,  0xad8b,  0x9de8,  0x8dc9,
		0x7c26,  0x6c07,  0x5c64,  0x4c45,  0x3ca2,  0x2c83,  0x1ce0,  0x0cc1,
		0xef1f,  0xff3e,  0xcf5d,  0xdf7c,  0xaf9b,  0xbfba,  0x8fd9,  0x9ff8,
		0x6e17,  0x7e36,  0x4e55,  0x5e74,  0x2e93,  0x3eb2,  0x0ed1,  0x1ef0,
};
unsigned short CheckCRC( unsigned char *p , int size )
{
	unsigned short	crc = 0;
	int		i;
	
	for( i = 0 ; i < size ; i ++ ){
		crc = ( crctab16[ ( crc >> 8 ) & 0xFF ] 
					^ ( crc << 8 ) ^ BitTable[ p[ i ] ] );
	}
	return crc;
}


BOOL mapCheckSum( int mode, int floor, int x1, int y1, int x2, int y2,
	int tileSum, int partsSum, int eventSum )
{
	int tilesum = 0;
	int objsum = 0;
	int eventsum = 0;
	int databufferindex = 0;
	int width = x2 - x1;
	int height = y2 - y1;
	int i, j;
	unsigned short tile[MAP_AREA_X_SIZE*MAP_AREA_Y_SIZE];
	unsigned short parts[MAP_AREA_X_SIZE*MAP_AREA_Y_SIZE];
	unsigned short event[MAP_AREA_X_SIZE*MAP_AREA_Y_SIZE];

#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( PROCSTACK_mapCheckSum );
#endif

	// タイル??布ツデータを読み出す
#if 0
#ifdef _DEBUG_MSG
	{
	char msg[256];
	sprintf( msg, "CheckSum的问题" );                                       //MLHIDE
	StockChatBufferLine( msg, FONT_PAL_GRAY );
	}
#endif
#endif

	readMap( mapKind, floor, x1, y1, x2, y2, tile, parts, event );

	for( i = 0 ; i < height; i ++ )
	{
        for( j = 0; j < width; j ++ )
		{
#ifdef PUK2
#if 1
			//0x0100がヒットフラグです。
			if(SugiMapStat.MapVer ==1){
				//新マップ
				event[i*width+j] &= 0x01ff;
			}else{
				//旧マップ
				event[i*width+j] &= 0x0fff;
			}
#else
			event[i*width+j] &= 0x00ff;
#endif
#else
			event[i*width+j] &= 0x0fff;
#endif
        }
    }

	tilesum  = CheckCRC( (unsigned char *)tile,  27 * 27 * sizeof( short ) );
	objsum   = CheckCRC( (unsigned char *)parts, 27 * 27 * sizeof( short ) );
	eventsum = CheckCRC( (unsigned char *)event, 27 * 27 * sizeof( short ) );

	if( tileSum == tilesum && partsSum == objsum && eventSum == eventsum )
	{
#if 0
#ifdef _DEBUG_MSG
		{
			char msg[256];
			sprintf( msg, "地形CheckSum相同，从本地文件读取。" );                           //MLHIDE
			StockChatBufferLine( msg, FONT_PAL_GRAY );
		}
#endif
#endif

#ifdef PUK3_SEGMENTATION_FAULT
		ProcPop();
#endif
		return TRUE;
	}
	else
	{
#if 0
#ifdef _DEBUG_MSG
		{
			char msg[256];
			sprintf( msg, "地形CheckSum不同，从服务器读取。" );                            //MLHIDE
			StockChatBufferLine( msg, FONT_PAL_GRAY );
		}
#endif
#endif

#ifndef _DEBUG
		nrproto_M_send( sockfd, mode, floor, x1, y1, x2, y2 );

		#ifdef MAP_DATA_EMPTY_PROC
		{
			MAP_AREA_INFO info;

			info.kind   = mode;
			info.no     = floor;
			info.areaX1 = x1;
			info.areaY1 = y1;
			info.areaX2 = x2;
			info.areaY2 = y2;
			setMapDataEmptyArea( &info, NULL );
		}
		#endif

#else
		if( !offlineFlag )
		{
			nrproto_M_send( sockfd, mode, floor, x1, y1, x2, y2 );
			
			// イベントワープ中だったら	// ohta
			if( warpEffectStart == TRUE ){
				// マップ名受信济みだったら
				if( warpMnFlag == TRUE ){	
					// マップ要求中フラグを立てる
					warpMcFlag = TRUE;
				}
			}
			
			#ifdef MAP_DATA_EMPTY_PROC
			{
				MAP_AREA_INFO info;

				info.kind   = mode;
				info.no     = floor;
				info.areaX1 = x1;
				info.areaY1 = y1;
				info.areaX2 = x2;
				info.areaY2 = y2;
				setMapDataEmptyArea( &info, NULL );
			}
			#endif

		}
#endif


#ifdef PUK3_SEGMENTATION_FAULT
		ProcPop();
#endif
		return FALSE;
	}
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}



// タイル??布ツから当たり判定情报を取り出す。
void readHitMap( int x1, int y1, int x2, int y2,
	unsigned short *tile, unsigned short *parts, unsigned short *event, unsigned short *hitMap )
{
	int width, height;
	int i, j, k, l, iTileGraNo, iPartsGraNo, pos;
	S2 hit, hitX, hitY;
	U4 bmpNo;

	memset( hitMap, 0, MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE * sizeof( short ) );

	width = x2 - x1;
	height = y2 - y1;

	if( width < 1 || height < 1 )
		return;

	// タイル
	for( i = 0; i < height; i++ )
	{
		for( j = 0; j < width; j++ )
		{
			pos = i*width+j;
			// タイルの番号（１～６５５３５）
			// をコンバート
			iTileGraNo = TILENO_CONVERT( (int)(tile[pos]) );

			// タイルの当たり判定
			if( iTileGraNo > CG_INVISIBLE
			 || (60 <= iTileGraNo && iTileGraNo <= 79) )
			{
				// 通番を取得
				realGetNo( iTileGraNo, &bmpNo );
				// 当たり判定の有无
				realGetHitFlag( bmpNo, &hit );
				// 当たり判定あるならバッファに设定
				if( hit == 0 && hitMap[pos] != 2 )
				{
					hitMap[pos] = 1;
				}
				else
				// hitが2の时はあたり判定なくす
				if( hit == 2 )
				{
					hitMap[pos] = 2;
				}
			}
			else
			{
				// 0から11はデフォルトで见えないタイル
				switch( iTileGraNo )
				{
					case 0:	// 0.bmp(无いけど)タイルは当たり判定あるとする
						// 読み込みフラグがたっていない时は处理しない
						if( (event[pos] & MAP_SEE_FLAG) == 0 )
							break;
				//	case 1:
					case 2:
					case 5:
					case 6:
					case 9:
					case 10:
						// 当たり判定が２の时は１を设定しない
						if( hitMap[pos] != 2 )
						{
							hitMap[pos] = 1;
						}
						break;

					case 4:
						hitMap[pos] = 2;
						break;
				}
			}
		}
	}


	// 布ツ
	for( i = 0; i < height; i++ )
	{
		for( j = 0; j < width; j++ )
		{
			pos = i*width+j;

			// タイルの番号（１～６５５３５）
			// をコンバート
			iPartsGraNo = TILENO_CONVERT( (int)(parts[pos]) );

			// 布ツの当たり判定
			if( iPartsGraNo > CG_INVISIBLE )
			{
				realGetNo( iPartsGraNo, &bmpNo );
				// 当たり判定の有无
				realGetHitFlag( bmpNo, &hit );
				// 当たり判定あるならバッファに设定
				if( hit == 0 )
				{
					realGetHitPoints( bmpNo, &hitX, &hitY );
					for( k = 0; k < hitY; k++ )
					{
						for( l = 0; l < hitX; l++ )
						{
							if( (i - k) >= 0 && (j + l) < width
							 && hitMap[(i-k)*width+j+l] != 2 )
							{
								hitMap[(i-k)*width+j+l] = 1;
							}
						}
					}
				}
				else
				// 当たり判定が２の时はタイルが步けなくても
				// 强制的に步けるようにする
				if( hit == 2 )
				{
					realGetHitPoints( bmpNo, &hitX, &hitY );
					for( k = 0; k < hitY; k++ )
					{
						for( l = 0; l < hitX; l++ )
						{
							if( (i - k) >= 0 && (j + l) < width )
							{
								hitMap[(i-k)*width+j+l] = 2;
							}
						}
					}
				}
			}
			else
			if( 60 <= iPartsGraNo && iPartsGraNo <= 79 )
			{
				realGetNo( iPartsGraNo, &bmpNo );
				// 当たり判定の有无
				realGetHitFlag( bmpNo, &hit );
				// 当たり判定あるならバッファに设定
				if( hit == 0 && hitMap[pos] != 2 )
				{
					hitMap[pos] = 1;
				}
				else
				// hitが2の时はあたり判定なくす
				if( hit == 2 )
				{
					hitMap[pos] = 2;
				}
			}
			else
			{
				// 0から11はデフォルトで见えないタイル
				switch( iPartsGraNo )
				{
				//	case 1:
					case 2:
					case 5:
					case 6:
					case 9:
					case 10:
						// 当たり判定が２の时は１を设定しない
						if( hitMap[pos] != 2 )
						{
							hitMap[pos] = 1;
						}
						break;

					case 4:
						hitMap[pos] = 2;
						break;
				}
			}
			// もう一度チェック
			// タイルと布ツが两方とも０の场合で Hit が ０だったならば
			// ヒットを１にする。
			if( parts[i*width+j] == 0 && tile[i*width+j] == 0 && hitMap[i*width+j] == 0 ){
				hitMap[pos] = 1;
			}


			// 固定ＮＰＣイベントは障害物として扱う
#ifdef PUK2
			if( (event[pos] & 0x00ff) == EVENT_NPC )
#else
			if( (event[pos] & 0x0fff) == EVENT_NPC )
#endif
			{
				hitMap[pos] = 1;
			}
		}
	}

}


//
// あたり判定のチェック
//   戾り值：TRUE ... 进入禁止
//
BOOL checkHitMap( int gx, int gy )
{
	int x, y;

	x = gx - mapAreaX1;
	y = gy - mapAreaY1;

	// バッファ范围外も进入禁止
	if( x < 0 || mapAreaWidth <= x
	 || y < 0 || mapAreaHeight <= y )
	{
		return TRUE;
	}

	// 当たり判定ありなら进入禁止
	if( mapHit[y*mapAreaWidth+x] == 1 )
	{
		return TRUE;
	}

	return FALSE;
}

//
// 移动先にデータが无い时は移动をとめてマップが来るのを待つ。
//
BOOL checkEmptyMap( int dir )
{
	// キャラの位置から±11のところを调べてデータが无ければTRUEを返す
	int i;
	int gx, gy;
	int tx, ty;
	int iLeftXLimit, iRightXLimit;
	int iUpYLimit, iDownYLimit;
	int len;
	BOOL flag = FALSE;

	if( mapAreaWidth < MAP_AREA_X_SIZE
	||  mapAreaHeight < MAP_AREA_Y_SIZE )
	{
		return FALSE;
	}

	// 左を检索するライン
	iLeftXLimit = ( mapGx > SEARCH_AREA )?( mapGx - SEARCH_AREA ):( 0 );
	iUpYLimit = ( mapGy > SEARCH_AREA )?( mapGy - SEARCH_AREA ):( 0 );

	// 右と下を检索するライン
	iRightXLimit = ( mapGxSize - mapGx > SEARCH_AREA )?( mapGx + SEARCH_AREA ):( mapGxSize-1 );
	iDownYLimit = ( mapGySize - mapGy > SEARCH_AREA )?( mapGy + SEARCH_AREA ):( mapGySize-1 );

	getMapAreaCnt = 0;

	// 左侧チェック  上と左の时に通る
//	if( dir == 6 || dir == 7 || dir == 0 )
	// 左侧チェック。 左、左上、左下に动くとき。
	if( dir == 6 || dir == 7 || dir == 5 )
	{
		gx = mapGx - SEARCH_AREA;	// チェックする座标の左上。
		gy = mapGy - SEARCH_AREA;	// 现在の位置から左と上に１１マスずつ
		tx = -SEARCH_AREA - MAP_AREA_X1; // 19
		ty = -SEARCH_AREA - MAP_AREA_Y1; // 9
		len = SEARCH_AREA*2+1;	// 23
		for( i = 0; i < len; i++ )
		{
			if( (0 <= gx && gx < mapGxSize)	// 现在のマップのサイズの内侧だけチェック
			 && (0 <= gy && gy < mapGySize) )
			{
				// mapEvent の 上位ビットはマップを読んだかどうかのフラグだ。
				if( mapEvent[ty*mapAreaWidth+tx] == 0 )	// その座标にマップがあるか？
				{	// 无かった。
					// マップを要求する范围
					getMapAreaX1[getMapAreaCnt] = gx - 1;	// １つ左から
					getMapAreaX2[getMapAreaCnt] = gx + 1;	// １つ右まで
					getMapAreaY1[getMapAreaCnt] = gy - 1;	// １つ上から
					getMapAreaY2[getMapAreaCnt] = gy + SEARCH_AREA*2+1 - i ; // ２３こ下まで。
					// 范围チェックして范围内に纳める。
					checkAreaLimit( &getMapAreaX1[getMapAreaCnt], &getMapAreaY1[getMapAreaCnt],
						&getMapAreaX2[getMapAreaCnt], &getMapAreaY2[getMapAreaCnt] );
					// もらうエリアを１个追加。（２个まで）
					getMapAreaCnt++;
					// 一ヶ所でも无かったら、EMPTYフラグをたてる
					flag = TRUE;
					break;
				}
			}
			gy++;
			ty++;
		}
	}

	// 上チェック。上と右の时
//	if( dir == 0 || dir == 1 || dir == 2 )
	// 上チェック。上、左上、右上の时。
	if( dir == 0 || dir == 7 || dir == 1 )
	{
		gx = mapGx - SEARCH_AREA;
		gy = mapGy - SEARCH_AREA;
//		tx = -SEARCH_AREA - MAP_AREA_X1;  // -11-(-30)= 19
//		ty = -SEARCH_AREA - MAP_AREA_Y1;  // -11-(-20)= 9 
		tx = -SEARCH_AREA - MAP_AREA_X1;  // -11-(-30)= 19
		ty = -SEARCH_AREA - MAP_AREA_Y1;  // -11-(-20)= 9 
		len = SEARCH_AREA*2+1;  // 19,9 の座标から 22 マス横に调べる。
		for( i = 0; i < len; i++ )
		{
			if( (0 <= gx && gx < mapGxSize)
			 && (0 <= gy && gy < mapGySize) )
			{
				if( mapEvent[ty*mapAreaWidth+tx] == 0 )
				{
					getMapAreaX1[getMapAreaCnt] = gx - 1;
					getMapAreaY1[getMapAreaCnt] = gy - 1;
					getMapAreaX2[getMapAreaCnt] = gx + SEARCH_AREA*2+1 -i;
					getMapAreaY2[getMapAreaCnt] = gy + 1;
					checkAreaLimit( &getMapAreaX1[getMapAreaCnt], &getMapAreaY1[getMapAreaCnt],
						&getMapAreaX2[getMapAreaCnt], &getMapAreaY2[getMapAreaCnt] );
					getMapAreaCnt++;
					flag = TRUE;
					break;
				}
			}
			gx++;
			tx++;
		}
	}

	if( getMapAreaCnt >= 2 )return flag;


	// 右と下
//	if( dir == 2 || dir == 3 || dir == 4 )
	// 右チェック 右、右上、右下
	if( dir == 2 || dir == 1 || dir == 3 )
	{
		gx = mapGx + SEARCH_AREA;
		gy = mapGy - SEARCH_AREA;
		tx = SEARCH_AREA - MAP_AREA_X1;
		ty = -SEARCH_AREA - MAP_AREA_Y1;
		len = SEARCH_AREA*2+1;
		for( i = 0; i < len; i++ )
		{
			if( (0 <= gx && gx < mapGxSize)
			 && (0 <= gy && gy < mapGySize) )
			{
				if( mapEvent[ty*mapAreaWidth+tx] == 0 )
				{
					getMapAreaX1[getMapAreaCnt] = gx;
					getMapAreaY1[getMapAreaCnt] = gy - 1;
					getMapAreaX2[getMapAreaCnt] = gx + 2;
					getMapAreaY2[getMapAreaCnt] = gy + SEARCH_AREA*2+1 -i ;
					checkAreaLimit( &getMapAreaX1[getMapAreaCnt], &getMapAreaY1[getMapAreaCnt],
						&getMapAreaX2[getMapAreaCnt], &getMapAreaY2[getMapAreaCnt] );
					getMapAreaCnt++;
					flag = TRUE;
					break;
				}
			}
			gy++;
			ty++;
		}
	}

	if( getMapAreaCnt >= 2 )return flag;

	// 左と下
//	if( dir == 4 || dir == 5 || dir == 6 )
	// 下チェック 下、右下、左下
	if( dir == 4 || dir == 3 || dir == 5 )
	{
		gx = mapGx - SEARCH_AREA;
		gy = mapGy + SEARCH_AREA;
		tx = -SEARCH_AREA - MAP_AREA_X1;
		ty = SEARCH_AREA - MAP_AREA_Y1;
		len = SEARCH_AREA*2+1;
		for( i = 0; i < len; i++ )
		{
			if( (0 <= gx && gx < mapGxSize)
			 && (0 <= gy && gy < mapGySize) )
			{
				if( mapEvent[ty*mapAreaWidth+tx] == 0 )
				{
					getMapAreaX1[getMapAreaCnt] = gx - 1;
					getMapAreaY1[getMapAreaCnt] = gy;
					getMapAreaX2[getMapAreaCnt] = gx + SEARCH_AREA*2+1 -i;
					getMapAreaY2[getMapAreaCnt] = gy + 2;
					checkAreaLimit( &getMapAreaX1[getMapAreaCnt], &getMapAreaY1[getMapAreaCnt],
						&getMapAreaX2[getMapAreaCnt], &getMapAreaY2[getMapAreaCnt] );
					getMapAreaCnt++;
					flag = TRUE;
					break;
				}
			}
			gx++;
			tx++;
		}
	}


	return flag;
}








///////////////////////////////////////////////////////////////////////////
// 移动处理
void moveProc( void )
{
	static unsigned int befortime = -1;


#if 0
#ifdef _DEBUG
	{
		char msg[256];

		sprintf( msg, "ServerNo = %d / MapMode = %d / MapNo = %5d", mapServerNo, mapKind, mapNo ); //MLHIDE
		StockFontBuffer( 16, 48, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "CfgId = %5d / SeqNo = %5d", mapCfgId, mapSeqNo );    //MLHIDE
		StockFontBuffer( 16, 64, FONT_PRIO_FRONT, 0, msg, 0 );
	}
#endif
#endif

#if 0
#ifdef _DEBUG
#ifdef MAP_DATA_EMPTY_PROC
	{
		char msg[256];

		sprintf( msg, "MapBuf %3d", getBufCntMapDataEmpty() );              //MLHIDE
		StockFontBuffer( 240, 16, FONT_PRIO_FRONT, 0, msg, 0 );
		
	}
#endif
#endif
#endif


#if 1
#ifdef _DEBUG
	{
		static short tglSw = 0;

		if( VK[ VK_INSERT ] & KEY_ON_ONCE )
		{
			tglSw++;
	#ifdef PUK3_WHALE_SHIP
			tglSw %= 9;
	#else
			tglSw %= 8;
	#endif
		}

#ifdef HIT_VIEW
		if( tglSw == 2 ){
			hitViewFlag=1;
		}else{
			hitViewFlag=0;
		}
#endif
			
		if( tglSw == 1 )
		{
			char msg[256];
			sprintf( msg, "EN send        : %d", sendEnFlag );                 //MLHIDE
			StockFontBuffer( 240, 16, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "EV (Warp) send : %d", eventWarpSendFlag );          //MLHIDE
			StockFontBuffer( 240, 32, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "EV (Enemy) send: %d", eventEnemySendFlag );         //MLHIDE
			StockFontBuffer( 240, 48, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "Empty          : %d", mapEmptyFlag );               //MLHIDE
			StockFontBuffer( 240, 64, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "  mapGx      = %3d/ mapGy      = %3d", mapGx, mapGy ); //MLHIDE
			StockFontBuffer( 240, 80, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "  mapEmptyGx = %3d/ mapEmptyGy = %3d", mapEmptyGx, mapEmptyGy ); //MLHIDE
			StockFontBuffer( 240, 96, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "  mapEmptyDir = %3d", mapEmptyDir );                //MLHIDE
			StockFontBuffer( 240, 112, FONT_PRIO_FRONT, 0, msg, 0 );
#ifdef PUK2
			sprintf( msg, "  mapArea(%3d,%3d)-(%3d,%3d)", mapAreaX1,mapAreaY1,mapAreaX2,mapAreaY2 ); //MLHIDE
			StockFontBuffer( 240, 128, FONT_PRIO_FRONT, 0, msg, 0 );
#endif
		}
		else
		if( tglSw == 2 )
		{
			char msg[256];
			sprintf( msg, "nowEncountPercentage : %d", nowEncountPercentage ); //MLHIDE
			StockFontBuffer( 240, 16, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "nowEncountExtra      : %d", nowEncountExtra );      //MLHIDE
			StockFontBuffer( 240, 32, FONT_PRIO_FRONT, 0, msg, 0 );
		}
		else
		if( tglSw == 3
		 || tglSw == 4 )
		{
			// あたり判定表示（デバッグ）
			// イベント判定表示（デバッグ）
			char msg[256];
			int x, y, xx, yy;
			int color;

			xx = -MAP_AREA_X1;
			yy = -MAP_AREA_Y1;

			if( MAP_AREA_X_SIZE > mapAreaWidth )
			{
				if( mapAreaX1 == 0 )
				xx -= (MAP_AREA_X_SIZE - mapAreaWidth);
			}
			if( MAP_AREA_Y_SIZE > mapAreaHeight )
			{
				if( mapAreaY1 == 0 )
				yy -= (MAP_AREA_Y_SIZE - mapAreaHeight);
			}

			for( y = 0; y < mapAreaHeight && y < 26; y++ )
			{
				for( x = 0; x < mapAreaWidth; x++ )
				{
					if( tglSw == 3 )
					{
						sprintf( msg, "%d", mapHit[y*mapAreaWidth+x] );                 //MLHIDE
						if( x == xx && y == yy )
						{
							color = FONT_PAL_RED;
						}
						else
						{
							color = FONT_PAL_WHITE;
						}
					}
					else
					{
#ifdef PUK2
						sprintf( msg, "%d", (mapEvent[y*mapAreaWidth+x] & 0x00ff) );    //MLHIDE
#else
						sprintf( msg, "%d", (mapEvent[y*mapAreaWidth+x] & 0xfff) );     //MLHIDE
#endif
						if( x == xx && y == yy )
						{
							color = FONT_PAL_RED;
						}
						else
						{
							if( mapEvent[y*mapAreaWidth+x] != 0 )
							{
#ifdef PUK2
								if( mapEvent[y*mapAreaWidth+x] & 0x00ff ){
#else
								if( mapEvent[y*mapAreaWidth+x] & 0xfff ){
#endif
									color = FONT_PAL_BLUE;
								}else{
									color = FONT_PAL_YELLOW;
								}
							}
							else
							{
								color = FONT_PAL_WHITE;
							}
						}
					}
					StockFontBuffer( x*10, y*18, FONT_PRIO_FRONT, color, msg, 0 );
				}
			}
			sprintf( msg, "gx = %5d / gy = %5d / hit = %d",                    //MLHIDE
				mouseMapGx, mouseMapGy, checkHitMap( mouseMapGx, mouseMapGy ) );
			StockFontBuffer( 332, 40, FONT_PRIO_FRONT, 0, msg, 0 );
		}
		else
		if( tglSw == 5 )
		{
			char msg[256];
			int x, y;

			x = mouseMapGx - mapAreaX1;
			y = mouseMapGy - mapAreaY1;
#ifdef PUK2
			sprintf( msg, "Tile  Bmp = %d", mapTile[y*mapAreaWidth+x] );       //MLHIDE
			StockFontBuffer( 220, 34, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "Parts Bmp = %d", mapParts[y*mapAreaWidth+x] );      //MLHIDE
			StockFontBuffer( 220, 54, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "Gx = %d / Gy = %d", mouseMapGx, mouseMapGy );       //MLHIDE
			StockFontBuffer( 220, 74, FONT_PRIO_FRONT, 0, msg, 0 );

			sprintf( msg, "mapServerNo = %3d", mapServerNo );                  //MLHIDE
			StockFontBuffer( 220, 114, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "mapKind = %3d", mapKind );                          //MLHIDE
			StockFontBuffer( 220, 134, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "mapNo = %3d", mapNo );                              //MLHIDE
			StockFontBuffer( 220, 154, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "mapCfgId = %3d", mapCfgId );                        //MLHIDE
			StockFontBuffer( 220, 174, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "mapSeqNo = %3d", mapSeqNo );                        //MLHIDE
			StockFontBuffer( 220, 194, FONT_PRIO_FRONT, 0, msg, 0 );
	#ifdef PUK3_CONNDOWNWALK
			sprintf( msg, "mapWarpId = %3d", mapWarpId );                      //MLHIDE
			StockFontBuffer( 220, 214, FONT_PRIO_FRONT, 0, msg, 0 );
	#endif
#else
			sprintf( msg, "Tile  Bmp = %d", mapTile[y*mapAreaWidth+x] );       //MLHIDE
			StockFontBuffer( 240, 34, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "Parts Bmp = %d", mapParts[y*mapAreaWidth+x] );      //MLHIDE
			StockFontBuffer( 240, 54, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "Gx = %d / Gy = %d", mouseMapGx, mouseMapGy );       //MLHIDE
			StockFontBuffer( 240, 74, FONT_PRIO_FRONT, 0, msg, 0 );
			sprintf( msg, "mapNo = %d", mapNo );                               //MLHIDE
			StockFontBuffer( 240, 94, FONT_PRIO_FRONT, 0, msg, 0 );
#endif
		}
		else
		if( tglSw == 6 )
		{
			char msg[256];

			if( (VK[ VK_RIGHT ] & KEY_ON_ONCE) && mapEffectRainLevel < 5 )
			{
				mapEffectRainLevel++;
			}
			else
			if( (VK[ VK_LEFT ] & KEY_ON_ONCE) && mapEffectRainLevel > 0 )
			{
				mapEffectRainLevel--;
			}

			sprintf( msg, "mapEffectRainLevel = %d", mapEffectRainLevel );     //MLHIDE
			StockFontBuffer( 240, 34, FONT_PRIO_FRONT, 0, msg, 0 );
		}
		else
		if( tglSw == 7 )
		{
			char msg[256];

			if( (VK[ VK_RIGHT ] & KEY_ON_ONCE) && mapEffectSnowLevel < 5 )
			{
				mapEffectSnowLevel++;
			}
			else
			if( (VK[ VK_LEFT ] & KEY_ON_ONCE) && mapEffectSnowLevel > 0 )
			{
				mapEffectSnowLevel--;
			}

			sprintf( msg, "mapEffectSnowLevel = %d", mapEffectSnowLevel );     //MLHIDE
			StockFontBuffer( 240, 34, FONT_PRIO_FRONT, 0, msg, 0 );
		}
	#ifdef PUK3_WHALE_SHIP
		else
		if( tglSw == 8 )
		{
			char msg[256];

			if( (VK[ VK_RIGHT ] & KEY_ON_ONCE) && mapEffectCloudLevel < 5 )
			{
				mapEffectCloudLevel++;
			}
			else
			if( (VK[ VK_LEFT ] & KEY_ON_ONCE) && mapEffectCloudLevel > 0 )
			{
				mapEffectCloudLevel--;
			}

			sprintf( msg, "mapEffectCloudLevel = %d", mapEffectCloudLevel );   //MLHIDE
			StockFontBuffer( 240, 34, FONT_PRIO_FRONT, 0, msg, 0 );
		}
	#endif
	}
#endif
#endif


#if 0
	// 咒术エフェクト
	if( joy_trg[ 1 ] & JOY_F9 )
	{
		setPcUseMagic();
		//createCommmonEffectNoLoop( SPR_effect01, mapGx, mapGy, 0, 0, DISP_PRIO_CHAR );
	}
#endif


#if 0
	{
		static char msg[256];
		sprintf( msg, "mouse.state = 0x%02x / mouse.onceState = 0x%02x",    //MLHIDE
			mouse.state, mouse.onceState );
		StockFontBuffer( 240, 16, FONT_PRIO_FRONT, 0, msg, 0 );
	}
#endif


#if 0
	// マウス情报表示（デバッグ）
	{
		static char msg[256];

		sprintf( msg, "moveStackGx = %5d", moveStackGx );                   //MLHIDE
		StockFontBuffer( 300, 40, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "moveStackGy = %5d", moveStackGy );                   //MLHIDE
		StockFontBuffer( 300, 56, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "mouseMapX = %5d/ mouseMapGx = %5d", mouseMapX, mouseMapGx ); //MLHIDE
		StockFontBuffer( 300, 72, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "mouseMapY = %5d/ mouseMapGy = %5d", mouseMapY, mouseMapGy ); //MLHIDE
		StockFontBuffer( 300, 88, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "mouseX = %5d", mouse.nowPoint.x );                   //MLHIDE
		StockFontBuffer( 300, 104, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "mouseY = %5d", mouse.nowPoint.y );                   //MLHIDE
		StockFontBuffer( 300, 120, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "leftPushTime  = %d", mouse.leftPushTime );           //MLHIDE
		StockFontBuffer( 300, 136, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "RightPushTime = %d", mouse.rightPushTime );          //MLHIDE
		StockFontBuffer( 300, 152, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "nextMapGx = %d/ nextMapGy = %d", nextMapGx, nextMapGy ); //MLHIDE
		StockFontBuffer( 300, 168, FONT_PRIO_FRONT, 0, msg, 0 );
	}
#endif


#if 0
	{
		static char msg[256];
		sprintf( msg, "partyModeFlag = %d", partyModeFlag );                //MLHIDE
		StockFontBuffer( 200, 40, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "useflag = %d %d %d %d %d",                           //MLHIDE
			party[0].useFlag,
			party[1].useFlag,
			party[2].useFlag,
			party[3].useFlag,
			party[4].useFlag );
		StockFontBuffer( 200, 56, FONT_PRIO_FRONT, 0, msg, 0 );
		sprintf( msg, "ptAct = %p %p", party[0].ptAct, party[1].ptAct );    //MLHIDE
		StockFontBuffer( 200, 72, FONT_PRIO_FRONT, 0, msg, 0 );
		
	}
#endif


	// 各种イベントフラグのチェック
	if( sendEnFlag == 0
	 && eventWarpSendFlag == 0
	 && eventEnemySendFlag == 0 )
	{
		etcEventFlag = 0;
	}
#ifdef PUK3_VEHICLE
	// ＰＣキャラに见えないフラグが立っているなら
	// 移动や拾うなどの行为は禁止
	if ( pc.status2 & CHR_STATUS2_INVISIBLE ){
		return;
	}
#endif


#if 0
	// 邻接する揭示板等を右键したら
	// サーバに见るプロトコルを送る
	if( mouseLeftClick )
	{
		if( lookAtAround() )
		{
			mouseLeftClick = FALSE;
		}
	}
#endif

	// その场で向き变え＆アイテム拾い
	if( mouseRightClick )
	{
#ifdef PUK3_PROF
		int id;
#endif

		// その场向き变え
		turnAround();
		// アイテム拾う
		getItem();

#ifdef PUK3_PROF
		id=getUserNpcID( mouseMapGx, mouseMapGy, CHAROBJ_TYPE_USER_NPC );
		if(id!=-1){
			nrproto_PRD_send(sockfd,PROFILE_NPC_OBJ,id);
		}
#endif
	}



	// 移动先の选择
	// 单独かリーダーの时だけ入力を受け付ける
	// 后、イベントとか起こっていないこと
	if( (partyModeFlag == 0 || (pc.status & CHR_STATUS_LEADER) != 0)
	 && etcSendFlag == 0
	 && etcEventFlag == 0 )
	{
		// 左ボタンを数秒押すと移动モードに移行
		if( mouseLeftPushTime >= MOVE_MODE_CHANGE_TIME )
		{
			mouseCursorMode = MOUSE_CURSOR_MODE_MOVE;
		}

		// 移动モードの处理
		if( mouseCursorMode == MOUSE_CURSOR_MODE_MOVE )
		{
			// クリックされたら通常モードに返回
#if defined(PUK2_NEW_MENU)
			if( mouse.onceState & MOUSE_LEFT_CRICK )
#else
			if( mouseLeftClick )
#endif
			{
				mouseCursorMode = MOUSE_CURSOR_MODE_NORMAL;
			}
			else
			// カーソルの指す位置を移动先とする
			{
				// マウスの连射を抑制
				if( befortime+MOVE_CLICK_WAIT_TIME <= GetTickCount() )
				{
					befortime = GetTickCount();
					moveStackGx = mouseMapGx;
					moveStackGy = mouseMapGy;
					moveStackFlag = TRUE;
				}
#ifdef PUK3_MOUSECURSOR
				if ( mouseMapGx-mapGx==0 && mouseMapGy-mapGy==0 );
				else{
					int type;
	
					type = getDirFromXY( (float)(mouseMapGx-mapGx),
						 (float)(mouseMapGy-mapGy) );
					setMouseType( MOUSE_CURSOR_TYPE_DIR_0 + type );
				}
#endif
			}
		}
		else
		// 通常モードの处理
		if( mouseLeftClick )
		{
			// カーソルの指す位置を移动先とする
			// マウスの连射を抑制
			if( befortime+MOVE_CLICK_WAIT_TIME <= GetTickCount() )
			{
				befortime = GetTickCount();
				moveStackGx = mouseMapGx;
				moveStackGy = mouseMapGy;
				moveStackFlag = TRUE;
			}
		}
	}



	// 布ティー番号诘めて取り出す
	getPartyTbl();

	// 一人かリーダーの时の移动处理
	if( partyModeFlag == 0
	 || (pc.status & CHR_STATUS_LEADER) != 0 )
	{
		onceMoveProc();
	}
	else
	// 布ティの子供の时の移动处理
	{
		partyMoveProc();
	}
	return;
}



short _encountFlag = 0;
short _warpEventFlag = 0;
short _enemyEventFlag = 0;

int  _enemyEventDir;


short justGoalFlag = 0;	// 目的地に着いた后すぐかどうか

short _partyTbl[MAX_PARTY];	// 布ティー情报を诘めるテーブル

void _etcEventCheck( void );
BOOL _execEtcEvent( void );
void _getMoveRoute2( void );

void setPcMovePointToChar( int, int );
void setPartyMovePoint( void );
void _mapMove( void );
void _partyMapMove( void );
#ifdef PUK3_RIDE
void _setMapMovePoint( int _nextGx, int _nextGy, int walkSpeed );
#else
void _setMapMovePoint( int, int );
#endif


BOOL _checkEncount( void );
void _sendEncount( void );
void _sendMoveRoute( void );
BOOL _checkWarpEvent( int, int );
void _sendWarpEvent( void );
BOOL _checkEnemyEvent( int, int );
void _sendEnemyEvent( void );
void _checkEmptyMap( void );


// プレーヤー一人で移动
void onceMoveProc( void )
{
	int dir;

	// ＰＣが基准に步くときは加速する必要无い
	mapSpdRate = 1.0F;

	// マップ空なので移动处理をしない
	if( mapEmptyFlag )
		return;


	if( sendEnFlag == 0				// エンカウント
	 && eventWarpSendFlag == 0		// ワープイベント
	 && eventEnemySendFlag == 0		// 敌イベント
	)
	{
		// イベント等の实行
		if( _execEtcEvent() )
			return;

		// グリッドの中心にいる时の处理
		if( (float)nextMapGx*GRID_SIZE == mapX && (float)nextMapGy*GRID_SIZE == mapY )
		{
#ifdef PUK3_RAG_PARTY_BREAK_UP_1
			// 布ティーのときの移动が残っている时
			if ( pc.ptAct && pc.ptAct->bufCount > 0 ){
				// 移动先をが设定されようとしてる
				if ( moveStackFlag ){
					pc.ptAct->bufCount = 0;
				}else{
	#ifdef PUK3_RIDE
					setMapMovePoint( pc.ptAct->bufGx[0], pc.ptAct->bufGy[0], pc.walkSpeed );
	#else
					setMapMovePoint( pc.ptAct->bufGx[0], pc.ptAct->bufGy[0] );
	#endif
					shiftBufCount( pc.ptAct );

					// ＰＣがリーダーの时で移动した瞬间に
					// 次の布ティキャラに现在の座标を设定してやる
					setPcMovePointToChar( mapGx, mapGy );


					// ＰＣがリーダーの时、他の布ティーの移动先を设定する
					setPartyMovePoint();

					// イベントとかのチェックが終わったら初期化しておく
					justGoalFlag = 0;

					// マップ空なので移动处理をしない
					if( mapEmptyFlag ) return;

					// ＰＣ移动处理（移动量がある时だけ）
					_mapMove();

					// 布ティーの移动处理
					_partyMapMove();

					updateMapAreaFromMapPos();

					viewPointX = mapX;
					viewPointY = mapY;
					return;
				}
			}
#endif
			// 移动先が设定されていたら移动経路を算出
			if( moveStackFlag
			 && moveRouteCnt2 == 0 )
			{
				moveStackFlag = FALSE;
				getRouteMap();
#if 0
				if( moveRouteCnt == 0 )
				{
					// 移动経路探したが无かった时向きだけ变えておく
					turnAround();
				}
#endif
			}

			// 移动后に目的地じゃないときその方向を向ける
			if( moveRouteCnt == 0 && moveRouteCnt2 == 0 )
			{
				turnAround2( moveLastDir );
				moveLastDir = -1;
			}

			// サーバへ送るデータを取り出す
			if( moveRouteCnt > 0 && moveRouteCnt2 == 0 )
			{
				// 移动経路からサーバに送る最大数分取り出す
				_getMoveRoute2();

				if( moveRouteCnt2 > 0 )
				{
					// 取り出した経路上でイベント等が発生するかチェック
					_etcEventCheck();
				}

				// サーバに移动経路送る
				_sendMoveRoute();
			}


			// 移动経路があるので移动量を算出
			if( moveRouteCnt2 > 0 )
			{
				// マップが空ならフラグ立ててまつ
#ifndef _DEBUG
				_checkEmptyMap();
#else
				if( !offlineFlag )
				{
					_checkEmptyMap();
				}
#endif

				// 移动先を设定し移动量を计算する
				dir = moveRoute2[0];
				shiftRouteMap2();
#ifdef PUK3_RIDE
				setMapMovePoint( mapGx+moveAddTbl[dir][0], mapGy+moveAddTbl[dir][1],
					 pc.walkSpeed );
#else
				setMapMovePoint( mapGx+moveAddTbl[dir][0], mapGy+moveAddTbl[dir][1] );
#endif

				// ＰＣがリーダーの时で移动した瞬间に
				// 次の布ティキャラに现在の座标を设定してやる
				setPcMovePointToChar( mapGx, mapGy );
			}
		}
	}

	// ＰＣがリーダーの时、他の布ティーの移动先を设定する
	setPartyMovePoint();


	// イベントとかのチェックが終わったら初期化しておく
	justGoalFlag = 0;

	// マップ空なので移动处理をしない
	if( mapEmptyFlag )
		return;

	// ＰＣ移动处理（移动量がある时だけ）
	_mapMove();

	// 布ティーの移动处理
	_partyMapMove();

	updateMapAreaFromMapPos();

	viewPointX = mapX;
	viewPointY = mapY;
}


// ＮＰＣがリーダーの时の处理
void partyMoveProc( void )
{
	int i;
	ACTION *ptAct, *ptActNext;

	// キャラがグリッドの中心に来てまだ移动先があるなら设定。
	// そして、后ろに布ティがいたら今の座标を设定してやる。
	for( i = 0; i < MAX_PARTY; i++ )
	{
		if( _partyTbl[i] >= 0 )
		{
			ptAct = party[_partyTbl[i]].ptAct;

			// ＮＰＣの时
			if( party[_partyTbl[i]].id != pc.id )
			{
				// グリッドの中心に来たら次の移动先を设定する
				if( (float)ptAct->nextGx*GRID_SIZE == ptAct->mx
				 && (float)ptAct->nextGy*GRID_SIZE == ptAct->my )
				{
					// バッファに次の座标があるので设定
					if( ptAct->bufCount > 0 )
					{
						// バッファがたまってたら加速する
						if( i == 0 )
						{
							mapSpdRate = 1.0F;
							if( ptAct->bufCount > 5 )
							{
								mapSpdRate = 2.0F;
							}
							else
							if( ptAct->bufCount >= 4 )
							{
								mapSpdRate = 1.6F;
							}
							else
							if( ptAct->bufCount >= 2 )
							{
								mapSpdRate = 1.2F;
							}
						}
#ifdef PUK3_RIDE
						_setCharMovePoint( ptAct, ptAct->bufGx[0], ptAct->bufGy[0],
							 party[_partyTbl[0]].ptAct->walkSpeed );
#else
						_setCharMovePoint( ptAct, ptAct->bufGx[0], ptAct->bufGy[0] );
#endif
						shiftBufCount( ptAct );
						if( _partyTbl[i+1] >= 0
						 && (i+1) < MAX_PARTY )
						{
							ptActNext = party[_partyTbl[i+1]].ptAct;
							stockCharMovePoint( ptActNext, ptAct->gx, ptAct->gy );
						}
					}
				}
			}
			// ＰＣの时
			else
			{
				// グリッドの中心に来たら次の移动先を设定する
				if( (float)nextMapGx*GRID_SIZE == mapX
				 && (float)nextMapGy*GRID_SIZE == mapY )
				{
					// バッファに次の座标があるので设定
					if( ptAct->bufCount > 0 )
					{
#ifdef PUK3_RIDE
						_setMapMovePoint( ptAct->bufGx[0], ptAct->bufGy[0],
							 party[_partyTbl[0]].ptAct->walkSpeed );
#else
						_setMapMovePoint( ptAct->bufGx[0], ptAct->bufGy[0] );
#endif
						shiftBufCount( ptAct );
						if( _partyTbl[i+1] >= 0
						 && (i+1) < MAX_PARTY )
						{
							ptActNext = party[_partyTbl[i+1]].ptAct;
							stockCharMovePoint( ptActNext, ptAct->gx, ptAct->gy );
						}
					}
				}
			}
		}
		else
		{
			break;
		}
	}


	// 移动处理
	for( i = 0; i < MAX_PARTY; i++ )
	{
		if( _partyTbl[i] >= 0 )
		{
			if( party[_partyTbl[i]].id != pc.id )
			{
				_charMove( party[_partyTbl[i]].ptAct );
			}
			else
			{
				//mapMove2();
				_mapMove();
			}
		}
		else
		{
			break;
		}
	}

	updateMapAreaFromMapPos();

	viewPointX = mapX;
	viewPointY = mapY;
}


///////////////////////////////////////////////////////////////////////////
// マップに移动先を设定
#ifdef PUK3_RIDE
void _setMapMovePoint( int _nextGx, int _nextGy, int walkSpeed )
#else
void _setMapMovePoint( int _nextGx, int _nextGy )
#endif
{
	float dx, dy;
	float len;
	int dir;

	nextMapGx = _nextGx;
	nextMapGy = _nextGy;

	// 移动量を求める
	dx = nextMapGx*GRID_SIZE-mapX;
	dy = nextMapGy*GRID_SIZE-mapY;
	len = (float)sqrt( (double)(dx*dx+dy*dy) );
	if( len > 0 )
	{
		dx /= len;
		dy /= len;
	}
	else
	{
		dx = 0;
		dy = 0;
	}
#ifdef PUK3_RIDE		// ライド移动のテスト
	if ( pc.ptAct != NULL ){
		mapVx = ( dx * MOVE_SPEED * walkSpeed ) / 100;
		mapVy = ( dy * MOVE_SPEED * walkSpeed ) / 100;
	}else{
		mapVx = dx * MOVE_SPEED;
		mapVy = dy * MOVE_SPEED;
	}
#else
	mapVx = dx * MOVE_SPEED;
	mapVy = dy * MOVE_SPEED;
#endif

	// PCキャラの方向设定
	if( dx != 0 || dy != 0 )
	{
		dir = getDirFromXY( dx, dy );
		setPcDir( dir );
		setPcWalkFlag();
	}

	// PCキャラをマップの位置と同期する
	setPcPoint();
}


///////////////////////////////////////////////////////////////////////////
// ＰＣがリーダーの时、移动した瞬间に
// 次の布ティキャラに现在の座标を设定してやる
void setPcMovePointToChar( int gx, int gy )
{
	if( partyModeFlag != 0 && (pc.status & CHR_STATUS_LEADER) != 0 )
	{
		if( _partyTbl[1] >= 0 )
		{
			stockCharMovePoint( party[_partyTbl[1]].ptAct, gx, gy );
		}
	}
}


///////////////////////////////////////////////////////////////////////////
// ＰＣがリーダーの时、移动したら他のキャラも移动させる
void setPartyMovePoint( void )
{
	int i;
	ACTION *ptAct, *ptActNext;

	if( partyModeFlag != 0 && (pc.status & CHR_STATUS_LEADER) != 0 )
	{
		for( i = 1; i < MAX_PARTY; i++ )
		{
			if( _partyTbl[i] >= 0 )
			{
				ptAct = party[_partyTbl[i]].ptAct;

				// キャラがグリッドの中心か
				if( (float)ptAct->nextGx*GRID_SIZE == ptAct->mx
				 && (float)ptAct->nextGy*GRID_SIZE == ptAct->my )
				{
					// バッファに次の座标があるので设定
					if( ptAct->bufCount > 0 )
					{
#ifdef PUK3_RIDE
						setCharMovePoint( ptAct, ptAct->bufGx[0], ptAct->bufGy[0],
							 pc.walkSpeed );
#else
						setCharMovePoint( ptAct, ptAct->bufGx[0], ptAct->bufGy[0] );
#endif
						shiftBufCount( ptAct );
						// 次の布ティに座标を渡す
						if( _partyTbl[i+1] >= 0
						 && (i+1) < MAX_PARTY )
						{
							ptActNext = party[_partyTbl[i+1]].ptAct;
							stockCharMovePoint( ptActNext, ptAct->gx, ptAct->gy );
						}
					}
				}
			}
			else
			{
				break;
			}
		}
	}
}


///////////////////////////////////////////////////////////////////////////
// 移动の计算
void _mapMove( void )
{
	float nGx, nGy;
	float vx, vy;

#ifdef PUK2
	// マップ座标ハック对策チェック（变更前）
	CheckMapHackStart();
#endif
	if( mapVx != 0 || mapVy != 0 )
	{
		nGx = (float)nextMapGx*GRID_SIZE;
		nGy = (float)nextMapGy*GRID_SIZE;

		vx = mapVx * mapSpdRate;
		vy = mapVy * mapSpdRate;

		// 现在地と目的地の距离が移动量以下なら目的地に到着
		if( pointLen2( mapX, mapY, nGx, nGy )
		 <= vx*vx+vy*vy )
		{
			// 目的地に着いたらパラメータ初期化
			mapX = nGx;
			mapY = nGy;
			mapVx = 0;
			mapVy = 0;
			justGoalFlag = 1;	// 今、目的地に着いた
#ifdef PUK2
			mapGx = (int)(mapX/GRID_SIZE);
			mapGy = (int)(mapY/GRID_SIZE);
#endif
#ifdef PUK3_RIDE
			// 残り步数
			if ( pc.walkRest > 0 ){
				pc.walkRest--;
				// 残り步数が无くなったら
				if ( pc.walkRest <= 0 ){
					if ( pc.ptAct != NULL ){
						CHAREXTRA *ext = (CHAREXTRA *)pc.ptAct->pYobi;
	
						// ライド終了
						pc.ptAct->actNo = CHARACT_GET_OFF;
						ext->actCnt = 0;
	
						// ライド終了送信
						nrproto_AC_send( sockfd, nextMapGx, nextMapGy, 70 );
					}
				}
			}
#endif
		}
		else
		{
			// 移动
			mapX += vx;
			mapY += vy;
		}
		setPcAction( ANIM_WALK );
		setPcWalkFlag();

		if( pc.ptAct != NULL )
		{
			mapEffectMoveDir = pc.ptAct->anim_ang;
		}
	}
	else
	{
		// PCキャラのアニメーションがWALKの次のフレームならならSTANDにする
		if( checkPcWalkFlag() == 1 )
		{
			setPcAction( ANIM_STAND );
			delPcWalkFlag();
		}
		mapEffectMoveDir = -1;
	}

#ifdef PUK2
#else
	// マップ座标ハック对策チェック（变更前）
	CheckMapHackStart();

	mapGx = (int)(mapX/GRID_SIZE);
	mapGy = (int)(mapY/GRID_SIZE);
#endif
	
	// マップ座标ハック对策チェック（变更后）
	CheckMapHackEnd();


	// PCキャラをマップの位置と同期する
	setPcPoint();
}


///////////////////////////////////////////////////////////////////////////
// ＰＣがリーダーの时の布ティー
void _partyMapMove( void )
{
	int i;

	// ＰＣがリーダーの时で移动したら他のキャラも移动する
	if( partyModeFlag != 0 && (pc.status & CHR_STATUS_LEADER) != 0 )
	{
		for( i = 1; i < MAX_PARTY; i++ )
		{
			if( _partyTbl[i] >= 0 )
			{
				charMove2( party[_partyTbl[i]].ptAct );
			}
			else
			{
				break;
			}
		}
	}
}


///////////////////////////////////////////////////////////////////////////
// 布ティー情报を诘める
void getPartyTbl( void )
{
	int i, j;

	if( partyModeFlag != 0 )
	{
		// テーブル初期化
		for( i = 0; i < MAX_PARTY; i++ )
		{
			_partyTbl[i] = -1;
		}
		// 布ティー番号を诘めて取り出す
		for( i = 0, j = 0; i < MAX_PARTY; i++ )
		{
			if( party[i].useFlag != 0 && party[i].ptAct != NULL )
			{
				_partyTbl[j] = i;
				j++;
			}
		}
	}
}


///////////////////////////////////////////////////////////////////////////
// 各种エベントチェック＆实行

enum
{
	etcEventMode_None,
	etcEventMode_Warp,
	etcEventMode_LocalEncount,
	etcEventMode_Enemy
};

BOOL _etcEventFlag = FALSE;
short _etcEventStep = 0;
short _etcEventMode = etcEventMode_None;
short _eventWarpNo;


//  初期化关数。
void initEvent( void )
{
	_etcEventFlag = FALSE;
	_etcEventStep = 0;
	_etcEventMode = etcEventMode_None;
}


void _etcEventCheck( void )
{
	int i;
	int dir;
	int gx, gy;
	int ogx, ogy;
	BOOL breakFlag;


	gx = mapGx;
	gy = mapGy;
	ogx = gx;
	ogy = gy;
	breakFlag = FALSE;

	// これから移动しようとする経路で何かが起こるかチェックする
	for( i = 0; i < moveRouteCnt2; i++ )
	{
		gx += moveAddTbl[moveRoute2[i]][0];
		gy += moveAddTbl[moveRoute2[i]][1];
		// 目的地の方向を调べる
		dir = getDirFromXY( (float)(gx-ogx), (float)(gy-ogy) );

		// 固定エンカウントチェック
		if( _checkEnemyEvent( gx, gy ) )
		{
			_etcEventFlag = TRUE;
			_etcEventStep = i;
			_etcEventMode = etcEventMode_Enemy;
			_enemyEventDir = dir;
			if( _enemyEventDir < 0 )
				_enemyEventDir += 8;
			i--;
			breakFlag = TRUE;
			break;
		}
		else
		// ワープイベントチェック
		if( _checkWarpEvent( gx, gy ) )
		{
			_etcEventFlag = TRUE;
			_etcEventStep = i+1;
			_etcEventMode = etcEventMode_Warp;
			breakFlag = TRUE;
			break;
		}
		else
		// ローカルエンカウントチェック
		if( _checkEncount() )
		{
			_etcEventFlag = TRUE;
			_etcEventStep = i+1;
			_etcEventMode = etcEventMode_LocalEncount;
			breakFlag = TRUE;
			break;
		}
		ogx = gx;
		ogy = gy;
#ifdef VERSION_TW
		//台服取消客户端判断,改为由服务端控制是否遇敌
		if (i >= moveRouteCnt2) {
			breakFlag = TRUE;
			break;
		}
#endif
	}
	// 移动途中で何かが起こるのでそれ以降の移动経路はいらない
	if( i < MOVE_MAX2 && breakFlag )
	{
		moveRouteCnt2 = i+1;
		moveRouteCnt = 0;
	}
}


BOOL _execEtcEvent( void )
{
	if( _etcEventFlag )
	{
		if( justGoalFlag )
		{
			if( _etcEventStep > 0 )
				_etcEventStep--;
		}

		if( _etcEventStep <= 0 )
		{
			switch( _etcEventMode )
			{
				// ワープイベント発动
				case etcEventMode_Warp:
#ifndef _DEBUG
					_sendWarpEvent();
#else
					if( !offlineFlag )
					{
						_sendWarpEvent();
					}
#endif
					break;

				// ローカルエンカウント発动
				case etcEventMode_LocalEncount:
#ifndef _DEBUG
					_sendEncount();
#else
					if( !offlineFlag )
					{
						_sendEncount();
					}
#endif
					break;

				// 敌イベントにあたった
				case etcEventMode_Enemy:
#ifndef _DEBUG
					_sendEnemyEvent();
#else
					if( !offlineFlag )
					{
						_sendEnemyEvent();
					}
#endif
					break;
			}

			_etcEventFlag = FALSE;
			_etcEventStep = 0;
			return TRUE;
		}
	}

	return FALSE;

}


///////////////////////////////////////////////////////////////////////////
// 移动経路からサーバに送る最大数分取り出す
void _getMoveRoute2( void )
{
	int i;

	for( i = 0; i < MOVE_MAX2 && moveRouteCnt > 0; i++ )
	{
		moveRoute2[moveRouteCnt2] = moveRoute[0];
		moveRouteCnt2++;
		shiftRouteMap();
	}
}


///////////////////////////////////////////////////////////////////////////
// サーバに移动経路送る
void _sendMoveRoute( void )
{
	int i;

	// データ无いなら何もしない
	if( moveRouteCnt2 <= 0 )
		return;

	// サーバに移动経路送る
	for( i = 0; i < moveRouteCnt2; i++ )
	{
		moveRouteDir[i] = cnvServDir( moveRoute2[i], 0 );
	}
#ifdef _DEBUG
	if( !offlineFlag )
#endif
	{
		// 谁の仲间でもない时に送る
		if( partyModeFlag == 0 || (pc.status & CHR_STATUS_LEADER) != 0 )
		{
			moveRouteDir[i] = '\0';

#ifdef MAP_CACHE_PROC
			if( checkMapCache( mapGx, mapGy, moveRoute2, moveRouteCnt2,
				mapNo, mapGxSize, mapGySize ) )
			{
				// キャッシュがあったのでチェックサム无し
				noChecksumWalkSendForServer( mapGx, mapGy, moveRouteDir );
			}
			else
			{
				// キャッシュが无いのでチェックサムあり
				walkSendForServer( mapGx, mapGy, moveRouteDir );
			}
#else
			walkSendForServer( mapGx, mapGy, moveRouteDir );
#endif
		}
	}
#if 0
#ifdef _DEBUG_MSG
	{
		char msg[256];
		sprintf( msg, "发给服务器移动线路(%d %d %s)", mapGx, mapGy, moveRouteDir );  //MLHIDE
		StockChatBufferLine( msg, FONT_PAL_GRAY );
	}
#endif
#endif
}


///////////////////////////////////////////////////////////////////////////
// エンカウント处理
//
// エンカウントしたかチェック
BOOL _checkEncount( void )
{
	BOOL ret = FALSE;

#ifdef VERSION_TW
	//台服取消客户端判断,改为由服务端控制是否遇敌
	return ret;
#endif

#ifdef _DEBUG
	// エンカウント无しフラグがたってたらエンカウントしない
	if( EncountOffFlag )
		return FALSE;
#endif

	// 自分が布ティーの一员でない时处理する
	if( partyModeFlag == 0 || (pc.status & CHR_STATUS_LEADER) != 0 )
	{
//		if( nowEncountPercentage > (rand()%100) )
		if( nowEncountPercentage > rand2() )
		{
			ret = TRUE;
			nowEncountExtra = 0;
			
			if( autoMapOpenFlag == FALSE ){	// ohta
				// オートマップ表示の有无を记忆
				autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
			}
		}
	}
	if( 6 > nowEncountExtra )
	{
		nowEncountExtra++;
	}
	else
	{
		if( maxEncountPercentage > nowEncountPercentage )
			nowEncountPercentage++;
	}

	return ret;
}

// サーバにエンカウントしたと送る
void _sendEncount( void )
{
	// 移动を止める
	resetMap();
	sendEnFlag = 1;
	etcEventFlag = 1;
	eventEnemyFlag = 0;

	// エンカウントしたとサーバーに言う
	nrproto_EN_send( sockfd, mapGx, mapGy );
}


///////////////////////////////////////////////////////////////////////////
// ワープイベント处理
//
// ワープイベントチェック
BOOL _checkWarpEvent( int gx, int gy )
{
	int x, y;
	int timeZoneNo;

	x = gx - mapAreaX1;
	y = gy - mapAreaY1;

	timeZoneNo = getNRTime( &nrTime );

#ifdef PUK2
	_eventWarpNo = (mapEvent[y*mapAreaWidth+x] & 0x00ff);
#else
	_eventWarpNo = (mapEvent[y*mapAreaWidth+x] & 0x0fff);
#endif
	// ワープ
	if( _eventWarpNo == EVENT_WARP
	 || _eventWarpNo == EVENT_WARP_HOUSE
	 || _eventWarpNo == EVENT_ROOM_IN
#ifdef PUK2
	 || _eventWarpNo == EVENT_GUILDROOM_IN
#endif
	 || _eventWarpNo == EVENT_WARP_OBJECT)
	{
		return TRUE;
	}
	else
	// 朝だけワープ
	if( _eventWarpNo == EVENT_WARP_MONING
	 && timeZoneNo == NR_MORNING )
	{
		return TRUE;
	}
	else
	// 昼だけワープ
	if( _eventWarpNo == EVENT_WARP_NOON
	 && (timeZoneNo == NR_NOON || timeZoneNo == NR_EVENING) )
	{
		return TRUE;
	}
	else
	// 夜だけワープ
	if( _eventWarpNo == EVENT_WARP_NIGHT
	 && timeZoneNo == NR_NIGHT )
	{
		return TRUE;
	}

	return FALSE;
}


// ワープイベント送信
void _sendWarpEvent( void )
{
	if( autoMapOpenFlag == FALSE ){	// ohta
		autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
	}
	// 移动を止める
	resetMap();	// <-menuClose( MENU_ALL );はこの中でされている
	eventWarpSendFlag = 1;
	etcEventFlag = 1;
	eventWarpSendId = eventId;
	nrproto_EV_send( sockfd, _eventWarpNo, eventId, mapGx, mapGy, -1 );
	eventId++;


#if 1
	// ワープ演出实行
	SubProcNo = 200;
	// 现时点の画面を作る
	warpEffectProc();
	warpEffectFlag = TRUE;
	warpEffectStart = TRUE;
	warpMcFlag = FALSE;
	// マップ名受信济みフラグＯＦＦ
	warpMnFlag = FALSE;	
	floorChangeFlag = TRUE;
#endif
}


///////////////////////////////////////////////////////////////////////////
// 固定エンカウントイベント处理
//
// イベントチェック
BOOL _checkEnemyEvent( int gx, int gy )
{
	int x, y;
	int ev;

	x = gx - mapAreaX1;
	y = gy - mapAreaY1;

#ifdef PUK2
	ev = (mapEvent[y*mapAreaWidth+x] & 0x00ff);
#else
	ev = (mapEvent[y*mapAreaWidth+x] & 0x0fff);
#endif
	// エンカウント
	if( ev == EVENT_ENEMY )
	{
		return TRUE;
	}

	return FALSE;
}


// 固定エンカウントイベント送信
void _sendEnemyEvent( void )
{
	if( autoMapOpenFlag == FALSE ){	// ohta
		autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
	}
	// 移动を止める
	resetMap();	// <- menuClose( MENU_ALL ); 中で实行してる
	eventEnemySendFlag = 1;
	etcEventFlag = 1;
	eventEnemySendId = eventId;
	nrproto_EV_send( sockfd, EVENT_ENEMY, eventId,	mapGx, mapGy, _enemyEventDir );
	eventId++;
	eventEnemyFlag = 1;	// 固定敌にエンカウントしたら１にする
}


///////////////////////////////////////////////////////////////////////////
//
// 移动先にデータが无い时は移动をとめてマップが来るのを待つ。
//
void _checkEmptyMap( void )
{
	int dir;
	int i, j;

	i = 0;

	dir = moveRoute2[i];

	// 移动先にデータが无ければフラグを立てて移动をとめる
	if( checkEmptyMap( dir ) )
	{
		for( j = 0; j < getMapAreaCnt; j++ )
		{
#if 0
#ifdef _DEBUG_MSG
			char msg[256];

			sprintf( msg, "没有数据，无法移动(%d,%d) dir = %d",                         //MLHIDE
				mapGx, mapGy, dir );
			StockChatBufferLine( msg, FONT_PAL_GRAY );
			sprintf( msg, "数据(%d,%d)-(%d,%d)从服务器请求",                           //MLHIDE
				getMapAreaX1[j],
				getMapAreaY1[j],
				getMapAreaX2[j],
				getMapAreaY2[j] );
			StockChatBufferLine( msg, FONT_PAL_GRAY );
#endif
#endif
			// サーバに要求
			nrproto_M_send( sockfd, mapKind, mapNo,
				getMapAreaX1[j],
				getMapAreaY1[j],
				getMapAreaX2[j],
				getMapAreaY2[j] );
		}

		#ifdef MAP_DATA_EMPTY_PROC
		{
			MAP_AREA_INFO *_info1 = NULL, *_info2 = NULL;
			MAP_AREA_INFO info1, info2;

			if( getMapAreaCnt >= 1 )
			{
				info1.kind   = mapKind;
				info1.no     = mapNo;
				info1.areaX1 = getMapAreaX1[0];
				info1.areaY1 = getMapAreaY1[0];
				info1.areaX2 = getMapAreaX2[0];
				info1.areaY2 = getMapAreaY2[0];
				_info1 = &info1;
			}
			if( getMapAreaCnt >= 2 )
			{
				info2.kind   = mapKind;
				info2.no     = mapNo;
				info2.areaX1 = getMapAreaX1[1];
				info2.areaY1 = getMapAreaY1[1];
				info2.areaX2 = getMapAreaX2[1];
				info2.areaY2 = getMapAreaY2[1];
				_info2 = &info2;
			}
			setMapDataEmptyArea( _info1, _info2 );
		}
		#endif
		// 空マップのフラグをたてる。_checkEmptyMap()
		mapEmptyFlag = TRUE;
		mapEmptyDir = dir;
		mapEmptyGx = mapGx;
		mapEmptyGy = mapGy;
		mapEmptyStartTime = GetTickCount();	// マップ要求した时の时间を记忆
	}
}


// 布ティのno番目のキャラを(x,y)へ移动し
// 以降のキャラを前へ诘める
void goFrontPartyCharacter( int no, int x, int y )
{
	int i;
	ACTION *ptAct;
	int ox, oy;

	// リーダの情报が无いなら（ありえないが）終わる
	if( party[0].ptAct == NULL )
		return;

	// リーダが移动中なら終わる
	ptAct = party[0].ptAct;
	if( ptAct->bufCount > 0
	 || (float)ptAct->nextGx*GRID_SIZE != ptAct->mx
	 || (float)ptAct->nextGy*GRID_SIZE != ptAct->my )
	{
		return;
	}

	// 拔けたキャラの前后が１グリッド以内なら終わる
	for( i = no-1; i >= 0; i-- )
	{
		if( party[i].useFlag && party[i].ptAct != NULL )
		{
			if( ABS( party[i].ptAct->nextGx - party[no].ptAct->nextGx ) < 2
			 && ABS( party[i].ptAct->nextGy - party[no].ptAct->nextGy ) < 2 )
				return;
			i = -1;
			break;
		}
	}
	if( i >= 0 )
		return;

	mapSpdRate = 1.0F;
	i = no;
	while( i < MAX_PARTY )
	{
		if( party[i].useFlag && party[i].ptAct != NULL )
		{
			ptAct = party[i].ptAct;
			ox = party[i].ptAct->nextGx;
			oy = party[i].ptAct->nextGy;
			stockCharMovePoint( ptAct, x, y );
			x = ox;
			y = oy;
		}
		i++;
	}
}







// マップに移动先を设定
#ifdef PUK3_RIDE
void setMapMovePoint( int _nextGx, int _nextGy, int walkSpeed )
#else
void setMapMovePoint( int _nextGx, int _nextGy )
#endif
{
	float dx, dy;
	float len;
	float rate = 1.0F;
	int dir;
#ifdef PUK3_RIDE
	CHAREXTRA *pYobi = NULL;
	if ( pc.ptAct ) pYobi = ( CHAREXTRA *)pc.ptAct->pYobi;

	rate = ( rate * walkSpeed ) / 100;

	if( pYobi &&  pYobi->ptPet != NULL ){
		// ペットを自分がいる位置に动かす
		setCharMovePoint( pYobi->ptPet, pc.ptAct->gx, pc.ptAct->gy, walkSpeed, rate );
	}
#else

#ifdef PUK2
	CHAREXTRA *pYobi = NULL;
	if ( pc.ptAct ) pYobi = ( CHAREXTRA *)pc.ptAct->pYobi;

	if( pYobi && pYobi->ptPet != NULL ){
		// ペットを自分がいる位置に动かす
		setCharMovePoint( pYobi->ptPet, pc.ptAct->gx, pc.ptAct->gy );
	}
#endif

#endif
	nextMapGx = _nextGx;
	nextMapGy = _nextGy;

	// 移动量を求める
	dx = nextMapGx*GRID_SIZE-mapX;
	dy = nextMapGy*GRID_SIZE-mapY;
	len = (float)sqrt( (double)(dx*dx+dy*dy) );
	if( len > 0 )
	{
		dx /= len;
		dy /= len;
	}
	else
	{
		dx = 0;
		dy = 0;
	}




	mapVx = dx * MOVE_SPEED * rate;
	mapVy = dy * MOVE_SPEED * rate;

	// PCキャラの方向设定
	if( dx != 0 || dy != 0 )
	{
		dir = getDirFromXY( dx, dy );
		setPcDir( dir );
		setPcWalkFlag();
	}

	// PCキャラをマップの位置と同期する
	setPcPoint();
}


// マップに移动先を设定（加速处理あり）
void setMapMovePoint2( int _nextGx, int _nextGy )
{
	float dx, dy;
	float len;
	float rate = 1.0F;
	int dir;

	if( pc.ptAct != NULL )
	{
		if( pc.ptAct->bufCount > 5 )
		{
			rate = 2.0F;
		}
		else
		if( pc.ptAct->bufCount >= 4 )
		{
			rate = 1.6F;
		}
		else
		if( pc.ptAct->bufCount >= 2 )
		{
			rate = 1.2F;
		}
	}

	nextMapGx = _nextGx;
	nextMapGy = _nextGy;

	// 移动量を求める
	dx = nextMapGx*GRID_SIZE-mapX;
	dy = nextMapGy*GRID_SIZE-mapY;
	len = (float)sqrt( (double)(dx*dx+dy*dy) );
	if( len > 0 )
	{
		dx /= len;
		dy /= len;
	}
	else
	{
		dx = 0;
		dy = 0;
	}
	mapVx = dx * MOVE_SPEED * rate;
	mapVy = dy * MOVE_SPEED * rate;

	// PCキャラの方向设定
	if( dx != 0 || dy != 0 )
	{
		dir = getDirFromXY( dx, dy );
		setPcDir( dir );
		setPcWalkFlag();
	}

	// PCキャラをマップの位置と同期する
	setPcPoint();
}


// マップ移动处理
void mapMove2( void )
{
	float dx, dy;

	// 移动中か？
	if( mapVx != 0 || mapVy != 0 )
	{
		// 目的地に着いたらパラメータ初期化
		dx = (float)nextMapGx*GRID_SIZE;
		dy = (float)nextMapGy*GRID_SIZE;
		if( pointLen2( mapX, mapY, dx, dy ) <= mapVx*mapVx+mapVy*mapVy )
		{
			mapX = dx;
			mapY = dy;
			mapVx = 0;
			mapVy = 0;
		}
		// 移动
		else
		{
			mapX += mapVx;
			mapY += mapVy;
		}
		setPcAction( ANIM_WALK );
		setPcWalkFlag();
	}
	else
	{
		// PCキャラのアニメーションがWALKの次のフレームならならSTANDにする
		if( checkPcWalkFlag() == 1 )
		{
			setPcAction( ANIM_STAND );
			delPcWalkFlag();
		}
	}

	// マップ座标ハック对策チェック（变更前）
	CheckMapHackStart();

	mapGx = (int)(mapX/GRID_SIZE);
	mapGy = (int)(mapY/GRID_SIZE);
	
	// マップ座标ハック对策チェック（变更后）
	CheckMapHackEnd();

	// PCキャラをマップの位置と同期する
	setPcPoint();
}



///////////////////////////////////////////////////////////////////////////
// その场で向きを变える
//
void turnAround( void )
{
	float tmpX, tmpY;
	int dir;
	char dir2[2];
	static unsigned int turnSendTime = 0;

	// PCキャラいなければ何もしない
	if( pc.ptAct == NULL )
		return;

	// 移动の途中でも何もしない
	if( mapVx != 0 || mapVy != 0 )
		return;

	// 何かのプロトコルを送ってたら何もしない
	if( etcSendFlag != 0 || etcEventFlag != 0 )
		return;

	// カーソルの位置がキャラ（マップ中心）と同じなら何もしない
	if( mouseMapGx == mapGx && mouseMapGy == mapGy )
	{
		return;
	}

	// 现在のマウスの方向を调べる
	tmpX = (float)(mouseMapGx - mapGx);
	tmpY = (float)(mouseMapGy - mapGy);
	dir = getDirFromXY( tmpX, tmpY );


	// 向きが同じ时も何もしない
//	if( pc.ptAct->anim_ang == dir )
//		return;

	// 连射抑制
	if( turnSendTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )
	{
		setPcDir( dir );

		dir2[0] = cnvServDir( dir, 1 );
		dir2[1] = '\0';

		walkSendForServer( mapGx, mapGy, dir2 );
		turnSendTime = GetTickCount();
	}
}


void turnAround2( int dir )
{
	char dir2[2];
	static unsigned int turnSendTime = 0;

	// 方向が间违っていれば何もしない
	if( dir < 0 || 7 < dir )
		return;

	// PCキャラいなければ何もしない
	if( pc.ptAct == NULL )
		return;

	// 移动の途中でも何もしない
	if( mapVx != 0 || mapVy != 0 )
		return;

	// 何かのプロトコルを送ってたら何もしない
	if( etcSendFlag != 0 || etcEventFlag != 0 )
		return;

	// 向きが同じ时も何もしない
	if( pc.ptAct->anim_ang == dir )
		return;

	// 连射抑制
	if( turnSendTime+FIELD_BTN_PUSH_WAIT < GetTickCount() )
	{
		setPcDir( dir );

		dir2[0] = cnvServDir( dir, 1 );
		dir2[1] = '\0';

		walkSendForServer( mapGx, mapGy, dir2 );
		turnSendTime = GetTickCount();
	}
}


///////////////////////////////////////////////////////////////////////////
// 移动経路を算出する
//


// (gx1,gy1)から(gx2,gy2)への向き
int getDirData( int gx1, int gy1, int gx2, int gy2 )
{
	float tmpX, tmpY;

	// 目的地の方向を调べる
	tmpX = (float)(gx2 - gx1);
	tmpY = (float)(gy2 - gy1);

	return getDirFromXY( tmpX, tmpY );
}

// (gx1,gy2)を目的地とし(gx2,gy2)が邻接グリッドかどうか调べる
// 戾り值：TRUE ... 邻接グリッド
BOOL checkGridAround( int gx1, int gy1, int gx2, int gy2 )
{
	if( ((gx1 == gx2) && ABS( gy2 - gy1 ) == 1)
	 || (ABS( gx2 - gx1 ) == 1 && (gy1 == gy2))
	 || (ABS( gx2 - gx1 ) == 1 && ABS( gy2 - gy1 ) == 1 ) )
	{
		return TRUE;
	}

	return FALSE;
}


#if 1

#ifdef PUK3_GET_ROUTE
// ２回の移动を可能なら１回の移动に置き换える
// 戾り值	TRUE なら变换した、FALSE なら变换できなかった
BOOL change2MoveTo1Move( int nowx, int nowy, int newx, int newy, int oldx, int oldy )
{
	int checkDir1, checkDir2;
	int dirN, dxA, dyA, dxB, dyB;
	BOOL flagA, flagB;
	int i;

	// 移动して无いなら終了
	if ( oldx == 0 && oldy == 0 ) return FALSE;
	if ( newx == 0 && newy == 0 ) return FALSE;

	// 斜め移动一回で济まなそうなとき終了
	if ( !(oldx + newx == 1 || oldx + newx == -1) ) return FALSE;
	if ( !(oldy + newy == 1 || oldy + newy == -1) ) return FALSE;

	for(i=0;i<8;i++){
		if ( moveAddTbl[i][0] != oldx + newx ) continue;
		if ( moveAddTbl[i][1] != oldy + newy ) continue;
		break;
	}
	dirN = i;

	checkDir1 = dirN + 1;
	checkDir1 &= 7;
	dxA = moveAddTbl[checkDir1][0];
	dyA = moveAddTbl[checkDir1][1];

	checkDir2 = dirN - 1;
	checkDir2 &= 7;
	dxB = moveAddTbl[checkDir2][0];
	dyB = moveAddTbl[checkDir2][1];

	flagA = checkHitMap( (nowx-oldx)+dxA, (nowy-oldy)+dyA );
	flagB = checkHitMap( (nowx-oldx)+dxB, (nowy-oldy)+dyB );

	// どっちにも障害物が无いとき
	if ( !flagA && !flagB ){
		moveRouteCnt = moveRouteCnt - 1;

		nowx += newx;
		nowy += newy;
		moveRoute[moveRouteCnt] = dirN;
		moveRouteGx[moveRouteCnt] = nowx;
		moveRouteGy[moveRouteCnt] = nowy;

		moveRouteCnt++;
		return TRUE;
	}

	return FALSE;
}
#endif
void getRouteMap( void )
{
	int mx = moveStackGx, my = moveStackGy;
	int nowx = mapGx, nowy = mapGy;
	int dir;
	int dx, dy;
	float tmpX, tmpY;
	int targetDir;
	int nowDir, checkDir1, checkDir2;
	int dirTbl[] = { 0, 1, -1 };
	int i;
	BOOL flag;
#ifdef PUK3_GET_ROUTE
	int rec[2] = {0};
#endif

	moveLastDir = -1;

	if( nowx == mx && nowy == my )
		return;

	// 目的地の方向を调べる
	tmpX = (float)(mx - nowx);
	tmpY = (float)(my - nowy);
	targetDir = getDirFromXY( tmpX, tmpY );

	moveRouteCnt = 0;

	while( nowx != mx || nowy != my )
	{
		if( moveRouteCnt >= MOVE_MAX )
		{
			moveRouteCnt = 0;
			return;
		}

		// 目的地の方向を调べる
		tmpX = (float)(mx - nowx);
		tmpY = (float)(my - nowy);
		nowDir = getDirFromXY( tmpX, tmpY );

#if 1
		{
			checkDir1 = targetDir + 1;
			checkDir1 &= 7;
			checkDir2 = targetDir - 1;
			checkDir2 &= 7;
			if( nowDir != targetDir && nowDir != checkDir1 && nowDir != checkDir2 )
			{
				// 初期位置から目的地への方向と左右前方以外の方向には移动しない
				break;
			}
		}
#endif


		flag = TRUE;
		for( i = 0; i < sizeof( dirTbl )/sizeof( int ); i++ )
		{
			dir = nowDir + dirTbl[i];
			dir &= 7;
			checkDir1 = targetDir + 1;
			checkDir1 &= 7;
			checkDir2 = targetDir - 1;
			checkDir2 &= 7;
			if( dir != targetDir && dir != checkDir1 && dir != checkDir2 )
			{
				// 初期位置から目的地への方向と左右前方以外の方向には移动しない
				continue;
			}

#if 0
			// １步でも动いた后、目的地と轴があったら終了
			if( moveRouteCnt > 0
			 && (nowx == mx || nowy == my) )
			{
				flag = TRUE;
				break;
			}
#endif

			dx = moveAddTbl[dir][0];
			dy = moveAddTbl[dir][1];
			if( checkHitMap( nowx+dx, nowy+dy ) )
			{
				// 进入不可
				if( (nowx+dx) == mx && (nowy+dy) == my )
				{
#if 1
					if( (dir % 2) == 1 )
						continue;
#endif
					// 移动先が目的地なら終了
					flag = TRUE;
					break;
				}
				else
				{
					continue;
				}
			}
			// グリッド间を斜めに移动する时、前方左右斜め前の障害物によって动作が变わる
			if( (dir % 2) == 1 )
			{
				int dir2, dx2, dy2;
				int dir3, dx3, dy3;
				BOOL flag2, flag3;

				dir2 = dir + 1;
				dir2 &= 7;
				dx2 = moveAddTbl[dir2][0];
				dy2 = moveAddTbl[dir2][1];

				dir3 = dir - 1;
				dir3 &= 7;
				dx3 = moveAddTbl[dir3][0];
				dy3 = moveAddTbl[dir3][1];
				flag2 = checkHitMap( nowx+dx2, nowy+dy2 );
				flag3 = checkHitMap( nowx+dx3, nowy+dy3 );
				// 两方に障害物があるなら行き止まり
				if( flag2 && flag3 )
				{
					break;
				}
				// 右斜め前に障害物がある时
				// 左斜め前に回避
				if( flag2 )
				{
#ifdef PUK3_GET_ROUTE
					// ２回の移动を１回にまとめる
					if ( change2MoveTo1Move( nowx, nowy, dx3, dy3, rec[0], rec[1] ) ){
						rec[0] = rec[0] + dx3;
						rec[1] = rec[1] + dy3;
					}else{
						moveRoute[moveRouteCnt] = dir3;
						moveRouteGx[moveRouteCnt] = nowx + dx3;
						moveRouteGy[moveRouteCnt] = nowy + dy3;
						moveRouteCnt++;
						// まとめる必要が无いので0で初期化
						rec[0] = 0;
						rec[1] = 0;
					}
#else
					moveRoute[moveRouteCnt] = dir3;
					moveRouteGx[moveRouteCnt] = nowx + dx3;
					moveRouteGy[moveRouteCnt] = nowy + dy3;
					moveRouteCnt++;
#endif
					dx = (nowx+dx) - (nowx+dx3);
					dy = (nowy+dy) - (nowy+dy3);
					dir = getDirData( 0, 0, dx, dy );
					nowx += dx3;
					nowy += dy3;
					flag = FALSE;
					break;
				}
				// 左斜め前に障害物がある时
				// 右斜め前に回避
				if( flag3 )
				{
#ifdef PUK3_GET_ROUTE
					// ２回の移动を１回にまとめる
					if ( change2MoveTo1Move( nowx, nowy, dx2, dy2, rec[0], rec[1] ) ){
						rec[0] = rec[0] + dx2;
						rec[1] = rec[1] + dy2;
					}else{
						moveRoute[moveRouteCnt] = dir2;
						moveRouteGx[moveRouteCnt] = nowx + dx2;
						moveRouteGy[moveRouteCnt] = nowy + dy2;
						moveRouteCnt++;
						// まとめる必要が无いので0で初期化
						rec[0] = 0;
						rec[1] = 0;
					}
#else
					moveRoute[moveRouteCnt] = dir2;
					moveRouteGx[moveRouteCnt] = nowx + dx2;
					moveRouteGy[moveRouteCnt] = nowy + dy2;
					moveRouteCnt++;
#endif
					dx = (nowx+dx) - (nowx+dx2);
					dy = (nowy+dy) - (nowy+dy2);
					dir = getDirData( 0, 0, dx, dy );
					nowx += dx2;
					nowy += dy2;
					flag = FALSE;
					break;
				}
			}
			// ここまで来たら移动先が决定している
			flag = FALSE;
			break;
		}

		// 行き止まりなので終わる
		if( flag )
			break;


#ifdef PUK3_GET_ROUTE
		// ２回の移动を１回にまとめる
		if ( change2MoveTo1Move( nowx, nowy, dx, dy, rec[0], rec[1] ) ){
			nowx += dx;
			nowy += dy;
			rec[0] = rec[0] + dx;
			rec[1] = rec[1] + dy;
			continue;
		}
#endif
		nowx += dx;
		nowy += dy;
		moveRoute[moveRouteCnt] = dir;
		moveRouteGx[moveRouteCnt] = nowx;
		moveRouteGy[moveRouteCnt] = nowy;

		moveRouteCnt++;
#ifdef PUK3_GET_ROUTE
		rec[0] = dx;
		rec[1] = dy;
#endif
	}

#if 1
	dx = 0;
	if( mx - nowx > 0 )
	{
		dx = 1;
	}
	else
	if( mx - nowx < 0 )
	{
		dx = -1;
	}
	if( dx != 0 )
	{
		while( !checkHitMap( nowx+dx, nowy ) && mx != nowx )
		{
			nowx += dx;
			moveRoute[moveRouteCnt] = getDirData( 0, 0, dx, 0 );
			moveRouteGx[moveRouteCnt] = nowx;
			moveRouteGy[moveRouteCnt] = nowy;
			moveRouteCnt++;
		}
	}

	dy = 0;
	if( my - nowy > 0 )
	{
		dy = 1;
	}
	else
	if( my - nowy < 0 )
	{
		dy = -1;
	}
	if( dy != 0 )
	{
		while( !checkHitMap( nowx, nowy+dy ) && my != nowy )
		{
			nowy += dy;
			moveRoute[moveRouteCnt] = getDirData( 0, 0, 0, dy );
			moveRouteGx[moveRouteCnt] = nowx;
			moveRouteGy[moveRouteCnt] = nowy;
			moveRouteCnt++;
		}
	}
#endif

	// 目的地の方向を调べる
	if( moveRouteCnt > 0
	 && (nowx != mx || nowy != my) )
	{
		tmpX = (float)(mx - nowx);
		tmpY = (float)(my - nowy);
		moveLastDir = getDirFromXY( tmpX, tmpY );
	}

	return;
}

#else

void getRouteMap( void )
{
	int mx = moveStackGx, my = moveStackGy;
	int nowx = mapGx, nowy = mapGy;
	int dir;
	int dx, dy;
	float tmpDir;
	float tmpX, tmpY;
	int targetDir;
	int nowDir, checkDir1, checkDir2;
	int dirTbl[] = { 0, 1, -1 };
	int i;
	BOOL flag;

	if( nowx == mx && nowy == my )
		return;

	// 目的地の方向を调べる
	tmpX = (float)(mx - nowx);
	tmpY = (float)(my - nowy);
	targetDir = getDirFromXY( tmpX, tmpY );

	moveRouteCnt = 0;

	while( nowx != mx || nowy != my )
	{
		if( moveRouteCnt >= MOVE_MAX )
		{
			moveRouteCnt = 0;
			return;
		}

		// 目的地の方向を调べる
		tmpX = (float)(mx - nowx);
		tmpY = (float)(my - nowy);
		nowDir = getDirFromXY( tmpX, tmpY );

		flag = TRUE;
		for( i = 0; i < sizeof( dirTbl )/sizeof( int ); i++ )
		{
			dir = nowDir + dirTbl[i];
			dir &= 7;
			checkDir1 = targetDir + 1;
			checkDir1 &= 7;
			checkDir2 = targetDir - 1;
			checkDir2 &= 7;
			if( dir != targetDir && dir != checkDir1 && dir != checkDir2 )
			{
				// 初期位置から目的地への方向と左右前方以外の方向には移动しない
				continue;
			}

			dx = moveAddTbl[dir][0];
			dy = moveAddTbl[dir][1];
			if( checkHitMap( nowx+dx, nowy+dy ) )
			{
				// 进入不可
				if( (nowx+dx) == mx && (nowy+dy) == my )
				{
					// 移动先が目的地なら終了
					flag = TRUE;
					break;
				}
				else
				{
					continue;
				}
			}
			// グリッド间を斜めに移动する时、前方左右斜め前の障害物によって动作が变わる
			if( (dir % 2) == 0 )
			{
				int dir2, dx2, dy2;
				int dir3, dx3, dy3;
				BOOL flag2, flag3;

				dir2 = dir + 1;
				dir2 &= 7;
				dx2 = moveAddTbl[dir2][0];
				dy2 = moveAddTbl[dir2][1];

				dir3 = dir - 1;
				dir3 &= 7;
				dx3 = moveAddTbl[dir3][0];
				dy3 = moveAddTbl[dir3][0];
				flag2 = checkHitMap( nowx+dx2, nowy+dy2 );
				flag3 = checkHitMap( nowx+dx3, nowy+dy3 );
				// 两方に障害物があるなら行き止まり
				if( flag2 && flag3 )
				{
					break;
				}
				// 右斜め前に障害物がある时
				// 左斜め前に回避
				if( flag2 )
				{
					dir = dir3;
					dx = dx3;
					dy = dy3;
					flag = FALSE;
					break;
				}
				// 左斜め前に障害物がある时
				// 右斜め前に回避
				if( flag3 )
				{
					dir = dir2;
					dx = dx2;
					dy = dy2;
					flag = FALSE;
					break;
				}
			}
			// ここまで来たら移动先が决定している
			flag = FALSE;
			break;
		}

		// 行き止まりなので終わる
		if( flag )
			break;

		nowx += dx;
		nowy += dy;
		moveRoute[moveRouteCnt] = dir;
		moveRouteGx[moveRouteCnt] = nowx;
		moveRouteGy[moveRouteCnt] = nowy;

		moveRouteCnt++;
	}
	return;
}

#endif


// 登録されている移动ルートを前にシフト
void shiftRouteMap( void )
{
	int i;

	if( moveRouteCnt <= 0 )
		return;

	moveRouteCnt--;
	for( i = 0; i < moveRouteCnt; i++ )
	{
		moveRoute[i] = moveRoute[i+1];
		moveRouteGx[i] = moveRouteGx[i+1];
		moveRouteGy[i] = moveRouteGy[i+1];
	}
}

// 登録されている移动ルートを前にシフト
void shiftRouteMap2( void )
{
	int i;

	if( moveRouteCnt2 <= 0 )
		return;

	moveRouteCnt2--;
	for( i = 0; i < moveRouteCnt2; i++ )
	{
		moveRoute2[i] = moveRoute2[i+1];
	}
}



// 移动中に强制ワープした时、
//   ワープ地点が通过后の位置ならワープ、
//   移动先ならそこまで移动する。
void forceWarpMap( int gx, int gy )
{
	int i;

	// 移动中で无いなら何もしない
	if( moveRouteCnt <= 0 )
		return;

	for( i = 0; i < moveRouteCnt; i++ )
	{
		// ワープ地点が移动先ならそこまで移动する
		if( moveRouteGx[i] == gx && moveRouteGy[i] == gy )
		{
			moveRouteCnt = i + 1;
			return;
		}
	}

	// ワープ地点が通过后の位置ならワープ
	// (移动处理終わる)
	moveRouteCnt = 0;
	setWarpMap( gx, gy );
}


// 方向をサーバに送るときの文字にする
// mode : 0 ... 移动用/ 1 ... 方向变えるだけ
char cnvServDir( int dir, int mode )
{
	char ret;

	if( mode == 0 )
	{
		ret = 'a'+dir;
	}
	else
	{
		ret = 'A'+dir;
	}

	return ret;
}



///////////////////////////////////////////////////////////////////////////
// キャラ、布ツの表示优先顺位决定用

#define MAX_MAP_CHAR_SORT_INFO		1024
#define MAX_MAP_PARTS_SORT_INFO		1024
#define MAX_MAP_TILE_SORT_INFO		1024
#define MAX_MAP_MARGE_INFO			(MAX_MAP_CHAR_SORT_INFO+MAX_MAP_PARTS_SORT_INFO+MAX_MAP_TILE_SORT_INFO)
#ifdef PUK2_DRESS_UP
#define MAX_MAP_CHAR_WITH_INFO		1024
#endif

MAP_CHAR_PARTS_SORT_INFO mapCharSortInfo[MAX_MAP_CHAR_SORT_INFO];
MAP_CHAR_PARTS_SORT_INFO mapPartsSortInfo[MAX_MAP_PARTS_SORT_INFO];
MAP_CHAR_PARTS_SORT_INFO mapTileSortInfo[MAX_MAP_TILE_SORT_INFO];
MAP_CHAR_PARTS_SORT_INFO mapMargeInfo[MAX_MAP_MARGE_INFO];
#ifdef PUK2_DRESS_UP
MAP_CHAR_PARTS_SORT_INFO mapCharWithInfo[MAX_MAP_CHAR_WITH_INFO];
#endif
int mapCharSortCnt;
int mapPartsSortCnt;
int mapTileSortCnt;
int mapMargeCnt;
#ifdef PUK2_DRESS_UP
int mapCharWithCnt;
#endif


void initCharPartsPrio( void )
{
	mapCharSortCnt = 0;
	mapPartsSortCnt = 0;
	mapTileSortCnt = 0;
#ifdef PUK2_DRESS_UP
	mapCharWithCnt = 0;
#endif
}

// キャラをバッファに贮める
#ifdef PUK2
#ifdef PUK2_DRESS_UP
int setMapChar( int graNo, float mx, float my, int screenX, int screenY,
		int pcFlag, struct BLT_MEMBER *bm )
#else
void setMapChar( int graNo, float mx, float my, int screenX, int screenY,
		int pcFlag, struct BLT_MEMBER *bm )
#endif
#else
void setMapChar( int graNo, float mx, float my, int screenX, int screenY,
		int pcFlag )
#endif
{
//	int bmpNo;

//	bmpNo = realGetBitmapNo( graNo );

	// 表示できない画像番号の时は終わる
	// 1.bmpはソート用に使うので通す
//	if( bmpNo <= CG_INVISIBLE && bmpNo == 1 )
//		return;

	// バッファが一杯なので終わる
	if( mapCharSortCnt >= MAX_MAP_CHAR_SORT_INFO )
#ifdef PUK2_DRESS_UP
		return -1;
#else
		return;
#endif

	mapCharSortInfo[mapCharSortCnt].graNo		= graNo;
	mapCharSortInfo[mapCharSortCnt].mx			= mx;
	mapCharSortInfo[mapCharSortCnt].my			= my;
	mapCharSortInfo[mapCharSortCnt].screenX		= screenX;
	mapCharSortInfo[mapCharSortCnt].screenY		= screenY;
	if( pcFlag )
	{
		mapCharSortInfo[mapCharSortCnt].type	= 1;
	}
	else
	{
		mapCharSortInfo[mapCharSortCnt].type	= 0;
	}
	mapCharSortInfo[mapCharSortCnt].no			= mapCharSortCnt;
#ifdef PUK2
	if (bm) mapCharSortInfo[mapCharSortCnt].bm=*bm;
	else{
		mapCharSortInfo[mapCharSortCnt].bm.u=0;
		mapCharSortInfo[mapCharSortCnt].bm.v=0;
		mapCharSortInfo[mapCharSortCnt].bm.w=0;
		mapCharSortInfo[mapCharSortCnt].bm.h=0;
		mapCharSortInfo[mapCharSortCnt].bm.rgba.rgba=0xffffffff;
		mapCharSortInfo[mapCharSortCnt].bm.BltVer=0;
		mapCharSortInfo[mapCharSortCnt].bm.bltf=0;
		mapCharSortInfo[mapCharSortCnt].bm.PalNo=0;
	}
#endif
#ifdef PUK2_DRESS_UP
	mapCharSortInfo[mapCharSortCnt].front = NULL;
	mapCharSortInfo[mapCharSortCnt].rear = NULL;
#endif

	mapCharSortCnt++;
#ifdef PUK2_DRESS_UP
	return mapCharSortCnt - 1;
#endif
}

// 布ツをバッファに贮める
#ifdef PUK2
void setMapParts( int graNo, float mx, float my, int screenX, int screenY, struct BLT_MEMBER *bm )
#else
void setMapParts( int graNo, float mx, float my, int screenX, int screenY )
#endif
{
	short prioType;
	int bmpNo;	// 人间が见る画像番号

	// シーケンス（通番）を画像番号に置き换える
	bmpNo = realGetBitmapNo( graNo );

	// 1.bmpはキャラ扱いで登録
	if( bmpNo == 1 )
	{
		setMapChar( graNo, mx, my, screenX, screenY, 0 );
	}

	// 表示できない画像番号の时は終わる
	if( bmpNo <= CG_INVISIBLE )
		return;

	realGetPrioType( graNo, &prioType );

	// prioTypeはタイル扱い
	if( prioType == 3 )
	{
		// バッファが一杯なので終わる
		if( mapTileSortCnt >= MAX_MAP_TILE_SORT_INFO )
			return;

		mapTileSortInfo[mapTileSortCnt].graNo		= graNo;
		mapTileSortInfo[mapTileSortCnt].mx			= mx;
		mapTileSortInfo[mapTileSortCnt].my			= my;
		mapTileSortInfo[mapTileSortCnt].screenX		= screenX;
		mapTileSortInfo[mapTileSortCnt].screenY		= screenY;
		mapTileSortInfo[mapTileSortCnt].no			= mapTileSortCnt;
		mapTileSortInfo[mapTileSortCnt].type		= 0;
#ifdef PUK2
		if (bm) mapTileSortInfo[mapTileSortCnt].bm=*bm;
		else{
			mapTileSortInfo[mapTileSortCnt].bm.u=0;
			mapTileSortInfo[mapTileSortCnt].bm.v=0;
			mapTileSortInfo[mapTileSortCnt].bm.w=0;
			mapTileSortInfo[mapTileSortCnt].bm.h=0;
			mapTileSortInfo[mapTileSortCnt].bm.rgba.rgba=0xffffffff;
			mapTileSortInfo[mapTileSortCnt].bm.BltVer=0;
			mapTileSortInfo[mapTileSortCnt].bm.bltf=0;
			mapTileSortInfo[mapTileSortCnt].bm.PalNo=0;
		}
#endif
#ifdef PUK2_DRESS_UP
		mapTileSortInfo[mapTileSortCnt].front = NULL;
		mapTileSortInfo[mapTileSortCnt].rear = NULL;
#endif
		mapTileSortCnt++;
	}
	else
	{
		// バッファが一杯なので終わる
		if( mapPartsSortCnt >= MAX_MAP_PARTS_SORT_INFO )
			return;

		mapPartsSortInfo[mapPartsSortCnt].graNo		= graNo;
		mapPartsSortInfo[mapPartsSortCnt].mx		= mx;
		mapPartsSortInfo[mapPartsSortCnt].my		= my;
		mapPartsSortInfo[mapPartsSortCnt].screenX	= screenX;
		mapPartsSortInfo[mapPartsSortCnt].screenY	= screenY;
		mapPartsSortInfo[mapPartsSortCnt].no		= mapPartsSortCnt;
		mapPartsSortInfo[mapPartsSortCnt].type		= 0;
#ifdef PUK2
		if (bm) mapPartsSortInfo[mapPartsSortCnt].bm=*bm;
		else{
			mapPartsSortInfo[mapPartsSortCnt].bm.u=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.v=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.w=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.h=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.rgba.rgba=0xffffffff;
			mapPartsSortInfo[mapPartsSortCnt].bm.BltVer=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.bltf=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.PalNo=0;
		}
#endif
#ifdef PUK2_DRESS_UP
		mapPartsSortInfo[mapPartsSortCnt].front = NULL;
		mapPartsSortInfo[mapPartsSortCnt].rear = NULL;
#endif
		mapPartsSortCnt++;
	}
}

#ifdef PUK2_DRESS_UP
void setMapCharWith( int charNum, BOOL front, int graNo, int screenX, int screenY, struct BLT_MEMBER *bm )
{
	MAP_CHAR_PARTS_SORT_INFO *lp;

	// ベースになる絵が正常に登録されていない场合終了
	if ( charNum < 0 ) return;

	// バッファが一杯なので終わる
	if( mapCharWithCnt >= MAX_MAP_CHAR_WITH_INFO )
		return;

	// front が TRUE なら自分より手前に表示
	if (front){
		// 最后の位置を探す
		for(lp=&mapCharSortInfo[charNum];lp;lp=lp->front){
			if (!lp->front) break;
		}
		// 最后尾に新しいのを繋げる
		lp->front = &mapCharWithInfo[mapCharWithCnt];
	}else{
		// 最后の位置を探す
		for(lp=&mapCharSortInfo[charNum];lp;lp=lp->rear){
			if (!lp->rear) break;
		}
		// 最后尾に新しいのを繋げる
		lp->rear = &mapCharWithInfo[mapCharWithCnt];
	}

	mapCharWithInfo[mapCharWithCnt].graNo		= graNo;
	mapCharWithInfo[mapCharWithCnt].mx			= mapCharSortInfo[charNum].mx;
	mapCharWithInfo[mapCharWithCnt].my			= mapCharSortInfo[charNum].my;
	mapCharWithInfo[mapCharWithCnt].screenX		= screenX;
	mapCharWithInfo[mapCharWithCnt].screenY		= screenY;
	mapCharWithInfo[mapCharWithCnt].type		= 0;
	mapCharWithInfo[mapCharWithCnt].no			= charNum;
#ifdef PUK2
	if (bm) mapCharWithInfo[mapCharWithCnt].bm=*bm;
	else{
		mapCharWithInfo[mapCharWithCnt].bm.u=0;
		mapCharWithInfo[mapCharWithCnt].bm.v=0;
		mapCharWithInfo[mapCharWithCnt].bm.w=0;
		mapCharWithInfo[mapCharWithCnt].bm.h=0;
		mapCharWithInfo[mapCharWithCnt].bm.rgba.rgba=0xffffffff;
		mapCharWithInfo[mapCharWithCnt].bm.BltVer=0;
		mapCharWithInfo[mapCharWithCnt].bm.bltf=0;
		mapCharWithInfo[mapCharWithCnt].bm.PalNo=0;
	}
#endif
#ifdef PUK2_DRESS_UP
	mapCharWithInfo[mapCharWithCnt].front = NULL;
	mapCharWithInfo[mapCharWithCnt].rear = NULL;
#endif

	mapCharWithCnt++;
}
#endif

// バッファの内容を描画（描画バッファに贮める）
void drawMapCharParts( void )
{
#ifdef PUK2_DRESS_UP
	int i;
	MAP_CHAR_PARTS_SORT_INFO *lp;

	for( i = 0; i < mapMargeCnt; i++ )
	{
		// 自分より手前に表示するもの
		for(lp=mapMargeInfo[i].front;lp;lp=lp->front){
			if (mapMargeInfo[i].bm.BltVer==BLTVER_PUK2){
				StockDispBuffer_PUK2( lp->screenX, lp->screenY, DISP_PRIO_PARTS, lp->graNo, 0, 0, &lp->bm );
			}else{
				StockDispBuffer2( lp->screenX, lp->screenY, DISP_PRIO_PARTS, lp->graNo, 0, &lp->bm );
			}
		}

		// 自分
		if (mapMargeInfo[i].bm.BltVer==BLTVER_PUK2){
			StockDispBuffer_PUK2( mapMargeInfo[i].screenX, mapMargeInfo[i].screenY,
				DISP_PRIO_PARTS, mapMargeInfo[i].graNo, 0, 0, &mapMargeInfo[i].bm );
		}else{
			StockDispBuffer2( mapMargeInfo[i].screenX, mapMargeInfo[i].screenY,
				DISP_PRIO_PARTS, mapMargeInfo[i].graNo, 0, &mapMargeInfo[i].bm );
		}

		// 自分より奥に表示するもの
		for(lp=mapMargeInfo[i].rear;lp;lp=lp->rear){
			if (mapMargeInfo[i].bm.BltVer==BLTVER_PUK2){
				StockDispBuffer_PUK2( lp->screenX, lp->screenY, DISP_PRIO_PARTS, lp->graNo, 0, 0, &lp->bm );
			}else{
				StockDispBuffer2( lp->screenX, lp->screenY, DISP_PRIO_PARTS, lp->graNo, 0, &lp->bm );
			}
		}
	}
#else
	int i;

	for( i = 0; i < mapMargeCnt; i++ )
	{
#ifdef PUK2
		if (mapMargeInfo[i].bm.BltVer==BLTVER_PUK2){
			StockDispBuffer_PUK2( mapMargeInfo[i].screenX, mapMargeInfo[i].screenY,
				DISP_PRIO_PARTS, mapMargeInfo[i].graNo, 0, 0, &mapMargeInfo[i].bm );
		}else{
			StockDispBuffer2( mapMargeInfo[i].screenX, mapMargeInfo[i].screenY,
				DISP_PRIO_PARTS, mapMargeInfo[i].graNo, 0, &mapMargeInfo[i].bm );
		}
#else
		StockDispBuffer2( mapMargeInfo[i].screenX, mapMargeInfo[i].screenY,
			DISP_PRIO_PARTS, mapMargeInfo[i].graNo, 0 );
#endif
	}
#endif
}

// バッファの内容をソート
void sortMapCharParts( void )
{
	// キャラのソート
	sortMapChar();
#ifndef PUK2
	// 布ツのソート
	// （キャラへ插入位置も检索してるのでsortMapChar()より后から实行する）
	sortMapParts();
#endif
	// キャラと布ツをマージする
	margeMapCharParts();
}


// 布ツのソート
void sortMapParts( void )
{
	int i, j;

#if 0
	// ソート
	qsort(	&mapPartsSortInfo[0],				// 构造体のアドレス
			mapPartsSortCnt,					// 比较する个数
			sizeof( MAP_CHAR_PARTS_SORT_INFO ), // 构造体のサイズ
			cmpSortMapParts						// 比较关数へのポインタ
	);

	for( i = 0; i < mapPartsSortCnt; i++ )
	{
		mapPartsSortInfo[i].no = i;
	}
#endif

	// キャラに插入する位置を检索
	for( i = 0; i < mapPartsSortCnt; i++ )
	{
		for( j = 0; j < mapCharSortCnt; j++ )
		{
			if( checkMapCharParts( &mapCharSortInfo[j], &mapPartsSortInfo[i] ) )
			{
				mapPartsSortInfo[i].insert = j;
				break;
			}
		}
		if( j >= mapCharSortCnt )
		{
			mapPartsSortInfo[i].insert = j;
		}
	}

	// ソート
	qsort(	&mapPartsSortInfo[0],				// 构造体のアドレス
			mapPartsSortCnt,					// 比较する个数
			sizeof( MAP_CHAR_PARTS_SORT_INFO ), // 构造体のサイズ
			cmpSortMapParts2					// 比较关数へのポインタ
	);
}


// 布ツソート用
int cmpSortMapParts( const void *_pt1, const void *_pt2 )
{
	MAP_CHAR_PARTS_SORT_INFO *pt1 = (MAP_CHAR_PARTS_SORT_INFO *)_pt1;
	MAP_CHAR_PARTS_SORT_INFO *pt2 = (MAP_CHAR_PARTS_SORT_INFO *)_pt2;
	short w1, h1;
	short w2, h2;
	float prio1, prio2;

	// pt1のグリッドサイズ取得
	realGetHitPoints( pt1->graNo, &w1, &h1 );
	// pt2のグリッドサイズ取得
	realGetHitPoints( pt2->graNo, &w2, &h2 );

	// pt1 が 1x1 で pt2 が 1x1 じゃない时
	if( (w1 == 1 && h1 == 1) && (w2 != 1 || h2 != 1) )
	{
		// pt1が手前の时
		if( (pt1->mx <= pt2->mx+(w2-1)*GRID_SIZE && pt1->my >= pt2->my)
		 || (pt1->mx <= pt2->mx && pt1->my >= pt2->my-(h2-1)*GRID_SIZE) )
		{
			return -1;
		}
		else
		// pt1がpt2の右侧にいる
		if( pt1->screenX > pt2->screenX )
		{
			prio1 = pt1->mx - pt1->my;
			prio2 = (pt2->mx+(w2-1)*GRID_SIZE) - pt2->my;
			// pt1が手前
			if( prio1 <= prio2 )
			{
				return -1;
			}
			else
			{
				return 1;
			}
		}
		else
		// pt1がpt2の左侧にいる
		if( pt1->screenX < pt2->screenX )
		{
			prio1 = pt1->mx - pt1->my;
			prio2 = pt2->mx - (pt2->my - (h2-1)*GRID_SIZE);
			// pt1が手前
			if( prio1 <= prio2 )
			{
				return -1;
			}
			else
			{
				return 1;
			}
		}
		else
		{
			prio1 = pt1->mx - pt1->my;
			prio2 = pt2->mx - pt2->my;
			// pt1が手前
			if( prio1 <= prio2 )
			{
				return -1;
			}
			else
			{
				return 1;
			}
		}
	}
	else
	// pt2 が 1x1 で pt1 が 1x1 じゃない时
	if( (w2 == 1 && h2 == 1) && (w1 != 1 || h1 != 1) )
	{
		// pt2が手前の时
		if( (pt2->mx <= pt1->mx+(w1-1)*GRID_SIZE && pt2->my >= pt1->my)
		 || (pt2->mx <= pt1->mx && pt2->my >= pt1->my-(h1-1)*GRID_SIZE) )
		{
			return -1;
		}
		else
		// pt2がpt1の右侧にいる
		if( pt2->screenX > pt1->screenX )
		{
			prio2 = pt2->mx - pt2->my;
			prio1 = (pt1->mx+(w1-1)*GRID_SIZE) - pt1->my;
			// pt2が手前
			if( prio1 <= prio2 )
			{
				return -1;
			}
			else
			{
				return 1;
			}
		}
		else
		// pt2がpt1の左侧にいる
		if( pt2->screenX < pt1->screenX )
		{
			prio2 = pt2->mx - pt2->my;
			prio1 = pt1->mx - (pt1->my - (h1-1)*GRID_SIZE);
			// pt2が手前
			if( prio1 <= prio2 )
			{
				return -1;
			}
			else
			{
				return 1;
			}
		}
		else
		{
			prio1 = pt1->mx - pt1->my;
			prio2 = pt2->mx - pt2->my;
			// pt1が手前
			if( prio1 <= prio2 )
			{
				return -1;
			}
			else
			{
				return 1;
			}
		}
	}
	else
	// pt2 が pt1 より登録が早ければ入れ替え
	if( pt1->no > pt2->no )
	{
		return 1;
	}

	// 入れ替え无し
	return -1;
}


// 布ツソート用
int cmpSortMapParts2( const void *_pt1, const void *_pt2 )
{
	MAP_CHAR_PARTS_SORT_INFO *pt1 = (MAP_CHAR_PARTS_SORT_INFO *)_pt1;
	MAP_CHAR_PARTS_SORT_INFO *pt2 = (MAP_CHAR_PARTS_SORT_INFO *)_pt2;

	// キャラへの插入位置でソート
	if( pt1->insert < pt2->insert )
	{
		return -1;
	}
	else
	if( pt1->insert > pt2->insert )
	{
		return 1;
	}
	else
	// pt2 が pt1 より登録が早ければ入れ替え
	if( pt1->no > pt2->no )
	{
		return 1;
	}

	// 入れ替え无し
	return -1;
}


// キャラと布ツの描画优先顺位チェック
//   戾り值：1 ... 布ツがキャラより手前
//           0 ... その逆
int checkMapCharParts( MAP_CHAR_PARTS_SORT_INFO *ptc, MAP_CHAR_PARTS_SORT_INFO *ptp )
{
	short w, h;
	float cPrio = (ptc->mx - ptc->my);
	float pPrio = (ptp->mx - ptp->my);

	// 布ツのグリッドサイズ取得
	realGetHitPoints( ptp->graNo, &w, &h );
	// 画像番号１はサイズを1x1にする
	if( realGetBitmapNo( ptp->graNo ) == 1 )
	{
		w = 1;
		h = 1;
	}

	// １ｘ１布ツとの优先顺位
	if( w == 1 && h == 1 )
	{
		// キャラが手前
		if( cPrio <= pPrio )
		{
			return 0;
		}
		else
		{
			return 1;
		}
	}
#if 0
	else
	// 布ツ内部にいる时はキャラが手前
	if( ptc->mx >= ptp->mx && ptc->mx < ptp->mx+w*GRID_SIZE
	 && ptc->my <= ptp->my && ptc->my > ptp->my-h*GRID_SIZE )
	{
		return 0;
	}
#endif
	else
	if( ptc->mx > ptp->mx && ptc->my < ptp->my )
	{
		return 1;
	}
	else
	{
		if( ptc->screenX > ptp->screenX )
		{
			// PCが建物の右侧にいる
			if( ptp->screenY-(w-1)*SURFACE_HEIGHT/2 <= ptc->screenY )
				return 0;
		}
		else
		if( ptc->screenX < ptp->screenX )
		{
			// PCが建物の左侧にいる
			if( ptp->screenY-(h-1)*SURFACE_HEIGHT/2 <= ptc->screenY )
				return 0;
		}
		else
		{
			if( ptp->screenY <= ptc->screenY )
				return 0;
		}
	}

	return 1;
}




// キャラのソート
void sortMapChar( void )
{
#if 0
	int i, max;
	float mx, my;
	float x, y;

	// 布ツのソートを正しくするためのダミーを设定
	if( mapAreaWidth >= mapAreaHeight )
	{
		max = mapAreaWidth;
	}
	else
	{
		max = mapAreaHeight;
	}
	mx = (float)mapAreaX1*GRID_SIZE;
	my = (float)(mapAreaY1+mapAreaHeight-1)*GRID_SIZE;
	for( i = 0; i < max; i++ )
	{
		// 画面表示位置
		camMapToGamen( mx, my, &x, &y );
		setMapChar( 1, mx, my, (int)x, (int)y, 0 );
		mx += (float)GRID_SIZE;
		my -= (float)GRID_SIZE;
	}
#endif

	// ソート
	qsort(	&mapCharSortInfo[0],				// 构造体のアドレス
			mapCharSortCnt,						// 比较する个数
			sizeof( MAP_CHAR_PARTS_SORT_INFO ), // 构造体のサイズ
			cmpSortMapChar 						// 比较关数へのポインタ
	);
}


// キャラソート用关数
int cmpSortMapChar( const void *_pt1, const void *_pt2 )
{
	MAP_CHAR_PARTS_SORT_INFO *pt1 = (MAP_CHAR_PARTS_SORT_INFO *)_pt1;
	MAP_CHAR_PARTS_SORT_INFO *pt2 = (MAP_CHAR_PARTS_SORT_INFO *)_pt2;
	float gPrio1 = (pt1->mx - pt1->my);
	float gPrio2 = (pt2->mx - pt2->my);


	// pt1が手前なら入れ替え无し
	if( gPrio1 < gPrio2 )
	{
		return -1;
	}
	else
	// pt2が手前なら入れ替える
	if( gPrio1 > gPrio2 )
	{
		return 1;
	}
	else
	// pt1 と pt2 が同じ位置の时
	if( pt1->mx == pt2->mx && pt1->my == pt2->my )
	{
		// pt2 がPCなら入れ替える
		if( pt2->type )
		{
			return 1;
		}
		else
		// pt1 がPCなら入れ替えない
		if( pt1->type )
		{
			return -1;
		}
		else
		// pt2 が pt1 より登録が早ければ入れ替え
		if( pt1->no > pt2->no )
		{
			return 1;
		}
	}
	else
	// pt2 が pt1 より登録が早ければ入れ替え
	if( pt1->no > pt2->no )
	{
		return 1;
	}

	// 入れ替え无し
	return -1;
}

// キャラと布ツをマージする
void margeMapCharParts( void )
{
	int i, j;

	// 布ツとキャラをマージ
#ifdef PUK2
	// 描画のプライオリティのバグを取ろうとして试したプログラム
	mapMargeCnt = 0;
	i = 0;
	j = 0;
	// キャラに插入する位置を检索
	for( ; i < mapPartsSortCnt; i++ )
	{
		for( ; j < mapCharSortCnt; j++ )
		{
			if( checkMapCharParts( &mapCharSortInfo[j], &mapPartsSortInfo[i] ) )
			{
				mapMargeInfo[mapMargeCnt++] = mapPartsSortInfo[i];
				i++;
				for( ; i < mapPartsSortCnt; i++ )
				{
					if( !checkMapCharParts( &mapCharSortInfo[j], &mapPartsSortInfo[i] ) ){
						i--;
						break;
					}
					else mapMargeInfo[mapMargeCnt++] = mapPartsSortInfo[i];
				}
				break;
			}else{
				for( ; j < mapCharSortCnt; j++ ){
					if( realGetBitmapNo( mapCharSortInfo[j].graNo ) != 1 ){
						mapMargeInfo[mapMargeCnt++] = mapCharSortInfo[j];
					}
					if (mapCharSortInfo[j].mx!=mapCharSortInfo[j+1].mx) break;
					if (mapCharSortInfo[j].my!=mapCharSortInfo[j+1].my) break;
				}
			}
		}
		if (j >= mapCharSortCnt){
			for( ; i < mapPartsSortCnt; i++ ) mapMargeInfo[mapMargeCnt++] = mapPartsSortInfo[i];
		}
	}
	if (i >= mapPartsSortCnt){
		for( ; j < mapCharSortCnt; j++ ){
			if( realGetBitmapNo( mapCharSortInfo[j].graNo ) != 1 ){
				mapMargeInfo[mapMargeCnt++] = mapCharSortInfo[j];
			}
		}
	}
#else
	mapMargeCnt = 0;
	i = 0;
	j = 0;
	while( mapMargeCnt < MAX_MAP_MARGE_INFO )
	{
		// 两方のデータがある
		if( i < mapCharSortCnt && j < mapPartsSortCnt )
		{
			if( i == mapPartsSortInfo[j].insert )
			{
				mapMargeInfo[mapMargeCnt++] = mapPartsSortInfo[j++];
			}
			else
			{
				if( realGetBitmapNo( mapCharSortInfo[i].graNo ) != 1 )
				{
					mapMargeInfo[mapMargeCnt++] = mapCharSortInfo[i++];
				}
				else
				{
					i++;
				}
			}
		}
		else
		// キャラが残ってる
		if( i < mapCharSortCnt )
		{
			if( realGetBitmapNo( mapCharSortInfo[i].graNo ) != 1 )
			{
				mapMargeInfo[mapMargeCnt++] = mapCharSortInfo[i++];
			}
			else
			{
				i++;
			}
		}
		else
		// 布ツが残ってる
		if( j < mapPartsSortCnt )
		{
			mapMargeInfo[mapMargeCnt++] = mapPartsSortInfo[j++];
		}
		// 两方ないので終わる
		else
		{
			break;
		}
	}
#endif

	i = 0;
	while( mapMargeCnt < MAX_MAP_MARGE_INFO )
	{
		if( i < mapTileSortCnt )
		{
			mapMargeInfo[mapMargeCnt++] = mapTileSortInfo[i++];
		}
		else
		{
			break;
		}
	}
}


///////////////////////////////////////////////////////////////////////////
// イベント处理
//

#if 0	// このイベントは障害物としてあたり判定であつかう
//
// NPCにぶつかったら止まるイベント
// （このイベントはプロトコルを送らない）
//
//   戾り值：TRUE  ... イベント発生
//           FALSE ... 何も无し
BOOL checkNpcEvent( int gx, int gy, int dx, int dy )
{
	int x, y;
	int ev;

	x = gx - mapAreaX1;
	y = gy - mapAreaY1;

#ifdef PUK2
	ev = (event[(y+dy)*mapAreaWidth+(x+dx)] & 0x00ff);
#else
	ev = (event[(y+dy)*mapAreaWidth+(x+dx)] & 0x0fff);
#endif
	// 移动先にNPC
	if( ev == EVENT_NPC )
	{
		// 移动を止める
		resetMap();
		return TRUE;
	}

	return FALSE;
}
#endif




///////////////////////////////////////////////////////////////////////////
// オートマッピング处理
//
void drawAutoMap( int x, int y )
{
	RECT rc;
	rc.left=0,	rc.right=300;
	rc.top=0,	rc.bottom=300;

#ifdef PUK2

	if(SugiMapStat.MapVer ==1){

		autoMappingInitFlag = TRUE;

		//新マップ时の处理
		if( autoMappingInitFlag )
		{
			createAutoMap( mapNo, mapGx, mapGy );
			autoMappingInitFlag = FALSE;
		}
		DrawAutoMapping_Sugi( x, y, (unsigned long *)NewAutoMappingBuf,
			autoMapSize[autoMapZoomFlag][0], autoMapSize[autoMapZoomFlag][1], autoMapZoomFlag );

	}else{
		//旧マップは从来どうり
		if( autoMappingInitFlag )
		{
			createAutoMap( mapNo, mapGx, mapGy );
			autoMappingInitFlag = FALSE;
		}
		DrawAutoMapping( x, y, (unsigned char *)autoMappingBuf,
			autoMapSize[autoMapZoomFlag][0], autoMapSize[autoMapZoomFlag][1], autoMapZoomFlag );
	}
#else
	if( autoMappingInitFlag )
	{
		createAutoMap( mapNo, mapGx, mapGy );
		autoMappingInitFlag = FALSE;
	}
	DrawAutoMapping( x, y, (unsigned char *)autoMappingBuf,
		autoMapSize[autoMapZoomFlag][0], autoMapSize[autoMapZoomFlag][1], autoMapZoomFlag );
#endif

}


//-------------------------------------------------------------------------//
// オートマッピングのカラーを autoMappingBuf に格纳する                    //
//-------------------------------------------------------------------------//
BOOL createAutoMap( int no, int gx, int gy )
{
	FILE *fp;
	char filename[256];
	int fWidth, fHeight, fOffset;
	int mWidth;
	int width, height;
	int fx, fy;
	int mx, my;
	int len, len2;
	int i, j;
	int x1, y1, x2, y2;
	int index;
#ifdef PUK2
	unsigned char Rwork,Gwork,Bwork;
#endif
	unsigned short tile[AUTO_MAPPING_W*AUTO_MAPPING_H];
	unsigned short parts[AUTO_MAPPING_W*AUTO_MAPPING_H];
	unsigned short event[AUTO_MAPPING_W*AUTO_MAPPING_H];

	memset( autoMappingBuf, 0, sizeof( autoMappingBuf ) );
#ifdef PUK2
	memset( NewAutoMappingBuf, 0, sizeof( NewAutoMappingBuf ) );
#endif

	// マップファイル名作成
	makeMapFileName( filename, mapKind, mapServerNo, no );

	// ファイルオープン
	if( (fp = fopen( filename, "rb" ))==NULL )                           //MLHIDE
	{
		return FALSE;
	}

	memset( tile, 0, sizeof( tile ) );
	memset( parts, 0, sizeof( parts ) );
	memset( event, 0, sizeof( event ) );


	fseek( fp, sizeof( int )*2+sizeof( char )*4, SEEK_SET );
	fread( &fWidth,  sizeof( int ), 1, fp );
	fread( &fHeight, sizeof( int ), 1, fp );

	x1 = gx - autoMapSize[autoMapZoomFlag][0]/2;
	y1 = gy - autoMapSize[autoMapZoomFlag][1]/2;
	x2 = x1 + autoMapSize[autoMapZoomFlag][0];
	y2 = y1 + autoMapSize[autoMapZoomFlag][1];


	mWidth = x2 - x1;
	width = mWidth;
	height = y2 - y1;

	mx = 0;
	fx = x1;
	if( x1 < 0 )
	{
		width += x1;
		fx = 0;
		mx -= x1;
	}
	if( x2 > fWidth )
	{
		width -= (x2 - fWidth);
	}
	my = 0;
	fy = y1;
	if( y1 < 0 )
	{
		height += y1;
		fy = 0;
		my -= y1;
	}
	if( y2 > fHeight )
	{
		height -= (y2 - fHeight);
	}


	fOffset = sizeof( int )*4 + sizeof( char )*4;
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &tile[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fOffset += sizeof( short ) * (fWidth * fHeight);
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &parts[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fOffset += sizeof( short ) * (fWidth * fHeight);
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &event[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fclose (fp);

#ifdef PUK2

	if(SugiMapStat.MapVer == 1){
		//新マップ

		//专用ファイルから読み込む
		for( i = 0; i < autoMapSize[autoMapZoomFlag][1]; i++ )
		{
			for( j = 0; j < autoMapSize[autoMapZoomFlag][0]; j++ )
			{
				// 行ったことの无い场所はオートマップに表示しない
				if( event[i*autoMapSize[autoMapZoomFlag][0]+j] & MAP_SEE_FLAG )
				{
					//オートマップ用ファイルのサイズはは399x399固定
					Rwork=SugiAutoMapDate[(y1+i)*fWidth+(x1+j)][0];
					Gwork=SugiAutoMapDate[(y1+i)*fWidth+(x1+j)][1];
					Bwork=SugiAutoMapDate[(y1+i)*fWidth+(x1+j)][2];
					NewAutoMappingBuf[i*autoMapSize[autoMapZoomFlag][0]+j] =((unsigned long)Rwork<<16)+((unsigned long)Gwork<<8)+(unsigned long)Bwork;
				}
			}
		}

	}else{
		//旧マップ

		// タイル设定
		for( i = 0; i < autoMapSize[autoMapZoomFlag][1]; i++ )
		{
			for( j = 0; j < autoMapSize[autoMapZoomFlag][0]; j++ )
			{
				// 行ったことの无い场所はオートマップに表示しない
				if( event[i*autoMapSize[autoMapZoomFlag][0]+j] & MAP_SEE_FLAG )
				{
					autoMappingBuf[i*autoMapSize[autoMapZoomFlag][0]+j] =
						autoMapColorTbl[tile[i*autoMapSize[autoMapZoomFlag][0]+j]];
				}
			}
		}
		// 布ツ设定
		for( i = 0; i < autoMapSize[autoMapZoomFlag][1]; i++ )
		{
			for( j = 0; j < autoMapSize[autoMapZoomFlag][0]; j++ )
			{
				// 行ったことの无い场所はオートマップに表示しない
				if( event[i*autoMapSize[autoMapZoomFlag][0]+j] & MAP_SEE_FLAG )
				{
					index = autoMapColorTbl[parts[i*autoMapSize[autoMapZoomFlag][0]+j]];
					if( index != 0 )
					{
						U4 bmpNo;
						S2 hit, hitX, hitY;
						int k, l;

						realGetNo( parts[i*autoMapSize[autoMapZoomFlag][0]+j], &bmpNo );
						// 当たり判定の有无
						realGetHitFlag( bmpNo, &hit );
						// 当たり判定あるならバッファに设定
						if( hit == 0 )
						{
							realGetHitPoints( bmpNo, &hitX, &hitY );
							for( k = 0; k < hitY; k++ )
							{
								for( l = 0; l < hitX; l++ )
								{
									if( (i - k) >= 0 && (j + l) < autoMapSize[autoMapZoomFlag][0] )
									{
										autoMappingBuf[(i-k)*autoMapSize[autoMapZoomFlag][0]+j+l]
											= index;
									}
								}
							}
						}
						else
						{
							autoMappingBuf[i*autoMapSize[autoMapZoomFlag][0]+j]	= index;
						}
					}
				}
			}
		}

	}

#else
	// タイル设定
	for( i = 0; i < autoMapSize[autoMapZoomFlag][1]; i++ )
	{
		for( j = 0; j < autoMapSize[autoMapZoomFlag][0]; j++ )
		{
			// 行ったことの无い场所はオートマップに表示しない
			if( event[i*autoMapSize[autoMapZoomFlag][0]+j] & MAP_SEE_FLAG )
			{
				autoMappingBuf[i*autoMapSize[autoMapZoomFlag][0]+j] =
					autoMapColorTbl[tile[i*autoMapSize[autoMapZoomFlag][0]+j]];
			}
		}
	}
	// 布ツ设定
	for( i = 0; i < autoMapSize[autoMapZoomFlag][1]; i++ )
	{
		for( j = 0; j < autoMapSize[autoMapZoomFlag][0]; j++ )
		{
			// 行ったことの无い场所はオートマップに表示しない
			if( event[i*autoMapSize[autoMapZoomFlag][0]+j] & MAP_SEE_FLAG )
			{
				index = autoMapColorTbl[parts[i*autoMapSize[autoMapZoomFlag][0]+j]];
				if( index != 0 )
				{
					U4 bmpNo;
					S2 hit, hitX, hitY;
					int k, l;

					realGetNo( parts[i*autoMapSize[autoMapZoomFlag][0]+j], &bmpNo );
					// 当たり判定の有无
					realGetHitFlag( bmpNo, &hit );
					// 当たり判定あるならバッファに设定
					if( hit == 0 )
					{
						realGetHitPoints( bmpNo, &hitX, &hitY );
						for( k = 0; k < hitY; k++ )
						{
							for( l = 0; l < hitX; l++ )
							{
								if( (i - k) >= 0 && (j + l) < autoMapSize[autoMapZoomFlag][0] )
								{
									autoMappingBuf[(i-k)*autoMapSize[autoMapZoomFlag][0]+j+l]
										= index;
								}
							}
						}
					}
					else
					{
						autoMappingBuf[i*autoMapSize[autoMapZoomFlag][0]+j]	= index;
					}
				}
			}
		}
	}
#endif

	return TRUE;
}


//-------------------------------------------------------------------------//
// 步いた所のフラグを読みこむ                                              //
//-------------------------------------------------------------------------//
void readAutoMapSeeFlag( void )
{
	FILE *fp;
	char filename[256];
	int fWidth, fHeight, fOffset;
	int mWidth;
	int width, height;
	int fx, fy;
	int mx, my;
	int len, len2;
	int i, j;
	int x1, y1, x2, y2;


	// マップファイル名作成
	makeMapFileName( filename, mapKind, mapServerNo, mapNo );

	// ファイルオープン
	if( (fp = fopen( filename, "rb" ))==NULL )                           //MLHIDE
	{
		return;
	}

	memset( autoMapSeeFlagBuf, 0, sizeof( autoMapSeeFlagBuf ) );

	fseek( fp, sizeof( int )*2+sizeof( char )*4, SEEK_SET );
	fread( &fWidth,  sizeof(int), 1, fp );
	fread( &fHeight, sizeof(int), 1, fp );

	x1 = mapGx - AUTO_MAPPING_SEE_W/2;
	y1 = mapGy - AUTO_MAPPING_SEE_H/2;
	x2 = x1 + AUTO_MAPPING_SEE_W;
	y2 = y1 + AUTO_MAPPING_SEE_H;


	mWidth = x2 - x1;
	width = mWidth;
	height = y2 - y1;

	mx = 0;
	fx = x1;
	if( x1 < 0 )
	{
		width += x1;
		fx = 0;
		mx -= x1;
	}
	if( x2 > fWidth )
	{
		width -= (x2 - fWidth);
	}
	my = 0;
	fy = y1;
	if( y1 < 0 )
	{
		height += y1;
		fy = 0;
		my -= y1;
	}
	if( y2 > fHeight )
	{
		height -= (y2 - fHeight);
	}


	fOffset = sizeof( int ) * 4 + sizeof( char ) * 4 + sizeof( short ) * (fWidth * fHeight) * 2;
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fread( &autoMapSeeFlagBuf[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fclose (fp);

	// 通った所にフラグを立ててオートマップに表示されるようにする
	for( i = 0; i < AUTO_MAPPING_SEE_H; i++ )
	{
		for( j = 0; j < AUTO_MAPPING_SEE_W; j++ )
		{
			autoMapSeeFlagBuf[i*AUTO_MAPPING_SEE_W+j] |= MAP_SEE_FLAG;
		}
	}

	autoMapSeeFloor = mapNo;
	autoMapSeeGx = mapGx;
	autoMapSeeGy = mapGy;

	autoMapSeeFlag = TRUE;
}


//-------------------------------------------------------------------------//
// 步いた所のフラグを书き込む                                              //
//-------------------------------------------------------------------------//
void writeAutoMapSeeFlag( void )
{
	FILE *fp;
	char filename[256];
	int fWidth, fHeight, fOffset;
	int mWidth;
	int width, height;
	int fx, fy;
	int mx, my;
	int len, len2;
	int i;
	int x1, y1, x2, y2;


	// データがバッファに无いなら何もしない。
	if( !autoMapSeeFlag )
		return;

	autoMapSeeFlag = FALSE;

	// マップファイル名作成
	makeMapFileName( filename, mapKind, mapServerNo, autoMapSeeFloor );

	// ファイルオープン
	if( (fp = fopen( filename, "rb+" ))==NULL )                          //MLHIDE
	{
		return;
	}


	fseek( fp, sizeof( int )*2 + sizeof( char )*4, SEEK_SET );
	fread( &fWidth,  sizeof(int), 1, fp );
	fread( &fHeight, sizeof(int), 1, fp );

	x1 = autoMapSeeGx - AUTO_MAPPING_SEE_W/2;
	y1 = autoMapSeeGy - AUTO_MAPPING_SEE_H/2;
	x2 = x1 + AUTO_MAPPING_SEE_W;
	y2 = y1 + AUTO_MAPPING_SEE_H;

	mWidth = x2 - x1;
	width = mWidth;
	height = y2 - y1;

	mx = 0;
	fx = x1;
	if( x1 < 0 )
	{
		width += x1;
		fx = 0;
		mx -= x1;
	}
	if( x2 > fWidth )
	{
		width -= (x2 - fWidth);
	}
	my = 0;
	fy = y1;
	if( y1 < 0 )
	{
		height += y1;
		fy = 0;
		my -= y1;
	}
	if( y2 > fHeight )
	{
		height -= (y2 - fHeight);
	}

	fOffset = sizeof( int ) * 4 + sizeof( char ) * 4 + sizeof( short ) * (fWidth * fHeight) * 2;
	len = fy * fWidth + fx;
	len2 = my * mWidth + mx;
	for( i = 0; i < height; i++ )
	{
		fseek( fp, sizeof(short)*len+fOffset, SEEK_SET );
		fwrite( &autoMapSeeFlagBuf[len2], sizeof(short)*width, 1, fp );
		len  += fWidth;
		len2 += mWidth;
	}

	fclose (fp);
}



///////////////////////////////////////////////////////////////////////////
// マップキャッシュ处理
#ifdef MAP_CACHE_PROC


// 初期化
void initMapCache( void )
{
	int i;

	for( i = 0; i < MAX_MAP_CACHE_SIZE; i++ )
	{
		mapCacheFloorNo[i] = -1;
		mapCacheFloorGxSize[i] = 0;
		mapCacheFloorGySize[i] = 0;
		clearMapCacheFlag( i );
		mapCacheLastTime[i] = 0;
	}

	mapCacheUse = 0;
}


// 移动先のマップがキャッシュされているかチェック
BOOL checkMapCache( int gx, int gy, short *buf, int size, int floor, int gxSize, int gySize )
{
	#define MAP_CACHE_RANGE		14
	#define MAP_CACHE_RANGE2	13
	int x1[2], y1[2], x2[2], y2[2];
	int tx1[2] = { 0xffff, 0xffff };
	int ty1[2] = { 0xffff, 0xffff };
	int tx2[2] = { 0, 0 };
	int ty2[2] = { 0, 0 };
	int dir;
	int dx, dy;
	int cnt;
	unsigned char bit;
	unsigned char tbit;
	int i;
	int mask;


	// 移动によって必要になるマップの范围を计算する
	tbit = 0;
	for( cnt = 0; cnt < size; cnt++ )
	{
		dir = buf[cnt];

		bit = 0;
		if( 0 <= dir && dir <= 2 )
		{
			x1[0] = gx - MAP_CACHE_RANGE;
			x2[0] = x1[0] + 1;
			y1[0] = gy - MAP_CACHE_RANGE2;
			y2[0] = gy + MAP_CACHE_RANGE2 + 1;
			bit |= 1;
		}
		else
		if( 4 <= dir && dir <= 6 )
		{
			x1[0] = gx + MAP_CACHE_RANGE;
			x2[0] = x1[0] + 1;
			y1[0] = gy - MAP_CACHE_RANGE2;
			y2[0] = gy + MAP_CACHE_RANGE2 + 1;
			bit |= 1;
		}

		if( 2 <= dir && dir <= 4 )
		{
			x1[1] = gx - MAP_CACHE_RANGE2;
			x2[1] = gx + MAP_CACHE_RANGE2 + 1;
			y1[1] = gy - MAP_CACHE_RANGE;
			y2[1] = y1[1] + 1;
			bit |= 2;
		}
		else
		if( 6 <= dir && dir <= 7 || dir == 0 )
		{
			x1[1] = gx - MAP_CACHE_RANGE2;
			x2[1] = gx + MAP_CACHE_RANGE2 + 1;
			y1[1] = gy + MAP_CACHE_RANGE;
			y2[1] = y1[1] + 1;
			bit |= 2;
		}


		if( (bit & 1) != 0 )
		{
			if( x1[0] < tx1[0] )
				tx1[0] = x1[0];
			if( x2[0] > tx2[0] )
				tx2[0] = x2[0];
			if( y1[0] < ty1[0] )
				ty1[0] = y1[0];
			if( y2[0] > ty2[0] )
				ty2[0] = y2[0];
			tbit |= 1;
		}

		if( (bit & 2) != 0 )
		{
			if( x1[1] < tx1[1] )
				tx1[1] = x1[1];
			if( x2[1] > tx2[1] )
				tx2[1] = x2[1];
			if( y1[1] < ty1[1] )
				ty1[1] = y1[1];
			if( y2[1] > ty2[1] )
				ty2[1] = y2[1];
			tbit |= 2;
		}

		dx = moveAddTbl[dir][0];
		dy = moveAddTbl[dir][1];
		gx += dx;
		gy += dy;
	}

	// マップの范围がマップ内にあるかチェック
	bit = 0;
	for( i = 0, mask = 1; i < 2; i++, mask <<= 1 )
	{
		if( (tbit & mask) != 0 )
		{
			if( checkMapCacheFlag( floor, gxSize, gySize, tx1[i], ty1[i], tx2[i], ty2[i] ) )
			{
				if( !checkMapCacheEvent( tx1[i], ty1[i], tx2[i], ty2[i] ) )
				{
					bit |= mask;
				}
			}
		}
	}

	if( tbit == bit )
	{
		return TRUE;
	}
	else
	{
		return FALSE;
	}
}


// フラグのクリア
void clearMapCacheFlag( int no )
{
	if( no < 0 || MAX_MAP_CACHE_SIZE <= no )
		return;
	memset( mapCacheFlag[no], 0, MAP_CACHE_X_BYTE*MAP_CACHE_Y_BYTE );
}


// 指定位置のフラグをチェックする
//  戾り值：TRUE  ... キャッシュにある
//			FALSE ... キャッシュにない
BOOL checkMapCacheFlag( int floor, int gxSize, int gySize,
	int x1, int y1, int x2, int y2 )
{
	int xByteStart, xByteEnd;
	int yByteStart, yByteEnd;
	unsigned char bits;
	unsigned char mask;
	int i, j;
	BOOL ret = TRUE;
	BOOL thisFloorCacheFlag = FALSE;

	// 指定のフロア番号があるか调べる
	j = mapCacheUse;
	for( i = 0; i < MAX_MAP_CACHE_SIZE; i++ )
	{
		if( mapCacheFloorNo[j] == floor )
		{
			// フロアがあった
			mapCacheUse = j;
			thisFloorCacheFlag = TRUE;
			break;
		}

		j++;
		if( j >= MAX_MAP_CACHE_SIZE )
			j = 0;
	}

	// キャッシュに无い时
	if( !thisFloorCacheFlag )
	{
		BOOL flag;
		unsigned int tmpTime;

		// マップサイズが１００ｘ１００未满はキャッシュしない
		if( gxSize < 100 || gySize < 100 )
		{
			return FALSE;
		}

		// キャッシュするので空いてるところを探す
		flag = FALSE;
		for( i = 0; i < MAX_MAP_CACHE_SIZE; i++ )
		{
			if( mapCacheFloorNo[i] == -1 )
			{
				flag = TRUE;
				break;
			}
		}
		// 空きが无いので一番古いキャッシュを消す
		if( !flag )
		{
			i = 0;
			tmpTime = 0xffffffff;
			for( j = 0; j < MAX_MAP_CACHE_SIZE; j++ )
			{
				if( tmpTime > mapCacheLastTime[j] )
				{
					i = j;
					tmpTime = mapCacheLastTime[j];
				}
			}
		}

		// キャッシュ登録
		mapCacheUse = i;
		mapCacheFloorNo[i] = floor;
		mapCacheFloorGxSize[i] = gxSize;
		mapCacheFloorGySize[i] = gySize;
		clearMapCacheFlag( i );
		ret = FALSE;
	}

	// キャッシュへのアクセス时间登録
	mapCacheLastTime[mapCacheUse] = GetTickCount();


	if( x1 < 0 )
	{
		x1 = 0;
	}
	if( y1 < 0 )
	{
		y1 = 0;
	}
	if( x2 > mapCacheFloorGxSize[mapCacheUse] )
	{
		x2 = mapCacheFloorGxSize[mapCacheUse];
	}
	if( y2 > mapCacheFloorGySize[mapCacheUse] )
	{
		y2 = mapCacheFloorGySize[mapCacheUse];
	}

	// マップ范围外
	if( x1 > x2 || y1 > y2 )
		return FALSE;

	xByteStart = x1/8;
	xByteEnd = x2/8;
	yByteStart = y1;
	yByteEnd = y2;

	for( i = xByteStart; i <= xByteEnd; i++ )
	{
		bits = 0xff;
		if( i == xByteStart )
		{
			mask = ((unsigned char)0xff >> (x1 % 8));
			bits &= mask;
		}
		if( i == xByteEnd )
		{
			mask = ((unsigned char)0xff << (7 - (x1 % 8)));
			bits &= mask;
		}
		for( j = yByteStart; j <= yByteEnd; j++ )
		{
			if( (mapCacheFlag[mapCacheUse][j*MAP_CACHE_X_BYTE+i] & bits) != bits )
			{
				// まだ来たことの无い所だった。
				ret = FALSE;
			}
			mapCacheFlag[mapCacheUse][j*MAP_CACHE_X_BYTE+i] |= bits;
		}
	}

	return ret;
}


// イベント情报内にEVENT_ALTERRATIVEがある时、TRUEを返す。
BOOL checkMapCacheEvent( int x1, int y1, int x2, int y2 )
{
	int x, y;
	int w, h;
	int i, j;

	if( x1 >= mapAreaX2 || x2 < 0
	 || y1 >= mapAreaY2 || y2 < 0 )
	{
		// チェックする场所が范围外ならＴＲＵＥを返して
		// サーバにチェックサムを要求する
		return TRUE;
	}

	w = x2 - x1;
	h = y2 - y1;
	x = x1 - mapAreaX1;
	y = y1 - mapAreaY1;

	if( x < 0 )
	{
		w += x;
		x = 0;
	}
	if( y < 0 )
	{
		h += y;
		y = 0;
	}
	if( mapAreaX1+w > mapAreaX2 )
	{
		w -= (mapAreaX1+w-mapAreaX2);
	}
	if( mapAreaY1+h > mapAreaY2 )
	{
		h -= (mapAreaY1+h-mapAreaY2);
	}

	for( i = 0; i < h; i++, y++ )
	{
		for( j = 0; j < w; j++, x++ )
		{
#ifdef PUK2
			if( (event[y*mapAreaWidth+x] & 0x00ff) == EVENT_ALTERRATIVE )
#else
			if( (event[y*mapAreaWidth+x] & 0x0fff) == EVENT_ALTERRATIVE )
#endif
			{
				return TRUE;
			}
		}
	}

	return FALSE;
}


#endif




//-------------------------------------------------------------------------//
// 相对座标から向きを算出                                                  //
//-------------------------------------------------------------------------//
int getDirFromXY( float x, float y )
{
	float tdir;

	tdir = Atan( x, y ) + 22.5F + 45.0F * 2;
	AdjustDir( &tdir );
	return (int)(tdir/45);
}


//-------------------------------------------------------------------------//
// マップの座标（グリッドではない）より画面座标にする。                    //
//-------------------------------------------------------------------------//
void camMapToGamen( float sx , float sy , float *ex , float *ey )
{
	float x0, y0;
	float x, y;
	float tx = SURFACE_WIDTH/2;
	float ty = SURFACE_HEIGHT/2;

	x0 = ( sx - viewPointX ) / GRID_SIZE; 
	y0 = ( sy - viewPointY ) / GRID_SIZE; 

	x = + x0 * tx  +   y0 * tx ;
	y = - x0 * ty  +   y0 * ty ;

	//画面座标
	*ex = x + SCREEN_WIDTH_CENTER;
	*ey = y + SCREEN_HEIGHT_CENTER;
}


//-------------------------------------------------------------------------//
// 画面座标よりマップの座标(グリッドではない)にする。                      //
//-------------------------------------------------------------------------//
void camGamenToMap( float sx , float sy , float *ex , float *ey )
{
	float x0, y0;
	float x, y;

	// 画面座标
	x0 = sx - SCREEN_WIDTH_CENTER;
	y0 = sy - SCREEN_HEIGHT_CENTER;

	x = x0 - (float)SURFACE_WIDTH / (float)SURFACE_HEIGHT * y0;
	y = x0 + (float)SURFACE_WIDTH / (float)SURFACE_HEIGHT * y0;

	*ex = x + viewPointX;
	*ey = y + viewPointY;
}


// マップ座标ハック对策チェック（变更前）
void CheckMapHackStart( void )
{
	// Ｘ＋Ｙの值をチェック
	if( mapGx + mapGy != mapHackCheck ){
		//MessageBox( hWnd, "外挂？", "注意！",MB_OK );
		nrproto_CH_send( sockfd, 0 );
	}
}

// マップ座标ハック对策チェック（变更后）
void CheckMapHackEnd( void )
{
	// Ｘ＋Ｙの值を记忆する
	mapHackCheck = mapGx + mapGy;
}


#ifdef EXPAND_GRAPHICID
//----------------------------------------------------------------
//  タイルや布ツの番号を受け取って、表示するための画像番号を返す
//----------------------------------------------------------------
int TileNoConvert( int no ){
	// 昔のタイル番号だったらそのまま返してよい。
	// 变换テーブルから取得する。
	if( no >= arraysizeof( TileConvertTbl ) )return no;
	if( no < 0 )return 0;
	return TileConvertTbl[no];
}

//----------------------------------------------------------------
//  タイルや布ツの番号に对应した画像番号テーブルを作成する
//----------------------------------------------------------------
void MakeTileConvertTable( void ){
	int i = 0, k;
	
	// ０から19999まではそのまま
	for( i = 0; i <= TILEPARTS_NORMAL_END; i ++ ){
		// 昔のタイル番号だったらそのまま返してよい。
		TileConvertTbl[i] = i;
	}
	k = arraysizeof( TileConvertTbl );
	for( ; i < k; i ++ ){
		// 新しい番号だったら２０万プラスして返す
		TileConvertTbl[i] = i + TILEPARTS_EX_OFFSET;
	}
}

#endif

#ifdef PUK2
#include"../puk2/map/newmap.cpp"
#endif
