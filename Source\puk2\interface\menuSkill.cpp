﻿//メニュー＞スキル

// スキル结果の音
#define SE_NO_SKILL_SUCCESS		SE_NO_CLOSE_WINDOW
#define SE_NO_SKILL_FAILED		SE_NO_PAGE_CHANGE

#define DECORATIONFP 100

#ifdef PUK3_MONSTER_HELPER
int CheckBattlePet( void );
#endif
extern char ItemNoOpe[MAX_ITEM];

int GetCharaEmptyGra( int graNo, int weapon );
extern unsigned int CategoryDExtraTime;

static int CreateSkillNo;
static int CreateRecipeNo;
static int CreateSelTech;

static char SkillGatherSkillNo;
static char SkillGatherSelTech;

static int CategoryCSkillNo;
static int CategoryCSelTech;

static int RebirthOKCnt = 0;
static char RebirthOffCnt = 0;
static char SkillMoveOffCnt = 0;
static char SkillIconCnt = 0;

static ACTION *pActCreate;
static ACTION *pActGather;

extern int itemInfoColor;


void SkillWindowInit()
{
	pActCreate = NULL;
	pActGather = NULL;

	RebirthOKCntInit();
	RebirthOffCntInit();
	SkillMoveOffCntInit();
	SkillIconCnt = 0;
}

void RebirthOKCntInit()
{
	RebirthOKCnt = 0;
}

void RebirthOKCntInc()
{
	RebirthOKCnt++;
}

void RebirthOKCntDec()
{
	RebirthOKCnt--;
	if (!RebirthOKCnt){
		if (skillrebirth){
			nrproto_AC_send( sockfd, mapGx, mapGy, 62 );		//リバース中止
			skillrebirth=0;
			ItemNoOpe[7]--;
			// レシピ情报の再取得
			regetRecipe();
		}
	}
}

void RebirthOffCntInit()
{
	RebirthOffCnt = 0;
}

void RebirthOffCntInc()
{
////	if (!RebirthOffCnt) ItemNoOpe[7]++;
	RebirthOffCnt++;
}

void RebirthOffCntDec()
{
	RebirthOffCnt--;
////	if (!RebirthOffCnt) ItemNoOpe[7]--;
}

void SkillMoveOffCntInit()
{
	SkillMoveOffCnt = 0;
}

#ifdef PUK2_NEWDRAG

void DrawSkillTitle( short x, short y, char mouse, int Num, BOOL usable, int FontPrio, int DispPrio, unsigned long rgba );

void DragSkillFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// 右クリックしたらアイテムドロップ
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_SKILL, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragSkillFunc );
		}
		// 右键したらドラッグ終了
		if ( mouse.onceState & MOUSE_LEFT_CRICK ) WinDD_DragCancel();

#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		DrawSkillTitle( mouse.nowPoint.x-103, mouse.nowPoint.y-8, 2, itemNo, TRUE, FONT_PRIO_DRAG, DISP_PRIO_DRAG, 0x80ffffff );
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
		break;
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		break;
	}
}

void DragSkill( int itemNo )
{
	// ドラッグ开始
	WinDD_DragStart( WINDD_SKILL, (void *)itemNo, DragSkillFunc );
}

#endif

void swapRebirthItemRelation( int from, int to )
{
	if (skillrebirth){
		if ( from == 7 ){
			ItemNoOpe[from]++;
			ItemNoOpe[to]--;
		}
		if ( to == 7 ){
			ItemNoOpe[to]++;
			ItemNoOpe[from]--;
		}
	}
}

#ifdef PUK3_MONSTER_HELPER

#define USE_REBIRTH_ADD 100
#define USE_PETHELP_ADD 200

#define CREATERATE_MAXLEVEL 10

// モンクリ地の范围
#define MONSTERCRYSTAL_EARTH_MIN 9401
#define MONSTERCRYSTAL_EARTH_MAX 9410
// モンクリ水の范围
#define MONSTERCRYSTAL_WATER_MIN 9411
#define MONSTERCRYSTAL_WATER_MAX 9420
// モンクリ火の范围
#define MONSTERCRYSTAL_FIRE__MIN 9421
#define MONSTERCRYSTAL_FIRE__MAX 9430
// モンクリ风の范围
#define MONSTERCRYSTAL_WIND__MIN 9431
#define MONSTERCRYSTAL_WIND__MAX 9440

BOOL usePetHelp;
int petHelperGraNo;

// リバース等级による、生产时间短缩の割合
char RebirthCreateRate[CREATERATE_MAXLEVEL]={ 100, 95, 90, 85, 80, 75, 65, 60, 55, 50 };

// ペットお手伝いによる、生产时间短缩の割合(等级０はペットヘルプ非発动时)
char PetHelpCreateRate1[CREATERATE_MAXLEVEL+1]={ 100, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 };
char *PetHelpCreateRate[3]={
	PetHelpCreateRate1,			// 生产
	PetHelpCreateRate1,			// 修理??鉴定
	PetHelpCreateRate1,			// 采取
};

// ＰＣがライダーを覚えているかチェック
BOOL CheckPCHaveRiderSkill()
{
	int i;

	for(i=0;i<MAX_SKILL;i++){
		if ( !job.sortSkill[i].useFlag ) continue;

		if ( job.skill[ job.sortSkill[i].index ].id == 1009 ) return TRUE;
	}

	return FALSE;
}

// モンクリの等级取得
int GetPCMonsterCrystalLevel()
{
	if ( !pc.item[7].useFlag ) return 0;

	// モンクリ地の范围
	if ( MONSTERCRYSTAL_EARTH_MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_EARTH_MAX ){
		return( (pc.item[7].id - MONSTERCRYSTAL_EARTH_MIN) + 1 );
	}
	// モンクリ水の范围
	if ( MONSTERCRYSTAL_WATER_MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_WATER_MAX ){
		return( (pc.item[7].id - MONSTERCRYSTAL_WATER_MIN) + 1 );
	}
	// モンクリ火の范围
	if ( MONSTERCRYSTAL_FIRE__MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_FIRE__MAX ){
		return( (pc.item[7].id - MONSTERCRYSTAL_FIRE__MIN) + 1 );
	}
	// モンクリ风の范围
	if ( MONSTERCRYSTAL_WIND__MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_WIND__MAX ){
		return( (pc.item[7].id - MONSTERCRYSTAL_WIND__MIN) + 1 );
	}

	return 0;
}

// モンクリの属性取得
int GetPCMonsterCrystalAttribute()
{
	if ( !pc.item[7].useFlag ) return 0;

	// モンクリ地の范围
	if ( MONSTERCRYSTAL_EARTH_MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_EARTH_MAX ){
		return 0;
	}
	// モンクリ水の范围
	if ( MONSTERCRYSTAL_WATER_MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_WATER_MAX ){
		return 1;
	}
	// モンクリ火の范围
	if ( MONSTERCRYSTAL_FIRE__MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_FIRE__MAX ){
		return 2;
	}
	// モンクリ风の范围
	if ( MONSTERCRYSTAL_WIND__MIN <= pc.item[7].id &&
		 pc.item[7].id <= MONSTERCRYSTAL_WIND__MAX ){
		return 3;
	}

	return -1;
}

// ペットヘル布のグラフィック取得(お手伝いが出来るかの判定は行ってません)
int GetPetHelperGraNo()
{
	int petIndex;

	petIndex = CheckBattlePet();
	if ( petIndex < 0 ) return 0;

	return pet[petIndex].graNo;
}

// ペットがギャリバー覚えてるかチェック
BOOL CheckPetHaveHelpSkill( int petIndex )
{
	int index;
	int i;

	// スキル分ループ
	for( i = 0 ; i < pet[petIndex].maxTech; i++ ){
		// ソート番号取り出し
		index = pet[petIndex].sortTech[i].index;
		// 文字列あるとき
		if( pet[petIndex].tech[index].name[0]=='\0' ) continue;

		// ギャリバーもってるなら
		if ( 40330 <= pet[petIndex].tech[index].techId &&
			 pet[petIndex].tech[index].techId <= 40332 ) return TRUE;
	}
	return FALSE;
}

const int pethelppetlevel[10][2] = {
	{ 1,15},{ 9,22},{16,29},{23,36},{30,43},
	{37,50},{44,57},{51,64},{58,70},{65,1000/*上限无いので适当に大きい数字*/},
};
// ペットが行うペットヘルプのランク
BOOL CheckPetHelpPetLevel( int petIndex, int rank )
{
	if ( pet[petIndex].lv < pethelppetlevel[rank-1][0] ) return FALSE;
	if ( pethelppetlevel[rank-1][1] < pet[petIndex].lv ) return FALSE;

	return TRUE;
}

// ペットの魔力が足りているかをチェック
const int pethelpUseFp[CREATERATE_MAXLEVEL] = { 1, 1, 2, 2, 3, 3, 4, 4, 5, 5 };
BOOL CheckPetHelpNeedFp()
{
	int petIndex, level;

	petIndex = CheckBattlePet();
	if ( petIndex < 0 ) return FALSE;
	level = GetPCMonsterCrystalLevel();
	if ( !(1 <= level && level <= CREATERATE_MAXLEVEL) ) return FALSE;

	if ( pet[petIndex].fp < pethelpUseFp[ level-1 ] ) return FALSE;

	return TRUE;
}

// ペットがお手伝い可能かをチェック
BOOL CheckPetHelp()
{
	int petIndex;
	int crystalLevel;

	// ＰＣがライダーを习得しているか？
	if ( !CheckPCHaveRiderSkill() ) return FALSE;

	// ＰＣがモンスタークリスタルを装备しているか？
	crystalLevel = GetPCMonsterCrystalLevel();
	if ( crystalLevel < 1 ) return FALSE;

	// ペットが战闘状态か？
	petIndex = CheckBattlePet();
	if ( petIndex < 0 ) return FALSE;

	// ペットがギャリバーをもってるか？
	if ( !CheckPetHaveHelpSkill( petIndex ) ) return FALSE;

	// モンスタークリスタルの属性がペットの属性と近似しているか？
	if ( pet[petIndex].attr[ GetPCMonsterCrystalAttribute() ] < 50 ) return FALSE;

	// モンスタークリスタルの等级がペットの等级と近似しているか？
	if ( !CheckPetHelpPetLevel( petIndex, crystalLevel ) ) return FALSE;

	return TRUE;
}

#else
// リバース等级による、生产时间短缩の割合
char RebirthCreateRate[]={ 100, 95, 90, 85, 80, 75, 70 };
#endif

//====================================//
//				メイン				  //
//====================================//

int CheckBattlePet( void );


static int SkillProcNo;

static int AbirityNo;
static int SelLine;

static int DispStart;
static int DispMax;
static int InfoNo;

static char BattleSkillIndex[MAX_SKILL];

static void (*ReturnFunc)( int lSkill, int lLevel );
static void (*PetReturnFunc)( int lSkill );

//========================================
// フィールド用
//========================================
void ModeChangeJobTitle( WINDOW_INFO *wi, int nowpos )
{
	int i;

	for(i=0;i<MAX_SKILL;i++){
		if( job.sortSkill[i].index == nowpos ) break;
	}
	if ( i >= MAX_SKILL ) i = 0;

	SkillProcNo = 0;
	DispStart = i+1 - 10;
	if ( DispStart < 0 ) DispStart = 0;
	if ( nowpos < 0 ) DispStart = 0;

	// つまみを移动
	NumToScrollVMove( &wi->sw[EnumBtSkillScroll], DispMax, DispStart );

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillJobTitle].Enabled = TRUE;
	wi->sw[EnumGraphSkillSlotCount].Enabled = TRUE;

	DispMax=0;
	for(i=0;i<MAX_SKILL;i++){
		if( job.sortSkill[i].useFlag ) DispMax++;
	}
	DispMax-=10;
	if ( DispMax < 0 ) DispMax = 0;
	for(i=EnumGraphSkillScroll;i<=EnumBtSkillScrollWheel;i++) wi->sw[i].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_SkillJobBase;

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchSkillSkillPanel;
	}
}

void ModeChangeSkillLv( WINDOW_INFO *wi, int Index )
{
	int i;

	SkillProcNo = 1;
	DispStart = 0;

	if (Index>=0) AbirityNo = job.sortSkill[Index].index;

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillName].Enabled = TRUE;
	wi->sw[EnumGraphSkillBack].Enabled = TRUE;
	wi->sw[EnumGraphSkillExp].Enabled = TRUE;
	wi->sw[EnumGraphSkillLv].Enabled = TRUE;
	wi->sw[EnumGraphSkillRebirth].Enabled = FALSE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_Skill__Base;

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchSkillNamePanel;
	}
}

void ModeChangeSkillRecipe( WINDOW_INFO *wi, int Index )
{
	int i;

	if (Index>=0) SelLine = Index;

	SkillProcNo = 2;
	DispStart = 0;

	// つまみを移动
	NumToScrollVMove( &wi->sw[EnumBtSkillScroll], DispMax, DispStart );

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillName].Enabled = TRUE;
	wi->sw[EnumGraphSkillBack].Enabled = TRUE;
	wi->sw[EnumGraphSkillLv].Enabled = TRUE;

	for(DispMax=0;;DispMax++){
		if( job.skill[AbirityNo].recipe[DispMax].id < 0 ) break;
	}
	DispMax-=10;

	for(i=EnumGraphSkillScroll;i<=EnumBtSkillScrollWheel;i++) wi->sw[i].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_SkillLvBase;

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchRecipeNamePanel;
	}
}

//========================================
// 战闘用
//========================================
void ModeChangeBattleJobTitle( WINDOW_INFO *wi, int nowpos )
{
	int i,j;

	for(i=0;i<MAX_SKILL;i++){
		if( job.sortSkill[i].index == nowpos ) break;
	}
	if ( i >= MAX_SKILL ) i = 0;

	SkillProcNo = 3;
	DispStart = i+1 - 10;
	if ( DispStart < 0 ) DispStart = 0;
	if ( nowpos < 0 ) DispStart = 0;

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillJobTitle].Enabled = TRUE;
	wi->sw[EnumGraphSkillSlotCount].Enabled = TRUE;

#ifdef PUK2_MAXSKILLSLOT_UP
	for(i=EnumGraphSkillScroll;i<=EnumBtSkillScrollWheel;i++) wi->sw[i].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_SkillJobBase;

	j=0;
	for(i=0;i<MAX_SKILL;i++){
		if ( job.sortSkill[i].useFlag ){
#ifdef PUK3_RIDE
			if ( job.skill[ job.sortSkill[i].index ].operationCategory == 0 ||
				 job.skill[ job.sortSkill[i].index ].operationCategory == 5 ){
#else
			if (job.skill[ job.sortSkill[i].index ].operationCategory==0){
#endif
				BattleSkillIndex[j] = i;
				j++;
			}
		}
	}
	for(;j<MAX_SKILL;j++) BattleSkillIndex[j] = -1;

	DispMax=0;
	for(i=0;i<MAX_SKILL;i++){
		if ( BattleSkillIndex[j] < 0 ) continue;
		if ( job.sortSkill[ BattleSkillIndex[i] ].useFlag ) DispMax++;
	}
	DispMax-=10;
	if ( DispMax < 0 ) DispMax = 0;
#else
	DispMax=0;
	for(i=0;i<MAX_SKILL;i++){
		if( job.sortSkill[i].useFlag ) DispMax++;
	}
	DispMax-=10;
	if ( DispMax < 0 ) DispMax = 0;
	for(i=EnumGraphSkillScroll;i<=EnumBtSkillScrollWheel;i++) wi->sw[i].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_SkillJobBase;

	j=0;
	for(i=0;i<MAX_SKILL;i++){
		if ( job.sortSkill[i].useFlag ){
#ifdef PUK3_RIDE
			if ( job.skill[ job.sortSkill[i].index ].operationCategory == 0 ||
				 job.skill[ job.sortSkill[i].index ].operationCategory == 5 ){
#else
			if (job.skill[ job.sortSkill[i].index ].operationCategory==0){
#endif
				BattleSkillIndex[j] = i;
				j++;
			}
		}
	}
	for(;j<MAX_SKILL;j++) BattleSkillIndex[j] = -1;
#endif

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchSkillBattleJobPanel;
	}
}

void ModeChangeBattleSkillName( WINDOW_INFO *wi, int Index, char flag )
{
	int i;

	SkillProcNo = 4;
	DispStart = 0;

	if (flag) AbirityNo = Index;
	else{
		if (Index>=0) AbirityNo = job.sortSkill[Index].index;
	}

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillName].Enabled = TRUE;
	wi->sw[EnumGraphSkillBack].Enabled = TRUE;
	wi->sw[EnumGraphSkillExp].Enabled = TRUE;
	wi->sw[EnumGraphSkillLv].Enabled = TRUE;
	if ( job.skill[AbirityNo].operationCategory ) wi->sw[EnumGraphSkillRebirth].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_Skill__Base;

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchSkillBattleNamePanel;
	}
}

void regetRecipe()
{
	if ( WindowFlag[MENU_WINDOW_SKILL].wininfo && SkillProcNo == 2 ){
		if ( (job.skill[AbirityNo].trn!=0)!=(skillrebirth!=0) ){
#ifdef PUK3_MONSTER_HELPER
			nrproto_IR_send( sockfd, AbirityNo+(skillrebirth?USE_REBIRTH_ADD:0) );
#else
			nrproto_IR_send( sockfd, AbirityNo+(skillrebirth?100:0) );
#endif
		}
	}
}

//========================================
// 战闘用ペットスキル
//========================================
void ModeChangeBattlePetSkill( WINDOW_INFO *wi )
{
	int i;
	int no;
	int index;
	int SkillCnt = 0, PassCnt = 0;

	SkillProcNo = 5;
	DispStart = 0;

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillName].Enabled = TRUE;

	DispMax = 0;
	for(i=EnumGraphSkillScroll;i<=EnumBtSkillScrollWheel;i++) wi->sw[i].Enabled = TRUE;

	no = CheckBattlePet();

	if (no<0) return;

	if (pet[no].freeName[0]=='\0'){
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillName].Switch )->text, pet[no].name );
	}else{
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillName].Switch )->text, pet[no].freeName );
	}

	// スキル分ループ
	for( i = 0 ; i < pet[no].maxTech; i++ ){
		// ソート番号取り出し
		index = pet[no].sortTech[i].index;
		// 文字列あるとき
		if( pet[no].tech[index].name[0]!='\0' ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
			// 魔力不足の时
			if( pet[no].tech[index].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp ) PassCnt++;
			// 使用できない时
			else if( !( BattlePetUsableSkillFlag & ( 1 << i ) ) ) PassCnt++;

			// スキル数カウント
			SkillCnt++;
		}
	}
	// 全てのスキルが、忘却または魔力切れのとき
	if( PassCnt <= PassCnt ){
		wi->sw[EnumGraphSkillPass].Enabled = TRUE;
	}

	DispMax = pet[no].maxTech - 10;

	wi->sw[EnumGraphSkillFp].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_SkillMonsBase;

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchSkillBattlePetPanel;
	}
}
#ifdef PUK3_RIDE_BATTLE
//========================================
// 战闘用ペットライドキャラスキル
//========================================
void ModeChangeBattlePetRideSkill( WINDOW_INFO *wi )
{
	int i;
	int no;
	int index;
	int SkillCnt = 0, PassCnt = 0;

	SkillProcNo = 6;
	DispStart = 0;

	// とりあえず全部消す
	for(i=EnumGraphSkillJobTitle;i<EnumGraphSkillSkillPanel0;i++) wi->sw[i].Enabled = FALSE;

	wi->sw[EnumGraphSkillName].Enabled = TRUE;

	DispMax = 0;
	for(i=EnumGraphSkillScroll;i<=EnumBtSkillScrollWheel;i++) wi->sw[i].Enabled = TRUE;

	no = BattleRidePetNo;

	if (no<0) return;

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBc[ BattleMyNo ] );
#endif
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGraphSkillName].Switch )->text, pActBc[ BattleMyNo ]->name );

	// スキル分ループ
	for( i = 0 ; i < pet[no].maxTech; i++ ){
		// ソート番号取り出し
		index = pet[no].sortTech[i].index;
		// 文字列あるとき
		if( pet[no].tech[index].name[0]!='\0' ){
			// 魔力不足の时
			if( pet[no].tech[index].fp > pActBc[ BattleMyNo ]->fp ) PassCnt++;
			// 使用できない时
			else if( !( BattlePetUsableSkillFlag & ( 1 << i ) ) ) PassCnt++;

			// スキル数カウント
			SkillCnt++;
		}
	}
	// 全てのスキルが、忘却または魔力切れのとき
	if( PassCnt <= PassCnt ){
		wi->sw[EnumGraphSkillPass].Enabled = TRUE;
	}

	DispMax = pet[no].maxTech - 10;

	wi->sw[EnumGraphSkillFp].Enabled = TRUE;

	( (GRAPHIC_SWITCH *)wi->sw[EnumGraphSkillBase].Switch )->graNo = GID_SkillMonsBase;

	for(i=EnumGraphSkillSkillPanel0;i<=EnumGraphSkillSkillPanel9;i++){
		wi->sw[i].func = MenuSwitchSkillBattlePetRidePanel;
	}
}
#endif

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------

// フィールド用スキルウィンドウ呼び出し关数
ACTION *openFeildSkillWindow()
{
	ReturnFunc = 0;
	SkillProcNo = 0;

	// ウィンドウ生成
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag&=~WIN_INFO_DEL;
	return openMenuWindow( MENU_WINDOW_SKILL, OPENMENUWINDOW_HIT, 0 );
}

// 战闘用スキルウィンドウ呼び出し关数
ACTION *openBattleSkillWindow( void (*lReturnFunc)( int lSkill, int lLevel ) )
{
	ReturnFunc = lReturnFunc;
	SkillProcNo = 3;

	// ウィンドウ生成
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag&=~WIN_INFO_DEL;
	return openMenuWindow( MENU_WINDOW_SKILL, OPENMENUWINDOW_HIT, 0 );
}

// 战闘用アビリティウィンドウ呼び出し关数
ACTION *openBattleAbirityWindow( int lSkillNo, void (*lReturnFunc)( int lSkill, int lLevel ) )
{
	ReturnFunc = lReturnFunc;
	SkillProcNo = 4;
	AbirityNo = lSkillNo;

	// ウィンドウ生成
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag&=~WIN_INFO_DEL;
	return openMenuWindow( MENU_WINDOW_SKILL, OPENMENUWINDOW_HIT, 0 );
}

// 战闘用ペットスキルウィンドウ呼び出し关数
ACTION *openBattlePetSkillWindow( void (*lPetReturnFunc)( int lSkill ) )
{
	PetReturnFunc = lPetReturnFunc;
	SkillProcNo = 5;

	// ウィンドウ生成
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag&=~WIN_INFO_DEL;
	return openMenuWindow( MENU_WINDOW_SKILL, OPENMENUWINDOW_HIT, 0 );
}
#ifdef PUK3_RIDE_BATTLE
// 战闘用ペットライドキャラスキルウィンドウ呼び出し关数
ACTION *openBattlePetRideSkillWindow( void (*lPetReturnFunc)( int lSkill ) )
{
	PetReturnFunc = lPetReturnFunc;
	SkillProcNo = 6;

	// ウィンドウ生成
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag&=~WIN_INFO_DEL;
	return openMenuWindow( MENU_WINDOW_SKILL, OPENMENUWINDOW_HIT, 0 );
}
#endif

BOOL BackSkillMode()
{
	if ( !WindowFlag[MENU_WINDOW_SKILL].wininfo ) return FALSE;

	switch(SkillProcNo){
#ifdef PUK3_RIDE_BATTLE
	case 0:case 3:case 5:case 6:	WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;	return TRUE;
#else
	case 0:case 3:case 5:	WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;	return TRUE;
#endif
	case 4:	ModeChangeBattleJobTitle(WindowFlag[MENU_WINDOW_SKILL].wininfo,AbirityNo);	break;
	}
	return FALSE;
}

BOOL MenuWindowSkillBf( int mouse )
{
	if ( mouse == WIN_INIT ){
		DispStart = 0;

		switch(SkillProcNo){
		case 0:	ModeChangeJobTitle(wI,-1);	break;
		case 3:	ModeChangeBattleJobTitle(wI,-1);	break;
		case 4:	ModeChangeBattleSkillName( wI, AbirityNo, 1 );	break;
		case 5:	ModeChangeBattlePetSkill(wI);	break;
#ifdef PUK3_RIDE_BATTLE
		case 6:	ModeChangeBattlePetRideSkill(wI);	break;
#endif
		}
	}
	InfoNo = -1;

	switch(SkillProcNo){
	case 1:case 2:case 4:
		if ( job.skill[AbirityNo].name[0] == '\0' ){
			switch(SkillProcNo){
			case 2:
				RebirthOffCntDec();
				SkillMoveOffCnt--;
			case 1:
				RebirthOKCntDec();
			case 4:
				ModeChangeJobTitle(wI,0);
				break;
			}
		}
	}

	wI->sw[EnumGraphSkillRebirth].Enabled = FALSE;
	if (SkillProcNo==1) wI->sw[EnumGraphSkillRebirth].Enabled = TRUE;

	wI->sw[EnumGraphSkillPass].Enabled = FALSE;
	if (SkillProcNo==5) wI->sw[EnumGraphSkillPass].Enabled = TRUE;
#ifdef PUK3_RIDE_BATTLE
	if (SkillProcNo==6) wI->sw[EnumGraphSkillPass].Enabled = TRUE;
#endif

	return TRUE;
}

BOOL MenuWindowSkillAf( int mouse )
{
	BUTTON_SWITCH *Bt = (BUTTON_SWITCH *)wI->sw[EnumBtSkillScroll].Switch;
	char str[256];
	int i,j;

	wI->sw[EnumGraphSkillScroll].Enabled = FALSE;
	switch(SkillProcNo){
	case 0:case 2:case 3:case 5:
		if (DispMax>0) wI->sw[EnumGraphSkillScroll].Enabled = TRUE;
	}
	// つまみを移动中なら、表示开始位置を变更
	if (Bt->status&1) DispStart = ScrollVPointToNum( &wI->sw[EnumBtSkillScroll], DispMax );

	// 职业名
	switch(SkillProcNo){
	case 0:
		// 每回スキルの数を确认（スキルウィンドウになった后でスキルが追加される事があるため）
		DispMax=0;
		for(i=0;i<MAX_SKILL;i++){
			if( job.sortSkill[i].useFlag ) DispMax++;
		}
		DispMax-=10;
		if ( DispMax < 0 ) DispMax = 0;
		if ( DispStart > DispMax ) DispStart = DispMax;

		// 职业名
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillJobTitle].Switch )->text, job.name );

		// スロット
		j = 0;
		for(i=0;i<MAX_SKILL;i++){
			if ( job.sortSkill[i].useFlag ){
				j += job.skill[ job.sortSkill[i].index ].slot;
			}
		}
#ifdef PUK2_MAXSKILLSLOT_UP
		sprintf( str, "%2d/%2d", j, pc.skillSlot );                         //MLHIDE
#else
		sprintf( str, "%2d/%2d", j, MAX_SKILL );                            //MLHIDE
#endif
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillSlotCount].Switch )->text, str );
		break;
	case 1:
		// スキル名
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName].Switch )->text, job.skill[AbirityNo].name );

		// 经验值
		// テックＬＶ制限で引っかかっている时
		if( job.skill[AbirityNo].lv >= job.skill[AbirityNo].maxLv ){
			sprintf( str, "%4d/ -- ", job.skill[AbirityNo].exp );              //MLHIDE
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp].Switch )->text, str );
		}else{
			sprintf( str, "%4d/%4d", job.skill[AbirityNo].exp, job.skill[AbirityNo].nextExp ); //MLHIDE
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp].Switch )->text, str );
		}

		// スキル等级
		sprintf( str, "%2d/%2d", job.skill[AbirityNo].lv, job.skill[AbirityNo].maxLv ); //MLHIDE
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillLv].Switch )->text, str );

		// スキル说明
		if (InfoNo>=0){
			for( i = 0; i < 4; i++ ){
				if( getMemoLine( str, sizeof( str ), job.skill[AbirityNo].tech[InfoNo].memo, i, 26 ) ){
					StockFontBuffer( wI->wx+23, wI->wy+208+i*12,
						FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
				}
			}
		}
		break;
	case 2:
		// 每回レシピの数を确认（レシピウィンドウになった后でレシピが追加される事があるため）
		for(DispMax=0;;DispMax++){
			if( job.skill[AbirityNo].recipe[DispMax].id < 0 ) break;
		}
		DispMax-=10;
		if ( DispMax < 0 ) DispMax = 0;
		if ( DispStart > DispMax ) DispStart = DispMax;

		// レシピ名
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName].Switch )->text, job.skill[AbirityNo].tech[SelLine].name );
		// スキル等级
		sprintf( str, "%5d", job.skill[AbirityNo].lv );                     //MLHIDE
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillLv].Switch )->text, str );

		for( i = 0; i < 2; i++ ){
			if( getMemoLine( str, sizeof( str ), job.skill[AbirityNo].recipe[InfoNo].memo, i, 28 ) ){
				StockFontBuffer( wI->wx+23, wI->wy+208+i*12,
					FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
			}
		}
		break;
	case 3:
		// 职业名
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillJobTitle].Switch )->text, job.name );
		// スロット
		j = 0;
		for(i=0;i<MAX_SKILL;i++){
			if ( job.sortSkill[i].useFlag ){
#ifdef PUK3_RIDE
				if ( job.skill[ job.sortSkill[i].index ].operationCategory == 0 ||
					 job.skill[ job.sortSkill[i].index ].operationCategory == 5 ){
#else
				if ( job.skill[ job.sortSkill[i].index ].operationCategory == 0 ){
#endif
					j += job.skill[ job.sortSkill[i].index ].slot;
				}
			}
		}
#ifdef PUK2_MAXSKILLSLOT_UP
		sprintf( str, "%2d/%2d", j, pc.skillSlot );                         //MLHIDE
#else
		sprintf( str, "%2d/%2d", j, MAX_SKILL );                            //MLHIDE
#endif
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillSlotCount].Switch )->text, str );
		break;
	case 4:
		// スキル名
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName].Switch )->text, job.skill[AbirityNo].name );

		// 经验值
		// テックＬＶ制限で引っかかっている时
		if( job.skill[AbirityNo].lv >= job.skill[AbirityNo].maxLv ){
			sprintf( str, "%4d/ -- ", job.skill[AbirityNo].exp );              //MLHIDE
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp].Switch )->text, str );
		}else{
			sprintf( str, "%4d/%4d", job.skill[AbirityNo].exp, job.skill[AbirityNo].nextExp ); //MLHIDE
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp].Switch )->text, str );
		}

		// スキル等级
		sprintf( str, "%2d/%2d", job.skill[AbirityNo].lv, job.skill[AbirityNo].maxLv ); //MLHIDE
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillLv].Switch )->text, str );

		// スキル说明
		if (InfoNo>=0){
			for( i = 0; i < 4; i++ ){
				if( getMemoLine( str, sizeof( str ), job.skill[AbirityNo].tech[InfoNo].memo, i, 26 ) ){
					StockFontBuffer( wI->wx+23, wI->wy+208+i*12,
						FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
				}
			}
		}
		break;
	case 5:
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
		// Fp
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillFp].Switch )->text, "%4d/%4d", //MLHIDE
			pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp );

		// スキル说明
		if (InfoNo>=0){
			for( i = 0; i < 4; i++ ){
				if( getMemoLine( str, sizeof( str ), pet[ CheckBattlePet() ].tech[InfoNo].memo, i, 26 ) ){
					StockFontBuffer( wI->wx+23, wI->wy+208+i*12,
						FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
				}
			}
		}
		break;
#ifdef PUK3_RIDE_BATTLE
	case 6:
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleMyNo ] );
#endif
		// Fp
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillFp].Switch )->text, "%4d/%4d", //MLHIDE
			pActBc[ BattleMyNo ]->fp, pActBc[ BattleMyNo ]->maxFp );

		// スキル说明
		if (InfoNo>=0){
			for( i = 0; i < 4; i++ ){
				if( getMemoLine( str, sizeof( str ), pet[ BattleRidePetNo ].tech[InfoNo].memo, i, 26 ) ){
					StockFontBuffer( wI->wx+23, wI->wy+208+i*12,
						FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
				}
			}
		}
		break;
#endif
	}

	displayMenuWindow();

	return TRUE;
}

BOOL closeSkillWindow()
{
	if ( SkillProcNo==1 || SkillProcNo==2 ) RebirthOKCntDec();
	if (SkillProcNo==2){
		RebirthOffCntDec();
		SkillMoveOffCnt--;
	}

	return TRUE;
}

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
BOOL MenuSwitchSkillClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillBack( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ){
		switch(SkillProcNo){
		case 1:	strcpy( OneLineInfoStr, MWONELINE_SKILL_LISTBACK );	break;
		case 2:	strcpy( OneLineInfoStr, MWONELINE_SKILL_RECIPEBACK );	break;
		case 4:	strcpy( OneLineInfoStr, MWONELINE_SKILL_LISTBACK );	break;
		}
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		switch(SkillProcNo){
		case 1:
			ModeChangeJobTitle(wI,AbirityNo);
			RebirthOKCntDec();
			break;
		case 2:
			ModeChangeSkillLv(wI, -1);
			RebirthOffCntDec();
			SkillMoveOffCnt--;
			break;
		case 4: ModeChangeBattleJobTitle(wI,AbirityNo);	break;
		}

		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_CommonBackOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_CommonBackOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_CommonBackOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillRibirth( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

#ifdef PUK3_VEHICLE
	// 乘り物移动中でないなら
	// もしくは见えないときでないなら
	if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
		 !nowVehicleProc() ){
	}else{
		wI->sw[no].Enabled = FALSE;
		return ReturnFlag;
	}
#endif
	if ( (pc.rebirthLevel==0) || (pc.bt<=0) ){
		wI->sw[no].Enabled = FALSE;
		return ReturnFlag;
	}
	// 战闘系なら
	if ( !job.skill[AbirityNo].operationCategory ){
		wI->sw[no].Enabled = FALSE;
		return ReturnFlag;
	}

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ){
		if (skillrebirth) strcpy( OneLineInfoStr, MWONELINE_SKILL_REBIRTH_OFF );
		else strcpy( OneLineInfoStr, MWONELINE_SKILL_REBIRTH_ON );
	}

	//押されたとき
	if ( !RebirthOffCnt ){
		if( flag & MENU_MOUSE_LEFT ){
			if ( (pc.rebirthLevel!=0) && (pc.bt>0) ){
				if (skillrebirth){
					nrproto_AC_send( sockfd, mapGx, mapGy, 62 );		//リバース中止
					skillrebirth=0;
					ItemNoOpe[7]--;
				}else{
					nrproto_AC_send( sockfd, mapGx, mapGy, 61 );		//リバース使用
					skillrebirth=1;
					ItemNoOpe[7]++;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag = TRUE;
			}
		}
	}else{
		if( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	Graph->graNo = GID_CommonRebirthOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_CommonRebirthOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_CommonRebirthOff;

	if ( RebirthOffCnt ) Graph->graNo = GID_CommonRebirthOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (DispStart>0){
			DispStart--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSkillScroll], DispMax, DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if ( DispStart < DispMax ){
			DispStart++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSkillScroll], DispMax, DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (DispStart>0){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		DispStart-=10;
		if (DispStart<0) DispStart = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSkillScroll], DispMax, DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if ( (DispMax>0) && (DispStart<DispMax) ){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		DispStart+=10;
		if ( DispStart > DispMax ) DispStart = DispMax;

		// つまみを移动
		NumToScrollVMove( &wI->sw[EnumBtSkillScroll], DispMax, DispStart );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		DispStart = WheelToMove( &wI->sw[EnumBtSkillScroll],
			 DispStart, DispMax, mouse.wheel );
	}

	return ReturnFlag;
}

//========================================
// ジョブタイトルウィンドウ
//========================================

// スキルパネルの描画
const int SkillTitlePanelGraTbl[][3]={
	{ GID_SkillTitlePanelROn, GID_SkillTitlePanelROff, GID_SkillTitlePanelROver },	// 战闘系
	{ GID_SkillTitlePanelBOn, GID_SkillTitlePanelBOff, GID_SkillTitlePanelBOver },	// 生产系 オペレーション分类ーA
	{ GID_SkillTitlePanelBOn, GID_SkillTitlePanelBOff, GID_SkillTitlePanelBOver },	// 生产系 オペレーション分类ーB
	{ GID_SkillTitlePanelYOn, GID_SkillTitlePanelYOff, GID_SkillTitlePanelYOver },	// その他 オペレーション分类ーC
	{ GID_SkillTitlePanelGOn, GID_SkillTitlePanelGOff, GID_SkillTitlePanelGOver },	// 采取系 オペレーション分类ーD
#ifdef PUK3_RIDE
	{ GID_SkillTitlePanelROn, GID_SkillTitlePanelROff, GID_SkillTitlePanelROver },	// ライド系
#endif
};
void DrawSkillTitle( short x, short y, char mouse, int Num, BOOL usable, int FontPrio, int DispPrio, unsigned long rgba )
{
	char str[20];
	int no;
	BLT_MEMBER bm={0};

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	if ( job.sortSkill[Num].useFlag ){
		no = job.sortSkill[Num].index;

		//フォント用バッファの区切り
		FontBufCut(FontPrio);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		StockFontBuffer( x+7, y+2, FontPrio, FONT_KIND_SIZE_12, (usable?FONT_PAL_WHITE:FONT_PAL_GRAY), job.skill[no].name, 0, 0 );

		// 等级
		sprintf( str, "%2d", job.skill[no].lv );                            //MLHIDE
		DrawGraphicNumber( x+155, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );

		// 使用スロット数
		sprintf( str, "%2d", job.skill[no].slot );                          //MLHIDE
		DrawGraphicNumber( x+193, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );

		StockDispBuffer( x+105, y+8, DispPrio, SkillTitlePanelGraTbl[job.skill[no].operationCategory][mouse], 0, &bm );
	}
}

BOOL MenuSwitchSkillSkillPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
#ifdef PUK2_NEWDRAG
	int DropNo;
#else
	int DropNo, DragNo;
#endif
	int Num = no-EnumGraphSkillSkillPanel0  + DispStart;

	if ( !job.sortSkill[Num].useFlag ) return FALSE;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_SKILL ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropNo = (int)WinDD_ObjData();
#else
		DropNo = (int)WinDD_GetObject();
#endif
		if ( DropNo != Num ){
			// スキル入れ替えプロトコル発动中じゃなかったら
			if( !SkillSwapFlg ){
				// スキル入れ替えプロトコルを呼ぶ。
				nrproto_SKSW_send( sockfd, job.sortSkill[DropNo].index, job.sortSkill[Num].index );
				// 発动中にする。
				SkillSwapFlg = 1;
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( flag & MENU_MOUSE_LEFT ){
		if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
			ModeChangeSkillLv(wI, Num);
			RebirthOKCntInc();
			WinDD_DragFinish();
			ReturnFlag=TRUE;
		}
	}else
	if ( flag & MENU_MOUSE_OVER ){
		// 右键したとき
		if( flag & MENU_MOUSE_RIGHT ){
			if (!SkillMoveOffCnt){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragSkill( Num );
#else
				WinDD_DragStart( WINDD_SKILL, (void *)(Num) );
#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_SKILL ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_SKILL ) && (WinDD_ButtonNo()==no ) ){
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ){
				DragNo = (int)WinDD_ObjData();
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_SKILL, (void *)(DragNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_LEFT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

#ifdef PUK2_NEWDRAG
#else
	if ( WinDD_CheckObjType()==WINDD_SKILL ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_SKILL ) && (WinDD_ButtonNo()==no ) ){
			DragNo = (int)WinDD_ObjData();

			DrawSkillTitle( mouse.nowPoint.x-103, mouse.nowPoint.y-8, 2, (int)WinDD_ObjData(), TRUE, FONT_PRIO_DRAG, DISP_PRIO_DRAG, 0x80ffffff );
/***
			StockBoxDispBuffer(
				wI->wx+wI->sw[no].ofx+2, wI->wy+wI->sw[no].ofy+2,
				wI->wx+wI->sw[no].ofx+wI->sw[no].sx-2, wI->wy+wI->sw[no].ofy+wI->sw[no].sy-2,
				DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
***/
		}
	}
#endif

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILL_SKILLPANEL );

	if ( flag&(MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/
	}
	if (flag&MENU_MOUSE_LEFT) mouseflag = 1;

	// 本体の描画
	DrawSkillTitle( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num, TRUE, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}

//========================================
// スキル名ウィンドウ
//========================================

// スキルパネルの描画
const int SkillNamePanelGraTbl[][3]={
	{ GID_SkillLevelPanelROn, GID_SkillLevelPanelROff, GID_SkillLevelPanelROver },	// 战闘系
	{ GID_SkillLevelPanelBOn, GID_SkillLevelPanelBOff, GID_SkillLevelPanelBOver },	// 生产系 オペレーション分类ーA
	{ GID_SkillLevelPanelBOn, GID_SkillLevelPanelBOff, GID_SkillLevelPanelBOver },	// 生产系 オペレーション分类ーB
	{ GID_SkillLevelPanelYOn, GID_SkillLevelPanelYOff, GID_SkillLevelPanelYOver },	// その他 オペレーション分类ーC
	{ GID_SkillLevelPanelGOn, GID_SkillLevelPanelGOff, GID_SkillLevelPanelGOver },	// 采取系 オペレーション分类ーD
#ifdef PUK3_RIDE
	{ GID_SkillLevelPanelROn, GID_SkillLevelPanelROff, GID_SkillLevelPanelROver },	// ライド系
#endif
};
void DrawSkillName( short x, short y, char mouse, int Num, int FontPrio, int DispPrio, unsigned long rgba )
{
	char str[20];
	BLT_MEMBER bm={0};
	int color;
	char BattleFlag = 0;

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	if (Num>=100){
		BattleFlag = 1;
		Num -= 100;
	}
	if( job.skill[AbirityNo].tech[Num].name[0] != '\0' ){
		if (job.skill[AbirityNo].tech[Num].usableFlag){
			color = FONT_PAL_WHITE;
			if (BattleFlag){
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBc[ BattleMyNo ] );
#endif
				if (pActBc[ BattleMyNo ]->fp<job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100){
					color = FONT_PAL_RED;
				}
			}
		}else{
			color = FONT_PAL_GRAY;
		}
		//フォント用バッファの区切り
		FontBufCut(FontPrio);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		StockFontBuffer( x+7, y+2, FontPrio, FONT_KIND_SIZE_12, color, job.skill[AbirityNo].tech[Num].name, 0, 0 );

		// Lv表示
		sprintf( str, "%2d", job.skill[AbirityNo].tech[Num].techlv );       //MLHIDE
		DrawGraphicNumber( x+155, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );

		// FP表示
		if ( job.skill[AbirityNo].operationCategory==1 || job.skill[AbirityNo].operationCategory==2 ){
			StockFontBuffer( x+189, y+3, FontPrio, FONT_KIND_SIZE_12, color, "???", 0, 0 ); //MLHIDE
		}else{
			sprintf( str, "%3d", job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 ); //MLHIDE
			DrawGraphicNumber( x+186, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );
		}

		StockDispBuffer( x+105, y+8, DispPrio, SkillNamePanelGraTbl[job.skill[AbirityNo].operationCategory][mouse], 0, &bm );
	}
}

#ifdef PUK3_CURE_FP_LUCK
char skillFpLuckString[2][81];
BOOL skillFpLuckReturn( int Button, int num, char **str, char linenum )
{
	return TRUE;
}
#endif
BOOL MenuSwitchSkillNamePanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
	int Num = no-EnumGraphSkillSkillPanel0;

	if( job.skill[AbirityNo].tech[Num].name[0] == '\0' ) return FALSE;

	if ( flag&MENU_MOUSE_OVER ){
		// 一行インフォ
		if (job.skill[AbirityNo].tech[Num].usableFlag) strcpy( OneLineInfoStr, MWONELINE_SKILL_ON );
		else strcpy( OneLineInfoStr, MWONELINE_SKILL_OFF );

		switch(job.skill[AbirityNo].operationCategory){
		case 4:	// 采取系スキルのとき
			// 魔力が足りないなら警告を出す
			if( pc.fp < job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 ){
				strcpy( OneLineInfoStr, MWONELINE_SKILL_FP );
			}
			break;

		case 3:	// その他
			// 魔力が足りないなら警告ウィンドウを出す
			if( pc.fp < job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 ){
				strcpy( OneLineInfoStr, MWONELINE_SKILL_FP );
			}
			break;
		}

		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( flag&MENU_MOUSE_LEFT ){
#ifdef PUK2_JOB_CHANGE2
		if (job.skill[AbirityNo].tech[Num].usableFlag){
#endif
		switch(job.skill[AbirityNo].operationCategory){
		case 4:	// 采取系スキルのとき
#ifdef PUK3_VEHICLE
			// 乘り物移动中でないなら
			// もしくは见えないときでないなら
			if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
				 !nowVehicleProc() ){
			}else{
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
				break;
			}
#endif
			// 魔力が足りないなら警告を出す
			if( pc.fp < job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 ){
				StockChatBufferLine( ML_STRING(996, "魔力不足。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 ); //MLHIDE
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
			}else
			// アイテム栏に空きがないなら警告を出す
			if( getItemEmpty() <= 0 ){
				StockChatBufferLine( ML_STRING(545, "物品栏没有空间了。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
			}else
			{
				if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo){
					if ( SkillGatherSkillNo != AbirityNo || CategoryCSelTech != Num ){
						pActGather = openSkillGatherWindow( AbirityNo, Num );
					}
				}else{
					if (!pActGather) pActGather = openSkillGatherWindow( AbirityNo, Num );
				}
/***
				if( job.skill[index].id == 254){
					menuClose( abilityMenuWin.menuNo );
					menuOpen( MENU_FISHING_FEED_WIN);
				}
				else {
					menuOpen( MENU_CATEGORY_D_GATHER_WIN );
				}
				// アビリティウィンドウからＤした时は、エキストラタイムを足す。
				CategoryDExtraTime = CATEGORY_D_EXTRA_TIME;
***/
			}
			break;

		case 3:	// その他
#ifdef PUK3_VEHICLE
			// 乘り物移动中でないなら
			// もしくは见えないときでないなら
			if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
				 !nowVehicleProc() ){
			}else{
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
				break;
			}
#endif
			// 魔力が足りないなら警告ウィンドウを出す
			if( pc.fp < job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 ){
#ifdef PUK3_CURE_FP_LUCK
				int LineLen, LineNum;

				sprintf( skillFpLuckString[0], ML_STRING(544, "魔力不足。") );
				sprintf( skillFpLuckString[1], ML_STRING(587, "需要魔力 %d。"), 
					 job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 );
				LineLen = GeneralWindowStrLen(320);
				LineNum = GeneralWindowStrLineNum( 100, GRL_PUSH_OK, 16 );
				openMenuGeneralWindow(
					(DEF_APPSIZEX-320)>>1, (DEF_APPSIZEY-100-24)>>1, 320, 100,
					GID_CommonWindow, GRL_PUSH_OK, skillFpLuckString,
					LineNum, 16, 0, 0, NULL, LineLen, 2, GRL_OPT_EXCLUSIVE|GRL_OPT_NOMOVE,
					skillFpLuckReturn, NULL, OPENMENUWINDOW_HIT, 0 );
#endif
//				categoryCUseFp = job.skill[index].tech[selTech].fp * job.skill[index].fpRate/100;
//				menuOpen( MENU_CATEGORY_C_ENOUGH_FP_WIN );
			}else
			// （对象选择１ウィンドウ开く）
			{
				CategoryCSkillNo = AbirityNo;
				CategoryCSelTech = Num;
				// 目の前のキャラの情报を要求
				nrproto_GFL_send( sockfd , AbirityNo );
			}
			break;

		case 2:	// 生产系スキルのとき

#ifdef PUK3_VEHICLE
			// 乘り物移动中でないなら
			// もしくは见えないときでないなら
			if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
				 !nowVehicleProc() ){
			}else{
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
				break;
			}
#endif
			if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo){
				if ( CreateSkillNo != AbirityNo || CreateSelTech != Num ){
					pActCreate = openSkillRepAppWindow( AbirityNo, Num );
				}
			}else{
				if (!pActCreate) pActCreate = openSkillRepAppWindow( AbirityNo, Num );
			}
			break;

		case 1:	// 生产系スキルのとき

			// 宝石追加の场合
			if( job.skill[AbirityNo].id == 255){
#ifdef PUK3_VEHICLE
				// 乘り物移动中でないなら
				// もしくは见えないときでないなら
				if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
					 !nowVehicleProc() ){
				}else{
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
					break;
				}
#endif
				openSkillDecoWindow( AbirityNo, Num );
			}
			else{
				RebirthOffCntInc();
				SkillMoveOffCnt++;
				if ( (job.skill[AbirityNo].trn!=0)!=(skillrebirth!=0) ){
					nrproto_IR_send( sockfd, AbirityNo+(skillrebirth?100:0) );
				}
				// レシピウィンドウ开く
				ModeChangeSkillRecipe(wI, Num);
			}
			break;
#ifdef PUK3_RIDE
		case 5:	// ライドのとき
	#ifdef PUK3_VEHICLE
			// 乘り物移动中でないなら
			// もしくは见えないときでないなら
			if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
				 !nowVehicleProc() ){
			}else{
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
				break;
			}
	#endif
			// ライド中なら
			if ( ( (CHAREXTRA *)pc.ptAct->pYobi )->ptRide ){
				// 技使用プロトコル送信
				nrproto_TU_send( sockfd, AbirityNo+(skillrebirth?100:0), Num, -1, "" );
			}else{
				if( pc.fp < job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100 ){
					StockChatBufferLine( ML_STRING(996, "魔力不足。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 ); //MLHIDE
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					// 技使用プロトコル送信
					nrproto_TU_send( sockfd, AbirityNo+(skillrebirth?100:0), Num, -1, "" );
				}
			}
			break;
#endif

		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
#ifdef PUK2_JOB_CHANGE2
		}else{
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
#endif

		ReturnFlag=TRUE;
	}
	if (flag&MENU_MOUSE_LEFT) mouseflag = 1;

	// 本体の描画
	DrawSkillName( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}

//========================================
// レシピウィンドウ
//========================================

// スキルパネルの描画
const int RecipeNamePanelGraTbl[][3]={
	{ GID_SkillRecipePanelROn, GID_SkillRecipePanelROff, GID_SkillRecipePanelROver },	// 战闘系
	{ GID_SkillRecipePanelBOn, GID_SkillRecipePanelBOff, GID_SkillRecipePanelBOver },	// 生产系 オペレーション分类ーA
	{ GID_SkillRecipePanelBOn, GID_SkillRecipePanelBOff, GID_SkillRecipePanelBOver },	// 生产系 オペレーション分类ーB
	{ GID_SkillRecipePanelYOn, GID_SkillRecipePanelYOff, GID_SkillRecipePanelYOver },	// その他 オペレーション分类ーC
	{ GID_SkillRecipePanelGOn, GID_SkillRecipePanelGOff, GID_SkillRecipePanelGOver },	// 采取系 オペレーション分类ーD
#ifdef PUK3_RIDE
	{ GID_SkillRecipePanelROn, GID_SkillRecipePanelROff, GID_SkillRecipePanelROver },	// 战闘系
#endif
};
void DrawRecipeName( short x, short y, char mouse, int Num, int FontPrio, int DispPrio, unsigned long rgba )
{
	char str[20];
	BLT_MEMBER bm={0};

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	if( job.skill[AbirityNo].recipe[Num].id >= 0 ){
		//フォント用バッファの区切り
		FontBufCut(FontPrio);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		StockFontBuffer( x+7, y+2, FontPrio, FONT_KIND_SIZE_12,
			(job.skill[AbirityNo].recipe[Num].usableFlag?FONT_PAL_WHITE:FONT_PAL_GRAY), job.skill[AbirityNo].recipe[Num].name, 0, 0 );

		// FP表示
		sprintf( str, "%3d", job.skill[AbirityNo].recipe[Num].fp*job.skill[AbirityNo].fpRate/100 ); //MLHIDE
		DrawGraphicNumber( x+186, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );

		StockDispBuffer( x+105, y+8, DispPrio, RecipeNamePanelGraTbl[job.skill[AbirityNo].operationCategory][mouse], 0, &bm );
	}
}

BOOL MenuSwitchRecipeNamePanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
	int Num = no-EnumGraphSkillSkillPanel0 + DispStart;

	if ( (job.skill[AbirityNo].trn!=0)!=(skillrebirth!=0) ) return FALSE;
	if( job.skill[AbirityNo].recipe[Num].id < 0 ) return FALSE;

	// アイテム栏を右键したとき
	if (flag&MENU_MOUSE_LEFT){
#ifdef PUK3_VEHICLE
		// 乘り物移动中でないなら
		// もしくは见えないときでないなら
		if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
			 !nowVehicleProc() ){
			if (job.skill[AbirityNo].recipe[Num].usableFlag){
				if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo){
					if ( CreateSkillNo != AbirityNo || CreateSelTech != SelLine || CreateRecipeNo != Num ){
						pActCreate = openSkillCreateWindow( AbirityNo, Num, SelLine );
					}
				}else{
					if (!pActCreate) pActCreate = openSkillCreateWindow( AbirityNo, Num, SelLine );
				}
	
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
			}
#else
		if (job.skill[AbirityNo].recipe[Num].usableFlag){
			if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo){
				if ( CreateSkillNo != AbirityNo || CreateSelTech != SelLine || CreateRecipeNo != Num ){
					pActCreate = openSkillCreateWindow( AbirityNo, Num, SelLine );
				}
			}else{
				if (!pActCreate) pActCreate = openSkillCreateWindow( AbirityNo, Num, SelLine );
			}

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
#endif
		}else{
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	if (flag&MENU_MOUSE_OVER){
		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/

		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SKILL_RECIPEPANEL );
	}
	if (flag&MENU_MOUSE_LEFT) mouseflag = 1;
	if ( WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo ){
		if ( CreateSkillNo==AbirityNo && CreateSelTech==SelLine && CreateRecipeNo==Num ){
			mouseflag = 1;
		}
	}

	// 本体の描画
	DrawRecipeName( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}

//========================================
// 战闘时用
//========================================
int CheckItemBlank( void );

BOOL MenuSwitchSkillBattleJobPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
	int Num = BattleSkillIndex[ no-EnumGraphSkillSkillPanel0  + DispStart ];

	if (Num<0) return FALSE;
	if ( !job.sortSkill[Num].useFlag ) return FALSE;

	// 右键したとき
	if( flag & MENU_MOUSE_LEFT ){
		// 使用できるとき
		if( BattleUsableSkillFlag & ( 1 << job.sortSkill[Num].index ) ){
			ModeChangeBattleSkillName( wI, Num, 0 );
			
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			ReturnFlag=TRUE;
		}else{
#ifdef PUK2_STEAL_REPLASE
			if( job.skill[ job.sortSkill[Num].index ].id == 72){
#else
			if( job.skill[Num].id == 72){
#endif
				if(CheckItemBlank() == 0){
					StockChatBufferLine( ML_STRING(142, "携带物品太多了，无法使用。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
				}
			}
			
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	if ( flag&MENU_MOUSE_OVER ){
		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/

		// 一行インフォ
		if( BattleUsableSkillFlag & ( 1 << job.sortSkill[Num].index ) ) strcpy( OneLineInfoStr, MWONELINE_SKILL_SKILLPANEL );
	}
	if (flag&MENU_MOUSE_LEFT) mouseflag = 1;

	// 本体の描画
	DrawSkillTitle( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num,
		( BattleUsableSkillFlag & (1<<job.sortSkill[Num].index) ) != 0, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}

BOOL MenuSwitchSkillBattleNamePanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
	int Num = no-EnumGraphSkillSkillPanel0;
	int needfp;

	if( job.skill[AbirityNo].tech[Num].name[0] == '\0' ) return FALSE;

	needfp = job.skill[AbirityNo].tech[Num].fp*job.skill[AbirityNo].fpRate/100;

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBc[ BattleMyNo ] );
#endif
	// 右键したとき
	if (job.skill[AbirityNo].tech[Num].usableFlag && pActBc[ BattleMyNo ]->fp>=needfp){
		if( flag & MENU_MOUSE_LEFT ){
			if (ReturnFunc) ReturnFunc( AbirityNo, Num );
			wI->flag |= WIN_INFO_DEL;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			ReturnFlag=TRUE;
		}
	}else{
		if( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	if (flag&MENU_MOUSE_OVER){
		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/

		// 一行インフォ
		if (!job.skill[AbirityNo].tech[Num].usableFlag){
			strcpy( OneLineInfoStr, ML_STRING(146, "能力不足，无法使用技能。") );
		}else if (pActBc[ BattleMyNo ]->fp<needfp){
			strcpy( OneLineInfoStr, ML_STRING(147, "魔力不足，无法使用技能。") );
		}else{
			strcpy( OneLineInfoStr, MWONELINE_SKILL_ON );
		}
	}
	if (flag&MENU_MOUSE_LEFT){
		if (job.skill[AbirityNo].tech[Num].usableFlag && pActBc[ BattleMyNo ]->fp>=needfp){
			mouseflag = 1;
		}
	}

	// 本体の描画
	DrawSkillName( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num+100, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}

//========================================
// 战闘用ペットスキル
//========================================

BOOL MenuSwitchSkillBattlePetPass( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i, cnt, passcnt;
	int PetNo, Index;

#ifdef PUK3_RIDE_BATTLE
	int CharNo;

	switch(SkillProcNo){
	case 5:
		PetNo = CheckBattlePet();
		CharNo = BattleCheckPetId( BattleMyNo );
		break;
	case 6:
		PetNo = BattleRidePetNo;
		CharNo = BattleMyNo;
		break;
	}

	cnt = 0;
	passcnt = 0;
	// スキル分ループ
	for( i = 0 ; i < pet[PetNo].maxTech; i++ ){
		// ソート番号取り出し
		Index = pet[PetNo].sortTech[i].index;
		// 文字列あるとき
		if( pet[PetNo].tech[Index].name[ 0 ] != NULL ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( CharNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ CharNo ] );
#endif
			// 魔力不足の时
			if( pet[PetNo].tech[Index].fp > pActBc[ CharNo ]->fp ) passcnt++;
			// 使用できない时
			else if( !( BattlePetUsableSkillFlag & ( 1 << i ) ) ) passcnt++;

			// スキル数カウント
			cnt++;
		}
	}
#else
	PetNo = CheckBattlePet();
	cnt = 0;
	passcnt = 0;
	// スキル分ループ
	for( i = 0 ; i < pet[PetNo].maxTech; i++ ){
		// ソート番号取り出し
		Index = pet[PetNo].sortTech[i].index;
		// 文字列あるとき
		if( pet[PetNo].tech[Index].name[ 0 ] != NULL ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
			// 魔力不足の时
			if( pet[PetNo].tech[Index].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp ) passcnt++;
			// 使用できない时
			else if( !( BattlePetUsableSkillFlag & ( 1 << i ) ) ) passcnt++;

			// スキル数カウント
			cnt++;
		}
	}
#endif
	
	// 一つでも使えるスキルがあるとき
	if( cnt > passcnt ){
		wI->sw[EnumGraphSkillPass].Enabled = FALSE;
		return ReturnFlag;
	}

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILL_PASS );

	// 右键したとき
	if( flag & MENU_MOUSE_LEFT ){
		if (PetReturnFunc) PetReturnFunc(-1);
		wI->flag |= WIN_INFO_DEL;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_PassButtonSOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_PassButtonSOver;
	if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_PassButtonSOff;

	return ReturnFlag;
}

// スキルパネルの描画
const int SkillPetPanelGraTbl[3]={ GID_SkillRecipePanelROn, GID_SkillRecipePanelROff, GID_SkillRecipePanelROver };
void DrawPetSkillName( short x, short y, char mouse, int Num, int FontPrio, int DispPrio, unsigned long rgba )
{
	char str[20];
	int color;
	int PetNo = CheckBattlePet();
	int no = pet[ CheckBattlePet() ].sortTech[Num].index;
	BLT_MEMBER bm={0};

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	// 文字列あるとき
	if( pet[PetNo].tech[no].name[0] != NULL ){
		//フォント用バッファの区切り
		FontBufCut(FontPrio);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );


#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
		// スキル名
		color = FONT_PAL_WHITE;
		// 魔力不足の时（赤）
		if( pet[PetNo].tech[no].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp ) color = FONT_PAL_RED;
		// 使用できない时（灰色）
		if( !( BattlePetUsableSkillFlag & ( 1 << Num ) ) ) color = FONT_PAL_GRAY;
		StockFontBuffer( x+7, y+2, FontPrio, FONT_KIND_SIZE_12, color, pet[PetNo].tech[no].name, 0, 0 );

		// FP表示
		sprintf( str, "%3d", pet[PetNo].tech[no].fp );                      //MLHIDE
		DrawGraphicNumber( x+186, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );

		StockDispBuffer( x+105, y+8, DispPrio, SkillPetPanelGraTbl[mouse], 0, &bm );
	}
}

BOOL MenuSwitchSkillBattlePetPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
	int Num = no-EnumGraphSkillSkillPanel0;

	if( pet[ CheckBattlePet() ].tech[ pet[ CheckBattlePet() ].sortTech[Num].index ].name[0] == NULL ) return FALSE;

	// 右键したとき
	if( BattlePetUsableSkillFlag & ( 1 << Num ) ) {
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
		if ( pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp >=
			pet[ CheckBattlePet() ].tech[ pet[ CheckBattlePet() ].sortTech[Num].index ].fp ){
			if( flag & MENU_MOUSE_LEFT ){
				if (PetReturnFunc) PetReturnFunc( pet[ CheckBattlePet() ].sortTech[Num].index );
				wI->flag |= WIN_INFO_DEL;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;
			}
		}
	}else{
		if( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	if (flag&MENU_MOUSE_OVER){
		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/

		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SKILL_ON );
	}
	if (flag&MENU_MOUSE_LEFT) mouseflag = 1;

	// 本体の描画
	DrawPetSkillName( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}
#ifdef PUK3_RIDE_BATTLE
void DrawPetRideSkillName( short x, short y, char mouse, int Num, int FontPrio, int DispPrio, unsigned long rgba )
{
	char str[20];
	int color;
	int PetNo = BattleRidePetNo;
	int no = pet[ BattleRidePetNo ].sortTech[Num].index;
	BLT_MEMBER bm={0};

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	// 文字列あるとき
	if( pet[PetNo].tech[no].name[0] != NULL ){
		//フォント用バッファの区切り
		FontBufCut(FontPrio);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );


#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleMyNo ] );
#endif
		// スキル名
		color = FONT_PAL_WHITE;
		// 魔力不足の时（赤）
		if( pet[PetNo].tech[no].fp > pActBc[ BattleMyNo ]->fp ) color = FONT_PAL_RED;
		// 使用できない时（灰色）
		if( !( BattlePetUsableSkillFlag & ( 1 << Num ) ) ) color = FONT_PAL_GRAY;
		StockFontBuffer( x+7, y+2, FontPrio, FONT_KIND_SIZE_12, color, pet[PetNo].tech[no].name, 0, 0 );

		// FP表示
		sprintf( str, "%3d", pet[PetNo].tech[no].fp );                      //MLHIDE
		DrawGraphicNumber( x+186, y+3, str, G_NUM_SIZE__9, FONT_PAL_WHITE, G_NUM_FLAG_LEFT_JUSTIFIED, DispPrio );

		StockDispBuffer( x+105, y+8, DispPrio, SkillPetPanelGraTbl[mouse], 0, &bm );
	}
}

BOOL MenuSwitchSkillBattlePetRidePanel( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	char mouseflag = 0;
	int Num = no-EnumGraphSkillSkillPanel0;

	if( pet[ BattleRidePetNo ].tech[ pet[ BattleRidePetNo ].sortTech[Num].index ].name[0] == NULL ) return FALSE;

	// 右键したとき
	if( BattlePetUsableSkillFlag & ( 1 << Num ) ) {
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleMyNo ] );
#endif
		if ( pActBc[ BattleMyNo ]->fp >=
			pet[ BattleRidePetNo ].tech[ pet[ BattleRidePetNo ].sortTech[Num].index ].fp ){
			if( flag & MENU_MOUSE_LEFT ){
				if (PetReturnFunc) PetReturnFunc( pet[ BattleRidePetNo ].sortTech[Num].index );
				wI->flag |= WIN_INFO_DEL;

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;
			}
		}
	}else{
		if( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	if (flag&MENU_MOUSE_OVER){
		mouseflag = 2;
		InfoNo = Num;

/***
		// アイテム选择枠
		StockBoxDispBuffer(
			wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
			DISP_PRIO_WIN2, BoxColor, 0 );
***/

		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_SKILL_ON );
	}
	if (flag&MENU_MOUSE_LEFT) mouseflag = 1;

	// 本体の描画
	DrawPetRideSkillName( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy, mouseflag, Num, FONT_PRIO_WIN, DISP_PRIO_WIN2, 0xffffffff );

	return ReturnFlag;
}
#endif






#ifdef PUK3_MONSTER_HELPER

//====================================//
//			ペットお手伝い用		  //
//====================================//

static int SkillMonsHelperStart;
short SkillCreateMonsPoint[][3] = {
	{ 30, -45, 0 },{ -30, -45, 7 },{ -50, -20, 6 },
	{ -35, 22, 4 },{ 35, 22, 3 },{ 50, -20, 2 },
};

void SetMonsPoint( WINDOW_INFO *wi, int swPC, int swF, int swB, int x, int y )
{
	wi->sw[swF].Enabled = FALSE;
	wi->sw[swF].ofx = wi->sw[swPC].ofx + x;
	wi->sw[swF].ofy = wi->sw[swPC].ofy + y;
	wi->sw[swB].Enabled = FALSE;
	wi->sw[swB].ofx = wi->sw[swPC].ofx + x;
	wi->sw[swB].ofy = wi->sw[swPC].ofy + y;

	if ( ( (ACTION_SWITCH *)wi->sw[swF].Switch )->ActionAdd->anim_chr_no <= 0 ){
		return;
	}

	// ＰＣより后ろのとき
	if ( y <= 0 ){
		wi->sw[swB].Enabled = TRUE;
	}
	// ＰＣより前のとき
	else{
		wi->sw[swF].Enabled = TRUE;
	}
}

void SetMonsAnim( WINDOW_INFO *wi, int swF, int swB, BOOL Loop, int Anim, int ang )
{
	ACTION_SWITCH *ActSw;
	ACTION *pAct;

	// 前
	ActSw = (ACTION_SWITCH *)wi->sw[swF].Switch;
	pAct = ActSw->ActionAdd;

	ActSw->AnimNoLoop = !Loop;
	pAct->anim_no = Anim;
	pAct->anim_ang = ang;

	// 后
	ActSw = (ACTION_SWITCH *)wi->sw[swB].Switch;
	pAct = ActSw->ActionAdd;

	ActSw->AnimNoLoop = !Loop;
	pAct->anim_no = Anim;
	pAct->anim_ang = ang;
}

void SetMonsChar( WINDOW_INFO *wi, int swF, int swB, int chr )
{
	ACTION_SWITCH *ActSw;
	ACTION *pAct;

	// 前
	ActSw = (ACTION_SWITCH *)wi->sw[swF].Switch;
	pAct = ActSw->ActionAdd;
	pAct->anim_chr_no = chr;

	// 后
	ActSw = (ACTION_SWITCH *)wi->sw[swB].Switch;
	pAct = ActSw->ActionAdd;
	pAct->anim_chr_no = chr;

	if ( chr <= 0 ){
		wi->sw[swF].Enabled = FALSE;
		wi->sw[swB].Enabled = FALSE;
	}
}

void MonsHelperMove( WINDOW_INFO *wi, int swPC, int swF, int swB, int ftime )
{
	int w,h, i, j, k;
	int nowtime = GetTickCount();
	i = (ftime - SkillMonsHelperStart) / 11;
	if ( i == 0 ) j = 0;
	else j = (nowtime - SkillMonsHelperStart) / i;

	switch( j ){
	// 移动中
	case 0:case 2:case 4:case 6:case 8:case 10:
		k = j/2 + 1;
		if ( k >= 6 ) k = 0;
		w = SkillCreateMonsPoint[k][0] - SkillCreateMonsPoint[j/2][0];
		h = SkillCreateMonsPoint[k][1] - SkillCreateMonsPoint[j/2][1];
		SetMonsPoint( wI, swPC, swF, swB,
			 ( w * ( (nowtime - SkillMonsHelperStart) % i) ) / i + SkillCreateMonsPoint[j/2][0],
			 ( h * ( (nowtime - SkillMonsHelperStart) % i) ) / i + SkillCreateMonsPoint[j/2][1]
			 );
		SetMonsAnim( wI, swF, swB,TRUE, ANIM_WALK, SkillCreateMonsPoint[k][2] );
		break;
	// アピール中
	case 1:case 3:case 5:case 7:case 9:
		k = j/2 + 1;
		if ( k > 7 ) k = 0;
		SetMonsPoint( wI, swPC, swF, swB, SkillCreateMonsPoint[k][0], SkillCreateMonsPoint[k][1] );
		SetMonsAnim( wI, swF, swB, TRUE, ANIM_STAND, 5 );
		break;
	default:
		SetMonsPoint( wI, swPC, swF, swB, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
		SetMonsAnim( wI, swF, swB, TRUE, ANIM_STAND, 5 );
	}
}

#endif

//====================================//
//				クリエート			  //
//====================================//

extern char ItemNoOpe[MAX_ITEM];

INPUT_STR SkillItemNameInputStr;
INIT_STR_STRUCT InitStrStructSkillItemName={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	&SkillItemNameInputStr,  0,  0,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_SHADOW,"",	  1,      ITEM_FREENAME_LEN,  0,     0
};

static int SkillCreateInfoNo;
static int SkillCreateInfoPage;

static char SkillCreateSel[MAX_ITEM];

static int SkillCreateFinishGra;

static int SkillCreateRecipeID;

extern int registItemGraNo[MATERIAL_SEL_MAX];
extern int registItemNum[MATERIAL_SEL_MAX];
extern int registItemIndex[MATERIAL_SEL_MAX];
static int registItemMaterial[MATERIAL_SEL_MAX];

static int targetItemGraNo;
static int targetItemNum;
static int targetItemIndex;

static int materialOK[RECIPE_MATERIAL_MAX];

static int RegistItemBoxOldPage[MATERIAL_SEL_MAX+1];

static char SkillCreateProc;

static unsigned int SkillCreateFinishTime;

extern char appendJewelSelItemFlag[MAX_DRAW_WIN_ITEM];

int getNumItem( int id );

int checkMaterialB( int kind );

BOOL checkRegistItem( int itempos);

extern int categoryAResult;
extern int categoryAResultGetExp;
extern int categoryAResultFame;
extern int categoryAResultLevelUp;
extern int categoryAResultStm;
extern int categoryAResultDex;
extern int categoryAResultInt;
extern int categoryAResultInjuryFlag;
extern int categoryAResultDrawGraNo;

extern int categoryBResult;
extern int categoryBResultGetExp;
extern int categoryBResultFame;
extern int categoryBResultLevelUp;
extern int categoryBResultStm;
extern int categoryBResultDex;
extern int categoryBResultInt;
extern int categoryBResultInjuryFlag;

#ifdef PUK3_MONSTER_HELPER_CANCEL

BOOL createUsePetHelp = FALSE;

void CancelRideSkillCreate()
{
	createUsePetHelp = FALSE;
}

#endif
#ifdef PUK3_MONSTER_HELPER_MMLOCK
BOOL CheckRideSkillCreate()
{
	return createUsePetHelp;
}
#endif
#ifdef PUK3_MONSTER_HELPER
// 作成物のランクがお手伝いの条件に合っているかをチェック
BOOL CheckPetHelpRecipeRank()
{
	if ( job.skill[CreateSkillNo].recipe[CreateRecipeNo].rank == 
		 GetPCMonsterCrystalLevel() ){
		return TRUE;
	}
	return FALSE;
}

// 修理鉴定のランクがお手伝いの条件に合っているかをチェック
BOOL CheckPetHelpRepAppRank()
{
	if ( targetItemIndex < 0 ) return FALSE;
	if ( pc.item[targetItemIndex].lv == 
		 GetPCMonsterCrystalLevel() ){
		return TRUE;
	}
	return FALSE;
}

// 装饰の宝石ランクがお手伝いの条件に合っているかをチェック
BOOL CheckPetHelpDecoRank()
{
	int i;

	for( i = 0; i < 6; i++ ){
		// 材料が登録されているなら
		if ( registItemIndex[i] >= 0 ){
			if( pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind == ITEM_JEWEL
			  || pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind == ITEM_MATERIAL
			  || pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind == ITEM_MATERIAL_B){
				break;
			}
		}
	}
	if ( i >= 6 ) return FALSE;
	if ( pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].lv != GetPCMonsterCrystalLevel() ) return FALSE;

	return TRUE;
}
#endif

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openSkillCreateWindow( int SkillNo, int RecipeNo, int SelTech )
{
	CreateSkillNo = SkillNo;
	CreateRecipeNo = RecipeNo;
	CreateSelTech = SelTech;
	SkillCreateRecipeID = job.skill[CreateSkillNo].recipe[CreateRecipeNo].id;

	SkillCreateProc = 0;

	// ＰＣの位置记忆
	memoryMapGridPos( mapGx, mapGy );

	RebirthOKCntInc();

	RebirthOffCntInc();

	SkillMoveOffCnt++;

	SkillIconCnt++;
	switch( job.skill[CreateSkillNo].id ){
	// 调理
	case 215:
		if ( GetHeadIcon() != HEADICON_COOK ) SetHeadIcon(HEADICON_COOK);
		break;
	// 鉴定
	case TECH_MODE_JUDGEITEM:
		if ( GetHeadIcon() != HEADICON_JUDGE ) SetHeadIcon(HEADICON_JUDGE);
		break;
	// 修理
	case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
		if ( GetHeadIcon() != HEADICON_REPAIR ) SetHeadIcon(HEADICON_REPAIR);
		break;
	// 制造
	default:
		if ( GetHeadIcon() != HEADICON_MAKE ) SetHeadIcon(HEADICON_MAKE);
		break;
	}

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindow( MENU_WINDOW_SKILLCREATE, OPENMENUWINDOW_HIT, 0 );
}

ACTION *openSkillRepAppWindow( int SkillNo, int SelTech )
{
	CreateSkillNo = SkillNo;
	CreateSelTech = SelTech;

	SkillCreateProc = 3;

	// ＰＣの位置记忆
	memoryMapGridPos( mapGx, mapGy );

	RebirthOKCntInc();

	RebirthOffCntInc();

	SkillMoveOffCnt++;

	SkillIconCnt++;
	switch( job.skill[CreateSkillNo].id ){
	// 调理
	case 215:
		if ( GetHeadIcon() != HEADICON_COOK ) SetHeadIcon(HEADICON_COOK);
		break;
	// 鉴定
	case TECH_MODE_JUDGEITEM:
		if ( GetHeadIcon() != HEADICON_JUDGE ) SetHeadIcon(HEADICON_JUDGE);
		break;
	// 修理
	case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
		if ( GetHeadIcon() != HEADICON_REPAIR ) SetHeadIcon(HEADICON_REPAIR);
		break;
	// 制造
	default:
		if ( GetHeadIcon() != HEADICON_MAKE ) SetHeadIcon(HEADICON_MAKE);
		break;
	}

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindow( MENU_WINDOW_SKILLCREATE, OPENMENUWINDOW_HIT, 0 );
}

ACTION *openSkillDecoWindow( int SkillNo, int SelTech )
{
	CreateSkillNo = SkillNo;
	CreateSelTech = SelTech;

	SkillCreateProc = 6;

	// ＰＣの位置记忆
	memoryMapGridPos( mapGx, mapGy );

	RebirthOKCntInc();

	RebirthOffCntInc();

	SkillMoveOffCnt++;

	SkillIconCnt++;
	switch( job.skill[CreateSkillNo].id ){
	// 调理
	case 215:
		if ( GetHeadIcon() != HEADICON_COOK ) SetHeadIcon(HEADICON_COOK);
		break;
	// 鉴定
	case TECH_MODE_JUDGEITEM:
		if ( GetHeadIcon() != HEADICON_JUDGE ) SetHeadIcon(HEADICON_JUDGE);
		break;
	// 修理
	case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
		if ( GetHeadIcon() != HEADICON_REPAIR ) SetHeadIcon(HEADICON_REPAIR);
		break;
	// 制造
	default:
		if ( GetHeadIcon() != HEADICON_MAKE ) SetHeadIcon(HEADICON_MAKE);
		break;
	}

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindow( MENU_WINDOW_SKILLCREATE, OPENMENUWINDOW_HIT, 0 );
}

void RegistEntryFree( int Num )
{
	if (Num<6){
		if ( registItemIndex[Num] < 0 ) return;

		if ( SkillCreateProc == 6 ) appendJewelSelItemFlag[ registItemIndex[Num] ] = 0;

		if (registItemMaterial[Num]>=0) materialOK[ registItemMaterial[Num] ] = 0;

		ItemNoOpe[ registItemIndex[Num]+MAX_EQUIP_ITEM ]--;
		SkillCreateSel[ registItemIndex[Num]+MAX_EQUIP_ITEM ] = 0;

		registItemIndex[Num]=-1;
		registItemGraNo[Num]=-1;
		registItemNum[Num] = 0;
		registItemMaterial[Num] = -1;
	}else{
		if ( targetItemIndex < 0 ) return;

		ItemNoOpe[targetItemIndex]--;
		SkillCreateSel[targetItemIndex] = 0;

		targetItemIndex = -1;
		targetItemGraNo = -1;
		targetItemNum = -1;

		SetDialogMenuChat();
	}
}


// アイテムの位置が移动された时の处理
// 引	form ---- 移动元	to ---- 移动先
void swapCreateItemRelation( int from, int to )
{
	int i,s;

	if ( !WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo ) return;

	from -= MAX_EQUIP_ITEM;
	to -= MAX_EQUIP_ITEM;

	for(i=0;i<MATERIAL_SEL_MAX;i++){
		if ( registItemIndex[i] < 0 ) continue;

		if ( registItemIndex[i] == from ){
			if ( to < 0 ){
				RegistEntryFree(i);
			}else{
				registItemIndex[i] = to;
			}
		}else if ( registItemIndex[i] == to ){
			if ( from < 0 ){
				RegistEntryFree(i);
			}else{
				registItemIndex[i] = from;
			}
		}
	}

	from += MAX_EQUIP_ITEM;
	to += MAX_EQUIP_ITEM;

	if ( targetItemIndex == from ){
		targetItemIndex = to;
		targetItemGraNo = pc.item[targetItemIndex].graNo;
		targetItemNum = getNumItem( pc.item[targetItemIndex+MAX_EQUIP_ITEM].id );
	}
	else if ( targetItemIndex == to ){
		targetItemIndex = from;
		targetItemGraNo = pc.item[targetItemIndex].graNo;
		targetItemNum = getNumItem( pc.item[targetItemIndex].id );
	}

	if ( from>=0 && to>=0 ){
		s = SkillCreateSel[from];
		SkillCreateSel[from] = SkillCreateSel[to];
		SkillCreateSel[to] = s;

		if ( SkillCreateProc == 6 ){
			s = appendJewelSelItemFlag[from];
			appendJewelSelItemFlag[from] = appendJewelSelItemFlag[to];
			appendJewelSelItemFlag[to] = s;
		}
	}

	// アイテムロックの交换は别部分で行われるのでここではしない
}

#ifdef PUK2_NEWDRAG

void DragSkillCreateItemFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		if ( itemNo < 6 ){
		}else{
			if ( targetItemGraNo < 0 ) WinDD_DragCancel();
		}

		// 右键したらアイテムドロップ
		if ( mouse.onceState & MOUSE_LEFT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_SKILLCREATE, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragSkillCreateItemFunc );
		}
		// 右クリックしたらドラッグ終了
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragCancel();

#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		if ( itemNo < 6 ){
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, registItemGraNo[itemNo], 0, &bm );
		}else{
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, targetItemGraNo, 0, &bm );
		}
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		RegistEntryFree( itemNo );
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
		break;
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		RegistEntryFree( itemNo );
		break;
	}
}

void DragSkillCreateItem( int itemNo )
{
	// ドラッグ开始
	WinDD_DragStart( WINDD_SKILLCREATE, (void *)itemNo, DragSkillCreateItemFunc );
}

#endif

void ChangeModeRegistSel( WINDOW_INFO *wi )
{
	int i, j, k;

	// 登録データ解放
	for(i=0;i<MATERIAL_SEL_MAX;i++) RegistEntryFree(i);
	RegistEntryFree(6);

	// データ初期化
	for(i=0;i<MAX_ITEM;i++) SkillCreateSel[i] = 0;

	for(i=0;i<MATERIAL_SEL_MAX;i++){
		registItemGraNo[i] = -1;
		registItemNum[i] = 0;
		registItemIndex[i] = -1;
		registItemMaterial[i] = -1;

		materialOK[i] = 0;

		RegistItemBoxOldPage[i] = -2;
	}
	RegistItemBoxOldPage[6] = -2;

	// 素材表示设定
	j = 0;
	for( i = RECIPE_MATERIAL_MAX-1; i >= 0; i-- ){
		materialOK[i] = 1;
		if( job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].id < 0 ) continue;

		materialOK[i] = 0;

		k = job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].num;
		if (k<=0) j = 1;

		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1+j].Switch )->text,
			"%-24s%3d", job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].name, k ); //MLHIDE
		j++;
	}
	for(;j<RECIPE_MATERIAL_MAX;j++) ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1+j].Switch )->text[0] = '\0';

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillCreateWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillMaterialList;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = TRUE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntry;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = TRUE;


	wi->sw[EnumSkillCreateItemFinishGra].Enabled = FALSE;


	wi->sw[EnumSkillCreateItemEntry7].Enabled = FALSE;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = FALSE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = FALSE;
	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_STAND;

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

	SkillCreateFinishGra = -1;

#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
#endif

	SkillCreateProc = 0;
}

void ChangeModeMaking( WINDOW_INFO *wi )
{
	int i;

#ifdef PUK3_MONSTER_HELPER
	// ペットお手伝いの発动をチェック
	usePetHelp = FALSE;
	if ( CheckPetHelp() && CheckPetHelpRecipeRank() && CheckPetHelpNeedFp() ){
		usePetHelp = TRUE;
	}
#endif
#ifdef PUK3_MONSTER_HELPER_CANCEL
	createUsePetHelp = usePetHelp;
#endif
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(865, "　　　…　制作中　…") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText4].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, "" );

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillCreateWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillCreateResultPanel2;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = TRUE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntryDisp;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = TRUE;

	wi->sw[EnumSkillCreateItemFinishGra].Enabled = TRUE;

	wi->sw[EnumSkillCreateItemEntry7].Enabled = FALSE;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = FALSE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = FALSE;
	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
	if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;

	SkillCreateFinishTime = 5000 + (rand() % 6) * 1000 + job.skill[CreateSkillNo].recipe[CreateRecipeNo].rank * 3000;
	if (skillrebirth){
		SkillCreateFinishTime = (SkillCreateFinishTime * RebirthCreateRate[ pc.rebirthLevel ]) / 100;
	}
#ifdef PUK3_MONSTER_HELPER
	if (usePetHelp){
		SkillCreateFinishTime = (SkillCreateFinishTime * PetHelpCreateRate[0][ GetPCMonsterCrystalLevel() ]) / 100;
	}
#endif

#ifdef VERSION_TW
	//台服发送生产采集技能所需的时间
	nrproto_ProduceTime_send(sockfd, SkillCreateFinishTime / 1000);
#endif
	SkillCreateFinishTime += GetTickCount();

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

	SkillCreateFinishGra = -1;

#ifdef PUK3_MONSTER_HELPER
	SkillMonsHelperStart = GetTickCount();
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
	}else{
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif

	SkillCreateProc = 1;
}

void ChangeModeFinish( WINDOW_INFO *wi )
{
	int i;
	char *p;
	char str[256];

#ifdef PUK3_MONSTER_HELPER_MMLOCK
	// 作成終わったのでフラグ落としておく
	// 落とさないと、ＭＭが使えないため
	createUsePetHelp = FALSE;
#endif
	// 登録データ解放
	for(i=0;i<MATERIAL_SEL_MAX;i++) RegistEntryFree(i);

	// 作ったものの名称
	if ( job.skill[CreateSkillNo].id == 255 ){
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, recipeNameBak );
	}else{
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, job.skill[CreateSkillNo].recipe[CreateRecipeNo].name );
	}

	// 成功か失败か
	if ( categoryAResult ){
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(866, "　制作成功。") );
		play_se( SE_NO_SKILL_SUCCESS, 320, 240 );
	}else{
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(867, "　制作失败。") );
		play_se( SE_NO_SKILL_FAILED, 320, 240 );
	}

	// スキル经验值と等级アップ
	if (categoryAResultLevelUp){
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, ML_STRING(868, "升级！") );
	}else{
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, ML_STRING(541, "技能经验 %+d"), categoryAResultGetExp );
	}

	// 状态变化
	p = ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText4].Switch )->text;
	p[0] = '\0';
	if( categoryAResultStm ){
		sprintf( str, ML_STRING(869, "　耐力 %+d"), categoryAResultStm );
		strcat( p, str );
	}
	if( categoryAResultDex ){
		sprintf( str, ML_STRING(870, "　敏捷 %+d"), categoryAResultDex );
		strcat( p, str );
	}
	if( categoryAResultInt ){
		sprintf( str, ML_STRING(871, "　智力 %+d"), categoryAResultInt );
		strcat( p, str );
	}

	// 怪我情报
	if( categoryAResultInjuryFlag ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, ML_STRING(872, "受伤了！") );
	else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, "" );

	// 作成されたアイテムを表示
	SkillCreateFinishGra = -1;
	if ( categoryAResult ) SkillCreateFinishGra = categoryAResultDrawGraNo;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillCreateWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillMaterialList;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = TRUE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntryDisp;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = TRUE;


	wi->sw[EnumSkillCreateItemFinishGra].Enabled = TRUE;


	wi->sw[EnumSkillCreateItemEntry7].Enabled = FALSE;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = TRUE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = TRUE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = TRUE;
	if ( categoryAResult ){
		( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
		if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
	}else{
		( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
		if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
	}

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		if ( categoryAResult ){
			SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_MAGIC, 5 );
		}else{
			SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_DAMAGE, 5 );
		}
	}else{
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif

	SkillCreateProc = 2;
}

void GiveResultCreate()
{
	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) ChangeModeFinish( WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo );
}


void ChangeModeTargetSel( WINDOW_INFO *wi )
{
	int i;

	// 登録データ解放
	for(i=0;i<MATERIAL_SEL_MAX;i++) RegistEntryFree(i);
	RegistEntryFree(6);

	// データ初期化
	for(i=0;i<MAX_ITEM;i++) SkillCreateSel[i] = 0;

	for(i=0;i<MATERIAL_SEL_MAX;i++){
		registItemGraNo[i] = -1;
		registItemNum[i] = 0;
		registItemIndex[i] = -1;
		registItemMaterial[i] = -1;

		materialOK[i] = 0;

		RegistItemBoxOldPage[i] = -2;
	}
	RegistItemBoxOldPage[6] = -2;

	switch(job.skill[CreateSkillNo].id){
	case TECH_MODE_JUDGEITEM:
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(873, "鉴定的物品") );
		break;
	case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(874, "修理的物品") );
		break;
	case TECH_MODE_MARK:
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(875, "刻印的物品") );
		break;
	}
	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillMaterialList;
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(876, "　请选择。") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText4].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, "" );

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillRepairWindow;

	for(i=0;i<6;i++) wi->sw[EnumSkillCreateItemEntry1+i].Enabled = FALSE;
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = FALSE;


	wi->sw[EnumSkillCreateItemFinishGra].Enabled = FALSE;


	wi->sw[EnumSkillCreateItemEntry7].Enabled = TRUE;
	wi->sw[EnumSkillCreateItemEntry7].func = MenuSwitchSkillTargetItemEntry;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = FALSE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = FALSE;
	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_STAND;

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

	SkillCreateFinishGra = -1;

#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
#endif

	SkillCreateProc = 3;
}

void ChangeModeWarking( WINDOW_INFO *wi )
{
	int i;

#ifdef PUK3_MONSTER_HELPER
	// ペットお手伝いの発动をチェック
	usePetHelp = FALSE;
	if ( CheckPetHelp() && CheckPetHelpRepAppRank() && CheckPetHelpNeedFp() ){
		if ( job.skill[CreateSkillNo].id != TECH_MODE_MARK ){
			usePetHelp = TRUE;
		}
	}
#endif
#ifdef PUK3_MONSTER_HELPER_CANCEL
	createUsePetHelp = usePetHelp;
#endif
	switch(job.skill[CreateSkillNo].id){
	case TECH_MODE_JUDGEITEM:
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(877, "　　　…　鉴定中　…") );
		break;
	case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(878, "　　　…　修理中　…") );
		break;
	case TECH_MODE_MARK:
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(879, "　　　…　刻印中　…") );
		break;
	}
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText4].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, "" );

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillRepairWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillCreateResultPanel2;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = FALSE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntryDisp;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = FALSE;

	wi->sw[EnumSkillCreateItemFinishGra].Enabled = FALSE;

	wi->sw[EnumSkillCreateItemEntry7].Enabled = TRUE;
	wi->sw[EnumSkillCreateItemEntry7].func = MenuSwitchSkillTargetItemEntryDisp;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = TRUE;
	if ( job.skill[CreateSkillNo].id == TECH_MODE_MARK ) wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = FALSE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = FALSE;
	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
	if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;

	SkillCreateFinishTime = 5000 + (rand() % 6) * 1000 + pc.item[targetItemIndex].lv * 3000;
	if (skillrebirth){
		SkillCreateFinishTime = (SkillCreateFinishTime * RebirthCreateRate[ pc.rebirthLevel ]) / 100;
	}
#ifdef PUK3_MONSTER_HELPER
	if (usePetHelp){
		SkillCreateFinishTime = (SkillCreateFinishTime * PetHelpCreateRate[1][ GetPCMonsterCrystalLevel() ]) / 100;
	}
#endif

#ifdef VERSION_TW
	//台服发送生产采集技能所需的时间
	nrproto_ProduceTime_send(sockfd, SkillCreateFinishTime / 1000);
#endif
	SkillCreateFinishTime += GetTickCount();

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

	SkillCreateFinishGra = -1;

#ifdef PUK3_MONSTER_HELPER
	SkillMonsHelperStart = GetTickCount();
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
	}else{
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif

	SkillCreateProc = 4;
}

void ChangeModeFinish2( WINDOW_INFO *wi )
{
	int i;
	char *p;
	char str[256];

	int Index;

#ifdef PUK3_MONSTER_HELPER_MMLOCK
	// 作成終わったのでフラグ落としておく
	// 落とさないと、ＭＭが使えないため
	createUsePetHelp = FALSE;
#endif
	Index = targetItemIndex;

	// 登録データ解放
	if ( categoryBResult ){
		if (job.skill[CreateSkillNo].id!=TECH_MODE_MARK) RegistEntryFree(6);
	}

	// 作ったものの名称
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, pc.item[Index].name );

	// 成功か失败か
	switch(job.skill[CreateSkillNo].id){
	case TECH_MODE_JUDGEITEM:
		if ( categoryBResult ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(880, "　鉴定成功。") );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(881, "　鉴定失败。") );
		break;
	case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
		if ( categoryBResult ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(882, "　修理成功。") );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(883, "　修理失败。") );
		break;
	case TECH_MODE_MARK:
		if ( categoryBResult ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(884, "　刻印成功。") );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(885, "　刻印失败。") );
		break;
	}
	if (categoryBResult) play_se( SE_NO_SKILL_SUCCESS, 320, 240 );
	else play_se( SE_NO_SKILL_FAILED, 320, 240 );

	// スキル经验值と等级アップ
	if (categoryBResultLevelUp){
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, ML_STRING(868, "升级！") );
	}else{
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, ML_STRING(541, "技能经验 %+d"), categoryBResultGetExp );
	}

	// 状态变化
	p = ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText4].Switch )->text;
	p[0] = '\0';
	if( categoryAResultStm ){
		sprintf( str, ML_STRING(869, "　耐力 %+d"), categoryBResultStm );
		strcat( p, str );
	}
	if( categoryAResultDex ){
		sprintf( str, ML_STRING(870, "　敏捷 %+d"), categoryBResultDex );
		strcat( p, str );
	}
	if( categoryAResultInt ){
		sprintf( str, ML_STRING(871, "　智力 %+d"), categoryBResultInt );
		strcat( p, str );
	}

	// 怪我情报
	if( categoryBResultInjuryFlag ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, ML_STRING(872, "受伤了！") );
	else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, "" );

	// 作成されたアイテムを表示
	SkillCreateFinishGra = -1;
	if ( categoryBResult ) SkillCreateFinishGra = pc.item[Index].graNo;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillRepairWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillMaterialList;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = FALSE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntryDisp;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = FALSE;


	wi->sw[EnumSkillCreateItemFinishGra].Enabled = FALSE;


	wi->sw[EnumSkillCreateItemEntry7].Enabled = TRUE;
	wi->sw[EnumSkillCreateItemEntry7].func = MenuSwitchSkillTargetItemEntryDisp;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = TRUE;
	if ( job.skill[CreateSkillNo].id == TECH_MODE_MARK ) wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = TRUE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = TRUE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = TRUE;
	if ( categoryBResult ){
		( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
		if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
	}else{
		( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
		if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
	}

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		if ( categoryBResult ){
			SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_MAGIC, 5 );
		}else{
			SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_DAMAGE, 5 );
		}
	}else{
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif

	SkillCreateProc = 5;
}

void GiveResultRepApp()
{
	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) ChangeModeFinish2( WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo );
}



void ChangeModeRegistSel2( WINDOW_INFO *wi )
{
	int i, j;

	// 登録データ解放
	for(i=0;i<MATERIAL_SEL_MAX;i++) RegistEntryFree(i);
	RegistEntryFree(6);

	// データ初期化
	for(i=0;i<MAX_ITEM;i++) SkillCreateSel[i] = 0;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++) appendJewelSelItemFlag[i] = 0;

	for(i=0;i<MATERIAL_SEL_MAX;i++){
		registItemGraNo[i] = -1;
		registItemNum[i] = 0;
		registItemIndex[i] = -1;
		registItemMaterial[i] = -1;

		materialOK[i] = 0;

		RegistItemBoxOldPage[i] = -2;
	}
	RegistItemBoxOldPage[6] = -2;

	// 素材表示设定
	j = 0;
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(886, "装饰") );
	sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, "                     FP%3d", DECORATIONFP ); //MLHIDE
	for(j=2;j<RECIPE_MATERIAL_MAX;j++) ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1+j].Switch )->text[0] = '\0';

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillCreateWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillCreateResultPanel2;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = TRUE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntry;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = TRUE;


	wi->sw[EnumSkillCreateItemFinishGra].Enabled = FALSE;


	wi->sw[EnumSkillCreateItemEntry7].Enabled = FALSE;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = FALSE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = FALSE;
	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_STAND;

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

	SkillCreateFinishGra = -1;
#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
#endif

	SkillCreateProc = 6;
}

void ChangeModeDecorating( WINDOW_INFO *wi )
{
	int i;

#ifdef PUK3_MONSTER_HELPER
	// ペットお手伝いの発动をチェック
	usePetHelp = FALSE;
	if ( CheckPetHelp() && CheckPetHelpDecoRank() && CheckPetHelpNeedFp() ){
		usePetHelp = TRUE;
	}
#endif
#ifdef PUK3_MONSTER_HELPER_CANCEL
	createUsePetHelp = usePetHelp;
#endif
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(887, "　　　…　装饰中　…") );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText2].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText4].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillCreateText5].Switch )->text, "" );

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateWindow].Switch )->graNo = GID_SkillCreateWindow;

	( (GRAPHIC_SWITCH *)wi->sw[EnumSkillCreateUnderBar].Switch )->graNo = GID_SkillCreateResultPanel2;

	for(i=0;i<6;i++){
		wi->sw[EnumSkillCreateItemEntry1+i].Enabled = TRUE;
		wi->sw[EnumSkillCreateItemEntry1+i].func = MenuSwitchSkillCreateItemEntryDisp;
	}
	wi->sw[EnumSkillCreateItemEntryGra].Enabled = TRUE;

	wi->sw[EnumSkillCreateItemFinishGra].Enabled = TRUE;

	wi->sw[EnumSkillCreateItemEntry7].Enabled = FALSE;
	wi->sw[EnumSkillCreateItemFinishGra2].Enabled = FALSE;

	wi->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateRetryBtn].Enabled = FALSE;
	wi->sw[EnumSkillCreateEndBtn].Enabled = FALSE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->AnimNoLoop = FALSE;
	( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
	if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_NOD;
	if ( i == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;


#ifdef PUK2_DECORATION_TIME
	SkillCreateFinishTime = 8000;
#else
	SkillCreateFinishTime = 5000 + (rand() % 6) * 1000 + job.skill[CreateSkillNo].recipe[CreateRecipeNo].rank * 3000;
#endif
	if (skillrebirth){
		SkillCreateFinishTime = (SkillCreateFinishTime * RebirthCreateRate[ pc.rebirthLevel ]) / 100;
	}
#ifdef PUK3_MONSTER_HELPER
	if (usePetHelp){
		SkillCreateFinishTime = (SkillCreateFinishTime * PetHelpCreateRate[0][ GetPCMonsterCrystalLevel() ]) / 100;
	}
#endif

#ifdef VERSION_TW
	//台服发送生产采集技能所需的时间
	nrproto_ProduceTime_send(sockfd, SkillCreateFinishTime / 1000);
#endif
	SkillCreateFinishTime += GetTickCount();

	wi->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameInput].Enabled = FALSE;
	wi->sw[EnumSkillCreateNameSet].Enabled = FALSE;

	SkillCreateFinishGra = -1;

#ifdef PUK3_MONSTER_HELPER
	SkillMonsHelperStart = GetTickCount();
	SetMonsPoint( wi, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
	}else{
		SetMonsAnim( wi, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif

	SkillCreateProc = 7;
}



BOOL closeSkillCreate()
{
	int i;

	// 电卓ウィンドウを呼び出したのが自分の场合、电卓ウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_CALCULATOR].wininfo){
		struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
		if (wm->WinType == MENU_WINDOW_SKILLCREATE){
			wm->wininfo->flag |= WIN_INFO_DEL;
		}
	}

#ifdef PUK3_MONSTER_HELPER_MMLOCK
	createUsePetHelp = FALSE;
#endif
	RebirthOKCntDec();

	RebirthOffCntDec();

	SkillMoveOffCnt--;

	// 登録データ解放
	for(i=0;i<MATERIAL_SEL_MAX;i++) RegistEntryFree(i);
	RegistEntryFree(6);

	SkillIconCnt--;
	if ( !SkillIconCnt ){
		switch( GetHeadIcon() ){
		case HEADICON_MAKE:case HEADICON_COOK:case HEADICON_JUDGE:case HEADICON_REPAIR:
			SetHeadIcon( HEADICON_DELETE );
			break;
		}
	}

	return TRUE;
}

BOOL MenuWindowSkillCreateBf( int mouse )
{
	int i;
#ifdef PUK3_MONSTER_HELPER
	BOOL drawflag, helpflag;
#endif

	if ( mouse == WIN_INIT ){
		pActCreate = NULL;

		SkillCreateInfoNo = -1;
		SkillCreateInfoPage = 0;

		// 初期化がされていないと、ChangeModeRegistSel がデータを解放しようとするので、ここでデータ初期化
		for(i=0;i<MAX_ITEM;i++) SkillCreateSel[i] = 0;

		for(i=0;i<MATERIAL_SEL_MAX;i++){
			registItemGraNo[i] = -1;
			registItemNum[i] = 0;
			registItemIndex[i] = -1;
			registItemMaterial[i] = -1;

			RegistItemBoxOldPage[i] = -2;
		}

		targetItemIndex = -1;
		targetItemGraNo = -1;
		targetItemNum = -1;

		switch(SkillCreateProc){
		case 0:
			// モードチェンジ
			ChangeModeRegistSel(wI);
			break;
		case 3:
			ChangeModeTargetSel(wI);
			break;
		case 6:
			ChangeModeRegistSel2(wI);
			break;
		}

		wI->sw[EnumSkillCreateInfo1].Enabled = FALSE;
		wI->sw[EnumSkillCreateInfo2].Enabled = FALSE;

		return TRUE;
	}
	// 作成系のとき
	if ( SkillCreateProc <= 2 ){
		// レシピの位置がずれてないか确认
		if ( SkillCreateRecipeID != job.skill[CreateSkillNo].recipe[CreateRecipeNo].id ){
			for(i=0;;i++){
				// レシピが见つけられなかったらウィンドウ关闭
				if( job.skill[CreateSkillNo].recipe[i].id < 0 ){
					wI->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					break;
				}
				// レシピが见つかったら
				if ( SkillCreateRecipeID == job.skill[CreateSkillNo].recipe[i].id ){
					CreateRecipeNo = i;
					break;
				}
			}
		}
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 1, 1 ) ){
		WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
#ifdef PUK3_MONSTER_HELPER
	drawflag = FALSE;
	helpflag = FALSE;
	if ( CheckPetHelp() ) helpflag = TRUE;

	if ( helpflag ){
		// 选择时
		if ( SkillCreateProc==0 ){
			if ( CheckPetHelpRecipeRank() ){
				drawflag = TRUE;
				if ( CheckPetHelpNeedFp() ){
					SetMonsAnim( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
				}else{
					SetMonsAnim( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
				}
			}
		}
		if ( SkillCreateProc==3 ){
			if ( job.skill[CreateSkillNo].id != TECH_MODE_MARK ){
				if ( CheckPetHelpRepAppRank() ){
					drawflag = TRUE;
					if ( CheckPetHelpNeedFp() ){
						SetMonsAnim( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
					}else{
						SetMonsAnim( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
					}
				}
			}
		}
		if ( SkillCreateProc==6 ){
			if ( CheckPetHelpDecoRank() ){
				drawflag = TRUE;
				SetMonsPoint( wI, EnumSkillCreatePc,
					 EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B,
					 SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
				if ( CheckPetHelpNeedFp() ){
					SetMonsAnim( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, TRUE, ANIM_STAND, 5 );
				}else{
					SetMonsAnim( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, FALSE, ANIM_DEAD, 5 );
				}
			}
		}
		switch( SkillCreateProc ){
		case 0:case 3:case 6:
			if (drawflag){
				SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, GetPetHelperGraNo() );
				// 表示するために呼ぶ
				SetMonsPoint( wI, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B,
					 SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
			}else{
				SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, 0 );
			}
			break;
		case 1:case 4:case 7:
			if (usePetHelp){
				SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, GetPetHelperGraNo() );
			}else{
				SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, 0 );
			}
			break;
		case 5:
			if ( job.skill[CreateSkillNo].id == TECH_MODE_MARK ) break;
		case 2:
			if (usePetHelp){
				SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, petHelperGraNo );
			}else{
				SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, 0 );
			}
			break;
		}
	}else{
		SetMonsChar( wI, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, 0 );
	}
	if (usePetHelp){
		if ( SkillCreateProc==1 || SkillCreateProc==4 || SkillCreateProc==7 ){
#ifdef PUK3_MONSTER_HELPER_CANCEL
			if ( helpflag && createUsePetHelp ){
#else
			if ( helpflag ){
#endif
				MonsHelperMove( wI, EnumSkillCreatePc, EnumSkillCreateMonsHelper_F, EnumSkillCreateMonsHelper_B, SkillCreateFinishTime );
			}else{
				// もしヘルプ中にヘルプ条件满たさなくなったら选择画面に返回
				switch(SkillCreateProc){
				case 1: ChangeModeRegistSel(wI); break;
				case 4: ChangeModeTargetSel(wI); break;
				case 7: ChangeModeRegistSel2(wI); break;
				}
			}
		}
	}
#endif

	if (SkillCreateProc==1){
		// 演出时间过ぎたならプロトコル送信
		if ( SkillCreateFinishTime <= GetTickCount() ){
			char str[256];
			char str2[128];

			// 送信文字列作成
			sprintf( str, "%d", job.skill[CreateSkillNo].recipe[CreateRecipeNo].id ); //MLHIDE
			// 先ずは必须材料を登録
			for( i = 0; i < MATERIAL_SEL_MAX; i++ ){
				if( registItemIndex[i] >= 0 ){
					if ( registItemMaterial[i] >= 0 ){
						sprintf( str2, "|%d", MAX_EQUIP_ITEM+registItemIndex[i] );      //MLHIDE
						strcat( str, str2 );
					}
				}
			}
			// 次に必须でない材料を登録
			for( i = 0; i < MATERIAL_SEL_MAX; i++ ){
				if( registItemIndex[i] >= 0 ){
					if ( registItemMaterial[i] < 0 ){
						sprintf( str2, "|%d", MAX_EQUIP_ITEM+registItemIndex[i] );      //MLHIDE
						strcat( str, str2 );
					}
				}
			}
			// S-JIS から EUC に变换
			sjisStringToEucString( str );

			// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
			if (skillrebirth){
				nrproto_TU_send( sockfd, CreateSkillNo+USE_REBIRTH_ADD, CreateSelTech, -1, str );
			}else if (usePetHelp){
				petHelperGraNo = GetPetHelperGraNo();
				nrproto_TU_send( sockfd, CreateSkillNo+USE_PETHELP_ADD, CreateSelTech, -1, str );
			}else{
				nrproto_TU_send( sockfd, CreateSkillNo, CreateSelTech, -1, str );
			}
#else
			nrproto_TU_send( sockfd, CreateSkillNo+(skillrebirth?100:0), CreateSelTech, -1, str );
#endif

			SkillCreateProc = 2;
		}
	}
	if (SkillCreateProc==4){
		// 演出时间过ぎたならプロトコル送信
		if ( SkillCreateFinishTime <= GetTickCount() ){
			char str[256];

			if ( job.skill[CreateSkillNo].id==TECH_MODE_MARK ) strcpy( str, SkillItemNameInputStr.buffer );
			else str[0] = '\0';
			// S-JIS から EUC に变换
			sjisStringToEucString( str );
			// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
			if (skillrebirth){
				nrproto_TU_send( sockfd, CreateSkillNo+USE_REBIRTH_ADD, selTech, targetItemIndex, str );
			}else if (usePetHelp){
				petHelperGraNo = GetPetHelperGraNo();
				nrproto_TU_send( sockfd, CreateSkillNo+USE_PETHELP_ADD, selTech, targetItemIndex, str );
			}else{
				nrproto_TU_send( sockfd, CreateSkillNo, selTech, targetItemIndex, str );
			}
#else
			nrproto_TU_send( sockfd, CreateSkillNo+(skillrebirth?100:0), selTech, targetItemIndex, str );
#endif

			SkillCreateProc = 5;
		}
	}
	if (SkillCreateProc==7){
		// 演出时间过ぎたならプロトコル送信
		if ( SkillCreateFinishTime <= GetTickCount() ){
			char str[256];
			int r0, r1;

			for( i = 0; i < 6; i++ ){
				// 材料が登録されているなら
				if ( registItemIndex[i] >= 0 ){
					if( pc.item[ registItemIndex[i]+8 ].kind == ITEM_JEWEL
					  || pc.item[ registItemIndex[i]+8 ].kind == ITEM_MATERIAL
					  || pc.item[ registItemIndex[i]+8 ].kind == ITEM_MATERIAL_B){
						r1 = MAX_EQUIP_ITEM+registItemIndex[i];
					}else{
						r0 = MAX_EQUIP_ITEM+registItemIndex[i];
					}
				}
			}
			sprintf( str, "%d|%d", r0, r1 );                                   //MLHIDE

			// S-JIS から EUC に变换
			sjisStringToEucString( str );
			// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
			if (skillrebirth){
				nrproto_TU_send( sockfd, CreateSkillNo+USE_REBIRTH_ADD, selTech, -1, str );
			}else if (usePetHelp){
				petHelperGraNo = GetPetHelperGraNo();
				nrproto_TU_send( sockfd, CreateSkillNo+USE_PETHELP_ADD, selTech, -1, str );
			}else{
				nrproto_TU_send( sockfd, CreateSkillNo, selTech, -1, str );
			}
#else
			nrproto_TU_send( sockfd, CreateSkillNo+(skillrebirth?100:0), selTech, -1, str );
#endif

			// 宝石を追加するアイテム名の保存
			strcpy( recipeNameBak, pc.item[r0].name);

			SkillCreateProc = 8;
		}
	}

	if ( SkillCreateProc==3 && job.skill[CreateSkillNo].id==TECH_MODE_MARK ){
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(875, "刻印的物品") );
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(876, "　请选择。") );

		if( pNowInputStr != &SkillItemNameInputStr ){
			wI->sw[EnumSkillCreateNameSet].Enabled = TRUE;
			wI->sw[EnumSkillCreateNameInputHit].Enabled = TRUE;
			wI->sw[EnumSkillCreateNameInput].Enabled = FALSE;
		}
	}else if ( (SkillCreateProc==0)||(SkillCreateProc==3)||(SkillCreateProc==6) ) wI->sw[EnumSkillCreateTryBtn].Enabled = TRUE;
	else{
		wI->sw[EnumSkillCreateTryBtn].Enabled = FALSE;
		wI->sw[EnumSkillCreateNameSet].Enabled = FALSE;
	}

	// キャラクター画像设定
	i = getNewGraphicNo(pc.graNo);
	i = GetCharaEmptyGra( i, 0 );
	( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_chr_no = i;
	( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_ang = 5;

	if ( pNowInputStr == &SkillItemNameInputStr ){
		// 文字入力栏移动
		SetInputStr( &InitStrStructSkillItemName, wI->wx+wI->sw[EnumSkillCreateNameInputHit].ofx, wI->wy+wI->sw[EnumSkillCreateNameInputHit].ofy, 2 );
	}

	SkillCreateInfoNo = -1;

	return TRUE;
}

BOOL MenuWindowSkillCreateAf( int mouse )
{
	ACTION *ActionAdd = ( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd;
	int i = ( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd->anim_chr_no;
	short x = wI->wx + 37;
	short y = wI->wy + 259;

	if ( SkillCreateProc==2 || SkillCreateProc==5 ){
		if ( ActionAdd->anim_no == ANIM_CHOKI || ActionAdd->anim_no == ANIM_ATTACK ){
			ActionAdd->anim_no = ANIM_ATTACK;
			if ( SPRPC_START <= i && i <= SPRPC_END ) ActionAdd->anim_no = ANIM_CHOKI;
			if ( SPRPC_START_V2 <= i && i <= SPRPC_END_V2 ) ActionAdd->anim_no = ANIM_CHOKI;
			if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ) ActionAdd->anim_no = ANIM_CHOKI;
			if ( i == SPR_shadow_mon ) ActionAdd->anim_no = ANIM_ATTACK;
		}else if ( ActionAdd->anim_no == ANIM_SAD || ActionAdd->anim_no == ANIM_DEAD ){
			ActionAdd->anim_no = ANIM_DEAD;
			if ( SPRPC_START <= i && i <= SPRPC_END ) ActionAdd->anim_no = ANIM_SAD;
			if ( SPRPC_START_V2 <= i && i <= SPRPC_END_V2 ) ActionAdd->anim_no = ANIM_SAD;
			if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ) ActionAdd->anim_no = ANIM_SAD;
			if ( i == SPR_shadow_mon ) ActionAdd->anim_no = ANIM_DEAD;
		}
	}
	if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ){
		( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd->bltfon = BLTF_NOCHG;
		( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd->bltf = BLTF_NOCHG;
	}else{
		( (ACTION_SWITCH *)wI->sw[EnumSkillCreatePc].Switch )->ActionAdd->bltfon = 0;
	}

	// アイテム信息
	if (SkillCreateInfoNo>=0) PcItemExplanationWindow( SkillCreateInfoNo, SkillCreateInfoPage );


	if ( targetItemIndex >= 0 ){
		StockFontBuffer( wI->wx+wI->sw[EnumSkillCreateText3].ofx, wI->wy+wI->sw[EnumSkillCreateText3].ofy,
			FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_SHADOW, SkillItemNameInputStr.buffer, 0, 0 );
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// スイッチ处理
//--------------------------------------------------------

BOOL MenuSwitchSkillCreateTry( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int i;
	char OKflag = 0;

	if (SkillCreateProc==0){
		for( i = 0; i < RECIPE_MATERIAL_MAX; i++ ){
			if (!materialOK[i]) break;
		}
		if (i < RECIPE_MATERIAL_MAX){
			wI->sw[no].Enabled = FALSE;
			return ReturnFlag;
		}
		if ( pc.fp >= job.skill[CreateSkillNo].recipe[CreateRecipeNo].fp*job.skill[CreateSkillNo].fpRate/100 ) OKflag = 1;
	}
	if (SkillCreateProc==3){
		if (targetItemIndex<0){
			wI->sw[no].Enabled = FALSE;
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText3].Switch )->text, "" );
			return ReturnFlag;
		}
		if (job.skill[CreateSkillNo].id == TECH_MODE_MARK){
			if ( pc.fp >= 10 ) OKflag = 1;
		}else{
			if ( pc.fp >= pc.item[targetItemIndex].lv*10 ) OKflag = 1;
			sprintf( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText3].Switch )->text,
				"                     FP%3d", pc.item[targetItemIndex].lv*10 );   //MLHIDE
		}

		switch(job.skill[CreateSkillNo].id){
		case TECH_MODE_JUDGEITEM:
			// すでに鉴定されてたら警告
			if( pc.item[targetItemIndex].checkFlag ) OKflag = 0;
			// 自分のスキルより上なら警告
			else if( pc.item[targetItemIndex].lv > job.skill[CreateSkillNo].lv+(skillrebirth?1:0) ) OKflag = 0;
			break;
		case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
			// 未鉴定アイテムなので警告。
			if( pc.item[targetItemIndex].checkFlag == 0 ) OKflag = 0;
			// 防具を选择した场合
			else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_WEPON && WhichEquipType( pc.item[targetItemIndex].kind ) == 2 ) OKflag = 0;
			// 武器を选择した场合
			else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_ARMOR && WhichEquipType( pc.item[targetItemIndex].kind ) == 1 ) OKflag = 0;
			// その他のアイテムを选择した场合
			else if( WhichEquipType( pc.item[targetItemIndex].kind ) == 0 ) OKflag = 0;
			// スキル等级が足りない场合
			else if( pc.item[targetItemIndex].lv > job.skill[CreateSkillNo].lv+(skillrebirth?1:0) ) OKflag = 0;
			break;
		case TECH_MODE_MARK:
			//すでに他人のハンコがある
			if( pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_HANKO
			  && !(pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_MYITEM) ) OKflag = 0;
			//すでに刻印されてる(ハンコはＯＫ）
			else if( pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_INCUSE
			  && !(pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_MYITEM) ) OKflag = 0;
			//未鉴定品
			else if( pc.item[targetItemIndex].checkFlag == 0 ) OKflag = 0;
			//スタックアイテムまたはクジ
			else if(pc.item[targetItemIndex].num > 0 || pc.item[targetItemIndex].kind == ITEM_LOTTERY) OKflag = 0;
			break;
		}
	}
	if (SkillCreateProc==6){
		if ( !(materialOK[0]&&materialOK[1]) ){
			wI->sw[no].Enabled = FALSE;
			return ReturnFlag;
		}
		if ( pc.fp >= DECORATIONFP ) OKflag = 1;
	}

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ){
		if (OKflag) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_TRY );
		if (SkillCreateProc==0){
			if ( pc.fp < job.skill[CreateSkillNo].recipe[CreateRecipeNo].fp*job.skill[CreateSkillNo].fpRate/100 ){
				strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_FP );
			}
		}
		if (SkillCreateProc==3){
			if (job.skill[CreateSkillNo].id == TECH_MODE_MARK){
				if ( pc.fp < 10 ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_FP );
			}else{
				if ( pc.fp < pc.item[targetItemIndex].lv*10 ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_FP );
			}
		}
		if (SkillCreateProc==6){
			if ( pc.fp < DECORATIONFP ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_FP );
		}
	}

	if (SkillCreateProc==0){
		if( flag & MENU_MOUSE_LEFT ){
			if (OKflag){
				ChangeModeMaking(wI);
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
		}
	}
	if (SkillCreateProc==3){
		if( (flag & MENU_MOUSE_LEFT) || keyOnOnce( VK_RETURN ) ){
			if (OKflag){
				if (job.skill[CreateSkillNo].id==TECH_MODE_MARK){
					if (pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_MYITEM){
						if( VirticalCheck( (unsigned char *)SkillItemNameInputStr.buffer ) == 1 ){
							//\や|がはいっているとダメ
							wI->sw[EnumSkillCreateInfo1].Enabled = TRUE;
							if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
							// ＮＧ音（短い）
							play_se( SE_NO_NG, 320, 240 );
						}else{
							char incusework[32];

							strcpy( incusework, SkillItemNameInputStr.buffer );
							//空白ならダメ
							deleteCharFromStringNoEscape(incusework," ");                  //MLHIDE
							deleteCharFromStringNoEscape(incusework,"　");                  //MLHIDE
							if(strlen(incusework) <= 0 && strlen(SkillItemNameInputStr.buffer) > 0){
								//空白はダメ
								wI->sw[EnumSkillCreateInfo2].Enabled = TRUE;
								if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
								// ＮＧ音（短い）
								play_se( SE_NO_NG, 320, 240 );
							}else{
								//正常な刻印だ
								char str[256];

								if ( job.skill[CreateSkillNo].id==TECH_MODE_MARK ) strcpy( str, SkillItemNameInputStr.buffer );
								else str[0] = '\0';
								// S-JIS から EUC に变换
								sjisStringToEucString( str );
								// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
								if (skillrebirth){
									nrproto_TU_send( sockfd, CreateSkillNo+USE_REBIRTH_ADD, selTech, targetItemIndex, str );
								}else if (usePetHelp){
									petHelperGraNo = GetPetHelperGraNo();
									nrproto_TU_send( sockfd, CreateSkillNo+USE_PETHELP_ADD, selTech, targetItemIndex, str );
								}else{
									nrproto_TU_send( sockfd, CreateSkillNo, selTech, targetItemIndex, str );
								}
#else
								nrproto_TU_send( sockfd, CreateSkillNo+(skillrebirth?100:0), selTech, targetItemIndex, str );
#endif

								SkillCreateProc = 5;

								if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
								SkillItemNameInputStr.buffer[0] = '\0';

								// クリック音
								play_se( SE_NO_CLICK, 320, 240 );
							}
						}
					}else if (SkillItemNameInputStr.buffer[0]!='\0'){
						char incusework[32];

						//空白ならダメ
						strcpy( incusework, SkillItemNameInputStr.buffer );
						deleteCharFromStringNoEscape(incusework ," ");                  //MLHIDE
						deleteCharFromStringNoEscape(incusework ,"　");                  //MLHIDE
						if(strlen(incusework) <= 0) {
							wI->sw[EnumSkillCreateInfo2].Enabled = TRUE;
							if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
							// ＮＧ音（短い）
							play_se( SE_NO_NG, 320, 240 );
						}else
						if(VirticalCheck( (unsigned char *)SkillItemNameInputStr.buffer ) == 1){
							//\や|がはいっているとダメ
							wI->sw[EnumSkillCreateInfo1].Enabled = TRUE;
							if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
							// ＮＧ音（短い）
							play_se( SE_NO_NG, 320, 240 );
						}else{
							char str[256];

							if ( job.skill[CreateSkillNo].id==TECH_MODE_MARK ) strcpy( str, SkillItemNameInputStr.buffer );
							else str[0] = '\0';
							// S-JIS から EUC に变换
							sjisStringToEucString( str );
							// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
							if (skillrebirth){
								nrproto_TU_send( sockfd, CreateSkillNo+USE_REBIRTH_ADD, selTech, targetItemIndex, str );
							}else if (usePetHelp){
								petHelperGraNo = GetPetHelperGraNo();
								nrproto_TU_send( sockfd, CreateSkillNo+USE_PETHELP_ADD, selTech, targetItemIndex, str );
							}else{
								nrproto_TU_send( sockfd, CreateSkillNo, selTech, targetItemIndex, str );
							}
#else
							nrproto_TU_send( sockfd, CreateSkillNo+(skillrebirth?100:0), selTech, targetItemIndex, str );
#endif

							SkillCreateProc = 5;

							if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
							SkillItemNameInputStr.buffer[0] = '\0';

							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						}
					}
				}else{
					ChangeModeWarking(wI);
					if ( pNowInputStr == &SkillItemNameInputStr ) SetDialogMenuChat();
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
			ReturnFlag = TRUE;
		}
	}
	if (SkillCreateProc==6){
		if( flag & MENU_MOUSE_LEFT ){
			if (OKflag){
				ChangeModeDecorating(wI);
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
		}
	}

	if (no==EnumSkillCreateTryBtn){
		Graph->graNo = GID_TryButtonOn;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_TryButtonOver;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_TryButtonOff;

		if (!OKflag) Graph->graNo = GID_TryButtonOff;
	}else{
		Graph->graNo = GID_TitleSetOn;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_TitleSetOver;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_TitleSetOff;

		if (OKflag){
			if (SkillCreateProc==3){
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(888, "请输出名称。") );
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText2].Switch )->text, "                     FP 10" ); //MLHIDE
			}
			if (SkillCreateProc==5){
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText1].Switch )->text, "" );
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText2].Switch )->text, "" );
			}
		}else{
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText1].Switch )->text, ML_STRING(889, "现在无法") );
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumSkillCreateText2].Switch )->text, ML_STRING(890, "　刻印此物品。") );

			Graph->graNo = GID_TitleSetOff;
		}
	}

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateRetry( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_RETRY );

	// モードチェンジ
	if( flag & MENU_MOUSE_LEFT ){
		if (SkillCreateProc==2){
			if ( job.skill[CreateSkillNo].id == 255 ){
				ChangeModeRegistSel2(wI);
			}else{
				ChangeModeRegistSel(wI);
			}
		}
		if (SkillCreateProc==5) ChangeModeTargetSel(wI);

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}

	Graph->graNo = GID_RetryButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RetryButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RetryButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateEnd( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_END );

	if( flag & MENU_MOUSE_LEFT ){
		WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	Graph->graNo = GID_EndButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_EndButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_EndButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateMyItem( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[10];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]) continue;
			x = wI->wx + wI->sw[no].ofx + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + ( (i/ITEM_DRAW_COLUMN) * 50 );
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo+8 ) ItemMove( MENU_WINDOW_SKILLCREATE, no, DropitemNo, itemNo+8 );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]) continue;
			x = wI->wx + wI->sw[no].ofx + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + ( (i/ITEM_DRAW_COLUMN) * 50 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ){
		if (itemNo>=0) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_MYITEM );
	}

	if ( ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				itemNo = -1;
			}
		}
	}

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) SkillCreateInfoPage = 0;
	olditemNo = itemNo;

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (pc.item[itemNo+8].useFlag) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE || WinDD_WinType()==MENU_WINDOW_NONE ){
			if ( (SkillCreateProc==0) || (SkillCreateProc==3) || (SkillCreateProc==6) ){
				WinDD_DragFinish();

				if ( SkillCreateProc == 0 ){
					x = -1;
					for(i=0;i<6;i++){
						if (registItemIndex[i]<0){ x = i;	break; }
					}

					if ( x>=0 && MAX_EQUIP_ITEM <= itemNo+MAX_EQUIP_ITEM && itemNo+MAX_EQUIP_ITEM < 100 && pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag ){
						// 必要な素材で量が不足してないかチェック
						int stack_cnt;

						stack_cnt = pc.item[itemNo+MAX_EQUIP_ITEM].num;
						if( stack_cnt == 0 ) stack_cnt = 1;

						// 调合??调理でなく素材Ｂのとき
						if( job.skill[CreateSkillNo].id != 215 && job.skill[CreateSkillNo].id != 216 && checkMaterialB( pc.item[itemNo+MAX_EQUIP_ITEM].kind ) ){
							for(i=0;i<6;i++){
								if (registItemIndex[i]>=0){
									if ( checkMaterialB( pc.item[registItemIndex[i]+MAX_EQUIP_ITEM].kind ) ) break;
								}
							}
							if (i>=6){
								registItemIndex[x] = itemNo;
								registItemGraNo[x] = pc.item[itemNo+MAX_EQUIP_ITEM].graNo;
								registItemNum[x] = getNumItem( pc.item[itemNo+MAX_EQUIP_ITEM].id );
								registItemMaterial[x] = -1;

								ItemNoOpe[itemNo+MAX_EQUIP_ITEM]++;
								SkillCreateSel[itemNo+MAX_EQUIP_ITEM] = 1;
							}
							else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}else{
							for( i = 0; i < RECIPE_MATERIAL_MAX; i++ ){
								if( job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].id >= 0 && !materialOK[i] ){
									if( !SkillCreateSel[itemNo+MAX_EQUIP_ITEM]
										&& pc.item[itemNo+MAX_EQUIP_ITEM].id == job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].id
										&& stack_cnt >= job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].num ){
										break;
									}
								}
							}
							if (i < RECIPE_MATERIAL_MAX){
								materialOK[i] = 1;

								registItemIndex[x] = itemNo;
								registItemGraNo[x] = pc.item[itemNo+MAX_EQUIP_ITEM].graNo;
								registItemNum[x] = getNumItem( pc.item[itemNo+MAX_EQUIP_ITEM].id );
								registItemMaterial[x] = i;

								ItemNoOpe[itemNo+MAX_EQUIP_ITEM]++;
								SkillCreateSel[itemNo+MAX_EQUIP_ITEM] = 1;
							}
							else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}
					}
					else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}
				else if ( SkillCreateProc == 3 ){
					if ( 8 <= itemNo+MAX_EQUIP_ITEM&& itemNo+MAX_EQUIP_ITEM < 100 ){
						char OKflag = 1;

						if (targetItemIndex>=0) RegistEntryFree(6);

						switch(job.skill[CreateSkillNo].id){
						case TECH_MODE_JUDGEITEM:
							// すでに鉴定されてたら警告
							if( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag ) OKflag = 0;
							// 自分のスキルより上なら警告
							else if( pc.item[itemNo+MAX_EQUIP_ITEM].lv > job.skill[CreateSkillNo].lv+(skillrebirth?1:0) ) OKflag = 0;
							break;
						case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
							// 未鉴定アイテムなので警告。
							if( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag == 0 ) OKflag = 0;
							// 防具を选择した场合
							else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_WEPON && WhichEquipType( pc.item[itemNo+8].kind ) == 2 ) OKflag = 0;
							// 武器を选择した场合
							else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_ARMOR && WhichEquipType( pc.item[itemNo+8].kind ) == 1 ) OKflag = 0;
							// その他のアイテムを选择した场合
							else if( WhichEquipType( pc.item[itemNo+MAX_EQUIP_ITEM].kind ) == 0 ) OKflag = 0;
							// スキル等级が足りない场合
							else if( pc.item[itemNo+MAX_EQUIP_ITEM].lv > job.skill[CreateSkillNo].lv+(skillrebirth?1:0) ) OKflag = 0;
							break;
						case TECH_MODE_MARK:
							//すでに他人のハンコがある
							if( pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_HANKO
							  && !(pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_MYITEM) ) OKflag = 0;
							//すでに刻印されてる(ハンコはＯＫ）
							else if( pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_INCUSE
							  && !(pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_MYITEM) ) OKflag = 0;
							//未鉴定品
							else if( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag == 0 ) OKflag = 0;
							//スタックアイテムまたはクジ
							else if(pc.item[itemNo+MAX_EQUIP_ITEM].num > 0 || pc.item[itemNo+MAX_EQUIP_ITEM].kind == ITEM_LOTTERY) OKflag = 0;
							break;
						}

						if (OKflag){
							targetItemIndex = itemNo+MAX_EQUIP_ITEM;
							targetItemGraNo = pc.item[itemNo+MAX_EQUIP_ITEM].graNo;
							targetItemNum = getNumItem( pc.item[itemNo+MAX_EQUIP_ITEM].id );

							ItemNoOpe[itemNo+MAX_EQUIP_ITEM]++;
							SkillCreateSel[itemNo+MAX_EQUIP_ITEM] = 1;
							if ( job.skill[CreateSkillNo].id ==TECH_MODE_MARK ){
								// ダイアログ初期化
								SetInputStr( &InitStrStructSkillItemName,
									wI->wx+wI->sw[EnumSkillCreateText3].ofx, wI->wy+wI->sw[EnumSkillCreateText3].ofy, 0 );
							}
						}
						else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
					}
				}
				else if ( SkillCreateProc == 6 ){
					if ( checkRegistItem(itemNo+8) ){
						x = -1;
						for(i=0;i<6;i++){
							if (registItemIndex[i]<0){ x = i;	break; }
						}

						if (x>=0){
							if( pc.item[itemNo+MAX_EQUIP_ITEM].kind == ITEM_JEWEL
							  || pc.item[itemNo+MAX_EQUIP_ITEM].kind == ITEM_MATERIAL
							  || pc.item[itemNo+MAX_EQUIP_ITEM].kind == ITEM_MATERIAL_B){
								if ( !materialOK[1] ){
									materialOK[1] = 1;

									registItemIndex[x] = itemNo;
									registItemGraNo[x] = pc.item[itemNo+MAX_EQUIP_ITEM].graNo;
									registItemNum[x] = getNumItem( pc.item[itemNo+MAX_EQUIP_ITEM].id );
									registItemMaterial[x] = 1;

									ItemNoOpe[itemNo+MAX_EQUIP_ITEM]++;
									SkillCreateSel[itemNo+MAX_EQUIP_ITEM] = 1;

									appendJewelSelItemFlag[itemNo] = 1;
								}
							}else{
								if ( !materialOK[0] ){
									materialOK[0] = 1;

									registItemIndex[x] = itemNo;
									registItemGraNo[x] = pc.item[itemNo+MAX_EQUIP_ITEM].graNo;
									registItemNum[x] = getNumItem( pc.item[itemNo+MAX_EQUIP_ITEM].id );
									registItemMaterial[x] = 0;

									ItemNoOpe[itemNo+MAX_EQUIP_ITEM]++;
									SkillCreateSel[itemNo+MAX_EQUIP_ITEM] = 1;

									appendJewelSelItemFlag[itemNo] = 1;
								}
							}
						}
						else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
					}
				}
			}
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragItem( itemNo+MAX_EQUIP_ITEM, TRUE );
#else
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+MAX_EQUIP_ITEM) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					SkillCreateInfoPage++;
					if( SkillCreateInfoPage >= pc.item[itemNo+MAX_EQUIP_ITEM].memoPage ) SkillCreateInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( !pc.item[DragitemNo].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
		}
#endif

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo >= MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + ( ( (DragitemNo-MAX_EQUIP_ITEM)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + ( ( (DragitemNo-MAX_EQUIP_ITEM)/ITEM_DRAW_COLUMN ) * 50 );
			StockBoxDispBuffer( x+1, y+2, x+47, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( itemNo >= 0 ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + ( (itemNo/ITEM_DRAW_COLUMN) * 50 );
		StockBoxDispBuffer( x+1, y, x+49, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		if (pc.item[itemNo+MAX_EQUIP_ITEM].useFlag) SkillCreateInfoNo = itemNo+MAX_EQUIP_ITEM;
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	bm.rgba.rgba=0xffffffff;
	for(i=0;i<20;i++){
		// アイテムがあるなら表示
		if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 24 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 24 + ( (i/ITEM_DRAW_COLUMN) * 50 );

			if (ItemNoOpe[i+MAX_EQUIP_ITEM]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

			// 个数表示
			if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
				sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );             //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

char RegistSettleDropItemShopFlag = 0;

void RegistSettleDropSkillCreate()
{
	if (RegistSettleDropItemShopFlag){
		RegistSettleDropItemShopFlag = 0;
		return;
	}

	RegistEntryFree( (int)WinDD_ObjData() );
}

BOOL MenuSwitchSkillCreateItemEntry( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};
	struct BLT_MEMBER bm2={0};
	int DragitemNo, DropitemNo;
	int Num = no-EnumSkillCreateItemEntry1;
	int i,j;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( ( WinDD_CheckObjType() != WINDD_SKILLCREATE ) && ( WinDD_CheckObjType() != WINDD_ITEM ) ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		if ( WinDD_CheckObjType() == WINDD_ITEM ){
#ifdef PUK2_NEWDRAG
			DropitemNo = (int)WinDD_ObjData();
#else
			DropitemNo = (int)WinDD_GetObject();
#endif

			if ( SkillCreateProc == 0 ){
				if ( MAX_EQUIP_ITEM <= DropitemNo && DropitemNo < 100 && pc.item[DropitemNo].checkFlag ){
					// 必要な素材で量が不足してないかチェック
					int stack_cnt;

					stack_cnt = pc.item[DropitemNo].num;
					if( stack_cnt == 0 ) stack_cnt = 1;

					// 调合??调理でなく素材Ｂのとき
					if( job.skill[CreateSkillNo].id != 215 && job.skill[CreateSkillNo].id != 216 && checkMaterialB( pc.item[DropitemNo].kind ) ){
						for(i=0;i<6;i++){
							if (registItemIndex[i]>=0){
								if ( checkMaterialB( pc.item[registItemIndex[i]+8].kind ) ) break;
							}
						}
						if (i>=6){
							if (registItemIndex[Num]>=0) RegistEntryFree(Num);

							registItemIndex[Num] = DropitemNo - MAX_EQUIP_ITEM;
							registItemGraNo[Num] = pc.item[DropitemNo].graNo;
							registItemNum[Num] = getNumItem( pc.item[DropitemNo].id );
							registItemMaterial[Num] = -1;

							ItemNoOpe[DropitemNo]++;
							SkillCreateSel[DropitemNo] = 1;
						}
					}else{
						for( i = 0; i < RECIPE_MATERIAL_MAX; i++ ){
							if( job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].id >= 0 && !materialOK[i] ){
								if( !SkillCreateSel[DropitemNo]
									&& pc.item[DropitemNo].id == job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].id
									&& stack_cnt >= job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[i].num ){
									break;
								}
							}
						}
						if (i < RECIPE_MATERIAL_MAX){
							if (registItemIndex[Num]>=0) RegistEntryFree(Num);

							materialOK[i] = 1;

							registItemIndex[Num] = DropitemNo - MAX_EQUIP_ITEM;
							registItemGraNo[Num] = pc.item[DropitemNo].graNo;
							registItemNum[Num] = getNumItem( pc.item[DropitemNo].id );
							registItemMaterial[Num] = i;

							ItemNoOpe[DropitemNo]++;
							SkillCreateSel[DropitemNo] = 1;
						}
					}
				}
			}else if ( SkillCreateProc == 6 ){
				if ( checkRegistItem(DropitemNo) ){
					if (registItemIndex[Num]>=0) RegistEntryFree(Num);

					if( pc.item[DropitemNo].kind == ITEM_JEWEL
					  || pc.item[DropitemNo].kind == ITEM_MATERIAL
					  || pc.item[DropitemNo].kind == ITEM_MATERIAL_B){
						if (!materialOK[1]){
							materialOK[1] = 1;

							registItemIndex[Num] = DropitemNo - MAX_EQUIP_ITEM;
							registItemGraNo[Num] = pc.item[DropitemNo].graNo;
							registItemNum[Num] = getNumItem( pc.item[DropitemNo].id );
							registItemMaterial[Num] = 1;

							ItemNoOpe[DropitemNo]++;
							SkillCreateSel[DropitemNo] = 1;

							appendJewelSelItemFlag[DropitemNo - MAX_EQUIP_ITEM] = 1;
						}
					}else{
						if (!materialOK[0]){
							materialOK[0] = 1;

							registItemIndex[Num] = DropitemNo - MAX_EQUIP_ITEM;
							registItemGraNo[Num] = pc.item[DropitemNo].graNo;
							registItemNum[Num] = getNumItem( pc.item[DropitemNo].id );
							registItemMaterial[Num] = 0;

							ItemNoOpe[DropitemNo]++;
							SkillCreateSel[DropitemNo] = 1;

							appendJewelSelItemFlag[DropitemNo - MAX_EQUIP_ITEM] = 1;
						}
					}
				}
			}
#ifdef PUK2_NEWDRAG
			WinDD_AcceptObject();
#endif
		}else if ( WinDD_CheckObjType() == WINDD_SKILLCREATE ){
#ifdef PUK2_NEWDRAG
			DropitemNo = (int)WinDD_ObjData();
#else
			RegistSettleDropItemShopFlag = 1;
			DropitemNo = (int)WinDD_GetObject();
#endif

			if ( DropitemNo != Num ){
				int xchg;
				xchg = registItemIndex[Num];
				registItemIndex[Num] = registItemIndex[DropitemNo];
				registItemIndex[DropitemNo] = xchg;

				xchg = registItemGraNo[Num];
				registItemGraNo[Num] = registItemGraNo[DropitemNo];
				registItemGraNo[DropitemNo] = xchg;

				xchg = registItemNum[Num];
				registItemNum[Num] = registItemNum[DropitemNo];
				registItemNum[DropitemNo] = xchg;

				xchg = registItemMaterial[Num];
				registItemMaterial[Num] = registItemMaterial[DropitemNo];
				registItemMaterial[DropitemNo] = xchg;
			}
#ifdef PUK2_NEWDRAG
			WinDD_AcceptObject();
#endif
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		DropitemNo = (int)WinDD_GetObject();
		registItemIndex[ (int)WinDD_ObjData() ]=-1;
		registItemGraNo[ (int)WinDD_ObjData() ]=-1;
		registItemNum[ (int)WinDD_ObjData() ] = 0;
		registItemMaterial[ (int)WinDD_ObjData() ]=-1;
	}
#endif


	if ( SkillCreateProc == 0 ){
		for( i = 0; i < 6; i++ ){
			// 材料が登録されているなら
			if ( registItemIndex[i] >= 0 ){
				j = pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].num;
				if (!j) j = 1;
				// 必须材料なら
				if ( registItemMaterial[i] >= 0 ){
					if ( pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].id
						!= job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[ registItemMaterial[i] ].id ){
						RegistEntryFree(i);
					}
					else if ( j < job.skill[CreateSkillNo].recipe[CreateRecipeNo].material[ registItemMaterial[i] ].num ){
						RegistEntryFree(i);
					}
				}
				// 必须材料でないとき
				else{
					// 必须材料しか登録できないときは登録解除
					if ( job.skill[CreateSkillNo].id == 215 ) RegistEntryFree(i);
					else if ( job.skill[CreateSkillNo].id == 216 ) RegistEntryFree(i);
					else if ( !checkMaterialB( pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind ) ) RegistEntryFree(i);
				}
			}
		}
	}else if ( SkillCreateProc == 6 ){
		char mateflag[2] = {0};
		for( i = 0; i < 6; i++ ){
			// 材料が登録されているなら
			if ( registItemIndex[i] >= 0 ){
				if ( !checkRegistItem(registItemIndex[i]+MAX_EQUIP_ITEM) ){
					RegistEntryFree(i);
					continue;
				}

				if( pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind == ITEM_JEWEL
				  || pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind == ITEM_MATERIAL
				  || pc.item[ registItemIndex[i]+MAX_EQUIP_ITEM ].kind == ITEM_MATERIAL_B){
					if (mateflag[1]) RegistEntryFree(i);
					mateflag[1] = 1;
				}else{
					if (mateflag[0]) RegistEntryFree(i);
					mateflag[0] = 1;
				}
			}
		}
	}

	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// アイテムがあるとき
		if ( registItemIndex[Num] >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragSkillCreateItem( Num );
#else
				WinDD_DragStart( WINDD_SKILLCREATE, (void *)(Num) );
#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_SKILLCREATE ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE ) && ( WinDD_ButtonNo()==no ) ){
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				// 自分の上でないなら
				if ( !(flag&MENU_MOUSE_DRAGOVER) ){
					DragitemNo = (int)WinDD_ObjData();
					WinDD_DragFinish();
					WinDD_DropObject( WINDD_SKILLCREATE, (void *)(DragitemNo), RegistSettleDropSkillCreate, mouse.nowPoint.x, mouse.nowPoint.y );
				}
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_SKILLCREATE ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE ) && ( WinDD_ButtonNo()==no ) ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, registItemGraNo[Num], 0, &bm2 );
		}
#endif

		// ドラッグ中のアイテムを自分が表示しているなら
		if (DragitemNo==Num){
			// 枠表示
			StockBoxDispBuffer( wI->wx+wI->sw[no].ofx+2, wI->wy+wI->sw[no].ofy+2,
				wI->wx+wI->sw[no].ofx+46, wI->wy+wI->sw[no].ofy+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	// 重なってるなら枠表示
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}
	if ( flag & MENU_MOUSE_DRAGOVER ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_SELECT );

	if( registItemIndex[Num]<0 ) return FALSE;

	if ( flag & MENU_MOUSE_OVER ){
		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( RegistItemBoxOldPage[Num] != Num ) SkillCreateInfoPage = 0;
		RegistItemBoxOldPage[Num] = Num;

		// アイテムがあり、右クリックしたら说明ページを进める
		if( flag & MENU_MOUSE_RIGHT ){
			SkillCreateInfoPage++;
			if( SkillCreateInfoPage >= ITEM_MEMO_PAGE ) SkillCreateInfoPage = 0;

			ReturnFlag=TRUE;
		}

		// アイテム说明をしてもらうため登録
		SkillCreateInfoNo = registItemIndex[Num] + MAX_EQUIP_ITEM;
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, registItemGraNo[Num], 0, &bm );

	return ReturnFlag;
}


BOOL MenuSwitchSkillTargetItemEntry( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};
	struct BLT_MEMBER bm2={0};
	int DragitemNo, DropitemNo;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		if ( 8 <= DropitemNo && DropitemNo < 100 ){
////			char OKflag = 1;

			if (targetItemIndex>=0) RegistEntryFree(6);
/***
			switch(job.skill[CreateSkillNo].id){
			case TECH_MODE_JUDGEITEM:
				// すでに鉴定されてたら警告
				if( pc.item[DropitemNo].checkFlag ) OKflag = 0;
				// 自分のスキルより上なら警告
				else if( pc.item[DropitemNo].lv > job.skill[CreateSkillNo].lv ) OKflag = 0;
				break;
			case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
				// 未鉴定アイテムなので警告。
				if( pc.item[DropitemNo].checkFlag == 0 ) OKflag = 0;
				// 防具を选择した场合
				else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_WEPON && WhichEquipType( pc.item[DropitemNo].kind ) == 2 ) OKflag = 0;
				// 武器を选择した场合
				else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_ARMOR && WhichEquipType( pc.item[DropitemNo].kind ) == 1 ) OKflag = 0;
				// その他のアイテムを选择した场合
				else if( WhichEquipType( pc.item[DropitemNo].kind ) == 0 ) OKflag = 0;
				// スキル等级が足りない场合
				else if( pc.item[DropitemNo].lv > job.skill[CreateSkillNo].lv ) OKflag = 0;
				break;
			case TECH_MODE_MARK:
				//すでに他人のハンコがある
				if( pc.item[DropitemNo].flag & ITEM_ETC_FLAG_HANKO
				  && !(pc.item[DropitemNo].flag & ITEM_ETC_FLAG_MYITEM) ) OKflag = 0;
				//すでに刻印されてる(ハンコはＯＫ）
				else if( pc.item[DropitemNo].flag & ITEM_ETC_FLAG_INCUSE
				  && !(pc.item[DropitemNo].flag & ITEM_ETC_FLAG_MYITEM) ) OKflag = 0;
				//未鉴定品
				else if( pc.item[DropitemNo].checkFlag == 0 ) OKflag = 0;
				//スタックアイテムまたはクジ
				else if(pc.item[DropitemNo].num > 0 || pc.item[DropitemNo].kind == ITEM_LOTTERY) OKflag = 0;
				break;
			}

			if (OKflag){
***/
				targetItemIndex = DropitemNo;
				targetItemGraNo = pc.item[DropitemNo].graNo;
				targetItemNum = getNumItem( pc.item[DropitemNo].id );

				ItemNoOpe[DropitemNo]++;
				SkillCreateSel[DropitemNo] = 1;
				if ( job.skill[CreateSkillNo].id ==TECH_MODE_MARK ){
					// ダイアログ初期化
					SetInputStr( &InitStrStructSkillItemName,
						wI->wx+wI->sw[EnumSkillCreateText3].ofx, wI->wy+wI->sw[EnumSkillCreateText3].ofy, 0 );
				}
////			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		DropitemNo = (int)WinDD_GetObject();
		targetItemIndex=-1;
		targetItemGraNo=-1;
		targetItemNum = 0;
	}
#endif
	if ( targetItemIndex >= 0 ){
		switch(job.skill[CreateSkillNo].id){
		case TECH_MODE_JUDGEITEM:
			// すでに鉴定されてたら警告
			if( pc.item[targetItemIndex].checkFlag ) RegistEntryFree(6);
			// 自分のスキルより上なら警告
			else if( pc.item[targetItemIndex].lv > job.skill[CreateSkillNo].lv+(skillrebirth?1:0) ) RegistEntryFree(6);
			break;
		case TECH_MODE_REPAIR_WEPON:case TECH_MODE_REPAIR_ARMOR:
			// 未鉴定アイテムなので警告。
			if( pc.item[targetItemIndex].checkFlag == 0 ) RegistEntryFree(6);
			// 防具を选择した场合
			else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_WEPON && WhichEquipType( pc.item[targetItemIndex].kind ) == 2 ) RegistEntryFree(6);
			// 武器を选择した场合
			else if( job.skill[CreateSkillNo].id ==TECH_MODE_REPAIR_ARMOR && WhichEquipType( pc.item[targetItemIndex].kind ) == 1 ) RegistEntryFree(6);
			// その他のアイテムを选择した场合
			else if( WhichEquipType( pc.item[targetItemIndex].kind ) == 0 ) RegistEntryFree(6);
			// スキル等级が足りない场合
			else if( pc.item[targetItemIndex].lv > job.skill[CreateSkillNo].lv+(skillrebirth?1:0) ) RegistEntryFree(6);
			break;
		case TECH_MODE_MARK:
			//すでに他人のハンコがある
			if( pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_HANKO
			  && !(pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_MYITEM) ) RegistEntryFree(6);
			//すでに刻印されてる(ハンコはＯＫ）
			else if( pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_INCUSE
			  && !(pc.item[targetItemIndex].flag & ITEM_ETC_FLAG_MYITEM) ) RegistEntryFree(6);
			//未鉴定品
			else if( pc.item[targetItemIndex].checkFlag == 0 ) RegistEntryFree(6);
			//スタックアイテムまたはクジ
			else if(pc.item[targetItemIndex].num > 0 || pc.item[targetItemIndex].kind == ITEM_LOTTERY) RegistEntryFree(6);
			break;
		}
	}

	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// アイテムがあるとき
		if ( targetItemIndex >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragSkillCreateItem( 6 );
#else
				WinDD_DragStart( WINDD_SKILLCREATE, (void *)6 );
#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_SKILLCREATE ){
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE ) && ( WinDD_ButtonNo()==no ) ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( targetItemIndex < 0 ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				// 自分の上でないなら
				if ( !(flag&MENU_MOUSE_DRAGOVER) ){
					WinDD_DragFinish();
					WinDD_DropObject( WINDD_SKILLCREATE, (void *)(DragitemNo), RegistSettleDropSkillCreate, mouse.nowPoint.x, mouse.nowPoint.y );
				}
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_SKILLCREATE ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( ( WinDD_WinType()==MENU_WINDOW_SKILLCREATE ) && ( WinDD_ButtonNo()==no ) ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, targetItemGraNo, 0, &bm2 );
		}
#endif

		// ドラッグ中のアイテムを自分が表示しているなら
		if (DragitemNo==6){
			// 枠表示
			StockBoxDispBuffer( wI->wx+wI->sw[no].ofx+2, wI->wy+wI->sw[no].ofy+2,
				wI->wx+wI->sw[no].ofx+46, wI->wy+wI->sw[no].ofy+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	// 重なってるなら枠表示
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}
	if ( flag & MENU_MOUSE_DRAGOVER ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_SELECT );

	if( targetItemIndex<0 ) return FALSE;

	if ( flag & MENU_MOUSE_OVER ){
		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( RegistItemBoxOldPage[6] != 6 ) SkillCreateInfoPage = 0;
		RegistItemBoxOldPage[6] = 6;

		// アイテムがあり、右クリックしたら说明ページを进める
		if( flag & MENU_MOUSE_RIGHT ){
			SkillCreateInfoPage++;
			if( SkillCreateInfoPage >= ITEM_MEMO_PAGE ) SkillCreateInfoPage = 0;

			ReturnFlag=TRUE;
		}

		// アイテム说明をしてもらうため登録
		SkillCreateInfoNo = targetItemIndex;
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, targetItemGraNo, 0, &bm );

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateItemEntryDisp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};
	int Num = no-EnumSkillCreateItemEntry1;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// 重なってるなら枠表示
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	if( registItemIndex[Num]<0 ) return FALSE;

	if ( flag & MENU_MOUSE_OVER ){
		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( RegistItemBoxOldPage[Num] != Num ) SkillCreateInfoPage = 0;
		RegistItemBoxOldPage[Num] = Num;

		// アイテムがあり、右クリックしたら说明ページを进める
		if( flag & MENU_MOUSE_RIGHT ){
			SkillCreateInfoPage++;
			if( SkillCreateInfoPage >= ITEM_MEMO_PAGE ) SkillCreateInfoPage = 0;

			ReturnFlag=TRUE;
		}

		// アイテム说明をしてもらうため登録
		SkillCreateInfoNo = registItemIndex[Num] + 8;
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, registItemGraNo[Num], 0, &bm );

	return ReturnFlag;
}

BOOL MenuSwitchSkillTargetItemEntryDisp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct BLT_MEMBER bm={0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// 重なってるなら枠表示
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
			wI->wx+wI->sw[no].ofx+48, wI->wy+wI->sw[no].ofy+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	if( targetItemIndex<0 ) return FALSE;

	if ( flag & MENU_MOUSE_OVER ){
		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( RegistItemBoxOldPage[6] != 6 ) SkillCreateInfoPage = 0;
		RegistItemBoxOldPage[6] = 6;

		// アイテムがあり、右クリックしたら说明ページを进める
		if( flag & MENU_MOUSE_RIGHT ){
			SkillCreateInfoPage++;
			if( SkillCreateInfoPage >= ITEM_MEMO_PAGE ) SkillCreateInfoPage = 0;

			ReturnFlag=TRUE;
		}

		// アイテム说明をしてもらうため登録
		SkillCreateInfoNo = targetItemIndex;
	}

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, targetItemGraNo, 0, &bm );

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateFinishItemDisp( int no, unsigned int flag )
{
	struct BLT_MEMBER bm={0};

	if ( SkillCreateFinishGra < 0 ) return FALSE;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, SkillCreateFinishGra, 0, &bm );

	return FALSE;
}

BOOL MenuSwitchSkillItemRenaming( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	if (targetItemIndex<0) return ReturnFlag;

	// マウスが重なっているなら枠表示
	displaySwitchFrame( &wI->sw[no], flag, BoxColor );

	if( flag & MENU_MOUSE_LEFT ){
		wI->sw[EnumSkillCreateNameInputHit].Enabled = FALSE;
		wI->sw[EnumSkillCreateNameInput].Enabled = TRUE;

		// ダイアログ初期化
		SetInputStr( &InitStrStructSkillItemName,
			wI->wx+wI->sw[EnumSkillCreateText3].ofx, wI->wy+wI->sw[EnumSkillCreateText3].ofy, 1 );

		// フォーカスを取る
		GetKeyInputFocus( &SkillItemNameInputStr );

/***
		// 入力初期化
		if (pc.item[targetItemIndex].freeName[0]!='\0') StrToNowInputStr( pc.item[targetItemIndex].freeName );
		else StrToNowInputStr( pc.item[targetItemIndex].name );
***/

		DiarogST.SwAdd = wI->sw[EnumSkillCreateNameInput].Switch;
		( (DIALOG_SWITCH *)wI->sw[EnumSkillCreateNameInput].Switch )->InpuStrAdd = &SkillItemNameInputStr;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	
		ReturnFlag = TRUE;
	}

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillCreateInfo1( int no, unsigned int flag )
{
	const short x = 115, y = 155, w = 410, h = 150;
	const short okx = 172, oky = 113, okw = 66, okh = 17;
	int GraNo;
	struct BLT_MEMBER bm={0};
	char okflg = 0;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	if ( MakeHitBox( x+okx, y+oky, x+okx+okw, y+oky+okh, -1 ) ) okflg = 1;

	GraNo = GID_BigOKButtonOn;
	if (okflg){
		GraNo = GID_BigOKButtonOver;
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			wI->sw[no].Enabled = FALSE;

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			GraNo = GID_BigOKButtonOff;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
	StockDispBuffer( x+okx+33 , y+oky+9, DISP_PRIO_YES_NO_WND, GraNo, 0, &bm );

	StockFontBuffer( x+30, y+30, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(588, "含有￥ 或 | 字符。"), 0, BoxColor );
	StockFontBuffer( x+30, y+50, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(589, "￥ 或 | 字符无法刻印。"), 0, BoxColor );

	MenuWindowCommonDraw( GID_CommonWindow, x, y, w, h, DISP_PRIO_YES_NO_WND, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

BOOL MenuSwitchSkillCreateInfo2( int no, unsigned int flag )
{
	const short x = 115, y = 155, w = 410, h = 150;
	const short okx = 172, oky = 113, okw = 66, okh = 17;
	int GraNo;
	struct BLT_MEMBER bm={0};
	char okflg = 0;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	if ( MakeHitBox( x+okx, y+oky, x+okx+okw, y+oky+okh, -1 ) ) okflg = 1;

	GraNo = GID_BigOKButtonOn;
	if (okflg){
		GraNo = GID_BigOKButtonOver;
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			wI->sw[no].Enabled = FALSE;

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			GraNo = GID_BigOKButtonOff;
		}
	}
	StockDispBuffer( x+okx+33 , y+oky+9, DISP_PRIO_YES_NO_WND, GraNo, 0, &bm );

	StockFontBuffer( x+30, y+30, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(891, "没有包含空格之外的文字"), 0, BoxColor );
	StockFontBuffer( x+30, y+50, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(591, "刻印名无法使用空格。"), 0, BoxColor );

	MenuWindowCommonDraw( GID_CommonWindow, x, y, w, h, DISP_PRIO_YES_NO_WND, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}




//====================================//
//				采取				  //
//====================================//

static char SkillGatherType;
static char SkillGatherMode;
static char AnimNo;
static unsigned int StartTime;
static unsigned int G_FinishTime;

extern int categoryDResult;
extern int categoryDResultGetExp;
extern int categoryDResultFame;
extern int categoryDResultLevelUp;
extern int categoryDResultStm;
extern int categoryDResultDex;
extern int categoryDResultInt;
extern int categoryDResultItemGraNo;
extern char categoryDResultItemName[ITEM_NAME_LEN+1];
#ifdef PUK3_MONSTER_HELPER
	extern int categoryDResultPetHelp;
	static int SkillGatherPetGraNo;
	static int SkillGatherMonsterCrystalLevel;
#endif

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openSkillGatherWindow( int SkillNo, int SelTech )
{
	SkillGatherSkillNo = SkillNo;
	SkillGatherSelTech = SelTech;

#ifdef PUK3_MONSTER_HELPER
	usePetHelp = FALSE;
	categoryDResultPetHelp = 0;
#endif
	// 伐采
	if( job.skill[SkillGatherSkillNo].id == 225 || job.skill[SkillGatherSkillNo].id == 251 ) SkillGatherType = 0;
	// 狩猟
	else if( job.skill[SkillGatherSkillNo].id == 226 || job.skill[SkillGatherSkillNo].id == 253 ) SkillGatherType = 1;
	// 采掘
	else if( job.skill[SkillGatherSkillNo].id == 227 || job.skill[SkillGatherSkillNo].id == 250 ) SkillGatherType = 2;

	// ＰＣの位置记忆
	memoryMapGridPos( mapGx, mapGy );

	CategoryDExtraTime = 2000;

	RebirthOKCntInc();

	RebirthOffCntInc();

	SkillMoveOffCnt++;

	SkillIconCnt++;
	// アイコン出す(出てたらそのまま)
	switch(job.skill[SkillGatherSkillNo].id){
	case 225:case 251:	// 伐采
		if ( GetHeadIcon() != HEADICON_FELL ) SetHeadIcon( HEADICON_FELL );
		break;
	case 226:case 253:	// 狩猟
		if ( GetHeadIcon() != HEADICON_HUNT ) SetHeadIcon( HEADICON_HUNT );
		break;
	case 227:case 250:	// 采掘
		if ( GetHeadIcon() != HEADICON_PICK ) SetHeadIcon( HEADICON_PICK );
		break;
	}

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindow( MENU_WINDOW_SKILLGATHER, OPENMENUWINDOW_HIT, 0 );
}

BOOL closeSkillGather()
{
	RebirthOKCntDec();

	if (SkillGatherMode==1||SkillGatherMode==2) RebirthOffCntDec();

	SkillMoveOffCnt--;

	WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	SkillIconCnt--;
	if (!SkillIconCnt){
		switch( GetHeadIcon() ){
		case HEADICON_FELL:case HEADICON_PICK:case HEADICON_HUNT:
			SetHeadIcon( HEADICON_DELETE );
			break;
		}
	}

	return TRUE;
}

void SkillGatherModeStop( WINDOW_INFO *wi )
{
	if ( SkillGatherMode!=0 && SkillGatherMode!=3 && SkillGatherMode!=4 ) RebirthOffCntDec();

	SkillGatherMode = 0;

	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText1].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText2].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText4].Switch )->text, "" );

	wi->sw[EnumSkillGatherText1].Enabled = FALSE;
	wi->sw[EnumSkillGatherText2].Enabled = FALSE;
	wi->sw[EnumSkillGatherText3].Enabled = FALSE;
	wi->sw[EnumSkillGatherText4].Enabled = FALSE;
	wi->sw[EnumSkillGatherGraText].Enabled = FALSE;
	wi->sw[EnumSkillGatherUnderBar].Enabled = FALSE;
	wi->sw[EnumSkillGatherItemBox].Enabled = FALSE;

	wi->sw[EnumSkillGatherTryBtn].Enabled = TRUE;
	wi->sw[EnumSkillGatherEndBtn].Enabled = TRUE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_STAND;
	( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->AnimNoLoop = FALSE;

	categoryDResultItemGraNo = -1;
#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillGatherPc, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	SetMonsAnim( wi, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, TRUE, ANIM_STAND, 5 );

	// キャンセルしたらペットヘルプの效果无效
	usePetHelp = FALSE;
	categoryDResultPetHelp = 0;
#endif
}

void SkillGatherModeGather( WINDOW_INFO *wi )
{
	if (SkillGatherMode!=1) RebirthOffCntInc();

	SkillGatherMode = 1;

#ifdef PUK3_MONSTER_HELPER
	// ペットお手伝いの発动をチェック
	usePetHelp = FALSE;
	if (categoryDResultPetHelp>0){
		usePetHelp = TRUE;
		petHelperGraNo = SkillGatherPetGraNo;
	}
#endif
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText1].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText2].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText3].Switch )->text, "" );
	strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText4].Switch )->text, "" );

	wi->sw[EnumSkillGatherText1].Enabled = FALSE;
	wi->sw[EnumSkillGatherText2].Enabled = FALSE;
	wi->sw[EnumSkillGatherText3].Enabled = FALSE;
	wi->sw[EnumSkillGatherText4].Enabled = FALSE;
	wi->sw[EnumSkillGatherGraText].Enabled = TRUE;
	wi->sw[EnumSkillGatherUnderBar].Enabled = FALSE;
	wi->sw[EnumSkillGatherItemBox].Enabled = FALSE;

	wi->sw[EnumSkillGatherTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillGatherEndBtn].Enabled = TRUE;

	( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_STAND;
	( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->AnimNoLoop = FALSE;

	categoryDResultItemGraNo = -1;

	AnimNo = 0;
	StartTime = GetTickCount();
	G_FinishTime = 4500 + CategoryDExtraTime;
	if (skillrebirth){
		G_FinishTime = (G_FinishTime * RebirthCreateRate[ pc.rebirthLevel ]) / 100;
	}
#ifdef PUK3_MONSTER_HELPER
	if (usePetHelp){
		G_FinishTime = (G_FinishTime * PetHelpCreateRate[2][ SkillGatherMonsterCrystalLevel ]) / 100;
	}
#endif
	G_FinishTime += StartTime;

#ifdef PUK3_MONSTER_HELPER
	SkillMonsHelperStart = StartTime;
	SetMonsPoint( wi, EnumSkillGatherPc, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		SetMonsAnim( wi, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, TRUE, ANIM_STAND, 5 );
	}else{
		SetMonsAnim( wi, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif
}

void SkillGatherModeFind( WINDOW_INFO *wi )
{
	char *p;
	char str[256];

	if ( SkillGatherMode!=0 && SkillGatherMode!=3 && SkillGatherMode!=4 ) RebirthOffCntDec();

	if ( SkillGatherMode == 0 ) SkillGatherMode = 4;
	else SkillGatherMode = 3;

	wi->sw[EnumSkillGatherText1].Enabled = TRUE;
	wi->sw[EnumSkillGatherText2].Enabled = TRUE;
	wi->sw[EnumSkillGatherText3].Enabled = TRUE;
	wi->sw[EnumSkillGatherText4].Enabled = TRUE;
	wi->sw[EnumSkillGatherGraText].Enabled = FALSE;
	wi->sw[EnumSkillGatherUnderBar].Enabled = TRUE;
	wi->sw[EnumSkillGatherItemBox].Enabled = TRUE;

	wi->sw[EnumSkillGatherTryBtn].Enabled = FALSE;
	wi->sw[EnumSkillGatherEndBtn].Enabled = TRUE;

	// 成功か失败か
	if ( categoryDResult ){
		// 作ったものの名称
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText1].Switch )->text, categoryDResultItemName );
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText2].Switch )->text, ML_STRING(892, "　发现了。") );
		play_se( SE_NO_SKILL_SUCCESS, 320, 240 );
	}
	else{
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText1].Switch )->text, ML_STRING(893, "什么都没发现。") );
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText2].Switch )->text, "" );
		play_se( SE_NO_SKILL_FAILED, 320, 240 );
		categoryDResultItemGraNo = -1;
	}

	// スキル经验值と等级アップ
	if (categoryDResultLevelUp){
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText3].Switch )->text,
			"%s", "升级！" );                                                     //MLHIDE
	}else{
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText3].Switch )->text,
			ML_STRING(541, "技能经验 %+d"), categoryDResultGetExp );
	}

	// 状态变化
	p = ( (TEXT_SWITCH *)wi->sw[EnumSkillGatherText4].Switch )->text;
	p[0] = '\0';
	if( categoryDResultStm ){
		sprintf( str, ML_STRING(869, "　耐力 %+d"), categoryDResultStm );
		strcat( p, str );
	}
	if( categoryDResultDex ){
		sprintf( str, ML_STRING(870, "　敏捷 %+d"), categoryDResultDex );
		strcat( p, str );
	}
	if( categoryDResultInt ){
		sprintf( str, ML_STRING(871, "　智力 %+d"), categoryDResultInt );
		strcat( p, str );
	}

	( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->AnimNoLoop = TRUE;
	if ( categoryDResult ){
		( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
		if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_CHOKI;
		if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
	}else{
		( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
		if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_SAD;
		if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wi->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
	}

	AnimNo = 0;
	StartTime = GetTickCount();

#ifdef PUK3_MONSTER_HELPER
	SkillMonsHelperStart = StartTime;
	SetMonsPoint( wi, EnumSkillGatherPc, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if (usePetHelp){
		if ( categoryDResult ){
			SetMonsAnim( wi, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, TRUE, ANIM_MAGIC, 5 );
		}else{
			SetMonsAnim( wi, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, TRUE, ANIM_DAMAGE, 5 );
		}
	}else{
		SetMonsAnim( wi, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, FALSE, ANIM_DEAD, 5 );
	}
#endif
}

void GiveResultGather()
{
	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) SkillGatherModeFind( WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo );
}

BOOL MenuWindowSkillGatherBf( int mouse )
{
	int i;
	ACTION *ActionAdd;

	if ( mouse == WIN_INIT ){
		pActGather = NULL;
		SkillGatherMode = 0;

		RebirthOffCntDec();
		SkillGatherModeGather(wI);

		return TRUE;
	}

	// キャラクター画像设定
	i = getNewGraphicNo(pc.graNo);
	switch( SkillGatherType ){
	case 0:		i = GetCharaEmptyGra( i, -2 );	break;
	case 1:		i = GetCharaEmptyGra( i, -1 );	break;
	default:	i = GetCharaEmptyGra( i, 0 );	break;
	}
	( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_chr_no = i;
	( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_ang = 5;

#ifdef PUK3_MONSTER_HELPER
	if (usePetHelp){
		if ( CheckPetHelp() ){
			SetMonsChar( wI, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, petHelperGraNo );
		}else{
			SetMonsChar( wI, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, 0 );
		}
		if ( SkillGatherMode==1 ){
			MonsHelperMove( wI, EnumSkillGatherPc, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, G_FinishTime );
		}
	}else{
		SetMonsChar( wI, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, 0 );
	}
#endif
	if (SkillGatherMode==1){
		switch( SkillGatherType ){
		case 0:
			// 伐采アニメーション
			switch(AnimNo){
			case 0:		// 初期化
				( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_WALK;
				( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->AnimNoLoop = FALSE;
				AnimNo++;
				break;
			case 1:		// ２秒间步くアニメ设定
				if( StartTime + 2000 < GetTickCount() ){
					( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
					AnimNo++;
				}
				break;
			case 2:		// ２回攻击したら点头
				if( ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->animLoopCnt >= 2 ){
					( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
					if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
					AnimNo++;
				}
				break;
			}
			break;
		case 1:
			// 狩猟アニメーション
			switch(AnimNo){
			case 0:		// 初期化
				( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_WALK;
				( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->AnimNoLoop = FALSE;
				AnimNo++;
				break;
			case 1:		// ２秒间步くアニメ设定
				if( StartTime + 2000 < GetTickCount() ){
					( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
					AnimNo++;
				}
				break;
			case 2:		// ２回攻击したら点头
				if( ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->animLoopCnt >= 2 ){
					( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
					if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
					AnimNo++;
				}
				break;
			}
			break;
		case 2:
			// 采掘アニメーション
			switch(AnimNo){
			case 0:		// 初期化
				( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
				( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->AnimNoLoop = FALSE;
				AnimNo++;
				break;
			case 1:		// ４回攻击したら点头
				if( ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->animLoopCnt >= 2 ){
					( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
					if ( SPRPC_START <= pc.graNo && pc.graNo <= SPRPC_END ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( SPRPC_START_V2 <= pc.graNo && pc.graNo <= SPRPC_END_V2 ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_NOD;
					if ( pc.graNo == SPR_shadow_mon ) ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_no = ANIM_MAGIC;
					AnimNo++;
				}
				break;
			}
		}

		// 时间で强制終了
		if( G_FinishTime < GetTickCount() ){
			CategoryDExtraTime = 0;
#ifdef PUK3_MONSTER_HELPER
			if (skillrebirth){
				nrproto_TU_send( sockfd, SkillGatherSkillNo+USE_REBIRTH_ADD, SkillGatherSelTech, -1, "" );
			}else if (usePetHelp){
				// 采取系の场合のみペットヘルプの発动をサーバーに通知しない
				nrproto_TU_send( sockfd, SkillGatherSkillNo, SkillGatherSelTech, -1, "" );
//				nrproto_TU_send( sockfd, SkillGatherSkillNo+USE_PETHELP_ADD, SkillGatherSelTech, -1, "" );
			}else{
				nrproto_TU_send( sockfd, SkillGatherSkillNo, SkillGatherSelTech, -1, "" );
			}
			// どうなるか分からないので保存しておく
			SkillGatherPetGraNo = GetPetHelperGraNo();
			SkillGatherMonsterCrystalLevel = GetPCMonsterCrystalLevel();
#else
			nrproto_TU_send( sockfd, SkillGatherSkillNo+(skillrebirth?100:0), SkillGatherSelTech, -1, "" );
#endif
			SkillGatherMode = 2;
		}
		// キャラが移动したら終わる
		else if( checkMoveMapGridPos( 1, 1 ) ) SkillGatherModeStop(wI);
	}else if ( SkillGatherMode == 3 ){
		// 时间で强制続行
		if( StartTime + 2000 < GetTickCount() ){
			// 魔力が足りないなら警告を出す
			if( pc.fp < job.skill[SkillGatherSkillNo].tech[SkillGatherSelTech].fp*job.skill[SkillGatherSkillNo].fpRate/100 ){
				StockChatBufferLine( ML_STRING(997, "魔力不足。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 ); //MLHIDE
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
				play_se( SE_NO_SKILL_FAILED, 320, 240 );

				SkillGatherModeStop(wI);
			}else
			// アイテム栏に空きがないなら警告を出す
			if( getItemEmpty() <= 0 ){
				StockChatBufferLine( ML_STRING(545, "物品栏没有空间了。"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
				play_se( SE_NO_SKILL_FAILED, 320, 240 );

				SkillGatherModeStop(wI);
			}else{
				SkillGatherModeGather(wI);
			}
		}
	}else if ( SkillGatherMode == 4 ){
		// 时间で强制続行
		if( StartTime + 2000 < GetTickCount() ){
			SkillGatherModeStop(wI);
		}
	}

#ifdef PUK3_MONSTER_HELPER
	ActionAdd=( (ACTION_SWITCH *)wI->sw[EnumSkillGatherMonsHelper_F].Switch )->ActionAdd;
	pattern( ActionAdd, ANM_NOMAL_SPD, ( ((ACTION_SWITCH *)wI->sw[EnumSkillGatherMonsHelper_F].Switch)->AnimNoLoop ? ANM_NO_LOOP : ANM_LOOP ) );

	ActionAdd=( (ACTION_SWITCH *)wI->sw[EnumSkillGatherMonsHelper_B].Switch )->ActionAdd;
	pattern( ActionAdd, ANM_NOMAL_SPD, ( ((ACTION_SWITCH *)wI->sw[EnumSkillGatherMonsHelper_B].Switch)->AnimNoLoop ? ANM_NO_LOOP : ANM_LOOP ) );

	// お手伝いペットをＰＣより手前に表示するとき
	if ( wI->sw[EnumSkillGatherMonsHelper_F].Enabled ){
		ActionAdd=( (ACTION_SWITCH *)wI->sw[EnumSkillGatherMonsHelper_F].Switch )->ActionAdd;
		if (ActionAdd->bltfon){
			ActionAdd->bm.bltf &= ~ActionAdd->bltfon;
			ActionAdd->bm.bltf |= ActionAdd->bltf & ActionAdd->bltfon;
		}
		if (ActionAdd->rgbaon){
			if (ActionAdd->rgbaon&1) ActionAdd->bm.rgba.r = ActionAdd->rgba.r;
			if (ActionAdd->rgbaon&2) ActionAdd->bm.rgba.g = ActionAdd->rgba.g;
			if (ActionAdd->rgbaon&4) ActionAdd->bm.rgba.b = ActionAdd->rgba.b;
			if (ActionAdd->rgbaon&8) ActionAdd->bm.rgba.a = ActionAdd->rgba.a;
		}
		// アクションを自力で表示设定
		StockDispBuffer2( wI->wx+wI->sw[EnumSkillGatherMonsHelper_F].ofx, wI->wy+wI->sw[EnumSkillGatherMonsHelper_F].ofy,
			DISP_PRIO_WIN2, ActionAdd->bmpNo, FALSE, &ActionAdd->bm );
	}else{
	}
#endif
	// ＰＣの絵はスイッチ内で描画しているアイテムの絵より手前に表示するため
	// プライオリティの关系上ここで描画

	ActionAdd=( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd;
	i = ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd->anim_chr_no;

	// 结果表示时のアクションの变换
	if ( SkillGatherMode == 4 ){
		if ( ActionAdd->anim_no == ANIM_CHOKI || ActionAdd->anim_no == ANIM_ATTACK ){
			ActionAdd->anim_no = ANIM_ATTACK;
			if ( SPRPC_START <= i && i <= SPRPC_END ) ActionAdd->anim_no = ANIM_CHOKI;
			if ( SPRPC_START_V2 <= i && i <= SPRPC_END_V2 ) ActionAdd->anim_no = ANIM_CHOKI;
			if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ) ActionAdd->anim_no = ANIM_CHOKI;
			if ( i == SPR_shadow_mon ) ActionAdd->anim_no = ANIM_ATTACK;
		}else if ( ActionAdd->anim_no == ANIM_SAD || ActionAdd->anim_no == ANIM_DEAD ){
			ActionAdd->anim_no = ANIM_DEAD;
			if ( SPRPC_START <= i && i <= SPRPC_END ) ActionAdd->anim_no = ANIM_SAD;
			if ( SPRPC_START_V2 <= i && i <= SPRPC_END_V2 ) ActionAdd->anim_no = ANIM_SAD;
			if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ) ActionAdd->anim_no = ANIM_SAD;
			if ( i == SPR_shadow_mon ) ActionAdd->anim_no = ANIM_DEAD;
		}
	}
	if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ){
		ActionAdd->bltfon = BLTF_NOCHG;
		ActionAdd->bltf = BLTF_NOCHG;
	}else{
		ActionAdd->bltfon = 0;
	}

	pattern( ActionAdd, ANM_NOMAL_SPD, ( ((ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch)->AnimNoLoop ? ANM_NO_LOOP : ANM_LOOP ) );

	if(  ActionAdd->bmpNo != NULL ){
		if (ActionAdd->bltfon){
			ActionAdd->bm.bltf &= ~ActionAdd->bltfon;
			ActionAdd->bm.bltf |= ActionAdd->bltf & ActionAdd->bltfon;
		}
		if (ActionAdd->rgbaon){
			if (ActionAdd->rgbaon&1) ActionAdd->bm.rgba.r = ActionAdd->rgba.r;
			if (ActionAdd->rgbaon&2) ActionAdd->bm.rgba.g = ActionAdd->rgba.g;
			if (ActionAdd->rgbaon&4) ActionAdd->bm.rgba.b = ActionAdd->rgba.b;
			if (ActionAdd->rgbaon&8) ActionAdd->bm.rgba.a = ActionAdd->rgba.a;
		}
		// アクションを自力で表示设定
		StockDispBuffer2( wI->wx+wI->sw[EnumSkillGatherPc].ofx, wI->wy+wI->sw[EnumSkillGatherPc].ofy,
			DISP_PRIO_WIN2, ActionAdd->bmpNo, FALSE, &ActionAdd->bm );
	}
#ifdef PUK3_MONSTER_HELPER
	// お手伝いペットをＰＣより后ろに表示するとき
	if ( wI->sw[EnumSkillGatherMonsHelper_F].Enabled ){
	}else{
		ActionAdd=( (ACTION_SWITCH *)wI->sw[EnumSkillGatherMonsHelper_B].Switch )->ActionAdd;
		if (ActionAdd->bltfon){
			ActionAdd->bm.bltf &= ~ActionAdd->bltfon;
			ActionAdd->bm.bltf |= ActionAdd->bltf & ActionAdd->bltfon;
		}
		if (ActionAdd->rgbaon){
			if (ActionAdd->rgbaon&1) ActionAdd->bm.rgba.r = ActionAdd->rgba.r;
			if (ActionAdd->rgbaon&2) ActionAdd->bm.rgba.g = ActionAdd->rgba.g;
			if (ActionAdd->rgbaon&4) ActionAdd->bm.rgba.b = ActionAdd->rgba.b;
			if (ActionAdd->rgbaon&8) ActionAdd->bm.rgba.a = ActionAdd->rgba.a;
		}
		// アクションを自力で表示设定
		StockDispBuffer2( wI->wx+wI->sw[EnumSkillGatherMonsHelper_B].ofx, wI->wy+wI->sw[EnumSkillGatherMonsHelper_B].ofy,
			DISP_PRIO_WIN2, ActionAdd->bmpNo, FALSE, &ActionAdd->bm );
	}

	wI->sw[EnumSkillGatherMonsHelper_F].Enabled = FALSE;
	wI->sw[EnumSkillGatherMonsHelper_B].Enabled = FALSE;
#endif

	return TRUE;
}

BOOL MenuWindowSkillGatherAf( int mouse )
{
	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// スイッチ处理
//--------------------------------------------------------

BOOL MenuSwitchSkillGatherClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillGatherTry( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	char OKflag = 1;

	// 魔力が足りないなら警告を出す
	if( pc.fp < job.skill[SkillGatherSkillNo].tech[SkillGatherSelTech].fp*job.skill[SkillGatherSkillNo].fpRate/100 ){
		OKflag = 0;
		if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLGATHER_FP );
	}

	// アイテム栏に空きがないなら警告を出す
	if( getItemEmpty() <= 0 ) OKflag = 0;

	if (!OKflag){
		Graph->graNo = GID_TryButtonOff;
		return ReturnFlag;
	}

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLGATHER_TRY );

	if( flag & MENU_MOUSE_LEFT ){
		// ＰＣの位置记忆
		memoryMapGridPos( mapGx, mapGy );

		SkillGatherModeGather(wI);

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_TryButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_TryButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_TryButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillGatherEnd( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if (SkillGatherMode==0) strcpy( OneLineInfoStr, MWONELINE_SKILLGATHER_ENDCLOSE );
		else strcpy( OneLineInfoStr, MWONELINE_SKILLGATHER_ENDSTOP );
	}

	if( flag & MENU_MOUSE_LEFT ){
		if (SkillGatherMode==0){
			wI->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}else{
			SkillGatherModeStop(wI);
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_EndButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_EndButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_EndButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillGatherItemDisp( int no, unsigned int flag )
{
	struct BLT_MEMBER bm={0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	if ( categoryDResultItemGraNo >= 0 ){
		StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, categoryDResultItemGraNo, 0, &bm );
	}
	StockDispBuffer( wI->wx+wI->sw[no].ofx+24, wI->wy+wI->sw[no].ofy+24, DISP_PRIO_WIN2, GID_ItemPanel, 0, &bm );

	return FALSE;
}

//====================================//
//				その他１			  //
//====================================//
static char *CategoryC1TargetStr[10];
static char CategoryC1TargetColor[10];
static char CategoryC1SendTarget;

extern int categoryCMode;				// 0 ... 变装
										// 1 ... 变身
										// 2 ... 应急手当
										// 3 ... 治疗
extern int categoryCSel1Cnt;
extern int categoryCSel1Page;
extern int categoryCSel1PageMax;
extern char categoryCSel1Name[10][CHAR_NAME_LEN+1];
extern int categoryCSel1Type[10];		// 0 ... 自キャラ
										// 1 ... グループ内キャラ
										// 2 ... グループ外キャラ
										// 3 ... のらペット
										// 4 ... 自ペット
										// 5 ... グループ内キャラのペット
										// 6 ... グループ外キャラのペット

extern char categoryCResultName[CHAR_NAME_LEN+1];
extern int categoryCSel1SelNo;

#ifdef PUK3_MONSTER_HELPER
// 使用スキルの等级がお手伝いの条件に合っているかをチェック
BOOL CheckPetHelpSkillRank()
{
	if ( job.skill[CategoryCSkillNo].tech[CategoryCSelTech].techlv == 
		 GetPCMonsterCrystalLevel() ){
		return TRUE;
	}

	return FALSE;
}
#endif

BOOL CategoryC1ReturnFunc( int ret, char *str )
{
	char str2[256];

	if ( ret == TARGETSEL1_RETURN_DEATHFUNC ){
		RebirthOKCntDec();
		SkillMoveOffCnt--;
		SkillIconCnt--;
		if (!SkillIconCnt){
			if ( GetHeadIcon() == HEADICON_CURE ) SetHeadIcon( HEADICON_DELETE );
		}
		return TRUE;
	}
	else if ( ret == TARGETSEL1_RETURN_CLOSE ){
		return TRUE;
	}
	if ( ret >= TARGETSEL1_RETURN_TEXT ){
		// まだプロトコルを送っていないとき
		if ( !CategoryC1SendTarget ){
			if( categoryCMode == 0 || categoryCSel1Type[ret] == 3 ){
#ifdef PUK3_MONSTER_HELPER
				// ペットお手伝いの発动をチェック
				usePetHelp = FALSE;
				// 变装??变身はお手伝い无し
#endif
				strcpy( categoryCResultName, str );
				// 变装はすぐ实行プロトコルを送る
				// 对象がのらペットの时も实行プロトコルを送る
				str2[0] = '\0';
				// S-JIS から EUC に变换
				sjisStringToEucString( str2 );
				// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
				if (skillrebirth){
					nrproto_TU_send( sockfd, CategoryCSkillNo+USE_REBIRTH_ADD, CategoryCSelTech, ret, str2 );
				}else if (usePetHelp){
					nrproto_TU_send( sockfd, CategoryCSkillNo+USE_PETHELP_ADD, CategoryCSelTech, ret, str2 );
				}else{
					nrproto_TU_send( sockfd, CategoryCSkillNo, CategoryCSelTech, ret, str2 );
				}
#else
				nrproto_TU_send( sockfd, CategoryCSkillNo+(skillrebirth?100:0), CategoryCSelTech, ret, str2 );
#endif
			}
			else{
				// その他のスキルはさらに情报を要求
				nrproto_GPD_send( sockfd, ret );

				categoryCSel1SelNo = ret;
			}
		}
	}
	CategoryC1SendTarget = 1;

	return FALSE;
}

ACTION *openCategoryC1Window()
{
	int i;
	const int color[] = {
		FONT_PAL_GREEN,
		FONT_PAL_AQUA,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE
	};

#ifdef PUK3_MONSTER_HELPER
	usePetHelp = FALSE;
#endif
	for(i=0;i<10;i++){
		CategoryC1TargetStr[i] = categoryCSel1Name[i];
		CategoryC1TargetColor[i] = color[ categoryCSel1Type[i] ];
	}

	SkillIconCnt++;
	// 治疗アイコン出す(出てたらそのまま)
	switch( getCategoryCMode(job.skill[CategoryCSkillNo].id) ){
	// 医疗系のスキルなら
	case 2:case 3:
		if ( GetHeadIcon() != HEADICON_CURE ) SetHeadIcon( HEADICON_CURE );
		break;
	default:
		SetHeadIcon( HEADICON_DELETE );
	}

	RebirthOKCntInc();

	SkillMoveOffCnt++;

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	CategoryC1SendTarget = 0;

	return openTargetSel1Window( CategoryC1TargetStr, CategoryC1TargetColor, categoryCSel1Cnt, CategoryC1ReturnFunc );
}


//====================================//
//				その他２			  //
//====================================//
struct STRUCT_TARGETSEL2 CategoryC2Sel2data[6];

extern int categoryCSel2Cnt;
extern int categoryCSel2InjuryLv[6];
extern int categoryCSel2Lv[6];
extern char categoryCSel2Name[6][CHAR_NAME_LEN+1];
extern int categoryCSel2Lp[6];
extern int categoryCSel2MaxLp[6];
extern int categoryCSel2Fp[6];
extern int categoryCSel2MaxFp[6];
extern int categoryCSel2Type[6];
extern int categoryCSel2SendNo[6];

extern char categoryCResultName[CHAR_NAME_LEN+1];

BOOL CategoryC2ReturnFunc( int ret, char *str )
{
	if ( ret == TARGETSEL2_RETURN_DEATHFUNC ){
		RebirthOKCntDec();
		SkillMoveOffCnt--;
		SkillIconCnt--;
		if (!SkillIconCnt){
			if ( GetHeadIcon() == HEADICON_CURE ) SetHeadIcon( HEADICON_DELETE );
		}
		return TRUE;
	}
	else if ( ret == TARGETSEL2_RETURN_BACK ){
		// 目の前のキャラの情报を要求
		nrproto_GFL_send( sockfd, CategoryCSkillNo );
	}
	else if ( ret == TARGETSEL2_RETURN_CLOSE ){
		return TRUE;
	}
	else{
		char str2[256];

#ifdef PUK3_MONSTER_HELPER
		// ペットお手伝いの発动をチェック
		usePetHelp = FALSE;
		if ( CheckPetHelp() && CheckPetHelpSkillRank() && CheckPetHelpNeedFp() ){
			usePetHelp = TRUE;
			petHelperGraNo = GetPetHelperGraNo();
		}
		// 变装??变身はお手伝い无し
		if ( categoryCMode <= 1 ){
			usePetHelp = FALSE;
		}
#endif
		strcpy( categoryCResultName, categoryCSel2Name[ret] );
		str2[0] = '\0';
		// S-JIS から EUC に变换
		sjisStringToEucString( str2 );
		// 技使用プロトコル送信
#ifdef PUK3_MONSTER_HELPER
		if (skillrebirth){
			nrproto_TU_send( sockfd, CategoryCSkillNo+USE_REBIRTH_ADD, CategoryCSelTech, categoryCSel2SendNo[ret], str2 );
		}else if (usePetHelp){
			nrproto_TU_send( sockfd, CategoryCSkillNo+USE_PETHELP_ADD, CategoryCSelTech, categoryCSel2SendNo[ret], str2 );
		}else{
			nrproto_TU_send( sockfd, CategoryCSkillNo, CategoryCSelTech, categoryCSel2SendNo[ret], str2 );
		}
#else
		nrproto_TU_send( sockfd, CategoryCSkillNo+(skillrebirth?100:0), CategoryCSelTech, categoryCSel2SendNo[ret], str2 );
#endif
	}

	return FALSE;
}

ACTION *openCategoryC2Window()
{
	int i;
	const int color[] = {
		FONT_PAL_GREEN,
		FONT_PAL_AQUA,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE
	};

#ifdef PUK3_MONSTER_HELPER
	usePetHelp = FALSE;
#endif
	for(i=0;i<categoryCSel2Cnt;i++){
		CategoryC2Sel2data[i].name = categoryCSel2Name[i];
		CategoryC2Sel2data[i].lv = categoryCSel2Lv[i];
		CategoryC2Sel2data[i].lp = categoryCSel2Lp[i];
		CategoryC2Sel2data[i].maxlp = categoryCSel2MaxLp[i];
		CategoryC2Sel2data[i].fp = categoryCSel2Fp[i];
		CategoryC2Sel2data[i].maxfp = categoryCSel2MaxFp[i];
		CategoryC2Sel2data[i].health = categoryCSel2InjuryLv[i];
		CategoryC2Sel2data[i].color = color[ categoryCSel2Type[i] ];
	}

	RebirthOKCntInc();

	SkillMoveOffCnt++;
	SkillIconCnt++;

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return openTargetSel2Window( CategoryC2Sel2data, categoryCSel2Cnt, CategoryC2ReturnFunc );
}

//====================================//
//			その他结果				  //
//====================================//

extern int categoryCResult;
extern int categoryCResultGetExp;
extern int categoryCResultFame;
extern int categoryCResultLevelUp;
extern int categoryCResultStm;
extern int categoryCResultDex;
extern int categoryCResultInt;
extern int categoryCResultParam;
extern char categoryCResultName[CHAR_NAME_LEN+1];

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openSkillOthersWindow()
{
	RebirthOKCntInc();

	SkillMoveOffCnt++;
	SkillIconCnt++;

	if (WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo) WindowFlag[MENU_WINDOW_SKILLCREATE].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo) WindowFlag[MENU_WINDOW_SKILLGATHER].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindow( MENU_WINDOW_SKILLOTHERSRESULT, OPENMENUWINDOW_HIT, 0 );
}

BOOL closeSkillOthers()
{
	RebirthOKCntDec();

	SkillMoveOffCnt--;
	SkillIconCnt--;
	if (!SkillIconCnt){
		if ( GetHeadIcon() == HEADICON_CURE ) SetHeadIcon( HEADICON_DELETE );
	}

	WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	return TRUE;
}

void SkillOthersInit( WINDOW_INFO *wi )
{
	char *p;
	char str[256];

	if( categoryCMode == 0 ){
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
			ML_STRING(894, "至%-22s"), categoryCResultName );
		if( categoryCResult ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(895, "　变装成功！") );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(896, "　变装失败！") );
	}
	else
	if( categoryCMode == 1 ){
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
			ML_STRING(894, "至%-22s"), categoryCResultName );
		if( categoryCResult ) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(897, "　变身成功！") );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(898, "　变身失败！") );
	}
	else
	if( categoryCMode == 2 ){
		if( categoryCResult ){
			sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
				ML_STRING(899, "%-16s的急救成功"), categoryCResultName );
			sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text,
				ML_STRING(900, "　体力回复了 %d。"), categoryCResultParam );
		}
		else{
			sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
				ML_STRING(894, "至%-22s"), categoryCResultName );
			if (categoryCResultParam==0) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(901, "　无法急救！") );
			else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(902, "　急救失败！") );
		}
	}
	else
	if( categoryCMode == 3 ){
		// 治疗に成功した场合。
		if( categoryCResult ){
			sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
				ML_STRING(903, "%-16s治疗成功"), categoryCResultName );
			if( categoryCResultParam == 4 ){	// 全快か？
				strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(559, "伤势恢复了。") );
			}else{
				strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(560, "伤势恢复了一些。") );
			}
		}else
		// 治疗の必要が无い场合
		if( categoryCResult == 0 && categoryCResultParam == 0 ){
			sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
				ML_STRING(894, "至%-22s"), categoryCResultName );
			strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(904, "　无法治疗！") );
		}else
		// 治疗に失败したばあい。
		if( categoryCResult == 0 ){
			sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText1].Switch )->text,
				ML_STRING(905, "%-16s治疗失败"), categoryCResultName );

			// パラメータに３が入っていたら、魂が拔けたー。
			if( categoryCResultParam == 3 ){
				strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(906, "　伤势恶化了，拾取了灵魂。") );
			}else
			// パラメータに２が入っていたら、ケガが恶化した。
			if( categoryCResultParam == 2 ){
				// ただの治疗の失败
				strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, ML_STRING(907, "　伤势恶化了。") );
			}else{
				// 治疗できなかったら
				strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText2].Switch )->text, "" );
			}
		}
	}
	if( categoryCResult ) play_se( SE_NO_SKILL_SUCCESS, 320, 240 );
	else play_se( SE_NO_SKILL_FAILED, 320, 240 );

	// スキル经验值と等级アップ
	if (categoryCResultLevelUp){
		strcpy( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText3].Switch )->text, ML_STRING(868, "升级！") );
	}else{
		sprintf( ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText3].Switch )->text, ML_STRING(541, "技能经验 %+d"), categoryCResultGetExp );
	}

	// 状态变化
	p = ( (TEXT_SWITCH *)wi->sw[EnumSkillOthersText4].Switch )->text;
	p[0] = '\0';
	if( categoryCResultStm ){
		sprintf( str, ML_STRING(869, "　耐力 %+d"), categoryCResultStm );
		strcat( p, str );
	}
	if( categoryCResultDex ){
		sprintf( str, ML_STRING(870, "　敏捷 %+d"), categoryCResultDex );
		strcat( p, str );
	}
	if( categoryCResultInt ){
		sprintf( str, ML_STRING(871, "　智力 %+d"), categoryCResultInt );
		strcat( p, str );
	}

	( (ACTION_SWITCH *)wi->sw[EnumSkillOthersPc].Switch )->AnimNoLoop = TRUE;
	if ( categoryCResult ){
		( (ACTION_SWITCH *)wi->sw[EnumSkillOthersPc].Switch )->ActionAdd->anim_no = ANIM_ATTACK;
	}else{
		( (ACTION_SWITCH *)wi->sw[EnumSkillOthersPc].Switch )->ActionAdd->anim_no = ANIM_DEAD;
	}

#ifdef PUK3_MONSTER_HELPER
	SetMonsPoint( wi, EnumSkillOthersPc, EnumSkillOthersMonsHelper_F, EnumSkillOthersMonsHelper_B, SkillCreateMonsPoint[0][0], SkillCreateMonsPoint[0][1] );
	if ( categoryCResult ){
		SetMonsAnim( wi, EnumSkillOthersMonsHelper_F, EnumSkillOthersMonsHelper_B, TRUE, ANIM_MAGIC, 5 );
	}else{
		SetMonsAnim( wi, EnumSkillOthersMonsHelper_F, EnumSkillOthersMonsHelper_B, TRUE, ANIM_DAMAGE, 5 );
	}
#endif
}

BOOL MenuWindowSkillOthersBf( int mouse )
{
	if ( mouse == WIN_INIT ) SkillOthersInit(wI);

	// キャラクター画像设定
	( (ACTION_SWITCH *)wI->sw[EnumSkillOthersPc].Switch )->ActionAdd->anim_chr_no = GetCharaEmptyGra( getNewGraphicNo(pc.graNo), 0 );
	( (ACTION_SWITCH *)wI->sw[EnumSkillOthersPc].Switch )->ActionAdd->anim_ang = 5;

#ifdef PUK3_MONSTER_HELPER
	// お手伝いペット画像设定
	// お手伝いは无し
	if ( usePetHelp ){
		SetMonsChar( wI, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, petHelperGraNo );
	}else{
		SetMonsChar( wI, EnumSkillGatherMonsHelper_F, EnumSkillGatherMonsHelper_B, 0 );
	}
#endif
	return TRUE;
}

BOOL MenuWindowSkillOthersAf( int mouse )
{
	ACTION *ActionAdd = ( (ACTION_SWITCH *)wI->sw[EnumSkillGatherPc].Switch )->ActionAdd;
	int i = ActionAdd->anim_chr_no;

	if ( ActionAdd->anim_no == ANIM_CHOKI || ActionAdd->anim_no == ANIM_ATTACK ){
		ActionAdd->anim_no = ANIM_ATTACK;
		if ( SPRPC_START <= i && i <= SPRPC_END ) ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPRPC_START_V2 <= i && i <= SPRPC_END_V2 ) ActionAdd->anim_no = ANIM_CHOKI;
		if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ) ActionAdd->anim_no = ANIM_CHOKI;
		if ( i == SPR_shadow_mon ) ActionAdd->anim_no = ANIM_ATTACK;
	}else if ( ActionAdd->anim_no == ANIM_SAD || ActionAdd->anim_no == ANIM_DEAD ){
		ActionAdd->anim_no = ANIM_DEAD;
		if ( SPRPC_START <= i && i <= SPRPC_END ) ActionAdd->anim_no = ANIM_SAD;
		if ( SPRPC_START_V2 <= i && i <= SPRPC_END_V2 ) ActionAdd->anim_no = ANIM_SAD;
		if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ) ActionAdd->anim_no = ANIM_SAD;
		if ( i == SPR_shadow_mon ) ActionAdd->anim_no = ANIM_DEAD;
	}

	if ( SPR_rt00_ax <= i && i <= SPR_rk00_sp ){
		ActionAdd->bltfon = BLTF_NOCHG;
		ActionAdd->bltf = BLTF_NOCHG;
	}else{
		ActionAdd->bltfon = 0;
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// スイッチ处理
//--------------------------------------------------------

BOOL MenuSwitchSkillOthersClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillOthersRetry( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	char OKflag = 1;

	// 魔力が足りないなら警告を出す
	if( pc.fp < job.skill[CategoryCSkillNo].tech[CategoryCSelTech].fp*job.skill[CategoryCSkillNo].fpRate/100 ) OKflag = 0;

	if (!OKflag){
		Graph->graNo = GID_RetryButtonOff;
		if( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
		return ReturnFlag;
	}

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_RETRY );

	if( flag & MENU_MOUSE_LEFT ){
		// 目の前のキャラの情报を要求
		nrproto_GFL_send( sockfd, CategoryCSkillNo );

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_RetryButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RetryButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RetryButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchSkillOthersEnd( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 一行インフォ
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_SKILLCREATE_END );

	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_EndButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_EndButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_EndButtonOff;

	return ReturnFlag;
}



//====================================//
//				ツール				  //
//====================================//

int GetCharaEmptyGra( int graNo, int weapon )
{
	int graNo2;
	int graNo3;
	int graNo4;

	if ( SPR_shadow_pcm <= graNo && graNo <= SPR_shadow_mon ){
		return graNo;
	}else if ( SPRPC_START <= graNo && graNo <= SPRPC_END ){
		graNo2 = graNo - SPR_000ax;
		graNo3 = (graNo2/25)*25;
		graNo4 = ( (graNo2 - graNo3)/6 )*6;
		return( SPR_000ax+graNo3+graNo4+2 + weapon );
	}else if ( SPRPC_START_V2 <= graNo && graNo <= SPRPC_END_V2 ){
		graNo2 = graNo - SPR_400ax;
		graNo3 = (graNo2/25)*25;
		graNo4 = ( (graNo2 - graNo3)/6 )*6;
		return( SPR_400ax+graNo3+graNo4+2 + weapon );
	}else if ( SPR_rt00_ax <= graNo && graNo <= SPR_rk00_sp ){
		return( SPR_rt00_ax+( ( (graNo-SPR_rt00_ax)/6 )*6 )+2 + weapon );
	}else{
		return graNo;
	}
}
