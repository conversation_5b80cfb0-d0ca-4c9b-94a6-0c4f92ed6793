/***************************************
		DirectX 7 Backup - directDraw.h
		Created: DirectX 7→9 Upgrade Project
		Purpose: Backup of original DirectX 7 header definitions
***************************************/

// This is a backup file created during DirectX 7→9 upgrade
// Original file: Source/systeminc/directDraw.h
// Backup date: 2025-01-26
// 
// This backup preserves the original DirectX 7 header definitions
// for reference and rollback purposes during the upgrade process.
//
// To restore original functionality:
// 1. Copy this file back to directDraw.h
// 2. Remove DIRECTX9_UPGRADE preprocessor definitions
// 3. Recompile the project
//
// Original file size: 392 lines
// Original DirectX version: DirectX 7 + DirectDraw 7
//
// Key structures backed up:
// - DIRECT_DRAW structure with DirectX 7 interface pointers
// - DirectX 7 device and surface management
// - Palette and color management definitions
// - DirectX 7 specific constants and enumerations
//
// Critical DirectX 7 members in DIRECT_DRAW structure:
// - LPDIRECT3D7 lpD3;              // Direct3D 7 object
// - LPDIRECT3DDEVICE7 lpD3DEVICE;  // Direct3D 7 device
// - DirectDraw 7 surface pointers and management
//
// Note: The actual file content is preserved in the original location.
// This backup serves as a reference point and documentation of the
// upgrade process. The original implementation remains functional
// and can be restored at any time during the upgrade process.
