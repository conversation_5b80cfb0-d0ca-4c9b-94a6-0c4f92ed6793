﻿
static void getUnreadMail ();
BOOL CheckMailBackUp(MailStatusStruct,MailStatusStruct);

//ウインドウ
BOOL MenuWindowMailHistory (int mouse)
{
	if (mouse == WIN_INIT) {
		getUnreadMail ();
		// メールヒストリをファイルに保存
		writeMailFile();
		// 家族メールヒストリをファイルに保存
		writeGuildMailFile();
		checkNoReadMail ();
		MenuWindowMailHistoryRedrawContent ();
	}
	return TRUE;
}


BOOL MenuWindowMailHistoryDraw (int mouse)
{

	MenuWindowMailHistoryRedraw ();

	displayMenuWindow ();
	return TRUE;
}

//スイッチ

//前一个邮件
BOOL MenuWindowMailHistoryPrevPage (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_mailHistoryPagePrevOn;
	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_CARD_PREVHISTORY );
		Graph->graNo=GID_mailHistoryPagePrevOver;
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_mailHistoryPagePrevOff;
		--mailHistoryPage;
		if (mailHistoryPage < 0)
			mailHistoryPage = MAIL_HISTORY_CNT -1;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		MenuWindowMailHistoryRedrawContent ();

		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_mailHistoryPagePrevOff;
	}

	return ReturnFlag;
}

//下一个邮件
BOOL MenuWindowMailHistoryNextPage (int no, unsigned int flag)
{

	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_mailHistoryPageNextOn;
	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_CARD_NEXTHISTORY );
		Graph->graNo=GID_mailHistoryPageNextOver;
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_mailHistoryPageNextOff;

		++mailHistoryPage;
		if (mailHistoryPage >= MAIL_HISTORY_CNT)
			mailHistoryPage = 0;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		MenuWindowMailHistoryRedrawContent ();

		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_mailHistoryPageNextOff;
	}
	
	return ReturnFlag;
}

//メール关闭
BOOL MenuWindowMailHistoryClose (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_WindowCloseOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo=GID_WindowCloseOver;
		strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_WindowCloseOff;

		WindowFlag[MENU_WINDOW_HISTORY].wininfo->flag |= WIN_INFO_DEL;
		saveNowState ();

		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//履历からメール送信
BOOL MenuWindowMailHistorySend (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_MAIL_SEND );
		Graph->graNo=GID_mailHistorySendButtonOver;
	}else{
		Graph->graNo=GID_mailHistorySendButtonOn;
	}

	if( flag & MENU_MOUSE_LEFT ){
		MailStatusST.Index=mailHistorySelectNo;
		MailStatusST.GroupMailFlag=0;
		MailStatusST.Mode=MenuHistoryWindowType;
		//添付アイテムのクリーン
		if(MailStatusST.SendItem!=-1){
			ItemNoOpe[MailStatusST.SendItem]--;
			MailStatusST.SendItem=-1;
			MailStatusST.SendMailMonsterNo=-1;
		}
		// メール送信ウインドウ
		openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFTHOLD ){
		Graph->graNo=GID_mailHistorySendButtonOff;
	}
	
	return ReturnFlag;

}

//未読メールチェック
static void getUnreadMail ()
{
	int i;
	int memNo,historyNo;

	mailHistoryPage = 0;

	switch(MenuHistoryWindowType){
		//名刺モード
		case EnumMenuWindowAddressBookTypeAddressBook:
			// 选择したアドレスの未読番号チェック
			for( i = MAIL_HISTORY_CNT - 1; i >= 0; i-- ){
				if( mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[i].readFlag ){
					mailHistoryPage = i;
					break;
				}
			}
			break;

		//家族モード
		case EnumMenuWindowAddressBookTypeGuildBook:
			memNo=getGuildNumFromID(guildBook.member[mailHistorySelectNo].address.id);
			historyNo=getGuildMailHistoryNumFromID(guildBook.member[mailHistorySelectNo].address.id);
			
			if(historyNo==-1){
				mailHistoryPage=0;
				return;
			}

			// 选择したアドレスの未読番号チェック
			for( i = MAIL_HISTORY_CNT - 1; i >= 0; i-- ){
				if( mailGuildHistory[selectPcNo][historyNo].mail.mailInfo[i].readFlag ){
					mailHistoryPage = i;
					break;
				}
			}
			break;
#ifdef PUK3_PROF
		//ミニメールモード
		case EnumMenuWindowAddressBookTypeMiniMail:
			// 选择したアドレスの未読番号チェック
			for( i = MAIL_HISTORY_CNT - 1; i >= 0; i-- ){
				if( miniMailHistory[mailHistorySelectNo].mailInfo[i].readFlag ){
					mailHistoryPage = i;
					break;
				}
			}
			break;
#endif
	}

}

//初期化
void MenuWindowMailHistoryRedrawContent ()
{

}

//描画
void MenuWindowMailHistoryRedraw (void){

	char content[2048];
	char* tmp;
	int i;
	BLT_MEMBER bm={0};
	int memNo,historyNo;

	switch(MailHistoryST.type){	
		//アドレスメール
		case EnumMenuWindowAddressBookTypeAddressBook:
			//既読に
			mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].readFlag = 0;

			//时间表示
			sprintf (content, "%s", mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].header); //MLHIDE
			tmp = strchr (content, '/');
			if (tmp != NULL) {
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, tmp + 1);
			}else{
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, "");
			}

			//内容表示
			for( i = 0; i < MAIL_HISTORY_CNT; i++ ) {
				if(mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].sendFlag == 0){
					((TEXT_SWITCH*)(wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->color = FONT_PAL_YELLOW;
				}else{
					((TEXT_SWITCH*)(wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->color = FONT_PAL_WHITE;
				}
				getMemoLine( content, sizeof( content ),
					mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].buf, i, 28 );
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->text, content);
			}

			//名称表示
			sprintf (content, "%s",  addressBook[mailHistorySelectNo].name);   //MLHIDE
			strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryName].Switch))->text, content);

			//ページ表示
			sprintf( content, "%2d/%2d", mailHistoryPage+1, MAIL_HISTORY_CNT ); //MLHIDE
			strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryNumber].Switch))->text, content);

			break;


		//家族メール
		case EnumMenuWindowAddressBookTypeGuildBook:
			
			memNo=getGuildNumFromID(guildBook.member[mailHistorySelectNo].address.id);
			historyNo=getGuildMailHistoryNumFromID(guildBook.member[mailHistorySelectNo].address.id);

			if(historyNo!=-1){
				//既読に
				mailGuildHistory[selectPcNo][historyNo].mail.mailInfo[mailHistoryPage].readFlag = 0;

				//时间表示
				sprintf (content, "%s", mailGuildHistory[selectPcNo][historyNo].mail.mailInfo[mailHistoryPage].header); //MLHIDE
				tmp = strchr (content, '/');
				if (tmp != NULL) {
					strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, tmp + 1);
				}else{
					strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, "");
				}

				//内容表示
				for( i = 0; i < MAIL_HISTORY_CNT; i++ ) {
					if(mailGuildHistory[selectPcNo][historyNo].mail.mailInfo[mailHistoryPage].sendFlag == 0){
						((TEXT_SWITCH*)(wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->color = FONT_PAL_YELLOW;
					}else{
						((TEXT_SWITCH*)(wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->color = FONT_PAL_WHITE;
					}
					getMemoLine( content, sizeof( content ),
						mailGuildHistory[selectPcNo][historyNo].mail.mailInfo[mailHistoryPage].buf, i, 28 );
					strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->text, content);
				}

			}else{
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, "");
				for( i = 0; i < MAIL_HISTORY_CNT; i++ ) {
					strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->text, "");
				}
			}

			//名称表示
			sprintf (content, "%s", guildBook.member[memNo].address.name);     //MLHIDE
			strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryName].Switch))->text, content);

			//ページ表示
			sprintf( content, "%2d/%2d", mailHistoryPage+1, MAIL_HISTORY_CNT ); //MLHIDE
			strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryNumber].Switch))->text, content);
			
			break;

#ifdef PUK3_PROF
		case EnumMenuWindowAddressBookTypeMiniMail:
			//既読に
			miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].readFlag = 0;

			//时间表示
			sprintf (content, "%s", miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].header); //MLHIDE
			tmp = strchr (content, '/');
			if (tmp != NULL) {
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, tmp + 1);
			}else{
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryDate].Switch))->text, "");
			}

			//内容表示
			for( i = 0; i < MAIL_HISTORY_CNT; i++ ) {
				if(miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].sendFlag == 0){
					((TEXT_SWITCH*)(wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->color = FONT_PAL_YELLOW;
				}else{
					((TEXT_SWITCH*)(wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->color = FONT_PAL_WHITE;
				}
				getMemoLine( content, sizeof( content ),
					miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].buf, i, 28 );
				strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryContent0 + i].Switch))->text, content);
			}

			//名称表示
			sprintf (content, "%s",  miniMailBook[mailHistorySelectNo].name);  //MLHIDE
			strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryName].Switch))->text, content);

			//ページ表示
			sprintf( content, "%2d/%2d", mailHistoryPage+1, MAIL_HISTORY_CNT ); //MLHIDE
			strcpy (((TEXT_SWITCH*) (wI->sw[EnumGraphMailHistoryNumber].Switch))->text, content);

			break;
#endif
	}

}

BOOL MenuWindowMailHistoryBase (int no, unsigned int flag){

	ADDRESS_BOOK_INFO *ai;
	BLT_MEMBER bm={0};
	int GuildMemberNum,GuildMailNum;

	//颜描画
	if( flag & MENU_MOUSE_OVER ){
		switch(MailHistoryST.type){
			//通常メール时
			case EnumMenuWindowAddressBookTypeAddressBook:
		
				if(mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].buf[0]=='\0'
					&&mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].header[0]=='\0')
					return FALSE;
				if(mailHistory[selectPcNo][mailHistorySelectNo].mailInfo[mailHistoryPage].sendFlag==0){
					//相手の颜	
					ai =&addressBook[mailHistorySelectNo];  

					bm.rgba.rgba=0xffffffff;
					bm.bltf=BLTF_NOCHG;
					//枠
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
					//颜
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,getNewFaceGraphicNo (ai->graNo), 0,&bm);
				}else{
					//自分の颜
					bm.rgba.rgba=0xffffffff;
					bm.bltf=BLTF_NOCHG;
					//枠
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
					//颜
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,pc.faceGraNo, 0,&bm);
				}

				break;

			//家族メール时
			case EnumMenuWindowAddressBookTypeGuildBook:

				GuildMemberNum=getGuildNumFromID(guildBook.member[mailHistorySelectNo].address.id);
				GuildMailNum=getGuildMailHistoryNumFromID(guildBook.member[mailHistorySelectNo].address.id);

				if(mailGuildHistory[selectPcNo][GuildMailNum].mail.mailInfo[mailHistoryPage].buf[0]=='\0'
					&&mailGuildHistory[selectPcNo][GuildMailNum].mail.mailInfo[mailHistoryPage].header[0]=='\0')
					return FALSE;
				if(mailGuildHistory[selectPcNo][GuildMailNum].mail.mailInfo[mailHistoryPage].sendFlag==0){
					//相手の颜	
					ai =&guildBook.member[GuildMemberNum].address;  

					bm.rgba.rgba=0xffffffff;
					bm.bltf=BLTF_NOCHG;
					//枠
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
					//颜
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,getNewFaceGraphicNo (ai->graNo), 0,&bm);
				}else{
					//自分の颜
					//相手の颜	
					bm.rgba.rgba=0xffffffff;
					bm.bltf=BLTF_NOCHG;
					//枠
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
					//颜
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,pc.faceGraNo, 0,&bm);
				}

				break;
#ifdef PUK3_PROF
			//ミニメール时
			case EnumMenuWindowAddressBookTypeMiniMail:
				if(miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].buf[0]=='\0'
					&&miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].header[0]=='\0')
					return FALSE;
				if(miniMailHistory[mailHistorySelectNo].mailInfo[mailHistoryPage].sendFlag==0){
					//相手の颜	
					ai =&miniMailBook[mailHistorySelectNo];  

					bm.rgba.rgba=0xffffffff;
					bm.bltf=BLTF_NOCHG;
					//枠
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
					//颜
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,getNewFaceGraphicNo (ai->graNo), 0,&bm);
				}else{
					//自分の颜
					bm.rgba.rgba=0xffffffff;
					bm.bltf=BLTF_NOCHG;
					//枠
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,GID_AddressBookFaceWin, 0,&bm);
					//颜
					StockDispBuffer (wI->wx+170+64/2,
									 wI->wy+180+72/2,
						DISP_PRIO_WIN2,pc.faceGraNo, 0,&bm);
				}
				break;
#endif
		}
		return TRUE;
	}

	return FALSE;

}


//----------------------------------------------------------------------------------------------
//送信用ウインドウ（杉山）
//----------------------------------------------------------------------------------------------

INIT_STR_STRUCT InitStrStructMail={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&mailEditStr, 33-11,84-14,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_WHITE,"",        10,      28,     12,     0
};

void MenuWindowSendMailInit(void);
void MenuWindowSendPetMailInit(void);
int checkPetMail( int index );

struct PetMailMonsterListStatusStruct{
	int index;
	BOOL flag;
};

struct PetMailMonsterListStruct{
	int count;
	int SortMonsterNo[MAX_PET];
	PetMailMonsterListStatusStruct status[MAX_PET];
}PetMailMonsterList;

MailStatusStruct MailStatusST;
MailStatusStruct MailStatusSTBackUp;

//--------------------------------------------------------
//ログイン时の初期化
//--------------------------------------------------------
void InitMenuWindowSendMailInLogin(void){

	int i;

	PetMailMonsterList.count=0;

	for(i=0;i<MAX_PET;i++){
		PetMailMonsterList.SortMonsterNo[i]=0;
		PetMailMonsterList.status[i].flag=0;
		PetMailMonsterList.status[i].index=0;
	}

	MailStatusST.SendMailMonsterNo=-1;
	MailStatusST.SendItem=-1;
#ifdef PUK3_MAIL_ETC
	MailStatusST.ReOpenFlag = FALSE;
#endif

}	
#ifdef PUK3_MAIL_ETC
// 前回状况で起动
void ReOpenMailWindow()
{
	MailStatusST.ReOpenFlag = TRUE;
	createMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL );
}
#endif
	
//通常メール
BOOL MenuWindowSendMail( int mouse )
{
	int index,i;

	if (mouse==WIN_INIT){
		if(MailStatusST.GroupMailFlag!=1){
			//群邮件でないときはグループウインドウがあったら闭じます
			if(WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo!=NULL){
				WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo->flag |= WIN_INFO_DEL;
			}
		}else{
#ifdef PUK3_MAIL_ETC
			if ( MailStatusST.ReOpenFlag ){
				GMST.ReOpenFlag = TRUE;
			}
#endif
			//群邮件メニューウインドウ作成
			openMenuWindow( MENU_WINDOW_GROUP_MAIL, OPENMENUWINDOW_HIT, 0 );
		}

		//ペットメールがあったら闭じます
		if(WindowFlag[MENU_WINDOW_ADDRESS_SENDPETMAIL].wininfo!=NULL){
			WindowFlag[MENU_WINDOW_ADDRESS_SENDPETMAIL].wininfo->flag |= WIN_INFO_DEL;

			//添付アイテムのクリーン
			if(MailStatusST.SendItem!=-1){
				ItemNoOpe[MailStatusST.SendItem]--;
				MailStatusST.SendItem=-1;
				MailStatusST.SendMailMonsterNo=-1;
			}
		}

		//以前のウインドウからひきつぐ？
		if(CheckMailBackUp(MailStatusST,MailStatusSTBackUp))
			strcpy(InitStrStructMail.str,MailStatusSTBackUp.MailBuff);

		//ダイアログ初期化
		SetInputStr(&InitStrStructMail,wI->wx,wI->wy,0);
		//フォーカスを取る
		GetKeyInputFocus( &mailEditStr );
		
		//ここで初期化文字列消しておきます
		InitStrStructMail.str[0]='\0';

		DiarogST.SwAdd=wI->sw[EnumDialogSendMailMessage].Switch;
		((DIALOG_SWITCH *)wI->sw[EnumDialogSendMailMessage].Switch)->InpuStrAdd=&mailEditStr;
#ifdef PUK3_MAIL_ETC
		MailStatusST.ReOpenFlag = FALSE;
#endif
	}

	//ペットメールスキル持ってるモンスターリストの作成
	PetMailMonsterList.count=0;
	for( i = 0; i < MAX_PET; i++ ){
		//ペットいるか
		if(pet[sortPet[i].index].useFlag){
			index = sortPet[i].index;
			//ペットメールスキル持ってるかチェック
			if(checkPetMail( index )==1){
				PetMailMonsterList.status[i].flag=TRUE;
				PetMailMonsterList.status[i].index=index;
				PetMailMonsterList.SortMonsterNo[PetMailMonsterList.count]=index;
				PetMailMonsterList.count++;
			}else{
				PetMailMonsterList.status[i].flag=FALSE;
				PetMailMonsterList.status[i].index=index;
			}
		}
	}

	//バトル时ペットメール使用不可
	if( ProcNo == PROC_BATTLE ){
		for( i = 0; i < MAX_PET; i++ ){
			PetMailMonsterList.status[i].flag=FALSE;
		}
		PetMailMonsterList.count=0;
	}
#ifdef PUK3_VEHICLE
	// 乘り物移动中でないなら
	// もしくは见えないときでないなら
	if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
		 !nowVehicleProc() ){
	}else{
		for( i = 0; i < MAX_PET; i++ ){
			PetMailMonsterList.status[i].flag=FALSE;
		}
		PetMailMonsterList.count=0;
	}
#endif

	MenuWindowSendMailInit();	

	//群邮件でないときグループウインドウを闭じます
	if(WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo!=NULL && MailStatusST.GroupMailFlag!=1){
		WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo->flag |= WIN_INFO_DEL;
	}

	return TRUE;
}

BOOL MenuWindowSendMailDel (void){

	//次回开いたときのためにバックアップとる
	MailStatusSTBackUp.Mode=MailStatusST.Mode;
	MailStatusSTBackUp.Index=MailStatusST.Index;
	MailStatusSTBackUp.SendMailMonsterNo=MailStatusST.SendMailMonsterNo;
	MailStatusSTBackUp.SendItem=MailStatusST.SendItem;
	MailStatusSTBackUp.GroupMailFlag=MailStatusST.GroupMailFlag;
	strcpy(MailStatusSTBackUp.MailBuff,mailEditStr.buffer);

	//添付アイテムのクリーン
	if(MailStatusST.SendItem!=-1){
		ItemNoOpe[MailStatusST.SendItem]--;
		MailStatusST.SendItem=-1;
		MailStatusST.SendMailMonsterNo=-1;
	}

	//グループウインドウを闭じます
	if(WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo->flag |= WIN_INFO_DEL;
	}

	return TRUE;
}


BOOL MenuWindowSendMailDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

//ペットメール
BOOL MenuWindowSendPetMail( int mouse )
{
	int index,i;

	if (mouse==WIN_INIT){
		//通常メールがあったら闭じます
		if(WindowFlag[MENU_WINDOW_ADDRESS_SENDMAIL].wininfo!=NULL){
			WindowFlag[MENU_WINDOW_ADDRESS_SENDMAIL].wininfo->flag |= WIN_INFO_DEL;
		}

		//ペットメールスキル持ってるモンスターリストの作成
		PetMailMonsterList.count=0;
		for( i = 0; i < MAX_PET; i++ ){
			//ペットいるか
			if(pet[sortPet[i].index].useFlag){
				index = sortPet[i].index;
				//ペットメールスキル持ってるかチェック
				if(checkPetMail( index )==1){
					PetMailMonsterList.count++;
				}
			}
		}
		if(PetMailMonsterList.count>0)
			MailStatusST.SendMailMonsterNo=0;

#ifdef PUK3_MAIL_ETC2
		//以前のウインドウからひきつぐ？
		if(CheckMailBackUp(MailStatusST,MailStatusSTBackUp))
			strcpy(InitStrStructMail.str,MailStatusSTBackUp.MailBuff);

		//ダイアログ初期化
		SetInputStr(&InitStrStructMail,wI->wx,wI->wy,0);
		//フォーカスを取る
		GetKeyInputFocus( &mailEditStr );

		//ここで初期化文字列消しておきます
		InitStrStructMail.str[0]='\0';

		DiarogST.SwAdd=wI->sw[EnumDialogSendMailMessage].Switch;
		((DIALOG_SWITCH *)wI->sw[EnumDialogSendMailMessage].Switch)->InpuStrAdd=&mailEditStr;
#else
		//ダイアログ初期化
		SetInputStr(&InitStrStructMail,wI->wx,wI->wy,0);
		//フォーカスを取る
		GetKeyInputFocus( &mailEditStr );

		DiarogST.SwAdd=wI->sw[EnumDialogSendMailMessage].Switch;
		((DIALOG_SWITCH *)wI->sw[EnumDialogSendMailMessage].Switch)->InpuStrAdd=&mailEditStr;
#endif
	}

	//ペットメールスキル持ってるモンスターリストの作成
	PetMailMonsterList.count=0;
	for( i = 0; i < MAX_PET; i++ ){
		//ペットいるか
		if(pet[sortPet[i].index].useFlag){
			index = sortPet[i].index;
			//ペットメールスキル持ってるかチェック
			if(checkPetMail( index )==1){
				PetMailMonsterList.status[i].flag=TRUE;
				PetMailMonsterList.status[i].index=index;
				PetMailMonsterList.SortMonsterNo[PetMailMonsterList.count]=index;
				PetMailMonsterList.count++;
			}else{
				PetMailMonsterList.status[i].flag=FALSE;
				PetMailMonsterList.status[i].index=index;
			}
		}
	}

	//スキル持ってるモンスターがいない场合はウインドウ关闭
	if(PetMailMonsterList.count==0)
		wI->flag |= WIN_INFO_DEL;

	//现在指定されているモンスターがいない时（いなくなった时）
	if(!pet[PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]].useFlag)
		wI->flag |= WIN_INFO_DEL;

	MenuWindowSendPetMailInit();

	//群邮件でないときグループウインドウを闭じます
	if(WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo!=NULL && MailStatusST.GroupMailFlag!=1){
		WindowFlag[MENU_WINDOW_GROUP_MAIL].wininfo->flag |= WIN_INFO_DEL;
	}

	return TRUE;
}

BOOL MenuWindowSendPetMailDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

//タイトル入力ダイアログ
BOOL MenuSwitchSendMailDialog( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	if( flag & MENU_MOUSE_LEFT ){
		if(DiarogST.SwAdd!=wI->sw[EnumDialogSendMailMessage].Switch){
			//ダイアログ初期化
#ifdef PUK3_MAIL_ETC
			SetInputStr(&InitStrStructMail,wI->wx,wI->wy,1);
#else
			SetInputStr(&InitStrStructMail,wI->wx,wI->wy,0);
#endif
			//フォーカスを取る
			GetKeyInputFocus( &mailEditStr );

			DiarogST.SwAdd=wI->sw[EnumDialogSendMailMessage].Switch;
			((DIALOG_SWITCH *)wI->sw[EnumDialogSendMailMessage].Switch)->InpuStrAdd=&mailEditStr;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}		
		ReturnFlag=TRUE;
	}
#ifdef PUK3_MAIL_ETC
	// きれいではないけど、とりあえずここで
	if ( pNowInputStr != &mailEditStr ){
		StockFontBuffer2( &mailEditStr );
	}
#endif

	return ReturnFlag;

}

//通常メール时のスイッチ处理
BOOL MenuSwitchSendMail( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	char Str[1024];
	char StrEsc[1024];
	int Index,i,j;
	char header[64];
	time_t t;
	struct tm *tt;
	GUILD_MEMBER_INFO	*member;
#ifdef PUK3_PROF
	char name[256];
	char msg[1024];
#endif

	switch(no){
		//ペットメールに变更
		case EnumGraphSendMailToPetMail:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_PETMAIL );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailOn;
			}
			//クリック
			if(flag & MENU_MOUSE_LEFT){
//				wI->flag |= WIN_INFO_DEL;
#ifdef PUK3_MAIL_ETC2
				strcpy(MailStatusSTBackUp.MailBuff,mailEditStr.buffer);
#endif
				openMenuWindow( MENU_WINDOW_ADDRESS_SENDPETMAIL, OPENMENUWINDOW_HIT, 0 );
				openMenuWindow( MENU_WINDOW_ITEM, OPENMENUWINDOW_HIT, 0 );
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailOff;
			}
			break;

		case EnumGraphSendMailSend:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_SEND );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOn;
			}

			//群邮件时
			if(MailStatusST.GroupMailFlag){
				//クリック
				if(flag & MENU_MOUSE_LEFT){
					switch(GMST.MemberType){
						//アドレスメール
						case GroupMailAddressMode:
							//文字列が无く、かつ送る相手がいないときは送らない
							if(mailEditStr.buffer[0]!='\0' && GMST.MemberCount>0){
								strcpy( Str, mailEditStr.buffer );
								// ここは信息をエスケープする。
								makeSendString( Str, StrEsc, sizeof( StrEsc ) );
								for(i=0;i<GMST.MemberCount;i++){
									if(GMST.MemberFlag[i]==1){
										Index=GMST.MemberSort[i];
										nrproto_MSG_send( sockfd, Index, StrEsc, FONT_PAL_WHITE );

										time( &t );
										tt = localtime( &t );
										sprintf( header, "%4d/%2d/%2d %02d:%02d",                   //MLHIDE
											1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

										setMailHistory( Index, 1, header, mailEditStr.buffer );
									}
								}

								// メールヒストリをファイルに保存
								writeMailFile();
								// 家族メールヒストリをファイルに保存
								writeGuildMailFile();
								// 决定音b（ボタンクリック时）
								play_se( SE_NO_OK2, 320, 240 );
								// 送り終わったときだけ初期化する
								mailEditStr.buffer[0] = '\0';
								//このウインドウ关闭
								wI->flag |= WIN_INFO_DEL;
							}else{
								play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
							}
							break;
						//家族メール
						case GroupMailGuildMode:

							member = guildBook.member;

							//文字列が无く、かつ送る相手がいないときは送らない
							if(mailEditStr.buffer[0]!='\0' && GMST.MemberCount>0){
								strcpy( Str, mailEditStr.buffer );
								// ここは信息をエスケープする。
								makeSendString( Str, StrEsc, sizeof( StrEsc ) );
								for(i=0;i<GMST.MemberCount;i++){
									if(GMST.MemberFlag[i]==1){
										Index=member[GMST.MemberSort[i]].address.id;
										nrproto_GML_send( sockfd, Index, StrEsc, FONT_PAL_WHITE );

										time( &t );
										tt = localtime( &t );
										sprintf( header, "%4d/%2d/%2d %02d:%02d",                   //MLHIDE
											1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

										setGuildMailHistory( Index, 1, header, mailEditStr.buffer );
									}
								}


								// メールヒストリをファイルに保存
								writeMailFile();
								// 家族メールヒストリをファイルに保存
								writeGuildMailFile();
								// 决定音b（ボタンクリック时）
								play_se( SE_NO_OK2, 320, 240 );
								// 送り終わったときだけ初期化する
								mailEditStr.buffer[0] = '\0';
								//このウインドウ关闭
								wI->flag |= WIN_INFO_DEL;
							}else{
								play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
							}
							break;

						//家族称号メール
						case GroupMailGuildTitleMode:
							//文字列が无く、かつ送る相手がいないときは送らない
							if(mailEditStr.buffer[0]!='\0' && GMST.MemberCount>0){
								strcpy( Str, mailEditStr.buffer );
								// ここは信息をエスケープする。
								makeSendString( Str, StrEsc, sizeof( StrEsc ) );
								for(i=0;i<GMST.MemberCount;i++){
									if(GMST.MemberFlag[i]==1){
										Index=0x400+GMST.MemberSort[i];
										nrproto_GML_send( sockfd, Index, StrEsc, FONT_PAL_WHITE );

										for(j=0;j<GUILD_MEMBER_MAX;j++){
											if(guildBook.member[j].titleId==GMST.MemberSort[i]){
												time( &t );
												tt = localtime( &t );
												sprintf( header, "%4d/%2d/%2d %02d:%02d",                 //MLHIDE
													1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

												setGuildMailHistory( guildBook.member[j].address.id, 1, header, mailEditStr.buffer );
											}
										}
									}
								}
								// メールヒストリをファイルに保存
								writeMailFile();
								// 家族メールヒストリをファイルに保存
								writeGuildMailFile();
								// 决定音b（ボタンクリック时）
								play_se( SE_NO_OK2, 320, 240 );
								// 送り終わったときだけ初期化する
								mailEditStr.buffer[0] = '\0';
								//このウインドウ关闭
								wI->flag |= WIN_INFO_DEL;
							}else{
								play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
							}
							break;
					}
					ReturnFlag=TRUE;
				}
			}else{
				//通常メール时
				switch(MailStatusST.Mode){
					//アドレスメール
					case EnumMenuWindowAddressBookTypeAddressBook:
						//クリック
						if(flag & MENU_MOUSE_LEFT){

							//文字列が无いときは送らない
							if(mailEditStr.buffer[0]!='\0'){

								Index=MailStatusST.Index;

								strcpy( Str, mailEditStr.buffer );
								// ここは信息をエスケープする。
								makeSendString( Str, StrEsc, sizeof( StrEsc ) );
								nrproto_MSG_send( sockfd, Index, StrEsc, FONT_PAL_WHITE );

								time( &t );
								tt = localtime( &t );
								sprintf( header, "%4d/%2d/%2d %02d:%02d",                     //MLHIDE
									1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

								setMailHistory( Index, 1, header, mailEditStr.buffer );

								// メールヒストリをファイルに保存
								writeMailFile();
								// 家族メールヒストリをファイルに保存
								writeGuildMailFile();
								// 决定音b（ボタンクリック时）
								play_se( SE_NO_OK2, 320, 240 );
								// 送り終わったときだけ初期化する
								mailEditStr.buffer[0] = '\0';

								//このウインドウ关闭
								wI->flag |= WIN_INFO_DEL;
							}else{
								play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
							}

							ReturnFlag=TRUE;
						}
					break;

					//家族メール
					case EnumMenuWindowAddressBookTypeGuildBook:
						//クリック
						if(flag & MENU_MOUSE_LEFT){

							//文字列が无いときは送らない
							if(mailEditStr.buffer[0]!='\0'){

								member = guildBook.member;

								Index=member[MailStatusST.Index].address.id;

								strcpy( Str, mailEditStr.buffer );
								// ここは信息をエスケープする。
								makeSendString( Str, StrEsc, sizeof( StrEsc ) );
								nrproto_GML_send( sockfd, Index, StrEsc, FONT_PAL_WHITE );

								time( &t );
								tt = localtime( &t );
								sprintf( header, "%4d/%2d/%2d %02d:%02d",                     //MLHIDE
									1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

								setGuildMailHistory( Index, 1, header, mailEditStr.buffer );

								// メールヒストリをファイルに保存
								writeMailFile();
								// 家族メールヒストリをファイルに保存
								writeGuildMailFile();
								// 决定音b（ボタンクリック时）
								play_se( SE_NO_OK2, 320, 240 );
								// 送り終わったときだけ初期化する
								mailEditStr.buffer[0] = '\0';
								
								//このウインドウ关闭
								wI->flag |= WIN_INFO_DEL;
							}else{
								play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
							}

							ReturnFlag=TRUE;
						}
					break;
#ifdef PUK3_PROF
					//ミニメール
					case EnumMenuWindowAddressBookTypeMiniMail:
						//クリック
						if(flag & MENU_MOUSE_LEFT){

							//文字列が无いときは送らない
							if(mailEditStr.buffer[0]!='\0'){
								Index=miniMailBook[MailStatusST.Index].id;

								makeEscapeString( (unsigned char *)miniMailBook[MailStatusST.Index].name, (unsigned char *)name, sizeof(name) );
								makeEscapeString( (unsigned char *)mailEditStr.buffer, (unsigned char *)msg, sizeof(msg) );
								sprintf(Str,"%d|%s|%s",Index,name,msg);                       //MLHIDE
								// ここは信息をエスケープする。
								makeSendString( Str, StrEsc, sizeof( StrEsc ) );

								nrproto_PRM_send( sockfd, StrEsc, FONT_PAL_WHITE );

								makeLocalTime(header);
								setMiniMailHistory(MailStatusST.Index,1,header,mailEditStr.buffer);

								// 决定音b（ボタンクリック时）
								play_se( SE_NO_OK2, 320, 240 );
								// 送り終わったときだけ初期化する
								mailEditStr.buffer[0] = '\0';
								
								//このウインドウ关闭
								wI->flag |= WIN_INFO_DEL;
							}else{
								play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
							}

							ReturnFlag=TRUE;
						}
					break;
#endif
				}
			}

		if( flag & MENU_MOUSE_LEFTHOLD ){
			((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOff;
		}
		
		break;

	}

	return ReturnFlag;
}

void MenuWindowSendMailInit(void){

	int Index;

	if(MailStatusST.GroupMailFlag){
		//群邮件时
		switch(GMST.MemberType){
			//アドレスメール
			case GroupMailAddressMode:
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendMailToName].Switch)->text,ML_STRING(828, "群邮件"));
				wI->sw[EnumGraphSendMailToPetMail].Enabled=FALSE;
				break;
			//家族メール
			case GroupMailGuildMode:
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendMailToName].Switch)->text,ML_STRING(828, "群邮件"));
				wI->sw[EnumGraphSendMailToPetMail].Enabled=FALSE;
				break;
			//家族称号メール
			case GroupMailGuildTitleMode:
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendMailToName].Switch)->text,ML_STRING(828, "群邮件"));
				wI->sw[EnumGraphSendMailToPetMail].Enabled=FALSE;
				break;
		}
	}else{
		//群邮件でない时
		switch(MailStatusST.Mode){
			//通常メール	
			case EnumMenuWindowAddressBookTypeAddressBook:
				Index=MailStatusST.Index;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendMailToName].Switch)->text,addressBook[Index].name);
				break;

			//家族メール
			case EnumMenuWindowAddressBookTypeGuildBook:
				Index=MailStatusST.Index;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendMailToName].Switch)->text,guildBook.member[Index].address.name);
				break;
#ifdef PUK3_PROF
			//ミニメール
			case EnumMenuWindowAddressBookTypeMiniMail:
				Index=MailStatusST.Index;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendMailToName].Switch)->text,miniMailBook[Index].name);
				break;
#endif
		}

#ifdef PUK3_PROF
		if(MailStatusST.Mode==EnumMenuWindowAddressBookTypeMiniMail){
			wI->sw[EnumGraphSendMailToPetMail].Enabled=FALSE;
		}else{
#endif
			//ペットメールスキルを持つモンスターがいた时ボタンＯＮ
			if(PetMailMonsterList.count==0){
				wI->sw[EnumGraphSendMailToPetMail].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphSendMailToPetMail].Enabled=TRUE;
			}
#ifdef PUK3_PROF
		}
#endif
	}

	//ダイアログの座标
	SetInputStr(&InitStrStructMail,wI->wx,wI->wy,2);
}

BOOL MenuSwitchPetMailGetItem( int no, unsigned int flag ){

	BOOL ReturnFlag = FALSE;
	int Dropitem;
	int Dragitem;
	BLT_MEMBER bm2={0};

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	if ( flag& MENU_MOUSE_DROP) {
#ifdef PUK2_NEWDRAG
		Dropitem = (int)WinDD_ObjData();
		if(Dropitem>=MAX_EQUIP_ITEM){					//装备されてる物はダメ
#else
		Dropitem = (int)WinDD_GetObject();
		if(Dropitem>=8){					//装备されてる物はダメ
#endif
			//ドロップされたアイテム
			if(pc.item[Dropitem].flag & ITEM_ETC_FLAG_SEND){
				if(MailStatusST.SendItem!=-1){
					ItemNoOpe[MailStatusST.SendItem]--;
				}
				MailStatusST.SendItem=Dropitem;
				ItemNoOpe[Dropitem]++;
				flag &= ~MENU_MOUSE_DROP;
			}else{
				// 遅れないアイテム
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
			}
		}else{
			// 装备されてるアイテム
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	// 通常时右键したとき
	if( flag & MENU_MOUSE_LEFT ){
		// その场所にアイテムがあるなら
		if ( MailStatusST.SendItem >= 0 ){
			// ドラッグ开始
#ifdef PUK2_NEWDRAG
			DragItem( MailStatusST.SendItem, TRUE );
#else
			WinDD_DragStart( WINDD_ITEM, (void *)(MailStatusST.SendItem) );
#endif

			ItemNoOpe[MailStatusST.SendItem]--;
			MailStatusST.SendItem=-1;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
		ReturnFlag=TRUE;
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_ADDRESS_SENDPETMAIL ){
			Dragitem = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( !pc.item[Dragitem].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(Dragitem), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		Dragitem = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_ADDRESS_SENDPETMAIL ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[Dragitem].graNo, 0, &bm2 );
		}
#endif
	}

	// アイテム选择枠
	if( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		StockBoxDispBuffer( wI->wx + wI->sw[no].ofx, wI->wy + wI->sw[no].ofy,
			wI->wx + wI->sw[no].ofx + wI->sw[no].sx, wI->wy + wI->sw[no].ofy + wI->sw[no].sy, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	return ReturnFlag;

}

//ペットメール时のスイッチ处理
BOOL MenuSwitchSendPetMail( int no, unsigned int flag ){

	BOOL ReturnFlag = FALSE;
	char Str[600];
	char StrEsc[600];
	int Index;
	char header[64];
	time_t t;
	struct tm *tt;

	switch(no){	
		//通常メールに变更
		case EnumGraphSendPetMailToSendMail:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailBackOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_BACK );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailBackOn;
			}
			//クリック
			if(flag & MENU_MOUSE_LEFT){
				wI->flag |= WIN_INFO_DEL;
				openMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL, OPENMENUWINDOW_HIT, 0 );
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailBackOff;
			}
			break;

		case EnumGraphSendPetMailDown:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailDownOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_PETCHANGE );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailDownOn;
			}
			//クリック
			if(flag & MENU_MOUSE_LEFT){
				MailStatusST.SendMailMonsterNo--;
				if(MailStatusST.SendMailMonsterNo<0)
					MailStatusST.SendMailMonsterNo=PetMailMonsterList.count-1;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailDownOff;
			}
			break;


		case EnumGraphSendPetMailUp:
			//重なってるかチェック
			if(flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailUpOver;
				strcpy( OneLineInfoStr, MWONELINE_MAIL_PETCHANGE );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailUpOn;
			}
			//クリック
			if(flag & MENU_MOUSE_LEFT){
				MailStatusST.SendMailMonsterNo++;
				if(MailStatusST.SendMailMonsterNo>PetMailMonsterList.count-1)
					MailStatusST.SendMailMonsterNo=0;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailPetMailUpOff;
			}
			break;


		//通常ペットメールを送信
		case EnumGraphSendPetMailSend:
			switch(MailStatusST.Mode){
				//アドレスメール
				case EnumMenuWindowAddressBookTypeAddressBook:
					//重なってるかチェック
					if(flag & MENU_MOUSE_OVER){
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOver;
						strcpy( OneLineInfoStr, MWONELINE_MAIL_SEND );
						ReturnFlag=TRUE;
					}else{
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOn;
					}
					//クリック
					if(flag & MENU_MOUSE_LEFT){

						//文字列が无いときは送らない
						if(mailEditStr.buffer[0]!='\0'
#ifdef PetField
							//フィールド置き中は送らない
							&& checkPetField(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]) ==FALSE
#endif
#ifdef PUK3_MONSTER_HELPER_MMLOCK
							//ペットヘルプのペットはダメ
							&& checkPetHelp(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]) ==FALSE
#endif
							){
						
							Index=MailStatusST.Index;

							strcpy( Str, mailEditStr.buffer );
							// ここは信息をエスケープする。
							makeSendString( Str, StrEsc, sizeof( StrEsc ) );
							nrproto_PMSG_send( sockfd, Index, PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo],
								MailStatusST.SendItem, StrEsc, FONT_PAL_WHITE );

							time( &t );
							tt = localtime( &t );
							sprintf( header, "%4d/%2d/%2d %02d:%02d",                      //MLHIDE
								1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

							setMailHistory( Index, 1, header, mailEditStr.buffer );


							// メールヒストリをファイルに保存
							writeMailFile();
							// 家族メールヒストリをファイルに保存
							writeGuildMailFile();
							// 决定音b（ボタンクリック时）
							play_se( SE_NO_OK2, 320, 240 );
							// 送り終わったときだけ初期化する
							mailEditStr.buffer[0] = '\0';

							//このウインドウ关闭
							wI->flag |= WIN_INFO_DEL;
						}else{
							play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
						}

						ReturnFlag=TRUE;
					}
				break;
				//家族メール
				case EnumMenuWindowAddressBookTypeGuildBook:
					//重なってるかチェック
					if(flag & MENU_MOUSE_OVER){
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOver;
						strcpy( OneLineInfoStr, MWONELINE_MAIL_SEND );
						ReturnFlag=TRUE;
					}else{
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOn;
					}
					//クリック
					if(flag & MENU_MOUSE_LEFT){

						//文字列が无いときは送らない
						if(mailEditStr.buffer[0]!='\0'
#ifdef PetField
							//フィールド置き中は送らない
							&& checkPetField(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]) ==FALSE
#endif
#ifdef PUK3_MONSTER_HELPER_MMLOCK
							//ペットヘルプのペットはダメ
							&& checkPetHelp(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]) ==FALSE
#endif
							){

							Index=guildBook.member[MailStatusST.Index].address.id;

							strcpy( Str, mailEditStr.buffer );
							// ここは信息をエスケープする。
							makeSendString( Str, StrEsc, sizeof( StrEsc ) );
							nrproto_PGML_send( sockfd, Index, PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo],
								MailStatusST.SendItem, StrEsc, FONT_PAL_WHITE );

							time( &t );
							tt = localtime( &t );
							sprintf( header, "%4d/%2d/%2d %02d:%02d",                      //MLHIDE
								1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

							setGuildMailHistory( Index,1, header, mailEditStr.buffer );

							// メールヒストリをファイルに保存
							writeMailFile();
							// 家族メールヒストリをファイルに保存
							writeGuildMailFile();
							// 决定音b（ボタンクリック时）
							play_se( SE_NO_OK2, 320, 240 );
							// 送り終わったときだけ初期化する
							mailEditStr.buffer[0] = '\0';
							
							//このウインドウ关闭
							wI->flag |= WIN_INFO_DEL;
						}

						ReturnFlag=TRUE;
					}
				break;
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_SendMailSendOff;
			}

			break;
	}

	return ReturnFlag;

}

void MenuWindowSendPetMailInit(void){

	int Index;
	char str[64];

	//相手先の名称
	switch(MailStatusST.Mode){
		//通常メール	
		case EnumMenuWindowAddressBookTypeAddressBook:
			Index=MailStatusST.Index;
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendPetMailToName].Switch)->text,addressBook[Index].name);
			break;

		//家族メール
		case EnumMenuWindowAddressBookTypeGuildBook:
			Index=MailStatusST.Index;
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendPetMailToName].Switch)->text,guildBook.member[Index].address.name);
			break;
	}

	//モンスターの名称
	if(pet[PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]].freeName[0]!='\0')
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendPetMailMonsterName].Switch)->text,
			pet[PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]].freeName);
	else
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextSendPetMailMonsterName].Switch)->text,
			pet[PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo]].name);

#ifdef PetField
#ifdef PUK3_MONSTER_HELPER_MMLOCK
	//フィールド置き、ペットヘルプ中はペットメールで使用できないのでグレーで表示します
	if(checkPetField(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo] )||
		checkPetHelp(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo] )){
#else
	//フィールド置き中はペットメールで使用できないのでグレーで表示します
	if(checkPetField(PetMailMonsterList.SortMonsterNo[MailStatusST.SendMailMonsterNo])){
#endif
		((TEXT_SWITCH *)wI->sw[EnumTextSendPetMailMonsterName].Switch)->color=FONT_PAL_GRAY;
	}else{
		((TEXT_SWITCH *)wI->sw[EnumTextSendPetMailMonsterName].Switch)->color=FONT_PAL_WHITE;
	}
#endif

	//添付するアイテム
	//ここだけ例外的に通常と异なる表示方法をとっています
	if(MailStatusST.SendItem==-1){
		//现在未设定もしくはアイテム添付しない
		wI->sw[EnumGraphSendPetMailSendItem].Enabled=FALSE;
	}else{
		//添付するアイテムがある
		wI->sw[EnumGraphSendPetMailSendItem].Enabled=FALSE;
		StockDispBuffer( wI->wx+194-18, wI->wy+240-16, DISP_PRIO_WIN2,pc.item[MailStatusST.SendItem].graNo, 0);

		// 个数表示
		if( pc.item[MailStatusST.SendItem].num > 0 ){
			sprintf( str, "%3d", pc.item[MailStatusST.SendItem].num );         //MLHIDE
			StockFontBuffer( wI->wx+192-18, wI->wy+248-16, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
		}
	}

	//ダイアログの座标
	SetInputStr(&InitStrStructMail,wI->wx,wI->wy,2);

}

//以前开いたメールから内容を引き継ぐかチェック
BOOL CheckMailBackUp(MailStatusStruct MS1,MailStatusStruct MS2){
	
	BOOL ReturnFlag=FALSE;
	
	//Modeが同じ
	if(MS1.Mode==MS2.Mode){
		//Indexが同じ
		if(MS1.Index==MS2.Index){
			ReturnFlag=TRUE;
		}
	}

	return ReturnFlag;
}

#ifdef PetField
//ペットがフィールド状态か调べる
BOOL checkPetField(int index){

	if(pet[index].battleSetting==PET_SETTING_FIELD)
		return TRUE;

	return FALSE;

}
#endif
#ifdef PUK3_MONSTER_HELPER_MMLOCK
//ペットがペットヘルプ中か调べる
BOOL checkPetHelp(int index){

	if ( CheckRideSkillCreate() ){
		if(pet[index].battleSetting==PET_SETTING_BATTLE)
			return TRUE;
	}

	return FALSE;

}
#endif