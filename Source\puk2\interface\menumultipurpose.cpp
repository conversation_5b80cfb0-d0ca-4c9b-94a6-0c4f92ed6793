﻿static void MenuWindowMultiPurposeSetContents ();
static void MenuWindowMultiPurposeSetButtonView ();
static void MenuWindowMultiPurposeClose ();
static void MenuWindowCentering (char* dest, char* src);


//ウインドウ处理
BOOL MenuWindowMultiPurpose (int mouse)
{
	if (mouse == WIN_INIT) {
		MenuWindowMultiPurposeSetButtonView ();
		MenuWindowMultiPurposeSetContents ();
		
		if (multiPurposeWindowInfo.funcInitialize){
			(*multiPurposeWindowInfo.funcInitialize) (mouse);
		}
		return TRUE;
	}

	
	return TRUE;
}

//ウインドウ描画处理
BOOL MenuWindowMultiPurposeDraw (int mouse)
{

	displayMenuWindow ();
	return TRUE;
}



//スイッチ处理
//Yesボタン
BOOL MenuWindowMultiPurposeYes (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_multiPurposeYesButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_multiPurposeYesButtonOver;
	}

	//このウインドウではYesを押すときだけCtrキーが必要です。
	if( (flag & MENU_MOUSE_LEFT) && (VK[VK_CONTROL] & KEY_ON)){
		MenuWindowMultiPurposeClose ();
		if (multiPurposeWindowInfo.funcYes)
			(*multiPurposeWindowInfo.funcYes) (no, flag);
		
		play_se( SE_NO_CLICK, 320, 240 ); // クリック音
		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//Noボタン
BOOL MenuWindowMultiPurposeNo (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_multiPurposeNoButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_multiPurposeNoButtonOver;
	}

	if( flag & MENU_MOUSE_LEFT ){
		MenuWindowMultiPurposeClose ();
		if (multiPurposeWindowInfo.funcNo)
			(*multiPurposeWindowInfo.funcNo) (no, flag);
		
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//Okボタン
BOOL MenuWindowMultiPurposeOk (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_multiPurposeOkButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_multiPurposeOkButtonOver;
	}

	if( flag & MENU_MOUSE_LEFT ){
		MenuWindowMultiPurposeClose ();
		if (multiPurposeWindowInfo.funcOk)
			(*multiPurposeWindowInfo.funcOk) (no, flag);
		
		play_se( SE_NO_CLICK, 320, 240 ); // クリック音
		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//Cancelボタン
BOOL MenuWindowMultiPurposeCancel (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_multiPurposeCancelButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_multiPurposeCancelButtonOver;
	}

	if( flag & MENU_MOUSE_LEFT ){
		MenuWindowMultiPurposeClose ();
		if (multiPurposeWindowInfo.funcCancel)
			(*multiPurposeWindowInfo.funcCancel) (no, flag);
		
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//Prevボタン
BOOL MenuWindowMultiPurposePrev (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_multiPurposeBackButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_multiPurposeBackButtonOver;
//		strcpy( OneLineInfoStr, "名片显示顺序排序。" );
	}

	if( flag & MENU_MOUSE_LEFT ){
		MenuWindowMultiPurposeClose ();
		if (multiPurposeWindowInfo.funcPrev)
			(*multiPurposeWindowInfo.funcPrev) (no, flag);
		
		play_se( SE_NO_CLICK, 320, 240 ); // クリック音
		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//Nextボタン
BOOL MenuWindowMultiPurposeNext (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = GID_multiPurposeNextButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = GID_multiPurposeNextButtonOver;
//		strcpy( OneLineInfoStr, "名片显示顺序排序。" );
	}

	if( flag & MENU_MOUSE_LEFT ){
		MenuWindowMultiPurposeClose ();
		if (multiPurposeWindowInfo.funcNext)
			(*multiPurposeWindowInfo.funcNext) (no, flag);
		
		play_se( SE_NO_CLICK, 320, 240 ); // クリック音
		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}



//ウインドウ关闭
static void MenuWindowMultiPurposeClose ()
{
	WindowFlag[MENU_WINDOW_MULTIPURPOSE].wininfo->flag |= WIN_INFO_DEL;
}

static void MenuWindowMultiPurposeSetContents ()
{
	TEXT_SWITCH* text;
	char dest[1024];
	char contentStr[1024];
	char* tok;
	int i = 0;

	for (i = EnumMultiPurposeContent0; i < EnumMultiPurposeEndOfContent; ++i) {
		text = (TEXT_SWITCH*) wI->sw[i].Switch;
		strcpy (text->text, "");
	}

	i = 0;

	strcpy(contentStr,multiPurposeWindowInfo.content);
	tok = strtok (contentStr, "\n");                                     //MLHIDE
	while (tok) {
		MenuWindowCentering (dest, tok);
		text = (TEXT_SWITCH*) wI->sw[EnumMultiPurposeContent0 + i].Switch;
		strcpy (text->text, dest);
		tok = strtok (NULL, "\n");                                          //MLHIDE
		++i;
	}
}

static void MenuWindowMultiPurposeSetButtonView ()
{
	int i, j=0;
	int type = multiPurposeWindowInfo.type;

	for (i = 0; i < 6; ++i) {
		wI->sw[EnumMultiPurposeOk + i].Enabled = FALSE;
		if (type & 1) {
			wI->sw[EnumMultiPurposeOk + i].Enabled = TRUE;
			++j;
		}
		type >>= 1;
	}
// butttonが1个だった时の表示位置调整ハイル
}

static void MenuWindowCentering (char* dest, char* src)
{
	int blank;
	int i;
	int size = (multiPurposeWindowInfo.size * WIDTH_OF_FRAME) - 20;
	int width = strlen (src) * 12;

	blank = ((size - width) / 2) / 12;

	strcpy (dest, "");
	for (i = 0; i < blank; ++i) 
		strcat (dest, " ");                                                 //MLHIDE

	strcat (dest, src);
}

