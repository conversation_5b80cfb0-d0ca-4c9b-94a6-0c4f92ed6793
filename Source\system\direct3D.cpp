﻿/************************/
//	direct3D.cpp		*/
/************************/
// Win32の必要のない机能は省きます
//#define WIN32_LEAN_AND_MEAN

//#define INITGUID				// COMインターフェイスの识别子の初期化

//#define  SHADE_GOURAUD

#include "../systeminc/system.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/direct3D.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/map.h"
#include "../systeminc/main.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/mapEffect.h"
#include "../systeminc/keyboard.h"

// 手动定义Direct3D Retained Mode GUID (因为新版dxguid.lib不包含这些)
const GUID IID_IDirect3DRM2 = {0x4516ec83, 0x8f20, 0x11d0, {0x9b, 0x6d, 0x00, 0x00, 0xc0, 0x78, 0x1b, 0xc3}};

BOOL bD3DReady = FALSE;							// セットアップが完了したことを示すフラグ

LPDIRECT3DRM			lpD3DRM = NULL;			// Direct3Dの保持モード
LPDIRECT3DRM2			lpD3DRM2 = NULL;		// Direct3Dの保持モード２
LPDIRECT3DRMFRAME2		lpD3DScene = NULL;		// Direct3Dの基准フレーム
LPDIRECT3DRMFRAME2		lpD3DCamera = NULL;		// Direct3Dのカメラ用フレーム
//LPDIRECTDRAWCLIPPER		lpDDClipper = NULL;		// DirectDrawのクリッパ
LPDIRECT3DRMDEVICE2		lpD3DRMDevice = NULL;	// Direct3Dデバイス
LPDIRECT3DRMVIEWPORT	lpD3DViewport = NULL;	// Direct3Dのビューポート
LPDIRECT3DRMANIMATIONSET	lpAnimSet = NULL;	// アニメーションセット

// 顶点の配列
D3DVECTOR archVertices[] = {	 
								 1.0f,  1.0f,  1.0f,	// 0
								 1.0f,  1.0f, -1.0f,	// 1
								-1.0f,  1.0f, -1.0f,	// 2
								-1.0f,  1.0f,  1.0f,	// 3
								
								 1.0f, -1.0f,  1.0f,	// 4
								-1.0f, -1.0f,  1.0f,	// 5
								-1.0f, -1.0f, -1.0f,	// 6
								 1.0f, -1.0f, -1.0f,	// 7
								 
								-1.0f,  1.0f, -1.0f,	// 8
								-1.0f, -1.0f, -1.0f,	// 9
								-1.0f, -1.0f,  1.0f,	// 10
								-1.0f,  1.0f,  1.0f,	// 11
								 
								 1.0f,  1.0f,  1.0f,	// 12
								 1.0f, -1.0f,  1.0f,	// 13
								 1.0f, -1.0f, -1.0f,	// 14
								 1.0f,  1.0f, -1.0f,	// 15
								 
								 1.0f,  1.0f,  1.0f,	// 16
								-1.0f,  1.0f,  1.0f,	// 17
								-1.0f, -1.0f,  1.0f,	// 18
								 1.0f, -1.0f,  1.0f,	// 19
								 
								-1.0f,  1.0f, -1.0f,	// 20
								 1.0f,  1.0f, -1.0f,	// 21
								 1.0f, -1.0f, -1.0f,	// 22
								-1.0f, -1.0f, -1.0f,	// 23
								 
								};
#ifndef  SHADE_GOURAUD

// アーチのモデルの顶点とポリゴンを关连付ける配列
int faces[] = { 
				4,  0,  1,  2,  3,  // 上
				4,  4,  5,  6,  7,  // 下
				4,  8,  9, 10, 11,  // 左
				4, 12, 13, 14, 15,  // 右
				4, 16, 17, 18, 19,  // 奥
				4, 20, 21, 22, 23,  // 前
				0 };	// 終わりを示す
#else

// アーチのモデルの顶点とポリゴンを关连付ける配列（法线あり）
int faces[] = { 
				4,  0, 0,  1, 0,  2, 0,  3, 0,  // 上
				4,  4, 1,  5, 1,  6, 1,  7, 1,  // 下
				4,  8, 2,  9, 2, 10, 2, 11, 2,  // 左
				4, 12, 3, 13, 3, 14, 3, 15, 3,  // 右
				4, 16, 4, 17, 4, 18, 4, 19, 4,  // 奥
				4, 20, 5, 21, 5, 22, 5, 23, 5,  // 前
				0 };	// 終わりを示す

#endif

// 顶点の法线ベクトル（各方向ごとに定义）
D3DVECTOR archNormals [] = { 
					0.0f, 1.0f,  0.0f,	// 1 - 上向き
					0.0f, -1.0f, 0.0f,	// 3 - 下向き
					-1.0f, 0.0f,  0.0f,	// 0 - 左向き
					1.0f, 0.0f,  0.0f,	// 2 - 右向き
					0.0f, 0.0f,  1.0f,	// 5 - z轴の正方向の向き
					0.0f, 0.0f,  -1.0f,	// 4 - z轴の负方向の向き
					};	

// Direct3Dの保持モードを初期化します
BOOL InitDirect3DRetainedMode( void );

// Direct3Dの保持モードを終了します
void ReleaseDirect3DRetainedMode( void );

// デバイスとビューポートを作成します
BOOL CreateDeviceAndViewport( void );

// メッシュなどを作りシーンを作成します
BOOL BuildScene( LPDIRECT3DRMFRAME2 lpParentFrame, float x, float y, float z );

// 描画オプションを设定します (照明や品质など)
BOOL SetRenderingOptions( void );

//*****************************************************************************
// Direct3D保持モードの初期化
//*****************************************************************************
BOOL InitDirect3DRetainedMode( void )
	{
	// インターフェイスを作成します。失败した场合には、
	// エラー信息を表示し、FALSEを返します
	
#if 1
	//取消使用d3drm.lib后需要注释Direct3DRMCreate函数,所以添加了#ifdef DIRECT3D_ON
	//如果启用需要在项目中重新调用d3drm.lib
#ifdef DIRECT3D_ON
	// D3DRMオブジェクトの作成。
	if( Direct3DRMCreate( &lpD3DRM ) != D3DRM_OK ){
		MessageBox(hWnd,"Could not create Retained Mode Interface!",NULL,MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;	// 作成に失败
	}
#endif
#else
	// DirectDrawからD3DRMオブジェクトを作成。
	if( lpDraw->lpDD2->QueryInterface( IID_IDirect3DRM, (LPVOID *)&lpD3DRM ) != DD_OK )
	{
		MessageBox( hWnd, "D3DRM QueryInterface Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;
	}
#endif

	// 新しいバージョンのD3DRMオブジェクトの作成。
	if( lpD3DRM->QueryInterface( IID_IDirect3DRM2, (LPVOID *)&lpD3DRM2 ) != DD_OK )
	{
		MessageBox( hWnd, "D3DRM2 QueryInterface Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
		lpD3DRM->Release();	// 古いバージョンを开放
		return FALSE;
	}
	
	lpD3DRM->Release();	// 古いバージョンを开放
	
	// シーンを作成します
	if( lpD3DRM2->CreateFrame( NULL, &lpD3DScene ) != D3DRM_OK ){
		MessageBox( hWnd,"Could not create scene!", NULL,MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;	// 作成に失败
	}

	// カメラを作成します
	if( lpD3DRM2->CreateFrame(lpD3DScene, &lpD3DCamera ) != D3DRM_OK ){
		MessageBox( hWnd, "Could not create camera frame!", NULL,MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;	// 作成に失败
	}

	// カメラの位置を设定します
	if( lpD3DCamera->SetPosition( lpD3DScene, 0.0f, 0.0f, -10.0f ) != D3DRM_OK ){
		MessageBox( hWnd, "Could not set camera position!", NULL,MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;	// 作成に失败
	}

	// カメラの方向を设定します（フレームの回転）
	if( FAILED( lpD3DCamera->SetOrientation( lpD3DScene, 0.0f, 0.0f, 1.0f, 0.0f, 1.0f, 0.0f ) ) )
		return FALSE;
		
	// デバイスとビューポートを作成します
	if( !CreateDeviceAndViewport() ){
		MessageBox( hWnd, "Could not create viewport!", NULL,MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;	// 作成に失败
	}

	// 最初にレンダリングオプションを设定します
	if( !SetRenderingOptions() )
		return FALSE;
		
	// シーンを作成します
	if( !BuildScene( lpD3DScene, 0, 0, 1 ) ){
		MessageBox( hWnd, "Could not create scene! Mesh", NULL,MB_OK | MB_ICONSTOP ); //MLHIDE
		return FALSE;	// 作成に失败
	}
	

	bD3DReady = TRUE;
	
	return TRUE;	// 作成に成功
}

//*****************************************************************************
// デバイスとビューポートの作成
//*****************************************************************************
BOOL CreateDeviceAndViewport( void )
{

	// D3D保持モードデバイスを作成します
	// バックバッファからデバイスを作成します
	// こうすると、D3DRMはバックバッファにレンダリングします
#ifdef PUK2
	if( lpD3DRM2->CreateDeviceFromSurface( NULL, (IDirectDraw *)lpDraw->lpDD2, (LPDIRECTDRAWSURFACE)lpDraw->lpBACKBUFFER, &lpD3DRMDevice ) != D3DRM_OK )
		return FALSE;
#else
	if( lpD3DRM2->CreateDeviceFromSurface( NULL, (IDirectDraw *)lpDraw->lpDD2, lpDraw->lpBACKBUFFER, &lpD3DRMDevice ) != D3DRM_OK )
		return FALSE;
#endif

	// D3D保持モードデバイスを作成します
//	if(lpD3DRM2->CreateDeviceFromClipper(lpDDClipper, NULL,
//				rect.right, rect.bottom, &lpD3DRMDevice) != D3DRM_OK)
//		return FALSE;

	// デバイスがダブルバッファリングされることを确认します
	lpD3DRMDevice->SetBufferCount( 1 );

	// 最后にビューポートを作成します
	if( lpD3DRM2->CreateViewport( lpD3DRMDevice, lpD3DCamera,
		0, 0, lpD3DRMDevice->GetWidth(), lpD3DRMDevice->GetHeight(), 
		&lpD3DViewport ) != D3DRM_OK )
		return FALSE;

	// 背景の奥行を、プレーヤーがダンジョン内で见ることができる
	// 最大距离に设定します。こうすると、ダンジョン全体の视野を
	// 邪魔することなく、远くのクリッピングプレーンをできるだけ近くに
	// 设定できます
	lpD3DViewport->SetBack( 70.0f );

	// フロントクリッピングプレーンを非常に近くに设定します
	lpD3DViewport->SetFront( 0.5f );
	//lpD3DViewport->SetFront( 10.0f );
	
	return TRUE;
}

#define	JAPANESE_EDITION

//*****************************************************************************
// レンダリングオプションの设定
//*****************************************************************************
BOOL SetRenderingOptions(void)
{
	LPDIRECT3DRMLIGHT pAmbient = NULL;		// アンビエントライト
	LPDIRECT3DRMLIGHT pDirectional = NULL;	// ディレクショナルライト


#ifdef JAPANESE_EDITION
	
	LPDIRECT3DRMLIGHTARRAY	lpLights;

	lpD3DScene->GetLights(&lpLights);
	if(0 == lpLights->GetSize())
	{
#endif

	// ディレクショナルライトを作成します
	//if(FAILED(lpD3DRM2->CreateLightRGB(D3DRMLIGHT_DIRECTIONAL, 0.5f, 0.5f, 0.5f,
	if( FAILED( lpD3DRM2->CreateLightRGB( D3DRMLIGHT_DIRECTIONAL, 1.0f, 1.0f, 1.0f,
	//if(FAILED(lpD3DRM2->CreateLightRGB(D3DRMLIGHT_PARALLELPOINT, 1.0f, 1.0f, 1.0f,
			 &pDirectional ) ) )
		return FALSE;

	// 照明をメインのシーンに追加します
	if( FAILED( lpD3DScene->AddLight( pDirectional ) ) )
		return FALSE;

	// アンビエントライトを作成します
	//if(FAILED(lpD3DRM2->CreateLightRGB(D3DRMLIGHT_AMBIENT, 0.0f, 0.0f, 0.0f,
	//if(FAILED(lpD3DRM2->CreateLightRGB(D3DRMLIGHT_AMBIENT, 0.6f, 0.6f, 0.6f,
	if(FAILED(lpD3DRM2->CreateLightRGB(D3DRMLIGHT_AMBIENT, 0.5f, 0.5f, 0.5f,
	//if( FAILED( lpD3DRM2->CreateLightRGB( D3DRMLIGHT_AMBIENT, 1.0f, 1.0f, 1.0f,
			 &pAmbient ) ) )
		return FALSE;

	// アンビエントライトをシーンに追加します
	if( FAILED( lpD3DScene->AddLight( pAmbient ) ) )
		return FALSE;

#ifdef JAPANESE_EDITION
	}
	lpLights->Release();
#endif

	// シーンの背景を黒に设定します
	lpD3DScene->SetSceneBackground( D3DRGB( 1.0f, 1.0f, 1.0f ) );
	//lpD3DScene->SetSceneBackground( D3DRGB( 0, 0, 0 ) );
	//lpD3DScene->SetSceneBackground( 0 );

	// シェーディングモードを石头ローシェーディングに设定し、ポリゴンを涂りつぶします
	lpD3DRMDevice->SetQuality(D3DRMLIGHT_ON | D3DRMFILL_SOLID |D3DRMSHADE_GOURAUD);
	//lpD3DRMDevice->SetQuality(D3DRMLIGHT_ON | D3DRMFILL_SOLID );
	//lpD3DRMDevice->SetQuality( D3DRMLIGHT_ON | D3DRMSHADE_GOURAUD);

	// 描画モードをワイヤーフレームに设定します
	//lpD3DRMDevice->SetQuality( D3DRMRENDER_WIREFRAME );
	//lpD3DRMDevice->SetQuality( D3DRMRENDER_UNLITFLAT );
	//lpD3DRMDevice->SetQuality( D3DRMRENDER_FLAT );
	//lpD3DRMDevice->SetQuality( D3DRMRENDER_GOURAUD );
	
	return TRUE;
}

#if 0
//*****************************************************************************
// シーンの作成、メッシュのロードなど
//*****************************************************************************
BOOL BuildScene( void )
{
	LPDIRECT3DRMMESHBUILDER2 pMeshBuilder = NULL;	// メッシュビルダインターフェイス
	LPDIRECT3DRMFRAME2 pArchFrame = NULL;			// アーチを格纳するフレーム
	LPDIRECT3DRMTEXTURE2 pTexture = NULL;			// テクスチャAPI


	// メッシュビルダを作成します
	if( FAILED( lpD3DRM2->CreateMeshBuilder( &pMeshBuilder ) ) )
		return FALSE;

#if 0
	// 顶点の配列から面をロードします
	pMeshBuilder->AddFaces( 24, archVertices, 	// 顶点配列
	
	#ifndef SHADE_GOURAUD
							0, NULL, 	// 法线配列
	#else							
							24, archNormals, 	// 法线配列
	#endif
							(unsigned long *)faces, NULL );

	// すべての面の色を白に设定します。これがテクスチャと组み合わされます
	//pMeshBuilder->SetColor( D3DRGB( 0, 0, 0 ) );
	//pMeshBuilder->SetColor( D3DRGB( 1, 2　1 ) );

#if 1
	
	// テクスチャオブジェクトの作成とロード
	if( FAILED( lpD3DRM2->LoadTexture( "emap2.bmp", &pTexture ) ) )      //MLHIDE
		return FALSE;

	// テクスチャを设定します
	pMeshBuilder->SetTexture( pTexture );

	// テクスチャ座标を设定します。各面は4つの顶点で构成され、
	// 反时计回りです。简单なループによって、テクスチャ座标は
	// すべて同じになるように设定されます
	for( int i = 0 ; i < 24 ; i+= 4 ){
	//	pMeshBuilder->SetTextureCoordinates( i,   0.0f, 0.0f );
	//	pMeshBuilder->SetTextureCoordinates( i+1, 1.0f, 0.0f );
	//	pMeshBuilder->SetTextureCoordinates( i+2, 1.0f, 1.0f );
	//	pMeshBuilder->SetTextureCoordinates( i+3, 0.0f, 1.0f );
		
		pMeshBuilder->SetTextureCoordinates( i+3, 0.0f, 0.0f );
		pMeshBuilder->SetTextureCoordinates( i+2, 1.0f, 0.0f );
		pMeshBuilder->SetTextureCoordinates( i+1, 1.0f, 1.0f );
		pMeshBuilder->SetTextureCoordinates( i+0, 0.0f, 1.0f );
	}
	//pMeshBuilder->Save( "box.x", D3DRMXOF_TEXT,D3DRMXOFSAVE_ALL );
	
#endif

#else
	
	// すべての面の色を白に设定します。これがテクスチャと组み合わされます
	//pMeshBuilder->SetColor( D3DRGB( 1, 1, 1 ) );
	pMeshBuilder->SetColor( D3DRGB( 0, 0, 0 ) );
	// Ｘ形式ファイルから読み込む
	//pMeshBuilder->Load( "EARTH.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//pMeshBuilder->Load( "bono.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	pMeshBuilder->Load( "Toma.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL ); //MLHIDE
	//pMeshBuilder->Load( "Ki.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//pMeshBuilder->Load( "Wood2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//pMeshBuilder->Load( "anim1.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//pMeshBuilder->Load( "pri.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//pMeshBuilder->Load( "pri2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
    
    //pMeshBuilder->Load("pri2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL);
	// テクスチャオブジェクトの作成とロード
//	if(FAILED(pMeshBuilder->Load("pri2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL))){
//		MessageBox(NULL,"Could not read X file",
//			NULL,MB_OK | MB_ICONSTOP);
//	}
#endif
	
	// フレームを作成します 
	if( FAILED( lpD3DRM2->CreateFrame( lpD3DScene, &pArchFrame ) ) )
		return FALSE;

	// メッシュをフレームに追加します
	if( FAILED( pArchFrame->AddVisual( pMeshBuilder ) ) )
		return FALSE;

	// シーン内でのフレームの位置を设定します
	if( FAILED( pArchFrame->SetPosition( lpD3DScene, 0.0f, 0.0f, 0.5f ) ) )
		return FALSE;

	pArchFrame->AddScale( D3DRMCOMBINE_AFTER, 10.0f, 10.0f, 10.0f );
	//pArchFrame->AddScale( D3DRMCOMBINE_AFTER, 1.0f, 1.0f, 1.0f );

	// インターフェイスを解放します
	pArchFrame->Release();
	pMeshBuilder->Release();
//	pTexture->Release();

	return TRUE;
}
#else
//*****************************************************************************
// シーンの作成、メッシュのロードなど
//*****************************************************************************
BOOL BuildScene( LPDIRECT3DRMFRAME2 lpParentFrame, float x, float y, float z )
{
	LPDIRECT3DRMMESHBUILDER2 lpMeshBuilder = NULL;	// メッシュビルダインターフェイス
	LPDIRECT3DRMFRAME2 lpChildFrame = NULL;			// アーチを格纳するフレーム

#define D3D _ANIM

#ifndef D3D_ANIM
	// メッシュビルダを作成します
	if( FAILED( lpD3DRM2->CreateMeshBuilder( &lpMeshBuilder ) ) )
		return FALSE;

	lpMeshBuilder->SetColor( D3DRGB( 1, 1, 1 ) );
	//lpMeshBuilder->SetColor( D3DRGB( 0, 0, 0 ) );
	// Ｘ形式ファイルから読み込む
	//lpMeshBuilder->Load( "EARTH.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//lpMeshBuilder->Load( "bono.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	lpMeshBuilder->Load( "Toma.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL ); //MLHIDE
	//lpMeshBuilder->Load( "Ki.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//lpMeshBuilder->Load( "Wood2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//lpMeshBuilder->Load( "anim1.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//lpMeshBuilder->Load( "pri.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
	//lpMeshBuilder->Load( "pri2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );
    
    //lpMeshBuilder->Load("pri2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL);
	// テクスチャオブジェクトの作成とロード
//	if(FAILED(lpMeshBuilder->Load("pri2.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL))){
//		MessageBox(NULL,"Could not read X file",
//			NULL,MB_OK | MB_ICONSTOP);
//	}

	//lpMeshBuilder->Load( "sin_x_ascii\\sin.x", NULL, D3DRMLOAD_FROMFILE, NULL, NULL );


#endif	
	// フレームを作成します 
	if( FAILED( lpD3DRM2->CreateFrame( lpParentFrame, &lpChildFrame ) ) )
		return FALSE;

#ifndef D3D_ANIM
	// メッシュをフレームに追加します
	if( FAILED( lpChildFrame->AddVisual( lpMeshBuilder ) ) )
		return FALSE;
#endif
	// シーン内でのフレームの位置を设定します
	if( FAILED( lpChildFrame->SetPosition( lpParentFrame, x, y, z ) ) )
		return FALSE;

#ifdef D3D_ANIM
	// アニメーションの読み込み
	lpD3DRM2->CreateAnimationSet( &lpAnimSet );
	//lpAnimSet->Load("walk.x",NULL,D3DRMLOAD_FROMFILE, NULL, NULL, lpChildFrame );
	if( FAILED( lpAnimSet->Load("anim1.x",NULL,D3DRMLOAD_FROMFILE, NULL, NULL, lpChildFrame ) ) ) //MLHIDE
	//if( FAILED( lpAnimSet->Load("walk.x",NULL,D3DRMLOAD_FROMFILE, NULL, NULL, lpChildFrame ) ) )
		return FALSE;
	
#endif
	//lpChildFrame->AddScale( D3DRMCOMBINE_AFTER, 10.0f, 10.0f, 10.0f );
	//lpChildFrame->AddScale( D3DRMCOMBINE_AFTER
	// インターフェイスを解放します
	lpChildFrame->Release();
#ifndef D3D_ANIM
	lpMeshBuilder->Release();
#endif
//	pTexture->Release();

	return TRUE;
}
#endif

//*****************************************************************************
// Direct3D保持モードの后始末
//*****************************************************************************
void ReleaseDirect3DRetainedMode( void )
{
	bD3DReady = FALSE;

	// インターフェイスポインタがNULLでない场合、それを解放します
	if( lpD3DViewport ){
		lpD3DViewport->Release();
		lpD3DViewport = NULL;
	}
	if( lpD3DRMDevice ){
		lpD3DRMDevice->Release();
		lpD3DRMDevice = NULL;
	}
	//if( lpDDClipper )
	//	lpDDClipper->Release();

	if( lpD3DCamera ){
		lpD3DCamera->Release();
		lpD3DCamera = NULL;
	}

	if( lpAnimSet ){
		lpAnimSet->Release();
		lpAnimSet = NULL;
	}

	if( lpD3DScene ){
		lpD3DScene->Release();
		lpD3DScene = NULL;
	}

	if( lpD3DRM2 ){
		lpD3DRM2->Release();
		lpD3DRM2 = NULL;
	}
}

//****************************************************************************
//Ｄ３Ｄテストプロセス
//****************************************************************************
void D3dTestProc( void )
{

	LPDIRECT3DRMFRAMEARRAY lpFrameChildren;	// フレームの配列
	LPDIRECT3DRMFRAME lpChildFrame;			// アーチのフレーム
	static float cameraX = 0.0f, cameraY = 0.0f, cameraZ = -10.0f;	// カメラの座标
	static float animTime = 0;
	static float dAnimTime = 1.0;
	
#ifdef D3D_ANIM
	lpAnimSet->SetTime( animTime ); // アニメーションセットに时间を设定
	animTime += dAnimTime;
#endif	
	// 子フレームの配列を取得します
	lpD3DScene->GetChildren( &lpFrameChildren );

	// アーチのフレームを取得します
	lpFrameChildren->GetElement( 1, &lpChildFrame );

	if( VK[ VK_UP ] & KEY_ON ){
		// キーストロークを基に回転させます
		lpChildFrame->AddRotation( D3DRMCOMBINE_AFTER, 1.0f, 0.0f, 0.0f, 0.01f );
	}
	if( VK[ VK_DOWN ] & KEY_ON ){
		lpChildFrame->AddRotation( D3DRMCOMBINE_BEFORE, 1.0f, 0.0f, 0.0f, 0.01f );
		// キーストロークを基に回転させます
		//lpChildFrame->AddRotation( D3DRMCOMBINE_AFTER, 1.0f, 0.0f, 0.0f, -0.01f );
	}
	//if( VK[ VK_LEFT ] & KEY_ON_ONCE ){ 
	if( VK[ VK_LEFT ] & KEY_ON_REP ){ 
		dAnimTime -= 0.01f;
		//BuildScene( ( LPDIRECT3DRMFRAME2 )lpChildFrame, 1, 0, 0 );
	}
	//if( VK[ VK_RIGHT ] & KEY_ON_ONCE ){
	if( VK[ VK_RIGHT ] & KEY_ON_REP ){
		dAnimTime += 0.01f;
		//BuildScene( ( LPDIRECT3DRMFRAME2 )lpChildFrame, -1, 0, 0 );
	}
	if( VK[ VK_HOME ] & KEY_ON_ONCE ){
	//	lpChildFrame->AddScale( D3DRMCOMBINE_AFTER, 2.0f, 2.0f, 2.0f );
	}
	if( VK[ VK_END ] & KEY_ON_ONCE ){
	//	lpChildFrame->AddScale( D3DRMCOMBINE_AFTER, 0.5f, 0.5f, 0.5f );
	}
	
	// 子フレームの配列を取得します
	lpChildFrame->GetChildren( &lpFrameChildren );
	lpFrameChildren->GetElement( 1, &lpChildFrame );
	
	if( VK[ VK_HOME ] & KEY_ON_ONCE ){
	//	lpChildFrame->AddScale( D3DRMCOMBINE_AFTER, 2.0f, 2.0f, 2.0f );
	}
	if( VK[ VK_END ] & KEY_ON_ONCE ){
	//	lpChildFrame->AddScale( D3DRMCOMBINE_AFTER, 0.5f, 0.5f, 0.5f );
	}
	
	if( VK[ VK_UP ] & KEY_ON ){
		if( lpChildFrame != NULL ){
			// キーストロークを基に回転させます
			lpChildFrame->AddRotation( D3DRMCOMBINE_AFTER, 1.0f, 0.0f, 0.0f, 0.01f );
		}
	}
	if( VK[ VK_DOWN ] & KEY_ON ){
		if( lpChildFrame != NULL ){
			lpChildFrame->AddRotation( D3DRMCOMBINE_BEFORE, 1.0f, 0.0f, 0.0f, 0.01f );
		}
		// キーストロークを基に回転させます
		//lpChildFrame->AddRotation( D3DRMCOMBINE_AFTER, 1.0f, 0.0f, 0.0f, -0.01f );
	}
	
	//lpChildFrame->AddRotation( D3DRMCOMBINE_BEFORE, 1.0f, 0.0f, 0.0f, 0.01f );
	
	//if(wParam == VK_DOWN)
	//	pChildFrame->AddRotation(D3DRMCOMBINE_AFTER, 1.0f, 0.0f, 0.0f, -0.2f);
		
	//if(wParam == VK_LEFT)
	//	pChildFrame->AddRotation(D3DRMCOMBINE_AFTER, 0.0f, 1.0f, 0.0f, 0.2f);

	//if(wParam == VK_RIGHT)
	//	pChildFrame->AddRotation(D3DRMCOMBINE_AFTER, 0.0f, 1.0f, 0.0f, -0.2f);


	// フレーム配列オブジェクトを解放し、
	// ウィンドウを无效化して再描画します
	lpFrameChildren->Release();	
	
	//LPDIRECT3DRMFACEARRAY lpFaceArray;	// フェースの配列
	//LPDIRECT3DRMFACE lpFace;			// フェース
	
	//InvalidateRect(hWnd,NULL,FALSE);
	
	//if( VK[ VK_UP ] & KEY_ON 	) cameraY+= 0.1f;
	//if( VK[ VK_DOWN ] & KEY_ON 	) cameraY-= 0.1f;
	//if( VK[ VK_LEFT ] & KEY_ON 	) cameraX-= 0.1f;
	//if( VK[ VK_RIGHT ] & KEY_ON ) cameraX+= 0.1f;
	if( VK[ VK_HOME ] & KEY_ON 	) cameraZ-= 0.1f;
	if( VK[ VK_END ] & KEY_ON 	) cameraZ+= 0.1f;
	
	// カメラの位置を设定します
	lpD3DCamera->SetPosition( lpD3DScene, cameraX, cameraY, cameraZ );
}

