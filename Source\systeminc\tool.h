﻿#ifndef _TOOL_H_
#define _TOOL_H_

#include<stdio.h>
//用于处理台服战斗协议字符串新增了一个替换函数
#include "../systeminc/version.h"

#if 0
//テキストファイル１行分を読み込み
void freadline( char *ptbuf , FILE *ptFile );
#endif

//その文字列のワード位置までポインタをずらす
int wordchk( char **pp );
//ｑ文字列と同じならその文字数を返し、ｐを动かす
int strstr2( char **pp , char *q );
//その文字列から１ワード分とって更にポインタを移动
int getword(char **pp , char *q);
//その文字列から１ワード分とり数值として返す。
int strint2( char **pp );
//その文字列から１ワード分とってそれをlong型数值として返す。
long strlong2( char **pp );


// SJISの1文字をEUCに变换する。
int sjis2euc( unsigned char *, unsigned char * );
// EUCの1文字をSJISに变换する。
int euc2sjis( unsigned char * );


void sjisStringToEucString( char *buffer );
void eucStringToSjisString( char *buffer );



int getStringToken( char *src , char delim , int count , int maxlen , char *out );
int getIntegerToken( char *src , char delim , int count );
int getIntegerToken( char *src, char delim, int count, int *val );
double getDoubleToken( char *src , char delim , int count );
void chop( char *src );
void shiftIntArray( int *a , int siz , int count );

int a62toi( char * );
int getInteger62Token( char *, char, int );


void jEncode( char *, int, int, char *, int *, int );
void jDecode( char *, int, int, char *, int * );

int strncmpi( char *s1 , char *s2 , int len );
struct tagRECT intToRect( int left, int top, int right, int bottom );
void insertString( char *buffer, char *string, int whereToInsert );
void insertChar( char *buffer, char character, int whereToInsert );
int isOnlySpaceChars( char *data );

char makeCharFromEscaped( char );

char*   makeStringFromEscaped( char* src );
char*   makeEscapeString( unsigned char *, unsigned char *, int );
void makeSendString(char *src, char *dest, int sizeofdest);
char *makeRecvString(char *src);

void deleteCharFromStringNoEscape( char* src , char* dels );

#define arraysizeof( a ) ( sizeof(a)/sizeof(a[0]) )

#ifdef VERSION_TW
void deleteSubString(char* src, const char* sub);
#endif
#endif /*_TOOL_H_*/
