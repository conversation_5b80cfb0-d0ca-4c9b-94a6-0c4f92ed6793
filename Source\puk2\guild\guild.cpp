﻿

// guild.cpp

#include<stdio.h>
#include<stdlib.h>
#include<time.h>

#include"../../systeminc/system.h"

#ifdef PUK2

#include"../../systeminc/ime_sa.h"
#include"../../systeminc/anim_tbl.h"
#include"../../systeminc/pc.h"
#include"../../systeminc/character.h"
#include"../../systeminc/battleMenu.h"
#include"../../systeminc/battleProc.h"
#include"../../systeminc/nrproto_cli.h"
#include"../../systeminc/netmain.h"
#include"../../systeminc/loadsprbin.h"
#include"../../systeminc/savedata.h"
#include"../../systeminc/t_music.h"
#include"../../systeminc/menu.h"
#include"../../systeminc/tool.h"
#include"../../systeminc/map.h"
#include"../../systeminc/field.h"
#include"../../systeminc/pet_skillinfo.h"

#include"../../systeminc/directDraw.h"
#include"../../systeminc/main.h"
#include"../../systeminc/gamemain.h"
#include"../../systeminc/sprmgr.h"
#include"../../systeminc/chat.h"
#include"../../systeminc/font.h"
#include"../../systeminc/action.h"
#include"../../systeminc/sprdisp.h"
#include"../../systeminc/mouse.h"
#include"../../systeminc/pattern.h"
#include"../../systeminc/math2.h"
#include"../../systeminc/process.h"
#include"../../systeminc/keyboard.h"
#include"../../systeminc/battle.h"
#include"../../systeminc/login.h"
#include"../../systeminc/sndcnf.h"
#include"../../systeminc/netproc.h"
#include"../../systeminc/mapGridCursol.h"
#ifdef _CG2_NEWGRAPHIC
#include"../../systeminc/anim_tbl2.h"
#endif

#ifdef _FISHING_WINDOW
#include "../../systeminc/menu_fishing.h"
#endif /* _FISHING_WINDOW */

#ifdef _APPEND_JEWEL
#include "../../systeminc/menu_appendjewel.h"
#endif /* _APPEND_JEWEL */

#ifdef PUK2
////#include "../puk2/interface/menuwin.cpp"
#endif

#include	"../../systeminc/guild.h"

// 家族ブック
GUILD_INFO				guildBook;
ADDRESS_BOOK_SORT_TBL	guildBookSortTbl[GUILD_MEMBER_MAX];

//家族解散のプロトコルが来たとき、ウインドウを关闭
void CloseGuildInfoWindow(void);

//-------------------------------------------------------------------------//
// センドアイテムセレクトウィンドウ                                        //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_sendItemSelectMenuWin =
{//   x,   y,   w,   h, menuNo,
	 368,  0, 272, 252, G_MENU_SEND_ITEM_SELECT_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_SEND_ITEM_SELECT_WIN
	},
};


static	int sendItemSelectMenuItemInfoPage;
static	int sendItemSelectNo;
static	int sendItemSelectXPos, sendItemSelectYPos;


// 初期化
void G_initSendItemSelectMenu( void )
{
	sendItemSelectMenuItemInfoPage = 0;
	sendItemSelectNo = -1;
}


void G_sendItemSelectMenu( int status )
{
	char str[128];
	int ret;
	int no;
	int itemNo;
	int focusNo;
	static int oldFocusNo = -1;
	int i, j;
	int ti, tj;
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif


	// メニュー共通处理
	ret = menuCommonProc( status, &G_sendItemSelectMenuWin,
			G_initSendItemSelectMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		// メールペットエディットウィンドウ关闭
		menuClose( MENU_MAIL_PET_EDIT_WIN );
		return;
	}


	// アイテム栏のカーソルが当っている位置を检索
	focusNo = -1;
	itemNo = -1;
	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( MakeHitBox(
				G_sendItemSelectMenuWin.x +8+j*52,
				G_sendItemSelectMenuWin.y +36+i*53,
				G_sendItemSelectMenuWin.x +8+j*52+47,
				G_sendItemSelectMenuWin.y +36+i*53+47, -1 ) )
			{
				focusNo = MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j;
				ti = i;
				tj = j;
				if( pc.item[focusNo].useFlag
				 && (pc.item[focusNo].flag & ITEM_ETC_FLAG_SEND) )
				{
					itemNo = focusNo;
				}
				break;
			}
		}
	}

	if( focusNo != oldFocusNo )
	{
		sendItemSelectMenuItemInfoPage = 0;
	}


	// アイテムが选择されたか？
	//if( itemNo >= 0 && (mouse.onceState & MOUSE_LEFT_CRICK) ) // ohta
	if( itemNo >= 0 && (mouse.onceState & MOUSE_LEFT_DBL_CRICK) )
	{
		// まだ选择されてないか违うアイテムが选择されていたら
		// いま选んだアイテムを选择状态にする
		if( sendItemSelectNo < 0
		 || sendItemSelectNo != itemNo )
		{
			sendItemSelectNo = itemNo;
			sendItemSelectXPos = tj;
			sendItemSelectYPos = ti;
			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
		else
		// すでに选择しているアイテムをクリックしたら选择を解除する
		{
			sendItemSelectNo = -1;
			// キャンセル音
			play_se( 54, 320, 240 );
		}
	}
	else
	// アイテム情报が出てる时右クリックすると情报ページを切り替え
	if( focusNo >= 0 && (mouse.onceState & MOUSE_RIGHT_CRICK) )
	{
		sendItemSelectMenuItemInfoPage ^= 1;
	}


	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			no = MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j;
			if( pc.item[no].useFlag )
			{
#if 0
				if( pc.item[no].sendFlag == 0 )
				{
					// マスク画像
					StockDispBuffer( G_sendItemSelectMenuWin.x  +32+j*52, G_sendItemSelectMenuWin.y +60+i*53,
						DISP_PRIO_BOX2, CG_SEND_ITEM_SELECT_MASK, 0 );
				}
#endif

				// アイテム画像
#ifdef PUK2
				StockDispBuffer( G_sendItemSelectMenuWin.x  +32+j*52, G_sendItemSelectMenuWin.y +60+i*53,
					DISP_PRIO_ITEM2, pc.item[no].graNo, 0, &bm );
#else
				StockDispBuffer( G_sendItemSelectMenuWin.x  +32+j*52, G_sendItemSelectMenuWin.y +60+i*53,
					DISP_PRIO_ITEM2, pc.item[no].graNo, 0 );
#endif
				// 个数表示
				if( pc.item[no].num > 0 )
				{
					sprintf( str, "%3d", pc.item[no].num );                          //MLHIDE
					StockFontBuffer(
						G_sendItemSelectMenuWin.x +29+j*52,
						G_sendItemSelectMenuWin.y +67+i*53,
						FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
						str, 0, 0 );
				}
			}
		}
	}

	// アイテム选择枠＆名称＆说明表示
	if( focusNo >= 0 )
	{
		// 枠表示
		StockBoxDispBuffer(
			G_sendItemSelectMenuWin.x  +8+tj*52,
			G_sendItemSelectMenuWin.y +36+ti*53,
			G_sendItemSelectMenuWin.x  +8+tj*52+47,
			G_sendItemSelectMenuWin.y +36+ti*53+47,
			DISP_PRIO_MENU, BoxColor, 0 );

		// アイテムの说明表示
		itemInfoWindow(
			G_sendItemSelectMenuWin.x,
			G_sendItemSelectMenuWin.y+G_sendItemSelectMenuWin.h,
			focusNo, sendItemSelectMenuItemInfoPage );
	}

	// 选择されたアイテムに枠表示
	if( sendItemSelectNo >= 0 )
	{
		StockBoxDispBuffer(
			G_sendItemSelectMenuWin.x  +10+sendItemSelectXPos*52,
			G_sendItemSelectMenuWin.y +38+sendItemSelectYPos*53,
			G_sendItemSelectMenuWin.x  +10+sendItemSelectXPos*52+43,
			G_sendItemSelectMenuWin.y +38+sendItemSelectYPos*53+43,
			//DISP_PRIO_MENU, SYSTEM_PAL_RED, 0 );	// 	ohta
			DISP_PRIO_MENU, SYSTEM_PAL_YELLOW, 0 );
	}

	if(pc.item[focusNo].useFlag ){
		//刻印
		if(pc.item[focusNo].flag & ITEM_ETC_FLAG_INCUSE)
		{
			strcpy( OneLineInfoStr,pc.item[ focusNo].name  );
			strcat(OneLineInfoStr,ITEM_INCUSE_STRING);
		}else if(pc.item[ focusNo].flag & ITEM_ETC_FLAG_HANKO){
			//ハンコ
			strcpy( OneLineInfoStr,pc.item[ focusNo].freeName  );
			strcat(OneLineInfoStr,ITEM_HANKO_STRING);
		}
	}else
	// １行インフォ表示
	if( checkFocusMenuClose( &G_sendItemSelectMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// 选择されたアイテム
	if( sendItemSelectNo >= 0 && sendItemSelectNo == focusNo )
	{
		strcpy( OneLineInfoStr, "このアイテムを家族成员に送ります。" );                      //MLHIDE
	}

	// メニュー共通表示处理
	menuCommonDraw( &G_sendItemSelectMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );

	oldFocusNo = focusNo;
}





//-------------------------------------------------------------------------//
// メールペットエディットウィンドウ                                        //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_mailPetEditMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 272, 438, G_MENU_MAIL_PET_EDIT_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_PET_EDIT_WIN
	},
};


#define PET_MAIL_TECH_ID	8900	// ペットメールの技ＩＤ
#define PET_MAIL2_TECH_ID	8901	// ペットメール２の技ＩＤ
#define PET_MAIL3_TECH_ID	8902	// ペットメール３の技ＩＤ
#define PET_MAIL4_TECH_ID	8903	// ペットメール４の技ＩＤ

static	INPUT_STR mailPetEditStr;
static	int petMailSendSelectNo;
static	int petMailSendPetSelectNo;


void G_initMailPetEditMenu( void )
{
	char str[ 400 ];

	//mailEditStr.buffer[0] = '\0';
	//mailEditStr.cnt = 0;
	//mailEditStr.cursorByte = 0;

	// 一时的にコピーする
	strcpy( str, mailPetEditStr.buffer );

	InitInputStr( &mailPetEditStr, G_mailPetEditMenuWin.x +10, G_mailPetEditMenuWin.y+219,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 10, 28, 20, 0 );

	GetKeyInputFocus( &mailPetEditStr );
}


void G_mailPetEditMenu( int status )
{
	GRA_BTN_INFO1 sendBtn =
	{
		G_mailPetEditMenuWin.x +80, G_mailPetEditMenuWin.y+197,
		G_mailPetEditMenuWin.x +40, G_mailPetEditMenuWin.y+187,
		80, 20,
		CG_MAIL_PET_EDIT_SEND_BTN_1, CG_MAIL_PET_EDIT_SEND_BTN_2
	};
	GRA_BTN_INFO1 cancelBtn =
	{
		G_mailPetEditMenuWin.x+192, G_mailPetEditMenuWin.y+197,
		G_mailPetEditMenuWin.x+152, G_mailPetEditMenuWin.y+187,
		80, 20,
		CG_MAIL_PET_EDIT_CANCEL_BTN_1, CG_MAIL_PET_EDIT_CANCEL_BTN_2
	};
	char str[128];
	int ret;
	int len;
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif

	// メニュー共通处理
	ret = menuCommonProc( status, &G_mailPetEditMenuWin,
			G_initMailPetEditMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		// キー入力のフォーカスを元に戾す
		GetKeyInputFocus( &MyChatBuffer );
		// センドアイテムセレクトウィンドウ关闭
		menuClose( G_MENU_SEND_ITEM_SELECT_WIN );
		return;
	}


	// SENDボタン押したか？
	if( (pushGraBtnInfo1( &sendBtn ) & BTN_LEFT_CLICK) )
	{
		if( mailPetEditStr.buffer[0] != '\0' )
		{
			char str[512];
			char strEscape[600]; // エスケープの事を考えてメールのバッファの２倍以上にする。
			char header[64];
			time_t t;
			struct tm *tt;

			time( &t );
			tt = localtime( &t );
			sprintf( header, "%4d/%2d/%2d %02d:%02d",                          //MLHIDE
				1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );
			strcpy( str, mailPetEditStr.buffer );
			// ここは信息をエスケープする。
			makeSendString( str, strEscape, sizeof( strEscape ) );

			nrproto_PMSG_send( sockfd, petMailSendSelectNo, petMailSendPetSelectNo,
			//	sendItemSelectNo, str, FONT_PAL_WHITE );
				sendItemSelectNo, strEscape, FONT_PAL_WHITE );

			setGuildMailHistory( petMailSendSelectNo, 1, header, mailPetEditStr.buffer );

			// メールヒストリー保存
			writeMailFile();
			// 决定音b（ボタンクリック时）
			play_se( SE_NO_OK2, 320, 240 );
			// アドレスブックウィンドウ开く
			menuOpen( G_MENU_ADDRESS_BOOK_WIN );
			// 送り終わったときだけ初期化する
			mailPetEditStr.buffer[0] = '\0';
		}
		else
		{
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}
	else
	// CANCELボタン押したか？
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_LEFT_CLICK) )
	{
		// アドレスブックウィンドウ开く
		menuOpen( G_MENU_ADDRESS_BOOK_WIN );
	}


	// 颜画像表示
#ifdef PUK2
	StockDispBuffer( G_mailPetEditMenuWin.x +52, G_mailPetEditMenuWin.y +84,
		DISP_PRIO_MENU,	guildBook.member[petMailSendSelectNo].address.graNo, 0, &bm );
#else
	StockDispBuffer( G_mailPetEditMenuWin.x +52, G_mailPetEditMenuWin.y +84,
		DISP_PRIO_MENU,	guildBook.member[petMailSendSelectNo].address.graNo, 0 );
#endif

	// 名称表示
	len = GetStrWidth( guildBook.member[petMailSendSelectNo].address.name, FONT_KIND_MIDDLE );
	StockFontBuffer( G_mailPetEditMenuWin.x+176-len/2, G_mailPetEditMenuWin.y +62,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		guildBook.member[petMailSendSelectNo].address.name, 0, 0 );

	// センドアイテムウィンドウでアイテムが选择されているか？
	if( sendItemSelectNo > 0 )
	{
#ifdef PUK2
		StockDispBuffer( G_mailPetEditMenuWin.x+216, G_mailPetEditMenuWin.y+120,
			DISP_PRIO_MENU,	pc.item[sendItemSelectNo].graNo, 0, &bm );
#else
		StockDispBuffer( G_mailPetEditMenuWin.x+216, G_mailPetEditMenuWin.y+120,
			DISP_PRIO_MENU,	pc.item[sendItemSelectNo].graNo, 0 );
#endif

		// 个数表示
		if( pc.item[sendItemSelectNo].num > 0 )
		{
			sprintf( str, "%3d", pc.item[sendItemSelectNo].num );              //MLHIDE
			StockFontBuffer( G_mailPetEditMenuWin.x+213, G_mailPetEditMenuWin.y+127,
				FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );
		}

		len = GetStrWidth( pc.item[sendItemSelectNo].name, FONT_KIND_MIDDLE );
		StockFontBuffer( G_mailPetEditMenuWin.x+136-len/2, G_mailPetEditMenuWin.y+152,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
			pc.item[sendItemSelectNo].name, 0, 0 );
	}

	// SENDボタン表示
	drawGraBtnInfo1( &sendBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// CANCELボタン表示
	drawGraBtnInfo1( &cancelBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 入力中の文字列表示
	StockFontBuffer2( &mailPetEditStr );


	// １行インフォ表示
	if( checkFocusMenuClose( &G_mailPetEditMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// SENDボタン
	if( (pushGraBtnInfo1( &sendBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "选择している家族成员にメールを送ります。" );                   //MLHIDE
	}
	else
	// CANCELボタン
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "家族ブックウインドウに戾ります。" );                       //MLHIDE
	}


	// メニュー共通表示处理
	menuCommonDraw( &G_mailPetEditMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}




//-------------------------------------------------------------------------//
// メールペットセレクトウィンドウ                                          //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_mailPetSelectMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 272, 238, G_MENU_MAIL_PET_SELECT_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_PET_SELECT_WIN
	},
};


// ペットが特定の技を持っているか调べる
// 引　数: index  ... ペットの位置
//         techId ... 技ＩＤ
// 戾り值: 1 ... 持ってる
//         0 ... 持ってない
int G_checkPetTech( int index, int techId )
{
	int i;

	if( index < 0 || MAX_PET <= index )
		return 0;

	if( !pet[index].useFlag )
		return 0;

	for( i = 0; i < MAX_PET_TECH; i++ )
	{
		if( pet[index].tech[i].useFlag
		 && pet[index].tech[i].techId == techId )
		{
			return 1;
		}
	}

	return 0;
}


// ペットがペットメールの技を持っているか调べる
// 引　数: index ... ペットの位置
// 戾り值: 1 ... 持っている
//         0 ... 持ってない
int G_checkPetMail( int index )
{
	if( G_checkPetTech( index, PET_MAIL_TECH_ID ) )
	{
		return 1;
	}
	else
	if( G_checkPetTech( index, PET_MAIL2_TECH_ID ) )
	{
		return 1;
	}
	else
	if( G_checkPetTech( index, PET_MAIL3_TECH_ID ) )
	{
		return 1;
	}
	else
	if( G_checkPetTech( index, PET_MAIL4_TECH_ID ) )
	{
		return 1;
	}

	return 0;
}


void G_mailPetSelectMenu( int status )
{
	GRA_BTN_INFO1 cancelBtn =
	{
		G_mailPetSelectMenuWin.x+136, G_mailPetSelectMenuWin.y+206,
		G_mailPetSelectMenuWin.x +96, G_mailPetSelectMenuWin.y+196,
		80, 20,
		CG_MAIL_PET_SELECT_CANCEL_BTN_1, CG_MAIL_PET_SELECT_CANCEL_BTN_2
	};
	char str[128];
	int ret;
	int len;
	int color;
	int petMailFlag[MAX_PET];
	int selNo;
	int index;
	int i;

	// メニュー共通处理
	ret = menuCommonProc( status, &G_mailPetSelectMenuWin, NULL, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		return;
	}



	selNo = -1;
	for( i = 0; i < MAX_PET; i++ )
	{
		index = sortPet[i].index;
		// ペットメール技を持ってるかチェック
		petMailFlag[i] = G_checkPetMail( index );
		if( MakeHitBox(
			G_mailPetSelectMenuWin.x+12,
			G_mailPetSelectMenuWin.y+41+i*28,
			G_mailPetSelectMenuWin.x+12+249,
			G_mailPetSelectMenuWin.y+41+i*28+20, -1 )
		 && pet[index].useFlag
		 && petMailFlag[i] )
		{
			selNo = i;
		}
	}


	// ペットメール技を持ったペットを选んだか？
	if( selNo >= 0 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		petMailSendPetSelectNo = sortPet[selNo].index;
		// メールペットエディットウィンドウ开く
		menuOpen( G_MENU_MAIL_PET_EDIT_WIN );
		// センドアイテムセレクトウィンドウ开く
		menuOpen( G_MENU_SEND_ITEM_SELECT_WIN );
	}
	else
	// CANCELボタン押したか？
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_LEFT_CLICK) )
	{
		// アドレスブック开く
		menuOpen( G_MENU_ADDRESS_BOOK_WIN );
	}


	// カーソルがあってるボタンに枠を表示
	if( selNo >= 0 )
	{
		StockBoxDispBuffer(
			G_mailPetSelectMenuWin.x+12,
			G_mailPetSelectMenuWin.y+41+selNo*28,
			G_mailPetSelectMenuWin.x+12+249,
			G_mailPetSelectMenuWin.y+41+selNo*28+20,
			DISP_PRIO_MENU, BoxColor, 0 );
	}

	for( i = 0; i < MAX_PET; i++ )
	{
		index = sortPet[i].index;
		// ペットいるなら情报表示
		if( pet[index].useFlag )
		{
			// ペットメールの技持ってるペットは
			if( petMailFlag[i] )
			{
				// 白で表示
				color = FONT_PAL_WHITE;
			}
			else
			// その他は
			{
				// 灰色で表示
				color = FONT_PAL_GRAY;
			}

			// ペット名表示
			if( pet[index].freeName[0] == '\0' )
			{
				strcpy( str, pet[index].name );
			}
			else
			{
				strcpy( str, pet[index].freeName );
			}
			len = GetStrWidth( str, FONT_KIND_MIDDLE );
			StockFontBuffer( G_mailPetSelectMenuWin.x +90-len/2, G_mailPetSelectMenuWin.y +43+i*28,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, str, 0, 0 );

			// ペットの等级表示
			sprintf( str, "%3d", pet[index].lv );                              //MLHIDE
			StockFontBuffer( G_mailPetSelectMenuWin.x+226, G_mailPetSelectMenuWin.y +45+i*28,
				FONT_PRIO_FRONT, FONT_KIND_SMALL, color, str, 0, 0 );
		}
	}

	// CANCELボタン表示
	drawGraBtnInfo1( &cancelBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );


	// １行インフォ表示
	if( checkFocusMenuClose( &G_mailPetSelectMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// CANCELボタン
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, ML_STRING(240, "返回家族列表窗口。") );
	}
	// 各使い魔の名称
	else
	if( selNo >= 0 )
	{
		strcpy( OneLineInfoStr, ML_STRING(241, "让这个宠物送信。") );
	}


	// メニュー共通表示处理
	menuCommonDraw( &G_mailPetSelectMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}




//-------------------------------------------------------------------------//
// メールヒストリウィンドウ                                                //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_mailHistoryMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 272, 428, G_MENU_MAIL_HISTORY_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_HISTORY_WIN
	},
};


int mailGuildHistorySelectNo;
int mailGuildHistoryPage;


// メールヒストリーウィンドウ初期化
void G_initMailHistoryMene( void )
{
	int i;

	mailGuildHistoryPage = 0;

	// 选择したアドレスの未読番号チェック
	for( i = MAIL_HISTORY_CNT - 1; i >= 0; i-- )
	{
		if( mailGuildHistory[selectPcNo][mailGuildHistorySelectNo].mail.mailInfo[i].readFlag )
		{
			mailGuildHistoryPage = i;
			break;
		}
	}

}


void G_mailHistoryMenu( int status )
{
	GRA_BTN_INFO1 leftBtn =
	{
		G_mailHistoryMenuWin.x +26, G_mailHistoryMenuWin.y+410,
		G_mailHistoryMenuWin.x +12, G_mailHistoryMenuWin.y+400,
		28, 20,
		CG_MAIL_HISTORY_LEFT_BTN_1, CG_MAIL_HISTORY_LEFT_BTN_2
	};
	GRA_BTN_INFO1 rightBtn =
	{
		G_mailHistoryMenuWin.x+126, G_mailHistoryMenuWin.y+410,
		G_mailHistoryMenuWin.x+112, G_mailHistoryMenuWin.y+400,
		28, 20,
		CG_MAIL_HISTORY_RIGHT_BTN_1, CG_MAIL_HISTORY_RIGHT_BTN_2
	};
	GRA_BTN_INFO1 backBtn =
	{
		G_mailHistoryMenuWin.x+204, G_mailHistoryMenuWin.y+410,
		G_mailHistoryMenuWin.x+164, G_mailHistoryMenuWin.y+400,
		80, 20,
		CG_MAIL_HISTORY_BACK_1, CG_MAIL_HISTORY_BACK_2
	};
	GRA_BTN_INFO1 sendBtn =
	{
		G_mailHistoryMenuWin.x +65, G_mailHistoryMenuWin.y+136,
		G_mailHistoryMenuWin.x +37, G_mailHistoryMenuWin.y+126,
		56, 20,
		CG_MAIL_ADDRESS_BOOK_SEND_BTN_1, CG_MAIL_ADDRESS_BOOK_SEND_BTN_2
	};
	char str[128];
	int ret;
	int len;
	int color;
	int i;
	int petMailflag;
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif
	GUILD_MEMBER_INFO	*member;

	// メニュー共通处理
	ret = menuCommonProc( status, &G_mailHistoryMenuWin, G_initMailHistoryMene,
			MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		// ヒストリをファイルに保存
		writeGuildMailFile();
		// 未読チェック
		checkNoReadMail();
		return;
	}


	// 左ボタン押したか？
	if( (pushGraBtnInfo1( &leftBtn ) & BTN_LEFT_CLICK_REP)
	 || keyOnRepWithCtrl( VK_Z ) )
	{
		mailGuildHistoryPage--;
		if( mailGuildHistoryPage < 0 )
		{
			mailGuildHistoryPage = MAIL_HISTORY_CNT - 1;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// 右ボタン押したか？
	if( (pushGraBtnInfo1( &rightBtn ) & BTN_LEFT_CLICK_REP)
	 || keyOnRepWithCtrl( VK_X ) )
	{
		mailGuildHistoryPage++;
		if( mailGuildHistoryPage >= MAIL_HISTORY_CNT )
		{
			mailGuildHistoryPage = 0;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// BACKボタン押したか？
	if( (pushGraBtnInfo1( &backBtn ) & BTN_LEFT_CLICK) )
	{
		// アドレスブック开く
		menuOpen( G_MENU_ADDRESS_BOOK_WIN );
	}
	else
	// SENDボタン押したか？
	if( (pushGraBtnInfo1( &sendBtn ) & BTN_LEFT_CLICK) )
	{
		// メールスキルを持ったペットがいるかチェック
		petMailflag = 0;

		if( ProcNo != PROC_BATTLE )
		{


			for( i = 0; i < MAX_PET; i++ )
			{
				// ペットメール技を持ってるかチェック
				if( (petMailflag = G_checkPetMail( i )) )
				{
					break;
				}
			}

		}


		// いたらメール选择ウィンドウを开く
		if( petMailflag )
		{
			menuOpen( G_MENU_MAIL_SELECT_WIN );
		}
		else
		// いないなら直接メールエディットウィンドウを开く
		{
			menuOpen( G_MENU_MAIL_EDIT_WIN );
		}
	}


	// 开いた文章は既読にしておく
	mailGuildHistory[selectPcNo][mailGuildHistorySelectNo].mail.mailInfo[mailGuildHistoryPage].readFlag = 0;

	member = &guildBook.member[mailGuildHistorySelectNo];

	// 颜画像表示
#ifdef PUK2
	StockDispBuffer( G_mailHistoryMenuWin.x +43, G_mailHistoryMenuWin.y +73,
		DISP_PRIO_MENU,	getNewFaceGraphicNo(member->address.graNo), 0, &bm );
////		DISP_PRIO_MENU,	getNewFaceGraphicNo(guildBook.member[mailHistorySelectNo].address.graNo), 0, &bm );
#else
	StockDispBuffer( G_mailHistoryMenuWin.x +43, G_mailHistoryMenuWin.y +73,
		DISP_PRIO_MENU,	getNewFaceGraphicNo(member->address.graNo), 0 );
////		DISP_PRIO_MENU,	getNewFaceGraphicNo(guildBook.member[mailHistorySelectNo].address.graNo), 0 );
#endif

	// 等级表示
	sprintf( str, "%3d", member->address.lv );                           //MLHIDE
	StockFontBuffer( G_mailHistoryMenuWin.x+134, G_mailHistoryMenuWin.y +49,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );

	// 名称表示
	len = GetStrWidth( member->address.name, FONT_KIND_MIDDLE );
	StockFontBuffer( G_mailHistoryMenuWin.x+162-len/2, G_mailHistoryMenuWin.y +70,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		member->address.name, 0, 0 );

	// 个人称号表示
	len = GetStrWidth( guildBook.title[member->titleId].name, FONT_KIND_MIDDLE );
	StockFontBuffer( G_mailHistoryMenuWin.x+162-len/2, G_mailHistoryMenuWin.y +93,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		guildBook.title[member->titleId].name, 0, 0 );

	// 送受信日时
	StockFontBuffer( G_mailHistoryMenuWin.x+128, G_mailHistoryMenuWin.y+128,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
		mailGuildHistory[selectPcNo][mailGuildHistorySelectNo].mail.mailInfo[mailGuildHistoryPage].header, 0, 0 );

	// 文章の表示
	if( mailGuildHistory[selectPcNo][mailGuildHistorySelectNo].mail.mailInfo[mailGuildHistoryPage].sendFlag == 0 ){
		color = FONT_PAL_YELLOW;
	}else{
		color = FONT_PAL_WHITE;
	}
	for( i = 0; i < MAIL_HISTORY_CNT; i++ ){
		getMemoLine( str, sizeof( str ),
			mailGuildHistory[selectPcNo][mailGuildHistorySelectNo].mail.mailInfo[mailGuildHistoryPage].buf, i, 28 );
		StockFontBuffer( G_mailHistoryMenuWin.x +10, G_mailHistoryMenuWin.y+169+i*20,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color, str, 0, 0 );
	}

	// ページ数表示
	sprintf( str, "%2d/%2d", mailGuildHistoryPage+1, MAIL_HISTORY_CNT ); //MLHIDE
	StockFontBuffer( G_mailHistoryMenuWin.x +56, G_mailHistoryMenuWin.y+403,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );

	// 左ボタン表示
	drawGraBtnInfo1( &leftBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 右ボタン表示
	drawGraBtnInfo1( &rightBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// BACKボタン表示
	drawGraBtnInfo1( &backBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// SENDボタン表示
	drawGraBtnInfo1( &sendBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );


	// １行インフォ表示
	if( checkFocusMenuClose( &G_mailHistoryMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// 左右ボタン
	if( (pushGraBtnInfo1( &leftBtn ) & BTN_FOCUS_ON)
	 || (pushGraBtnInfo1( &rightBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "ページを切り替えます。" );                            //MLHIDE
	}
	else
	// SENDボタン
	if( (pushGraBtnInfo1( &sendBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "この家族成员にメールを送ります。" );                       //MLHIDE
	}
	else
	// BACKボタン
	if( (pushGraBtnInfo1( &backBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "家族ブックウインドウに戾ります。" );                       //MLHIDE
	}


	// メニュー共通表示处理
	menuCommonDraw( &G_mailHistoryMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}




//-------------------------------------------------------------------------//
// メールエディットウィンドウ                                              //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_mailEditMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 272, 438, G_MENU_MAIL_EDIT_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_EDIT_WIN
	},
};


static	INPUT_STR mailEditStr;
static	int mailEditAddressBookPage;
static	int mailSendSelectNo;
static	char mailSendSelectFlag[GUILD_MEMBER_MAX];


// 初期化
void G_initMailEditMenu( void )
{
	char str[ 400 ];

	//mailEditStr.buffer[0] = '\0';
	//mailEditStr.cnt = 0;
	//mailEditStr.cursorByte = 0;

	// 一时的にコピーする
	strcpy( str, mailEditStr.buffer );

	InitInputStr( &mailEditStr, G_mailEditMenuWin.x +10, G_mailEditMenuWin.y+219,
		//FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, NULL, 10, 28, 20, 0 );
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 10, 28, 20, 0 );

	GetKeyInputFocus( &mailEditStr );

	memset( &mailSendSelectFlag, 0, sizeof( mailSendSelectFlag ) );
	mailSendSelectFlag[mailSendSelectNo] = 1;
}


void G_mailEditMenu( int status )
{
	GRA_BTN_INFO1 sendBtn =
	{
		G_mailEditMenuWin.x +80, G_mailEditMenuWin.y+197,
		G_mailEditMenuWin.x +40, G_mailEditMenuWin.y+187,
		80, 20,
		CG_MAIL_EDIT_SEND_BTN_1, CG_MAIL_EDIT_SEND_BTN_2
	};
	GRA_BTN_INFO1 cancelBtn =
	{
		G_mailEditMenuWin.x+192, G_mailEditMenuWin.y+197,
		G_mailEditMenuWin.x+152, G_mailEditMenuWin.y+187,
		80, 20,
		CG_COMMON_SMALL_CANCEL_BTN_1, CG_COMMON_SMALL_CANCEL_BTN_2
	};
	GRA_BTN_INFO1 leftBtn =
	{
		G_mailEditMenuWin.x +89, G_mailEditMenuWin.y+159,
		G_mailEditMenuWin.x +75, G_mailEditMenuWin.y+149,
		28, 20,
		CG_MAIL_EDIT_LEFT_BTN_1, CG_MAIL_EDIT_LEFT_BTN_2
	};
	GRA_BTN_INFO1 rightBtn =
	{
		G_mailEditMenuWin.x+189, G_mailEditMenuWin.y+159,
		G_mailEditMenuWin.x+175, G_mailEditMenuWin.y+149,
		28, 20,
		CG_MAIL_EDIT_RIGHT_BTN_1, CG_MAIL_EDIT_RIGHT_BTN_2
	};
	char str[128];
	int no;
	int ret;
	int len;
	int color;
	int selNo;
	int i;
	int sendCnt;

	// メニュー共通处理
	ret = menuCommonProc( status, &G_mailEditMenuWin, G_initMailEditMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		// キー入力のフォーカスを元に戾す
		GetKeyInputFocus( &MyChatBuffer );
		return;
	}

	// 选择した送信相手の数を数える
	for( i = 0, sendCnt = 0; i < GUILD_MEMBER_MAX; i++ )
	{
		if( mailSendSelectFlag[i] )
		{
			sendCnt++;
		}
	}


	// マウスカーソル位置チェック
	selNo = -1;
	for( i = 0; i < GUILD_BOOK_LINE; i++ )
	{
		if( MakeHitBox(
			G_mailEditMenuWin.x+64,
			G_mailEditMenuWin.y+38+i*27,
			G_mailEditMenuWin.x+64+149,
			G_mailEditMenuWin.y+38+i*27+20, -1 ) )
		{
			selNo = i;
			break;
		}
	}


	// 送信相手が选择されたか？
	if( selNo >= 0 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		no = mailEditAddressBookPage * GUILD_BOOK_LINE + selNo;
		if( guildBook.member[guildBookSortTbl[no].index].address.useFlag )
		{
			mailSendSelectFlag[guildBookSortTbl[no].index] ^= 1;
			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
	}
	else
	// SENDボタン押したか？
	if( (pushGraBtnInfo1( &sendBtn ) & BTN_LEFT_CLICK) )
	{
		if( mailEditStr.buffer[0] != '\0'
		 && sendCnt > 0 )
		{
			char str[512];
			char strEscape[600]; // エスケープの事を考えてメールのバッファの２倍以上にする。
			char header[64];
			time_t t;
			struct tm *tt;

			time( &t );
			tt = localtime( &t );
			sprintf( header, "%4d/%2d/%2d %02d:%02d",                          //MLHIDE
				1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );
			strcpy( str, mailEditStr.buffer );
			// ここは信息をエスケープする。
			makeSendString( str, strEscape, sizeof( strEscape ) );
			for( i = 0; i < GUILD_MEMBER_MAX; i++ )
			{
				if( mailSendSelectFlag[guildBookSortTbl[i].index] )
				{
					nrproto_GML_send( sockfd,
						guildBookSortTbl[i].index, strEscape, FONT_PAL_WHITE );
					setGuildMailHistory( guildBookSortTbl[i].index, 1, header, mailEditStr.buffer );
				}
			}
			// メールヒストリー保存
			writeGuildMailFile();
			// 决定音b（ボタンクリック时）
			play_se( SE_NO_OK2, 320, 240 );
			// アドレスブックウィンドウ开く
			menuOpen( G_MENU_ADDRESS_BOOK_WIN );
			// 送り終わったときだけ初期化する
			mailEditStr.buffer[0] = '\0';

		}
		else
		{
			// 空のメールをおくれないようにする
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}
	else
	// CANCELボタン押したか？
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_LEFT_CLICK) )
	{
		// アドレスブックウィンドウ开く
		menuOpen( G_MENU_ADDRESS_BOOK_WIN );
	}
	else
	// 左ボタン押したか？
	if( (pushGraBtnInfo1( &leftBtn ) & BTN_LEFT_CLICK_REP)
	 || keyOnRepWithCtrl( VK_Z ) )
	{
		mailEditAddressBookPage--;
		if( mailEditAddressBookPage < 0 )
		{
			mailEditAddressBookPage = GUILD_BOOK_PAGE - 1;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// 右ボタン押したか？
	if( (pushGraBtnInfo1( &rightBtn ) & BTN_LEFT_CLICK_REP)
	 || keyOnRepWithCtrl( VK_X ) )
	{
		mailEditAddressBookPage++;
		if( mailEditAddressBookPage >= GUILD_BOOK_PAGE )
		{
			mailEditAddressBookPage = 0;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}


	// カーソルがあってるボタンに枠を表示
	if( selNo >= 0 )
	{
		StockBoxDispBuffer(
			G_mailEditMenuWin.x+64,
			G_mailEditMenuWin.y+38+selNo*27,
			G_mailEditMenuWin.x+60+149,
			G_mailEditMenuWin.y+38+selNo*27+20,
			DISP_PRIO_MENU, BoxColor, 0 );
	}


	// 名称の表示
	for( i = 0; i < GUILD_BOOK_LINE; i++ )
	{
		no = mailEditAddressBookPage * GUILD_BOOK_LINE + i;
		if( guildBook.member[guildBookSortTbl[no].index].address.useFlag )
		{
			if( mailSendSelectFlag[guildBookSortTbl[no].index] )
			{
				color = FONT_PAL_AQUA;
			}
			else
			if( guildBook.member[guildBookSortTbl[no].index].address.onlineFlag == 0 )
			{
				color = FONT_PAL_GRAY;
			}
			else
			{
				color = FONT_PAL_WHITE;
			}
			len = GetStrWidth( guildBook.member[guildBookSortTbl[no].index].address.name,
				FONT_KIND_MIDDLE );
			StockFontBuffer( G_mailEditMenuWin.x+136-len/2, G_mailEditMenuWin.y +40+i*27,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, color,
				guildBook.member[guildBookSortTbl[no].index].address.name, 0, 0 );
		}
	}

	// ページ数表示
	sprintf( str, "%2d/%2d", mailEditAddressBookPage+1, GUILD_BOOK_PAGE ); //MLHIDE
	StockFontBuffer( G_mailEditMenuWin.x+119, G_mailEditMenuWin.y+152,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );

	// Sendボタン表示
	drawGraBtnInfo1( &sendBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// Cancelボタン表示
	drawGraBtnInfo1( &cancelBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 左ボタン表示
	drawGraBtnInfo1( &leftBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 右ボタン表示
	drawGraBtnInfo1( &rightBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 入力中の文字列表示
	StockFontBuffer2( &mailEditStr );


	// １行インフォ表示
	if( checkFocusMenuClose( &G_mailEditMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// 左右ボタン
	if( (pushGraBtnInfo1( &leftBtn ) & BTN_FOCUS_ON)
	 || (pushGraBtnInfo1( &rightBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "ページを切り替えます。" );                            //MLHIDE
	}
	else
	// SENDボタン
	if( (pushGraBtnInfo1( &sendBtn ) & BTN_FOCUS_ON) )
	{
		if( sendCnt )
		{
			strcpy( OneLineInfoStr, "选择している家族成员にメールを送ります。" );                  //MLHIDE
		}
		else
		{
			strcpy( OneLineInfoStr, "送信相手を选择してください。" );                        //MLHIDE
		}
	}
	else
	// CANCELボタン
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "家族ブックウインドウに戾ります。" );                       //MLHIDE
	}
	else
	// 送信相手
	if( selNo >= 0 )
	{
		no = mailEditAddressBookPage * GUILD_BOOK_LINE + selNo;
		if( guildBook.member[guildBookSortTbl[no].index].address.useFlag
		 &&	mailSendSelectFlag[guildBookSortTbl[no].index] )
		{
			strcpy( OneLineInfoStr, ML_STRING(242, "取消给这个家族成员送信。") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(243, "给这个家族成员送信。") );
		}
	}


	// メニュー共通表示处理
	menuCommonDraw( &G_mailEditMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}




//-------------------------------------------------------------------------//
// メールセレクトウィンドウ                                                //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_mailSelectMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 192, 174, G_MENU_MAIL_SELECT_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_SELECT_WIN
	},
};


void G_mailSelectMenu( int status )
{
	GRA_BTN_INFO1 normaMailBtn =
	{
		G_mailSelectMenuWin.x +95, G_mailSelectMenuWin.y +55,
		G_mailSelectMenuWin.x +25, G_mailSelectMenuWin.y +43,
		140, 24,
		CG_MAIL_SELECT_NORMAL_MAIL_BTN_1, CG_MAIL_SELECT_NORMAL_MAIL_BTN_2
	};
	GRA_BTN_INFO1 monsterMailBtn =
	{
		G_mailSelectMenuWin.x +95, G_mailSelectMenuWin.y +95,
		G_mailSelectMenuWin.x +25, G_mailSelectMenuWin.y +83,
		140, 24,
		CG_MAIL_SELECT_MONSTER_MAIL_BTN_1, CG_MAIL_SELECT_MONSTER_MAIL_BTN_2
	};
	// キャンセルボタン
	GRA_BTN_INFO1 cancelBtn =
	{
		G_mailSelectMenuWin.x +96, G_mailSelectMenuWin.y+137,
		G_mailSelectMenuWin.x +56, G_mailSelectMenuWin.y+127,
		80, 20,
		CG_COMMON_SMALL_CANCEL_BTN_1, CG_COMMON_SMALL_CANCEL_BTN_2
	};
	int ret;


	// メニュー共通处理
	ret = menuCommonProc( status, &G_mailSelectMenuWin, NULL, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		return;
	}

	// 「通常メール」ボタン押したか？
	if( (pushGraBtnInfo1( &normaMailBtn ) & BTN_LEFT_CLICK) )
	{
		// メールエディットウィンドウを开く
		menuOpen( G_MENU_MAIL_EDIT_WIN );
	}
	else
	// 「モンスターメール」ボタン押したか？
	if( (pushGraBtnInfo1( &monsterMailBtn ) & BTN_LEFT_CLICK) )
	{
		// メールペットセレクトウィンドウを开く
		menuOpen( G_MENU_MAIL_PET_SELECT_WIN );
	}
	else
	// 「キャンセル」ボタン押したか？
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_LEFT_CLICK) )
	{
		menuOpen( G_MENU_ADDRESS_BOOK_WIN );
	}

	// 「通常メール」ボタン表示
	drawGraBtnInfo1( &normaMailBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 「モンスターメール」ボタン表示
	drawGraBtnInfo1( &monsterMailBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 「キャンセル」ボタン表示
	drawGraBtnInfo1( &cancelBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );


	// １行インフォ表示
	if( checkFocusMenuClose( &G_mailSelectMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// NORMAL MAIL ボタン
	if( (pushGraBtnInfo1( &normaMailBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "通常メールで送信します。" );                           //MLHIDE
	}
	else
	// MONSTER MAIL ボタン
	if( (pushGraBtnInfo1( &monsterMailBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "モンスターメールで送信します。" );                        //MLHIDE
	}
	else
	// CANCELボタン
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "家族ブックウインドウに戾ります。" );                       //MLHIDE
	}


	// メニュー共通表示处理
	menuCommonDraw( &G_mailSelectMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}



//-------------------------------------------------------------------------//
// アドレス削除ウィンドウ                                                  //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_addressBookDelMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 320, 240, G_MENU_ADDRESS_BOOK_DEL_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		0
	},
};

static	int addressBookDelNo;

void G_addressBookDelMenu( int status )
{
	STR_BTN_INFO yesBtnInfo =
	{
		G_addressBookDelMenuWin.x +89, G_addressBookDelMenuWin.y+182,
		G_addressBookDelMenuWin.x +79, G_addressBookDelMenuWin.y+180,
		55, 20, "はい"                                                        //MLHIDE
	};
	STR_BTN_INFO noBtnInfo =
	{
		G_addressBookDelMenuWin.x+187, G_addressBookDelMenuWin.y+182,
		G_addressBookDelMenuWin.x+185, G_addressBookDelMenuWin.y+180,
		55, 20, "いいえ"                                                       //MLHIDE
	};
	int yesBtnBit, noBtnBit;
	int len;
	int ret;

	// メニュー共通处理
	ret = menuCommonProc( status, &G_addressBookDelMenuWin, NULL, 0 );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		return;
	}
	// マウスのボタンチェック
	yesBtnBit = pushStrBtnInfo( &yesBtnInfo );
	noBtnBit  = pushStrBtnInfo( &noBtnInfo );
	if( (yesBtnBit & BTN_LEFT_CLICK) )
	{
		if(guildBook.title[guildBook.pcGuildTitleId].flag & GUILD_FLAG_DISMISS){
			// アドレス削除プロトコル送信
			nrproto_RGM_send( sockfd, addressBookDelNo );
			// 一人分のヒストリを削除
			delGuildMailHistoryOnce( addressBookDelNo );
			// ヒストリの保存
			writeMailFile();
			// このウィンドウを关闭
			menuClose( G_addressBookDelMenuWin.menuNo );
			// 决定音b（ボタンクリック时）
			play_se( SE_NO_OK2, 320, 240 );
		}
		else{
			StockChatBufferLine( "除名许可がありません！", FONT_PAL_YELLOW, FONT_KIND_MIDDLE ); //MLHIDE
			play_se( SE_NO_NG, 320, 240 );
		}
	}
	else
	if( (noBtnBit & BTN_LEFT_CLICK) )
	{
		menuOpen( G_MENU_ADDRESS_BOOK_WIN );
	}


	StockFontBuffer( G_addressBookDelMenuWin.x +84, G_addressBookDelMenuWin.y +38,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		"家族ブックから", 0, 0 );                                                  //MLHIDE

	len = GetStrWidth( guildBook.member[addressBookDelNo].address.name, FONT_KIND_MIDDLE );
	StockFontBuffer( G_addressBookDelMenuWin.x +160 -len/2, G_addressBookDelMenuWin.y +86,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		guildBook.member[addressBookDelNo].address.name, 0, 0 );

	StockFontBuffer( G_addressBookDelMenuWin.x+109, G_addressBookDelMenuWin.y +134,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		"を消除", 0, 0 );                                                      //MLHIDE

	// ボタン文字列表示
	drawStrBtnInfo( &yesBtnInfo, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
		1, BoxColor, DISP_PRIO_BOX2 );

	// ボタン文字列表示
	drawStrBtnInfo( &noBtnInfo, FONT_PRIO_FRONT, FONT_PAL_YELLOW,
		1, BoxColor, DISP_PRIO_BOX2 );


	// １行インフォ表示
	// 「はい」ボタン
	if( (yesBtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "消除。" );                                    //MLHIDE
	}
	else
	// 「いいえ」ボタン
	if( (noBtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "削除を取りやめます。" );                             //MLHIDE
	}


	// メニュー共通表示处理
	menuCommonDraw( &G_addressBookDelMenuWin, 0 );
}




//-------------------------------------------------------------------------//
// メールアドレスブックソート设定ウィンドウ                                //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_addressBookSortMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 192, 254, G_MENU_ADDRESS_BOOK_SORT_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_ADDRESS_BOOK_SORT_WIN
	},
};


void G_addressBookSortMenu( int status )
{
	GRA_BTN_INFO1 okBtn =
	{
		G_addressBookSortMenuWin.x +96, G_addressBookSortMenuWin.y+220,
		G_addressBookSortMenuWin.x +56, G_addressBookSortMenuWin.y+210,
		80, 20,
		CG_COMMON_SMALL_OK_BTN_1, CG_COMMON_SMALL_OK_BTN_2
	};
	int selNo;
	int ret;
	int i;

	// メニュー共通处理
	ret = menuCommonProc( status, &G_addressBookSortMenuWin, NULL, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		return;
	}

	// マウスカーソル位置チェック
	selNo = -1;
	for( i = 0; i < 3; i++ )
	{
		if( MakeHitBox(
			G_addressBookSortMenuWin.x+19,
			G_addressBookSortMenuWin.y+38+i*30,
			G_addressBookSortMenuWin.x+19+154,
			G_addressBookSortMenuWin.y+38+i*30+26, -1 ) )
		{
			selNo = i;
			break;
		}
	}

	// ボタンが选择されか？
	if( selNo >= 0 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		addressBookSortMode = selNo;
		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}
	else
	// ＯＫボタン押したか？
	if( (pushGraBtnInfo1( &okBtn ) & BTN_LEFT_CLICK) )
	{
		// 设定を保存
		setUserMailSetting();
		saveNowState();

		menuOpen( G_MENU_ADDRESS_BOOK_WIN );

		// 决定音b（ボタンクリック时）
		play_se( SE_NO_OK2, 320, 240 );
	}

	// カーソルがあってるボタンに枠を表示
	if( selNo >= 0 )
	{
		StockBoxDispBuffer(
			G_addressBookSortMenuWin.x+19,
			G_addressBookSortMenuWin.y+38+selNo*30,
			G_addressBookSortMenuWin.x+19+154,
			G_addressBookSortMenuWin.y+38+selNo*30+26,
			DISP_PRIO_MENU, BoxColor, 0 );
	}

	StockBoxDispBuffer(
		G_addressBookSortMenuWin.x+19,
		G_addressBookSortMenuWin.y+38+addressBookSortMode*30,
		G_addressBookSortMenuWin.x+19+154,
		G_addressBookSortMenuWin.y+38+addressBookSortMode*30+26,
		DISP_PRIO_MENU, SYSTEM_PAL_AQUA, 0 );

	// ボタン文字列
	StockFontBuffer( G_addressBookSortMenuWin.x +71, G_addressBookSortMenuWin.y +43,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, "登録顺", 0, 0 );   //MLHIDE

	// ボタン文字列
	StockFontBuffer( G_addressBookSortMenuWin.x +62, G_addressBookSortMenuWin.y +73,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, "５０音顺", 0, 0 );  //MLHIDE

	// ボタン文字列
	StockFontBuffer( G_addressBookSortMenuWin.x +45, G_addressBookSortMenuWin.y+103,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, "ONライン顺", 0, 0 ); //MLHIDE

	// ＯＫボタン表示
	drawGraBtnInfo1( &okBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// メニュー共通表示处理
	menuCommonDraw( &G_addressBookSortMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}




//-------------------------------------------------------------------------//
// メールアドレスブック                                                    //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_addressBookMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 272, 451, G_MENU_ADDRESS_BOOK_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MAIL_GUILD_BOOK_WIN_P2
	},
};

int		guildTitleIdx;

static	int addressBookPage;
static	int addressBookNewPage;
static	int addressBookSortMode = 0;	// ソート方法
static	int connectServerNameGraNoTbl[] =
{
	CG_MAIL_ADDRESS_BOOK_OFFLINE_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_1_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_2_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_3_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_4_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_5_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_6_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_7_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_8_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_9_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_10_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_11_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_12_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_13_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_14_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_15_LAMP,
	CG_MAIL_ADDRESS_BOOK_SERVER_16_LAMP
};
static	ACTION *ptActMail[GUILD_BOOK_LINE];

// SPR表示アクション生成
ACTION *G_createActSpr( int graNo, int x, int y, int prio )
{
	ACTION *ptAct;

	// アクション取得
	ptAct = GetAction( PRIO_CHR, 0 );
	if( ptAct == NULL )
		return NULL;	// 取得失败

	// 实行关数
	ptAct->func = createActSprProc;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = ANIM_STAND;
	// アニメーション向き
	ptAct->anim_ang = 0;	// 下向きにする
	// 表示优先度
	ptAct->dispPrio = prio;
	// 1行インフォ表示フラグ
	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIT;

	// 表示座标设定
	ptAct->x = x;
	ptAct->y = y;

	return ptAct;
}


// SPR表示アクション处理
void G_createActSprProc( ACTION *ptAct )
{
	int animLoop;

	// アニメーション处理
	if( ptAct->anim_no == ANIM_ATTACK
	 || ptAct->anim_no == ANIM_HAND
	 || ptAct->anim_no == ANIM_HAPPY
	 || ptAct->anim_no == ANIM_ANGRY
	 || ptAct->anim_no == ANIM_SAD
	 || ptAct->anim_no == ANIM_WALK
	 || ptAct->anim_no == ANIM_STAND
	 || ptAct->anim_no == ANIM_NOD )
	{
		animLoop = ANM_LOOP;
	}
	else
	{
		animLoop = ANM_NO_LOOP;
	}
	pattern( ptAct, ANM_NOMAL_SPD, animLoop );
}



// アドレスブックソート时の比较关数
int G_cmpAddressBook( const void *_pt1, const void *_pt2 )
{
	ADDRESS_BOOK_SORT_TBL *pt1 = (ADDRESS_BOOK_SORT_TBL *)_pt1;
	ADDRESS_BOOK_SORT_TBL *pt2 = (ADDRESS_BOOK_SORT_TBL *)_pt2;
	int flag;

	// pt2にデータがないなら何もしない
	if( guildBook.member[pt2->index].address.useFlag == 0 )
	{
		return -1;
	}
	else
	// pt2にデータがあり、pt1にデータがないなら无条件で入れ替え
	if( guildBook.member[pt1->index].address.useFlag == 0 )
	{
		return 1;
	}

	// 登録顺にソート
	if( addressBookSortMode == 0 )
	{
		// pt1の登録がpt2より后なら入れ替え
		if( guildBook.member[pt1->index].joinDate > guildBook.member[pt2->index].joinDate )
		{
			return 1;
		}
	}
	else
	// ５０音顺にソート
	if( addressBookSortMode == 1 )
	{
		flag = strcmp( guildBook.member[pt1->index].address.name, guildBook.member[pt2->index].address.name );
		// pt1がtp2より大きいなら入れ替え
		if( flag > 0 )
		{
			return 1;
		}
		else
		// 名称がいっしょならpt1の登録がpt2より后なら入れ替え
		if( flag == 0 && guildBook.member[pt1->index].address.id > guildBook.member[pt2->index].address.id )
		{
			return 1;
		}
	}
	else
	// ONライン顺にソート（登録顺でもソート）
	{
		// pt1がOFFラインで、pt2がONラインなら入れ替え
		if( guildBook.member[pt1->index].address.onlineFlag == 0 && guildBook.member[pt2->index].address.onlineFlag > 0 )
		{
			return 1;
		}
		else
		// pt1がONラインで、pt2がOFFラインなら入れ替えなし
		if( guildBook.member[pt1->index].address.onlineFlag > 0 && guildBook.member[pt2->index].address.onlineFlag == 0 )
		{
			return -1;
		}
		else
		// 两方ONラインなら登録顺をみて入れ替え
		if( guildBook.member[pt1->index].address.onlineFlag > 0 && guildBook.member[pt2->index].address.onlineFlag > 0
		 && guildBook.member[pt1->index].address.id > guildBook.member[pt2->index].address.id )
		{
			return 1;
		}
		else
		// 两方OFFラインなら登録顺をみて入れ替え
		if( guildBook.member[pt1->index].address.onlineFlag == 0 && guildBook.member[pt2->index].address.onlineFlag == 0
		 && guildBook.member[pt1->index].address.id > guildBook.member[pt2->index].address.id )
		{
			return 1;
		}
	}

	return -1;
}


// アドレスブックのソート
void G_sortAddressBook( void )
{
	int i;

	for( i = 0; i < GUILD_MEMBER_MAX; i++ )
	{
		guildBookSortTbl[i].index = i;
	}

	//クイックソート
	qsort(	guildBookSortTbl,					// 构造体のアドレス
			GUILD_MEMBER_MAX,						// 比较する个数
			sizeof( ADDRESS_BOOK_SORT_TBL ), 	// 构造体のサイズ
			G_cmpAddressBook						// 比较关数へのポインタ
	);
}


// 初期化
void G_initAddressBookMenu( void )
{
	int i;

	G_sortAddressBook();
	checkNoReadMail();

	addressBookPage = addressBookNewPage;

	for( i = 0; i < GUILD_BOOK_LINE; i++ )
	{
		if( ptActMail[i] == NULL )
		{
			ptActMail[i] =
				G_createActSpr( SPR_mail,
				G_addressBookMenuWin.x+72, G_addressBookMenuWin.y+38+i*90, DISP_PRIO_BOX3 );
			if( ptActMail[i] )
			{
				ptActMail[i]->atr |= ACT_ATR_HIDE;
			}
		}
	}
}


// 开いて处理
void G_addressBookMenu( int status )
{
	GRA_BTN_INFO1 sendBtn[] =
	{
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+45,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+38,
			53, 15,
			CG_MAIL_GUILD_BOOK_SEND_BTN_1, CG_MAIL_GUILD_BOOK_SEND_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+45+90,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+38+90,
			53, 15,
			CG_MAIL_GUILD_BOOK_SEND_BTN_1, CG_MAIL_GUILD_BOOK_SEND_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+45+180,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+38+180,
			53, 15,
			CG_MAIL_GUILD_BOOK_SEND_BTN_1, CG_MAIL_GUILD_BOOK_SEND_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+45+270,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+38+270,
			53, 15,
			CG_MAIL_GUILD_BOOK_SEND_BTN_1, CG_MAIL_GUILD_BOOK_SEND_BTN_2
		}
	};
	GRA_BTN_INFO1 historyBtn[] =
	{
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+65,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+58,
			53, 15,
			CG_MAIL_GUILD_BOOK_HISTORY_BTN_1, CG_MAIL_GUILD_BOOK_HISTORY_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+65+90,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+58+90,
			53, 15,
			CG_MAIL_GUILD_BOOK_HISTORY_BTN_1, CG_MAIL_GUILD_BOOK_HISTORY_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+65+180,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+58+180,
			53, 15,
			CG_MAIL_GUILD_BOOK_HISTORY_BTN_1, CG_MAIL_GUILD_BOOK_HISTORY_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+65+270,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y+58+270,
			53, 15,
			CG_MAIL_GUILD_BOOK_HISTORY_BTN_1, CG_MAIL_GUILD_BOOK_HISTORY_BTN_2
		}
	};
	GRA_BTN_INFO1 delBtn[] =
	{
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+106,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +99,
			53, 15,
			CG_MAIL_GUILD_BOOK_DEL_BTN_1, CG_MAIL_GUILD_BOOK_DEL_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+106+90,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +99+90,
			53, 15,
			CG_MAIL_GUILD_BOOK_DEL_BTN_1, CG_MAIL_GUILD_BOOK_DEL_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+106+180,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +99+180,
			53, 15,
			CG_MAIL_GUILD_BOOK_DEL_BTN_1, CG_MAIL_GUILD_BOOK_DEL_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y+106+270,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +99+270,
			53, 15,
			CG_MAIL_GUILD_BOOK_DEL_BTN_1, CG_MAIL_GUILD_BOOK_DEL_BTN_2
		}
	};
	GRA_BTN_INFO1 gutitleBtn[] =
	{
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y +86,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +79,
			53, 15,
			CG_MAIL_GUILD_BOOK_GUTITLE_BTN_1, CG_MAIL_GUILD_BOOK_GUTITLE_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y +86+90,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +79+90,
			53, 15,
			CG_MAIL_GUILD_BOOK_GUTITLE_BTN_1, CG_MAIL_GUILD_BOOK_GUTITLE_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y +86+180,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +79+180,
			53, 15,
			CG_MAIL_GUILD_BOOK_GUTITLE_BTN_1, CG_MAIL_GUILD_BOOK_GUTITLE_BTN_2
		},
		{
			G_addressBookMenuWin.x+236, G_addressBookMenuWin.y +86+270,
			G_addressBookMenuWin.x+210, G_addressBookMenuWin.y +79+270,
			53, 15,
			CG_MAIL_GUILD_BOOK_GUTITLE_BTN_1, CG_MAIL_GUILD_BOOK_GUTITLE_BTN_2
		}
	};
	GRA_BTN_INFO1 sortBtn =
	{
		G_addressBookMenuWin.x +40, G_addressBookMenuWin.y+406,
		G_addressBookMenuWin.x +10, G_addressBookMenuWin.y+396,
		60, 20,
		CG_MAIL_GUILD_BOOK_SORT_BTN_1, CG_MAIL_GUILD_BOOK_SORT_BTN_2
	};
	GRA_BTN_INFO1 leftBtn =
	{
		G_addressBookMenuWin.x+157, G_addressBookMenuWin.y+406,
		G_addressBookMenuWin.x+144, G_addressBookMenuWin.y+396,
		26, 20,
		CG_MAIL_GUILD_BOOK_LEFT_1, CG_MAIL_GUILD_BOOK_LEFT_2
	};
	GRA_BTN_INFO1 rightBtn =
	{
		G_addressBookMenuWin.x+248, G_addressBookMenuWin.y+406,
		G_addressBookMenuWin.x+235, G_addressBookMenuWin.y+396,
		26, 20,
		CG_MAIL_GUILD_BOOK_RIGHT_1, CG_MAIL_GUILD_BOOK_RIGHT_2
	};
	GRA_BTN_INFO1 g_detBtn =
	{
		G_addressBookMenuWin.x+107, G_addressBookMenuWin.y+406,
		G_addressBookMenuWin.x+77, G_addressBookMenuWin.y+396,
		60, 20,
		CG_MAIL_GUILD_BOOK_DETAIL_1, CG_MAIL_GUILD_BOOK_DETAIL_2
	};

#ifdef PUK2		//####toda
	GRA_BTN_INFO1 adr_go_Btn =
	{
		G_addressBookMenuWin.x +135,G_addressBookMenuWin.y+436,		//center
		G_addressBookMenuWin.x +90, G_addressBookMenuWin.y+422,		//start
		90, 28,
		0, 0
	};
#endif

	char str[128];
	int ret;
	int leftBtnBit, rightBtnBit;
	int sortBtnBit;
	int	g_detBtnBit;

#ifdef PUK2		//####toda
	int adr_go_BtnBit;
#endif

	int no;
	int color;
	int petMailflag;
	int i, j;
	int btnFocusNo;
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif

	// メニュー共通处理
	ret = menuCommonProc( status, &G_addressBookMenuWin, G_initAddressBookMenu,
			MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		for( i = 0; i < GUILD_BOOK_LINE; i++ )
		{
			if( ptActMail[i] )
			{
				DeathAction( ptActMail[i] );
				ptActMail[i] = NULL;
			}
		}
		return;
	}

	for( i = 0; i < GUILD_BOOK_LINE; i++ )
	{
		if( ptActMail[i] )
		{
			no = addressBookPage * GUILD_BOOK_LINE + i;
			if( checkNoReadMailOnce( guildBookSortTbl[no].index ) )
			{
				ptActMail[i]->atr &= (~ACT_ATR_HIDE);
			}
			else
			{
				ptActMail[i]->atr |= ACT_ATR_HIDE;
			}
		}
	}

	btnFocusNo = -1;
	for( i = 0; i < GUILD_BOOK_LINE; i++ )
	{
		no = addressBookPage * GUILD_BOOK_LINE + i;
		if( guildBook.member[guildBookSortTbl[no].index].address.useFlag )
		{
			// Sendボタンにフォーカスがあるか？
			if( (pushGraBtnInfo1( &sendBtn[i] ) & BTN_FOCUS_ON) )
			{
				btnFocusNo = 0;
			}
			else
			// Historyボタンにフォーカスがあるか？
			if( (pushGraBtnInfo1( &historyBtn[i] ) & BTN_FOCUS_ON) )
			{
				btnFocusNo = 1;
			}
			else
			// DELボタンにフォーカスがあるか？
			if( (pushGraBtnInfo1( &delBtn[i] ) & BTN_FOCUS_ON) )
			{
				btnFocusNo = 2;
			}
			else
			// GU TITLE ボタンにフォーカスがあるか？
			if( (pushGraBtnInfo1( &gutitleBtn[i] ) & BTN_FOCUS_ON) )
			{
				btnFocusNo = 3;
			}
			// Sendボタンチェック
			if( (pushGraBtnInfo1( &sendBtn[i] ) & BTN_LEFT_CLICK) )
			{
				mailSendSelectNo = guildBookSortTbl[no].index;
				mailEditAddressBookPage = addressBookPage;
				petMailSendSelectNo = guildBookSortTbl[no].index;

				// メールスキルを持ったペットがいるかチェック
				petMailflag = 0;
				if( ProcNo != PROC_BATTLE )
				{
					for( j = 0; j < MAX_PET; j++ )
					{
						// ペットメール技を持ってるかチェック
						if( (petMailflag = G_checkPetMail( j )) )
						{
							break;
						}
					}
				}
				// いたらメール选择ウィンドウを开く
				if( petMailflag )
				{
					menuOpen( G_MENU_MAIL_SELECT_WIN );
				}
				else
				// いないなら直接メールエディットウィンドウを开く
				{
					menuOpen( G_MENU_MAIL_EDIT_WIN );
				}
				break;
			}
			else
			// Historyボタンチェック
			if( (pushGraBtnInfo1( &historyBtn[i] ) & BTN_LEFT_CLICK) )
			{
				mailGuildHistorySelectNo = guildBookSortTbl[no].index;
#if 1
				mailSendSelectNo = guildBookSortTbl[no].index;
				mailEditAddressBookPage = addressBookPage;
				petMailSendSelectNo = guildBookSortTbl[no].index;
#endif
				menuOpen( G_MENU_MAIL_HISTORY_WIN );
				break;
			}
			else
			// DELボタンチェック
			if( (pushGraBtnInfo1( &delBtn[i] ) & BTN_LEFT_CLICK) )
			{
				// 削除ウィンドウ开く
				addressBookDelNo = guildBookSortTbl[no].index;
				menuOpen( G_MENU_ADDRESS_BOOK_DEL_WIN );
				break;
			}
			else
			// GU TITLE ボタンチェック
			if( (pushGraBtnInfo1( &gutitleBtn[i] ) & BTN_LEFT_CLICK) )
			{
				// GUTITLEウィンドウ开く
				guildTitleIdx = guildBookSortTbl[no].index;
				menuOpen( G_MENU_TITLE_BOOK_WIN );
				break;
			}
		}
	}

	sortBtnBit = pushGraBtnInfo1( &sortBtn );
	leftBtnBit = pushGraBtnInfo1( &leftBtn );
	rightBtnBit = pushGraBtnInfo1( &rightBtn );
	g_detBtnBit = pushGraBtnInfo1( &g_detBtn );

#ifdef PUK2		//####toda
	adr_go_BtnBit = pushGraBtnInfo1( &adr_go_Btn );
#endif

	// 左ボタン押したか？
	if( (leftBtnBit & BTN_LEFT_CLICK_REP)
	 || keyOnRepWithCtrl( VK_Z ) )
	{
		addressBookPage--;
		if( addressBookPage < 0 )
		{
			addressBookPage = GUILD_BOOK_PAGE-1;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// 右ボタン押したか？
	if( (rightBtnBit & BTN_LEFT_CLICK_REP)
	 || keyOnRepWithCtrl( VK_X ) )
	{
		addressBookPage++;
		if( addressBookPage >= GUILD_BOOK_PAGE )
		{
			addressBookPage = 0;
		}
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// ソートボタン押されたか？
	if( (sortBtnBit & BTN_LEFT_CLICK) )
	{
		menuOpen( G_MENU_ADDRESS_BOOK_SORT_WIN );
	}
	else
	// GUILD DETAIL BUTTON 押されたか？
	if( (g_detBtnBit & BTN_LEFT_CLICK) ){
		menuOpen( G_MENU_DETAIL_BOOK_WIN );
	}
#ifdef PUK2		//####toda
	else
	// 家族ボタン押されたか？
	if( (adr_go_BtnBit & BTN_LEFT_CLICK) )
	{
		menuOpen( MENU_ADDRESS_BOOK_WIN );
	}
#endif

	for( i = 0; i < GUILD_BOOK_LINE; i++ )
	{
		no = addressBookPage * GUILD_BOOK_LINE + i;
		if( guildBook.member[guildBookSortTbl[no].index].address.useFlag )
		{
			if( guildBook.member[guildBookSortTbl[no].index].address.onlineFlag > 0 )
			{
				color = FONT_PAL_WHITE;
			}
			else
			{
				color = FONT_PAL_GRAY;
			}
#ifdef _CG2_NEWGRAPHIC
			// 颜画像表示
	#ifdef PUK2
			StockDispBuffer( G_addressBookMenuWin.x+36, G_addressBookMenuWin.y+68+i*90,
				DISP_PRIO_MENU,
				getNewFaceGraphicNo(guildBook.member[guildBookSortTbl[no].index].address.graNo), 0, &bm );
	#else
			StockDispBuffer( G_addressBookMenuWin.x+36, G_addressBookMenuWin.y+68+i*90,
				DISP_PRIO_MENU,
				getNewFaceGraphicNo(guildBook.member[guildBookSortTbl[no].index].address.graNo), 0 );
	#endif
#else
			// 颜画像表示
	#ifdef PUK2
			StockDispBuffer( G_addressBookMenuWin.x+36, G_addressBookMenuWin.y+68+i*90,
				DISP_PRIO_MENU,	guildBook.member[guildBookSortTbl[no].index].address.graNo, 0, &bm );
	#else
			StockDispBuffer( G_addressBookMenuWin.x+36, G_addressBookMenuWin.y+68+i*90,
				DISP_PRIO_MENU,	guildBook.member[guildBookSortTbl[no].index].address.graNo, 0 );
	#endif
#endif

			// 接続状态表示
#ifdef PUK2
			StockDispBuffer( G_addressBookMenuWin.x+105, G_addressBookMenuWin.y+52+i*90, DISP_PRIO_MENU,
				connectServerNameGraNoTbl[guildBook.member[guildBookSortTbl[no].index].address.onlineFlag], 0, &bm );
#else
			StockDispBuffer( G_addressBookMenuWin.x+105, G_addressBookMenuWin.y+52+i*90, DISP_PRIO_MENU,
				connectServerNameGraNoTbl[guildBook.member[guildBookSortTbl[no].index].address.onlineFlag], 0 );
#endif

			// 等级表示
			sprintf( str, "%3d", guildBook.member[guildBookSortTbl[no].index].address.lv ); //MLHIDE
			StockFontBuffer( G_addressBookMenuWin.x+180, G_addressBookMenuWin.y +45 +i*90,
				FONT_PRIO_FRONT, FONT_KIND_SMALL, color, str, 0, 0 );

			// 名称の表示
			StockFontBuffer( G_addressBookMenuWin.x +77, G_addressBookMenuWin.y +68 +i*90,
				FONT_PRIO_FRONT, FONT_KIND_SMALL, color,
				guildBook.member[guildBookSortTbl[no].index].address.name, 0, 0 );

			if( guildBook.title[guildBook.member[guildBookSortTbl[no].index].titleId].name[0] != '\0' )
			{
				// 家族称号の表示
				StockFontBuffer( G_addressBookMenuWin.x +77, G_addressBookMenuWin.y +91 +i*90,
					FONT_PRIO_FRONT, FONT_KIND_SMALL, color,
					guildBook.title[guildBook.member[guildBookSortTbl[no].index].titleId].name, 0, 0 );
			}
//			if( guildBook.member[guildBookSortTbl[no].index].address.freeName[0] != '\0' )
//			{
//				// 自己称号の表示
//				StockFontBuffer( G_addressBookMenuWin.x +77, G_addressBookMenuWin.y +91 +i*90,
//					FONT_PRIO_FRONT, FONT_KIND_SMALL, color,
//					guildBook.member[guildBookSortTbl[no].index].address.freeName, 0, 0 );
//			}

			// Sendボタン表示
			drawGraBtnInfo1( &sendBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );

			// Historyボタン表示
			drawGraBtnInfo1( &historyBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );

			// Delボタン表示
			drawGraBtnInfo1( &delBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );

			// GU TITLEボタン表示
			drawGraBtnInfo1( &gutitleBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );

		}
	}

	// ページ表示
	sprintf( str, "%2d/%2d", addressBookPage+1, GUILD_BOOK_PAGE );       //MLHIDE
	StockFontBuffer( G_addressBookMenuWin.x+176, G_addressBookMenuWin.y+399,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );

	// Sortボタン表示
	drawGraBtnInfo1( &sortBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 左ボタン表示
	drawGraBtnInfo1( &leftBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// 右ボタン表示
	drawGraBtnInfo1( &rightBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// GUILD DETAIL ボタン表示
	drawGraBtnInfo1( &g_detBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

#ifdef PUK2		//####toda
	//家族ボタン表示（表示なし）
	drawGraBtnInfo1( &adr_go_Btn, DISP_PRIO_MENU, 1, BoxColor, 0 );
#endif

	// １行インフォ表示
	if( checkFocusMenuClose( &statusMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// 左右ボタン
	if( (leftBtnBit & BTN_FOCUS_ON)
	 || (rightBtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "ページを切り替えます。" );                            //MLHIDE
	}
	else
	// SORTボタン
	if( (sortBtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "家族成员の表示顺番をソートします。" );                      //MLHIDE
	}
	else
	// GUILD DETAILボタン
	if( (g_detBtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "家族详细を表示します。" );                            //MLHIDE
	}

#ifdef PUK2		//####toda
	else
	// ADDRESS GO ボタン
	if( (adr_go_BtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "アドレスブックを表示します。" );                         //MLHIDE
	}
#endif

	else
	// Sendボタン
	if( btnFocusNo == 0 )
	{
		strcpy( OneLineInfoStr, ML_STRING(243, "给这个家族成员送信。") );
	}
	else
	// Historyボタン
	if( btnFocusNo == 1 )
	{
		strcpy( OneLineInfoStr, ML_STRING(244, "查看这个家族成员的邮件历史记录。") );
	}
	else
	// DELボタン
	if( btnFocusNo == 2 )
	{
		strcpy( OneLineInfoStr, ML_STRING(245, "删除这个家族成员。") );
	}
	else
	// GU TITLEボタン
	if( btnFocusNo == 3 )
	{
		strcpy( OneLineInfoStr, ML_STRING(246, "家族称号变更。") );
	}

	// メニュー共通表示处理
	menuCommonDraw( &G_addressBookMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}

//-------------------------------------------------------------------------//
// 家族ディテール   		                                               //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_detailMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 300, 438, G_MENU_DETAIL_BOOK_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_DETAIL_GUILD_BOOK_WIN
	},
};
// 0/1/2 == guild/guild room/new
INPUT_STR	*guildInput[3] = {NULL, NULL, NULL};
INPUT_STR	guildInputBuf[3];
char		guild_ibak[2][128];
int			iMtr;

int			iSelTitleNo = -1;

// 初期化
void G_initdetailBookMenu( void )
{
	// 家族组んでない场合
	if(guildBook.guildId == -1 ){
		iMtr = 0;
		return;
	}
	if(guildBook.pcGuildTitleId==0)	iMtr=1;
	else							iMtr=0;
}

void	G_detailBookMenu	( int status )
{
	GRA_BTN_INFO1 setBtn[3] =
	{
		{	G_detailMenuWin.x+139, G_detailMenuWin.y+212, 	G_detailMenuWin.x+205, G_detailMenuWin.y+58,
			64, 24,		CG_DETAIL_GUILD_BOOK_SET_1, CG_DETAIL_GUILD_BOOK_SET_2		},
		{	G_detailMenuWin.x+139, G_detailMenuWin.y+272,	G_detailMenuWin.x+205, G_detailMenuWin.y+118,
			64, 24,		CG_DETAIL_GUILD_BOOK_SET_1, CG_DETAIL_GUILD_BOOK_SET_2		},
		{	G_detailMenuWin.x+139, G_detailMenuWin.y+334,	G_detailMenuWin.x+205, G_detailMenuWin.y+180,
			64, 24,		CG_DETAIL_GUILD_BOOK_SET_1, CG_DETAIL_GUILD_BOOK_SET_2		},
	};
	GRA_BTN_INFO1 leftBtn =
	{	G_detailMenuWin.x+120, G_detailMenuWin.y+233,	G_detailMenuWin.x+112, G_detailMenuWin.y+225,
		17, 17,		CG_DETAIL_GUILD_BOOK_LEFT_1, CG_DETAIL_GUILD_BOOK_LEFT_2	};
	GRA_BTN_INFO1 rightBtn =
	{	G_detailMenuWin.x+197, G_detailMenuWin.y+233,	G_detailMenuWin.x+189, G_detailMenuWin.y+225,
		17, 17,		CG_DETAIL_GUILD_BOOK_RIGHT_1, CG_DETAIL_GUILD_BOOK_RIGHT_2	};
	GRA_BTN_INFO1 invBtn[GUILD_TITLE_LINE_D] =		//加入
	{
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+  0+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+  0,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+ 30+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+ 30,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+ 60+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+ 60,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+ 90+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+ 90,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+120+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+120,
			14, 14,		0, 0		},
	};
	GRA_BTN_INFO1 disBtn[GUILD_TITLE_LINE_D] =		//除名
	{
		{	G_detailMenuWin.x+232, G_detailMenuWin.y+266+  0+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+  0,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+232, G_detailMenuWin.y+266+ 30+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+ 30,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+232, G_detailMenuWin.y+266+ 60+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+ 60,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+232, G_detailMenuWin.y+266+ 90+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+ 90,
			14, 14,		0, 0		},
		{	G_detailMenuWin.x+232, G_detailMenuWin.y+266+120+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+120,
			14, 14,		0, 0		},
	};
	GRA_BTN_INFO1 invBtn2[GUILD_TITLE_LINE_D] =		//加入
	{
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+  0+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+  0,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+ 30+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+ 30,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+ 60+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+ 60,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+ 90+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+ 90,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+216, G_detailMenuWin.y+266+120+2,	G_detailMenuWin.x+208, G_detailMenuWin.y+261+120,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
	};
	GRA_BTN_INFO1 disBtn2[GUILD_TITLE_LINE_D] =		//除名
	{
		{	G_detailMenuWin.x+232 +1, G_detailMenuWin.y+266+  0+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+  0,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+232 +1, G_detailMenuWin.y+266+ 30+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+ 30,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+232 +1, G_detailMenuWin.y+266+ 60+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+ 60,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+232 +1, G_detailMenuWin.y+266+ 90+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+ 90,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
		{	G_detailMenuWin.x+232 +1, G_detailMenuWin.y+266+120+2,	G_detailMenuWin.x+224, G_detailMenuWin.y+261+120,
			14, 14,		CG_DETAIL_GUILD_BOOK_ON, 0		},
	};
	GRA_BTN_INFO1 dtlBtn[GUILD_TITLE_LINE_D] =		//g_detail title select
	{
		{	G_detailMenuWin.x+11+188, G_detailMenuWin.y+258+22+  0,	G_detailMenuWin.x+11, G_detailMenuWin.y+258+  0,
			376, 44,		0, 0	},
		{	G_detailMenuWin.x+11+188, G_detailMenuWin.y+258+22+ 30,	G_detailMenuWin.x+11, G_detailMenuWin.y+258+ 30,
			376, 44,		0, 0	},
		{	G_detailMenuWin.x+11+188, G_detailMenuWin.y+258+22+ 60,	G_detailMenuWin.x+11, G_detailMenuWin.y+258+ 60,
			376, 44,		0, 0	},
		{	G_detailMenuWin.x+11+188, G_detailMenuWin.y+258+22+ 90,	G_detailMenuWin.x+11, G_detailMenuWin.y+258+ 90,
			376, 44,		0, 0	},
		{	G_detailMenuWin.x+11+188, G_detailMenuWin.y+258+22+120,	G_detailMenuWin.x+11, G_detailMenuWin.y+258+120,
			376, 44,		0, 0	},
	};

int		ret;
int		i,k;
int		col;
char	tmp[128];
int		setBtnBit[3] = {0,0,0};
// ＳＥＴボタンの押された时间を记忆
static unsigned int setBtnPushTime = 0;
static	int		g_dt_page;
static	int		g_dt_pmax;
// title focus
static	int		g_d_fcs[GUILD_TITLE_LINE_D];
// 左ボタンに对するマウス操作情报ビット
int leftBtnBit = 0;
// 右ボタンに对するマウス操作情报ビット
int rightBtnBit = 0;
//title btn
int	dtlBtnBit[GUILD_TITLE_LINE_D]={0,0,0,0,0};
int invBtnBit[GUILD_TITLE_LINE_D]={0,0,0,0,0};
int disBtnBit[GUILD_TITLE_LINE_D]={0,0,0,0,0};

	// メニュー共通处理
	ret = menuCommonProc( status, &G_detailMenuWin, G_initdetailBookMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		k=0;
		for(i=0;i<3;i++){
			if(checkInputFocus( guildInput[i] ))	k++;
		}
		if(k){
			GetKeyInputFocus( &MyChatBuffer );
		}
		for(i=0;i<3;i++)	guildInput[i]=NULL;
		return;
	}
	if(guildInput[0]==NULL){	//初期化
		for(i=0;i<3;i++){
			guildInput[i] = &guildInputBuf[i];
			//
			guildInput[i]->buffer[0] = '\0';
			guildInput[i]->cnt = 0;
			guildInput[i]->cursorByte = 0;
			if(i==0)	strcpy(tmp, guildBook.guildName);
			if(i==1)	strcpy(tmp, guildBook.guildRoomName);
			if(i==2)	strcpy(tmp, "");
			InitInputStr( guildInput[i], G_detailMenuWin.x+11 +2, G_detailMenuWin.y+58+ 60*i +2,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, tmp, 1, CHAR_FREENAME_LEN, 0, 0 );
		}
		strcpy(	guild_ibak[0], guildBook.guildName);
		strcpy(	guild_ibak[1], guildBook.guildRoomName);
		GetKeyInputFocus( guildInput[0] );
		//
		g_dt_page=0;
		g_dt_pmax=GUILD_TITLE_MAX/GUILD_TITLE_LINE_D;
		if(GUILD_TITLE_MAX % GUILD_TITLE_LINE_D)	g_dt_pmax++;
		for(i=0;i<GUILD_TITLE_LINE_D;i++) g_d_fcs[i]=0;
		iSelTitleNo = -1;
	}
	//client<-server で变わった时
	if(strcmp(guild_ibak[0], guildBook.guildName)){
		strcpy(	guild_ibak[0], guildBook.guildName);
		InitInputStr( guildInput[0], G_detailMenuWin.x+11 +2, G_detailMenuWin.y+58+ 60*0 +2,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, guild_ibak[0], 1, CHAR_FREENAME_LEN, 0, 0 );
	}
	if(strcmp(guild_ibak[1], guildBook.guildRoomName)){
		strcpy(	guild_ibak[1], guildBook.guildRoomName);
		InitInputStr( guildInput[1], G_detailMenuWin.x+11 +2, G_detailMenuWin.y+58+ 60*1 +2,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, guild_ibak[1], 1, CHAR_FREENAME_LEN, 0, 0 );
	}
	//共通
	// 左ボタンが押されたか？
	if( ((leftBtnBit = pushGraBtnInfo1( &leftBtn )) & BTN_LEFT_CLICK)
	 || keyOnRepWithCtrl( VK_Z ) ){
		g_dt_page--;
		if(g_dt_page<0) g_dt_page=g_dt_pmax-1;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		for(i=0;i<GUILD_TITLE_LINE_D;i++) g_d_fcs[i]=0;
		iSelTitleNo = -1;
	}
	else
	// 右ボタンが押されたか？
	if( ((rightBtnBit = pushGraBtnInfo1( &rightBtn )) & BTN_LEFT_CLICK)
	 || keyOnRepWithCtrl( VK_X ) ){
		g_dt_page = ++g_dt_page%g_dt_pmax;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		for(i=0;i<GUILD_TITLE_LINE_D;i++) g_d_fcs[i]=0;
		iSelTitleNo = -1;
	}
	//家族マスター用
	if(iMtr){
		// 入力上３つ
		for(i=0;i<3;i++){
			if( MakeHitBox(
					G_detailMenuWin.x +11 -3,     G_detailMenuWin.y+58    +i*60,
					G_detailMenuWin.x +11+188 -3, G_detailMenuWin.y+58 +22+i*60, -1 ) )	{
				StockBoxDispBuffer(	G_detailMenuWin.x +11 -3,     G_detailMenuWin.y+58   +i*60,
									G_detailMenuWin.x +11+188 -3, G_detailMenuWin.y+58+22+i*60,
									DISP_PRIO_BOXFILL, BoxColor/*SYSTEM_PAL_AQUA*/, 0 );
				GetKeyInputFocus( guildInput[i] );
			}
		}
		if( setBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
			//SET for 家族名
			if( ((setBtnBit[0] = pushGraBtnInfo1( &setBtn[0] )) & BTN_LEFT_CLICK) ){
				if(strcmp(guildInput[0]->buffer, guildBook.guildName)){
				char szTranBuffer[128],szSendBuffer[128];
////					strcpy(guildBook.guildName, guildInput[0]->buffer);
					// ここは S-JIS から EUC に变换して信息をエスケープする。
////					makeSendString( guildBook.guildName, szSendBuffer, sizeof( szSendBuffer ) );
					strcpy(szTranBuffer,guildInput[0]->buffer);
					makeSendString( szTranBuffer, szSendBuffer, sizeof( szSendBuffer ) );
					// サーバに送信
					nrproto_GI_send( sockfd, 2, szSendBuffer );
					play_se( SE_NO_OK3, 320, 240 );
////					strcpy(guildBook.guildName, guildInput[0]->buffer);
				}
				setBtnPushTime = GetTickCount();
			}
			else
			//家族ルーム名
			if( ((setBtnBit[1] = pushGraBtnInfo1( &setBtn[1] )) & BTN_LEFT_CLICK) ){
				if(strcmp(guildInput[1]->buffer, guildBook.guildRoomName)){
				char szTranBuffer[128],szSendBuffer[128];
////					strcpy(guildBook.guildRoomName, guildInput[1]->buffer);
					// ここは S-JIS から EUC に变换して信息をエスケープする。
////					makeSendString( guildBook.guildRoomName, szSendBuffer, sizeof( szSendBuffer ) );
					strcpy(szTranBuffer,guildInput[1]->buffer);
					makeSendString( szTranBuffer, szSendBuffer, sizeof( szSendBuffer ) );
					// サーバに送信
					nrproto_GI_send( sockfd, 3, szSendBuffer );
					play_se( SE_NO_OK3, 320, 240 );
////					strcpy(guildBook.guildRoomName, guildInput[1]->buffer);
				}
				setBtnPushTime = GetTickCount();
			}
			else
			//新家族称号
			if( ((setBtnBit[2] = pushGraBtnInfo1( &setBtn[2] )) & BTN_LEFT_CLICK) ){
				if(guildInput[2]->buffer[0]){
					for(k=0;k<GUILD_TITLE_LINE_D;k++){
						if(g_d_fcs[k]) break;
					}
					if(k<GUILD_TITLE_LINE_D){	//上书き
					char szTranBuffer[128],szSendBuffer[128];
						//g_d_fcs[k] 对象
////						strcpy(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+k].name,
////								guildInput[2]->buffer);
						// ここは S-JIS から EUC に变换して信息をエスケープする。
						strcpy(szTranBuffer,guildInput[2]->buffer);
						makeSendString( szTranBuffer, szSendBuffer, sizeof( szSendBuffer ) );
						// サーバに送信
						nrproto_GT_send( 	sockfd,
											GUILD_TITLE_LINE_D*g_dt_page+k,
											guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+k].flag/* & 3*/,
											szSendBuffer );
						play_se( SE_NO_OK3, 320, 240 );
						for(i=0;i<GUILD_TITLE_LINE_D;i++) g_d_fcs[i]=0;
						iSelTitleNo = -1;

						InitInputStr( guildInput[2], G_detailMenuWin.x+11 +2, G_detailMenuWin.y+58+ 60*2 +2,
							FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, "", 1, CHAR_FREENAME_LEN, 0, 0 );

////						strcpy(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+k].name,
////								guildInput[2]->buffer);
					}
					else{						//新规
						for(k=0;k<GUILD_TITLE_MAX;k++){
							if(guildBook.title[k].name[0]==NULL) break;
						}
						if(k<GUILD_TITLE_MAX){
						char szTranBuffer[128],szSendBuffer[128];
////							strcpy(guildBook.title[k].name,
////									guildInput[2]->buffer);
							// ここは S-JIS から EUC に变换して信息をエスケープする。
							strcpy(szTranBuffer,guildInput[2]->buffer);
							makeSendString( szTranBuffer, szSendBuffer, sizeof( szSendBuffer ) );
							// サーバに送信cvs update
							
							nrproto_GT_send(	sockfd,
												-1,
												guildBook.title[k].flag/* & 3*/,
												szSendBuffer );
							play_se( SE_NO_OK3, 320, 240 );
////							strcpy(guildBook.title[k].name,
////									guildInput[2]->buffer);
							InitInputStr( guildInput[2], G_detailMenuWin.x+11 +2, G_detailMenuWin.y+58+ 60*2 +2,
								FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, "", 1, CHAR_FREENAME_LEN, 0, 0 );
						}
						else{	//ng
							play_se( SE_NO_NG, 320, 240 );
						}
					}
				}
				setBtnPushTime = GetTickCount();
			}
			//加入 on/off
			for(i=0;i<GUILD_TITLE_LINE_D;i++){
				if( ((invBtnBit[i] = pushGraBtnInfo1( &invBtn[i] )) & BTN_LEFT_CLICK) ) break;
			}
			if(i<GUILD_TITLE_LINE_D){
				if(GUILD_TITLE_LINE_D*g_dt_page+i < GUILD_TITLE_MAX
				&& guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name[0]){
				char szSendBuffer[128];
				char	cbak[128];
					strcpy(cbak, guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name);
					guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].flag^=GUILD_FLAG_INVITE;
					// ここは S-JIS から EUC に变换して信息をエスケープする。
					makeSendString( cbak, szSendBuffer, sizeof( szSendBuffer ) );
					// サーバに送信
					nrproto_GT_send( 	sockfd,
										GUILD_TITLE_LINE_D*g_dt_page+i,
										guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].flag/* & 3*/,
										szSendBuffer );
					play_se( SE_NO_OK3, 320, 240 );
					setBtnPushTime = GetTickCount();
////					strcpy(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name, cbak);
				}
			}
			//除名 on/off
			for(i=0;i<GUILD_TITLE_LINE_D;i++){
				if( ((disBtnBit[i] = pushGraBtnInfo1( &disBtn[i] )) & BTN_LEFT_CLICK) ) break;
			}
			if(i<GUILD_TITLE_LINE_D){
				if(GUILD_TITLE_LINE_D*g_dt_page+i < GUILD_TITLE_MAX
				&& guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name[0]){
				char szSendBuffer[128];
				char cbak[128];
					strcpy(cbak, guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name);
					guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].flag^=GUILD_FLAG_DISMISS;
					// ここは S-JIS から EUC に变换して信息をエスケープする。
					makeSendString( cbak, szSendBuffer, sizeof( szSendBuffer ) );
					// サーバに送信
					nrproto_GT_send( 	sockfd,
										GUILD_TITLE_LINE_D*g_dt_page+i,
										guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].flag/* & 3*/,
										szSendBuffer );
					play_se( SE_NO_OK3, 320, 240 );
					setBtnPushTime = GetTickCount();
////					strcpy(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name, cbak);
				}
			}
		}
		for(i=0;i<3;i++){
			// ＳＥＴボタン表示
			drawGraBtnInfo1( &setBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );
		}
		for(i=0;i<GUILD_TITLE_LINE_D;i++){
			if( (GUILD_TITLE_LINE_D*g_dt_page+i)<GUILD_TITLE_MAX
			&&  guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name[0]){
				// onoffボタン表示
				drawGraBtnInfo1( &invBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );
				drawGraBtnInfo1( &disBtn[i], DISP_PRIO_MENU, 1, BoxColor, 0 );
				if(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].flag & GUILD_FLAG_INVITE){
					drawGraBtnInfo1( &invBtn2[i], DISP_PRIO_MENU, 0, BoxColor, 1 );
				}
				if(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].flag & GUILD_FLAG_DISMISS){
					drawGraBtnInfo1( &disBtn2[i], DISP_PRIO_MENU, 0, BoxColor, 1 );
				}
			}
		}
		//フォーカスタイトル
		for(i=0;i<GUILD_TITLE_LINE_D;i++){
			if((GUILD_TITLE_LINE_D*g_dt_page+i)<GUILD_TITLE_MAX){
				if( MakeHitBox(
						G_detailMenuWin.x +11,     G_detailMenuWin.y+258    +i*30,
						G_detailMenuWin.x +11+188, G_detailMenuWin.y+258 +22+i*30, -1 ) )	{
					if(guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name[0]!=NULL){
						StockBoxDispBuffer(	G_detailMenuWin.x +11,     G_detailMenuWin.y+258   +i*30 -1,
											G_detailMenuWin.x +11+188, G_detailMenuWin.y+258+22+i*30 -1,
											DISP_PRIO_BOXFILL, BoxColor/*SYSTEM_PAL_AQUA*/, 0 );
						if( (dtlBtnBit[i] = pushGraBtnInfo1( &dtlBtn[i] )) & BTN_LEFT_CLICK){
							for(k=0;k<GUILD_TITLE_LINE_D;k++) g_d_fcs[k]=0;
							g_d_fcs[i]=1;
							iSelTitleNo = GUILD_TITLE_LINE_D*g_dt_page+i;
							InitInputStr( guildInput[2], G_detailMenuWin.x+11 +4, G_detailMenuWin.y+58+ 60*2 +4,
								FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
								guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name,
								1, CHAR_FREENAME_LEN, 0, 0 );
						}
					}
				}
			}
		}
	}
	//共通
	//タイトル表示するだけ、フォーカスビットで色がえ
	for(i=0;i<GUILD_TITLE_LINE_D;i++){
		if(g_d_fcs[i])	col = FONT_PAL_AQUA;
		else			col = FONT_PAL_WHITE;
		if((GUILD_TITLE_LINE_D*g_dt_page+i)<GUILD_TITLE_MAX){
			StockFontBuffer( G_detailMenuWin.x+11 +2, G_detailMenuWin.y+258 + 30*i +2,
							 FONT_PRIO_FRONT, FONT_KIND_MIDDLE, col,
							 guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name, 0, 0 );
		}
	}
	// 左ボタン表示
	drawGraBtnInfo1( &leftBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	// 右ボタン表示
	drawGraBtnInfo1( &rightBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	// ページ数表示
	sprintf( tmp, "%2d/%2d", g_dt_page+1,  g_dt_pmax);                   //MLHIDE
	StockFontBuffer( G_detailMenuWin.x+132, G_detailMenuWin.y+225,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		tmp, 0, 0 );
	// 入力中の文字列表示
	for(i=0;i<3;i++)	StockFontBuffer2( guildInput[i] );
	//以下、说明
	if(iMtr){
		// ＳＥＴボタンの说明
		if( (setBtnBit[0] & BTN_FOCUS_ON) )	{
			strcpy( OneLineInfoStr, "家族名を入力します。" );                            //MLHIDE
		}
		else
		if( (setBtnBit[1] & BTN_FOCUS_ON) )	{
			strcpy( OneLineInfoStr, "家族ハウス名を入力します。" );                         //MLHIDE
		}
		else
		if( (setBtnBit[2] & BTN_FOCUS_ON) )	{
			strcpy( OneLineInfoStr, "家族称号を入力します。" );                           //MLHIDE
		}
	}
	//共通
	// ページ切り替えボタンの说明
	if( (leftBtnBit & BTN_FOCUS_ON) || (rightBtnBit & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "ページを切り替えます。" );                            //MLHIDE
	}
	if(((dtlBtnBit[0]&BTN_FOCUS_ON)&&1)
	|| ((dtlBtnBit[1]&BTN_FOCUS_ON)&&1)
	|| ((dtlBtnBit[2]&BTN_FOCUS_ON)&&1)
	|| ((dtlBtnBit[3]&BTN_FOCUS_ON)&&1)
	|| ((dtlBtnBit[4]&BTN_FOCUS_ON)&&1)
	){
		strcpy( OneLineInfoStr, "家族称号を选择します。" );                            //MLHIDE
	}
	for(i=0;i<GUILD_TITLE_LINE_D;i++){
		if((invBtnBit[i]&BTN_FOCUS_ON)
		&& (GUILD_TITLE_LINE_D*g_dt_page+i<GUILD_TITLE_MAX)
		&& (guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name[0]!=NULL) ){
			break;
		}
	}
	if(i<GUILD_TITLE_LINE_D){
		strcpy( OneLineInfoStr, "加入权限を许可します。" );                            //MLHIDE
	}
	for(i=0;i<GUILD_TITLE_LINE_D;i++){
		if((disBtnBit[i]&BTN_FOCUS_ON)
		&& (GUILD_TITLE_LINE_D*g_dt_page+i<GUILD_TITLE_MAX)
		&& (guildBook.title[GUILD_TITLE_LINE_D*g_dt_page+i].name[0]!=NULL) ){
			break;
		}
	}
	if(i<GUILD_TITLE_LINE_D){
		strcpy( OneLineInfoStr, "除名权限を许可します。" );                            //MLHIDE
	}
	//
	// メニュー共通表示处理
	menuCommonDraw( &G_detailMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}

//-------------------------------------------------------------------------//
// 家族称号   		                                               //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO G_titleMenuWin =
{//   x,   y,   w,   h, menuNo,
	   0,  0, 272, 411, G_MENU_TITLE_BOOK_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_TITLE_GUILD_BOOK_WIN
	},
};

int		Gtitle_init=NULL;

// 初期化
void G_inittitleBookMenu( void )
{
	if(guildBook.pcGuildTitleId==0)	iMtr=1;
	else							iMtr=0;
}

void	G_titleBookMenu	( int status )
{
	GRA_BTN_INFO1 leftBtn =
	{	G_titleMenuWin.x+139, G_titleMenuWin.y+109,		G_titleMenuWin.x+125, G_titleMenuWin.y+97,
		28, 24,		CG_TITLE_GUILD_BOOK_LEFT_1, CG_TITLE_GUILD_BOOK_LEFT_2		};
	GRA_BTN_INFO1 rightBtn =
	{	G_titleMenuWin.x+239, G_titleMenuWin.y+109,		G_titleMenuWin.x+225, G_titleMenuWin.y+97,
		28, 24,		CG_TITLE_GUILD_BOOK_RIGHT_1, CG_TITLE_GUILD_BOOK_RIGHT_2	};
	GRA_BTN_INFO1 remBtn =
	{	G_titleMenuWin.x+81, G_titleMenuWin.y+384,		G_titleMenuWin.x+41, G_titleMenuWin.y+374,
		80, 20,		CG_TITLE_GUILD_BOOK_REM_1, CG_TITLE_GUILD_BOOK_REM_2		};
	GRA_BTN_INFO1 setBtn =
	{	G_titleMenuWin.x+139-40, G_titleMenuWin.y+212+314, 	G_titleMenuWin.x+205-40, G_titleMenuWin.y+58+314,
		64, 24,		CG_TITLE_GUILD_BOOK_SET_1, CG_TITLE_GUILD_BOOK_SET_2		};
	//
	GRA_BTN_INFO1 ttlBtn[GUILD_TITLE_LINE_T] =		//g_title title select
	{
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*0,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*0,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*1,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*1,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*2,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*2,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*3,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*3,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*4,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*4,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*5,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*5,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*6,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*6,
			376, 44,		0, 0	},
		{	G_titleMenuWin.x+41+188, G_titleMenuWin.y+133+22+ 30*7,	G_titleMenuWin.x+41, G_titleMenuWin.y+133+ 30*7,
			376, 44,		0, 0	},
	};


int 	ret;
int		i,k;
int		col;
static	int		g_tt_page;
static	int		g_tt_pmax;
// 左ボタンに对するマウス操作情报ビット
int leftBtnBit = 0;
// 右ボタンに对するマウス操作情报ビット
int rightBtnBit = 0;
//
int	remBtnBit=0;
int	setBtnBit=0;
char	tmp[128];
// ＳＥＴボタンの押された时间を记忆
static unsigned int setBtnPushTime = 0;
//
int	ttlBtnBit[GUILD_TITLE_LINE_T]={0,0,0,0,0};
// title focus
static	int		g_t_fcs[GUILD_TITLE_LINE_T];
static	int		iT_SelTitleNo;

	// メニュー共通处理
	ret = menuCommonProc( status, &G_titleMenuWin, G_inittitleBookMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		Gtitle_init=NULL;
		return;
	}
	if(Gtitle_init==NULL){		//初期化
		g_tt_page=0;
		g_tt_pmax=GUILD_TITLE_MAX/GUILD_TITLE_LINE_T;
		Gtitle_init=1;
		for(i=0;i<GUILD_TITLE_LINE_T;i++){
			g_t_fcs[i]=0;
		}
		iT_SelTitleNo=(-1);
	}
	// 左ボタンが押されたか？
	if( ((leftBtnBit = pushGraBtnInfo1( &leftBtn )) & BTN_LEFT_CLICK)
	 || keyOnRepWithCtrl( VK_Z ) ){
		g_tt_page--;
		if(g_tt_page<0) g_tt_page=g_tt_pmax-1;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		for(i=0;i<GUILD_TITLE_LINE_T;i++){
			g_t_fcs[i]=0;
		}
		iT_SelTitleNo=(-1);
	}
	else
	// 右ボタンが押されたか？
	if( ((rightBtnBit = pushGraBtnInfo1( &rightBtn )) & BTN_LEFT_CLICK)
	 || keyOnRepWithCtrl( VK_X ) ){
		g_tt_page = ++g_tt_page%g_tt_pmax;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		for(i=0;i<GUILD_TITLE_LINE_T;i++){
			g_t_fcs[i]=0;
		}
		iT_SelTitleNo=(-1);
	}
	else
	if(iMtr){
		//
		for(i=0;i<GUILD_TITLE_LINE_T;i++){
			if( MakeHitBox(
					G_titleMenuWin.x  +41,     G_titleMenuWin.y+133    +i*30,
					G_titleMenuWin.x  +41+188, G_titleMenuWin.y+133 +22+i*30, -1 ) )	{
				if(guildBook.title[GUILD_TITLE_LINE_T*g_tt_page+i].name[0]!=NULL){
					StockBoxDispBuffer(	G_titleMenuWin.x +41,     G_titleMenuWin.y+133   +i*30 -1,
										G_titleMenuWin.x +41+188, G_titleMenuWin.y+133+22+i*30 -1,
										DISP_PRIO_BOXFILL, BoxColor/*SYSTEM_PAL_AQUA*/, 0 );
					if( (ttlBtnBit[i] = pushGraBtnInfo1( &ttlBtn[i] )) & BTN_LEFT_CLICK){
						for(k=0;k<GUILD_TITLE_LINE_T;k++) g_t_fcs[k]=0;
						g_t_fcs[i]=1;
						iT_SelTitleNo = GUILD_TITLE_LINE_T*g_tt_page+i;
					}
				}
			}
		}
		//
		if( setBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){
			//REM	TITLE
			if( ((remBtnBit = pushGraBtnInfo1( &remBtn )) & BTN_LEFT_CLICK)  ){
////				guildBook.member[guildTitleIdx].titleId=(-1);
				// サーバに送信
				nrproto_BGT_send( sockfd, guildTitleIdx, (-1) );
				setBtnPushTime = GetTickCount();
			}
			else
			//SET	TITLE
			if( ((setBtnBit = pushGraBtnInfo1( &setBtn )) & BTN_LEFT_CLICK)  ){
////				guildBook.member[guildTitleIdx].titleId=iSelTitleNo;
				if(iT_SelTitleNo!=(-1)){
					// サーバに送信
					nrproto_BGT_send( sockfd, guildTitleIdx, iT_SelTitleNo);
					// 决定音c（文字等クリック时）
					play_se( SE_NO_OK3, 320, 240 );
				}
				else{
					play_se( SE_NO_NG, 320, 240 );
					StockChatBufferLine( "家族称号を选择してください！", FONT_PAL_YELLOW, FONT_KIND_MIDDLE ); //MLHIDE
				}
				setBtnPushTime = GetTickCount();
			}
		}
	}
	//
	//
	//设定されているタイトル == iSelTitleNo
	//guildBook.member[guildTitleIdx].address.name	//どのメンバーか？
	//
	//
	//ユーザー名
	StockFontBuffer( G_titleMenuWin.x+30, G_titleMenuWin.y+8,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		guildBook.member[guildTitleIdx].address.name, 0, 0 );
	//设定されているタイトル == iSelTitleNo
	//guildBook.title[iSelTitleNo].name
	if(guildBook.member[guildTitleIdx].titleId!=(-1)){
		StockFontBuffer( G_titleMenuWin.x+8, G_titleMenuWin.y+58,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
			guildBook.title[guildBook.member[guildTitleIdx].titleId].name, 0, 0 );
	}
	//タイトル表示するだけ
	for(i=0;i<GUILD_TITLE_LINE_T;i++){
		if(g_t_fcs[i])	col = FONT_PAL_AQUA;
		else			col = FONT_PAL_WHITE;
		StockFontBuffer( G_titleMenuWin.x+41, G_titleMenuWin.y+133 + 30*i,
						 FONT_PRIO_FRONT, FONT_KIND_MIDDLE, col,
						 guildBook.title[GUILD_TITLE_LINE_T*g_tt_page+i].name, 0, 0 );
	}
	// 左ボタン表示
	drawGraBtnInfo1( &leftBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	// 右ボタン表示
	drawGraBtnInfo1( &rightBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	// ページ数表示
	sprintf( tmp, "%2d/%2d", g_tt_page+1,  g_tt_pmax);                   //MLHIDE
	StockFontBuffer( G_titleMenuWin.x+161, G_titleMenuWin.y+100,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		tmp, 0, 0 );
	if(iMtr){
		// REM
		drawGraBtnInfo1( &remBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
		// SET
		drawGraBtnInfo1( &setBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	//
	//
	// ページ切り替えボタンの说明
	if( (leftBtnBit & BTN_FOCUS_ON) || (rightBtnBit & BTN_FOCUS_ON) ){
		strcpy( OneLineInfoStr, "ページを切り替えます。" );                            //MLHIDE
	}
	if(iMtr){
		//REM
		if(remBtnBit & BTN_FOCUS_ON){
			strcpy( OneLineInfoStr, "家族称号を外します。" );                            //MLHIDE
		}
		//SET
		if(setBtnBit & BTN_FOCUS_ON){
			strcpy( OneLineInfoStr, "家族称号を变更します。" );                           //MLHIDE
		}
		for(i=0;i<GUILD_TITLE_LINE_T;i++){
			if(ttlBtnBit[i] & BTN_FOCUS_ON){
				strcpy( OneLineInfoStr, "家族称号を选择します。" );                          //MLHIDE
				break;
			}
		}
	}
	// メニュー共通表示处理
	menuCommonDraw( &G_titleMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}

//家族关系の初期化处理
void GuildInfoInit(void){

	int i;

	//-----------------------------------------------------
	// 家族に所属していない状态に
	guildBook.guildId = -1;
	guildBook.pcGuildTitleId = -1;
	guildBook.serverNo=-1;
	guildBook.guildName[ 0 ] = '\0';
	guildBook.guildRoomName[ 0 ] = '\0';

	// メンバーリストなど、すべて削除
	for( i = 0 ; i < GUILD_MEMBER_MAX ; i++ ){
		guildBook.member[ i ].titleId = -1;
		guildBook.member[ i ].address.useFlag = 0;
		guildBook.member[ i ].address.name[ 0 ] = '\0';
		guildBook.member[ i ].address.freeName[ 0 ] = '\0';
	}

	// 家族称号の情报を消す
	for( i = 0 ; i < GUILD_TITLE_MAX ; i++ ){
		guildBook.title[ i ].flag = 0;
		guildBook.title[ i ].name[ 0 ] = '\0';
	}

	// 家族モンスターの情报を消す
	for( i = 0 ; i < GUILD_MONSTER_MAX ; i++ ){
		guildBook.monsterName[i][0] = '\0';
	}

	//メンバー数初期化
	guildBook.memberCount=0;

	//-----------------------------------------------------
	//家族インフォウインドウが存在していたとき、ウインドウを闭じます
	CloseGuildInfoWindow();

}

//家族关系のクリア处理
void GuildInfoClear(void){

	int i;

	//-----------------------------------------------------
	// 家族に所属していない状态に
	guildBook.guildId = -1;
	guildBook.pcGuildTitleId = -1;
	guildBook.serverNo=-1;
	guildBook.guildName[ 0 ] = '\0';
	guildBook.guildRoomName[ 0 ] = '\0';

	// メンバーリストなど、すべて削除
	for( i = 0 ; i < GUILD_MEMBER_MAX ; i++ ){
		guildBook.member[ i ].titleId = -1;
		guildBook.member[ i ].address.useFlag = 0;
		guildBook.member[ i ].address.name[ 0 ] = '\0';
		guildBook.member[ i ].address.freeName[ 0 ] = '\0';
	}

	// 家族称号の情报を消す
	for( i = 0 ; i < GUILD_TITLE_MAX ; i++ ){
		guildBook.title[ i ].flag = 0;
		guildBook.title[ i ].name[ 0 ] = '\0';
	}

	// 家族モンスターの情报を消す
	for( i = 0 ; i < GUILD_MONSTER_MAX ; i++ ){
		guildBook.monsterName[i][0] = '\0';
	}

	//メンバー数初期化
	guildBook.memberCount=0;

	//使用しているキャラクターの家族メールを消去
	delGuildMailHistory();

	writeGuildMailFile();

	//-----------------------------------------------------
	//家族インフォウインドウが存在していたとき、ウインドウを闭じます
	CloseGuildInfoWindow();

}

#endif