﻿#ifndef _ANIM_TBL2_H_
#define _ANIM_TBL2_H_

//---------------------------------------------------------
// このファイルはコンバータも通る
//
//	コンバーターは #define SPR_xxxxx の行を见て
//　  xxxxxファイルを読み込んで处理をする。
//　(例 SPR_abc は abc.sprを处理する)
//---------------------------------------------------------
//#if 0

#ifdef MULTI_GRABIN
// アニメーション番号 -------------------------------------
#define SPRSTART_V2			105000		// SPRデータの始まりの番号
#define SPREND_V2			106800		// SPRデータの終わりくらいの番号
//#define SPRSTART2			300000		// SPRデータの始まりの番号
//#define SPR2OFFS			(SPRSTART2-12000)		// SPRデータの始まりの番号
//#define CHRNO_TO_SPR( a )   (( (a)>=SPRSTART2 )?((a)-SPR2OFFS):((a)-SPRSTART) )
// アニメーション番号 -------------------------------------
#define SPRSTART_V2			105000		// SPRデータの始まりの番号
#define SPREND_V2			106800		// SPRデータの始まりの番号
#define SPRPC_START_V2		105000		// ＰＣキャラ开始番号

#endif





//XG2リニューアルキャラ
#define SPR_400ax			105000		//	Rバウ斧
#define SPR_400bw			105001		//	Rバウ弓
#define SPR_400em			105002		//	Rバウ素手
#define SPR_400sd			105003		//	Rバウ剑
#define SPR_400sf			105004		//	Rバウ杖
#define SPR_400sp			105005		//	Rバウ枪

#define SPR_401ax			105006		//	Rバウ2斧
#define SPR_401bw			105007		//	Rバウ2弓
#define SPR_401em			105008		//	Rバウ2素手
#define SPR_401sd			105009		//	Rバウ2剑
#define SPR_401sf			105010		//	Rバウ2杖
#define SPR_401sp			105011		//	Rバウ2枪

#define SPR_402ax			105012		//	Rバウ3斧
#define SPR_402bw			105013		//	Rバウ3弓
#define SPR_402em			105014		//	Rバウ3素手
#define SPR_402sd			105015		//	Rバウ3剑
#define SPR_402sf			105016		//	Rバウ3杖
#define SPR_402sp			105017		//	Rバウ3枪

#define SPR_403ax			105018		//	Rバウ4斧
#define SPR_403bw			105019		//	Rバウ4弓
#define SPR_403em			105020		//	Rバウ4素手
#define SPR_403sd			105021		//	Rバウ4剑
#define SPR_403sf			105022		//	Rバウ4杖
#define SPR_403sp			105023		//	Rバウ4枪


#define SPR_410ax			105025		//	Rカズ斧
#define SPR_410bw			105026		//	Rカズ弓
#define SPR_410em			105027		//	Rカズ素手
#define SPR_410sd			105028		//	Rカズ剑
#define SPR_410sf			105029		//	Rカズ杖
#define SPR_410sp			105030		//	Rカズ枪

#define SPR_411ax			105031		//	Rカズ2斧
#define SPR_411bw			105032		//	Rカズ2弓
#define SPR_411em			105033		//	Rカズ2素手
#define SPR_411sd			105034		//	Rカズ2剑
#define SPR_411sf			105035		//	Rカズ2杖
#define SPR_411sp			105036		//	Rカズ2枪

#define SPR_412ax			105037		//	Rカズ3斧
#define SPR_412bw			105038		//	Rカズ3弓
#define SPR_412em			105039		//	Rカズ3素手
#define SPR_412sd			105040		//	Rカズ3剑
#define SPR_412sf			105041		//	Rカズ3杖
#define SPR_412sp			105042		//	Rカズ3枪

#define SPR_413ax			105043		//	Rカズ4斧
#define SPR_413bw			105044		//	Rカズ4弓
#define SPR_413em			105045		//	Rカズ4素手
#define SPR_413sd			105046		//	Rカズ4剑
#define SPR_413sf			105047		//	Rカズ4杖
#define SPR_413sp			105048		//	Rカズ4枪


#define SPR_420ax			105050		//	Rシン斧
#define SPR_420bw			105051		//	Rシン弓
#define SPR_420em			105052		//	Rシン素手
#define SPR_420sd			105053		//	Rシン剑
#define SPR_420sf			105054		//	Rシン杖
#define SPR_420sp			105055		//	Rシン枪

#define SPR_421ax			105056		//	Rシン2斧
#define SPR_421bw			105057		//	Rシン2弓
#define SPR_421em			105058		//	Rシン2素手
#define SPR_421sd			105059		//	Rシン2剑
#define SPR_421sf			105060		//	Rシン2杖
#define SPR_421sp			105061		//	Rシン2枪

#define SPR_422ax			105062		//	Rシン3斧
#define SPR_422bw			105063		//	Rシン3弓
#define SPR_422em			105064		//	Rシン3素手
#define SPR_422sd			105065		//	Rシン3剑
#define SPR_422sf			105066		//	Rシン3杖
#define SPR_422sp			105067		//	Rシン3枪

#define SPR_423ax			105068		//	Rシン4斧
#define SPR_423bw			105069		//	Rシン4弓
#define SPR_423em			105070		//	Rシン4素手
#define SPR_423sd			105071		//	Rシン4剑
#define SPR_423sf			105072		//	Rシン4杖
#define SPR_423sp			105073		//	Rシン4枪


#define SPR_430ax			105075		//	Rトブ斧
#define SPR_430bw			105076		//	Rトブ弓
#define SPR_430em			105077		//	Rトブ素手
#define SPR_430sd			105078		//	Rトブ剑
#define SPR_430sf			105079		//	RトソK?
#define SPR_430sp			105080		//	Rトブ枪

#define SPR_431ax			105081		//	Rトブ2斧
#define SPR_431bw			105082		//	Rトブ2弓
#define SPR_431em			105083		//	Rトブ2素手
#define SPR_431sd			105084		//	Rトブ2剑
#define SPR_431sf			105085		//	Rトブ2杖
#define SPR_431sp			105086		//	Rトブ2枪

#define SPR_432ax			105087		//	Rトブ3斧
#define SPR_432bw			105088		//	Rトブ3弓
#define SPR_432em			105089		//	Rトブ3素手
#define SPR_432sd			105090		//	Rトブ3剑
#define SPR_432sf			105091		//	Rトブ3杖
#define SPR_432sp			105092		//	Rトブ3枪

#define SPR_433ax			105093		//	Rトブ4斧
#define SPR_433bw			105094		//	Rトブ4弓
#define SPR_433em			105095		//	Rトブ4素手
#define SPR_433sd			105096		//	Rトブ4剑
#define SPR_433sf			105097		//	Rトブ4杖
#define SPR_433sp			105098		//	Rトブ4枪


#define SPR_440ax			105100		//	Rゲン斧
#define SPR_440bw			105101		//	Rゲン弓
#define SPR_440em			105102		//	Rゲン素手
#define SPR_440sd			105103		//	Rゲン剑
#define SPR_440sf			105104		//	Rゲン杖
#define SPR_440sp			105105		//	Rゲン枪

#define SPR_441ax			105106		//	Rゲン2斧
#define SPR_441bw			105107		//	Rゲン2弓
#define SPR_441em			105108		//	Rゲン2素手
#define SPR_441sd			105109		//	Rゲン2剑
#define SPR_441sf			105110		//	Rゲン2杖
#define SPR_441sp			105111		//	Rゲン2枪

#define SPR_442ax			105112		//	Rゲン3斧
#define SPR_442bw			105113		//	Rゲン3弓
#define SPR_442em			105114		//	Rゲン3素手
#define SPR_442sd			105115		//	Rゲン3剑
#define SPR_442sf			105116		//	Rゲン3杖
#define SPR_442sp			105117		//	Rゲン3枪

#define SPR_443ax			105118		//	Rゲン4斧
#define SPR_443bw			105119		//	Rゲン4弓
#define SPR_443em			105120		//	Rゲン4素手
#define SPR_443sd			105121		//	Rゲン4剑
#define SPR_443sf			105122		//	Rゲン4杖
#define SPR_443sp			105123		//	Rゲン4枪


#define SPR_450ax			105125		//	Rベイ斧
#define SPR_450bw			105126		//	Rベイ弓
#define SPR_450em			105127		//	Rベイ素手
#define SPR_450sd			105128		//	Rベイ剑
#define SPR_450sf			105129		//	Rベイ杖
#define SPR_450sp			105130		//	Rベイ枪

#define SPR_451ax			105131		//	Rベイ2斧
#define SPR_451bw			105132		//	Rベイ2弓
#define SPR_451em			105133		//	Rベイ2素手
#define SPR_451sd			105134		//	Rベイ2剑
#define SPR_451sf			105135		//	Rベイ2杖
#define SPR_451sp			105136		//	Rベイ2枪

#define SPR_452ax			105137		//	Rベイ3斧
#define SPR_452bw			105138		//	Rベイ3弓
#define SPR_452em			105139		//	Rベイ3素手
#define SPR_452sd			105140		//	Rベイ3剑
#define SPR_452sf			105141		//	Rベイ3杖
#define SPR_452sp			105142		//	Rベイ3枪

#define SPR_453ax			105143		//	Rベイ4斧
#define SPR_453bw			105144		//	Rベイ4弓
#define SPR_453em			105145		//	Rベイ4素手
#define SPR_453sd			105146		//	Rベイ4剑
#define SPR_453sf			105147		//	Rベイ4杖
#define SPR_453sp			105148		//	Rベイ4枪

#define SPR_460ax			105150		//	Rボグ斧
#define SPR_460bw			105151		//	Rボグ弓
#define SPR_460em			105152		//	Rボグ素手
#define SPR_460sd			105153		//	Rボグ剑
#define SPR_460sf			105154		//	Rボグ杖
#define SPR_460sp			105155		//	Rボグ枪

#define SPR_461ax			105156		//	Rボグ2斧
#define SPR_461bw			105157		//	Rボグ2弓
#define SPR_461em			105158		//	Rボグ2素手
#define SPR_461sd			105159		//	Rボグ2剑
#define SPR_461sf			105160		//	Rボグ2杖
#define SPR_461sp			105161		//	Rボグ2枪

#define SPR_462ax			105162		//	Rボグ3斧
#define SPR_462bw			105163		//	Rボグ3弓
#define SPR_462em			105164		//	Rボグ3素手
#define SPR_462sd			105165		//	Rボグ3剑
#define SPR_462sf			105166		//	Rボグ3杖
#define SPR_462sp			105167		//	Rボグ3枪

#define SPR_463ax			105168		//	Rボグ4斧
#define SPR_463bw			105169		//	Rボグ4弓
#define SPR_463em			105170		//	Rボグ4素手
#define SPR_463sd			105171		//	Rボグ4剑
#define SPR_463sf			105172		//	Rボグ4杖
#define SPR_463sp			105173		//	Rボグ4枪



//女
#define SPR_500ax			105250		//	Rウル斧
#define SPR_500bw			105251		//	Rウル弓
#define SPR_500em			105252		//	Rウル素手
#define SPR_500sd			105253		//	Rウル剑
#define SPR_500sf			105254		//	Rウル杖
#define SPR_500sp			105255		//	Rウル枪

#define SPR_501ax			105256		//	Rウル2斧
#define SPR_501bw			105257		//	Rウル2弓
#define SPR_501em			105258		//	Rウル2素手
#define SPR_501sd			105259		//	Rウル2剑
#define SPR_501sf			105260		//	Rウル2杖
#define SPR_501sp			105261		//	Rウル2枪

#define SPR_502ax			105262		//	Rウル3斧
#define SPR_502bw			105263		//	Rウル3弓
#define SPR_502em			105264		//	Rウル3素手
#define SPR_502sd			105265		//	Rウル3剑
#define SPR_502sf			105266		//	Rウル3杖
#define SPR_502sp			105267		//	Rウル3枪

#define SPR_503ax			105268		//	Rウル4斧
#define SPR_503bw			105269		//	Rウル4弓
#define SPR_503em			105270		//	Rウル4素手
#define SPR_503sd			105271		//	Rウル4剑
#define SPR_503sf			105272		//	Rウル4杖
#define SPR_503sp			105273		//	Rウル4枪


#define SPR_510ax			105275		//	Rモエ斧
#define SPR_510bw			105276		//	Rモエ弓
#define SPR_510em			105277		//	Rモエ素手
#define SPR_510sd			105278		//	Rモエ剑
#define SPR_510sf			105279		//	Rモエ杖
#define SPR_510sp			105280		//	Rモエ枪

#define SPR_511ax			105281		//	Rモエ2斧
#define SPR_511bw			105282		//	Rモエ2弓
#define SPR_511em			105283		//	Rモエ2素手
#define SPR_511sd			105284		//	Rモエ2剑
#define SPR_511sf			105285		//	Rモエ2杖
#define SPR_511sp			105286		//	Rモエ2枪

#define SPR_512ax			105287		//	Rモエ3斧
#define SPR_512bw			105288		//	Rモエ3弓
#define SPR_512em			105289		//	Rモエ3素手
#define SPR_512sd			105290		//	Rモエ3剑
#define SPR_512sf			105291		//	Rモエ3杖
#define SPR_512sp			105292		//	Rモエ3枪

#define SPR_513ax			105293		//	Rモエ4斧
#define SPR_513bw			105294		//	Rモエ4弓
#define SPR_513em			105295		//	Rモエ4素手
#define SPR_513sd			105296		//	Rモエ4剑
#define SPR_513sf			105297		//	Rモエ4杖
#define SPR_513sp			105298		//	Rモエ4枪


#define SPR_520ax			105300		//	Rアミ斧
#define SPR_520bw			105301		//	Rアミ弓
#define SPR_520em			105302		//	Rアミ素手
#define SPR_520sd			105303		//	Rアミ剑
#define SPR_520sf			105304		//	Rアミ杖
#define SPR_520sp			105305		//	Rアミ枪

#define SPR_521ax			105306		//	Rアミ2斧
#define SPR_521bw			105307		//	Rアミ2弓
#define SPR_521em			105308		//	Rアミ2素手
#define SPR_521sd			105309		//	Rアミ2剑
#define SPR_521sf			105310		//	Rアミ2杖
#define SPR_521sp			105311		//	Rアミ2枪

#define SPR_522ax			105312		//	Rアミ3斧
#define SPR_522bw			105313		//	Rアミ3弓
#define SPR_522em			105314		//	Rアミ3素手
#define SPR_522sd			105315		//	Rアミ3剑
#define SPR_522sf			105316		//	Rアミ3杖
#define SPR_522sp			105317		//	Rアミ3枪

#define SPR_523ax			105318		//	Rアミ4斧
#define SPR_523bw			105319		//	Rアミ4弓
#define SPR_523em			105320		//	Rアミ4素手
#define SPR_523sd			105321		//	Rアミ4剑
#define SPR_523sf			105322		//	Rアミ4杖
#define SPR_523sp			105323		//	Rアミ4枪


#define SPR_530ax			105325		//	Rメグ斧
#define SPR_530bw			105326		//	Rメグ弓
#define SPR_530em			105327		//	Rメグ素手
#define SPR_530sd			105328		//	Rメグ剑
#define SPR_530sf			105329		//	Rメグ杖
#define SPR_530sp			105330		//	Rメグ枪

#define SPR_531ax			105331		//	Rメグ2斧
#define SPR_531bw			105332		//	Rメグ2弓
#define SPR_531em			105333		//	Rメグ2素手
#define SPR_531sd			105334		//	Rメグ2剑
#define SPR_531sf			105335		//	Rメグ2杖
#define SPR_531sp			105336		//	Rメグ2枪

#define SPR_532ax			105337		//	Rメグ3斧
#define SPR_532bw			105338		//	Rメグ3弓
#define SPR_532em			105339		//	Rメグ3素手
#define SPR_532sd			105340		//	Rメグ3剑
#define SPR_532sf			105341		//	Rメグ3杖
#define SPR_532sp			105342		//	Rメグ3枪

#define SPR_533ax			105343		//	Rメグ4斧
#define SPR_533bw			105344		//	Rメグ4弓
#define SPR_533em			105345		//	Rメグ4素手
#define SPR_533sd			105346		//	Rメグ4剑
#define SPR_533sf			105347		//	Rメグ4杖
#define SPR_533sp			105348		//	Rメグ4枪


#define SPR_540ax			105350		//	Rレイ斧
#define SPR_540bw			105351		//	Rレイ弓
#define SPR_540em			105352		//	Rレイ素手
#define SPR_540sd			105353		//	Rレイ剑
#define SPR_540sf			105354		//	Rレイ杖
#define SPR_540sp			105355		//	Rレイ枪

#define SPR_541ax			105356		//	Rレイ2斧
#define SPR_541bw			105357		//	Rレイ2弓
#define SPR_541em			105358		//	Rレイ2素手
#define SPR_541sd			105359		//	Rレイ2剑
#define SPR_541sf			105360		//	Rレイ2杖
#define SPR_541sp			105361		//	Rレイ2枪

#define SPR_542ax			105362		//	Rレイ3斧
#define SPR_542bw			105363		//	Rレイ3弓
#define SPR_542em			105364		//	Rレイ3素手
#define SPR_542sd			105365		//	Rレイ3剑
#define SPR_542sf			105366		//	Rレイ3杖
#define SPR_542sp			105367		//	Rレイ3枪

#define SPR_543ax			105368		//	Rレイ4斧
#define SPR_543bw			105369		//	Rレイ4弓
#define SPR_543em			105370		//	Rレイ4素手
#define SPR_543sd			105371		//	Rレイ4剑
#define SPR_543sf			105372		//	Rレイ4杖
#define SPR_543sp			105373		//	Rレイ4枪


#define SPR_550ax			105375		//	Rケイ斧
#define SPR_550bw			105376		//	Rケイ弓
#define SPR_550em			105377		//	Rケイ素手
#define SPR_550sd			105378		//	Rケイ剑
#define SPR_550sf			105379		//	Rケイ杖
#define SPR_550sp			105380		//	Rケイ枪

#define SPR_551ax			105381		//	Rケイ2斧
#define SPR_551bw			105382		//	Rケイ2弓
#define SPR_551em			105383		//	Rケイ2素手
#define SPR_551sd			105384		//	Rケイ2剑
#define SPR_551sf			105385		//	Rケイ2杖
#define SPR_551sp			105386		//	Rケイ2枪

#define SPR_552ax			105387		//	Rケイ3斧
#define SPR_552bw			105388		//	Rケイ3弓
#define SPR_552em			105389		//	Rケイ3素手
#define SPR_552sd			105390		//	Rケイ3剑
#define SPR_552sf			105391		//	Rケイ3杖
#define SPR_552sp			105392		//	Rケイ3枪

#define SPR_553ax			105393		//	Rケイ4斧
#define SPR_553bw			105394		//	Rケイ4弓
#define SPR_553em			105395		//	Rケイ4素手
#define SPR_553sd			105396		//	Rケイ4剑
#define SPR_553sf			105397		//	Rケイ4杖
#define SPR_553sp			105398		//	Rケイ4枪

#define SPR_560ax			105400		//	Rエル斧
#define SPR_560bw			105401		//	Rエル弓
#define SPR_560em			105402		//	Rエル素手
#define SPR_560sd			105403		//	Rエル剑
#define SPR_560sf			105404		//	Rエル杖
#define SPR_560sp			105405		//	Rエル枪

#define SPR_561ax			105406		//	Rエル2斧
#define SPR_561bw			105407		//	Rエル2弓
#define SPR_561em			105408		//	Rエル2素手
#define SPR_561sd			105409		//	Rエル2剑
#define SPR_561sf			105410		//	Rエル2杖
#define SPR_561sp			105411		//	Rエル2枪

#define SPR_562ax			105412		//	Rエル3斧
#define SPR_562bw			105413		//	Rエル3弓
#define SPR_562em			105414		//	Rエル3素手
#define SPR_562sd			105415		//	Rエル3剑
#define SPR_562sf			105416		//	Rエル3杖
#define SPR_562sp			105417		//	Rエル3枪

#define SPR_563ax			105418		//	Rエル4斧
#define SPR_563bw			105419		//	Rエル4弓
#define SPR_563em			105420		//	Rエル4素手
#define SPR_563sd			105421		//	Rエル4剑
#define SPR_563sf			105422		//	Rエル4杖
#define SPR_563sp			105423		//	Rエル4枪


//ＸＧＥＸ新キャラ -> XG2
#define SPR_600ax			106000		//	人间A男1斧
#define SPR_600bw			106001		//	人间A男1弓
#define SPR_600em			106002		//	人间A男1素手
#define SPR_600sd			106003		//	人间A男1剑
#define SPR_600sf			106004		//	人间A男1杖
#define SPR_600sp			106005		//	人间A男1枪

#define SPR_601ax			106006		//	人间A男1斧
#define SPR_601bw			106007		//	人间A男1弓
#define SPR_601em			106008		//	人间A男1素手
#define SPR_601sd			106009		//	人间A男1剑
#define SPR_601sf			106010		//	人间A男1杖
#define SPR_601sp			106011		//	人间A男1枪

#define SPR_602ax			106012		//	人间A男1斧
#define SPR_602bw			106013		//	人间A男1弓
#define SPR_602em			106014		//	人间A男1素手
#define SPR_602sd			106015		//	人间A男1剑
#define SPR_602sf			106016		//	人间A男1杖
#define SPR_602sp			106017		//	人间A男1枪

#define SPR_603ax			106018		//	人间A男1斧
#define SPR_603bw			106019		//	人间A男1弓
#define SPR_603em			106020		//	人间A男1素手
#define SPR_603sd			106021		//	人间A男1剑
#define SPR_603sf			106022		//	人间A男1杖
#define SPR_603sp			106023		//	人间A男1枪


#define SPR_610ax			106025		//	人间B男1斧
#define SPR_610bw			106026		//	人间B男1弓
#define SPR_610em			106027		//	人间B男1素手
#define SPR_610sd			106028		//	人间B男1剑
#define SPR_610sf			106029		//	人间B男1杖
#define SPR_610sp			106030		//	人间B男1枪

#define SPR_611ax			106031		//	人间B男1斧
#define SPR_611bw			106032		//	人间B男1弓
#define SPR_611em			106033		//	人间B男1素手
#define SPR_611sd			106034		//	人间B男1剑
#define SPR_611sf			106035		//	人间B男1杖
#define SPR_611sp			106036		//	人间B男1枪

#define SPR_612ax			106037		//	人间B男1斧
#define SPR_612bw			106038		//	人间B男1弓
#define SPR_612em			106039		//	人间B男1素手
#define SPR_612sd			106040		//	人间B男1剑
#define SPR_612sf			106041		//	人间B男1杖
#define SPR_612sp			106042		//	人间B男1枪

#define SPR_613ax			106043		//	人间B男1斧
#define SPR_613bw			106044		//	人间B男1弓
#define SPR_613em			106045		//	人间B男1素手
#define SPR_613sd			106046		//	人间B男1剑
#define SPR_613sf			106047		//	人间B男1杖
#define SPR_613sp			106048		//	人间B男1枪


//XG2新キャラ
#define SPR_620ax			106050		//	人间C男1斧
#define SPR_620bw			106051		//	人间C男1弓
#define SPR_620em			106052		//	人间C男1素手
#define SPR_620sd			106053		//	人间C男1剑
#define SPR_620sf			106054		//	人间C男1杖
#define SPR_620sp			106055		//	人间C男1枪

#define SPR_621ax			106056		//	人间C男2斧
#define SPR_621bw			106057		//	人间C男2弓
#define SPR_621em			106058		//	人间C男2素手
#define SPR_621sd			106059		//	人间C男2剑
#define SPR_621sf			106060		//	人间C男2杖
#define SPR_621sp			106061		//	人间C男2枪

#define SPR_622ax			106062		//	人间C男3斧
#define SPR_622bw			106063		//	人间C男3弓
#define SPR_622em			106064		//	人间C男3素手
#define SPR_622sd			106065		//	人间C男3剑
#define SPR_622sf			106066		//	人间C男3杖
#define SPR_622sp			106067		//	人间C男3枪

#define SPR_623ax			106068		//	人间C男4斧
#define SPR_623bw			106069		//	人间C男4弓
#define SPR_623em			106070		//	人间C男4素手
#define SPR_623sd			106071		//	人间C男4剑
#define SPR_623sf			106072		//	人间C男4杖
#define SPR_623sp			106073		//	人间C男4枪


#define SPR_630ax			106075		//	人间D男1斧
#define SPR_630bw			106076		//	人间D男1弓
#define SPR_630em			106077		//	人间D男1素手
#define SPR_630sd			106078		//	人间D男1剑
#define SPR_630sf			106079		//	人间D男1杖
#define SPR_630sp			106080		//	人间D男1枪

#define SPR_631ax			106081		//	人间D男2斧
#define SPR_631bw			106082		//	人间D男2弓
#define SPR_631em			106083		//	人间D男2素手
#define SPR_631sd			106084		//	人间D男2剑
#define SPR_631sf			106085		//	人间D男2杖
#define SPR_631sp			106086		//	人间D男2枪

#define SPR_632ax			106087		//	人间D男3斧
#define SPR_632bw			106088		//	人间D男3弓
#define SPR_632em			106089		//	人间D男3素手
#define SPR_632sd			106090		//	人间D男3剑
#define SPR_632sf			106091		//	人间D男3杖
#define SPR_632sp			106092		//	人间D男3枪

#define SPR_633ax			106093		//	人间D男4斧
#define SPR_633bw			106094		//	人间D男4弓
#define SPR_633em			106095		//	人间D男4素手
#define SPR_633sd			106096		//	人间D男4剑
#define SPR_633sf			106097		//	人间D男4杖
#define SPR_633sp			106098		//	人间D男4枪


#define SPR_640ax			106100		//	小人男1斧
#define SPR_640bw			106101		//	小人男1弓
#define SPR_640em			106102		//	小人男1素手
#define SPR_640sd			106103		//	小人男11剑
#define SPR_640sf			106104		//	小人男11杖
#define SPR_640sp			106105		//	小人男1枪

#define SPR_641ax			106106		//	小人男2斧
#define SPR_641bw			106107		//	小人男2弓
#define SPR_641em			106108		//	小人男2素手
#define SPR_641sd			106109		//	小人男2剑
#define SPR_641sf			106110		//	小人男2杖
#define SPR_641sp			106111		//	小人男2枪

#define SPR_642ax			106112		//	小人男3斧
#define SPR_642bw			106113		//	小人男3弓
#define SPR_642em			106114		//	小人男3素手
#define SPR_642sd			106115		//	小人男3剑
#define SPR_642sf			106116		//	小人男3杖
#define SPR_642sp			106117		//	小人男3枪

#define SPR_643ax			106118		//	小人男4斧
#define SPR_643bw			106119		//	小人男4弓
#define SPR_643em			106120		//	小人男4素手
#define SPR_643sd			106121		//	小人男4剑
#define SPR_643sf			106122		//	小人男4杖
#define SPR_643sp			106123		//	小人男4枪


#define SPR_650ax			106125		//	エルフ男1斧
#define SPR_650bw			106126		//	エルフ男1弓
#define SPR_650em			106127		//	エルフ男1素手
#define SPR_650sd			106128		//	エルフ男11剑
#define SPR_650sf			106129		//	エルフ男11杖
#define SPR_650sp			106130		//	エルフ男1枪

#define SPR_651ax			106131		//	エルフ男2斧
#define SPR_651bw			106132		//	エルフ男2弓
#define SPR_651em			106133		//	エルフ男2素手
#define SPR_651sd			106134		//	エルフ男2剑
#define SPR_651sf			106135		//	エルフ男2杖
#define SPR_651sp			106136		//	エルフ男2枪

#define SPR_652ax			106137		//	エルフ男3斧
#define SPR_652bw			106138		//	エルフ男3弓
#define SPR_652em			106139		//	エルフ男3素手
#define SPR_652sd			106140		//	エルフ男3剑
#define SPR_652sf			106141		//	エルフ男3杖
#define SPR_652sp			106142		//	エルフ男3枪

#define SPR_653ax			106143		//	エルフ男4斧
#define SPR_653bw			106144		//	エルフ男4弓
#define SPR_653em			106145		//	エルフ男4素手
#define SPR_653sd			106146		//	エルフ男4剑
#define SPR_653sf			106147		//	エルフ男4杖
#define SPR_653sp			106148		//	エルフ男4枪


#define SPR_660ax			106150		//	ドワーフ男1斧
#define SPR_660bw			106151		//	ドワーフ男1弓
#define SPR_660em			106152		//	ドワーフ男1素手
#define SPR_660sd			106153		//	ドワーフ男11剑
#define SPR_660sf			106154		//	ドワーフ男11杖
#define SPR_660sp			106155		//	ドワーフ男1枪

#define SPR_661ax			106156		//	ドワーフ男2斧
#define SPR_661bw			106157		//	ドワーフ男2弓
#define SPR_661em			106158		//	ドワーフ男2素手
#define SPR_661sd			106159		//	ドワーフ男2剑
#define SPR_661sf			106160		//	ドワーフ男2杖
#define SPR_661sp			106161		//	ドワーフ男2枪

#define SPR_662ax			106162		//	ドワーフ男3斧
#define SPR_662bw			106163		//	ドワーフ男3弓
#define SPR_662em			106164		//	ドワーフ男3素手
#define SPR_662sd			106165		//	ドワーフ男3剑
#define SPR_662sf			106166		//	ドワーフ男3杖
#define SPR_662sp			106167		//	ドワーフ男3枪

#define SPR_663ax			106168		//	ドワーフ男4斧
#define SPR_663bw			106169		//	ドワーフ男4弓
#define SPR_663em			106170		//	ドワーフ男4素手
#define SPR_663sd			106171		//	ドワーフ男4剑
#define SPR_663sf			106172		//	ドワーフ男4杖
#define SPR_663sp			106173		//	ドワーフ男4枪

//EX -> XG2
#define SPR_700ax			106250		//	人间A女1斧
#define SPR_700bw			106251		//	人间A女1弓
#define SPR_700em			106252		//	人间A女1素手
#define SPR_700sd			106253		//	人间A女1剑
#define SPR_700sf			106254		//	人间A女1杖
#define SPR_700sp			106255		//	人间A女1枪

#define SPR_701ax			106256		//	人间A女1斧
#define SPR_701bw			106257		//	人间A女1弓
#define SPR_701em			106258		//	人间A女1素手
#define SPR_701sd			106259		//	人间A女1剑
#define SPR_701sf			106260		//	人间A女1杖
#define SPR_701sp			106261		//	人间A女1枪

#define SPR_702ax			106262		//	人间A女1斧
#define SPR_702bw			106263		//	人间A女1弓
#define SPR_702em			106264		//	人间A女1素手
#define SPR_702sd			106265		//	人间A女1剑
#define SPR_702sf			106266		//	人间A女1杖
#define SPR_702sp			106267		//	人间A女1枪

#define SPR_703ax			106268		//	人间A女1斧
#define SPR_703bw			106269		//	人间A女1弓
#define SPR_703em			106270		//	人间A女1素手
#define SPR_703sd			106271		//	人间A女1剑
#define SPR_703sf			106272		//	人间A女1杖
#define SPR_703sp			106273		//	人间A女1枪


#define SPR_710ax			106275		//	人间B女1斧
#define SPR_710bw			106276		//	人间B女1弓
#define SPR_710em			106277		//	人间B女1素手
#define SPR_710sd			106278		//	人间B女1剑
#define SPR_710sf			106279		//	人间B女1杖
#define SPR_710sp			106280		//	人间B女1枪

#define SPR_711ax			106281		//	人间B女1斧
#define SPR_711bw			106282		//	人间B女1弓
#define SPR_711em			106283		//	人间B女1素手
#define SPR_711sd			106284		//	人间B女1剑
#define SPR_711sf			106285		//	人间B女1杖
#define SPR_711sp			106286		//	人间B女1枪

#define SPR_712ax			106287		//	人间B女1斧
#define SPR_712bw			106288		//	人间B女1弓
#define SPR_712em			106289		//	人间B女1素手
#define SPR_712sd			106290		//	人间B女1剑
#define SPR_712sf			106291		//	人间B女1杖
#define SPR_712sp			106292		//	人间B女1枪

#define SPR_713ax			106293		//	人间B女1斧
#define SPR_713bw			106294		//	人间B女1弓
#define SPR_713em			106295		//	人间B女1素手
#define SPR_713sd			106296		//	人间B女1剑
#define SPR_713sf			106297		//	人间B女1杖
#define SPR_713sp			106298		//	人间B女1枪





//XG2新キャラ(女)
#define SPR_720ax			106300		//	人间C女1斧
#define SPR_720bw			106301		//	人间C女1弓
#define SPR_720em			106302		//	人间C女1素手
#define SPR_720sd			106303		//	人间C女1剑
#define SPR_720sf			106304		//	人间C女1杖
#define SPR_720sp			106305		//	人间C女1枪

#define SPR_721ax			106306		//	人间C女2斧
#define SPR_721bw			106307		//	人间C女2弓
#define SPR_721em			106308		//	人间C女2素手
#define SPR_721sd			106309		//	人间C女2剑
#define SPR_721sf			106310		//	人间C女2杖
#define SPR_721sp			106311		//	人间C女2枪

#define SPR_722ax			106312		//	人间C女3斧
#define SPR_722bw			106313		//	人间C女3弓
#define SPR_722em			106314		//	人间C女3素手
#define SPR_722sd			106315		//	人间C女3剑
#define SPR_722sf			106316		//	人间C女3杖
#define SPR_722sp			106317		//	人间C女3枪

#define SPR_723ax			106318		//	人间C女4斧
#define SPR_723bw			106319		//	人间C女4弓
#define SPR_723em			106320		//	人间C女4素手
#define SPR_723sd			106321		//	人间C女4剑
#define SPR_723sf			106322		//	人间C女4杖
#define SPR_723sp			106323		//	人间C女4枪


#define SPR_730ax			106325		//	人间D女1斧
#define SPR_730bw			106326		//	人间D女1弓
#define SPR_730em			106327		//	人间D女1素手
#define SPR_730sd			106328		//	人间D女1剑
#define SPR_730sf			106329		//	人间D女1杖
#define SPR_730sp			106330		//	人间D女1枪

#define SPR_731ax			106331		//	人间D女2斧
#define SPR_731bw			106332		//	人间D女2弓
#define SPR_731em			106333		//	人间D女2素手
#define SPR_731sd			106334		//	人间D女2剑
#define SPR_731sf			106335		//	人间D女2杖
#define SPR_731sp			106336		//	人间D女2枪

#define SPR_732ax			106337		//	人间D女3斧
#define SPR_732bw			106338		//	人间D女3弓
#define SPR_732em			106339		//	人间D女3素手
#define SPR_732sd			106340		//	人间D女3剑
#define SPR_732sf			106341		//	人间D女3杖
#define SPR_732sp			106342		//	人间D女3枪

#define SPR_733ax			106343		//	人间D女4斧
#define SPR_733bw			106344		//	人间D女4弓
#define SPR_733em			106345		//	人间D女4素手
#define SPR_733sd			106346		//	人间D女4剑
#define SPR_733sf			106347		//	人间D女4杖
#define SPR_733sp			106348		//	人间D女4枪


#define SPR_740ax			106350		//	小人女1斧
#define SPR_740bw			106351		//	小人女1弓
#define SPR_740em			106352		//	小人女1素手
#define SPR_740sd			106353		//	小人女11剑
#define SPR_740sf			106354		//	小人女11杖
#define SPR_740sp			106355		//	小人女1枪

#define SPR_741ax			106356		//	小人女2斧
#define SPR_741bw			106357		//	小人女2弓
#define SPR_741em			106358		//	小人女2素手
#define SPR_741sd			106359		//	小人女2剑
#define SPR_741sf			106360		//	小人女2杖
#define SPR_741sp			106361		//	小人女2枪

#define SPR_742ax			106362		//	小人女3斧
#define SPR_742bw			106363		//	小人女3弓
#define SPR_742em			106364		//	小人女3素手
#define SPR_742sd			106365		//	小人女3剑
#define SPR_742sf			106366		//	小人女3杖
#define SPR_742sp			106367		//	小人女3枪

#define SPR_743ax			106368		//	小人女4斧
#define SPR_743bw			106369		//	小人女4弓
#define SPR_743em			106370		//	小人女4素手
#define SPR_743sd			106371		//	小人女4剑
#define SPR_743sf			106372		//	小人女4杖
#define SPR_743sp			106373		//	小人女4枪


#define SPR_750ax			106375		//	エルフ女1斧
#define SPR_750bw			106376		//	エルフ女1弓
#define SPR_750em			106377		//	エルフ女1素手
#define SPR_750sd			106378		//	エルフ女11剑
#define SPR_750sf			106379		//	エルフ女11杖
#define SPR_750sp			106380		//	エルフ女1枪

#define SPR_751ax			106381		//	エルフ女2斧
#define SPR_751bw			106382		//	エルフ女2弓
#define SPR_751em			106383		//	エルフ女2素手
#define SPR_751sd			106384		//	エルフ女2剑
#define SPR_751sf			106385		//	エルフ女2杖
#define SPR_751sp			106386		//	エルフ女2枪

#define SPR_752ax			106387		//	エルフ女3斧
#define SPR_752bw			106388		//	エルフ女3弓
#define SPR_752em			106389		//	エルフ女3素手
#define SPR_752sd			106390		//	エルフ女3剑
#define SPR_752sf			106391		//	エルフ女3杖
#define SPR_752sp			106392		//	エルフ女3枪

#define SPR_753ax			106393		//	エルフ女4斧
#define SPR_753bw			106394		//	エルフ女4弓
#define SPR_753em			106395		//	エルフ女4素手
#define SPR_753sd			106396		//	エルフ女4剑
#define SPR_753sf			106397		//	エルフ女4杖
#define SPR_753sp			106398		//	エルフ女4枪


#define SPR_760ax			106400		//	ドワーフ女1斧
#define SPR_760bw			106401		//	ドワーフ女1弓
#define SPR_760em			106402		//	ドワーフ女1素手
#define SPR_760sd			106403		//	ドワーフ女11剑
#define SPR_760sf			106404		//	ドワーフ女11杖
#define SPR_760sp			106405		//	ドワーフ女1枪

#define SPR_761ax			106406		//	ドワーフ女2斧
#define SPR_761bw			106407		//	ドワーフ女2弓
#define SPR_761em			106408		//	ドワーフ女2素手
#define SPR_761sd			106409		//	ドワーフ女2剑
#define SPR_761sf			106410		//	ドワーフ女2杖
#define SPR_761sp			106411		//	ドワーフ女2枪

#define SPR_762ax			106412		//	ドワーフ女3斧
#define SPR_762bw			106413		//	ドワーフ女3弓
#define SPR_762em			106414		//	ドワーフ女3素手
#define SPR_762sd			106415		//	ドワーフ女3剑
#define SPR_762sf			106416		//	ドワーフ女3杖
#define SPR_762sp			106417		//	ドワーフ女3枪

#define SPR_763ax			106418		//	ドワーフ女4斧
#define SPR_763bw			106419		//	ドワーフ女4弓
#define SPR_763em			106420		//	ドワーフ女4素手
#define SPR_763sd			106421		//	ドワーフ女4剑
#define SPR_763sf			106422		//	ドワーフ女4杖
#define SPR_763sp			106423		//	ドワーフ女4枪



//XG2管理キャラ
#define SPR_800ax			106425		//	管理人间男A斧
#define SPR_800bw			106426		//	管理人间男A弓
#define SPR_800em			106427		//	管理人间男A素手
#define SPR_800sd			106428		//	管理人间男A剑
#define SPR_800sf			106429		//	管理人间男A杖
#define SPR_800sp			106430		//	管理人间男A枪

#define SPR_801ax			106450		//	管理人间男B斧
#define SPR_801bw			106451		//	管理人间男B弓
#define SPR_801em			106452		//	管理人间男B素手
#define SPR_801sd			106453		//	管理人间男B剑
#define SPR_801sf			106454		//	管理人间男B杖
#define SPR_801sp			106455		//	管理人间男B枪

#define SPR_802ax			106475		//	管理人间男C斧
#define SPR_802bw			106476		//	管理人间男C弓
#define SPR_802em			106477		//	管理人间男C素手
#define SPR_802sd			106478		//	管理人间男C剑
#define SPR_802sf			106479		//	管理人间男C杖
#define SPR_802sp			106480		//	管理人间男C枪

#define SPR_803ax			106500		//	管理人间男D斧
#define SPR_803bw			106501		//	管理人间男D弓
#define SPR_803em			106502		//	管理人间男D素手
#define SPR_803sd			106503		//	管理人间男D剑
#define SPR_803sf			106504		//	管理人间男D杖
#define SPR_803sp			106505		//	管理人间男D枪


#define SPR_804ax			106525		//	管理人间小人D斧
#define SPR_804bw			106526		//	管理人间小人D弓
#define SPR_804em			106527		//	管理人间小人D素手
#define SPR_804sd			106528		//	管理人间小人D剑
#define SPR_804sf			106529		//	管理人间小人D杖
#define SPR_804sp			106530		//	管理人间小人D枪


#define SPR_805ax			106550		//	管理人间エルフD斧
#define SPR_805bw			106551		//	管理人间エルフD弓
#define SPR_805em			106552		//	管理人间エルフD素手
#define SPR_805sd			106553		//	管理人间エルフD剑
#define SPR_805sf			106554		//	管理人间エルフD杖
#define SPR_805sp			106555		//	管理人间エルフD枪

#define SPR_806ax			106575		//	管理人间ドワーフD斧
#define SPR_806bw			106576		//	管理人间ドワーフD弓
#define SPR_806em			106577		//	管理人间ドワーフD素手
#define SPR_806sd			106578		//	管理人间ドワーフD剑
#define SPR_806sf			106579		//	管理人间ドワーフD杖
#define SPR_806sp			106580		//	管理人间ドワーフD枪


#define SPR_807ax			106600		//	管理人间女A斧
#define SPR_807bw			106601		//	管理人间女A弓
#define SPR_807em			106602		//	管理人间女A素手
#define SPR_807sd			106603		//	管理人间女A剑
#define SPR_807sf			106604		//	管理人间女A杖
#define SPR_807sp			106605		//	管理人间女A枪

#define SPR_808ax			106625		//	管理人间女B斧
#define SPR_808bw			106626		//	管理人间女B弓
#define SPR_808em			106627		//	管理人间女B素手
#define SPR_808sd			106628		//	管理人间女B剑
#define SPR_808sf			106629		//	管理人间女B杖
#define SPR_808sp			106630		//	管理人间女B枪

#define SPR_809ax			106650		//	管理人间女C斧
#define SPR_809bw			106651		//	管理人间女C弓
#define SPR_809em			106652		//	管理人间女C素手
#define SPR_809sd			106653		//	管理人间女C剑
#define SPR_809sf			106654		//	管理人间女C杖
#define SPR_809sp			106655		//	管理人间女C枪

#define SPR_810ax			106675		//	管理人间女D斧
#define SPR_810bw			106676		//	管理人间女D弓
#define SPR_810em			106677		//	管理人间女D素手
#define SPR_810sd			106678		//	管理人间女D剑
#define SPR_810sf			106679		//	管理人间女D杖
#define SPR_810sp			106680		//	管理人间女D枪


#define SPR_811ax			106700		//	管理人间小人D女斧
#define SPR_811bw			106701		//	管理人间小人D女弓
#define SPR_811em			106702		//	管理人间小人D女素手
#define SPR_811sd			106703		//	管理人间小人D女剑
#define SPR_811sf			106704		//	管理人间小人D女杖
#define SPR_811sp			106705		//	管理人间小人D女枪

#define SPR_812ax			106725		//	管理人间エルフD女斧
#define SPR_812bw			106726		//	管理人间エルフD女弓
#define SPR_812em			106727		//	管理人间エルフD女素手
#define SPR_812sd			106728		//	管理人间エルフD女剑
#define SPR_812sf			106729		//	管理人间エルフD女杖
#define SPR_812sp			106730		//	管理人间エルフD女枪

#define SPR_813ax			106750		//	管理人间ドワーフD女斧
#define SPR_813bw			106751		//	管理人间ドワーフD女弓
#define SPR_813em			106752		//	管理人间ドワーフD女素手
#define SPR_813sd			106753		//	管理人间ドワーフD女剑
#define SPR_813sf			106754		//	管理人间ドワーフD女杖
#define SPR_813sp			106755		//	管理人间ドワーフD女枪

#define SPRPC_END_V2		106800		// ＰＣキャラ終了番号


//台服新人物编号
#define TW_SPR_100ax            105500
#define TW_SPR_100bw            105501
#define TW_SPR_100em            105502
#define TW_SPR_100sd            105503
#define TW_SPR_100sf            105504
#define TW_SPR_100sp            105505

#define TW_SPR_101ax            105525
#define TW_SPR_101bw            105526
#define TW_SPR_101em            105527
#define TW_SPR_101sd            105528
#define TW_SPR_101sf            105529
#define TW_SPR_101sp            105530

#define TW_SPR_102ax            104400
#define TW_SPR_102bw            104401
#define TW_SPR_102em            104402
#define TW_SPR_102sd            104403
#define TW_SPR_102sf            104404
#define TW_SPR_102sp            104405

#define TW_SPR_103ax            104430
#define TW_SPR_103bw            104431
#define TW_SPR_103em            104432
#define TW_SPR_103sd            104433
#define TW_SPR_103sf            104434
#define TW_SPR_103sp            104435

#define TW_SPR_104ax            104460
#define TW_SPR_104bw            104461
#define TW_SPR_104em            104462
#define TW_SPR_104sd            104463
#define TW_SPR_104sf            104464
#define TW_SPR_104sp            104465

#define TW_SPR_105ax            104490
#define TW_SPR_105bw            104491
#define TW_SPR_105em            104492
#define TW_SPR_105sd            104493
#define TW_SPR_105sf            104494
#define TW_SPR_105sp            104495

#define TW_SPR_106ax            120100
#define TW_SPR_106bw            120101
#define TW_SPR_106em            120102
#define TW_SPR_106sd            120103
#define TW_SPR_106sf            120104
#define TW_SPR_106sp            120105

#define TW_SPR_107ax            120124
#define TW_SPR_107bw            120125
#define TW_SPR_107em            120126
#define TW_SPR_107sd            120127
#define TW_SPR_107sf            120128
#define TW_SPR_107sp            120129


// Ｖ２用キャラクタ颜グラフィック番号 ---------------------------

#define RN_FACE_0					202800	// 人间男Ａ（セディ）
#define RN_FACE_1					202900	// 人间男Ｂ（カズ）
#define RN_FACE_2					203000	// 人间男Ｃ（シン）
#define RN_FACE_3					203100	// 人间男Ｄ（ゲン）
#define RN_FACE_4					203200	// 小人男　（トブ）
#define RN_FACE_5					203300	// ドワーフ男（ベイ）
#define RN_FACE_6					203400	// エルフ男（ボグ）
#define RN_FACE_7					203500	// 人间女Ａ ()
#define RN_FACE_8					203600	// 人间女Ｂ ()
#define RN_FACE_9					203700	// 人间女Ｃ ()
#define RN_FACE_10					203800	// 人间女Ｄ ()
#define RN_FACE_11					203900	// 小人女Ａ ()
#define RN_FACE_12					204000	// ドワーフ女 ()
#define RN_FACE_13					204100	// エルフ女 ()


#define V2_FACE_0					200000	// 人间男Ａ（セディ）
#define V2_FACE_1					200200	// 人间男Ｂ（カズ）
#define V2_FACE_2					200800	// 人间男Ｃ（シン）
#define V2_FACE_3					201000	// 人间男Ｄ（ゲン）
#define V2_FACE_4					201200	// 小人男　（トブ）
#define V2_FACE_5					201400	// ドワーフ男（ベイ）
#define V2_FACE_6					201600	// エルフ男（ボグ）
#define V2_FACE_7					200400	// 人间女Ａ ()
#define V2_FACE_8					200600	// 人间女Ｂ ()
#define V2_FACE_9					201800	// 人间女Ｃ ()
#define V2_FACE_10					202000	// 人间女Ｄ ()
#define V2_FACE_11					202200	// 小人女Ａ ()
#define V2_FACE_12					202400	// ドワーフ女 ()
#define V2_FACE_13					202600	// エルフ女 ()



#define V2_FACE_UP_0	22611		// 颜アップ（セディ）
#define V2_FACE_UP_1	22612		// 颜アップ（ピート）
#define V2_FACE_UP_2	22613		// 颜アップ（サイゾウ）
#define V2_FACE_UP_3	22614		// 颜アップ（ネルソン）
#define V2_FACE_UP_4	22615		// 颜アップ（ペティット）
#define V2_FACE_UP_5	22616		// 颜アップ（ランスロット）
#define V2_FACE_UP_6	22617		// 颜アップ（ウィスケルス）

#define V2_FACE_UP_7	22618		// 颜アップ（セーラ）
#define V2_FACE_UP_8	22619		// 颜アップ（あやめ）
#define V2_FACE_UP_9	22644		// 颜アップ（フルーティア）
#define V2_FACE_UP_10	22645		// 颜アップ（シャハラ）
#define V2_FACE_UP_11	22646		// 颜アップ（ピピン）
#define V2_FACE_UP_12	22647		// 颜アップ（グレイス）
#define V2_FACE_UP_13	22648		// 颜アップ（ホミ）

#define CG_FACE_UP_0	22649		// 颜アップ（バウ）
#define CG_FACE_UP_1	23007		// 颜アップ（カズ）
#define CG_FACE_UP_2	23008		// 颜アップ（シン）
#define CG_FACE_UP_3	23009		// 颜アップ（トブ）
#define CG_FACE_UP_4	23010		// 颜アップ（ゲン）
#define CG_FACE_UP_5	23011		// 颜アップ（ベイ）
#define CG_FACE_UP_6	23012		// 颜アップ（ボグ）

#define CG_FACE_UP_7	23013		// 颜アップ（ウル）
#define CG_FACE_UP_8	23014		// 颜アップ（モエ）
#define CG_FACE_UP_9	23015		// 颜アップ（アミ）
#define CG_FACE_UP_10	23016		// 颜アップ（メグ）
#define CG_FACE_UP_11	23017		// 颜アップ（レイ）
#define CG_FACE_UP_12	23018		// 颜アップ（ケイ）
#define CG_FACE_UP_13	23019		// 颜アップ（エル）


//------------ 新规キャラ选择 -------------------
#define CG_CHR_MAKE_SEL_WINDOW_V2		22219	// 新规キャラ选择画面Ｖ２
#define CG_CHR_MAKE_SEL_WINDOW_V2_N		22296	// 新规キャラ选择画面Ｖ２通常
#define CG_FACE_UPUNDER					22295	// バージョン２用下敷き（エル）
#define V2_CHR_MAKESEL_CHANGE			22293	// ページ变更ボタン
#define V2_CHR_MAKE_SEL_BACK			22292	// BACKボタン凹 Ｖ２
#define V2_CHR_MAKE_SEL_OK				22294	// ＯＫボタン凹 Ｖ２

#define V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_1	22154	// 新キャラ选择画面色左矢印
#define V2_CHR_MAKE_SEL_COLOR_LEFT_BTN_2	22155	// 新キャラ选择画面色左矢印
#define V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_1	22156	// 新キャラ选择画面色右矢印
#define V2_CHR_MAKE_SEL_COLOR_RIGHT_BTN_2	22157	// 新キャラ选择画面色右矢印

#define V2_CHR_MAKE_SEL_UNDERSHEET_1		22198	// 新キャラ用下敷き
#define V2_CHR_MAKE_SEL_UNDERSHEET_2		22199	// 旧キャラ用下敷き




// ID & PASS 入力关连 -------------------------------------
#define EX_ID_PASS_WINDOW					22212	// ID & PASS 入力画面 EX
#define V2_ID_PASS_WINDOW					22311	// ID & PASS 入力画面 V2

#define EX_ID_PASS_OK						22238	// OKボタン
#define EX_ID_PASS_QUIT						22239	// QUITボタン
#define EX_CHR_SEL_ICO						22216	// キャラリスト画面にのせるアイコン


#define V2_IDPASS_SANDCLOCK					22312	// 砂时计表示
#define V2_IDPASS_XG2LOGO					22313	// Ｖ２のロゴ

#endif
