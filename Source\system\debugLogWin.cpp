﻿// リリース版でrecvdata.txtが生成されるようするための变更
//// #ifdef _DEBUG
#if defined(_DEBUG)||defined(_RELDEB)

#include<windows.h>
#include<commctrl.h>
#include<richedit.h>
#include"..\resource.h"

#include<stdio.h>
#include<string.h>

#include"../systeminc/system.h"

#include"..\systeminc\debugLogWin.h"
#include"..\systeminc\tool.h"
#include"..\systeminc\netmain.h"
#include"..\systeminc\main.h"
#include"..\systeminc\mouse.h"
#include"..\systeminc\netmain.h"
#include"..\systeminc\nrproto_util.h"


HWND hLogWindow = NULL;		// リアルタイムログウィンドウのハンドル
HWND hLogList = NULL;		// ログリストのビュアーのハンドル
HFONT hLogFont = NULL;		// ログのフォントのハンドル

HINSTANCE hRichedLib = NULL;	// リッチエディットＤＬＬのハンドル

int logWinX = 0;
int logWinY = 505;

#define MAX_LINE_CHAR	1023 // 入力行の最大文字数

char logFileName[256];		// ログファイル名

char sjisMsg[8192];			// EUCからS-JISに变换した文字列

#if 0
char sendHisMsg[10][1024];	// 送信文字列の履历
int hisMsg;

unsigned int upKey = 0;
unsigned int downKey = 0;
#endif
#ifdef PUK3_PROTO_RECVTIME
	extern int RunCnt;
#endif


#ifdef PUK2
void updateLogMessage( int flg, char *msg )
{
#ifdef PUK3_PROTO_RECVTIME
	static int befcnt = -1;

	if ( RunCnt != befcnt ){
		char buf[256];

		sprintf( buf, "# %d #", RunCnt );                                   //MLHIDE

		// ログファイルに书きこむ
		writeLogFile( buf );
	
		// ウィンドウに表示
		appendLogMessage( buf );

		befcnt = RunCnt;
	}
#endif
	// 文字コードの变换
	if( strlen( msg ) < sizeof( sjisMsg ) )
	{
		// どっちからどっちへのデータか
		if( flg ){
			// 元のバッファを破坏しない样にコピー
			sprintf( sjisMsg, "->%s", msg );                                   //MLHIDE
		}else{
			// 元のバッファを破坏しない样にコピー
			strcpy( sjisMsg, msg );
		}
		// 文字列をEUCからSJISに变换
		eucStringToSjisString( sjisMsg );
		// 文字列をEUCからSJISに变换
		//sjisStringToEucString( sjisMsg );
	}
	else
	{
		sprintf( sjisMsg, "--- バッファが足りないのよ(sjisMsg[]) ---\n" );             //MLHIDE
	}

	// ログファイルに书きこむ
	writeLogFile( sjisMsg );

	// ウィンドウに表示
	appendLogMessage( sjisMsg );
}
#else
void updateLogMessage( char *msg )
{
	// 文字コードの变换
	if( strlen( msg ) < sizeof( sjisMsg ) )
	{
		// 元のバッファを破坏しない样にコピー
		strcpy( sjisMsg, msg );
		// 文字列をEUCからSJISに变换
		eucStringToSjisString( sjisMsg );
		// 文字列をEUCからSJISに变换
		//sjisStringToEucString( sjisMsg );
	}
	else
	{
		sprintf( sjisMsg, "--- バッファが足りないのよ(sjisMsg[]) ---\n" );             //MLHIDE
	}


	// ログファイルに书きこむ
	writeLogFile( sjisMsg );


	// ウィンドウに表示
	appendLogMessage( sjisMsg );
}
#endif

LRESULT CALLBACK logWindowProc( HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam )
{
	switch( message )
	{
		// 初期化
		case WM_INITDIALOG:
#if 0
			strcpy( sendHisMsg[0], "TEST1" );                                  //MLHIDE
			strcpy( sendHisMsg[1], "TEST2" );                                  //MLHIDE
			strcpy( sendHisMsg[2], "TEST3" );                                  //MLHIDE
			strcpy( sendHisMsg[3], "TEST4" );                                  //MLHIDE
			hisMsg = -1;
			upKey = 0;
			downKey = 0;
#endif
			break;

		case WM_MOUSEMOVE:		// マウスを动かした时
		case WM_NCMOUSEMOVE:	// マウスがウィンドウからはみ出た时
			if( mouse.flag == FALSE )
			{
				ShowCursor( TRUE ); // マウスカーソルを表示
				mouse.flag = TRUE;
			}
			return TRUE;

		case WM_COMMAND:
			switch( LOWORD( wParam ) )
			{
				case IDC_BUTTON1:
					sendNetworkMessage();
					return TRUE;
			}
			break;

		case WM_SIZE:
			if( wParam == SIZE_MINIMIZED )
			{
				SetFocus( hWnd );
			}
			break;



#if 0
      	case WM_KEYDOWN:	// キー入力处理
      		siwtch( wParam )
      		{
      			case VK_UP:
					upKey = 1;
      				return TRUE;
      			case VK_DOWN:
					downKey = 1;
      				return TRUE;
      		}
      		break;

      	case WM_KEYUP:		// キー入力处理
      		break;
#endif
	}

	return 0;
}


// リアルタイムログウィンドウを表示する
HWND openLogWindow( void )
{
	HWND hDlg;		// 作成したダイアログのハンドル

	RECT rect;		// 现在のダイアログの位置とサイズ
	RECT newRect;	// 移动后のダイアログの位置とサイズ


	// リッチエディットＤＬＬの読み込み
	hRichedLib = LoadLibrary( "RICHED32.DLL" );                          //MLHIDE


	hDlg = CreateDialog( hInst, MAKEINTRESOURCE( IDD_DIALOG1 ),
			hWnd, (DLGPROC)logWindowProc );

	if( hDlg == NULL )
	{
		return NULL;
	}

	hLogWindow = hDlg;

	// メインダイアログを画面中心へ移动
	GetWindowRect( hDlg, &rect );	// ダイアログの位置取得

	newRect.top    = logWinY;	// 位置ずらし计算
	newRect.left   = logWinX;
	newRect.bottom = rect.bottom - rect.top;
	newRect.right  = rect.right - rect.left;

	MoveWindow( hDlg,
		newRect.left, newRect.top, newRect.right, newRect.bottom, TRUE );

	// ログのリストビュアーのハンドル取得
	hLogList = GetDlgItem( hDlg, IDC_RICHEDIT1 );
	// フォント变更
	setFont( hLogList );

	SendDlgItemMessage( hDlg, IDC_EDIT1, EM_SETLIMITTEXT, MAX_LINE_CHAR, 0 );

	// ダイアログ表示
	UpdateWindow( hDlg );

	sjisMsg[0] = 0;

	return hDlg;
}


// リアルタイムログウィンドウを抹消する
void closeLogWindow( void )
{
	// フォントの削除
	if( hLogFont )
	{
		DeleteObject( hLogFont );
		hLogFont = NULL;
	}
	// ダイアログの削除
	if( hLogWindow )
	{
		DestroyWindow( hLogWindow );
		hLogWindow = NULL;
	}
	// 
	if( hRichedLib )
	{
		FreeLibrary( hRichedLib );
		hRichedLib = NULL;
	}
}


// ログのリストビュアーに文字列を追加する
void appendLogMessage( char *msg )
{
	char *CRLF = "\n";                                                   //MLHIDE
	int len;

	if( !hLogList )
		return;

	// バッファの最大サイズを取り出す
	len = SendMessage( hLogList, EM_GETLIMITTEXT, 0, 0 );
	// キャレット（カーソル）をバッファの最后に移动する
	SendMessage( hLogList, EM_SETSEL, len, len );
	// 文字列をバッファに送る
	SendMessage( hLogList, EM_REPLACESEL, FALSE, (LPARAM)msg );
	// 改行コードもバッファに送る
	SendMessage( hLogList, EM_REPLACESEL, FALSE, (LPARAM)CRLF );
	// キャレットに位置にスクロールさせる
	SendMessage( hLogList, EM_SCROLLCARET, 0, 0 );

	// ダイアログ表示
	UpdateWindow( hLogWindow );
}


// 入力文字列をサーバに送る
void sendNetworkMessage( void )
{
	char msg[MAX_LINE_CHAR+1];

	if( !hLogWindow )
		return;

	GetDlgItemText( hLogWindow, IDC_EDIT1, msg, MAX_LINE_CHAR );

	if( strlen( msg ) == 0 )
		return;

	nrproto_CreateHeader( nrproto.work, msg );
	nrproto_Send( sockfd, nrproto.work );

	SetDlgItemText( hLogWindow, IDC_EDIT1, "" );
}


// フォントの设定
void setFont( HWND hWnd )
{
	if( !hWnd )
		return;

	hLogFont = CreateFont( 12, 0, 0, 0, FW_NORMAL, FALSE, FALSE, 0,
				GB2312_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
#if 1
				DEFAULT_QUALITY, DEFAULT_PITCH, "宋体" );                           //MLHIDE
#else
				DEFAULT_QUALITY, DEFAULT_PITCH, "FixedSys" );                     //MLHIDE
#endif
	SendMessage( hWnd, WM_SETFONT, (WPARAM)hLogFont, MAKELPARAM( FALSE, 0 ) );
}


/////////////////////////////////////////////////////////////////////
// ログファイルに关する处理

// ログファイルに信息を追加
void writeLogFile( char *msg )
{
	FILE *fp;

	// ファイル名がないなら終わる
	if( strlen( logFileName ) == 0 )
		return;

	// ファイルを开く
	if( (fp = fopen( logFileName, "at" )) )                              //MLHIDE
	{
		// ファイルに出力
		fprintf( fp, "%s\n", msg );                                         //MLHIDE
		// ファイル关闭
		fclose( fp );
	}
}


// ログファイルを空にし、ファイル名を记忆
void clearLogFile( char *name )
{
	FILE *fp;

	// ログファイル名を记忆
	strcpy( logFileName, name );

	// ファイル名がないなら終わる
	if( strlen( logFileName ) == 0 )
		return;

	if( (fp = fopen( logFileName, "w" )) )                               //MLHIDE
	{
#ifdef PUK3_EXETYPE_LOG
	#if defined(_CGXL)
		writeLogFile( "# ExeType CgXL" );                                   //MLHIDE
	#elif defined(_CGL)
		writeLogFile( "# ExeType CgL" );                                    //MLHIDE
	#elif defined(_CGS)
		writeLogFile( "# ExeType CgS" );                                    //MLHIDE
	#elif defined(_RELDEB)
		writeLogFile( "# ExeType CgRelDeb" );                               //MLHIDE
	#endif
	#if defined(_DEBUG)
		writeLogFile( "# DebugExe" );                                       //MLHIDE
	#endif
#endif
		fclose( fp );
	}
}

#ifdef PUK2

void loadLogFile( void )
{
	FILE *fp;
	long siz;
	char *msg, *bp, *p;

	if( !hLogList ) return;

	// ファイル名がないなら終わる
	if( strlen( logFileName ) == 0 ) return;

	// ファイルを开く
	if( ( fp=fopen(logFileName,"rt") ) ){                                //MLHIDE
		fseek( fp, 0, SEEK_END );
		siz = ftell(fp);
		msg = (char *)malloc(siz+1);
#ifdef PUK2_MEMCHECK
		memlistset( msg, MEMLISTTYPE_LOADLOGFILE );
#endif

		fseek( fp, 0, SEEK_SET );

		siz = fread( msg, 1, siz, fp );
		msg[siz] = '\0';

		// ファイル关闭
		fclose( fp );

		bp = msg;
		while( p = strchr( bp, '\n' ) ){
			p[0] = '\0';

			// ウィンドウに出力
			appendLogMessage(bp);

			bp = p + 1;
		}
		// ウィンドウに出力
		if ( bp[0]!='\0' ) appendLogMessage(bp);

#ifdef PUK2_MEMCHECK
		memlistrel( msg, MEMLISTTYPE_LOADLOGFILE );
#endif
		free(msg);
	}
}

#endif

#endif
