﻿/**** SYSTEM INCLUDE ****/
#include<stdlib.h>
#include<stdio.h>
#include<time.h>
#include<direct.h>
#include<errno.h>

#include "../systeminc/version.h"
#include "../systeminc/system.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/direct3D.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/map.h"
#include "../systeminc/main.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/mapEffect.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/filetbl.h"

//#define PAL_CHANGE_INTERVAL_WIN		120		// パレット变更の间隔（ウィンドウモード）
//#define PAL_CHANGE_INTERVAL_WIN		10		// パレット变更の间隔（ウィンドウモード）
#define PAL_CHANGE_INTERVAL_WIN		2		// パレット变更の间隔（ウィンドウモード）
#define PAL_CHANGE_INTERVAL_FULL	1		// パレット变更の间隔（フルスクリーンモード）

//---------------------------------------------------------------------------//
// グローバル变数定义                                                        //
//---------------------------------------------------------------------------//
DIRECT_DRAW	*lpDraw;		// DirectDrawオブジェクトポインタ
BOOL DDinitFlag = FALSE;	// DirectDraw初期化フラグ

PALETTEENTRY	Palette[256];	// パレット构造体
#ifdef PUK2
PALETTEENTRY	DefPalette[256];	// パレット构造体
#endif
PALETTE_STATE 	PalState;		// パレット状态构造体
BOOL 			PalChangeFlag;	// パレットチェンジフラグ
BOOL			palChageStatus;	// パレットチェンジ中か？

int displayBpp = 8;			// 画面色数
int rBitMask;				// Ｒビットのマスク值
int gBitMask;				// Ｇビットのマスク值
int bBitMask;				// Ｂビットのマスク值

int rBitLShift;				// Ｒビットの左シフト值
int gBitLShift;				// Ｇビットの左シフト值
int bBitLShift;				// Ｂビットの左シフト值

int rBitRShift;				// Ｒビットの右シフト值
int gBitRShift;				// Ｇビットの右シフト值
int bBitRShift;				// Ｂビットの右シフト值

void make24BitColorPalette( void );

typedef struct {
	unsigned char b,g,r;
}COL24BIT;
COL24BIT Color24Palette[256];
unsigned short highColorPalette[256];
unsigned long  fullColorPalette[256];
#ifdef PUK2
	PALDATA PDpal[256];
#endif

int FlipCnt; // フリップカウンター

short RasterNoWaitMode = 0;

typedef struct {
	short version;	// 使用するバージョン
	char name[128];	// ファイル名
}PALFILE;
// パレットファイルネーム
//char *palFileName[] =
PALFILE PalFile[] =
{
	#include "../systeminc/palName.h"
	#ifdef PUK2
		{ 0, "pal\\palet_10.cgp" },	// 黄	32                                 //MLHIDE
		{ 0, "pal\\palet_06.cgp" },	// 青	33                                 //MLHIDE
		{ 0, "pal\\palet_08.cgp" },	// 赤	34                                 //MLHIDE
		{ 0, "pal\\palet_04.cgp" },	// 緑	35                                 //MLHIDE

		{ 0, "pal\\palet_14.cgp" },	// セピア	36                               //MLHIDE
		{ 0, "pal\\palet_15.cgp" },	// モノクロ	37                              //MLHIDE

		{ 0, "pal\\palet_e_s.cgp" },	// スクウェアエニックスロゴ	38                     //MLHIDE
	#endif
};

// ある值の立っているビットの数を调べる
int getBitCount( int bit );

#ifdef PUK2
	#include "../puk2/newDraw/directdraw3D.cpp"
#endif

/* DirectDraw 初期化 **********************************************************/

#ifndef PUK2

BOOL InitDirectDraw( void )
{

	DDSCAPS ddscaps;	// ハードウェア能力构造体

//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
	// 画面解像度（BPP）を调べる
	HDC hDcDest = GetDC( hWnd );
	displayBpp = GetDeviceCaps( hDcDest, BITSPIXEL ); // ビットの深さを取得
	ReleaseDC( hWnd, hDcDest );
#endif

	// DirectDraw管理构造体实体确保
	lpDraw = ( DIRECT_DRAW *)GlobalAllocPtr( GPTR, ( DWORD )sizeof( DIRECT_DRAW ) );
	if( lpDraw == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_61, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
		MessageBox( hWnd, "GlobalAllocPtr Error ( DIRECT_DRAW )", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
		return FALSE;
	}
#ifdef PUK2_MEMCHECK
		memlistset( lpDraw, MEMLISTTYPE_STRUCT_DIRECT_DRAW );
#endif
	
	// ウィンドウサイズ
	lpDraw->xSize = DEF_APPSIZEX;
	lpDraw->ySize = DEF_APPSIZEY;
	
	// サーフェスサイズ
	SurfaceSizeX = SURFACE_WIDTH;
	SurfaceSizeY = SURFACE_HEIGHT;
	
	// DirectDrawの初期化处理 失败したら NULL を返す
	if( DirectDrawCreate( NULL, &lpDraw->lpDD, NULL ) != DD_OK )
	{
		if( DirectDrawCreate( (GUID *)DDCREATE_EMULATIONONLY, &lpDraw->lpDD, NULL ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_62, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "DirectDrawCreate Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
			return FALSE;
		}
	}

	// DirectDraw2扩张モードへ移行
	if( lpDraw->lpDD->QueryInterface( IID_IDirectDraw2, (LPVOID *)&lpDraw->lpDD2 ) != DD_OK )
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_63, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
		MessageBox( hWnd, "QueryInterface Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
		// 古いDirectDrawを开放する
		lpDraw->lpDD->Release();
		return FALSE;
	}
	
	// 古いDirectDrawを开放する
	lpDraw->lpDD->Release();

////// ウィンドウモードの时 //////////////////////////////////////////////////
	if( WindowMode ){
		// このアプリケーションの权限等级を设定(ウィンドウモード）
		if( lpDraw->lpDD2->SetCooperativeLevel( hWnd ,DDSCL_NORMAL ) != DD_OK )
		//if( lpDraw->lpDD->SetCooperativeLevel( hWnd ,DDSCL_NORMAL ) != DD_OK )
		// このアプリケーションの权限等级を设定(排他等级 & フルスクリーン画面）
		//if( lpDraw->lpDD2->SetCooperativeLevel( hWnd, DDSCL_EXCLUSIVE | DDSCL_FULLSCREEN ) != DD_OK )
		//if( lpDraw->lpDD2->SetCooperativeLevel( hWnd, DDSCL_EXCLUSIVE ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_64, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "SetCooperativeLevel Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
			return FALSE;
		}
		
		// ディスプレイ表示モードを变更
		//lpDraw->lpDD2->SetDisplayMode( 1152, 864, 8 ,0 ,0 );
		//lpDraw->lpDD2->SetDisplayMode( 640, 480, 8 ,0 ,0 );
		//Modfied by LitChi
		//lpDraw->lpDD2->SetDisplayMode( 1024, 768, 8 ,0 ,0 );

		// 表(プライマリ)バッファの初期化
		ZeroMemory( &lpDraw->ddsd, sizeof( lpDraw->ddsd ) ); 
		lpDraw->ddsd.dwSize = sizeof( lpDraw->ddsd );
#if 0
		{	// オーバーレイ对应チェック
			HRESULT ret;
			ret = lpDraw->lpDD2->GetCaps( (DDCAPS_DX6*)&ddscaps, NULL );
			if( ret != DD_OK || !( ddscaps.dwCaps & DDCAPS_OVERLAY ) ){
				MessageBox( hWnd, "Overlay不匹配", "确认", MB_OK | MB_ICONSTOP );      //MLHIDE
			}else{ 
				MessageBox( hWnd, "Overlay匹配", "确认", MB_OK | MB_ICONSTOP );       //MLHIDE
			}
		}
#endif
		// ddsCaps构造体を有效化 & dwBackBufferCount变数を有效
		lpDraw->ddsd.dwFlags = DDSD_CAPS;
		
		// プライマリサーフェス(表画面)として设定 ,このサーフェスがFlipを使用できる。,复数のサーフェスの记述を有效 
		//lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE | DDSCAPS_3DDEVICE;
		lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE;
		
		// プライマリーサーフェスの作成
		if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpDraw->lpFRONTBUFFER, NULL ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_65, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "主Surface生成失败。", "确认", MB_OK | MB_ICONSTOP );    //MLHIDE
#endif
			return FALSE;
		}
		
		// クリッ布の作成
		if( lpDraw->lpDD2->CreateClipper( 0,&lpDraw->lpCLIPPER, NULL ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_66, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "Clipper生成失败。", "确认", MB_OK | MB_ICONSTOP );     //MLHIDE
#endif
			return FALSE;
		}
		// クリッ布にウィンドウのハンドルをセットする
		lpDraw->lpCLIPPER->SetHWnd( 0, hWnd );
		// プライマリサーフェスにクリッ布を关连付ける
		lpDraw->lpFRONTBUFFER->SetClipper( lpDraw->lpCLIPPER );
		
		// 里(バック)バッファの初期化
		ZeroMemory( &lpDraw->ddsd, sizeof( lpDraw->ddsd ) ); 
		lpDraw->ddsd.dwSize = sizeof( lpDraw->ddsd );
        lpDraw->ddsd.dwFlags           = DDSD_CAPS | DDSD_WIDTH | DDSD_HEIGHT;
        
#ifdef DIRECT3D_ON
       	lpDraw->ddsd.ddsCaps.dwCaps    = DDSCAPS_OFFSCREENPLAIN | DDSCAPS_3DDEVICE;
#else 
       	lpDraw->ddsd.ddsCaps.dwCaps    = DDSCAPS_OFFSCREENPLAIN;
#endif
       	lpDraw->ddsd.dwWidth           = lpDraw->xSize;
       	lpDraw->ddsd.dwHeight          = lpDraw->ySize;
       	if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpDraw->lpBACKBUFFER, NULL ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_67, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "BackBuffer生成失白。", "确认", MB_OK | MB_ICONSTOP );  //MLHIDE
#endif
			return FALSE;
		}
		//MessageBox( hWnd, "DirectDraw 初始化结束", "确认", MB_OK | MB_ICONSTOP );
		
	
////// フルスクリーンモードの时 //////////////////////////////////////////////
	}else{
		// このアプリケーションの权限等级を设定(排他等级 & フルスクリーン画面）
		//if( lpDraw->lpDD2->SetCooperativeLevel( hWnd, DDSCL_EXCLUSIVE | DDSCL_FULLSCREEN ) != DD_OK )
		if( lpDraw->lpDD2->SetCooperativeLevel( hWnd, DDSCL_EXCLUSIVE | DDSCL_FULLSCREEN | DDSCL_ALLOWMODEX ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_68, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "SetCooperativeLevel Error", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
#endif
			return FALSE;
		}
//#ifdef _DEBUG
		// ディスプレイ表示モードを变更
		//lpDraw->lpDD2->SetDisplayMode( lpDraw->xSize, lpDraw->ySize, displayBpp ,0 ,0 );
//#else
		displayBpp = 8;
		lpDraw->lpDD2->SetDisplayMode( lpDraw->xSize, lpDraw->ySize, 8 ,0 ,0 );
//#endif
		//lpDraw->lpDD2->SetDisplayMode( 320,240, 8 ,0 ,0 );
		
		// 表(プライマリ)バッファの初期化
		// ddsd 构造体の内容をゼロクリアする
		ZeroMemory( &lpDraw->ddsd, sizeof( lpDraw->ddsd ) );
		
		// ddsd 构造体に作成するサーフェスの情报を格纳する
		// ddsd 构造体のサイズの指定（ バージョンアップに对应させるため ）
		lpDraw->ddsd.dwSize = sizeof( lpDraw->ddsd );
		
		// 格纳するデータの种类の设定
		// ddsCaps构造体を有效化 & dwBackBufferCount变数を有效にする
		lpDraw->ddsd.dwFlags = DDSD_CAPS | DDSD_BACKBUFFERCOUNT;
		
		// プライマリサーフェス(表画面)として设定 ,このサーフェスがFlipを使用できる。,复数のサーフェスの记述を有效 
#ifdef DIRECT3D_ON
		lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE | DDSCAPS_FLIP | DDSCAPS_COMPLEX | DDSCAPS_3DDEVICE;
#else		
		lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE | DDSCAPS_FLIP | DDSCAPS_COMPLEX;
#endif		
		// バックバッファ数を１つセット
		lpDraw->ddsd.dwBackBufferCount = 1;
		
		// プライマリーサーフェスの实作成处理
		if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpDraw->lpFRONTBUFFER, NULL ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_69, "确认", MB_OK | MB_ICONSTOP );          //MLHIDE
#else
			MessageBox( hWnd, "主Surface生成失败。", "确认", MB_OK | MB_ICONSTOP );    //MLHIDE
#endif
			return FALSE;
		}
		
		// 里バッファの初期化
		ddscaps.dwCaps = DDSCAPS_BACKBUFFER;
		// 里バッファをプライマリサーフェスに接続
		lpDraw->lpFRONTBUFFER->GetAttachedSurface( &ddscaps, &lpDraw->lpBACKBUFFER );
		
#if 0		
		// クリッ布の作成
		if( lpDraw->lpDD2->CreateClipper( 0,&lpDraw->lpCLIPPER, NULL ) != DD_OK )
		{
			MessageBox( hWnd, "Clipper生成失败。", "确认", MB_OK | MB_ICONSTOP );     //MLHIDE
			return FALSE;
		}
#if 1		
		RGNDATAHEADER* lpRgn;
		LPRECT lpRect;
		
		lpRgn = (RGNDATAHEADER *)GlobalAlloc( GPTR,
					sizeof( RGNDATAHEADER ) + sizeof( RECT ) * 1 );
#ifdef PUK2_MEMCHECK
		memlistset( lpRgn, MEMLISTTYPE_RGNDATAHEADER );
#endif

		if( lpRgn )
		{
			lpRect = (RECT *)(lpRgn + 1);

			lpRgn->dwSize			= sizeof( RGNDATAHEADER );
			lpRgn->iType			= RDH_RECTANGLES;
			lpRgn->nCount			= 1;
			lpRgn->nRgnSize			= sizeof( RECT );
			lpRgn->rcBound.left		= 0;
			lpRgn->rcBound.top		= 0;
			lpRgn->rcBound.right	= DEF_APPSIZEX;
			lpRgn->rcBound.bottom	= DEF_APPSIZEY;

			SetRect( lpRect, 0, 0, DEF_APPSIZEX, DEF_APPSIZEX );

			// クリップリストの设定
			lpDraw->lpCLIPPER->SetClipList( (RGNDATA *)lpRgn, 0 );
			// バックサーフェスにクリッ布を关连付ける
			lpDraw->lpBACKBUFFER->SetClipper( lpDraw->lpCLIPPER );

#ifdef PUK2_MEMCHECK
			memlistrel( lpRgn, MEMLISTTYPE_RGNDATAHEADER );
#endif
			GlobalFree( lpRgn );
		}
#endif		
		// クリッ布にウィンドウのハンドルをセットする
		//lpDraw->lpCLIPPER->SetHWnd( 1, hWnd );
		// プライマリサーフェスにクリッ布を关连付ける
		//lpDraw->lpFRONTBUFFER->SetClipper( lpDraw->lpCLIPPER );
		//lpDraw->lpBACKBUFFER->SetClipper( lpDraw->lpCLIPPER );
#endif		
	}
	
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
	DDPIXELFORMAT ddPixelFormat;	// ピクセル情报构造体
	
	// １６ビットカラーのＲＧＢビット情报を取得する
	if( displayBpp != 8 )
	{
		// サーフェスポインタがNULLの时返回
		if( lpDraw->lpBACKBUFFER == NULL )
			return FALSE;

		// 构造体の初期化
		ZeroMemory( &ddPixelFormat, sizeof( DDPIXELFORMAT ) );
		ddPixelFormat.dwSize = sizeof( DDPIXELFORMAT );
		//ddPixelFormat.dwFlags = DDPF_RGB;

		if( lpDraw->lpBACKBUFFER->GetPixelFormat( &ddPixelFormat ) != DD_OK )
		{
			return FALSE;
		}

		rBitMask = ddPixelFormat.dwRBitMask;
		gBitMask = ddPixelFormat.dwGBitMask;
		bBitMask = ddPixelFormat.dwBBitMask;

		rBitRShift = 8 - getBitCount( rBitMask );	// Ｒビットの右シフト值
		gBitRShift = 8 - getBitCount( gBitMask );	// Ｇビットの右シフト值
		bBitRShift = 8 - getBitCount( bBitMask );	// Ｂビットの右シフト值

		rBitLShift = getBitCount( bBitMask )+getBitCount( gBitMask );	// Ｒビットの右シフト值
		gBitLShift = getBitCount( bBitMask );							// Ｇビットの右シフト值
		bBitLShift = 0;													// Ｂビットの右シフト值
	}
#endif	
	// DirectDraw初期化フラグをTUREにする
	DDinitFlag = TRUE;	
	
	// 设定したDirectDraw管理构造体をリターン
	return TRUE;
		
}

#endif

#if 0

//---------------------------------------------------------------------------//
// 概要 ：指定リソースによるパレット初期化关数                               //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        LPBITMAPINFO pInfo  : BITMAP情报构造体                             //
//---------------------------------------------------------------------------//
BOOL InitPalette( void )
{
	int i;
	FILE *fp; // ファイルポインタ
	char palName[512];
	
	// ウィンドウシステムパレット
	//PALETTEENTRY pal[ 20 ]={
	PALETTEENTRY pal[ 32 ]={
		// 最初の１０色
		{0x00 ,0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 0:黒
		{0x80 ,0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 1:暗い赤
		{0x00 ,0x80, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 2:暗い緑
		{0x80 ,0x80, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 3:暗い黄
		{0x00 ,0x00, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 4:暗い青
		{0x80 ,0x00, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 5:暗い紫
		{0x00 ,0x80, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 6:暗い水色
		{0xc0 ,0xc0, 0xc0, PC_NOCOLLAPSE | PC_RESERVED}, // 7:暗い白
		{0xc0 ,0xdc, 0xc0, PC_NOCOLLAPSE | PC_RESERVED}, // 8:？
		{0xa6 ,0xca, 0xf0, PC_NOCOLLAPSE | PC_RESERVED}, // 9:？
		
		// 新システムパレット
		{ 0xde, 0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xff, 0x5f, 0x00, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xff, 0xff, 0xa0, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x00, 0x5f, 0xd2, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x50, 0xd2, 0xff, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x28, 0xe1, 0x28, PC_NOCOLLAPSE | PC_RESERVED },
		
		// 新システムパレット
		{ 0xf5, 0xc3, 0x96, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xe1, 0xa0, 0x5f, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xc3, 0x7d, 0x46, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x9b, 0x55, 0x1e, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x46, 0x41, 0x37, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x28, 0x23, 0x1e, PC_NOCOLLAPSE | PC_RESERVED },
		
		// 最后の１０色
		{0xff ,0xfb, 0xf0, PC_NOCOLLAPSE | PC_RESERVED}, // 246:？
		{0xa0 ,0xa0, 0xa4, PC_NOCOLLAPSE | PC_RESERVED}, // 247:？
		{0x80 ,0x80, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 248:灰色
		{0xff ,0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 249:赤
		{0x00 ,0xff, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 250:緑
		{0xff ,0xff, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 251:黄
		{0x00 ,0x00, 0xff, PC_NOCOLLAPSE | PC_RESERVED}, // 252:青
		{0xff ,0x00, 0xff, PC_NOCOLLAPSE | PC_RESERVED}, // 253:紫
		{0x00 ,0xff, 0xff, PC_NOCOLLAPSE | PC_RESERVED}, // 254:水色
		{0xff ,0xff, 0xff, PC_NOCOLLAPSE | PC_RESERVED}  // 255:白
	};


	// システムパレットの设定
	for( i = 0; i < 10; i++ ){
		Palette[i].peBlue  	= pal[i].peBlue;
		Palette[i].peGreen 	= pal[i].peGreen;
		Palette[i].peRed 	= pal[i].peRed;
		Palette[i].peFlags 	= PC_EXPLICIT;
		
		Palette[i+246].peBlue  	= pal[i+22].peBlue;
		Palette[i+246].peGreen 	= pal[i+22].peGreen;
		Palette[i+246].peRed 	= pal[i+22].peRed;
		Palette[i+246].peFlags 	= PC_EXPLICIT;
	}
	
	// 新システムパレットの设定
	for( i = 0; i < 6; i++ ){
		Palette[i+10].peBlue  	= pal[i+10].peBlue;
		Palette[i+10].peGreen 	= pal[i+10].peGreen;
		Palette[i+10].peRed 	= pal[i+10].peRed;
		Palette[i+10].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED;
		
		Palette[i+240].peBlue  	= pal[i+16].peBlue;
		Palette[i+240].peGreen 	= pal[i+16].peGreen;
		Palette[i+240].peRed 	= pal[i+16].peRed;
		Palette[i+240].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED;
	}
	
	// 初期化されてない时（一番最初だけ）
	if( PalState.flag == FALSE ){
	
		// パレットファイルオープン
		//fp = fopen( palFileName[ DEF_PAL ], "rb" );
		// パレットファイルオープン
//		sprintf( palName, "%s\\%s", binFolder, palFileName[ DEF_PAL ] );
		// そのパレットがインストールされているはずだったらオープンする。
		sprintf( palName, "%s\\%s", binFolder, PalFile[ DEF_PAL ].name );   //MLHIDE
		fp = fopen( palName, "rb" );                                        //MLHIDE
		if( fp == NULL )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox(hWnd, ERRMSG_70, "Error", MB_OK| MB_ICONSTOP );         //MLHIDE
#else
			MessageBox(hWnd, "色盘文件无法打开。", "Error", MB_OK| MB_ICONSTOP );       //MLHIDE
#endif
			return FALSE;
		}
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ ){
		for( i = 16; i < 240; i++ ){
			// パレットの読み込み
			Palette[i].peBlue  	= fgetc( fp );
			Palette[i].peGreen 	= fgetc( fp );
			Palette[i].peRed 	= fgetc( fp );
			
			// ウィンドウモードの时
			//if( WindowMode ){
				Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED;
				//Palette[i].peFlags = PC_RESERVED;
			//}else{
			//	Palette[i].peFlags = PC_EXPLICIT;
			//}
		}
		// ファイル关闭
		fclose( fp );
		
	}

	// 転生エフェクト中はこれをしない
	//if( !transmigrationEffectFlag )
	//{
	//	Palette[168].peBlue  	= 0;
	//	Palette[168].peGreen 	= 0;
	//	Palette[168].peRed 		= 0;
		//Palette[168].peFlags 	= PC_EXPLICIT;
	//}


#if 0
	else{
	
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ ){
		for( i = 16; i < 240; i++ ){
			// ウィンドウモードの时
			if( WindowMode ){
				Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED;
			}else{
				Palette[i].peFlags = PC_EXPLICIT;
			}
		}
	}
#endif
	// パレットの作成
	// 8BitインデックスをPalette变数で初期化し、结果をlpPALETTEへ返す。
	lpDraw->lpDD2->CreatePalette( DDPCAPS_8BIT, Palette, &lpDraw->lpPALETTE, NULL );
	if( lpDraw->lpPALETTE == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox(hWnd, ERRMSG_71, "Erro	r", MB_OK| MB_ICONSTOP );         //MLHIDE
#else
		MessageBox(hWnd, "色盘生成失白", "Error", MB_OK| MB_ICONSTOP );           //MLHIDE
#endif
		return FALSE;
	}

	// プライマリサーフェスのパレットに上记取得パレットをアタッチする
	//if( lpDraw->lpFRONTBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK ){
		//MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);
	//	MessageBox(hWnd, "将画面变成256色模式", "Error", MB_OK);
	//	return FALSE;
	//}
	
	// プライマリサーフェスのパレットに上记取得パレットをアタッチする
#ifdef SUPPORT_16BIT
	if( displayBpp == 8 )
	{
		if( lpDraw->lpFRONTBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox(hWnd, ERRMSG_72, "Error", MB_OK);                       //MLHIDE
#else
			MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);                        //MLHIDE
#endif
//		if( MessageBox(hWnd, "将画面变成256色模式", "确认", MB_RETRYCANCEL | MB_ICONEXCLAMATION ) == IDCANCEL) return FALSE;
			return FALSE;
		}
	}
#else
	while( lpDraw->lpFRONTBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK )
	{
		if( MessageBox(hWnd, "将画面变成256色模式", "确认", MB_RETRYCANCEL | MB_ICONEXCLAMATION ) == IDCANCEL) return FALSE; //MLHIDE
	}
#endif

	// パレットの中身を设定
	//lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );


	if( displayBpp == 16 )
	{
		// ハイカラーの时の变换テーブル作成
		for( i = 0; i < 256; i++ )
		{
			highColorPalette[i] =
				((Palette[i].peBlue>>bBitRShift)<<bBitLShift)
				+((Palette[i].peGreen>>gBitRShift)<<gBitLShift)
				+((Palette[i].peRed>>rBitRShift)<<rBitLShift);
#ifdef PUK2
			if (!highColorPalette[i]) highColorPalette[i] = 1;
#endif
		}
#ifdef PUK2
		highColorPalette[DEF_COLORKEY] = 0;
#endif
	}else 
	if( displayBpp == 24 ){
		for( i = 0; i < 256; i++ )
		{
			Color24Palette[i].r = (unsigned char)Palette[i].peRed
			Color24Palette[i].g = (unsigned char)Palette[i].peGreen
			Color24Palette[i].b = (unsigned char)Palette[i].peBlue
#ifdef PUK2
			if ( !(Color24Palette[i].r |
				 Color24Palette[i].g |
				 Color24Palette[i].b) ) Color24Palette[i].b = 1;
#endif
		}
#ifdef PUK2
		Color24Palette[DEF_COLORKEY].r = 0;
		Color24Palette[DEF_COLORKEY].g = 0;
		Color24Palette[DEF_COLORKEY].b = 0;
#endif
	}else
	if( displayBpp >= 32 )
	{
		for( i = 0; i < 256; i++ )
		{
			fullColorPalette[i] =
				(unsigned long)Palette[i].peBlue
				+((unsigned long)Palette[i].peGreen<<8)
				+((unsigned long)Palette[i].peRed<<16);
#ifdef PUK2
			if (!fullColorPalette[i]) fullColorPalette[i] = 1;
#endif
		}
#ifdef PUK2
		fullColorPalette[DEF_COLORKEY] = 0;
#endif
	}

	// 初期化フラグをＯＮ
	PalState.flag = TRUE;

	palChageStatus = FALSE;

	return TRUE;
}

#else

//---------------------------------------------------------------------------//
// 概要 ：指定リソースによるパレット初期化关数                               //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        LPBITMAPINFO pInfo  : BITMAP情报构造体                             //
//---------------------------------------------------------------------------//
BOOL InitPalette( void )
{
	int i;
	FILE *fp; // ファイルポインタ
	char palName[512];

	// ウィンドウシステムパレット
	//PALETTEENTRY pal[ 20 ]={
	PALETTEENTRY pal[ 32 ]={
		// 最初の１０色
		{0x00 ,0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 0:黒
		{0x80 ,0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 1:暗い赤
		{0x00 ,0x80, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 2:暗い緑
		{0x80 ,0x80, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 3:暗い黄
		{0x00 ,0x00, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 4:暗い青
		{0x80 ,0x00, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 5:暗い紫
		{0x00 ,0x80, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 6:暗い水色
		{0xc0 ,0xc0, 0xc0, PC_NOCOLLAPSE | PC_RESERVED}, // 7:暗い白
		{0xc0 ,0xdc, 0xc0, PC_NOCOLLAPSE | PC_RESERVED}, // 8:？
		{0xa6 ,0xca, 0xf0, PC_NOCOLLAPSE | PC_RESERVED}, // 9:？
		
		// 新システムパレット
		{ 0xde, 0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xff, 0x5f, 0x00, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xff, 0xff, 0xa0, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x00, 0x5f, 0xd2, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x50, 0xd2, 0xff, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x28, 0xe1, 0x28, PC_NOCOLLAPSE | PC_RESERVED },
		
		// 新システムパレット
		{ 0xf5, 0xc3, 0x96, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xe1, 0xa0, 0x5f, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0xc3, 0x7d, 0x46, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x9b, 0x55, 0x1e, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x46, 0x41, 0x37, PC_NOCOLLAPSE | PC_RESERVED },
		{ 0x28, 0x23, 0x1e, PC_NOCOLLAPSE | PC_RESERVED },
		
		// 最后の１０色
		{0xff ,0xfb, 0xf0, PC_NOCOLLAPSE | PC_RESERVED}, // 246:？
		{0xa0 ,0xa0, 0xa4, PC_NOCOLLAPSE | PC_RESERVED}, // 247:？
		{0x80 ,0x80, 0x80, PC_NOCOLLAPSE | PC_RESERVED}, // 248:灰色
		{0xff ,0x00, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 249:赤
		{0x00 ,0xff, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 250:緑
		{0xff ,0xff, 0x00, PC_NOCOLLAPSE | PC_RESERVED}, // 251:黄
		{0x00 ,0x00, 0xff, PC_NOCOLLAPSE | PC_RESERVED}, // 252:青
		{0xff ,0x00, 0xff, PC_NOCOLLAPSE | PC_RESERVED}, // 253:紫
		{0x00 ,0xff, 0xff, PC_NOCOLLAPSE | PC_RESERVED}, // 254:水色
		{0xff ,0xff, 0xff, PC_NOCOLLAPSE | PC_RESERVED}  // 255:白
	};


#ifndef DIRECT3D_ON
	// システムパレットの设定
	for( i = 0; i < 10; i++ ){
		Palette[i].peBlue  	= pal[i].peBlue;
		Palette[i].peGreen 	= pal[i].peGreen;
		Palette[i].peRed 	= pal[i].peRed;
		Palette[i].peFlags 	= PC_EXPLICIT;
		
		Palette[i+246].peBlue  	= pal[i+22].peBlue;
		Palette[i+246].peGreen 	= pal[i+22].peGreen;
		Palette[i+246].peRed 	= pal[i+22].peRed;
		Palette[i+246].peFlags 	= PC_EXPLICIT;
	}
	
	// 新システムパレットの设定
	for( i = 0; i < 6; i++ ){
		Palette[i+10].peBlue  	= pal[i+10].peBlue;
		Palette[i+10].peGreen 	= pal[i+10].peGreen;
		Palette[i+10].peRed 	= pal[i+10].peRed;
		Palette[i+10].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED;
		
		Palette[i+240].peBlue  	= pal[i+16].peBlue;
		Palette[i+240].peGreen 	= pal[i+16].peGreen;
		Palette[i+240].peRed 	= pal[i+16].peRed;
		Palette[i+240].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED;
	}
	
	// 初期化されてない时（一番最初だけ）
	if( PalState.flag == FALSE ){
	
		// パレットファイルオープン
		sprintf( palName, "%s\\%s", binFolder, PalFile[ DEF_PAL ].name );   //MLHIDE
		fp = fopen( palName, "rb" );                                        //MLHIDE
		if( fp == NULL )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox(hWnd, ERRMSG_73, "Error", MB_OK| MB_ICONSTOP );         //MLHIDE
#else
			MessageBox(hWnd, "色盘文件无法打开。", "Error", MB_OK| MB_ICONSTOP );       //MLHIDE
#endif /* PUK3_ERRORMESSAGE_NUM */
			return FALSE;
		}
		
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ ){
		for( i = 16; i < 240; i++ ){
			// パレットの読み込み
			Palette[i].peBlue  	= fgetc( fp );
			Palette[i].peGreen 	= fgetc( fp );
			Palette[i].peRed 	= fgetc( fp );
			
			// ウィンドウモードの时
			//if( WindowMode ){
				Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED;
				//Palette[i].peFlags = PC_RESERVED;
			//}else{
			//	Palette[i].peFlags = PC_EXPLICIT;
			//}
		}
		// ファイル关闭
		fclose( fp );

#ifdef PUK2
		memcpy( DefPalette, Palette, (256)*sizeof(PALETTEENTRY) );
#endif /* PUK2 */
	}
#else
	// システムパレットの设定
	for( i = 0; i < 10; i++ ){
		Palette[i].peBlue  	= pal[i].peBlue;
		Palette[i].peGreen 	= pal[i].peGreen;
		Palette[i].peRed 	= pal[i].peRed;
		Palette[i].peFlags 	= PC_EXPLICIT | D3DPAL_READONLY;
		//Palette[i].peFlags 	= PC_EXPLICIT | D3DPAL_READONLY | D3DPAL_FREE;
		
		Palette[i+246].peBlue  	= pal[i+22].peBlue;
		Palette[i+246].peGreen 	= pal[i+22].peGreen;
		Palette[i+246].peRed 	= pal[i+22].peRed;
		Palette[i+246].peFlags 	= PC_EXPLICIT | D3DPAL_READONLY;
		//Palette[i+246].peFlags 	= PC_EXPLICIT | D3DPAL_FREE;
	}
	
	// 新システムパレットの设定
	for( i = 0; i < 6; i++ ){
		Palette[i+10].peBlue  	= pal[i+10].peBlue;
		Palette[i+10].peGreen 	= pal[i+10].peGreen;
		Palette[i+10].peRed 	= pal[i+10].peRed;
		Palette[i+10].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED | D3DPAL_READONLY;
		//Palette[i+10].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED | D3DPAL_FREE;
		
		Palette[i+240].peBlue  	= pal[i+16].peBlue;
		Palette[i+240].peGreen 	= pal[i+16].peGreen;
		Palette[i+240].peRed 	= pal[i+16].peRed;
		Palette[i+240].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED | D3DPAL_READONLY;
		//Palette[i+240].peFlags 	= PC_NOCOLLAPSE | PC_RESERVED | D3DPAL_FREE;
	}
	
	// 初期化されてない时（一番最初だけ）
	if( PalState.flag == FALSE ){
	
		// パレットファイルオープン
		sprintf( palName, "%s\\%s", binFolder, PalFile[ DEF_PAL ].name );   //MLHIDE
		fp = fopen( palName, "rb" );                                        //MLHIDE
		if( fp == NULL )
		{
#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox(hWnd, ERRMSG_74, "Error", MB_OK| MB_ICONSTOP );         //MLHIDE
#else
			MessageBox(hWnd, "色盘文件无法打开。", "Error", MB_OK| MB_ICONSTOP );       //MLHIDE
#endif /* PUK3_ERRORMESSAGE_NUM */
			return FALSE;
		}
		
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ ){
		for( i = 16; i < 240; i++ ){
			// パレットの読み込み
			Palette[i].peBlue  	= fgetc( fp );
			Palette[i].peGreen 	= fgetc( fp );
			Palette[i].peRed 	= fgetc( fp );
			
			// ウィンドウモードの时
			//if( WindowMode ){
				Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED | D3DPAL_READONLY; 
				//Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED | D3DPAL_FREE; 
				//Palette[i].peFlags = PC_RESERVED;
			//}else{
			//	Palette[i].peFlags = PC_EXPLICIT;
			//}
		}
		// ファイル关闭
		fclose( fp );
		
#ifdef PUK2
		memcpy( DefPalette, Palette, (256)*sizeof(PALETTEENTRY) );
#endif /* PUK2 */
	}
#endif /* !DIRECT3D_ON */
		Palette[168].peBlue  	= 0;
		Palette[168].peGreen 	= 0;
		Palette[168].peRed 		= 0;
		//Palette[168].peFlags 	= PC_EXPLICIT;
	
#if 0
	else{
	
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ ){
		for( i = 16; i < 240; i++ ){
			// ウィンドウモードの时
			if( WindowMode ){
				Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED;
			}else{
				Palette[i].peFlags = PC_EXPLICIT;
			}
		}
	}
#endif /* 0 */
	// パレットの作成
	// 8BitインデックスをPalette变数で初期化し、结果をlpPALETTEへ返す。
	lpDraw->lpDD2->CreatePalette( DDPCAPS_8BIT, Palette, &lpDraw->lpPALETTE, NULL );
	if( lpDraw->lpPALETTE == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox(hWnd, ERRMSG_75, "Error", MB_OK| MB_ICONSTOP );          //MLHIDE
#else
		MessageBox(hWnd, "色盘生成失白", "Error", MB_OK| MB_ICONSTOP );           //MLHIDE
#endif /* PUK3_ERRORMESSAGE_NUM */
		return FALSE;
	}

	// プライマリサーフェスのパレットに上记取得パレットをアタッチする
	//if( lpDraw->lpFRONTBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK ){
		//MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);
	//	MessageBox(hWnd, "将画面变成256色模式", "Error", MB_OK);
	//	return FALSE;
	//}
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
	if( displayBpp == 8 ){
	
		// プライマリサーフェスのパレットに上记取得パレットをアタッチする
		while( lpDraw->lpFRONTBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK ){
			//MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);
			if( MessageBox(hWnd, "将画面变成256色模式", "确认", MB_RETRYCANCEL | MB_ICONEXCLAMATION ) == IDCANCEL) return FALSE; //MLHIDE
		}
		
		// プライマリサーフェスのパレットに上记取得パレットをアタッチする
		//while( lpDraw->lpBACKBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK ){
			//MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);
		//	if( MessageBox(hWnd, "将画面变成256色模式", "确认", MB_RETRYCANCEL | MB_ICONEXCLAMATION ) == IDCANCEL) return FALSE;
		//}
	}
	if( displayBpp == 16 )
	{
		// ハイカラーの时の变换テーブル作成
		for( i = 0; i < 256; i++ )
		{
			highColorPalette[i] =
				((Palette[i].peBlue>>bBitRShift)<<bBitLShift)
				+((Palette[i].peGreen>>gBitRShift)<<gBitLShift)
				+((Palette[i].peRed>>rBitRShift)<<rBitLShift);
#ifdef PUK2
			if (!highColorPalette[i]) highColorPalette[i] = 1;
#endif /* PUK2 */
		}
#ifdef PUK2
		highColorPalette[DEF_COLORKEY] = 0;
#endif /* PUK2 */
	}
	else
	if( displayBpp == 24 )
	{
		for( i = 0; i < 256; i++ )
		{
			Color24Palette[i].r = Palette[i].peRed;
			Color24Palette[i].g = Palette[i].peGreen;
			Color24Palette[i].b = Palette[i].peBlue;
#ifdef PUK2
			if ( !(Color24Palette[i].r |
				 Color24Palette[i].g |
				 Color24Palette[i].b) ) Color24Palette[i].b = 1;
#endif /* PUK2 */
		}
#ifdef PUK2
		Color24Palette[DEF_COLORKEY].r = 0;
		Color24Palette[DEF_COLORKEY].g = 0;
		Color24Palette[DEF_COLORKEY].b = 0;
#endif /* PUK2 */
	}else
	if( displayBpp >= 32 )
	{
		for( i = 0; i < 256; i++ )
		{
			fullColorPalette[i] =
				(unsigned long)Palette[i].peBlue
				+((unsigned long)Palette[i].peGreen<<8)
				+((unsigned long)Palette[i].peRed<<16);
#ifdef PUK2
			if (!fullColorPalette[i]) fullColorPalette[i] = 1;
#endif /* PUK2 */
		}
#ifdef PUK2
		fullColorPalette[DEF_COLORKEY] = 0;
#endif /* PUK2 */
	}
	
#else
	// プライマリサーフェスのパレットに上记取得パレットをアタッチする
	while( lpDraw->lpFRONTBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK ){
		//MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);
		if( MessageBox(hWnd, "将画面变成256色模式", "确认", MB_RETRYCANCEL | MB_ICONEXCLAMATION ) == IDCANCEL) return FALSE; //MLHIDE
	}
	
	// プライマリサーフェスのパレットに上记取得パレットをアタッチする
	//while( lpDraw->lpBACKBUFFER->SetPalette( lpDraw->lpPALETTE ) != DD_OK ){
		//MessageBox(hWnd, "色盘附加失败", "Error", MB_OK);
	//	if( MessageBox(hWnd, "将画面变成256色模式", "确认", MB_RETRYCANCEL | MB_ICONEXCLAMATION ) == IDCANCEL) return FALSE;
	//}
#endif /* SUPPORT_16BIT */
	// パレットの中身を设定
	//lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
	#ifdef PUK2
		// 初期化されてない时（一番最初だけ）
		if( PalState.flag == FALSE ){
			for(i=0;i<256;i++){
				PalchgTbl[0][i] = i;
				PalchgTbl[1][i] = i;
				PalchgTbl[2][i] = i;
			}
		}
	#endif /* PUK2 */

	
	// 初期化フラグをＯＮ
	PalState.flag = TRUE;

	palChageStatus = FALSE;

	return TRUE;
}

#endif

// パレットを特定のバッファに読み込む
int LoadPaletteToBuffer( int palNo, PALETTEENTRY *pPalette ){
	FILE *fp;
	char palName[256];
	int i;

	// マップに使うパレットをロードする。
	sprintf( palName, "%s\\%s", binFolder, PalFile[ palNo ].name );      //MLHIDE
	fp = fopen( palName, "rb" );                                         //MLHIDE
	if( fp == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox(hWnd, ERRMSG_76, "Error", MB_OK| MB_ICONSTOP );          //MLHIDE
#else
		MessageBox(hWnd, "色盘文件无法打开。", "Error", MB_OK| MB_ICONSTOP );        //MLHIDE
#endif
		return FALSE;
	}
	// 自由に使えるパレットの设定
	for( i = 16; i < 240; i++ ){
		// パレットの読み込み
		pPalette[i].peBlue  	= fgetc( fp );
		pPalette[i].peGreen 	= fgetc( fp );
		pPalette[i].peRed 	= fgetc( fp );
		pPalette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED;
	}
	// ファイル关闭
	fclose( fp );

	// システムパレット设定
	for( i = 0; i < 16; i++ ){
		pPalette[i] = Palette[i];
	}
	for( i = 240; i < 256; i++ ){
		pPalette[i] = Palette[i];
	}
	return TRUE;
}


// パレットチェンジ ***********************************************************/
void PaletteChange( int palNo, int time )
{
	// リミットチェック
	if( palNo >= MAX_PAL ) return;

	// インストールされてるはずの无いパレットへの变化は无视
	if( PalFile[palNo].version > giInstallVersion ){
		return;
	}

	// パレット番号
	PalState.palNo = palNo;

	// パレット变更时间
	PalState.time = time;
	// リミットチェック
	if( PalState.time <= 0 ) PalState.time = 1;
}

// パレット处理 ***************************************************************/
void PaletteProc( void )
{
	FILE *fp; // ファイルポインタ
	static PALETTEENTRY	pal[256];	// パレット构造体
	static float	dRed[256];		// パレット增分
	static float	dGreen[256];	// パレット增分
	static float	dBlue[256];		// パレット增分
	static float	dRedBak[256];	// 小数点パレット
	static float	dGreenBak[256];	// 小数点パレット
	static float	dBlueBak[256];	// 小数点パレット
#ifdef PUK2
	static float	dpl[3];			// パレット增分
	static float	apl[3];			// 小数点パレット
	static int		p2pPalNum;		// 变更后のパレットナンバー
#endif
	static int 	timeCnt = 0;		// タイムカウンター
	static int 	changeCnt;			// チェンジカウンター
	static int 	palNoBak = -1;		// カウンター
	static int 	openFlag = FALSE;	// ファイルオープンフラグ
	int i;
	char palName[512];
#ifdef PUK2
	int OldpalNo = PalState.palNo;
	int OldpalCnt= PalState.count;
#endif
	
	// パレットが变更されてない时
	if( palNoBak == PalState.palNo && openFlag == FALSE ) return;
	
	// オープンしてなかったら
	if( palNoBak != PalState.palNo ){
		// このパレットがインストールされているはずなら
		if( PalFile[PalState.palNo].version <= giInstallVersion ){
			// パレットファイルオープン
			sprintf( palName, "%s\\%s", binFolder, PalFile[ PalState.palNo ].name ); //MLHIDE
			fp = fopen( palName, "rb" );                                       //MLHIDE
			if( fp == NULL ) {
#ifdef _DEBUG
				char buf[1024];
				sprintf( buf, "file open error [%s]\n", palName);                 //MLHIDE
				MessageBox( hWnd, buf, "error", MB_OK);                           //MLHIDE
#endif
				return;
			}
		}else{
			// インストールされてなかったら无视
			return;
		}
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ )
		for( i = 16; i < 240; i++ )
		{
			pal[i].peBlue  	= fgetc( fp );
			pal[i].peGreen 	= fgetc( fp );
			pal[i].peRed 	= fgetc( fp );
			
			// 168の色は( 0, 0, 0 )に变换
			if( i == 168 ){
				pal[168].peBlue  	= 0;
				pal[168].peGreen 	= 0;
				pal[168].peRed 		= 0;
			}
			
			// 小数パレット
			dBlueBak[ i ] 	= 	Palette[i].peBlue;
			dGreenBak[ i ] 	= 	Palette[i].peGreen;
			dRedBak[ i ] 	= 	Palette[i].peRed;
			// 增分计算
			dBlue[ i ] 	= 	( float )( pal[ i ].peBlue - Palette[i].peBlue ) 	/ ( float )PalState.time;
			dGreen[ i ] = 	( float )( pal[ i ].peGreen - Palette[i].peGreen )	/ ( float )PalState.time;
			dRed[ i ] 	= 	( float )( pal[ i ].peRed - Palette[i].peRed ) 		/ ( float )PalState.time;
#if 0
			// ウィンドウモードの时
			if( WindowMode ){
				Palette[i].peFlags = PC_NOCOLLAPSE | PC_RESERVED;
			}else{
				Palette[i].peFlags = PC_EXPLICIT;
			}
#endif
		}
		
	
		fclose( fp );				// ファイル关闭
		timeCnt = 0;				// 内部的にパレットをセットするカウンター初期化
		changeCnt = 0;				// 实际にパレットをセットするカウンター初期化;
		palNoBak = PalState.palNo;	// バックアップ
		openFlag = TRUE;			// フラグＯＮ
		PalState.count = 1;		// Windowモード用カウンターは１から数える
#ifdef PUK2
		if (Palchg==Palchg_a_data){
			p2pPalNum=PalState.palNo;
			i=p2pPalNum;
			// 小数パレット
			apl[PPLR]=Palchg_a_data[PPLR];
			apl[PPLG]=Palchg_a_data[PPLG];
			apl[PPLB]=Palchg_a_data[PPLB];
			// 增分计算
			dpl[PPLR] = ( float )( Pchgdata[i][PPLR] - Palchg_a_data[PPLR] ) 	/ ( float )PalState.time;
			dpl[PPLG] = ( float )( Pchgdata[i][PPLG] - Palchg_a_data[PPLG] )	/ ( float )PalState.time;
			dpl[PPLB] = ( float )( Pchgdata[i][PPLB] - Palchg_a_data[PPLB] ) 	/ ( float )PalState.time;
		}else{
			// 小数パレット
			apl[PPLR]=apl[PPLG]=apl[PPLB]=0;
			// 增分计算
			dpl[PPLR]=dpl[PPLG]=dpl[PPLB]=0;
		}
/***	旧マップで新パレットチェンジをしないのでいらない
		if (Palchg){
			for(i=0;i<256;i++){
				PDpal[i][PPLR]=Palette[i].peRed;
				PDpal[i][PPLG]=Palette[i].peGreen;
				PDpal[i][PPLB]=Palette[i].peBlue;
			}
		}
***/
#endif
	}
	
	// カウンター
	timeCnt++;
	// 变更する时
	if( timeCnt <= PalState.time ){	
		// 自由に使えるパレットの设定
		//for( i = 10 ; i < 246 ; i++ ){
		for( i = 16; i < 240; i++ ){
			// 小数点パレット
			dBlueBak[ i ] 	+= 	dBlue[ i ];
			dGreenBak[ i ]	+=	dGreen[ i ];
			dRedBak[ i ]	+=	dRed[ i ];
			// 整数パレット
			Palette[i].peBlue  	= ( BYTE )dBlueBak[ i ];
			Palette[i].peGreen 	= ( BYTE )dGreenBak[ i ];
			Palette[i].peRed 	= ( BYTE )dRedBak[ i ];
		}
#ifdef PUK2
		if (Palchg==Palchg_a_data){
			// 小数点パレット
			apl[PPLR] 	+= 	dpl[PPLR];
			apl[PPLG] 	+= 	dpl[PPLG];
			apl[PPLB] 	+= 	dpl[PPLB];
			// 整数パレット
			Palchg_a_data[PPLR]=( BYTE )apl[PPLR];
			Palchg_a_data[PPLG]=( BYTE )apl[PPLG];
			Palchg_a_data[PPLB]=( BYTE )apl[PPLB];

/***	旧マップで新パレットチェンジをしないのでいらない
			for( i = 16; i < 240; i++ ){
				PDpal[i][PPLR]=Palette[i].peRed;
				PDpal[i][PPLG]=Palette[i].peGreen;
				PDpal[i][PPLB]=Palette[i].peBlue;
			}
***/
		}
#endif
		palChageStatus = TRUE;
	}else{ 
			
		// 最后のフレームにに完全なパレットの值をいれる
		// 自由に使えるパレットの设定
		//for( i = 10; i < 246; i++ ){
		for( i = 16; i < 240; i++ ){
			Palette[i].peBlue  	= pal[ i ].peBlue;
			Palette[i].peGreen 	= pal[ i ].peGreen;
			Palette[i].peRed 	= pal[ i ].peRed;
		}
#ifdef PUK2
		if (Palchg==Palchg_a_data){
			i=p2pPalNum;
			// 整数パレット
			Palchg_a_data[PPLR]=Pchgdata[i][PPLR];
			Palchg_a_data[PPLG]=Pchgdata[i][PPLG];
			Palchg_a_data[PPLB]=Pchgdata[i][PPLB];

/***	旧マップで新パレットチェンジをしないのでいらない
			for( i = 16; i < 240; i++ ){
				PDpal[i][PPLR]=Palette[i].peRed;
				PDpal[i][PPLG]=Palette[i].peGreen;
				PDpal[i][PPLB]=Palette[i].peBlue;
			}
***/
		}
#endif
		openFlag = FALSE;	// フラグ初期化
		palChageStatus = FALSE;
		PalState.count +=10;		// １６ビット用カウンターを加算して絶对パレットチェンジするようにする。
	}
	
	// １６ビットモードパレット生成
	if( displayBpp == 16 ) makeHighColorPalette( );
	if( displayBpp == 32 ) makeFullColorPalette( );
	if( displayBpp == 24 ) make24BitColorPalette( );
#ifndef PUK2
	// １０フレームに一回だけ表示（重过ぎるため）
	if( changeCnt == 0 || openFlag == FALSE ){
		// パレットチェンジフラグＯＮ
		PalChangeFlag = TRUE;
		// パレットの中身を设定
		//lpDraw->lpPALETTE->SetEntries( 0, 0, 256, Palette );
	}
#endif
	// カウンタープラス
	changeCnt++;
#ifdef PUK2
	// PUK2 は１６ＢＰＰなので
	if( PalState.time > 120 ){	// 长い间チェンジするときは
		if( changeCnt >= PalState.time / 20 ){	// ２０段阶固定
			changeCnt = 0;
			PalState.count++;	// １６ビット用カウンターを加算
		}
	}else{
		if( changeCnt >= PAL_CHANGE_INTERVAL_WIN ) changeCnt = 0;
		PalState.count++;	// １６ビット用カウンターを加算
	}
#else
	// ウィンドウモードの时
	if( WindowMode ){
		if( PalState.time > 120 ){	// 长い间チェンジするときは
			if( changeCnt >= PalState.time / 20 ){	// ２０段阶固定
				changeCnt = 0;
				PalState.count++;	// １６ビット用カウンターを加算
			}
		}else{
			if( changeCnt >= PAL_CHANGE_INTERVAL_WIN ) changeCnt = 0;
			PalState.count++;	// １６ビット用カウンターを加算
		}
	}else{
		if( changeCnt >= PAL_CHANGE_INTERVAL_FULL ) changeCnt = 0;
	}
#endif
#ifdef PUK2
	if ( OldpalNo!=PalState.palNo || OldpalCnt!=PalState.count ){
		int a;
		if (Palchg[PPLR]==128){
			for(i=0;i<256;i++) PalchgTbl[PPLR][i] = i;
		}else{
			for(i=0;i<256;i++){
				a=(i*Palchg[PPLR])>>7;
				PalchgTbl[PPLR][i] = ( a>255 ? 255 : a );
			}
		}

		if (Palchg[PPLG]==128){
			for(i=0;i<256;i++) PalchgTbl[PPLG][i] = i;
		}else{
			for(i=0;i<256;i++){
				a=(i*Palchg[PPLG])>>7;
				PalchgTbl[PPLG][i] = ( a>255 ? 255 : a );
			}
		}

		if (Palchg[PPLB]==128){
			for(i=0;i<256;i++) PalchgTbl[PPLB][i] = i;
		}else{
			for(i=0;i<256;i++){
				a=(i*Palchg[PPLB])>>7;
				PalchgTbl[PPLB][i] = ( a>255 ? 255 : a );
			}
		}
	}
#endif
}	


// 8bit色画像を16bit色で表示するためのカラーテーブル作成
void makeHighColorPalette( void )
{
	unsigned short tmpR,tmpG,tmpB;
	int i;

	// ハイカラーの时の变换テーブル作成
	for( i = 16; i < 240; i++ )
	{
		tmpR = Palette[i].peRed;
		tmpG = Palette[i].peGreen;
		tmpB = Palette[i].peBlue;
		highColorPalette[i] =
			  ((tmpR >> rBitRShift)<<rBitLShift )
			| ((tmpG >> gBitRShift)<<gBitLShift )
			| ((tmpB >> bBitRShift)<<bBitLShift );
/*
			(((int)(Palette[i].peBlue)>>bBitRShift)<<bBitLShift)
			+(((int)(Palette[i].peGreen)>>gBitRShift)<<gBitLShift)
			+(((int)(Palette[i].peRed)>>rBitRShift)<<rBitLShift) );
*/
#ifdef PUK2
		if (!highColorPalette[i]) highColorPalette[i] = 1;
#endif
	}
#ifdef PUK2
	highColorPalette[DEF_COLORKEY] = 0;
#endif
}


// 8bit色画像を32bit色で表示するためのカラーテーブル作成
void makeFullColorPalette( void )
{
	int i;

	for( i = 0; i < 256; i++ )
	{
		fullColorPalette[i] =
			(unsigned long)Palette[i].peBlue
			+((unsigned long)Palette[i].peGreen<<8)
			+((unsigned long)Palette[i].peRed<<16);
#ifdef PUK2
		if (!fullColorPalette[i]) fullColorPalette[i] = 1;
#endif
	}
#ifdef PUK2
	fullColorPalette[DEF_COLORKEY] = 0;
#endif
}

// 8bit色画像を24bit色で表示するためのカラーテーブル作成
void make24BitColorPalette( void )
{
	int i;

	for( i = 0; i < 256; i++ )
	{
		Color24Palette[i].r = (unsigned char)Palette[i].peRed;
		Color24Palette[i].g = (unsigned char)Palette[i].peGreen;
		Color24Palette[i].b = (unsigned char)Palette[i].peBlue;
#ifdef PUK2
		if ( !(Color24Palette[i].r |
			 Color24Palette[i].g |
			 Color24Palette[i].b) ) Color24Palette[i].b = 1;
#endif
	}
#ifdef PUK2
	Color24Palette[DEF_COLORKEY].r = 0;
	Color24Palette[DEF_COLORKEY].g = 0;
	Color24Palette[DEF_COLORKEY].b = 0;
#endif
}

int InitLine, tmpInitLine, FirstLine=-1, LastLine=-1, LineCount = 0;

#ifndef PUK2

//---------------------------------------------------------------------------//
// 概要 ：垂直同期线中にバックサーフェスからフロントサーフェスへコピー       //
// 引数 ：なし                         										 //
// 戾值 ：なし                                                               //
//---------------------------------------------------------------------------//
void Flip( void )
{
// ウィンドウクラス构造体
//extern WNDCLASS wndclass;

	// ウィンドウモードの时
	if( WindowMode == TRUE ){
	
		RECT 	clientRect;  // クライアント领域の矩形领域を格纳
		POINT 	clientPoint; // クライアント领域の左上のスクリーン座标位置を格纳
		//RECT 	moveRect;  	 // 転送元の矩形领域を格纳
		
		// クライアント领域の左上座标の设定
		clientPoint.x = clientPoint.y = 0;
		// クライアント座标から左上のスクリーン座标を取得
		ClientToScreen( hWnd, &clientPoint ); 
		// クライアント领域のサイズを取得
		GetClientRect( hWnd, &clientRect );
		// クライアント领域全体のスクリーン座标を求める ( clientRect に格纳される )
		OffsetRect( &clientRect, clientPoint.x, clientPoint.y );
		// 転送元の矩形领域をセット
		//SetRect( &moveRect, 0, 0, lpDraw->xSize, lpDraw->ySize );
#if 1
#if 1
		RECT deskrc, rc;
		HWND hDeskWnd = GetDesktopWindow();
		HRESULT ddreturn;    
		DWORD scanLine;
		DWORD start_pos;
		//BOOL verticalBlankFlag;
		static int modlcnt = 0;
		int cnt = 0;

	    GetWindowRect(hDeskWnd, (LPRECT)&deskrc);
	    GetWindowRect(hWnd, (LPRECT)&rc);
		//デスクトップからはみ出してるなら
		if( rc.bottom + 100 >= deskrc.bottom ){
			start_pos = 0;
//		} else
		//下まで１００ドット未满なら
//		if(deskrc.bottom - rc.bottom < 100){
//			start_pos = 0;
		} else {
		//下まで１００ドット以上なら
			start_pos = rc.bottom;
		}

		// 描画するタイミングまでループ
		while( 1 )
		{
			// ラスターウエイトしないなら即ブレイク
			if( RasterNoWaitMode )break;
#if 0		
			// 垂直归线中かどうか（０：そうでない　１：归线中）
			ddreturn = lpDraw->lpDD2->GetVerticalBlankStatus( &verticalBlankFlag );
			
			// 戾り值チェック
			switch( ddreturn ){
				case DD_OK:
					ddreturn = ddreturn;
				break;

				case DDERR_INVALIDOBJECT:
					ddreturn = ddreturn;
				break;
				
				case DDERR_INVALIDPARAMS:
					ddreturn = ddreturn;
				break;
				
			}	
			if( verticalBlankFlag == TRUE ) continue;
			//if( lpDraw->lpDD2->GetVerticalBlankStatus( &verticalBlankFlag ) == FALSE ) continue;
#endif

			// スキャンライン取得
			ddreturn = lpDraw->lpDD2->GetScanLine( &scanLine );
#if 0			
			// 戾り值チェック
			switch( ddreturn ){
				case DD_OK:
					ddreturn = ddreturn;
				break;

				case DDERR_INVALIDOBJECT:
					ddreturn = ddreturn;
				break;
				
				case DDERR_INVALIDPARAMS:
					ddreturn = ddreturn;
				break;
				
				case DDERR_UNSUPPORTED:
					ddreturn = ddreturn;
				break;
				
				case DDERR_VERTICALBLANKINPROGRESS:
					ddreturn = ddreturn;
				break;
			}
#endif			
			//if(ddreturn != DD_OK){
				//start_pos = 5000;
				//break;
			//	continue;
			//}
			// 成功したとき
			if( ddreturn == DD_OK ){

				// 描画するタイミングのとき
				if( scanLine >= start_pos && scanLine < start_pos + 100 )
				{
					break;
				}
			}
			// 垂直归线中のとき（省电力モードなどでかえってこなくなる）
			else 
//				if( ddreturn == DDERR_VERTICALBLANKINPROGRESS )
			{
				cnt++;
				// 待ったら拔ける
				if( cnt >= 10000 ){
					// 画面モード切替
					//PostMessage( hWnd, WM_SYSKEYDOWN, VK_RETURN, NULL );
					break;
				}else{
				}
			}
			// サポートされてなかったとき（省电力モードなど）
			//else if( ddreturn == DDERR_UNSUPPORTED ){
			//	break;
			//}
		}
#else
		// 垂直同期待ち
		//lpDraw->lpDD2->WaitForVerticalBlank( DDWAITVB_BLOCKBEGIN, NULL );
		lpDraw->lpDD2->WaitForVerticalBlank( DDWAITVB_BLOCKEND, NULL );
#endif
#endif
		// プライマリーサーフェスへ一発転送 ( これがウィンドウモードのフリップ )
		//lpDraw->lpFRONTBUFFER->Blt( &clientRect, lpDraw->lpBACKBUFFER, &moveRect, DDBLT_WAIT, NULL );
		lpDraw->lpFRONTBUFFER->Blt( &clientRect, lpDraw->lpBACKBUFFER, NULL, DDBLT_WAIT, NULL );
		// バックサーフェスへ高速転送
		//lpDraw->lpFRONTBUFFER->BltFast( clientRect.left, clientRect.top, lpDraw->lpBACKBUFFER, &moveRect, DDBLTFAST_WAIT );
		//SetCursor( wndclass.hCursor );

	}else{  // フルスクリーンモードの时

		// 画面のフリップ
		lpDraw->lpFRONTBUFFER->Flip( NULL, DDFLIP_WAIT );
		
	}

	// フリップカウンター变更
	if( FlipCnt == 0 ) FlipCnt = 1;
	else FlipCnt = 0;
	
	return;
}

#endif
#ifdef PUK2
int		PUK2_fillColor = 0;
#endif
//---------------------------------------------------------------------------//
// 概要 ：バックサーフェスクリア关数                                         //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
// 戾值 ：なし                                                               //
//---------------------------------------------------------------------------//
void ClearBackSurface( void )
{
#if 1
	DDBLTFX ddbltfx;

	ZeroMemory( &ddbltfx, sizeof( DDBLTFX ) );
	ddbltfx.dwSize = sizeof( DDBLTFX );
	//ddbltfx.dwFillColor = DEF_COLORKEY; // 透明色を设定
	// 涂りつぶす色を设定
#ifdef PUK2
	ddbltfx.dwFillColor = PUK2_fillColor;
#endif

	lpDraw->lpBACKBUFFER->Blt( NULL, NULL, NULL, DDBLT_COLORFILL|DDBLT_WAIT, &ddbltfx );

	return;
#endif
}

//---------------------------------------------------------------------------//
// 概要 ：サーフェスクリア关数                 		                         //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
// 戾值 ：なし                                                               //
//---------------------------------------------------------------------------//
#ifdef PUK2
void ClearSurface( LPDIRECTDRAWSURFACE7 lpSurface )
#else
void ClearSurface( LPDIRECTDRAWSURFACE lpSurface )
#endif
{
	DDBLTFX ddbltfx;

	ZeroMemory( &ddbltfx, sizeof( DDBLTFX ) );
	ddbltfx.dwSize = sizeof( DDBLTFX );
	ddbltfx.dwFillColor = DEF_COLORKEY; // 透明色を设定
	
	lpSurface->Blt( NULL, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbltfx );

	return;
}

//---------------------------------------------------------------------------//
// 概要 ：ビットマップファイルの読み込み及びメモリー确保                     //
// 引数 ：char * pFile  : ビットマップファイル名                             //
// 戾り值：LPBITMAPINFO : NULL .メモリ确保Orファイル失败                     //
//                NULL以外.LPBITMAPINFOアドレス                              //
//---------------------------------------------------------------------------//
LPBITMAPINFO LoadDirectDrawBitmap( char *pFile )
{
	HFILE hFile;
	OFSTRUCT ofSt;
	BITMAPFILEHEADER BmpFileHeader;
	LPBITMAPINFO lpBmpInfo;

	//ファイル読み込み
	if( ( hFile = OpenFile( pFile, &ofSt, OF_READ ) ) == HFILE_ERROR )
		return (LPBITMAPINFO)NULL; // File Open Error

	//ビットマップファイルヘッダー読み込み
	_hread( hFile, &BmpFileHeader, sizeof(BITMAPFILEHEADER) );

	//ビットマップ用领域确保
	if( (lpBmpInfo = (LPBITMAPINFO)GlobalAllocPtr( GPTR, BmpFileHeader.bfSize )) == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_77, "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
#else
		MessageBox( hWnd, "位图用空间申请失败", "确认", MB_OK | MB_ICONSTOP );         //MLHIDE
#endif
		return (LPBITMAPINFO)NULL; //Memory Error
	}
#ifdef PUK2_MEMCHECK
	memlistset( lpBmpInfo, MEMLISTTYPE_BITMAPMEMORY );
#endif
	
	//ビットマップ読み出し
	_hread( hFile, (void *)lpBmpInfo, BmpFileHeader.bfSize );

	//ファイルクローズ
	_lclose( hFile );
	
	// ＢＭＰのイメージデータまでのOFFセット
	BmpOffBits = BmpFileHeader.bfOffBits - sizeof( BITMAPFILEHEADER );
	
	return lpBmpInfo;
}

#ifndef PUK2

//---------------------------------------------------------------------------//
// 概要 ：パターン用サーフェスの作成                                         //
// 引数 ：short bxsize           : 作成??????????横幅(????????)                       //
//        short bysize           : 作成??????????縦幅(????????)                       //
//        DWORD ColorKey         : 透明色とする色番号(0～255)                //
// 戾り值：正常終了 ... サーフェスのアドレス / 失败 ... NULL                 //
//---------------------------------------------------------------------------//
LPDIRECTDRAWSURFACE CreateSurface( short bxsize, short bysize, DWORD ColorKey, unsigned int VramOrSysram )
{
	DDCOLORKEY ddck;
	LPDIRECTDRAWSURFACE lpSurface;

	// パターン用バッファの生成
	// ddsCaps构造体を  有效化 & dwWidth,dwHeight指定有效
	lpDraw->ddsd.dwFlags = DDSD_CAPS | DDSD_WIDTH | DDSD_HEIGHT;
	// この、サーフェスはフロントでもバックでもない、サーフェスであることを指定
	lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_OFFSCREENPLAIN | VramOrSysram;
	//lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_OFFSCREENPLAIN | DDSCAPS_SYSTEMMEMORY;
	//lpDraw->ddsd.ddsCaps.dwCaps = DDSCAPS_OFFSCREENPLAIN | DDSCAPS_VIDEOMEMORY;
	// サーフェスの幅を指定
	lpDraw->ddsd.dwWidth = bxsize;
	// サーフェスの高さを指定
	lpDraw->ddsd.dwHeight = bysize;	
	// パターン用サーフェスの实定义
    if( lpDraw->lpDD2->CreateSurface( &lpDraw->ddsd, &lpSurface, NULL ) != DD_OK )
		return (LPDIRECTDRAWSURFACE)NULL;

	// 透明色カラーキーの设定
	ddck.dwColorSpaceLowValue =  ColorKey;
	ddck.dwColorSpaceHighValue = ColorKey;
	lpSurface->SetColorKey( DDCKEY_SRCBLT, &ddck );

	return lpSurface;
}

#endif

//---------------------------------------------------------------------------//
// 概要 ：ビットマップを指定サーフェスへ描画                                 //
// 引数 ：LPDIRECTDRAWSURFACE lpSurface : 描画サーフェス                     //
//        short Xpoint        : 読み取るＢＭＰ横位置                         //
//        short Ypoint        :	読み取るＢＭＰ縦位置                         //
//        LPBITMAPINFO pInfo  : 描画するビットマップ构造体                   //
// 戾值 ：なし                                                               //
//---------------------------------------------------------------------------//
// ＢＭＰをサーフェスへ転送关数 （ StretchDIBits を使用 ）
void DrawBitmapToSurface( LPDIRECTDRAWSURFACE lpSurface, int offsetX, int offsetY, LPBITMAPINFO pBmpInfo )
{
	HDC hDcDest;
	//HDC hDcBmp;
	//HBITMAP hBmp;
	
	//DDSURFACEDESC ddsd;	// サーフェス构造体
    //char *pDest;		// 転送先のポインタ
    //char *pSource; 		// 転送元のポインタ
	//int i;
	
	// 指定サーフェス番号へのビットマップ画像の転送
	lpSurface->GetDC( &hDcDest );
	StretchDIBits( 	hDcDest, 
					0, 0, 
					//pBmpInfo->bmiHeader.biWidth, pBmpInfo->bmiHeader.biHeight, 
					SurfaceSizeX, SurfaceSizeY,
					offsetX, offsetY, 
					//pBmpInfo->bmiHeader.biWidth, pBmpInfo->bmiHeader.biHeight,
					SurfaceSizeX, SurfaceSizeY,
					(void *)((BYTE *)pBmpInfo + (sizeof(BITMAPINFOHEADER) + sizeof(RGBQUAD) * 256)), 
					pBmpInfo, 
					
					//DIB_PAL_COLORS,	
					DIB_RGB_COLORS, 
					//DIB_PAL_INDICES,
					
					SRCCOPY );
					//NOTSRCCOPY );
					//DSTINVERT );
					//BLACKNESS );

	lpSurface->ReleaseDC( hDcDest );
	
	return;
}

// ＢＭＰをサーフェスへ転送关数 （ memcpy を使用 ）****************************/
#ifdef PUK2
	void DrawBitmapToSurface2( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo )
#else
	void DrawBitmapToSurface2( LPDIRECTDRAWSURFACE lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo )
#endif
{
	#ifdef PUK2
		DDSURFACEDESC2 ddsd;	// サーフェス构造体
	#else
		DDSURFACEDESC ddsd;	// サーフェス构造体
	#endif
    char *pDest;		// 転送先のポインタ
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
	short *pDest2;		// 転送先のポインタ（ワード型）
	long *pDest3;		// 転送先のポインタ（ロング型）
	COL24BIT *pDest24;	// 転送先のポインタ（２４ＢＩＴ）
#endif
    char *pSource; 		// 転送元のポインタ
    //char *pDestBak;		// 転送先のポインタバックアップ
    //char *pSourceBak; 	// 転送元のポインタバックアップ
	int surfacePitch;	// サーフェスの横幅记忆
	int bmpWidth;		// ＢＭＰの横幅记忆
	int i;
	
	// サーフェスポインタがNULLの时返回
	if( lpSurface == NULL ) return;
	
	// 构造体の初期化
	#ifdef PUK2
		ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
		ddsd.dwSize = sizeof( DDSURFACEDESC2 );
	#else
		ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
		ddsd.dwSize = sizeof( DDSURFACEDESC );
	#endif

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpSurface->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}	
	
	// サーフェスの左上隅のアドレス
	pDest = ( char *)( ddsd.lpSurface );
	
#if 0	// ＢＭＰファイルから読み込むとき

	// 転送元のアドレス
	pSource = ( char *)pBmpInfo + BmpOffBits
				+ offsetY * pBmpInfo->bmiHeader.biWidth + offsetX;
				
#else	// Realbin から読み込むとき

	// 転送元のアドレス
	pSource = pRealBinBits
				+ offsetY * RealBinWidth + offsetX;

#endif
				
	// サーフェスの横幅记忆
	surfacePitch = ddsd.lPitch;
#if 0
	// ＢＭＰの横幅记忆
	bmpWidth = pBmpInfo->bmiHeader.biWidth;
	
#else // Realbin 読み込むとき
	// ＢＭＰの横幅记忆
	bmpWidth = RealBinWidth;
#endif

	// 縦のサイズ分ループする
	for( i = 0 ; i < sizeY ; i++ ){
		
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
		if( displayBpp == 24 ){
			int j;
			COL24BIT pixel;

			pDest24 = (COL24BIT *)pDest;

			for( j= 0; j < sizeX; j++ ){
				if( pSource[j] == DEF_COLORKEY ){
					pDest24[j].r = pDest24[j].g = pDest24[j].b = 0;
					continue;
				}else{
					pixel = Color24Palette[(unsigned char)pSource[j]];
					if( pixel.r == 0 && pixel.g == 0 && pixel.b == 0 ){
						pDest24[j].r = 0; pDest24[j].g = 0 ; pDest24[j].b = 1;
					}else{
						pDest24[j] = pixel;
					}
				}
			}
		}else
		if( displayBpp == 32 ){
			int j;
			long pixel;

			pDest3 = (long *)pDest;

			for( j= 0; j < sizeX; j++ ){
				if( pSource[j] == DEF_COLORKEY ){
					pDest3[j] = 0;
					continue;
					
				}else{
				
					pixel = fullColorPalette[(unsigned char)pSource[j]];
					if( pixel == 0 )
					{
						pDest3[j] = 1;
					}
					else
					{
						pDest3[j] = pixel;
					}
				}
			}
		}else
		if( displayBpp == 16 ){
//			memcpy( pDest, pSource, sizeX );

			int j;
			short pixel;

			pDest2 = (short *)pDest;

			for( j = 0; j < sizeX; j++ ){
				// 透过色なら０を入れる
				if( pSource[j] == DEF_COLORKEY ){
					pDest2[j] = 0;
				}else{
					pixel = highColorPalette[(unsigned char)pSource[j]];
					if( pixel == 0 ){
						pDest2[j] = 1;
					}else{
						pDest2[j] = pixel;
					}
				}
			}

		}else
#endif
		{
			memcpy( pDest, pSource, sizeX );
		}
		//HiOここまでいじった
		
		// サーフェスの横幅分アドレスを戾す（ 逆から転送しているため ）
		pSource -= bmpWidth;
		
		// サーフェスの横幅分アドレスを进める
		pDest += surfacePitch;
    }

	// アクセスするサーフェスをアンロックする
	if( lpSurface->Unlock( NULL ) != DD_OK ){
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}	
	
	return;
}

#ifdef PUK2
unsigned char puk2_box_color[][3]={
	//	赤		緑		青
	{	0xa2,	0xe8,	0xff	},		// 少し淡い青
};
#endif

// ボックスを描画 **************************************************************/
#ifdef PUK2
void DrawBox( RECT *rect, unsigned short color, BOOL fill )
#else
void DrawBox( RECT *rect, unsigned char color, BOOL fill )
#endif
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
    char *pDest;		// 転送先のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int i;				// ループカウンタ
	int bottom;			// 下线までのアドレス数
	int w;				// 横幅
	int h;				// 縦幅
	int flag = FALSE;
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
	int j;
	short *pDest2;		// 転送先のポインタ（ワード型）
	long *pDest3;		// 転送先のポインタ（ロング型）
	COL24BIT *pDest24;	// 転送先のポインタ（３バイト型）
	short pixel;		// 16BitColorピクセル情报
	long pixel2;		// 32BitColorピクセル情报
	COL24BIT pixel24;	// 24BitColorピクセル情报
#endif
	
	// リミットチェック
	if( rect->left >= lpDraw->xSize ) return;
	if( rect->right < 0 ) return;
	if( rect->top >= lpDraw->ySize ) return;
	if( rect->bottom < 0 ) return;
	
	if( rect->left < 0 ) rect->left = 0;
	if( rect->right >= lpDraw->xSize ) rect->right = lpDraw->xSize;
	if( rect->top < 0 ) rect->top = 0;
	if( rect->bottom >= lpDraw->ySize ) rect->bottom = lpDraw->ySize;
	
	
	w = rect->right - rect->left; // 横幅
	h = rect->bottom - rect->top; // 縦幅
	
	// 幅がない时　返回
	//if( w <= 2 || h <= 2 ) return;
	// ラインでなく幅がない时　返回
	if( fill != 2 && ( w <= 2 || h <= 2 ) ) return;
	if( fill == 2 && w == 0 ) return;
	
	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif
	
	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpBACKBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}	
	
	// サーフェスの横幅记忆
	surfacePitch = ddsd.lPitch;

//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
	// 涂り始めるサーフェスのアドレス
	if( displayBpp == 32 )
	{
		// 32 Bit Color
		surfacePitch = ddsd.lPitch >> 2;
		pDest3 = (long *)ddsd.lpSurface + rect->top * surfacePitch + rect->left + 1;
	}
	else
	// 涂り始めるサーフェスのアドレス
	if( displayBpp == 24 )
	{
		// 24 Bit Color
		surfacePitch = ddsd.lPitch / 3 ;
		pDest24= (COL24BIT *)ddsd.lpSurface + rect->top * surfacePitch + rect->left + 1;
	}
	else
	if( displayBpp == 16 )
	{
		// 16 Bit Color
		surfacePitch = ddsd.lPitch >> 1;
		pDest2 = (short *)ddsd.lpSurface + rect->top * surfacePitch + rect->left + 1;
	}
	else
#endif
	{
		// 8 Bit Color
		pDest = ( char *)( ddsd.lpSurface ) + rect->top * surfacePitch + rect->left;
	}

	// ボックスのとき
	if( fill == FALSE )
	{
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
		if( displayBpp == 32 )
		{
			// 32 Bit Color

			// 下线のアドレスまでの修正值
			bottom = ( h - 1 ) * surfacePitch;

	#ifdef PUK2
			if ( color >= 0x100 ) pixel2 = puk2_box_color[color-0x100][2]|(puk2_box_color[color-0x100][1]<<8)|(puk2_box_color[color-0x100][0]<<16);
			else
	#endif
			pixel2 = fullColorPalette[color];

			// 横ループ
			for( i = 0; i < w - 2; i++ )
			{
				// 上の线を描画
				*(pDest3 + i) = pixel2;
				*(pDest3 + surfacePitch + i) = pixel2;
				// 下の线を描画
				*(pDest3 + bottom - surfacePitch + i) = pixel2;
				*(pDest3 + bottom + i) = pixel2;
    		}

			// 内侧の四点を描画
			*(pDest3 + surfacePitch + surfacePitch + 1) = pixel2;					// 左上
			*(pDest3 + surfacePitch + surfacePitch + w - 4) = pixel2;				// 右上
			*(pDest3 + bottom - ( surfacePitch + surfacePitch ) + 1) = pixel2;		// 左下
			*(pDest3 + bottom - ( surfacePitch + surfacePitch ) + w - 4)  = pixel2;	// 右下

			// サーフェスの横幅分アドレスを进める
			pDest3 += surfacePitch - 1;

			// 縦ループ
			for( i = 0 ; i < h - 2 ; i++ )
			{
				// 左の线を描画
				*pDest3 = pixel2;
				*(pDest3 + 1) = pixel2;
				// 右の线を描画
				*(pDest3 + w - 1) = pixel2;
				*(pDest3 + w - 2) = pixel2;
				// サーフェスの横幅分アドレスを进める
				pDest3 += surfacePitch;
    		}
		}
		else
		if( displayBpp == 24 )
		{
			// 24 Bit Color

			// 下线のアドレスまでの修正值
			bottom = ( h - 1 ) * surfacePitch;

	#ifdef PUK2
			if ( color >= 0x100 ){
				pixel24.r = puk2_box_color[color-0x100][0];
				pixel24.g = puk2_box_color[color-0x100][1];
				pixel24.b = puk2_box_color[color-0x100][2];
			}else
	#endif
			pixel24 = Color24Palette[color];

			// 横ループ
			for( i = 0; i < w - 2; i++ )
			{
				// 上の线を描画
				*(pDest24 + i) = pixel24;
				*(pDest24 + surfacePitch + i) = pixel24;
				// 下の线を描画
				*(pDest24 + bottom - surfacePitch + i) = pixel24;
				*(pDest24 + bottom + i) = pixel24;
    		}

			// 内侧の四点を描画
			*(pDest24 + surfacePitch + surfacePitch + 1) = pixel24;					// 左上
			*(pDest24 + surfacePitch + surfacePitch + w - 4) = pixel24;				// 右上
			*(pDest24 + bottom - ( surfacePitch + surfacePitch ) + 1) = pixel24;		// 左下
			*(pDest24 + bottom - ( surfacePitch + surfacePitch ) + w - 4)  = pixel24;	// 右下

			// サーフェスの横幅分アドレスを进める
			pDest24 += surfacePitch - 1;

			// 縦ループ
			for( i = 0 ; i < h - 2 ; i++ )
			{
				// 左の线を描画
				*pDest24 = pixel24;
				*(pDest24 + 1) = pixel24;
				// 右の线を描画
				*(pDest24 + w - 1) = pixel24;
				*(pDest24 + w - 2) = pixel24;
				// サーフェスの横幅分アドレスを进める
				pDest24 += surfacePitch;
    		}
		}
		else
		if( displayBpp == 16 )
		{
			// 16 Bit Color

			// 下线のアドレスまでの修正值
			bottom = ( h - 1 ) * surfacePitch;

	#ifdef PUK2
			if ( color >= 0x100 ) pixel = 
				lpDraw->PalTbl[ puk2_box_color[color-0x100][0] ][PPLR] |
				lpDraw->PalTbl[ puk2_box_color[color-0x100][1] ][PPLG] |
				lpDraw->PalTbl[ puk2_box_color[color-0x100][2] ][PPLB];
			else
	#endif
			pixel = highColorPalette[color];

			// 横ループ
			for( i = 0; i < w - 2; i++ )
			{
				// 上の线を描画
				*(pDest2 + i) = pixel;
				*(pDest2 + surfacePitch + i) = pixel;
				// 下の线を描画
				*(pDest2 + bottom - surfacePitch + i) = pixel;
				*(pDest2 + bottom + i) = pixel;
    		}

			// 内侧の四点を描画
			*(pDest2 + surfacePitch + surfacePitch + 1) = pixel;					// 左上
			*(pDest2 + surfacePitch + surfacePitch + w - 4) = pixel;				// 右上
			*(pDest2 + bottom - ( surfacePitch + surfacePitch ) + 1) = pixel;		// 左下
			*(pDest2 + bottom - ( surfacePitch + surfacePitch ) + w - 4)  = pixel;	// 右下

			// サーフェスの横幅分アドレスを进める
			pDest2 += surfacePitch - 1;

			// 縦ループ
			for( i = 0 ; i < h - 2 ; i++ )
			{
				// 左の线を描画
				*pDest2 = pixel;
				*(pDest2 + 1) = pixel;
				// 右の线を描画
				*(pDest2 + w - 1) = pixel;
				*(pDest2 + w - 2) = pixel;
				// サーフェスの横幅分アドレスを进める
				pDest2 += surfacePitch;
    		}
		}
		else
#endif
		{
			// 8 Bit Color
			pDest++;	// 位置ドットずらす

			// 下线までのアドレス
			bottom = ( h - 1 ) * surfacePitch;

#ifdef PUK2
			// 横ループ
			for( i = 0; i < w - 2; i++ )
			{
				// 上の线を描画
				*( pDest + i ) = (char)color;
				*( pDest + i + surfacePitch ) = (char)color;
				// 下の线を描画
				*( pDest + i + bottom ) = (char)color;
				*( pDest + i + bottom - surfacePitch ) = (char)color;
    		}

			// 内侧の四点を描画
			*( pDest + surfacePitch + surfacePitch + 1 ) = (char)color;						// 左上
			*( pDest + surfacePitch + surfacePitch + w - 4 ) = (char)color;					// 右上
			*( pDest + bottom - ( surfacePitch + surfacePitch ) + 1 ) = (char)color;		// 左下
			*( pDest + bottom - ( surfacePitch + surfacePitch ) + w - 4 ) = (char)color;	// 右下

			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch - 1;

			// 縦ループ
			for( i = 0 ; i < h - 2 ; i++ )
			{
				// 左の线を描画
				*pDest = (char)color;
				*( pDest + 1 ) = (char)color;
				// 右の线を描画
				*( pDest + w - 1 ) = (char)color;
				*( pDest + w - 2 ) = (char)color;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
    		}
    	}
#else
			// 横ループ
			for( i = 0; i < w - 2; i++ )
			{
				// 上の线を描画
				*( pDest + i ) = color;
				*( pDest + i + surfacePitch ) = color;
				// 下の线を描画
				*( pDest + i + bottom ) = color;
				*( pDest + i + bottom - surfacePitch ) = color;
    		}
	
			// 内侧の四点を描画
			*( pDest + surfacePitch + surfacePitch + 1 ) = color;					// 左上
			*( pDest + surfacePitch + surfacePitch + w - 4 ) = color;				// 右上
			*( pDest + bottom - ( surfacePitch + surfacePitch ) + 1 ) = color;		// 左下
			*( pDest + bottom - ( surfacePitch + surfacePitch ) + w - 4 ) = color;	// 右下

			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch - 1;

			// 縦ループ
			for( i = 0 ; i < h - 2 ; i++ )
			{
				// 左の线を描画
				*pDest = color;
				*( pDest + 1 ) = color;
				// 右の线を描画
				*( pDest + w - 1 ) = color;
				*( pDest + w - 2 ) = color;
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
    		}
    	}
#endif
	}else if( fill == 1 ){	// 涂りつぶしの时

//#ifdef _DEBUG
#ifdef SUPPORT_16BIT

		if( displayBpp == 32 )
		{
			// 32 Bit Color
			pixel2 = fullColorPalette[color];

			// 上のＬＩＮＥを描画
			for( i = 0; i < w-2; i++ )
			{
				*(pDest3 + i) = pixel2;
			}
			// サーフェスの横幅分アドレスを进める
			pDest3 += surfacePitch - 1;

			// 横ループ
			for( i = 0; i < h-2; i++ )
			{
				for( j = 0; j < w; j++ )
				{
					*(pDest3+j) = pixel2;
				}
				pDest3 += surfacePitch;
			}

			// 下のＬＩＮＥを描画
			pDest3++;
			for( i = 0; i < w-2; i++ )
			{
				*(pDest3 + i) = pixel2;
			}
		}
		else
		if( displayBpp == 24 )
		{
			// 24 Bit Color
			pixel24 = Color24Palette[color];

			// 上のＬＩＮＥを描画
			for( i = 0; i < w-2; i++ )
			{
				*(pDest24 + i) = pixel24;
			}
			// サーフェスの横幅分アドレスを进める
			pDest24 += surfacePitch - 1;

			// 横ループ
			for( i = 0; i < h-2; i++ )
			{
				for( j = 0; j < w; j++ )
				{
					*(pDest24+j) = pixel24;
				}
				pDest24 += surfacePitch;
			}

			// 下のＬＩＮＥを描画
			pDest24++;
			for( i = 0; i < w-2; i++ )
			{
				*(pDest24 + i) = pixel24;
			}
		}
		else
		if( displayBpp == 16 )
		{
			// 16 Bit Color

			pixel = highColorPalette[color];

			// 上のＬＩＮＥを描画
			for( i = 0; i < w-2; i++ )
			{
				*(pDest2 + i) = pixel;
			}
			// サーフェスの横幅分アドレスを进める
			pDest2 += surfacePitch - 1;

			// 横ループ
			for( i = 0; i < h-2; i++ )
			{
				for( j = 0; j < w; j++ )
				{
					*(pDest2+j) = pixel;
				}
				pDest2 += surfacePitch;
			}

			// 下のＬＩＮＥを描画
			pDest2++;
			for( i = 0; i < w-2; i++ )
			{
				*(pDest2 + i) = pixel;
			}
		}
		else
#endif
		{
			// 8 Bit Color

			pDest++;	// 位置ドットずらす

			// 上のＬＩＮＥを描画
			memset( pDest, color, w - 2 );
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch - 1;

			// 縦ループ
			for( i = 0 ; i < h - 2 ; i++ )
			{
				// ＬＩＮＥを描画
				memset( pDest, color, w );
				// サーフェスの横幅分アドレスを进める
				pDest += surfacePitch;
	    	}
			// 下のＬＩＮＥを描画
			memset( pDest + 1, color, w - 2 );
		}
	
	}else if( fill == 2 ){	// ラインの时
		
//#ifdef _DEBUG
#ifdef SUPPORT_16BIT
		
		if( displayBpp == 32 )
		{
			// 32 Bit Color

			pixel2 = fullColorPalette[color];

			// ＬＩＮＥを描画
			for( i = 0; i < w; i++ )
			{
				*(pDest3 + i) = pixel2;
			}
		}
		else
		if( displayBpp == 24 )
		{
			// 24 Bit Color

			pixel24 = Color24Palette[color];

			// ＬＩＮＥを描画
			for( i = 0; i < w; i++ )
			{
				*(pDest24 + i) = pixel24;
			}
		}
		else
		if( displayBpp == 16 )
		{
			// 16 Bit Color

			pixel = highColorPalette[color];

			// ＬＩＮＥを描画
			for( i = 0; i < w; i++ )
			{
				*(pDest2 + i) = pixel;
			}
		}
		else

#endif

		{
			// 8 Bit Color

			// ＬＩＮＥを描画
			memset( pDest, color, w );
			// サーフェスの横幅分アドレスを进める
			//pDest += surfacePitch;
			// ＬＩＮＥを描画
			//memset( pDest, color, w );
		}
	}
	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpBACKBUFFER->Unlock( NULL ) != DD_OK ){
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}	
	
	return;
}

#ifndef PUK2
//---------------------------------------------------------------------------//
// オートマッピングを描画                                                    //
//---------------------------------------------------------------------------//
// 引数：	int x, y;						左上表示座标
//			unsigned char *autoMap;			マップ表示用色情报
//			int w, h;						autoMap のサイズ
//			int zoom;						扩大
void DrawAutoMapping( int x, int y, unsigned char *autoMap, int w, int h, int zoom )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j;			// ループカウンタ
	int color;
	static short pcFlush = 0;
	static unsigned int pcFlushTime = 0;
	int xx, yy;
	int ww, hh;

	xx = 18;
	yy = 118;
	ww = w;
	hh = h;

	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpBACKBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}

//#ifdef _DEBUG
#ifdef SUPPORT_16BIT

	if( displayBpp == 8 )
#endif
	{
		char *ptDest;		// 転送先のポインタ
		char *tmpPtDest;	// ワーク
		char *tmpPtDest2;	// ワーク２

		// サーフェスの横幅记忆
		surfacePitch = ddsd.lPitch;

		// 涂り始めるサーフェスのアドレス
		if( zoom == 0 )
		{
			ptDest = (char *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					*(ptDest)   = color;
					*(ptDest+1) = color;
					if( (j & 1) == 0 )
					{
						ptDest -= (surfacePitch-1);
					}
					else
					{
						ptDest += 1;
					}
				}
				if( (i & 1) == 0 )
				{
					tmpPtDest += (surfacePitch+1);
				}
				else
				{
					tmpPtDest += 1;
				}
				autoMap += w;
			}
		}
		else
		if( zoom == 1 )
		{
			ptDest = (char *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					*(ptDest-1)				= color;
					*(ptDest)				= color;
					*(ptDest+1)				= color;
					*(ptDest-surfacePitch)	= color;
					ptDest -= (surfacePitch-2);
				}
				tmpPtDest += (surfacePitch+2);
				autoMap += w;
			}
		}
		else
		if( zoom == 2 )
		{
			ptDest = (char *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx) +1;
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					memset( ptDest-1, color, 6 );
					memset( ptDest-1-surfacePitch, color, 6 );
					*(ptDest-surfacePitch*2+1)	= color;
					*(ptDest-surfacePitch*2+2)	= color;
					*(ptDest-surfacePitch*3+1)	= color;
					*(ptDest-surfacePitch*3+2)	= color;
					ptDest -= (surfacePitch*2-4);
				}
				tmpPtDest += (surfacePitch*2+4);
				autoMap += w;
			}
		}

		// ＰＣキャラの表示（マップ中央で白黒点灭）
		if( pcFlush )
		{
			color = 255;
		}
		else
		{
			color = 0;
		}
		if( pcFlushTime+1000 <= GetTickCount() )
		{
			pcFlushTime = GetTickCount();
			pcFlush++;
			pcFlush &= 1;
		}
		if( zoom == 0 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/4 - (surfacePitch-2)*ww/4;
			*(ptDest)					= color;
			*(ptDest+1)					= color;
			*(ptDest-surfacePitch)		= color;
			*(ptDest-surfacePitch+1)	= color;
		}
		else
		if( zoom == 1 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
		}
		else
		if( zoom == 2 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh - (surfacePitch-2)*ww;
			memset( ptDest-2, color, 6 );
			memset( ptDest-2-surfacePitch, color, 6 );
			*(ptDest-surfacePitch*2)	= color;
			*(ptDest-surfacePitch*2+1)	= color;
			*(ptDest-surfacePitch*3)	= color;
			*(ptDest-surfacePitch*3+1)	= color;
		}
	}

//#ifdef _DEBUG
#ifdef SUPPORT_16BIT

	else
	if( displayBpp == 16 )
	{
		short *ptDest;		// 転送先のポインタ
		short *tmpPtDest;	// ワーク
		short *tmpPtDest2;	// ワーク２

		// サーフェスの横幅记忆
		surfacePitch = (ddsd.lPitch >> 1);

		// 涂り始めるサーフェスのアドレス
		if( zoom == 0 )
		{
			ptDest = (short *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						color = highColorPalette[color];
					}
					else
					{
						color = 0;
					}
					*(ptDest)	= color;
					*(ptDest+1)	= color;
					if( (j & 1) == 0 )
					{
						ptDest -= (surfacePitch-1);
					}
					else
					{
						ptDest += 1;
					}
				}
				if( (i & 1) == 0 )
				{
					tmpPtDest += (surfacePitch+1);
				}
				else
				{
					tmpPtDest += 1;
				}
				autoMap += w;
			}
		}
		else
		if( zoom == 1 )
		{
			ptDest = (short *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						color = highColorPalette[color];
					}
					else
					{
						color = 0;
					}
					*(ptDest-1)				= color;
					*(ptDest)				= color;
					*(ptDest+1)				= color;
					*(ptDest-surfacePitch)	= color;
					ptDest -= (surfacePitch-2);
				}
				tmpPtDest += (surfacePitch+2);
				autoMap += w;
			}
		}
		else
		if( zoom == 2 )
		{
			ptDest = (short *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx) +1;
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						color = highColorPalette[color];
					}
					else
					{
						color = 0;
					}
					*(ptDest-1) = color;
					*(ptDest)   = color;
					*(ptDest+1) = color;
					*(ptDest+2) = color;
					*(ptDest+3) = color;
					*(ptDest+4) = color;
					*(ptDest-surfacePitch-1) = color;
					*(ptDest-surfacePitch)   = color;
					*(ptDest-surfacePitch+1) = color;
					*(ptDest-surfacePitch+2) = color;
					*(ptDest-surfacePitch+3) = color;
					*(ptDest-surfacePitch+4) = color;
					*(ptDest-surfacePitch*2+1)	= color;
					*(ptDest-surfacePitch*2+2)	= color;
					*(ptDest-surfacePitch*3+1)	= color;
					*(ptDest-surfacePitch*3+2)	= color;
					ptDest -= (surfacePitch*2-4);
				}
				tmpPtDest += (surfacePitch*2+4);
				autoMap += w;
			}
		}

		// ＰＣキャラの表示（マップ中央で白黒点灭）
		if( pcFlush )
		{
			color = highColorPalette[255];
		}
		else
		{
			color = 0;
		}
		if( pcFlushTime+1000 <= GetTickCount() )
		{
			pcFlushTime = GetTickCount();
			pcFlush++;
			pcFlush &= 1;
		}
		if( zoom == 0 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/4 - (surfacePitch-2)*ww/4;
			*(ptDest)					= color;
			*(ptDest+1)					= color;
			*(ptDest-surfacePitch)		= color;
			*(ptDest-surfacePitch+1)	= color;
		}
		else
		if( zoom == 1 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
		}
		else
		if( zoom == 2 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh - (surfacePitch-2)*ww;
			*(ptDest-2) = color;
			*(ptDest-1) = color;
			*(ptDest)   = color;
			*(ptDest+1) = color;
			*(ptDest+2) = color;
			*(ptDest+3) = color;
			*(ptDest-surfacePitch-2) = color;
			*(ptDest-surfacePitch-1) = color;
			*(ptDest-surfacePitch)   = color;
			*(ptDest-surfacePitch+1) = color;
			*(ptDest-surfacePitch+2) = color;
			*(ptDest-surfacePitch+3) = color;
			*(ptDest-surfacePitch*2)	= color;
			*(ptDest-surfacePitch*2+1)	= color;
			*(ptDest-surfacePitch*3)	= color;
			*(ptDest-surfacePitch*3+1)	= color;
		}
	}else
	if( displayBpp == 32 )
	{
		long *ptDest;		// 転送先のポインタ
		long *tmpPtDest;	// ワーク
		long *tmpPtDest2;	// ワーク２

		// サーフェスの横幅记忆
		surfacePitch = (ddsd.lPitch >> 2);

		// 涂り始めるサーフェスのアドレス
		if( zoom == 0 )
		{
			ptDest = (long *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						color = fullColorPalette[color];
					}
					else
					{
						color = 0;
					}
					*(ptDest)	= color;
					*(ptDest+1)	= color;
					if( (j & 1) == 0 )
					{
						ptDest -= (surfacePitch-1);
					}
					else
					{
						ptDest += 1;
					}
				}
				if( (i & 1) == 0 )
				{
					tmpPtDest += (surfacePitch+1);
				}
				else
				{
					tmpPtDest += 1;
				}
				autoMap += w;
			}
		}
		else
		if( zoom == 1 )
		{
			ptDest = (long *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						color = fullColorPalette[color];
					}
					else
					{
						color = 0;
					}
					*(ptDest-1)				= color;
					*(ptDest)				= color;
					*(ptDest+1)				= color;
					*(ptDest-surfacePitch)	= color;
					ptDest -= (surfacePitch-2);
				}
				tmpPtDest += (surfacePitch+2);
				autoMap += w;
			}
		}
		else
		if( zoom == 2 )
		{
			ptDest = (long *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx) +1;
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						color = fullColorPalette[color];
					}
					else
					{
						color = 0;
					}
					*(ptDest-1) = color;
					*(ptDest)   = color;
					*(ptDest+1) = color;
					*(ptDest+2) = color;
					*(ptDest+3) = color;
					*(ptDest+4) = color;
					*(ptDest-surfacePitch-1) = color;
					*(ptDest-surfacePitch)   = color;
					*(ptDest-surfacePitch+1) = color;
					*(ptDest-surfacePitch+2) = color;
					*(ptDest-surfacePitch+3) = color;
					*(ptDest-surfacePitch+4) = color;
					*(ptDest-surfacePitch*2+1)	= color;
					*(ptDest-surfacePitch*2+2)	= color;
					*(ptDest-surfacePitch*3+1)	= color;
					*(ptDest-surfacePitch*3+2)	= color;
					ptDest -= (surfacePitch*2-4);
				}
				tmpPtDest += (surfacePitch*2+4);
				autoMap += w;
			}
		}

		// ＰＣキャラの表示（マップ中央で白黒点灭）
		if( pcFlush )
		{
			color = fullColorPalette[255];
		}
		else
		{
			color = 0;
		}
		if( pcFlushTime+1000 <= GetTickCount() )
		{
			pcFlushTime = GetTickCount();
			pcFlush++;
			pcFlush &= 1;
		}
		if( zoom == 0 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/4 - (surfacePitch-2)*ww/4;
			*(ptDest)					= color;
			*(ptDest+1)					= color;
			*(ptDest-surfacePitch)		= color;
			*(ptDest-surfacePitch+1)	= color;
		}
		else
		if( zoom == 1 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
			*(ptDest-1)				= color;
			*(ptDest)				= color;
			*(ptDest+1)				= color;
			*(ptDest-surfacePitch)	= color;
		}
		else
		if( zoom == 2 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh - (surfacePitch-2)*ww;
			*(ptDest-2) = color;
			*(ptDest-1) = color;
			*(ptDest)   = color;
			*(ptDest+1) = color;
			*(ptDest+2) = color;
			*(ptDest+3) = color;
			*(ptDest-surfacePitch-2) = color;
			*(ptDest-surfacePitch-1) = color;
			*(ptDest-surfacePitch)   = color;
			*(ptDest-surfacePitch+1) = color;
			*(ptDest-surfacePitch+2) = color;
			*(ptDest-surfacePitch+3) = color;
			*(ptDest-surfacePitch*2)	= color;
			*(ptDest-surfacePitch*2+1)	= color;
			*(ptDest-surfacePitch*3)	= color;
			*(ptDest-surfacePitch*3+1)	= color;
		}
	}else
	if( displayBpp == 24 )
	{
		COL24BIT *ptDest;		// 転送先のポインタ
		COL24BIT *tmpPtDest;	// ワーク
		COL24BIT *tmpPtDest2;	// ワーク２
		COL24BIT Color24;

		// サーフェスの横幅记忆
		surfacePitch = (ddsd.lPitch / 3);

		// 涂り始めるサーフェスのアドレス
		if( zoom == 0 )
		{
			ptDest = (COL24BIT *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						Color24 = Color24Palette[color];
					}
					else
					{
						Color24.r = 0;Color24.g = 0; Color24.b = 0;
					}
					*(ptDest)	= Color24;
					*(ptDest+1)	= Color24;
					if( (j & 1) == 0 )
					{
						ptDest -= (surfacePitch-1);
					}
					else
					{
						ptDest += 1;
					}
				}
				if( (i & 1) == 0 )
				{
					tmpPtDest += (surfacePitch+1);
				}
				else
				{
					tmpPtDest += 1;
				}
				autoMap += w;
			}
		}
		else
		if( zoom == 1 )
		{
			ptDest = (COL24BIT *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx);
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						Color24 = Color24Palette[color];
					}
					else
					{
						Color24.r = 0;Color24.g = 0; Color24.b = 0;
					}
					*(ptDest-1)				= Color24;
					*(ptDest)				= Color24;
					*(ptDest+1)				= Color24;
					*(ptDest-surfacePitch)	= Color24;
					ptDest -= (surfacePitch-2);
				}
				tmpPtDest += (surfacePitch+2);
				autoMap += w;
			}
		}
		else
		if( zoom == 2 )
		{
			ptDest = (COL24BIT *)(ddsd.lpSurface) + (y+yy) * surfacePitch + (x+xx) +1;
			tmpPtDest  = ptDest;
			tmpPtDest2 = ptDest;
			for( i = 0; i < hh; i++ )
			{
				ptDest = tmpPtDest;
				for( j = 0; j < ww; j++ )
				{
					color = autoMap[j];
					if( color != DEF_COLORKEY )
					{
						Color24 = Color24Palette[color];
					}
					else
					{
						Color24.r = 0;Color24.g = 0; Color24.b = 0;
					}
					*(ptDest-1) = Color24;
					*(ptDest)   = Color24;
					*(ptDest+1) = Color24;
					*(ptDest+2) = Color24;
					*(ptDest+3) = Color24;
					*(ptDest+4) = Color24;
					*(ptDest-surfacePitch-1) = Color24;
					*(ptDest-surfacePitch)   = Color24;
					*(ptDest-surfacePitch+1) = Color24;
					*(ptDest-surfacePitch+2) = Color24;
					*(ptDest-surfacePitch+3) = Color24;
					*(ptDest-surfacePitch+4) = Color24;
					*(ptDest-surfacePitch*2+1)	= Color24;
					*(ptDest-surfacePitch*2+2)	= Color24;
					*(ptDest-surfacePitch*3+1)	= Color24;
					*(ptDest-surfacePitch*3+2)	= Color24;
					ptDest -= (surfacePitch*2-4);
				}
				tmpPtDest += (surfacePitch*2+4);
				autoMap += w;
			}
		}

		// ＰＣキャラの表示（マップ中央で白黒点灭）
		if( pcFlush )
		{
			Color24 = Color24Palette[255];
		}
		else
		{
			Color24.r = Color24.g = Color24.b = 0;
		}
		if( pcFlushTime+1000 <= GetTickCount() )
		{
			pcFlushTime = GetTickCount();
			pcFlush++;
			pcFlush &= 1;
		}
		if( zoom == 0 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/4 - (surfacePitch-2)*ww/4;
			*(ptDest)					= Color24;
			*(ptDest+1)					= Color24;
			*(ptDest-surfacePitch)		= Color24;
			*(ptDest-surfacePitch+1)	= Color24;
		}
		else
		if( zoom == 1 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh/2 - (surfacePitch-2)*ww/2;
			*(ptDest-1)				= Color24;
			*(ptDest)				= Color24;
			*(ptDest+1)				= Color24;
			*(ptDest-surfacePitch)	= Color24;
		}
		else
		if( zoom == 2 )
		{
			ptDest = tmpPtDest2 + (surfacePitch+2)*hh - (surfacePitch-2)*ww;
			*(ptDest-2) = Color24;
			*(ptDest-1) = Color24;
			*(ptDest)   = Color24;
			*(ptDest+1) = Color24;
			*(ptDest+2) = Color24;
			*(ptDest+3) = Color24;
			*(ptDest-surfacePitch-2) = Color24;
			*(ptDest-surfacePitch-1) = Color24;
			*(ptDest-surfacePitch)   = Color24;
			*(ptDest-surfacePitch+1) = Color24;
			*(ptDest-surfacePitch+2) = Color24;
			*(ptDest-surfacePitch+3) = Color24;
			*(ptDest-surfacePitch*2)	= Color24;
			*(ptDest-surfacePitch*2+1)	= Color24;
			*(ptDest-surfacePitch*3)	= Color24;
			*(ptDest-surfacePitch*3+1)	= Color24;
		}
	}
#endif

	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpBACKBUFFER->Unlock( NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}

	return;
}
#endif

#ifdef PUK2
	extern int RealBinBmpNo;
#endif
// 画像の平均を取ってオートマップの色を作る
int getAutoMapColor( unsigned int GraphicNo, PALETTEENTRY *pPalette )
{
	int index = 0;
	static int width, height;
	static unsigned char *graBuf;
	unsigned int red = 0, green = 0, blue = 0;
	unsigned int cnt = 0;
	int i, j;
	int color;

#ifdef PUK2
	// Graphic.binの通し番号からイメージデータを読み込む
	if( realGetImage( GraphicNo, ( unsigned char **)&graBuf, &width, &height ) == FALSE )
	{
		RealBinBmpNo = -1;
		return 0;
	}
	RealBinBmpNo = GraphicNo;
#else
	// Graphic.binの通し番号からイメージデータを読み込む
	if( realGetImage( GraphicNo, ( unsigned char **)&graBuf, &width, &height ) == FALSE )
	{
		return 0;
	}
#endif

#if 0
	// 画像の中心のＲＧＢ
	cnt = 1;
	index = graBuf[(height/2)*width+width/2];
	red   = pPalette[index].peRed;
	green = pPalette[index].peGreen;
	blue  = pPalette[index].peBlue;
	
#else
	// 画像のＲＧＢ成分を平均する
	for( i = 0; i < height; i++ )
	{
		for( j = 0; j < width; j++ )
		{
			index = graBuf[i*width+j];
			if( index != DEF_COLORKEY )	// 透过色は处理しない
			{
				red   += pPalette[index].peRed;
				green += pPalette[index].peGreen;
				blue  += pPalette[index].peBlue;
				cnt++;
			}
		}
	}
#endif

	if( cnt == 0 )
		return 0;

	// 平均色に一番近い色のパレットインデックスを探す
	color = getNearestColorIndex( RGB( red/cnt, green/cnt, blue/cnt ), pPalette, 256 );

	return color;
}


//---------------------------------------------------------------------------//
// 色数entryのパレットpaletteの中から、colorに一番近いもののindexを返す。
//---------------------------------------------------------------------------//
int getNearestColorIndex( COLORREF color, PALETTEENTRY *palette, int entry )
{
	double distance, mindist;
	int min_index;
	int i;

	mindist = (palette[0].peRed - GetRValue(color))*(palette[0].peRed - GetRValue(color))
		      + (palette[0].peGreen - GetGValue(color))*(palette[0].peGreen - GetGValue(color))
			  +(palette[0].peBlue - GetBValue(color))*(palette[0].peBlue - GetBValue(color));
	min_index = 0;

	for( i = 16; i < entry - 16; i++ )
	{
		distance = (palette[i].peRed - GetRValue(color))*(palette[i].peRed - GetRValue(color))
			+ (palette[i].peGreen - GetGValue(color))*(palette[i].peGreen - GetGValue(color))
			+(palette[i].peBlue - GetBValue(color))*(palette[i].peBlue - GetBValue(color));
		if( distance < mindist )
		{
			min_index = i;
			mindist = distance;
		}
	}

	return min_index;
}



//---------------------------------------------------------------------------//
// マップエフェクトを描画                                                    //
//---------------------------------------------------------------------------//
void DrawMapEffect8( void )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
	char *ptDest;		// 転送先のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int ww, hh;
	MAP_EFFECT *buf;
	char color;
	int i, j;


	ww = DEF_APPSIZEX;
	hh = DEF_APPSIZEY;

	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpBACKBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}
	// サーフェスの横幅记忆
	surfacePitch = ddsd.lPitch;


	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( 0 <= buf->x && buf->x < ww-4		//01/07/19 oft
		 && 0 <= buf->y && buf->y < hh-12 )		//01/07/19 oft
		{
			ptDest = (char *)(ddsd.lpSurface)
				+ buf->y * surfacePitch + buf->x;
			if( buf->type == MAP_EFFECT_TYPE_RAIN )
			{
				*(ptDest) = (char)143;
				*(ptDest+surfacePitch) = (char)143;
				*(ptDest+surfacePitch*2+1) = (char)143;
				*(ptDest+surfacePitch*3+1) = (char)143;
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_SNOW )
			{
				if( buf->mode == 0 )
				{
					*(ptDest)   = (char)161;
					*(ptDest+1) = (char)159;
					*(ptDest+2) = (char)161;
					*(ptDest+surfacePitch)   = (char)159;
					*(ptDest+surfacePitch+1) = (char)159;
					*(ptDest+surfacePitch+2) = (char)159;
					*(ptDest+surfacePitch*2)   = (char)161;
					*(ptDest+surfacePitch*2+1) = (char)159;
					*(ptDest+surfacePitch*2+2) = (char)161;
				}
				else
				if( buf->mode == 1 )
				{
					*(ptDest)   = (char)162;
					*(ptDest+1) = (char)159;
					*(ptDest+2) = (char)159;
					*(ptDest+3) = (char)162;
					*(ptDest+surfacePitch)   = (char)159;
					*(ptDest+surfacePitch+1) = (char)159;
					*(ptDest+surfacePitch+2) = (char)159;
					*(ptDest+surfacePitch+3) = (char)159;
					*(ptDest+surfacePitch*2)   = (char)159;
					*(ptDest+surfacePitch*2+1) = (char)159;
					*(ptDest+surfacePitch*2+2) = (char)159;
					*(ptDest+surfacePitch*2+3) = (char)159;
					*(ptDest+surfacePitch*3)   = (char)162;
					*(ptDest+surfacePitch*3+1) = (char)159;
					*(ptDest+surfacePitch*3+2) = (char)159;
					*(ptDest+surfacePitch*3+3) = (char)162;
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_STAR )
			{
				if( buf->mode == 0 )
				{
					// 星（白、丸）
					*(ptDest-surfacePitch-1) = (char)8;
					*(ptDest-surfacePitch) = (char)255;
					*(ptDest-surfacePitch+1) = (char)8;
					*(ptDest-1) = (char)255;
					*(ptDest) = (char)255;
					*(ptDest+1) = (char)255;
					*(ptDest+surfacePitch-1) = (char)8;
					*(ptDest+surfacePitch) = (char)255;
					*(ptDest+surfacePitch+1) = (char)8;
				}
				else
				if( buf->mode == 1 )
				{
					// 星（黄色、十字）
					*(ptDest-surfacePitch) = (char)251;
					*(ptDest-1) = (char)251;
					*(ptDest) = (char)251;
					*(ptDest+1) = (char)251;
					*(ptDest+surfacePitch) = (char)251;
				}
				else
				if( buf->mode == 2 )
				{
					// 星（黄色??白、４点）
					*(ptDest) = (char)251;
					*(ptDest+1) = (char)255;
					*(ptDest+surfacePitch) = (char)255;
					*(ptDest+surfacePitch+1) = (char)251;
				}
				else
				if( buf->mode == 3 )
				{
					// 星（白??黄色、４点）
					*(ptDest) = (char)255;
					*(ptDest+1) = (char)251;
					*(ptDest+surfacePitch) = (char)251;
					*(ptDest+surfacePitch+1) = (char)255;
				}
				else
				if( buf->mode == 4 )
				{
					// 星（黄色１点??橙３点）
					*(ptDest) = (char)251;
					*(ptDest+1) = (char)193;
					*(ptDest+surfacePitch) = (char)193;
					*(ptDest+surfacePitch+1) = (char)193;
				}
				else
				if( buf->mode == 5 )
				{
					// 星（薄黄色１）
					*(ptDest) = (char)198;
				}
				else
				if( buf->mode == 6 )
				{
					// 星（薄黄色２）
					*(ptDest) = (char)193;
				}
				else
				if( buf->mode == 7 )
				{
					// 星（薄黄色３）
					*(ptDest) = (char)208;
				}
				else
				if( buf->mode == 8 )
				{
					// 星（黄色）
					*(ptDest) = (char)251;
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_KAMIFUBUKI )
			{
				// 纸ふぶき
				if( buf->mode == 0 )
				{
					if( buf->type2 == 0 )
					{
						color = (char)223;
					}
					else
					if( buf->type2 == 1 )
					{
						color = (char)217;
					}
					else
					{
						color = (char)159;
					}
				}
				else
				if( buf->mode == 1 )
				{
					if( buf->type2 == 0 )
					{
						color = (char)212;
					}
					else
					if( buf->type2 == 1 )
					{
						color = (char)208;
					}
					else
					{
						color = (char)159;
					}
				}
				else
				if( buf->mode == 2 )
				{
					if( buf->type2 == 0 )
					{
						color = (char)130;
					}
					else
					if( buf->type2 == 1 )
					{
						color = (char)147;
					}
					else
					{
						color = (char)159;
					}
				}
				else
				{
					if( buf->type2 == 0 )
					{
						color = (char)116;
					}
					else
					if( buf->type2 == 1 )
					{
						color = (char)99;
					}
					else
					{
						color = (char)159;
					}
				}
				for( i = 0; i < buf->h; i++ )
				{
					for( j = 0; j < buf->w; j++ )
					{
#if 1
						if( 0 <= (buf->ex+j) && (buf->ex+j) < ww
						 && 0 <= (buf->ey+i) && (buf->ey+i) < hh )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#else
						if( (j+buf->ex) < ww-8 )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#endif
					}
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_HOTARU )
			{
			}
		}

		buf = buf->next;
	}



	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpBACKBUFFER->Unlock( NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}

	return;
}

void DrawMapEffect16( void )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
	unsigned short *ptDest;		// 転送先のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int ww, hh;
	MAP_EFFECT *buf;
	unsigned short color;
	int i, j;


	ww = DEF_APPSIZEX;
	hh = DEF_APPSIZEY;

	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpBACKBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}
	// サーフェスの横幅记忆
#ifdef PUK2
	surfacePitch = ddsd.lPitch >> 1;
#else
//	surfacePitch = ddsd.lPitch;
	surfacePitch = 640;
#endif

	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( 0 <= buf->x && buf->x < ww-4		//01/07/19 oft
		 && 0 <= buf->y && buf->y < hh-12 )		//01/07/19 oft
		{
			ptDest = (unsigned short *)(ddsd.lpSurface) + buf->y * surfacePitch + buf->x;
			if( buf->type == MAP_EFFECT_TYPE_RAIN )
			{
				*(ptDest) = highColorPalette[143];
				*(ptDest+surfacePitch) = highColorPalette[143];
				*(ptDest+surfacePitch*2+1) = highColorPalette[143];
				*(ptDest+surfacePitch*3+1) = highColorPalette[143];
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_SNOW )
			{
				if( buf->mode == 0 )
				{
					*(ptDest)   = highColorPalette[161];
					*(ptDest+1) = highColorPalette[159];
					*(ptDest+2) = highColorPalette[161];
					*(ptDest+surfacePitch)   = highColorPalette[159];
					*(ptDest+surfacePitch+1) = highColorPalette[159];
					*(ptDest+surfacePitch+2) = highColorPalette[159];
					*(ptDest+surfacePitch*2)   = highColorPalette[161];
					*(ptDest+surfacePitch*2+1) = highColorPalette[159];
					*(ptDest+surfacePitch*2+2) = highColorPalette[161];
				}
				else
				if( buf->mode == 1 )
				{
					*(ptDest)   = highColorPalette[162];
					*(ptDest+1) = highColorPalette[159];
					*(ptDest+2) = highColorPalette[159];
					*(ptDest+3) = highColorPalette[162];
					*(ptDest+surfacePitch)   = highColorPalette[159];
					*(ptDest+surfacePitch+1) = highColorPalette[159];
					*(ptDest+surfacePitch+2) = highColorPalette[159];
					*(ptDest+surfacePitch+3) = highColorPalette[159];
					*(ptDest+surfacePitch*2)   = highColorPalette[159];
					*(ptDest+surfacePitch*2+1) = highColorPalette[159];
					*(ptDest+surfacePitch*2+2) = highColorPalette[159];
					*(ptDest+surfacePitch*2+3) = highColorPalette[159];
					*(ptDest+surfacePitch*3)   = highColorPalette[162];
					*(ptDest+surfacePitch*3+1) = highColorPalette[159];
					*(ptDest+surfacePitch*3+2) = highColorPalette[159];
					*(ptDest+surfacePitch*3+3) = highColorPalette[162];
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_STAR )
			{
				if( buf->mode == 0 )
				{
					// 星（白、丸）
					*(ptDest-surfacePitch-1) = highColorPalette[8];
					*(ptDest-surfacePitch) = highColorPalette[255];
					*(ptDest-surfacePitch+1) = highColorPalette[8];
					*(ptDest-1) = highColorPalette[255];
					*(ptDest) = highColorPalette[255];
					*(ptDest+1) = highColorPalette[255];
					*(ptDest+surfacePitch-1) = highColorPalette[8];
					*(ptDest+surfacePitch) = highColorPalette[255];
					*(ptDest+surfacePitch+1) = highColorPalette[8];
				}
				else
				if( buf->mode == 1 )
				{
					// 星（黄色、十字）
					*(ptDest-surfacePitch) = highColorPalette[251];
					*(ptDest-1) = highColorPalette[251];
					*(ptDest) = highColorPalette[251];
					*(ptDest+1) = highColorPalette[251];
					*(ptDest+surfacePitch) = highColorPalette[251];
				}
				else
				if( buf->mode == 2 )
				{
					// 星（黄色??白、４点）
					*(ptDest) = highColorPalette[251];
					*(ptDest+1) = highColorPalette[255];
					*(ptDest+surfacePitch) = highColorPalette[255];
					*(ptDest+surfacePitch+1) = highColorPalette[251];
				}
				else
				if( buf->mode == 3 )
				{
					// 星（白??黄色、４点）
					*(ptDest) = highColorPalette[255];
					*(ptDest+1) = highColorPalette[251];
					*(ptDest+surfacePitch) = highColorPalette[251];
					*(ptDest+surfacePitch+1) = highColorPalette[255];
				}
				else
				if( buf->mode == 4 )
				{
					// 星（黄色１点??橙３点）
					*(ptDest) = highColorPalette[251];
					*(ptDest+1) = highColorPalette[193];
					*(ptDest+surfacePitch) = highColorPalette[193];
					*(ptDest+surfacePitch+1) = highColorPalette[193];
				}
				else
				if( buf->mode == 5 )
				{
					// 星（薄黄色１）
					*(ptDest) = highColorPalette[198];
				}
				else
				if( buf->mode == 6 )
				{
					// 星（薄黄色２）
					*(ptDest) = highColorPalette[193];
				}
				else
				if( buf->mode == 7 )
				{
					// 星（薄黄色３）
					*(ptDest) = highColorPalette[208];
				}
				else
				if( buf->mode == 8 )
				{
					// 星（黄色）
					*(ptDest) = highColorPalette[251];
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_KAMIFUBUKI )
			{
				// 纸ふぶき
				if( buf->mode == 0 )
				{
					if( buf->type2 == 0 )
					{
						color = highColorPalette[223];
					}
					else
					if( buf->type2 == 1 )
					{
						color = highColorPalette[217];
					}
					else
					{
						color = highColorPalette[159];
					}
				}
				else
				if( buf->mode == 1 )
				{
					if( buf->type2 == 0 )
					{
						color = highColorPalette[212];
					}
					else
					if( buf->type2 == 1 )
					{
						color = highColorPalette[208];
					}
					else
					{
						color = highColorPalette[159];
					}
				}
				else
				if( buf->mode == 2 )
				{
					if( buf->type2 == 0 )
					{
						color = highColorPalette[130];
					}
					else
					if( buf->type2 == 1 )
					{
						color = highColorPalette[147];
					}
					else
					{
						color = highColorPalette[159];
					}
				}
				else
				{
					if( buf->type2 == 0 )
					{
						color = highColorPalette[116];
					}
					else
					if( buf->type2 == 1 )
					{
						color = highColorPalette[99];
					}
					else
					{
						color = highColorPalette[159];
					}
				}
				for( i = 0; i < buf->h; i++ )
				{
					for( j = 0; j < buf->w; j++ )
					{
#if 1
						if( 0 <= (buf->ex+j) && (buf->ex+j) < ww
						 && 0 <= (buf->ey+i) && (buf->ey+i) < hh )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#else
						if( (j+buf->ex) < ww-8 )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#endif
					}
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_HOTARU )
			{
			}
		}

		buf = buf->next;
	}



	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpBACKBUFFER->Unlock( NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}

	return;
}

void DrawMapEffect32( void )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
	unsigned long *ptDest;		// 転送先のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int ww, hh;
	MAP_EFFECT *buf;
	unsigned long color;
	int i, j;


	ww = DEF_APPSIZEX;
	hh = DEF_APPSIZEY;

	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpBACKBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}
	// サーフェスの横幅记忆
#ifdef PUK2
	surfacePitch = ddsd.lPitch >> 2;
#else
//	surfacePitch = ddsd.lPitch;
	surfacePitch = 640;
#endif


	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( 0 <= buf->x && buf->x < ww-4		//01/07/19 oft
		 && 0 <= buf->y && buf->y < hh-12 )		//01/07/19 oft
		{
			ptDest = (unsigned long *)(ddsd.lpSurface) + buf->y * surfacePitch + buf->x;
			if( buf->type == MAP_EFFECT_TYPE_RAIN )
			{
				*(ptDest) = fullColorPalette[143];
				*(ptDest+surfacePitch) = fullColorPalette[143];
				*(ptDest+surfacePitch*2+1) = fullColorPalette[143];
				*(ptDest+surfacePitch*3+1) = fullColorPalette[143];
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_SNOW )
			{
				if( buf->mode == 0 )
				{
					*(ptDest)   = fullColorPalette[161];
					*(ptDest+1) = fullColorPalette[159];
					*(ptDest+2) = fullColorPalette[161];
					*(ptDest+surfacePitch)   = fullColorPalette[159];
					*(ptDest+surfacePitch+1) = fullColorPalette[159];
					*(ptDest+surfacePitch+2) = fullColorPalette[159];
					*(ptDest+surfacePitch*2)   = fullColorPalette[161];
					*(ptDest+surfacePitch*2+1) = fullColorPalette[159];
					*(ptDest+surfacePitch*2+2) = fullColorPalette[161];
				}
				else
				if( buf->mode == 1 )
				{
					*(ptDest)   = fullColorPalette[162];
					*(ptDest+1) = fullColorPalette[159];
					*(ptDest+2) = fullColorPalette[159];
					*(ptDest+3) = fullColorPalette[162];
					*(ptDest+surfacePitch)   = fullColorPalette[159];
					*(ptDest+surfacePitch+1) = fullColorPalette[159];
					*(ptDest+surfacePitch+2) = fullColorPalette[159];
					*(ptDest+surfacePitch+3) = fullColorPalette[159];
					*(ptDest+surfacePitch*2)   = fullColorPalette[159];
					*(ptDest+surfacePitch*2+1) = fullColorPalette[159];
					*(ptDest+surfacePitch*2+2) = fullColorPalette[159];
					*(ptDest+surfacePitch*2+3) = fullColorPalette[159];
					*(ptDest+surfacePitch*3)   = fullColorPalette[162];
					*(ptDest+surfacePitch*3+1) = fullColorPalette[159];
					*(ptDest+surfacePitch*3+2) = fullColorPalette[159];
					*(ptDest+surfacePitch*3+3) = fullColorPalette[162];
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_STAR )
			{
				if( buf->mode == 0 )
				{
					// 星（白、丸）
					*(ptDest-surfacePitch-1) = fullColorPalette[8];
					*(ptDest-surfacePitch) = fullColorPalette[255];
					*(ptDest-surfacePitch+1) = fullColorPalette[8];
					*(ptDest-1) = fullColorPalette[255];
					*(ptDest) = fullColorPalette[255];
					*(ptDest+1) = fullColorPalette[255];
					*(ptDest+surfacePitch-1) = fullColorPalette[8];
					*(ptDest+surfacePitch) = fullColorPalette[255];
					*(ptDest+surfacePitch+1) = fullColorPalette[8];
				}
				else
				if( buf->mode == 1 )
				{
					// 星（黄色、十字）
					*(ptDest-surfacePitch) = fullColorPalette[251];
					*(ptDest-1) = fullColorPalette[251];
					*(ptDest) = fullColorPalette[251];
					*(ptDest+1) = fullColorPalette[251];
					*(ptDest+surfacePitch) = fullColorPalette[251];
				}
				else
				if( buf->mode == 2 )
				{
					// 星（黄色??白、４点）
					*(ptDest) = fullColorPalette[251];
					*(ptDest+1) = fullColorPalette[255];
					*(ptDest+surfacePitch) = fullColorPalette[255];
					*(ptDest+surfacePitch+1) = fullColorPalette[251];
				}
				else
				if( buf->mode == 3 )
				{
					// 星（白??黄色、４点）
					*(ptDest) = fullColorPalette[255];
					*(ptDest+1) = fullColorPalette[251];
					*(ptDest+surfacePitch) = fullColorPalette[251];
					*(ptDest+surfacePitch+1) = fullColorPalette[255];
				}
				else
				if( buf->mode == 4 )
				{
					// 星（黄色１点??橙３点）
					*(ptDest) = fullColorPalette[251];
					*(ptDest+1) = fullColorPalette[193];
					*(ptDest+surfacePitch) = fullColorPalette[193];
					*(ptDest+surfacePitch+1) = fullColorPalette[193];
				}
				else
				if( buf->mode == 5 )
				{
					// 星（薄黄色１）
					*(ptDest) = fullColorPalette[198];
				}
				else
				if( buf->mode == 6 )
				{
					// 星（薄黄色２）
					*(ptDest) = fullColorPalette[193];
				}
				else
				if( buf->mode == 7 )
				{
					// 星（薄黄色３）
					*(ptDest) = fullColorPalette[208];
				}
				else
				if( buf->mode == 8 )
				{
					// 星（黄色）
					*(ptDest) = fullColorPalette[251];
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_KAMIFUBUKI )
			{
				// 纸ふぶき
				if( buf->mode == 0 )
				{
					if( buf->type2 == 0 )
					{
						color = fullColorPalette[223];
					}
					else
					if( buf->type2 == 1 )
					{
						color = fullColorPalette[217];
					}
					else
					{
						color = fullColorPalette[159];
					}
				}
				else
				if( buf->mode == 1 )
				{
					if( buf->type2 == 0 )
					{
						color = fullColorPalette[212];
					}
					else
					if( buf->type2 == 1 )
					{
						color = fullColorPalette[208];
					}
					else
					{
						color = fullColorPalette[159];
					}
				}
				else
				if( buf->mode == 2 )
				{
					if( buf->type2 == 0 )
					{
						color = fullColorPalette[130];
					}
					else
					if( buf->type2 == 1 )
					{
						color = fullColorPalette[147];
					}
					else
					{
						color = fullColorPalette[159];
					}
				}
				else
				{
					if( buf->type2 == 0 )
					{
						color = fullColorPalette[116];
					}
					else
					if( buf->type2 == 1 )
					{
						color = fullColorPalette[99];
					}
					else
					{
						color = fullColorPalette[159];
					}
				}
				for( i = 0; i < buf->h; i++ )
				{
					for( j = 0; j < buf->w; j++ )
					{
#if 1
						if( 0 <= (buf->ex+j) && (buf->ex+j) < ww
						 && 0 <= (buf->ey+i) && (buf->ey+i) < hh )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#else
						if( (j+buf->ex) < ww-8 )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#endif
					}
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_HOTARU )
			{
			}
		}

		buf = buf->next;
	}



	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpBACKBUFFER->Unlock( NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}

	return;
}



void DrawMapEffect24( void )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
	COL24BIT *ptDest;		// 転送先のポインタ
	int surfacePitch;	// サーフェスの横幅记忆
	int ww, hh;
	MAP_EFFECT *buf;
	COL24BIT color;
	int i, j;


	ww = DEF_APPSIZEX;
	hh = DEF_APPSIZEY;

	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif

	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpBACKBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}
	// サーフェスの横幅记忆
#ifdef PUK2
	surfacePitch = ddsd.lPitch / 3;
#else
//	surfacePitch = ddsd.lPitch;
	surfacePitch = 640;
#endif
	

	buf = useBufMapEffect;
	while( buf != (MAP_EFFECT *)NULL )
	{
		if( 0 <= buf->x && buf->x < ww-4		//01/07/19 oft
		 && 0 <= buf->y && buf->y < hh-12 )		//01/07/19 oft
		{
			ptDest = (COL24BIT *)(ddsd.lpSurface) + buf->y * surfacePitch + buf->x;
			if( buf->type == MAP_EFFECT_TYPE_RAIN )
			{
				*(ptDest) = Color24Palette[143];
				*(ptDest+surfacePitch) = Color24Palette[143];
				*(ptDest+surfacePitch*2+1) = Color24Palette[143];
				*(ptDest+surfacePitch*3+1) = Color24Palette[143];
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_SNOW )
			{
				if( buf->mode == 0 )
				{
					*(ptDest)   = Color24Palette[161];
					*(ptDest+1) = Color24Palette[159];
					*(ptDest+2) = Color24Palette[161];
					*(ptDest+surfacePitch)   = Color24Palette[159];
					*(ptDest+surfacePitch+1) = Color24Palette[159];
					*(ptDest+surfacePitch+2) = Color24Palette[159];
					*(ptDest+surfacePitch*2)   = Color24Palette[161];
					*(ptDest+surfacePitch*2+1) = Color24Palette[159];
					*(ptDest+surfacePitch*2+2) = Color24Palette[161];
				}
				else
				if( buf->mode == 1 )
				{
					*(ptDest)   = Color24Palette[162];
					*(ptDest+1) = Color24Palette[159];
					*(ptDest+2) = Color24Palette[159];
					*(ptDest+3) = Color24Palette[162];
					*(ptDest+surfacePitch)   = Color24Palette[159];
					*(ptDest+surfacePitch+1) = Color24Palette[159];
					*(ptDest+surfacePitch+2) = Color24Palette[159];
					*(ptDest+surfacePitch+3) = Color24Palette[159];
					*(ptDest+surfacePitch*2)   = Color24Palette[159];
					*(ptDest+surfacePitch*2+1) = Color24Palette[159];
					*(ptDest+surfacePitch*2+2) = Color24Palette[159];
					*(ptDest+surfacePitch*2+3) = Color24Palette[159];
					*(ptDest+surfacePitch*3)   = Color24Palette[162];
					*(ptDest+surfacePitch*3+1) = Color24Palette[159];
					*(ptDest+surfacePitch*3+2) = Color24Palette[159];
					*(ptDest+surfacePitch*3+3) = Color24Palette[162];
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_STAR )
			{
				if( buf->mode == 0 )
				{
					// 星（白、丸）
					*(ptDest-surfacePitch-1) = Color24Palette[8];
					*(ptDest-surfacePitch) = Color24Palette[255];
					*(ptDest-surfacePitch+1) = Color24Palette[8];
					*(ptDest-1) = Color24Palette[255];
					*(ptDest) = Color24Palette[255];
					*(ptDest+1) = Color24Palette[255];
					*(ptDest+surfacePitch-1) = Color24Palette[8];
					*(ptDest+surfacePitch) = Color24Palette[255];
					*(ptDest+surfacePitch+1) = Color24Palette[8];
				}
				else
				if( buf->mode == 1 )
				{
					// 星（黄色、十字）
					*(ptDest-surfacePitch) = Color24Palette[251];
					*(ptDest-1) = Color24Palette[251];
					*(ptDest) = Color24Palette[251];
					*(ptDest+1) = Color24Palette[251];
					*(ptDest+surfacePitch) = Color24Palette[251];
				}
				else
				if( buf->mode == 2 )
				{
					// 星（黄色??白、４点）
					*(ptDest) = Color24Palette[251];
					*(ptDest+1) = Color24Palette[255];
					*(ptDest+surfacePitch) = Color24Palette[255];
					*(ptDest+surfacePitch+1) = Color24Palette[251];
				}
				else
				if( buf->mode == 3 )
				{
					// 星（白??黄色、４点）
					*(ptDest) = Color24Palette[255];
					*(ptDest+1) = Color24Palette[251];
					*(ptDest+surfacePitch) = Color24Palette[251];
					*(ptDest+surfacePitch+1) = Color24Palette[255];
				}
				else
				if( buf->mode == 4 )
				{
					// 星（黄色１点??橙３点）
					*(ptDest) = Color24Palette[251];
					*(ptDest+1) = Color24Palette[193];
					*(ptDest+surfacePitch) = Color24Palette[193];
					*(ptDest+surfacePitch+1) = Color24Palette[193];
				}
				else
				if( buf->mode == 5 )
				{
					// 星（薄黄色１）
					*(ptDest) = Color24Palette[198];
				}
				else
				if( buf->mode == 6 )
				{
					// 星（薄黄色２）
					*(ptDest) = Color24Palette[193];
				}
				else
				if( buf->mode == 7 )
				{
					// 星（薄黄色３）
					*(ptDest) = Color24Palette[208];
				}
				else
				if( buf->mode == 8 )
				{
					// 星（黄色）
					*(ptDest) = Color24Palette[251];
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_KAMIFUBUKI )
			{
				// 纸ふぶき
				if( buf->mode == 0 )
				{
					if( buf->type2 == 0 )
					{
						color = Color24Palette[223];
					}
					else
					if( buf->type2 == 1 )
					{
						color = Color24Palette[217];
					}
					else
					{
						color = Color24Palette[159];
					}
				}
				else
				if( buf->mode == 1 )
				{
					if( buf->type2 == 0 )
					{
						color = Color24Palette[212];
					}
					else
					if( buf->type2 == 1 )
					{
						color = Color24Palette[208];
					}
					else
					{
						color = Color24Palette[159];
					}
				}
				else
				if( buf->mode == 2 )
				{
					if( buf->type2 == 0 )
					{
						color = Color24Palette[130];
					}
					else
					if( buf->type2 == 1 )
					{
						color = Color24Palette[147];
					}
					else
					{
						color = Color24Palette[159];
					}
				}
				else
				{
					if( buf->type2 == 0 )
					{
						color = Color24Palette[116];
					}
					else
					if( buf->type2 == 1 )
					{
						color = Color24Palette[99];
					}
					else
					{
						color = Color24Palette[159];
					}
				}
				for( i = 0; i < buf->h; i++ )
				{
					for( j = 0; j < buf->w; j++ )
					{
#if 1
						if( 0 <= (buf->ex+j) && (buf->ex+j) < ww
						 && 0 <= (buf->ey+i) && (buf->ey+i) < hh )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#else
						if( (j+buf->ex) < ww-8 )
						{
							*(ptDest+surfacePitch*(i+buf->ey)+j+buf->ex) = color;
						}
#endif
					}
				}
			}
			else
			if( buf->type == MAP_EFFECT_TYPE_HOTARU )
			{
			}
		}

		buf = buf->next;
	}



	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpBACKBUFFER->Unlock( NULL ) != DD_OK )
	{
		//MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}

	return;
}


void DrawMapEffect( void )
{
	if( displayBpp == 8 ){
		DrawMapEffect8( );
	}else
	if(	displayBpp == 16 ){
		DrawMapEffect16( );
	}else
	if(	displayBpp == 24 ){
		DrawMapEffect24( );
	}else{
		DrawMapEffect32( );
	}
}



// 处理にかかった时间を描画（ デバッグ用 ）************************************/
void DrawDebugLine( unsigned char color )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsd;	// サーフェス构造体
#else
	DDSURFACEDESC ddsd;	// サーフェス构造体
#endif
    char *pDest;		// 転送先のポインタ
    short *pDest2;		// 転送先のポインタ（ワード型）
    long *pDest3;		// 転送先のポインタ（ロング型）
    COL24BIT *pDest24;	// 転送先のポインタ（３バイト型）
	int surfacePitch;	// サーフェスの横幅记忆
	int i, j, k, l, m;
	short pixel;
	long pixel2;
	COL24BIT pixel24;
	
	// ウィンドウモードなら返回
	if( WindowMode ) return;
	
	// 构造体の初期化
#ifdef PUK2
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC2 ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC2 );
#else
	ZeroMemory( &ddsd, sizeof( DDSURFACEDESC ) );
	ddsd.dwSize = sizeof( DDSURFACEDESC );
#endif
	
	// アクセスするサーフェスをロックする( 同时に ddsd に情报を入れてもらう )
	if( lpDraw->lpFRONTBUFFER->Lock( NULL, &ddsd, DDLOCK_WAIT, NULL ) != DD_OK ){
		//MessageBox( hWnd, "Surface锁定失败", "确认", MB_OK | MB_ICONSTOP );
		return; 
	}	
	//Ｘ座标
	j = DEF_APPSIZEX-6;
	k = DEF_APPSIZEY;
	l = 4;
	
	if( displayBpp == 24 )
	{
		pixel24 = Color24Palette[color];
		// 涂り始めるサーフェスのアドレス
		pDest24 = (COL24BIT *)ddsd.lpSurface + j;
		// サーフェスの横幅记忆
		surfacePitch = ddsd.lPitch / 3 ;
		// 縦ループ
		for( i = 0 ; i < k ; i++ )
		{
			// ＬＩＮＥを描画
			for( m = 0; m < l; m++ )
			{
				*(pDest24+m) = pixel24;
			}
			// サーフェスの横幅分アドレスを进める
			pDest24 += surfacePitch;
    	}
	}else
	if( displayBpp == 32 )
	{
		pixel2 = fullColorPalette[color];
		// 涂り始めるサーフェスのアドレス
		pDest3 = (long *)ddsd.lpSurface + j;
		// サーフェスの横幅记忆
		surfacePitch = ddsd.lPitch >> 2;
		// 縦ループ
		for( i = 0 ; i < k ; i++ )
		{
			// ＬＩＮＥを描画
			for( m = 0; m < l; m++ )
			{
				*(pDest3+m) = pixel2;
			}
			// サーフェスの横幅分アドレスを进める
			pDest3 += surfacePitch;
    	}
	}else
	if( displayBpp == 16 )
	{
		pixel = highColorPalette[color];
		// 涂り始めるサーフェスのアドレス
		pDest2 = (short *)ddsd.lpSurface + j;
		// サーフェスの横幅记忆
		surfacePitch = ddsd.lPitch >> 1;
		// 縦ループ
		for( i = 0 ; i < k ; i++ )
		{
			// ＬＩＮＥを描画
			for( m = 0; m < l; m++ )
			{
				*(pDest2+m) = pixel;
			}
			// サーフェスの横幅分アドレスを进める
			pDest2 += surfacePitch;
    	}
	}else{
		// 涂り始めるサーフェスのアドレス
		pDest = ( char *)( ddsd.lpSurface ) + j;

		// サーフェスの横幅记忆
		surfacePitch = ddsd.lPitch;

		// 縦ループ
		for( i = 0 ; i < k ; i++ )
		{
			// ＬＩＮＥを描画
			memset( pDest, color, l );
			// サーフェスの横幅分アドレスを进める
			pDest += surfacePitch;
    	}
    }

	// アクセスするサーフェスをアンロックする
	if( lpDraw->lpFRONTBUFFER->Unlock( NULL ) != DD_OK ){
		MessageBox( hWnd, "Surface解锁失败", "确认", MB_OK | MB_ICONSTOP );       //MLHIDE
		return; 
	}	
	
	return;
}

#ifndef PUK2

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE lpSurface : 描画元サーフェス                   //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
HRESULT DrawSurfaceFast( short bx, short by, LPDIRECTDRAWSURFACE lpSurface )
{
	short x0, y0;
	long w, h;
	
	// 転送领域のセット
	//RECT rect = { 0, 0, SurfaceSizeX - 1, SurfaceSizeY - 1 }; // デバッグ枠付き
	RECT rect = { 0, 0, SurfaceSizeX, SurfaceSizeY };
	x0 = bx;
	y0 = by;
	w = rect.right - rect.left;
	h = rect.bottom - rect.top;

	// クリッピング处理
	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}
	
	// 左端のチェック
	if( bx < 0 ){
		rect.left -= bx;
		x0 = 0;
	}
	// 右端のチェック
	if( bx + w > lpDraw->xSize ){
		rect.right -= bx + w - lpDraw->xSize;
	}
	// 上端のチェック
	if( by < 0 ){
		rect.top -= by;
		y0 = 0;
	}
	// 下端のチェック
	if( by + h > lpDraw->ySize ){
		rect.bottom -= by + h - lpDraw->ySize;
	}
	
#ifdef _DEBUG		
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif
	
	// バックサーフェスへ高速転送
	return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
	
	//return lpDraw->lpBACKBUFFER->BltFast( bx, by, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
	//return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_WAIT );
	//return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY );
}

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE lpSurface : 描画元サーフェス                   //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
HRESULT DrawSurfaceStretch( short bx, short by, float scaleX, float scaleY, LPDIRECTDRAWSURFACE lpSurface )
{
	float x0, y0;
	float w, h;
	float top = 0, left = 0;
	float right = ( ( float )SurfaceSizeX ) * scaleX;
	float bottom = ( ( float )SurfaceSizeY ) * scaleY;
	
	// 転送领域のセット
	//RECT rect = { 0, 0, SurfaceSizeX - 1, SurfaceSizeY - 1 }; // デバッグ枠付き
	//RECT rect = { 0, 0, ( int )( ( float )SurfaceSizeX * scaleX ), ( int )( ( float )SurfaceSizeY * scaleY ) };
	x0 = ( float )bx;
	y0 = ( float )by;
	//w = ( int )( rect.right - rect.left ) * scaleX;
	//h = ( int )( rect.bottom - rect.top ) * scaleY;
	w = right - left;
	h = bottom - top;

	// クリッピング处理
	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}
	
	// 左端のチェック
	if( bx < 0 ){
		left -= ( float )bx;
		x0 = 0;
	}
	// 右端のチェック
	if( ( float )bx + w > ( float )lpDraw->xSize ){
		right -= ( float )bx + w - ( float )lpDraw->xSize;
	}
	// 上端のチェック
	if( by < 0 ){
		top -= ( float )by;
		y0 = 0;
	}
	// 下端のチェック
	if( ( float )by + h > ( float )lpDraw->ySize ){
		bottom -= ( float )by + h - ( float )lpDraw->ySize;
	}
	
#ifdef _DEBUG		
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif
	
	//rect.top = ( int )( top / scaleY );
	//rect.bottom = ( int )( bottom / scaleY );
	//rect.left = ( int )( left / scaleX );
	//rect.right = ( int )( rect.right / scaleX );
	
	RECT rect;
	// 転送もとの矩形をセット
	SetRect( &rect, ( int )( left / scaleX ), ( int )( top / scaleY ), ( int )( right / scaleX ), ( int )( bottom / scaleY ) );
	//SetRect( &rect, ( int )( left ), ( int )( top ), ( int )( right / scaleX ), ( int )( bottom / scaleY ) );
	
	// バックサーフェスへ高速転送
	//return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
	
	
	RECT rect2;
	
	// 隙间をなくすための处理
	// 右端のチェック
	if( x0 + right - left < ( float )lpDraw->xSize ) right += 1;
	
	// 下端のチェック
	if( y0 + bottom - top < ( float )lpDraw->ySize ) bottom += 1;
	
	// 転送先の矩形をセット
	SetRect( &rect2, ( int )x0, ( int )y0, ( int )( x0 + right - left ), ( int )( y0 + bottom - top ) );
	
	//SetRect( &rect2, x0, y0, x0 + rect.right - rect.left, y0 + rect.bottom - rect.top );
	//SetRect( &rect2, x0, y0, x0 + ( int )( ( float )( rect.right - rect.left ) * scaleX ), y0 + ( int )( ( float )( rect.bottom - rect.top ) * scaleY ) );
	
	
	return lpDraw->lpBACKBUFFER->Blt( &rect2, lpSurface, &rect, DDBLT_KEYSRC | DDBLT_WAIT, NULL );
	
	//return lpDraw->lpBACKBUFFER->BltFast( bx, by, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
	//return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_WAIT );
	//return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, &rect, DDBLTFAST_SRCCOLORKEY );
}

#endif

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        RECT * rect         : 描画元领域构造体                             //
//        LPDIRECTDRAWSURFACE lpSurface : 描画元サーフェス                   //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
#ifdef PUK2
HRESULT DrawSurfaceFast2( short bx, short by, RECT *rect, LPDIRECTDRAWSURFACE7 lpSurface )
#else
HRESULT DrawSurfaceFast2( short bx, short by, RECT *rect, LPDIRECTDRAWSURFACE lpSurface )
#endif
{
	short x0, y0;
	long w, h;
	
	x0 = bx;
	y0 = by;
	w = rect->right - rect->left;
	h = rect->bottom - rect->top;

	// クリッピング处理
	//   （ちなみに RECT の右下座标のドットは表示されない）

	// 全く表示する部分が无ければ返回
	if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
		return DD_OK;
	}
	
	// 左端のチェック
	if( bx < 0 ){
		rect->left -= bx;
		x0 = 0;
	}
	// 右端のチェック
	if( bx + w > lpDraw->xSize ){
		rect->right -= bx + w - lpDraw->xSize;
	}
	// 上端のチェック
	if( by < 0 ){
		rect->top -= by;
		y0 = 0;
	}
	// 下端のチェック
	if( by + h > lpDraw->ySize ){
		rect->bottom -= by + h - lpDraw->ySize;
	}
	
#ifdef _DEBUG		
	// 现在表示しているサーフェスの数カウント
	SurfaceDispCnt++;
#endif
	
	// バックサーフェスへ高速転送
	//return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, rect, DDBLTFAST_SRCCOLORKEY | DDBLTFAST_WAIT );
	return lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpSurface, rect, DDBLTFAST_WAIT );
}

/* オブジェクトを开放する ****************************************************/

#ifndef PUK2

void ReleaseDirectDraw( void )
{
	int i;
	
	// 全てのOFFスクリーンサーフェスを开放
	for( i = 0 ; i < SurfaceCnt ; i++ ){
		// サーフェスがあったら
		if( SurfaceInfo[ i ].lpSurface != NULL ){
			// リリース
			SurfaceInfo[ i ].lpSurface->Release();
			SurfaceInfo[ i ].lpSurface = NULL;
		}
	}
	// バトルサーフェスの开放
	if( lpBattleSurface != NULL ){
		// リリース
		lpBattleSurface->Release();
		lpBattleSurface = NULL;
	}
	
	// パレット解放
	if( lpDraw->lpPALETTE != NULL ){
		lpDraw->lpPALETTE->Release();
		lpDraw->lpPALETTE = NULL;
	}
	// クリッ布开放
	if( lpDraw->lpCLIPPER != NULL ){
		lpDraw->lpCLIPPER->Release();
		lpDraw->lpCLIPPER = NULL;
	}
	// バックサーフェス解放
	if( lpDraw->lpBACKBUFFER != NULL ){
		lpDraw->lpBACKBUFFER->Release();
		lpDraw->lpBACKBUFFER = NULL;
	}
	// プライマリサーフェス解放
	if( lpDraw->lpFRONTBUFFER != NULL ){
		lpDraw->lpFRONTBUFFER->Release();
		lpDraw->lpFRONTBUFFER = NULL;
	}
	// DirectDrawの开放
	if( lpDraw->lpDD2 != NULL){ 
		lpDraw->lpDD2->Release();
		lpDraw->lpDD2 = NULL;
	}
	// DIRECT_DRAW 构造体开放
	if( lpDraw != NULL){ 
#ifdef PUK2_MEMCHECK
		memlistrel( lpDraw, MEMLISTTYPE_STRUCT_DIRECT_DRAW );
#endif
		GlobalFreePtr( lpDraw );
		lpDraw = NULL;
	}
	
	// DirectDraw 初期化フラグを FALSE にする
	DDinitFlag = FALSE;	
}

#endif

// サーフェスロストのチェック关数 ////////////////////////////////////////////
BOOL CheckSurfaceLost( void )
{
	int i;
	BOOL SurfaceLostFlag = FALSE;
	
	// 全てのOFFスクリーンサーフェスを复归
	for( i = 0 ; i < SurfaceCnt ; i++ ){
		// サーフェスがあったら
		if( SurfaceInfo[ i ].lpSurface != NULL ){
			// サーフェスが坏れていたらリストア
			if( SurfaceInfo[ i ].lpSurface->IsLost() ){
				SurfaceLostFlag = TRUE;
			}
		}
	}
	
	// バトルサーフェスの复归
	if( lpBattleSurface != NULL ){
		// リリース
		if( lpBattleSurface->IsLost() ){
			SurfaceLostFlag = TRUE;
		}
	}
	
	// バックサーフェス复归
	if( lpDraw->lpBACKBUFFER != NULL ){
		// サーフェスが坏れていたらリストア
		if( lpDraw->lpBACKBUFFER->IsLost() ){
			SurfaceLostFlag = TRUE;
		}
	}
	// プライマリサーフェス复归
	if( lpDraw->lpFRONTBUFFER != NULL ){
		// サーフェスが坏れていたらリストア
		if( lpDraw->lpFRONTBUFFER->IsLost() ){
			SurfaceLostFlag = TRUE;
		}
	}
	
	return SurfaceLostFlag;
}	


DWORD ColorMask[3];
#ifdef PUK2
	// 24bppビットマップ书き込み用关数 ++++
	BOOL WriteBmp( unsigned char *l_bmp, int l_wid, int l_hei, const char *ls_filename );
#endif

//---------------------------------------------------------------------------//
// スクリーンショットの保存
//---------------------------------------------------------------------------//
void screenShot( void )
{
#ifdef PUK2
	DDSURFACEDESC2 ddsdDesc;
#else
	DDSURFACEDESC ddsdDesc;
#endif
	RGBQUAD rgbpal[256];
	char fileName[256];
	int i;
	struct tm *nowTime;
	time_t longTime;
	FILE *fp;
#ifdef PUK2
	int palcount = 0;
#else
	int w, h, palcount = 0;
#endif
	POINT 	clientPoint; // クライアント领域の左上のスクリーン座标位置を格纳
	HRESULT ret;
#ifdef PUK2
	unsigned char *srf, *buf;
#endif

	// ポイント构造体の初期化
	clientPoint.x = 0;
	clientPoint.y = 0;
	// クライアント领域の左上のスクリーン座标を取得
	ClientToScreen( hWnd, &clientPoint ); 

	// 保存フォルダの作成
	if( _mkdir( "screenshot" ) != 0 )                                    //MLHIDE
	{
		if( errno != EEXIST )
			return;
	}

	// 保存ファイル名の作成
	time( &longTime );					// 现在の日时を取得
	nowTime = localtime( &longTime );

	for( i =  0; i < 1000; i ++ )
	{
		sprintf( fileName, "screenshot\\CG%02d%02d%02d_%03d.bmp",           //MLHIDE
			(nowTime->tm_year % 100), nowTime->tm_mon+1, nowTime->tm_mday, i );
 
		if( (fp = fopen( fileName, "r" )) != NULL )                         //MLHIDE
		{
			// ファイルがあったら次のファイルに进む。
			fclose( fp );
			continue;
		}
		else
		{
			break;
		}
    }
	// １０００枚以上だったら
	if( i >= 1000 ){
		char szBuffer[256];
		sprintf( szBuffer, "今天已经无法保存图像了。" );                                //MLHIDE
		StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
		return ;
	}

	// パレット取得
//	if( WindowMode )
//	{
//	}
//	else
//	{
	if( displayBpp == 8 ){
		// ２５６色モード
		// フルスクリーンモード
		for( i = 0; i < 256; i++ )
		{
			rgbpal[i].rgbRed   = Palette[i].peRed;
			rgbpal[i].rgbGreen = Palette[i].peGreen;
			rgbpal[i].rgbBlue  = Palette[i].peBlue;
			rgbpal[i].rgbReserved = 0;
		}
	}else
	if( displayBpp == 16 || displayBpp == 32 ){
		ColorMask[0] = rBitMask;  // 何故か赤が０番目
		ColorMask[1] = gBitMask;
		ColorMask[2] = bBitMask;  // 何故か青が２番目
	}

//	}
	if( displayBpp == 8 ){
		palcount = 256;
	}else{
		palcount = 0;
	}


	char szBuffer[256];
#ifdef PUK2
	memset( &ddsdDesc, 0, sizeof( DDSURFACEDESC2 ) );
	ddsdDesc.dwSize = sizeof( DDSURFACEDESC2 );
#else
	memset( &ddsdDesc, 0, sizeof( DDSURFACEDESC ) );
	ddsdDesc.dwSize = sizeof( DDSURFACEDESC );
#endif
	//if( lpDraw->lpFRONTBUFFER->Lock( NULL, &ddsdDesc, 0, NULL ) != DD_OK ){
	if( ret = lpDraw->lpFRONTBUFFER->Lock( NULL, &ddsdDesc, DDLOCK_WAIT, NULL ) != DD_OK ){
		
		//一应Unlock
		lpDraw->lpFRONTBUFFER->Unlock( NULL );

		switch(ret){

			case DDERR_INVALIDOBJECT:	sprintf( szBuffer, "保存图像失败(1)。(%s)", fileName );break; //MLHIDE
			case DDERR_INVALIDPARAMS:	sprintf( szBuffer, "保存图像失败(2)。(%s)", fileName );break; //MLHIDE
			case DDERR_OUTOFMEMORY:		sprintf( szBuffer, "保存图像失败(3)。(%s)", fileName );break; //MLHIDE
			case DDERR_SURFACEBUSY:		sprintf( szBuffer, "保存图像失败(4)。(%s)", fileName );break; //MLHIDE
			case DDERR_SURFACELOST:		sprintf( szBuffer, "保存图像失败(5)。(%s)", fileName );break; //MLHIDE
			case DDERR_WASSTILLDRAWING:	sprintf( szBuffer, "保存图像失败(6)。(%s)", fileName );break; //MLHIDE
#ifdef PUK2_SCREENSHOT_DDOFF
#else
			default:					sprintf( szBuffer, "保存图像失败(7)。(%s)", fileName );break; //MLHIDE
#endif
		}
		 
		//DDERR_INVALIDOBJECT DirectDraw が、无效な DirectDraw オブジェクトのポインタを受け取った。
		//DDERR_INVALIDPARAMS メソッドに渡された 1 つ以上のパラメータが不正である
		//DDERR_OUTOFMEMORY DirectDraw が处理を行うのに十分なメモリがない
		//DDERR_SURFACEBUSY サーフェスがもう 1 つのスレッドでロックされているため、サーフェスへのアクセスが拒否された。
		//DDERR_SURFACELOST サーフェス メモリの不足のため、サーフェスへのアクセスが拒否された。
		//DDERR_WASSTILLDRAWING このサーフェスから、またはサーフェスへの前回のブリット处理が完了していない。 

		
#ifdef PUK2_SCREENSHOT_DDOFF
		// ＧＤＩで画面を取得
		{
			HDC hdc = GetDC(NULL), hcdc;
			HBITMAP hbmp, hbfobj;
			BITMAPINFO bi;
			BITMAPINFOHEADER *bih = &bi.bmiHeader;
	
			bih->biSize=sizeof(BITMAPINFOHEADER);				// この构造体のサイズ
			//Modified by LitChi
			bih->biWidth=640;									// 絵の幅
			bih->biHeight=480;									// 絵の高さ
			bih->biPlanes=1;									// 必ず 1
			bih->biBitCount=32;									// ＢＰＰ
			bih->biCompression=BI_RGB;							// 压缩の种类(BI_RGB は无压缩)
			bih->biSizeImage=0;									// 絵のサイズ(BI_RGB の场合 0 も可能)
			bih->biXPelsPerMeter=0;								// 水平解像度(基本的に 0)
			bih->biYPelsPerMeter=0;								// 垂直解像度(基本的に 0)
			bih->biClrUsed=0;									// 使用するカラーインデックス(基本的に 0)
			bih->biClrImportant=0;								// 必须のカラーインデックス(基本的に 0)
	
			//Modified by LitChi
			buf = (unsigned char *)malloc(640*480*4);
		#ifdef PUK2_MEMCHECK
				memlistset( buf, MEMLISTTYPE_SCREENSHOTBUF );
		#endif
	
			hcdc = CreateCompatibleDC(hdc);
			if (!hcdc){
				sprintf( szBuffer, "保存图像失败(G1)。(%s)", fileName );                 //MLHIDE
				goto GDI_ERR;
			}

			//Modified by LitChi
			hbmp = CreateCompatibleBitmap( hdc, 640, 480 );
			if (!hbmp){
				sprintf( szBuffer, "保存图像失败(G2)。(%s)", fileName );                 //MLHIDE
				goto GDI_ERR;
			}
			hbfobj = (HBITMAP)SelectObject( hcdc, hbmp );
	
			if ( !BitBlt( hcdc, 0, 0, 640, 480, hdc, clientPoint.x, clientPoint.y, SRCCOPY ) ){
				sprintf( szBuffer, "保存图像失败(G3)。(%s)", fileName );                 //MLHIDE
				goto GDI_ERR;
			}
	
			SelectObject( hcdc, hbfobj );
	
			if ( !GetDIBits( hcdc, hbmp, 0, 480, buf, &bi, DIB_RGB_COLORS ) ){
				sprintf( szBuffer, "保存图像失败(G4)。(%s)", fileName );                 //MLHIDE
				goto GDI_ERR;
			}
	
			DeleteObject(hbmp);
			ReleaseDC( hWnd, hcdc );
	
			ReleaseDC( hWnd, hdc );
	
			// 画像の保存 //Modified by LitChi
			if ( WriteBmp( buf, 640, 480, fileName ) ){
				sprintf( szBuffer, "图像保存成功。(%s)", fileName );                     //MLHIDE
			}else{
				sprintf( szBuffer, "保存图像失败(7)。(%s)", fileName );                  //MLHIDE
			}

GDI_ERR:
			// 信息表示
			StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	
			// バッファ解放
			#ifdef PUK2_MEMCHECK
				memlistrel( buf, MEMLISTTYPE_SCREENSHOTBUF );
			#endif
			free(buf);
		}
#else
		//sprintf( szBuffer, "保存图像失败。(%s)", fileName );
		StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
#endif
		return;
	}

#ifdef PUK2
	//Modified by LitChi
	srf = (unsigned char *)malloc(640*480*4);
	buf = (unsigned char *)malloc(640*480*4);
	#ifdef PUK2_MEMCHECK
		memlistset( srf, MEMLISTTYPE_SCREENSHOTSRF );
		memlistset( buf, MEMLISTTYPE_SCREENSHOTBUF );
	#endif
#endif
#ifdef PUK2
	// バッファにサーフェースの内容をコピー
	{
		char *pd = (char *)ddsdDesc.lpSurface + (ddsdDesc.lPitch*clientPoint.y) + ( clientPoint.x*(displayBpp>>3) );
		int len = 640 * (displayBpp>>3);
		for(i=0;i<480;i++){
			memcpy( srf + len*i, pd, len );
			pd += ddsdDesc.lPitch;
		}
	}
#else
	w = 640;
	h = 480;
	//Modified by LitChi

	if( saveBmpFile( fileName, (BYTE*)ddsdDesc.lpSurface,
		clientPoint.x, clientPoint.y, w, h, ddsdDesc.lPitch, rgbpal, palcount ) == FALSE 
	){
		char szBuffer[256];
		sprintf( szBuffer, "图像文件开打失败。(%s)", fileName );                     //MLHIDE
		StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	}else{
		char szBuffer[256];
		sprintf( szBuffer, "图像保存成功。(%s)", fileName );                       //MLHIDE
		StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	}
#endif

#ifdef PUK2
	if( lpDraw->lpFRONTBUFFER->Unlock( NULL ) != DD_OK ){
	#ifdef PUK2_MEMCHECK
		memlistrel( srf, MEMLISTTYPE_SCREENSHOTSRF );
		memlistrel( buf, MEMLISTTYPE_SCREENSHOTBUF );
	#endif
		free(srf);
		free(buf);
		return;
	}
#else
	if( lpDraw->lpFRONTBUFFER->Unlock( NULL ) != DD_OK )
		return;
#endif

#ifdef PUK2
	switch(displayBpp){
	case 16:
		{
			int i,j;
			COL24BIT *p24d;
			unsigned char *pcd;
			int dPitch = ( ( (640*3)+3 )>>2 ) << 2;
			unsigned short *pss;
			unsigned char *pcs;

			pcd = (unsigned char *)buf + dPitch * 480;
			pcs = (unsigned char *)srf;
			for(j=0;j<480;j++){
				pcd -= dPitch;
				p24d = (COL24BIT *)pcd;
				pss = (unsigned short *)pcs;
				for(i=0;i<640;i++){
					p24d[i].r = (pss[i]>>rBitLShift) << rBitRShift;
					p24d[i].g = (pss[i]>>gBitLShift) << gBitRShift;
					p24d[i].b = (pss[i]>>bBitLShift) << bBitRShift;
				}
				pcs += 640*2;
			}
			break;
		}
	case 24:
		{
			int i,j;
			COL24BIT *p24d;
			unsigned char *pcd;
			int dPitch = ( ( (640*3)+3 )>>2 ) << 2;
			COL24BIT *p24s;
			unsigned char *pcs;

			pcd = (unsigned char *)buf + dPitch * 480;
			pcs = (unsigned char *)srf;
			for(j=0;j<480;j++){
				pcd -= dPitch;
				p24d = (COL24BIT *)pcd;
				p24s = (COL24BIT *)pcs;
				for(i=0;i<640;i++){
					p24d[i].r = p24s[i].b;
					p24d[i].g = p24s[i].g;
					p24d[i].b = p24s[i].r;
				}
				pcs += 640 * 3;
			}
			break;
		}
		break;
	case 32:
		{
			int i,j;
			COL24BIT *p24d;
			unsigned char *pcd;
			int dPitch = ( ( (640*3)+3 )>>2 ) << 2;
			unsigned char (*pls)[4];
			unsigned char *pcs;

			pcd = (unsigned char *)buf + dPitch * 480;
			pcs = (unsigned char *)srf;
			for(j=0;j<480;j++){
				pcd -= dPitch;
				p24d = (COL24BIT *)pcd;
				pls = (unsigned char (*)[4])pcs;
				for(i=0;i<640;i++){
					p24d[i].r = pls[i][2];
					p24d[i].g = pls[i][1];
					p24d[i].b = pls[i][0];
				}
				pcs += 640 * 4;
			}
			break;
		}
		break;
	}
#endif

	// 画像の保存
	if ( WriteBmp( buf, 640, 480, fileName ) ){
		char szBuffer[256];
		sprintf( szBuffer, "图像保存成功。(%s)", fileName );                       //MLHIDE
		StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	}else{
		char szBuffer[256];
		sprintf( szBuffer, "保存图像失败。(%s)", fileName );                       //MLHIDE
		StockChatBufferLine( szBuffer, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	}

	// バッファ解放
	#ifdef PUK2_MEMCHECK
		memlistrel( srf, MEMLISTTYPE_SCREENSHOTSRF );
		memlistrel( buf, MEMLISTTYPE_SCREENSHOTBUF );
	#endif
	free(srf);
	free(buf);
}


// 画像をＢＭＰファイルに保存
BOOL saveBmpFile( const char *filename, BYTE *buf,
	int x, int y, int width, int height, int srcpitch,
	RGBQUAD *rgbpal, int colorCnt )
{
	HANDLE fh;
	BITMAPFILEHEADER fileheader;
	BITMAPINFOHEADER infoheader;
	int linesize = (width+3)/4;
	DWORD writtensize;
	BYTE zero = 0;
	int i, mul = 1;

	fh = CreateFile( filename, GENERIC_WRITE, (DWORD)NULL,
			(LPSECURITY_ATTRIBUTES)NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, (HANDLE)NULL );

	if( fh == INVALID_HANDLE_VALUE )
	{
		return FALSE;
	}


	mul = displayBpp/8;

	fileheader.bfType = 0x4D42;	// 'BM'をリトルエンディアン形式に
	fileheader.bfReserved1 = 0;	// 予约领域
	fileheader.bfReserved2 = 0;

	// 画像データへのOFFセットを计算。まずはヘッダ部分
	fileheader.bfOffBits = sizeof( BITMAPFILEHEADER ) +sizeof( BITMAPINFOHEADER );
	if( displayBpp == 8 ){	// ８ビットの场合はパレット分を追加
		fileheader.bfOffBits += sizeof(RGBQUAD)*(colorCnt);
	}else
	if( displayBpp == 16 || displayBpp == 32 ){ // １６ビットのビットフィールド设定值を追加
		fileheader.bfOffBits += sizeof(ColorMask);
	}
	
	fileheader.bfSize = fileheader.bfOffBits + linesize*height*mul;

	
	WriteFile( fh, (void*)(&fileheader), sizeof( fileheader ), &writtensize, NULL );


	infoheader.biSize = sizeof( BITMAPINFOHEADER );
	infoheader.biWidth = width;
	infoheader.biHeight = height;
	infoheader.biPlanes = 1;
	infoheader.biBitCount = displayBpp;	// １ドットのバイト数

//	infoheader.biCompression = NULL;

//	infoheader.biSizeImage = sizeof( BITMAPINFOHEADER ) +sizeof(ColorMask)+linesize*height*mul;

	if( displayBpp == 16 || displayBpp == 32 ){	
		// １６ビットと３２ビットではビットフィールド设定にする。
		infoheader.biCompression = BI_BITFIELDS;
	}else{
		// それ以外は普通の压缩无し形式
		infoheader.biCompression = BI_RGB;
	}
	infoheader.biSizeImage = 0;		// 压缩していないのでイメージサイズは０で良い。

	infoheader.biXPelsPerMeter = 0;
	infoheader.biYPelsPerMeter = 0;
	infoheader.biClrUsed = colorCnt;	// パレットの色数。８ビットの时のみ。
	infoheader.biClrImportant = 0;

	// ヘッダ部分を书き込み
	WriteFile( fh, (void*)(&infoheader), sizeof(BITMAPINFOHEADER), &writtensize, NULL );
	if( displayBpp == 8 ){ 
		// ８ビットの场合はパレットを书き込み
		WriteFile( fh, (void*)rgbpal, sizeof(RGBQUAD)*(colorCnt), &writtensize, NULL );
	}else
	if( displayBpp == 16 || displayBpp == 32 ){
		// １６と３２ビットの场合はマスクパターンを书き込み
		WriteFile( fh, (void*)ColorMask, sizeof(ColorMask), &writtensize, NULL );
	}
	for( i = 0; i < height; i++ )
	{
		WriteFile( fh, (void*)(buf+srcpitch*(height+y-1-i)+x*mul), width*mul, &writtensize, NULL );
		WriteFile( fh, (void*)(&zero), (linesize-width)*mul, &writtensize, NULL );
	}
	CloseHandle( fh );

	return TRUE;
}

// ある值の立っているビットの数を调べる
int getBitCount( int bit )
{
	int i, j, k;

	j = 1;
	k = 0;
	for( i = 0; i < sizeof( int )*8; i++ )
	{
		if( (bit & j) )
		{
			k++;
		}
		j <<= 1;
	}

	return k;
}

#include "../puk2/newDraw/Save_bmp.cpp"