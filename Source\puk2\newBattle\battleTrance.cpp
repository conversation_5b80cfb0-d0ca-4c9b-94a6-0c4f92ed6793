﻿// トランスエフェクト用构造体
typedef struct {
	ACTION *pParent;
	int fx,fy;
} TRN_YOBI;

#ifdef PUK2
// トランスのエフェクト
void BattleTranceAnim( ACTION *pAct )
{
#ifdef PUK3_REBIRTH_BAG2
	// 处理の必要が无いので拔ける
	if ( pAct->actNo ) return;

	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		// 见えなくする、自分では死なない
		pAct->atr |= ACT_ATR_HIDE;
		// アニメーションが終わったことを保存
		pAct->actNo = 1;
	}
#else
	// アニメーション終わったら抹杀
	if( pattern( pAct, ANM_NO_LOOP ) == 1 ){
		DeathAction( pAct );
	}
#endif
}

// トランスのエフェクト作成
ACTION *MakeTranceAnim( ACTION *pParent, int SprNo )
{
	ACTION *pAct;
	TRN_YOBI *pYobi;

#ifdef PUK3_ACTION_CHECKRANGE
	if ( !CheckAction( pParent ) ) return NULL;
#endif
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( TRN_YOBI ), ACT_T_TRN_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( TRN_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( TRN_YOBI *)pAct->pYobi;
	pYobi->pParent = pParent;
	// 实行关数
	pAct->func = BattleTranceAnim;

	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 效果のスプライト番号
	pAct->anim_chr_no = SprNo;
	pAct->anim_no = 0;		// デフォ
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 0;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_HIT_MARK;
	/* 初期位置 */
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
#ifdef PUK3_REBIRTH_BAG2
	// アニメーションが終わったかどうかのフラグ
	pAct->actNo = 0;
#endif

	pAct->level = 0;
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	return pAct;
}
#endif

// トランスのエフェクト
void BattleTranceEffect( ACTION *pAct )
{
	ACTION *pParent;
	TRN_YOBI *pYobi;

	// 予备构造体
	pYobi = (TRN_YOBI *)pAct->pYobi;
	// 亲の构造体取得
	pParent = (ACTION *)pYobi->pParent;
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pParent );
#endif
	// 亲が死ぬときは子も一绪
	if( pParent->deathFlag == TRUE ){
		DeathAction( pAct );
	}
	// 表示座标だけ亲から贳ってずらす
	pAct->x = pParent->x + pYobi->fx;
	pAct->y = pParent->y + pYobi->fy;

	pattern( pAct, ANM_LOOP );
}


// トランスのエフェクト作成
ACTION *MakeTranceEffect( ACTION *pParent, int fx, int fy, int SprNo )
{
	ACTION *pAct;
	TRN_YOBI *pYobi;

	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( TRN_YOBI ), ACT_T_TRN_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( TRN_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( TRN_YOBI *)pAct->pYobi;
	pYobi->pParent = pParent;
	// 实行关数
	pAct->func = BattleTranceEffect;

	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// 效果のスプライト番号
	pAct->anim_chr_no = SprNo;
	pAct->anim_no = 0;		// デフォ
	// アニメーションスピード
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 0;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_CHAR;
	/* 初期位置 */
	pAct->fx = pParent->fx;
	pAct->fy = pParent->fy;
	/* OFFセット座标 */
	pYobi->fx = fx;
	pYobi->fy = fy;
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
	// 表示优先度普通
	pAct->bufCount = 0;
#endif
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )( pAct->fx + pYobi->fx );
	pAct->y = ( int )( pAct->fy + pYobi->fy );
	
	return pAct;
}


//
//覚え书き
//
//とりあえず（バトルムービーの）コマンドにトランスとトランス解除を追加。
//プロトコルの内容はコマンド＋对象＋Ｏｎ／Ｏｆｆくらい？
//（あとはバースト时间。絶对值と相对值のどちらにするかは微妙）
//
//それと、トランス时のキャラ变更。
//テストでモンスターにでも变身させてみる。
//（223056～224367あたりにいかにもエレメンタルっぽいのがいるので、
//　そいつを使ってみる？　スプライト番号は101510～101513）
//
//上记に付随して、バースト时间の确认プロトコルを追加。
//普通に战闘中に送られてくるデータに付け加えるのもありかも。
//カウントをクライアント侧でやれば问题ないと思うし。
//（ラグによって时间のずれが発生するかも知れんけど）
//
//
//
//


//
//トランス起动の流れ
//
// トランスエフェクト発生后（発生中？）にトランスキャラに变身、トランスポーズ。
//　エフェクト発生と同时にバースト时间减少（？）。
//
//
//
//トランス解除の流れ
//
// 构えて、トランス解除ポーズ。エフェクト発生后（発生中？）にもとのキャラに变身。
//
//


//
//トランスのコマンド入力。
//
//　他のコマンドと多少の变更の必要あり。
//　（ペットがいるとき、どうするのか？）
//
//トランス稼动中の时间减少、どうするのか？（减少のタイミングとか）
//


//
// トランスプロトコルに必要なデータ
// エフェクトの番号、变身后のグラフィック番号
// 

//
// 时间减少は专用のコマンドを用意する？
// 他のコマンドと同时には起动しない流れで。
//

