﻿//メニュー＞アイテム

#ifndef _MENUGUILD_H_
#define _MENUGUILD_H_

//=========================================
// 饵やりウィンドウ
//=========================================

// ボタン处理关数 *********************//

BOOL MenuGuilMonFoodCloseButton( int no, unsigned int flag );
BOOL MenuGuilMonFoodOKButton( int no, unsigned int flag );

// アイテム
BOOL MenuGuilMonFoodMyItemPanel( int no, unsigned int flag );
BOOL MenuGuilMonFoodGiveItemPanel( int no, unsigned int flag );


BOOL MenuWindowGuilMonFoodBf( int mouse );
BOOL MenuWindowGuilMonFoodAf( int mouse );
BOOL closeGuilMonFoodWindow();


GRAPHIC_SWITCH MenuWindowGuilMonFoodGraph[]={
	{GID_GuilMonFoodBase,0,0,0,0,0xFFFFFFFF},	// ベース

	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},		// ＯＫボタン

	{GID_GuilMonFoodMy,0,0,0,0,0xFFFFFFFF},		// 自分侧パネル
	{GID_GuilMonFoodBox,0,0,0,0,0xFFFFFFFF},	// 公会宠物侧パネル
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},		// クローズボタン
};


// スイッチ
static SWITCH_DATA GuilMonFoodSwitch[] = {

{ SWITCH_GRAPHIC,547,  9,  11, 11, TRUE, &MenuWindowGuilMonFoodGraph[4], MenuGuilMonFoodCloseButton },		// クローズボタン
{ SWITCH_GRAPHIC,476,308,  66, 17, TRUE, &MenuWindowGuilMonFoodGraph[1], MenuGuilMonFoodOKButton },			// ＯＫボタン

{ SWITCH_GRAPHIC,286, 27, 273,280, TRUE, &MenuWindowGuilMonFoodGraph[2], MenuGuilMonFoodMyItemPanel },		// 自分侧パネル
{ SWITCH_GRAPHIC, 14, 27, 273,280, TRUE, &MenuWindowGuilMonFoodGraph[3], MenuGuilMonFoodGiveItemPanel },	// 公会宠物侧パネル

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowGuilMonFoodGraph[0], MenuSwitchNone },					// ベース

{ SWITCH_NONE,	  571, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },										// ドラッグ用

};

enum{
	EnumGuilMonFoodBtClose,
	EnumGuilMonFoodBtOk,

	EnumGuilMonFoodMyPanel,
	EnumGuilMonFoodGivePanel,

	EnumGuilMonFoodWindow,

	EnumGuilMonFoodDragBack,

	EnumGuilMonFoodEnd,
};

// ウィンドウ管理构造体
struct GUILMONFOODWINDOWMASTER{
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 

	int WinType;							// ウィンドウの种类
	int SeqNo;								// サーバの管理番号
	int ObjIndex;							// 现在の处理サーバの管理番号

	int itemInfoNo;
	int itemInfoPage;
};

const WINDOW_DATA WindowDataMenuGuilMonFood = {
 0,															// メニューウィンドウ
     4,  300, 82,567,339, 0x80010101,  EnumGuilMonFoodEnd,  GuilMonFoodSwitch, MenuWindowGuilMonFoodBf,MenuWindowGuilMonFoodAf,closeGuilMonFoodWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateGuilMonFood={
	2,
	 9,  0,558, 27,
	 571, 7,18,  78,
};

ACTION *openGuilMonFoodMenuWindow( int WinType, int SeqNo, int ObjIndex, char *data, unsigned char flg, char opentype );



//=========================================
// 公会宠物状态ウィンドウ
//=========================================

// ボタン处理关数 *********************//

BOOL MenuSwitchGuilMonStatusInfo( int no, unsigned int flag );

BOOL MenuSwitchGuilMonStatusClose( int no, unsigned int flag );

BOOL MenuSwitchGuilMonStatusScrollUp( int no, unsigned int flag );
BOOL MenuSwitchGuilMonStatusScrollDown( int no, unsigned int flag );
BOOL MenuSwitchGuilMonStatusScrollLeft( int no, unsigned int flag );
BOOL MenuSwitchGuilMonStatusScrollRight( int no, unsigned int flag );

BOOL MenuSwitchGuilMonStatusRename( int no, unsigned int flag );
BOOL MenuSwitchGuilMonStatusGiveFood( int no, unsigned int flag );

BOOL MenuSwitchGuilMonStatusRenaming( int no, unsigned int flag );

BOOL MenuSwitchGuilMonStatusItem( int no, unsigned int flag );

BOOL MenuSwitchGuilMonStatusBase( int no, unsigned int flag );
BOOL MenuSwitchGuilMonStatusScrollWheel( int no, unsigned int flag );


BOOL MenuWindowGuilMonStatusBf( int mouse );
BOOL MenuWindowGuilMonStatusAf( int mouse );
BOOL closeGuilMonStatusWindow();


GRAPHIC_SWITCH MenuWindowGuilMonStatusGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},			// クローズボタン

	{GID_GuilmonStatusWindow,0,0,0,0,0xFFFFFFFF},		// ベース
	{GID_GuilmonStatusWindowBack,0,0,0,0,0x80FFFFFF},	// 背景

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},					// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},				// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},				// スクロールバー(下ボタン)

	{GID_RenameButtonOn,0,0,0,0,0xFFFFFFFF},			// リネームボタン
	{GID_GiveFoodButtonOn,0,0,0,0,0xFFFFFFFF},			// 饵あげボタン

	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},				// スクロールバー(左ボタン)
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},				// スクロールバー(右ボタン)
};

BUTTON_SWITCH MenuWindowGuilMonStatusButton[]={
	{0,0,0},
};

TEXT_SWITCH MenuWindowGuilMonStatusText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(826, "公会宠物")},			// 公会宠物の名称
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态１
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态２
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态３
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态４
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态５
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态６
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态７
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态８
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,ML_STRING(827, "状态")},			// 公会宠物の状态９
};

// スイッチ
static SWITCH_DATA GuilMonStatusSwitch[] = {

{ SWITCH_NONE,	   0,  0, 330,110, TRUE, NULL, MenuSwitchGuilMonStatusInfo },									// 确认ウィンドウ

{ SWITCH_GRAPHIC,211,  8,  11, 11, TRUE, &MenuWindowGuilMonStatusGraph[0], MenuSwitchGuilMonStatusClose },		// クローズボタン

{ SWITCH_NONE,	  57, 30, 150, 12, TRUE, NULL, MenuSwitchGuilMonStatusRenaming },								// 公会宠物の名称のあたり判定
{ SWITCH_TEXT,	  57, 30,   0,  0, TRUE, &MenuWindowGuilMonStatusText[0], MenuSwitchNone },						// 公会宠物の名称
{ SWITCH_DIALOG,  57, 30,   0,  0, TRUE, NULL, MenuSwitchNone },												// 公会宠物の名称入力栏

{ SWITCH_TEXT,	  17, 64,   0,  0, TRUE, &MenuWindowGuilMonStatusText[1], MenuSwitchNone },						// 公会宠物の状态１
{ SWITCH_TEXT,	  17, 81,   0,  0, TRUE, &MenuWindowGuilMonStatusText[2], MenuSwitchNone },						// 公会宠物の状态２
{ SWITCH_TEXT,	  17, 98,   0,  0, TRUE, &MenuWindowGuilMonStatusText[3], MenuSwitchNone },						// 公会宠物の状态３
{ SWITCH_TEXT,	  17,115,   0,  0, TRUE, &MenuWindowGuilMonStatusText[4], MenuSwitchNone },						// 公会宠物の状态４
{ SWITCH_TEXT,	  17,132,   0,  0, TRUE, &MenuWindowGuilMonStatusText[5], MenuSwitchNone },						// 公会宠物の状态５
{ SWITCH_TEXT,	  17,149,   0,  0, TRUE, &MenuWindowGuilMonStatusText[6], MenuSwitchNone },						// 公会宠物の状态６
{ SWITCH_TEXT,	  17,166,   0,  0, TRUE, &MenuWindowGuilMonStatusText[7], MenuSwitchNone },						// 公会宠物の状态７
{ SWITCH_TEXT,	  17,183,   0,  0, TRUE, &MenuWindowGuilMonStatusText[8], MenuSwitchNone },						// 公会宠物の状态８
{ SWITCH_TEXT,	  17,200,   0,  0, TRUE, &MenuWindowGuilMonStatusText[9], MenuSwitchNone },						// 公会宠物の状态９

{ SWITCH_GRAPHIC,212, 75,   0, 14, TRUE, &MenuWindowGuilMonStatusGraph[3], MenuSwitchNone },					// スクロールバー(つまみ)
{ SWITCH_BUTTON, 212, 75,  11,136, TRUE, &MenuWindowGuilMonStatusButton[0], MenuSwitchScrollBarV },				// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,212, 65,  11, 11, TRUE, &MenuWindowGuilMonStatusGraph[4], MenuSwitchGuilMonStatusScrollUp },	// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,212,210,  11, 11, TRUE, &MenuWindowGuilMonStatusGraph[5], MenuSwitchGuilMonStatusScrollDown },	// スクロールバー(下ボタン)
{ SWITCH_GRAPHIC,169,225,  18, 18, TRUE, &MenuWindowGuilMonStatusGraph[8], MenuSwitchGuilMonStatusScrollLeft },	// スクロールバー(左ボタン)
{ SWITCH_GRAPHIC,189,225,  18, 18, TRUE, &MenuWindowGuilMonStatusGraph[9], MenuSwitchGuilMonStatusScrollRight },// スクロールバー(右ボタン)
{ SWITCH_NONE,	   0,  0, 231,305, TRUE, NULL, MenuSwitchGuilMonStatusScrollWheel },							// マウスホイール判定

{ SWITCH_GRAPHIC,165, 43,  57, 17, TRUE, &MenuWindowGuilMonStatusGraph[6], MenuSwitchGuilMonStatusRename },		// リネームボタン
{ SWITCH_GRAPHIC, 74,237,  57, 17, TRUE, &MenuWindowGuilMonStatusGraph[7], MenuSwitchGuilMonStatusGiveFood },	// 饵あげボタン

{ SWITCH_NONE,	  15,225,  48, 48, TRUE, NULL, MenuSwitchGuilMonStatusItem },									// アイテム栏のあたり判定

{ SWITCH_GRAPHIC,  0,  0, 267,305, TRUE, &MenuWindowGuilMonStatusGraph[1], MenuSwitchGuilMonStatusBase },		// ベース
{ SWITCH_GRAPHIC, 12, 27,   0,  0, TRUE, &MenuWindowGuilMonStatusGraph[2], MenuSwitchNone },					// 背景

{ SWITCH_NONE,	  234, 5,  18, 79, TRUE, NULL, MenuSwitchDelMouse },											// ドラッグ用

};

enum{
	EnumGuilMonStatusInfoWidnow,

	EnumGuilMonStatusBtClose,

	EnumGuilMonStatusTxtNameHit,
	EnumGuilMonStatusTxtName,
	EnumGuilMonStatusTxtNameInput,

	EnumGuilMonStatusTxtStatus1,
	EnumGuilMonStatusTxtStatus2,
	EnumGuilMonStatusTxtStatus3,
	EnumGuilMonStatusTxtStatus4,
	EnumGuilMonStatusTxtStatus5,
	EnumGuilMonStatusTxtStatus6,
	EnumGuilMonStatusTxtStatus7,
	EnumGuilMonStatusTxtStatus8,
	EnumGuilMonStatusTxtStatus9,

	EnumGuilMonStatusGraScroll,
	EnumGuilMonStatusBtScroll,
	EnumGuilMonStatusBtScrollUp,
	EnumGuilMonStatusBtScrollDown,
	EnumGuilMonStatusBtScrollLeft,
	EnumGuilMonStatusBtScrollRight,
	EnumGuilMonStatusBtScrollWheel,

	EnumGuilMonStatusRename,
	EnumGuilMonStatusGiveFood,

	EnumGuilMonStatusWindow,
	EnumGuilMonStatusWindowBack,

	EnumGuilMonStatusDragBack,

	EnumGuilMonStatusEnd,
};

const WINDOW_DATA WindowDataMenuGuilMonStatus = {
 0,															// メニューウィンドウ
     4,  100, 100,231,305, 0x80010101,  EnumGuilMonStatusEnd,  GuilMonStatusSwitch, MenuWindowGuilMonStatusBf,MenuWindowGuilMonStatusAf,closeGuilMonStatusWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateGuilMonStatus={
	2,
	 10,  0,231, 27,
	 234, 5, 18, 79
};

ACTION *openGuilMonStatusWindow( int SeqNo,	int ObjIndex, char *data );
void remakeGuilMonStatusData( char *data );

// 一行の最大文字数
#define GMS_MAXLETTER 30

#endif