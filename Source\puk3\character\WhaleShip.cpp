﻿/***************************************
			WhaleShip.cpp
***************************************/

#ifdef PUK3

#ifdef PUK3_WHALE_SHIP

#include"../systeminc/mapEffect.h"

MAP_EFFECT *getMapEffectBuf( void );

extern short mapEffectCloudLevel;
extern short mapEffectCloud2Level;
extern float mapEffectCloudSpeed;
extern short mapEffectCloudAngle;
extern float mapEffectCloudMoveX, mapEffectCloudMoveY;

static void whalepier( ACTION *pAct );

#define YSPEEDMAG 0.14f

static int getshiptime, getshiptimeMax;

static void WhaleShip_Master( ACTION *pAct )
{
	int i, j, k;
	double ang;
	ACTION *pActShip;

	//ゲーム中のプロセスじゃないなら
	if( ProcNo != PROC_GAME ){
		//抹杀
		DeathAction( pAct );
		return;
	}
	//海マップでないかフロアが变わったなら
	if( mapNo != 59529 && mapNo != pAct->gy ){
		//抹杀
		DeathAction( pAct );
		return;
	}
	// 栈桥を取得
	pActShip = check_ship_task( whalepier );

	i = GetTickCount() - getshiptime;
	k = getshiptimeMax * 1000;
	j = 0;
	if (k) j = i * 130 / k;

	if ( i > k || i < 0 ){
		mapEffectCloudMoveX = 0;
		mapEffectCloudMoveY = 0;
	}else
	// 入港中は船の速度に合わせる
	if ( i > (getshiptimeMax - 15) * 1000 ){
		if ( i < (getshiptimeMax - 2) * 1000){
			if ( i % 40 == 0 ) play_se( 454, 320, 240 );
		}

		if (pActShip){
			mapEffectCloudMoveX = pActShip->speed;
			mapEffectCloudMoveY = -pActShip->speed * YSPEEDMAG;
		}
	}else
	// 入港直前は停止
	if ( i > (getshiptimeMax - 16) * 1000 ){
		mapEffectCloudMoveX = 0;
		mapEffectCloudMoveY = 0;
	}else
	// 减速
	if ( i > (getshiptimeMax - 17) * 1000 ){
		if ( i % 40 == 0 ) play_se( 454, 320, 240 );

		pAct->speed -= 0.025f;
		if ( pAct->speed < 0 ) pAct->speed = 0;
		ang = (double)( (j + 250) % 360 ) * 3.1415926535 / 180;
		mapEffectCloudMoveX = (float)( pAct->speed * cos(ang) );
		mapEffectCloudMoveY = (float)( pAct->speed * -sin(ang) );
	}else
	// 出港中は船の速度に合わせる
	if ( i <= 14 * 1000 ){
		if ( i >= 13 * 1000 ){
			if ( i % 120 == 0 ) play_se( 455, 320, 240 );
		}
		if ( i % 40 == 0 ) play_se( 454, 320, 240 );

		if (pActShip){
			mapEffectCloudMoveX = -pActShip->speed;
			mapEffectCloudMoveY = pActShip->speed * YSPEEDMAG;
		}
	}else
	// 减速
	if ( i <= 15 * 1000 ){
		if ( i % 120 == 0 ) play_se( 455, 320, 240 );
		if ( i % 40 == 0 ) play_se( 454, 320, 240 );

		if (pActShip){
			pActShip->speed -= 0.025f;
			if ( pActShip->speed < 0 ) pActShip->speed = 0;
			mapEffectCloudMoveX = -pActShip->speed;
			mapEffectCloudMoveY = pActShip->speed * YSPEEDMAG;
		}
	}else
	// １秒间停止
	if ( i <= 16 * 1000 ){
		if ( i % 120 == 0 ) play_se( 455, 320, 240 );
		mapEffectCloudMoveX = 0;
		mapEffectCloudMoveY = 0;
	}else{
		if ( i <= (getshiptimeMax - 30) * 1000 ){
			if ( i % 120 == 0 ) play_se( 455, 320, 240 );
		}
		if ( i % 120 == 80 ) play_se( 454, 320, 240 );

		ang = (double)( (j + 250) % 360 ) * 3.1415926535 / 180;
		mapEffectCloudMoveX = (float)( pAct->speed * cos(ang) );
		mapEffectCloudMoveY = (float)( pAct->speed * -sin(ang) );
		// 入港前に减速する
		if ( i > (getshiptimeMax - 25) * 1000 ){
			pAct->speed -= 0.15f;
			if ( pAct->speed < 1.f ) pAct->speed = 1.f;
		}else{
			pAct->speed += 0.1f;
			if ( pAct->speed > 15.f ) pAct->speed = 15.f;
		}
	}

	for(j=0;j<10;j++){
		for(i=0;i<10;i++){
			StockDispBuffer_PUK2( i*64, j*48,  0, 14648, 0, 1, &pAct->bm );
		}
	}
}

void create_WhaleShip_Master( void )
{
	ACTION *pAct;

	//ゲーム中のプロセスじゃないなら
	if( ProcNo != PROC_GAME ) return;
	//プレイヤーがいないなら
	if( pc.ptAct == NULL ){
		return;
	}
	if ( mapNo != 59529 ) return;

	//アクション无いあるなら
	if( ( pAct = check_ship_task( WhaleShip_Master ) ) != NULL ) return;

	/* アクションリストに登録 */
	pAct = GetAction( PRIO_CHR + 1, NULL );
	//作成失败なら
	if( pAct == NULL ){
		return;
	}
	// 实行关数
	pAct->func = WhaleShip_Master;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_SEA;
	//フロア番号保存
	pAct->gy = mapNo;
	// 表示を消す
	pAct->atr = ACT_ATR_HIDE;

	// 航行速度
	pAct->speed = 0;

	pAct->bm.BltVer = BLTVER_PUK2;
	pAct->bm.u = 0   - 320;
	pAct->bm.v = 820 - 480;
	pAct->bm.w = 64;
	pAct->bm.h = 48;
}




static void whaleship( ACTION *pAct )
{
	float mx, my;

	//海マップでないかフロアが变わったなら
	if( mapNo != pAct->gy ){
		//抹杀
		DeathAction( pAct );
		return;
	}

	//移动
	switch( pAct->anim_no ){
	//入港
	case 1:
		//到着なら
		if( pAct->fx < 0 ){
			if ( pAct->speed > 0 && pAct->speed - 0.11f <= 0 ){
				// 鲸泣き声(３つ重ね音)
				play_se( 444, 320, 240 );
				play_se( 443, 320, 240 );
				play_se( 404, 320, 240 );
			}
			pAct->speed -= 0.11f;
			pAct->fx -= pAct->speed;
			pAct->fy += pAct->speed * YSPEEDMAG;
			if ( pAct->fx > 0 ){
				pAct->fx = 0;
				pAct->fy = 0;
				pAct->speed = 0;
				//停止へ
				pAct->anim_no++;
			}
		}else{
			pAct->fx -= pAct->speed;
			pAct->fy += pAct->speed * YSPEEDMAG;
		}
		break;
	//停止
	case 2:
		break;
	//出港
	case 3:
		pAct->speed += 0.002f;
		if ( pAct->speed > 1.5f ) pAct->speed = 1.5f;
		pAct->fx += pAct->speed;
		pAct->fy -= pAct->speed * 0.2f;
		break;
	}
	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	pAct->x = (int)(mx+pAct->fx+.5);
	pAct->y = (int)(my+pAct->fy+.5);
}

void create_whaleship( int kind, int map, int floor, int gx, int gy )
{
	U4 BmpNo;
	short dx,dy;
	int create_flg = 0;
	ACTION *pAct;

	// 航海时间なら
	if ( kind == 0 ){
		getshiptimeMax = map;
		getshiptime = GetTickCount() - floor * 1000;
		return;
	}

#ifdef PUK3_WHALESHIP_DUEL
	//ゲーム中のプロセスじゃないなら
	if( ProcNo != PROC_GAME ) return;
#endif
	//画面外消すなら
	if( kind == 4 ){
		//船がいるなら
		if( pAct = check_ship_task( whaleship ) ){
			//船抹杀
			DeathAction( pAct );
			return;
		}
	}

	//船のアクション无いなら
	if( ( pAct = check_ship_task( whaleship ) ) == NULL ){
		create_flg = 1;
		/* アクションリストに登録 */
		pAct = GetAction( PRIO_CHR + 1, NULL );
		//作成失败なら
		if( pAct == NULL ){
			return;
		}
	}

	// 实行关数
	pAct->func = whaleship;
	// グラフィックの番号
	realGetNo( 14647, (U4 *)&BmpNo );
	realGetPos( BmpNo, &dx, &dy );
	pAct->bmpNo = BmpNo;		// ＢＭＰ番号セット
	pAct->anim_x = dx;		// Ｘ座标OFFセットセット
	pAct->anim_y = dy;		// Ｙ座标OFFセットセット
	//作成されたなら
	if( create_flg ){
		pAct->fx = 0;
		pAct->fy = 0;
		pAct->speed = 0.25f;
		pAct->dir = 0;
		pAct->delta = 0;
	}

	// 表示位置をずらす
	pAct->anim_x += 86;
	pAct->anim_y -= 93;

	// 动作番号
	pAct->anim_no = kind;
	// アニメーション向き( ０～７ )( 下が０で右回り )
//	pAct->anim_ang = dir;
	// 表示优先度
//	if ( mapNo == 59527 ) pAct->dispPrio = DISP_PRIO_B_CHAR;
	/*if ( mapNo == 59528 ) */pAct->dispPrio = DISP_PRIO_PARTS + 1;
	//新规作成フラグ
	pAct->gx = 0;
	//フロア番号保存
	pAct->gy = floor;
	//动作别设定
	switch( kind ){
	//入港
	case 1:
		pAct->fx = +870;
		pAct->fy = -870 * YSPEEDMAG;
		pAct->speed = 1;
		// マップ座标
		pAct->mx = (float)gx * GRID_SIZE;
		pAct->my = (float)gy * GRID_SIZE;
		break;
	//停止
	case 2:
		pAct->fx = 0;
		pAct->speed = 0;
		// マップ座标
		pAct->mx = (float)gx * GRID_SIZE;
		pAct->my = (float)gy * GRID_SIZE;
		break;
	//出港
	case 3:
		// 鲸泣き声(３つ重ね音)
		play_se( 444, 320, 240 );
		play_se( 443, 320, 240 );
		play_se( 404, 320, 240 );

		pAct->fx = 0;
		pAct->fy = 0;
		pAct->speed = 0;
		// マップ座标
		pAct->mx = (float)gx * GRID_SIZE;
		pAct->my = (float)gy * GRID_SIZE;
		break;
	//エラー
	default:
		//船抹杀
		DeathAction( pAct );
		pAct = NULL;
		return;
	}
}

static void whalepier( ACTION *pAct )
{
	float mx, my;

	//海マップでないかフロアが变わったなら
	if( mapNo != pAct->gy ){
		//抹杀
		DeathAction( pAct );
		return;
	}

	//移动
	switch( pAct->anim_no ){
	//入港
	case 1:
		//到着なら
		if( pAct->fx > 0 ){
			if ( pAct->speed > 0 && pAct->speed - 0.11f <= 0 ){
				// 鲸泣き声(３つ重ね音)
				play_se( 444, 320, 240 );
				play_se( 443, 320, 240 );
				play_se( 404, 320, 240 );
			}
			pAct->speed -= 0.11f;
			pAct->fx += pAct->speed;
			pAct->fy -= pAct->speed * YSPEEDMAG;
			if ( pAct->fx < 0 ){
				pAct->fx = 0;
				pAct->fy = 0;
				pAct->speed = 0;
				//停止へ
				pAct->anim_no++;
			}
		}else{
			pAct->fx += pAct->speed;
			pAct->fy -= pAct->speed * YSPEEDMAG;
		}
		break;
	//停止
	case 2:
		break;
	//出港
	case 3:
		pAct->speed += 0.002f;
		if ( pAct->speed > 1.5f ) pAct->speed = 1.5f;
		pAct->fx -= pAct->speed;
		pAct->fy += pAct->speed * 0.2f;
		break;
	}
	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	pAct->x = (int)(mx+pAct->fx+.5);
	pAct->y = (int)(my+pAct->fy+.5);
}

void create_whalepier( int kind, int map, int floor, int gx, int gy )
{
	U4 BmpNo;
	int bmpNo = 0;
	short dx,dy;
	int create_flg = 0;
	ACTION *pAct;
	int type;

	type = -kind / 100;
	kind = -kind % 100;

	if ( kind == 0 ) return;

#ifdef PUK3_WHALESHIP_DUEL
	//ゲーム中のプロセスじゃないなら
	if( ProcNo != PROC_GAME ) return;
#endif
	//画面外消すなら
	if( kind == 4 ){
		//船がいるなら
		if( pAct = check_ship_task( whalepier ) ){
			//船抹杀
			DeathAction( pAct );
			return;
		}
	}

	// グラフィックの设定
	switch(type){
	case 100:	// 村侧栈桥
		bmpNo = 14645;
		break;
	case 101:	// 王国侧栈桥
		bmpNo = 14646;
		break;
	default:	// 上记以外は栈桥を出さない
		return;
	}

	//船のアクション无いなら
	if( ( pAct = check_ship_task( whalepier ) ) == NULL ){
		create_flg = 1;
		/* アクションリストに登録 */
		pAct = GetAction( PRIO_CHR + 1, NULL );
		//作成失败なら
		if( pAct == NULL ){
			return;
		}
	}

	// 实行关数
	pAct->func = whalepier;
	realGetNo( bmpNo, (U4 *)&BmpNo );
	realGetPos( BmpNo, &dx, &dy );
	pAct->bmpNo = BmpNo;		// ＢＭＰ番号セット
	pAct->anim_x = dx;		// Ｘ座标OFFセットセット
	pAct->anim_y = dy;		// Ｙ座标OFFセットセット
	//作成されたなら
	if( create_flg ){
		pAct->fx = 0;
		pAct->fy = 0;
		pAct->speed = 0.25f;
		pAct->dir = 0;
		pAct->delta = 0;
	}

	// 表示位置をずらす
	switch(type){
	case 100:	// 村侧栈桥
		pAct->anim_x -= 189;
		pAct->anim_y += 217;
		break;
	case 101:	// 王国侧栈桥
		pAct->anim_x -= 215;
		pAct->anim_y -= 220;
		break;
	}

	// 动作番号
	pAct->anim_no = kind;
	// アニメーション向き( ０～７ )( 下が０で右回り )
//	pAct->anim_ang = dir;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_BG + 1;
	//新规作成フラグ
	pAct->gx = 0;
	//フロア番号保存
	pAct->gy = floor;

	//动作别设定
	switch( kind ){
	//入港
	case 1:
		pAct->fx = -870;
		pAct->fy = +870 * YSPEEDMAG;
		pAct->speed = 1;
		// マップ座标
		pAct->mx = (float)gx * GRID_SIZE;
		pAct->my = (float)gy * GRID_SIZE;
		break;
	//停止
	case 2:
		pAct->fx = 0;
		pAct->speed = 0;
		// マップ座标
		pAct->mx = (float)gx * GRID_SIZE;
		pAct->my = (float)gy * GRID_SIZE;
		break;
	//出港
	case 3:
		// 鲸泣き声(３つ重ね音)
		play_se( 444, 320, 240 );
		play_se( 443, 320, 240 );
		play_se( 404, 320, 240 );

		pAct->fx = 0;
		pAct->fy = 0;
		pAct->speed = 0;
		// マップ座标
		pAct->mx = (float)gx * GRID_SIZE;
		pAct->my = (float)gy * GRID_SIZE;
		break;
	//エラー
	default:
		//船抹杀
		DeathAction( pAct );
		pAct = NULL;
		return;
	}
}
#endif

#endif