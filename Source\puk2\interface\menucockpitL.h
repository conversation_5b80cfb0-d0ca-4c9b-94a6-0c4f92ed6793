﻿//左コクピット

#ifndef _MENUCOCKPITL_H_
#define _MENUCOCKPITL_H_

//サイズ管理フラグ
extern int MenuWindowLeftSizeFlag;

BOOL MenuWindowLeftLarge( int mouse );
BOOL MenuWindowLeftMiddle( int mouse );
BOOL MenuWindowLeftSmall( int mouse );
BOOL MenuWindowLeftLargeDraw( int mouse );
BOOL MenuWindowLeftMiddleDraw( int mouse );
BOOL MenuWindowLeftSmallDraw( int mouse );

BOOL MenuSwitchCockpitLeftLarge( int no, unsigned int flag );
BOOL MenuSwitchCockpitLeftMiddle( int no, unsigned int flag );
BOOL MenuSwitchCockpitLeftSmall( int no, unsigned int flag );
BOOL MenuSwitchLargeHit( int no, unsigned int flag );

BOOL MenuSwitchCockpitLeftLargeToMiddle( int no, unsigned int flag );
BOOL MenuSwitchCockpitLeftMiddleToSmall( int no, unsigned int flag );
BOOL MenuSwitchCockpitLeftSmallToLarge( int no, unsigned int flag );

BOOL MenuSwitchCockpitLeftLarge( int no, unsigned int flag );
BOOL MenuSwitchCockpitLeftLargeLamp( int no, unsigned int flag );

int CheckInjury_PUK2(void);

char *StrAddressCallok(char *StrAdd);
void MenuWindowLeftLargeInit(void);

void ChangeEtcFlagDu(void);
void ChangeEtcFlagCh(void);
void ChangeEtcFlagPa(void);
void ChangeEtcFlagAd(void);
void ChangeEtcFlagTr(void);
void ChangeEtcFlagGu(void);
void ChangeEtcFlagSend(void);

//グラフィックスイッチ
GRAPHIC_SWITCH MenuWindowCockPitLeftLargeGraph[]={
	{GID_CockpitLeftLargeBase,0,0,0,0,0xFFFFFFFF},	//左コクピット大

	{GID_CockpitDuOff,0,0,0,0,0xFFFFFFFF},		//Du
	{GID_CockpitChOff,0,0,0,0,0xFFFFFFFF},		//Ch
	{GID_CockpitGpOff,0,0,0,0,0xFFFFFFFF},		//Gp
	{GID_CockpitAdOff,0,0,0,0,0xFFFFFFFF},		//Ad
	{GID_CockpitTrOff,0,0,0,0,0xFFFFFFFF},		//Tr
	{GID_CockpitGuOff,0,0,0,0,0xFFFFFFFF},		//Gu

	{GID_CockpitHealth0Off,0,0,0,0,0xFFFFFFFF},	//
	{GID_CockpitMailOff,0,0,0,0,0xFFFFFFFF},	//
	{GID_CockpitLvUpOff,0,0,0,0,0xFFFFFFFF},	//

	{GID_CockpitTimeVer,0,3,60,15,0xFFFFFFFF},	//タイムバー

	{GID_Num_White_0,0,0,0,0,0xFFFFFFFF},	//タイムバー
	{GID_Num_White_0,0,0,0,0,0xFFFFFFFF},	//タイムバー
	{GID_Num_White_0,0,0,0,0,0xFFFFFFFF},	//タイムバー

};

GRAPHIC_SWITCH MenuWindowCockPitLeftMiddleGraph[]={
	{GID_CockpitLeftMiddleBase,0,0,0,0,0xFFFFFFFF},	//左コクピット中
};

GRAPHIC_SWITCH MenuWindowCockPitLeftSmallGraph[]={
	{GID_CockpitLeftSmallBase,0,0,0,0,0xFFFFFFFF},	//左コクピット中
};

//テキストスイッチ
TEXT_SWITCH MenuWindowCockPitLeftLargeText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"1000/1000"},			//ＨＰ              //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"1000/1000"},			//魔力              //MLHIDE
};

//ボタンスイッチ
BUTTON_SWITCH MenuWindowCockPitButtonLeft[]={
	{0},										//左大＞中
	{0},										//左中＞小
	{0},										//左小＞大
	{0},										//
};


char CockNum_Lp[22];
char CockNum_Fp[22];

NUMBER_SWITCH MenuWindowCockPitNum[] = {
	{ CockNum_Lp, FONT_PAL_BLACK, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
	{ CockNum_Fp, FONT_PAL_BLACK, G_NUM_SIZE__9, G_NUM_FLAG_LEFT_JUSTIFIED },
};


//左コクピット大スイッチ
static SWITCH_DATA MenuCockpitLeftLargeSwitch[] = {

//默认X坐标20,数值超过1000时会显示位置偏左
{ SWITCH_NUMBER,    25, 19,   0,  0, TRUE, &MenuWindowCockPitNum[0], MenuSwitchNone },				//体力
{ SWITCH_NUMBER,    25, 29,   0,  0, TRUE, &MenuWindowCockPitNum[1], MenuSwitchNone },				//魔力

//グラフィック	
{ SWITCH_GRAPHIC  , 63,  3,  14, 14, TRUE, &MenuWindowCockPitLeftLargeGraph[ 1], MenuSwitchNone },	//Du
{ SWITCH_GRAPHIC  , 82,  3,  14, 14, TRUE, &MenuWindowCockPitLeftLargeGraph[ 2], MenuSwitchNone },	//Ch
{ SWITCH_GRAPHIC  ,101,  3,  14, 14, TRUE, &MenuWindowCockPitLeftLargeGraph[ 3], MenuSwitchNone },	//Gp
{ SWITCH_GRAPHIC  ,120,  3,  14, 14, TRUE, &MenuWindowCockPitLeftLargeGraph[ 4], MenuSwitchNone },	//Ad
{ SWITCH_GRAPHIC  ,139,  3,  14, 14, TRUE, &MenuWindowCockPitLeftLargeGraph[ 5], MenuSwitchNone },	//Tr
{ SWITCH_GRAPHIC  ,158,  3,  14, 14, TRUE, &MenuWindowCockPitLeftLargeGraph[ 6], MenuSwitchNone },	//Gu

{ SWITCH_GRAPHIC  ,145, 25,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[11], MenuSwitchNone },	//バーストタイム表记
{ SWITCH_GRAPHIC  ,158, 25,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[12], MenuSwitchNone },	//バーストタイム表记
{ SWITCH_GRAPHIC  ,165, 25,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[13], MenuSwitchNone },	//バーストタイム表记

{ SWITCH_GRAPHIC  ,  6, 42,  36, 20, TRUE, &MenuWindowCockPitLeftLargeGraph[ 7], MenuSwitchCockpitLeftLargeLamp },	//ヘルス
{ SWITCH_GRAPHIC  , 51, 41,  36, 20, TRUE, &MenuWindowCockPitLeftLargeGraph[ 8], MenuSwitchCockpitLeftLargeLamp },	//メール
{ SWITCH_GRAPHIC  , 83, 41,  36, 20, TRUE, &MenuWindowCockPitLeftLargeGraph[ 9], MenuSwitchCockpitLeftLargeLamp },	//等级アップ

{ SWITCH_GRAPHIC  ,  0,  0,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[ 0], MenuSwitchNone },	//左コクピット大

{ SWITCH_GRAPHIC  ,  3,  4,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[10], MenuSwitchNone },	//タイムバー


//ボタン	
{ SWITCH_NONE     ,175,  0, 15, 33, TRUE, NULL,MenuSwitchCockpitLeftLargeToMiddle },
//{ SWITCH_BUTTON   ,175,  0, 15, 33, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLargeToMiddle },

{ SWITCH_BUTTON   , 63,  3, 14, 14, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLarge },
{ SWITCH_BUTTON   , 82,  3, 14, 14, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLarge },
{ SWITCH_BUTTON   ,101,  3, 14, 14, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLarge },
{ SWITCH_BUTTON   ,120,  3, 14, 14, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLarge },
{ SWITCH_BUTTON   ,139,  3, 14, 14, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLarge },
{ SWITCH_BUTTON   ,158,  3, 14, 14, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftLarge },

//あたり判定用スイッチ
{ SWITCH_NONE     ,  0, 40, 120, 30, TRUE, NULL,MenuSwitchLargeHit },

};

enum{

	EnumTextMenuCockpitLeftLargeLP,
	EnumTextMenuCockpitLeftLargeFP,

	EnumGraphCockpitLeftLargeDu,
	EnumGraphCockpitLeftLargeCh,
	EnumGraphCockpitLeftLargeGp,
	EnumGraphCockpitLeftLargeAd,
	EnumGraphCockpitLeftLargeTr,
	EnumGraphCockpitLeftLargeGu,

	EnumGraphCockpitLeftLargeBt00,
	EnumGraphCockpitLeftLargeBt01,
	EnumGraphCockpitLeftLargeBt02,

	EnumGraphCockpitLeftLargeHearth,
	EnumGraphCockpitLeftLargeMeail,
	EnumGraphCockpitLeftLargeLevelUp,

	EnumGraphCockpitLeftLarge,

	EnumGraphCockpitLeftLargeTimeBar,

	EnumBtToMiddleCockpitLeftLarge,

	EnumBtCockpitLeftLargeDu,
	EnumBtCockpitLeftLargeCh,
	EnumBtCockpitLeftLargeGp,
	EnumBtCockpitLeftLargeAd,
	EnumBtCockpitLeftLargeTr,
	EnumBtCockpitLeftLargeGu,

	EnumHitSwitchCockpitLeftLarge,

	EnumCockpitLeftLargeEND
};

//左コクピット中スイッチ
static SWITCH_DATA MenuCockpitLeftMiddleSwitch[] = {

//默认X坐标20,数值超过1000时会显示位置偏左
{ SWITCH_NUMBER,    25,  2,   0,  0, TRUE, &MenuWindowCockPitNum[0], MenuSwitchNone },				//体力
{ SWITCH_NUMBER,    25, 12,   0,  0, TRUE, &MenuWindowCockPitNum[1], MenuSwitchNone },				//魔力

{ SWITCH_GRAPHIC  ,146,  9,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[11], MenuSwitchNone },	//バーストタイム表记
{ SWITCH_GRAPHIC  ,158,  9,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[12], MenuSwitchNone },	//バーストタイム表记
{ SWITCH_GRAPHIC  ,165,  9,   0,  0, TRUE, &MenuWindowCockPitLeftLargeGraph[13], MenuSwitchNone },	//バーストタイム表记

{ SWITCH_GRAPHIC  ,  0,  0,   0,  0, TRUE, &MenuWindowCockPitLeftMiddleGraph[ 0], MenuSwitchNone },	//左コクピット中

{ SWITCH_BUTTON   ,181,  0, 16, 37, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftMiddleToSmall },

};

enum{

	EnumTextMenuCockpitLeftMiddleLP,
	EnumTextMenuCockpitLeftMiddleFP,

	EnumGraphCockpitLeftMiddleBt00,
	EnumGraphCockpitLeftMiddleBt01,
	EnumGraphCockpitLeftMiddleBt02,
	
	EnumGraphCockpitLeftMiddle,
	EnumBtToSmallCockpitLeftLarge,

	EnumCockpitLeftMiddleEND
};

//左コクピット小スイッチ
static SWITCH_DATA MenuCockpitLeftSmallSwitch[] = {

//グラフィック	
{ SWITCH_GRAPHIC  ,  0,  0,   0,  0, TRUE, &MenuWindowCockPitLeftSmallGraph[ 0], MenuSwitchNone },	//左コクピット小

//ボタン	
{ SWITCH_BUTTON   ,  0,  0, 19, 34, TRUE, &MenuWindowCockPitButtonLeft[0], MenuSwitchCockpitLeftSmallToLarge },

};

enum{
	EnumGraphCockpitLeftSmall,
	EnumBtToLargeCockpitLeftLarge,

	EnumCockpitLeftSmallEND
};



//ウインドウ
// 左大
const WINDOW_DATA WindowDataMenuLeftLarge = {
 0,																	
     5,  0,  0,197, 40, 0x80000080,  EnumCockpitLeftLargeEND, MenuCockpitLeftLargeSwitch , MenuWindowLeftLarge,MenuWindowLeftLargeDraw,MenuWindowDel 
};

// 左中
const WINDOW_DATA WindowDataMenuLeftMiddle = {
 0,																	
     5,  0,  0,197, 32, 0x80000080,  EnumCockpitLeftMiddleEND, MenuCockpitLeftMiddleSwitch , MenuWindowLeftMiddle,MenuWindowLeftMiddleDraw,MenuWindowDel 
};

// 左小
const WINDOW_DATA WindowDataMenuLeftSmall = {
 0,																	
     5,  0,  0, 19, 34, 0x80000080,  EnumCockpitLeftSmallEND, MenuCockpitLeftSmallSwitch , MenuWindowLeftSmall,MenuWindowLeftSmallDraw,MenuWindowDel 
};


#endif