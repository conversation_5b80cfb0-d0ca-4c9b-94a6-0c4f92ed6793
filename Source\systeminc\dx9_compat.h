/***************************************
		dx9_compat.h
		DirectX 9 Compatibility Layer
		Created: DirectX 7→9 Upgrade Project
***************************************/

#ifndef _DX9_COMPAT_H_
#define _DX9_COMPAT_H_

// DirectX 9 升级兼容性头文件
// 用于在DirectX 7和DirectX 9之间提供兼容性层

#ifdef DIRECTX9_UPGRADE

//=============================================================================
// DirectX 9 头文件包含 - 解决与DirectX Retained Mode的冲突
//=============================================================================
// 先包含DirectX 7头文件以支持DirectX Retained Mode
#undef DIRECT3D_VERSION
#define DIRECT3D_VERSION 0x0700
#include <d3d.h>

// 然后重新定义版本并包含DirectX 9
#undef DIRECT3D_VERSION
#define DIRECT3D_VERSION 0x0900

// 禁用DirectX 9Ex扩展以避免兼容性问题
#define D3D_DISABLE_9EX

// 包含DirectX 9头文件
#include <d3d9.h>

//=============================================================================
// 接口映射宏定义
//=============================================================================
// 设备接口映射
#define CURRENT_D3DDEVICE   lpDraw->lpD3DEVICE_9
#define CURRENT_D3D         lpDraw->lpD3_9

// 渲染状态映射 (DirectX 7 → DirectX 9)
#define D3DRENDERSTATE_CLIPPING_COMPAT          D3DRS_CLIPPING
#define D3DRENDERSTATE_LIGHTING_COMPAT          D3DRS_LIGHTING
#define D3DRENDERSTATE_LASTPIXEL_COMPAT         D3DRS_LASTPIXEL
#define D3DRENDERSTATE_ZENABLE_COMPAT           D3DRS_ZENABLE
#define D3DRENDERSTATE_ALPHABLENDENABLE_COMPAT  D3DRS_ALPHABLENDENABLE
#define D3DRENDERSTATE_CULLMODE_COMPAT          D3DRS_CULLMODE
#define D3DRENDERSTATE_SRCBLEND_COMPAT          D3DRS_SRCBLEND
#define D3DRENDERSTATE_DESTBLEND_COMPAT         D3DRS_DESTBLEND

// 混合模式映射
#define D3DBLEND_SRCALPHA_COMPAT    D3DBLEND_SRCALPHA
#define D3DBLEND_INVSRCALPHA_COMPAT D3DBLEND_INVSRCALPHA
#define D3DBLEND_ONE_COMPAT         D3DBLEND_ONE
#define D3DBLEND_ZERO_COMPAT        D3DBLEND_ZERO

// 剔除模式映射
#define D3DCULL_NONE_COMPAT         D3DCULL_NONE
#define D3DCULL_CW_COMPAT           D3DCULL_CW
#define D3DCULL_CCW_COMPAT          D3DCULL_CCW

// Z缓冲映射
#define D3DZB_FALSE_COMPAT          D3DZB_FALSE
#define D3DZB_TRUE_COMPAT           D3DZB_TRUE

// 纹理阶段状态映射 (DirectX 9中基本兼容)
#define D3DTSS_ALPHAOP_COMPAT       D3DTSS_ALPHAOP
#define D3DTSS_ALPHAARG1_COMPAT     D3DTSS_ALPHAARG1
#define D3DTSS_ALPHAARG2_COMPAT     D3DTSS_ALPHAARG2

// 纹理操作映射
#define D3DTOP_MODULATE_COMPAT      D3DTOP_MODULATE
#define D3DTA_TEXTURE_COMPAT        D3DTA_TEXTURE
#define D3DTA_DIFFUSE_COMPAT        D3DTA_DIFFUSE

// 图元类型映射
#define D3DPT_TRIANGLEFAN_COMPAT    D3DPT_TRIANGLEFAN
#define D3DPT_TRIANGLELIST_COMPAT   D3DPT_TRIANGLELIST

// 顶点格式映射
#define D3DFVF_BLTPOINT_COMPAT      D3DFVF_BLTPOINT

//=============================================================================
// 函数映射宏
//=============================================================================
// 设备创建函数映射
#define CreateD3DDevice_COMPAT(adapter, devtype, hwnd, flags, pp, device) \
    lpDraw->lpD3_9->CreateDevice(adapter, devtype, hwnd, flags, pp, device)

// 渲染状态设置映射
#define SetRenderState_COMPAT(state, value) \
    CURRENT_D3DDEVICE->SetRenderState(state, value)

// 纹理阶段状态设置映射
#define SetTextureStageState_COMPAT(stage, type, value) \
    CURRENT_D3DDEVICE->SetTextureStageState(stage, type, value)

// 纹理设置映射
#define SetTexture_COMPAT(stage, texture) \
    CURRENT_D3DDEVICE->SetTexture(stage, (LPDIRECT3DTEXTURE9)texture)

// 视口设置映射
#define SetViewport_COMPAT(viewport) \
    CURRENT_D3DDEVICE->SetViewport(viewport)

// 图元绘制映射
#define DrawPrimitive_COMPAT(primtype, startvertex, primcount) \
    CURRENT_D3DDEVICE->DrawPrimitive(primtype, startvertex, primcount)

//=============================================================================
// 错误处理宏
//=============================================================================
#define DX9_CHECK_RESULT(hr, action) \
    if (FAILED(hr)) { \
        /* 记录错误信息 */ \
        action; \
    }

#else // !DIRECTX9_UPGRADE

//=============================================================================
// DirectX 7 兼容性定义
//=============================================================================
// 当不使用DirectX 9升级时，保持原有的DirectX 7定义
#define CURRENT_D3DDEVICE   lpDraw->lpD3DEVICE
#define CURRENT_D3D         lpDraw->lpD3

// 保持原有的渲染状态常量
#define D3DRENDERSTATE_CLIPPING_COMPAT          D3DRENDERSTATE_CLIPPING
#define D3DRENDERSTATE_LIGHTING_COMPAT          D3DRENDERSTATE_LIGHTING
#define D3DRENDERSTATE_LASTPIXEL_COMPAT         D3DRENDERSTATE_LASTPIXEL
#define D3DRENDERSTATE_ZENABLE_COMPAT           D3DRENDERSTATE_ZENABLE
#define D3DRENDERSTATE_ALPHABLENDENABLE_COMPAT  D3DRENDERSTATE_ALPHABLENDENABLE
#define D3DRENDERSTATE_CULLMODE_COMPAT          D3DRENDERSTATE_CULLMODE
#define D3DRENDERSTATE_SRCBLEND_COMPAT          D3DRENDERSTATE_SRCBLEND
#define D3DRENDERSTATE_DESTBLEND_COMPAT         D3DRENDERSTATE_DESTBLEND

// Other compatibility definitions remain the same

#endif // DIRECTX9_UPGRADE

//=============================================================================
// 通用工具函数声明
//=============================================================================
#ifdef __cplusplus
extern "C" {
#endif

// DirectX 9兼容性检测函数
BOOL IsDirectX9Available(void);

// 设备能力检测函数
BOOL CheckDeviceCapabilities(void);

// 错误信息获取函数
const char* GetDirectXErrorString(HRESULT hr);

#ifdef __cplusplus
}
#endif

#endif // _DX9_COMPAT_H_
