﻿/***************************************
			newSprdisp.cpp
***************************************/

#ifdef PUK2

//**************************************************************************/
// 	表示データをバッファに溜める（アクションタスク以外）(ｕ、ｖ值使用时はこちらを使用)(ｕ、ｖ值の 0 は、元ビットマップの中心を指す)
//**************************************************************************/
// 	引数：	int x, int y	：表示座标
//			UCHAR dispPrio	：表示のプライオリティ
//			int bmpNo：ＢＭＰ番号
//			BOOL hitFlag	：当たり判定フラグ	０：判定无し
//												１：当たり判定のみ
//												２：当たり判定ボックスも表示
//			char use		：realGetNo()をするかの指定
//												０：しない
//												１：する
//			struct BLT_MEMBER *bm
//							：绘图设定（ＮＵＬＬ可）
//	戾り值：自分の判定番号
//**************************************************************************/
int StockDispBuffer_PUK2( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, char use, struct BLT_MEMBER *bm )
{	
	short dx,dy;
	int BmpNo;
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
	
	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return -2;
	
	// ＢＭＰがセットされてないとき
	if( -1 <= bmpNo && bmpNo <= CG_INVISIBLE ) return -2;
	
	// 通常表示のとき
	if( bmpNo > CG_INVISIBLE ){
		if (use) realGetNo( bmpNo , (U4 *)&BmpNo );
		else BmpNo=bmpNo;
		realGetPos( BmpNo  , &dx, &dy);
	}else{
		// ボックス表示のときは变换しない
		dx = 0;
		dy = 0;
		BmpNo = bmpNo;
	}

	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->x = x;
	pDispInfo->y = y;
	pDispInfo->bmpNo = BmpNo;
	pDispInfo->hitFlag = hitFlag;
	pDispInfo->pAct = NULL;
	if (bm){
		pDispInfo->bm=*bm;
	}else{
		pDispInfo->bm.u=0;
		pDispInfo->bm.v=0;
		pDispInfo->bm.w=0;
		pDispInfo->bm.h=0;
		pDispInfo->bm.rgba.rgba=0xffffffff;
		pDispInfo->bm.BltVer=0;
		pDispInfo->bm.bltf=0;
		pDispInfo->bm.PalNo=0;
	}
	pDispInfo->bm.u-=dx;
	pDispInfo->bm.v-=dy;
	pDispInfo->type=DITYPE_BMP_NOMAL;
	if (pDispInfo->bm.BltVer==BLTVER_PUK2) pDispInfo->type=DITYPE_BMP_PUK2;
	if( BmpNo >= 3096 && BmpNo <= 3096 + 9 ) draw_sea_flg=1;

	// 表示カウンタープラス
	return DispBuffer.DispCnt++;
}

// アルファブレンドの描画指令をバッファに溜める ++++
void StockAlphaDispBuffer( short lx, short ly, short lw, short lh, UCHAR dispPrio, unsigned long rgba )
{
	short x[2]={lx,lx+lw},y[2]={ly,ly+lh};
	struct BLT_MEMBER bm={0};
	int i;

	bm.rgba.rgba=rgba;
	// バッファに溜める
	i=StockDispBuffer( *( (int *)x ), *( (int *)y ), dispPrio, STOCK_DISP_BUFFER_NO_BMP|STOCK_DISP_BUFFER_ALPHA, 0, &bm );

	DispBuffer.DispInfo[i].type = DITYPE_PAINT_ALPHA;
}

// アルファブレンドの描画を呼び出す ++++
void AlphaCall( DISP_INFO *pDispInfo )
{
	short *x=(short *)&pDispInfo->x,*y=(short *)&pDispInfo->y;

	if ( RectAlphaBlend( x[0], x[1], y[0], y[1], pDispInfo->bm.rgba.rgba, pDispInfo->bm.bltf ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
}

//----------------------------------//

void GetSurface( int bmpNo, LPBITMAPINFO lpBmpInfo );

char CheckSurfacePal( int bmpNo, short lu, short lv, short lw, short lh, int *p_PalNo, int *p_Palcnt, int AnimPalNo=0 );
void AllocateBmpToSurface_PUK2( int bmpNo, short lu, short lv, short lw, short lh, LPBITMAPINFO lpBmpInfo, char usechg );

extern int palchgcnt;
int nowchging=-1;
int RealBinBmpNo;

//---------------------------------------------------------------------------//
// 概要 ：RECT座标が示す????????????????????から??????????????????へ高速転送                    //
// 引数 ：DIRECT_DRAW *lpDraw : DirectDraw管理构造体                         //
//        short  bx           : 描画先横位置                                 //
//        short  by           : 描画先縦位置                                 //
//        LPDIRECTDRAWSURFACE7 lpSurface : 描画元サーフェス                  //
// 戾值 ：DD_OK:正常終了                                                     //
//---------------------------------------------------------------------------//
void DrawSurface_PUK2( SPRITE_INFO *lpSpi, DISP_INFO *lpdi )
{
	SURFACE_INFO *psfi,*psfi2;	// サーフェース参照用
	char wq,hq;					// 絵の縦横のサーフェースの枚数
	char wfs,hfs;				// 切り取り始めのサーフェースの位置
	char wfsp,hfsp;				// 切り取り始めのサーフェースの切り取り始めの位置
	unsigned long bltf;			// 描画方法
	RECT rc;					// 転送元座标
	short x,y;					// 転送先座标
	int i,j;					// ループカウンタ
	short lu=lpdi->bm.u,lv=lpdi->bm.v,lw=lpdi->bm.w,lh=lpdi->bm.h;

	x=lpdi->x;
	y=lpdi->y;

	if (lu+lw<0) return;
	if (lv+lh<0) return;
	if (lu<0){ x-=lu,	lw+=lu,	lu=0; }
	if (lv<0){ y-=lv,	lh+=lv,	lv=0; }

	if (lu>lpSpi->width) return;
	if (lv>lpSpi->height) return;
	if (lu+lw>lpSpi->width) lw=lpSpi->width-lu;
	if (lv+lh>lpSpi->height) lh=lpSpi->height-lv;

	// 絵の縦横のサーフェースの枚数取得
	wq=(lpSpi->width+SurfaceSizeX-1)/SurfaceSizeX;
	hq=(lpSpi->height+SurfaceSizeY-1)/SurfaceSizeY;

	// 切り取り始めのサーフェースの位置取得
	wfs=lu/SurfaceSizeX;
	hfs=lv/SurfaceSizeY;

	// 切り取り始めのサーフェースの切り取り始めの位置取得
	wfsp=lu%SurfaceSizeX;
	hfsp=lv%SurfaceSizeY;

	// 最初に描画するサーフェースに移动
	psfi=lpSpi->lpSurfaceInfo;
	for(j=0;j<hfs;j++){
		for(i=0;i<wq;i++){
			if (psfi) psfi=psfi->pNext;
			else break;
		}
	}
	for(i=0;i<wfs;i++){
		if (psfi) psfi=psfi->pNext;
		else break;
	}

	// 描画处理
	bltf=lpdi->bm.bltf;
	for(j=0;j<lh;){
		// 縦の切り取り位置の指定
		if (j==0) rc.top=hfsp;
		else rc.top=0;
		if (j+(SurfaceSizeY-rc.top)>lh) rc.bottom=rc.top+lh-j;
		else rc.bottom=SurfaceSizeY;

		psfi2=psfi;
		for(i=0;i<lw;){
			if (psfi==NULL) break;
			// 横の切り取り位置の指定
			if (i==0) rc.left=wfsp;
			else rc.left=0;
			if (i+(SurfaceSizeX-rc.left)>lw) rc.right=rc.left+lw-i;
			else rc.right=SurfaceSizeX;

			// 描画
			// ポインタあるときかつ、当倍でないとき
			if( lpdi->pAct != NULL && ( lpdi->pAct->scaleX != 1.0 || lpdi->pAct->scaleY != 1.0 ) ){
				// 扩大缩小転送
				if( DrawSurfaceStretch_PUK2(	x + ( int )( ( float )( (bltf&BLTF_MRR_X)?lw-i-(rc.right-rc.left):i ) * lpdi->pAct->scaleX ), 
												y + ( int )( ( float )( (bltf&BLTF_MRR_Y)?lh-j-(rc.bottom-rc.top):j ) * lpdi->pAct->scaleY ), 
												&rc,
												lpdi->pAct->scaleX, 
												lpdi->pAct->scaleY,
#ifdef PUK2
												psfi->lpSurface, bltf, lpdi->bm.rgba.rgba ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
#else
												psfi->lpSurface ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
#endif
			}else{
				// 当倍転送
				//rc.right-rc.left与rc.bottom-rc.top结果为long,出现编译警告,暂时增加强制转换(short),影响未知
				if( DrawSurfaceFast_PUK2( 	x + ( (bltf&BLTF_MRR_X)?lw-i-(short)(rc.right-rc.left):i ),
											y + ( (bltf&BLTF_MRR_Y)?lh-j-(short)(rc.bottom-rc.top):j ),
											rc,
#ifdef PUK2
											psfi->lpSurface, bltf, lpdi->bm.rgba.rgba ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
#else
											psfi->lpSurface ) == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
#endif
			}
			psfi=psfi->pNext;
			i+=rc.right-rc.left;
		}
		j+=rc.bottom-rc.top;
		if (j<lh){
			psfi=psfi2;
			for(i=0;i<wq;i++){
				if (psfi) psfi=psfi->pNext;
				else break;
			}
		}
	}
}

int palchgcnt;
extern int nowchging;

BOOL LoadBmp_VerNomal( DISP_INFO *pDispInfo );
BOOL LoadBmp_VerPUK2( DISP_INFO *pDispInfo );

void DrawBitmapToSurface_AnimPal( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo, int PalNo, char pchg );

#ifdef PUK2_DIFFPAL_SURFACE
BOOL MakeSurface( DISP_INFO *di, SURFACE_INFO *si )
{
	SPRITE_INFO *sp = &SpriteInfo[di->bmpNo];
	int PalNo, PalCnt;
	int sizeX, sizeY;

	PalNo = PalState.palNo,	PalCnt = PalState.count;
	if ( di->bm.bltf & BLTF_NOCHG ){ PalNo = 0;		PalCnt = 0; }

	// 准备されただけなら作成する
	if ( si->palchg != 2 ){
		// 目的とするパレットで作られたデータかを确认
		if ( si->PalNo == PalNo && si->PalCount == PalCnt ) return TRUE;
	}

	if (LimiteLoadBmpFlag){
		if ( sp->BmpVer ){
			if ( si->PalNo >= 0 ){
				if ( palchgcnt >= MAXPALCHGQ ){
					if ( si->palchg != 2 ) return TRUE;
				}
				palchgcnt++;
			}
		}
	}

	// バッファにあるデータが必要とするものと违うなら
	if ( RealBinBmpNo != di->bmpNo ){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if ( realGetImage( di->bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &RealBinPalSize ) == FALSE ){
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_37, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo = -1;
			pRealBinBits = 0;
			RealBinWidth = RealBinHeight = 0;
			RealBinPalSize = 0;
			// 次回确实に読み込みなおすように
			si->PalNo = -1;
			si->PalCount = -1;
			return FALSE;
		}
		RealBinBmpNo = di->bmpNo;
	}

	// 横に余りがあるかを确认
	sizeX = SurfaceSizeX;
	if ( si->offsetX + sizeX > sp->width ) sizeX = sp->width - si->offsetX;
	
	// 縦に余りがあるかを确认
	sizeY = SurfaceSizeY;
	if ( si->offsetY + sizeY > sp->height ) sizeY = sp->height - si->offsetY;

	// 余りがあるなら先ず黒で初期化する
	if ( sizeX<SurfaceSizeX || sizeY<SurfaceSizeY ){
		ClearSurface(si->lpSurface);
	}

	// ＢＭＰをサーフェスへ転送
	if (di->bm.PalNo){
		if ( !SprPal[ di->bm.PalNo ].dt ) return FALSE;
		if ( !DrawBitmapToSurface4( si->lpSurface,
			 si->offsetX, sp->height - si->offsetY - 1,
			 sizeX, sizeY, NULL,
			 ( !(di->bm.bltf&BLTF_NOCHG) ?
				 SprPal[ di->bm.PalNo ].dt->ChgAf :
				 SprPal[ di->bm.PalNo ].dt->ChgBf) ) ){
			// 次回确实に読み込みなおすように
			si->PalNo = -1;
			si->PalCount = -1;
			return FALSE;
		 }
	}else{
		if (sp->lpPalList){
			// 目的とするパレットか
			if ( sp->lpPalList->palNo != PalNo ||
				 sp->lpPalList->palcnt != PalCnt ){
				sp->lpPalList->sprNo = -1;
				sp->lpPalList = NULL;
			}
		}
		if (!sp->lpPalList){
			if ( RealBinPalSize > 0 ){
				sp->lpPalList = MakePalList( di->bmpNo, GETPALDATA_PUK2PAL,
					 !(di->bm.bltf&BLTF_NOCHG),
					 (PALDATA *)(pRealBinBits+RealBinWidth*RealBinHeight), NULL );
			}else{
				if ( di->bm.bltf & BLTF_NOCHG ){
					sp->lpPalList = MakePalList( di->bmpNo, GETPALDATA_OLDDEFPAL,
						 !(di->bm.bltf&BLTF_NOCHG), NULL, NULL );
				}
			}
		}

		// ＢＭＰをサーフェスへ転送
		if ( !DrawBitmapToSurface4( si->lpSurface,
			 si->offsetX, sp->height - si->offsetY - 1,
			 sizeX, sizeY, NULL,
			 (sp->lpPalList?sp->lpPalList->pal:NULL) ) ){
			// 次回确实に読み込みなおすように
			si->PalNo = -1;
			si->PalCount = -1;
			return FALSE;
		}

		// ビットマップがパレットを持っているかを记録
		sp->BmpVer = 0;
		if ( RealBinPalSize > 0 ) sp->BmpVer = 1;
	}

	si->PalNo = PalNo;
	si->PalCount = PalCnt;
	si->AnimPalNo = di->bm.PalNo;

	si->palchg = 1;
	if ( di->bm.bltf & BLTF_NOCHG ) si->palchg = 0;

	return TRUE;
}
#else
BOOL MakeSurface( DISP_INFO *di, SURFACE_INFO *si )
{
	SPRITE_INFO *sp = &SpriteInfo[di->bmpNo];
	int PalNo, PalCnt;
	int sizeX, sizeY;

	PalNo = PalState.palNo,	PalCnt = PalState.count;
	if ( di->bm.bltf & BLTF_NOCHG ){ PalNo = 0;		PalCnt = 0; }

	// 目的とするパレットで作られたデータかを确认
	if ( si->PalNo == PalNo && si->PalCount == PalCnt &&
		 si->AnimPalNo == di->bm.PalNo ) return TRUE;

	if (LimiteLoadBmpFlag){
		if ( sp->BmpVer ){
			if ( si->PalNo >= 0 ){
				if ( palchgcnt >= MAXPALCHGQ ){
					return TRUE;
				}
				palchgcnt++;
			}
		}
	}
	// 目的とするパレットで作られたデータかを确认
	if ( si->PalNo == PalNo && si->PalCount == PalCnt &&
		 si->AnimPalNo == di->bm.PalNo ) return TRUE;

	// バッファにあるデータが必要とするものと违うなら
	if ( RealBinBmpNo != di->bmpNo ){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if ( realGetImage( di->bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &RealBinPalSize ) == FALSE ){
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_38, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo = -1;
			pRealBinBits = 0;
			RealBinWidth = RealBinHeight = 0;
			RealBinPalSize = 0;
			return FALSE;
		}
		RealBinBmpNo = di->bmpNo;
	}

	// 横に余りがあるかを确认
	sizeX = SurfaceSizeX;
	if ( si->offsetX + sizeX > sp->width ) sizeX = sp->width - si->offsetX;
	
	// 縦に余りがあるかを确认
	sizeY = SurfaceSizeY;
	if ( si->offsetY + sizeY > sp->height ) sizeY = sp->height - si->offsetY;

	// 余りがあるなら先ず黒で初期化する
	if ( sizeX<SurfaceSizeX || sizeY<SurfaceSizeY ){
		ClearSurface(si->lpSurface);
	}

	// ＢＭＰをサーフェスへ転送
	if (di->bm.PalNo){
		if (SprPal[ di->bm.PalNo ].dt){
			DrawBitmapToSurface4( si->lpSurface,
				 si->offsetX, sp->height - si->offsetY - 1,
				 sizeX, sizeY, NULL,
				 ( !(di->bm.bltf&BLTF_NOCHG) ?
					 SprPal[ di->bm.PalNo ].dt->ChgAf :
					 SprPal[ di->bm.PalNo ].dt->ChgBf) );
		}
	}else{
		if (sp->lpPalList){
			// 目的とするパレットか
			if ( sp->lpPalList->palNo != PalNo ||
				 sp->lpPalList->palcnt != PalCnt ){
				sp->lpPalList->sprNo = -1;
				sp->lpPalList = NULL;
			}
		}
		if (!sp->lpPalList){
			if ( RealBinPalSize > 0 ){
				sp->lpPalList = MakePalList( di->bmpNo, GETPALDATA_PUK2PAL,
					 !(di->bm.bltf&BLTF_NOCHG),
					 (PALDATA *)(pRealBinBits+RealBinWidth*RealBinHeight), NULL );
			}else{
				if ( di->bm.bltf & BLTF_NOCHG ){
					sp->lpPalList = MakePalList( di->bmpNo, GETPALDATA_OLDDEFPAL,
						 !(di->bm.bltf&BLTF_NOCHG), NULL, NULL );
				}
			}
		}

		// ＢＭＰをサーフェスへ転送
		DrawBitmapToSurface4( si->lpSurface,
			 si->offsetX, sp->height - si->offsetY - 1,
			 sizeX, sizeY, NULL,
			 (sp->lpPalList?sp->lpPalList->pal:NULL) );

		// ビットマップがパレットを持っているかを记録
		sp->BmpVer = 0;
		if ( RealBinPalSize > 0 ) sp->BmpVer = 1;
	}

	si->PalNo = PalNo;
	si->PalCount = PalCnt;
	si->AnimPalNo = di->bm.PalNo;

	return TRUE;
}
#endif

char PrepareSprPal( DISP_INFO *pDispInfo );

// ＢＭＰの描画 /////////////////////////////////////////////////////////////////
void PutBmp( void )
{
	SURFACE_INFO *lpSurfaceInfo;
#ifdef PUK2_DIFFPAL_SURFACE
	SURFACE_INFO *lpSurfaceInfoTop;
#endif
	DISP_SORT 	*pDispSort = DispBuffer.DispSort;
	DISP_INFO 	*pDispInfo;
	int i;
	int bmpNo;
#ifdef _DEBUG
	int putFontFlag=0;
#endif
	int x, y;			// 描画先の座标
	int u, v, w, h;		// 描画元の范围
	RECT rc;			// 描画元の范围
#ifdef PUK2_DIFFPAL_SURFACE
	int ox, oy;
#endif
	int add;

	// パレットチェンジ数の初期化
	palchgcnt=0;

	if ( lpDraw->lpBACKBUFFER->IsLost() != DD_OK ) return;
	if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();

	// 溜められた数だけループ
	for( i = 0; i < DispBuffer.DispCnt ; i++, pDispSort++ ){
		// 表示データの入っているアドレスにセット
		pDispInfo = DispBuffer.DispInfo + pDispSort->no;

		switch(pDispInfo->type){
		case DITYPE_BMP_NOMAL:			// 絵の描画(ＵＶ值无し)
		case DITYPE_BMP_PUK2:			// 絵の描画(ＵＶ值有り)
#ifdef PUK2_DIFFPAL_SURFACE
			bmpNo = pDispInfo->bmpNo;

			// リミットチェック
			if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) break;

			// アニメーションパレットのパレットチェンジ
			PrepareSprPal(pDispInfo);

			// ビットマップサイズ取得未だなら取得する
			// サーフェースが无い场合、この关数で准备される
			if ( !GetBmpSize(bmpNo) ) break;

			// サーフェースがなぜか无いなら
			if ( !SpriteInfo[bmpNo].lpSurfaceInfo ) break;

			// GetBmpSize()でサーフェースが准备されたなら
			if ( SpriteInfo[bmpNo].lpSurfaceInfo->palchg == 2 ){
				lpSurfaceInfoTop = SpriteInfo[bmpNo].lpSurfaceInfo;
			}
			// そうでないなら
			else{
				// 目的のサーフェースを探す
				lpSurfaceInfoTop = SearchPaletteSurface( bmpNo,
					 pDispInfo->bm.PalNo,
					 ((pDispInfo->bm.bltf&BLTF_NOCHG)?0:1) );
				// 目的のパレットのサーフェースが无いなら
				if ( lpSurfaceInfoTop == NULL ){
					// サーフェースの作成
					GetPaletteSurface( bmpNo,
						 pDispInfo->bm.PalNo,
						 ((pDispInfo->bm.bltf&BLTF_NOCHG)?0:1) );
					lpSurfaceInfoTop = SearchPaletteSurface( bmpNo,
						 pDispInfo->bm.PalNo,
						 ((pDispInfo->bm.bltf&BLTF_NOCHG)?0:1) );
				}
			}
			// 结局取得できなかったら
			if ( lpSurfaceInfoTop == NULL ) break;

			// ＵＶ值无しなら、ここででっち上げる
			if ( pDispInfo->type == DITYPE_BMP_NOMAL ){
				pDispInfo->bm.u = pDispInfo->bm.v = 0;
				pDispInfo->bm.w = SpriteInfo[bmpNo].width;
				pDispInfo->bm.h = SpriteInfo[bmpNo].height;
			}

			ox = pDispInfo->x,		oy = pDispInfo->y;
			u = pDispInfo->bm.u,	v = pDispInfo->bm.v;
			w = pDispInfo->bm.w,	h = pDispInfo->bm.h;

			// 描画する所がないなら
			if ( u+w<0 || v+h<0 ) break;
			if ( u>SpriteInfo[bmpNo].width || v>SpriteInfo[bmpNo].height ) break;

			// クリッピング
			if ( u < 0 ){ ox -= u,	w += u,	u = 0; }
			if ( v < 0 ){ oy -= v,	h += v,	v = 0; }
			if ( u+w > SpriteInfo[bmpNo].width ) w = SpriteInfo[bmpNo].width - u;
			if ( v+h > SpriteInfo[bmpNo].height ) h = SpriteInfo[bmpNo].height - v;

			// 描画する所がないなら
			if ( w<=0 || h<=0 ) break;

			// ポインタあるときかつ、当倍でないとき
			if( pDispInfo->pAct != NULL && ( pDispInfo->pAct->scaleX != 1.0 || pDispInfo->pAct->scaleY != 1.0 ) ){
				// サーフェスリストの数だけループ
				for( lpSurfaceInfo=lpSurfaceInfoTop;
					 lpSurfaceInfo!=NULL;lpSurfaceInfo=lpSurfaceInfo->pNext ){
					// サーフェスを使用した日付を记忆
					lpSurfaceInfo->date = SurfaceDate;

					// 描画しないサーフェースなら
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY < v ) continue;
					if ( lpSurfaceInfo->offsetY > v+h ) continue;
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX < u ) continue;
					if ( lpSurfaceInfo->offsetX > u+w ) continue;
	
					// 描画できるようにサーフェースを准备する
					if ( !MakeSurface( pDispInfo, lpSurfaceInfo ) ) continue;
	
					// 范围计算
					rc.left = 0;
					if ( lpSurfaceInfo->offsetX < u ) rc.left = u - lpSurfaceInfo->offsetX;
					rc.right = SurfaceSizeX;
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX > u+w ) rc.right = (u+w) - lpSurfaceInfo->offsetX;
					rc.top = 0;
					if ( lpSurfaceInfo->offsetY < v ) rc.top = v - lpSurfaceInfo->offsetY;
					rc.bottom = SurfaceSizeY;
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY > v+h ) rc.bottom = (v+h) - lpSurfaceInfo->offsetY;

					// 描画先座标の计算
					x = ox;
					if ( pDispInfo->bm.bltf & BLTF_MRR_X ){
						add = SpriteInfo[bmpNo].width - ( lpSurfaceInfo->offsetX - u ) - (rc.right-rc.left);
						add -= rc.left;
						x += (int)(add*pDispInfo->pAct->scaleX);
					}else{
						add = lpSurfaceInfo->offsetX - u;
						add += rc.left;
						x += (int)(add*pDispInfo->pAct->scaleX);
					}
					y = oy;
					if ( pDispInfo->bm.bltf & BLTF_MRR_Y ){
						add = SpriteInfo[bmpNo].height - ( lpSurfaceInfo->offsetY - v ) - (rc.bottom-rc.top);
						add -= rc.top;
						y += (int)(add*pDispInfo->pAct->scaleY);
					}else{
						add = lpSurfaceInfo->offsetY - v;
						add += rc.top;
						y += (int)(add*pDispInfo->pAct->scaleY);
					}

					// 扩大缩小転送
					if ( DrawSurfaceStretch_PUK2( x, y, &rc,
						 pDispInfo->pAct->scaleX, pDispInfo->pAct->scaleY,
						 lpSurfaceInfo->lpSurface, pDispInfo->bm.bltf,
						 pDispInfo->bm.rgba.rgba )
						 == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
				}
			}else{
				// サーフェスリストの数だけループ
				for( lpSurfaceInfo=lpSurfaceInfoTop;
					 lpSurfaceInfo!=NULL;lpSurfaceInfo=lpSurfaceInfo->pNext ){
					// サーフェスを使用した日付を记忆
					lpSurfaceInfo->date = SurfaceDate;

					 // 描画しないサーフェースなら
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX < u ) continue;
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY < v ) continue;
					if ( lpSurfaceInfo->offsetX > u+w ) continue;
					if ( lpSurfaceInfo->offsetY > v+h ) continue;

					// 描画できるようにサーフェースを准备する
					if ( !MakeSurface( pDispInfo, lpSurfaceInfo ) ) continue;

					// 范围计算
					rc.left = 0;
					if ( lpSurfaceInfo->offsetX < u ) rc.left = u - lpSurfaceInfo->offsetX;
					rc.right = SurfaceSizeX;
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX > u+w ) rc.right = (u+w) - lpSurfaceInfo->offsetX;
					rc.top = 0;
					if ( lpSurfaceInfo->offsetY < v ) rc.top = v - lpSurfaceInfo->offsetY;
					rc.bottom = SurfaceSizeY ;
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY > v+h ) rc.bottom = (v+h) - lpSurfaceInfo->offsetY;

					// 描画先座标の计算
					x = ox;
					if ( pDispInfo->bm.bltf & BLTF_MRR_X ){
						add = SpriteInfo[bmpNo].width - ( lpSurfaceInfo->offsetX - u ) - (rc.right-rc.left);
						add -= rc.left;
						x += add;
					}else{
						add = lpSurfaceInfo->offsetX - u;
						add += rc.left;
						x += add;
					}
					y = oy;
					if ( pDispInfo->bm.bltf & BLTF_MRR_Y ){
						add = SpriteInfo[bmpNo].height - ( lpSurfaceInfo->offsetY - v ) - (rc.bottom-rc.top);
						add -= rc.top;
						y += add;
					}else{
						add = lpSurfaceInfo->offsetY - v;
						add += rc.top;
						y += add;
					}

					// 当倍転送
					if ( DrawSurfaceFast_PUK2( x, y, rc,
						 lpSurfaceInfo->lpSurface,
						 pDispInfo->bm.bltf, pDispInfo->bm.rgba.rgba )
						 == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
				}
			}
#else
			bmpNo = pDispInfo->bmpNo;

			// リミットチェック
			if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) break;

			// アニメーションパレットのパレットチェンジ
			PrepareSprPal(pDispInfo);

			// サーフェースが未确保なら确保する(GetBmpSizeでサーフェースの确保も行われる)
			if ( !GetBmpSize(bmpNo) ) break;

			// ＵＶ值无しなら、ここででっち上げる
			if ( pDispInfo->type == DITYPE_BMP_NOMAL ){
				pDispInfo->bm.u = pDispInfo->bm.v = 0;
				pDispInfo->bm.w = SpriteInfo[bmpNo].width;
				pDispInfo->bm.h = SpriteInfo[bmpNo].height;
			}

			u = pDispInfo->bm.u,	v = pDispInfo->bm.v;
			w = pDispInfo->bm.w,	h = pDispInfo->bm.h;

			// 描画する所がないなら
			if ( u+w<0 || v+h<0 ) break;
			if ( u>SpriteInfo[bmpNo].width || v>SpriteInfo[bmpNo].height ) break;

			// クリッピング
			if ( u < 0 ){ pDispInfo->x -= u,	w += u,	u = 0; }
			if ( v < 0 ){ pDispInfo->y -= v,	h += v,	v = 0; }
			if ( u+w > SpriteInfo[bmpNo].width ) w = SpriteInfo[bmpNo].width - u;
			if ( v+h > SpriteInfo[bmpNo].height ) h = SpriteInfo[bmpNo].height - v;

			// 描画する所がないなら
			if ( w<=0 || h<=0 ) break;

			// ポインタあるときかつ、当倍でないとき
			if( pDispInfo->pAct != NULL && ( pDispInfo->pAct->scaleX != 1.0 || pDispInfo->pAct->scaleY != 1.0 ) ){
				// サーフェスリストの数だけループ
				for( lpSurfaceInfo=SpriteInfo[bmpNo].lpSurfaceInfo;
					 lpSurfaceInfo!=NULL;lpSurfaceInfo=lpSurfaceInfo->pNext ){
					// サーフェスを使用した日付を记忆
					lpSurfaceInfo->date = SurfaceDate;

					// 描画しないサーフェースなら
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY < v ) continue;
					if ( lpSurfaceInfo->offsetY > v+h ) continue;
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX < u ) continue;
					if ( lpSurfaceInfo->offsetX > u+w ) continue;
	
					// 描画できるようにサーフェースを准备する
					if ( !MakeSurface( pDispInfo, lpSurfaceInfo ) ) continue;
	
					// 范围计算
					rc.left = 0;
					if ( lpSurfaceInfo->offsetX < u ) rc.left = u - lpSurfaceInfo->offsetX;
					rc.right = SurfaceSizeX;
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX > u+w ) rc.right = (u+w) - lpSurfaceInfo->offsetX;
					rc.top = 0;
					if ( lpSurfaceInfo->offsetY < v ) rc.top = v - lpSurfaceInfo->offsetY;
					rc.bottom = SurfaceSizeY;
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY > v+h ) rc.bottom = (v+h) - lpSurfaceInfo->offsetY;

					// 描画先座标の计算
					x = pDispInfo->x;
					if ( pDispInfo->bm.bltf & BLTF_MRR_X ){
						add = SpriteInfo[bmpNo].width - ( lpSurfaceInfo->offsetX - u ) - (rc.right-rc.left);
						add -= rc.left;
						x += (int)(add*pDispInfo->pAct->scaleX);
					}else{
						add = lpSurfaceInfo->offsetX - u;
						add += rc.left;
						x += (int)(add*pDispInfo->pAct->scaleX);
					}
					y = pDispInfo->y;
					if ( pDispInfo->bm.bltf & BLTF_MRR_Y ){
						add = SpriteInfo[bmpNo].height - ( lpSurfaceInfo->offsetY - v ) - (rc.bottom-rc.top);
						add -= rc.top;
						y += (int)(add*pDispInfo->pAct->scaleY);
					}else{
						add = lpSurfaceInfo->offsetY - v;
						add += rc.top;
						y += (int)(add*pDispInfo->pAct->scaleY);
					}

					// 扩大缩小転送
					if ( DrawSurfaceStretch_PUK2( x, y, &rc,
						 pDispInfo->pAct->scaleX, pDispInfo->pAct->scaleY,
						 lpSurfaceInfo->lpSurface, pDispInfo->bm.bltf,
						 pDispInfo->bm.rgba.rgba )
						 == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
				}
			}else{
				// サーフェスリストの数だけループ
				for( lpSurfaceInfo=SpriteInfo[bmpNo].lpSurfaceInfo;
					 lpSurfaceInfo!=NULL;lpSurfaceInfo=lpSurfaceInfo->pNext ){
					// サーフェスを使用した日付を记忆
					lpSurfaceInfo->date = SurfaceDate;

					 // 描画しないサーフェースなら
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX < u ) continue;
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY < v ) continue;
					if ( lpSurfaceInfo->offsetX > u+w ) continue;
					if ( lpSurfaceInfo->offsetY > v+h ) continue;

					// 描画できるようにサーフェースを准备する
					if ( !MakeSurface( pDispInfo, lpSurfaceInfo ) ) continue;

					// 范围计算
					rc.left = 0;
					if ( lpSurfaceInfo->offsetX < u ) rc.left = u - lpSurfaceInfo->offsetX;
					rc.right = SurfaceSizeX;
					if ( lpSurfaceInfo->offsetX+SurfaceSizeX > u+w ) rc.right = (u+w) - lpSurfaceInfo->offsetX;
					rc.top = 0;
					if ( lpSurfaceInfo->offsetY < v ) rc.top = v - lpSurfaceInfo->offsetY;
					rc.bottom = SurfaceSizeY ;
					if ( lpSurfaceInfo->offsetY+SurfaceSizeY > v+h ) rc.bottom = (v+h) - lpSurfaceInfo->offsetY;

					// 描画先座标の计算
					x = pDispInfo->x;
					if ( pDispInfo->bm.bltf & BLTF_MRR_X ){
						add = SpriteInfo[bmpNo].width - ( lpSurfaceInfo->offsetX - u ) - (rc.right-rc.left);
						add -= rc.left;
						x += add;
					}else{
						add = lpSurfaceInfo->offsetX - u;
						add += rc.left;
						x += add;
					}
					y = pDispInfo->y;
					if ( pDispInfo->bm.bltf & BLTF_MRR_Y ){
						add = SpriteInfo[bmpNo].height - ( lpSurfaceInfo->offsetY - v ) - (rc.bottom-rc.top);
						add -= rc.top;
						y += add;
					}else{
						add = lpSurfaceInfo->offsetY - v;
						add += rc.top;
						y += add;
					}

					// 当倍転送
					if ( DrawSurfaceFast_PUK2( x, y, rc,
						 lpSurfaceInfo->lpSurface,
						 pDispInfo->bm.bltf, pDispInfo->bm.rgba.rgba )
						 == DDERR_SURFACEBUSY ) SurfaceBusyFlag = TRUE;
				}
			}
#endif
			if (SpriteInfo[bmpNo].lpPalList){
				SpriteInfo[bmpNo].lpPalList->cnt = 0;
			}

			break;
		case DITYPE_PAINT:				// 涂りつぶし
			// 表示バッファーからボックスデータを取り出す
			GetBoxDispBuffer( pDispInfo, pDispInfo->bmpNo );
			continue;

		case DITYPE_PAINT_ALPHA:		// 涂りつぶし(半透明)
			AlphaCall( pDispInfo );
			continue;

		case DITYPE_TEXT:				// 文字描画
			if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();

			// フォントをバックサーフェスにセット
			PutFonts( pDispInfo->pFnt );

			if ( getUsable3D() ) lpDraw->lpD3DEVICE->BeginScene();
			continue;

		case DITYPE_AUTOMAP:			// オートマップ描画
			// オートマップ描画
			drawAutoMap( pDispInfo->x, pDispInfo->y );
			continue;

		case DITYPE_PUT_TILE:			// タイルの描画
			if (highSpeedDrawSw){
				RECT src;
				int sx, sy;

				// バトルサーフェスからバックサーフェスにタイルデータをコピー
				src.left = 0,	src.right = DEF_APPSIZEX;
				src.top = 0,	src.bottom = DEF_APPSIZEY;
				sx = sy = 0;

				if( highSpeedDrawVarX > 0 ){ src.right -= highSpeedDrawVarX;	sx += highSpeedDrawVarX; }
				else if( highSpeedDrawVarX < 0 ) src.left -= highSpeedDrawVarX;

				if( highSpeedDrawVarY > 0 ){ src.bottom -= highSpeedDrawVarY;	sy += highSpeedDrawVarY; }
				else if( highSpeedDrawVarY < 0 ) src.top -= highSpeedDrawVarY;

				lpDraw->lpBACKBUFFER->BltFast( sx, sy, lpBattleSurface, &src, DDBLTFAST_WAIT );
			}
			continue;

		case DITYPE_GET_TILE:			// 假サーフェースにマップタイルの描画
			if (highSpeedDrawSw){
				// バックサーフェスのタイルデータをバトルサーフェスにコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
			}
			continue;

		case DITYPE_SEA:				// 海タイル描画
			draw_special();
			continue;

		case DITYPE_MAPEFFECT:			// オートマップ描画
			// マップのエフェクト
			drawMapEffect();
			continue;
		}
	}
	if ( getUsable3D() ) lpDraw->lpD3DEVICE->EndScene();

	if (!palchgcnt) nowchging=PalState.count;

	// SprPal解放管理
	SprPalDeathCheck();

	CheckPalList();
	draw_sea_flg=0;

#ifdef PUK2_DEBUG_DRAW
	// デバッグ描画データ描画
	_Debug_Draw_DrawFunc();
#endif
}

// 	文字列描画タイミングをバッファに溜める ++++
int StockTextDispBuffer( void *FontBuffer, UCHAR dispPrio )
{
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;

	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return -2;

	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->bmpNo = -1;
	pDispInfo->hitFlag = 0;
	pDispInfo->pAct = NULL;
	pDispInfo->type=DITYPE_TEXT;
	pDispInfo->pFnt = FontBuffer;

	// 表示カウンタープラス
	return DispBuffer.DispCnt++;
}

// 	オートマップ描画タイミングをバッファに溜める ++++
int StockAutoMapDispBuffer( int x, int y, UCHAR dispPrio )
{
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;

	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return -2;

	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->x = x;
	pDispInfo->y = y;
	pDispInfo->bmpNo = -1;
	pDispInfo->hitFlag = 0;
	pDispInfo->pAct = NULL;
	pDispInfo->type=DITYPE_AUTOMAP;

	// 表示カウンタープラス
	return DispBuffer.DispCnt++;
}

#endif