﻿#ifndef _LOGIN_H_
#define _LOGIN_H_

#include"../systeminc/chat.h"

//-------------------------------------------------------------------------//
// 定数定义                                                                //
//-------------------------------------------------------------------------//
enum
{
	LOGIN_FLAG_ALL_OFF					= 0x00,	// フラグのクリア
	LOGIN_FLAG_WAIT_FOR_MAPDATA			= 0x01,	// ログイン时にマップデータを待つフラグ
	LOGIN_FLAG_WAIT_FOR_PC_PARAM 		= 0x02	// 　　〃　　　PCのパラメータを待つフラグ
};


// パッケージバージョン
enum{
	PV_NORMAL = 0,			//通常版
	PV_TRIAL  = 1,			//见习版
	PV_EQUAL  = 2,			//见习を卒业したバージョン
	PV_FIRST_EX   =  3,		// ベータ版の人
	PV_UP_EX      =  4,		// ベータ版の人。事实上见使用
	PV_FIRST_VER2 =  5,		// 最初からバージョン２の人
	PV_UP_VER2    =  6,		// バージョンアップしてバージョン２の人
#ifdef PUK2
	PV_PUK2    =  7,		// PU2
#endif
#ifdef PUK3_UPGRADE
	PV_PUK3    =  8,		// PU3
#endif
};



//-------------------------------------------------------------------------//

extern char selectServerName[][64];
extern char selectServerName2[][64];

extern unsigned char loginFlag;
extern BOOL logOutFlag;

extern short createCharFlag;

// パッケージバージョン
extern BYTE PackageVer;
extern BYTE RecvVerData;

void initInputIdPassword( void );
int inputIdPassword( int );
int InputProbationKey(int flag);
#ifdef PUK2
void PUK2_idPasswordProc( void );
void PUK2_titleProc( void );
void PUK2_selectCharacterProc( void );
void PUK2_upgradeProc( void );
#endif
void V2_idPasswordProc( void );
void idPasswordProc( void );
//void ProbationProc( void );

void UpGradeProc( void );

void titleProc( void );
void selectCharacterProc( void );
void characterLoginProc( void );

void makeCharacterProc( void );

void characterLogoutProc( void );

void initCommonMsgWin( void );
int commonMsgWin( char * );

void initConnecGameServer( void );
int connecGameServer( void );

void initDownloadCharList( void );
int downloadCharList( void );

void initDeleteCharacter( void );
int deleteCharacter( void );

int commonYesNoWindow( int, int, char *, int );
int commonYesNoWindow2( int, int, char *, int, int, int );
int DeleteYesNoWindow( int, int );// ohta


void initInputStr( INPUT_STR *, int, int, int, int, int );

void getStrSplit( char *, char *, int, int, int );


void initUserCertifyErrorMsgWin( void );
int userCertifyErrorMsgWin( void );

int selGraId( int *, int );
int selRepGraId( int *, int );
int pushGraId( int *, int );
int selFontId( int *, int );
int focusGraId( int *, int );
int focusFontId( int *, int );

void drawNumGra( int, int, int );



void initCertifyIdPassword( void );
int certifyIdPassword( char *, char * );

int VirticalCheck( unsigned char *pszBuffer );

	#ifdef PUK2_NEW_MENU
		void makeCharacterProc_PUK2( void );
		void initInputStrNewChat( INPUT_STR *pt, int x, int y,int color, int prio ,int Kind);
	#endif

	#ifdef PUK2_SERVERCHANGE
		void ServerChangeProc();
	#endif
#endif
