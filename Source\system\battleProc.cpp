﻿/************************/
/*	battleProc.cpp		*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/map.h"
#include "../systeminc/menu.h"
#include "../systeminc/process.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/font.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/action.h"
#include "../systeminc/produce.h"
#include "../systeminc/field.h"
#include "../systeminc/battle.h"
#include "../systeminc/mouse.h"
#include "../systeminc/t_music.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/battleMaster.h"
#include "../systeminc/math2.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"
#include "../systeminc/main.h"
#include "../systeminc/character.h"
#include "../systeminc/pattern.h"
#include "../systeminc/tool.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/battleEffect.h"
#include "../systeminc/handleTime.h"

#ifdef PUK2
#include "../puk2/map/newmap.h"
#include "../systeminc/loadsprbin.h"
#endif
#ifdef PUK2_NEW_MENU
	#include "../puk2/interface/menuwin.h"
#endif
#if defined(PUK2) && defined(_DEBUG)
	#include "../systeminc/login.h"
#endif
#ifdef PUK2_ACID_PAPER_SNOWSTORM
	#include "../systeminc/mapeffect.h"
#endif

// エンカウントフラグ
BOOL EncountFlag = FALSE;
// エンカウントの种类
int EncountStatus = 0;
// デュエルフラグ
BOOL DuelFlag = FALSE;
// ヘルプ无しの时
BOOL NoHelpFlag = FALSE;
// エンカウントOFFフラグ
BOOL EncountOffFlag = FALSE;

// 一人用か二人用かフラグ
BOOL Battle1P2PFlag;

// 不意打ち表示ウィンドウ
ACTION* pActSurprisalWnd;
ACTION* pActAudienceExitWnd;

// ターゲットナンバー
int TargetNo;
#ifdef PUK3_NEWSKILL_LVDOWN
	// 背景の变更チェック用
	int BattleMapNoBak;
#endif

#if defined(PUK2) && defined(_DEBUG)
	int battlePlayLine;
	#ifdef PUK3_PROC_BATTLE_ELM
		BOOL elmdispalway = FALSE;
	#endif
#endif
#ifdef PUK2_NEW_MENU
	void BattleMenuProc_PUK2( void );
#endif

#ifdef PUK2_ELEMENTDISP
	// 属性表示 ***********************************************************/
	void ElementDisp();
#endif
#ifdef PUK2
	void SetActAlbumRegist( ACTION *pAct );
#endif

#if 0
// 怪我表示处理 ***********************************************************************/
void BattleInjuryDisp( void )
{
	// 読み込みポイント
	int readPoint = 2;
	int i;
	char moji[ 256 ];
	
	//strcpy( BiData, "I|太田哲生|だれやねん|そんなあほな|どないやねん|" );
	//strcpy( BiData, "I|?????????|??????????蝪???|???????????|??????????|" );
	
	// 人数分ループ
	for( i = 0 ; i < 4 ; i++ ){
		// 文字列取り出し
		if( getStringToken( BiData, '|', i + 2, sizeof( moji ) - 1, moji ) == 1 ) break;
		// ＥＵＣからＳＪＩＳに变换
		eucStringToSjisString( moji );
		// チャットバッファに送る
		StockChatBufferLine( moji, FONT_PAL_RED, FONT_KIND_MIDDLE2 );
	}
}
#endif

// 登场終了チェック *******************************************************************/
int BattleCheckAppear( void )
{
	int i;
	int appearFlag = FALSE;
	
	// 登场してないかチェック
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタ无い时
		if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ i ] );
#endif
		//BC_YOBI *pYobi = ( BC_YOBI *)pActBc[ i ]->pYobi;
		// 登场フラグ立っていなかったら
		if( !( ( ( BC_YOBI * )pActBc[ i ]->pYobi )->bcFlag & BC_FLAG_APPEAR ) ){
			// 登场へ
			pActBc[ i ]->actNo = BC_ACT_APPEAR;
			appearFlag = TRUE;
		}
	}
	// 登场終了したら
	if( appearFlag == FALSE ) return 1;
	
	return 0;
}

// 不意打ち表示处理 **************************************************************/
void SurprisalDisp( void ){

#ifdef PUK2_NEW_MENU
	// 不意ついた时、つかれた时
	if( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL || BattleBpFlag & BP_FLAG_ENEMY_SURPRISAL ){
		if (pActSurprisalWnd==NULL){
			if (WindowFlag[MENU_WINDOW_SURPRISE].wininfo==NULL){
				pActSurprisalWnd = openMenuWindow( MENU_WINDOW_SURPRISE, OPENMENUWINDOW_HIT, 0 );
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			}
		}
	}
#else
	// 不意ついた时、つかれた时
	if( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL || BattleBpFlag & BP_FLAG_ENEMY_SURPRISAL ){
		// ウィンドウ无かったら
		if( pActSurprisalWnd == NULL ){
			// ウィンドウ表示タスク作成
			pActSurprisalWnd = makeWindowDisp( 320 - 96, 240 - 48, 192, 96, 1 );
			// ウィンドウ开く音
			play_se( SE_NO_OPEN_WINDOW, 320, 240 );
		}
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActSurprisalWnd );
#endif
		// ウィンドウ出来あがっていたら
		if( pActSurprisalWnd->hp > 0 ){
			// メールウィンドウ出ていたら
#if 0
			if( MenuToggleFlag & JOY_CTRL_E || MenuToggleFlag & JOY_CTRL_A ){ 
				if( pActSurprisalWnd->x < 320 - 96 + 56 ) pActSurprisalWnd->x += 6;
			}else{ 
				if( pActSurprisalWnd->x > 320 - 96 ) pActSurprisalWnd->x -= 6;
			}
#endif
			// プレイヤーが偷袭时
			if( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL ){
				StockFontBuffer( pActSurprisalWnd->x + 38, pActSurprisalWnd->y + 40, FONT_PRIO_FRONT, 0, 	ML_STRING(183, " 偷袭 "), 0 );
			}
			// 敌が偷袭时（不意つかれた时）
			if( BattleBpFlag & BP_FLAG_ENEMY_SURPRISAL ){
				StockFontBuffer( pActSurprisalWnd->x + 38, pActSurprisalWnd->y + 40, FONT_PRIO_FRONT, 0, 	ML_STRING(184, "被偷袭"), 0 );
			}
		}
	}
#endif
}

// 不意打ち处理 **********************************************************************/
void Surprisal( void )
{
	int i;
	
	BC_YOBI *pYobi;	// 予备构造体
	
	// プレイヤーが偷袭时
	if( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL ){
		// キャラクター分ループ
		for( i = 10 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( i );
#endif
			// ポインタ无い时
			if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ i ] );
#endif
			// 予备构造体
			pYobi = ( BC_YOBI* )pActBc[ i ]->pYobi;
			// 敌を后ろ向けにする
			pActBc[ i ]->dir = Atan( ( float )pYobi->startX - ( float )pYobi->defX, ( float )pYobi->startY - ( float )pYobi->defY );
			// 方向からアングルに变换する
			DirToAngle( pActBc[ i ] );
			// １回だけ呼ぶアニメーション
			pattern( pActBc[ i ], ANM_NO_LOOP );
		}
	}
	// 敌が偷袭时（不意つかれた时）
	if( BattleBpFlag & BP_FLAG_ENEMY_SURPRISAL ){
		// キャラクター分ループ
		for( i = 0 ; i < 10 ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( i );
#endif
			// ポインタ无い时
			if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ i ] );
#endif
			// 予备构造体
			pYobi = ( BC_YOBI* )pActBc[ i ]->pYobi;
			// 敌を后ろ向けにする
			pActBc[ i ]->dir = Atan( ( float )pYobi->startX - ( float )pYobi->defX, ( float )pYobi->startY - ( float )pYobi->defY );
			// 方向からアングルに变换する
			DirToAngle( pActBc[ i ] );
			// １回だけ呼ぶアニメーション
			pattern( pActBc[ i ], ANM_NO_LOOP );
		}
	}
}

// プレイヤーの数を调べる ********************************************/
int CheckBattlePlayerCnt( void )
{
	int i, cnt = 0;
	
	// ２０匹分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// キャラがいるとき
		if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ i ] );
#endif
		// プレイヤーなら
		if( ( ( BC_YOBI * )pActBc[ i ]->pYobi )->bcFlag & BC_FLAG_PLAYER ) cnt++;
	}
	
	return cnt;
}

// バトル时のアクション走らせる **********************************************************/
void BattleRunAction( void )
{
	// スローの时
	if( ActSlowFlag == TRUE ){
		if( ActSlowCnt == 0 ){
			/* アクション走らせる */
			RunAction();
			ActSlowCnt++;
		}else{
			ActSlowCnt++;
			if( ActSlowCnt >= ACT_SLOW_WAIT ) ActSlowCnt = 0;
		}
	}else{
		/* アクション走らせる */
		RunAction();
	}
}
	
// 名称の表示 ************************************************************************/
void BattleNameDisp( void )
{
	int i;
	int color = FONT_PAL_WHITE;
	int fontKind = FONT_KIND_SMALL;
	
	// 名称表示ＯＦＦの时、返回
	if( nameOverTheHeadFlag == FALSE ) return;
	
	// キャラクターの数だけループ
	for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
		// ポインタがない时返回
		if( pActBc[ i ] == NULL ) continue;
		
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ i ] );
#endif
		// 観战じゃないとき
		if( BattleMyNo < BC_MAX ){
			// 自分の时
			if( i == BattleMyNo || i == BattleCheckPetId( BattleMyNo ) ){ 
				// 文字色とサイズ
				color = FONT_PAL_YELLOW;
				//fontKind = FONT_KIND_BIG;
			}else{ 
				// 文字色とサイズ
				color = FONT_PAL_WHITE;
				//fontKind = FONT_KIND_MIDDLE;
			}
		}
		
		// 名称を表示
		StockFontBuffer( pActBc[ i ]->x - GetStrWidth( pActBc[ i ]->name, fontKind ) / 2, pActBc[ i ]->y - 24, 
							FONT_PRIO_BACK, fontKind, color, pActBc[ i ]->name, 0 );
#if 0
#ifdef _DEBUG
		char str[ 256 ];
		sprintf( str, "%X", ( ( BC_YOBI * )pActBc[ i ]->pYobi )->myId );    //MLHIDE
		// 名称を表示
		StockFontBuffer( pActBc[ i ]->x - GetStrWidth( str, FONT_KIND_BIG ) / 2, pActBc[ i ]->y - 40, 
							FONT_PRIO_BACK, FONT_KIND_BIG, FONT_PAL_YELLOW, str, 0 );
							
		sprintf( str, "%d", pActBc[ i ]->hitDispNo );                       //MLHIDE
		// 名称を表示
		StockFontBuffer( pActBc[ i ]->x - GetStrWidth( str, FONT_KIND_BIG ) / 2, pActBc[ i ]->y - 56, 
							FONT_PRIO_BACK, FONT_KIND_BIG, FONT_PAL_YELLOW, str, 0 );
#endif
#endif
	}	
}

#ifdef PUK3_NEWSKILL_LVDOWN

void SetBattleMap()
{
	// 演出画像の作成
	// バッファ初期化
	DispBuffer.DispCnt = 0;
	FontCnt = 0;
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();
	
	//StockTaskDispBuffer();	// タスク表示データをバッファに溜める
	
	initCharPartsPrio();		// キャラ??布ツの优先顺位决定处理の初期化

	// 背景
#ifdef PUK3_BATTLEMAP				
	drawMap_Sugi(FALSE);
#else
	drawMap3();				//	マップ表示
#endif

	StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, BattleMapNo, 0 );

	// グリッド枠
	//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18028, 0 );
	
	// グリッド（灰色）
	//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18009, 0 );
	// メッシュ（セピア暗）
	//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18013, 0 );

	SortDispBuffer(); 	// 表示バッファソート
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	
	// 色变え制限を一时的にＯＦＦ
	LimiteLoadBmpFlag = FALSE;
	// ＢＭＰをバックサーフェスにセット
	PutBmp();	
	// バックサーフェスからバトルサーフェスへコピー
	lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	// 色变え制限をＯＮ
	LimiteLoadBmpFlag = TRUE;
	// バッファ初期化
	DispBuffer.DispCnt = 0;
	FontCnt = 0;
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();

	BattleMapNoBak = BattleMapNo;
}

#endif
/* バトルプロセス处理 ********************************************************************/
void BattleProc( void )
{
	static int i = 0;
	int no;
#ifdef PUK3_BATTLE_CMDRECV
	static int cmdrecvCnt = 0;
#endif
#ifdef PUK3_BATTLE_BG
	#ifdef _DEBUG
		BOOL ENreadFlag = FALSE;
	#endif
#endif
	
#ifdef PUK3_NEWSKILL_LVDOWN
	// 背景变更のチェック
	if ( BATTLE_PROC_RECV_BC_DATA <= SubProcNo &&
		 SubProcNo <= BATTLE_PROC_MOVIE ){
		// 背景が变わっていたら
		if ( BattleMapNo != BattleMapNoBak ) SetBattleMap();
	}
#endif
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( SubProcNo );
#endif
	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
	
		case BATTLE_PROC_INIT: // 初期化 ***************************************/
		
#ifdef WIN_SIZE_DEF
			//根据窗口分辨率计算战斗站位坐标
			for (i = 0; i < BC_MAX_EX; i++) {
				BcPos[i].startX = DEF_APPSIZEX_SCALE_RINT(BcPosTmp[i].startX);
				BcPos[i].startY = DEF_APPSIZEY_SCALE_RINT(BcPosTmp[i].startY);

				BcPos[i].defX = DEF_APPSIZEX_SCALE_RINT(BcPosTmp[i].defX);
				BcPos[i].defY = DEF_APPSIZEY_SCALE_RINT(BcPosTmp[i].defY);
			}
#endif
			// エンカウントフラグ初期化
			EncountFlag = FALSE;
#ifndef PUK2
			// 战闘中は昼パレット
			PaletteChange( 0, 0 );
#endif
#ifdef PUK2
	#ifdef _DEBUG
			// 离线模式の时
			if( offlineFlag == TRUE ){
				// 战闘中は昼パレット
				PaletteChange( 0, 0 );

				// 自分のバージョン设定
				switch(giInstallVersion){
		#ifdef PUK3_UPGRADE
				case 4:	PackageVer = PV_PUK3;		break;
		#endif
				case 3:	PackageVer = PV_PUK2;		break;
				case 2:	PackageVer = PV_FIRST_VER2;	break;
				case 1:	PackageVer = PV_FIRST_VER2;	break;
				case 0:	PackageVer = PV_NORMAL;		break;
				}

				battlePlayLine = 0;
		#ifdef PUK3_BATTLE_BG
				BattleMapNo = Rnd( 22130, 22149 );
				SugiMapStat.MapVer = 0;
				{
					FILE *fp;
					char s[sizeof(BcData)+2];
					int i, line;

					fp = fopen( "battlerecv.txt", "rt" );                            //MLHIDE
					if (fp){
						line = battlePlayLine;
						for(i=0;i<line;i++) fgets( s, sizeof(s), fp );
						for(;;){
							fgets( s, sizeof(s), fp );
							sjisStringToEucString(s);
							i = strlen(s);
							line++;
							// もうデータが无いとき
							if ( feof(fp) || s[0] == '@' ) break;

							if ( s[0] == 'B' && s[1] == ' ' ){
								if ( s[2] == 'E' ){
									efficacydisp = EFFICACYDISP_NOMAL|EFFICACYDISP_DUEL|EFFICACYDISP_BOSS;
	#ifdef PUK3_PROC_BATTLE_ELM
									elmdispalway = TRUE;
	#endif
								}else
								if ( s[2] == 'I' ){
									sscanf( s, "B I %d", &i );                                   //MLHIDE
									BattleMyNo = i;
								}else{
									battlePlayLine = line - 1;
									break;
								}
							}
							if ( s[0] == 'E' && s[1] == 'N' ){
								char str[2][256];
								int result, field;

								sscanf( s, "EN %s %s", str[0], str[1] );                      //MLHIDE

								result = nrproto_demkstr_int( str[0] );
								field = nrproto_demkstr_int( str[1] );

								// エンカウントの种类　０：失败　１：通常　２：ＤＵＥＬ　３：観战　４：固定敌　５：ボス　６：ラスボス
								EncountFlag = TRUE;
								EncountStatus = result;

								// デュエルの时
								if( result == 2 ) DuelFlag = TRUE;
								else DuelFlag = FALSE;

								// 観战
								if( result == 3 ) vsLookFlag = 1;
								else vsLookFlag = 0;

								// デュエルか固定敌の时
								if( result ==2 || result == 4 || result == 5 || result == 6 ) eventEnemyFlag = 1;
								else eventEnemyFlag = 0;

								BattleMapNo = field;	// 战闘マップ番号

								if ( 238600 <= BattleMapNo && BattleMapNo < 238700 ){
									SugiMapStat.MapVer = 1;
								}else{
									SugiMapStat.MapVer = 0;
								}

								battlePlayLine = line - 1;
								ENreadFlag = TRUE;
								break;
							}
						}
						fclose(fp);
					}
				}
		#endif
			}
	#endif
			// 属性表示の设定
	#ifdef PUK3_PROC_BATTLE_ELM
			if ( !elmdispalway )
	#endif
			{
				int l;
				for(l=0;l<CHAR_TITLE_MAX;l++){
					if ( sortCharTitleInfo[l].titleid == 1101 ) break;
//					if ( !strcmp( sortCharTitleInfo[l].title, "魔界風水老" ) ) break;
				}
				efficacydisp = 0;
				if (l<CHAR_TITLE_MAX){
					efficacydisp = EFFICACYDISP_NOMAL|EFFICACYDISP_BOSS;
				}
	#ifdef PUK2_TITILEELEMENTEFFECT_2
		#ifdef _DEBUG
				if ( efficacydisp_2 & 0x80 ) efficacydisp = efficacydisp_2;
		#endif
	#endif
			}
#endif
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			// 雨とかを降らせきる
			if( (mapEffectRainLevel != 0) || (mapEffectSnowLevel != 0) ){
				mapEffectProc2( 160 );		// マップエフェクト（雨??雪等）
			}
#endif

			// アクション抹杀
			DeathAllAction();
			// バトル初期化
			InitBattle();
			// 不意打ちウィンド初期化
			pActSurprisalWnd = NULL;
			// 观战结束ウィンドウ初期化
			pActAudienceExitWnd = NULL;
			// プロデュース初期化
			ProduceInitFlag = TRUE;
			// 战闘ウィンドウアクションポインタ初期化
			pActBattleWnd = NULL;
			// 战闘结果ウィンドウフラグ初期化
			battleResultMsg.useFlag = 3;
			// 入力フォーカス取得
			GetKeyInputFocus( &MyChatBuffer );
			
#ifdef PUK3_NEWSKILL_LVDOWN
	#ifdef _DEBUG
		#ifdef PUK3_BATTLE_BG
			if( offlineFlag == TRUE && ENreadFlag == FALSE ){
				BattleMapNo = Rnd( 22130, 22149 );
			}
		#else
			if( offlineFlag == TRUE ) BattleMapNo = Rnd( 22130, 22149 );
			//BattleMapNo = 18032;
		#endif
	#endif
			//{
			//	int static BattleMapNo2 = 22130;
			//	BattleMapNo = BattleMapNo2++;
			//	if( BattleMapNo2 > 22149 ) BattleMapNo2 = 22130;
			//}
			
			//BattleMapNo = Rnd( 22130, 22149 );
			// 背景の设定
			SetBattleMap();
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_PRODUCE;

			ChatProc();				// チャット处理
#else
				// 演出画像の作成
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
				
				//StockTaskDispBuffer();	// タスク表示データをバッファに溜める
				
				initCharPartsPrio();		// キャラ??布ツの优先顺位决定处理の初期化

				// 背景
#ifdef PUK3_BATTLEMAP				
				drawMap_Sugi(FALSE);
#else
				drawMap3();				//	マップ表示
#endif

#ifdef _DEBUG
		#ifdef PUK3_BATTLE_BG
			if( offlineFlag == TRUE && ENreadFlag == FALSE ){
				BattleMapNo = Rnd( 22130, 22149 );
			}
		#else
				if( offlineFlag == TRUE ) BattleMapNo = Rnd( 22130, 22149 );
				//BattleMapNo = 18032;
		#endif
#endif
				//{
				//	int static BattleMapNo2 = 22130;
				//	BattleMapNo = BattleMapNo2++;
				//	if( BattleMapNo2 > 22149 ) BattleMapNo2 = 22130;
				//}
				
				//BattleMapNo = Rnd( 22130, 22149 );
				StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, BattleMapNo, 0 );
				
				// グリッド枠
				//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18028, 0 );
				
				// グリッド（灰色）
				//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18009, 0 );
				// メッシュ（セピア暗）
				//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18013, 0 );
				
				
				//menuProc();				// メニュー处理
				// 一行インフォ栏涂りつぶし（黒色）
				//StockBoxDispBuffer( 0, 456, 640, 480, DISP_PRIO_MENU, 0, 1 );
				//KeyBoardProc();			// キーボード处理
				//ChatProc();				// チャット处理
				//ImeProc();				// ＩＭＥ关连处理
				
				ChatProc();				// チャット处理
				SortDispBuffer(); 	// 表示バッファソート
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
	#ifdef PUK2
				// 色变え制限を一时的にＯＦＦ
				LimiteLoadBmpFlag = FALSE;
	#endif
				// ＢＭＰをバックサーフェスにセット
				PutBmp();	
				// バックサーフェスからバトルサーフェスへコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
				// 色变え制限をＯＮ
				LimiteLoadBmpFlag = TRUE;
	#endif
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
				// バックバッファー描画方法变更
				BackBufferDrawType = DRAW_BACK_PRODUCE; 
#endif

			// エンカウントフラグ初期化
			//EncountFlag = 2;

			// エンカウントフラグ　０：失败　１：通常　２：ＤＵＥＬ　３：観战　４：固定敌　５：ボス　６：ラスボス
			// 新マップ？
			if( SugiMapStat.MapVer ==1 ){
				// 通常战闘ＢＧＭ再生
#ifdef PUK3
				//かなりやっつけなんですが
				//こうならざるを得ませんでした
				//かんべんしてください（杉
	#ifdef PUK3_NEWBATTLEBGM
				if ( 238607 <= BattleMapNo && BattleMapNo <= 238618 ){
	#else
				if(BattleMapNo==238610 ||
					BattleMapNo==238611 ||
					BattleMapNo==238613 ||
					BattleMapNo==238618
				){
	#endif
					switch(EncountStatus){
						case 1:	//通常
						case 4: //固定敌
							play_bgm( BGM_PUK3_BATTLE1 );
							break;
						case 2: //ＤＵＥＬ
						case 3: //観战
							play_bgm( BGM_BATTLE_DUEL );
							break;
						case 5: //ボス
							play_bgm( BGM_PUK3_BATTLE2 );
							break;
						case 6: //ラスボス
							play_bgm( BGM_PUK2_BATTLE02 );
							break;
					}
				}else{
					switch(EncountStatus){
						case 1:	//通常
						case 4: //固定敌
							play_bgm( BGM_PUK2_BATTLE01 );
							break;
						case 2: //ＤＵＥＬ
						case 3: //観战
							play_bgm( BGM_BATTLE_DUEL );
							break;
						case 5: //ボス
							play_bgm( BGM_PUK2_BATTLE02 );
							break;
						case 6: //ラスボス
							play_bgm( BGM_PUK2_BATTLE02 );
							break;
					}
				}
#else
				switch(EncountStatus){
					case 1:	//通常
					case 4: //固定敌
						play_bgm( BGM_PUK2_BATTLE01 );
						break;
					case 2: //ＤＵＥＬ
					case 3: //観战
						play_bgm( BGM_BATTLE_DUEL );
						break;
					case 5: //ボス
						play_bgm( BGM_PUK2_BATTLE02 );
						break;
					case 6: //ラスボス
						play_bgm( BGM_PUK2_BATTLE02 );
						break;
				}
#endif
			}else{
				//旧マップ
				// 通常战闘ＢＧＭ再生
				if( EncountStatus == 1 || EncountStatus == 4 ) play_bgm( BGM_BATTLE );
				else if( EncountStatus == 2 || EncountStatus == 3 ) play_bgm( BGM_BATTLE_DUEL );
				else if( EncountStatus == 5 ) play_bgm( BGM_BATTLE_BOSS );
				else if( EncountStatus == 6 ) play_bgm( BGM_BATTLE_LAST_BOSS );
			}
			
			// ＤＵＥＬＮＰＣ专用マップＩＤチェック
			if( mapNo >= 1457 && mapNo <= 1480 ) play_bgm( BGM_BATTLE_DUEL );

#ifdef _DEBUG			
		#ifdef PUK3_BATTLE_BG
			if( offlineFlag == TRUE && ENreadFlag == FALSE ){
				play_bgm( BGM_BATTLE_DUEL );
			}
		#else
			// 离线模式の时
			if( offlineFlag == TRUE )
			{
				play_bgm( BGM_BATTLE_DUEL );
			}
		#endif
#endif
#if 0
			//固定敌かデュエルなら
			if( DuelFlag == TRUE || eventEnemyFlag == 1 || vsLookFlag == 1 ){
				//ボス战闘ＢＧＭ再生
				play_bgm( BGM_BATTLE_BOSS );
			}else{
				//通常战闘ＢＧＭ再生
				play_bgm( BGM_BATTLE );
			}
#endif
			
			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
			
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
			
			// 演出中
#ifdef PUK2
			DrawProduce( PRODUCE_UNERI_BRAKE, 8, 2.0f );
#else
			DrawProduce( PRODUCE_UNERI_BRAKE );
#endif
			//DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE );
			//DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE );
				
			// 点灭を无くす为
			menuProc();				// メニュー处理
			ImeProc();				// ＩＭＥ关连处理
			// フィールドメニュー处理
			fieldProc2();
			
			// メニューフラグＯＮ 
			//battleMenuFlag2 = TRUE;
			
			// パラメータセーブ
			//saveUserSetting();
			
			
			// 次ぎのプロセスへ
			//SubProcNo = BATTLE_PROC_RECV_BC_DATA;
			SubProcNo++;
			
			break;
			
		case BATTLE_PROC_IN_PRODUCE: // バトルイン演出 *************************************/
			
			// 演出中
#ifdef PUK2
			if( DrawProduce( PRODUCE_UNERI_BRAKE, 8, 2.0f ) == TRUE ){
#else
			if( DrawProduce( PRODUCE_UNERI_BRAKE ) == TRUE ){
#endif
			//if( DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE ) == TRUE ){
			//if( DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE ) == TRUE ){
			//if( DrawProduce( PRODUCE_HAGARE_OCHI_IN ) == TRUE ){
			
#ifdef PUK3_NEWSKILL_LVDOWN
				// 背景の设定
				SetBattleMap();
				// バックバッファー描画方法变更
				BackBufferDrawType = DRAW_BACK_BATTLE; 
#else
				// 演出画像の作成
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
				
				//StockTaskDispBuffer();	// タスク表示データをバッファに溜める
				
				initCharPartsPrio();		// キャラ??布ツの优先顺位决定处理の初期化
				// 背景
#ifdef PUK3_BATTLEMAP				
				drawMap_Sugi(FALSE);
#else
				drawMap3();				//	マップ表示
#endif
				
				// 背景
				// バトルマップ番号から読み込み
				StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, BattleMapNo, 0 );
				// グリッド枠
				//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18028, 0 );
				// グリッド（灰色）
				//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18009, 0 );
				// メッシュ（セピア暗）
				//StockDispBuffer( 320, 240, DISP_PRIO_B_EFFECT, 18013, 0 );
				
				
				SortDispBuffer(); 	// 表示バッファソート
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
	#ifdef PUK2
				// 色变え制限を一时的にＯＦＦ
				LimiteLoadBmpFlag = FALSE;
	#endif
				// ＢＭＰをバックサーフェスにセット
				PutBmp();
				// バックサーフェスからバトルサーフェスへコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
				// 色变え制限をＯＮ
				LimiteLoadBmpFlag = TRUE;
	#endif
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
				
#endif
				// 现在の时间を记忆
				NowTime = GetTickCount();
#ifdef PUK2_FPS
				NowDrawTime = NowTime;
#endif
				
				// バックバッファー描画方法变更
				BackBufferDrawType = DRAW_BACK_BATTLE; 
				
				// キーボード处理
				KeyBoardProc();			
				/* チャット处理 */
				ChatProc();
				// メニュー处理
				menuProc();
				// ＩＭＥ关连处理
				ImeProc();
				// キーボードカーソル点灭处理
				FlashKeyboardCursor();
				// フィールドメニュー处理
				fieldProc2();
				// パレット处理
				//PaletteProc();
#ifdef PUK2
				initMenu();			// メニュー关连の初期化
#endif
				SubProcNo++;
			}else{
				menuProc();	// メニュー处理
				ImeProc();	// ＩＭＥ关连处理
				// フィールドメニュー处理
				fieldProc2();
			}
			
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			
		
			break;
			
		case BATTLE_PROC_RECV_BC_DATA: // ＢＣデータ受信待ち ********************************************/
			
			// キーボード处理
			KeyBoardProc();		
			// バトル时のアクション走らせる
			//BattleRunAction();
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

			/* チャット处理 */
			ChatProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();
			
			// フィールド关连处理（グループ设定ショートカットキーのためだけ）
			fieldProc();			
			
			// 参战の时
			if( BattleBpFlag & BP_FLAG_JOIN ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"参戦待ち。");                                  //MLHIDE
			}
			
			// ＢＣデータが来ていたら
			if( BcDataGetBufNo != BcDataSetBufNo ){
				// ＢＣデータコピー
				strcpy( BcData, BcDataBak[ BcDataGetBufNo ] );
				// 次のバッファへ
				BcDataGetBufNo++;
				// リミットチェック
				if( BcDataGetBufNo >= B_BUF_SIZE ) BcDataGetBufNo = 0;
				// 取り出しポイント进める
				//BcDataGetBufNo = BcDataSetBufNo;
				// バトルキャラクターデータの読み込み
				ReadBcData();
				// 强制終了の时
				if( BattleBcFieldFlag & BC_FIELD_FLAG_END ){
					// 战闘終了演出へ
					SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
					break;
				}
				// 不意打ち判定处理
				Surprisal();
				
#ifdef PUK2
				// 自分のＩＤが变わってたら再チェック
				if ( BattleMyNo != BattleMyNoBak ){
					// ＢＰから自分のＩＤを取り出す
					BattleMyNo = BattleMyNoBak;
					for(i=0;i<BC_MAX;i++){
#ifdef PUK3_PACTBC_CHECKRANGE
						CheckIdRange( i );
#endif
						if ( !pActBc[i] ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
						CheckAction( pActBc[i] );
#endif
						// アルバム登録チェック
						SetActAlbumRegist(pActBc[i]);
					}
				}else{
					// ＢＰから自分のＩＤを取り出す
					BattleMyNo = BattleMyNoBak;
				}
#else
				// ＢＰから自分のＩＤを取り出す
				BattleMyNo = BattleMyNoBak;
#endif
#ifdef PUK3_RIDE_BATTLE
				// 现在のペットの番号を更新
				BattleNowPetNo = CheckBattlePet();
				// ペットのデータ更新
				SetBattlePetdata();
#endif
				
				// 観战の时、ウィンドウの表示
				if( BattleMyNo >= BC_MAX ){
#ifdef PUK2_NEW_MENU
					if ( WindowFlag[MENU_WINDOW_WATCH].wininfo == NULL ){
						openMenuWindow( MENU_WINDOW_WATCH, OPENMENUWINDOW_HIT, 0 );
						// ウィンドウ开く音
						play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					}
#else
					// ウィンドウ无かったら
					if( pActAudienceExitWnd == NULL ){
						// ウィンドウ表示タスク作成
						//pActAudienceExitWnd = makeWindowDisp( 444, 4, 192, 96, 0 );
						pActAudienceExitWnd = makeWindowDisp( 444 + 64, 4, 128, 96, 1 );
						// ウィンドウ开く音
						play_se( SE_NO_OPEN_WINDOW, 320, 240 );
					}
#endif
				}
				
				// 次ぎのプロセスへ
				SubProcNo = BATTLE_PROC_CHAR_APPEAR;
				
			}
			
			// 参战の时
			if( BattleBpFlag & BP_FLAG_JOIN ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"等待回合结束。");                                //MLHIDE
			}
			// 観战の时
			//if( EncountStatus == 3 ){
				// 一行インフォ
			//	strcpy( OneLineInfoStr,"等待回合结束。２");
			//}
			
#ifdef _DEBUG			
			// 离线模式の时
			if( offlineFlag == TRUE ){
				strcpy(BcData,
//				"BC|0|0|ＪＳＳ零号機||18859|49|140|140|0|1|山賊志願のキンゾ|ス布キンゾ|18837|4E|2D0|2D0|1|5|ドラピス||1880A|4E|19C|2E4|2|6|スー布カッくん||187E3|4E|2CF|2CF|3|A|暁||186D2|4A|128|128|4|B|いざよい|月兎|186C0|44|F2|F5|4|C|らっく|精霊族：小判|18718|3A|BF|BF|4|D|wako|いざよいの妹|18781|48|11F|11F|4|E|りんぐ|聖獣族?白虎|1874A|3B|0|137|6|F|バオ||18801|41|2D9|2D9|0|10|きゃべつー||187BB|41|277|27F|0|11|ブラキトス||18802|36|9D|2A2|0|12|◎レッドスター◎||18801|3A|38|299|0|13|ライトグリーン||187BF|3B|26E|26E|0"
//  			"C|0|0|マッスル||18898|1|BB8|BB8|4|A|Mr.Teki||1889B|1|6D|6D|1|B|Mr.Teki||1889C|1|97|97|1|C|Mr.Teki||1889E|1|A0|A0|1|D|Mr.Teki||18898|1|A8|A8|1|E|Mr.Teki||1889A|1|BF|BF|1|F|Mr.Teki||18894|1|C0|C0|1|10|Mr.Teki||18896|1|C1|C1|1|11|Mr.Teki||18899|1|B7|B7|1|12|Mr.Teki||1889B|1|9E|9E|1|"
//				"C|0|0|マッスル||18898|1|BB8|BB8|4|1|マッスル||18898|1|BB8|BB8|1|2|マッスル||18898|1|BB8|BB8|1|3|マッスル||18898|1|BB8|BB8|1|4|マッスル||18898|1|BB8|BB8|1|5|マッスル||18898|1|BB8|BB8|1|6|マッスル||18898|1|BB8|BB8|1|7|マッスル||18898|1|BB8|BB8|1|8|マッスル||18898|1|BB8|BB8|1|9|マッスル||18898|1|BB8|BB8|1|A|Mr.Teki||1889B|1|6D|6D|1|B|Mr.Teki||1889C|1|97|97|1|C|Mr.Teki||1889E|1|A0|A0|1|D|Mr.Teki||18898|1|A8|A8|1|E|Mr.Teki||1889A|1|BF|BF|1|F|Mr.Teki||18894|1|C0|C0|1|10|Mr.Teki||18896|1|C1|C1|1|11|Mr.Teki||18899|1|B7|B7|1|12|Mr.Teki||1889B|1|9E|9E|1|"
// 殴って戾って殴る
//"C|0|0|マッスル||186D3|1|2FC|398|5|1|うーろんちゃ||186D4|1|398|398|2|うーろんちゃ||186D4|1|398|398|3|うーろんちゃ||186D4|1|398|398|4|うーろんちゃ||186D4|1|398|398|5|うーろんちゃ||186D4|1|398|398|6|うーろんちゃ||186D4|1|398|398|7|うーろんちゃ||186D4|1|398|398|8|うーろんちゃ||186D4|1|398|398|9|うーろんちゃ||186D4|1|398|398|5|A|Mr.Teki||18AEC|1|7D|7D|1|B|Mr.Teki||18AED|1|AD|AD|1|C|Mr.Teki||18B00|1|80|80|1|D|Mr.Teki||18C18|1|8F|8F|1|E|Mr.Teki||18C7E|1|74|74|1|"
//"C|0|0|マッスル||186D4|1|18B|640|5|1|マッスル||186D4|1|18B|640|5|2|マッスル||186D4|1|18B|640|5|3|マッスル||186D4|1|18B|640|5|4|マッスル||186D4|1|18B|640|5|5|マッスル||186D4|1|18B|640|5|6|マッスル||186D4|1|18B|640|5|7|マッスル||186D4|1|18B|640|5|8|マッスル||186D4|1|18B|640|5|9|マッスル||186D4|1|18B|640|5|A|ざく||18C7C|5|B8|B8|1|B|ざく||18B03|5|1BF|1BF|1|C|ざく||18B03|5|1AE|1AE|1|D|ぐふ||18C1D|5|1C0|1C0|1|E|ぐふ||18B55|5|191|191|1|F|じおんぐ||18B52|5|3E8|3E8|1|10|どむ||18C1A|5|1A5|1A5|1|11|どむ||18B55|5|1A6|1A6|1|12|げるぐぐ||18C19|5|1B3|1B3|1|13|げるぐぐ||18B55|5|1E5|1E5|1|"
//"C|0|0|マッスル||186D4|1|18B|640|100|6|1|マッスル||186D4|1|18B|640|100|4|2|マッスル||186D4|1|18B|640|100|5|3|マッスル||186D4|1|18B|640|100|5|4|マッスル||186D4|1|18B|640|100|5|5|マッスル||186D4|1|18B|640|100|5|6|マッスル||186D4|1|18B|640|100|5|7|マッスル||186D4|1|18B|640|100|5|8|マッスル||186D4|1|18B|640|100|5|9|マッスル||186D4|1|18B|640|100|5|A|マッスル||186D4|5|18B|640|100|5|B|マッスル||186D4|1|18B|640|100|5|C|マッスル||186D4|1|18B|640|100|5|D|マッスル||186D4|1|18B|640|100|5|E|マッスル||186D4|1|18B|640|100|5|F|マッスル||186D4|5|18B|640|100|5|10|マッスル||186D4|1|18B|640|100|5|11|マッスル||186D4|1|18B|640|100|5|12|マッスル||186D4|1|18B|640|100|5|13|マッスル||186D4|1|18B|640|100|5|"

//"C|0|0|マッスル||186D4|1|18B|640|100|5|1|マッスル||186D4|1|18B|640|100|5|2|マッスル||186D4|1|18B|640|100|5|3|マッスル||186D4|1|18B|640|100|5|4|マッスル||186D4|1|18B|640|100|5|6|マッスル||186D4|1|18B|640|100|5|7|マッスル||186D4|1|18B|640|100|5|8|マッスル||186D4|1|18B|640|100|5|9|マッスル||186D4|1|18B|640|100|5|A|マッスル||186D4|5|18B|640|100|5|B|マッスル||186D4|1|18B|640|100|5|C|マッスル||186D4|1|18B|640|100|5|D|マッスル||186D4|1|18B|640|100|5|E|マッスル||186D4|1|18B|640|100|5|F|マッスル||186D4|5|18B|640|100|5|10|マッスル||186D4|1|18B|640|100|5|11|マッスル||186D4|1|18B|640|100|5|12|マッスル||186D4|1|18B|640|100|5|13|マッスル||186D4|1|18B|640|100|5|"
				// 逃げて終わるバグ
				//"C|0|0|マッスル||1889F|1|5C7|A28|4|B|ざく||1889D|5|A6|A6|0|C|ざく||18894|5|86|86|0|E|ぐふ||18898|5|6B|6B|0|10|どむ||1889E|5|78|78|0|11|どむ||18896|5|C5|C5|0|"
				// 行动終わらんバグ
				//"C|0|0|マッスル||1889F|1|706|708|4|A|ビグザム||1889A|5|37|A7|0|B|ビグザム||18898|5|0|64|2|C|ビグザム||1889A|5|8D|8D|1|D|ビグザム||1889D|5|0|A0|2|"
				//"C|0|0|wwwwwwwwwwwwwwww||18AF7|78|8C3|90E|0|4|A|?????????||18C1E|A|135|135|0|1|"
				//"C|0|0|たけのこ||186DB|5|50|9C|3E8|4|5|レディーキュール||18A8D|4|1C|52|3E8|0|A|プディング||18C7F|3|0|68|0|2|B|スライム||18C7C|2|0|53|0|2|C|ウーズ||18C7D|4|2|93|0|0|"
				//"C|0|0|たけのこ||186DB|5|50|9C|3E8|4|5|レディーキュール||18A8D|4|1C|52|3E8|0|A|ブランディッシュ||18CEA|2|24|24|0|1|"

//"C|0|5|?????????|186D5|B|F6|32E|3AC|4|A|??????????蝪???|18A8D|14|166|166|0|0|B|???????????||18A88|13|190|190|0|0|C|??????????||18A89|15|1B2|1B2|0|0|"
//*************************
// デフォルトＢＣ
//*************************
"C|0|0|マッスル0|186D4|1|18B|640|100|100|1|1|マッスル1|186D4|1|18B|640|100|100|5|2|マッスル2|186D4|1|18B|640|100|100|5|3|マッスル3|186D4|1|18B|640|100|100|5|4|マッスル4|186D4|1|18B|640|100|100|5|5|マッスル5|186D4|1|18B|640|100|100|5|6|マッスル6|186D4|1|18B|640|100|100|5|7|マッスル7|186D4|1|18B|640|100|100|5|8|マッスル8|186D4|1|18B|640|100|100|5|9|マッスル9|186D4|1|18B|640|100|100|5|A|マッスル10|186D4|1|18B|640|100|100|1|B|マッスル11|186D4|1|18B|640|100|100|1|C|マッスル12|186D4|1|18B|640|100|100|405|D|マッスル13|186D4|1|18B|640|100|100|5|E|マッスル14|186D4|1|18B|640|100|100|5|F|マッスル15|186D4|5|18B|640|100|100|5|10|マッスル16|186D4|1|18B|640|100|100|5|11|マッスル17|186D4|1|18B|640|100|100|5|12|マッスル18|186D4|1|18B|640|100|100|5|13|マッスル19|186D4|1|18B|640|100|100|5|" //MLHIDE

//"C|0|0|マッスル0|186D4|1|18B|640|100|100|1|1|マッスル1|186D4|1|18B|640|100|100|5|2|マッスル2|186D4|1|18B|640|100|100|5|3|マッスル3|186D4|1|18B|640|100|100|5|4|マッスル4|186D4|1|18B|640|100|100|5|6|マッスル6|186D4|1|18B|640|100|100|5|7|マッスル7|186D4|1|18B|640|100|100|5|8|マッスル8|186D4|1|18B|640|100|100|5|9|マッスル9|186D4|1|18B|640|100|100|5|A|マッスル10|186D4|1|18B|640|100|100|1|B|マッスル11|186D4|1|18B|640|100|100|1|C|マッスル12|186D4|1|18B|640|100|100|405|D|マッスル13|186D4|1|18B|640|100|100|5|E|マッスル14|186D4|1|18B|640|100|100|5|10|マッスル16|186D4|1|18B|640|100|100|5|11|マッスル17|186D4|1|18B|640|100|100|5|12|マッスル18|186D4|1|18B|640|100|100|5|13|マッスル19|186D4|1|18B|640|100|100|5|"


// 左サイドは全部的バージョン
//"C|0|0|マッスル0|186D4|1|18B|640|100|100|1|1|マッスル1|186D4|1|18B|640|100|100|5|2|マッスル2|186D4|1|18B|640|100|100|5|3|マッスル3|186D4|1|18B|640|100|100|5|4|マッスル4|186D4|1|18B|640|100|100|5|5|マッスル5|186D4|1|18B|640|100|100|1|6|マッスル6|186D4|1|18B|640|100|100|5|7|マッスル7|186D4|1|18B|640|100|100|5|8|マッスル8|186D4|1|18B|640|100|100|5|9|マッスル9|186D4|1|18B|640|100|100|5|A|マッスル10|186D4|1|18B|640|100|100|1|B|マッスル11|186D4|1|18B|640|100|100|1|C|マッスル12|186D4|1|18B|640|100|100|1|D|マッスル13|186D4|1|18B|640|100|100|1|E|マッスル14|186D4|1|18B|640|100|100|1|F|マッスル15|186D4|5|18B|640|100|100|1|10|マッスル16|186D4|1|18B|640|100|100|1|11|マッスル17|186D4|1|18B|640|100|100|1|12|マッスル18|186D4|1|18B|640|100|100|1|13|マッスル19|186D4|1|18B|640|100|100|1|"

//"C|0|1|マッスル1|186D4|1|18B|640|100|100|5|2|マッスル2|186D4|1|18B|640|100|100|5|3|マッスル3|186D4|1|18B|640|100|100|5|4|マッスル4|186D4|1|18B|640|100|100|5|5|マッスル5|186D4|1|18B|640|100|100|1|6|マッスル6|186D4|1|18B|640|100|100|5|7|マッスル7|186D4|1|18B|640|100|100|5|8|マッスル8|186D4|1|18B|640|100|100|5|9|マッスル9|186D4|1|18B|640|100|100|5|A|マッスル10|186D4|1|18B|640|100|100|1|B|マッスル11|186D4|1|18B|640|100|100|1|C|マッスル12|186D4|1|18B|640|100|100|405|D|マッスル13|186D4|1|18B|640|100|100|5|E|マッスル14|186D4|1|18B|640|100|100|5|F|マッスル15|186D4|5|18B|640|100|100|5|10|マッスル16|186D4|1|18B|640|100|100|5|11|マッスル17|186D4|1|18B|640|100|100|5|12|マッスル18|186D4|1|18B|640|100|100|5|13|マッスル19|186D4|1|18B|640|100|100|5|"



// バグ再现 ***************************************************************************

//"C|0|5|天野聡子?UBR?|187A9|C|62|163|80|8A|404|1|天野美汐|1883A|10|5F|ED|208|23E|4|2|レイシャ|187C3|A|CC|CE|13B|173|404|3|久遠?|18813|F|F2|13E|C0|D2|404|9|ハート|18833|F|56|148|9A|AE|404|6|魅紗樹|18B51|10|0|17C|19B|1A3|A|A|熊殺しのオズナグ|18911|19|31A|53C|28F|28F|0|B|ケイブベア|18A92|14|30C|30C|1CE|1CE|0|C|ケイブベア|18A92|14|319|319|1F3|1F3|0|D|ケイブベア|18A92|14|306|306|1F4|1F4|0|E|ケイブベア|18A92|14|306|306|1D2|1D2|0|F|ケイブベア|18A92|14|31C|31C|1E9|1E9|0|10|ケイブベア|18A92|14|351|351|1E6|1E6|0|11|ケイブベア|18A92|14|2F3|2F3|1D2|1D2|0|12|ケイブベア|18A92|14|32A|32A|1FA|1FA|0|13|ケイブベア|18A92|14|313|313|1E2|1E2|0|"

// 合体攻击の后、ＩＤおらんエラー
//"C|0|0|Yuki|187B0|A|ED|FE|EF|163|5|1|☆ヒイロ☆|18726|A|106|124|D2|D2|5|2|しるふ〆|186A6|F|111|114|BB|ED|405|8|オセロまじーん|1872C|D|12C|14E|99|BC|5|5|プッチバット|18B78|A|EC|EE|11B|11B|9|6|プッチバット|18B78|A|D5|E7|10F|10F|9|3|インプ|18B51|7|E0|E0|DD|101|9|A|ゴブリン|18DA8|5|AA|AA|6C|6C|1|"

// ＤＵＥＬアルティメットＢＣ
//"C|0|0|マッスル0|186D4|1|18B|640|100|100|1|1|マッスル1|186D4|1|18B|640|100|100|5|A|マッスル10|186D4|1|18B|640|100|100|1|"
// １／１１日バグ
//"C|0|0|8@hyoiu||18806|64|D1|168|320|4|1|ijk||186AF|1|160|168|3E8|4|2|ぽん||187CF|3|100|168|3E8|4|3|じゅん||187FF|1|115|14C|3E8|4|4|悪おさる||186DE|1|166|668|3E8|4|A|マッスル||18E21|B|C4|168|348|4|B|リューム||186E1|1|168|168|3E8|4|C|かずっち||186BB|1|A0|14E|3E8|4|12|0||18816|1|1E|168|3AC|4|13|ショック||18803|1|4B|168|3C5|4|F|ゴースト||18B00|A|70|AF|3E8|8|10|ゴブリン||18DA8|A|D5|105|3E8|8|E|スケルトン||18AF6|1|16|3A|3E8|8|"

//走りっぱなし。
//"C|0|5|まーりまり||187E0|5|250|28A|3E8|4|1|pon||187CC|2|3B|3C|3E8|4|2|古代慎治||186D5|1|73|74|3E8|4|3|ショック||187BB|2|66|84|370|4|4|マッスル||186CD|2|167|168|348|4|0|リザードマン||18D44|1|3A|46|0|8|6|スケルトン||18AF6|1|28|36|0|8|7|スケルトン||18AF6|1|39|39|3E8|8|8|トレント||18C18|1|55|5A|0|8|9|リザードマン||18D44|A|EC|EC|0|8|A|ゴブリン||18DA8|1|24|52|3E8|0|B|リザードマン||18D44|1|2E|4A|3E8|0|C|リザードマン||18D44|1|46|46|3E8|0|D|トレント||18C18|1|3A|56|3E8|0|E|ゴブリン||18DA8|1|50|50|3E8|0|F|グレートバット||18B82|1|2|4A|3E8|0|11|リザードマン||18D44|1|D|4A|3E8|0|"

// カウンターおかしいバグ
//"C|0|0|マッスル||186CD|A|117|168|1FE|5|5|ゴブリン||18DA8|A|105|107|0|9|A|トレントキング||18C1E|1|6C|6C|0|1|"

//"C|0|0|マッスル||186CD|A|7E|140|370|4|5|ゴブリン||18DA8|A|B|107|3E8|8|A|アックスリザード||18D47|1|4A|4A|0|1|B|フレイムリザード||18D46|1|45|45|0|1|C|リザードキング||18D45|1|44|44|0|1|D|リザードマン||18D44|1|48|48|0|1|E|リザードキング||18D45|1|40|40|0|1|F|リザードマン||18D44|1|45|45|0|1|10|アックスリザード||18D47|1|4B|4B|0|1|"

// 反射死亡ばく
//"C|0|0|よろしき||187CE|7|DA|10E|190|4|A|ミミック||18CED|1|5|3A|3E8|0|"

// 自分に魔法反射した时
//"C|0|5|???????螟?|187E0|1|48|140|50|4|A|???????愠???||18DB3|1|60|60|3E8|0|"

// 魔法となえたら落ちた。
//"C|0|0|???????螟?|186CD|8|197|1CC|366|404|1|?????窩|187CE|3|166|168|320|404|7|roro||18838|14|140|140|334|4|3|???????????|187AC|4|F95|1072|3E8|404|4|ijk||1880A|14|123|140|3B6|404|2|??????|18DA8|2|59|63|3E8|8|A|????????????||18C88|1|10"

//"C|0|0|よろよろ|187CE|7|1FA|25F|7|9D|5|5|ワータイガー|18A88|A|9A|FB|96|D2|9|A|ヴァリアント|18E2E|1|58|58|46|46|1|B|ヘルナイト|18E2D|1|5D|5D|4B|4B|1|C|ブラッドナイト|18E2B|1|57|57|45|45|1|D|93f|18E2F|1|57|57|45|45|1|E|デュラハン|18E2A|1|5B|5B|45|45|1|F|バルバロッサ|18E2C|1|58|58|46|46|1|"
//"C|0|0|ザンギ|1871F|8|8F4|8F4|746|8F4|5|5|プッチバト|18B78|A|C1|C1|0|F0|9|A|ファントムソード|18CE2|1|3B|3B|5F|5F|1|B|ドラゴンエッジ|18CE1|1|42|42|52|52|1|C|ブランディッシュ|18CE0|1|3E|3E|59|59|1|D|レンジソード|18CE3|1|42|42|61|61|1|E|ファントムソード|18CE2|1|3D|3D|63|63|1|F|ドラゴンエッジ|18CE1|1|44|44|54|54|1|10|ブランディッシュ|18CE0|1|40|40|5B|5B|1|"


// 片侧回复の魔法反射 *****************************************************************************
//"C|0|0|あんきも|186E0|1|104|168|154|168|404|1|コンパネパンチ|186CE|12|146|168|136|168|604|2|roro|1880D|12|151|168|8C|168|404|A|かずっち|186CA|12|E8|168|168|168|4|10|ショック|187AC|12|1B|168|104|168|4|C|ザンギ|1871D|8|14C|14C|E8|14C|4|F|ヘルゴブリン|18DA9|1|2E|5F|3C|3C|8|B|火熊|18A93|1|69|69|D|49|8|11|プッチバト|18B78|A|C1|C1|A0|F0|8|"

// 亲方K倩渐啸?
//"C|0|5|こってり|186D4|8|3BA|44C|15E|17C|4|0|トレント|18C18|1|0|66|4F|4F|A|A|親分|18DC0|1|14|58|46|46|0|B|子分|18DBF|1|5A|5A|4A|4A|0|C|子分|18DBF|1|59|59|45|45|0|D|子分|18DBF|1|18|58|47|47|0|E|子分|18DBF|1|59|59|46|46|0|F|子分|18DBF|1|21|5B|46|46|0|10|子分|18DBF|2|26|6C|58|58|0|11|子分|18DBF|1|57|57|45|45|0|12|子分|18DBF|2|3B|68|54|54|0|13|子分|18DBF|2|6E|6E|53|53|0|"

//"C|0|5|こってりん|18742|1E|138|138|115|115|5|0|ネコマタ|18A8C|1|24|36|37|37|9|C|シャドウ|18C9A|1|39|39|43|43|1|E|トゥーンシェイド|18C9B|1|3D|3D|41|41|1|F|デコイ|18C9D|1|3F|3F|3D|3D|1|11|シェイド|18C9C|1|3E|3E|40|40|1|"


// チャット流れバグ
//"C|0|0|しず|187C9|10|27|141|1EE|248|404|1|コニー|187E8|16|CC|18B|8C|E6|404|2|モア|187B0|D|0|107|140|1AE|406|3|りな|187E0|17|3C|18C|D0|11E|404|4|華鱗|187F5|10|77|159|1A7|1E3|404|A|ケイブベア|18A92|14|310|310|20B|20B|0|B|ケイブベア|18A92|14|332|332|1E5|1E5|0|C|ケイブベア|18A92|14|6E|334|1D6|1D6|0|D|ケイブベア|18A92|14|326|326|1FA|1FA|0|E|ケイブベア|18A92|14|310|310|205|205|0|F|ケイブベア|18A92|14|4F|309|1E3|1E3|0|10|ケイブベア|18A92|14|307|307|1DE|1DE|0|11|熊殺しのオズナグ|18911|19|8|514|265|265|0|12|ケイブベア|18A92|14|262|304|1E0|1E0|0|13|ケイブベア|18A92|14|83|33E|1F1|1F1|0|"

// ＰＣが飞ばされて、ペットが逃げて、また戾ってくる
//"C|0|0|エルシィス|18730|A|B6|CC|C2|EA|4|1|den|186D4|C|B2|FD|C0|F5|4|2|りっきー|18716|5|C7|C7|5E|7C|404|8|ギル|18826|A|B3|EE|72|8E|4|4|ちゃる|187AE|5|92|92|93|C2|4|5|使い魔ガーゴイル|18B50|8|BF|C0|DF|DF|8|6|デンケロ|18AA6|A|FF|FF|E1|E1|8|3|インプ|18B51|9|EF|FF|10B|11D|8|9|プッチバット|18B78|5|93|A6|AF|BD|8|A|ケイブベア|18A92|14|2FC|2FC|1C4|1C4|0|B|ケイブベア|18A92|14|316|316|200|200|0|C|ケイブベア|18A92|14|319|319|1CF|1CF|0|D|ケイブベア|18A92|14|327|327|1EC|1EC|0|E|ケイブベア|18A92|14|307|307|1E0|1E0|0|F|熊殺しのオズナグ|18911|19|54D|54D|2B4|2B4|0|10|ケイブベア|18A92|14|30C|30C|1E3|1E3|0|11|ケイブベア|18A92|14|341|341|1FD|1FD|0|12|ケイブベア|18A92|14|302|302|1D2|1D2|0|13|ケイブベア|18A92|14|30B|30B|1CD|1CD|0|"

// 长老召唤
//"C|0|0|まじゅちゅち|187A4|64|1C9|2B1|2CE|45C|404|A|長老トレント|18C1D|23|123|8E9|7FE|7FE|0|D|ファーンウィドー|18BD2|18|1E0|1E0|1B3|1B3|0|E|ベノムビー|18BBF|18|47|190|1B4|1B4|0|"

// はとばす止まる
//"C|0|0|あんきも〆|1892B|69|32F0|32F0|9D6|9D6|405|1|名声６００ちぇく|186BA|64|2D0|2D0|87|87|405|2|aaa|1879C|14|AA|AA|20|20|5|7|1|18A88|1|5F|5F|28|28|9|A|ijk|186A2|23|4470|4470|2238|2238|405|B|はとバス|187CE|23|35|35|AF|AF|405|"


// ホテルの骸骨、护卫吸收
//"C|0|0|ケロ|186BB|1E|1F1|1F1|123|173|4|5|ケットシー|18A89|1E|1F3|1F3|E6|ED|8|A|ショックっク|186D5|64|9EC|9EC|4F6|4F6|4|F|朽ち果てた騎士|18AF9|19|370|370|22F|22F|8|"

// 属性反転するボスでＩＤおらんエラー
//"C|0|0|あんきもい√|18866|32|276|29B|65A|66E|4|1|BHM|187CD|34|363|378|149|1B6|4|2|かず|186F9|34|3B8|3E8|195|1A1|404|3|魚僧侶|186D6|32|285|2A0|5B0|640|404|4|まじゅちゅち|186A4|32|231|280|57D|591|4|5|プッチバット|18B78|32|295|2D9|35E|35E|8|6|ボム|18C92|28|19E|207|3FE|3FE|8|9|３割５分２厘|18DA8|2F|38B|38B|215|215|8|A|オカシラ|18DC0|3C|1B77|1D04|C92|C92|0|B|リベナント|18B03|28|23F|23F|351|351|0|C|リベナント|18B03|28|80|225|32B|32B|0|D|パイレイト|18DBE|2D|DD|368|2AF|2AF|0|E|パイレイト|18DBE|2D|2CD|2CD|265|265|0|F|パイレイト|18DBE|2D|336|336|2B3|2B3|0|10|カワンチャ|18AF7|28|1EA|263|2C9|2C9|0|11|カワンチャ|18AF7|28|1CD|247|2E9|2E9|0|12|リベナント|18B03|28|279|279|36B|36B|0|13|リベナント|18B03|28|162|22B|311|311|0|"
//"C|0|0|あんきもい√|18866|32|269|29B|5AC|66E|4|1|まじゅちゅち|186A4|64|242|280|4A1|591|4|2|jk|186B3|32|226|2B1|1BF|1FB|4|3|かず|186F9|34|383|3E8|189|1A1|404|4|BHM|187CD|34|24E|378|1B6|1B6|404|5|プッチバット|18B78|32|27|2D9|33B|35E|8|6|３割５分２厘|18DA8|2F|2BC|38B|206|215|8|7|ホーネット|18BC1|28|E3|26A|191|23C|8|A|オカシラ|18DC0|3C|18C2|1DDA|CB8|CB8|0|C|リベナント|18B03|28|91|24E|342|342|0|E|パイレイト|18DBE|2D|10|308|24E|24E|0|11|カワンチャ|18AF7|28|59|273|318|318|0|12|リベナント|18B03|28|57|27C|323|323|0|"

// 観战で突然落ち
//"C|0|0|百鬼丸|186B2|25|1C7|1C7|28|170|4|1|つとむ|186CD|29|16E|20E|175|425|4|2|ヨハン|186B0|2D|25A|25A|351|3CE|4|3|のりぽん|1880E|2C|2CF|2CF|40B|471|4|4|MECCO|187B0|25|131|1EE|22A|444|4|5|カマキリマル|18BC8|26|209|2CD|265|27A|8|6|ザクレロ|18BC8|1F|1A0|245|1E2|1F0|8|7|箱１号|18CEE|2C|18D|29E|3C7|3EA|8|8|コンクリート|18D00|25|0|256|1C7|1C7|A|9|壱角神|18AA9|1E|1DC|249|21A|21A|8|A|オカシラ|18DC0|3C|1A8E|1DB7|C80|C80|0|B|リベナント|18B03|28|D|22C|354|354|0|C|リベナント|18B03|28|222|222|341|341|0|D|パイレイト|18DBE|2D|31E|31E|296|296|0|E|パイレイト|18DBE|2D|33C|33C|280|280|0|F|パイレイト|18DBE|2D|1E1|30F|26E|26E|0|10|カワンチャ|18AF7|28|E6|2B2|319|319|0|11|カワンチャ|18AF7|28|253|253|2D5|2D5|0|13|リベナント|18B03|28|ED|223|335|335|0|"
// ACT_ACT
//"C|0|0|qqqqq|1879F|64|FB3|107C|8AC|A3C|404|A|イスカリオテ|18E2E|41|19C7|1CFB|9|11C4|0|"

// 暗杀で星が出バグ
//"C|0|0|a|186D4|3C|58C|6F4|212|5E6|405|1|天下一品|18800|5|263|334|78|78|405|A|バンドレロ|18DBC|6|B2|B2|82|82|1|F|バンドレロ|18DBC|6|BB|BB|90|90|1|"

//"C|0|0|クレリック|187B5|1E|CC|1F1|227|35B|405|B|リベナント|18B03|1F|1E3|1E3|2B0|2B0|1|"

// 召唤で落ちるバグ
//"C|0|0|まじゅちゅち|18708|3E|316|316|4ED|6FA|4|1|ぽにゃらら|1879E|3E|351|351|5D3|739|4|2|クレリック|18721|3C|440|440|4FB|609|4|3|剣士Lv60_Bランク|186EE|3E|2E5|39A|1DF|225|14|4|戦斧凍死|186AC|3C|335|33C|196|2F4|4|5|ホーネット|18BC1|3D|39F|3A1|225|2B1|8|6|パンダ|18A96|3C|3AD|530|358|402|8|7|クレイジーエッジ|18BCA|3C|3FB|3FB|3C1|3C1|8|8|ケルベロス|18AA6|3C|410|443|304|3CC|8|9|ポーラーベア|18A94|28|328|39D|236|24E|18|A|ナバ|18E2E|41|1316|1DB8|122D|122D|0|B|ワイバーン|18D4E|28|D5|352|272|272|0|C|ワイバーン|18D4E|28|12E|3BE|249|249|0|11|ワイバーン|18D4E|28|202|34D|274|274|0|12|ワイバーン|18D4E|28|FB|37B|22E|22E|0|"

// ラスボス観战でＩＤおらんエラー
//"C|0|0|犬歯|186EE|53|45|4F7|273|273|4|5|ヘルハウンド|18AA8|50|2C8|67C|492|492|8|A|リヴェリウス|18E16|5A|3DF5|3DF5|2A4F|2A4F|0|F|バロス|18E19|50|3608|3608|2495|2495|0|10|フリアボロス|18E17|50|3674|3674|25AC|25AC|0|11|アッカス|18E18|50|3549|3549|241C|241C|0|"

// 复数魔法反射で死亡したときバグ
//"C|0|0|赤目|1879E|49|3A0|47A|66B|8A6|4|1|樹音|187A7|57|3BB|3BB|300|300|4|2|バターカップ|187D9|55|418|418|2D7|3C5|4|3|フェア|187E2|58|230|413|810|B3D|4|4|ろろ|1879A|49|355|438|24A|26B|404|5|???|18AA0|49|2B5|429|692|8C2|8|6|キュベレイ|18BC1|56|3C0|54D|3D2|418|8|7|名将バビーソ|18BC8|56|5D9|600|399|4E2|8|8|ハチヤロー|18BC1|4E|4B2|4B2|328|3EC|8|A|リヴェリウス|18E16|5A|3E75|3E75|2A38|2A38|0|F|バロス|18E19|50|256A|3713|2563|2563|0|10|フリアボロス|18E17|50|2275|320C|21B3|21B3|0|11|アッカス|18E18|50|210D|3570|2440|2440|0|"

// 召还できないバグ
//"C|0|0|まじゅちゅち|1879E|5A|51F|51F|AC2|AC2|5|5|ピグミーバット|18B7A|58|4CE|4CE|551|551|9|A|メヅク|188AF|1E|ACF|ACF|910|910|1|"

// 召还ターン
//"C|0|0|たまこ。|1879E|64|2453|32CF|11A3|1397|405|B|グリ|1879E|49|602|602|496|496|1|C|グラ|1879E|49|815|815|591|591|1|F|ピート|1879E|4B|EA5|EA5|BC6|BC6|1|"

// 召还でとまる？
//"C|0|0|たまこ。|1879E|64|321C|32CF|1397|1397|404|A|シーバット|18B84|46|4D2|4D2|399|399|0|B|グリ|18B84|49|602|602|496|496|0|C|グラ|18B84|49|815|815|591|591|0|D|シーバット|18B84|44|49D|49D|3BC|3BC|0|E|シーバット|18B84|45|4D6|4D6|3DF|3DF|0|F|ピート|18B84|4B|EA5|EA5|BC6|BC6|0|10|シーバット|18B84|45|4C7|4C7|3D9|3D9|0|11|シーバット|18B84|44|429|429|33D|33D|0|12|シーバット|18B84|45|47E|47E|32B|32B|0|"

// フラメンコバグ
//"C|0|5|たまこ。|19F30|64|26F8|271A|B01|BC9|405|1|サトル|19E14|19|1D36|201C|492|4C4|405|2|フレミング|19F0C|63|4E00|4EAA|4E41|4E41|5|3|こってり|19E2B|6|1BD|1BD|13B|140|405|7|ヒートシェル|1A204|25|238|2EB|22D|22D|9|A|アダマンタス|1A205|31|39E|39E|2C0|2C0|1|F|アダマンタス|1A205|31|3A9|3A9|2C7|2C7|1|10|アダマンタス|1A205|31|3CA|3CA|30E|30E|1|11|アダマンタス|1A205|31|3B0|3B0|2B0|2B0|1|"

//"C|0|0|真葛|187CE|4D|5C9|6E2|6|15A|4|6|ヒューズ|186E4|51|0|4B5|15F|195|406|2|マルコ?ロッソ|186BD|52|83|498|16D|3CE|4|3|さやん|187EF|45|0|3F7|11A|3B3|6|9|リル|187D2|42|7A|37F|BB|11F|4|5|はちにゅ☆|18BC1|45|205|440|11E|1AA|8|7|?????|18BC1|4F|4CD|4F3|1A2|1C5|48|8|井　宿|18CE1|41|475|4B9|1D0|278|48|4|真フォルネウス|18B00|47|3A1|46D|363|3D9|48|A|ひめ|187B7|53|475|475|191|4CD|4|10|AIKO?|187DC|44|5C2|5FA|A7|1C9|4|11|珠晶|187AE|50|3F2|550|72|180|4|12|Rena|187E7|44|67B|67B|AF|181|4|13|りき|18710|42|3EC|41A|B|10E|4|F|ごれむくん|18CF4|4A|5C6|5C6|CE|1F4|8|B|9/4アルバム発売|18D59|37|45D|66C|53|125|8|C|ウルスラ|18B02|47|3DC|3DC|303|413|8|D|白い恋人|18B02|40|3A0|3A0|2B4|38C|8|E|ホーネット|18BC1|3E|411|411|75|19C|8|"


// 搅乱の反射で止まる
//"C|0|0|リザードテイル団|186B4|64|3D|58C|2D|172|4|5|ワータイガー|18A88|1|5D|5D|57|57|8|A|ゴブリン|18DA8|1|3D|62|47|47|0|"

// 踊るポジションチェンジ
//"C|0|0|まんが道場|19EA6|68|3BA|3BA|1CF|1CF|4|6|真珠夫人|19F63|68|3CF|3CF|1D6|1D6|4|7|ごじゅう|19F76|53|3CF|3CF|1D6|1D6|4|5|ケイブベア|18A92|50|67D|67D|232|232|8|1|ポーラーベア|18A94|3C|69A|69A|197|197|8|2|ケイブベア|18A92|50|679|679|207|207|8|F|コンボイまにあ|19E1E|68|3BA|3BA|1CF|1CF|4|10|かかってくるにゅ|19F70|50|F31|F31|1F7|1F7|4|11|デバッグ次郎|19E5D|68|3BA|3BA|1CF|1CF|4|A|ケイブベア|18A92|50|651|651|248|248|8|B|ケイブベア|18A92|50|5E5|5E5|21D|21D|8|C|ケイブベア|18A92|50|5E4|5E4|220|220|8|"

);
#ifdef PUK2
				// ＢＰから自分のＩＤを取り出す
				BattleMyNo = 20;
				{
					FILE *fp;
					char s[sizeof(BcData)+2];
					int i;

					// 必要な B プロトコルの C データと M データが书き込まれているファイル
					// 例)
					//   B C|0|0|バッツ|19A5A|64|527|527|356|36B|1000404|A|ゴールドスカル|1AF00|1F|22|183|300|300|9000000|F|ゴールドスカル|1AF00|1E|A9|13D|279|279|9000000|10|ゴールドスカル|1AF00|20|30|18B|2DE|2DE|9000000|11|ゴールドスカル|1AF00|20|37|168|2CE|2CE|9000000|
					//   B M|CFP|aA|d2E|CFP|a11|d2E|CFP|a10|d2E|CFP|aF|d2E|SKL|s6594|f15|w0|a0|dF|80001|A0|dA|221|F9|11|FF|ACT|0|bg|0| 
					fp = fopen( "battlerecv.txt", "rt" );                            //MLHIDE
					if (fp){
						for(i=0;i<battlePlayLine;i++) fgets( s, sizeof(s), fp );
						for(;;){
							fgets( s, sizeof(s), fp );
							sjisStringToEucString(s);
							i = strlen(s);
							battlePlayLine++;
							// もうデータが无いとき
							if ( feof(fp) || s[0] == '@' ){
		#ifdef PUK3_NOTFREE_WINDDOW
								SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
		#else
								SubProcNo = BATTLE_PROC_INIT;
								// アクション全抹杀
								DeathAllAction();
								// ムービー読み込みフラグＯＮ
								ReadBmDataFlag = FALSE;
		#endif
								break;
							}
							if ( s[0] == 'B' && s[1] == ' ' && s[2] == 'E' ){
								efficacydisp = EFFICACYDISP_NOMAL|EFFICACYDISP_DUEL|EFFICACYDISP_BOSS;
							}
							if ( s[0] == 'B' && s[1] == ' ' && s[2] == 'I' ){
								sscanf( s, "B I %d", &i );                                    //MLHIDE
								BattleMyNo = i;
							}
							if ( s[0] == 'B' && s[1] == ' ' && s[2] == 'P' ){
								sscanf( s, "B P|%X", &BattleMyNo );                           //MLHIDE
							}
							if ( s[0] == 'B' && s[1] == ' ' && s[2] == 'C' ){
								i -= 2;
								memcpy( BcData, s+2, i );
								BcData[i] = '\0';
								for(;;i--){
									if ( BcData[i-1] == '\n' ) BcData[i-1] = '\0';
									else if ( BcData[i-1] == '\r' ) BcData[i-1] = '\0';
									else if ( BcData[i-1] == ' ' ) BcData[i-1] = '\0';
									else break;
								}
								break;
							}
						}
						fclose(fp);
						// もうデータが无いとき
		#ifdef PUK3_NOTFREE_WINDDOW
						if ( SubProcNo == BATTLE_PROC_OUT_PRODUCE_INIT ) break;
		#else
						if ( SubProcNo == BATTLE_PROC_INIT ) break;
		#endif
					}
				}
				// 飞んだなら
				if (MyAkoFlag){
					// アクション抹杀
					DeathAllAction();
					// バトル初期化
					InitBattle();
				}
#else
				// アクション抹杀
				DeathAllAction();
				// バトル初期化
				InitBattle();
#endif
				
				// バトルキャラクターデータの読み込み
				ReadBcData();
				// 强制終了の时
				if( BattleBcFieldFlag & BC_FIELD_FLAG_END ){
#ifdef PUK2
	#ifdef PUK3_BATTLE_CMDRECV
					// 战闘終了演出へ
					SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
	#else
					// アクション抹杀
					DeathAllAction();
					// バトル初期化
					InitBattle();
	#endif
#else
					// 战闘終了演出へ
					SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
#endif
					break;
				}
				// 不意打ち判定处理
				Surprisal();
#ifdef PUK2
#else
				// ＢＰから自分のＩＤを取り出す
				BattleMyNo = 20;
				// ＢＰから自分のＩＤを取り出す
				//BattleMyNo = 1;
#endif
#ifdef PUK3_RIDE_BATTLE
				// 现在のペットの番号を更新
				BattleNowPetNo = CheckBattlePet();
				// ペットのデータ更新
				SetBattlePetdata();
#endif
				// 次ぎのプロセスへ
				SubProcNo = BATTLE_PROC_CHAR_APPEAR;
				break;
			}
#endif
			
			
			break;
		
		case BATTLE_PROC_CHAR_APPEAR: // 登场演出 ********************************************/
			
			// マウス等级がメニュー以下の时
			//if( mouse.level < DISP_PRIO_MENU && TaskBarFlag == FALSE ){
			//	drawGrid();		// グリッドカーソル表示
			//	moveProc();		// 移动处理
			//}
			
			//BattleBpFlag |= BP_FLAG_ENEMY_SURPRISAL; 
			//BattleBpFlag |= BP_FLAG_PLAYER_MENU_NON;
			//BattleBpFlag |= BP_FLAG_PLAYER_MENU2_NON;
			//BattleBpFlag |= BP_FLAG_PET_MENU_NON;
			// 不意打ち表示处理
			SurprisalDisp();
			
			// キーボード处理
			KeyBoardProc();		
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示
			
			// 名称の表示
			BattleNameDisp();
			/* チャット处理 */
			ChatProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();
			// フィールド关连处理（グループ设定ショートカットキーのためだけ）
			fieldProc();			
			
			// 登场終了チェック
			if( BattleCheckAppear() == TRUE ){ 
				// 不意打ちウィンドウ出ていたら
				if( pActSurprisalWnd != NULL ){
					// ウィンドウ抹杀
					DeathAction( pActSurprisalWnd );
					pActSurprisalWnd = NULL;
					// 不意打ちフラグ初期化
					BattleBpFlag &= ~BP_FLAG_PLAYER_SURPRISAL; 
					BattleBpFlag &= ~BP_FLAG_ENEMY_SURPRISAL; 
					
				}
				// ターン每のバトルメニュー初期化
				InitTurnBattleMenu();
				// コマンド入力へ
				SubProcNo = BATTLE_PROC_CMD_INPUT;
			}
			
			break;
			
		case BATTLE_PROC_CMD_INPUT: // コマンド入力 ********************************************/
		
			// キーボード处理
			KeyBoardProc();		
			// コマンド入力济みフラグのチェック
			//CheckBattleAnimFlag();
			// バトル时のアクション走らせる
			//BattleRunAction();
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
#ifdef PUK3_NEWSKILL_LVDOWN
			// 整理の为にここに持ってこようとしたが、
			// BattleMenuProc_PUK2で使う情报を更新してしまう为断念
//			StockTaskDispBuffer();
#endif
			// タスク表示データをバッファに溜める（マウスラグ有りで×）
			//StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

			// 名称の表示
			BattleNameDisp();
			/* チャット处理 */
			ChatProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();		
			
			// フィールド关连处理（グループ设定ショートカットキーのためだけ）
			fieldProc();			
			
#ifdef PUK3_BATTLE_CMDRECV
			cmdrecvCnt = 0;
#endif
#ifdef _DEBUG			
			// 离线模式の时
			if( offlineFlag == TRUE ){
				// ２０匹分ループ
				for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( i );
#endif
					// キャラがいるとき
					if( pActBc[ i ] != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
						CheckAction( pActBc[ i ] );
#endif
						// 最下位ビットを判定
						pActBc[ i ]->atr |= ACT_ATR_BTL_CMD_END;
					}
				}
				// 次のプロセスへ
				SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
				break;
			}
#endif
#ifdef PUK2_NEW_MENU
			// バトルメニュー处理
////			BattleMenuProc();
			BattleMenuProc_PUK2();
#else
			// バトルメニュー处理
			BattleMenuProc();
#endif
			
			// タスク表示データをバッファに溜める（マウス判定に１フレームのラグをなくす）
			StockTaskDispBuffer();

#ifdef PUK2
			LocalBurstTimeProc();
#endif
			
			break;
			
		case BATTLE_PROC_RECV_MOVIE_DATA: // ムービー受信待ち ********************************************/
		
			// キーボード处理
			KeyBoardProc();		
			// コマンド入力济みフラグのチェック
			//CheckBattleAnimFlag();
			// バトル时のアクション走らせる
			//BattleRunAction();
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

			// 名称の表示
			BattleNameDisp();
			/* チャット处理 */
			ChatProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();
			
			// フィールド关连处理（グループ设定ショートカットキーのためだけ）
			fieldProc();

#ifdef PUK2
			LocalBurstTimeProc();
#endif
			
#ifdef PUK3_BATTLE_CMDRECV
			// 离线模式の时
			if( offlineFlag == TRUE ){
				// まだ次へ行かないなら
				if ( VK[ VK_S ] & KEY_ON ||
					 cmdrecvCnt < BATTLECHECK_CMDRECV_INTERVAL ){
					// プレイヤーの数を调べる（复数の时）
					if( CheckBattlePlayerCnt() >= 2 ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(185, "等待指令状态。"));
					}
					if ( cmdrecvCnt < BATTLECHECK_CMDRECV_INTERVAL ) cmdrecvCnt++;
					break;
				}
			}
#endif
#ifdef _DEBUG			
			// 离线模式の时
			if( offlineFlag == TRUE ){
				// 文字列作成
				strcpy( BmData,
						// 防御
						//"M|HIT|a0|dF|1|1AE|FF|HIT|a0|d10|25|1AE|FF|END|"
						// 逃げる
						//"M|HIT|a0|dF|61|1AE|FF|ESC|a10|0|ESC|a11|1|HIT|a0|d10|62|1AE|FF|END|"
						// アルティメット
						//"M|HIT|a0|dD|1|1AE|aD|d0|76|1AE|FF|END|"
						//　カウンターで志望
						//"M|HIT|aD|d0|1|1AE|a0|dD|21|1AE|FF|HIT|a0|dF|1|1AE|FF|END|"
						//　殴って戾って殴る						              
//						"M|HIT|aC|d0|42|C|aC|d0|1|6D|aC|d0|8|0|aC|d0|2|79|a0|dC|11|72|aC|d0|8|0|a0|dC|21|72|FF|HIT|aD|d0|1|48|aD|d0|2|A5|a0|dD|1|71|aD|d0|2|C7|a0|dD|22|7E|FF|HIT|a0|dA|2|48|a0|dA|8|0|a0|dA|8|0|a0|dA|22|4F|a0|dB|21|C2|a0|dE|2|5D|a0|dE|0|0|aE|d0|21|B3|FF|HIT|a1|dE|2|15|a1|dE|22|2A|FF|END|"
						// 逃げて終わるバグ
						//"M|bg|b|ff|bg|c|ff|ESC|e11|f1|END|"
						// 殴って退却
						//"M|HIT|a0|dD|21|C|a0|dA|21|C|FF|CLR|A|B|C|D|E|0|1|FF|"
						// 飞び道具
#if 0
						"M|MSL|a0|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\             //MLHIDE
						MSL|a1|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a2|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a3|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a4|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a5|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a6|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a7|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a8|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a9|2|dD|5|C|dB|1|C|dA|1|C|dC|1|C|dE|1|E|FF|\
						MSL|a1|2|dE|5|C|FF|\
						MSL|a2|2|dE|5|C|FF|\
						MSL|a3|2|dE|5|C|FF|\
						MSL|a4|2|dE|5|C|FF|\
						MSL|a5|2|dE|5|C|FF|\
						MSL|a6|2|dE|5|C|FF|\
						MSL|a7|2|dE|5|C|FF|\
						MSL|a8|2|dE|5|C|FF|\
						MSL|a9|2|dE|5|C|FF|\
						MSL|a0|2|d13|1|E|FF|\
						MSL|a1|2|d13|1|E|FF|\
						MSL|a2|2|d13|1|E|FF|\
						MSL|a3|2|d13|1|E|FF|\
						MSL|a4|2|d13|1|E|FF|\
						MSL|a5|2|d13|1|E|FF|\
						MSL|a6|2|d13|1|E|FF|\
						MSL|a7|2|d13|1|E|FF|\
						MSL|a8|2|d13|1|E|FF|\
						MSL|a9|2|d13|1|E|FF|\
						MSL|aA|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|aB|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|aC|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|aD|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|aE|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|aF|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|a10|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|a11|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|a12|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|\
						MSL|a13|2|d9|5|C|d7|5|C|d5|5|C|d6|5|C|d8|5|C|FF|"
#endif
#if 0
						"M|MSL|a8|2|dC|5|C|dE|5|C|FF|\                                  //MLHIDE
						MSL|a5|2|dE|5|C|FF|\
						MSL|a5|2|dE|5|C|FF|\
						MSL|a5|2|dE|5|C|FF|"
#endif
						"M|"                                                            //MLHIDE
//						"M|MSL|a5|2|dE|5|C|FF|"
						//"M|MSL|a0|1|dD|1|C|dA|2|C|FF|MSL|a13|2|dD|5|C|dB|9|C|dA|21|C|dC|41|C|dE|41|E|FF|"
						//"M|MSL|a0|1|dD|1|C|FF|MSL|a13|2|dD|5|C|dB|9|C|dA|21|C|dC|41|C|dE|41|E|FF|"
						// 一番右ラインが仲间に攻击
						//"M|MSL|a0|2|d8|5|C|d6|9|C|d5|21|C|d7|41|C|d9|41|E|FF|"
						// 一番左ラインが仲间に攻击
						//"M|MSL|aA|2|d12|5|C|d10|9|C|dF|21|C|d11|41|C|d13|41|E|FF|"
						//
						//"M|MSL|aA|1|d8|5|C|d6|9|C|d7|61|C|d9|61|E|FF|"
						//"M|MSL|aA|1|d8|5|C|d6|9|C|d5|21|C|d7|41|C|d9|41|E|FF|"
						//"M|MSL|a0|w2|dA|22|173|FF|"
						//"M|MSL|aA|1|d6|9|C|d5|21|C|d7|41|C|d9|41|E|FF|"
						//"M|MSL|a0|4|dA|5|C|dB|9|C|dC|21|C|dD|41|C|dE|41|C|FF|"
						// 飞び道具アルティメットですぐ攻击？
					//"M|MSL|a0|4|dA|62|C|d10|62|C|dD|62|C|FF|HIT|aC|d0|1|C|FF|"
					//"M|MSL|a0|4|dA|22|C|dB|22|C|dD|22|C|FF|HIT|aC|d0|1|C|FF|"
						// 行动終わらんバグ
						//"M|HIT|a0|dA|9|32|a0|dA|1|32|FF|bg|a|ff|ESC|eC|f0|"
						// スキル
						//"M|SKL|1|a0|dD|21|C|a0|dA|21|C|FF|"
						// 柴田さんと止まったバグ
						//"M|HIT|a0|dD|62|55B9|a0|dA|62|4E1D|FF|bg|1|ff|HIT|aF|d1|4|0|aF|d1|5|1|FF|HIT|aB|d0|8|0|aB|d0|8|0|FF|HIT|aE|d0|1|1|aE|d0|8|0|FF|HIT|a11|d1|5|1|a11|d1|6|D0|FF|"
						// 气孔弹でスキルアップ
						//"M|SKL|22|10|a0|dA|5|C|dB|9|C|dC|21|C|dD|41|C|dE|41|C|FF|SUP|0|1|2|3|4|5|6|7|8|9|FF|"
						//"M|SKL|22|10|a0|dA|5|C|dB|9|C|dC|21|C|dD|41|C|dE|41|C|FF|SUP|0|1|FF|"
						// 气孔弹でスキルアップ
						//"M|SKL|22|a0|dA|5|C|dB|9|C|dC|21|C|dD|41|C|dE|41|C|FF|SUP|0|1|2|3|4|5|6|7|8|9|FF|"
						//"M|SKL|22|a0|dA|5|C|9|C|FF|"
						//"M|SKL|22|10|a0|dA|5|C|dA|9|C|FF|"
						// Ｉｄおらんエラー
						//"M|SKL|s22|a0dA|1|79|aA|d0|f11|1|FF|HIT|aA|d0|1|0|aA|d0|1|1|aA|d0|8|0|aA|d0|1|1|aA|d0|2|18|FF|"
						//"M|SKL|s22|a0|dA|1|2|aA|d0|f18|0|FF|HIT|a1|dA|2|A7|a1|dA|2|9F|a1|dA|1|4F|a1|dA|1|4F|a1|dA|2|9F|FF|HIT|aA|d0|8|0|aA|d0|1|0|aA|d0|8|0|aA|d0|8|0|aA|d0|8|0|FF|"
						// 逃げる
						//"M|HIT|a0|dF|61|1AE|FF|ESC|a10|0|ESC|a11|1|ESC|aD|1|"
						//"M|HIT|a0|dF|61|1AE|FF|ESC|a10|0|ESC|a11|1|HIT|a0|d10|62|1AE|FF|"
						//"M|MSL|a0|4|dA|21|C|FF|ESC|a10|0|ESC|a11|1|HIT|a0|d10|62|1AE|FF|"
						//"M|ESC|a4|0|ESC|a4|1|HIT|a0|d10|62|1AE|FF|END|"
						// フォースポイント消费
						//"M|SKL|s22|10|a0|dA|1|79|dB|f11|1|FF|"
						// アルティメットでペットも逃げる
						//"M|HIT|aF|d1|61|1AE|FF|"
						//"M|HIT|aF|d6|61|1AE|FF|"
						// ポジションチェンジ
						//"M|POS|a0|POS|a1|POS|a2|POS|a3|POS|a4|MSL|a0|4|dA|21|C|FF|ESC|a10|0|ESC|a11|1|HIT|a0|d10|62|1AE|FF|POS|a4"
						//"M|POS|a0|POS|a1|POS|a2|POS|a3|POS|a4|POS|a5|POS|a6|POS|a7|POS|a8|POS|a9|POS|aA|POS|aB|POS|aC|POS|aD|POS|aE|POS|aF|POS|a10|POS|a11|POS|a12|POS|a13|"
						// ポジションチェンジしてから逃げる
						//"M|POS|a0|ESC|a5|1|"
						// ぺっと返回
						//"M|MON|a0|0|"
						// ペット出す
						//"MON|a0|0|MON|a0|1|マッスル||186D4|1|18B|640|100|5|MON|a5|0|MON|a5|1|マッスル||186D4|1|18B|640|100|5|MON|aA|0|MON|aA|1|マッスル||186D4|1|18B|640|100|5|MON|aF|0|MON|aF|1|マッスル||186D4|1|18B|640|100|5|"
						//"M|MON|a0|0|MON|a0|1|マッスル||186D4|1|18B|640|100|5|MON|a5|0|MON|a5|1|マッスル||186D4|1|18B|640|100|5|MON|aA|0|HIT|a10|d1|61|1AE|FF|MON|aA|1|マッスル||186D4|1|18B|640|100|5|MON|aF|0|MON|aF|1|マッスル||186D4|1|18B|640|100|5|"
						//"M|MON|a5|0|MON|a5|1|マッスル||186D4|1|18B|640|100|5|MON|a1|0|MON|a2|0|MON|a3|0|"
						// 装备变更
						//"M|EQU|a5|186D5|EQU|aA|186D8|"
						//"M|ESC|e0|f1|END|"						
						//"M|END|"

						//"M|HIT|aA|d5|11|5|FF|HIT|aC|d5|11|0|FF|HIT|aB|d5|11|2A|FF|MSL|a5|w2|dB|1|139|dA|22|1B8|dC|2|166|FF|MSL|a5|w2|dB|21|139|dC|21|19B|FF|"
						//"M|HIT|aA|d5|22|5|FF|HIT|aC|d5|11|0|FF|HIT|aB|d5|11|2A|FF|MSL|a5|w2|dB|1|139|dA|22|1B8|dC|2|166|FF|MSL|a5|w2|dB|21|139|dC|21|19B|FF|"

						// アイテム体力回复演出
						//"M|HIT|aA|d0|8|0|FF|ITM|0|a0|0|d5|12|FF|"
						//"M|ITM|0|a0|0|d5|12|d6|12|FF|"
						//"M|ITM|0|a0|0|d1|12|d2|12|d3|12|d4|12|d5|12|FF|"
						//"M|HIT|aB|d5|1|12|FF|HIT|aA|d0|8|0|FF|ITM|e0|r5|0|B|10|FF|HIT|a0|dA|1|15|FF|"
						//"M|HIT|aA|d5|1|1|FF|ITM|e0|r5|0|5|d70|0|d62|FF|HIT|a5|dA|1|12|FF|"
						
						// スキル体力回复
						//"M|HIT|aA|d5|1|1|FF|SKL|64|20|r5|0|5|d70|0|d62|FF|HIT|a5|dA|1|12|FF|"
						
						// バグ
						//"M|HIT|aA|d0|8|0|FF|MSL|a0|w2|dA|22|8E|FF|END|"
						//"M|HIT|aA|d0|1|1E|FF|HIT|a5|d0|1|6|FF|HIT|a0|d5|1|14|FF|"
						
						// コンボ
						//"M|CMB|d10|a0|1|1E|a1|1|1E|a2|1|1E|a3|1|1E|a4|1|1E|a5|1|1E|FF|"
						//"M|CMB|dA|a0|1|1E|FF|"
						//"M|CMB|d10|a0|1|1E|a1|1|1E|a2|1|1E|a3|1|1E|a4|1|1E|a5|1|1E|a6|1|1E|a7|1|1E|a8|1|1E|a9|1|1E|aB|1|1E|aC|1|1E|aD|1|1E|aE|1|1E|aA|1|1E|aF|1|1E|a11|1|1E|a12|1|1E|a13|1|1E|FF|"
						
						//|SKL| 64|20|r5|0|5|d70|0|d62|FF|HIT|a5|dA|1|12|FF|"
						
// 新スキルＩＤ **************************************************
						// 魔法攻击
						//"M|SKL|C7|20|a10|d1|21|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|SKL|BE|20|r0|d10|1|100|dC|1|1208|FF|SKL|C8|20|a10|d3|1|1E|d9|5|3949|FF|SKL|F9|20|r0|d10|1|100|dC|1|1208|FF|"
						// 地
						//"M|SKL|76C|20|a10|d1|1|1E|d3|1|1E|d6|1|1E|d9|5|3949|FF|SKL|76F|20|a0|dA|1|1E|d10|1|1E|dC|1|1E|d13|5|3949|FF|SKL|772|20|a10|d1|1|1E|d3|1|1E|d4|1|1E|d9|5|3949|FF|SKL|775|20|a10|d1|1|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						// 地（反射）
						//"M|SKL|76C|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|SKL|C1|20|a0|dA|20001|1E|d10|40001|1E|dC|20001|1E|d13|5|3949|FF|SKL|C4|20|a10|d1|20001|1E|d3|40001|1E|d4|20001|1E|d9|5|3949|FF|SKL|C7|20|a10|d1|20001|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						//"M|SKL|BE|20|a1|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|10001|3949|FF|SKL|C1|20|a0|dA|10001|1E|d10|10001|1E|dC|10001|1E|d13|5|3949|FF|SKL|C4|20|a10|d1|10001|1E|d3|10001|1E|d4|20001|1E|d9|5|3949|FF|SKL|C7|20|a10|d1|10001|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						//"M|SKL|AFD|20|a10|d1|1|1E|d3|1|1E|d6|1|1E|d9|5|3949|FF|"
						
						// マジック防御
						//"M|SKL|AFD|20|a10|d1|401|1E|d3|801|1E|d6|1|1E|d9|5|3949|FF|"
						
						// 水（反射）
						//"SKL|AF0|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|SKL|CB|20|a0|dA|1|1E|d10|1|1E|dC|1|1E|d13|5|3949|FF|SKL|CE|20|a10|d1|1|1E|d3|1|1E|d4|1|1E|d9|5|3949|FF|SKL|D1|20|a10|d1|1|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						//"M|SKL|AF0|20|a10|d1|10001|1E|d3|10001|1E|d6|10061|1E|d9|5|3949|FF|SKL|C8|20|a11|d1|10001|1E|d3|10021|1E|d9|5|3949|FF|"
						// 水（吸收、无效）
						//"M|SKL|AF0|20|a10|d1|20001|1E|d3|40001|1E|d6|20001|1E|d9|40005|3949|FF|SKL|CB|20|a0|dA|20001|1E|d10|40001|1E|dC|20001|1E|d13|5|3949|FF|SKL|CE|20|a10|d1|1|1E|d3|20001|1E|d4|40001|1E|d9|5|3949|FF|SKL|D1|20|a10|d1|20001|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						//"SKL|AF0|20|a10|d1|20001|1E|d3|40001|1E|d6|20001|1E|d9|40005|3949|FF|"
						//"SKL|AF0|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|"
						//"SKL|AF0|20|a10|d1|1|1E|d3|1|1E|d6|1|1E|d9|5|3949|FF|"
						
						// 风
						//"M|SKL|898|20|aA|d1|10001|1E|d3|1|1E|d6|1|1E|d9|5|3949|FF|"
						//"M|SKL|898|20|aA|d1|10001|1E|FF|"
						//"M|SKL|898|20|a10|d1|1|1E|d3|1|1E|d6|1|1E|d9|5|3949|FF|SKL|76F|20|a0|dA|1|1E|d10|1|1E|dC|1|1E|d13|5|3949|FF|SKL|772|20|a10|d1|1|1E|d3|1|1E|d4|1|1E|d9|5|3949|FF|SKL|775|20|a10|d1|1|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						
						// マジック防御
						//"M|SKL|898|20|a10|d1|401|1E|d3|801|1E|d6|1|1E|d9|5|3949|FF|SKL|76F|20|a0|dA|401|1E|d10|1|1E|dC|801|1E|d13|5|3949|FF|SKL|772|20|a10|d1|1|1E|d3|1|1E|d4|1|1E|d9|5|3949|FF|SKL|775|20|a10|d1|1|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						
						
						
						// 气孔弹
						//"M|SKL|190|0|aA|d5|1|5|d10|1|5|d1|1|5|FF|SKL|28|0|a0|dB|1|5|dD|1|5|dA|1|5|dC|1|5|dE|1|5|FF|SKL|28|0|a1|d12|1|5|FF|"
						//"SKL|190|0|aA|d5|1|5|d10|1|5|d1|9|5|FF|SKL|190|0|aE|d5|1|5|d10|1|5|d1|9|5|FF|"
						//"SKL|190|0|a2|d5|1|5|d10|1|5|d1|9|5|FF|SKL|190|0|aE|d5|1|5|d10|1|5|d1|9|5|FF|"
						
						// 通常攻击、直接攻击
						//"M|SKL|2DA|0|0|aA|d5|1|5|aA|d10|1|5|a10|dA|1|5|FF|SKL|2DA|0|0|a0|dA|1|5|FF|SKL|2DA|0|0|a1|d12|1|5|FF|SKL|2DA|0|0|aC|d9|1|5|FF|"
						// 飞び道具攻击
						//"SKL|1C84|0|1|aA|d5|1|5|d10|1|5|d1|9|5|FF|SKL|1C84|0|2|a0|dD|9|5|dB|9|5|dA|21|5|dC|61|5|dE|21|5|FF|SKL|1C84|0|2|a1|d12|1|5|d11|1|5|d10|1|5|FF|"
				//		"SKL|1C84|0|2|a0|dD|9|5|dB|9|5|dA|21|5|dC|61|5|dE|21|5|FF|"
				//		"SKL|1C84|0|3|aA|d5|2001|5|FF|"
				//		"SKL|1C84|0|3|aA|d5|2005|5|FF|"
				//		"SKL|1C84|0|3|aA|d5|4001|5|FF|"
				//		"SKL|1C84|0|3|aA|d5|8001|5|FF|"
						//"SKL|1C84|0|1|a2|d5|9|5|d10|9|5|d1|9|5|FF|"
						
						
						// スキル体力回复
						//"SKL|17D6|20|a0|1|5|d70|2|0|d62|FF|"
						//"SKL|17D9|20|a0|1|5|d70|2|0|d62|FF|"
						//"SKL|17DC|20|a0|1|5|d70|2|0|d62|FF|"
						//"SKL|17DD|20|a0|1|5|d70|2|0|d62|FF|"
						//"SKL|17D6|20|a1|FF|"
						
						// 反射魔法
						//"SKL|157C|20|a0|t1|5|t2|0|FF|"
						
						// コンボ
						//"M|CMB|d10|a0|1|1|a1|1|1|a2|1|1|a3|1|1|a4|1|1|a5|1|1|FF|"
						
						// 通常攻击、直接攻击（反射）
						//"M|SKL|2DA|0|0|aA|d5|1|5|a5|dA|2001|5|aA|d5|1|5|FF|SKL|2DA|0|0|a0|dB|1|5|FF|SKL|2DA|0|0|a1|d12|1|5|FF|SKL|2DA|0|0|aC|d9|1|5|FF|"
						
						// 通常攻击、直接攻击（吸收）
						//"M|SKL|2DA|0|0|aA|d5|4001|5|FF|SKL|2DA|0|0|a0|dB|1|5|FF|SKL|2DA|0|0|a1|d12|1|5|FF|SKL|2DA|0|0|aC|d9|1|5|FF|"
						
						// 通常攻击、直接攻击（无效）
						//"M|SKL|2DA|0|0|aA|d5|8001|5|FF|SKL|2DA|0|0|a0|dB|1|5|FF|SKL|2DA|0|0|a1|d12|1|5|FF|SKL|2DA|0|0|aC|d9|1|5|FF|"
						
						// コンボ（反射）
						//"M|CMB|d10|a0|2001|1|a1|1|1|a2|2001|1|a3|1|1|a4|2001|1|a5|1|1|FF|"
						//"M|CMB|d10|a0|1|1|a1|1|1|a2|1|1|a3|1|1|a4|1|1|a5|1|1|FF|"
						//"M|CMB|d10|a0|2001|1|a1|1|1|a2|1|1|a3|1|1|a4|1|1|a5|1|1|FF|"
						
						// コンボ（吸收）
						//"M|CMB|d10|a0|4001|100|a1|1|100|a2|1|100|a3|1|100|a4|1|100|a5|1|100|FF|"
						
						// コンボ（无效）
						//"M|CMB|d10|a0|8001|100|a1|1|100|a2|1|100|a3|1|100|a4|1|100|a5|1|100|FF|"
						//"M|CMB|d10|a0|2001|100|a1|2001|100|a2|2001|100|a3|2001|100|a4|2001|100|a5|2001|100|FF|"
						//"M|CMB|d10|a0|4001|345|a1|4001|160|a2|4001|396|a3|4001|940|a4|4001|862|a5|4001|610|FF|"
						//"M|CMB|d10|a0|8001|100|a1|8001|100|a2|8001|100|a3|8001|100|a4|8001|100|a5|8001|100|FF|"

						//"M|CMB|d10|a0|2001|100|a1|2001|100|a2|2001|100|FF|"

						// 飞び道具攻击（反射、吸收、无效）
						//"M|SKL|2DA|0|1|aA|d5|2001|5|d10|4001|5|d1|8001|5|FF|SKL|2DA|0|2|a0|dD|2001|5|dB|61|5|dA|4001|5|dC|61|5|dE|8001|5|FF|SKL|2DA|0|3|a1|d12|2001|5|d11|4001|5|d10|8001|5|FF|"
						
						// 身交わし（飞び道具）
					//	"SKL|1C84|0|1|aA|d5|101|5|d10|101|5|d1|101|5|FF|"
						
						// 气孔弹（反射、吸收、无效）
						//"M|SKL|28|0|aA|d5|2001|5|d10|4001|5|d1|8001|5|FF|SKL|28|0|a0|dB|1|5|dD|1|5|dA|1|5|dC|1|5|dE|1|5|FF|SKL|28|0|a1|d12|1|5|FF|"
						
						// 连続攻击、直接攻击（反射、吸收、无效）
						//"SKL|0|0|aA|d5|1|5|aA|d5|1|5|aA|d5|1|5|FF|"
						
						//"SKL|0|0|aA|d5|2001|5|aA|d5|4001|5|aA|d5|8001|5|aA|d5|2001|5|a5|dA|4001|5|FF|"
						
						////"SKL|0|0|aA|d5|4001|5|aA|d5|2001|5|a5|dA|2031|5|aA|d5|2011|5|FF|"
						// カウンターの反射で死亡したとき
						//"SKL|0|0|aA|d0|2002|1AD|a0|dA|f2031|1|FF|"
					//	"SKL|0|0|aA|d0|2002|1AD|a0|dA|f1|1|FF|"
					//	"SKL|0|0|aA|d5|1|5|aA|d5|1|5|a5|dA|11|5|FF|"
						
						// 回复魔法
						//"M|SKL|17D4|20|a10|d1|21|1E|d3|1|1E|d6|61|1E|d9|5|3949|FF|"
						// 回复魔法（反射、吸收、无效）
						//"M|SKL|262|20|a2|d1|10000|1E|d3|20001|1E|d6|40000|1E|d9|20020|3949|FF|SKL|262|20|a10|d1|20000|1E|d3|20001|1E|d6|40000|1E|FF|"
						//"M|SKL|262|20|a10|d1|10000|1E|d3|10000|1E|d6|10000|1E|FF|SKL|262|20|a10|d1|20000|1E|d3|20001|1E|d6|40000|1E|FF|"
						//"M|SKL|17D4|20|a10|d1|10000|1E|FF|SKL|17D4|20|a10|d1|20000|1E|d3|20021|1E|d6|40000|1E|FF|"
						
						
						// 通常攻击、护卫
						//"SKL|1C84|0|0|aA|d5|2201|5|d3|aA|d5|4201|5|dD|aA|d5|8201|5|d4|aA|d5|261|5|dE|FF|"
						//"SKL|1C84|0|0|aA|d5|2201|5|d3|aA|d5|4201|5|dD|aA|d5|8201|5|d4|aA|d5|261|5|dE|FF|"
						//"SKL|1C84|0|0|aA|d5|2201|5|d3|aA|d5|4201|5|dD|aA|d5|8201|5|d4|FF|"
						//"M|SKL|1C84|0|0|aA|d5|201|5|d3|FF|SKL|1C84|0|0|aB|d5|201|5|d3|FF|SKL|1C84|0|0|aC|d5|201|5|d3|FF|"
						//"M|SKL|1C84|0|0|aA|d5|2201|5|d3|aA|d3|4201|5|d5|aA|d5|8201|5|d3|aA|d3|261|5|d5|FF|"
						//"M|SKL|1C84|0|0|aA|d5|2201|5|d3|aA|d5|4201|5|d3|aA|d5|8201|5|d3|FF|"
						
						// 身交わし、クロスカウンター（物理攻击）
					//	"SKL|1C84|0|0|aA|d5|1|5|a5|dA|1111|5|FF|"
						
						// スペシャル防御
						//"M|SKL|1C84|0|0|aA|d5|401|5|aA|d5|401|5|aA|d5|8201|5|d4|aA|d5|261|5|dE|FF|"
						//"M|SKL|1C84|0|0|aA|d5|21|5|aA|d6|401|5|aA|d6|8201|5|d6|aA|d6|261|5|dE|FF|"
						
						// ドレイン
					//	"SKL|C1C|20|a10|d1|1|1EE|FF|"
					//	"SKL|C1C|20|a10|d1|21|1E|FF|"
					//	"SKL|C1C|20|a10|d2|61|1E|FF|"
					//	"SKL|C1C|20|a10|d3|10001|1E|FF|"
					//	"SKL|C1C|20|a10|d4|10021|1E|FF|"
					//	"SKL|C1C|20|a11|d5|10061|1E|FF|"
					//	"SKL|C1C|20|a12|d6|20001|1E|FF|"
					//	"SKL|C1C|20|a12|d6|40001|1E|FF|"
						//"M|SKL|C1C|20|a10|d1|10001|1E|FF|SKL|C1C|20|a10|d1|20001|1E|FF|SKL|C1C|20|a10|d1|40001|1E|FF|"
						//"M|SKL|C1C|20|a10|d10|1|1E|FF|"
						//"M|SKL|C1C|20|a10|d1|401|1E|FF|SKL|C1C|20|a10|d1|801|1E|FF|"
						
						// 体力再生魔法
						//"SKL|1900|20|a10|d1|f0|d2|f0|FF|PSN|1|0|a10|d20|f10|PSN|0|0|a10|d20|f10|"
					//	"SKL|1900|20|a10|d1|f0|d2|f0|FF|PSN|1|0|a1|d20|f10|PSN|1|0|a2|d20|f10|"
					//	"SKL|1903|20|a10|d1|f0|d2|f0|FF|PSN|1|3|a1|d20|f10|PSN|1|3|a2|d20|f10|"
					//	"SKL|1906|20|a10|d1|f0|d2|f0|FF|PSN|1|6|a1|d20|f10|PSN|1|6|a2|d20|f10|"
					//	"SKL|1909|20|a10|d1|f0|d2|f0|FF|PSN|1|9|a1|d20|f10|PSN|1|9|a2|d20|f10|"
						//"PSN|0|a10|d20|f10|"
						//"PSN|1|a10|d20|f10|"
						//"SKL|1900|20|a10|d1|f0|d2|f0|FF|"
						
						// 毒受伤、体力再生、酔い（魔力マイナス）
						//"PSN|0|a10|d10|f0|"
						//"M|PSN|1|a10|d10|f0|"
						//"M|PSN|2|d10|f10|5|"
						
						// 状态异常発生
						//"SKL|C80|20|a10|d1|f80000|d2|f0|FF|ANR|a1|"
						//"M|SKL|C80|20|a10|d1|f80000|d2|f0|FF|PSN|0|d1|f10|5|"
						//"SKL|C80|20|a10|d1|f80000|d2|f0|FF|PSN|0|d1|f10|5|PSN|2|d1|f10|5|"
						
						// 状态异常自动回复
						//"M|ANR|0|a10|"
						
						// 状态异常回复魔法
						//"M|SKL|1A2C|20|a10|d1|f0|d2|f0|FF|"
						//"M|SKL|1A2C|20|a10|FF|"
						//"M|SKL|C80|20|a10|d1|f80000|d2|f0|FF|SKL|1A2C|20|a10|d1|f0|FF|SKL|C80|20|a10|d3|0|d2|f80000|FF|"
						
						// お目覚めフラグ
					//	"SKL|C80|20|a10|d1|f80000|d2|f0|FF|SKL|1C84|0|0|aA|d5|1|5|aA|d1|100001|5|FF|SKL|C80|20|a10|d3|0|d2|f80000|FF|"
						//"SKL|C80|20|a10|d1|f80000|d2|f0|FF|SKL|1C84|0|0|aA|d5|1|5|aA|d1|100001|5|FF|SKL|C80|20|a10|d3|0|d2|f80000|FF|"
						
						// 属性优遇
						//"SKL|1388|20|a11|"
						//"M|SKL|13EC|20|a11|"
						//"SKL|13EC|20|aA|SKL|1388|20|aB|SKL|13EC|20|aC|SKL|1388|20|aD|"
						
						// 属性反転
						//"SKL|1518|20|a10|d1|f80000|d2|f0|FF|SKL|1518|20|a10|d1|f80000|d2|f80000|FF|"
						//"SKL|1518|20|a10|FF|"
						
						// 気絶回复魔法
						//"M|SKL|1C84|0|0|a10|d5|21|5|a10|d6|1|5|a10|d7|1|5|FF|SKL|1A90|20|a10|d5|0|d6|f0|FF|SKL|1C84|0|0|a10|d6|1|5|a10|d7|1|5|FF|"
						//"M|SKL|1C84|0|0|a5|d10|1|5|a10|d5|31|5|FF|SKL|1A90|20|a10|d5|0000|100|d6|f0|0|FF|SKL|1C84|0|0|a10|d6|1|5|a10|d7|1|5|FF|SKL|1A90|20|a10|d5|80000|100|d6|f0|0|FF|SKL|1C84|0|0|a10|d6|1|5|a10|d7|1|5|FF|"
						//"M|SKL|1C84|0|0|a5|d10|1|5|a10|d5|31|5|FF|SKL|1C84|0|0|a10|d6|1|5|a10|d7|1|5|FF|"
						
						// エッジ
						//"SKL|64|20|aA|d5|1|5|FF|"
						// エッジカウンター
						//"SKL|64|20|aA|d5|1|5|a5|dA|11|10|FF|"
						//"SKL|64|0|aA|d5|2001|5|a5|dA|2011|5|aA|d5|2011|5|a5|dA|2011|5|aA|d5|2011|5|a5|dA|2011|5|FF|"
						//"SKL|64|0|aA|d5|2001|5|FF|"
						
						
						// 通常攻击、直接攻击
						//"M|SKL|2DA|0|0|aA|d5|1|5|aA|d10|1|5|a10|dA|1|5|FF|SKL|2DA|0|0|a0|dA|1|5|FF|SKL|2DA|0|0|a1|d12|1|5|FF|SKL|2DA|0|0|aC|d9|1|5|FF|"
						// 飞び道具攻击
						//"SKL|1C84|0|1|aA|d5|1|5|d10|1|5|d1|9|5|FF|SKL|1C84|0|2|a0|dD|9|5|dB|9|5|dA|21|5|dC|61|5|dE|21|5|FF|SKL|1C84|0|2|a1|d12|1|5|d11|1|5|d10|1|5|FF|"
						
						// バーストアタック
						//"SKL|12C|20|aA|d5|1|5|FF|"
					//	"SKL|12C|20|0|aA|d5|1|5|FF|"
					//	"SKL|12C|20|1|aA|d5|1|5|FF|"
					//	"SKL|12C|20|2|aA|d5|1|5|FF|"
					//	"SKL|12C|20|3|aA|d5|1|5|FF|"
						
						// バーストアタックカウンター
						//"SKL|12C|20|aA|d5|1|5|a5|dA|11|10|FF|"
						
						// 精神统一
					//	"SKL|4B0|20|aA|100|"
					//	"SKL|4B3|20|aA|100|"
					//	"SKL|4B6|20|aA|100|"
					//	"SKL|4B9|20|aA|100|"
						
						// チャット文字列チェック
						//"SKL|1C84|20|0|aA|d5|200001|5|aA|d6|3E00001|5|ken|yari|kutu|tate|aA|d7|802001|5|yumi|aA|d8|1000001|5|yoroi|aA|d9|2000201|5|kabuto|d0|FF|CHT|0|0|0|60|naruhodo|"
						
						// フォースカット
						//"SKL|258|20|aA|d5|1|5|FF|SKL|258|20|aA|d5|2001|5|FF|SKL|258|20|aA|d5|4001|5|FF|SKL|258|20|aA|d5|8001|5|FF|"
						//"M|SKL|258|20|aA|d5|1|0|FF|SKL|258|20|aA|d5|2001|0|FF|SKL|258|20|aA|d5|4001|0|FF|SKL|258|20|aA|d5|8001|0|FF|"
						// フォースカットカウンター
						//"SKL|258|20|aA|d5|1|5|a5|dA|11|10|aA|d5|11|5|FF|"
						//"SKL|258|20|a5|dA|1|5|aA|d5|11|10|FF|"
						
						// 防御ブレイク
						//"SKL|1F4|20|aA|d5|1|100|FF|SKL|1F4|20|aA|d5|1|0|FF|SKL|1F4|20|aA|d5|2001|100|FF|SKL|1F4|20|aA|d5|2001|0|FF|SKL|1F4|20|aA|d5|4001|100|FF|SKL|1F4|20|aA|d5|4001|0|FF|SKL|1F4|20|aA|d5|8001|100|FF|SKL|1F4|20|aA|d5|8001|0|FF|"
						//"SKL|1F4|20|aA|d5|1|100|FF|"
						// 防御ブレイクカウンター
						//"M|SKL|1F4|20|aA|d5|1|100|a5|dA|11|100|FF|"
						
						// 魔力消费
						//"M|SKL|64|20|aA|d5|1|5|FF|CFP|0|20|SKL|64|20|aA|d5|1|5|FF|"
						
						// 战闘チャットプロトコル
						//"M|SKL|64|20|aA|d5|1|5|FF|CHT|0|0|0|60|naruhodo|SKL|64|20|aA|d5|1|5|FF|"
						
						// スキルアップ
						//"M|SUP|0|FF|"
						
						// 盗む
						"SKL|1C20|20|w0|a0|dF|80000|0|aF|d0|11|15|FF|SKL|1C20|w0|20|a0|dF|1|0|aF|d0|11|5|FF|SKL|1C20|20|w0|a0|dF|80000|0|FF|SKL|1C20|20|w0|a0|dF|0|0|FF|" //MLHIDE
						// ペット嫌がる
						//"M|PNO|0|SKL|1C20|20|a0|dF|80000|0|aF|d0|11|15|FF|"
						//"M|PNO|0|PLV|0|"
						
						// ペット逃走
						//"PLV|7|SKL|1C20|20|a0|dF|80000|0|aF|d0|11|15|FF|"
						//"PLV|0|PLV|1|PLV|2|PLV|3|PLV|4|PLV|5|PLV|6|PLV|7|PLV|8|PLV|9|"
						//"PLV|A|PLV|B|PLV|C|PLV|D|PLV|E|PLV|F|PLV|10|PLV|11|PLV|12|PLV|13|"
						
						// 新グラフィックチェック
						//"SKL|1C84|20|0|aA|d5|802001|5|186A2|aA|d5|1|5|FF|CHT|0|0|0|60|naruhodo|CHT|0|0|0|60|naruhodo2|CHT|0|0|0|60|naruhodo3|"
						//"SKL|1C84|20|0|aA|d5|800001|5|186A2|FF|CHT|0|0|0|60|naruhodo|CHT|0|0|0|60|naruhodo2|CHT|0|0|0|60|naruhodo3|"
						
						// 飞び道具攻击（武器こわれ）
						//"M|SKL|1C84|0|3|a1|d12|800001|5|186A2|FF|SKL|1C84|0|1|aA|d5|800001|5|186A2|FF|SKL|1C84|0|2|a3|dD|800001|5|186A2|FF|"
						//"M|SKL|1C84|0|3|a1|d12|800001|5|186A2|FF|"
						//"M|SKL|1C84|0|1|a10|d5|2000001|5|FF|SKL|1C84|0|2|a0|dD|400001|5|FF|"
						
						// 风
						//"SKL|898|20|aA|d1|210001|1E|d3|21|1E|d6|21|1E|d9|200021|3949|FF|"
						// 风（ターゲットなし）
						//"M|SKL|898|20|aA|FF|"
						
						// ヒットマークチェック
					//	"SKL|0|0|aA|d5|1|5|aA|d5|1|5|aA|d5|1|5|FF|SKL|64|20|aA|d5|1|5|FF|SKL|12C|20|aA|d5|1|5|FF|SKL|258|20|aA|d5|1|5|FF|SKL|1F4|20|aA|d5|1|100|FF|SKL|20D0|20|aA|d5|1|1|FF|SKL|21FC|20|aA|d5|1|1|FF|"
						
						// 水（反射）
						//"M|SKL|AF0|20|a1|d1|1|1E|d3|10001|1E|d6|1|1E|d9|5|3949|FF|"
 						// 水（ターゲットなし）
						//"M|SKL|AF0|20|a1|FF"
						
						// 通常攻击、直接攻击（反射）
						//"SKL|1C84|0|0|a1|d12|E02001|5|186A2|FF|"
						
						// 封印
						//"|ITM|6|a0|dF|0|ITM|6|a0|dA|1|"
						
						// 静寂
						//"M|SKL|2260|20|aA|SKL|1388|20|aB|SKL|13EC|20|aC|SKL|1388|20|aD|"
						
						// 即死攻击
						//"SKL|2198|20|w0|a10|d1|f80000|d2|f0|FF|"
						//"SKL|2198|20|w0|a10|d1|f0|d2|f0|FF|"
						//"SKL|2198|20|w0|a10|d1|f0|d2|f0|FF|"
					//	"SKL|2198|20|w0|a10|d1|f0|d2|f0|FF|"
					//	"SKL|2198|20|w0|a10|d1|f0|d2|f0|FF|"
					//	"SKL|2198|20|w0|a10|d1|f0|d2|f0|FF|"
						
						// 状态异常付加攻击
						//"SKL|1D4C|20|aA|d6|f80001|5|FF|SKL|1DB0|20|aA|d0|f84001|5|FF|SKL|1E14|20|a4|dA|f82001|5|FF|SKL|1E78|20|aA|d2|f80001|5|FF|SKL|1EDC|20|aA|d3|f80001|5|FF|SKL|1F40|20|aA|d4|f80001|5|FF|"
						
						// 武器破坏攻击
						//"SKL|2134|20|aA|d5|1|0|FF|SKL|2134|20|aA|d5|800001|5|186A2|FF|"
						
						// エナジードレイン
						//"SKL|20D0|20|aA|d5|1|0|FF|SKL|20D0|20|aA|d5|1|1|FF|"
						
						// ゴールド攻击
						//"SKL|21FC|20|aA|d5|1|0|FF|SKL|21FC|20|aA|d5|1|1|FF|"
						
						// 自爆
						//"M|SKL|2008|20|aA|d0|61|100|d1|5|100|d2|1|100|d3|1|100|d4|1|100|d5|1|100|d6|1|100|d7|1|100|d8|1|100|d9|1|100|FF|"
					//	"SKL|2008|20|w0|a4|dA|1|100|dB|1|100|dC|1|100|dD|1|100|dE|1|100|dF|1|100|d10|1|100|d11|1|100|d12|1|100|d13|1|100|FF|"
						
					// ターゲットなしチェック **************
						// 地水火风ドレイン
						//"M|SKL|76C|20|aA|FF|SKL|AF0|20|aA|FF|SKL|834|20|aA|FF|SKL|898|20|aA|FF|SKL|C1C|20|aA|FF|"
						// ○状态异常
						//"M|SKL|C80|20|aA|FF|"
						// ×属性优遇
						//"M|SKL|1388|20|aA|FF|SKL|1388|20|aA|0|"
						// ○属性反転
						//"M|SKL|1518|20|aA|FF|"
						// ○反射魔法
						//"M|SKL|157C|20|aA|FF|"
						// ○回复魔法
						//"M|SKL|17D4|20|aA|FF|"
						// ○体力再生魔法
						//"M|SKL|1900|20|aA|FF|"
						// ○状态异常回复魔法
						//"M|SKL|1A2C|20|aA|FF|"
						// ○気絶回复魔法
						//"M|SKL|1A90|20|aA|FF|"
						// ○即死攻击
						//"M|SKL|2198|20|aA|FF|"
						// 静寂
						//"M|SKL|2260|20|aA|"
						
						// サクリファイス
						//"SKL|206C|20|aA|dB|1|100|dC|1|100|dD|1|100|dE|1|100|dF|1|100|d10|1|100|d11|1|100|d12|1|100|d13|1|100|FF|"
					//	"SKL|206C|20|w0|aA|dB|1|100|dC|1|100|dD|1|100|dE|1|100|dF|1|100|d10|1|100|d11|1|100|d12|1|100|d13|1|100|FF|"
						//"SKL|17D4|20|w0|aA|dB|1|100|dC|1|100|dD|1|100|dE|1|100|dF|1|100|d10|1|100|d11|1|100|d12|1|100|d13|1|100|FF|"
						
						//"SKL|206C|20|aA|FF|"
						//"SKL|1C84|0|0|a11|d0|1|9|FF|"
						
						//"SKL|a206C|fA|a5|FF|"

						
						// 吸血攻击
						//"SKL|1FA4|20|aA|d5|800201|100|50|186A2|d2|FF|"
						//"M|SKL|1FA4|20|aA|d5|201|100|50|d2|a5|dA|11|100|FF|"
						//"SKL|1FA4|20|aA|d5|2001|100|50|FF|SKL|1FA4|20|aA|d5|4001|100|50|FF|SKL|1FA4|20|aA|d5|8001|100|50|FF|"
						//"SKL|1FA4|20|aA|d5|2021|100|50|FF|"
						//"SKL|1FA4|20|aA|d5|9|100|50|FF|"
						
						// 攻击魔法４発
						//"SKL|76C|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|"
						//"SKL|7D0|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|"
						//"SKL|834|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|"
						//"SKL|898|20|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|d9|5|3949|FF|"
						
						//"CHT|0|0|0|60|naruhodo|"
						
						//"SKL|898|20|a10|d1|1|1E|d3|1|1E|d6|1|1E|FF|"
#if 0						
						// 演出调整
						// 地
					//	"SKL|76C|20|w0|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|76F|20|w0|a11|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|772|20|w0|a12|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|775|20|w0|a13|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						"SKL|775|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|775|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|775|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
						
						// 水
					//	"SKL|7D0|20|w0|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|7D3|20|w0|a11|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|7D6|20|w0|a12|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|7D9|20|w0|a13|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						"SKL|7D0|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|7D0|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|7D0|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
						"SKL|7D6|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|7D9|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|7D9|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
						
						// 火
					//	"SKL|834|20|w0|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|837|20|w0|a11|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|83A|20|w0|a12|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|83D|20|w0|a13|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						"SKL|834|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|834|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|834|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
						"SKL|83D|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|83D|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|83D|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
						
						// 风
					//	"SKL|898|20|w0|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|89B|20|w0|a11|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|89E|20|w0|a12|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
					//	"SKL|8A1|20|w0|a13|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						"SKL|898|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|898|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|898|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
						"SKL|8A1|20|w0|a10|d1|10001|1E|d3|10001|1E|d6|10001|1E|FF|"     //MLHIDE
						"SKL|8A1|20|w0|a10|d1|20001|1E|d3|20001|1E|d6|20001|1E|FF|"     //MLHIDE
						"SKL|8A1|20|w0|a10|d1|40001|1E|d3|40001|1E|d6|40001|1E|FF|"     //MLHIDE
#endif					
						
						// 攻击魔法かわし
					//	"SKL|775|20|a13|d0|9|1E|d10|9|1E|FF|"
					//	"SKL|7D9|20|a13|d0|9|1E|d10|9|1E|FF|"
					//	"SKL|83D|20|a13|d0|9|1E|d10|9|1E|FF|"
					//	"SKL|8A1|20|a13|d0|9|1E|d10|9|1E|FF|"
					//	"SKL|C1C|20|a13|d0|9|1E|d10|9|1E|FF|"
					//	"SKL|775|20|a13|d0|801|1E|d10|801|1E|FF|"
					//	"SKL|7D9|20|a13|d0|801|1E|d10|801|1E|FF|"
					//	"SKL|83D|20|a13|d0|801|1E|d10|801|1E|FF|"
					//	"SKL|8A1|20|a13|d0|801|1E|d10|801|1E|FF|"
					//	"SKL|C1C|20|a13|d0|801|1E|d10|801|1E|FF|"
						
						// ドレイン
					//	"SKL|C1C|20|w0|a13|d0|1|1E|d10|21|1E|FF|"
					//	"SKL|C1C|20|w0|a13|d10|61|1E|FF|"
					//	"SKL|C1C|20|w0|a13|d0|801|1E|FF|"
					//	"SKL|C1C|20|w0|a13|d0|801|1E|FF|"
					//	"SKL|C1C|20|w0|a13|d0|801|1E|FF|"
					//	"SKL|C1C|20|w0|a13|d0|801|1E|FF|"
					//	"SKL|C1C|20|w0|a13|d0|801|1E|FF|"
						//"SKL|C1C|20|w0|a13|d0|801|1E|d10|801|1E|FF|"
						
						//"SKL|8A1|20|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						
						// 召唤
						//"SKL|2454|20|aF|dB|マッスル|186D4|1|18B|640|100|100|5|dC|マッスル|186D4|1|18B|640|100|100|5|FF|"
						
						//"SKL|83D|20|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						//"SKL|8A1|20|a10|d0|1|1E|d1|1|1E|d2|1|1E|d3|1|1E|d4|1|1E|d5|1|1E|d6|1|1E|d7|1|1E|d8|1|1E|d9|1|1E|FF|"
						
						// 新ペット出す
						//"MON|a5|0|MON|a5|1|マッスル|186D4|1|18B|640|100|100|5|MON|a1|0|MON|a2|0|MON|a3|0|"
						
						// ＢＣデータ变更
						//"CBC|a0|18DA8|100|200|10|20|"
						
						// 暗杀
//						"SKL|2580|0|w0|a0|dA|20|0|FF|"
					//	"SKL|2580|0|w0|a0|dB|20|0|FF|"
//						"SKL|2580|0|w0|aC|d0|20|0|FF|"
//						"SKL|2580|0|w0|a0|dD|20|0|FF|"
//						"SKL|2580|0|w0|a0|dE|20|0|FF|"
//						"SKL|2580|0|w0|a0|dF|20|0|FF|"
//						"SKL|2580|0|w0|a0|d10|20|0|FF|"
//						"SKL|2580|0|w0|a0|d11|20|0|FF|"
//						"SKL|2580|0|w0|a0|d12|20|0|FF|"
//						"SKL|2580|0|w0|a0|d13|20|0|FF|"
//						"SKL|2580|0|w0|a0|d14|20|0|FF|"
						
						// 搅乱
					//	"SKL|24B8|fA|w0|a5|dA|f1|962|0|FF|"
					//	"SKL|24B8|fA|w0|a5|dB|f2001|962|1|FF|"
					//	"SKL|24B8|fA|w0|a5|dC|f4001|962|2|FF|"
					//	"SKL|24B8|fA|w0|a5|dD|f8001|962|3|FF|"
					//	"SKL|24B8|fA|w0|a5|dE|f1|962|4|FF|"
					//	"SKL|24B8|fA|w0|a5|dF|f1|962|5|FF|"
					//	"SKL|24B8|fA|w0|a5|d10|f1|962|6|FF|"
					//	"SKL|24B8|fA|w0|a5|d10|f201|962|11|6|FF|"
						
						// 地震
					//	"SKL|25ED|20|0|a4|dB|1|100|dC|1|100|dD|1|100|dE|1|100|dF|1|100|d10|1|100|d11|1|100|d12|1|100|d13|1|100|FF|"
					
					// 踊る：状态异常
					//"SKL|s2648|f37|w0|dance0|a5|dA|f80000|dB|f80000|dC|f80000|dD|f80000|FF|"
				//	"SKL|s2648|f37|w0|dance0|a0|d0|f80000|d1|f80000|d2|f80000|d3|f80000|FF|"
					// 踊る：受伤アップ
					//"SKL|s2648|f37|w0|dance6|a0|a0|a1|a2|a3|a4|a5|a6|a7|a8|a9|aA|aB|aC|aD|aE|aF|a10|a11|a12|a13|FF|"
			//		"SKL|s2648|f37|w0|dance6|a0|a0|a1|a2|a3|a4|a5|a6|a7|a8|a9|FF|"
					// 受伤ダウン
					//"PDN|0|PDN|2|PDN|3|PDN|4|PDN|5|PDN|6|"
					// 踊る：ポジションチェンジ
				//	"SKL|s2648|f37|w0|dance7|a0|a1|a2|a3|a4|FF|"
				//	"SKL|s2648|f37|w0|dance7|a0|a0|a1|a2|a3|a4|FF|"
				//	"SKL|s2648|f37|w0|dance7|a1|a0|a1|a2|a3|a4|FF|"
				//	"SKL|s2648|f37|w0|dance7|a5|a5|a1|a2|a3|a4|FF|"
				//	"SKL|s2648|f37|w0|dance7|a0|aA|aB|aC|aD|aE|FF|"
				
				//"SKL|s264E|fA|0|7|a0|A|FF|ACT|0|SKL|1C84|0|0|aF|d0|5|3|FF|bg|0|"
					
					// 踊る：ペット戾す
				//	"SKL|s2648|f37|w0|dance8|a0|a0|a1|a2|a3|a4|a5|a6|a7|a8|a9|FF|"
					//"SKL|s2648|f37|w0|dance8|a0|aA|aB|aC|aD|aE|aF|a10|a11|a12|a13|FF|"
					
					// 踊る：即死
					//"SKL|s2648|f37|w0|dance9|a0|dA|f80000|dB|f80000|dC|f80000|dD|f80000|FF|"
					//"SKL|s2648|f37|w0|dance9|a0|d0|f80000|d1|f80000|d2|f80000|d3|f80000|d4|f80000|d5|f80000|d6|f80000|d7|f80000|d8|f80000|d9|f80000|FF|"
				//	"SKL|s2648|f37|w0|dance9|aA|dA|f80000|dB|f80000|dC|f80000|dD|f80000|dE|f80000|dF|f80000|d10|f80000|d11|f80000|d12|f80000|d13|f80000|FF|"
					
					// 踊る：不発
				//	"SKL|s2648|f37|w0|danceE|a5|FF|"
					
					// 踊る：状态异状回复
				//	"SKL|s2648|f37|w0|danceA|a0|d0|f80000|d1|f80000|d2|f80000|d3|f80000|d4|f80000|d5|f80000|d6|f80000|d7|f80000|d8|f80000|d9|f80000|FF|"
					
					// 踊る：リバイブ
			//		"SKL|s2648|f37|w0|danceB|a0|d1|f80000|100|d2|f80000|100|d3|f80000|100|d4|f80000|100|d5|f80000|100|d6|f80000|100|d7|f80000|100|d8|f80000|100|d9|f80000|100|FF|"
				//	"SKL|s2648|f37|w0|danceB|a0|dA|f80000|100|dB|f80000|100|dC|f80000|100|dD|f80000|100|dE|f80000|100|dF|f80000|100|d10|f80000|100|d11|f80000|100|d12|f80000|100|d13|f80000|100|FF|"
					
					// 踊る：自爆
			//		"SKL|s2648|f37|w0|danceC|a1|a1|dA|f61|100|dB|f1|100|dC|f1|100|dD|f1|100|dE|f1|100|dF|f1|100|d10|f1|100|d11|f1|100|d12|f1|100|d13|f1|100|FF|"
					//"SKL|s2648|f37|w0|danceC|a1|aA|d0|f21|100|d1|f1|100|d2|f1|100|d3|f1|100|d4|f1|100|d5|f1|100|d6|f1|100|d7|f1|100|d8|f1|100|d9|f1|100|FF|"
					//"SKL|s2648|f37|w0|danceC|a1|FF|"
					
					// 踊る：サクリファイス
				//	"SKL|s2648|f37|w0|danceD|a0|a1|d0|f1|100|d2|f1|100|d3|f1|100|d4|f1|100|d5|f1|100|d6|f1|100|d7|f1|100|d8|f1|100|d9|f1|100|FF|"
				//	"SKL|s2648|f37|w0|danceD|aA|aB|dA|f1|100|dB|f1|100|dC|f1|100|dD|f1|100|dE|f1|100|dF|f1|100|d10|f1|100|d11|f1|100|d12|f1|100|d13|f1|100|FF|"
				//	"SKL|s2648|f37|w0|danceD|a0|a1|d0|f1|100|d2|f1|100|d3|f1|100|d4|f1|100|d5|f1|100|d6|f1|100|d7|f1|100|d8|f1|100|d9|f1|100|FF|"
				//	"SKL|s2648|f37|w0|danceD|aA|aB|FF|"
				
				
				// パラメータアップダウン
//				"SKL|26AC|f20|w0|a10|d1|f80000|d2|f0|FF|"
//				"PSN|0|d1|f10|5|PSN|2|d1|f10|5|"
				// パラメータアップダウン自动回复
//				"PUE|1|"
				// パラメータアップダウン
//				"SKL|2719|f20|w0|a0|dA|f80000|dF|f80000|FF|"
//				"SKL|24B8|fA|w0|a0|dA|f1|962|0|FF|"
				//
				//"SKL|s28A9|fA|0|a0|1|f80000|FF|"

				
				//"SKL|26AC|f20|w0|a10|d1|f80000|d2|f0|FF|PSN|0|d1|f10|5|PSN|2|d1|f10|5|"
				
					
// バグ再现 *****************************************************************************

						//"M|SKL|s2C|a64|0|dF|1|15|d10|1|18|dA|1|1|dB|1|0|dC|1|1|d12|1|0|d13|1|0|dE|8|0|FF|MON|mA|0|MON|mA|1|ゴブリン||18DA8|A|107|107|3E8|0|MON|m13|0|MON|m13|1|スライム||18C7C|1|6E|6E|3E8|0|HIT|a4|dA|21|D9|FF|MON|m1|0|MON|m1|1|ネコマタ||18A8C|1|48|48|0|0|MON|m3|0|MON|m3|1|ケットシー||18A89|1|46|46|3E8|0|MON|m12|0|MON|m12|1|ゾンビ||18AEC|1|65|65|3E8|0|HIT|a10|d1|8|0|FF|HIT|aC|d4|8|0|a4|dC|f32|F2|FF|HIT|a2|d12|1|7|FF|"

// 回复魔法だれも回复しないとき
//"M|ESC|et5|f1|HIT|a9|d11|21|30|FF|HIT|a8|dB|1|4|FF|HIT|a6|dB|1|6|FF|SKL|a6D|f3C|a4|0|FF|SUP|4|FF|HIT|aE|d3|1|B|FF|HIT|aC|d1|1|B|FF|CMB|dD|a7|f1|d1|a2|f1|d1B|FF|HIT|aD|d9|1|0|FF|SKL|s6|23|a1|dB|f21|37|a1|dA|f21|40|a1|dD|f21|47|FF|SUP|1|FF|HIT|aF|d8|1|E|FF|"

// カウンターおかしいバグ
//"M|HIT|a5|dA|1|2A|FF|HIT|a0|dA|1|3A|FF|HIT|aA|d0|1|0|a0|dA|f31|23|FF|CLR|FF|"

//"M|ESC|e0|f0|SKL|2DA|0|0|a10|d0|1|0|FF|SKL|2DA|0|0|aA|d0|1|1|FF|SKL|2DA|0|0|aE|d5|1|1|FF|SKL|2DA|0|0|aC|d0|8|0|FF|SKL|2DA|0|0|aB|d0|1|1|FF|SKL|2DA|0|0|aD|d0|1|1|FF|SKL|2DA|0|0|aF|d0|1|0|FF|SKL|2DA|0|0|a5|dB|1|2C|FF|"

// 反射死亡バグ
//"M|SKL|s226|f64|a0|0|f0|FF|bg|0|SKL|2DA|0|0|aA|d0|2063|7|FF|END|"

// 自分に魔法反射した时
//"M|SKL|aBE|f3C|a5|5|f10021|dA0|6|f10021|dA0|FF|"
//"M|SKL|aBE|f3C|a5|5|f10021|dA0|FF|"

// 谜の止まり
//"M|CMB|d0|aA|f1|d1|aB|f1|d1|FF|SKL|a122|f3C|a0|800AAA1|f10021|d75|B|f10021|d6E|FF|"
//"M|CMB|d0|aA|f1|d1|aB|f1|d1|FF|SKL|a122|f3C|a0|dA|f10021|d75|B|f10021|d6E|FF|"

// 护卫のカウンター
//"M|SKL|1C84|0|0|a0|d5|1|3B|FF|SKL|1C84|0|0|aA|d0|201|14|a0|dA|f1|18|FF|"

//"M|SKL|aA31|f64|a0|FF|CMB|d0|aB|f5|d1|aA|f5|d1|FF|bg|0|"
//"M|SKL|aBC1|f64|a0|FF|CMB|d0|aA|f5|d1|aB|f5|d2|aE|f5|d1|aD|f5|d1|aC|f5|d3|FF|bg|0|"

//"M|bg|5|CMB|d5|aB|f5|d1|aA|f5|d4|FF|ANR|aD|ACT|C|SKL|1C84|0|0|aC|d5|5|3|FF|bg|5|"

// 魔法となえたら落ちた
//"M|SKL|aAF9|f64|a1|A|f21|d21|E|f1|d1A|F|f21|d21|FF|ACT|d1|SKL|aB54|fA|a7|E|f1|d5|FF|SKL|aC1D|f32|a0|FF|ACT|d0|SKL|1C84|0|w3|a3|dE|61|61B|FF|END|ACT|d3|"

//"M|SKL|1C84|0|0|a0|d13|1|3E|FF|POS|13|POS|B|SKL|a1841|fA|a5|F|f10000|d6E|A|f0|d75|10|f0|d77|11|f0|d77|FF|POS|A|POS|A|POS|B|POS|11|"

//"M|SKL|1C84|0|0|a10|d0|61|3E|FF|POS|5|POS|11|POS|12|POS|13|"

// ペット交换でバグ
//"M|POS|F|SKL|1C84|0|0|a5|d0|1|5|FF|SKL|aC1C|f14|a0|A|f10001|d0|FF|"
//"M|SKL|aC1C|f14|a0|A|f10001|d1|FF|"

//"M|SKL|1C84|0|0|a5|d10|62|B5|FF|POS|C|POS|B|POS|A|POS|A|POS|D|POS|E|SKL|s199|f64|a0|dF|61|3D8|d10|61|3D8|d11|61|299|dA|61|277|d12|62|235|d13|62|319|FF|CLR|FF|"
//"M|SKL|1C84|0|0|a5|d10|62|B5|FF|POS|C|POS|B|POS|A|POS|A|POS|D|POS|E|SKL|s199|f64|a0|dF|61|3D8|d10|61|3D8|d11|61|299|dA|61|277|d12|62|235|d13|62|319|FF|CLR|FF|"


// 气孔弹かも
//"C|0|0|あんきも|186A2|5|190|190|82|82|405|1|シン|186D5|5|168|168|168|168|405|2|ザンギ|1871F|8|14C|14C|14C|14C|5|3|aaaaaaaa|18800|14|1|140|32|32|405|7|プッチバト|18B78|A|C1|C1|F0|F0|9|A|クラッシャー|18DBF|15|1C5|1C5|159|159|1|B|ゴブリン|18DA8|19|233|233|150|150|1|"
//"M|SKL|1C84|0|0|aA|d7|1|60|FF|SKL|1C84|0|0|aB|d0|1|97|FF|SKL|1C84|0|0|a1|dB|8|0|aB|d1|f11|54|FF|ACT|1|SKL|1C84|0|w2|a0|dB|1|25|dA|1|2B|FF|ACT|0|PNO|a7|SKL|1C84|0|0|a7|dA|8|0|FF|SKL|s199|f64|a2|dB|1|6|dA|8|0|aA|d2|f11|5|FF|SKL|1C84|0|0|a3|dB|8|"
//"M|SKL|s199|f64|a2|dB|1|6|dA|8|0|aA|d2|f11|5|FF|"

// 片侧回复の魔法反射 *****************************************************************************

//"M|POS|10|SKL|a189C|fA|a0|0|f0|d20|1|f0|d25|2|f10000|d24|FF|ACT|0|PSN|1|aF|f0|d4|PNO|aF|PSN|1|aA|f0|d12|CMB|d0|aF|f5|d1|aA|f5|d1|FF|SKL|aC21|f78|a2|10|f1|d2E|FF|ACT|2|ANR|a1|SKL|s1F4|32|a1|d10|f1|0|FF|ACT|1|PSN|1|aC|f0|d10|SKL|a775|f64|aC|2|f1|d50|FF|PNO|a11|SKL|s12C|32|a11|d1|f1|C|FF|PSN|1|a10|f0|d5|PNO|a10|SKL|1C84|0|0|a10|d0|5|1|FF|SKL|1C84|0|0|a1|d10|200021|7D|FF|CHT|14|0|0|60|火熊は怪我をした。|bg|0|SKL|1C84|0|0|a2|dB|200021|38|FF|CHT|14|0|0|60|ショックは怪我をした。|"

// 精神统一バグ
//"M|PNO|a5|SKL|1C84|0|0|a5|dB|1|12|FF|PSN|1|a1|f0|d10|PSN|1|a6|f0|dD|PNO|a6|SKL|a4B1|f2D|a6|PNO|aB|SKL|1C84|0|0|aB|d5|1|19|FF|PSN|1|a0|f0|d12|PSN|1|a8|f0|d12|SKL|a1A35|f5|a8|0|f80000|FF|SKL|s1068|f32|a10|6|f80000|1|f0|8|f0|5|f0|FF|PNO|a3|SKL|1C84|0|0|a3|d10|1|5|FF|"

//"M|ACT|0|SKL|1C84|0|0|a10|d0|8|0|FF|SKL|1C84|0|0|aA|d0|8|0|FF|SKL|aA28|fA|aC|0|f1|d5|FF|POS|13|"
//"C|0|0|ピース|187CE|8|5A|71|65|83|404|A|クラッシャー|18DBF|1|5A|5A|48|48|0|10|クラッシャー|18DBF|1|5C|5C|4A|4A|0|C|クラッシャー|18DBF|1|57|57|45|45|0|E|クラッシャー|18DBF|1|5A|5A|48|48|0|"

//"M|SKL|1C84|0|0|aC|d5|1|B|FF|SKL|1C84|0|0|aA|d5|1|A|FF|SKL|s1068|f32|a5|A|f80000|C|f0|FF|ACT|5|SKL|1C84|0|0|a10|d5|1|B|FF|SKL|1C84|0|w3|a5|d10|1|43|FF|"

//"M|SKL|1C84|0|0|aF|d5|1|17|a5|dF|f800011|5C|18832|FF|CHT|14|0|0|0|しょっく\Sの\SC率ＵＰの剣\Sが壊れた。|"
//"M|SKL|1C84|0|0|aF|d5|1|17|a5|dF|f11|5C|FF|CHT|14|0|0|0|しょっく\Sの\SC率ＵＰの剣\Sが壊れた。|"

//"M|SKL|a20D2|fA|a5|d0|f1|0|5|1|f1|0|FF|"

// 怪我をした时だけなぜかチャットを待っている。
//"M|SKL|1C84|0|w1|a5|d1|200021|5A|FF|CHT|14|0|0|60|天下一品は怪我をした。|ACT|5|SKL|1C84|0|0|aA|d5|8|0|FF|"

// ポジションチェンジした人に攻击したらＩＤおらんえらー
//"M|POS|F|SKL|1C84|0|0|aF|d5|6|1|FF|bg|5|SKL|1C84|0|0|a0|dF|1|16|FF|"

// 合体攻击
//"CMB|dF|a0|f1|d17|a1|f2001|d1A|a2|f1|d15|a3|f1|d19|FF|"
//"SKL|1C84|0|0|aD|dB|21|1B|FF|bg|0|ACT|0|CMB|dF|a13|f1|d17|aA|f1|d1A|a12|f1|d15|FF|bg|0|"

// 亲方K倩渐啸?
//"SKL|s2455|fA|aA|12|子分|a18DBF|2|68|68|54|54|8|FF|ACT|A|bg|f|SKL|1C84|0|0|aC|d5|1|B|FF|SKL|a9CD|fA|a5|10|f1|d46|B|f21|d46|12|f1|d2D|F|f1|d3A|FF|SKL|1C84|0|0|a10|d0|1|1C|FF|SKL|1C84|0|0|a13|d0|1|16|FF|PNO|a0|bg|0|CMB|d0|aD|f5|d2|a11|f25|d4|FF|CHT|14|4|0|60|トレントは怪我をした。|SKL|1C84|0|0|aE|d5|1|C|FF|SKL|s2455|fA|aA|B|子分|a18DBF|1|5A|5A|4A|4A|8|FF|"

// 魔方阵でおかしくなる
//"CFP|aF|dA|bg|5|SKL|1C84|0|0|aF|d5|5|1|FF|SKL|a2260|fA|a0|0|SKL|1C84|0|0|aA|d5|5|5|FF|"

// ポジションチェンジした人に攻击したらＩＤおらんえらー
//"SKL|C80|20|a10|dF|f80000|d2|f0|FF|ANR|a1|POS|F|SKL|1C84|0|0|aF|d5|6|1|FF|bg|5|SKL|1C84|0|0|a0|dF|1|16|FF|"
//"SKL|1C84|0|0|aE|d5|8|0|a5|dE|f71|20C|FF|SKL|1C84|0|0|a5|dF|61|1B8|FF|SKL|1C84|0|0|a11|d0|5|2|FF|bg|0|" 
//"SKL|1C84|0|0|a5|dA|61|164|FF|END|"

//"SKL|a76C|fA|a0|5|fA10001|d6D|187CE|FF|CHT|14|4|3|60|こってり子は怪我をした。|CHT|14|4|3|0|こってり子のスモールソードが壊れた。|"

// 状态异常発生
//"SKL|CE4|20|a0|d10|f80000|d2|f0|FF|"
//"CMB|d10|a0|f100001|d11|a1|f1|d1B|FF|"
//"CMB|d10|a0|f100001|d11|a1|f100001|d1B|FF|"
//"CMB|d10|a0|f100001|d11|a1|f1|d1B|FF|"

//"SKL|1C84|0|0|aA|d0|65|7DD|FF|ACT|A|SKL|1C84|0|0|aA|d1|5|1|FF|bg|1|ACT|1|bg|1|"

//"SKL|s352|0|aA|d0|f1|39|FF|ACT|A|SKL|1C84|0|0|a10|d0|1|C|FF|SKL|aA8F|fA0|aA|0|f1|d26|FF|"
//"CMB|dA|a8|f1|d75|a0|f1|d1F|a3|f1|d3A|a1|f1|dB0|a6|f1|d5A|a5|f1|d61|a2|f61|d150|FF|ACT|2|CHT|A|0|0|0|ゴブリン:\Sガッカリ…|"
//"CMB|dA|a8|f1|d75|a0|f1|d1F|a3|f1|d3A|a1|f1|dB0|a6|f1|d5A|a5|f1|d61|a2|f61|d150|FF|ACT|2|CHT|A|0|0|0|ゴブリン:ガッカリ…|"

//"SKL|a835|fA|a1|A|f1|dB8|FF|SKL|1C84|0|0|a13|d3|1|4E|FF|SKL|s83|14|aF|d5|f200021|8B|FF|CHT|14|4|3|60|天野聡子?UBR?は怪我をした。|CHT|14|4|3|60|天野聡子?UBR?は魂を失った！|CBC|5|187A9|0|141|7E|7E|SKL|1C84|0|0|aC|d3|1|4C|FF|SKL|1C84|0|0|a12|d2|5|1D|FF|SKL|1C84|0|0|a11|d9|1|41|FF|SKL|s213|A|aA|d2|f200065|1CA|FF|CHT|14|4|3|60|レイシャは怪我をした。|ACT|A|SKL|1C84|0|0|aA|d1|200061|146|FF|CHT|14|4|3|60|天野美汐は怪我をした。|SKL|1C84|0|0|aE|d9|200021|48|FF|CHT|14|4|3|60|ハートは怪我をした。|SKL|1C84|0|0|a3|dA|1|4E|FF|ACT|3|CFP|a3|d12|SKL|1C84|0|0|a10|d3|1|46|FF|SKL|1C84|0|0|aB|d3|100|0|FF|SKL|1C84|0|0|a3|dA|1|60|FF|SKL|1C84|0|0|aD|d3|21|4D|FF|END|"

// チャットが流れる
//"SKL|1C84|0|0|a13|d1|1|23|FF|SKL|s83|14|aE|d4|f5|10|FF|SKL|1C84|0|0|a12|d3|21|4B|FF|SKL|s1|1E|a1|d11|f21|78|a1|dA|f1|E7|aA|d1|f11|1E|FF|CHT|11|0|0|0|熊殺しのオズナグ:\Sちっ！油断したか…。|ACT|1|SKL|1C84|0|0|aA|d0|5|1|FF|SKL|1C84|0|0|aD|d1|5|3|FF|SKL|1C84|0|0|aF|d0|5|21|FF|bg|4|ACT|4|SKL|1C84|0|0|aB|d4|1|2C|FF|SKL|s83|14|aC|d4|f1|30|FF|SKL|1C84|0|0|a10|d4|21|30|FF|bg|0|ACT|0|bg|1|SKL|aA29|f14|a0|C|f21|d9D|F|f21|d9C|13|f21|dA4|FF|"
//"SKL|1C84|0|0|a13|d1|1|23|FF|SKL|s83|14|aE|d4|f5|10|FF|SKL|1C84|0|0|a12|d3|21|4B|FF|SKL|s1|1E|a1|d11|f21|78|a1|dA|f1|E7|aA|d1|f11|1E|FF|CHT|11|0|0|0|ohta|ACT|1|SKL|1C84|0|0|aA|d0|5|1|FF|SKL|1C84|0|0|aD|d1|5|3|FF|SKL|1C84|0|0|aF|d0|5|21|FF|bg|4|ACT|4|SKL|1C84|0|0|aB|d4|1|2C|FF|SKL|s83|14|aC|d4|f1|30|FF|SKL|1C84|0|0|a10|d4|21|30|FF|bg|0|ACT|0|bg|1|SKL|aA29|f14|a0|C|f21|d9D|F|f21|d9C|13|f21|dA4|FF|"

// ＰＣが飞ばされて、ペットが逃げて、また戾ってくる
//"ESC|e1|f0|ESC|e8|f1|ESC|e0|f0|SKL|1C84|0|0|aD|d9|21|C7|FF|SKL|s83|14|a11|d5|f5|1|FF|SKL|1C84|0|0|a13|d4|1|7A|FF|SKL|1C84|0|0|aB|d2|1|5F|FF|SKL|1C84|0|0|aC|d6|1|9F|FF|SKL|1C84|0|0|aF|d0|200061|159|FF|CHT|14|4|3|60|エルシィスは怪我をした。|ACT|F|SKL|1C84|0|0|aE|d6|21|C9|FF|SKL|1C84|0|0|aA|d2|1|58|FF|SKL|1C84|0|0|a12|d4|200021|7B|FF|CHT|14|4|3|60|ちゃるは怪我をした。|SKL|1C84|0|0|a10|d2|200021|6B|FF|CHT|14|4|3|60|りっきーは怪我をした。|SKL|s12F|28|aF|d1|f200061|258|FF|CHT|14|4|3|60|denは怪我をした。|"
//"CMB|d0|a13|f1|d1|aA|f1|d4|a11|f1|d7|FF|bg|f|SKL|s199|f32|a0|dC|8|0|dF|65|73|d13|61|490|dA|61|43A|d11|61|28F|dB|61|375|dC|8|0|FF|ACT|0|SKL|s1F4|5|aC|d0|f5|7D|FF|ACT|C|bg|0|SKL|s1F4|5|aC|d0|f5|7D|FF|"
//"CMB|d0|a13|f1|d1|aA|f1|d4|a11|f1|d7|FF|bg|f|SKL|s199|f32|a0|dF|65|73|d13|61|490|dA|61|43A|d11|61|28F|dB|61|375|dC|8|0|FF|ACT|0|SKL|s1F4|5|aC|d0|f5|7D|FF|ACT|C|bg|0|SKL|s1F4|5|aC|d0|f5|7D|FF|"
//"SKL|1C84|0|w0|a0|d12|8001|10|FF|"
//"SKL|12C|0|w3|a0|d12|2001|0|FF|"

//"SKL|1C84|0|0|aA|d0|1|54|FF|ACT|A|SKL|s1DD0|1E|aD|d0|f1|10|FF|SKL|aA2A|f1E|a0|A|f1|d4B|F|f1|d86|C|f1|d81|FF|ACT|0|CHT|A|5|3|30|長老トレント:\S水よ…|SKL|aAF2|f78|aA|0|f1|d5A|FF|SKL|s65|14|aC|d0|f5|13|FF|bg|0|SKL|1C84|0|0|aF|d0|5|D|FF|SKL|1C84|0|0|a10|d0|5|3|FF|"
//"SKL|1C84|0|0|aA|d0|1|54|FF|ACT|A|SKL|s1DD0|1E|aD|d0|f1|10|FF|SKL|aA2A|f1E|a0|A|f1|d4B|F|f1|d86|C|f1|d81|FF|ACT|0|CHT|A|5|3|30|長老トレント:水よ…|SKL|aAF2|f78|aA|0|f1|d5A|FF|SKL|s65|14|aC|d0|f5|13|FF|bg|0|SKL|1C84|0|0|aF|d0|5|D|FF|SKL|1C84|0|0|a10|d0|5|3|FF|"

// 长老召唤
//"SKL|1C84|0|0|aE|d0|5|C|FF|SKL|1C84|0|0|aD|d0|5|2|FF|bg|0|ACT|0|SKL|s2455|fA|aA|B|ファーンウィドー|a18BD2|1A|1EC|1EC|1C6|1C6|8|C|ファーンウィドー|a18BD2|19|1E7|1E7|1C7|1C7|8|FF|ACT|A|SKL|aA31|f64|a0|A|f21|d169|B|f21|d1FC|C|f21|d206|FF|CHT|A|5|4|0|"
//"|bg|a|SKL|1C84|0|w2|a5|d10|1|3A|dF|2|4F|FF|ACT|5|SKL|1C84|0|0|aC|d5|8|0|FF|SKL|1C84|0|w2|a5|d10|1|80|dF|21|93|FF|SKL|1C84|0|0|a10|d5|8|0|FF|"
//"|SKL|s251C|fA|w3|a5|dF|1|46|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|56|dF|1|46|dF|21|56|FF|"
//"|SKL|s251C|fA|w3|a0|dF|8|0|dF|8|0|dF|1|3D|dF|8|0|dF|1|43|dF|1|4A|FF|ACT|0|bg|0|"
//"|SKL|s251C|fA|w3|a0|dF|1|3D|dF|8|0|dF|1|43|dF|1|4A|FF|ACT|0|bg|0|"

//"CFP|a5|d14|bg|0|SKL|s20D0|fA|w0|aF|d0|f200|0|5|FF|CHT|14|4|3|0|ワータイガーは何も影響をうけなかった|ACT|F|SKL|s20D0|fA|w0|aF|d0|f200|0|5|FF|CHT|14|4|3|0|ワータイガーは何も影響をうけなかった|"
//"CFP|a5|d14|bg|0|SKL|s20D0|fA|w0|aF|d0|f200|0|5|FF|CHT|14|4|3|0|ワータイガーは何も影響をうけなかった|ACT|F|SKL|s20D0|fA|w0|aF|d0|f200|0|5|FF|CHT|14|4|3|0|ワータイガーは何も影響をうけなかった|"

//"|SKL|s20D8|fA|w0|aE|d0|f201|34F6|5|FF|CHT|14|4|3|0|ワータイガーは13558Exp奪われた。|ACT|E|SKL|1C84|0|0|a12|d5|1|14|a5|d12|f11|14|FF|SKL|s20D8|fA|w0|aE|d0|f200|0|5|FF|CHT|14|4|3|0|ワータイガーは何も影響をうけなかった|SKL|1C84|0|0|aD|d5|1|15|FF|"

//"|SKL|s199|f32|w0|a0|dA|1|BB|dB|5|CE|dC|1|277|dF|1|121|dE|5|D2|dD|5|4B|FF|ACT|0|SKL|s11FC|fC8|0|aA|0|f0|FF|ACT|A|CHT|F|6|5|30|最終決戦型牛鬼:\Sコザカシイ……！！|SKL|s216|f19|w0|aF|d0|f1|0|aF|d0|f1|0|aF|d0|f1|0|FF|ACT|F|SKL|s1E98|f1E|w0|aB|d0|f1|33|FF|SKL|s1E98|f1E|w0|aC|d0|f8|0|FF|SKL|s1E98|f1E|w0|aE|d0|f8|0|FF|SKL|s1E98|f1E|w0|aD|d0|f8|0|FF|SKL|1C84|0|0|a0|dA|401|25|FF|CHT|A|0|1|30|闇医者アルバス:\S痛くもかゆくもないわ！|CHT|F|6|5|30|最終決戦型牛鬼:\Sミ?ミナゴロシ?ダ……ッ！|SKL|s14E|f32|w0|aF|d0|f1|15C|FF|"

//"|CHT|F|6|5|30|最終決戦型牛鬼:\Sク?クラエ…！！|SKL|s2135|fA|0|aF|d0|f2000001|A|a0|dF|f11|7C|FF|CHT|14|4|3|0|名声６００ちぇく\Sの\S地のクリスタル\Sが破壊された。|CBC|F|186BB|8C9|C30|848|910|ACT|F|SKL|s135|f64|w0|a0|dF|f1|12C|FF|ACT|0|PSN|1|t4|aA|f0|d122|SKL|s11FC|fC8|0|aA|0|f80000|FF|ACT|A|CFP|aA|d28|PSN|1|t4|aB|f0|d3F|SKL|1C84|0|0|aB|d0|1|14|FF|PSN|1|t4|aD|f0|d42|SKL|s1FC4|f32|w0|aD|d0|f1|C|b6|FF|SKL|1C84|0|0|aC|d0|1|12|FF|SKL|s84|f1E|w0|aE|d0|f1|F|FF|CHT|F|6|5|30|最終決戦型牛鬼:\Sク?クラエ…！！|SKL|s2135|fA|0|aF|d0|f1|0|FF|PSN|1|t4|aA|f0|d122|CHT|A|0|1|30|闇医者アルバス:\S痛くもかゆくもないわ！|"

// 気絶してすぐにリヴァイブバグ
//"SKL|1C84|0|0|aC|d5|200021|10|FF|SKL|a1A91|f14|0|a0|5|f80000|84|FF|"

//"SKL|1C84|0|0|a4|d5|200021|10|FF|SKL|a1A91|f14|0|a0|5|f80000|84|FF|"
//"SKL|1C84|0|0|aA|d3|200021|10|FF|SKL|a1A91|f14|0|a0|3|f80000|84|FF|"
//"SKL|1C84|0|0|aA|d3|200021|10|FF|"
//"SKL|1C84|0|0|a5|dF|1|10|aF|d5|200031|10|FF|SKL|a1A91|f14|0|a0|5|f80000|84|FF|"
//"SKL|a1A91|f14|0|a0|3|f80000|84|FF|"
// 防御ブレイク反射吸收无效
//"|SKL|s1FD|f32|w0|a0|d5|f4001|0|FF|bg|a|ACT|A|SKL|1C84|0|0|a5|dA|5|3|FF|bg|a|"
//"|SKL|s1FD|f32|w0|a0|d5|f1|0|FF|bg|a|ACT|A|SKL|1C84|0|0|a5|dA|5|3|FF|bg|a|"

//"|bg|a|ACT|A|SKL|1C84|0|w3|a0|dA|21|26A|FF|ACT|0|ESC|e0|f1|ESC|e2|f1|SKL|s251D|f14|w3|a1|dB|5|18|dB|5|18|dB|25|A|FF|"

// オズニック逃げてバグ
//"|CFP|aA|d32|ESC|e0|f0|ACT|0|ESC|e0|f0|SKL|1C84|0|0|aA|d0|2|1E4|FF|ACT|A|SKL|1C84|0|0|aB|d0|1|1B|FF|bg|f|SKL|1C84|0|0|a10|d0|1|17|FF|SKL|1C84|0|0|a11|d0|1|19|FF|SKL|s195|f1E|w0|aA|FF|"
//"|SKL|s195|f1E|w0|aA|FF|"

// ホテルの骸骨、护卫吸收
//"CFP|a5|d7|bg|a|SKL|1C84|0|0|aF|d0|4201|162|5|FF|SKL|s1647|f64|0|a0|5|f0|FF|"

// 属性反転ボスでＩＤおらん
//"SKL|s68|f32|w0|aD|d2|f1|90|FF|SKL|s131|f3C|w0|aA|d3|f5|17B|FF|ACT|A|bg|3|ACT|3|SKL|1C84|0|0|a5|dA|1|85|FF|SKL|s68|f32|w0|aF|d3|f8|0|FF|SKL|s2520|f19|w3|a1|d10|1|8D|dF|1|C2|d13|1|59|dB|1|5D|d10|1|54|d13|1|35|dC|5|26|FF|bg|c|SKL|s68|f32|w0|aE|d6|f1|83|FF|bg|0|SKL|a8FF|f50|0|a13|6|f1|d43|1|f1|dEB|5|f1|d76|FF|PNO|a9|bg|9|SKL|1C84|0|0|a12|d5|1|57|FF|SKL|s194|fC|w0|a2|dD|21|12B|dF|1|100|d10|1|A0|FF|ACT|2|SKL|1C84|0|0|aB|d6|1|84|FF|SKL|a91C|f3C|0|a6|10|f1|d66|B|f1|d72|12|f1|d6F|F|f1|d6F|FF|SKL|1C84|0|0|a10|d2|1|44|FF|SKL|1C84|0|0|a11|d3|8|0|FF|SKL|a76E|fF|0|a4|10|f21|dF0|FF|FF|SKL|a189E|f48|0|a3|0|f0|d42|1|f0|d5B|2|f0|d67|3|f0|d4F|4|f0|d4F|5|f0|d42|6|f0|d44|9|f0|d54|FF|SKL|1C84|0|0|a2|d11|1|63|FF|"
//"SKL|s68|f32|w0|aD|d2|f1|90|FF|SKL|s131|f3C|w0|aA|d3|f5|17B|FF|ACT|A|bg|3|ACT|3|SKL|1C84|0|0|a5|dA|1|85|FF|SKL|s68|f32|w0|aF|d3|f8|0|FF|SKL|s2520|f19|w3|a1|d10|1|8D|dF|1|C2|d13|1|59|dB|1|5D|d10|1|54|d13|1|35|dC|5|26|FF|bg|c|SKL|s68|f32|w0|aE|d6|f1|83|FF|bg|0|SKL|a8FF|f50|0|a13|6|f1|d43|1|f1|dEB|5|f1|d76|FF|PNO|a9|bg|9|SKL|1C84|0|0|a12|d5|1|57|FF|SKL|s194|fC|w0|a2|dD|21|12B|dF|1|100|d10|1|A0|FF|ACT|2|SKL|1C84|0|0|aB|d6|1|84|FF|SKL|a91C|f3C|0|a6|10|f1|d66|B|f1|d72|12|f1|d6F|F|f1|d6F|FF|SKL|1C84|0|0|a10|d2|1|44|FF|SKL|1C84|0|0|a11|d3|8|0|FF|SKL|a76E|fF|0|a4|10|f21|dF0|FF|SKL|a189E|f48|0|a3|0|f0|d42|1|f0|d5B|2|f0|d67|3|f0|d4F|4|f0|d4F|5|f0|d42|6|f0|d44|9|f0|d54|FF|SKL|1C84|0|0|a2|d11|1|63|FF|"
//"SKL|a76E|fF|0|a4|10|f21|dF0|FF|FF|SKL|a189E|f48|0|a3|0|f0|d42|1|f0|d5B|2|f0|d67|3|f0|d4F|4|f0|d4F|5|f0|d42|6|f0|d44|9|f0|d54|FF|SKL|1C84|0|0|a2|d11|1|63|FF|"
//"SKL|a76E|fF|0|a4|10|f21|dF0|FF|SKL|a189E|f48|0|a3|0|f0|d42|1|f0|d5B|2|f0|d67|3|f0|d4F|4|f0|d4F|5|f0|d42|6|f0|d44|9|f0|d54|FF|SKL|1C84|0|0|a2|d11|1|63|FF|"
//"SKL|s1FC4|f23|w0|a5|d0|f1|A4|b52|FF|SKL|s2|f10|w0|a7|dA|f8|0|a7|dA|f1|2D|FF|SKL|s2455|fA|0|aE|B|パイレイト|a18DBE|2D|314|314|2E8|2E8|1|D|パイレイト|a18DBE|2D|35B|35B|2C2|2C2|1|FF|CHT|A|0|0|30|オカシラ:\Sほうれ！ちうちうちうっ！|SKL|s1FC6|f50|w0|aA|d1|f1|E9|b74|FF|ACT|A|SKL|s131|f3C|w3|a2|dA|1|EB|FF|SKL|s2520|f19|w3|a4|dC|1|62|d11|21|91|dA|1|37|d12|5|11|dC|21|35|d12|5|11|dE|21|B1|FF|ACT|4|bg|12|SKL|a18A1|f90|0|a0|0|f0|d8F|1|f0|d94|2|f0|dAC|3|f0|dC9|4|f0|dD6|5|f0|d96|6|f0|dBC|7|f0|dA5|FF|SKL|s214|fF|w0|a6|dA|f1|0|FF|SKL|s194|fC|w0|a3|dA|8|0|dD|1|D8|d12|25|56|FF|ACT|3|SKL|a771|f1E|0|a1|A|f1|d1C4|FF|CHT|A|0|0|30|オカシラ:\Sいくべかね|FF|SKL|1C84|0|w3|a4|dD|1|16C|FF|SKL|1C84|0|0|a3|dD|8|0|FF|"
//"SKL|s1FC4|f23|w0|a5|d0|f1|A4|b52|FF|SKL|s2|f10|w0|a7|dA|f8|0|a7|dA|f1|2D|FF|SKL|s2455|fA|0|aE|B|パイレイト|a18DBE|2D|314|314|2E8|2E8|1|D|パイレイト|a18DBE|2D|35B|35B|2C2|2C2|1|FF|CHT|A|0|0|30|オカシラ:\Sほうれ！ちうちうちうっ！|SKL|s1FC6|f50|w0|aA|d1|f1|E9|b74|FF|ACT|A|SKL|s131|f3C|w3|a2|dA|1|EB|FF|SKL|s2520|f19|w3|a4|dC|1|62|d11|21|91|dA|1|37|d12|5|11|dC|21|35|d12|5|11|dE|21|B1|FF|ACT|4|bg|12|SKL|a18A1|f90|0|a0|0|f0|d8F|1|f0|d94|2|f0|dAC|3|f0|dC9|4|f0|dD6|5|f0|d96|6|f0|dBC|7|f0|dA5|FF|SKL|s214|fF|w0|a6|dA|f1|0|FF|SKL|s194|fC|w0|a3|dA|8|0|dD|1|D8|d12|25|56|FF|ACT|3|SKL|a771|f1E|0|a1|A|f1|d1C4|FF|CHT|A|0|0|30|オカシラ:\Sいくべかね|SKL|1C84|0|w3|a4|dD|1|16C|FF|SKL|1C84|0|0|a3|dD|8|0|FF|"

// 観战で突然落ち
//"CFP|a0|d12|SKL|1C84|0|0|a0|d13|1|E0|FF|SKL|a89B|f14|0|a4|13|f21|d18C|FF|CHT|A|0|0|30|オカシラ:いくべかね|SKL|a151D|f2D|0|aA|F|f80000|FF|ACT|A|SKL|s2455|fA|0|aF|12|パイレイト|a18DBE|2D|317|317|275|275|1|13|パイレイト|a18DBE|2D|2FA|2FA|283|283|1|FF|SKL|a183B|f48|0|a2|9|f0|d92|7|f0|d81|4|f0|d88|FF|SKL|s16A8|f28|0|a1|1|f0|FF|SKL|s14C|f15|w0|a5|dB|f21|1EF|FF|SKL|s1F8|f19|w0|aE|d5|f1|0|FF|SKL|1C84|0|0|aD|d6|1|90|FF|SKL|s82|f7|w0|a6|dD|f1|111|FF|SKL|1C84|0|0|a10|d9|1|64|FF|bg|7|SKL|s1FC4|f26|w0|a9|dC|f1|138|b64|FF|SKL|a8FF|f50|0|aC|2|f1|dA8|7|f1|d62|0|f1|dC8|4|f1|d77|FF|SKL|a17D6|f2D|0|a3|4|f0|dA8|FF|bg|11|CHT|A|0|0|30|オカシラ:いくべかね|SKL|a151D|f2D|0|aA|11|f80000|FF|"

// ACT_ACTおち
//"CFP|aA|dF|ACT|A|SKL|s261|f64|w0|a0|dA|f1|4|FF|ACT|0|bg|0|"

// 暗杀で星が出バグ
//"SKL|1C84|0|0|a0|dF|61|127|FF|ACT|0|bg|1|ACT|1|SKL|1C84|0|0|aA|d1|5|16|FF|SKL|s2580|f1|0|a0|dA|f80020|A|FF|"

//"bg|0|ACT|0|SKL|1C84|0|0|aB|d0|5|4A|FF|bg|0|CHT|B|14|63|30|リベナント:あああ|"
//"bg|0|ACT|0|SKL|1C84|0|0|aB|d0|5|4A|FF|bg|0|CHT|B|0|0|30|リベナント:あああ|"

// 召唤で落ちるバグ
//"PSN|1|t6|a5|f0|d44|SKL|s14E|f23|w0|a5|dA|f8|0|FF|SKL|s2455|f0|0|aA|D|ワイバーン|a18D4E|28|39D|39D|24E|24E|1|E|ワイバーン|a18D4E|28|389|389|25E|25E|1|FF|ACT|A|PSN|1|t6|a1|f0|d41|SKL|a1A2E|f14|0|a1|9|f80000|FF|PSN|1|t6|a3|f0|d43|ANR|a3|PSN|0|1|a3|d0|f61|SKL|s6|f46|w0|a3|d11|f5|13|a3|d11|f5|4D|FF|PSN|1|t6|a7|f0|d3D|ANR|a7|SKL|1C84|0|0|a7|dA|1|99|FF|PSN|1|t6|a6|f0|d53|ANR|a6|PNO|a6|SKL|1C84|0|0|a6|d12|21|145|FF|SKL|1C84|0|0|aC|d9|1|58|FF|PSN|1|t6|a8|f0|d48|ANR|a8|PNO|a8|SKL|1C84|0|0|a8|dD|1|139|FF|SKL|1C84|0|0|aB|d8|1|58|FF|PSN|1|t6|a4|f0|d3E|ANR|a4|SKL|s132|f46|w0|a4|dA|f8|0|FF|bg|11|PSN|1|t6|a9|f0|d4D|ANR|a9|SKL|1C84|0|0|a9|dA|8|0|FF|PSN|1|t6|a0|f0|d3A|ANR|a0|SKL|aBBE|f8C|0|a0|A|f1|d4F|B|f21|d138|C|f21|d13B|D|f1|d142|E|f1|d148|11|f1|d142|FF|PSN|1|t6|a2|f0|d4C|ANR|a2|bg|2|SKL|s2455|f0|0|aA|B|ワイバーン|a18D4E|28|36A|36A|27B|27B|1|C|ワイバーン|a18D4E|28|359|359|274|274|1|FF|"
//"PSN|1|t6|a5|f0|d44|SKL|s14E|f23|w0|a5|dA|f8|0|FF|SKL|s2455|f0|0|aA|D|ワイバーン|a18D4E|28|39D|39D|24E|24E|1|E|ワイバーン|a18D4E|28|389|389|25E|25E|1|FF|ACT|A|PSN|1|t6|a1|f0|d41|SKL|a1A2E|f14|0|a1|9|f80000|FF|PSN|1|t6|a3|f0|d43|ANR|a3|PSN|0|1|a3|d0|f61|SKL|s6|f46|w0|a3|d11|f5|13|a3|d11|f5|4D|FF|PSN|1|t6|a7|f0|d3D|ANR|a7|SKL|1C84|0|0|a7|dA|1|99|FF|PSN|1|t6|a6|f0|d53|ANR|a6|PNO|a6|SKL|1C84|0|0|a6|d12|21|145|FF|SKL|1C84|0|0|aC|d9|1|58|FF|PSN|1|t6|a8|f0|d48|ANR|a8|PNO|a8|SKL|1C84|0|0|a8|dD|1|139|FF|SKL|1C84|0|0|aB|d8|1|58|FF|PSN|1|t6|a4|f0|d3E|ANR|a4|SKL|s132|f46|w0|a4|dA|f8|0|FF|bg|11|PSN|1|t6|a9|f0|d4D|ANR|a9|SKL|1C84|0|0|a9|dA|8|0|FF|PSN|1|t6|a0|f0|d3A|ANR|a0|SKL|aBBE|f8C|0|a0|A|f1|d4F|B|f21|d138|C|f21|d13B|D|f1|d142|E|f1|d148|11|f1|d142|FF|PSN|0|t6|a2|f0|d4C|bg|2|SKL|s2455|f0|0|aA|B|ワイバーン|a18D4E|28|36A|36A|27B|27B|1|C|ワイバーン|a18D4E|28|359|359|274|274|1|FF||"

// 2回无效でバグ
//"SKL|s170F|f64|0|aA|A|f0|FF|CMB|dA|a0|f8001|d0|a1|f8001|d0|FF|"
//"SKL|s170F|f64|0|aA|A|f0|FF|ACT|A|CMB|dA|a0|f8005|d0|a1|f8005|d0|a5|f5|d1|a6|f5|d1|FF|bg|a|"
//"SKL|s170F|f64|0|aA|A|f0|FF|ACT|A|CMB|dA|a0|f8001|d0|a1|f8001|d0|a5|f5|d1|a6|f5|d1|FF|bg|a|"

//"SKL|s170F|f64|0|aA|A|f0|FF|ACT|A|CMB|dA|a0|f8005|d1|a1|f8005|d1|a5|f5|d1|a6|f5|d1|FF|bg|a|"

//"SKL|1C84|0|0|a0|dA|2005|0|FF|ACT|0|bg|1|ACT|1|SKL|1C84|0|0|aA|d1|5|16|FF|"

// ラスボス観战でＩＤおらんえらー
//"ESC|et0|f1|ESC|et1|f1|CFP|aF|d0|CFP|aA|d0|"

// カウンターアルティメットでペット入れ替え
//"|bg|a|ACT|A|CFP|aA|d5|SKL|1C84|0|0|a0|dA|1|30|aA|d0|f1071|52A|FF|MON|m5|1|ワータイガー|18A88|1|58|58|28|28|9|"
//"|bg|a|ACT|A|CFP|aA|d5|SKL|1C84|0|0|a0|dA|1|30|aA|d0|f1031|52A|FF|MON|m5|1|ワータイガー|18A88|1|58|58|28|28|9|"

// 新连激でとまる
//"CFP|aF|d46|SKL|s9|f37|w0|a5|dA|f202|5F|F|a5|dA|f202|60|F|a5|dA|f202|60|F|a5|dA|f202|60|F|a5|dA|f202|5F|F|a5|dA|f202|5F|F|FF|ACT|5|SKL|s0|fA|w0|aA|d5|f5|5|aA|d5|f5|6|FF|bg|5|"
//"CFP|aF|d46|SKL|s9|f37|w0|a5|dA|f202|5F|F|a5|dA|f202|60|F|a5|dA|f202|60|F|a5|dA|f202|60|F|a5|dA|f202|5F|F|FF|ACT|5|SKL|s0|fA|w0|aA|d5|f5|5|aA|d5|f5|6|FF|bg|5|"

//"SKL|s9|f37|w0|a5|dA|f202|5F|F|FF|"

//a5|dA|f202|60|F|a5|dA|f202|60|F|a5|dA|f202|60|F|a5|dA|f202|5F|F|a5|dA|f202|5F|F|FF|ACT|5|SKL|s0|fA|w0|aA|d5|f5|5|aA|d5|f5|6|FF|bg|5|"

//"SKL|0|0|aA|d5|1|5|aA|d5|1|5|aA|d5|1|5|FF|"
//"SKL|0|0|aA|d5|2001|5|aA|d5|4001|5|aA|d5|8001|5|aA|d5|2001|5|a5|dA|4001|5|FF|"
////"SKL|0|0|aA|d5|4001|5|aA|d5|2001|5|a5|dA|2031|5|aA|d5|2011|5|FF|"

//"CFP|aF|d26|CFP|a11|d26|SKL|s150|f46|w0|a6|d10|f1|1FC|FF|SKL|1C84|0|0|aF|d5|1|E2|FF|ACT|F|CFP|aF|d26|SKL|1C84|0|0|a11|d5|1|F0|FF|ACT|11|CFP|a11|d0|SKL|aC22|f8C|0|a10|1|f1|dF7|FF|ACT|10|CFP|a10|d0|SKL|a985|f70|0|a5|F|f1|dB7|10|f401|d32|A|f10021|d42|11|f10021|dC4|FF|SKL|1C84|0|w3|a1|d10|401|C|FF|SKL|s12F|f22|w3|a2|d10|401|1F|FF|SKL|s150|f31|w0|a8|dF|f1|25E|FF|SKL|s150|f3B|w0|a7|d10|f401|E6|FF|SKL|s1FDE|f0|w0|aA|d7|f2|226|b1F4|FF|ACT|A|SKL|1C84|0|0|a4|d10|402|F8|FF|ACT|4|SKL|a9C9|f78|0|a3|F|f1|d184|A|f1|d87|10|f401|d29|11|f401|d72|FF|ITM|e0|r0|0|3|d2F6|FF|SKL|1C84|0|0|aF|d8|1|111|FF|ACT|F|ACT|11|ACT|10|CFP|a10|d26|SKL|s1FDE|f0|w0|aA|d4|f1|14E|b14E|FF|ACT|A|CFP|aA|d0|SKL|1C84|0|0|a4|d10|2|29D|FF|"

// 魔法复数反射で死亡したら止まるバグ
//"SKL|a985|f70|0|a5|F|f1|dB7|10|f401|d32|8128C34|f10021|d42|11|f10021|dC4|FF|"

//"SKL|a985|f70|0|a5|F|f1|dB7|10|f401|d32|A|f10021|d42|11|f10021|dC4|FF|"

//"SKL|1C84|0|w3|a1|d10|401|C|FF|SKL|s12F|f22|w3|a2|d10|401|1F|FF|SKL|s150|f31|w0|a8|dF|f1|25E|FF|SKL|s150|f3B|w0|a7|d10|f401|E6|FF|SKL|s1FDE|f0|w0|aA|d7|f2|226|b1F4|FF|ACT|A|SKL|1C84|0|0|a4|d10|402|F8|FF|ACT|4|SKL|a9C9|f78|0|a3|F|f1|d184|A|f1|d87|10|f401|d29|11|f401|d72|FF|ITM|e0|r0|0|3|d2F6|FF|SKL|1C84|0|0|aF|d8|1|111|FF|ACT|F|ACT|11|ACT|10|CFP|a10|d26|SKL|s1FDE|f0|w0|aA|d4|f1|14E|b14E|FF|ACT|A|CFP|aA|d0|SKL|1C84|0|0|a4|d10|2|29D|FF|"

// ＬＶ１ガスト同士が殴ると止まるバグ
//"SKL|1C84|0|0|a0|dD|5|46|FF|ACT|0|SKL|1C84|0|0|aE|d0|1|23|FF|bg|d|SKL|1C84|0|0|a13|d0|1|22|FF|CMB|dF|aA|f1|d10|a12|f1|d19|aF|f1|d18|FF|SKL|1C84|0|0|a0|d13|21|21D|FF|"
//"SKL|1C84|0|0|a0|dD|5|46|FF|ACT|0|SKL|1C84|0|0|aE|d0|1|23|FF|bg|d|SKL|1C84|0|0|a13|d0|1|22|FF|CMB|dB|aA|f1|d10|aF|f1|d19|a12|f21|d18|FF|SKL|1C84|0|0|a0|d13|21|21D|FF|"
//"CMB|dF|aA|f1|d10|a12|f1|d19|aF|f1|d18|FF|SKL|1C84|0|0|a0|d13|21|21D|FF|"
//"CMB|dB|aA|f1|d10|a12|f1|d19|aF|f1|d18|FF|SKL|1C84|0|0|a0|d13|21|21D|FF|"

// 召还できないバグ
//"|bg|5|SKL|s2456|f0|0|aA|B|メヅク|a188AF|1E|51B|51B|51E|51E|1|C|メヅク|a188AF|1E|519|519|526|526|1|D|メヅク|a188AF|1E|4FC|4FC|4FE|4FE|1|FF|CHT|A|0|0|30|メヅク:ハハハハ！貴様にこの術が見破れるか！|ACT|A|bg|0|SKL|s2456|f0|0|aA|E|メヅク|a188B0|1E|540|540|512|512|1|F|メヅク|a188B0|1E|567|567|532|532|1|10|メヅク|a188B0|1E|4ED|4ED|4F3|4F3|1|FF|CHT|A|0|0|30|メヅク:貴様にこの術が見破れるか！|ACT|A|"


// 召还
//"|bg|0|ACT|0|bg|0|SKL|s194|f19|w0|aF|d0|5|3A|FF|ACT|F|SKL|s2459|f0|0|aB|A|シーバット|a18B84|46|4D2|4D2|399|399|1|D|シーバット|a18B84|44|49D|49D|3BC|3BC|1|E|シーバット|a18B84|45|4D6|4D6|3DF|3DF|1|10|シーバット|a18B84|45|4C7|4C7|3D9|3D9|1|11|シーバット|a18B84|44|429|429|33D|33D|1|12|シーバット|a18B84|45|47E|47E|32B|32B|1|FF|CMB|d0|aC|f5|d26|aF|f5|d53|FF|ACT|F|"


// 召还でとまる？
//"|CFP|aA|d1E|CFP|a10|d1E|CFP|aD|d1E|CFP|aE|d1E|SKL|1C84|0|w3|a0|d12|21|543|FF|ACT|0|SKL|1C84|0|w3|a0|dD|61|66D|FF|SKL|1C84|0|0|aA|d0|1|94|FF|SKL|1C84|0|0|a10|d0|8|0|FF|SKL|1C84|0|0|aE|d0|8|0|FF|SKL|1C84|0|0|aF|d0|8|0|FF|ACT|F|bg|b|POS|11|SKL|s132|f3A|w0|a11|d0|f8|0|FF|SKL|s194|f19|w0|aF|d0|2|1F2|FF|ACT|F|"

//"|CFP|aA|d1E|CFP|a10|d1E|CFP|aD|d1E|CFP|aE|d1E|ACT|0|SKL|1C84|0|w3|a0|dD|61|66D|FF|SKL|1C84|0|0|aA|d0|1|94|FF|SKL|1C84|0|0|a10|d0|8|0|FF|SKL|1C84|0|0|aE|d0|8|0|FF|SKL|1C84|0|0|aF|d0|8|0|FF|ACT|F|bg|b|POS|11|SKL|s132|f3A|w0|a11|d0|f8|0|FF|SKL|s194|f19|w0|aF|d0|2|1F2|FF|ACT|F|"

//"SKL|1C84|0|w3|a0|d12|21|543|FF|ACT|0|SKL|1C84|0|w3|a0|dD|61|66D|FF|SKL|1C84|0|0|aA|d0|1|94|FF|SKL|1C84|0|0|a10|d0|8|0|FF|SKL|1C84|0|0|aE|d0|8|0|FF|SKL|1C84|0|0|aF|d0|8|0|FF|ACT|F|bg|b|POS|11|SKL|s132|f3A|w0|a11|d0|f8|0|FF|SKL|s194|f19|w0|aF|d0|2|1F2|FF|ACT|F|"

//|SKL|TechId|FP|WeaponId|踊り效果ＩＤ|攻击侧ＩＤ|踊り效果ＩＤで派生～

//"CFP|a10|d0|CFP|aA|d0|CFP|aF|d0|CFP|a11|d0|SKL|s264F|f2D|0|B|a2|1|f0|0|2|f0|0|3|f0|0|5|f0|0|7|f0|0|FF|bg|5|ACT|5|SKL|s0|f5|w0|a3|d10|f401|E|a3|d11|f401|30|FF|ACT|3|SKL|1C84|0|0|a1|dF|401|5|FF|ACT|1|bg|7|bg|5|SKL|1C84|0|0|a3|d10|401|4D|FF|SKL|1C84|0|0|a1|dF|401|1A|FF|"

//"SKL|s264F|f2D|0|B|a2|1|f0|0|3|f0|0|5|f0|0|7|f0|0|FF|"

//"CFP|aB|d23|CFP|aF|d31|SKL|s0|fA|w0|a13|d2|f21|E3|a13|d5|f2|139|a5|d13|f11|C4|FF|SKL|s12C0|f28|0|a12|0|f0|4|f0|5|f0|7|f0|8|f0|9|f0|FF|PNO|aE|bg|e|SKL|sD4E|f23|0|a10|8|f0|FF|PNO|aC|bg|c|SKL|s198|f2D|w0|a11|d9|61|13B|d0|1|EE|d7|1|8F|d0|1|111|FF|PNO|aF|SKL|1C84|0|0|aF|d8|1|54|FF|SKL|a9C8|f64|0|aA|7|f1|d6D|5|f21|d131|FF|SKL|1C84|0|w1|a0|d10|1|17|dB|1|1C|FF|SKL|a7F4|f2B|0|aD|8|f1|dA8|FF|"

//"SKL|s198|f2D|w0|a11|d9|61|13B|d0|1|EE|d7|1|8F|d0|1|111|FF|PNO|aF|SKL|1C84|0|0|aF|d8|1|54|FF|SKL|a9C8|f64|0|aA|7|f1|d6D|5|f21|d131|FF|SKL|1C84|0|w1|a0|d10|1|17|dB|1|1C|FF|SKL|a7F4|f2B|0|aD|8|f1|dA8|FF|"
//"SKL|s198|f2D|w0|a11|d9|61|13B|d0|1|EE|d7|1|8F|FF|PNO|aF|SKL|1C84|0|0|aF|d8|1|54|FF|SKL|a9C8|f64|0|aA|7|f1|d6D|5|f21|d131|FF|SKL|1C84|0|w1|a0|d10|1|17|dB|1|1C|FF|SKL|a7F4|f2B|0|aD|8|f1|dA8|FF|"

// 搅乱の反射で止まる
//"CFP|a0|d0|SKL|s24B8|f5|w0|a0|d5|f2021|1F7|0|FF|"
//"CFP|a0|d0|SKL|s24B8|f5|w0|a0|d5|f2001|1F7|1|FF|"
//"CFP|a0|d0|SKL|s24B8|f5|w0|a0|d5|f4001|1F7|2|FF|"

//"CFP|a0|d0|SKL|s24B8|f5|w0|a0|d5|f8001|1F7|3|FF|"
//"CFP|a0|d0|SKL|s24B8|f5|w0|a0|d5|f2021|1F7|4|FF|"

// 踊るポジションチェンジバグ
//"SKL|s264B|f19|0|7|a7|F|10|11|FF|SKL|s264B|f19|0|7|a6|6|FF|SKL|1C84|0|0|a10|d5|8|0|FF|SKL|s2649|fF|0|2|aB|0|f80000|1|f80000|2|f80000|5|f80000|6|f80000|7|f80000|FF|CMB|d5|aA|f1|d5D|aF|f1|dA6|a11|f1|d9C|aB|f1|d15|FF|"
						);
#ifdef PUK2
				{
					FILE *fp;
					char s[sizeof(BcData)+2];
					int i;

					// 必要な B プロトコルの C データと M データが书き込まれているファイル
					// 例)
					//   B C|0|0|バッツ|19A5A|64|527|527|356|36B|1000404|A|ゴールドスカル|1AF00|1F|22|183|300|300|9000000|F|ゴールドスカル|1AF00|1E|A9|13D|279|279|9000000|10|ゴールドスカル|1AF00|20|30|18B|2DE|2DE|9000000|11|ゴールドスカル|1AF00|20|37|168|2CE|2CE|9000000|
					//   B M|CFP|aA|d2E|CFP|a11|d2E|CFP|a10|d2E|CFP|aF|d2E|SKL|s6594|f15|w0|a0|dF|80001|A0|dA|221|F9|11|FF|ACT|0|bg|0| 
					fp = fopen( "battlerecv.txt", "rt" );                            //MLHIDE
					if (fp){
						for(i=0;i<battlePlayLine;i++) fgets( s, sizeof(s), fp );
						for(;;){
							fgets( s, sizeof(s), fp );
							sjisStringToEucString(s);
							i = strlen(s);
							battlePlayLine++;
							if ( s[0] == 'B' && s[1] == ' ' && s[2] == 'M' ){
								i -= 2;
								memcpy( BmData, s+2, i );
								BmData[i] = '\0';
								for(;;i--){
									if ( BmData[i-1] == '\n' ) BmData[i-1] = '\0';
									else if ( BmData[i-1] == '\r' ) BmData[i-1] = '\0';
									else if ( BmData[i-1] == ' ' ) BmData[i-1] = '\0';
									else break;
								}
								break;
							}
							else if ( s[0] == 'B' && s[1] == ' ' && s[2] == 'C' ){
								battlePlayLine--;
								strcpy( BmData, "B M" );                                      //MLHIDE
								break;
							}
						}
						fclose(fp);
					}
				}
#endif
						
						
				// アクション抹杀
				//DeathAllAction();
				// バトル初期化
				//InitBattle();
				// ムービー読み込みポイント初期化
				BmReadPoint = 2;
				// 次ぎのプロセスへ
				SubProcNo = BATTLE_PROC_MOVIE;
				break;
			}
#endif
			// ムービーデータが来ていたら
			if( BmDataGetBufNo != BmDataSetBufNo ){
				// ＢＣデータコピー
				strcpy( BmData, BmDataBak[ BmDataGetBufNo ] );
				// 次のバッファへ
				BmDataGetBufNo++;
				// リミットチェック
				if( BmDataGetBufNo >= B_BUF_SIZE ) BmDataGetBufNo = 0;
				// 取り出しポイント进める
				//BmDataGetBufNo = BmDataSetBufNo;
				// ムービー読み込みポイント初期化
				BmReadPoint = 2;
				// 次ぎのプロセスへ
				SubProcNo = BATTLE_PROC_MOVIE;
			}else{
				// プレイヤーの数を调べる（复数の时）
				if( CheckBattlePlayerCnt() >= 2 ){
					// 一行インフォ
					strcpy( OneLineInfoStr,ML_STRING(186, "等待其他玩家的指令。"));
				}
			}
				
					
			break;
			
		case BATTLE_PROC_MOVIE: // ムービー处理 ********************************************/
		
			// マウス等级がメニュー以下の时
			//if( mouse.level < DISP_PRIO_MENU && TaskBarFlag == FALSE ){
			//	drawGrid();		// グリッドカーソル表示
			//	moveProc();		// 移动处理
			//}
			
			// キーボード处理
			KeyBoardProc();
			// バトル时のアクション走らせる
			//BattleRunAction();
			/* アクション走らせる */
			RunAction();
			// バトルキャラクターソート
			SortBattleChar();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			//drawMap();	// マップ表示

#if 0
#ifdef _DEBUG
			// 名称の表示
			BattleNameDisp();
#endif
#endif
			
			/* チャット处理 */
			ChatProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();
			// フィールド关连处理（グループ设定ショートカットキーのためだけ）
			fieldProc();			
			// パレット处理
			//PaletteProc();
			// ＢＭＰをバックサーフェスにセット
			//PutBmp();	
#ifdef PUK2
			LocalBurstTimeProc();
#endif
		
			break;
			
#ifdef PUK3_BATTLEEND_LOGOUT
		case BATTLE_PROC_LOGOUT_INIT: // 战闘中登出初期化 ********************************************/
			// 基本的にBATTLE_PROC_OUT_PRODUCE_INITの处理を行う
			// 处理が終わったら、BATTLE_PROC_OUT_PRODUCEへではなく
			// 登出处理へ移动

			BattleMasterBit = 0;
			MyAkoFlag = TRUE;
			// 各バトルバトルマスターに、終了处理をさせる
			RunAction();
			BattleSuperMaster_Death();

#ifdef PUK3_CMD_END_SET
			// キャラクター分ループ
		#ifdef PUK2_GUILMON_BATTLE
			for( i = 0 ; i < BC_MAX_EX ; i++ ){
		#else
			for( i = 0 ; i < BC_MAX ; i++ ){
		#endif
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタ初期化
				pActBc[ i ] = NULL;
			}
#endif
		
			//ＢＧＭフェードアウト
			fade_out_bgm();
			// バトルサーフェスの画像作成 
			CopyBackBuffer();
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			// 雨とかを降らせきる
			if ( battleEffectRainLevel || battleEffectSnowLevel ||
				 battleEffectKamiFubukiLevel || battleEffectCloudLevel ){
				battleEffectRainLevel = 0;
				battleEffectSnowLevel = 0;
				battleEffectKamiFubukiLevel = 0;
				battleEffectCloudLevel = 0;
				mapEffectProc2( 160 );		// マップエフェクト（雨??雪等）
			}

			// 确实に初期化
			battleEffectRainLevel = 0;
			battleEffectSnowLevel = 0;
			battleEffectKamiFubukiLevel = 0;
			battleEffectCloudLevel = 0;
#endif
			
			/* チャット处理 */
			//ChatProc();
			// キーボードカーソル点灭处理
			//FlashKeyboardCursor();
			// ＩＭＥ关连处理
			//ImeProc();


			// ここからはBATTLE_PROC_OUT_PRODUCEでの初期化处理を行う
			// nrproto_EO_sendは、登出の时は送る必要なし
			// アクション全抹杀
			DeathAllAction();
			// キャラ管理テーブルのアクションを抹消
			clearPtActCharObj();
			// この后からのＣ、ＣＡプロトコルを受け付ける
			encountNowFlag = 0;


			// 登出处理へ
			GameState = GAME_LOGIN;
			ChangeProc2( PROC_CHAR_LOGOUT );

	#ifdef PUK3_SEGMENTATION_FAULT
			ProcPop();
	#endif
			return;
#endif
		case BATTLE_PROC_OUT_PRODUCE_INIT: // 終了演出初期化 ********************************************/
#ifdef PUK2
			BattleMasterBit = 0;
			MyAkoFlag = TRUE;
			// 各バトルバトルマスターに、終了处理をさせる
			RunAction();
			BattleSuperMaster_Death();
#endif
#ifdef PUK3_CMD_END_SET
			// キャラクター分ループ
		#ifdef PUK2_GUILMON_BATTLE
			for( i = 0 ; i < BC_MAX_EX ; i++ ){
		#else
			for( i = 0 ; i < BC_MAX ; i++ ){
		#endif
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタ初期化
				pActBc[ i ] = NULL;
			}
#endif
		
			//ＢＧＭフェードアウト
			fade_out_bgm();
			// バトルサーフェスの画像作成 
			CopyBackBuffer();
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
#ifdef PUK2_ACID_PAPER_SNOWSTORM
			// 雨とかを降らせきる
			if ( battleEffectRainLevel || battleEffectSnowLevel ||
				 battleEffectKamiFubukiLevel || battleEffectCloudLevel ){
				battleEffectRainLevel = 0;
				battleEffectSnowLevel = 0;
				battleEffectKamiFubukiLevel = 0;
				battleEffectCloudLevel = 0;
				mapEffectProc2( 160 );		// マップエフェクト（雨??雪等）
			}

			// 确实に初期化
			battleEffectRainLevel = 0;
			battleEffectSnowLevel = 0;
			battleEffectKamiFubukiLevel = 0;
			battleEffectCloudLevel = 0;
#endif
			
			/* チャット处理 */
			//ChatProc();
			// キーボードカーソル点灭处理
			//FlashKeyboardCursor();
			// ＩＭＥ关连处理
			//ImeProc();
			
			
			// 演出初期化
			ProduceInitFlag = TRUE;
			SubProcNo++;
			
			//break;
			
		case BATTLE_PROC_OUT_PRODUCE: // 終了演出 ********************************************/

			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			
			// 简易登出の时
			if( SimpleLogoutFlag == TRUE ){
				no = PRODUCE_UP_DOWN_LINE_ACCELE;
			}else{
				no = PRODUCE_UNERI_ACCELE;
			}
			// 演出中
#ifdef PUK2
			if( DrawProduce( no, 8, 2.0f ) == TRUE ){
#else
			if( DrawProduce( no ) == TRUE ){
#endif
			//if( DrawProduce( PRODUCE_LEFT_RIGHT_ACCELE ) == TRUE ){
			//if( DrawProduce( PRODUCE_4WAY_OUT ) == TRUE ){
#ifdef PUK3_NOTFREE_WINDDOW
				// メニューの初期化しておく
				initMenu();
#endif
#ifdef PUK3_BATTLE_CMDRECV
				// 离线模式の时
				if( offlineFlag == TRUE ){
					SubProcNo = BATTLE_PROC_INIT;
					break;
				}
#endif
				// プロセスチェンジ
				ChangeProc( PROC_GAME, 1 );
				// アクション全抹杀
				DeathAllAction();
				// キャラ管理テーブルのアクションを抹消
				clearPtActCharObj();
				// この后からのＣ、ＣＡプロトコルを受け付ける
				encountNowFlag = 0;
				// クライアントが最后のムービーを见終わったときに送る。
				nrproto_EO_send( sockfd, 0 );
				// 时间带でパレットチェンジする时
		//		if( TimeZonePalChangeFlag == TRUE ){
					// 时间を更新する
		//			timeZoneProc();
					// パレットチェンジ
		//			PaletteChange( nrTimeZoneNo, 0 );
		//		}
				//フィールドのＢＧＭ再生
				//play_bgm(map_bgm);		//フィールドＢＧＭ再生
				//SubProcNo++;
	#ifdef PUK3_SEGMENTATION_FAULT
				ProcPop();
	#endif
				return;
			}
			
			
			break;
	}

#ifdef PUK2
	// プロデュース中でないとき
	if( BackBufferDrawType != DRAW_BACK_PRODUCE ){ 
#else
	// コマンド入力时またはムービーデータ受信待ちの时
	if( SubProcNo == BATTLE_PROC_CMD_INPUT || SubProcNo == BATTLE_PROC_RECV_MOVIE_DATA ){
#endif
		// 観战じゃないとき
		if( BattleMyNo < 20 ){
			// 右下グループの时
			if( BattleMyNo < 10 ){
				for( i  = 0 ; i < 10 ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( i );
#endif
					// 耐久力メーター表示
#ifdef PUK2
					if( pActBc[ i ] != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
						CheckAction( pActBc[ i ] );
#endif
						if ( (pActBc[ i ]->x == BcPos[ BcPosId[ i ] ].defX) && (pActBc[ i ]->y == BcPos[ BcPosId[ i ] ].defY) ){
							if ( (pActBc[ i ]->rgbaon==0) &&
								 !(pActBc[ i ]->atr&ACT_ATR_HIDE) ){
								HpMeterDisp( i );
							}
						}else if ( pActBc[ i ]->anim_no == ANIM_STAND
							|| pActBc[ i ]->anim_no == ANIM_GUARD
							|| pActBc[ i ]->anim_no == ANIM_DAMAGE
							|| pActBc[ i ]->anim_no == ANIM_DEAD
							|| pActBc[ i ]->anim_no == ANIM_WALK ){
							if ( (pActBc[ i ]->rgbaon==0) &&
								 !(pActBc[ i ]->atr&ACT_ATR_HIDE) ){
								HpMeterDisp( i );
							}
						}
					}
#else
					if( pActBc[ i ] != NULL ) HpMeterDisp( i );
#endif
				}
			}else 
			// 左上グループの时
			if( BattleMyNo >= 10 ){ 
				for( i = 10 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
					CheckIdRange( i );
#endif
					// 耐久力メーター表示
#ifdef PUK2
					if( pActBc[ i ] != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
						CheckAction( pActBc[ i ] );
#endif
						if ( (pActBc[ i ]->x == BcPos[ BcPosId[ i ] ].defX) && (pActBc[ i ]->y == BcPos[ BcPosId[ i ] ].defY) ){
							if ( (pActBc[ i ]->rgbaon==0) &&
								 !(pActBc[ i ]->atr&ACT_ATR_HIDE) ){
								HpMeterDisp( i );
							}
						}else if ( pActBc[ i ]->anim_no == ANIM_STAND
							|| pActBc[ i ]->anim_no == ANIM_GUARD
							|| pActBc[ i ]->anim_no == ANIM_DAMAGE
							|| pActBc[ i ]->anim_no == ANIM_DEAD
							|| pActBc[ i ]->anim_no == ANIM_WALK ){
							if ( (pActBc[ i ]->rgbaon==0) &&
								 !(pActBc[ i ]->atr&ACT_ATR_HIDE) ){
								HpMeterDisp( i );
							}
						}
					}
#else
					if( pActBc[ i ] != NULL ) HpMeterDisp( i );
#endif
				}
			}
		}else{
			// 観战の时
			for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// 耐久力メーター表示
#ifdef PUK2
				if( pActBc[ i ] != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
					CheckAction( pActBc[ i ] );
#endif
					if ( (pActBc[ i ]->x == BcPos[ BcPosId[ i ] ].defX) && (pActBc[ i ]->y == BcPos[ BcPosId[ i ] ].defY) ){
						if ( (pActBc[ i ]->rgbaon==0) &&
							 !(pActBc[ i ]->atr&ACT_ATR_HIDE) ){
							HpMeterDisp( i );
						}
					}else if ( pActBc[ i ]->anim_no == ANIM_STAND
						|| pActBc[ i ]->anim_no == ANIM_GUARD
						|| pActBc[ i ]->anim_no == ANIM_DAMAGE
						|| pActBc[ i ]->anim_no == ANIM_DEAD
						|| pActBc[ i ]->anim_no == ANIM_WALK ){
						if ( (pActBc[ i ]->rgbaon==0) &&
							 !(pActBc[ i ]->atr&ACT_ATR_HIDE) ){
							HpMeterDisp( i );
						}
					}
				}
#else
				if( pActBc[ i ] != NULL ) HpMeterDisp( i );
#endif
			}
		}
	}
#ifdef PUK2_ELEMENTDISP
	#ifdef _DEBUG
		// 属性表示
		if (CmdLineFlg&CMDLINE_ELMDISP) ElementDisp();
	#endif
#endif

	// プロデュース中でないとき
	if( BackBufferDrawType != DRAW_BACK_PRODUCE ){ 
#ifdef PUK2_NEWSKILL
		mapEffectProc();		// マップエフェクト（雨??雪等）
#endif
		// 战闘强制終了の时
		if( BattleEscFlag == TRUE ){
			// 終了プロセスへ
			SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
			// フラグ初期化
			BattleEscFlag = FALSE;
		}
		
		// ウィンドウある时
		if( pActAudienceExitWnd != NULL ){
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActAudienceExitWnd );
#endif
			// 观战结束文字表示ウィンドウが出来あがっていたら
			if( pActAudienceExitWnd->hp > 0 ){
				// 枠内に入っていたら
				if( MakeHitBox( pActAudienceExitWnd->x + 23 - 3, pActAudienceExitWnd->y + 40 - 3,
								pActAudienceExitWnd->x + 23 + 84 + 3, pActAudienceExitWnd->y + 40 + 20 + 3, DISP_PRIO_MENU + 1 ) == TRUE ){
					// 一行インフォ
					strcpy( OneLineInfoStr,ML_STRING(187, "结束观战。"));
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
						// 观战结束プロトコル送る
						nrproto_B_send( sockfd, "U" );                                  //MLHIDE
						// 終了プロセスへ
						SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
					}
				}
				// 文字を表示
				StockFontBuffer( pActAudienceExitWnd->x + 23, pActAudienceExitWnd->y + 40, FONT_PRIO_FRONT, FONT_KIND_BIG, FONT_PAL_WHITE, ML_STRING(188, "观战结束"), 0 );
				//StockFontBuffer( pActAudienceExitWnd->x + 30, pActAudienceExitWnd->y + 28, FONT_PRIO_FRONT, 0, 	"按鼠标右键", 0 );
				//StockFontBuffer( pActAudienceExitWnd->x + 30, pActAudienceExitWnd->y + 52, FONT_PRIO_FRONT, 0, 	"结束观战", 0 );
				//StockFontBuffer( pActInfoWnd->x + 38, pActInfoWnd->y + 28, FONT_PRIO_FRONT, FONT_PAL_YELLOW, 	"选择", 0 );
				//StockFontBuffer( pActInfoWnd->x + 38, pActInfoWnd->y + 52, FONT_PRIO_FRONT, FONT_PAL_YELLOW, 	"目标", 0 );
			}
		}
	}

#ifdef _DEBUG			
	// 离线模式の时
	if( offlineFlag == TRUE ){
		// Ｚキーで最初からやり直し
		if( VK[ VK_Z ] & KEY_ON_REP ){
	#ifdef PUK2
		#ifdef PUK3_NOTFREE_WINDDOW
			SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
		#else
			SubProcNo = BATTLE_PROC_INIT;

			// アクション全抹杀
			DeathAllAction();
			BmEndFlag = FALSE;
		#endif
	#else
			// バトルキャラクターデータデータ受信待ちへ
			SubProcNo = BATTLE_PROC_RECV_BC_DATA;
	#endif
			// ムービー読み込みフラグＯＮ
			ReadBmDataFlag = FALSE;
		}
	#ifdef PUK2
		else if ( VK[ VK_N ] & KEY_ON_REP ){
			MyAkoFlag = TRUE;
			// ムービー読み込みフラグＯＮ
			ReadBmDataFlag = FALSE;
		}
	#endif
	}
#endif

#if 0
#ifdef _DEBUG
	// 离线模式の时
	if( offlineFlag == TRUE ){
		// 右キーでプラス
		if( VK[ VK_RIGHT ] & KEY_ON_REP ){
			// カウントアップ
			BattleMapNo++;
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif
			// 読み込み
			if( ReadBattleMap( BattleMapNo ) == FALSE ){ 
				 BattleMapNo--;
			}else{
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
				SortDispBuffer(); 	// 表示バッファソート
	#ifdef PUK2
				// 色变え制限を一时的にＯＦＦ
				LimiteLoadBmpFlag = FALSE;
	#endif
				// ＢＭＰをバックサーフェスにセット
				PutBmp();
				// バックサーフェスからバトルサーフェスへコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
				// 色变え制限をＯＮ
				LimiteLoadBmpFlag = TRUE;
	#endif
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
			}
		}
		// 左キーでマイナス
		if( VK[ VK_LEFT ] & KEY_ON_REP && BattleMapNo > 0 ){
			// カウントアップ
			BattleMapNo--;
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif
			// バトルマップ読み込みとバトルサーフェスの画像作成 */
			ReadBattleMap( BattleMapNo );
			
			ChatProc();				// チャット处理
			// バックサーフェスを黒でクリアー
			ClearBackSurface();	
			SortDispBuffer(); 	// 表示バッファソート
	#ifdef PUK2
			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
	#endif
			// ＢＭＰをバックサーフェスにセット
			PutBmp();
			// バックサーフェスからバトルサーフェスへコピー
			lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;
	#endif
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif
		}
	}
#if 1	
	{
		//int i;
		
		//for( i = 0 ; i < 700 ; i++ ){
		
			//StockDispBuffer( 0,0, DISP_PRIO_MENU, CG_B_GENERAL_CANCEL_BTN_UP, 0 );
			if( VK[ VK_SPACE ] & KEY_ON_REP ) StockDispBuffer( 0,0, DISP_PRIO_MENU, 17999, 0 );
		//}
	}
#endif
#endif
#endif

#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}
