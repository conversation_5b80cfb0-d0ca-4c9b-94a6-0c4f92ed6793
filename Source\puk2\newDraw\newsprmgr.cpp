﻿/***************************************
			newsprmgr.cpp
***************************************/

#ifdef PUK2

void DrawBitmapToSurface2_PUK2_PAL( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, PALDATA *ppl, char pchg=1 );

void AllocateBmpToSurface_PUK2_PAL( int bmpNo, char usechg );

extern PALDATA PDpal[256];
extern unsigned char *Palchg;

extern int palchgcnt;
extern int RealBinBmpNo;

BOOL LimiteLoadBmpFlag;


// ＢＭＰをロードする（グラフィック番号变换もする）（ LoadBmp() の新型パレットチェンジ对应型 ） ***************************/
BOOL LoadBmp2_PUK2_PAL( int bmpNo, char usechg )
{
	char flg=0;
	int palsiz;

	// RIAL.BIN　番号にする
	realGetNo( bmpNo , (U4 *)&bmpNo );

#ifdef SUPPORT_16BIT
	if (SpriteInfo[ bmpNo ].lpSurfaceInfo){
		if ( ( displayBpp != 8 && SpriteInfo[ bmpNo ].lpSurfaceInfo->PalNo != PalState.palNo )
		|| ( displayBpp != 8 && SpriteInfo[ bmpNo ].lpSurfaceInfo->PalCount != PalState.count )
		){
			if (SpriteInfo[ bmpNo ].BmpVer==1){
				if ( palchgcnt>=MAXPALCHGQ ){
					return TRUE;
				}
				palchgcnt++;
			}
			flg=1;
		}
	}
#endif

	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL || flg ){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if( realGetImage( 	bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &palsiz ) == FALSE ){
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_39, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo=-1;
			return FALSE;
		}
		RealBinBmpNo=bmpNo;

		// ＢＭＰのサイズを记忆
		SpriteInfo[ bmpNo ].width = RealBinWidth;
		SpriteInfo[ bmpNo ].height = RealBinHeight;

	}
	return TRUE;
}

//----------------------------------//

/* ＢＭＰをサーフェスへ割り当て ***********************************************/
void AllocateBmpToSurface_PUK2_PAL( int bmpNo, char usechg )
{
	//int bmpWidth  = lpBmpInfo->bmiHeader.biWidth;	// ＢＭＰの横サイズ
	//int bmpHeight = lpBmpInfo->bmiHeader.biHeight; 	// ＢＭＰの縦サイズ

	int bmpWidth  = RealBinWidth;	// ＢＭＰの横サイズ
	int bmpHeight = RealBinHeight; 	// ＢＭＰの縦サイズ
	
	int offsetX, offsetY; 			// 送るＢＭＰのOFFセットポイント
	int sizeX, sizeY;				// 転送するサイズ
	int surfaceCntX;				// 必要なサーフェスの横枚数
	int surfaceCntY;				// 必要なサーフェスの横枚数
	int totalSurface;				// 必要なサーフェスの総数
	int totalSurfaceCnt = 0;		// 现在の确保したサーフェスの枚数（ ループカウンタ ）
	int	SurfaceSearchPointBak = SurfaceSearchPoint; // 一周检索したら終了するため最初の位置を记忆
	int amariSizeX = FALSE;		// 横に余りがあるかフラグ
	int amariSizeY = FALSE;		// 縦に余りがあるかフラグ
	BOOL vramFullFlag = FALSE; 		// VRAMがいっぱいかどうか
	SURFACE_INFO *prevSurfaceInfo = NULL; 	// 前のサーフェスインフォ构造体のアドレス
#ifdef PUK2
	int i;							// ループカウンタ
	PALDATA *ppl = NULL;
	PALDATA *pal;								// 使用するパレット
	PALDATA cpl[256];							// パレットチェンジ后のパレット
	// 赤
	#define PPLR 2
	// 緑
	#define PPLG 1
	// 青
	#define PPLB 0
#endif
	
	// 送るＢＭＰのOFFセットポイント
	offsetX = 0; 
	offsetY = bmpHeight;
	
	// 必要なサーフェスの横枚数计算
	surfaceCntX = bmpWidth / SURFACE_WIDTH;
	
	// 横に余りがあったら半端サイズを记忆
	if( ( amariSizeX = bmpWidth % SURFACE_WIDTH ) ){ 
		surfaceCntX++;		// もうひとつ必要
	}
	
	// 必要なサーフェスの縦枚数计算
	surfaceCntY = bmpHeight / SURFACE_HEIGHT;
	
	// 縦に余りがあったら半端サイズを记忆
	if( ( amariSizeY = bmpHeight % SURFACE_HEIGHT ) ){ 
		surfaceCntY++;		// もうひとつ必要
	}
	// 必要なサーフェスの総数计算
	totalSurface  = surfaceCntX * surfaceCntY;
	
#if 0
	// 横幅を４のバウンダリにする
	if( ( lpBmpInfo->bmiHeader.biWidth & 3 ) ){
		lpBmpInfo->bmiHeader.biWidth += 4 - lpBmpInfo->bmiHeader.biWidth & 3;
	}
#else	// Realbin 読み込むとき
	// 横幅を４のバウンダリにする
	if( ( RealBinWidth & 3 ) ){
		RealBinWidth += 4 - RealBinWidth & 3;
	}
#endif

	// 同じ所に上书きするなら前回のものは消去する。
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo != NULL ){
			prevSurfaceInfo = SpriteInfo[ bmpNo ].lpSurfaceInfo;
			// サーフェスリストの初期化ループ
			for( ; prevSurfaceInfo != NULL;
				prevSurfaceInfo = prevSurfaceInfo->pNext ){
				
			// ＢＭＰ番号初期化
				prevSurfaceInfo->bmpNo = -1;
				
#ifdef _DEBUG		
				// 现在使っているサーフェスの数マイナス
				SurfaceUseCnt--;
#endif
			}
			SpriteInfo[ bmpNo ].lpSurfaceInfo = NULL;
	}

#ifdef PUK2
	// パレットの准备(パレットチェンジの为)
	if (usechg){
		if (Palchg){
			// 赤
			if (Palchg[PPLR]==128){
				for(i=0;i<256;i++) cpl[i][PPLR]=ppl[i][PPLR];
			}else{
				for(i=0;i<256;i++) cpl[i][PPLR]=PalchgTbl[PPLR][ ppl[i][PPLR] ];
			}

			// 緑
			if (Palchg[PPLG]==128){
				for(i=0;i<256;i++) cpl[i][PPLG]=ppl[i][PPLG];
			}else{
				for(i=0;i<256;i++) cpl[i][PPLG]=PalchgTbl[PPLG][ ppl[i][PPLG] ];
			}

			// 青
			if (Palchg[PPLB]==128){
				for(i=0;i<256;i++) cpl[i][PPLB]=ppl[i][PPLB];
			}else{
				for(i=0;i<256;i++) cpl[i][PPLB]=PalchgTbl[PPLB][ ppl[i][PPLB] ];
			}
			pal=cpl;
		}else pal=ppl;
	}else pal=ppl;
#endif
	// 空いているサーフェスを探す
	// サーフェスの数だけループ
	while( 1 ){
		// 使用できる状态なら
		if( SurfaceInfo[ SurfaceSearchPoint ].date < SurfaceDate - SURACE_BMP_DEATH_DATE ){
#ifdef _DEBUG		
			// 现在使っているサーフェスの数カウント
			SurfaceUseCnt++;
#endif
			// 上书きする时
			if( SurfaceInfo[ SurfaceSearchPoint ].bmpNo != -1 ){
				// サーフェスインフォ构造体のアドレス
				SURFACE_INFO *lpSurfaceInfo;
				
				// サーフェスリストの先头アドレスを记忆
				lpSurfaceInfo = SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo;
				
				// 前にいたＢＭＰとサーフェスのリンクを削除
				SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo = NULL;

				// サーフェスリストの初期化ループ
				for( ; lpSurfaceInfo != NULL;
					lpSurfaceInfo = lpSurfaceInfo->pNext ){
					
					// ＢＭＰ番号初期化
					lpSurfaceInfo->bmpNo = -1;
					
#ifdef _DEBUG		
					// 现在使っているサーフェスの数マイナス
					SurfaceUseCnt--;
#endif
				}
				
			}
			// サーフェスリストの先头アドレスを记忆するとき
			if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){
				// スプライトインフォに先头アドレスを记忆する
				SpriteInfo[ bmpNo ].lpSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				// パレット番号とフェードのカウンターも学习
				SurfaceInfo[ SurfaceSearchPoint ].PalNo = PalState.palNo;
				SurfaceInfo[ SurfaceSearchPoint ].PalCount = PalState.count;
				SurfaceInfo[ SurfaceSearchPoint ].AnimPalNo = 0;
				
			}else{ // つなげるとき
				
				// 覚えておいた前のサーフェスインフォ构造体にアドレスを教える
				prevSurfaceInfo->pNext = &SurfaceInfo[ SurfaceSearchPoint ];
			}
			
			// ＢＭＰ番号を记忆する
			SurfaceInfo[ SurfaceSearchPoint ].bmpNo = bmpNo;
			
			// OFFセット座标を学习
			SurfaceInfo[ SurfaceSearchPoint ].offsetX = offsetX;
			SurfaceInfo[ SurfaceSearchPoint ].offsetY = bmpHeight - offsetY;
			
			// 横に余りがあるときは転送する前にサーフェスを黒でクリアー
			if( offsetX >= bmpWidth - SURFACE_WIDTH && amariSizeX ){
				ClearSurface( SurfaceInfo[ SurfaceSearchPoint ].lpSurface );
				// 余りサイズ
				sizeX = amariSizeX;				
			}else sizeX = SURFACE_WIDTH;
			
			// 縦に余りがあるときは転送する前にサーフェスを黒でクリアー
			if( offsetY - SURFACE_HEIGHT <= 0 && amariSizeY ){
				// 先に黒でクリアーされてないとき
				if( sizeX != amariSizeX ){
					ClearSurface( SurfaceInfo[ SurfaceSearchPoint ].lpSurface );
				}
				// 余りサイズ
				sizeY = amariSizeY;
			}else sizeY = SURFACE_HEIGHT;
			
			// ＢＭＰをサーフェスへ転送
#ifdef PUK2
			// こっちの场合 usechg は DrawBitmapToSurface2_PUK2_PAL() 关数内において未使用
			DrawBitmapToSurface2_PUK2_PAL( 	SurfaceInfo[ SurfaceSearchPoint ].lpSurface, 
									offsetX, 
									offsetY - 1, 
									sizeX,
									sizeY,
									pal, usechg );
#endif

			// 现在の确保したサーフェスの枚数をカウント
			totalSurfaceCnt++;

			// すべて确保し終わったら
			if( totalSurfaceCnt >= totalSurface ){
				//　pNext にＮＵＬＬを入れて終了
				SurfaceInfo[ SurfaceSearchPoint ].pNext = NULL;
				// 检索位置を进ませる
				SurfaceSearchPoint++;
				
#if 0			// VRAMを优先使用バージョン

				// VRAMのサーフェスを检索するとき
				if( vramFullFlag == FALSE ){
					// リミットチェック
					if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
				}else{
					// 检索位置を戾す
					SurfaceSearchPoint = SurfaceSearchPointBak;
				}
				
#else			// VRAMとSYSRAMを同等级で使用バージョン

				// リミットチェック
				if( SurfaceSearchPoint >= SurfaceCnt ) SurfaceSearchPoint = 0;
#endif
				break;
				
			}else{
				// 今のサーフェスインフォ构造体のアドレスを学习
				prevSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				
				// 右端まで送ったら
				if( offsetX >= bmpWidth - SURFACE_WIDTH ){ 
					offsetX = 0;
					offsetY -= SURFACE_HEIGHT;
				}else{ 
					offsetX += SURFACE_WIDTH;
				}
			}
		}
		// 检索位置を进ませる
		SurfaceSearchPoint++;
		
#if 0	// VRAMを优先使用バージョン

		// VRAMのサーフェスを检索するとき
		if( vramFullFlag == FALSE ){
			// VRAMサーフェスの最后まで检索したとき
			if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
			// 一周检索したらVRAMに空きなし状态であきらめる
			if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_40, "确认", MB_OK );
#else
				//MessageBox( hWnd, "ＶＲＡＭ没有剩余空间。", "确认", MB_OK );
#endif
				// 检索位置をSYSTEMサーフェスが存在するところへ移动
				SurfaceSearchPoint = VramSurfaceCnt + 1;
				vramFullFlag = TRUE;
			}
		}
		// SYSTEMRAMのサーフェスを检索するとき
		if( vramFullFlag == TRUE ){
			// 最后まで检索したら
			if( SurfaceSearchPoint >= SurfaceCnt ){ 
				// 检索位置を戾す
				SurfaceSearchPoint = SurfaceSearchPointBak;
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_41, "确认", MB_OK );
#else
				//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
				break;
			}
		}
		
#else	// VRAMとSYSRAMを同等级で使用バージョン
		
		// 最后まで检索したら
		if( SurfaceSearchPoint >= SurfaceCnt ){ 
			// 最初に返回
			SurfaceSearchPoint = 0;
		}
		// 一周检索したらサーフェスに空きなし状态であきらめる
		if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_42, "确认", MB_OK );
#else
			//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
			char szBuffer[256];
			sprintf( szBuffer, "Surface不足。%d\n", SurfaceSearchPoint );         //MLHIDE
			OutputDebugString( szBuffer);
			break;
		}
	}
#endif
}

#ifdef PUK2_DIFFPAL_SURFACE
// サーフェースを探し、先头のポインタを取得する
SURFACE_INFO *SearchPaletteSurface( int bmpNo, int AnimPalNo, char palchg )
{
	SURFACE_INFO *lpsi = NULL;	 	// ループ用

	// 同じパレットのサーフェースがあるかチェック
	for( lpsi = SpriteInfo[ bmpNo ].lpSurfaceInfo;
		 lpsi;
		 lpsi = lpsi->otherPalSrf ){
		// アニメーションパレットが同じか？
		if ( lpsi->AnimPalNo != AnimPalNo ) continue;
		// パレットチェンジの确认
		if ( lpsi->palchg != palchg ) continue;

		return lpsi;
	}
	return NULL;
}

// サーフェースを使用可能状态にする
BOOL RelesePaletteSurface( int bmpNo, int AnimNo, BOOL palchg )
{
	SURFACE_INFO *prevlpsi = NULL;	// 前のサーフェスインフォ构造体のアドレス
	SURFACE_INFO *lpsi = NULL;	 	// ループ用

	prevlpsi = NULL;
	// 同じパレットのサーフェースがあるかチェック
	for( lpsi = SpriteInfo[ bmpNo ].lpSurfaceInfo;
		 lpsi;
		 prevlpsi = lpsi, lpsi = lpsi->otherPalSrf ){
		// アニメーションパレットが同じか？
		if ( lpsi->AnimPalNo != AnimNo ) continue;
		// パレットチェンジの确认
		if ( lpsi->palchg != palchg ) continue;

		break;
	}
	// 无いなら
	if ( !lpsi ) return FALSE;

	// 先ずリンクからはずす

	// 先头なら
	if ( prevlpsi == NULL ){
		SpriteInfo[ bmpNo ].lpSurfaceInfo = lpsi->otherPalSrf;
	}else{
		prevlpsi->otherPalSrf = lpsi->otherPalSrf;
	}
	lpsi->otherPalSrf = NULL;

	// サーフェスリストの初期化ループ
	for( ; lpsi != NULL; lpsi = lpsi->pNext ){
		// ＢＭＰ番号初期化
		lpsi->bmpNo = -1;
		// パレットデータ初期化
		lpsi->AnimPalNo = -1;
		lpsi->palchg = -1;
#ifdef _DEBUG
		// 现在使っているサーフェスの数マイナス
		SurfaceUseCnt--;
#endif
	}

	return TRUE;
}

// サーフェースを确保 ++++
void GetPaletteSurface( int bmpNo, int AnimPalNo, char palchg )
{
	int bmpWidth,bmpHeight;		 	// ＢＭＰのサイズ
	
	int offsetX, offsetY; 			// 送るＢＭＰのOFFセットポイント
	int surfaceCntX;				// 必要なサーフェスの横枚数
	int surfaceCntY;				// 必要なサーフェスの横枚数
	int totalSurface;				// 必要なサーフェスの総数
	int totalSurfaceCnt = 0;		// 现在の确保したサーフェスの枚数（ ループカウンタ ）
	int	SurfaceSearchPointBak = SurfaceSearchPoint; // 一周检索したら終了するため最初の位置を记忆
	int amariSizeX = FALSE;			// 横に余りがあるかフラグ
	int amariSizeY = FALSE;			// 縦に余りがあるかフラグ
	BOOL vramFullFlag = FALSE; 		// VRAMがいっぱいかどうか
	SURFACE_INFO *siwk = NULL;		// 作业用
	SURFACE_INFO *lpsi = NULL;		// 作业用
	SURFACE_INFO *prevlpsi = NULL;	// 前のサーフェスインフォ构造体のアドレス

	// 事前にGetBmpSize()で取得しておく
	bmpWidth  = SpriteInfo[ bmpNo ].width;	// ＢＭＰの横サイズ
	bmpHeight = SpriteInfo[ bmpNo ].height;	// ＢＭＰの横サイズ

	// 送るＢＭＰのOFFセットポイント
	offsetX = 0; 
	offsetY = bmpHeight;

	// 必要なサーフェスの横枚数计算
	surfaceCntX = ( bmpWidth + (SURFACE_WIDTH - 1) ) / SURFACE_WIDTH;

	// 必要なサーフェスの縦枚数计算
	surfaceCntY = ( bmpHeight + (SURFACE_HEIGHT - 1) ) / SURFACE_HEIGHT;

	// 必要なサーフェスの総数计算
	totalSurface  = surfaceCntX * surfaceCntY;

	// 同じ所に上书きするなら前回のものは消去する。
	RelesePaletteSurface( bmpNo, AnimPalNo, palchg );

	prevlpsi = NULL;
	// 空いているサーフェスを探す
	// サーフェスの数だけループ
	while( 1 ){
		siwk = &SurfaceInfo[ SurfaceSearchPoint ];
		// 使用できる状态なら
		if ( siwk->bmpNo == -1 ||
			 siwk->date < SurfaceDate - SURACE_BMP_DEATH_DATE ){
#ifdef _DEBUG		
			// 现在使っているサーフェスの数カウント
			SurfaceUseCnt++;
#endif
			// 上书きする时
			if( siwk->bmpNo != -1 ){
				// 前にいたＢＭＰとサーフェスのリンクを削除
				RelesePaletteSurface( siwk->bmpNo, siwk->AnimPalNo, siwk->palchg );
			}

			// サーフェスリストの先头アドレスを记忆するとき
			if( prevlpsi == NULL ){
				// 最后のを探す
				for( lpsi = SpriteInfo[ bmpNo ].lpSurfaceInfo;
					 lpsi;
					 prevlpsi = lpsi, lpsi = lpsi->otherPalSrf );

				// 一つもサーフェースが无いなら
				if ( prevlpsi == NULL ){
					// スプライトインフォに先头アドレスを记忆する
					SpriteInfo[ bmpNo ].lpSurfaceInfo = siwk;
				}else{
					// 最后に追加する
					prevlpsi->otherPalSrf = siwk;
				}
			}
			// つなげるとき
			else{
				// 覚えておいた前のサーフェスインフォ构造体にアドレスを教える
				prevlpsi->pNext = siwk;
			}
			// パレット番号とフェードのカウンターも学习
			siwk->PalNo = -1;
			siwk->PalCount = -1;

			// パレット情报を保存
			siwk->AnimPalNo = AnimPalNo;
			siwk->palchg = palchg;
			
			// ＢＭＰ番号を记忆する
			siwk->bmpNo = bmpNo;
			
			// OFFセット座标を学习
			siwk->offsetX = offsetX;
			siwk->offsetY = bmpHeight - offsetY;
			
			// 现在の确保したサーフェスの枚数をカウント
			totalSurfaceCnt++;

			// すべて确保し終わったら
			if( totalSurfaceCnt >= totalSurface ){
				//　pNext にＮＵＬＬを入れて終了
				siwk->pNext = NULL;
				// 检索位置を进ませる
				SurfaceSearchPoint++;
				
#if 0			// VRAMを优先使用バージョン

				// VRAMのサーフェスを检索するとき
				if( vramFullFlag == FALSE ){
					// リミットチェック
					if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
				}else{
					// 检索位置を戾す
					SurfaceSearchPoint = SurfaceSearchPointBak;
				}
				
#else			// VRAMとSYSRAMを同等级で使用バージョン

				// リミットチェック
				if( SurfaceSearchPoint >= SurfaceCnt ) SurfaceSearchPoint = 0;
#endif
				break;
				
			}else{
				// 今のサーフェスインフォ构造体のアドレスを学习
				prevlpsi = siwk;
				
				// 右端まで送ったら
				if( offsetX >= bmpWidth - SURFACE_WIDTH ){ 
					offsetX = 0;
					offsetY -= SURFACE_HEIGHT;
				}else{ 
					offsetX += SURFACE_WIDTH;
				}
			}
		}
		// 检索位置を进ませる
		SurfaceSearchPoint++;
		
#if 0	// VRAMを优先使用バージョン

		// VRAMのサーフェスを检索するとき
		if( vramFullFlag == FALSE ){
			// VRAMサーフェスの最后まで检索したとき
			if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
			// 一周检索したらVRAMに空きなし状态であきらめる
			if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_43, "确认", MB_OK );
#else
				//MessageBox( hWnd, "ＶＲＡＭ没有剩余空间。", "确认", MB_OK );
#endif
				// 检索位置をSYSTEMサーフェスが存在するところへ移动
				SurfaceSearchPoint = VramSurfaceCnt + 1;
				vramFullFlag = TRUE;
			}
		}
		// SYSTEMRAMのサーフェスを检索するとき
		if( vramFullFlag == TRUE ){
			// 最后まで检索したら
			if( SurfaceSearchPoint >= SurfaceCnt ){ 
				// 检索位置を戾す
				SurfaceSearchPoint = SurfaceSearchPointBak;
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_44, "确认", MB_OK );
#else
				//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
				break;
			}
		}
		
#else	// VRAMとSYSRAMを同等级で使用バージョン
		
		// 最后まで检索したら
		if( SurfaceSearchPoint >= SurfaceCnt ){ 
			// 最初に返回
			SurfaceSearchPoint = 0;
		}
		// 一周检索したらサーフェスに空きなし状态であきらめる
		if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_45, "确认", MB_OK );
#else
			//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
			char szBuffer[256];
			sprintf( szBuffer, "Surface不足。%d\n", SurfaceSearchPoint );         //MLHIDE
			OutputDebugString( szBuffer);
			break;
		}
	}
#endif
}

//----------------------------------//

// 絵の大きさを取得、サーフェースを确保 ++++
BOOL GetBmpSize( int bmpNo )
{
	// リミットチェック
	if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) return FALSE;

	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if ( realGetImage( bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &RealBinPalSize ) == FALSE ){
							
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_46, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo = -1;
			pRealBinBits = 0;
			RealBinWidth = RealBinHeight = 0;
			RealBinPalSize = 0;
			return FALSE;
		}
		RealBinBmpNo=bmpNo;
		// ＢＭＰのサイズを记忆
		SpriteInfo[ bmpNo ].width = RealBinWidth;
		SpriteInfo[ bmpNo ].height = RealBinHeight;

		// 絵のサイズを読み込んである事を示すために
		// 假にサーフェースを确保しておく
		GetPaletteSurface( bmpNo, 0, 2 );
	}
	return TRUE;
}
#else
// サーフェースを确保 ++++
void GetSurface( int bmpNo, LPBITMAPINFO lpBmpInfo )
{
	int bmpWidth,bmpHeight;		 	// ＢＭＰのサイズ
	
	int offsetX, offsetY; 			// 送るＢＭＰのOFFセットポイント
	int surfaceCntX;				// 必要なサーフェスの横枚数
	int surfaceCntY;				// 必要なサーフェスの横枚数
	int totalSurface;				// 必要なサーフェスの総数
	int totalSurfaceCnt = 0;		// 现在の确保したサーフェスの枚数（ ループカウンタ ）
	int	SurfaceSearchPointBak = SurfaceSearchPoint; // 一周检索したら終了するため最初の位置を记忆
	int amariSizeX = FALSE;			// 横に余りがあるかフラグ
	int amariSizeY = FALSE;			// 縦に余りがあるかフラグ
	BOOL vramFullFlag = FALSE; 		// VRAMがいっぱいかどうか
	SURFACE_INFO *prevSurfaceInfo = NULL; 	// 前のサーフェスインフォ构造体のアドレス
	
	if (lpBmpInfo){	// ファイルから読み込む时
		bmpWidth  = lpBmpInfo->bmiHeader.biWidth;	// ＢＭＰの横サイズ
		bmpHeight = lpBmpInfo->bmiHeader.biHeight; 	// ＢＭＰの縦サイズ
	}else{			// bin から読み込む时
		bmpWidth  = RealBinWidth;					// ＢＭＰの横サイズ
		bmpHeight = RealBinHeight;					// ＢＭＰの縦サイズ
	}

	// 送るＢＭＰのOFFセットポイント
	offsetX = 0; 
	offsetY = bmpHeight;
	
	// 必要なサーフェスの横枚数计算
	surfaceCntX = bmpWidth / SURFACE_WIDTH;
	
	// 横に余りがあったら半端サイズを记忆
	if( ( amariSizeX = bmpWidth % SURFACE_WIDTH ) ){ 
		surfaceCntX++;		// もうひとつ必要
	}
	
	// 必要なサーフェスの縦枚数计算
	surfaceCntY = bmpHeight / SURFACE_HEIGHT;
	
	// 縦に余りがあったら半端サイズを记忆
	if( ( amariSizeY = bmpHeight % SURFACE_HEIGHT ) ){ 
		surfaceCntY++;		// もうひとつ必要
	}
	// 必要なサーフェスの総数计算
	totalSurface  = surfaceCntX * surfaceCntY;
	
	if (lpBmpInfo){	// ファイルから読み込む时
		// 横幅を４のバウンダリにする
		if( ( lpBmpInfo->bmiHeader.biWidth & 3 ) ){
			lpBmpInfo->bmiHeader.biWidth += 4 - lpBmpInfo->bmiHeader.biWidth & 3;
		}
	}else{			// bin から読み込む时
		// 横幅を４のバウンダリにする
		if( ( RealBinWidth & 3 ) ){
			RealBinWidth += 4 - RealBinWidth & 3;
		}
	}

	// 同じ所に上书きするなら前回のものは消去する。
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo != NULL ){
			prevSurfaceInfo = SpriteInfo[ bmpNo ].lpSurfaceInfo;
			// サーフェスリストの初期化ループ
			for( ; prevSurfaceInfo != NULL;
				prevSurfaceInfo = prevSurfaceInfo->pNext ){
				
			// ＢＭＰ番号初期化
				prevSurfaceInfo->bmpNo = -1;
				
#ifdef _DEBUG		
				// 现在使っているサーフェスの数マイナス
				SurfaceUseCnt--;
#endif
			}
			SpriteInfo[ bmpNo ].lpSurfaceInfo = NULL;
	}

	// 空いているサーフェスを探す
	// サーフェスの数だけループ
	while( 1 ){
		// 使用できる状态なら
		if( SurfaceInfo[ SurfaceSearchPoint ].date < SurfaceDate - SURACE_BMP_DEATH_DATE ){
#ifdef _DEBUG		
			// 现在使っているサーフェスの数カウント
			SurfaceUseCnt++;
#endif
			// 上书きする时
			if( SurfaceInfo[ SurfaceSearchPoint ].bmpNo != -1 ){
				// サーフェスインフォ构造体のアドレス
				SURFACE_INFO *lpSurfaceInfo;
				
				// サーフェスリストの先头アドレスを记忆
				lpSurfaceInfo = SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo;
				
				// 前にいたＢＭＰとサーフェスのリンクを削除
				SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo = NULL;

				// サーフェスリストの初期化ループ
				for( ; lpSurfaceInfo != NULL;
					lpSurfaceInfo = lpSurfaceInfo->pNext ){
					
					// ＢＭＰ番号初期化
					lpSurfaceInfo->bmpNo = -1;
					
#ifdef _DEBUG		
					// 现在使っているサーフェスの数マイナス
					SurfaceUseCnt--;
#endif
				}
				
			}
			// サーフェスリストの先头アドレスを记忆するとき
			if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){
				// スプライトインフォに先头アドレスを记忆する
				SpriteInfo[ bmpNo ].lpSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				// パレット番号とフェードのカウンターも学习
				SurfaceInfo[ SurfaceSearchPoint ].PalNo = -1;
				SurfaceInfo[ SurfaceSearchPoint ].PalCount = -1;
				SurfaceInfo[ SurfaceSearchPoint ].AnimPalNo = 0;
			}else{ // つなげるとき
				// 覚えておいた前のサーフェスインフォ构造体にアドレスを教える
				prevSurfaceInfo->pNext = &SurfaceInfo[ SurfaceSearchPoint ];
				// パレット番号とフェードのカウンターも学习
				SurfaceInfo[ SurfaceSearchPoint ].PalNo = -1;
				SurfaceInfo[ SurfaceSearchPoint ].PalCount = -1;
				SurfaceInfo[ SurfaceSearchPoint ].AnimPalNo = 0;
			}
			
			// ＢＭＰ番号を记忆する
			SurfaceInfo[ SurfaceSearchPoint ].bmpNo = bmpNo;
			
			// OFFセット座标を学习
			SurfaceInfo[ SurfaceSearchPoint ].offsetX = offsetX;
			SurfaceInfo[ SurfaceSearchPoint ].offsetY = bmpHeight - offsetY;
			
			// 现在の确保したサーフェスの枚数をカウント
			totalSurfaceCnt++;

			// すべて确保し終わったら
			if( totalSurfaceCnt >= totalSurface ){
				//　pNext にＮＵＬＬを入れて終了
				SurfaceInfo[ SurfaceSearchPoint ].pNext = NULL;
				// 检索位置を进ませる
				SurfaceSearchPoint++;
				
#if 0			// VRAMを优先使用バージョン

				// VRAMのサーフェスを检索するとき
				if( vramFullFlag == FALSE ){
					// リミットチェック
					if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
				}else{
					// 检索位置を戾す
					SurfaceSearchPoint = SurfaceSearchPointBak;
				}
				
#else			// VRAMとSYSRAMを同等级で使用バージョン

				// リミットチェック
				if( SurfaceSearchPoint >= SurfaceCnt ) SurfaceSearchPoint = 0;
#endif
				break;
				
			}else{
				// 今のサーフェスインフォ构造体のアドレスを学习
				prevSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				
				// 右端まで送ったら
				if( offsetX >= bmpWidth - SURFACE_WIDTH ){ 
					offsetX = 0;
					offsetY -= SURFACE_HEIGHT;
				}else{ 
					offsetX += SURFACE_WIDTH;
				}
			}
		}
		// 检索位置を进ませる
		SurfaceSearchPoint++;
		
#if 0	// VRAMを优先使用バージョン

		// VRAMのサーフェスを检索するとき
		if( vramFullFlag == FALSE ){
			// VRAMサーフェスの最后まで检索したとき
			if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
			// 一周检索したらVRAMに空きなし状态であきらめる
			if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_47, "确认", MB_OK );
#else
				//MessageBox( hWnd, "ＶＲＡＭ没有剩余空间。", "确认", MB_OK );
#endif
				// 检索位置をSYSTEMサーフェスが存在するところへ移动
				SurfaceSearchPoint = VramSurfaceCnt + 1;
				vramFullFlag = TRUE;
			}
		}
		// SYSTEMRAMのサーフェスを检索するとき
		if( vramFullFlag == TRUE ){
			// 最后まで检索したら
			if( SurfaceSearchPoint >= SurfaceCnt ){ 
				// 检索位置を戾す
				SurfaceSearchPoint = SurfaceSearchPointBak;
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_48, "确认", MB_OK );
#else
				//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
				break;
			}
		}
		
#else	// VRAMとSYSRAMを同等级で使用バージョン
		
		// 最后まで检索したら
		if( SurfaceSearchPoint >= SurfaceCnt ){ 
			// 最初に返回
			SurfaceSearchPoint = 0;
		}
		// 一周检索したらサーフェスに空きなし状态であきらめる
		if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_49, "确认", MB_OK );
#else
			//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
			char szBuffer[256];
			sprintf( szBuffer, "Surface不足。%d\n", SurfaceSearchPoint );         //MLHIDE
			OutputDebugString( szBuffer);
			break;
		}
	}
#endif
}

//----------------------------------//

// 絵の大きさを取得、サーフェースを确保 ++++
BOOL GetBmpSize( int bmpNo )
{
	// リミットチェック
	if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) return FALSE;

	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if ( realGetImage( bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &RealBinPalSize ) == FALSE ){
							
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_50, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo = -1;
			pRealBinBits = 0;
			RealBinWidth = RealBinHeight = 0;
			RealBinPalSize = 0;
			return FALSE;
		}
		RealBinBmpNo=bmpNo;
		// ＢＭＰのサイズを记忆
		SpriteInfo[ bmpNo ].width = RealBinWidth;
		SpriteInfo[ bmpNo ].height = RealBinHeight;

		// サーフェースを确保
		GetSurface( bmpNo, NULL );
	}
	return TRUE;
}
#endif

//----------------------------------//

// 矩形范围のパレットを确认 ++++
char CheckSurfacePal( int bmpNo, short lu, short lv, short lw, short lh, int *p_PalNo, int *p_Palcnt, int AnimPalNo )
{
	SURFACE_INFO *psfi,*psfi2;	// サーフェース参照用
	char wq,hq;					// 絵の縦横のサーフェースの枚数
	char wfs,hfs;				// 切り取り始めのサーフェースの位置
	char wes,hes;				// 切り終わりのサーフェースの位置
	int i,j;					// ループカウンタ
	char flg;

#ifdef SUPPORT_16BIT
	if (SpriteInfo[ bmpNo ].width<=lu) return FALSE;
	if (SpriteInfo[ bmpNo ].height<=lv) return FALSE;
	if (lu+lw<0) return FALSE;
	if (lv+lh<0) return FALSE;

	if (lu<0) lu=0;
	if (lv<0) lv=0;
	if (lu+lw>=SpriteInfo[ bmpNo ].width) lw=SpriteInfo[ bmpNo ].width-lu;
	if (lv+lh>=SpriteInfo[ bmpNo ].height) lh=SpriteInfo[ bmpNo ].height-lv;

	// 絵の縦横のサーフェースの枚数取得
	wq=(SpriteInfo[ bmpNo ].width+SurfaceSizeX-1)/SurfaceSizeX;
	hq=(SpriteInfo[ bmpNo ].height+SurfaceSizeY-1)/SurfaceSizeY;

	// 切り取り始めのサーフェースの位置取得
	wfs=lu/SurfaceSizeX;
	hfs=lv/SurfaceSizeY;

	// 切り終わりのサーフェースの位置取得
	wes=(lu+lw+SurfaceSizeX-1)/SurfaceSizeX;
	hes=(lv+lh+SurfaceSizeY-1)/SurfaceSizeY;

	// 最初のサーフェースに移动
	psfi=SpriteInfo[ bmpNo ].lpSurfaceInfo;
	for(j=0;j<hfs;j++){
		for(i=0;i<wq;i++){
			if (psfi) psfi=psfi->pNext;
			else break;
		}
	}
	for(i=0;i<wfs;i++){
		if (psfi) psfi=psfi->pNext;
		else break;
	}

	if (!psfi) return 0;

	if (p_PalNo) *p_PalNo=psfi->PalNo;
	if (p_Palcnt) *p_Palcnt=psfi->PalCount;

	flg=0;
	for(j=hfs;j<hes;j++){
		psfi2=psfi;
		for(i=wfs;i<wes;i++){
			if (!psfi) break;
			if (
				( displayBpp != 8 && psfi->PalNo != PalState.palNo )||
				( displayBpp != 8 && psfi->PalCount != PalState.count )
				|| ( psfi->AnimPalNo != AnimPalNo )
				){
				if (p_PalNo){if (*p_PalNo!=psfi->PalNo) *p_PalNo=-1; }
				if (p_Palcnt){if (*p_Palcnt!=psfi->PalCount) *p_Palcnt=-1; }
				if (psfi->AnimPalNo!=AnimPalNo) return 2;

				flg=1;
				if ( psfi->PalNo==-1 ) return 2;
			}
			psfi=psfi->pNext;
		}
		if (j<hq){
			psfi=psfi2;
			for(i=0;i<wq;i++){
				if (psfi) psfi=psfi->pNext;
				else break;
			}
		}
	}
	if (flg) return 1;
#endif
	return 0;
}

//----------------------------------//

// サーフェースへ絵の読み込み ++++
void AllocateBmpToSurface_PUK2( int bmpNo, short lu, short lv, short lw, short lh, LPBITMAPINFO lpBmpInfo, char usechg )
{
	SURFACE_INFO *psfi,*psfi2;	// サーフェース参照用
	char wq,hq;					// 絵の縦横のサーフェースの枚数
	char wfs,hfs;				// 切り取り始めのサーフェースの位置
	char wes,hes;				// 切り終わりのサーフェースの位置
	int ofX,ofY;				// 縦横の余り
	int i,j;					// ループカウンタ

	if (SpriteInfo[ bmpNo ].width<=lu) return;
	if (SpriteInfo[ bmpNo ].height<=lv) return;
	if (lu+lw<0) return;
	if (lv+lh<0) return;

	if (lu<0) lu=0;
	if (lv<0) lv=0;
	if (lu+lw>=SpriteInfo[ bmpNo ].width) lw=SpriteInfo[ bmpNo ].width-lu;
	if (lv+lh>=SpriteInfo[ bmpNo ].height) lh=SpriteInfo[ bmpNo ].height-lv;

	// 絵の縦横のサーフェースの枚数取得
	wq=(SpriteInfo[ bmpNo ].width+SurfaceSizeX-1)/SurfaceSizeX;
	hq=(SpriteInfo[ bmpNo ].height+SurfaceSizeY-1)/SurfaceSizeY;

	// 切り取り始めのサーフェースの位置取得
	wfs=lu/SurfaceSizeX;
	hfs=lv/SurfaceSizeY;

	// 切り終わりのサーフェースの位置取得
	wes=(lu+lw+SurfaceSizeX-1)/SurfaceSizeX;
	hes=(lv+lh+SurfaceSizeY-1)/SurfaceSizeY;

	// 最初のサーフェースに移动
	psfi=SpriteInfo[ bmpNo ].lpSurfaceInfo;
	for(j=0;j<hfs;j++){
		for(i=0;i<wq;i++){
			if (psfi) psfi=psfi->pNext;
			else break;
		}
	}
	for(i=0;i<wfs;i++){
		if (psfi) psfi=psfi->pNext;
		else break;
	}

	for(j=hfs;j<hes;j++){
		psfi2=psfi;
		for(i=wfs;i<wes;i++){
			if (!psfi) break;
			if (
				( displayBpp != 8 && psfi->PalNo != PalState.palNo )||
				( displayBpp != 8 && psfi->PalCount != PalState.count )
				){
				// パレット番号とフェードのカウンターも学习
				psfi->PalNo = PalState.palNo;
				psfi->PalCount = PalState.count;
				psfi->AnimPalNo = 0;

				// 絵の左端、上端からのOFFセット值を求める
				ofX=psfi->offsetX,	ofY=SpriteInfo[ bmpNo ].height-psfi->offsetY;
			}
			psfi=psfi->pNext;
		}
		if (j<hq){
			psfi=psfi2;
			for(i=0;i<wq;i++){
				if (psfi) psfi=psfi->pNext;
				else break;
			}
		}
	}
}


#include "../PUK2/newDraw/anim_tbl_PUK2.h"

// 赤
#define PPLR 2
// 緑
#define PPLG 1
// 青
#define PPLB 0

// この变数の中身を见てアニメーション用パレットデータを作成する
int LoadSprPal[]={
	0,		// 予约(要素 0 は、このパレットを使用しないことを示す为、未使用)
	110300,	110301,	110302,	110303,	110304,	110305,	110306,	110307,	110308,	110309,
	110310,	110311,	110312,	110313,	110314,	110315,	110316,	110317,	110318,	110319,
	110320,	110321,	110322,	110323,	110324,	110325,	110326,	110327,	110328,	110329,
	110330,	110331,	110332,	110333,	110334,	110335,	110336,	110337,	110338,	110339,
	110340,	110341,	110342,	110343,	110344,	110345,	110346,	110347,	110348,	110349,
	110350,	110351,	110352,	110353,	110354,	110355,	110356,	110357,	110358,	110359,
	110360,	110361,	110362,	110363,	110364,	110365,	110366,	110367,	110368,	110369,
	110370,	110371,	110372,	110373,	110380,	110381,	110382,	110383,	110384,	110385,
	110386,	110387,	110388,	110389,	110390,	110391,	110392,	110393,	110394,	110395,
	110400,	110401,	110402,	110403,	110404,	110405,	110406,	110407,	110408,	110409,
	110410,	110411,	110412,	110413,	110414,	110420,	110421,	110422,	110423,	110424,
	110425,	110426,	110427,	110428,	110429,	110430,	110431,	110432,	110433,	110434,
	110435,	110436,	110437,	110438,	110439,	110440,	110441,	110442,	110443,	110444,
	110445,	110446,	110447,	110448,	110449,	110450,	110451,	110452,	110453,	110454,
	110455,	110456,	110457,	110458,	110459,	110460,	110461,	110462,	110463,	110464,
	110465,	110466,	110467,	110468,	110500,	110501,	110600,	110601,	110602,	110603,
	110604,	110605,	110606,	110607,	110608,	110609,	110610,	110611,	110612,	110613,
	110614,	110615,	110616,	110617,	110618,	110619,	110620,	110621,	110622,	110623,
	110624,	110625,	110626,	110627,	110628,	110629,	110630,	110631,	110632,	110633,
	110634,	110635,	110650,	110651,	110652,	110653,	110654,	110655,	110656,	110657,
	110658,	110659,	110660,	110661,	110662,	110663,	110664,	110665,	110666,	110667,
	110668,	110669,	110670,	110671,	110672,	110673,	110674,	110675,	110676,	110677,
	110678,	110679,	110680,	110681,	110682,	110683,	110684,	110685,	110700,	110701,
	110702,	110703,	110704,	110705,	110706,	110707,	110708,	110709,	110710,	110711,
	110712,	110713,	110714,	110715,	110750,	110751,	110752,	110753,	110754,	110755,
	110756,	110757,	110758,	110759,	110760,	110761,	110762,	110763,	110764,	110765,
	110766,	113000,	113001,	113002,	113003,	113004,	113005,	113006,	113007,	113008,
	113009,	113010,	113011,	113012,	113013,	113014,	113015,	113016,	113017,	113018,
	113019,	113020,	113021,	113022,	113023,	113024,	113025,	113026,	113027,	113028,
	113029,	113030,	113031,	113032,	113033,	113034,	113035,	113036,	113037,	113038,
	113039,	113040,	113041,	113042,	113043,	113044,	113045,	113046,	113047,	113048,
	113049,	113050,	113051,	113052,	113053,	113054,	113055,	113056,	113057,	113058,
	113059,	113060,	113061,	113062,	113063,	113064,	113065,	113066,	113067,	113068,
	113069,	113070,	113071,	113072,	113073,	113074,	113075,	113076,	113077,	113078,
	113079,	113080,	113081,	113082,	113083,	113084,	113085,	113086,	113087,	113088,
	113089,	113090,	113091,	113092,	113093,	113094,	113095,	113096,	113097,	113098,
	113099,	113100,	113101,	113102,	113103,	113104,	113105,	113106,	113107,	113108,
	113109,	113110,	113111,	113112,	113113,	113114,	113115,	113116,	113117,	113118,
	113119,	113120,	113121,	113122,	113123,	113124,	113125,	113126,	113127,	113128,
	113129,	113130,	113131,	113132,	113133,	113134,	113135,	113136,	113137,	113138,
	113139,	113140,	113141,	113142,	113143,	113144,	113145,	113146,	113147,	113148,
	113149,	113150,	113151,	113152,	113153,	113154,	113155,	113156,	113157,	113158,
	113159,	113160,	113161,	113162,	113163,	113164,	113165,	113166,	113167,	113168,
	113169,	113170,	113171,	113172,	113173,	113174,	113175,	113176,	113177,	113178,
	113179,	113180,	113181,	113182,	113183,	113184,	113185,	113186,	113187,	113188,
	113189,	113190,	113191,	113192,	113193,	113194,	113195,	113196,	113197,	113198,
	113199,	113200,	113201,	113202,	113203,	113204,	113205,	113206,	113207,	113208,
	113209,	113210,	113211,	113212,	113213,	113214,	113215,	113216,	113217,	113218,
	113219,	113220,	113221,	113222,	113223,	113224,	113225,	113226,	113227,	113228,
	113229,	113230,	113231,	113232,	113233,	113234,	113235,	113236,	113237,	113238,
	113239,	113240,	113241,	113242,	113243,	113244,	113245,	113246,	113247,	113248,
	113249,	113250,	113251,	113252,	113253,	113254,	113255,	113256,	113257,	113258,
	113259,	113260,	113261,	113262,	113263,	113264,	113265,	113266,	113267,	113268,
	113269,	113270,	113271,	113272,	113273,	113274,	113275,	113276,	113277,	113278,
	113279,	113280,	113281,	113282,	113283,	113284,	113285,	113286,	113287,	113288,
	113289,	113290,	113291,	113292,	113293,	113294,	113295,	113296,	113297,	113298,
	113299,	113300,	113301,	113302,	113303,	113304,	113305,	113306,	113307,	113308,
	113309,	113310,	113311,	113312,	113313,	113314,	113315,	113316,	113317,	113318,
	113319,	113320,	113321,	113322,	113323,	113324,	113325,	113326,	113327,	113328,
	113329,	113330,	113331,	113332,	113333,	113334,	113335,	113336,	113337,	113338,
	113339,	113340,	113341,	113342,	113343,	113344,	113345,	113346,	113347,	113348,
	113349,	113350,	113351,	113352,	113353,	113354,	113355,	113356,	113357,	113358,
	113359,	113360,	113361,	113362,	113363,	113364,	113365,	113366,	113367,	113368,
	113369,	113370,	113371,	113372,	113373,	113374,	113375,	113376,	113377,	113378,
	113379,	113380,	113381,	113382,	113383,	113384,	113385,	113386,	113387,	113388,
	113389,	113390,	113391,	113392,	113393,	113394,	113395,	113396,	113397,	113398,
	113399,	113400,	113401,	113402,	113403,	113404,	113405,	113406,	113407,	113408,
	113409,	113410,	113411,	113412,	113413,	113414,	113415,	113416,	113417,	113418,
	113419,	113420,	113421,	113422,	113423,	113424,	113425,	113426,	113427,	113428,
	113429,	113430,	113431,	113432,	113433,	113434,	113435,	113436,	113437,	113438,
	113439,	113440,	113441,	113442,	113443,	113444,	113445,	113446,	113447,	113448,
	113449,	113450,	113451,	113452,	113453,	113454,	113455,	113456,	113457,	113458,
	113459,	113460,	113461,	113462,	113463,	113464,	113465,	113466,	113467,	113468,
	113469,	113470,	113471,	113472,	113473,	113474,	113475,	113476,	113477,	113478,
	113479,	113480,	113481,	113482,	113483,	113484,	113485,	113486,	113487,	113488,
	113489,	113490,	113491,	113492,	113493,	113494,	113495,	113496,	113497,	113498,
	113499,	113500,	113501,	113502,	113503,	113504,	113505,	113506,	113507,	113508,
	113509,	113510,	113511,	113512,	113513,	113514,	113515,	113516,	113517,	113518,
	113519,	113520,	113521,	113522,	113523,	113524,	113525,	113526,	113527,	113528,
	113529,	113530,	113531,	113532,	113533,	113534,	113535,	113536,	113537,	113538,
	113539,	113540,	113541,	113542,	113543,	113544,	113545,	113546,	113547,	113548,
	113549,	113550,	113551,	113552,	113553,	113554,	113555,	113556,	113557,	113558,
	113559,	113560,	113561,	113562,	113563,	113564,	113565,	113566,	113567,	113568,
	113569,	113570,	113571,	113572,	113573,	113574,	113575,	113576,	113577,	113578,
	113579,	113580,	113581,	113582,	113583,	113584,	113585,	113586,	113587,	113588,
	113589,	113590,	113591,	113592,	113593,	113594,	113595,	113596,	113597,	113598,
	113599,	113600,	113601,	113602,	113603,	113604,	113605,	113606,	113607,	113608,
	113609,	113610,	113611,	113612,	113613,	113614,	113615,	113616,	113617,	113618,
	113619,	113620,	113621,	113622,	113623,	113624,	113625,	113626,	113627,	113628,
	113629,	113630,	113631,	113632,	113633,	113634,	113635,	113636,	113637,	113638,
	113639,	113640,	113641,	113642,	113643,	113644,	113645,	113646,	113647,	113648,
	113649,	113650,	113651,	113652,	113653,	113654,	113655,	113656,	113657,	113658,
	113659,	113660,	113661,	113662,	113663,	113664,	113665,	113666,	113667,	113668,
	113669,	113670,	113671,	113672,	113673,	113674,	113675,	113676,	113677,	113678,
	113679,	113680,	113681,	113682,	113683,	113684,	113685,	113686,	113687,	113688,
	113689,	113690,	113691,	113692,	113693,	113694,	113695,	113696,	113697,	113698,
	113699,	113700,	113701,	113702,	113703,	113704,	113705,	113706,	113707,	113708,
	113709,	113710,	113711,	113712,	113713,	113714,	113715,	113716,	113717,	113718,
	113719,	113720,	113721,	113722,	113723,	113724,	113725,	113726,	113727,	113728,
	113729,	113730,	113731,	113732,	113733,	113734,	113735,	113736,	113737,	113738,
	113739,	113740,	113741,	113742,	113743,	113744,	113745,	113746,	113747,	113748,
	113749,	113750,	113751,	113752,	113753,	113754,	113755,	113756,	113757,	113758,
	113759,	113760,	113761,	113762,	113763,	113764,	113765,	113766,	113767,	113768,
	113769,	113770,	113771,	113772,	113773,	113774,	113775,	113776,	113777,	113778,
	113779,	113780,	113781,	113782,	113783,	113784,	113785,	113786,	113787,	113788,
	113789,	113790,	113791,	113792,	113793,	113794,	113795,	113796,	113797,	113798,
	113799,	113800,	113801,	113802,	113803,	113804,	113805,	113806,	113807,	113808,
	113809,	113810,	113811,	113812,	113813,	113814,	113815,	113816,	113817,	113818,
	113819,	113820,	113821,	113822,	113823,	113824,	113825,	113826,	113827,	113828,
	113829,	113830,	113831,	113832,	113833,	113834,	113835,	113836,	113837,	113838,
	113839,	113840,	113841,	113842,	113843,	113844,	113845,	113846,	113847,	113848,
	113849,	113850,	113851,	113852,	113853,	113854,	113855,	113856,	113857,	113858,
	113859,	113860,	113861,	113862,	113863,	113864,	113865,	113866,	113867,	113868,
	113869,	113870,	113871,	113872,	113873,	113874,	113875,	113876,	113877,	113878,
	113879,	113880,	113881,	113882,	113883,	113884,	113885,	113886,	113887,	113888,
	113889,	113890,	113891,	113892,	113893,	113894,	113895,	113896,	113897,	113898,
	113899,	113900,	113901,	113902,	113903,	113904,	113905,	113906,	113907,	113908,
	113909,	113910,	113911,	113912,	113913,	113914,	113915,	113916,	113917,	113918,
	113919,	113920,	113921,	113922,	113923,	113924,	113925,	113926,	113927,	113928,
	113929,	113930,	113931,	113932,	113933,	113934,	113935,	113936,	113937,	113938,
	113939,	113940,	113941,	113942,	113943,	113944,	113945,	113946,	113947,	113948,
	113949,	113950,	113951,	113952,	113953,	113954,	113955,	113956,	113957,	113958,
	113959,	113960,	113961,	113962,	113963,	113964,	113965,	113966,	113967,	113968,
	113969,	113970,	113971,	113972,	113973,	113974,	113975,	113976,	113977,	113978,
	113979,	113980,	113981,	113982,	113983,	113984,	113985,	113986,	113987,	113988,
	113989,	113990,	113991,	113992,	113993,	113994,	113995,	113996,	113997,	113998,
	113999,
	// ２期
	110401,	110402,	110403,	110404,	110405,	110406,	110407,	110408,	110409,	110410,
	110411,	110412,	110414,
	// ３期
	110504,
	// ４期
	110376,	110377,	110378,	110379,	110469,	110470,	110471,	110472,	110473,	110474,
	110475,	110476,	110477,
	// ＰＵＫ３の１期の１
	110520,	110521,	110522,	110523,	110524,	110525,	110526,	110527,	110528,	110529,
	110530,	110531,	110532,	110533,	110534,	110535,	110536,	110537,	110538,	110539,
	110540,	110541,	110542,	110543,	110544,	110545,	110546,	110547,	110548,	110549,
	110550,	110551,	110552,	110553,	110554,	110555,	110556,	110557,	110558,	110559,
	110560,	110561,	110562,	110563,	110564,	110565,	110566,	110567,	110568,	110569,
	110570,	110571,	110572,	110573,	110574,	110575,	110576,	110577,	110578,	110579,
	110580,	110581,	110582,	110583,	110584,	110585,	114165,	114166,	114167,	114168,
	114169,	114170,	114171,	114172,	114173,	114174,	114175,	114176,	114177,	114178,
	114179,	114180,	114181,	114182,	114183,	114184,	114185,	114186,	114187,	114188,
	114189,	114190,	114191,	114192,	114193,	114194,	114195,	114196,	114197,	114198,
	114199,
	// ５期
	110767,	110768,	110769,
	// ＰＵＫ３の１期の２
	110505,	110506,	110507,	110508,
	// ５期の続き
	110396,	110397,	110398,	110399,
	// ＰＵＫ３の１期の３
	110509,	110510,	110511,	110512,	110513,	110514,	110586,	110587,	110588,	110589,
	110590,	110591,	110592,	110593,	110594,	110595,	110596,	110597,	110598,	110599,
	// ＰＵＫ３の１期の５
	110770, 110771, 110772, 110773, 110774, 110775, 110776, 110777, 110778, 110779,
	110780, 110781, 110782, 110783, 110784, 110785, 110786, 110787, 110788, 110789,
	110790, 110791, 110792, 110793, 110794, 110795, 110796, 110797, 110798, 110799,
	// ＰＵＫ３の１期の６
	114200,
	// ＰＵＫ３の３期
	114201, 114202, 114203, 114204, 114205, 114206, 114207, 114208, 114209, 114210,
	114211, 114212, 114213,
	// ＰＵＫ３の４期
	114214, 114215, 114216, 114217, 114218, 114219, 114220, 114221, 114222, 114223,
	// ＰＵＫ３の５期
	114224, 110599,
#ifdef TW_CHARA
	//台服新增加的隐藏调色板动画编号,包括新人物
	110400, 104332, 104333, 104334, 104344, 104345, 104346, 104347, 104348, 104349, 
	104350, 104351, 104352, 104339, 104340, 104341, 104342, 104343, 104353, 104354, 
	104355, 104356, 104357, 104358, 104359, 104360, 104361, 104362, 104363, 104364, 
	104365, 104366, 104367, 104368, 104369, 104370, 104371, 104372, 104373, 104374, 
	104375, 104376, 104377, 104378, 104379, 104380, 104381, 104382, 104383, 104384,
	104385, 104386, 104387, 104388, 104389, 104390, 104391, 104392, 104400, 104406, 
	104412, 104418, 104424, 104430, 104436, 104442, 104448, 104454, 104460, 104466, 
	104472, 104478, 104484, 104490, 104496, 104502, 104508, 104514, 104393, 104394, 
	104395, 104396, 104397, 104398, 104399, 104400, 104401, 104402, 104520, 104521, 
	104522, 104523, 104524, 104525, 104526, 104527, 104528, 104529, 104530, 104531, 
	104532, 104533, 104534, 104535, 104536, 104537, 104538, 104539, 104540, 104541, 
	104542, 104543, 104544, 104545, 104546, 104547, 104548, 104549, 104550, 104551,
	104552, 104553, 104554, 104555, 104556, 104557, 104558, 104559, 104560, 104335, 
	104336, 104561, 104562, 104563, 104564, 104565, 104566, 104567, 104568, 104569, 
	104570, 104571, 104572, 104573, 104574, 104575, 104576, 104577, 104578, 104579, 
	104580, 104581, 104582, 104583, 104584, 104585, 104586, 104587, 104588, 104589, 
	104590, 104591, 104592, 104593, 104594, 104595, 104596, 104597, 104598, 104599, 
	104600, 104601, 104602, 104603, 104604, 104605, 104606, 104607, 104608, 104609, 
	104610, 104611, 104612, 104613, 104614, 104615, 104616, 104617, 104618, 104619,
	104620, 104621, 104622, 104623, 104624, 104625, 104626, 104669, 104670, 104671, 
	104672, 104673, 104674, 104675, 104676, 104677, 104678, 104679, 104680, 104681, 
	104682, 104683, 104684, 104337, 104338, 104627, 104628, 104629, 104630, 104631, 
	104632, 104633, 104634, 104635, 104636, 104637, 104638, 104639, 104640, 104641, 
	104642, 104643, 104644, 104645, 104646, 104647, 104648, 104649, 104650, 104651, 
	104652, 104653, 104654, 104655, 104656, 104657, 104658, 104659, 104660, 104661, 
	104662, 104663, 104664, 104665, 104666, 104667, 104668, 104685, 104686, 104687,
	104688, 104689, 104690, 104691, 104692, 104693, 104694, 104695, 104696, 104697, 
	104698, 104700, 104701, 104702, 104703,

		//从dbf文件中读取出的需要使用自定义调色板的动画编号,暂时采用手动方式添加,在读取dbf函数制作完成之前在这里使用
		104704, 104705, 104706, 104707, 104708, 104709, 104710, 104711, 104712, 104713,
		104714, 104715, 104716, 104717, 104718, 104719, 104720, 104721, 104722, 104723,
		104724, 104725, 104726, 104727, 104728, 104729, 104730, 104731, 104732, 104733,
		104734, 104735, 104736, 104737, 104738, 111001, 111002, 111003, 111004, 111005,
		111006, 111007, 111008, 111009, 111010, 111011, 111012, 111013, 111014, 111015,
		111016, 111017, 111018, 111019, 111020, 111021, 111022, 111023, 111024, 111025,
		111026, 111027, 111028, 104739, 104740, 104741, 104742, 104743, 104744, 104745,
		104746, 104747, 104748, 104749, 104750, 104751, 104752, 104753, 104754, 104755,
		104756, 104757, 104758, 104759, 104760, 104761, 104762, 111029, 111030, 111031,
		111032, 111033, 111034, 111035, 111036, 111037, 111038, 111039, 111040, 111041,
		111042, 111043, 111044, 111045, 111046, 111047, 111048, 111049, 111050, 111051,
		111052, 111053, 111054, 111055, 111056, 111057, 111058, 111059, 111060, 111061,
		111062, 104763, 104764, 104765, 104766, 104767, 104768, 104769, 104770, 104771,
		104772, 104773, 104774, 104775, 104776, 104777, 104778, 104779, 104780, 104781,
		104782, 104783, 104784, 104785, 104786, 104787, 104788, 104789, 104790, 104791,
		104792, 104793, 104794, 104795, 104796, 104797, 104798, 104799, 104800, 104801,
		104802, 104803, 104804, 104805, 104806, 104807, 104808, 104809, 104810, 104811,
		104812, 104813, 104814, 104815, 104816, 104817, 104818, 104819, 104820, 104821,
		104822, 104823, 104824, 104825, 104826, 104827, 104828, 104829, 104830, 104831,
		104832, 104833, 104834, 104835, 104836, 104837, 104838, 104839, 104840, 104841,
		104842, 104843, 104844, 104845, 104846, 104847, 104848, 104849, 104850, 104851,
		104852, 104853, 104854, 104855, 104856, 104857, 104858, 104859, 104860, 104861,
		104862, 111063, 111064, 111065, 111066, 111067, 111068, 111069, 111070, 111071,
		111072, 111073, 111074, 111075, 111076, 111077, 111078, 111079, 111080, 111081,
		111082, 111083, 111084, 111085, 111086, 111087, 111088, 111089, 111090, 111091,
		111092, 111093, 111094, 111095, 111096, 111097, 120100, 120101, 120102, 120103,
		120104, 120105, 120106, 120107, 120108, 120109, 120110, 120111, 120112, 120113,
		120114, 120115, 120116, 120117, 120118, 120119, 120120, 120121, 120122, 120123,
		120124, 120125, 120126, 120127, 120128, 120129, 120130, 120131, 120132, 120133,
		120134, 120135, 120136, 120137, 120138, 120139, 120140, 120141, 120142, 120143,
		120144, 120145, 120146, 120147, 120148, 120149, 120150, 120151, 120152, 120153,
		120154, 120155, 104863, 104864, 104865, 104866, 104867, 104868, 104869, 104870,
		104871, 104872, 111098, 111099, 111100, 104873, 104874, 104875, 104876, 104877,
		104878, 104879, 104880, 104881, 104882, 104883, 104884, 104885, 104886, 104887,
		104888, 104889, 104890, 104891, 104892, 104893, 104894, 104895, 104896, 104897,
		104898, 104899, 104900, 104901, 104902, 104903, 104904, 104905, 104906, 104907,
		104908, 104909, 104910, 104911, 104912, 111106, 111107, 111108, 111109, 111110,
		111111, 111112, 111113, 111114, 111115, 111116, 111117, 111118, 111119, 111120,
		111121, 111122, 111123, 111124, 111125, 111126, 111127, 111128, 111129, 111130,
		111131, 111132, 111133, 111134, 111135, 111136, 111137, 111138, 111139, 111140,
		111141, 111142, 111143, 111144, 111145, 111146, 111147, 111148, 111149, 111150,
		111151, 111152, 104913, 104914, 104915, 104916, 104917, 104918, 104919, 104920,
		104921, 104922, 104923, 104924, 104925, 104926, 104927, 104928, 104929, 104930,
		104931, 104932, 104933, 104934, 104935, 104936, 104937, 104938, 104939, 104940,
		104941, 104942, 111153, 111154, 111155, 111156, 111157, 111158, 111159, 111160,
		111161, 111162, 111163, 111164, 111165, 111166, 111167, 111168, 111169, 111170,
		111171, 111172, 111173, 111174, 111175, 111176, 111177, 111178, 111179, 111180,
		111181, 111182, 111183, 111184, 111185, 111186, 111187, 111188, 111189, 111190,
		111191, 104943, 104944, 104945, 104946, 104947, 104948, 104949, 104950, 111192,
		111193, 104951, 104952, 104953, 104954, 104955, 104956, 104957, 104958, 104959,
		104960, 104961, 104962, 104963, 104964, 104965, 104966, 104967, 111194, 111195,
		111196, 111197, 111198, 111199, 111200, 111201, 111202, 111203, 111204, 111205,
		111206, 111207, 111208, 111209, 111210, 111211, 111212, 111213, 111214, 111215,
		111216, 111217, 111218, 111219, 111220, 111221, 111222, 111223, 111224, 111225,
		111226, 111227, 111228, 111229, 111230, 111231, 111232, 111233, 111234, 111235,
		111236, 111237, 111238, 111239, 111240, 111241, 111242, 111243, 111244, 111245,
		111246, 111247, 111248, 111249, 111250, 104968, 104969, 104970, 104971, 104972,
		104973, 104974, 104975, 104976, 104977, 104978, 104979, 104980, 104981, 104982,
		104983, 104984, 104985, 104986, 104987, 104988, 104989, 104990, 104991, 104992,
		104993, 104994, 104995, 104996, 104997, 104998, 114225, 114226, 104999, 108000,
		108001, 108002, 108003, 108004, 108005, 108006, 108007, 108008, 108009, 108010, 
		108011, 108012, 108013, 108014, 108015, 108016, 108017, 108018, 108019, 108020, 
		108021, 108022, 108023, 108024, 108025, 108026, 108027, 108028, 108029, 108030, 
		108031, 108032, 108033, 108034, 108035, 108036, 108037, 108038, 108039, 108040, 
		108041, 108042, 108043, 108044, 108045, 108056, 108057, 108058, 108059
#endif
};

int SprPalNum;					// パレットの数(LoadSprPalの大きさを见て后で代入する)
int NowSprPalNum;				// パレットの现在の确保数

struct SPRPAL_MASTER *SprPal;	// パレットデータ(アニメーション用)
struct SPRPAL *pSprPaldt;


#define SPRPAL_DEATHCNT 10
#define SPRPAL_CHECKNUM 20

// SprPal初期化关数 ++++
void InitSprPal()
{
	int i;

	// パレットの数を计算
	SprPalNum=sizeof(LoadSprPal)/sizeof(int);

	// パレットの数の分管理构造体を确保
	SprPal=(struct SPRPAL_MASTER *)GlobalAllocPtr( GPTR, (SprPalNum)*sizeof(SPRPAL_MASTER) );
	memset( SprPal, 0, (SprPalNum)*sizeof(SPRPAL_MASTER) );
#ifdef PUK2_MEMCHECK
	memlistset( SprPal, MEMLISTTYPE_SPRPAL_MASTER );
#endif

	pSprPaldt = (struct SPRPAL *)GlobalAllocPtr( GPTR, (SprPalNum)*sizeof(SPRPAL) );
#ifdef PUK2_MEMCHECK
	memlistset( pSprPaldt, MEMLISTTYPE_SPRPAL );
#endif
	for(i=0;i<SprPalNum;i++){
		SprPal[i].dt = &pSprPaldt[i];
		SprPal[i].useFlag = 0;
	}
}

//----------------------------------//

// SprPal解放关数 ++++
void RelSprPal()
{
	#ifdef PUK2_MEMCHECK
		memlistrel( pSprPaldt, MEMLISTTYPE_SPRPAL );
	#endif
	GlobalFreePtr( pSprPaldt );
	pSprPaldt = NULL;
#ifdef PUK2_MEMCHECK
	memlistrel( SprPal, MEMLISTTYPE_SPRPAL_MASTER );
#endif
	GlobalFreePtr( SprPal );
	SprPal=NULL;
}

//----------------------------------//

// SprPal解放管理关数 ++++
void SprPalDeathCheck()
{
/*** 今はチェックの必要がない
	int i;

	// 确保数がSPRPAL_CHECKNUM以下ならチェックしない
	if (NowSprPalNum<=SPRPAL_CHECKNUM) return;

	// カウンタを进め、SPRPAL_DEATHCNTを超えたら解放
	for(i=0;i<SprPalNum;i++){
		if (!SprPal[i].dt) continue;
		SprPal[i].DeathCnt++;
		if (SprPal[i].DeathCnt>SPRPAL_DEATHCNT){
	#ifdef PUK2_MEMCHECK
			memlistrel( SprPal[i].dt, MEMLISTTYPE_SPRPAL );
	#endif
			GlobalFreePtr( SprPal[i].dt );
			SprPal[i].dt=NULL;
			SprPal[i].DeathCnt=0;
			NowSprPalNum--;
		}
	}
***/
}

//----------------------------------//

// SprPalパレットチェンジ后パレット取得关数 ++++
void ChangeSprPal( int PalNo )
{
	int i=PalNo, j;		// ループカウンタ(今回iはPalNoで固定)
	unsigned char *xPalchg=0;						// パレットチェンジの指定
	unsigned char xPalchgdata[3]={ 128,128,128 };	// パレットチェンジの指定无し时のパレットチェンジの指定
	union PALPOINTER pal;

	if (PalNo<0) return;
	if (PalNo>=SprPalNum) return;

	// パレットデータが未取得の场合
	if (!SprPal[i].useFlag){
		int palsiz;										// パレットサイズ取得用
		int bmpNo;										// パレットの入った絵の通し番号

		// グラフィックＩＤを通し番号に变换
		realGetNo( LoadSprPal[i], (U4 *)&bmpNo );
		
		// パレットのデータを読み込む
		if( realGetImage( bmpNo, (unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &palsiz ) == FALSE ){
			RealBinBmpNo=-1;
			// パレットを0で埋めとく
			memset( SprPal[i].dt->Base, 0, (sizeof(PALDATA)<<8) );
		}else{
			RealBinBmpNo=bmpNo;

			// Baseにパレットデータをコピーする
			memcpy( SprPal[i].dt->Base, pRealBinBits+RealBinWidth*RealBinHeight, palsiz );

			// パレットが256色无かったら、一应0で埋めとく
			if ( palsiz<(sizeof(PALDATA)<<8) ) memset( SprPal[i].dt->Base+palsiz, 0, (sizeof(PALDATA)<<8)-palsiz );
		}

		// パレットチェンジ前の现在のＢＰＰ用パレットを作成する
		pal.p4 = SprPal[i].dt->ChgBf;
		switch(displayBpp){
		case 16:			// １６ＢＰＰモード时
			// 透过色は0番パレットで、赤:0,緑:0,青:0 なので、0番パレットに0を代入
			pal.p2[DEF_COLORKEY] = 0;

			// 0番以降のパレットを作成
			for(j=1;j<256;j++){
				// 色の设定(パレットチェンジ后)
				pal.p2[j]=
					lpDraw->PalTbl[ SprPal[i].dt->Base[j][PPLR] ][PPLR] |
					lpDraw->PalTbl[ SprPal[i].dt->Base[j][PPLG] ][PPLG] |
					lpDraw->PalTbl[ SprPal[i].dt->Base[j][PPLB] ][PPLB];
				if (!pal.p2[j]) pal.p2[j] = 1;
			}
			break;

		case 24:			// ２４ＢＰＰモード时
			// 透过色は0番パレットで、赤:0,緑:0,青:0 なので、0番パレットに0を代入
			pal.p3[DEF_COLORKEY].r = pal.p3[DEF_COLORKEY].g = pal.p3[DEF_COLORKEY].b = 0;

			// 0番以降のパレットを作成
			for(j=1;j<256;j++){
				// 色の设定(パレットチェンジ后)
				pal.p3[j].r = SprPal[i].dt->Base[j][PPLR];
				pal.p3[j].g = SprPal[i].dt->Base[j][PPLG];
				pal.p3[j].b = SprPal[i].dt->Base[j][PPLB];
				if ( !(pal.p3[j].r|pal.p3[j].g|pal.p3[j].b) ) pal.p3[j].b = 1;
			}
			break;

		case 32:			// ３２ＢＰＰモード时
			// 透过色は0番パレットで、赤:0,緑:0,青:0 なので、0番パレットに0を代入
			pal.p4[DEF_COLORKEY] = 0;

			// 0番以降のパレットを作成
			for(j=1;j<256;j++){
				// 色の设定(パレットチェンジ后)
				pal.p4[j] = RGB_MAKE(
					 SprPal[i].dt->Base[j][PPLR],
					 SprPal[i].dt->Base[j][PPLG],
					 SprPal[i].dt->Base[j][PPLB] );
				if (!pal.p4[j]) pal.p4[j] = 1;
			}
			break;
		}

		// SPRPAL确保数を增やす
		NowSprPalNum++;
	}

	// パレットチェンジの指定が无かったら
	if (Palchg) xPalchg=Palchg;
	else xPalchg=xPalchgdata;

	// パレットチェンジ后の现在のＢＰＰ用パレットを作成する
	pal.p4 = SprPal[i].dt->ChgAf;
	switch(displayBpp){
	case 16:			// １６ＢＰＰモード时
		// 透过色は0番パレットで、赤:0,緑:0,青:0 なので、0番パレットに0を代入
		pal.p2[DEF_COLORKEY] = 0;

		// 0番以降のパレットを作成
		for(j=1;j<256;j++){
			// 色の设定(パレットチェンジ后)
			pal.p2[j]=
				lpDraw->PalTbl[ PalchgTbl[PPLR][ SprPal[i].dt->Base[j][PPLR] ] ][PPLR] |
				lpDraw->PalTbl[ PalchgTbl[PPLG][ SprPal[i].dt->Base[j][PPLG] ] ][PPLG] |
				lpDraw->PalTbl[ PalchgTbl[PPLB][ SprPal[i].dt->Base[j][PPLB] ] ][PPLB];
			if (!pal.p2[j]) pal.p2[j] = 1;
		}
		break;

	case 24:			// ２４ＢＰＰモード时
		// 透过色は0番パレットで、赤:0,緑:0,青:0 なので、0番パレットに0を代入
		pal.p3[DEF_COLORKEY].r = pal.p3[DEF_COLORKEY].g = pal.p3[DEF_COLORKEY].b = 0;

		// 0番以降のパレットを作成
		for(j=1;j<256;j++){
			// 色の设定(パレットチェンジ后)
			pal.p3[j].r = PalchgTbl[PPLR][ SprPal[i].dt->Base[j][PPLR] ];
			pal.p3[j].g = PalchgTbl[PPLG][ SprPal[i].dt->Base[j][PPLG] ];
			pal.p3[j].b = PalchgTbl[PPLB][ SprPal[i].dt->Base[j][PPLB] ];
			if ( !(pal.p3[j].r|pal.p3[j].g|pal.p3[j].b) ) pal.p3[j].b = 1;
		}
		break;

	case 32:			// ３２ＢＰＰモード时
		// 透过色は0番パレットで、赤:0,緑:0,青:0 なので、0番パレットに0を代入
		pal.p4[DEF_COLORKEY] = 0;

		// 0番以降のパレットを作成
		for(j=1;j<256;j++){
			// 色の设定(パレットチェンジ后)
			pal.p4[j] = RGB_MAKE(
				 PalchgTbl[PPLR][ SprPal[i].dt->Base[j][PPLR] ],
				 PalchgTbl[PPLG][ SprPal[i].dt->Base[j][PPLG] ],
				 PalchgTbl[PPLB][ SprPal[i].dt->Base[j][PPLB] ] );
			if (!pal.p4[j]) pal.p4[j] = 1;
		}
		break;
	}
	SprPal[i].dt->PalNo = PalState.palNo;
	SprPal[i].dt->PalCount = PalState.count;
}

//----------------------------------//

// アニメーションパレットのパレットチェンジ ++++
char PrepareSprPal( DISP_INFO *pDispInfo )
{
	if ( 0<pDispInfo->bm.PalNo && pDispInfo->bm.PalNo<SprPalNum ){
		if ( SprPal[ pDispInfo->bm.PalNo ].dt == NULL ){
			ChangeSprPal( pDispInfo->bm.PalNo );
			return 1;
		}else if ( ( SprPal[ pDispInfo->bm.PalNo ].dt->PalNo != PalState.palNo )
			|| ( SprPal[ pDispInfo->bm.PalNo ].dt->PalCount != PalState.count )
			){
			ChangeSprPal( pDispInfo->bm.PalNo );
			return 1;
		}
	}else{
		pDispInfo->bm.PalNo=0;
		return 2;
	}
	return 0;
}

//----------------------------------//

void AllocateBmpToSurface_NewVerNomal( DISP_INFO *pDispInfo );

// ＢＭＰをロードしてあるかを确认し、无かったらロードする ++++
BOOL LoadBmp_VerNomal( DISP_INFO *pDispInfo )
{
	char flg=0;
	int palsiz;
	int bmpNo=pDispInfo->bmpNo;
	char usechg=!(pDispInfo->bm.bltf&BLTF_NOCHG);
	int nowPalno = (usechg?PalState.palNo:0);

	// リミットチェック
	if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) return FALSE;

	// アニメーションパレットのパレットチェンジ
	PrepareSprPal( pDispInfo );

#ifdef SUPPORT_16BIT
	if (SpriteInfo[ bmpNo ].lpSurfaceInfo){
		if ( displayBpp != 8 ){
			if ( ( SpriteInfo[ bmpNo ].lpSurfaceInfo->PalNo != PalState.palNo )
			|| ( SpriteInfo[ bmpNo ].lpSurfaceInfo->PalCount != PalState.count )
			|| ( SpriteInfo[ bmpNo ].lpSurfaceInfo->AnimPalNo != pDispInfo->bm.PalNo )
			){
#if 0
				if (SpriteInfo[ bmpNo ].BmpVer==1){
					if ( palchgcnt>=MAXPALCHGQ ) return TRUE;
					palchgcnt++;
				}
#endif
				flg=1;
			}
		}
	}
#endif

	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL || flg ){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if( realGetImage( bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &palsiz ) == FALSE ){
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_51, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo=-1;
			return FALSE;
		}
		RealBinBmpNo=bmpNo;

		// ＢＭＰのサイズを记忆
		SpriteInfo[ bmpNo ].width = RealBinWidth;
		SpriteInfo[ bmpNo ].height = RealBinHeight;

		SpriteInfo[ bmpNo ].BmpVer=0;

		// ＢＭＰをサーフェスへ割り当て
		if ( 0<pDispInfo->bm.PalNo && pDispInfo->bm.PalNo<SprPalNum ){
			AllocateBmpToSurface_NewVerNomal( pDispInfo );
		}
	}
	return TRUE;
}

//----------------------------------//

void AllocateBmpToSurface_NewVerPUK2( DISP_INFO *pDispInfo, LPBITMAPINFO lpBmpInfo );

extern int nowchging;

// ＢＭＰをロードする *********************************************************/
BOOL LoadBmp_VerPUK2( DISP_INFO *pDispInfo )
{
	SURFACE_INFO *psfi;	// サーフェース参照用
#if !BINLOAD	// ファイルから読み込む时
	char *fn[]={
		"D:/sugiwork/program/nrcli/bmp/dodai_l_ari01.bmp",                  //MLHIDE
	};
#else
	int palsiz;
#endif
	char flg;
	int PalNo, Palcnt;
	int bmpNo=pDispInfo->bmpNo;
	char usechg=!(pDispInfo->bm.bltf&BLTF_NOCHG);
	int nowPalno = (usechg?PalState.palNo:0);

	// リミットチェック
	if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) return FALSE;

	// アニメーションパレットのパレットチェンジ
	PrepareSprPal( pDispInfo );
	
	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){

	#if BINLOAD	// bin から読み込む时

		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if( realGetImage( bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &palsiz ) == FALSE ){
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_52, "确认", MB_OK );
#else
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
			RealBinBmpNo=-1;
			return FALSE;
		}
		RealBinBmpNo=bmpNo;

		SpriteInfo[bmpNo].width = RealBinWidth;
		SpriteInfo[bmpNo].height = RealBinHeight;

#ifdef PUK2_DIFFPAL_SURFACE
		GetPaletteSurface( bmpNo, 1, 1 );
#else
		GetSurface( bmpNo, NULL );
#endif
		if ( 0<pDispInfo->bm.PalNo && pDispInfo->bm.PalNo<SprPalNum ){
			AllocateBmpToSurface_NewVerPUK2( pDispInfo, NULL );
		}else{
			AllocateBmpToSurface_PUK2( bmpNo, pDispInfo->bm.u, pDispInfo->bm.v, pDispInfo->bm.w, pDispInfo->bm.h, NULL, usechg );
		}

	#else	// ファイルから読み込む时

		lpBmpInfo=LoadDirectDrawBitmap( fn[bmpNo-DEBUGBMPID] );
		SpriteInfo[ bmpNo ].width = (short)lpBmpInfo->bmiHeader.biWidth;
		SpriteInfo[ bmpNo ].height = (short)lpBmpInfo->bmiHeader.biHeight;

		GetSurface( bmpNo, lpBmpInfo );

		if ( SpriteInfo[bmpNo].lpPaldata==NULL ||  
			(SpriteInfo[bmpNo].PalNo != nowPalno ) || (SpriteInfo[bmpNo].PalCount != PalState.count) ){
			if (palsiz>0){
				SpriteInfo[bmpNo].lpPaldata=GetPalData( 1, usechg, NULL, (RGBQUAD *)( (int)pBmpInfo+sizeof(BITMAPINFOHEADER) ) );
				SpriteInfo[bmpNo].PalNo = nowPalno;
				SpriteInfo[bmpNo].PalCount = PalState.count;
			}
		}else{
			if (!usechg){
				if ( SpriteInfo[bmpNo].lpPaldata==NULL ||  
					(SpriteInfo[bmpNo].PalNo != nowPalno ) || (SpriteInfo[bmpNo].PalCount != PalState.count) ){
					SpriteInfo[bmpNo].lpPaldata=GetPalData( 0, usechg, NULL, NULL );
					SpriteInfo[bmpNo].PalNo = nowPalno;
					SpriteInfo[bmpNo].PalCount = PalState.count;
				}
			}
		}

		if ( 0<pDispInfo->bm.PalNo && pDispInfo->bm.PalNo<SprPalNum ){
			AllocateBmpToSurface_NewVerPUK2( pDispInfo, lpBmpInfo );
		}else{
			AllocateBmpToSurface_PUK2( bmpNo, pDispInfo->lu, pDispInfo->lv, pDispInfo->lw, pDispInfo->lh, lpBmpInfo, usechg );
		}
	#ifdef PUK2_MEMCHECK
		memlistrel( lpBmpInfo, MEMLISTTYPE_BITMAPMEMORY );
	#endif
		GlobalFreePtr( lpBmpInfo );
		lpBmpInfo=NULL;

	#endif

	}
	else{
		flg=CheckSurfacePal( bmpNo, pDispInfo->bm.u, pDispInfo->bm.v, pDispInfo->bm.w, pDispInfo->bm.h, &PalNo, &Palcnt, pDispInfo->bm.PalNo );
		if (flg){
			if (LimiteLoadBmpFlag){
				if (flg==1){
					if ( palchgcnt>=MAXPALCHGQ ){
						for(psfi=SpriteInfo[ bmpNo ].lpSurfaceInfo;psfi!=NULL;psfi=psfi->pNext) psfi->date=SurfaceDate;
						return TRUE;
					}else{
						if (nowchging<0) nowchging=PalState.count;

						if (PalNo == PalState.palNo){
							if (Palcnt >= nowchging){
								for(psfi=SpriteInfo[ bmpNo ].lpSurfaceInfo;psfi!=NULL;psfi=psfi->pNext) psfi->date=SurfaceDate;
								return TRUE;
							}
						}
					}
				}
				palchgcnt++;
			}

	#if BINLOAD	// bin から読み込む时

			if (RealBinBmpNo!=bmpNo){
				// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
				if( realGetImage( 	bmpNo, ( unsigned char **)&pRealBinBits, &RealBinWidth, &RealBinHeight, &palsiz ) == FALSE ){
#ifdef PUK3_ERRORMESSAGE_NUM
					//MessageBox( hWnd, ERRMSG_53, "确认", MB_OK );
#else
					//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
#endif
					RealBinBmpNo=-1;
					return FALSE;
				}
				RealBinBmpNo=bmpNo;

				SpriteInfo[ bmpNo ].width = RealBinWidth;
				SpriteInfo[ bmpNo ].height = RealBinHeight;
			}

			if ( 0<pDispInfo->bm.PalNo && pDispInfo->bm.PalNo<SprPalNum ){
				AllocateBmpToSurface_NewVerPUK2( pDispInfo, NULL );
			}else{
				AllocateBmpToSurface_PUK2( bmpNo, pDispInfo->bm.u, pDispInfo->bm.v, pDispInfo->bm.w, pDispInfo->bm.h, NULL, usechg );
			}

	#else	// ファイルから読み込む时

			lpBmpInfo=LoadDirectDrawBitmap( fn[bmpNo-DEBUGBMPID] );
			SpriteInfo[ bmpNo ].width = (short)lpBmpInfo->bmiHeader.biWidth;
			SpriteInfo[ bmpNo ].height = (short)lpBmpInfo->bmiHeader.biHeight;

			if ( SpriteInfo[bmpNo].lpPaldata==NULL ||  
				(SpriteInfo[bmpNo].PalNo != nowPalno ) || (SpriteInfo[bmpNo].PalCount != PalState.count) ){
				if (palsiz>0){
					if (SpriteInfo[bmpNo].lpPaldata){
						SpriteInfo[bmpNo].lpPaldata=GetPalData( 1, usechg, NULL, (RGBQUAD *)( (int)pBmpInfo+sizeof(BITMAPINFOHEADER) ) );
					}
					SpriteInfo[bmpNo].PalNo = nowPalno;
					SpriteInfo[bmpNo].PalCount = PalState.count;
				}
			}else{
				if (!usechg){
					if ( SpriteInfo[bmpNo].lpPaldata==NULL ||  
						(SpriteInfo[bmpNo].PalNo != nowPalno ) || (SpriteInfo[bmpNo].PalCount != PalState.count) ){
						if (SpriteInfo[bmpNo].lpPaldata){
							SpriteInfo[bmpNo].lpPaldata=GetPalData( 0, usechg, NULL, NULL );
						}
						SpriteInfo[bmpNo].PalNo = nowPalno;
						SpriteInfo[bmpNo].PalCount = PalState.count;
					}
				}
			}
			SpriteInfo[bmpNo].PalNo = nowPalno;
			SpriteInfo[bmpNo].PalCount = PalState.count;

			if ( 0<pDispInfo->bm.PalNo && pDispInfo->bm.PalNo<SprPalNum ){
				AllocateBmpToSurface_NewVerPUK2( pDispInfo, lpBmpInfo );
			}else{
				AllocateBmpToSurface_PUK2( bmpNo, pDispInfo->lu, pDispInfo->lv, pDispInfo->lw, pDispInfo->lh, lpBmpInfo, usechg );
			}
	#ifdef PUK2_MEMCHECK
			memlistrel( lpBmpInfo, MEMLISTTYPE_BITMAPMEMORY );
	#endif
			GlobalFreePtr( lpBmpInfo );
			lpBmpInfo=NULL;

	#endif
		}
	}
	for(psfi=SpriteInfo[ bmpNo ].lpSurfaceInfo;psfi!=NULL;psfi=psfi->pNext) psfi->date=SurfaceDate;

	return TRUE;
}

//----------------------------------//

void DrawBitmapToSurface_AnimPal( LPDIRECTDRAWSURFACE7 lpSurface, int offsetX, int offsetY, int sizeX, int sizeY, LPBITMAPINFO pBmpInfo, int PalNo, char pchg );

/* ＢＭＰをサーフェスへ割り当て ***********************************************/
void AllocateBmpToSurface_NewVerNomal( DISP_INFO *pDispInfo )
{
	//int bmpWidth  = lpBmpInfo->bmiHeader.biWidth;	// ＢＭＰの横サイズ
	//int bmpHeight = lpBmpInfo->bmiHeader.biHeight; 	// ＢＭＰの縦サイズ

	int bmpWidth  = RealBinWidth;	// ＢＭＰの横サイズ
	int bmpHeight = RealBinHeight; 	// ＢＭＰの縦サイズ
	
	int offsetX, offsetY; 			// 送るＢＭＰのOFFセットポイント
	int sizeX, sizeY;				// 転送するサイズ
	int surfaceCntX;				// 必要なサーフェスの横枚数
	int surfaceCntY;				// 必要なサーフェスの横枚数
	int totalSurface;				// 必要なサーフェスの総数
	int totalSurfaceCnt = 0;		// 现在の确保したサーフェスの枚数（ ループカウンタ ）
	int	SurfaceSearchPointBak = SurfaceSearchPoint; // 一周检索したら終了するため最初の位置を记忆
	int amariSizeX = FALSE;		// 横に余りがあるかフラグ
	int amariSizeY = FALSE;		// 縦に余りがあるかフラグ
	BOOL vramFullFlag = FALSE; 		// VRAMがいっぱいかどうか
	SURFACE_INFO *prevSurfaceInfo = NULL; 	// 前のサーフェスインフォ构造体のアドレス

	int bmpNo=pDispInfo->bmpNo;
	
	// 送るＢＭＰのOFFセットポイント
	offsetX = 0; 
	offsetY = bmpHeight;
	
	// 必要なサーフェスの横枚数计算
	surfaceCntX = bmpWidth / SURFACE_WIDTH;
	
	// 横に余りがあったら半端サイズを记忆
	if( ( amariSizeX = bmpWidth % SURFACE_WIDTH ) ){ 
		surfaceCntX++;		// もうひとつ必要
	}
	
	// 必要なサーフェスの縦枚数计算
	surfaceCntY = bmpHeight / SURFACE_HEIGHT;
	
	// 縦に余りがあったら半端サイズを记忆
	if( ( amariSizeY = bmpHeight % SURFACE_HEIGHT ) ){ 
		surfaceCntY++;		// もうひとつ必要
	}
	// 必要なサーフェスの総数计算
	totalSurface  = surfaceCntX * surfaceCntY;
	
#if 0
	// 横幅を４のバウンダリにする
	if( ( lpBmpInfo->bmiHeader.biWidth & 3 ) ){
		lpBmpInfo->bmiHeader.biWidth += 4 - lpBmpInfo->bmiHeader.biWidth & 3;
	}
#else	// Realbin 読み込むとき
	// 横幅を４のバウンダリにする
	if( ( RealBinWidth & 3 ) ){
		RealBinWidth += 4 - RealBinWidth & 3;
	}
#endif

	// 同じ所に上书きするなら前回のものは消去する。
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo != NULL ){
			prevSurfaceInfo = SpriteInfo[ bmpNo ].lpSurfaceInfo;
			// サーフェスリストの初期化ループ
			for( ; prevSurfaceInfo != NULL;
				prevSurfaceInfo = prevSurfaceInfo->pNext ){
				
			// ＢＭＰ番号初期化
				prevSurfaceInfo->bmpNo = -1;
				
#ifdef _DEBUG		
				// 现在使っているサーフェスの数マイナス
				SurfaceUseCnt--;
#endif
			}
			SpriteInfo[ bmpNo ].lpSurfaceInfo = NULL;
	}

	// 空いているサーフェスを探す
	// サーフェスの数だけループ
	while( 1 ){
		// 使用できる状态なら
		if( SurfaceInfo[ SurfaceSearchPoint ].date < SurfaceDate - SURACE_BMP_DEATH_DATE ){
#ifdef _DEBUG		
			// 现在使っているサーフェスの数カウント
			SurfaceUseCnt++;
#endif
			// 上书きする时
			if( SurfaceInfo[ SurfaceSearchPoint ].bmpNo != -1 ){
				// サーフェスインフォ构造体のアドレス
				SURFACE_INFO *lpSurfaceInfo;
				
				// サーフェスリストの先头アドレスを记忆
				lpSurfaceInfo = SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo;
				
				// 前にいたＢＭＰとサーフェスのリンクを削除
				SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo = NULL;

				// サーフェスリストの初期化ループ
				for( ; lpSurfaceInfo != NULL;
					lpSurfaceInfo = lpSurfaceInfo->pNext ){
					
					// ＢＭＰ番号初期化
					lpSurfaceInfo->bmpNo = -1;
					
#ifdef _DEBUG		
					// 现在使っているサーフェスの数マイナス
					SurfaceUseCnt--;
#endif
				}
				
			}
			// サーフェスリストの先头アドレスを记忆するとき
			if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){
				// スプライトインフォに先头アドレスを记忆する
				SpriteInfo[ bmpNo ].lpSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				// パレット番号とフェードのカウンターも学习
				SurfaceInfo[ SurfaceSearchPoint ].PalNo = PalState.palNo;
				SurfaceInfo[ SurfaceSearchPoint ].PalCount = PalState.count;
				SurfaceInfo[ SurfaceSearchPoint ].AnimPalNo = pDispInfo->bm.PalNo;
				
			}else{ // つなげるとき
				
				// 覚えておいた前のサーフェスインフォ构造体にアドレスを教える
				prevSurfaceInfo->pNext = &SurfaceInfo[ SurfaceSearchPoint ];
			}
			
			// ＢＭＰ番号を记忆する
			SurfaceInfo[ SurfaceSearchPoint ].bmpNo = bmpNo;
			
			// OFFセット座标を学习
			SurfaceInfo[ SurfaceSearchPoint ].offsetX = offsetX;
			SurfaceInfo[ SurfaceSearchPoint ].offsetY = bmpHeight - offsetY;
			
			// 横に余りがあるときは転送する前にサーフェスを黒でクリアー
			if( offsetX >= bmpWidth - SURFACE_WIDTH && amariSizeX ){
				ClearSurface( SurfaceInfo[ SurfaceSearchPoint ].lpSurface );
				// 余りサイズ
				sizeX = amariSizeX;				
			}else sizeX = SURFACE_WIDTH;
			
			// 縦に余りがあるときは転送する前にサーフェスを黒でクリアー
			if( offsetY - SURFACE_HEIGHT <= 0 && amariSizeY ){
				// 先に黒でクリアーされてないとき
				if( sizeX != amariSizeX ){
					ClearSurface( SurfaceInfo[ SurfaceSearchPoint ].lpSurface );
				}
				// 余りサイズ
				sizeY = amariSizeY;
			}else sizeY = SURFACE_HEIGHT;
			
			// ＢＭＰをサーフェスへ転送
			if (pDispInfo->bm.PalNo){
				DrawBitmapToSurface_AnimPal( SurfaceInfo[ SurfaceSearchPoint ].lpSurface,
					offsetX, offsetY-1, sizeX, sizeY, NULL, pDispInfo->bm.PalNo, !(pDispInfo->bm.bltf&BLTF_NOCHG) );
			}else{
			}

			// 现在の确保したサーフェスの枚数をカウント
			totalSurfaceCnt++;

			// すべて确保し終わったら
			if( totalSurfaceCnt >= totalSurface ){
				//　pNext にＮＵＬＬを入れて終了
				SurfaceInfo[ SurfaceSearchPoint ].pNext = NULL;
				// 检索位置を进ませる
				SurfaceSearchPoint++;
				
#if 0			// VRAMを优先使用バージョン

				// VRAMのサーフェスを检索するとき
				if( vramFullFlag == FALSE ){
					// リミットチェック
					if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
				}else{
					// 检索位置を戾す
					SurfaceSearchPoint = SurfaceSearchPointBak;
				}
				
#else			// VRAMとSYSRAMを同等级で使用バージョン

				// リミットチェック
				if( SurfaceSearchPoint >= SurfaceCnt ) SurfaceSearchPoint = 0;
#endif
				break;
				
			}else{
				// 今のサーフェスインフォ构造体のアドレスを学习
				prevSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				
				// 右端まで送ったら
				if( offsetX >= bmpWidth - SURFACE_WIDTH ){ 
					offsetX = 0;
					offsetY -= SURFACE_HEIGHT;
				}else{ 
					offsetX += SURFACE_WIDTH;
				}
			}
		}
		// 检索位置を进ませる
		SurfaceSearchPoint++;
		
#if 0	// VRAMを优先使用バージョン

		// VRAMのサーフェスを检索するとき
		if( vramFullFlag == FALSE ){
			// VRAMサーフェスの最后まで检索したとき
			if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
			// 一周检索したらVRAMに空きなし状态であきらめる
			if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_54, "确认", MB_OK );
#else
				//MessageBox( hWnd, "ＶＲＡＭ没有剩余空间。", "确认", MB_OK );
#endif
				// 检索位置をSYSTEMサーフェスが存在するところへ移动
				SurfaceSearchPoint = VramSurfaceCnt + 1;
				vramFullFlag = TRUE;
			}
		}
		// SYSTEMRAMのサーフェスを检索するとき
		if( vramFullFlag == TRUE ){
			// 最后まで检索したら
			if( SurfaceSearchPoint >= SurfaceCnt ){ 
				// 检索位置を戾す
				SurfaceSearchPoint = SurfaceSearchPointBak;
#ifdef PUK3_ERRORMESSAGE_NUM
				//MessageBox( hWnd, ERRMSG_55, "确认", MB_OK );
#else
				//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
				break;
			}
		}
		
#else	// VRAMとSYSRAMを同等级で使用バージョン
		
		// 最后まで检索したら
		if( SurfaceSearchPoint >= SurfaceCnt ){ 
			// 最初に返回
			SurfaceSearchPoint = 0;
		}
		// 一周检索したらサーフェスに空きなし状态であきらめる
		if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
#ifdef PUK3_ERRORMESSAGE_NUM
			//MessageBox( hWnd, ERRMSG_56, "确认", MB_OK );
#else
			//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
#endif
			char szBuffer[256];
			sprintf( szBuffer, "Surface不足。%d\n", SurfaceSearchPoint );         //MLHIDE
			OutputDebugString( szBuffer);
			break;
		}
	}
#endif
}

//----------------------------------//

// サーフェースへ絵の読み込み ++++
void AllocateBmpToSurface_NewVerPUK2( DISP_INFO *pDispInfo, LPBITMAPINFO lpBmpInfo )
{
	SURFACE_INFO *psfi,*psfi2;	// サーフェース参照用
	char wq,hq;					// 絵の縦横のサーフェースの枚数
	char wfs,hfs;				// 切り取り始めのサーフェースの位置
	char wes,hes;				// 切り終わりのサーフェースの位置
	int ofX,ofY;				// 縦横の余り
	int i,j;					// ループカウンタ

	int bmpNo=pDispInfo->bmpNo;
	short lu=pDispInfo->bm.u, lv=pDispInfo->bm.v, lw=pDispInfo->bm.w, lh=pDispInfo->bm.h;

	if (SpriteInfo[ bmpNo ].width<=lu) return;
	if (SpriteInfo[ bmpNo ].height<=lv) return;
	if (lu+lw<0) return;
	if (lv+lh<0) return;

	if (lu<0) lu=0;
	if (lv<0) lv=0;
	if (lu+lw>=SpriteInfo[ bmpNo ].width) lw=SpriteInfo[ bmpNo ].width-lu;
	if (lv+lh>=SpriteInfo[ bmpNo ].height) lh=SpriteInfo[ bmpNo ].height-lv;

	// 絵の縦横のサーフェースの枚数取得
	wq=(SpriteInfo[ bmpNo ].width+SurfaceSizeX-1)/SurfaceSizeX;
	hq=(SpriteInfo[ bmpNo ].height+SurfaceSizeY-1)/SurfaceSizeY;

	// 切り取り始めのサーフェースの位置取得
	wfs=lu/SurfaceSizeX;
	hfs=lv/SurfaceSizeY;

	// 切り終わりのサーフェースの位置取得
	wes=(lu+lw+SurfaceSizeX-1)/SurfaceSizeX;
	hes=(lv+lh+SurfaceSizeY-1)/SurfaceSizeY;

	// 最初のサーフェースに移动
	psfi=SpriteInfo[ bmpNo ].lpSurfaceInfo;
	for(j=0;j<hfs;j++){
		for(i=0;i<wq;i++){
			if (psfi) psfi=psfi->pNext;
			else break;
		}
	}
	for(i=0;i<wfs;i++){
		if (psfi) psfi=psfi->pNext;
		else break;
	}

	for(j=hfs;j<hes;j++){
		psfi2=psfi;
		for(i=wfs;i<wes;i++){
			if (!psfi) break;
			if (
				( displayBpp != 8 && psfi->PalNo != PalState.palNo )||
				( displayBpp != 8 && psfi->PalCount != PalState.count )
				){
				// パレット番号とフェードのカウンターも学习
				psfi->PalNo = PalState.palNo;
				psfi->PalCount = PalState.count;
				psfi->AnimPalNo = pDispInfo->bm.PalNo;

				// 絵の左端、上端からのOFFセット值を求める
				ofX=psfi->offsetX,	ofY=SpriteInfo[ bmpNo ].height-psfi->offsetY;

				// 絵の読み込み
				if (pDispInfo->bm.PalNo){
					DrawBitmapToSurface_AnimPal( psfi->lpSurface, ofX, ofY-1,
						(SpriteInfo[ bmpNo ].width-ofX<SurfaceSizeX?SpriteInfo[ bmpNo ].width-ofX:SurfaceSizeX),
						(SpriteInfo[ bmpNo ].height-psfi->offsetY<SurfaceSizeY?SpriteInfo[ bmpNo ].height-psfi->offsetY:SurfaceSizeY),
						lpBmpInfo, pDispInfo->bm.PalNo, !(pDispInfo->bm.bltf&BLTF_NOCHG) );
				}else{
				}
			}
			psfi=psfi->pNext;
		}
		if (j<hq){
			psfi=psfi2;
			for(i=0;i<wq;i++){
				if (psfi) psfi=psfi->pNext;
				else break;
			}
		}
	}
}


/* ＢＭＰデータをクリアする ***********************************************/
void ClearBmpData()
{
	int i;

	/* サーフェスインフォ构造体の初期化　**************************************/
	InitSurfaceInfo();

	/* スプライトインフォ构造体の初期化　**************************************/
	// ＢＭＰの数だけループ */
	for( i = 0 ; i < MAX_GRAPHICS ; i++ ){
		// VRAM にいないことにする
		SpriteInfo[ i ].lpSurfaceInfo = NULL;
	}
}

#endif
