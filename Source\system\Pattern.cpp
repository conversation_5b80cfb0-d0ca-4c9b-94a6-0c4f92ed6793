﻿/************************/
/*	pattern.cpp			*/
/************************/


/*	ENT	:   ACTION *a0 , 
		:	アニメーション速度（０はｓｐｒ．ｂｉｎの速度），
		:	ループフラグ（ＡＮＭ＿ＬＯＯＰかＡＮＭ＿ＮＯ＿ＬＯＯＰ）

	RET	:   ０：ループしなかった
		:   １：ループした
*/
#include "../systeminc/system.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/t_music.h"
#include "../systeminc/action.h"
#include "../systeminc/pattern.h"
#include "../systeminc/process.h"
#ifdef PUK2
	#include "../systeminc/sprmgr.h"
#endif


//void StockDispBuffer2( int x, int y, UCHAR dispPrio, int bmpNo );

// アニメーション处理 **********************************************/
#ifdef PUK2
int pattern( ACTION *pAct, int anim_spd, int loop_flg, char mrr_flg )
#else
int pattern( ACTION *pAct, int anim_spd, int loop_flg )
#endif
{
	ANIMLIST	*ptAnimlist;
	FRAMELIST	*ptFramelist;
#ifdef PUK3_RIDEBIN
	ANIMCOORDINATE *ptCdlist;
#endif
	int i;
	short dx,dy;
	int BmpNo;
#ifdef MULTI_GRABIN
	int tmpNo;
#endif
	UINT chrNo;
	
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	// アニメーションＯＦＦの时返回
	if( pAct->animOffFlag == TRUE ) return 0;
#ifdef PUK3_RIDE
	pAct->animLoop_bak = loop_flg;
#endif
#if 1
	// 新しいアニメーションか动作か向きなら
	if( pAct->anim_no_bak != pAct->anim_no ||
		pAct->anim_ang != pAct->anim_ang_bak ||
		pAct->anim_chr_no != pAct->anim_chr_no_bak ){
		
		pAct->anim_chr_no_bak = pAct->anim_chr_no;	/* 新しいアニメーションセット*/
		pAct->anim_no_bak = pAct->anim_no;			/* 新しい动作セット*/
		pAct->anim_ang_bak = pAct->anim_ang;		/* 新しい向きセット*/
		pAct->anim_frame_cnt = 0;					/* アニメーションの时间クリア*/
		pAct->anim_cnt = 0;							/* パターンのクリア*/
		pAct->animLoopCnt = 0;
	}
#else
	// 新しいアニメーションか动作なら
	if( pAct->anim_no_bak != pAct->anim_no
	 || pAct->anim_chr_no != pAct->anim_chr_no_bak )
	{
		pAct->anim_chr_no_bak = pAct->anim_chr_no;	/* 新しいアニメーションセット*/
		pAct->anim_no_bak = pAct->anim_no;			/* 新しい动作セット*/
		pAct->anim_frame_cnt = 0;					/* アニメーションの时间クリア*/
		pAct->anim_cnt = 0;							/* パターンのクリア*/
		pAct->animLoopCnt = 0;
	}
	// 新しい向きなら
	if( pAct->anim_ang != pAct->anim_ang_bak )
	{
		pAct->anim_ang_bak = pAct->anim_ang;		/* 新しい向きセット*/
		if( loop_flg == ANM_NO_LOOP )
		{
			pAct->anim_frame_cnt = 0;					/* アニメーションの时间クリア*/
			pAct->anim_cnt = 0;							/* パターンのクリア*/
		}
	}
	
#endif

	// パターン变更の时间でなければ
	if( pAct->anim_frame_cnt ){		
		pAct->anim_frame_cnt--;	// カウントマイナス
		return 0;				// アニメーションは続く
	}
//	pAct->anim_frame_cnt = 2;		//１枚２割り込み
	
	// 画像のない番号を指定した时
	if( pAct->anim_chr_no <= CG_INVISIBLE )
	{
		pAct->bmpNo = 0;	// 画像のない番号を指定して见えなくする
		return 0;			// 予约番号なら拔ける
	}

#ifdef PUK2
////	pAct->bm.bltf&=~(BLTF_MRR_X|BLTF_MRR_Y);
#endif

	// アニメーション无いなら
	if( pAct->anim_chr_no < SPRSTART 		// アニメキャラ番号の范围でないなら
	||  pAct->anim_chr_no >= SPREND 		// そのままの番号のスプライト表示
	){ 	realGetNo( pAct->anim_chr_no, (U4 *)&BmpNo );
		realGetPos( BmpNo, &dx, &dy );
		pAct->bmpNo = BmpNo;	// ＢＭＰ番号セット
		pAct->anim_x = dx;		// Ｘ座标OFFセットセット
		pAct->anim_y = dy;		// Ｙ座标OFFセットセット
		pAct->anim_frame_cnt = 0x7fffffff;

#ifdef PUK2
		pAct->bm.PalNo = 0;					// アニメーションパレット番号
		pAct->bm.bltf = 0;					// 绘图设定
		pAct->bm.rgba.rgba = 0xffffffff;	// 色要素指定

		if (mrr_flg){
			pAct->bm.bltf&=~(BLTF_MRR_X|BLTF_MRR_Y);
			if (pAct->anim_ang>=0){
				if (1<pAct->anim_ang){if (pAct->anim_ang<5) pAct->bm.bltf|=BLTF_MRR_X; }
			}

			if ( pAct->bm.bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
				// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
				if ( GetBmpSize( pAct->bmpNo ) ){
					if (pAct->bm.bltf&BLTF_MRR_X) pAct->anim_x = -pAct->anim_x-SpriteInfo[ pAct->bmpNo ].width;		// Ｘ座标OFFセットセット
					if (pAct->bm.bltf&BLTF_MRR_Y) pAct->anim_y = -pAct->anim_y-SpriteInfo[ pAct->bmpNo ].height;		// Ｙ座标OFFセットセット
				}
			}
		}
#endif
#ifdef PUK3_RIDEBIN
		pAct->anim_cdx = 0;
		pAct->anim_cdy = 0;
#endif

		return 0;
	}

	// 规定值外なら拔ける
	if( pAct->anim_chr_no > SPRSTART + mxSPRITE ) return 1;	
	
	chrNo = pAct->anim_chr_no - SPRSTART;				// 实际の番号に直す
#ifdef MULTI_GRABIN
	tmpNo = Spr_Number2Tbl( chrNo );
	if( tmpNo < 0 )return 1;
	chrNo = (UINT)(tmpNo);
#endif

#ifdef ANIMATION_BUFFER_LESS	// アニメーションバッファ少なくするなら
	if( ReadAnimationData( chrNo ) == 0 )return -1;	// アニメーション読み込み
#endif

	ptAnimlist = SpriteData[ chrNo ].ptAnimlist;		// １アニメーションの情报取りだし
	if( SpriteData[ chrNo ].animSize == 0 ) return 1;	// アニメーションデータが无いなら拔ける

	// アニメーションの种类の设定（この处理は无駄かも、データの持ち方が恶いかも）
	for( i = 0 ; i < SpriteData[ chrNo ].animSize ; i++ ){
		// 方向と种类が同じ时拔ける
		if( pAct->anim_ang == ptAnimlist[ i ].dir && 
			ptAnimlist[ i ].no == pAct->anim_no ){
			break;
		}
	}
	
	// 设定できなかった时
	//if( i >= SpriteData[ chrNo ].animSize ) i = 0;
	
	// 设定できなかった时
	// アニメーション終った事にする
	if( i >= SpriteData[ chrNo ].animSize ) return 1;
	
	ptFramelist = ptAnimlist[ i ].ptFramelist;	// そのフレームの构造体ポインタをセット

	// １枚の表示时间セット
	pAct->anim_frame_cnt = ( ptAnimlist[ i ].dtAnim * anim_spd ) / ANM_NOMAL_SPD2 ;
	// １枚の表示时间セット
	//pAct->anim_frame_cnt = ( int )( ( float )ptAnimlist[ i ].dtAnim * ( ( float )anim_spd / ( float )ANM_NOMAL_SPD ) ) ;
	// リミットチェック
	if( pAct->anim_frame_cnt < 1 ) pAct->anim_frame_cnt = 1;

//	for( i = 0; ptFramelist[i].ptBmplist != NULL; i++ );
//	if(pAct->anim_cnt == i)		//アニメーションループなら
//		pAct->anim_cnt = 0;		//先头に戾す

	//アニメーション終ったら
	if( (U4)pAct->anim_cnt >= ptAnimlist[ i ].frameCnt ){	
		// ループしない时
		if( loop_flg == ANM_NO_LOOP ){		
			pAct->anim_frame_cnt = 255;
			pAct->animLoopCnt = 1;
			return 1;		//アニメーション終了
		} else {
			pAct->anim_cnt = 0;		//先头に戾す
			pAct->animLoopCnt++;
		}
	}

	//效果音再生なら
	if( ptFramelist[ pAct->anim_cnt ].SoundNo != 0 ){
		// 效果音番号が１００００未满の时
		if( ptFramelist[ pAct->anim_cnt ].SoundNo < 10000 ){
			// フィールド上のペットはうるさいので鸣らさない。
#ifdef PUK2
			if( ProcNo != PROC_GAME || !( (pAct->atr&ACT_ATR_TYPE_PET)||(pAct->atr&ACT_ATR_TYPE_TAKENPET) ) ){
#else
			if( ProcNo != PROC_GAME || !( pAct->atr & ACT_ATR_TYPE_PET ) ){
#endif
				// 自动で鸣らす
				play_se( ptFramelist[ pAct->anim_cnt ].SoundNo, pAct->x, pAct->y );
			}
		}else{
			// 攻击のインパクトの瞬间の时
			pAct->anim_hit = ptFramelist[ pAct->anim_cnt ].SoundNo;
		}
	}
#ifdef PUK2
	pAct->bm.bltf&=~(BLTF_MRR_X|BLTF_MRR_Y);
#endif

	pAct->bmpNo = ptFramelist[ pAct->anim_cnt ].BmpNo;			// ＢＭＰ番号セット
	realGetPos(ptFramelist[ pAct->anim_cnt ].BmpNo, &dx, &dy );	// アダーンビンのＸＹ座标取り出し
	pAct->anim_x = ptFramelist[ pAct->anim_cnt ].PosX + dx;		// Ｘ座标OFFセットセット
	pAct->anim_y = ptFramelist[ pAct->anim_cnt ].PosY + dy;		// Ｙ座标OFFセットセット
#ifdef PUK2
	pAct->bm.PalNo = ptAnimlist[ i ].PalNo;						// アニメーションパレット番号
	pAct->bm.bltf = (unsigned char)(ptAnimlist[ i ].bltf&0xff);	// 绘图设定
	pAct->bm.rgba.rgba = ptAnimlist[ i ].rgba;					// 色要素指定

	if ( pAct->bm.bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		if ( GetBmpSize( pAct->bmpNo ) ){
			if (pAct->bm.bltf&BLTF_MRR_X) pAct->anim_x = -pAct->anim_x-SpriteInfo[ pAct->bmpNo ].width;		// Ｘ座标OFFセットセット
			if (pAct->bm.bltf&BLTF_MRR_Y) pAct->anim_y = -pAct->anim_y-SpriteInfo[ pAct->bmpNo ].height;		// Ｙ座标OFFセットセット
		}
	}
#endif
#ifdef PUK3_RIDEBIN
	// 目的の座标データを探す
	ptCdlist = SpriteData[ chrNo ].ptCdlist;
	if (ptCdlist){
		for(i=0;i<SpriteData[ chrNo ].cdSize;i++){
			if ( ptCdlist[i].graNo == pAct->bmpNo ) break;
		}
	}

	// 座标データの设定
	pAct->anim_cdx = ptFramelist[ pAct->anim_cnt ].PosX;
	pAct->anim_cdy = ptFramelist[ pAct->anim_cnt ].PosY;
	if ( ptCdlist && i < SpriteData[ chrNo ].cdSize ){
		pAct->anim_cdx += ptCdlist[i].x;
		pAct->anim_cdy += ptCdlist[i].y;
	}
	if (pAct->bm.bltf&BLTF_MRR_X) pAct->anim_cdx = -pAct->anim_cdx;
	if (pAct->bm.bltf&BLTF_MRR_Y) pAct->anim_cdy = -pAct->anim_cdy;
#endif

	pAct->anim_cnt++;		//カウンター更新
	pAct->anim_frame_cnt--;		//
	return 0;
}

// アニメーション处理 **********************************************/
int pattern( ACTION *pAct, int loop_flg )
{
	ANIMLIST	*ptAnimlist;
	FRAMELIST	*ptFramelist;
#ifdef PUK3_RIDEBIN
	ANIMCOORDINATE *ptCdlist;
#endif
	int i;
	short dx,dy;
	int BmpNo;
#ifdef MULTI_GRABIN
	int tmpNo;
#endif
	UINT chrNo;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pAct );
#endif
	// アニメーションＯＦＦの时返回
	if( pAct->animOffFlag == TRUE ) return 0;

#ifdef PUK3_RIDE
	pAct->animLoop_bak = loop_flg;
#endif
#if 1
	// 新しいアニメーションか动作か向きなら
	if( pAct->anim_no_bak != pAct->anim_no ||
		pAct->anim_ang != pAct->anim_ang_bak ||
		pAct->anim_chr_no != pAct->anim_chr_no_bak ){
		
		pAct->anim_chr_no_bak = pAct->anim_chr_no;	/* 新しいアニメーションセット*/
		pAct->anim_no_bak = pAct->anim_no;			/* 新しい动作セット*/
		pAct->anim_ang_bak = pAct->anim_ang;		/* 新しい向きセット*/
		pAct->anim_frame_cnt = 0;					/* アニメーションの时间クリア*/
		pAct->anim_cnt = 0;							/* パターンのクリア*/
		pAct->animLoopCnt = 0;
	}
#else
	// 新しいアニメーションか动作なら
	if( pAct->anim_no_bak != pAct->anim_no
	 || pAct->anim_chr_no != pAct->anim_chr_no_bak )
	{
		pAct->anim_chr_no_bak = pAct->anim_chr_no;	/* 新しいアニメーションセット*/
		pAct->anim_no_bak = pAct->anim_no;			/* 新しい动作セット*/
		pAct->anim_frame_cnt = 0;					/* アニメーションの时间クリア*/
		pAct->anim_cnt = 0;							/* パターンのクリア*/
		pAct->animLoopCnt = 0;
	}
	// 新しい向きなら
	if( pAct->anim_ang != pAct->anim_ang_bak )
	{
		pAct->anim_ang_bak = pAct->anim_ang;		/* 新しい向きセット*/
		if( loop_flg == ANM_NO_LOOP )
		{
			pAct->anim_frame_cnt = 0;					/* アニメーションの时间クリア*/
			pAct->anim_cnt = 0;							/* パターンのクリア*/
		}
	}
#endif

	// パターン变更の时间でなければ
	if( pAct->anim_frame_cnt ){		
		pAct->anim_frame_cnt--;	// カウントマイナス
		return 0;				// アニメーションは続く
	}
//	pAct->anim_frame_cnt = 2;		//１枚２割り込み

#ifdef PUK2
////	pAct->bm.bltf&=~(BLTF_MRR_X|BLTF_MRR_Y);
#endif

	// 画像のない番号を指定した时
	if( pAct->anim_chr_no <= CG_INVISIBLE )
	{
		pAct->bmpNo = 0;	// 画像のない番号を指定して见えなくする
		return 0;			// 予约番号なら拔ける
	}

	// アニメーション无いなら
	if( pAct->anim_chr_no < SPRSTART ){
	
	 	realGetNo( pAct->anim_chr_no, (U4 *)&BmpNo );
		realGetPos( BmpNo, &dx, &dy );
		pAct->bmpNo = BmpNo;	// ＢＭＰ番号セット
		pAct->anim_x = dx;		// Ｘ座标OFFセットセット
		pAct->anim_y = dy;		// Ｙ座标OFFセットセット
		pAct->anim_frame_cnt = 0x7fffffff;

#ifdef PUK2
		pAct->bm.PalNo = 0;					// アニメーションパレット番号
		pAct->bm.bltf = 0;					// 绘图设定
		pAct->bm.rgba.rgba = 0xffffffff;	// 色要素指定

		pAct->bm.bltf&=~(BLTF_MRR_X|BLTF_MRR_Y);
		if (pAct->anim_ang>=0){
			if (1<pAct->anim_ang){if (pAct->anim_ang<5) pAct->bm.bltf|=BLTF_MRR_X; }
		}

		if ( pAct->bm.bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
			// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
			if ( GetBmpSize( pAct->bmpNo ) ){
				if (pAct->bm.bltf&BLTF_MRR_X) pAct->anim_x = -pAct->anim_x-SpriteInfo[ pAct->bmpNo ].width;		// Ｘ座标OFFセットセット
				if (pAct->bm.bltf&BLTF_MRR_Y) pAct->anim_y = -pAct->anim_y-SpriteInfo[ pAct->bmpNo ].height;		// Ｙ座标OFFセットセット
			}
		}
#endif
#ifdef PUK3_RIDEBIN
		pAct->anim_cdx = 0;
		pAct->anim_cdy = 0;
#endif

		return 0;
	}

	// 规定值外なら拔ける
	if( pAct->anim_chr_no > SPRSTART + mxSPRITE ) return 1;	
	
	chrNo = pAct->anim_chr_no - SPRSTART;				// 实际の番号に直す
#ifdef MULTI_GRABIN
	tmpNo = Spr_Number2Tbl( chrNo );
	if( tmpNo < 0 )return 1;
	chrNo = (UINT)(tmpNo);
#endif

#ifdef ANIMATION_BUFFER_LESS	// アニメーションバッファ少なくするなら
	if( ReadAnimationData( chrNo ) == 0 )return -1;	// アニメーション読み込み
#endif

	ptAnimlist = SpriteData[ chrNo ].ptAnimlist;		// １アニメーションの情报取りだし
	if( SpriteData[ chrNo ].animSize == 0 ) return 1;	// アニメーションデータが无いなら拔ける

	// アニメーションの种类の设定（この处理は无駄かも、データの持ち方が恶いかも）
	for( i = 0 ; i < SpriteData[ chrNo ].animSize ; i++ ){
		// 方向と种类が同じ时拔ける
		if( pAct->anim_ang == ptAnimlist[ i ].dir && 
			ptAnimlist[ i ].no == pAct->anim_no ){
			break;
		}
	}
	
	// 设定できなかった时
//	if( i >= SpriteData[ chrNo ].animSize ) i = 0;
//	ptFramelist = ptAnimlist[ i ].ptFramelist;	// そのフレームの构造体ポインタをセット
	
	// 设定できなかった时
	// アニメーション終った事にする
	if( i >= SpriteData[ chrNo ].animSize ) return 1;
	
	ptFramelist = ptAnimlist[ i ].ptFramelist;	// そのフレームの构造体ポインタをセット

	// １枚の表示时间セット
	pAct->anim_frame_cnt = ( ptAnimlist[ i ].dtAnim * pAct->anim_speed ) / ANM_NOMAL_SPD2 ;
	// １枚の表示时间セット
	//pAct->anim_frame_cnt = ( int )( ( float )ptAnimlist[ i ].dtAnim * ( float )( ( float )pAct->anim_speed / ( float )ANM_NOMAL_SPD ) ) ;
	//pAct->anim_frame_cnt = 5;
	// リミットチェック
	if( pAct->anim_frame_cnt < 1 ) pAct->anim_frame_cnt = 1;

//	for( i = 0; ptFramelist[i].ptBmplist != NULL; i++ );
//	if(pAct->anim_cnt == i)		//アニメーションループなら
//		pAct->anim_cnt = 0;		//先头に戾す

	//アニメーション終ったら
	if( (U4)pAct->anim_cnt >= ptAnimlist[ i ].frameCnt ){	
		// ループしない时
		if( loop_flg == ANM_NO_LOOP ){		
			pAct->anim_frame_cnt = 255;
			pAct->animLoopCnt = 1;
			return 1;		//アニメーション終了
		} else {
			pAct->anim_cnt = 0;		//先头に戾す
			pAct->animLoopCnt++;
		}
	}

	//效果音再生なら
	if( ptFramelist[ pAct->anim_cnt ].SoundNo != 0 ){
		// 效果音番号が１００００未满の时
		if( ptFramelist[ pAct->anim_cnt ].SoundNo < 10000 ){
			// フィールド上のペットはうるさいので鸣らさない。
#ifdef PUK2
			if( ProcNo != PROC_GAME || !( (pAct->atr&ACT_ATR_TYPE_PET)||(pAct->atr&ACT_ATR_TYPE_TAKENPET) ) ){
#else
			if( ProcNo != PROC_GAME || !( pAct->atr & ACT_ATR_TYPE_PET ) ){
#endif
				// 自动で鸣らす
				play_se( ptFramelist[ pAct->anim_cnt ].SoundNo, pAct->x, pAct->y );
			}
		}else{
			// 攻击のインパクトの瞬间の时
			pAct->anim_hit = ptFramelist[ pAct->anim_cnt ].SoundNo;
		}
	}
#ifdef PUK2
	pAct->bm.bltf&=~(BLTF_MRR_X|BLTF_MRR_Y);
#endif

	pAct->bmpNo = ptFramelist[ pAct->anim_cnt ].BmpNo;			// ＢＭＰ番号セット
	realGetPos(ptFramelist[ pAct->anim_cnt ].BmpNo, &dx, &dy );	// アダーンビンのＸＹ座标取り出し
	pAct->anim_x = ptFramelist[ pAct->anim_cnt ].PosX + dx;		// Ｘ座标OFFセットセット
	pAct->anim_y = ptFramelist[ pAct->anim_cnt ].PosY + dy;		// Ｙ座标OFFセットセット
#ifdef PUK2
	pAct->bm.PalNo = ptAnimlist[ i ].PalNo;						// アニメーションパレット番号
	pAct->bm.bltf = (unsigned char)(ptAnimlist[ i ].bltf&0xff);	// 绘图设定
	pAct->bm.rgba.rgba = ptAnimlist[ i ].rgba;					// 色要素指定

	if ( pAct->bm.bltf&(BLTF_MRR_X|BLTF_MRR_Y) ){
		// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
		if ( GetBmpSize( pAct->bmpNo ) ){
			if (pAct->bm.bltf&BLTF_MRR_X) pAct->anim_x = -pAct->anim_x-SpriteInfo[ pAct->bmpNo ].width;		// Ｘ座标OFFセットセット
			if (pAct->bm.bltf&BLTF_MRR_Y) pAct->anim_y = -pAct->anim_y-SpriteInfo[ pAct->bmpNo ].height;		// Ｙ座标OFFセットセット
		}
	}
#endif
#ifdef PUK3_RIDEBIN
	// 目的の座标データを探す
	ptCdlist = SpriteData[ chrNo ].ptCdlist;
	if (ptCdlist){
		for(i=0;i<SpriteData[ chrNo ].cdSize;i++){
			if ( ptCdlist[i].graNo == pAct->bmpNo ) break;
		}
	}

	// 座标データの设定
	pAct->anim_cdx = ptFramelist[ pAct->anim_cnt ].PosX;
	pAct->anim_cdy = ptFramelist[ pAct->anim_cnt ].PosY;
	if ( ptCdlist && i < SpriteData[ chrNo ].cdSize ){
		pAct->anim_cdx += ptCdlist[i].x;
		pAct->anim_cdy += ptCdlist[i].y;
	}
	if (pAct->bm.bltf&BLTF_MRR_X) pAct->anim_cdx = -pAct->anim_cdx;
	if (pAct->bm.bltf&BLTF_MRR_Y) pAct->anim_cdy = -pAct->anim_cdy;
#endif

	pAct->anim_cnt++;		//カウンター更新
	pAct->anim_frame_cnt--;		//
	return 0;
}


#if 0

/*----------　アニメーション处理　----------*/
int pattern( ACTION *a0, int anim_spd, int loop_flg )
{
	ANIMLIST	*ptAnimlist;
	FRAMELIST	*ptFramelist;
	int i;
	short dx,dy;
	int BmpNo;
	U4 chrNo;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( a0 );
#endif
	/*新しいアニメーションか动作か向きなら*/
	if(ATR_CHR_ACT_OLD(a0) != ATR_CHR_ACT(a0) ||
	ATR_CHR_ANG(a0) != ATR_CHR_ANG_OLD(a0) ||
	ATR_CHR_NO(a0) != ATR_CHR_NO_OLD(a0)){
		ATR_CHR_NO_OLD(a0) = ATR_CHR_NO(a0);		/*新しいアニメーションセット*/
		ATR_CHR_ACT_OLD(a0) = ATR_CHR_ACT(a0);		/*新しい动作セット*/
		ATR_CHR_ANG_OLD(a0) = ATR_CHR_ANG(a0);		/*新しい向きセット*/
		ATR_CHR_TIM(a0) = 0;		/*アニメーションの时间クリア*/
		ATR_CHR_CNT(a0) = 0;		/*パターンのクリア*/
	}
	if(ATR_CHR_TIM(a0)){		/*パターン变更の时间でなければ*/
		ATR_CHR_TIM(a0)--;
		return 0;		/*アニメーションは続く*/
	}
//	ATR_CHR_TIM(a0)=2;		//１枚２割り込み

	if(ATR_CHR_NO(a0) <= CG_INVISIBLE)
	{
		ATR_PAT_NO(a0) = 0;	//画像のない番号を指定して见えなくする
		return 0;			//予约番号なら拔ける
	}

	if(ATR_CHR_NO(a0) < SPRSTART){		//无いなら
	 	realGetNo( ATR_CHR_NO(a0) , (U4 *)&BmpNo );
		realGetPos( BmpNo  , &dx, &dy);
		ATR_PAT_NO(a0) = BmpNo;			//ＢＭＰ番号セット
		ATR_CHR_H_POS(a0) = dx;		//Ｘ座标OFFセットセット
		ATR_CHR_V_POS(a0) = dy;		//Ｙ座标OFFセットセット
		ATR_CHR_TIM(a0)=0x7fffffff;
		return 0;
	}

	if(ATR_CHR_NO(a0) > SPRSTART + mxSPRITE) return 0;	// 规定值外なら拔ける

	chrNo = ATR_CHR_NO(a0) - SPRSTART;
	ptAnimlist = SpriteData[chrNo].ptAnimlist;
	if( SpriteData[chrNo].animSize == 0 ) return 0;		// アニメーションデータが无いなら拔ける

	//アニメーションの种类の设定
	for(i=0 ; i < SpriteData[chrNo].animSize; i++){
		if( ATR_CHR_ANG(a0) == ptAnimlist[i].dir && ptAnimlist[i].no == ATR_CHR_ACT(a0) ){
			break;
		}
	}
	if( i >= SpriteData[chrNo].animSize )
		i = 0;
	ptFramelist = ptAnimlist[i].ptFramelist;

	if(anim_spd){		//アニメーション速度指定なら
		ATR_CHR_TIM(a0) = anim_spd;		//１枚の表示时间セット
	} else {
		ATR_CHR_TIM(a0) = ptAnimlist[i].dtAnim;		//１枚の表示时间セット
	}

//	for( i = 0; ptFramelist[i].ptBmplist != NULL; i++ );
//	if(ATR_CHR_CNT(a0) == i)		//アニメーションループなら
//		ATR_CHR_CNT(a0) = 0;		//先头に戾す

	if((U4)ATR_CHR_CNT(a0) >= ptAnimlist[i].frameCnt){	//アニメーションループなら
		if(loop_flg == ANM_NO_LOOP){		//ループ无しか
			ATR_CHR_TIM(a0) = 255;
			return 1;		//アニメーション終了
		} else {
			ATR_CHR_CNT(a0) = 0;		//先头に戾す
		}
	}

	//效果音再生なら
	if( ptFramelist[ATR_CHR_CNT(a0)].SoundNo != 0 ){
		if(ptFramelist[ATR_CHR_CNT(a0)].SoundNo < 10000){
			play_se( ptFramelist[ATR_CHR_CNT(a0)].SoundNo, ATR_H_POS(a0), ATR_V_POS(a0) );
		} else {
			ATR_HIT(a0) = ptFramelist[ATR_CHR_CNT(a0)].SoundNo;
		}
	}

	ATR_PAT_NO(a0) = ptFramelist[ATR_CHR_CNT(a0)].BmpNo;			//ＢＭＰ番号セット
	realGetPos(ptFramelist[ATR_CHR_CNT(a0)].BmpNo, &dx, &dy);		//アダーンビンのＸＹ座标取り出し
	ATR_CHR_H_POS(a0) = ptFramelist[ATR_CHR_CNT(a0)].PosX + dx;		//Ｘ座标OFFセットセット
	ATR_CHR_V_POS(a0) = ptFramelist[ATR_CHR_CNT(a0)].PosY + dy;		//Ｙ座标OFFセットセット
	ATR_CHR_CNT(a0)++;		//カウンター更新
	ATR_CHR_TIM(a0)--;		//
	return 0;
}

int pattern( ACTION *a0, int anim_spd, int loop_flg )
{
	ANIMLIST	*ptAnimlist;
	FRAMELIST	*ptFramelist;
	BMPLIST		*ptBmplist;
	int i;
	short dx,dy;
	int BmpNo;

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( a0 );
#endif
	/*新しいアニメーションか动作か向きなら*/
	if(ATR_CHR_ACT_OLD(a0) != ATR_CHR_ACT(a0) ||
	ATR_CHR_ANG(a0) != ATR_CHR_ANG_OLD(a0) ||
	ATR_CHR_NO(a0) != ATR_CHR_NO_OLD(a0)){
		ATR_CHR_NO_OLD(a0) = ATR_CHR_NO(a0);		/*新しいアニメーションセット*/
		ATR_CHR_ACT_OLD(a0) = ATR_CHR_ACT(a0);		/*新しい动作セット*/
		ATR_CHR_ANG_OLD(a0) = ATR_CHR_ANG(a0);		/*新しい向きセット*/
		ATR_CHR_TIM(a0) = 0;		/*アニメーションの时间クリア*/
		ATR_CHR_CNT(a0) = 0;		/*パターンのクリア*/
	}
	if(ATR_CHR_TIM(a0)){		/*パターン变更の时间でなければ*/
		ATR_CHR_TIM(a0)--;
		return 0;		/*アニメーションは続く*/
	}
//	ATR_CHR_TIM(a0)=2;		//１枚２割り込み
	if (ATR_CHR_NO(a0) >= mxSPRITE || ATR_CHR_NO(a0) <= CG_INVISIBLE) return 0;		//无いなら拔ける
	ptAnimlist = SpriteData[ATR_CHR_NO(a0)].ptAnimlist;		//？？？アドレスセット
	if (ptAnimlist == NULL){		//无いなら
	 	realGetNo( ATR_CHR_NO(a0) , (U4 *)&BmpNo );
		realGetPos( BmpNo  , &dx, &dy);
		ATR_PAT_NO(a0) = BmpNo;			//ＢＭＰ番号セット
		ATR_CHR_H_POS(a0) = dx;		//Ｘ座标OFFセットセット
		ATR_CHR_V_POS(a0) = dy;		//Ｙ座标OFFセットセット
		ATR_CHR_TIM(a0)=0x7fffffff;
		return 0;
	}

	//アニメーションの种类の设定
	for (i=0 ;  ; i++){
		if ( ptAnimlist[i].ptFramelist == NULL ){
			if (i==0) return 0;
			i = 0;
			break;
		}
		if ( ATR_CHR_ANG(a0) == ptAnimlist[i].dir && ptAnimlist[i].id == ATR_CHR_ACT(a0) ){
			break;
		}
	}
	ptFramelist = ptAnimlist[i].ptFramelist;

	//效果音再生なら
	if( ptFramelist[ATR_CHR_CNT(a0)].SoundNo != 0 ){
		play_se( ptFramelist[ATR_CHR_CNT(a0)].SoundNo, ATR_H_POS(a0), ATR_V_POS(a0) );
	}

	if(anim_spd){		//アニメーション速度指定なら
		ATR_CHR_TIM(a0) = anim_spd;		//１枚の表示时间セット
	} else {
		ATR_CHR_TIM(a0) = ptAnimlist[i].frameCnt;		//１枚の表示时间セット
	}

//	for( i = 0; ptFramelist[i].ptBmplist != NULL; i++ );
//	if(ATR_CHR_CNT(a0) == i)		//アニメーションループなら
//		ATR_CHR_CNT(a0) = 0;		//先头に戾す

	if(ptFramelist[ATR_CHR_CNT(a0)].ptBmplist == NULL){		//アニメーションループなら
		if(loop_flg == ANM_NO_LOOP){		//ループ无しか
			ATR_CHR_TIM(a0) = 255;
			return 1;		//アニメーション終了
		} else {
			ATR_CHR_CNT(a0) = 0;		//先头に戾す
		}
	}
	ptBmplist = ptFramelist[ATR_CHR_CNT(a0)].ptBmplist;		//今回の
#if 0
	for (i=0 ; ptBmplist[i].ptBmppos != NULL ; i++ ){		//枚数分表示
		if(!i){
			ATR_PAT_NO(a0) = ptBmplist[i].ptBmppos->BmpNo;			//ＢＭＰ番号セット
			realGetPos(ptBmplist[i].ptBmppos->BmpNo, &dx, &dy);		//アダーンビンのＸＹ座标取り出し
			ATR_CHR_H_POS(a0) = ptBmplist[i].PosX + dx;		//Ｘ座标OFFセットセット
			ATR_CHR_V_POS(a0) = ptBmplist[i].PosY + dy;		//Ｙ座标OFFセットセット
		} else {
			realGetPos(ptBmplist[i].ptBmppos->BmpNo, &dx, &dy);		//アダーンビンのＸＹ座标取り出し
			StockDispBuffer2( ATR_H_POS(a0) + ptBmplist[i].PosX + dx,
			ATR_V_POS(a0) + ptBmplist[i].PosY + dy,
			a0->dispPrio,
			ptBmplist[i].ptBmppos->BmpNo );
		}
	}

#else
	ATR_PAT_NO(a0) = ptBmplist[0].ptBmppos->BmpNo;			//ＢＭＰ番号セット
	realGetPos(ptBmplist[0].ptBmppos->BmpNo, &dx, &dy);		//アダーンビンのＸＹ座标取り出し
	ATR_CHR_H_POS(a0) = ptBmplist[0].PosX + dx;		//Ｘ座标OFFセットセット
	ATR_CHR_V_POS(a0) = ptBmplist[0].PosY + dy;		//Ｙ座标OFFセットセット
#endif
	ATR_CHR_CNT(a0)++;		//カウンター更新
	ATR_CHR_TIM(a0)--;		//
	return 0;

#if 0	//未使用//////////////////////////////////////////////////////
	// １フレームアニメーション枚数を计算
	for( i = 0; ptFramelist[i].ptBmplist != NULL; i++ );
	animSize = i;

	//现在のフレーム位置を取得
	if (ptsprite->ctAnim < 1){		//１未满モード
		ptBmplist = ptFramelist[(int)(ptsprite->ctAnim * animSize)].ptBmplist;
	}else{
		ptBmplist = ptFramelist[(int)(ptsprite->ctAnim)                        ].ptBmplist;
	}

	//1フレーム内の
	for (i=0 ; ptBmplist[i].ptBmppos != NULL ; i++ ){
		realGetPos(ptBmplist[i].ptBmppos->BmpNo, &dx, &dy);
		drawImage( ptBmplist[i].ptBmppos->BmpNo , 
				  x + ptBmplist[i].ptBmppos->PosX + ptBmplist[i].PosX + dx ,
				  y + ptBmplist[i].ptBmppos->PosY + ptBmplist[i].PosY + dy);
	}
#endif	/////////////////////////////////////////////////////////////

}

#endif

#ifdef PUK2
	#include "../PUK2/newDraw/newPattern.cpp"
#endif
