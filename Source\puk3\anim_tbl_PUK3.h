﻿/***************************************								
			anim_tbl_PUK3.h					
***************************************/								

#ifndef _ANIM_TBL_PUK3
#define _ANIM_TBL_PUK3

// アニメーション ********************//
								
//PUK3モンスター*****************************************************
#define SPR_mon1999a	110505		//ゴースト１	メイジゴースト	
#define SPR_mon1999b	110506		//ゴースト２	ドルイドゴースト
#define SPR_mon1999c	110507		//ゴースト３	レッドゴースト
#define SPR_mon1999d	110508		//ゴースト４	ウィザードゴースト

#define SPR_mon1998a	110509		//プチバット	ウィルバーバット
#define SPR_mon1998b	110510		//プチバット	オービルバット
#define SPR_mon1998c	110511		//プチバット	オイラーバット
#define SPR_mon1998d	110512		//プチバット	オットーバット
#define SPR_mon1998e	110513		//プチバット	ミシェルバット
#define SPR_mon1998f	110514		//プチバット	エティンヌバット

#define SPR_mon2000a	110520		//云ボス1	ボスはシナリオ担当者が命名;リヴァイアサン
#define SPR_mon2000b	110521		//云ボス2	ボスはシナリオ担当者が命名;ヴォジャノイ
#define SPR_mon2000c	110522		//云ボス3	ボスはシナリオ担当者が命名;テュポン	
#define SPR_mon2000d	110523		//云ボス4	ボスはシナリオ担当者が命名			
#define SPR_mon2000e	110524		//云ボス5	ボスはシナリオ担当者が命名			
#define SPR_mon2000f	110525		//云ボス6	ボスはシナリオ担当者が命名			

#define SPR_mon2001a	110526		//マンタ1	レイ
#define SPR_mon2001b	110527		//マンタ2	ロッヘン
#define SPR_mon2001c	110528		//マンタ3	ラッザ
#define SPR_mon2001d	110529		//マンタ4	ラジャー
#define SPR_mon2001e	110530		//マンタ5	ライア
#define SPR_mon2001f	110531		//マンタ6	ラヤ

#define SPR_mon2002a	110532		//ロデムボス１	ボスはシナリオ担当者が命名
#define SPR_mon2002b	110533		//ロデムボス２	ボスはシナリオ担当者が命名
#define SPR_mon2002c	110534		//ロデムボス３	ボスはシナリオ担当者が命名
#define SPR_mon2002d	110535		//ロデムボス４	ボスはシナリオ担当者が命名
#define SPR_mon2002e	110536		//ロデムボス５	ボスはシナリオ担当者が命名
#define SPR_mon2002f	110537		//ロデムボス６	ボスはシナリオ担当者が命名

#define SPR_mon2003a	110538		//うにょうにょ1	ボスはシナリオ担当者が命名;ウーゼイ
#define SPR_mon2003b	110539		//うにょうにょ2	ボスはシナリオ担当者が命名;ティンテン
#define SPR_mon2003c	110540		//うにょうにょ3	ボスはシナリオ担当者が命名;セーシュ
#define SPR_mon2003d	110541		//うにょうにょ4	ボスはシナリオ担当者が命名;ヒビア
#define SPR_mon2003e	110542		//うにょうにょ5	ボスはシナリオ担当者が命名;セッピア
#define SPR_mon2003f	110543		//うにょうにょ6	ボスはシナリオ担当者が命名;カラカーチツァ

#define SPR_mon2004a	110544		//1/2ロボ	魔术机　甲
#define SPR_mon2004b	110545		//1/2ロボ	魔术机　乙
#define SPR_mon2004c	110546		//1/2ロボ	魔术机　丙
#define SPR_mon2004d	110547		//1/2ロボ	魔术机　丁
#define SPR_mon2004e	110548		//1/2ロボ	魔术机　戊
#define SPR_mon2004f	110549		//1/2ロボ	魔术机	己

#define SPR_mon2005a	110550		//ユキポン(tobe)	
#define SPR_mon2005b	110551		//ユキポン(tobe)	
#define SPR_mon2005c	110552		//ユキポン(tobe)	
#define SPR_mon2005d	110553		//ユキポン(tobe)	
#define SPR_mon2005e	110554		//ユキポン(tobe)	
#define SPR_mon2005f	110555		//ユキポン(tobe)	

#define SPR_mon2006a	110556		//トベネコ(tobe)	バステト
#define SPR_mon2006b	110557		//トベネコ(tobe)	シュレディンガー
#define SPR_mon2006c	110558		//トベネコ(tobe)	キャットキャップ
#define SPR_mon2006d	110559		//トベネコ(tobe)	キャットメア
#define SPR_mon2006e	110560		//トベネコ(tobe)	フルフェイス	
#define SPR_mon2006f	110561		//トベネコ(tobe)	セクメト

#define SPR_mon2007a	110562		//ヨロイ人		ボスはシナリオ担当者が命名
#define SPR_mon2007b	110563		//ヨロイ人		ボスはシナリオ担当者が命名
#define SPR_mon2007c	110564		//ヨロイ人		ボスはシナリオ担当者が命名
#define SPR_mon2007d	110565		//ヨロイ人		ボスはシナリオ担当者が命名
#define SPR_mon2007e	110566		//ヨロイ人		ボスはシナリオ担当者が命名
#define SPR_mon2007f	110567		//ヨロイ人		ボスはシナリオ担当者が命名
				
#define SPR_mon2008a	110568		//ヨロイ獣通常	タッツェルヴルム
#define SPR_mon2008b	110569		//ヨロイ獣铜		サウロペルタ
#define SPR_mon2008c	110570		//ヨロイ獣银		タラルルス
#define SPR_mon2008d	110571		//ヨロイ獣金		スケリドサウルス			
#define SPR_mon2008e	110572		//ヨロイ獣－		ポラカントス			
#define SPR_mon2008f	110573		//ヨロイ獣－		タラルルス			

#define SPR_mon2009a	110574		//ロデム獣通常	マ－イョル
#define SPR_mon2009b	110575		//ロデム		ルプス
#define SPR_mon2009c	110576		//ロデム		フルド
#define SPR_mon2009d	110577		//ロデム		ムリフェイン
#define SPR_mon2009e	110578		//ロデム		カニス
#define SPR_mon2009f	110579		//ロデム		ルーパス
#define SPR_mon2009g	110580		//ロデム		レラプス
#define SPR_mon2009h	110581		//ロデム		アルドーラ
#define SPR_mon2009i	110582		//ロデム		アダラ
#define SPR_mon2009j	110583		//ロデム		ウェズン
#define SPR_mon2009k	110584		//ロデム		ミルザム
#define SPR_mon2009l	110585		//ロデム阴キャラ	シリウス

#define SPR_mon2010a	110586		//ヨロイ人ボス１	ボスはシナリオ担当者が命名
#define SPR_mon2010b	110587		//	２	ボスはシナリオ担当者が命名
#define SPR_mon2010c	110588		//	３	ボスはシナリオ担当者が命名
#define SPR_mon2010d	110589		//	４	ボスはシナリオ担当者が命名
#define SPR_mon2010e	110590		//	５	ボスはシナリオ担当者が命名
#define SPR_mon2010f	110591		//	６	ボスはシナリオ担当者が命名



#define SPR_mon2011a	110592		//トベプチ１	スネコスリ
#define SPR_mon2011b	110593		//	２	キジムナー
#define SPR_mon2011c	110594		//	３	ガジュマル
#define SPR_mon2011d	110595		//	４	プーカ
#define SPR_mon2011e	110596		//	５	パック
#define SPR_mon2011f	110597		//	６	マルゲ



#define SPR_mon1998g	110598		//プチバット
#define SPR_mon2100		110599		// 歌姬モンスター			// 2005/11追加予定




//-----------------puk3----------------------------------
#define SPR_mon2012a	110770		//トベボス１	アーミー
#define SPR_mon2012b	110771		//	２	カンパニー
#define SPR_mon2012c	110772		//	３	プラトゥーン
#define SPR_mon2012d	110773		//	４	カルテット
#define SPR_mon2012e	110774		//	５	グループ
#define SPR_mon2012f	110775		//	６	バンド

#define SPR_mon2013a	110776		//ミズモン１	ローズクリープ
#define SPR_mon2013b	110777		//	２	ミルククリープ
#define SPR_mon2013c	110778		//	３	ワインクリープ
#define SPR_mon2013d	110779		//	４	マラカイトクリープ
#define SPR_mon2013e	110780		//	５	シルバークリープ
#define SPR_mon2013f	110781		//	６	ハニークリープ
//-----------------puk3----------------------------------

//-----------------乘り物Puk3----------------------------------
#define SPR_norimon01a	110782		//ノリモン１ゾウ	
#define SPR_norimon01b	110783		//	2
#define SPR_norimon01c	110784		//	3
#define SPR_norimon01d	110785		//	4
#define SPR_norimon01e	110786		//	5
#define SPR_norimon01f	110787		//	6
#define SPR_norimon02a	110788		//ノリモン2	
#define SPR_norimon02b	110789		//	2
#define SPR_norimon02c	110790		//	3
#define SPR_norimon02d	110791		//	4
#define SPR_norimon02e	110792		//	5
#define SPR_norimon02f	110793		//	6
#define SPR_norimon03a	110794		//ノリモン3	
#define SPR_norimon03b	110795		//	2
#define SPR_norimon03c	110796		//	3
#define SPR_norimon03d	110797		//	4
#define SPR_norimon03e	110798		//	5
#define SPR_norimon03f	110799		//	6
//-----------------乘り物Puk3----------------------------------





/*114000～114167までは、座りキャラに使用します。（中岛）*/								
//14体＋28体＝42  42*4=168								
#define SPR_s_000				114000		//座りＰＣ1a		
#define SPR_s_001				114001		//座りＰＣ1b		
#define SPR_s_002				114002		//座りＰＣ1c		
#define SPR_s_003				114003		//座りＰＣ1d		
#define SPR_s_010				114004		//座りＰＣ2a		
#define SPR_s_011				114005		//座りＰＣ2b		
#define SPR_s_012				114006		//座りＰＣ2c		
#define SPR_s_013				114007		//座りＰＣ2d		
#define SPR_s_020				114008		//座りＰＣ3a		
#define SPR_s_021				114009		//座りＰＣ3b		
#define SPR_s_022				114010		//座りＰＣ3c		
#define SPR_s_023				114011		//座りＰＣ3d		
#define SPR_s_030				114012		//座りＰＣ4a		
#define SPR_s_031				114013		//座りＰＣ4b		
#define SPR_s_032				114014		//座りＰＣ4c		
#define SPR_s_033				114015		//座りＰＣ4d		
#define SPR_s_040				114016		//座りＰＣ5a		
#define SPR_s_041				114017		//座りＰＣ5b		
#define SPR_s_042				114018		//座りＰＣ5c		
#define SPR_s_043				114019		//座りＰＣ5d		
#define SPR_s_050				114020		//座りＰＣ6a		
#define SPR_s_051				114021		//座りＰＣ6b		
#define SPR_s_052				114022		//座りＰＣ6c		
#define SPR_s_053				114023		//座りＰＣ6d		
#define SPR_s_060				114024		//座りＰＣ7a		
#define SPR_s_061				114025		//座りＰＣ7b		
#define SPR_s_062				114026		//座りＰＣ7c		
#define SPR_s_063				114027		//座りＰＣ7d		
#define SPR_s_200				114028		//座りＰＣ8a		
#define SPR_s_201				114029		//座りＰＣ8b		
#define SPR_s_202				114030		//座りＰＣ8c		
#define SPR_s_203				114031		//座りＰＣ8d		
#define SPR_s_210				114032		//座りＰＣ9a		
#define SPR_s_211				114033		//座りＰＣ9b		
#define SPR_s_212				114034		//座りＰＣ9c		
#define SPR_s_213				114035		//座りＰＣ9d		
#define SPR_s_220				114036		//座りＰＣ10a		
#define SPR_s_221				114037		//座りＰＣ10b		
#define SPR_s_222				114038		//座りＰＣ10c		
#define SPR_s_223				114039		//座りＰＣ10d		
#define SPR_s_230				114040		//座りＰＣ11a		
#define SPR_s_231				114041		//座りＰＣ11b		
#define SPR_s_232				114042		//座りＰＣ11c		
#define SPR_s_233				114043		//座りＰＣ11d		
#define SPR_s_240				114044		//座りＰＣ12a		
#define SPR_s_241				114045		//座りＰＣ12b		
#define SPR_s_242				114046		//座りＰＣ12c		
#define SPR_s_243				114047		//座りＰＣ12d		
#define SPR_s_250				114048		//座りＰＣ13a		
#define SPR_s_251				114049		//座りＰＣ13b		
#define SPR_s_252				114050		//座りＰＣ13c		
#define SPR_s_253				114051		//座りＰＣ13d		
#define SPR_s_260				114052		//座りＰＣ14a		
#define SPR_s_261				114053		//座りＰＣ14b		
#define SPR_s_262				114054		//座りＰＣ14c		
#define SPR_s_263				114055		//座りＰＣ14d		
#define SPR_s_400				114056		//座りＰＣ15a		
#define SPR_s_401				114057		//座りＰＣ15b		
#define SPR_s_402				114058		//座りＰＣ15c		
#define SPR_s_403				114059		//座りＰＣ15d		
#define SPR_s_410				114060		//座りＰＣ16a		
#define SPR_s_411				114061		//座りＰＣ16b		
#define SPR_s_412				114062		//座りＰＣ16c		
#define SPR_s_413				114063		//座りＰＣ16d		
#define SPR_s_420				114064		//座りＰＣ17a		
#define SPR_s_421				114065		//座りＰＣ17b		
#define SPR_s_422				114066		//座りＰＣ17c		
#define SPR_s_423				114067		//座りＰＣ17d		
#define SPR_s_430				114068		//座りＰＣ18a		
#define SPR_s_431				114069		//座りＰＣ18b		
#define SPR_s_432				114070		//座りＰＣ18c		
#define SPR_s_433				114071		//座りＰＣ18d		
#define SPR_s_440				114072		//座りＰＣ19a		
#define SPR_s_441				114073		//座りＰＣ19b		
#define SPR_s_442				114074		//座りＰＣ19c		
#define SPR_s_443				114075		//座りＰＣ19d		
#define SPR_s_450				114076		//座りＰＣ20a		
#define SPR_s_451				114077		//座りＰＣ20b		
#define SPR_s_452				114078		//座りＰＣ20c		
#define SPR_s_453				114079		//座りＰＣ20d		
#define SPR_s_460				114080		//座りＰＣ21a		
#define SPR_s_461				114081		//座りＰＣ21b		
#define SPR_s_462				114082		//座りＰＣ21c		
#define SPR_s_463				114083		//座りＰＣ21d		
#define SPR_s_500				114084		//座りＰＣ22a		
#define SPR_s_501				114085		//座りＰＣ22b		
#define SPR_s_502				114086		//座りＰＣ22c		
#define SPR_s_503				114087		//座りＰＣ22d		
#define SPR_s_510				114088		//座りＰＣ23a		
#define SPR_s_511				114089		//座りＰＣ23b		
#define SPR_s_512				114090		//座りＰＣ23c		
#define SPR_s_513				114091		//座りＰＣ23d		
#define SPR_s_520				114092		//座りＰＣ24a		
#define SPR_s_521				114093		//座りＰＣ24b		
#define SPR_s_522				114094		//座りＰＣ24c		
#define SPR_s_523				114095		//座りＰＣ24d		
#define SPR_s_530				114096		//座りＰＣ25a		
#define SPR_s_531				114097		//座りＰＣ25b		
#define SPR_s_532				114098		//座りＰＣ25c		
#define SPR_s_533				114099		//座りＰＣ25d		
#define SPR_s_540				114100		//座りＰＣ26a		
#define SPR_s_541				114101		//座りＰＣ26b		
#define SPR_s_542				114102		//座りＰＣ26c		
#define SPR_s_543				114103		//座りＰＣ26d		
#define SPR_s_550				114104		//座りＰＣ27a		
#define SPR_s_551				114105		//座りＰＣ27b		
#define SPR_s_552				114106		//座りＰＣ27c		
#define SPR_s_553				114107		//座りＰＣ27d		
#define SPR_s_560				114108		//座りＰＣ28a		
#define SPR_s_561				114109		//座りＰＣ28b		
#define SPR_s_562				114110		//座りＰＣ28c		
#define SPR_s_563				114111		//座りＰＣ28d		
#define SPR_s_600				114112		//座りＰＣ29a		
#define SPR_s_601				114113		//座りＰＣ29b		
#define SPR_s_602				114114		//座りＰＣ29c		
#define SPR_s_603				114115		//座りＰＣ29d		
#define SPR_s_610				114116		//座りＰＣ30a		
#define SPR_s_611				114117		//座りＰＣ30b		
#define SPR_s_612				114118		//座りＰＣ30c		
#define SPR_s_613				114119		//座りＰＣ30d		
#define SPR_s_620				114120		//座りＰＣ31a		
#define SPR_s_621				114121		//座りＰＣ31b		
#define SPR_s_622				114122		//座りＰＣ31c		
#define SPR_s_623				114123		//座りＰＣ31d		
#define SPR_s_630				114124		//座りＰＣ32a		
#define SPR_s_631				114125		//座りＰＣ32b		
#define SPR_s_632				114126		//座りＰＣ32c		
#define SPR_s_633				114127		//座りＰＣ32d		
#define SPR_s_640				114128		//座りＰＣ33a		
#define SPR_s_641				114129		//座りＰＣ33b		
#define SPR_s_642				114130		//座りＰＣ33c		
#define SPR_s_643				114131		//座りＰＣ33d		
#define SPR_s_650				114132		//座りＰＣ34a		
#define SPR_s_651				114133		//座りＰＣ34b		
#define SPR_s_652				114134		//座りＰＣ34c		
#define SPR_s_653				114135		//座りＰＣ34d		
#define SPR_s_660				114136		//座りＰＣ35a		
#define SPR_s_661				114137		//座りＰＣ35b		
#define SPR_s_662				114138		//座りＰＣ35c		
#define SPR_s_663				114139		//座りＰＣ35d		
#define SPR_s_700				114140		//座りＰＣ36a		
#define SPR_s_701				114141		//座りＰＣ36b		
#define SPR_s_702				114142		//座りＰＣ36c		
#define SPR_s_703				114143		//座りＰＣ36d		
#define SPR_s_710				114144		//座りＰＣ37a		
#define SPR_s_711				114145		//座りＰＣ37b		
#define SPR_s_712				114146		//座りＰＣ37c		
#define SPR_s_713				114147		//座りＰＣ37d		
#define SPR_s_720				114148		//座りＰＣ38a		
#define SPR_s_721				114149		//座りＰＣ38b		
#define SPR_s_722				114150		//座りＰＣ38c		
#define SPR_s_723				114151		//座りＰＣ38d		
#define SPR_s_730				114152		//座りＰＣ39a		
#define SPR_s_731				114153		//座りＰＣ39b		
#define SPR_s_732				114154		//座りＰＣ39c		
#define SPR_s_733				114155		//座りＰＣ39d		
#define SPR_s_740				114156		//座りＰＣ40a		
#define SPR_s_741				114157		//座りＰＣ40b		
#define SPR_s_742				114158		//座りＰＣ40c		
#define SPR_s_743				114159		//座りＰＣ40d		
#define SPR_s_750				114160		//座りＰＣ41a		
#define SPR_s_751				114161		//座りＰＣ41b		
#define SPR_s_752				114162		//座りＰＣ41c		
#define SPR_s_753				114163		//座りＰＣ41d		
#define SPR_s_760				114164		//座りＰＣ42a		
#define SPR_s_761				114165		//座りＰＣ42b		
#define SPR_s_762				114166		//座りＰＣ42c		
#define SPR_s_763				114167		//座りＰＣ42d		

#define SPR_s_kage_pcf				114168		//カゲ１		
#define SPR_s_kage_pcm				114169		//カゲ２

								
/*114167～からは、カブリモノ用に使用します*/								

#define SPR_kabu01			114170		// 1 キノコ
#define SPR_kabu02			114171		// 2 花Ａ
#define SPR_kabu03			114172		// 3 花Ｂ
#define SPR_kabu04			114173		// 4 王冠
#define SPR_kabu05			114174		// 5 王女冠
#define SPR_kabu06			114175		// 6 ウサ耳
#define SPR_kabu07			114176		// 7 ネコ耳
#define SPR_kabu08			114177		// 8 天使の轮
#define SPR_kabu09			114178		// 9 ちょんまげヅラ
#define SPR_kabu10			114179		//10 ハゲヅラ
#define SPR_kabu11			114180		//11 金たらい
#define SPR_kabu12			114181		//12 サッカーボール
#define SPR_kabu13			114182		//13 巨大キノコ
#define SPR_kabu14			114183		//14 リボン
#define SPR_kabu15			114184		//15 帽子
#define SPR_kabu16			114185		//16 アヒル帽子
#define SPR_kabu17			114186		//17 ヒヨコ帽子
#define SPR_kabu18			114187		//18 カモノハシ帽子
#define SPR_kabu19			114188		//19 ウサギ帽子
#define SPR_kabu20			114189		//20 ノッカー帽子
#define SPR_kabu21			114190		//21 ネコ帽子
#define SPR_kabu22			114191		//22 ネコ帽子
#define SPR_kabu23			114192		//23 ネコ帽子
#define SPR_kabu24			114193		//24 ネコ帽子
#define SPR_kabu25			114194		//25 キャノン炮
#define SPR_kabu26			114195		//26 烟突
#define SPR_kabu27			114196		//27 星
#define SPR_kabu28			114197		//28 アンテナ
#define SPR_kabu29			114198		//29 初心者マーク
#define SPR_kabu30			114199		//30 初心者救济マーク
#define SPR_kabu31			114200		//31 等级キャップ
#define SPR_kabu32			114201		//32 かぼちゃ				// 2005/01/11追加
#define SPR_kabu33			114202		//33 コック帽				// 2005/01/11追加
#define SPR_kabu34			114203		//34 ナース帽				// 2005/01/11追加
#define SPR_kabu35			114204		//35 探侦帽					// 2005/01/11追加
#define SPR_kabu36			114205		//36 花轮					// 2005/02/22追加
#define SPR_kabu37			114206		//37 若叶					// 2005/01/11追加
#define SPR_kabu38			114207		//38 サングラス				// 2005/01/11追加
#define SPR_kabu39			114208		//39 パトライト				// 2005/01/11追加
#define SPR_kabu40			114209		//40 クリスマスツリー		// 2005/01/11追加
#define SPR_kabu41			114210		//41 バケツ					// 2005/01/11追加
#define SPR_kabu42			114211		//42 ハート					// 2005/01/13追加
#define SPR_kabu43			114212		//43 リーゼント				// 2005/01/24追加
#define SPR_kabu44			114213		//44 アフロ					// 2005/01/24追加
#define SPR_kabu45			114214		//45 シルクハット			// 2005/02/22追加
#define SPR_kabu46			114215		//46 伞						// 2005/02/22追加
#define SPR_kabu47			114216		//47 星ピン					// 2005/02/22追加
#define SPR_kabu48			114217		//48 ゴマたん				// 2005/02/22追加
#define SPR_kabu49			114218		//49 スイカ					// 2005/02/22追加
#define SPR_kabu50			114219		//50 ノッカー				// 2005/02/22追加
#define SPR_kabu51			114220		//51 ノッカー赤				// 2005/02/22追加
#define SPR_kabu52			114221		//52 ノッカーちょんまげ		// 2005/02/22追加
#define SPR_kabu53			114222		//53 ノッカーはげ			// 2005/02/22追加
#define SPR_kabu54			114223		//54 ノッカー折れ耳			// 2005/02/22追加
#define SPR_kabu55			114224		//55 アフロ色违い			// 2005/10/20追加

#define SPR_kabu_start		SPR_kabu01
#define SPR_kabu_end		SPR_kabu55


// グラフィックＩＤ

//======================================//
//				アイテム				//
//======================================//
#define GID_PetItemBase 246050

#define GID_PetItemGoldPanel 246051

#define GID_PetItemNoSellOn 246052
#define GID_PetItemNoSellOff 246053
#define GID_PetItemNoSellOver 246054

#endif
