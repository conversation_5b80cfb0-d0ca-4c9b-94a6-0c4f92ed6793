﻿/*************************************************************

 * $Id: base64.c,v 1.2 2004/02/04 02:17:32 hno Exp $
 * 重写新的Base64加密和解谜函数 by LitChi 08/31/2011 0:30
 * 2009/03/22 iawen修正了对中文支持的一些BUG

使用示例[VC]：
1、获取需要加密的字符串：
	char srcString[BASE64_RESULT_SZ];
	memset(srcString,0,BASE64_RESULT_SZN);
	GetDlgItemText(hDlg,IDC_SRC,srcString,BASE64_RESULT_SZ-1);

2、调用相应函数，并接收返回的值：
	char destString[BASE64_RESULT_SZ];
	memset(destString,0,BASE64_RESULT_SZ);
	Base64Init();
	Base64Encode(srcString,destString);
	SetDlgItemText(hDlg,IDC_DEST,destString);

**************************************************************/

#define __IAWEN_BASE64_H__

#ifdef __IAWEN_BASE64_H__

	#define BASE64_VALUE_SZ 256
	#define BASE64_RESULT_SZ 8192

	extern char base64_code[];
	void Base64Init();	//初始化设置
	void Base64Decode(const char *srcString,char *destString);
			//对字符串进行解密
			//	srcString：		指向需要加密的字符串
			//	destString：	保存加密后的字符串，用以返回
										
	void Base64Encode(const char *srcString,char *destString);
			//对字符串进行加密
			//	srcString：		指向加密后的字符串
			//	destString：	保存解密后的字符串，用以返回

#endif