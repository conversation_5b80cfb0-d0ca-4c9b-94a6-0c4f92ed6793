﻿//メニュー＞状态

#ifndef _MENUSTATUS_H_
#define _MENUSTATUS_H_

void InitMenuWindowMenuStatusInLogin(void);
BOOL MenuWindowMenuStatus( int mouse );
BOOL MenuWindowMenuDetail( int mouse );
BOOL MenuWindowMenuTitle( int mouse );
BOOL MenuWindowMenuStatusDraw( int mouse );
BOOL MenuWindowMenuDetailDraw( int mouse );
BOOL MenuWindowMenuTitleDraw( int mouse );
#ifdef PUK3_WINDOW_OPEN_POINT
	BOOL MenuWindowMenuStatusCloseStatus();
	BOOL MenuWindowMenuStatusCloseDetail();
	BOOL MenuWindowMenuStatusCloseTitle();
	BOOL MenuWindowMenuStatusCloseProfile();
#endif

BOOL MenuSwitchStatusClose( int no, unsigned int flag );
BOOL MenuSwitchStatus( int no, unsigned int flag );
BOOL MenuSwitchDetail( int no, unsigned int flag );

BOOL MenuSwitchTitleUserName( int no, unsigned int flag );
BOOL MenuSwitchSetTitleUserName( int no, unsigned int flag );
BOOL MenuSwitchSetTitleSelectList( int no, unsigned int flag );
BOOL MenuSwitchSetTitle( int no, unsigned int flag );
BOOL MenuSwitchTitleScrollWheel( int no, unsigned int flag );
BOOL MenuSwitchTitleScrollV( int no, unsigned int flag );

#ifdef PUK3_PROF
BOOL MenuWindowMenuProf( int mouse );
BOOL MenuWindowMenuProfDraw( int mouse );
BOOL MenuSwitchSetProf( int no, unsigned int flag );
BOOL MenuSwitchProfMenuUserName( int no, unsigned int flag );
#endif

//--------------------------------------------------------------------------------------------------------
//状态

GRAPHIC_SWITCH MenuWindowStatusGraph[]={
	{0,0,0,0,0,0xFFFFFFFF},	//颜画像

	{GID_StatusHealth0,0,0,0,0,0xFFFFFFFF},		//ヘルス
	{GID_StatusHeart,0,0,0,0,0xFFFFFFFF},		//魂

	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_StatusBtStatusOff,0,0,0,0,0xFFFFFFFF},	//状态ボタン
	{GID_StatusBtDetailOn,0,0,0,0,0xFFFFFFFF},	//ディティールボタン
	{GID_StatusBtTitleOn,0,0,0,0,0xFFFFFFFF},	//タイトルボタン

	{GID_StatusPosFrontOff,0,0,0,0,0xFFFFFFFF},	//フロントボタン
	{GID_StatusPosBackOff,0,0,0,0,0xFFFFFFFF},	//バックボタン
	{GID_StatusPosChar,0,0,0,0,0xFFFFFFFF},		//ポジション用キャラクタ

	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//土
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//

	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//水
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWaterCenter,0,0,0,0,0xFFFFFFFF},	//

	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//火
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusFireCenter,0,0,0,0,0xFFFFFFFF},	//

	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//风
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//
	{GID_StatusWindCenter,0,0,0,0,0xFFFFFFFF},	//

	{GID_StatusEarthBack,0,0,0,0,0xFFFFFFFF},	//土ベース
	{GID_StatusWaterBack,0,0,0,0,0xFFFFFFFF},	//水ベース
	{GID_StatusFireBack,0,0,0,0,0xFFFFFFFF},	//火ベース
	{GID_StatusWindBack,0,0,0,0,0xFFFFFFFF},	//风ベース

	{GID_StatusBase,0,0,0,0,0xFFFFFFFF},		//ベース画像

#ifdef PUK3_PROF
	{GID_ProfSetWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
#else
	{GID_StatusWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
#endif

	{GID_StatusBack,0,0,0,0,0x80808080},		//バック画像

#ifdef PUK3_PROF
	{GID_ProfButtonOff,0,0,0,0,0xFFFFFFFF},		//プロフィールボタン画像
#endif

};

BUTTON_SWITCH MenuWindowStatusButton[]={
	{0},					//クローズボタン
	{0},					//状态ボタン
	{0},					//ディティールボタン
	{0},					//タイトルボタン
};

TEXT_SWITCH MenuWindowStatusText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"名称"},				//名称                    //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"等级"},				//等级                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"x5"},					//スタンポイント               //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"职业"},				//职业                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"家族"},				//家族                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"家族タイトル"},		//家族タイトル               //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"特殊称号"},	//特殊称号                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"玩家称号"},	//玩家称号                    //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"种族"},				//种族                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"经验值"},				//经验值                   //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ＮＥＸＴ"},			//ＮＥＸＴ经验值               //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"体力"},				//体力                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"魔力"},				//魔力                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ＤＰ"},				//ＤＰ                     //MLHIDE
};


//スイッチリスト
// ステータその１（状态）
static SWITCH_DATA MenuStatusSwitch[] = {

//テキスト表示
{ SWITCH_TEXT	  ,  70,  16-6,   0,  0, TRUE, &MenuWindowStatusText[ 0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 290-18,  19- 8,   0,  0, TRUE, &MenuWindowStatusText[ 1], MenuSwitchNone },	//等级

{ SWITCH_TEXT	  ,  88-18, 101,   0,  0, TRUE, &MenuWindowStatusText[ 2], MenuSwitchNone },	//魂の数

{ SWITCH_TEXT	  , 183-18,  44-13,   0,  0, TRUE, &MenuWindowStatusText[ 3], MenuSwitchNone },	//职业
{ SWITCH_TEXT	  , 183-18,  62-13,   0,  0, TRUE, &MenuWindowStatusText[ 4], MenuSwitchNone },	//家族
{ SWITCH_TEXT	  , 183-18,  81-13,   0,  0, TRUE, &MenuWindowStatusText[ 5], MenuSwitchNone },	//家族タイトル
{ SWITCH_TEXT	  , 183-18,  99-13,   0,  0, TRUE, &MenuWindowStatusText[ 6], MenuSwitchNone },	//特殊称号
{ SWITCH_TEXT	  , 183-18, 117-13,   0,  0, TRUE, &MenuWindowStatusText[ 7], MenuSwitchNone },	//玩家称号

{ SWITCH_TEXT	  , 107-18, 137-13,   0,  0, TRUE, &MenuWindowStatusText[ 8], MenuSwitchNone },	//种族
{ SWITCH_TEXT	  , 107-18, 154-13,   0,  0, TRUE, &MenuWindowStatusText[ 9], MenuSwitchNone },	//经验值
{ SWITCH_TEXT	  , 107-18, 171-13,   0,  0, TRUE, &MenuWindowStatusText[10], MenuSwitchNone },	//ネクスト
{ SWITCH_TEXT	  , 107-18, 189-13,   0,  0, TRUE, &MenuWindowStatusText[11], MenuSwitchNone },	//体力
{ SWITCH_TEXT	  , 107-18, 207-13,   0,  0, TRUE, &MenuWindowStatusText[12], MenuSwitchNone },	//魔力
{ SWITCH_TEXT	  , 107-18, 224-13,   0,  0, TRUE, &MenuWindowStatusText[13], MenuSwitchNone },	//ＤＰ

//画像
{ SWITCH_GRAPHIC  ,  26-11,  42-13,   0,  0, TRUE, &MenuWindowStatusGraph[ 0], MenuSwitchNone },	//颜
{ SWITCH_GRAPHIC  ,  66-18, 116-13,   0,  0, TRUE, &MenuWindowStatusGraph[ 1], MenuSwitchNone },	//ヘルス
{ SWITCH_GRAPHIC  ,  78-18, 117-13,   0,  0, TRUE, &MenuWindowStatusGraph[ 2], MenuSwitchNone },	//ハート

{ SWITCH_GRAPHIC  , 333-37,   9- 1,  12, 12, TRUE, &MenuWindowStatusGraph[ 3], MenuSwitchCloseButton },	//クローズ
{ SWITCH_GRAPHIC  , 189-38, 250-20,   0,  0, TRUE, &MenuWindowStatusGraph[ 4], MenuSwitchNone },	//状态
{ SWITCH_GRAPHIC  , 242-38, 251-20,  38, 25, TRUE, &MenuWindowStatusGraph[ 5], MenuSwitchStatus },	//ディティール
{ SWITCH_GRAPHIC  , 295-37, 250-20,  38, 25, TRUE, &MenuWindowStatusGraph[ 6], MenuSwitchStatus },	//タイトル
#ifdef PUK3_PROF
{ SWITCH_GRAPHIC  , 131-37, 250-20,  38, 25, TRUE, &MenuWindowStatusGraph[57], MenuSwitchStatus },	//プロフィール
#endif

{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowStatusGraph[ 9], MenuSwitchNone },	//ポジション用キャラクタ
{ SWITCH_GRAPHIC  , 235-18, 213-13,  36, 25, TRUE, &MenuWindowStatusGraph[ 7], MenuSwitchStatus },	//フロント
{ SWITCH_GRAPHIC  , 271-18, 213-13,  36, 25, TRUE, &MenuWindowStatusGraph[ 8], MenuSwitchStatus },	//バック

{ SWITCH_GRAPHIC  , 247-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },//土
{ SWITCH_GRAPHIC  , 255-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 263-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 271-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 279-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 287-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 295-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 303-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 311-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 319-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[10], MenuSwitchNone },

{ SWITCH_GRAPHIC  , 247-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },//水
{ SWITCH_GRAPHIC  , 255-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 263-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 271-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 279-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 287-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 295-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 303-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 311-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 319-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[20], MenuSwitchNone },

{ SWITCH_GRAPHIC  , 247-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },//火
{ SWITCH_GRAPHIC  , 255-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 263-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 271-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 279-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 287-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 295-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 303-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 311-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 319-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[30], MenuSwitchNone },

{ SWITCH_GRAPHIC  , 247-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },//风
{ SWITCH_GRAPHIC  , 255-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 263-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 271-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 279-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 287-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 295-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 303-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 311-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 319-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[40], MenuSwitchNone },

{ SWITCH_GRAPHIC  , 213-18, 143-13,   0,  0, TRUE, &MenuWindowStatusGraph[50], MenuSwitchNone },//属性ベース
{ SWITCH_GRAPHIC  , 213-18, 160-13,   0,  0, TRUE, &MenuWindowStatusGraph[51], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 213-18, 176-13,   0,  0, TRUE, &MenuWindowStatusGraph[52], MenuSwitchNone },
{ SWITCH_GRAPHIC  , 213-18, 192-13,   0,  0, TRUE, &MenuWindowStatusGraph[53], MenuSwitchNone },

//背景画像
{ SWITCH_GRAPHIC  ,  26-10,  42-13,   0,  0, TRUE, &MenuWindowStatusGraph[54], MenuSwitchNone },//ベース
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowStatusGraph[55], MenuSwitchNone },//ウインドウ
{ SWITCH_GRAPHIC  ,  12,  27,   0,  0, TRUE, &MenuWindowStatusGraph[56], MenuSwitchNone },//バック

{ SWITCH_NONE  , 315, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{
	
	EnumTextMenuStatusName,
	EnumTextMenuStatusLevel,

	EnumTextMenuStatusStun,

	EnumTextMenuStatusJob,
	EnumTextMenuStatusGuild,
	EnumTextMenuStatusGuildTitle,
	EnumTextMenuStatusSpecialTitle,
	EnumTextMenuStatusUserTitle,

	EnumTextMenuStatusType,
	EnumTextMenuStatusExp,
	EnumTextMenuStatusNext,
	EnumTextMenuStatusLP,
	EnumTextMenuStatusFP,
	EnumTextMenuStatusDP,

	EnumGraphMenuStatusFace,
	EnumGraphMenuStatusHealth,
	EnumGraphMenuStatusSprit,

	EnumGraphMenuStatusClose,
	EnumGraphMenuStatusStatus,
	EnumGraphMenuStatusDetail,
	EnumGraphMenuStatusTitle,
#ifdef PUK3_PROF
	EnumGraphMenuStatusProfile,
#endif

	EnumNoMenuStatusPosChar,
	EnumNoMenuStatusPosFront,
	EnumNoMenuStatusPosBack,

	EnumGraphMenuStatusEarth0,
	EnumGraphMenuStatusEarth1,
	EnumGraphMenuStatusEarth2,
	EnumGraphMenuStatusEarth3,
	EnumGraphMenuStatusEarth4,
	EnumGraphMenuStatusEarth5,
	EnumGraphMenuStatusEarth6,
	EnumGraphMenuStatusEarth7,
	EnumGraphMenuStatusEarth8,
	EnumGraphMenuStatusEarth9,

	EnumGraphMenuStatusWater0,
	EnumGraphMenuStatusWater1,
	EnumGraphMenuStatusWater2,
	EnumGraphMenuStatusWater3,
	EnumGraphMenuStatusWater4,
	EnumGraphMenuStatusWater5,
	EnumGraphMenuStatusWater6,
	EnumGraphMenuStatusWater7,
	EnumGraphMenuStatusWater8,
	EnumGraphMenuStatusWater9,

	EnumGraphMenuStatusFire0,
	EnumGraphMenuStatusFire1,
	EnumGraphMenuStatusFire2,
	EnumGraphMenuStatusFire3,
	EnumGraphMenuStatusFire4,
	EnumGraphMenuStatusFire5,
	EnumGraphMenuStatusFire6,
	EnumGraphMenuStatusFire7,
	EnumGraphMenuStatusFire8,
	EnumGraphMenuStatusFire9,

	EnumGraphMenuStatusWind0,
	EnumGraphMenuStatusWind1,
	EnumGraphMenuStatusWind2,
	EnumGraphMenuStatusWind3,
	EnumGraphMenuStatusWind4,
	EnumGraphMenuStatusWind5,
	EnumGraphMenuStatusWind6,
	EnumGraphMenuStatusWind7,
	EnumGraphMenuStatusWind8,
	EnumGraphMenuStatusWind9,

	EnumGraphMenuStatusEarthBase,
	EnumGraphMenuStatusWaterBase,
	EnumGraphMenuStatusFireBase,
	EnumGraphMenuStatusWindBase,

	EnumGraphMenuStatusStatusBase,
	EnumGraphMenuStatusWindow,
	EnumGraphMenuStatusBack,

	EnumHitMenuStatusStatus1,

	EnumMenuStatusEND
};

const WINDOW_DATA WindowDataMenuStatus = {
 0,																		// メニューstatus
#ifdef PUK3_WINDOW_OPEN_POINT
     4, 15,  97, 315, 261, 0x80000080,  	EnumMenuStatusEND, MenuStatusSwitch , MenuWindowMenuStatus,MenuWindowMenuStatusDraw,MenuWindowMenuStatusCloseStatus 
#else
     4, 15,  97, 315, 261, 0x80000080,  	EnumMenuStatusEND, MenuStatusSwitch , MenuWindowMenuStatus,MenuWindowMenuStatusDraw,MenuWindowDel 
#endif
};

//--------------------------------------------------------------------------------------------------------
//ディティール

GRAPHIC_SWITCH MenuWindowDetailGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},		//クローズボタン
	{GID_StatusBtStatusOn,0,0,0,0,0xFFFFFFFF},		//状态ボタン
	{GID_StatusBtDetailOff,0,0,0,0,0xFFFFFFFF},		//ディティールボタン
	{GID_StatusBtTitleOn,0,0,0,0,0xFFFFFFFF},		//タイトルボタン

	{243003,0,0,0,0,0xFFFFFFFF},		//状态アップ

	{GID_DetailBase,0,0,0,0,0xFFFFFFFF},		//ベース画像

#ifdef PUK3_PROF
	{GID_ProfSetWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
#else
	{GID_StatusWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
#endif

	{GID_StatusBack,0,0,0,0,0x80FFFFFF},		//バック画像
#ifdef PUK3_PROF
	{GID_ProfButtonOff,0,0,0,0,0xFFFFFFFF},		//プロフィールボタン画像
#endif
};

BUTTON_SWITCH MenuWindowDetailButton[]={
	{0},										//クローズボタン
	{0},										//状态ボタン
	{0},										//ディティールボタン
	{0},										//タイトルボタン
	{0},										//玩家称号决定
};

TEXT_SWITCH MenuWindowDetailText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"????????"},			//名称               //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"等级"},			//等级                     //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"BONUS"},			//BONUS                //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"伏凋"},				//VIT                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"荐楚"},				//STR                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"脓业"},				//TGH                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"堀业"},				//QUI                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"征隈"},				//MGC                    //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"攻击"},				//ATK                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"防御"},				//DEF                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"樗楯"},				//AGL                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"精神"},				//MND                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"回复"},				//RCV                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"??荐"},				//CHM                   //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"抗毒"},				//POI                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"抗昏睡"},				//SLP                   //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"抗石化"},				//STN                   //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"抗酒醉"},				//ITX                   //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"抗混乱"},				//CNF                   //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"抗遗忘"},				//AMN                   //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"必杀"},				//CRI                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"反击"},				//CTR                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"凋崭"},				//HIT                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"描吟"},				//AVD                    //MLHIDE

	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"冢荐"},				//STM                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"痩派"},				//DEX                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"仑荐"},				//INT                    //MLHIDE
#ifdef VERSION_TW
	//台服客户端新增的魔攻和魔防
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"魔攻"},				//DEX                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"魔防"},				//INT                    //MLHIDE
#endif
};



static SWITCH_DATA MenuDetailSwitch[] = {
//type            , ofx, ofy, sx, sy, Enable,Switch             ,func

//テキスト表示
{ SWITCH_TEXT	  ,  70,  16-6,   0,  0, TRUE, &MenuWindowDetailText[ 0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 290-18,  19-8,   0,  0, TRUE, &MenuWindowDetailText[ 1], MenuSwitchNone },	//等级

{ SWITCH_TEXT	  , 146-16,  45-14,   0,  0, TRUE, &MenuWindowDetailText[ 2], MenuSwitchNone },	//BONUS
{ SWITCH_TEXT	  , 140-16,  62-14,   0,  0, TRUE, &MenuWindowDetailText[ 3], MenuSwitchNone },	//VIT
{ SWITCH_TEXT	  , 140-16,  78-14,   0,  0, TRUE, &MenuWindowDetailText[ 4], MenuSwitchNone },	//STR
{ SWITCH_TEXT	  , 140-16,  94-14,   0,  0, TRUE, &MenuWindowDetailText[ 5], MenuSwitchNone },	//TGH
{ SWITCH_TEXT	  , 140-16, 110-14,   0,  0, TRUE, &MenuWindowDetailText[ 6], MenuSwitchNone },	//QUI
{ SWITCH_TEXT	  , 140-16, 126-14,   0,  0, TRUE, &MenuWindowDetailText[ 7], MenuSwitchNone },	//MGC

{ SWITCH_TEXT	  ,  62-16, 173-14,   0,  0, TRUE, &MenuWindowDetailText[ 8], MenuSwitchNone },	//ATK
{ SWITCH_TEXT	  ,  62-16, 189-14,   0,  0, TRUE, &MenuWindowDetailText[ 9], MenuSwitchNone },	//DEF
{ SWITCH_TEXT	  ,  62-16, 205-14,   0,  0, TRUE, &MenuWindowDetailText[10], MenuSwitchNone },	//AGL
{ SWITCH_TEXT	  , 140-16, 173-14,   0,  0, TRUE, &MenuWindowDetailText[11], MenuSwitchNone },	//MND
{ SWITCH_TEXT	  , 140-16, 189-14,   0,  0, TRUE, &MenuWindowDetailText[12], MenuSwitchNone },	//RCV
{ SWITCH_TEXT	  , 140-16, 205-14,   0,  0, TRUE, &MenuWindowDetailText[13], MenuSwitchNone },	//CHM
#ifdef VERSION_TW
//台服客户端新增的魔攻和魔防
{ SWITCH_TEXT	  ,  62-16, 221-14,   0,  0, TRUE, &MenuWindowDetailText[27], MenuSwitchNone },	//ADM
{ SWITCH_TEXT	  , 140-16, 221-14,   0,  0, TRUE, &MenuWindowDetailText[28], MenuSwitchNone },	//RSS
//修正台服显示位置
{ SWITCH_TEXT	  , 235 - 23,  62 - 14,   0,  0, TRUE, &MenuWindowDetailText[14], MenuSwitchNone },	//POI
{ SWITCH_TEXT	  , 235 - 23,  78 - 14,   0,  0, TRUE, &MenuWindowDetailText[15], MenuSwitchNone },	//SLP
{ SWITCH_TEXT	  , 235 - 23,  94 - 14,   0,  0, TRUE, &MenuWindowDetailText[16], MenuSwitchNone },	//STN
{ SWITCH_TEXT	  , 307 - 23,  62 - 14,   0,  0, TRUE, &MenuWindowDetailText[17], MenuSwitchNone },	//ITX
{ SWITCH_TEXT	  , 307 - 23,  78 - 14,   0,  0, TRUE, &MenuWindowDetailText[18], MenuSwitchNone },	//CNF
{ SWITCH_TEXT	  , 307 - 23,  94 - 14,   0,  0, TRUE, &MenuWindowDetailText[19], MenuSwitchNone },	//AMN

{ SWITCH_TEXT	  , 235 - 23, 151 - 14,   0,  0, TRUE, &MenuWindowDetailText[20], MenuSwitchNone },	//CRI
{ SWITCH_TEXT	  , 235 - 23, 167 - 14,   0,  0, TRUE, &MenuWindowDetailText[21], MenuSwitchNone },	//CTR
{ SWITCH_TEXT	  , 307 - 23, 151 - 14,   0,  0, TRUE, &MenuWindowDetailText[22], MenuSwitchNone },	//HIT
{ SWITCH_TEXT	  , 307 - 23, 167 - 14,   0,  0, TRUE, &MenuWindowDetailText[23], MenuSwitchNone },	//AVD

{ SWITCH_TEXT	  , 235 - 23, 205 - 14,   0,  0, TRUE, &MenuWindowDetailText[24], MenuSwitchNone },	//STM
{ SWITCH_TEXT	  , 307 - 23, 205 - 14,   0,  0, TRUE, &MenuWindowDetailText[25], MenuSwitchNone },	//DEX
{ SWITCH_TEXT	  , 307 - 23, 221 - 14,   0,  0, TRUE, &MenuWindowDetailText[26], MenuSwitchNone },	//INT
#else

{ SWITCH_TEXT	  , 220-23,  62-14,   0,  0, TRUE, &MenuWindowDetailText[14], MenuSwitchNone },	//POI
{ SWITCH_TEXT	  , 220-23,  78-14,   0,  0, TRUE, &MenuWindowDetailText[15], MenuSwitchNone },	//SLP
{ SWITCH_TEXT	  , 220-23,  94-14,   0,  0, TRUE, &MenuWindowDetailText[16], MenuSwitchNone },	//STN
{ SWITCH_TEXT	  , 292-23,  62-14,   0,  0, TRUE, &MenuWindowDetailText[17], MenuSwitchNone },	//ITX
{ SWITCH_TEXT	  , 292-23,  78-14,   0,  0, TRUE, &MenuWindowDetailText[18], MenuSwitchNone },	//CNF
{ SWITCH_TEXT	  , 292-23,  94-14,   0,  0, TRUE, &MenuWindowDetailText[19], MenuSwitchNone },	//AMN

{ SWITCH_TEXT	  , 220-23, 151-14,   0,  0, TRUE, &MenuWindowDetailText[20], MenuSwitchNone },	//CRI
{ SWITCH_TEXT	  , 220-23, 167-14,   0,  0, TRUE, &MenuWindowDetailText[21], MenuSwitchNone },	//CTR
{ SWITCH_TEXT	  , 292-23, 151-14,   0,  0, TRUE, &MenuWindowDetailText[22], MenuSwitchNone },	//HIT
{ SWITCH_TEXT	  , 292-23, 167-14,   0,  0, TRUE, &MenuWindowDetailText[23], MenuSwitchNone },	//AVD

{ SWITCH_TEXT	  , 220-23, 205-14,   0,  0, TRUE, &MenuWindowDetailText[24], MenuSwitchNone },	//STM
{ SWITCH_TEXT	  , 292-23, 205-14,   0,  0, TRUE, &MenuWindowDetailText[25], MenuSwitchNone },	//DEX
{ SWITCH_TEXT	  , 292-23, 221-14,   0,  0, TRUE, &MenuWindowDetailText[26], MenuSwitchNone },	//INT
#endif

//画像
{ SWITCH_GRAPHIC  , 110-16,  62-14,  11, 11, TRUE, &MenuWindowDetailGraph[ 4], MenuSwitchDetail },	//Vit UP
{ SWITCH_GRAPHIC  , 110-16,  78-14,  11, 11, TRUE, &MenuWindowDetailGraph[ 4], MenuSwitchDetail },	//STR UP
{ SWITCH_GRAPHIC  , 110-16,  94-14,  11, 11, TRUE, &MenuWindowDetailGraph[ 4], MenuSwitchDetail },	//TGH UP
{ SWITCH_GRAPHIC  , 110-16, 110-14,  11, 11, TRUE, &MenuWindowDetailGraph[ 4], MenuSwitchDetail },	//QUI UP
{ SWITCH_GRAPHIC  , 110-16, 126-14,  11, 11, TRUE, &MenuWindowDetailGraph[ 4], MenuSwitchDetail },	//MGC UP

{ SWITCH_GRAPHIC  , 333-37,   9- 1,  12, 12, TRUE, &MenuWindowDetailGraph[ 0], MenuSwitchCloseButton },//クローズ
{ SWITCH_GRAPHIC  , 189-38, 250-20,  38, 25, TRUE, &MenuWindowDetailGraph[ 1], MenuSwitchDetail },//状态
{ SWITCH_GRAPHIC  , 242-38, 251-20,  38, 25, TRUE, &MenuWindowDetailGraph[ 2], MenuSwitchNone },//ディティール
{ SWITCH_GRAPHIC  , 295-37, 250-20,  38, 25, TRUE, &MenuWindowDetailGraph[ 3], MenuSwitchDetail },//タイトル
#ifdef PUK3_PROF
{ SWITCH_GRAPHIC  , 131-37, 250-20,  38, 25, TRUE, &MenuWindowDetailGraph[ 8], MenuSwitchDetail },//プロフィール
#endif

//背景画像
{ SWITCH_GRAPHIC  ,  26-11,  42-14,   0,  0, TRUE, &MenuWindowDetailGraph[ 5], MenuSwitchNone },//ベース
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowDetailGraph[ 6], MenuSwitchNone },//ウインドウ
{ SWITCH_GRAPHIC  ,  12,  27,   0,  0, TRUE, &MenuWindowDetailGraph[ 7], MenuSwitchNone },//バック

{ SWITCH_NONE  , 315, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ
};

enum{
	EnumTextMenuDetailName,
	EnumTextMenuDetailLevel,

	EnumTextMenuDetailBonus,
	EnumTextMenuDetailVIT,
	EnumTextMenuDetailSTR,
	EnumTextMenuDetailTGH,
	EnumTextMenuDetailQUI,
	EnumTextMenuDetailMGC,

	EnumTextMenuDetailATK,
	EnumTextMenuDetailDEF,
	EnumTextMenuDetailAGL,
	EnumTextMenuDetailMND,
	EnumTextMenuDetailRCV,
	EnumTextMenuDetailCHM,
#ifdef VERSION_TW
	//台服客户端新增的魔攻和魔防
	EnumTextMenuDetailADM,
	EnumTextMenuDetailRSS,
#endif

	EnumTextMenuDetailPOI,
	EnumTextMenuDetailSLP,
	EnumTextMenuDetailSTN,
	EnumTextMenuDetailITX,
	EnumTextMenuDetailCNF,
	EnumTextMenuDetailAMN,

	EnumTextMenuDetailCRI,
	EnumTextMenuDetailCTR,
	EnumTextMenuDetailHIT,
	EnumTextMenuDetailAVD,

	EnumTextMenuDetailSTM,
	EnumTextMenuDetailDEX,
	EnumTextMenuDetailINT,

	EnumGraphMenuDetailVITUp,
	EnumGraphMenuDetailSTRUp,
	EnumGraphMenuDetailTGHUp,
	EnumGraphMenuDetailQUIUp,
	EnumGraphMenuDetailMGCUp,

	EnumGraphMenuDetailClose,
	EnumGraphMenuDetailStatus,
	EnumGraphMenuDetailDetail,
	EnumGraphMenuDetailTitle,
#ifdef PUK3_PROF
	EnumGraphMenuDetailProfile,
#endif

	EnumGraphMenuDetailDetailBase,
	EnumGraphMenuDetailWindow,
	EnumGraphMenuDetailBack,

	EnumHitMenuStatusDetail1,

	EnumMenuDetailEND
};

const WINDOW_DATA WindowDataMenuDetail = {
 0,																		// メニューstatus
#ifdef PUK3_WINDOW_OPEN_POINT
     4,  15,  97, 315, 261, 0x80000080,  EnumMenuDetailEND, MenuDetailSwitch , MenuWindowMenuDetail,MenuWindowMenuDetailDraw,MenuWindowMenuStatusCloseDetail 
#else
     4,  15,  97, 315, 261, 0x80000080,  EnumMenuDetailEND, MenuDetailSwitch , MenuWindowMenuDetail,MenuWindowMenuDetailDraw,MenuWindowDel 
#endif
};

//--------------------------------------------------------------------------------------------------------
//タイトル

GRAPHIC_SWITCH MenuWindowTitleGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},		//クローズボタン
	{GID_StatusBtStatusOn,0,0,0,0,0xFFFFFFFF},		//状态ボタン
	{GID_StatusBtDetailOn,0,0,0,0,0xFFFFFFFF},		//ディティールボタン
	{GID_StatusBtTitleOff,0,0,0,0,0xFFFFFFFF},		//タイトルボタン
	{GID_TitleSetOn,0,0,0,0,0xFFFFFFFF},			//セットボタン

	{GID_TitleRemoveOn,0,0,0,0,0xFFFFFFFF},			//リムーブ
	{GID_TitleDeleteOn,0,0,0,0,0xFFFFFFFF},			//デリート
	{GID_TitlePageUpOn,0,0,0,0,0xFFFFFFFF},			//→
	{GID_TitlePageDownOn,0,0,0,0,0xFFFFFFFF},		//←

	{GID_TitlePanelOff,0,0,0,0,0xFFFFFFFF},			//リスト枠

	{GID_TitleBase,0,0,0,0,0xFFFFFFFF},			//ベース画像

#ifdef PUK3_PROF
	{GID_ProfSetWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
#else
	{GID_StatusWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
#endif

	{GID_StatusBack,0,0,0,0,0x80808080},		//バック画像

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},						//スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(下ボタン)
#ifdef PUK3_PROF
	{GID_ProfButtonOff,0,0,0,0,0xFFFFFFFF},		//プロフィールボタン画像
#endif
};

BUTTON_SWITCH MenuWindowTitleButton[]={
	{0},										//クローズボタン
	{0},										//状态ボタン
	{0},										//ディティールボタン
	{0},										//タイトルボタン
	{0},										//スクロールボタン
	{0},										//ウインドウ全体
};

TEXT_SWITCH MenuWindowTitleText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"名称"},				//名称                    //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"等级"},				//等级                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"タイトル"},			//タイトル                  //MLHIDE
};


static SWITCH_DATA MenuTitleSwitch[] = {
//type            , ofx, ofy, sx, sy, Enable,Switch             ,func
{ SWITCH_DIALOG, 123-18, 47-16, 12*12, 12, TRUE, NULL,	 MenuSwitchTitleUserName },				//ダイアログ表示

{ SWITCH_GRAPHIC,252-13,118-14,   0, 14, TRUE, &MenuWindowTitleGraph[13], MenuSwitchNone },				//スクロールバー(つまみ)
{ SWITCH_BUTTON, 252-13,118-14,  11, 72, TRUE, &MenuWindowTitleButton[0], MenuSwitchTitleScrollV },			//スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,252-13,107-14,  11, 11, TRUE, &MenuWindowTitleGraph[14], MenuSwitchSetTitle },		//スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,252-13,189-14,  11, 11, TRUE, &MenuWindowTitleGraph[15], MenuSwitchSetTitle },		//スクロールバー(下ボタン)
{ SWITCH_NONE,	   0,  0, 315, 261, TRUE, NULL, MenuSwitchTitleScrollWheel },						// マウスホイール判定

//テキスト表示
{ SWITCH_TEXT	  ,  70,  16-6,   0,  0, TRUE, &MenuWindowTitleText[ 0], MenuSwitchNone },	//名称
{ SWITCH_TEXT	  , 290-18,  19-8,   0,  0, TRUE, &MenuWindowTitleText[ 1], MenuSwitchNone },	//等级

{ SWITCH_TEXT	  , 123-18,  47-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//玩家称号

{ SWITCH_TEXT	  , 123-18,  80-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//特殊称号

{ SWITCH_TEXT	  ,  43-18, 110-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//特殊称号リスト
{ SWITCH_TEXT	  ,  43-18, 126-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//
{ SWITCH_TEXT	  ,  43-18, 142-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//
{ SWITCH_TEXT	  ,  43-18, 158-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//
{ SWITCH_TEXT	  ,  43-18, 174-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//
{ SWITCH_TEXT	  ,  43-18, 190-16,   0,  0, TRUE, &MenuWindowTitleText[ 2], MenuSwitchNone },	//

//画像
{ SWITCH_GRAPHIC  , 333-37,   9- 1,  12, 12, TRUE, &MenuWindowTitleGraph[ 0], MenuSwitchCloseButton },	//クローズ
{ SWITCH_GRAPHIC  , 189-38, 250-20,  38, 25, TRUE, &MenuWindowTitleGraph[ 1], MenuSwitchSetTitle },	//状态
{ SWITCH_GRAPHIC  , 242-38, 251-20,  38, 25, TRUE, &MenuWindowTitleGraph[ 2], MenuSwitchSetTitle },	//ディティール
{ SWITCH_GRAPHIC  , 295-37, 250-20,  38, 25, TRUE, &MenuWindowTitleGraph[ 3], MenuSwitchNone },	//タイトル
#ifdef PUK3_PROF
{ SWITCH_GRAPHIC  , 131-37, 250-20,  38, 25, TRUE, &MenuWindowTitleGraph[16], MenuSwitchSetTitle },	//プロフィール
#endif

{ SWITCH_GRAPHIC  , 278-18,  43-16,  49, 17, TRUE, &MenuWindowTitleGraph[ 4], MenuSwitchSetTitle },	//セットボタン

{ SWITCH_GRAPHIC  , 278-18,  77-16,  49, 17, TRUE, &MenuWindowTitleGraph[ 5], MenuSwitchSetTitle },	//リムーブ

{ SWITCH_GRAPHIC  , 278-18, 191-16,  49, 17, TRUE, &MenuWindowTitleGraph[ 6], MenuSwitchSetTitle },	//デリート
{ SWITCH_GRAPHIC  , 234-14, 213-10,  18, 18, TRUE, &MenuWindowTitleGraph[ 7], MenuSwitchSetTitle },	//→
{ SWITCH_GRAPHIC  , 213-14, 213-10,  18, 18, TRUE, &MenuWindowTitleGraph[ 8], MenuSwitchSetTitle },	//←

{ SWITCH_GRAPHIC  ,  41-18, 108-16, 216, 16, TRUE, &MenuWindowTitleGraph[ 9], MenuSwitchSetTitleSelectList },	//リスト枠０
{ SWITCH_GRAPHIC  ,  41-18, 124-16, 216, 16, TRUE, &MenuWindowTitleGraph[ 9], MenuSwitchSetTitleSelectList },	//リスト枠１
{ SWITCH_GRAPHIC  ,  41-18, 140-16, 216, 16, TRUE, &MenuWindowTitleGraph[ 9], MenuSwitchSetTitleSelectList },	//リスト枠２
{ SWITCH_GRAPHIC  ,  41-18, 156-16, 216, 16, TRUE, &MenuWindowTitleGraph[ 9], MenuSwitchSetTitleSelectList },	//リスト枠３
{ SWITCH_GRAPHIC  ,  41-18, 172-16, 216, 16, TRUE, &MenuWindowTitleGraph[ 9], MenuSwitchSetTitleSelectList },	//リスト枠４
{ SWITCH_GRAPHIC  ,  41-18, 188-16, 216, 16, TRUE, &MenuWindowTitleGraph[ 9], MenuSwitchSetTitleSelectList },	//リスト枠５


//背景画像
{ SWITCH_GRAPHIC  ,  26-11,  42-14,   0,  0, TRUE, &MenuWindowTitleGraph[10], MenuSwitchNone },	//ベース
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowTitleGraph[11], MenuSwitchNone },	//ウインドウ
{ SWITCH_GRAPHIC  ,  12,  27,   0,  0, TRUE, &MenuWindowTitleGraph[12], MenuSwitchNone },	//バック

//ボタン
{ SWITCH_BUTTON   ,   0,   0, 374,281, TRUE, &MenuWindowTitleButton[5], MenuSwitchSetTitle },

{ SWITCH_NONE  , 315, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{
	EnumDialogUserName,

	EnumGraphMenuTitleTumami,
	EnumBtMenuTitleDrag,
	EnumGraphMenuTitleScrollUp,
	EnumGraphMenuTitleScrollDown,
	EnumGraphMenuTitleScrollWheel,

	EnumTextMenuTitleName,
	EnumTextMenuTitleLevel,

	EnumTextMenuUserTitle,
	EnumTextMenuSpecialTitle,

	EnumTextMenuTitleList00,
	EnumTextMenuTitleList01,
	EnumTextMenuTitleList02,
	EnumTextMenuTitleList03,
	EnumTextMenuTitleList04,
	EnumTextMenuTitleList05,

	EnumGraphMenuTitleClose,
	EnumGraphMenuTitleStatus,
	EnumGraphMenuTitleDetail,
	EnumGraphMenuTitleTitle,
#ifdef PUK3_PROF
	EnumGraphMenuTitleProfile,
#endif

	EnumGraphMenuTitleSetButton,

	EnumGraphMenuTitleRemoveButton,
	EnumGraphMenuTitleDeleteButton,
	EnumGraphMenuTitlePageUpButton,
	EnumGraphMenuTitlePageDownButton,

	EnumGraphMenuTitleList00,
	EnumGraphMenuTitleList01,
	EnumGraphMenuTitleList02,
	EnumGraphMenuTitleList03,
	EnumGraphMenuTitleList04,
	EnumGraphMenuTitleList05,

	EnumGraphMenuTitleDetailBase,
	EnumGraphMenuTitleWindow,
	EnumGraphMenuTitleBack,

	EnumBtMenuTitleWindow,

	EnumHitMenuStatusSkill1,

	EnumMenuTitleEND
};

const WINDOW_DATA WindowDataMenuTitle = {
 0,																		// メニューstatus
#ifdef PUK3_WINDOW_OPEN_POINT
     4,  15,  97, 315, 261, 0x80000080,  EnumMenuTitleEND, MenuTitleSwitch , MenuWindowMenuTitle,MenuWindowMenuTitleDraw,MenuWindowMenuStatusCloseTitle 
#else
     4,  15,  97, 315, 261, 0x80000080,  EnumMenuTitleEND, MenuTitleSwitch , MenuWindowMenuTitle,MenuWindowMenuTitleDraw,MenuWindowDel 
#endif
};

#ifdef PUK3_PROF
//--------------------------------------------------------------------------------------------------------
//プロフィール

GRAPHIC_SWITCH MenuWindowProfGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},		//クローズボタン

	{GID_StatusBtStatusOn,0,0,0,0,0xFFFFFFFF},		//状态ボタン
	{GID_StatusBtDetailOn,0,0,0,0,0xFFFFFFFF},		//ディティールボタン
	{GID_StatusBtTitleOn,0,0,0,0,0xFFFFFFFF},		//タイトルボタン
	{GID_ProfButtonOff,0,0,0,0,0xFFFFFFFF},		//プロフィールボタン画像

	{GID_ProfBase,0,0,0,0,0xFFFFFFFF},			//ベース画像
	{GID_ProfSetWindow,0,0,0,0,0xFFFFFFFF},		//ウインドウ画像
	{GID_StatusBack,0,0,0,0,0x80808080},		//バック画像

	{GID_ProfButtonToListOn,0,0,0,0,0xFFFFFFFF},		//右矢印

	{GID_TitleSetOn,0,0,0,0,0xFFFFFFFF},			//セットボタン

	{GID_ProfCategoryDef,0,0,0,0,0xFFFFFFFF},			//分类画像デフォルト（未使用

	{GID_ProfOpenOnOff,0,0,0,0,0xFFFFFFFF},			//公开非公开グラフィック

};

BUTTON_SWITCH MenuWindowProfButton[]={
	{0},										//クローズボタン
	{0},										//状态ボタン
	{0},										//ディティールボタン
	{0},										//タイトルボタン
	{0},										//スクロールボタン
	{0},										//ウインドウ全体
};

TEXT_SWITCH MenuWindowProfText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"名称"},				//名称                    //MLHIDE
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"等级"},			//等级                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"たいとる"},			//名称                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"しょくぎょう"},		//职上                   //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ぎるど"},			//等级                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"―――"},			//家族                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"せる"},			//Ｓｅｌｌ                    //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"ばい"},			//Ｂｕｙ                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"あばうと"},			//Ａｂｏｕｔ                 //MLHIDE
};


static SWITCH_DATA MenuProfSwitch[] = {

//テキスト表示
{ SWITCH_TEXT	  ,  70,  10,   0,  0, TRUE, &MenuWindowProfText[ 0], MenuSwitchNone },		//名称
{ SWITCH_TEXT	  , 272,  11,   0,  0, TRUE, &MenuWindowProfText[ 1], MenuSwitchNone },	//等级

{ SWITCH_TEXT	  , 140,  39+20*0,   0,  0, TRUE, &MenuWindowProfText[ 2], MenuSwitchNone },	//タイトル
{ SWITCH_TEXT	  , 140,  39+20*1,   0,  0, TRUE, &MenuWindowProfText[ 3], MenuSwitchNone },	//职业
{ SWITCH_TEXT	  , 140,  39+20*2,   0,  0, TRUE, &MenuWindowProfText[ 4], MenuSwitchNone },	//家族

{ SWITCH_DIALOG, 90, 105, 12*14, 14, TRUE, NULL,	 MenuSwitchProfMenuUserName },				//ダイアログ
{ SWITCH_DIALOG, 90, 129, 12*14, 14, TRUE, NULL,	 MenuSwitchProfMenuUserName },				//
{ SWITCH_DIALOG, 90, 153, 12*14, 14, TRUE, NULL,	 MenuSwitchProfMenuUserName },				//
{ SWITCH_DIALOG, 90, 177, 12*14, 54, TRUE, NULL,	 MenuSwitchProfMenuUserName },				//

{ SWITCH_TEXT	  , 97,  108,   0,  0, TRUE, &MenuWindowProfText[ 6], MenuSwitchNone },	//Ｓｅｌｌ
{ SWITCH_TEXT	  , 97,  130,   0,  0, TRUE, &MenuWindowProfText[ 7], MenuSwitchNone },	//Ｂｕｙ
{ SWITCH_TEXT	  , 97,  152,   0,  0, TRUE, &MenuWindowProfText[ 8], MenuSwitchNone },	//Ａｂｏｕｔ
{ SWITCH_TEXT	  , 97,  170,   0,  0, TRUE, &MenuWindowProfText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ１行
{ SWITCH_TEXT	  , 97,  184,   0,  0, TRUE, &MenuWindowProfText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ２行
{ SWITCH_TEXT	  , 97,  198,   0,  0, TRUE, &MenuWindowProfText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ３行
{ SWITCH_TEXT	  , 97,  212,   0,  0, TRUE, &MenuWindowProfText[ 8], MenuSwitchNone },	//Ｐｒｏｆｉｌｅ４行

//画像
{ SWITCH_GRAPHIC  , 333-37,   9- 1,  12, 12, TRUE, &MenuWindowProfGraph[ 0], MenuSwitchCloseButton },	//クローズ

{ SWITCH_GRAPHIC  ,  26-11,  42-13,  64, 72, TRUE, &MenuWindowProfGraph[11], MenuSwitchSetProf }, //公开非公开の颜グラオーバー画像
{ SWITCH_GRAPHIC  ,  26-11,  42-13,   0,  0, TRUE, &MenuWindowProfGraph[ 0], MenuSwitchNone },	//颜

{ SWITCH_GRAPHIC  , 189-38, 250-20,  38, 25, TRUE, &MenuWindowProfGraph[ 1], MenuSwitchSetProf },	//状态
{ SWITCH_GRAPHIC  , 242-38, 251-20,  38, 25, TRUE, &MenuWindowProfGraph[ 2], MenuSwitchSetProf },	//ディティール
{ SWITCH_GRAPHIC  , 295-37, 250-20,  38, 25, TRUE, &MenuWindowProfGraph[ 3], MenuSwitchSetProf },	//タイトル
{ SWITCH_GRAPHIC  , 131-37, 250-20,  38, 25, TRUE, &MenuWindowProfGraph[ 4], MenuSwitchNone },	//プロフィール

{ SWITCH_GRAPHIC  , 266, 105,  20, 20, TRUE, &MenuWindowProfGraph[ 8], MenuSwitchSetProf },	//sellList
{ SWITCH_GRAPHIC  , 266, 128,  20, 20, TRUE, &MenuWindowProfGraph[ 8], MenuSwitchSetProf },	//buyList
{ SWITCH_GRAPHIC  , 266, 151,  20, 20, TRUE, &MenuWindowProfGraph[ 8], MenuSwitchSetProf },	//AboutList

{ SWITCH_GRAPHIC  ,  73, 102,  20, 20, TRUE, &MenuWindowProfGraph[10], MenuSwitchSetProf },	//sellIcon
{ SWITCH_GRAPHIC  ,  73, 124,  20, 20, TRUE, &MenuWindowProfGraph[10], MenuSwitchSetProf },	//buyIcon
{ SWITCH_GRAPHIC  ,  73, 146,  20, 20, TRUE, &MenuWindowProfGraph[10], MenuSwitchSetProf },	//AboutIcon

{ SWITCH_GRAPHIC  , 257, 32,  40, 30, TRUE, &MenuWindowProfGraph[ 9], MenuSwitchSetProf },	//SET

//背景画像
{ SWITCH_GRAPHIC  ,  26-11,  36,   0,  0, TRUE, &MenuWindowProfGraph[5], MenuSwitchNone },	//ベース
{ SWITCH_GRAPHIC  ,   0,   0,   0,  0, TRUE, &MenuWindowProfGraph[6], MenuSwitchNone },	//ウインドウ
{ SWITCH_GRAPHIC  ,  12,  27,   0,  0, TRUE, &MenuWindowProfGraph[7], MenuSwitchNone },	//バック

{ SWITCH_NONE  , 315, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumTextMenuProfName,
	EnumTextMenuProfLevel,

	EnumTextMenuProfMyTitle,
	EnumTextMenuProfMyJob,
	EnumTextMenuProfMyGuild,

	EnumDialogMenuProfSell,
	EnumDialogMenuProfBuy,
	EnumDialogMenuProfAbout,
	EnumDialogMenuProfProfile,

	EnumTextMenuProfMySell,
	EnumTextMenuProfMyBuy,
	EnumTextMenuProfMyAbout,
	EnumTextMenuProfMyProfile0,
	EnumTextMenuProfMyProfile1,
	EnumTextMenuProfMyProfile2,
	EnumTextMenuProfMyProfile3,

	EnumGraphMenuProfClose,

	EnumGraphMenuProfFaceOverOnOff,
	EnumGraphMenuProfFace,

	EnumGraphMenuProfStatus,
	EnumGraphMenuProfDetail,
	EnumGraphMenuProfTitle,
	EnumGraphMenuProfProfile,

	EnumGraphMenuProfSellList,
	EnumGraphMenuProfBuyList,
	EnumGraphMenuProfAboutList,

	EnumGraphMenuProfSellIcon,
	EnumGraphMenuProfBuyIcon,
	EnumGraphMenuProfAboutIcon,

	EnumGraphMenuProfSet,

	EnumGraphMenuProfBase,
	EnumGraphMenuProfWindow,
	EnumGraphMenuProfBack,

	EnumHitMenuProf1,

	EnumMenuProfEND
};

const WINDOW_DATA WindowDataMenuProf = {
 0,																		// メニューstatus
#ifdef PUK3_WINDOW_OPEN_POINT
     4,  15,  97, 315, 261, 0x80000080,  EnumMenuProfEND, MenuProfSwitch , MenuWindowMenuProf,MenuWindowMenuProfDraw,MenuWindowMenuStatusCloseProfile 
#else
     4,  15,  97, 315, 261, 0x80000080,  EnumMenuProfEND, MenuProfSwitch , MenuWindowMenuProf,MenuWindowMenuProfDraw,MenuWindowDel 
#endif
};

#endif



//--------------------------------------------------------------------------------------------------------
// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateStatus={
	2,
	0,0,330,25,
	315,0,20,92,
};

#endif
