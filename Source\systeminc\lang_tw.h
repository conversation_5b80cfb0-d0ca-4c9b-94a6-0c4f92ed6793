﻿#ifndef __LANG_TW_H__
#define __LANG_TW_H__

/* -------------------------------------------------------------------
* main.h
*/
#define DEF_APPNAME		"魔力寶貝"


/* -------------------------------------------------------------------
* font.cpp
*/
#define FONT_SONGTI		"細明體"


/* -------------------------------------------------------------------
* netmain.h
*/
#define NET_ERRMSG_SOCKLIBERROR			"網絡還沒有準備好。"
#define NET_ERRMSG_BADNAME          	"伺服器名錯誤。"
#define NET_ERRMSG_SOCKETERROR      	"TCP生成失敗。"
#define NET_ERRMSG_NOTGETADDR			"無法取得伺服器IP。(%s)"
#define NET_ERRMSG_NOTCONNECT_S			"無法連接伺服器。"
#define NET_ERRMSG_NOTCONNECT			"無法連接伺服器。 "
#define NET_ERRMSG_CONNECTTIMEOUT		"連接伺服器超時。" 
#define NET_ERRMSG_LOGINTIMEOUT			"登入處理超時。"
#define NET_ERRMSG_CHARLISTTIMEOUT		"取得角色列表超時。"
#define NET_ERRMSG_LOGOUTTIMEOUT		"登出超時。"
#define NET_ERRMSG_LOGINFAIL			"無法登入。"
#define NET_ERRMSG_CREATECHARTIMEOUT	"建立角色超時。"
#define NET_ERRMSG_DELETECHARTIMEOUT    "刪除角色超時。"

#define NET_ERRMSG_ERR_CDKEY    		"輸入有錯。"
#define NET_ERRMSG_ERR_EXPIRE    		"沒有足夠的點數了。"

//#define NET_ERRMSG_NEWEST_NON    		"無法連接伺服器．"
#define NET_ERRMSG_NEWEST_NON    		"檢查伺服器失敗."
#ifdef PUK3_LOGIN_VERCHECK
#define NET_ERRMSG_CLIENTVER_DIFF		"版本有誤，請再次啟動遊戲。"
#endif

#define NET_ERRMSG_SERVER_BUSY    		"這個伺服器相當繁忙。"


/* -------------------------------------------------------------------
* menuwinonelineinfo.h
*/
#define MWONELINE_COMMON_WINDOWCLOSE		"關閉這個窗口。"					// クローズ

//----------------------------------------
// 战闘
	// プレーヤーコマンド
#define MWONELINE_BATTLE_ATTACK				"普通攻擊。"						// Attack
#define MWONELINE_BATTLE_GUARD				"物理防禦。"						// Guard
#define MWONELINE_BATTLE_SKILL				"使用技能。"							// Skill
#define MWONELINE_BATTLE_ITEM				"變更物品使用和裝備。"			// Item
#define MWONELINE_BATTLE_MONSTER			"召喚、召還寵物。"			// Monster
#define MWONELINE_BATTLE_POSITION			"變更位置。"						// Position
#define MWONELINE_BATTLE_ESCAPE				"逃跑。"									// Escape
#define MWONELINE_BATTLE_REBIRTH_ON			"精靈變身。"							// Re-birth（OFF状态）
#define MWONELINE_BATTLE_REBIRTH_OFF		"解除精靈變身。"						// Re-birth（ON状态）
// スキルウィンドウ
	// ※フィールドのスキルウィンドウに准据してください。

// アイテムウィンドウ
	// ※フィールドのアイテムウィンドウに准据してください。

// モンスターリストウィンドウ
#define MWONELINE_BATTLEPETLIST_PANEL_ON	"召喚這個寵物。"						// パネル（召唤可能）
#define MWONELINE_BATTLEPETLIST_PANEL_OFF	"無法召喚這個寵物。"					// パネル（召唤不可能）
#define MWONELINE_BATTLEPETLIST_RETURN		"召還寵物。"						// Returanボタン

// モンスターコマンド
#define MWONELINE_BATTLEPET_SKILL_ON		"關閉技能列表窗口。"			// Skillボタン（ウィンドウが开いている）
#define MWONELINE_BATTLEPET_SKILL_OFF		"打開技能列表窗口。"			// Skillボタン（ウィンドウが闭じている）

// モンスタースキルウィンドウ
	// ※フィールドのスキルウィンドウに准据してください。

// 観战
#define MWONELINE_BATTLE_WATCH				"結束觀戰。"							// 观战结束ボタン

//----------------------------------------
// チャットウィンドウ
#define MWONELINE_CHAT_LINE				"使用滑鼠左鍵增加顯示行數，使用右鍵減少。"	// 行ボタン
#define MWONELINE_CHAT_AREA				"切換對話範圍。"						// 聊天范围
#define MWONELINE_CHAT_SIZE				"切換文字大小。"						// 文字サイズ
#define MWONELINE_CHAT_COLOR			"切換文字顏色。"						// 文字カラー
#define MWONELINE_CHAT_DISP				"使用滑鼠左鍵增加顯示文字數，使用右鍵減少。"	// 表示文字数

//----------------------------------------
// 左コックピット
	// ヘルスボタン
#define MWONELINE_LCOCKPIT_HEALTH_0		"現在健康狀態良好。"						// ヘルスボタン
#define MWONELINE_LCOCKPIT_HEALTH_1		"現在受了皮外傷。"					// ヘルスボタン
#define MWONELINE_LCOCKPIT_HEALTH_2		"現在受了輕傷。"						// ヘルスボタン
#define MWONELINE_LCOCKPIT_HEALTH_3		"現在受了重傷。"								// ヘルスボタン
#define MWONELINE_LCOCKPIT_HEALTH_4		"現在處於瀕死狀態。"								// ヘルスボタン

#define MWONELINE_LCOCKPIT_PET_HEALTH_0	"現在%s的健康狀態良好。"					// ヘルスボタン
#define MWONELINE_LCOCKPIT_PET_HEALTH_1	"現在%s受了皮外傷。"				// ヘルスボタン
#define MWONELINE_LCOCKPIT_PET_HEALTH_2	"現在%s受了輕傷。"					// ヘルスボタン
#define MWONELINE_LCOCKPIT_PET_HEALTH_3	"現在%s受了重傷。"							// ヘルスボタン
#define MWONELINE_LCOCKPIT_PET_HEALTH_4	"現在%s處於瀕死狀態。"							// ヘルスボタン

// メールボタン
#define MWONELINE_LCOCKPIT_MAIL_ON		"收到郵件。"							// メールボタン
#define MWONELINE_LCOCKPIT_MAIL_OFF		"現在沒收到郵件。"					// メールボタン

// 等级アップボタン
#define MWONELINE_LCOCKPIT_LVUP_ON		"現在點數還有%d。"				// 等级アップボタン(%d…ボーナスポイント值)
#define MWONELINE_LCOCKPIT_LVUP_OFF		"現在沒有點數了。"				// 等级アップボタン

#define MWONELINE_LCOCKPIT_PET_LVUP_ON	"現在%s的點數還有%d。"			// 等级アップボタン(%d…ボーナスポイント值)
#define MWONELINE_LCOCKPIT_PET_LVUP_OFF	"現在%s沒有點數了。"			// 等级アップボタン

// Ｄｕ
#define MWONELINE_LCOCKPIT_DU_ON		"現在同意競技。"			// Ｄｕ
#define MWONELINE_LCOCKPIT_DU_OFF		"現在不同意競技。"		// Ｄｕ
// Ｃｈ
#define MWONELINE_LCOCKPIT_CH_ON		"現在可以隊伍頻道對話。"	// Ｃｈ
#define MWONELINE_LCOCKPIT_CH_OFF		"現在可以正常說話。"		// Ｃｈ
// Ｇｐ
#define MWONELINE_LCOCKPIT_GP_ON		"現在同意加入隊伍。"		// Ｇｐ
#define MWONELINE_LCOCKPIT_GP_OFF		"現在不同意加入隊伍。"	// Ｇｐ
// Ａｄ
#define MWONELINE_LCOCKPIT_AD_ON		"現在同意交換名片。"			// Ａｄ
#define MWONELINE_LCOCKPIT_AD_OFF		"現在不同意交換名片。"		// Ａｄ
// Ｔｒ
#define MWONELINE_LCOCKPIT_TR_ON		"現在同意交易。"			// Ｔｒ
#define MWONELINE_LCOCKPIT_TR_OFF		"現在不同意交易。"		// Ｔｒ
// Ｇｕ
#define MWONELINE_LCOCKPIT_GU_ON		"現在同意邀請加入家族。"		// Ｇｕ
#define MWONELINE_LCOCKPIT_GU_OFF		"現在不同意邀請加入家族。"		// Ｇｕ

#define MWONELINE_LCOCKPIT_GU_NO		"無法使用家族功能"	// Ｇｕ

//----------------------------------------
// 右コックピット
#define MWONELINE_RCOCKPIT_DUEL			"申請競技。"						// デュエル
#define MWONELINE_RCOCKPIT_WATCH		"觀戰。"									// 観战
#define MWONELINE_RCOCKPIT_GROUP		"加入隊伍。"							// グループ
#define MWONELINE_RCOCKPIT_CARD			"交換名片。"							// 名刺交换
#define MWONELINE_RCOCKPIT_TRADE		"交易。"								// トレード
#define MWONELINE_RCOCKPIT_GUILD		"邀請加入家族。"							// 家族
#define MWONELINE_RCOCKPIT_ACTION		"打開動作列表窗口。"		// アクション
#define MWONELINE_RCOCKPIT_MAP			"打開地圖窗口。"					// マップ

#define MWONELINE_RCOCKPIT_GUILD_NO		"無法使用家族功能"		// 家族

//----------------------------------------
// アクションウィンドウ
#define MWONELINE_ACTION_SIT			"做坐下。"				// 座る
#define MWONELINE_ACTION_HAND			"做揮手。"			// 挥手
#define MWONELINE_ACTION_NOD			"做點頭動作　　　"			// 点头
#define MWONELINE_ACTION_HAPPY			"做高興動作　　　"				// 喜ぶ
#define MWONELINE_ACTION_ANGRY			"做憤怒動作　 　　"				// 怒る
#define MWONELINE_ACTION_SAD			"做悲傷動作　 　"			// 悲伤
#define MWONELINE_ACTION_THROW			"做投擲動作。"			// 投掷
#define MWONELINE_ACTION_CHOKI			"做剪刀動作。"			// 剪刀
#define MWONELINE_ACTION_PA				"做布動作。"				// 布

#define MWONELINE_ACTION_STAND			"做站立動作。"				// 立つ
#define MWONELINE_ACTION_WALK			"做走動作。"				// 步く
#define MWONELINE_ACTION_DEAD			"做倒下動作。"			// 倒下
#define MWONELINE_ACTION_ATTACK			"做攻擊動作。"				// 攻击
#define MWONELINE_ACTION_GUARD			"做防禦動作。"			// 防御
#define MWONELINE_ACTION_DAMAGE			"做受傷動作。"			// 受伤
#define MWONELINE_ACTION_MAGIC			"做魔法動作。"				// 魔法
#define MWONELINE_ACTION_MOVE			"做移動動作。"				// 移动
#define MWONELINE_ACTION_GU				"做石頭動作。"				// 石头

//----------------------------------------
// マップウィンドウ

//----------------------------------------
// 状态ウィンドウ
	// 共通
#define MWONELINE_STATUS_STATUS			"切換玩家狀態窗口。"	// 状态ボタン
#define MWONELINE_STATUS_DETAIL			"切換玩家詳細窗口。"	// ディティールボタン
#define MWONELINE_STATUS_TITLE			"切換玩家稱號窗口。"				// タイトルボタン
#ifdef PUK3_PROF
#define MWONELINE_STATUS_PROFILE		"切換玩家個人信息窗口。"			// プロフィールボタン
#endif
	// 状态
#define MWONELINE_STATUS_FRONT			"戰鬥時的位置變更在前排。"			// 前卫ボタン
#define MWONELINE_STATUS_BACK			"戰鬥時的位置變更在後排。"			// 后卫ボタン

// ディティール
	// ＋ボタン
#define MWONELINE_STATUS_PLUS_ON		"在這個屬性上面分配點數。"			// ＋ボタン
#define MWONELINE_STATUS_PLUS_OFF		"無法在分配點數。"		// ＋ボタン（ポイントを割り振れない时）

// タイトル
#define MWONELINE_STATUS_USERSET		"玩家稱號設定。"								// UserTitleセットボタン
#define MWONELINE_STATUS_REMOVE			"解除選擇的稱號。"					// SpecialTitle Removeボタン
#define MWONELINE_STATUS_DELETE			"清除選擇的稱號。"					// SpecialTitle Deleteボタン
#define MWONELINE_STATUS_SPECIALPANEL	"使用這個稱號。"							// SpecialTitleパネル

#ifdef PUK3_PROF
	//プロフィール
#define MWONELINE_CATEGORY_LIST			"打開分類窗口。"					// 分类リスト开く
#define MWONELINE_SET_PROFILE			"決定個人信息。"						// プロフィールの决定
#define MWONELINE_PROFILE_OPEN			"公開個人信息。"						// プロフィールのＯＮ
#define MWONELINE_PROFILE_CLOSE			"不公開個人信息。"					// プロフィールのＯＦＦ
#define MWONELINE_PROFILE_DIALOG		"請輸入指令。"						// コメント入力

#define MWONELINE_STATUS_PROFILE_NO		"無法使用個人信息窗口"		// プロフィールＮＯ
#endif


//----------------------------------------
// スキルウィンドウ
	// スキル
#define MWONELINE_SKILL_SKILLPANEL		"打開這個技能的能力窗口。"		// パネル

// スキルリスト
#define MWONELINE_SKILL_LISTBACK		"回到技能列表窗口。"				// バックボタン
#define MWONELINE_SKILL_REBIRTH_ON		"精靈變身。"									// リバースボタン
#define MWONELINE_SKILL_REBIRTH_OFF		"解除精靈變身。"							// リバースボタン
#define MWONELINE_SKILL_PASS			"這回合什麼都不做。"							// パスボタン
#define MWONELINE_SKILL_ON				"使用這個能力。"						// パネル（使用可能）
#define MWONELINE_SKILL_OFF				"無法使用這個能力。"		// パネル（使用不可能）
#define MWONELINE_SKILL_FP				"魔力不足。"								// パネル（FPが足りない）

// レシピ
#define MWONELINE_SKILL_RECIPEBACK		"回到能力窗口。"					// バックボタン
#define MWONELINE_SKILL_RECIPEPANEL		"製作這個配方的物品。"				// パネル

//----------------------------------------
// 作成ウィンドウ　＆　修理ウィンドウ　＆　鉴定ウィンドウ　＆　刻印ウィンドウ
#define MWONELINE_SKILLCREATE_MYITEM	"通過拖動選擇物品。"	// Myアイテム
#define MWONELINE_SKILLCREATE_SELECT	"在此放入材料。"		// 选择アイテム
#define MWONELINE_SKILLCREATE_TRY		"執行。"										// TRYボタン
#define MWONELINE_SKILLCREATE_RETRY		"繼續執行。"								// ReTRYボタン
#define MWONELINE_SKILLCREATE_END		"關閉這個窗口。"						// ENDボタン
#define MWONELINE_SKILLCREATE_FP		"魔力不足。"								// FPが足りない

//----------------------------------------
// ギャザーウィンドウ
#define MWONELINE_SKILLGATHER_ENDCLOSE	"關閉這個窗口。"						// ENDボタン
#define MWONELINE_SKILLGATHER_ENDSTOP	"中止。"									// ENDボタン
#define MWONELINE_SKILLGATHER_TRY		"再次執行。"										// TRYボタン
#define MWONELINE_SKILLGATHER_FP		"魔力不足。"								// FPが足りない

//----------------------------------------
// 对象选择ウィンドウその１
#define MWONELINE_TARGETSEL1_NAME		"選擇這個角色。"					// 对象プレーヤー
#define MWONELINE_TARGETSEL1_CANCEL		"關閉這個窗口。"						// キャンセル

//----------------------------------------
// 对象选择ウィンドウその２
#define MWONELINE_TARGETSEL2_NAME		"選擇這個角色。"					// 对象キャラクター
#define MWONELINE_TARGETSEL2_CANCEL		"關閉這個窗口。"						// キャンセル
#define MWONELINE_TARGETSEL2_BACK		"返回。"								// バック

//----------------------------------------
// アイテムウィンドウ
	// アイテム
#define MWONELINE_ITEM_MYITEM			"在地面上放置金幣。"				// PutMoneyボタン
#define MWONELINE_ITEM_EQUIP_OFF		"打開裝備窗口。"						// ＥＱＵＩＰボタン
#define MWONELINE_ITEM_ABLEITEM			"這個物品可以使用。"						// 使用可能アイテム
#define MWONELINE_ITEM_ABLEEQUIP		"這個物品可以裝備。"						// 装备可能アイテム
#define MWONELINE_ITEM_UNABLEITEM		"這個物品不能使用。"			// 使用不可アイテム
#define MWONELINE_ITEM_UNABLEEQUIP		"這個物品不能裝備。"			// 装备不可アイテム

// ＥＱＵＩＰ
#define MWONELINE_ITEM_EQUIP_ON			"關閉裝備窗口。"						// ＥＱＵＩＰボタン
#define MWONELINE_ITEM_ACCESS1			"裝飾品的裝備位。"							// Acces1
#define MWONELINE_ITEM_ACCESS2			"裝飾品的裝備位。"							// Acces2
#define MWONELINE_ITEM_HEAD				"頭部防具裝備位。"							// Head
#define MWONELINE_ITEM_BODY				"身體防具裝備位。"							// Body
#define MWONELINE_ITEM_HAND				"武器和盾的裝備位。"						// Hand
#define MWONELINE_ITEM_FOOT				"腳步裝備位。"							// Foot
#define MWONELINE_ITEM_CRYSTAL			"水晶裝備位。"						// Crystal

// マネーウィンドウ　＆　スタックウィンドウ
#define MWONELINE_CALCULATOR_NUM		"指定數字。"								// 0～9ボタン
#define MWONELINE_CALCULATOR_OK_ON		"以指定數字確定。"							// ＯＫボタン
#define MWONELINE_CALCULATOR_OK_OFF		"輸入數字。"							// ＯＫボタン（押せない）
#define MWONELINE_CALCULATOR_BS			"返回。"										// ＢＳボタン
#define MWONELINE_CALCULATOR_ALL		"指定最大數。"								// ＡＬＬボタン
#define MWONELINE_CALCULATOR_CLR		"將數字歸０。"								// ＣＬＲボタン

#define MWONELINE_CALCULATOR_Up			"增加數字。"						//　数字表示横の↑ボタン
#define MWONELINE_CALCULATOR_Down		"減少數字。"						//　数字表示横の↓ボタン

//----------------------------------------
// 使い魔ウィンドウ
#define MWONELINE_PET_PANEL				"打開寵物的狀態窗口。"		// パネル
#define MWONELINE_PET_BATTLE_OFF		"將寵物設為戰鬥狀態。"					// Battleボタン（押せる）
#define MWONELINE_PET_BATTLE			"這個寵物無法設為戰鬥狀態。"				// Battleボタン（押せない）
#define MWONELINE_PET_FIELD_OFF			"將寵物設為休息狀態。"					// Fieldボタン（押せる）
#define MWONELINE_PET_FIELD				"這個寵物無法設為休息狀態。"				// Fieldボタン（押せない）
#define MWONELINE_PET_WALK_OFF			"將寵物設為散步狀態。"					// Walkボタン（押せる）
#define MWONELINE_PET_WALK				"這個寵物無法設為散步狀態。"				// Walkボタン（押せない）
#define MWONELINE_PET_STANDBY_OFF		"將寵物設為待命狀態。"					// Stand byボタン（押せる）
#define MWONELINE_PET_STANDBY			"這個寵物無法設為待命狀態。"				// Stand byボタン（押せない）

//----------------------------------------
// 使い魔详细ウィンドウ
	// 共通
#define MWONELINE_PETSTATUS_SET			"無法變更寵物名。"					// Setボタン
#define MWONELINE_PETSTATUS_RELEASE		"扔到這個寵物。"							// Releaseボタン
#define MWONELINE_PETSTATUS_LEFT		"切換頁面。"							// ←ボタン
#define MWONELINE_PETSTATUS_RIGHT		"切換頁面。"							// →ボタン
#define MWONELINE_PETSTATUS_STATUS		"切換到寵物的狀態窗口。"	// 状态ボタン
#define MWONELINE_PETSTATUS_DETAIL		"切換到寵物的詳細窗口。"	// ディティールボタン
#define MWONELINE_PETSTATUS_SKILL		"切換到寵物的技能窗口。"		// スキルボタン

// 状态
// ディティール
	// ＋ボタン
#define MWONELINE_PETSTATUS_PLUS_ON		"將點數分配到這個屬性上。"		// ＋ボタン
#define MWONELINE_PETSTATUS_PLUS_OFF	"無法在分配點數。"		// ＋ボタン（ポイントを割り振れない时）
// スキル

//----------------------------------------
// 名刺ウィンドウ
	// 共通
#define MWONELINE_CARD_LIST				"切換到列表。"						// リスト切替ボタン
#define MWONELINE_CARD_DETAIL			"切換到詳細。"							// ディティール切替ボタン
#define MWONELINE_CARD_SORT				"名片排序方式變更。"					// ソート切り替えボタン
#define MWONELINE_CARD_ADDRESS			"切換到名片窗口。"					// アドレスボタン
#define MWONELINE_CARD_GUILD			"切換到家族列表。"				// 家族ボタン
#define MWONELINE_CARD_GROUP			"切換到群郵件。"						// 群邮件ボタン
#define MWONELINE_CARD_GUILDDETAIL		"切換到家族窗口。"					// 家族详细ボタン
#define MWONELINE_CARD_LEFT				"切換頁面。"							// ←ボタン
#define MWONELINE_CARD_RIGHT			"切換頁面。"							// →ボタン

// 名刺一覧详细??名刺一覧リスト??家族一覧详细??家族一覧リスト
#define MWONELINE_CARD_GROUPMAIL		"切換到群郵件。"						// 群邮件ボタン
#define MWONELINE_CARD_HISTORY			"查看這個好友的郵件歷史記錄。"			// ヒストリーボタン
#define MWONELINE_CARD_GUILDHISTORY		"查看這個家族成員的郵件歷史記錄。"		// ヒストリーボタン
#define MWONELINE_CARD_PREVHISTORY		"前一個郵件"										//前一个邮件
#define MWONELINE_CARD_NEXTHISTORY		"下一個郵件"										//下一个邮件
#define MWONELINE_CARD_SEND				"送信給好友。"				// Ｓｅｎｄボタン
#define MWONELINE_CARD_DELETE			"刪除名片。"							// 削除ボタン
#define MWONELINE_CARD_GUILD_SEND		"給這個家族成員發送郵件。"			// Ｓｅｎｄボタン
#define MWONELINE_CARD_TITLE			"變更這個家族成員的稱號。"		// タイトル变更ボタン
#define MWONELINE_CARD_GUILDDELETE		"將這個家族成員除名。"					// 家族成员削除ボタン

//名刺ウィンドウ
#define MWONELINE_GUILD_TITLE_OPEN	"打開家族稱號窗口。"	//家族称号ウインドウ开く

//タイトルウィンドウ内
#define MWONELINE_GUILD_TITLE_SELECT	"選擇這個稱號。"				//タイトル选择ボタン
#define MWONELINE_GUILD_TITLE_SET		"設定選擇的稱號。"			//タイトル决定ボタン
#define MWONELINE_GUILD_TITLE_CLOSE		"關閉這個窗口。"				//ウインドウ关闭ボタン


//----------------------------------------
// 家族详细ウィンドウ
#define MWONELINE_GUILDDETAIL_SETGUILD	"變更家族名稱。"								// ＳＥＴ家族名ボタン
#define MWONELINE_GUILDDETAIL_SETHOUSE	"變更家族房間名稱。"							// ＳＥＴ家族ハウス名ボタン
#define MWONELINE_GUILDDETAIL_JOIN_ON	"有添加這個稱號的權限。"				// 加入权限ボタン（チェック有り）
#define MWONELINE_GUILDDETAIL_JOIN_OFF	"沒有添加這個稱號的權限。"				// 加入权限ボタン（チェック无し）
#define MWONELINE_GUILDDETAIL_JOIN		"這個稱號的添加權限。"						// 加入权限ボタン（押せない）
#define MWONELINE_GUILDDETAIL_DEL_ON	"這個稱號有除名權限。"				// 除名权限ボタン（チェック有り）
#define MWONELINE_GUILDDETAIL_DEL_OFF	"這個稱號沒有除名權限。"				// 除名权限ボタン（チェック无し）
#define MWONELINE_GUILDDETAIL_DEL		"這個稱號的除名權限。"						// 除名权限ボタン（押せない）
#define MWONELINE_GUILDDETAIL_FOOD_ON	"這個稱號有給家族寵物餵食的權限。"	// エサ权限ボタン（チェック有り）
#define MWONELINE_GUILDDETAIL_FOOD_OFF	"這個稱號沒有給家族寵物餵食的權限"	// エサ权限ボタン（チェック无し）
#define MWONELINE_GUILDDETAIL_FOOD		"這個稱號的給家族寵物餵食權限。"	// エサ权限ボタン（押せない）
#define MWONELINE_GUILDDETAIL_ITEM_ON	"這個稱號有使用物品箱的權限。"	//アイテム权限ボタン（チェック有り）
#define MWONELINE_GUILDDETAIL_ITEM_OFF	"這個稱號沒有使用物品箱的權限。"	// アイテム权限ボタン（チェック无し）
#define MWONELINE_GUILDDETAIL_ITEM		"這個稱號的使用物品箱權限。"	// アイテム权限ボタン（押せない）
#define MWONELINE_GUILDDETAIL_BBS_ON	"這個稱號有刪除留言板的權限。"		// 揭示板权限ボタン（チェック有り）
#define MWONELINE_GUILDDETAIL_BBS_OFF	"這個稱號沒有刪除留言板的權限。"		// 揭示板权限ボタン（チェック无し）
#define MWONELINE_GUILDDETAIL_BBS		"這個稱號的刪除留言板權限。"				// 揭示板权限ボタン（押せない）
#define MWONELINE_GUILDDETAIL_TITRENAME	"變更選擇的稱號名稱。"						// タイトルリネームボタン
#define MWONELINE_GUILDDETAIL_TITDELETE	"刪除選擇的稱號名稱。"						// タイトル削除ボタン

//----------------------------------------
// メール
	// 共通
#define MWONELINE_MAIL_SEND				"送信給好友。"					// Ｓｅｎｄボタン

// 送信
#define MWONELINE_MAIL_PETMAIL			"用寵物郵件送信。"						// ペットメールボタン

// 履历
#define MWONELINE_MAIL_LEFT				"切換頁面。"								// ←ボタン
#define MWONELINE_MAIL_RIGHT			"切換頁面。"								// →ボタン

#define MWONELINE_MAIL_BACK				"用普通郵件送信。"								// Ｂａｃｋボタン
#define MWONELINE_MAIL_PETCHANGE		"讓不同的寵物送信。"						// ペット切り替えボタン
#define MWONELINE_MAIL_NONE				"從物品窗口選擇要拖動的物品。"	// カーソル未选择

// 群邮件
#define MWONELINE_MAIL_GROUPSEND		"送信給這個群。"				// Ｓｅｎｄボタン
#define MWONELINE_MAIL_GROUPCHANGE		"切換群郵件。"						//
#define MWONELINE_MAIL_GROUPSELECTON	"送信給這個好友。"						// チェックボタン
#define MWONELINE_MAIL_GROUPSELECTOFF	"設定成送信給這個好友。"					// チェックボタン

// 削除确认ウィンドウ
#define MWONELINE_MAIL_YES				"消除。"											// Yes
#define MWONELINE_MAIL_NO				"取消清除。"									// No

//----------------------------------------
// アルバムリストウィンドウ
#define MWONELINE_ALBAM_PANEL				"打開這個寵物的圖鑑窗口。"		// アルバム详细开く

//----------------------------------------
// アルバム详细ウィンドウ
#define MWONELINE_ALBAMDETAIL_FORWARD	"切換頁面。"									// ページ送り
#define MWONELINE_ALBAMDETAIL_F_FORWARD	"發送10頁。"								// ページ早送り
#define MWONELINE_ALBAMDETAIL_BACK		"切換頁面。"									// ページ戾し
#define MWONELINE_ALBAMDETAIL_F_BACK	"發送10頁。"								// ページ早戾し

//----------------------------------------
// システムウィンドウ
#define MWONELINE_SYSTEM_LOGOUT			"登出。"										// 登出ボタン
#define MWONELINE_SYSTEM_OPERATION		"滑鼠和鍵盤設定。"						// 操作设定ボタン
#define MWONELINE_SYSTEM_SOUND			"聲音設定。"										// 音设定ボタン
#define MWONELINE_SYSTEM_DRAWSET		"圖形設定。"										// 绘图设定ボタン
#define MWONELINE_SYSTEM_SHORTCUT		"快捷鍵總覽。"							// ショートカットボタン

// 登出ウィンドウ
#define MWONELINE_SYSTEM_LOGINGATE		"回到登入點。"								// 登入点へ
#define MWONELINE_SYSTEM_SERVERSELECT	"登出回到伺服器選擇畫面。"					// サーバー选择へ

// 操作设定ウィンドウ
#define MWONELINE_SYSTEM_ITEMMOVE		"打開堆棧窗口。"					// アイテムの移动
#define MWONELINE_SYSTEM_CURSOR			"光標的圖標變更。"								// マウスカーソル

// 音设定ウィンドウ
#define MWONELINE_SYSTEM_BGMDOWN		"BGM的音量減少。"										// ＢＧＭ音量（←）
#define MWONELINE_SYSTEM_BGMDOWN_MIN	"無法再降低音量了。"								// ＢＧＭ音量（←最小）
#define MWONELINE_SYSTEM_BGMUP			"BGM的音量增加。"										// ＢＧＭ音量（→）
#define MWONELINE_SYSTEM_BGMUP_MAX		"無法增加了。"								// ＢＧＭ音量（→最大）
#define MWONELINE_SYSTEM_SEDOWN			"SE的音量減少。"										// ＳＥ音量（←）
#define MWONELINE_SYSTEM_SEDOWN_MIN		"無法再降低音量了。"								// ＳＥ音量（←最小）
#define MWONELINE_SYSTEM_SEUP			"SE的音量增加。"										// ＳＥ音量（→）
#define MWONELINE_SYSTEM_SEUP_MAX		"無法增加了。"								// ＳＥ音量（→最大）
#define MWONELINE_SYSTEM_SETYPE			"變更音聲。"										// ＳＥ音源

// 绘图设定ウィンドウ
#define MWONELINE_SYSTEM_DRAWSETBUTTON	"繪圖方式變更。"									// 垂直同步（同期）
#ifdef PUK2_3DDEVICE_DISP
#define MWONELINE_SYSTEM_3DDEVICESHOW	"現在的繪圖方式。"										// 描画机能表示ボタン
#endif
#ifdef PUK2_CHANGE_3DDEIVCE
#define MWONELINE_SYSTEM_3DDEVICECHANGE	"變更繪圖功能。"									// 描画机能变更ボタン
#define MWONELINE_SYSTEM_3DDEVICENOCHG	"現在無法變更繪圖方式。"						// 描画机能变更ボタン切り替えられないとき
/*
		#define MWONELINE_SYSTEM_3DDEVICEPARTY	"現在在隊伍中，無法變更繪圖方式。"	// 描画机能变更ボタン切り替えられないとき
		#define MWONELINE_SYSTEM_3DDEVICEDUEL	"現在在競技中，無法變更繪圖方式。"			// 描画机能变更ボタン切り替えられないとき
		#define MWONELINE_SYSTEM_3DDEVICEWATCH	"現在在觀戰中，無法變更繪圖方式。"			// 描画机能变更ボタン切り替えられないとき
		#define MWONELINE_SYSTEM_3DDEVICEWATCH	"現在在觀戰中，無法變更繪圖方式。"			// 描画机能变更ボタン切り替えられないとき
*/
#endif

// ショートカットウィンドウ
#define MWONELINE_SHORTCUT_WINDOW		"顯示窗口相關的快捷鍵。"				// Windowボタン
#define MWONELINE_SHORTCUT_SWITCH		"顯示開關相關的快捷鍵。"				// Switchボタン
#define MWONELINE_SHORTCUT_ACTION		"顯示行動相關的快捷鍵。"				// Actionボタン
#define MWONELINE_SHORTCUT_CHAT			"進行對話登錄。"									// Chatボタン

//----------------------------------------
// スキルマスターNPC
	// 选择ウィンドウ
#define MWONELINE_SKILLMASTER_LEARN		"從這個技能訓練師學習技能。"				// スキルを学习
#define MWONELINE_SKILLMASTER_FORGET	"忘記已經學習的技能。"						// スキルを忘れる
#define MWONELINE_SKILLMASTER_SELCANCEL	"取消學習技能。"							// 取消

// スキル习得ウィンドウ
#define MWONELINE_SKILLMASTER_OK		"學習這個技能。"									// OKボタン
#define MWONELINE_SKILLMASTER_LRNCANCEL	"取消學習。"									// キャンセルボタン

// スキル排除ウィンドウ
#define MWONELINE_SKILLMASTER_PANEL		"忘記這個技能。"									// スキルパネル
#define MWONELINE_SKILLMASTER_FGTCANCEL	"取消忘記。"									// キャンセルボタン

// スキル排除确认ウィンドウ
#define MWONELINE_SKILLMASTER_YES		"忘記這個技能。"									// Yesボタン
#define MWONELINE_SKILLMASTER_NO		"取消忘記這個技能。"						// Noボタン

//----------------------------------------
// モンスタースキルショップNPC
	// 贩卖スキルウィンドウ
#define MWONELINE_PETSKILLMASTER_SKILL	"讓寵物學習這個技能。"						// 贩卖スキルパネル
#define MWONELINE_PETSKILLMASTER_PET	"讓這個寵物學習技能。"						// 使い魔パネル
#define MWONELINE_PETSKILLMASTER_SLOT	"用這個欄位學習技能。"						// スキルスロットパネル
#define MWONELINE_PETSKILLMASTER_CANCEL	"返回。"										// キャンセル
#define MWONELINE_PETSKILLMASTER_L_CANC	"取消讓寵物學習技能。"				// キャンセル

//----------------------------------------
// ショップNPC
	// 卖り买い选择ウィンドウ
#define MWONELINE_SHOP_BUYSELLTOPBUY	"購買物品。"							// 要买
#define MWONELINE_SHOP_BUYSELLTOPSELL	"賣出物品。"							// 要卖
#define MWONELINE_SHOP_BUYSELLTOPCANCEL	"取消買賣。"										// 取消

// 购入ウィンドウ
#define MWONELINE_SHOP_BUYITEMPANEL		"購買這個物品。"									// ショップアイテムパネル
#define MWONELINE_SHOP_BUYPLUS			"增加物品的購入數。"						// ＋ボタン
#define MWONELINE_SHOP_BUYPLUS_MAX		"已經無法增加了。"							// ＋ボタン（增やせない）
#define MWONELINE_SHOP_BUYMINUS			"減少物品的購入數。"						// －ボタン
#define MWONELINE_SHOP_BUYMINUS_MIN		"已經無法減少了。"							// －ボタン（减らせない）
#define MWONELINE_SHOP_BUYOK			"購入指定的物品。"						// OKボタン
#define MWONELINE_SHOP_BUYCANCEL		"取消購入。"											// キャンセルボタン

// 购入确认ウィンドウ
#define MWONELINE_SHOP_BUYYES			"購入。"												// Yesボタン
#define MWONELINE_SHOP_BUYNO			"取消購入。"										// Noボタン

// 卖却ウィンドウ
#define MWONELINE_SHOP_SELLMYITEM		"拖動選擇物品賣出物品。"		// Myアイテム
#define MWONELINE_SHOP_SELLSHOPPANEL	"將要賣出的物品放在這裡。"	// ショップパネル
#define MWONELINE_SHOP_SELLITEMPANEL	"拖動取消物品選擇。"		// ショップアイテムパネル
#define MWONELINE_SHOP_SELLPLUS			"增加物品賣出個數。"						// ＋ボタン
#define MWONELINE_SHOP_SELLPLUS_MAX		"已經無法增加了。"							// ＋ボタン（增やせない）
#define MWONELINE_SHOP_SELLMINUS		"減少物品賣出個數。"						// －ボタン
#define MWONELINE_SHOP_SELLMINUS_MIN	"已經無法減少了。"							// －ボタン（减らせない）
#define MWONELINE_SHOP_SELLOK			"賣出指定的物品。"						// OKボタン
#define MWONELINE_SHOP_SELLCANCEL		"取消賣出。"											// キャンセルボタン

// 卖却确认ウィンドウ
#define MWONELINE_SHOP_SELLYES			"賣。"												// Yesボタン
#define MWONELINE_SHOP_SELLNO			"取消賣出。"										// Noボタン

// 鉴定ウィンドウ
#define MWONELINE_SHOP_APPMYITEM		"拖動選擇要鑑定的物品。"		// Myアイテム
#define MWONELINE_SHOP_APPSHOPPANEL		"在這裡放入要鑑定的物品。"	// ショップパネル
#define MWONELINE_SHOP_APPITEMPANEL		"拖動取消物品選擇。"		// ショップアイテムパネル
#define MWONELINE_SHOP_APPPLUS			"增加物品數量。"								// ＋ボタン
#define MWONELINE_SHOP_APPPLUS_MAX		"已經無法增加了。"							// ＋ボタン（增やせない）
#define MWONELINE_SHOP_APPMINUS			"減少物品數量。"								// －ボタン
#define MWONELINE_SHOP_APPMINUS_MIN		"已經無法減少了。"							// －ボタン（减らせない）
#define MWONELINE_SHOP_APPOK			"鑑定制定的物品。"						// OKボタン
#define MWONELINE_SHOP_APPCANCEL		"取消鑑定。"											// キャンセルボタン

// 鉴定确认
#define MWONELINE_SHOP_APPYES			"鑑定。"												// Yesボタン
#define MWONELINE_SHOP_APPNO			"取消鑑定。"										// Noボタン

// 修理ウィンドウ
#define MWONELINE_SHOP_REPAIRMYITEM		"拖動要修理的物品。"		// Myアイテム
#define MWONELINE_SHOP_REPAIRSHOPPANEL	"在這裡放入要修理的物品。"	// ショップパネル
#define MWONELINE_SHOP_REPAIRITEMPANEL	"拖動取消物品選擇。"		// ショップアイテムパネル
#define MWONELINE_SHOP_REPAIRPLUS		"增加物品數量。"								// ＋ボタン
#define MWONELINE_SHOP_REPAIRPLUS_MAX	"已經無法增加了。"							// ＋ボタン（增やせない）
#define MWONELINE_SHOP_REPAIRMINUS		"減少物品數量。"								// －ボタン
#define MWONELINE_SHOP_REPAIRMINUS_MIN	"已經無法減少了。"							// －ボタン（减らせない）
#define MWONELINE_SHOP_REPAIROK			"修理指定的物品。"						// OKボタン
#define MWONELINE_SHOP_REPAIRCANCEL		"取消修理。"											// キャンセルボタン

// 修理确认
#define MWONELINE_SHOP_REPAIRYES		"修理。"												// Yesボタン
#define MWONELINE_SHOP_REPAIRNO			"取消修理。"										// Noボタン

// 交换NPCスタートウィンドウ
#define MWONELINE_SHOP_TRADETOPTRADE	"與這個商店交換物品。"							// 交换
#define MWONELINE_SHOP_TRADETOPCANCEL	"取消物品交換。"									// 取消

// 交换NPC
#define MWONELINE_SHOP_TRADEMYITEM		"拖動要交換的物品。"		// Myアイテム
#define MWONELINE_SHOP_TRADESHOPPANEL	"在這裡放入要交換的物品。"	// ショップパネル
#define MWONELINE_SHOP_TRADEITEMPANEL	"拖動取消物品選擇。"		// ショップアイテムパネル
#define MWONELINE_SHOP_TRADEPLUS		"增加物品數量。"								// ＋ボタン
#define MWONELINE_SHOP_TRADEPLUS_MAX	"已經無法增加了。"							// ＋ボタン（增やせない）
#define MWONELINE_SHOP_TRADEMINUS		"減少物品數量。"								// －ボタン
#define MWONELINE_SHOP_TRADEMINUS_MIN	"已經無法減少了。"							// －ボタン（减らせない）
#define MWONELINE_SHOP_TRADEOK			"交換指定的物品。"						// OKボタン
#define MWONELINE_SHOP_TRADECANCEL		"取消交換。"											// キャンセルボタン

// 交换确认
#define MWONELINE_SHOP_TRADEYES			"交換。"												// Yesボタン
#define MWONELINE_SHOP_TRADENO			"取消交換。"										// Noボタン

//----------------------------------------
// BBSウィンドウ
#define MWONELINE_BBS_DELETE			"刪除這個信息。"							// デリートボタン
#define MWONELINE_BBS_RIGHT				"回到上一頁。"								// ←ボタン
#define MWONELINE_BBS_LEFT				"翻到下一頁。"								// →ボタン
#define MWONELINE_BBS_OK				"寫入信息。"								// OKボタン
#define MWONELINE_BBS_CANCEL			"取消寫入。"									// キャンセルボタン

//----------------------------------------
// 银行NPC　＆　家族共用ITEMBOX
	// 共通
#define MWONELINE_BANK_ITEM				"放入取出物品。"							// Itemボタン
#define MWONELINE_BANK_MONSTER			"放入取出寵物。"							// モンスターボタン
#define MWONELINE_BANK_DEPOSIT			"放入取出金幣。"									// Moneyボタン（自分侧）
#define MWONELINE_BANK_WITHDRAW			"取出金幣。"									// Moneyボタン（相手侧）
#define MWONELINE_BANK_OK				"關閉這個窗口。"							// OKボタン

// 自分侧アイテム
#define MWONELINE_BANK_MYITEM_ON		"拖動選擇保存。"			// 预けられるアイテム
#define MWONELINE_BANK_MYITEM_OFF		"這個物品無法保存。"				// 预けられないアイテム

#define MWONELINE_BANK_MYMONSTER		"拖動保存寵物。"	// 自分侧モンスター

// マネー
#define MWONELINE_BANK_MYMONEYNUM		"指定金額。"									// 0～9ボタン
#define MWONELINE_BANK_MYMONEYOK_ON		"存入指定金額。"								// ＯＫボタン
#define MWONELINE_BANK_MYMONEYOK_OFF	"指定金額。"								// ＯＫボタン（押せない）
#define MWONELINE_BANK_MYMONEYBS		"返回。"											// ＢＳボタン
#define MWONELINE_BANK_MYMONEYALL		"指定全額。"										// ＡＬＬボタン
#define MWONELINE_BANK_MYMONEYCLR		"將指定金額歸0。"								// ＣＬＲボタン

#define MWONELINE_BANK_BANKITEM			"拖動取出。"		// 银行侧アイテム

#define MWONELINE_BANK_BANKMONSTER		"拖動取出寵物。"		// 银行侧モンスター

// 银行侧マネー
#define MWONELINE_BANK_BANKMONEYNUM		"指定金額。"									// 0～9ボタン
#define MWONELINE_BANK_BANKMONEYOK_ON	"取出指定金額。"								// ＯＫボタン
#define MWONELINE_BANK_BANKMONEYOK_OFF	"指定金額。"								// ＯＫボタン（押せない）
#define MWONELINE_BANK_BANKMONEYBS		"返回。"											// ＢＳボタン
#define MWONELINE_BANK_BANKMONEYALL		"指定全額。"										// ＡＬＬボタン
#define MWONELINE_BANK_BANKMONEYCLR		"將指定金額歸0。"								// ＣＬＲボタン

//----------------------------------------
// エサ箱
	// 共通
#define MWONELINE_FOODBOX_OK			"關閉這個窗口。"							// OKボタン

// 自分侧アイテム
#define MWONELINE_FOODBOX_MYITEM_ON		"拖動移動餵食箱。"			// 与えられるアイテム
#define MWONELINE_FOODBOX_MYITEM_OFF	"無法給與這個物品。"				// 与えられないアイテム

//----------------------------------------
// 家族モンスター状态
#define MWONELINE_GUILMONSTATUS_SET		"家族寵物無法更名。"					// SETボタン
#define MWONELINE_GUILMONSTATUS_GIVE	"往這個窗口拖放物品餵食。"	// エサをやるボタン
#define MWONELINE_GUILMONSTATUS_NONE	"從物品窗口拖動物品餵食。"	// カーソル未选择


//----------------------------------------
// 怪我治疗NPC
#define MWONELINE_DOCTOR_NAME_ON		"治療這個角色。"			// 各キャラ（怪我有り）
#define MWONELINE_DOCTOR_NAME_OFF		"這個角色沒受傷。"				// 各キャラ（怪我无し）
#define MWONELINE_DOCTOR_ALL_ON			"治療所有受傷的角色。"	// まとめて治疗（怪我人有り）
#define MWONELINE_DOCTOR_ALL_OFF		"沒有受傷的角色。"				// まとめて治疗（怪我人无し）
#define MWONELINE_DOCTOR_CANCEL			"取消治療。"									// キャンセル

//----------------------------------------
// トレードウィンドウ
#define MWONELINE_TRADE_MYITEM_ON		"拖放這個物品 進行交易。"	// トレード可能アイテム（MyItem侧）
#define MWONELINE_TRADE_MYITEM_OFF		"這個物品無法交易"						// トレード不可能アイテム（MyItem侧）
#define MWONELINE_TRADE_OPENITEM		"拖放這個物品取消交易。"	// アイテム（Open侧）
#define MWONELINE_TRADE_TRADEITEM		"交易這個物品給對方。"		// アイテム（Trade侧）
#define MWONELINE_TRADE_MYMONSTER		"拖放這個寵物進行交易。"	// モンスターパネル（MyItem侧）
#define MWONELINE_TRADE_OPENMONSTER		"拖放這個寵物取消交易。"	// モンスターパネル（Open侧）
#define MWONELINE_TRADE_TRADEMONSTER	"交易這個寵物給對方。"						// モンスターパネル（Trade侧）
// マネー表示（MyMoney）
	// ※银行系に准据

#define MWONELINE_TRADE_MYITEMBUTTON	"顯示我的物品。"				// アイテムボタン（MyMonster＆MyMonster时）
#define MWONELINE_TRADE_OPENITEMBUTTON	"顯示我要交易的物品。"	// アイテムボタン（Open侧）
#define MWONELINE_TRADE_TRADEITEMBUTTON	"顯示對方要交易的物品。"	// アイテムボタン（Trade侧）
#define MWONELINE_TRADE_MYMONSBUTTON	"顯示我的寵物。"				// モンスターボタン（MyItem＆MyMoney时）
#define MWONELINE_TRADE_OPENMONSBUTTON	"顯示我要交易的寵物。"	// モンスターボタン（Open＆Trade侧）
#define MWONELINE_TRADE_TRADEMONSBUTTON	"顯示對方要交易的寵物。"	// モンスターボタン（Trade侧）
#define MWONELINE_TRADE_MYGOLDBUTTON	"指定要交易的金額。"						// マネーボタン（MyItem＆MyMonster时）
#define MWONELINE_TRADE_OPENGOLDBUTTON	"顯示我要交易的金額。"		// マネーボタン（Open＆Trade侧）
#define MWONELINE_TRADE_TRADEGOLDBUTTON	"顯示對方要交易的金額。"		// マネーボタン（Trade侧）

#define MWONELINE_TRADE_ITEMOPEN		"向對方公開。"							// ItemOpen
#define MWONELINE_TRADE_CLOSE			"取消公開。"						// Close
#define MWONELINE_TRADE_TRADEOK			"確認交易。"								// TradeOK

//----------------------------------------
// 取引ＢＢＳ
#ifdef PUK3_PROF

#define MWONELINE_MINIMAIL_HISTORY			"看這個迷你郵件的歷史記錄。"				// ヒストリーボタン
#define MWONELINE_MINIMAIL_SEND				"發送迷你郵件。"						// Ｓｅｎｄボタン
#define MWONELINE_MINIMAIL_DELETE			"刪除這個迷你郵件的名片。"			// 削除ボタン
#define MWONELINE_MINIMAIL_MINIBOOK			"切換迷你郵件表。"				// 家族ボタン

#define MWONELINE_MINIMAIL_SELL				"設定賣出的分類"				//
#define MWONELINE_MINIMAIL_BUY				"設定買入的分類"					//
#define MWONELINE_MINIMAIL_ABOUT			"設定關於的分類"				//

//BBS1
#define MWONELINE_BBS1_CATEGOEY			"搜索這個分類。"						// 分类のパネルボタン

//BBS2
#define MWONELINE_BBS2_USER				"查看這個用戶的信息。"					// パネル
#define MWONELINE_BBS2_NEXT				"下一頁。"									// →
#define MWONELINE_BBS2_PREV				"前一頁。"									// ←
#define MWONELINE_BBS2_SEARCH			"關鍵字搜索。"							//サーチ
#define MWONELINE_BBS2_SEARCH_STR		"輸入搜索的關鍵字。"				//ダイアログ

//BBS3
#define MWONELINE_BBS3_SEND_MINIMAIL	"發送迷你郵件將使用%d金幣。"			//ミニメールセンド


#endif


/* -------------------------------------------------------------------
* menuServerrequest.cpp
*/
#define LANG_MENUSERVERREQUEST_CPP_BUY						"要買"
#define LANG_MENUSERVERREQUEST_CPP_SELL						"要賣"
#define LANG_MENUSERVERREQUEST_CPP_CANCEL					"取消"

#define LANG_MENUSERVERREQUEST_CPP_TRADETOPTRADE			"交換"
#define LANG_MENUSERVERREQUEST_CPP_TRADETOPCANCEL			"什麼都不做"

#define LANG_MENUSERVERREQUEST_CPP_LEARN					"學習技能"
#define LANG_MENUSERVERREQUEST_CPP_FORGET					"遺忘技能"
#define LANG_MENUSERVERREQUEST_CPP_SELCANCEL				"取消"

#define LANG_MENUSERVERREQUEST_CPP_GIVEITEM					"給予物品"
#define LANG_MENUSERVERREQUEST_CPP_RECVITEM					"獲得物品"


/* -------------------------------------------------------------------
* login.cpp
*/
#define LANG_LOGIN_CPP_SELECTSERVERNAME_1                   "卡蓮"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_2                   "金牛座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_3                   "雙子座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_4                   "香港"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_5                   "獅子座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_6                   "處女座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_7                   "天秤座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_8                   "歌姬"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_9                   "櫻之舞"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_10                  "摩羯座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_11                  "水瓶座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_12                  "雙魚座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_13                  "香港"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_14                  "測試"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_15                  "九龍"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_16                  "金牛座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_17                  "露比"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_18                  "香港"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_19                  "獅子座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_20                  "處女座"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_21                  "露比"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_22                  "心美"
#define LANG_LOGIN_CPP_SELECTSERVERNAME_23                  "露比"

#define LANG_LOGIN_CPP_YES									" 確 定 "
#define LANG_LOGIN_CPP_NO									" 取 消 "


/* -------------------------------------------------------------------
* menuSystem.h
*/
#define LANG_MENUSYSTEM_H_WINDOWPUK1_1                      "狀態窗口開啟/關閉　　　　ctrl + W"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_2                      "技能窗口開啟/關閉　　　　ctrl + E"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_3                      "物品窗口開啟/關閉　　　　ctrl + R"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_4                      "寵物窗口開啟/關閉　　　　ctrl + A"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_5                      "好友窗口開啟/關閉　　　　ctrl + S"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_6                      "圖鑑窗口開啟/關閉　　　　ctrl + D"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_7                      "系統窗口開啟/關閉　　　　ESC"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_8                      "動作窗口開啟/關閉　　　　ctrl + F"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_9                      "地圖窗口開啟/關閉　　　　ctrl + Q"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_10                     "快捷鍵確認開啟/關閉　　　ctrl + Y"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_11                     "窗口回到畫面內 　　　　　ctrl + F12"
#define LANG_MENUSYSTEM_H_WINDOWPUK1_12                     "關閉所有窗口　　　　　　 SHIFT + ESC"						

#define LANG_MENUSYSTEM_H_SWITCHPUK1_1						"競技同意/不同意　　　　　ctrl + P"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_2						"隊伍聊天開啟/關閉　　　　ctrl + ;"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_3						"組隊同意/不同意 　　　　 ctrl + ."
#define LANG_MENUSYSTEM_H_SWITCHPUK1_4						"名片交換同意/不同意　 　 ctrl + L"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_5						"交易同意/不同意　　　　  ctrl + "
#define LANG_MENUSYSTEM_H_SWITCHPUK1_6						"家族邀請同意/不同意　　　ctrl + N"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_7						"申請競技　　　　　　　　 ctrl + @"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_8						"觀戰　　　　　　　　　 　ctrl + :"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_9						"加入隊伍　　　　　　　　 ctrl + /"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_10						"交換名片　　　　　　　 　ctrl + ["
#define LANG_MENUSYSTEM_H_SWITCHPUK1_11						"申請交易　　　　　　　　 ctrl + ]"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_12						"邀請加入家族　　　　　　 ctrl + M"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_13						"文字大小變更　　　　　　 ctrl + O"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_14						"對話欄行數↑　　　　　　 ctrl + ↑"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_15						"對話欄行數行數↓　　　　 ctrl + ↓"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_16						"對話欄文字數←　　　　　 ctrl + ←"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_17						"對話欄文字數→　　　　　 ctrl + →"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_18						"對話範圍擴大　　　　 　　SHIFT + ←"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_19						"對話範圍縮小　　　　　 　SHIFT + →"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_20						"上翻對話記錄　　　　　 　SHIFT + ↑"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_21						"下翻對話記錄　　　　　　 SHIFT + ↓"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_22						"清除對話記錄　　　　　　 HOME"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_23						"對話窗口暫時隱藏　　　 　SHIFT + HOME"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_24						"對話顯示模式切換　　　　 ctrl + HOME"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_25						"抓圖　　　　　　　　　　 F12"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_26						"名字顯示切換　　　　　　 F11"
#define LANG_MENUSYSTEM_H_SWITCHPUK1_27						"狀態顯示開啟/關閉　　　　ctrl + F11"

#define LANG_MENUSYSTEM_H_ACTIONPUK1_1						"坐下　　　　　　　 　　　ctrl + 1"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_2						"揮手　　　　　　　　 　　ctrl + 2"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_3						"點頭　　　　　　　　　 　ctrl + 3"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_4						"高興　　　　　　　　　　 ctrl + 4"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_5						"憤怒　 　　　　　　　　　ctrl + 5"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_6						"悲傷　　 　　　　　　　　ctrl + 6"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_7						"投擲　　　 　　　　　　　ctrl + 7"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_8						"站立　　　　 　　　　　　ctrl + 8"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_9						"走路　　　　　 　　　　　ctrl + 9"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_10						"倒下　　　　　　 　　　　ctrl + 0"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_11						"移動　　　　　　　 　　　ctrl + T"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_12						"攻擊 　　　　　　　　　　ctrl + -"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_13						"防禦　 　　　　　　　　　ctrl + ^"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_14						"受傷　　 　　　　　　　　ctrl + G"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_15						"魔法　　　 　　　　　　　ctrl + B"
#define LANG_MENUSYSTEM_H_ACTIONPUK1_16						"隨機猜拳　　　 　　　　　ctrl + \\"

#define LANG_MENUSYSTEM_H_WINDOWPUK2_1						"狀態窗口開啟/關閉　　　　ctrl + Q"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_2						"技能窗口開啟/關閉　　　　ctrl + W"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_3						"物品窗口開啟/關閉　　　　ctrl + E"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_4						"寵物窗口開啟/關閉　　　　ctrl + R"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_5						"好友窗口開啟/關閉　　　　ctrl + T"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_6						"圖鑑窗口開啟/關閉　　　　ctrl + Y"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_7						"系統窗口開啟/關閉　　　　ESC"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_8						"動作窗口開啟/關閉　　　　ctrl + A"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_9						"地圖窗口開啟/關閉　　　　ctrl + S"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_10						"快捷鍵確認開啟/關閉　　　ctrl + D"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_11						"窗口回到畫面內 　　　　　ctrl + F12"
#define LANG_MENUSYSTEM_H_WINDOWPUK2_12						"關閉所有窗口　　　　　　 SHIFT + ESC"

#define LANG_MENUSYSTEM_H_SWITCHPUK2_1						"競技同意/不同意　　 　　 ctrl + U"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_2						"隊伍聊天開啟/關閉　　 　 ctrl + I"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_3						"組隊同意/不同意　　　　  ctrl + O"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_4						"名片交換同意/不同意　　  ctrl + P"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_5						"交易同意/不同意　　　　　ctrl + @"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_6						"家族邀請同意/不同意　　　ctrl + ["
#define LANG_MENUSYSTEM_H_SWITCHPUK2_7						"申請競技　　　　　 　　　ctrl + J"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_8						"觀戰　　　　　　　　 　　ctrl + K"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_9						"加入隊伍　　　　　　　 　ctrl + L"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_10						"交換名片　　　　　 　　　ctrl + ;"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_11						"申請交易　　　　　　　　 ctrl + :"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_12						"邀請加入家族　　　　　　 ctrl + ]"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_13						"文字大小變更　　　 　　　ctrl + Z"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_14						"對話欄行數↑　　　　　　 ctrl + ↑"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_15						"對話欄行數行數↓　　　　 ctrl + ↓"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_16						"對話欄文字數←　　　　　 ctrl + ←"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_17						"對話欄文字數→　　　　　 ctrl + →"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_18						"對話範圍擴大　　　　 　　SHIFT + ←"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_19						"對話範圍縮小　　　　　 　SHIFT + →"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_20						"上翻對話記錄　　　　　 　SHIFT + ↑"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_21						"下翻對話記錄　　　　　　 SHIFT + ↓"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_22						"清除對話記錄　　　　　　 HOME"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_23						"對話窗口暫時隱藏　　　 　SHIFT + HOME"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_24						"對話顯示模式切換　　　　 ctrl + HOME"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_25						"抓圖　　　　　　　　　　 F12"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_26						"名字顯示切換　　　　　　 F11"
#define LANG_MENUSYSTEM_H_SWITCHPUK2_27						"狀態顯示開啟/關閉　　　　ctrl + F11"

#define LANG_MENUSYSTEM_H_ACTIONPUK2_1						"坐下　　　　　　　 　　　ctrl + 1"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_2						"揮手　　　　　　　　 　　ctrl + 2"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_3						"點頭　　　　　　　　　 　ctrl + 3"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_4						"高興　　　　　　　　　　 ctrl + 4"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_5						"憤怒　 　　　　　　　　　ctrl + 5"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_6						"悲傷　　 　　　　　　　　ctrl + 6"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_7						"投擲　　　 　　　　　　　ctrl + 7"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_8						"站立　　　　 　　　　　　ctrl + 8"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_9						"走路　　　　　 　　　　　ctrl + 9"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_10						"倒下　　　　　　　　 　　ctrl + B"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_11						"移動　　　　　　　　　 　ctrl + N"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_12						"攻擊　　　　　　 　　　　ctrl + M"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_13						"防禦　　　　　　　 　　　ctrl + ,"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_14						"受傷　　　　　　　　 　　ctrl + ."
#define LANG_MENUSYSTEM_H_ACTIONPUK2_15						"魔法　　　　　　　　　 　ctrl + /"
#define LANG_MENUSYSTEM_H_ACTIONPUK2_16						"隨機猜拳　　　 　　　　　ctrl + \\"


/* -------------------------------------------------------------------
* menu.cpp
*/
#define LANG_MENUSYSTEM_H_TRIBE_1							"人形系"
#define LANG_MENUSYSTEM_H_TRIBE_2							"龍系"
#define LANG_MENUSYSTEM_H_TRIBE_3							"不死系"
#define LANG_MENUSYSTEM_H_TRIBE_4							"飛行系"
#define LANG_MENUSYSTEM_H_TRIBE_5							"昆蟲系"
#define LANG_MENUSYSTEM_H_TRIBE_6							"植物系"
#define LANG_MENUSYSTEM_H_TRIBE_7							"野獸系"
#define LANG_MENUSYSTEM_H_TRIBE_8							"特殊系"
#define LANG_MENUSYSTEM_H_TRIBE_9							"金屬系"
#define LANG_MENUSYSTEM_H_TRIBE_10							"邪魔系"
#define LANG_MENUSYSTEM_H_TRIBE_11							"神族"
#define LANG_MENUSYSTEM_H_TRIBE_12							"精靈族"
#define LANG_MENUSYSTEM_H_TRIBE_13							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_14							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_15							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_16							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_17							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_18							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_19							"未開放"
#define LANG_MENUSYSTEM_H_TRIBE_20							"未開放"



#endif//__LANG_TW_H__