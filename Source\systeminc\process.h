﻿/************************/
/*	process.h			*/
/************************/
#ifndef _PROCESS_H_
#define _PROCESS_H_

/* プロセスの种类 */
enum{
	PROC_INIT,
	PROC_ID_PASSWORD,				// ＩＤ、パスワード入力处理
	PROC_TITLE_MENU,				// タイトル画面メニュー表示
	PROC_CHAR_SELECT,				// キャラ选择
	PROC_CHAR_MAKE,					// キャラ作成
	PROC_CHAR_LOGIN_START,			// キャラログイン开始
	PROC_CHAR_LOGIN,				// キャラ登入中
	PROC_CHAR_LOGOUT,				// キャラ登出
	PROC_OPENNING,
	PROC_GAME,						// ゲームプロセス
	PROC_BATTLE,					// バトルプロセス
	PROC_DISCONNECT_SERVER,			// ネットワークが切断された时の处理
	PROC_KEY_CANCELLMODE,			//お试しから本作へVerUP
	PROC_UPGRADE,					//アップグレード
#ifdef PUK2_SERVERCHANGE
	PROC_CHAR_SERVER_CHANGE,		// サーバー移动
#endif

#ifdef _DEBUG

	PROC_OHTA_TEST,					// 太田テスト
	
	PROC_TAKE_TEST,					// 竹内テスト
	PROC_DWAF_TEST,					// 松村テスト
	PROC_SPR_VIEW,					// スプライト确认プロセス
	PROC_ANIM_VIEW,					// アニメーション确认プロセス
	PROC_SE_TEST,					// ＳＥ确认プロセス
	PROC_D3D_TEST,					// Ｄ３Ｄテストプロセス
	
#endif
	PROC_ENDING
#if defined(PUK2) && defined(_DEBUG)
	,
	PROC_G_ID_VIEW,					// スプライト确认プロセス
	#ifdef PUK3_RIDEBIN
		PROC_COORDINATE_MAKE,			// 座标データ作成プロセス
	#endif
#endif
	
};

#ifdef PUK2
// ゲームプロセス サブプロセス番号
enum{
	GAME_PROC_INIT,						// ０：初始化
	GAME_PROC_INIT_2,					// １：初期化２
	GAME_PROC_LOGIN_PRODUCE,			// ２：ログイン演出
	GAME_PROC_MAIN,						// ３：メイン处理
	GAME_PROC_BATTLE_IN_PRODUCE_INIT,	// ４：エンカウント演出初期化
	GAME_PROC_BATTLE_IN_PRODUCE,		// ５：エンカウント演出处理
	
	GAME_PROC_CMD_INPUT,				// ４：输入指令
	GAME_PROC_RECV_MOVIE_DATA,			// ５：等待动画受信
	GAME_PROC_MOVIE,					// ６：处理动画
	GAME_PROC_OUT_PRODUCE_INIT,			// ７：结束演出初始化
	GAME_PROC_OUT_PRODUCE,				// ８：演出结束
#ifdef PUK2
	GAME_PROC_INIT_2_2,					// 初期化２のメイン处理
#endif

	GAME_PROC_MAIN_INIT = 20,			// メイン处理初期化

	GAME_PROC_LOGIN_INIT = 100,			// ログイン初期化
	GAME_PROC_LOGIN_WAIT = 101,			// ログイン时データ待ち处理
	GAME_PROC_LOGIN_EFFECT_INIT = 102,	// ログイン演出初期化
	GAME_PROC_LOGIN_EFFECT = 103,		// ログイン演出中
#ifdef PUK2
	GAME_PROC_LOGIN_EFFECT_INIT2 = 104,	// ログイン演出初期化２
#endif

	GAME_PROC_WARP_START_EFFECT_INIT    = 200,	// ワープ开始演出初期化
	GAME_PROC_WARP_START_EFFECT         = 201,	// ワープ开始演出中
	GAME_PROC_WARP_END_EFFECT_WAIT      = 202,	// ワープ时データ待ち处理
	GAME_PROC_WARP_END_EFFECT_INIT      = 203,	// ワープ終了演出初期化
	GAME_PROC_WARP_END_EFFECT           = 204,	// ワープ終了演出中
#ifdef PUK2
	GAME_PROC_WARP_END_EFFECT_INIT2     = 205,	// ワープ終了演出初期化
#endif
#ifdef PUK3_VEHICLE
	GAME_PROC_VEHICLE_MOVING_INIT       = 250,	// 乘り物移动演出初期化
	GAME_PROC_VEHICLE_MOVING            = 251,	// 乘り物移动演出
	GAME_PROC_VEHICLE_END               = 252,	// 乘り物移动演出終了
#endif

	GAME_PROC_OFFLINE_INIT              = 1000,	// 离线模式初期化
	GAME_PROC_OFFLINE_LOGIN_EFFECT_INIT = 1001,	// 离线模式演出初期化
	GAME_PROC_OFFLINE_LOGIN_EFFECT      = 1002,	// 离线模式演出中
	GAME_PROC_OFFLINE_MAIN              = 1003,	// 离线模式メイン处理
};
#endif


/* プロセス番号 */
extern UINT ProcNo;
/* サブプロセス番号 */
extern UINT SubProcNo;
/* プロセス番号 */
extern int ProcNo2;
/* サブプロセス番号 */
extern int SubProcNo2;

extern int palNo;
extern int oldPalNo;

extern int recvBgmNo;
extern int recvBgmNoFlag;

extern int encountEndFlag;	// エンカウント終了フラグ（オートマップ表示时に使用）

/* プロセス处理 */
void Process( void );

/* プロセス变更处理 */
void ChangeProc( int procNo );
/* プロセス变更处理 */
void ChangeProc( int procNo, int subProcNo );
/* プロセス变更处理 */
void ChangeProc2( int procNo );
/* プロセス变更处理 */
void ChangeProc2( int procNo, int subProcNo );

/* ゲームプロセス处理 */
void GameProc( void );

// ワープ演出用
void warpEffectProc( void );
// マップ画面修复
void repairMap( void );

// 时间からパレット番号に变换する ******************************************
void TimeZonePaletteProc( void );

#ifdef _DEBUG		

/* 竹内さんテストプロセス */
void TakeTestProc( void );

/* 太田テストプロセス */
void OhtaTestProc( void );

#endif

#endif
