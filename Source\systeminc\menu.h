﻿//-------------------------------------------------------------------------//
// menu.cpp ヘッダファイル                                                 //
//-------------------------------------------------------------------------//
#ifndef _MENU_H_
#define _MENU_H_

#include"../systeminc/action.h"
#include"../systeminc/pc.h"
#include"../systeminc/chat.h"


//-------------------------------------------------------------------------//
// 定数定义                                                                //
//-------------------------------------------------------------------------//

// メニュー管理番号
enum
{
	MENU_NONE,						// メニューなし

	MENU_AUTOMAP,					// オートマップ表示メニュー

	MENU_STATUS,					// 状态メニュー

	MENU_SYSTEM,					// システムメニュー
	MENU_SYSTEM_LOGOUT,				// 登出の确认
	MENU_SYSTEM_CAHT_CFG,			// システムメニューの对话设定
	MENU_SYSTEM_CAHT_STR_SET,		// システムメニューのチャット文字登録
	MENU_SYSTEM_BGM_CFG,			// システムメニューの设定ＢＧＭ
	MENU_SYSTEM_SE_CFG,				// システムメニューの设定ＳＥ
	MENU_SYSTEM_MOUSE_CFG,			// システムメニューの设定鼠标
	MENU_SYSTEM_KEY_CFG,			// システムメニューの设定键盘
	MENU_SYSTEM_DRAW_CFG,			// システムメニューの绘图设定
#ifdef _SYSTEMMENU_BTN_CONFIG
	MENU_SYSTEM_BTN_CFG,			// システムメニューの设定键位
#endif /* _SYSTEMMENU_BTN_CONFIG */
	MENU_SYSTEM_LOGIN_GATE,			// 登入点の确认

	MENU_ITEM,						// アイテムメニュー
	MENU_ITEM_DROP_GOLD,			// お金を置くメニュー
	MENU_STACK_ITEM_WIN,			// スタックアイテムウィンドウ

	MENU_ACTION,					// アクションメニュー

	MENU_SKILL,						// スキルメニュー
	MENU_ABILITY,					// アビリティメニュー

	MENU_MONSTER_LIST,				// モンスターリストメニュー
	MENU_MONSTER_STATUS,			// モンスター状态メニュー
	MENU_MONSTER_NAME_CHANGE,		// 怪物名字チェンジメニュー

	MENU_MONSTER_ALBUM_LIST,		// モンスターアルバムリストメニュー
	MENU_MONSTER_ALBUM,				// モンスターアルバムメニュー

	MENU_RECIPE,					// レシピメニュー
	MENU_CREATE,					// クリエイトウィンドウ
	MENU_MATERIAL_SELECT,			// マテリアル选择ウィンドウ
	MENU_CATEGORY_A_RESULT_WIN,		// 分类Ａ结果ウィンドウ

#ifdef _APPEND_JEWEL
	MENU_APPEND_JEWEL,				// 宝石追加ウインドウ
	MENU_APPEND_JEWEL_MATERIAL,		// 宝石追加素材选择ウインドウ
#endif _APPEND_JEWEL

#ifdef _OPERATION_REMAKE_ITEM
	MENU_REMAKE_ITEM,				// アイテムリメイクウインドウ
	MENU_REMAKE_ITEM_MATERIAL,		// アイテムリメイク素材选择ウインドウ
#endif /* _OPERATION_REMAKE_ITEM */

	MENU_TARGET_ITEM_SEL,			// ターゲット选择
	MENU_INCUSE_WIN,				// 刻印ウィンドウ
	MENU_CATEGORY_B_RESULT_WIN,		// 分类Ｂ结果ウィンドウ

	MENU_CATEGORY_C_ENOUGH_FP_WIN,	// 分类Ｃ 魔力足りない警告ウィンドウ
	MENU_CATEGORY_C_TARGET_SEL1_WIN,// 分类Ｃ 对象选择１ウィンドウ
	MENU_CATEGORY_C_TARGET_SEL2_WIN,// 分类Ｃ 对象选择２ウィンドウ
	MENU_CATEGORY_C_RESULT_WIN,		// 分类Ｃ 结果ウィンドウ
#ifdef _FISHING_WINDOW
	MENU_FISHING_FEED_WIN,			// 釣り饵选择ウインドウ
#endif /* _FISHING_WINDOW */
	MENU_CATEGORY_D_GATHER_WIN,		// 分类Ｄ 收集ウィンドウ
	MENU_CATEGORY_D_RESULT_WIN,		// 分类Ｄ 结果ウィンドウ

	MENU_BATTLE_RESULT_WIN,			// 战闘结果ウィンドウ
	MENU_DUEL_RESULT_WIN,			// デュエル结果ウィンドウ

	MENU_ADDRESS_BOOK_WIN,			// メールアドレスブック
	MENU_ADDRESS_BOOK_SORT_WIN,		// アドレスブックソート设定ウィンドウ
	MENU_ADDRESS_BOOK_DEL_WIN,		// アドレス削除ウィンドウ
	MENU_MAIL_SELECT_WIN,			// メールセレクトウィンドウ
	MENU_MAIL_EDIT_WIN,				// メールエディットウィンドウ
	MENU_MAIL_HISTORY_WIN,			// メールヒストリーウィンドウ
	MENU_MAIL_PET_SELECT_WIN,		// メールペットセレクトウィンドウ
	MENU_MAIL_PET_EDIT_WIN,			// メールペットエディットウィンドウ
	MENU_SEND_ITEM_SELECT_WIN,		// センドアイテムセレクトウィンドウ

	MENU_TRADE_PLAYER_SELECT_WIN,	// トレードプレイヤー选择ウィンドウ
	MENU_TRADE_WIN,					// トレードウィンドウ

	MENU_SERVER_REQUEST_WINDOW_MESSAGE,		// サーバリクエストウィンドウ（信息表示、１行入力）
	MENU_SERVER_REQUEST_WINDOW_SELECT,		// サーバリクエストウィンドウ（项目选择）
	MENU_SKILL_SHOP_WINDOW_1,		// スキルショップウィンドウ１（トップウィンドウ）
	MENU_SKILL_SHOP_WINDOW_2,		// スキルショップウィンドウ２（ＰＣが买ウィンドウ）
	MENU_SKILL_SHOP_WINDOW_3,		// スキルショップウィンドウ３（はずすウィンドウ）

	MENU_MONSTER_SKILL_SHOP_WIN1,	// モンスタースキルショップ（贩卖ウィンドウ）
	MENU_MONSTER_SKILL_SHOP_WIN2,	// モンスタースキルショップ（モンスター选择ウィンドウ）
	MENU_MONSTER_SKILL_SHOP_WIN3,	// モンスタースキルショップ（记忆位置选择ウィンドウ）
	MENU_MONSTER_SKILL_SHOP_OK_WIN,	// モンスタースキルショップ确认ウィンドウ

	MENU_ITEM_SHOP_TOP_WIN,			// アイテムショップトップウィンドウ
	MENU_ITEM_SHOP_SELL_WIN,		// アイテムショップ贩卖ウィンドウ
	MENU_ITEM_SHOP_BUY_WIN,			// アイテムショップ买取ウィンドウ
	MENU_ITEM_SHOP_SELL_OK_WIN,		// アイテムショップ贩卖确认ウィンドウ
	MENU_ITEM_SHOP_BUY_OK_WIN,		// アイテムショップ买取确认ウィンドウ

	MENU_DOCTOR_NPC_WIN,			// 医者ＮＰＣウィンドウ
	MENU_APPRAISAL_NPC_WIN,			// 鉴定ＮＰＣウィンドウ
	MENU_APPRAISAL_OK_WIN,			// 鉴定确认ウィンドウ
	MENU_REPAIR_NPC_WIN,			// 修理ＮＰＣウィンドウ
	MENU_REPAIR_OK_WIN,				// 修理确认ウィンドウ
	MENU_BANK_NPC_WIN,				// 银行ＮＰＣウィンドウ

	MENU_MESSAGE_BOARD_WIN,			// 伝言版ウィンドウ

	MENU_TRADE_NPC_TOP_WIN,			// 交换ＮＰＣトップウィンドウ
	MENU_TRADE_NPC_MAIN_WIN,		// 交换ＮＰＣメインウィンドウ
	MENU_TRADE_NPC_MAIN_OK_WIN,		// 交换ＮＰＣ确认ウィンドウ

	MENU_USE_ITEM_TARGET_SEL_WIN,	// アイテム使用时のターゲット选择ウィンドウ
	MENU_USE_ITEM_TARGET_SEL_WIN2,	// アイテム使用时のターゲット选择ウィンドウ２

	MENU_ITEM_HANKO_TARGET_WIN,		// ハンコ使用时の对象アイテム选择ウィンドウ

	MENU_ITEM_SHOVEL_TARGET_WIN,	// スコップ使用时の选择ウィンドウ
	MENU_CATEGORY_B_ENOUGH_MOJI_WIN,	// 分类B "\"や"|"ダメ警告ウィンドウ
	MENU_CATEGORY_B_ENOUGH_SPACE_WIN,	// 分类B "\"や"|"ダメ警告ウィンドウ

	MENU_ITEMCOUNT_NPC_TOP_WIN,		// アイテムカウントNPCトップウインドウ
	MENU_ITEMCOUNT_NPC_MAIN_WIN,	// アイテムカウントNPCメインウインドウ
	MENU_ITEMCOUNT_NPC_OK_WIN,		// アイテムカウントNPC确认ウインドウ

#ifdef PUK2		//####toda
	//なんかあるといけないので一番下に。
	G_MENU_SEND_ITEM_SELECT_WIN,
	G_MENU_MAIL_PET_EDIT_WIN,
	G_MENU_MAIL_PET_SELECT_WIN,
	G_MENU_MAIL_HISTORY_WIN,
	G_MENU_MAIL_EDIT_WIN,
	G_MENU_MAIL_SELECT_WIN,
	G_MENU_ADDRESS_BOOK_DEL_WIN,
	G_MENU_ADDRESS_BOOK_SORT_WIN,
	G_MENU_ADDRESS_BOOK_WIN,
	G_MENU_DETAIL_BOOK_WIN,
	G_MENU_TITLE_BOOK_WIN,
#endif

	MENU_ALBUM_WINDOW,				// アルバムウインドウ

	MENU_ALL						// 全メニュー

};

// スキルの种类
enum
{
	SKILL_KIND_NONE,				// なし
	SKILL_KIND_BATTLE,				// 战闘スキル
	SKILL_KIND_CRAFT				// 生产スキル
};


#define WINDOW_CREATE_FRAME 10			// ウィンドウが表示されるまでのエフェクト时间

// メニューの状态
enum
{
	CLOSE_MENU,
	OPEN_MENU
};

// メニューの表示位置
enum
{
	MENU_AREA_LEFT,					// 左画面に表示
	MENU_AREA_RIGHT,				// 右画面に表示
	MENU_AREA_ALL					// 画面全体に表示
};

//
enum
{
	MENU_COMMON_FLAG_CLOSE_BUTTON = 0x0001		// クローズボタンを有效にする
};

// 通常凸で押すと凹になるボタンでの
// マウス情报
enum
{
	BTN_LEFT_CLICK		= 0x001,
	BTN_LEFT_ON			= 0x002,
	BTN_RIGHT_CLICK		= 0x004,
	BTN_RIGHT_ON		= 0x008,
	BTN_LEFT_CLICK_REP	= 0x010,
	BTN_RIGHT_CLICK_REP	= 0x020,
	BTN_FOCUS_ON		= 0x040,
	BTN_LEFT_DBL_CLICK	= 0x080,
	BTN_RIGHT_DBL_CLICK	= 0x100,
};


enum
{
	WINDOW_MESSAGETYPE_MESSAGE,					// 信息のみ
	WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT,		// 信息と一行入力
	WINDOW_MESSAGETYPE_SELECT,					// 选择ウィンドウ
	WINDOW_MESSAGETYPE_PETSELECT,				// ペット选择ウィンドウ
	WINDOW_MESSAGETYPE_PARTYSELECT,				// 仲间选择ウィンドウ
	WINDOW_MESSAGETYPE_ITEMSHOPMENU,			// アイテム屋のメニューウインドウ
	WINDOW_MESSAGETYPE_ITEMSHOPBUY,			    // アイテム屋から买ウインドウ
	WINDOW_MESSAGETYPE_ITEMSHOPSELL,			// アイテム屋に卖ウインドウ
	WINDOW_MESSAGETYPE_LIMITITEMSHOPMAIN,		// 买い取り专门アイテム屋のメインウインドウ
	WINDOW_MESSAGETYPE_PETSKILLSHOP,			// ペットの技屋さんウインドウ
	WINDOW_MESSAGETYPE_WIDEMESSAGE,				// 信息のみ（大きい方）
	WINDOW_MESSAGETYPE_WIDEMESSAGEANDLINEINPUT,	// 信息と一行入力（大きい方）
	WINDOW_MESSAGETYPE_POOLITEMSHOPMENU,		// アイテム预り屋のメニューウインドウ
	WINDOW_MESSAGETYPE_POOLITEMSHOPMAIN,		// アイテム预り屋のメインウインドウ
	WINDOW_MESSAGETYPE_PLAYERANDPETSELECT,		// 自分とペット选择ウインドウ
	WINDOW_MESSAGETYPE_PETANDPARTYSELECT,		// ペット，仲间选择ウィンドウ
	WINDOW_MESSAGETYPE_SKILLMASTER_SHOP,		// スキルマスター店ウィンドウ
	WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_BUY,	// スキルマスター店买ウィンドウ
	WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_REMOVE,	// スキルマスター店外すウィンドウ
	WINDOW_MESSAGETYPE_INJURY_DOCTOR,			// 医者ＮＰＣウィンドウ
	WINDOW_MESSAGETYPE_JUDGEMAN,				// 鉴定士ＮＰＣウィンドウ
	WINDOW_MESSAGETYPE_BOARD,					// 伝言版ウィンドウ
	WINDOW_MESSAGETYPE_REPAIR_MAN,				// 修理屋ＮＰＣウィンドウ
	WINDOW_MESSAGETYPE_BANK,					// 银行ＮＰＣウィンドウ
	WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP1,		// モンスタースキルショップ　贩卖ウィンドウ
	WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP2,		// モンスタースキルショップ　モンスター选择ウィンドウ
	WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP3,		// モンスタースキルショップ　记忆位置选择ウィンドウ
	WINDOW_MESSAGETYPE_TRADE_NPC_MENU,			// トレードＮＰＣのトップメニュー
	WINDOW_MESSAGETYPE_TRADE_NPC_MAIN,			// トレードＮＰＣのメインウィンドウ
	WINDOW_MESSAGETYPE_SHOVEL_SELECT,			/* スコップ选择 */
	WINDOW_MESSAGETYPE_ITEMCOUNT_TOP,			/* アイテムカウントＮＰＣトップメニュー */
	WINDOW_MESSAGETYPE_ITEMCOUNT_MAIN,			/* アイテムカウントＮＰＣメインメニュー */
#ifdef PUK2
	WINDOW_MESSAGETYPE_GUILDMONSTER,			/* 家族モンスター状态 */
	WINDOW_MESSAGETYPE_ITEMBOX,					/* 公会宠物部屋アイテムボックス */
	WINDOW_MESSAGETYPE_FOODBOX,					/* 公会宠物部屋えさ箱 */
	WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT,	// 信息とニ行入力
	WINDOW_MESSAGETYPE_ORTHOPEDIST,				// 整形外科医ウィンドウ
	WINDOW_MESSAGETYPE_ORTHOPEDIST_CONFIRMATION,// 整形外科医确认ウィンドウ
#endif
#ifdef PUK3_PROF
	WINDOW_MESSAGETYPE_PROFILE,                 //プロフィール初期ウィンドウ
	WINDOW_MESSAGETYPE_PROFILELIST,             //プロフィールリストウィンドウ
	WINDOW_MESSAGETYPE_PROFILEMAIL,             //プロフィールメールウィンドウ
#endif
#ifdef PUK3_PETSKILLSHOPEX
	WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX1=42,// モンスタースキルショップ扩张　贩卖ウィンドウ
	WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX2,	// モンスタースキルショップ扩张　モンスター选择ウィンドウ
	WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX3,	// モンスタースキルショップ扩张　记忆位置选择ウィンドウ
#endif

#ifdef _ENABLE_ALBUM_ITEMS
	//なんだろう过去（ドワンゴ）の遗产？
	//鲭侧ではifdefできることなくＯＮになっていたが
	//クライアントではＯＦＦになってる
	//ウインドウを追加する际はこの上に追加すること（杉山
	WINDOW_MESSAGETYPE_ALBUM_WINDOW,			// アルバムウインドウ
#endif /* _ENABLE_ALBUM_ITEMS */
};

#ifdef PUK2
typedef enum
{
    CHAR_WINDOWTYPE_RETURNTOELDER=-1,   /*  长老へ返回ウィンドウ    */
    CHAR_WINDOWTYPE_RESURRECTION=-2,   /*  复活するウィンドウ    */

	CHAR_WINDOWTYPE_SELECTBATTLE = 1,		/* 入る战闘を选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTDUEL = 2,  		/* 入るDUELを选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTTRADECARD = 3, 	/* 名刺交换を选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTPARTY = 4, 		/* 布ティを选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTBATTLEWATCH = 5, 	/* 観战を选择するウィンドウ */
	CHAR_WINDOWTYPE_MICMESSAGE = 6,			/* MICNPCを使って出ウィンドウ */
	CHAR_WINDOWTYPE_SELECTITEM = 7,			/* ITEMを使って出ウィンドウ */
	CHAR_WINDOWTYPE_SELECTITEM2 = 8,			/* ITEMを使って出ウィンドウ（假)*/

	CHAR_WINDOWTYPE_SELECTRENAMEITEM_PAGE1 = 10,	/* 名称を变更するアイテムを选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTRENAMEITEM_PAGE2 = 11,	/* 名称を变更するアイテムを选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTRENAMEITEM_PAGE3 = 12,	/* 名称を变更するアイテムを选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTRENAMEITEM_PAGE4 = 13,	/* 名称を变更するアイテムを选择するウィンドウ */
	CHAR_WINDOWTYPE_SELECTRENAMEITEM_RENAME = 14,	/* 名称を入力するウィンドウ */
	CHAR_WINDOWTYPE_SELECTRENAMEITEM_RENAME_ATTENTION  = 15,	/* 名称を入力するウィンドウ */
	CHAR_WINDOWTYPE_CONFIRMPROFILECARD = 20,


	CHAR_WINDOWTYPE_DENGON = 50,			/* 伝言板 */
	/* それぞれのNPCが使うウィンドウを定义する */
	/* 番号はそれなりに间を空けて使うとよろしいかと。 */

	/* ウィンドウマンの定义。
	 * これは设定で决めたウィンドウ分出すので，とりあえず１００个予约する。
	 * この100 - 200 のウィンドウは他では使わないで下さい。
	 */

	CHAR_WINDOWTYPE_WINDOWMAN_START = 100,
	CHAR_WINDOWTYPE_WINDOWMAN_STARTMSG = CHAR_WINDOWTYPE_WINDOWMAN_START,
	CHAR_WINDOWTYPE_WINDOWMAN_END = 200,

	CHAR_WINDOWTYPE_WINDOWHEALER_START = 220,
	CHAR_WINDOWTYPE_WINDOWHEALER_STARTMSG = CHAR_WINDOWTYPE_WINDOWHEALER_START,
	CHAR_WINDOWTYPE_WINDOWHEALER_HPMSG = 221,
	CHAR_WINDOWTYPE_WINDOWHEALER_OKHPMSG = 222,
	CHAR_WINDOWTYPE_WINDOWHEALER_SPIRITMSG = 223,
	CHAR_WINDOWTYPE_WINDOWHEALER_OKSPIRITMSG = 224,
	CHAR_WINDOWTYPE_WINDOWHEALER_ALLMSG = 225,
	CHAR_WINDOWTYPE_WINDOWHEALER_OKALLMSG = 226,
	CHAR_WINDOWTYPE_WINDOWHEALER_END = 227,

	CHAR_WINDOWTYPE_WINDOWSAVEPOINT_START = 230,

	CHAR_WINDOWTYPE_WINDOWEVENT_STARTMSG = 231,
	CHAR_WINDOWTYPE_WINDOWEVENT_NOWEVENT = 232,
	CHAR_WINDOWTYPE_WINDOWEVENT_ENDEVENT = 233,
	CHAR_WINDOWTYPE_WINDOWEVENT_REQMAINMSG = 234,
	CHAR_WINDOWTYPE_WINDOWEVENT_ACCMAINMSG = 235,
	CHAR_WINDOWTYPE_WINDOWEVENT_NOMALMSG = 236,
	CHAR_WINDOWTYPE_WINDOWEVENT_CLEANMSG = 237,
	CHAR_WINDOWTYPE_WINDOWEVENT_REQTHANK = 238,
	CHAR_WINDOWTYPE_WINDOWEVENT_ACCTHANK = 239,
	

	CHAR_WINDOWTYPE_ITEMSHOP_START = 240,
	CHAR_WINDOWTYPE_ITEMSHOP_STARTMENU = CHAR_WINDOWTYPE_ITEMSHOP_START,
	CHAR_WINDOWTYPE_ITEMSHOP_BUY = 241,
	CHAR_WINDOWTYPE_ITEMSHOP_BUY_MSG = 242,
	CHAR_WINDOWTYPE_ITEMSHOP_SELL_MSG = 243,
	CHAR_WINDOWTYPE_ITEMSHOP_END = 244,
	CHAR_WINDOWTYPE_ITEMSHOP_LIMIT = 245,
	CHAR_WINDOWTYPE_ITEMSHOP_EXPRESS = 246,

	CHAR_WINDOWTYPE_DUELRANKING_START = 250,
	CHAR_WINDOWTYPE_DUELRANKING_TOPRANKING = 251,
	CHAR_WINDOWTYPE_DUELRANKING_MYRANKING = 252,
	CHAR_WINDOWTYPE_DUELRANKING_WAIT = 253,

	CHAR_WINDOWTYPE_WINDOWPETSKILLSHOP = 260,

	CHAR_WINDOWTYPE_WINDOWPETSHOP_START = 261,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_PETSELECT = 262,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_MAIN = 263,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_GOLDOVER 	= 264,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_PETSELECT2 = 265,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_MAIN2 = 266,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_DRAWSELECT = 267,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_ASKDRAW = 268,
	CHAR_WINDOWTYPE_WINDOWPETSHOP_END = 269,

	CHAR_WINDOWTYPE_WINDOWWARPMAN_MAIN = 271,
	CHAR_WINDOWTYPE_WINDOWWARPMAN_ERR = 272,
	CHAR_WINDOWTYPE_WINDOWWARPMAN_END = 273,

	CHAR_WINDOWTYPE_NPCENEMY_START = 281,
	
	CHAR_WINDOWTYPE_CHARM_START = 282,
	CHAR_WINDOWTYPE_CHARM_REARY = 283,
	
	CHAR_WINDOWTYPE_QUIZ_START = 284,
	CHAR_WINDOWTYPE_QUIZ_MAIN = 285,
	CHAR_WINDOWTYPE_QUIZ_END = 286,
	
	CHAR_WINDOWTYPE_POOLITEMSHOP_START = 290,
	CHAR_WINDOWTYPE_POOLITEMSHOP_POOL_MSG = 291,
	CHAR_WINDOWTYPE_POOLITEMSHOP_DRAW_MSG = 292,
	CHAR_WINDOWTYPE_POOLITEMSHOP_FULL_MSG = 293,
	CHAR_WINDOWTYPE_POOLITEMSHOP_HAVEITEMFULL_MSG = 294,
	CHAR_WINDOWTYPE_POOLITEMSHOP_END = 300,

	CHAR_WINDOWTYPE_JOBSMASTER_START = 301,
	CHAR_WINDOWTYPE_JOBSMASTER_LIST = 301,
	CHAR_WINDOWTYPE_JOBSMASTER_MYJOBS = 302,
	
	CHAR_WINDOWTYPE_SKILLMASTER_START = 311,
	CHAR_WINDOWTYPE_SKILLMASTER_LIST = 312,
	CHAR_WINDOWTYPE_SKILLMASTER_MYJOBS = 313,
	CHAR_WINDOWTYPE_SKILLMASTER_MYSKILL = 314,

	CHAR_WINDOWTYPE_SKILLMASTER_MYALLSKILL = 315,
	CHAR_WINDOWTYPE_SKILLMASTER_KILL_SKILL = 316,
	CHAR_WINDOWTYPE_SKILLMASTER_DISP_ALL = 317,
	CHAR_WINDOWTYPE_SKILLMASTER_EXPENSIVE = 318,

	CHAR_WINDOWTYPE_SKILLMASTER_SHOP = 319,
	CHAR_WINDOWTYPE_SKILLMASTER_SHOP_BUY = 320,
	CHAR_WINDOWTYPE_SKILLMASTER_SHOP_REMOVE = 321,

	CHAR_WINDOWTYPE_JOBMASTER_START = 322,
	CHAR_WINDOWTYPE_JOBMASTER_END = 323,
	CHAR_WINDOWTYPE_JOBMASTER_CHANGE = 324,
	CHAR_WINDOWTYPE_JOBMASTER_RANKUP = 325,

	CHAR_WINDOWTYPE_EVENT_NPC = 326,

	CHAR_WINDOWTYPE_WINDOWMAN_NPC = 327,

	CHAR_WINDOWTYPE_WINDOWHEALER_NPC = 328,
	CHAR_WINDOWTYPE_WINDOWHEALER_NPC_LP = 329,
	CHAR_WINDOWTYPE_WINDOWHEALER_NPC_END = 330,
	CHAR_WINDOWTYPE_WINDOWHEALER_NPC_FP = 331,
	CHAR_WINDOWTYPE_WINDOWHEALER_NPC_PET = 332,

	CHAR_WINDOWTYPE_ITEM_SHOP2 = 333,
	CHAR_WINDOWTYPE_ITEM_SHOP2_BUY = 334,
	CHAR_WINDOWTYPE_ITEM_SHOP2_SELL = 335,

	CHAR_WINDOWTYPE_INJURY_DOCTOR = 336,

	CHAR_WINDOWTYPE_JUDGEMAN = 337,

	CHAR_WINDOWTYPE_REPAIRMAN = 338,

	CHAR_WINDOWTYPE_BANKMAN_NPC = 339,

	CHAR_WINDOWTYPE_TECHSHOP_START = 340,
	CHAR_WINDOWTYPE_TECHSHOP_SELECT = 341,
	CHAR_WINDOWTYPE_TECHSHOP_SET = 342,

	CHAR_WINDOWTYPE_SPIRITMAN_NPC = 343,

	CHAR_WINDOWTYPE_BRUSHMAN_START = 344,
	CHAR_WINDOWTYPE_BRUSHMAN_SELECT = 345,

	CHAR_WINDOWTYPE_JOBMASTER_CHANGE_CHECK = 346,

	CHAR_WINDOWTYPE_TIMECARD_NPC = 347,

	CHAR_WINDOWTYPE_GOLDRANKING_START = 348,
	CHAR_WINDOWTYPE_GOLDRANKING_TOPRANKING = 349,
	CHAR_WINDOWTYPE_GOLDRANKING_MYRANKING = 350,
	CHAR_WINDOWTYPE_GOLDRANKING_WAIT = 351,
	CHAR_WINDOWTYPE_GOLDRANKING_CHANGEMODE = 352,

	CHAR_WINDOWTYPE_DISEMPLOYMENT_START = 353,	
	CHAR_WINDOWTYPE_DISEMPLOYMENT_REALY1 = 354,	
	CHAR_WINDOWTYPE_DISEMPLOYMENT_OK = 355,
	CHAR_WINDOWTYPE_DISEMPLOYMENT_NO = 356,

	CHAR_WINDOWTYPE_CHARM_NONE = 359,

	CHAR_WINDOWTYPE_PETSHOP_START = 360,
	CHAR_WINDOWTYPE_PETSHOP_YES_NO = 361,
	CHAR_WINDOWTYPE_PETSHOP_SELL = 362,
	CHAR_WINDOWTYPE_PETSHOP_END = 363,

	CHAR_WINDOWTYPE_WINDOWHEALER2_NPC = 364,
	CHAR_WINDOWTYPE_WINDOWHEALER2_NPC_LP = 365,
	CHAR_WINDOWTYPE_WINDOWHEALER2_NPC_END = 366,
	CHAR_WINDOWTYPE_WINDOWHEALER2_NPC_FP = 367,
	CHAR_WINDOWTYPE_WINDOWHEALER2_NPC_PET = 368,

	CHAR_WINDOWTYPE_SELECTRINGWATCH = 369, 	/* 远隔観战を选择するウィンドウ */

	CHAR_WINDOWTYPE_SHOVEL = 370,

	CHAR_WINDOWTYPE_DUSTBOX = 371,
	CHAR_WINDOWTYPE_DUSTBOX_DELETE = 372,

	CHAR_WINDOWTYPE_WATCHEVENT = 373,

	CHAR_WINDOWTYPE_ITEMCOUNT_TOP = 374,
	CHAR_WINDOWTYPE_ITEMCOUNT_MAIN = 375,
	CHAR_WINDOWTYPE_ITEMCOUNT_NPC = 376,

#ifdef PUK2
	CHAR_WINDOWTYPE_GUILD_IS_CREATE = 378,
	CHAR_WINDOWTYPE_GUILD_NAME_REQ,
	CHAR_WINDOWTYPE_GUILD_CREATE_ERROR,
	CHAR_WINDOWTYPE_GUILD_CREATE_SUCCESS,

	CHAR_WINDOWTYPE_GUILD_DELETE,
	CHAR_WINDOWTYPE_GUILD_DELETE_ERROR,
	CHAR_WINDOWTYPE_GUILD_DELETE_CANCEL,
	CHAR_WINDOWTYPE_GUILD_DELETE_COMPLETE,

	CHAR_WINDOWTYPE_GUILD_EGGER,
	CHAR_WINDOWTYPE_GUILD_EGGER_EXIT,

	CHAR_WINDOWTYPE_GUILD_INVITE,
	CHAR_WINDOWTYPE_GUILD_INVITED,

	CHAR_WINDOWTYPE_GUILDMONSTER_MES,
	CHAR_WINDOWTYPE_GUILDMONSTER_STATUS,
	CHAR_WINDOWTYPE_GUILDMONSTER_ITEM,

	CHAR_WINDOWTYPE_GUILD_DELETE_CHECK = 392,

        CHAR_WINDOWTYPE_ORTHOPEDIST_START_SELECT = 393,
        CHAR_WINDOWTYPE_ORTHOPEDIST_MONEY_SELECT = 394,
        CHAR_WINDOWTYPE_ORTHOPEDIST_MESSAGE = 395,
        CHAR_WINDOWTYPE_ORTHOPEDIST_FACECHANGE1 = 396,
        CHAR_WINDOWTYPE_ORTHOPEDIST_FACECHANGE2 = 397,
        CHAR_WINDOWTYPE_ORTHOPEDIST_END = 398,
#endif 
	CHAR_WINDOWTYPE_ALBUMWINDOW = 377,

}CHAR_WINDOWTYPE;
#endif

#define	WINDOW_BUTTONTYPE_NONE		(0)
#define	WINDOW_BUTTONTYPE_OK		(1 << 0)
#define	WINDOW_BUTTONTYPE_CANCEL	(1 << 1)
#define	WINDOW_BUTTONTYPE_YES		(1 << 2)
#define	WINDOW_BUTTONTYPE_NO		(1 << 3)
#define	WINDOW_BUTTONTYPE_PRE		(1 << 4)
#define	WINDOW_BUTTONTYPE_NEXT		(1 << 5)
#define WINDOW_BUTTONTYPE_DELETE	(1 << 6)
#ifdef SERVER_WIN_CLOSE
#define WINDOW_BUTTONTYPE_WIN_CLOSE	-1
#endif

typedef enum
{
	CHAR_SE_JOINPARTY,				// 仲间入る
	CHAR_SE_DISCHARGEPARTY,			// 仲间拔ける
	CHAR_SE_PICKUPITEM,				// アイテム拾う
	CHAR_SE_DROPITEM,				// アイテム落す
	CHAR_SE_PICKUPGOLD,				// お金拾う
	CHAR_SE_DROPGOLD1,				// お金落す(1～999 GOLD)
	CHAR_SE_DROPGOLD2,				// お金落す(1000～99999 GOLD)
	CHAR_SE_DROPGOLD3,				// お金落す(100000～ GOLD)
	CHAR_SE_EXCHANGECARD,			// 名刺交换
} CHAR_SE;


typedef enum
{
	ITEM_SWORD,				// 剑
	ITEM_AXE,				// 斧
	ITEM_SPEAR,				// 枪
	ITEM_STAFF,				// 杖
	ITEM_BOW,				// 弓矢
	ITEM_KNIFE,				// ナイフ(投げ系)
	ITEM_BOOMERANG,			// ブーメラン(投げ系)
	ITEM_SHIELD,			// 盾
	ITEM_HELM,				// 兜
	ITEM_HAT,				// 帽子
	ITEM_ARMOUR,			// 铠
	ITEM_CLOTHES,			// 服
	ITEM_ROBE,				// ローブ
	ITEM_BOOTS,				// ブーツ
	ITEM_SHOES,				// 靴
	ITEM_BRACELET,			// ブレスレット
	ITEM_MUSIC,				// 乐器
	ITEM_NECKLACE,			// 首饰り
	ITEM_RING,				// 指轮
	ITEM_BELT,				// ベルト
	ITEM_EARRING,			// イヤリング
	ITEM_AMULET,			// お守り
	ITEM_CRYSTAL,			// クリスタル
	ITEM_EQUIPCATEGORYNUM,
	ITEM_DISH = ITEM_EQUIPCATEGORYNUM,	// 料理
	ITEM_FURNITURE,			// 家具
	ITEM_MATERIAL,			// そのた装备品素材
	ITEM_OTHER,				// それ以外
	ITEM_BOX,				// 宝箱
	ITEM_KEY,				// 键
	ITEM_ORE,				// 鉱石
	ITEM_LUMBER,			// 木材
	ITEM_CLOTH,				// 布
	ITEM_MEAT,				// 肉
	ITEM_FISH,				// 鱼
	ITEM_VEGETABLE,			// 野菜
	ITEM_FOODINGREDIENT,	// そのた食材
	ITEM_HERB,				// ハーブ
	ITEM_MEDICINALPLANTS,	// 薬草
	ITEM_JEWEL,				// 宝石
	ITEM_MATERIAL_B,		// その他素材B
	ITEM_SEALEDCARD,		// 封印カード
	ITEM_ETC_CARD,			// その他のカード
	ITEM_FOOD,				// 食べ物
	ITEM_DRUG,				// 薬
	ITEM_BOOK,				// 本
	ITEM_MAP,				// 地图
	ITEM_PILOT,				// パイロット用アイテム
	ITEM_LOTTERY,			// くじ
	ITEM_STAMP,				// スタンプアイテム
	ITEM_REMAKEMATERIAL,	// アイテム再作成の素材
	ITEM_ALBUM,				// アルバムアイテム
	ITEM_BOMB,				// 爆弹
#ifdef PUK2
	ITEM_FECES,				// ふん
	ITEM_GUILMONSNACK,		// モンスターおやつ
	ITEM_MISC,				// ？
#endif
	ITEM_CATEGORYNUM,
} ITEM_CATEGORY;

// アイテムパラメータをサーバーから受け取る时の列举
typedef enum{
	ITEM_RECV_ARRAY = 0,	// アイテム栏の番号
	ITEM_RECV_NAME,			// 名称
	ITEM_RECV_COLOR,		// 色
	ITEM_RECV_MEMO,			// 说明文
	ITEM_RECV_IMAGENO,		// 画像番号
	ITEM_RECV_ABSFIELD,		// フィールドで使えるか？
	ITEM_RECV_ABSBATTLE,	// 战闘中に使えるか？
	ITEM_RECV_TARGET,		// 使用できる对象
	ITEM_RECV_LV,			// アイテムのランク
	ITEM_RECV_FLAG,			// ペットメールで送信可能か？
	ITEM_RECV_ID,			// アイテムのＩＤ
	ITEM_RECV_KIND,			// アイテムの种类
	ITEM_RECV_NUM,			// 数量
	ITEM_RECV_FREENAME,		// 刻印文字
#ifdef _OPERATION_REMAKE_ITEM
	ITEM_RECV_VARDATA,		// 泛用データ领域
#endif /* _OPERATION_REMAKE_ITEM */

	ITEM_RECV_ENUMMAX		// この enum の数。今は14
}ITEM_RECV_ENUM;


// オペカテのテックモード。
typedef enum{
	TECH_MODE_REPAIR_WEPON = 217,	// 武器修理スキル
	TECH_MODE_REPAIR_ARMOR = 218,	// 防具修理スキル
	TECH_MODE_JUDGEITEM = 219,		// 鉴定スキル
	TECH_MODE_MARK = 220,	// 防具修理スキル
	TECH_MODE_END
}TECH_MODE;

// 等级の最大值
#ifdef PUK2_MAXLEVEL_UP
	#define MAX_LEVEL 130
#else
#define MAX_LEVEL 120
#endif

//ハンコ、刻印识别文字
#define ITEM_HANKO_STRING	"[han]"
#define ITEM_INCUSE_STRING	"[kok]"



//-------------------------------------------------------------------------//
// 构造体定义                                                              //
//-------------------------------------------------------------------------//

// 通常凸で押すと凹になるボタン情报
typedef struct
{
	int cx, cy;						// 表示座标
	int x, y;						// 范围の左上座标
	int w, h;						// 范围の縦横幅
	unsigned int graNo1;			// 凸状态のボタンの画像番号
	unsigned int graNo2;			// 凹状态のボタンの画像番号
} GRA_BTN_INFO1;


// 通常凸でカーソルが合うと凹になるボタン情报
typedef struct
{
	int cx, cy;						// 表示座标
	int x, y;						// 范围の左上座标
	int w, h;						// 范围の縦横幅
	unsigned int graNo1;			// 凸状态のボタンの画像番号
	unsigned int graNo2;			// 凹状态のボタンの画像番号
} GRA_BTN_INFO2;


// 通常凸で押すと凹になりそのまま保持するボタン情报
typedef struct
{
	int cx, cy;						// 表示座标
	int x, y;						// 范围の左上座标
	int w, h;						// 范围の縦横幅
	unsigned int graNo1;			// 凸状态のボタンの画像番号
	unsigned int graNo2;			// 凹状态のボタンの画像番号
} GRA_BTN_INFO3;


// 文字列のボタン情报
typedef struct
{
	int cx, cy;						// 表示座标
	int x, y;						// 范围の左上座标
	int w, h;						// 范围の縦横幅
	char *str;						// ボタン文字列ポインタ
} STR_BTN_INFO;


// トップバーボタン情报
typedef struct
{
	int cx, cy;						// 表示座标
	int x, y;						// 范围の左上座标
	int w, h;						// 范围の縦横幅
	unsigned int graNo1;			// 凸状态のボタンの画像番号
	unsigned int graNo2;			// 凹状态のボタンの画像番号
	int dy;							// 上下ずれ位置
} TOP_BAR_BTN_INFO;



// 泛用ウィンドウの情报构造体
typedef struct
{
	short type;						// ウィンドウ种类
	short cnt;						// ウィンドウ表示エフェクトのカウンタ
	int w, h;						// ウィンドウ縦横幅
	int cx, cy;						// ウィンドウの中心座标
	int nx, ny;						// エフェクトの计算用
	unsigned int graNo;				// ウィンドウの画像番号
	short hitFlag;					// マウスカーソルとの当り判定
} WIN_DISP;


typedef struct
{
	unsigned int no;				// メニュー管理番号
	void (*func)( int );			// メニュー处理关数へのポインタ
	char menuArea;					// メニューの表示エリア
	ACTION *ptAct;					// ウィンドウまたはエフェクト用アクションポインタ
	char openFlag;					// オープンフラグ
} MENU_INFO;


typedef struct
{
	unsigned int graNo;				// タブ画像番号
	int x, y;						// タブの范围の左上（ウィンドウの表示位置を后で加算）
	int w, h;						// 范围の縦横幅
	char *info;
} TAB_INFO;

typedef struct
{
	int x, y;						// 表示座标（左上）
	int w, h;						// 縦横幅
	int menuNo;						// メニュー机能番号
	unsigned int winGraNo[5];		// ウィンドウ画像番号
	TAB_INFO tab[5];				// タブ情报
	char winMode;					// ウィンドウの状态
	char tabOn;						// マウスカーソルがどのタブの上にあるか
	GRA_BTN_INFO1 closeBtn;			// クローズボタンの情报
} MENU_WINDOW_INFO;


typedef struct
{
	STR_BTN_INFO strBtnInfo;
	int (*func)( void );			// 呼び出す关数へのポインタ
	unsigned char color;
	unsigned char hitFlag;
	unsigned char hitBoxColor;
	char *info;
} MENU_ITEM_INFO;


//-------------------------------------------------------------------------//
// 外部参照变数                                                            //
//-------------------------------------------------------------------------//
//extern CHAR_TITLE_INFO spNameSet[];

// 简易登出フラグ
extern int SimpleLogoutFlag;

extern int useItemTargetSelNo;
extern USE_ITEM_TARGET_SEL useItemTargetSel[];

extern int tradeOpponentMode;
extern int tradeOwnerMode;
extern int tradeOpponentGold;
extern int tradeStackFlag;

extern int sendItemSelectNo;

extern int mailHistorySelectNo;
extern int mailHistoryPage;

extern int addressBookSortMode;
extern int addressBookNewPage;
extern int addressBookPage;

extern int categoryCMode;
extern int categoryCUseFp;

extern int giTechMode;
extern int targetItemSelMode;
extern int targetItemSelNo;
extern int targetLastItemImageNo;
extern char targetLastItemName[];

extern int registItemGraNo[];
extern int registItemNum[];
extern int registItemIndex[];

extern int recipePageNo;
extern int recipeMaxPage;
extern int selectRecipeNo;

extern int onCursorMenuNo;

extern int autoMapWinX;
extern int autoMapWinY;

extern int bonusPoint;

extern MENU_WINDOW_INFO mosterStatusMenuWin;
extern MENU_WINDOW_INFO statusMenuWin;
extern MENU_WINDOW_INFO skillMenuWin;

extern int takeOnActiveItemNo;
extern int takeOnItemNo;
extern int takeOnItemGraNo;
extern int takeOnItemX, takeOnItemY;
extern int useItemNo;

extern int taskBarDrawFlag;
extern int taskBarStatus;

extern int actionNoChgTbl[];

extern int serverRequestWinWindowType;
extern int serverRequestWinObjIndex;

extern int MouseCursorFlag;
extern int ctrlSetting;

extern char abilityPage;

extern char selTech;

// レシピの名称バックアップ用文字列
extern char recipeNameBak[ 256 ];


extern INPUT_STR mailEditStr;
extern INPUT_STR mailPetEditStr;
extern INPUT_STR messageBoardInputBuf;

extern int autoMapOpenFlag;

// モンスターメニューセレクトペット番号
extern int monsterStatusSelPetNo;

// マップウィンドウの座标
extern MENU_WINDOW_INFO autoMapMenuWin;

#ifdef _SYSTEMMENU_BTN_CONFIG
// 设定键位
extern int systemMenuBtnConfigSetting;
#endif /* _SYSTEMMENU_BTN_CONFIG */

//-------------------------------------------------------------------------//
// 关数プロト种类宣言                                                    //
//-------------------------------------------------------------------------//
void openShovelMenu( char * );
void openTradeNpcMenu( char * );
void initTradeNpcMenu( void );
void tradeNpcMenu( int );
void tradeNpcItemInfoWindow( int, int, int, int );
void tradeNpcOkMenu( int );

void openTradeNpcTopMenu( char * );
void initTradeNpcTopMenu( void );
void tradeNpcTopMenu( int );

void openUseItemTargetSelMenu2( char * );
void useItemTargetSelMenu2( int );
void openUseItemTargetSelMenu( char * );
void useItemTargetSelMenu( int );

void monsterSkillShopOkMenu( int );
void openMonsterSkillShopMenu3( char * );
void initMonsterSkillShopMenu3( void );
void monsterSkillShopMenu3( int );
void openMonsterSkillShopMenu2( char * );
void initMonsterSkillShopMenu2( void );
void monsterSkillShopMenu2( int );
void openMonsterSkillShopMenu1( char * );
void initMonsterSkillShopMenu1( void );
void monsterSkillShopMenu1( int );

void openItemShopSellWin( char * );
void initItemShopSellWin( void );
void itemShopSellWin( int );
void shopItemInfoWindow( int, int, int, int, int );
void itemShopSellOkWin( int );
void openItemShopBuyWin( char * );
void initItemShopBuyWin( void );
void itemShopBuyWin( int );
void itemShopBuyOkWin( int );

ACTION *createActNpc( int, int, int, int );
void actNpcProc( ACTION * );
void openItemShopTopWin( char * );
void initItemShopTopWin( void );
void itemShopTopWin( int );


void setTradeMonsterList( int );
void setTradeItemListOnce( int, int );
void setTradeItemList( int );
void tradeStackItemProc( int, int );
void openTradePlayerSelectMenu( char * );
void tradePlayerSelectMenu( int );
void openTradeMenu( char * );
void tradeMenu( int );
void tradeOpponentItemProc( void );
void tradeOpponentMonsterProc( void );
void tradeOpponentGoldProc( void );
void tradeOwnerItemProc( void );
void tradeOwnerMonsterProc( void );
void tradeOwnerGoldProc( void );
void setOpponentItemList( char * );
void setOppnentMonsterList( int, char * );
void setOppnentMonsterSkillList( int, char * );
void clearTradeList( int );
void swapTradeItem( int, int, int );

void initAddressBookMenu( void );
void addressBookMenu( int );
void sortAddressBook( void );
void addressBookSortMenu( int );
void addressBookDelMenu( int );
void mailSelectMenu( int );
void initMailEditMenu( void );
void mailEditMenu( int );
void initMailHistoryMene( void );
void mailHistoryMenu( int );
void mailPetSelectMenu( int );
void initMailPetEditMenu( void );
void mailPetEditMenu( int );
void initSendItemSelectMenu( void );
void sendItemSelectMenu( int );
ACTION *createActSpr( int, int, int, int );
void createActSprProc( ACTION * );


void initBattleResultMenu( void );
void battleResultMenu( int );

void initDuelResultMenu( void );
void duelResultMenu( int );

void automapWindow( int );

void statusMenu( int );
//int statusMenuItemClose( void );
int statusMenuStatusWindow( int, int );
int statusMenuDetailWindow( int, int );
void bonusPointFlashProc( void );
int statusMenuTitleInputWindow( int, int );



void shortCutFuncSystemMenu( void );
void shortCutFncPlayerStatusMenu( void );
void shortCutFncSkill( void );
void shortCutFncItem( void );
void shortCutFncAddress( void );
void shortCutFncAlbumList( void );
void shortCutFncAutoMap( void );
void shortCutFncMonsterList( void );
void shortCutFncAction( void );
void shortCutFncScreenShot( void );
void shortCutFncChangeFontSize( void );
void shortCutFncShowName( void );
void shortCutFncActionSit( void );
void shortCutFncActionHand( void );
void shortCutFncActionNod( void );
void shortCutFncActionHappy( void );
void shortCutFncActionAngry( void );
void shortCutFncActionSad( void );
void shortCutFncActionStand( void );
void shortCutFncActionWalk( void );
void shortCutFncActionAttack( void );
void shortCutFncActionDamage( void );
void shortCutFncActionDead( void );
void shortCutFncActionGuard( void );
void shortCutFncActionThrow( void );
void shortCutFncActionJanken( void );
void shortCutFncChatLineUp( void );
void shortCutFncChatLineDown( void );
void shortCutFncChatVoiceUp( void );
void shortCutFncChatVoiceDown( void );
#ifdef _DEBUG
void shortCutFncDebugCommonWindow( void );
#endif
#ifdef _DEBUG
void shortCutFncDebug( void );
void shortCutFncDebug2( void );
#endif


void systemMenu( int );
int systemMenuItemLogout( void );
int systemMenuItemChatConfig( void );
int systemMenuItemChatConfigStrSet( void );
int systemMenuItemChatSoundChange( void );
int systemMenuItemChatAreaUp( void );
int systemMenuItemChatAreaDown( void );
void initSystemMenuChatStrSet( void );
void systemMenuChatStrSet( int );
int systemMenuItemBgmConfig( void );
int systemMenuItemSeConfig( void );
int systemMenuItemMouseConfig( void );
int systemMenuItemKeyboardConfig( void );
int systemMenuItemDrawConfig( void );
#ifdef _SYSTEMMENU_BTN_CONFIG
int systemMenuItemBtnConfig( void );
#endif /* _SYSTEMMENU_BTN_CONFIG */
//int systemMenuItemLoginGate( void );


void systemMenuLogout( int );
void systemMenuChatConfig( int );
void systemMenuBgmConfig( int );
void systemMenuSeConfig( int );
void systemMenuMouseConfig( int );
void systemMenuKeyConfig( int );
void systemMenuDrawConfig( int );
#ifdef _SYSTEMMENU_BTN_CONFIG
void systemMenuBtnConfig( int );
#endif /* _SYSTEMMENU_BTN_CONFIG */
//void systemMenuLoginGate( int );

int systemMenuItemChatConfigLineUp( void );
int systemMenuItemChatConfigLineDown( void );
int systemMenuItemChatConfigColorChange( void );
int systemMenuItemChatConfigReturn( void );

int systemMenuItemBgmConfigVolUp( void );
int systemMenuItemBgmConfigVolDown( void );
int systemMenuItemBgmConfigReturn( void );

int systemMenuItemSeConfigVolUp( void );
int systemMenuItemSeConfigVolDown( void );
int systemMenuItemSeConfigStereo( void );
int systemMenuItemSeConfigReturn( void );

int systemMenuItemMouseConfigCursorChange( void );
int systemMenuItemMouseConfigReturn( void );

int systemMenuItemKeyConfigCtrlChange( void );
int systemMenuItemKeyConfigReturn( void );

int systemMenuItemDrawConfigCtrlChange( void );
int systemMenuItemDrawConfigReturn( void );

#ifdef _SYSTEMMENU_BTN_CONFIG
int systemMenuItemBtnConfigCtrlChange( void );
int systemMenuItemBtnConfigReturn( void );
#endif /* _SYSTEMMENU_BTN_CONFIG */

void initTargetItemSelMemu( void );
void targetItemSelMenu( int );
void incuseMenu( int );

void categoryAResultMenu( int );
void openCategoryAResult( int, char * );

void categoryBResultMenu( int );
void openCategoryBResult( int, char * );

void categoryCResultMenu( int );
void openCategoryCResult( int, char * );
void poseResultChara( ACTION *, int );

void categoryCEnoughFpMenu( int );
void openCategoryCTargetSel1Menu( char * );
void categoryCTargetSel1Menu( int );
void openCategoryCTargetSel2Menu( char * );
void categoryCTargetSel2Menu( int );
int getCategoryCMode( int );

void recipeMenu( int );
void initCreateMenu( void );
void createMenu( int );
void clearRegistItem( void );
void initMaterialSelectMenu( void );
void materialSelectMenu( int );

void initGatherMenu( void );
void gatherMenu( int );
int gatheringParformance( void );
int fellingAnime( void );
int huntingAnime( void );
int diggingAnime( void );

int getModeCategoryD( void );
void openCategoryDResult( int, char * );
void categoryDResultMenu( int );

void initItemMenu( void );
void itemMenu( int );
void clearTakeOnItem( void );
void chaekClearTakeOnItem( int );
void itemInfoWindow( int, int, int, int );
void initItemMenuDropGold( void );
void itemMenuDropGold( int );
void stockFontBuffer( int, int, char, int, int, char *, BOOL, unsigned char );
ACTION *createActEmChara( int, int, int );
void emPlayerProc( ACTION * );
void stackItemMenu( int );
void categoryBEnoughMojiMenu( int );
void categoryBEnoughSpaceMenu( int );

void actionMenu( int );
void changeAction( int );

void skillMenu( int );


//void initAbilityMenu( void );
void abilityMenu( int );

void initMonsterStatusMenu( void );
void monsterStatusMenu( int );
int monsterStatusMenuItemWin1( int, int );
int monsterStatusMenuItemWin2( int, int );
int monsterStatusMenuItemWin3( int, int );
ACTION *createStatusWinPetAction( int, int, int, int );
void statusWinPetProc( ACTION * );

void initMonsterListMenu( void );
void monsterListMenu( int );
void monsterListChargeMenu( int, int );

void initMonsterNameChange( void );
void monsterNameChange( int );


void initMonsterAlbumListMenu( void );
void monsterAlbumListMenu( int );

void initMonsterAlbumMenu( void );
void monsterAlbumMenu( int );
void drawStatusStar( int, int, int, int );


void initOpenServerRequestWindow( void );
void openServerRequestWindow( int, int, int, int, char * );
#ifdef PUK3_PROF
void openServerRequestWindowForProfile( int, int, int, int, char * );
#endif
void initServerRequestWindowMessage( char * );
void serverRequestWindowMessage( int );
void initServerRequestWindowSelect( char * );
void serverRequestWindowSelect( int  );

ACTION *createActSkillMaster( int, int, int );
void skillMasterProc( ACTION * );
void initSkillShopWindow1( char * );
void init2SkillShopWindow1( void );
void skillShopWindow1( int );
void initSkillShopWindow2( char * );
void init2SkillShopWindow2( void );
void skillShopWindow2( int );
void initSkillShopWindow3( char * );
void init2SkillShopWindow3( void );
void skillShopWindow3( int );

void openDoctorNpcMenu( char * );
void doctorNpcMenu( int );

void openAppraisalNpcMenu( char * );
void initAppraisalNpcMenu( void );
void appraisalNpcMenu( int );
void appraisalNpcItemInfoWindow( int, int, int, int );
void appraisalOkMenu( int );

void openRepairNpcMenu( char * );
void initRepairNpcMenu( void );
void repairNpcMenu( int );
void repairNpcItemInfoWindow( int, int, int, int );
void repairOkMenu( int );

void openMessageBoardMenu( char * );
void messageBoardMenu( int );

void openBankNpcMenu( char * );
void setBankItemListOnce( int, int );
void setBankItemList( int );
void swapBankItem( int, int, int );
void bankNpcMenu( int );
void bankItemProc( void );
void bankMonsterProc( void );
void bankDepositProc( void );
void bankEduceProc( void );

void inputGold( int *, int );


void taskBarProc( void );
void taskBarDraw( void );


ACTION *createActNoFunc( int, int, int, int );

void memoryMapGridPos( int, int );
int checkMoveMapGridPos( int, int );

int menuItemCommonProc( MENU_ITEM_INFO *, int );
void menuItemCommonDraw( MENU_ITEM_INFO *, int );

int menuCommonProc( int, MENU_WINDOW_INFO *, void (*)( void ), unsigned char );
void menuCommonDraw( MENU_WINDOW_INFO *, unsigned char );
int checkFocusMenuClose( MENU_WINDOW_INFO * );

int pushGraBtnInfo1( GRA_BTN_INFO1 * );
void drawGraBtnInfo1( GRA_BTN_INFO1 *, unsigned char, int, int, int );
int pushGraBtnInfo2( GRA_BTN_INFO2 * );
void drawGraBtnInfo2( GRA_BTN_INFO2 *, unsigned char, int, int, int );
int pushGraBtnInfo3( GRA_BTN_INFO3 * );
void drawGraBtnInfo3( GRA_BTN_INFO3 *, unsigned char, int, int, int );
int pushStrBtnInfo( STR_BTN_INFO * );
void drawStrBtnInfo( STR_BTN_INFO *, unsigned char, int, int, int, int );
int pushTopBarBtnInfo( TOP_BAR_BTN_INFO * );
void drawTopBarBtnInfo( TOP_BAR_BTN_INFO *, unsigned char, int, int, int );

int getMemoLine( char *, int, char *, int, int );


int checkDefaultInputFocus( void );
int checkInputFocus( INPUT_STR * );

void initMenu( void );
void initMenuOnce( void );
int menuOpen( unsigned int );
int menuClose( unsigned int );
int otherMenuClose( unsigned int );
void menuProc( void );
int checkMenuOpen( int );
int checkMenuActive( int );
int checkMenuOpenNo( unsigned int );
int checkMenuActiveNo( unsigned int );
int menuSw( unsigned int );
int searchMenuNo( unsigned int );


#ifdef PUK2_NEW_MENU
ACTION *makeWindowDisp( int, int, int, int, int, BOOL adjust = TRUE );
#else
ACTION *makeWindowDisp( int, int, int, int, int );
#endif
void windowDisp( ACTION * );

void incuseSendSub();


void hankoSelectMenu(int);
void shovelSelectMenu(int status);
void openShovelSelectMenu( char *data );

void openItemCountNpcTopMenu( char *);
void initTradeNpcTopMenu( void);
void itemCountNpcTopMenu( int status);

void openItemCountNpcMainMenu( char *);
void initItenCountNpcMainMenu( void );
void itemCountNpcMainMenu( int status);
void itemCountNpcItemInfoWindow( int, int, int);
void itemCountNpcOkMenu( int status);

// 当たり判定ボックス作成 ******************************************************/
BOOL MakeHitBox( int x1, int y1, int x2, int y2, int dispPrio );
BOOL MakeHitBox( int, int, int, int, int, int, int );

// ログイン时の初期化（太田专用） **************************************************/
void InitOhtaParam( void );

// 文字列のセンターリング *****************************************************/
void CenteringStr( char *inStr, char *outStr, int max );

// 战闘结果ＬＶアップチェック ***********************************************
BOOL CheckBattleResultLvUp( void );

// 战闘结果ウィンドウの表示チェック **************************************************
void CheckBattleResultMenu( void );
// マップウィンドウの自动オープンチェック **************************************
void CheckAutoMapOpen( void );
//*************************************************************
// 自分とペットのボーナスポイントがあるかチェックする
//*************************************************************
//
// 戾り值：０～４：ペットのインデックス、５：自分、６：なし。
//
//*************************************************************
int CheckBonusPoint( void );

//*************************************************************
// 何れかのペットが怪我をしてるかチェック
//
// 戾り值：０～４：ペットのインデックス、５：自分、６：なし。
//
//*************************************************************
int CheckPetInjury( void );

//ハンコ、刻印、通常の名称色判定
void ItemInfoWindowTrueName(int x,int y,int w,int font,int size, char *name, char *freename,int etc1,int etc2,int flg);

#ifdef PUK2_NEW_MENU
	// アイテムの位置が移动された时の处理
	// 引	form ---- 移动元	to ---- 移动先
	void swapItemRelation( int from, int to );

	// トレードウィンドウ破弃关数
	void closeTradeWindow();
	int checkPetWalk( int index );
#endif



#endif
