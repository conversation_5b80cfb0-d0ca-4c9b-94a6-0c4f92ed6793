﻿//メニュー＞アルバム

//１ページ中でのリスト数
#define NEW_ALBUM_LIST_LINE 10

#ifdef ADD_ALBUM 
#define NEW_ALBUM_MAX       320
#else
#define NEW_ALBUM_MAX       255
#endif

//リスト番号
int MonsAlbumPageNo;
//详细番号
int MonsAlbumNo;
//モンスター角度
int MonsAlbumAng;

void InitMenuWindowAlbum( void );
void InitMenuWindowAlbumDetail( void );
void MenuAlbumSetStar(int Point,int SwitchNum);

int NewsealGraNo[] ={
	GID_AlbumDetailCard01,	//封印不可
	GID_AlbumDetailCard00,	//封印可能
};

int NewrareGraNo[] ={
	0,							//通常
	GID_AlbumDetailCard02,		//レア
	GID_AlbumDetailCard03,		//スー布レア
};

//--------------------------------------------------------
//ログイン时の初期化
//--------------------------------------------------------
void InitMenuWindowAlbumInLogin(void){

	//リスト番号
	MonsAlbumPageNo=0;
	//详细番号
	MonsAlbumNo=0;
	//モンスター角度
	MonsAlbumAng=5;

}

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowAlbum( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
		MonsAlbumPageNo=0;
	}

	//初期化
	InitMenuWindowAlbum();

	return TRUE;
}

BOOL MenuWindowAlbumDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

BOOL MenuWindowAlbumDetail( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
		//角度初期化
		MonsAlbumAng=5;
	}

	//初期化
	InitMenuWindowAlbumDetail();

	return TRUE;
}

BOOL MenuWindowAlbumDetailDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
//パネルスイッチ
BOOL MenuSwitchAlbumPanel( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	if( flag & MENU_MOUSE_OVER ){
		strcpy( OneLineInfoStr, MWONELINE_ALBAM_PANEL );
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOver;
		ReturnFlag=TRUE;	
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOff;
	}

	if( flag & MENU_MOUSE_LEFT ){
		if(album.status[selectPcNo][MonsAlbumPageNo+no-EnumGraphAlbumPanel00].useFlag){
			if(WindowFlag[MENU_WINDOW_ALBUM_DETAIL].wininfo==NULL){
				//ウインドウ无いので作る
				MonsAlbumNo=MonsAlbumPageNo+no-EnumGraphAlbumPanel00;
				openMenuWindow( MENU_WINDOW_ALBUM_DETAIL, OPENMENUWINDOW_HIT, 0 );
			}else{
				//ナンバー书き换えだけ
				MonsAlbumNo=MonsAlbumPageNo+no-EnumGraphAlbumPanel00;
				// ウインドウ开く
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			}
		}
		ReturnFlag=TRUE;	
	}

	return ReturnFlag;

}

//アルバムスイッチ类
BOOL MenuSwitchAlbumSwitch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	switch(no){
		case EnumGraphMenuAlbumScrollUp:

			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOn;
			}
			if(flag & MENU_MOUSE_LEFTHOLD){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOff;
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumPageNo-=1;
				if(MonsAlbumPageNo<0){
					MonsAlbumPageNo=0;
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				NumToScrollVMove(&wI->sw[EnumBtMenuAlbumDrag],NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE,MonsAlbumPageNo);
				ReturnFlag=TRUE;	
			}
		break;

		case EnumGraphMenuAlbumScrollDown:

			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOn;
			}
			if(flag & MENU_MOUSE_LEFTHOLD){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOff;
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumPageNo+=1;
				if(MonsAlbumPageNo>NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE-1){
					MonsAlbumPageNo=NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE;
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				NumToScrollVMove(&wI->sw[EnumBtMenuAlbumDrag],NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE,MonsAlbumPageNo);
				ReturnFlag=TRUE;	
			}
		break;

		case EnumGraphMenuAlbumUp:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDownOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDownOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				MonsAlbumPageNo-=10;
				if(MonsAlbumPageNo<0){
					MonsAlbumPageNo=0;
				}
				NumToScrollVMove(&wI->sw[EnumBtMenuAlbumDrag],NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE,MonsAlbumPageNo);
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDownOff;
			}
			break;

		case EnumGraphMenuAlbumDown:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumUpOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumUpOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				MonsAlbumPageNo+=10;
				if(MonsAlbumPageNo>NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE-1){
					MonsAlbumPageNo=NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE;
				}
				NumToScrollVMove(&wI->sw[EnumBtMenuAlbumDrag],NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE,MonsAlbumPageNo);
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumUpOff;
			}
		break;

	}



	return ReturnFlag;
}

BOOL MenuSwitchAlbumScrollSwitch( int no, unsigned int flag ){

	BOOL ReturnFlag;

	ReturnFlag=MenuSwitchScrollBarV(no,flag);

	if(ReturnFlag==TRUE){
		//スクロールから值决める
		MonsAlbumPageNo=ScrollVPointToNum(&wI->sw[EnumBtMenuAlbumDrag],NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE);
	}

	return ReturnFlag;
}

BOOL MenuSwitchAlbumScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		MonsAlbumPageNo = WheelToMove( &wI->sw[EnumBtMenuAlbumDrag], MonsAlbumPageNo,
			 NEW_ALBUM_MAX-NEW_ALBUM_LIST_LINE, mouse.wheel );
	}

	return ReturnFlag;
}

//アルバムディティールスイッチ类
BOOL MenuSwitchAlbumDetailSwitch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	switch(no){
		case EnumGraphAlbumDetailPageDown:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_ALBAMDETAIL_F_BACK );
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailPageDownOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailPageDownOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumNo-=10;
				if(MonsAlbumNo<0)
					MonsAlbumNo=0;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailPageDownOff;
			}
			break;
		case EnumGraphAlbumDetailDown:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_ALBAMDETAIL_BACK );
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailDownOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailDownOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumNo--;
				if(MonsAlbumNo<0)
					MonsAlbumNo=0;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailDownOff;
			}
			break;
		case EnumGraphAlbumDetailUp:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_ALBAMDETAIL_FORWARD );
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailUpOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailUpOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumNo++;
				if(MonsAlbumNo>NEW_ALBUM_MAX-1)
					MonsAlbumNo=NEW_ALBUM_MAX-1;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailUpOff;
			}
			break;
		case EnumGraphAlbumDetailPageUp:
			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_ALBAMDETAIL_F_FORWARD );
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailPageUpOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailPageUpOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumNo+=10;
				if(MonsAlbumNo>NEW_ALBUM_MAX-1)
					MonsAlbumNo=NEW_ALBUM_MAX-1;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDetailPageUpOff;
			}
			break;
		case EnumBTAlbumDetailAng:
			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsAlbumAng++;
				if(MonsAlbumAng>7)
					MonsAlbumAng=0;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			if( flag & MENU_MOUSE_RIGHTAUTO ){
				MonsAlbumAng--;
				if(MonsAlbumAng<0)
					MonsAlbumAng=7;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			break;
	}


	return ReturnFlag;

}

//初期化
void InitMenuWindowAlbum( void ){

	BLT_MEMBER bm={0};
	int i;
	int NoWork;

	for( i = 0; i < NEW_ALBUM_LIST_LINE; i++ )
	{
		if( album.status[selectPcNo][MonsAlbumPageNo+i].useFlag ){
			//ある时
#if 0			
			//ナンバー表示
			NoWork=MonsAlbumPageNo+i+1;		
			wI->sw[EnumGraphAlbumNo0000+i*3+0].Enabled=TRUE;
			wI->sw[EnumGraphAlbumNo0000+i*3+1].Enabled=TRUE;
			wI->sw[EnumGraphAlbumNo0000+i*3+2].Enabled=TRUE;

			((GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumNo0000+i*3+0].Switch)->graNo=GID_Num_Blue2_0+(NoWork%1000)/100;
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumNo0000+i*3+1].Switch)->graNo=GID_Num_Blue2_0+(NoWork%100)/10;
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumNo0000+i*3+2].Switch)->graNo=GID_Num_Blue2_0+NoWork%10;
#else
			//ナンバー表示
			NoWork=MonsAlbumPageNo+i+1;		
			wI->sw[EnumGraphAlbumNo0000+i*3+0].Enabled=FALSE;
			wI->sw[EnumGraphAlbumNo0000+i*3+1].Enabled=FALSE;
			wI->sw[EnumGraphAlbumNo0000+i*3+2].Enabled=FALSE;
	
			bm.rgba.rgba=0xffffffff;
			bm.bltf=BLTF_NOCHG;
			//ここだけ例外的に通常と异なる表示方法をとっています
			StockDispBuffer( wI->wx+wI->sw[EnumGraphAlbumNo0000+i*3+0].ofx+1,
							 wI->wy+wI->sw[EnumGraphAlbumNo0000+i*3+0].ofy+4,
							 DISP_PRIO_WIN2,
							 GID_Num_Blue2_0+(NoWork%1000)/100,
							 0,&bm);
			StockDispBuffer( wI->wx+wI->sw[EnumGraphAlbumNo0000+i*3+1].ofx+1,
							 wI->wy+wI->sw[EnumGraphAlbumNo0000+i*3+1].ofy+4,
							 DISP_PRIO_WIN2,
							 GID_Num_Blue2_0+(NoWork%100)/10,
							 0,&bm);
			StockDispBuffer( wI->wx+wI->sw[EnumGraphAlbumNo0000+i*3+2].ofx+1,
							 wI->wy+wI->sw[EnumGraphAlbumNo0000+i*3+2].ofy+4,
							 DISP_PRIO_WIN2,
							 GID_Num_Blue2_0+NoWork%10,
							 0,&bm);
#endif

			//名称表示
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumPanel00+i].Switch)->text,album.status[selectPcNo][MonsAlbumPageNo+i].name);

			//NEWマーク表示
			if( album.status[selectPcNo][MonsAlbumPageNo+i].newFlag ){
				wI->sw[EnumGraphAlbumNew00+i].Enabled=TRUE;
			}else{
				wI->sw[EnumGraphAlbumNew00+i].Enabled=FALSE;
			}
		}else{
			//无い时

			//ナンバー非表示
			wI->sw[EnumGraphAlbumNo0000+i*3+0].Enabled=FALSE;
			wI->sw[EnumGraphAlbumNo0000+i*3+1].Enabled=FALSE;
			wI->sw[EnumGraphAlbumNo0000+i*3+2].Enabled=FALSE;

			//名称非表示（空白）
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumPanel00+i].Switch)->text,"");

			//NEWマーク非表示
			wI->sw[EnumGraphAlbumNew00+i].Enabled=FALSE;
		}
	}

}

//初期化
void InitMenuWindowAlbumDetail( void ){

	char str[256];
	int i,j;
	GRAPHIC_SWITCH *Graph;

	if(album.status[selectPcNo][MonsAlbumNo].useFlag){
		//データがある时
		wI->sw[EnumTextAlbumDetailName].Enabled=TRUE;
		wI->sw[EnumActionAlbumDetailChar].Enabled=TRUE;
		wI->sw[EnumTextAlbumDetailNo].Enabled=TRUE;
		wI->sw[EnumTextAlbumDetailSlot].Enabled=TRUE;
		wI->sw[EnumTextAlbumDetailType].Enabled=TRUE;
		wI->sw[EnumGraphAlbumDetailCard00].Enabled=TRUE;
		wI->sw[EnumGraphAlbumDetailCard01].Enabled=TRUE;
		for(i=0;i<5;i++){
			wI->sw[EnumGraphAlbumDetailStarVit00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailStarStr00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailStarTgh00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailStarQui00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailStarMgc00+i].Enabled=TRUE;
		}
		for(i=0;i<10;i++){
			wI->sw[EnumGraphAlbumDetailEarth00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailWater00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailFire00+i].Enabled=TRUE;
			wI->sw[EnumGraphAlbumDetailWind00+i].Enabled=TRUE;
		}

		//Newフラグ下げる
		album.status[selectPcNo][MonsAlbumNo].newFlag = 0;

		//アルバム情报のセーブ
//		writeAlbumFile();

		//名称
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailName].Switch)->text,album.status[selectPcNo][MonsAlbumNo].name);
		//キャラクターアクション

		((ACTION_SWITCH *)wI->sw[EnumActionAlbumDetailChar].Switch)->ActionAdd->anim_chr_no=
			getNewGraphicNo(album.status[selectPcNo][MonsAlbumNo].graNo);
		((ACTION_SWITCH *)wI->sw[EnumActionAlbumDetailChar].Switch)->ActionAdd->anim_ang=MonsAlbumAng;
		//ナンバー
		sprintf( str, "%3d",MonsAlbumNo+1 );										                      //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailNo].Switch)->text,str);
		//Slot数
		sprintf( str, "%2d",album.status[selectPcNo][MonsAlbumNo].maxTech );										 //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailSlot].Switch)->text,str);
		//种类
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailType].Switch)->text,characterTribeStr[album.status[selectPcNo][MonsAlbumNo].tribe]);
		//ページ数
		sprintf( str, "%3d/%3d", MonsAlbumNo+1,NEW_ALBUM_MAX);										    //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailPageNum].Switch)->text,str);
		//封印マーク表示
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailCard00].Switch)->graNo=NewsealGraNo[album.status[selectPcNo][MonsAlbumNo].seal];
		//レアマーク表示
		((GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailCard01].Switch)->graNo=NewrareGraNo[album.status[selectPcNo][MonsAlbumNo].rare];
		//状态の☆表示
		MenuAlbumSetStar(album.status[selectPcNo][MonsAlbumNo].vit,EnumGraphAlbumDetailStarVit00);
		MenuAlbumSetStar(album.status[selectPcNo][MonsAlbumNo].str,EnumGraphAlbumDetailStarStr00);
		MenuAlbumSetStar(album.status[selectPcNo][MonsAlbumNo].tgh,EnumGraphAlbumDetailStarTgh00);
		MenuAlbumSetStar(album.status[selectPcNo][MonsAlbumNo].qui,EnumGraphAlbumDetailStarQui00);
		MenuAlbumSetStar(album.status[selectPcNo][MonsAlbumNo].mgc,EnumGraphAlbumDetailStarMgc00);

		//属性表示
		//地
		for (i=0;i<10;i++)
			wI->sw[EnumGraphAlbumDetailEarth00+i].Enabled=FALSE;
		j=album.status[selectPcNo][MonsAlbumNo].attr[0];
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailEarth00].Switch;
			Graph->graNo=GID_MonsterStatusEarthOnly;
			wI->sw[EnumGraphAlbumDetailEarth00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailEarth00+i].Switch;
				Graph->graNo=GID_MonsterStatusEarthCenter;
				wI->sw[EnumGraphAlbumDetailEarth00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailEarth00+i].Switch;
					Graph->graNo=GID_MonsterStatusEarthLeft;
					wI->sw[EnumGraphAlbumDetailEarth00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailEarth00+i].Switch;
					Graph->graNo=GID_MonsterStatusEarthRight;
					wI->sw[EnumGraphAlbumDetailEarth00+i].Enabled=TRUE;
				}
			}
		}

		//水
		for (i=0;i<10;i++)
			wI->sw[EnumGraphAlbumDetailWater00+i].Enabled=FALSE;
		j=album.status[selectPcNo][MonsAlbumNo].attr[1];
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWater00].Switch;
			Graph->graNo=GID_MonsterStatusWaterOnly;
			wI->sw[EnumGraphAlbumDetailWater00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWater00+i].Switch;
				Graph->graNo=GID_MonsterStatusWaterCenter;
				wI->sw[EnumGraphAlbumDetailWater00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWater00+i].Switch;
					Graph->graNo=GID_MonsterStatusWaterLeft;
					wI->sw[EnumGraphAlbumDetailWater00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWater00+i].Switch;
					Graph->graNo=GID_MonsterStatusWaterRight;
					wI->sw[EnumGraphAlbumDetailWater00+i].Enabled=TRUE;
				}
			}
		}

		//火
		for (i=0;i<10;i++)
			wI->sw[EnumGraphAlbumDetailFire00+i].Enabled=FALSE;
		j=album.status[selectPcNo][MonsAlbumNo].attr[2];
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailFire00].Switch;
			Graph->graNo=GID_MonsterStatusFireOnly;
			wI->sw[EnumGraphAlbumDetailFire00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailFire00+i].Switch;
				Graph->graNo=GID_MonsterStatusFireCenter;
				wI->sw[EnumGraphAlbumDetailFire00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailFire00+i].Switch;
					Graph->graNo=GID_MonsterStatusFireLeft;
					wI->sw[EnumGraphAlbumDetailFire00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailFire00+i].Switch;
					Graph->graNo=GID_MonsterStatusFireRight;
					wI->sw[EnumGraphAlbumDetailFire00+i].Enabled=TRUE;
				}
			}
		}

		//风
		for (i=0;i<10;i++)
			wI->sw[EnumGraphAlbumDetailWind00+i].Enabled=FALSE;
		j=album.status[selectPcNo][MonsAlbumNo].attr[3];
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWind00].Switch;
			Graph->graNo=GID_MonsterStatusWindOnly;
			wI->sw[EnumGraphAlbumDetailWind00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWind00+i].Switch;
				Graph->graNo=GID_MonsterStatusWindCenter;
				wI->sw[EnumGraphAlbumDetailWind00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWind00+i].Switch;
					Graph->graNo=GID_MonsterStatusWindLeft;
					wI->sw[EnumGraphAlbumDetailWind00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphAlbumDetailWind00+i].Switch;
					Graph->graNo=GID_MonsterStatusWindRight;
					wI->sw[EnumGraphAlbumDetailWind00+i].Enabled=TRUE;
				}
			}
		}

	}else{
		//データが无い时
		wI->sw[EnumTextAlbumDetailName].Enabled=FALSE;
		wI->sw[EnumActionAlbumDetailChar].Enabled=FALSE;
		wI->sw[EnumTextAlbumDetailSlot].Enabled=FALSE;
		wI->sw[EnumTextAlbumDetailType].Enabled=FALSE;
		wI->sw[EnumGraphAlbumDetailCard00].Enabled=FALSE;
		wI->sw[EnumGraphAlbumDetailCard01].Enabled=FALSE;
		for(i=0;i<5;i++){
			wI->sw[EnumGraphAlbumDetailStarVit00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailStarStr00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailStarTgh00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailStarQui00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailStarMgc00+i].Enabled=FALSE;
		}
		for(i=0;i<10;i++){
			wI->sw[EnumGraphAlbumDetailEarth00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailWater00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailFire00+i].Enabled=FALSE;
			wI->sw[EnumGraphAlbumDetailWind00+i].Enabled=FALSE;
		}

		//ナンバー
		sprintf( str, "%3d",MonsAlbumNo+1 );										                      //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailNo].Switch)->text,str);
		//ページ数
		sprintf( str, "%3d/%3d", MonsAlbumNo+1,NEW_ALBUM_MAX);										    //MLHIDE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextAlbumDetailPageNum].Switch)->text,str);

	}

}

//状态の☆表示
void MenuAlbumSetStar(int Point,int SwitchNum){

	int i;
	int PointWork;

	for(i=0;i<5;i++){
		//非表示に初期化
		wI->sw[SwitchNum+i].Enabled=FALSE;
	}

	PointWork=Point+1;
	if( PointWork < 1 || 10 < PointWork )
		return;
	
	for(i=0;i<PointWork;i++){
		if(i%2==0){
			//表示にする
			wI->sw[SwitchNum+i/2].Enabled=TRUE;
			((GRAPHIC_SWITCH *)wI->sw[SwitchNum+i/2].Switch)->graNo=GID_AlbumDetailStar;
		}

		if(i==PointWork-1 && (PointWork%2 == 1)){
			//最后奇数なんで☆半分にしなおす
			((GRAPHIC_SWITCH *)wI->sw[SwitchNum+i/2].Switch)->graNo=GID_AlbumDetailStarHalf;
		}
	}	

}


