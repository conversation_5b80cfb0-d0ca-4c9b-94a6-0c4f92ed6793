﻿/***************************************
				ride_pc.cpp
***************************************/

#ifdef PUK3

#ifdef PUK3_RIDE

// ライドキャラの絵取得关数
int getRiderCharaGra( int graNo )
{
	int chara, color;

	// CGキャラ男なら
	if ( SPR_000ax <= graNo && graNo <= SPR_063sp ){
		chara = (graNo - SPR_000ax) / 25;
		color = ( (graNo - SPR_000ax) % 25 ) / 6;
		return SPR_s_000 + 4 * chara + color;
	}
	// CGキャラ女なら
	if ( SPR_200ax <= graNo && graNo <= SPR_263sp ){
		chara = (graNo - SPR_200ax) / 25;
		color = ( (graNo - SPR_200ax) % 25 ) / 6;
		return SPR_s_200 + 4 * chara + color;
	}

	// リニューアルキャラ男なら
	if ( SPR_400ax <= graNo && graNo <= SPR_463sp ){
		chara = (graNo - SPR_400ax) / 25;
		color = ( (graNo - SPR_400ax) % 25 ) / 6;
		return SPR_s_400 + 4 * chara + color;
	}
	// リニューアルキャラ女なら
	if ( SPR_500ax <= graNo && graNo <= SPR_563sp ){
		chara = (graNo - SPR_500ax) / 25;
		color = ( (graNo - SPR_500ax) % 25 ) / 6;
		return SPR_s_500 + 4 * chara + color;
	}

	// PUKキャラ男なら
	if ( SPR_600ax <= graNo && graNo <= SPR_663sp ){
		chara = (graNo - SPR_600ax) / 25;
		color = ( (graNo - SPR_600ax) % 25 ) / 6;
		return SPR_s_600 + 4 * chara + color;
	}
	// PUKキャラ女なら
	if ( SPR_700ax <= graNo && graNo <= SPR_763sp ){
		chara = (graNo - SPR_700ax) / 25;
		color = ( (graNo - SPR_700ax) % 25 ) / 6;
		return SPR_s_700 + 4 * chara + color;
	}
	if ( graNo == SPR_shadow_pcm ){
		return SPR_s_kage_pcm;
	}
	if ( graNo == SPR_shadow_pcf ){
		return SPR_s_kage_pcf;
	}

	return graNo;
}

#endif

#endif