﻿//メニュー＞アイテム

#ifndef _MENUBANK_H_
#define _MENUBANK_H_

// ボタン处理关数 *********************//

// 共通
BOOL MenuBankCloseButton( int no, unsigned int flag );
BOOL MenuBankOKButton( int no, unsigned int flag );

BOOL MenuBankItemButton( int no, unsigned int flag );
BOOL MenuBankMonsterButton( int no, unsigned int flag );
BOOL MenuBankDepositButton( int no, unsigned int flag );
BOOL MenuBankWithdrawButton( int no, unsigned int flag );

BOOL MenuBankTextDraw( int no, unsigned int flag );

// アイテム
BOOL MenuBankMyItemPanel( int no, unsigned int flag );
BOOL MenuBankBankItemPanel( int no, unsigned int flag );

// モンスター
BOOL MenuSwitchBankScrollUp( int no, unsigned int flag );
BOOL MenuSwitchBankScrollDown( int no, unsigned int flag );
BOOL MenuSwitchBankScrollWheel( int no, unsigned int flag );

BOOL MenuBankMyMonsterPanel( int no, unsigned int flag );
BOOL MenuBankBankMonsterPanel( int no, unsigned int flag );

// お金
BOOL MenuBankSwitchCalculator( int no, unsigned int flag );

BOOL MenuBankGetKeyForcus( int no, unsigned int flag );


BOOL MenuWindowBankBf( int mouse );
BOOL MenuWindowBankAf( int mouse );
BOOL closeBankWindow();


GRAPHIC_SWITCH MenuWindowBankGraph[]={
	{GID_BankBase,0,0,0,0,0xFFFFFFFF},			// ベース

// 共通
	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},		// ＯＫボタン

	{GID_ItemChgOn,0,0,0,0,0xFFFFFFFF},			// アイテムボタン
	{GID_MonsterChgOn,0,0,0,0,0xFFFFFFFF},		// モンスターボタン
	{GID_MoneyChgOn,0,0,0,0,0xFFFFFFFF},		// お金ボタン

	{GID_My_Item,0,0,0,0,0xFFFFFFFF},			// 自分侧パネル
	{GID_BankItemPanel,0,0,0,0,0xFFFFFFFF},		// 银行侧パネル

// アイテム

// モンスター
	{GID_MonsterPanel,0,0,0,0,0xFFFFFFFF},		// モンスターパネル

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},			// スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},		// スクロールバー(下ボタン)

// お金

	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},		// クローズボタン
};

BUTTON_SWITCH MenuWindowBankButton[]={ {0,0},{0,0} };

TEXT_SWITCH MenuWindowBankText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,""},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,""},
	{FONT_PAL_WHITE,FONT_KIND_SIZE_11,""},
};

char BankNum[2][11];

NUMBER_SWITCH MenuWindowBankNum[2] = {
	{ BankNum[0], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ BankNum[1], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
};

// スイッチ
static SWITCH_DATA BankSwitch[] = {

{ SWITCH_DIALOG,   0,  0, 567,339, TRUE, NULL, MenuBankGetKeyForcus },								// キーフォーカス取得用假入力栏

// 共通
{ SWITCH_GRAPHIC,547,  9,  11, 11, TRUE, &MenuWindowBankGraph[11], MenuBankCloseButton },			// クローズボタン
{ SWITCH_GRAPHIC,476,308,  66, 17, TRUE, &MenuWindowBankGraph[1], MenuBankOKButton },				// ＯＫボタン

{ SWITCH_GRAPHIC,298,264,  80, 15, TRUE, &MenuWindowBankGraph[2], MenuBankItemButton },				// アイテム
{ SWITCH_GRAPHIC,383,264,  80, 15, TRUE, &MenuWindowBankGraph[3], MenuBankMonsterButton },			// モンスター
{ SWITCH_GRAPHIC,469,264,  80, 15, TRUE, &MenuWindowBankGraph[4], MenuBankDepositButton },			// 入金

{ SWITCH_GRAPHIC, 26,264,  80, 15, TRUE, &MenuWindowBankGraph[2], MenuBankItemButton },				// アイテム
{ SWITCH_GRAPHIC,111,264,  80, 15, TRUE, &MenuWindowBankGraph[3], MenuBankMonsterButton },			// モンスター
{ SWITCH_GRAPHIC,197,264,  80, 15, TRUE, &MenuWindowBankGraph[4], MenuBankWithdrawButton },			// 出金

{ SWITCH_TEXT,   478,328,  80, 15, TRUE, &MenuWindowBankText[2], MenuSwitchNone },					// 手续费

// アイテム

// モンスター
{ SWITCH_GRAPHIC,297, 53, 240, 69, TRUE, &MenuWindowBankGraph[7], MenuBankMyMonsterPanel },			// 自分侧モンスターパネル１
{ SWITCH_GRAPHIC,297,122, 240, 69, TRUE, &MenuWindowBankGraph[7], MenuBankMyMonsterPanel },			// 自分侧モンスターパネル２
{ SWITCH_GRAPHIC,297,191, 240, 69, TRUE, &MenuWindowBankGraph[7], MenuBankMyMonsterPanel },			// 自分侧モンスターパネル３

{ SWITCH_GRAPHIC, 25, 53, 240, 69, TRUE, &MenuWindowBankGraph[7], MenuBankBankMonsterPanel },		// 银行侧モンスターパネル１
{ SWITCH_GRAPHIC, 25,122, 240, 69, TRUE, &MenuWindowBankGraph[7], MenuBankBankMonsterPanel },		// 银行侧モンスターパネル２
{ SWITCH_GRAPHIC, 25,191, 240, 69, TRUE, &MenuWindowBankGraph[7], MenuBankBankMonsterPanel },		// 银行侧モンスターパネル３

{ SWITCH_GRAPHIC,538, 65,   0, 14, TRUE, &MenuWindowBankGraph[8], MenuSwitchNone },					// スクロールバー(つまみ)
{ SWITCH_BUTTON, 538, 65,  11,183, TRUE, &MenuWindowBankButton[0], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,538, 55,  11, 11, TRUE, &MenuWindowBankGraph[9], MenuSwitchBankScrollUp },			// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,538,247,  11, 11, TRUE, &MenuWindowBankGraph[10], MenuSwitchBankScrollDown },		// スクロールバー(下ボタン)
{ SWITCH_NONE,	 286, 27, 273,280, TRUE, NULL, MenuSwitchBankScrollWheel },							// 自分侧パネルマウスホイール判定

{ SWITCH_GRAPHIC,266, 65,   0, 14, TRUE, &MenuWindowBankGraph[8], MenuSwitchNone },					// スクロールバー(つまみ)
{ SWITCH_BUTTON, 266, 65,  11,183, TRUE, &MenuWindowBankButton[1], MenuSwitchScrollBarV },			// スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,266, 55,  11, 11, TRUE, &MenuWindowBankGraph[9], MenuSwitchBankScrollUp },			// スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,266,247,  11, 11, TRUE, &MenuWindowBankGraph[10], MenuSwitchBankScrollDown },		// スクロールバー(下ボタン)
{ SWITCH_NONE, 	  14, 27, 273,280, TRUE, NULL, MenuSwitchBankScrollWheel },							// 银行侧パネルマウスホイール判定

// お金
{ SWITCH_NONE,   375,135, 106, 85, TRUE, NULL, MenuBankSwitchCalculator },							// 电卓コントロール(入金用)
{ SWITCH_NONE,   103,135, 106, 85, TRUE, NULL, MenuBankSwitchCalculator },							// 电卓コントロール(出金用)

{ SWITCH_NUMBER, 465,115,   0,  0, TRUE, &MenuWindowBankNum[0], MenuSwitchNone },					// 电卓表示部(入金用)
{ SWITCH_NUMBER, 193,115,   0,  0, TRUE, &MenuWindowBankNum[1], MenuSwitchNone },					// 电卓表示部(出金用)

{ SWITCH_GRAPHIC,286, 27, 273,280, TRUE, &MenuWindowBankGraph[5], MenuBankMyItemPanel },			// 自分侧パネル
{ SWITCH_GRAPHIC, 14, 27, 273,280, TRUE, &MenuWindowBankGraph[6], MenuBankBankItemPanel },			// 银行侧パネル

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowBankGraph[0], MenuBankTextDraw },				// ベース

{ SWITCH_NONE,	  571, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },								// ドラッグ用

};

enum{
	EnumBankGetKeyForcus,

	EnumBankBtClose,
	EnumBankBtOk,

// 共通
	EnumBankBtItem,
	EnumBankBtMonster,
	EnumBankBtDeposit,

	EnumBankBtItem2,
	EnumBankBtMonster2,
	EnumBankBtWithdraw,

	EnumBankChargesText,

	// アイテム

	// モンスター
	EnumBankMyMonster1Panel,
	EnumBankMyMonster2Panel,
	EnumBankMyMonster3Panel,

	EnumBankBankMonster1Panel,
	EnumBankBankMonster2Panel,
	EnumBankBankMonster3Panel,

	EnumBankMyMonsterScrollGra,
	EnumBankMyMonsterScroll,
	EnumBankMyMonsterScrollUp,
	EnumBankMyMonsterScrollDown,
	EnumBankMyMonsterScrollWheel,

	EnumBankBankMonsterScrollGra,
	EnumBankBankMonsterScroll,
	EnumBankBankMonsterScrollUp,
	EnumBankBankMonsterScrollWheel,
	EnumBankBankMonsterScrollDown,

	// お金
	EnumBankBankCalculatorD,
	EnumBankBankCalculatorW,

	EnumBankBankMyGoldText,
	EnumBankBankBankGoldText,

	EnumBankMyPanel,
	EnumBankBankPanel,

	EnumBankWindow,

	EnumBankDragBack,

	EnumBankEnd,
};

// ウィンドウ管理构造体
struct BANKWINDOWMASTER{
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 

	int WinType;							// ウィンドウの种类
	int SeqNo;								// サーバの管理番号
	int ObjIndex;							// 现在の处理サーバの管理番号

	int itemInfoNo;
	int itemInfoPage;

	int MonsterStart[2];
};

const WINDOW_DATA WindowDataMenuBank = {
 0,															// メニューウィンドウ
     4,   20, 25,567,339, 0x80010101,  EnumBankEnd,  BankSwitch, MenuWindowBankBf,MenuWindowBankAf,closeBankWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateBank={
	2,
	 9,  0,558, 27,
	 571, 7,18,  78,
};

struct NEW_BANKMONSTER{
	short useFlag;						// この栏にペットがいるかフラグ
	short tribe;						// 种族
	int graNo;							// 画像番号
	int lv;								// 等级
	int lp, maxLp;						// 体力／最大体力
	int fp, maxFp;						// 魔力／最大魔力
	// 基本能力
	int vit;							// 体力(Vit)
	int str;							// 攻击力(Str)
	int tgh;							// 防御力(Tgh)
	int qui;							// 敏捷力(Qui)
	int mgc;							// 魔法力(Mgc)

	// 现在能力值
	int atk;							// 攻击值(Atk)
	int def;							// 防御值(Def)
	int agi;							// 敏捷值(Agi)
	int mnd;							// 精神值(Mnd)
	int rcv;							// 回复值(Rcv)

	int hmg;							// 忠诚值(Hmd)

	// 属性值
	int attr[4];						// 0 ... 地属性
										// 1 ... 水属性
										// 2 ... 火属性
										// 3 ... 风属性

	int maxTech;						// 最大技数

	PET_TECH_INFO tech[MAX_PET_TECH];	// 技

	char name[CHAR_NAME_LEN+1];			// 种类名
	char defname[CHAR_NAME_LEN+1];		// 种类名
};
extern struct NEW_BANKMONSTER bankMonster[5];

ACTION *openBankMenuWindow( int WinType, int SeqNo, int ObjIndex, unsigned char flg, char opentype );
BOOL BankDataRenewal( char *data );
void ItemMove( int WinType, int ButtonNo, int from, int to );

enum{
	BankType_Nomal = 0,
	BankType_Guild,
};

#endif