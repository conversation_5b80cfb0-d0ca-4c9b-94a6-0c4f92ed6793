﻿#include <winsock.h>
#include <time.h>
#include "../systeminc/version.h"
#include "../systeminc/system.h"
#include "../systeminc/netmain.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/process.h"
#include "../systeminc/debugLogWin.h"
#include "../systeminc/debug.h"

//#define NO_SELECT

// recv 时のエラーチェック
int CheckRecvErrror( void );
int CheckSendErrror( void );

//-------------------------------------------------------------------------//
// 变数定义                                                                //
//-------------------------------------------------------------------------//
unsigned int sockfd;				// SOCKET は unsigned int。
char networkFlag = 0;				// ネットワークが使用可能かを示す。(WSAStartupしたかどうか)
									// 1 ... 使用可能 / 0 ... 使用不可
char networkServerChooseFlag = 0;	// サーバを选择したかのフラグ
									// 1 ... サーバ决定 / 0 ... 未决定

char networkWriteBuffer[NETBUFSIZ];	// ネットワーク送信データ书き込みバッファ
char networkReadBuffer[NETBUFSIZ];	//      〃     受信データ书き込みバッファ
int  networkWriteBufferLen;			// 送信バッファのデータサイズ
int  networkReadBufferLen;			// 受信バッファのデータサイズ

char rpcLineBuffer[32768];			//rpc用


char networkDisconnectFlag = 0;		// 途中で接続が途切れたかのフラグ
#ifdef PUK3_CONNECTERROR
	int networkDisconnectErrorNum = 0;	// 途中で接続が切れたときのエラー番号
#endif


#ifdef VERSION_TW
#define SERVER_MAX	800
	int MainServerLineNum[ MAINSERVER_MAX ] = { 0 };;	//主服务器中开启的线路数量
#else
#define SERVER_MAX	12
#endif

struct gameserver gmsv[ SERVER_MAX ];
int serverMaxNo;


#ifdef _LOG_MSG
#ifdef PUK3_RECVDATA
	char debugLogFileName_base[256] = "recvdata_%d.txt";	// デバッグ用ログファイル名テンプレート //MLHIDE
	char debugLogFileName[256];	// デバッグ用（送受信データログファイル）
#else
char debugLogFileName[256] = "recvdata.txt";	// デバッグ用（送受信データログファイル）   //MLHIDE
#endif
#endif




//-------------------------------------------------------------------------//
// 关数                                                                    //
//-------------------------------------------------------------------------//

//-------------------------------------------------------------------------//
// コマンドラインのIPアドレス设定解析                                      //
//-------------------------------------------------------------------------//
void ipAddressAnalyze( LPSTR CmdLine )
{
	int id;
	char *ipAdr;
	int i;

	memset( &gmsv, 0, sizeof( gmsv ) );

	serverMaxNo = -1;
	ipAdr = CmdLine;
	while( 1 )
	{
		if( (ipAdr = strstr( ipAdr, "IP:" )) == NULL )                      //MLHIDE
			break;

		ipAdr += 3;
		// ＩＤ読み込み
		sscanf( ipAdr, "%d:", &id );                                        //MLHIDE
		//id = *ipAdr - '0';	// ohta
		
#ifdef VERSION_TW
		if ( id >= 800 )
			break;

		// アドレス进める
		if (id < 10) 
			ipAdr += 2;
		else if ( id < 100)
			ipAdr += 3;
		else
			ipAdr += 4;
#else
		//if( id < 0 || sizeof( gmsv )/sizeof( gmsv[0] ) <= id )
		if( id < 0 || SERVER_MAX <= id )
			return;

		if( id > serverMaxNo )
			serverMaxNo = id;

		// アドレス进める
		if( id < 10 ) ipAdr += 2;
		else ipAdr += 3;
#endif
		i = 0;
		while( *ipAdr != '\0' && *ipAdr != ':' )
		{
			if( i >= sizeof( gmsv[0].ipaddr )-1 )
				break;

			gmsv[id].ipaddr[i] = *ipAdr++;
			i++;
		}
		gmsv[id].ipaddr[i] = '\0';

		ipAdr++;
		i = 0;
		while( *ipAdr != '\0' && *ipAdr != ' ' && *ipAdr != '\t' )
		{
			if( i >= sizeof( gmsv[0].port )-1 )
				break;

			gmsv[id].port[i] = *ipAdr++;
			i++;
		}
		gmsv[id].port[i] = '\0';

		gmsv[id].useFlag = 1;

#ifdef VERSION_TW
		//获取主服务器中开启的线路数量
		++MainServerLineNum[id / 20];
#endif
	}
}


//-------------------------------------------------------------------------//
// サーバ数を返す                                                          //
//-------------------------------------------------------------------------//
int getServerMax( void )
{
	return sizeof( gmsv )/sizeof( gmsv[0] );
}


//-------------------------------------------------------------------------//
// ゲームサーバーの情报を得る。                                            //
//-------------------------------------------------------------------------//
//  引　数: id       ... 入力。この数值は、0 からgetServerMaxの值-1までである。
//          hostname ... IPアドレス
//          port     ... ポート番号
//  戾り值:	 0       ... 成功
//			-1       ... 失败
//
//  この关数はI/Oをしない
int getServerInfo( int id, char *hostname, short *port )
{
	if( id < 0 || id >= sizeof( gmsv )/sizeof( gmsv[0] ) )
		return -1;

	strcpy( hostname, gmsv[id].ipaddr );
	*port = atoi( gmsv[id].port );

	return 0;
}


// リードエラーを连続で起こした回数
static int giReadErrorCount = 0;

static int skip_cnt = 0;
//-------------------------------------------------------------------------//
// ネットワーク处理                                                        //
//   每ループ实行される                                                    //
//-------------------------------------------------------------------------//
void networkLoop( void )
{
	static unsigned int lastSendTime = GetTickCount();	// 最后に送信した时间

	// 初期化終わってないなら終わる
	if( networkFlag == 0 )
		return;

	// ネットワークが切断されたら終わる
	if( networkDisconnectFlag == 1 )
	{
		ChangeProc( PROC_DISCONNECT_SERVER );
		networkDisconnectFlag = 2;
	}
	if( networkDisconnectFlag )
		return;

	// サーバが选择されてなければ終わる
    if( networkServerChooseFlag == 0 )
    	return;

	skip_cnt++;
	skip_cnt &= 7;
	if( skip_cnt ){
    	return;
	}

#ifndef NO_SELECT
	fd_set rfds, wfds;
	struct timeval tm;

	tm.tv_sec = 0;
	tm.tv_usec = 0;

	FD_ZERO( &rfds );
	FD_ZERO( &wfds );

	FD_SET( sockfd , &rfds );
	FD_SET( sockfd , &wfds );

	select( 2, &rfds, &wfds, (fd_set*)NULL, &tm );

	// 受信する时
	if( FD_ISSET( sockfd, &rfds ) )
#endif
	{
		char buf[4096];
		int len;

        memset( buf, 0, sizeof( buf ) );
		len = recv( sockfd, buf, sizeof( buf )-1, 0 );

#if 0
		{	// 受信した时间を书き込む
			FILE *fp = fopen( "recvtime.txt", "a+" );                          //MLHIDE
			if( fp )
				fprintf( fp, "%u RECV %d\n", GetTickCount(), len );               //MLHIDE
			if( fp )
				fclose( fp );
		}
#endif
		if( len > 0 ){
			// エラーカウント消す。
			giReadErrorCount = 0;
			appendReadBuf( buf, len );
		}else
		if( len == 0 ){
/*
			// システムログファイル书き込み
			WriteSystemLogfile( "Close By Server" );
			closesocket( sockfd );
			// サーバーとの接続が途切れたことを表示する
			networkDisconnectFlag = 1;
			// エラーカウントを消す。
			giReadErrorCount = 0;
*/
		}else
		if( len == SOCKET_ERROR )
		{	
#ifdef _DEBUG
			CheckNetErrror();
#endif
#ifdef PUK3_CONNECTERROR
			int ret = CheckRecvErrror();
			// エラーにして良いか？
			if( ret > 0 ){
				closesocket( sockfd );
				// サーバーとの接続が途切れたことを表示する
				networkDisconnectFlag = 1;
				networkDisconnectErrorNum = ret;
			}
#else
			// エラーにして良いか？
			if( CheckRecvErrror() > 0 ){
				closesocket( sockfd );
				// サーバーとの接続が途切れたことを表示する
				networkDisconnectFlag = 1;
			}
#endif
		}
		else
		{
		}
	}

	while( 1 )
	{
		if( networkReadBufferLen > 0 )
		{
			// ここで１行分のデータがきているかどうかしらべて、
			// LSRPCで１行の呼び出しをするのだ。
			int r = getLineFromReadBuf( rpcLineBuffer, sizeof( rpcLineBuffer ) );
			if( r == 0 )
			{
				nrproto_ClientDispatchMessage( sockfd , rpcLineBuffer );
#ifdef PUK3_MEMORYLEAK
	MemCheck();			// メモリのチェック关数
#endif
			}
			else
			{
				break;
			}
		}
		else
		{
			break;
		}
	}

	if( networkWriteBufferLen > 0  && networkDisconnectFlag == 0 )
	// 送信する时
//	if( FD_ISSET( sockfd, &wfds ) && networkDisconnectFlag == 0  )
	{
		int len;

		len = send( sockfd, networkWriteBuffer, networkWriteBufferLen, 0 );
		if( len > 0 )
		{
			lastSendTime = GetTickCount();
		}
		if( len == SOCKET_ERROR )
		{
#ifdef _DEBUG
			CheckNetErrror();
#endif
#ifdef PUK3_CONNECTERROR
			int ret = CheckSendErrror();
			// エラーにして良いか？
			if( ret > 0 ){
				closesocket(sockfd);
				// サーバーとの接続が途切れたことを表示する
				networkDisconnectFlag = 1;
				networkDisconnectErrorNum = ret;
			}
#else
			// エラーにして良いか？
			if( CheckSendErrror() > 0 ){
				closesocket(sockfd);
				// サーバーとの接続が途切れたことを表示する
				networkDisconnectFlag = 1;
			}
#endif
		}
		else
		{
			shiftWriteBuf( len );
		}
	}
	if( lastSendTime + NEXT_SEND_TIME < GetTickCount() )
	{
		if(	networkFlag == 1 )
		{ 
			nrproto_Echo_send( sockfd, "nr" );                                 //MLHIDE

#ifdef _DEBUG
			testCnt++;
#endif
#if 0
			{
				FILE *fp;
				if( (fp = fopen( "debug.txt", "w" )) != NULL )                    //MLHIDE
				{
					fprintf( fp, "%ld\n", GetTickCount() );                          //MLHIDE
					fclose( fp );
				}
			}
#endif
		}
	}

#ifdef _DEBUG
#if 0
	// 永久にWebにアクセスしつづける。
	if( do_http_request_forever( "203.182.168.20", 80,                   //MLHIDE
		//"/~stoneage/newest.txt",180 ) < 0 )
		//"/~stoneage/newest.txt",60 ) < 0 )
		//"/~stoneage/newest.txt",10 ) < 0 )
		//"/~stoneage/slfjfjlsf",180 ) < 0 )
		//"/~stoneage/slfjfjlsf",60 ) < 0 )
		"/~stoneage/slfjfjlsf",10 ) < 0 )                                   //MLHIDE
	{
		MessageBox( hWnd, "Ｗｅｂ连接失败", "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
	}
#endif
#endif
}




//-------------------------------------------------------------------------//
// StoneAgeクライアント用ネットワーク基本ルーチン                          //
//                                                                         //
//  入力、出力バッファリング、非同期、ということから、                     //
//  バッファー付きselect入出力をまたしても使うことになる。                 //
//  ゲームサーバーに关してはこれでよい。                                   //
//  それとはまったく别に、マルチスレッドでインターネットから               //
//  HTTPでファイルをとってくる手段を用意する。                             //
//                                                                         //
//  ネットワークを使えるようにする方法                                     //
//                                                                         //
//    1. 最初のほうでinitNet()する                                         //
//    2. 每ループnetworkLoopするようにループの部分に书く                   //
//    3. gmsv 构造体の配列にゲームサーバーの情报を书きこむ                 //
//    4. 以上で、ネットワークが使えるようになっている                      //
//                                                                         //
//-------------------------------------------------------------------------//


//-------------------------------------------------------------------------//
// ネットワーク初期化                                                      //
//-------------------------------------------------------------------------//
BOOL initNet( void )
{
	int ret;
	WSADATA wsadata;

	// winsockを初期化する
    ret = WSAStartup( MAKEWORD( 1, 1 ), &wsadata );
	if( ret != 0 )
		return FALSE;

	// nrprotoの初期化
	nrproto_InitClient( appendWriteBuf, 65536, sockfd );

	networkFlag = 1;
	networkDisconnectFlag = 0;

#ifdef _RELDEB

	#ifdef _LOG_MSG
		// ログファイルを空にする
		clearLogFile( debugLogFileName );
	#endif
	nrproto_SetLogCallback( updateLogMessage );

#else
//TRUE
	#ifdef _DEBUG
		#ifdef _LOG_MSG
			// ログファイルを空にする
			clearLogFile( debugLogFileName );
		#endif
		nrproto_SetLogCallback( updateLogMessage );
	#endif

#endif

	return TRUE;
}


//-------------------------------------------------------------------------//
// ネットワーク終了（アプリ終了のとき用）                                  //
//-------------------------------------------------------------------------//
void cleanupNetwork( void )
{
	if( networkFlag == 0 )
		return;

	networkFlag = 0;
	networkServerChooseFlag = 0;

	networkDisconnectFlag = 0;

	closesocket( sockfd );
	WSACleanup();

	// lsrpcのクリーンアップ关数が必要だがまだないのだ。
	// ネットワークバッファはグローバルでもってるからいいのだ。
	nrproto_CleanupClient();
}


//-------------------------------------------------------------------------//
// リードバッファにデータを追加                                            //
//                                                                         //
//  戾り值：	   0 ... 成功                                              //
//				  -1 ... バッファが足りない                                //
//				-100 ... サーバを选择していない                            //
//                                                                         //
//-------------------------------------------------------------------------//
int appendReadBuf( char *buf, int size )
{
	if( networkServerChooseFlag == 0 )
		return -100;

	if( (networkReadBufferLen + size) > NETBUFSIZ )
		return -1;

	memcpy( networkReadBuffer + networkReadBufferLen, buf, size );
	networkReadBufferLen += size;

	return 0;
}


//-------------------------------------------------------------------------//
// ライトバッファにデータを追加                                            //
//                                                                         //
//  戾り值：	   0 ... 成功                                              //
//				  -1 ... バッファが足りない                                //
//				-100 ... サーバを选择していない                            //
//                                                                         //
//-------------------------------------------------------------------------//
int appendWriteBuf( int index, char *buf, int size )
{
	if( networkServerChooseFlag == 0 )
		return -100;

	if( (networkWriteBufferLen + size) > NETBUFSIZ )
		return -1;

	memcpy( networkWriteBuffer + networkWriteBufferLen, buf, size );
	networkWriteBufferLen += size;

	return 0;
}


//-------------------------------------------------------------------------//
// リードバッファのデータをsizeバイト分前にシフト                          //
// （最初からsizeバイト分のデータが消される）                              //
//                                                                         //
//  戾り值：	   0 ... 成功                                              //
//				  -1 ... バッファを超えている                              //
//				-100 ... サーバ选择されてない                              //
//                                                                         //
//-------------------------------------------------------------------------//
int shiftReadBuf( int size )
{
//	int i;

	if( networkServerChooseFlag == 0 )
		return -100;

	if( size >= NETBUFSIZ )
		return -1;

//	for( i = size; i < NETBUFSIZ; i++ )
//	{
//		networkReadBuffer[i-size] = networkReadBuffer[i];
//	}
	networkReadBufferLen -= size;
	memmove( networkReadBuffer, &networkReadBuffer[size],  networkReadBufferLen);

	return 0;
}


//-------------------------------------------------------------------------//
// ライトバッファのデータをsizeバイト分前にシフト                          //
// （最初からsizeバイト分のデータが消される）                              //
//                                                                         //
//  戾り值：	   0 ... 成功                                              //
//				  -1 ... バッファを超えている                              //
//				-100 ... サーバ选择されてない                              //
//                                                                         //
//-------------------------------------------------------------------------//
int shiftWriteBuf( int size )
{
//	int i;

	if( networkServerChooseFlag == 0 )
		return -100;

	if( size >= NETBUFSIZ )
		return -1;

//	for( i = size; i < NETBUFSIZ; i++ )
//	{
//		networkWriteBuffer[i-size] = networkWriteBuffer[i];
//	}
	networkWriteBufferLen -= size;
	memmove( networkWriteBuffer, &networkWriteBuffer[size], networkWriteBufferLen);

	return 0;
}


//-------------------------------------------------------------------------//
// リードバッファから１行（改行文字までの文字列）を取り出す                //
//                                                                         //
//  戾り值：	   0 ... 成功                                              //
//				  -1 ... １行分データが无い                                //
//				-100 ... サーバが选择されてない                            //
//                                                                         //
//-------------------------------------------------------------------------//
int getLineFromReadBuf( char *output, int maxlen )
{
	int i, j;

	if( networkServerChooseFlag == 0 )
		return -100;

	for( i = 0; i < networkReadBufferLen && i < (maxlen-1); i++ )
	{
		if( networkReadBuffer[i] == '\n' )
		{
			memcpy( output, networkReadBuffer, i );
			output[i] = '\0';
			// 出力を调整。文字列の最后から检索して 0x0dがあったら消す
			for( j = i+1; j >= 0; j-- )
			{
				if( output[j] == 0x0d )
				{
					output[j] = '\0';
					break;
				}
			}

			// 消费した分シフトする
			shiftReadBuf( i+1 );

			// バッファの外侧に１バイト、ヌル文字をつける。
			// シフト操作の时はバッファの后ろは破坏されないという仕样に基づいている。
			networkReadBuffer[networkReadBufferLen] = '\0';

			return 0;
		}
	}

	return -1;
}


//-------------------------------------------------------------------------//
// ソケットに确实にlenバイト送信する                                       //
//                                                                         //
//  戾り值：	0以上 ... 送信したバイト数                                 //
//				 -100 ... サーバが选择されていない                         //
//                                                                         //
//-------------------------------------------------------------------------//
int sendn( SOCKET s, char *buffer, int len )
{
	int total = 0;
	int r;

	if( networkServerChooseFlag == 0 )
		return -100;

	while( 1 )
	{
		r = send( s, buffer, len, 0 );
		if( r == SOCKET_ERROR )
			return  SOCKET_ERROR;
		total += r;
		if( total == len )
			return total;
	}
}



//-------------------------------------------------------------------------//
// recv 时のエラー处理を调べる。
//-------------------------------------------------------------------------//
int CheckRecvErrror( void )
{
	int error;
	char szBuffer[256];
	// 直前のエラーを取得。
	error = WSAGetLastError();
	// ログに吐き出す。
	sprintf( szBuffer, "RECV ERROR=%d", error );                         //MLHIDE
	WriteSystemLogfile( szBuffer );

	// 何のエラーか判定
	switch ( error )
	{
		// 非ブロックモードでは発生してしまうらしい。
		case WSAEWOULDBLOCK:	
			return 0;
	}
	return error;
}

//-------------------------------------------------------------------------//
// recv 时のエラー处理を调べる。
//-------------------------------------------------------------------------//
int CheckSendErrror( void )
{
	int error;
	char szBuffer[256];
	// 直前のエラーを取得。
	error = WSAGetLastError();
	// ログに吐き出す。
	sprintf( szBuffer, "SNED ERROR=%d", error );                         //MLHIDE
	WriteSystemLogfile( szBuffer );

	// 何のエラーか判定
	switch ( error )
	{
		// 非ブロックモードでは発生してしまうらしい。
		case WSAEWOULDBLOCK:	
			return 0;
	}
	return error;
}

#ifdef _DEBUG
//-------------------------------------------------------------------------//
// デバッグ用                                                              //
//-------------------------------------------------------------------------//
int CheckNetErrror( void )
{
	int error = WSAGetLastError();

	// 何のエラーか判定
	switch ( error )
	{
		case WSANOTINITIALISED:
			return error;
		case WSAENETDOWN:
			return error;
		case WSAEACCES:
			return error;
		case WSAEINPROGRESS:
			return error;
		case WSAEFAULT:
			return error;
		case WSAENETRESET:
			return error;
		case WSAENOBUFS:
			return error;
		case WSAENOTCONN:
			return error;
		case WSAENOTSOCK:
			return error;
		case WSAEOPNOTSUPP:
			return error;
		case WSAESHUTDOWN:
			return error;
		case WSAEWOULDBLOCK:
			return error;
		case WSAEMSGSIZE:
			return error;
		case WSAEINVAL:
			return error;
		case WSAECONNABORTED:
			return error;
		case WSAECONNRESET:
			return error;
		case WSAETIMEDOUT:
			return error;
		default:
			return 0;
	}

	return error;
}
#endif

#ifdef _DEBUG
/*
  永久にWebにアクセスしつづける。
  
  do_http_request_forever()

  static な变数をつかうので、スレッド安全ではありません。

  char *ip : IPアドレス(ドット表记の数字のみ。名称は遅いので引きません)
  unsigned short port : ポート番号。ふつうは80。
  char *obj : ダウンロードするオブジェクト
  int sec : 何秒おきにやるか

  かえりち： 0以上なら正常終了。负ならなにかエラー
 */
static int http_sock;
static int http_call_counter = 0;
static time_t http_call_last_time;
static int http_connecting = 0;
static int http_sent_request = 0;

int
do_http_request_forever( char *ip, unsigned short port,
                         char *obj, int sec )
{
    fd_set rfds,wfds;
    int r;
    struct timeval tm;

    /* ゲームの处理に影响をあたえないよに、
       1秒に1回よばれるようにする */
    if( http_call_last_time != time(NULL )){
        http_call_last_time = time(NULL);
    } else {
        return 0;
    }

    //fprintf(stderr,".");
    
    if( http_call_counter == 0 ){
        struct sockaddr_in sin;

        /* sec秒に1回connectする */
        if( time(NULL) % sec != 0 )return 0;
        
        http_sock = socket( AF_INET, SOCK_STREAM, 0 );
        if( http_sock < 0 )return -1;

		unsigned long flg = 1;
		ioctlsocket( http_sock, FIONBIO, &flg );
#if 0
        fprintf(stderr,"socket()\n" );                                //MLHIDE
        flags = fcntl( http_sock, F_GETFL,0);
        if( fcntl( http_sock, F_SETFL, flags|O_NONBLOCK )< 0){
            close( http_sock );
            return -2;
        }
#endif
        memset( &sin, 0 , sizeof( sin ));
        sin.sin_addr.s_addr = inet_addr( ip );
        sin.sin_port = htons( port );
        sin.sin_family = AF_INET;
        
        if( connect( http_sock, (struct sockaddr*)&sin,
                     sizeof(sin))== SOCKET_ERROR ){
	
			if( WSAGetLastError() == WSAEWOULDBLOCK )
			{
				// 本来はブロックするはずだったんやけど
            }
			else
			{
				closesocket( http_sock );
				return -5;
			}
		}

        http_call_counter ++;
        http_sent_request = 0;
        //fprintf(stderr,"connected\n" );
        return 0;
    }

    FD_ZERO( &rfds );
    FD_SET( http_sock, &rfds );
    FD_ZERO( &wfds );
    FD_SET( http_sock, &wfds );    

    tm.tv_usec = tm.tv_sec = 0;
    r = select( http_sock+1, &rfds, &wfds,(fd_set*)NULL,&tm);

    if( r > 0 && FD_ISSET( http_sock, &rfds ) ){
        char buf[1000];
        r = recv( http_sock, buf,sizeof(buf), 0);
        if( r <= 0 ){
            closesocket( http_sock );
            /* 最初からやりなおし */
            http_call_counter = 0;
        }
        //fprintf(stderr,"read %d\n",r );        
    }
    
    if( r > 0 && FD_ISSET( http_sock, &wfds ) && http_sent_request == 0 ){
        /* HTTPの要求はMSSよりも非常に小さいので、
           1回のwriteでうまくいくと假定している。
           よくないコードだが、よくない机能を实现しようとしているので
           いいのだ！ */
        int r;
        char fuck[1000];
        sprintf( fuck, "GET %s HTTP/1.0\r\n\r\n" , obj );             //MLHIDE

        r = send( http_sock, fuck, strlen( fuck ), 0 );

        if( r <= 0 ){
            closesocket(r );
            http_call_counter = 0;
            return -10;
        }
        //fprintf(stderr,"wrote %d\n",r );
        http_sent_request = 1;
    }
    return 0;
}
#endif










