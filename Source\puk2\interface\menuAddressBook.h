﻿#ifndef __MENUADDRESSBOOK_H__
#define __MENUADDRESSBOOK_H__

#include "../systeminc/guild.h"

ADDRESS_BOOK_SORT_TBL MenuAddressBookSortTable[GUILD_MEMBER_MAX];
#ifdef PUK3_MAIL_ETC
	void AddressBookInit();
#endif

BOOL MenuAddressBookButton (int no, unsigned int flag);
BOOL MenuAddressBookDelete (int no, unsigned int flag);
BOOL MenuAddressBookTypeChange (int no, unsigned int flag);
BOOL MenuAddressBookHistory (int no, unsigned int flag);
BOOL MenuAddressBookGuildSettingOpen (int no, unsigned int flag);
BOOL MenuAddressBookScrollOneLine (int no, unsigned int flag);
BOOL MenuAddressBookGroupMail (int no, unsigned int flag);
BOOL MenuAddressBookChangeTitle (int no, unsigned int flag);
void MenuWindowAddressBookSortFromVal (int mode);
BOOL MenuAddressBookListPanel (int no, unsigned int flag);
BOOL MenuAddressBookScrollWheel (int no, unsigned int flag);

//杉山
BOOL MenuAddressBookSendSwitch (int no, unsigned int flag);

BOOL MenuWindowChangeGuildTitle (int);
BOOL MenuWindowChangeGuildTitleDraw (int);
BOOL MenuWindowChangeGuildTitleDel(void);
BOOL MenudChangeGuildTitleSelect (int no, unsigned int flag);
BOOL MenudChangeGuildTitleClose (int no, unsigned int flag);
BOOL MenudChangeGuildTitleSet (int no, unsigned int flag);
BOOL MenudChangeGuildTitleScrollTopDown (int no, unsigned int flag);
BOOL MenuChangeGuildTitleScrollWheel( int no, unsigned int flag );
BOOL MenuChangeGuildTitleScrollWheel( int no, unsigned int flag );
static int MenuAddressBookGetTargetNumberFromPanel (int no);
void ChangeBookType(int ViewType,int BookType);
void ChangeBookTypeInit(int type);
void ChangeViewModeInit(int mode);
BOOL MenuAddressBookDeleteFromMiniMailMember (int no, unsigned int flag);
#ifdef PUK3_PROF
static void MenuAddressBookDeleteFromMiniMailMember (int delNo);
BOOL MenuAddressBookDeleteFromMiniMail (int no, unsigned int flag);
#endif
struct MailHistoryStatus{
	int type;		//アドレス(0)or家族(1)
	int index;		//index
};

//ディティールorリストモード
typedef enum {
	EnumMenuWindowAddressBookViewModeDetail = 0,
	EnumMenuWindowAddressBookViewModeList
} VIEW_MODE;

//アドレスor家族モード
typedef enum {
	EnumMenuWindowAddressBookTypeAddressBook = 0,
	EnumMenuWindowAddressBookTypeGuildBook,
#ifdef PUK3_PROF
	EnumMenuWindowAddressBookTypeMiniMail,
#endif
} BOOK_TYPE;

extern MailHistoryStatus MailHistoryST;

void menuWindowAddressBookToList ();
void menuWindowAddressBookToDetail ();
int sortKeyAlphabet (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2);
int sortKeyAlphabet (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2);
int sortKeyTitle (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2);
int sortKeyGuild (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2);
int sortKeyLevel (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2);
int sortKeyLevel (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2);
int sortKeyRegist (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2);
int sortKeyRegist (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2);
int sortKeyServer (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2);
int sortKeyServer (GUILD_MEMBER_INFO* a1, GUILD_MEMBER_INFO* a2);
#ifdef PUK3_PROF
int sortKeyForMiniMail (ADDRESS_BOOK_INFO* a1, ADDRESS_BOOK_INFO* a2);
#endif
BOOL MenuWindowAddressBook (int mouse);
BOOL MenuWindowAddressBookDraw (int mouse);
//BOOL addressBookSwitch (int no);
void MenuWindowAddressBookSort ();
int MenuWindowAddressBookCompare (const void* _pt1, const void* _pt2);
void MenuWindowAddressBookRedraw ();
BOOL MenuAddressBookDeleteOk (int no, unsigned int flag);
BOOL MenuAddressBookDeleteCancel (int no, unsigned int flag);
BOOL MenuAddressBookDeleteFromGuild (int no, unsigned int flag);

#define AMOUNT_OF_DETAIL_VIEW 3
#define AMOUNT_OF_LIST_VIEW 9

GRAPHIC_SWITCH MenuWindowAddressBookGraph [] = {
	{GID_addressBookListView, 0, 0, 0, 0, 0xffffffff},	
	{GID_addressBookDetailView, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookSortKeyGuild, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookSortKeyAlphabetical, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookSortKeyTitle, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookSortKeyLevel, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookSortKeyRegist, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookSendOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookReceiveOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookGroupOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookDeleteOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookCloseOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookScrollUpArrowOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookScrooTab, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookScrollDownArrowOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookPagePrevOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookPageNextOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookAddressOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookGuildOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookDetailViewBase, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookListBar, 0, 0, 0, 0, 0xffffffff}, 
	{GID_addressBookGuildSettingOn, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookBack, 0, 0, 0, 0, 0xffffffff},
	{GID_addressBookBase, 0, 0, 0, 0, 0x80ffffff},
#ifdef PUK3_PROF
	{GID_AddressBookMiniMailButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_MiniMailBook, 0, 0, 0, 0, 0xffffffff}
#endif
};

TEXT_SWITCH MenuWindowAddressBookText [] = {
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "name"},                         //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "level"},                        //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "guild"},                        //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "title"},                        //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "server"},                       //MLHIDE
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, "guildserver"}                   //MLHIDE
};

BUTTON_SWITCH MenuWindowAddressBookButton[] = {
	{0 ,0}};

enum {
	EnumGraphAddressBookGuildSetting,
	EnumGraphAddressBookViewModePrev,
	EnumGraphAddressBookViewModeNext,
	EnumGraphAddressBookSortKeyPrev,
	EnumGraphAddressBookSortKeyNext,
	EnumGraphAddressBookPrevPage,
	EnumGraphAddressBookNextPage,
	EnumGraphAddressBookViewMode,
	EnumGraphAddressBookSortKey,
	EnumGraphAddressBookClose,

	EnumGraphAddressBookAddressBook,
	EnumGraphAddressBookGuildBook,
#ifdef PUK3_PROF
	EnumGraphAddressBookMiniMail,
	EnumGraphAddressBookMiniMailBar,
#endif
	EnumGraphAddressBookScrollUp,
	EnumGraphAddressBookScrollDown,
	EnumGraphAddressBookScrollTab,
	EnumGraphAddressBookScrollBar,
	EnumGraphAddressBookScrollWheel,
	EnumGraphAddressBookGroup,
	EnumGraphAddressBookGuildServer,

// detailViewMode
	EnumGraphAddressBookChangeTitle0,
	EnumGraphAddressBookChangeTitle1,
	EnumGraphAddressBookChangeTitle2,
	EnumGraphAddressBookChangeTitle3,
	EnumGraphAddressBookChangeTitle4,
	EnumGraphAddressBookChangeTitle5,

	EnumGraphAddressBookRecvSub0,
	EnumGraphAddressBookSendSub0,
	EnumGraphAddressBookDeleteSub0,
	EnumGraphAddressBookName0,
	EnumGraphAddressBookLevel0,
	EnumGraphAddressBookGuild0,
	EnumGraphAddressBookTitle0,
	EnumGraphAddressBookServer0,

	EnumGraphAddressBookRecvSub1,
	EnumGraphAddressBookSendSub1,
	EnumGraphAddressBookDeleteSub1,
	EnumGraphAddressBookName1,
	EnumGraphAddressBookLevel1,
	EnumGraphAddressBookGuild1,
	EnumGraphAddressBookTitle1,
	EnumGraphAddressBookServer1,

	EnumGraphAddressBookRecvSub2,
	EnumGraphAddressBookSendSub2,
	EnumGraphAddressBookDeleteSub2,
	EnumGraphAddressBookName2,
	EnumGraphAddressBookLevel2,
	EnumGraphAddressBookGuild2,
	EnumGraphAddressBookTitle2,
	EnumGraphAddressBookServer2,

	EnumGraphAddressBookDetail0,
	EnumGraphAddressBookDetail1,
	EnumGraphAddressBookDetail2,

// listViewMode
	EnumGraphAddressBookListRecvSub0,
	EnumGraphAddressBookListSendSub0,
	EnumGraphAddressBookListDeleteSub0,
	EnumGraphAddressBookListName0,
	EnumGraphAddressBookListServer0,

	EnumGraphAddressBookListRecvSub1,
	EnumGraphAddressBookListSendSub1,
	EnumGraphAddressBookListDeleteSub1,
	EnumGraphAddressBookListName1,
	EnumGraphAddressBookListServer1,

	EnumGraphAddressBookListRecvSub2,
	EnumGraphAddressBookListSendSub2,
	EnumGraphAddressBookListDeleteSub2,
	EnumGraphAddressBookListName2,
	EnumGraphAddressBookListServer2,

	EnumGraphAddressBookListRecvSub3,
	EnumGraphAddressBookListSendSub3,
	EnumGraphAddressBookListDeleteSub3,
	EnumGraphAddressBookListName3,
	EnumGraphAddressBookListServer3,

	EnumGraphAddressBookListRecvSub4,
	EnumGraphAddressBookListSendSub4,
	EnumGraphAddressBookListDeleteSub4,
	EnumGraphAddressBookListName4,
	EnumGraphAddressBookListServer4,

	EnumGraphAddressBookListRecvSub5,
	EnumGraphAddressBookListSendSub5,
	EnumGraphAddressBookListDeleteSub5,
	EnumGraphAddressBookListName5,
	EnumGraphAddressBookListServer5,

	EnumGraphAddressBookListRecvSub6,
	EnumGraphAddressBookListSendSub6,
	EnumGraphAddressBookListDeleteSub6,
	EnumGraphAddressBookListName6,
	EnumGraphAddressBookListServer6,

	EnumGraphAddressBookListRecvSub7,
	EnumGraphAddressBookListSendSub7,
	EnumGraphAddressBookListDeleteSub7,
	EnumGraphAddressBookListName7,
	EnumGraphAddressBookListServer7,

	EnumGraphAddressBookListRecvSub8,
	EnumGraphAddressBookListSendSub8,
	EnumGraphAddressBookListDeleteSub8,
	EnumGraphAddressBookListName8,
	EnumGraphAddressBookListServer8,

	EnumGraphAddressBookList0, 
	EnumGraphAddressBookList1, 
	EnumGraphAddressBookList2, 
	EnumGraphAddressBookList3, 
	EnumGraphAddressBookList4, 
	EnumGraphAddressBookList5, 
	EnumGraphAddressBookList6, 
	EnumGraphAddressBookList7, 
	EnumGraphAddressBookList8, 

	EnumGraphAddressBookBack,
	EnumGraphAddressBookBase,

	EnumHitAddressBook1,

	EnumGraphAddressBookEnd,
};

	
static SWITCH_DATA MenuWindowAddressBookSwitch [] = {
	{SWITCH_GRAPHIC, 270-11,  48-3, 41, 17, FALSE, &MenuWindowAddressBookGraph[21], MenuAddressBookGuildSettingOpen},
	{SWITCH_GRAPHIC,  30-11,  33-3, 18, 18, TRUE, &MenuWindowAddressBookGraph[15], MenuAddressBookButton},
	{SWITCH_GRAPHIC, 135-11,  33-3, 18, 18, TRUE, &MenuWindowAddressBookGraph[16], MenuAddressBookButton},
	{SWITCH_GRAPHIC,  30-11,  52-3, 18, 18, TRUE, &MenuWindowAddressBookGraph[15], MenuAddressBookButton},
	{SWITCH_GRAPHIC, 135-11,  52-3, 18, 18, TRUE, &MenuWindowAddressBookGraph[16], MenuAddressBookButton},
	{SWITCH_GRAPHIC,  30-11, 336-3, 18, 18, TRUE, &MenuWindowAddressBookGraph[15], MenuAddressBookButton},
	{SWITCH_GRAPHIC,  53-11, 336-3, 18, 18, TRUE, &MenuWindowAddressBookGraph[16], MenuAddressBookButton},
	{SWITCH_GRAPHIC,  53-11,  36-3, 0, 0, TRUE, &MenuWindowAddressBookGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC,  53-11,  55-3, 0, 0, TRUE, &MenuWindowAddressBookGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 336-26,   9, 11, 11, TRUE, &MenuWindowAddressBookGraph[11], MenuSwitchCloseButton},

	{SWITCH_GRAPHIC, 245-27, 347+6, 39, 24, TRUE, &MenuWindowAddressBookGraph[17], MenuAddressBookTypeChange},
	{SWITCH_GRAPHIC, 298-27, 346+6, 38, 25, TRUE, &MenuWindowAddressBookGraph[18], MenuAddressBookTypeChange},
#ifdef PUK3_PROF
	{SWITCH_GRAPHIC, 170-27, 343+6, 60, 25, TRUE, &MenuWindowAddressBookGraph[24], MenuAddressBookTypeChange},
	{SWITCH_GRAPHIC,   120,   8, 60, 25, TRUE, &MenuWindowAddressBookGraph[25], MenuSwitchNone},
#endif
	{SWITCH_GRAPHIC, 327-17,  73-4, 11, 11, TRUE, &MenuWindowAddressBookGraph[12], MenuAddressBookScrollOneLine},
	{SWITCH_GRAPHIC, 327-17, 319-4, 11, 11, TRUE, &MenuWindowAddressBookGraph[14], MenuAddressBookScrollOneLine},
	{SWITCH_GRAPHIC, 327-17,  84-4, 10, 14, TRUE, &MenuWindowAddressBookGraph[13], MenuSwitchNone},
	{SWITCH_BUTTON,  326-17,  84-4, 11, 235, TRUE, &MenuWindowAddressBookButton[0], MenuSwitchScrollBarV},
	{SWITCH_NONE,	   0,  0, 331,381, TRUE, NULL, MenuAddressBookScrollWheel },								// マウスホイール判定

	{SWITCH_GRAPHIC, 230-11,  48-3, 19, 19, TRUE, &MenuWindowAddressBookGraph[9], MenuAddressBookGroupMail},
	{SWITCH_TEXT, 200, 30, 0, 0, TRUE, &MenuWindowAddressBookText[5], MenuSwitchNone},

// detailViewBlock
	{SWITCH_GRAPHIC, 146-4, 88-4, 18, 18, FALSE, &MenuWindowAddressBookGraph[15], MenuAddressBookChangeTitle},
	{SWITCH_GRAPHIC, 286-4, 88-4, 18, 18, FALSE, &MenuWindowAddressBookGraph[16], MenuAddressBookChangeTitle},
	{SWITCH_GRAPHIC, 146-4, 175-4, 18, 18, FALSE, &MenuWindowAddressBookGraph[15], MenuAddressBookChangeTitle},
	{SWITCH_GRAPHIC, 286-4, 175-4, 18, 18, FALSE, &MenuWindowAddressBookGraph[16], MenuAddressBookChangeTitle},
	{SWITCH_GRAPHIC, 146-4, 262-4, 18, 18, FALSE, &MenuWindowAddressBookGraph[15], MenuAddressBookChangeTitle},
	{SWITCH_GRAPHIC, 286-4, 262-4, 18, 18, FALSE, &MenuWindowAddressBookGraph[16], MenuAddressBookChangeTitle},

	//３キャラクター分情报
	{SWITCH_GRAPHIC, 231-9, 126-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 252-9, 126-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 273-9, 126-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 150 + 12+3, 44 + 71 - 39-1-4, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 272 + 7, 44 + 72 - 39-2-4, 0, 0, TRUE, &MenuWindowAddressBookText[1], MenuSwitchNone},
	{SWITCH_TEXT, 150 + 12+3, 59 + 71 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[2], MenuSwitchNone},
	{SWITCH_TEXT, 150 + 12+3, 74 + 71 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[3], MenuSwitchNone},
	{SWITCH_TEXT, 151 , 105 + 71 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 231-9, 213-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 252-9, 213-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 273-9, 213-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 150 + 12+3, 44 + 158 - 39-1-4, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 272 + 7, 44 + 159 - 39-2-4, 0, 0, TRUE, &MenuWindowAddressBookText[1], MenuSwitchNone},
	{SWITCH_TEXT, 150 + 12+3, 59 + 158 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[2], MenuSwitchNone},
	{SWITCH_TEXT, 150 + 12+3, 74 + 158 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[3], MenuSwitchNone},
	{SWITCH_TEXT, 151 , 105 + 158 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 231-9, 309-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 252-9, 309-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 273-9, 309-2-4, 19, 19, TRUE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 150 + 12+3, 44 + 245 - 39-1-4, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 272 + 7, 44 + 246 - 39-2-4, 0, 0, TRUE, &MenuWindowAddressBookText[1], MenuSwitchNone},
	{SWITCH_TEXT, 150 + 12+3, 59 + 245 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[2], MenuSwitchNone},
	{SWITCH_TEXT, 150 + 12+3, 74 + 245 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[3], MenuSwitchNone},
	{SWITCH_TEXT, 151 , 105 + 245 - 39-4, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	//キャラクター枠
	{SWITCH_GRAPHIC, 27-11,  71-4,292,87, TRUE, &MenuWindowAddressBookGraph[19], MenuAddressBookButton},
	{SWITCH_GRAPHIC, 27-11, 158-4,292,87, TRUE, &MenuWindowAddressBookGraph[19], MenuAddressBookButton},
	{SWITCH_GRAPHIC, 27-11, 245-4,292,87, TRUE, &MenuWindowAddressBookGraph[19], MenuAddressBookButton},
	
// listViewBlock
	{SWITCH_GRAPHIC, 251-9, 75-3, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 75-3, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 75-3, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 70-3, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 70-3, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 104-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 104-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 104-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 99-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 99-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 133-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 133-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 133-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 128-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 128-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 162-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 162-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 162-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 157-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 157-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 191-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 191-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 191-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 186-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 186-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 220-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 220-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 220-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 215-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 215-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 249-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 249-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 249-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 244-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 244-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 278-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 278-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 278-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 273-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 273-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 251-9, 307-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[8], MenuAddressBookHistory},
	{SWITCH_GRAPHIC, 272-9, 307-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[7], MenuAddressBookSendSwitch},
	{SWITCH_GRAPHIC, 293-9, 307-2, 19, 19, FALSE, &MenuWindowAddressBookGraph[10], MenuAddressBookDelete},
	{SWITCH_TEXT, 65 -9, 13 + 302-2, 0, 0, TRUE, &MenuWindowAddressBookText[0], MenuSwitchNone},
	{SWITCH_TEXT, 155 + 27-9, 13 + 302-2, 0, 0, TRUE, &MenuWindowAddressBookText[4], MenuSwitchNone},

	{SWITCH_GRAPHIC, 15, 68+29*0,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*1,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*2,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*3,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*4,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*5,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*6,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*7,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
	{SWITCH_GRAPHIC, 15, 68+29*8,230, 29, FALSE, &MenuWindowAddressBookGraph[20], MenuAddressBookListPanel},
// baseBlock

	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowAddressBookGraph[22], MenuSwitchNone},
	{SWITCH_GRAPHIC, 12, 27, 0, 0, TRUE, &MenuWindowAddressBookGraph[23], MenuSwitchNone},

	{ SWITCH_NONE  , 331, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};


const WINDOW_DATA WindowDataMenuAddressBook = {
	0,
		4,  2, 64, 331, 381, 0x80010101, EnumGraphAddressBookEnd, MenuWindowAddressBookSwitch, MenuWindowAddressBook, MenuWindowAddressBookDraw,MenuWindowDel
};

static WINDOW_DRAGMOVE DragMoveDataAddressBook = {
	2, 
	0, 0, 342, 30,
	331, 0, 20,120,
};

//现在接続しているサーバー
char gameServerNameList[][64] =
{
	"不在线",                                                               //MLHIDE
	"牧羊", "金牛", "双子", "巨蟹", "Ornis", "Freat",                            //MLHIDE
	"Yeoskeer", "Farkalt", "Milliotice", "Finia", "Erenor", "Karen" };   //MLHIDE

char gameServerColorList[]={
7,		//OFFライン
5,1,3,4,2,6,
5,1,3,4,2,6
};

//家族の所属しているサーバー
char GuildServerNameList[][64] =
{
	"",
	"牧羊", "金牛", "双子", "巨蟹", "Ornis", "Freat",                            //MLHIDE
	"Yeoskeer", "Farkalt", "Milliotice", "Finia", "Erenor", "Karen"      //MLHIDE
};

char GuildServerColorList[]={
0,
0,0,0,0,0,0,
0,0,0,0,0,0
};



POINT detailFace[] = {
	{62, 76 + 39}, {62, 163 + 39}, {62, 250 + 39}};



//------------------------------------------------------------------
//家族称号ウインドウ
//------------------------------------------------------------------

GRAPHIC_SWITCH MenuWindowChangeGuildTitleGraph [] = {
	{GID_ChangeGuildTitleTitleBackOn, 0, 0, 0, 0, 0xffffffff},	
	{GID_ChangeGuildTitleCloseOn, 0, 0, 0, 0, 0xffffffff},	
	{GID_ChangeGuildTitleSetOn, 0, 0, 0, 0, 0xffffffff},	
	{GID_ChangeGuildTitleScrollTumami, 0, 0, 0, 0, 0xffffffff},	
	{GID_ChangeGuildTitleScrollUPOn, 0, 0, 0, 0, 0xffffffff},	
	{GID_ChangeGuildTitleScrollDownOn, 0, 0, 0, 0, 0xffffffff},	
	{GID_ChangeGuildTitleScrollBar, 0, 0, 0, 0, 0xffffffff},	
};

TEXT_SWITCH MenuWindowChangeGuildTitleText [] = {
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, ML_STRING(820, "家族称号")},
	{FONT_PAL_WHITE, FONT_KIND_SIZE_12, ML_STRING(821, "对象名字")},
};

//ボタンスイッチ
BUTTON_SWITCH MenuWindowChangeGuildTitleButton[]={
	{0},										
};

enum {

	EnumTextChangeGuildTitle00,
	EnumTextChangeGuildTitle01,
	EnumTextChangeGuildTitle02,
	EnumTextChangeGuildTitle03,
	EnumTextChangeGuildTitle04,
	EnumTextChangeGuildTitle05,
	EnumTextChangeGuildTitle06,
	EnumTextChangeGuildTitle07,
	EnumTextChangeGuildTitle08,
	EnumTextChangeGuildTitle09,

	EnumTextChangeGuildTitleName,

	EnumGraphChangeGuildTitleClose,
	EnumGraphChangeGuildTitleSet,

	EnumScrollChangeGuildTitle01,
	EnumScrollChangeGuildTitle02,
	EnumScrollChangeGuildTitle03,
	EnumScrollChangeGuildTitle04,
	EnumScrollChangeGuildTitle05,
	EnumScrollChangeGuildTitle06,

	EnumGraphChangeGuildTitle00,
	EnumGraphChangeGuildTitle01,
	EnumGraphChangeGuildTitle02,
	EnumGraphChangeGuildTitle03,
	EnumGraphChangeGuildTitle04,
	EnumGraphChangeGuildTitle05,
	EnumGraphChangeGuildTitle06,
	EnumGraphChangeGuildTitle07,
	EnumGraphChangeGuildTitle08,
	EnumGraphChangeGuildTitle09,

	EnumChangeGuildTitleEnd,
};

	
static SWITCH_DATA MenuWindowChangeGuildTitleSwitch [] = {

	//タイトル
	{SWITCH_TEXT, 7, 21+16*0, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*1, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*2, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*3, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*4, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*5, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*6, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*7, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*8, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},
	{SWITCH_TEXT, 7, 21+16*9, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[0], MenuSwitchNone},

	//名称
	{SWITCH_TEXT, 8, 181, 0, 0, TRUE, &MenuWindowChangeGuildTitleText[1], MenuSwitchNone},

	//クローズ
	{SWITCH_GRAPHIC, 150, 181, 49, 17, TRUE, &MenuWindowChangeGuildTitleGraph[1], MenudChangeGuildTitleClose},
	//セット
	{SWITCH_GRAPHIC, 206, 181, 32, 17, TRUE, &MenuWindowChangeGuildTitleGraph[2], MenudChangeGuildTitleSet},
	
	{ SWITCH_GRAPHIC,224,  0,  0, 14, TRUE, &MenuWindowChangeGuildTitleGraph[3], MenuSwitchNone },	//スクロールバー(つまみ)
	{ SWITCH_BUTTON, 225, 33, 11,135, TRUE, &MenuWindowChangeGuildTitleButton[0], MenuSwitchScrollBarV },	//スクロールバー(ドラッグ部分)
	{ SWITCH_GRAPHIC,224, 22, 11, 11, TRUE, &MenuWindowChangeGuildTitleGraph[4], MenudChangeGuildTitleScrollTopDown },	//スクロールバー(上ボタン)
	{ SWITCH_GRAPHIC,224,168, 11, 11, TRUE, &MenuWindowChangeGuildTitleGraph[5], MenudChangeGuildTitleScrollTopDown },	//スクロールバー(下ボタン)
	{ SWITCH_NONE,	   0,  0,243,206, TRUE, NULL, MenuChangeGuildTitleScrollWheel },									// マウスホイール判定
	{SWITCH_GRAPHIC, 221, 18,  0,  0, TRUE, &MenuWindowChangeGuildTitleGraph[6], MenuSwitchNone},	//スクロールバー(バー自体)

	{SWITCH_GRAPHIC, 5, 19+16*0,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*1,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*2,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*3,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*4,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*5,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*6,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*7,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*8,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
	{SWITCH_GRAPHIC, 5, 19+16*9,216, 16, TRUE, &MenuWindowChangeGuildTitleGraph[0], MenudChangeGuildTitleSelect},
};


//ウインドウ
// 家族称号变更ウインドウ
const WINDOW_DATA WindowDataChangeGuildTitle = {
 0,																	
     4, 100, 100,243,206, 0x80000080, EnumChangeGuildTitleEnd, MenuWindowChangeGuildTitleSwitch, MenuWindowChangeGuildTitle,MenuWindowChangeGuildTitleDraw,MenuWindowChangeGuildTitleDel 
};

static WINDOW_DRAGMOVE DragMoveDateChangeGuildTitle = {
	1, 
	0, 0, 243, 10,
};


#endif


