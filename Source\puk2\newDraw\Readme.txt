﻿backup 5/13 分

アルファブレンディングを使用する为、Direct3D を组み込み、
Direct3D を使用して描画出来るように变更しました。

"newDraw.h"は、"directDraw.h"内でインクルード

"directdraw3D.cpp"は、"directDraw.cpp"内でインクルード
"newPutBmp.cpp"は、"Sprdisp.cpp"内での PutBmp() 关数の、直前、または直后でインクルード

これを行った后コンパイルすると、新しく加えた关数はすでに定义されていると言われるので、
古い方の关数を、コンパイルしないようにする

//--------------------------------------------------------------------------------

backup 5/14 分

反転描画を利用し、キャラの絵を少なく出来るように变更を加えました
２を０，７を３，６を４の向きの反転描画にしています

"newaction.h"は、"action.h"内でインクルード
"newmap.h"は、"map.h"内でインクルード
"newSprdisp.h"は、"sprdisp.h"内でインクルード

"newPutBmp.cpp"は、なくなりましたので、インクルードしません
变わりに、PutBmp() 关数の、直前に"newSprdisp.cpp"をインクルード
"newaction.cpp"は、"action.cpp"内でインクルード
"newcharactor.cpp"は、"charactor.cpp"内の createPetAction 关数の直前でインクルード
"newPattern.cpp"は、"Pattern.cpp"でインクルード

全てのファイル中の DDSURFACEDESC を DDSURFACEDESC2 に变える

ClearSurface()、DrawBitmapToSurface2()、DrawSurfaceFast2() 关数の引数の型の
LPDIRECTDRAWSURFACE を LPDIRECTDRAWSURFACE7 に变更

"sprmgr.cpp"でグローバル宣言されている
LPDIRECTDRAWSURFACE lpBattleSurface;
を
LPDIRECTDRAWSURFACE7 lpBattleSurface;
に变更

これを行った后コンパイルすると、
新しく加えた关数はすでに定义されていると言われるので、
古い方の关数を、コンパイルしないようにする
又、プロトタイプ宣言で问题が出るので、
问题が出たプロトタイプ宣言は"directDraw.cpp"から消してください

//--------------------------------------------------------------------------------

backup 5/15 分

アルファブレンディングを使用し描画出来るように变更しました
色データは RGBA_MAKE( r,g,b,a ) の戾り值として取得できます
r 赤、g 緑、b 青、a 不透过の度合い
どの数值も 255 が最大です

新たに使用できるようになった关数

HRESULT RectAlphaBlend( short l, short r, short t, short b, unsigned long rgba );

矩形领域をアルファブレンドを使用して涂りつぶします
l に左端座标、r に右端座标、t に上端座标、b に下端座标、rgba に色データを渡します

void StockAlphaDispBuffer( short l, short r, short t, short b, UCHAR dispPrio, unsigned long rgba );

矩形领域をアルファブレンドを使用して涂りつぶす命令をバッファに溜めます
l に左端座标、r に右端座标、t に上端座标、b に下端座标、
dispPrio に表示优先顺位、rgba に色データを渡します

又、キャラクタの描画も、アルファブレンドの指定が出来ます。
ACTON.rgba に色データを渡してください。

//--------------------------------------------------------------------------------

backup 5/16 分

今までの分のバグ取りと、新マップの描画部分を作りました
现状では、新旧の判断方法が不明なため、完成はしていません

//--------------------------------------------------------------------------------

backup 5/17 分

5/16 分のバグ取りと、フォントのサイズなどを增やせるように变更しました
"newproduce.h"と"newproduce.h"は中に记述がありますが、まだ使用していません

//--------------------------------------------------------------------------------

backup 5/19 分

フォント回りのバグ取り、ラスタ表示部分の速度变更を可能にするように变更しました
ラスタ部分の速度变更は、DrawProduce() 关数に新たに加えた引数、char ad と float ac を变更します
元々の设定を使用する时は、ad 、ac とも、元々の设定にするものに 0 を渡します
引数 int no の值によって ad や ac の意味が变わるので下に载せておきます

PRODUCE_UP_ACCELE ---- 上加速移动
PRODUCE_DOWN_ACCELE ---- 下加速移动
　それぞれに对应した方向に画面が移动します
	ad: (初:   0) 使用しません
	ac: (初: 0.3) 移动の加速度を设定します
PRODUCE_LEFT_ACCELE ---- 左加速移动
PRODUCE_RIGHT_ACCELE ---- 右加速移动
　それぞれに对应した方向に画面が移动します
	ad: (初:   0) 使用しません
	ac: (初: 0.4) 移动の加速度を设定します
PRODUCE_LEFT_RIGHT_ACCELE ---- 左右加速移动
　左右方向に何ピクセルか每に反对方向に移动します
	ad: (初:   0) 使用しません
	ac: (初: 0.4) 移动の加速度を设定します
PRODUCE_UP_DOWM_ACCELE ---- 上下加速移动
　上下方向に何ピクセルか每に反对方向に移动します
	ad: (初:   0) 使用しません
	ac: (初: 0.3) 移动の加速度を设定します
PRODUCE_UNERI_ACCELE ---- うねり加速移动
　上方向から何ピクセルか每に反对方向に移动します(バトルイン时の画面が黒くなる时)
	ad: (初:   4) 上方向から下方向への移动の速さ
	ac: (初: 1.0) 移动の加速度を设定します
PRODUCE_UP_DOWN_LINE_ACCELE ---- 上下ライン加速移动
　上下两端から何ピクセルかずつ画面外に移动します
	ad: (初:   4) 次の固まりに移るまでの速さ（值が大きいほど早い）
	ac: (初: 1.0) 画面外移动の加速度を设定します
PRODUCE_LINE_HAGARE_OCHI_OUT ---- ラインはがれ落ちＯＵＴ
　上方向から何ピクセルかずつ下に落ちていきます
	ad: (初:  16) 次の固まりに移るまでのスピード（值が大きいほど早い）
	ac: (初: 1.0) 落ちる时の加速度を设定します

PRODUCE_UP_BRAKE ---- 上减速移动
PRODUCE_DOWN_BRAKE ---- 下减速移动
　それぞれに对应した方向から画面が移动してきます
	ad: (初:   0) 使用しません
	ac: (初: 0.3) 移动の减速度を设定します
PRODUCE_LEFT_BRAKE ---- 左减速移动
PRODUCE_RIGHT_BRAKE ---- 右减速移动
　それぞれに对应した方向から画面が移动してきます
	ad: (初:   0) 使用しません
	ac: (初: 0.4) 移动の减速度を设定します
PRODUCE_LEFT_RIGHT_BRAKE ---- 左右减速移动
　左右方向に何ピクセルか每に反对方向から移动してきます
	ad: (初:   0) 使用しません
	ac: (初: 0.4) 移动の减速度を设定します
PRODUCE_UP_DOWM_BRAKE ---- 上下减速移动
　上下方向に何ピクセルか每に反对方向から移动してきます
	ad: (初:   0) 使用しません
	ac: (初: 0.3) 移动の减速度を设定します
PRODUCE_UNERI_BRAKE ---- うねり减速移动
　上方向から何ピクセルか每に反对方向に移动します(バトルインじの黒い画面から战闘画面が出てくる时)
	ad: (初:   4) 上方向から下方向への移动の早さ
	ac: (初: 1.0) 移动の减速度を设定します
PRODUCE_UP_DOWN_LINE_BRAKE ---- 上下ライン减速移动
　上下两端から何ピクセルかずつ画面外に移动します(ログイン)
	ad: (初:   4) 次の固まりに移るまでの速さ（值が大きいほど早い）
	ac: (初: 1.0) 画面外移动の减速度を设定します
PRODUCE_LINE_HAGARE_OCHI_IN ---- ラインはがれ落ちＩＮ
　上から何ピクセルかずつ下に落ちてきます
	ad: (初:  16) 次の固まりに移るまでの速さ（值が大きいほど早い）
	ac: (初: 1.0) 落ちる时の减速度を设定します

PRODUCE_4WAY_OUT ---- 四方向移动画面外へ
PRODUCE_4WAY_IN ---- 四方向移动画面内へ
　画面が四分割され、それぞれの部分に对应した方向へ移动、又は、その方向から移动してきます
	ad: (初:   4) 移动の加速度を设定します
	ac: (初:   0) 使用しません

PRODUCE_HAGARE_OUT ---- はがれ处理（消える）
PRODUCE_HAGARE_IN ---- はがれ处理（出现する）
　画面が细かく分けられ、ランダムで消え、又、出现します
	ad: (初:   1) 一回の处理で消える、出现する数
	ac: (初: 1.0) 使用しません
PRODUCE_HAGARE_OCHI_OUT ---- はがれ落ち（消える）
PRODUCE_HAGARE_OCHI_IN ---- はがれ落ち（出现する）
　画面が细かく分けられ、ランダムで下に落ち、又、上から落ちてきます
	ad: (初:   1) 一回の处理で落ちる数
	ac: (初: 1.0) 落ちる时の加速度（これに关してだけは、小数で设定できません）

PRODUCE_BRAN_SMALL ---- フスマ缩小
PRODUCE_BRAN_BIG ---- フスマ扩大
　ad の值により、画面が小さくなって消えていったり、大きくなりながら现れたりします
	ad: (初:-1,1) マイナスなら、小さく、プラスなら大きくなります、絶对值が大きくなるほど、扩大缩小の速さが速くなります
	ac: (初:   0) 使用しません

PRODUCE_BREAK_UP1 ---- 折りたたみ上アウト
PRODUCE_BREAK_UP3 ---- 折りたたみ上アウト
　画面が上の方に溃れます
	ad: (初:   8) 溃れる速さ
	ac: (初:   0) 使用しません
PRODUCE_BREAK_UP2 ---- 折りたたみ上イン
PRODUCE_BREAK_UP8 ---- 折りたたみ上イン
　画面が上の方から膨らんできます
	ad: (初:   8) 溃れる速さ
	ac: (初:   0) 使用しません
PRODUCE_BREAK_UP4 ---- 折りたたみ下イン
PRODUCE_BREAK_UP6 ---- 折りたたみ下イン
　画面が下の方に溃れます
	ad: (初:   8) 溃れる速さ
	ac: (初:   0) 使用しません
PRODUCE_BREAK_UP5 ---- 折りたたみ下アウト
PRODUCE_BREAK_UP7 ---- 折りたたみ下アウト
　画面が下の方から膨らんできます
	ad: (初:   8) 溃れる速さ
	ac: (初:   0) 使用しません
PRODUCE_CENTER_PRESSIN:
　画面が上下に中央に溃れます
	ad: (初:   8) 溃れる速さ
	ac: (初:   0) 使用しません
PRODUCE_CENTER_PRESSOUT:
　画面が上下に中央から膨らんできます
	ad: (初:   8) 溃れる速さ
	ac: (初:   0) 使用しません

そういえば、何日分からか、Direct3D の机能を使用しないように设定しているので、使用するように设定变更が必要

//--------------------------------------------------------------------------------

backup 5/20 分

なんだか、实际组み込んだ时、"mapGridCursol.cpp"に问题出るので、
"system.h"と"map.h"を最初の部分でインクルード

BLT_MEMBER 构造体を使用することが决まり、これを使用した形にプログラムを变更
实际に杉山さんのプログラムに组み込める形にして、组み込んだ
デバッグの都合上、新型描画部分をパレット变更しないように一时的に变更

//--------------------------------------------------------------------------------

backup 5/21 分

setMapParts() 关数の初期化部分にバグがあったため、バグを取った
これによって３Ｄ使用时にマップオブジェクトが表示されないというバグが解消

//--------------------------------------------------------------------------------

old_prog (backup 5/22 分)

反転描画时の表示位置の调整用に、

int Blt_adjust( ACTION *pAct, char xy );

という关数を追加、今后、ACTION の anim_x 、anim_y で、ACTION の x 、y を调整せずにキャラを描画する场合、
描画命令を出す关数の呼び出しの时、ACTION の x には、xy に 0 を渡して归ってきた戾り值を、
ACTION の y には、xy に 1 を渡して归ってきた戾り值を、一时的に足してください

//--------------------------------------------------------------------------------

backup 5/23 分

前回からプログラムの组み込み方に变更があり、その变更と、絵の読み込み部分のプログラムに变更を加えた

//--------------------------------------------------------------------------------

backup 5/27 分（5/24、5/26、5/27 分）

絵を、自分が用意したパレットを使用して読み込めるようにした
新型のパレットチェンジが出来るようにした
PUK2_3D 等の分野分けを少々变更
新型のパレットチェンジを、实际のパレットチェンジに合わせて实行するようにした

//--------------------------------------------------------------------------------

backup 5/28 分

新型のパレットチェンジのバグ取り、画面上の全てに对してパレットチェンジが行われるようになった

//--------------------------------------------------------------------------------

backup 5/29 分

ＣＶＳをいれ、齐藤２号さんのプログラムに组み込み、そのプログラムをこれから组替えていくことになった
组み込んだことによってバグが発生していないかバグ取り

//--------------------------------------------------------------------------------

backup 5/30 分

新型パレットチェンジのＯＮ、ＯＦＦ、色合いの指定を出来るようにしました
初期状态では、ＯＦＦの状态です

方法(@ の部分にはスペースが几つ入っていても平气、@ は入力しないように)

ＯＮ
	@{@newpal:@on@}
ＯＦＦ
	@{@newpal:@off@}
色合いの指定
	@{@newpalchg:@[r] @[g] @[b]@}
	[r]には赤、[g]には緑、[b]には青の色要素が入ります

胜手ではあるものの、战闘中に使い魔を变更した时、战闘后、使い魔が变わらないバグを、一应直した
また、フィールドで、使い魔の常态を变更する时、いちいち Battle状态の使い魔を 
Standby状态にするのが面倒だったので、变更する使い魔の状态と、Battle状态の使い魔の状态を入れ替えるように变更した

以上二つの变更点は、PUK2_DRAW_EXTRA で围われており、PUK2_DRAW_EXTRA は puk2.h で宣言してあるので、
宣言をコメントアウトすれば机能しなくなる

//--------------------------------------------------------------------------------

backup 6/7 分

今まで书くのを忘れていたが、新ビンを読み込めるようにしたりした

战闘エフェクトの色合いを指定できるようにした

方法(@ の部分にはスペースが几つ入っていても平气、@ は入力しないように)

	@{@effect:@[r] @[g] @[b] @[a]@}
	[r]には赤、[g]には緑、[b]には青の色、[a]には不透明度要素が入ります

以上をチャット栏に入力すると变化します
初期值は [r]:255、[g]:255、[b]:255、[a]:128

backup 5/30 分の新型パレットチェンジのＯＮ、ＯＦＦ、色合いの指定も同样の方法です

//--------------------------------------------------------------------------------

backup 6/10 分

sprview の时、スプライトデータからビットマップを作れるようにした

ＮＰＣキャラに话し挂けた时、ＰＣキャラの方に振り向くようにした

战闘时、クリティカル攻击なら、回転しながら吹っ飞ぶように变更した

//--------------------------------------------------------------------------------

backup 6/12 分

サーバー选择画面から、sprviewモード、animviewモードに移动できるようにした

方法は、コマンドラインの指定のところに、

sprviewに移动できるようにするには、
IP:[サーバー番号－１]:sprview:0

animviewに移动できるようにするには、
IP:[サーバー番号－１]:animview:0

を追加して下さい

それで、指定したサーバーを选择すると、
それぞれのモードに移动できます

又、この方法で、それぞれのモードに移动した时は、
Escキーで、サーバ选择画面に戾れます

//--------------------------------------------------------------------------------

backup 6/19 分

マップ描画时に、プライオリティがおかしくなるのを直そうとした

リバースが出来るようにした
现状では、ペット选择ウィンドウのキャンセルボタンを押すと、リバースＯＮ、
アイテムウィンドウののキャンセルボタンを押すと、リバースＯＦＦです

战闘时、怪我の报告などのチャット栏に对する表示が、实际のゲーム进行より速く表示されるバグを直した

//--------------------------------------------------------------------------------

backup 6/25 分

ポンスビックロゴを表示するようにしました

NOWLOADING、サーバ选择画面、キャラクタセレクト画面、キャラ作成时のキャラ选择画面、キャラ作成画面などを、
PUK2 の絵が表示されるようにしました、PUK がはいっている状态で、新binが入っていると、
PUK2 の絵になります

名词の絵の选择时、PUK2 の名词も选べるようにしました

企画さんからの要望で、反転描画が、新マップのＮＰＣのみ适用という形になっています

话し挂けたときＮＰＣキャラが振り向くを、新マップのＮＰＣに限定しました

企画さんからの要望で、フィールド上で、怪我以外の情报は表示されなくなっています
又、怪我のアイコンをinjuryに变更しました

リバース时、バースト时间の减少が表示されるようにしました
又、そのために、时间表示をするための关数、

ACTION *MakeBattleTimeDisp( int x, int y, int time );

を作成しました

新マップのオートマップを描画するために使用するサーフェースを用意しました

lpMnMapSf ------- 新マップのオートマップ格纳先サーフェース
lpAMapWarkSf ---- オートマップ作成用作业サーフェース

又、lpMnMapSf に、絵を読み込む为の关数

BOOL SetBmpToSurface( LPDIRECTDRAWSURFACE7 sf, int bmpNo, short lu, short lv, short lw, short lh, char usechg );

を用意しました

描画には、基本的に Blt关数や BltFast关数を使用することをお劝めします
（只单に、この关数の方がみんな分かりやすいと思うので）

//--------------------------------------------------------------------------------

backup 6/27 分

火属性のリバースを、おおよそ仕上げました

//--------------------------------------------------------------------------------

backup 6/28 分

anim_tbl_PUK2.h を组み込みました
それに伴い、私が今まで组んだプログラム内の、
PUK2の絵の指定を、数值から、anim_tbl_PUK2.h の定义を使用するように变更しました

コクピット回りを变更しました
主に、

战闘コマンドコクピットにリバースボタンを付けました

アビリティコクピットにリバースボタンを付けました

ステータスウィンドウに、ギルドタイトルの表示位置を确保しました

の三点です

现在、ギルドタイトルの表示位置には、ユーザータイトルが表示されるようになっています

//--------------------------------------------------------------------------------

backup 7/16 分

アニメーションごとにパレットを指定できるように、变更しました

//--------------------------------------------------------------------------------

backup 7/29 分

描画系プログラムのちょっとした整理

フルスクリーン时、画面右侧に出ていた线を、
デフォルトで表示しないようにし、
コマンドラインで、

debugline

を指定した场合に表示するようにしました

３Ｄ使用时の使用デバイスの最高ラインを设定できるようにしました、
コマンドラインで、

3Ddevice:?

を指定し、? には、适当な数值を入れます
このコマンドラインは、
普通にプレイしていて、
正常に动作しない场合、
速度を牺牲にして、确实に动くものに变更するためのものです

//--------------------------------------------------------------------------------

backup 7/31 分

コマンドライン部分のちょっとした整理整理

コマンドラインに

fulldispmode

の指定が无い限り、
１６ＢＰＰ以外のディスプレイモードは切るようにしました

PUK2 用 bin のサイズチェックを完成させました

//--------------------------------------------------------------------------------
