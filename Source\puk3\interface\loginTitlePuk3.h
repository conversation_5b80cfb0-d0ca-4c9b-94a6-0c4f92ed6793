﻿#ifndef _PUK3_LOGIN_TITLE_H_
#define _PUK3_LOGIN_TITLE_H_

//シーケンス
enum {
	enumPuk3Title_Init,
	enumPuk3Title_Opening,
	enumPuk3Title_IDInputInit,
	enumPuk3Title_IDInput,
	enumPuk3Title_ToServerSelect,
	enumPuk3Title_ERRInit,
	enumPuk3Title_ERR,
	enumPuk3Title_DELETEWinInit,
	enumPuk3Title_DELETEWin,
	enumPuk3Title_DELETEInit,
	enumPuk3Title_DELETE,

	enumPuk3Title_END
};

//タブボタン座标
typedef struct{
	int x,y,w,h;
}PUK3_TITLE_TAB_POS;

void PUK3_idPasswordProc(void);

#endif