﻿/************************/
/*	pattern.h			*/
/************************/
#ifndef _PATTERN_
#define _PATTERN_

#define	ANM_NOMAL_SPD	100
#define	ANM_NOMAL_SPD2	100
#define	ANM_LOOP		0
#define	ANM_NO_LOOP		1

//----------　アニメーション处理　----------*/
#ifdef PUK2
	int pattern( ACTION *pAct, int anim_spd, int loop_flg, char mrr_flg=0 );
#else
int pattern( ACTION *pAct, int anim_spd, int loop_flg );
#endif
// アニメーション处理 **********************************************/
int pattern( ACTION *pAct, int loop_flg );

#ifdef PUK2
	// 反転时描画位置调整用
	int Blt_adjust( ACTION *pAct, char xy );
	// このＩＤがアニメーションかをチェックする
	BOOL CheckAnimetion( int graNo );
#endif

#endif
