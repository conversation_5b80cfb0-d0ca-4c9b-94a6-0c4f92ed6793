﻿//右コクピット＞アクション

#ifndef _MENUACTION_H_
#define _MENUACTION_H_

#define GID_ActionCloseOn		GID_WindowCloseOn//239585
#define GID_ActionCloseOff		GID_WindowCloseOff//239585
#define GID_ActionCloseOver		GID_WindowCloseOver//239585

BOOL MenuWindowAction( int mouse );
BOOL MenuWindowActionDraw( int mouse );
BOOL MenuWindowActionDel( void );
BOOL MenuSwitchAction( int no, unsigned int flag );
void SetMenuWindowActionFlag(int Num);
void InitMenuWindowActionFlag(void);


GRAPHIC_SWITCH MenuWindowActionGraph[]={

	{GID_Action00Off,0,0,0,0,0xFFFFFFFF},		//座る
	{GID_Action01Off,0,0,0,0,0xFFFFFFFF},		//挥手
	{GID_Action02Off,0,0,0,0,0xFFFFFFFF},		//点头
	{GID_Action03Off,0,0,0,0,0xFFFFFFFF},		//よろこぶ
	{GID_Action04Off,0,0,0,0,0xFFFFFFFF},		//怒る
	{GID_Action05Off,0,0,0,0,0xFFFFFFFF},		//悲伤
	{GID_Action06Off,0,0,0,0,0xFFFFFFFF},		//投掷
	{GID_Action07Off,0,0,0,0,0xFFFFFFFF},		//剪刀
	{GID_Action08Off,0,0,0,0,0xFFFFFFFF},		//布
	{GID_Action09Off,0,0,0,0,0xFFFFFFFF},		//立つ
	{GID_Action10Off,0,0,0,0,0xFFFFFFFF},		//步く
	{GID_Action11Off,0,0,0,0,0xFFFFFFFF},		//倒下
	{GID_Action12Off,0,0,0,0,0xFFFFFFFF},		//攻击
	{GID_Action13Off,0,0,0,0,0xFFFFFFFF},		//防御
	{GID_Action14Off,0,0,0,0,0xFFFFFFFF},		//受伤
	{GID_Action15Off,0,0,0,0,0xFFFFFFFF},		//魔法
	{GID_Action16Off,0,0,0,0,0xFFFFFFFF},		//移动
	{GID_Action17Off,0,0,0,0,0xFFFFFFFF},		//石头

	{GID_ActionCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_ActionWindow,0,0,0,0,0xFFFFFFFF},		//ベース

};


BUTTON_SWITCH MenuWindowActionButton[]={
	{0},									//状态
};


// スイッチ
static SWITCH_DATA ActionSwitch[] = {
{ SWITCH_GRAPHIC, 14- 9, 31- 6,  23, 23, TRUE, &MenuWindowActionGraph[ 0], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9, 58- 8,  23, 23, TRUE, &MenuWindowActionGraph[ 1], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9, 85-10,  23, 23, TRUE, &MenuWindowActionGraph[ 2], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9,112-12,  23, 23, TRUE, &MenuWindowActionGraph[ 3], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9,139-14,  23, 23, TRUE, &MenuWindowActionGraph[ 4], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9,166-16,  23, 23, TRUE, &MenuWindowActionGraph[ 5], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9,193-18,  23, 23, TRUE, &MenuWindowActionGraph[ 6], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9,220-20,  23, 23, TRUE, &MenuWindowActionGraph[ 7], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 14- 9,247-22,  23, 23, TRUE, &MenuWindowActionGraph[ 8], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14, 31- 6,  23, 23, TRUE, &MenuWindowActionGraph[ 9], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14, 58- 8,  23, 23, TRUE, &MenuWindowActionGraph[10], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14, 85-10,  23, 23, TRUE, &MenuWindowActionGraph[11], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14,112-12,  23, 23, TRUE, &MenuWindowActionGraph[12], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14,139-14,  23, 23, TRUE, &MenuWindowActionGraph[13], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14,166-16,  23, 23, TRUE, &MenuWindowActionGraph[14], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14,193-18,  23, 23, TRUE, &MenuWindowActionGraph[15], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14,220-20,  23, 23, TRUE, &MenuWindowActionGraph[16], MenuSwitchAction },			//
{ SWITCH_GRAPHIC, 43-14,247-22,  23, 23, TRUE, &MenuWindowActionGraph[17], MenuSwitchAction },			//

{ SWITCH_GRAPHIC, 66-21,  7,  11, 11, TRUE, &MenuWindowActionGraph[18], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowActionGraph[19], MenuSwitchNone },			//ウインドウ

};

enum{
	EnumGraphAction00,
	EnumGraphAction01,
	EnumGraphAction02,
	EnumGraphAction03,
	EnumGraphAction04,
	EnumGraphAction05,
	EnumGraphAction06,
	EnumGraphAction07,
	EnumGraphAction08,
	EnumGraphAction09,
	EnumGraphAction10,
	EnumGraphAction11,
	EnumGraphAction12,
	EnumGraphAction13,
	EnumGraphAction14,
	EnumGraphAction15,
	EnumGraphAction16,
	EnumGraphAction17,

	EnumGraphActionClose,		
	EnumGraphActionWindow,		
	
	EnumActionEnd,
};

#define ACTION_MAX EnumGraphAction17+1


const WINDOW_DATA WindowDataAction = {
 0,															// メニューウィンドウ
     4, 575, 97, 61,260,0x80010101,EnumActionEnd,ActionSwitch, MenuWindowAction,MenuWindowActionDraw,MenuWindowActionDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateAction={
	1,
	 0,  0, 61, 25,
};

#endif