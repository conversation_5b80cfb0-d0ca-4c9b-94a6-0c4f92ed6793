﻿#include "../systeminc/system.h"
#ifdef DIRECT_MUSIC		//DirectMusicシステム

//-----------------------------------------------------------------------------
// File: GetDXVer.cpp
//
// Desc: Demonstrates how applications can detect what version of DirectX
//       is installed.
//
// (C) Copyright 1995-2001 Microsoft Corp.  All rights reserved.
//-----------------------------------------------------------------------------
#include <windows.h>
#include <windowsx.h>
#include <basetsd.h>
#include <ddraw.h>
#include <dinput.h>

#include <dmusici.h>

#include <tchar.h>

DWORD dwDXVersion;



typedef HRESULT(WINAPI * DIRECTDRAWCREATE)( GUID*, LPDIRECTDRAW*, IUnknown* );
typedef HRESULT(WINAPI * DIRECTDRAWCREATEEX)( GUID*, VOID**, REFIID, IUnknown* );
typedef HRESULT(WINAPI * DIRECTINPUTCREATE)( HINSTANCE, DWORD, LPDIRECTINPUT*,
                                             IUnknown* );




//-----------------------------------------------------------------------------
// Name: GetDXVersion()
// Desc: This function returns the DirectX version number as follows:
//          0x0000 = No DirectX installed
//          0x0100 = DirectX version 1 installed
//          0x0200 = DirectX 2 installed
//          0x0300 = DirectX 3 installed
//          0x0500 = At least DirectX 5 installed.
//          0x0600 = At least DirectX 6 installed.
//          0x0601 = At least DirectX 6.1 installed.
//          0x0700 = At least DirectX 7 installed.
//          0x0800 = At least DirectX 8 installed.
// 
//       Please note that this code is intended as a general guideline. Your
//       app will probably be able to simply query for functionality (via
//       QueryInterface) for one or two components.
//
//       Please also note:
//          "if( dwDXVersion != 0x500 ) return FALSE;" is VERY BAD. 
//          "if( dwDXVersion <  0x500 ) return FALSE;" is MUCH BETTER.
//       to ensure your app will run on future releases of DirectX.
//-----------------------------------------------------------------------------
DWORD GetDXVersion()
{
    DIRECTDRAWCREATE     DirectDrawCreate   = NULL;
    DIRECTDRAWCREATEEX   DirectDrawCreateEx = NULL;
    DIRECTINPUTCREATE    DirectInputCreate  = NULL;
    HINSTANCE            hDDrawDLL          = NULL;
    HINSTANCE            hDInputDLL         = NULL;
    HINSTANCE            hD3D8DLL           = NULL;
    HINSTANCE            hDPNHPASTDLL       = NULL;
    LPDIRECTDRAW         pDDraw             = NULL;
    LPDIRECTDRAW2        pDDraw2            = NULL;
    LPDIRECTDRAWSURFACE  pSurf              = NULL;
    LPDIRECTDRAWSURFACE3 pSurf3             = NULL;
    LPDIRECTDRAWSURFACE4 pSurf4             = NULL;
    DWORD                dwDXVersion        = 0;
    HRESULT              hr;

    // First see if DDRAW.DLL even exists.
    hDDrawDLL = LoadLibrary( "DDRAW.DLL" );                           //MLHIDE
    if( hDDrawDLL == NULL )
    {
        dwDXVersion = 0;
        OutputDebugString( "Couldn't LoadLibrary DDraw\r\n" );        //MLHIDE
        return dwDXVersion;
    }

    // See if we can create the DirectDraw object.
    DirectDrawCreate = (DIRECTDRAWCREATE)GetProcAddress( hDDrawDLL, "DirectDrawCreate" ); //MLHIDE
    if( DirectDrawCreate == NULL )
    {
        dwDXVersion = 0;
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't GetProcAddress DirectDrawCreate\r\n" ); //MLHIDE
        return dwDXVersion;
    }

    hr = DirectDrawCreate( NULL, &pDDraw, NULL );
    if( FAILED(hr) )
    {
        dwDXVersion = 0;
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't create DDraw\r\n" );             //MLHIDE
        return dwDXVersion;
    }

    // So DirectDraw exists.  We are at least DX1.
    dwDXVersion = 0x100;

    // Let's see if IID_IDirectDraw2 exists.
    hr = pDDraw->QueryInterface( IID_IDirectDraw2, (VOID**)&pDDraw2 );
    if( FAILED(hr) )
    {
        // No IDirectDraw2 exists... must be DX1
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't QI DDraw2\r\n" );                //MLHIDE
        return dwDXVersion;
    }

    // IDirectDraw2 exists. We must be at least DX2
    pDDraw2->Release();
    dwDXVersion = 0x200;


	//-------------------------------------------------------------------------
    // DirectX 3.0 Checks
	//-------------------------------------------------------------------------

    // DirectInput was added for DX3
    hDInputDLL = LoadLibrary( "DINPUT.DLL" );                         //MLHIDE
    if( hDInputDLL == NULL )
    {
        // No DInput... must not be DX3
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't LoadLibrary DInput\r\n" );       //MLHIDE
        return dwDXVersion;
    }

    DirectInputCreate = (DIRECTINPUTCREATE)GetProcAddress( hDInputDLL,
                                                        "DirectInputCreateA" ); //MLHIDE
    if( DirectInputCreate == NULL )
    {
        // No DInput... must be DX2
        FreeLibrary( hDInputDLL );
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't GetProcAddress DInputCreate\r\n" ); //MLHIDE
        return dwDXVersion;
    }

    // DirectInputCreate exists. We are at least DX3
    dwDXVersion = 0x300;
    FreeLibrary( hDInputDLL );

    // Can do checks for 3a vs 3b here


	//-------------------------------------------------------------------------
    // DirectX 5.0 Checks
	//-------------------------------------------------------------------------

    // We can tell if DX5 is present by checking for the existence of
    // IDirectDrawSurface3. First, we need a surface to QI off of.
    DDSURFACEDESC ddsd;
    ZeroMemory( &ddsd, sizeof(ddsd) );
    ddsd.dwSize         = sizeof(ddsd);
    ddsd.dwFlags        = DDSD_CAPS;
    ddsd.ddsCaps.dwCaps = DDSCAPS_PRIMARYSURFACE;

    hr = pDDraw->SetCooperativeLevel( NULL, DDSCL_NORMAL );
    if( FAILED(hr) )
    {
        // Failure. This means DDraw isn't properly installed.
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        dwDXVersion = 0;
        OutputDebugString( "Couldn't Set coop level\r\n" );           //MLHIDE
        return dwDXVersion;
    }

    hr = pDDraw->CreateSurface( &ddsd, &pSurf, NULL );
    if( FAILED(hr) )
    {
        // Failure. This means DDraw isn't properly installed.
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        dwDXVersion = 0;
        OutputDebugString( "Couldn't CreateSurface\r\n" );            //MLHIDE
        return dwDXVersion;
    }

    // Query for the IDirectDrawSurface3 interface
    if( FAILED( pSurf->QueryInterface( IID_IDirectDrawSurface3,
                                       (VOID**)&pSurf3 ) ) )
    {
        pSurf->Release();
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't QI DDS3\r\n" );                  //MLHIDE
        return dwDXVersion;
    }

    // QI for IDirectDrawSurface3 succeeded. We must be at least DX5
    dwDXVersion = 0x500;
    pSurf3->Release();


	//-------------------------------------------------------------------------
    // DirectX 6.0 Checks
	//-------------------------------------------------------------------------

    // The IDirectDrawSurface4 interface was introduced with DX 6.0
    if( FAILED( pSurf->QueryInterface( IID_IDirectDrawSurface4,
                                       (VOID**)&pSurf4 ) ) )
    {
        pSurf->Release();
        pDDraw->Release();
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't QI DDS4\r\n" );                  //MLHIDE
        return dwDXVersion;
    }

    // IDirectDrawSurface4 was create successfully. We must be at least DX6
    dwDXVersion = 0x600;
    pSurf4->Release();
    pSurf->Release();
    pDDraw->Release();

#if 0
	//-------------------------------------------------------------------------
    // DirectX 6.1 Checks
	//-------------------------------------------------------------------------

    // Check for DMusic, which was introduced with DX6.1
    LPDIRECTMUSIC pDMusic = NULL;
    CoInitialize( NULL );
    hr = CoCreateInstance( CLSID_DirectMusic, NULL, CLSCTX_INPROC_SERVER,
                           IID_IDirectMusic, (VOID**)&pDMusic );
    if( FAILED(hr) )
    {
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't create CLSID_DirectMusic\r\n" ); //MLHIDE
        return dwDXVersion;
    }

    // DirectMusic was created successfully. We must be at least DX6.1
    dwDXVersion = 0x601;
    pDMusic->Release();
    CoUninitialize();
    
	//-------------------------------------------------------------------------
    // DirectX 7.0 Checks
	//-------------------------------------------------------------------------

    // Check for DirectX 7 by creating a DDraw7 object
    LPDIRECTDRAW7 pDD7;
    DirectDrawCreateEx = (DIRECTDRAWCREATEEX)GetProcAddress( hDDrawDLL,
                                                       "DirectDrawCreateEx" ); //MLHIDE
    if( NULL == DirectDrawCreateEx )
    {
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't GetProcAddress DirectDrawCreateEx\r\n" ); //MLHIDE
        return dwDXVersion;
    }

    if( FAILED( DirectDrawCreateEx( NULL, (VOID**)&pDD7, IID_IDirectDraw7,
                                    NULL ) ) )
    {
        FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't DirectDrawCreateEx\r\n" );       //MLHIDE
        return dwDXVersion;
    }

    // DDraw7 was created successfully. We must be at least DX7.0
    dwDXVersion = 0x700;
    pDD7->Release();


	//-------------------------------------------------------------------------
    // DirectX 8.0 Checks
	//-------------------------------------------------------------------------

    // Simply see if D3D8.dll exists.
    hD3D8DLL = LoadLibrary( "D3D8.DLL" );                             //MLHIDE
    if( hD3D8DLL == NULL )
    {
	    FreeLibrary( hDDrawDLL );
        OutputDebugString( "Couldn't LoadLibrary D3D8.DLL\r\n" );     //MLHIDE
        return dwDXVersion;
    }

    // D3D8.dll exists. We must be at least DX8.0
    dwDXVersion = 0x800;


	//-------------------------------------------------------------------------
    // DirectX 8.1 Checks
	//-------------------------------------------------------------------------

    // Simply see if dpnhpast.dll exists.
    hDPNHPASTDLL = LoadLibrary( "dpnhpast.dll" );                     //MLHIDE
    if( hDPNHPASTDLL == NULL )
    {
	    FreeLibrary( hDPNHPASTDLL );
        OutputDebugString( "Couldn't LoadLibrary dpnhpast.dll\r\n" ); //MLHIDE
        return dwDXVersion;
    }

    // dpnhpast.dll exists. We must be at least DX8.1
    dwDXVersion = 0x801;


	//-------------------------------------------------------------------------
    // End of checking for versions of DirectX 
	//-------------------------------------------------------------------------

    // Close open libraries and return
    FreeLibrary( hDDrawDLL );
    FreeLibrary( hD3D8DLL );
    
#endif
    return dwDXVersion;

}

//-----------------------------------------------------------------------------
// DirectX６．０以上がちゃんとインストールされてるか
//-----------------------------------------------------------------------------
int check_dx_version( void )
{
    TCHAR* strResult;
	dwDXVersion = GetDXVersion();

    switch( dwDXVersion )
    {
        case 0x000:
            strResult = _T("No DirectX installed" );                  //MLHIDE
            break;
        case 0x100:
            strResult = _T("DirectX 1 installed" );                   //MLHIDE
            break;
        case 0x200:
            strResult = _T("DirectX 2 installed" );                   //MLHIDE
            break;
        case 0x300:
            strResult = _T("DirectX 3 installed" );                   //MLHIDE
            break;
        case 0x500:
            strResult = _T("DirectX 5 installed" );                   //MLHIDE
            break;
        case 0x600:
            strResult = _T("DirectX 6 installed" );                   //MLHIDE
            break;
        case 0x601:
            strResult = _T("DirectX 6.1 installed" );                 //MLHIDE
            break;
        case 0x700:
            strResult = _T("DirectX 7" );                             //MLHIDE
            break;
        case 0x800:
            strResult = _T("DirectX 8.0 installed" );                 //MLHIDE
            break;
        case 0x801:
            strResult = _T("DirectX 8.1 or better installed" );       //MLHIDE
            break;
        default:
            strResult = _T("Unknown version of DirectX installed." ); //MLHIDE
            break;
    }

#if 0
#ifdef _DEBUG
    MessageBox( NULL, strResult, "DirectX Version:",                  //MLHIDE
                MB_OK | MB_ICONINFORMATION );
#endif
#endif
    //DirectX5以下なら
	if( dwDXVersion <= 0x500 ){
		return 1;
	}
    return 0;
}
#endif	//DIRECT_MUSIC
