﻿//メニュー＞モンスター
BOOL MenuSwitchMonsterWitch( int no, unsigned int flag );

struct BattleStatus{

	int BattleIndex;		//战闘状态のモンスター
	int WalkIndex;			//おさんぽモンスター
	int StandbyIndex[3];	//战闘待机状态のモンスター
	int StandbyCount;		//スタンバイ状态のモンスターの数
	int PetCount;			//自分の连れているペット総数

};

BattleStatus BattleST;

//表示するモンスターナンバー
int MonsStatusNo;
int MonsStatusNoBack;
//モンスターの角度
int MonsStatusAng;
//バトルフラグワーク
int BattleSettingFlag[5];
//位置变更プロセスフラグ
struct MonsterMoveStruct{
	int Proc;
	int From;
	int To;
}MonsStatusMove;


void MenuWindowMonsterInit( void );

extern INIT_STR_STRUCT InitStrStructMonsterName;

void DrawMonsterPanel( short x, short y, int no, int FontPrio, int DispPrio, unsigned long rgba );
void DrawMyMonsterStatus( short x, short y, int no, int Prio );

//--------------------------------------------------------
//ログイン时の初期化
//--------------------------------------------------------
void InitMenuWindowMonsterInLogin(void){

	MonsStatusNo=0;
	MonsStatusAng=5;
	BattleSettingFlag[0]=0;
	BattleSettingFlag[1]=0;
	BattleSettingFlag[2]=0;
	BattleSettingFlag[3]=0;
	BattleSettingFlag[4]=0;
	BattleST.BattleIndex=0;
	BattleST.StandbyIndex[0]=0;
	BattleST.StandbyIndex[1]=0;
	BattleST.StandbyIndex[2]=0;
	BattleST.WalkIndex=0;
	MonsStatusMove.Proc=0;
	MonsStatusMove.From=-1;
	MonsStatusMove.To=-1;

}

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowMonster( int mouse )
{

	if (mouse==WIN_INIT){
		//位置变更初期化
		MonsStatusMove.Proc=0;
		MonsStatusMove.From=-1;
		MonsStatusMove.To=-1;
	}

	MenuWindowMonsterInit();

	return TRUE;
}

BOOL MenuWindowMonsterDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

BOOL MenuSwitchMonsterSWitch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	int i,j;
	int Num;

	switch(no){
		//详细画面へ
		case EnumBtMonsterToDetail00:
		case EnumBtMonsterToDetail01:
		case EnumBtMonsterToDetail02:
		case EnumBtMonsterToDetail03:
		case EnumBtMonsterToDetail04:

			Num=no-EnumBtMonsterToDetail00;

			// ドラッグしてるもの、ドロップされたものの确认
			if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
				if ( WinDD_CheckObjType() != WINDD_MONSTER ){
					flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
	#ifdef PUK2_NEWDRAG
					ReturnFlag = TRUE;
	#endif
				}
			}

			if ( flag & MENU_MOUSE_DROP ){
	#ifdef PUK2_NEWDRAG
				int DropitemNo = (int)WinDD_ObjData();
	#else
				int DropitemNo = (int)WinDD_GetObject();
	#endif

				MonsMove( MENU_WINDOW_MONSTER, no, DropitemNo, sortPet[Num].index );
	#ifdef PUK2_NEWDRAG
				WinDD_AcceptObject();
	#endif
			}

			if( flag & MENU_MOUSE_OVER ){
				strcpy( OneLineInfoStr, MWONELINE_PET_PANEL );
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFT ){
				if(pet[sortPet[Num].index].useFlag ){
					MonsStatusNo=Num;
					MonsStatusAng=5;
					if(WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo==NULL
						&&WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo==NULL
						&&WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo==NULL){
							openMenuWindow( MENU_WINDOW_MONSTER_STATUS, OPENMENUWINDOW_HIT, 0 );
					}else{
						//Name入れなおし
						if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0'){
							strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
						}else{
							strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
						}
						SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);
					}
				}
				WinDD_DragFinish();
				ReturnFlag=TRUE;
			}

			// 通常时
			else if ( (flag&MENU_MOUSE_OVER) && (flag&MENU_MOUSE_RIGHT) ){
				// その场所にモンスターがいるなら
				if ( pet[ sortPet[Num].index ].useFlag ){
					// ドラッグ开始
	#ifdef PUK2_NEWDRAG
					DragMons( sortPet[Num].index, 0 );
	#else
					WinDD_DragStart( WINDD_MONSTER, (void *)(sortPet[Num].index) );
	#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
	#ifdef PUK2_NEWDRAG
	#else
			// ドラッグ中
			else if ( WinDD_CheckObjType()==WINDD_MONSTER ){
				// ドラッグ元が自分なら
				if ( WinDD_WinType()==MENU_WINDOW_MONSTER && WinDD_ButtonNo()==no ){
					int DragitemNo = (int)WinDD_ObjData();
					// ドラッグ元にモンスターがいないならドラッグ終了
					if ( !pet[DragitemNo].useFlag ) WinDD_DragFinish();
					// 右键したらアイテムドロップ
					if ( (mouse.onceState&MOUSE_RIGHT_CRICK) || (flag&MENU_MOUSE_RIGHT) ){
						WinDD_DragFinish();
						WinDD_DropObject( WINDD_MONSTER, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
					}
				}
			}
	#endif
		
			MonsStatusMove.Proc = 0;
			MonsStatusMove.From = -1;
			if ( WinDD_CheckObjType()==WINDD_MONSTER ){
				int DragitemNo = (int)WinDD_ObjData();
		
	#ifdef PUK2_NEWDRAG
	#else
				// ドラッグ元が自分なら
				if ( WinDD_WinType()==MENU_WINDOW_MONSTER && WinDD_ButtonNo()==no ){
					struct BLT_MEMBER bm2 = {0};
		
					bm2.rgba.rgba=0x80ffffff;
					bm2.bltf=BLTF_NOCHG;
		
					// 掴んだモンスターの表示
					DrawMonsterPanel( mouse.nowPoint.x, mouse.nowPoint.y, DragitemNo, FONT_PRIO_DRAG, DISP_PRIO_DRAG, 0x80ffffff );
				}
	#endif
				MonsStatusMove.Proc = 1;
				MonsStatusMove.From = getsortMonsPos( DragitemNo );
			}
			break;

		//连れ歩き设定
		case EnumGraphMonsterWalk00:
		case EnumGraphMonsterWalk01:
		case EnumGraphMonsterWalk02:
		case EnumGraphMonsterWalk03:
		case EnumGraphMonsterWalk04:

			Num=no-EnumGraphMonsterWalk00;
			if(PackageVer>=PV_PUK2){
			//PUK2以降のユーザーの时
				//ペットメールスキル持ってるかチェック
				if(checkPetWalk(sortPet[Num].index)==1){
					//もってれば
					if( flag & MENU_MOUSE_OVER ){
						((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterWalkOver;
						if(pet[sortPet[Num].index].Walk){
							strcpy( OneLineInfoStr, MWONELINE_PET_WALK );
						}else{
							strcpy( OneLineInfoStr, MWONELINE_PET_WALK_OFF );
						}
							ReturnFlag=TRUE;
					}

					if( flag & MENU_MOUSE_LEFT ){
						if(pet[sortPet[Num].index].Walk){
							//このペットがすでに步いている时
							//步くのやめる
							nrproto_KS_send( sockfd,BattleSettingFlag[0],BattleSettingFlag[1],BattleSettingFlag[2],BattleSettingFlag[3],BattleSettingFlag[4]);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						}else{
							//このペットが步いてない时
							j=0;
							for(i=0;i<5;i++){
								if(pet[sortPet[i].index].Walk)
									j++;
							}
							if(j>0){
								//他に步いているのがいるのでダメ
							}else{
								//宠物忠诚小于60无法设置散步
								if (pet[sortPet[Num].index].hmg < 60){
									StockChatBufferLine(ML_STRING(830, "宠物忠诚度需达到60以上才可以带它散步喔!"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2);
									play_se(SE_NO_CLICK, 320, 240);
								}
								else {
									//步かせる
									BattleSettingFlag[sortPet[Num].index] |= PET_SETTING_WALK;
									nrproto_KS_send(sockfd, BattleSettingFlag[0], BattleSettingFlag[1], BattleSettingFlag[2], BattleSettingFlag[3], BattleSettingFlag[4]);
									BattleSettingFlag[sortPet[Num].index] &= ~PET_SETTING_WALK;
									// クリック音
									play_se(SE_NO_CLICK, 320, 240);
								}
							}
						}
					}
				}
			}else{
			//PUK2以前のユーザーの时
				wI->sw[no].Enabled=FALSE;
			}
			break;

		//战闘准备设定
		case EnumGraphMonsterStandBy00:
		case EnumGraphMonsterStandBy01:
		case EnumGraphMonsterStandBy02:
		case EnumGraphMonsterStandBy03:
		case EnumGraphMonsterStandBy04:

			Num=no-EnumGraphMonsterStandBy00;
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStandByOver;
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_STADBY){
					strcpy( OneLineInfoStr, MWONELINE_PET_STANDBY );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_PET_STANDBY_OFF );
				}
				ReturnFlag=TRUE;
			}
			
			if( flag & MENU_MOUSE_LEFT ){
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_FIELD){
					//フィールド状态の时は变更できません
					return ReturnFlag;
				}
				
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_STADBY){
					//このペットがすでに准备状态
					//レスト状态へ
					BattleSettingFlag[sortPet[Num].index] &= ~PET_SETTING_STADBY;	
					SendBattleStatus();
				}else{
	#ifdef PUK3_MONSTER_HELPER_CANCEL
					if ( BattleSettingFlag[sortPet[Num].index] == PET_SETTING_BATTLE ){
						CancelRideSkillCreate();
					}
	#endif
					//このペットが准备状态でない
					j=0;
					for(i=0;i<5;i++){
						if(BattleSettingFlag[i]==PET_SETTING_STADBY)
							j++;
						if(BattleSettingFlag[i]==PET_SETTING_BATTLE)
							j++;
					}
					if(j>=3){
						//满员ですので交代
						if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_BATTLE){
							//自分が战闘状态のときは自分が准备にうつるだけ
							BattleSettingFlag[sortPet[Num].index]=PET_SETTING_STADBY;
						}else{
							//他のスタンドバイ状态のモンスターをレスト状态に
							BattleSettingFlag[BattleST.StandbyIndex[0]] &= ~PET_SETTING_STADBY;	
							BattleSettingFlag[sortPet[Num].index] = PET_SETTING_STADBY;	
						}
						SendBattleStatus();
					}else{
						BattleSettingFlag[sortPet[Num].index] = PET_SETTING_STADBY;	
						SendBattleStatus();
					}
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}

			break;

		//战闘设定
		case EnumGraphMonsterBattle00:
		case EnumGraphMonsterBattle01:
		case EnumGraphMonsterBattle02:
		case EnumGraphMonsterBattle03:
		case EnumGraphMonsterBattle04:

			Num=no-EnumGraphMonsterBattle00;
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterBattleOver;
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_BATTLE){
					strcpy( OneLineInfoStr, MWONELINE_PET_BATTLE );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_PET_BATTLE_OFF );
				}
				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFT ){
		
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_FIELD){
					//フィールド状态の时は变更できません
					return ReturnFlag;
				}
				
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_BATTLE){
					//このペットがすでに战闘状态
					//レスト状态へ
					BattleSettingFlag[sortPet[Num].index] &= ~PET_SETTING_BATTLE;	
					SendBattleStatus();
				}else{
					//このペットが战闘状态でない
					if(BattleST.BattleIndex!=-1){
						//他に战闘状态がいるので交代
						BattleSettingFlag[BattleST.BattleIndex] &= ~PET_SETTING_BATTLE;	
						BattleSettingFlag[sortPet[Num].index] = PET_SETTING_BATTLE;	
						SendBattleStatus();
					}else{
						BattleSettingFlag[sortPet[Num].index] = PET_SETTING_BATTLE;	
						SendBattleStatus();
					}
				}
	#ifdef PUK3_MONSTER_HELPER_CANCEL
				CancelRideSkillCreate();
	#endif
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			break;
#ifdef PetField
		//モンスター地面に置く
		case EnumGraphMonsterField00:
		case EnumGraphMonsterField01:
		case EnumGraphMonsterField02:
		case EnumGraphMonsterField03:
		case EnumGraphMonsterField04:

			Num=no-EnumGraphMonsterField00;
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterFieldOver;

				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_FIELD){
					strcpy( OneLineInfoStr, MWONELINE_PET_FIELD );
				}else{
					strcpy( OneLineInfoStr, MWONELINE_PET_FIELD_OFF );
				}

				ReturnFlag=TRUE;
			}

			if( flag & MENU_MOUSE_LEFT ){
	#ifdef PUK3_MONSTER_HELPER_CANCEL
				if ( BattleSettingFlag[sortPet[Num].index] == PET_SETTING_BATTLE ){
					CancelRideSkillCreate();
				}
	#endif
				if(BattleSettingFlag[sortPet[Num].index]==PET_SETTING_FIELD){
					//このペットがすでにフィールド状态
					//レスト状态へ
					BattleSettingFlag[sortPet[Num].index] &= ~PET_SETTING_FIELD;	
					SendBattleStatus();
				}else{
#ifdef PUK3_VEHICLE
					// 乘り物移动中でないなら
					// もしくは见えないときでないなら
					if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
						 !nowVehicleProc() ){
						//このペットがフィールド状态でない
						BattleSettingFlag[sortPet[Num].index] = PET_SETTING_FIELD;	
						SendBattleStatus();
					}
#else
					//このペットがフィールド状态でない
					BattleSettingFlag[sortPet[Num].index] = PET_SETTING_FIELD;	
					SendBattleStatus();
#endif
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			break;
#endif
	}

	return ReturnFlag;

}

void MenuWindowMonsterInit( void ){

	char StrWork[256];
	int i,j,k;
	GRAPHIC_SWITCH *Graph;

	//バトルフラグ初期化(BattleSettingFlagの格纳顺はPet顺です)
	SetBattleStatus();

	for(k=0;k<MAX_PET;k++){
		if(pet[sortPet[k].index].useFlag){

			wI->sw[k*5+EnumTextMonsterName00].Enabled=TRUE;
			wI->sw[k*5+EnumTextMonsterLevel00].Enabled=TRUE;
			wI->sw[k*5+EnumTextMonsterLp00].Enabled=TRUE;
			wI->sw[k*5+EnumTextMonsterFp00].Enabled=TRUE;
			wI->sw[k*5+EnumTextMonsterType00].Enabled=TRUE;
			wI->sw[EnumGraphMonsterWalk00+k].Enabled=TRUE;
			wI->sw[EnumGraphMonsterStandBy00+k].Enabled=TRUE;
			wI->sw[EnumGraphMonsterBattle00+k].Enabled=TRUE;

			if(checkPetWalk(sortPet[k].index)==1)
				wI->sw[EnumGraphMonsterWalk00+k].Enabled=TRUE;

#ifdef PetField
			if(pet[sortPet[k].index].hmg==100 || BattleSettingFlag[sortPet[k].index]==PET_SETTING_FIELD){
				wI->sw[EnumGraphMonsterField00+k].Enabled=TRUE;
			}else{
				wI->sw[EnumGraphMonsterField00+k].Enabled=FALSE;
			}
#endif
			//名称
			if(pet[sortPet[k].index].freeName[0]== '\0'){
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterName00+5*k].Switch)->text,pet[sortPet[k].index].name);
			}else{
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterName00+5*k].Switch)->text,pet[sortPet[k].index].freeName);
			}

			if(BattleSettingFlag[sortPet[k].index]==3){
				//フィールド置き中は名称を灰色表示
				((TEXT_SWITCH *)wI->sw[EnumTextMonsterName00+5*k].Switch)->color=FONT_PAL_GRAY;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMonsterName00+5*k].Switch)->color=FONT_PAL_WHITE;
			}

			//等级
			sprintf( StrWork, "%3d",pet[sortPet[k].index].lv );										      //MLHIDE
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterLevel00+5*k].Switch)->text,StrWork);
			//LP
			sprintf( StrWork, "%6d/%6d",pet[sortPet[k].index].lp, pet[sortPet[k].index].maxLp );										 //MLHIDE
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterLp00+5*k].Switch)->text,StrWork);
			//FP
			sprintf( StrWork, "%6d/%6d", pet[sortPet[k].index].fp,pet[sortPet[k].index].maxFp );										 //MLHIDE
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterFp00+5*k].Switch)->text,StrWork);
			//TYPE
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterType00+5*k].Switch)->text,characterTribeStr[pet[sortPet[k].index].tribe]);

			//バトル状态パネル
			switch(BattleSettingFlag[sortPet[k].index]){
				//Rest
				case PET_SETTING_REST:
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStandBy00+k].Switch)->graNo=GID_MonsterStandByOff;
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterBattle00+k].Switch)->graNo=GID_MonsterBattleOff;
#ifdef PetField
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterField00+k].Switch)->graNo=GID_MonsterFieldOff;
#endif
				break;
				//StandBy
				case PET_SETTING_STADBY:
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStandBy00+k].Switch)->graNo=GID_MonsterStandByOn;
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterBattle00+k].Switch)->graNo=GID_MonsterBattleOff;
#ifdef PetField
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterField00+k].Switch)->graNo=GID_MonsterFieldOff;
#endif
				break;
				//Battle
				case PET_SETTING_BATTLE:
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStandBy00+k].Switch)->graNo=GID_MonsterStandByOff;
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterBattle00+k].Switch)->graNo=GID_MonsterBattleOn;
#ifdef PetField
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterField00+k].Switch)->graNo=GID_MonsterFieldOff;
#endif
				break;
#ifdef PetField
				//Field
				case PET_SETTING_FIELD:
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStandBy00+k].Switch)->graNo=GID_MonsterStandByOff;
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterBattle00+k].Switch)->graNo=GID_MonsterBattleOff;
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterField00+k].Switch)->graNo=GID_MonsterFieldOn;
				break;
#endif
			}

			//ペットメールスキル持ってるかチェック
			if(checkPetWalk(sortPet[k].index)==1){
				//连れ歩き
				if(pet[sortPet[k].index].Walk){
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWalk00+k].Switch)->graNo=GID_MonsterWalkOn;
				}else{
					((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWalk00+k].Switch)->graNo=GID_MonsterWalkOff;
				}
			}else{
				//スキルが无い时は非表示
				wI->sw[EnumGraphMonsterWalk00+k].Enabled=FALSE;
			}

			//属性表示
			//地
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterEarth0000+40*k+i].Enabled=FALSE;
			j=pet[sortPet[k].index].attr[0]/10;
			if(j==1){	//属性が１０のときは专用画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterEarth0000+40*k].Switch;
				Graph->graNo=GID_MonsterStatusEarthOnly;
				wI->sw[EnumGraphMonsterEarth0000+40*k].Enabled=TRUE;
			}else{
				for(i=0;i<j;i++){
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterEarth0000+40*k+i].Switch;
					Graph->graNo=GID_MonsterStatusEarthCenter;
					wI->sw[EnumGraphMonsterEarth0000+40*k+i].Enabled=TRUE;
					if(i==0){	//头画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterEarth0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusEarthLeft;
						wI->sw[EnumGraphMonsterEarth0000+40*k+i].Enabled=TRUE;
					}
					if(i==j-1){	//后ろ画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterEarth0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusEarthRight;
						wI->sw[EnumGraphMonsterEarth0000+40*k+i].Enabled=TRUE;
					}
				}
			}

			//水
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterWater0000+40*k+i].Enabled=FALSE;
			j=pet[sortPet[k].index].attr[1]/10;
			if(j==1){	//属性が１０のときは专用画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWater0000+40*k].Switch;
				Graph->graNo=GID_MonsterStatusWaterOnly;
				wI->sw[EnumGraphMonsterWater0000+40*k].Enabled=TRUE;
			}else{
				for(i=0;i<j;i++){
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWater0000+40*k+i].Switch;
					Graph->graNo=GID_MonsterStatusWaterCenter;
					wI->sw[EnumGraphMonsterWater0000+40*k+i].Enabled=TRUE;
					if(i==0){	//头画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWater0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusWaterLeft;
						wI->sw[EnumGraphMonsterWater0000+40*k+i].Enabled=TRUE;
					}
					if(i==j-1){	//后ろ画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWater0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusWaterRight;
						wI->sw[EnumGraphMonsterWater0000+40*k+i].Enabled=TRUE;
					}
				}
			}

			//火
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterFire0000+40*k+i].Enabled=FALSE;
			j=pet[sortPet[k].index].attr[2]/10;
			if(j==1){	//属性が１０のときは专用画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterFire0000+40*k].Switch;
				Graph->graNo=GID_MonsterStatusFireOnly;
				wI->sw[EnumGraphMonsterFire0000+40*k].Enabled=TRUE;
			}else{
				for(i=0;i<j;i++){
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterFire0000+40*k+i].Switch;
					Graph->graNo=GID_MonsterStatusFireCenter;
					wI->sw[EnumGraphMonsterFire0000+40*k+i].Enabled=TRUE;
					if(i==0){	//头画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterFire0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusFireLeft;
						wI->sw[EnumGraphMonsterFire0000+40*k+i].Enabled=TRUE;
					}
					if(i==j-1){	//后ろ画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterFire0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusFireRight;
						wI->sw[EnumGraphMonsterFire0000+40*k+i].Enabled=TRUE;
					}
				}
			}

			//风
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterWind0000+40*k+i].Enabled=FALSE;
			j=pet[sortPet[k].index].attr[3]/10;
			if(j==1){	//属性が１０のときは专用画像
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWind0000+40*k].Switch;
				Graph->graNo=GID_MonsterStatusWindOnly;
				wI->sw[EnumGraphMonsterWind0000+40*k].Enabled=TRUE;
			}else{
				for(i=0;i<j;i++){
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWind0000+40*k+i].Switch;
					Graph->graNo=GID_MonsterStatusWindCenter;
					wI->sw[EnumGraphMonsterWind0000+40*k+i].Enabled=TRUE;
					if(i==0){	//头画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWind0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusWindLeft;
						wI->sw[EnumGraphMonsterWind0000+40*k+i].Enabled=TRUE;
					}
					if(i==j-1){	//后ろ画像
						Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWind0000+40*k+i].Switch;
						Graph->graNo=GID_MonsterStatusWindRight;
						wI->sw[EnumGraphMonsterWind0000+40*k+i].Enabled=TRUE;
					}
				}
			}
		}else{
			//ペットがいないので非表示

			wI->sw[k*5+EnumTextMonsterName00].Enabled=FALSE;
			wI->sw[k*5+EnumTextMonsterLevel00].Enabled=FALSE;
			wI->sw[k*5+EnumTextMonsterLp00].Enabled=FALSE;
			wI->sw[k*5+EnumTextMonsterFp00].Enabled=FALSE;
			wI->sw[k*5+EnumTextMonsterType00].Enabled=FALSE;

			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterEarth0000+40*k+i].Enabled=FALSE;
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterWater0000+40*k+i].Enabled=FALSE;
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterFire0000+40*k+i].Enabled=FALSE;
			for (i=0;i<10;i++)
				wI->sw[EnumGraphMonsterWind0000+40*k+i].Enabled=FALSE;

			wI->sw[EnumGraphMonsterStandBy00+k].Enabled=FALSE;
			wI->sw[EnumGraphMonsterBattle00+k].Enabled=FALSE;
			wI->sw[EnumGraphMonsterWalk00+k].Enabled=FALSE;
#ifdef PetField
			wI->sw[EnumGraphMonsterField00+k].Enabled=FALSE;
#endif
		}
	}

	for(i=0;i<5;i++){
		if(MonsStatusMove.Proc==1 && MonsStatusMove.From==i){
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWaku00+i].Switch)->graNo=GID_MonsterListPanelOff;
		}else{
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterWaku00+i].Switch)->graNo=GID_MonsterListPanelOn;
		}
	}

}

//モンスターの状态セット
void SetBattleStatus(void){

	int i;

	//とりあえず初期化
	BattleST.BattleIndex=-1;
	BattleST.StandbyCount=0;
	BattleST.StandbyIndex[0]=-1;
	BattleST.StandbyIndex[1]=-1;
	BattleST.StandbyIndex[2]=-1;
	BattleST.WalkIndex=-1;

	for(i=0;i<5;i++){
		BattleSettingFlag[i]=pet[i].battleSetting;

		switch(pet[i].battleSetting){
			//バトル状态のモンスター
			case PET_SETTING_BATTLE:
				BattleST.BattleIndex=i;
				break;
			//スタンバイ状态のモンスター
			case PET_SETTING_STADBY:
				BattleST.StandbyIndex[BattleST.StandbyCount]=i;
				BattleST.StandbyCount++;
				break;
		}

		//おさんぽ状态のモンスター
		if(pet[i].Walk)
			BattleST.WalkIndex=i;
	}

}

//モンスターの状态を送信
void SendBattleStatus(void){

	int i;
	int SendWork[5];
	
	for(i=0;i<5;i++){
		//バトル状态取得
		SendWork[i]=BattleSettingFlag[i];
		//步いているペットがいたらフラグ付ける
		if(pet[i].Walk){
			SendWork[i] |= PET_SETTING_WALK;
		}
	}

	//送信
	nrproto_KS_send( sockfd,SendWork[0],SendWork[1],SendWork[2],SendWork[3],SendWork[4]);

}


#define MONSPANEL_XSIZ 300
#define MONSPANEL_YSIZ 48
#define MONSPANEL_HXSIZ (MONSPANEL_XSIZ/2)
#define MONSPANEL_HYSIZ (MONSPANEL_YSIZ/2)

int MonsBarGra[4][4] = {
	{ GID_MonsterStatusEarthLeft, GID_MonsterStatusEarthCenter, GID_MonsterStatusEarthRight, GID_MonsterStatusEarthOnly },
	{ GID_MonsterStatusWaterLeft, GID_MonsterStatusWaterCenter, GID_MonsterStatusWaterRight, GID_MonsterStatusWaterOnly },
	{ GID_MonsterStatusFireLeft, GID_MonsterStatusFireCenter, GID_MonsterStatusFireRight, GID_MonsterStatusFireOnly },
	{ GID_MonsterStatusWindLeft, GID_MonsterStatusWindCenter, GID_MonsterStatusWindRight, GID_MonsterStatusWindOnly },
};

void DrawMonsterPanel( short x, short y, int no, int FontPrio, int DispPrio, unsigned long rgba )
{
	struct BLT_MEMBER bm = {0};
	char str[255];
	int a,b;
	int i,j,k;

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	x -= MONSPANEL_HXSIZ,	y -= MONSPANEL_HYSIZ;

	if (pet[no].useFlag){
		// プライオリティの制御
		FontBufCut(FontPrio);
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );
	
		// 名称表示
		if( strlen( pet[no].freeName ) == 0 ){
			StockFontBuffer( x+8, y+6, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, pet[no].name, 0, 0 );
		}else{
			StockFontBuffer( x+8, y+6, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, pet[no].freeName, 0, 0 );
		}
		// 等级表示
		sprintf( str, "%3d", pet[no].lv );                                  //MLHIDE
		StockFontBuffer( x+141, y+6, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
		// 种族表示
		if ( (pet[no].tribe>=0) && (pet[no].tribe< (sizeof(characterTribeStr)>>2) ) ){
			StockFontBuffer( x+118, y+21, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, characterTribeStr[pet[no].tribe], 0, 0 );
		}
		// LP表示
		sprintf( str, "%6d/%6d", pet[no].lp, pet[no].maxLp );               //MLHIDE
		StockFontBuffer( x+37, y+19, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );
		// FP表示
		sprintf( str, "%6d/%6d", pet[no].fp, pet[no].maxFp );               //MLHIDE
		StockFontBuffer( x+37, y+32, FontPrio, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );

		//ペットメールスキル持ってるかチェック
		if( checkPetWalk(no) == 1 ){
			if(pet[no].Walk) StockDispBuffer_PUK2( x+244, y+26, DispPrio, GID_MonsterWalkOn, 0, 1, &bm );
			else StockDispBuffer_PUK2( x+244, y+26, DispPrio, GID_MonsterWalkOff, 0, 1, &bm );
		}
		if ( pet[no].battleSetting == PET_SETTING_STADBY ){
			StockDispBuffer_PUK2( x+270, y+26, DispPrio, GID_MonsterStandByOn, 0, 1, &bm );
		}else{
			StockDispBuffer_PUK2( x+270, y+26, DispPrio, GID_MonsterStandByOff, 0, 1, &bm );
		}
		if ( pet[no].battleSetting == PET_SETTING_BATTLE ){
			StockDispBuffer_PUK2( x+244, y+ 6, DispPrio, GID_MonsterBattleOn, 0, 1, &bm );
		}else{
			StockDispBuffer_PUK2( x+244, y+ 6, DispPrio, GID_MonsterBattleOff, 0, 1, &bm );
		}

		// 属性表示
		b = y+5;
		for(i=0;i<4;i++){
			k = pet[no].attr[i] / 10;
			a = x+175;
			if ( k > 1 ){
				StockDispBuffer_PUK2( a, b, DispPrio, MonsBarGra[i][0], 0, 1, &bm );
				a += 6;
				for(j=1;j<k-1;j++){
					StockDispBuffer_PUK2( a, b, DispPrio, MonsBarGra[i][1], 0, 1, &bm );
					a += 6;
				}
				StockDispBuffer_PUK2( a, b, DispPrio, MonsBarGra[i][2], 0, 1, &bm );
			}else if ( k == 1 ){
				StockDispBuffer_PUK2( a, b, DispPrio, MonsBarGra[i][3], 0, 1, &bm );
			}
			b += 10;
		}
	}

	StockDispBuffer( x+MONSPANEL_HXSIZ, y+MONSPANEL_HYSIZ, DispPrio, GID_MonsterListPanelOn, 0, &bm );
}
