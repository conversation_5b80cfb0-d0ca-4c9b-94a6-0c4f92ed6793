﻿#ifndef _CHARACTER_H_
#define _CHARACTER_H_

#include"action.h"
#include"pc.h"

enum
{
	CHR_STATUS_P 		= 0x0001,			// 毒
	CHR_STATUS_N 		= 0x0002,			// 麻痹
	CHR_STATUS_Q 		= 0x0004,			// 沈黙
	CHR_STATUS_S 		= 0x0008,			// 石化
	CHR_STATUS_D 		= 0x0010,			// 暗闇
	CHR_STATUS_C 		= 0x0020,			// 混乱
	CHR_STATUS_W 		= 0x0040,			// この上を步けるか
	CHR_STATUS_H 		= 0x0080,			// 高さがあるか
	CHR_STATUS_LEADER	= 0x0100,			// リーダー
	CHR_STATUS_PARTY	= 0x0200,			// 布ティの一员
	CHR_STATUS_BATTLE	= 0x0400,			// 战闘中
	CHR_STATUS_USE_MAGIC= 0x0800,			// 咒术使用
	CHR_STATUS_HELP		= 0x1000,			// ヘルプ
	CHR_STATUS_FUKIDASHI= 0x2000,			// 吹き出し
	CHR_STATUS_WATCH	= 0x4000,			// 観战中
	CHR_STATUS_DUEL		= 0x8000			// デュエル
};

#ifdef PUK2
	enum
	{
		CHR_STATUS2_HEALTH 	= 0x0001,			// 怪我
		CHR_STATUS2_SKILL	= 0x0002,			// スキル
		CHR_STATUS2_LEVELUP	= 0x0004,			// 等级アップ
		CHR_STATUS2_PETHEALTH 	= 0x0008,		// 怪我
		CHR_STATUS2_PETLEVELUP	= 0x0010,		// 等级アップ
		CHR_STATUS2_LEVELUPSE	= 0x0020,		// 等级アップの音
		CHR_STATUS2_PETLEVELUPSE= 0x0040,		// 等级アップの音
	#ifdef PUK3_VEHICLE
		CHR_STATUS2_INVISIBLE	= 0x0080,		// 见えない
	#endif
	#ifdef PUK3_NOEXISTCHARA
		CHR_STATUS2_UNKNOWN		= 0x0100,		// 不明なキャラ(キャラが存在しなかったときの假キャラ)
	#endif
	};
#endif

enum
{											// キャラ管理时の种类
	CHAROBJ_TYPE_NPC		= 0x0001,		// NPC
	CHAROBJ_TYPE_ITEM		= 0x0002,		// アイテム
	CHAROBJ_TYPE_MONEY		= 0x0004,		// お金
	CHAROBJ_TYPE_USER_NPC	= 0x0008,		// 他人のキャラクタ
	CHAROBJ_TYPE_LOOKAT		= 0x0010,		// 见れる
	CHAROBJ_TYPE_PARTY_OK	= 0x0020,		// 布ティに入れる
#ifdef PUK2
	CHAROBJ_TYPE_GUILMON	= 0x0100,		// 家族モンスター
#endif
#ifdef PUK3_NOEXISTCHARA
	CHAROBJ_TYPE_UNKNOWN	= 0x0200,		// 不明なキャラ（キャラ居なかったとき作った假キャラ））
#endif
	CHAROBJ_TYPE_ALL		= 0x00FF		// 全て
};


// サーバ上での管理番号
enum
{
#ifdef PUK2
	CHAR_TYPENONE,          /*  何でもない  */
 	CHAR_TYPEPLAYER,        /*  プレイヤー  */
 	CHAR_TYPEENEMY,         /*  敌      */
 	CHAR_TYPEPET,           /*  ペット */
	CHAR_TYPEDOOR,          /*  ドア   */
	CHAR_TYPEBOX ,          /*  宝箱 */
 	CHAR_TYPEMSG ,          /*  看板 */
	CHAR_TYPEWARP ,         /*  ワープゾーン */
	CHAR_TYPESHOP ,         /*  店 */
	CHAR_TYPEHEALER ,       /*  ヒーラー */
	CHAR_TYPEOLDMAN ,       /*  长老 */
	CHAR_TYPEROOMADMIN,     /*  不动产屋 */
	CHAR_TYPETOWNPEOPLE,    /*  まちのひと */
	CHAR_TYPEDENGON,        /*  伝言版  */
	CHAR_TYPEADM,           /*  伝言版  */
	CHAR_TYPETEMPLE,        /*  Temple master */
	CHAR_TYPESTORYTELLER,   /*  语り部  */
	CHAR_TYPERANKING,       /*  不动产ランキング表示ＮＰＣ  */
	CHAR_TYPEOTHERNPC,      /*  その他の检索对象にならないNPC*/
	CHAR_TYPEPRINTPASSMAN,  /* ドアのパスワード表示するNPC */
	CHAR_TYPENPCENEMY,      /* 固定敌 */
	CHAR_TYPEACTION,        /* アクションに反应するNPC */
	CHAR_TYPEWINDOWMAN,		/* ウィンドウ表示するNPC（テストかも）*/
	CHAR_TYPESAVEPOINT,		/* セーブポイント */
	CHAR_TYPEWINDOWHEALER,	/* ウインドウ种类のヒーラー*/
	CHAR_TYPEITEMSHOP,		/* お店 */
	CHAR_TYPESTONESHOP,		/* 石盘屋（ペットの技屋） */
	CHAR_TYPEDUELRANKING,	/* DUELランキングNPC */
	CHAR_TYPEWARPMAN,		/* ワープマンNPC */
	CHAR_TYPEEVENT,			/* イベントNPC */
	CHAR_TYPEMIC,			/* マイクNPC */
	CHAR_TYPELUCKYMAN,		/* 占いNPC */
	CHAR_TYPEBUS,			/* マンモスバス */
	CHAR_TYPECHARM,			/* 魅力回复NPC */
	CHAR_TYPESKILLMASTER,
	CHAR_TYPEJOBSMASTER,
	CHAR_TYPEBOLTAC,
	CHAR_TYPEINJURYDOCTOR,	/* 怪我专用医者 */
	CHAR_TYPEJUDGEMAN,		/* 鉴定屋 */
	CHAR_TYPEREPAIRMAN,		/* 修理屋 */
	CHAR_TYPESTANDENEMY,	/* 固定敌 */
	CHAR_TYPEBOSSBOX,		/* 固定敌の宝箱 */
	CHAR_TYPEPILOT,			/* ボスへ战に必要なアイテムをくれるNPC */
	CHAR_TYPESIMPLETRADE,	/* 简单なアイテム交换ＮＰＣ */
	CHAR_TYPEMINEMASTER,	/* 采掘场管理者 */
	CHAR_TYPEHOUSEMASTER,	/* 家管理者 */
	CHAR_TYPEHOUSEWARP,		/* 家用ワープ */
	CHAR_TYPEBANK,			/* 银行NPC */
	CHAR_TYPEBILLBOARD,		/* 看板NPC */
	CHAR_TYPETECHSHOP,		/* テックショップNPC */
	CHAR_TYPEQUIZ,			/* クイズNPC */
	CHAR_TYPETITLEMAN,		/* 称号NPC */
	CHAR_TYPESPIRITMAN,		/* 魂NPC */
	CHAR_TYPEBRUSHMAN,		/* たわしNPC */
	CHAR_TYPETIMECARD,		/* タイムカードNPC */
	CHAR_TYPEGOLDRANKING,	/* GOLDランキングNPC */
	CHAR_TYPEDISEMPLOYMENT,	/* 无职ＮＰＣ */
	CHAR_TYPEPETSHOP,		/* ペットショップＮＰＣ */
	CHAR_TYPEEQUIPMAN,		/* 装备确认ＮＰＣ */
	CHAR_TYPEWINDOWHEALER2,	/* ウインドウ种类のヒーラー２*/
	CHAR_TYPEHOUSEWARP2,	/* 家ワープ２*/
	CHAR_TYPEDUSTBOX,		/* ごみ箱 */
	CHAR_TYPEWATCHEVENT,	/* ウォッチイベントNPC */
	CHAR_TYPEITEMCOUNT,		/* アイテムカウントNPC */
	CHAR_TYPEGUILDNEW,		/* guildCreater */
	CHAR_TYPEGUILDDEL,		/* guildEraser */
	CHAR_TYPEGUILDWARP,		/* 家族ルーム用ワープ */
	CHAR_TYPEGUILDMONSTERMASTER,	/* guildMonsterMaster */
	CHAR_TYPESEPLAY,		/* seplay */
	CHAR_TYPEWATCHEX,		/* 扩张watchEvent */
	CHAR_TYPEITEMBOX,		/* アイテムボックス */
	CHAR_TYPEGUILDMONSTER,		/* 家族モンスター */
	CHAR_TYPEFOODBOX,		/* えさ箱 */
#else
	CHAR_TYPENONE,          // 何でもない
	CHAR_TYPEPLAYER,        // プレイヤー
	CHAR_TYPEENEMY,         // 敌
	CHAR_TYPEPET,           // ペット
	CHAR_TYPEDOOR,          // ドア
	CHAR_TYPEBOX ,          // 宝箱
	CHAR_TYPEMSG ,          // 看板
	CHAR_TYPEWARP ,         // ワープゾーン
	CHAR_TYPESHOP ,         // 店
	CHAR_TYPEHEALER ,       // ヒーラー
	CHAR_TYPEOLDMAN ,       // 长老
	CHAR_TYPEROOMADMIN,     // 不动产屋
	CHAR_TYPETOWNPEOPLE,    // まちのひと
	CHAR_TYPEDENGON,        // 伝言版
	CHAR_TYPEADM,           // 伝言版
	CHAR_TYPETEMPLE,        // Temple master
	CHAR_TYPESTORYTELLER,   // 语り部
	CHAR_TYPERANKING,       // 不动产ランキング表示ＮＰＣ
	CHAR_TYPEOTHERNPC,      // その他の检索对象にならないNPC
	CHAR_TYPEPRINTPASSMAN,  // ドアのパスワード表示するNPC
	CHAR_TYPENPCENEMY,      // 固定敌
	CHAR_TYPEACTION,        // アクションに反应するNPC
	CHAR_TYPEWINDOWMAN,     // ウィンドウ表示するNPC（テストかも)
	CHAR_TYPESAVEPOINT,     // セーブポイント
	CHAR_TYPEWINDOWHEALER,  // ウインドウ种类のヒーラー
	CHAR_TYPEITEMSHOP,      // お店
	CHAR_TYPESTONESHOP,     // 石盘屋（ペットの技屋）
	CHAR_TYPEDUELRANKING,   // DUELランキングNPC
	CHAR_TYPEWARPMAN,       // ワープマンNPC
	CHAR_TYPEEVENT,         // イベントNPC
	CHAR_TYPEMIC,           // イベントNPC
	CHAR_TYPELUCKYMAN,      // イベントNPC
	CHAR_TYPEBUS,           // マンモスバス
	CHAR_TYPECHARM,         // イベントNPC
#endif
	CHAR_TYPENUM,
};

#ifdef PUK3_CHARA_ACT
enum{
	CHARACT_NOMAL,		// 通常行动
	CHARACT_RIDE_ON,	// ペットに乘る
	CHARACT_GET_OFF,	// ペットから降りる
	#ifdef PUK3_VEHICLE
		CHARACT_VEHICLE_RETURN,	// 乘り物が归还する
	#endif
};
#define RIDE_JMPCNT_HALF 15
#endif
#ifdef PUK3_RIDE
	#define RIDE_SE_GETON_START		465
	#define RIDE_SE_GETON_END		462
	#define RIDE_SE_GETOFF_START	463
	#define RIDE_SE_GETOFF_END		457
#endif

typedef struct
{
	short use;				// 使用中か
	short type;				// 种类
	int id;					// サーバの管理番号
	int graNo;				// グラフィック番号
	int gx, gy;				// マップの座标
	int level;				// 等级
	int dir;				// 向き
	int stockDir;			// 移动后に向き变更があれば变更する
	int stockDirX, stockDirY;// stockDirを适用する座标
	short nameColor;		// 名称表示时の色
	unsigned short status;	// 状态
	int classNo;			// アイテム用（？）
	int money;				// お金用
	int num;				// アイテムの个数
	char name[CHAR_NAME_LEN+1];			// 名称
	char freeName[CHAR_FREENAME_LEN+1];	// ユーザが付けた自称
#ifdef PUK2
	char guildName[32+1];		// 家族名
	char guildTitle[32+1];	// 家族称号名
#endif
	char title[CHAR_TITLE_LEN+1];		// 称号
	char info[64];			// １行信息（ACTIONに入れたらいらないかも）
	int battleNo;			// 战闘番号
	short sideNo;			// 战闘时にどちらサイドに属しているか
	short helpMode;			// ヘルプモード
	int charType;			// アクションに设定する属性
	short newFoundFlag;		// 新発见の怪獣かをチェックするかのフラグ
	ACTION *ptAct;
#ifdef PUK2
	unsigned short status2;	// 状态
	int headNo;				// 表示するスキルアイコンの番号
#endif
} CHAROBJ;


typedef struct
{
	int charObjTblId;			// 管理テーブルの何番目に入っているか
	ACTION *ptActLeaderMark;	// リーダーマーク表示用
#ifdef PUK3_CHANGE_ICONACTIONPOINTER
	ACTION *ptActMagicEffect;	// ライドの召唤エフェクトなど
#else
	ACTION *ptActMagicEffect;	// 咒术エフェクト
#endif
#ifdef PUK2
	ACTION *ptActLevelup;		// 等级アップマーク表示用
	ACTION *ptActInjury;		// 怪我マーク表示用
#endif
	unsigned int drawFukidashiTime;	// 吹き出しを出す时间
	int num;					// アイテムの个数

#ifdef PUK2
	// 信息关连
	char	*msg;						// 信息データのポインタ
	int		msg_col;					// 信息の表示色
	int		msg_num;					// 信息の文字数（半角）
	int		msg_kind;					// フォントの种类

	ACTION	*ptPet;						// 连れ歩き用ペットのアクション
#endif
#ifdef PUK3_PUT_ON
	ACTION	*ptPuton;					// 被り物
#endif
#ifdef PUK3_RIDE
	ACTION	*ptRide;					// ペットライド
	int		actCnt;						// アクション制御用カウンタ
#endif
#ifdef PUK3_CHANGE_ICONACTIONPOINTER
	ACTION *ptActIcon;					// スキル等のアイコン
#endif
} CHAREXTRA;


typedef struct
{
	short mode;					// ペットの动作モード
	short moveDir;				// 移动时に向かう方向
	short preDir;				// ひとつ前に曲がった向き
	short dirCnt;				// 连続で曲がった回数
	int preGx, preGy;			// ひとつ前のグリッド
	int walkCnt;				// 移动步数
	unsigned int createTime;	// 作成时间
	ACTION *ptAct;
	ACTION *ptAct2;
	float angle;
	int r;
	int id;						// サーバの管理番号
	int drawFlag;
	int flipCnt;
	int flashCnt;
	int flashWaitCnt;
	int h;
	int th;
	int bmpNo[10];
	int x[10];
	int y[10];
	float mx[10];
	float my[10];
#ifdef PUK3_MM_MEMLEAK
	unsigned long flag;
#endif
} PETEXTRA;
#ifdef PUK3_MM_MEMLEAK
	#define PE_FLAG_USE_PTACT1 (1<<0)
	#define PE_FLAG_USE_PTACT2 (1<<1)
#endif


extern short nameOverTheHeadFlag;


void charMove( ACTION * );
void charMove2( ACTION * );
void _charMove( ACTION * );
void shiftBufCount( ACTION * );

ACTION *createCharAction( int, int, int, int );
void stockCharMovePoint( ACTION *, int, int );
void correctCharMovePoint( ACTION *, int, int );
#ifdef PUK3_RIDE
float setCharMovePoint( ACTION *ptAct, int nextGx, int nextGy, int walkSpeed = 100, float l_rate = 0 );
void _setCharMovePoint( ACTION *ptAct, int nextGx, int nextGy, int walkSpeed = 100 );
#else
void setCharMovePoint( ACTION *, int, int );
void _setCharMovePoint( ACTION *, int, int );
#endif
void setCharWarpPoint( ACTION *, int, int );
void setCharStatus( unsigned short *, char * );

ACTION *createCommmonEffectAction( int, int, int, int, int, int );
#ifdef PUK3_MM_MEMLEAK
ACTION *createCommmonEffectNoLoop( int graNo, int gx, int gy, int anim, int dir, int prio, ACTION **pActParent = NULL );
#else
ACTION *createCommmonEffectNoLoop( int, int, int, int, int, int );
#endif
#ifdef PUK2_DRESS_UP
void drawCharStatus( ACTION *ptAct, int charNum );
#else
void drawCharStatus( ACTION * );
#endif

#ifdef PUK3_PUT_ON
void changeCharAct( ACTION *, int, int, int, int, int, int );
#else
void changeCharAct( ACTION *, int, int, int, int, int );
#endif
#ifdef PUK3_VEHICLE
	// 乘り物关系の动作变更
	BOOL changeVehicleAct( int id, int x, int y, int dir, int action, char *data );
#endif
#ifdef PUK3_RIDE
	// キャラのアニメーションを设定する
	void setRiderAnimeNo( ACTION *pAct, int anim_no, BOOL InitAct );
#endif

void setCharLeader( ACTION * );
void delCharLeader( ACTION * );
void setCharParty( ACTION * );
void delCharParty( ACTION * );
void setCharWatch( ACTION * );
void delCharWatch( ACTION * );
void setCharBattle( ACTION *, int, short, short );
void delCharBattle( ACTION * );
void setCharUseMagic( ACTION * );
void delCharUseMagic( ACTION * );
void setCharDuel( ACTION * );
void delCharDuel( ACTION * );

#ifdef PUK2
// キャラに怪我情报を出す
void setCharHealth( ACTION *ptAct );
// キャラに怪我情报を消す
void delCharHealth( ACTION *ptAct );
// キャラをスキル使用中にする
void setCharSkill( ACTION *ptAct, int headNo );
// キャラをスキル使用中から元に戾す
void delCharSkill( ACTION *ptAct );
// キャラに等级アップマークを表示
void setCharLevelup( ACTION *ptAct );

// ペットに怪我情报を出す
void setCharPetHealth( ACTION *ptAct );
// ペットに怪我情报を消す
void delCharPetHealth( ACTION *ptAct );
// ペットに等级アップマークを表示
void setCharPetLevelup( ACTION *ptAct );
#endif
#ifdef PUK3_VEHICLE
	// キャラを不可视にする
	void setCharInvisible( ACTION *ptAct );
	// キャラを不可视から元に戾す
	void delCharInvisible( ACTION *ptAct );
#endif


#ifdef PUK2
void setCharFukidashi( ACTION *, unsigned int, unsigned char *, int, int, int );
#else
void setCharFukidashi( ACTION *, unsigned int );
#endif

#ifdef PUK3_CHARA_ACT
	// 呼びもとの关数が終了して欲しいならTRUE、続行して欲しいならFALSE
	BOOL charAction( ACTION *pAct );
#endif
void charObjProc( void );
BOOL checkCharObjPoint( int, int, short );
#ifdef PUK3
int getUserNpcID( int gx, int gy, short type );
#endif
#ifdef PUK2
int checkCharObjPointStatus( int gx, int gy, short type, unsigned short status, unsigned short status2=0 );
int checkCharObjPointNotStatus( int gx, int gy, short type, unsigned short status, unsigned short status2=0 );
#else
int checkCharObjPointStatus( int, int, short, unsigned short );
int checkCharObjPointNotStatus( int, int, short, unsigned short );
#endif
void setNpcCharObj( int, int, int, int, int, char *, char *, char *, int, int, int, int, int );
BOOL setReturnPetObj( int, int, int, int, int, char *, char *, int, int, int, int, int );
void setItemCharObj( int, int, int, int, int, int, char *, int );
void setMoneyCharObj( int, int, int, int, int, int, char * );
int getAtrCharType( int );
int getAtrCharObjType( int );
void delCharObj( int );
void initCharObj( void );
void resetCharObj( void );
void clearPtActCharObj( void );
void restorePtActCharObjAll( void );
void restorePtActCharObj( int );
void setMovePointCharObj( int, int, int );
ACTION *getCharObjAct( int );

int getCharObjBuf( void );

void initItemOverlapCheck( void );
BOOL itemOverlapCheck( unsigned int, int, int );

ACTION *createPetAction( int, int, int, int, int, int, int );
void ajustClientDir( short * );
// 船のアクション作成
void create_ship( int graNo, int map, int floor, int gx, int gy );

#ifdef _FISHING_WINDOW
void CreateFishingShip( int id);
#endif /* _FISHING_WINDOW */

#ifdef PUK2
	void changeNpcCharDir( ACTION *ptAct );
	// キャラの状态を表示
	void CharaStatusDisp( ACTION *pAct, short ofx, short ofy, unsigned long rgba=0xffffffff );
#endif
#ifdef PUK2_NEW_PETTAKE
	// 戾り值で、次のトークンを指す数を返す
	int RecvCharaDataEx( ACTION *pAct, char *bigtoken, int starttoken );
#endif
#ifdef PUK3_PUT_ON
	ACTION *makePuton( int graNo );
#endif

#endif
#ifdef PUK3_VEHICLE
	// 乘っている乘り物のアクションを取得
	ACTION *getRideVehicle( int id );

	// 乘り物リスト初期化
	void VehicleInit();
	// 现存の乘り物を全て削除
	void delVehicleAll();

	// 现在乘り物移动中のプロセスかを取得
	BOOL nowVehicleProc();

	// 乘り物移动中プロセスの初期化关数warpEffectProc内用
	void vehicleProcInit_in_warpEffectProc();
	// 乘り物移动中プロセスの初期化关数
	int vehicleProcInit();
	// 乘り物移动中プロセス
	void vehicleProc();
	// 乘り物移动終了处理
	void vehicleProcEnd();

	ACTION *createTestVehicleAction( int graNo, int gx, int gy, int dir );
	ACTION *createVehicleDepartureAction( int graNo, int gx, int gy, int dir, int time );
	ACTION *createVehicleArrivalAction( int graNo, int gx, int gy, int dir );
	void setVehicleRider( ACTION *pAct,
		 int riderId1, int riderId2, int riderId3, int riderId4, int riderId5 );
#endif

#ifdef PUK2
extern BOOL CharHPLPStatusFlag;
extern BOOL CharFukidashiFlag;
#endif

