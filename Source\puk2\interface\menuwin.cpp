﻿
#ifdef PUK2_MEMCHECK
	#define WINDOW_CREATE 1
#else
	#define WINDOW_CREATE 1
#endif

#define MENU_WINDOW_MENU_Y	( 456 - 32 )

#include "menuwin.h"
#include "../../systeminc/handletime.h"
#include "../../systeminc/loadrealbin.h"

#include "MenuCockpitR.h"
#include "MenuCockpitL.h"
#include "MenuMenu.h"
#include "MenuStatus.h"
#include "MenuSkill.h"
#include "MenuItem.h"
#include "MenuMonster.h"
#include "MenuMonsterStatus.h"
#include "MenuAlbum.h"
#include "MenuAddress.h"
#include "MenuSystem.h"
#include "MenuMap.h"
#include "MenuAction.h"
#include "MenuBattle.h"
#include "MenuGeneral.h"
#include "MenuServerrequest.h"
#include "menuAddressBook.h"
#include "menuMail.h"
#include "menuMultiPurpose.h"
#include "MenuChat.h"
#include "MenuBank.h"
#include "MenuGuild.h"
#include "menuGuildInfo.h"
#include "MenuTrade.h"
#include "menuGroupMail.h"
#include "MenuBBS.h"
#include "MenuShop.h"
#include "MenuOrthopedist.h"

#include "MenuShortCut.h"

#include "MenuWinFile.h"

#ifdef PUK3
#ifdef PUK3_PROF
#include "../puk3/profile/profile.h"
#include "../puk3/interface/menuprofile.h"
#endif
#ifdef PUK3_ACCOUNT
#include "../puk3/account/account.h"
#endif
#endif

void MenuChatStockFontBuff(int,int);
void NewChatAreaProc( void );
void MenuWindowShortCut(void);
ACTION *createMenuAction( int graNo, int gx, int gy );

//变数宣言--------------------------------------------------------------------------------
// マウス关连
int MouseNoHitFlag;
extern NRTIME nrTime;				// NRの时间管理

//ウインドウの座标系デバッグ机能フラグ
int NewWinDebugFlag;

// ウィンドウ管理构造体
WINDOW_INFO *WindowBuff[MENU_WINDOW_MAX];
int		MenuWinMax;
WINDOW_FLAG WindowFlag[ MENU_WINDOW_TYPE_NUM ];

// 现在处理中のウィンドウのポインタ
// （スイッチの制御で、いちいち受け渡すの面倒なので）
WINDOW_INFO *wI;
WINDOW_FLAG *wF;

//ダイアログ管理构造体
STRUCT_DIAROG_STATUS DiarogST;

extern char ItemNoOpe[MAX_ITEM];		// アイテムの使用フラグ
extern int UsingItemNo;
extern char UsingItemCnt;

extern ACTION *pActMonsSW;
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	int Lock3DChangeWindowCnt = 0;		// 描画机能切り替えに邪魔なウィンドウの数
#endif

// ドラッグドロップ管理构造体の动作状态
enum WINDD_PROCNO{
	WINDD_DONOT,			// 何もしてない
	WINDD_DRAGGING,			// ドラッグ中
#ifdef PUK2_NEWDRAG
	WINDD_DRAGCANCEL,		// ドラッグをキャンセル
#endif
	WINDD_DROPENTRY,		// ドロップされた
	WINDD_DROPSEEKING,		// ドロップされたものの受け取り先を调查中
	WINDD_DROPRETURN,		// ドロップされたものを、ドロップ元へ返す
#ifdef PUK2_NEWDRAG
	WINDD_DROPCANCEL,		// ドロップをキャンセル(谁にも受け取られなかった)
#endif
};
// ドラッグドロップ管理构造体
struct WINDOW_DRAGDROP{
	enum WINDD_OBJECTTYPE ObjType;			// ドラッグしているもの、ドロップしたものの种类
	void *ObjData;							// ドロップしたもののデータ
#ifdef PUK2_NEWDRAG
	void (*DragFunc)( int ProcNo, unsigned int flag, void *ObjData );	// ドロップしたものが受け取られたときの后始末用关数
#else
	void (*SettleDropItem)();				// ドロップしたものが受け取られたときの后始末用关数
#endif
	short DropX, DropY;						// ドロップポイント
	int WinType;							// ドラッグ??ドロップ元のウィンドウ种类
	int SwitchNo;							// ドラッグ??ドロップ元のボタン番号

	enum WINDD_PROCNO ProcNo;				// この构造体の动作状态
};

// ドラッグドロップ管理构造体
static struct WINDOW_DRAGDROP WindowDD = { WINDD_NONE, NULL, NULL, 0, 0, MENU_WINDOW_NONE, -1, WINDD_DONOT };
// 现在处理中のウィンドウ种类、ボタンの番号
static int nowWinType = MENU_WINDOW_NONE, nowBtnNo = -1;

//ダミー
//ダミーウインドウ
const WINDOW_DATA WindowDataNULL = {
 0,	0, 0, 0, 0, 0, 0x00000000,  0, NULL , NULL , NULL
};

//ダミードラッグ
static WINDOW_DRAGMOVE DragMoveNULL={
0
};

// ウィンドウ设定データ
const WINDOW_DATA *WindowData[] = {
	&WindowDataNULL,				//ダミー
	&WindowDataMenuLeftLarge,		//左コンソール大
	&WindowDataMenuLeftMiddle,		//左コンソール中
	&WindowDataMenuLeftSmall,		//左コンソール小
	&WindowDataMenuRightLarge,		//右コンソール大
	&WindowDataMenuRightSmall,		//右コンソール小
	&WindowDataMenuMenu,			//メニュー
	&WindowDataMenuStatus,			//状态状态
	&WindowDataMenuDetail,			//状态ディティール
	&WindowDataMenuTitle,			//状态タイトル
	&WindowDataMenuSkill,			//スキルウインドウ
	&WindowDataMenuSkillCreate,		//スキルクリエートウィンドウ
	&WindowDataMenuSkillGather,		//スキル采取ウィンドウ
	&WindowDataMenuSkillOthers,		//スキルその他结果ウィンドウ
	&WindowDataMenuItem,			//アイテムウインドウ
	&WindowDataMenuMonster,			//モンスターウインドウ
	&WindowDataMonsterStatus,		//モンスター状态ウインドウ
	&WindowDataMonsterDetail,		//モンスターディティールウインドウ
	&WindowDataMonsterSkill,		//モンスタースキルウインドウ
	&WindowDataMenuAlbum,			//アルバムウインドウ
	&WindowDataMenuAlbumDetail,		//アルバムディティールウインドウ
	&WindowDataMenuAddressBook,		//アドレスウインドウ
	&WindowDataSendMailWindow,		//メール送信ウインドウ（杉）
	&WindowDataSendPetMailWindow,	//ペットメール送信ウインドウ（杉）
	&WindowDataMenuSystem,			//システムウインドウ
	&WindowDataMenuSystemShortCut,	//ショートカットウィンドウ
	&WindowDataBattle,				//バトルウインドウ
	&WindowDataMenuBtlItem,			//战闘用アイテムウィンドウ
	&WindowDataMenuBtlPet,			//战闘用使い魔ウィンドウ
	&WindowDataWatch,				//観战ウィンドウ
	&WindowDataResult,				//バトル结果ウインドウ
	&WindowDataDuelResult,			//デュエル结果ウインドウ
	&WindowDataSurprise,			//不意打ちウィンドウ
	&WindowDataMap,					//マップウインドウ	
	&WindowDataAction,				//アクションウインドウ	
	&WindowDataChat,				//チャットウィンドウ
	&WindowDataGeneral,				//通常版泛用ウィンドウ
	&WindowDataGeneral,				//サーバーリクエスト版泛用ウィンドウ
	&WindowDataMenuGuilMonFood,		//公会宠物饵やりウィンドウ
	&WindowDataMapName,				//マップ名ウィンドウ
	&WindowDataMenuMailHistory,		// mailHistory
	&WindowDataMenuBank,			//银行ウィンドウ
	&WindowDataMenuCalculator,		//电卓ウィンドウ
	&WindowDataMenuTrade,			//トレードウィンドウ
	&WindowDataMenuTargetSel,		//トレード对象选择ウィンドウ
	&WindowDataMenuTargetSel1,		//对象选择ウィンドウ１
	&WindowDataMenuTargetSel2,		//对象选择ウィンドウ２
	&WindowDataMenuMultiPurpose,	// multiPurpose
	&WindowDataGuildInfo,					// guildInfomation
	&WindowDataMenuGroupMail,				// groupMail
	&WindowDataBBS,					//ＢＢＳ（揭示板）	
	&WindowDataMenuGuilMonStatus,	//公会宠物状态ウィンドウ
	&WindowDataMenuShopTop,			//ショップトップウィンドウ
	&WindowDataMenuItemShop,		//アイテムショップウィンドウ
	&WindowDataMenuSkillShop,		//スキルショップウィンドウ
	&WindowDataChangeGuildTitle,	//家族称号变更ウインドウ
	&WindowDataMenuOrthopedist,		//整形外科医ウィンドウ
	&WindowDataOldChat,				//旧版チャットウィンドウ

#ifdef PUK3_PROF
	&WindowDataMenuProf,			//状态プロフィール
	&WindowDataMenuProfCategory,	//分类リスト
	&WindowDataProfileBBS1,			//プロフィールＢＢＳ１
	&WindowDataProfileBBS2,			//プロフィールＢＢＳ２
	&WindowDataProfileBBS3,			//プロフィールＢＢＳ３
	&WindowDataProfileBBS4,			//プロフィールＢＢＳ４
	&WindowDataProfUserNpc,			//他人のプロフィール
#endif

};

//void CloseWindowSelectType( int Type )
//で关闭ウインドウリスト
//typeはビットで＆计算して使用します。
// 关闭ウィンドウ设定データ
const char CloseWindowListFlag[] = {
	0,		//ダミー
	0,		//左コンソール大
	0,		//左コンソール中
	0,		//左コンソール小
	0,		//右コンソール大
	0,		//右コンソール小
	0,		//メニュー
	1,		//状态状态
	1,		//状态ディティール
	1,		//状态タイトル
	4,		//スキルウインドウ
	1,		//スキルクリエートウィンドウ
	1,		//スキル采取ウィンドウ
	1,		//スキルその他结果ウィンドウ
	1,		//アイテムウインドウ
	1,		//モンスターウインドウ
	1,		//モンスター状态ウインドウ
	1,		//モンスターディティールウインドウ
	1,		//モンスタースキルウインドウ
	1,		//アルバムウインドウ
	1,		//アルバムディティールウインドウ
	1,		//アドレスウインドウ
	1,		//メール送信ウインドウ（杉）
	1,		//ペットメール送信ウインドウ（杉）
	1,		//システムウインドウ
	1,		//ショートカットウィンドウ
	0,		//バトルウインドウ
	0,		//战闘用アイテムウィンドウ
	0,		//战闘用使い魔ウィンドウ
	0,		//観战ウィンドウ
	0,		//バトル结果ウインドウ
	0,		//デュエル结果ウインドウ
	0,		//不意打ちウィンドウ
	4,		//マップウインドウ	
	4,		//アクションウインドウ	
	0,		//チャットウィンドウ

	1,		//通常版泛用ウィンドウ
	2,		//サーバーリクエスト版泛用ウィンドウ
	2,		//公会宠物饵やりウィンドウ
	0,		//マップ名ウィンドウ
	1,		// mailHistory
	2,		//银行ウィンドウ
	4,		//电卓ウィンドウ
	2,		//トレードウィンドウ
	2,		//トレード对象选择ウィンドウ
	2,		//对象选择ウィンドウ１
	2,		//对象选择ウィンドウ２
	2,		// multiPurpose
	1,		// guildInfomation
	1,		// groupMail
	2,		//ＢＢＳ（揭示板）	
	4,		//公会宠物状态ウィンドウ
	2,		//ショップトップウィンドウ
	2,		//アイテムショップウィンドウ
	2,		//スキルショップウィンドウ
	1,		//家族称号变更ウインドウ
	2,		//整形外科医ウィンドウ
	0,		//旧版チャットウィンドウ
#ifdef PUK3_PROF
	1,		//状态プロフィール
	1,		//プロフィール分类
	1,		//プロフィールＢＢＳ１
	1,		//プロフィールＢＢＳ２
	1,		//プロフィールＢＢＳ３
	1,		//プロフィールＢＢＳ４
	1,		//他人のプロフィール
#endif
};

//各ウインドウのドラッグムーブの设定(个数、(x,y,w,h)x个数)
static WINDOW_DRAGMOVE *DragMoveDate[]={
	&DragMoveNULL,					//
	&DragMoveNULL,					//左コンソール大
	&DragMoveNULL,					//左コンソール中
	&DragMoveNULL,					//左コンソール小
	&DragMoveNULL,					//右コンソール大
	&DragMoveNULL,					//右コンソール小
	&DragMoveNULL,					//メニュー
	&DragMoveDateStatus,			//状态状态
	&DragMoveDateStatus,			//状态ディティール
	&DragMoveDateStatus,			//状态タイトル
	&DragMoveDateSkill,				//スキルウインドウ
	&DragMoveDateSkillCreate,		//スキルクリエートウィンドウ
	&DragMoveDateSkillGather,		//スキル采取ウィンドウ
	&DragMoveDateSkillOthers,		//スキルその他结果ウィンドウ
	&DragMoveDateItem,				//アイテムウインドウ
	&DragMoveDateMonster,			//モンスターウインドウ
	&DragMoveDateMonsterStatus,		//モンスター状态ウインドウ
	&DragMoveDateMonsterStatus,		//モンスター状态ウインドウ
	&DragMoveDateMonsterStatus,		//モンスター状态ウインドウ
	&DragMoveDateAlbum,				//アルバムウインドウ
	&DragMoveDateAlbumDetail,		//アルバムディティールウインドウ
	&DragMoveDataAddressBook,		//アドレスウインドウ
	&DragMoveDateSendMailWindow,	//メール送信ウインドウ（杉）
	&DragMoveDateSendMailWindow,	//ペットメール送信ウインドウ（杉）
	&DragMoveDateSystem,			//システムウインドウ
	&DragMoveDateSystemShortCut,	//ショートカットウィンドウ
	&DragMoveNULL,					//バトルウインドウ
	&DragMoveDateBtlItem,			//战闘用アイテムウィンドウ
	&DragMoveDateBtlPet,			//战闘用使い魔ウィンドウ
	&DragMoveNULL,					//観战ウィンドウ
	&DragMoveDateResult,			//バトル结果ウインドウ
	&DragMoveNULL,					//デュエル结果ウインドウ
	&DragMoveNULL,					//不意打ちウィンドウ
	&DragMoveDateMap,				//マップウインドウ
	&DragMoveDateAction,			//アクションウインドウ
	&DragMoveDateChat,				//チャットウィンドウ
	&DragMoveNULL,					//通常版泛用ウィンドウ
	&DragMoveNULL,					//サーバーリクエスト版泛用ウィンドウ
	&DragMoveDateGuilMonFood,		//公会宠物饵やりウィンドウ
	&DragMoveNULL,					//マップ名ウィンドウ
	&DragMoveDataHistoryMail,					// mailHistory
	&DragMoveDateBank,				//银行ウィンドウ
	&DragMoveDateCalculator,		//电卓ウィンドウ
	&DragMoveDateTrade,				//トレードウィンドウ
	&DragMoveNULL,					//トレード对象选择ウィンドウ
	&DragMoveNULL,					//对象选择ウィンドウ１
	&DragMoveNULL,					//对象选择ウィンドウ２
	&DragMoveNULL,					// multiPurpose
	&DragMoveDataGuildInfo,					// guildInfo
	&DragMoveDataGroupMail,					// groupMail
	&DragMoveDateBBS,					//ＢＢＳ（揭示板）
	&DragMoveDateGuilMonStatus,		//公会宠物状态ウィンドウ
	&DragMoveNULL,					//ショップトップウィンドウ
	&DragMoveDateItemShop,			//アイテムショップウィンドウ
	&DragMoveDateSkillShop,			//スキルショップウィンドウ
	&DragMoveDateChangeGuildTitle,	//家族称号变更ウインドウ
	&DragMoveNULL,					//整形外科医ウィンドウ
	&DragMoveNULL,					//旧版チャット
#ifdef PUK3_PROF
	&DragMoveDateStatus,			//状态プロフィール
	&DragMoveDateProfCategory,		//プロフィール分类
	&DragMoveDateProfileBBS1,		//プロフィールＢＢＳ
	&DragMoveDateProfileBBS2,		//プロフィールＢＢＳ
	&DragMoveDateProfileBBS3,		//プロフィールＢＢＳ
	&DragMoveDateSendMailWindow,	//プロフィールＢＢＳ
	&DragMoveDateStatus,			//他人のプロフィール
#endif
};

#ifdef _DEBUG		

#define OUT_LOG_WIN_MEM_FILE_NAME "winmemlog.txt"

char DEBUG_WIN_NAME[][256]={
	"MENU_WINDOW_NONE",                                                  //MLHIDE
	"MENU_WINDOW_LEFT_COCKPIT_LARGE",	// 左コンソール                          //MLHIDE
	"MENU_WINDOW_LEFT_COCKPIT_MIDDLE",	// 左コンソール（小型化）                    //MLHIDE
	"MENU_WINDOW_LEFT_COCKPIT_SMALL",	// 左コンソール（最小）                      //MLHIDE
	"MENU_WINDOW_RIGHT_COCKPIT_LARGE",	// 右コンソール                         //MLHIDE
	"MENU_WINDOW_RIGHT_COCKPIT_SMALL",	// 右コンソール（最小）                     //MLHIDE
	"MENU_WINDOW_MENU",					// メニューウィンドウ                                 //MLHIDE
	"MENU_WINDOW_STATUS",				// 状态ウインドウ                                  //MLHIDE
	"MENU_WINDOW_DETAIL",				// ディティールウインドウ                              //MLHIDE
	"MENU_WINDOW_TITLE",				// タイトルウインドウ                                 //MLHIDE
	"MENU_WINDOW_SKILL",				// スキルウインドウ                                  //MLHIDE
	"MENU_WINDOW_SKILLCREATE",			// スキルクリエートウィンドウ                        //MLHIDE
	"MENU_WINDOW_SKILLGATHER",			// スキル采取ウィンドウ                           //MLHIDE
	"MENU_WINDOW_SKILLOTHERSRESULT",	// スキルその他结果ウィンドウ                    //MLHIDE
	"MENU_WINDOW_ITEM",					// アイテムウインドウ                                 //MLHIDE
	"MENU_WINDOW_MONSTER",				// モンスターウインドウ                              //MLHIDE
	"MENU_WINDOW_MONSTER_STATUS",		// モンスター状态ウインドウ                       //MLHIDE
	"MENU_WINDOW_MONSTER_DETAIL",		// モンスターディティールウインドウ                   //MLHIDE
	"MENU_WINDOW_MONSTER_SKILL",		// モンスタースキルウインドウ                       //MLHIDE
	"MENU_WINDOW_ALBUM",				// アルバムウインドウ                                 //MLHIDE
	"MENU_WINDOW_ALBUM_DETAIL",			// アルバムディティールウインドウ                     //MLHIDE
	"MENU_WINDOW_ADDRESS",				// アドレスウインドウ（未使用）                          //MLHIDE
	"MENU_WINDOW_ADDRESS_SENDMAIL",		// メール送信ウインドウ（杉）                    //MLHIDE
	"MENU_WINDOW_ADDRESS_SENDPETMAIL",	// ペットメール送信ウインドウ（杉）               //MLHIDE
	"MENU_WINDOW_SYSTEM",				// システムウインドウ                                //MLHIDE
	"MENU_WINDOW_SYSTEMSHORTCUT",		// ショートカットウインドウ                       //MLHIDE
	"MENU_WINDOW_BATTLE",				// バトルウィンドウ                                 //MLHIDE
	"MENU_WINDOW_BTLITEM",				// 战闘用アイテムウィンドウ                            //MLHIDE
	"MENU_WINDOW_BTLPET",				// 战闘用使い魔ウィンドウ                              //MLHIDE
	"MENU_WINDOW_WATCH",				// 観战ウィンドウ                                   //MLHIDE
	"MENU_WINDOW_RESULT",				// バトル结果ウィンドウ                               //MLHIDE
	"MENU_WINDOW_DUELRESULT",			// デュエル结果ウインドウ                           //MLHIDE
	"MENU_WINDOW_SURPRISE",				// 不意打ちウィンドウ                              //MLHIDE
	"MENU_WINDOW_MAP",					// マップウインドウ                                   //MLHIDE
	"MENU_WINDOW_ACTION",				// アクションウインドウ                               //MLHIDE
	"MENU_CHAT_WINDOW",					// チャットウインドウ                                 //MLHIDE
	"MENU_WINDOW_GENERAL",				// 通常版泛用ウィンドウ                              //MLHIDE
	"MENU_WINDOW_SRGENERAL",			// サーバーリクエスト版泛用ウィンドウ                      //MLHIDE
	"MENU_WINDOW_GUILMONFOOD",			// 公会宠物饵やりウィンドウ                         //MLHIDE
	"MENU_WINDOW_MAPNAME",				// マップ名                                    //MLHIDE
	"MENU_WINDOW_HISTORY",                                               //MLHIDE
	"MENU_WINDOW_BANK",					// 银行ウィンドウ                                   //MLHIDE
	"MENU_WINDOW_CALCULATOR",			// 电卓ウィンドウ                               //MLHIDE
	"MENU_WINDOW_TRADE",				// トレードウィンドウ                                 //MLHIDE
	"MENU_WINDOW_TARGETSEL",			// 对象选择ウィンドウ                              //MLHIDE
	"MENU_WINDOW_TARGETSEL1",			// 对象选择ウィンドウ１                            //MLHIDE
	"MENU_WINDOW_TARGETSEL2",			// 对象选择ウィンドウ２                            //MLHIDE
	"MENU_WINDOW_MULTIPURPOSE",			// アドレスウインドウ                           //MLHIDE
	"MENU_WINDOW_GUILDINFO",			// guildSettingWindow                     //MLHIDE
	"MENU_WINDOW_GROUP_MAIL",			// groupMail                             //MLHIDE
	"MENU_WINDOW_BBS",					// ＢＢＳ（揭示板）                                   //MLHIDE
	"MENU_WINDOW_GUILMONSTATUS",		// 公会宠物状态ウィンドウ                         //MLHIDE
	"MENU_WINDOW_SHOPTOP",				// ショップトップウィンドウ                            //MLHIDE
	"MENU_WINDOW_ITEMSHOP",				// アイテムショップウィンドウ                          //MLHIDE
	"MENU_WINDOW_SKILLSHOP",			// スキルショップウィンドウ                           //MLHIDE
	"MENU_WINDOW_CHANGE_GUILD_TITLE",	// 家族称号变更ウインドウ                     //MLHIDE
	"MENU_WINDOW_ORTHOPEDIST",			// 整形外科医ウィンドウ                           //MLHIDE
	"MENU_OLD_CHAT_WINDOW",				// 旧版チャットウインドウ                            //MLHIDE
#ifdef PUK3_PROF
	"MENU_WINDOW_PROFILE",				// プロフィールウインドウ                             //MLHIDE
	"MENU_WINDOW_PROFILE_CATEGORY",		// プロフィール分类                         //MLHIDE
	"MENU_WINDOW_PROFILE_PROFILE_1",	// プロフィールＢＢＳ１（分类选择）                 //MLHIDE
	"MENU_WINDOW_PROFILE_PROFILE_2",	// プロフィールＢＢＳ２（对象者一覧）                //MLHIDE
	"MENU_WINDOW_PROFILE_PROFILE_3",	// プロフィールＢＢＳ３（１对象者の详细）              //MLHIDE
	"MENU_WINDOW_PROFILE_PROFILE_4",	// プロフィールＢＢＳ４（メール）                  //MLHIDE
	"MENU_WINDOW_PROFILE_USER_NPC",		// 他の人のプロフィール                       //MLHIDE
#endif
	"MENU_WINDOW_TYPE_NUM"                                               //MLHIDE
};

void InitOutLogOfWinMemory(void){

	FILE *File;

	if( (File  = fopen( OUT_LOG_WIN_MEM_FILE_NAME, "w" )) == NULL ){     //MLHIDE
		return;
	}

	fclose(File);

}

void OutLogOfWinMemory(int num,int seq,long mem){

	FILE *File;
	char buff[1024];

	if( (File  = fopen( OUT_LOG_WIN_MEM_FILE_NAME, "a+" )) != NULL ){    //MLHIDE

		switch(seq){
			case 0:
				sprintf(buff,"create?:%d:%s\n",mem,DEBUG_WIN_NAME[num]);          //MLHIDE
				break;
			case 1:
				sprintf(buff,"delete?%d:%s\n",mem,DEBUG_WIN_NAME[num]);           //MLHIDE
				break;
		}

		fwrite(buff,strlen(buff),1,File);

		fclose(File);
	}

}

#endif

// EXE起动时に一度だけ呼ばれる
void MenuWindowStartExe( void ){

#ifdef _DEBUG
	InitOutLogOfWinMemory();
#endif

	//チャットの初期化
	InitMenuWindowChatStartExe();
	
	//ウインドウの情报ロード
	WindowStateLoad();

	//デバッグ机能ＯＦＦ
	NewWinDebugFlag=0;

	//リオープンフラグのクリア
	ReSetMenuWindowReOpen();

#ifdef PUK3_PROF
	//分类ーファイル読み込み
	loadCategoryDateFile();
#endif

}

// EXE終了时に一度だけ呼ばれる
void MenuWindowEndExe( void ){

	//ウインドウの情报セーブ
	WindowStateSave();

	//アルバム情报のセーブ
	writeAlbumFile();

#ifdef PUK3_NOTFREE_WINDDOW
	pushMenuWindowAll();
#endif
}

//	メニューの初期化（ログイン时に呼ばれます。自前で使用しているフラグなど、
//							初期化したい时にここでよびだせます）			 
void InitNewMenuInLogin(void){

	//ウインドウ情报をファイルからロード
	WindowStateLoad();

	//アルバム
	InitMenuWindowAlbumInLogin();

	//ＢＢＳ
	InitMenuWindowBBSInLogin();

	//チャット
	InitMenuWindowChatInLogin();

	//メール
	InitMenuWindowSendMailInLogin();

	//モンスター
	InitMenuWindowMonsterInLogin();

	//状态
	InitMenuWindowMenuStatusInLogin();

#ifdef PUK3_PROF
	//プロフィール
	InitMenuWindowMenuProfileInLogin();

	clearProfile();

	if(loadProfileDateFile()==FALSE){
		//ロードの失败ならデフォルト值
		setDefaultProfile();
	}
#endif
#ifdef PUK3_MAIL_ETC
	// アドレスブックの初期化
	AddressBookInit();

	// ここでメール关系のウィンドウの表示の初期化
	// リオープンフラグ消さないと表示されてしまう场合がある
	WindowFlag[ MENU_WINDOW_ADDRESS ].ReOpen=0;
	WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen=0;
	WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen=0;
	WindowFlag[ MENU_WINDOW_HISTORY ].ReOpen=0;
#endif

}

//	メニューの初期化（登出时に呼ばれます。自前で使用しているフラグなど、
//							初期化したい时にここでよびだせます
//							また回线が切断されたときも呼ばれます。）			 
void InitNewMenuInLogout(void){

	//ウインドウ情报をファイルにセーブ
	WindowStateSave();

	//アルバム情报のセーブ
	writeAlbumFile();

	// メールヒストリをファイルに保存
	writeMailFile();
	// 家族メールヒストリをファイルに保存
	writeGuildMailFile();

#ifdef PUK3_PROF
	saveProfileDateFile();

	saveCategoryDateFile();
#endif
}

// ログイン时の初期化
void initMenuWindow( void ){

	int i;

	// ウィンドウ管理バッファの初期化
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		WindowBuff[ i ] = NULL;
	}

	// 管理构造体を初期化
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		WindowFlag[i].wininfo=NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	Lock3DChangeWindowCnt = 0;		// 描画机能切り替えに邪魔なウィンドウの数
#endif

	// ウィンドウドラッグドロップ管理构造体初期化
	initWindowDDSystem();

	// 现在处理中のウィンドウ种类、ボタンの番号初期化
	nowWinType = MENU_WINDOW_NONE;
	nowBtnNo = -1;

#if WINDOW_CREATE

	// 基本ウィンドウのエントリー
	// メインメニュー
	createMenuWindow( MENU_WINDOW_MENU );

	// 左コクピット
	switch(MenuWindowLeftSizeFlag){
		case 0:
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_LARGE );
			break;
		case 1:
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_MIDDLE );
			break;
		case 2:
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_SMALL );
			break;
	}

	// 右コクピット
	switch(MenuWindowRightSizeFlag){
		case 0:
			createMenuWindow( MENU_WINDOW_RIGHT_COCKPIT_LARGE );
			break;
		case 1:
			createMenuWindow( MENU_WINDOW_RIGHT_COCKPIT_SMALL );
			break;
	}

	// チャットウインドウ
	switch(ChatMode){
	case 0:
		createMenuWindow( MENU_CHAT_WINDOW );
		break;
	case 1:
		createMenuWindow( MENU_OLD_CHAT_WINDOW );
		break;
	}

	//ReOpenフラグに设定されているウインドウを开く
	MenuWindowReOpen();

	//ダイアログ管理の初期化
	initDialogSystem();

	// アイテムウィンドウの初期化
	UsingItemNo = -1;
	UsingItemCnt = 0;

	// スキルウィンドウの初期化
	SkillWindowInit();

	// モンスター状态ビューの初期化
	pActMonsSW = NULL;

	//チャットウインドウ描画フラグ
	ChatWindowView=1;

	//ダイアログをチャットへ
	SetDialogMenuChat();
#endif

}

//フィールド时の初期化
void initMenuWindowFealdSeq( void ){

	int i;

	// ウィンドウ管理バッファの初期化
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		WindowBuff[ i ] = NULL;
	}

	// 管理构造体を初期化
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		WindowFlag[i].wininfo=NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	Lock3DChangeWindowCnt = 0;		// 描画机能切り替えに邪魔なウィンドウの数
#endif

	// ウィンドウドラッグドロップ管理构造体初期化
	initWindowDDSystem();

	// 现在处理中のウィンドウ种类、ボタンの番号初期化
	nowWinType = MENU_WINDOW_NONE;
	nowBtnNo = -1;

	//ダイアログ管理の初期化
	initDialogSystem();

#if WINDOW_CREATE
	// 基本ウィンドウのエントリー
	// メインメニュー
	createMenuWindow( MENU_WINDOW_MENU );

	// 左コクピット
	switch(MenuWindowLeftSizeFlag){
		case 0:
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_LARGE );
			break;
		case 1:
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_MIDDLE );
			break;
		case 2:
			createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_SMALL );
			break;
	}

	// 右コクピット
	switch(MenuWindowRightSizeFlag){
		case 0:
			createMenuWindow( MENU_WINDOW_RIGHT_COCKPIT_LARGE );
			break;
		case 1:
			createMenuWindow( MENU_WINDOW_RIGHT_COCKPIT_SMALL );
			break;
	}

	// チャットウインドウ
	switch(ChatMode){
	case 0:
		createMenuWindow( MENU_CHAT_WINDOW );
		break;
	case 1:
		createMenuWindow( MENU_OLD_CHAT_WINDOW );
		break;
	}

	//以前のフィールド时に存在していたウインドウの复归
	MenuWindowReOpen();

	// アイテムウィンドウの初期化
	UsingItemNo = -1;
	UsingItemCnt = 0;

	// スキルウィンドウの初期化
	SkillWindowInit();

	// モンスター状态ビューの初期化
	pActMonsSW = NULL;

	//チャットウインドウ描画フラグ
	ChatWindowView=1;

#ifdef PUK3_MAIL_ETC
	// メールウィンドウ开いてるかチェック
	// ペットメールウィンドウ开いてるかチェック
	if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].wininfo ||
		 WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].wininfo ){
		// フォーカスはメールに持たせたいので何もしない
	}else{
		//ダイアログをチャットへ
		SetDialogMenuChat();
	}
#else
	//ダイアログをチャットへ
	SetDialogMenuChat();
#endif
#endif
}

//バトル时の初期化
void initMenuWindowBattleSeq( void )
{

	int i;

	// ウィンドウ管理バッファの初期化
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		WindowBuff[ i ] = NULL;
	}

	// 管理构造体を初期化
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		WindowFlag[i].wininfo=NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	Lock3DChangeWindowCnt = 0;		// 描画机能切り替えに邪魔なウィンドウの数
#endif

	// ウィンドウドラッグドロップ管理构造体初期化
	initWindowDDSystem();

	// 现在处理中のウィンドウ种类、ボタンの番号初期化
	nowWinType = MENU_WINDOW_NONE;
	nowBtnNo = -1;
	
	//ダイアログ管理の初期化
	initDialogSystem();

	// 基本ウィンドウのエントリー
	// メインメニュー
	createMenuWindow( MENU_WINDOW_MENU );

	// 左コクピット
	createMenuWindow( MENU_WINDOW_LEFT_COCKPIT_MIDDLE );

	// チャットウインドウ
	switch(ChatMode){
	case 0:
		createMenuWindow( MENU_CHAT_WINDOW );
		break;
	case 1:
		createMenuWindow( MENU_OLD_CHAT_WINDOW );
		break;
	}
#ifdef PUK3_MAIL_ETC
	// アドレスブック开いてたかチェック
	// 开いてたなら开く
	if ( WindowFlag[ MENU_WINDOW_ADDRESS ].ReOpen ){
		createMenuWindow( MENU_WINDOW_ADDRESS );
		WindowFlag[ MENU_WINDOW_ADDRESS ].ReOpen=0;
	}
	// ヒストリー开いてたかチェック
	// 开いてたなら开く
	if ( WindowFlag[ MENU_WINDOW_HISTORY ].ReOpen ){
		int mailHistoryPageback = mailHistoryPage;
		createMenuWindow( MENU_WINDOW_HISTORY );
		// 关闭前に开いてたページに
		mailHistoryPage = mailHistoryPageback;
		WindowFlag[ MENU_WINDOW_HISTORY ].ReOpen=0;
	}

	#ifdef PUK3_MAIL_ETC2
		// メールウィンドウ开いてたかチェック
		// 开いてたなら开く
		if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen ||
			 WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen ){
			createMenuWindow( MENU_WINDOW_ADDRESS_SENDMAIL );
			WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen=0;
			WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen=0;
		}
	#else
		// メールウィンドウ开いてたかチェック
		// 开いてたなら开く
		if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen ){
			ReOpenMailWindow();
			WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen=0;
		}
		// ペットメールウィンドウ开いてたかチェック
		// 开いてたなら开く
		if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen ){
			createMenuWindow( MENU_WINDOW_ADDRESS_SENDPETMAIL );
			WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen=0;
		}
	#endif
#endif

	// スキルウィンドウの初期化
	SkillWindowInit();

	// モンスター状态ビューの初期化
	pActMonsSW = NULL;

	//チャットウインドウ描画フラグ
	ChatWindowView=1;

#ifdef PUK3_MAIL_ETC
	// メールウィンドウ开いてるかチェック
	// ペットメールウィンドウ开いてるかチェック
	if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].wininfo ||
		 WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].wininfo ){
		// フォーカスはメールに持たせたいので何もしない
	}else{
		//ダイアログをチャットへ
		SetDialogMenuChat();
	}
#else
	//ダイアログをチャットへ
	SetDialogMenuChat();
#endif
}

//-------------------------------------------------------------------------//
//	メニューウィンドウ削除													 //
//-------------------------------------------------------------------------//
void deleteMenuWindow( WINDOW_INFO *win )
{
	int i;
//	ACTION_SWITCH	*Action;
	DIALOG_SWITCH	*Dialog;

#ifdef _DEBUG
	OutLogOfWinMemory(win->type,1,(long)win);
#endif

	win->funcDel();

#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	if ( win->type > MENU_WINDOW_MENU ){
		switch(win->type){
		case MENU_WINDOW_SYSTEM:
		case MENU_WINDOW_SYSTEMSHORTCUT:
		case MENU_WINDOW_BATTLE:
		case MENU_WINDOW_SURPRISE:
		case MENU_WINDOW_MAP:
		case MENU_WINDOW_ACTION:
		case MENU_CHAT_WINDOW:
		case MENU_WINDOW_MAPNAME:
		case MENU_OLD_CHAT_WINDOW:
			break;
		default:
			Lock3DChangeWindowCnt--;
		}
	}
#endif
	// ウィンドウの状态を保存（次に开いたときに座标データなどを保持するため）
	wF = &WindowFlag[ win->type ];
	wF->wx = win->wx;
	wF->wy = win->wy;

	wF->flag &= ~( 0x80 );
	wF->flag |= 0x01;
	wF->wininfo = NULL;

	// 破弃されるウィンドウが、ドラッグ、ドロップをしてたらそれを中止
	if ( WindowDD.WinType == win->type ){
#ifdef PUK2_NEWDRAG
		WinDD_DragCancel();
#else
		WindowDD.ObjType = WINDD_NONE;
		WindowDD.ObjData = NULL;
		WindowDD.SettleDropItem = NULL;
		WindowDD.DropX = 0;
		WindowDD.DropY = 0;

		WindowDD.WinType = MENU_WINDOW_NONE;
		WindowDD.SwitchNo = -1;

		WindowDD.ProcNo = WINDD_DONOT;
#endif
	}

	// スイッチ用に确保されたエリアを开放
	for( i = 0 ; i < win->swNum ; i++ ){
		switch(win->sw[i].type){
			case SWITCH_NONE:
				break;
			case SWITCH_GRAPHIC:
				if((GRAPHIC_SWITCH *)win->sw[ i ].Switch!=NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (GRAPHIC_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;
				}
				break;
			case SWITCH_ACTION:
				if((ACTION_SWITCH *)win->sw[ i ].Switch!=NULL){
					((ACTION_SWITCH *)win->sw[ i ].Switch)->ActionAdd->deathFlag=TRUE;
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (ACTION_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;
				}
				break;
			case SWITCH_TEXT:						
				if(((TEXT_SWITCH *)win->sw[ i ].Switch)->text!=NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( ((TEXT_SWITCH *)win->sw[ i ].Switch)->text, MEMLISTTYPE_SWITCH_TEXT );
	#endif
					free( ((TEXT_SWITCH *)win->sw[ i ].Switch)->text );
					((TEXT_SWITCH *)win->sw[ i ].Switch)->text=NULL;
				}
				if((TEXT_SWITCH *)win->sw[ i ].Switch!=NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (TEXT_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;
				}
				break;
			case SWITCH_BUTTON:
				if((BUTTON_SWITCH *)win->sw[ i ].Switch!=NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (BUTTON_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;
				}
				break;
			case SWITCH_DIALOG:
				Dialog=(DIALOG_SWITCH *)win->sw[ i ].Switch;
				//现在自分が使用していて、チャットウインドウが存在する时
//				if(Dialog==DiarogST.SwAdd && WindowFlag[MENU_CHAT_WINDOW].wininfo != NULL){
				if(Dialog==DiarogST.SwAdd){
					//现在使用していたらココでチャットに变更する
					SetDialogMenuChat();
				}
				if((DIALOG_SWITCH *)win->sw[ i ].Switch != NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (DIALOG_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;	
				}
				break;
			case SWITCH_CHATSTR:
				if((CHATSTR_SWITCH *)win->sw[ i ].Switch!=NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (CHATSTR_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;
				}
				break;
			case SWITCH_NUMBER:
				if((NUMBER_SWITCH *)win->sw[ i ].Switch!=NULL){
	#ifdef PUK2_MEMCHECK
					memlistrel( win->sw[ i ].Switch, MEMLISTTYPE_SWITCH_DATA );
	#endif
					free( (NUMBER_SWITCH *)win->sw[ i ].Switch );
					win->sw[ i ].Switch=NULL;
				}
				break;

		}
	}

	// ウィンドウとスイッチ用に确保されたメモリを开放
#ifdef PUK2_MEMCHECK
	memlistrel( win->sw, MEMLISTTYPE_SWITCH_INFO );
#endif
	if(win->sw)
		free( win->sw );
	win->sw=NULL;

#ifdef PUK2_MEMCHECK
	memlistrel( win, MEMLISTTYPE_WINDOW_INFO );
#endif
	if(win)
		free( win );
	win=NULL;

}

//--------------------------------------------------------------------------//
//	メニューウィンドウ作成													//
//--------------------------------------------------------------------------//
WINDOW_INFO *createMenuWindow( int type )
{
	int i,j;
	int winno;
	WINDOW_INFO *win,*wintmp;
	const WINDOW_DATA *mwd;
	SWITCH_INFO *sw;
	SWITCH_DATA *swd;
	BUTTON_SWITCH		*Button;
	BUTTON_SWITCH		*mButton;
	TEXT_SWITCH			*Text;
	TEXT_SWITCH			*mText;
	GRAPHIC_SWITCH		*Graph;
	GRAPHIC_SWITCH		*mGraph;
	DIALOG_SWITCH		*Dialog;
	DIALOG_SWITCH		*mDialog;
	CHATSTR_SWITCH		*ChatStr;
	CHATSTR_SWITCH		*mChatStr;
	ACTION_SWITCH		*Action;
	NUMBER_SWITCH		*Number;

	//开くウインドウによって闭じなくてはいけないウインドウを关闭
	switch(CloseWindowListFlag[type]){
		case 0:
			//なにも闭じない
			break;
		case 1:
			CloseWindowSelectType(2);
			break;
		case 2:
			CloseWindowSelectType(3);
			break;
	}

	// 既存のウィンドウに同じものが无いかチェック
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		if( WindowBuff[ i ] == NULL) continue;

		// あるじゃん
		if( WindowBuff[ i ]->type == type ){
			WindowBuff[ i ]->flag |= WIN_INFO_PRIO;
			return WindowBuff[ i ];
		}
	}

	// 空きバッファの检索
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		if( WindowBuff[ i ] == NULL ){
			break;
		}
	}
	// 空きバッファなし
	if( i == MENU_WINDOW_MAX ){
		return NULL;
	}
	winno = i;

	// そんなわけで、ウィンドウ作成
	win = ( WINDOW_INFO *)calloc( 1, sizeof( WINDOW_INFO ) );
	if( win == NULL ){
		return NULL;
	}
#ifdef PUK2_MEMCHECK
	memlistset( win, MEMLISTTYPE_WINDOW_INFO );
#endif

	// メニューウィンドウの设定
	mwd = WindowData[ type ];

	win->type = type;					// ウィンドウの种类
	win->flag = mwd->flag;				// ウィンドウフラグ
	win->prio = mwd->prio;				// プライオリティ
	win->sx = mwd->w;					// ウィンドウのサイズ
	win->sy = mwd->h;
	win->rgba = mwd->rgba;				// ウィンドウの背景色
		// （ウィンドウタイトル背景のアルファ值は0xffとの平均值を使ってみるみる）
	win->func = mwd->func;				// 制御关数
	win->funcDraw = mwd->funcDraw;		// 描画关数
	win->funcDel = mwd->funcDel;		// 削除时关数
	win->swNum = mwd->num;			// スイッチの个数

	// スイッチがある场合、スイッチの设定
	if( win->swNum ){
		// スイッチの数だけメモリを确保
		win->sw = ( SWITCH_INFO *)calloc( win->swNum, sizeof( SWITCH_INFO ) );
		if( win->sw == NULL ){
			deleteMenuWindow( win );
			return NULL;
		}
#ifdef PUK2_MEMCHECK
		memlistset( win->sw, MEMLISTTYPE_SWITCH_INFO );
#endif

		// スイッチの登録（スイッチはプライオリティ顺に登録すること）
		swd = mwd->sw;
		for( j = 0 ; j < win->swNum ; j++ ){
			sw = &win->sw[ j ];

			sw->type = swd->type;
			sw->status = 0;
			sw->ofx = swd->ofx;				// スイッチの表示基准座标
			sw->ofy = swd->ofy;
			sw->sx = swd->sx;				// スイッチの判定范围
			sw->sy = swd->sy;

			sw->Enabled = swd->Enabled;		//实行フラグ

			switch(sw->type){
				case SWITCH_NONE:
					break;
				case SWITCH_GRAPHIC:
					Graph=( GRAPHIC_SWITCH *)calloc( 1, sizeof(GRAPHIC_SWITCH) );
					if(Graph!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( Graph, MEMLISTTYPE_SWITCH_DATA );
#endif
						mGraph=(GRAPHIC_SWITCH *)swd->Switch;
						Graph->graNo=mGraph->graNo;
						Graph->rgba=mGraph->rgba;
						Graph->u=mGraph->u;
						Graph->v=mGraph->v;
						Graph->w=mGraph->w;
						Graph->h=mGraph->h;

						sw->Switch=Graph;				
					}else{
						sw->Switch=NULL;				
					}
					break;
				case SWITCH_ACTION:
					Action=( ACTION_SWITCH *)calloc( 1, sizeof(ACTION_SWITCH) );
					if(Action!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( Action, MEMLISTTYPE_SWITCH_DATA );
#endif
						Action->ActionAdd=GetAction( PRIO_CHR, 0 );
						Action->ActionAdd->atr |= ACT_ATR_HIDE;		// 非表示（自力で表示するので）
						Action->ActionAdd->func=NULL;
						Action->ActionAdd->anim_chr_no=((ACTION_SWITCH_INIT *)swd->Switch)->ActionGraphicNo;
						Action->ActionAdd->dispPrio=DISP_PRIO_WIN2;
						Action->AnimNoLoop = FALSE;

						sw->Switch=Action;
					}else{
						sw->Switch=NULL;				
					}
					break;
				case SWITCH_TEXT:						
					Text=( TEXT_SWITCH *)calloc( 1, sizeof(TEXT_SWITCH) );
					if(Text!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( Text, MEMLISTTYPE_SWITCH_DATA );
#endif
						mText=(TEXT_SWITCH *)swd->Switch;

						Text->text=(char *)calloc( 256, sizeof(char) );
#ifdef PUK2_MEMCHECK
						memlistset( Text->text, MEMLISTTYPE_SWITCH_TEXT );
#endif
						strcpy( Text->text,mText->text);
						Text->color=mText->color;
						Text->FontSize=mText->FontSize;

						sw->Switch=Text;				
					}else{
						sw->Switch=NULL;				
					}
					break;
				case SWITCH_BUTTON:
					Button=( BUTTON_SWITCH *)calloc( 1, sizeof(BUTTON_SWITCH) );
					if(Button!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( Button, MEMLISTTYPE_SWITCH_DATA );
#endif
						mButton=(BUTTON_SWITCH *)swd->Switch;
						Button->status=mButton->status;

						sw->Switch=Button;				
					}else{
						sw->Switch=NULL;				
					}
					break;
				case SWITCH_DIALOG:
					Dialog=(DIALOG_SWITCH *)calloc( 1, sizeof(DIALOG_SWITCH) );
					if(Dialog!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( Dialog, MEMLISTTYPE_SWITCH_DATA );
#endif
						mDialog=(DIALOG_SWITCH *)swd->Switch;

						sw->Switch=Dialog;				
					}else{
						sw->Switch=NULL;				
					}
					break;
				case SWITCH_CHATSTR:
					ChatStr=(CHATSTR_SWITCH *)calloc( 1, sizeof(CHATSTR_SWITCH) );
					if(ChatStr!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( ChatStr, MEMLISTTYPE_SWITCH_DATA );
#endif
						mChatStr=(CHATSTR_SWITCH *)swd->Switch;

						sw->Switch=ChatStr;				
					}else{
						sw->Switch=NULL;				
					}
					break;
				case SWITCH_NUMBER:
					Number=( NUMBER_SWITCH *)calloc( 1, sizeof(NUMBER_SWITCH) );
					if(Number!=NULL){
#ifdef PUK2_MEMCHECK
						memlistset( Number, MEMLISTTYPE_SWITCH_DATA );
#endif
						*Number=*( (NUMBER_SWITCH *)swd->Switch );

						sw->Switch=Number;				
					}else{
						sw->Switch=NULL;				
					}
					break;
			}
											
			// 实行关数の设定
			if(swd->func!=NULL){
				sw->func = swd->func;
			}else{
				sw->func = MenuSwitchNone;
			}
			// 次のスイッチへ
			swd++;
		}
	}

	// DragMoveがある场合设定する
	if(DragMoveDate[type]->count>0){
		win->DragMove.count=DragMoveDate[type]->count;
		for(i=0;i<DragMoveDate[type]->count;i++){
			win->DragMove.rect[i].x=DragMoveDate[type]->rect[i].x;
			win->DragMove.rect[i].y=DragMoveDate[type]->rect[i].y;
			win->DragMove.rect[i].w=DragMoveDate[type]->rect[i].w;
			win->DragMove.rect[i].h=DragMoveDate[type]->rect[i].h;
		}
	}

	// 登録完了したら管理バッファに登録
	WindowBuff[ winno ] = win;					// 管理バッファに登録
	win->flag |= WIN_INFO_PRIO;					// プライオリティを最上位に

	// 以前开いたときの状态を、作成されたウィンドウに反映
	win->wx = WindowFlag[ type ].wx;
	win->wy = WindowFlag[ type ].wy;

	WindowFlag[ type ].flag |= 0x80;

	// ウィンドウの初期化（引数－１で初期化处理へ）
	wintmp = wI;
	wI = win;
	win->func( WIN_INIT );
	wI = wintmp;

	//フラグにウインドウのアドレス入れる
	WindowFlag[type].wininfo=win;	

#ifdef _DEBUG
	OutLogOfWinMemory(type,0,(long)win);
#endif
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	if ( type > MENU_WINDOW_MENU ){
		switch(type){
		case MENU_WINDOW_SYSTEM:
		case MENU_WINDOW_SYSTEMSHORTCUT:
		case MENU_WINDOW_BATTLE:
		case MENU_WINDOW_SURPRISE:
		case MENU_WINDOW_MAP:
		case MENU_WINDOW_ACTION:
		case MENU_CHAT_WINDOW:
		case MENU_WINDOW_MAPNAME:
		case MENU_OLD_CHAT_WINDOW:
			break;
		default:
			Lock3DChangeWindowCnt++;
		}
	}
#endif

	return win;
}

//--------------------------------------------------------------------------//
//	メニューウィンドウ退避													//
//--------------------------------------------------------------------------//
void pushMenuWindow( WINDOW_INFO *win )
{
	// 退避フラグを设定
	wF = &WindowFlag[ win->type ];
	wF->flag |= 0x02;

	// 消去处理
	deleteMenuWindow( win );
}

//--------------------------------------------------------------------------//
//	メニューウィンドウをすべて退避											//
//--------------------------------------------------------------------------//
void pushMenuWindowAll( void )
{
	int i;

	// ウィンドウ登録バッファに登録されているウィンドウを全部退避
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		if( WindowBuff[ i ] != NULL ){
			pushMenuWindow( WindowBuff[ i ] );
			WindowBuff[ i ] = NULL;
		}
	}

	//アイテムのドラッグ＆ドロップ初期化
	for(i=0;i<MAX_ITEM;i++)
		ItemNoOpe[i] = 0;

	if (skillrebirth){
		nrproto_AC_send( sockfd, mapGx, mapGy, 62 );		//リバース中止
		skillrebirth = 0;
	}
}

//すべてのウインドウのDelフラグを立てる
void SetDelFlagAllMenuWindow( void ){

	int i;

	// ウィンドウ登録バッファに登録されているウィンドウを全部退避
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		if( WindowFlag[ i ].wininfo != NULL ){
			// 退避フラグを设定
			WindowFlag[ i ].flag |= WIN_INFO_DEL;

		}
	}

	//アイテムのドラッグ＆ドロップ初期化
	for(i=0;i<MAX_ITEM;i++)
		ItemNoOpe[i] = 0;

	if (skillrebirth){
		nrproto_AC_send( sockfd, mapGx, mapGy, 62 );		//リバース中止
		skillrebirth = 0;
	}
}


//复归させるウインドウのフラグ初期化
void ReSetMenuWindowReOpen(void){

	int i;

	//すべてクリア
	for( i = 0 ; i < MENU_WINDOW_TYPE_NUM ; i++ ){
		WindowFlag[ i ].ReOpen=0;
	}

}

//复归させるウインドウのフラグ设定
void SetMenuWindowReOpen(void){

	int i;

	//现在开いているかの取得
	for( i = 0 ; i < MENU_WINDOW_TYPE_NUM ; i++ ){
		if(WindowFlag[ i ].wininfo!=NULL)
			WindowFlag[ i ].ReOpen=1;
	}

}

//	リストのウィンドウをすべて关闭
void CloseWindowSelectType( int Type )
{
	int i;

	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		if(WindowBuff[i]!=NULL){
			if(CloseWindowListFlag[WindowBuff[i]->type] & Type){
				// 退避フラグを设定
				wF = &WindowFlag[ WindowBuff[i]->type ];
				wF->flag |= 0x02;
				// 消去处理
				deleteMenuWindow( WindowBuff[i] );
				WindowBuff[ i ] = NULL;
			}
		}
	}
}

//--------------------------------------------------------------------------//
//	退避されているメニューウィンドウを全部复归								//
//--------------------------------------------------------------------------//
void popMenuWindow( WINDOW_INFO *win )
{
	int i;

	// すべてのウィンドウについて退避フラグをチェック
	for( i = 0 ; i < MENU_WINDOW_TYPE_NUM ; i++ ){
		if( WindowFlag[ i ].flag & 0x02 ){
			// 退避されている场合は、复归させる
			createMenuWindow( i );
			WindowFlag[ i ].flag &= ~( 0x02 );
		}
	}
}

//--------------------------------------------------------------------------//
//	メニューウィンドウ制御の初期化											//
//--------------------------------------------------------------------------//
// ウィンドウドラッグドロップ管理构造体初期化
void initWindowDDSystem(void){

	int i;

	for(i=0;i<MAX_ITEM;i++)
		ItemNoOpe[i] = 0;

	WindowDD.ObjType = WINDD_NONE;				// ドラッグしているものの种类
	WindowDD.ObjData = NULL;					// ドロップしたもののデータ
#ifdef PUK2_NEWDRAG
	WindowDD.DragFunc = NULL;					// ドラッグドロップの处理を行う关数
#else
	WindowDD.SettleDropItem = NULL;				// ドロップしたものが受け取られたときの后始末用关数
#endif
	WindowDD.DropX = WindowDD.DropY = 0;		// ドロップポイント
	WindowDD.WinType = MENU_WINDOW_NONE;		// ドラッグ??ドロップ元のウィンドウ种类
	WindowDD.SwitchNo = -1;						// ドラッグ??ドロップ元のボタン番号

	WindowDD.ProcNo = WINDD_DONOT;

}

// ダイアログ管理构造体の初期化
void initDialogSystem(void){

	DiarogST.SwAdd=NULL;

}

// 特定のウインドウのReOpen
void MenuWindowReOpen(void){

	int i;	

	for( i = 0 ; i < MENU_WINDOW_TYPE_NUM ; i++ ){
		if(WindowFlag[ i ].ReOpen==1){
			switch(i){
				case MENU_WINDOW_MAP:
					createMenuWindow( i );
					WindowFlag[ i ].ReOpen=0;
					break;
				case MENU_WINDOW_ACTION:
					createMenuWindow( i );
					WindowFlag[ i ].ReOpen=0;
					break;
#ifdef PUK3_MAIL_ETC
				// メール关系のウィンドウはまとめて
				// 群邮件は必要ないので除外
				case MENU_WINDOW_HISTORY:
				case MENU_WINDOW_ADDRESS:
				case MENU_WINDOW_ADDRESS_SENDMAIL:
				case MENU_WINDOW_ADDRESS_SENDPETMAIL:

					// アドレスブック开いてたかチェック
					// 开いてたなら开く
					if ( WindowFlag[ MENU_WINDOW_ADDRESS ].ReOpen ){
						createMenuWindow( MENU_WINDOW_ADDRESS );
						WindowFlag[ MENU_WINDOW_ADDRESS ].ReOpen=0;
					}
					// ヒストリー开いてたかチェック
					// 开いてたなら开く
					if ( WindowFlag[ MENU_WINDOW_HISTORY ].ReOpen ){
						int mailHistoryPageback = mailHistoryPage;
						createMenuWindow( MENU_WINDOW_HISTORY );
						mailHistoryPage = mailHistoryPageback;
						WindowFlag[ MENU_WINDOW_HISTORY ].ReOpen=0;
					}

					// メールウィンドウ开いてたかチェック
					// 开いてたなら开く
					if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen ){
						ReOpenMailWindow();
						WindowFlag[ MENU_WINDOW_ADDRESS_SENDMAIL ].ReOpen=0;
					}
					// ペットメールウィンドウ开いてたかチェック
					// 开いてたなら开く
					if ( WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen ){
						createMenuWindow( MENU_WINDOW_ADDRESS_SENDPETMAIL );
						WindowFlag[ MENU_WINDOW_ADDRESS_SENDPETMAIL ].ReOpen=0;
					}
					break;
#endif
				default:
					WindowFlag[ i ].ReOpen=0;
					break;
			}
		}
	}
}

// 指定した矩形にマウスカーソルが重なっているか？
#ifdef PUK2_NEWDRAG
BOOL checkMouseCursor( int x, int y, int w, int h )
#else
static BOOL checkMouseCursor( int x, int y, int w, int h )
#endif
{
	int dx,dy;

#ifdef PUK2_NEWDRAG
#else
	if( MouseNoHitFlag ) return FALSE;
#endif

	dx = mouse.nowPoint.x - x;
	if( dx >= 0 && dx < w ){
		dy = mouse.nowPoint.y - y;
		if( dy >= 0 && dy < h ){
			return TRUE;
		}
	}

	return FALSE;
}

// 指定した矩形にマウスカーソルが重なっているか？
static BOOL checkMouseCursor_onDragging( int x, int y, int w, int h )
{
	int dx,dy;

	if ( WindowDD.ProcNo != WINDD_DRAGGING ){
		if( MouseNoHitFlag ) return FALSE;
	}

	dx = mouse.nowPoint.x - x;
	if( dx >= 0 && dx < w ){
		dy = mouse.nowPoint.y - y;
		if( dy >= 0 && dy < h ){
			return TRUE;
		}
	}

	return FALSE;
}


// スイッチにカーソルがあっているときに枠を表示する
static void displaySwitchFrame( SWITCH_INFO *sw, int flag, int col )
{
	int	x,y,w,h;

	// マウスカーソルがあってる场合、枠を表示
	if( flag & MENU_MOUSE_OVER ){
/*
		x = wI->wx + sw->ofx - sw->sx - 1;
		y = wI->wy + sw->ofy - sw->sy - 1;
		w = sw->sx * 2 + 2;
		h = sw->sy * 2 + 2;
*/
		x = wI->wx + sw->ofx - 1;
		y = wI->wy + sw->ofy - 1;
		w = sw->sx + 2;
		h = sw->sy + 2;

		StockBoxDispBuffer( x, y, x+w, y+h, DISP_PRIO_WIN2, col, 0 );
	}
}

//--------------------------------------------------------------------------//
//	メニューウィンドウの通常表示											//
//--------------------------------------------------------------------------//
void displayMenuWindow( void )
{
#ifdef PUK2
	int i;
#else
	int i,j;
#endif
	int x,y;
	SWITCH_INFO *sw;
	BLT_MEMBER bm={0};
//	BUTTON_SWITCH	*Button;
	TEXT_SWITCH		*Text;
	GRAPHIC_SWITCH	*Graph;
	DIALOG_SWITCH *Dialog;
	NUMBER_SWITCH	*Number;
	S2 X,Y,X2,Y2;
	U4 work;

	// スイッチ类の表示
	for( i = 0 ; i < wI->swNum ; i++ ){

		if(wI->sw[i].Enabled!=TRUE)
			continue;

		sw = &wI->sw[ i ];

		x = wI->wx + sw->ofx;
		y = wI->wy + sw->ofy;

		switch(sw->type){
			case SWITCH_NONE:				//なにもしない

				break;
			case SWITCH_GRAPHIC:			//静止画像
				Graph=(GRAPHIC_SWITCH *)sw->Switch;
				if (Graph->graNo != NULL){
					if(Graph->u==0 && Graph->v==0 && Graph->w==0 && Graph->h==0){
						bm.u=0;
						bm.v=0;
						bm.w=0;
						bm.h=0;
						bm.rgba.rgba=Graph->rgba;
						bm.BltVer=BLTVER_NOMAL;
						bm.bltf=BLTF_NOCHG;
						bm.PalNo=0;
						StockDispBuffer_PUK2( wI->wx+sw->ofx, wI->wy+sw->ofy,  DISP_PRIO_WIN2, Graph->graNo, 0, 1, &bm);
					}else{
						realGetNo( (U4)Graph->graNo, &work );
						realGetPos( work, &X, &Y);
						X2=X;
						Y2=Y;

						bm.u=Graph->u+X2;
						bm.v=Graph->v+Y2;
						bm.w=Graph->w;
						bm.h=Graph->h;
						bm.rgba.rgba=Graph->rgba;
						bm.BltVer=BLTVER_PUK2;
						bm.bltf=BLTF_NOCHG;
						bm.PalNo=0;
						StockDispBuffer_PUK2( wI->wx+sw->ofx, wI->wy+sw->ofy,  DISP_PRIO_WIN2, Graph->graNo, 0, 1, &bm);
					}
				}
				break;
			case SWITCH_ACTION:				//アクション
				ACTION *ActionAdd;
				ActionAdd=((ACTION_SWITCH *)sw->Switch)->ActionAdd;
				pattern( ActionAdd, ANM_NOMAL_SPD, ( ((ACTION_SWITCH *)sw->Switch)->AnimNoLoop ? ANM_NO_LOOP : ANM_LOOP ) );

				if(  ActionAdd->bmpNo != NULL ){
					if (ActionAdd->bltfon){
						ActionAdd->bm.bltf &= ~ActionAdd->bltfon;
						ActionAdd->bm.bltf |= ActionAdd->bltf & ActionAdd->bltfon;
					}
					if (ActionAdd->rgbaon){
						if (ActionAdd->rgbaon&1) ActionAdd->bm.rgba.r = ActionAdd->rgba.r;
						if (ActionAdd->rgbaon&2) ActionAdd->bm.rgba.g = ActionAdd->rgba.g;
						if (ActionAdd->rgbaon&4) ActionAdd->bm.rgba.b = ActionAdd->rgba.b;
						if (ActionAdd->rgbaon&8) ActionAdd->bm.rgba.a = ActionAdd->rgba.a;
					}
					// アクションを自力で表示设定
					StockDispBuffer2( wI->wx+sw->ofx+Blt_adjust(ActionAdd,0), wI->wy+sw->ofy+Blt_adjust(ActionAdd,1), DISP_PRIO_WIN2, ActionAdd->bmpNo, FALSE, &ActionAdd->bm );
				}
				break;
			case SWITCH_TEXT:				//テキスト
				Text=(TEXT_SWITCH *)sw->Switch;
				if( Text->text != NULL ){
						int tx,ty;

						tx = x - sw->sx;
						ty = y - sw->sy;
						StockFontBuffer( tx, ty, FONT_PRIO_WIN, Text->FontSize, Text->color, Text->text, 0 );	//一时的
				}

				break;

			case SWITCH_BUTTON:				//ボタン

				break;
			case SWITCH_CHATSTR:			//チャットの履历文字表示
				MenuChatStockFontBuff(x+5,y);
				break;

			case SWITCH_DIALOG:				//ダイアログ表示
				if(DiarogST.SwAdd==sw->Switch){
					//自分が使用中のときのみ
					Dialog=(DIALOG_SWITCH *)sw->Switch;
					if(pNowInputStr==&MyChatBuffer){
						if(ChatWindowView==1)
							MenuChatStockFontBuffInput(Dialog->InpuStrAdd);
					}else{
						//その他ダイアログ
						if(Dialog!=NULL)
							MenuDialogStockFontBuffInput(Dialog->InpuStrAdd);
					}
				}
				break;
			case SWITCH_NUMBER:
				Number=(NUMBER_SWITCH *)sw->Switch;
				if(Number!=NULL){
					DrawGraphicNumber( wI->wx+sw->ofx, wI->wy+sw->ofy, Number->str, Number->size, Number->color, Number->flag, DISP_PRIO_WIN2 );
				}
				break;
		};

#if defined(_DEBUG)||defined(_RELDEB)
		//スイッチの背景を表示
		if(NewWinDebugFlag & NEW_WIN_DEBUG_SW){
			StockAlphaDispBuffer( wI->wx+sw->ofx, wI->wy+sw->ofy, sw->sx, sw->sy, DISP_PRIO_TOP, 0x800000FF );
		}

		if(NewWinDebugFlag & NEW_WIN_DEBUG_WN){
			if(sw->type==SWITCH_NONE){
				StockAlphaDispBuffer( wI->wx+sw->ofx, wI->wy+sw->ofy, sw->sx, sw->sy, DISP_PRIO_TOP, 0x80FF0000 );
			}
		}
#endif

	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	//聊天范围表示
	NewChatAreaProc();

#if defined(_DEBUG)||defined(_RELDEB)
	if(NewWinDebugFlag & NEW_WIN_DEBUG_WN){
		//ウィンドウの背景を表示
		StockAlphaDispBuffer( wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_TOP, 0x80FF0000 );
	}

	if(NewWinDebugFlag & NEW_WIN_DEBUG_MV){
		for(i=0;i<wI->DragMove.count;i++){
			//ドラッグできる范围を表示
			StockAlphaDispBuffer( wI->wx+wI->DragMove.rect[i].x,
								  wI->wy+wI->DragMove.rect[i].y,
								  wI->DragMove.rect[i].w,
								  wI->DragMove.rect[i].h,
								  DISP_PRIO_TOP, 0x8000FF00 );
		}
	}
#endif
}

//--------------------------------------------------------------------------//
//	メニューウィンドウに对するマウスの判定									//
//--------------------------------------------------------------------------//
unsigned int checkMenuWindowMouse( void )
{
	unsigned int flag = 0;
	int i;

	// ドラッグ中は、この处理をしない
	if ( WindowDD.ProcNo != WINDD_DRAGGING ){
		// ウィンドウの移动チェック
		if( !( wI->flag & MENU_ATTR_NOMOVE ) ){
			// 移动中なら、左ボタンが离されるまで移动
			if( wI->flag & WIN_INFO_MOVING ){
				if( mouse.state & MOUSE_LEFT_CRICK ){
					wI->wx += mouse.nowPoint.x - wI->mx;
					wI->wy += mouse.nowPoint.y - wI->my;

					wI->mx = mouse.nowPoint.x;
					wI->my = mouse.nowPoint.y;
				}else{
					wI->flag &= ~( WIN_INFO_MOVING );
				}
				flag |= 0x02;
			}else{
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					int x,y,w,h;

					for (i=0;i<wI->DragMove.count;i++){					
						x = wI->wx + wI->DragMove.rect[i].x;
						y = wI->wy + wI->DragMove.rect[i].y;
						w = wI->DragMove.rect[i].w;
						h = wI->DragMove.rect[i].h;

#ifdef PUK2_NEWDRAG
						if( !MouseNoHitFlag && checkMouseCursor( x, y, w, h ) ){
#else
						if( checkMouseCursor( x, y, w, h ) ){
#endif
							wI->mx = mouse.nowPoint.x;
							wI->my = mouse.nowPoint.y;

							wI->flag |= WIN_INFO_MOVING;
							flag |= 0x02;
						}
					}
				}
			}
		}
	}

	// 移动しなかった场合
	if( !( wI->flag & MENU_ATTR_NOHIT ) ){
#ifdef PUK2_NEWDRAG
		// 通常时
		if ( !( WindowDD.ProcNo == WINDD_DRAGGING || WindowDD.ProcNo == WINDD_DROPSEEKING ) ){
			if( !MouseNoHitFlag && checkMouseCursor( wI->wx, wI->wy, wI->sx, wI->sy ) ){
				flag |= 0x01;
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					wI->flag |= WIN_INFO_PRIO;
				}
			}
		}
		// ドラッグ中
		else{
			if( checkMouseCursor( wI->wx, wI->wy, wI->sx, wI->sy ) ){
				flag |= 0x01;
			}
		}
#else
		if( checkMouseCursor_onDragging( wI->wx, wI->wy, wI->sx, wI->sy ) ){
			flag |= 0x01;

			// プライオリティの调整
			if ( WindowDD.ProcNo != WINDD_DRAGGING ){
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					wI->flag |= WIN_INFO_PRIO;
				}
			}

			// これ以降のマウスの判定を切ってやる
//			mouse.level = DISP_PRIO_MENU;
//			MouseNoHitFlag = 1;
		}
#endif
	}


	return flag;
}

//--------------------------------------------------------------------------//
//	メニューウィンドウの整理												//
//--------------------------------------------------------------------------//
int arrangementMenuWindow( void )
{
	int i,j;
	int num = 0;
	WINDOW_INFO *win;

	// ウィンドウバッファ全体をチェック
	for( i = 0 ; i < MENU_WINDOW_MAX ; i++ ){
		win = WindowBuff[ i ];

		// バッファが空の场合
		if( win == NULL ){
			// 次のバッファが见つかるまで检索
			for( j = i ; j < MENU_WINDOW_MAX ; j++ ){
				if( WindowBuff[ j ] != NULL ){
					break;
				}
			}

			// ここより后ろに何も无かった场合
			if( j == MENU_WINDOW_MAX ){
				break;
			}else{
				WindowBuff[ i ] = WindowBuff[ j ];
				WindowBuff[ j ] = NULL;
				i--;
			}
		}else
		// メニューウィンドウを消去する场合
		if( win->flag & WIN_INFO_DEL ){
			// ウィンドウを消去
			deleteMenuWindow( win );

			// ここより后ろを诘める
			for( j = i ; j < ( MENU_WINDOW_MAX - 1 ) ; j++ ){
				WindowBuff[ j ] = WindowBuff[ j + 1 ];
			}
			WindowBuff[ j ] = NULL;

			// ずらした场合はもう一回このバッファをチェック
			if( j != i ){
				i--;
			}
		}else{
			// プライオリティ变更？
			if( win->flag & WIN_INFO_PRIO ){
				// 自分と同じプライオリティの先头に移动
				for( j = i ; j > 0 ; j-- ){
					if( WindowBuff[ j - 1 ]->prio < win->prio ){
						break;
					}
					WindowBuff[ j ] = WindowBuff[ j - 1 ];
				}
				WindowBuff[ j ] = win;

				// プライオリティフラグを消す
				win->flag &= ~(WIN_INFO_PRIO);
			}

			// ウィンドウの数＋１
			num++;
		}
	}

	return num;
}

//--------------------------------------------------------------------------//
//	メニュースイッチ制御													//
//--------------------------------------------------------------------------//
void MenuSwitchProc( int no )
{
	int x,y,w,h;
	unsigned int status = 0;
	SWITCH_INFO *sw = &wI->sw[ no ];
	BUTTON_SWITCH *BtSw;

	// 判定范围の设定
	x = wI->wx + sw->ofx;
	y = wI->wy + sw->ofy;
	w = sw->sx;
	h = sw->sy;
#ifdef PUK2_NEWDRAG
	// ドラッグ中
	if ( WindowDD.ProcNo == WINDD_DRAGGING ||
		 WindowDD.ProcNo == WINDD_DROPSEEKING ||
		 WindowDD.ProcNo == WINDD_DRAGCANCEL ||
		 WindowDD.ProcNo == WINDD_DROPCANCEL ){
		// マウスが重なってるなら
		if( (mouse.level < DISP_PRIO_MENU) && checkMouseCursor( x, y, w, h ) ){
#else
	// ドラッグ中( checkMouseCursor()の动作が通常时とちょっと违う )
	if ( WindowDD.ProcNo == WINDD_DRAGGING ){
		// マウスが重なってるなら
		if( (mouse.level < DISP_PRIO_MENU) && checkMouseCursor_onDragging( x, y, w, h ) ){
#endif
#ifdef PUK2_NEWDRAG
			// ドラッグ中
			if ( WindowDD.ProcNo == WINDD_DRAGGING ||
				 WindowDD.ProcNo == WINDD_DROPSEEKING ){
				// マウスが重なってるよ
				status |= MENU_MOUSE_DRAGOVER;
			}
#else
			// マウスが重なってるよ
			status |= MENU_MOUSE_DRAGOVER;
#endif

			// ドラッグ元のボタンなら
			if ( WindowDD.WinType == nowWinType ){
				if ( WindowDD.SwitchNo == nowBtnNo ){
					// 左ボタンが押されたよ（押されたフレームのみ）
					if( mouse.onceState & MOUSE_LEFT_CRICK ) status |= MENU_MOUSE_LEFT;
					// 右ボタンが押されたよ（押されたフレームのみ）
					if( mouse.onceState & MOUSE_RIGHT_CRICK ) status |= MENU_MOUSE_RIGHT;
					// 左ボタンが押されてるよ（押されてればいつでも）
					if( mouse.state & MOUSE_LEFT_CRICK ) status |= MENU_MOUSE_LEFTHOLD;
					// 右ボタンが押されてるよ（押されてればいつでも）
					if( mouse.state & MOUSE_RIGHT_CRICK ) status |= MENU_MOUSE_RIGHTHOLD;
					// 左ボタンがダブルクリックされてるよ
					if( mouse.onceState & MOUSE_LEFT_DBL_CRICK ) status |= MENU_MOUSE_LEFT_DC;
					// 右ボタンがダブルクリックされてるよ
					if( mouse.onceState & MOUSE_RIGHT_DBL_CRICK ) status |= MENU_MOUSE_RIGHT_DC;
					// 左ボタンがリピートしたよ
					if( mouse.autoState & MOUSE_LEFT_CRICK ) status |= MENU_MOUSE_LEFTAUTO;
					// 右ボタンがリピートしたよ
					if( mouse.autoState & MOUSE_RIGHT_CRICK ) status |= MENU_MOUSE_RIGHTAUTO;
				}
			}
		}
	}else
	// 通常时
#ifdef PUK2_NEWDRAG
	if( !MouseNoHitFlag && checkMouseCursor( x, y, w, h ) ){
#else
	if( checkMouseCursor( x, y, w, h ) ){
#endif
		// マウスが重なってるよ
		status |= MENU_MOUSE_OVER;

		// 左ボタンが押されたよ（押されたフレームのみ）
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			status |= MENU_MOUSE_LEFT;
		}
		// 右ボタンが押されたよ（押されたフレームのみ）
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			status |= MENU_MOUSE_RIGHT;
		}
		// 左ボタンが押されてるよ（押されてればいつでも）
		if( mouse.state & MOUSE_LEFT_CRICK ){
			status |= MENU_MOUSE_LEFTHOLD;
		}
		// 右ボタンが押されてるよ（押されてればいつでも）
		if( mouse.state & MOUSE_RIGHT_CRICK ){
			status |= MENU_MOUSE_RIGHTHOLD;
		}
		// 左ボタンがダブルクリックされてるよ
		if( mouse.onceState & MOUSE_LEFT_DBL_CRICK ){
			status |= MENU_MOUSE_LEFT_DC;
		}
		// 右ボタンがダブルクリックされてるよ
		if( mouse.onceState & MOUSE_RIGHT_DBL_CRICK ){
			status |= MENU_MOUSE_RIGHT_DC;
		}
		// 左ボタンがリピートしたよ
		if( mouse.autoState & MOUSE_LEFT_CRICK ){
			status |= MENU_MOUSE_LEFTAUTO;
		}
		// 右ボタンがリピートしたよ
		if( mouse.autoState & MOUSE_RIGHT_CRICK ){
			status |= MENU_MOUSE_RIGHTAUTO;
		}

#ifdef PUK2_NEWDRAG
#else
		if ( WindowDD.ProcNo == WINDD_DROPSEEKING ){
			// ドロップポイントが、スイッチに重なっているなら
			if( WindowDD.DropX-x >= 0 && WindowDD.DropX-x < w ){
				if( WindowDD.DropY-y >= 0 && WindowDD.DropY-y < h ){
					status |= MENU_MOUSE_DROP;
				}
			}
		}
#endif

		//ボタンのとき
		if(wI->sw[ no ].type==SWITCH_BUTTON){
			BtSw=(BUTTON_SWITCH *)wI->sw[ no ].Switch;
			BtSw->x=mouse.nowPoint.x- (wI->wx + wI->sw[ no ].ofx);
			BtSw->y=mouse.nowPoint.y- (wI->wy + wI->sw[ no ].ofy);
		}
	}
#ifdef PUK2_NEWDRAG
	if ( WindowDD.ProcNo == WINDD_DROPSEEKING ){
		// ドロップポイントが、スイッチに重なっているなら
		if( WindowDD.DropX-x >= 0 && WindowDD.DropX-x < w ){
			if( WindowDD.DropY-y >= 0 && WindowDD.DropY-y < h ){
				status |= MENU_MOUSE_DROP;
			}
		}
	}
#else
	if ( WindowDD.ProcNo == WINDD_DROPRETURN ){
		if ( WindowDD.WinType == nowWinType ){
			if ( WindowDD.SwitchNo == nowBtnNo ){
				status |= MENU_MOUSE_DROPRETURN;
			}
		}
	}
#endif

	// 实行关数へ
	if(sw->Enabled==TRUE){	
		if(	sw->func( no, status )){
			// これ以降のマウスの判定は切ってやる
			mouse.level = DISP_PRIO_MENU;
			MouseNoHitFlag = 1;

#ifdef PUK2_NEWDRAG
			if ( WindowDD.ProcNo == WINDD_DROPSEEKING ){
				if (WindowDD.DragFunc) WindowDD.DragFunc( WINDDPROC_DROP_NOACCEPT, 0, WindowDD.ObjData );
				WinDD_DragFinish();
			}
#else
			if ( WindowDD.ProcNo == WINDD_DROPSEEKING ) WinDD_GetObject();
#endif
		}
	}
}

#ifdef PUK2_NEWDRAG
void MenuWindowDragProc()
{
	// ドラッグ中
	if ( WindowDD.ProcNo == WINDD_DRAGGING && WindowDD.DragFunc ){
		unsigned int status = 0;

		// 左ボタンが押されたよ（押されたフレームのみ）
		if( mouse.onceState & MOUSE_LEFT_CRICK )		status |= MENU_MOUSE_LEFT;
		// 右ボタンが押されたよ（押されたフレームのみ）
		if( mouse.onceState & MOUSE_RIGHT_CRICK )		status |= MENU_MOUSE_RIGHT;
		// 左ボタンが押されてるよ（押されてればいつでも）
		if( mouse.state & MOUSE_LEFT_CRICK )			status |= MENU_MOUSE_LEFTHOLD;
		// 右ボタンが押されてるよ（押されてればいつでも）
		if( mouse.state & MOUSE_RIGHT_CRICK )			status |= MENU_MOUSE_RIGHTHOLD;
		// 左ボタンがダブルクリックされてるよ
		if( mouse.onceState & MOUSE_LEFT_DBL_CRICK )	status |= MENU_MOUSE_LEFT_DC;
		// 右ボタンがダブルクリックされてるよ
		if( mouse.onceState & MOUSE_RIGHT_DBL_CRICK )	status |= MENU_MOUSE_RIGHT_DC;
		// 左ボタンがリピートしたよ
		if( mouse.autoState & MOUSE_LEFT_CRICK )		status |= MENU_MOUSE_LEFTAUTO;
		// 右ボタンがリピートしたよ
		if( mouse.autoState & MOUSE_RIGHT_CRICK )		status |= MENU_MOUSE_RIGHTAUTO;

		WindowDD.DragFunc( WINDDPROC_DRAG, status, WindowDD.ObjData );
	}
	else if ( WindowDD.ProcNo == WINDD_DRAGCANCEL ) WindowDD.ProcNo = WINDD_DONOT;

	// ドロップ处理
	if ( WindowDD.ProcNo == WINDD_DROPENTRY ) WindowDD.ProcNo = WINDD_DROPSEEKING;
	else if ( WindowDD.ProcNo == WINDD_DROPSEEKING ){
		if (WindowDD.DragFunc) WindowDD.DragFunc( WINDDPROC_DROP_FIELD, 0, WindowDD.ObjData );

		// 初期化
		WindowDD.ObjType = WINDD_NONE;
		WindowDD.ObjData = NULL;
		WindowDD.DragFunc = NULL;
		WindowDD.DropX = 0;
		WindowDD.DropY = 0;

		WindowDD.WinType = MENU_WINDOW_NONE;
		WindowDD.SwitchNo = -1;

		WindowDD.ProcNo = WINDD_DONOT;
	}
	else if ( WindowDD.ProcNo == WINDD_DROPCANCEL ) WindowDD.ProcNo = WINDD_DONOT;

	// ドラッグ中なら、マウスのあたり判定をOffに
	if ( WindowDD.ProcNo == WINDD_DRAGGING ||
		 WindowDD.ProcNo == WINDD_DROPSEEKING ){
		MouseNoHitFlag = 1;
		// あたりを调べるためにマウス等级を下げる
		mouse.level = DISP_PRIO_TILE;
	}
}
#endif
//--------------------------------------------------------------------------//
//	メニューウィンドウ制御	（コクピットメイン处理）						//
//--------------------------------------------------------------------------//
void MenuWindowProc( void )
{
	int i;
	int status;
	BOOL mf = TRUE;	// マウス有效
	BOOL text = TRUE;	// テキスト入力有效

	// 各种ワークを设定
	gridCursorFlag = FALSE;			// 通常グリッドカーソルの处理はしない
	gridCursorDrawFlag = FALSE;		// 通常グリッドカーソルは出ない
	MouseNoHitFlag = 0;				// マウスのあたり判定をＯｎに

#ifdef PUK2_NEWDRAG
	MenuWindowDragProc();
#else
	// ドラッグ中なら、マウスのあたり判定をOffに
	if ( WindowDD.ProcNo == WINDD_DRAGGING ){
		MouseNoHitFlag = 1;
		// あたりを调べるためにマウス等级を下げる
		mouse.level = DISP_PRIO_TILE;
	}

	if ( WindowDD.ProcNo == WINDD_DROPENTRY ) WindowDD.ProcNo = WINDD_DROPSEEKING;
	else if ( WindowDD.ProcNo == WINDD_DROPSEEKING ) WindowDD.ProcNo = WINDD_DROPRETURN;
	else if ( WindowDD.ProcNo == WINDD_DROPRETURN ){
		// 初期化
		WindowDD.ObjType = WINDD_NONE;
		WindowDD.ObjData = NULL;
		WindowDD.SettleDropItem = NULL;
		WindowDD.DropX = 0;
		WindowDD.DropY = 0;

		WindowDD.WinType = MENU_WINDOW_NONE;
		WindowDD.SwitchNo = -1;

		WindowDD.ProcNo = WINDD_DONOT;
	}
#endif

	// 处理前にウィンドウのバッファを整理
	MenuWinMax = arrangementMenuWindow();

	// ウィンドウ处理ループ
	for( i = 0 ; i < MenuWinMax ; i++ ){
		wI = WindowBuff[ i ];
		wF = &WindowFlag[ wI->type ];
		nowWinType = wI->type;
#ifdef PUK3_SEGMENTATION_FAULT
		ProcStack( nowWinType );
#endif

		// ウィンドウに对するマウスの处理
		status = checkMenuWindowMouse();

		//ウインドウ计算处理
		mf = wI->func( mf );

		// スイッチの个数分处理する
		for( nowBtnNo = 0 ; nowBtnNo < wI->swNum ; nowBtnNo++ ){
			// スイッチの计算と描画处理
			if(wI->sw[nowBtnNo].Enabled==TRUE)
				MenuSwitchProc( nowBtnNo );

		}
		nowBtnNo = -1;

		// 何らかのレスあり
		if( status ){
			// これ以降のマウスの判定を切ってやる
			mouse.level = DISP_PRIO_MENU;
			MouseNoHitFlag = 1;

#ifdef PUK2_NEWDRAG
			if ( WindowDD.ProcNo == WINDD_DROPSEEKING ){
				if (WindowDD.DragFunc) WindowDD.DragFunc( WINDDPROC_DROP_NOACCEPT, 0, WindowDD.ObjData );
				WinDD_DragFinish();
			}
#else
			if ( WindowDD.ProcNo == WINDD_DROPSEEKING ) WinDD_GetObject();
#endif

			mf = FALSE;
		}

		// ウィンドウ描画处理
		mf = wI->funcDraw( mf );
#ifdef PUK3_SEGMENTATION_FAULT
		ProcPop();
#endif
	}

	//ショートカットの处理	
	MenuWindowShortCut();

	// チャット登録文字列のショートカットキーのチェック
	shortCutSendChatReg();
	
	nowWinType = MENU_WINDOW_NONE;

	// ドラッグ中なら
	if ( WindowDD.ProcNo == WINDD_DRAGGING ){
		// マウスがフィールド上にあるなら
		if ( mouse.level < DISP_PRIO_MENU ){
			gridCursorDrawFlag = TRUE;		// 通常グリッドカーソルを强制表示
			mouse.level = DISP_PRIO_MENU;
		}
	}

}

#include "menuWinEtcObj.cpp"

