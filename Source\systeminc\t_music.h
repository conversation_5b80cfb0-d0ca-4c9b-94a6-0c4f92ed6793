﻿/************************/
/*	t_music.h			*/
/************************/
#ifndef _T_MUSIC_
#define _T_MUSIC_

#include <mmsystem.h>
#include <dsound.h>


#define T_MUSIC_MONO	0
#define T_MUSIC_STEREO	1

#ifdef PUK2
	#define TONE_MAX	500		//SE番号の最大数
	#define ENV_SE_MAX 50		// 环境音の最大数
#else

#define TONE_MAX	320		//SE番号の最大数

#endif


#define MAX_BGM_VOL		15		// ＢＧＭの最大音量
#define MAX_SE_VOL		15		// ＳＥの最大音量
#define MAX_PITCH		8		// ピッチの最大数

#define BGM_NO_START	200		// ＢＧＭ番号の开始位置
#define BGM_NO_END		249		// ＢＧＭ番号の終了位置
#define ENV_NO_START	250		// 环境音番号の开始位置
#define ENV_NO_END		299		// 环境音番号の終了位置

#ifdef _USE_RECENT_SOUND_LIST
#define MAX_RECENT_SOUND_LIST	10		// 最近使ったサウンドを保存するテーブルの最大确保数
#endif /* _USE_RECENT_SOUND_LIST */

enum
{
	BGM_CASTLE					=   0,
	BGM_TOWN					=   1,
	BGM_VILLAGE1				=   2,
	BGM_VILLAGE2				=   3,
	BGM_VILLAGE3				=   4,

	BGM_FIELD1					=   5,
	BGM_FIELD2					=   6,
	BGM_FIELD3					=   7,

	BGM_DUNGEON1				=   8,
	BGM_DUNGEON2				=   9,
	BGM_DUNGEON3				=   10,
	BGM_DUNGEON4				=   11,
	BGM_DUNGEON5				=   12,

	BGM_BATTLE					=   13,
	BGM_BATTLE_BOSS				=   14,
	BGM_BATTLE_LAST_BOSS		=   15,
	BGM_BATTLE_DUEL				=   16,

	BGM_TITLE					=   17,

	BGM_SHIP					=   18,
	BGM_MINEGARU_FIELD			=   19,
	BGM_MINEGARU_TOWN			=   20,
	BGM_KURUKUSU_FIELD			=   21,
	BGM_KURUKUSU_TOWN			=   22,
	BGM_EXTRA_LOOP				=   23,
	BGM_EXTRA_NOLOOP			=   24,

#ifdef PUK2
	BGM_PUK2_BATTLE01			=   25,
	BGM_PUK2_BATTLE02			=   26,

	BGM_PUK2_FIELD01			=   27,

	BGM_PUK2_MATI01				=   28,
	BGM_PUK2_SHINDEN01			=   29,

	BGM_PUK2_YAMA				=   30,
	BGM_PUK2_HAIKYO				=   31,
	BGM_PUK2_M_TOWN				=   32,
	BGM_PUK2_OP					=   33,
#endif

#ifdef PUK3
	BGM_PUK3_BATTLE1			=   34,
	BGM_PUK3_BATTLE2			=   35,
	BGM_PUK3_DUNGEON			=   36,
	BGM_PUK3_KAME				=   37,
	BGM_PUK3_KUJIRA				=   38,
	BGM_PUK3_KUMO				=   39,
	BGM_PUK3_LOVE				=   40,
	BGM_PUK3_PLAYER_BATTLE		=   41,
	BGM_PUK3_OP					=   42,
#endif
#ifdef PUK3_NEWMUSIC
	BGM_PUK3_ORGEL				=   43,
#endif

#ifdef PUK2
	BGM_NON
#else
	//BGM_NON						=   18
#endif
};


typedef struct
{
	int name;
	int tone_no;
	int volume;
	int note_no;
	int release_cnt;
	int release_spd;
	int lfo_h;
	int lfo_m;
	int lfo_s;
	int lfo_s_cnt;
	int lfo_s_ang;
	int pan;
	int loop_flg;
	int delay_cnt;
	long freq;
} VOICE_EQU;


typedef struct
{
	int voice_address;	// ボイスの场所（－１はボイスなし）
	int tone;
	int count;
	int distance;
	int volume;
	int volume_old;
	int side;
	int panpot;
	int panpot_old;
} ENVIRONMENT_EQU;


typedef struct
{
	int voice_place;	//ボイスの场所（－１はボイスなし）
	int voice_cnt;		//ボイスの数
	int voice_loop;		//ループフラグ
	int voice_volume;		//音色ボリューム
	int voice_note;		//ノート番号
	int play_time;		//再生时间
	int voice_rate;
	int voice_address;
	char filename[64];	// ファイル名
} TONE_EQU;

typedef struct
{
	char fname[64];
	int volume;
	char loop_flg;
	int loop_point;
} T_MUSIC_BGM;


typedef struct
{
	short no;
	char name[64];
	char vol;
	char note;
	char loop_flg;
} SNDCONFIG;


#ifdef PUK2
typedef struct {
	BOOL isPlayed;
	int seno;
	int x;
	int y;
	int distance;
} ENV_SE;
#endif
extern T_MUSIC_BGM bgm_tbl[];

extern TONE_EQU tone_tbl[];

extern int cdda_no;
extern int stereo_flg;
extern int t_music_se_volume;
extern int t_music_bgm_volume;
extern int t_music_bgm_no;
extern int t_music_se_no;
extern char t_music_bgm_pitch[16];
extern int draw_map_bgm_flg;
extern int map_bgm_no;
#ifdef PUK2_NOSOUNDMUSIC
	extern int soundSoundFlag;	// 效果音を鸣らすかのフラグ
	extern int musicSoundFlag;	// ＢＧＭを鸣らすかのフラグ
#endif

int t_music_init( void );
void t_music_end( void );
void music_init( void );

int initDSound( void );

BOOL WaveFormatRead( HMMIO, WAVEFORMATEX *, DWORD * );
BOOL WaveDataRead( HMMIO, DWORD *, LPDIRECTSOUNDBUFFER );
BOOL dwSoundInit( WAVEFORMATEX *, DWORD, LPDIRECTSOUNDBUFFER * );

void stop_thread( void );


void t_music_end( void );
bool cdda_play(int);
bool cdda_stop(void);
int play_se(int tone, int x, int y);
int play_bgm(int bgm_no);
void bgm_volume_change(void);
void set_bgm_pitch(void);
void stop_bgm(void);
void fade_out_bgm(void);
int play_environment(int tone, int x, int y);
int play_map_bgm(int tone);

#ifdef _USE_RECENT_SOUND_LIST
BOOL usecheck_recent_sound_list( int tone);
BOOL settop_recent_sound_list( int index, int tone);
BOOL add_recent_sound_list( int tone);
#endif /* _USE_RECENT_SOUND_LIST */

#ifdef PUK2
void playEnvironmentSE ();
void setEnvironmentSE (int seno, int x, int y);
void environmentSEClean ();
#endif

#endif
