﻿#ifndef _PC_H_ 
#define _PC_H_

#include"action.h"

//-------------------------------------------------------------------------//
// 定数定义                                                                //
//-------------------------------------------------------------------------//
#define MAXCHARACTER			2		// サーバーに保存する最大キャラ数

#define CHAR_NAME_LEN			16		// キャラの名称の长さ

#define CHAR_FREENAME_LEN		16		// ユーザが设定した称号の长さ

#define CHAR_TITLE_LEN			16		// 称号の文字数
#define CHAR_TITLE_LINE			8		// 称号の１ページあたりの表示行数

#ifdef PUK2_TITLE48
#define CHAR_TITLE_PAGE			6		// 称号のページ数
#else
#define CHAR_TITLE_PAGE			3		// 称号のページ数
#endif

#define CHAR_TITLE_MAX			(CHAR_TITLE_LINE*CHAR_TITLE_PAGE)// 称号の総数

#define MAGIC_NAME_LEN			24		// 咒术名の长さ
#define MAGIC_MEMO_LEN			72		// 咒术の说明文の长さ

#define MAX_EQUIP_ITEM			8		// 装备栏の数
#define ITEM_DRAW_LINE			4		// アイテム表示行数
#define ITEM_DRAW_COLUMN		5		// アイテム表示縦行
#define MAX_DRAW_WIN_ITEM		(ITEM_DRAW_LINE*ITEM_DRAW_COLUMN)// アイテム栏の数
#define MAX_ITEM				(MAX_DRAW_WIN_ITEM+MAX_EQUIP_ITEM)// 最大アイテム数（装备８个＋所持２０个）
#define ITEM_NAME_LEN			28		// アイテム名の长さ
#define ITEM_NAME2_LEN			16		// アイテム名２の长さ
#define ITEM_FREENAME_LEN		16		// 刻印文字数
#define ITEM_MEMO_LINE_LEN		32		// アイテム说明の１行の文字数
#define ITEM_MEMO_LINE			8		// アイテム说明の１ページの行数
#define ITEM_MEMO_PAGE			2		// アイテム说明のページ数
#define ITEM_MEMO_LEN			(ITEM_MEMO_LINE_LEN*ITEM_MEMO_LINE*ITEM_MEMO_PAGE)// アイテムの说明の长さ

#define PET_NAME_LEN			16		// ペットの名称の长さ
#define PET_FREENAME_LEN		32		// ペットの称号の长さ
#define MAX_PET					5		// 最大ペット取得数

#define MAX_PARTY				5		// 最大仲间数（自分を含む）

#define ADDRESS_BOOK_LINE		4		// １ページあたりの表示人数
#define ADDRESS_BOOK_PAGE		15		// 最大ページ数
#define ADDRESS_BOOK			(ADDRESS_BOOK_LINE*ADDRESS_BOOK_PAGE)	// アドレスブックの数

#define MAIL_HISTORY_CNT		10		// 一人あたりの履历数

#define TECH_NAME_LEN			24		// 技の名称の文字数
#define TECH_MEMO_LEN			96		// 技の说明の文字数
#define MAX_TECH				11		// １技能あたりの学习技の最大数

#define SKILL_NAME_LEN			24		// 技能の名称の文字数
#define SKILL_MEMO_LEN			96		// 技能の说明の文字数
#ifdef PUK2_MAXSKILLSLOT_UP
	#define MAX_SKILL				15		// １职业あたりの学习技能の最大数
#else
#define MAX_SKILL				10		// １职业あたりの学习技能の最大数
#endif

#define MAX_PET_TECH			10		// １ペットあたりの学习技の最大数

#define JOB_NAME_LEN			24		// 职业名の文字数
#define MAX_JOB					2		// 职业の最大数

#define RECIPE_LINE				8		// １ページあたりの表示数
#define RECIPE_PAGE				7		// ページ数
#define RECIPE_MAX				(RECIPE_LINE*RECIPE_PAGE)	// レシピ最大数
#define RECIPE_MEMO_LEN			56		// レシピ说明文字数
#define RECIPE_MATERIAL_MAX		5		// １レシピに必要な素材の最大数

#define MATERIAL_SEL_MAX		6		// 素材选择数

#define MAX_GOLD				1000000	// 所持できる金额

#define RESULT_CHR_INFO			4		// 战闘结果 キャラ情报の数
#define RESULT_ITEM_INFO		3		// 战闘结果 取得アイテム数
#define RESULT_SKILL_INFO		MAX_SKILL	// 战闘结果 スキル情报数

#ifdef PUK2
#ifdef ADD_ALBUM 
	#define ALBUM_MAX_MULTI			320
#else
	#define ALBUM_MAX_MULTI			255		// アルバムの数。PUK2用
#endif
#else
	#define ALBUM_MAX_MULTI			225		// アルバムの数。バージョン２用
#endif

#define ALBUM_MAX_SINGLE		210		// アルバムの数。

#define ALBUM_LIST_LINE			15		// １ページのリスト数
//#define ALBUM_LIST_PAGE			(ALBUM_MAX / ALBUM_LIST_LINE)	// リストのページ数
extern int ALBUM_LIST_PAGE;
extern int ALBUM_MAX;
#ifdef PUK2
#define ALBUM_GUILD_NAME_LEN		32	// 家族名の文字数（半角）
#define ALBUM_GUILD_TITLENAME_LEN	32	// 家族称号の文字数（半角）
#endif

#define GUILD_MAIL_HISTORY_VER	1	// 家族メールヒストリーのバージョン

// メール
#define MAIL_FILE_NAME	"mail.dat"
#ifdef PUK2
#define GUILDMAIL_FILE_NAME	"guildmail.dat"
#endif

enum
{
	MAIN_JOB,						// メイン职业番号
	SUB_JOB							// サブ职业番号
};


enum
{
	PC_ETCFLAG_PARTY		= 0x0001,	// 仲间ＯＫフラグビット
	PC_ETCFLAG_JOINT_BTL	= 0x0002,	// 参战ＯＫフラグビット
	PC_ETCFLAG_DUEL			= 0x0004,	// 对战ＯＫフラグビット
	PC_ETCFLAG_CHAT_MODE	= 0x0008,	// チャットモードフラグビット
	PC_ETCFLAG_MAIL			= 0x0010,	// 名刺交换ＯＫフラグビット
	PC_ETCFLAG_TRADE		= 0x0020,	// 取引ＯＫフラグビット
#ifdef PUK2
	PC_ETCFLAG_GUILD		= 0x0040,	// 家族入会ＯＫフラグビット
#endif
};

// ターゲットの种类
#define TARGET_WHO				0x0001 			// 谁か选择するとき

#define TARGET_IN_DEAD			0x0002 			// 死んでる人含む

#define TARGET_IN_PET			0x0004 			// ペット含む
#define TARGET_IN_ME			0x0008 			// 自分含む
#define TARGET_IN_FRIEND		0x0010 			// 味方（自分とペット以外）含む
#define TARGET_IN_ENEMY			0x0020  		// 敌含

#define TARGET_SORO				0x0040			// 单体
#define TARGET_CIRCUMFERENCE	0x0080 			// 周围
#define TARGET_SIDE				0x0100 			// 片侧
#define TARGET_ALL				0x0200 			// 全体

#define TARGET_CHECK_WEAPON		0x0400 			// ターゲットが武器で影响するか（前卫后卫システムが必要なとき）

#ifdef PUK3_TARGET_FRIEND_PLAYER
	#define TARGET_IN_FRIENDPLAYER	0x0800 			// 自分以外の味方プレイヤー
#endif

enum
{
	PET_SETTING_REST,			// 休み
	PET_SETTING_STADBY,			// 待机
	PET_SETTING_BATTLE,			// 战闘
#ifdef PUK2
	PET_SETTING_FIELD,			// フィールド
#endif
};

#ifdef PUK2
#define PET_SETTING_WALK	0x10	// 连れ歩き（ここはビット判定）
#endif


enum
{
	ITEM_FIELD_FLAG_USEABLE		= (1 <<  0),	// 使用可能
};

enum
{
	ITEM_ETC_FLAG_SEND			= (1 <<  0),	// ペットメール送信可能
	ITEM_ETC_FLAG_COMBINE		= (1 <<  1),	// 合成可能
	ITEM_ETC_FLAG_COOK			= (1 <<  2),	// 料理
	ITEM_ETC_FLAG_DISCRIMINATION = (1 << 3),	// 识别されてる
	//ITEM_ETC_FLAG_VALIDEQUIP	= (1 << 4),		// 装备が有效かどうか（ランクダウンの时のみ）
	ITEM_ETC_FLAG_DROP_ERASE	= (1 <<  5),	// 落とすと消える
	ITEM_ETC_FLAG_LOGOUT_DROP	= (1 <<  6),	// 登出すると落とす
	ITEM_ETC_FLAG_VALIDEQUIP	= (1 << 7),		// そもそも装备できるか（ランクダウンの时のみ）
	ITEM_ETC_FLAG_INCUSE		= (1 << 8),		// 刻印されてるか
	ITEM_ETC_FLAG_HANKO			= (1 << 9),		// ハンコされてるか
	ITEM_ETC_FLAG_MYITEM		= (1 << 10),	// 自分がハンコ、刻印したアイテムか
#ifdef _APPEND_JEWEL
	ITEM_ETC_FLAG_CANADDJEWEL	= (1 << 11),	// 宝石を追加することの出来るアイテムか
#endif /* _APPEND_JEWEL */
	ITEM_ETC_FLAG_MERGED		= (1 << 12),	// プレイヤーが作成したアイテム
};



#define ITEM_FLAG_USEABLE		( 1 <<  0 )		// 0x0001：使用できる
#define ITEM_FLAG_EQUIP			( 1 <<  1 )		// 0x0002：装备できる（战闘中のみ）
#define ITEM_FLAG_ONE_HAND		( 1 <<  2 )		// 0x0004：方手持ち武器
#define ITEM_FLAG_TWO_HAND		( 1 <<  3 )		// 0x0008：两手もち武器

#define ITEM_FLAG_PET_MAIL		( 1 <<  5 ) 	// ペットメールで送信可能アイテム
#define ITEM_FLAG_MIX			( 1 <<  6 ) 	// 根本的に合成できるか
#define ITEM_FLAG_COOKING_MIX	( 1 <<  7 ) 	// 料理合成可能アイテム

#ifdef PUK2
#define MESSAGE_BOARD_LINE		2				// １人の揭示板の使用行数
#define MESSAGE_BOARD_LEN		70				// １行あたりの半角文字数
#else
#define MESSAGE_BOARD_LINE		2				// １人の揭示板の使用行数
#define MESSAGE_BOARD_LEN		54				// １行あたりの半角文字数
#endif

#ifdef _ENABLE_ALBUM_ITEMS
#define ALBUM_MAX_FLAGS	96
#define ALBUM_MAX_BUTTONS	2
#define ALBUM_MAX_TEXT	10
#define ALBUM_MAX_SOUNDS	2
#endif /* _ENALBE_ALBUM_ITEMS */



//-------------------------------------------------------------------------//
// 构造体宣言                                                              //
//-------------------------------------------------------------------------//

// レシピ素材アイテム构造体
typedef struct
{
	int id;							// ID番号(-1でデータ无し)
	int num;						// 数量
	char name[ITEM_NAME_LEN+1];		// アイテム名
} RECIPE_MATERIAL;


// レシピ情报构造体
typedef struct
{
	int id;							// ID番号
	int fp;							// 消费FP
	int rank;						// ランク
	int usableFlag;					// 使用可能フラグ	// ohta
#ifdef _APPEND_JEWEL
	int itemid;						// アイテムID
#endif
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[RECIPE_MEMO_LEN+1];	// 说明
	RECIPE_MATERIAL material[RECIPE_MATERIAL_MAX];	// レシピ素材アイテム
} RECIPE;


// 称号の构造体
typedef struct
{
	int id;							// 履历番号
#ifdef PUK2
	int titleid;					// タイトルのＩＤ
#endif
	char title[CHAR_TITLE_LEN+1];	// 称号
} CHAR_TITLE_INFO;

// アイテム构造体
typedef struct
{
	short useFlag;					// 使用フラグ
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char freeName[ITEM_FREENAME_LEN+1];	// 刻印文字
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];
	int memoPage;					// メモのページ数
	int color;						// 名称の色
	int graNo;						// 画像番号
	int lv;							// アイテムランク
	int id;							// ID番号
	int num;						// 数量
	int kind;						// 种类
	short field;					// フィールドで使用できるか？
	short battle;					// 战闘で使用できるか？
	short target;					// 对象
	short flag;						// ペットメールで送信可能か
	short checkFlag;				// 鉴定フラグ
	int vardata1;					// 泛用データ领域
} ITEM;

// プレイヤーの构造体
typedef struct
{
	short tribe;						// 种族

	int lv;								// 等级(Lv)
	int lp, maxLp;						// 体力／最大体力
	int fp, maxFp;						// 魔力／最大魔力
#ifdef PUK2
	int rebirthLevel;					// リバースの等级（０ならリバース不可）

	int bt, btFlg;						// バースト时间とバースト使用フラグ
	int btLast;							// バースト时间カウント最終チェック时间
#endif

	int exp;							// 经验值(Exp)
	int nextExp;						// 次等级に必要な经验值(Next Exp)

	// 基本能力
	int vtl;							// 体力(Vtl)
	int str;							// 攻击力(Str)
	int tgh;							// 防御力(Tgh)
	int qui;							// 敏捷力(Qui)
	int mgc;							// 魔法力(Mgc)
	int stm;							// スタミナ(Stm)
	int dex;							// 器用さ(Dex)
	int inte;							// 知性(Int)

	// 现在能力值
	int atk;							// 攻击值(Atk)
	int def;							// 防御值(Def)
	int agi;							// 敏捷值(Agi)
	int mnd;							// 精神值(Mnd)
	int rcv;							// 回复值(Rcv)

	int chm;							// 魅力(Chm)

#ifdef VERSION_TW
	//台服客户端新增的魔攻和魔防
	int adm;							// 魔攻(Adm)
	int rss;							// 魔防(Rss)
#endif

	int fme;							// 名声(Fme)

	// 抵抗值
	int poi;							// 毒抵抗值
	int slp;							// 眠り抵抗值
	int stn;							// 石化抵抗值
	int itx;							// 酔い抵抗值
	int cnf;							// 混乱抵抗值
	int amn;							// 丧失抵抗值

	// 修正值
	int cri;							// クリティカル修正值
	int ctr;							// カウンタ修正值
	int hit;							// 命中修正值
	int avd;							// 回避修正值

	// 属性值
	int attr[4];						// 0 ... 地属性
										// 1 ... 水属性
										// 2 ... 火属性
										// 3 ... 风属性

	int injuryLv;						// 怪我等级
	int stunPoint;						// 気絶ポイント

	int gold;							// お金
	int titleId;						// 称号の格纳番号
	int dp;								// デュエルポイント
	char name[CHAR_NAME_LEN+1];			// 名称
	char freeName[CHAR_FREENAME_LEN+1];	// 自己称号
#ifdef PUK2
	char guildName[33];					// 家族名
	char guildTitle[33];				// 家族称号名

	int skillSlot;						// スキルスロット数
#endif

#ifdef PUK3_RIDE		//　ライド移动のテスト
	int	walkSpeed;
	int walkRest;		// ライドの残り步数
#endif


	int graNo;							// グラフィック番号
	int faceGraNo;						// 颜画像番号
	int id;								// 管理用ID
	int dir;							// 向き
	short nameColor;					// 名称の色

	unsigned short status;				// 状态(ビットフラグ)
#ifdef PUK2
	unsigned short status2;				// 状态(ビットフラグ)
	int headNo;							// 表示するスキルアイコンの番号
#endif
	unsigned short etcFlag;				// 仲间OK??战闘途中参加OK??デュエルOK フラグ
	int battleNo;						// 战闘番号
	short sideNo;						// 战闘时にどちら侧か
	short helpMode;						// ヘルプモード

	short battlePositionFlag;			// 战闘时の立ち位置
										//   0 ... 后
										//   1 ... 前

	ITEM item[MAX_ITEM];				// アイテム
	ACTION *ptAct;						// プレイヤーのアクション
} PC;


// 技(TECH)の构造体
typedef struct
{
	char name[TECH_NAME_LEN+1];			// 技名
	char memo[TECH_MEMO_LEN+1];			// 说明
	int fp;								// 必要FP
	int field;							// 使用场所
	int target;							// 使用对象
	char useFlag;						// 使用フラグ
	int usableFlag;						// 使用できるかフラグ（ＬＶ制限）
#ifdef PUK2
	int techlv;							// 技の等级（正确には、技が使用できるようになる等级基准の数值）
#endif
} TECH_INFO;


// 技能(SKILL)の构造体
typedef struct
{
	char name[SKILL_NAME_LEN+1];		// 技能名
	char slot;							// 必要スロット数
	int lv;								// 等级
	int maxLv;							// 最大等级
	int exp;							// 经验值
	int nextExp;						// 必要经验值
	int operationCategory;				// オペレーション分类ー
	int id;								// スキルＩＤ
	int fpRate;							// 魔力使用时にこの值を挂けて实际の使用魔力を算出する
	int hisId;							// 履历番号
#ifdef PUK2_JOB_CHANGE
	int usableLv;						// 使用可能等级
#endif
	TECH_INFO tech[MAX_TECH];			// 技情报
	RECIPE recipe[RECIPE_MAX];			// レシピ情报
#ifdef PUK2
	int	trn;							// 最后に设定された时にトランスしていたかどうか
#endif
} SKILL_INFO;


// スキルソート用构造体
typedef struct
{
	short useFlag;						// 使用フラグ
	short index;						// ソート前の位置
	int id;								// 履历番号
} SKILL_SORT_INFO;


// 职业(JOB)の构造体
typedef struct
{
	char name[JOB_NAME_LEN+1];			// 职业名
	SKILL_INFO skill[MAX_SKILL];		// 技能情报
	SKILL_SORT_INFO sortSkill[MAX_SKILL];	// ソート用情报
	char kind;							// 0 ... 战闘系职业
										// 1 ... 生产系职业
} JOB_INFO;


// ペットの技（本当は技能）のソート用构造体
typedef struct
{
	short useFlag;						// 使用フラグ
	short index;						// ソート前の位置
	int id;								// 履历番号
} PET_TECH_SORT_INFO;


// ペットの技（本当は技能）构造体
typedef struct
{
	short useFlag;						// 使用フラグ
#ifdef PUK3_MONSTER_HELPER
#else
	short techId;						// 技ＩＤ
#endif
	short field;						// 使用できる场所
	short target;						// 技の对象
	char name[TECH_NAME_LEN+1];			// 技の名称
	char memo[TECH_MEMO_LEN+1];			// メモ
#ifdef PUK3_MONSTER_HELPER
	int techId;							// 技ＩＤ(范围が足らなくなったので)
#endif
	int fp;								// 必要FP
	int hisId;							// 履历番号
} PET_TECH_INFO;


typedef struct
{
	short useFlag;
	short index;
	int id;
} PET_SORT;


// ペット情报构造体
typedef struct
{
	short useFlag;						// この栏にペットがいるかフラグ
	short changeNameFlag;				// 名称をつけれるかフラグ
	short tribe;						// 种族

	int lv;								// 等级
	int lp, maxLp;						// 体力／最大体力
	int fp, maxFp;						// 魔力／最大魔力

	int exp, nextExp;					// 经验值／次の等级に必要な经验值
	int bonusPoint;						// 基本能力に振り分けるボーナスポイント

	// 基本能力
	int vit;							// 体力(Vit)
	int str;							// 攻击力(Str)
	int tgh;							// 防御力(Tgh)
	int qui;							// 敏捷力(Qui)
	int mgc;							// 魔法力(Mgc)

	// 现在能力值
	int atk;							// 攻击值(Atk)
	int def;							// 防御值(Def)
	int agi;							// 敏捷值(Agi)
	int mnd;							// 精神值(Mnd)
	int rcv;							// 回复值(Rcv)
	int hmg;							// 忠诚值(Hmd)

	// 抵抗值
	int poi;							// 毒抵抗值
	int slp;							// 眠り抵抗值
	int stn;							// 石化抵抗值
	int itx;							// 酔い抵抗值
	int cnf;							// 混乱抵抗值
	int amn;							// 丧失抵抗值

	// 修正值
	int cri;							// クリティカル修正值
	int ctr;							// カウンタ修正值
	int hit;							// 命中修正值
	int avd;							// 回避修正值

	// 属性值
	int attr[4];						// 0 ... 地属性
										// 1 ... 水属性
										// 2 ... 火属性
										// 3 ... 风属性

	int injuryLv;						// 怪我等级

	PET_TECH_INFO tech[MAX_PET_TECH];	// 技
	PET_TECH_SORT_INFO sortTech[MAX_PET_TECH];	// 技ソート用

	int graNo;							// 画像番号
	int maxTech;						// 最大技数
	unsigned short status;				// 状态(ビットフラグ)
	short battleSetting;				// 战闘への参加状态
	char name[CHAR_NAME_LEN+1];			// 种类名
	char freeName[PET_NAME_LEN+1];		// ユーザがつける名称
	int hisId;							// 履历番号
#ifdef PUK2
	int Walk;
#endif

} PET;


typedef struct
{
	short useFlag;					// 使用フラグ
	int id;							// 管理ＩＤ
	int lv;							// 等级
	int lp;							// 体力
	int maxLp;						// 最大体力
	int fp;							// 魔力
	char name[CHAR_NAME_LEN+1];		// 名称
	ACTION *ptAct;					// アクション
} PARTY;


typedef struct
{
	short no;						// -2     ... PCキャラ
									// 0 以上 ... ペットの位置番号
	short lvUp;						// 等级アップフラグ
	int exp;						// 取得经验值
} BATTLE_RESULT_CHR_INFO;

typedef struct
{
	short lvUp;						// 等级アップフラグ
	int exp;						// 取得经验值
} BATTLE_RESULT_SKILL_INFO;

typedef struct
{
	short useFlag;												// 使用フラグ
	BATTLE_RESULT_CHR_INFO resChrInfo[RESULT_CHR_INFO];			// キャラ情报
	char itemName[RESULT_ITEM_INFO][ITEM_NAME_LEN+1];			// 取得アイテム名
	BATTLE_RESULT_SKILL_INFO resSkillInfo[RESULT_SKILL_INFO];	// スキル情报
	int getDp;						// 取得ＤＰ
	int dp;							// 现在ＤＰ
#ifdef PUK2_NEW_MENU
	int itemgraNo[RESULT_ITEM_INFO];// アイテムグラフィック
#endif
} BATTLE_RESULT_MSG;


typedef struct
{
	char name[CHAR_NAME_LEN+1];		// SJISで名称をいれる
	short lv;						// 等级
	int login;						// ログイン回数

	int faceGraNo;					// 颜画像番号
#ifdef PUK2
	int baseImgNo;					// 基本グラフィック番号
	int guildTitle;					// 家族称号ＩＤ（家族マスターかどうかのチェックに使用）

	int faceVer;					// 颜の现在のバージョン
#endif
	int vit;						// 体力
	int str;						// 攻击力
	int tgh;						// 防御力
	int qui;						// 敏捷力
	int mgc;						// 魔法力
	int attr[4];					// 属性（地??水??火??风）
	int registnumber;               // キャラ登録番号
	int isRenewal;					// リニューアルかどうか。１ならリニューアル
	char JobName[64];				// SJISで职业名を入れる
} CHARLISTTABLE;


typedef struct
{
	short useFlag;					// 使用フラグ
	short onlineFlag;				// ONライン＆サーバ番号
	int id;							// 登録番号
	int lv;							// 等级
	int graNo;						// 颜の画像番号
	char name[CHAR_NAME_LEN+1];			// 名称
	char freeName[CHAR_FREENAME_LEN+1];	// 自己称号
#ifdef PUK2
	char guildName[ALBUM_GUILD_NAME_LEN+1];	// 家族名
	char guildTitle[ALBUM_GUILD_TITLENAME_LEN+1];	// 	家族称号
#endif
} ADDRESS_BOOK_INFO;

typedef struct
{
	int index;
} ADDRESS_BOOK_SORT_TBL;


typedef struct
{
	char readFlag;					// 未読 ... 1 / 既読 ... 0
	char sendFlag;					// 送信 ... 1 / 受信 ... 0
	char header[18];				// yyyy/mm/dd hh:mm
	char buf[300];					// 文章
} MAIL_INFO;

//通常メールヒストリー
typedef struct
{
	MAIL_INFO mailInfo[MAIL_HISTORY_CNT];
} MAIL_HISTORY;



//家族メールヒストリー
typedef struct
{
	MAIL_INFO mailInfo[MAIL_HISTORY_CNT];
} GUILD_MAIL_INFO;

typedef struct
{
	int ID;
	int useFlag;
	GUILD_MAIL_INFO mail;
} GUILD_MAIL_HISTORY;

typedef struct
{
	short useFlag;					// 使用フラグ
	short selFlag;					// 选择状态フラグ
	int graNo;						// 画像番号
	int num;						// 个数
	int id;							// ID
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];// メモ
	int memoPage;					// メモのページ数
	int otherflg;					//色々フラグ
	char freeName[ITEM_FREENAME_LEN+1];	//刻印文字	
} TRADE_ITEM;

typedef struct
{
	short useFlag;					// 使用フラグ
	short selFlag;					// 选择状态フラグ
	short tribe;					// 种族
	int graNo;						// 画像番号
	int lv;							// 等级
	int lp, maxLp;					// 体力／最大体力
	int fp, maxFp;					// 魔力／最大魔力

#ifdef PUK2_NEW_MENU
	// 基本能力
	int vit;						// 体力(Vit)
	int str;						// 攻击力(Str)
	int tgh;						// 防御力(Tgh)
	int qui;						// 敏捷力(Qui)
	int mgc;						// 魔法力(Mgc)

	int hmg;						// 忠诚值(Hmd)
#endif
	int atk;						// 攻击值(Atk)
	int def;						// 防御值(Def)
	int agi;						// 敏捷值(Agi)
	int mnd;						// 精神值(Mnd)
	int rcv;						// 回复值(Rcv)

	// 属性值
	int attr[4];					// 0 ... 地属性
									// 1 ... 水属性
									// 2 ... 火属性
									// 3 ... 风属性

	int maxTech;					// 最大技数

	PET_TECH_INFO tech[MAX_PET_TECH];	// 技

	char name[PET_NAME_LEN+1];		// 名称
#ifdef PUK2_NEW_MENU
	char defname[PET_NAME_LEN+1];	// 种族名
#endif
} TRADE_MONSTER;


// アルバムの状态情报构造体
typedef struct
{
	char useFlag;					// 使用フラグ
	char seal;						// 封印可能フラグ
	char rare;						// レア度
	char tribe;						// 种族
	int no;							// アルバム番号
	int graNo;						// 画像番号
	int vit;						// 体力
	int str;						// 攻击力
	int tgh;						// 防御力
	int qui;						// 敏捷力
	int mgc;						// 魔法力
	int attr[4];					// 属性值
	int maxTech;					// 最大技数
	char name[PET_NAME_LEN+1];		// 名称
	char memo[2][28+1];				// 说明
	char newFlag;					// 新规フラグ
} MONSTER_ALBUM_STATUS;

// アルバム构造体
typedef struct
{
	char fileId[4];
	int version[MAXCHARACTER];
	MONSTER_ALBUM_STATUS status[MAXCHARACTER][ALBUM_MAX_MULTI];
} MONSTER_ALBUM;

// アイテムショップの情报构造体
typedef struct
{
	char useFlag;					// 使用フラグ
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];	// 说明
	int graNo;						// 画像番号
	int price;						// 值段
	int num;						// 卖买个数
	int place;						//サーバーのアイテムの场所
	int stack_num;					// 现在のスタック数
	int stack_max;					// 最大スタック数
	int sellunit;					// 卖却单位
	char buyOkFlag;					// 卖却可能フラグ
	int otherflg;					//色々フラグ
	char freeName[ITEM_FREENAME_LEN+1];	//刻印文字	
} ITEM_SHOP_INFO;


// 交换ＮＰＣの情报构造体
typedef struct
{
	char useFlag;					// 使用フラグ
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];	// 说明
	int graNo;						// 画像番号
	int price;						// 值段
	int num;						// 卖买个数
} TRADE_NPC_INFO;


// アイテムカウントＮＰＣの情报构造体
typedef struct
{
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];	// 说明
	int graNo;			// 画像番号
	int stack;			// 给予物品场合はプレイヤーが持っているアイテムのスタック数
						// 获得物品场合は、受け取るアイテムの最大スタック数
	int itemid;			// 取引するアイテム番号
	int gold;			// 受け取る时に必要なお金/アイテムを渡した时にもらえるお金
	int total;			// 取引するアイテムの数
	int count;			// 取引したアイテムの数
} ITEMCOUNT_NPC_INFO;

#ifdef _ENABLE_ALBUM_ITEMS
// アルバムウインドウボタン情报
typedef struct {
	short type;
	short x;
	short y;
	short fx;
	short fy;
	char string[32];
	int imgno1;
	int imgno2;
	short cmd;
} ALBUMITEM_BUTTONS_INFO;

// アルバムウインドウ画像情报
typedef struct {
	short x;
	short y;
	int img;
	char flg;
} ALBUMITEM_IMAGE_INFO;

// アルバムウインドウ情报
typedef struct {
	short x;
	short y;
	short fx;
	short fy;
	int img;
} ALBUMITEM_WINDOW_INFO;

typedef struct {
	short x;
	short y;
	short color;
	char string[128];
} ALBUMITEM_TEXT_INFO;

// アルバムウインドウ情报构造体
typedef struct
{
	ALBUMITEM_WINDOW_INFO framewindow;
	ALBUMITEM_IMAGE_INFO albumimg[ALBUM_MAX_FLAGS];
	ALBUMITEM_BUTTONS_INFO buttons[ALBUM_MAX_BUTTONS];
	ALBUMITEM_TEXT_INFO text[ALBUM_MAX_TEXT];
	int sounds[ALBUM_MAX_SOUNDS];
	int page;
} ALBUMITEM_MENU_INFO;
#endif /* _ENABLE_ALBUM_ITEMS */


// 医者ＮＰＣウィンドウ情报构造体
typedef struct
{
	int lv;							// 等级
	int injuryLv;					// 怪我等级
	int cost;						// 治疗费
	char name[CHAR_NAME_LEN+1];		// 名称
} DOCTOR_NPC_WIN_INFO;


// 鉴定、修理ＮＰＣの情报构造体
typedef struct
{
	char useFlag;					// 使用フラグ
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];	// 说明
	int graNo;						// 画像番号
	int price;						// 值段
	int num;						// 鉴定するかフラグ
	int place;						//サーバーのアイテムの场所
	int otherflg;					//色々フラグ
	char freeName[ITEM_FREENAME_LEN+1];	//刻印文字
} APPRAISAL_NPC_INFO, REPAIR_NPC_INFO;


// 揭示板情报构造体
typedef struct
{
	int graNo;						// 颜画像番号
	unsigned int time;				// 书き込み日时
	char delFlag;					// 削除ボタンフラグ
	char name[CHAR_NAME_LEN+1];		// 名称
	char message[MESSAGE_BOARD_LINE*MESSAGE_BOARD_LEN+8];	// 书き込み内容
} MESSAGE_BOARD_INFO;


typedef struct
{
	short useFlag;					// 使用フラグ
	short selFlag;					// 选择状态フラグ
	int graNo;						// 画像番号
	int num;						// 个数
	char name[ITEM_NAME_LEN+1];		// アイテム名
	char memo[ITEM_MEMO_LINE*ITEM_MEMO_PAGE][ITEM_MEMO_LINE_LEN+64];// メモ
	char freeName[ITEM_FREENAME_LEN+1];	//刻印文字
	int memoPage;					// メモのページ数
	int flag;						// 刻印かハンコフラグ
} BANK_ITEM;

typedef struct
{
	char name[TECH_NAME_LEN+1];			// 技の名称
} BANK_MONSTER_TECH_INFO;

typedef struct
{
	short useFlag;					// 使用フラグ
	short selFlag;					// 选择状态フラグ
	short tribe;					// 种族
	int graNo;						// 画像番号
	int lv;							// 等级
	int lp, maxLp;					// 体力／最大体力
	int fp, maxFp;					// 魔力／最大魔力

	int atk;						// 攻击值(Atk)
	int def;						// 防御值(Def)
	int agi;						// 敏捷值(Agi)
	int mnd;						// 精神值(Mnd)
	int rcv;						// 回复值(Rcv)

	// 属性值
	int attr[4];					// 0 ... 地属性
									// 1 ... 水属性
									// 2 ... 火属性
									// 3 ... 风属性

	int maxTech;					// 最大技数

	BANK_MONSTER_TECH_INFO tech[MAX_PET_TECH];	// 技

	char name[PET_NAME_LEN+1];		// 名称
} BANK_MONSTER;


typedef struct
{
	BANK_ITEM item[MAX_DRAW_WIN_ITEM];
	BANK_MONSTER monster[MAX_PET];
	int gold;
} BANK_NPC;


// モンスタースキルショップ（贩卖构造体）
typedef struct
{
	short useFlag;
	int fp;
	int price;
	char name[TECH_NAME_LEN+1];		// 名称
	char memo[2][29];				// 说明
} MONSTER_SKILL_SHOP_SELL;


// モンスタースキルショップ（使い魔选择构造体）
typedef struct
{
	short useFlag;
	int lv;
	int maxTech;
	char name[PET_NAME_LEN+1];
	char canSetFlg;
#ifdef PUK3_PETSKILLSHOPEX
	int tribe;
	char rideflg;
#endif
} MONSTER_SKILL_SHOP_SELECT_PET;


// モンスタースキルショップ（记忆位置构造体）
typedef struct
{
	short useFlag;
	int fp;
	char name[TECH_NAME_LEN+1];
	char memo[2][29];
} MONSTER_SKILL_SHOP_SELECT_POS;


// アイテム使用时のターゲット选择ウィンドウ用构造体
typedef struct
{
	short type;
	char name[CHAR_NAME_LEN+1];
} USE_ITEM_TARGET_SEL;


// アイテム使用时のターゲット选择ウィンドウ２用构造体
typedef struct
{
	char name[CHAR_NAME_LEN+1];
	int lv;
	int injuryLv;
	int lp;
	int maxLp;
	int fp;
	int maxFp;
	int type;
	int sendNo;
} USE_ITEM_TARGET_SEL2;


//-------------------------------------------------------------------------//
// 外部参照变数定义                                                        //
//-------------------------------------------------------------------------//
extern PC pc;
extern JOB_INFO job;
extern CHAR_TITLE_INFO charTitleInfo[];
extern CHAR_TITLE_INFO sortCharTitleInfo[];
extern short maxPcNo;
extern short selectPcNo;
extern short prSendMode;
extern short prSendFlag;
extern short jbSendFlag;
extern short duelSendFlag;

extern int loginDp;

extern short helpFlag;


extern PET pet[];
extern PET_SORT sortPet[];
extern int petCnt;

extern PARTY party[];
extern short partyModeFlag;

extern ADDRESS_BOOK_INFO addressBook[];
extern ADDRESS_BOOK_SORT_TBL addressBookSortTbl[];

extern MAIL_HISTORY mailHistory[][ADDRESS_BOOK];

extern BATTLE_RESULT_MSG battleResultMsg;

extern CHARLISTTABLE chartable[];

extern char newCharacterName[];
extern int newCharacterGraNo;
extern int newCharacterFaceGraNo;
extern int newCharacterVit;
extern int newCharacterStr;
extern int newCharacterTgh;
extern int newCharacterQui;
extern int newCharacterMgc;
extern int newCharacterEarth;
extern int newCharacterWater;
extern int newCharacterFire;
extern int newCharacterWind;
extern int newCharacterHomeTown;

extern MONSTER_ALBUM album;

#ifdef _DEBUG
extern int sortOffFlag;
#endif
#ifdef PUK2
extern char skillrebirth;
#endif
//-------------------------------------------------------------------------//
// プロト种类宣言                                                        //
//-------------------------------------------------------------------------//
void petSort( PET *, PET_SORT *, int );
int petSortFunc( const void *, const void * );

void petTechSort( PET_TECH_INFO *, PET_TECH_SORT_INFO *, int );
int petTechSortFunc( const void *, const void * );

void skillSort( SKILL_INFO *, SKILL_SORT_INFO *, int cnt );
int skillSortFunc( const void *, const void * );

void setAlbum( int, int, int, int, int, int, int, int, int, int,
		int, int, int, int, int, char *, char * );
void checkAlbumBit( int , char *data );
void delAlbum( void );
void initAlbum( void );
void initAlbumStartUp( void );
void readAlbumFile( void );
void writeAlbumFile( void );

int getItemEmpty( void );

void initMail( void );
void writeMailFile( void );
void readMailFile( void );
void delMailHistory( void );
void delGuildMailHistory( void );
void delMailHistoryOnce( int );
void delGuildMailHistoryOnce( int );
void setMailHistory( int, int, char *, char * );
int checkNoReadMail( void );
int checkNoReadMailOnce( int );
#ifdef PUK2
void writeGuildMailFile( void );
void setGuildMailHistory( int, int, char *, char * );
int checkNoReadGuildMailOnce( int );
int getGuildNumFromID(int);
int getGuildMailHistoryNumFromID(int);
#endif

#ifdef PUK3_PROF
int checkNoReadMiniMailOnce( int index );
#endif

int getRecipeMaxPage( int );

int getHelthColor( int );

void setItemMemo( int, char * );

int getNoCharTitle( int );
int getMaxPageCharTitle( void );
void charTitleCopy( CHAR_TITLE_INFO *, CHAR_TITLE_INFO *, int );
void charTitleSort( CHAR_TITLE_INFO *, int );
int charTitleSortFunc( const void *, const void * );

int calcPetCnt( void );

void clearRecipe( int );

void initPcAll( void );
void initPc( void );
void createPc( int, int, int, int );
void resetPc( void );
void setPcMovePoint( int, int );
void setPcWarpPoint( int, int );
void setPcPoint( void );
void setPcGraNo( int, int );
void setPcId( int );
void setPcDir( int );
void setPcAction( int );
int getPcAction( void );
void setPcParam( char *, char *, int, int, int, int );
void updataPcAct( void );
void setPcLeader( void );
void delPcLeader( void );
void setPcParty( void );
void delPcParty( void );
void setPcWatch( void );
void delPcWatch( void );
#ifdef PUK3_RIDE		// ライドの移动テスト
void changePcAct( int, int, int, int, int, int );
#else
void changePcAct( int, int, int, int, int );
#endif
void setPcWalkFlag( void );
void delPcWalkFlag( void );
int checkPcWalkFlag( void );
void setPcUseMagic( void );
void delPcUseMagic( void );
#ifdef PUK2
void setPcFukidashi( unsigned int, unsigned char *, int, int, int );
#else
void setPcFukidashi( unsigned int );
#endif
void setPcDuel( void );
void delPcDuel( void );

#ifdef PUK2
	// PCキャラに怪我情报を出す
	void setPcHealth( void );
	// PCキャラに怪我情报を消す
	void delPcHealth( void );
	// PCキャラをスキル使用中にする
	void setPcSkill( int headNo );
	// PCキャラをスキル使用中から元に戾す
	void delPcSkill( void );
	// PCキャラに等级アップマークを表示
	void setPcLevelup( void );

	// PCキャラペットの怪我情报を出す
	void setPcPetHealth( void );
	// PCキャラペットの怪我情报を消す
	void delPcPetHealth( void );
	// PCキャラペットに等级アップマークを表示
	void setPcPetLevelup( void );
#endif
#ifdef PUK3_VEHICLE
	// PCキャラを不可视にする
	void setPcInvisible( void );
	// PCキャラを不可视から元に戾す
	void delPcInvisible( void );
#endif

void clearPartyParam( void );
void clearPtActPartyParam( void );

int existCharacterListEntry( int );
int cmpNameCharacterList( char * );
int setCharacterList( char *, char * );
int resetCharacterList( int );

void getItem( void );
void swapItem( int, int );

BOOL lookAtAround( void );

#ifdef _CG2_NEWGRAPHIC
int getNewGraphicNo(int graNo);
int getNewFaceGraphicNo(int graNo);

int getOtherFaceGraphicType( int newCharacterFaceGraNo );

int getReFaceGraphic(int graNo);
int getOldFaceGraphic(int graNo);

#ifdef PUK2
	int getFaceGraphicFormPUK2(int graNo);
	int getFaceGraphicToPUK2(int graNo);
	int checkNoReadGuildMailOnce( int index );
	void GetNoReadMail( int *MailType,int *MailNo );
#endif
#ifdef PUK3_RIDE
	// ライドキャラの絵取得关数
	int getRiderCharaGra( int graNo );
#endif

//他人のキャラグラ情报
enum{
	CG_OTHER_1,		//１のキャラ
	CG_OTHER_EX,	//ＥＸ用新キャラ
	CG_OTHER_2,		//２の新キャラ
	CG_OTHER_RE,	//リニューアルキャラ
#ifdef PUK2
	CG_OTHER_PUK2_1,	//PUK2の１キャラ
	CG_OTHER_PUK2_2,	//PUK2の２キャラ
	CG_OTHER_PUK2_M,	//PUK2のモンスター
	#ifdef PUK3_UPGRADE
		CG_OTHER_PUK3_M,	//PUK2のモンスター
	#endif
#endif
	CG_OTHER_NUM
};

#ifdef PUK2
enum{
	HEADICON_DELETE,	// アイコンを消す
	HEADICON_LEADER,	// リーダーマーク
	HEADICON_BATTLE,	// 战闘
	HEADICON_DUEL,		// デュエル
	HEADICON_WATCH,		// 観战
	HEADICON_FELL,		// 伐采
	HEADICON_PICK,		// 采掘
	HEADICON_HUNT,		// 狩猟
	HEADICON_FISH,		// 釣り
	HEADICON_MAKE,		// 制造
	HEADICON_COOK,		// 调理
	HEADICON_JUDGE,		// 鉴定
	HEADICON_REPAIR,	// 修理
	HEADICON_CURE,		// 治疗
	HEADICON_LVUP,		// 等级アップ
	HEADICON_INJURY,	// 怪我
};
// icon のアイコンを头に表示する
void SetHeadIcon( int icon );
// 现在出てる头の上のアイコン取得
int GetHeadIcon();
#endif

extern BYTE CG2PackageVer;


#endif


#endif
