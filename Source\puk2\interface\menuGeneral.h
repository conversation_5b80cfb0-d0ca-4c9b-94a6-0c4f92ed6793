﻿// サーバリクエスト(共通ウィンドウ)

#ifndef _MENUGENERAL_H_
#define _MENUGENERAL_H_

//====================================//
//			泛用ウィンドウ			  //
//====================================//

// ボタン处理关数 *********************//

BOOL MenuWindowGraphicButton( int no, unsigned int flag );
BOOL MenuWindowStringButton( int no, unsigned int flag );

GRAPHIC_SWITCH MenuWindowGeneralGraph[]={
	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},		// ＯＫ
	{GID_BigCancelButtonOn,0,0,0,0,0xFFFFFFFF},	// Ｃａｎｃｅｌ
	{GID_BigYesButtonOn,0,0,0,0,0xFFFFFFFF},	// Ｙｅｓ
	{GID_BigNoButtonOn,0,0,0,0,0xFFFFFFFF},		// Ｎｏ
	{GID_BigBackButtonOn,0,0,0,0,0xFFFFFFFF},	// Ｂａｃｋ
	{GID_BigNextButtonOn,0,0,0,0,0xFFFFFFFF},	// Ｎｅｘｔ
};

ACTION_SWITCH_INIT MenuWindowGeneralAction[]={
	{0},
};
// スイッチ设定
static SWITCH_DATA GeneralSwitch[] = {
//type         , ofx, ofy, sx, sy, SprNum, graNo, view , text   ,  func
{ SWITCH_GRAPHIC,   0,   0,  66, 17, TRUE, &MenuWindowGeneralGraph[0], MenuWindowGraphicButton },
{ SWITCH_GRAPHIC,   0,  20,  66, 17, TRUE, &MenuWindowGeneralGraph[1], MenuWindowGraphicButton },
{ SWITCH_GRAPHIC,   0,  20,  66, 17, TRUE, &MenuWindowGeneralGraph[2], MenuWindowGraphicButton },
{ SWITCH_GRAPHIC,  60,   0,  66, 17, TRUE, &MenuWindowGeneralGraph[3], MenuWindowGraphicButton },
{ SWITCH_GRAPHIC,  60,  20,  66, 17, TRUE, &MenuWindowGeneralGraph[4], MenuWindowGraphicButton },
{ SWITCH_GRAPHIC, 120,   0,  66, 17, TRUE, &MenuWindowGeneralGraph[5], MenuWindowGraphicButton },

{ SWITCH_NONE	  , 120,   0,  28,  8, TRUE, NULL,					   MenuWindowStringButton },

{ SWITCH_DIALOG,   80,  42,   0,  0, TRUE, NULL, MenuSwitchNone },									// 一行入力栏

{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション０
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション１
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション２
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション３
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション４
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション５
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション６
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション７
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション８
{ SWITCH_ACTION,    0,  0,  0,  0, TRUE, &MenuWindowGeneralAction[0], MenuSwitchNone },				// アクション９
};

// スイッチ管理用enum
enum{
	EnumGeneralGraHead = 0,

	EnumGeneral__OK = EnumGeneralGraHead,
	EnumGeneralCanc,
	EnumGeneral_Yes,
	EnumGeneral__No,
	EnumGeneralBack,
	EnumGeneralNext,

	EnumGeneralGraTail,

	EnumGeneralStrButton = EnumGeneralGraTail,

	EnumGeneralInput,

	EnumGeneralActionHead,
	EnumGeneralAction0 = EnumGeneralActionHead,
	EnumGeneralAction1,
	EnumGeneralAction2,
	EnumGeneralAction3,
	EnumGeneralAction4,
	EnumGeneralAction5,
	EnumGeneralAction6,
	EnumGeneralAction7,
	EnumGeneralAction8,
	EnumGeneralAction9,
	EnumGeneralActionTail,

	EnumGeneralEnd,
};

#define GRL_ACT_NUM (EnumGeneralActionTail-EnumGeneralActionHead)

// ウィンドウ处理关数 ++++
BOOL MenuWindowGeneralBf( int mouse );
BOOL MenuWindowGeneralAf( int mouse );
BOOL MenuWindowGeneralCl();

// ウインドウ设定
const WINDOW_DATA WindowDataGeneral = {
//flag
 MENU_ATTR_NOMOVE,										
     3, 0, 0,  0, 30, 0x80010101, EnumGeneralEnd,  GeneralSwitch, MenuWindowGeneralBf,MenuWindowGeneralAf,MenuWindowGeneralCl
};

// アクションの初期化
void initGrl_Action();
// アクションの追加
void addGrl_Action( char useFlag, int actNo, char actAng, short x, short y );
// アクションの设定变更
/***	flagのフラグ*/
enum{
	ADDGRLFLAG_ACTNO = 1 << 0,
	ADDGRLFLAG_ACTANG = 1 << 1,
	ADDGRLFLAG_X = 1 << 2,
	ADDGRLFLAG_Y = 1 << 3,
	ADDGRLFLAG_USE = 1 << 4,
};
/***/
void chgGrl_Action( int index, char flag, char useFlag, int actNo, char actAng, short x, short y );

// 泛用ウィンドウ一列中の最大文字数取得关数
int GeneralWindowStrLen( short winw );
// 泛用ウィンドウの最大行数取得关数
int GeneralWindowStrLineNum( short winh, int BottonType, int Interval );
// 通常ウィンドウ版
ACTION *openMenuGeneralWindow( short x, short y, short w, short h, int GraNo, int BottonType, char (*Msg)[81],
	int LineNum, int Interval, int SelCnt, char InputStr, short *InputStrPos, int InputStrLen, char Move, unsigned char Option,
	BOOL (*ReturnFunc)( int Button, int num, char **str, char linenum ),
	BOOL (*ExtraFunc)( WINDOW_INFO *wininfo, int ButtonOver, int StrOver, int flag ),
	unsigned char flg, char opentype );
// サーバーリクエスト版ウィンドウ版
ACTION *openMenuSrGeneralWindow( short x, short y, short w, short h, int GraNo, int BottonType, char (*Msg)[81],
	int LineNum, int Interval, int SelCnt, char InputStr, short *InputStrPos, int InputStrLen, char Move, unsigned char Option,
	BOOL (*ReturnFunc)( int Button, int num, char **str, char linenum ),
	BOOL (*ExtraFunc)( WINDOW_INFO *wininfo, int ButtonOver, int StrOver, int flag ),
	unsigned char flg, char opentype );
// オプションの值
enum{
	GRL_OPT_NONE = 0,
	GRL_OPT_EXCLUSIVE = 1 << 0,
	GRL_OPT_INPUTBACK = 1 << 1,
#ifdef PUK3_CURE_FP_LUCK
	GRL_OPT_NOMOVE = 1 << 2,
#endif
};

enum{
	GRL_PUSH_OK		= 1 << 0,
	GRL_PUSH_Cancel	= 1 << 1,
	GRL_PUSH_Yes	= 1 << 2,
	GRL_PUSH_No		= 1 << 3,
	GRL_PUSH_Back	= 1 << 4,
	GRL_PUSH_Next	= 1 << 5,
};

//====================================//
//====================================//

// ウィンドウ描画关数 ++++
#define COMMONCHIP_W 74
#define COMMONCHIP_H 54
void MenuWindowCommonDraw( int GraNo, short x, short y, short w, short h, int DispPrio, unsigned long frame_rgba, unsigned long back_rgba,
	char sizeX = COMMONCHIP_W, char sizeY = COMMONCHIP_H );


#endif