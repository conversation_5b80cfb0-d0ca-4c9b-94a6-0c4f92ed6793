﻿//メニュー＞モンスター＞状态、ディティール、スキル

void MenuWindowMonsterStatusInit(void);
void MenuWindowMonsterDetailInit(void);
void MenuWindowMonsterSkillInit(void);
void DrawSkillSortPanel(void);
void InitMonsterListWork(void);
BOOL CheckMonsterListWork(void);
void InitSendPetNameWork(void);

void SetSendPetNameWorkChangeFlag(void);
void ResetSendPetNameWorkChangeFlag(void);
BOOL CheckSendPetNameWorkChangeFlag(void);


INPUT_STR MenuMonsterNameInputStr;

INIT_STR_STRUCT InitStrStructMonsterName={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuMonsterNameInputStr, 66,31,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   1,      16,  0,     0
};

char MonsterName<PERSON>endWork[256];

//モンスター并び替えワーク
int SkillSortFlag;
int SkillSortFrom;
int SkillSortTo;

int PetNameWorkChangeFlag=0;

int localpethis[MAX_PET];

//--------------------------------------------------------
//ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowMonsterStatus( int mouse )
{


	if(mouse==WIN_INIT){
		MonsStatusNoBack=MonsStatusNo;
		InitMonsterListWork();
		InitSendPetNameWork();
		
		SkillSortFlag=0;
		SkillSortFrom=-1;
		SkillSortTo=-1;
	}else{
		if(MonsStatusNo!=MonsStatusNoBack){
			InitSendPetNameWork();
		}
		MonsStatusNoBack=MonsStatusNo;

		if(CheckMonsterListWork() || CheckSendPetNameWorkChangeFlag()){
			InitMonsterListWork();
			InitSendPetNameWork();
			ResetSendPetNameWorkChangeFlag();
			//初期值としてタイトル设定
			strcpy(InitStrStructMonsterName.str,MonsterNameSendWork);
			//ダイアログ初期化
			SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);
		}

		if(!pet[sortPet[MonsStatusNo].index].useFlag){
			wI->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}

		MenuWindowMonsterStatusInit();
	}

	return TRUE;
}

BOOL MenuWindowMonsterStatusDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}
BOOL MenuWindowMonsterDetail( int mouse )
{
	
	if(mouse==WIN_INIT){
		InitMonsterListWork();
		InitSendPetNameWork();
		// クリック音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}else{
		if(MonsStatusNo!=MonsStatusNoBack){
			InitSendPetNameWork();
		}
		MonsStatusNoBack=MonsStatusNo;

		if(CheckMonsterListWork() || CheckSendPetNameWorkChangeFlag()){
			InitMonsterListWork();
			InitSendPetNameWork();
			ResetSendPetNameWorkChangeFlag();
			//初期值としてタイトル设定
			strcpy(InitStrStructMonsterName.str,MonsterNameSendWork);
			//ダイアログ初期化
			SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);
		}

		if(!pet[sortPet[MonsStatusNo].index].useFlag){
			wI->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}

		MenuWindowMonsterDetailInit();
	}

	return TRUE;
}

BOOL MenuWindowMonsterDetailDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}
BOOL MenuWindowMonsterSkill( int mouse )
{
	
	if(mouse==WIN_INIT){
		InitMonsterListWork();
		InitSendPetNameWork();
		// クリック音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
	}else{
		if(MonsStatusNo!=MonsStatusNoBack){
			InitSendPetNameWork();
		}
		MonsStatusNoBack=MonsStatusNo;

		if(CheckMonsterListWork() || CheckSendPetNameWorkChangeFlag()){
			InitMonsterListWork();
			InitSendPetNameWork();
			ResetSendPetNameWorkChangeFlag();
			//初期值としてタイトル设定
			strcpy(InitStrStructMonsterName.str,MonsterNameSendWork);
			//ダイアログ初期化
			SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);
		}

		if(!pet[sortPet[MonsStatusNo].index].useFlag){
			wI->flag |= WIN_INFO_DEL;
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}

		MenuWindowMonsterSkillInit();
	}


	return TRUE;
}

BOOL MenuWindowMonsterSkillDraw( int mouse ){

	DrawSkillSortPanel();

	displayMenuWindow();

	return TRUE;

}
#ifdef PUK3_WINDOW_OPEN_POINT
BOOL MenuWindowMonsterStatusClose()
{
	if ( !WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wx = WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->wx;
	WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wy = WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->wy;
	WindowFlag[MENU_WINDOW_MONSTER_SKILL].wx = WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->wx;
	WindowFlag[MENU_WINDOW_MONSTER_SKILL].wy = WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->wy;

	return TRUE;
}
BOOL MenuWindowMonsterDetailClose()
{
	if ( !WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_MONSTER_STATUS].wx = WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->wx;
	WindowFlag[MENU_WINDOW_MONSTER_STATUS].wy = WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->wy;
	WindowFlag[MENU_WINDOW_MONSTER_SKILL].wx = WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->wx;
	WindowFlag[MENU_WINDOW_MONSTER_SKILL].wy = WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->wy;

	return TRUE;
}
BOOL MenuWindowMonsterSkillClose()
{
	if ( !WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo ) return TRUE;

	WindowFlag[MENU_WINDOW_MONSTER_STATUS].wx = WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->wx;
	WindowFlag[MENU_WINDOW_MONSTER_STATUS].wy = WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->wy;
	WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wx = WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->wx;
	WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wy = WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->wy;

	return TRUE;
}
#endif

//--------------------------------------------------------
//スイッチ处理
//--------------------------------------------------------
//モンスター状态ウインドウのボタン
BOOL MenuSwitchMonsterStatus( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	char FreeName[256];
	char SendFreeName[256];
	int i;

	switch(no){
		// Ｎａｍｅセット
		case EnumGraphMonsterStatusNameSet:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_SET );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOn;
			}
			if( flag & MENU_MOUSE_LEFT ){

				strcpy(FreeName,MenuMonsterNameInputStr.buffer);
				makeSendString(FreeName,SendFreeName,sizeof( SendFreeName ) );

				// 变更后の名称をサーバに送信
				nrproto_KN_send( sockfd, sortPet[MonsStatusNo].index, SendFreeName );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				//ダイアログをチャットに变更
				SetDialogMenuChat();

				if(FreeName[0]=='\0'){
					strcpy(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name);
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->text,pet[sortPet[MonsStatusNo].index].name);
				}

				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOff;
			}
			break;
		
		// アクション回転
		case EnumBtMenuStatusSwitchActionAng:
			if( flag & MENU_MOUSE_LEFTAUTO ){
				MonsStatusAng++;
				if(MonsStatusAng>7)
					MonsStatusAng=0;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_RIGHTAUTO ){
				MonsStatusAng--;
				if(MonsStatusAng<0)
					MonsStatusAng=7;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}
			break;
		
		//モンスターリリース
		case EnumGraphMonsterStatusRelease:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_RELEASE );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(sortPet[MonsStatusNo].useFlag){
#ifdef PUK3_VEHICLE
					// 乘り物移动中でないなら
					// もしくは见えないときでないなら
					if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
						 !nowVehicleProc() ){
						// ペットを置く
						nrproto_DP_send( sockfd, mapGx, mapGy, sortPet[MonsStatusNo].index );
						// 决定音c（文字等クリック时）
						play_se( SE_NO_OK3, 320, 240 );
	#ifdef PUK3_MONSTER_HELPER_CANCEL
						if( pet[ sortPet[MonsStatusNo].index ].battleSetting == PET_SETTING_BATTLE ){
							CancelRideSkillCreate();
						}
	#endif
					}
#else
					// ペットを置く
					nrproto_DP_send( sockfd, mapGx, mapGy, sortPet[MonsStatusNo].index );
					// 决定音c（文字等クリック时）
					play_se( SE_NO_OK3, 320, 240 );
	#ifdef PUK3_MONSTER_HELPER_CANCEL
					if( pet[ sortPet[MonsStatusNo].index ].battleSetting == PET_SETTING_BATTLE ){
						CancelRideSkillCreate();
					}
	#endif
#endif
				}
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOff;
			}
			break;

		//?
		case EnumGraphMonsterStatusNoUp:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_RIGHT );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				for(i=0;i<6;i++){
					MonsStatusNo++;
					if(MonsStatusNo>=5)
						MonsStatusNo=0;
					if(pet[sortPet[MonsStatusNo].index].useFlag)
						break;
				}

				//Name入れなおし
				if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0')
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
				else
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);

				MenuWindowMonsterStatus(WIN_INIT);

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOff;
			}
			break;

		//?
		case EnumGraphMonsterStatusNoDown:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_LEFT );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				for(i=0;i<6;i++){
					MonsStatusNo--;
					if(MonsStatusNo<0)
						MonsStatusNo=4;
					if(pet[sortPet[MonsStatusNo].index].useFlag)
						break;
				}

				//Name入れなおし
				if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0')
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
				else
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);

				MenuWindowMonsterStatus(WIN_INIT);

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOff;
			}
			break;
	}

	return ReturnFlag;
}

//モンスターディティールウインドウのボタン
BOOL MenuSwitchMonsterDetail( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	char FreeName[256];
	char SendFreeName[256];
	int i;

	switch(no){
		// Ｎａｍｅセット
		case EnumGraphMonsterDetailNameSet:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_SET );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOn;
			}
			if( flag & MENU_MOUSE_LEFT ){

				strcpy(FreeName,MenuMonsterNameInputStr.buffer);
				makeSendString(FreeName,SendFreeName,sizeof( SendFreeName ) );

				// 变更后の名称をサーバに送信
				nrproto_KN_send( sockfd, sortPet[MonsStatusNo].index, SendFreeName );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				//ダイアログをチャットに变更
				SetDialogMenuChat();

				if(FreeName[0]=='\0'){
					strcpy(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name);
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->text,pet[sortPet[MonsStatusNo].index].name);
				}
				
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOff;
			}
			break;
		
		// リリース
		case EnumGraphMonsterDetailRelease:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_RELEASE );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOn;

			}
			if( flag & MENU_MOUSE_LEFT ){
				if(sortPet[MonsStatusNo].useFlag){
#ifdef PUK3_VEHICLE
					// 乘り物移动中でないなら
					// もしくは见えないときでないなら
					if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
						 !nowVehicleProc() ){
						// ペットを置く
						nrproto_DP_send( sockfd, mapGx, mapGy, sortPet[MonsStatusNo].index );
						// 决定音c（文字等クリック时）
						play_se( SE_NO_OK3, 320, 240 );
	#ifdef PUK3_MONSTER_HELPER_CANCEL
						if( pet[ sortPet[MonsStatusNo].index ].battleSetting == PET_SETTING_BATTLE ){
							CancelRideSkillCreate();
						}
	#endif
					}
#else
					// ペットを置く
					nrproto_DP_send( sockfd, mapGx, mapGy, sortPet[MonsStatusNo].index );
					// 决定音c（文字等クリック时）
					play_se( SE_NO_OK3, 320, 240 );
	#ifdef PUK3_MONSTER_HELPER_CANCEL
					if( pet[ sortPet[MonsStatusNo].index ].battleSetting == PET_SETTING_BATTLE ){
						CancelRideSkillCreate();
					}
	#endif
#endif
				}
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOff;
			}
			break;

		// ?
		case EnumGraphMonsterDetailNoUp:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_RIGHT );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				for(i=0;i<6;i++){
					MonsStatusNo++;
					if(MonsStatusNo>=5)
						MonsStatusNo=0;
					if(pet[sortPet[MonsStatusNo].index].useFlag)
						break;
				}

				//Name入れなおし
				if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0')
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
				else
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);

				MenuWindowMonsterDetail(WIN_INIT);

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOff;
			}
			break;

		// ?
		case EnumGraphMonsterDetailNoDown:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_LEFT );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				for(i=0;i<6;i++){
					MonsStatusNo--;
					if(MonsStatusNo<0)
						MonsStatusNo=4;
					if(pet[sortPet[MonsStatusNo].index].useFlag)
						break;
				}

				//Name入れなおし
				if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0')
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
				else
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);

				MenuWindowMonsterDetail(WIN_INIT);

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOff;
			}
			break;

		// VitUp
		case EnumGraphMonsterDetailVitUp:	
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_PLUS_ON );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				// 振り分けた场所をサーバに知らせる
				nrproto_PLVUP_send( sockfd, sortPet[MonsStatusNo].index, 0 );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				ReturnFlag=TRUE;	
			}
			break;
		// StrUp
		case EnumGraphMonsterDetailStrUp:	
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_PLUS_ON );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				// 振り分けた场所をサーバに知らせる
				nrproto_PLVUP_send( sockfd, sortPet[MonsStatusNo].index, 1 );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				ReturnFlag=TRUE;	
			}
			break;
		// TghUp
		case EnumGraphMonsterDetailTghUp:	
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_PLUS_ON );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				// 振り分けた场所をサーバに知らせる
				nrproto_PLVUP_send( sockfd, sortPet[MonsStatusNo].index, 2 );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				ReturnFlag=TRUE;	
			}
			break;
		// QuiUp
		case EnumGraphMonsterDetailQuiUp:	
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_PLUS_ON );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				// 振り分けた场所をサーバに知らせる
				nrproto_PLVUP_send( sockfd, sortPet[MonsStatusNo].index, 3 );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				ReturnFlag=TRUE;	
			}
			break;
		// MgcUp
		case EnumGraphMonsterDetailMgcUp:	
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_PLUS_ON );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DetailStatusUpOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				// 振り分けた场所をサーバに知らせる
				nrproto_PLVUP_send( sockfd, sortPet[MonsStatusNo].index, 4 );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				ReturnFlag=TRUE;	
			}
			break;

	}

	return ReturnFlag;
}

//モンスタースキルウインドウのボタン
BOOL MenuSwitchMonsterSkill( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	char FreeName[256];
	char SendFreeName[256];
	int i;

	switch(no){
		// Ｎａｍｅセット
		case EnumGraphMonsterSkillNameSet:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_SET );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOn;
			}
			if( flag & MENU_MOUSE_LEFT ){

				strcpy(FreeName,MenuMonsterNameInputStr.buffer);
				makeSendString(FreeName,SendFreeName,sizeof( SendFreeName ) );

				// 变更后の名称をサーバに送信
				nrproto_KN_send( sockfd, sortPet[MonsStatusNo].index, SendFreeName );
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
				//ダイアログをチャットに变更
				SetDialogMenuChat();

				if(FreeName[0]=='\0'){
					strcpy(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name);
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->text,pet[sortPet[MonsStatusNo].index].name);
				}
				
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterNameSetOff;
			}
			break;
		
		// リリース
		case EnumGraphMonsterSkillRelease:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_RELEASE );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				if(sortPet[MonsStatusNo].useFlag){
#ifdef PUK3_VEHICLE
					// 乘り物移动中でないなら
					// もしくは见えないときでないなら
					if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
						 !nowVehicleProc() ){
						// ペットを置く
						nrproto_DP_send( sockfd, mapGx, mapGy, sortPet[MonsStatusNo].index );
						// 决定音c（文字等クリック时）
						play_se( SE_NO_OK3, 320, 240 );
	#ifdef PUK3_MONSTER_HELPER_CANCEL
						if( pet[ sortPet[MonsStatusNo].index ].battleSetting == PET_SETTING_BATTLE ){
							CancelRideSkillCreate();
						}
	#endif
					}
#else
					// ペットを置く
					nrproto_DP_send( sockfd, mapGx, mapGy, sortPet[MonsStatusNo].index );
					// 决定音c（文字等クリック时）
					play_se( SE_NO_OK3, 320, 240 );
	#ifdef PUK3_MONSTER_HELPER_CANCEL
					if( pet[ sortPet[MonsStatusNo].index ].battleSetting == PET_SETTING_BATTLE ){
						CancelRideSkillCreate();
					}
	#endif
#endif
				}
				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusReleaseOff;
			}
			break;

		// ?
		case EnumGraphMonsterSkillNoUp:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_RIGHT );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOn;
			}
 			if( flag & MENU_MOUSE_LEFT || MonsStatusNo>=5){
				for(i=0;i<6;i++){
					MonsStatusNo++;
					if(MonsStatusNo>=5)
						MonsStatusNo=0;
					if(pet[sortPet[MonsStatusNo].index].useFlag)
						break;
				}

				//Name入れなおし
				if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0')
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
				else
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);

				MenuWindowMonsterSkill(WIN_INIT);

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoUpOff;
			}


			break;
		// ←
		case EnumGraphMonsterSkillNoDown:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_LEFT );
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				for(i=0;i<6;i++){
					MonsStatusNo--;
					if(MonsStatusNo<0)
						MonsStatusNo=4;
					if(pet[sortPet[MonsStatusNo].index].useFlag)
						break;
				}
				//Name入れなおし
				if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0')
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].name);
				else
					strcpy(InitStrStructMonsterName.str,pet[sortPet[MonsStatusNo].index].freeName);
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);

				MenuWindowMonsterSkill(WIN_INIT);

				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );

				ReturnFlag=TRUE;	
			}
			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusNoDownOff;
			}
			break;

		case EnumGraphMenuMonsterSkillScrollUp:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOff;
			}
			break;

		case EnumGraphMenuMonsterSkillScrollDown:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOn;
			}
			if( flag & MENU_MOUSE_LEFTAUTO ){
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOff;
			}
			break;

		}

	return ReturnFlag;
}

//怪物名字入力ダイアログ
BOOL MenuMonsterStatusNameDialog( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	DIALOG_SWITCH *Dialog;

	if( flag & MENU_MOUSE_LEFT ){
		//初期值としてタイトル设定
		strcpy(InitStrStructMonsterName.str,MonsterNameSendWork);

		//ダイアログ初期化
		SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,0);
		//フォーカスを取る
		GetKeyInputFocus( &MenuMonsterNameInputStr );

		DiarogST.SwAdd=wI->sw[no].Switch;
		Dialog=(DIALOG_SWITCH *)wI->sw[no].Switch;
		Dialog->InpuStrAdd=&MenuMonsterNameInputStr;
		
		ReturnFlag=TRUE;
	}


	return ReturnFlag;

}

//各ウインドウへのボタン处理
BOOL MenuSwitchMonsterChangeWindow( int no, unsigned int flag ){
	
	BOOL ReturnFlag=FALSE;	

	switch(no){
		case EnumGraphMonsterStatusStatusBt:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_STATUS );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterStatusOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_MONSTER_STATUS );
				WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_MONSTER_STATUS].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;

		case EnumGraphMonsterStatusDetailBt:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterDetailOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_DETAIL );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterDetailOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_MONSTER_DETAIL );
				WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_MONSTER_DETAIL].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;

		case EnumGraphMonsterStatusSkillBt:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterSkillOver;
				strcpy( OneLineInfoStr, MWONELINE_PETSTATUS_SKILL );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_MonsterSkillOn;
			}
			if( flag & MENU_MOUSE_LEFT ){
				wI->flag |= WIN_INFO_DEL;
				createMenuWindow( MENU_WINDOW_MONSTER_SKILL );
				WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->wx=wI->wx;
				WindowFlag[MENU_WINDOW_MONSTER_SKILL].wininfo->wy=wI->wy;
				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				ReturnFlag=TRUE;
			}
			break;
	}

	return ReturnFlag;
}

#ifdef PUK2_NEWDRAG

void DragMonsSkillFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// 右クリックしたらアイテムドロップ
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_MONSTERSKILL, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragMonsSkillFunc );
		}

#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		// 别のところで表示
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		SkillSortFlag=0;
		SkillSortFrom=-1;
		SkillSortTo=-1;
		break;
	}
}

void DragMonsSkill( int itemNo )
{
	//移动元选择
	SkillSortFrom = itemNo;
	SkillSortFlag = 1;
	// ドラッグ开始
	WinDD_DragStart( WINDD_MONSTERSKILL, (void *)SkillSortFrom, DragMonsSkillFunc );
}

#endif

BOOL MenuSwitchMonsterSkillSwitch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	char Str[256];
	int i;
	int PetNumberWork;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_MONSTERSKILL ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
		}
	}
	if(no==EnumGraphMonsterSkillPanel00){
		wI->sw[EnumTextMonsterSkillMemo00].Enabled=FALSE;
		wI->sw[EnumTextMonsterSkillMemo01].Enabled=FALSE;
		wI->sw[EnumTextMonsterSkillMemo02].Enabled=FALSE;
		wI->sw[EnumTextMonsterSkillMemo03].Enabled=FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		wI->sw[EnumTextMonsterSkillMemo00].Enabled=TRUE;
		wI->sw[EnumTextMonsterSkillMemo01].Enabled=TRUE;
		wI->sw[EnumTextMonsterSkillMemo02].Enabled=TRUE;
		wI->sw[EnumTextMonsterSkillMemo03].Enabled=TRUE;
		PetNumberWork=no-EnumGraphMonsterSkillPanel00;
		for( i = 0; i < 4; i++ ){
			if( getMemoLine( Str, sizeof( Str ),pet[sortPet[MonsStatusNo].index].tech[pet[sortPet[MonsStatusNo].index].sortTech[PetNumberWork].index].memo, i, 26 ) ){
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillMemo00+i].Switch)->text,Str);
			}
		}
		ReturnFlag=TRUE;
	}

#ifdef PUK2_NEWDRAG
#else
	if ( no == EnumGraphMonsterSkillPanel00 ){
		SkillSortFlag=0;
		SkillSortFrom = -1;
	}
#endif
	//スキルソートの处理
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		SkillSortFrom = (int)WinDD_ObjData();
#else
		SkillSortFrom = (int)WinDD_GetObject();
#endif
		//移动先选择
		SkillSortTo=no-EnumGraphMonsterSkillPanel00;
		nrproto_PSSW_send(sockfd,
				sortPet[MonsStatusNo].index,
				pet[sortPet[MonsStatusNo].index].sortTech[SkillSortFrom].index,
				pet[sortPet[MonsStatusNo].index].sortTech[SkillSortTo].index);
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#else
		SkillSortFlag=0;
		SkillSortFrom=-1;
		SkillSortTo=-1;
#endif
	}

#ifdef PUK2_NEWDRAG
	if( (flag&MENU_MOUSE_RIGHT)&&(flag&MENU_MOUSE_OVER) ){
		if ( wI->sw[EnumTextMonsterSkillName00+no-EnumGraphMonsterSkillPanel00].Enabled ){
			// ドラッグ开始
			DragMonsSkill( no-EnumGraphMonsterSkillPanel00 );
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
#else
	if( (flag&MENU_MOUSE_RIGHT)&&(flag&MENU_MOUSE_OVER) ){
		if ( wI->sw[EnumTextMonsterSkillName00+no-EnumGraphMonsterSkillPanel00].Enabled ){
			//移动元选择
			SkillSortFrom=no-EnumGraphMonsterSkillPanel00;
			SkillSortFlag=1;
			// ドラッグ开始
			WinDD_DragStart( WINDD_MONSTERSKILL, (void *)SkillSortFrom );
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}else if ( WinDD_CheckObjType()==WINDD_MONSTERSKILL ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_MONSTER_SKILL && WinDD_ButtonNo()==no ){
			SkillSortFrom = (int)WinDD_ObjData();
			SkillSortFlag=1;
			if ( (mouse.onceState&MOUSE_RIGHT_CRICK) || (flag&MENU_MOUSE_RIGHT) ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_MONSTERSKILL, (void *)(SkillSortFrom), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
		}
	}
#endif


	return ReturnFlag;

}

void MenuWindowMonsterStatusInit(void){

	char StrWork[256];
	int i,j;
	GRAPHIC_SWITCH *Graph;

	if(pet[sortPet[MonsStatusNo].index].useFlag ){
		//ペットいたら
		wI->sw[EnumTextMonsterStatusName].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusExp].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusLp].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusFp].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusType].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusHMC].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusSlot].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusPage].Enabled=TRUE;
		wI->sw[EnumActionMonsterStatusChar].Enabled=TRUE;
		wI->sw[EnumGraphMonsterStatusHealth].Enabled=TRUE;
		wI->sw[EnumTextMonsterStatusLevel].Enabled=TRUE;

		//名称
		if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0'){
			if(	pNowInputStr != &MenuMonsterNameInputStr){
				//ダイアログ表示でない时はテキストのほうを表示
				wI->sw[EnumTextMonsterStatusName].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->text,MonsterNameSendWork);
				if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name)==0){
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->color=FONT_PAL_WHITE;
				}else{
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->color=FONT_PAL_RED;
				}
			}else{
				//ダイアログ表示
				wI->sw[EnumTextMonsterStatusName].Enabled=FALSE;
				strcpy(MonsterNameSendWork,InitStrStructMonsterName.inputStr->buffer);
				//ダイアログの位置变更
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,1);
			}

			if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name)==0){
				wI->sw[EnumGraphMonsterStatusNameSet].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphMonsterStatusNameSet].Enabled=TRUE;
			}
		}else{
			if(	pNowInputStr != &MenuMonsterNameInputStr){
				//ダイアログ表示でない时はテキストのほうを表示
				wI->sw[EnumTextMonsterStatusName].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->text,MonsterNameSendWork);
				if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName)==0){
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->color=FONT_PAL_WHITE;
				}else{
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusName].Switch)->color=FONT_PAL_RED;
				}
			}else{
				//ダイアログ表示
				wI->sw[EnumTextMonsterStatusName].Enabled=FALSE;
				strcpy(MonsterNameSendWork,InitStrStructMonsterName.inputStr->buffer);
				//ダイアログの位置变更
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,1);
			}

			if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName)==0){
				wI->sw[EnumGraphMonsterStatusNameSet].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphMonsterStatusNameSet].Enabled=TRUE;
			}
		}
	
		//EXP
		if(pet[sortPet[MonsStatusNo].index].nextExp==-1){
			sprintf( StrWork, "%8d/       -",pet[sortPet[MonsStatusNo].index].exp );										
		}else{
			sprintf( StrWork, "%8d/%8d",pet[sortPet[MonsStatusNo].index].exp, pet[sortPet[MonsStatusNo].index].nextExp );										
		}
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusExp].Switch)->text,StrWork);
		//LP
		sprintf( StrWork, "%5d/%5d",pet[sortPet[MonsStatusNo].index].lp, pet[sortPet[MonsStatusNo].index].maxLp );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusLp].Switch)->text,StrWork);
		//FP
		sprintf( StrWork, "%5d/%5d", pet[sortPet[MonsStatusNo].index].fp,pet[sortPet[MonsStatusNo].index].maxFp );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusFp].Switch)->text,StrWork);
		//TYPE
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusType].Switch)->text,characterTribeStr[pet[sortPet[MonsStatusNo].index].tribe]);
		//HMC
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].hmg );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusHMC].Switch)->text,StrWork);
		//SLOT
		sprintf( StrWork, "%2d", pet[sortPet[MonsStatusNo].index].maxTech );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusSlot].Switch)->text,StrWork);
		//Level
		sprintf( StrWork, "%3d",pet[sortPet[MonsStatusNo].index].lv );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusLevel].Switch)->text,StrWork);

		//アクションキャラクター
		((ACTION_SWITCH *)wI->sw[EnumActionMonsterStatusChar].Switch)->ActionAdd->anim_chr_no=pet[sortPet[MonsStatusNo].index].graNo;
		((ACTION_SWITCH *)wI->sw[EnumActionMonsterStatusChar].Switch)->ActionAdd->anim_ang=MonsStatusAng;

		//健康状态（ヘルス）
		if(pet[sortPet[MonsStatusNo].index].injuryLv == 0){
			//健康状态
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusHealth].Switch)->graNo=GID_StatusHealth0;
		}else if( pet[sortPet[MonsStatusNo].index].injuryLv <= 25 ){
			//かすりキズ
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusHealth].Switch)->graNo=GID_StatusHealth1;
		}else if( pet[sortPet[MonsStatusNo].index].injuryLv <= 50 ){
			//軽伤
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusHealth].Switch)->graNo=GID_StatusHealth2;
		}else if( pet[sortPet[MonsStatusNo].index].injuryLv <= 75 ){
			//重症
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusHealth].Switch)->graNo=GID_StatusHealth3;
		}else{
			//濒死
			((GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusHealth].Switch)->graNo=GID_StatusHealth4;
		}


		//属性表示
		//地
		for (i=0;i<10;i++)
			wI->sw[EnumGraphMonsterStatusEarth00+i].Enabled=FALSE;
		j=pet[sortPet[MonsStatusNo].index].attr[0]/10;
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusEarth00].Switch;
			Graph->graNo=GID_MonsterStatusEarthOnly;
			wI->sw[EnumGraphMonsterStatusEarth00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusEarth00+i].Switch;
				Graph->graNo=GID_MonsterStatusEarthCenter;
				wI->sw[EnumGraphMonsterStatusEarth00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusEarth00+i].Switch;
					Graph->graNo=GID_MonsterStatusEarthLeft;
					wI->sw[EnumGraphMonsterStatusEarth00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusEarth00+i].Switch;
					Graph->graNo=GID_MonsterStatusEarthRight;
					wI->sw[EnumGraphMonsterStatusEarth00+i].Enabled=TRUE;
				}
			}
		}

		//水
		for (i=0;i<10;i++)
			wI->sw[EnumGraphMonsterStatusWater00+i].Enabled=FALSE;
		j=pet[sortPet[MonsStatusNo].index].attr[1]/10;
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWater00].Switch;
			Graph->graNo=GID_MonsterStatusWaterOnly;
			wI->sw[EnumGraphMonsterStatusWater00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWater00+i].Switch;
				Graph->graNo=GID_MonsterStatusWaterCenter;
				wI->sw[EnumGraphMonsterStatusWater00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWater00+i].Switch;
					Graph->graNo=GID_MonsterStatusWaterLeft;
					wI->sw[EnumGraphMonsterStatusWater00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWater00+i].Switch;
					Graph->graNo=GID_MonsterStatusWaterRight;
					wI->sw[EnumGraphMonsterStatusWater00+i].Enabled=TRUE;
				}
			}
		}

		//火
		for (i=0;i<10;i++)
			wI->sw[EnumGraphMonsterStatusFire00+i].Enabled=FALSE;
		j=pet[sortPet[MonsStatusNo].index].attr[2]/10;
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusFire00].Switch;
			Graph->graNo=GID_MonsterStatusFireOnly;
			wI->sw[EnumGraphMonsterStatusFire00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusFire00+i].Switch;
				Graph->graNo=GID_MonsterStatusFireCenter;
				wI->sw[EnumGraphMonsterStatusFire00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusFire00+i].Switch;
					Graph->graNo=GID_MonsterStatusFireLeft;
					wI->sw[EnumGraphMonsterStatusFire00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusFire00+i].Switch;
					Graph->graNo=GID_MonsterStatusFireRight;
					wI->sw[EnumGraphMonsterStatusFire00+i].Enabled=TRUE;
				}
			}
		}

		//风
		for (i=0;i<10;i++)
			wI->sw[EnumGraphMonsterStatusWind00+i].Enabled=FALSE;
		j=pet[sortPet[MonsStatusNo].index].attr[3]/10;
		if(j==1){	//属性が１０のときは专用画像
			Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWind00].Switch;
			Graph->graNo=GID_MonsterStatusWindOnly;
			wI->sw[EnumGraphMonsterStatusWind00].Enabled=TRUE;
		}else{
			for(i=0;i<j;i++){
				Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWind00+i].Switch;
				Graph->graNo=GID_MonsterStatusWindCenter;
				wI->sw[EnumGraphMonsterStatusWind00+i].Enabled=TRUE;
				if(i==0){	//头画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWind00+i].Switch;
					Graph->graNo=GID_MonsterStatusWindLeft;
					wI->sw[EnumGraphMonsterStatusWind00+i].Enabled=TRUE;
				}
				if(i==j-1){	//后ろ画像
					Graph=(GRAPHIC_SWITCH *)wI->sw[EnumGraphMonsterStatusWind00+i].Switch;
					Graph->graNo=GID_MonsterStatusWindRight;
					wI->sw[EnumGraphMonsterStatusWind00+i].Enabled=TRUE;
				}
			}
		}
	}else{
		//ペットいなかったら
		wI->sw[EnumTextMonsterStatusName].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusExp].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusLp].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusFp].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusType].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusHMC].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusSlot].Enabled=FALSE;
		wI->sw[EnumActionMonsterStatusChar].Enabled=FALSE;
		wI->sw[EnumTextMonsterStatusLevel].Enabled=FALSE;

		for (i=0;i<10;i++){
			wI->sw[EnumGraphMonsterStatusEarth00+i].Enabled=FALSE;
			wI->sw[EnumGraphMonsterStatusWater00+i].Enabled=FALSE;
			wI->sw[EnumGraphMonsterStatusFire00+i].Enabled=FALSE;
			wI->sw[EnumGraphMonsterStatusWind00+i].Enabled=FALSE;
		}

		wI->sw[EnumGraphMonsterStatusHealth].Enabled=FALSE;
	}

	sprintf( StrWork, "%d/%d", MonsStatusNo+1,MAX_PET );										
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterStatusPage].Switch)->text,StrWork);

}

void MenuWindowMonsterDetailInit(void){

	char StrWork[256];
	int i;
	int maxPoint;
	int point[6];
	int index;

	if(pet[sortPet[MonsStatusNo].index].useFlag ){
		//ペットいたら
		wI->sw[EnumTextMonsterDetailName].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailLevel].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailLp].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailFp].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailBonus].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailVit].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailStr].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailTgh].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailQui].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailMgc].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailHmc].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailSlot].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailCri].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailHit].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailCtr].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailAvd].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailPoi].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailItx].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailSlp].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailCnf].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailStn].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailAmn].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailAtk].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailMnd].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailDef].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailRcv].Enabled=TRUE;
		wI->sw[EnumTextMonsterDetailAgl].Enabled=TRUE;


		//名称
		if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0'){
			if(	pNowInputStr != &MenuMonsterNameInputStr){
				//ダイアログ表示でない时はテキストのほうを表示
				wI->sw[EnumTextMonsterDetailName].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->text,MonsterNameSendWork);
				if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name)==0){
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->color=FONT_PAL_WHITE;
				}else{
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->color=FONT_PAL_RED;
				}
			}else{
				//ダイアログ表示
				wI->sw[EnumTextMonsterDetailName].Enabled=FALSE;
				strcpy(MonsterNameSendWork,InitStrStructMonsterName.inputStr->buffer);
				//ダイアログの位置变更
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,1);
			}
			
			if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name)==0){
				wI->sw[EnumGraphMonsterDetailNameSet].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphMonsterDetailNameSet].Enabled=TRUE;
			}
		}else{
			if(	pNowInputStr != &MenuMonsterNameInputStr){
				//ダイアログ表示でない时はテキストのほうを表示
				wI->sw[EnumTextMonsterDetailName].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->text,MonsterNameSendWork);
				if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName)==0){
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->color=FONT_PAL_WHITE;
				}else{
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailName].Switch)->color=FONT_PAL_RED;
				}
			}else{
				//ダイアログ表示
				wI->sw[EnumTextMonsterDetailName].Enabled=FALSE;
				strcpy(MonsterNameSendWork,InitStrStructMonsterName.inputStr->buffer);
				//ダイアログの位置变更
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,1);
			}

			if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName)==0){
				wI->sw[EnumGraphMonsterDetailNameSet].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphMonsterDetailNameSet].Enabled=TRUE;
			}
		}

		//Level
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].lv );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailLevel].Switch)->text,StrWork);
		//Lp
		sprintf( StrWork, "%4d/%4d", pet[sortPet[MonsStatusNo].index].lp, pet[sortPet[MonsStatusNo].index].maxLp );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailLp].Switch)->text,StrWork);
		//Fp
		sprintf( StrWork, "%4d/%4d", pet[sortPet[MonsStatusNo].index].fp, pet[sortPet[MonsStatusNo].index].maxFp );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailFp].Switch)->text,StrWork);

		//Bonus
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].bonusPoint );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailBonus].Switch)->text,StrWork);
		//Vit
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].vit );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailVit].Switch)->text,StrWork);
		//Str
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].str );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailStr].Switch)->text,StrWork);
		//Tgh
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].tgh );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailTgh].Switch)->text,StrWork);
		//Qui
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].qui );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailQui].Switch)->text,StrWork);
		//Mgc
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].mgc );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailMgc].Switch)->text,StrWork);

		//Hmc
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].hmg );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailHmc].Switch)->text,StrWork);
		//Slot
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].maxTech );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailSlot].Switch)->text,StrWork);

		//Cri
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].cri );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailCri].Switch)->text,StrWork);
		//Hit
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].hit );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailHit].Switch)->text,StrWork);
		//Ctr
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].ctr );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailCtr].Switch)->text,StrWork);
		//Avd
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].avd );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailAvd].Switch)->text,StrWork);

		//Poi
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].poi );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailPoi].Switch)->text,StrWork);
		//Itx
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].itx );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailItx].Switch)->text,StrWork);
		//Slp
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].slp );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailSlp].Switch)->text,StrWork);
		//Cnf
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].cnf );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailCnf].Switch)->text,StrWork);
		//Stn
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].stn );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailStn].Switch)->text,StrWork);
		//Amn
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].amn );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailAmn].Switch)->text,StrWork);

		//Atk
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].atk );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailAtk].Switch)->text,StrWork);
		//Mnd
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].mnd );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailMnd].Switch)->text,StrWork);
		//Def
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].def );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailDef].Switch)->text,StrWork);
		//Rcv
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].rcv );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailRcv].Switch)->text,StrWork);
		//Agl
		sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].agi );										
		strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailAgl].Switch)->text,StrWork);


		wI->sw[EnumGraphMonsterDetailVitUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailStrUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailTghUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailQuiUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailMgcUp].Enabled=FALSE;

		//状态アップボタン
		if(pet[sortPet[MonsStatusNo].index].bonusPoint>0){
			index = sortPet[MonsStatusNo].index;
			point[0] = pet[index].vit;
			point[1] = pet[index].str;
			point[2] = pet[index].tgh;
			point[3] = pet[index].qui;
			point[4] = pet[index].mgc;
			point[5] = pet[index].bonusPoint;
			maxPoint = 0;
			for( i = 0; i < 6; i++ ){
				maxPoint += point[i];
			}
			maxPoint /= 2;

			if(point[0] < maxPoint)
				wI->sw[EnumGraphMonsterDetailVitUp].Enabled=TRUE;
			if(point[1] < maxPoint)
				wI->sw[EnumGraphMonsterDetailStrUp].Enabled=TRUE;
			if(point[2] < maxPoint)
				wI->sw[EnumGraphMonsterDetailTghUp].Enabled=TRUE;
			if(point[3] < maxPoint)
				wI->sw[EnumGraphMonsterDetailQuiUp].Enabled=TRUE;
			if(point[4] < maxPoint)
				wI->sw[EnumGraphMonsterDetailMgcUp].Enabled=TRUE;
		}

	}else{
		//ペットいなかったら
		wI->sw[EnumTextMonsterDetailName].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailLevel].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailLp].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailFp].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailBonus].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailVit].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailStr].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailTgh].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailQui].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailMgc].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailHmc].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailSlot].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailCri].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailHit].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailCtr].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailAvd].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailPoi].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailItx].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailSlp].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailCnf].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailStn].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailAmn].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailAtk].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailMnd].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailDef].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailRcv].Enabled=FALSE;
		wI->sw[EnumTextMonsterDetailAgl].Enabled=FALSE;

		wI->sw[EnumGraphMonsterDetailVitUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailStrUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailTghUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailQuiUp].Enabled=FALSE;
		wI->sw[EnumGraphMonsterDetailMgcUp].Enabled=FALSE;
	}

	sprintf( StrWork, "%d/%d", MonsStatusNo+1,MAX_PET );										
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterDetailPage].Switch)->text,StrWork);

}

void MenuWindowMonsterSkillInit(void){

	char StrWork[256];
	int i;

	if(pet[sortPet[MonsStatusNo].index].useFlag ){
		//ペットいたら

		//名称
		if(pet[sortPet[MonsStatusNo].index].freeName[0]== '\0'){
			if(	pNowInputStr != &MenuMonsterNameInputStr){
				//ダイアログ表示でない时はテキストのほうを表示
				wI->sw[EnumTextMonsterSkillName].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->text,MonsterNameSendWork);
				if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name)==0){
					wI->sw[EnumGraphMonsterSkillNameSet].Enabled=FALSE;
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->color=FONT_PAL_WHITE;
				}else{
					wI->sw[EnumGraphMonsterSkillNameSet].Enabled=TRUE;
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->color=FONT_PAL_RED;
				}
			}else{
				//ダイアログ表示
				wI->sw[EnumTextMonsterSkillName].Enabled=FALSE;
				strcpy(MonsterNameSendWork,InitStrStructMonsterName.inputStr->buffer);
				//ダイアログの位置变更
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,1);
			}

			if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name)==0){
				wI->sw[EnumGraphMonsterSkillNameSet].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphMonsterSkillNameSet].Enabled=TRUE;
			}
		}else{
			if(	pNowInputStr != &MenuMonsterNameInputStr){
				//ダイアログ表示でない时はテキストのほうを表示
				wI->sw[EnumTextMonsterSkillName].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->text,MonsterNameSendWork);
				if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName)==0){
					wI->sw[EnumGraphMonsterSkillNameSet].Enabled=FALSE;
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->color=FONT_PAL_WHITE;
				}else{
					wI->sw[EnumGraphMonsterSkillNameSet].Enabled=TRUE;
					((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName].Switch)->color=FONT_PAL_RED;
				}
			}else{
				//ダイアログ表示
				wI->sw[EnumTextMonsterSkillName].Enabled=FALSE;
				strcpy(MonsterNameSendWork,InitStrStructMonsterName.inputStr->buffer);
				//ダイアログの位置变更
				SetInputStr(&InitStrStructMonsterName,wI->wx,wI->wy,1);
			}

			if(strcmp(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName)==0){
				wI->sw[EnumGraphMonsterSkillNameSet].Enabled=FALSE;
			}else{
				wI->sw[EnumGraphMonsterSkillNameSet].Enabled=TRUE;
			}
		}

		//スキルパネル
		//全部ＯＦＦ
		for(i=0;i<10;i++){
			wI->sw[EnumGraphMonsterSkillPanel00+i].Enabled=FALSE;
			wI->sw[EnumTextMonsterSkillName00+i].Enabled=FALSE;
			wI->sw[EnumTextMonsterSkillFp00+i].Enabled=FALSE;
		}

		//スロット分ＯＮ
		for(i=0;i<pet[sortPet[MonsStatusNo].index].maxTech;i++)
			wI->sw[EnumGraphMonsterSkillPanel00+i].Enabled=TRUE;

		for(i=0;i<10;i++){
			if(pet[sortPet[MonsStatusNo].index].tech[i].useFlag){			
				//スキル名称
				wI->sw[EnumTextMonsterSkillName00+i].Enabled=TRUE;
				strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillName00+i].Switch)->text,pet[sortPet[MonsStatusNo].index].tech[i].name);

				//使用魔力
				wI->sw[EnumTextMonsterSkillFp00+i].Enabled=TRUE;
				sprintf( StrWork, "%3d", pet[sortPet[MonsStatusNo].index].tech[i].fp );										

				strcpy(MonsterSkillNum[i],StrWork);
			}
		}
	}else{
		//ペットいなかったら
		//全部ＯＦＦ
		wI->sw[EnumTextMonsterSkillName].Enabled=FALSE;
		for(i=0;i<10;i++){
			wI->sw[EnumGraphMonsterSkillPanel00+i].Enabled=FALSE;
			wI->sw[EnumTextMonsterSkillName00+i].Enabled=FALSE;
			wI->sw[EnumTextMonsterSkillFp00+i].Enabled=FALSE;
		}
	}

	sprintf( StrWork, "%d/%d", MonsStatusNo+1,MAX_PET );										
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMonsterSkillPage].Switch)->text,StrWork);

	//このタイミングで说明のテキストを初期化しています。
	wI->sw[EnumTextMonsterSkillMemo00].Enabled=FALSE;
	wI->sw[EnumTextMonsterSkillMemo01].Enabled=FALSE;
	wI->sw[EnumTextMonsterSkillMemo02].Enabled=FALSE;
	wI->sw[EnumTextMonsterSkillMemo03].Enabled=FALSE;

}

void DrawSkillSortPanel(void){

	if (SkillSortFlag){
		short x = mouse.nowPoint.x - 105, y = mouse.nowPoint.y - 8;
		struct BLT_MEMBER bm = {0};
		char str[10];

		bm.bltf = BLTF_NOCHG;
		bm.rgba.rgba = 0x80ffffff;

		//フォント用バッファの区切り
		FontBufCut(FONT_PRIO_DRAG);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		StockFontBuffer( x+5, y+2, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, pet[sortPet[MonsStatusNo].index].tech[SkillSortFrom].name, 0, 0 );

		// FP
		sprintf( str, "%3d", pet[sortPet[MonsStatusNo].index].tech[SkillSortFrom].fp );
		StockFontBuffer( x+187, y+2, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str, 0, 0 );

		StockDispBuffer( x+105, y+8, DISP_PRIO_DRAG, GID_MonsterSkillPanelROn, 0, &bm );
	}

}

void SetSendPetNameWorkChangeFlag(void){

	PetNameWorkChangeFlag=1;

}

void ResetSendPetNameWorkChangeFlag(void){

	PetNameWorkChangeFlag=0;

}

BOOL CheckSendPetNameWorkChangeFlag(void){

	if(PetNameWorkChangeFlag==1){
		return TRUE;
	}else{
		return FALSE;
	}
}

//
void InitSendPetNameWork(void){

	int No;

	switch(wI->type){
	case MENU_WINDOW_MONSTER_STATUS:	No = EnumTextMonsterStatusName;	break;		// モンスター状态ウインドウ
	case MENU_WINDOW_MONSTER_DETAIL:	No = EnumTextMonsterDetailName;	break;		// モンスターディティールウインドウ
	case MENU_WINDOW_MONSTER_SKILL:		No = EnumTextMonsterSkillName;	break;		// モンスタースキルウインドウ
	}
	if ( ((TEXT_SWITCH *)wI->sw[No].Switch)->text ){
		if(pet[sortPet[MonsStatusNo].index].freeName[0]!='\0'){
			strcpy(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].freeName);
			strcpy(((TEXT_SWITCH *)wI->sw[No].Switch)->text,pet[sortPet[MonsStatusNo].index].freeName);
		}else{
			strcpy(MonsterNameSendWork,pet[sortPet[MonsStatusNo].index].name);
			strcpy(((TEXT_SWITCH *)wI->sw[No].Switch)->text,pet[sortPet[MonsStatusNo].index].name);
		}
	}

}

//现在のモンスターのリスト作成
void InitMonsterListWork(void){

	int i;

	for(i=0;i<MAX_PET;i++){
		localpethis[i]=pet[sortPet[i].index].hisId;
	}

}

//モンスターのリストに变更があったかチェック
BOOL CheckMonsterListWork(void ){

	int i;

	for(i=0;i<MAX_PET;i++){
		if(localpethis[i]!=pet[sortPet[i].index].hisId){
			return TRUE;
		}
	}

	return FALSE;
}



