﻿/************************/
/*	battle.h			*/
/************************/
#ifndef _BATTLE_MASTERH_
#define _BATTLE_MASTERH_

// スキルＩＤ *********************************************
typedef enum{

	B_SKILL_DOUBLE_ATTACK,		// 连続攻击
	B_SKILL_EDGE,				// エッジ
	B_SKILL_CHARGE,				// チャージアタック
	B_SKILL_BURST,				// バーストアタック
	B_SKILL_KIKOH,				// 气功弹
	B_SKILL_GUARDBRAKE,			// 防御ブレイク
	B_SKILL_FORCECUT,			// フォースカット
	B_SKILL_GUARD,				// 护卫
	B_SKILL_SPECIALGUARD,		// スペシャル防御
	B_SKILL_MIKAWASHI,			// みかわし
	B_SKILL_MAGICGUARD,			// マジック防御
	B_SKILL_CROSSCOUNTER,		// クロスカウンター
	B_SKILL_CONSENTRATION,		// 精神统一
	
	B_SKILL_RESIST_POISON,		// 毒状态异常耐性
	B_SKILL_RESIST_SLEEP,		// 眠り状态异常耐性
	B_SKILL_RESIST_STONE,		// 石化状态异常耐性
	B_SKILL_RESIST_INEBRIETY,	// 酔い状态异常耐性
	B_SKILL_RESIST_CONFUSION,	// 混乱状态异常耐性
	B_SKILL_RESIST_FORGET,		// 忘却状态异常耐性
	
	B_SKILL_MAGICATTACK_SORO_EARTH,	// 地属性单体攻击魔法
	B_SKILL_MAGICATTACK_SORO_WATER,	// 水属性单体攻击魔法
	B_SKILL_MAGICATTACK_SORO_FIRE,	// 火属性单体攻击魔法
	B_SKILL_MAGICATTACK_SORO_WIND,	// 风属性单体攻击魔法
	
	B_SKILL_MAGICATTACK_AREA_EARTH,	// 地属性周辺攻击魔法
	B_SKILL_MAGICATTACK_AREA_WATER,	// 水属性周辺攻击魔法
	B_SKILL_MAGICATTACK_AREA_FIRE,	// 火属性周辺攻击魔法
	B_SKILL_MAGICATTACK_AREA_WIND,	// 风属性周辺攻击魔法
	
	B_SKILL_MAGICATTACK_SIDE_EARTH,	// 地属性片侧攻击魔法
	B_SKILL_MAGICATTACK_SIDE_WATER,	// 水属性片侧攻击魔法
	B_SKILL_MAGICATTACK_SIDE_FIRE,	// 火属性片侧攻击魔法
	B_SKILL_MAGICATTACK_SIDE_WIND,	// 风属性片侧攻击魔法
	
	B_SKILL_DRAIN,					// ドレイン
	
	B_SKILL_ABNORMAL_SORO_POISON,		// 毒状态异常单体魔法
	B_SKILL_ABNORMAL_SORO_SLEEP,		// 眠り状态异常单体魔法
	B_SKILL_ABNORMAL_SORO_STONE,		// 石化状态异常单体魔法
	B_SKILL_ABNORMAL_SORO_INEBRIETY,	// 酔い状态异常单体魔法
	B_SKILL_ABNORMAL_SORO_CONFUSION,	// 混乱状态异常单体魔法
	B_SKILL_ABNORMAL_SORO_FORGET,		// 忘却状态异常单体魔法
	
	B_SKILL_ABNORMAL_AREA_POISON,		// 毒状态异常周辺魔法
	B_SKILL_ABNORMAL_AREA_SLEEP,		// 眠り状态异常周辺魔法
	B_SKILL_ABNORMAL_AREA_STONE,		// 石化状态异常周辺魔法
	B_SKILL_ABNORMAL_AREA_INEBRIETY,	// 酔い状态异常周辺魔法
	B_SKILL_ABNORMAL_AREA_CONFUSION,	// 混乱状态异常周辺魔法
	B_SKILL_ABNORMAL_AREA_FORGET,		// 忘却状态异常周辺魔法
	
	B_SKILL_ABNORMAL_SIDE_POISON,		// 毒状态异常片侧魔法
	B_SKILL_ABNORMAL_SIDE_SLEEP,		// 眠り状态异常片侧魔法
	B_SKILL_ABNORMAL_SIDE_STONE,		// 石化状态异常片侧魔法
	B_SKILL_ABNORMAL_SIDE_INEBRIETY,	// 酔い状态异常片侧魔法
	B_SKILL_ABNORMAL_SIDE_CONFUSION,	// 混乱状态异常片侧魔法
	B_SKILL_ABNORMAL_SIDE_FORGET,		// 忘却状态异常片侧魔法
	
	B_SKILL_TREAT_TYPE_EARTH,	// 地属性优遇魔法
	B_SKILL_TREAT_TYPE_WATER,	// 水属性优遇魔法
	B_SKILL_TREAT_TYPE_FIRE,	// 火属性优遇魔法
	B_SKILL_TREAT_TYPE_WIND,	// 风属性优遇魔法
	
	B_SKILL_REVERSE_TYPE,		// 属性反転魔法
	
	B_SKILL_REFLECTION_PHYSICS,	// 物理反射魔法
	B_SKILL_REFLECTION_MAGIC,	// 魔法反射魔法
	
	B_SKILL_ABSORB_PHYSICS,		// 物理吸收魔法
	B_SKILL_ABSORB_MAGIC,		// 魔法吸收魔法
	
	B_SKILL_INEFFECTIVE_PHYSICS,// 物理无效魔法
	B_SKILL_INEFFECTIVE_MAGIC,	// 魔法无效魔法
	
	B_SKILL_LP_INCREASE_SORO,	// LP回复单体魔法
	B_SKILL_LP_INCREASE_AREA,	// LP回复周辺魔法
	B_SKILL_LP_INCREASE_SIDE,	// LP回复片侧魔法
	B_SKILL_LP_RECOVER_SORO,	// LP再生单体魔法
	B_SKILL_LP_RECOVER_AREA,	// LP再生周辺魔法
	B_SKILL_LP_RECOVER_SIDE,	// LP再生片侧魔法
	
	B_SKILL_STATUS_RECOVER,		// 状态异常回复魔法
	B_SKILL_REVIVE,				// 気絶回复
	B_SKILL_SEAL,				// 封印支援
	
	B_SKILL_BREED,				// ブリード
	B_SKILL_TRAIN,				// 调教
	B_SKILL_STEAL,				// 盗む
	
	B_SKILL_NORMAL_ATTACK,		// 通常攻击
	B_SKILL_NORMAL_DEFFENCE,	// 通常防御
	
	// 以下モンスター专用スキル------ 
	
	B_SKILL_ADD_BAD_CONDITION_POISON,		// 毒状态付加攻击
	B_SKILL_ADD_BAD_CONDITION_SLEEP,		// 眠り状态付加攻击
	B_SKILL_ADD_BAD_CONDITION_STONE,		// 石化状态付加攻击
	B_SKILL_ADD_BAD_CONDITION_INEBRIETY,	// 酔い状态付加攻击
	B_SKILL_ADD_BAD_CONDITION_CONFUSION,	// 混乱状态付加攻击
	B_SKILL_ADD_BAD_CONDITION_FORGET,		// 忘却状态付加攻击
	
	B_SKILL_ATTACK_BLOOD,		// 吸血攻击
	B_SKILL_BOMB,				// 自爆
	B_SKILL_SACRIFICE,			// サクリファイス
	B_SKILL_ENERGYDRAIN,		// エナジードレイン
	B_SKILL_BREAKWEAPON,		// 装备破坏攻击
	B_SKILL_TERROR,				// 即死
	B_SKILL_ATTACKGOLD,			// GOLD攻击
	B_SKILL_SILENCE,			// 魔法封じ
	B_SKILL_SUMMON = 93,		// 召唤（ＩＤ　93）
	B_SKILL_KAKURAN = 94,		// 搅乱（格闘家）
	B_SKILL_RANDOM_SHOT = 95,	// 乱れ打ち（弓术士）
	B_SKILL_ASSASSIN = 96,		// 暗杀（忍者）
	B_SKILL_EARTHQUAKE = 97,	// 地震
	B_SKILL_DANCE = 98,			// 踊る
  
	B_SKILL_DEF_UP = 99,		// 防御力アップ
	B_SKILL_DEF_DOWN = 100,		// 防御力ダウン
	B_SKILL_ATK_UP = 101,		// 攻击力アップ
	B_SKILL_ATK_DOWN = 102,		// 攻击力ダウン
	B_SKILL_AGL_UP = 103,		// すばやさアップ
	B_SKILL_AGL_DOWN = 104,		// すばやさダウン
  
#ifdef _TEST_TECH_YUK
	B_SKILL_RANBU = 105,		// 连激乱舞
	B_SKILL_ATTACKALL = 106,	// 全体攻击
	B_SKILL_DETECTENEMY = 107,	// 敌状态调查
	B_SKILL_URGENTALLOWANCE = 108,	// 紧急手当
	B_SKILL_URGENTMEDIC = 109,	// 紧急治疗
	B_SKILL_POISON_ARROW = 110,	// 毒矢
	B_SKILL_AXEBOMBER = 111,	// アックスボンバー
	B_SKILL_ULTIMATEATTACK = 112,	// 狮子咆
	B_SKILL_PICKPOCKET = 113,	// 掏る
	B_SKILL_RCV_UP = 114,		// 回复力アップ
	B_SKILL_PARRYING = 115,		// 捌く
	B_SKILL_THROWITEM = 116,	// 投掷
	B_SKILL_PROVOCATION = 117,	// 煽る
#endif /* _TEST_TECH_YUK */
#ifdef PUK2_NEWSKILL
	B_SKILL_PUK2_START = 257,						// ＰＵＫ２スキル开始位置

	B_SKILL_KAIKYOUKAISOU = B_SKILL_PUK2_START,		// 戒骄戒躁
	B_SKILL_ICHIGEKIHICCHU,							// 一击必中
	B_SKILL_DOKUGEKI,								// 毒击
	B_SKILL_ISSEKINICHO,							// 一石二鸟
	B_SKILL_KISHINOHOMARE,							// 骑士ノ誉
	B_SKILL_JINNSOKUKADANN,							// 迅速果断
	B_SKILL_YOTOKUNIKU,								// 羊头狗肉
	B_SKILL_KYOSHIIJO,								// 虚死为上
	B_SKILL_JINNBAITTAI,							// 人马一体
	B_SKILL_INNGAOHO,								// 因果应报
	#ifdef PUK3_NEWSKILL
		B_SKILL_PSYBLAST = 267,							// サイブラスト
		B_SKILL_COINSHOT,								// コインショット
		B_SKILL_JUMP,									// ジャンプ
	#endif

	B_SKILL_TOPPU = 400,							// 突风
	B_SKILL_ACID,									// アシッド
	B_SKILL_BARRIER,								// バリア
	#ifdef PUK3_NEWSKILL
		B_SKILL_BREAK_ATK = 404,						// ブレイク(ATK)
		B_SKILL_LVDOWN = 405,							// 等级ダウン
		B_SKILL_BREAK_MND = 406,						// ブレイク(MND)
	#endif

#ifdef TECH_BLASTWAVE
	B_SKILL_BLASTWAVE = 2005,						//追月
#endif

	B_SKILL_PUK2_END,								// ＰＵＫ２スキル終了位置
#endif
}B_SKILL;

// アイテム演出ＩＤ *******************************
typedef enum{

	B_ITEM_RECOVERY_LP,			// 体力回复
	B_ITEM_RECOVERY_FP,			// 魔力回复
	B_ITEM_RECOVERY_LP_FP,		// 体力魔力回复
	B_ITEM_ANABIOSIS,			// 苏生
	B_ITEM_STATUS_TROUBLE,		// 状态异常
	B_ITEM_STATUS_RECOVERY,		// 状态异常回复
	B_ITEM_SEAL,				// 封印
#ifdef PUK2
	B_ITEM_GUILDMONSTER_CALL,	// 公会宠物笛
#endif
	
}B_TIEM;

// 武器ＩＤ *************************************
typedef enum{
	
	B_WEAPON_DIRECT,			// 直接攻击武器
	B_WEAPON_KNIFE,				// ナイフ
	B_WEAPON_BOOMERANG,			// ブーメラン
	B_WEAPON_BOW,				// 弓矢
	
}B_WEAPON;

// 踊り效果ＩＤ ***********************************
typedef enum
{
	B_DANCE_POISON,				// 毒状态异常
	B_DANCE_SLEEP,				// 眠り状态异常
	B_DANCE_STONE,				// 石化状态异常
	B_DANCE_INEBRIETY,			// 酔い状态异常
	B_DANCE_CONFUSION,			// 混乱状态异常
	B_DANCE_FORGET,				// 忘却状态异常
	
	B_DANCE_POWER,				// 攻击受伤アップ
	B_DANCE_POSITION,			// ポジションチェンジ
	B_DANCE_PET_RETURN,			// ペット戾す
	B_DANCE_DEATH,				// 死亡
	B_DANCE_STATUS_RECOVER,		// 状态异常回复
	B_DANCE_REVIVE,				// 全体复活
	B_DANCE_BOMB,				// 自爆
	B_DANCE_SACRIFICE,			// サクリファイス
	
	B_DANCE_NONE,				// 效果なし
}B_DANCE;

// バトルマスターの予备构造体
typedef struct{
	int cmbCnt;
	int cmbCntMax;
	int cmbList[ 20 ];
	int subActNo;
#ifdef PUK2
	int bmCnt;				// 实行するバトルマスターの数
	int bmNo;				// BattleMaster ミラー登録番号
	int BmReadPoint;		// このミラーが管理するムービーデータのポインタ
	int bmWait;				// 待机カウンタ
	int myId,myIdBak;		// 自分のＩＤ
	int petId,enemyId;		// ペットと敌のＩＤ
	int skillId,techId,itemId;		// スキルと使用アイテムのＩＤ
	int danceId,reflectId;	// 反射する人のＩＤ
	int bmFlag,bmFlagBak;
	int newGraNoBak;
	#ifdef PUK3_WEPON_BREAK
		int ridePetGraNoBak, putonGraNoBak;	// 新グラフィック番号バックアップ
	#endif
	int point;				// チャット用表示フレーム数
#endif
#ifdef PUK2_HIGHSPEED_BATTLE
	unsigned long actFlag;	// どのキャラが动いているかを示す
#endif
#ifdef PUK3_BATTLEACT_STOP
	int runcnt;				// 行动开始からの経过时间
#endif
	ACTION *pTreatType;
}BM_YOBI;

// バトルマスターの行动
enum{
	//BM_ACT_HIT,		// 攻击
	//BM_ACT_MSL,		// 飞び道具
	BM_ACT_SKL,		// スキル攻击
	BM_ACT_ESC,		// 逃げる
	BM_ACT_CLR,		// 退却
	BM_ACT_SUP,		// スキルアップ
	BM_ACT_POS,		// ポジションチェンジ
	BM_ACT_MON,		// ぺっと出し入れ
	BM_ACT_EQU,		// 装备变更
	BM_ACT_ITM,		// アイテム使用
	BM_ACT_CMB,		// 合体攻击
	BM_ACT_PSN,		// 毒受伤、体力再生
	BM_ACT_ANR,		// 状态异常自动回复
	BM_ACT_PUE,		// パラメータアップダウン效果終了( Parameter Updown End )
	BM_ACT_ACT,		// ２アクションマーク
	BM_ACT_CFP,		// 魔力消费
	BM_ACT_CHT,		// 战闘チャット
	BM_ACT_PNO,		// ペット嫌がる
	BM_ACT_PLV,		// ペット逃走
	BM_ACT_CBC,		// ＢＣデータ变更
	BM_ACT_PDN,		// 受伤ダウン（踊り专用）
#ifdef PUK2
	BM_ACT_REB,		// トランス
	BM_ACT_BST,		// バースト时间减少
#endif
#ifdef PUK2_GUILMON_BATTLE
	BM_ACT_GMN,		// 公会宠物登场
#endif
#ifdef PUK3_RIDE_BATTLE
	BM_ACT_RIDE,	// ペットライド
#endif
	BM_ACT_END,		// 战闘終了
	BM_ACT_MAX,		// 終端记号
};

// バトルマスターアクションポインタ
extern ACTION *pActBm;

// バトルキャラクターデータ読み込みポイント
extern int BcReadPoint;
// バトルムービーデータ読み込みポイント
extern int BmReadPoint;

// バトルムービーデータ文字列
extern char BmData[ B_MOVIE_DATA_SIZE ];
extern char BmDataBak[ B_BUF_SIZE ][ B_MOVIE_DATA_SIZE ];
// バトルコマンド文字列ポインター
extern int BmDataGetBufNo;
extern int BmDataSetBufNo;

// バトルキャラクター文字列
extern char BcData[ B_CHAR_DATA_SIZE ];
extern char BcDataBak[ B_BUF_SIZE ][ B_CHAR_DATA_SIZE ];
// バトル状态文字列ポインター
extern int BcDataGetBufNo;
extern int BcDataSetBufNo;

// Ｂｍチャットバッファー
//extern char BmChatBuffer[ BM_CHAT_BUFFER_SIZE ][ 256 ];

// 怪我情报受信
//extern char BiData[ B_MOVIE_DATA_SIZE ];

// ムービー読み込みフラグ
extern int ReadBmDataFlag;
extern int BmEndFlag;
// 自分がＡＫＯかフラグ
extern int MyAkoFlag;

#ifdef PUK2_BATTLE_ACT_TURN
	extern int battlewait;
#endif
#ifdef PUK2
	// ミラー识别
	extern unsigned int BattleMasterBit;
	// バトルマスター事后处理
	void BattleSuperMaster_Death();
#endif

// バトル初期化 ****************************************************************/
void InitBattle( void );
// バトルキャラクター处理 ***********************************************/
void BattleChar( ACTION *pAct );

// リストの初期化
void BattleInitList( void );
// 行动リストをセットする
#ifdef PUK2
	void BattleSetActList( ACTION *pActBmm, ACTION *pAct, BC_ACT actNo );
#else
	void BattleSetActList( ACTION *pAct, BC_ACT actNo );
#endif
// 行动リストスタート
void BattleStartActList( ACTION *pAct );
// 次の行动リストへ
void BattleNextActList( ACTION *pAct );
// 敌リストをセットする
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage );
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage, int newGraNo );
#ifdef PUK2
	// バースト时间减少リストをセットする
	void BattleSetBstList( ACTION *pAct, int time );
	// 次のバースト时间减少リストへ
	BOOL BattleNextBstList( ACTION *pAct );
#endif
// 次の敌リストへ
BOOL BattleNextEnemyList( ACTION *pAct );
// 行动終了チェック
BOOL BattleCheckActEnd( ACTION *pActBmm, int flag );
// バトルムービーデータからコマンドを読み込む
int ReadBmDataCmd( char *cmd );
// バトルムービーデータから数字を読み込む
int ReadBmDataNum( void );
// Ｂｍチャットバッファーにためる *********************************************
void StockBmChatBuffer( char *moji );
// Ｂｍチャットバッファーからとって来る *********************************************
char *GetBmChatBuffer( void );
	#ifdef PUK3_RIDE_BATTLE
		void SetActAlbumRegist( ACTION *pAct );
	#endif
	#ifdef PUK3_WEPON_BREAK
		void setnewGraNo( ACTION *pActMy, int enemyId, int bmFlag,
			 int newGraNo, int ridePetGraNo, int putonGraNo );
		void getnewGraNo( ACTION *pAct, int *enemyId,
			 int *newGraNo, int *ridePetGraNo, int *putonGraNo );
	#endif
#endif
