﻿/************************/
/*	action.h			*/
/************************/
#ifndef _ACTION_H_
#define _ACTION_H_

#ifdef PUK2
	#include "../PUK2/newDraw/BLT_MEMBER.H"
#endif

// アクションの文字サイズ
#define ACT_STR_SIZE 64
// アクションスローのフレーム数
#define ACT_SLOW_WAIT 4

/* 处理优先顺位  prio の值 ****************************************************/
enum{
	PRIO_TOP,			/* 最优先 	*/

	PRIO_MASTER,		/* キャラクタ管理マスター*/
	PRIO_CHR,			/* キャラクタ等 */
	PRIO_ETC,			// いろいろ

	PRIO_BG,			/* ＢＧ 	*/
	PRIO_JIKI,			/* 自机 	*/
	PRIO_ENEMY,			/* 敌 		*/
	PRIO_ENEMY_TAMA,	/* 敌弹 	*/
	PRIO_JIKI_TAMA,		/* 自机弹 	*/
	PRIO_ITEM,			/* アイテム	*/
	PRIO_BOSS,			/* ボス		*/
	PRIO_GAME_OVER,		/* GAME OVER */
	PRIO_BTM = 255		/* 最后尾 	*/
};

/* アクションの状态 ***********************************************************/
enum{
	ACT_STATE_ACT = 0,	/* 通常状态 */
	ACT_STATE_DEAD		/* 死亡状态 */
};
/* 属性 ***********************************************************************/
#define ACT_ATR_HIDE 			( 1 <<  1 )	/* 非表示 */
#define ACT_ATR_HIT 			( 1 <<  2 )	/* 当たり判定する */
#define ACT_ATR_HIT_BOX 		( 1 <<  3 )	/* 当たり判定 + ボックス表示 */
#define ACT_ATR_INFO 			( 1 <<  4 )	/* 一行インフォ表示 */
#define ACT_ATR_HIDE2 			( 1 <<  5 )	/* 非表示+あたり判定あり */
#define ACT_ATR_BTL_CMD_END 	( 1 <<  6 )	/* 战闘コマンド入力終了フラグ */
#define ACT_ATR_TYPE_PC 		( 1 <<  7 )	/* ＰＣ */
#define ACT_ATR_TYPE_OTHER_PC 	( 1 <<  8 )	/* 他のＰＣ */
#define ACT_ATR_TYPE_PET 		( 1 <<  9 )	/* ペット */
#define ACT_ATR_TYPE_ITEM 		( 1 << 10 )	/* アイテム */
#define ACT_ATR_TYPE_GOLD 		( 1 << 11 )	/* お金 */
#define ACT_ATR_TYPE_OTHER 		( 1 << 12 )	/* その他（ドア、町の人、看板） */
#define ACT_ATR_TRAVEL 			( 1 << 13 )	/* 旅行中 */
#define ACT_ATR_FONT_HIDE 		( 1 << 14 )	/* フォント非表示 */
#define ACT_ATR_FLASH_0 		( 1 << 15 )	/* 点灭表示（フリップカウントが０の时） */
#define ACT_ATR_FLASH_1 		( 1 << 16 )	/* 点灭表示（フリップカウントが１の时） */
#define ACT_ATR_BATTLE_SORT 	( 1 << 17 )	/* 战闘时のＹ座标ソート处理ＯＮ */
#define ACT_ATR_ACT_SLOW 		( 1 << 18 )	/* アクションスローフラグＯＮ */
#ifdef PUK2
	#define ACT_ATR_TYPE_TAKENPET 		( 1 << 19 )	/* 连れ歩きペット */
	#define ACT_ATR_TRANCEPARENT 		( 1 << 20 )	/* 半透明表示（不透明度值128） */
#endif
#ifdef PUK2
	#define ACT_ATR_TYPE_MONSTERMAIL	( 1 << 21 )	/* モンスターメール中ペット */
	#define ACT_ATR_NOFLIP		( 1 << 22 )	/* 话し挂けたときの反転无し */
#endif
#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE)
	#define ACT_ATR_BATTLE_SORT_D 	( 1 << 23 )	/* 战闘时のＹ座标ソート处理dx,dy使用 */
#endif
#ifdef PUK2_3DDEVICECHANGE_BATTLE
	#define ACT_ATR_3D_NOFLASH 		( 1 << 24 )	/* 3D使用时に ACT_ATR_FLASH_0 と ACT_ATR_FLASH_1 を无视 */
#endif

#ifdef PUK2_MEMCHECK
enum ACT_TYPE{
	ACT_T_WIN_NULL,
	// ウィンドウ关系
	ACT_T_WIN_DISP,
	// 战闘关系
	ACT_T_BM_YOBI,
	ACT_T_BC_YOBI,
#ifdef PUK3_RIDE_BATTLE_2
	ACT_T_RIDE_YOBI,
#endif
	ACT_T_EFFECT_ANABIOSIS,
	ACT_T_BATTLEMESSAGEBOX,
	// キャラ关系
	ACT_T_CHAREXTRA,
	ACT_T_PETEXTRA,
#ifdef PUK3_VEHICLE
	ACT_T_VEHICLEEXTRA,
#endif
	ACT_T_TRN_YOBI,
	// その他
	ACT_T_JIKI,
	ACT_T_ENEMY,
};
#endif

#if defined(PUK3_ACTION_CHECKSUM) || defined(PUK3_ACTION_CHECKRANGE)

#ifdef PUK3_ACTION_CHECKRANGE

enum eACTION_DEBUG_DEATH_STATE{
	eACTION_DEBUG_DEATH_STATE_FREE = 0,
#ifdef PUK3_ACTION_CHECKSUM
	eACTION_DEBUG_DEATH_STATE_DEATH,
#endif
	eACTION_DEBUG_DEATH_STATE_ALIVE, 
};

#endif

typedef struct{
#ifdef PUK3_ACTION_CHECKSUM
	int checksum;						// チェックサム
	int pYobichecksum;					// チェックサム
	int pYobiSize;						// サイズ
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	int deathState;
#endif

	char getPos_file[300];				// GetActionを呼び出したファイル名
	int getPos_line;					// GetActionを呼び出した行数
} ACTION_DEBUG;

#endif
/* アクション构造体 **********************************************************/
struct action{
#ifdef PUK2_MEMCHECK
	enum ACT_TYPE	actType;			// 予备构造体の种类
#endif
	// システム关连
	struct 	action *pPrev, *pNext;		// 前后のアクションポインタ
	void 	( *func )( struct action * );	// 实行关数へのポインタ
	void 	*pYobi;						// 予备构造体ポインタ
	void 	*pOther;					// 泛用记忆ポインタ
	UCHAR 	prio;						// 处理の优先顺位
	UCHAR 	dispPrio;					// 表示の优先顺位
	int		hitDispNo;					// 当たり判定用番号
	BOOL	deathFlag;					// アクション死亡フラグ
	int		bmpNo;						// スプライト番号
	int		atr;						// 属性
	int		state;						// 状态
	int		actNo;						// 行动番号（自由に使用可）
	
	// 座标关连
	int 	x, y;						// 表示座标
	int 	dx, dy;						// 增分
	float 	fx, fy;						// 小数点座标
	float 	dfx, dfy;					// 小数点增分
	float 	dir;						// 方向（右が０度で右回り）
	float 	speed;						// 速度
	int 	delta;  					// 合成ベクトル
	float 	scaleX;  					// 扩大缩小スケールＸ（初期值１．０）
	float 	scaleY;  					// 扩大缩小スケールＹ（初期值１．０）

	// 	フォント关连
	char 	fontStr[ ACT_STR_SIZE ];	// フォントの文字列
	int 	fontX, fontY;				// フォントの座标
	float 	fontfX, fontfY;				// フォントの少数座标
	float 	fontdfX, fontdfY;			// 增分
	char 	fontColor;					// 表示色
	char 	fontKind;					// 种类
	UCHAR	fontPrio; 					// 表示の优先顺位
	BOOL 	fontHitFlag;				// 当たり判定するかフラグ
		
	// キャラクター关连
	char	name[ 29 ];					// 名称
	char	freeName[ 33 ];				// フリー名称
#ifdef PUK2
	char	guildName[32+1];			// 家族名
	char	guildTitle[32+1];			// 家族称号名
#endif
	char	title[17];					// 称号
	char	jobName[25];				// 职业名
	int 	hp;							// ＨＰ
	int 	maxHp;						// 最大ＨＰ
	int 	mp;							// ＭＰ
	int 	maxMp;						// 最大ＭＰ
	int 	fp;							// 魔力
	int 	maxFp;						// 最大魔力
	int 	level;						// ＬＥＶＥＬ
	int 	status;						// 状态
	int 	itemNameColor;				// アイテムの名称の色
	int		damage;						// 受伤
	
	// マップ关连
	int		gx, gy;						// マップグリッド座标（现在地）
	int		nextGx, nextGy;				// マップグリッド座标（目的地）
	int		bufGx[10], bufGy[10];		// 移动先座标のバッファ
	short	bufCount;					// 移动座标が何个设定されているか
	short	walkFlag;					// 歩きアニメから立ちアニメへ切り替えタイミングフラグ
	float	mx, my;						// マップ座标
	float	vx, vy;						// 移动增分

#ifdef PUK3_RIDE	// ライドの移动テスト
	int		walkSpeed;						// 移动速度
#endif

	// 属性
	short 	earth;						// 地 属性
	short 	water;						// 水 属性
	short 	fire;						// 火 属性
	short 	wind;						// 风 属性

	// ｒａｄｅｒ用
	int		dirCnt;						// 方向变换カウンター

	//ｇｅｍｉｎｉ用
	int		spd;						// 移动スピード( ０～６３ )( ４で１ドット )
	int		crs;						// 方向( ０～３１ )( 上が０で右回り )
	int		h_mini;						// 小数点横
	int		v_mini;						// 小数点縦

	//アニメーション关连
	int		anim_speed;					// アニメーションスピード（デフォルト１００％）
	int		anim_chr_no;				// アニメーション番号（ anim_tbl.h の番号 )
	int		anim_chr_no_bak;			// 前回アニメーション番号
	int		anim_no;					// 动作番号
	int		anim_no_bak;				// 前回の动作番号
	int		anim_ang;					// アニメーション向き( ０～７ )( 下が０で右回り )
	int		anim_ang_bak;				// 前回のアニメーション向き
	int		anim_cnt;					// アニメーションカウンタ
	int		anim_frame_cnt;				// アニメーションフレームカウンタ
	int		anim_x;						// OFFセットＸ座标 ( Sprbin + Adrnbin )
	int		anim_y;						// OFFセットＹ座标 ( Sprbin + Adrnbin )
	int		anim_hit;					// 当たり判定
	int		animOffFlag;				// アニメーションしないフラグ
	int		animNoLoopFlag;				// アニメーションループしないフラグ
	int		animLoopCnt;				// ループ回数
#ifdef PUK3_RIDE
	int		animLoop_bak;				// 前回のループの设定
#endif
#ifdef PUK3_RIDEBIN
	int		anim_cdx;					// Ｘ座标データ
	int		anim_cdy;					// Ｙ座标データ
#endif

#ifdef PUK2
	//绘图设定
	struct BLT_MEMBER bm;				// 绘图设定

	unsigned char rgbaon;				// 色、アルファ值の指定をプログラムで变更できるようにする
	unsigned char bltfon;				// 绘图设定をプログラムで变更できるようにする
	union RGBA_DATA rgba;				// プログラムで变更する描画时の色、アルファ值の指定
	unsigned char bltf;					// プログラムで变更する描画フラグ

	int MapAnimePartsCX;				//マップで使用するアニメーション布ツの描画补正值
	int MapAnimePartsCY;
#endif
#if defined(PUK3_ACTION_CHECKSUM) || defined(PUK3_ACTION_CHECKRANGE)
	ACTION_DEBUG db;
#endif
};

typedef struct action ACTION;


/* 最初と最后のアクションリストポインタ */
extern ACTION *pActTop;
extern ACTION *pActBtm;

// アクションスローフラグ
extern int	ActSlowFlag;
extern int ActSlowCnt;

#ifdef _DEBUG		
/* アクション数カウント */
extern int ActCnt;
#endif

/* アクションリストに登録 *****************************************************/
#if defined(PUK3_ACTION_CHECKSUM) || defined(PUK3_ACTION_CHECKRANGE)
	#ifdef PUK2_MEMCHECK
	ACTION *_GetAction( UCHAR prio, UINT yobiSize, char *file, int line, enum ACT_TYPE actType = ACT_T_WIN_NULL );
	#define GetAction( prio, yobiSize, actType ) _GetAction( prio, yobiSize, __FILE__, __LINE__, actType )
	#define GetAction( prio, yobiSize ) _GetAction( prio, yobiSize, __FILE__, __LINE__ )
	#else
	ACTION *_GetAction( UCHAR prio, UINT yobiSize, char *file, int line );
	#define GetAction( prio, yobiSize ) _GetAction( prio, yobiSize, __FILE__, __LINE__ )
	#endif
#else
#ifdef PUK2_MEMCHECK
ACTION *GetAction( UCHAR prio, UINT yobiSize, enum ACT_TYPE actType = ACT_T_WIN_NULL );
#else
ACTION *GetAction( UCHAR prio, UINT yobiSize );
#endif
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	BOOL _CheckAction( ACTION *pAct, char *file, int line );
	#define CheckAction( pAct ) _CheckAction( pAct, __FILE__, __LINE__ )
#endif

/* アクションリスト初期化 *****************************************************/
void InitAction( void );

/* アクション走らせる *********************************************************/
void RunAction( void );

/* アクションリストから抹杀准备************************************************/
void DeathAction( ACTION *pAct );

/* 全アクション抹杀准备 *******************************************************/
void DeathAllAction( void );

/* アクションリストから完全抹杀 ***********************************************/
void ClearAction( ACTION *pAct );

/* アクション終了处理 *********************************************************/
void EndAction( void );

#endif
