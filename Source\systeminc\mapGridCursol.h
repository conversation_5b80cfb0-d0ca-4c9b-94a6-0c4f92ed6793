﻿#ifndef _MAP_GRID_CURSOL_H_
#define _MAP_GRID_CURSOL_H_


// マップ画面上のグリッドカーソル处理
enum
{
	MOUSE_CURSOR_MODE_NORMAL,
	MOUSE_CURSOR_MODE_MOVE
};

// 移动モードに变わるまでのマウスの左ボタンを押す时间（ミリ秒）
#define MOVE_MODE_CHANGE_TIME	500

extern int mouseMapGx, mouseMapGy;
extern int mouseMapX, mouseMapY;
extern short mouseCursorMode;

// マップ画面でのマウスボタン处理
extern BOOL mouseLeftClick;
extern BOOL mouseLeftOn;
extern BOOL mouseRightClick;
extern BOOL mouseRightOn;
extern unsigned int mouseLeftPushTime;
extern unsigned int beforeMouseLeftPushTime;

extern int gridCursorFlag;
extern int gridCursorDrawFlag;
extern int leftBtnOffFlag;
extern int rightBtnOffFlag;

void mapGridCursolProc( void );

#endif
