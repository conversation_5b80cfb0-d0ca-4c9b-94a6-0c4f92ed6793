﻿
#include "menuShortCut.h"

//ショートカットのモード
int ShortCutMode;

void InitShortCut(void){
	ShortCutMode=ShortCutPuk1Mode;
}

void ChangeChatMode(void);

// ショートカット管理
void MenuWindowShortCut(void)
{
	int res;

	switch(ShortCutMode){

	//旧版ショートカット
	case ShortCutPuk1Mode:
		//Ctr＋＊＊＊
		if( (VK[VK_CONTROL] & KEY_ON) ){
		//ウインドウ开く系
			//状态（+'W'）
			if( (VK[VK_W] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_STATUS].wininfo==NULL &&
					WindowFlag[MENU_WINDOW_DETAIL].wininfo==NULL &&
					WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL
#ifdef PUK3_PROF
					&& WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL
#endif
					){
					//作る
					openMenuWindow( MENU_WINDOW_STATUS, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					if(WindowFlag[MENU_WINDOW_STATUS].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_STATUS].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					if(WindowFlag[MENU_WINDOW_DETAIL].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_DETAIL].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					if(WindowFlag[MENU_WINDOW_TITLE].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_TITLE].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
#ifdef PUK3_PROF
					if(WindowFlag[MENU_WINDOW_PROFILE].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
#endif
				}
			}

			//スキル（+'E'）
			if( (VK[VK_E] & KEY_ON_ONCE)){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_SKILL].wininfo==NULL){
						//ないので作る
						openFeildSkillWindow();
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskSkillButton();
			}

			//アイテム（+'R'）
			if( (VK[VK_R] & KEY_ON_ONCE)){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_ITEM].wininfo==NULL){
						//ないので作る
						openMenuWindow( MENU_WINDOW_ITEM, OPENMENUWINDOW_HIT, 0 );
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_ITEM].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskItemButton();
			}

			//モンスター（+'A'）
			if( (VK[VK_A] & KEY_ON_ONCE)){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_MONSTER].wininfo==NULL){
						//作る
						openMenuWindow( MENU_WINDOW_MONSTER, OPENMENUWINDOW_HIT, 0 );
					}else{
						//消す
						WindowFlag[MENU_WINDOW_MONSTER].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskMonsterButton();
			}

			//アドレス（+'S'）
			if( (VK[VK_S] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_ADDRESS].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_ADDRESS, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_ADDRESS].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			//アルバム（+'D'）
			if( (VK[VK_D] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_ALBUM].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_ALBUM, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_ALBUM].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			//アクション（+'F'）
			if( (VK[VK_F] & KEY_ON_ONCE)){
				//バトル时使用不可
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_ACTION].wininfo==NULL){
						//作る
						openMenuWindow( MENU_WINDOW_ACTION, OPENMENUWINDOW_HIT, 0 );
					}else{
						//消す
						WindowFlag[MENU_WINDOW_ACTION].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}

			//マップ（+'Q'）
			if( (VK[VK_Q] & KEY_ON_ONCE)){
				//バトル时使用不可
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_MAP].wininfo==NULL){
						//作る
						openMenuWindow( MENU_WINDOW_MAP, OPENMENUWINDOW_HIT, 0 );
					}else{
						//消す
						WindowFlag[MENU_WINDOW_MAP].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}
			//ショートカット确认（+'Y'）
			if( (VK[VK_Y] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_SYSTEMSHORTCUT, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

	//チャットウインドウ系
			//聊天文字大小变更（+'O'）(オー)
			if( (VK[VK_O] & KEY_ON_ONCE)){
				ChatWindowChangeFontSize();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'↑'）
			if( (VK[VK_UP] & KEY_ON_ONCE)){
				ChatWindowLineUp();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'↓'）
			if( (VK[VK_DOWN] & KEY_ON_ONCE)){
				ChatWindowLineDown();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'→'）
			if( (VK[VK_RIGHT] & KEY_ON_ONCE)){
				ChatWindowStrDown();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'←'）
			if( (VK[VK_LEFT] & KEY_ON_ONCE)){
				ChatWindowStrUp();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			
	//コミュニケーション系
			//デュエルフラグ（+'P'）
			if( (VK[VK_P] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagDu();
			}
			//観战フラグ（+';'）
			if( (VK[VK_USER_SEMICOLON] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagCh();
			}
			//グループフラグ（+'.'）
			if( (VK[VK_USER_PERIOD] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagPa();
			}
			//名刺交换フラグ（+'L'）
			if( (VK[VK_L] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagAd();
			}
			//トレードフラグ（+','）
			if( (VK[VK_USER_COMMA] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagTr();
			}
			//家族フラグ（+'N'）
			if( (VK[VK_N] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagGu();
			}


			//バトル时使用不可
			if( ProcNo != PROC_BATTLE ){
				//デュエル申し込み（+'@'）
				if( (VK[VK_USER_AT_MARK] & KEY_ON_ONCE)){
					CockpitRightLargeDuel();
				}
				//観战申し込み（+':'）
				if( (VK[VK_USER_COLON] & KEY_ON_ONCE)){
					CockpitRightLargeWatch();
				}
				//グループ申し込み（+'/'）
				if( (VK[VK_USER_SLASH] & KEY_ON_ONCE)){
					CockpitRightLargeGroup();
				}
				//名刺交换申し込み（+'['）
				if( (VK[VK_USER_OPEN_BRACKET] & KEY_ON_ONCE)){
					CockpitRightLargeNameCard();
				}
				//トレード申し込み（+']'）
				if( (VK[VK_USER_CLOSE_BRACKET] & KEY_ON_ONCE)){
					CockpitRightLargeTrade();
				}
				//家族劝诱（+'M'）
				if( (VK[VK_M] & KEY_ON_ONCE)){
					CockpitRightLargeGuild();
				}

		//アクション系
				//アクション（+'1'）座る
				if( (VK[VK_1] & KEY_ON_ONCE)){
					changeAction(0);
					SetMenuWindowActionFlag(0);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'2'）挥手
				if( (VK[VK_2] & KEY_ON_ONCE)){
					changeAction(2);
					SetMenuWindowActionFlag(1);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'3'）点头
				if( (VK[VK_3] & KEY_ON_ONCE)){
					changeAction(4);
					SetMenuWindowActionFlag(2);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'4'）喜ぶ
				if( (VK[VK_4] & KEY_ON_ONCE)){
					changeAction(6);
					SetMenuWindowActionFlag(3);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'5'）怒る
				if( (VK[VK_5] & KEY_ON_ONCE)){
					changeAction(8);
					SetMenuWindowActionFlag(4);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'6'）悲伤
				if( (VK[VK_6] & KEY_ON_ONCE)){
					changeAction(10);
					SetMenuWindowActionFlag(5);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'7'）投掷
				if( (VK[VK_7] & KEY_ON_ONCE)){
					changeAction(12);
					SetMenuWindowActionFlag(6);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'8'）立つ
				if( (VK[VK_8] & KEY_ON_ONCE)){
					changeAction(1);
					SetMenuWindowActionFlag(9);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'9'）步く
				if( (VK[VK_9] & KEY_ON_ONCE)){
					changeAction(3);
					SetMenuWindowActionFlag(10);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'0'）倒下
				if( (VK[VK_0] & KEY_ON_ONCE)){
					changeAction(5);
					SetMenuWindowActionFlag(11);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'T'）移动
				if( (VK[VK_T] & KEY_ON_ONCE)){
					changeAction(15);
					SetMenuWindowActionFlag(16);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'-'）攻击
				if( (VK[VK_USER_SUB] & KEY_ON_ONCE)){
					changeAction(7);
					SetMenuWindowActionFlag(12);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'^'）防御
				if( (VK[VK_USER_TILT] & KEY_ON_ONCE)){
					changeAction(9);
					SetMenuWindowActionFlag(13);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'G'）受伤
				if( (VK[VK_G] & KEY_ON_ONCE)){
					changeAction(11);
					SetMenuWindowActionFlag(14);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'B'）魔法
				if( (VK[VK_B] & KEY_ON_ONCE)){
					changeAction(13);
					SetMenuWindowActionFlag(15);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'\'）ランダムじゃんけん
				if( (VK[VK_USER_YEN] & KEY_ON_ONCE)){
					res = (rand() % 3);
					switch(res){
						case 0:
							changeAction( 14 );
							SetMenuWindowActionFlag(7);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						break;
						case 1:
							changeAction( 16 );
							SetMenuWindowActionFlag(8);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						break;
						default:
							changeAction( 17 );
							SetMenuWindowActionFlag(17);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						break;
					}
				}
			}

			//画面から半分以上外れているウインドウを初期位置へ(+'F12')
			if( (VK[VK_F12] & KEY_ON_ONCE)){
				ResetWindowPos();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//フェイールド上でのLPFP表示(+'F11')
			if( (VK[VK_F11] & KEY_ON_ONCE)){
				if(CharHPLPStatusFlag==TRUE){
					CharHPLPStatusFlag=FALSE;
				}else{
					CharHPLPStatusFlag=TRUE;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

#ifdef NewOrOldChatMode
			//チャットモードの新旧切换
			if( (VK[VK_HOME] & KEY_ON_ONCE)){
				ChangeChatMode();
			}
#endif

		//Shift＋＊＊＊
		}else if(VK[VK_SHIFT] & KEY_ON){

			//すべてのウインドウを关闭('Esc')
			if(VK[VK_ESCAPE] & KEY_ON_ONCE){
				CloseWindowSelectType( 7 );
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}

			//チャットウインドウ消す
			if( (VK[VK_HOME] & KEY_ON_ONCE)){
				if(ChatWindowView==1){
					ChatWindowView=0;
				}else{
					ChatWindowView=1;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//チャットエリア增やす
			if( (VK[VK_RIGHT] & KEY_ON_ONCE)){
				NowMaxVoice++;
				if( NowMaxVoice > MAX_VOICE )
					NowMaxVoice=1;				
				// チャットエリア表示时间设定
				ChatAreaDispTime = 180;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//チャットエリア减らす
			if( (VK[VK_LEFT] & KEY_ON_ONCE)){
				NowMaxVoice--;
				if( NowMaxVoice < 1 )
					NowMaxVoice=MAX_VOICE;				
				// チャットエリア表示时间设定
				ChatAreaDispTime = 180;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//チャットログ↑
			if( (VK[VK_UP] & KEY_ON_REP)){
				ChatWinST.DrawChatLine++;
				if(ChatWinST.DrawChatLine>ChatWinST.StockLine){
					ChatWinST.DrawChatLine=ChatWinST.StockLine;
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}

			//チャットログ↓
			if( (VK[VK_DOWN] & KEY_ON_REP)){
				ChatWinST.DrawChatLine--;
				if(ChatWinST.DrawChatLine<0){
					ChatWinST.DrawChatLine=0;
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}

			// チャット表示を一时消す
			if( VK[ VK_HOME ] & KEY_ON ){ 
				if(ChatHideFlag==TRUE){
					// フラグＯＦＦ
					ChatHideFlag = FALSE;
				}else{
					// フラグＯＮ
					ChatHideFlag = TRUE;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
		
		
		}else{
			//その他

			//システム（'ESCAPE'）
			if( (VK[VK_ESCAPE] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_SYSTEM].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_SYSTEM, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_SYSTEM].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			//フェイールド上での名称表示('F11')
			if( (VK[VK_F11] & KEY_ON_ONCE)){
				shortCutFncShowName();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//スクリーンショット('F12')
			if( (VK[VK_F12] & KEY_ON_ONCE)){
				shortCutFncScreenShot();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			// チャットクリア
			if( VK[ VK_HOME ] & KEY_ON_ONCE ){ 
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				// チャットバッファー抹杀
				ClearChatBuffer();
			}
		
		}
		break;

	//新版ショートカット
	case ShortCutPuk2Mode:

		//Ctr＋＊＊＊
		if( (VK[VK_CONTROL] & KEY_ON) ){
		//ウインドウ开く系
			//状态（+'Q'）
			if( (VK[VK_Q] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_STATUS].wininfo==NULL &&
					WindowFlag[MENU_WINDOW_DETAIL].wininfo==NULL &&
					WindowFlag[MENU_WINDOW_TITLE].wininfo==NULL
#ifdef PUK3_PROF
					&& WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL
#endif
				){
					//作る
					openMenuWindow( MENU_WINDOW_STATUS, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					if(WindowFlag[MENU_WINDOW_STATUS].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_STATUS].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					if(WindowFlag[MENU_WINDOW_DETAIL].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_DETAIL].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					if(WindowFlag[MENU_WINDOW_TITLE].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_TITLE].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
#ifdef PUK3_PROF
					if(WindowFlag[MENU_WINDOW_PROFILE].wininfo!=NULL){
						WindowFlag[MENU_WINDOW_PROFILE].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
#endif
				}
			}

			//スキル（+'W'）
			if( (VK[VK_W] & KEY_ON_ONCE)){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_SKILL].wininfo==NULL){
						//ないので作る
						openFeildSkillWindow();
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskSkillButton();
			}

			//アイテム（+'E'）
			if( (VK[VK_E] & KEY_ON_ONCE)){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_ITEM].wininfo==NULL){
						//ないので作る
						openMenuWindow( MENU_WINDOW_ITEM, OPENMENUWINDOW_HIT, 0 );
					}else{
						//あるので消す
						WindowFlag[MENU_WINDOW_ITEM].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskItemButton();
			}

			//モンスター（+'R'）
			if( (VK[VK_R] & KEY_ON_ONCE)){
				// フィールド
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_MONSTER].wininfo==NULL){
						//作る
						openMenuWindow( MENU_WINDOW_MONSTER, OPENMENUWINDOW_HIT, 0 );
					}else{
						//消す
						WindowFlag[MENU_WINDOW_MONSTER].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
				// 战闘中
				else BattleTaskMonsterButton();
			}

			//アドレス（+'T'）
			if( (VK[VK_T] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_ADDRESS].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_ADDRESS, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_ADDRESS].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			//アルバム（+'Y'）
			if( (VK[VK_Y] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_ALBUM].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_ALBUM, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_ALBUM].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			//アクション（+'A'）
			if( (VK[VK_A] & KEY_ON_ONCE)){
				//バトル时使用不可
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_ACTION].wininfo==NULL){
						//作る
						openMenuWindow( MENU_WINDOW_ACTION, OPENMENUWINDOW_HIT, 0 );
					}else{
						//消す
						WindowFlag[MENU_WINDOW_ACTION].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}

			//マップ（+'S'）
			if( (VK[VK_S] & KEY_ON_ONCE)){
				//バトル时使用不可
				if( ProcNo != PROC_BATTLE ){
					if(WindowFlag[MENU_WINDOW_MAP].wininfo==NULL){
						//作る
						openMenuWindow( MENU_WINDOW_MAP, OPENMENUWINDOW_HIT, 0 );
					}else{
						//消す
						WindowFlag[MENU_WINDOW_MAP].wininfo->flag |= WIN_INFO_DEL;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}
			//ショートカット确认（+'D'）
			if( (VK[VK_D] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_SYSTEMSHORTCUT, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_SYSTEMSHORTCUT].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

	//チャットウインドウ系
			//聊天文字大小变更（+'Z'）
			if( (VK[VK_Z] & KEY_ON_ONCE)){
				ChatWindowChangeFontSize();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'↑'）
			if( (VK[VK_UP] & KEY_ON_ONCE)){
				ChatWindowLineUp();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'↓'）
			if( (VK[VK_DOWN] & KEY_ON_ONCE)){
				ChatWindowLineDown();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'→'）
			if( (VK[VK_RIGHT] & KEY_ON_ONCE)){
				ChatWindowStrDown();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			//聊天文字大小变更（+'←'）
			if( (VK[VK_LEFT] & KEY_ON_ONCE)){
				ChatWindowStrUp();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			
	//コミュニケーション系
			//デュエルフラグ（+'U'）
			if( (VK[VK_U] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagDu();
			}
			//観战フラグ（+'I'）
			if( (VK[VK_I] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagCh();
			}
			//グループフラグ（+'O'）
			if( (VK[VK_O] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagPa();
			}
			//名刺交换フラグ（+'P'）
			if( (VK[VK_P] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagAd();
			}
			//トレードフラグ（+'@'）
			if( (VK[VK_USER_AT_MARK] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagTr();
			}
			//家族フラグ（+'['）
			if( (VK[VK_USER_OPEN_BRACKET] & KEY_ON_ONCE)){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ChangeEtcFlagGu();
			}


			//バトル时使用不可
			if( ProcNo != PROC_BATTLE ){
				//デュエル申し込み（+'J'）
				if( (VK[VK_J] & KEY_ON_ONCE)){
					CockpitRightLargeDuel();
				}
				//観战申し込み（+'K'）
				if( (VK[VK_K] & KEY_ON_ONCE)){
					CockpitRightLargeWatch();
				}
				//グループ申し込み（+'L'）
				if( (VK[VK_L] & KEY_ON_ONCE)){
					CockpitRightLargeGroup();
				}
				//名刺交换申し込み（+';'）
				if( (VK[VK_USER_SEMICOLON] & KEY_ON_ONCE)){
					CockpitRightLargeNameCard();
				}
				//トレード申し込み（+':'）
				if( (VK[VK_USER_COLON] & KEY_ON_ONCE)){
					CockpitRightLargeTrade();
				}
				//家族劝诱（+']'）
				if( (VK[VK_USER_CLOSE_BRACKET] & KEY_ON_ONCE)){
					CockpitRightLargeGuild();
				}

		//アクション系
				//アクション（+'1'）座る
				if( (VK[VK_1] & KEY_ON_ONCE)){
					changeAction(0);
					SetMenuWindowActionFlag(0);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'2'）挥手
				if( (VK[VK_2] & KEY_ON_ONCE)){
					changeAction(2);
					SetMenuWindowActionFlag(1);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'3'）点头
				if( (VK[VK_3] & KEY_ON_ONCE)){
					changeAction(4);
					SetMenuWindowActionFlag(2);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'4'）喜ぶ
				if( (VK[VK_4] & KEY_ON_ONCE)){
					changeAction(6);
					SetMenuWindowActionFlag(3);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'5'）怒る
				if( (VK[VK_5] & KEY_ON_ONCE)){
					changeAction(8);
					SetMenuWindowActionFlag(4);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'6'）悲伤
				if( (VK[VK_6] & KEY_ON_ONCE)){
					changeAction(10);
					SetMenuWindowActionFlag(5);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'7'）投掷
				if( (VK[VK_7] & KEY_ON_ONCE)){
					changeAction(12);
					SetMenuWindowActionFlag(6);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'8'）立つ
				if( (VK[VK_8] & KEY_ON_ONCE)){
					changeAction(1);
					SetMenuWindowActionFlag(9);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'9'）步く
				if( (VK[VK_9] & KEY_ON_ONCE)){
					changeAction(3);
					SetMenuWindowActionFlag(10);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'B'）倒下
				if( (VK[VK_B] & KEY_ON_ONCE)){
					changeAction(5);
					SetMenuWindowActionFlag(11);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'N'）移动
				if( (VK[VK_N] & KEY_ON_ONCE)){
					changeAction(15);
					SetMenuWindowActionFlag(16);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'M'）攻击
				if( (VK[VK_M] & KEY_ON_ONCE)){
					changeAction(7);
					SetMenuWindowActionFlag(12);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+','）防御
				if( (VK[VK_USER_COMMA] & KEY_ON_ONCE)){
					changeAction(9);
					SetMenuWindowActionFlag(13);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'.'）受伤
				if( (VK[VK_USER_PERIOD] & KEY_ON_ONCE)){
					changeAction(11);
					SetMenuWindowActionFlag(14);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'/'）魔法
				if( (VK[VK_USER_SLASH] & KEY_ON_ONCE)){
					changeAction(13);
					SetMenuWindowActionFlag(15);
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				//アクション（+'\'）ランダムじゃんけん
				if( (VK[VK_USER_YEN] & KEY_ON_ONCE)){
					res = (rand() % 3);
					switch(res){
						case 0:
							changeAction( 14 );
							SetMenuWindowActionFlag(7);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						break;
						case 1:
							changeAction( 16 );
							SetMenuWindowActionFlag(8);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						break;
						default:
							changeAction( 17 );
							SetMenuWindowActionFlag(17);
							// クリック音
							play_se( SE_NO_CLICK, 320, 240 );
						break;
					}
				}
			}

			//画面から半分以上外れているウインドウを初期位置へ(+'F12')
			if( (VK[VK_F12] & KEY_ON_ONCE)){
				ResetWindowPos();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//フェイールド上でのLPFP表示(+'F11')
			if( (VK[VK_F11] & KEY_ON_ONCE)){
				if(CharHPLPStatusFlag==TRUE){
					CharHPLPStatusFlag=FALSE;
				}else{
					CharHPLPStatusFlag=TRUE;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
#ifdef NewOrOldChatMode
			//チャットモードの新旧切换
			if( (VK[VK_HOME] & KEY_ON_ONCE)){
				ChangeChatMode();
			}
#endif
		//Shift＋＊＊＊
		}else if(VK[VK_SHIFT] & KEY_ON){

			//すべてのウインドウを关闭('Esc')
			if(VK[VK_ESCAPE] & KEY_ON_ONCE){
				CloseWindowSelectType( 7 );
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}

			//チャットウインドウ消す
			if( (VK[VK_HOME] & KEY_ON_ONCE)){
				if(ChatWindowView==1){
					ChatWindowView=0;
				}else{
					ChatWindowView=1;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//チャットエリア增やす
			if( (VK[VK_RIGHT] & KEY_ON_ONCE)){
				NowMaxVoice++;
				if( NowMaxVoice > MAX_VOICE )
					NowMaxVoice=1;				
				// チャットエリア表示时间设定
				ChatAreaDispTime = 180;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//チャットエリア减らす
			if( (VK[VK_LEFT] & KEY_ON_ONCE)){
				NowMaxVoice--;
				if( NowMaxVoice < 1 )
					NowMaxVoice=MAX_VOICE;				
				// チャットエリア表示时间设定
				ChatAreaDispTime = 180;
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//チャットログ↑
			if( (VK[VK_UP] & KEY_ON_REP)){
				ChatWinST.DrawChatLine++;
				if(ChatWinST.DrawChatLine>ChatWinST.StockLine){
					ChatWinST.DrawChatLine=ChatWinST.StockLine;
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}

			//チャットログ↓
			if( (VK[VK_DOWN] & KEY_ON_REP)){
				ChatWinST.DrawChatLine--;
				if(ChatWinST.DrawChatLine<0){
					ChatWinST.DrawChatLine=0;
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}

			// チャット表示を一时消す
			if( VK[ VK_HOME ] & KEY_ON ){ 
				if(ChatHideFlag==TRUE){
					// フラグＯＦＦ
					ChatHideFlag = FALSE;
				}else{
					// フラグＯＮ
					ChatHideFlag = TRUE;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

		}else{
			//その他

			//システム（'ESCAPE'）
			if( (VK[VK_ESCAPE] & KEY_ON_ONCE)){
				if(WindowFlag[MENU_WINDOW_SYSTEM].wininfo==NULL){
					//作る
					openMenuWindow( MENU_WINDOW_SYSTEM, OPENMENUWINDOW_HIT, 0 );
				}else{
					//消す
					WindowFlag[MENU_WINDOW_SYSTEM].wininfo->flag |= WIN_INFO_DEL;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			//フェイールド上での名称表示('F11')
			if( (VK[VK_F11] & KEY_ON_ONCE)){
				shortCutFncShowName();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			//スクリーンショット('F12')
			if( (VK[VK_F12] & KEY_ON_ONCE)){
				shortCutFncScreenShot();
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}

			// チャットクリア
			if( VK[ VK_HOME ] & KEY_ON_ONCE ){ 
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				// チャットバッファー抹杀
				ClearChatBuffer();
			}
		}
	break;
	}

}
