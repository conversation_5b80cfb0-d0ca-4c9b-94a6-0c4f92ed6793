﻿/************************/
/*	sprdisp.h			*/
/************************/
#ifndef _SPRDISP_H_
#define _SPRDISP_H_

#define DISP_BUFFER_SIZE 4096 	// 表示バッファサイズ

/* 表示优先顺位  dispPrio の值 *************************************************/
enum{
	DISP_PRIO_SEA 		= 1,	// タイル（海）
#ifdef PUK2
	DISP_PRIO_SEA_END 	= 2,	// タイル（海）描画の終了タイミング(これは、StockDispBuffer 等では使わないで下さい)
#endif
	DISP_PRIO_BG 		= 3,	/* ＢＧ 	*/
	DISP_PRIO_TILE 		= 5,	// タイル（ゲーム画面のフィールド部）
#ifdef PUK2
	DISP_PRIO_TILE_END	= 6,	// タイル描画の終了タイミング(これは、StockDispBuffer 等では使わないで下さい)
#endif
	DISP_PRIO_B_EFFECT_BAK,		// 战闘エフェクト（里侧）
	DISP_PRIO_B_EFFECT_BAK2,	// 战闘エフェクト（里侧２）
	DISP_PRIO_B_CHAR,			// 战闘キャラ
	DISP_PRIO_CHAR 		= 10,	// キャラ
	DISP_PRIO_PARTS 	= 10,	// 布ツ（建物等）
#ifdef PUK2
	DISP_PRIO_RESERVE 	= 20,	// マップエフェクトの描画タイミング(これは、StockDispBuffer 等では使わないで下さい)
#else
	DISP_PRIO_RESERVE 	= 20,
#endif
	DISP_PRIO_JIKI 		= 30,	/* 自机 	*/
	DISP_PRIO_GRID 		= 100,	// グリッドカーソル
	
	DISP_PRIO_B_EFFECT = 200,	// 战闘エフェクト
	DISP_PRIO_B_HIT_MAGIC,		// 攻击魔法エフェクト
	DISP_PRIO_B_EFFECT2,		// 战闘エフェクト
	DISP_PRIO_B_MISSILE,		// 战闘飞び道具
	DISP_PRIO_B_HIT_MARK,		// 战闘エフェクト
	DISP_PRIO_B_EFFECT3,		// 战闘エフェクト
	
	DISP_PRIO_BOX,				/* ボックス */
	DISP_PRIO_METER,			/* メーター */
	DISP_PRIO_METER2,			/* メーター２ */
	DISP_PRIO_CNT_DOWN,			// カウントダウン
								/* フォントバック */
	DISP_PRIO_IME1,				/* ＩＭＥボックス（バック用　黒色） */
	DISP_PRIO_IME2,				/* ＩＭＥボックス（バック用　青色） */
								/* フォントバック２ */
	DISP_PRIO_MENU,				/* メニュー */
	DISP_PRIO_ITEM2,			/* アイテム２ */
	DISP_PRIO_BOXFILL,
	DISP_PRIO_BOXFILL2,
								/* フォントフロント */
	DISP_PRIO_IME3,				/* ＩＭＥボックス（フロント用　黒色） */
	DISP_PRIO_IME4,				/* ＩＭＥボックス（フロント用　青色） */
	DISP_PRIO_BOX2,				/* ボックス２ */
	DISP_PRIO_TASK_BAR,			// タスクバー
	DISP_PRIO_TASK_BAR_WAKU,	// タスクバーの选择枠
	DISP_PRIO_ITEM,				/* アイテム */
	DISP_PRIO_WIN,				// ウィンドウ
	DISP_PRIO_WIN2,				// ウィンドウ２
#ifdef PUK2
	DISP_PRIO_NEW_IME1,			// PUK2用の赤
	DISP_PRIO_NEW_IME2,			// PUK2用の青
#endif
	DISP_PRIO_YES_NO_WND,		/* 确认ウィンドウ */
	DISP_PRIO_YES_NO_BTN,		/* 确认ボタン */
	DISP_PRIO_BOX3,				/* ボックス３ */
	DISP_PRIO_BOX4,				/* ボックス３ */
	DISP_PRIO_DRAG,				/* ドラッグ中 */
	DISP_PRIO_MOUSE,			/* マウスカーソル 	*/
	DISP_PRIO_TOP = 255			/* 最优先 	*/
};

#ifdef PUK2

enum{
	DITYPE_NOTBLT = -1,			// 描画无し
	DITYPE_BMP_NOMAL,			// 絵の描画(ＵＶ值无し)
	DITYPE_BMP_PUK2,			// 絵の描画(ＵＶ值有り)
	DITYPE_PAINT,				// 涂りつぶし
	DITYPE_PAINT_ALPHA,			// 涂りつぶし(半透明)
	DITYPE_TEXT,				// 文字描画
	DITYPE_AUTOMAP,				// オートマップ描画
	DITYPE_PUT_TILE,			// タイルの描画
	DITYPE_GET_TILE,			// 假サーフェースにマップタイルの描画
	DITYPE_SEA,					// 海タイル描画
	DITYPE_MAPEFFECT,			// オートマップ描画
};

#endif

// 表示情报构造体
typedef struct {
	int x, y;			// 表示座标
	int bmpNo;			// ＢＭＰ番号( ボックス表示の时は色番号 )
#ifdef PUK2
	union{
		ACTION *pAct;		// アクションポインタ
		void *pFnt;			// フォントポインタ
	};
#else
	ACTION *pAct;		// アクションポインタ
#endif
	BOOL hitFlag;		// 当たり判定するかフラグ　０：しない、１：する、２：ＢＯＸ表示

#ifdef PUK2
	struct BLT_MEMBER bm;	// 绘图设定
#endif
#ifdef PUK2
	char type;			// 描画の种类
#endif
}DISP_INFO;

// 表示优先ソート用构造体
typedef struct {
	short no;			// バッファ格纳时の番号
	UCHAR	dispPrio; 	// 表示の优先顺位
}DISP_SORT;

// 表示バッファ构造体
typedef struct{
	DISP_INFO 	DispInfo[ DISP_BUFFER_SIZE ];
	DISP_SORT 	DispSort[ DISP_BUFFER_SIZE ];
	short 		DispCnt;	// 现在の格纳数
}DISP_BUFFER;

// 表示バッファ构造体
extern DISP_BUFFER 	DispBuffer;

// ＢＭＰ読み込み用ワーク领域へのポインタ
extern LPBITMAPINFO lpBmpInfo;
// ＢＭＰのイメージデータまでのOFFセット
extern int BmpOffBits;

// Realbin 読み込み用ワーク领域へのポインタ
extern char *pRealBinBits;
// 今回 Realbin から読み込むＢＭＰのサイズ
extern int RealBinWidth, RealBinHeight;
#ifdef PUK2
// ＢＭＰのパレットのサイズ
extern int RealBinPalSize;
#endif

// BitBltの时
extern HBITMAP	hBmp;


// サーフェスビジーフラグ
extern int SurfaceBusyFlag;

// 表示バッファソート ///////////////////////////////////////////////////////////
void SortDispBuffer( void );

/* キャラクターの描画 **************************************************/
void PutBmp( void );

#ifdef PUK2

// 表示データをバッファに溜める ///////////////////////////////////////////////////
int StockDispBuffer( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, struct BLT_MEMBER *bm=NULL );
int StockDispBufferEx( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, struct BLT_MEMBER *bm=NULL );

// 表示データをバッファに溜める ///////////////////////////////////////////////////
// ただし中でrealGetNo()を呼ばない
int StockDispBuffer2( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, struct BLT_MEMBER *bm=NULL );

// 表示データをバッファに溜める(ｕ、ｖ值使用时はこちらを使用) ///////////////////////////////////////////////////
int StockDispBuffer_PUK2( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag, char use, struct BLT_MEMBER *bm );

// アルファブレンドの描画指令をバッファに溜める ++++
void StockAlphaDispBuffer( short lx, short ly, short lw, short lh, UCHAR dispPrio, unsigned long rgba );

#else

// 表示データをバッファに溜める ///////////////////////////////////////////////////
int StockDispBuffer( int x, int y, UCHAR prio, int bmpNo, BOOL hitFlag );
int StockDispBufferEx( int x, int y, UCHAR dispPrio, int bmpNo, BOOL hitFlag );

// 表示データをバッファに溜める ///////////////////////////////////////////////////
// ただし中でrealGetNo()を呼ばない
int StockDispBuffer2( int x, int y, UCHAR prio, int bmpNo, BOOL hitFlag );

#endif

// タスク表示データをバッファに溜める ///////////////////////////////////////////////////
void StockTaskDispBuffer( void );

// タスク表示データをバッファに溜める ///////////////////////////////////////////////////
// ただしprio1からprio2までのものは处理しない
void StockTaskDispBuffer2( int prio1, int prio2 );

// ボックス表示データをバッファに溜める ***************************************/
void StockBoxDispBuffer( int x1, int y1, int x2, int y2, UCHAR dispPrio, int color, BOOL fill );

#ifdef PUK2
	// 	文字列描画タイミングをバッファに溜める ++++
	int StockTextDispBuffer( void *FontBuffer, UCHAR dispPrio );
	// 	オートマップ描画タイミングをバッファに溜める ++++
	int StockAutoMapDispBuffer( int x, int y, UCHAR dispPrio );
#endif

#endif
