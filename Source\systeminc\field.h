﻿#ifndef _FIELD_H_ 
#define _FIELD_H_

#include<time.h>

// 连射した时に受け付ける间隔
#define FIELD_BTN_PUSH_WAIT				500		// 0.5秒

// フィールドランプの点灭间隔
#define FIELD_LAMP_FLASH_TIME			1000	// 1秒间隔

enum
{
	FIELD_FUNC_MENU,
	FIELD_FUNC_CARD,
	FIELD_FUNC_PARTY,
	FIELD_FUNC_JOIN_BATTLE,
	FIELD_FUNC_DUEL,
	FIELD_FUNC_ACT,
	FIELD_FUNC_END
};

extern int onCursorFieldWin;
extern int onCursorFieldInfoWin;


extern short etcSendFlag;

extern short actionBtnProcNo;
extern short mailLamp;

extern unsigned int fieldBtnPushTime;
extern unsigned int fieldInfoTime;

extern struct tm *serverAliveTime;
extern time_t serverAliveLongTime;


void initFieldProc( void );
void resetFieldProc( void );
void fieldProc( void );
void fieldProc2( void );
void drawField( void );


int disconnectServer( void );

void drawFieldInfoWin( void );


void duelBtnProc( void );
void shortCutFncDuel( void );
void duelBtnDraw( void );

void spectateBtnProc( void );
void shortCutFncSpectate( void );
void spectateBtnDraw( void );

void partyBtnProc( void );
void shortCutFncParty( void );
void partyBtnDraw( void );

void cardBtnProc( void );
void shortCutFncCard( void );
void cardBtnDraw( void );

void tradeBtnProc( void );
void shortCutFncTrade( void );
void tradeBtnDraw( void );

#ifdef PUK2
	void guildBtnProc( void );
	void shortCutFncGuild( void );
	void guildBtnDraw( void );
#endif

void actionBtnProc( void );
void actionBtnDraw( void );

void fontSizeBtnProc( void );
void fontSizeBtnDraw( void );

void mailLampProc( void );
void mailLampDraw( void );
void levelUpLampProc( void );
void levelUpLampDraw( void );
void helthLampProc( void );
void helthLampDraw( void );
void fieldLampFlashProc( void );

void duelOkLampProc( void );
void shortCutFncDuelFlagChange( void );
void duelOkLampInfoDraw( void );
void chatModeLampProc( void );
void shortCutFncChatFlagChange( void );
void chatModeLampInfoDraw( void );
void groupOkLampProc( void );
void shortCutFncGroupFlagChange( void );
void groupOkLampInfoDraw( void );
void meishiOkLampProc( void );
void shortCutFncMeishiFlagChange( void );
void meishiOkLampInfoDraw( void );
void tradeOkLampProc( void );
void shortCutFncTradeFlagChange( void );
void tradeOkLampInfoDraw( void );

#ifdef PUK2
	void guildOkLampProc( void );
	void shortCutFncGuildFlagChange( void );
	void guildOkLampInfoDraw( void );
#endif

#endif
