﻿//ＢＢＳ（揭示板）
#ifndef _MENUBBS_H_
#define _MENUBBS_H_

void InitMenuWindowBBSInLogin(void);
BOOL MenuWindowBBS( int mouse );
BOOL MenuWindowBBSDraw( int mouse );
BOOL MenuSwitchBBS( int no, unsigned int flag );
BOOL MenuSwitchBBSDialog( int no, unsigned int flag );
void openMessageNewBoardMenu( int , char *);
BOOL MenuWindowBBSDel( void );


GRAPHIC_SWITCH MenuWindowBBSGraph[]={
	{0             ,0,0,0,0,0xFFFFFFFF},		//颜
	{GID_BBSWindow,0,0,0,0,0xFFFFFFFF},			//ウインドウ
	{GID_BBSBack,0,0,0,0,0x80FFFFFF},			//バック
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズ
	{GID_TitleDeleteOn,0,0,0,0,0xFFFFFFFF},		//デリートボタン
	{GID_BBSOKOn,0,0,0,0,0xFFFFFFFF},			//ＯＫボタン
	{GID_BBSCancelOn,0,0,0,0,0xFFFFFFFF},		//キャンセルボタン

	{GID_BBSNextOn,0,0,0,0,0xFFFFFFFF},			//ネクストボタン
	{GID_BBSBackOn,0,0,0,0,0xFFFFFFFF},			//バックボタン
};



TEXT_SWITCH MenuWindowBBSText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"留言板文字列"},	
	{FONT_PAL_WHITE,FONT_KIND_CHAT_M,"名称"},	
	{FONT_PAL_WHITE,FONT_KIND_CHAT_M,"时间"},	
};

// マップスイッチ
static SWITCH_DATA BBSSwitch[] = {
{ SWITCH_GRAPHIC,593,  8,  12, 12, TRUE, &MenuWindowBBSGraph[ 3], MenuSwitchBBS },	//クローズ

{ SWITCH_DIALOG,  27,362,503, 42, TRUE, NULL,	 MenuSwitchBBSDialog },				//ダイアログ表示

{ SWITCH_GRAPHIC, 22,  38,   0,  0, TRUE, &MenuWindowBBSGraph[ 0], MenuSwitchNone },			//颜
{ SWITCH_TEXT,	 135,  46,   0,  0, TRUE, &MenuWindowBBSText[1], MenuSwitchNone },				//名称
{ SWITCH_TEXT,	 330,  46,   0,  0, TRUE, &MenuWindowBBSText[2], MenuSwitchNone },				//タイム
{ SWITCH_GRAPHIC,530,  46,  49, 17, TRUE, &MenuWindowBBSGraph[ 4], MenuSwitchBBS },				//デリートボタン
{ SWITCH_TEXT,	 100, 73+16*0,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ00/00
{ SWITCH_TEXT,	 100, 73+16*1,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ00/01
{ SWITCH_TEXT,	 100, 73+16*2,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ00/02

{ SWITCH_GRAPHIC, 22,114,  0,  0, TRUE, &MenuWindowBBSGraph[ 0], MenuSwitchNone },				//颜
{ SWITCH_TEXT,	 135,122,  0,  0, TRUE, &MenuWindowBBSText[1], MenuSwitchNone },				//名称
{ SWITCH_TEXT,	 330,122,  0,  0, TRUE, &MenuWindowBBSText[2], MenuSwitchNone },				//タイム
{ SWITCH_GRAPHIC,530,122,  49, 17, TRUE, &MenuWindowBBSGraph[ 4], MenuSwitchBBS },				//デリートボタン
{ SWITCH_TEXT,	 100,150+16*0,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ01/00
{ SWITCH_TEXT,	 100,150+16*1,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ01/01
{ SWITCH_TEXT,	 100,150+16*2,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ01/02

{ SWITCH_GRAPHIC, 22,190,  0,  0, TRUE, &MenuWindowBBSGraph[ 0], MenuSwitchNone },				//颜
{ SWITCH_TEXT,	 135,198,  0,  0, TRUE, &MenuWindowBBSText[1], MenuSwitchNone },				//名称
{ SWITCH_TEXT,	 330,198,  0,  0, TRUE, &MenuWindowBBSText[2], MenuSwitchNone },				//タイム
{ SWITCH_GRAPHIC,530,198, 49, 17, TRUE, &MenuWindowBBSGraph[ 4], MenuSwitchBBS },				//デリートボタン
{ SWITCH_TEXT,	 100,226+16*0,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ02/00
{ SWITCH_TEXT,	 100,226+16*1,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ02/01
{ SWITCH_TEXT,	 100,226+16*2,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ02/02

{ SWITCH_GRAPHIC, 22,266,   0,  0, TRUE, &MenuWindowBBSGraph[ 0], MenuSwitchNone },				//颜
{ SWITCH_TEXT,	 135,274,  0,  0, TRUE, &MenuWindowBBSText[1], MenuSwitchNone },				//名称
{ SWITCH_TEXT,	 330,274,  0,  0, TRUE, &MenuWindowBBSText[2], MenuSwitchNone },				//タイム
{ SWITCH_GRAPHIC,530,274, 49, 17, TRUE, &MenuWindowBBSGraph[ 4], MenuSwitchBBS },				//デリートボタン
{ SWITCH_TEXT,	 100,302+16*0,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ03/00
{ SWITCH_TEXT,	 100,302+16*1,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ03/01
{ SWITCH_TEXT,	 100,302+16*2,  0,  0, TRUE, &MenuWindowBBSText[0], MenuSwitchNone },			//ＢＢＳ03/02

{ SWITCH_GRAPHIC,538,364,  49, 17, TRUE, &MenuWindowBBSGraph[ 5], MenuSwitchBBS },				//ＯＫボタン
{ SWITCH_GRAPHIC,538,386,  49, 17, TRUE, &MenuWindowBBSGraph[ 6], MenuSwitchBBS },				//キャンセルボタン

{ SWITCH_GRAPHIC,576,342,  17, 17, TRUE, &MenuWindowBBSGraph[ 7], MenuSwitchBBS },				//ネクストボタン
{ SWITCH_GRAPHIC,536,342,  17, 17, TRUE, &MenuWindowBBSGraph[ 8], MenuSwitchBBS },				//バックボタン

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowBBSGraph[ 1], MenuSwitchNone },			//ウインドウ
{ SWITCH_GRAPHIC, 15, 30,   0,  0, TRUE, &MenuWindowBBSGraph[ 2], MenuSwitchNone },			//バック

};

enum{
	EnumGraphBBSClose,

	EnumBBSDialogInput,

	EnumGraphBBSFace00,
	EnumTextBBSName00,
	EnumTextBBSTime00,
	EnumGraphBBSDelete00,
	EnumTextBBS00_00,
	EnumTextBBS00_01,
	EnumTextBBS00_02,

	EnumGraphBBSFace01,
	EnumTextBBSName01,
	EnumTextBBSTime01,
	EnumGraphBBSDelete01,
	EnumTextBBS01_00,
	EnumTextBBS01_01,
	EnumTextBBS01_02,

	EnumGraphBBSFace02,
	EnumTextBBSName02,
	EnumTextBBSTime02,
	EnumGraphBBSDelete02,
	EnumTextBBS02_00,
	EnumTextBBS02_01,
	EnumTextBBS02_02,

	EnumGraphBBSFace03,
	EnumTextBBSName03,
	EnumTextBBSTime03,
	EnumGraphBBSDelete03,
	EnumTextBBS03_00,
	EnumTextBBS03_01,
	EnumTextBBS03_02,

	EnumGraphBBSOK,
	EnumGraphBBSCancel,

	EnumGraphBBSNextBt,
	EnumGraphBBSBackBt,

	EnumGraphBBSWindow,
	EnumGraphBBSBack,
	
	EnumBBSEnd,
};


const WINDOW_DATA WindowDataBBS = {
 0,															// メニューウィンドウ
     4,5,20,634,424,0x80010101,EnumBBSEnd,BBSSwitch, MenuWindowBBS,MenuWindowBBSDraw,MenuWindowBBSDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateBBS={
	2,
	 0,  0, 634, 30,
	604,  0, 30, 130,
};

#endif