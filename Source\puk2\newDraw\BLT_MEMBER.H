﻿/***************************************
			BLT_MEMBER.H
***************************************/

#ifndef _BLT_MEMBER_H_
#define _BLT_MEMBER_H_

union RGBA_DATA{
	struct{
		unsigned char b,g,r,a;			// 色要素
	};
	unsigned long rgba;					// Direct3D や 关数用
};

struct BLT_MEMBER{
	short u,v;							// 切り取り元の座标
	short w,h;							// 切り取る大きさ
	union RGBA_DATA rgba;				// 描画时の色、アルファ值の指定
	char BltVer;						// 新旧マップの区别用
	unsigned char bltf;					// 描画フラグ
	short PalNo;						// パレット番号(SprPal)
};

#define BLTVER_NOMAL	0
#define BLTVER_PUK2		1

#define BLTF_MRR_X	0x1
#define BLTF_MRR_Y	0x2
#define BLTF_NOCHG	0x4
#define BLTF_ADDBLT	0x8

#define BLTF_3DBLTTYPE	(BLTF_ADDBLT)

#endif