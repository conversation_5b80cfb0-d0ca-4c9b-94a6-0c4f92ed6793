﻿//メニュー＞マップ

//=========================================
// 饵やりウィンドウ
//=========================================

extern int itemInfoColor;

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

// 共通ウィンドウ生成(ウィンドウがすでにある场合は、以前のものを书き换え) ++++
WINDOW_INFO *createGuilMonFoodWindow( int WinType, int SeqNo, int ObjIndex, char *data )
{
	struct GUILMONFOODWINDOWMASTER *wm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];

	// ウィドウ种类取得
	wm->WinType=WinType;

	wm->wininfo = createMenuWindow( MENU_WINDOW_GUILMONFOOD );

	// サーバ情报保存
	wm->SeqNo=SeqNo,	wm->ObjIndex=ObjIndex;

	return wm->wininfo;
}

// ウィンドウ破弃 ++++
BOOL closeGuilMonFoodWindow()
{
	struct GUILMONFOODWINDOWMASTER *wm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];

	if ( serverRequestWinWindowType == WINDOW_MESSAGETYPE_FOODBOX ) serverRequestWinWindowType = -1;

	// 电卓ウィンドウを呼び出したのが自分の场合、电卓ウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_CALCULATOR].wininfo){
		struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
		if (wm->WinType == MENU_WINDOW_BANK){
			wm->wininfo->flag |= WIN_INFO_DEL;
		}
	}

	// キー入力のフォーカスを元に戾す
	if( checkInputFocus( NULL )  ) GetKeyInputFocus( &MyChatBuffer );

	return TRUE;
}

BOOL MenuWindowGuilMonFoodBf( int mouse )
{
	struct GUILMONFOODWINDOWMASTER *wm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if ( checkMoveMapGridPos( 1, 1 ) ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	if ( serverRequestWinWindowType != WINDOW_MESSAGETYPE_FOODBOX ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
	wm->itemInfoNo = -1;

	return TRUE;
}

BOOL MenuWindowGuilMonFoodAf( int mouse )
{
	struct GUILMONFOODWINDOWMASTER *wm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];
	int x = wI->wx + 35;
	int y = wI->wy + 313;

	// アイテム信息
	if (wm->itemInfoNo>=0){
		// １００未满は手持ち
		if (wm->itemInfoNo<100) PcItemExplanationWindow( wm->itemInfoNo, wm->itemInfoPage );
		else BankItemExplanationWindow( wm->itemInfoNo-100, wm->itemInfoPage );
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------
BOOL MenuGuilMonFoodCloseButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	if ( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuGuilMonFoodOKButton( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_FOODBOX_OK );

	if ( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	Graph->graNo = GID_BigOKButtonOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;

	return ReturnFlag;
}

BOOL MenuGuilMonFoodMyItemPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct GUILMONFOODWINDOWMASTER *wm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[10];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		// 手持ちの入れ替えなら
		if (DropitemNo<100){
			itemNo = -1;
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				x = wI->wx + wI->sw[no].ofx + 12 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = wI->wy + wI->sw[no].ofy + 39 + ( (i/ITEM_DRAW_COLUMN) * 52 );
				// 四角のあたり判定
				if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
					itemNo = i;
					break;
				}
			}
			if ( itemNo >= 0 ){
				// 掴んだアイテム位置と违うならプロトコル送信
				if( DropitemNo != itemNo+MAX_EQUIP_ITEM ) ItemMove( MENU_WINDOW_GUILMONFOOD, no, DropitemNo, itemNo+MAX_EQUIP_ITEM );
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			x = wI->wx + wI->sw[no].ofx + 12 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 39 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	//重なってたら一行インフォ表示
	if ( flag & MENU_MOUSE_OVER ){
		if (itemNo>=0) strcpy( OneLineInfoStr, MWONELINE_FOODBOX_MYITEM_ON );
	}

#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				itemNo = -1;
			}
		}
	}
#else
	if ( ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				itemNo = -1;
			}
		}
	}
#endif

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) wm->itemInfoPage = 0;
	olditemNo = itemNo;

#ifdef PUK3_FOODBOX
	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
#else
	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (pc.item[itemNo+MAX_EQUIP_ITEM].useFlag) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_GUILMONFOOD || WinDD_WinType()==MENU_WINDOW_NONE ){
			WinDD_DragFinish();
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				// アイテムがあるなら次へ
				if( bank[0].item[i].useFlag ) continue;
				break;
			}
			// 移动プロトコル送信
			if ( i < MAX_DRAW_WIN_ITEM ){
				if ( itemNo+MAX_EQUIP_ITEM<100 && ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) );
				else if ( pc.item[itemNo+MAX_EQUIP_ITEM].kind == ITEM_JEWEL || pc.item[itemNo+MAX_EQUIP_ITEM].kind == ITEM_FECES );
				else ItemMove( MENU_WINDOW_GUILMONFOOD, no, itemNo+MAX_EQUIP_ITEM, i+100 );
			}
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
#endif
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragItem( itemNo+MAX_EQUIP_ITEM, TRUE );
#else
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+MAX_EQUIP_ITEM) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= pc.item[itemNo+MAX_EQUIP_ITEM].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_GUILMONFOOD ){
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				DragitemNo = (int)WinDD_ObjData();
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_GUILMONFOOD ){
			if ( WinDD_ButtonNo()==no ){
				// 掴んだアイテムの表示
				bm.rgba.rgba=0x80ffffff;
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm );
			}
		}
#endif

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo >= MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + 12 + ( ( (DragitemNo-MAX_EQUIP_ITEM)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 39 + ( ( (DragitemNo-MAX_EQUIP_ITEM)/ITEM_DRAW_COLUMN ) * 52 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( itemNo >= 0 ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx + 12 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 39 + ( (itemNo/ITEM_DRAW_COLUMN) * 52 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ) wm->itemInfoNo = itemNo+MAX_EQUIP_ITEM;
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	bm.rgba.rgba=0xffffffff;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 36 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 64 + ( (i/ITEM_DRAW_COLUMN) * 52 );

			// 选择不可能なアイテムに禁止マークをつける
			if ( pc.item[i+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );
			}
			else
			if ( pc.item[i+MAX_EQUIP_ITEM].kind == ITEM_JEWEL ||
				 pc.item[i+MAX_EQUIP_ITEM].kind == ITEM_FECES ||
				 pc.item[i+MAX_EQUIP_ITEM].kind == ITEM_MISC ){
				StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );
			}
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

			StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

			// 个数表示
			if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
				sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );             //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

BOOL MenuGuilMonFoodGiveItemPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct GUILMONFOODWINDOWMASTER *wm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];
	BLT_MEMBER bm={0};
	char str[10];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( (WinDD_CheckObjType()!=WINDD_ITEM) && (WinDD_CheckObjType()!=WINDD_GUILMONFOOD) ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}else
		// 装备品はだめ
		if ( (int)WinDD_ObjData() < MAX_EQUIP_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<20;i++){
			x = wI->wx + wI->sw[no].ofx + 12 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 39 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo+100 ){
				if ( DropitemNo<100 && ( pc.item[DropitemNo].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) );
				else if ( pc.item[DropitemNo].kind == ITEM_JEWEL || pc.item[DropitemNo].kind == ITEM_FECES );
				else ItemMove( MENU_WINDOW_GUILMONFOOD, no, DropitemNo, itemNo+100 );
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			x = wI->wx + wI->sw[no].ofx + 12 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 39 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}
#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		DropitemNo = (int)WinDD_ObjData();
		if ( DropitemNo<100 && ( pc.item[DropitemNo].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) ){
			itemNo = -1;
		}
	}
#else
	if ( flag & MENU_MOUSE_DRAGOVER ){
		DropitemNo = (int)WinDD_ObjData();
		if ( DropitemNo<100 && ( pc.item[DropitemNo].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ) ){
			itemNo = -1;
		}
	}
#endif

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) wm->itemInfoPage = 0;
	olditemNo = itemNo;

	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( bank[0].item[itemNo].useFlag ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragItem( itemNo+100, TRUE );
#else
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+100) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( bank[0].item[itemNo].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= bank[0].item[itemNo].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( (WinDD_CheckObjType()==WINDD_ITEM) || (WinDD_CheckObjType()==WINDD_GUILMONFOOD) ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_GUILMONFOOD ){
			// 银行侧のアイテムなら
			if ( (int)WinDD_ObjData() >= 100 ){
				// 右键したらアイテムドロップ
				if ( mouse.onceState & MOUSE_LEFT_CRICK ){
					DragitemNo = (int)WinDD_ObjData();
					WinDD_DragFinish();
					WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
				}
				// 右クリックしたらドラッグ終了
				if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
			}
		}
	}
#endif
	if ( (WinDD_CheckObjType()==WINDD_ITEM) || (WinDD_CheckObjType()==WINDD_GUILMONFOOD) ){
		DragitemNo = (int)WinDD_ObjData();
		// 银行侧のアイテムなら
		if ( DragitemNo >= 100 ){
			DragitemNo-=100;

#ifdef PUK2_NEWDRAG
#else
			// ドラッグ元が自分なら
			if ( WinDD_WinType()==MENU_WINDOW_GUILMONFOOD ){
				if ( WinDD_ButtonNo()==no ){
					// 掴んだアイテムの表示
					bm.rgba.rgba=0x80ffffff;
					StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, bank[0].item[DragitemNo].graNo, 0, &bm );
				}
			}
#endif

			// アイテムを掴んだ位置に枠表示
			x = wI->wx + wI->sw[no].ofx + 12 + ( ( (DragitemNo)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 39 + ( ( (DragitemNo)/ITEM_DRAW_COLUMN ) * 52 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( itemNo >= 0 ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx + 12 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 39 + ( (itemNo/ITEM_DRAW_COLUMN) * 52 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		if ( bank[0].item[itemNo].useFlag ) wm->itemInfoNo = itemNo+100;
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	bm.rgba.rgba=0xffffffff;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムがあるなら表示
		if( bank[0].item[i].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 36 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 64 + ( (i/ITEM_DRAW_COLUMN) * 52 );

			StockDispBuffer( x, y, DISP_PRIO_WIN2, bank[0].item[i].graNo, 0, &bm );

			// 个数表示
			if( bank[0].item[i].num > 0 ){
				sprintf( str, "%3d", bank[0].item[i].num );                       //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

//------------------------------------------------------------------------//
// ウィンドウオープンアニメーション(饵やりウィンドウ用)					  //
//------------------------------------------------------------------------//
void openGuilMonFoodWindowAnim( ACTION *ptAct )
{
	WIN_DISP *ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	StockBoxDispBuffer( ptWinDisp->cx - ptWinDisp->nx,
						ptWinDisp->cy - ptWinDisp->ny,
						ptWinDisp->cx + ptWinDisp->nx,
						ptWinDisp->cy + ptWinDisp->ny,
						DISP_PRIO_MENU, SYSTEM_PAL_BLACK, 0 );

	// 增分プラス
	ptWinDisp->nx += ptAct->dx;
	ptWinDisp->ny += ptAct->dy;

	// リミットチェック
	if( ptWinDisp->cnt >= WINDOW_CREATE_FRAME ){
		createGuilMonFoodWindow( ptWinDisp->type, ptAct->gx, ptAct->gy, (char *)ptAct->actNo );
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
		Lock3DChangeWindowCnt--;
#endif
		DeathAction( ptAct );
	}

	// カウンタープラス
	ptWinDisp->cnt++;
}

ACTION *openGuilMonFoodMenuWindow( int WinType, int SeqNo, int ObjIndex, char *data, unsigned char flg, char opentype )
{
	ACTION *ptAct;
	WIN_DISP *ptWinDisp;
	int x, y, w, h;

	// ウィンドウ开く音
	play_se( SE_NO_OPEN_WINDOW, 320, 240 );

	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ), ACT_T_WIN_DISP );
#else
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ) );
#endif

	// アクション取得に失败したら終わる
	if( ptAct == NULL ) {
		createGuilMonFoodWindow( WinType, SeqNo, ObjIndex, data );
		return NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	Lock3DChangeWindowCnt++;
#endif

	// ウィンドウの场所、サイズを取得
	x = WindowData[ MENU_WINDOW_GUILMONFOOD ]->wx,	y = WindowData[ MENU_WINDOW_GUILMONFOOD ]->wy;
	w = WindowData[ MENU_WINDOW_GUILMONFOOD ]->w,	h = WindowData[ MENU_WINDOW_GUILMONFOOD ]->h;

	// 以前开いたときの状态を、作成されたウィンドウに反映
	if( WindowFlag[ MENU_WINDOW_GUILMONFOOD ].flag & 0x01 ){
		x = WindowFlag[ MENU_WINDOW_GUILMONFOOD ].wx;
		y = WindowFlag[ MENU_WINDOW_GUILMONFOOD ].wy;
	}

	// 予备构造体へのポインタ
	ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	// 实行关数
	ptAct->func = openGuilMonFoodWindowAnim;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_WIN2;
	// 当たり判定する
	ptAct->atr |= ACT_ATR_HIT;
	// 表示しない
	ptAct->atr |= ACT_ATR_HIDE;

	ptWinDisp->graNo = 0;
	ptWinDisp->hitFlag = 0;
	if( flg & OPENMENUWINDOW_HIT ) ptWinDisp->hitFlag = 1;

	// 座标を记忆
	ptAct->x = x,	ptAct->y = y;
	ptWinDisp->w = w,	ptWinDisp->h = h;

	// ウィンドウ种类
	ptWinDisp->type = WinType;

	// 文字列へのポインタ记忆
	ptAct->actNo = (int)data;

	// サーバ关系情报保存
	ptAct->gx = SeqNo;
	ptAct->gy = ObjIndex;

	// カウンタ
	ptWinDisp->cnt = 0;

	// ＮＲバージョンエフェクト
	if( opentype == 0 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = 0;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;

		ptWinDisp->nx = ptWinDisp->cx - x;
	}
	// ＳＡバージョンエフェクト
	if( opentype == 1 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = (ptWinDisp->cx - x) / WINDOW_CREATE_FRAME;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;
	}

	return ptAct;
}

//=========================================
// 公会宠物状态ウィンドウ
//=========================================

extern char ItemNoOpe[MAX_ITEM];

static int GMS_GuilMonFood = -1;					// アイテム栏に乘ってるアイテム

static int GMS_SeqNo;								// サーバの管理番号
static int GMS_ObjIndex;							// 现在の处理サーバの管理番号
static char GMS_Name[50];							// 公会宠物の名称
static char **GMS_Status = NULL;					// 公会宠物の状态
static int GMS_StatusNum = 0;

static int GMS_TextStart;

char GMS_InputStrUseFlag;

INPUT_STR GuilMonNameInputStr;

INIT_STR_STRUCT InitStrStructGuilMonName={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	&GuilMonNameInputStr,  0,  0,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_WHITE,"",	  1,      16,  0,     0
};

BOOL GMS_Reportflag;

static char GMS_InfoStr[256];
//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

void swapGMStatusItemRelation( int from, int to )
{
	if ( !WindowFlag[MENU_WINDOW_GUILMONSTATUS].wininfo ) return;

	if ( GMS_GuilMonFood == from ) GMS_GuilMonFood = to;
}

void GMS_Status_Free()
{
	int i;

	if (!GMS_Status) return;

	for(i=0;i<GMS_StatusNum;i++){
#ifdef PUK2_MEMCHECK
		memlistrel( GMS_Status[i], MEMLISTTYPE_GuilmonStatusWindow );
#endif
		free( GMS_Status[i] );
	}

#ifdef PUK2_MEMCHECK
	memlistrel( GMS_Status, MEMLISTTYPE_GuilmonStatusWindow );
#endif
	free( GMS_Status );

	GMS_Status = 0;

	GMS_StatusNum = 0;
}

void GMS_TextDispMove( WINDOW_INFO *wi )
{
	int i;

	for(i=0;i<9;i++){
		if (GMS_TextStart+i<GMS_StatusNum) strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGuilMonStatusTxtStatus1+i].Switch )->text, GMS_Status[GMS_TextStart+i] );
		else strcpy( ( (TEXT_SWITCH *)wi->sw[EnumGuilMonStatusTxtStatus1+i].Switch )->text, "" );
	}
}

ACTION *openGuilMonStatusWindow( int SeqNo,	int ObjIndex, char *data )
{
	char str[1024];
	char *p, *bp, *ep;
	int i, j=1, k, len;

	GMS_SeqNo = SeqNo,	GMS_ObjIndex = ObjIndex;

	// 公会宠物の名称を取得
	if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) return NULL;
	makeRecvString(str);

	strcpy( GMS_Name, str );

	// 公会宠物の状态データを解放
	GMS_Status_Free();

	// 公会宠物の状态を取得
	getStringToken( data, '|', j++, sizeof(str) - 1 , str );
	makeRecvString(str);

	// 行数分の文字列配列を确保
	p = str;
	ep = str + strlen(str);
	for(GMS_StatusNum=0;p;GMS_StatusNum++){
		bp = p;
		p = strchr( bp, '\n' );
		if (!p) p = ep;
		if ( (int)p - (int)bp  > GMS_MAXLETTER ){
			GMS_StatusNum--;
			for(i=0;i<=(int)p-(int)bp;){
				// 最后のバイトが全角文字の２バイト目でないなら
				if ( CheckLetterType( bp+i, GMS_MAXLETTER )!=CHECKLETTERTYPE_FULL_TAIL ) i += GMS_MAXLETTER;
				// 最后のバイトが全角文字の２バイト目なら
				else i += GMS_MAXLETTER-1;
				GMS_StatusNum++;
			}
		}
		if ( p >= ep ){ GMS_StatusNum++;	break; }
		p++;
	}

	GMS_Status = (char **)calloc( GMS_StatusNum, sizeof(char *) );
#ifdef PUK2_MEMCHECK
	memlistset( GMS_Status, MEMLISTTYPE_GuilmonStatusWindow );
#endif

	// 状态データを作成
	bp = str;
	for(i=0;i<GMS_StatusNum;i++){
		p = strchr( bp, '\n' );

		if (p) *p = '\0';
		else p = ep;

		len = strlen(bp);
		j = 0;
		if (!len){
			GMS_Status[i] = (char *)malloc(1);
#ifdef PUK2_MEMCHECK
			memlistset( GMS_Status[i], MEMLISTTYPE_GuilmonStatusWindow );
#endif
			GMS_Status[i][0] = '\0';
		}
		for(k=0;k<len;){
			GMS_Status[i+j] = (char *)malloc(GMS_MAXLETTER+1);
#ifdef PUK2_MEMCHECK
			memlistset( GMS_Status[i+j], MEMLISTTYPE_GuilmonStatusWindow );
#endif
			if (len-k>GMS_MAXLETTER){
				// 最后のバイトが全角文字の２バイト目でないなら
				if ( CheckLetterType( bp+k, GMS_MAXLETTER )!=CHECKLETTERTYPE_FULL_TAIL ){
					memcpy( GMS_Status[i+j], bp + k, GMS_MAXLETTER );
					GMS_Status[i+j][GMS_MAXLETTER] = '\0';
					k += GMS_MAXLETTER;
				}
				// 最后のバイトが全角文字の２バイト目なら
				else{
					memcpy( GMS_Status[i+j], bp + k, GMS_MAXLETTER-1 );
					GMS_Status[i+j][GMS_MAXLETTER-1] = '\0';
					k += GMS_MAXLETTER-1;
				}
			}else{
				strcpy( GMS_Status[i+j], bp + k );
				k += GMS_MAXLETTER;
			}
			j++;
		}
		if (j>0) i += j-1;

		bp = p + 1;
	}

	return openMenuWindow( MENU_WINDOW_GUILMONSTATUS, OPENMENUWINDOW_HIT, 0 );
}

void remakeGuilMonStatusData( char *data )
{
	char str[1024];
	char *p, *bp, *ep;
	int i, j=1, k, len;

	// 公会宠物の名称を取得
	if( getStringToken( data, '|', j++, sizeof(str) - 1 , str ) != 0 ) return;
	makeRecvString(str);

	strcpy( GMS_Name, str );
	strcpy( ( (TEXT_SWITCH *)WindowFlag[MENU_WINDOW_GUILMONSTATUS].wininfo->sw[EnumGuilMonStatusTxtName].Switch )->text, GMS_Name );

	// 公会宠物の状态データを解放
	GMS_Status_Free();

	// 公会宠物の状态を取得
	getStringToken( data, '|', j++, sizeof(str) - 1 , str );
	makeRecvString(str);

	// 行数分の文字列配列を确保
	p = str;
	ep = str + strlen(str);
	for(GMS_StatusNum=0;p;GMS_StatusNum++){
		bp = p;
		p = strchr( bp, '\n' );
		if (!p) p = ep;
		if ( (int)p - (int)bp  > GMS_MAXLETTER ){
			GMS_StatusNum--;
			for(i=0;i<=(int)p-(int)bp;){
				// 最后のバイトが全角文字の２バイト目でないなら
				if ( CheckLetterType( bp+i, GMS_MAXLETTER )!=CHECKLETTERTYPE_FULL_TAIL ) i += GMS_MAXLETTER;
				// 最后のバイトが全角文字の２バイト目なら
				else i += GMS_MAXLETTER-1;
				GMS_StatusNum++;
			}
		}
		if ( p >= ep ){ GMS_StatusNum++;	break; }
		p++;
	}

	GMS_Status = (char **)calloc( GMS_StatusNum, sizeof(char *) );
#ifdef PUK2_MEMCHECK
	memlistset( GMS_Status, MEMLISTTYPE_GuilmonStatusWindow );
#endif

	// 状态データを作成
	bp = str;
	for(i=0;i<GMS_StatusNum;i++){
		p = strchr( bp, '\n' );

		if (p) *p = '\0';
		else p = ep;

		len = strlen(bp);
		j = 0;
		if (!len){
			GMS_Status[i] = (char *)malloc(1);
#ifdef PUK2_MEMCHECK
			memlistset( GMS_Status[i], MEMLISTTYPE_GuilmonStatusWindow );
#endif
			GMS_Status[i][0] = '\0';
		}
		for(k=0;k<len;){
			GMS_Status[i+j] = (char *)malloc(GMS_MAXLETTER+1);
#ifdef PUK2_MEMCHECK
			memlistset( GMS_Status[i+j], MEMLISTTYPE_GuilmonStatusWindow );
#endif
			if (len-k>GMS_MAXLETTER){
				// 最后のバイトが全角文字の２バイト目でないなら
				if ( CheckLetterType( bp+k, GMS_MAXLETTER )!=CHECKLETTERTYPE_FULL_TAIL ){
					memcpy( GMS_Status[i+j], bp + k, GMS_MAXLETTER );
					GMS_Status[i+j][GMS_MAXLETTER] = '\0';
					k += GMS_MAXLETTER;
				}
				// 最后のバイトが全角文字の２バイト目なら
				else{
					memcpy( GMS_Status[i+j], bp + k, GMS_MAXLETTER-1 );
					GMS_Status[i+j][GMS_MAXLETTER-1] = '\0';
					k += GMS_MAXLETTER-1;
				}
			}else{
				strcpy( GMS_Status[i+j], bp + k );
				k += GMS_MAXLETTER;
			}
			j++;
		}
		if (j>0) i += j-1;

		bp = p + 1;
	}

	if ( WindowFlag[MENU_WINDOW_GUILMONSTATUS].wininfo ){
		GMS_TextDispMove( WindowFlag[MENU_WINDOW_GUILMONSTATUS].wininfo );
	}
}

BOOL closeGuilMonStatusWindow()
{
	// 公会宠物の状态データ破弃
	GMS_Status_Free();

	if (GMS_GuilMonFood>=0){
		if (ItemNoOpe[GMS_GuilMonFood]>=0) ItemNoOpe[GMS_GuilMonFood]--;
		GMS_GuilMonFood = -1;
	}

	// サーバに終了を报告
	if (GMS_Reportflag) nrproto_WN_send( sockfd, mapGx, mapGy, GMS_SeqNo, GMS_ObjIndex, WINDOW_BUTTONTYPE_NONE, "C" ); //MLHIDE

	serverRequestWinWindowType = -1;

	return TRUE;
}

BOOL MenuWindowGuilMonStatusBf( int mouse )
{
	if ( mouse == WIN_INIT ){
		GMS_GuilMonFood = -1;

		GMS_TextStart = 0;

		wI->sw[EnumGuilMonStatusTxtNameHit].Enabled = TRUE;
		wI->sw[EnumGuilMonStatusTxtName].Enabled = TRUE;
		wI->sw[EnumGuilMonStatusTxtNameInput].Enabled = FALSE;

		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGuilMonStatusTxtName].Switch )->text, GMS_Name );
		GMS_TextDispMove(wI);

		wI->sw[EnumGuilMonStatusInfoWidnow].Enabled = FALSE;

		GMS_Reportflag = TRUE;
	}

	if ( serverRequestWinWindowType != WINDOW_MESSAGETYPE_GUILDMONSTER ){
		GMS_Reportflag = FALSE;
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	else if ( checkMoveMapGridPos( 1, 1 ) ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	if( pNowInputStr != &GuilMonNameInputStr ){
		wI->sw[EnumGuilMonStatusTxtNameHit].Enabled = TRUE;
		wI->sw[EnumGuilMonStatusTxtName].Enabled = TRUE;
		wI->sw[EnumGuilMonStatusTxtNameInput].Enabled = FALSE;
	}

	if ( guildBook.pcAuthority & GUILD_FLAG_FEED ){
		wI->sw[EnumGuilMonStatusGiveFood].Enabled = FALSE;
		if (GMS_GuilMonFood>=0) wI->sw[EnumGuilMonStatusGiveFood].Enabled = TRUE;
	}

	// 饵をあげる权限をもっているなら、饵あげを可能に
	wI->sw[EnumGuilMonStatusTxtNameHit].Enabled = FALSE;
	wI->sw[EnumGuilMonStatusRename].Enabled = FALSE;
	if ( guildBook.pcAuthority & GUILD_FLAG_MASTER ){
		wI->sw[EnumGuilMonStatusTxtNameHit].Enabled = TRUE;
		wI->sw[EnumGuilMonStatusRename].Enabled = TRUE;
	}

	// 饵をあげる权限をもっているなら、饵あげを可能に
	wI->sw[EnumGuilMonStatusGiveFood].Enabled = FALSE;
	if ( guildBook.pcAuthority & GUILD_FLAG_FEED ){
		if (GMS_GuilMonFood>=0) wI->sw[EnumGuilMonStatusGiveFood].Enabled = TRUE;
	}

	// 公会宠物おやつなら、饵あげを可能に
	if (GMS_GuilMonFood>=0){
		if ( pc.item[GMS_GuilMonFood].kind == ITEM_GUILMONSNACK ){
			wI->sw[EnumGuilMonStatusGiveFood].Enabled = TRUE;
		}
	}

	return TRUE;
}

BOOL MenuWindowGuilMonStatusAf( int mouse )
{
	wI->sw[EnumGuilMonStatusGraScroll].Enabled = TRUE;
	if (GMS_StatusNum-9<=0) wI->sw[EnumGuilMonStatusGraScroll].Enabled = FALSE;
	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wI->sw[EnumGuilMonStatusBtScroll].Switch )->status&1 ){
		GMS_TextStart = ScrollVPointToNum( &wI->sw[EnumGuilMonStatusBtScroll], GMS_StatusNum-9 );
		GMS_TextDispMove(wI);
	}

	// 文字入力栏移动
	SetInputStr( &InitStrStructGuilMonName,
		wI->wx+wI->sw[EnumGuilMonStatusTxtNameInput].ofx, wI->wy+wI->sw[EnumGuilMonStatusTxtNameInput].ofy, 2 );

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuSwitchGuilMonStatusInfo( int no, unsigned int flag )
{
	const short x = 155, y = 155, w = 330, h = 110;
	const short okx = 132, oky = 63, okw = 66, okh = 17;
	int GraNo;
	struct BLT_MEMBER bm={0};
	char okflg = 0;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	if ( MakeHitBox( x+okx, y+oky, x+okx+okw, y+oky+okh, -1 ) ) okflg = 1;

	GraNo = GID_BigOKButtonOn;
	if (okflg){
		GraNo = GID_BigOKButtonOver;
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			wI->sw[no].Enabled = FALSE;

			// ウィンドウの移动可能に
			wI->flag &= ~MENU_ATTR_NOMOVE;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			GraNo = GID_BigOKButtonOff;
		}
	}
	StockDispBuffer( x+okx+33 , y+oky+9, DISP_PRIO_YES_NO_WND, GraNo, 0, &bm );

	StockFontBuffer( x+30, y+30, FONT_PRIO_FRONT2, FONT_KIND_MIDDLE, FONT_PAL_WHITE, GMS_InfoStr, 0, BoxColor );

	MenuWindowCommonDraw( GID_CommonWindow, x, y, w, h, DISP_PRIO_YES_NO_WND, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

BOOL MenuSwitchGuilMonStatusClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo=GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo=GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo=GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (GMS_TextStart>0){
			GMS_TextStart--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-1], GMS_StatusNum-9, GMS_TextStart );

		// 表示を更新
		GMS_TextDispMove(wI);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if ( GMS_TextStart<(GMS_StatusNum-9<0?0:GMS_StatusNum-9) ){
			GMS_TextStart++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-2], GMS_StatusNum-9, GMS_TextStart );

		// 表示を更新
		GMS_TextDispMove(wI);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (GMS_TextStart>0){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		GMS_TextStart-=9;
		if (GMS_TextStart<0) GMS_TextStart = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-3], GMS_StatusNum-9, GMS_TextStart );

		// 表示を更新
		GMS_TextDispMove(wI);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if ( (GMS_StatusNum-9>0) && (GMS_TextStart<GMS_StatusNum-9) ){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		GMS_TextStart+=9;
		if ( GMS_TextStart>(GMS_StatusNum-9<0?0:GMS_StatusNum-9) ) GMS_TextStart=(GMS_StatusNum-9<0?0:GMS_StatusNum-9);

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-4], GMS_StatusNum-9, GMS_TextStart );

		// 表示を更新
		GMS_TextDispMove(wI);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		GMS_TextStart = WheelToMove( &wI->sw[no-5],
			 GMS_TextStart, GMS_StatusNum-9, mouse.wheel );
	}

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusRename( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 名称变更中
	if ( wI->sw[EnumGuilMonStatusTxtNameInput].Enabled ){
		//重なってたら一行インフォ表示
		if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_GUILMONSTATUS_SET );

		//押されたとき
		if( (flag&MENU_MOUSE_LEFT) || keyOnOnce( VK_RETURN ) ){
			char msg[255];
			char msg2[255];

			// 入力を决定
			strcpy( GMS_Name, GuilMonNameInputStr.buffer );
			SetDialogMenuChat();

			// 表示内容を变更
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGuilMonStatusTxtName].Switch )->text, GMS_Name );

			wI->sw[EnumGuilMonStatusTxtNameInput].Enabled = FALSE;
			wI->sw[EnumGuilMonStatusTxtNameHit].Enabled = TRUE;
			wI->sw[EnumGuilMonStatusTxtName].Enabled = TRUE;

			strcpy( msg, GMS_Name );

			// ここは S-JIS から EUC に变换して信息をエスケープする。
			makeSendString( msg, msg2, sizeof( msg2 ) );

			sprintf( msg, "N|%s", msg2 );                                      //MLHIDE

			// すでに S-JIS から EUC に变换してあるので、信息のエスケープだけをする。
			makeEscapeString( (unsigned char *)msg, (unsigned char *)msg2, sizeof( msg2 ) );

			// 设定した名称をサーバに送信
			nrproto_WN_send( sockfd, mapGx, mapGy, GMS_SeqNo, GMS_ObjIndex, WINDOW_BUTTONTYPE_NONE, msg2 );

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			ReturnFlag = TRUE;
		}
	}else{
		if(flag&MENU_MOUSE_LEFT){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	Graph->graNo = GID_RenameButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RenameButtonOver;
	if( pNowInputStr != &GuilMonNameInputStr ) Graph->graNo = GID_RenameButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusGiveFood( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_GUILMONSTATUS_GIVE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		char msg[50];

		sprintf( msg, "I|%d", GMS_GuilMonFood );                            //MLHIDE
		// サーバに終了を报告
		nrproto_WN_send( sockfd, mapGx, mapGy, GMS_SeqNo, GMS_ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_GiveFoodButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_GiveFoodButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_GiveFoodButtonOff;
	if ( GMS_GuilMonFood < 0 ) Graph->graNo = GID_GiveFoodButtonOff;

	return ReturnFlag;
}

// モンスター名入力ダイアログ
BOOL MenuSwitchGuilMonStatusRenaming( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが重なっているなら枠表示
	displaySwitchFrame( &wI->sw[no], flag, BoxColor );

	if( flag & MENU_MOUSE_LEFT ){
		wI->sw[EnumGuilMonStatusTxtNameHit].Enabled = FALSE;
		wI->sw[EnumGuilMonStatusTxtName].Enabled = FALSE;
		wI->sw[EnumGuilMonStatusTxtNameInput].Enabled = TRUE;

		// ダイアログ初期化
		SetInputStr( &InitStrStructGuilMonName,
			wI->wx+wI->sw[EnumGuilMonStatusTxtNameInput].ofx, wI->wy+wI->sw[EnumGuilMonStatusTxtNameInput].ofy, 0 );

		// フォーカスを取る
		GetKeyInputFocus( &GuilMonNameInputStr );

		// 入力初期化
		StrToNowInputStr( GMS_Name );

		DiarogST.SwAdd = wI->sw[EnumGuilMonStatusTxtNameInput].Switch;
		( (DIALOG_SWITCH *)wI->sw[EnumGuilMonStatusTxtNameInput].Switch )->InpuStrAdd = &GuilMonNameInputStr;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	
		ReturnFlag = TRUE;
	}

	return ReturnFlag;
}

// 饵やりアイテム栏
BOOL MenuSwitchGuilMonStatusItem( int no, unsigned int flag )
{
	BOOL ReturnFlag = FALSE;
	short x = wI->wx+wI->sw[no].ofx;
	short y = wI->wy+wI->sw[no].ofy;
	int Dropitem;
	struct BLT_MEMBER bm = {0};
	struct BLT_MEMBER bm2 = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM && WinDD_CheckObjType() != WINDD_GUILMONSTATUS ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			Dropitem = (int)WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		Dropitem = (int)WinDD_ObjData();
#else
		Dropitem = (int)WinDD_GetObject();
#endif

		if ( Dropitem >= MAX_EQUIP_ITEM ){
			// 自分が饵をあげる权限をもっているか、公会宠物おやつなら受け取る
			if ( (guildBook.pcAuthority&GUILD_FLAG_FEED) || (pc.item[Dropitem].kind==ITEM_GUILMONSNACK) ){
/***
				// 与えられないアイテムははじく
				if ( pc.item[Dropitem].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
					strcpy( GMS_InfoStr, "无法喂这个饲料。" );
					// 确认ウィンドウ表示
					wI->sw[EnumGuilMonStatusInfoWidnow].Enabled = TRUE;
					// ウィンドウの移动不可能に
					wI->flag |= MENU_ATTR_NOMOVE;

  					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}else if ( pc.item[Dropitem].kind == ITEM_JEWEL || pc.item[Dropitem].kind == ITEM_FECES ){
					strcpy( GMS_InfoStr, "无法喂这个饲料。" );
					// 确认ウィンドウ表示
					wI->sw[EnumGuilMonStatusInfoWidnow].Enabled = TRUE;
					// ウィンドウの移动不可能に
					wI->flag |= MENU_ATTR_NOMOVE;

					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}else
***/
				{
					// すでに登録されているなら登録解除
					if (ItemNoOpe[GMS_GuilMonFood]>=0) ItemNoOpe[GMS_GuilMonFood]--;

					// アイテムを登録する
					GMS_GuilMonFood = Dropitem;
					ItemNoOpe[GMS_GuilMonFood]++;
				}
			}else{
				strcpy( GMS_InfoStr, ML_STRING(825, "没有权限为饲料。") );
				// 确认ウィンドウ表示
				wI->sw[EnumGuilMonStatusInfoWidnow].Enabled = TRUE;
				// ウィンドウの移动不可能に
				wI->flag |= MENU_ATTR_NOMOVE;

				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
#ifdef PUK2_NEWDRAG
			WinDD_AcceptObject();
#endif
		}
	}

	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// 右键したとき
		if( flag & MENU_MOUSE_LEFT ){
			// アイテムがあるなら
			if ( GMS_GuilMonFood >= 0 ){
				// ドラッグ开始
#ifdef PUK2_NEWDRAG
				DragItem( GMS_GuilMonFood, TRUE );
#else
				WinDD_DragStart( WINDD_ITEM, (void *)GMS_GuilMonFood );
#endif

				if (ItemNoOpe[GMS_GuilMonFood]>=0) ItemNoOpe[GMS_GuilMonFood]--;
				GMS_GuilMonFood = -1;
/***
				WinDD_DragStart( WINDD_GUILMONSTATUS, 0 );
***/
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}
			ReturnFlag=TRUE;
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM && WinDD_WinType()==MENU_WINDOW_GUILMONSTATUS ){
////	else if ( WinDD_CheckObjType()==WINDD_GUILMONSTATUS ){
		// 右键したらアイテムドロップ
		if ( mouse.onceState & MOUSE_LEFT_CRICK ){
			int DragitemNo = (int)WinDD_ObjData();
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
/***
			if ( !(flag & MENU_MOUSE_DRAGOVER) ){
				if (ItemNoOpe[GMS_GuilMonFood]>=0) ItemNoOpe[GMS_GuilMonFood]--;
				GMS_GuilMonFood = -1;
			}
***/
		}
		// 右クリックしたらドラッグ終了
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
	}
#endif

#ifdef PUK2_NEWDRAG
#else
////	if ( WinDD_CheckObjType()==WINDD_GUILMONSTATUS ){
	if ( WinDD_CheckObjType()==WINDD_ITEM && WinDD_WinType()==MENU_WINDOW_GUILMONSTATUS ){
		int DragitemNo = (int)WinDD_ObjData();
		// 掴んだアイテムの表示
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
/***
		// 掴んだアイテムの表示
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[GMS_GuilMonFood].graNo, 0, &bm2 );

		// アイテムを掴んだ位置に枠表示
		StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
***/
	}
#endif

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ) StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

	if (GMS_GuilMonFood>=0){
		// アイテムがあるなら表示
		if( pc.item[GMS_GuilMonFood].useFlag ){
			StockDispBuffer( x+24, y+24, DISP_PRIO_WIN2, pc.item[GMS_GuilMonFood].graNo, 0, &bm );
		}else{
			if (ItemNoOpe[GMS_GuilMonFood]>=0) ItemNoOpe[GMS_GuilMonFood]--;
			GMS_GuilMonFood = -1;
		}
	}

	return ReturnFlag;
}

BOOL MenuSwitchGuilMonStatusBase( int no, unsigned int flag )
{
	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER){
		if (OneLineInfoStr[0]=='\0') strcpy( OneLineInfoStr, MWONELINE_GUILMONSTATUS_NONE );
	}
	return FALSE;
}
