﻿/************************/
/*	sprmgr.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/main.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"


//#define SURACE_INFO_MAX 64 	// サーフェスインフォ构造体の最大数
//#define SURACE_INFO_MAX 128 	// サーフェスインフォ构造体の最大数
//#define SURACE_INFO_MAX 256 	// サーフェスインフォ构造体の最大数
//#define SURACE_INFO_MAX 512 	// サーフェスインフォ构造体の最大数
//#define SURACE_INFO_MAX 1024 	// サーフェスインフォ构造体の最大数
#ifndef PUK2
#define SURACE_INFO_MAX 2048 	// サーフェスインフォ构造体の最大数
#endif
//#define SURACE_INFO_MAX 4096 	// サーフェスインフォ构造体の最大数
#ifdef PUK2
#define SURACE_INFO_MAX 4096 	// サーフェスインフォ构造体の最大数
#endif

//#define VRAM_SYSRAM_POINT_ON 	// VRAM サーフェスと SYSRAM サーフェスを许可する
#define VRAM_SYSRAM_POINT 0 	// VRAM サーフェスと SYSRAM サーフェスの境目

#define SURACE_BMP_DEATH_DATE 2 // サーフェス上のＢＭＰの抹杀が许可されるまでの日数


int MAX_GRAPHICS = 0;	// MAXを变数にして变化させる

// サーフェスインフォ
SURFACE_INFO SurfaceInfo[ SURACE_INFO_MAX ];

// スプライトインフォ
SPRITE_INFO *SpriteInfo = NULL;


// バトルサーフェスのポインタ
#ifdef PUK2
LPDIRECTDRAWSURFACE7 lpBattleSurface; 	
#else
LPDIRECTDRAWSURFACE lpBattleSurface; 	
#endif

// サーフェスを使用した日付
UINT SurfaceDate = 3;

// 现在のサーフェス检索位置
int SurfaceSearchPoint;

#ifdef _DEBUG		
// 现在使っているサーフェスの数カウント
int SurfaceUseCnt;

// 现在表示しているサーフェスの数カウント
int SurfaceDispCnt;
#endif

// 作成したサーフェスの数カウント
int SurfaceCnt;

// VRAM に作成したサーフェスの数カウント
int VramSurfaceCnt;

// SYSTEMRAM に作成したサーフェスの数カウント
int SysramSurfaceCnt;

// 分割サーフェイスの幅
int SurfaceSizeX;
// 分割サーフェイスの高さ
int SurfaceSizeY;

#ifdef PUK2
	#include "../PUK2/newDraw/newsprmgr.cpp"
#endif

/* ＢＭＰをサーフェスへ割り当て ***********************************************/
void AllocateBmpToSurface( int bmpNo )
{
	//int bmpWidth  = lpBmpInfo->bmiHeader.biWidth;	// ＢＭＰの横サイズ
	//int bmpHeight = lpBmpInfo->bmiHeader.biHeight; 	// ＢＭＰの縦サイズ

	int bmpWidth  = RealBinWidth;	// ＢＭＰの横サイズ
	int bmpHeight = RealBinHeight; 	// ＢＭＰの縦サイズ
	
	int offsetX, offsetY; 			// 送るＢＭＰのOFFセットポイント
	int sizeX, sizeY;				// 転送するサイズ
	int surfaceCntX;				// 必要なサーフェスの横枚数
	int surfaceCntY;				// 必要なサーフェスの横枚数
	int totalSurface;				// 必要なサーフェスの総数
	int totalSurfaceCnt = 0;		// 现在の确保したサーフェスの枚数（ ループカウンタ ）
	int	SurfaceSearchPointBak = SurfaceSearchPoint; // 一周检索したら終了するため最初の位置を记忆
	int amariSizeX = FALSE;		// 横に余りがあるかフラグ
	int amariSizeY = FALSE;		// 縦に余りがあるかフラグ
	BOOL vramFullFlag = FALSE; 		// VRAMがいっぱいかどうか
	SURFACE_INFO *prevSurfaceInfo = NULL; 	// 前のサーフェスインフォ构造体のアドレス
	
	// 送るＢＭＰのOFFセットポイント
	offsetX = 0; 
	offsetY = bmpHeight;
	
	// 必要なサーフェスの横枚数计算
	surfaceCntX = bmpWidth / SURFACE_WIDTH;
	
	// 横に余りがあったら半端サイズを记忆
	if( ( amariSizeX = bmpWidth % SURFACE_WIDTH ) ){ 
		surfaceCntX++;		// もうひとつ必要
	}
	
	// 必要なサーフェスの縦枚数计算
	surfaceCntY = bmpHeight / SURFACE_HEIGHT;
	
	// 縦に余りがあったら半端サイズを记忆
	if( ( amariSizeY = bmpHeight % SURFACE_HEIGHT ) ){ 
		surfaceCntY++;		// もうひとつ必要
	}
	// 必要なサーフェスの総数计算
	totalSurface  = surfaceCntX * surfaceCntY;
	
#if 0
	// 横幅を４のバウンダリにする
	if( ( lpBmpInfo->bmiHeader.biWidth & 3 ) ){
		lpBmpInfo->bmiHeader.biWidth += 4 - lpBmpInfo->bmiHeader.biWidth & 3;
	}
#else	// Realbin 読み込むとき
	// 横幅を４のバウンダリにする
	if( ( RealBinWidth & 3 ) ){
		RealBinWidth += 4 - RealBinWidth & 3;
	}
#endif

	// 同じ所に上书きするなら前回のものは消去する。
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo != NULL ){
			prevSurfaceInfo = SpriteInfo[ bmpNo ].lpSurfaceInfo;
			// サーフェスリストの初期化ループ
			for( ; prevSurfaceInfo != NULL;
				prevSurfaceInfo = prevSurfaceInfo->pNext ){
				
			// ＢＭＰ番号初期化
				prevSurfaceInfo->bmpNo = -1;
				
#ifdef _DEBUG		
				// 现在使っているサーフェスの数マイナス
				SurfaceUseCnt--;
#endif
			}
			SpriteInfo[ bmpNo ].lpSurfaceInfo = NULL;
	}

	// 空いているサーフェスを探す
	// サーフェスの数だけループ
	while( 1 ){
		// 使用できる状态なら
		if( SurfaceInfo[ SurfaceSearchPoint ].date < SurfaceDate - SURACE_BMP_DEATH_DATE ){
#ifdef _DEBUG		
			// 现在使っているサーフェスの数カウント
			SurfaceUseCnt++;
#endif
			// 上书きする时
			if( SurfaceInfo[ SurfaceSearchPoint ].bmpNo != -1 ){
				// サーフェスインフォ构造体のアドレス
				SURFACE_INFO *lpSurfaceInfo;
				
				// サーフェスリストの先头アドレスを记忆
				lpSurfaceInfo = SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo;
				
				// 前にいたＢＭＰとサーフェスのリンクを削除
				SpriteInfo[ SurfaceInfo[ SurfaceSearchPoint ].bmpNo ].lpSurfaceInfo = NULL;
				
				// サーフェスリストの初期化ループ
				for( ; lpSurfaceInfo != NULL;
					lpSurfaceInfo = lpSurfaceInfo->pNext ){
					
					// ＢＭＰ番号初期化
					lpSurfaceInfo->bmpNo = -1;
					
#ifdef _DEBUG		
					// 现在使っているサーフェスの数マイナス
					SurfaceUseCnt--;
#endif
				}
				
			}
			// サーフェスリストの先头アドレスを记忆するとき
			if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL ){
				// スプライトインフォに先头アドレスを记忆する
				SpriteInfo[ bmpNo ].lpSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				// パレット番号とフェードのカウンターも学习
				SurfaceInfo[ SurfaceSearchPoint ].PalNo = PalState.palNo;
				SurfaceInfo[ SurfaceSearchPoint ].PalCount = PalState.count;
#ifdef PUK2
				SurfaceInfo[ SurfaceSearchPoint ].AnimPalNo = 0;
#endif
				
			}else{ // つなげるとき
				
				// 覚えておいた前のサーフェスインフォ构造体にアドレスを教える
				prevSurfaceInfo->pNext = &SurfaceInfo[ SurfaceSearchPoint ];
			}
			
			// ＢＭＰ番号を记忆する
			SurfaceInfo[ SurfaceSearchPoint ].bmpNo = bmpNo;
			
			// OFFセット座标を学习
			SurfaceInfo[ SurfaceSearchPoint ].offsetX = offsetX;
			SurfaceInfo[ SurfaceSearchPoint ].offsetY = bmpHeight - offsetY;
			
			// 横に余りがあるときは転送する前にサーフェスを黒でクリアー
			if( offsetX >= bmpWidth - SURFACE_WIDTH && amariSizeX ){
				ClearSurface( SurfaceInfo[ SurfaceSearchPoint ].lpSurface );
				// 余りサイズ
				sizeX = amariSizeX;				
			}else sizeX = SURFACE_WIDTH;
			
			// 縦に余りがあるときは転送する前にサーフェスを黒でクリアー
			if( offsetY - SURFACE_HEIGHT <= 0 && amariSizeY ){
				// 先に黒でクリアーされてないとき
				if( sizeX != amariSizeX ){
					ClearSurface( SurfaceInfo[ SurfaceSearchPoint ].lpSurface );
				}
				// 余りサイズ
				sizeY = amariSizeY;
			}else sizeY = SURFACE_HEIGHT;
			
			// ＢＭＰをサーフェスへ転送
			DrawBitmapToSurface2( 	SurfaceInfo[ SurfaceSearchPoint ].lpSurface, 
									offsetX, 
									offsetY - 1, 
									sizeX,
									sizeY,
									lpBmpInfo );

			// 现在の确保したサーフェスの枚数をカウント
			totalSurfaceCnt++;

			// すべて确保し終わったら
			if( totalSurfaceCnt >= totalSurface ){
				//　pNext にＮＵＬＬを入れて終了
				SurfaceInfo[ SurfaceSearchPoint ].pNext = NULL;
				// 检索位置を进ませる
				SurfaceSearchPoint++;
				
#if 0			// VRAMを优先使用バージョン

				// VRAMのサーフェスを检索するとき
				if( vramFullFlag == FALSE ){
					// リミットチェック
					if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
				}else{
					// 检索位置を戾す
					SurfaceSearchPoint = SurfaceSearchPointBak;
				}
				
#else			// VRAMとSYSRAMを同等级で使用バージョン

				// リミットチェック
				if( SurfaceSearchPoint >= SurfaceCnt ) SurfaceSearchPoint = 0;
#endif
				break;
				
			}else{
				// 今のサーフェスインフォ构造体のアドレスを学习
				prevSurfaceInfo = &SurfaceInfo[ SurfaceSearchPoint ];
				
				// 右端まで送ったら
				if( offsetX >= bmpWidth - SURFACE_WIDTH ){ 
					offsetX = 0;
					offsetY -= SURFACE_HEIGHT;
				}else{ 
					offsetX += SURFACE_WIDTH;
				}
			}
		}
		// 检索位置を进ませる
		SurfaceSearchPoint++;
		
#if 0	// VRAMを优先使用バージョン

		// VRAMのサーフェスを检索するとき
		if( vramFullFlag == FALSE ){
			// VRAMサーフェスの最后まで检索したとき
			if( SurfaceSearchPoint >= VramSurfaceCnt ) SurfaceSearchPoint = 0;
			// 一周检索したらVRAMに空きなし状态であきらめる
			if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
				//MessageBox( hWnd, "ＶＲＡＭ没有剩余空间。", "确认", MB_OK );
				// 检索位置をSYSTEMサーフェスが存在するところへ移动
				SurfaceSearchPoint = VramSurfaceCnt + 1;
				vramFullFlag = TRUE;
			}
		}
		// SYSTEMRAMのサーフェスを检索するとき
		if( vramFullFlag == TRUE ){
			// 最后まで检索したら
			if( SurfaceSearchPoint >= SurfaceCnt ){ 
				// 检索位置を戾す
				SurfaceSearchPoint = SurfaceSearchPointBak;
				//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
				break;
			}
		}
		
#else	// VRAMとSYSRAMを同等级で使用バージョン
		
		// 最后まで检索したら
		if( SurfaceSearchPoint >= SurfaceCnt ){ 
			// 最初に返回
			SurfaceSearchPoint = 0;
		}
		// 一周检索したらサーフェスに空きなし状态であきらめる
		if( SurfaceSearchPoint == SurfaceSearchPointBak ){ 
			//MessageBox( hWnd, "Surface不足。", "确认", MB_OK );
			char szBuffer[256];
			sprintf( szBuffer, "Surface不足。%d\n", SurfaceSearchPoint );         //MLHIDE
			OutputDebugString( szBuffer);
			break;
		}
	}
#endif
}

/* OFFスクリーンサーフェスの作成 *********************************************/
BOOL InitOffScreenSurface( void )
{
	int i;
	BOOL vramFullFlag = FALSE; // VRAMがいっぱいかどうか
	
	// サーフェスの数
	SurfaceCnt = 0;
	// VRAM に作成したサーフェスの数
	VramSurfaceCnt = 0;
	// SYSTEMRAM に作成したサーフェスの数
	SysramSurfaceCnt = 0;

#ifdef PUK2
	// オートマップ作业用サーフェスの作成（每フレームじか打ちするので、システムに确保）
#ifdef PUK3_SCENE_CHANGE
	if( ( lpAutoMapSurface = CreateWarkSurface( DEF_APPSIZEX, DEF_APPSIZEY, DEF_COLORKEY, DDSCAPS_SYSTEMMEMORY )) == NULL ){
#else
	if( ( lpAutoMapSurface = CreateSurface( DEF_APPSIZEX, DEF_APPSIZEY, DEF_COLORKEY, DDSCAPS_SYSTEMMEMORY )) == NULL ){
#endif
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd ,ERRMSG_108 ,"确认",MB_OK | MB_ICONSTOP );           //MLHIDE
	#else
		MessageBox( hWnd ,"オートマップ作業用Surfaceの作成に失敗しました。" ,"确认",MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		return FALSE;
	}
#endif
	// バトルサーフェスの作成
#ifdef PUK3_SCENE_CHANGE
	if( ( lpBattleSurface = CreateWarkSurface( DEF_APPSIZEX, DEF_APPSIZEY, DEF_COLORKEY, DDSCAPS_VIDEOMEMORY )) == NULL ){
#else
	if( ( lpBattleSurface = CreateSurface( DEF_APPSIZEX, DEF_APPSIZEY, DEF_COLORKEY, DDSCAPS_VIDEOMEMORY )) == NULL ){
#endif
		// バトルサーフェスの作成
		if( ( lpBattleSurface = CreateSurface( DEF_APPSIZEX, DEF_APPSIZEY, DEF_COLORKEY, DDSCAPS_SYSTEMMEMORY )) == NULL ){
	#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd ,ERRMSG_109 ,"确认",MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
			MessageBox( hWnd ,"バトルSurfaceの作成に失敗しました。" ,"确认",MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
			return FALSE;
		}
	}
	// SURACE_INFO_MAX までサーフェスを作る */
	for( i = 0 ; i < SURACE_INFO_MAX ; i++ ){
	
#ifdef _DEBUG		
#ifdef VRAM_SYSRAM_POINT_ON
		// 今だけ（ チェック用 )
		if( i == VRAM_SYSRAM_POINT ){
			 vramFullFlag = TRUE;
		}
#endif
#endif

		// VRAMにサーフェスを作成できるとき
		if( vramFullFlag == FALSE ){
			// DirectDraw用にBITMAPを格纳するためのサーフェスを作成
			if( ( SurfaceInfo[ i ].lpSurface = CreateSurface( SurfaceSizeX, SurfaceSizeY, DEF_COLORKEY, DDSCAPS_VIDEOMEMORY )) == NULL ){
				//MessageBox( hWnd ,"ＶＲＡＭSurfaceの作成に失敗しました。" ,"确认",MB_OK | MB_ICONSTOP );
				vramFullFlag = TRUE;
			}else{
				// VRAM に作成したサーフェスの数カウント
				VramSurfaceCnt++;
			}
		}
		
		// SYSTEMRAMにサーフェスを作成するとき
		if( vramFullFlag == TRUE ){
			if( ( SurfaceInfo[ i ].lpSurface = CreateSurface( SurfaceSizeX, SurfaceSizeY, DEF_COLORKEY, DDSCAPS_SYSTEMMEMORY )) == NULL ){
	#ifdef PUK3_ERRORMESSAGE_NUM
				MessageBox( hWnd ,ERRMSG_110 ,"确认",MB_OK | MB_ICONSTOP );         //MLHIDE
	#else
				MessageBox( hWnd ,"ＳＹＳＲＡＭSurfaceの作成に失敗しました。" ,"确认",MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
				return FALSE;
			}else{
				// SYSTEMRAM に作成したサーフェスの数カウント
				SysramSurfaceCnt++;
			}
		}
	}
	
	// 作成したサーフェスの数を记忆
	SurfaceCnt = i;
	
	/* サーフェスインフォ构造体の初期化 */
	InitSurfaceInfo();
	/* スプライトインフォ构造体の初期化 */
	InitSpriteInfo();
		
	
	return TRUE;
}

/* サーフェスインフォ构造体の初期化　**************************************/
void InitSurfaceInfo( void )
{
	int i;
	
#ifdef _DEBUG		
	// 现在使っているサーフェスの数初期化
	SurfaceUseCnt = 0;
#endif
	
	// サーフェス检索位置初期化
	SurfaceSearchPoint = 0;
	
	// サーフェス数だけループ */
	for( i = 0 ; i < SurfaceCnt ; i++ ){
	
		// サーフェスインフォ构造体の初期化
		SurfaceInfo[ i ].bmpNo = -1;
		SurfaceInfo[ i ].date = 0;
		SurfaceInfo[ i ].pNext = NULL;
#ifdef PUK2_DIFFPAL_SURFACE
		SurfaceInfo[ i ].otherPalSrf = NULL;
#endif
	}
}	

/* スプライトインフォ构造体の初期化　**************************************/
void InitSpriteInfo( void )
{
	int i;
	
	// ＢＭＰの数だけループ */
	for( i = 0 ; i < MAX_GRAPHICS ; i++ ){
		// VRAM にいないことにする
		SpriteInfo[ i ].lpSurfaceInfo = NULL;
#ifdef PUK2
		SpriteInfo[ i ].lpPalList = NULL;
#endif
	}
}	

// ＢＭＰをロードする *********************************************************/
BOOL LoadBmp( int bmpNo )
{
	// リミットチェック
	if( bmpNo < 0 || bmpNo >= MAX_GRAPHICS ) return FALSE;
	
	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL
#ifdef SUPPORT_16BIT
	|| ( displayBpp != 8 && SpriteInfo[ bmpNo ].lpSurfaceInfo->PalNo != PalState.palNo )
	|| ( displayBpp != 8 && SpriteInfo[ bmpNo ].lpSurfaceInfo->PalCount != PalState.count )
#endif
	){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if( realGetImage( 	bmpNo, 
							( unsigned char **)&pRealBinBits, 
							&RealBinWidth, 
							&RealBinHeight ) == FALSE ){
							
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
			return FALSE;
		}
		// ＢＭＰのサイズを记忆
		SpriteInfo[ bmpNo ].width = RealBinWidth;
		SpriteInfo[ bmpNo ].height = RealBinHeight;
		// ＢＭＰをサーフェスへ割り当て 
		AllocateBmpToSurface( bmpNo );
	}
	return TRUE;
}

// ＢＭＰをロードする（グラフィック番号变换もする） ***************************/
BOOL LoadBmp2( int bmpNo )
{
	// RIAL.BIN　番号にする
	realGetNo( bmpNo , (U4 *)&bmpNo );
	// ＶＲＡＭにいないときはハードディスクからロードする
	if( SpriteInfo[ bmpNo ].lpSurfaceInfo == NULL 
#ifdef SUPPORT_16BIT
	|| ( displayBpp != 8 && SpriteInfo[ bmpNo ].lpSurfaceInfo->PalNo != PalState.palNo )
	|| ( displayBpp != 8 && SpriteInfo[ bmpNo ].lpSurfaceInfo->PalCount != PalState.count )
#endif
	){
		// ＢＭＰ番号からイメージデータを返す( Realbin から読み込む )
		if( realGetImage( 	bmpNo, 
							( unsigned char **)&pRealBinBits, 
							&RealBinWidth, 
							&RealBinHeight ) == FALSE ){
							
			//MessageBox( hWnd, "图像数据读取失败。", "确认", MB_OK );
			return FALSE;
		}
		// ＢＭＰのサイズを记忆
		SpriteInfo[ bmpNo ].width = RealBinWidth;
		SpriteInfo[ bmpNo ].height = RealBinHeight;
		// ＢＭＰをサーフェスへ割り当て 
		AllocateBmpToSurface( bmpNo );
	}
	return TRUE;
}

#if 0
/* ビットマップの読み込み *****************************************************/
void OpenBmp( void )
{
	int i;
   	char *fileName[]=
	/* 0 */	  { "data\\spr\\自.bmp","data\\spr\\敵１.bmp","data\\spr\\敵１２.bmp","data\\spr\\敵２.bmp","data\\spr\\敵２２.bmp", //MLHIDE
	/* 5 */		"data\\spr\\敵３２.bmp","data\\spr\\敵３.bmp","data\\spr\\敵４.bmp","data\\spr\\敵４２.bmp","data\\spr\\敵５.bmp", //MLHIDE
	/*10 */		"data\\spr\\敵５２.bmp","data\\spr\\消す.bmp","data\\spr\\Ｍ.bmp","data\\spr\\Ｗ.bmp","data\\spr\\Ｓ.bmp", //MLHIDE
	/*15 */		"data\\spr\\Ｍ２.bmp","data\\spr\\Ｗ２.bmp","data\\spr\\Ｓ２.bmp","data\\spr\\Ｍ３.bmp","data\\spr\\Ｗ３.bmp", //MLHIDE
	/*20 */		"data\\spr\\Ｓ３.bmp","data\\spr\\爆発１.bmp","data\\spr\\爆発２.bmp","data\\spr\\爆発３.bmp", "data\\spr\\自弾.bmp", //MLHIDE
	/*25 */		"data\\spr\\自弾消.bmp","data\\spr\\敵弾.bmp","data\\spr\\敵弾消.bmp","data\\spr\\黄色弾.bmp","data\\spr\\青色弾.bmp", //MLHIDE
	/*30 */		"data\\spr\\風呂場.bmp","data\\spr\\pl01.bmp","data\\spr\\フォント.bmp","data\\spr\\st\\ken.bmp" }; //MLHIDE

	//BITMAPファイルの展开及びメモリー确保
	//lpBmp[ 0 ] = LoadDirectDrawBitmap( fileName[ 0 ] ); 
	//if( lpBmp[ 0 ] == NULL ) MessageBox( hWnd, "ＢＭＰ打开失败。", "确认", MB_OK );
	
	//if( !WindowMode ){
		// システムパレット初期化
	//	InitPalette( lpBmp[ 0 ] );
	//}
	
	// 作れるだけサーフェスを作る */
	for( i = 0 ; i < SURACE_INFO_MAX ; i++ ){
	
		// サーフェスインフォ构造体の初期化
		SurfaceInfo[ i ].bmpNo = -1;
		SurfaceInfo[ i ].pNext = NULL;
		
		// DirectDraw用にBITMAPを格纳するためのサーフェスを作成
		if( ( SurfaceInfo[ i ].lpSurface = CreateSurface( SURFACE_WIDTH, SURFACE_HEIGHT, DEF_COLORKEY, 0 )) == NULL ){
	#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd ,ERRMSG_111 ,"确认",MB_OK | MB_ICONSTOP );          //MLHIDE
	#else
			MessageBox( hWnd ,"Surface初始化失败。" ,"确认",MB_OK | MB_ICONSTOP );     //MLHIDE
	#endif
			break;
			//DestroyWindow( hWnd );
		}
		
		// サーフェスの数
		SurfaceCnt++;
		
		// BITMAPをサーフェス(座标[0,0])へ复写
		//DrawBitmapToSurface( lpSurface[ i ], 0, 0 ,lpBmp[ 0 ] );
		//SetRect( &Rect, 0, 0, lpBmp[ 0 ]->bmiHeader.biWidth, lpBmp[ 0 ]->bmiHeader.biHeight ); 
	}
}
#endif
