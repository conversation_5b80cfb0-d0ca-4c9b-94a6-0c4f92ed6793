﻿//
// データ压缩展开
//

#include<stdio.h>
#include<stdlib.h>
#include<string.h>

#include"../PUK2/puk2.h"
#ifdef PUK2_MEMCHECK
	#include"../systeminc/system.h"
#endif
#include"../systeminc/unpack.h"

#define BIT_CMP			(unsigned char)0x80
#define BIT_ZERO		(unsigned char)0x40
#define BIT_REP_LARG	(unsigned char)0x10
#define BIT_REP_LARG2	(unsigned char)0x20

// 压缩
// 引数
// 		buf    ... 压缩するデータへのポインタ
//		disBuf ... 压缩后のデータを入れるポインタ（NULLを入れとけばメモリを自动で确保）
//		width  ... 画像の幅
//		height ... 画像の高さ
//		len    ... 压缩后のサイズへのポインタ
//		cmpFlag ... 1 で压缩。0 で非压缩。
// 戾り值
// 		压缩データへのポインタ。失败は0
#ifdef PUK2
// 引数(追加分)
//		palsiz ... パレットデータの大きさ
unsigned char *encoder( unsigned char *buf, unsigned char **disBuf,
	unsigned int width, unsigned int height, unsigned int *len, int cmpFlag, unsigned int palsize )
#else
unsigned char *encoder( unsigned char *buf, unsigned char **disBuf,
	unsigned int width, unsigned int height, unsigned int *len, int cmpFlag )
#endif
{
	unsigned char *wBuf, *ewBuf, *eBuf;
	unsigned char *wBuf1, *buf1;
	unsigned char *wBuf2, *buf2;
	unsigned char idx;
	int flag = 1;
	RD_HEADER *header;
	unsigned int cnt, cnt2;
	unsigned char repData;
	int mode;
	unsigned int l, el;
	int addWBuf;

	// メモリをヘッダ＋画像サイズ分确保
	if( *disBuf == NULL )
	{
		if( (wBuf = (unsigned char *)malloc( width * height + sizeof( RD_HEADER ) )) == NULL )
		{
			return NULL;
		}
#ifdef PUK2_MEMCHECK
		memlistset( wBuf, MEMLISTTYPE_UNPACK );
#endif
		*disBuf = wBuf;
	}
	else
	{
		wBuf = *disBuf;
	}

	if( cmpFlag )
	{
		flag = 1;
	}
	else
	{
		flag = 0;
	}

	// バッファのエンドアドレス
#ifdef PUK2
	eBuf = buf + width * height + palsize;
	ewBuf = wBuf + width * height + palsize + sizeof( RD_HEADER );
#else
	eBuf = buf + width * height;
	ewBuf = wBuf + width * height + sizeof( RD_HEADER );
#endif

	wBuf1 = wBuf+sizeof( RD_HEADER );
#ifdef PUK2
	if (palsize){
		*( (unsigned int *)wBuf1 )=palsize;
		wBuf1+=4;
		ewBuf+=4;
	}
#endif
	buf1 = buf;
	while( cmpFlag )
	{
		// 終了チェック
		if( buf1 >= eBuf )
		{
			// 読み込みデータが无くなった
			flag = 1;
			break;
		}
		if( wBuf1 >= ewBuf )
		{
			// 压缩データサイズが元データサイズを超えたので終了
			flag = 0;
			break;
		}

		// ゼロの压缩（２个以上なら压缩）
		if( *buf1 == 0 && *(buf1+1) == 0 )
		{
			// まずは压缩个数を数える
			idx = BIT_CMP | BIT_ZERO;
			cnt = 2;
			buf1 += 2;
			while( buf1 < eBuf && cnt < 0xfffff )
			{
				if( *buf1 != 0 )
				{
					break;
				}
				buf1++;
				cnt++;
			}
			// ０の连続が16个未满の时
			if( cnt <= 0xf )
			{
				addWBuf = 0;
			}
			else
			// 16个以上4096未满の时
			if( cnt <= 0xfff )
			{
				addWBuf = 1;
			}
			// 4096以上の时
			else
			{
				addWBuf = 2;
			}

			// 压缩データサイズが元データサイズを超えたので終了
			if( wBuf1+addWBuf >= ewBuf )
			{
				flag = 0;
				break;
			}

			// 压缩データを书き込む
			if( addWBuf == 0 )
			{
				idx |= (cnt & 0x0f);
				*wBuf1++ = idx;
				continue;
			}
			else
			if( addWBuf == 1 )
			{
				idx |= (BIT_REP_LARG | ((cnt >> 8) & 0xf));
				*wBuf1++ = idx;
				*wBuf1++ = (unsigned char)(cnt & 0xff);
				continue;
			}
			else
			{
				idx |= (BIT_REP_LARG2 | ((cnt >> 16) & 0xf));
				*wBuf1++ = idx;
				*wBuf1++ = (unsigned char)((cnt >> 8) & 0xff);
				*wBuf1++ = (unsigned char)(cnt & 0xff);
				continue;
			}
		}
#if 1
		// ゼロ以外で压缩（３个以上なら压缩）
		if( *buf1 == *(buf1+1) && *buf1 == *(buf1+2) )
		{
			repData = *buf1;
			idx = BIT_CMP;
			cnt = 3;
			buf1 += 3;
			while( buf1 < eBuf && cnt < 0xfffff )
			{
				if( *buf1 != repData )
				{
					break;
				}
				buf1++;
				cnt++;
			}

			// 连続データが16个未满の时
			if( cnt <= 0xf )
			{
				addWBuf = 1;
			}
			else
			// 16个以上4096未满の时
			if( cnt <= 0xfff )
			{
				addWBuf = 2;
			}
			// 4096以上の时
			else
			{
				addWBuf = 3;
			}

			// 压缩データサイズが元データサイズを超えたので終了
			if( wBuf1+addWBuf >= ewBuf )
			{
				flag = 0;
				break;
			}

			// 压缩データを书き込む
			if( addWBuf == 1 )
			{
				idx |= (cnt & 0x0f);
				*wBuf1++ = idx;
				*wBuf1++ = repData;
				continue;
			}
			else
			if( addWBuf == 2 )
			{
				idx |= (BIT_REP_LARG | ((cnt >> 8) & 0xf));
				*wBuf1++ = idx;
				*wBuf1++ = repData;
				*wBuf1++ = (unsigned char)(cnt & 0xff);
				continue;
			}
			else
			{
				idx |= (BIT_REP_LARG2 | ((cnt >> 16) & 0xf));
				*wBuf1++ = idx;
				*wBuf1++ = repData;
				*wBuf1++ = (unsigned char)((cnt >> 8) & 0xff);
				*wBuf1++ = (unsigned char)(cnt & 0xff);
				continue;
			}
		}
#endif

		// 压缩无し
		// 最初はどこまで压缩しないかを见るだけ
		idx = 0;
		cnt2 = 0;
		buf2 = buf1;
		wBuf2 = wBuf1;
		while( 1 )
		{
			// 終了チェック
			if( buf2 >= eBuf		// 読み込むデータが无いので終了
			 || cnt2 >= 0xfff )		// 一回に扱えるデータ长を超えたので次へ
			{
				mode = 0;
				break;
			}
			if( wBuf2 >= ewBuf )
			{
				// 压缩データサイズが元データサイズを超えたので終了
				mode = 1;
				break;
			}
			// データの最后から４番目以前なら压缩データがあるか确认する
			// それ以外は无压缩として扱う
			if( buf2+2 < eBuf )
			{
				if( *buf2 == 0 && *(buf2+1) == 0 )
				{
					mode = 0;
					break;
				}
				if( *buf2 != 0 && *buf2 == *(buf2+1) && *(buf2+1) == *(buf2+2) )
				{
					mode = 0;
					break;
				}

			}
			buf2++;
			wBuf2++;
			cnt2++;
		}

		if( cnt2 <= 0xf )
		{
			addWBuf = 0;
		}
		else
		if( cnt2 <= 0xfff )
		{
			addWBuf = 1;
		}
		else
		{
			addWBuf = 2;
		}

		if( mode == 1
		 || wBuf2+addWBuf >= ewBuf )
		{
			// 压缩データサイズが元データサイズを超えたので終了
			flag = 0;
			break;
		}

		// 非压缩データの数がわかったので転送
		if( addWBuf == 0 )
		{
			idx = (cnt2 & 0xf);
			*wBuf1++ = idx;
		}
		else
		if( addWBuf == 1 )
		{
			idx = BIT_REP_LARG | ((cnt2 >> 8) & 0xf);
			*wBuf1++ = idx;
			*wBuf1++ = (unsigned char)(cnt2 & 0xff);
		}
		else
		{
			idx = BIT_REP_LARG2 | ((cnt2 >> 16) & 0xf);
			*wBuf1++ = idx;
			*wBuf1++ = (unsigned char)((cnt2 >> 8) & 0xff);
			*wBuf1++ = (unsigned char)(cnt2 & 0xff);
		}
		for( cnt = 0; cnt < cnt2; cnt++ )
		{
			*wBuf1++ = *buf1++;
		}
	}


	header = (RD_HEADER *)wBuf;
	header->id[0] = 'R';
	header->id[1] = 'D';
	header->width = width;
	header->height = height;

	if( flag == 1 )
	{
		header->compressFlag = 1;
		header->size = wBuf1 - wBuf;
		l = header->size;
	}
	else
	// 压缩せずにコピー
	{
		header->compressFlag = 0;
		header->size = (int)wBuf + width * height + sizeof( RD_HEADER );
		wBuf1 = wBuf + sizeof( RD_HEADER );
#ifdef PUK2
		if (palsize){
			*( (unsigned int *)wBuf1 )=palsize;
			wBuf1+=4;
			header->size+=4;
		}
#endif
		buf1 = buf;
#ifdef PUK2
		el = width * height + palsize;
#else
		el = width * height;
#endif
		for( l = 0; l < el; l++ )
			*wBuf1++ = *buf1++;
		l += sizeof( RD_HEADER );
#ifdef PUK2
		if (palsize) l += 4;
#endif
	}
#ifdef PUK2
	if (palsize) header->compressFlag|=2;
#endif

	*len = l;
	return wBuf;
}


// 展开
// 引数
// 		buf    ... 展开するデータへのポインタ
//		disBuf ... 展开后のデータを入れるポインタ
//		width  ... 画像の幅へのポインタ
//		height ... 画像の高さへのポインタ
//		len    ... 展开するデータサイズへのポインタ
// 戾り值
// 		展开データへのポインタ。失败は0
#ifdef PUK2
// 引数(追加分)
//		palsiz ... パレットデータの大きさをいれるポインタ
unsigned char *decoder( unsigned char *buf, unsigned char **disBuf,
	unsigned int *width1, unsigned int *height1, unsigned int *len, unsigned int *palsiz )
#else
unsigned char *decoder( unsigned char *buf, unsigned char **disBuf,
	unsigned int *width1, unsigned int *height1, unsigned int *len )
#endif
{
	RD_HEADER *header;
	unsigned char *wBuf, *ewBuf, *eBuf;
	unsigned char *wBuf1, *buf1;
	unsigned int width, height;
	unsigned int cnt;
	unsigned int l, el;
	unsigned char repData;
	unsigned char idx;

	if( *disBuf == NULL )
		return NULL;

	wBuf = *disBuf;

	header = (RD_HEADER *)buf;

	// フォーマットのチェック
	if( header->id[0] != 'R' || header->id[1] != 'D' )
	{
		return NULL;
	}

	width  = header->width;
	height = header->height;

#if 0
	// メモリを画像サイズ分确保
	if( (wBuf = (unsigned char *)malloc( width * height )) == NULL )
	{
		return NULL;
	}
	#ifdef PUK2_MEMCHECK
		memlistset( wBuf, MEMLISTTYPE_UNPACK );
	#endif
#endif

	// 压缩されてなければコピー
#ifdef PUK2
	if( (header->compressFlag&1)==0 )
#else
	if( header->compressFlag == 0 )
#endif
	{
		wBuf1 = wBuf;
		buf1 = buf+sizeof( RD_HEADER );
#ifdef PUK2
		if( header->compressFlag & 2 ){
			*palsiz=*( (unsigned int *)buf1 );
			buf1+=4;
			el = width * height + *palsiz;
		}else{
			*palsiz=0;
			el = width * height;
		}
#else
		el = width * height;
#endif
		for( l = 0; l < el; l++ )
			*wBuf1++ = *buf1++;

		*len = l;
		*width1 = width;
		*height1 = height;
		return wBuf;
	}

	// バッファのエンドアドレス
	eBuf = buf + header->size;
	ewBuf = wBuf + width * height;

	wBuf1 = wBuf;
	buf1 = buf+sizeof( RD_HEADER );
#ifdef PUK2
	if( header->compressFlag & 2 ){
		*palsiz=*( (unsigned int *)buf1 );
		buf1+=4;
	}else *palsiz=0;
#endif
	while( buf1 < eBuf )
	{
		idx = *buf1++;
		// 压缩されいる
		if( (idx & BIT_CMP) != 0 )
		{
			// ゼロ压缩の场合
			if( (idx & BIT_ZERO) != 0 )
			{
				repData = 0;
			}
			else
			// その他の场合
			{
				repData = *buf1++;
			}
			// 4096个以上连続の时
			if( (idx & BIT_REP_LARG2) != 0 )
			{
				cnt = ((idx & 0x0f)<<16);
				cnt |= ((*buf1) << 8);
				buf1++;
				cnt |= *buf1++;
			}
			else
			// 16个以上连続の时
			if( (idx & BIT_REP_LARG) != 0 )
			{
				cnt = ((idx & 0x0f)<<8);
				cnt |= *buf1++;
			}
			else
			{
				cnt = (idx & 0x0f);
			}
			memset( wBuf1, repData, cnt );
			wBuf1 += cnt;
		}
		else
		// 压缩されてない
		{
			// １６个以上连続の时
			if( (idx & BIT_REP_LARG) != 0 )
			{
				cnt = ((idx & 0x0f)<<8);
				cnt |= *buf1++;
			}
			else
			{
				cnt = (idx & 0x0f);
			}
			if( cnt >= 0xfffff )
			{
				// ここにきたらどっか变
				return NULL;
			}
			for( l = 0; l < cnt; l++ )
				*wBuf1++ = *buf1++;
		}
	}

	*len = width * height;
	*width1  = width;
	*height1 = height;
	return wBuf;
}
