﻿/************************/	
/*	gamemain.h			*/
/************************/

#ifndef _GAMEMAIN_H_
#define _GAMEMAIN_H_

// ゲームの状态
enum{
	GAME_LOGIN,					// ログイン
	GAME_FIELD,					// フィールドマップ
	GAME_FIELD_TO_ENCOUNT,		// フィールドマップからエンカウント演出中
	GAME_ENCOUNT_TO_BATTLE,		// エンカウント演出から战闘
	GAME_BATTLE				// エンカウント演出から战闘
};


extern int	GameState;	// ゲームの状态
extern double NowTime;	// 现在の时间记忆
#ifdef PUK2_FPS
	extern double NowDrawTime; 					// 现在の时间记忆
#endif

//---------------------------------------------------------------------------//
// 关数プロト种类宣言                                                      //
//---------------------------------------------------------------------------//
#ifdef PUK3_ENCOUNT_NULL_GRA
	// これを呼んだフレームは确实に画面の更新が行われるようになる
	void surelyDispDraw();
#endif
BOOL GameMain( void );				// ゲーム处理メイン关数

// ゲーム开始处理
BOOL InitGame( void );

// ゲーム終了处理
void EndGame( void );
void endGame( void );
#ifdef PUK2_DEVICE_CHANGE
	// デバイス变更前に行うべき处理をこの关数で行う
	void DeviceChangeProc();
#endif

#endif
