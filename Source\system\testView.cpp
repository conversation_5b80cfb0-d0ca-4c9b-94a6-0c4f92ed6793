﻿/************************/
/*	testView.cpp		*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/t_music.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/process.h"
#include "../systeminc/main.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/pattern.h"
#include "../systeminc/math2.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/battle.h"
#include "../systeminc/battleEffect.h"
#ifdef PUK3_RIDEBIN
	#include "../systeminc/mouse.h"
	#include "../systeminc/filetbl.h"
	#include <time.h>
#endif

#ifdef PUK2
unsigned char Save_bmp( int bmpNo );
#endif

#ifdef _DEBUG		

#define ANIM_KIND_MAX 21	// アニメーションの种类の最大

static int backColor = SYSTEM_PAL_GRAY; // 背景パレット番号

// アニメーション文字列
static char *animStr[ 21 ] = { 
				"立ち",                                                             //MLHIDE
				"歩き",                                                             //MLHIDE
				"バトル時移動（前）",                                                      //MLHIDE
				"バトル時移動（中）",                                                      //MLHIDE
				"バトル時移動（後）",                                                      //MLHIDE
				"攻击",                                                             //MLHIDE
				"魔法",                                                             //MLHIDE
				"投掷",                                                             //MLHIDE
				"受伤",                                                             //MLHIDE
				"防御",                                                             //MLHIDE
				"気絶",                                                             //MLHIDE
				"座る",                                                             //MLHIDE
				"挥手",                                                             //MLHIDE
				"喜ぶ",                                                             //MLHIDE
				"怒る",                                                             //MLHIDE
				"悲伤",                                                             //MLHIDE
				"点头",                                                             //MLHIDE
				"じゃんけん（石头）",                                                      //MLHIDE
				"じゃんけん（剪刀）",                                                      //MLHIDE
				"じゃんけん（布）",                                                       //MLHIDE
				"釣り",                                                             //MLHIDE
			};
// ヒットマーク处理（会心）
//extern void hit_mark_critical( ACTION *a0 );

// 表示バッファに溜める（ＳｐｒＶｉｅｗ用）
void StockDispBufferSprView( int x, int y, UCHAR dispPrio, int bmpNo )
{	
	// 今回保存する场所までアドレスを进ませる
	DISP_SORT 	*pDispSort = DispBuffer.DispSort + DispBuffer.DispCnt;
	DISP_INFO 	*pDispInfo = DispBuffer.DispInfo + DispBuffer.DispCnt;
	
	// カウントオーバーの时
	if( DispBuffer.DispCnt >= DISP_BUFFER_SIZE ) return;
	
	// 表示优先ソート用构造体
	pDispSort->dispPrio = dispPrio;
	pDispSort->no = DispBuffer.DispCnt;
	// 表示情报构造体（ ソートしない内容 ）
	pDispInfo->x = x;
	pDispInfo->y = y;
	pDispInfo->bmpNo = bmpNo;
	pDispInfo->hitFlag = 2;
#ifdef PUK2
	pDispInfo->pAct = NULL;
	pDispInfo->bm.u=0;
	pDispInfo->bm.v=0;
	pDispInfo->bm.w=0;
	pDispInfo->bm.h=0;
	pDispInfo->bm.rgba.rgba=0xffffffff;
	pDispInfo->bm.BltVer=0;
	pDispInfo->bm.bltf=0;
	pDispInfo->bm.PalNo=0;
	pDispInfo->type=DITYPE_BMP_NOMAL;
#endif

	// 表示カウンタープラス
	DispBuffer.DispCnt++;
}

/* スプライト确认プロセス ********************************************************************/
void SprViewProc( void )
{
	char szMoji[ 256 ];
	int bmpX;
	int bmpY;
	static int bmpNo = 0;
	int bmpNoBak;
	static int no = 0; // 增分番号
	static BOOL fontFlag = TRUE; // フォント表示フラグ
	// 增分テーブル
#ifdef PUK2
	int zoubunTbl[] ={   1,  5,  25,  100, 	500,  1000, 10000, 100000,
						-1, -5, -25, -100, -500, -1000, -10000, -100000 };
#else
	int zoubunTbl[] ={   1,  5,  25,  100, 	500,  1000,
						-1, -5, -25, -100, -500, -1000 };
#endif
	static int palNo = 0; // パレット番号
	static int time = 600; // パレット番号
	
	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
	
		// パレットを元に戾す
		case 0:
			// パレットチェンジ
			PaletteChange( 0, 0 );
			// サブプロセス番号プラス
			SubProcNo++;
			break;
		
		
		case 1:
			// バックバッファー描画方法で分岐
			//BackBufferDrawType = DRAW_BACK_NON
			// キーボードで操作する时
			// 进ませる
			if( VK[ VK_RIGHT ] & KEY_ON_REP ) bmpNo += zoubunTbl[ no ];
			// 返回
			if( VK[ VK_LEFT ] & KEY_ON_REP ) bmpNo -= zoubunTbl[ no ];
#ifdef PUK2
			// 增分プラス
			if( VK[ VK_UP ] & KEY_ON_REP ){ 
				no++;
				if( no >= 8 ) no = 0; // リミットチェック
			}
			// 增分マイナス
			if( VK[ VK_DOWN ] & KEY_ON_REP ){
				no--;
				if( no <= -1 ) no = 7; // リミットチェック
			}
#else
			// 增分プラス
			if( VK[ VK_UP ] & KEY_ON_REP ){ 
				no++;
				if( no >= 6 ) no = 0; // リミットチェック
			}
			// 增分マイナス
			if( VK[ VK_DOWN ] & KEY_ON_REP ){
				no--;
				if( no <= -1 ) no = 5; // リミットチェック
			}
#endif
#if 0
			// マウスで操作する时
			// 右键で进む
			if( mouse.onceState & MOUSE_LEFT_CRICK ){
				bmpNo += zoubunTbl[ no ];
				// クリック音
				//play_se( 201, 320, 240 );
			}
			// 真中クリックで早送り
			if( GetAsyncKeyState( VK_MBUTTON ) ){
				bmpNo += zoubunTbl[ no ];
			}
			
			// 按鼠标右键增分变更
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			//if( mouse.state & MOUSE_RIGHT_CRICK ){
				no++;
				if( no == 6 ) no = 0; // リミットチェック
				// クリック音
				//play_se( 201, 320, 240 );
			}
#endif			
			// リミットチェック
			if( bmpNo < 0 ) bmpNo = 0;
			if( bmpNo >= MAX_GRAPHICS - 25 ) bmpNo = MAX_GRAPHICS -25;
			
			// パレット变更
			if( VK[ VK_Z ] & KEY_ON_REP ){	/* プラス */
				palNo--;
				if( palNo < 0 ) palNo = MAX_PAL - 1;
				// パレットチェンジ
				PaletteChange( palNo, 10 );
				// ウィンドウ开く音
				//play_se( 202, 320, 240 );
			}
			if( VK[ VK_X ] & KEY_ON_REP ){	/* マイナス */
				palNo++;
				if( palNo >= MAX_PAL ) palNo = 0;
				// パレットチェンジ
				PaletteChange( palNo, 10 );
				// ウィンドウ开く音
				//play_se( 202, 320, 240 );
			}
			// フォントフラグ变更
			if( VK[ VK_DELETE ] & KEY_ON_REP ){	/* プラス */
				if( fontFlag ) fontFlag = 0;
				else fontFlag = 1;
				// アイテム使う音
				//play_se( 212, 320, 240 );
			}
			
			// 背景色变更
			if( VK[ VK_PRIOR ] & KEY_ON_REP ){	/* プラス */
				if( backColor == 0 ) backColor = SYSTEM_PAL_GRAY;
				else if( backColor == SYSTEM_PAL_WHITE ) backColor = 0;
				else backColor++;
				// アイテム使う音
				//play_se( 212, 320, 240 );
			}
			
			// ＥＳＣで終了
			if( VK[ VK_ESCAPE ] & KEY_ON_REP ){	/* マイナス */
#if defined(PUK2)&&defined(_DEBUG)
				GetWindowText( hWnd, szMoji, 256 );
				// ウィンドウタイトルに"sprview"の文字列があったら、サーバ选择画面に返回
				if ( strstr( szMoji, "sprview" ) ) ChangeProc2( PROC_TITLE_MENU ); //MLHIDE
				// 无ければ、ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				else PostMessage( hWnd, WM_CLOSE, 0, 0L );
				offlineFlag = FALSE;
				return;
#else
				// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				PostMessage( hWnd, WM_CLOSE, 0, 0L );
#endif
			}
			
			// ＢＭＰ番号バックアップ
			bmpNoBak = bmpNo;
			
#ifdef PUK2
			if( VK[ VK_NUMPAD8 ] & KEY_ON_ONCE ) CreateDirectory( "cutspr", NULL ); //MLHIDE
#endif
			// 一画面分ループ（２５枚分）
			for( bmpY = 0 ; bmpY < 480 ; bmpY += 96 ){
				for( bmpX = 0 ; bmpX < 640 ; bmpX += 128 ){
					// フォントフラグＯＮの时
					if( fontFlag == TRUE ){
						// 通し番号表示
						wsprintf( szMoji,"%7d", bmpNoBak );                             //MLHIDE
						StockFontBuffer( bmpX, bmpY, FONT_PRIO_BACK, FONT_PAL_WHITE, szMoji, 0 );
						
						// ＢＭＰ番号表示
						int realBmpNo = realGetBitmapNo( bmpNoBak );
						// ０じゃない时
						if( realBmpNo != 0 ){
							wsprintf( szMoji,"%7d", realBmpNo );                           //MLHIDE
							StockFontBuffer( bmpX, bmpY + 17, FONT_PRIO_BACK, FONT_PAL_GREEN, szMoji, 0 );
						}else{
						}
						
						
						// リアルゲットナンバー
						//realGetNo( bmpNoBak , &work );
						//wsprintf( szMoji,"%7d", work );
						//StockFontBuffer( bmpX, bmpY + 16, FONT_PRIO_BACK, 0, szMoji, 0 );
					}
#ifdef PUK2
					if( VK[ VK_NUMPAD8 ] & KEY_ON_ONCE ) Save_bmp( bmpNoBak );
#endif
					// ＢＭＰ表示バッファにためる
					StockDispBufferSprView( bmpX, bmpY, DISP_PRIO_TILE, bmpNoBak++ );
					//StockDispBuffer( bmpX, bmpY, DISP_PRIO_TILE, bmpNoBak++, 0 );
#ifdef PUK2
					if ( GetAsyncKeyState(VK_NUMPAD0)<0 ) break;
#endif
				}
#ifdef PUK2
				if ( GetAsyncKeyState(VK_NUMPAD0)<0 ) break;
#endif
			}
			// フォントフラグＯＮの时
			if( fontFlag == TRUE ){
				// 增分表示
				wsprintf( szMoji, "PAL:%2d　増分:%6d", palNo, zoubunTbl[ no ] );     //MLHIDE
				StockFontBuffer( 640 - 16 * 12, 462, FONT_PRIO_FRONT, 0, szMoji, 0 );
					
				// 背景色变更の说明
#ifdef PUK2
				wsprintf( szMoji, "PageUp：背景色変更 NUM0：一枚表示 NUM8：ビットマップの生成" );      //MLHIDE
#else
				wsprintf( szMoji, "PageUp：背景色変更" );                               //MLHIDE
#endif
				StockFontBuffer( 4, 440, FONT_PRIO_FRONT, 0, szMoji, 0 );
				// 終了表示
				StockFontBuffer( 4, 462, FONT_PRIO_FRONT, 0, "ESC:終了　Z or X:PAL変更　DEL:文字 ON/OFF", 0 ); //MLHIDE
			}
			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			// 背景描画
			if( backColor != 0 ) StockBoxDispBuffer( 0, 0, 640, 480, DISP_PRIO_BG, backColor , 1 );
			
			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif

			break;
	}
}

#ifdef PUK2
	char AnimCheckmode = 1;
#endif
#ifdef PUK3_ANIMVIEW_MOVECHARA
	int pActX = 320, pActY = 360;
#endif
#ifdef PUK3_RIDEBIN
	ACTION *pActCd;
	int pActCdSpr = SPRSTART, pActCdAng = 5, pActCdAct = 11;
	int CdMakeSprNo;
#endif
/* アニメーションアクション处理 ***********************************************/
void AnimSpr( ACTION *pAct )
{
	char szMoji[ 256 ];
	int x = 32, y = 32;
	int c = 0;
#ifdef MULTI_GRABIN
	int sprNo;
#endif
	static int palNo = 0;
	static int anim_spd = ANM_NOMAL_SPD;
	static int slowFlag = FALSE;
	static int hitNo = 0;
	static int hitSeFlag = TRUE;
#ifdef PUK2
	static char scale[6]={0,1};		// 小数点は、要素１と２の间
	static int scaleCur = 0;		// スケールを变更するためのカーソル位置
	int i,j;
#endif
#ifdef PUK3_RIDEBIN
	static int CdShowFlag = 1;
#endif
	
	/* 状态で分岐 */
	switch( pAct->state ){
		/* 通常时 */
		case ACT_STATE_ACT:
			// スローでない时
#ifdef PUK2
			if( slowFlag == FALSE && (AnimCheckmode == 1||AnimCheckmode == 0) && !(VK[ VK_CONTROL ] & KEY_ON) ){
#else
			if( slowFlag == FALSE ){
#endif
				// スプライト番号变更（±１）
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* プラス */
					while( 1 ){
						pAct->anim_chr_no++;
#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pAct->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ){
							break;
						}
#else
						if( SpriteData[pAct->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
#endif
						if( pAct->anim_chr_no > 1000000 ){
							 pAct->anim_chr_no = 1000000;
							 break;
						}
					}
#ifdef PUK2_SUMMON_HIT
/***
			{
				pAct->anim_no = ANIM_STAND;
				pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP );

				pAct->bmpNo = -1;
				pAct->anim_no = ANIM_MAGIC;
				pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP );

				if( pAct->anim_chr_no < SPRSTART 		// アニメキャラ番号の范围でないなら
					||  pAct->anim_chr_no >= SPREND 		// そのままの番号のスプライト表示
					);
				else if ( pAct->bmpNo >= 0 ){
					int i;
					int j;
					int k;

					pAct->anim_no = ANIM_STAND;
					pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP );

					pAct->anim_no = ANIM_MAGIC;
					for(i=0;i<8;i++){
						pAct->anim_ang = i;
						j = 0;
						k = 0;
						for(;;){
							// アニメーションが終わったら
							if ( pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP ) == 1 ){
								if( pAct->anim_hit >= ANIM_HIT_BACK_NUM ){
									if( pAct->anim_hit >= ANIM_HIT_BACK_NUM && pAct->anim_hit < ANIM_HIT_STOP_NUM ) k++;
									j++;
								}
								if ( j > 1 ){
									char s[256];
									sprintf( s, "方向 %d", i );
									MessageBox( hWnd, s, "HIT_BACKやHIT_STOPが複数有る", MB_OK );
								}
								if ( k != 1 ){
									char s[256];
									sprintf( s, "方向 %d", i );
									MessageBox( hWnd, s, "HIT_BACKが１個でない", MB_OK );
								}
								break;
							}else{
								if( pAct->anim_hit >= ANIM_HIT_BACK_NUM ){
									if( pAct->anim_hit >= ANIM_HIT_BACK_NUM && pAct->anim_hit < ANIM_HIT_STOP_NUM ) k++;
									j++;
								}
							}
							pAct->anim_hit = 0;
						}
					}
				}
			}
***/
#endif
				}
				if( VK[ VK_LEFT ] & KEY_ON_REP ){		/* マイナス */
					while( 1 ){
						pAct->anim_chr_no--;

#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pAct->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ){
							break;
						}
#else
						if( SpriteData[pAct->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
#endif
						if( pAct->anim_chr_no <  SPRSTART ){ 
							pAct->anim_chr_no =  SPRSTART;
							break;
						}
					}
				}
				// スプライト番号变更（±１０００）
				if( VK[ VK_INSERT ] & KEY_ON_REP ){	/* プラス */
					pAct->anim_chr_no += 1000;
					while( 1 ){
						pAct->anim_chr_no++;
#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pAct->anim_chr_no - SPRSTART );
						if( sprNo >= 0 )break;
#else
						if( SpriteData[pAct->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
#endif
						if( pAct->anim_chr_no > 1000000 ){ 
							pAct->anim_chr_no = 1000000;
							break;
						}
					}
				}
				if( VK[ VK_DELETE ] & KEY_ON_REP ){	/* マイナス */
					pAct->anim_chr_no -= 1000;
					while( 1 ){
						pAct->anim_chr_no--;
#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pAct->anim_chr_no - SPRSTART );
						if( sprNo >= 0 )break;
#else
						if( SpriteData[pAct->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
#endif
						if( pAct->anim_chr_no <  SPRSTART ){ 
							pAct->anim_chr_no =  SPRSTART;
							break;
						}
					}
				}
				// アニメーション有り无しチェック
				//while( 1 ){
				//if( SpriteData[ pAct->anim_chr_no ].ptAnimlist == NULL )
				//{
					
				
				// 方向变换
				if( VK[ VK_UP ] & KEY_ON_REP ){		/* プラス */
					pAct->anim_ang++;
					if( pAct->anim_ang >= 8 ) pAct->anim_ang = 0;
				}
				if( VK[ VK_DOWN ] & KEY_ON_REP ){		/* マイナス */
					pAct->anim_ang--;
					if( pAct->anim_ang < 0 ) pAct->anim_ang =  7;
				}
				
				// アニメーション番号表示
				if( VK[ VK_HOME ] & KEY_ON_REP ){		/* プラス */
					pAct->anim_no++;
					if( pAct->anim_no >= ANIM_KIND_MAX ) pAct->anim_no = 0;
				}
				if( VK[ VK_END ] & KEY_ON_REP ){	/* マイナス */
					pAct->anim_no--;
					if( pAct->anim_no < 0 ) pAct->anim_no = ANIM_KIND_MAX - 1;
				}
				
				// スピード变更
				if( VK[ VK_Z ] & KEY_ON_REP ){	/* プラス */
					anim_spd--;
					if( anim_spd < 0 ) anim_spd = 0;
				}
				if( VK[ VK_X ] & KEY_ON_REP ){	/* マイナス */
					anim_spd++;
					if( anim_spd >= 255 ) anim_spd = 255;
				}
			}
#ifdef PUK3_RIDEBIN
			else if( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ){
				// スプライト番号变更（±１）
				if( (VK[ VK_RIGHT ]&KEY_ON_REP) || (VK[ VK_INSERT ]&KEY_ON_REP) ){
					while( 1 ){
						if (VK[ VK_RIGHT ]&KEY_ON_REP) pActCd->anim_chr_no++;
						else pActCd->anim_chr_no += 1000;
	#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pActCd->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ) break;
	#else
						if( SpriteData[pActCd->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
	#endif
						if( pActCd->anim_chr_no > 1000000 ){ pActCd->anim_chr_no = 1000000; break; }
					}
				}
				if( (VK[ VK_LEFT ]&KEY_ON_REP) || (VK[ VK_DELETE ]&KEY_ON_REP) ){
					while( 1 ){
						if (VK[ VK_LEFT ]&KEY_ON_REP) pActCd->anim_chr_no--;
						else pActCd->anim_chr_no -= 1000;
	#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pActCd->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ) break;
	#else
						if( SpriteData[pActCd->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
	#endif
						if( pActCd->anim_chr_no <  SPRSTART ){ pActCd->anim_chr_no =  SPRSTART; break; }
					}
				}
				// 方向变换
				if( VK[ VK_UP ] & KEY_ON_REP ){
					pActCd->anim_ang++;
					if( pActCd->anim_ang >= 8 ) pActCd->anim_ang = 0;
				}
				if( VK[ VK_DOWN ] & KEY_ON_REP ){
					pActCd->anim_ang--;
					if( pActCd->anim_ang < 0 ) pActCd->anim_ang =  7;
				}
				
				// アニメーション番号表示
				if( VK[ VK_HOME ] & KEY_ON_REP ){
					pActCd->anim_no++;
					if( pActCd->anim_no >= ANIM_KIND_MAX ) pActCd->anim_no = 0;
				}
				if( VK[ VK_END ] & KEY_ON_REP ){
					pActCd->anim_no--;
					if( pActCd->anim_no < 0 ) pActCd->anim_no = ANIM_KIND_MAX - 1;
				}
			}
#endif
#ifdef PUK2
			else if( AnimCheckmode == 2 ){
				if( VK[ VK_LEFT ] & KEY_ON_REP ){	/* カーソル左移动 */
					scaleCur--;
					if ( scaleCur < 0 ) scaleCur = 0;
				}
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* カーソル右移动 */
					scaleCur++;
					if ( scaleCur >= 6 ) scaleCur = 6 - 1;
				}
				if( VK[ VK_DOWN] & KEY_ON_REP ){	/* マイナス */
					scale[scaleCur]--;
					if ( scale[scaleCur] < 0 ) scale[scaleCur] = 9;
				}
				if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
					scale[scaleCur]++;
					if ( scale[scaleCur] >= 10 ) scale[scaleCur] = 0;
				}
				pAct->scaleX = 0;
				for(i=6-1,j=1;i>=0;i--,j*=10){
					pAct->scaleX += scale[i] * j;
				}
				pAct->scaleX /= 10000;
				pAct->scaleY = pAct->scaleX;
			}
#endif
#ifdef PUK3_ANIMVIEW_MOVECHARA
			else if( AnimCheckmode == 4 ){
				if( VK[ VK_LEFT ] & KEY_ON_REP ){	/* カーソル左移动 */
					pAct->x--;
				}
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* カーソル右移动 */
					pAct->x++;
				}
				if( VK[ VK_DOWN] & KEY_ON_REP ){	/* マイナス */
					pAct->y++;
				}
				if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
					pAct->y--;
				}
				pActX = pAct->x;
				pActY = pAct->y;
			}
#endif
#ifdef PUK2
			// モード变更
			if ( VK[ VK_M ] & KEY_ON_REP ){
				AnimCheckmode++;
	#ifdef PUK3_ANIMVIEW_MOVECHARA
				if ( AnimCheckmode > 4 ) AnimCheckmode = 0;
	#else
	#ifdef PUK3_RIDEBIN
				if ( AnimCheckmode > 3 ) AnimCheckmode = 0;
	#else
				if ( AnimCheckmode > 2 ) AnimCheckmode = 0;
	#endif
	#endif
			}
#endif
			// 背景色变更
			if( VK[ VK_PRIOR ] & KEY_ON_REP ){	/* プラス */
				if( backColor == 0 ) backColor = SYSTEM_PAL_GRAY;
				else if( backColor == SYSTEM_PAL_WHITE ) backColor = 0;
				else backColor++;
				// アイテム使う音
				//play_se( 212, 320, 240 );
			}
			// ＥＳＣで終了
			if( VK[ VK_ESCAPE ] & KEY_ON_REP ){	/* マイナス */
#if defined(PUK2)&&defined(_DEBUG)
				GetWindowText( hWnd, szMoji, 256 );
				// ウィンドウタイトルに"animview"の文字列があったら、サーバ选择画面に返回
				if ( strstr( szMoji, "animview" ) ) ChangeProc2( PROC_TITLE_MENU ); //MLHIDE
				// 无ければ、ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				else PostMessage( hWnd, WM_CLOSE, 0, 0L );
				DeathAllAction();
				offlineFlag = FALSE;
				return;
#else
				// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				PostMessage( hWnd, WM_CLOSE, 0, 0L );
#endif
			}
#ifdef PUK3_RIDEBIN
			// RETURNで座标设定モードへ
			if( VK[ VK_RETURN ] & KEY_ON_REP ){
				CdMakeSprNo = pAct->anim_chr_no;
				ChangeProc2( PROC_COORDINATE_MAKE );
				pActCdSpr = pActCd->anim_chr_no;
				pActCdAng = pActCd->anim_ang;
				pActCdAct = pActCd->anim_no;
				DeathAllAction();
				return;
			}
#endif
			
			
			// コマ送りフラグＯＮ
			if( VK[ VK_F12 ] & KEY_ON_REP ){ 
				pAct->anim_frame_cnt = 0;
				slowFlag = TRUE;
			}
			// コマ戾しフラグＯＮ
			if( VK[ VK_F11 ] & KEY_ON_REP ){ 
				pAct->anim_cnt -= 2;
				// リミットチェック
				if( pAct->anim_cnt <= 0 ) pAct->anim_cnt = 0;
				pAct->anim_frame_cnt = 0;
				slowFlag = TRUE;
			}
			// コマ送りフラグＯＦＦ
			if( VK[ VK_F9 ] & KEY_ON_REP ) slowFlag = FALSE;
			
			if( slowFlag == FALSE || VK[ VK_F11 ] & KEY_ON_REP || VK[ VK_F12 ] & KEY_ON_REP ){
#ifdef PUK2
				// アニメーション
				if (pAct->anim_chr_no<1000000) pattern( pAct, anim_spd, ANM_LOOP );
				else pAct->bmpNo=-1;
#else
				// アニメーション
				pattern( pAct, anim_spd, ANM_LOOP );
#endif
			}
#ifdef PUK3_RIDEBIN
			// 座标上絵表示切り替え
			if( ( AnimCheckmode == 3  || (VK[ VK_CONTROL ] & KEY_ON) ) && (VK[ VK_SPACE ]&KEY_ON_REP) ){
				CdShowFlag++;
				if (CdShowFlag>3) CdShowFlag = 0;
			}

			if (pActCd){
				pActCd->anim_ang = pAct->anim_ang;
				pActCd->x = pAct->x + (int)(pAct->anim_cdx*pAct->scaleX);
				pActCd->y = pAct->y + (int)(pAct->anim_cdy*pAct->scaleY);
				pattern( pActCd, ANM_NOMAL_SPD, ANM_LOOP );

				pActCd->atr = 0;
				if (CdShowFlag&1){
					StockBoxDispBuffer( pActCd->x-50, pActCd->y-1, pActCd->x, pActCd->y+2, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x+1, pActCd->y-1, pActCd->x+50, pActCd->y+2, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x-1, pActCd->y-50, pActCd->x+2, pActCd->y, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x-1, pActCd->y+1, pActCd->x+2, pActCd->y+50, DISP_PRIO_WIN, 0, 1 );
				}
				if ( !(CdShowFlag&2) ){
					pActCd->atr = ACT_ATR_HIDE;
				}
			}
#endif
			
			// ヒットＳＥフラグ切り替え
			if( VK[ VK_F4 ] & KEY_ON_REP ){
				if( hitSeFlag == TRUE ) hitSeFlag = FALSE;
				else hitSeFlag = TRUE;
			}
			
			// 当たり判定の时
			if( pAct->anim_hit >= ANIM_HIT_BACK_NUM ){
				hitNo = pAct->anim_hit; // 当たり判定番号记忆
				
				// 一时的に上にあげる
				pAct->y -= 128;
				
				// ヒットバックのとき
				if( !( pAct->anim_hit >= ANIM_HIT_STOP_NUM ) ){
					// ヒット音
					if( hitSeFlag == TRUE ) play_se( pAct->anim_hit - ANIM_HIT_BACK_NUM, pAct->x, pAct->y );
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pAct, SPR_critica1 );
				}else{
					// ヒット音
					if( hitSeFlag == TRUE ) play_se( pAct->anim_hit - ANIM_HIT_STOP_NUM, pAct->x, pAct->y );
					// 战闘ヒットマークタスク作成
					MakeBattleHitMark( pAct, SPR_hit01 + Rnd( 0, 2 ) );
				}
				
				// 位置戾す
				pAct->y += 128;
				
				
				pAct->anim_hit = 0;
				
			}
#ifdef PUK3_ANIMVIEW_MOVECHARA
			if( AnimCheckmode == 4  ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　表示位置変更モード", 0 ); //MLHIDE
				wsprintf( szMoji, "ｘ座標 = %8d ： Left or Right ( ±1 )", pAct->x );  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "ｙ座標 = %8d ： Down or UP", pAct->y );            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else
#endif
#ifdef PUK3_RIDEBIN
			if( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　座標上絵設定モード", 0 ); //MLHIDE
				// 座标上スプライト番号表示
				wsprintf( szMoji, "座標上動画番号 = %8d ： Left or Right ( ±1 )", pActCd->anim_chr_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				StockFontBuffer( x + 236, y, FONT_PRIO_FRONT, 0, "： DEL or INS ( ±1000 )", 0 ); y += 20; //MLHIDE
				// 方向表示
//				wsprintf( szMoji, "座標上　方　向 = %8d ： Down or UP", pActCd->anim_ang );
//				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// アニメーション番号表示
				wsprintf( szMoji, "座標上行動番号 = %8d ： END or HOME", pActCd->anim_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "座標上表示切替 ： SPACE" );                            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else
#endif
#ifdef PUK2
			if( AnimCheckmode == 2 ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　縮尺設定モード", 0 );   //MLHIDE
				// 缩尺
				sprintf( szMoji, "　　　　　　　 %c%c %c%c%c%c ： Left ←, Right →, Up ＋, Down －", //MLHIDE
					(scaleCur==0?'_':' '), (scaleCur==1?'_':' '), (scaleCur==2?'_':' '),
					(scaleCur==3?'_':' '), (scaleCur==4?'_':' '), (scaleCur==5?'_':' ') );
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 );
				sprintf( szMoji, "縮尺　　　　　 = %s%0.4f", (pAct->scaleX>=10.0f?"":" "), pAct->scaleX ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else if (AnimCheckmode == 1){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　アニメーション確認モード", 0 ); //MLHIDE
#endif
			// スプライト番号表示
			wsprintf( szMoji, "スプライト番号 = %8d ： Left or Right ( ±1 )", pAct->anim_chr_no ); //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			StockFontBuffer( x + 236, y, FONT_PRIO_FRONT, 0, "： DEL or INS ( ±1000 )", 0 ); y += 20; //MLHIDE
			// 方向表示
			wsprintf( szMoji, "方　　　　　向 = %8d ： Down or UP", pAct->anim_ang );  //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// アニメーション番号表示
			wsprintf( szMoji, "行　動　番　号 = %8d ： END or HOME", pAct->anim_no );  //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// 增分表示
			wsprintf( szMoji, "ス　ピ　ー　ド = %8d ： Z or X", anim_spd );            //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			
			// 效果音番号
			wsprintf( szMoji, "効果音　番　号 = %8d", t_music_se_no );                //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// 效果音ある时
			if( t_music_se_no != -1 ){
				// 效果音の音量
				if( VK[ VK_F8 ] & KEY_ON_REP ){		/* プラス */
					tone_tbl[ t_music_se_no ].voice_volume++;
					if( tone_tbl[ t_music_se_no ].voice_volume >= 128 ) tone_tbl[ t_music_se_no ].voice_volume = 127;
				}
				if( VK[ VK_F7 ] & KEY_ON_REP ){	/* マイナス */
					tone_tbl[ t_music_se_no ].voice_volume--;
					if( tone_tbl[ t_music_se_no ].voice_volume <= -1 ) tone_tbl[ t_music_se_no ].voice_volume = 0;
				}
				
				// 效果音のピッチ
				if( VK[ VK_F6 ] & KEY_ON_REP ){	/* プラス */
					tone_tbl[ t_music_se_no ].voice_note++;
					if( tone_tbl[ t_music_se_no ].voice_note + tone_tbl[ t_music_se_no ].voice_rate >= 63 ) tone_tbl[ t_music_se_no ].voice_note = 62 - tone_tbl[ t_music_se_no ].voice_rate;
				}
				if( VK[ VK_F5 ] & KEY_ON_REP ){	/* マイナス */
					tone_tbl[ t_music_se_no ].voice_note--;
					if( tone_tbl[ t_music_se_no ].voice_note + tone_tbl[ t_music_se_no ].voice_rate <= 0 ) tone_tbl[ t_music_se_no ].voice_note = -tone_tbl[ t_music_se_no ].voice_rate + 1;
				}
				// 效果音音量
				wsprintf( szMoji, "効果音　音　量 = %8d ： F7 or F8", tone_tbl[ t_music_se_no ].voice_volume ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 效果音ピッチ
				wsprintf( szMoji, "効果音　ピッチ = %8d ： F5 or F6", tone_tbl[ t_music_se_no ].voice_note ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else{
				// 效果音音量
				wsprintf( szMoji, "効果音　音　量 = %8d ： F7 or F8", 0 );                //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 效果音ピッチ
				wsprintf( szMoji, "効果音　ピッチ = %8d ： F5 or F6", 0 );                //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}
			
			// ＢＭＰ番号
			//wsprintf( szMoji, "ＢＭＰ　番　号 = %8d", pAct->bmpNo );
			//StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// サイズ表示
			//wsprintf( szMoji, "ＢＭＰ　サイズ = %4d,%4d", SpriteInfo[ pAct->bmpNo ].width, SpriteInfo[ pAct->bmpNo ].height );
			//StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
						
#ifdef PUK2
			// 当たり判定番号
			wsprintf( szMoji, "当たり判定番号 = %8d ????????????番号 = %8d", hitNo, pAct->bm.PalNo ); //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
#else
			// 当たり判定番号
			wsprintf( szMoji, "当たり判定番号 = %8d", hitNo );                        //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
#endif
#ifdef PUK2
			}
#endif
			
			// 行动番号の表示
			StockFontBuffer( 320 - GetStrWidth( animStr[ pAct->anim_no ] ) / 2, 420, FONT_PRIO_FRONT, 0, animStr[ pAct->anim_no ], 0 );
			
#ifdef PUK3_RIDEBIN
			// 背景色、座标絵の说明
			wsprintf( szMoji, "PageUp:背景色変更　M:モード切替　CTRL:座標絵モード ON/OFF　RETURN:座標設定" ); //MLHIDE
			StockFontBuffer( 4, 440, FONT_PRIO_FRONT, 0, szMoji, 0 );
#else
			// 背景色变更の说明
			wsprintf( szMoji, "PageUp:背景色変更" );                                //MLHIDE
			StockFontBuffer( 4, 440, FONT_PRIO_FRONT, 0, szMoji, 0 );
#endif
			
			// コマ送りの说明、ヒット音の说明、終了表示
			wsprintf( szMoji, "F12:コマ送り　F11:コマ戻し　F9:ＯＦＦ　F4:ヒット音 ON/OFF　ESC:終了" ); //MLHIDE
			StockFontBuffer( 4, 460, FONT_PRIO_FRONT, 0, szMoji, 0 );
#if 0
			// 一覧の表示
			if( VK[ VK_L ] & KEY_ON_REP ){	/* プラス */
				ANIMLIST	*ptAnimlist;
				FRAMELIST	*ptFramelist;
				FILE *fp;
				int char_no = 0, kind, now;
				int pagelist[100];
				fp = fopen( "animlist.txt", "w" );                                //MLHIDE
				for( char_no = 0; char_no < mxSPRITE; char_no ++ ){
					ptAnimlist = SpriteData[ char_no ].ptAnimlist;	// １アニメーションの情报取りだし
					// キャラの最后までいったら終わり。
					if( ptAnimlist ==NULL ) continue;
					if( SpriteData[ char_no ].animSize == 0 )continue;// アニメーションデータが无いなら拔ける

					// そのキャラの行动の数分ループする。
					for( kind = 0; kind < ANIM_LIST_; kind ++ ){
						int maxcount = 0, cnt ;
						// ページリスト初期化
						memset( pagelist, 0xFF, sizeof( pagelist ) );
						// 方向はどれでもよいので检索
						for( now = 0; now < SpriteData[ char_no ].animSize; now ++ ){
							// 方向０のものだけでよいので检索
							if( ptAnimlist[ now ].no == kind )break;
						}
						// 见つからなかったら次の行动へ
						if( now >= SpriteData[ char_no ].animSize )continue;

						// そのフレームの构造体ポインタをセット
						ptFramelist = ptAnimlist[ now ].ptFramelist;

						// アニメーションの枚数でループ。何种类のBMPが使われてるか。
						for( cnt = 0; cnt < ptAnimlist[ now ].frameCnt; cnt ++ ){
							int kk;
							for( kk = 0; kk < maxcount; kk ++ ){
								// 同じBMPがあるか
								if( pagelist[kk] == ptFramelist[ cnt ].BmpNo )break;
							}
							if( kk >= maxcount ){
								// 新発见だ。
								pagelist[maxcount] = ptFramelist[ cnt ].BmpNo;
								maxcount ++;
							}
						}
						fprintf( fp, "%d %20s Frame= %3d 枚数=%3d\n",                     //MLHIDE
							char_no + SPRSTART,	// キャラ番号
							animStr[kind],	// 行动
							ptAnimlist[ now ].frameCnt,
							maxcount
						);
							// アニメのフレーム数をプリント
					}
					fprintf( fp, "\n" );                                             //MLHIDE
				}
				fclose( fp );
			}
#endif

			break;
		
		/* 死亡时 */	
		case ACT_STATE_DEAD:
		
			DeathAction( pAct );
			
			break;
	}
}

/* アニメーションアクション作成 ***********************************************/
ACTION *MakeAnimSpr( void )
{
	ACTION *pAct;
	
	/* アクションリストに登録 */
	pAct = GetAction( PRIO_JIKI, 0 );
	if( pAct == NULL ) return NULL;
	
	/* 实行关数 */
	pAct->func = AnimSpr;
	// anim_tbl.h の番号
	pAct->anim_chr_no = SPRSTART; 
	// 动作番号
	pAct->anim_no = 0;//ANIM_ATTACK;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 5;//Rnd( 0, 7 );
	// ボックス表示
	pAct->atr |= ACT_ATR_HIT_BOX;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_CHAR;
	/* 初期位置 */
#ifdef PUK3_ANIMVIEW_MOVECHARA
	pAct->x = pActX;
	pAct->y = pActY;
#else
	pAct->x = 320;
	pAct->y = 360;
#endif
	
	// アニメーション
	pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
	
	return pAct;
}

/* アニメーション确认プロセス ********************************************************************/
void AnimViewProc( void )
{
	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
	
		case 0:
			
#ifdef PUK3_RIDEBIN
			// 座标を示す矢印作成
			{
				/* アクションリストに登録 */
				pActCd = GetAction( PRIO_ENEMY, 0 );

				/* 实行关数 */
				pActCd->func = NULL;
				// anim_tbl.h の番号
				pActCd->anim_chr_no = pActCdSpr; 
				// 动作番号
				pActCd->anim_no = pActCdAct;
				// アニメーション向き( ０～７ )( 下が０で右回り )
				pActCd->anim_ang = pActCdAng;
				// ボックス表示
				pActCd->atr = 0;
				/* 表示优先度 */
				pActCd->dispPrio = DISP_PRIO_WIN2;
				/* 初期位置 */
#ifdef PUK3_ANIMVIEW_MOVECHARA
				pActCd->x = pActX;
				pActCd->y = pActY;
#else
				pActCd->x = 320;
				pActCd->y = 360;
#endif
			}
#endif
			// アニメーションアクション作成
			MakeAnimSpr();
			// 战闘ＢＧＭ鸣らす
			play_bgm( 1 );
			// パレットチェンジ
			PaletteChange( 0, 0 );
			// サブプロセス番号プラス
			SubProcNo++;
			
			break;
			
		case 1:
		
			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			// 背景描画
			if( backColor != 0 ) StockBoxDispBuffer( 0, 0, 640, 480, DISP_PRIO_BG, backColor , 1 );
			
			break;
#ifdef PUK3_RIDEBIN
		case 2:
			
			// 座标を示す矢印作成
			{
				/* アクションリストに登録 */
				pActCd = GetAction( PRIO_ENEMY, 0 );

				/* 实行关数 */
				pActCd->func = NULL;
				// anim_tbl.h の番号
				pActCd->anim_chr_no = pActCdSpr; 
				// 动作番号
				pActCd->anim_no = pActCdAct;
				// アニメーション向き( ０～７ )( 下が０で右回り )
				pActCd->anim_ang = pActCdAng;
				// ボックス表示
				pActCd->atr = 0;
				/* 表示优先度 */
				pActCd->dispPrio = DISP_PRIO_WIN2;
				/* 初期位置 */
#ifdef PUK3_ANIMVIEW_MOVECHARA
				pActCd->x = pActX;
				pActCd->y = pActY;
#else
				pActCd->x = 320;
				pActCd->y = 360;
#endif
			}
			// アニメーションアクション作成
			{
				ACTION *pAct;
				pAct = MakeAnimSpr();
				if (pAct) pAct->anim_chr_no = CdMakeSprNo;
			}
			// 战闘ＢＧＭ鸣らす
			play_bgm( 1 );
			// パレットチェンジ
			PaletteChange( 0, 0 );
			// サブプロセス番号
			SubProcNo--;
			
			break;
#endif
	}
}

#ifdef PUK3_RIDEBIN

extern FILE *CrdBinfp[BINMODE_MAX];
// 座标データ読み込み关数
BOOL ReadCoordinateData( int sprNo );

extern int BinOffsetTbl[BINMODE_MAX];

BOOL lineflag = TRUE;

//======================================
// 座标データbin书き込み关数
BOOL addDataCoordinatebin( char *datafile, char *infofile, char *logfile,
	 int sprNo, int *GraList, short *XYList, int Max )
{
	FILE *dfp, *ifp, *lfp;
	unsigned char format = 1;
	unsigned long start;
	int i;
	unsigned short cnt;
	int offset;
	int graNo;
	int chrNo, tmpNo;
	char s[255];
	time_t ltime;
	struct tm *ldate;

	chrNo = sprNo - SPRSTART;
#ifdef MULTI_GRABIN
	tmpNo = Spr_Number2Tbl( chrNo );
	if( tmpNo < 0 ) return FALSE;
	chrNo = (UINT)(tmpNo);
#endif
	offset = BinOffsetTbl[ SpriteData[chrNo].BinMode ];

	ifp = fopen( infofile, "rb" );                                       //MLHIDE
	// あるなら后ろに追加
	if (ifp){
		fclose(ifp);

		// infoファイルを开く
		ifp = fopen( infofile, "ab" );                                      //MLHIDE
		if (!ifp) return FALSE;

		// dataファイルを开く
		dfp = fopen( datafile, "ab" );                                      //MLHIDE
		if (!dfp){ fclose(ifp);	return FALSE; }
	}
	// ないならファイル作成
	else{
		// infoファイルを开く
		ifp = fopen( infofile, "wb" );                                      //MLHIDE
		if (!ifp) return FALSE;

		// dataファイルを开く
		dfp = fopen( datafile, "wb" );                                      //MLHIDE
		if (!dfp){ fclose(ifp);	return FALSE; }

		// フォーマット记述
		fwrite( &format, sizeof(format), 1, ifp );
	}

	// 书き込み开始位置を取得
	fseek( dfp, 0, SEEK_END );
	start = ftell(dfp);

	// dataファイルに书き込み
	cnt = 0;
	for(i=0;i<Max;i++){
		// デフォルト值のままなら书き込まない
		if ( XYList[i*2+0] == 0 && XYList[i*2+1] == 0 ) continue;

		graNo = GraList[i] - offset;
		fwrite( &graNo, sizeof(graNo), 1, dfp );				// グラフィック番号
		fwrite( &XYList[i*2], sizeof(XYList[0]), 2, dfp );		// 座标データ
		cnt++;
	}

	// infoファイルに书き込み
	fwrite( &sprNo, sizeof(sprNo), 1, ifp );		// キャラ番号
	fwrite( &start, sizeof(start), 1, ifp );		// 読み取り位置
	fwrite( &cnt, sizeof(cnt), 1, ifp );			// データ数
	
	fclose(ifp);
	fclose(dfp);

	// logファイルを开く
	lfp = fopen( logfile, "rt" );                                        //MLHIDE
	// あるなら后ろに追加
	if (lfp){
		fclose(lfp);

		// logファイルを开く
		lfp = fopen( logfile, "at" );                                       //MLHIDE
		if (!lfp) return FALSE;
	}
	// ないならファイル作成
	else{
		// logファイルを开く
		lfp = fopen( logfile, "wt" );                                       //MLHIDE
		if (!lfp) return FALSE;

		fprintf( lfp, "ＳＰＲ番号 | 書込データ数/最大データ数 | 書き込み先 | 書込日時\n" );           //MLHIDE
	}

	// ＥＸＥ起动后一番最初のみ区切り线を书き込む
	if (lineflag) fprintf( lfp, "----------------------------------------------------------------\n" ); //MLHIDE
	lineflag = FALSE;

	fprintf( lfp, "%12d | %11d / %11d | %10d", sprNo, cnt, Max, start ); //MLHIDE

	time(&ltime);
	ldate = localtime( &ltime );
	strftime( s, sizeof(s), "%Y/%m/%d", ldate );                         //MLHIDE
	fprintf( lfp, " | %s", s );                                          //MLHIDE
	_strtime(s);
	fprintf( lfp, "  %s\n", s );                                         //MLHIDE

	fclose(lfp);

	return TRUE;
}

//======================================
// 座标データbin整理关数
struct COORDINATEINFO{
	unsigned long sprNo;
	unsigned long offset;
	unsigned short size;
	unsigned short maxsize;
};
BOOL arrangeCoordinatebin( char *datafiles, char *infofiles, char *logfiles,
	 char *datafilet, char *infofilet, char *logfilet, unsigned long difspr, int difNum )
{
	FILE *fp, *fp2;
	char s[255];
	time_t ltime;
	struct tm *ldate;
	int i, j, k, cnt, maxcnt;
	struct COORDINATEINFO *cdif;
	int offset;
	char *p;
	ANIMCOORDINATE cd;
	int a,b,c,d;

	fp = fopen( logfiles, "rt" );                                        //MLHIDE
	if (!fp) return FALSE;

	// "ＳＰＲ番号 | 書込データ数/最大データ数 | 書き込み先 | 書込日時\n"を読み飞ばす
	for(;!feof(fp);){
		s[0] = fgetc(fp);
		if (s[0]=='\n') break;
		if ( feof(fp) ){
			fclose(fp);
			return FALSE;
		}
	}

	for(i=0;;){
		j = 0;
		for(;!feof(fp);){
			s[j] = fgetc(fp);
			if (s[j]=='\n') break;
			if (!j) j++;
		}
		if ( feof(fp) ) break;
		if ( s[0] == '-' ) continue;
		i++;
	}
	maxcnt = i;

	// 构造体确保
	cdif = (struct COORDINATEINFO *)malloc( maxcnt * sizeof(struct COORDINATEINFO) );
#ifdef PUK2_MEMCHECK
	memlistset( cdif, MEMLISTTYPE_TESTVIEW );
#endif

	fseek( fp, 0, SEEK_SET );
	offset = 0;

	// "ＳＰＲ番号 | 書込データ数/最大データ数 | 書き込み先 | 書込日時\n"を読み飞ばす
	for(;!feof(fp);){
		s[0] = fgetc(fp);
		if (s[0]=='\n') break;
		if ( feof(fp) ){
			fclose(fp);
			return FALSE;
		}
	}

	cnt = 0;
	for(i=0;i<maxcnt;){
		for(k=0;;){
			s[k] = fgetc(fp);
			if (s[k]==' ') continue;
			if (s[k]=='\n'){ s[k]='\0';	break; }
			k++;
		}
		if ( s[0] == '-' ) continue;

		sscanf( s, "%d|%d/%d|%d", &a, &b, &c, &d );                         //MLHIDE
		cdif[cnt].sprNo = a;
		cdif[cnt].size = b;
		cdif[cnt].maxsize = c;
		cdif[cnt].offset = d;

		if ( cnt == 0 ) offset = cdif[cnt].offset;

		for(k=0;k<cnt;k++){
			if ( cdif[cnt].sprNo == cdif[k].sprNo ) break;
		}
		if ( k < cnt ) cdif[k] = cdif[cnt];
		else cnt++;
		i++;
	}

	fclose(fp);


	// dataファイルの作成
	fp = fopen( datafiles, "rb" );                                       //MLHIDE
	if (!fp){
#ifdef PUK2_MEMCHECK
		memlistrel( cdif, MEMLISTTYPE_TESTVIEW );
#endif
		free(cdif);
		return FALSE;
	}
	fp2 = fopen( datafilet, "wb" );                                      //MLHIDE
	if (!fp2){
#ifdef PUK2_MEMCHECK
		memlistrel( cdif, MEMLISTTYPE_TESTVIEW );
#endif
		free(cdif);
		fclose(fp);
		return FALSE;
	}

	// ログにない部分の书き込み
	if (offset){
		p = (char *)malloc( offset );
#ifdef PUK2_MEMCHECK
		memlistset( p, MEMLISTTYPE_TESTVIEW );
#endif

		fread( p, sizeof(char), offset, fp );
		fwrite( p, sizeof(char), offset, fp2 );

#ifdef PUK2_MEMCHECK
		memlistrel( p, MEMLISTTYPE_TESTVIEW );
#endif
		free(p);
	}

	// ログにある部分の书き込み
	for(i=0;i<cnt;i++){
		fseek( fp, cdif[i].offset, SEEK_SET );
		for(j=0;j<cdif[i].size;j++){
			// データ読み込み
			fread( &cd.graNo, sizeof(cd.graNo), 1, fp );
			fread( &cd.x, sizeof(cd.x), 1, fp );
			fread( &cd.y, sizeof(cd.y), 1, fp );

			// グラフィックの番号を指定分ずらす
			if ( cdif[i].sprNo == difspr ) cd.graNo += difNum;

			// データ书き込み
			fwrite( &cd.graNo, sizeof(cd.graNo), 1, fp2 );
			fwrite( &cd.x, sizeof(cd.x), 1, fp2 );
			fwrite( &cd.y, sizeof(cd.y), 1, fp2 );
		}
	}

	fclose(fp);
	fclose(fp2);


	// infoファイルの作成
	fp = fopen( infofiles, "rb" );                                       //MLHIDE
	if (!fp){
#ifdef PUK2_MEMCHECK
		memlistrel( cdif, MEMLISTTYPE_TESTVIEW );
#endif
		free(cdif);
		remove(datafilet);
		return FALSE;
	}
	fp2 = fopen( infofilet, "wb" );                                      //MLHIDE
	if (!fp2){
#ifdef PUK2_MEMCHECK
		memlistrel( cdif, MEMLISTTYPE_TESTVIEW );
#endif
		free(cdif);
		fclose(fp);
		remove(datafilet);
		return FALSE;
	}

	// 読み込み位置データの更新
	cdif[0].offset = offset;
	for(i=1;i<cnt;i++){
		cdif[i].offset = cdif[i-1].offset + cdif[i-1].size * 8;
	}

	// ログにない部分の书き込み
	{
		fseek( fp, 0, SEEK_END );
		offset = ftell(fp);
	
		offset -= maxcnt * 10;
	
		fseek( fp, 0, SEEK_SET );

		p = (char *)malloc( offset );
#ifdef PUK2_MEMCHECK
		memlistset( p, MEMLISTTYPE_TESTVIEW );
#endif

		fread( p, sizeof(char), offset, fp );
		fwrite( p, sizeof(char), offset, fp2 );
	
#ifdef PUK2_MEMCHECK
		memlistrel( p, MEMLISTTYPE_TESTVIEW );
#endif
		free(p);
	}

	// ログにある部分の书き込み
	for(i=0;i<cnt;i++){
		fwrite( &cdif[i].sprNo, sizeof(cdif[i].sprNo), 1, fp2 );
		fwrite( &cdif[i].offset, sizeof(cdif[i].offset), 1, fp2 );
		fwrite( &cdif[i].size, sizeof(cdif[i].size), 1, fp2 );
	}

	fclose(fp);
	fclose(fp2);

	// ログの作成
	fp2 = fopen( logfilet, "wt" );                                       //MLHIDE
	if (!fp){
#ifdef PUK2_MEMCHECK
		memlistrel( cdif, MEMLISTTYPE_TESTVIEW );
#endif
		free(cdif);
		remove(datafilet);
		remove(infofilet);
		return FALSE;
	}

	fprintf( fp2, "ＳＰＲ番号 | 書込データ数/最大データ数 | 書き込み先 | 書込日時\n" );            //MLHIDE

	fprintf( fp2, "----------------------------------------------------------------\n" ); //MLHIDE

	for(i=0;i<cnt;i++){
		fprintf( fp2, "%12d | %11d / %11d | %10d",                          //MLHIDE
			 cdif[i].sprNo, cdif[i].size, cdif[i].maxsize, cdif[i].offset );
	
		time(&ltime);
		ldate = localtime( &ltime );
		strftime( s, sizeof(s), "%Y/%m/%d", ldate );                        //MLHIDE
		fprintf( fp2, " | %s", s );                                         //MLHIDE
		_strtime(s);
		fprintf( fp2, "  %s\n", s );                                        //MLHIDE
	}

	fclose(fp2);
#ifdef PUK2_MEMCHECK
	memlistrel( cdif, MEMLISTTYPE_TESTVIEW );
#endif
	free(cdif);

	return TRUE;
}

int CdListMax;
int CdListGraNo[1000];
short CdListXY[1000][2];

BOOL SaveCdData()
{
	int chrNo, tmpNo;
	BOOL ret;

	chrNo = CdMakeSprNo - SPRSTART;
#ifdef MULTI_GRABIN
	tmpNo = Spr_Number2Tbl( chrNo );
	chrNo = (UINT)(tmpNo);
#endif
	// binファイルを关闭
	CloseCoordinateBinFiles();

	// 书き込む
/***
	addDataCoordinatebin(
		 databin[SpriteData[chrNo].BinMode],
		 infobin[SpriteData[chrNo].BinMode],
		 logfile[SpriteData[chrNo].BinMode],
		 CdMakeSprNo, &CdListGraNo[0], &CdListXY[0][0], CdListMax );
***/
	ret = addDataCoordinatebin( coordinateBinName[0], coordinateInfoBinName[0], coordinateLogName[0],
		 CdMakeSprNo, &CdListGraNo[0], &CdListXY[0][0], CdListMax );

	// binファイルを再度読み込む
#ifdef _DEBUG
	InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName, coordinateLogName );
#else
	InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName );
#endif
#ifdef PUK2_MEMCHECK
	memlistrel( SpriteData[chrNo].ptCdlist, MEMLISTTYPE_ACTIONCDLIST );
#endif
	if (SpriteData[chrNo].ptCdlist) free(SpriteData[chrNo].ptCdlist);
	SpriteData[chrNo].ptCdlist = NULL;
	// 座标データを更新する
	ReadCoordinateData(chrNo);

	return ret;
}

int InitAnimListBuffer( void );
BOOL arrangeCdData( unsigned long difSpr = 0, int difNum = 0 )
{
	char *databinx[] = { "bin\\xcrd.bin", "bin\\xcrdEx.bin", "bin\\xcrdV2.bin", "bin\\xcrd_PUK2.bin", "bin\\xcrdV3.bin" }; //MLHIDE
	char *infobinx[] = { "bin\\xcrdinfo.bin", "bin\\xcrdinfoEx.bin", "bin\\xcrdinfoV2.bin", "bin\\xcrdinfo_PUK2.bin", "bin\\xcrdinfoV3.bin" }; //MLHIDE
	char *logfilex[] = { "bin\\xcrdlog.txt", "bin\\xcrdlogEx.txt", "bin\\xcrdlogV2.txt", "bin\\xcrdlog_PUK2.txt", "bin\\xcrdlogV3.txt" }; //MLHIDE
	BOOL ret;

	int i = 0;

	// binファイルを关闭
	CloseCoordinateBinFiles();

	ret = TRUE;
	// 整理する
	for(i=0;i<1;i++){
		if ( rename( coordinateBinName[i], databinx[i] ) ){
			// binファイルを再度読み込む
#ifdef _DEBUG
			InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName, coordinateLogName );
#else
			InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName );
#endif
			return FALSE;
		}
		if ( rename( coordinateInfoBinName[i], infobinx[i] ) ){
			rename( databinx[i], coordinateBinName[i] );
			// binファイルを再度読み込む
#ifdef _DEBUG
			InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName, coordinateLogName );
#else
			InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName );
#endif
			return FALSE;
		}
		if ( rename( coordinateLogName[i], logfilex[i] ) ){
			rename( databinx[i], coordinateBinName[i] );
			rename( infobinx[i], coordinateInfoBinName[i] );
			// binファイルを再度読み込む
#ifdef _DEBUG
			InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName, coordinateLogName );
#else
			InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName );
#endif
			return FALSE;
		}

		if ( !arrangeCoordinatebin(
			 databinx[i], infobinx[i], logfilex[i],
			 coordinateBinName[i], coordinateInfoBinName[i], coordinateLogName[i],
			 difSpr, difNum ) ) ret = FALSE;

		remove(databinx[i]);
		remove(infobinx[i]);
		remove(logfilex[i]);
	}

	// binファイルを再度読み込む
#ifdef _DEBUG
	InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName, coordinateLogName );
#else
	InitCoordinateBinFilesOpen( coordinateBinName, coordinateInfoBinName );
#endif
	if ( difSpr ){
		int chrNo, tmpNo;

		chrNo = difSpr - SPRSTART;
	#ifdef MULTI_GRABIN
		tmpNo = Spr_Number2Tbl( chrNo );
		if( tmpNo < 0 ) return FALSE;
		chrNo = (UINT)(tmpNo);
	#endif
	#ifdef PUK2_MEMCHECK
		memlistrel( SpriteData[chrNo].ptCdlist, MEMLISTTYPE_ACTIONCDLIST );
	#endif
		free( SpriteData[chrNo].ptCdlist );
		SpriteData[chrNo].ptCdlist = NULL;
		ReadCoordinateData( chrNo );
	}

	lineflag = TRUE;

	return ret;
}

void CopyCdData( int sprNo )
{
	int i;
	int chrNo, tmpNo;

	chrNo = sprNo - SPRSTART;
#ifdef MULTI_GRABIN
	tmpNo = Spr_Number2Tbl( chrNo );
	if ( tmpNo < 0 ) return;
	chrNo = (UINT)(tmpNo);
#endif

	ReadCoordinateData(chrNo);

	// 既存のデータ読み込み
	for(i=0;i<SpriteData[chrNo].cdSize;i++){
		if ( i >= CdListMax ) break;
		CdListXY[i][0] = SpriteData[chrNo].ptCdlist[i].x;
		CdListXY[i][1] = SpriteData[chrNo].ptCdlist[i].y;
	}
}

// 色违いモンスへのコピー
void CopyJustCdData( int sprNo )
{
	int i, j;
	int chrNo, tmpNo;

	chrNo = sprNo - SPRSTART;
#ifdef MULTI_GRABIN
	tmpNo = Spr_Number2Tbl( chrNo );
	if ( tmpNo < 0 ) return;
	chrNo = (UINT)(tmpNo);
#endif

	ReadCoordinateData(chrNo);

	// 座标データ初期化
	memset( &CdListXY[0][0], 0, CdListMax*2*sizeof(short) );

	// 既存のデータ読み込み
	for(i=0;i<SpriteData[chrNo].cdSize;i++){
		for(j=0;j<CdListMax;j++){
			if ( SpriteData[chrNo].ptCdlist[i].graNo
				 != CdListGraNo[j] ) continue;

			CdListXY[j][0] = SpriteData[chrNo].ptCdlist[i].x;
			CdListXY[j][1] = SpriteData[chrNo].ptCdlist[i].y;
			break;
		}
	}
}

/* アニメーションアクション处理 ***********************************************/
void CdSprAnim( ACTION *pAct )
{
	char szMoji[ 256 ];
	int x = 32, y = 32;
	int c = 0;
#ifdef MULTI_GRABIN
	int sprNo;
#endif
	static int palNo = 0;
	static int anim_spd = ANM_NOMAL_SPD;
	static int slowFlag = FALSE;
	static char scale[6]={0,1};		// 小数点は、要素１と２の间
	static int scaleCur = 0;		// スケールを变更するためのカーソル位置
	int i,j;
#ifdef PUK3_RIDEBIN
	static int CdShowFlag = 1;
#endif
	static char messflag = 0, messcnt = 0;
	
	/* 状态で分岐 */
	switch( pAct->state ){
		/* 通常时 */
		case ACT_STATE_ACT:
			// スローでない时
			if( slowFlag == FALSE && (AnimCheckmode == 1||AnimCheckmode == 0) && !(VK[ VK_CONTROL ] & KEY_ON) ){
				// 方向变换
				if( VK[ VK_UP ] & KEY_ON_REP ){		/* プラス */
					pAct->anim_ang++;
					if( pAct->anim_ang >= 8 ) pAct->anim_ang = 0;
				}
				if( VK[ VK_DOWN ] & KEY_ON_REP ){		/* マイナス */
					pAct->anim_ang--;
					if( pAct->anim_ang < 0 ) pAct->anim_ang =  7;
				}
				
				// アニメーション番号表示
				if( VK[ VK_HOME ] & KEY_ON_REP ){		/* プラス */
					pAct->anim_no++;
					if( pAct->anim_no >= ANIM_KIND_MAX ) pAct->anim_no = 0;
				}
				if( VK[ VK_END ] & KEY_ON_REP ){	/* マイナス */
					pAct->anim_no--;
					if( pAct->anim_no < 0 ) pAct->anim_no = ANIM_KIND_MAX - 1;
				}
				
				// スピード变更
				if( VK[ VK_Z ] & KEY_ON_REP ){	/* プラス */
					anim_spd--;
					if( anim_spd < 0 ) anim_spd = 0;
				}
				if( VK[ VK_X ] & KEY_ON_REP ){	/* マイナス */
					anim_spd++;
					if( anim_spd >= 255 ) anim_spd = 255;
				}
				// ＳＰＡＣＥで保存
				if( VK[ VK_SPACE ] & KEY_ON_ONCE ){
					messflag = 1;
					if ( !SaveCdData() ) messflag = 2;

					messcnt = 0;
				}
				// ＲＥＴＵＲＮで整理
				if( VK[ VK_RETURN ] & KEY_ON_ONCE ){
					messflag = 3;
					if ( !arrangeCdData() ) messflag = 4;

					messcnt = 0;
				}
			}
			else if( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ){
				// スプライト番号变更（±１）
				if( (VK[ VK_RIGHT ]&KEY_ON_REP) || (VK[ VK_INSERT ]&KEY_ON_REP) ){
					while( 1 ){
						if (VK[ VK_RIGHT ]&KEY_ON_REP) pActCd->anim_chr_no++;
						else pActCd->anim_chr_no += 1000;
	#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pActCd->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ) break;
	#else
						if( SpriteData[pActCd->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
	#endif
						if( pActCd->anim_chr_no > 1000000 ){ pActCd->anim_chr_no = 1000000; break; }
					}
				}
				if( (VK[ VK_LEFT ]&KEY_ON_REP) || (VK[ VK_DELETE ]&KEY_ON_REP) ){
					while( 1 ){
						if (VK[ VK_LEFT ]&KEY_ON_REP) pActCd->anim_chr_no--;
						else pActCd->anim_chr_no -= 1000;
	#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pActCd->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ) break;
	#else
						if( SpriteData[pActCd->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
	#endif
						if( pActCd->anim_chr_no <  SPRSTART ){ pActCd->anim_chr_no =  SPRSTART; break; }
					}
				}
				// 方向变换
				if( VK[ VK_UP ] & KEY_ON_REP ){
					pActCd->anim_ang++;
					if( pActCd->anim_ang >= 8 ) pActCd->anim_ang = 0;
				}
				if( VK[ VK_DOWN ] & KEY_ON_REP ){
					pActCd->anim_ang--;
					if( pActCd->anim_ang < 0 ) pActCd->anim_ang =  7;
				}
				
				// アニメーション番号表示
				if( VK[ VK_HOME ] & KEY_ON_REP ){
					pActCd->anim_no++;
					if( pActCd->anim_no >= ANIM_KIND_MAX ) pActCd->anim_no = 0;
				}
				if( VK[ VK_END ] & KEY_ON_REP ){
					pActCd->anim_no--;
					if( pActCd->anim_no < 0 ) pActCd->anim_no = ANIM_KIND_MAX - 1;
				}

				if( VK[ VK_V ] & KEY_ON_ONCE ){
					CopyCdData( pActCd->anim_chr_no );
				}
				if( VK[ VK_J ] & KEY_ON_ONCE ){
					CopyJustCdData( pActCd->anim_chr_no );
				}
			}
			else if( AnimCheckmode == 2 ){
				if( VK[ VK_LEFT ] & KEY_ON_REP ){	/* カーソル左移动 */
					scaleCur--;
					if ( scaleCur < 0 ) scaleCur = 0;
				}
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* カーソル右移动 */
					scaleCur++;
					if ( scaleCur >= 6 ) scaleCur = 6 - 1;
				}
				if( VK[ VK_DOWN] & KEY_ON_REP ){	/* マイナス */
					scale[scaleCur]--;
					if ( scale[scaleCur] < 0 ) scale[scaleCur] = 9;
				}
				if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
					scale[scaleCur]++;
					if ( scale[scaleCur] >= 10 ) scale[scaleCur] = 0;
				}
				pAct->scaleX = 0;
				for(i=6-1,j=1;i>=0;i--,j*=10){
					pAct->scaleX += scale[i] * j;
				}
				pAct->scaleX /= 10000;
				pAct->scaleY = pAct->scaleX;
				// +で今变更中のsprのグラフィックをインクリメント
				if( VK[ VK_ADD ] & KEY_ON_ONCE ){
					messflag = 5;
					if ( !arrangeCdData(pAct->anim_chr_no,1) ) messflag = 6;

					messcnt = 0;
				}
				// -で今变更中のsprのグラフィックをデクリメント
				if( VK[ VK_SUBTRACT ] & KEY_ON_ONCE ){
					messflag = 5;
					if ( !arrangeCdData(pAct->anim_chr_no,-1) ) messflag = 6;

					messcnt = 0;
				}
			}
#ifdef PUK3_ANIMVIEW_MOVECHARA
			else if( AnimCheckmode == 4 ){
				if( VK[ VK_LEFT ] & KEY_ON_REP ){	/* カーソル左移动 */
					pAct->x--;
				}
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* カーソル右移动 */
					pAct->x++;
				}
				if( VK[ VK_DOWN] & KEY_ON_REP ){	/* マイナス */
					pAct->y++;
				}
				if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
					pAct->y--;
				}
				pActX = pAct->x;
				pActY = pAct->y;
			}
#endif
			// モード变更
			if ( VK[ VK_M ] & KEY_ON_REP ){
				AnimCheckmode++;
#ifdef PUK3_ANIMVIEW_MOVECHARA
				if ( AnimCheckmode > 4 ) AnimCheckmode = 0;
#else
#ifdef PUK3_RIDEBIN
				if ( AnimCheckmode > 3 ) AnimCheckmode = 0;
#else
				if ( AnimCheckmode > 2 ) AnimCheckmode = 0;
#endif
#endif
			}
			// 背景色变更
			if( VK[ VK_PRIOR ] & KEY_ON_REP ){	/* プラス */
				if( backColor == 0 ) backColor = SYSTEM_PAL_GRAY;
				else if( backColor == SYSTEM_PAL_WHITE ) backColor = 0;
				else backColor++;
				// アイテム使う音
				//play_se( 212, 320, 240 );
			}
			// ＥＳＣで終了
			if( VK[ VK_ESCAPE ] & KEY_ON_REP ){	/* マイナス */
				ChangeProc2( PROC_ANIM_VIEW, 2 );
				pActCdSpr = pActCd->anim_chr_no;
				pActCdAng = pActCd->anim_ang;
				pActCdAct = pActCd->anim_no;
				DeathAllAction();
				return;
			}
			// ＴＡＢで切り替え
			if( VK[ VK_TAB ] & KEY_ON_REP ){
				SubProcNo = 1;
				// コマ再生中なら
				if (slowFlag){
					for(pAct->level=0;pAct->level<CdListMax;pAct->level++){
						if ( CdListGraNo[pAct->level] == pAct->bmpNo ) break;
					}
					if ( pAct->level >= CdListMax ) pAct->level = 0;
				}
				return;
			}

			// コマ送りフラグＯＮ
			if( VK[ VK_F12 ] & KEY_ON_REP ){ 
				pAct->anim_frame_cnt = 0;
				slowFlag = TRUE;
			}
			// コマ戾しフラグＯＮ
			if( VK[ VK_F11 ] & KEY_ON_REP ){ 
				pAct->anim_cnt -= 2;
				// リミットチェック
				if( pAct->anim_cnt <= 0 ) pAct->anim_cnt = 0;
				pAct->anim_frame_cnt = 0;
				slowFlag = TRUE;
			}
			// コマ送りフラグＯＦＦ
			if( VK[ VK_F9 ] & KEY_ON_REP ) slowFlag = FALSE;

			if( slowFlag == FALSE || VK[ VK_F11 ] & KEY_ON_REP || VK[ VK_F12 ] & KEY_ON_REP ){
	#ifdef PUK2
				// アニメーション
				if (pAct->anim_chr_no<1000000) pattern( pAct, anim_spd, ANM_LOOP );
				else pAct->bmpNo=-1;
	#else
				// アニメーション
				pattern( pAct, anim_spd, ANM_LOOP );
	#endif
			}

			// 座标上絵表示切り替え
			if( ( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ) && (VK[ VK_SPACE ]&KEY_ON_REP) ){
				CdShowFlag++;
				if (CdShowFlag>3) CdShowFlag = 0;
			}

			if (pActCd){
				int i;
				short dx,dy;
				realGetPos( pAct->bmpNo, &dx, &dy );
				for(i=0;i<CdListMax;i++){
					if ( pAct->bmpNo == CdListGraNo[i] ) break;
				}

				pActCd->anim_ang = pAct->anim_ang;
				pActCd->x = pAct->x + pAct->anim_x;
				pActCd->y = pAct->y + pAct->anim_y;
				if (pAct->bm.bltf&BLTF_MRR_X) pActCd->x += SpriteInfo[ pAct->bmpNo ].width+dx;
				else pActCd->x -= dx;
				if (pAct->bm.bltf&BLTF_MRR_Y) pActCd->y += SpriteInfo[ pAct->bmpNo ].height+dy;
				else pActCd->y -= dy;

				if (i<CdListMax){
					if (pAct->bm.bltf&BLTF_MRR_X) pActCd->x -= (int)(CdListXY[i][0]*pAct->scaleX);
					else pActCd->x += (int)(CdListXY[i][0]*pAct->scaleX);
					if (pAct->bm.bltf&BLTF_MRR_Y) pActCd->y -= (int)(CdListXY[i][1]*pAct->scaleY);
					else pActCd->y += (int)(CdListXY[i][1]*pAct->scaleY);
				}
				pattern( pActCd, ANM_NOMAL_SPD, ANM_LOOP );

				pActCd->atr = 0;
				if (CdShowFlag&1){
					StockBoxDispBuffer( pActCd->x-50, pActCd->y-1, pActCd->x, pActCd->y+2, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x+1, pActCd->y-1, pActCd->x+50, pActCd->y+2, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x-1, pActCd->y-50, pActCd->x+2, pActCd->y, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x-1, pActCd->y+1, pActCd->x+2, pActCd->y+50, DISP_PRIO_WIN, 0, 1 );
				}
				if ( !(CdShowFlag&2) ){
					pActCd->atr = ACT_ATR_HIDE;
				}
			}

#ifdef PUK3_ANIMVIEW_MOVECHARA
			if( AnimCheckmode == 4  ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　表示位置変更モード", 0 ); //MLHIDE
				wsprintf( szMoji, "ｘ座標 = %8d ： Left or Right ( ±1 )", pAct->x );  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "ｙ座標 = %8d ： Down or UP", pAct->y );            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else
#endif
			if( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　座標上絵設定モード", 0 ); //MLHIDE
				// 座标上スプライト番号表示
				wsprintf( szMoji, "座標上動画番号 = %8d ： Left or Right ( ±1 )", pActCd->anim_chr_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				StockFontBuffer( x + 236, y, FONT_PRIO_FRONT, 0, "： DEL or INS ( ±1000 )", 0 ); y += 20; //MLHIDE
				// 方向表示
//				wsprintf( szMoji, "座標上　方　向 = %8d ： Down or UP", pActCd->anim_ang );
//				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// アニメーション番号表示
				wsprintf( szMoji, "座標上行動番号 = %8d ： END or HOME", pActCd->anim_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "座標上表示切替 ： SPACE" );                            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "座標上座標データコピー ： V" );                            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "                 (色違い用)： J" );                 //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else if( AnimCheckmode == 2 ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　縮尺設定モード", 0 );   //MLHIDE
				// 缩尺
				sprintf( szMoji, "　　　　　　　 %c%c %c%c%c%c ： Left ←, Right →, Up ＋, Down －", //MLHIDE
					(scaleCur==0?'_':' '), (scaleCur==1?'_':' '), (scaleCur==2?'_':' '),
					(scaleCur==3?'_':' '), (scaleCur==4?'_':' '), (scaleCur==5?'_':' ') );
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 );
				sprintf( szMoji, "縮尺　　　　　 = %s%0.4f", (pAct->scaleX>=10.0f?"":" "), pAct->scaleX ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				sprintf( szMoji, "対応するグラフィックＩＤをずらす = %s%0.4f: Num- or Num+ ( ±1 )", (pAct->scaleX>=10.0f?"":" "), pAct->scaleX ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else if( AnimCheckmode == 1 ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　座標確認モード", 0 );   //MLHIDE
				// スプライト番号表示
				wsprintf( szMoji, "スプライト番号 = %8d", pAct->anim_chr_no );           //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 方向表示
				wsprintf( szMoji, "方　　　　　向 = %8d ： Down or UP", pAct->anim_ang ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// アニメーション番号表示
				wsprintf( szMoji, "行　動　番　号 = %8d ： END or HOME", pAct->anim_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 增分表示
				wsprintf( szMoji, "ス　ピ　ー　ド = %8d ： Z or X", anim_spd );           //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				
				// 效果音番号
				wsprintf( szMoji, "効果音　番　号 = %8d", t_music_se_no );               //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 效果音ある时
				if( t_music_se_no != -1 ){
					// 效果音の音量
					if( VK[ VK_F8 ] & KEY_ON_REP ){		/* プラス */
						tone_tbl[ t_music_se_no ].voice_volume++;
						if( tone_tbl[ t_music_se_no ].voice_volume >= 128 ) tone_tbl[ t_music_se_no ].voice_volume = 127;
					}
					if( VK[ VK_F7 ] & KEY_ON_REP ){	/* マイナス */
						tone_tbl[ t_music_se_no ].voice_volume--;
						if( tone_tbl[ t_music_se_no ].voice_volume <= -1 ) tone_tbl[ t_music_se_no ].voice_volume = 0;
					}
					
					// 效果音のピッチ
					if( VK[ VK_F6 ] & KEY_ON_REP ){	/* プラス */
						tone_tbl[ t_music_se_no ].voice_note++;
						if( tone_tbl[ t_music_se_no ].voice_note + tone_tbl[ t_music_se_no ].voice_rate >= 63 ) tone_tbl[ t_music_se_no ].voice_note = 62 - tone_tbl[ t_music_se_no ].voice_rate;
					}
					if( VK[ VK_F5 ] & KEY_ON_REP ){	/* マイナス */
						tone_tbl[ t_music_se_no ].voice_note--;
						if( tone_tbl[ t_music_se_no ].voice_note + tone_tbl[ t_music_se_no ].voice_rate <= 0 ) tone_tbl[ t_music_se_no ].voice_note = -tone_tbl[ t_music_se_no ].voice_rate + 1;
					}
					// 效果音音量
					wsprintf( szMoji, "効果音　音　量 = %8d ： F7 or F8", tone_tbl[ t_music_se_no ].voice_volume ); //MLHIDE
					StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
					// 效果音ピッチ
					wsprintf( szMoji, "効果音　ピッチ = %8d ： F5 or F6", tone_tbl[ t_music_se_no ].voice_note ); //MLHIDE
					StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				}else{
					// 效果音音量
					wsprintf( szMoji, "効果音　音　量 = %8d ： F7 or F8", 0 );               //MLHIDE
					StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
					// 效果音ピッチ
					wsprintf( szMoji, "効果音　ピッチ = %8d ： F5 or F6", 0 );               //MLHIDE
					StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				}
				// 操作说明
				wsprintf( szMoji, "SPACE:データ保存 ENTER:データ整理" );                    //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				
				// ＢＭＰ番号
				//wsprintf( szMoji, "ＢＭＰ　番　号 = %8d", pAct->bmpNo );
				//StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// サイズ表示
				//wsprintf( szMoji, "ＢＭＰ　サイズ = %4d,%4d", SpriteInfo[ pAct->bmpNo ].width, SpriteInfo[ pAct->bmpNo ].height );
				//StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}
			
			// 行动番号の表示
			StockFontBuffer( 320 - GetStrWidth( animStr[ pAct->anim_no ] ) / 2, 420, FONT_PRIO_FRONT, 0, animStr[ pAct->anim_no ], 0 );

			if (messcnt<120){
				switch(messflag){
				case 1:	wsprintf( szMoji, "データを保存しました。" );	break;                 //MLHIDE
				case 2:	wsprintf( szMoji, "データの保存に失敗しました。" );	break;              //MLHIDE
				case 3:	wsprintf( szMoji, "データを整理しました。" );	break;                 //MLHIDE
				case 4:	wsprintf( szMoji, "データの整理に失敗しました。" );	break;              //MLHIDE
				case 5:	wsprintf( szMoji, "データを改造整理しました。" );	break;               //MLHIDE
				case 6:	wsprintf( szMoji, "データの改造整理に失敗しました。" );	break;            //MLHIDE
				}
				if (messflag) StockFontBuffer( 4, 400, FONT_PRIO_FRONT, 0, szMoji, 0 );
				messcnt++;
			}
			wsprintf( szMoji, "TAB:座標確認モード M:モード切り替え　CTRL:座標絵モード ON/OFF" );    //MLHIDE
			StockFontBuffer( 4, 440, FONT_PRIO_FRONT, 0, szMoji, 0 );
			
			// コマ送りの说明、ヒット音の说明、終了表示
			wsprintf( szMoji, "F12:コマ送り　F11:コマ戻し　F9:ＯＦＦ ESC:終了" );             //MLHIDE
			StockFontBuffer( 4, 460, FONT_PRIO_FRONT, 0, szMoji, 0 );

			break;
		
		/* 死亡时 */	
		case ACT_STATE_DEAD:
		
			DeathAction( pAct );
			
			break;
	}
}

/* アニメーションアクション处理 ***********************************************/
void CdSprStop( ACTION *pAct )
{
	char szMoji[ 256 ];
	int x = 32, y = 32;
	int c = 0;
#ifdef MULTI_GRABIN
	int sprNo;
#endif
	static int palNo = 0;
	static int anim_spd = ANM_NOMAL_SPD;
	static char scale[6]={0,1};		// 小数点は、要素１と２の间
	static int scaleCur = 0;		// スケールを变更するためのカーソル位置
	int i,j;
#ifdef PUK3_RIDEBIN
	static int CdShowFlag = 1;
#endif
	static char messflag = 0, messcnt = 0;
	
	/* 状态で分岐 */
	switch( pAct->state ){
		/* 通常时 */
		case ACT_STATE_ACT:
			// スローでない时
			if( ( AnimCheckmode == 1 || AnimCheckmode == 0 ) && !(VK[ VK_CONTROL ] & KEY_ON) ){
				// スプライト番号变更（±１）
				if( (VK[ VK_RIGHT ]&KEY_ON_REP) || (VK[ VK_INSERT ]&KEY_ON_REP) ){
					int bf = pAct->level;
					if (VK[ VK_RIGHT ]&KEY_ON_REP) pAct->level++;
					else pAct->level += 50;
					if( pAct->level >= CdListMax ) pAct->level = CdListMax - 1;

					// 移动した先に座标データコピー
					if ( VK[ VK_NUMPAD0 ]&KEY_ON ){
						CdListXY[pAct->level][0] = CdListXY[bf][0];
						CdListXY[pAct->level][1] = CdListXY[bf][1];
					}
				}
				if( (VK[ VK_LEFT ]&KEY_ON_REP) || (VK[ VK_DELETE ]&KEY_ON_REP) ){
					int bf = pAct->level;
					if (VK[ VK_LEFT ]&KEY_ON_REP) pAct->level--;
					else pAct->level -= 50;
					if( pAct->level <  0 ) pAct->level = 0;

					// 移动した先に座标データコピー
					if ( VK[ VK_NUMPAD0 ]&KEY_ON ){
						CdListXY[pAct->level][0] = CdListXY[bf][0];
						CdListXY[pAct->level][1] = CdListXY[bf][1];
					}
				}

				if ( mouse.state & MOUSE_RIGHT_CRICK ){
					CdListXY[pAct->level][0] = (int)((mouse.nowPoint.x-pAct->x)/pAct->scaleX);
					CdListXY[pAct->level][1] = (int)((mouse.nowPoint.y-pAct->y)/pAct->scaleY);
				}
				if ( VK[ VK_NUMPAD4 ] & KEY_ON_REP ) CdListXY[pAct->level][0]--;
				if ( VK[ VK_NUMPAD6 ] & KEY_ON_REP ) CdListXY[pAct->level][0]++;
				if ( VK[ VK_NUMPAD8 ] & KEY_ON_REP ) CdListXY[pAct->level][1]--;
				if ( VK[ VK_NUMPAD2 ] & KEY_ON_REP ) CdListXY[pAct->level][1]++;

				// ＳＰＡＣＥで保存
				if( VK[ VK_SPACE ] & KEY_ON_ONCE ){
					messflag = 1;
					if ( !SaveCdData() ) messflag = 2;

					messcnt = 0;
				}
				// ＲＥＴＵＲＮで整理
				if( VK[ VK_RETURN ] & KEY_ON_ONCE ){
					messflag = 3;
					if ( !arrangeCdData() ) messflag = 4;

					messcnt = 0;
				}

				// データを削除し、つめる
				if ( VK[ VK_D ] & KEY_ON_ONCE ){
					for(i=pAct->level;i<CdListMax-1;i++){
						CdListXY[i][0] = CdListXY[i+1][0];
						CdListXY[i][1] = CdListXY[i+1][1];
					}
					if (i==CdListMax-1){
						CdListXY[i][0] = 0;
						CdListXY[i][1] = 0;
					}
				}
				// 空データを插入
				if ( VK[ VK_I ] & KEY_ON_ONCE ){
					for(i=CdListMax-1;i>=pAct->level+1;i--){
						CdListXY[i][0] = CdListXY[i-1][0];
						CdListXY[i][1] = CdListXY[i-1][1];
					}
					if (i==pAct->level){
						CdListXY[i][0] = 0;
						CdListXY[i][1] = 0;
					}
				}
			}
			else if( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ){
				// スプライト番号变更（±１）
				if( (VK[ VK_RIGHT ]&KEY_ON_REP) || (VK[ VK_INSERT ]&KEY_ON_REP) ){
					while( 1 ){
						if (VK[ VK_RIGHT ]&KEY_ON_REP) pActCd->anim_chr_no++;
						else pActCd->anim_chr_no += 1000;
	#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pActCd->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ) break;
	#else
						if( SpriteData[pActCd->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
	#endif
						if( pActCd->anim_chr_no > 1000000 ){ pActCd->anim_chr_no = 1000000; break; }
					}
				}
				if( (VK[ VK_LEFT ]&KEY_ON_REP) || (VK[ VK_DELETE ]&KEY_ON_REP) ){
					while( 1 ){
						if (VK[ VK_LEFT ]&KEY_ON_REP) pActCd->anim_chr_no--;
						else pActCd->anim_chr_no -= 1000;
	#ifdef MULTI_GRABIN
						sprNo = SPR_NUMBER2TBL( pActCd->anim_chr_no - SPRSTART );
						if( sprNo >= 0 ) break;
	#else
						if( SpriteData[pActCd->anim_chr_no - SPRSTART].ptAnimlist !=NULL ) break;
	#endif
						if( pActCd->anim_chr_no <  SPRSTART ){ pActCd->anim_chr_no =  SPRSTART; break; }
					}
				}
				// 方向变换
				if( VK[ VK_UP ] & KEY_ON_REP ){
					pActCd->anim_ang++;
					if( pActCd->anim_ang >= 8 ) pActCd->anim_ang = 0;
				}
				if( VK[ VK_DOWN ] & KEY_ON_REP ){
					pActCd->anim_ang--;
					if( pActCd->anim_ang < 0 ) pActCd->anim_ang =  7;
				}
				
				// アニメーション番号表示
				if( VK[ VK_HOME ] & KEY_ON_REP ){
					pActCd->anim_no++;
					if( pActCd->anim_no >= ANIM_KIND_MAX ) pActCd->anim_no = 0;
				}
				if( VK[ VK_END ] & KEY_ON_REP ){
					pActCd->anim_no--;
					if( pActCd->anim_no < 0 ) pActCd->anim_no = ANIM_KIND_MAX - 1;
				}

				if( VK[ VK_V ] & KEY_ON_ONCE ){
					CopyCdData( pActCd->anim_chr_no );
				}
				if( VK[ VK_J ] & KEY_ON_ONCE ){
					CopyJustCdData( pActCd->anim_chr_no );
				}
			}
			else if( AnimCheckmode == 2 ){
				if( VK[ VK_LEFT ] & KEY_ON_REP ){	/* カーソル左移动 */
					scaleCur--;
					if ( scaleCur < 0 ) scaleCur = 0;
				}
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* カーソル右移动 */
					scaleCur++;
					if ( scaleCur >= 6 ) scaleCur = 6 - 1;
				}
				if( VK[ VK_DOWN] & KEY_ON_REP ){	/* マイナス */
					scale[scaleCur]--;
					if ( scale[scaleCur] < 0 ) scale[scaleCur] = 9;
				}
				if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
					scale[scaleCur]++;
					if ( scale[scaleCur] >= 10 ) scale[scaleCur] = 0;
				}
				pAct->scaleX = 0;
				for(i=6-1,j=1;i>=0;i--,j*=10){
					pAct->scaleX += scale[i] * j;
				}
				pAct->scaleX /= 10000;
				pAct->scaleY = pAct->scaleX;
				// +で今变更中のsprのグラフィックをインクリメント
				if( VK[ VK_ADD ] & KEY_ON_ONCE ){
					messflag = 5;
					if ( !arrangeCdData(pAct->anim_chr_no,1) ) messflag = 6;

					messcnt = 0;
				}
				// -で今变更中のsprのグラフィックをデクリメント
				if( VK[ VK_SUBTRACT ] & KEY_ON_ONCE ){
					messflag = 5;
					if ( !arrangeCdData(pAct->anim_chr_no,-1) ) messflag = 6;

					messcnt = 0;
				}
			}
#ifdef PUK3_ANIMVIEW_MOVECHARA
			else if( AnimCheckmode == 4 ){
				if( VK[ VK_LEFT ] & KEY_ON_REP ){	/* カーソル左移动 */
					pAct->x--;
				}
				if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* カーソル右移动 */
					pAct->x++;
				}
				if( VK[ VK_DOWN] & KEY_ON_REP ){	/* マイナス */
					pAct->y++;
				}
				if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
					pAct->y--;
				}
				pActX = pAct->x;
				pActY = pAct->y;
			}
#endif
			// モード变更
			if( VK[ VK_M ] & KEY_ON_REP ){
				AnimCheckmode++;
#ifdef PUK3_ANIMVIEW_MOVECHARA
				if ( AnimCheckmode > 4 ) AnimCheckmode = 0;
#else
#ifdef PUK3_RIDEBIN
				if ( AnimCheckmode > 3 ) AnimCheckmode = 0;
#else
				if ( AnimCheckmode > 2 ) AnimCheckmode = 0;
#endif
#endif
			}
			// 背景色变更
			if( VK[ VK_PRIOR ] & KEY_ON_REP ){	/* プラス */
				if( backColor == 0 ) backColor = SYSTEM_PAL_GRAY;
				else if( backColor == SYSTEM_PAL_WHITE ) backColor = 0;
				else backColor++;
				// アイテム使う音
				//play_se( 212, 320, 240 );
			}
			// ＥＳＣで終了
			if( VK[ VK_ESCAPE ] & KEY_ON_REP ){	/* マイナス */
				ChangeProc2( PROC_ANIM_VIEW, 2 );
				pActCdSpr = pActCd->anim_chr_no;
				pActCdAng = pActCd->anim_ang;
				pActCdAct = pActCd->anim_no;
				DeathAllAction();
				return;
			}
			// ＴＡＢで切り替え
			if( VK[ VK_TAB ] & KEY_ON_REP ){
				SubProcNo = 2;
				return;
			}

			pAct->bm.bltf = 0;
			{
				short dx,dy;
				pAct->bmpNo = CdListGraNo[pAct->level];
				realGetPos(pAct->bmpNo, &dx, &dy );	// アダーンビンのＸＹ座标取り出し
				pAct->anim_x = dx;		// Ｘ座标OFFセットセット
				pAct->anim_y = dy;		// Ｙ座标OFFセットセット
			}

			// 座标上絵表示切り替え
			if( ( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ) && (VK[ VK_SPACE ]&KEY_ON_REP) ){
				CdShowFlag++;
				if (CdShowFlag>3) CdShowFlag = 0;
			}

			if (pActCd){
				pActCd->x = pAct->x + (int)(CdListXY[pAct->level][0]*pAct->scaleX);
				pActCd->y = pAct->y + (int)(CdListXY[pAct->level][1]*pAct->scaleY);
				pattern( pActCd, ANM_NOMAL_SPD, ANM_LOOP );

				pActCd->atr = 0;
				if (CdShowFlag&1){
					StockBoxDispBuffer( pActCd->x-50, pActCd->y-1, pActCd->x, pActCd->y+2, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x+1, pActCd->y-1, pActCd->x+50, pActCd->y+2, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x-1, pActCd->y-50, pActCd->x+2, pActCd->y, DISP_PRIO_WIN, 0, 1 );
					StockBoxDispBuffer( pActCd->x-1, pActCd->y+1, pActCd->x+2, pActCd->y+50, DISP_PRIO_WIN, 0, 1 );
				}
				if ( !(CdShowFlag&2) ){
					pActCd->atr = ACT_ATR_HIDE;
				}
			}

#ifdef PUK3_ANIMVIEW_MOVECHARA
			if( AnimCheckmode == 4  ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　表示位置変更モード", 0 ); //MLHIDE
				wsprintf( szMoji, "ｘ座標 = %8d ： Left or Right ( ±1 )", pAct->x );  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "ｙ座標 = %8d ： Down or UP", pAct->y );            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else
#endif
			if( AnimCheckmode == 3 || (VK[ VK_CONTROL ] & KEY_ON) ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　座標上絵設定モード", 0 ); //MLHIDE
				// 座标上スプライト番号表示
				wsprintf( szMoji, "座標上動画番号 = %8d ： Left or Right ( ±1 )", pActCd->anim_chr_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				StockFontBuffer( x + 236, y, FONT_PRIO_FRONT, 0, "： DEL or INS ( ±1000 )", 0 ); y += 20; //MLHIDE
				// 方向表示
				wsprintf( szMoji, "座標上　方　向 = %8d ： Down or UP", pActCd->anim_ang ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// アニメーション番号表示
				wsprintf( szMoji, "座標上行動番号 = %8d ： END or HOME", pActCd->anim_no ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "座標上表示切替 ： SPACE" );                            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "座標上座標データコピー ： V" );                            //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;

				wsprintf( szMoji, "                 (色違い用)： J" );                 //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else if( AnimCheckmode == 2 ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　縮尺設定モード", 0 );   //MLHIDE
				// 缩尺
				sprintf( szMoji, "　　　　　　　 %c%c %c%c%c%c ： Left ←, Right →, Up ＋, Down －", //MLHIDE
					(scaleCur==0?'_':' '), (scaleCur==1?'_':' '), (scaleCur==2?'_':' '),
					(scaleCur==3?'_':' '), (scaleCur==4?'_':' '), (scaleCur==5?'_':' ') );
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 );
				sprintf( szMoji, "縮尺　　　　　 = %s%0.4f", (pAct->scaleX>=10.0f?"":" "), pAct->scaleX ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				sprintf( szMoji, "対応するグラフィックＩＤをずらす = %s%0.4f: Num- or Num+ ( ±1 )", (pAct->scaleX>=10.0f?"":" "), pAct->scaleX ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}else if( AnimCheckmode == 1 ){
				StockFontBuffer( x, y-20, FONT_PRIO_FRONT, 0, "　　座標設定モード", 0 );   //MLHIDE
				// スプライト番号表示
				wsprintf( szMoji, "スプライト番号 = %8d", CdMakeSprNo );                 //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// インデックス番号
				wsprintf( szMoji, "インデックス　 = %3d / %3d : Left or Right ( ±1 )", pAct->level, CdListMax - 1 ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				StockFontBuffer( x + 236, y, FONT_PRIO_FRONT, 0, ": DEL or INS ( ±50 )", 0 ); y += 20; //MLHIDE
				// グラフィック番号
				wsprintf( szMoji, "グラフィック　 = %8d", pAct->bmpNo );                 //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// グラフィック番号
				wsprintf( szMoji, "座　標　位　置 = (%4d,%4d)", CdListXY[pAct->level][0], CdListXY[pAct->level][1] ); //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 操作说明
				wsprintf( szMoji, "マウス右ボタン押し : マウスの位置に座标设定" );                    //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "Num4        : 座标を左に１ドット移动" );                  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "Num6        : 座标を右に１ドット移动" );                  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "Num8        : 座标を上に１ドット移动" );                  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "Num2        : 座标を下に１ドット移动" );                  //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "Num0押しながら絵变更:变更した絵に座标コピー" );                   //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				// 操作说明
				wsprintf( szMoji, "SPACE:データ保存 ENTER:データ整理" );                    //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
				wsprintf( szMoji, "D:データを削除し诘める  I:空データを插入" );                    //MLHIDE
				StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			}

			if (messcnt<120){
				switch(messflag){
				case 1:	wsprintf( szMoji, "データを保存しました。" );	break;                 //MLHIDE
				case 2:	wsprintf( szMoji, "データの保存に失敗しました。" );	break;              //MLHIDE
				case 3:	wsprintf( szMoji, "データを整理しました。" );	break;                 //MLHIDE
				case 4:	wsprintf( szMoji, "データの整理に失敗しました。" );	break;              //MLHIDE
				case 5:	wsprintf( szMoji, "データを改造整理しました。" );	break;               //MLHIDE
				case 6:	wsprintf( szMoji, "データの改造整理に失敗しました。" );	break;            //MLHIDE
				}
				if (messflag) StockFontBuffer( 4, 420, FONT_PRIO_FRONT, 0, szMoji, 0 );
				messcnt++;
			}
			// 終了表示
			wsprintf( szMoji, "TAB:座標確認モード M:モード切り替え　CTRL:座標絵モード ON/OFF  ESC:終了" ); //MLHIDE
			StockFontBuffer( 4, 460, FONT_PRIO_FRONT, 0, szMoji, 0 );

			break;
		
		/* 死亡时 */	
		case ACT_STATE_DEAD:
		
			DeathAction( pAct );
			
			break;
	}
}

/* アニメーションアクション作成 ***********************************************/
ACTION *MakeCdSpr( void )
{
	ACTION *pAct;
	
	/* アクションリストに登録 */
	pAct = GetAction( PRIO_JIKI, 0 );
	if( pAct == NULL ) return NULL;
	
	/* 实行关数 */
	pAct->func = CdSprStop;
	// anim_tbl.h の番号
	pAct->anim_chr_no = SPRSTART; 
	// 动作番号
	pAct->anim_no = 0;//ANIM_ATTACK;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 5;//Rnd( 0, 7 );
	// ボックス表示
	pAct->atr |= ACT_ATR_HIT_BOX;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_CHAR;
	/* 初期位置 */
#ifdef PUK3_ANIMVIEW_MOVECHARA
	pAct->x = pActX;
	pAct->y = pActY;
#else
	pAct->x = 320;
	pAct->y = 360;
#endif
	
	// アニメーション
	pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
	
	return pAct;
}

// 座标データ作成プロセス
void AnimCoordinateMakeProc( void )
{
	static ACTION *pAct;

	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
	case 0:
		{
			int i,j,k;
			int chrNo, tmpNo;

			chrNo = CdMakeSprNo - SPRSTART;
#ifdef MULTI_GRABIN
			tmpNo = Spr_Number2Tbl( chrNo );
			if ( tmpNo < 0 ){
				ChangeProc2( PROC_ANIM_VIEW, 2 );
				DeathAllAction();
				return;
			}
			chrNo = (UINT)(tmpNo);
#endif

			CdListMax = 0;

			// どんなグラフィックがあるか检索
			for(i=0;i<SpriteData[chrNo].animSize;i++){
				for(j=0;j<(int)SpriteData[chrNo].ptAnimlist[i].frameCnt;j++){
					for(k=0;k<CdListMax;k++){
						if ( (int)SpriteData[chrNo].ptAnimlist[i].ptFramelist[j].BmpNo
							 == CdListGraNo[k] ) break;
					}
					if ( k < CdListMax ) continue;

					CdListGraNo[CdListMax] = SpriteData[chrNo].ptAnimlist[i].ptFramelist[j].BmpNo;
					CdListMax++;
				}
			}

			// 座标データ初期化
			memset( &CdListXY[0][0], 0, CdListMax*2*sizeof(short) );

			// 既存のデータ読み込み
			for(i=0;i<SpriteData[chrNo].cdSize;i++){
				for(j=0;j<CdListMax;j++){
					if ( SpriteData[chrNo].ptCdlist[i].graNo
						 != CdListGraNo[j] ) continue;

					CdListXY[j][0] = SpriteData[chrNo].ptCdlist[i].x;
					CdListXY[j][1] = SpriteData[chrNo].ptCdlist[i].y;
					break;
				}
			}
		}
		// 座标を示す矢印作成
		{
			/* アクションリストに登録 */
			pActCd = GetAction( PRIO_ENEMY, 0 );

			/* 实行关数 */
			pActCd->func = NULL;
			// anim_tbl.h の番号
			pActCd->anim_chr_no = pActCdSpr;
			// 动作番号
			pActCd->anim_no = pActCdAct;
			// アニメーション向き( ０～７ )( 下が０で右回り )
			pActCd->anim_ang = pActCdAng;
			// ボックス表示
			pActCd->atr = 0;
			/* 表示优先度 */
			pActCd->dispPrio = DISP_PRIO_WIN2;
			/* 初期位置 */
#ifdef PUK3_ANIMVIEW_MOVECHARA
			pActCd->x = pActX;
			pActCd->y = pActY;
#else
			pActCd->x = 320;
			pActCd->y = 360;
#endif
		}
		{
			pAct = MakeCdSpr();
			if (pAct){
				pAct->anim_chr_no = CdMakeSprNo;
				pAct->level = 0;
				pAct->anim_no = 0;
				pAct->anim_ang = 0;
			}
			// アニメーション
			if (pAct->anim_chr_no<1000000) pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
			else pAct->bmpNo=-1;
		}
		// 战闘ＢＧＭ鸣らす
		play_bgm( 2 );
		// パレットチェンジ
		PaletteChange( 0, 0 );
		// サブプロセス番号プラス
		SubProcNo++;
		
		break;
		
	case 1:

		pAct->func = CdSprStop;

		/* アクション走らせる */
		RunAction();
		// タスク表示データをバッファに溜める
		StockTaskDispBuffer();

		// 背景描画
		if( backColor != 0 ) StockBoxDispBuffer( 0, 0, 640, 480, DISP_PRIO_BG, backColor , 1 );

		break;

	case 2:

		pAct->func = CdSprAnim;

		/* アクション走らせる */
		RunAction();
		// タスク表示データをバッファに溜める
		StockTaskDispBuffer();

		// 背景描画
		if( backColor != 0 ) StockBoxDispBuffer( 0, 0, 640, 480, DISP_PRIO_BG, backColor , 1 );

		break;
	}
}

#endif

#ifdef PUK2
	void bgm_volume_change(void);
	void set_bgm_pitch(void);
	extern LPDIRECTSOUNDBUFFER pDSData_tone[TONE_MAX + 1];
	extern long volume_tbl[128];
	extern int freq_tbl[];
#endif
/* ＳＥ确认プロセス ********************************************************************/
void SeTestProc( void )
{
	char szMoji[ 256 ];
#ifdef PUK2
	int x = 80, y = 196;
	static int seNo = 1, bgmNo = 0, bgmFlag;
	static int bgmvol=0;
	static char bgmpitch=0;
#else
	int x = 128, y = 196;
	static int seNo = 1, bgmFlag;
#endif
	
	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
	
		case 0:
			
#ifdef PUK2
			stop_bgm();
#endif
			// サブプロセス番号プラス
			SubProcNo++;
			
			break;
			
		case 1:
			
			// 效果音鸣らす
			if( VK[ VK_Z ] & KEY_ON_REP ) play_se( seNo, 320, 240 );
			// フィールドＢＧＭ鸣らす
#ifdef PUK2
			if( VK[ VK_X ] & KEY_ON_ONCE ){ 
				if (bgmFlag) stop_bgm();
				bgmFlag=TRUE;
				play_bgm( bgmNo );
				bgmvol=bgm_tbl[bgmNo].volume;
				bgmpitch=t_music_bgm_pitch[bgmNo];
			}
			if( VK[ VK_C ] & KEY_ON_ONCE ){ 
				bgmFlag=FALSE;
				stop_bgm();
			}
#else
			if( VK[ VK_X ] & KEY_ON_ONCE ){ 
				if( bgmFlag == FALSE ){
					bgmFlag = TRUE;
					play_bgm( BGM_BATTLE_BOSS );
				}else{
					bgmFlag = FALSE;
					stop_bgm();
				}
			}
#endif
			
#ifdef PUK2
			// ＢＧＭ番号变更（±１）
			if( VK[ VK_UP ] & KEY_ON_REP ){	/* プラス */
				bgmNo++;
				if( bgmNo >= BGM_NON ) bgmNo = 1;
			}
			if( VK[ VK_DOWN ] & KEY_ON_REP ){		/* マイナス */
				bgmNo--;
				if( bgmNo < 0 ) bgmNo = BGM_NON - 1;
			}

			// ＢＧＭの音量
			if( VK[ VK_R ] & KEY_ON_REP ){		/* プラス */
				if (bgmFlag){
					bgmvol++;
					if( bgmvol > 127 ) bgmvol = 127;
					//音量セット
					pDSData_tone[ TONE_MAX ]->SetVolume(volume_tbl[ bgmvol * t_music_bgm_volume / 15 ]);		// ボリュームセット
				}
			}
			if( VK[ VK_E ] & KEY_ON_REP ){	/* マイナス */
				if (bgmFlag){
					bgmvol--;
					if( bgmvol < 0 ) bgmvol = 0;
					//音量セット
					pDSData_tone[ TONE_MAX ]->SetVolume(volume_tbl[ bgmvol * t_music_bgm_volume / 15 ]);		// ボリュームセット
				}
			}
			
			// ＢＧＭのピッチ
			if( VK[ VK_W ] & KEY_ON_REP ){	/* プラス */
				if (bgmFlag){
					bgmpitch++;
					if( bgmpitch > 35 ) bgmpitch = 35;
					//ピッチセット
					pDSData_tone[ TONE_MAX ]->SetFrequency((DWORD)freq_tbl[36+1+bgmpitch]);
				}
			}
			if( VK[ VK_Q ] & KEY_ON_REP ){	/* マイナス */
				if (bgmFlag){
					bgmpitch--;
					if( bgmpitch <= 0 ) bgmpitch = 0;
					//ピッチセット
					pDSData_tone[ TONE_MAX ]->SetFrequency((DWORD)freq_tbl[36+1+bgmpitch]);
				}
			}
#endif
			// 效果音番号变更（±１）
			if( VK[ VK_RIGHT ] & KEY_ON_REP ){	/* プラス */
				while( 1 ){
					seNo++;
					if( seNo >= TONE_MAX ) seNo = 1;
					if( tone_tbl[ seNo ].voice_volume != 0 ) break;
				}
			}
			if( VK[ VK_LEFT ] & KEY_ON_REP ){		/* マイナス */
				while( 1 ){
					seNo--;
					if( seNo < 0 ) seNo = TONE_MAX - 1;
					if( tone_tbl[ seNo ].voice_volume != 0 ) break;
				}
			}
			// 效果音の音量
			if( VK[ VK_F8 ] & KEY_ON_REP ){		/* プラス */
				tone_tbl[ seNo ].voice_volume++;
				if( tone_tbl[ seNo ].voice_volume >= 128 ) tone_tbl[ seNo ].voice_volume = 127;
			}
			if( VK[ VK_F7 ] & KEY_ON_REP ){	/* マイナス */
				tone_tbl[ seNo ].voice_volume--;
				if( tone_tbl[ seNo ].voice_volume <= -1 ) tone_tbl[ seNo ].voice_volume = 0;
			}
			
			// 效果音のピッチ
			if( VK[ VK_F6 ] & KEY_ON_REP ){	/* プラス */
				tone_tbl[ seNo ].voice_note++;
				if( tone_tbl[ seNo ].voice_note + tone_tbl[ seNo ].voice_rate >= 63 ) tone_tbl[ seNo ].voice_note = 62 - tone_tbl[ seNo ].voice_rate;
			}
			if( VK[ VK_F5 ] & KEY_ON_REP ){	/* マイナス */
				tone_tbl[ seNo ].voice_note--;
				if( tone_tbl[ seNo ].voice_note + tone_tbl[ seNo ].voice_rate <= 0 ) tone_tbl[ seNo ].voice_note = -tone_tbl[ seNo ].voice_rate + 1;
			}
			
			// ＥＳＣで終了
			if( VK[ VK_ESCAPE ] & KEY_ON_REP ){	/* マイナス */
#if defined(PUK2)&&defined(_DEBUG)
				GetWindowText( hWnd, szMoji, 256 );
				// ウィンドウタイトルに"soundcheck"の文字列があったら、サーバ选择画面に返回
				if ( strstr( szMoji, "soundcheck" ) ) ChangeProc2( PROC_TITLE_MENU ); //MLHIDE
				// 无ければ、ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				else PostMessage( hWnd, WM_CLOSE, 0, 0L );
				offlineFlag = FALSE;
				return;
#else
				// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				PostMessage( hWnd, WM_CLOSE, 0, 0L );
#endif
			}
#ifdef PUK2
			// ＢＧＭ番号
			wsprintf( szMoji, "ＢＧＭ　番　号 =  %4d ： Up or Down", bgmNo );          //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// ＢＧＭ音量
			wsprintf( szMoji, "ＢＧＭ　音　量 =  %4d ： E    or R", bgmvol );          //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// ＢＧＭピッチ
			wsprintf( szMoji, "ＢＧＭ　ピッチ =  %4d ： Q    or W", bgmpitch );        //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			y += 20;
#endif
			// 效果音番号
			wsprintf( szMoji, "效果音　番　号 =  %4d ： Left or Right", seNo );        //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// 效果音音量
			wsprintf( szMoji, "效果音　音　量 =  %4d ： F7   or F8", tone_tbl[ seNo ].voice_volume ); //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
			// 效果音ピッチ
			wsprintf( szMoji, "效果音　ピッチ =  %4d ： F5   or F6", tone_tbl[ seNo ].voice_note ); //MLHIDE
			StockFontBuffer( x, y, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
#ifdef PUK2
			// 音鸣らすボタン
			wsprintf( szMoji, "Z：效果音鸣らす　X：ＢＧＭ鸣らす　C：ＢＧＭ止める" );                  //MLHIDE
			StockFontBuffer( 16, 460, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
#else
			// 效果音鸣らすボタン
			wsprintf( szMoji, "Z：效果音鸣らす　X：ＢＧＭ鸣らす" );                           //MLHIDE
			StockFontBuffer( 16, 460, FONT_PRIO_FRONT, 0, szMoji, 0 ); y += 20;
#endif
			
			// 終了表示
			StockFontBuffer( 530, 460, FONT_PRIO_FRONT, 0, "終了：ESC", 0 );      //MLHIDE
			
			break;
	}
}

#ifdef PUK2

extern int g_id_num;

/* スプライト确认プロセス ********************************************************************/
void G_IDViewProc( void )
{
	char szMoji[ 256 ];
	int bmpX;
	int bmpY;
	static int bmpNo = 0;
	int bmpNoBak;
	int realNo;			// 通し番号表示
	static int no = 0; // 增分番号
	static BOOL fontFlag = TRUE; // フォント表示フラグ
	// 增分テーブル
	int zoubunTbl[] ={   1,  5,  25,  100, 	500,  1000, 10000, 100000,
						-1, -5, -25, -100, -500, -1000, -10000, -100000 };
	static int palNo = 0; // パレット番号
	static int time = 600; // パレット番号
	
	/* サブプロセス番号で分岐 */
	switch( SubProcNo ){
		// パレットチェンジ??サブプロセス番号プラス
		case 0:	PaletteChange( 0, 0 );	SubProcNo++; break;
	
		case 1:
			// キーボードで操作する时
			// 进ませる
			if( VK[ VK_RIGHT ] & KEY_ON_REP ) bmpNo += zoubunTbl[ no ];
			// 返回
			if( VK[ VK_LEFT ] & KEY_ON_REP ) bmpNo -= zoubunTbl[ no ];
			// 增分プラス
			if( VK[ VK_UP ] & KEY_ON_REP ){ 
				no++;
				if( no >= 8 ) no = 0; // リミットチェック
			}
			// 增分マイナス
			if( VK[ VK_DOWN ] & KEY_ON_REP ){
				no--;
				if( no <= -1 ) no = 7; // リミットチェック
			}

			// リミットチェック
			if( bmpNo < 0 ) bmpNo = 0;
			if( bmpNo >= g_id_num - 25 ) bmpNo = g_id_num -25;
			
			// パレット变更
			if( VK[ VK_Z ] & KEY_ON_REP ){	/* プラス */
				palNo--;
				if( palNo < 0 ) palNo = MAX_PAL - 1;
				// パレットチェンジ
				PaletteChange( palNo, 10 );
			}
			if( VK[ VK_X ] & KEY_ON_REP ){	/* マイナス */
				palNo++;
				if( palNo >= MAX_PAL ) palNo = 0;
				// パレットチェンジ
				PaletteChange( palNo, 10 );
			}
			// フォントフラグ变更
			if( VK[ VK_DELETE ] & KEY_ON_REP ){	/* プラス */
				if( fontFlag ) fontFlag = 0;
				else fontFlag = 1;
			}
			
			// 背景色变更
			if( VK[ VK_PRIOR ] & KEY_ON_REP ){	/* プラス */
				if( backColor == 0 ) backColor = SYSTEM_PAL_GRAY;
				else if( backColor == SYSTEM_PAL_WHITE ) backColor = 0;
				else backColor++;
			}
			
			// ＥＳＣで終了
			if( VK[ VK_ESCAPE ] & KEY_ON_REP ){	/* マイナス */
#if defined(PUK2)&&defined(_DEBUG)
				GetWindowText( hWnd, szMoji, 256 );
				// ウィンドウタイトルに"graphic_IDview"の文字列があったら、サーバ选择画面に返回
				if ( strstr( szMoji, "graphic_IDview" ) ) ChangeProc2( PROC_TITLE_MENU ); //MLHIDE
				// 无ければ、ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				else PostMessage( hWnd, WM_CLOSE, 0, 0L );
				offlineFlag = FALSE;
				return;
#else
				// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
				PostMessage( hWnd, WM_CLOSE, 0, 0L );
#endif
			}
			
			// ＢＭＰ番号バックアップ
			bmpNoBak = bmpNo;
			
#ifdef PUK2
			if( VK[ VK_NUMPAD8 ] & KEY_ON_ONCE ) CreateDirectory( "cutspr", NULL ); //MLHIDE
#endif
			// 一画面分ループ（２５枚分）
			for( bmpY = 0 ; bmpY < 480 ; bmpY += 96 ){
				for( bmpX = 0 ; bmpX < 640 ; bmpX += 128 ){
					realGetNo( bmpNoBak , (U4 *)&realNo );
					// フォントフラグＯＮの时
					if( fontFlag == TRUE ){
						// ＢＭＰ番号表示
						wsprintf( szMoji,"%7d", bmpNoBak );                             //MLHIDE
						StockFontBuffer( bmpX, bmpY, FONT_PRIO_BACK, FONT_PAL_GREEN, szMoji, 0 );

						if (realNo==0){
							if (bmpNoBak==0){
								// 通し番号表示
								wsprintf( szMoji,"%7d", realNo );                             //MLHIDE
								StockFontBuffer( bmpX, bmpY + 17, FONT_PRIO_BACK, FONT_PAL_WHITE, szMoji, 0 );
							}else StockFontBuffer( bmpX, bmpY + 17, FONT_PRIO_BACK, FONT_PAL_YELLOW, "   なし", 0 ); //MLHIDE
						}else{
							// 通し番号表示
							wsprintf( szMoji,"%7d", realNo );                              //MLHIDE
							StockFontBuffer( bmpX, bmpY + 17, FONT_PRIO_BACK, FONT_PAL_WHITE, szMoji, 0 );
						}
					}
					if (realNo==0){
						if (bmpNoBak==0){
#ifdef PUK2
							if( VK[ VK_NUMPAD8 ] & KEY_ON_ONCE ) Save_bmp( realNo );
#endif
							// ＢＭＰ表示バッファにためる
							StockDispBufferSprView( bmpX, bmpY, DISP_PRIO_TILE, realNo );
						}
					}else{
#ifdef PUK2
						if( VK[ VK_NUMPAD8 ] & KEY_ON_ONCE ) Save_bmp( realNo );
#endif
						// ＢＭＰ表示バッファにためる
						StockDispBufferSprView( bmpX, bmpY, DISP_PRIO_TILE, realNo );
					}
					bmpNoBak++;
					if ( GetAsyncKeyState(VK_NUMPAD0)<0 ) break;
				}
				if ( GetAsyncKeyState(VK_NUMPAD0)<0 ) break;
			}
			// フォントフラグＯＮの时
			if( fontFlag == TRUE ){
				// 增分表示
				wsprintf( szMoji, "PAL:%2d　増分:%6d", palNo, zoubunTbl[ no ] );     //MLHIDE
				StockFontBuffer( 640 - 16 * 12, 462, FONT_PRIO_FRONT, 0, szMoji, 0 );
					
				// 背景色变更の说明
				wsprintf( szMoji, "PageUp：背景色変更 NUM0：一枚表示 NUM8：ビットマップの生成" );      //MLHIDE

				StockFontBuffer( 4, 440, FONT_PRIO_FRONT, 0, szMoji, 0 );
				// 終了表示
				StockFontBuffer( 4, 462, FONT_PRIO_FRONT, 0, "ESC:終了　Z or X:PAL変更　DEL:文字 ON/OFF", 0 ); //MLHIDE
			}
			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			StockTaskDispBuffer();
			
			// 背景描画
			if( backColor != 0 ) StockBoxDispBuffer( 0, 0, 640, 480, DISP_PRIO_BG, backColor , 1 );
			
			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif

			break;
	}
}

#endif

#endif
