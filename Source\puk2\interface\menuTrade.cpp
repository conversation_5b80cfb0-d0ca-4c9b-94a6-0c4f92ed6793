﻿//メニュー＞マップ

//========================================
// メイン
//========================================

extern char ItemNoOpe[MAX_ITEM];

extern TRADE_ITEM tradeItem[2][MAX_DRAW_WIN_ITEM];
extern TRADE_MONSTER tradeMonster[2][MAX_PET];
extern int tradeOwnerGold;
extern int tradeOkFlag;
extern char tradePlayerName[CHAR_NAME_LEN+1];

char Tr_ItemselFlag[ MAX_EQUIP_ITEM + MAX_DRAW_WIN_ITEM ];
char Tr_MonsselFlag[MAX_PET];

char openItem[MAX_DRAW_WIN_ITEM];
char openMonster[MAX_PET];

extern int tradeOpponentItemSelCnt;
extern int tradeOpponentMonsterSelCnt;

extern int tradeCancelFlag;

static ACTION *pActOpnTr = NULL;

static char TradeMode;
static char ModeChgAcc;
static char Tr_MyMode;
static char Tr_OpenMode;

static int Tr_M_ScrNum;
static int Tr_O_ScrNum;
static int Tr_T_ScrNum;

static int Tr_itemInfoNo;
static char Tr_itemInfoPage;
static char Tr_olditemInfoType;

static char tradecloseflag = 0;

// 假の入力栏
static INPUT_STR TradeInputStr;
static INIT_STR_STRUCT InitStrStructTrade={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	&TradeInputStr,  0,  -100,FONT_PRIO_WIN,FONT_KIND_SIZE_11,FONT_PAL_SHADOW,"",	  1,      0,	  0,     0
};

void PcMonsStatusWindow( short x, short y, char Num );
void TradeMonsStatusWindow( short x, short y, char side, char Num );

// オープンにアイテムを登録
// 引	OpenNo ---- 登録先の番号	ItemNo ---- 登録するアイテムの场所(装备品も含む)
// 戾	成功 ---- TRUE	失败 ---- FALSE
BOOL registOpenItem( int OpenNo, int ItemNo )
{
	// 空いてないなら失败
	if ( openItem[OpenNo] >= 0 ) return FALSE;

	openItem[OpenNo] = ItemNo;
	Tr_ItemselFlag[ItemNo] = 1;
	ItemNoOpe[ItemNo]++;

	return TRUE;
}

// オープンに登録されたアイテムの登録を解除
// 引	OpenNo ---- 解除する番号
// 戾	登録解除されたアイテムの场所、登録されていなかった场合-1
int cancelOpenItem( int OpenNo )
{
	int ret;

	if ( openItem[OpenNo] < 0 ) return -1 ;

	ret = openItem[OpenNo];

	ItemNoOpe[ret]--;
	Tr_ItemselFlag[ret] = 0;
	openItem[OpenNo] = -1;

	return ret;
}

// オープンの登録场所を交换する
// 引	OpenNo1 ---- 交换する场所１	OpenNo1 ---- 交换する场所２
void swapOpenItem( int OpenNo1, int OpenNo2 )
{
	int s;

	s = openItem[OpenNo1];
	openItem[OpenNo1] = openItem[OpenNo2];
	openItem[OpenNo2] = s;
}

// オープンに登録されたアイテムの场所を取得
// 引	OpenNo ---- 登録先の番号
// 戾	登録されているアイテムの场所、登録されていなかった场合-1
BOOL getOpenItem( int OpenNo )
{
	if ( openItem[OpenNo] < 0 ) return -1 ;
	return openItem[OpenNo];
}

// アイテムの位置が移动された时の处理
// 引	form ---- 移动元	to ---- 移动先
void swapTradeItemRelation( int from, int to )
{
	int i;

	if ( !WindowFlag[MENU_WINDOW_TRADE].wininfo ) return;

	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		if ( openItem[i] == from ) openItem[i] = to;
		else if ( openItem[i] == to ) openItem[i] = from;
	}
	i = Tr_ItemselFlag[from];
	Tr_ItemselFlag[from] = Tr_ItemselFlag[to];
	Tr_ItemselFlag[to] = i;
}






// オープンにモンスターを登録
// 引	OpenNo ---- 登録先の番号	ItemNo ---- 登録するアイテムの场所(装备品も含む)
// 戾	成功 ---- TRUE	失败 ---- FALSE
BOOL registOpenMons( int OpenNo, int MonsNo )
{
	// 空いてないなら失败
	if ( openMonster[OpenNo] >= 0 ) return FALSE;

	openMonster[OpenNo] = MonsNo;
	Tr_MonsselFlag[MonsNo] = 1;

	return TRUE;
}

// オープンに登録されたモンスターの登録を解除
// 引	OpenNo ---- 解除する番号
// 戾	登録解除されたアイテムの场所、登録されていなかった场合-1
int cancelOpenMons( int OpenNo )
{
	int ret;

	if ( openMonster[OpenNo] < 0 ) return -1 ;

	ret = openMonster[OpenNo];

	Tr_MonsselFlag[ret] = 0;
	openMonster[OpenNo] = -1;

	return ret;
}

// オープンの登録场所を交换する
// 引	OpenNo1 ---- 交换する场所１	OpenNo1 ---- 交换する场所２
void swapOpenMons( int OpenNo1, int OpenNo2 )
{
	int s;

	s = openMonster[OpenNo1];
	openMonster[OpenNo1] = openMonster[OpenNo2];
	openMonster[OpenNo2] = s;
}

// オープンに登録されたモンスターの场所を取得
// 引	OpenNo ---- 登録先の番号
// 戾	登録されているアイテムの场所、登録されていなかった场合-1
BOOL getOpenMons( int OpenNo )
{
	if ( openMonster[OpenNo] < 0 ) return -1 ;
	return openMonster[OpenNo];
}

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openTradeWindow()
{
	tradecloseflag = 0;
	pActOpnTr = openMenuWindow( MENU_WINDOW_TRADE, OPENMENUWINDOW_HIT, 0 );

	return pActOpnTr;
}

void MovePanel( WINDOW_INFO *wi, int no, short x, short y )
{
	wi->sw[no].ofx = x,			wi->sw[no].ofy = y;				// パネル本体
	wi->sw[no-1].ofx = x+252,	wi->sw[no-1].ofy = y+37;
	wi->sw[no-2].ofx = x+252;									// つまみのｙ座标は变更しない
}

void PanelEnabled( WINDOW_INFO *wi, int no, BOOL Enabled )
{
	wi->sw[no].Enabled = Enabled;				// パネル本体
	if (!Enabled){
		wi->sw[no-1].Enabled = FALSE;
		wi->sw[no-2].Enabled = FALSE;
	}
}

void PanelRGB( WINDOW_INFO *wi, int no, unsigned long rgba )
{
	( (GRAPHIC_SWITCH *)wi->sw[no].Switch )->rgba = rgba;		// パネル本体
}

void closeTradeWindow()
{
	tradecloseflag = 1;

	if (WindowFlag[MENU_WINDOW_TRADE].wininfo){
		WindowFlag[MENU_WINDOW_TRADE].wininfo->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
}

BOOL closeTradeWindowAf()
{
	int i;

	// 电卓ウィンドウを呼び出したのが自分の场合、电卓ウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_CALCULATOR].wininfo){
		struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
		if (wm->WinType == MENU_WINDOW_TRADE){
			wm->wininfo->flag |= WIN_INFO_DEL;
		}
	}

	if (!tradecloseflag){
		nrproto_TROC_send( sockfd, 0 );
		tradeCancelFlag = 1;
	}

	for(i=0;i<MAX_DRAW_WIN_ITEM;i++) cancelOpenItem(i);
	for(i=0;i<MAX_PET;i++) cancelOpenMons(i);

	// ウィンドウ关闭音
	play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

	tradecloseflag = 0;

	return TRUE;
}

BOOL MenuWindowTradeBf( int mouse )
{
	int alpha;

	if (mouse==WIN_INIT){
		int i;

		pActOpnTr = NULL;

		tradeOpponentMode = 0;		// Close状态
		tradeOwnerMode = 0;			// Close状态

		TradeMode = TRADEMODE_A;

		PanelEnabled( wI, EnumTradeMyPanel, TRUE );
		MovePanel( wI, EnumTradeMyPanel, 287, 28 );
		PanelRGB( wI, EnumTradeMyPanel, 0xFFFFFFFF );

		PanelEnabled( wI, EnumTradeOpenPanel, TRUE );
		MovePanel( wI, EnumTradeOpenPanel, 15, 28 );
		PanelRGB( wI, EnumTradeOpenPanel, 0xFFFFFFFF );

		PanelEnabled( wI, EnumTradeTradePanel, FALSE );
		MovePanel( wI, EnumTradeTradePanel, 15, 28 );
		PanelRGB( wI, EnumTradeTradePanel, 0xFFFFFFFF );

		wI->sw[EnumTradeBtOpen].Enabled = TRUE;
		wI->sw[EnumTradeBtTradeOK].Enabled = FALSE;
		wI->sw[EnumTradeBtClose].Enabled = FALSE;

		Tr_olditemInfoType = -1;

		Tr_MyMode = 0;
		Tr_OpenMode = 0;

		Tr_M_ScrNum = 0;
		Tr_O_ScrNum = 0;
		Tr_T_ScrNum = 0;

		NumToScrollVMove( &wI->sw[EnumTradeMyScrollBtn], 2, Tr_M_ScrNum );
		NumToScrollVMove( &wI->sw[EnumTradeOpenScrollBtn], 2, Tr_O_ScrNum );
		NumToScrollVMove( &wI->sw[EnumTradeTradeScrollBtn], 2, Tr_T_ScrNum );

		for(i=0;i<MAX_EQUIP_ITEM+MAX_DRAW_WIN_ITEM;i++) Tr_ItemselFlag[i] = 0;
		for(i=0;i<MAX_PET;i++) Tr_MonsselFlag[i] = 0;

		for(i=0;i<MAX_DRAW_WIN_ITEM;i++) openItem[i] = -1;
		for(i=0;i<MAX_PET;i++) openMonster[i] = -1;

		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumTradeName1].Switch )->text,
			"%16s", tradePlayerName );                                         //MLHIDE
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTradeArrow].Switch )->text, "<=>" ); //MLHIDE
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumTradeName2].Switch )->text,
			"%-16s", pc.name );                                                //MLHIDE
		
		// 开いた时のＰＣの位置を记忆
		memoryMapGridPos( mapGx, mapGy );
	}

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if ( checkMoveMapGridPos( 1, 1 ) ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
	if (tradecloseflag){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	switch(TradeMode){
	case TRADEMODE_A:
		if (tradeOwnerMode>0){
			TradeMode = TRADEMODE_AtoB;

			PanelEnabled( wI, EnumTradeTradePanel, TRUE );
			MovePanel( wI, EnumTradeTradePanel, 15, 28 );

			PanelRGB( wI, EnumTradeTradePanel, 0x00FFFFFF );

			ModeChgAcc = 23;
		}
		break;
	case TRADEMODE_AtoB:
		if (wI->sw[EnumTradeOpenPanel].ofx+ModeChgAcc<287){
			PanelEnabled( wI, EnumTradeOpenPanel, TRUE );
			MovePanel( wI, EnumTradeOpenPanel, wI->sw[EnumTradeOpenPanel].ofx+ModeChgAcc, 28 );
			ModeChgAcc--;

			alpha = ( (GRAPHIC_SWITCH *)wI->sw[EnumTradeMyPanel].Switch )->rgba>>24;
			alpha -= 10;
			if (alpha<0) alpha = 0;
			PanelRGB( wI, EnumTradeMyPanel, (alpha<<24) | 0x00FFFFFF );

			alpha = ( (GRAPHIC_SWITCH *)wI->sw[EnumTradeTradePanel].Switch )->rgba>>24;
			alpha += 10;
			if (alpha>255) alpha = 255;
			PanelRGB( wI, EnumTradeTradePanel, (alpha<<24) | 0x00FFFFFF );
		}else{
			PanelRGB( wI, EnumTradeTradePanel, 0xFFFFFFFF );

			PanelEnabled( wI, EnumTradeOpenPanel, TRUE );
			MovePanel( wI, EnumTradeOpenPanel, 287, 28 );

			PanelEnabled( wI, EnumTradeMyPanel, FALSE );

			wI->sw[EnumTradeBtOpen].Enabled = FALSE;
			wI->sw[EnumTradeBtTradeOK].Enabled = TRUE;
			wI->sw[EnumTradeBtClose].Enabled = TRUE;

			TradeMode = TRADEMODE_B;
		}
		break;
	case TRADEMODE_BtoA:
		if (wI->sw[EnumTradeOpenPanel].ofx-ModeChgAcc>15){
			PanelEnabled( wI, EnumTradeOpenPanel, TRUE );
			MovePanel( wI, EnumTradeOpenPanel, wI->sw[EnumTradeOpenPanel].ofx-ModeChgAcc, 28 );
			ModeChgAcc--;

			alpha = ( (GRAPHIC_SWITCH *)wI->sw[EnumTradeMyPanel].Switch )->rgba>>24;
			alpha += 10;
			if (alpha>255) alpha = 255;
			PanelRGB( wI, EnumTradeMyPanel, (alpha<<24) | 0x00FFFFFF );

			alpha = ( (GRAPHIC_SWITCH *)wI->sw[EnumTradeTradePanel].Switch )->rgba>>24;
			alpha -= 10;
			if (alpha<0) alpha = 0;
			PanelRGB( wI, EnumTradeTradePanel, (alpha<<24) | 0x00FFFFFF );
		}else{
			PanelRGB( wI, EnumTradeMyPanel, 0xFFFFFFFF );

			PanelEnabled( wI, EnumTradeOpenPanel, TRUE );
			MovePanel( wI, EnumTradeOpenPanel, 15, 28 );

			PanelEnabled( wI, EnumTradeTradePanel, FALSE );

			wI->sw[EnumTradeBtOpen].Enabled = TRUE;
			wI->sw[EnumTradeBtTradeOK].Enabled = FALSE;
			wI->sw[EnumTradeBtClose].Enabled = FALSE;

			TradeMode = TRADEMODE_A;
		}
		break;
	case TRADEMODE_B:
		if (tradeOwnerMode<=0){
			TradeMode = TRADEMODE_BtoA;

			PanelEnabled( wI, EnumTradeMyPanel, TRUE );
			MovePanel( wI, EnumTradeMyPanel, 287, 28 );

			PanelRGB( wI, EnumTradeMyPanel, 0x00FFFFFF );

			ModeChgAcc = 23;
		}
		break;
	}

	Tr_itemInfoNo = -1;

	return TRUE;
}

BOOL MenuWindowTradeAf( int Mouse )
{
	char side;
	int x = wI->wx + 35;
	int y = wI->wy + 329;

	// アイテム信息
	if (Tr_itemInfoNo>=0){
		// アイテム
		if (Tr_itemInfoNo<300){
			// １００未满は手持ち??オープン
			if (Tr_itemInfoNo<100) side = 1;
			// ２００から３００未满は相手侧
			else if (Tr_itemInfoNo<300){
				side = 0;
				Tr_itemInfoNo-=200;
			}

			if (Tr_itemInfoNo>=0){
				// 自分侧、オープンなら
				if (side) PcItemExplanationWindow( Tr_itemInfoNo, Tr_itemInfoPage );
				else TradeItemExplanationWindow( side, Tr_itemInfoNo, Tr_itemInfoPage );
			}
		}
		// モンスター
		else{
			Tr_itemInfoNo -= 300;

			// １００未满は手持ち??オープン
			if (Tr_itemInfoNo<100) side = 1;
			// ２００から３００未满は相手侧
			else if (Tr_itemInfoNo<300){
				side = 0;
				Tr_itemInfoNo-=200;
			}

			if (Tr_itemInfoNo>=0){
				x = 0;
				if (mouse.nowPoint.x<320) x = 640 - 227;

				if (side) PcMonsStatusWindow( x, 0, Tr_itemInfoNo );
				else TradeMonsStatusWindow( x, 0, side, Tr_itemInfoNo );
			}
		}
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuTradeGetKeyForcus( int no, unsigned int flag )
{
	if ( Tr_MyMode==2 ){
		if ( flag & MENU_MOUSE_LEFT ){
			WINDOW_INFO *wi = WindowFlag[MENU_WINDOW_TRADE].wininfo;

			// 文字入力栏移动
			SetInputStr( &InitStrStructTrade, 0, -100, 0 );

			// フォーカスを取る
			GetKeyInputFocus( &TradeInputStr );

			DiarogST.SwAdd = wi->sw[EnumTradeGetKeyForcus].Switch;
			( (DIALOG_SWITCH *)wi->sw[EnumTradeGetKeyForcus].Switch )->InpuStrAdd = &TradeInputStr;
		}
	}else{
		if (pNowInputStr==&TradeInputStr) SetDialogMenuChat();
	}

	return FALSE;
}

BOOL MenuSwitchTradeClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_WindowCloseOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_WindowCloseOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_WindowCloseOff;

	return ReturnFlag;
}

BOOL MenuSwitchTradeBtItemOpen( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_TRADE_ITEMOPEN );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プロトコル送信
		char itemStr[1024];
		char itemSendStr[1024];
		char monsterStr[1024];
		char monsterSendStr[1024];
		char num[50];
		int i, j, k;

		itemStr[0] = '\0';
		monsterStr[0] = '\0';

		// アイテムの送信文字列作成
		for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
			k = getOpenItem(i);

			if ( k < 0 ) continue;

			if ( j != 0 ) strcat( itemStr, "|" );                              //MLHIDE

			sprintf( num, "%d|%d", k, i );                                     //MLHIDE
			strcat( itemStr, num );

			j++;
		}
		// ここはエスケープしない。
		strncpy( itemSendStr, itemStr, sizeof( itemSendStr ) );
		for( i = 0, j = 0; i < MAX_PET; i++ ){
			k = getOpenMons(i);

			if ( k < 0 ) continue;

			if ( j != 0 ) strcat( monsterStr, "|" );                           //MLHIDE

			sprintf( num, "%d|%d", k, i );                                     //MLHIDE
			strcat( monsterStr, num );

			j++;
		}
		// ここはエスケープしない。
		strncpy( monsterSendStr, monsterStr, sizeof( monsterSendStr ) );

		nrproto_TROP_send( sockfd, itemSendStr, monsterSendStr, tradeOwnerGold );
		tradeOwnerMode = 1;

		// スクロールバーの位置初期化
		Tr_T_ScrNum = 0;
		NumToScrollVMove( &wI->sw[EnumTradeTradeScrollBtn], 2, Tr_T_ScrNum );

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_ItemOpenOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ItemOpenOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_ItemOpenOff;

	return ReturnFlag;
}

BOOL MenuSwitchTradeBtClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_TRADE_CLOSE );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// プロトコル送信
		nrproto_TRCL_send( sockfd );
		tradeOwnerMode = 0;
		if( tradeOpponentMode == 2 ) tradeOpponentMode = 1;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		ModeChgAcc = 23;

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_CloseButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_CloseButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_CloseButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTradeBtTradeOK( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_TRADE_TRADEOK );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// トレードＯＫボタンが押されてない状态のとき
		if( tradeOwnerMode == 1 && tradeOpponentMode >= 1 ){
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){	// 连射抑制
				nrproto_TROC_send( sockfd, 1 );
				tradeOwnerMode = 2;
				tradeOkFlag = 1;
				tradeCancelFlag = 1;
////				if( tradeOpponentMode == 2 ) closeTradeWindow();
				fieldBtnPushTime = GetTickCount();
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
			}
		}else
		// トレードＯＫボタンが押された状态のとき
		if( tradeOwnerMode == 2 ){
			if( fieldBtnPushTime+FIELD_BTN_PUSH_WAIT < GetTickCount() ){	// 连射抑制
				nrproto_TRCL_send( sockfd );
				tradeOwnerMode = 0;
				if( tradeOpponentMode == 2 ) tradeOpponentMode = 1;

				fieldBtnPushTime = GetTickCount();
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
			}
		}else{
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_TradeOKOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_TradeOKOver;
	if( tradeOwnerMode == 2 ) Graph->graNo = GID_TradeOKOff;

	return ReturnFlag;
}

BOOL MenuSwitchGetTopPrio( int no, unsigned int flag )
{
	return FALSE;
}

//========================================
// パネル

// モンスターの状态表示 ++++
void DrawTradeMonsterStatus( short x, short y, char side, int no, int FontPrio, int DispPrio, BLT_MEMBER *bm )
{
	char str[255];

	if (tradeMonster[side][no].useFlag){
		// プライオリティの制御
		FontBufCut(FontPrio);
		StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, "", 0, 0 );

		// 名称表示
		StockFontBuffer( x+45, y+5, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, tradeMonster[side][no].name, 0, 0 );
		// 等级表示
		sprintf( str, "%3d", tradeMonster[side][no].lv );                   //MLHIDE
		StockFontBuffer( x+35, y+22, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// 种族表示
		if ( (tradeMonster[side][no].tribe>=0) && (tradeMonster[side][no].tribe< (sizeof(characterTribeStr)>>2) ) ){
			StockFontBuffer( x+100, y+22, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, characterTribeStr[tradeMonster[side][no].tribe], 0, 0 );
		}
		// LP表示
		sprintf( str, "%4d", tradeMonster[side][no].maxLp );                //MLHIDE
		StockFontBuffer( x+210, y+5, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// FP表示
		sprintf( str, "%4d", tradeMonster[side][no].maxFp );                //MLHIDE
		StockFontBuffer( x+210, y+22, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
#ifdef PUK2_NEW_MENU
		// VTL表示
		sprintf( str, "%3d", tradeMonster[side][no].vit );                  //MLHIDE
		StockFontBuffer( x+95, y+40, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// STR表示
		sprintf( str, "%3d", tradeMonster[side][no].str );                  //MLHIDE
		StockFontBuffer( x+155, y+40, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// TGH表示
		sprintf( str, "%3d", tradeMonster[side][no].tgh );                  //MLHIDE
		StockFontBuffer( x+215, y+40, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// QUI表示
		sprintf( str, "%3d", tradeMonster[side][no].qui );                  //MLHIDE
		StockFontBuffer( x+95, y+55, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// MGC表示
		sprintf( str, "%3d", tradeMonster[side][no].mgc );                  //MLHIDE
		StockFontBuffer( x+155, y+55, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
		// HMC表示
		sprintf( str, "%3d", tradeMonster[side][no].hmg );                  //MLHIDE
		StockFontBuffer( x+215, y+55, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
#endif
		// 技最大数表示
		sprintf( str, "%2d", tradeMonster[side][no].maxTech );              //MLHIDE
		StockFontBuffer( x+45, y+55, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_BLACK|FONT_PAL_NOSHADOW, str, 0, 0 );
	}

	// パネル
	StockDispBuffer_PUK2( x, y, DispPrio, GID_MonsterPanel, 0, 1, bm );
}

static char CheckMouseHit( int x, int y, int w, int h )
{
	int dx, dy;

	dx = mouse.nowPoint.x - x;
	if( dx >= 0 && dx < w ){
		dy = mouse.nowPoint.y - y;
		if( dy >= 0 && dy < h ){
			return 1;
		}
	}
	return 0;
}

const short Tr_ItemPos[4]   ={  12, 240,  80, 15 };
const short Tr_MonsterPos[4]={  97, 240,  80, 15 };
const short Tr_MoneyPos[4]  ={ 182, 240,  80, 15 };

const short Tr_ScrUpPos[4]  ={ 252,  28,  11, 11 };
const short Tr_ScrDownPos[4]={ 252, 220,  11, 11 };

static char *TradecalcuwinOneLineInfo[]={
	MWONELINE_BANK_BANKMONEYNUM,
	MWONELINE_BANK_BANKMONEYOK_ON,
	MWONELINE_BANK_BANKMONEYOK_OFF,
	MWONELINE_BANK_BANKMONEYBS,
	MWONELINE_BANK_BANKMONEYALL,
	MWONELINE_BANK_BANKMONEYCLR,
};

BOOL TradePanelFunc(
	WINDOW_INFO *wi, int no, unsigned int flag, const int *PanelGraNo,
	char *Mode, int ScrollNo, int *ScrollNum, int CalculatorNo, int Max,
	int Num1, int Num2, int Num3, BLT_MEMBER *bm,
	void (*Func)( short x, short y, short mousex, short mousey, unsigned int flag, char *Mode,
		int ScrNum, char CalRet, int *CalNum, int *Num1, int *Num2, int *Num3, int *Num4, BLT_MEMBER *bm )
	)
{
	int GraNo;
	short x = wi->wx+wi->sw[no].ofx;
	short y = wi->wy+wi->sw[no].ofy;
	int Btn = -1;
	char ret;
	char str[50];
	int Num4;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		*ScrollNum = WheelToMove( &wi->sw[ScrollNo],
			 *ScrollNum, 2, mouse.wheel );
	}

	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wi->sw[ScrollNo].Switch )->status&1 ){
		*ScrollNum = ScrollVPointToNum( &wi->sw[ScrollNo], 2 );
	}

	// ボタンの状态の取得
	if ( flag & MENU_MOUSE_OVER ){
		if ( CheckMouseHit( x+Tr_ItemPos[0], y+Tr_ItemPos[1], Tr_ItemPos[2], Tr_ItemPos[3] ) ) Btn = 0;
		if ( CheckMouseHit( x+Tr_MonsterPos[0], y+Tr_MonsterPos[1], Tr_MonsterPos[2], Tr_MonsterPos[3] ) ) Btn = 1;
		if ( CheckMouseHit( x+Tr_MoneyPos[0], y+Tr_MoneyPos[1], Tr_MoneyPos[2], Tr_MoneyPos[3] ) ) Btn = 2;

		if ( CheckMouseHit( x+Tr_ScrUpPos[0], y+Tr_ScrUpPos[1], Tr_ScrUpPos[2], Tr_ScrUpPos[3] ) ) Btn = 3;
		if ( CheckMouseHit( x+Tr_ScrDownPos[0], y+Tr_ScrDownPos[1], Tr_ScrDownPos[2], Tr_ScrDownPos[3] ) ) Btn = 4;
	}
	if ( flag & MENU_MOUSE_LEFT ){
		if ( 0<=Btn && Btn<=2 ){
			if (*Mode!=Btn){
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
			}else{
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
			*Mode = Btn;
			MenuTradeGetKeyForcus( 0, MENU_MOUSE_LEFT );
		}
	}

	if ( flag & MENU_MOUSE_LEFTAUTO ){
		if (*Mode == 1){
			if (Btn==3){
				if (*ScrollNum>0){
					(*ScrollNum)--;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				NumToScrollVMove( &wi->sw[ScrollNo], 2, *ScrollNum );
			}
			if (Btn==4){
				if (*ScrollNum<2){
					(*ScrollNum)++;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}else{
					// ＮＧ音（短い）
					play_se( SE_NO_NG, 320, 240 );
				}
				NumToScrollVMove( &wi->sw[ScrollNo], 2, *ScrollNum );
			}
		}
	}
	ret = 0;
	if (CalculatorNo>=0){
		if ( *Mode == 2 ){
			ret = CalculatorControl( wi, CalculatorNo, flag, Max, DISP_PRIO_WIN2, TradecalcuwinOneLineInfo,
				CALCULATOR_OPT_ZERO_OK | (pNowInputStr==&TradeInputStr?CALCULATOR_OPT_SHORTCUT:0), bm->rgba.rgba );
		}
	}

	if (Func) Func( x, y, mouse.nowPoint.x, mouse.nowPoint.y, flag, Mode, *ScrollNum, ret, &wi->sw[CalculatorNo].status, &Num1, &Num2, &Num3, &Num4, bm );

	// アイテムボタン
	if ( *Mode == 0 ) GraNo = GID_ItemChgOff;
	else{
		GraNo = GID_ItemChgOn;
		if ( Btn == 0 ) GraNo = GID_ItemChgOver;
	}
	StockDispBuffer_PUK2( x+Tr_ItemPos[0], y+Tr_ItemPos[1], DISP_PRIO_WIN2, GraNo, 0, 1, bm );

	// モンスターボタン
	if ( *Mode == 1 ) GraNo = GID_MonsterChgOff;
	else{
		GraNo = GID_MonsterChgOn;
		if ( Btn == 1 ) GraNo = GID_MonsterChgOver;
	}
	StockDispBuffer_PUK2( x+Tr_MonsterPos[0], y+Tr_MonsterPos[1], DISP_PRIO_WIN2, GraNo, 0, 1, bm );

	// お金ボタン
	if ( *Mode == 2 ) GraNo = GID_MoneyChgOff;
	else{
		GraNo = GID_MoneyChgOn;
		if ( Btn == 2 ) GraNo = GID_MoneyChgOver;
	}
	StockDispBuffer_PUK2( x+Tr_MoneyPos[0], y+Tr_MoneyPos[1], DISP_PRIO_WIN2, GraNo, 0, 1, bm );

	wi->sw[ScrollNo].Enabled = FALSE;
	wi->sw[ScrollNo-1].Enabled = FALSE;
	if ( *Mode == 1 ){
		// スクロールバーつまみ
		StockDispBuffer_PUK2( wi->wx+wi->sw[ScrollNo-1].ofx, wi->wy+wi->sw[ScrollNo-1].ofy, DISP_PRIO_WIN2, GID_ScrollBar, 0, 1, bm );

		// スクロールバー上ボタン
		GraNo = GID_UpButtonOn;
		if ( Btn == 3 ){
			GraNo = GID_UpButtonOver;
			if( flag & MENU_MOUSE_LEFTHOLD ) GraNo = GID_UpButtonOff;
		}
		StockDispBuffer_PUK2( x+Tr_ScrUpPos[0], y+Tr_ScrUpPos[1], DISP_PRIO_WIN2, GraNo, 0, 1, bm );

		// スクロールバー上ボタン
		GraNo = GID_DownButtonOn;
		if ( Btn == 4 ){
			GraNo = GID_DownButtonOver;
			if( flag & MENU_MOUSE_LEFTHOLD ) GraNo = GID_DownButtonOff;
		}
		StockDispBuffer_PUK2( x+Tr_ScrDownPos[0], y+Tr_ScrDownPos[1], DISP_PRIO_WIN2, GraNo, 0, 1, bm );

		wi->sw[ScrollNo].Enabled = TRUE;
		wi->sw[ScrollNo-1].Enabled = TRUE;
	}

	if ( *Mode == 2 ){
		sprintf( str, "%9d", Num4 );                                        //MLHIDE
		DrawGraphicNumber( x+179, y+88, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );
	}

	sprintf( str, "%3d", Num1 );                                         //MLHIDE
	DrawGraphicNumber( x+71, y+258, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );

	sprintf( str, "%3d", Num2 );                                         //MLHIDE
	DrawGraphicNumber( x+156, y+258, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );

	sprintf( str, "%10d", Num3 );                                        //MLHIDE
	DrawGraphicNumber( x+241, y+258, str, G_NUM_SIZE__9_S, FONT_PAL_WHITE, G_NUM_FLAG_RIGHT_JUSTIFIED, DISP_PRIO_WIN2 );

	// パネル
	StockDispBuffer_PUK2( x, y, DISP_PRIO_WIN2, PanelGraNo[*Mode], 0, 1, bm );

	return (flag & MENU_MOUSE_LEFT);
}

void TradeMyItemPanelFunc( char *Mode, short xx, short yy, short mousex, short mousey, unsigned int flag, BLT_MEMBER *bm )
{
	int x, y;
	int itemNo, DragitemNo;
	int i;
	char str[256];
	static int olditemNo;
	BLT_MEMBER bm2={0};

	bm2.rgba.rgba=0x80ffffff;
	bm2.bltf=BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
		}
	}

	// オープンアイテム选择中なら
	if ( TradeMode != TRADEMODE_A ) flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		int DrapPointX = WinDD_DropX();
		int DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		int DropitemNo = (int)WinDD_ObjData();
#else
		int DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]) continue;
			x = xx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = yy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo+MAX_EQUIP_ITEM ) ItemMove( MENU_WINDOW_TRADE, EnumTradeMyPanel, DropitemNo, itemNo+MAX_EQUIP_ITEM );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	if (*Mode != 0) return;

	itemNo = -1;
	if ( flag&(MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if (ItemNoOpe[i+MAX_EQUIP_ITEM]) continue;
			x = xx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = yy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i+MAX_EQUIP_ITEM;	break; }
		}
	}

	//重なってるかチェック
	if(itemNo>=0){
		// オープンアイテム选择中なら
		if ( TradeMode == TRADEMODE_A ){
			// その场所にアイテムがあるなら
			if ( pc.item[itemNo].useFlag ){
				// 选择不可能なアイテムに禁止マークをつける
				if( !(pc.item[itemNo].flag&ITEM_ETC_FLAG_SEND) ) strcpy( OneLineInfoStr, MWONELINE_TRADE_MYITEM_OFF );
				else if (!Tr_ItemselFlag[itemNo]) strcpy( OneLineInfoStr, MWONELINE_TRADE_MYITEM_ON );
			}
		}
	}

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( Tr_olditemInfoType == 0 ){
		if ( olditemNo != itemNo ) Tr_itemInfoPage = 0;
		olditemNo = itemNo;
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分以外のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_TRADE || WinDD_WinType()==MENU_WINDOW_NONE ){
			// その场所に登録前のアイテムがあるなら
			if ( pc.item[itemNo].useFlag &&
				(pc.item[i+MAX_EQUIP_ITEM].flag&ITEM_ETC_FLAG_SEND) && !Tr_ItemselFlag[itemNo] ){
				for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
					if ( getOpenItem(i) < 0 ) break;
				}
				if ( i < MAX_DRAW_WIN_ITEM ){
					// 先ずは场所を空ける
					cancelOpenItem(i);
					// アイテムを登録
					registOpenItem( i, itemNo );

					*Mode = 0;
				}
				if (Tr_OpenMode != 0) Tr_OpenMode = 0;
			}
			else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
	}
	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		if( itemNo >= 0 ){
			// オープンアイテム选择中なら
			if ( TradeMode == TRADEMODE_A ){
				if ( flag & MENU_MOUSE_LEFT ){
					// その场所にアイテムがあるなら
					if ( pc.item[itemNo].useFlag && !Tr_ItemselFlag[itemNo] ){
						// ドラッグ开始
#ifdef PUK2_NEWDRAG
						DragItem( itemNo, TRUE );
#else
						WinDD_DragStart( WINDD_ITEM, (void *)(itemNo) );
#endif
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
					}
				}
			}
			if ( flag & MENU_MOUSE_RIGHT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo].useFlag ){
					Tr_itemInfoPage++;
					if( Tr_itemInfoPage >= pc.item[itemNo].memoPage ) Tr_itemInfoPage = 0;
				}
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeMyPanel ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( !pc.item[DragitemNo].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mousex, mousey );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeMyPanel ){
#ifdef PUK2_NEWDRAG
#else
			// 掴んだアイテムの表示
			StockDispBuffer( mousex, mousey, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
#endif

			// アイテムを掴んだ位置に枠表示
			x = xx + 13 + ( ( (DragitemNo-MAX_EQUIP_ITEM) % ITEM_DRAW_COLUMN ) * 50 );
			y = yy + 26 + ( ( (DragitemNo-MAX_EQUIP_ITEM) / ITEM_DRAW_COLUMN ) * 52 );
			StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		if( itemNo >= 0 ){
			// アイテム选择枠
			x = xx + 13 + ( ( (itemNo-MAX_EQUIP_ITEM) % ITEM_DRAW_COLUMN ) * 50 );
			y = yy + 26 + ( ( (itemNo-MAX_EQUIP_ITEM) / ITEM_DRAW_COLUMN ) * 52 );
			StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

			if ( pc.item[itemNo].useFlag ){
				Tr_itemInfoNo = itemNo;
				// 刻印されていたら１行インフォに表示
				if( (strlen( pc.item[itemNo].freeName ) > 0 ) && (pc.item[itemNo].flag & ITEM_ETC_FLAG_INCUSE) ){
					sprintf( str, "%s" ITEM_INCUSE_STRING, pc.item[itemNo].name );   //MLHIDE
					strcpy( OneLineInfoStr, str );
				}else if( pc.item[itemNo].flag & ITEM_ETC_FLAG_HANKO ){
					//ハンコ
					sprintf( str, "%s" ITEM_HANKO_STRING, pc.item[itemNo].freeName ); //MLHIDE
					strcpy( OneLineInfoStr, str );
				}
			}
			Tr_olditemInfoType = 0;
		}
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	bm2=*bm;
	bm2.rgba.a = bm2.rgba.a>>1;
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムが无いなら次へ
		if( !pc.item[i+MAX_EQUIP_ITEM].useFlag ) continue;

		x = xx + 37 + ( (i%ITEM_DRAW_COLUMN) * 50 );
		y = yy + 51 + ( (i/ITEM_DRAW_COLUMN) * 52 );

		// 枠表示
		if ( Tr_ItemselFlag[i+MAX_EQUIP_ITEM] > 0 ){
			StockBoxDispBuffer( x-21, y-21, x+21, y+21, DISP_PRIO_WIN2, SYSTEM_PAL_YELLOW, 0 );
		}else if ( ItemNoOpe[i+MAX_EQUIP_ITEM] ){
			StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
		}

		// 选择不可能なアイテムに禁止マークをつける
		if( !(pc.item[i+MAX_EQUIP_ITEM].flag&ITEM_ETC_FLAG_SEND) ){
			StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );
		}

		// 枠表示
		if ( Tr_ItemselFlag[i+MAX_EQUIP_ITEM] > 0 || ItemNoOpe[i+MAX_EQUIP_ITEM] ){
			StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
		}else{
			StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, bm );
		}

		// 个数表示
		if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
			sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );              //MLHIDE
			StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
		}
	}
}

void TradeMyMonsterPanelFunc( char *Mode, short xx, short yy, short mousex, short mousey, unsigned int flag, int ScrNum, BLT_MEMBER *bm )
{
	int x, y;
	int itemNo, DragitemNo, RealNo;
	int i;
	const short YPos[3] = { 26, 95, 164 };
	BLT_MEMBER bm2={0};

	bm2.rgba.rgba=0x80ffffff;
	bm2.bltf=BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_MONSTER ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
		}
	}

	// オープンアイテム选择中なら
	if ( TradeMode != TRADEMODE_A ) flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);

	// モンスターがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		int DrapPointX = WinDD_DropX();
		int DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		int DropitemNo = (int)WinDD_ObjData();
#else
		int DropitemNo = (int)WinDD_GetObject();
#endif

		// ドロップされた场所を调べる
		itemNo = -1;
		for(i=0;i<MAX_PET;i++){
			x = xx + 11;
			y = yy + YPos[i];
			// 四角のあたり判定
			if( DrapPointX < x+240 && x <= DrapPointX && DrapPointY < y+69 && y <= DrapPointY ){
				itemNo = i+ScrNum;
				break;
			}
		}
		if ( itemNo>=0 && !Tr_MonsselFlag[ sortPet[itemNo].index ] ){
			MonsMove( MENU_WINDOW_TRADE, EnumTradeMyPanel, DropitemNo, sortPet[itemNo].index );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	if (*Mode != 1) return;

	itemNo = -1;
	if ( flag&(MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<3;i++){
			x = xx + 11;
			y = yy + YPos[i];
			if ( MakeHitBox( x, y, x+240, y+69, -1 ) ){ itemNo = i+ScrNum;	break; }
		}
	}
	RealNo = -1;
	if ( itemNo >= 0 ){
		RealNo = sortPet[itemNo].index;
	}

	//重なってるかチェック
	if(RealNo>=0) strcpy( OneLineInfoStr, MWONELINE_TRADE_MYMONSTER );

	// モンスター栏を左ダブルクリックしたとき
	if ( ( RealNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分以外のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_TRADE || WinDD_WinType()==MENU_WINDOW_NONE ){
			// その场所に登録前のモンスターがいるなら
			if ( pet[RealNo].useFlag && !Tr_MonsselFlag[RealNo] ){
				for(i=0;i<MAX_PET;i++){
					if ( getOpenMons(i) < 0 ) break;
				}
				if ( i < MAX_PET ){
					// 先ずは场所を空ける
					cancelOpenMons(i);
					// モンスターを登録
					registOpenMons( i, RealNo );

					*Mode = 1;
				}
				if (Tr_OpenMode != 1) Tr_OpenMode = 1;
			}
			else play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
	}
	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		if( RealNo >= 0 ){
			// オープンアイテム选择中なら
			if ( TradeMode == TRADEMODE_A ){
				if ( flag & MENU_MOUSE_LEFT ){
					// その场所にモンスターがいるなら
					if ( pet[RealNo].useFlag && !Tr_MonsselFlag[RealNo] ){
						// ドラッグ开始
#ifdef PUK2_NEWDRAG
						DragMons( RealNo, 1 );
#else
						WinDD_DragStart( WINDD_MONSTER, (void *)(RealNo) );
#endif
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
					}
				}
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_MONSTER ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeMyPanel ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にモンスターがいないならドラッグ終了
			if ( !pet[DragitemNo].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_MONSTER, (void *)(DragitemNo), NULL, mousex, mousey );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_MONSTER ){
		DragitemNo = (int)WinDD_ObjData();

		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeMyPanel ){
#ifdef PUK2_NEWDRAG
#else
			// 掴んだモンスターの表示
			DrawMyMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, DragitemNo, FONT_PRIO_DRAG );
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm2 );
#endif

			i = getsortMonsPos( DragitemNo ) - ScrNum;
			if ( 0<=i && i<3 ){
				// モンスターを掴んだ位置に枠表示
				x = xx + 11;
				y = yy + YPos[i];
				StockBoxDispBuffer( x, y, x+240, y+69, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
			}
		}
	}

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		if( 0 <= itemNo-ScrNum && itemNo-ScrNum < 3 ){
			// モンスター选择枠
			x = xx + 11;
			y = yy + YPos[itemNo-ScrNum];
			StockBoxDispBuffer( x, y, x+240, y+69, DISP_PRIO_WIN2, BoxColor, 0 );
			if (pet[RealNo].useFlag) Tr_itemInfoNo = RealNo + 300;
		}
	}

	bm2=*bm;
	bm2.rgba.a = bm2.rgba.a>>1;
	for(i=0;i<3;i++){
		x = xx + 11;
		y = yy + YPos[i];
		if ( Tr_MonsselFlag[ sortPet[ScrNum+i].index ] > 0 ){
			StockBoxDispBuffer( x+2, y+2, x+238, y+67, DISP_PRIO_WIN2, SYSTEM_PAL_YELLOW, 0 );
		}
		DrawMyMonsterStatus( x, y, sortPet[ScrNum+i].index, FONT_PRIO_WIN );
		StockDispBuffer( x+120, y+35, DISP_PRIO_WIN2, GID_MonsterPanel, 0, bm );
	}
}

static void MyPanelFunc( short xx, short yy, short mousex, short mousey, unsigned int flag, char *Mode,
	int ScrNum, char CalRet, int *CalNum, int *Num1, int *Num2, int *Num3, int *Num4, BLT_MEMBER *bm )
{
	int i, j;

	if (CalRet&CALCULATOR_OK){
		if (Tr_OpenMode != 2) Tr_OpenMode = 2;
	}

	TradeMyItemPanelFunc( Mode, xx, yy, mousex, mousey, flag, bm );
	TradeMyMonsterPanelFunc( Mode, xx, yy, mousex, mousey, flag, ScrNum, bm );

	if ( CalRet&CALCULATOR_OK ){
		tradeOwnerGold = *CalNum;
		*CalNum = 0;
	}
	*Num4 = *CalNum;

	// Num1, Num2 の设定
	j=0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){ if( tradeItem[1][i].useFlag ) j++; }
	*Num1 = j;
	j=0;
	for( i = 0; i < MAX_PET; i++ ){ if( tradeMonster[1][i].useFlag ) j++; }
	*Num2 = j;
}

const int MyPanelGraNo[3]  ={ GID_My_Item, GID_My_Monster, GID_My_Gold };
BOOL MenuSwitchTradeMyPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag;
	BLT_MEMBER bm={0};

	bm.rgba.rgba=( (GRAPHIC_SWITCH *)wI->sw[no].Switch )->rgba;
	bm.bltf=BLTF_NOCHG;

	ReturnFlag = TradePanelFunc(
					wI, no, flag, MyPanelGraNo,
					&Tr_MyMode, EnumTradeMyScrollBtn, &Tr_M_ScrNum, EnumTradeCalculator, pc.gold,
					0, 0, pc.gold, &bm,
					MyPanelFunc
					);

	return ReturnFlag;
}

#ifdef PUK2_NEWDRAG

void DragOpenItemFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// ドラッグ元にアイテムが无いならドラッグ終了
		if ( getOpenItem(itemNo)<0 || !pc.item[ getOpenItem(itemNo) ].useFlag ) WinDD_DragFinish();
	
		// 右键したらアイテムドロップ
		if ( mouse.onceState & MOUSE_LEFT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_TRADEITEM, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragOpenItemFunc );
		}
		// 右クリックしたらドラッグ終了
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragCancel();
	
#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[ getOpenItem(itemNo) ].graNo, 0, &bm );
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		cancelOpenItem(itemNo);
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
		break;
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
		break;
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		cancelOpenItem(itemNo);
		break;
	}
}

void DragOpenItem( int itemNo )
{
	// ドラッグ开始
	WinDD_DragStart( WINDD_TRADEITEM, (void *)itemNo, DragOpenItemFunc );
}

#endif

void TradeOpenItemPanelFunc( char *Mode, short xx, short yy, short mousex, short mousey, unsigned int flag, BLT_MEMBER *bm )
{
	int x, y;
	int itemNo, DragitemNo;
	int i, k;
	char str[256];
	static int olditemNo;
	BLT_MEMBER bm2={0};

	bm2.rgba.rgba=0x80ffffff;
	bm2.bltf=BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
#ifdef PUK2_NEWDRAG
		if ( WinDD_CheckObjType() != WINDD_ITEM &&
			 WinDD_CheckObjType() != WINDD_TRADEITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
		}
#else
		if ( WinDD_CheckObjType()!=WINDD_ITEM ){
			if ( WinDD_CheckObjType()==WINDD_TRADEITEM ){
				flag &= ~MENU_MOUSE_DROP;
			}else{
				flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
			}
		}
#endif
	}

	// オープンアイテム选择中なら
	if ( TradeMode != TRADEMODE_A ) flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);

	if (*Mode != 0){
		// アイテムがドロップされたら
		if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
			int DropitemNo = (int)WinDD_ObjData();
#else
			int DropitemNo = (int)WinDD_GetObject();
#endif

			if ( checkItemFrom(DropitemNo) == ITEMFROMHAVE ){
				for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
					if ( getOpenItem(i) < 0 ) break;
				}
				if ( i < MAX_DRAW_WIN_ITEM ){
					if ( pc.item[DropitemNo].flag & ITEM_ETC_FLAG_SEND ){
						// 先ずは场所を空ける
						cancelOpenItem(i);
						// アイテムを登録
						registOpenItem( i, DropitemNo );

						*Mode = 0;
					}
				}
			}
#ifdef PUK2_NEWDRAG
			WinDD_AcceptObject();
#endif
		}

		if ( flag & MENU_MOUSE_DRAGOVER ){
			x = xx;
			y = yy;
			StockBoxDispBuffer( x, y, x+273, y+280, DISP_PRIO_WIN2, BoxColor, 0 );
		}

		return;
	}else{
		// アイテムがドロップされたら
		if ( flag & MENU_MOUSE_DROP ){
			int DrapPointX = WinDD_DropX();
			int DrapPointY = WinDD_DropY();

			int DropObjType = WinDD_CheckObjType();
#ifdef PUK2_NEWDRAG
			int DropitemNo = (int)WinDD_ObjData();

			// ドロップされた场所を调べる
			itemNo = -1;
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				x = xx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = yy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
				// 四角のあたり判定
				if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
					itemNo = i;
					break;
				}
			}

			if (itemNo >= 0 ){
				if ( DropObjType == WINDD_ITEM ){
					if ( checkItemFrom(DropitemNo) == ITEMFROMHAVE ){
						if ( pc.item[DropitemNo].flag & ITEM_ETC_FLAG_SEND ){
							// 先ずは场所を空ける
							cancelOpenItem(itemNo);
							// アイテムを登録
							registOpenItem( itemNo, DropitemNo );
						}
					}
				}else if ( DropObjType == WINDD_TRADEITEM ){
					swapOpenItem( DropitemNo, itemNo );
				}
	
				WinDD_AcceptObject();
			}
#else
			int DropitemNo = (int)WinDD_GetObject();

			// ドロップされた场所を调べる
			itemNo = -1;
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				x = xx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = yy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
				// 四角のあたり判定
				if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
					itemNo = i;
					break;
				}
			}
			if ( itemNo >= 0 ){
				if ( checkItemFrom(DropitemNo) == ITEMFROMHAVE ){
					if ( pc.item[DropitemNo].flag & ITEM_ETC_FLAG_SEND ){
						// 先ずは场所を空ける
						cancelOpenItem(itemNo);
						// アイテムを登録
						registOpenItem( itemNo, DropitemNo );
					}
				}
			}
#endif
		}
	}

	// 登録场所とのあたり判定
	itemNo = -1;
	if ( flag&(MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			x = xx + 13 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = yy + 26 + ( (i/ITEM_DRAW_COLUMN) * 52 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	//重なってるかチェック
	if ( TradeMode == TRADEMODE_A ){
		if(itemNo>=0) strcpy( OneLineInfoStr, MWONELINE_TRADE_OPENITEM );
	}

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( Tr_olditemInfoType == 1 ){
		if ( olditemNo != itemNo ) Tr_itemInfoPage = 0;
		olditemNo = itemNo;
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// オープンアイテム选择中なら
		if ( TradeMode == TRADEMODE_A ){
			// 自分以外のウィンドウがドラッグしていないとき
			if ( WinDD_WinType()==MENU_WINDOW_TRADE || WinDD_WinType()==MENU_WINDOW_NONE ){
				// その场所の登録を解除し、できなかったらＮＧ音
				if ( cancelOpenItem(itemNo) < 0 ){
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}
			}
		}else{
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
	}
	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// その场所に登録されたアイテムがあるなら
		if( itemNo>=0 && getOpenItem(itemNo)>=0 ){
			// オープンアイテム选择中なら
			if ( TradeMode == TRADEMODE_A ){
				if ( flag & MENU_MOUSE_LEFT ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragOpenItem( itemNo );
#else
					WinDD_DragStart( WINDD_TRADEITEM, (void *)(itemNo) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
			if ( flag & MENU_MOUSE_RIGHT ){
				Tr_itemInfoPage++;
				if( Tr_itemInfoPage >= pc.item[ getOpenItem(itemNo) ].memoPage ) Tr_itemInfoPage = 0;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_TRADEITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeOpenPanel ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( getOpenItem(DragitemNo)<0 || !pc.item[ getOpenItem(DragitemNo) ].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				// 枠の上でないなら登録解除
				if (itemNo<0) cancelOpenItem(DragitemNo);
				// 枠の上なら场所を入れ替える
				else swapOpenItem( DragitemNo, itemNo );

				WinDD_DragFinish();
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_TRADEITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeOpenPanel ){
			DragitemNo = (int)WinDD_ObjData();

			// ドラッグ元にアイテムがあるなら
			if ( getOpenItem(DragitemNo)>=0 && pc.item[ getOpenItem(DragitemNo) ].useFlag ){
#ifdef PUK2_NEWDRAG
#else
				// 掴んだアイテムの表示
				StockDispBuffer( mousex, mousey, DISP_PRIO_DRAG, pc.item[ getOpenItem(DragitemNo) ].graNo, 0, &bm2 );
#endif

				// アイテムを掴んだ位置に枠表示
				x = xx + 13 + ( (DragitemNo%ITEM_DRAW_COLUMN) * 50 );
				y = yy + 26 + ( (DragitemNo/ITEM_DRAW_COLUMN) * 52 );
				StockBoxDispBuffer( x, y, x+48, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
			}
		}
	}

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		if( itemNo >= 0 ){
			// アイテム选择枠
			x = xx + 13 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
			y = yy + 26 + ( (itemNo/ITEM_DRAW_COLUMN) * 52 );
			StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

			k = getOpenItem(itemNo);
			// アイテムがあるなら
			if ( k>=0 && pc.item[k].useFlag ){
				Tr_itemInfoNo = k;
				// 刻印されていたら１行インフォに表示
				if( (strlen( pc.item[k].freeName ) > 0 ) && (pc.item[k].flag & ITEM_ETC_FLAG_INCUSE) ){
					sprintf( str, "%s" ITEM_INCUSE_STRING, pc.item[k].name );        //MLHIDE
					strcpy( OneLineInfoStr, str );
				}else if( pc.item[k].flag & ITEM_ETC_FLAG_HANKO ){
					//ハンコ
					sprintf( str, "%s" ITEM_HANKO_STRING, pc.item[k].freeName );     //MLHIDE
					strcpy( OneLineInfoStr, str );
				}
			}
			Tr_olditemInfoType = 1;
		}
	}

	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		k = getOpenItem(i);
		// アイテムが无いなら次へ
		if( k<0 || !pc.item[k].useFlag ) continue;

		// 选择不可能なアイテムは、选择解除する
		if( !(pc.item[k].flag&ITEM_ETC_FLAG_SEND) ) cancelOpenItem(i);
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		k = getOpenItem(i);
		// アイテムが无いなら次へ
		if( k<0 || !pc.item[k].useFlag ) continue;
		x = xx + 37 + ( (i%ITEM_DRAW_COLUMN) * 50 );
		y = yy + 51 + ( (i/ITEM_DRAW_COLUMN) * 52 );

		// 选择不可能なアイテムに禁止マークをつける
		if( !(pc.item[k].flag&ITEM_ETC_FLAG_SEND) ){
			StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_TRADE_ITEM_SELECT_MASK_RED, 0 );
		}
		StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[k].graNo, 0, bm );

		// 个数表示
		if( pc.item[k].num > 0 ){
			sprintf( str, "%3d", pc.item[k].num );                             //MLHIDE
			StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
		}
	}
}

#ifdef PUK2_NEWDRAG

void DragOpenMonsFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// ドラッグ元にアイテムが无いならドラッグ終了
		if ( getOpenMons(itemNo)<0 || !pet[ getOpenMons(itemNo) ].useFlag ) WinDD_DragFinish();
	
		// 右键したらアイテムドロップ
		if ( mouse.onceState & MOUSE_LEFT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_TRADEMONSTER, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragOpenMonsFunc );
		}
		// 右クリックしたらドラッグ終了
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragCancel();
	
#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		DrawMyMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, getOpenMons(itemNo), FONT_PRIO_DRAG );
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm );
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		cancelOpenMons(itemNo);
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
		break;
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
		break;
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		cancelOpenMons(itemNo);
		break;
	}
}

void DragOpenMons( int itemNo )
{
	// ドラッグ开始
	WinDD_DragStart( WINDD_TRADEMONSTER, (void *)itemNo, DragOpenMonsFunc );
}

#endif

void TradeOpenMonsterPanelFunc( char *Mode, short xx, short yy, short mousex, short mousey, unsigned int flag, int ScrNum, BLT_MEMBER *bm )
{
	int x, y;
	int itemNo, DragitemNo;
	int i, j, k;
	const short YPos[3] = { 26, 95, 164 };
	BLT_MEMBER bm2={0};

	bm2.rgba.rgba=0x80ffffff;
	bm2.bltf=BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
#ifdef PUK2_NEWDRAG
		if ( WinDD_CheckObjType() != WINDD_MONSTER &&
			 WinDD_CheckObjType() != WINDD_TRADEMONSTER ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
		}
#else
		if ( WinDD_CheckObjType()!=WINDD_MONSTER ){
			if ( WinDD_CheckObjType()==WINDD_TRADEMONSTER ){
				flag &= ~MENU_MOUSE_DROP;
			}else{
				flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
			}
		}
#endif
	}

	// オープンアイテム选择中なら
	if ( TradeMode != TRADEMODE_A ) flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);

	if (*Mode != 1){
		// モンスターがドロップされたら
		if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
			int DropitemNo = (int)WinDD_ObjData();
#else
			int DropitemNo = (int)WinDD_GetObject();
#endif

			if ( checkMonsterFrom(DropitemNo) == MONSTERFROMPC ){
				for(i=0;i<MAX_PET;i++){
					if ( getOpenMons(i) < 0 ) break;
				}
				if ( i < MAX_PET ){
					// 先ずは场所を空ける
					cancelOpenMons(i);
					// モンスターを登録
					registOpenMons( i, DropitemNo );

					*Mode = 1;
				}
			}
#ifdef PUK2_NEWDRAG
			WinDD_AcceptObject();
#endif
		}

		if ( flag & MENU_MOUSE_DRAGOVER ){
			x = xx;
			y = yy;
			StockBoxDispBuffer( x, y, x+273, y+280, DISP_PRIO_WIN2, BoxColor, 0 );
		}

		return;
	}else{
		// モンスターがドロップされたら
		if ( flag & MENU_MOUSE_DROP ){
			int DrapPointX = WinDD_DropX();
			int DrapPointY = WinDD_DropY();

			int DropObjType = WinDD_CheckObjType();
#ifdef PUK2_NEWDRAG
			int DropitemNo = (int)WinDD_ObjData();

			// ドロップされた场所を调べる
			itemNo = -1;
			for(i=0;i<MAX_PET;i++){
				x = xx + 11;
				y = yy + YPos[i];
				// 四角のあたり判定
				if( DrapPointX < x+240 && x <= DrapPointX && DrapPointY < y+69 && y <= DrapPointY ){
					itemNo = i+ScrNum;
					break;
				}
			}

			if (itemNo >= 0 ){
				if ( DropObjType == WINDD_MONSTER ){
					if ( checkMonsterFrom(DropitemNo) == MONSTERFROMPC ){
						// 先ずは场所を空ける
						cancelOpenMons(itemNo);
						// モンスターを登録
						registOpenMons( itemNo, DropitemNo );
					}
				}else if ( DropObjType == WINDD_TRADEMONSTER ){
					swapOpenMons( DropitemNo, itemNo );
				}
	
				WinDD_AcceptObject();
			}
#else
			int DropitemNo = (int)WinDD_GetObject();

			// ドロップされた场所を调べる
			itemNo = -1;
			for(i=0;i<MAX_PET;i++){
				x = xx + 11;
				y = yy + YPos[i];
				// 四角のあたり判定
				if( DrapPointX < x+240 && x <= DrapPointX && DrapPointY < y+69 && y <= DrapPointY ){
					itemNo = i+ScrNum;
					break;
				}
			}
			if ( itemNo >= 0 ){
				if ( checkMonsterFrom(DropitemNo) == MONSTERFROMPC ){
					// 先ずは场所を空ける
					cancelOpenMons(itemNo);
					// モンスターを登録
					registOpenMons( itemNo, DropitemNo );
				}
			}
#endif
		}
	}

	if (*Mode != 1) return;

	itemNo = -1;
	if ( flag&(MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<3;i++){
			x = xx + 11;
			y = yy + YPos[i];
			if ( MakeHitBox( x, y, x+240, y+69, -1 ) ){ itemNo = i+ScrNum;	break; }
		}
	}

	//重なってるかチェック
	if ( TradeMode == TRADEMODE_A ){
		if(itemNo>=0) strcpy( OneLineInfoStr, MWONELINE_TRADE_OPENMONSTER );
	}

	// モンスター栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// オープンアイテム选择中なら
		if ( TradeMode == TRADEMODE_A ){
			// 自分以外のウィンドウがドラッグしていないとき
			if ( WinDD_WinType()==MENU_WINDOW_TRADE || WinDD_WinType()==MENU_WINDOW_NONE ){
				// その场所の登録を解除し、できなかったらＮＧ音
				if ( cancelOpenMons(itemNo) < 0 ){
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}
			}
		}else{
			play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
		}
	}
	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// その场所に登録されたモンスターがいるなら
		if( itemNo>=0 && getOpenMons(itemNo)>=0 ){
			// オープンアイテム选择中なら
			if ( TradeMode == TRADEMODE_A ){
				if ( flag & MENU_MOUSE_LEFT ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragOpenMons( itemNo );
#else
					WinDD_DragStart( WINDD_TRADEMONSTER, (void *)(itemNo) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_TRADEMONSTER ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeOpenPanel ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にモンスターがいないならドラッグ終了
			if ( getOpenMons(DragitemNo)<0 || !pet[ getOpenMons(DragitemNo) ].useFlag ) WinDD_DragFinish();
			// 右键したらモンスタードロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				// 枠の上でないなら登録解除
				if (itemNo<0) cancelOpenMons(DragitemNo);
				// 枠の上なら场所を入れ替える
				else swapOpenMons( DragitemNo, itemNo );

				WinDD_DragFinish();
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_TRADEMONSTER ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_TRADE && WinDD_ButtonNo()==EnumTradeOpenPanel ){
			DragitemNo = (int)WinDD_ObjData();

			// ドラッグ元にアイテムがあるなら
			if ( getOpenMons(DragitemNo)>=0 && pet[ getOpenMons(DragitemNo) ].useFlag ){
#ifdef PUK2_NEWDRAG
#else
				// 掴んだモンスターの表示
				DrawMyMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, getOpenMons(DragitemNo), FONT_PRIO_DRAG );
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm2 );
#endif

				DragitemNo -= ScrNum;
				if ( DragitemNo>=0 && DragitemNo<3 ){
					// モンスターを掴んだ位置に枠表示
					x = xx + 11;
					y = yy + YPos[DragitemNo];
					StockBoxDispBuffer( x, y, x+240, y+69, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
				}
			}
		}
	}

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		if( 0 <= itemNo-ScrNum && itemNo-ScrNum < 3 ){
			// モンスター选择枠
			x = xx + 11;
			y = yy + YPos[itemNo-ScrNum];
			StockBoxDispBuffer( x, y, x+240, y+69, DISP_PRIO_WIN2, BoxColor, 0 );

			k = getOpenMons(itemNo);
			// モンスターがいるなら
			if ( k>=0 && pet[k].useFlag ) Tr_itemInfoNo = 300 + k;
		}
	}

	// モンスターの表示
	j = 0;
	for(i=0;i<3;i++){
		x = xx + 11;
		y = yy + YPos[i];
		k = getOpenMons(i+ScrNum);

		// モンスターがいないなら次へ
		if( k<0 || !pet[k].useFlag ){
			// パネル
			StockDispBuffer_PUK2( x, y, DISP_PRIO_WIN2, GID_MonsterPanel, 0, 1, bm );
			continue;
		}

		DrawMyMonsterStatus( x, y, k, FONT_PRIO_WIN );
		StockDispBuffer( x+120, y+35, DISP_PRIO_WIN2, GID_MonsterPanel, 0, bm );
	}
}

static void OpenPanelFunc( short xx, short yy, short mousex, short mousey, unsigned int flag, char *Mode,
	int ScrNum, char CalRet, int *CalNum, int *Num1, int *Num2, int *Num3, int *Num4, BLT_MEMBER *bm )
{
	int i, j;

	if (CalRet&CALCULATOR_OK){
		if (Tr_MyMode != 2) Tr_MyMode = 2;
	}

	TradeOpenItemPanelFunc( Mode, xx, yy, mousex, mousey, flag, bm );
	TradeOpenMonsterPanelFunc( Mode, xx, yy, mousex, mousey, flag, ScrNum, bm );

	*Num4 = tradeOwnerGold;

	// Num1, Num2 の设定
	j=0;
	for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++ ){
		if ( getOpenItem(i) >= 0 ) j++;
	}
	*Num1 = j;
	j=0;
	for( i = 0, j = 0; i < MAX_PET; i++ ){
		if ( getOpenMons(i) >= 0 ) j++;
	}
	*Num2 = j;
	*Num3 = tradeOwnerGold;
}

const int OpenPanelGraNo[3]  ={ GID_OpenItemPanel, GID_OpenMonsterPanel, GID_OpenMoneyPanel };
BOOL MenuSwitchTradeOpenPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag;
	BLT_MEMBER bm={0};

	bm.rgba.rgba=( (GRAPHIC_SWITCH *)wI->sw[no].Switch )->rgba;
	bm.bltf=BLTF_NOCHG;

	if ( tradeOwnerMode == 2 ) bm.rgba.a >>= 1;

	ReturnFlag = TradePanelFunc(
					wI, no, flag, OpenPanelGraNo,
					&Tr_OpenMode, EnumTradeOpenScrollBtn, &Tr_O_ScrNum, -1, 0,
					0, 0, pc.gold, &bm,
					OpenPanelFunc
					);

	return ReturnFlag;
}

static void TradePanelFunc( short xx, short yy, short mousex, short mousey, unsigned int flag, char *Mode,
	int ScrNum, char CalRet, int *CalNum, int *Num1, int *Num2, int *Num3, int *Num4, BLT_MEMBER *bm )
{
	int x, y;
	int i,j;
	int itemNo;
	char str[256];
	static int olditemNo;

	if (*Mode == 0){
		itemNo = -1;
		if ( flag&MENU_MOUSE_OVER ){
			for(i=0;i<20;i++){
				x = xx + 13 + ( (i%5) * 50 );
				y = yy + 26 + ( (i/5) * 52 );
				if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
			}
		}

		//重なってるかチェック
		if(itemNo>=0){
			if ( tradeItem[0][itemNo].useFlag ) strcpy( OneLineInfoStr, MWONELINE_TRADE_TRADEITEM );
		}

		// カーソル位置が变わっていたらページ数を最初に戾す
		if ( Tr_olditemInfoType == 2 ){
			if ( olditemNo != itemNo ) Tr_itemInfoPage = 0;
			olditemNo = itemNo;
		}

		if( itemNo >= 0 ){
			if ( flag & MENU_MOUSE_RIGHT ){
				// その场所にアイテムがあるなら
				if ( tradeItem[0][itemNo].useFlag ){
					Tr_itemInfoPage++;
					if( Tr_itemInfoPage >= tradeItem[0][itemNo].memoPage ) Tr_itemInfoPage = 0;
				}
			}
		}

		if ( flag & MENU_MOUSE_OVER ){
			if( itemNo >= 0 ){
				// アイテム选择枠
				x = xx + 13 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
				y = yy + 25 + ( (itemNo/ITEM_DRAW_COLUMN) * 52 );
				StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

				if ( tradeItem[0][itemNo].useFlag ){
					Tr_itemInfoNo = itemNo + 200;
					// 刻印されていたら１行インフォに表示
					if( (strlen( tradeItem[0][itemNo].freeName ) > 0 ) && (tradeItem[0][itemNo].otherflg & ITEM_ETC_FLAG_INCUSE) ){
						sprintf( str, "%s" ITEM_INCUSE_STRING, tradeItem[0][itemNo].name ); //MLHIDE
						strcpy( OneLineInfoStr, str );
					}else if( tradeItem[0][itemNo].otherflg & ITEM_ETC_FLAG_HANKO ){
						//ハンコ
						sprintf( str, "%s" ITEM_HANKO_STRING, tradeItem[0][itemNo].freeName ); //MLHIDE
						strcpy( OneLineInfoStr, str );
					}
				}
				Tr_olditemInfoType = 2;
			}
		}

		//フォント用バッファの区切り
		FontBufCut(FONT_PRIO_WIN);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
		// アイテムの表示
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			// アイテムがあるなら表示
			if( tradeItem[0][i].useFlag ){
				x = xx + 37 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = yy + 51 + ( (i/ITEM_DRAW_COLUMN) * 52 );

				// 选择不可能なアイテムに禁止マークをつける
				if( tradeItem[0][i].selFlag < 0 ){
					StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );
				}
				StockDispBuffer( x, y, DISP_PRIO_WIN2, tradeItem[0][i].graNo, 0, bm );

				// 个数表示
				if( tradeItem[0][i].num > 0 ){
					sprintf( str, "%3d", tradeItem[0][i].num );                      //MLHIDE
					StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
				}
			}
		}
	}
	else if (*Mode == 1){
		const short YPos[3] = { 26, 95, 164 };

		itemNo = -1;
		if ( flag&MENU_MOUSE_OVER ){
			for(i=0;i<3;i++){
				x = xx + 11;
				y = yy + YPos[i];
				if ( MakeHitBox( x, y, x+240, y+69, -1 ) ){ itemNo = i+ScrNum;	break; }
			}
		}

		//重なってるかチェック
		if(itemNo>=0){
			if (tradeMonster[0][itemNo].useFlag){
				strcpy( OneLineInfoStr, MWONELINE_TRADE_TRADEMONSTER );
				if (tradeMonster[0][itemNo].useFlag) Tr_itemInfoNo = itemNo+500;
			}
		}

		DrawTradeMonsterStatus( xx+11, yy+26, 0, ScrNum+0, FONT_PRIO_WIN, DISP_PRIO_WIN2, bm );
		DrawTradeMonsterStatus( xx+11, yy+95, 0, ScrNum+1, FONT_PRIO_WIN, DISP_PRIO_WIN2, bm );
		DrawTradeMonsterStatus( xx+11, yy+164, 0, ScrNum+2, FONT_PRIO_WIN, DISP_PRIO_WIN2, bm );
	}

	*Num4 = tradeOpponentGold;

	// Num1, Num2 の设定
	j=0;
	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++ ){ if( tradeItem[0][i].useFlag ) j++; }
	*Num1 = j;
	j=0;
	for( i = 0; i < MAX_PET; i++ ){ if( tradeMonster[0][i].useFlag ) j++; }
	*Num2 = j;
	// Num3 の设定
	*Num3 = tradeOpponentGold;
}

const int TradePanelGraNo[3]  ={ GID_TradeItemPanel, GID_TradeMonsterPanel, GID_TradeMoneyPanel };
BOOL MenuSwitchTradeTradePanel( int no, unsigned int flag )
{
	static char Mode;
	BOOL ReturnFlag;
	BLT_MEMBER bm={0};

	bm.rgba.rgba=( (GRAPHIC_SWITCH *)wI->sw[no].Switch )->rgba;
	bm.bltf=BLTF_NOCHG;

	if ( tradeOpponentMode == 2 ) bm.rgba.a >>= 1;

	ReturnFlag = TradePanelFunc(
					wI, no, flag, TradePanelGraNo,
					&Mode, EnumTradeTradeScrollBtn, &Tr_T_ScrNum, -1, 0,
					0, 0, pc.gold, &bm,
					TradePanelFunc
					);

	return ReturnFlag;
}















//========================================
// モンスター状态ウィンドウ
//========================================

ACTION *pActMonsSW = NULL;

extern int BarGra[4][4];
extern struct NEW_BANKMONSTER bankMonster[5];

// スキルパネルの描画
void DrawPetSkillPanel( short x, short y, int PetNo, int Num )
{
	int color;
	int no = pet[ PetNo ].sortTech[Num].index;
	BLT_MEMBER bm={0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// 文字列あるとき
	if( pet[PetNo].tech[no].name[0] != NULL ){
		//フォント用バッファの区切り
		FontBufCut(FONT_PRIO_DRAG);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		color = FONT_PAL_WHITE;
		StockFontBuffer( x+7, y+2, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, color, pet[PetNo].tech[no].name, 0, 0 );
	}
	StockDispBuffer( x+105, y+8, DISP_PRIO_DRAG, GID_TitlePanelOn, 0, &bm );
}

#define MONSSTATWIN_NAME_X 60
#define MONSSTATWIN_NAME_Y 11

#define MONSSTATWIN_GRAPOS_X 70
#define MONSSTATWIN_GRAPOS_Y 120

#define MONSSTATWIN_STATUS_X 144
#define MONSSTATWIN_STATUS_Y 23

#define MONSSTATWIN_ELMBAR_X 149
#define MONSSTATWIN_ELMBAR_Y 97

#define MONSSTATWIN_SKILL_X 10
#define MONSSTATWIN_SKILL_Y 141

void PcMonsStatusWindow( short x, short y, char Num )
{
	int i, j, k, a, b;
	char s[100];
	struct BLT_MEMBER bm={0};

	if (!pet[Num].useFlag) return;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_DRAG);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

	// 名称表示
	if (pet[Num].name){
		StockFontBuffer( x+MONSSTATWIN_NAME_X, y+MONSSTATWIN_NAME_Y, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, pet[Num].name, 0, 0 );
	}

	b = y + MONSSTATWIN_STATUS_Y;
	// ATK
	sprintf( s, "%10d", pet[Num].atk );                                  //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// DEF
	sprintf( s, "%10d", pet[Num].def );                                  //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// AGL
	sprintf( s, "%10d", pet[Num].agi );                                  //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// MND
	sprintf( s, "%10d", pet[Num].mnd );                                  //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// RCV
	sprintf( s, "%10d", pet[Num].rcv );                                  //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );

	// モンスターの絵
	if (!pActMonsSW){
		pActMonsSW = GetAction( PRIO_CHR, 0 );
	}
#ifdef _CG2_NEWGRAPHIC
	pActMonsSW->anim_chr_no = getNewGraphicNo( pet[Num].graNo );	// 新グラフィック番号代入
#else
	pActMonsSW->anim_chr_no = pet[Num].graNo;
#endif
	pActMonsSW->dispPrio = DISP_PRIO_CHAR;
	pActMonsSW->atr |= ACT_ATR_HIDE;
	pActMonsSW->func = NULL;
	pActMonsSW->anim_ang = 5;

	pattern( pActMonsSW, ANM_NOMAL_SPD, ANM_LOOP );
	StockDispBuffer2( x + MONSSTATWIN_GRAPOS_X, y + MONSSTATWIN_GRAPOS_Y, DISP_PRIO_DRAG, pActMonsSW->bmpNo, 0, &pActMonsSW->bm );

	// 属性バーの表示
	b = y+MONSSTATWIN_ELMBAR_Y;
	for(i=0;i<4;i++){
		k = pet[Num].attr[i] / 10;
		if ( k > 1 ){
			a = x+MONSSTATWIN_ELMBAR_X;

			StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][0], 0, 1, &bm );
			a += 6;
			for(j=1;j<k-1;j++){
				StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][1], 0, 1, &bm );
				a += 6;
			}
			StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][2], 0, 1, &bm );
		}else if ( k == 1 ){
			StockDispBuffer_PUK2( x+MONSSTATWIN_ELMBAR_X, b, DISP_PRIO_DRAG, BarGra[i][3], 0, 1, &bm );
		}
		b += 11;
	}

	a = x + MONSSTATWIN_SKILL_X;
	b = y + MONSSTATWIN_SKILL_Y;
	DrawPetSkillPanel( a, b, Num, 0 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 1 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 2 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 3 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 4 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 5 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 6 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 7 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 8 ),	b += 16;
	DrawPetSkillPanel( a, b, Num, 9 ),	b += 16;

	// ウィドウ表示
	StockDispBuffer_PUK2( x, y, DISP_PRIO_DRAG, GID_MonsterStatusView, 0, 1, &bm );
}

// スキルパネルの描画
void DrawBankPetSkillPanel( short x, short y, int PetNo, int Num )
{
	int color;
	int no = Num;
	BLT_MEMBER bm={0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// 文字列あるとき
	if( bankMonster[PetNo].tech[no].name[0] != NULL ){
		//フォント用バッファの区切り
		FontBufCut(FONT_PRIO_DRAG);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		color = FONT_PAL_WHITE;
		StockFontBuffer( x+7, y+2, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, color, bankMonster[PetNo].tech[no].name, 0, 0 );
	}
	StockDispBuffer( x+105, y+8, DISP_PRIO_DRAG, GID_TitlePanelOn, 0, &bm );
}

void BankMonsStatusWindow( short x, short y, char Num )
{
	int i, j, k, a, b;
	char s[100];
	struct BLT_MEMBER bm={0};

	if (!bankMonster[Num].useFlag) return;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_DRAG);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

	// 名称表示
	if (bankMonster[Num].defname){
		StockFontBuffer( x+MONSSTATWIN_NAME_X, y+MONSSTATWIN_NAME_Y, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, bankMonster[Num].defname, 0, 0 );
	}

	b = y + MONSSTATWIN_STATUS_Y;
	// ATK
	sprintf( s, "%10d", bankMonster[Num].atk );                          //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// DEF
	sprintf( s, "%10d", bankMonster[Num].def );                          //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// AGL
	sprintf( s, "%10d", bankMonster[Num].agi );                          //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// MND
	sprintf( s, "%10d", bankMonster[Num].mnd );                          //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// RCV
	sprintf( s, "%10d", bankMonster[Num].rcv );                          //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );

	// モンスターの絵
	if (!pActMonsSW){
		pActMonsSW = GetAction( PRIO_CHR, 0 );
	}
#ifdef _CG2_NEWGRAPHIC
	pActMonsSW->anim_chr_no = getNewGraphicNo( bankMonster[Num].graNo );	// 新グラフィック番号代入
#else
	pActMonsSW->anim_chr_no = bankMonster[Num].graNo;
#endif
	pActMonsSW->dispPrio = DISP_PRIO_CHAR;
	pActMonsSW->atr |= ACT_ATR_HIDE;
	pActMonsSW->func = NULL;
	pActMonsSW->anim_ang = 5;

	pattern( pActMonsSW, ANM_NOMAL_SPD, ANM_LOOP );
	StockDispBuffer2( x + MONSSTATWIN_GRAPOS_X, y + MONSSTATWIN_GRAPOS_Y, DISP_PRIO_DRAG, pActMonsSW->bmpNo, 0, &pActMonsSW->bm );

	// 属性バーの表示
	b = y+MONSSTATWIN_ELMBAR_Y;
	for(i=0;i<4;i++){
		k = bankMonster[Num].attr[i] / 10;
		if ( k > 1 ){
			a = x+MONSSTATWIN_ELMBAR_X;

			StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][0], 0, 1, &bm );
			a += 6;
			for(j=1;j<k-1;j++){
				StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][1], 0, 1, &bm );
				a += 6;
			}
			StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][2], 0, 1, &bm );
		}else if ( k == 1 ){
			StockDispBuffer_PUK2( x+MONSSTATWIN_ELMBAR_X, b, DISP_PRIO_DRAG, BarGra[i][3], 0, 1, &bm );
		}
		b += 11;
	}

	a = x + MONSSTATWIN_SKILL_X;
	b = y + MONSSTATWIN_SKILL_Y;
	DrawBankPetSkillPanel( a, b, Num, 0 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 1 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 2 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 3 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 4 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 5 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 6 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 7 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 8 ),	b += 16;
	DrawBankPetSkillPanel( a, b, Num, 9 ),	b += 16;

	// ウィドウ表示
	StockDispBuffer_PUK2( x, y, DISP_PRIO_DRAG, GID_MonsterStatusView, 0, 1, &bm );
}

// スキルパネルの描画
void DrawTradePetSkillPanel( short x, short y, char side, int PetNo, int Num )
{
	int color;
	int no = Num;
	BLT_MEMBER bm={0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// 文字列あるとき
	if( tradeMonster[side][PetNo].tech[no].name[0] != NULL ){
		//フォント用バッファの区切り
		FontBufCut(FONT_PRIO_DRAG);
		// プライオリティの制御
		StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

		// スキル名
		color = FONT_PAL_WHITE;
		StockFontBuffer( x+7, y+2, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, color, tradeMonster[side][PetNo].tech[no].name, 0, 0 );
	}
	StockDispBuffer( x+105, y+8, DISP_PRIO_DRAG, GID_TitlePanelOn, 0, &bm );
}

void TradeMonsStatusWindow( short x, short y, char side, char Num )
{
	int i, j, k, a, b;
	char s[100];
	struct BLT_MEMBER bm={0};

	if (!tradeMonster[side][Num].useFlag) return;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_DRAG);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

	// 名称表示
	if (tradeMonster[side][Num].defname){
		StockFontBuffer( x+MONSSTATWIN_NAME_X, y+MONSSTATWIN_NAME_Y, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, tradeMonster[side][Num].defname, 0, 0 );
	}

	b = y + MONSSTATWIN_STATUS_Y;
	// ATK
	sprintf( s, "%10d", tradeMonster[side][Num].atk );                   //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// DEF
	sprintf( s, "%10d", tradeMonster[side][Num].def );                   //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// AGL
	sprintf( s, "%10d", tradeMonster[side][Num].agi );                   //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// MND
	sprintf( s, "%10d", tradeMonster[side][Num].mnd );                   //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );
	b += 15;
	// RCV
	sprintf( s, "%10d", tradeMonster[side][Num].rcv );                   //MLHIDE
	StockFontBuffer( x+MONSSTATWIN_STATUS_X, b, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, s, 0, 0 );

	// モンスターの絵
	if (!pActMonsSW){
		pActMonsSW = GetAction( PRIO_CHR, 0 );
	}
#ifdef _CG2_NEWGRAPHIC
	pActMonsSW->anim_chr_no = getNewGraphicNo( tradeMonster[side][Num].graNo );	// 新グラフィック番号代入
#else
	pActMonsSW->anim_chr_no = tradeMonster[side][Num].graNo;
#endif
	pActMonsSW->dispPrio = DISP_PRIO_CHAR;
	pActMonsSW->atr |= ACT_ATR_HIDE;
	pActMonsSW->func = NULL;
	pActMonsSW->anim_ang = 5;

	pattern( pActMonsSW, ANM_NOMAL_SPD, ANM_LOOP );
	StockDispBuffer2( x + MONSSTATWIN_GRAPOS_X, y + MONSSTATWIN_GRAPOS_Y, DISP_PRIO_DRAG, pActMonsSW->bmpNo, 0, &pActMonsSW->bm );

	// 属性バーの表示
	b = y+MONSSTATWIN_ELMBAR_Y;
	for(i=0;i<4;i++){
		k = tradeMonster[side][Num].attr[i] / 10;
		if ( k > 1 ){
			a = x+MONSSTATWIN_ELMBAR_X;

			StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][0], 0, 1, &bm );
			a += 6;
			for(j=1;j<k-1;j++){
				StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][1], 0, 1, &bm );
				a += 6;
			}
			StockDispBuffer_PUK2( a, b, DISP_PRIO_DRAG, BarGra[i][2], 0, 1, &bm );
		}else if ( k == 1 ){
			StockDispBuffer_PUK2( x+MONSSTATWIN_ELMBAR_X, b, DISP_PRIO_DRAG, BarGra[i][3], 0, 1, &bm );
		}
		b += 11;
	}

	a = x + MONSSTATWIN_SKILL_X;
	b = y + MONSSTATWIN_SKILL_Y;
	DrawTradePetSkillPanel( a, b, side, Num, 0 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 1 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 2 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 3 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 4 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 5 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 6 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 7 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 8 ),	b += 16;
	DrawTradePetSkillPanel( a, b, side, Num, 9 ),	b += 16;

	// ウィドウ表示
	StockDispBuffer_PUK2( x, y, DISP_PRIO_DRAG, GID_MonsterStatusView, 0, 1, &bm );
}
