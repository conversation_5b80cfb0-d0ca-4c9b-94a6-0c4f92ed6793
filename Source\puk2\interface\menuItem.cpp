﻿//メニュー＞マップ

//======================================//
// メイン								//
//======================================//

void closeEquipPetItem();

char ItemFrameFlag = -1;
static int ItemFramePos;
static int ItemFrameAcc;
#ifdef PUK3_PET_BANK
	static int ItemPetNum;
#endif

int UsingItemNo;

#ifdef PUK3_PET_BANK
const int ItemFramePosMin[]={ -213,-213,-213,-213 };
#else
const int ItemFramePosMin[]={ -213,-265,-265,-265 };
#endif

#define ITEMWINSTARTLINE 16

void MoveEquipWindow( int dif );
void MovePetWindow1( int dif );
void MovePetWindow2( int dif );
void MovePetWindow3( int dif );

void (*ItemFrameMove[4])( int dif ) = { MoveEquipWindow, MovePetWindow1, MovePetWindow2, MovePetWindow3 };

static char ItemWindowMode;
static int UseItemNo;

static int ItemMoveStackFrom, ItemMoveStackTo;
extern BANK_NPC bank[2];

extern int serverRequestWinSeqNo;					// サーバの管理番号
extern int serverRequestWinObjIndex;				// サーバの管理番号

char ItemNoOpe[MAX_ITEM] = {0};

BOOL closeItemWindow()
{
	// 电卓ウィンドウを呼び出したのが自分の场合、电卓ウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_CALCULATOR].wininfo){
		struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
		if (wm->WinType == MENU_WINDOW_ITEM){
			wm->wininfo->flag |= WIN_INFO_DEL;
		}
	}

	if (ItemWindowMode==1||ItemWindowMode==2){
		ItemWindowMode = 0;

		WindowFlag[MENU_WINDOW_ITEM].wininfo->sw[EnumGraphItemWindow].func = MenuItemSwitchItemWindow;
		WindowFlag[MENU_WINDOW_ITEM].wininfo->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
		WindowFlag[MENU_WINDOW_ITEM].wininfo->sw[EnumGraphPetItemWindow].func = MenuItemSwitchPetItemWindow;

		ItemNoOpe[UseItemNo]--;

		UseItemNo = -1;

		// ドラッグ終了
		WinDD_DragFinish();
	}

	if (serverRequestWinWindowType==WINDOW_MESSAGETYPE_SHOVEL_SELECT){
		serverRequestWinWindowType = -1;
	}

	return TRUE;
}

void ItemMoveCalculator( int ret )
{
	struct BANKWINDOWMASTER *bwm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];
	struct GUILMONFOODWINDOWMASTER *gwm=(GUILMONFOODWINDOWMASTER *)&WindowFlag[MENU_WINDOW_GUILMONFOOD];

#ifdef PUK3_MONSTER_HELPER_CANCEL
	if ( ItemMoveStackFrom == 7 || ItemMoveStackTo == 7 ) CancelRideSkillCreate();
#endif
	if ( ItemMoveStackFrom<100 && ItemMoveStackTo<100 ){
		// アイテム移动プロトコル送信
		nrproto_MI_send( sockfd, ItemMoveStackFrom, ItemMoveStackTo, ret );
	}else if (ItemMoveStackFrom>=MAX_EQUIP_ITEM){
		if ( bwm->wininfo ){
			char str[128], msg[256];
			sprintf( str, "I|%d|%d|%d", ItemMoveStackFrom, ItemMoveStackTo, ret ); //MLHIDE
			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, bwm->SeqNo, bwm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );
		}else if ( gwm->wininfo ){
			char str[128], msg[256];
			sprintf( str, "I|%d|%d|%d", ItemMoveStackFrom, ItemMoveStackTo, ret ); //MLHIDE
			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, gwm->SeqNo, gwm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );
		}
	}
}

void ItemMove( int WinType, int ButtonNo, int from, int to )
{
	char ctrlFlag;
	int fromNum, toNum;
	int use;

	if (from<0) return;
	if (to<0) return;

	if ( from < MAX_ITEM && ItemNoOpe[from] ) return;
	if ( to < MAX_ITEM && ItemNoOpe[to] ) return;

	// 掴んだアイテム位置と违うならプロトコル送信
	if( from != to ){
		ctrlFlag = 1;
		if( (VK[VK_CONTROL] & KEY_ON) ) ctrlFlag = 0;

		if( ctrlSetting ) ctrlFlag = !ctrlFlag;

		if (from<100) fromNum = pc.item[from].num;
		else fromNum = bank[0].item[from-100].num;
		if (to<100) use = pc.item[to].useFlag;
		else use = bank[0].item[to-100].useFlag;
		// 移动先が空栏か同アイテムでスタックアイテムなら移动する数を入力
		if( from >= MAX_EQUIP_ITEM && to >= MAX_EQUIP_ITEM
			&& fromNum > 0 && use == 0 && ctrlFlag ){
			ItemMoveStackFrom = from;
			ItemMoveStackTo = to;
			if (to<100) toNum = pc.item[to].num;
			else toNum = bank[0].item[to-100].num;
			openCalculatorMenuWindow( WinType, ButtonNo, GID_StackWindow, fromNum, fromNum, 0, ItemMoveCalculator, OPENMENUWINDOW_HIT, 0 );
		}
		// それ以外はアイテムの入れ替え
		else{
			ItemMoveStackFrom = from;
			ItemMoveStackTo = to;
			ItemMoveCalculator( -1 );
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}
	}
}


void ChangeShovelItemSelectMode( char *data )
{
	UseItemNo = atoi( data );		// しゃべるの位置

	ItemNoOpe[UseItemNo]++;

	WindowFlag[MENU_WINDOW_ITEM].wininfo->sw[EnumGraphItemWindow].func = MenuItemSwitchUseItemWindow;
	WindowFlag[MENU_WINDOW_ITEM].wininfo->sw[EnumGraphEquipWindow].func = MenuItemSwitchUseEquipWindow;
	WindowFlag[MENU_WINDOW_ITEM].wininfo->sw[EnumGraphPetItemWindow].func = MenuItemSwitchUsePetItemWindow;

	ItemWindowMode = 2;

	// ドラッグ禁止
#ifdef PUK2_NEWDRAG
	WinDD_DragStart( WINDD_CANNOTDRAG, NULL, NULL );
#else
	WinDD_DragStart( WINDD_CANNOTDRAG, NULL );
#endif
	WinDD_DragerChange( MENU_WINDOW_ITEM, 0 );
}

void MoveEquipWindow( int dif )
{
	int BaseX;

	wI->sw[EnumGraphEquipWindow].ofx = ITEMWINSTARTLINE + dif;
	wI->sw[EnumGraphEquipWindow].func = MenuSwitchNone;

	BaseX = wI->sw[EnumGraphEquipWindow].ofx;

	wI->sw[EnumGraphEquipPetWindow].Enabled = TRUE;
	wI->sw[EnumGraphEquipPetWindow].ofx = BaseX;
	wI->sw[EnumGraphEquipPetWindow].sx = ITEMWINSTARTLINE - wI->sw[EnumGraphEquipPetWindow].ofx;

	wI->sw[EnumGraphEquipButton].Enabled = TRUE;	wI->sw[EnumGraphEquipButton].ofx = BaseX;
	wI->sw[EnumGraphPet1Button].Enabled = FALSE;	wI->sw[EnumGraphPet1Button].ofx = BaseX;
	wI->sw[EnumGraphPet2Button].Enabled = FALSE;	wI->sw[EnumGraphPet2Button].ofx = BaseX;
	wI->sw[EnumGraphPet3Button].Enabled = FALSE;	wI->sw[EnumGraphPet3Button].ofx = BaseX;

	wI->sw[EnumGraphEquipButton2].Enabled = FALSE;	wI->sw[EnumGraphEquipButton2].ofx = BaseX;
	wI->sw[EnumGraphPet1Button2].Enabled = TRUE;	wI->sw[EnumGraphPet1Button2].ofx = BaseX;
	wI->sw[EnumGraphPet2Button2].Enabled = TRUE;	wI->sw[EnumGraphPet2Button2].ofx = BaseX;
	wI->sw[EnumGraphPet3Button2].Enabled = TRUE;	wI->sw[EnumGraphPet3Button2].ofx = BaseX;

	wI->sw[EnumGraphItemPcAction].Enabled = FALSE;
#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphItemPetAction].Enabled = FALSE;

	wI->sw[EnumTextItemPetName].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItem1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPrice1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = FALSE;
#endif

#ifdef PUK3_PET_BANK
	if (dif<=ItemFramePosMin[0]){
#else
	if (dif<=-213){
#endif
		wI->sw[EnumGraphItemPcAction].Enabled = TRUE;
		wI->sw[EnumGraphItemPcAction].ofx = BaseX + 116;

		if (ItemWindowMode == 0){
			wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
		}else{
			wI->sw[EnumGraphItemWindow].func = MenuItemSwitchUseItemWindow;
			wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchUseEquipWindow;
			wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchUsePetItemWindow;
		}
	}
}

void MovePetWindow( int dif, int no )
{
	int BaseX;

#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphPetItemWindow].ofx = ITEMWINSTARTLINE + 15 + dif;
#else
	wI->sw[EnumGraphPetItemWindow].ofx = ITEMWINSTARTLINE + dif;
#endif
	wI->sw[EnumGraphPetItemWindow].func = MenuSwitchNone;

#ifdef PUK3_PET_BANK
	BaseX = wI->sw[EnumGraphPetItemWindow].ofx - 15;
#else
	BaseX = wI->sw[EnumGraphPetItemWindow].ofx;
#endif

	wI->sw[EnumGraphEquipPetWindow].Enabled = TRUE;
	wI->sw[EnumGraphEquipPetWindow].ofx = BaseX;
	wI->sw[EnumGraphEquipPetWindow].sx = ITEMWINSTARTLINE - wI->sw[EnumGraphEquipPetWindow].ofx;

	wI->sw[EnumGraphItemPcAction].Enabled = FALSE;
#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphItemPetAction].Enabled = FALSE;

	wI->sw[EnumTextItemPetName].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItem1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPrice1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = FALSE;
#endif

	wI->sw[EnumGraphEquipButton].Enabled = FALSE;	wI->sw[EnumGraphEquipButton].ofx = BaseX;
	wI->sw[EnumGraphPet1Button].Enabled = FALSE;	wI->sw[EnumGraphPet1Button].ofx = BaseX;
	wI->sw[EnumGraphPet2Button].Enabled = FALSE;	wI->sw[EnumGraphPet2Button].ofx = BaseX;
	wI->sw[EnumGraphPet3Button].Enabled = FALSE;	wI->sw[EnumGraphPet3Button].ofx = BaseX;

	wI->sw[EnumGraphEquipButton2].Enabled = TRUE;	wI->sw[EnumGraphEquipButton2].ofx = BaseX;
	wI->sw[EnumGraphPet1Button2].Enabled = TRUE;	wI->sw[EnumGraphPet1Button2].ofx = BaseX;
	wI->sw[EnumGraphPet2Button2].Enabled = TRUE;	wI->sw[EnumGraphPet2Button2].ofx = BaseX;
	wI->sw[EnumGraphPet3Button2].Enabled = TRUE;	wI->sw[EnumGraphPet3Button2].ofx = BaseX;

	wI->sw[EnumGraphPet1Button+no].Enabled = TRUE;
	wI->sw[EnumGraphPet1Button2+no].Enabled = FALSE;

#ifdef PUK3_PET_BANK
	if (dif<=ItemFramePosMin[no + 1]){
		int i, j;

		wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchPetItemWindow;

		wI->sw[EnumGraphItemPetAction].Enabled = TRUE;
		wI->sw[EnumGraphItemPetAction].ofx = BaseX + 161;

		wI->sw[EnumTextItemPetName].Enabled = TRUE;

		wI->sw[EnumGraphItemPetItem1].Enabled = TRUE;
		wI->sw[EnumGraphItemPetItem1].ofx = BaseX + 37;
		wI->sw[EnumGraphItemPetItemPricePanel1].ofx = BaseX + 21;
		wI->sw[EnumGraphItemPetItemPrice1].ofx = BaseX + 82;
		wI->sw[EnumGraphItemPetItem2].Enabled = TRUE;
		wI->sw[EnumGraphItemPetItem2].ofx = BaseX + 37;
		wI->sw[EnumGraphItemPetItemPricePanel2].ofx = BaseX + 21;
		wI->sw[EnumGraphItemPetItemPrice2].ofx = BaseX + 82;
		wI->sw[EnumGraphItemPetItem3].Enabled = TRUE;
		wI->sw[EnumGraphItemPetItem3].ofx = BaseX + 37;
		wI->sw[EnumGraphItemPetItemPricePanel3].ofx = BaseX + 21;
		wI->sw[EnumGraphItemPetItemPrice3].ofx = BaseX + 82;

		j = no;
		for(i=0;i<MAX_PET;i++){
			if ( !sortPet[i].useFlag ) continue;
			if ( pet[ sortPet[i].index ].battleSetting == PET_SETTING_STADBY ){
				j--;
				if ( j < 0 ) break;
			}
		}
		if ( !(i < MAX_PET) ) i = -1;
		ItemPetNum = i;
	}
#else
	if (dif<=-281) wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchPetItemWindow;
#endif
}

void MovePetWindow1( int dif )
{
	MovePetWindow( dif, 0 );
}

void MovePetWindow2( int dif )
{
	MovePetWindow( dif, 1 );
}

void MovePetWindow3( int dif )
{
	MovePetWindow( dif, 2 );
}

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowItemBf( int mouse )
{
	struct ITEMWINDOWMASTER *wm = (struct ITEMWINDOWMASTER *)&WindowFlag[MENU_WINDOW_ITEM];

	if ( mouse == WIN_INIT ){
		wm->itemInfoPage = 0;

		wI->sw[EnumGraphEquipWindow].Enabled = FALSE;
		wI->sw[EnumGraphPetItemWindow].Enabled = FALSE;
		wI->sw[EnumGraphEquipPetWindow].Enabled = FALSE;
		closeEquipPetItem();

		// モードチェンジ
		ItemWindowMode = 0;

		wI->sw[EnumGraphItemWindow].func = MenuItemSwitchItemWindow;
		wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
		wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchPetItemWindow;

		UseItemNo = -1;
#ifdef PUK3_PET_BANK
#else
		if ( ItemFrameFlag > 0 ) ItemFrameFlag = -1;
#endif

		if (ItemFrameFlag<0){
			ItemFramePos = 0;
		}else{
			ItemFramePos = ItemFramePosMin[ItemFrameFlag];
			switch(ItemFrameFlag){
			case 0:
				wI->sw[EnumGraphEquipWindow].Enabled = TRUE;
				break;
			case 1:case 2:case 3:
				wI->sw[EnumGraphPetItemWindow].Enabled = TRUE;
				break;
			}
			ItemFrameMove[ItemFrameFlag]( ItemFramePos );
		}
	}

	// 判子のとき
	if (ItemWindowMode == 1){
		// ハンコアイテムなら
		if( pc.item[UseItemNo].graNo != 26569 ){
			ItemWindowMode = 0;
	
			wI->sw[EnumGraphItemWindow].func = MenuItemSwitchItemWindow;
			wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
			wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchPetItemWindow;
	
			ItemNoOpe[UseItemNo]--;
	
			UseItemNo = -1;
	
			if (serverRequestWinWindowType==WINDOW_MESSAGETYPE_SHOVEL_SELECT){
				serverRequestWinWindowType = -1;
			}
			// ドラッグ終了
			WinDD_DragFinish();
		}
	}
	// シャベルのとき
	if (ItemWindowMode == 2){
		if (serverRequestWinWindowType!=WINDOW_MESSAGETYPE_SHOVEL_SELECT){
			wI->sw[EnumGraphItemWindow].func = MenuItemSwitchItemWindow;
			wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
			wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchPetItemWindow;

			ItemNoOpe[UseItemNo]--;

			UseItemNo = -1;

			ItemWindowMode = 0;

			// ドラッグ終了
			WinDD_DragFinish();
		}
	}

	wm->itemInfoNo = -1;

	// 电卓ウィンドウを呼び出したのが自分の场合最大值をＰＣの所持金额に合わせる
	if (WindowFlag[MENU_WINDOW_CALCULATOR].wininfo){
		struct CALCULATORWINDOWMASTER *cwm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
		if ( cwm->WinType == MENU_WINDOW_ITEM ){
			if ( cwm->ButtonNo == EnumGraphPutMoney ){
				cwm->Max = pc.gold;
				cwm->All = pc.gold;
			}
		}
	}
#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = FALSE;
	// ペットアイテム栏が表示されているなら
	if ( wI->sw[EnumGraphItemPetItem1].Enabled ){
		// 连れ歩きスキル持ってるかチェック
		if ( checkPetWalk(sortPet[ItemPetNum].index)==1 ){
			if (pc.item[0].useFlag) wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = TRUE;
			if (pc.item[1].useFlag) wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = TRUE;
			if (pc.item[2].useFlag) wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = TRUE;
		}
	}
#endif

	return TRUE;
}

BOOL MenuWindowItemAf( int Mouse )
{
	struct ITEMWINDOWMASTER *wm = (struct ITEMWINDOWMASTER *)&WindowFlag[MENU_WINDOW_ITEM];
	int x = wI->wx + 45;
	int y = wI->wy + 286;

	if ( ItemFrameFlag >= 0 ){
		if ( ItemFramePos > ItemFramePosMin[ItemFrameFlag] ){
			ItemFramePos -= ItemFrameAcc;
			ItemFrameAcc--;
			if (ItemFramePos<ItemFramePosMin[ItemFrameFlag]) ItemFramePos = ItemFramePosMin[ItemFrameFlag];
			ItemFrameMove[ItemFrameFlag]( ItemFramePos );
		}
	}

	// 通常时
	if ( ItemWindowMode == 0 ){
		// アイテム信息
		if (wm->itemInfoNo>=0) PcItemExplanationWindow( wm->itemInfoNo, wm->itemInfoPage );
	}
	// はんこを押すアイテムの选择时
	else{
		switch(ItemWindowMode){
		case 1:
			ItemExplanationWindow(
				mouse.nowPoint.x + ( mouse.nowPoint.x<320 ? 60 : -60 - 197 ),
				mouse.nowPoint.y + ( mouse.nowPoint.y<240 ? 60 : -60 - 114 ),
				"", 0,
				"　用左键点击想要密封的物品\0 　 "                                              //MLHIDE
				"　来进行密封\0"                                                        //MLHIDE
				"　\0 　　                 "                                         //MLHIDE
				"　用右键取消\0 　 ",                                                    //MLHIDE
				31, 4, NULL, 0 );
			break;
		case 2:
			ItemExplanationWindow(
				mouse.nowPoint.x + ( mouse.nowPoint.x<320 ? 60 : -60 - 197 ),
				mouse.nowPoint.y + ( mouse.nowPoint.y<240 ? 60 : -60 - 114 ),
				"", 0,
				"　用左键点击想要埋藏的物品\0 　　　　 "                                           //MLHIDE
				"　来进行埋藏\0"                                                        //MLHIDE
				"　\0                  "                                           //MLHIDE
				"　用右键取消\0 　 ",                                                    //MLHIDE
				31, 4, NULL, 0 );
			break;
		}
	}

	( (ACTION_SWITCH *)wI->sw[EnumGraphItemPcAction].Switch )->ActionAdd->anim_chr_no = getNewGraphicNo(pc.graNo);
	( (ACTION_SWITCH *)wI->sw[EnumGraphItemPcAction].Switch )->ActionAdd->anim_ang = 5;

	if ( SPR_rt00_ax <= pc.graNo && pc.graNo <= SPR_rk00_sp ){
		( (ACTION_SWITCH *)wI->sw[EnumGraphItemPcAction].Switch )->ActionAdd->bm.bltf |= BLTF_NOCHG;
	}
#ifdef PUK3_PET_BANK
	// ペットの取得
	if ( ItemPetNum >= 0 ){
		( (ACTION_SWITCH *)wI->sw[EnumGraphItemPetAction].Switch )->ActionAdd->anim_chr_no = pet[ sortPet[ItemPetNum].index ].graNo;
		( (ACTION_SWITCH *)wI->sw[EnumGraphItemPetAction].Switch )->ActionAdd->anim_ang = 5;

		if ( pet[ sortPet[ItemPetNum].index ].freeName[0] == '\0' ){
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTextItemPetName].Switch )->text, pet[ sortPet[ItemPetNum].index ].name );
		}else{
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTextItemPetName].Switch )->text, pet[ sortPet[ItemPetNum].index ].freeName );
		}
		wI->sw[EnumTextItemPetName].ofx = wI->sw[EnumGraphItemPetAction].ofx -
			 GetStrWidth( ( (TEXT_SWITCH *)wI->sw[EnumTextItemPetName].Switch )->text,
				  ( (TEXT_SWITCH *)wI->sw[EnumTextItemPetName].Switch )->FontSize ) / 2;
	}
#endif

	displayMenuWindow();

	return TRUE;
}

void closeEquipPetItem()
{
	wI->sw[EnumGraphItemPcAction].Enabled = FALSE;
#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphItemPetAction].Enabled = FALSE;

	wI->sw[EnumTextItemPetName].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItem1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPrice1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = FALSE;
#endif

	wI->sw[EnumGraphEquipButton].Enabled = FALSE;	wI->sw[EnumGraphEquipButton].ofx = 2;
	wI->sw[EnumGraphPet1Button].Enabled = FALSE;	wI->sw[EnumGraphPet1Button].ofx = 2;
	wI->sw[EnumGraphPet2Button].Enabled = FALSE;	wI->sw[EnumGraphPet2Button].ofx = 2;
	wI->sw[EnumGraphPet3Button].Enabled = FALSE;	wI->sw[EnumGraphPet3Button].ofx = 2;

	wI->sw[EnumGraphEquipButton2].Enabled = TRUE;	wI->sw[EnumGraphEquipButton2].ofx = 2;
	wI->sw[EnumGraphPet1Button2].Enabled = TRUE;	wI->sw[EnumGraphPet1Button2].ofx = 2;
	wI->sw[EnumGraphPet2Button2].Enabled = TRUE;	wI->sw[EnumGraphPet2Button2].ofx = 2;
	wI->sw[EnumGraphPet3Button2].Enabled = TRUE;	wI->sw[EnumGraphPet3Button2].ofx = 2;
}

void openEquipWindow()
{
	wI->sw[EnumGraphItemPcAction].Enabled = TRUE;
#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphItemPetAction].Enabled = FALSE;

	wI->sw[EnumTextItemPetName].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItem1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPrice1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = FALSE;
#endif

	wI->sw[EnumGraphEquipButton].Enabled = TRUE;	wI->sw[EnumGraphEquipButton].ofx = wI->sw[EnumGraphEquipWindow].ofx;
	wI->sw[EnumGraphPet1Button].Enabled = FALSE;	wI->sw[EnumGraphPet1Button].ofx = wI->sw[EnumGraphEquipWindow].ofx;
	wI->sw[EnumGraphPet2Button].Enabled = FALSE;	wI->sw[EnumGraphPet2Button].ofx = wI->sw[EnumGraphEquipWindow].ofx;
	wI->sw[EnumGraphPet3Button].Enabled = FALSE;	wI->sw[EnumGraphPet3Button].ofx = wI->sw[EnumGraphEquipWindow].ofx;

	wI->sw[EnumGraphEquipButton2].Enabled = FALSE;	wI->sw[EnumGraphEquipButton2].ofx = wI->sw[EnumGraphEquipWindow].ofx;
	wI->sw[EnumGraphPet1Button2].Enabled = TRUE;	wI->sw[EnumGraphPet1Button2].ofx = wI->sw[EnumGraphEquipWindow].ofx;
	wI->sw[EnumGraphPet2Button2].Enabled = TRUE;	wI->sw[EnumGraphPet2Button2].ofx = wI->sw[EnumGraphEquipWindow].ofx;
	wI->sw[EnumGraphPet3Button2].Enabled = TRUE;	wI->sw[EnumGraphPet3Button2].ofx = wI->sw[EnumGraphEquipWindow].ofx;
}

void openPetItemWindow( int no )
{
	wI->sw[EnumGraphItemPcAction].Enabled = FALSE;
#ifdef PUK3_PET_BANK
	wI->sw[EnumGraphItemPetAction].Enabled = FALSE;

	wI->sw[EnumTextItemPetName].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItem1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItem3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPrice1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPrice3].Enabled = FALSE;

	wI->sw[EnumGraphItemPetItemPricePanel1].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel2].Enabled = FALSE;
	wI->sw[EnumGraphItemPetItemPricePanel3].Enabled = FALSE;
#endif

	wI->sw[EnumGraphEquipButton].Enabled = FALSE;	wI->sw[EnumGraphEquipButton].ofx = wI->sw[EnumGraphPetItemWindow].ofx;
	wI->sw[EnumGraphPet1Button].Enabled = FALSE;	wI->sw[EnumGraphPet1Button].ofx = wI->sw[EnumGraphPetItemWindow].ofx;
	wI->sw[EnumGraphPet2Button].Enabled = FALSE;	wI->sw[EnumGraphPet2Button].ofx = wI->sw[EnumGraphPetItemWindow].ofx;
	wI->sw[EnumGraphPet3Button].Enabled = FALSE;	wI->sw[EnumGraphPet3Button].ofx = wI->sw[EnumGraphPetItemWindow].ofx;

	wI->sw[EnumGraphEquipButton2].Enabled = TRUE;	wI->sw[EnumGraphEquipButton2].ofx = wI->sw[EnumGraphPetItemWindow].ofx;
	wI->sw[EnumGraphPet1Button2].Enabled = TRUE;	wI->sw[EnumGraphPet1Button2].ofx = wI->sw[EnumGraphPetItemWindow].ofx;
	wI->sw[EnumGraphPet2Button2].Enabled = TRUE;	wI->sw[EnumGraphPet2Button2].ofx = wI->sw[EnumGraphPetItemWindow].ofx;
	wI->sw[EnumGraphPet3Button2].Enabled = TRUE;	wI->sw[EnumGraphPet3Button2].ofx = wI->sw[EnumGraphPetItemWindow].ofx;

	wI->sw[EnumGraphPet1Button+no].Enabled = TRUE;
	wI->sw[EnumGraphPet1Button2+no].Enabled = FALSE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------
BOOL MenuItemSwitchClose( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってるかチェック
	Graph->graNo=GID_WindowCloseOn;
	if(flag & MENU_MOUSE_OVER){
		//重なってる
		Graph->graNo=GID_WindowCloseOver;
		strcpy( OneLineInfoStr, MWONELINE_COMMON_WINDOWCLOSE );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		//ウインドウ作成されてるので画像をＯＮに
		Graph->graNo=GID_WindowCloseOff;

		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}

void ItemMoneyCalcurator( int ret )
{
#ifdef PUK3_VEHICLE
	// 乘り物移动中でないなら
	// もしくは见えないときでないなら
	if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
		 !nowVehicleProc() ){
		// お金置くプロトコル送信
		nrproto_DG_send( sockfd, mapGx, mapGy, ret );
	}
#else
	// お金置くプロトコル送信
	nrproto_DG_send( sockfd, mapGx, mapGy, ret );
#endif
}

BOOL MenuItemSwitchPutMoney( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_ITEM_MYITEM );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (wm->wininfo && wm->WinType == MENU_WINDOW_ITEM && wm->ButtonNo == no){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else{
			openCalculatorMenuWindow( MENU_WINDOW_ITEM, no, GID_MoneyWindow, pc.gold, pc.gold, 0, ItemMoneyCalcurator, OPENMENUWINDOW_HIT, 0 );
		}
		ReturnFlag = TRUE;
	}

	if (wm->wininfo){if (wm->WinType == MENU_WINDOW_ITEM){if (wm->ButtonNo == no){
		wm->Max = pc.gold;
		wm->All = pc.gold;
	}}}

	Graph->graNo = GID_ItemPutMoneyOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ItemPutMoneyOver;
	if (wm->wininfo){if (wm->WinType == MENU_WINDOW_ITEM){if (wm->ButtonNo == no){
		Graph->graNo = GID_ItemPutMoneyOff;
	}}}

	return ReturnFlag;
}

BOOL MenuItemSwitchGold( int no, unsigned int flag )
{
	sprintf( ItemNum_PCGold, "%10d", pc.gold );                          //MLHIDE
	return FALSE;
}
#ifdef PUK3_PET_BANK
void PetItemPriceCalcurator( int ret )
{
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];

	sprintf( ItemNum_PetItemPrice[wm->ButtonNo-EnumGraphItemPetItemPricePanel1],
		 "%d", ret );                                                       //MLHIDE
}

BOOL MenuItemSwitchPetItemPrice( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
	int Num = no - EnumGraphItemPetItemPricePanel1;

	//重なってるかチェック
	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_ITEM_MYITEM );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (wm->wininfo && wm->WinType == MENU_WINDOW_ITEM && wm->ButtonNo == no){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}else{
			openCalculatorMenuWindow( MENU_WINDOW_ITEM, no, GID_MoneyWindow, 1000000, 1000000, 0, PetItemPriceCalcurator, OPENMENUWINDOW_HIT, 0, CALCULATOR_OPT_ZERO_OK );
		}
		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_PetItemNoSellOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_PetItemNoSellOver;
	wI->sw[EnumGraphItemPetItemPrice1+Num].Enabled = FALSE;

	if ( atoi(ItemNum_PetItemPrice[Num]) > 0 ){
		Graph->graNo = GID_PetItemGoldPanel;
		wI->sw[EnumGraphItemPetItemPrice1+Num].Enabled = TRUE;
	}

	return ReturnFlag;
}
#endif

BOOL MenuItemSwitchEquipButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 重なってるなら一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if ( no < EnumGraphEquipWindow ) strcpy( OneLineInfoStr, MWONELINE_ITEM_EQUIP_ON );
		else strcpy( OneLineInfoStr, MWONELINE_ITEM_EQUIP_OFF );
	}

	if ( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		wI->sw[EnumGraphPetItemWindow].Enabled = FALSE;
		wI->sw[EnumGraphEquipWindow].Enabled = !wI->sw[EnumGraphEquipWindow].Enabled;

		wI->sw[EnumGraphEquipPetWindow].Enabled = FALSE;

		ItemFrameFlag = -1;
		ItemFramePos = 0;
		closeEquipPetItem();
		wI->sw[EnumGraphEquipWindow].func = MenuSwitchNone;

		if (wI->sw[EnumGraphEquipWindow].Enabled){
			ItemFrameFlag = 0;
			ItemFrameAcc = 21;
		}
	}

	Graph->graNo = GID_EquipButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_EquipButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_EquipButtonOff;

	return ReturnFlag;
}

BOOL MenuItemSwitchPetItemButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Num;
#ifdef PUK3_PET_BANK
	int i, j;
#endif

	Num = no - EnumGraphPet1Button;
	if ( no > EnumGraphEquipWindow ) Num = no - EnumGraphPet1Button2;

#ifdef PUK3_PET_BANK
	j = Num;
	for(i=0;i<MAX_PET;i++){
		if ( !sortPet[i].useFlag ) continue;
		if ( pet[ sortPet[i].index ].battleSetting == PET_SETTING_STADBY ){
			j--;
			if ( j < 0 ) break;
		}
	}
	if ( !(i < MAX_PET) ) Num = -1;
#endif

	// 重なってるなら一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
#ifdef PUK3_PET_BANK
		if ( no < EnumGraphEquipWindow ) strcpy( OneLineInfoStr, "宠物携带的物品使用完毕。" ); //MLHIDE
		else strcpy( OneLineInfoStr, "使用宠物携带的物品。" );                        //MLHIDE
#endif
	}

#ifdef PUK3_PET_BANK
	if ( Num >= 0 ){
#else
	if (Graph->graNo != GID_ItemPetButtonNull){
#endif
		if ( flag & MENU_MOUSE_LEFT ){
			// プライオリティを最上位に
			wI->flag |= WIN_INFO_PRIO;

			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			wI->sw[EnumGraphEquipWindow].Enabled = FALSE;
			if (!wI->sw[EnumGraphPetItemWindow].Enabled) wI->sw[EnumGraphPetItemWindow].Enabled = TRUE;
			else{
				if (wI->sw[EnumGraphPet1Button+Num].Enabled) wI->sw[EnumGraphPetItemWindow].Enabled = FALSE;
			}

			wI->sw[EnumGraphEquipPetWindow].Enabled = FALSE;

			ItemFrameFlag = -1;
			ItemFramePos = 0;
			closeEquipPetItem();
			wI->sw[EnumGraphPetItemWindow].func = MenuSwitchNone;

			if (wI->sw[EnumGraphPetItemWindow].Enabled){
				ItemFrameFlag = Num + 1;
#ifdef PUK3_PET_BANK
				ItemFrameAcc = 21;
#else
				ItemFrameAcc = 23;
#endif
			}

			ReturnFlag = TRUE;
		}
	}else{
		if (no<EnumGraphEquipWindow){
			if (wI->sw[no].Enabled) closeEquipPetItem();
		}
		if ( flag & MENU_MOUSE_LEFT ){
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );

			ReturnFlag = TRUE;
		}
	}

	switch(Num){
	case 0:
		Graph->graNo = GID_ItemPetButton1On;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ItemPetButton1Over;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_ItemPetButton1Off;
		break;
	case 1:
		Graph->graNo = GID_ItemPetButton2On;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ItemPetButton2Over;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_ItemPetButton2Off;
		break;
	case 2:
		Graph->graNo = GID_ItemPetButton3On;
		if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ItemPetButton3Over;
		if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_ItemPetButton3Off;
		break;
#ifdef PUK3_PET_BANK
	default:
		Graph->graNo = GID_ItemPetButtonNull;
		break;
	}
#else
	}
	Graph->graNo = GID_ItemPetButtonNull;
#endif

	return ReturnFlag;
}

BOOL MenuItemSwitchItemWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct ITEMWINDOWMASTER *wm = (struct ITEMWINDOWMASTER *)&WindowFlag[MENU_WINDOW_ITEM];
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[256];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( ItemNoOpe[i+MAX_EQUIP_ITEM] ) continue;
			x = wI->wx + wI->sw[no].ofx + 15 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 48 + ( (i/ITEM_DRAW_COLUMN) * 50 );
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo+MAX_EQUIP_ITEM ) ItemMove( MENU_WINDOW_ITEM, no, DropitemNo, itemNo+MAX_EQUIP_ITEM );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( ItemNoOpe[i+MAX_EQUIP_ITEM] ) continue;
			x = wI->wx + wI->sw[no].ofx + 15 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 48 + ( (i/ITEM_DRAW_COLUMN) * 50 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				itemNo = -1;
			}
		}
	}
#else
	if ( ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ){
			// 选择不可能なアイテムとの入れ替えは禁止
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].flag & (ITEM_ETC_FLAG_DROP_ERASE | ITEM_ETC_FLAG_LOGOUT_DROP) ){
				itemNo = -1;
			}
		}
	}
#endif

	// プライオリティを最上位に
	if ( flag & MENU_MOUSE_LEFT ) wI->flag |= WIN_INFO_PRIO;
	
	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) wm->itemInfoPage = 0;
	olditemNo = itemNo;

	// アイテムを使用していないときアイテム栏を左ダブルクリックしたとき
	if ( (UsingItemNo<0) && ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分以外のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_ITEM || WinDD_WinType()==MENU_WINDOW_NONE ){
			// フィールドで使用（装备）可能ならプロトコル送る
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag &&
				 (pc.item[itemNo+MAX_EQUIP_ITEM].field & ITEM_FIELD_FLAG_USEABLE) ){
				// ハンコアイテムなら
				if( pc.item[itemNo+MAX_EQUIP_ITEM].graNo == 26569 ){
					// はんこ使用
					ItemWindowMode = 1;

					wI->sw[EnumGraphItemWindow].func = MenuItemSwitchUseItemWindow;
					wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchUseEquipWindow;
					wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchUsePetItemWindow;
					play_se( SE_NO_USE_ITEM, 320, 240 );	// アイテム使用音

					UseItemNo = itemNo+MAX_EQUIP_ITEM;

					ItemNoOpe[UseItemNo]++;

					// ドラッグ終了
					WinDD_DragFinish();
					// ドラッグ禁止
#ifdef PUK2_NEWDRAG
					WinDD_DragStart( WINDD_CANNOTDRAG, NULL, NULL );
#else
					WinDD_DragStart( WINDD_CANNOTDRAG, NULL );
#endif
				}
				// 普通のアイテムなら
				else{
#ifdef PUK3_VEHICLE
					// 乘り物移动中でないなら
					// もしくは见えないときでないなら
					if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
						 !nowVehicleProc() ){
						UseItemNo = itemNo+MAX_EQUIP_ITEM;
						//アイテム使用プロトコル送信
						nrproto_ID_send( sockfd, mapGx, mapGy, itemNo+8, 0 );
						play_se( SE_NO_USE_ITEM, 320, 240 );	// アイテム使用音
					}
#else
					UseItemNo = itemNo+MAX_EQUIP_ITEM;
					//アイテム使用プロトコル送信
					nrproto_ID_send( sockfd, mapGx, mapGy, itemNo+8, 0 );
					play_se( SE_NO_USE_ITEM, 320, 240 );	// アイテム使用音
#endif
					// ドラッグ終了
					WinDD_DragFinish();
				}
			}else{
				// 使用出来ないアイテム
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				// ドラッグ終了
				WinDD_DragFinish();
			}
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragItem( itemNo+MAX_EQUIP_ITEM, TRUE );
#else
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+MAX_EQUIP_ITEM) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= pc.item[itemNo+8].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_ITEM ){
			DragitemNo = (int)WinDD_ObjData();
			// ドラッグ元にアイテムが无いならドラッグ終了
			if ( !pc.item[DragitemNo].useFlag ) WinDD_DragFinish();
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_ITEM ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
		}
#endif

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo >= MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + 15 + ( ( (DragitemNo-MAX_EQUIP_ITEM)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 48 + ( ( (DragitemNo-MAX_EQUIP_ITEM)/ITEM_DRAW_COLUMN ) * 50 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	// アイテム选择枠
	if( itemNo >= 0 ){
		x = wI->wx + wI->sw[no].ofx + 15 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 48 + ( (itemNo/ITEM_DRAW_COLUMN) * 50 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		// アイテム说明をしてもらうため登録
		if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
			wm->itemInfoNo = itemNo + MAX_EQUIP_ITEM;
			// 刻印されていたら１行インフォに表示
			if ( (strlen( pc.item[itemNo+MAX_EQUIP_ITEM].freeName ) > 0 ) &&
				 (pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_INCUSE) ){
				sprintf( str, "%s" ITEM_INCUSE_STRING, pc.item[itemNo+MAX_EQUIP_ITEM].name ); //MLHIDE
				strcpy( OneLineInfoStr, str );
			}else if( pc.item[itemNo+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_HANKO ){
				//ハンコ
				sprintf( str, "%s" ITEM_HANKO_STRING, pc.item[itemNo+MAX_EQUIP_ITEM].freeName ); //MLHIDE
				strcpy( OneLineInfoStr, str );
			}
		}
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 39 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 73 + ( (i/ITEM_DRAW_COLUMN) * 50 );

			if (ItemNoOpe[i+MAX_EQUIP_ITEM]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

			// 个数表示
			if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
				sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );             //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

const short EquipPos[MAX_EQUIP_ITEM][2]={
	{115, 30 }, { 51, 130}, { 51, 80 }, {180, 80 }, {180, 130}, { 51, 30 }, {180, 30 }, {180, 180}
};
const char *EquipOneLineInfo[MAX_EQUIP_ITEM]={
	MWONELINE_ITEM_HEAD,
	MWONELINE_ITEM_BODY,
	MWONELINE_ITEM_HAND,
	MWONELINE_ITEM_HAND,
	MWONELINE_ITEM_FOOT,
	MWONELINE_ITEM_ACCESS1,
	MWONELINE_ITEM_ACCESS2,
	MWONELINE_ITEM_CRYSTAL,
};
BOOL MenuItemSwitchEquipWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct ITEMWINDOWMASTER *wm = (struct ITEMWINDOWMASTER *)&WindowFlag[MENU_WINDOW_ITEM];
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;
	char str[256];

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}else
		// 银行からのはだめ
		if ( (int)WinDD_ObjData() >= 100 ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		itemNo = -1;
		for(i=0;i<MAX_EQUIP_ITEM;i++){
			if ( ItemNoOpe[i] ) continue;
			x = wI->wx + wI->sw[no].ofx + EquipPos[i][0] - 24;
			y = wI->wy + wI->sw[no].ofy + EquipPos[i][1] - 24;
			// 四角のあたり判定
			if( DrapPointX < x+48 && x <= DrapPointX && DrapPointY < y+48 && y <= DrapPointY ){
				itemNo = i;
				break;
			}
		}
		if ( itemNo >= 0 ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != itemNo ) ItemMove( MENU_WINDOW_ITEM, no, DropitemNo, itemNo );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_EQUIP_ITEM;i++){
			if ( ItemNoOpe[i] ) continue;
			x = wI->wx + wI->sw[no].ofx + EquipPos[i][0] - 24;
			y = wI->wy + wI->sw[no].ofy + EquipPos[i][1] - 24;
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	// 重なってるなら一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if (itemNo>=0) strcpy( OneLineInfoStr, EquipOneLineInfo[itemNo] );
	}

#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if ( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ) itemNo = -1;
	}
#else
	if ( ( flag & MENU_MOUSE_DRAGOVER ) ){
		// 银行侧のアイテムをドラッグしているとき
		if ( (int)WinDD_ObjData() >= 100 ) itemNo = -1;
	}
#endif

	// プライオリティを最上位に
	if ( flag & MENU_MOUSE_LEFT ) wI->flag |= WIN_INFO_PRIO;

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) wm->itemInfoPage = 0;
	olditemNo = itemNo;

	// アイテムを使用していないときアイテム栏を左ダブルクリックしたとき
	if ( (UsingItemNo<0) && ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分以外のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_ITEM || WinDD_WinType()==MENU_WINDOW_NONE ){
			// フィールドで使用（装备）可能ならプロトコル送る
			if( pc.item[itemNo].useFlag && (pc.item[itemNo].field & ITEM_FIELD_FLAG_USEABLE) ){
				// ハンコアイテムなら
				if( pc.item[itemNo].graNo == 26569 ){
					// はんこ使用
					ItemWindowMode = 1;

					wI->sw[EnumGraphItemWindow].func = MenuItemSwitchUseItemWindow;
					wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchUseEquipWindow;
					wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchUsePetItemWindow;
					play_se( SE_NO_USE_ITEM, 320, 240 );	// アイテム使用音

					UseItemNo = itemNo;

					ItemNoOpe[UseItemNo]++;

					// ドラッグ終了
					WinDD_DragFinish();
					// ドラッグ禁止
#ifdef PUK2_NEWDRAG
					WinDD_DragStart( WINDD_CANNOTDRAG, NULL, NULL );
#else
					WinDD_DragStart( WINDD_CANNOTDRAG, NULL );
#endif
				}
				// 普通のアイテムなら
				else{
#ifdef PUK3_VEHICLE
					// 乘り物移动中でないなら
					// もしくは见えないときでないなら
					if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
						 !nowVehicleProc() ){
						UseItemNo = itemNo;
						//アイテム使用プロトコル送信
						nrproto_ID_send( sockfd, mapGx, mapGy, itemNo, 0 );
						play_se( SE_NO_USE_ITEM, 320, 240 );	// アイテム使用音
					}
#else
					UseItemNo = itemNo;
					//アイテム使用プロトコル送信
					nrproto_ID_send( sockfd, mapGx, mapGy, itemNo, 0 );
					play_se( SE_NO_USE_ITEM, 320, 240 );	// アイテム使用音
#endif
					// ドラッグ終了
					WinDD_DragFinish();
				}
			}else{
				// 使用出来ないアイテム
				play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				// ドラッグ終了
				WinDD_DragFinish();
			}
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo].useFlag ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragItem( itemNo, TRUE );
#else
					WinDD_DragStart( WINDD_ITEM, (void *)(itemNo) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= pc.item[itemNo].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
	// ドラッグ中の处理はメインにお任せ

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

		// 掴んだアイテムの表示はメインにお任せ

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo < MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + EquipPos[DragitemNo][0] - 24;
			y = wI->wy + wI->sw[no].ofy + EquipPos[DragitemNo][1] - 24;
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( itemNo >= 0 ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx + EquipPos[i][0] - 24;
		y = wI->wy + wI->sw[no].ofy + EquipPos[i][1] - 24;
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

		// アイテム说明をしてもらうため登録
		if( pc.item[itemNo].useFlag ){
			wm->itemInfoNo = itemNo;
			// 刻印されていたら１行インフォに表示
			if( (strlen( pc.item[itemNo].freeName ) > 0 ) && (pc.item[itemNo].flag & ITEM_ETC_FLAG_INCUSE) ){
				sprintf( str, "%s" ITEM_INCUSE_STRING, pc.item[itemNo].name );    //MLHIDE
				strcpy( OneLineInfoStr, str );
			}else if( pc.item[itemNo].flag & ITEM_ETC_FLAG_HANKO ){
				//ハンコ
				sprintf( str, "%s" ITEM_HANKO_STRING, pc.item[itemNo].freeName ); //MLHIDE
				strcpy( OneLineInfoStr, str );
			}
		}
	}

	// アイテムの表示
	for(i=0;i<MAX_EQUIP_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i].useFlag ){
			x = wI->wx + wI->sw[no].ofx + EquipPos[i][0];
			y = wI->wy + wI->sw[no].ofy + EquipPos[i][1];

			//武器が有效の场合は１が立ってる
			if( !(pc.item[i].flag & ITEM_ETC_FLAG_VALIDEQUIP) ){
				StockDispBuffer( x + 105, y + 93, DISP_PRIO_WIN2, 22739, 0, &bm );
			}
			if (ItemNoOpe[i]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i].graNo, 0, &bm );
		}
	}

	return ReturnFlag;
}

BOOL MenuItemSwitchPetItemWindow( int no, unsigned int flag )
{
	return FALSE;
}

#ifdef PUK3_PET_BANK
BOOL MenuItemSwitchPetItemPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct ITEMWINDOWMASTER *wm = (struct ITEMWINDOWMASTER *)&WindowFlag[MENU_WINDOW_ITEM];
	BLT_MEMBER bm={0};
	int Num = no - EnumGraphItemPetItem1;
	int x, y;
	int DropitemNo, DragitemNo;
	int DrapPointX, DrapPointY;
	static int olditemNo = -2;
	char str[256];

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}else
		// 银行からのはだめ
		if ( (int)WinDD_ObjData() >= 100 ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
		DrapPointX = WinDD_DropX();
		DrapPointY = WinDD_DropY();

#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		if ( pc.item[Num].useFlag ){
			// 掴んだアイテム位置と违うならプロトコル送信
			if( DropitemNo != Num ) ItemMove( MENU_WINDOW_ITEM, no, DropitemNo, Num );
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}
#ifdef PUK2_NEWDRAG
#else
	// 前回の处理でドロップしたアイテムの后始末
	if ( flag & MENU_MOUSE_DROPRETURN ){
		// アイテム置くプロトコル送信
		nrproto_DI_send( sockfd, mapGx, mapGy, (int)WinDD_GetObject() );
	}
#endif

	// 重なってるなら一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if( pc.item[Num].useFlag ) strcpy( OneLineInfoStr, EquipOneLineInfo[Num] );
	}

	// プライオリティを最上位に
	if ( flag & MENU_MOUSE_LEFT ) wI->flag |= WIN_INFO_PRIO;

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != Num ) wm->itemInfoPage = 0;
	olditemNo = Num;

	// 通常时
	if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if( pc.item[Num].useFlag ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[Num].useFlag ){
					// ドラッグ开始
#ifdef PUK2_NEWDRAG
					DragItem( Num, TRUE );
#else
					WinDD_DragStart( WINDD_ITEM, (void *)(Num) );
#endif
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[Num].useFlag ){
					wm->itemInfoPage++;
					if( wm->itemInfoPage >= pc.item[Num].memoPage ) wm->itemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

		// 掴んだアイテムの表示はメインにお任せ

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo == Num ){
			x = wI->wx + wI->sw[no].ofx;
			y = wI->wy + wI->sw[no].ofy;
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// アイテム选择枠
		x = wI->wx + wI->sw[no].ofx;
		y = wI->wy + wI->sw[no].ofy;
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

	}

	// アイテムがあるなら
	if( pc.item[Num].useFlag ){
		x = wI->wx + wI->sw[no].ofx;
		y = wI->wy + wI->sw[no].ofy;

		if ( flag & MENU_MOUSE_OVER ){
			// アイテム说明をしてもらうため登録
			wm->itemInfoNo = Num;
			// 刻印されていたら１行インフォに表示
			if( (strlen( pc.item[Num].freeName ) > 0 ) && (pc.item[Num].flag & ITEM_ETC_FLAG_INCUSE) ){
				sprintf( str, "%s" ITEM_INCUSE_STRING, pc.item[Num].name );       //MLHIDE
				strcpy( OneLineInfoStr, str );
			}else if( pc.item[Num].flag & ITEM_ETC_FLAG_HANKO ){
				//ハンコ
				sprintf( str, "%s" ITEM_HANKO_STRING, pc.item[Num].freeName );    //MLHIDE
				strcpy( OneLineInfoStr, str );
			}
		}

		// アイテム表示
		StockDispBuffer( x+24, y+24, DISP_PRIO_WIN2, pc.item[Num].graNo, 0, &bm );
	}

	return ReturnFlag;
}
#endif




BOOL MenuItemSwitchUseItemWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[256];
	int i, x, y;
	int itemNo;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&MENU_MOUSE_DRAGOVER ){
		// ドラッグを自分が禁止にしたなら
		if ( WinDD_CheckObjType() == WINDD_CANNOTDRAG && WinDD_WinType()==MENU_WINDOW_ITEM ){
			flag |= MENU_MOUSE_OVER;
			if ( mouse.onceState & MOUSE_LEFT_CRICK ) flag |= MENU_MOUSE_LEFT;
		}
	}

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & MENU_MOUSE_OVER ){
		switch(ItemWindowMode){
		case 1:
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				if( !pc.item[i+MAX_EQUIP_ITEM].useFlag ) continue;
				if ( ItemNoOpe[i+MAX_EQUIP_ITEM] ) continue;
				if( pc.item[i+MAX_EQUIP_ITEM].kind != ITEM_LOTTERY		//くじはだめ
					&& !(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_INCUSE) //刻印もだめ
					&& !(pc.item[i+MAX_EQUIP_ITEM].num > 0)	//スタックもだめ
					&& (!(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_HANKO) || (pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_MYITEM) )
					){
					x = wI->wx + wI->sw[no].ofx + 15 + ( (i%ITEM_DRAW_COLUMN) * 50 );
					y = wI->wy + wI->sw[no].ofy + 48 + ( (i/ITEM_DRAW_COLUMN) * 50 );
					if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
				}
			}
			break;
		case 2:
			for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
				if( !pc.item[i+MAX_EQUIP_ITEM].useFlag ) continue;
				if ( ItemNoOpe[i+MAX_EQUIP_ITEM] ) continue;
				if( !(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_DROP_ERASE)	//落とすと消える
					&& !(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_LOGOUT_DROP)	//登出すると落とす
					){
					x = wI->wx + wI->sw[no].ofx + 15 + ( (i%ITEM_DRAW_COLUMN) * 50 );
					y = wI->wy + wI->sw[no].ofy + 48 + ( (i/ITEM_DRAW_COLUMN) * 50 );
					if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
				}
			}
			break;
		}
	}

	// プライオリティを最上位に
	if ( flag & MENU_MOUSE_LEFT ) wI->flag |= WIN_INFO_PRIO;

	// アイテム栏の上にあるとき
	if ( itemNo >= 0 ){
		// 右键したとき
		if( flag & MENU_MOUSE_LEFT ){
			// その场所にアイテムがあるなら
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
				// 判子のとき
				if (ItemWindowMode == 1){
					//サーバーへ送信(はんこ专用のプロトコルを使用）
					nrproto_IH_send( sockfd, itemNo+8, UseItemNo );
				}else
				// シャベルのとき
				if (ItemWindowMode == 2){
					//サーバーへ送信
					char msg[16];
					sprintf( str, "%d", itemNo+MAX_EQUIP_ITEM );                     //MLHIDE
					makeSendString( str, msg, sizeof( msg )-1 );
					nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg );

					ItemWindowMode = 0;

					wI->sw[EnumGraphItemWindow].func = MenuItemSwitchItemWindow;
					wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
					wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchPetItemWindow;

					ItemNoOpe[UseItemNo]--;

					UseItemNo = -1;

					if (serverRequestWinWindowType==WINDOW_MESSAGETYPE_SHOVEL_SELECT){
						serverRequestWinWindowType = -1;
					}
					// ドラッグ終了
					WinDD_DragFinish();
				}
			}
			ReturnFlag=TRUE;
		}
	}
	// 右クリックしたら中止
	if( mouse.onceState & MOUSE_RIGHT_CRICK ){
		ItemWindowMode = 0;

		wI->sw[EnumGraphItemWindow].func = MenuItemSwitchItemWindow;
		wI->sw[EnumGraphEquipWindow].func = MenuItemSwitchEquipWindow;
		wI->sw[EnumGraphPetItemWindow].func = MenuItemSwitchPetItemWindow;

		ItemNoOpe[UseItemNo]--;

		UseItemNo = -1;

		if (serverRequestWinWindowType==WINDOW_MESSAGETYPE_SHOVEL_SELECT){
			serverRequestWinWindowType = -1;
		}
		// ドラッグ終了
		WinDD_DragFinish();
	}

	// 使用アイテムの表示
	StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[UseItemNo].graNo, 0, &bm );

	// アイテム选择枠
	if( itemNo >= 0 ){
		x = wI->wx + wI->sw[no].ofx + 15 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
		y = wI->wy + wI->sw[no].ofy + 48 + ( (itemNo/ITEM_DRAW_COLUMN) * 50 );
		StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	switch(ItemWindowMode){
	case 1:
		// アイテムの表示
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			// アイテムがあるなら表示
			if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
				x = wI->wx + wI->sw[no].ofx + 39 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = wI->wy + wI->sw[no].ofy + 73 + ( (i/ITEM_DRAW_COLUMN) * 50 );

				if( pc.item[i+MAX_EQUIP_ITEM].kind != ITEM_LOTTERY		//くじはだめ
					&& !(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_INCUSE) //刻印もだめ
					&& !(pc.item[i+MAX_EQUIP_ITEM].num > 0)	//スタックもだめ
					&& (!(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_HANKO) || (pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_MYITEM) )
					);
				else StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );

				if (ItemNoOpe[i+MAX_EQUIP_ITEM]){
					StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
					StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
				}
				else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

				// 个数表示
				if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
					sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );            //MLHIDE
					StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
				}
			}
		}
		break;
	case 2:
		// アイテムの表示
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			// アイテムがあるなら表示
			if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
				x = wI->wx + wI->sw[no].ofx + 39 + ( (i%ITEM_DRAW_COLUMN) * 50 );
				y = wI->wy + wI->sw[no].ofy + 73 + ( (i/ITEM_DRAW_COLUMN) * 50 );

				if( !(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_DROP_ERASE)	//落とすと消える
					&& !(pc.item[i+MAX_EQUIP_ITEM].flag & ITEM_ETC_FLAG_LOGOUT_DROP)	//登出すると落とす
					);
				else StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );

				if (ItemNoOpe[i+MAX_EQUIP_ITEM]){
					StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
					StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
				}
				else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

				// 个数表示
				if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
					sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );            //MLHIDE
					StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
				}
			}
		}
		break;
	}

	return ReturnFlag;
}

BOOL MenuItemSwitchUseEquipWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	int i, x, y;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// プライオリティを最上位に
	if ( flag & MENU_MOUSE_LEFT ) wI->flag |= WIN_INFO_PRIO;

	// アイテムの表示
	for(i=0;i<MAX_EQUIP_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i].useFlag ){
			x = wI->wx + wI->sw[no].ofx + EquipPos[i][0];
			y = wI->wy + wI->sw[no].ofy + EquipPos[i][1];

			StockDispBuffer( x, y, DISP_PRIO_WIN2, CG_BANK_ITEM_SELECT_MASK_RED, 0 );

			if (ItemNoOpe[i]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i].graNo, 0, &bm );
		}
	}

	return ReturnFlag;
}

BOOL MenuItemSwitchUsePetItemWindow( int no, unsigned int flag )
{
	return FALSE;
}






//======================================//
// 电卓									//
//======================================//

// 假の入力栏
static INPUT_STR CalculatorInputStr;
static INIT_STR_STRUCT InitStrStructCalculator={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	&CalculatorInputStr,  0,  -100,FONT_PRIO_WIN,FONT_KIND_SIZE_11,FONT_PAL_SHADOW,"",	  1,      0,	  0,     0
};

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

// 共通ウィンドウ生成(ウィンドウがすでにある场合は、以前のものを书き换え) ++++
WINDOW_INFO *createCalculatorWindow( int WinType, int ButtonNo, int BackNo, int Max, int All, int First, void (*func)( int ret ) )
{
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];

	// ウィンドウが存在していなければ生成、死ぬ直前なら复活
	if (wm->wininfo){
		wm->wininfo->flag&=~WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
	else wm->wininfo = createMenuWindow( MENU_WINDOW_CALCULATOR );

	( (GRAPHIC_SWITCH *)wm->wininfo->sw[EnumGraphCalcuWindow].Switch )->graNo = BackNo;

	wm->Max = Max;
	wm->All = All;
	wm->returnFunc = func;

	wm->wininfo->sw[EnumGraphCalcuMain].status = First;

	wm->WinType = WinType;
	wm->ButtonNo = ButtonNo;

	return wm->wininfo;
}

BOOL MenuWindowCalculatorBf( int mouse )
{
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];

	if ( mouse == WIN_INIT ){
		wm->Max = 999999999;
		wm->All = 0;
		wm->returnFunc = NULL;

		wI->sw[EnumGraphCalcuMain].status = 0;
		MenuCalculatorGetKeyForcus( 0, MENU_MOUSE_LEFT );
	}

	return TRUE;
}

BOOL MenuWindowCalculatorAf( int mouse )
{
	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------
BOOL MenuCalculatorGetKeyForcus( int no, unsigned int flag )
{
	if ( flag & MENU_MOUSE_LEFT ){
		// 文字入力栏移动
		SetInputStr( &InitStrStructCalculator, 0, -100, 0 );

		// フォーカスを取る
		GetKeyInputFocus( &CalculatorInputStr );

		DiarogST.SwAdd = wI->sw[EnumCalcuGetKeyForcus].Switch;
		( (DIALOG_SWITCH *)wI->sw[EnumCalcuGetKeyForcus].Switch )->InpuStrAdd = &CalculatorInputStr;
	}

	return FALSE;
}

static char *calcuwinOneLineInfo[]={
	MWONELINE_CALCULATOR_NUM,
	MWONELINE_CALCULATOR_OK_ON,
	MWONELINE_CALCULATOR_OK_OFF,
	MWONELINE_CALCULATOR_BS,
	MWONELINE_CALCULATOR_ALL,
	MWONELINE_CALCULATOR_CLR,
};
BOOL MenuCalculatorMain( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
	int ret;

#ifdef PUK3_PET_BANK
	ret = CalculatorControl( wI, no, flag, wm->All, DISP_PRIO_WIN2, calcuwinOneLineInfo,
		(pNowInputStr==&CalculatorInputStr?CALCULATOR_OPT_SHORTCUT:0)|wm->option );
#else
	ret = CalculatorControl( wI, no, flag, wm->All, DISP_PRIO_WIN2, calcuwinOneLineInfo,
		(pNowInputStr==&CalculatorInputStr?CALCULATOR_OPT_SHORTCUT:0) );
#endif
	if ( wI->sw[no].status>wm->Max ) wI->sw[no].status = wm->Max;

	if ( ret&CALCULATOR_PUSH ){
		if ( wI->sw[no].status>wm->Max ) wI->sw[no].status = wm->Max;
		ReturnFlag = TRUE;
	}
	if ( ret&CALCULATOR_OK ){
		if (wm->returnFunc) wm->returnFunc( wI->sw[no].status );
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag = TRUE;
	}

	sprintf( CalculatorNum, "%9d", wI->sw[no].status );                  //MLHIDE

	return ReturnFlag;
}

BOOL MenuCalculatorBtUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[MENU_WINDOW_CALCULATOR];
	GRAPHIC_SWITCH *Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_CALCULATOR_Up );

	if ( flag & MENU_MOUSE_LEFT ){
		if (wI->sw[EnumGraphCalcuMain].status<wm->Max){
			wI->sw[EnumGraphCalcuMain].status++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuCalculatorBtDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if ( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_CALCULATOR_Down );

	if ( flag & MENU_MOUSE_LEFT ){
		if (wI->sw[EnumGraphCalcuMain].status>0){
			wI->sw[EnumGraphCalcuMain].status--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

//------------------------------------------------------------------------//
// ウィンドウオープンアニメーション					  //
//------------------------------------------------------------------------//
void openCalculatorWindowAnim( ACTION *ptAct )
{
	WIN_DISP *ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	StockBoxDispBuffer( ptWinDisp->cx - ptWinDisp->nx,
						ptWinDisp->cy - ptWinDisp->ny,
						ptWinDisp->cx + ptWinDisp->nx,
						ptWinDisp->cy + ptWinDisp->ny,
						DISP_PRIO_MENU, SYSTEM_PAL_BLACK, 0 );

	// 增分プラス
	ptWinDisp->nx += ptAct->dx;
	ptWinDisp->ny += ptAct->dy;

	// リミットチェック
	if( ptWinDisp->cnt >= WINDOW_CREATE_FRAME ){
		createCalculatorWindow( ptAct->fontX, ptAct->fontY, ptAct->bmpNo, ptAct->level, ptAct->gx, ptAct->gy, ( void (*)( int ret ) )ptAct->actNo );
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
		Lock3DChangeWindowCnt--;
#endif
		DeathAction( ptAct );
	}

	// カウンタープラス
	ptWinDisp->cnt++;
}

#ifdef PUK3_PET_BANK
ACTION *openCalculatorMenuWindow( int WinType, int ButtonNo, int BackNo, int Max, int All, int First, void (*func)( int ret ), unsigned char flg, char opentype, int option )
#else
ACTION *openCalculatorMenuWindow( int WinType, int ButtonNo, int BackNo, int Max, int All, int First, void (*func)( int ret ), unsigned char flg, char opentype )
#endif
{
	ACTION *ptAct;
	WIN_DISP *ptWinDisp;
	int x, y, w, h;
#ifdef PUK3_PET_BANK
	struct CALCULATORWINDOWMASTER *wm = (struct CALCULATORWINDOWMASTER *)&WindowFlag[ MENU_WINDOW_CALCULATOR ];

	wm->option = option;
#endif

	// ウィンドウ开く音
	play_se( SE_NO_OPEN_WINDOW, 320, 240 );

	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ), ACT_T_WIN_DISP );
#else
	ptAct = GetAction( PRIO_ETC, sizeof( WIN_DISP ) );
#endif

	// すでにある电卓ウィンドウを破弃
	if (WindowFlag[ MENU_WINDOW_CALCULATOR ].wininfo){
		WindowFlag[ MENU_WINDOW_CALCULATOR ].wininfo->flag|=WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	// アクション取得に失败したら終わる
	if( ptAct == NULL ) {
		createCalculatorWindow( WinType, ButtonNo, BackNo, Max, All, First, func );
		return NULL;
	}
#ifdef PUK2_3DDEVICECHANGESTOPWINDOW
	Lock3DChangeWindowCnt++;
#endif

	// ウィンドウの场所、サイズを取得
	x = WindowData[ MENU_WINDOW_CALCULATOR ]->wx,	y = WindowData[ MENU_WINDOW_CALCULATOR ]->wy;
	w = WindowData[ MENU_WINDOW_CALCULATOR ]->w,	h = WindowData[ MENU_WINDOW_CALCULATOR ]->h;

	// 以前开いたときの状态を、作成されたウィンドウに反映
	if( WindowFlag[ MENU_WINDOW_CALCULATOR ].flag & 0x01 ){
		x = WindowFlag[ MENU_WINDOW_CALCULATOR ].wx;
		y = WindowFlag[ MENU_WINDOW_CALCULATOR ].wy;
	}

	// 予备构造体へのポインタ
	ptWinDisp = (WIN_DISP *)ptAct->pYobi;

	// 实行关数
	ptAct->func = openCalculatorWindowAnim;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_WIN2;
	// 当たり判定する
	ptAct->atr |= ACT_ATR_HIT;
	// 表示しない
	ptAct->atr |= ACT_ATR_HIDE;

	ptAct->bmpNo = BackNo;
	ptWinDisp->hitFlag = 0;
	if( flg & OPENMENUWINDOW_HIT ) ptWinDisp->hitFlag = 1;

	// 座标を记忆
	ptAct->x = x,	ptAct->y = y;
	ptWinDisp->w = w,	ptWinDisp->h = h;

	ptAct->level = Max;

	// 关数へのポインタ记忆
	ptAct->actNo = (int)func;

	// 初期化データ
	ptAct->gx = All;
	ptAct->gy = First;
	ptAct->fontX = WinType;
	ptAct->fontY = ButtonNo;

	// カウンタ
	ptWinDisp->cnt = 0;

	// ＮＲバージョンエフェクト
	if( opentype == 0 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = 0;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;

		ptWinDisp->nx = ptWinDisp->cx - x;
	}
	// ＳＡバージョンエフェクト
	if( opentype == 1 ){
		// 中心座标计算
		ptWinDisp->cx = x + w/2;
		ptWinDisp->cy = y + h/2;

		// 增分计算
		ptAct->dx = (ptWinDisp->cx - x) / WINDOW_CREATE_FRAME;
		ptAct->dy = (ptWinDisp->cy - y) / WINDOW_CREATE_FRAME;
	}

	return ptAct;
}


//====================================//
//			アイテム使用１			  //
//====================================//
static char *Item1TargetStr[10];
static char Item1TargetColor[10];
char UsingItemCnt = 0;

extern USE_ITEM_TARGET_SEL useItemTargetSel[10];
extern int useItemTargetSelCnt;

BOOL Item1ReturnFunc( int ret, char *str )
{
	if ( ret >= TARGETSEL1_RETURN_TEXT ){
		// 最初の选择を送信
		nrproto_GPDI_send( sockfd, ret );
		useItemTargetSelNo = ret;
		return FALSE;
	}

	if ( ret == TARGETSEL1_RETURN_DEATHFUNC ){
		// 使用中のアイテム操作を可能にする
		if (UsingItemNo>=0) ItemNoOpe[UsingItemNo]--;

		UsingItemCnt--;
		if (UsingItemCnt<=0) UsingItemNo = -1;
	}

	return TRUE;
}

ACTION *openItem1Window()
{
	int i;
	const int color[] = {
		FONT_PAL_GREEN,
		FONT_PAL_AQUA,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE
	};
#ifdef PUK3_ITEM_LOCK
	ACTION *pAct;
#endif

	for(i=0;i<10;i++){
		Item1TargetStr[i] = useItemTargetSel[i].name;
		Item1TargetColor[i] = color[ useItemTargetSel[i].type ];
	}

#ifdef PUK3_ITEM_LOCK
	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;

	// 使用アイテムの记忆
	UsingItemNo = UseItemNo;

	// 使用中のアイテム操作を不可能にする
	ItemNoOpe[UsingItemNo]++;
	UsingItemCnt++;

	pAct = openTargetSel1Window( Item1TargetStr, Item1TargetColor, useItemTargetSelCnt, Item1ReturnFunc );
	if ( pAct == NULL ){
		// 目的のウィンドウが作られていないなら
		if ( WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo == NULL ||
			 ( WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo && WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag & WIN_INFO_DEL ) ){
			// 失败なのでアイテムの操作を元に戾す
			ItemNoOpe[UsingItemNo]--;
			UsingItemCnt--;

			UsingItemNo = -1;
		}
	}
	return pAct;
#else
	if (UsingItemNo<0) UsingItemNo = UseItemNo;

	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;

	// 使用中のアイテム操作を不可能にする
	ItemNoOpe[UsingItemNo]++;
	UsingItemCnt++;

	return openTargetSel1Window( Item1TargetStr, Item1TargetColor, useItemTargetSelCnt, Item1ReturnFunc );
#endif
}


//====================================//
//			アイテム使用２			  //
//====================================//
struct STRUCT_TARGETSEL2 Item2Sel2data[6];

extern USE_ITEM_TARGET_SEL2 useItemTargetSel2[6];
extern int useItemTargetSel2Cnt;

BOOL Item2ReturnFunc( int ret, char *str )
{
	if ( ret == TARGETSEL2_RETURN_BACK ){
		// 目の前のキャラの情报を要求
		nrproto_GFLI_send( sockfd );
		return FALSE;
	}else if ( ret >= TARGETSEL2_RETURN_TEXT ){
		// 技使用プロトコル送信
		nrproto_IDF_send( sockfd, mapGx, mapGy, UseItemNo, useItemTargetSel2[ret].sendNo );
	}

	if ( ret == TARGETSEL2_RETURN_DEATHFUNC ){
		// 使用中のアイテム操作を可能にする
		if (UsingItemNo>=0) ItemNoOpe[UsingItemNo]--;

		UsingItemCnt--;
		if (UsingItemCnt<=0) UsingItemNo = -1;
	}

	return TRUE;
}

ACTION *openItem2Window()
{
	int i;
	const int color[] = {
		FONT_PAL_GREEN,
		FONT_PAL_AQUA,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE,
		FONT_PAL_WHITE
	};
#ifdef PUK3_ITEM_LOCK
	ACTION *pAct;
#endif

	for(i=0;i<useItemTargetSel2Cnt;i++){
		Item2Sel2data[i].name = useItemTargetSel2[i].name;
		Item2Sel2data[i].lv = useItemTargetSel2[i].lv;
		Item2Sel2data[i].lp = useItemTargetSel2[i].lp;
		Item2Sel2data[i].maxlp = useItemTargetSel2[i].maxLp;
		Item2Sel2data[i].fp = useItemTargetSel2[i].fp;
		Item2Sel2data[i].maxfp = useItemTargetSel2[i].maxFp;
		Item2Sel2data[i].health = useItemTargetSel2[i].injuryLv;
		Item2Sel2data[i].color = color[ useItemTargetSel2[i].type ];
	}

	if (WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo) WindowFlag[MENU_WINDOW_SKILLOTHERSRESULT].wininfo->flag |= WIN_INFO_DEL;

	if (WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL1].wininfo->flag |= WIN_INFO_DEL;
	if (WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo) WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag |= WIN_INFO_DEL;

#ifdef PUK3_ITEM_LOCK
	pAct = openTargetSel2Window( Item2Sel2data, useItemTargetSel2Cnt, Item2ReturnFunc );
	if ( pAct || 
		 ( WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo && !( WindowFlag[MENU_WINDOW_TARGETSEL2].wininfo->flag & WIN_INFO_DEL ) )){
		// 使用中のアイテム操作を不可能にする
		ItemNoOpe[UsingItemNo]++;
		UsingItemCnt++;
	}
	return pAct;
#else
	// 使用中のアイテム操作を不可能にする
	ItemNoOpe[UsingItemNo]++;
	UsingItemCnt++;

	return openTargetSel2Window( Item2Sel2data, useItemTargetSel2Cnt, Item2ReturnFunc );
#endif
}


//====================================//
//		アイテム说明ウィンドウ		  //
//====================================//
short ItemExplanationWindowX, ItemExplanationWindowY;
int ItemExplanationWindowItemNo;

extern int itemInfoColor;

void ItemExplanationWindow( short x, short y, char *name, int namecolor, char *str, int linelen, int max );

void PcItemExplanationWindow( int ItemNo, int Page )
{
	char *name;
	int color;

	// 表示位置の调整
	if (ItemExplanationWindowItemNo!=ItemNo){
		ItemExplanationWindowX = mouse.nowPoint.x;
		ItemExplanationWindowY = mouse.nowPoint.y;
		if (mouse.nowPoint.x<320) ItemExplanationWindowX += 60;
		else ItemExplanationWindowX -= 60 + 197;
		if (mouse.nowPoint.y<240) ItemExplanationWindowY += 60;
		else ItemExplanationWindowY -= 60 + 114;
	}
	ItemExplanationWindowItemNo = ItemNo;

	//刻印
	if(pc.item[ItemExplanationWindowItemNo].flag & ITEM_ETC_FLAG_INCUSE){
		name = pc.item[ItemExplanationWindowItemNo].freeName;
		color = FONT_PAL_GREEN;
	}else{
		name = pc.item[ItemExplanationWindowItemNo].name;
		color = ( (pc.item[ItemExplanationWindowItemNo].flag&ITEM_ETC_FLAG_HANKO) ? FONT_PAL_AQUA : FONT_PAL_WHITE );
	}

	// ウィンドウの表示
	ItemExplanationWindow( ItemExplanationWindowX, ItemExplanationWindowY, name, color,
		&pc.item[ItemExplanationWindowItemNo].memo[Page*ITEM_MEMO_LINE][0], ITEM_MEMO_LINE_LEN+64, ITEM_MEMO_LINE, NULL, 0 );
}

void BankItemExplanationWindow( int ItemNo, int Page )
{
	char *name;
	int color;

	// 表示位置の调整
	if (ItemExplanationWindowItemNo!=ItemNo+100){
		ItemExplanationWindowX = mouse.nowPoint.x;
		ItemExplanationWindowY = mouse.nowPoint.y;
		if (mouse.nowPoint.x<320) ItemExplanationWindowX += 60;
		else ItemExplanationWindowX -= 60 + 197;
		if (mouse.nowPoint.y<240) ItemExplanationWindowY += 60;
		else ItemExplanationWindowY -= 60 + 114;
	}
	ItemExplanationWindowItemNo = ItemNo+100;

	//刻印
	if(bank[0].item[ItemExplanationWindowItemNo-100].flag & ITEM_ETC_FLAG_INCUSE){
		name = bank[0].item[ItemExplanationWindowItemNo-100].freeName;
		color = FONT_PAL_GREEN;
	}else{
		name = bank[0].item[ItemExplanationWindowItemNo-100].name;
		color = ( (bank[0].item[ItemExplanationWindowItemNo-100].flag&ITEM_ETC_FLAG_HANKO) ? FONT_PAL_AQUA : FONT_PAL_WHITE );
	}

	// ウィンドウの表示
	ItemExplanationWindow( ItemExplanationWindowX, ItemExplanationWindowY, name, color,
		&bank[0].item[ItemExplanationWindowItemNo-100].memo[Page*ITEM_MEMO_LINE][0], ITEM_MEMO_LINE_LEN+64, ITEM_MEMO_LINE, NULL, 0 );
}

extern TRADE_ITEM tradeItem[2][MAX_DRAW_WIN_ITEM];
void TradeItemExplanationWindow( char Side, int ItemNo, int Page )
{
	char *name;
	int color;

	// 表示位置の调整
	if (ItemExplanationWindowItemNo!=ItemNo+200){
		ItemExplanationWindowX = mouse.nowPoint.x;
		ItemExplanationWindowY = mouse.nowPoint.y;
		if (mouse.nowPoint.x<320) ItemExplanationWindowX += 60;
		else ItemExplanationWindowX -= 60 + 197;
		if (mouse.nowPoint.y<240) ItemExplanationWindowY += 60;
		else ItemExplanationWindowY -= 60 + 114;
	}
	ItemExplanationWindowItemNo = ItemNo+200;

	//刻印
	if(tradeItem[Side][ItemExplanationWindowItemNo-200].otherflg & ITEM_ETC_FLAG_INCUSE){
		name = tradeItem[Side][ItemExplanationWindowItemNo-200].freeName;
		color = FONT_PAL_GREEN;
	}else{
		name = tradeItem[Side][ItemExplanationWindowItemNo-200].name;
		color = ( (tradeItem[Side][ItemExplanationWindowItemNo-200].otherflg&ITEM_ETC_FLAG_HANKO) ? FONT_PAL_AQUA : FONT_PAL_WHITE );
	}

	// ウィンドウの表示
	ItemExplanationWindow( ItemExplanationWindowX, ItemExplanationWindowY, name, color,
		&tradeItem[Side][ItemExplanationWindowItemNo-200].memo[Page*ITEM_MEMO_LINE][0], ITEM_MEMO_LINE_LEN+64, ITEM_MEMO_LINE, NULL, 0 );
}

extern ITEM_SHOP_INFO itemShopList[20];
void ShopItemExplanationWindow( int ItemNo, int Page, BOOL LastOn )
{
	int color;
	char name[256];
	char *last;

	// 表示位置の调整
	if (ItemExplanationWindowItemNo!=ItemNo+300){
		ItemExplanationWindowX = mouse.nowPoint.x;
		ItemExplanationWindowY = mouse.nowPoint.y;
		if (mouse.nowPoint.x<320) ItemExplanationWindowX += 60;
		else ItemExplanationWindowX -= 60 + 197;
		if (mouse.nowPoint.y<240) ItemExplanationWindowY += 60;
		else ItemExplanationWindowY -= 60 + 114;
	}
	ItemExplanationWindowItemNo = ItemNo+300;

	//刻印
	if(itemShopList[ItemExplanationWindowItemNo-300].otherflg & ITEM_ETC_FLAG_INCUSE){
		strcpy( name, itemShopList[ItemExplanationWindowItemNo-300].freeName );
		color = FONT_PAL_GREEN;
	}else{
		strcpy( name, itemShopList[ItemExplanationWindowItemNo-300].freeName );
		if( itemShopList[ItemExplanationWindowItemNo-300].stack_num > 1){
			_snprintf( name, sizeof(name), "%s(%d)",                           //MLHIDE
				itemShopList[ItemExplanationWindowItemNo-300].name,
				itemShopList[ItemExplanationWindowItemNo-300].stack_num );
		}else{
			strcpy( name, itemShopList[ItemExplanationWindowItemNo-300].name);
		}
		color = ( (itemShopList[ItemExplanationWindowItemNo-300].otherflg&ITEM_ETC_FLAG_HANKO) ? FONT_PAL_AQUA : FONT_PAL_WHITE );
	}

	// 贩卖モードで装备品なのに装备出来ない场合は
	last = NULL;
	if( itemShopList[ItemExplanationWindowItemNo-300].buyOkFlag == 0 && LastOn ) last = ML_STRING(416, "＊现在无法装备");

	// ウィンドウの表示
	ItemExplanationWindow( ItemExplanationWindowX, ItemExplanationWindowY, name, color,
		&itemShopList[ItemExplanationWindowItemNo-300].memo[Page*ITEM_MEMO_LINE][0],
		ITEM_MEMO_LINE_LEN+64, ITEM_MEMO_LINE, last, FONT_PAL_YELLOW );
}

extern ITEMCOUNT_NPC_INFO itemCountInfo;
void CountItemExplanationWindow( int Page )
{
	// 表示位置の调整
	if (ItemExplanationWindowItemNo!=400){
		ItemExplanationWindowX = mouse.nowPoint.x;
		ItemExplanationWindowY = mouse.nowPoint.y;
		if (mouse.nowPoint.x<320) ItemExplanationWindowX += 60;
		else ItemExplanationWindowX -= 60 + 197;
		if (mouse.nowPoint.y<240) ItemExplanationWindowY += 60;
		else ItemExplanationWindowY -= 60 + 114;
	}
	ItemExplanationWindowItemNo = 400;

	// ウィンドウの表示
	ItemExplanationWindow( ItemExplanationWindowX, ItemExplanationWindowY, itemCountInfo.name, FONT_PAL_WHITE,
		&itemCountInfo.memo[Page*ITEM_MEMO_LINE][0],
		ITEM_MEMO_LINE_LEN+64, ITEM_MEMO_LINE, NULL, 0 );
}

void ItemExplanationWindow( short x, short y, char *name, int namecolor, char *str, int linelen, int max, char *laststr, int lastcolor )
{
	int i,j;
	struct BLT_MEMBER bm={0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_DRAG);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_DRAG, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );

	// 名称表示
	if (name) StockFontBuffer( x+10, y+10 ,FONT_PRIO_DRAG, FONT_KIND_SIZE_12, namecolor, name, 0, 0 );

	// 说明表示
	j = 0;
	itemInfoColor = FONT_PAL_WHITE;
	for( i = 0; i < max; i++ ){
		if( str[j] != '\0' ){
			stockFontBuffer( x+10, y+22+i*13, FONT_PRIO_DRAG, FONT_KIND_SIZE_12, FONT_PAL_WHITE, str+j, 0, 0 );
		}
		j += linelen;
	}

	// コメント
	if (laststr) StockFontBuffer( x+10, y+127 ,FONT_PRIO_DRAG, FONT_KIND_SIZE_12, lastcolor, laststr, 0, 0 );

	// ウィドウ表示
	StockDispBuffer_PUK2( x, y, DISP_PRIO_DRAG, GID_ItemExplanationWindow, 0, 1, &bm );
}









//======================================
// アイテム制御用关数

void swapTradeItemRelation( int from, int to );
void swapCreateItemRelation( int from, int to );
void swapGMStatusItemRelation( int from, int to );
void swapRebirthItemRelation( int from, int to );

// アイテムの位置が移动された时の处理
// 引	form ---- 移动元	to ---- 移动先
void swapItemRelation( int from, int to )
{
	if ( from<100 && to<100 ){
		char c;

		// 使用アイテムの移动
		if ( UsingItemNo == from ) UsingItemNo = to;
		else if ( UsingItemNo == to ) UsingItemNo = from;
		// ハンコアイテムのときのみ
		if( pc.item[from].graNo == 26569 || pc.item[to].graNo == 26569 ){
			if ( UseItemNo == from ) UseItemNo = to;
			else if ( UseItemNo == to ) UseItemNo = from;
		}

		// トレードオープン中のアイテムの移动
		swapTradeItemRelation( from, to );
		// アイテムクリエートの素材アイテムの移动
		swapCreateItemRelation( from, to );
		// 公会宠物への饵アイテムの移动
		swapGMStatusItemRelation( from, to );
		// リバース中のアイテム栏ロック处理
		swapRebirthItemRelation( from, to );

		// 操作禁止の移动
		c = ItemNoOpe[from],
		ItemNoOpe[from] = ItemNoOpe[to];
		ItemNoOpe[to] = c;
	}
}

// アイテムの所属を返す
// 引	ItemNo ---- アイテムの场所
// 戾	アイテムの所属に对应した值
/***
enum{
	ITEMFROM____,		// 不明
	ITEMFROMEQUIP,		// ＰＣの装备品
	ITEMFROMHAVE,		// ＰＣの手持ち
	ITEMFROMBANK,		// 银行
};
***/
int checkItemFrom( int ItemNo )
{
	if ( 0<=ItemNo && ItemNo<MAX_EQUIP_ITEM ) return ITEMFROMEQUIP;
	if ( MAX_EQUIP_ITEM<=ItemNo && ItemNo<MAX_EQUIP_ITEM+MAX_DRAW_WIN_ITEM ) return ITEMFROMHAVE;
	if ( 100<=ItemNo && ItemNo<100+MAX_DRAW_WIN_ITEM ) return ITEMFROMBANK;

	return ITEMFROM____;
}

// モンスターの所属を返す
// 引	ItemNo ---- モンスターの场所
// 戾	モンスターの所属に对应した值
/***
enum{
	MONSTERFROM____,		// 不明
	MONSTERFROMPC,			// ＰＣの手持ち
	MONSTERFROMBANK,		// 银行
};
***/
int checkMonsterFrom( int MonsNo )
{
	if ( 0<=MonsNo && MonsNo<MAX_PET ) return MONSTERFROMPC;
	if ( 100<=MonsNo && MonsNo<100+MAX_PET ) return MONSTERFROMBANK;

	return MONSTERFROMBANK;
}


void MonsMove( int WinType, int ButtonNo, int from, int to )
{
	if (from<0) return;
	if (to<0) return;

	// 掴んだアイテム位置と违うならプロトコル送信
	if( from != to ){
		struct BANKWINDOWMASTER *bwm = (struct BANKWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BANK];

		if ( from<100 && to<100 ){
			// ペット移动プロトコル送信
			nrproto_MP_send( sockfd, from, to );
		}else if (bwm->wininfo){
			char str[128], msg[256];
			sprintf( str, "P|%d|%d", from, to );                               //MLHIDE
			makeSendString( str, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, bwm->SeqNo, bwm->ObjIndex, WINDOW_BUTTONTYPE_NONE, msg );
		}
	}
}

int getsortMonsPos( int Num )
{
	int i;

	for(i=0;i<MAX_PET;i++){
		if ( sortPet[i].index == Num ) return i;
	}
	return -1;
}

#ifdef PUK2_NEWDRAG

BOOL PutFieldFlag;

void DragItemFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// ドラッグ元にアイテムが无いならドラッグ終了
		switch( checkItemFrom(itemNo) ){
		case ITEMFROMEQUIP:		// ＰＣの装备品
		case ITEMFROMHAVE:		// ＰＣの手持ち
			if ( !pc.item[itemNo].useFlag ) WinDD_DragFinish();
			break;
		case ITEMFROMBANK:		// 银行
			if ( !bank[0].item[itemNo-100].useFlag ) WinDD_DragFinish();
			break;
		}
	
		// 右键したらアイテムドロップ
		if ( mouse.onceState & MOUSE_LEFT_CRICK ){
			WinDD_DragFinish();
			WinDD_DropObject( WINDD_ITEM, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragItemFunc );
		}
		// 右クリックしたらドラッグ終了
		if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragCancel();
	
#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		switch( checkItemFrom(itemNo) ){
		case ITEMFROMEQUIP:		// ＰＣの装备品
		case ITEMFROMHAVE:		// ＰＣの手持ち
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[itemNo].graNo, 0, &bm );
			break;
		case ITEMFROMBANK:		// 银行
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, bank[0].item[itemNo-100].graNo, 0, &bm );
			break;
		}
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		// フィールド置きフラグがＯＮなら
		if ( PutFieldFlag ){
#ifdef PUK3_VEHICLE
			// 乘り物移动中でないなら
			// もしくは见えないときでないなら
			if ( !( pc.status2 & CHR_STATUS2_INVISIBLE ) &&
				 !nowVehicleProc() ){
				// アイテム置くプロトコル送信
				nrproto_DI_send( sockfd, mapGx, mapGy, itemNo );
			}
#else
			// アイテム置くプロトコル送信
			nrproto_DI_send( sockfd, mapGx, mapGy, itemNo );
#endif
		}
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		break;
	}
}

void DragItem( int itemNo, BOOL PutField )
{
	PutFieldFlag = PutField;
	// ドラッグ开始
	WinDD_DragStart( WINDD_ITEM, (void *)itemNo, DragItemFunc );
}


void DrawMonsterPanel( short x, short y, int no, int FontPrio, int DispPrio, unsigned long rgba );
void DrawMyMonsterStatus( short x, short y, int no, int Prio );
void DrawBankMonsterStatus( short x, short y, int no, int Prio );

static int MonsPanelType;

void DragMonsFunc( int ProcNo, unsigned int flag, void *ObjData )
{
	int itemNo = (int)ObjData;
	BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0x80ffffff;
	bm.bltf = BLTF_NOCHG;

	switch(ProcNo){
	// ドラッグ中
	case WINDDPROC_DRAG:
		// ドラッグ元にアイテムが无いならドラッグ終了
		switch( checkMonsterFrom(itemNo) ){
		case MONSTERFROMPC:		// ＰＣの手持ち
			if ( !pet[itemNo].useFlag ) WinDD_DragFinish();
			break;
		case MONSTERFROMBANK:		// 银行
			if ( !bankMonster[itemNo-100].useFlag ) WinDD_DragFinish();
			break;
		}

		if ( MonsPanelType == 0 ){
			// 右クリックしたらアイテムドロップ
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_MONSTER, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragMonsFunc );
			}
		}else{
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_MONSTER, (void *)(itemNo), mouse.nowPoint.x, mouse.nowPoint.y, DragMonsFunc );
			}
			// 右クリックしたらドラッグ終了
			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragCancel();
		}
	
#ifdef PUK3_MOUSECURSOR
		// マウスカーソルの变更
		setMouseType( MOUSE_CURSOR_TYPE_HAND );
#endif
		// 掴んだアイテムの表示
		switch(MonsPanelType){
		case 0:		// モンスターウィンドウ用
			switch( checkMonsterFrom(itemNo) ){
			case MONSTERFROMPC:		// ＰＣの手持ち
				// 掴んだモンスターの表示
				DrawMonsterPanel( mouse.nowPoint.x, mouse.nowPoint.y, itemNo, FONT_PRIO_DRAG, DISP_PRIO_DRAG, 0x80ffffff );
				break;
			}
			break;
		case 1:		// 银行用
			switch( checkMonsterFrom(itemNo) ){
			case MONSTERFROMPC:		// ＰＣの手持ち
				DrawMyMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, itemNo, FONT_PRIO_DRAG );
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm );
				break;
			case MONSTERFROMBANK:		// 银行
				DrawBankMonsterStatus( mouse.nowPoint.x-120, mouse.nowPoint.y-35, itemNo-100, FONT_PRIO_DRAG );
				StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, GID_MonsterPanel, 0, &bm );
				break;
			}
			break;
		}
		break;
	// フィールドにドロップされた
	case WINDDPROC_DROP_FIELD:
		break;
	// ドラッグがキャンセルされた
	case WINDDPROC_DRAGCANCEL:
		break;
	// 谁かに受け取られた
	case WINDDPROC_DROP_ACCEPT:
	// 谁にも受け取られなかった
	case WINDDPROC_DROP_NOACCEPT:
		break;
	}
}

void DragMons( int itemNo, int l_MonsPanelType )
{
	MonsPanelType = l_MonsPanelType;
	// ドラッグ开始
	WinDD_DragStart( WINDD_MONSTER, (void *)itemNo, DragMonsFunc );
}

#endif