﻿// サーバリクエストメイン

#ifdef PUK2_NEW_MENU

// 泛用のウィンドウ描画关数 ++++
void MenuWindowCommonDraw( int GraNo, short x, short y, short w, short h, int DispPrio, unsigned long frame_rgba, unsigned long back_rgba, char sizeX, char sizeY )
{
	BLT_MEMBER bm={0}, bm2={0};
	short lw,rw,th,bh;
	short hw=sizeX>>1, hh=sizeY>>1;

	int i,j;

	// 绘图设定初期化
	bm.rgba.rgba=frame_rgba;
	bm.BltVer=BLTVER_PUK2;
	bm.bltf=BLTF_NOCHG;
	bm.PalNo=0;

	bm2=bm;

	// 四隅配置
	lw=rw=sizeX;
	if ( w < sizeX+sizeX ){
		lw = w >> 1;
		rw = w - lw;
	}
	th=bh=sizeY;
	if ( h < sizeY+sizeY ){
		th = h >> 1;
		bh = h - th;
	}

	// 左上
	bm.u=-hw,					bm.v=-hh,					bm.w=lw,	bm.h=th;
	StockDispBuffer_PUK2( x,	  y,	  DispPrio, GraNo + 0, 0, 1, &bm);
	// 右上
	bm.u=sizeX-rw-hw,	bm.v=-hh,					bm.w=rw,	bm.h=th;
	StockDispBuffer_PUK2( x+w-rw, y,	  DispPrio, GraNo + 2, 0, 1, &bm);
	// 左下
	bm.u=-hw,					bm.v=sizeY-bh-hh,	bm.w=lw,	bm.h=bh;
	StockDispBuffer_PUK2( x,	  y+h-bh, DispPrio, GraNo + 6, 0, 1, &bm);
	// 右下
	bm.u=sizeX-rw-hw,	bm.v=sizeY-bh-hh,	bm.w=rw,	bm.h=bh;
	StockDispBuffer_PUK2( x+w-rw, y+h-bh, DispPrio, GraNo + 8, 0, 1, &bm);

	// 左??右
	bm.u=-hw,					bm.v=-hh,					bm.w=lw,	bm.h=sizeY;
	bm2.u=sizeX-rw-hw,	bm2.v=-hh,					bm2.w=rw,	bm2.h=sizeY;

	j=x+w-rw;
	for(i=th;i<h-sizeY-bh;i+=sizeY){
		StockDispBuffer_PUK2( x, y+i, DispPrio, GraNo+3, 0, 1, &bm);
		StockDispBuffer_PUK2( j, y+i, DispPrio, GraNo+5, 0, 1, &bm2);
	}
	if (i<h-bh){
		bm.h=bm2.h=(h-bh)-i;
		StockDispBuffer_PUK2( x, y+i, DispPrio, GraNo+3, 0, 1, &bm);
		StockDispBuffer_PUK2( j, y+i, DispPrio, GraNo+5, 0, 1, &bm2);
	}

	// 上??下
	bm.u=-hw,					bm.v=-hh,					bm.w=sizeX,	bm.h=th;
	bm2.u=-hw,					bm2.v=sizeY-bh-hh,	bm2.w=sizeX,	bm2.h=bh;

	j=y+h-bh;
	for(i=lw;i<w-sizeX-rw;i+=sizeX){
		StockDispBuffer_PUK2( x+i, y, DispPrio, GraNo+1, 0, 1, &bm);
		StockDispBuffer_PUK2( x+i, j, DispPrio, GraNo+7, 0, 1, &bm2);
	}
	if (i<w-rw){
		bm.w=bm2.w=(w-rw)-i;
		StockDispBuffer_PUK2( x+i, y, DispPrio, GraNo+1, 0, 1, &bm);
		StockDispBuffer_PUK2( x+i, j, DispPrio, GraNo+7, 0, 1, &bm2);
	}

	bm.rgba.rgba=bm2.rgba.rgba=back_rgba;
	// 背景
	for(j=0;j<h-sizeY;j+=sizeY){
		bm.u=-hw,					bm.v=-hh,					bm.w=sizeX,	bm.h=sizeY;
		for(i=0;i<w-sizeX;i+=sizeX){
			StockDispBuffer_PUK2( x+i, y+j, DispPrio, GraNo+4, 0, 1, &bm);
		}
		if (i<w){
			bm.w=w-i;
			StockDispBuffer_PUK2( x+i, y+j, DispPrio, GraNo+4, 0, 1, &bm);
		}
	}
	if (j<h){
		bm.u=-hw,					bm.v=-hh,					bm.w=sizeX,	bm.h=sizeY;
		bm.h=h-j;
		for(i=0;i<w-sizeX;i+=sizeX){
			StockDispBuffer_PUK2( x+i, y+j, DispPrio, GraNo+4, 0, 1, &bm);
		}
		if (i<w){
			bm.w=w-i;
			StockDispBuffer_PUK2( x+i, y+j, DispPrio, GraNo+4, 0, 1, &bm);
		}
	}
}

//====================================//
//			泛用ウィンドウ			  //
//====================================//

struct GENERAL_WINDOW{
	int w,h;
	char (*Msg)[81];
	int GraNo;
	int BottonType;
	int LineNum;
	int Interval;
	int SelCnt;
	int InputStrLen;
	short *InputStrPos;
	char InputStr;
	char Move;
	unsigned char Option;
	BOOL (*ReturnFunc)( int Button, int num, char **str, char linenum );
	BOOL (*ExtraFunc)( WINDOW_INFO *wininfo, int ButtonOver, int StrOver, int flag );
} sGrl;
BOOL GrlClose;
char **GrlInput;
int GrlNowInput;

int GrlButtonOver;
int GrlStrOver;
int GrlMouseFlag;

#define GENERAL_BUTTON_AREA_H 24
#define GENERAL_INPUTSTR_AREA_H 28
#define GENERAL_STRPOS_X 16
#define GENERAL_STRPOS_Y 16
#define GENERAL_BUTTON_W 66
#define GENERAL_BUTTON_H 17

INPUT_STR GeneralWinInputStr;
INIT_STR_STRUCT InitStrStructGeneralWin={
//  本体		         ofx,ofy,piro        ,Font               ,color         ,str     ,MaxLine ,MAXLen,dist, flag
	&GeneralWinInputStr,  0,  0,FONT_PRIO_WIN,FONT_KIND_MIDDLE,FONT_PAL_WHITE,"",	  1,      32,  0,     0
};

static struct GRL_ACTION{
	int actNo;			// アニメーションのＩＤ
	char actAng;		// アニメーションの方向
	short x,y;			// 表示座标
	char useFlag;		// 使用フラグ
} Grl_Action[GRL_ACT_NUM];
static char nowUseGrlAction = 0;

// アクションの初期化
void initGrl_Action()
{
	int i;

	for(i=0;i<GRL_ACT_NUM;i++) Grl_Action[i].actNo = 0;
	nowUseGrlAction = 0;

	// ウィンドウがすでにあるなら
	if (WindowFlag[MENU_WINDOW_GENERAL].wininfo){
		WINDOW_INFO *wininfo = WindowFlag[MENU_WINDOW_GENERAL].wininfo;

		for(i=EnumGeneralActionHead;i<EnumGeneralActionTail;i++){
			wininfo->sw[i].Enabled = FALSE;
		}
	}
}

// アクションの追加
void addGrl_Action( char useFlag, int actNo, char actAng, short x, short y )
{
	// 最大数超えるなら追加せず
	if ( nowUseGrlAction >= GRL_ACT_NUM ) return;

	Grl_Action[nowUseGrlAction].actNo = actNo;
	Grl_Action[nowUseGrlAction].actAng = actAng;
	Grl_Action[nowUseGrlAction].x = x;
	Grl_Action[nowUseGrlAction].y = y;
	Grl_Action[nowUseGrlAction].useFlag = useFlag;

	// ウィンドウがすでにあるなら
	if (WindowFlag[MENU_WINDOW_GENERAL].wininfo){
		SWITCH_INFO *sw = &WindowFlag[MENU_WINDOW_GENERAL].wininfo->sw[EnumGeneralActionHead+nowUseGrlAction];

		( (ACTION_SWITCH *)sw->Switch )->ActionAdd->anim_chr_no = actNo;
		( (ACTION_SWITCH *)sw->Switch )->ActionAdd->anim_ang = actAng;
		sw->ofx = x,	sw->ofy = y;
		sw->Enabled = FALSE;
		if (useFlag) sw->Enabled = TRUE;
	}

	// カウンタを增やす
	nowUseGrlAction++;
}

// アクションの设定变更
/***	flagのフラグ
enum{
	ADDGRLFLAG_ACTNO = 1 << 0,
	ADDGRLFLAG_ACTANG = 1 << 1,
	ADDGRLFLAG_X = 1 << 2,
	ADDGRLFLAG_Y = 1 << 3,
	ADDGRLFLAG_USE = 1 << 4,
};
***/
void chgGrl_Action( int index, char flag, char useFlag, int actNo, char actAng, short x, short y )
{
	// 最大数超えるなら追加せず
	if ( index >= nowUseGrlAction ) return;

	if ( flag & ADDGRLFLAG_ACTNO ) Grl_Action[index].actNo = actNo;
	if ( flag & ADDGRLFLAG_ACTANG ) Grl_Action[index].actAng = actAng;
	if ( flag & ADDGRLFLAG_X ) Grl_Action[index].x = x;
	if ( flag & ADDGRLFLAG_Y ) Grl_Action[index].y = y;
	if ( flag & ADDGRLFLAG_USE ) Grl_Action[index].useFlag = useFlag;

	// ウィンドウがすでにあるなら
	if (WindowFlag[MENU_WINDOW_GENERAL].wininfo){
		SWITCH_INFO *sw = &WindowFlag[MENU_WINDOW_GENERAL].wininfo->sw[EnumGeneralActionHead+index];

		( (ACTION_SWITCH *)sw->Switch )->ActionAdd->anim_chr_no = Grl_Action[index].actNo;
		( (ACTION_SWITCH *)sw->Switch )->ActionAdd->anim_ang = Grl_Action[index].actAng;
		sw->ofx = Grl_Action[index].x,	sw->ofy = Grl_Action[index].y;
		sw->Enabled = FALSE;
		if (Grl_Action[index].useFlag) sw->Enabled = TRUE;
	}
}

//--------------------------------------
// ウィンドウ关数
//--------------------------------------

int GeneralWindowStrLen( short winw )
{
	return( ( winw - (GENERAL_STRPOS_X*2) ) / 9 );
}

int GeneralWindowStrLineNum( short winh, int BottonType, int Interval )
{
	return( ( winh - (GENERAL_STRPOS_X*2) - (BottonType!=0?GENERAL_BUTTON_AREA_H:0) ) / Interval );
}

ACTION *openMenuGeneralWindow( short x, short y, short w, short h, int GraNo, int BottonType, char (*Msg)[81],
	int LineNum, int Interval, int SelCnt, char InputStr, short *InputStrPos, int InputStrLen, char Move, unsigned char Option,
	BOOL (*ReturnFunc)( int Button, int num, char **str, char linenum ),
	BOOL (*ExtraFunc)( WINDOW_INFO *wininfo, int ButtonOver, int StrOver, int flag ),
	unsigned char flg, char opentype )
{
	int MaxLine;

	// 表示座标设定
	WindowFlag[MENU_WINDOW_GENERAL].wx = x,	WindowFlag[MENU_WINDOW_GENERAL].wy = y;
	sGrl.w = w,		sGrl.h = h;

	sGrl.GraNo = GraNo;
	sGrl.BottonType = BottonType;
	sGrl.Msg = Msg;
	sGrl.LineNum = LineNum;
	sGrl.Interval = Interval;
	sGrl.SelCnt = SelCnt;
	sGrl.InputStrPos = InputStrPos;
	sGrl.InputStr = InputStr;
	sGrl.InputStrLen = InputStrLen;
	sGrl.Move = Move;
	sGrl.Option = Option;
	sGrl.ReturnFunc = ReturnFunc;
	sGrl.ExtraFunc = ExtraFunc;

	MaxLine = GeneralWindowStrLineNum( h, sGrl.BottonType, sGrl.Interval );
	if ( sGrl.LineNum > MaxLine ) sGrl.LineNum = MaxLine;

	if ( WindowFlag[MENU_WINDOW_GENERAL].wininfo ) WindowFlag[MENU_WINDOW_GENERAL].wininfo->flag |= WIN_INFO_DEL;
	if ( WindowFlag[MENU_WINDOW_SRGENERAL].wininfo ) WindowFlag[MENU_WINDOW_SRGENERAL].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindowPos( x, y, w, h, MENU_WINDOW_GENERAL, flg, opentype );
}

ACTION *openMenuSrGeneralWindow( short x, short y, short w, short h, int GraNo, int BottonType, char (*Msg)[81],
	int LineNum, int Interval, int SelCnt, char InputStr, short *InputStrPos, int InputStrLen, char Move, unsigned char Option,
	BOOL (*ReturnFunc)( int Button, int num, char **str, char linenum ),
	BOOL (*ExtraFunc)( WINDOW_INFO *wininfo, int ButtonOver, int StrOver, int flag ),
	unsigned char flg, char opentype )
{
	int MaxLine;

	// 表示座标设定
	WindowFlag[MENU_WINDOW_SRGENERAL].wx = x,	WindowFlag[MENU_WINDOW_SRGENERAL].wy = y;
	sGrl.w = w,		sGrl.h = h;

	sGrl.GraNo = GraNo;
	sGrl.BottonType = BottonType;
	sGrl.Msg = Msg;
	sGrl.LineNum = LineNum;
	sGrl.Interval = Interval;
	sGrl.SelCnt = SelCnt;
	sGrl.InputStrPos = InputStrPos;
	sGrl.InputStr = InputStr;
	sGrl.InputStrLen = InputStrLen;
	sGrl.Move = Move;
	sGrl.Option = Option;
	sGrl.ReturnFunc = ReturnFunc;
	sGrl.ExtraFunc = ExtraFunc;

	MaxLine = GeneralWindowStrLineNum( h, sGrl.BottonType, sGrl.Interval );
	if ( sGrl.LineNum > MaxLine ) sGrl.LineNum = MaxLine;

	if ( WindowFlag[MENU_WINDOW_GENERAL].wininfo ) WindowFlag[MENU_WINDOW_GENERAL].wininfo->flag |= WIN_INFO_DEL;
	if ( WindowFlag[MENU_WINDOW_SRGENERAL].wininfo ) WindowFlag[MENU_WINDOW_SRGENERAL].wininfo->flag |= WIN_INFO_DEL;

	return openMenuWindowPos( x, y, w, h, MENU_WINDOW_SRGENERAL, flg, opentype );
}

// ウィンドウ处理 ++++
BOOL MenuWindowGeneralBf( int mouse )
{
	// 外部から消されたとき内容が更新されている可能性があるので
	if (GrlClose){
		if ( wI->flag & WIN_INFO_DEL ){
			int i;
			for(i=0;i<EnumGeneralEnd;i++) wI->sw[i].Enabled = FALSE;
			return TRUE;
		}
	}

	if ( mouse == WIN_INIT ){
		int cnt;
		int mask;
		int w2, dw;
		int BtnH = ( sGrl.BottonType!=0 ? GENERAL_BUTTON_AREA_H : 0 );
		int i;

		wI->sx = sGrl.w,	wI->sy = sGrl.h;

		cnt = 0;
		mask = 1;
		for( i = 0; i < EnumGeneralGraTail-EnumGeneralGraHead; i++ ){
			if( sGrl.BottonType & mask ) cnt++;
			mask <<= 1;
		}
	
		// ボタン配置
		w2 = ( wI->sx - (GENERAL_STRPOS_X*2) ) / ( cnt + 1 );
		dw = w2;
	
		cnt = 0;
		mask = 1;
		for( i = EnumGeneralGraHead; i < EnumGeneralGraTail; i++ ){
			wI->sw[i].Enabled=FALSE;
			if( sGrl.BottonType & mask ){
				wI->sw[i].Enabled = TRUE;
				wI->sw[i].sx = GENERAL_BUTTON_W;
				wI->sw[i].sy = GENERAL_BUTTON_H;
				wI->sw[i].ofx = GENERAL_STRPOS_X + w2 - ( GENERAL_BUTTON_W / 2 );
				wI->sw[i].ofy = ( wI->sy - BtnH - GENERAL_STRPOS_Y ) + ( BtnH / 2 ) - 9;
				w2 += dw;
			}
			mask <<= 1;
		}

		// 文字列选择部分初期化
		wI->sw[EnumGeneralStrButton].ofx = 0,	wI->sw[EnumGeneralStrButton].ofy = 0;
		wI->sw[EnumGeneralStrButton].sx = wI->sx;
		wI->sw[EnumGeneralStrButton].sy = wI->sy;

		GrlNowInput = -1;
		// 文字列入力部分初期化
		if ( sGrl.InputStr > 0 ){
#ifdef PUK3_NOTFREE_GENERALWINDDOW
			GrlInput = (char **)calloc( sGrl.InputStr, sizeof(char *) );
			if ( GrlInput != NULL ){
	#ifdef PUK2_MEMCHECK
				memlistset( GrlInput, MEMLISTTYPE_GeneralWindow );
	#endif
				for(i=0;i<sGrl.InputStr;i++){
					GrlInput[i] = (char *)calloc( sGrl.InputStrLen+1, sizeof(char) );
					if ( GrlInput == NULL ){
						break;
					}
	#ifdef PUK2_MEMCHECK
					memlistset( GrlInput[i], MEMLISTTYPE_GeneralWindow );
	#endif
				}
				// 确保に失败したとき
				if ( i < sGrl.InputStr ){
					for(i=0;i<sGrl.InputStr;i++){
						if ( GrlInput[i] == NULL ) break;
	#ifdef PUK2_MEMCHECK
						memlistrel( GrlInput[i], MEMLISTTYPE_GeneralWindow );
	#endif
						free( GrlInput[i] );	GrlInput[i] = NULL;
					}
	#ifdef PUK2_MEMCHECK
					memlistrel( GrlInput, MEMLISTTYPE_GeneralWindow );
	#endif
					free( GrlInput );	GrlInput = NULL;
				}
			}
#else
			GrlInput = (char **)calloc( sGrl.InputStr, sizeof(char *) );
#ifdef PUK2_MEMCHECK
			memlistset( GrlInput, MEMLISTTYPE_GeneralWindow );
#endif
			for(i=0;i<sGrl.InputStr;i++){
				GrlInput[i] = (char *)calloc( sGrl.InputStrLen+1, sizeof(char) );
#ifdef PUK2_MEMCHECK
				memlistset( GrlInput[i], MEMLISTTYPE_GeneralWindow );
#endif
			}
#endif

			InitStrStructGeneralWin.lineLen = sGrl.InputStrLen;

			wI->sw[EnumGeneralInput].Enabled = TRUE;

			// ダイアログ初期化
			SetInputStr( &InitStrStructGeneralWin, wI->wx + GENERAL_STRPOS_X, wI->wy+sGrl.InputStrPos[0], 0 );

			// フォーカスを取る
			GetKeyInputFocus( &GeneralWinInputStr );

			DiarogST.SwAdd = wI->sw[EnumGeneralInput].Switch;
			( (DIALOG_SWITCH *)wI->sw[EnumGeneralInput].Switch )->InpuStrAdd = &GeneralWinInputStr;

			GrlNowInput = 0;
		}

		GrlClose = 1;

		// アクションの初期化
		for(i=EnumGeneralActionHead,cnt=0;i<EnumGeneralActionTail;i++,cnt++){
			( (ACTION_SWITCH *)wI->sw[i].Switch )->ActionAdd->anim_chr_no = Grl_Action[cnt].actNo;
			( (ACTION_SWITCH *)wI->sw[i].Switch )->ActionAdd->anim_ang = Grl_Action[cnt].actAng;
			( (ACTION_SWITCH *)wI->sw[i].Switch )->ActionAdd->bltf = BLTF_NOCHG;
			( (ACTION_SWITCH *)wI->sw[i].Switch )->ActionAdd->bltfon = BLTF_NOCHG;
			wI->sw[i].ofx = Grl_Action[cnt].x,	wI->sw[i].ofy = Grl_Action[cnt].y;
			wI->sw[i].Enabled = FALSE;
			if (Grl_Action[cnt].useFlag) wI->sw[i].Enabled = TRUE;
		}

		return TRUE;
	}
#ifdef PUK3_NOTFREE_GENERALWINDDOW
	else{
		// 文字列入力部分初期化
		if ( sGrl.InputStr > 0  ){
			if ( GrlInput == NULL ){
				wI->flag |= WIN_INFO_DEL;
				GrlClose = 0;
			}
		}
	}
#endif

	GrlButtonOver = 0;
	GrlStrOver = -1;
	GrlMouseFlag = 0;

	return TRUE;
}

BOOL MenuWindowGeneralAf( int Mouse )
{
	struct BLT_MEMBER bm = {0};
	int i,j,k;

	// 外部から消されたとき内容が更新されている可能性があるので
	if (GrlClose){
		if ( wI->flag & WIN_INFO_DEL ) return TRUE;
	}

	bm.bltf = BLTF_NOCHG;
	bm.rgba.rgba = 0xffffffff;
	bm.BltVer = BLTVER_PUK2;
	bm.u = -150,	bm.v = -8;
	bm.h = 16;

	// ウィンドウを开いた位置から数グリッド离れたら、ウィンドウ种类が异なっていたらウィンドウを关闭
#ifdef PUK3_CURE_FP_LUCK
	if ( !( sGrl.Option & GRL_OPT_NOMOVE ) &&
		 checkMoveMapGridPos( sGrl.Move, sGrl.Move ) ){
#else
	if ( checkMoveMapGridPos( sGrl.Move, sGrl.Move ) ){
#endif
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}
	if (sGrl.ExtraFunc){
		if ( GrlButtonOver ) GrlMouseFlag = 0;
		if ( GrlStrOver > 0 ) GrlMouseFlag = 0;
		if ( sGrl.ExtraFunc( wI, GrlButtonOver, GrlStrOver, GrlMouseFlag ) ){
			wI->flag |= WIN_INFO_DEL;
			GrlClose = 0;
		}
	}

	// 信息の表示
	for( i = 0; i < sGrl.LineNum; i++ ){
		if( sGrl.Msg[i][0] == '\0' ) continue;

		StockFontBuffer( wI->wx+GENERAL_STRPOS_X, wI->wy+GENERAL_STRPOS_Y + 4 + (sGrl.Interval*i),
			FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, sGrl.Msg[i], 0, BoxColor );
	}

	if ( sGrl.InputStr > 0 ){
		// 入力文字列表示
		int x = wI->wx + GENERAL_STRPOS_X;

		if ( GrlNowInput >= 0 ){
			if ( pNowInputStr != &GeneralWinInputStr ){
				strcpy( GrlInput[GrlNowInput], GeneralWinInputStr.buffer );
				GrlNowInput = -1;
			}
		}

		for(i=0;i<sGrl.InputStr;i++){
			if ( i == GrlNowInput ) StockFontBuffer2(pNowInputStr);
			else{
				StockFontBuffer( x, wI->wy+sGrl.InputStrPos[i],
					FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, GrlInput[i], 0, BoxColor );
			}
		}

		// 文字入力栏背景描画
		if ( sGrl.Option & GRL_OPT_INPUTBACK ){
			k = wI->sx - GENERAL_STRPOS_X;
			for(j=GENERAL_STRPOS_X;j<k;){
				bm.w = k - j;
				if ( bm.w > 300 ) bm.w = 300;
				for(i=0;i<sGrl.InputStr;i++){
					StockDispBuffer_PUK2( wI->wx+j, wI->wy+sGrl.InputStrPos[i], DISP_PRIO_WIN2, GID_DialogBack, 0, 1, &bm );
				}
				j += bm.w;
			}
		}
	}

	displayMenuWindow();

	MenuWindowCommonDraw( sGrl.GraNo, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );
	
	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

// 共通ウィンドウ破弃 ++++
BOOL MenuWindowGeneralCl()
{
	int i;

	if ( GrlClose ){
		if (sGrl.ReturnFunc) sGrl.ReturnFunc( 0, -1, NULL, 0 );
	}

#ifdef PUK3_NOTFREE_GENERALWINDDOW
	if ( sGrl.InputStr > 0 ){
		if ( GrlInput != NULL ){
			// 文字入力列确保用配列解放
			for(i=0;i<sGrl.InputStr;i++){
				if ( GrlInput[i] == NULL ) continue;
	#ifdef PUK2_MEMCHECK
				memlistrel( GrlInput[i], MEMLISTTYPE_GeneralWindow );
	#endif
				free(GrlInput[i]);
			}
	#ifdef PUK2_MEMCHECK
			memlistrel( GrlInput, MEMLISTTYPE_GeneralWindow );
	#endif
			free(GrlInput);		GrlInput = NULL;
		}
	}
#else
	// 文字入力列确保用配列解放
	for(i=0;i<sGrl.InputStr;i++){
#ifdef PUK2_MEMCHECK
		memlistrel( GrlInput[i], MEMLISTTYPE_GeneralWindow );
#endif
		free(GrlInput[i]);
	}
#ifdef PUK2_MEMCHECK
	memlistrel( GrlInput, MEMLISTTYPE_GeneralWindow );
#endif
	free(GrlInput);		GrlInput = NULL;
#endif

	return TRUE;
}

// 各ボタンの处理 *********************//

// ボタン
const int GeneralBtnGra[][3]={
	{ GID_BigOKButtonOn, GID_BigOKButtonOff, GID_BigOKButtonOver },				// ＯＫ
	{ GID_BigCancelButtonOn, GID_BigCancelButtonOff, GID_BigCancelButtonOver },	// Ｃａｎｃｅｌ
	{ GID_BigYesButtonOn, GID_BigYesButtonOff, GID_BigYesButtonOver },			// Ｙｅｓ
	{ GID_BigNoButtonOn, GID_BigNoButtonOff, GID_BigNoButtonOver },				// Ｎｏ
	{ GID_BigBackButtonOn, GID_BigBackButtonOff, GID_BigBackButtonOver },		// Ｂａｃｋ
	{ GID_BigNextButtonOn, GID_BigNextButtonOff, GID_BigNextButtonOver },		// Ｎｅｘｔ
};

BOOL MenuWindowGraphicButton( int no, unsigned int flag )
{
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	BOOL ReturnFlag=FALSE;	

	if ( flag & MENU_MOUSE_OVER ) GrlButtonOver = 1<<(no-EnumGeneralGraHead);

	if ( flag & MENU_MOUSE_LEFT ){
		if (sGrl.ReturnFunc){
			if ( GrlNowInput >= 0 ) strcpy( GrlInput[GrlNowInput], GeneralWinInputStr.buffer );

			if ( sGrl.ReturnFunc( 1<<(no-EnumGeneralGraHead), -1, GrlInput, sGrl.InputStr ) ){
				wI->flag |= WIN_INFO_DEL;
				GrlClose = 0;
			}
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GeneralBtnGra[no-EnumGeneralGraHead][0];
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GeneralBtnGra[no-EnumGeneralGraHead][2];
	if ( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GeneralBtnGra[no-EnumGeneralGraHead][1];

	return ReturnFlag;
}

// 项目ボタン
BOOL MenuWindowStringButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	short x,y, len;
	int i,j;

	GrlMouseFlag = flag;

	// 先ずは、文字入力栏とのあたり判定
	x = wI->wx + GENERAL_STRPOS_X;
	len = wI->sx - ( GENERAL_STRPOS_X * 2 );

	for(i=0;i<sGrl.InputStr;i++){
		if( MakeHitBox( x, wI->wy+sGrl.InputStrPos[i], x+len, wI->wy+sGrl.InputStrPos[i]+16, -1 ) ) break;
	}
	// 文字入力栏の上なら
	if ( i < sGrl.InputStr ){
		if ( i != GrlNowInput ){
			// ボックス表示データをバッファに溜める
			StockBoxDispBuffer( x, wI->wy+sGrl.InputStrPos[i], x+len, wI->wy+sGrl.InputStrPos[i]+16, DISP_PRIO_WIN2, BoxColor, 0 );

			if( flag & MENU_MOUSE_LEFT ){
				if ( GrlNowInput >= 0 ) strcpy( GrlInput[GrlNowInput], GeneralWinInputStr.buffer );

				wI->sw[EnumGeneralInput].Enabled = TRUE;

				// ダイアログ初期化
				SetInputStr( &InitStrStructGeneralWin, x, wI->wy+sGrl.InputStrPos[i], 0 );

				// フォーカスを取る
				GetKeyInputFocus( &GeneralWinInputStr );

				DiarogST.SwAdd = wI->sw[EnumGeneralInput].Switch;
				( (DIALOG_SWITCH *)wI->sw[EnumGeneralInput].Switch )->InpuStrAdd = &GeneralWinInputStr;

				GrlNowInput = i;
				StrToNowInputStr( GrlInput[GrlNowInput] );

				ReturnFlag = TRUE;
			}
		}
	}
	// 文字入力栏の上でないなら
	else if ( sGrl.SelCnt > 0 ){
		x = wI->wx + GENERAL_STRPOS_X - 2;
		y = wI->wy + GENERAL_STRPOS_Y + 4 - 2;
		len = wI->sx - ( GENERAL_STRPOS_X * 2 );

		j = 0;
		for(i=0;i<sGrl.LineNum;i++){
			if( MakeHitBox( x, y+j, x+len, y+j+20, -1 ) ) break;
			j += sGrl.Interval;
		}
		if ( i < sGrl.LineNum ) GrlStrOver = i;

		j = sGrl.SelCnt * sGrl.Interval;

		for(i=sGrl.SelCnt;i<sGrl.LineNum;i++){
			if( MakeHitBox( x, y+j, x+len, y+j+20, -1 ) ) break;
			j += sGrl.Interval;
		}
		if ( i >= sGrl.LineNum ) i = -1;
		else if ( sGrl.Msg[i][0] == '\0' ) i = -1;

		// 项目の上なら
		if( i >= 0 ){
			// ボックス表示データをバッファに溜める
			StockBoxDispBuffer( x, y+j, x+len, y+j+20, DISP_PRIO_WIN2, BoxColor, 0 );

			// 项目が押されたらプロトコル送信
			if( flag & MENU_MOUSE_LEFT ){
				if ( GrlNowInput >= 0 ) strcpy( GrlInput[GrlNowInput], GeneralWinInputStr.buffer );

				if (sGrl.ReturnFunc){
					if ( sGrl.ReturnFunc( 0, i - sGrl.SelCnt, GrlInput, sGrl.InputStr ) ){
						wI->flag |= WIN_INFO_DEL;
						GrlClose = 0;
					}
				}

				ReturnFlag=TRUE;
			}
		}
	}

	if ( sGrl.Option & GRL_OPT_EXCLUSIVE ) ReturnFlag = TRUE;
	return ReturnFlag;
}

#endif