﻿/*
 *	タイトル表示用表示バッファ设定处理
 */
static void titleStockDisp( int sx, int sy, int prio, int graNo, int hit, int rgba )
{
	BLT_MEMBER bm={0};

	bm.u=0;
	bm.v=0;
	bm.w=0;
	bm.h=0;
	bm.rgba.rgba=rgba;
	bm.BltVer=BLTVER_NOMAL;
	bm.bltf=BLTF_NOCHG;
	bm.PalNo=0;
	StockDispBuffer_PUK2( sx, sy,  prio, graNo, hit, 1, &bm );
}

/*
 *	ラグってるかどうかチェック
 */
#define	PROC_TIME ((1.0/60)*1000)	// 处理に使う时间、后は描画时间
BOOL checkRag( void )
{
	if( NowTime + PROC_TIME < GetTickCount() ){
		// ラグってる
		return TRUE;
	}
	// ラグってない
	return FALSE;
}

// 各种ワーク
int		PUK2_titleFade;
int		PUK2_logo1Fade,PUK2_logo2Fade;
int		PUK2_inputFade;
ACTION	*pActTitleLogo[2];

// ＰＵＫ２用のグラフィックボタン用构造体
typedef struct {
	int cx, cy;						// 表示座标
	int w, h;						// 范围の縦横幅
	unsigned int graNo1;			// 凸状态のボタンの画像番号
	unsigned int graNo2;			// フォーカス状态のボタンの画像番号
	unsigned int graNo3;			// 凹状态のボタンの画像番号
} PUK2_BTN_INFO;

//-------------------------------------------------------------------------//
// ＰＵＫ２用のボタン                                                      //
//   ボタンが押されたかチェック                                            //
//-------------------------------------------------------------------------//
static int PUK2pushGraBtnInfo( PUK2_BTN_INFO *btn )
{
	int ret = 0;
	if( btn == NULL )return 0;
	if( MakeHitBox( SymOffsetX + btn->cx, SymOffsetY + btn->cy, SymOffsetX + btn->cx+btn->w, SymOffsetY + btn->cy+btn->h, -1 ) ){
		ret |= BTN_FOCUS_ON;
		if( (mouse.onceState & MOUSE_LEFT_CRICK) ){
			ret |= BTN_LEFT_CLICK;
		}
		if( (mouse.onceState & MOUSE_RIGHT_CRICK) ){
			ret |= BTN_RIGHT_CLICK;
		}
		if( (mouse.state & MOUSE_LEFT_CRICK) ){
			ret |= BTN_LEFT_ON;
		}
		if( (mouse.state & MOUSE_RIGHT_CRICK) ){
			ret |= BTN_RIGHT_ON;
		}
		if( (mouse.autoState & MOUSE_LEFT_CRICK) ){
			ret |= BTN_LEFT_CLICK_REP;
		}
		if( (mouse.autoState & MOUSE_RIGHT_CRICK) ){
			ret |= BTN_RIGHT_CLICK_REP;
		}
		if( (mouse.onceState & MOUSE_LEFT_DBL_CRICK) ){
			ret |= BTN_LEFT_DBL_CLICK;
		}
		if( (mouse.onceState & MOUSE_RIGHT_DBL_CRICK) ){
			ret |= BTN_RIGHT_DBL_CLICK;
		}
	}

	return ret;
}

//-------------------------------------------------------------------------//
// ＰＵＫ２用のボタン                                                      //
//   ボタンの描画                                                          //
//-------------------------------------------------------------------------//
void PUK2drawGraBtnInfo( PUK2_BTN_INFO *btn, unsigned char prio, int hitBoxFlag, int hitBoxColor, int drawFlag )
{
	int		graNo;

	if( btn == NULL )return;

	graNo = btn->graNo1;
	if( !drawFlag ){
		int flg;

		flg = PUK2pushGraBtnInfo( btn );
		if( flg & BTN_FOCUS_ON ){
			graNo = btn->graNo2;

			if( flg & BTN_LEFT_ON ){
				btn->graNo3;
			}
		}
	}else if( drawFlag == 2 ){
		graNo = btn->graNo2;
	}
	titleStockDisp( SymOffsetX + btn->cx, SymOffsetY + btn->cy, prio, graNo, 1, 0xffffffff );

	if( hitBoxFlag && MakeHitBox( SymOffsetX + btn->cx, SymOffsetY + btn->cy, SymOffsetX + btn->cx+btn->w, SymOffsetY + btn->cy+btn->h, -1 ) && drawFlag == 0 ){
		int boxType = 0;
		if( hitBoxFlag == 2 ){
			// 涂りつぶし
			boxType = 1;
		}
		StockBoxDispBuffer( SymOffsetX + btn->cx-2, SymOffsetY + btn->cy-2, SymOffsetX + btn->cx+btn->w+2, SymOffsetY + btn->cy+btn->h+2,
			prio, hitBoxColor, boxType );
	}
}



// ＩＤ??パスワード入力バッファ初期化
static void initPUK2InputIdPassword( void )
{
	char str[256] = {0};

	// インストールしているバージョンを调べる
	giInstallVersion = GetInstallVersion( );

	// ID入力バッファ初期化
	//idKeyBoxX = 195+4+58;
	//idKeyBoxY = 339+5+14;
	idKeyBoxX = 0;
	idKeyBoxY = 0;
	idKey.buffer[0] = '\0';
	idKey.cnt = 0;
	idKey.cursorByte = 0;
#ifdef _DEBUG
	getId( str );
	InitInputStr( &idKey, idKeyBoxX, idKeyBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
		FONT_PAL_BLUE, str, 1, 40, 0, 0 );
#endif
	// パスワード入力バッファ初期化
	passwdBoxX = 195+4+58;
	passwdBoxY = 370+5+14;
	passwd.buffer[0] = '\0';
	passwd.cnt = 0;
	passwd.cursorByte = 0;
#ifdef _DEBUG
	getPassword( str );
#endif
	InitInputStr( &passwd, passwdBoxX, passwdBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
		FONT_PAL_BLUE, str, 1, 20, 0, TRUE );

	// ＣＤキー入力バッファ初期化
	cdKeyBoxX = 195+4+58;
	//cdKeyBoxY = 401+5+14;
	cdKeyBoxY = 339+5+14;
	cdKey.buffer[0] = '\0';
	cdKey.cnt = 0;
	cdKey.cursorByte = 0;
#ifdef _DEBUG
	getCdkey( str );
#endif
	InitInputStr( &cdKey, cdKeyBoxX, cdKeyBoxY, FONT_PRIO_BACK, FONT_KIND_MIDDLE,
		FONT_PAL_BLUE, str, 1, 23, 0, 0 );

	// フォーカス设定
	idPasswordFocusSw = 0;
	GetKeyInputFocus( idPasswordFocus[idPasswordFocusSw] );

	// 入力の表示设定
	PUK2_inputFade = 0xff;

}


// 入力处理??画面表示
//
//  戾り值：	 0 ... 入力中
//				 1 ... 入力完了
//				 2 ... 終了ボタン
//				-1 ... 入力栏が空
static int PUK2InputIdPassword( int flag )
{
	int id;
	int selUseFlag;
	static BOOL flag2 = FALSE;
	static int oldId = 0;
	int ret = 0;
	int x1, y1, x2, y2;
	int strWidth;

	PUK2_BTN_INFO pOkBtn =
	{// 范围始点座标(x,y)
		240, 438+14,
	 // 范围幅(w,h)
		64, 16,
	 // ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_OK0, PUK2_BUTTON_OK2, PUK2_BUTTON_OK1
	};
	// ＱＵＩＴボタン
	PUK2_BTN_INFO pQuitBtn =
	{// 范围始点座标(x,y)
		332, 438+14,
	 // 范围幅(w,h)
		64, 16,
	 // ボタン画像番号(凸画像,凹画像)
		PUK2_BUTTON_QUIT0, PUK2_BUTTON_QUIT2, PUK2_BUTTON_QUIT1
	};

	// ボタン等の选择可??不可の设定
	if( flag == 0 ){
		selUseFlag = 0;	// ボタンの选择可
	}else{
		selUseFlag = 1;	// ボタンの选择不可
	}

	// ＯＫボタンが押された
	if( (PUK2pushGraBtnInfo( &pOkBtn ) & BTN_LEFT_CLICK) && !selUseFlag ){
		// 押されたときのグラフィックに切り替え
		pOkBtn.graNo1 = PUK2_BUTTON_OK1;

		// ID??パスワード??CDキーが设定されているか
		if( strlen( idKey.buffer ) > 0
		 && strlen( passwd.buffer ) > 0
		 && strlen( cdKey.buffer ) > 0 )
		{
			idKey.buffer[idKey.cnt] = '\0';
			passwd.buffer[passwd.cnt] = '\0';
			cdKey.buffer[cdKey.cnt] = '\0';
			ret = 1;
		}else{
			// 设定されていない
			ret = -1;
		}
	}
	// 終了ボタンが押された
	else if( (PUK2pushGraBtnInfo( &pQuitBtn ) & BTN_LEFT_CLICK) && !selUseFlag ){
		// 押されたときのグラフィックに切り替え
		pQuitBtn.graNo1 = PUK2_BUTTON_QUIT1;

		ret = 2;
	}

	if( flag < 2 ){
		// 文字の背景を描画
		// ＩＭＥバッファーの文字列の横幅を求める
		strWidth = GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind );

		// ボックス表示データをバッファに溜める
		StockBoxDispBuffer( pNowInputStr->imeX - 1, 
							pNowInputStr->imeY - 1, 
							pNowInputStr->imeX + strWidth, 
							pNowInputStr->imeY + FontKind[ pNowInputStr->fontKind ].zenkakuHeight, 
							DISP_PRIO_IME2, SYSTEM_PAL_BLACK, 1 );
		
		StockFontBuffer2( &idKey );
		StockFontBuffer2( &passwd );
		StockFontBuffer2( &cdKey );
	}

	// 入力フォーカス变更
	id = -1;
	if( keyOnRep( VK_TAB ) || keyOnRep( VK_RETURN ) ){
		if( oldId == 0 ){
			id = 1;
		}else if( oldId == 1 ){
			id = 2;
		}else if( oldId == 2 ){
			id = 0;
		}
	}

	if( !selUseFlag ){
		x1 = idKeyBoxX-5;
		y1 = idKeyBoxY-4;
		x2 = x1 + 372;
		y2 = y1 + 26;
		if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
			id = 0;
		}
		x1 = passwdBoxX-5;
		y1 = passwdBoxY-4;
		x2 = x1 + 372;
		y2 = y1 + 26;
		if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
			id = 1;
		}
		x1 = cdKeyBoxX-5;
		y1 = cdKeyBoxY-4;
		x2 = x1 + 372;
		y2 = y1 + 26;
		if( MakeHitBox( x1, y1, x2, y2, DISP_PRIO_BOX ) ){
			id = 2;
		}
	}

	if( flag > 0 ){
		// フラグがFALSEなら入力できなくする
		GetKeyInputFocus( NULL );
		flag2 = TRUE;
	}else if( (0 <= id && id <= 2) || flag2 ){
		if( flag2 )
			id = oldId;
		GetKeyInputFocus( idPasswordFocus[id] );
		flag2 = FALSE;
		oldId = id;
	}

	// ＯＫボタン
	PUK2drawGraBtnInfo( &pOkBtn, DISP_PRIO_BOX+1, 0, 0, selUseFlag );

	// ＱＵＩＴボタン
	PUK2drawGraBtnInfo( &pQuitBtn, DISP_PRIO_BOX+1, 0, 0, selUseFlag );

	// バージョン表示
#ifdef _TAIKEN
	StockFontBuffer( 480, 424, FONT_PRIO_BACK, FONT_PAL_BLUE, ML_STRING(305, "体验版"), 0 );
#endif
#ifdef PUK2_NEWVER
	if( giInstallVersion >= 1 ){
		StockFontBuffer( 460, 462, FONT_PRIO_BACK, FONT_PAL_BLUE, NR_VERSION, 0 );
	}else{
		StockFontBuffer( 460, 462, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
	}
#else
	if( giInstallVersion >= 1 ){
		StockFontBuffer( 540, 462, FONT_PRIO_BACK, FONT_PAL_BLUE, NR_VERSION, 0 );
	}else{
		StockFontBuffer( 540, 462, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
	}
#endif
	
	return ret;
}


//
//	タイトル表示??ＩＤ入力
//
int		PUK2_titleCounter;
extern int	PUK2_fillColor;
// 赤
#define PPLR 2
// 緑
#define PPLG 1
// 青
#define PPLB 0
void PUK2_idPasswordProc( void )
{
	int		flag = 2;
	int		ret;
	static char	msg[256];

	switch( SubProcNo ){
	case E_IDPASS_INIT:	// 初期化时
		DeathAllAction( );

		// ワークを初期化
		PUK2_titleCounter = 0;
		PUK2_titleFade = 0;
		PUK2_logo1Fade = 0;
		PUK2_logo2Fade = 0;
		PUK2_inputFade = 0;

		// 事前にグラフィックを読み込んでおくようにする
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLEINPUT, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLELOGO0, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLELOGO1, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLELOGO2, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLE, 0, 0xffffffff );

		// 次の处理へ
		SubProcNo = E_IDPASS_WHITE;
#ifdef PUK2_TITLEBGM_2
		play_bgm( BGM_PUK2_OP );
#endif
		break;

	case E_IDPASS_WHITE:	// 初期化时
		// 事前読み込み用
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLEINPUT, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLELOGO0, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLELOGO1, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLELOGO2, 0, 0xffffffff );
		titleStockDisp( 638, 478, DISP_PRIO_BG, PUK2_TITLE, 0, 0xffffffff );
		// 处理が追いつくまで待つ
		if( checkRag() ){
			break;
		}

		PUK2_titleCounter += 4;

		if ( displayBpp == 16 ){
			PUK2_fillColor =
					lpDraw->PalTbl[PUK2_titleCounter][PPLR]
					| lpDraw->PalTbl[PUK2_titleCounter][PPLG]
					| lpDraw->PalTbl[PUK2_titleCounter][PPLB];
		}else{
			PUK2_fillColor = (PUK2_titleCounter<<16) | (PUK2_titleCounter<<8) | PUK2_titleCounter;
		}

		if( PUK2_titleCounter >= 0xff ){
			if ( displayBpp == 16 ){
				PUK2_fillColor = 0xffff;
			}else{
				PUK2_fillColor = 0xffffff;
			}
			PUK2_titleCounter = 0;
			// 次の处理へ
			SubProcNo = E_IDPASS_SANDCLOCK;
		}
		break;

	case E_IDPASS_SANDCLOCK:	// 砂时计画面
		// たまごフェードイン
		PUK2_titleCounter++;
		PUK2_titleFade = ( 0xff * PUK2_titleCounter ) / 40;
		if( PUK2_titleCounter >= 40 ){
			PUK2_titleCounter = 0;
			PUK2_titleFade = 0xff;

			// タイトルロゴアクション起こす
			pActTitleLogo[0] = GetAction( PRIO_CHR, 0 );
			if( pActTitleLogo[0] != NULL ){
				pActTitleLogo[0]->anim_chr_no = PUK2_TITLELOGO0;
				pActTitleLogo[0]->anim_no = ANIM_STAND;
				pActTitleLogo[0]->dispPrio = DISP_PRIO_CHAR;
				pActTitleLogo[0]->x = 320+131;
				pActTitleLogo[0]->y = 240-47;
				pActTitleLogo[0]->anim_ang = 0;
				pActTitleLogo[0]->rgbaon = 8;
				pActTitleLogo[0]->rgba.rgba = 0x00ffffff;
				pattern( pActTitleLogo[0], ANM_NOMAL_SPD, ANM_LOOP );
			}
			pActTitleLogo[1] = GetAction( PRIO_CHR, 0 );
			if( pActTitleLogo[1] != NULL ){
				pActTitleLogo[1]->anim_chr_no = PUK2_TITLELOGO0;
				pActTitleLogo[1]->anim_no = ANIM_STAND;
				pActTitleLogo[1]->dispPrio = DISP_PRIO_CHAR;
				pActTitleLogo[1]->x = 320-131;
				pActTitleLogo[1]->y = 240+47;
				pActTitleLogo[1]->anim_ang = 0;
				pActTitleLogo[1]->rgbaon = 8;
				pActTitleLogo[1]->rgba.rgba = 0x00ffffff;
				pattern( pActTitleLogo[1], ANM_NOMAL_SPD, ANM_LOOP );
				SubProcNo = E_IDPASS_XGLOGO;	// ロゴ飞ぶへ
			}else{
				SubProcNo = E_IDPASS_TITLE;	// 失败したら入力へ
#ifdef PUK2_TITLEBGM_1
				play_bgm( BGM_PUK2_OP );
#endif
			}
		}
		break;

	case E_IDPASS_XGLOGO:	// ロゴ飞んでくる
		pActTitleLogo[0]->x -= 3;
		pActTitleLogo[0]->y += 1;
		pActTitleLogo[1]->x += 3;
		pActTitleLogo[1]->y -= 1;

		// フェードの设定
		PUK2_titleCounter = 47 - ( pActTitleLogo[1]->y - 240 );
		if( PUK2_titleCounter >= 0 ){
			pActTitleLogo[0]->rgba.a = PUK2_titleCounter * 0x80 / 47;
			pActTitleLogo[1]->rgba.a = pActTitleLogo[0]->rgba.a;
		}
		if( pActTitleLogo[0]->x <= 320 ){
			// アクション停止＆黒く
			pActTitleLogo[0]->x = 320;
			pActTitleLogo[0]->y = 240;
			pActTitleLogo[0]->rgba.a = 0x00;
			// アクション消去
			DeathAction( pActTitleLogo[1] );
			pActTitleLogo[1] = NULL;

			// 一瞬白く
			PUK2_titleFade = 0;
			PUK2_logo1Fade = 0;
			PUK2_logo2Fade = 0;
			SubProcNo = E_IDPASS_PRE_TITLE;	// タイトル表示へ
			PUK2_titleCounter = 16;
#ifdef PUK2_TITLEBGM_3
			play_bgm( BGM_PUK2_OP );
#endif
		}
		break;

	case E_IDPASS_PRE_TITLE:	// タイトル表示へ
		// カウントチェック
		if( PUK2_titleCounter > 0 ){
			PUK2_titleCounter--;
			if( !PUK2_titleCounter ){
				// まだアクションが残っているか？
				if( pActTitleLogo[0] ){
					pActTitleLogo[0]->rgba.a = 0xff;
					PUK2_titleFade = 0xff;
//					PUK2_logo1Fade = 0xff;
				}
			}
			break;
		}
		if( PUK2_logo1Fade < 0xff ){
			PUK2_logo1Fade += 4;
			if( PUK2_logo1Fade >= 0xff ){
				PUK2_logo1Fade = 0xff;
				PUK2_titleCounter = 60;

				// アクション消去
				DeathAction( pActTitleLogo[0] );
				pActTitleLogo[0] = NULL;
				// 背景を黒に
				PUK2_fillColor = 0;
				PUK2_titleFade = 0;
			}
		}else{
			// フェードイン
			PUK2_logo2Fade += 4;
			if( PUK2_logo2Fade > 0xff ){
				PUK2_logo1Fade = 0;
				PUK2_logo2Fade = 0xff;

				initPUK2InputIdPassword();
				SubProcNo = E_IDPASS_TITLE;	// タイトル表示へ
#ifdef PUK2_TITLEBGM_1
				play_bgm( BGM_PUK2_OP );
#endif
			}
		}
		break;

	case E_IDPASS_SKIP_TITLE:	// キャンセルされてタイトル表示へ
		PUK2_titleFade = 0;
		PUK2_logo1Fade = 0;
		PUK2_logo2Fade = 0xff;
		PUK2_fillColor = 0;			// 背景を黒に
		DeathAction( pActTitleLogo[0] );
		DeathAction( pActTitleLogo[1] );
		pActTitleLogo[0] = pActTitleLogo[1] = NULL;

		initPUK2InputIdPassword();
		SubProcNo = E_IDPASS_TITLE;	// タイトル表示へ
#ifdef PUK2_TITLEBGM_1
		play_bgm( BGM_PUK2_OP );
#endif
#ifdef PUK2_TITLEBGM_3
		play_bgm( BGM_PUK2_OP );
#endif
		break;

	case E_IDPASS_TITLE:	// タイトル表示へ
		flag = 0;	// ＩＤ??パスワード入力可能フラグ
		break;

	case E_IDPASS_ENTER:	// 入力された
		if( palChageStatus == FALSE ){
			// フェードアウト１段阶目は通常のみ
			PaletteChange( 19, 60 );		// 通常フェードアウト
			SubProcNo = E_IDPASS_NEXT;		// 次の画面へ
		}
		break;

	case E_IDPASS_ENTER_ERR:			// エラーの时
		initCommonMsgWin();
		// ウィンドウ开く音
		play_se( SE_NO_OPEN_WINDOW, 320, 240 );
		SubProcNo = E_IDPASS_ENTER_ERR_WAIT;
		break;

	case E_IDPASS_ENTER_ERR_WAIT:	// エラーで确认待ち
		if( commonMsgWin( msg ) ){
			// ＯＫボタンが押された
			SubProcNo = E_IDPASS_SKIP_TITLE;
		}
		break;

	case E_IDPASS_NEXT:		// 次の画面へ行くなら
		// タイトル画面へ
		ChangeProc( PROC_TITLE_MENU );
		DeathAllAction( );
		PUK2_fillColor = 0;			// 背景を黒に
		break;
	}

	// 右键でスキップ
	if( SubProcNo < E_IDPASS_PRE_TITLE ){
		if( (mouse.onceState & MOUSE_LEFT_CRICK) ){
			SubProcNo = E_IDPASS_SKIP_TITLE;	// スキップ
		}
	}

	// 入力状态になったら
	if( SubProcNo != E_IDPASS_TITLE ){
		// フラグがFALSEなら入力できなくする
		GetKeyInputFocus( NULL );
//		return ;
	}else{
		ret = PUK2InputIdPassword( 0 );
		if( ret == 1 ){
			// 入力されたID,PASSWDをとりあえずファイルに保存
#ifndef VERSION_TW
			//台服版取消id保存到SaveDat
			setId( idKey.buffer );
#endif
			setPassword( passwd.buffer );
			setCdkey( cdKey.buffer );
			if( saveNowState() )
			{
			}

			// 决定ａ
			play_se( SE_NO_OK, 320, 240 );
			// 决定时のパレットチェンジ
			PaletteChange( 30, 30 );	// ＥＸ用
			SubProcNo = E_IDPASS_NEXT;	// 次へ
		}
		else
		// 終了ボタンが押されたので終了处理
		if( ret == 2 ){
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
			// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			PostMessage( hWnd, WM_CLOSE, 0, 0L );
		}
		//-------------------------------------------------------------
		// ID,PASSWORDが空の时のエラー信息
		else 
		if( ret < 0 ){
			SubProcNo = E_IDPASS_ENTER_ERR;
			strcpy( msg, ML_STRING(263, "请输入帐号和密码") );
			//台服新增清除已保存密码和账号
			memset( passwd.buffer, 0, sizeof(passwd.buffer) );
			setCdkey( passwd.buffer );
		}
	}

	/* 各种表示处理 */
	// 入力バッファ
	if( PUK2_inputFade ){
//		titleStockDisp( 138, 348, DISP_PRIO_BG, PUK2_TITLEINPUT, 0, ( ( PUK2_inputFade * 0x01000000 ) | 0x00ffffff ) );
		titleStockDisp( 139, 350, DISP_PRIO_BOX, PUK2_TITLEINPUT, 0, ( ( PUK2_inputFade * 0x01000000 ) | 0x00ffffff ) );
	}//81,336
	// タイトルロゴ（重ね）
	if( PUK2_logo2Fade ){
		titleStockDisp(   0,   0, DISP_PRIO_BOX, PUK2_TITLELOGO2, 0, ( ( PUK2_logo2Fade * 0x01000000 ) | 0x00ffffff ) );
	}
	// タイトルロゴ（文字黒）
	if( PUK2_logo1Fade ){
		titleStockDisp(   0,   0, DISP_PRIO_BOX, PUK2_TITLELOGO1, 0, ( ( PUK2_logo1Fade * 0x01000000 ) | 0x00ffffff ) );
	}
	// タイトルバック
	if( PUK2_titleFade ){
		titleStockDisp(   0,   0, DISP_PRIO_BG, PUK2_TITLE, 0, ( ( PUK2_titleFade * 0x01000000 ) | 0x00ffffff ) );
	}

}


#ifdef VERSION_TW
PUK2_BTN_INFO PUK2_backBtn =
{// 范围始点座标(x,y)
	540, 360,
	// 范围幅(w,h)
	   64, 16,
	   // ボタン画像番号(凸画像,凹画像)
		  PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK2, PUK2_BUTTON_BACK1
};
PUK2_BTN_INFO PUK2_exitBtn =
{// 范围始点座标(x,y)
	540, 392,
	// 范围幅(w,h)
	   64, 16,
	   // ボタン画像番号(凸画像,凹画像)
		  PUK2_BUTTON_QUIT0, PUK2_BUTTON_QUIT2, PUK2_BUTTON_QUIT1
};
PUK2_BTN_INFO PUK2_leftBtn =
{// 范围始点座标(x,y)
	130, 310,
	// 范围幅(w,h)
	   16, 16,
	   // ボタン画像番号(凸画像,凹画像)
		  PUK2_BUTTON_LEFT0, PUK2_BUTTON_LEFT2, PUK2_BUTTON_LEFT1
};
PUK2_BTN_INFO PUK2_rightBtn =
{// 范围始点座标(x,y)
	254, 310,
	// 范围幅(w,h)
	   16, 16,
	   // ボタン画像番号(凸画像,凹画像)
		  PUK2_BUTTON_RIGHT0, PUK2_BUTTON_RIGHT2, PUK2_BUTTON_RIGHT1
};
//台服新增的返回按钮.使用在返回主服务器选择界面
PUK2_BTN_INFO TW_BackMainServerBtn =
{// 范围始点座标(x,y)
	168, 338,
	// 范围幅(w,h)
	   92, 34,
	   // ボタン画像番号(凸画像,凹画像)
		  PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK2, PUK2_BUTTON_BACK1
};

#define TW_SB_L_X	109
#define TW_SB_R_X	209
#define TW_SB_Y		108
#define TW_SB_W		98
#define TW_SB_H		34
#define TW_SB_FY	32

//台服主服务图档
PUK2_BTN_INFO TW_MainServerNameBtn[] =
{
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 0, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_1_1,	TW_CG_MAIN_SERVER_NAME_1_2,		TW_CG_MAIN_SERVER_NAME_1_2	},
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 4, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_2_1,	TW_CG_MAIN_SERVER_NAME_2_2,		TW_CG_MAIN_SERVER_NAME_2_2	},
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 2, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_3_1,	TW_CG_MAIN_SERVER_NAME_3_2,		TW_CG_MAIN_SERVER_NAME_3_2	},
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 3, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_4_1,	TW_CG_MAIN_SERVER_NAME_4_2,		TW_CG_MAIN_SERVER_NAME_4_2	},
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 1, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_5_1,	TW_CG_MAIN_SERVER_NAME_5_2,		TW_CG_MAIN_SERVER_NAME_5_2	},
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 5, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_6_1,	TW_CG_MAIN_SERVER_NAME_6_2,		TW_CG_MAIN_SERVER_NAME_6_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 3, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_7_1,	TW_CG_MAIN_SERVER_NAME_7_2,		TW_CG_MAIN_SERVER_NAME_7_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 2, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_8_1,	TW_CG_MAIN_SERVER_NAME_8_2,		TW_CG_MAIN_SERVER_NAME_8_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 0, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_9_1,	TW_CG_MAIN_SERVER_NAME_9_2,		TW_CG_MAIN_SERVER_NAME_9_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 6, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_10_1,	TW_CG_MAIN_SERVER_NAME_10_2,	TW_CG_MAIN_SERVER_NAME_10_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 5, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_11_1,	TW_CG_MAIN_SERVER_NAME_11_2,	TW_CG_MAIN_SERVER_NAME_11_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 6, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_12_1,	TW_CG_MAIN_SERVER_NAME_12_2,	TW_CG_MAIN_SERVER_NAME_12_2	},
	{ 224, 338, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_4_1,	TW_CG_MAIN_SERVER_NAME_4_2,		TW_CG_MAIN_SERVER_NAME_4_2	},
	{ TW_SB_R_X, TW_SB_Y + TW_SB_FY * 5, TW_SB_W - 6, TW_SB_H, TW_CG_MAIN_SERVER_NAME_13_1,	TW_CG_MAIN_SERVER_NAME_13_2,	TW_CG_MAIN_SERVER_NAME_13_2	},
	{ 124, 338, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_14_1,	TW_CG_MAIN_SERVER_NAME_14_2,	TW_CG_MAIN_SERVER_NAME_14_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 0, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_1_1,	TW_CG_MAIN_SERVER_NAME_1_2,		TW_CG_MAIN_SERVER_NAME_1_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 2, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_2_1,	TW_CG_MAIN_SERVER_NAME_2_2,		TW_CG_MAIN_SERVER_NAME_2_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 0, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_15_1,	TW_CG_MAIN_SERVER_NAME_15_2,	TW_CG_MAIN_SERVER_NAME_15_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 3, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_4_1,	TW_CG_MAIN_SERVER_NAME_4_2,		TW_CG_MAIN_SERVER_NAME_4_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 4, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_5_1,	TW_CG_MAIN_SERVER_NAME_5_2,		TW_CG_MAIN_SERVER_NAME_5_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 5, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_6_1,	TW_CG_MAIN_SERVER_NAME_6_2,		TW_CG_MAIN_SERVER_NAME_6_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 1, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_15_1,	TW_CG_MAIN_SERVER_NAME_15_2,	TW_CG_MAIN_SERVER_NAME_15_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 0, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_16_1,	TW_CG_MAIN_SERVER_NAME_16_2,	TW_CG_MAIN_SERVER_NAME_16_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 3, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_15_1,	TW_CG_MAIN_SERVER_NAME_15_2,	TW_CG_MAIN_SERVER_NAME_15_2	},
	{ TW_SB_L_X, TW_SB_Y + TW_SB_FY * 4, TW_SB_W, TW_SB_H, TW_CG_MAIN_SERVER_NAME_16_1,	TW_CG_MAIN_SERVER_NAME_16_2,	TW_CG_MAIN_SERVER_NAME_16_2	}
};

//台服线路图档
PUK2_BTN_INFO TW_ServerNameBtn[] =
{
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 0,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_1_1, TW_CG_SERVER_NAME_1_2, TW_CG_SERVER_NAME_1_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 1,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_2_1, TW_CG_SERVER_NAME_2_2, TW_CG_SERVER_NAME_2_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 2,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_3_1, TW_CG_SERVER_NAME_3_2, TW_CG_SERVER_NAME_3_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 3,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_4_1, TW_CG_SERVER_NAME_4_2, TW_CG_SERVER_NAME_4_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 4,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_5_1, TW_CG_SERVER_NAME_5_2, TW_CG_SERVER_NAME_5_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 0,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_6_1, TW_CG_SERVER_NAME_6_2, TW_CG_SERVER_NAME_6_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 1,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_7_1, TW_CG_SERVER_NAME_7_2, TW_CG_SERVER_NAME_7_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 2,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_8_1, TW_CG_SERVER_NAME_8_2, TW_CG_SERVER_NAME_8_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 3,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_9_1, TW_CG_SERVER_NAME_9_2, TW_CG_SERVER_NAME_9_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 4,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_10_1, TW_CG_SERVER_NAME_10_2, TW_CG_SERVER_NAME_10_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 0,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_1_1, TW_CG_SERVER_NAME_1_2, TW_CG_SERVER_NAME_1_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 1,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_2_1, TW_CG_SERVER_NAME_2_2, TW_CG_SERVER_NAME_2_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 2,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_3_1, TW_CG_SERVER_NAME_3_2, TW_CG_SERVER_NAME_3_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 3,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_4_1, TW_CG_SERVER_NAME_4_2, TW_CG_SERVER_NAME_4_2
	},
	{
		TW_SB_L_X,  TW_SB_Y + TW_SB_FY * 4,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_5_1, TW_CG_SERVER_NAME_5_2, TW_CG_SERVER_NAME_5_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 0,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_6_1, TW_CG_SERVER_NAME_6_2, TW_CG_SERVER_NAME_6_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 1,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_7_1, TW_CG_SERVER_NAME_7_2, TW_CG_SERVER_NAME_7_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 2,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_8_1, TW_CG_SERVER_NAME_8_2, TW_CG_SERVER_NAME_8_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 3,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_9_1, TW_CG_SERVER_NAME_9_2, TW_CG_SERVER_NAME_9_2
	},
	{
		TW_SB_R_X,  TW_SB_Y + TW_SB_FY * 4,
		TW_SB_W, TW_SB_H,  TW_CG_SERVER_NAME_10_1, TW_CG_SERVER_NAME_10_2, TW_CG_SERVER_NAME_10_2
	}
};
#else

PUK2_BTN_INFO PUK2_backBtn =
{// 范围始点座标(x,y)
	540, 360,
 // 范围幅(w,h)
	64, 16,
 // ボタン画像番号(凸画像,凹画像)
	PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK2, PUK2_BUTTON_BACK1
};
PUK2_BTN_INFO PUK2_exitBtn =
{// 范围始点座标(x,y)
	540, 392,
 // 范围幅(w,h)
	64, 16,
 // ボタン画像番号(凸画像,凹画像)
	PUK2_BUTTON_QUIT0, PUK2_BUTTON_QUIT2, PUK2_BUTTON_QUIT1
};
PUK2_BTN_INFO PUK2_leftBtn =
{// 范围始点座标(x,y)
	191, 348,
 // 范围幅(w,h)
	16, 16,
 // ボタン画像番号(凸画像,凹画像)
	PUK2_BUTTON_LEFT0, PUK2_BUTTON_LEFT2, PUK2_BUTTON_LEFT1
};
PUK2_BTN_INFO PUK2_rightBtn =
{// 范围始点座标(x,y)
	191, 348,
 // 范围幅(w,h)
	16, 16,
 // ボタン画像番号(凸画像,凹画像)
	PUK2_BUTTON_RIGHT0, PUK2_BUTTON_RIGHT2, PUK2_BUTTON_RIGHT1
};

#define SSPUK2_SB_X		113
#define SSPUK2_SB_Y		108
#define SSPUK2_SB_SX	184
#define SSPUK2_SB_SY	32
#define SSPUK2_CUR_X	110
#define SSPUK2_CUR_Y	105


PUK2_BTN_INFO PUK2_serverNameBtn[] =
{
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*0,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  CG_SERVER_NAME_1_1, CG_SERVER_NAME_1_2, CG_SERVER_NAME_1_2
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*1,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  CG_SERVER_NAME_2_1, CG_SERVER_NAME_2_2, CG_SERVER_NAME_2_2
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*2,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  CG_SERVER_NAME_3_1, CG_SERVER_NAME_3_2, CG_SERVER_NAME_3_2
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*3,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  CG_SERVER_NAME_4_1, CG_SERVER_NAME_4_2, CG_SERVER_NAME_4_2
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*4,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  CG_SERVER_NAME_5_1, CG_SERVER_NAME_5_2, CG_SERVER_NAME_5_2
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*5,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  CG_SERVER_NAME_6_1, CG_SERVER_NAME_6_2, CG_SERVER_NAME_6_2
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*0,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  PUK2_SERVER_YEOSKEER0, PUK2_SERVER_YEOSKEER1, PUK2_SERVER_YEOSKEER1
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*1,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  PUK2_SERVER_FARKALT0, PUK2_SERVER_FARKALT1, PUK2_SERVER_FARKALT1
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*2,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  PUK2_SERVER_MILLIOTICE0, PUK2_SERVER_MILLIOTICE1, PUK2_SERVER_MILLIOTICE1
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*3,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  PUK2_SERVER_FINIA0, PUK2_SERVER_FINIA1, PUK2_SERVER_FINIA1
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*4,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  PUK2_SERVER_ERENOR0, PUK2_SERVER_ERENOR1, PUK2_SERVER_ERENOR1
	},
	{
		SSPUK2_SB_X,  SSPUK2_SB_Y+SSPUK2_SB_SY*5,
		SSPUK2_SB_SX, SSPUK2_SB_SY,  PUK2_SERVER_KAREN0, PUK2_SERVER_KAREN1, PUK2_SERVER_KAREN1
	}
};
#endif


void PUK2_titleProc( void )
{
	int		ret = 0;
	int		id, addNo;
	int		no;
	int		i;
	int		serverSelFlag = 0;
	static int line;
	static char msg[256];
	PUK2_BTN_INFO	*pserverNameBtn, *pExitBtn, *pBackBtn, *pLeftBtn, *pRightBtn;

#ifdef VERSION_TW
	PUK2_BTN_INFO	*pBackMainServerBtn, *pMainServerNameBtn;

	pMainServerNameBtn = TW_MainServerNameBtn;
	pserverNameBtn = TW_ServerNameBtn;

	pBackMainServerBtn = &TW_BackMainServerBtn;
#else

	// ボタンのポインタの设定
	pserverNameBtn = PUK2_serverNameBtn;
#endif
	pExitBtn = &PUK2_exitBtn;
	pBackBtn = &PUK2_backBtn;
	pLeftBtn = &PUK2_leftBtn;
	pRightBtn = &PUK2_rightBtn;

	// 初期化
	if( SubProcNo == 0 ){
		char str[128];
		SubProcNo++;
#ifndef _TAIKEN
		sprintf( str, "%s %s", DEF_APPNAME, NR_VERSION );                   //MLHIDE
#else
		sprintf( str, "%s %s [体验版]", DEF_APPNAME, NR_VERSION );             //MLHIDE
#endif
		SetWindowText( hWnd, str );
		networkDisconnectFlag = 0;
		ProduceInitFlag = TRUE;

		//selectServerPage = 0;
		line = 0;

		PaletteChange( 27, 0 );
		play_bgm( BGM_TITLE );
	}

	// サーバ选择处理
	if( SubProcNo == 1 ){
		serverSelFlag = 1;

#ifndef VERSION_TW
		// 表示に复数ページが必要？
		if( serverMaxNo >= SERVER_LINE_PAR_PAGE ){
			// １ページ目？
			if( selectServerPage == 0 ){
				// 右ボタン押したか？
				if( PUK2pushGraBtnInfo( pRightBtn ) & BTN_LEFT_CLICK){
					// ページ切り替え
					selectServerPage = 1;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}else
			// ２ページ目
			{
				// 左ボタン押したか？
				if( PUK2pushGraBtnInfo( pLeftBtn ) & BTN_LEFT_CLICK){
					// ページ切り替え
					selectServerPage = 0;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
		}
#endif

		// EXIT押したか？
		if( (PUK2pushGraBtnInfo( pExitBtn ) & BTN_LEFT_CLICK) ){
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
			// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
			PostMessage( hWnd, WM_CLOSE, 0, 0L );
		}else
		// BACK押したか？
		if( (PUK2pushGraBtnInfo( pBackBtn ) & BTN_LEFT_CLICK) ){
			ChangeProc2( PROC_ID_PASSWORD );
			// 白パレットに变更
			PaletteChange( 20, 1 );
			// 返回音
			play_se( SE_NO_BACK, 320, 240 );
			// ＢＧＭフェードアウト开始
			fade_out_bgm();
#ifdef VERSION_TW
		}else
		// 返回主服务器选择界面
		if ((PUK2pushGraBtnInfo(pBackMainServerBtn) & BTN_LEFT_CLICK)) {
			selectMainServer = -1;
			selectServerPage = 0;
			play_se(SE_NO_BACK, 320, 240);
		}
		else {
			for ( i = 0; i < MAINSERVER_MAX; i++ ) {
				if ((PUK2pushGraBtnInfo( &pMainServerNameBtn[i]) & BTN_LEFT_CLICK) && MainServerLineNum[i] > 0 && selectMainServer < 0) {
					selectMainServer = i;
					play_se(SE_NO_CLICK, 320, 240);
					for (int n = 0; n < SERVER_LINE_PAR_PAGE * 2; n++ ) {
						pserverNameBtn[n].graNo1 = pMainServerNameBtn[i].graNo2 + n*2 + 1;
						pserverNameBtn[n].graNo2 = pMainServerNameBtn[i].graNo2 + n*2 + 2;
					}
					break;
				}
			}

			for (i = 0; i < SERVER_LINE_PAR_PAGE; i++) {
				if ( selectMainServer >= 0) {
					addNo = ( selectMainServer * SERVER_LINE_PAR_PAGE * 2 ) + (selectServerPage * SERVER_LINE_PAR_PAGE ) ;
					int flg = PUK2pushGraBtnInfo( &pserverNameBtn[i] );

					if (flg & BTN_FOCUS_ON) {
						line = i;
					}

					if ((flg & BTN_LEFT_DBL_CLICK) && gmsv[addNo+ i].useFlag) {
						id = addNo + i;
						char title[256];

						sprintf(title, "%s %s [%s %s]", DEF_APPNAME, NR_VERSION, selectServerName[ id/2/10 ], selectServerNoStr[selectServerPage * SERVER_LINE_PAR_PAGE +i] ); //MLHIDE
						if( i == 9 )
							sprintf(title, ML_STRING(321, "%s %s [逆 %s]"), DEF_APPNAME, NR_VERSION, selectServerName[id / 2 / 10] );
						SetWindowText(hWnd, title);

						selectServerIndex = id;
						line = i;

						play_se(SE_NO_OK, 320, 240);

						getId(userId);
						getPassword(userPassword);
						getCdkey(userCdKey);
						SubProcNo++;
						break;
					}
				}
			}
		}

#else
		}else
		{
			id = -1;
			addNo = selectServerPage*SERVER_LINE_PAR_PAGE;
			for( i = 0; i < SERVER_LINE_PAR_PAGE; i++ ){
				int		flg = PUK2pushGraBtnInfo( &pserverNameBtn[addNo+i] );

				// フォーカスがあっていたらカーソル位置を设定
				if( flg & BTN_FOCUS_ON ){
					line = i;
				}

				if( ( flg & BTN_LEFT_CLICK ) && gmsv[addNo+i].useFlag ){
					// サーバ决定
					id = addNo + i;
					char title[256];
					
#ifdef _DEBUG
					if ( !strcmp(gmsv[id].ipaddr, "sound") ){                        //MLHIDE
						sprintf( title, "%s %s [soundcheck]", DEF_APPNAME, NR_VERSION ); //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_SE_TEST;
						offlineFlag = TRUE;
						return;
					}
					if ( !strcmp(gmsv[id].ipaddr, "g_id") ){                         //MLHIDE
						sprintf( title, "%s %s [graphic_IDview]", DEF_APPNAME, NR_VERSION ); //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_G_ID_VIEW;
						offlineFlag = TRUE;
						return;
					}
					if ( !strcmp(gmsv[id].ipaddr, "spr") ){                          //MLHIDE
						sprintf( title, "%s %s [sprview]", DEF_APPNAME, NR_VERSION );   //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_SPR_VIEW;
						offlineFlag = TRUE;
						return;
					}
					if ( !strcmp(gmsv[id].ipaddr, "anim") ){                         //MLHIDE
						sprintf( title, "%s %s [animview]", DEF_APPNAME, NR_VERSION );  //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_ANIM_VIEW;
						offlineFlag = TRUE;
						return;
					}
	#ifdef PUK3_BATTLECHECK_SERVER
					if ( !strcmp(gmsv[id].ipaddr, "battle") ){                       //MLHIDE
						sprintf( title, "%s %s [battlecheck]", DEF_APPNAME, NR_VERSION ); //MLHIDE
						SetWindowText( hWnd, title );
						SubProcNo=0;
	 					ProcNo = PROC_BATTLE;
	 					PaletteChange( 0, 0 );
						offlineFlag = TRUE;
						return;
					}
	#endif

					sprintf( title, "%s %s [%s  %s:%s]", DEF_APPNAME, NR_VERSION,    //MLHIDE
						selectServerName[id], gmsv[id].ipaddr, gmsv[id].port );
#else
					sprintf( title, "%s %s [%s]", DEF_APPNAME, NR_VERSION, selectServerName[id] ); //MLHIDE
#endif
					SetWindowText( hWnd, title );

					selectServerIndex = id;
					line = i;

					// 决定ａ
					play_se( SE_NO_OK, 320, 240 );

#if 0
					certifyIdPasswordServerIndex = selectServerIndex;
#endif
					getId( userId );
					getPassword( userPassword );
					getCdkey( userCdKey );
					SubProcNo++;
					break;
				}
			}
		}
#endif
	}

	// ゲームサーバへ接続
	if( SubProcNo == 2 ){
		// 初期化
		initConnecGameServer();
		SubProcNo++;
	}
	if( SubProcNo == 3 ){
		// 接続处理
		ret = connecGameServer();
		// 接続完了
		if( ret == 1 ){
			ChangeProc( PROC_CHAR_SELECT );
		}else
		// ネットワークの准备が出来てない
		if( ret == -1 ){
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_112 );
#else
			strcpy( msg, NET_ERRMSG_SOCKLIBERROR );
#endif
			// ネットワーク初期化
			cleanupNetwork();
		}else
		// サーバに接続できなかった
		if( ret == -2 ){
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			switch( Err_clientLogin ){
			case 1234:
				// 人数满タン
				strcpy( msg, INFOMSG_29 );
				break;
			case AUTH_ERROR_CDKEY:
				// ＣＤキー又は、ＩＤ，パスワードが违う
				strcpy( msg, INFOMSG_27 );
				break;
			case AUTH_ERROR_EXPIRE:
				// 制品权利を购入していない又は期限が切れています。
				strcpy( msg, INFOMSG_28 );
				break;
			default:
				// その他のエラー。
				strcpy( msg, ERRMSG_113 );
				break;
			}
#else
			switch( Err_clientLogin ){
			case 1234:
				// 人数满タン
				strcpy( msg, NET_ERRMSG_SERVER_BUSY );
				break;
			case AUTH_ERROR_CDKEY:
				// ＣＤキー又は、ＩＤ，パスワードが违う
				strcpy( msg, NET_ERRMSG_ERR_CDKEY );
				break;
			case AUTH_ERROR_EXPIRE:
				// 制品权利を购入していない又は期限が切れています。
				strcpy( msg, NET_ERRMSG_ERR_EXPIRE );
				break;
			default:
				// その他のエラー。
				strcpy( msg, NET_ERRMSG_NOTCONNECT );
				break;
			}
#endif
			// ネットワーク初期化
			cleanupNetwork();
		}else
		// ニューエストが无い时
		if( ret == -3 ){
			SubProcNo = 100;
#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_116 );
#else
			strcpy( msg, NET_ERRMSG_NEWEST_NON );
#endif
			// ネットワーク初期化
			cleanupNetwork();
		}
#ifdef PUK3_LOGIN_VERCHECK
		// クライアントのバージョン异常
		else if( ret == -4 ){
			SubProcNo = 100;
	#ifdef PUK3_ERRORMESSAGE_NUM
			strcpy( msg, ERRMSG_118 );
	#else
			strcpy( msg, NET_ERRMSG_CLIENTVER_DIFF );
	#endif
			// ネットワーク初期化
			cleanupNetwork();
		}
#endif
#ifdef PUK3_ERRORMESSAGE_NUM
		// ニューエストにサーバーが无い时
		else if( ret == -5 ){
			SubProcNo = 100;
			strcpy( msg, ERRMSG_117 );
			// ネットワーク初期化
			cleanupNetwork();
		}
#endif
	}

	// エラー信息
	if( SubProcNo == 100 ){
		initCommonMsgWin();
		SubProcNo++;
	}
	if( SubProcNo == 101 ){
		if( commonMsgWin( msg ) ){
			if( Err_clientLogin == AUTH_ERROR_CDKEY){
				// ＯＫボタンが押された
				strcpy( msg, ML_STRING(265, "请注意大小写和书号输入？") );
				initCommonMsgWin();
				SubProcNo = 102;
				
			}else{
				
				SubProcNo = 1;

				char str[128];
		
#ifndef _TAIKEN
				sprintf( str, "%s %s", DEF_APPNAME, NR_VERSION );                 //MLHIDE
#else
				sprintf( str, "%s %s [体验版]", DEF_APPNAME, NR_VERSION );           //MLHIDE
#endif
				SetWindowText( hWnd, str );
			}
		}
	}

	if( SubProcNo == 102){
		if( commonMsgWin( msg ) ){
			SubProcNo = 1;
		}

		char str[128];
		
#ifndef _TAIKEN
			sprintf( str, "%s %s", DEF_APPNAME, NR_VERSION );                  //MLHIDE
#else
			sprintf( str, "%s %s [体验版]", DEF_APPNAME, NR_VERSION );            //MLHIDE
#endif
			SetWindowText( hWnd, str );
	}


#ifdef VERSION_TW
	// EXITボタン表示
	PUK2drawGraBtnInfo(pExitBtn, DISP_PRIO_CHAR, 0, 0, 0);
	// BACKボタン表示
	PUK2drawGraBtnInfo(pBackBtn, DISP_PRIO_CHAR, 0, 0, 0);
	if (serverSelFlag) {

		for (i = 0; i < MAINSERVER_MAX; i++) {
			if (MainServerLineNum[i] > 0) {
				if ( selectMainServer == i ){
					PUK2drawGraBtnInfo( pBackMainServerBtn, DISP_PRIO_CHAR, 0, 0, 0);
				}
				else if ( selectMainServer < 0 ) {
					PUK2drawGraBtnInfo(&pMainServerNameBtn[i], DISP_PRIO_CHAR, 0, 0, 0);
				}
			}
		}

		if (selectMainServer >= 0 && MainServerLineNum[selectMainServer] > 10) {
			PUK2drawGraBtnInfo(pRightBtn, DISP_PRIO_CHAR, 0, 0, 0);
			PUK2drawGraBtnInfo(pLeftBtn, DISP_PRIO_CHAR, 0, 0, 0);

			if (PUK2pushGraBtnInfo(pRightBtn) & BTN_LEFT_CLICK) {
				if (selectServerPage < 1)
					++selectServerPage;
				play_se(SE_NO_CLICK, 320, 240);
			}

			if (PUK2pushGraBtnInfo(pLeftBtn) & BTN_LEFT_CLICK) {
				if (selectServerPage > 0)
					--selectServerPage;
				play_se(SE_NO_CLICK, 320, 240);
			}
		}

		no =  SERVER_LINE_PAR_PAGE * (selectServerPage + 2 * selectMainServer);
		for (i = 0; i < SERVER_LINE_PAR_PAGE; i++) {
			if ( selectMainServer >= 0 && gmsv[no + i].useFlag ) {
				if ( selectServerPage ) {
					if (selectServerPage == 1)
						PUK2drawGraBtnInfo( &pserverNameBtn[ i + 10 ], DISP_PRIO_CHAR, 0, 0, 0);
				}else {
					PUK2drawGraBtnInfo( &pserverNameBtn[i], DISP_PRIO_CHAR, 0, 0, 0);
				}
			}
		}
	}
	else
	{
		for (i = 0; i < MAINSERVER_MAX; i++) {
			if (MainServerLineNum[i] > 0) {
				if (selectMainServer == i) {
					PUK2drawGraBtnInfo(&pBackMainServerBtn[i], DISP_PRIO_CHAR, 0, 0, 0);
				}
				else if ( selectMainServer < 0 ) {
					PUK2drawGraBtnInfo( &pMainServerNameBtn[i], DISP_PRIO_CHAR, 0, 0, 0 );
				}
			}
		}

		no = SERVER_LINE_PAR_PAGE * (selectServerPage + 2 * selectMainServer);
		for (i = 0; i < SERVER_LINE_PAR_PAGE; i++) {
			if (gmsv[no + i].useFlag) {
				if (selectServerPage) {
					if (selectServerPage == 1) {
						if( i == line )
							PUK2drawGraBtnInfo( &pserverNameBtn[i + 10], DISP_PRIO_CHAR, 0, 0, 2);
						else
							PUK2drawGraBtnInfo( &pserverNameBtn[i + 10], DISP_PRIO_CHAR, 0, 0, 1);
					}
				}
				else {
					if (i == line)
						PUK2drawGraBtnInfo( &pserverNameBtn[i], DISP_PRIO_CHAR, 0, 0, 2);
					else
						PUK2drawGraBtnInfo( &pserverNameBtn[i], DISP_PRIO_CHAR, 0, 0, 1);
				}
			}
		}
	}

	getUsable3D();

#else
	// ページ表示
	if( serverMaxNo >= SERVER_LINE_PAR_PAGE ){
//		StockDispBuffer( 140, 380, DISP_PRIO_BG, V2_SERVER_SELECT_PAGE1+selectServerPage, 0 );
//		StockDispBuffer( 320, 240, DISP_PRIO_BG, V2_SERVER_SELECT_PAGE1+selectServerPage, 0 );
		if( !selectServerPage ){
			titleStockDisp( 187, 334, DISP_PRIO_BG+1, PUK2_SERVERPAGE1, 0, 0xffffffff );
			// 右ボタン表示
			PUK2drawGraBtnInfo( pRightBtn, DISP_PRIO_CHAR, 0, 0, 0 );
		}else{
			titleStockDisp( 187, 334, DISP_PRIO_BG+1, PUK2_SERVERPAGE2, 0, 0xffffffff );
			// 左ボタン表示
			PUK2drawGraBtnInfo( pLeftBtn, DISP_PRIO_CHAR, 0, 0, 0 );
		}
		titleStockDisp( 203, 334, DISP_PRIO_BG+1, PUK2_SERVERPAGE2, 0, 0xffffffff );
	}else{
		titleStockDisp( 187, 334, DISP_PRIO_BG+1, PUK2_SERVERPAGE1, 0, 0xffffffff );
		titleStockDisp( 203, 334, DISP_PRIO_BG+1, PUK2_SERVERPAGE1, 0, 0xffffffff );
	}

	// 鲭选择中？
	if( serverSelFlag ){
		// サーバ名ボタン表示
		for( i = 0; i < SERVER_LINE_PAR_PAGE; i++ ){
			no = selectServerPage*SERVER_LINE_PAR_PAGE+i;
			if( gmsv[no].useFlag ){
				PUK2drawGraBtnInfo( &pserverNameBtn[no], DISP_PRIO_CHAR, 0, 0, 0 );
			}
		}

		// EXITボタン表示
		PUK2drawGraBtnInfo( pExitBtn, DISP_PRIO_CHAR, 0, 0, 0 );
		// BACKボタン表示
		PUK2drawGraBtnInfo( pBackBtn, DISP_PRIO_CHAR, 0, 0, 0 );
	}else
	{
		// サーバ名ボタン表示
		for( i = 0; i < SERVER_LINE_PAR_PAGE; i++ ){
			no = selectServerPage*SERVER_LINE_PAR_PAGE+i;
			if( gmsv[no].useFlag ){
				if( i != line ){
					PUK2drawGraBtnInfo( &pserverNameBtn[selectServerPage*SERVER_LINE_PAR_PAGE+i],
						DISP_PRIO_CHAR, 0, 0, 1 );
				}else
				{
					PUK2drawGraBtnInfo( &pserverNameBtn[selectServerPage*SERVER_LINE_PAR_PAGE+i],
						DISP_PRIO_CHAR, 0, 0, 2 );
				}
			}
		}

		// EXITボタン表示
		PUK2drawGraBtnInfo( pExitBtn, DISP_PRIO_CHAR, 0, 0, 1 );
		// BACKボタン表示
		PUK2drawGraBtnInfo( pBackBtn, DISP_PRIO_CHAR, 0, 0, 1 );
	}

	/* カーソルを表示 */
	// 枠を表示
	titleStockDisp( SSPUK2_CUR_X, SSPUK2_CUR_Y+SSPUK2_SB_SY*line, DISP_PRIO_CHAR+1, PUK2_SERVERFRAME, 0, 0xffffffff );
	// 下地の表示（绘图设定によっては书き込まないように）
	if ( getUsable3D() ){
		titleStockDisp( SSPUK2_CUR_X, SSPUK2_CUR_Y+SSPUK2_SB_SY*line, DISP_PRIO_CHAR+1, PUK2_SERVERFRAMEB, 0, 0xa0ffffff );
	}
#endif

	/* タイトル背景表示 */
	// サーバー名の下地表示
	//根据窗口分辨率计算服务器列表背景的显示位置
	titleStockDisp( SymOffsetX + 49, SymOffsetY + 67, DISP_PRIO_BG, PUK2_SERVERBASE, 0, 0xffffffff );
	// ＢＧ表示
	titleStockDisp( 0, 0, DISP_PRIO_BG, PUK2_SERVERSELECT, 0, 0xffffffff );

	// バージョン表示
#ifdef PUK2_NEWVER
	StockFontBuffer( SymOffsetX + 460, SymOffsetY + 462, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
#else
#ifdef VERSION_TW
	//根据窗口分辨率计算服务器界面右下角版本号的显示位置
	//台服选择服务器界面右下角版本号位置
	StockFontBuffer( SymOffsetX + 540, SymOffsetY + 424, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0);
#else
	StockFontBuffer( SymOffsetX + 540, SymOffsetY + 462, FONT_PRIO_BACK, FONT_PAL_WHITE, NR_VERSION, 0 );
#endif
#endif
}

