﻿#include <stdio.h>
#include <winsock.h>
#include <time.h>
#include <WININET.H>

#include "../systeminc/system.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/netmain.h"
#include "../systeminc/netproc.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/savedata.h"
#include "../systeminc/process.h"
#include "../systeminc/pc.h"
#include "../systeminc/tool.h"
#include "../systeminc/map.h"
#include "../systeminc/character.h"
#include "../systeminc/action.h"
#include "../systeminc/battlemenu.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/menu.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/login.h"
#include "../systeminc/handletime.h"
#include "../systeminc/field.h"
#include "../systeminc/t_music.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/math2.h"
#include"../systeminc/main.h"
#include"../systeminc/mapEffect.h"
#include"../systeminc/keyboard.h"
#include"../systeminc/battle.h"
#include"../systeminc/battleMaster.h"
#include"../systeminc/sndcnf.h"
#include"../systeminc/debug.h"
#include"../systeminc/mouse.h"
#include "../systeminc/dmctrl.h"

#ifdef PUK2
#include "../systeminc/gamemain.h"
#include "../systeminc/guild.h"
#include"../puk2/map/newmap.h"
#include"../puk2/newdraw/graphic_ID.h"
#include"../puk2/interface/menuwin.h"
void SetSendPetNameWorkChangeFlag(void);
#endif

#ifdef PUK3
#ifdef PUK3_PROF
#include "../puk3/profile/profile.h"
#endif
#endif

#ifdef _DEBUG
//void appendLogMessage( char *msg );
#endif


BOOL SetGuildMemberStatus(int SetNum,char *data);
int GetGuildMemberCount(void);
void GuildMemberClear(GUILD_MEMBER_INFO	*member);

//-------------------------------------------------------------------------//
// 定数定义                                                                //
//-------------------------------------------------------------------------//
#define NETPROC_NOTSEND     0           // 何かを送信するまでこれ
#define NETPROC_SENDING     1           // 送信している途中
#define NETPROC_RECEIVED    2           // 受信できた

#define DELIMITER	'|'					// 区切り文字


//-------------------------------------------------------------------------//
// 处理の定义                                                              //
//-------------------------------------------------------------------------//
#define SETSENDING  netproc_sending = NETPROC_SENDING;start_time=GetTickCount();

#define SETTIMEOUT( msg )  \
	if( ( GetTickCount() - start_time ) > TIMEOUT )	\
	{												\
		sprintf( netprocErrmsg, msg );				\
		return -1;									\
	}


//-------------------------------------------------------------------------//
// グローバル变数定义                                                      //
//-------------------------------------------------------------------------//
// 右クリックのスキルスワップを行って良いかどうかのフラグ
// このフラグが立ってると、スキル顺番入れ替え中
short SkillSwapFlg = 0;

// 选择したサーバのインデックス（StoneAgeの场合は0か1）
short selectServerIndex = -1;
// ログイン状态(1...登入中)
short clientLoginStatus = 0;
// ログイン失败理由
short Err_clientLogin = 0;
// キャラリスト状态(1...取得できた)
short charListStatus = 0;
// ユーザＩＤ??パスワード
char userId[64];
char userPassword[64];
char userCdKey[64];
// キャラ削除の状态
short charDelStatus = 0;
// キャラ作成の状态
short newCharStatus = 0;
// キャラクタログインの状态
short charLoginStatus = 0;
// キャラクタログイン时のエラーコード
short charLoginErrCode;
// キャラクタログイン时のエラー信息
char *charLoginErrMsg[] =
{
	"登入成功(这条信息已经不使用了)",                                                  //MLHIDE
	"登入失败。"                                                              //MLHIDE
};
int charLoginErrCodeMax = sizeof( charLoginErrMsg )/sizeof( charLoginErrMsg[0] );
// キャラクタ登出の状态
short charLogoutStatus = 0;



// GAMESTATE_CHOOSESERVERのために设定しなければならない情报。
char gamestate_chooseserver_name[128];
// GAMESTATE_LOGINのために设定しなければならない情报。
char gamestate_login_charname[128];
// GAMESTATE_DELETECHARのために设定しなければならない情报。
char gamestate_deletechar_charname[128];

// エラー信息
char netprocErrmsg[1024];

int netproc_sending = NETPROC_NOTSEND; 
DWORD start_time = 0;               // 何でも处理をはじめた时间。 TIMEOUTまで

BYTE RecvVerData = 128;

//-------------------------------------------------------------------------//
// システム系プロトコル                                                    //
//-------------------------------------------------------------------------//


//-------------------------------------------------------------------------//
// サーバー接続处理

// 初期化
int connectServerCounter = 0;

void initConnectServer( void )
{
	connectServerCounter = 0;
}


// 接続处理
//
//  实行前に selectServerIndex 、userId 、userPassword を设定しておく
//
//  戾り值：	 0 ... 处理中
//				 1 ... 接続完了
//				-1 ... タイムアウト
//				-2 ... 指定されたサーバ名が无い(普通はこれはおこらない)。
//				-3 ... ソケット作成に失败した。
//				-4 ... IPアドレスの变换に失败した。
//				-5 ... connectに失败した。
//				-6 ... selectに失败した。
//				-7 ... クライアントログインに失败した。
int connectServer( void )
{
	// 初期化が完了してなければ何もしない
	if( !networkFlag )
		return 0;

	if( connectServerCounter == 0 )
	{
		// 接続をはじめた时间を记忆
		start_time = GetTickCount();

		// ソケットはノンブロッキングになってるので、その状态でconnectする
		char hostname[128];
		short pt;
		if( getServerInfo( selectServerIndex, hostname, &pt ) < 0 )
		{
			sprintf( netprocErrmsg, NET_ERRMSG_BADNAME );
			//connectServerCounter = 0;
			return -2;
		}

        // socketつくru
		sockfd = socket( AF_INET , SOCK_STREAM ,0 );
		if( sockfd == INVALID_SOCKET )
		{
			sprintf( netprocErrmsg, NET_ERRMSG_SOCKETERROR );
			//connectServerCounter = 0;
			return -3;
		}

        // ノンブロッキングにする
#if 1
		unsigned long flg = 1;
		ioctlsocket( sockfd, FIONBIO, &flg );
#endif
		// NoDelay の场合は、setsockoptする。 by ringo
		extern BOOL NoDelay;
		if( NoDelay ){
			int flag = 1;
			if( setsockopt( sockfd, IPPROTO_TCP, TCP_NODELAY, (char*) &flag, sizeof(int)) != 0 ){
				//MessageBox( NULL, "fuck", "ringo", MB_OK );
				return -100;
			}
		}
#if 0
#ifdef _DEBUG
		{
			extern int CheckNetErrror( void );
			unsigned char time = 1;
			int len = 1;
			char moji[256];
			// 受信のタイムアウト设定
			if( getsockopt( sockfd, IPPROTO_TCP, SO_RCVTIMEO, (char*) &time,  &len ) == SOCKET_ERROR ){
				CheckNetErrror();
				//return -100;
			}
			sprintf( moji, "小时：%u", time );                                    //MLHIDE
			MessageBox( NULL, moji, "ohta", MB_OK );                           //MLHIDE
		}
#endif
#endif


		// アドレスを设定
		struct sockaddr_in sin;
		struct hostent *h;

		sin.sin_family = AF_INET;
		sin.sin_port = htons( pt );
		sin.sin_addr.s_addr = inet_addr( hostname );     /* accept only dot notaion  */
		if( sin.sin_addr.s_addr == -1 )
		{
			h = gethostbyname( hostname );
			if( h == NULL )
			{
				sprintf( netprocErrmsg, NET_ERRMSG_NOTGETADDR, hostname );
				closesocket( sockfd ); 
				//connectServerCounter = 0;
				return -4;
			}
			memcpy( (void*) &sin.sin_addr.s_addr , h->h_addr, sizeof( struct in_addr ) );
		}

		// Non blocking Connect
		int ret = connect( sockfd, (struct sockaddr*)&sin , sizeof( sin ) );
		if( ret == SOCKET_ERROR )
		{
			char szBuffer[256];
			// 直前のエラーを取得。
			int error = WSAGetLastError();

			if( error == WSAEWOULDBLOCK )
			{
				// 本来はブロックするはずだったんやけど
            }
			else
			{
				// ログに吐き出す。
				sprintf( szBuffer, "CONNECT ERROR=%d", error );                   //MLHIDE
				WriteSystemLogfile( szBuffer );
				closesocket( sockfd );
				sprintf( netprocErrmsg, NET_ERRMSG_NOTCONNECT_S );
				return -5;
			}
		}else{

		}

		// ここまでできたらカウンタを1にする
		connectServerCounter = 1;
	}
	else
	if( connectServerCounter >= 1 && connectServerCounter <= 70 )
	{
		connectServerCounter++;
		if( connectServerCounter == 70 )
		{
			connectServerCounter = 69;
		}
		// selectする
		fd_set rfds , wfds , efds;
		struct timeval tm;
		tm.tv_sec = 0;
		tm.tv_usec = 0;
		FD_ZERO( &rfds );
		FD_ZERO( &wfds );
		FD_ZERO( &efds );
		FD_SET( sockfd , &rfds );
		FD_SET( sockfd , &wfds );
		FD_SET( sockfd , &efds );
		int a = select(  2 , &rfds , &wfds , &efds,  &tm );
		if( FD_ISSET( sockfd, &wfds ) )
		{
			connectServerCounter = 71;
			// これ以降はネットワークルーチンが生きる。そのためのフラグ立て
			networkServerChooseFlag = 1;
			// ログに吐き出す。
			WriteSystemLogfile( "CONNECT OK" );                                //MLHIDE
		}
		if( FD_ISSET( sockfd , &efds ) )
		{
			sprintf( netprocErrmsg, NET_ERRMSG_NOTCONNECT );
			closesocket( sockfd ); 
			//connectServerCounter = 0;
			return -6; 
		}
    }
	else
	if( connectServerCounter >= 71 && connectServerCounter <= 80 )
	{
		// ClientLoginする
		if( connectServerCounter == 71 )
		{
			clientLoginStatus = 0;	// ログイン状态をOFFにする
			nrproto_ClientLogin_send( sockfd, userId, userPassword, userCdKey );
			netproc_sending = NETPROC_SENDING;
		}
		if( netproc_sending == NETPROC_RECEIVED )
		{
			//  recv しおわった
			if( clientLoginStatus )
			{
				connectServerCounter = 81;
			}
			else
			{
				netproc_sending = NETPROC_NOTSEND;
				sprintf( netprocErrmsg, NET_ERRMSG_LOGINFAIL );
				closesocket( sockfd ); 
				return -7;
			}
		}

		connectServerCounter++;
		if( connectServerCounter == 81 )
			connectServerCounter = 80;
    }
	else
	if( connectServerCounter >= 81 && connectServerCounter <= 98 )
	{
		// まわすだけ
		connectServerCounter ++;
    }
	else
	if( connectServerCounter == 99 )
	{
		//ChangeProc( PROC_CHAR_SELECT );

		// また送信していないモードに戾す。何回してもいいように
		netproc_sending = NETPROC_NOTSEND;
		//connectServerCounter = 0;
		return 1;
	}

	// 制限时间がすぎたら、エラー状态に移行、そのあとエラー終了
	SETTIMEOUT( NET_ERRMSG_CONNECTTIMEOUT );

	return 0;
}




//-------------------------------------------------------------------------//
// クライアントログイン处理

// ログイン受信处理
void nrproto_ClientLogin_recv( int fd, int result, char *data )
{
	if( netproc_sending == NETPROC_SENDING )
	{
		netproc_sending = NETPROC_RECEIVED;	

		Err_clientLogin = result;	// エラーコードを保存する。
		if( result == 0 )
		{
			clientLoginStatus = 1;
			// 现在の日时を取得
			time( &serverAliveLongTime );
			serverAliveTime = localtime( &serverAliveLongTime );
		}
		else
		// 人数满タン
		if( strcmp( data, BUSYSTR ) == 0 ){
			Err_clientLogin = 1234;
		}

	}
}




//-------------------------------------------------------------------------//
// キャラリスト取得处理

// キャラリストプロトコル送信
void charListStart( void )
{
	int i;

	for( i = 0; i < MAXCHARACTER; i++ )
	{
		resetCharacterList( i );
	}

	charListStatus = 0;

	nrproto_CharList_send( sockfd );

	SETSENDING;
}


// 受信待ち
//   戾り值：  0 ... 受信待ち
//             1 ... 受信完了
//            -1 ... タイムアウト
//            -2 ... エラーが归ってきた
//            -3 ... メンテナンス中
//            -4 ... ユーザ认证失败
int charListProc( void )
{
	if( netproc_sending == NETPROC_RECEIVED )
	{
		netproc_sending = NETPROC_NOTSEND;
		if( charListStatus == 1 )
		{
			return 1;
		}
		else
		if( charListStatus == 2 )
		{
			return -3;
		}
#if 0
		else
		if( charListStatus == 3 )
		{
			return -4;
		}
#endif
		else
		{
			return -2;
		}
	}

	SETTIMEOUT( NET_ERRMSG_CHARLISTTIMEOUT );

	return 0;
}


// サーバーに保存されているキャラクタのリストの受信
void nrproto_CharList_recv( int fd, int result, char *data )
{
	if( netproc_sending == NETPROC_SENDING )
	{
		netproc_sending = NETPROC_RECEIVED;

		if( result != 0 )
		{
			if( strcmp( data, "OUTOFSERVICE" ) == 0 )                          //MLHIDE
			{
				// 现在メンテナンス中
				charListStatus = 2;
			}
#if 0
			else
			if( strcmp( data, "???????" ) == 0 )                               //MLHIDE
			{
				// ユーザ认证失败
				charListStatus = 3;
			}
#endif
			return;
		}

		charListStatus = 1;

		// 实际に情报をセットする
		char nm[1024], opt[1024];
		int i;
		for( i=0; i < MAXCHARACTER; i++ )
		{
			strcpy( nm ,"" );
			strcpy( opt , "" );
			getStringToken( data, DELIMITER, i*2+1, sizeof( nm )-1 , nm );
			getStringToken( data, DELIMITER, i*2+2, sizeof( opt )-1, opt );

			setCharacterList( nm , opt );
		}
	}
}




//-------------------------------------------------------------------------//
// キャラログイン处理

// ログイン开始
void charLoginStart( void )
{
	charLoginStatus = 0;

	// リニューアル变更フラグつき。
#ifdef PUK2
	nrproto_CharLogin_send( sockfd, chartable[selectPcNo].registnumber, 
		chartable[selectPcNo].isRenewal, chartable[selectPcNo].faceGraNo );
#else
	nrproto_CharLogin_send( sockfd, chartable[selectPcNo].registnumber, 
		chartable[selectPcNo].isRenewal, 0 );
#endif

	SETSENDING;
}


// 登入中
//   戾り值： 0 ... 受信待ち / 1 ... 受信完了 / -1 ... タイムアウト / -2 ... エラーが归ってきた
int charLoginProc( void )
{
	if( !networkServerChooseFlag )
		return 0;

	if( netproc_sending == NETPROC_RECEIVED )
	{
		netproc_sending = NETPROC_NOTSEND;
		if( charLoginStatus == 1 )
		{
			return 1;
		}
		else
		{
			return -2;
		}
	}

	SETTIMEOUT( NET_ERRMSG_LOGINTIMEOUT );

	return 0;
}


// キャラログイン受信
void nrproto_CharLogin_recv( int fd, char* result, char* data )
{
	if( netproc_sending == NETPROC_SENDING )
	{
		netproc_sending = NETPROC_RECEIVED;
#if 1
		if( strcmp( result, SUCCESSFULSTR ) == 0 )
		{
			charLoginStatus = 1;
        }
#else
		charLoginErrCode = atoi( result );
		// ログイン成功
		if( charLoginErrCode == 0 )
		{
			charLoginStatus = 1;
		}
#endif
	}
}




//-------------------------------------------------------------------------//
// 登出处理

// 登出开始
void charLogoutStart( void )
{
	charLogoutStatus = 0;

	nrproto_CharLogout_send( sockfd );

	SETSENDING;
}


// 登出中
//   戾り值：	 0 ... 登出中
//				 1 ... 登出完了
#ifdef PUK2
//				 2 ... 强制登出完了
#endif
//				-1 ... タイムアウト
//				-2 ... エラーが归ってきた
int charLogoutProc( void )
{
	if( netproc_sending == NETPROC_RECEIVED )
	{
		netproc_sending = NETPROC_NOTSEND;
		if( charLogoutStatus == 1 )
		{
			return 1;
		}
#ifdef PUK2
		else if( charLogoutStatus == 2 )
		{
			return 2;
		}
#endif
		else
		{
			return -2;
		}
    }

	SETTIMEOUT( NET_ERRMSG_LOGOUTTIMEOUT );

	return 0;
}


// 登出受信
void nrproto_CharLogout_recv( int fd, char *result, char *data )
{
	if( netproc_sending == NETPROC_SENDING )
	{
		netproc_sending = NETPROC_RECEIVED;
		if( strcmp( result, SUCCESSFULSTR ) == 0 )
		{
			charLogoutStatus = 1;
		}
	}
#ifdef PUK2
	else{
		if( strcmp( result, SUCCESSFULSTR ) == 0 )
		{
			netproc_sending = NETPROC_RECEIVED;
			charLogoutStatus = 2;

			GameState = GAME_LOGIN;
			ChangeProc2( PROC_CHAR_LOGOUT );

			// 登出音
			play_se( SE_NO_LOGOUT, 320, 240 );
		}
	}
#endif
}




//-------------------------------------------------------------------------//
// 新キャラ作成处理

// 作成したキャラデータをサーバに送る
void createNewCharStart( void )
{
	newCharStatus = 0;

	// サーバで保存位置が固定になったらこっちにする
	nrproto_CreateNewChar_send( sockfd, selectPcNo, newCharacterName,
		newCharacterGraNo, newCharacterFaceGraNo,
		newCharacterVit, newCharacterStr,
		newCharacterTgh, newCharacterQui, newCharacterMgc,
		newCharacterEarth, newCharacterWater, newCharacterFire, newCharacterWind );

	SETSENDING;
}


// 新キャラ登録完了待ち
//   戾り值： 0 ... 受信待ち / 1 ... 受信完了 / -1 ... タイムアウト / -2 ... エラーが归ってきた
int createNewCharProc( void )
{
	if( netproc_sending == NETPROC_RECEIVED )
	{
		netproc_sending = NETPROC_NOTSEND;
		if( newCharStatus == 1 )
		{
			return 1;
		}
		else
		{
			return -2;
		}
    }

	SETTIMEOUT( NET_ERRMSG_CREATECHARTIMEOUT );

	return 0;
}

extern int giCreateRenewalFlg;	// リニューアルページかどうか

// 新キャラ登録完了受信
void nrproto_CreateNewChar_recv( int fd, char *result, int registnumber, char *data ) 
{
	if( netproc_sending == NETPROC_SENDING )
	{
		netproc_sending = NETPROC_RECEIVED;

		if( strcmp( result, SUCCESSFULSTR ) == 0 )
		{
			chartable[selectPcNo].registnumber = registnumber;      // キャラ登録番号 
			newCharStatus = 1;
			chartable[selectPcNo].isRenewal = giCreateRenewalFlg;   // リニューアルかどうか
		}
    }
}




//-------------------------------------------------------------------------//
// キャラ削除处理

// 削除キャラ番号送信
void delCharStart( void )
{
	charDelStatus = 0;

	nrproto_CharDelete_send( sockfd, chartable[selectPcNo].registnumber );

	SETSENDING;
}


// キャラ削除待ち
//   戾り值： 0 ... 削除待ち / 1 ... 削除完了 / -1 ... タイムアウト / -2 ... エラーが归ってきた
int delCharProc( void )
{
	if( netproc_sending == NETPROC_RECEIVED )
	{
		netproc_sending = NETPROC_NOTSEND;
		if( charDelStatus )
		{
			return 1;
		}
		else
		{
			return -2;
		}
	}

	SETTIMEOUT( NET_ERRMSG_DELETECHARTIMEOUT );

	return 0;
}


// キャラ削除完了受信
void nrproto_CharDelete_recv( int fd, char *result, char *data )
{
	if( netproc_sending == NETPROC_SENDING )
	{
		netproc_sending = NETPROC_RECEIVED;
		if( strcmp( result, SUCCESSFULSTR ) == 0 )
		{
			charDelStatus = 1;
    	}
    }
}




//-------------------------------------------------------------------------//
// サーバとのコネクションを継続するために
// 一定间隔でEchoを送ってその返事を受信する
void nrproto_Echo_recv( int fd, char *test )
{
#if 1
#ifdef _DEBUG_MSG

	// 现在の日时を取得
	time( &serverAliveLongTime );
	serverAliveTime = localtime( &serverAliveLongTime );

#endif
#endif
}




//-------------------------------------------------------------------------//
// サーバ内部データの受信处理
void nrproto_ProcGet_recv( int fd, char *data )
{
}




//-------------------------------------------------------------------------//
// プレイヤー数の受信处理
void nrproto_PlayerNumGet_recv( int fd, int logincount, int player )
{
}




//-------------------------------------------------------------------------//
// マップ??位置情报系プロトコル                                            //
//-------------------------------------------------------------------------//

//-------------------------------------------------------------------------//
// あるエリアのチェックサム情报を受信
//   チェックサムが违うならサーバにマップデータを要求

void nrproto_MC_recv( int fd, int id, int fl, int x1, int y1, int x2, int y2,
	int tileSum, int partsSum, int eventSum )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

#ifdef DEBUGPUSH
    char msg[800];
    sprintf( msg , "地形のチェックサムをうけとった。FL%d %d,%d-%d,%d (%ud/%ud)",      //MLHIDE
		fl,x1,y1,x2,y2, tileSum, partsSum );
	StockChatBufferLine( msg, FONT_PAL_RED );
#endif

	if( mapCheckSum( id, fl, x1, y1, x2, y2, tileSum, partsSum, eventSum ) )
	{
		if( mapNo == fl )
		{
			// チェックサム同じ时の处理
			floorChangeFlag = FALSE;
		//	warpEffectStart = FALSE; // ohta
		}

		// ログイン时にマップを読み込むようにする
		if( (loginFlag & LOGIN_FLAG_WAIT_FOR_MAPDATA) )
		{
			redrawMap();
			loginFlag &= ~LOGIN_FLAG_WAIT_FOR_MAPDATA;
		}
	}
}




//-------------------------------------------------------------------------//
// マップデータを受信
//   そのままファイルに书き込む
void nrproto_M_recv( int fd, int id, int fl, int x1, int y1, int x2, int y2, char *data )
{
	char tilestring[18192];
	char partsstring[18192];
	char eventstring[18192];
	unsigned short tile[2048] , parts[2048], event[2048];
	int i;
	char tmp[100];
	int flag;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

#if DEBUGPUSH
    char msg[800];
    sprintf( msg , "サーバーから地形をうけとった。FL%d %d,%d-%d,%d" , fl,x1,y1,x2,y2 ); //MLHIDE
	StockChatBufferLine( msg, FONT_PAL_RED );
#endif

	getStringToken( data, DELIMITER, 1, sizeof( tilestring )-1, tilestring );	
	getStringToken( data, DELIMITER, 2, sizeof( partsstring )-1, partsstring );
	getStringToken( data, DELIMITER, 3, sizeof( eventstring )-1, eventstring );

	for( i = 0; ; i++ ){
		flag = getStringToken( tilestring, ',', i+1, sizeof( tmp )-1, tmp );
		tile[i] = a62toi( tmp );

		getStringToken( partsstring, ',', i+1, sizeof( tmp )-1, tmp );
		parts[i] = a62toi( tmp );

		getStringToken( eventstring, ',', i+1, sizeof( tmp )-1, tmp );
		event[i] = a62toi( tmp );

		if( flag == 1 )
			break;
	}

#ifndef _DEBUG
	writeMap( id, fl, x1, y1, x2, y2, tile, parts, event );
#else
	if( writeMap( id, fl, x1, y1, x2, y2, tile, parts, event ) < 0 ){
		MessageBox( hWnd, "マップ書きこみエラー", "确认", MB_OK | MB_ICONSTOP );        //MLHIDE
	}
#endif

	#ifdef MAP_DATA_EMPTY_PROC
	{
		MAP_AREA_INFO info;

		info.mapMode = id;
		info.mapNo = fl;
		info.areaX1 = x1;
		info.areaY1 = y1;
		info.areaX2 = x2;
		info.areaY2 = y2;

		delMapDataEmptyArea( &info );
	}
	#endif

	// マップが空の时データがきたらマップを読み込むようにする
	if( mapEmptyFlag || floorChangeFlag ){
		if( mapNo == fl ){
			redrawMap();
			floorChangeFlag = FALSE;
		//	warpEffectStart = FALSE; // ohta
		}
	}

	// マップデータ待ちフラグを降ろす
	loginFlag &= ~LOGIN_FLAG_WAIT_FOR_MAPDATA;

	// 现在のマップ情报ならすぐに読み込む
	if( mapKind == id && mapNo == fl
	 && (mapGx-10 <= x1 && x2 <= mapGx+10 && mapGy-10 <= y1 && y2 <= mapGy+10) ){
		mapInsideFlag = 1;
	}
	
	// マップ要求中の时	// ohta
	if( warpMcFlag == TRUE ){
		// ワープ先のフロアＩＤのマップデータを受信した时
		if( mapNo == fl ){
			// ワープ时データ待ち終了
			warpEffectStart = FALSE;
			warpMcFlag = FALSE;
			warpMnFlag = FALSE;
		}
	}
}




//-------------------------------------------------------------------------//
// 战闘終了直前にＰＣの座标と向きを受信
void nrproto_XYD_recv( int fd, int x, int y, int dir )
{
	updateMapAreaFromMapPos();
	setPcWarpPoint( x, y );
	setPcPoint();
	setPcDir( dir );
}



//-------------------------------------------------------------------------//
// キャラのマップ位置
#ifdef PUK3_CONNDOWNWALK
void nrproto_CC_recv( int fd, int mapid, int floor, int maxx, int maxy, int x, int y,
	int cfgid, int seqno, int var, int bgm, int cut, int warpid )
#else
void nrproto_CC_recv( int fd, int mapid, int floor, int maxx, int maxy, int x, int y,
	int cfgid, int seqno, int var, int bgm, int cut )
#endif
{
	// フロア变更フラグ(マップを読みこむように)
	floorChangeFlag = TRUE;
	if( !loginFlag && ProcNo == PROC_GAME )	// ログイン时は实行しない
	{
		// ワープイベントですでに实行されていれば何もしない。
		if( !warpEffectFlag )
		{
			if( autoMapOpenFlag == FALSE ){	// ohta
				autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
			}
			// ワープ演出实行
			SubProcNo = 200;
			// 现时点の画面を作る
			warpEffectProc();
		}

		// PCキャラリセット
		resetPc();
		warpEffectFlag = FALSE;
	//	warpEffectStart = TRUE;	// ohta
	}


	// 新しいマップならファイルを作る
#ifndef _DEBUG
	createMap( mapServerNo, mapid, floor, maxx, maxy, cfgid, seqno );
#else
	if( createMap( mapServerNo, mapid, floor, maxx, maxy, cfgid, seqno ) < 0 )
	{
		MessageBox( hWnd, "新規マップファイル作成エラー", "确认", MB_OK | MB_ICONSTOP );    //MLHIDE
	}
#endif

	// マップ位置设定
	setMapAreaInfo( mapid, floor, maxx, maxy, cfgid, seqno, var, cut );
	setWarpMap( x, y );

	// キャラ管理テーブルリセット
	resetCharObj();

	// 空マップフラグ
	mapEmptyFlag = FALSE;

	// エンカウント率の初期化
	nowEncountPercentage = minEncountPercentage;
	nowEncountExtra = 0;

	resetMap();

#ifdef TRUE

	if(bgm>0){
		recvBgmNo = bgm;
		recvBgmNoFlag = TRUE;	// recvBgmNoは有效g
	}

#else

#ifdef PUK2
	recvBgmNo = bgm;
#endif
	// サーバからのBGM番号を有效にする
	if( recvBgmNo >= 0 )
	{
		recvBgmNoFlag = TRUE;	// recvBgmNoは有效g
	}

#endif
#ifdef PUK3_CONNDOWNWALK
	mapWarpId = warpid;
#endif

}


// マップとパレット情报
void nrproto_MN_recv( int fd, char *str )
{

	char mapName[256];
	char strPal[32];

	makeStringFromEscaped( str );

	// マップ名处理
	getStringToken( str, DELIMITER, 1, sizeof( mapName )-1, mapName );

	makeRecvString( mapName );

	if( strlen( mapName ) <= MAP_NAME_LEN ){
		strcpy( ::mapName, mapName );
	}else{
		strcpy( ::mapName, "???" );                                         //MLHIDE
	}

	// パレット变更の处理
	palNo = -2;
	getStringToken( str, DELIMITER, 2, sizeof( strPal )-1, strPal );
	if( strPal[0] == '\0' ){
		//if( nrTimeZoneNo != PalState.palNo || loginFlag )
	//	if( loginFlag )
	//	{
			// パレットチェンジ
			palNo = -1;
	//	}
	}else{
		int pal;

		pal = atoi( strPal );
		if( pal >= 0 ){
			//if( pal != PalState.palNo || loginFlag )
	//		if( loginFlag )
	//		{
				// 固定パレット设定
				palNo = pal;
				//PaletteChange( pal, 0 );
	//		}
		}else{
			//if( pal != PalState.palNo || loginFlag )
	//		if( loginFlag )
	//		{
				// パレットチェンジ
				palNo = -1;
	//		}
		}
	}
	
	// イベントワープ中だったら	// ohta
	if( warpEffectStart == TRUE ){
		// マップ名受信济みフラグＯＮ
		warpMnFlag = TRUE;
	}
	
}



//-------------------------------------------------------------------------//
// キャラクタ情报系プロトコル                                              //
//-------------------------------------------------------------------------//

#if 1
void nrproto_S_recv( int fd, char *data )
{
}
#else
#endif



//-------------------------------------------------------------------------//
// ＰＣのパラメータ受信
void nrproto_CP_recv( int fd, char *data )
{
	unsigned int bitField;		// 各要素のビット情报
	unsigned int bitMask;		// 取り出すビット位置
	int pickUpId;				// data文字列から取り出す要素の位置番号
	char name[256];
	char freeName[256];

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	pickUpId = 1;
	bitMask = (1 << 1);

	// 各要素のビット情报を取り出す
	// （このビット情报で取り出す要素が决まる。
	//   0bit目が1なら全パラメータを取り出す。）
	bitField = getInteger62Token( data, DELIMITER, pickUpId );
	pickUpId++;

	// Life Point
	if( bitField & (1 | bitMask) )
	{
		pc.lp				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// MAX Life Point
	if( bitField & (1 | bitMask) )
	{
		pc.maxLp			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// Force Point
	if( bitField & (1 | bitMask) )
	{
		pc.fp				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// MAX Force Point
	if( bitField & (1 | bitMask) )
	{
		pc.maxFp			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 体力
	if( bitField & (1 | bitMask) )
	{
		pc.vtl				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 腕力
	if( bitField & (1 | bitMask) )
	{
		pc.str				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 防御力
	if( bitField & (1 | bitMask) )
	{
		pc.tgh				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 敏捷力
	if( bitField & (1 | bitMask) )
	{
		pc.qui				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 魔法力
	if( bitField & (1 | bitMask) )
	{
		pc.mgc				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// スタミナ
	if( bitField & (1 | bitMask) )
	{
		pc.stm				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 器用さ
	if( bitField & (1 | bitMask) )
	{
		pc.dex				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 知性
	if( bitField & (1 | bitMask) )
	{
		pc.inte				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 经验值
	if( bitField & (1 | bitMask) )
	{
		pc.exp				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 必要经验值
	if( bitField & (1 | bitMask) )
	{
		pc.nextExp			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 等级
	if( bitField & (1 | bitMask) )
	{
		pc.lv				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 攻击值
	if( bitField & (1 | bitMask) )
	{
		pc.atk				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 防御值
	if( bitField & (1 | bitMask) )
	{
		pc.def				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 敏捷值
	if( bitField & (1 | bitMask) )
	{
		pc.agi				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 精神值
	if( bitField & (1 | bitMask) )
	{
		pc.mnd				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 回复力
	if( bitField & (1 | bitMask) )
	{
		pc.rcv				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 魅力
	if( bitField & (1 | bitMask) )
	{
		pc.chm				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 名声
	if( bitField & (1 | bitMask) )
	{
		pc.fme				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 地属性
	if( bitField & (1 | bitMask) )
	{
		pc.attr[0]			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 水属性
	if( bitField & (1 | bitMask) )
	{
		pc.attr[1]			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 火属性
	if( bitField & (1 | bitMask) )
	{
		pc.attr[2]			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 风属性
	if( bitField & (1 | bitMask) )
	{
		pc.attr[3]			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// お金
	if( bitField & (1 | bitMask) )
	{
		pc.gold				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 称号のインデックス番号
	if( bitField & (1 | bitMask) )
	{
		pc.titleId			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
#if 0
	// デュエルポイント
	if( bitField & (1 | bitMask) )
	{
		pc.dp				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
#endif
	// 名称
	if( bitField & (1 | bitMask) )
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( name )-1, name );
		makeRecvString( name );
		if( strlen( name ) <= CHAR_NAME_LEN )
			strcpy( pc.name, name );
		pickUpId++;
	}
	bitMask <<= 1;
	// 自己称号
	if( bitField & (1 | bitMask) )
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( freeName )-1, freeName );
		makeRecvString( freeName );
		if( strlen( freeName ) <= CHAR_FREENAME_LEN )
			strcpy( pc.freeName, freeName );
		pickUpId++;
	}
	bitMask <<= 1;
}


// ＰＣのパラメータ受信２
void nrproto_CP2_recv( int fd, char *data )
{
	unsigned int bitField;		// 各要素のビット情报
	unsigned int bitMask;		// 取り出すビット位置
	int pickUpId;				// data文字列から取り出す要素の位置番号

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;


	pickUpId = 1;
	bitMask = (1 << 1);

	// 各要素のビット情报を取り出す
	// （このビット情报で取り出す要素が决まる。
	//   0bit目が1なら全パラメータを取り出す。）
	bitField = getInteger62Token( data, DELIMITER, pickUpId );
	pickUpId++;

	// クリティカル修正值
	if( bitField & (1 | bitMask) )
	{
		pc.cri				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// カウンター修正值
	if( bitField & (1 | bitMask) )
	{
		pc.ctr				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 命中修正值
	if( bitField & (1 | bitMask) )
	{
		pc.hit				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 回避修正值
	if( bitField & (1 | bitMask) )
	{
		pc.avd				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 毒修正值
	if( bitField & (1 | bitMask) )
	{
		pc.poi				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 眠り修正值
	if( bitField & (1 | bitMask) )
	{
		pc.slp				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 石化修正值
	if( bitField & (1 | bitMask) )
	{
		pc.stn				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 酔い修正值
	if( bitField & (1 | bitMask) )
	{
		pc.itx				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 混乱修正值
	if( bitField & (1 | bitMask) )
	{
		pc.cnf				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 丧失修正值
	if( bitField & (1 | bitMask) )
	{
		pc.amn				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// デュエルポイント
	if( bitField & (1 | bitMask) )
	{
		pc.dp				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 怪我等级
	if( bitField & (1 | bitMask) )
	{
		pc.injuryLv			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 种族
	if( bitField & (1 | bitMask) )
	{
		pc.tribe			= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 気絶ポイント
	if( bitField & (1 | bitMask) )
	{
		pc.stunPoint		= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
#ifdef PUK2
	// リバース等级
	if( bitField & (1 | bitMask) )
	{
		pc.rebirthLevel		= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 颜グラフィック
	if( bitField & (1 | bitMask) )
	{
		pc.faceGraNo		= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// スキルスロット数
	if( bitField & (1 | bitMask) )
	{
		pc.skillSlot		= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;

#ifdef PUK3_RIDE		// ライド移动テスト
	// 移动速度
	if( bitField & (1 | bitMask) )
	{
		pc.walkSpeed		= getIntegerToken( data, DELIMITER, pickUpId );

		if( pc.walkSpeed <= 0 ){
			pc.walkSpeed = 100;
		}
		if (pc.ptAct) pc.ptAct->walkSpeed = pc.walkSpeed;

		pickUpId++;
	}
	bitMask <<= 1;
#endif

#ifdef VERSION_TW
	//台服客户端新增的魔攻和魔防
	if (bitField & (1 | bitMask))
	{
		pc.adm = getIntegerToken(data, DELIMITER, pickUpId);
		pickUpId++;
	}
	bitMask <<= 1;
	if (bitField & (1 | bitMask))
	{
		pc.rss = getIntegerToken(data, DELIMITER, pickUpId);
		pickUpId++;
	}
	bitMask <<= 1;
#endif
#endif
}




//-------------------------------------------------------------------------//
// ＰＣの职业情报受信
void nrproto_CJ_recv( int fd, int jobassort, char *jobname )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	
	job.kind = jobassort;
	makeRecvString( jobname );
	if( strlen( jobname ) < JOB_NAME_LEN )
	{
		strcpy( job.name, jobname );
	}
	else
	{
		strcpy( job.name, "???" );                                          //MLHIDE
	}
}


//-------------------------------------------------------------------------//
// ＰＣのスキル情报受信
void nrproto_CS_recv( int fd, char *data )
{
	int skillIndex;			// スキル区分
	char skillName[256];	// スキルの名称
	int skillLv;			// スキルの等级
	int skillExp;			// スキルの经验值
	int maxSkillLv;			// スキルの最大等级
	int nextSkillExp;		// 次の等级に必要な经验值
	int slot;				// スキルの必要スロット
	int operationCategory;	// オペレーション分类ー
	int id;					// スキルＩＤ
	int fpRate;				// 魔力消费率
	int hisId;				// 履历番号
#ifdef PUK2_JOB_CHANGE
	int usableSkillLv;		// 使用可能なスキル等级
#endif
	int i;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;
	i = 0;
	while( 1 )
	{
		skillIndex        = getIntegerToken( data, DELIMITER, i+1 );
		id                = getIntegerToken( data, DELIMITER, i+2 );
		getStringToken( data, DELIMITER, i+3, sizeof( skillName )-1, skillName );
		makeRecvString( skillName );
		skillLv           = getIntegerToken( data, DELIMITER, i+4 );
		skillExp          = getIntegerToken( data, DELIMITER, i+5 );
		maxSkillLv        = getIntegerToken( data, DELIMITER, i+6 );
		nextSkillExp      = getIntegerToken( data, DELIMITER, i+7 );
		slot              = getIntegerToken( data, DELIMITER, i+8 );
		operationCategory = getIntegerToken( data, DELIMITER, i+9 );
		fpRate            = getIntegerToken( data, DELIMITER, i+10 );
		hisId             = getIntegerToken( data, DELIMITER, i+11 );
#ifdef PUK2_JOB_CHANGE
		usableSkillLv     = getIntegerToken( data, DELIMITER, i+12 );
#endif

		// 終了チェック
		if( skillIndex < 0 )
			break;

		// 名称以降のパラメータがない时はバッファをクリアする
		if( strlen( skillName ) == 0 && skillLv < 0 && skillExp < 0 )
		{
			job.skill[skillIndex].name[0] = '\0';
			job.skill[skillIndex].lv      = 0;
			job.skill[skillIndex].exp     = 0;
			memset( job.skill[skillIndex].tech, 0,
				sizeof( job.skill[skillIndex].tech ) );
			clearRecipe( skillIndex );
		}
		else
		{
			// 技能名の设定
			if( strlen( skillName ) <= SKILL_NAME_LEN )
			{
				strcpy( job.skill[skillIndex].name, skillName );
			}
			else
			{
				strcpy( job.skill[skillIndex].name, "???" );                      //MLHIDE
			}
#if 0
			// 技能说明の设定
			if( strlen( skillMemo ) <= SKILL_MEMO_LEN )
			{
				strcpy( job.skill[skillIndex].memo, skillMemo );
			}
			else
			{
				strcpy( job.skill[skillIndex].memo, "???" );                      //MLHIDE
			}
#endif
			job.skill[skillIndex].lv = skillLv;
			job.skill[skillIndex].exp = skillExp;
			job.skill[skillIndex].maxLv = maxSkillLv;
			job.skill[skillIndex].nextExp = nextSkillExp;
			job.skill[skillIndex].slot = slot;
			job.skill[skillIndex].operationCategory = operationCategory;
			job.skill[skillIndex].id = id;
			job.skill[skillIndex].fpRate = fpRate;
			job.skill[skillIndex].hisId = hisId;
#ifdef PUK2_JOB_CHANGE
			job.skill[skillIndex].usableLv = usableSkillLv;
#endif
			if( operationCategory == 1 )
			{
#ifdef PUK2
				// 胜手にトランス
//				nrproto_IR_send( sockfd, skillIndex + 100 );
				nrproto_IR_send( sockfd, skillIndex + (skillrebirth?100:0) );
#else
				nrproto_IR_send( sockfd, skillIndex );
#endif
			}
		}

#ifdef PUK2_JOB_CHANGE
		i += 12;
#else
		i += 11;
#endif
	}

	skillSort( &job.skill[0], &job.sortSkill[0], MAX_SKILL );
	// スキルプロトコルを受けました。右クリックのソートを认めます。
	SkillSwapFlg = 0;
	
}


//-------------------------------------------------------------------------//
// ＰＣの技情报受信
void nrproto_CT_recv( int fd, int skillindex, char *data )
{
	char techName[256];			// 技の名称
	char techMemo[256];			// 技の说明
	int useFp;					// 必要フォースポイント
	int field;					// 使用可能な场所??时
	int target;					// 使用对象
	int usableFlag;				// 使用できるかフラグ
	int i, j;
#ifdef PUK2
	int techlv;
#endif

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// バッファクリア
	memset( &job.skill[skillindex].tech[0], 0, sizeof( job.skill[skillindex].tech ) );

	i = 0;
	j = 0;
	while( 1 )
	{
		if( getStringToken( data, DELIMITER, i+1, sizeof( techName )-1, techName ) == 1 )
			break;
		makeRecvString( techName );
		getStringToken( data, DELIMITER, i+2, sizeof( techMemo )-1, techMemo );
		makeRecvString( techMemo );
		useFp = getIntegerToken( data, DELIMITER, i+3 );
		field = getIntegerToken( data, DELIMITER, i+4 );
		target = getIntegerToken( data, DELIMITER, i+5 );
		usableFlag = getIntegerToken( data, DELIMITER, i+6 );

		// 名称がない时はパラメータを初期化する
		if( strlen( techName ) == 0 )
		{
#if 0
			job.skill[skillindex].tech[j].name[0] = '\0';
			job.skill[skillindex].tech[j].memo[0] = '\0';
			job.skill[skillindex].tech[j].fp = 0;
			job.skill[skillindex].tech[j].useFlag = 0;
#endif
			continue;
		}

		// 使用フラグを立てる
		job.skill[skillindex].tech[j].useFlag = 1;

		// 技の名称を设定
		if( strlen( techName ) <= TECH_NAME_LEN )
		{
			strcpy( job.skill[skillindex].tech[j].name, techName );
		}
		else
		{
			strcpy( job.skill[skillindex].tech[j].name, "???" );               //MLHIDE
		}
		// 技の说明を设定
		if( strlen( techMemo ) <= TECH_MEMO_LEN )
		{
			strcpy( job.skill[skillindex].tech[j].memo, techMemo );
		}
		else
		{
			strcpy( job.skill[skillindex].tech[j].memo, "???" );               //MLHIDE
		}
		// 技の必要FPを设定
		job.skill[skillindex].tech[j].fp = useFp;
		job.skill[skillindex].tech[j].field = field;
#ifdef PUK2
		techlv = (target >> 12) & 0x0f;
		target &= 0xfff;
		job.skill[skillindex].tech[j].techlv = techlv;
#endif
		job.skill[skillindex].tech[j].target = target;
		job.skill[skillindex].tech[j].usableFlag = usableFlag;

		i += 6;
		j++;
	}
}



//-------------------------------------------------------------------------//
// ＰＣの称号情报受信
void nrproto_TITLE_recv( int fd, char *data )
{
	int id,titleid;
	char title[256];
	int i, j;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	for( i = 0, j = 0; i < CHAR_TITLE_MAX; i++ )
	{
		getStringToken( data, DELIMITER, j+1, sizeof( title )-1, title );
		makeRecvString( title );

		id = getIntegerToken( data, DELIMITER, j+2 );
		if( id >= 0 ){
			charTitleInfo[i].id = id;
			strcpy( charTitleInfo[i].title, title );

			titleid = getIntegerToken( data, DELIMITER, j+3 );
// どっか适当なバッファに插入すべし
#ifdef PUK2
			charTitleInfo[i].titleid = titleid;
#endif
		}else{
			charTitleInfo[i].id = -1;
			charTitleInfo[i].title[0] = '\0';
#ifdef PUK2
			charTitleInfo[i].titleid = -1;
#endif
		}

		j += 3;
	}

	// ソートバッファにコピー
	charTitleCopy( &sortCharTitleInfo[0], &charTitleInfo[0], CHAR_TITLE_MAX );
	// 情报をソート
	charTitleSort( &sortCharTitleInfo[0], CHAR_TITLE_MAX );
}



//-------------------------------------------------------------------------//
// 仲间情报
void nrproto_CN_recv( int fd, int no, char *data )
{
	ACTION *ptAct;
	int bitField;
	int index;
	int gx, gy;
	int i;
	int no2;
	int checkPartyCount;
	char name[256];

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	bitField = getInteger62Token( data, DELIMITER, 1 );

	// 仲间情报がない时
	if( bitField == 0 )
	{
		// さっきまで仲间がいたら后处理して終了
		if( party[no].useFlag != 0 && party[no].id != pc.id )
		{
			ptAct = getCharObjAct( party[no].id );
			if( ptAct != NULL )
			{
				delCharParty( ptAct );
			}
		}

		// 拔けるキャラの座标を求める
		gx = -1;
		gy = -1;
		if( party[no].ptAct != NULL )
		{
			gx = party[no].ptAct->nextGx;
			gy = party[no].ptAct->nextGy;
		}

		// 布ティー情报から抹消
		party[no].useFlag	= 0;
		party[no].ptAct		= NULL;

		// 残りの布ティーのキャラ数を调べる
		// ついでに拔けたキャラ以降のキャラの布ティー番号を调べる
		checkPartyCount = 0;
		no2 = -1;
		for( i = 0; i < MAX_PARTY; i++ )
		{
			if( party[i].useFlag != 0 )
			{
				checkPartyCount++;
				if( no2 == -1 && i > no )
				{
					no2 = i;
				}
			}
		}

		// 布ティーが１人なら解散
		if( checkPartyCount <= 1 )
		{
			partyModeFlag = 0;
			clearPartyParam();
		}
		else
		{
			// 谁かが拔けてまだ后ろにキャラがいたら前に诘める
#ifdef PUK3_PARTY_INOUT
			if( no2 >= 0 && ( gx >= 0 || gy >= 0 ) )
#else
			if( no2 >= 0 || gx >= 0 || gy >= 0 )
#endif
			{
				goFrontPartyCharacter( no2, gx, gy );
			}
		}
		return;
	}

	// 布ティが增えたので情报设定
	partyModeFlag = 1;
	prSendFlag = 0;		// たまにPRの返事がこないのでここでフラグを下ろす。

	// 布ティーに登録
	party[no].useFlag	= 1;

	// パラメータ取り出し布ティー情报バッファに设定
	index = 2;
	if( bitField & (1 | (1<<1)) )
	{
		party[no].id		= getIntegerToken( data, DELIMITER, index );
		index++;
	}
	if( bitField & (1 | (1<<2)) )
	{
		party[no].lv		= getIntegerToken( data, DELIMITER, index );
		index++;
	}
	if( bitField & (1 | (1<<3)) )
	{
		party[no].maxLp		= getIntegerToken( data, DELIMITER, index );
		index++;
	}
	if( bitField & (1 | (1<<4)) )
	{
		party[no].lp		= getIntegerToken( data, DELIMITER, index );
		index++;
	}
	if( bitField & (1 | (1<<5)) )
	{
		party[no].fp		= getIntegerToken( data, DELIMITER, index );
		index++;
	}
	if( bitField & (1 | (1<<6)) )
	{
		getStringToken( data, DELIMITER, index, sizeof( name )-1, name );
		makeRecvString( name );
		if( strlen( name ) <= sizeof( party[no].name )-1 )
		{
			strcpy( party[no].name, name );
		}
		else
		{
			strcpy( party[no].name, "???" );                                   //MLHIDE
		}
	}

	// 仲间に入ったのが他のキャラの时
	if( party[no].id != pc.id )
	{
		ptAct = getCharObjAct( party[no].id );
		if( ptAct != NULL )
		{
			party[no].ptAct = ptAct;
			setCharParty( ptAct );
			// NPCがリーダーの时
			if( no == 0 )
			{
				// リーダーマーク表示
				setCharLeader( ptAct );
			}
		}
		else
		{
			party[no].ptAct = NULL;
		}
	}
	else
	// 自分の情报の时
	{
		party[no].ptAct = pc.ptAct;
		setPcParty();

		// PCがリーダーの时
		if( no == 0 )
		{
			// リーダーマーク表示
			setPcLeader();
		}
	}
}



//-------------------------------------------------------------------------//
// ペットの情报を受信
void nrproto_KP_recv( int fd, int number, char *data )
{
	unsigned int bitField;		// 各要素のビット情报
	unsigned int bitMask;		// 取り出すビット位置
	int pickUpId;				// data文字列から取り出す要素の位置番号
	char name[256];
	char freeName[256];

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// エラーチェック
	if( number < 0 || MAX_PET <= number )
		return;

	pickUpId = 1;
	bitMask = (1 << 1);

	// 各要素のビット情报を取り出す
	// （このビット情报で取り出す要素が决まる。
	//   0bit目が1なら全パラメータを取り出す。）
	bitField = getInteger62Token( data, DELIMITER, pickUpId );
	pickUpId++;

	// ペットいない时はバッファをクリアして終わる
	if( bitField == 0 )
	{
		memset( &pet[number], 0, sizeof( PET ) );
	}
	else
	{
		// ohta
	//	memset( &pet[number], 0, sizeof( PET ) );
		
		// ペットがいるのでパラメータ取り出し
		pet[number].useFlag = 1;

		// 画像番号
		if( bitField & (1 | bitMask) )
		{

#ifdef _CG2_NEWGRAPHIC
			pet[number].graNo	= getNewGraphicNo( getIntegerToken( data, DELIMITER, pickUpId ));
#else
			pet[number].graNo	= getIntegerToken( data, DELIMITER, pickUpId );
#endif

			pickUpId++;
		}
		bitMask <<= 1;
		// 种族
		if( bitField & (1 | bitMask) )
		{
			pet[number].tribe	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// Life Point
		if( bitField & (1 | bitMask) )
		{
			pet[number].lp		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// MAX Life Point
		if( bitField & (1 | bitMask) )
		{
			pet[number].maxLp	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// Force Point
		if( bitField & (1 | bitMask) )
		{
			pet[number].fp		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// MAX Force Point
		if( bitField & (1 | bitMask) )
		{
			pet[number].maxFp	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 经验值
		if( bitField & (1 | bitMask) )
		{
			pet[number].exp		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 必要经验值
		if( bitField & (1 | bitMask) )
		{
			pet[number].nextExp	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 等级
		if( bitField & (1 | bitMask) )
		{
			pet[number].lv		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 攻击值
		if( bitField & (1 | bitMask) )
		{
			pet[number].atk		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 防御值
		if( bitField & (1 | bitMask) )
		{
			pet[number].def		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 敏捷值
		if( bitField & (1 | bitMask) )
		{
			pet[number].agi		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 精神值
		if( bitField & (1 | bitMask) )
		{
			pet[number].mnd		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 回复值
		if( bitField & (1 | bitMask) )
		{
			pet[number].rcv		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 忠诚值
		if( bitField & (1 | bitMask) )
		{
			pet[number].hmg		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 地属性
		if( bitField & (1 | bitMask) )
		{
			pet[number].attr[0]	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 水属性
		if( bitField & (1 | bitMask) )
		{
			pet[number].attr[1]	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 火属性
		if( bitField & (1 | bitMask) )
		{
			pet[number].attr[2]	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 风属性
		if( bitField & (1 | bitMask) )
		{
			pet[number].attr[3]	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 最大スキル数
		if( bitField & (1 | bitMask) )
		{
			pet[number].maxTech = getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 名称变更许可フラグ
		if( bitField & (1 | bitMask) )
		{
			pet[number].changeNameFlag	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 名称
		if( bitField & (1 | bitMask) )
		{
			getStringToken( data, DELIMITER, pickUpId, sizeof( name )-1, name );
			makeRecvString( name );
			if( strlen( name ) <= CHAR_NAME_LEN )
				strcpy( pet[number].name, name );
			pickUpId++;
		}
		bitMask <<= 1;
		// 自己称号
		if( bitField & (1 | bitMask) )
		{
			getStringToken( data, DELIMITER, pickUpId, sizeof( freeName )-1, freeName );
			makeRecvString( freeName );
			if( strlen( freeName ) <= CHAR_FREENAME_LEN )
				strcpy( pet[number].freeName, freeName );
			pickUpId++;
		}
		bitMask <<= 1;
		// 战闘の参加状态
		if( bitField & (1 | bitMask) )
		{
			short BattleSettingWork;

			BattleSettingWork = getIntegerToken( data, DELIMITER, pickUpId );
#ifdef PUK2
			if(BattleSettingWork & PET_SETTING_WALK){
				BattleSettingWork &= ~PET_SETTING_WALK;
				pet[number].battleSetting = BattleSettingWork;
				pet[number].Walk=1;
			}else{
				pet[number].battleSetting = BattleSettingWork;
				pet[number].Walk=0;
			}
#endif

			pickUpId++;
		}
		bitMask <<= 1;
		// 怪我等级
		if( bitField & (1 | bitMask) )
		{
			pet[number].injuryLv = getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
	}

	// 现在いるペットの数を数える
	calcPetCnt();
	// ソート
	petSort( &pet[0], &sortPet[0], MAX_PET );	

#ifdef PUK2
	SetSendPetNameWorkChangeFlag();
#endif

}


// ペットの情报２を受信
void nrproto_KP2_recv( int fd, int number, char *data )
{
	unsigned int bitField;		// 各要素のビット情报
	unsigned int bitMask;		// 取り出すビット位置
	int pickUpId;				// data文字列から取り出す要素の位置番号
//	char name[256];
//	char freeName[256];

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// エラーチェック
	if( number < 0 || MAX_PET <= number )
		return;

	pickUpId = 1;
	bitMask = (1 << 1);

	// 各要素のビット情报を取り出す
	// （このビット情报で取り出す要素が决まる。
	//   0bit目が1なら全パラメータを取り出す。）
	bitField = getInteger62Token( data, DELIMITER, pickUpId );
	pickUpId++;

	// ペットいない时はバッファをクリアして終わる
	if( bitField == 0 )
	{
		pet[number].useFlag = 0;
		pet[number].name[0] = '\0';
		pet[number].freeName[0] = '\0';
	}
	else
	{
		// ペットがいるのでパラメータ取り出し
		pet[number].useFlag = 1;

		// 体力(Vit)
		if( bitField & (1 | bitMask) )
		{
			pet[number].vit		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 攻击力(Str)
		if( bitField & (1 | bitMask) )
		{
			pet[number].str		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 防御力(Tgh)
		if( bitField & (1 | bitMask) )
		{
			pet[number].tgh		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 敏捷力(Qui)
		if( bitField & (1 | bitMask) )
		{
			pet[number].qui		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 魔法力(Mgc)
		if( bitField & (1 | bitMask) )
		{
			pet[number].mgc		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// クリティカル修正值
		if( bitField & (1 | bitMask) )
		{
			pet[number].cri		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// カウンタ修正值
		if( bitField & (1 | bitMask) )
		{
			pet[number].ctr		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 命中修正值
		if( bitField & (1 | bitMask) )
		{
			pet[number].hit		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 回避修正值
		if( bitField & (1 | bitMask) )
		{
			pet[number].avd		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 毒抵抗值
		if( bitField & (1 | bitMask) )
		{
			pet[number].poi		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 眠り抵抗值
		if( bitField & (1 | bitMask) )
		{
			pet[number].slp		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 石化抵抗值
		if( bitField & (1 | bitMask) )
		{
			pet[number].stn		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 酔い抵抗值
		if( bitField & (1 | bitMask) )
		{
			pet[number].itx		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 混乱抵抗值
		if( bitField & (1 | bitMask) )
		{
			pet[number].cnf		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 丧失抵抗值
		if( bitField & (1 | bitMask) )
		{
			pet[number].amn		= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
		// 履历番号
		if( bitField & (1 | bitMask) )
		{
			pet[number].hisId	= getIntegerToken( data, DELIMITER, pickUpId );
			pickUpId++;
		}
		bitMask <<= 1;
	}

	// 现在いるペットの数を数える
	calcPetCnt();
	// ソート
	petSort( &pet[0], &sortPet[0], MAX_PET );

#ifdef PUK2
	SetSendPetNameWorkChangeFlag();
#endif

}

#ifdef PUK2
//-------------------------------------------------------------------------//
// 布ティメンバーのパラメータを受信
void nrproto_PP_recv( int fd, char *data )
{
	int		i;
	char	token[2048];

	int		no;
	int		objindex;
	int		lp,maxlp,fp,maxfp;
	ACTION	*ptAct;

	// 最大布ティ人数分までパラメータを取得
	for( i = 0 ; i < 5 ; i++ ){
		no = i * 5 + 1;

		getStringToken( data, DELIMITER,  no + 0, sizeof( token )-1, token );
		if( strlen( token ) == 0 ){
			break;
		}
		objindex = a62toi( token );
		getStringToken( data, DELIMITER,  no + 1, sizeof( token )-1, token );
		lp = atoi( token );
		getStringToken( data, DELIMITER,  no + 2, sizeof( token )-1, token );
		maxlp = atoi( token );
		getStringToken( data, DELIMITER,  no + 3, sizeof( token )-1, token );
		fp = atoi( token );
		getStringToken( data, DELIMITER,  no + 4, sizeof( token )-1, token );
		maxfp = atoi( token );

		// 对应するキャラクタを检索
		ptAct = getCharObjAct( objindex );
		if( ptAct != NULL ){
			// パラメータを设定
			ptAct->hp = lp;
			ptAct->maxHp = maxlp;
			ptAct->fp = fp;
			ptAct->maxFp = maxfp;
		}
	}

}
#endif

//-------------------------------------------------------------------------//
// ペットの技情报を受信
void nrproto_PT_recv( int fd, int petindex, char *data )
{
	char techName[256];			// 技の名称
	char techMemo[256];			// 技の说明
	int useFp;					// 必要フォースポイント
	int field;					// 使用可能な场所??时
	int target;					// 使用对象
	int techIndex;				// 格纳位置
	int techId;					// 技ＩＤ
	int hisId;					// 履历番号
	int i;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// ペット位置が变か、ペットがいない时は終わる
	if( petindex < 0 || MAX_PET <= petindex
	 || pet[petindex].useFlag == 0 )
		return;
	
	// 受け取る前にペットテック构造体を初期化しておく ohta
	memset( &pet[petindex].tech[0], 0, sizeof( pet[petindex].tech ) );
	
	i = 0;
	while( 1 )
	{
		techIndex = getIntegerToken( data, DELIMITER, i+1 );

		// データが終わりなのでループ拔ける
		if( techIndex < 0 )
			break;

		techId = getIntegerToken( data, DELIMITER, i+2 );
		getStringToken( data, DELIMITER, i+3, sizeof( techName )-1, techName );
		makeRecvString( techName );
		getStringToken( data, DELIMITER, i+4, sizeof( techMemo )-1, techMemo );
		makeRecvString( techMemo );
		useFp = getIntegerToken( data, DELIMITER, i+5 );
		field = getIntegerToken( data, DELIMITER, i+6 );
		target = getIntegerToken( data, DELIMITER, i+7 );
		hisId = getIntegerToken( data, DELIMITER, i+8 );

		// 名称がない时はパラメータを初期化する
		if( strlen( techName ) == 0 )
		{
			pet[petindex].tech[techIndex].name[0] = '\0';
			pet[petindex].tech[techIndex].memo[0] = '\0';
			pet[petindex].tech[techIndex].fp = 0;
			pet[petindex].tech[techIndex].useFlag = 0;
			continue;
		}

		// 使用フラグ立てる
		pet[petindex].tech[techIndex].useFlag = 1;

		pet[petindex].tech[techIndex].techId = techId;
		// 技の名称を设定
		if( strlen( techName ) <= TECH_NAME_LEN )
		{
			strcpy( pet[petindex].tech[techIndex].name, techName );
		}
		else
		{
			strcpy( pet[petindex].tech[techIndex].name, "???" );               //MLHIDE
		}
		// 技の说明を设定
		if( strlen( techMemo ) <= TECH_MEMO_LEN )
		{
			strcpy( pet[petindex].tech[techIndex].memo, techMemo );
		}
		else
		{
			strcpy( pet[petindex].tech[techIndex].memo, "???" );               //MLHIDE
		}
		// 技の必要FPを设定
		pet[petindex].tech[techIndex].fp = useFp;
		pet[petindex].tech[techIndex].field = field;
		pet[petindex].tech[techIndex].target = target;
		pet[petindex].tech[techIndex].hisId = hisId;

		i += 8;
	}

	petTechSort( &pet[petindex].tech[0], &pet[petindex].sortTech[0], MAX_PET_TECH );
}



//-------------------------------------------------------------------------//
// キャラの情报を受信
void nrproto_C_recv( int fd, char *data )
{
	int i, j;
	char bigtoken[2048];
	char smalltoken[2048];

	int id;
	int x;
	int y;
	int dir;
	int graNo;
	int level;
	int nameColor;
	char name[256];
	char freeName[256];
	char title[256];
	int walkable;
	int height;
	int classNo;
	int popupNameColor;
	char info[1024];
	int money;
	int num;
	int charType;
	int iRet;
	ACTION *ptAct;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag )
	{
		return;
	}

	for( i = 0; ; i++ )
	{
		// パラメータ无くなったら終わる
		iRet = getStringToken( data, '\t', i+1, sizeof( bigtoken )-1, bigtoken );
//		if( strlen( bigtoken ) == 0 )break;
		if( strlen( bigtoken ) == 0 && iRet == 1 )break;

		// 11个目のトークンがあったらそれはキャラ。
		getStringToken( bigtoken , DELIMITER , 11 , sizeof( smalltoken )-1, smalltoken );
		if( strlen( smalltoken ) > 0 )
		{
			// NPCの情报
			charType = getIntegerToken( bigtoken, DELIMITER, 1 );
			getStringToken( bigtoken, DELIMITER,  2, sizeof( smalltoken )-1, smalltoken );
			id = a62toi( smalltoken );
			getStringToken( bigtoken, DELIMITER,  3, sizeof( smalltoken )-1, smalltoken );
			x = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER,  4, sizeof( smalltoken )-1, smalltoken );
			y = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER,  5, sizeof( smalltoken )-1, smalltoken );
			dir = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER,  6, sizeof( smalltoken )-1, smalltoken );
			graNo = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER,  7, sizeof( smalltoken )-1,smalltoken );
			level = atoi( smalltoken );
			nameColor = getIntegerToken( bigtoken, DELIMITER, 8 );
			getStringToken( bigtoken, DELIMITER,  9, sizeof( name )-1, name );
			makeRecvString( name );
			getStringToken( bigtoken, DELIMITER, 10, sizeof( freeName )-1, freeName );
#ifdef PUK2
			makeStringFromEscaped( freeName );
#else
			makeRecvString( freeName );
#endif
			getStringToken( bigtoken, DELIMITER, 11, sizeof( smalltoken )-1, smalltoken );
			walkable = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER, 12, sizeof( smalltoken )-1, smalltoken );
			height = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER, 13, sizeof( smalltoken )-1, smalltoken );
			popupNameColor = atoi( smalltoken );
			getStringToken( bigtoken, DELIMITER, 14, sizeof( title )-1, title );
			makeRecvString( title );

			if( pc.id == id )
			{
#ifdef _CG2_NEWGRAPHIC
				//新グラフィック番号を决めるＣｇＶ２、ＥＸ用
				graNo = getNewGraphicNo( graNo);
#endif
#ifdef PUK3_LOGIN_DIR_0
				// ＰＣのデータがまだなら方向を设定する
				if ( pc.dir < 0 ) pc.dir = dir;
#endif
				// PCキャラ	
				if( pc.ptAct == NULL )
				{
					// アクション无かったら作る
					createPc( graNo, x, y, dir );
					updataPcAct();

				}
				else
				{
#if 0
					setPcGraNo( graNo, dir );
					setPcMovePoint( x, y );
#else
					// すでにアクションある时は、
					// X,Y,DIRは无视する
					setPcGraNo( graNo, pc.dir );
#endif
				}

#ifdef PUK2
				// ＰＣのアクションのポインタをとっておく
				ptAct = pc.ptAct;
#endif
				// ここでPCパラメータ待ちフラグを降ろす
				loginFlag &= ~LOGIN_FLAG_WAIT_FOR_PC_PARAM;

				updateMapAreaFromMapPos();
				setPcParam( name, freeName, level, nameColor, walkable, height );
				if( (pc.status & CHR_STATUS_LEADER) != 0
				 && party[0].useFlag != 0 )
				{
					party[0].lv = pc.lv;
					strcpy( party[0].name, pc.name );
				}
				// 仲间情报があったらアクションポインタを更新
				for( j = 0; j < MAX_PARTY; j++ )
				{
					if( party[j].useFlag != 0 && party[j].id == id )
					{
						party[j].ptAct = pc.ptAct;
						setPcParty();
						// PCがリーダーの时
						if( j == 0 )
						{
							setPcLeader();
						}
						break;
					}
				}
			}
			else
			{
#ifdef _CG2_NEWGRAPHIC
				//新グラフィック番号を决めるＣｇＶ２、ＥＸ用
				graNo = getNewGraphicNo(graNo);
#endif
				// NPC
				setNpcCharObj( id, graNo, x, y, dir, name, freeName, title,
					level, nameColor, walkable, height, charType );
				ptAct = getCharObjAct( id );
				if( ptAct != NULL )
				{
					// 仲间情报があったらアクションポインタを更新
					for( j = 0; j < MAX_PARTY; j++ )
					{
						if( party[j].useFlag != 0 && party[j].id == id )
						{
							party[j].ptAct = ptAct;
							setCharParty( ptAct );
							// NPCがリーダーの时
							if( j == 0 )
							{
								setCharLeader( ptAct );
							}
							break;
						}
					}
				}
			}

#ifdef DEBUGPUSH
//			PUSHVAR( "Charindex %d　img %dのキャラをだせとサーバーがいった。", id, graNo );
#endif

#ifdef PUK2
	#ifdef PUK3_CHECK_VALUE
			CHAREXTRA *pYobi = NULL;
			if ( ptAct ) pYobi = ( CHAREXTRA *)ptAct->pYobi;
	#else
			CHAREXTRA *pYobi = ( CHAREXTRA *)ptAct->pYobi;
	#endif

#ifdef VERSION_TW
			//台服新增了2个参数,作用未知,暂时补0
			getStringToken(bigtoken, DELIMITER, 17, sizeof(smalltoken) - 1, smalltoken);
#else
			// 15个目のトークンがあったらそのキャラはペット连れ歩き中
			getStringToken( bigtoken , DELIMITER , 15 , sizeof( smalltoken )-1, smalltoken );
#endif
			if( strlen( smalltoken ) > 0 ){
#ifdef PUK2_NEW_PETTAKE
				int tokenNo;

#ifdef VERSION_TW
				//台服新增了2个参数,作用未知,暂时补0
				tokenNo = RecvCharaDataEx(ptAct, bigtoken, 17);
#else
				tokenNo = RecvCharaDataEx( ptAct, bigtoken, 15 );
#endif
#else
				int pGraNo;
				int pDir,pDir2;
				int pLv;
				int pCol;
				char pName[256];

				getStringToken( bigtoken, DELIMITER, 15, sizeof( smalltoken )-1, smalltoken );
#ifdef PUK2
				pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
				pGraNo = atoi( smalltoken );
#endif
				getStringToken( bigtoken, DELIMITER, 16, sizeof( smalltoken )-1, smalltoken );
				pDir = atoi( smalltoken );
				pDir2 = ( pDir / 10 ) - 1;
				pDir = ( pDir % 10 ) - 1;
				getStringToken( bigtoken, DELIMITER, 17, sizeof( smalltoken )-1,smalltoken );
				pLv = atoi( smalltoken );
				getStringToken( bigtoken, DELIMITER, 18, sizeof( smalltoken )-1,smalltoken );
				pCol = atoi( smalltoken );
				getStringToken( bigtoken, DELIMITER, 19, sizeof( pName )-1, pName );
				makeRecvString( pName );

				// ペットがいる场合は一旦ペットを消す
				if( pYobi->ptPet != NULL ){
					DeathAction( pYobi->ptPet );
					pYobi->ptPet = NULL;
				}
				if( pDir != -1 ){
					x += moveAddTbl[pDir][0];
					y += moveAddTbl[pDir][1];
				}
				pYobi->ptPet = createCharAction( pGraNo, x, y, pDir2 );

				if( pc.ptAct == ptAct ){
					(( CHAREXTRA *)pYobi->ptPet->pYobi)->charObjTblId = -1;
				} else {
					(( CHAREXTRA *)pYobi->ptPet->pYobi)->charObjTblId = pYobi->charObjTblId;
				}
#ifdef PUK2
				pYobi->ptPet->atr |= ACT_ATR_TYPE_OTHER_PC | ACT_ATR_TYPE_TAKENPET;
#else
				pYobi->ptPet->atr |= ACT_ATR_TYPE_OTHER_PC;
#endif

				strcpy( pYobi->ptPet->name, pName );
				pYobi->ptPet->level = pLv;
				pYobi->ptPet->itemNameColor = pCol;
#endif
	#ifdef PUK3_CHECK_VALUE
			} else if( pYobi && pYobi->ptPet != NULL ) {
	#else
			} else if( pYobi->ptPet != NULL ) {
	#endif
				// ペットをつれていて、连れ歩きの情报が无い场合はペットを消す
				DeathAction( pYobi->ptPet );
				pYobi->ptPet = NULL;
			}
#endif
		}
		else
		{
			getStringToken( bigtoken, DELIMITER, 6, sizeof( smalltoken )-1, smalltoken );
			if( strlen( smalltoken ) > 0 )
			{
				// アイテム情报
				getStringToken( bigtoken, DELIMITER, 1, sizeof(smalltoken)-1, smalltoken );
				id = a62toi( smalltoken );
				getStringToken( bigtoken, DELIMITER, 2, sizeof(smalltoken)-1, smalltoken );
				x = atoi( smalltoken );
				getStringToken( bigtoken, DELIMITER, 3, sizeof(smalltoken)-1, smalltoken );
				y = atoi( smalltoken );
				getStringToken( bigtoken, DELIMITER, 4, sizeof(smalltoken)-1, smalltoken );
				graNo = atoi( smalltoken );
				classNo = getIntegerToken( bigtoken, DELIMITER, 5 );
				getStringToken( bigtoken, DELIMITER, 6, sizeof( info )-1, info );
				makeRecvString( info );
				num = getIntegerToken( bigtoken, DELIMITER, 7 );

				setItemCharObj( id, graNo, x, y, 0, classNo, info, num );
			}
			else
			{
				getStringToken( bigtoken, DELIMITER, 4, sizeof( smalltoken )-1, smalltoken );
				if( strlen( smalltoken ) > 0 )
				{
					int graNo;

					// お金情报
					getStringToken( bigtoken, DELIMITER, 1, sizeof(smalltoken)-1, smalltoken );
					id = a62toi( smalltoken );
					getStringToken( bigtoken, DELIMITER, 2, sizeof(smalltoken)-1, smalltoken );
					x = atoi( smalltoken );
					getStringToken( bigtoken, DELIMITER, 3, sizeof(smalltoken)-1, smalltoken );
					y = atoi( smalltoken );
					getStringToken( bigtoken, DELIMITER, 4, sizeof(smalltoken)-1, smalltoken );
					money = atoi( smalltoken );

					sprintf( info, "%d GOLD", money );                               //MLHIDE

					// お金の量でグラフィックを变えるかも
					if( money < 50 )
						graNo = CG_GOLD_1;
					else 
					if( money < 100 )
						graNo = CG_GOLD_2;
					else
					if( money < 1000 )
						graNo = CG_GOLD_3;
					else
					if( money < 5000 )
						graNo = CG_GOLD_4;
					else
					if( money < 10000 )
						graNo = CG_GOLD_5;
					else
					if( money < 50000 )
						graNo = CG_GOLD_6;
					else
					if( money < 100000 )
						graNo = CG_GOLD_7;
					else
					if( money < 500000 )
						graNo = CG_GOLD_8;
					else
					if( money < 1000000 )
						graNo = CG_GOLD_9;
					else
						graNo = CG_GOLD_10;

					setMoneyCharObj( id, graNo, x, y, 0, money, info );
				}
			}
		}
	}
}




//-------------------------------------------------------------------------//
// キャラのアクションを受信
void nrproto_CA_recv( int fd, char *data )
{
	char bigtoken[2048];
	char smalltoken[2048];
	int alreadytellC[1024];
	int tellCindex = 0;
	int tellflag;
	int i, j;
	int charindex;
	int x, y;
	int act;
	int dir;
	int param2;
	ACTION *ptAct;


	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ){
		return;
	}
	
	for( i = 0; ; i++ )
	{
		// データが无くなったら終わる
		getStringToken( data, ',', i+1, sizeof( bigtoken )-1, bigtoken );
		if( strlen( bigtoken ) == 0 )
			break;

		getStringToken( bigtoken, DELIMITER, 1, sizeof( smalltoken )-1, smalltoken );
		charindex = a62toi( smalltoken );
		x      = getIntegerToken( bigtoken, DELIMITER, 2 );
		y      = getIntegerToken( bigtoken, DELIMITER, 3 );
		act    = getIntegerToken( bigtoken, DELIMITER, 4 );
		dir    = getIntegerToken( bigtoken, DELIMITER, 5 );
#ifdef PUK3_VEHICLE
		// TRUEを返したらこの关数内で处理したので次へ
		if ( changeVehicleAct( charindex, x, y, dir, act, bigtoken ) ){
			continue;
		}
#endif
		param2 = getIntegerToken( bigtoken, DELIMITER, 6 );
		//param3 = getIntegerToken( bigtoken, DELIMITER, 7 );
		//param4 = getIntegerToken( bigtoken, DELIMITER, 8 );
		
		// 自分の时
		if( pc.id == charindex )
		{
#ifdef PUK3_RIDE	// ライドの移动テスト
			int		param3 = getIntegerToken( bigtoken, DELIMITER, 7 );
#endif
			// PCキャラの动作变更
			if( pc.ptAct == NULL
			 || (pc.ptAct != NULL && pc.ptAct->anim_chr_no == 0) )
			{
				// アクション出来てない时はCを要求しアクションを作る
				//nrproto_C_send( sockfd, charindex );
			}
			else
			{
#ifdef _FISHING_WINDOW
				// パラメータ２に１が入っていて、アクションがキャラクターのアクションなら
				// 船に乘っていることにする
				if( param2 == 1 && (act < 20 || act >= 50)){
					// 船に乘ってるフラグをつける
					pc.ptAct->dirCnt = 1;
					CreateFishingShip( charindex);
				}
				// パラメータ２に何も入っていなくて、アクションがキャラクターのアクションなら
				// 船から下ろす
				else if( (act < 20 || act >= 50)){
					// 船に乘ってるフラグをつける
					pc.ptAct->dirCnt = 0;
				}
#endif /* _FISHING_WINDOW */
				
#ifdef PUK3_RIDE		// ライドの移动テスト
				changePcAct( x, y, dir, act, param2, param3 );
#else
				changePcAct( x, y, dir, act, param2 );
#endif
#ifdef PUK2
#else
				// マップ处理リセット	// ohta
				resetMap();
#endif
			}
			continue;
		}

		// NPCの处理
		ptAct = getCharObjAct( charindex );
#ifdef PUK3_NOEXISTCHARA
		// キャラが登録されてないなら、假キャラを登録してみる
		if ( ptAct == NULL ){
			// 复旧してみる
			restorePtActCharObj( charindex );
			ptAct = getCharObjAct( charindex );

			// 复旧失败なら假キャラを作る
			if ( ptAct == NULL ){
				// 假キャラは范围外を指定
				setNpcCharObj( charindex, SPR_shadow_mon, x, y, dir, "", "", "",
					0, 0, 0, 0, CHAR_TYPENUM );

				ptAct = getCharObjAct( charindex );
			}
		}
#endif
		if( ptAct == NULL )
		{
			// 同じIDが复数あってもサーバに问い合わせるのは１IDで一回にする
			tellflag = 0;
			for( j = 0; j < tellCindex; j++ )
			{
				if( alreadytellC[j] == charindex )
				{
					tellflag = 1;
					break;
				}
			}
			if( tellflag == 0 && tellCindex < sizeof(alreadytellC) )
			{
				alreadytellC[tellCindex] = charindex;
				tellCindex++;

				//nrproto_C_send( sockfd, charindex );
			}
		}
		else
		{
#ifdef PUK3_PUT_ON
			int		param3 = getIntegerToken( bigtoken, DELIMITER, 7 );
#endif
#ifdef _FISHING_WINDOW
			// パラメータ２に１が入っていて、アクションがキャラクターのアクションなら
			// 船に乘っていることにする
			if( param2 == 1 && (act < 20 || act >= 50)){
				// 船に乘ってるフラグをつける
				ptAct->dirCnt = 1;
				CreateFishingShip( charindex);
			}
			// パラメータ２に何も入っていなくて、アクションがキャラクターのアクションなら
			// 船から下ろす
			else if( param2 == 2){
				ptAct->dirCnt = 0;
			}
#endif /* _FISHING_WINDOW */
#ifdef PUK3_PUT_ON
			changeCharAct( ptAct, x, y, dir, act, param2, param3 );
#else
			changeCharAct( ptAct, x, y, dir, act, param2 );
#endif
		}
	}
}




//-------------------------------------------------------------------------//
// キャラの抹消
void nrproto_CD_recv( int fd, char *data )
{
	int i, j;
	int id;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	for( i = 1; ; i++ )
	{
//		id = getInteger62Token( data, DELIMITER, i );
		id = getInteger62Token( data, ',', i );
		if( id == -1 )
			break;

		delCharObj( id );

		for( j = 0; j < MAX_PARTY; j++ )
		{
			if( party[j].useFlag != 0 && party[j].id == id )
			{
				party[j].ptAct = NULL;
				break;
			}
		}
	}
}




//-------------------------------------------------------------------------//
// 仲间OK、战闘途中参加OK、DUEL OK の状态受信
void nrproto_FS_recv( int fd, int flg )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	pc.etcFlag = (unsigned short)flg;
	pc.etcFlag &= ~PC_ETCFLAG_JOINT_BTL;	// このビットは通常ＯＦＦ
}


//-------------------------------------------------------------------------//
// 战闘时のポジションを受信
void nrproto_POS_recv( int fd, int pos )
{
	pc.battlePositionFlag = pos;
}


//-------------------------------------------------------------------------//
// キャラが持っている全てのアイテム情报
void nrproto_IA_recv( int fd, char *data )
{
	int i;
	int no;
	char name[256];
	char memo[768];
	char freeName[256];

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	for( i = 0; i < MAX_ITEM; i++ )
	{
		// アイテムメニューで位置变更をしたアイテムを掴んでいたら离す
		chaekClearTakeOnItem( i );

		// getIntegerToken が １からの引数なので + 1 する。
		// IA プロトコルでは( アイテムの位置が来ないので １个少ないので - 1 )
		no = i * ( ITEM_RECV_ENUMMAX - 1 )+ 1;

		getStringToken( data, DELIMITER, no+ITEM_RECV_NAME, sizeof( name ) - 1, name );
		makeRecvString( name );

		// 名称が无い时はアイテムが无いとする
		if( strlen( name ) == 0 )
		{
			pc.item[i].useFlag = 0;
			pc.item[i].name[0] = '\0';
			// トレードリストに设定
			if( i >= MAX_EQUIP_ITEM )
			{
				setTradeItemListOnce( 1, i-MAX_EQUIP_ITEM );
			}
			continue;
		}

		pc.item[i].useFlag = 1;
		if( strlen( name ) <= ITEM_NAME_LEN )
		{
			strcpy( pc.item[i].name, name );
		}
		pc.item[i].color = getIntegerToken( data, DELIMITER, no+ITEM_RECV_COLOR );
		if( pc.item[i].color < 0 )
			pc.item[i].color = 0;
		getStringToken( data, DELIMITER, no+ITEM_RECV_MEMO, sizeof( memo ) - 1, memo );
		makeRecvString( memo );
		setItemMemo( i, memo );
		// 说明がある时は鉴定济み
		if( strlen( pc.item[i].memo[0] ) > 0 )
		{
			pc.item[i].checkFlag = 1;
		}
		else
		{
			pc.item[i].checkFlag = 0;
		}
		pc.item[i].graNo = getIntegerToken( data, DELIMITER, no+ITEM_RECV_IMAGENO );
		pc.item[i].field = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ABSFIELD );
		pc.item[i].battle = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ABSBATTLE );
		pc.item[i].target = getIntegerToken( data, DELIMITER, no+ITEM_RECV_TARGET );
		pc.item[i].lv = getIntegerToken( data, DELIMITER, no+ITEM_RECV_LV );
		pc.item[i].flag = getIntegerToken( data, DELIMITER, no+ITEM_RECV_FLAG );
		pc.item[i].id = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ID );
		pc.item[i].kind = getIntegerToken( data, DELIMITER, no+ITEM_RECV_KIND );
		pc.item[i].num = getIntegerToken( data, DELIMITER, no+ITEM_RECV_NUM );
		getStringToken( data, DELIMITER, no+ITEM_RECV_FREENAME, sizeof( freeName ) - 1 , freeName );
#ifdef _OPERATION_REMAKE_ITEM
		pc.item[i].vardata1 = getIntegerToken( data, DELIMITER, no+ITEM_RECV_VARDATA );
#endif /* _OPERATION_REMAKE_ITEM */
		makeRecvString( freeName );
		if( strlen( freeName ) <= ITEM_FREENAME_LEN )
		{
			strcpy( pc.item[i].freeName, freeName );
		}

		// トレードリストに设定
		if( i >= MAX_EQUIP_ITEM )
		{
			setTradeItemListOnce( 1, i-MAX_EQUIP_ITEM );
		}
	}
}




//-------------------------------------------------------------------------//
// アイテム情报受信
void nrproto_I_recv( int fd, char *data )
{
	int i, j;
	int no;
	char name[256];
	char memo[768];
	char freeName[256];

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	for( j = 0; ; j++ )
	{
		// getIntegerToken が １からの引数なので + 1 する。
		no = j * ITEM_RECV_ENUMMAX + 1;

		i = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ARRAY );

		// アイテムメニューで位置变更をしたアイテムを掴んでいたら离す
		chaekClearTakeOnItem( i );

		if( getStringToken( data, DELIMITER, no+ITEM_RECV_NAME, sizeof( name ) - 1 , name ) == 1 )
			break;
		makeRecvString( name );

		// 名称が无い时はアイテムが无いとする
		if( strlen( name ) == 0 )
		{
			pc.item[i].useFlag = 0;
			// トレードリストに设定
			// バンクリストに设定
			if( i >= MAX_EQUIP_ITEM )
			{
				setTradeItemListOnce( 1, i-MAX_EQUIP_ITEM );
				setBankItemListOnce( 1, i-MAX_EQUIP_ITEM );
			}
			continue;
		}

		pc.item[i].useFlag = 1;
		if( strlen( name ) <= ITEM_NAME_LEN )
		{
			strcpy( pc.item[i].name, name );
		}
		pc.item[i].color = getIntegerToken( data, DELIMITER, no+ITEM_RECV_COLOR );
		if( pc.item[i].color < 0 )
			pc.item[i].color = 0;
		getStringToken( data, DELIMITER, no+ITEM_RECV_MEMO, sizeof( memo ) - 1, memo );
		makeRecvString( memo );
		setItemMemo( i, memo );
		// 说明がある时は鉴定济み
		if( strlen( pc.item[i].memo[0] ) > 0 )
		{
			pc.item[i].checkFlag = 1;
		}
		else
		{
			pc.item[i].checkFlag = 0;
		}
		pc.item[i].graNo = getIntegerToken( data, DELIMITER, no+ITEM_RECV_IMAGENO );
		pc.item[i].field = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ABSFIELD );
		pc.item[i].battle = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ABSBATTLE );
		pc.item[i].target = getIntegerToken( data, DELIMITER, no+ITEM_RECV_TARGET );
		pc.item[i].lv = getIntegerToken( data, DELIMITER, no+ITEM_RECV_LV );
		pc.item[i].flag = getIntegerToken( data, DELIMITER, no+ITEM_RECV_FLAG );
		pc.item[i].id = getIntegerToken( data, DELIMITER, no+ITEM_RECV_ID );
		pc.item[i].kind = getIntegerToken( data, DELIMITER, no+ITEM_RECV_KIND );
		pc.item[i].num = getIntegerToken( data, DELIMITER, no+ITEM_RECV_NUM );
		getStringToken( data, DELIMITER, no+ITEM_RECV_FREENAME, sizeof( freeName ) - 1 , freeName );
#ifdef _OPERATION_REMAKE_ITEM
		pc.item[i].vardata1 = getIntegerToken( data, DELIMITER, no+ITEM_RECV_VARDATA );
#endif /* _OPERATION_REMAKE_ITEM */
		makeRecvString( freeName );
		if( strlen( freeName ) <= ITEM_FREENAME_LEN )
		{
			strcpy( pc.item[i].freeName, freeName );
		}

		// トレードリストに设定
		// バンクリストに设定
		if( i >= MAX_EQUIP_ITEM )
		{
			setTradeItemListOnce( 1, i-MAX_EQUIP_ITEM );
			setBankItemListOnce( 1, i-MAX_EQUIP_ITEM );
		}
	}
}


//-------------------------------------------------------------------------//
// アイテムレシピ情报受信
void nrproto_IR_recv( int fd, int haveskillindex, char *data )
{
	char name[256];
	char memo[256];
	int no;
	int i, j;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

#ifdef PUK2
	// トランスの状态を保存
	job.skill[haveskillindex].trn = skillrebirth;
#endif
	no = 1;
	for( i = 0; i < RECIPE_MAX; i++ )
	{
		job.skill[haveskillindex].recipe[i].id = getIntegerToken( data, DELIMITER, no );
		no++;

		job.skill[haveskillindex].recipe[i].rank = getIntegerToken( data, DELIMITER, no );
		no++;
#ifdef _APPEND_JEWEL
		job.skill[haveskillindex].recipe[i].itemid = getIntegerToken( data, DELIMITER, no);
		no++;
#endif /* _APPEND_JEWEL */
		getStringToken( data, DELIMITER, no, sizeof( name ) - 1, name );
		makeRecvString( name );
		if( strlen( name ) <= ITEM_NAME_LEN )
		{
			strcpy( job.skill[haveskillindex].recipe[i].name, name );
		}
		no++;

		getStringToken( data, DELIMITER, no, sizeof( memo ) - 1, memo );
		makeRecvString( memo );
		if( strlen( memo ) <= RECIPE_MEMO_LEN )
		{
			strcpy( job.skill[haveskillindex].recipe[i].memo, memo );
		}
		no++;

		job.skill[haveskillindex].recipe[i].fp = getIntegerToken( data, DELIMITER, no );
		no++;
		
		// 使用できるかフラグ  // ohta
		job.skill[haveskillindex].recipe[i].usableFlag = getIntegerToken( data, DELIMITER, no );
//		job.skill[haveskillindex].recipe[i].usableFlag = FALSE;
//		job.skill[haveskillindex].recipe[i].usableFlag = TRUE;
		no++;

		for( j = 0; j < RECIPE_MATERIAL_MAX; j++ )
		{
			job.skill[haveskillindex].recipe[i].material[j].id = getIntegerToken( data, DELIMITER, no );
			no++;

			getStringToken( data, DELIMITER, no, sizeof( name ) - 1, name );
			makeRecvString( name );
			if( strlen( name ) <= ITEM_NAME_LEN )
			{
				strcpy( job.skill[haveskillindex].recipe[i].material[j].name, name );
			}
			no++;

			job.skill[haveskillindex].recipe[i].material[j].num = getIntegerToken( data, DELIMITER, no );
			no++;
		}
	}
}


#ifdef PUK2
//-------------------------------------------------------------------------//
// バースト时间と状态の受信
void nrproto_BT_recv( int fd, int time, int flg )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// ０以下にはならないように
	if( time < 0 ){
		time = 0;
	}
	pc.bt = time * 1000;		// １秒＝１０００カウント
	pc.btFlg = flg;

	// バースト时间カウント用にワークをクリア
	pc.btLast = GetTickCount();
}
#endif


//-------------------------------------------------------------------------//
// 技使用时の结果受信
void nrproto_TU_recv( int fd, int result, int type, char *data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	// オペレーション分类ーＡの结果
	if( type == 1 )
	{
		openCategoryAResult( result, data );
	}
	else
	// オペレーション分类ーＢの结果
	if( type == 2 )
	{
		openCategoryBResult( result, data );
	}
	else
	// オペレーション分类ーＣの结果
	if( type == 3 )
	{
		openCategoryCResult( result, data );
	}
	else
	// オペレーション分类ーＤの结果
	if( type == 4 )
	{
		openCategoryDResult( result, data );
	}
}



//-------------------------------------------------------------------------//
// ログイン时のキャラの番号と时间
void nrproto_LI_recv( int fd, int index, int servertime, int servernumber, int adjusttime )
{
	setPcId( index );
	serverTime = servertime;
	clientTime = (long)time( NULL );
	// ＮＲ时间を取得
	realTimeToNRTime( &nrTime );
	// ＮＲ时间で今の时间区分を得る
	nrTimeZoneNo = getNRTime( &nrTime );
	// パレットチェンジ
	PaletteChange( nrTimeZoneNo, 0 );
#if 1
	mapServerNo = servernumber;
#endif
#ifdef PUK2
	adjustTime = adjusttime;
#endif
}




//-------------------------------------------------------------------------//
// キャラクタ操作系プロトコル                                              //
//-------------------------------------------------------------------------//


//-------------------------------------------------------------------------//
// 移动プロトコルの受信
void nrproto_W_recv( int fd, int id, int x, int y )
{
	// 今回はあたり判定とかクライアントでして
	// サーバには教えるだけなので何もしない。かも？
}


// 移动プロトコル送信
void walkSendForServer( int x, int y, char *direction )
{
#ifndef _DEBUG
#ifdef PUK3_CONNDOWNWALK
    nrproto_W_send( sockfd , x, y, direction, mapWarpId );
#else
    nrproto_W_send( sockfd , x, y, direction );
#endif
#else
	if( !offlineFlag )
	{
#ifdef PUK3_CONNDOWNWALK
		nrproto_W_send( sockfd , x, y, direction, mapWarpId );
#else
		nrproto_W_send( sockfd , x, y, direction );
#endif
	}
#endif
}


// 移动プロトコル送信
//   こちらはチェックサムを送ってこないので
//   １度でもいった所でないと使えない。
void noChecksumWalkSendForServer( int x, int y, char *direction )
{
#ifndef _DEBUG
	nrproto_w_send( sockfd , x, y, direction );
#else
	if( !offlineFlag )
	{
		nrproto_w_send( sockfd , x, y, direction );
	}
#endif
}




//-------------------------------------------------------------------------//
// アイテム位置变更受信
void nrproto_SI_recv( int fd, int from, int to )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	swapItem( from, to );

	// アイテムメニューで位置变更をしたアイテムを掴んでいたら离す
	chaekClearTakeOnItem( from );
	// アイテムメニューで位置变更をしたアイテムを掴んでいたら离す
	chaekClearTakeOnItem( to );

	// トレード情报の入れ替え
	swapTradeItem( 1, from, to );

	// バンク情报の入れ替え
	swapBankItem( 1, from, to );
}




//-------------------------------------------------------------------------//
// チャット文字列送信
void chatStrSendForServer( char *str, int color )
{
	char dest[1024], m[1024];
	int x, y;

#ifdef _DEBUG
	if( offlineFlag )
	{
		return;
	}

	// チャット文字列でデバッグ制御
	chatDebug( str );
#endif

	x = mapGx;
	y = mapGy;

	makeSendString( str, dest, sizeof(dest) );
	sprintf( m, "P|%s", dest );                                          //MLHIDE
	nrproto_TK_send( sockfd, x, y, m, color, NowMaxVoice, chatFontSize );
}




//-------------------------------------------------------------------------//
// テキスト受信
void nrproto_TK_recv( int fd, int index, char *message, int color, int fontsize )
{
	char id[2];
	char msg[2024];
	ACTION *ptAct;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	getStringToken( message, DELIMITER, 1, sizeof( id  )-1, id );

	if( id[0] == 'P' )
	{
		getStringToken( message, DELIMITER, 2, sizeof( msg )-1, msg );
		makeRecvString( msg );
#ifdef PUK2
		int i, len;
		unsigned char fuki[2048];

		len = strlen( msg );
		for( i = 0 ; i < len ; i++){
			fuki[ i ] = msg[ i ];
		}
#endif
		// チャット文字をバッファに溜める（一行）
		//StockChatBufferLine( msg, color );
		StockChatBufferLine( msg, color, fontsize );

		if( NowChatSound )
		{
			// チャット受信ＳＥ
			play_se( SE_NO_CHAT_ROLL, 320 ,240 );
		}

		if( index >= 0 )
		{
			if( pc.ptAct != NULL && pc.id == index )
			{
#ifdef PUK2
				// 1000ミリ秒表示
				setPcFukidashi( 1000, fuki, color, fontsize, len );
#else
				// 1000ミリ秒表示
				setPcFukidashi( 1000 );
#endif
			}
			else
			{
				ptAct = getCharObjAct( index );
				if( ptAct != NULL )
				{
#ifdef PUK2
					// 1000ミリ秒表示
					setCharFukidashi( ptAct, 1000, fuki, color, fontsize, len );
#else
					// 1000ミリ秒表示
					setCharFukidashi( ptAct, 1000 );
#endif
				}
			}
		}
	}
}


//-------------------------------------------------------------------------//
// システムテキスト受信
void nrproto_STK_recv( int fd, char *message )
{
	char msg[2024];

	strcpy( msg, message );
	makeRecvString( msg );
	StockChatBufferLine( msg, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );

	if( NowChatSound )
	{
		// チャット受信ＳＥ
		play_se( SE_NO_CHAT_ROLL, 320 ,240 );
	}
}


//-------------------------------------------------------------------------//
// ペットの技を使い終わったを受信
void nrproto_PS_recv( int fd,int result,int havepetindex,int havepetskill,int toindex )
{
	char moji[ 256 ];

	// 失败したら
	if( result == 0 ){
		//一行インフォに表示
		sprintf( moji,"失败しました！");                                           //MLHIDE
		// チャット文字をバッファに溜める（一行）
		StockChatBufferLine( moji, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	}
}



//-------------------------------------------------------------------------//
// 等级アップボーナスポイント
void nrproto_LVUP_recv( int fd, int point )
{
	bonusPoint = point;
}


//-------------------------------------------------------------------------//
// ペットの等级アップボーナスポイント
void nrproto_PLVUP_recv( int fd, int petindex, int point )
{
	pet[petindex].bonusPoint = point;
}


//-------------------------------------------------------------------------//
// EV（イベント）送信后の受信待ち
void nrproto_EV_recv( int fd, int seqno, int result )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	if( eventWarpSendId == seqno )
	{
		eventWarpSendFlag = 0;
		if( result == 0 )
		{
			// ワープ失败したら元の画面を表示させる
			redrawMap();
			floorChangeFlag = FALSE;
			// 强制的にフェードインさせる
			//warpEffectStart = TRUE;
			warpEffectStart = FALSE;
		}
		else{
			// マップ要求中じゃない时
			if( warpMcFlag == FALSE ){
				// 正常にワープが終わった时 // ohta
				warpEffectStart = FALSE;
			}
		}
		
	}
	else
	if( eventEnemySendId == seqno )
	{
		if( result == 0 )
		{
			eventEnemySendFlag = 0;
		}
		//else
		//{
			// フラグOFFはprocess.cppでやる
		//}
	}
}




//-------------------------------------------------------------------------//
// 仲间要求の受信
void nrproto_PR_recv( int fd, int request, int result )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	if( request == 1 && result == 1 )
	{
		// 谁かの布ティーに入る要求が通った
		setPcParty();
	}
	else
	if( request == 0 && result == 1 )
	{
		// 布ティーから除队した
		//delPcParty();
		//delPcLeader();
		partyModeFlag = 0;
		clearPartyParam();

		char dir = pc.dir;
		nrproto_SP_send( sockfd, nextMapGx, nextMapGy, dir );
	}
	prSendFlag = 0;
}




//-------------------------------------------------------------------------//
// 战闘に出すペットを选择した
void nrproto_KS_recv( int fd,int petarray,int result )
{
#if 0
	int cnt = 0; // 待机カウント
	int i;
	
	// 受信待ちフラグ初期化
	BattlePetReceiveFlag = FALSE;
	// バトル受信中のペット番号初期化
	BattlePetReceivePetNo = -1;
	// 成功した时
	if( result == TRUE ){ 
		// ペット番号バックアップ初期化
		battlePetNoBak = -2;
		// 以前のバトルペットを待机でなくする
		//if( pc.battlePetNo != -1 ) pc.selectPetNo[ pc.battlePetNo ] = FALSE;
		
		// 今回のバトルペットを待机にする
		if( petarray != -1 ){ 
			// 待机にする
			pc.selectPetNo[ petarray ] = TRUE;
			// メールペットだったら、ＯＦＦにする
			if( pc.mailPetNo == petarray ) pc.mailPetNo = -1;
			// 待机ペット数チェック
			for( i = 0 ; i < 5 ; i++ ){
				if( pc.selectPetNo[ i ] == TRUE && i != petarray ) cnt++;
				// 人数オバーの时、ＯＦＦにする
				if( cnt >= 3 ){ 
					pc.selectPetNo[ i ] = FALSE;
					cnt--;
				}
			}
		}
		// 今回のバトルペット番号记忆
		pc.battlePetNo = petarray;
	}
#endif
}




//-------------------------------------------------------------------------//
// 战闘系プロトコル                                                        //
//-------------------------------------------------------------------------//
// エンカウント率の设定
void nrproto_EP_recv( int fd, int min, int max )
{
	minEncountPercentage = min;
	maxEncountPercentage = max;
	nowEncountPercentage = minEncountPercentage;
}




//-------------------------------------------------------------------------//
// EN（エンカウント）送信后の受信待ち
// result の值	１：通常エンカウント
//				２：デュエル
//				４：観战
//				５：ヘルプ不可
//				６：固定敌
void nrproto_EN_recv( int fd,int result,int field )
{
	int i;
	
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;
#ifdef PUK2
	// 简易登出はじめたらサーバからの情报は无视する
	if( SimpleLogoutFlag ){
		// 先ず拟似的にエンカウントする
		// オートマップウィンドウフラグ记忆
		if( autoMapOpenFlag == FALSE ){	// ohta
			autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
		}
		resetPc();				// PCリセット
		resetCharObj();			// キャラ管理テーブルリセット
		resetMap();				// マップ处理リセット
		clearPtActPartyParam();	// 仲间情报のアクションポインタだけをNULLにする
		fieldInfoTime = 0;		// 场所情报が出てたら消す
		drawFieldInfoWin();		// ワープ后の场所情报
		resetFieldProc();		// フィールド处理のリセット
		nowEncountPercentage = minEncountPercentage;// エンカウント率を最小に戾す
		sendEnFlag = 0;
		encountNowFlag = 1;
		eventEnemySendFlag = 0;
		duelSendFlag = 0;
		jbSendFlag = 0;

		// 次に拟似的に战闘を終了する
		// プロセスチェンジ
		ChangeProc( PROC_GAME, 1 );
		// アクション全抹杀
		DeathAllAction();
		// キャラ管理テーブルのアクションを抹消
		clearPtActCharObj();
		// この后からのＣ、ＣＡプロトコルを受け付ける
		encountNowFlag = 0;
		// クライアントが最后のムービーを见終わったときに送る。
		nrproto_EO_send( sockfd, 0 );

		return;
	}
#endif

	// エンカウンフラグＯＮ
	if( result > 0 )
	{
		if( partyModeFlag && (pc.status & CHR_STATUS_LEADER) == 0 )
		{
			if( autoMapOpenFlag == FALSE ){	// ohta
				// オートマップ表示の有无を记忆
				autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
			}
		}

		// エンカウントの种类　０：失败　１：通常　２：ＤＵＥＬ　３：観战　４：固定敌　５：ボス　６：ラスボス
		EncountFlag = TRUE;
		EncountStatus = result;
		
		// デュエルの时
		if( result == 2 ) DuelFlag = TRUE;
		else DuelFlag = FALSE;
		
		// 観战
		if( result == 3 ) vsLookFlag = 1;
		else vsLookFlag = 0;
		
		// デュエルか固定敌の时
		if( result ==2 || result == 4 || result == 5 || result == 6 ) eventEnemyFlag = 1;
		else eventEnemyFlag = 0;
		
		// エラーチェック
		//if( field < 0 || BATTLE_MAP_FILES <= field ){
			//BattleMapNo = 0;	// デフォルト番号
		//}else{
			BattleMapNo = field;	// 战闘マップ番号
		//}
		
		// ヘルプ无しの时
		//if( result == 2 || result == 5 ) NoHelpFlag = TRUE;
		//else NoHelpFlag = FALSE;
		
		// フラグOFFはprocess.cppでやる
		// sendEnFlag = 0;
		// duelSendFlag = 0;

		// ムービー読み込みフラグＯＮ
		ReadBmDataFlag = FALSE;
#ifdef PUK2
		BmEndFlag = FALSE;
#endif
		// バトルコマンド初期化
		BcDataGetBufNo = BcDataSetBufNo = 0;
		BmDataGetBufNo = BmDataSetBufNo = 0;
		// バッファクリア
		BcData[ 0 ] = NULL;
		BmData[ 0 ] = NULL;
		for( i = 0 ; i < B_BUF_SIZE ; i++ ){
			BcDataBak[ i ][ 0 ] = NULL;
			BmDataBak[ i ][ 0 ] = NULL;
		}
		BattleTurnReceiveFlag = TRUE;	// ターン受信フラグＯＮ
	}
	else
	{
		// 结果もらってエンカウントできないならすぐフラグOFF
		sendEnFlag = 0;
		duelSendFlag = 0;
		jbSendFlag = 0;
	}
}




//-------------------------------------------------------------------------//
// 战闘中ヘルプの受信
void nrproto_HL_recv( int fd, int flg )
{
	helpFlag = flg;
}




//-------------------------------------------------------------------------//
// バトル关连受信
void nrproto_B_recv( int fd,char* command )
{
	// バトルキャラクターデータ受信
	if( *command == 'C'){
		// データコピー
		strcpy( BcDataBak[ BcDataSetBufNo ], command );
		// 次のバッファへ
		BcDataSetBufNo++;
		// リミットチェック
		if( BcDataSetBufNo >= B_BUF_SIZE ) BcDataSetBufNo = 0;
	}
	// 本人受信
	else if( *command == 'P')
#ifdef PUK2
	#ifdef PUK3_RIDE_BATTLE
		sscanf( command + 2, "%X|%X|%X|%X|%X|%X", &BattleMyNoBak, &BattleBpFlag, &BattleUsableSkillFlag, &BattlePetUsableSkillFlag, &BattleRebirthLevel, &BattleRidePetNo ); //MLHIDE
	#else
		sscanf( command + 2, "%X|%X|%X|%X|%X", &BattleMyNoBak, &BattleBpFlag, &BattleUsableSkillFlag, &BattlePetUsableSkillFlag, &BattleRebirthLevel ); //MLHIDE
	#endif
#else
		sscanf( command + 2, "%X|%X|%X|%X", &BattleMyNoBak, &BattleBpFlag, &BattleUsableSkillFlag, &BattlePetUsableSkillFlag ); //MLHIDE
		//sscanf( command + 2, "%X|%X|%X", &BattleMyNo, &BattleBpFlag, &BattleUsableSkill );
#endif
	// コマンド入力济みフラグ受信
	else if( *command == 'A'){
		sscanf( command + 2, "%X|%X", &BattleAnimFlag, &BattleSvTurnNo );   //MLHIDE
		// ターン受信フラグＯＮの时
		if( BattleTurnReceiveFlag == TRUE ){ 
			BattleCliTurnNo = BattleSvTurnNo;	// 最初のターン番号を学习。
			BattleTurnReceiveFlag = FALSE;		// フラグＯＦＦ
		}
		// コマンド入力济みフラグのチェック
		CheckBattleAnimFlag();
	}
	// 战闘强制終了受信
	else if( *( command + 1 ) == 'U')
		BattleEscFlag = TRUE;
	// バトルムービー受信
	else if( *command == 'M'){
#ifdef TW_BATTLE_COMMAND
		//清除台服战斗协议中多余的参数
		deleteSubString(command, "|i49|l1E|pFFFFFFFF");                     //MLHIDE
#endif
		// データコピー
		strcpy( BmDataBak[ BmDataSetBufNo ], command );
		// 次のバッファへ
		BmDataSetBufNo++;
		// リミットチェック
		if( BmDataSetBufNo >= B_BUF_SIZE ) BmDataSetBufNo = 0;
	}
	// 怪我情报受信
	//else if( *command == 'I'){
		// データコピー
		//strcpy( BiData, command );
	//}
	
#ifdef _DEBUG_MSG
		StockChatBufferLine( command, FONT_PAL_GRAY );
#endif
	
}




//-------------------------------------------------------------------------//
// 战闘结果信息受信
void nrproto_RS_recv( int fd, char *data )
{
	int i;
	int no;
	char token[2048];
	char item[2048];
#ifdef PUK2_NEW_MENU
	char str[2048];
#endif

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	battleResultMsg.useFlag = 1;

	no = 1;
	for( i = 0; i < RESULT_CHR_INFO; i++ )
	{
		getStringToken( data, ',', no, sizeof( token )-1, token );
		no++;

		battleResultMsg.resChrInfo[i].no	= getIntegerToken( token, DELIMITER, 1 );
		battleResultMsg.resChrInfo[i].lvUp	= getIntegerToken( token, DELIMITER, 2 );
		battleResultMsg.resChrInfo[i].exp	= getInteger62Token( token, DELIMITER, 3 );
	}

	getStringToken( data, ',', no, sizeof( token )-1, token );
	no++;
	for( i = 0; i < RESULT_ITEM_INFO; i++ )
	{
		getStringToken( token, DELIMITER, i+1, sizeof( item )-1, item );
#ifdef PUK2_NEW_MENU
		makeStringFromEscaped( item );

		battleResultMsg.itemgraNo[i] = getIntegerToken( item, '|', 1 );

		getStringToken( item, '|', 2, sizeof(str) - 1 , str );
		makeRecvString( str );
		if( strlen( str ) <= ITEM_NAME_LEN )
		{
			strcpy( battleResultMsg.itemName[i], str );
		}
		else
		{
			battleResultMsg.itemName[i][0] = '\0';
		}
#else
		makeRecvString( item );
		if( strlen( item ) <= ITEM_NAME_LEN )
		{
			strcpy( battleResultMsg.itemName[i], item );
		}
		else
		{
			battleResultMsg.itemName[i][0] = '\0';
		}
#endif
	}

	for( i = 0; i < RESULT_SKILL_INFO; i++ )
	{
		getStringToken( data, ',', no, sizeof( token )-1, token );
		no++;

		battleResultMsg.resSkillInfo[i].lvUp	= getIntegerToken( token, DELIMITER, 1 );
		if( battleResultMsg.resSkillInfo[i].lvUp < 0 )
		{
			battleResultMsg.resSkillInfo[i].lvUp = 0;
		}
		battleResultMsg.resSkillInfo[i].exp		= getInteger62Token( token, DELIMITER, 2 );
		if( battleResultMsg.resSkillInfo[i].exp < 0 )
		{
			battleResultMsg.resSkillInfo[i].exp = 0;
		}
	}
}


//-------------------------------------------------------------------------//
// デュエル结果信息受信
void nrproto_RD_recv( int fd, char *data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	battleResultMsg.useFlag = 2;

	battleResultMsg.getDp = getInteger62Token( data, DELIMITER, 1 );
	battleResultMsg.dp = getInteger62Token( data, DELIMITER, 2 );
}




//-------------------------------------------------------------------------//
// メール系プロトコル                                                      //
//-------------------------------------------------------------------------//

//-------------------------------------------------------------------------//
// アドレスブックの内容受信
void nrproto_AB_recv( int fd, char *data )
{
	int i;
	int no;
	char name[256];
	char freeName[256];
	int flag;
	int useFlag;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	for( i = 0; i < ADDRESS_BOOK; i++ )
	{
		no = i * 7;

		useFlag = getIntegerToken( data, DELIMITER, no+1 );
		if( useFlag <= 0 )
		{
			addressBook[i].useFlag = 0;
			addressBook[i].name[0] = '\0';
			addressBook[i].freeName[0] = '\0';
			continue;
		}

		addressBook[i].useFlag = 1;

		flag = getStringToken( data, DELIMITER, no+2, sizeof( name )-1 , name );
		if( flag == 1 )
			break;
		makeRecvString( name );
		if( strlen( name ) <= CHAR_NAME_LEN )
		{
			strcpy( addressBook[i].name, name );
		}
		flag = getStringToken( data, DELIMITER, no+3, sizeof( freeName )-1 , freeName );
		makeRecvString( freeName );
		if( strlen( freeName ) <= CHAR_FREENAME_LEN )
		{
			strcpy( addressBook[i].freeName, freeName );
		}
		addressBook[i].id = getIntegerToken( data, DELIMITER, no+4 );
		addressBook[i].lv = getIntegerToken( data, DELIMITER, no+5 );
		addressBook[i].onlineFlag = (short)getIntegerToken( data, DELIMITER, no+6 );
		addressBook[i].graNo = getIntegerToken( data, DELIMITER, no+7 );
	}

#ifndef PUK2
	// アドレスブックのソート
	sortAddressBook();
#endif
}

#ifdef PUK2
void nrproto_ABG_recv( int fd, char *data )
{
	int i;
	int no;
	char guildName[256];
	char guildTitle[256];
	int flag;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	for( i = 0; i < ADDRESS_BOOK; i++ )
	{
		no = i * 2;

		// 登録されているアドレスブックかどうか确认
//		if( addressBook[i].useFlag ){
//			continue;
//		}

		flag = getStringToken( data, DELIMITER, no+1, sizeof( guildName )-1 , guildName );
		if( flag == 1 ){
			// これ以上文字列が无ければ終了
			break;
		}
		makeRecvString( guildName );
		if( strlen( guildName ) <= ALBUM_GUILD_NAME_LEN )
		{
			strcpy( addressBook[i].guildName, guildName );
		}
		flag = getStringToken( data, DELIMITER, no+2, sizeof( guildTitle )-1 , guildTitle );
		makeRecvString( guildTitle );
		if( strlen( guildTitle ) <= ALBUM_GUILD_TITLENAME_LEN )
		{
			strcpy( addressBook[i].guildTitle, guildTitle );
		}
	}

	// アドレスブックのソート
	sortAddressBook();
}
#endif


//-------------------------------------------------------------------------//
// アドレスブックの内容受信（单体）
void nrproto_ABI_recv( int fd, int num, char* data )
{
	char name[256];
	char freeName[256];
	int useFlag;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	useFlag = getIntegerToken( data, DELIMITER, 1 );
	if( useFlag <= 0 )
	{
		addressBook[num].useFlag = 0;
		addressBook[num].name[0] = '\0';
		addressBook[num].freeName[0] = '\0';
		return;
	}

	addressBook[num].useFlag = 1;

	getStringToken( data, DELIMITER, 2, sizeof( name )-1 , name );
	makeRecvString( name );
	if( strlen( name ) <= CHAR_NAME_LEN )
	{
		strcpy( addressBook[num].name, name );
	}
	getStringToken( data, DELIMITER, 3, sizeof( freeName )-1 , freeName );
	makeRecvString( freeName );
	if( strlen( freeName ) <= CHAR_FREENAME_LEN )
	{
		strcpy( addressBook[num].freeName, freeName );
	}
	addressBook[num].id = getIntegerToken( data, DELIMITER, 4 );
	addressBook[num].lv = getIntegerToken( data, DELIMITER, 5 );
	addressBook[num].onlineFlag = (short)getIntegerToken( data, DELIMITER, 6 );
	addressBook[num].graNo = getIntegerToken( data, DELIMITER, 7 );
	
	// 新规の时はクライアント履历データを抹杀 ohta
	if( getIntegerToken( data, DELIMITER, 8 ) == TRUE ){
	
		memset( &mailHistory[selectPcNo][num], 0, sizeof( mailHistory[selectPcNo][num] ) );
	}
	
#ifdef PUK2
	// 家族名と家族称号を取得
	getStringToken( data, DELIMITER, 9, sizeof( freeName )-1 , freeName );
	makeRecvString( freeName );
	if( strlen( freeName ) <= ALBUM_GUILD_NAME_LEN )
	{
		strcpy( addressBook[num].guildName, freeName );
	}
	getStringToken( data, DELIMITER,10, sizeof( freeName )-1 , freeName );
	makeRecvString( freeName );
	if( strlen( freeName ) <= ALBUM_GUILD_TITLENAME_LEN )
	{
		strcpy( addressBook[num].guildTitle, freeName );
	}
#endif
	// アドレスブックのソート
	sortAddressBook();
}




//-------------------------------------------------------------------------//
// アドレスブック信息を受け取った
void nrproto_MSG_recv( int fd, int index, char *data, int color )
{
	char header[32];
	char text[300];
	char str[ 256 ];
	int graNo;

	getStringToken( data, DELIMITER, 1, sizeof( header )-1, header );
	getStringToken( data, DELIMITER, 2, sizeof( text )-1, text );
	graNo = getIntegerToken( data, DELIMITER, 3 );
	// エスケープをはずす。
	makeRecvString( text );
	if( graNo == -1 ){
		// 通常メール
		setMailHistory( index, 0, header, text );
		// 受信したことをチャット行で知らせる
		sprintf( str, "%s からメールが届きました！", addressBook[index].name );         //MLHIDE
	}else{
		// ペットメール
		setMailHistory( index, 0, header, text );
		// 受信したことをチャット行で知らせる
		sprintf( str, "%s からモンスターメールが届きました！", addressBook[index].name );    //MLHIDE
	}

	// ヒストリをファイルに保存
	writeMailFile();

	// 未読チェック
	checkNoReadMail();

	// 见てるページにデータがきたらページずらす
	if( mailHistorySelectNo == index ){
		if( mailHistoryPage < MAIL_HISTORY_CNT - 1 )
			mailHistoryPage++;
	}

	// 受信音
	play_se( SE_NO_MAIL_RECEIVE, 320, 240 );
	StockChatBufferLine( str, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
}




//-------------------------------------------------------------------------//
// ペットメール演出
void nrproto_PME_recv( int fd, int objindex,
	int graphicsno, int x, int y, int dir, int flg, int no, char *cdata )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag )
	{
		return;
	}

#ifdef _CG2_NEWGRAPHIC
	graphicsno = getNewGraphicNo( graphicsno);
#endif

	// 送信演出
	if( flg == 0 )
	{
		switch( no )
		{
			case 0:
				// 回転して出かける
				createPetAction( graphicsno, x, y, dir, 2, 0, -1 );
				break;

			case 1:
				// ジャンプして出かける
				createPetAction( graphicsno, x, y, dir, 8, 0, -1 );
				break;

			case 2:
				// その场で点灭して出かける
				createPetAction( graphicsno, x, y, dir, 4, 0, -1 );
				break;
			case 3:
				// 点灭しながら移动する
				createPetAction( graphicsno, x, y, dir, 0, dir, -1 );
				break;
		}
	}
	else
	// 受信演出
	{
		char smalltoken[2048];
		int id;
		int x;
		int y;
		int dir;
		int graNo;
		int level;
		int nameColor;
		char name[2048];
		char freeName[2048];
		int walkable;
		int height;
		int charType;

		charType = getIntegerToken( cdata, DELIMITER, 1 );
		getStringToken( cdata, DELIMITER, 2, sizeof( smalltoken )-1, smalltoken );
		id = a62toi( smalltoken );
		getStringToken( cdata, DELIMITER, 3, sizeof( smalltoken )-1, smalltoken );
		x = atoi( smalltoken );
		getStringToken( cdata, DELIMITER, 4, sizeof( smalltoken )-1, smalltoken );
		y = atoi( smalltoken );
		getStringToken( cdata, DELIMITER, 5, sizeof( smalltoken )-1, smalltoken );
		dir = atoi( smalltoken );
		getStringToken( cdata, DELIMITER, 6, sizeof( smalltoken )-1, smalltoken );
#ifdef _CG2_NEWGRAPHIC
		graNo = getNewGraphicNo( atoi( smalltoken ));
#else
		graNo = atoi( smalltoken );
#endif
		getStringToken( cdata, DELIMITER, 7, sizeof( smalltoken )-1,smalltoken );
		level = atoi( smalltoken );
		nameColor = getIntegerToken( cdata, DELIMITER, 8 );
		getStringToken( cdata, DELIMITER , 9 , sizeof( name )-1, name );
		makeRecvString( name );
		getStringToken( cdata, DELIMITER , 10 , sizeof( freeName )-1, freeName );
		makeRecvString( freeName );
		getStringToken( cdata, DELIMITER, 11, sizeof( smalltoken )-1, smalltoken );
		walkable = atoi( smalltoken );
		getStringToken( cdata, DELIMITER, 12, sizeof( smalltoken )-1, smalltoken );
		height = atoi( smalltoken );

		// すでに情报があったら何もしない
		if( setReturnPetObj( id, graNo, x, y, dir, name, freeName,
			level, nameColor, walkable, height, charType ) )
		{
			switch( no )
			{
				case 0:
					// 回転して归ってくる
					createPetAction( graphicsno, x, y, dir, 3, 0, objindex );
					break;

				case 1:
					// ジャンプして归ってくる
					createPetAction( graphicsno, x, y, dir, 9, 0, objindex );
					break;

				case 2:
					// その场で点灭して归ってくる
					createPetAction( graphicsno, x, y, dir, 5, 0, objindex );
					// 点灭しながら追いかけてくる（やばい）
					//createPetAction( graphicsno, x, y, dir, 7, 0, objindex );
					break;
				case 3:
					// その场に突然现れる
					createPetAction( graphicsno, x, y, dir, 1, 0, objindex );
					break;
			}
		}
	}
}




//-------------------------------------------------------------------------//
// その他、描画、演出プロトコル                                            //
//-------------------------------------------------------------------------//

//-------------------------------------------------------------------------//
// ウィンドウ处理
void nrproto_WN_recv( int fd, int windowtype, int buttontype, int seqno, int objindex, char *data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;
#ifdef PUK2
	// 简易登出はじめたらサーバからの情报は无视する
	if( SimpleLogoutFlag ) return;
#endif

	// 战闘中は无视する
	if( encountNowFlag ) return;

#ifdef SERVER_WIN_CLOSE
	//サーバーからのウインドウ关闭要求
	if(buttontype==WINDOW_BUTTONTYPE_WIN_CLOSE){
		if(windowtype==serverRequestWinWindowType && objindex==serverRequestWinObjIndex){
			CloseWindowSelectType(3);
			serverRequestWinWindowType = -1;
		}

		return;
	}
#endif
	
	openServerRequestWindow( windowtype, buttontype, seqno, objindex, data );
#ifdef PUK2
	ACTION *ptAct;

	// ウィンドウを発行したＮＰＣの向きを变更する
	ptAct = getCharObjAct( objindex );
	changeNpcCharDir( ptAct );
#endif


}




//-------------------------------------------------------------------------//
// マップエフェクトの受信待ち
void nrproto_EF_recv( int fd, int effect, int level, char *option )
{
	// 全てのエフェクトを止める
	if( effect == 0 )
	{
		mapEffectRainLevel = 0;
		mapEffectSnowLevel = 0;
		mapEffectKamiFubukiLevel = 0;
#ifdef PUK3_WHALE_SHIP
		setMapEffect( effect, level, option );
#endif
		return;
	}
	// 雨のエフェクト
	if( effect & 1 )
	{
		mapEffectRainLevel = level;
	}
	// 雪のエフェクト
	if( effect & 2 )
	{
		mapEffectSnowLevel = level;
	}
	// 纸ふぶき
	if( effect & 4 )
	{
		mapEffectKamiFubukiLevel = level;
	}
#ifdef PUK3_WHALE_SHIP
	setMapEffect( effect, level, option );
#endif
}




//-------------------------------------------------------------------------//
// サーバからＳＥ発音の受信
void nrproto_SE_recv( int fd, int senumber )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 仲间入る
	if( senumber == CHAR_SE_JOINPARTY )
	{
		play_se( SE_NO_PARTY, 320, 240 );
	}
	else
	// 仲间拔ける
	if( senumber == CHAR_SE_DISCHARGEPARTY )
	{
		play_se( SE_NO_BREAKUP, 320, 240 );
	}
	else
	// アイテム拾う
	if( senumber == CHAR_SE_PICKUPITEM )
	{
		play_se( SE_NO_TAKE_GOLD, 320, 240 );
	}
	else
	// アイテム落す
	if( senumber == CHAR_SE_DROPITEM )
	{
		play_se( SE_NO_DROP_ITEM, 320, 240 );
	}
	else
	// お金拾う
	if( senumber == CHAR_SE_PICKUPGOLD )
	{
		play_se( SE_NO_TAKE_GOLD, 320, 240 );
	}
	else
	// お金落す(1～999 GOLD)
	if( senumber == CHAR_SE_DROPGOLD1 )
	{
		play_se( SE_NO_DROP_GOLD, 320, 240 );
	}
	else
	// お金落す(1000～99999 GOLD)
	if( senumber == CHAR_SE_DROPGOLD2 )
	{
		play_se( SE_NO_DROP_GOLD2, 320, 240 );
	}
	else
	// お金落す(100000～ GOLD)
	if( senumber == CHAR_SE_DROPGOLD3 )
	{
		play_se( SE_NO_DROP_GOLD3, 320, 240 );
	}
	else
	// 名刺交换
	if( senumber == CHAR_SE_EXCHANGECARD )
	{
		play_se( SE_NO_CARD_CHANGE, 320, 240 );
	}
}


// サーバからＢＧＭ再生の受信
void nrproto_BGMW_recv( int fd, int sw )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 范围外の值は无视
	if( sw < BGM_NO_START || BGM_NO_END < sw )
		return;

	recvBgmNo = sw;
#ifdef PUK2
	recvBgmNoFlag = TRUE;	// recvBgmNoは有效g
#endif
}




//-------------------------------------------------------------------------//
// 目の前のキャラリスト要求に对する受信
void nrproto_GFL_recv( int fd, char *data )
{
	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	openCategoryCTargetSel1Menu( data );
}

// 指定キャラとペットの情报受信
void nrproto_GPD_recv( int fd, char *data )
{
	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	openCategoryCTargetSel2Menu( data );
}


// アイテム使用时に目の前のキャラリストを受信
void nrproto_GFLI_recv( int fd, char *data )
{
	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	openUseItemTargetSelMenu( data );
}

// アイテム使用时に指定キャラとペットの情报受信
void nrproto_GPDI_recv( int fd, char *data )
{
	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	openUseItemTargetSelMenu2( data );
}

// アイテム使用时に指定キャラとペットの情报受信
void nrproto_IH_recv( int fd)
{
	// 战闘中は无视する
	if( encountNowFlag ) return;
	
//	openUseItemTargetSelMenu2( data );
}



//-------------------------------------------------------------------------//
// トレード关连

// トレード可能プレイヤーのリスト受信
void nrproto_TRPL_recv( int fd, char *data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	openTradePlayerSelectMenu( data );
}

// 他のプレイヤーからトレードの要求がきた
void nrproto_TRS_recv( int fd, char *name, int level )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;
#ifdef PUK2
	// 简易登出はじめたらサーバからの情报は无视する
	if( SimpleLogoutFlag ){
		nrproto_TROC_send( sockfd, 0 );
		return;
	}
#endif

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	makeRecvString( name );
	openTradeMenu( name );
}

// 他のプレイヤーがトレード物の开示をした
void nrproto_TROP_recv( int fd )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	tradeOpponentMode = 1;
}

// トレード额の受信
void nrproto_TRLG_recv( int fd, int gold )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	tradeOpponentGold = gold;
}

// トレードアイテムの受信
void nrproto_TRLI_recv( int fd, char* data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	setOpponentItemList( data );
}

// トレードモンスターの受信
void nrproto_TRLP_recv( int fd, int petplace, char *data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	setOppnentMonsterList( petplace, data );
}

// トレードモンスターのスキルの受信
void nrproto_TRLPS_recv( int fd, int petplace, char *data )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	setOppnentMonsterSkillList( petplace, data );
}

// 开示状态をキャンセルした
void nrproto_TRCL_recv( int fd )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
	tradeOpponentMode = 0;
	clearTradeList( 0 );
	if( tradeOwnerMode == 2 )
	{
		tradeOwnerMode = 1;
	}
}

// トレードの许诺??終了
void nrproto_TROC_recv( int fd, int flg )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 战闘中は无视する
	if( encountNowFlag ) return;
	
#ifdef PUK2_NEW_MENU
	switch(flg){
	case 1:	// トレードＯＫが押された
		tradeOpponentMode = 2;
		break;
	case 0:	// トレードがキャンセルされた
		closeTradeWindow();
		break;
	case 2:	// トレードが成立した
		closeTradeWindow();
		break;
	}
#else
	if( flg )
	{
		if( tradeOwnerMode == 2 )
		{
#ifdef PUK2_NEW_MENU
			closeTradeWindow();
#else
			menuClose( MENU_TRADE_WIN );
#endif
		}
		else
		{
			tradeOpponentMode = 2;
		}
	}
	else
	{
#ifdef PUK2_NEW_MENU
		closeTradeWindow();
#else
		menuClose( MENU_TRADE_WIN );
#endif
	}
#endif
}



//-------------------------------------------------------------------------//
// アルバム关连

// 新规记忆情报
void nrproto_AL_recv( int fd, int albumversion, char *data )
{
	checkAlbumBit( albumversion, data );
}


// １体分のアルバム情报
void nrproto_ALI_recv( int fd, int albumid, int imgno, int catchflg, int rare, int type,
	int vital, int str, int tgh, int quick, int magic,
	int earth, int water, int fire, int wind, int slot, char *comment, char *name )
{
	setAlbum( albumid, imgno, catchflg, rare, type,
		vital, str, tgh, quick, magic, earth, water, fire, wind, slot, comment, name );
}


// アルバムウィンドウを开く命令
void nrproto_ALO_recv( int fd, int albumid )
{
#if 0
	albumPage = albumid;
	// アルバムリストウィンドウを开く
	albumListReturnFlag = 1;
	// リストウィンドウを开く
	menuOpen( MENU_MONSTER_ALBUM_LIST );
#endif
}


void nrproto_ALN_recv( int fd, int albumid, char *name )
{
}


// パレットチェンジプロトコル
void nrproto_PC_recv( int fd,int palnumber,int frame_cnt )
{
	// 战闘中はパレット变更しない
	if( ProcNo == PROC_BATTLE ) return;
	
	// パレットとフレーム数
	PaletteChange( palnumber, frame_cnt );
	
}

void nrproto_SH_recv( int fd,int action,int mapid,int floor,int x,int y )
{
	//船のアクション作成
	create_ship( action, mapid, floor, x, y );
	return;
}

void nrproto_PLAYSE_recv( int fd,int seno, int x,int y )
{
#ifdef DIRECT_MUSIC		//DirectMusicシステム
	//ＤＭ效果音なら
	if( seno >= 1000 ){
		//ＤＭ效果音再生
		PlaySMF( seno - 1000, x );
	} else {
		//ＳＥ番号
		play_se( seno, x, y );
	}
#else
	//ＳＥ番号
	play_se( seno, x, y );
#endif
}

void nrproto_ES_recv( int fd,int seno, int x,int y )
{
#ifdef PUK2
	setEnvironmentSE (seno, x, y);
#endif
}



// クライアントのＩＰアドレス
char ClientIpAddress[ 256 ];
// ＩＰアドレス受信プロトコル
void nrproto_IP_recv( int fd,char* ip ) /* ../doc/nrproto.html line 3420 */
{
	char str[ 256 ];
	
	// クライアント起动时は素直に受信する
	if( ClientIpAddress[ 0 ] == '\0' ){
		strcpy( ClientIpAddress, ip );
	}else{
		// ＩＰアドレスが违ったら
		if( strstr( ClientIpAddress, ip ) == NULL ){
#ifdef PUK3_ERRORMESSAGE_NUM
			sprintf( str, INFOMSG_25, ClientIpAddress, ip );
			StockChatBufferLine( str, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
			StockChatBufferLine( INFOMSG_26, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
#else
			sprintf( str, "ＩＰアドレスが[%s]から[%s]に変わりました。", ClientIpAddress, ip );  //MLHIDE
			StockChatBufferLine( str, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
			StockChatBufferLine( "インターネットに再接続された可能性があります。", FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 ); //MLHIDE
#endif
			strcpy( ClientIpAddress, ip );
		}
	}
}


// パッケージバージョンを受信
void nrproto_PV_recv( int fd,int ver )/* ../doc/nrproto.html line 3450 */
{
	// バージョン情报取得
	PackageVer = ver;
}


// パッケージのバージョンアップの结果を受信
void nrproto_PVUP_recv( int fd,int result )/* ../doc/nrproto.html line 3476 */
{
	RecvVerData = result;
	// 今は バージョンアッププロセスかどうか。违ったら无视する。
	if( ProcNo != PROC_UPGRADE )return ;

	if( RecvVerData == 0 ){
		// ０ならバージョンアップ成功
		SubProcNo = 100;	// バージョンアップＯＫ
	}else{
		// それ以外は失败
		SubProcNo = 200;
	}
}


// マクロチェックのパラメータ受信
void nrproto_MAC_recv( int fd,int listmax,int count ) /* ../doc/nrproto.html line 3497 */
{
	giMousePointListMax = listmax;	// マウスポインタリスト
	giMouseMacroCount = count;		// この回数同じ所おしたらアウトの数
	// このパラメータで初期化
	InitMousePointList( );
}

#ifdef PUK2
// 家族加入プロトコル
void nrproto_GC_recv( int fd )
{
	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 现存する古い家族の家族メール内容を消去
	delGuildMailHistory();
	writeGuildMailFile();

}

// 家族情报受信プロトコル
void nrproto_GI_recv( int fd, char *data )
{
	unsigned int bitField;		// 各要素のビット情报
	unsigned int bitMask;		// 取り出すビット位置
	int pickUpId;				// data文字列から取り出す要素の位置番号
	char name[256];
	char roomname[256];
	char monsterName[256];

	int dummy;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	pickUpId = 1;
	bitMask = (1 << 1);

	// 各要素のビット情报を取り出す
	// （このビット情报で取り出す要素が决まる。
	//   0bit目が1なら全パラメータを取り出す。）
	bitField = getInteger62Token( data, DELIMITER, pickUpId );
	pickUpId++;

	// 家族ＩＤ
	if( bitField & (1 | bitMask) )
	{
		guildBook.guildId	= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 家族名
	if( bitField & (1 | bitMask) )
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( name )-1, name );
		makeRecvString( name );
		if( strlen( name ) <= GUILD_NAME_LEN )
			strcpy( guildBook.guildName, name );
		pickUpId++;
	}
	bitMask <<= 1;
	// 家族ルーム名
	if( bitField & (1 | bitMask) )
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( roomname )-1, roomname );
		makeRecvString( roomname );
		if( strlen( roomname ) <= GUILD_NAME_LEN )
			strcpy( guildBook.guildRoomName, roomname );
		pickUpId++;
	}
	bitMask <<= 1;
	// 自分の家族称号番号
	if( bitField & (1 | bitMask) )
	{
		guildBook.pcGuildTitleId	= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 家族情报の更新时间
	if( bitField & (1 | bitMask) )
	{
		dummy				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// ソートの种类
	if( bitField & (1 | bitMask) )
	{
		dummy				= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 家族が存在するサーバー番号
	if( bitField & (1 | bitMask) )
	{
		guildBook.serverNo	= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 本人が持つ权限のビット情报
	if( bitField & (1 | bitMask) )
	{
		guildBook.pcAuthority	= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
	// 家族モンスターの名称、その１～３
	if (bitField & (1 | bitMask))
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( monsterName )-1, monsterName );
		makeRecvString( monsterName );
		if( strlen( monsterName ) <= GUILD_MONSTER_NAME_LEN )
			strcpy( guildBook.monsterName[0], monsterName );
		pickUpId++;
	}
	bitMask <<= 1;
	if (bitField & (1 | bitMask))
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( monsterName )-1, monsterName );
		makeRecvString( monsterName );
		if( strlen( monsterName ) <= GUILD_MONSTER_NAME_LEN )
			strcpy( guildBook.monsterName[1], monsterName );
		pickUpId++;
	}
	bitMask <<= 1;
	if (bitField & (1 | bitMask))
	{
		getStringToken( data, DELIMITER, pickUpId, sizeof( monsterName )-1, monsterName );
		makeRecvString( monsterName );
		if( strlen( monsterName ) <= GUILD_MONSTER_NAME_LEN )
			strcpy( guildBook.monsterName[2], monsterName );
		pickUpId++;
	}
	bitMask <<= 1;
	// 本人の家族内管理番号
	if( bitField & (1 | bitMask) )
	{
		guildBook.pcMemberId	= getIntegerToken( data, DELIMITER, pickUpId );
		pickUpId++;
	}
	bitMask <<= 1;
}

// 家族称号受信プロトコル
void nrproto_GT_recv( int fd, char *data )
{
	int i,index;
	int no;
	int num;
	char name[256];
	int flag;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 先头のトークンは送られていた家族称号の个数
	num = getIntegerToken( data, DELIMITER, 1 );

	// 个数分取得する
	for( i = 0; i < num ; i++ ){
		index = i * 3 + 2;

		// 家族成员番号を取得
		no = getIntegerToken( data, DELIMITER, index + 0 );
		flag = getIntegerToken( data, DELIMITER, index + 1 );

		// この家族称号は使われているか？
		if( flag == -1 ){
			guildBook.title[ no ].flag = 0;
			guildBook.title[ no ].name[ 0 ] = '\0';
		}else{
			getStringToken( data, DELIMITER, ( index + 2 ), sizeof( name ) - 1, name );
			makeRecvString( name );
			// ワークに设定
			if( strlen( name ) <= GUILD_NAME_LEN ){
				guildBook.title[ no ].flag = flag;
				strcpy( guildBook.title[ no ].name, name );
			}
		}
	}

}

// 家族成员受信プロトコル
void nrproto_GM_recv( int fd, char *data )
{
	int i,j,num,useOk;
	int no;
	char name[256];
	char MyName[256];
	char FreeName[256];
	int flag;
	int useFlag;
	GUILD_MEMBER_INFO	*member;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 家族成员情报に书き込む
	member = guildBook.member;

	// バッファをすべてクリア
	for( i=0; i < GUILD_MEMBER_MAX ; i++ ){
		GuildMemberClear(&member[i]);
	}

	//受信データ书き込み
	for( i = 0; i < GUILD_MEMBER_MAX; i++ ){
		no = i * 8;

// 使用フラグ
		useFlag = getIntegerToken( data, DELIMITER, no + 1 );
		if( useFlag <= 0 ){
			GuildMemberClear(&member[i]);
			continue;
		}
		member[i].address.useFlag = 1;

// 名簿番号
		num = getIntegerToken( data, DELIMITER, no + 2 );
		if( num == -1 ){
			break;
		}
		member[i].address.id=num;

// 名称データ
		flag = getStringToken( data, DELIMITER, no + 3, sizeof( name )-1 , name );
		if( flag == 1 )
			break;

		makeStringFromEscaped(name);

//名称
		getStringToken( name, DELIMITER, 1, sizeof( MyName )-1 , MyName );
		makeRecvString(MyName);
		if( strlen( MyName ) <= CHAR_NAME_LEN ){
			strcpy(member[i].address.name,MyName);
		}

//玩家称号
		getStringToken( name, DELIMITER, 2, sizeof( FreeName )-1 , FreeName );
		makeRecvString(FreeName);
		if( strlen( FreeName ) <= CHAR_NAME_LEN ){
			strcpy(member[i].address.freeName,FreeName);
		}

// 家族入会日时
		member[i].joinDate = getIntegerToken( data, DELIMITER, no + 4 );
// 等级
		member[i].address.lv = getIntegerToken( data, DELIMITER, no + 5 );
// ONライン情报
		member[i].address.onlineFlag = (short)getIntegerToken( data, DELIMITER, no + 6 );
// 画像番号
		member[i].address.graNo = getIntegerToken( data, DELIMITER, no + 7 );
// 家族称号ＩＤ
		member[i].titleId = getIntegerToken( data, DELIMITER, no + 8 );
	}

	//家族成员の総数
	guildBook.memberCount=GetGuildMemberCount();

	//家族メールヒストリー中で使用されていない领域を开放します
	for(i=0;i<GUILD_MEMBER_MAX;i++){
		useOk=0;
		for(j=0;j<GUILD_MEMBER_MAX;j++){
			if(mailGuildHistory[selectPcNo][i].ID==guildBook.member[j].address.id)		
				useOk=1;
		}
		if(useOk==0){
			//ヒストリー领域にあるＩＤで家族上に无いＩＤであれば开放します
			mailGuildHistory[selectPcNo][i].useFlag=0;
			mailGuildHistory[selectPcNo][i].ID=0;
			memset(&mailGuildHistory[selectPcNo][i].mail,0,sizeof(mailGuildHistory[0][0].mail));
		}
	}


}

// 家族成员（单体）受信プロトコル
void nrproto_GMI_recv( int fd, int num, char *data )
{
	int no;
	int i;
	GUILD_MEMBER_INFO	*member;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 名簿番号
	no = getIntegerToken( data, DELIMITER, 2 );
	// 家族成员情报に书き込む
	member = guildBook.member;

	//すでに登録されているかチェック
	for( i=0; i < GUILD_MEMBER_MAX ; i++ ){
		if(member[i].address.id == no){
			//登録されているので上书き
			SetGuildMemberStatus(i,data);
			//家族成员の総数
			guildBook.memberCount=GetGuildMemberCount();
			return;
		}
	}

	//新规なのであいているところ探す
	for( i=0; i < GUILD_MEMBER_MAX ; i++ ){
		if(member[i].address.id == -1){
			//登録されていないので新规追加
			SetGuildMemberStatus(i,data);
			//家族成员の総数
			guildBook.memberCount=GetGuildMemberCount();
			return;
		}
	}

	//登録されてはいませんが、空きもありませんでした
	return;

}

//家族成员情报のクリア
void GuildMemberClear(GUILD_MEMBER_INFO	*member){

	member->joinDate = 0;
	member->titleId = 0;

	member->address.name[0] = '\0';
	member->address.freeName[0] = '\0';

	member->address.guildName[0] = '\0';
	member->address.guildTitle[0] = '\0';

	member->address.useFlag = 0;
	member->address.onlineFlag = 0;
	member->address.id = -1;
	member->address.graNo = 0;
	member->address.lv = 0;

}

//家族成员の総数を调べる
int GetGuildMemberCount(void){

	int count,i;
	GUILD_MEMBER_INFO	*member;

	count=0;
	member = guildBook.member;

	for(i=0;i<GUILD_MEMBER_MAX;i++){
		if(member[i].address.useFlag == 1)
			count++;
	}

	return count;
}

//家族成员の设定
BOOL SetGuildMemberStatus(int SetNum,char *data){

	int no;
	char name[256];
	char MyName[256];
	char FreeName[256];
	int useFlag;
	GUILD_MEMBER_INFO	*member;

	//登録ナンバー取得
	no = getIntegerToken( data, DELIMITER, 2 );
	// 家族成员情报に书き込む
	member = guildBook.member;

	// 使用フラグ
	useFlag = getIntegerToken( data, DELIMITER, 1 );
	if( useFlag <= 0 )
	{
		GuildMemberClear(&member[SetNum]);
		return FALSE;
	}
	member[SetNum].address.useFlag = 1;
	member[SetNum].address.id = no;

	// 名称データ
	getStringToken( data, DELIMITER, 3, sizeof( name )-1 , name );

	makeStringFromEscaped(name);

	//名称
	getStringToken( name, DELIMITER, 1, sizeof( MyName )-1 , MyName );
	makeRecvString(MyName);
	if( strlen( MyName ) <= CHAR_NAME_LEN ){
		strcpy(member[SetNum].address.name,MyName);
	}

	//玩家称号
	getStringToken( name, DELIMITER, 2, sizeof( FreeName )-1 , FreeName );
	makeRecvString(FreeName);
	if( strlen( FreeName ) <= CHAR_NAME_LEN ){
		strcpy(member[SetNum].address.freeName,FreeName);
	}

	// 家族入会日时
	member[SetNum].joinDate = getIntegerToken( data, DELIMITER, 4 );
	// 等级
	member[SetNum].address.lv = getIntegerToken( data, DELIMITER, 5 );
	// ONライン情报
	member[SetNum].address.onlineFlag = (short)getIntegerToken( data, DELIMITER, 6 );
	// 画像番号
	member[SetNum].address.graNo = getIntegerToken( data, DELIMITER, 7 );
	// 家族称号ＩＤ
	member[SetNum].titleId = getIntegerToken( data, DELIMITER, 8 );

	return TRUE;
}

// 家族成员削除プロトコル
void nrproto_RGM_recv( int fd, int index )
{
	GUILD_MEMBER_INFO	*member;
	int memberIndex;

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 家族成员情报に书き込む
	member = guildBook.member;

	// メンバーリストを未使用状态に
	memberIndex=getGuildNumFromID(index);
	member[memberIndex].titleId = -1;
	member[memberIndex].address.useFlag = 0;
	member[memberIndex].address.name[0] = '\0';
	member[memberIndex].address.freeName[0] = '\0';
	member[memberIndex].address.id = -1;

	//家族成员の総数
	guildBook.memberCount=GetGuildMemberCount();

	// 一人分のヒストリを削除
	delGuildMailHistoryOnce( index );

}

//-------------------------------------------------------------------------//
// 家族メールを受け取った
void nrproto_GML_recv( int fd, int index, char *data, int color )
{
	char header[32];
	char text[300];
	char str[ 256 ];
	int graNo;
	int memberIndex;

	getStringToken( data, DELIMITER, 1, sizeof( header )-1, header );
	getStringToken( data, DELIMITER, 2, sizeof( text )-1, text );
	graNo = getIntegerToken( data, DELIMITER, 3 );
	// エスケープをはずす。
	makeRecvString( text );

	memberIndex=getGuildNumFromID(index);

	//ヒストリをメモリに保存
	setGuildMailHistory( index, 0, header, text );
	
	if( graNo == -1 ){
		// 通常メール
		sprintf( str, "%s から家族メールが届きました！", guildBook.member[memberIndex].address.name ); //MLHIDE
	}else{
		// ペットメール
		sprintf( str, "%s から家族モンスターメールが届きました！", guildBook.member[memberIndex].address.name ); //MLHIDE
	}

	// ヒストリをファイルに保存
	writeGuildMailFile();

	// 受信音
	play_se( SE_NO_MAIL_RECEIVE, 320, 240 );
	StockChatBufferLine( str, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
}

// 家族消灭受信プロトコル
void nrproto_GD_recv( int fd, int index )
{
	// 自分の家族ＩＤとの照合
	if( guildBook.guildId == index ){
//		GuildInfoInit();
		GuildInfoClear();
	}
}
#endif

#ifdef PUK3

#ifdef PUK3_PROF

void nrproto_PRL_recv( int fd, char *data){

	int i;
	int j=1;
	int k=0;
	int ret;

	for(i=0;i<PROFILE_MENU_MAX;i++){
		categorySys.Sell[i].use = FALSE;
		categorySys.Buy[i].use = FALSE;
	}

	categorySys.SellBuyver=getIntegerToken( data, DELIMITER, j++);
	categorySys.SellBuyCount=0;
	
	while(1){
		categorySys.Sell[k].use = TRUE;
		categorySys.Sell[k].ID = getIntegerToken( data, DELIMITER, j++);
		categorySys.Sell[k].GraphNo = getIntegerToken( data, DELIMITER, j++ );
		ret=getStringToken( data, DELIMITER, j++, sizeof( categorySys.Sell[j].Name )-1, categorySys.Sell[k].Name );
		makeRecvString( categorySys.Sell[k].Name );

		categorySys.Buy[k].use = TRUE;
		categorySys.Buy[k].ID = categorySys.Sell[k].ID;
		categorySys.Buy[k].GraphNo = categorySys.Sell[k].GraphNo;
		strcpy(categorySys.Buy[k].Name,categorySys.Sell[k].Name);			
		
		k++;
		categorySys.SellBuyCount=k;

		if(ret==1)
			break;
	}

}

void nrproto_PRV_recv( int fd, char *data){

	int i=1;
	int NewVersionSellBuy;
	int NewVersionAbout;
	int BitFlag=0;

	//ＳＥＬＬ，ＢＵＹのリストのバージョン
	NewVersionSellBuy = getIntegerToken( data, DELIMITER, i++ );
	//Ａｂｏｕｔのリストのバージョン
	NewVersionAbout = getIntegerToken( data, DELIMITER, i++ );

	//ミニメール代
	categorySys.mailMoney = getIntegerToken( data, DELIMITER, i++ );
	//自分のプロフィールＩＤ
	MyProfile[selectPcNo].userID=getIntegerToken( data, DELIMITER, i++ );


	//SellBuyバージョンチェック
	if(categorySys.SellBuyver!=NewVersionSellBuy){
		BitFlag|=1;
	}

	//Aboutバージョンチェック
	if(categorySys.Aboutver!=NewVersionAbout){
		BitFlag|=2;
	}

	//バージョン违うなら要求
	if(0<BitFlag)
		nrproto_PRV_send(fd,BitFlag);


}

void nrproto_PRE_recv( int fd,char* data ){

	int index,id;
	int i=1;

	id = getIntegerToken( data, DELIMITER, i );

	index=getMiniMailMemIndexfromID(id);

	deleteMiniMailBookMember(index);

}

void nrproto_PRD_recv( int fd,char* name,int lv,char* job,char* title,char* guildname,int graphic,int sid,char* smsg,int bid,char* bmsg,int aid,char* amsg,char* pmsg ){	

	char namework[256];
	char jobwork[256];
	char titlework[256];
	char guildwork[256];
	char bmsgwork[256];
	char smsgwork[256];
	char amsgwork[256];
	char pmsgwork[256];
	int id;

	UserNpcProfile.graphID=graphic;

	strcpy(namework,name);
	makeRecvString( namework );
	strcpy(::UserNpcProfile.name,namework);

	UserNpcProfile.lv=lv;

	strcpy(jobwork,job);
	makeRecvString( jobwork );
	strcpy(::UserNpcProfile.jobName,jobwork);
	
	strcpy(titlework,title);
	makeRecvString( titlework );
	strcpy(::UserNpcProfile.titleName,titlework);
	
	strcpy(guildwork,guildname);
	makeRecvString( guildwork );
	strcpy(::UserNpcProfile.guildName,guildwork);
	
	UserNpcProfile.BuyID=bid;
	strcpy(bmsgwork,bmsg);
	makeRecvString( bmsgwork );
	strcpy(::UserNpcProfile.BuyStr,bmsgwork);

	UserNpcProfile.SellID=sid;
	strcpy(smsgwork,smsg);
	makeRecvString( smsgwork );
	strcpy(::UserNpcProfile.SellStr,smsgwork);

	UserNpcProfile.AboutID=aid;
	strcpy(amsgwork,amsg);
	makeRecvString( amsgwork );
	strcpy(::UserNpcProfile.AboutStr,amsgwork);

	strcpy(pmsgwork,pmsg);
	makeRecvString( pmsgwork );
	strcpy(::UserNpcProfile.ProfileStr,pmsgwork);

	UserNpcProfile.BuyGraphID=GID_ProfCategoryDef;
	UserNpcProfile.SellGraphID=GID_ProfCategoryDef;
	UserNpcProfile.AboutGraphID=GID_ProfCategoryDef;
	
	id=getCategoryGraphicID(UserNpcProfile.BuyID,PROFILE_CATEGORY_SELLBUY);
	if(id!=-1)
		UserNpcProfile.BuyGraphID=id;

	id=getCategoryGraphicID(UserNpcProfile.SellID,PROFILE_CATEGORY_SELLBUY);
	if(id!=-1)
		UserNpcProfile.SellGraphID=id;

	id=getCategoryGraphicID(UserNpcProfile.AboutID,PROFILE_CATEGORY_ABOUT);
	if(id!=-1)
		UserNpcProfile.AboutGraphID=id;

	//ウインドウ开く
	userNpcProfileWinOpen();

}

void nrproto_PRM_recv( int fd,int index,char* text,int color ){

	char header[32];
	char msg[512];
	char str[256];
	int memIndex;

	//相手が登録されているか。いなければそのまま返す
	memIndex=getMiniMailMemIndexfromID(index);
	if(memIndex==-1)
		return;

	getStringToken( text, DELIMITER, 1, sizeof( header )-1, header );
	getStringToken( text, DELIMITER, 2, sizeof( msg )-1, msg );
	// エスケープをはずす。
	makeRecvString( msg );

	// 通常メール
	setMiniMailHistory( memIndex, 0, header, msg );
	// 受信したことをチャット行で知らせる
	sprintf( str, "%s からミニメールが届きました！", miniMailBook[memIndex].name );    //MLHIDE

	// 未読チェック
	checkNoReadMail();

	// 见てるページにデータがきたらページずらす
	if( mailHistorySelectNo == index ){
		if( mailHistoryPage < MAIL_HISTORY_CNT - 1 )
			mailHistoryPage++;
	}

	// 受信音
	play_se( SE_NO_MAIL_RECEIVE, 320, 240 );
	StockChatBufferLine( str, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
}

void nrproto_PRA_recv( int fd,char* data ){

	int i;
	int j=1;
	int k=0;
	int ret;

	for(i=0;i<PROFILE_MENU_MAX;i++){
		categorySys.About[i].use = FALSE;
	}

	categorySys.Aboutver=getIntegerToken( data, DELIMITER, j++);
	categorySys.AboutCount=0;
	
	while(1){
		categorySys.About[k].use = TRUE;
		categorySys.About[k].ID=getIntegerToken( data, DELIMITER, j++);
		categorySys.About[k].GraphNo=getIntegerToken( data, DELIMITER, j++);
		ret=getStringToken( data, DELIMITER, j++, sizeof( categorySys.About[j].Name )-1, categorySys.About[k].Name );
		makeRecvString( categorySys.About[k].Name );

		k++;
		categorySys.AboutCount=k;

		if(ret==1)
			break;
	}
	
}

void nrproto_PRW_recv( int fd,int windowtype,int buttontype,int seqno,int objindex,char* data ){

	// 登出はじめたらサーバからの情报は无视する
	if( logOutFlag )
		return;

	// 简易登出はじめたらサーバからの情报は无视する
	if( SimpleLogoutFlag ) return;

	// 战闘中は无视する
	if( encountNowFlag ) return;

	openServerRequestWindowForProfile( windowtype, buttontype, seqno, objindex, data );

}

void nrproto_PRAD_recv( int fd,char* data ){

	int i=1,j=0;
	int index;
	int ID;
	char name[256];
	char header[256];
	char buff[1024];

	//追加するのに空きがあるか
	index=getMiniMailNoUseMemIndex();
	if(index==-1)
		return;

	//メールの履历を初期化
	for(j=0;j<MINI_MAIL_STOCK_MAX;j++){
		strcpy(miniMailHistory[index].mailInfo[j].buf,"");
		strcpy(miniMailHistory[index].mailInfo[j].header,"");
		miniMailHistory[index].mailInfo[j].readFlag=0;
		miniMailHistory[index].mailInfo[j].sendFlag=0;
	}	

	//追加する人のメール情报
	//使用中フラグ
	miniMailBook[index].useFlag=TRUE;
	//ID
	miniMailBook[index].id=getIntegerToken( data, DELIMITER, i++);
	//颜グラフィック
	miniMailBook[index].graNo=getIntegerToken( data, DELIMITER, i++);
	//等级
	miniMailBook[index].lv=getIntegerToken( data, DELIMITER, i++);
	//名称
	getStringToken( data, DELIMITER, i++, sizeof( miniMailBook[index].name )-1, miniMailBook[index].name );
	makeRecvString( miniMailBook[index].name );
	//家族名
	getStringToken( data, DELIMITER, i++, sizeof( miniMailBook[index].guildName )-1, miniMailBook[index].guildName );
	makeRecvString( miniMailBook[index].guildName );
	//玩家称号
	getStringToken( data, DELIMITER, i++, sizeof( miniMailBook[index].freeName )-1, miniMailBook[index].freeName );
	makeRecvString( miniMailBook[index].freeName );

	//ソート用にカウンタを使用しないOnLineフラグに使用
	miniMailBook[index].onlineFlag=MiniMailBookCounter++;

	//最初のメール部分
	//送信者のインデックス
	ID=getIntegerToken( data, DELIMITER, i++);
	//ヘッダ情报
	getStringToken( data, DELIMITER, i++, sizeof( header )-1, header );
	makeRecvString( header );
	//信息
	getStringToken( data, DELIMITER, i++, sizeof( buff )-1, buff );
	makeRecvString( buff );
	//文字色（未使用）
//	miniMailBook[0].id=getIntegerToken( data, DELIMITER, i++);	

	makeLocalTime(header);
	if(MyProfile[selectPcNo].userID==ID){
		//自分からのメールでした！
		setMiniMailHistory(index,1,header,buff);
	}else{
		//他人からのメールです！
		setMiniMailHistory(index,0,header,buff);
		//他人からなので着信ログ出します
		// 受信音
		play_se( SE_NO_MAIL_RECEIVE, 320, 240 );
		// 受信したことをチャット行で知らせる
		sprintf( name, "%s からミニメールが届きました！", miniMailBook[index].name );     //MLHIDE
		StockChatBufferLine( name, FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
	}
}

#endif

#endif


// ニューエストチェック ****************************************************************************


// ニューエストソケットファイルディスクプリタ
int NewestSockfd;
int NewestRecvSize;	// newest を読み込んだサイズ
int urlopen_mode;	// URLOPENモードか

// ニューエストファイルネーム
#ifdef PUK2_NEWEST_CHANGE
	#ifdef PUK2_IPCHANGE
		char newestFileName[256] = "/update/newest.txt";                    //MLHIDE
	#else
		char newestFileName[256] = "/update/newest.txt";                    //MLHIDE
	#endif
#else
#ifdef PUK2_IPCHANGE
char newestFileName[256] = "/update/newest.txt";                      //MLHIDE
#else
char newestFileName[256] = "/update/newest.txt";                      //MLHIDE
#endif
#endif
#ifdef PUK2_NEWEST_BUFF_OVER_0
static char NewestRecvBuffer[ PUK2_NEWESTSIZE ];
#else
static char NewestRecvBuffer[ 8192 ];
#endif
#define DOWN_SIZE	2048

// ニューエストネットワーク初期化　***********************************************************/
#ifdef PUK3_LOGIN_VERCHECK
int NewestInitNetWork( void )
#else
BOOL NewestInitNetWork( void )
#endif
{
	int ret;
	WSADATA wsadata;	// 	ウィンソック
	HINTERNET hInternet;
	HINTERNET hFile;
	char hostName[256];
	char ipPortStr[ 256 ];

	//ファイル名作成
/*
#ifdef PUK2_IPCHANGE
	sprintf( hostName, "http://litchi-blz.3322.org%s", newestFileName );
#else
	#ifdef PUK2_IP_MISS
		sprintf( hostName, "http://litchi-blz.3322.org%s", newestFileName );
	#else
		sprintf( hostName, "http://litchi-blz.3322.org%s", newestFileName );
	#endif
#endif
*/
sprintf( hostName, "http://localhost%s", newestFileName );            //MLHIDE

	/* WININET初期化 */
	hInternet = InternetOpen(
		"CROSSGATE",                                                        //MLHIDE
		INTERNET_OPEN_TYPE_PRECONFIG,
		NULL,
		NULL,
		0);

	//エラーなら
	if(hInternet == NULL){
		//通常モードへ
		goto NewestInitNetWork_100;
	}

	/* URLのオープン */
	hFile = InternetOpenUrl(
		hInternet,
		hostName,
		NULL,
		0,
		INTERNET_FLAG_RELOAD,
		0);

	//エラーなら
	if(hFile == NULL){
		InternetCloseHandle(hInternet);
		//通常モードへ
		goto NewestInitNetWork_100;
	}

	NewestRecvSize = 0;
	DWORD ReadSize;
	BOOL bResult;
#ifdef PUK3_LOGIN_VERCHECK
	char ClientStr[256];
#endif

	while( 1 ){
		/* オープンしたURLからデータを(2048バイトずつ)読み込む */
		ReadSize = DOWN_SIZE;
#ifdef PUK2_NEWEST_BUFF_OVER_0
		if ( NewestRecvSize + ReadSize > sizeof(NewestRecvBuffer) ){
			/* 后处理 */
			InternetCloseHandle(hFile);
			InternetCloseHandle(hInternet);
	#ifdef PUK3_ERRORMESSAGE_NUM
			MessageBox( hWnd, ERRMSG_105, "NewestInitNetWork", MB_OK | MB_ICONSTOP ); //MLHIDE
	#else
			MessageBox( hWnd, "newest.txt init error", "NewestInitNetWork", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
	#ifdef PUK2_NEWEST_SIZE_TEST
			// サイズチェックしたかっただけなので、无理やりＯＫにする
			//URLオープンモードセット
			urlopen_mode = 1;
		#ifdef PUK3_LOGIN_VERCHECK
			return 1;
		#else
			return TRUE;
		#endif
	#else
		#ifdef PUK3_LOGIN_VERCHECK
			return 0;
		#else
			return FALSE;
		#endif
	#endif
		}
#endif

		bResult = InternetReadFile(
			hFile,
			NewestRecvBuffer + NewestRecvSize,
			DOWN_SIZE,
			&ReadSize);

		//エラーなら
		if(bResult == NULL){
			/* 后处理 */
			InternetCloseHandle(hFile);
			InternetCloseHandle(hInternet);
			//通常モードへ
			goto NewestInitNetWork_100;
		}

		/* 全て読み込んだら終了 */
		if(bResult && (ReadSize == 0)){
			/* 后处理 */
			InternetCloseHandle(hFile);
			InternetCloseHandle(hInternet);
			NewestRecvBuffer[ NewestRecvSize ] = 0;
#ifdef PUK3_LOGIN_VERCHECK
			// クライアントのダウンロードが自分のバージョンと一致しているか？
	#ifdef PUK3_LOGIN_VERCHECK_BAG2
			sprintf( ClientStr, "EXE:Cg_%d%02d.exe:", XGVerNum[1], XGVerNum[2] ); //MLHIDE
	#else
			sprintf( ClientStr, "EXE:Cg_%d%d.exe:", XGVerNum[1], XGVerNum[2] ); //MLHIDE
	#endif
	#ifdef PUK3_LOGIN_VERCHECK_BAG
			// このままだとnewestがないときバージョンチェックの文章が出ちゃうので移动
	#else
			// newestが合ってないなら
			if ( !strstr( NewestRecvBuffer, ClientStr ) ){
				return -1;
			}
	#endif
#endif
			// 接続サーバーのＩＰとポート番号文字列作成。
			sprintf( ipPortStr, "%s:%s", gmsv[ selectServerIndex ].ipaddr, gmsv[ selectServerIndex ].port ); //MLHIDE
//#ifdef PUK2_NEWEST_SIZE_TEST
#if 0
			// サイズチェックしたかっただけなので、无理やりＯＫにする
			//URLオープンモードセット
			urlopen_mode = 1;
		#ifdef PUK3_LOGIN_VERCHECK
			#ifdef PUK3_LOGIN_VERCHECK_BAG
				// newestが合ってないなら
				if ( !strstr( NewestRecvBuffer, ClientStr ) ){
					return -1;
				}else{
					return 1;
				}
			#else
			return 1;
			#endif
		#else
			return TRUE;
		#endif
#else
			//newestが合ってるなら
			if( strstr( NewestRecvBuffer, ipPortStr ) ){
				//URLオープンモードセット
				urlopen_mode = 1;
		#ifdef PUK3_LOGIN_VERCHECK
			#ifdef PUK3_LOGIN_VERCHECK_BAG
				// newestが合ってないなら
				if ( !strstr( NewestRecvBuffer, ClientStr ) ){
					return -1;
				}else{
					return 1;
				}
			#else
				return 1;
			#endif
		#else
				return TRUE;
		#endif
			} else {
				//通常モードへ
				goto NewestInitNetWork_100;
			}
#endif
		}

		/* まだ続く */
		NewestRecvSize += ReadSize;
	}

NewestInitNetWork_100:
	//URLオープンモードクリア
	urlopen_mode = 0;

	// winsockを初期化する
    ret = WSAStartup( MAKEWORD( 1, 1 ), &wsadata );
#ifdef PUK3_LOGIN_VERCHECK
	if( ret != 0 ) return 0;
#else
	if( ret != 0 ) return FALSE;
#endif

	// nrprotoの初期化
	//nrproto_InitClient( appendWriteBuf, 65536, sockfd );

	// 読み込みバイト数初期化。
	NewestRecvSize = 0;
	// バッファ初期化。
	NewestRecvBuffer[0] = 0;
#ifdef PUK3_LOGIN_VERCHECK
	return 1;
#else
	return TRUE;
#endif
}


// ニューエスト接続处理******************************************************************************
//
//  实行前に selectServerIndex 、userId 、userPassword を设定しておく
//
//  戾り值：	 0 ... 处理中
//				 1 ... 接続完了
//				-1 ... タイムアウト
//				-2 ... 指定されたサーバ名が无い(普通はこれはおこらない)。
//				-3 ... ソケット作成に失败した。
//				-4 ... IPアドレスの变换に失败した。
//				-5 ... connectに失败した。
//				-6 ... selectに失败した。
//				-7 ... クライアントログインに失败した。
int NewestConnectServer( void )
{
	//URLオープンモードなら
	if( urlopen_mode ){
		return 1;
	}

	// ソケットはノンブロッキングになってるので、その状态でconnectする
	//char hostName[] = { "sakura" }; // ホストネーム
	//char hostName[] = { "mas.ath.cx" }; // ホストネーム
	//char hostName[] = { "crossgate.enix.isao.net" }; // ホストネーム
#ifdef PUK2_IPCHANGE
	char hostName[] = PUK2_DEBUGIP; // ホストネーム
#else
	char hostName[] = "localhost"; // ホストネーム                             //MLHIDE
#endif
	short portNum = 80;	// ポート番号

    // ソケットをつくる
	NewestSockfd = socket( AF_INET , SOCK_STREAM ,0 );
	
	// エラー处理
	if( NewestSockfd == INVALID_SOCKET ){
		// エラー处理
		//sprintf( netprocErrmsg, NET_ERRMSG_SOCKETERROR );
		MessageBox( hWnd, "Socket连接失败。", "确认", MB_OK | MB_ICONSTOP );       //MLHIDE
		//connectServerCounter = 0;
		return -3;
	}

	struct sockaddr_in sin;	// 接続先情报记忆构造体
	struct hostent *h;		// ホストの情报构造体
	
	sin.sin_family = AF_INET; // ＩＰヴァージョン４をつかう、今のネットＩＰと同じ。
	sin.sin_port = htons( portNum );	// ポート番号
	sin.sin_addr.s_addr = inet_addr( hostName );     // ホストＩＰアドレス文字列を３２ビット数值に变换
	
	// ＩＰアドレスじゃなかったとき（たとえばsakura)
	if( sin.sin_addr.s_addr == -1 )
	{	
		// 名称からホスト情报构造体（ＩＰ含む）をもらう、ＤＮＳに闻きにいく
		h = gethostbyname( hostName );
		// 失败したら
		if( h == NULL )
		{
			// エラー处理
			//sprintf( netprocErrmsg, NET_ERRMSG_NOTGETADDR, hostName );
			////MessageBox( hWnd, "Host的IP变了。", "确认", MB_OK | MB_ICONSTOP );
			closesocket( NewestSockfd ); 
			//connectServerCounter = 0;
			return -4;
		}
		
		// ＩＰをコピーする（３２ビットに变换济み）
		memcpy( (void*) &sin.sin_addr.s_addr , h->h_addr, sizeof( struct in_addr ) );
	}

	// サーバー接続する（コネクション张る）（３ウェイハンドシェークと呼ばれている）
	int ret = connect( NewestSockfd, (struct sockaddr*)&sin , sizeof( sin ) );
	
	// エラー处理
	if( ret == SOCKET_ERROR )
	{
		if( WSAGetLastError() == WSAEWOULDBLOCK )
		{
			// 本来はブロックするはずだったんやけど
        }
		else
		{
			closesocket( NewestSockfd );
			// エラー处理
			//sprintf( netprocErrmsg, NET_ERRMSG_NOTCONNECT_S );
		////MessageBox( hWnd, "连接失败。", "确认", MB_OK | MB_ICONSTOP );
			return -5;
		}
	}
	
	//MessageBox( hWnd, "连接成功。", "确认", MB_OK | MB_ICONSTOP );
	
	return 1;
}


// 送信处理 *************************************************/
void NewestSendMessege( void )
{
	//URLオープンモードなら
	if( urlopen_mode ){
		return;
	}

	//char str[] = { "GET /cgi-bin/all_link.cgi?user=mas&score=10000\n\n" };
	char str[ 256 ];
	
	// 送信文字列作成
	sprintf( str,"GET %s HTTP/1.0\n\n", newestFileName );                //MLHIDE
	
	// 送る
	send( NewestSockfd, str, strlen( str ), 0 );
}


// 受信处理 *************************************************/
int NewestReceiveMessege( void )
{
	//URLオープンモードなら
	if( urlopen_mode ){
		return TRUE;
	}

	//char buf[ 4096 ];
#ifdef PUK2_NEWEST_BUFF_OVER_0
	char buf[ PUK2_NEWESTSIZE ];
#else
	char buf[ 8192 ];
#endif
	int len;
	fd_set readFds;			// 読み込みようＦＤ构造体
	struct timeval timeVal;	// タイムアウト设定构造体
	
	// おそらく待たない设定だろう
	timeVal.tv_sec = 0;
	timeVal.tv_usec = 0;
	
	// 构造体初期化
	FD_ZERO( &readFds );
	// fd をセットする
	FD_SET( NewestSockfd, &readFds );
	
	// データが到着しているかチェック
	select( 0, &readFds, NULL, NULL, &timeVal );
	
	// 结果を见る
	if( FD_ISSET( NewestSockfd, &readFds ) ){
		char ipPortStr[ 256 ];
#ifdef PUK3_LOGIN_VERCHECK
		char ClientStr[256];
		// クライアントのバージョン
		sprintf( ClientStr, "EXE:Cg_%d%d.exe:", XGVerNum[1], XGVerNum[2] ); //MLHIDE
#endif
		
		// 接続サーバーのＩＰとポート番号文字列作成。
		sprintf( ipPortStr, "%s:%s", gmsv[ selectServerIndex ].ipaddr, gmsv[ selectServerIndex ].port ); //MLHIDE
		
		// 文字列の受信
		len = recv( NewestSockfd, buf, sizeof( buf ) - 1 , 0 );
		
		if( len == -1 ) return FALSE;
		// 読み込みサイズ加算。
		if( len > 0 ){
#ifdef PUK2_NEWEST_BUFF_OVER
			// バッファサイズ超えるなら
			if ( NewestRecvSize + len >= sizeof( NewestRecvBuffer ) ) {
				len = ( sizeof( NewestRecvBuffer ) - 1 ) - NewestRecvSize;
				// 文字列追加。
				strncat( NewestRecvBuffer, buf, len );

				NewestRecvSize += len;
				// ＮＵＬＬにしておく。
				NewestRecvBuffer[ NewestRecvSize ] = NULL;

				return -1;
			}else{
				// 文字列追加。
				strncat( NewestRecvBuffer, buf, len );

				NewestRecvSize += len;
				// ＮＵＬＬにしておく。
				NewestRecvBuffer[ NewestRecvSize ] = NULL;
			}
#else
			NewestRecvSize += len;
			// バッファサイズ超えないように。
			if( NewestRecvSize >= sizeof( NewestRecvBuffer ) ) {
				NewestRecvSize = sizeof( NewestRecvBuffer )-1;
			}
			// 文字列追加。
			strncat( NewestRecvBuffer, buf, sizeof( NewestRecvBuffer ) -1 );
			// ＮＵＬＬにしておく。
			NewestRecvBuffer[ NewestRecvSize ] = NULL;
#endif
		}
#ifdef _DEBUG
		// アペンドログ
//		appendLogMessage( NewestRecvBuffer );
#endif
		// 最后まで読んだら判定する。
		if( len == 0 ){
			// 取り出す文字列の先头を求める
			//if( strstr( buf, "404 Not Found" ) ) return -1;
			//if( strstr( buf, "MESSAGE:" ) ) return TRUE;
#ifdef PUK3_LOGIN_VERCHECK_BAG
			if( strstr( NewestRecvBuffer, ipPortStr ) ){
				if( !strstr( NewestRecvBuffer, ClientStr ) ){
					return -2;
				}else{
					return TRUE;
				}
			}
			else return -1;
#else
#ifdef PUK3_LOGIN_VERCHECK
			if( !strstr( NewestRecvBuffer, ClientStr ) ) return -2;
#endif
			if( strstr( NewestRecvBuffer, ipPortStr ) ) return TRUE;
			else return -1;
#endif
			//if( strstr( buf, "404" ) ) return FALSE;
		}
	}
	
	return FALSE;
}


// ニューエストネットワーク終了 *********************************************/
void NewestEndNetWork( void )
{
	//URLオープンモードなら
	if( urlopen_mode ){
		return;
	}

	// ソケットを关闭
	closesocket( NewestSockfd );
	// winsockをクリーンナップ
	WSACleanup();
}



