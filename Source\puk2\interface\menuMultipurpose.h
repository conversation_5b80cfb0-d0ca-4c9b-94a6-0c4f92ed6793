﻿typedef struct {
	unsigned int type;
	int size;
	int x;
	int y;

	BOOL (*funcInitialize) (int mouse);
	BOOL (*funcYes) (int, unsigned int);
	BOOL (*funcNo) (int, unsigned int);
	BOOL (*funcOk) (int, unsigned int);
	BOOL (*funcCancel) (int, unsigned int);
	BOOL (*funcPrev) (int, unsigned int);
	BOOL (*funcNext) (int, unsigned int);
	char content[1024];
} MULTI_PURPOSE_WINDOW;

MULTI_PURPOSE_WINDOW multiPurposeWindowInfo;


enum {
	EnumMultiPurposeTiny = 3,
	EnumMultiPurposeSmall = 4,
	EnumMultiPurposeMiddle = 5,
	EnumMultiPurposeBig = 6,
	EnumMultiPurposeNA = 7,		// not support
	EnumMultiPurposeHuge = 8,
};

enum {
	EnumWithYesButton = 1 << 0,
	EnumWithNoButton = 1 << 1,
	EnumWithOkButton = 1 << 2,
	EnumWithCancelButton = 1 << 3,
	EnumWithPrevButton = 1 << 4,
	EnumWithNextButton = 1 << 5,
	EnumWithYesNoButton = EnumWithYesButton | EnumWithNoButton,
	EnumWithOkCancelButton = EnumWithOkButton | EnumWithCancelButton,
	EnumWithPrevNextButton = EnumWithPrevButton | EnumWithNextButton,
};

BOOL MenuWindowMultiPurpose (int mouse);
BOOL MenuWindowMultiPurposeDraw (int mouse);
BOOL MenuWindowMultiPurposeYes (int no, unsigned int flag);
BOOL MenuWindowMultiPurposeNo (int no, unsigned int flag);
BOOL MenuWindowMultiPurposeOk (int no, unsigned int flag);
BOOL MenuWindowMultiPurposeCancel (int no, unsigned int flag);
BOOL MenuWindowMultiPurposePrev (int no, unsigned int flag);
BOOL MenuWindowMultiPurposeNext (int no, unsigned int flag);

#define WIDTH_OF_FRAME 74

// frame
#define GID_multiPurposeTopLeft 245300
#define GID_multiPurposeTop 245301
#define GID_multiPurposeTopRight 245302
#define GID_multiPurposeLeft 245303
#define GID_multiPurposeBase 245304
#define GID_multiPurposeRight 245305
#define GID_multiPurposeBottomLeft 245306
#define GID_multiPurposeBottom 245307
#define GID_multiPurposeBottomRight 245308

#define GID_multiPurposeOkButtonOn 243048
#define GID_multiPurposeOkButtonOff 243049
#define GID_multiPurposeOkButtonOver 243050
#define GID_multiPurposeCancelButtonOn 245320
#define GID_multiPurposeCancelButtonOff 245321
#define GID_multiPurposeCancelButtonOver 245322
#define GID_multiPurposeYesButtonOn 245323
#define GID_multiPurposeYesButtonOff 245324
#define GID_multiPurposeYesButtonOver 245325
#define GID_multiPurposeNoButtonOn 245326
#define GID_multiPurposeNoButtonOff 245327
#define GID_multiPurposeNoButtonOver 245328
#define GID_multiPurposeNextButtonOn 245329
#define GID_multiPurposeNextButtonOff 245330
#define GID_multiPurposeNextButtonOver 245331
#define GID_multiPurposeBackButtonOn 245332
#define GID_multiPurposeBackButtonOff 245333
#define GID_multiPurposeBackButtonOver 245334

GRAPHIC_SWITCH MenuWindowMultiPurposeGraph [] = {

	{GID_multiPurposeTopLeft,     0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeTop,         0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeTopRight,    0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeLeft,        0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeBase,        0, 0, 74, 54,0x80ffffff},
	{GID_multiPurposeRight,       0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeBottomLeft,  0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeBottom,      0, 0, 74, 54,0xffffffff},
	{GID_multiPurposeBottomRight, 0, 0, 74, 54,0xffffffff},

	{GID_multiPurposeOkButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_multiPurposeCancelButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_multiPurposeYesButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_multiPurposeNoButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_multiPurposeNextButtonOn, 0, 0, 0, 0, 0xffffffff},
	{GID_multiPurposeBackButtonOn, 0, 0, 0, 0, 0xffffffff}};

TEXT_SWITCH MenuWindowMultiPurposeText [] = {
	{FONT_PAL_WHITE, FONT_KIND_SMALL, "content"}};                       //MLHIDE

enum {
	EnumMultiPurposeContent0,
	EnumMultiPurposeContent1,
	EnumMultiPurposeContent2,
	EnumMultiPurposeContent3,
	EnumMultiPurposeContent4,
	EnumMultiPurposeContent5,
	EnumMultiPurposeContent6,
	EnumMultiPurposeContent7,
	EnumMultiPurposeContent8,
	EnumMultiPurposeContent9,
	EnumMultiPurposeContent10,
	EnumMultiPurposeEndOfContent,
	EnumMultiPurposeOk = EnumMultiPurposeEndOfContent,
	EnumMultiPurposeCancel,
	EnumMultiPurposeYes,
	EnumMultiPurposeNo,
	EnumMultiPurposeNext,
	EnumMultiPurposeBack,
	EnumMultiPurposeFrameTopLeft,
	EnumMultiPurposeFrameTop0,
	EnumMultiPurposeFrameTopRight,
	EnumMultiPurposeFrameLeft0,
	EnumMultiPurposeFrameRight0,
	EnumMultiPurposeFrameBottomLeft,
	EnumMultiPurposeFrameBottom0,
	EnumMultiPurposeFrameBottomRight,
	EnumMultiPurposeFrameBase0,
	EnumMultiPurposeFrameBase1,
	EnumMultiPurposeFrameBase2,
	EnumMultiPurposeFrameBase3,
	EnumMultiPurposeFrameBase4,
	EnumMultiPurposeFrameBase5,
	EnumMultiPurposeFrameBase6,
	EnumMultiPurposeFrameBase7,
	EnumMultiPurposeFrameBase8,
	EnumMultiPurposeEnd};

static SWITCH_DATA MenuWindowMultiPurposeSwitch [] = {
// textBlock
	{SWITCH_TEXT, 30, 10+13* 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 1, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 2, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 3, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 4, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 5, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 6, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 7, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 8, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13* 9, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_TEXT, 30, 10+13*10, 0, 0,  FALSE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
// buttonBlock
//	{SWITCH_GRAPHIC, 15, 130, 66, 17, TRUE, &MenuWindowMultiPurposeGraph[9], MenuWindowMultiPurposeYes},
//	{SWITCH_GRAPHIC, 81, 130, 66, 17, TRUE, &MenuWindowMultiPurposeGraph[10], MenuWindowMultiPurposeNo},
	{SWITCH_GRAPHIC, 45, 130, 66, 17, TRUE, &MenuWindowMultiPurposeGraph[9], MenuWindowMultiPurposeYes},
	{SWITCH_GRAPHIC,111, 130, 66, 17, TRUE, &MenuWindowMultiPurposeGraph[10], MenuWindowMultiPurposeNo},
	{SWITCH_GRAPHIC, 15, 130, 66, 17, FALSE, &MenuWindowMultiPurposeGraph[11], MenuWindowMultiPurposeOk},
	{SWITCH_GRAPHIC, 81, 130, 66, 17, FALSE, &MenuWindowMultiPurposeGraph[12], MenuWindowMultiPurposeCancel},
	{SWITCH_GRAPHIC, 15, 130, 66, 17, FALSE, &MenuWindowMultiPurposeGraph[13], MenuWindowMultiPurposePrev},
	{SWITCH_GRAPHIC, 81, 130, 66, 17, FALSE, &MenuWindowMultiPurposeGraph[14], MenuWindowMultiPurposeNext},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 74, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 148, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[2], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 54, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 215, 54, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 108, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[6], MenuSwitchNone},
	{SWITCH_GRAPHIC, 74, 156, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 148, 108, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[8], MenuSwitchNone},
// baseBlock
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 74, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 148, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 54, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 74, 54, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 148, 54, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 108, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 74, 108, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 148, 108, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone}};

const WINDOW_DATA WindowDataMenuMultiPurpose = {
	0,
		4, 209, 159, 222, 162 , 0x80010101, EnumMultiPurposeEnd, MenuWindowMultiPurposeSwitch, MenuWindowMultiPurpose, MenuWindowMultiPurposeDraw,MenuWindowDel
};




/*
enum {
	EnumMultiPurposeOk,
	EnumMultiPurposeCancel,
	EnumMultiPurposeYes,
	EnumMultiPurposeNo,
	EnumMultiPurposeNext,
	EnumMultiPurposeBack,
	EnumMultiPurposeContent0,
	EnumMultiPurposeContent1,
	EnumMultiPurposeContent2,
	EnumMultiPurposeContent3,
	EnumMultiPurposeContent4,
	EnumMultiPurposeContent5,
	EnumMultiPurposeContent6,
	EnumMultiPurposeContent7,
	EnumMultiPurposeContent8,
	EnumMultiPurposeContent9,
	EnumMultiPurposeContent10,
	EnumMultiPurposeContent11,
	EnumMultiPurposeContent12,
	EnumMultiPurposeContent13,
	EnumMultiPurposeContent14,
	EnumMultiPurposeContent15,
	EnumMultiPurposeContent16,
	EnumMultiPurposeContent17,
	EnumMultiPurposeContent18,
	EnumMultiPurposeContent19,
	EnumMultiPurposeContent20,
	EnumMultiPurposeContent21,
	EnumMultiPurposeContent22,
	EnumMultiPurposeContent23,
	EnumMultiPurposeContent24,
	EnumMultiPurposeContent25,
	EnumMultiPurposeContent26,
	EnumMultiPurposeContent27,
	EnumMultiPurposeContent28,
	EnumMultiPurposeContent29,
	EnumMultiPurposeContent30,
	EnumMultiPurposeContent31,
	EnumMultiPurposeContent32,
	EnumMultiPurposeContent33,
	EnumMultiPurposeContent34,
	EnumMultiPurposeFrameTopLeft,
	EnumMultiPurposeFrameTop0,
	EnumMultiPurposeFrameTop1,
	EnumMultiPurposeFrameTop2,
	EnumMultiPurposeFrameTop3,
	EnumMultiPurposeFrameTop4,
	EnumMultiPurposeFrameTop5,
	EnumMultiPurposeFrameTopRight,
	EnumMultiPurposeFrameLeft0,
	EnumMultiPurposeFrameLeft1,
	EnumMultiPurposeFrameLeft2,
	EnumMultiPurposeFrameLeft3,
	EnumMultiPurposeFrameLeft4,
	EnumMultiPurposeFrameLeft5,
	EnumMultiPurposeFrameRight0,
	EnumMultiPurposeFrameRight1,
	EnumMultiPurposeFrameRight2,
	EnumMultiPurposeFrameRight3,
	EnumMultiPurposeFrameRight4,
	EnumMultiPurposeFrameRight5,
	EnumMultiPurposeFrameBottomLeft,
	EnumMultiPurposeFrameBottom0,
	EnumMultiPurposeFrameBottom1,
	EnumMultiPurposeFrameBottom2,
	EnumMultiPurposeFrameBottom3,
	EnumMultiPurposeFrameBottom4,
	EnumMultiPurposeFrameBottom5,
	EnumMultiPurposeFrameBottomRight,
	EnumMultiPurposeFrameBase0,
	EnumMultiPurposeFrameBase1,
	EnumMultiPurposeFrameBase2,
	EnumMultiPurposeFrameBase3,
	EnumMultiPurposeFrameBase4,
	EnumMultiPurposeFrameBase5,
	EnumMultiPurposeFrameBase6,
	EnumMultiPurposeFrameBase7,
	EnumMultiPurposeFrameBase8,
	EnumMultiPurposeFrameBase9,
	EnumMultiPurposeFrameMask};

static SWITCH_DATA MenuWindowMultiPurposeSwitch [] = {
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[1], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[2], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[3], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[5], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[6], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[7], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[8], MenuSwitchNone},
// textBlock
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0,  TRUE, &MenuWindowMultiPurposeText[0], MenuSwitchNone},
// buttonBlock
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[9], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[10], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[11], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[12], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[13], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[14], MenuSwitchNone},
// baseBlock
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone},
	{SWITCH_GRAPHIC, 0, 0, 0, 0, TRUE, &MenuWindowMultiPurposeGraph[4], MenuSwitchNone}};
*/
