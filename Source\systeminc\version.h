﻿#ifndef __VERSION_H__
#define __VERSION_H__

/* -------------------------------------------------------------------
* 语言版本
*/
#define MULTI_LANG                  //开启多国语言
#ifdef MULTI_LANG
#define LANG_CN                     //简体中文
//#define LANG_TW                     //繁体中文
#endif

/* -------------------------------------------------------------------
* 台服版本相关功能与显示
*/
#define VERSION_TW                  //台服版本
#ifdef VERSION_TW
#define TW_CHARA                    //台服新人物
#define TW_BATTLE_COMMAND           //台服战斗协议
#define TECH_BLASTWAVE			    //技能追月
#endif

/* -------------------------------------------------------------------
* DLL相关
*/
//#define CGMSV_DLL                 //DLL
#ifdef CGMSV_DLL
//封包处理
typedef char* (__stdcall* lpPacketProcessFun)(const char* a);
extern lpPacketProcessFun SendPacketProcessFun;
extern lpPacketProcessFun RecvPacketProcessFun;
#endif

/* -------------------------------------------------------------------
* 动画图档扩展
*/
#define BIN_EXPAND                  //载入图档文件扩展
#define ANIM_NUM_EXPAND             //扩容动画数量

/* -------------------------------------------------------------------
* 自定义窗口分辨率
*/
#define WIN_SIZE_DEF                //自定义窗口分辨率
//不使用自定义窗口分辨率时需要设置0
//按窗口分辨率对称偏移
extern int SymOffsetX, SymOffsetY;
//按窗口分辨率比例偏移
extern int ScaleOffsetX, ScaleOffsetY;

/* -------------------------------------------------------------------
* NPC显名
*/
#define NPC_SHOW_NAME

#endif