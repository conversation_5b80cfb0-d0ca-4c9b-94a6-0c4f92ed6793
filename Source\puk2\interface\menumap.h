﻿//右コクピット＞マップ

#ifndef _MENUMAP_H_
#define _MENUMAP_H_

// ボタン处理关数 *********************//
BOOL MenuMapSwitchClose( int no, unsigned int flag );
BOOL MenuSwitchMapDisp( int no, unsigned int flag );
BOOL MenuSwitchEastText( int no, unsigned int flag );
BOOL MenuSwitchSouthText( int no, unsigned int flag );


BOOL MenuWindowMap( int mouse );
BOOL MenuWindowMapDraw( int mouse );

GRAPHIC_SWITCH MenuWindowMapGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_MapBase,0,0,0,0,0xFFFFFFFF},			//ベース
	{GID_MapBack,0,0,0,0,0x80FFFFFF},			//背景
};

TEXT_SWITCH MenuWindowMapText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,""},	
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,""},	
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,""},	
};


// マップスイッチ
static SWITCH_DATA MapSwitch[] = {
{ SWITCH_GRAPHIC,165,  4,  11, 11, TRUE, &MenuWindowMapGraph[0], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_TEXT,	  51, 21,   0,  0, TRUE, &MenuWindowMapText[0],	 MenuSwitchEastText },		//东座标
{ SWITCH_TEXT,	 159, 21,   0,  0, TRUE, &MenuWindowMapText[1],  MenuSwitchSouthText },		//南座标

{ SWITCH_TEXT,	 160,138,   0,  0, TRUE, &MenuWindowMapText[2],  MenuSwitchNone },			//マップ名

{ SWITCH_NONE,	 -19, 81,   0,  0, TRUE, NULL,					 MenuSwitchMapDisp },		//オートマップ

{ SWITCH_GRAPHIC,-16,  0,   0,  0, TRUE, &MenuWindowMapGraph[1], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC,  4, 16,   0,  0, TRUE, &MenuWindowMapGraph[2], MenuSwitchNone },			//背景
};

enum{
	EnumMapGraphClose,

	EnumMapEastText,
	EnumMapSouthText,

	EnumMapName,

	EnumMapAutoMap,

	EnumMapGraphBase,
	EnumMapGraphWindow,

	EnumMapEnd,
};


const WINDOW_DATA WindowDataMap = {
 0,															// メニューウィンドウ
     4,   353,  55,184,21, 0x80010101,  EnumMapEnd,  MapSwitch, MenuWindowMap,MenuWindowMapDraw,MenuWindowDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateMap={
	1,
	 0,  0,184, 21,
};

ACTION *openMapMenuWindow( unsigned char flg, char opentype, ACTION **ret = NULL );

#endif