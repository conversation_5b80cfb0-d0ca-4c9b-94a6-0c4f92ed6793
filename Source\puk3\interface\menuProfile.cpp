﻿//プロフィールウインドウ
#include "../profile/profile.h"

#define PROF_CATEGORY_LIST_LINE 10

void MenuWindowProfCategoryReDraw( void );

int ProfCategoryNo;
#ifdef PUK3_MAIL_ETC
	extern int ProfileBBS4BfId;
#endif

//ログイン时の初期化
void InitMenuWindowMenuProfileInLogin(void){
		
	MiniMailBookCounter=0;
#ifdef PUK3_MAIL_ETC
	ProfileBBS4BfId = -1;
#endif

}

//--------------------------------------------------------
//分类リストウインドウ
//--------------------------------------------------------
BOOL MenuWindowProfCategory( int mouse )
{

	if (mouse==WIN_INIT){
		ProfCategoryNo=0;
	}

	if(WindowFlag[MENU_WINDOW_PROFILE].wininfo==NULL){
		wI->flag |= WIN_INFO_DEL;
	}

	MenuWindowProfCategoryReDraw();

	return TRUE;
}

BOOL MenuWindowProfCategoryDraw( int mouse ){

	displayMenuWindow();

	return TRUE;

}

//パネルスイッチ
BOOL MenuSwitchProfCategoryPanel( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	if( flag & MENU_MOUSE_OVER ){
		switch(ProfileListMode){
			case ProfileListModeSell:
				strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_SELL );
				break;
			case ProfileListModeBuy:
				strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_BUY );
				break;
			case ProfileListModeAbout:
				strcpy( OneLineInfoStr, MWONELINE_MINIMAIL_ABOUT );
				break;
		}
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOver;
		ReturnFlag=TRUE;	
	}else{
		((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOff;
	}

	switch(ProfileListMode){
		case ProfileListModeSell:
			if(MyProfile_SendWork.SellID==ProfCategoryNo+no-EnumGraphProfCategoryPanel00){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOn;
			}
			break;

		case ProfileListModeBuy:
			if(MyProfile_SendWork.BuyID==ProfCategoryNo+no-EnumGraphProfCategoryPanel00){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOn;
			}
			break;

		case ProfileListModeAbout:
			if(MyProfile_SendWork.AboutID==ProfCategoryNo+no-EnumGraphProfCategoryPanel00){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumPanelOn;
			}
			break;
	}		

	if( flag & MENU_MOUSE_LEFT ){

		switch(ProfileListMode){
			case ProfileListModeSell:
				if(categorySys.Sell[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].use==FALSE)
					return TRUE;
				MyProfile_SendWork.SellID=categorySys.Sell[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].ID;
				MyProfile_SendWork.SellGraphID=categorySys.Sell[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].GraphNo;
				break;
			case ProfileListModeBuy:
				if(categorySys.Buy[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].use==FALSE)
					return TRUE;
				MyProfile_SendWork.BuyID=categorySys.Buy[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].ID;
				MyProfile_SendWork.BuyGraphID=categorySys.Buy[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].GraphNo;
				break;	
			case ProfileListModeAbout:
				if(categorySys.About[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].use==FALSE)
					return TRUE;
				MyProfile_SendWork.AboutID=categorySys.About[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].ID;
				MyProfile_SendWork.AboutGraphID=categorySys.About[ProfCategoryNo+no-EnumGraphProfCategoryPanel00].GraphNo;
				break;
		}

		SetDialogMenuChat();

		ReturnFlag=TRUE;	
	}

	return ReturnFlag;

}

//プロフィールスイッチ类
BOOL MenuSwitchProfCategorySwitch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	switch(no){
		case EnumGraphProfCategoryScrollUp:

			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOn;
			}
			if(flag & MENU_MOUSE_LEFTHOLD){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_UpButtonOff;
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTAUTO ){
				ProfCategoryNo-=1;
				if(ProfCategoryNo<0){
					ProfCategoryNo=0;
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
				switch(ProfileListMode){
				case ProfileListModeSell:
				case ProfileListModeBuy:
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				case ProfileListModeAbout:
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.AboutCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				}
				ReturnFlag=TRUE;	
			}
		break;

		case EnumGraphProfCategoryScrollDown:

			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOver;
				ReturnFlag=TRUE;	
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOn;
			}
			if(flag & MENU_MOUSE_LEFTHOLD){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_DownButtonOff;
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTAUTO ){
				switch(ProfileListMode){
				case ProfileListModeSell:
				case ProfileListModeBuy:
					ProfCategoryNo+=1;
					if(ProfCategoryNo>categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE-1){
						if(NEW_ALBUM_LIST_LINE<categorySys.SellBuyCount)
							ProfCategoryNo=categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE;
						else
							ProfCategoryNo=0;

						// ＮＧ音
						play_se( SE_NO_NG, 320, 240 );
					}else{
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
					}
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				case ProfileListModeAbout:
					ProfCategoryNo+=1;
					if(ProfCategoryNo>categorySys.AboutCount-NEW_ALBUM_LIST_LINE-1){
						if(NEW_ALBUM_LIST_LINE<categorySys.AboutCount)
							ProfCategoryNo=categorySys.AboutCount-NEW_ALBUM_LIST_LINE;
						else
							ProfCategoryNo=0;
						// ＮＧ音
						play_se( SE_NO_NG, 320, 240 );
					}else{
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
					}
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.AboutCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				}
				ReturnFlag=TRUE;	
			}
		break;

		case EnumGraphProfCategoryUp:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDownOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDownOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				ProfCategoryNo-=10;
				if(ProfCategoryNo<0){
					ProfCategoryNo=0;
				}
				switch(ProfileListMode){
				case ProfileListModeSell:
				case ProfileListModeBuy:
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				case ProfileListModeAbout:
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.AboutCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				}
				// クリック音
				play_se( SE_NO_CLICK, 320, 240 );
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumDownOff;
			}
			break;

		case EnumGraphProfCategoryDown:
			if( flag & MENU_MOUSE_OVER ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumUpOver;
			}else{
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumUpOn;
			}

			if( flag & MENU_MOUSE_LEFT ){
				switch(ProfileListMode){
				case ProfileListModeSell:
				case ProfileListModeBuy:
					ProfCategoryNo+=10;
					if(ProfCategoryNo>categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE-1){
						if(NEW_ALBUM_LIST_LINE<categorySys.SellBuyCount)
							ProfCategoryNo=categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE;
						else
							ProfCategoryNo=0;
					}
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.SellBuyCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				case ProfileListModeAbout:
					ProfCategoryNo+=10;
					if(ProfCategoryNo>categorySys.AboutCount-NEW_ALBUM_LIST_LINE-1){
						if(NEW_ALBUM_LIST_LINE<categorySys.AboutCount)
							ProfCategoryNo=categorySys.AboutCount-NEW_ALBUM_LIST_LINE;
						else
							ProfCategoryNo=0;
					}
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
					NumToScrollVMove(&wI->sw[EnumBtProfCategoryDrag],categorySys.AboutCount-NEW_ALBUM_LIST_LINE,ProfCategoryNo);
					break;
				}
				ReturnFlag=TRUE;	
			}

			if( flag & MENU_MOUSE_LEFTHOLD ){
				((GRAPHIC_SWITCH *)wI->sw[no].Switch)->graNo=GID_AlbumUpOff;
			}
		break;

	}

	return ReturnFlag;
}


BOOL MenuSwitchProfCategoryScrollSwitch( int no, unsigned int flag ){

	BOOL ReturnFlag;

	ReturnFlag=MenuSwitchScrollBarV(no,flag);

	if(ReturnFlag==TRUE){

		//スクロールから值决める
		switch(ProfileListMode){
		case ProfileListModeSell:
		case ProfileListModeBuy:
			ProfCategoryNo=ScrollVPointToNum(&wI->sw[EnumBtProfCategoryDrag],categorySys.SellBuyCount-PROF_CATEGORY_LIST_LINE);
			break;
		case ProfileListModeAbout:
			ProfCategoryNo=ScrollVPointToNum(&wI->sw[EnumBtProfCategoryDrag],categorySys.AboutCount-PROF_CATEGORY_LIST_LINE);
			break;
		}

	}

	return ReturnFlag;
}

BOOL MenuSwitchProfCategoryScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		switch(ProfileListMode){
		case ProfileListModeSell:
		case ProfileListModeBuy:
			ProfCategoryNo = WheelToMove( &wI->sw[EnumBtProfCategoryDrag], ProfCategoryNo,
				 categorySys.SellBuyCount-PROF_CATEGORY_LIST_LINE, mouse.wheel );
			break;
		case ProfileListModeAbout:
			ProfCategoryNo = WheelToMove( &wI->sw[EnumBtProfCategoryDrag], ProfCategoryNo,
				 categorySys.AboutCount-PROF_CATEGORY_LIST_LINE, mouse.wheel );
			break;
		}
	}

	return ReturnFlag;
}

void MenuWindowProfCategoryReDraw( void ){

	BLT_MEMBER bm={0};
	int i;

	switch(ProfileListMode){
		case ProfileListModeSell:
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphProfCategoryName].Switch))->graNo=GID_ProfCategorySell;
			for( i = 0; i < PROF_CATEGORY_LIST_LINE; i++ ){
				if( categorySys.Sell[ProfCategoryNo+i].use ){
					//名称表示
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfCategoryPanel00+i].Switch)->text,categorySys.Sell[ProfCategoryNo+i].Name);
				}else{
					//无い时
					//名称非表示（空白）
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfCategoryPanel00+i].Switch)->text,"");
				}
			}
		break;

		case ProfileListModeBuy:
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphProfCategoryName].Switch))->graNo=GID_ProfCategoryBuy;
			for( i = 0; i < PROF_CATEGORY_LIST_LINE; i++ ){
				if( categorySys.Buy[ProfCategoryNo+i].use ){
					//名称表示
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfCategoryPanel00+i].Switch)->text,categorySys.Buy[ProfCategoryNo+i].Name);
				}else{
					//无い时
					//名称非表示（空白）
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfCategoryPanel00+i].Switch)->text,"");
				}
			}
		break;

		case ProfileListModeAbout:
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphProfCategoryName].Switch))->graNo=GID_ProfCategoryAbout;
			for( i = 0; i < PROF_CATEGORY_LIST_LINE; i++ ){
				if( categorySys.About[ProfCategoryNo+i].use ){
					//名称表示
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfCategoryPanel00+i].Switch)->text,categorySys.About[ProfCategoryNo+i].Name);
				}else{
					//无い时
					//名称非表示（空白）
					strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfCategoryPanel00+i].Switch)->text,"");
				}
			}
		break;
	}

}

//--------------------------------------------------------
//プロフィール揭示板１
//--------------------------------------------------------
#define CATEGORY_SELL_TEXT_MAX	32
#define CATEGORY_BUY_TEXT_MAX	32
#define CATEGORY_ABOUT_TEXT_MAX	15

void MenuWindowProfileBBS1ReDraw( void );
BOOL CheckCategoryOkFromSwitchNum(int num);
BOOL getCategoryIDFromSwitchNum(int num,int *category,int *id);

// 揭示板ウィンドウの情报を取り出す
void openMessageProfileBBS1( int ButtonType, char *data)
{

	int i,j;
	int flag;

	//信息の解析と格纳
	makeStringFromEscaped( data );

	j=1;
	for(i=0;i<categorySys.SellBuyCount;i++){
		flag=getIntegerToken( data, '|', j++ );
		if(flag & 1){
			categorySys.Sell[i].ok=TRUE;
		}else{
			categorySys.Sell[i].ok=FALSE;
		}
		if(flag & 2){
			categorySys.Buy[i].ok=TRUE;
		}else{
			categorySys.Buy[i].ok=FALSE;
		}
	}
	for(i=0;i<categorySys.AboutCount;i++){
		flag=getIntegerToken( data, '|', j++ );
		if(flag & 1){
			categorySys.About[i].ok=TRUE;
		}else{
			categorySys.About[i].ok=FALSE;
		}
	}
}

BOOL MenuWindowProfileBBS1( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
		MenuWindowProfileBBS1Init();
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 2, 2 ) )
	{
		wI->flag |= WIN_INFO_DEL;
	}

	//内容书き换え	
	MenuWindowProfileBBS1ReDraw();

	return TRUE;
}

BOOL MenuWindowProfileBBS1Draw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

void MenuWindowProfileBBS1ReDraw( void ){

}

void MenuWindowProfileBBS1Init(void){

	int i;

	//１つめは「非公开」の分类なので处理しません

	//SELL
	for(i=1;i<CATEGORY_SELL_TEXT_MAX+1;i++){
		if(categorySys.Sell[i].use){
			wI->sw[EnumTextMenuProfileBBS1Sell00+i-1].Enabled=TRUE;
			wI->sw[EnumGraphMenuProfileBBS1Sell00+i-1].Enabled=TRUE;
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1Sell00+i-1].Switch)->text,categorySys.Sell[i].Name);
			if(categorySys.Sell[i].ok){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1Sell00+i-1].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1Sell00+i-1].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			wI->sw[EnumTextMenuProfileBBS1Sell00+i-1].Enabled=FALSE;
			wI->sw[EnumGraphMenuProfileBBS1Sell00+i-1].Enabled=FALSE;
		}
	}

	//BUY
	for(i=1;i<CATEGORY_BUY_TEXT_MAX+1;i++){
		if(categorySys.Buy[i].use){
			wI->sw[EnumTextMenuProfileBBS1Buy00+i-1].Enabled=TRUE;
			wI->sw[EnumGraphMenuProfileBBS1Buy00+i-1].Enabled=TRUE;
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1Buy00+i-1].Switch)->text,categorySys.Buy[i].Name);
			if(categorySys.Buy[i].ok){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1Buy00+i-1].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1Buy00+i-1].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			wI->sw[EnumTextMenuProfileBBS1Buy00+i-1].Enabled=FALSE;
			wI->sw[EnumGraphMenuProfileBBS1Buy00+i-1].Enabled=FALSE;
		}
	}

	//ABOUT
	for(i=1;i<CATEGORY_ABOUT_TEXT_MAX+1;i++){
		if(categorySys.About[i].use){
			wI->sw[EnumTextMenuProfileBBS1About00+i-1].Enabled=TRUE;
			wI->sw[EnumGraphMenuProfileBBS1About00+i-1].Enabled=TRUE;
			strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1About00+i-1].Switch)->text,categorySys.About[i].Name);
			if(categorySys.About[i].ok){
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1About00+i-1].Switch)->color=FONT_PAL_WHITE;
			}else{
				((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS1About00+i-1].Switch)->color=FONT_PAL_GRAY;
			}
		}else{
			wI->sw[EnumTextMenuProfileBBS1About00+i-1].Enabled=FALSE;
			wI->sw[EnumGraphMenuProfileBBS1About00+i-1].Enabled=FALSE;
		}
	}


}

// 分类选择
BOOL MenuWindowProfileBBS1SelectSwitch( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	int category,id;
	char str[256];
	char data[256];

	if( flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, MWONELINE_BBS1_CATEGOEY );
		((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS1Select;
		ReturnFlag=TRUE;
	}else{
		//非表示
		((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=0;
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT && CheckCategoryOkFromSwitchNum(no)){

		if(getCategoryIDFromSwitchNum(no,&category,&id)){
			sprintf( str, "%d|%d|", category,id );
			makeSendString( str, data, sizeof( data )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy,
				serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_NONE, data );			
			//サーチ文字列をこのときだけクリア
			strcpy(ProfileSearchStr,"");
		}

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}

BOOL MenuWindowProfileBBS1Del( void ){

	serverRequestWinWindowType = -1;

	closeProfileBBSWindowFromType(2);
	closeProfileBBSWindowFromType(3);
	closeProfileBBSWindowFromType(4);

	return TRUE;
}

//--------------------------------------------------------
//プロフィール揭示板２
//--------------------------------------------------------

void MenuWindowProfileBBS2ReDraw( void );

INPUT_STR MenuProfMenuBBs2SearchInputStr;

INIT_STR_STRUCT InitStrStructProfMenuBBs2Search={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&MenuProfMenuBBs2SearchInputStr, 20,370,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_RED,"",   1,      26,  0,     0
};


// 揭示板ウィンドウの情报を取り出す
void openMessageProfileBBS2( int ButtonType, char *data)
{

	int i,j;

	//クリア
	for(i=0;i<PROFILE_HIT_LIST_MAX;i++){
		ProfileHitList.list[i].use=FALSE;
	}

	//信息の解析と格纳
	makeStringFromEscaped( data );

	i=1;	//これデータの头が１ってやめたいよなぁ
	ProfileHitList.category=getIntegerToken( data, '|', i++ );
	ProfileHitList.id=getIntegerToken( data, '|', i++ );

	for(j=0;j<PROFILE_HIT_LIST_MAX;j++){
		//なければ終了
		if(getIntegerToken( data, '|', i )==-1)
			break;

		ProfileHitList.list[j].use=TRUE;
		ProfileHitList.list[j].userID=getIntegerToken( data, '|', i++ );
		ProfileHitList.list[j].graphID=getIntegerToken( data, '|', i++ );
		getStringToken( data, '|', i++, sizeof( ProfileHitList.list[j].name )-1, ProfileHitList.list[j].name );
		makeRecvString( ProfileHitList.list[j].name );
		getStringToken( data, '|', i++, sizeof( ProfileHitList.list[j].ProfileStr)-1, ProfileHitList.list[j].ProfileStr);
		makeRecvString( ProfileHitList.list[j].ProfileStr );
	}

}

BOOL MenuWindowProfileBBS2( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
		MenuWindowProfileBBS2Init();
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 2, 2 ) )
	{
		wI->flag |= WIN_INFO_DEL;
	}

	//内容书き换え	
	MenuWindowProfileBBS2ReDraw();

	return TRUE;
}

BOOL MenuWindowProfileBBS2Draw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

void MenuWindowProfileBBS2ReDraw( void ){

	//About
	if(	pNowInputStr != &MenuProfMenuBBs2SearchInputStr){
		wI->sw[EnumMenuProfileBBS2SearchText].Enabled=TRUE;
		strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuProfileBBS2SearchText].Switch)->text,ProfileSearchStr);
	}else{
		wI->sw[EnumMenuProfileBBS2SearchText].Enabled=FALSE;

		//ダイアログ位置
		SetInputStr(&InitStrStructProfMenuBBs2Search,wI->wx,wI->wy,1);
		strcpy(ProfileSearchStr,InitStrStructProfMenuBBs2Search.inputStr->buffer);
	}

}

void MenuWindowProfileBBS2Init(void){

	int i;

	//セレクト枠
	for(i=0;i<PROFILE_HIT_LIST_MAX;i++){

		if(ProfileHitList.list[i].use){
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuProfileBBS2Message00+i].Switch)->text,ProfileHitList.list[i].ProfileStr);
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuProfileBBS2Name00+i].Switch)->text,ProfileHitList.list[i].name);
		}else{

			wI->sw[EnumMenuProfileBBS2Message00+i].Enabled=FALSE;
			wI->sw[EnumMenuProfileBBS2Name00+i].Enabled=FALSE;
			wI->sw[EnumMenuProfileBBS2Select00+i].Enabled=FALSE;

		}
	}

	//分类と种类
	switch(ProfileHitList.category){
		case ProfileListModeSell:
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuProfileBBS2Category].Switch)->text,categorySys.Sell[ProfileHitList.id].Name);
			((GRAPHIC_SWITCH *)(wI->sw[EnumMenuProfileBBS2CategoryGraph].Switch))->graNo=GID_ProfBBS2CategorySell;
			break;

		case ProfileListModeBuy:
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuProfileBBS2Category].Switch)->text,categorySys.Buy[ProfileHitList.id].Name);
			((GRAPHIC_SWITCH *)(wI->sw[EnumMenuProfileBBS2CategoryGraph].Switch))->graNo=GID_ProfBBS2CategoryBuy;
			break;

		case ProfileListModeAbout:
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuProfileBBS2Category].Switch)->text,categorySys.About[ProfileHitList.id].Name);
			((GRAPHIC_SWITCH *)(wI->sw[EnumMenuProfileBBS2CategoryGraph].Switch))->graNo=GID_ProfBBS2CategoryAbout;
			break;
	}

}

BOOL MenuWindowProfileBBS2Switch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;
	char str[256];
	char Escstr[256];
	char EscSearchstr[256];

	switch(no){
		//检索ボタン
		case EnumMenuProfileBBS2Search:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2SearchOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS2_SEARCH );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2SearchOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){

				makeEscapeString( (unsigned char *)ProfileSearchStr, (unsigned char *)EscSearchstr, sizeof(EscSearchstr) );

				sprintf( str, "%d|%d|%s", ProfileHitList.category,ProfileHitList.id,EscSearchstr);
				makeSendString( str, Escstr, sizeof( Escstr )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_OK, Escstr );			

				ReturnFlag=TRUE;
			}
			break;

		//←
		case EnumMenuProfileBBS2PageBack:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2LeftOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS2_PREV );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2LeftOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){

				makeEscapeString( (unsigned char *)ProfileSearchStr, (unsigned char *)EscSearchstr, sizeof(EscSearchstr) );

				sprintf( str, "%d|%d|%s", ProfileHitList.category,ProfileHitList.id,EscSearchstr);
				makeSendString( str, Escstr, sizeof( Escstr )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_PRE, Escstr );			

				ReturnFlag=TRUE;
			}
			break;

		//→
		case EnumMenuProfileBBS2PageNext:
			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2RightOver;
				strcpy( OneLineInfoStr, MWONELINE_BBS2_NEXT );
				ReturnFlag=TRUE;
			}else{
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2RightOn;
			}
			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){

				makeEscapeString( (unsigned char *)ProfileSearchStr, (unsigned char *)EscSearchstr, sizeof(EscSearchstr) );

				sprintf( str, "%d|%d|%s", ProfileHitList.category,ProfileHitList.id,EscSearchstr);
				makeSendString( str, Escstr, sizeof( Escstr )-1 );
				nrproto_WN_send( sockfd, mapGx, mapGy,
					serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_NEXT, Escstr );			

				ReturnFlag=TRUE;
			}
			break;
	}

	return ReturnFlag;

}

// 分类选择
BOOL MenuWindowProfileBBS2SelectSwitch( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	int num;
	char str[1024];
	char data[256];
	char name[256];

	num=no-EnumMenuProfileBBS2Select00;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_BBS2_USER );

	if( flag & MENU_MOUSE_OVER){
		((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2SelectOver;
		ReturnFlag=TRUE;
	}else{
		//非表示
		((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2SelectOff;
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		strcpy(name,ProfileHitList.list[num].name);		
		sprintf( str, "%d|%s", ProfileHitList.list[num].userID,name);
		makeSendString( str, data, sizeof( data )-1 );
		nrproto_WN_send( sockfd, mapGx, mapGy,
			serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_YES, data );			

		ReturnFlag=TRUE;
	}

	if(WindowFlag[MENU_WINDOW_PROFILE_PROFILE_3].wininfo!=NULL){
		if(SearchProfile.userID==ProfileHitList.list[num].userID){
			((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS2SelectOn;
		}
	}

	return ReturnFlag;
	
}

BOOL MenuWindowProfileBBS2Del( void ){

	serverRequestWinWindowType = -1;

	closeProfileBBSWindowFromType(3);
	closeProfileBBSWindowFromType(4);

	return TRUE;
}

//サーチ入力ダイアログ
BOOL MenuSwitchProfileBBS2SearchDialog( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	
	DIALOG_SWITCH *Dialog;

	if( flag & MENU_MOUSE_OVER ){
		strcpy( OneLineInfoStr, MWONELINE_BBS2_SEARCH_STR );
		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFT ){
		if(DiarogST.SwAdd!=wI->sw[EnumMenuProfileBBS2SearchDialog].Switch){

			strcpy(InitStrStructProfMenuBBs2Search.str,ProfileSearchStr);
			//ダイアログ初期化
			SetInputStr(&InitStrStructProfMenuBBs2Search,wI->wx,wI->wy,0);
			//フォーカスを取る
			GetKeyInputFocus( &MenuProfMenuBBs2SearchInputStr );

			DiarogST.SwAdd=wI->sw[EnumMenuProfileBBS2SearchDialog].Switch;
			Dialog=(DIALOG_SWITCH *)wI->sw[EnumMenuProfileBBS2SearchDialog].Switch;
			Dialog->InpuStrAdd=&MenuProfMenuBBs2SearchInputStr;
		}					
		ReturnFlag=TRUE;
	}

	return ReturnFlag;

}


//--------------------------------------------------------
//プロフィール揭示板３
//--------------------------------------------------------

void MenuWindowProfileBBS3ReDraw( void );

// 揭示板ウィンドウの情报を取り出す
void openMessageProfileBBS3( int ButtonType, char *data)
{

	int i,id;
	char str[256];

	//信息の解析と格纳
	makeStringFromEscaped( data );

	i=1;

	//等级
	SearchProfile.userID=getIntegerToken( data, '|', i++ );

	//名称
	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.name,str);

	//等级
	SearchProfile.lv=getIntegerToken( data, '|', i++ );

	//职业
	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.jobName,str);

	//称号
	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.titleName,str);
	
	//家族名称
	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.guildName,str);

	//グラフィック
	SearchProfile.graphID=getIntegerToken( data, '|', i++ );

	//Sell
	SearchProfile.SellID=getIntegerToken( data, '|', i++ );

	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.SellStr,str);

	//Buy
	SearchProfile.BuyID=getIntegerToken( data, '|', i++ );

	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.BuyStr,str);

	//About
	SearchProfile.AboutID=getIntegerToken( data, '|', i++ );

	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.AboutStr,str);

	//Profile
	getStringToken( data, '|', i++, sizeof( str )-1, str );
	makeRecvString( str );
	strcpy(::SearchProfile.ProfileStr,str);



	//その他の准备
	SearchProfile.BuyGraphID=GID_ProfCategoryDef;
	SearchProfile.SellGraphID=GID_ProfCategoryDef;
	SearchProfile.AboutGraphID=GID_ProfCategoryDef;
	
	id=getCategoryGraphicID(SearchProfile.BuyID,PROFILE_CATEGORY_SELLBUY);
	if(id!=-1)
		SearchProfile.BuyGraphID=id;

	id=getCategoryGraphicID(SearchProfile.SellID,PROFILE_CATEGORY_SELLBUY);
	if(id!=-1)
		SearchProfile.SellGraphID=id;

	id=getCategoryGraphicID(SearchProfile.AboutID,PROFILE_CATEGORY_ABOUT);
	if(id!=-1)
		SearchProfile.AboutGraphID=id;


}

BOOL MenuWindowProfileBBS3( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
		MenuWindowProfileBBS3Init();
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 2, 2 ) )
	{
		wI->flag |= WIN_INFO_DEL;
	}

	//内容书き换え	
	MenuWindowProfileBBS3ReDraw();

	return TRUE;
}

BOOL MenuWindowProfileBBS3Draw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

void MenuWindowProfileBBS3ReDraw( void ){

	char strwork[256];
	char ProfileBuff[1024];

	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfileBBS3Face].Switch))->graNo=SearchProfile.graphID;

	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfileBBS3SellIcon].Switch))->graNo=SearchProfile.SellGraphID;
	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfileBBS3BuyIcon].Switch))->graNo=SearchProfile.BuyGraphID;
	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfileBBS3AboutIcon].Switch))->graNo=SearchProfile.AboutGraphID;

	//テキスト关连
	//名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3Name].Switch)->text,SearchProfile.name);

	sprintf(strwork,"%d",SearchProfile.lv);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3Level].Switch)->text,strwork);

	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyTitle].Switch)->text,SearchProfile.titleName);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyJob].Switch)->text,SearchProfile.jobName);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyGuild].Switch)->text,SearchProfile.guildName);

	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MySell].Switch)->text,SearchProfile.SellStr);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyBuy].Switch)->text,SearchProfile.BuyStr);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyAbout].Switch)->text,SearchProfile.AboutStr);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),SearchProfile.ProfileStr, 0, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyProfile0].Switch)->text,ProfileBuff);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),SearchProfile.ProfileStr, 1, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyProfile1].Switch)->text,ProfileBuff);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),SearchProfile.ProfileStr, 2, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyProfile2].Switch)->text,ProfileBuff);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),SearchProfile.ProfileStr, 3, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfileBBS3MyProfile3].Switch)->text,ProfileBuff);

}

void MenuWindowProfileBBS3Init(void){

}

BOOL MenuWindowProfileBBS3Switch( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	char str[256];

	switch(no){
		case EnumGraphMenuProfileBBS3Mail:

			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS3SendOver;
				sprintf(str,MWONELINE_BBS3_SEND_MINIMAIL, categorySys.mailMoney);
				strcpy( OneLineInfoStr,str);
				ReturnFlag=TRUE;
			}else{
				//非表示
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS3SendOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if(pc.gold>=categorySys.mailMoney){
					ProfileSendMail.id=SearchProfile.userID;
					strcpy(ProfileSendMail.name,SearchProfile.name);
					strcpy(ProfileSendMail.str,"");

					createMenuWindow( MENU_WINDOW_PROFILE_PROFILE_4 );
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
				}else{
					//金がないとエラー
					play_se( SE_NO_NG, 320, 240 );
				}
				ReturnFlag=TRUE;
			}

			break;
	}

	return ReturnFlag;
}

//スイッチの场所からそのスイッチに相当する分类がＯＫか调べる
BOOL CheckCategoryOkFromSwitchNum(int num){

	if(num-EnumGraphMenuProfileBBS1Sell00<CATEGORY_SELL_TEXT_MAX){
		//Ｓｅｌｌの场合
		if(categorySys.Sell[num-EnumGraphMenuProfileBBS1Sell00+1].ok)
			return TRUE;
	}else if(num-EnumGraphMenuProfileBBS1Sell00<CATEGORY_SELL_TEXT_MAX+CATEGORY_BUY_TEXT_MAX){
		//Ｂｕｙの场合
		if(categorySys.Buy[num-EnumGraphMenuProfileBBS1Sell00-CATEGORY_SELL_TEXT_MAX+1].ok)
			return TRUE;
	}else{
		//Ａｂｏｕｔの场合
		if(categorySys.About[num-EnumGraphMenuProfileBBS1Sell00-CATEGORY_SELL_TEXT_MAX-CATEGORY_BUY_TEXT_MAX+1].ok)
			return TRUE;
	}

	return FALSE;
}

//スイッチの场所からそのスイッチに相当する分类のＩＤを调べる
BOOL getCategoryIDFromSwitchNum(int num,int *category,int *id){

	if(num-EnumGraphMenuProfileBBS1Sell00<CATEGORY_SELL_TEXT_MAX){
		//Ｓｅｌｌの场合
		*category=ProfileListModeSell;
		*id=categorySys.Sell[num-EnumGraphMenuProfileBBS1Sell00+1].ID;
		return TRUE;
	}else if(num-EnumGraphMenuProfileBBS1Sell00<CATEGORY_SELL_TEXT_MAX+CATEGORY_BUY_TEXT_MAX){
		//Ｂｕｙの场合
		*category=ProfileListModeBuy;
		*id=categorySys.Buy[num-EnumGraphMenuProfileBBS1Sell00-CATEGORY_SELL_TEXT_MAX+1].ID;
		return TRUE;
	}else{
		//Ａｂｏｕｔの场合
		*category=ProfileListModeAbout;
		*id=categorySys.About[num-EnumGraphMenuProfileBBS1Sell00-CATEGORY_SELL_TEXT_MAX-CATEGORY_BUY_TEXT_MAX+1].ID;
		return TRUE;
	}

	return FALSE;
}

BOOL MenuWindowProfileBBS3Del( void ){

	serverRequestWinWindowType = -1;

	closeProfileBBSWindowFromType(4);

	return TRUE;
}

//--------------------------------------------------------
//プロフィール揭示板４
//--------------------------------------------------------

INPUT_STR ProfileBBS4EditStr;

INIT_STR_STRUCT InitStrStructProfileBBS4={
//  本体		       ofx,ofy ,piro       ,Font           ,color         ,str ,MaxLine,MAXLen	,dist, flag
	&ProfileBBS4EditStr, 33-11,84-14,FONT_PRIO_WIN,FONT_KIND_SIZE_12,FONT_PAL_WHITE,"",        10,      28,     12,     0
};
#ifdef PUK3_MAIL_ETC
	int ProfileBBS4BfId;
#endif

BOOL MenuSwitchProfileBBS4( int mouse ){

	if (mouse==WIN_INIT){
#ifdef PUK3_MAIL_ETC
		//以前のウインドウからひきつぐ？
		if ( ProfileSendMail.id == ProfileBBS4BfId ){
			SetInputStr(&InitStrStructProfileBBS4,wI->wx,wI->wy,1);
		}else{
			SetInputStr(&InitStrStructProfileBBS4,wI->wx,wI->wy,0);
		}
		//フォーカスを取る
		GetKeyInputFocus( &ProfileBBS4EditStr );

		DiarogST.SwAdd=wI->sw[EnumDialogProfileBBS4Message].Switch;
		((DIALOG_SWITCH *)wI->sw[EnumDialogProfileBBS4Message].Switch)->InpuStrAdd=&ProfileBBS4EditStr;

		//ここで初期化文字列消しておきます
		InitStrStructProfileBBS4.str[0]='\0';

		// 今回开くＩＤ保存
		ProfileBBS4BfId = ProfileSendMail.id;
#endif

	}

	MenuWindowProfileBBS4ReDraw();

	return TRUE;
}

BOOL MenuWindowProfileBBS4Draw( int mouse ){

	displayMenuWindow();

	return TRUE;
}

BOOL MenuWindowProfileBBS4Del(void){

	return TRUE;
}

void MenuWindowProfileBBS4ReDraw( void ){

	int i,j=1;
	int num;

	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextProfileBBS4ToName].Switch)->text,ProfileSendMail.name);

	//ダイアログの座标
	SetInputStr(&InitStrStructProfileBBS4,wI->wx,wI->wy,2);

	//金额表示
	for(i=0;i<8;i++){
		if(categorySys.mailMoney>=j){
			num=(categorySys.mailMoney/j)%10;
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphProfileBBS4Str1+i].Switch))->graNo=GID_Num_White_S_0+num;
		}else{
			((GRAPHIC_SWITCH *)(wI->sw[EnumGraphProfileBBS4Str1+i].Switch))->graNo=0;
		}
		j*=10;
	}
	if(categorySys.mailMoney==0){
		((GRAPHIC_SWITCH *)(wI->sw[EnumGraphProfileBBS4Str1].Switch))->graNo=GID_Num_White_S_0;
	}

}

BOOL MenuSwitchProfileBBS4Switch( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;
	char str[256];
	char message[1024];
	char messageEsc[1024];
	char nameEsc[256];
	char name[256];
	char sendStr[1024];
	char sendStrEsc[1024];
	struct BLT_MEMBER bm={0};
	time_t t;
	struct tm *tt;
	char header[64];
	int index;

	switch(no){
		case EnumGraphProfileBBS4Send:

			if( flag & MENU_MOUSE_OVER){
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS3SendOver;
				sprintf(str,MWONELINE_BBS3_SEND_MINIMAIL, categorySys.mailMoney);
				strcpy( OneLineInfoStr,str);
				ReturnFlag=TRUE;
			}else{
				//非表示
				((GRAPHIC_SWITCH *)(wI->sw[no].Switch))->graNo=GID_ProfBBS3SendOn;
			}

			//押されたとき
			if( flag & MENU_MOUSE_LEFT ){
				if(ProfileBBS4EditStr.buffer[0]!='\0'){
					strcpy(name,ProfileSendMail.name);
					strcpy(message,ProfileBBS4EditStr.buffer);

					makeEscapeString( (unsigned char *)name, (unsigned char *)nameEsc, sizeof(nameEsc) );
					makeEscapeString( (unsigned char *)message, (unsigned char *)messageEsc, sizeof(messageEsc) );
					sprintf(sendStr,"%d|%s|%s|%d",ProfileSendMail.id,nameEsc,messageEsc,FONT_PAL_WHITE);
					makeSendString( sendStr, sendStrEsc, sizeof( sendStrEsc )-1 );

					//送信
					nrproto_WN_send( sockfd, mapGx, mapGy,
						serverRequestWinSeqNo, serverRequestWinObjIndex, WINDOW_BUTTONTYPE_NO, sendStrEsc );			

					//もしもすでに名刺があれば自前で自分の送信ログを残す
					index=getMiniMailMemIndexfromID(ProfileSendMail.id);
					if(index!=-1){
						time( &t );
						tt = localtime( &t );
						sprintf( header, "%4d/%2d/%2d %02d:%02d",
							1900+tt->tm_year, tt->tm_mon+1, tt->tm_mday, tt->tm_hour, tt->tm_min );

						setMiniMailHistory( index,1, header, message );
					}


					closeProfileBBSWindowFromType(3);
					closeProfileBBSWindowFromType(4);

					// 决定音b（ボタンクリック时）
					play_se( SE_NO_OK2, 320, 240 );

					ProfileBBS4EditStr.buffer[0]='\0';	
#ifdef PUK3_MAIL_ETC
					// 今回开くＩＤ保存
					ProfileBBS4BfId = -1;
#endif

					ReturnFlag=TRUE;
				}else{
					play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
				}
			}

			break;
	}

	return ReturnFlag;

}

//タイトル入力ダイアログ
BOOL MenuSwitchProfileBBS4Dialog( int no, unsigned int flag ){

	BOOL ReturnFlag=FALSE;	

	if( flag & MENU_MOUSE_LEFT ){
		if(DiarogST.SwAdd!=wI->sw[EnumDialogProfileBBS4Message].Switch){
			//ダイアログ初期化
#ifdef PUK3_MAIL_ETC
			SetInputStr(&InitStrStructProfileBBS4,wI->wx,wI->wy,1);
#else
			SetInputStr(&InitStrStructProfileBBS4,wI->wx,wI->wy,0);
#endif
			//フォーカスを取る
			GetKeyInputFocus( &ProfileBBS4EditStr );

			DiarogST.SwAdd=wI->sw[EnumDialogProfileBBS4Message].Switch;
			((DIALOG_SWITCH *)wI->sw[EnumDialogProfileBBS4Message].Switch)->InpuStrAdd=&ProfileBBS4EditStr;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}		
		ReturnFlag=TRUE;
	}
#ifdef PUK3_MAIL_ETC
	// きれいではないけど、とりあえずここで
	if ( pNowInputStr != &ProfileBBS4EditStr ){
		StockFontBuffer2( &ProfileBBS4EditStr );
	}
#endif

	return ReturnFlag;

}


//--------------------------------------------------------
//プロフィール揭示板その他
//--------------------------------------------------------
void closeAllProfileBBSWindow(void){


	if(WindowFlag[MENU_WINDOW_PROFILE_PROFILE_1].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_PROFILE_PROFILE_1].wininfo->flag|=WIN_INFO_DEL;
	}

	if(WindowFlag[MENU_WINDOW_PROFILE_PROFILE_2].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_PROFILE_PROFILE_2].wininfo->flag|=WIN_INFO_DEL;
	}

	if(WindowFlag[MENU_WINDOW_PROFILE_PROFILE_3].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_PROFILE_PROFILE_3].wininfo->flag|=WIN_INFO_DEL;
	}

	if(WindowFlag[MENU_WINDOW_PROFILE_PROFILE_4].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_PROFILE_PROFILE_4].wininfo->flag|=WIN_INFO_DEL;
	}

}

void closeProfileBBSWindowFromType(int type){


	if(WindowFlag[MENU_WINDOW_PROFILE_PROFILE_1-1+type].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_PROFILE_PROFILE_1-1+type].wininfo->flag|=WIN_INFO_DEL;
	}

}

//--------------------------------------------------------
//プロフィールウインドウ
//--------------------------------------------------------
void MenuWindowProfUserNpcReDraw( void );

BOOL MenuWindowProfUserNpc( int mouse )
{

	//初期化
	if (mouse==WIN_INIT){
	}

	//内容书き换え	
	MenuWindowProfUserNpcReDraw();

	return TRUE;
}

BOOL MenuWindowProfUserNpcDraw( int mouse )
{

	displayMenuWindow();

	return TRUE;

}

//タイトルスイッチ
BOOL MenuSwitchSetProfUserNpc( int no, unsigned int flag ){

	// 自由称号の作业バッファ
	BOOL ReturnFlag=FALSE;	
/*
	switch(no){
		default:
			break;		
	}
*/
	return ReturnFlag;
}

void MenuWindowProfUserNpcReDraw( void ){

	char strwork[256];	
	char ProfileBuff[256];	

	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfUserNpcFace].Switch))->graNo=getNewFaceGraphicNo(UserNpcProfile.graphID);

	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfUserNpcSellIcon].Switch))->graNo=UserNpcProfile.SellGraphID;
	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfUserNpcBuyIcon].Switch))->graNo=UserNpcProfile.BuyGraphID;
	((GRAPHIC_SWITCH *)(wI->sw[EnumGraphMenuProfUserNpcAboutIcon].Switch))->graNo=UserNpcProfile.AboutGraphID;

	//テキスト关连
	//名称
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcName].Switch)->text,UserNpcProfile.name);

	sprintf(strwork,"%d",UserNpcProfile.lv);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcLevel].Switch)->text,strwork);

	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyTitle].Switch)->text,UserNpcProfile.titleName);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyJob].Switch)->text,UserNpcProfile.jobName);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyGuild].Switch)->text,UserNpcProfile.guildName);

	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMySell].Switch)->text,UserNpcProfile.SellStr);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyBuy].Switch)->text,UserNpcProfile.BuyStr);
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyAbout].Switch)->text,UserNpcProfile.AboutStr);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),UserNpcProfile.ProfileStr, 0, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyProfile0].Switch)->text,ProfileBuff);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),UserNpcProfile.ProfileStr, 1, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyProfile1].Switch)->text,ProfileBuff);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),UserNpcProfile.ProfileStr, 2, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyProfile2].Switch)->text,ProfileBuff);

	getMemoLine( ProfileBuff, sizeof( ProfileBuff ),UserNpcProfile.ProfileStr, 3, 28 );
	strcpy(((TEXT_SWITCH *)wI->sw[EnumTextMenuProfUserNpcMyProfile3].Switch)->text,ProfileBuff);

}

