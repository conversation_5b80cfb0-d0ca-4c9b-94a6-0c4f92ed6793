﻿#ifndef _NEWMAP_H_ 
#define _NEWMAP_H_

#define DeCutDataMaxSize 50000
#define DeAutoMapSize 399*399
#define DeMapDateOffSet 1000
#define ANIME_PARTS_MAX 399*399
#define ANIME_PARTS_X_MAX 399
#define ANIME_PARTS_Y_MAX 399

#ifdef HIT_VIEW
#define HIT_MAP_GRAPH	246676
#endif

// 
#define MAP_HIT_FLAG	0x0100		

//カットデータファイルフォーマット
struct SugiCutFileHeader{
	char Str[4];
	short HeaderSize;
	short Feald;
	short Parts;
	short Anime;
};

//カットデータファイルデータ
struct SugiCutFileDate{
	long GNum;
	short Flag;
	short U;
	short V;
	short W;
	short H;
	short CX;
	short CY;
};

//カットデータファイルフォーマット
struct SugiCutFileFormat{
	
	SugiCutFileHeader Header;
	SugiCutFileDate Date[DeCutDataMaxSize];

};

//マップ状态
struct SugiMapStatus{
	int mapNo;			//フロアナンバー
	int mapID;			//フロアＩＤ
	int CutDateNum;		//使用するカットデータナンバー	
	int AutoMapNum;		//使用するオートマップナンバー
	int MapVer;			//マップバージョン（０：旧マップ　１：新マップ）
};

//アニメ布ツ管理构造体
struct AnimeMapStatusStruct{
/*
	char AnimePartsFlag[ANIME_PARTS_MAX];		//アクション作成したかフラグ
	ACTION *AnimePartsAction[ANIME_PARTS_MAX];	//アニメ用アクション
*/
	//アクション作成したかフラグ
	char AnimePartsFlag[ANIME_PARTS_X_MAX][ANIME_PARTS_Y_MAX];		
	//アニメ用アクション
	ACTION *AnimePartsAction[ANIME_PARTS_X_MAX][ANIME_PARTS_Y_MAX];	
};

void drawMap_Sugi(BOOL);
void SugiCutDateRead(long);
void readHitMap_PUK2( int, int, int, int,unsigned short *, unsigned short *, unsigned short *, unsigned short *);
void SugiMapBGM(void);
void SugiRGBDateReadForAutoMap(long);
void MapStatusSet(int,int,int,int);
ACTION *createAnimePartsAction( int, int, int );
void AnimePartsInit(void);
void AnimePartsFunction(void);
void setMapAnimeParts( int , float , float , int , int , struct BLT_MEMBER *);
void AnimPartsProc( ACTION * );


// 6/27 20:00 齐藤２追加
extern	int		cutNo;
extern  unsigned char SugiAutoMapDate[DeAutoMapSize][3];
extern  SugiMapStatus SugiMapStat;
extern int hitViewFlag;
#endif
