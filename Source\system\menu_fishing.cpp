﻿/*--------------------------------------
 * 釣り
 *------------------------------------*/
#include<stdio.h>
#include<stdlib.h>
#include<time.h>

#include "../systeminc/system.h"
#include "../systeminc/menu.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/map.h"
#include "../systeminc/t_music.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/mouse.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/font.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/character.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/loadrealbin.h"

#ifdef _FISHING_WINDOW
#include "../systeminc/menu_fishing.h"

//-------------------------------------------------------------------------//
// 釣り饵选择ウインドウ                                                    //
//-------------------------------------------------------------------------//
MENU_WINDOW_INFO fishingFeedSelMenuWin =
{//   x,   y,   w,   h, menuNo,
	368,   0, 272, 440, MENU_FISHING_FEED_WIN,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_FISHING_FEED_MENU_WIN
	},
};

ACTION *ptActFishingFeedMenu = NULL;
int fishingFeedSelWinMsg;
char fishingFeedSelMemo[30*3];
int feedSelNo = 0;
int feedSelID = 0;

void initFishingFeedMenu( void)
{
	memoryMapGridPos( mapGx, mapGy );

	ptActFishingFeedMenu =
		createActEmChara( pc.graNo, fishingFeedSelMenuWin.x +78, fishingFeedSelMenuWin.y+408 );
	ptActFishingFeedMenu->atr |= ACT_ATR_HIDE;

	feedSelNo = -1;
}

void fishingFeedMenu( int status)
{
	GRA_BTN_INFO1 btn =
	{
		fishingFeedSelMenuWin.x+192, fishingFeedSelMenuWin.y+408,
		fishingFeedSelMenuWin.x+152, fishingFeedSelMenuWin.y+398,
		80, 20,
		CG_FISHING_FEED_MENU_BTN_1, CG_FISHING_FEED_MENU_BTN_2
	};
	GRA_BTN_INFO1 btn2 =
	{
		fishingFeedSelMenuWin.x+192, fishingFeedSelMenuWin.y+408,
		fishingFeedSelMenuWin.x+152, fishingFeedSelMenuWin.y+398,
		80, 20,
		CG_FISHING_FEED_MENU_BTN_3
	};
	int ret;
	int i, j;
	int focusItemNo;
	static int itemInfoPage = 0;
	static int oldFocus = -1;
	char str[256];
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif

	// メニュー共通处理
	ret = menuCommonProc( status, &fishingFeedSelMenuWin, initFishingFeedMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		if( ptActFishingFeedMenu != NULL )
		{
			DeathAction( ptActFishingFeedMenu );
			ptActFishingFeedMenu = NULL;
		}
		return;
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 1, 1 ) )
	{
		// このウィンドウを关闭
		menuClose( fishingFeedSelMenuWin.menuNo );
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	// キャラを见えるようにする
	if( ptActFishingFeedMenu )
	{
		ptActFishingFeedMenu->atr &= (~ACT_ATR_HIDE);
	}

	focusItemNo = -1;
	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( MakeHitBox(
				fishingFeedSelMenuWin.x +8+j*52,
				fishingFeedSelMenuWin.y+36+i*53,
				fishingFeedSelMenuWin.x +8+j*52+48,
				fishingFeedSelMenuWin.y+36+i*53+48, -1 ) )
			{
				focusItemNo = i * ITEM_DRAW_COLUMN + j;
				break;
			}
		}
	}

	if( focusItemNo != oldFocus )
	{
		itemInfoPage = 0;
	}

	// ボタンが押されたかチェック
	if( (pushGraBtnInfo1( &btn ) & BTN_LEFT_CLICK) ){
		if( feedSelNo >= 0){
			feedSelID = pc.item[MAX_EQUIP_ITEM+feedSelNo].id;
			menuClose( fishingFeedSelMenuWin.menuNo);
			menuOpen( MENU_CATEGORY_D_GATHER_WIN );
			// 决定音b（ボタンクリック时）
			play_se( SE_NO_OK2, 320, 240 );
		}
		else {
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
	else
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag
	 && (mouse.onceState & MOUSE_LEFT_DBL_CRICK) )
	{
		if( !pc.item[MAX_EQUIP_ITEM+focusItemNo].checkFlag){
			fishingFeedSelWinMsg = 2;
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
		else if( feedSelNo < 0
		 || feedSelNo != focusItemNo )
		{
			fishingFeedSelWinMsg = 1;
			feedSelNo = focusItemNo;
			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );
		}
		else
		if( feedSelNo == focusItemNo )
		{
			fishingFeedSelWinMsg = 0;
			feedSelNo = -1;
			// キャンセル音
			play_se( 54, 320, 240 );
		}
	}
	else
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag
	 && (mouse.onceState & MOUSE_RIGHT_CRICK) )
	{
		itemInfoPage ^= 1;
	}

	// アイテム一覧の表示
	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].useFlag )
			{
				// アイテム画像表示
#ifdef PUK2
				StockDispBuffer(
					fishingFeedSelMenuWin.x +8+24+j*52,
					fishingFeedSelMenuWin.y+36+24+i*53,
					DISP_PRIO_MENU, pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].graNo, 0 );
#else
				StockDispBuffer(
					fishingFeedSelMenuWin.x +8+24+j*52,
					fishingFeedSelMenuWin.y+36+24+i*53,
					DISP_PRIO_MENU, pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].graNo, 0 );
#endif
				if( pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].num > 0 )
				{
					// スタック数表示
					sprintf( str, "%3d", pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].num ); //MLHIDE
					StockFontBuffer(
						fishingFeedSelMenuWin.x +8+21+j*52,
						fishingFeedSelMenuWin.y+36+31+i*53,
						FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
						str, 0, 0 );
				}
			}
			if( focusItemNo == i * ITEM_DRAW_COLUMN + j )
			{
				StockBoxDispBuffer(
					fishingFeedSelMenuWin.x +8+j*52,
					fishingFeedSelMenuWin.y+36+i*53,
					fishingFeedSelMenuWin.x +8+j*52+48,
					fishingFeedSelMenuWin.y+36+i*53+48,
					DISP_PRIO_MENU, BoxColor, 0 );
			}
			if( feedSelNo == i * ITEM_DRAW_COLUMN + j )
			{
				StockBoxDispBuffer(
					fishingFeedSelMenuWin.x + 10 + j * 52,
					fishingFeedSelMenuWin.y + 38 + i * 53,
					fishingFeedSelMenuWin.x + 10 + j * 52+44,
					fishingFeedSelMenuWin.y + 38 + i * 53+44,

					DISP_PRIO_MENU, SYSTEM_PAL_YELLOW, 0 );
			}
		}
	}

	// ボタンの表示
	if( feedSelNo >= 0){
		drawGraBtnInfo1( &btn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	else {
		drawGraBtnInfo1( &btn2, DISP_PRIO_MENU, 1, BoxColor, 1 );
	}

	switch( fishingFeedSelWinMsg){
	case 0:
		strncpy( fishingFeedSelMemo, "选择鱼饵。", sizeof( fishingFeedSelMemo)); //MLHIDE
		break;

	case 1:
		strncpy( fishingFeedSelMemo, "开始钓鱼。", sizeof( fishingFeedSelMemo)); //MLHIDE
		break;

	case 2:
		strncpy( fishingFeedSelMemo, "无法使用未鉴定物品作为鱼饵。", sizeof( fishingFeedSelMemo)); //MLHIDE
		break;
	}

	// 信息表示
	for( i = 0; i < 3; i++ )
	{
		// 信息表示
		if( getMemoLine( str, sizeof( str ),
			fishingFeedSelMemo, i, 28 ) )
		{
			StockFontBuffer( fishingFeedSelMenuWin.x  +9, fishingFeedSelMenuWin.y+255+i*20,
				FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
		}
	}

	// アイテム情报ウインドウを表示
	if( focusItemNo >= 0 )
	{
		itemInfoWindow(
			fishingFeedSelMenuWin.x,
			fishingFeedSelMenuWin.y+244,
			focusItemNo+MAX_EQUIP_ITEM, itemInfoPage );
	}

	// １行インフォ表示
	// x ボタン
	if( checkFocusMenuClose( &fishingFeedSelMenuWin ) )
	{
		strcpy( OneLineInfoStr, "关闭这个窗口。" );                                //MLHIDE
	}

	if( focusItemNo >= 0
	&& pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag){
		strcpy( OneLineInfoStr, "使用这个物品作为鱼饵。");                             //MLHIDE
	}

	// メニュー共通表示处理
	menuCommonDraw( &fishingFeedSelMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );

	oldFocus = focusItemNo;
}

/*--------------------------------------
 * 釣り用の船アクション实行关数
 *------------------------------------*/
void fishingship_func( ACTION *p)
{
	ACTION *targetAct;

	if( pc.id != p->actNo) targetAct = getCharObjAct( p->actNo);
	else targetAct = pc.ptAct;

	if( targetAct == NULL){
		DeathAction( p);
		return;
	}

	if( targetAct->dirCnt == 0){
		DeathAction( p);
		return;
	}

	p->x = targetAct->x;
	p->y = targetAct->y;
	p->fx = targetAct->fx;
	p->fy = targetAct->fy;
	p->mx = targetAct->mx;
	p->my = targetAct->my;
	p->gx = targetAct->gx;
	p->gy = targetAct->gy;
	p->anim_ang = targetAct->anim_ang;
}

/*--------------------------------------
 * IDからすでに船に乘っているか调查する
 *------------------------------------*/
BOOL CheckFishingShipID( int id)
{
	ACTION *pActLoop = pActTop->pNext;

	// Loop
	for(;;){
		// 最后までループする
		if( pActLoop == pActBtm){
			break;
		}
		// IDとfuncが一致
		if( pActLoop->func == fishingship_func && pActLoop->actNo == id){
			// 生きていれば
			if( pActLoop->deathFlag == FALSE){
				return TRUE;
			}
		}
		// 次のポインタをセット
		pActLoop = pActLoop->pNext;
	}

	return FALSE;
}

/*--------------------------------------
 * 釣り用の船アクション作成
 *------------------------------------*/
void CreateFishingShip( int id)
{
	ACTION *p, *targetAct;
	U4 BmpNo;
	short dx,dy;

	// すでにそのIDのアクションが作成されているかどうか
	if( CheckFishingShipID( id)) return;

	// 新规にアクションを作成する
	p = GetAction( PRIO_ETC, NULL);
	if( p == NULL) return;

	if( pc.id != id) targetAct = getCharObjAct( id );
	else targetAct = pc.ptAct;

	p->func = fishingship_func;

	realGetNo( 2000, (U4 *)&BmpNo );
	realGetPos( BmpNo, &dx, &dy);
	p->bmpNo = BmpNo;
	p->anim_x = dx;
	p->anim_y = dy;
	p->fx = 0;
	p->fy = 0;
	p->dispPrio = DISP_PRIO_CHAR-1;
	p->prio = PRIO_TOP;
	p->atr = ACT_ATR_TYPE_OTHER;
	p->actNo = id;
	p->x = targetAct->x;
	p->y = targetAct->y;
	p->anim_ang = targetAct->anim_ang;
}

#endif /* _FISHING_WINDOW */
