﻿#include<stdio.h>
#include<stdlib.h>
#include<time.h>

#include "../systeminc/system.h"
#include "../systeminc/menu.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/anim_tbl2.h"
#include "../systeminc/mouse.h"
#include "../systeminc/t_music.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/font.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/map.h"
#include "../systeminc/tool.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/pattern.h"
#include "../systeminc/menu2.h"
#include "../systeminc/loadrealbin.h"

#ifdef _OPERATION_REMAKE_ITEM
/*--------------------------------------
 * アイテムリメイク素材选择ウインドウ
 *------------------------------------*/
MENU_WINDOW_INFO remakeItemMaterialMenuWin =
{//   x,   y,   w,   h, menuNo,
	368,   0, 272, 380, MENU_REMAKE_ITEM_MATERIAL,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MATERIAL_SELECT_WIN
	},
};

char remakeItemSelItemFlag[MAX_DRAW_WIN_ITEM];
int remakeItemMaterialMenuMsg;
static int materialSelTakeItemNo;
static int materialSelTakeItemGraNo;

// 初期化
void initRemakeItemMaterialMenu( void )
{
	ZeroMemory( remakeItemSelItemFlag, sizeof( remakeItemSelItemFlag));
	remakeItemMaterialMenuMsg = 0;
	materialSelTakeItemNo = -1;
	materialSelTakeItemGraNo = 0;
}

// 选择されている素材が正しいかどうかチェック
int checkRemakeItemMaterials( void)
{
	int i;
	int index;
	BOOL materialflg = FALSE;
	BOOL materialBflg = FALSE;

	index = job.sortSkill[abilityPage].index;

	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++){
		if( !remakeItemSelItemFlag[i]) continue;
		else if( ( pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_REMAKEMATERIAL
			&& pc.item[MAX_EQUIP_ITEM+i].vardata1 & ( 1 << ( job.skill[index].id - 256))))
		{
			if( materialBflg) return 2;
			else {
				materialBflg = TRUE;
				continue;
			}
		}
		else if( ITEM_SWORD <= pc.item[MAX_EQUIP_ITEM+i].kind && pc.item[MAX_EQUIP_ITEM+i].kind <= ITEM_SHOES){
			if( materialflg) return 2;
			else if( pc.item[MAX_EQUIP_ITEM+i].flag & ITEM_ETC_FLAG_MERGED
#ifdef _APPEND_JEWEL
				&& pc.item[MAX_EQUIP_ITEM+i].flag & ITEM_ETC_FLAG_CANADDJEWEL
#endif /* _APPEND_JEWEL */
				)
			{
				materialflg = TRUE;
				continue;
			}
			else return 2;
		}
		else{
			return 2;
		}
	}

	if( materialflg && materialBflg) return 4;
	else return 2;
}

// 选择されたアイテムが正しいかチェックする
BOOL checkRemakeRegistItem( int itempos)
{
	int index;

	index = job.sortSkill[abilityPage].index;

	// 鉴定フラグチェック
	if( !pc.item[itempos].checkFlag) return FALSE;

	// リメイクするアイテム
	if( ITEM_SWORD <= pc.item[itempos].kind && pc.item[itempos].kind <= ITEM_SHOES){
#ifdef _APPEND_JEWEL
		// すでに宝石が追加されてるアイテムはリメイク不可
		if( !( pc.item[itempos].flag & ITEM_ETC_FLAG_CANADDJEWEL)){
			return FALSE;
		}
#endif /* _APPEND_JEWEL */

		// リメイクするアイテムは作成物でなければならない
		if( !( pc.item[itempos].flag & ITEM_ETC_FLAG_MERGED)){
			return FALSE;
		}
		// 使用するスキルに对应するアイテムでなければならない
		if( !( pc.item[itempos].kind == job.skill[index].id - 256)){
			return FALSE;
		}
	}
	else if( pc.item[itempos].kind == ITEM_REMAKEMATERIAL){
		// 使用するスキルに对应するアイテムでなければならない
		if( !( pc.item[itempos].vardata1 & ( 1 << ( job.skill[index].id - 256)))){
			return FALSE;
		}
	}
	else return FALSE;

	return TRUE;
}

// アイテムリメイク素材选择ウインドウ
void remakeItemMaterialMenu( int status )
{
	GRA_BTN_INFO1 registerBtn =
	{
		remakeItemMaterialMenuWin.x+140, remakeItemMaterialMenuWin.y+357,
		remakeItemMaterialMenuWin.x+100, remakeItemMaterialMenuWin.y+347,
		80, 20,
		CG_MATERIAL_SELECT_REGISTER_BTN_1, CG_MATERIAL_SELECT_REGISTER_BTN_2
	};
	GRA_BTN_INFO1 register2Btn =
	{
		remakeItemMaterialMenuWin.x+140, remakeItemMaterialMenuWin.y+357,
		remakeItemMaterialMenuWin.x+100, remakeItemMaterialMenuWin.y+347,
		80, 20,
		CG_MATERIAL_SELECT_REGISTER_BTN_3
	};
	int i, j;
	int focusItemNo;
	static int oldFocusItemNo;
	static int infoPage;
	int selCnt = 0;
	int ret;
	int len;
	int index;
	char str[256];

	// メニュー共通处理
	ret = menuCommonProc( status, &remakeItemMaterialMenuWin, initRemakeItemMaterialMenu,
			MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		// アイテムリメイクウィンドウを关闭
		menuClose( MENU_REMAKE_ITEM );
		return;
	}

	index = job.sortSkill[abilityPage].index;

	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++){
		selCnt += remakeItemSelItemFlag[i];
	}

	// どのアイテムにカーソルがあっているか调べる
	focusItemNo = -1;
	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( MakeHitBox(
				remakeItemMaterialMenuWin.x +8+j*52,
				remakeItemMaterialMenuWin.y+36+i*53,
				remakeItemMaterialMenuWin.x +8+j*52+48,
				remakeItemMaterialMenuWin.y+36+i*53+48, -1 ) )
			{
				focusItemNo = i * ITEM_DRAW_COLUMN + j;
				break;
			}
		}
	}

	// カーソル位置がかわったら说明ページを元に戾す
	if( focusItemNo != oldFocusItemNo )
	{
		infoPage = 0;
	}

	// つかんだアイテムを离す
	if( materialSelTakeItemNo >= 0
	 && (mouse.onceState & MOUSE_RIGHT_CRICK) )
	{
		materialSelTakeItemNo = -1;
		materialSelTakeItemGraNo = 0;
	}
	else
	// アイテム说明が出てる时に右クリックしたらページ切り替え
	if( focusItemNo >= 0 && (mouse.onceState & MOUSE_RIGHT_CRICK)
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag )
	{
		infoPage++;
		if( infoPage >= pc.item[MAX_EQUIP_ITEM+focusItemNo].memoPage )
		{
			infoPage = 0;
		}
	}
	else
	if( remakeItemMaterialMenuMsg > 4)
	{
	}
	else
	// アイテムをダブルクリックした时
	if( (mouse.onceState & MOUSE_LEFT_DBL_CRICK)
	 && focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag )
	{
		if( !checkRemakeRegistItem( MAX_EQUIP_ITEM+focusItemNo))
		{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
		else
		if( remakeItemSelItemFlag[focusItemNo] )
		{
			remakeItemSelItemFlag[focusItemNo] = 0;
			// キャンセル音
			play_se( 54, 320, 240 );
		}
		else
		{
//			if( selCnt < MATERIAL_SEL_MAX )
			if( selCnt < 2 )
			{
				remakeItemSelItemFlag[focusItemNo] = 1;
				// 决定音c（文字等クリック时）
				play_se( SE_NO_OK3, 320, 240 );
			}
		}
		materialSelTakeItemNo = -1;
		materialSelTakeItemGraNo = 0;
	}
	else
	// Registerボタンチェック
	if( ( pushGraBtnInfo1( &registerBtn) & BTN_LEFT_CLICK)){
		// 正しい素材が登録されているかチェック
		if( selCnt > 2) remakeItemMaterialMenuMsg = 2;
		else if( selCnt < 2) remakeItemMaterialMenuMsg = 1;
		else remakeItemMaterialMenuMsg = checkRemakeItemMaterials();
		// 正しい素材が登録されているなら、宝石追加ウインドウにセットする
		if( remakeItemMaterialMenuMsg == 4){
			for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++){
				if( !remakeItemSelItemFlag[i]) continue;
				else if( pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_REMAKEMATERIAL)
				{
					registItemGraNo[5] = pc.item[MAX_EQUIP_ITEM+i].graNo;
					registItemNum[5] = 1;
					registItemIndex[5] = i;
				}
				else {
					registItemGraNo[j] = pc.item[MAX_EQUIP_ITEM+i].graNo;
					registItemNum[j] = 1;
					registItemIndex[j] = i;
					j++;
				}
				if( j >= MATERIAL_SEL_MAX) break;
			}
		}
	}
	else
	// アイテムを掴んだ状态で别の枠を右键したらアイテム移动
	if( focusItemNo >= 0
	 && materialSelTakeItemNo >= 0
	 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		if( materialSelTakeItemNo != focusItemNo )
		{
			// アイテム移动プロトコル送信
			nrproto_MI_send( sockfd,
				MAX_EQUIP_ITEM+materialSelTakeItemNo, MAX_EQUIP_ITEM+focusItemNo, 0 );
			remakeItemSelItemFlag[materialSelTakeItemNo] = 0;
			remakeItemSelItemFlag[focusItemNo] = 0;
		}
		materialSelTakeItemNo = -1;
		materialSelTakeItemGraNo = 0;
		// クリック音	// ohta
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// アイテムを掴む
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag
	 && materialSelTakeItemNo < 0
	 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		materialSelTakeItemNo = focusItemNo;
		materialSelTakeItemGraNo = pc.item[MAX_EQUIP_ITEM+focusItemNo].graNo;
		// クリック音	// ohta
		play_se( SE_NO_CLICK, 320, 240 );
	}

	// 掴んだアイテムをマウスといっしょに移动
	if( materialSelTakeItemNo >= 0 )
	{
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_ITEM, materialSelTakeItemGraNo, 0 );
	}

	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].useFlag )
			{
				// アイテム画像表示
				StockDispBuffer( 
					remakeItemMaterialMenuWin.x +8+24+j*52,
					remakeItemMaterialMenuWin.y+36+24+i*53,
					DISP_PRIO_MENU, pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].graNo, 0 );
				if( pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].num > 0 )
				{
					// スタック数表示
					sprintf( str, "%3d", pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].num );
					StockFontBuffer(
						remakeItemMaterialMenuWin.x +8+21+j*52,
						remakeItemMaterialMenuWin.y+36+31+i*53,
						FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
						str, 0, 0 );
				}
				if( !checkRemakeRegistItem( MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j))
				{
					StockDispBuffer(
						remakeItemMaterialMenuWin.x +8+24+j*52+105,
						remakeItemMaterialMenuWin.y+36+24+i*53+93,
						DISP_PRIO_ITEM2, 22739, 0 );
				}
			}

			if( focusItemNo == i * ITEM_DRAW_COLUMN + j )
			{
				StockBoxDispBuffer(
					remakeItemMaterialMenuWin.x +8+j*52,
					remakeItemMaterialMenuWin.y+36+i*53,
					remakeItemMaterialMenuWin.x +8+j*52+48,
					remakeItemMaterialMenuWin.y+36+i*53+48,
					DISP_PRIO_MENU, BoxColor, 0 );
			}
			if( materialSelTakeItemNo == i * ITEM_DRAW_COLUMN + j )
			{
				StockBoxDispBuffer(
					remakeItemMaterialMenuWin.x +10+j*52,
					remakeItemMaterialMenuWin.y+38+i*53,
					remakeItemMaterialMenuWin.x +10+j*52+44,
					remakeItemMaterialMenuWin.y+38+i*53+44,
					DISP_PRIO_MENU, SYSTEM_PAL_AQUA, 0 );
			}
			else
			if( remakeItemSelItemFlag[i * ITEM_DRAW_COLUMN + j] )
			{
				StockBoxDispBuffer(
					remakeItemMaterialMenuWin.x + 10 + j * 52,
					remakeItemMaterialMenuWin.y + 38 + i * 53,
					remakeItemMaterialMenuWin.x + 10 + j * 52+44,
					remakeItemMaterialMenuWin.y + 38 + i * 53+44,
					DISP_PRIO_MENU, SYSTEM_PAL_YELLOW, 0 );
			}
		}
	}

	// カーソルがあっているアイテムの说明を出す
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag )
	{
		itemInfoWindow( remakeItemMaterialMenuWin.x, remakeItemMaterialMenuWin.y+remakeItemMaterialMenuWin.h/2+54,
			MAX_EQUIP_ITEM+focusItemNo, infoPage );
	}

	if( remakeItemMaterialMenuMsg <= 4){
		drawGraBtnInfo1( &registerBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	else {
		drawGraBtnInfo1( &register2Btn, DISP_PRIO_MENU, 1, BoxColor, 1 );
	}

	if( remakeItemMaterialMenuMsg){
		switch( remakeItemMaterialMenuMsg)
		{
		case 1:
			strcpy( str, "材料不足。");
			break;
		case 2:
			strcpy( str, "材料选择不正确。");
			break;
		case 3:
			strcpy( str, "无法登录材料。");
			break;
		case 4:
			strcpy( str, "已经登录了材料。");
			break;
		case 5:
			strcpy( str, "魔力不足。");
			break;
		case 6:
		case 7:
			strcpy( str, "加工中......");
			break;

		default:
			break;
		}
		len = GetStrWidth( str, FONT_KIND_MIDDLE );
		StockFontBuffer(
			remakeItemMaterialMenuWin.x+remakeItemMaterialMenuWin.w/2-len/2,
			remakeItemMaterialMenuWin.y+278,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
	}
	// 一行インフォ表示
	// x ボタン
	if( checkFocusMenuClose( &remakeItemMaterialMenuWin ) )
	{
		strcpy( OneLineInfoStr, "关闭这个窗口。" );
	}
	// Registerボタン
	if( ( pushGraBtnInfo1( &registerBtn) & BTN_FOCUS_ON) &&
		remakeItemMaterialMenuMsg <= 4)
	{
		if( selCnt > 0 )
		{
			strcpy( OneLineInfoStr, "将选择的物品作为材料登录。" );
		}
		else
		{
			strcpy( OneLineInfoStr, "无法选择材料。" );
		}
	}
	// アイテム
	else
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag)
	{
		if(pc.item[MAX_EQUIP_ITEM+focusItemNo].flag & ITEM_ETC_FLAG_INCUSE){
			strcpy( OneLineInfoStr, pc.item[MAX_EQUIP_ITEM+focusItemNo].name  );
			strcat( OneLineInfoStr,ITEM_INCUSE_STRING);
		}else if(pc.item[MAX_EQUIP_ITEM+focusItemNo].flag & ITEM_ETC_FLAG_HANKO){
			strcpy( OneLineInfoStr, pc.item[MAX_EQUIP_ITEM+focusItemNo].freeName );
			strcat( OneLineInfoStr,ITEM_HANKO_STRING);
		}else
		if( remakeItemSelItemFlag[focusItemNo] )
		{
			strcpy( OneLineInfoStr, "选择这个物品。" );
		}
		else
		// 素材Ｂなら
		if( checkRemakeRegistItem( MAX_EQUIP_ITEM+focusItemNo))
		{
			strcpy( OneLineInfoStr, "选择这个物品。" );
		}
		else
		{
			strcpy( OneLineInfoStr, "无法选择这个物品。" );
		}
	}

	// メニュー共通表示处理
	menuCommonDraw( &remakeItemMaterialMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );

	oldFocusItemNo = focusItemNo;
}

/*--------------------------------------
 * アイテムリメイクウインドウ
 *------------------------------------*/
MENU_WINDOW_INFO remakeItemMenuWin =
{//   x,   y,   w,   h, menuNo,
	  0,   0, 344, 380, MENU_REMAKE_ITEM,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_CREATE_WIN
	},
};

static ACTION *ptActCreateChara;

// 加工中キャラの演出（最初のポーズに设定する）
static void createCharaProduceStart( void )
{
	if( ptActCreateChara != NULL )
	{
		if( ( SPRPC_START <= ptActCreateChara->anim_chr_no
			&& ptActCreateChara->anim_chr_no <= SPRPC_END)
#ifdef _CG2_NEWGRAPHIC
		 || ( SPRPC_START_V2 <= ptActCreateChara->anim_chr_no
			&& ptActCreateChara->anim_chr_no <= SPRPC_END_V2 )
#endif
		){
			ptActCreateChara->anim_no = ANIM_NOD;
		}else{
			ptActCreateChara->anim_no = ANIM_MAGIC;
		}
		if( ptActCreateChara->anim_no != ANIM_WALK )
		{
			ptActCreateChara->anim_no_bak = -1;
		}
	}
	pattern( ptActCreateChara, ANM_NOMAL_SPD, ANM_LOOP );
}

void initRemakeItemMenu( void )
{
	memoryMapGridPos( mapGx, mapGy );
	if( !ptActCreateChara){
		ptActCreateChara = createActEmChara( pc.graNo, remakeItemMenuWin.x + 94, remakeItemMenuWin.y + 290);
	}
	ptActCreateChara->atr |= ACT_ATR_HIDE;
	clearRegistItem();
}

void remakeItemMenu( int status )
{
	GRA_BTN_INFO1 executeBtn =
	{
		remakeItemMenuWin.x +97, remakeItemMenuWin.y+357,
		remakeItemMenuWin.x +57, remakeItemMenuWin.y+347,
		80, 20,
		CG_CREATE_EXECUTE_BTN_1, CG_CREATE_EXECUTE_BTN_2
	};
	GRA_BTN_INFO1 execute2Btn =
	{
		remakeItemMenuWin.x +97, remakeItemMenuWin.y+357,
		remakeItemMenuWin.x +57, remakeItemMenuWin.y+347,
		80, 20,
		CG_CREATE_EXECUTE_BTN_3
	};
	GRA_BTN_INFO1 cancelBtn =
	{
		remakeItemMenuWin.x+257, remakeItemMenuWin.y+357,
		remakeItemMenuWin.x+217, remakeItemMenuWin.y+347,
		80, 20,
		CG_COMMON_SMALL_CANCEL_BTN_1, CG_COMMON_SMALL_CANCEL_BTN_2
	};
	int itemPos[][2] =
	{
		{ 177, 214 },
		{ 229, 214 },
		{ 281, 214 },
		{ 177, 267 },
		{ 229, 267 },
		{ 281, 267 }
	};
	int i;
	int ret;
	int len;
	int index = job.sortSkill[abilityPage].index;
	static int startTime = 0, produceTime = 0, elapsedTime = 0;
	char str[256];

	// メニュー共通处理
	ret = menuCommonProc( status, &remakeItemMenuWin, initRemakeItemMenu,
			MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		if( ptActCreateChara){
			DeathAction( ptActCreateChara);
			ptActCreateChara = NULL;
		}
		// アイテムリメイク素材选择ウィンドウを关闭
		menuClose( MENU_REMAKE_ITEM_MATERIAL );
		return;
	}

	if( ptActCreateChara){
		ptActCreateChara->atr &= ~ACT_ATR_HIDE;
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 1, 1 ) )
	{
		// このウィンドウを关闭
		menuClose( remakeItemMenuWin.menuNo );
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	// ＣＡＮＣＥＬボタン押したか？
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_LEFT_CLICK) )
	{
		// このウィンドウを关闭
		menuClose( remakeItemMenuWin.menuNo );
		// アビリティウィンドウ开く
		menuOpen( MENU_ABILITY );
		// 决定音c（文字等クリック时）// ohta
		//play_se( SE_NO_OK3, 320, 240 );
	}
	else
	if( remakeItemMaterialMenuMsg == 5 || remakeItemMaterialMenuMsg == 7)
	{
	}
	else
	// 加工中でオペレーション抑制
	if( remakeItemMaterialMenuMsg == 6 )
	{
		elapsedTime = GetTickCount() - startTime;
		//createCharaProduce( elapsedTime );
		if( produceTime < elapsedTime )
		{
			char str2[128];
			// 送信文字列作成
			// 宝石追加では、登録アイテムの一番最初と最后しか使われていない
			if( registItemIndex[0] >= 0 )
			{
				sprintf( str2, "%d|", MAX_EQUIP_ITEM+registItemIndex[0] );
				strcpy( str, str2 );
			}
			if( registItemIndex[5] >= 0){
				sprintf( str2, "%d", MAX_EQUIP_ITEM+registItemIndex[5] );
				strcat( str, str2 );
			}

			// S-JIS から EUC に变换
			sjisStringToEucString( str );

			// 宝石を追加するアイテム名の保存
			strcpy( recipeNameBak, pc.item[MAX_EQUIP_ITEM+registItemIndex[0]].name);

			// 技使用プロトコル送信
			nrproto_TU_send( sockfd, index, selTech, -1, str );
			remakeItemMaterialMenuMsg = 7;
		}
	}
	else
	// EXECUTEボタン押したか？
	if( (pushGraBtnInfo1( &executeBtn ) & BTN_LEFT_CLICK) )
	{
		if( registItemGraNo[0] > 0 )
		{
			if( pc.fp >= 50)
			{
				remakeItemMaterialMenuMsg = 6;
				startTime = GetTickCount();
//				produceTime = 5000 + (rand() % 6) * 1000
//								+ pc.item[MAX_EQUIP_ITEM+registItemIndex[0]].lv * 3000;
				produceTime = 100;

				createCharaProduceStart();
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
			}
			else
			{
				remakeItemMaterialMenuMsg = 5;
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
		}
		else
		{
			// 「素材が登録されてません。」って表示
			remakeItemMaterialMenuMsg = 3;
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	// 宝石追加表示
	len = GetStrWidth( job.skill[index].name, FONT_KIND_MIDDLE );
	StockFontBuffer( remakeItemMenuWin.x+11+126-len/2, remakeItemMenuWin.y+37,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		job.skill[index].name, 0, 0 );

	// 魔力表示
	sprintf( str, "%4d", 50 );
	StockFontBuffer( remakeItemMenuWin.x+295, remakeItemMenuWin.y+39,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );

	if( registItemGraNo[0] > 0){
		for( i = 0; i < MATERIAL_SEL_MAX; i++){
			if( registItemGraNo[i] > 0){
				// 素材画像表示
				StockDispBuffer(
					remakeItemMenuWin.x+itemPos[i][0]+24,
					remakeItemMenuWin.y+itemPos[i][1]+24,
					DISP_PRIO_ITEM2, registItemGraNo[i], 0 );
				if( registItemNum[i] > 0 )
				{
					// スタック数表示
					sprintf( str, "%3d", registItemNum[i] );
					StockFontBuffer(
						remakeItemMenuWin.x+itemPos[i][0]+21,
						remakeItemMenuWin.y+itemPos[i][1]+31,
						FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
						str, 0, 0 );
				}
			}
		}
	}

	// ＥＸＥＣＵＴＥボタン表示
	if( remakeItemMaterialMenuMsg <= 4){
		drawGraBtnInfo1( &executeBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	else {
		drawGraBtnInfo1( &execute2Btn, DISP_PRIO_MENU, 1, BoxColor, 1 );
	}

	// ＣＡＮＣＥＬボタン表示
	drawGraBtnInfo1( &cancelBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// x ボタン
	if( checkFocusMenuClose( &remakeItemMenuWin ) )
	{
		strcpy( OneLineInfoStr, "关闭这个窗口。" );
	}
	else
	// EXECUTE ボタン
	if( (pushGraBtnInfo1( &executeBtn ) & BTN_FOCUS_ON) )
	{
		if( registItemGraNo[0] > 0 )
		{
			wsprintf( OneLineInfoStr, "执行%s。", job.skill[index].name);
		}
		else
		{
			strcpy( OneLineInfoStr, "无法登录材料。" );
		}
	}
	else
	// CANCEL ボタン
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, "返回能力窗口。" );
	}

	// メニュー共通表示处理
	menuCommonDraw( &remakeItemMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}

#endif /* _OPERATION_REMAKE_ITEM */

#ifdef _ENABLE_ALBUM_ITEMS

/*--------------------------------------
 * アルバムウインドウ
 *------------------------------------*/
MENU_WINDOW_INFO albumWindowMenu;

ALBUMITEM_MENU_INFO albumWindowInfo;

static int albumWindowPage;
static int albumWindowPageMax;

void openAlbumWindow( char *data)
{
	int i = 0;
	int j = 1;
	int page = 0;
	BOOL flg = FALSE;
	char buf[256] = "";

	// 构造体を初期化
	ZeroMemory( &albumWindowMenu, sizeof( albumWindowMenu));
	ZeroMemory( &albumWindowInfo, sizeof( albumWindowInfo));

	// フレーム情报を取得
	albumWindowInfo.framewindow.x = getIntegerToken( data, '|', j++);
	albumWindowInfo.framewindow.y = getIntegerToken( data, '|', j++);
	albumWindowInfo.framewindow.fx = getIntegerToken( data, '|', j++);
	albumWindowInfo.framewindow.fy = getIntegerToken( data, '|', j++);
	albumWindowInfo.framewindow.img = getIntegerToken( data, '|', j++);

	// フレーム情报をメニュー情报に设定
	albumWindowMenu.x = albumWindowInfo.framewindow.x;
	albumWindowMenu.y = albumWindowInfo.framewindow.y;
	albumWindowMenu.w = albumWindowInfo.framewindow.fx;
	albumWindowMenu.h = albumWindowInfo.framewindow.fy;
	albumWindowMenu.menuNo = MENU_ALBUM_WINDOW;
	albumWindowMenu.winGraNo[0] = albumWindowInfo.framewindow.img;

	// ページ情报を取得
	while(1){
		if( !flg){
			albumWindowInfo.albumimg[i].x = getIntegerToken( data, '|', j++);
			if( albumWindowInfo.albumimg[i].x == -1) break;
			albumWindowInfo.albumimg[i].y = getIntegerToken( data, '|', j++);
			albumWindowInfo.albumimg[i].img = getIntegerToken( data, '|', j++);
			if( getStringToken( data, '|', j, sizeof( buf), buf) == 1) break;
			if( buf[0] == 'N'){
				albumWindowInfo.albumimg[i].flg = 1;
				j++;
				i++;
				if( getStringToken( data, '|', j, sizeof( buf), buf) == 1) break;
				if( buf[0] == 'B'){
					flg = 1;
					j++;
					i = 0;
				}
			}
			else if( buf[0] == 'B'){
				albumWindowInfo.albumimg[i].flg = 1;
				flg = 1;
				j++;
				i = 0;
			}
			else i++;
		}
		else if( flg == 1){
			albumWindowInfo.buttons[i].type = getIntegerToken( data, '|', j++);
			if( albumWindowInfo.buttons[i].type == -1) break;
			else if( albumWindowInfo.buttons[i].type == 0){
				getStringToken( data, '|', j++, sizeof( albumWindowInfo.buttons[i].string), albumWindowInfo.buttons[i].string);
				makeRecvString( albumWindowInfo.buttons[i].string);
			}
			else {
				albumWindowInfo.buttons[i].imgno1 = getIntegerToken( data, '|', j++);
				albumWindowInfo.buttons[i].imgno2 = getIntegerToken( data, '|', j++);
			}
			albumWindowInfo.buttons[i].x = getIntegerToken( data, '|', j++);
			albumWindowInfo.buttons[i].y = getIntegerToken( data, '|', j++);
			albumWindowInfo.buttons[i].fx = getIntegerToken( data, '|', j++);
			albumWindowInfo.buttons[i].fy = getIntegerToken( data, '|', j++);
			albumWindowInfo.buttons[i].cmd = getIntegerToken( data, '|', j++);
			if( getStringToken( data, '|', j, sizeof( buf), buf) == 1) break;
			if( buf[0] == 'T'){
				flg++;
				j++;
				i = 0;
			}
			else i++;
		}
		else if( flg == 2){
			albumWindowInfo.text[i].x = getIntegerToken( data, '|', j++);
			albumWindowInfo.text[i].y = getIntegerToken( data, '|', j++);
			albumWindowInfo.text[i].color = getIntegerToken( data, '|', j++);
			getStringToken( data, '|', j++, sizeof( albumWindowInfo.text[i].string), albumWindowInfo.text[i].string);
			makeRecvString( albumWindowInfo.text[i].string);
			if( getStringToken( data, '|', j, sizeof( buf), buf) == 1) break;
			if( buf[0] == 'S'){
				flg++;
				j++;
				i = 0;
			}
			else i++;
		}
		else if( flg == 3){
			albumWindowInfo.sounds[i] = getIntegerToken( data, '|', j++);
			if( getIntegerToken( data, '|', j) == -1) break;
			i++;
		}
	}

	// メニューを开く
	menuOpen( MENU_ALBUM_WINDOW);
}

// アルバムウインドウ初期化
void initAlbumItemMenu( void )
{
	int i;

	albumWindowPage = 0;
	albumWindowPageMax = 0;

	// 最大で何ページあるか调べる
	for( i = 0; i < ALBUM_MAX_FLAGS; i++){
		if( albumWindowInfo.albumimg[i].flg == 1) albumWindowPageMax++;
	}
	albumWindowPageMax--;

	// 开くサウンドがある场合はサウンドを再生する
	if( albumWindowInfo.sounds[0] > 0){
		play_se( albumWindowInfo.sounds[0], 320, 240);
	}
}

// アルバムウインドウ
void albumItemMenu( int status)
{
	GRA_BTN_INFO1 albumBtnImg[ALBUM_MAX_BUTTONS];
	STR_BTN_INFO albumBtnStr[ALBUM_MAX_BUTTONS];
	int ret;
	int starti;
	int i;
	int j = 0;
	unsigned long BmpNo;
	short tx, ty;
	BOOL flg = 0;

	// メニュー共通处理
	ret = menuCommonProc( status, &albumWindowMenu, initAlbumItemMenu,
			MENU_COMMON_FLAG_CLOSE_BUTTON );

	// ウインドウオープンアニメーション中
	if( ret == 0)
	{
		return;
	}
	else
	// ウインドウクローズ时
	if( ret == 2)
	{
		serverRequestWinWindowType = -1;
		// 关闭サウンドがあるときはサウンドを再生する
		if( albumWindowInfo.sounds[1] > 0){
			play_se( albumWindowInfo.sounds[1], 320, 240);
		}
		return;
	}

	// ボタン构造体を初期化
	ZeroMemory( albumBtnImg, sizeof( albumBtnImg));
	ZeroMemory( albumBtnStr, sizeof( albumBtnStr));

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if( checkMoveMapGridPos( 2, 2 ) )
	{
		// このウィンドウを关闭
		menuClose( albumWindowMenu.menuNo );
	}

	// 现在のページ数から読み込み开始位置を决定する
	for( i = 0; i < ALBUM_MAX_FLAGS; i++){
		if( j == albumWindowPage) break;
		if( albumWindowInfo.albumimg[i].flg == 1) j++;
	}

	starti = i;

	// ボタンを设定する
	for( i = 0; i < ALBUM_MAX_BUTTONS; i++){
		if( !albumWindowInfo.buttons[i].type){
			albumBtnStr[i].cx = albumWindowInfo.buttons[i].x + albumWindowInfo.framewindow.x;
			albumBtnStr[i].cy = albumWindowInfo.buttons[i].y + albumWindowInfo.framewindow.y;
			albumBtnStr[i].x = albumWindowInfo.buttons[i].x + albumWindowInfo.framewindow.x;
			albumBtnStr[i].y = albumWindowInfo.buttons[i].y + albumWindowInfo.framewindow.y;
			albumBtnStr[i].w = albumWindowInfo.buttons[i].fx;
			albumBtnStr[i].h = albumWindowInfo.buttons[i].fy;
			albumBtnStr[i].str = albumWindowInfo.buttons[i].string;
		}
		else{
			albumBtnImg[i].cx = albumWindowInfo.buttons[i].x + albumWindowInfo.framewindow.x;
			albumBtnImg[i].cy = albumWindowInfo.buttons[i].y + albumWindowInfo.framewindow.y;
			if( albumWindowInfo.buttons[i].imgno1 < SPRSTART || albumWindowInfo.buttons[i].imgno1 >= SPREND){
				realGetNo( albumWindowInfo.buttons[i].imgno1, &BmpNo);
				realGetPos( BmpNo, &tx, &ty);
				albumBtnImg[i].x = albumWindowInfo.buttons[i].x + tx + albumWindowInfo.framewindow.x;
				albumBtnImg[i].y = albumWindowInfo.buttons[i].y + ty + albumWindowInfo.framewindow.y;
				albumBtnImg[i].w = (albumWindowInfo.buttons[i].x + (tx * -1) + albumWindowInfo.framewindow.x) - albumBtnImg[i].x;
				albumBtnImg[i].h = (albumWindowInfo.buttons[i].y + (ty * -1) + albumWindowInfo.framewindow.y) - albumBtnImg[i].y;
			}
			albumBtnImg[i].graNo1 = albumWindowInfo.buttons[i].imgno1;
			albumBtnImg[i].graNo2 = albumWindowInfo.buttons[i].imgno2;
		}
	}

	// ボタンが押されたか调べる
	for( i = 0; i < ALBUM_MAX_BUTTONS; i++){
		flg = 0;
		if( !albumWindowInfo.buttons[i].type){
			if( pushStrBtnInfo( &albumBtnStr[i]) & BTN_LEFT_CLICK_REP){
				flg = 1;
			}
		}
		else {
			if( pushGraBtnInfo1( &albumBtnImg[i]) & BTN_LEFT_CLICK_REP){
				flg = 1;
			}
		}
		if( flg){
			if( !albumWindowInfo.buttons[i].cmd){
				if( albumWindowPage > 0) albumWindowPage--;
			}
			else {
				if( albumWindowPage < albumWindowPageMax) albumWindowPage++;
			}
		}
	}

	// 表示する
	for( i = starti; i < ALBUM_MAX_FLAGS; i++){
		StockDispBuffer(
			albumWindowInfo.albumimg[i].x + albumWindowInfo.framewindow.x,
			albumWindowInfo.albumimg[i].y + albumWindowInfo.framewindow.y,
			DISP_PRIO_BOXFILL,
			albumWindowInfo.albumimg[i].img,
			0
		);
		if( albumWindowInfo.albumimg[i].flg) break;
	}

	// ボタンを表示する
	for( i = 0; i < ALBUM_MAX_BUTTONS; i++){
		if( !albumWindowInfo.buttons[i].type){
			drawStrBtnInfo( &albumBtnStr[i], FONT_PRIO_FRONT, FONT_PAL_YELLOW, 1, BoxColor, DISP_PRIO_BOX2);
		}
		else {
			drawGraBtnInfo1( &albumBtnImg[i], DISP_PRIO_ITEM2, 1, BoxColor, 0);
		}
	}

	// テキストを表示する
	for( i = 0; i < ALBUM_MAX_TEXT; i++){
		if( albumWindowInfo.text[i].string[0] == '\0') break;
		StockFontBuffer(
			albumWindowInfo.text[i].x,
			albumWindowInfo.text[i].y,
			FONT_PRIO_FRONT,
			FONT_KIND_MIDDLE,
			albumWindowInfo.text[i].color,
			albumWindowInfo.text[i].string,
			0
		);
	}

	menuCommonDraw( &albumWindowMenu, MENU_COMMON_FLAG_CLOSE_BUTTON );
}

#endif /* _ENABLE_ALBUM_ITEMS */
