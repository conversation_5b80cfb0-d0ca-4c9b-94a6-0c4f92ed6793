﻿/***************************************
			anim_tbl_PUK2.h
***************************************/
#ifndef _ANIM_TBL_PUK2
#define _ANIM_TBL_PUK2


// アニメーション ********************//

//		サメ目
#define SPR_mon1000a		110300		//		陆サメ
#define SPR_mon1000b		110301		//		砂シャーク
#define SPR_mon1000c		110302		//		丘フカ
#define SPR_mon1000d		110303		//		岩シュモク

//		カモノハシ目
#define SPR_mon1001a		110304		//		ヒヨコ
#define SPR_mon1001b		110305		//		カモノハシ
#define SPR_mon1001c		110306		//		アヒル
#define SPR_mon1001d		110307		//		ウサギ
#define SPR_mon1001e		110308		//		ニワトリ（BOSS）

//		イワ目
#define SPR_mon1002a		110309		//		オ布ル
#define SPR_mon1002b		110310		//		ルビー
#define SPR_mon1002c		110311		//		ダイヤ
#define SPR_mon1002d		110312		//		クリスタル

//		ダチョウ目
#define SPR_mon1003a		110313		//		ナイフチキン
#define SPR_mon1003b		110314		//		ソードオストリッチ
#define SPR_mon1003c		110315		//		チョッ布エミュ
#define SPR_mon1003d		110316		//		ブレードムー

//		トカゲ目
#define SPR_mon1004a		110317		//		ファイヤーリザ－ド
#define SPR_mon1004b		110318		//		ポイズンリザード
#define SPR_mon1004c		110319		//		アイスリザ－ド
#define SPR_mon1004d		110320		//		メタルリザ－ド

//		靴目
#define SPR_mon1005a		110321		//		ブーツかぶり
#define SPR_mon1005b		110322		//		ブーツマイマイ
#define SPR_mon1005c		110323		//		ブーツもぐり
#define SPR_mon1005d		110324		//		ブーツでんでん


#define SPR_mon1000			110325		//		シュモキング


//		人间目
#define SPR_mon1006a		110326		//突击神官	
#define SPR_mon1006b		110327		//机甲神官
#define SPR_mon1006c		110328		//机动神官
#define SPR_mon1006d		110329		//駆逐神官
#define SPR_mon1006bossA	110330		//ド级神官
#define SPR_mon1006bossB	110331		//ド级神官
#define SPR_mon1006bossC	110332		//ド级神官
#define SPR_mon1006bossD	110333		//ド级神官

//		死体目　骸骨恐竜
#define SPR_mon1007a		110334			//ドラゴスカル
#define SPR_mon1007b		110335			//ダークスカル
#define SPR_mon1007c		110336			//ゴールドスカル	
#define SPR_mon1007d		110337			//ポイズンスカル
#define SPR_mon1007bossA	110338			//龙骨	
#define SPR_mon1007bossB	110339			//灼龙骨
#define SPR_mon1007bossC	110340			//冻龙骨
#define SPR_mon1007bossD	110341			//闇龙骨	

//		机械目（丸ロボ）
#define SPR_mon1008a		110342			//ポップマイン
#define SPR_mon1008b		110343			//チップマイン
#define SPR_mon1008c		110344			//ピップマイン
#define SPR_mon1008d		110345			//サップマイン
#define SPR_mon1008bossA	110346			//カッ布ガーディアン	
#define SPR_mon1008bossB	110347			//ブラックガーディアン	
#define SPR_mon1008bossC	110348			//プラチナガーディアン	
#define SPR_mon1008bossD	110349			//ゴールドガーディアン	



//		トランスキャラ
#define SPR_rt00_ax		110350		//		トランスキャラ（地）
#define SPR_rt00_bw		110351
#define SPR_rt00_em		110352
#define SPR_rt00_sd		110353
#define SPR_rt00_sf		110354
#define SPR_rt00_sp		110355
#define SPR_rm00_ax		110356		//		トランスキャラ（水）
#define SPR_rm00_bw		110357
#define SPR_rm00_em		110358
#define SPR_rm00_sd		110359
#define SPR_rm00_sf		110360
#define SPR_rm00_sp		110361
#define SPR_rh00_ax		110362		//		トランスキャラ（火）
#define SPR_rh00_bw		110363
#define SPR_rh00_em		110364
#define SPR_rh00_sd		110365
#define SPR_rh00_sf		110366
#define SPR_rh00_sp		110367
#define SPR_rk00_ax		110368		//		トランスキャラ（风）
#define SPR_rk00_bw		110369
#define SPR_rk00_em		110370
#define SPR_rk00_sd		110371
#define SPR_rk00_sf		110372
#define SPR_rk00_sp		110373

//PUK运营用セット***********************************************************************
//#define SPR_mon		110374			//	
//#define SPR_mon		110375			//	

#define SPR_mon1005e		110376			//ミル??クリスマス
#define SPR_mon1002e		110377			//ロック
#define SPR_mon1001f		110378			//イタチ
#define SPR_mon1001g		110379			//ダークニワトリ	
//PUK运营用セット***********************************************************************

//		猫目（ライON）
#define SPR_mon1009a		110380			//白银	
#define SPR_mon1009b		110381			//黄铜	
#define SPR_mon1009c		110382			//黒钢	
#define SPR_mon1009d		110383			//赤铜	
#define SPR_mon1009bossA	110384			//白獣	
#define SPR_mon1009bossB	110385			//黒獣	
#define SPR_mon1009bossC	110386			//獣王	
#define SPR_mon1009bossD	110387			//桃獣

//		植物目
#define SPR_mon1010a		110388			//ハナアルキ
#define SPR_mon1010b		110389			//ハナバシリ
#define SPR_mon1010c		110390			//ハナオドリ
#define SPR_mon1010d		110391			//ハナマルキ
#define SPR_mon1010bossA	110392			//デカハナアルキ	
#define SPR_mon1010bossB	110393			//デカハナバシリ
#define SPR_mon1010bossC	110394			//デカハナオドリ
#define SPR_mon1010bossD	110395			//デカハナマルキ

//PUK2雪だるま						
/***********************************************************************************								
#define SPR_mon1011a		110396			//雪だるま			
#define SPR_mon1011b		110397			//雪だるま			
#define SPR_mon1011c		110398			//雪だるま			
#define SPR_mon1011d		110399			//雪だるま			
************************************************************************************/								

//		フィールドアイコン
#define SPR_icn_1			110400		//		リーダーマーク	1
#define SPR_icn_2			110401		//		战闘	2
#define SPR_icn_3			110402		//		デュエル	3
#define SPR_icn_4			110403		//		観战	4
#define SPR_icn_5			110404		//		采取	5
#define SPR_icn_6			110405		//		采掘	6
#define SPR_icn_7			110406		//		狩猟	7
#define SPR_icn_8			110407		//		釣り	8
#define SPR_icn_9			110408		//		制造	9
#define SPR_icn_10			110409		//		调理	10
#define SPR_icn_11			110410		//		鉴定	11
#define SPR_icn_12			110411		//		修理	12
#define SPR_icn_13			110412		//		治疗	13
#define SPR_icn_14			110413		//		等级アップ	14
#define SPR_icn_15			110414		//		怪我	15

//		トランスエフェクト地
#define SPR_eff_earth_on0	110420		//		ONエフェクト左上	1
#define SPR_eff_earth_on4	110421		//		ONエフェクト右下	2
#define SPR_eff_earth_on5	110422		//		ONエフェクト正面	3
#define SPR_eff_earth_off0	110423		//		OFFエフェクト左上	4
#define SPR_eff_earth_off4	110424		//		OFFエフェクト右下	5
#define SPR_eff_earth_off5	110425		//		OFFエフェクト正面	6
#define SPR_eff_earth_loop1	110426		//		エフェクト１	7
#define SPR_eff_earth_loop2	110427		//		エフェクト２	8
#define SPR_eff_earth_loop3	110428		//		エフェクト３	9
#define SPR_eff_earth_loop4	110429		//		エフェクト４	10
#define SPR_eff_earth_dmg	110430		//		受伤	11
#define SPR_eff_earth_hit	110431		//		ヒット	12

//		トランスエフェクト水
#define SPR_eff_water_on0	110432		//		ONエフェクト左上	13
#define SPR_eff_water_on4	110433		//		ONエフェクト右下	14
#define SPR_eff_water_on5	110434		//		ONエフェクト正面	15
#define SPR_eff_water_off0	110435		//		OFFエフェクト左上	16
#define SPR_eff_water_off4	110436		//		OFFエフェクト右下	17
#define SPR_eff_water_off5	110437		//		OFFエフェクト正面	18
#define SPR_eff_water_loop1	110438		//		エフェクト１	19
#define SPR_eff_water_loop2	110439		//		エフェクト２	20
#define SPR_eff_water_loop3	110440		//		エフェクト３	21
#define SPR_eff_water_loop4	110441		//		エフェクト４	22
#define SPR_eff_water_dmg	110442		//		受伤	23
#define SPR_eff_water_hit	110443		//		ヒット	24


//		トランスエフェクト炎
#define SPR_eff_fire_on0	110444		//		ONエフェクト左上	25
#define SPR_eff_fire_on4	110445		//		ONエフェクト右下	26
#define SPR_eff_fire_on5	110446		//		ONエフェクト正面	27
#define SPR_eff_fire_off0	110447		//		OFFエフェクト左上	28
#define SPR_eff_fire_off4	110448		//		OFFエフェクト右下	29
#define SPR_eff_fire_off5	110449		//		OFFエフェクト正面	30
#define SPR_eff_fire_loop1	110450		//		エフェクト１	31
#define SPR_eff_fire_loop2	110451		//		エフェクト２	32
#define SPR_eff_fire_loop3	110452		//		エフェクト３	33
#define SPR_eff_fire_loop4	110453		//		エフェクト４	34
#define SPR_eff_fire_dmg	110454		//		受伤	36
#define SPR_eff_fire_hit	110455		//		ヒット	35

//		トランスエフェクト风
#define SPR_eff_wind_on0			110456		//		ONエフェクト左上	37
#define SPR_eff_wind_on4			110457		//		ONエフェクト右下	38
#define SPR_eff_wind_on5			110458		//		ONエフェクト正面	39
#define SPR_eff_wind_off0			110459		//		OFFエフェクト左上	40
#define SPR_eff_wind_off4			110460		//		OFFエフェクト右下	41
#define SPR_eff_wind_off5			110461		//		OFFエフェクト正面	42
#define SPR_eff_wind_loop1			110462		//		エフェクト１	43
#define SPR_eff_wind_loop2			110463		//		エフェクト２	44
#define SPR_eff_wind_loop3			110464		//		エフェクト３	45
#define SPR_eff_wind_loop4			110465		//		エフェクト４	46
#define SPR_eff_wind_dmg			110466		//		受伤	47
#define SPR_eff_wind_hit			110467		//		ヒット	48


//		公会宠物召还エフェクト大用
#define SPR_eff_guildmon_cam		110468		//		公会宠物大登场エフェクト

//スキル用エフェクト运营中追加（V3）
#define SPR_efect01			110469		//		スキル用エフェクト１
#define SPR_efect02			110470		//		スキル用エフェクト２
#define SPR_efect03			110471		//		スキル用エフェクト３
#define SPR_efect04			110472		//		スキル用エフェクト４
#define SPR_efect05			110473		//		スキル用エフェクト５
#define SPR_efect06			110474		//		スキル用エフェクト６
#define SPR_efect07			110475		//		スキル用エフェクト７
#define SPR_efect08			110476		//		スキル用エフェクト８
#define SPR_efect09			110477		//		スキル用エフェクト９




#define SPR_gate001			110500		//		ルーンゲート无料
#define SPR_gate002			110501		//		ルーンゲート有料
#define SPR_gate003			110502		//		旧世界ルーンゲート
#define SPR_priest			110503		//		旧世界用神官

#define SPR_gate004			110504		//		ミニルーンゲート（20040128追加）


//公会宠物极小						
#define SPR_Gmon_ss_00a			110600		//	公会宠物极小0A
#define SPR_Gmon_ss_00b			110601		//	公会宠物极小0B
#define SPR_Gmon_ss_00c			110602		//	公会宠物极小0C
#define SPR_Gmon_ss_00d			110603		//	公会宠物极小0D
#define SPR_Gmon_ss_00e			110604		//	公会宠物极小0E
#define SPR_Gmon_ss_00f			110605		//	公会宠物极小0F

#define SPR_Gmon_ss_01a			110606		//	公会宠物极小1A
#define SPR_Gmon_ss_01b			110607		//	公会宠物极小1B
#define SPR_Gmon_ss_01c			110608		//	公会宠物极小1C
#define SPR_Gmon_ss_01d			110609		//	公会宠物极小1D
#define SPR_Gmon_ss_01e			110610		//	公会宠物极小1E
#define SPR_Gmon_ss_01f			110611		//	公会宠物极小1F

#define SPR_Gmon_ss_02a			110612		//	公会宠物极小2A
#define SPR_Gmon_ss_02b			110613		//	公会宠物极小2B
#define SPR_Gmon_ss_02c			110614		//	公会宠物极小2C
#define SPR_Gmon_ss_02d			110615		//	公会宠物极小2D
#define SPR_Gmon_ss_02e			110616		//	公会宠物极小2E
#define SPR_Gmon_ss_02f			110617		//	公会宠物极小2F

#define SPR_Gmon_ss_03a			110618		//	公会宠物极小3A
#define SPR_Gmon_ss_03b			110619		//	公会宠物极小3B
#define SPR_Gmon_ss_03c			110620		//	公会宠物极小3C
#define SPR_Gmon_ss_03d			110621		//	公会宠物极小3D
#define SPR_Gmon_ss_03e			110622		//	公会宠物极小3E
#define SPR_Gmon_ss_03f			110623		//	公会宠物极小3F

#define SPR_Gmon_ss_04a			110624		//	公会宠物极小4A
#define SPR_Gmon_ss_04b			110625		//	公会宠物极小4B
#define SPR_Gmon_ss_04c			110626		//	公会宠物极小4C
#define SPR_Gmon_ss_04d			110627		//	公会宠物极小4D
#define SPR_Gmon_ss_04e			110628		//	公会宠物极小4E
#define SPR_Gmon_ss_04f			110629		//	公会宠物极小4F

#define SPR_Gmon_ss_05a			110630		//	公会宠物极小5A
#define SPR_Gmon_ss_05b			110631		//	公会宠物极小5B
#define SPR_Gmon_ss_05c			110632		//	公会宠物极小5C
#define SPR_Gmon_ss_05d			110633		//	公会宠物极小5D
#define SPR_Gmon_ss_05e			110634		//	公会宠物极小5E
#define SPR_Gmon_ss_05f			110635		//	公会宠物极小5F

//公会宠物小						
#define SPR_Gmon_s_00a			110650		//	公会宠物小0A
#define SPR_Gmon_s_00b			110651		//	公会宠物小0B
#define SPR_Gmon_s_00c			110652		//	公会宠物小0C
#define SPR_Gmon_s_00d			110653		//	公会宠物小0D
#define SPR_Gmon_s_00e			110654		//	公会宠物小0E
#define SPR_Gmon_s_00f			110655		//	公会宠物小0F

#define SPR_Gmon_s_01a			110656		//	公会宠物小1A
#define SPR_Gmon_s_01b			110657		//	公会宠物小1B
#define SPR_Gmon_s_01c			110658		//	公会宠物小1C
#define SPR_Gmon_s_01d			110659		//	公会宠物小1D
#define SPR_Gmon_s_01e			110660		//	公会宠物小1E
#define SPR_Gmon_s_01f			110661		//	公会宠物小1F

#define SPR_Gmon_s_02a			110662		//	公会宠物小2A
#define SPR_Gmon_s_02b			110663		//	公会宠物小2B
#define SPR_Gmon_s_02c			110664		//	公会宠物小2C
#define SPR_Gmon_s_02d			110665		//	公会宠物小2D
#define SPR_Gmon_s_02e			110666		//	公会宠物小2E
#define SPR_Gmon_s_02f			110667		//	公会宠物小2F

#define SPR_Gmon_s_03a			110668		//	公会宠物小3A
#define SPR_Gmon_s_03b			110669		//	公会宠物小3B
#define SPR_Gmon_s_03c			110670		//	公会宠物小3C
#define SPR_Gmon_s_03d			110671		//	公会宠物小3D
#define SPR_Gmon_s_03e			110672		//	公会宠物小3E
#define SPR_Gmon_s_03f			110673		//	公会宠物小3F

#define SPR_Gmon_s_04a			110674		//	公会宠物小4A	
#define SPR_Gmon_s_04b			110675		//	公会宠物小4B	
#define SPR_Gmon_s_04c			110676		//	公会宠物小4C	
#define SPR_Gmon_s_04d			110677		//	公会宠物小4D	
#define SPR_Gmon_s_04e			110678		//	公会宠物小4E	
#define SPR_Gmon_s_04f			110679		//	公会宠物小4F	

#define SPR_Gmon_s_05a			110680		//	公会宠物小5A	
#define SPR_Gmon_s_05b			110681		//	公会宠物小5B	
#define SPR_Gmon_s_05c			110682		//	公会宠物小5C	
#define SPR_Gmon_s_05d			110683		//	公会宠物小5D	
#define SPR_Gmon_s_05e			110684		//	公会宠物小5E	
#define SPR_Gmon_s_05f			110685		//	公会宠物小5F	

//公会宠物中							
#define SPR_Gmon_m_00a			110700		//	公会宠物中0a	鱼
#define SPR_Gmon_m_00b			110701		//	公会宠物中0b	
#define SPR_Gmon_m_00c			110702		//	公会宠物中0c	
#define SPR_Gmon_m_00d			110703		//	公会宠物中0d	

#define SPR_Gmon_m_01a			110704		//	公会宠物中1a	龟
#define SPR_Gmon_m_01b			110705		//	公会宠物中1b	
#define SPR_Gmon_m_01c			110706		//	公会宠物中1c	
#define SPR_Gmon_m_01d			110707		//	公会宠物中1d	

#define SPR_Gmon_m_02a			110708		//	公会宠物中2a	獣
#define SPR_Gmon_m_02b			110709		//	公会宠物中2b	
#define SPR_Gmon_m_02c			110710		//	公会宠物中2c	
#define SPR_Gmon_m_02d			110711		//	公会宠物中2d	

#define SPR_Gmon_m_03a			110712		//	公会宠物中3a	鸟
#define SPR_Gmon_m_03b			110713		//	公会宠物中3b	
#define SPR_Gmon_m_03c			110714		//	公会宠物中3c	
#define SPR_Gmon_m_03d			110715		//	公会宠物中3d	

//公会宠物大							
#define SPR_Gmon_l_00a			110750		//	公会宠物大0a	鱼（リザーブ）
#define SPR_Gmon_l_00b			110751		//	公会宠物大0b
#define SPR_Gmon_l_00c			110752		//	公会宠物大0c
#define SPR_Gmon_l_00d			110753		//	公会宠物大0d
#define SPR_Gmon_l_01a			110754		//	公会宠物大1a	龟
#define SPR_Gmon_l_01b			110755		//	公会宠物大1b
#define SPR_Gmon_l_01c			110756		//	公会宠物大1c
#define SPR_Gmon_l_01d			110757		//	公会宠物大1d
#define SPR_Gmon_l_02a			110758		//	公会宠物大2a	獣	（リザーブ）
#define SPR_Gmon_l_02b			110759		//	公会宠物大2b
#define SPR_Gmon_l_02c			110760		//	公会宠物大2c
#define SPR_Gmon_l_02d			110761		//	公会宠物大2d
#define SPR_Gmon_l_03a			110762		//	公会宠物大3a	鸟
#define SPR_Gmon_l_03b			110763		//	公会宠物大3b
#define SPR_Gmon_l_03c			110764		//	公会宠物大3c
#define SPR_Gmon_l_03d			110765		//	公会宠物大3d

//公会宠物大サイズ、エネミー用							
#define SPR_Gmon_l_enemy01		110766		//	公会宠物大龟（色违いエネミー）
#define SPR_Gmon_l_enemy02		110767		//	公会宠物大獣（色违いエネミー）
#define SPR_Gmon_l_enemy03		110768		//	公会宠物大鱼（色违いエネミー）	
#define SPR_Gmon_l_enemy04		110769		//	公会宠物大鸟（色违いエネミー）		

//	マップアニメ用　アニメID	map
#define SPR_map0000			113000		//	map0000(ヌルアニメ当たり判定用)
#define SPR_map0001			113001		//	map0001
#define SPR_map0002			113002		//	map0002
#define SPR_map0003			113003		//	map0003
#define SPR_map0004			113004		//	map0004
#define SPR_map0005			113005		//	map0005
#define SPR_map0006			113006		//	map0006
#define SPR_map0007			113007		//	map0007
#define SPR_map0008			113008		//	map0008
#define SPR_map0009			113009		//	map0009
#define SPR_map0010			113010		//	map0010
#define SPR_map0011			113011		//	map0011
#define SPR_map0012			113012		//	map0012
#define SPR_map0013			113013		//	map0013
#define SPR_map0014			113014		//	map0014
#define SPR_map0015			113015		//	map0015
#define SPR_map0016			113016		//	map0016
#define SPR_map0017			113017		//	map0017
#define SPR_map0018			113018		//	map0018
#define SPR_map0019			113019		//	map0019
#define SPR_map0020			113020		//	map0020
#define SPR_map0021			113021		//	map0021
#define SPR_map0022			113022		//	map0022
#define SPR_map0023			113023		//	map0023
#define SPR_map0024			113024		//	map0024
#define SPR_map0025			113025		//	map0025
#define SPR_map0026			113026		//	map0026
#define SPR_map0027			113027		//	map0027
#define SPR_map0028			113028		//	map0028
#define SPR_map0029			113029		//	map0029
#define SPR_map0030			113030		//	map0030
#define SPR_map0031			113031		//	map0031
#define SPR_map0032			113032		//	map0032
#define SPR_map0033			113033		//	map0033
#define SPR_map0034			113034		//	map0034
#define SPR_map0035			113035		//	map0035
#define SPR_map0036			113036		//	map0036
#define SPR_map0037			113037		//	map0037
#define SPR_map0038			113038		//	map0038
#define SPR_map0039			113039		//	map0039
#define SPR_map0040			113040		//	map0040
#define SPR_map0041			113041		//	map0041
#define SPR_map0042			113042		//	map0042
#define SPR_map0043			113043		//	map0043
#define SPR_map0044			113044		//	map0044
#define SPR_map0045			113045		//	map0045
#define SPR_map0046			113046		//	map0046
#define SPR_map0047			113047		//	map0047
#define SPR_map0048			113048		//	map0048
#define SPR_map0049			113049		//	map0049
#define SPR_map0050			113050		//	map0050
#define SPR_map0051			113051		//	map0051
#define SPR_map0052			113052		//	map0052
#define SPR_map0053			113053		//	map0053
#define SPR_map0054			113054		//	map0054
#define SPR_map0055			113055		//	map0055
#define SPR_map0056			113056		//	map0056
#define SPR_map0057			113057		//	map0057
#define SPR_map0058			113058		//	map0058
#define SPR_map0059			113059		//	map0059
#define SPR_map0060			113060		//	map0060
#define SPR_map0061			113061		//	map0061
#define SPR_map0062			113062		//	map0062
#define SPR_map0063			113063		//	map0063
#define SPR_map0064			113064		//	map0064
#define SPR_map0065			113065		//	map0065
#define SPR_map0066			113066		//	map0066
#define SPR_map0067			113067		//	map0067
#define SPR_map0068			113068		//	map0068
#define SPR_map0069			113069		//	map0069
#define SPR_map0070			113070		//	map0070
#define SPR_map0071			113071		//	map0071
#define SPR_map0072			113072		//	map0072
#define SPR_map0073			113073		//	map0073
#define SPR_map0074			113074		//	map0074
#define SPR_map0075			113075		//	map0075
#define SPR_map0076			113076		//	map0076
#define SPR_map0077			113077		//	map0077
#define SPR_map0078			113078		//	map0078
#define SPR_map0079			113079		//	map0079
#define SPR_map0080			113080		//	map0080
#define SPR_map0081			113081		//	map0081
#define SPR_map0082			113082		//	map0082
#define SPR_map0083			113083		//	map0083
#define SPR_map0084			113084		//	map0084
#define SPR_map0085			113085		//	map0085
#define SPR_map0086			113086		//	map0086
#define SPR_map0087			113087		//	map0087
#define SPR_map0088			113088		//	map0088
#define SPR_map0089			113089		//	map0089
#define SPR_map0090			113090		//	map0090
#define SPR_map0091			113091		//	map0091
#define SPR_map0092			113092		//	map0092
#define SPR_map0093			113093		//	map0093
#define SPR_map0094			113094		//	map0094
#define SPR_map0095			113095		//	map0095
#define SPR_map0096			113096		//	map0096
#define SPR_map0097			113097		//	map0097
#define SPR_map0098			113098		//	map0098
#define SPR_map0099			113099		//	map0099
#define SPR_map0100			113100		//	map0100
#define SPR_map0101			113101		//	map0101
#define SPR_map0102			113102		//	map0102
#define SPR_map0103			113103		//	map0103
#define SPR_map0104			113104		//	map0104
#define SPR_map0105			113105		//	map0105
#define SPR_map0106			113106		//	map0106
#define SPR_map0107			113107		//	map0107
#define SPR_map0108			113108		//	map0108
#define SPR_map0109			113109		//	map0109
#define SPR_map0110			113110		//	map0110
#define SPR_map0111			113111		//	map0111
#define SPR_map0112			113112		//	map0112
#define SPR_map0113			113113		//	map0113
#define SPR_map0114			113114		//	map0114
#define SPR_map0115			113115		//	map0115
#define SPR_map0116			113116		//	map0116
#define SPR_map0117			113117		//	map0117
#define SPR_map0118			113118		//	map0118
#define SPR_map0119			113119		//	map0119
#define SPR_map0120			113120		//	map0120
#define SPR_map0121			113121		//	map0121
#define SPR_map0122			113122		//	map0122
#define SPR_map0123			113123		//	map0123
#define SPR_map0124			113124		//	map0124
#define SPR_map0125			113125		//	map0125
#define SPR_map0126			113126		//	map0126
#define SPR_map0127			113127		//	map0127
#define SPR_map0128			113128		//	map0128
#define SPR_map0129			113129		//	map0129
#define SPR_map0130			113130		//	map0130
#define SPR_map0131			113131		//	map0131
#define SPR_map0132			113132		//	map0132
#define SPR_map0133			113133		//	map0133
#define SPR_map0134			113134		//	map0134
#define SPR_map0135			113135		//	map0135
#define SPR_map0136			113136		//	map0136
#define SPR_map0137			113137		//	map0137
#define SPR_map0138			113138		//	map0138
#define SPR_map0139			113139		//	map0139
#define SPR_map0140			113140		//	map0140
#define SPR_map0141			113141		//	map0141
#define SPR_map0142			113142		//	map0142
#define SPR_map0143			113143		//	map0143
#define SPR_map0144			113144		//	map0144
#define SPR_map0145			113145		//	map0145
#define SPR_map0146			113146		//	map0146
#define SPR_map0147			113147		//	map0147
#define SPR_map0148			113148		//	map0148
#define SPR_map0149			113149		//	map0149
#define SPR_map0150			113150		//	map0150
#define SPR_map0151			113151		//	map0151
#define SPR_map0152			113152		//	map0152
#define SPR_map0153			113153		//	map0153
#define SPR_map0154			113154		//	map0154
#define SPR_map0155			113155		//	map0155
#define SPR_map0156			113156		//	map0156
#define SPR_map0157			113157		//	map0157
#define SPR_map0158			113158		//	map0158
#define SPR_map0159			113159		//	map0159
#define SPR_map0160			113160		//	map0160
#define SPR_map0161			113161		//	map0161
#define SPR_map0162			113162		//	map0162
#define SPR_map0163			113163		//	map0163
#define SPR_map0164			113164		//	map0164
#define SPR_map0165			113165		//	map0165
#define SPR_map0166			113166		//	map0166
#define SPR_map0167			113167		//	map0167
#define SPR_map0168			113168		//	map0168
#define SPR_map0169			113169		//	map0169
#define SPR_map0170			113170		//	map0170
#define SPR_map0171			113171		//	map0171
#define SPR_map0172			113172		//	map0172
#define SPR_map0173			113173		//	map0173
#define SPR_map0174			113174		//	map0174
#define SPR_map0175			113175		//	map0175
#define SPR_map0176			113176		//	map0176
#define SPR_map0177			113177		//	map0177
#define SPR_map0178			113178		//	map0178
#define SPR_map0179			113179		//	map0179
#define SPR_map0180			113180		//	map0180
#define SPR_map0181			113181		//	map0181
#define SPR_map0182			113182		//	map0182
#define SPR_map0183			113183		//	map0183
#define SPR_map0184			113184		//	map0184
#define SPR_map0185			113185		//	map0185
#define SPR_map0186			113186		//	map0186
#define SPR_map0187			113187		//	map0187
#define SPR_map0188			113188		//	map0188
#define SPR_map0189			113189		//	map0189
#define SPR_map0190			113190		//	map0190
#define SPR_map0191			113191		//	map0191
#define SPR_map0192			113192		//	map0192
#define SPR_map0193			113193		//	map0193
#define SPR_map0194			113194		//	map0194
#define SPR_map0195			113195		//	map0195
#define SPR_map0196			113196		//	map0196
#define SPR_map0197			113197		//	map0197
#define SPR_map0198			113198		//	map0198
#define SPR_map0199			113199		//	map0199
#define SPR_map0200			113200		//	map0200
#define SPR_map0201			113201		//	map0201
#define SPR_map0202			113202		//	map0202
#define SPR_map0203			113203		//	map0203
#define SPR_map0204			113204		//	map0204
#define SPR_map0205			113205		//	map0205
#define SPR_map0206			113206		//	map0206
#define SPR_map0207			113207		//	map0207
#define SPR_map0208			113208		//	map0208
#define SPR_map0209			113209		//	map0209
#define SPR_map0210			113210		//	map0210
#define SPR_map0211			113211		//	map0211
#define SPR_map0212			113212		//	map0212
#define SPR_map0213			113213		//	map0213
#define SPR_map0214			113214		//	map0214
#define SPR_map0215			113215		//	map0215
#define SPR_map0216			113216		//	map0216
#define SPR_map0217			113217		//	map0217
#define SPR_map0218			113218		//	map0218
#define SPR_map0219			113219		//	map0219
#define SPR_map0220			113220		//	map0220
#define SPR_map0221			113221		//	map0221
#define SPR_map0222			113222		//	map0222
#define SPR_map0223			113223		//	map0223
#define SPR_map0224			113224		//	map0224
#define SPR_map0225			113225		//	map0225
#define SPR_map0226			113226		//	map0226
#define SPR_map0227			113227		//	map0227
#define SPR_map0228			113228		//	map0228
#define SPR_map0229			113229		//	map0229
#define SPR_map0230			113230		//	map0230
#define SPR_map0231			113231		//	map0231
#define SPR_map0232			113232		//	map0232
#define SPR_map0233			113233		//	map0233
#define SPR_map0234			113234		//	map0234
#define SPR_map0235			113235		//	map0235
#define SPR_map0236			113236		//	map0236
#define SPR_map0237			113237		//	map0237
#define SPR_map0238			113238		//	map0238
#define SPR_map0239			113239		//	map0239
#define SPR_map0240			113240		//	map0240
#define SPR_map0241			113241		//	map0241
#define SPR_map0242			113242		//	map0242
#define SPR_map0243			113243		//	map0243
#define SPR_map0244			113244		//	map0244
#define SPR_map0245			113245		//	map0245
#define SPR_map0246			113246		//	map0246
#define SPR_map0247			113247		//	map0247
#define SPR_map0248			113248		//	map0248
#define SPR_map0249			113249		//	map0249
#define SPR_map0250			113250		//	map0250
#define SPR_map0251			113251		//	map0251
#define SPR_map0252			113252		//	map0252
#define SPR_map0253			113253		//	map0253
#define SPR_map0254			113254		//	map0254
#define SPR_map0255			113255		//	map0255
#define SPR_map0256			113256		//	map0256
#define SPR_map0257			113257		//	map0257
#define SPR_map0258			113258		//	map0258
#define SPR_map0259			113259		//	map0259
#define SPR_map0260			113260		//	map0260
#define SPR_map0261			113261		//	map0261
#define SPR_map0262			113262		//	map0262
#define SPR_map0263			113263		//	map0263
#define SPR_map0264			113264		//	map0264
#define SPR_map0265			113265		//	map0265
#define SPR_map0266			113266		//	map0266
#define SPR_map0267			113267		//	map0267
#define SPR_map0268			113268		//	map0268
#define SPR_map0269			113269		//	map0269
#define SPR_map0270			113270		//	map0270
#define SPR_map0271			113271		//	map0271
#define SPR_map0272			113272		//	map0272
#define SPR_map0273			113273		//	map0273
#define SPR_map0274			113274		//	map0274
#define SPR_map0275			113275		//	map0275
#define SPR_map0276			113276		//	map0276
#define SPR_map0277			113277		//	map0277
#define SPR_map0278			113278		//	map0278
#define SPR_map0279			113279		//	map0279
#define SPR_map0280			113280		//	map0280
#define SPR_map0281			113281		//	map0281
#define SPR_map0282			113282		//	map0282
#define SPR_map0283			113283		//	map0283
#define SPR_map0284			113284		//	map0284
#define SPR_map0285			113285		//	map0285
#define SPR_map0286			113286		//	map0286
#define SPR_map0287			113287		//	map0287
#define SPR_map0288			113288		//	map0288
#define SPR_map0289			113289		//	map0289
#define SPR_map0290			113290		//	map0290
#define SPR_map0291			113291		//	map0291
#define SPR_map0292			113292		//	map0292
#define SPR_map0293			113293		//	map0293
#define SPR_map0294			113294		//	map0294
#define SPR_map0295			113295		//	map0295
#define SPR_map0296			113296		//	map0296
#define SPR_map0297			113297		//	map0297
#define SPR_map0298			113298		//	map0298
#define SPR_map0299			113299		//	map0299
#define SPR_map0300			113300		//	map0300
#define SPR_map0301			113301		//	map0301
#define SPR_map0302			113302		//	map0302
#define SPR_map0303			113303		//	map0303
#define SPR_map0304			113304		//	map0304
#define SPR_map0305			113305		//	map0305
#define SPR_map0306			113306		//	map0306
#define SPR_map0307			113307		//	map0307
#define SPR_map0308			113308		//	map0308
#define SPR_map0309			113309		//	map0309
#define SPR_map0310			113310		//	map0310
#define SPR_map0311			113311		//	map0311
#define SPR_map0312			113312		//	map0312
#define SPR_map0313			113313		//	map0313
#define SPR_map0314			113314		//	map0314
#define SPR_map0315			113315		//	map0315
#define SPR_map0316			113316		//	map0316
#define SPR_map0317			113317		//	map0317
#define SPR_map0318			113318		//	map0318
#define SPR_map0319			113319		//	map0319
#define SPR_map0320			113320		//	map0320
#define SPR_map0321			113321		//	map0321
#define SPR_map0322			113322		//	map0322
#define SPR_map0323			113323		//	map0323
#define SPR_map0324			113324		//	map0324
#define SPR_map0325			113325		//	map0325
#define SPR_map0326			113326		//	map0326
#define SPR_map0327			113327		//	map0327
#define SPR_map0328			113328		//	map0328
#define SPR_map0329			113329		//	map0329
#define SPR_map0330			113330		//	map0330
#define SPR_map0331			113331		//	map0331
#define SPR_map0332			113332		//	map0332
#define SPR_map0333			113333		//	map0333
#define SPR_map0334			113334		//	map0334
#define SPR_map0335			113335		//	map0335
#define SPR_map0336			113336		//	map0336
#define SPR_map0337			113337		//	map0337
#define SPR_map0338			113338		//	map0338
#define SPR_map0339			113339		//	map0339
#define SPR_map0340			113340		//	map0340
#define SPR_map0341			113341		//	map0341
#define SPR_map0342			113342		//	map0342
#define SPR_map0343			113343		//	map0343
#define SPR_map0344			113344		//	map0344
#define SPR_map0345			113345		//	map0345
#define SPR_map0346			113346		//	map0346
#define SPR_map0347			113347		//	map0347
#define SPR_map0348			113348		//	map0348
#define SPR_map0349			113349		//	map0349
#define SPR_map0350			113350		//	map0350
#define SPR_map0351			113351		//	map0351
#define SPR_map0352			113352		//	map0352
#define SPR_map0353			113353		//	map0353
#define SPR_map0354			113354		//	map0354
#define SPR_map0355			113355		//	map0355
#define SPR_map0356			113356		//	map0356
#define SPR_map0357			113357		//	map0357
#define SPR_map0358			113358		//	map0358
#define SPR_map0359			113359		//	map0359
#define SPR_map0360			113360		//	map0360
#define SPR_map0361			113361		//	map0361
#define SPR_map0362			113362		//	map0362
#define SPR_map0363			113363		//	map0363
#define SPR_map0364			113364		//	map0364
#define SPR_map0365			113365		//	map0365
#define SPR_map0366			113366		//	map0366
#define SPR_map0367			113367		//	map0367
#define SPR_map0368			113368		//	map0368
#define SPR_map0369			113369		//	map0369
#define SPR_map0370			113370		//	map0370
#define SPR_map0371			113371		//	map0371
#define SPR_map0372			113372		//	map0372
#define SPR_map0373			113373		//	map0373
#define SPR_map0374			113374		//	map0374
#define SPR_map0375			113375		//	map0375
#define SPR_map0376			113376		//	map0376
#define SPR_map0377			113377		//	map0377
#define SPR_map0378			113378		//	map0378
#define SPR_map0379			113379		//	map0379
#define SPR_map0380			113380		//	map0380
#define SPR_map0381			113381		//	map0381
#define SPR_map0382			113382		//	map0382
#define SPR_map0383			113383		//	map0383
#define SPR_map0384			113384		//	map0384
#define SPR_map0385			113385		//	map0385
#define SPR_map0386			113386		//	map0386
#define SPR_map0387			113387		//	map0387
#define SPR_map0388			113388		//	map0388
#define SPR_map0389			113389		//	map0389
#define SPR_map0390			113390		//	map0390
#define SPR_map0391			113391		//	map0391
#define SPR_map0392			113392		//	map0392
#define SPR_map0393			113393		//	map0393
#define SPR_map0394			113394		//	map0394
#define SPR_map0395			113395		//	map0395
#define SPR_map0396			113396		//	map0396
#define SPR_map0397			113397		//	map0397
#define SPR_map0398			113398		//	map0398
#define SPR_map0399			113399		//	map0399
#define SPR_map0400			113400		//	map0400
#define SPR_map0401			113401		//	map0401
#define SPR_map0402			113402		//	map0402
#define SPR_map0403			113403		//	map0403
#define SPR_map0404			113404		//	map0404
#define SPR_map0405			113405		//	map0405
#define SPR_map0406			113406		//	map0406
#define SPR_map0407			113407		//	map0407
#define SPR_map0408			113408		//	map0408
#define SPR_map0409			113409		//	map0409
#define SPR_map0410			113410		//	map0410
#define SPR_map0411			113411		//	map0411
#define SPR_map0412			113412		//	map0412
#define SPR_map0413			113413		//	map0413
#define SPR_map0414			113414		//	map0414
#define SPR_map0415			113415		//	map0415
#define SPR_map0416			113416		//	map0416
#define SPR_map0417			113417		//	map0417
#define SPR_map0418			113418		//	map0418
#define SPR_map0419			113419		//	map0419
#define SPR_map0420			113420		//	map0420
#define SPR_map0421			113421		//	map0421
#define SPR_map0422			113422		//	map0422
#define SPR_map0423			113423		//	map0423
#define SPR_map0424			113424		//	map0424
#define SPR_map0425			113425		//	map0425
#define SPR_map0426			113426		//	map0426
#define SPR_map0427			113427		//	map0427
#define SPR_map0428			113428		//	map0428
#define SPR_map0429			113429		//	map0429
#define SPR_map0430			113430		//	map0430
#define SPR_map0431			113431		//	map0431
#define SPR_map0432			113432		//	map0432
#define SPR_map0433			113433		//	map0433
#define SPR_map0434			113434		//	map0434
#define SPR_map0435			113435		//	map0435
#define SPR_map0436			113436		//	map0436
#define SPR_map0437			113437		//	map0437
#define SPR_map0438			113438		//	map0438
#define SPR_map0439			113439		//	map0439
#define SPR_map0440			113440		//	map0440
#define SPR_map0441			113441		//	map0441
#define SPR_map0442			113442		//	map0442
#define SPR_map0443			113443		//	map0443
#define SPR_map0444			113444		//	map0444
#define SPR_map0445			113445		//	map0445
#define SPR_map0446			113446		//	map0446
#define SPR_map0447			113447		//	map0447
#define SPR_map0448			113448		//	map0448
#define SPR_map0449			113449		//	map0449
#define SPR_map0450			113450		//	map0450
#define SPR_map0451			113451		//	map0451
#define SPR_map0452			113452		//	map0452
#define SPR_map0453			113453		//	map0453
#define SPR_map0454			113454		//	map0454
#define SPR_map0455			113455		//	map0455
#define SPR_map0456			113456		//	map0456
#define SPR_map0457			113457		//	map0457
#define SPR_map0458			113458		//	map0458
#define SPR_map0459			113459		//	map0459
#define SPR_map0460			113460		//	map0460
#define SPR_map0461			113461		//	map0461
#define SPR_map0462			113462		//	map0462
#define SPR_map0463			113463		//	map0463
#define SPR_map0464			113464		//	map0464
#define SPR_map0465			113465		//	map0465
#define SPR_map0466			113466		//	map0466
#define SPR_map0467			113467		//	map0467
#define SPR_map0468			113468		//	map0468
#define SPR_map0469			113469		//	map0469
#define SPR_map0470			113470		//	map0470
#define SPR_map0471			113471		//	map0471
#define SPR_map0472			113472		//	map0472
#define SPR_map0473			113473		//	map0473
#define SPR_map0474			113474		//	map0474
#define SPR_map0475			113475		//	map0475
#define SPR_map0476			113476		//	map0476
#define SPR_map0477			113477		//	map0477
#define SPR_map0478			113478		//	map0478
#define SPR_map0479			113479		//	map0479
#define SPR_map0480			113480		//	map0480
#define SPR_map0481			113481		//	map0481
#define SPR_map0482			113482		//	map0482
#define SPR_map0483			113483		//	map0483
#define SPR_map0484			113484		//	map0484
#define SPR_map0485			113485		//	map0485
#define SPR_map0486			113486		//	map0486
#define SPR_map0487			113487		//	map0487
#define SPR_map0488			113488		//	map0488
#define SPR_map0489			113489		//	map0489
#define SPR_map0490			113490		//	map0490
#define SPR_map0491			113491		//	map0491
#define SPR_map0492			113492		//	map0492
#define SPR_map0493			113493		//	map0493
#define SPR_map0494			113494		//	map0494
#define SPR_map0495			113495		//	map0495
#define SPR_map0496			113496		//	map0496
#define SPR_map0497			113497		//	map0497
#define SPR_map0498			113498		//	map0498
#define SPR_map0499			113499		//	map0499
#define SPR_map0500			113500		//	map0500
#define SPR_map0501			113501		//	map0501
#define SPR_map0502			113502		//	map0502
#define SPR_map0503			113503		//	map0503
#define SPR_map0504			113504		//	map0504
#define SPR_map0505			113505		//	map0505
#define SPR_map0506			113506		//	map0506
#define SPR_map0507			113507		//	map0507
#define SPR_map0508			113508		//	map0508
#define SPR_map0509			113509		//	map0509
#define SPR_map0510			113510		//	map0510
#define SPR_map0511			113511		//	map0511
#define SPR_map0512			113512		//	map0512
#define SPR_map0513			113513		//	map0513
#define SPR_map0514			113514		//	map0514
#define SPR_map0515			113515		//	map0515
#define SPR_map0516			113516		//	map0516
#define SPR_map0517			113517		//	map0517
#define SPR_map0518			113518		//	map0518
#define SPR_map0519			113519		//	map0519
#define SPR_map0520			113520		//	map0520
#define SPR_map0521			113521		//	map0521
#define SPR_map0522			113522		//	map0522
#define SPR_map0523			113523		//	map0523
#define SPR_map0524			113524		//	map0524
#define SPR_map0525			113525		//	map0525
#define SPR_map0526			113526		//	map0526
#define SPR_map0527			113527		//	map0527
#define SPR_map0528			113528		//	map0528
#define SPR_map0529			113529		//	map0529
#define SPR_map0530			113530		//	map0530
#define SPR_map0531			113531		//	map0531
#define SPR_map0532			113532		//	map0532
#define SPR_map0533			113533		//	map0533
#define SPR_map0534			113534		//	map0534
#define SPR_map0535			113535		//	map0535
#define SPR_map0536			113536		//	map0536
#define SPR_map0537			113537		//	map0537
#define SPR_map0538			113538		//	map0538
#define SPR_map0539			113539		//	map0539
#define SPR_map0540			113540		//	map0540
#define SPR_map0541			113541		//	map0541
#define SPR_map0542			113542		//	map0542
#define SPR_map0543			113543		//	map0543
#define SPR_map0544			113544		//	map0544
#define SPR_map0545			113545		//	map0545
#define SPR_map0546			113546		//	map0546
#define SPR_map0547			113547		//	map0547
#define SPR_map0548			113548		//	map0548
#define SPR_map0549			113549		//	map0549
#define SPR_map0550			113550		//	map0550
#define SPR_map0551			113551		//	map0551
#define SPR_map0552			113552		//	map0552
#define SPR_map0553			113553		//	map0553
#define SPR_map0554			113554		//	map0554
#define SPR_map0555			113555		//	map0555
#define SPR_map0556			113556		//	map0556
#define SPR_map0557			113557		//	map0557
#define SPR_map0558			113558		//	map0558
#define SPR_map0559			113559		//	map0559
#define SPR_map0560			113560		//	map0560
#define SPR_map0561			113561		//	map0561
#define SPR_map0562			113562		//	map0562
#define SPR_map0563			113563		//	map0563
#define SPR_map0564			113564		//	map0564
#define SPR_map0565			113565		//	map0565
#define SPR_map0566			113566		//	map0566
#define SPR_map0567			113567		//	map0567
#define SPR_map0568			113568		//	map0568
#define SPR_map0569			113569		//	map0569
#define SPR_map0570			113570		//	map0570
#define SPR_map0571			113571		//	map0571
#define SPR_map0572			113572		//	map0572
#define SPR_map0573			113573		//	map0573
#define SPR_map0574			113574		//	map0574
#define SPR_map0575			113575		//	map0575
#define SPR_map0576			113576		//	map0576
#define SPR_map0577			113577		//	map0577
#define SPR_map0578			113578		//	map0578
#define SPR_map0579			113579		//	map0579
#define SPR_map0580			113580		//	map0580
#define SPR_map0581			113581		//	map0581
#define SPR_map0582			113582		//	map0582
#define SPR_map0583			113583		//	map0583
#define SPR_map0584			113584		//	map0584
#define SPR_map0585			113585		//	map0585
#define SPR_map0586			113586		//	map0586
#define SPR_map0587			113587		//	map0587
#define SPR_map0588			113588		//	map0588
#define SPR_map0589			113589		//	map0589
#define SPR_map0590			113590		//	map0590
#define SPR_map0591			113591		//	map0591
#define SPR_map0592			113592		//	map0592
#define SPR_map0593			113593		//	map0593
#define SPR_map0594			113594		//	map0594
#define SPR_map0595			113595		//	map0595
#define SPR_map0596			113596		//	map0596
#define SPR_map0597			113597		//	map0597
#define SPR_map0598			113598		//	map0598
#define SPR_map0599			113599		//	map0599
#define SPR_map0600			113600		//	map0600	NPCワープ用炎
#define SPR_map0601			113601		//	map0601
#define SPR_map0602			113602		//	map0602
#define SPR_map0603			113603		//	map0603
#define SPR_map0604			113604		//	map0604
#define SPR_map0605			113605		//	map0605
#define SPR_map0606			113606		//	map0606
#define SPR_map0607			113607		//	map0607
#define SPR_map0608			113608		//	map0608
#define SPR_map0609			113609		//	map0609
#define SPR_map0610			113610		//	map0610
#define SPR_map0611			113611		//	map0611
#define SPR_map0612			113612		//	map0612
#define SPR_map0613			113613		//	map0613
#define SPR_map0614			113614		//	map0614
#define SPR_map0615			113615		//	map0615
#define SPR_map0616			113616		//	map0616
#define SPR_map0617			113617		//	map0617
#define SPR_map0618			113618		//	map0618
#define SPR_map0619			113619		//	map0619
#define SPR_map0620			113620		//	map0620
#define SPR_map0621			113621		//	map0621
#define SPR_map0622			113622		//	map0622
#define SPR_map0623			113623		//	map0623
#define SPR_map0624			113624		//	map0624
#define SPR_map0625			113625		//	map0625
#define SPR_map0626			113626		//	map0626
#define SPR_map0627			113627		//	map0627
#define SPR_map0628			113628		//	map0628
#define SPR_map0629			113629		//	map0629
#define SPR_map0630			113630		//	map0630
#define SPR_map0631			113631		//	map0631
#define SPR_map0632			113632		//	map0632
#define SPR_map0633			113633		//	map0633
#define SPR_map0634			113634		//	map0634
#define SPR_map0635			113635		//	map0635
#define SPR_map0636			113636		//	map0636
#define SPR_map0637			113637		//	map0637
#define SPR_map0638			113638		//	map0638
#define SPR_map0639			113639		//	map0639
#define SPR_map0640			113640		//	map0640
#define SPR_map0641			113641		//	map0641
#define SPR_map0642			113642		//	map0642
#define SPR_map0643			113643		//	map0643
#define SPR_map0644			113644		//	map0644
#define SPR_map0645			113645		//	map0645
#define SPR_map0646			113646		//	map0646
#define SPR_map0647			113647		//	map0647
#define SPR_map0648			113648		//	map0648
#define SPR_map0649			113649		//	map0649
#define SPR_map0650			113650		//	map0650
#define SPR_map0651			113651		//	map0651
#define SPR_map0652			113652		//	map0652
#define SPR_map0653			113653		//	map0653
#define SPR_map0654			113654		//	map0654
#define SPR_map0655			113655		//	map0655
#define SPR_map0656			113656		//	map0656
#define SPR_map0657			113657		//	map0657
#define SPR_map0658			113658		//	map0658
#define SPR_map0659			113659		//	map0659
#define SPR_map0660			113660		//	map0660
#define SPR_map0661			113661		//	map0661
#define SPR_map0662			113662		//	map0662
#define SPR_map0663			113663		//	map0663
#define SPR_map0664			113664		//	map0664
#define SPR_map0665			113665		//	map0665
#define SPR_map0666			113666		//	map0666
#define SPR_map0667			113667		//	map0667
#define SPR_map0668			113668		//	map0668
#define SPR_map0669			113669		//	map0669
#define SPR_map0670			113670		//	map0670
#define SPR_map0671			113671		//	map0671
#define SPR_map0672			113672		//	map0672
#define SPR_map0673			113673		//	map0673
#define SPR_map0674			113674		//	map0674
#define SPR_map0675			113675		//	map0675
#define SPR_map0676			113676		//	map0676
#define SPR_map0677			113677		//	map0677
#define SPR_map0678			113678		//	map0678
#define SPR_map0679			113679		//	map0679
#define SPR_map0680			113680		//	map0680
#define SPR_map0681			113681		//	map0681
#define SPR_map0682			113682		//	map0682
#define SPR_map0683			113683		//	map0683
#define SPR_map0684			113684		//	map0684
#define SPR_map0685			113685		//	map0685
#define SPR_map0686			113686		//	map0686
#define SPR_map0687			113687		//	map0687
#define SPR_map0688			113688		//	map0688(クリスマスカムリ)
#define SPR_map0689			113689		//	map0689
#define SPR_map0690			113690		//	map0690
#define SPR_map0691			113691		//	map0691
#define SPR_map0692			113692		//	map0692
#define SPR_map0693			113693		//	map0693
#define SPR_map0694			113694		//	map0694
#define SPR_map0695			113695		//	map0695
#define SPR_map0696			113696		//	map0696
#define SPR_map0697			113697		//	map0697
#define SPR_map0698			113698		//	map0698
#define SPR_map0699			113699		//	map0699
#define SPR_map0700			113700		//	map0700
#define SPR_map0701			113701		//	map0701
#define SPR_map0702			113702		//	map0702
#define SPR_map0703			113703		//	map0703
#define SPR_map0704			113704		//	map0704
#define SPR_map0705			113705		//	map0705
#define SPR_map0706			113706		//	map0706
#define SPR_map0707			113707		//	map0707
#define SPR_map0708			113708		//	map0708
#define SPR_map0709			113709		//	map0709
#define SPR_map0710			113710		//	map0710
#define SPR_map0711			113711		//	map0711
#define SPR_map0712			113712		//	map0712
#define SPR_map0713			113713		//	map0713
#define SPR_map0714			113714		//	map0714
#define SPR_map0715			113715		//	map0715
#define SPR_map0716			113716		//	map0716
#define SPR_map0717			113717		//	map0717
#define SPR_map0718			113718		//	map0718
#define SPR_map0719			113719		//	map0719
#define SPR_map0720			113720		//	map0720
#define SPR_map0721			113721		//	map0721
#define SPR_map0722			113722		//	map0722
#define SPR_map0723			113723		//	map0723
#define SPR_map0724			113724		//	map0724
#define SPR_map0725			113725		//	map0725
#define SPR_map0726			113726		//	map0726
#define SPR_map0727			113727		//	map0727
#define SPR_map0728			113728		//	map0728
#define SPR_map0729			113729		//	map0729
#define SPR_map0730			113730		//	map0730
#define SPR_map0731			113731		//	map0731
#define SPR_map0732			113732		//	map0732
#define SPR_map0733			113733		//	map0733
#define SPR_map0734			113734		//	map0734
#define SPR_map0735			113735		//	map0735
#define SPR_map0736			113736		//	map0736
#define SPR_map0737			113737		//	map0737
#define SPR_map0738			113738		//	map0738
#define SPR_map0739			113739		//	map0739
#define SPR_map0740			113740		//	map0740
#define SPR_map0741			113741		//	map0741
#define SPR_map0742			113742		//	map0742
#define SPR_map0743			113743		//	map0743
#define SPR_map0744			113744		//	map0744
#define SPR_map0745			113745		//	map0745
#define SPR_map0746			113746		//	map0746
#define SPR_map0747			113747		//	map0747
#define SPR_map0748			113748		//	map0748
#define SPR_map0749			113749		//	map0749
#define SPR_map0750			113750		//	map0750
#define SPR_map0751			113751		//	map0751
#define SPR_map0752			113752		//	map0752
#define SPR_map0753			113753		//	map0753
#define SPR_map0754			113754		//	map0754
#define SPR_map0755			113755		//	map0755
#define SPR_map0756			113756		//	map0756
#define SPR_map0757			113757		//	map0757
#define SPR_map0758			113758		//	map0758
#define SPR_map0759			113759		//	map0759
#define SPR_map0760			113760		//	map0760
#define SPR_map0761			113761		//	map0761
#define SPR_map0762			113762		//	map0762
#define SPR_map0763			113763		//	map0763
#define SPR_map0764			113764		//	map0764
#define SPR_map0765			113765		//	map0765
#define SPR_map0766			113766		//	map0766
#define SPR_map0767			113767		//	map0767
#define SPR_map0768			113768		//	map0768
#define SPR_map0769			113769		//	map0769
#define SPR_map0770			113770		//	map0770
#define SPR_map0771			113771		//	map0771
#define SPR_map0772			113772		//	map0772
#define SPR_map0773			113773		//	map0773
#define SPR_map0774			113774		//	map0774
#define SPR_map0775			113775		//	map0775
#define SPR_map0776			113776		//	map0776
#define SPR_map0777			113777		//	map0777
#define SPR_map0778			113778		//	map0778
#define SPR_map0779			113779		//	map0779
#define SPR_map0780			113780		//	map0780
#define SPR_map0781			113781		//	map0781
#define SPR_map0782			113782		//	map0782
#define SPR_map0783			113783		//	map0783
#define SPR_map0784			113784		//	map0784
#define SPR_map0785			113785		//	map0785
#define SPR_map0786			113786		//	map0786
#define SPR_map0787			113787		//	map0787
#define SPR_map0788			113788		//	map0788
#define SPR_map0789			113789		//	map0789
#define SPR_map0790			113790		//	map0790
#define SPR_map0791			113791		//	map0791
#define SPR_map0792			113792		//	map0792
#define SPR_map0793			113793		//	map0793
#define SPR_map0794			113794		//	map0794
#define SPR_map0795			113795		//	map0795
#define SPR_map0796			113796		//	map0796
#define SPR_map0797			113797		//	map0797
#define SPR_map0798			113798		//	map0798
#define SPR_map0799			113799		//	map0799
#define SPR_map0800			113800		//	map0800
#define SPR_map0801			113801		//	map0801
#define SPR_map0802			113802		//	map0802
#define SPR_map0803			113803		//	map0803
#define SPR_map0804			113804		//	map0804
#define SPR_map0805			113805		//	map0805
#define SPR_map0806			113806		//	map0806
#define SPR_map0807			113807		//	map0807
#define SPR_map0808			113808		//	map0808
#define SPR_map0809			113809		//	map0809
#define SPR_map0810			113810		//	map0810
#define SPR_map0811			113811		//	map0811
#define SPR_map0812			113812		//	map0812
#define SPR_map0813			113813		//	map0813
#define SPR_map0814			113814		//	map0814
#define SPR_map0815			113815		//	map0815
#define SPR_map0816			113816		//	map0816
#define SPR_map0817			113817		//	map0817
#define SPR_map0818			113818		//	map0818
#define SPR_map0819			113819		//	map0819
#define SPR_map0820			113820		//	map0820
#define SPR_map0821			113821		//	map0821
#define SPR_map0822			113822		//	map0822
#define SPR_map0823			113823		//	map0823
#define SPR_map0824			113824		//	map0824
#define SPR_map0825			113825		//	map0825
#define SPR_map0826			113826		//	map0826
#define SPR_map0827			113827		//	map0827
#define SPR_map0828			113828		//	map0828
#define SPR_map0829			113829		//	map0829
#define SPR_map0830			113830		//	map0830
#define SPR_map0831			113831		//	map0831
#define SPR_map0832			113832		//	map0832
#define SPR_map0833			113833		//	map0833
#define SPR_map0834			113834		//	map0834
#define SPR_map0835			113835		//	map0835
#define SPR_map0836			113836		//	map0836
#define SPR_map0837			113837		//	map0837
#define SPR_map0838			113838		//	map0838
#define SPR_map0839			113839		//	map0839
#define SPR_map0840			113840		//	map0840
#define SPR_map0841			113841		//	map0841
#define SPR_map0842			113842		//	map0842
#define SPR_map0843			113843		//	map0843
#define SPR_map0844			113844		//	map0844
#define SPR_map0845			113845		//	map0845
#define SPR_map0846			113846		//	map0846
#define SPR_map0847			113847		//	map0847
#define SPR_map0848			113848		//	map0848
#define SPR_map0849			113849		//	map0849
#define SPR_map0850			113850		//	map0850
#define SPR_map0851			113851		//	map0851
#define SPR_map0852			113852		//	map0852
#define SPR_map0853			113853		//	map0853
#define SPR_map0854			113854		//	map0854
#define SPR_map0855			113855		//	map0855
#define SPR_map0856			113856		//	map0856
#define SPR_map0857			113857		//	map0857
#define SPR_map0858			113858		//	map0858
#define SPR_map0859			113859		//	map0859
#define SPR_map0860			113860		//	map0860
#define SPR_map0861			113861		//	map0861
#define SPR_map0862			113862		//	map0862
#define SPR_map0863			113863		//	map0863
#define SPR_map0864			113864		//	map0864
#define SPR_map0865			113865		//	map0865
#define SPR_map0866			113866		//	map0866
#define SPR_map0867			113867		//	map0867
#define SPR_map0868			113868		//	map0868
#define SPR_map0869			113869		//	map0869
#define SPR_map0870			113870		//	map0870
#define SPR_map0871			113871		//	map0871
#define SPR_map0872			113872		//	map0872
#define SPR_map0873			113873		//	map0873
#define SPR_map0874			113874		//	map0874
#define SPR_map0875			113875		//	map0875
#define SPR_map0876			113876		//	map0876
#define SPR_map0877			113877		//	map0877
#define SPR_map0878			113878		//	map0878
#define SPR_map0879			113879		//	map0879
#define SPR_map0880			113880		//	map0880
#define SPR_map0881			113881		//	map0881
#define SPR_map0882			113882		//	map0882
#define SPR_map0883			113883		//	map0883
#define SPR_map0884			113884		//	map0884
#define SPR_map0885			113885		//	map0885
#define SPR_map0886			113886		//	map0886
#define SPR_map0887			113887		//	map0887
#define SPR_map0888			113888		//	map0888
#define SPR_map0889			113889		//	map0889
#define SPR_map0890			113890		//	map0890
#define SPR_map0891			113891		//	map0891
#define SPR_map0892			113892		//	map0892
#define SPR_map0893			113893		//	map0893
#define SPR_map0894			113894		//	map0894
#define SPR_map0895			113895		//	map0895
#define SPR_map0896			113896		//	map0896
#define SPR_map0897			113897		//	map0897
#define SPR_map0898			113898		//	map0898
#define SPR_map0899			113899		//	map0899
#define SPR_map0900			113900		//	map0900
#define SPR_map0901			113901		//	map0901
#define SPR_map0902			113902		//	map0902
#define SPR_map0903			113903		//	map0903
#define SPR_map0904			113904		//	map0904
#define SPR_map0905			113905		//	map0905
#define SPR_map0906			113906		//	map0906
#define SPR_map0907			113907		//	map0907
#define SPR_map0908			113908		//	map0908
#define SPR_map0909			113909		//	map0909
#define SPR_map0910			113910		//	map0910
#define SPR_map0911			113911		//	map0911
#define SPR_map0912			113912		//	map0912
#define SPR_map0913			113913		//	map0913
#define SPR_map0914			113914		//	map0914
#define SPR_map0915			113915		//	map0915
#define SPR_map0916			113916		//	map0916
#define SPR_map0917			113917		//	map0917
#define SPR_map0918			113918		//	map0918
#define SPR_map0919			113919		//	map0919
#define SPR_map0920			113920		//	map0920
#define SPR_map0921			113921		//	map0921
#define SPR_map0922			113922		//	map0922
#define SPR_map0923			113923		//	map0923
#define SPR_map0924			113924		//	map0924
#define SPR_map0925			113925		//	map0925
#define SPR_map0926			113926		//	map0926
#define SPR_map0927			113927		//	map0927
#define SPR_map0928			113928		//	map0928
#define SPR_map0929			113929		//	map0929
#define SPR_map0930			113930		//	map0930
#define SPR_map0931			113931		//	map0931
#define SPR_map0932			113932		//	map0932
#define SPR_map0933			113933		//	map0933
#define SPR_map0934			113934		//	map0934
#define SPR_map0935			113935		//	map0935
#define SPR_map0936			113936		//	map0936
#define SPR_map0937			113937		//	map0937
#define SPR_map0938			113938		//	map0938
#define SPR_map0939			113939		//	map0939
#define SPR_map0940			113940		//	map0940
#define SPR_map0941			113941		//	map0941
#define SPR_map0942			113942		//	map0942
#define SPR_map0943			113943		//	map0943
#define SPR_map0944			113944		//	map0944
#define SPR_map0945			113945		//	map0945
#define SPR_map0946			113946		//	map0946
#define SPR_map0947			113947		//	map0947
#define SPR_map0948			113948		//	map0948
#define SPR_map0949			113949		//	map0949
#define SPR_map0950			113950		//	map0950
#define SPR_map0951			113951		//	map0951
#define SPR_map0952			113952		//	map0952
#define SPR_map0953			113953		//	map0953
#define SPR_map0954			113954		//	map0954
#define SPR_map0955			113955		//	map0955
#define SPR_map0956			113956		//	map0956
#define SPR_map0957			113957		//	map0957
#define SPR_map0958			113958		//	map0958
#define SPR_map0959			113959		//	map0959
#define SPR_map0960			113960		//	map0960
#define SPR_map0961			113961		//	map0961
#define SPR_map0962			113962		//	map0962
#define SPR_map0963			113963		//	map0963
#define SPR_map0964			113964		//	map0964
#define SPR_map0965			113965		//	map0965
#define SPR_map0966			113966		//	map0966
#define SPR_map0967			113967		//	map0967
#define SPR_map0968			113968		//	map0968
#define SPR_map0969			113969		//	map0969
#define SPR_map0970			113970		//	map0970
#define SPR_map0971			113971		//	map0971
#define SPR_map0972			113972		//	map0972
#define SPR_map0973			113973		//	map0973
#define SPR_map0974			113974		//	map0974
#define SPR_map0975			113975		//	map0975
#define SPR_map0976			113976		//	map0976
#define SPR_map0977			113977		//	map0977
#define SPR_map0978			113978		//	map0978
#define SPR_map0979			113979		//	map0979
#define SPR_map0980			113980		//	map0980
#define SPR_map0981			113981		//	map0981
#define SPR_map0982			113982		//	map0982
#define SPR_map0983			113983		//	map0983
#define SPR_map0984			113984		//	map0984
#define SPR_map0985			113985		//	map0985
#define SPR_map0986			113986		//	map0986
#define SPR_map0987			113987		//	map0987
#define SPR_map0988			113988		//	map0988
#define SPR_map0989			113989		//	map0989
#define SPR_map0990			113990		//	map0990
#define SPR_map0991			113991		//	map0991
#define SPR_map0992			113992		//	map0992
#define SPR_map0993			113993		//	map0993
#define SPR_map0994			113994		//	map0994
#define SPR_map0995			113995		//	map0995
#define SPR_map0996			113996		//	map0996
#define SPR_map0997			113997		//	map0997
#define SPR_map0998			113998		//	map0998
#define SPR_map0999			113999		//	map0999
/*114000まで、マップ用にキープしました*/

/***
#define SPR_PUK2_END		114999		//	スプライトID末尾
***/


// スプライト ************************//


/***		ＮＰＣ

14563		//		伟神官	1
14564		//		并神官	2
14565		//		商人男.bmp	3
14566		//		商人女.bmp	4
14567		//		银行员.bmp	5
14568		//		头取.bmp	6
14569		//		工房职人１.bmp	7
14570		//		工房职人２.bmp	8
14571		//		土产屋１.bmp	9
14572		//		土产屋２.bmp	10
14573		//		ペットショップ店员.bmp	11
14574		//		バーテンダー.bmp	12
14575		//		バニー.bmp	13
14576		//		ウェイトレス.bmp	14
14577		//		おかま.bmp	15
14578		//		考古学者.bmp	16
14579		//		考古学者（倒れ）.bmp	17
14580		//		事务员.bmp	18
14581		//		街人男１.bmp	19
14582		//		街人男２.bmp	20
14583		//		街人壮年男１.bmp	21
14584		//		街人壮年男２.bmp	22
14585		//		街人少年.bmp	23
14586		//		街人じいさん.bmp	24
14587		//		街人女１.bmp	25
14588		//		街人女２.bmp	26
14589		//		街人壮年女１.bmp	27
14590		//		街人壮年女２.bmp	28
14591		//		街人少女.bmp	29
14592		//		街人老女.bmp	30
14593		//		アヒル少女.bmp	31
14594		//		和风　男	32
14595		//		和风　女	33
14596		//		和风战士　男	34
14597		//		看护妇		35
14598		//		考古学者１	36
14599		//		倒れた战士	37
14600		//		山师		38
14601		//		和风战士　男（だぶり）	39
14602		//		魔法少女１	40
14603		//		魔法少女２	41
14604		//		魔法少女３	42
14605		//		魔法少女４	43
14606		//		行き止まり	44
14607		//		公会宠物村长	45
14608		//		公会宠物战士	46
14609		//		公会宠物村民（鸟）	47
14610		//			48
14611		//			49
14612		//			50
14613		//		黒い砂时计	51
14614		//		宝箱（古い）	52
14615		//		宝箱（豪华）	53
14616		//		宝箱（新しい）	54
14617		//		压力锅（新）	55
14618		//		压力锅（旧）	56
14619		//		公会宠物村花（开）	57
14620		//		公会宠物村花（闭）	58
14621		//		降り阶段	59
14622		//		とおせんぼ岩	60
14623		//			61	

***/

#define CG_EARTH_LOSE		22450		//		耐土效果（战闘用アイコン）	
#define CG_WATER_LOSE		22451		//		耐火效果（战闘用アイコン）	
#define CG_FIRE_LOSE		22452		//		耐水效果（战闘用アイコン）	
#define CG_WIND_LOSE		22453		//		耐风效果（战闘用アイコン）	
#define CG_CRITICAL			22454		//		クリティカル（战闘用アイコン）	
#define CG_EARTH_WIN		22455		//		土受伤（战闘用アイコン）	
#define CG_WATER_WIN		22456		//		火受伤（战闘用アイコン）	
#define CG_FIRE_WIN			22457		//		水受伤（战闘用アイコン）	
#define CG_WIND_WIN			22458		//		风受伤（战闘用アイコン）	

#define CG_REVERTH_LOSE		22459		//		リバース效果による避け（战闘用アイコン）	

#define PUK2_ITEM_FIRST		238512		//	ウィンドウ改	アビリティーウィンドウ改

/***		アイテム
20100		//		日本刀（大）.bmp
20101		//		日本刀（中）.bmp
20102		//		日本刀（胁差）.bmp
20103		//		ガリアンソード.bmp
20104		//		くし型短剑.bmp
20105		//		カタール.bmp
20106		//		カタール２.bmp
20107		//		冰武器	冰剑.bmp

20167		//		シュレーカーナイフ.bmp

20300		//		冰武器	冰斧.bmp

20500		//		冰武器	冰枪.bmp

20700		//		ウォーハンマー.bmp
20701		//		ウォーハンマー??大.bmp
20702		//		ピック.bmp
20703		//		ピック??大.bmp
20704		//		メイス.bmp
20705		//		棍棒.bmp

20706		//		冰武器	冰杖.bmp

20900		//		冰武器	冰弓.bmp

21100		//		クナイ.bmp
21101		//		冰武器	冰ナイフ.bmp

21250		//		冰武器	冰ブーメラン.bmp

21300		//		グローブ.bmp
21301		//		ヌンチャク.bmp
21302		//		バグナグ.bmp
21303		//		メリケンサック.bmp
21304		//		三节棍.bmp


26250		//		液体


27300		//		おにぎり.bmp
27301		//		お子样ランチ.bmp
27302		//		ケチャップご饭。.bmp
27303		//		ご饭。.bmp
27304		//		ジャンボおにぎり.bmp
27305		//		ソース挂けご饭。.bmp
27306		//		チーズバーガー.bmp
27307		//		ネギトロ丼.bmp
27308		//		ハンバーガー.bmp
27309		//		マヨご饭。.bmp
27310		//		牛丼.bmp
27311		//		醤油.bmp
27312		//		谜料理.bmp
27313		//		卵ご饭。.bmp
27314		//		

27320		//		いくら.bmp
27321		//		キムチ.bmp
27322		//		ソース.bmp
27323		//		チーズ.bmp
27324		//		マグロ.bmp
27325		//		マヨネーズ.bmp
27326		//		ヨーグルト.bmp
27327		//		燕の巢.bmp
27328		//		蚊の目玉.bmp
27329		//		海老(小）.bmp
27330		//		海老(小.bmp
27331		//		海老（大.bmp
27332		//		熊の手.bmp
27333		//		鲸肉.bmp
27334		//		月桂树の叶.bmp
27335		//		鲑.bmp
27336		//		獣肉.bmp
27337		//		食用油.bmp
27338		//		神圣米.bmp
27339		//		酢.bmp
27340		//		大豆.bmp
27341		//		茶叶.bmp
27342		//		豚肉.bmp
27343		//		纳豆.bmp
27344		//		白菜.bmp
27345		//		羊肉.bmp

27350		//		アルマイト.bmp
27351		//		ぐみの木.bmp
27352		//		ヒスイ.bmp
27353		//		フェイクラット??琥珀.bmp
27354		//		フェイクラット化石.bmp
27355		//		火の宝石.bmp
27356		//		风の宝石.bmp
27357		//		月桂树の木.bmp
27358		//		邪神の金属.bmp
27359		//		邪神の心臓.bmp
27360		//		邪神の宝石.bmp
27361		//		邪神の木.bmp
27362		//		硝石.bmp
27363		//		神木.bmp
27364		//		人参果の木.bmp
27365		//		水の宝石.bmp
27366		//		星の砂.bmp
27367		//		石英.bmp
27368		//		石炭.bmp
27369		//		超合金乙.bmp
27370		//		土の宝石.bmp
27371		//		木炭.bmp
27372		//		龙の鳞.bmp
27373		//		琅珎.bmp


27380		//		公会宠物ドロップ
27381		//		
27382		//		
27385		//		
27386		//		
27387		//		
27390		//		
27391		//		
27392		//		
27395		//		
27396		//		
27397		//		



27400		//		家族认可证
27401		//		家族假认可证（地）
27402		//		家族假认可证（水）
27403		//		家族假认可证（火）
27404		//		家族假认可证（风）
27405		//		トランスアイテム（空）
27406		//		トランスアイテム（地）
27407		//		トランスアイテム（水）
27408		//		トランスアイテム（火）
27409		//		トランスアイテム（风）
27410		//		ゴマちゃん

27411		//		键
27412		//		
27413		//		
27414		//		

27415		//		本
27415		//		
27415		//		
27415		//		
27415		//		
27420		//		
27421		//		

27422		//		设计图
27423		//		

27424		//		地图
27425		//		
27426		//		

27427		//		陶器
27428		//		
27429		//		
27430		//		
27431		//		
27432		//		
27433		//		

27434		//		压力锅
27434		//		

27551		//		公会宠物うんち
27552		//		
27553		//		

27560		//		公会宠物フード
27561		//		
27562		//		
27563		//		
27564		//		
27565		//		
27566		//		
27567		//		
27568		//		
27569		//		

27660		//		ブレスレット
27661		//		
27662		//		
27663		//		
27664		//		
27665		//		
27666		//		
27667		//		

***/



#define PUK2_ABILITY_WINDOW	238512		//	ウィンドウ改	アビリティーウィンドウ改

#define CG_MAIL_GUILD_BOOK_RIGHT_1			238513		//		右ボタン通常　改
#define CG_MAIL_GUILD_BOOK_RIGHT_2			238514		//		右ボタン押す　改
#define CG_MAIL_GUILD_BOOK_LEFT_1			238515		//		左ボタン通常　改
#define CG_MAIL_GUILD_BOOK_LEFT_2			238516		//		左ボタン押す　改

#define PUK2_SKILL_BTN_U	238517		//		スキルリストボタン通常　改
#define PUK2_SKILL_BTN_D	238518		//		スキルリストボタン押す　改
#define PUK2_REBIRTH_BTN_U	238519		//		リバースボタン　通常
#define PUK2_REBIRTH_BTN_D	238520		//		リバースボタン　押す

#define CG_MAIL_ADDRESS_BOOK_WIN_P2			238521		//		アドレスブック改
#define CG_MAIL_GUILD_BOOK_WIN_P2			238522		//		家族ブック
#define CG_MAIL_GUILD_BOOK_SEND_BTN_1		238523		//		Sendボタン通常　改
#define CG_MAIL_GUILD_BOOK_SEND_BTN_2		238524		//		Sendボタン押す　改
#define CG_MAIL_GUILD_BOOK_HISTORY_BTN_1	238525		//		Historyボタン通常　改
#define CG_MAIL_GUILD_BOOK_HISTORY_BTN_2	238526		//		Historyボタン押す　改
#define CG_MAIL_GUILD_BOOK_DEL_BTN_1		238527		//		デリートボタン通常　改
#define CG_MAIL_GUILD_BOOK_DEL_BTN_2		238528		//		デリートボタン押す　改
#define CG_MAIL_GUILD_BOOK_GUTITLE_BTN_1	238529		//		家族称号ボタン通常
#define CG_MAIL_GUILD_BOOK_GUTITLE_BTN_2	238530		//		家族称号ボタン押す
#define CG_MAIL_GUILD_BOOK_SORT_BTN_1		238531		//		ソートボタン通常　改
#define CG_MAIL_GUILD_BOOK_SORT_BTN_2		238532		//		ソートボタン押す　改
#define CG_MAIL_GUILD_BOOK_DETAIL_1			238533		//		家族详细ボタン　通常
#define CG_MAIL_GUILD_BOOK_DETAIL_2			238534		//		家族详细ボタン　押す
#define CG_DETAIL_GUILD_BOOK_WIN			238535		//		家族详细ウィンドウ
#define CG_DETAIL_GUILD_BOOK_RIGHT_1		238536		//		右（緑）ボタン通常　改
#define CG_DETAIL_GUILD_BOOK_RIGHT_2		238537		//		右（緑）ボタン押す　改
#define CG_DETAIL_GUILD_BOOK_LEFT_1			238538		//		左（緑）ボタン通常　改
#define CG_DETAIL_GUILD_BOOK_LEFT_2			238539		//		左（緑）ボタン押す　改
#define CG_DETAIL_GUILD_BOOK_ON				238540		//		权限ON

#define PUK2_LEFTWINDW		238541		//		左コックピット　改
#define CG_GILD_LAMP		238542		//		家族ランプ
#define PUK2_RIGHTWINDW		238543		//		右コックピット　改
#define GC_GUILD_IN_BTN_U	238544		//		家族加入ボタン　通常
#define GC_GUILD_IN_BTN_D	238545		//		家族加入ボタン　押す

#define PUK2_STATUS_WINDW	238546		//		状态ウィンドウ　改
#define PUK2_BATTLE_WINDW_1	238547		//		战闘コックピット１　改
#define PUK2_BATTLE_WINDW_2	238548		//		战闘コックピット２　改
#define PUK2_BATTLE_WINDW_3	238549		//		战闘コックピット３　改

#define PUK2_ATTACK_BTN_U	238550		//		攻击ボタン通常　改
#define PUK2_ATTACK_BTN_D	238551		//		攻击ボタン押す　改

#define PUK2_GUARD_BTN_U	238552		//		防御ボタン通常　改
#define PUK2_GUARD_BTN_D	238553		//		防御ボタン押す　改

#define CG_REBIRTH_BTN_U	238554		//		リバースボタン　通常
#define CG_REBIRTH_BTN_D	238555		//		リバースボタン　押す

#define CG_TITLE_GUILD_BOOK_WIN				238556		//		家族称号ウィンドウ

//#define 238557				238557		//		文字入力用の下地

#define CG_NOW_LOADINGPUK2	238558		//		PUK2ロード画面
#define PUK2_SERVER_SELECT	238559		//		PUK2鲭セレ画面

//#define 238560				238560		//		PUK2　パワーアップ入力

#define PUK2_MAKECHRSELCG2	238561		//		PUK2キャラメイク１(ＰＵＫキャラ)
#define PUK2_MAKECHRSELCG1	238562		//		PUK2キャラメイク１（ＯＬＤキャラ）
#define PUK2_CHARAMAKE		238563		//		PUK2キャラメイク２

#define CG_PONSBIC_LOGO		238564		//		ポンスビックロゴ

#define PUK2_LOGINCHRSEL	238565		//		キャラクターセレクト

#define CG_NUM_MINUS		238566		//		マイナス（-）
#define CG_NUM_COLON		238567		//		コロン（:）



#define PUK2_BATTLEMAP_1	238600		//		ＰＵＫ２バトルマップ１
#define PUK2_BATTLEMAP_2	238601		//		ＰＵＫ２バトルマップ２
#define PUK2_BATTLEMAP_3	238602		//		ＰＵＫ２バトルマップ3	岩场用
#define PUK2_BATTLEMAP_4	238603		//		ＰＵＫ２バトルマップ4	手动生成ダンジョン洞窟用
#define PUK2_BATTLEMAP_5	238604		//		ＰＵＫ２バトルマップ5	手动生成ダンジョン室内用
#define PUK2_BATTLEMAP_6	238605		//		ＰＵＫ２バトルマップ6	手动生成ダンジョン室内废墟用

//#define 238700			238700		//		新规ウィンドウ布ツ（スタート位置）
//#define 239299			239499		//		新规ウィンドウ布ツ（エンド位置）


#define PUK2_EFF_MAP_1		239992		//		公会宠物召还时エフェクトマップ　中地
#define PUK2_EFF_MAP_2		239993		//		公会宠物召还时エフェクトマップ	中水
#define PUK2_EFF_MAP_3		239994		//		公会宠物召还时エフェクトマップ	中火
#define PUK2_EFF_MAP_4		239995		//		公会宠物召还时エフェクトマップ	中风
#define PUK2_EFF_MAP_5		239996		//		公会宠物召还时エフェクトマップ	大地
#define PUK2_EFF_MAP_6		239997		//		公会宠物召还时エフェクトマップ	大水（リザーブ）
#define PUK2_EFF_MAP_7		239998		//		公会宠物召还时エフェクトマップ	大火
#define PUK2_EFF_MAP_8		239999		//		公会宠物召还时エフェクトマップ	大风（リザーブ）


#define PUK2_FACE_01		240000		//		モンタージュ 01_バウ
#define PUK2_FACE_02		240100		//		モンタージュ 02_カズ
#define PUK2_FACE_03		240200		//		モンタージュ 03_シン
#define PUK2_FACE_04		240300		//		モンタージュ 04_トブ
#define PUK2_FACE_05		240400		//		モンタージュ 05_ゲン
#define PUK2_FACE_06		240500		//		モンタージュ 06_ベイ
#define PUK2_FACE_07		240600		//		モンタージュ 07_ボグ
#define PUK2_FACE_08		240700		//		モンタージュ 08_ウル
#define PUK2_FACE_09		240800		//		モンタージュ 09_モエ
#define PUK2_FACE_10		240900		//		モンタージュ 10_アミ
#define PUK2_FACE_11		241000		//		モンタージュ 11_メグ
#define PUK2_FACE_12		241100		//		モンタージュ 12_レイ
#define PUK2_FACE_13		241200		//		モンタージュ 13_ケイ
#define PUK2_FACE_14		241300		//		モンタージュ 14_エル
#define PUK2_FACE_15		241400		//		モンタージュ 15_セディ
#define PUK2_FACE_16		241500		//		モンタージュ 16_ビート
#define PUK2_FACE_17		241600		//		モンタージュ 17_サイゾウ
#define PUK2_FACE_18		241700		//		モンタージュ 18_ネルソン
#define PUK2_FACE_19		241800		//		モンタージュ 19_ペティット
#define PUK2_FACE_20		241900		//		モンタージュ 20_ランスロット
#define PUK2_FACE_21		242000		//		モンタージュ 21_ウィスケルス
#define PUK2_FACE_22		242100		//		モンタージュ 22_セーラ
#define PUK2_FACE_23		242200		//		モンタージュ 23_あやめ
#define PUK2_FACE_24		242300		//		モンタージュ 24_フルーティア
#define PUK2_FACE_25		242400		//		モンタージュ 25_シャハラ
#define PUK2_FACE_26		242500		//		モンタージュ 26_ピピン＠
#define PUK2_FACE_27		242600		//		モンタージュ 27_グレイス
#define PUK2_FACE_28		242700		//		モンタージュ 28_ホミ

#define PUK2_FACE_UP_01		242800		//		颜（大）1
#define PUK2_FACE_UP_02		242801		//		颜（大）2
#define PUK2_FACE_UP_03		242802		//		颜（大）3
#define PUK2_FACE_UP_04		242803		//		颜（大）4
#define PUK2_FACE_UP_05		242804		//		颜（大）5
#define PUK2_FACE_UP_06		242805		//		颜（大）6
#define PUK2_FACE_UP_07		242806		//		颜（大）7
#define PUK2_FACE_UP_08		242807		//		颜（大）8
#define PUK2_FACE_UP_09		242808		//		颜（大）9
#define PUK2_FACE_UP_10		242809		//		颜（大）10
#define PUK2_FACE_UP_11		242810		//		颜（大）11
#define PUK2_FACE_UP_12		242811		//		颜（大）12
#define PUK2_FACE_UP_13		242812		//		颜（大）13
#define PUK2_FACE_UP_14		242813		//		颜（大）14
#define PUK2_FACE_UP_15		242814		//		颜（大）15
#define PUK2_FACE_UP_16		242815		//		颜（大）16
#define PUK2_FACE_UP_17		242816		//		颜（大）17
#define PUK2_FACE_UP_18		242817		//		颜（大）18
#define PUK2_FACE_UP_19		242818		//		颜（大）19
#define PUK2_FACE_UP_20		242819		//		颜（大）20
#define PUK2_FACE_UP_21		242820		//		颜（大）21
#define PUK2_FACE_UP_22		242821		//		颜（大）22
#define PUK2_FACE_UP_23		242822		//		颜（大）23
#define PUK2_FACE_UP_24		242823		//		颜（大）24
#define PUK2_FACE_UP_25		242824		//		颜（大）25
#define PUK2_FACE_UP_26		242825		//		颜（大）26
#define PUK2_FACE_UP_27		242826		//		颜（大）27
#define PUK2_FACE_UP_28		242827		//		颜（大）28

//台服新增人物头像
#define TW_FACE_01      205000
#define TW_FACE_02      205100
#define TW_FACE_03      205200
#define TW_FACE_04      205300
#define TW_FACE_05      205400
#define TW_FACE_06      205500
#define TW_FACE_07      270000
#define TW_FACE_08      205700
#define TW_FACE_09      205800
#define TW_FACE_10      205900
#define TW_FACE_11      270100
#define TW_FACE_12      206100
#define TW_FACE_13      206200
#define TW_FACE_14      206300

#define TW_FACE_END_1      205600
#define TW_FACE_END_2      270200

/***
#define 238700			243000		//		共用（スタート位置）
#define 243224			243224		//		共用（ＥＮＤ）
#define 243300			243300		//		数字（スタート位置）
#define 243892			243892		//		数字（ＥＮＤ）
#define 243900			243900		//		システムウィンドウ（スタート位置）
#define 243979			243979		//		システムウィンドウ（ＥＮＤ）
#define 243980			243980		//		右コックピット（スタート位置）
#define 244005			244005		//		右コックピット（ＥＮＤ）
#define 244010			244010		//		左コックピット（スタート位置）
#define 244054			244054		//		左コックピット（ＥＮＤ）
#define 244060			244060		//		メインメニュー（スタート位置）
#define 244082			244082		//		メインメニュー（ＥＮＤ）
#define 244090			244090		//		マップウィンドウ（スタート位置）
#define 244091			244091		//		マップウィンドウ（ＥＮＤ）
#define 244100			244100		//		アクションウィンドウ（スタート位置）
#define 244154			244154		//		アクションウィンドウ（ＥＮＤ）
#define 244160			244160		//		アクションウィンドウ（スタート位置）
#define 244187			244187		//		アクションウィンドウ（ＥＮＤ）
#define 244160			244160		//		コマンドコックピット（スタート位置）
#define 244187			244187		//		コマンドコックピット（ＥＮＤ）
#define 244190			244190		//		名刺ウィンドウ（スタート位置）
#define 244192			244192		//		名刺ウィンドウ（ＥＮＤ）
#define 244195			244195		//		家族ウィンドウ（スタート位置）
#define 244200			244200		//		家族ウィンドウ（ＥＮＤ）
#define 244210			244210		//		家族设定ウィンドウ（スタート位置）
#define 244220			244220		//		家族设定ウィンドウ（ＥＮＤ）
#define 244230			244230		//		メールウィンドウ（スタート位置）
#define 244250			244253		//		メールウィンドウ（ＥＮＤ）
#define 244260			244260		//		スキルウィンドウ（スタート位置）
#define 244263			244263		//		スキルウィンドウ（ＥＮＤ）
#define 244270			244270		//		スキル采取系ウィンドウ（スタート位置）
#define 244275			244275		//		スキル采取系ウィンドウ（ＥＮＤ）
#define 244280			244280		//		スキルクリエートウィンドウ（スタート位置）
#define 244288			244288		//		スキルクリエートウィンドウ（ＥＮＤ）
#define 244290			244290		//		アイテムウィンドウ（スタート位置）
#define 244316			244316		//		アイテムウィンドウ（ＥＮＤ）
#define 244320			244320		//		家族共用Item BOX（スタート位置）
#define 244323			244323		//		家族共用Item BOX（ＥＮＤ）
#define 244330			244330		//		家族モンスター状态（スタート位置）
#define 244334			244334		//		家族モンスター状态（ＥＮＤ）
#define 244340			244340		//		战闘用使い魔ウィンドウ（スタート位置）
#define 244341			244341		//		战闘用使い魔ウィンドウ（ＥＮＤ）
#define 244350			244350		//		リザルト表示（スタート位置）
#define 244352			244352		//		リザルト表示（ＥＮＤ）
#define 244360			244360		//		トレードウインドウ（スタート位置）
#define 244372			244372		//		トレードウインドウ（ＥＮＤ）
#define 244380			244380		//		ショップウィンドウ（スタート位置）
#define 244382			244382		//		ショップウィンドウ（ＥＮＤ）
#define 244390			244390		//		银行ウィンドウ（スタート位置）
#define 244393			244393		//		银行ウィンドウ（ＥＮＤ）
#define 244390			244400		//		状态ウィンドウ（スタート位置）
#define 244393			244441		//		状态ウィンドウ（ＥＮＤ）
#define 244450			244450		//		使い魔ウィンドウ（スタート位置）
#define 244451			244451		//		使い魔ウィンドウ（ＥＮＤ）
#define 244460			244460		//		使い魔详细ウィンドウ（スタート位置）
#define 244473			244473		//		使い魔详细ウィンドウ（ＥＮＤ）
#define 244460			244480		//		アルバムウィンドウ（スタート位置）
#define 244473			244485		//		アルバムウィンドウ（ＥＮＤ）
#define 244490			244490		//		アルバムウィンドウ详细（スタート位置）
#define 244497			244497		//		アルバムウィンドウ详细（ＥＮＤ）
#define 244520			244520		//		共通系２（スタート位置）
#define 244536			244536		//		共通系２（ＥＮＤ）
#define 244520			244520		//		共通系２（スタート位置）
#define 244536			244536		//		共通系２（ＥＮＤ）
#define 244540			244540		//		ＢＢＳウィンドウ（スタート位置）
#define 244547			244547		//		ＢＢＳウィンドウ（ＥＮＤ）
#define 244500			245000		//		共通ウインドウ（スタート位置）
#define 245038			245038		//		共通ウインドウ（ＥＮＤ）
#define 245040			245040		//		キャラクターメイク画面１（スタート位置）
#define 245047			245047		//		キャラクターメイク画面１（ＥＮＤ）
#define 245040			245040		//		キャラクターメイク画面２（スタート位置）
#define 245055			245055		//		キャラクターメイク画面２（ＥＮＤ）
#define 245060			245060		//		キャラクターセレクト（スタート位置）
#define 245077			245077		//		キャラクターセレクト（ＥＮＤ）
#define 245150			245150		//		ログイン（スタート位置）
#define 245155			245155		//		ログイン（ＥＮＤ）
#define 245160			245160		//		チャットウィンドウ（スタート位置）
#define 245211			245211		//		チャットウィンドウ（ＥＮＤ）
#define 245160			245220		//		公会宠物えさ箱（スタート位置）
#define 245211			245222		//		公会宠物えさ箱（ＥＮＤ）
#define 245230			245230		//		ゲーム中状态（スタート位置）
#define 245233			245233		//		ゲーム中状态（ＥＮＤ）
#define 245240			245240		//		サーバーセレクト（スタート位置）
#define 245292			245292		//		サーバーセレクト（ＥＮＤ）
#define 245300			245300		//		共有ウインドウ（スタート位置）
#define 245318			245318		//		共有ウインドウ（ＥＮＤ）
#define 245320			245320		//		四角ボタン大（スタート位置）
#define 245334			245334		//		四角ボタン大（ＥＮＤ）
#define 239299			244999		//		新规ウィンドウボタン布ツ（エンド位置）
***/

#define PUK2_MAPTILE		300000		//		PUK２　マップタイル
//#define PUK2_MAPTILE_END	??????		//		PUK２　マップタイル終わり
//#define PUK2_MAPOBJ		??????		//		PUK２　マップオブジェ
//#define PUK2_MAPOBJ_END		??????		//		PUK２　マップオブジェ終わり

//
//	さいとう２ついか
//

/* 共通ボタン */
enum {
	PUK2_BUTTON_OKs0 = 244542,				// ＰＵＫ２ＯＫボタン
	PUK2_BUTTON_OKs1	,
	PUK2_BUTTON_OKs2	,
};
enum {
	PUK2_BUTTON_OK0	 = 243048,				// ＰＵＫ２ＯＫボタン
	PUK2_BUTTON_OK1	,
	PUK2_BUTTON_OK2	,
};
enum {
	PUK2_BUTTON_QUIT0 = 245153,				// ＰＵＫ２Ｑｕｉｔボタン
	PUK2_BUTTON_QUIT1,
	PUK2_BUTTON_QUIT2,
};
enum {
	PUK2_BUTTON_BACK0 = 245045,				// ＰＵＫ２Ｂａｃｋボタン
	PUK2_BUTTON_BACK1,
	PUK2_BUTTON_BACK2,
};
enum {
	PUK2_BUTTON_LOGIN0 = 245075,			// ＰＵＫ２Ｌｏｇｉｎボタン
	PUK2_BUTTON_LOGIN1,
	PUK2_BUTTON_LOGIN2,
};
enum {
	PUK2_BUTTON_DEL0 = 245072,				// ＰＵＫ２Ｄｅｌｅｔｅボタン
	PUK2_BUTTON_DEL1,
	PUK2_BUTTON_DEL2,
};
enum {
	PUK2_BUTTON_CREATE0 = 245069,			// ＰＵＫ２Ｃｒｅａｔｅボタン
	PUK2_BUTTON_CREATE1,
	PUK2_BUTTON_CREATE2,
};
enum {
	PUK2_BUTTON_LEFT0 = 243030,				// ＰＵＫ２左矢印ボタン
	PUK2_BUTTON_LEFT1,
	PUK2_BUTTON_LEFT2,
};
enum {
	PUK2_BUTTON_RIGHT0 = 243033,			// ＰＵＫ２右矢印ボタン
	PUK2_BUTTON_RIGHT1,
	PUK2_BUTTON_RIGHT2,
};
enum {
	PUK2_BUTTON_SLEFT0 = 245066,			// ＰＵＫ２左小矢印ボタン
	PUK2_BUTTON_SLEFT1,
	PUK2_BUTTON_SLEFT2,
};
enum {
	PUK2_BUTTON_SRIGHT0 = 245063,			// ＰＵＫ２右小矢印ボタン
	PUK2_BUTTON_SRIGHT1,
	PUK2_BUTTON_SRIGHT2,
};
enum {
	PUK2_BUTTON_PLUS0 = 243003,				// ＰＵＫ２プラスボタン
	PUK2_BUTTON_PLUS1,
	PUK2_BUTTON_PLUS2,
};
enum {
	PUK2_BUTTON_MINUS0 = 243006,			// ＰＵＫ２マイナスボタン
	PUK2_BUTTON_MINUS1,
	PUK2_BUTTON_MINUS2,
};


/* タイトル */
#define PUK2_TITLE			245151			// ＰＵＫ２タイトル背景
#define PUK2_TITLEINPUT		245152			// ＰＵＫ２タイトル入力栏
#define PUK2_TITLELOGO0		245150			// ＰＵＫ２タイトルロゴ
#define PUK2_TITLELOGO1		245156			// ＰＵＫ２タイトルロゴ
#define PUK2_TITLELOGO2		245157			// ＰＵＫ２タイトルロゴ
/* 鲭选择 */
#define PUK2_SERVERSELECT	245410			// ＰＵＫ２鲭选择背景
#define PUK2_SERVERBASE		245411			// ＰＵＫ２鲭选择下地
#define PUK2_SERVERFRAME	245412			// ＰＵＫ２鲭选择カーソル枠
#define PUK2_SERVERFRAMEB	245413			// ＰＵＫ２鲭选择カーソル下地
enum {
	PUK2_SERVER_NAME_NOSEL = 245460,		// ＰＵＫ２鲭名（非选择）
	PUK2_SERVER_ARZES0 ,
	PUK2_SERVER_DIOMEER0,
	PUK2_SERVER_FANITYS0,
	PUK2_SERVER_EMEUS0,
	PUK2_SERVER_ORNIS0,
	PUK2_SERVER_FREAT0,
	PUK2_SERVER_YEOSKEER0,
	PUK2_SERVER_FARKALT0,
	PUK2_SERVER_MILLIOTICE0,
	PUK2_SERVER_FINIA0,
	PUK2_SERVER_ERENOR0,
	PUK2_SERVER_KAREN0,
};
enum {
	PUK2_SERVER_NAME_SEL = 245440,			// ＰＵＫ２鲭名（选择）
	PUK2_SERVER_ARZES1,
	PUK2_SERVER_DIOMEER1,
	PUK2_SERVER_FANITYS1,
	PUK2_SERVER_EMEUS1,
	PUK2_SERVER_ORNIS1,
	PUK2_SERVER_FREAT1,
	PUK2_SERVER_YEOSKEER1,
	PUK2_SERVER_FARKALT1,
	PUK2_SERVER_MILLIOTICE1,
	PUK2_SERVER_FINIA1,
	PUK2_SERVER_ERENOR1,
	PUK2_SERVER_KAREN1,
};
#define PUK2_NUMERAL_NOSEL	245430			// サーバ用数字０（非选择）
#define PUK2_NUMERAL_SEL	245420			// サーバ用数字０（选择）
#define PUK2_SERVERPAGE1	245481			// サーバ选择ページ１
#define PUK2_SERVERPAGE2	245482			// サーバ选择ページ２

/* キャラクタメイキング */
#define PUK2_CHARACREATE0	245040			// ＰＵＫ２キャラクリ背景??その１
#define PUK2_CHARACREBASE	245041			// ＰＵＫ２キャラクリ下地
#define PUK2_CHARACRECOLOR	245044			// ＰＵＫ２キャラクリ色选择下地
#define PUK2_CHARACREFRAME	245042			// ＰＵＫ２キャラクリ选择カーソル枠
#define PUK2_CHARACREFRAMEB	245043			// ＰＵＫ２キャラクリ选择カーソル下地

#define PUK2_CHARASTATUS	245050			// ＰＵＫ２キャラクリ背景??その２
#define PUK2_STATUSBASE		245051			// ＰＵＫ２キャラ状态下地
enum {
	PUK2_STATUS_EARTH = 245052,				// ＰＵＫ２キャラ状态用属性
	PUK2_STATUS_WATER,
	PUK2_STATUS_FIRE,
	PUK2_STATUS_WIND,
};

#define PUK2_CHARASELECT	245060			// ＰＵＫ２キャラ选择背景
#define PUK2_CHARASELBASE	245061			// ＰＵＫ２キャラ选择下地
#define PUK2_CHARASELDISP	245062			// ＰＵＫ２キャラクタ表示

#define PUK2_UPDATEBTNBASE	245078			// ＰＵＫ２アップデートボタンウィンドウ


//
//	ここまで
//



// その他のグラフィックの宣言
#include "Graphic_ID.h"

#endif
