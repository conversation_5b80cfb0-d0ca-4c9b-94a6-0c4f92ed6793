﻿/************************/
/*	battleMaster.cpp	*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/math2.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/pattern.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/mouse.h"
#include "../systeminc/t_music.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/battle.h"
#include "../systeminc/battleEffect.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/battleMaster.h"
#include "../systeminc/process.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/tool.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/pc.h"
#include <math.h>
#ifdef PUK2
	#include "../PUK2/newDraw/anim_tbl_PUK2.h"
#endif

#ifdef PUK2
#include "../puk2/newBattle/newBattle.cpp"
#else
// バトルマスターアクションポインタ
ACTION *pActBm;
// バトルキャラクターデータ読み込みポイント
int BcReadPoint;
// バトルムービーデータ読み込みポイント
int BmReadPoint;

// バトルコマンド文字列
char BmData[ B_MOVIE_DATA_SIZE ];
// バトルコマンドバックアップ
char BmDataBak[ B_BUF_SIZE ][ B_MOVIE_DATA_SIZE ];

// バトルコマンド文字列ポインター
int BmDataGetBufNo;
int BmDataSetBufNo;

// バトル状态文字列
char BcData[ B_CHAR_DATA_SIZE ];
// バトル状态文字列バックアップ
char BcDataBak[ B_BUF_SIZE ][ B_CHAR_DATA_SIZE ];
// バトル状态文字列ポインター
int BcDataGetBufNo;
int BcDataSetBufNo;

// Ｂｍチャットバッファー
//char BmChatBuffer[ BM_CHAT_BUFFER_SIZE ][ 256 ];
// Ｂｍチャットバッファーストックカウンター
//int BmChatBufferStockCnt = 0;
// Ｂｍチャットバッファーゲットカウンター
//int BmChatBufferGetCnt = 0;

// 武器坏れたときの新ＳＰＲ番号バッファー
//int BmWeaponBreakBuffer[ 100 ];
// 武器坏れたときの新ＳＰＲ番号ストックカウンター
//int BmWeaponBreakBufferStockCnt = 0;
// 武器坏れたときの新ＳＰＲ番号ゲットカウンター
//int BmWeaponBreakBufferGetCnt = 0;


// 怪我情报受信
//char BiData[ B_MOVIE_DATA_SIZE ];

// ムービー読み込みフラグ
int ReadBmDataFlag = FALSE;
// 自分がＡＫＯかフラグ
int MyAkoFlag = FALSE;

// コマンドの文字列
char *BattleCmdStr[] ={
					//"HIT",	// 攻击
					//"MSL",	// 飞び道具
					"SKL",	// スキル攻击                                                  //MLHIDE
					"ESC",	// 逃げる                                                    //MLHIDE
					"CLR",	// 退却                                                     //MLHIDE
					"SUP",	// スキルアップ                                                 //MLHIDE
					"POS",	// ポジションチェンジ                                              //MLHIDE
					"MON",	// ペット出し入れ                                                //MLHIDE
					"EQU",	// 装备变更                                                   //MLHIDE
					"ITM",	// アイテム使用                                                 //MLHIDE
					"CMB",	// 合体攻击                                                   //MLHIDE
					"PSN",	// 毒受伤、体力再生                                               //MLHIDE
					"ANR",	// 状态异常自动回复                                               //MLHIDE
					"PUE",	// パラメータアップダウン效果終了                                        //MLHIDE
					"ACT",	// ２アクションマーク                                              //MLHIDE
					"CFP",	// 魔力消费プロトコル                                              //MLHIDE
					"CHT",	// 战闘チャットプロトコル                                            //MLHIDE
					"PNO",	// ペット嫌がる                                                 //MLHIDE
					"PLV",	// ペット逃走                                                  //MLHIDE
					"CBC",	// ＢＣデータ变更                                                //MLHIDE
					"PDN",	// 受伤ダウン（踊り专用）                                            //MLHIDE
					"END",	// 战闘終了                                                   //MLHIDE
					"MAX"                                                            //MLHIDE
				};

// ターゲットナンバー
extern TargetNo;

// リストの初期化
void BattleInitList( void );
// リストの初期化（ＩＤ指定版、死んでる人も）
void BattleInitListWithDead( int id );
// 行动リストをセットする
void BattleSetActList( ACTION *pAct, BC_ACT actNo );
// 行动リストをセットする
void BattleSetActList( ACTION *pAct, BC_ACT actNo, int actListCnt );
// 行动リストスタート
void BattleStartActList( ACTION *pAct );
// 次の行动リストへ
void BattleNextActList( ACTION *pAct );
// 敌リストをセットする
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage );
// 敌リストをセットする
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage, int newGraNo );
// 次の敌リストへ
BOOL BattleNextEnemyList( ACTION *pAct );
// 行动終了チェック
BOOL BattleCheckActEnd( int flag );
// バトルムービーデータからコマンドを読み込む
int ReadBmDataCmd( char *cmd );
// バトルムービーデータから数字を読み込む
int ReadBmDataNum( void );
// バトルムービーデータから文字列を読み込む
void ReadBmDataStr( char *str, int len );

// バトルキャラクター作成 
ACTION *MakeBattleChar( int id );
// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
//void BattleMastarCheckBmChat( int bmFlag );


// 直接攻击読み込み处理 ******************************************************
void ReadDirectAttack( int skillId, int techId, int consumeFp, int *myId, int *myIdBak, int *enemyId, int *bodyGuardId )
{
	int sameId, bmFlag, damage, bloodPoint, newGraNo, cnt = 0;
	int deathFlag = FALSE;
#ifdef _DEBUG
	char moji[ 1024 ];
#endif	
	// バトルムービーデータから数字を読み込む
	*myId = *myIdBak = sameId = ReadBmDataNum();		// 自分の番号
	
	// 行动リスト初期化
	//pYobi->actListCnt = 0;
	// 攻击侧がいなくなったら拔ける
	if( *myId == 0xFF ) return;
	
	// いるかチェック
	if( pActBc[ *myId ] == NULL ){
		#ifdef _DEBUG
		sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, *myId );  //MLHIDE
		MessageBox( hWnd, moji, "BM_ACT_HIT", MB_OK | MB_ICONSTOP );        //MLHIDE
		#endif
		return;
	}
	
	// デフォルトの方向へ向ける
	BattleSetActList( pActBc[ *myId ], BC_ACT_CHANGE_DEF_DIR );
	
	
	// 连続攻击のとき *************************************************
	if( skillId == B_SKILL_DOUBLE_ATTACK ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
		// 残像フラグＯＮ
		//( ( BC_YOBI *)pActBc[ *myIdBak ]->pYobi )->actZanzouFlag = TRUE;
	}else
	// エッジ *********************************************************/
	if( skillId == B_SKILL_EDGE ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// バーストアタック *********************************************************/
	if( skillId == B_SKILL_BURST ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// フォースカット *********************************************************/
	if( skillId == B_SKILL_FORCECUT ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 盗む *********************************************************/
	if( skillId == B_SKILL_STEAL ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 状态付加攻击 *********************************************************/
	if( skillId >= B_SKILL_ADD_BAD_CONDITION_POISON && skillId <= B_SKILL_ADD_BAD_CONDITION_FORGET ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 吸血攻击 *********************************************************/
	if( skillId == B_SKILL_ATTACK_BLOOD ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// エナジードレイン *********************************************************/
	if( skillId == B_SKILL_ENERGYDRAIN ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 装备破坏攻击 *********************************************************/
	if( skillId == B_SKILL_BREAKWEAPON ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// GOLD攻击 *********************************************************/
	if( skillId == B_SKILL_ATTACKGOLD ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 吸血攻击 *********************************************************/
	//if( skillId == B_SKILL_ATTACK_BLOOD ){
		// 行动リストをセットする（自分）
	//	BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	//}else
	// 暗杀攻击 *********************************************************/
	if( skillId == B_SKILL_ASSASSIN ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 搅乱 *********************************************************/
	if( skillId == B_SKILL_KAKURAN ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}else
	// 防御ブレイク *********************************************************/
	if( skillId == B_SKILL_GUARDBRAKE ){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}
	
	
#ifdef _TEST_TECH_YUK
	if( skillId == B_SKILL_RANBU){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SUPERSKILL_GENERATE );
		// 残像フラグＯＮ
		( ( BC_YOBI *)pActBc[ *myIdBak ]->pYobi )->actZanzouFlag = TRUE;
	}
	if( skillId == B_SKILL_PICKPOCKET){
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_EFFECT_SKILL_GENERATE );
	}
#endif /* _TEST_TECH_YUK */

	// 相手の番号
	*enemyId = ReadBmDataNum();
	
	// いるかチェック
	if( pActBc[ *enemyId ] == NULL ){
		#ifdef _DEBUG
		sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d",skillId, *enemyId ); //MLHIDE
		MessageBox( hWnd, moji, "BM_ACT_HIT", MB_OK | MB_ICONSTOP );        //MLHIDE
		#endif
		return;
	}
	
	// スキルＩＤ记忆
	( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->skillId = skillId;
	// テックＩＤ记忆
	( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->techId = techId;
	// 消费フォースポイント记忆
	( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->consumeFp = consumeFp;
	// 魔力マイナス
	BattleSetActList( pActBc[ *myId ], BC_ACT_CONSUME_FP );
	
	bmFlag = ReadBmDataNum();		// フラグ
	damage = ReadBmDataNum();		// 受伤
	
	// 吸血攻击の时
	if( skillId == B_SKILL_ATTACK_BLOOD ){
		// 吸血ポイント読み込み
		bloodPoint = ReadBmDataNum();		// 受伤
	}
	
	// 武器坏れたとき
	if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
		newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
		newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
	}else{
		newGraNo = 0;
	}
	
	// 防御ブレイクの时は、防御フラグを落とす
	if( skillId == B_SKILL_GUARDBRAKE ){
		bmFlag &= ~BM_FLAG_GUARD;
	}
	// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
	//BattleMastarCheckBmChat( bmFlag );
	
	// 狙う敌がいなくなるまでループ
	while( 1 ){
		
		// 行动リストをセットする（自分）
		BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_STANDBY_WAIT );
		// カウンターの时
		if( bmFlag & BM_FLAG_COUNTER ){
			BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE );
			BattleSetActList( pActBc[ *myId ], BC_ACT_EFFECT_COUNTER );
		}else
		// クロスカウンターの时
		if( bmFlag & BM_FLAG_CROSS_COUNTER ){
			BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE );
			BattleSetActList( pActBc[ *myId ], BC_ACT_EFFECT_COUNTER );
			//BattleSetActList( pActBc[ *myId ], BC_ACT_EFFECT_CROSS_COUNTER );
		}else
		// 暗杀の时
		if( skillId == B_SKILL_ASSASSIN ){
			// 何もしない
		}else
		// ２回目以降はここ
		if( cnt > 0 && *myId == sameId ){
			BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE );
		}else{
			BattleSetActList( pActBc[ *myId ], BC_ACT_B_MOVE_START );
			BattleSetActList( pActBc[ *myId ], BC_ACT_B_MOVE );
			BattleSetActList( pActBc[ *myId ], BC_ACT_B_MOVE_END );
		}
		
		// 敌リストをセットする
		BattleSetEnemyList( pActBc[ *myId ], *enemyId, bmFlag, damage, newGraNo );

		// 护卫のとき *********************************************************
		if( bmFlag & BM_FLAG_BODYGUARD ){
			// 护卫のＩＤ読み込み
			*bodyGuardId = ReadBmDataNum();
			// 敌リストをセットする
			BattleSetEnemyList( pActBc[ *myId ], *bodyGuardId, bmFlag, damage, newGraNo );
			
			// 次の敌リストへ
			BattleSetActList( pActBc[ *myId ], BC_ACT_NEXT_ENEMY_LIST );
			// 行动リストをセットする（自分）
			BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_STANDBY_WAIT );
			// 护卫の人をスタートさせる
			BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
			// いったん停止
			BattleSetActList( pActBc[ *myId ], BC_ACT_STANDBY );
			// 行动リストをセットする（自分）
			BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_STANDBY_WAIT );
			
			
			// 护卫侧
			// 行动リストをセットする
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_STANDBY );
			
			// 敌リストをセットする
			BattleSetEnemyList( pActBc[ *bodyGuardId ], *enemyId, bmFlag, damage );
			// 敌リストをセットする
			//BattleSetEnemyList( pActBc[ *bodyGuardId ], *myId, bmFlag, damage );
			// 敌リストをセットする
			BattleSetEnemyList( pActBc[ *bodyGuardId ], *enemyId, bmFlag, damage );
			
			
			// 相手のＩＤ记忆
			( ( BC_YOBI *)pActBc[ *bodyGuardId ]->pYobi )->otherId = *enemyId;
			// 相手の方向へ向ける
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_CHANGE_OTHER_ID_DIR );
			// ＳＥ番号记忆
			( ( BC_YOBI *)pActBc[ *bodyGuardId ]->pYobi )->seNo = 252;
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_PLAY_SE );
			// ジャンプグラフィック番号
			( ( BC_YOBI *)pActBc[ *bodyGuardId ]->pYobi )->jumpGraNo = CG_B_STR_SURPRISAL;
			// グラフィック表示
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_GRA_DISP );
			// ０．５秒待つ
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_WAIT_05 );
			// 残像フラグＯＮ
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_ZANZOU_ON );
			// 护卫する人のところまで移动
			//BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_MOVE_NEAR );
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_B_MOVE_START );
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_MOVE_NEAR );
			//BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_B_MOVE );
			
			// 护卫される人をこっちに向ける
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_CHANGE_MY_ID_DIR );
			// 护卫される人の行动をスタート
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
			// 次の敌リストへ
		//	BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_NEXT_ENEMY_LIST );
			
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_B_MOVE_END );
			// 残像フラグＯＦＦ
			BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_ZANZOU_OFF );
			
			// 相手を次の行动へ
		//	BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
			// 次の敌リストへ
		//	BattleSetActList( pActBc[ *bodyGuardId ], BC_ACT_NEXT_ENEMY_LIST );
			
		
			
			// 行动リストをセットする
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_STANDBY );
			// 相手のＩＤ记忆
			//( ( BC_YOBI *)pActBc[ *enemyId ]->pYobi )->otherId = *bodyGuardId;
			// 相手の方向へ向ける
			//BattleSetActList( pActBc[ *enemyId ], BC_ACT_CHANGE_OTHER_ID_DIR_2 );
			// 护卫される人突き飞ばされる处理
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_HIT_BACK2 );
			// 相手を次の行动へ
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
			// 次の敌リストへ
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_NEXT_ENEMY_LIST );
			// 立ちアニメーションにする
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_ANIM_STAND );
			// 行动リストをセットする
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_STANDBY2 );
			// デフォルト位置に移动する
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_MOVE_DEF );
			// 行动リストをセットする
			//BattleSetActList( pActBc[ *enemyId ], BC_ACT_STANDBY );
			
			// 敌リストをセットする
			BattleSetEnemyList( pActBc[ *enemyId ], *myId, bmFlag, damage );
			
			// 敌を护卫の人に入れ替え
			*enemyId = *bodyGuardId;
			
		}
		
		// 搅乱の时
		if( skillId == B_SKILL_KAKURAN ){
			// 状态异常の种类読み込み
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->kakuranKind = ReadBmDataNum();
		}
		
		// 防御ブレイク *********************************************************/
		if( skillId == B_SKILL_GUARDBRAKE ){
			// グラフィック番号
			( ( BC_YOBI *)pActBc[ *myIdBak ]->pYobi )->jumpGraNo = CG_B_STR_GUARD_BREAK;
			// 行动リストをセットする（自分）
			BattleSetActList( pActBc[ *myIdBak ], BC_ACT_JUMP_DISP );
		}else
		// エナジードレイン *********************************************************/
		if( skillId == B_SKILL_ENERGYDRAIN ){
			// グラフィック番号
			( ( BC_YOBI *)pActBc[ *myIdBak ]->pYobi )->jumpGraNo = CG_B_STR_ENERGY_DRAIN;
			// 行动リストをセットする（自分）
			BattleSetActList( pActBc[ *myIdBak ], BC_ACT_JUMP_DISP );
		}else
		// 装备破坏攻击 *********************************************************/
//		if( skillId == B_SKILL_BREAKWEAPON ){
			// グラフィック番号
//			( ( BC_YOBI *)pActBc[ *myIdBak ]->pYobi )->jumpGraNo = CG_B_STR_SKILL_UP;
			// 行动リストをセットする（自分）
//			BattleSetActList( pActBc[ *myIdBak ], BC_ACT_JUMP_DISP );
//		}else
		// GOLD攻击 *********************************************************/
		if( skillId == B_SKILL_ATTACKGOLD ){
			// グラフィック番号
			( ( BC_YOBI *)pActBc[ *myIdBak ]->pYobi )->jumpGraNo = CG_B_STR_GOLD_ATTACK;
			// 行动リストをセットする（自分）
			BattleSetActList( pActBc[ *myIdBak ], BC_ACT_JUMP_DISP );
		}
		
		
		// 盗む时
#ifdef _TEST_TECH_YUK
		if( skillId == B_SKILL_STEAL || skillId == B_SKILL_PICKPOCKET ){
#else
		if( skillId == B_SKILL_STEAL ){
#endif /* _TEST_TECH_YUK */
			// きょろきょろする
			BattleSetActList( pActBc[ *myId ], BC_ACT_KYORO );
			// 相手のＩＤ记忆
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->otherId = *enemyId;
			// 相手の方向へ向ける
			BattleSetActList( pActBc[ *myId ], BC_ACT_CHANGE_OTHER_ID_DIR );
			// 残像フラグＯＮ
			BattleSetActList( pActBc[ *myId ], BC_ACT_ZANZOU_ON );
			// ＳＥ番号记忆
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo = 110;
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE );
			// 敌を次に行动にする
			BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
			// 里侧に回る
			BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE_BACK );

			
			// 相手の方向へ向ける
			BattleSetActList( pActBc[ *myId ], BC_ACT_CHANGE_OTHER_ID_DIR );
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE );
			// 里侧に回る
			BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE_BACK );
			
			
			// 残像フラグＯＮ
			BattleSetActList( pActBc[ *myId ], BC_ACT_ZANZOU_OFF );
			
			// 成功したとき
			if( bmFlag & BM_FLAG_SUCCESS ){
				// ＳＥ番号记忆
				( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo2 = 257;
				// ＳＥ鸣らす
				BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE_2 );
				// グラフィックジャンプさせる
				BattleSetActList( pActBc[ *myId ], BC_ACT_JUMP_DISP );
				// グラフィック番号
				( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->jumpGraNo = CG_B_STR_STEAL_SUCCESS;
				// 喜ぶ
				BattleSetActList( pActBc[ *myId ], BC_ACT_HAPPY );
			}else{
				// ＳＥ番号记忆
				( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo2 = 258;
				// ＳＥ鸣らす
				BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE_2 );
				// グラフィックジャンプさせる
				BattleSetActList( pActBc[ *myId ], BC_ACT_JUMP_DISP );
				// グラフィック番号
				( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->jumpGraNo = CG_B_STR_STEAL_MISS;
				
				// 悲伤
				//BattleSetActList( pActBc[ *myId ], BC_ACT_SAD );
				
				// 立ちアニメーションにする
				//BattleSetActList( pActBc[ *myId ], BC_ACT_ANIM_STAND );
				BattleSetActList( pActBc[ *myId ], BC_ACT_SAD_2 );
				// １秒待つ
				BattleSetActList( pActBc[ *myId ], BC_ACT_WAIT_1 );
			}
			
			// 敌を次に行动にする
			//BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_NEXT_ACT_LIST );

		}else
		// 暗杀の时
		if( skillId == B_SKILL_ASSASSIN ){
			// ＳＥ番号记忆
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo = 62;
		//	( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo = 204;
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE );
			// 点灭フラグＯＮ
			BattleSetActList( pActBc[ *myId ], BC_ACT_TENMETU_ON );
			// ０．５秒待つ
			BattleSetActList( pActBc[ *myId ], BC_ACT_WAIT_05 );
			// 隠すフラグＯＮ
			BattleSetActList( pActBc[ *myId ], BC_ACT_HIDE );
			// ０．５秒待つ
			BattleSetActList( pActBc[ *myId ], BC_ACT_WAIT_05 );
			// 相手の后ろに瞬间移动
			BattleSetActList( pActBc[ *myId ], BC_ACT_WARP_ENEMY_BACK );
			// ＳＥ番号记忆
	//		( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo2 = 61;
			// ＳＥ鸣らす
	//		BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE_2 );
			// 隠すフラグＯＦＦ
			BattleSetActList( pActBc[ *myId ], BC_ACT_HIDE_OFF );
			// ０．５秒待つ
			BattleSetActList( pActBc[ *myId ], BC_ACT_WAIT_05 );
			// 点灭フラグＯＦＦ
			BattleSetActList( pActBc[ *myId ], BC_ACT_TENMETU_OFF );
			// ０．５秒待つ
			BattleSetActList( pActBc[ *myId ], BC_ACT_WAIT_05 );
			
			// 残像フラグＯＮ
			BattleSetActList( pActBc[ *myId ], BC_ACT_ZANZOU_ON );
			
			// ＳＥ番号记忆
			//( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo = 110;
			//( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo2 = 118;
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo2 = 210;
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE_2 );
			
			
			// 后ろから駆け拔ける
			//BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE_BACK );
			BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE_ASSASSIN );
			// 相手の方向へ向ける
		//	BattleSetActList( pActBc[ *myId ], BC_ACT_CHANGE_OTHER_ID_DIR );
			// ＳＥ鸣らす
		//	BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE );
			// 里侧に回る
		//	BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE_BACK );
			
			// 残像フラグＯＦＦ
			BattleSetActList( pActBc[ *myId ], BC_ACT_ZANZOU_OFF );
			
			// 立ちアニメーションにする
			BattleSetActList( pActBc[ *myId ], BC_ACT_ANIM_STAND );
			
			// ＳＥ番号记忆
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo3 = 133;
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE_3 );
			
			// ＫＩＬＬアイコン表示
			// グラフィック番号
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->jumpGraNo = CG_B_STR_KILL;
			// グラフィックジャンプさせる
			BattleSetActList( pActBc[ *myId ], BC_ACT_JUMP_DISP );
			
			// 相手のアニメーションを止める
			BattleSetActList( pActBc[ *myId ], BC_ACT_ANIM_OFF );
			
			
			// １秒待つ
			BattleSetActList( pActBc[ *myId ], BC_ACT_WAIT_1 );
			
			// 相手をアニメーションさせる
			BattleSetActList( pActBc[ *myId ], BC_ACT_ANIM_ON );
			
			// 敌を次の行动にする
			BattleSetActList( pActBc[ *myId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
				
			// 次の敌リストへ
			BattleSetActList( pActBc[ *myId ], BC_ACT_NEXT_ENEMY_LIST );
			
		//	BattleSetActList( pActBc[ *enemyId ], BC_ACT_DEAD_FLAG_ON );
			
		}else
		// 搅乱の时
		if( skillId == B_SKILL_KAKURAN ){
			// ここら辺に搅乱の处理を入れる
			// ＳＥ番号记忆
			( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo = 260;
			// ＳＥ鸣らす
			BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE );
			// 搅乱の时
			BattleSetActList( pActBc[ *myId ], BC_ACT_KAKURAN_GENERATE );
			// 攻击
			BattleSetActList( pActBc[ *myId ], BC_ACT_ATTACK );
#ifdef _TEST_TECH_YUK
		}else
		if( skillId == B_SKILL_RANBU){
			// アニメーション速度４倍
			BattleSetActList( pActBc[ *myId ], BC_ACT_ANIMSPD_X4 );
			// 乱舞アクション
			BattleSetActList( pActBc[ *myId ], BC_ACT_RANBU_ACT );
			// 攻击
			BattleSetActList( pActBc[ *myId ], BC_ACT_ATTACK );
#endif /* _TEST_TECH_YUK */
		}else{
			// 攻击
			BattleSetActList( pActBc[ *myId ], BC_ACT_ATTACK );
			// 攻击しに行った侧が反射した时（カウンターされたとき）
			//if( *enemyId == *myIdBak ){
				
			//}
		}
		
		// 行动リストをセットする（相手）
		BattleSetActList( pActBc[ *enemyId ], BC_ACT_STANDBY );
		
		// 回避じゃないとき
		if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
			
			// ＡＫＯ１のとき
			if( bmFlag & BM_FLAG_AKO1 ){
				
				// 通常攻击の时
				if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
					
					// 物理反射のとき
					if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
						BattleSetActList( pActBc[ *myId ], BC_ACT_AKO1 );		// ＡＫＯ
						// 反射エフェクト
						//BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
						BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
					}else{
						BattleSetActList( pActBc[ *enemyId ], BC_ACT_AKO1 );		// ＡＫＯ
						// 护卫のとき（后始末处理）***************************************
						if( bmFlag & BM_FLAG_BODYGUARD ){
							// 相手を次の行动へ
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
							// 次の敌リストへ
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_NEXT_ENEMY_LIST );
						}
					}
					
					// 物理反射のとき（攻击した侧が飞ぶ）
					if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
						// 攻击する侧が自分（ BattleMyNo )のとき
						if( BattleMyNo == *myId ){
							BattleSetActList( pActBc[ *myId ], BC_ACT_BATTLE_END );	// 战闘終了
						}else{
							BattleSetActList( pActBc[ *myId ], BC_ACT_PET_OUT );		// ペットいたら退场
							BattleSetActList( pActBc[ *myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
						}
					}else{
						// 自分のとき（攻击された侧が飞ぶ）
						if( BattleMyNo == *enemyId ){
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
						}else{
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
						}
					}
				}
			}else{
				// 受伤が０じゃないとき
				if( damage != 0 || skillId == B_SKILL_FORCECUT ){
					// 通常攻击かクリティカルの时
					if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
						// 物理无效のとき
						if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
							// 无效エフェクト
							//BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS );
						}else
						// 物理吸收のとき
						if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
							// 吸收エフェクト
							//BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS );
						}else
						// 物理反射のとき
						if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
							// 自分がヒットバック
							//BattleSetActList( pActBc[ *myIdBak ], BC_ACT_HIT_BACK );
							BattleSetActList( pActBc[ *myId ], BC_ACT_HIT_BACK );
							
#if 0
							// 攻击しに行った侧が反射された时（临时で移动する）
							if( *enemyId != *myIdBak ){
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ *myId ], *enemyId, bmFlag, damage );
								// 反射された时だけ、间合いを诘める
								BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE );
								// 次の敌リストへ
								BattleSetActList( pActBc[ *myId ], BC_ACT_NEXT_ENEMY_LIST );
							}
#endif						
							// 反射エフェクト
							//BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
							BattleSetActList( pActBc[ *enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
						}else{
							// 盗むじゃ无いとき
#ifdef _TEST_TECH_YUK
							if( !( skillId == B_SKILL_STEAL || skillId == B_SKILL_PICKPOCKET) ){
#else
							if( !( skillId == B_SKILL_STEAL ) ){
#endif /* _TEST_TECH_YUK */
								BattleSetActList( pActBc[ *enemyId ], BC_ACT_HIT_BACK );
							}
						}
					}
				}
			}
		}
		// 回避の时
		//if( bmFlag & BM_FLAG_AVOIDANCE ){
			//BattleSetActList( pActBc[ *enemyId ], BC_ACT_HIT_BACK );
		//}else
		
		// 护卫のとき（后始末处理）***************************************
		if( bmFlag & BM_FLAG_BODYGUARD ){
		
			// 护卫しに行って、相手の攻击がミスの时
			if( damage == 0 ){
				BattleSetActList( pActBc[ *enemyId ], BC_ACT_WAIT_1 );
			}
			// 相手を次の行动へ
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
			// 次の敌リストへ
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_NEXT_ENEMY_LIST );
			// 物理无效のとき、物理吸收のとき、 物理反射のとき
			if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS 
				|| bmFlag & BM_FLAG_ABSORB_PHYSICS 
				|| bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
				// デフォルト位置に移动する
				BattleSetActList( pActBc[ *enemyId ], BC_ACT_MOVE_DEF );
			}
			// 気絶じゃない时
			if( !( bmFlag & BM_FLAG_DEATH ) ){
				BattleSetActList( pActBc[ *enemyId ], BC_ACT_CHANGE_DEF_DIR );
			}
			// 护卫しに行って、相手の攻击がミスの时
			if( damage == 0 ){
				//BattleSetActList( pActBc[ *enemyId ], BC_ACT_WAIT_2 );
				BattleSetActList( pActBc[ *enemyId ], BC_ACT_MOVE_DEF );
			}
			
		}
		
		// 受伤が０じゃないとき（受伤食らったとき）または、回避の时
		if( damage != 0 || skillId == B_SKILL_FORCECUT || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
			// 物理无效のとき
			if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
			}else
			// 物理吸收のとき
			if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
			}else
			// 物理反射のとき
			if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
				// 攻击しに行った侧が反射した时（カウンターされたとき）
				if( *enemyId == *myIdBak ){
					BattleSetActList( pActBc[ *myId ], BC_ACT_MOVE_DEF );
				}
			}else{
				// カウンターでない、または自分にカウンターじゃない时（返り讨ちじゃ无いとき）
				//if( !( bmFlag & BM_FLAG_COUNTER ) || *enemyId != *myIdBak ){
				// 自分にカウンターじゃない时（返り讨ちじゃ无いとき）
				if( *enemyId != *myIdBak ){
					// 盗むじゃ无いとき
#ifdef _TEST_TECH_YUK
					if( skillId != B_SKILL_STEAL && skillId != B_SKILL_ASSASSIN && skillId != B_SKILL_PICKPOCKET){
#else
					if( skillId != B_SKILL_STEAL && skillId != B_SKILL_ASSASSIN ){
#endif /* _TEST_TECH_YUK */
						BattleSetActList( pActBc[ *enemyId ], BC_ACT_MOVE_DEF );
					}
				}
				// 身交わしのとき
				if( bmFlag & BM_FLAG_MIKAWASHI ){
					BattleSetActList( pActBc[ *enemyId ], BC_ACT_ZANZOU_OFF );
				}
			}
		}
		
		// 気絶の时
		if( bmFlag & BM_FLAG_DEATH ){
			// 物理反射のとき
			if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
				// 攻击侧が攻击されてないとき
				if( *enemyId != *myIdBak ){
					deathFlag = TRUE;
				}else{
					BattleSetActList( pActBc[ *myId ], BC_ACT_DEAD );
				}
			}else{
				// 攻击侧が攻击されてないとき
				if( *enemyId != *myIdBak ){
					// 暗杀の时
					if( skillId == B_SKILL_ASSASSIN ){
						BattleSetActList( pActBc[ *enemyId ], BC_ACT_DEAD_FLAG_ON );
					}
					BattleSetActList( pActBc[ *enemyId ], BC_ACT_DEAD );
					
				}else{
					deathFlag = TRUE;
				}
			}
		}
		
		// 盗むとき *****************************************************
#ifdef _TEST_TECH_YUK
		if( skillId == B_SKILL_STEAL || skillId == B_SKILL_PICKPOCKET){
#else
		if( skillId == B_SKILL_STEAL ){
#endif /* _TEST_TECH_YUK */
			BattleSetActList( pActBc[ *enemyId ], BC_ACT_DAMAGE );
		}
		
		
		// 吸血攻击の时
		if( skillId == B_SKILL_ATTACK_BLOOD ){
			// 受伤が０じゃないとき（受伤食らったとき）または、回避の时
			if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
				// 物理吸收、无效じゃない时
				if( !( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ) && !( bmFlag & BM_FLAG_ABSORB_PHYSICS ) ){
					// 物理反射で死んでない时
					if( !( bmFlag & BM_FLAG_REFLECTION_PHYSICS && bmFlag & BM_FLAG_DEATH ) ){
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ *myId ], BC_ACT_ANIM_STAND );
						
						// ＳＥ番号记忆
						( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->seNo = 271;
						// ＳＥ鸣らす
						BattleSetActList( pActBc[ *myId ], BC_ACT_PLAY_SE );
						
						
						// 回复ポイント记忆
						( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->point = bloodPoint;
						// 文字色记忆
						( ( BC_YOBI *)pActBc[ *myId ]->pYobi )->color = FONT_PAL_GREEN;
						// 回复ポイント表示
						BattleSetActList( pActBc[ *myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
					}
				}
			}
		}
		
		// バトルムービーデータから数字を読み込む
		*myId = sameId = ReadBmDataNum();		// 自分の番号
		
		// 攻击侧がいなくなったら拔ける
		if( *myId == 0xFF ) break;
		// いるかチェック
		if( pActBc[ *myId ] == NULL ){
			#ifdef _DEBUG
			sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, *myId ); //MLHIDE
			MessageBox( hWnd, moji, "BM_ACT_HIT", MB_OK | MB_ICONSTOP );       //MLHIDE
			#endif
			break;
		}
		// 次の相手を読み込み
		*enemyId = ReadBmDataNum();
		
		// いるかチェック
		if( pActBc[ *enemyId ] == NULL ){
			#ifdef _DEBUG
			sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d",skillId, *enemyId ); //MLHIDE
			MessageBox( hWnd, moji, "BM_ACT_HIT", MB_OK | MB_ICONSTOP );       //MLHIDE
			#endif
			break;
		}
		
		bmFlag = ReadBmDataNum();		// フラグ
		damage = ReadBmDataNum();		// 受伤
		
		// 武器坏れたとき
		if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
			newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
			newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
		}else{
			newGraNo = 0;
		}
		// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
		//BattleMastarCheckBmChat( bmFlag );
		
		// カウンターでない时
		//if( bmFlag & BM_FLAG_COUNTER ){
			// 行动リストをセットする（自分）
		//	BattleSetActList( pActBc[ *enemyId ], BC_ACT_ENEMY_STANDBY_WAIT );
		//}
		// カウンタープラス
		cnt++;
		
		// スキルＩＤ初期化
		skillId = -1;
	}
	
	// 気絶の时
	if( deathFlag == TRUE ){
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_MOVE_DEF );
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_DEAD );
		
	}else{
		// 返回处理
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_B_MOVE_START );
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_MOVE_DEF );
		BattleSetActList( pActBc[ *myIdBak ], BC_ACT_B_MOVE_END );
	}
}

#if 0
// 间接攻击読み込み处理 ******************************************************
void ReadIndirectAttack( int skillId, int teckId, int consumeFp, int weaponId, int *myId, int *myIdBak, int *enemyId )
{
	int sameId, bmFlag, damage, cnt = 0;
	int deathFlag;
	char moji[ 1024 ];
	
	// バトルムービーデータから数字を読み込む
	myId = ReadBmDataNum();		// 自分の番号
	//kind = ReadBmDataNum();		// 飞び道具の种类
	
	// いるかチェック
	if( pActBc[ myId ] == NULL ){
		#ifdef _DEBUG
		sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                          //MLHIDE
		MessageBox( hWnd, moji, "BM_ACT_MSL", MB_OK | MB_ICONSTOP );        //MLHIDE
		#endif
		break;
	}
	
	// 飞び道具フラグ记忆
	( ( BC_YOBI *)pActBc[ myId ]->pYobi )->weaponId = weaponId;
	
	// 行动リストをセットする（自分）
	if( kind & BM_MSL_BOW ){	// 弓矢
		BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
		BattleSetActList( pActBc[ myId ], BC_ACT_BOW );
	}else 
	if( kind & BM_MSL_BOOMERANG ){	// ブーメラン
		BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
		BattleSetActList( pActBc[ myId ], BC_ACT_THROW );
		BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
		BattleSetActList( pActBc[ myId ], BC_ACT_BOOMERANG_CATCH );
	}else 
	if( kind & BM_MSL_KNIFE ){		// ナイフ
		BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
		BattleSetActList( pActBc[ myId ], BC_ACT_THROW );
	}
	
	//enemyCnt = 0; // 敌カウンター初期化
	
	while( 1 ){
		enemyId = ReadBmDataNum();		// 相手の番号
		// 攻击相手がいなくなったら拔ける
		if( enemyId == 0xFF ){ 
			
			break;
		}
		
		//enemyCnt++; // 敌カウンタープラス
		
		// いるかチェック
		if( pActBc[ enemyId ] == NULL ){
			#ifdef _DEBUG
			sprintf( moji, "ＩＤ无效错误。enemyId = %d", enemyId );                   //MLHIDE
			MessageBox( hWnd, moji, "BM_ACT_MSL", MB_OK | MB_ICONSTOP );       //MLHIDE
			#endif
			continue;
		}
		
		bmFlag = ReadBmDataNum();		// フラグ
		damage = ReadBmDataNum();		// 受伤
		
		// 敌リストをセットする
		BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage );
		
		// 行动リストをセットする（相手）
		BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
		
		// 回避じゃないとき
		if( !( bmFlag & BM_FLAG_AVOIDANCE ) ){
			// ＡＫＯ１のとき
			if( bmFlag & BM_FLAG_AKO1 ){
				BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
				// 自分のとき
				if( BattleMyNo == enemyId ){
					BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
				}else{
					BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
					BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
				}
			}else{
				// 受伤が０じゃないとき
				if( damage != 0 ){
					// 通常攻击かクリティカルの时
					if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
						BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
					}
				}
			}
		}
		// 回避の时
		//if( bmFlag & BM_FLAG_AVOIDANCE ){
			//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
		//}else
		
		// 受伤が０じゃないかつ、回避じゃないとき
		if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE ){
			BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
		}
		
		// 気絶の时
		if( bmFlag & BM_FLAG_DEATH ){
			BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
		}
	}
}
#endif

// バトルマスター处理 ***********************************************/
void BattleMaster( ACTION *pAct )
{
	int i;
	char cmd[ 4 ];
	
#ifdef _DEBUG
	char moji[ 256 ];
#endif

	BM_YOBI *pYobiBm = ( BM_YOBI *)pAct->pYobi;
	//static int subActNo = 0; // 现在の行动番号
	static int myId; 		// 自分のＩＤ
	static int myId2; 		// 自分のＩＤ２
	static int myIdBak; 	// 自分のＩＤバックアップ
	static int enemyId; 	// 敌のＩＤ
	static int petId; 		// ペットのＩＤ
	static int bodyGuardId; // 护卫する人のＩＤ
	
	static int reflectId;	// 反射する人のＩＤ
	
	int weaponId;			// 武器ＩＤ
	static danceId;			// 踊り效果ＩＤ
	
	static int bmFlag; 			// 攻击时のフラグ
	int damage; 			// 受伤
	int newGraNo;			// 新グラフィック番号
	
	static int bmFlagBak;	// 攻击时のフラグバックアップ
	static int newGraNoBak;	// 新グラフィック番号バックアップ
	
	//int kind; 				// 种类
	//int enemyCnt; 		// 敌の数
	int cnt = 0; 			// 泛用カウンター
	//int sameId; 			// 同一Ｉｄチェック用
	
	static int skillId; 	// スキル番号
	static int kindId; 		// 种类番号（特别スキル判别用）
	static int techId; 		// テック番号
	int consumeFp;			// 消费フォースポイント
	
	static int itemId; 		// アイテム演出ＩＤ
	int effectSize;			// エフェクトの大きさ
	static int point;		// 回复ポイント
	
	int deathFlag = FALSE; 	// 死んだ时のフラグ
	
	// エフェクトサイズテーブル
	int effectSizeTbl[] = { 3, 3, 3, 2, 2, 2, 1, 1, 1, 0 };
	
#if 0
	static dispCnt = 0;
	// 上キー押した时
	if( VK[ VK_UP ] & KEY_ON_REP ) dispCnt += 10;
	// 下キー押した时
	if( VK[ VK_DOWN ] & KEY_ON_REP ) dispCnt -= 10;
	// リミットチェック
	if( dispCnt < 0 ) dispCnt = 0;
	// 描画
	for( i = 0 ; i < dispCnt ; i++ ){
		StockFontBuffer( 4, 460, FONT_PRIO_FRONT, FONT_KIND_BIG, FONT_PAL_WHITE, "WWWWWWWWWWWWWWWWWWWWWWWWW", 0 ); //MLHIDE
	}
#endif
	
	// ＢＣ登场以下の时返回、または自分がＡＫＯフラグＯＮの时
	if( ( ProcNo != PROC_BATTLE || SubProcNo <= BATTLE_PROC_RECV_MOVIE_DATA ) && MyAkoFlag != TRUE ) return;
	
	// フィールド属性表示
	
	// ムービー取り出す时
	if( ReadBmDataFlag == FALSE ){
		// バトルムービーデータからコマンドを読み込む
		if( ReadBmDataCmd( cmd ) == TRUE ){
			// 文字列が終ったらムービー終了
			// 自分がＡＫＯのとき
			if( MyAkoFlag == TRUE ){
				// 战闘終了演出へ
				SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
				return;
			}
			// クライアントターン进める
			BattleCliTurnNo++;
			// バトルキャラクターデータデータ受信待ちへ
			SubProcNo = BATTLE_PROC_RECV_BC_DATA;
			return;
		}
		// 自分がＡＫＯのとき
		if( MyAkoFlag != TRUE ){
			// ムービー読み込みフラグＯＮ
			ReadBmDataFlag = TRUE;
			// サブ行动番号初期化
			pYobiBm->subActNo = 0; 
			// 行动リスト初期化
			BattleInitList();
		}
		// 一致する文字列を探す
		for( i = 0 ; i < BM_ACT_MAX ; i++ ){
			// 文字列一致したら
			if( strstr( cmd, BattleCmdStr[ i ] ) ){
				// 自分がＡＫＯのとき
				if( MyAkoFlag == TRUE ){
					// チャットプロトコルじゃ无かったら終了
					if( i != BM_ACT_CHT ){
						// 战闘終了演出へ
						SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
						return;
					}
				}
				// コマンド决定
				pAct->actNo = i;
				break;
			}
		}
	}
	
	// ムービーの行动で分岐
	switch( pAct->actNo ){
		
	case BM_ACT_CMB:	// 合体攻击 ****************************************************/
	
		//pBcYobi = ( BC_YOBI *)pActBc[ myId ]->pYobi;
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				pYobiBm->cmbCnt = 0;
				pYobiBm->cmbCntMax = 0;
				point = 0;
				// 相手の番号
				enemyId = ReadBmDataNum();
				
				// いるかチェック
				if( pActBc[ enemyId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。enemyId = %d",enemyId );                  //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_CMB", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// スタンバイ行动（相手）
				BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
				
				// 狙う敌がいなくなるまでループ
				while( 1 ){
				
					// バトルムービーデータから数字を読み込む
					myId = ReadBmDataNum();		// 自分の番号
					
					// 行动リスト初期化
					//pYobi->actListCnt = 0;
					// 攻击侧がいなくなったら拔ける
					if( myId == 0xFF ) break;
					
					// いるかチェック
					if( pActBc[ myId ] == NULL ){
						#ifdef _DEBUG
						sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                      //MLHIDE
						MessageBox( hWnd, moji, "BM_ACT_HIT", MB_OK | MB_ICONSTOP );    //MLHIDE
						#endif
						break;
					}
					
					bmFlag = ReadBmDataNum();		// フラグ
					damage = ReadBmDataNum();		// 受伤
					
					// 武器坏れたとき
					if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
						newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
						newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
					}else{
						newGraNo = 0;
					}
					
					// スキルＩＤ初期化
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = -1;
					
					// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
					//BattleMastarCheckBmChat( bmFlag );
					// 物理反射のとき、物理吸收のとき、物理无效のとき
					if( bmFlag & BM_FLAG_REFLECTION_PHYSICS || bmFlag & BM_FLAG_ABSORB_PHYSICS || bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
						// 敌リストをセットする
						BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
					}else{
						// 受伤プラス
						point += damage;
						// 敌リストをセットする
						BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, point, newGraNo );
					}
						// 物理吸收のとき
					//	if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
						
						
					// マスターに自分のＩＤを教える
					pYobiBm->cmbList[ pYobiBm->cmbCntMax ] = myId;
					// コンボ数プラス
					pYobiBm->cmbCntMax++;
					
					
					// 相手の准备できるまで待つ（自分）
					BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
					
					// 目的地へ走る
					BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
					BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE );
					BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
					
					// マスターに到着通知
					BattleSetActList( pActBc[ myId ], BC_ACT_CMB_STANDBY_OK );
					
					// マスターによしと言われるまで待つ
					BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
					
					// 攻击
					BattleSetActList( pActBc[ myId ], BC_ACT_ATTACK );
					
					// 物理反射のときじゃ无いとき、かつ物理吸收じゃないとき、かつ物理无效じゃないとき
					if( !( bmFlag & BM_FLAG_REFLECTION_PHYSICS ) && !( bmFlag & BM_FLAG_ABSORB_PHYSICS ) && !( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ) ){
						// 强制ヒットストップフラグＯＮ
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->hitStopFlag = TRUE;
					}
					
					// マスターに到着通知
					//BattleSetActList( pActBc[ myId ], BC_ACT_CMB_STANDBY_OK );
					
					// ＡＫＯ１のとき
					if( bmFlag & BM_FLAG_AKO1 ){
						
						// 通常攻击の时
						if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
							
							// 物理反射のとき
							if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
								BattleSetActList( pActBc[ myId ], BC_ACT_AKO1 );		// ＡＫＯ
								// 反射エフェクト
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_NO_WAIT );
								// スタンバイ行动（相手）
								BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
							}
							
							// 物理反射のとき（攻击した侧が飞ぶ）
							if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
								// 攻击する侧が自分（ BattleMyNo )のとき
								if( BattleMyNo == myId ){
									BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
								}else{
									BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
									BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
								}
							}
						}
					}else{
						// 受伤が０じゃないとき
						if( damage != 0 ){
							// 通常攻击かクリティカルの时
							if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
								// 物理无效のとき
								if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
									// 无效エフェクト
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS_NO_WAIT );
									// スタンバイ行动（相手）
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								}else
								// 物理吸收のとき
								if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
									// 吸收エフェクト
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS_NO_WAIT );
									// スタンバイ行动（相手）
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								}else
								// 物理反射のとき
								if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
									// 自分がヒットバック
									BattleSetActList( pActBc[ myId ], BC_ACT_HIT_BACK );
									// 反射エフェクト
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_NO_WAIT );
									// スタンバイ行动（相手）
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								}
							}
						}
					}
					// 回避の时
					//if( bmFlag & BM_FLAG_AVOIDANCE ){
						//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
					//}else
#if 0				
					// 受伤が０じゃないまたは、回避の时
					if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE ){
						// 物理反射のとき
						if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
							// 攻击しに行った侧が反射されたとき
							if( enemyId == myIdBak ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
							}
						}
					}
#endif				

					// 気絶の时
					if( bmFlag & BM_FLAG_DEATH ){
						// 物理反射のとき
						if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
							BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
							BattleSetActList( pActBc[ myId ], BC_ACT_DEAD );
						}else{
							// 返回处理
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
							BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
						}
					}else{
						// 返回处理
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
						BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
					}
					
					
					// 行动リストスタート
					BattleStartActList( pActBc[ myId ] );
					
					// バックアップ
					myIdBak = myId;
				}
				
				// 最后の人だけ强制ヒットストップフラグＯＦＦにする
				( ( BC_YOBI *)pActBc[ myIdBak ]->pYobi )->hitStopFlag = FALSE;
				
				// 物理反射、吸收、无效のとき
				if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS || bmFlag & BM_FLAG_ABSORB_PHYSICS || bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
					// 一つ前の行动をリセットする
					BattleSetActList( pActBc[ enemyId ], BC_ACT_END, ( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->actSetListCnt - 1 );
					
				}else{
					// ＡＫＯ１のとき
					if( bmFlag & BM_FLAG_AKO1 ){
						
						// 通常攻击の时
						if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
							
							// 物理反射のとき
							if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
								BattleSetActList( pActBc[ myId ], BC_ACT_AKO1 );		// ＡＫＯ
								// 反射エフェクト
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_RECOVERY );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_NO_WAIT );
							}else{
								BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );		// ＡＫＯ
							}
							
							// 物理反射のとき（攻击した侧が飞ぶ）
							if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
								// 攻击する侧が自分（ BattleMyNo )のとき
								if( BattleMyNo == myId ){
									BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
								}else{
									BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
									BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
								}
							}else{
								// 自分のとき（攻击された侧が飞ぶ）
								if( BattleMyNo == enemyId ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
								}else{
									BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
								}
							}
						}
					}else{
						// 受伤が０じゃないとき
						if( damage != 0 ){
							// 通常攻击かクリティカルの时
							if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
								// 物理无效のとき
								if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
									// 无效エフェクト
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS_NO_WAIT );
								}else
								// 物理吸收のとき
								if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
									// 吸收エフェクト
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS_NO_WAIT );
								}else
								// 物理反射のとき
								if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
									// 自分がヒットバック
									BattleSetActList( pActBc[ myIdBak ], BC_ACT_HIT_BACK );
									// 反射エフェクト
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_RECOVERY );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_NO_WAIT );
								}else{
									BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
								}
							}
						}
					}
					// 回避の时
					//if( bmFlag & BM_FLAG_AVOIDANCE ){
						//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
					//}else
					
					// 受伤が０じゃないまたは、回避の时
					if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE ){
						// 物理吸收のとき
						if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
						}else
						// 物理反射のとき
						if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
							// 攻击しに行った侧が反射されたとき
							if( enemyId == myIdBak ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
							}
						}else{
							// 自分にカウンターじゃない时（返り讨ちじゃ无いとき）
							if( enemyId != myIdBak ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
							}
						}
					}
					
					// 気絶の时
					if( bmFlag & BM_FLAG_DEATH ){
						// 物理反射のとき
						if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
							// 攻击しに行った侧が反射されたとき
							if( enemyId == myIdBak ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}else{
								deathFlag = TRUE;
							}
						}else{
							// 自分にカウンターじゃないとき
							if( enemyId != myIdBak ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}else{
								deathFlag = TRUE;
							}
						}
					}
#if 0					
					// 気絶の时
					if( deathFlag == TRUE ){
						BattleSetActList( pActBc[ myIdBak ], BC_ACT_MOVE_DEF );
						BattleSetActList( pActBc[ myIdBak ], BC_ACT_DEAD );
						
					}else{
						// 返回处理
						BattleSetActList( pActBc[ myIdBak ], BC_ACT_B_MOVE_START );
						BattleSetActList( pActBc[ myIdBak ], BC_ACT_MOVE_DEF );
						BattleSetActList( pActBc[ myIdBak ], BC_ACT_B_MOVE_END );
					}
#endif
				}
#if 0				
				// ＡＫＯ１のとき
				if( bmFlag & BM_FLAG_AKO1 ){
					
					// 通常攻击の时
					if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
						
						BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );		// ＡＫＯ
						
						// 自分のとき
						//if( BattleMyNo == enemyId ){
						//	BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
						//}else{
						BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
						BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
						//}
					}
				}else{
					// 受伤が０じゃないとき
					if( damage != 0 ){
						// 通常攻击かクリティカルの时
						if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
							BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
						}
					}
				}
				// 回避の时
				//if( bmFlag & BM_FLAG_AVOIDANCE ){
					//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
				//}else
				
				// 受伤が０じゃないまたは、回避の时
				if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE ){
					// カウンターでない、または自分にカウンターじゃない时
					if( !( bmFlag & BM_FLAG_COUNTER ) || enemyId != myIdBak ){
						BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
					}
				}
				
				// 気絶の时
				if( bmFlag & BM_FLAG_DEATH ){
					BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
				}
#endif			
				//１０ヒットコンボなら
				if( pYobiBm->cmbCntMax >= 2 ){
					// Ｘアニメセット
//					set_combo_x_anime();
				}

				pYobiBm->subActNo++; // 次へ
				break;
			
			case 1: // 全员集合チェック
			
				// 終了チェック
				if( pYobiBm->cmbCnt >= pYobiBm->cmbCntMax ){
				
					pYobiBm->cmbCnt = 0;
					pYobiBm->subActNo++; // 次へ
				}
				break;
				
			case 2: // 次々行动スタート
			
				if( pActBc[ pYobiBm->cmbList[ pYobiBm->cmbCnt ] ]->actNo == BC_ACT_STANDBY ){
					// 行动リストスタート
					BattleNextActList( pActBc[ pYobiBm->cmbList[ pYobiBm->cmbCnt ] ] );
				}
				
				// 終了チェック
				if( pYobiBm->cmbCnt >= pYobiBm->cmbCntMax - 1 ){
					// 防御でなければ
					if( !(bmFlag & (BM_FLAG_GUARD | BM_FLAG_SPECIAL_GUARD) ) ){
						//２人以上なら
						if( pYobiBm->cmbCntMax >= 2 ){
							// コンボ数表示セット
							set_combo_num( pYobiBm->cmbCntMax, myIdBak );
						}
					}
					pYobiBm->subActNo++; // 次へ
					break;
				}
				
				break;
				
			case 3: // 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// 自分がいるときかつ、死んでいないとき
					if( pActBc[ enemyId ] != NULL && !( ( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->bcFlag & BC_FLAG_DEATH ) ){
						// デフォルトの方向へ向ける
						ChangeDefDir( pActBc[ enemyId ] );
					}
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				break;
				
		}
		break;
		
		
	case BM_ACT_ESC:	// 逃げる ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                       //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_ESC", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				bmFlag = ReadBmDataNum();		// 成功か失败か
				
				// グラフィックジャンプさせる
				BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
				// グラフィック番号
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_ESCAPE;
				// 逃げる准备
				BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_ESC );
				// 成功なら
				if( bmFlag == TRUE ){
					// ペットのＩＤ
					//int petId = BattleCheckPetId( myId );
					
					// ＳＥ番号记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 211;
					// ＳＥ鸣らす
					BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
					
					BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
					BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_OUT );	// 画面外へ
					// 自分の时
					if( BattleMyNo == myId ){
						BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
					}else{
						BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
					}
				}else{
					BattleSetActList( pActBc[ myId ], BC_ACT_DEAD_2 );			// 倒下
					BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );			// １秒待つ
					BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );	// デフォルトの方向へ向ける
				}
				//BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE );
				//BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					//ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		
		break;
	
	
	case BM_ACT_CLR:	// 退却 ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				while( 1 ){
					// バトルムービーデータから数字を読み込む
					myId = ReadBmDataNum();		// 自分の番号
					// 攻击侧がいなくなったら拔ける
					if( myId == 0xFF ) break;
					
					// いるかチェック
					if( pActBc[ myId ] == NULL ){
						#ifdef _DEBUG
						sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                      //MLHIDE
						MessageBox( hWnd, moji, "BM_ACT_CLR", MB_OK | MB_ICONSTOP );    //MLHIDE
						#endif
						continue;
					}
					
					// グラフィックジャンプさせる
					BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
					// グラフィック番号
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_ESCAPE;
					// 退却准备
					BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_ESC );
					// 退却
					BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_OUT );
					// アクション抹杀
					BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	
					// 行动リストスタート
					BattleStartActList( pActBc[ myId ] );
				}
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( TRUE ) == TRUE ){
					// デフォルトの方向へ向ける
					//ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		
		break;
		
		
	case BM_ACT_SUP:	// スキルアップ ***********************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				while( 1 ){
					// バトルムービーデータから数字を読み込む
					myId = ReadBmDataNum();		// 自分の番号
					// 攻击侧がいなくなったら拔ける
					if( myId == 0xFF ) break;
					
					// いるかチェック
					if( pActBc[ myId ] == NULL ){
						#ifdef _DEBUG
						sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                      //MLHIDE
						MessageBox( hWnd, moji, "BM_ACT_SUP", MB_OK | MB_ICONSTOP );    //MLHIDE
						#endif
						continue;
					}
					
					// グラフィックジャンプさせる
					BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
					// グラフィック番号
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_SKILL_UP;
					
					// ＳＥ番号记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 206;
					// ＳＥ鸣らす
					BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
					// スキルアップ演出
					BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY );
					// 立ちアニメーションにする
					BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
					// 行动リストスタート
					BattleStartActList( pActBc[ myId ] );
				}
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					//ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		
		break;
		
		
	case BM_ACT_PDN:	// 受伤ダウン（踊り专用） ***********************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				// バトルムービーデータから数字を読み込む
				myId = ReadBmDataNum();		// 自分の番号
				
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                       //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_PDN", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					
					break;
				}
				
				
				// グラフィックジャンプさせる
			//	BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
				// グラフィック番号
			//	( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_SKILL_UP;
				
				// ＳＥ番号记忆
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 205;
				// ＳＥ鸣らす
				BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
				// 受伤ダウン缩小BC_ACT_HAPPY
				BattleSetActList( pActBc[ myId ], BC_ACT_DANCE_POW_DOWN_SCALE );
				// 立ちアニメーションにする
				////BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					//ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		
		break;
	
	
	case BM_ACT_POS:	// ポジションチェンジ ********************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				// バトルムービーデータから数字を読み込む
				myId = ReadBmDataNum();		// 自分の番号
				// 攻击侧がいなくなったら拔ける
				if( myId == 0xFF ) break;
				
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                       //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_POS", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					pYobiBm->subActNo++; // 次へ
					break;
				}
				
				{
					// ペットのＩＤ
					int petId = BattleCheckPetId( myId );
					// ＩＤバックアップ
					int idBak = myId;
					// アクションポインタバックアップ
					ACTION *actBak = pActBc[ myId ];
					
					// 自分の时はＭｙＩｄの变更
					if( BattleMyNo == myId ) BattleMyNo = petId;
					
					// 自分
					// デフォルト座标の更新
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->defX = BcPos[ BcPosId[ petId ] ].defX;
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->defY = BcPos[ BcPosId[ petId ] ].defY;
					// スタート位置设定
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->startX = BcPos[ BcPosId[ petId ] ].startX;
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->startY = BcPos[ BcPosId[ petId ] ].startY;
					// ＩＤの更新
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->myId = petId;
					
					// ペットいるとき
					if( pActBc[ petId ] != NULL ){
					//if( pActBc[ petId ] != NULL ){
						// ＩＤの更新
						( ( BC_YOBI *)pActBc[ petId ]->pYobi )->myId = myId;
						// アルティメットじゃないとき
						if( pActBc[ petId ] != NULL && pActBc[ petId ]->actNo != BC_ACT_AKO1 ){
							// デフォルト座标の更新
							( ( BC_YOBI *)pActBc[ petId ]->pYobi )->defX = BcPos[ BcPosId[ myId ] ].defX;
							( ( BC_YOBI *)pActBc[ petId ]->pYobi )->defY = BcPos[ BcPosId[ myId ] ].defY;
							// スタート位置设定
							( ( BC_YOBI *)pActBc[ petId ]->pYobi )->startX = BcPos[ BcPosId[ myId ] ].startX;
							( ( BC_YOBI *)pActBc[ petId ]->pYobi )->startY = BcPos[ BcPosId[ myId ] ].startY;
						}
					}
					
					// アクション入れ替え
					pActBc[ myId ] = pActBc[ petId ];
					pActBc[ petId ] = actBak;
					// ＩＤ入れ替え
					myId = petId;
					petId = idBak;
					
					// 自分
					// ＳＥ番号记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 210;
					// ＳＥ鸣らす
					BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
					// デフォルト位置に移动
					BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
					// 行动リストスタート
					BattleStartActList( pActBc[ myId ] );
					// スピード
					pActBc[ myId ]->speed = 3;
					// アニメーションスピード
					pActBc[ myId ]->anim_speed = 100;
					
					// ペットいるとき
					if( pActBc[ petId ] != NULL && pActBc[ petId ]->actNo != BC_ACT_AKO1 ){
						// デフォルト位置に移动
						BattleSetActList( pActBc[ petId ], BC_ACT_MOVE_DEF );
						// 行动リストスタート
						BattleStartActList( pActBc[ petId ] );
						// 死亡移动中ではないとき
						if( pActBc[ petId ]->speed != 1 ){
							// スピード
							pActBc[ petId ]->speed = 3;
							// アニメーションスピード
							pActBc[ petId ]->anim_speed = 100;
						}
					}
				}
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// ペットのＩＤ
					int petId = BattleCheckPetId( myId );
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					// ペットいるとき
					if( pActBc[ petId ] != NULL && pActBc[ petId ]->anim_no != ANIM_DEAD 
						&& pActBc[ petId ]->actNo != BC_ACT_AKO1 ){
						// デフォルトの方向へ向ける
						ChangeDefDir( pActBc[ petId ] );
					}
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		
		break;
		
	
	case BM_ACT_MON:	// ペット出し入れ ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_MON", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// ペットのＩＤ
				petId = BattleCheckPetId( myId );
				
				// ペットの方向へ向ける
				BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_OTHER_ID_DIR );
				// ペットＩＤ记忆
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->otherId = petId;
				// 敌リストをセットする
				BattleSetEnemyList( pActBc[ myId ], petId, 0, 0 );
				
				// ペット戾すとき
				if( ReadBmDataNum() == 0 ){
					
					// グラフィックジャンプさせる
					BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
					// ウェイト
					//BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					//BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					// グラフィック番号
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_PET_RETURN;
					
					// ペット召唤モーション
					BattleSetActList( pActBc[ myId ], BC_ACT_PET_SUMMON );
					
					
					// ペットがいるとき
					if( pActBc[ petId ] != NULL ){
						// 気絶していたら
						if( ( ( BC_YOBI* )pActBc[ petId ]->pYobi )->bcFlag & BC_FLAG_DEATH ){
							// リストの初期化（ＩＤ指定版、死んでる人も）
							BattleInitListWithDead( petId );
						}
						
						// ＳＥ番号记忆
						( ( BC_YOBI *)pActBc[ petId ]->pYobi )->seNo = 205;
						// ＳＥ鸣らす
						BattleSetActList( pActBc[ petId ], BC_ACT_PLAY_SE );
						// ペット缩小
						BattleSetActList( pActBc[ petId ], BC_ACT_PET_CHANGE );
						// アクション抹杀
						BattleSetActList( pActBc[ petId ], BC_ACT_DEATH_ACT );
					}else{
						#ifdef _DEBUG
						sprintf( moji, "ＩＤ无效错误。petId %d", petId );                      //MLHIDE
						MessageBox( hWnd, moji, "BM_ACT_MON", MB_OK | MB_ICONSTOP );    //MLHIDE
						#endif
					}
				}
				// ペット出すとき
				else{
					// アクションが无い时
					if( pActBc[ petId ] == NULL ){
						pActBc[ petId ] = MakeBattleChar( petId );
					}
					
					ACTION *pAct = pActBc[ petId ];
					// 予备构造体
					BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
					
					ReadBmDataStr( pAct->name, sizeof( pAct->name ) );			// 名称読み込み
					//ReadBmDataStr( pAct->freeName, sizeof( pAct->freeName ) );	// 称号読み込み
					
#ifdef _CG2_NEWGRAPHIC
					pAct->anim_chr_no = getNewGraphicNo(ReadBmDataNum());	// グラフィック番号読み込み
#else
					pAct->anim_chr_no = ReadBmDataNum();	// グラフィック番号読み込み
#endif					
					pAct->level = ReadBmDataNum();	// 等级読み込み
					pAct->hp = ReadBmDataNum();		// ＨＰ読み込み
					pAct->maxHp = ReadBmDataNum();	// 最大ＨＰ読み込み
					pAct->fp = ReadBmDataNum();		// 魔力読み込み
					pAct->maxFp = ReadBmDataNum();	// 最大魔力読み込み
					
					pYobi->bcFlag = ReadBmDataNum();	// フラグ読み込み
					
					// 行动リスト初期化
					pYobi->actListCnt = 0;
					pYobi->actSetListCnt = -1;
					pYobi->actList[ 0 ] = BC_ACT_STANDBY;
					pYobi->actList[ 1 ] = (BC_ACT)-1;
					
					// 初期位置
					pAct->fx = ( float )BcPos[ BcPosId[ petId ] ].defX;
					pAct->fy = ( float )BcPos[ BcPosId[ petId ] ].defY;
					pAct->x = BcPos[ BcPosId[ petId ] ].defX;
					pAct->y = BcPos[ BcPosId[ petId ] ].defY;
					// 初期スケール
					pAct->scaleX = 0;
					pAct->scaleY = 0;
					// 自分のＩＤ记忆
					pYobi->myId = petId;
					
					// 行动番号初期化（アルティメットのとき用）
					pAct->actNo = BC_ACT_STANDBY;
					
					// グラフィックジャンプさせる
					BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
					// ウェイト
					//BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					//BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					// ジャンプグラフィック番号
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_PET_SUMMON;
					//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_GENERAL_CANCEL_BTN_UP;
					
					// ペット召唤モーション
					BattleSetActList( pActBc[ myId ], BC_ACT_PET_SUMMON );
					
					// ＳＥ番号记忆
					( ( BC_YOBI *)pActBc[ petId ]->pYobi )->seNo = 204;
					// ＳＥ鸣らす
					BattleSetActList( pActBc[ petId ], BC_ACT_PLAY_SE );
					// ペット扩大
					BattleSetActList( pActBc[ petId ], BC_ACT_PET_CHANGE2 );
				}
				
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				// 行动リストスタート
				//BattleStartActList( pActBc[ petId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// ペットのアクション抹杀されたとき、またはペットの行动が終わったとき
					if( pActBc[ petId ]== NULL || pActBc[ petId ] !=NULL && ( (BC_YOBI *)pActBc[ petId ]->pYobi )->actFlag == FALSE ){
						// デフォルトの方向へ向ける
						ChangeDefDir( pActBc[ myId ] );
						ChangeDefDir( pActBc[ BattleCheckPetId( myId ) ] );
						//BattleInitList();
						// ムービー読み込みフラグＯＮ
						ReadBmDataFlag = FALSE;
					}
				}
				
			break;
		}
		
		break;
	
	
	case BM_ACT_EQU:	// 装备变更 ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_EQU", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// 画像番号
				// グラフィック番号
#ifdef _CG2_NEWGRAPHIC
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->epuipChangeGraNo = getNewGraphicNo( ReadBmDataNum());
#else
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->epuipChangeGraNo = ReadBmDataNum();
#endif
				
				// グラフィックジャンプさせる
				BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
				// ウェイト
				//BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
				
				// ジャンプグラフィック番号
				//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_BUTTON_EQUIP_UP;
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_WEAPON;
				
				// ＳＥ番号记忆
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 209;
				// ＳＥ鸣らす
				BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
				
				// 装备变更演出
				BattleSetActList( pActBc[ myId ], BC_ACT_EQUIP_CHANGE );
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
	
	
	case BM_ACT_PSN:	// 毒受伤、体力再生 ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				kindId = ReadBmDataNum();		// 种类
				techId = ReadBmDataNum();		// テックＩＤ记忆
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_PSN", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				bmFlag = ReadBmDataNum();		// フラグ
				damage = ReadBmDataNum();		// 受伤
				
				// テックＩＤ记忆
				( ( BC_YOBI * )pActBc[ myId ]->pYobi )->techId = techId;
				
				// 毒のとき
				if( kindId == 0 ){
					// 毒受伤エフェクト
					//BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_RECOVERY );
					// ＳＥ番号记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 273;
					// ＳＥ鸣らす
					BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
					// 回复ポイント表示
					BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP );
					// 受伤アニメーション
					BattleSetActList( pActBc[ myId ], BC_ACT_DAMAGE );
					// 回复ポイント记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage;
					// 文字色记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_RED;
					// 気絶の时
					if( bmFlag & BM_FLAG_DEATH ){
						BattleSetActList( pActBc[ myId ], BC_ACT_DEAD );
						BattleSetActList( pActBc[ myId ], BC_ACT_DEAD_FLAG_ON );
					}
				}else
				// 体力再生のとき
				if( kindId == 1 ){
					// スキルＩＤ记忆
					//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
					// テックＩＤ记忆
					//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = 0;
					// 回复エフェクト
					BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_LP_RECOVER );
					// 回复ポイント表示
					BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
					// 回复ポイント记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage;
					// 文字色记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_GREEN;
				}else
				// 酔いの时（魔力マイナス）
				if( kindId == 2 ){
					// 毒受伤エフェクト
					//BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_RECOVERY );
					// ＳＥ番号记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 274;
					// ＳＥ鸣らす
					BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
					// マイナス魔力ポイント表示
					BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
					// マイナス魔力ポイント记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage;
					// 文字色记忆
					( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_BLUE;
				}
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
		
		
	case BM_ACT_ANR:	// 状态异常自动回复 ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				//kindId = ReadBmDataNum();		// 种类
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_ANR", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// スキルＩＤ教えてもらう
				//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = kindId;
				// 状态异常マーク作成
				BattleSetActList( pActBc[ myId ], BC_ACT_ABNORMAL_OFF );
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
	
	
	case BM_ACT_PUE:	// パラメータアップダウン效果終了 ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				//kindId = ReadBmDataNum();		// 种类
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_ANR", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// スキルＩＤ教えてもらう
				//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = kindId;
				// パラメータアップダウン效果終了
				BattleSetActList( pActBc[ myId ], BC_ACT_PARAMETER_UP_DOWN_OFF );
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
	
	
	case BM_ACT_ACT:	// ２アクションマーク ****************************************************/
		
		// バトルムービーデータから数字を読み込む
		//kindId = ReadBmDataNum();		// 种类
		myId = ReadBmDataNum();		// 自分の番号
		// いるかチェック
		if( pActBc[ myId ] == NULL ){
			#ifdef _DEBUG
			sprintf( moji, "ＩＤ无效错误。myId %d", myId );                           //MLHIDE
			MessageBox( hWnd, moji, "BM_ACT_ACT", MB_OK | MB_ICONSTOP );       //MLHIDE
			#endif
			break;
		}
		
		// ２アクション目に变更
		MakeBattle2Action( pActBc[ myId ], 2 );
		
		// ムービー読み込みフラグＯＮ
		ReadBmDataFlag = FALSE;
		
		break;
	
	
	case BM_ACT_CFP:	// 魔力消费 ****************************************************/
		
		// バトルムービーデータから数字を読み込む
		//kindId = ReadBmDataNum();		// 种类
		myId = ReadBmDataNum();		// 自分の番号
		// いるかチェック
		if( pActBc[ myId ] == NULL ){
			#ifdef _DEBUG
			sprintf( moji, "ＩＤ无效错误。myId %d", myId );                           //MLHIDE
			MessageBox( hWnd, moji, "BM_ACT_CFP", MB_OK | MB_ICONSTOP );       //MLHIDE
			#endif
			break;
		}
		// 消费フォースポイント
		consumeFp = ReadBmDataNum();
		
		// フォースポイントマイナス
		pActBc[ myId ]->fp -= consumeFp;
		// リミットチェック
		if( pActBc[ myId ]->fp < 0 ) pActBc[ myId ]->fp = 0;
		
		// ムービー読み込みフラグＯＮ
		ReadBmDataFlag = FALSE;
		
		break;
		
		
	case BM_ACT_CHT:	// 战闘チャットプロトコル ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				//kindId = ReadBmDataNum();		// 种类
				myId = ReadBmDataNum();		// 自分の番号
				
				{
					int color = ReadBmDataNum();		// 文字の色
					int kind = ReadBmDataNum();			// 文字の种类
					char str[ 256 ];					// 文字列
					
					point = ReadBmDataNum();		// 表示フレーム数
					// Ｂｍデータから文字列を取り出す
					ReadBmDataStr( str, 256 );
					
					// チャット音鸣らすなら
					if( NowChatSound )
					{
						// チャット受信ＳＥ
						play_se( SE_NO_CHAT_ROLL, 320 ,240 );
					}
					// チャットに文字列を送る
					StockChatBufferLine( str, color, kind );
				}
				
				// 谁かが喋るとき
				if( myId < 20 ){
					// いるかチェック
					if( pActBc[ myId ] == NULL ){
						//#ifdef _DEBUG
						//sprintf( moji, "ＩＤ无效错误。myId %d", myId );
						//MessageBox( hWnd, moji, "BM_ACT_CHT", MB_OK | MB_ICONSTOP );
						//#endif
						pYobiBm->subActNo++; // 次へ
						break;
					}else{
						
						// ロードされて无いとサイズが分からないため、ＢＭＰをロードする
#ifdef PUK2
						GetBmpSize( pActBc[ myId ]->bmpNo );
#else
						LoadBmp( pActBc[ myId ]->bmpNo );
#endif
						// 吹き出し作成
						//MakeBattleJumpDisp( pActBc[ myId ], CG_B_STR_LEAVE );
						//MakeBattleJumpDisp( pActBc[ myId ], CG_ICON_FUKIDASI );
						MakeBattleGraDisp( pActBc[ myId ], -30, -SpriteInfo[ pActBc[ myId ]->bmpNo ].height, point, CG_ICON_FUKIDASI );
					}
				}
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
				
				// 表示时间マイナス
				point--;
				if( point <= 0 ){
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
	
	
	case BM_ACT_PNO:	// ペット嫌がる ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				//kindId = ReadBmDataNum();		// 种类
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_PNO", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// ジャンプグラフィック番号
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_PET_NO;
				// グラフィックジャンプさせる
				BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
				// ペット嫌がる
				BattleSetActList( pActBc[ myId ], BC_ACT_PET_NO );
				
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
	
	
	case BM_ACT_PLV:	// ペット逃走 ****************************************************/
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
			
				// バトルムービーデータから数字を読み込む
				//kindId = ReadBmDataNum();		// 种类
				myId = ReadBmDataNum();		// 自分の番号
				// いるかチェック
				if( pActBc[ myId ] == NULL ){
					#ifdef _DEBUG
					sprintf( moji, "ＩＤ无效错误。myId %d", myId );                         //MLHIDE
					MessageBox( hWnd, moji, "BM_ACT_PLV", MB_OK | MB_ICONSTOP );     //MLHIDE
					#endif
					break;
				}
				
				// 移动先指定
				//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveX = BcPos[ BcPosId[ 15 ] ].defX + ( BcPos[ BcPosId[ 5 ] ].defX - BcPos[ BcPosId[ 15 ] ].defX ) / 2;
				//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveY = BcPos[ BcPosId[ 15 ] ].defY + ( BcPos[ BcPosId[ 5 ] ].defY - BcPos[ BcPosId[ 15 ] ].defY ) / 2;
				// 目的地に移动する
				//BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
				//BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_POINT );
				//BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
				
				// クライアントで演出を统一するためランダムシードする。
				srand( myId );
				
				// 目的地决定
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->startX = BcPos[ Rnd( 0, 19 ) ].startX;
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->startY = BcPos[ Rnd( 0, 19 ) ].startY;
				
				// 逃げる准备
				BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_ESC );
				
				// ジャンプグラフィック番号
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_LEAVE;
				// グラフィックジャンプさせる
				BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
				
				// ＳＥ番号记忆
				( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 211;
				// ＳＥ鸣らす
				BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
				BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_OUT );	// 退场
				BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
		
				// 行动リストスタート
				BattleStartActList( pActBc[ myId ] );
				
				pYobiBm->subActNo++; // 次へ
			
			break;
			
			case 1:	// 終了チェック
			
				// 終了チェック
				if( BattleCheckActEnd( FALSE ) == TRUE ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pActBc[ myId ] );
					//BattleInitList();
					// ムービー読み込みフラグＯＮ
					ReadBmDataFlag = FALSE;
				}
				
			break;
		}
		break;
	
	
	case BM_ACT_CBC:	// ＢＣデータ变更 ****************************************************/
		
		
		myId = ReadBmDataNum();		// 自分の番号
		// いるかチェック
		if( pActBc[ myId ] == NULL ){
			#ifdef _DEBUG
			sprintf( moji, "ＩＤ无效错误。myId %d", myId );                           //MLHIDE
			MessageBox( hWnd, moji, "BM_ACT_CBC", MB_OK | MB_ICONSTOP );       //MLHIDE
			#endif
			break;
		}
		
		// プレイヤーグラフィック番号読み込み
#ifdef _CG2_NEWGRAPHIC
		pActBc[ myId ]->anim_chr_no = getNewGraphicNo( ReadBmDataNum());
#else
		pActBc[ myId ]->anim_chr_no = ReadBmDataNum();
#endif
		pActBc[ myId ]->hp = ReadBmDataNum();		// ＨＰ読み込み
		pActBc[ myId ]->maxHp = ReadBmDataNum();	// 最大ＨＰ読み込み
		pActBc[ myId ]->fp = ReadBmDataNum();		// 魔力読み込み
		pActBc[ myId ]->maxFp = ReadBmDataNum();	// 最大魔力読み込み
		
		// ムービー読み込みフラグＯＮ
		ReadBmDataFlag = FALSE;
		
		break;
	
	
	case BM_ACT_SKL:	// スキル ****************************************************/
	
		//pBcYobi = ( BC_YOBI *)pActBc[ myId ]->pYobi;
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				// スキル番号を読み込む
				i = ReadBmDataNum();
				// １００の桁以上はスキルＩＤ
				skillId = i / 100;
				// １０の桁は特别スキルＩＤ
				kindId = ( i % 100 ) / 10;
				
				
				// １００の桁以上はスキルＩＤ
				//skillId = i / 10;
				
				
				// 下一桁はテックＩＤ
				techId = i % 10;
				
				// 消费フォースポイント
				consumeFp = ReadBmDataNum();
				
				// スキル番号で派生
				switch( skillId ){
				
					// 通常攻击 *********************************************************/
					case B_SKILL_NORMAL_ATTACK:
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
					
						// 武器ＩＤで分岐
						switch( weaponId ){
							
							case B_WEAPON_DIRECT:		// 直接攻击武器
							
								// 直接攻击読み込み处理
								ReadDirectAttack( skillId, techId, consumeFp, &myId, &myIdBak, &enemyId, &bodyGuardId );
							
								// 行动リストスタート
								BattleStartActList( pActBc[ myIdBak ] );
								
								break;
							
							case B_WEAPON_KNIFE:		// ナイフ
							case B_WEAPON_BOOMERANG:	// ブーメラン
							case B_WEAPON_BOW:			// 弓矢
							
								// バトルムービーデータから数字を読み込む
								myId = ReadBmDataNum();		// 自分の番号
								//kind = ReadBmDataNum();		// 飞び道具の种类
								
								// スキルＩＤ记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
								// テックＩＤ记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
								
								// いるかチェック
								if( pActBc[ myId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
									MessageBox( hWnd, moji, "BM_ACT_MSL", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								// 飞び道具フラグ记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->weaponId = weaponId;
								
								// 行动リストをセットする（自分）
								if( weaponId == B_WEAPON_KNIFE ){		// ナイフ
									BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
									BattleSetActList( pActBc[ myId ], BC_ACT_THROW );
								}else 
								if( weaponId == B_WEAPON_BOOMERANG ){	// ブーメラン
									BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
									BattleSetActList( pActBc[ myId ], BC_ACT_THROW );
									//BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
									BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_OTHER_POINTER );
									BattleSetActList( pActBc[ myId ], BC_ACT_BOOMERANG_CATCH );
								}else 
								if( weaponId == B_WEAPON_BOW ){	// 弓矢
									BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
									BattleSetActList( pActBc[ myId ], BC_ACT_BOW );
								}
								//enemyCnt = 0; // 敌カウンター初期化
								
								while( 1 ){
									enemyId = ReadBmDataNum();		// 相手の番号
									// 攻击相手がいなくなったら拔ける
									if( enemyId == 0xFF ){ 
										
										break;
									}
									
									//enemyCnt++; // 敌カウンタープラス
									
									// いるかチェック
									if( pActBc[ enemyId ] == NULL ){
										#ifdef _DEBUG
										sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
										MessageBox( hWnd, moji, "BM_ACT_MSL", MB_OK | MB_ICONSTOP ); //MLHIDE
										#endif
										continue;
									}
									
									bmFlag = ReadBmDataNum();		// フラグ
									damage = ReadBmDataNum();		// 受伤
									
									// 武器坏れたとき
									if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
										newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
										newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
									}else{
										newGraNo = 0;
									}
									
									// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
									//BattleMastarCheckBmChat( bmFlag );
									
									// 敌リストをセットする
									BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
									
									// 行动リストをセットする（相手）
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
									
									// 回避じゃないとき
									if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
										// ＡＫＯ１のとき
										if( bmFlag & BM_FLAG_AKO1 ){
											BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
											// 自分のとき
											if( BattleMyNo == enemyId ){
												BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
											}else{
												BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
												BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
											}
										}else{
											// 受伤が０じゃないとき
											if( damage != 0 ){
												// 通常攻击かクリティカルの时
												if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
													// 物理无效のとき
													if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
														// 无效エフェクト
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
														BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS );
													}else
													// 物理吸收のとき
													if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
														// 吸收エフェクト
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
														BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS );
													}else
													// 物理反射のとき
													if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
														// 反射エフェクト
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
														BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
													}else{
														BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
													}
												}
											}
										}
									}
									// 回避の时
									//if( bmFlag & BM_FLAG_AVOIDANCE ){
										//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
									//}else
									
									// 受伤が０じゃないかつ、回避じゃないとき
									if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
										// 物理无效のとき
										if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
										}else
										// 物理吸收のとき
										if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
										}else
										// 物理反射のとき
										if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
										}else{
											BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
											// 身交わしのとき
											if( bmFlag & BM_FLAG_MIKAWASHI ){
												BattleSetActList( pActBc[ enemyId ], BC_ACT_ZANZOU_OFF );
											}
										}
									}
									// 気絶の时
									if( bmFlag & BM_FLAG_DEATH ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
									}
									
								}
								
								// 行动リストスタート
								BattleStartActList( pActBc[ myId ] );
							
								break;
						}
						
						pYobiBm->subActNo++; // 次へ
					
					break;
				
					// 连続攻击 *********************************************************/
					case B_SKILL_DOUBLE_ATTACK:
					// エッジ *********************************************************/
					case B_SKILL_EDGE:
					// バーストアタックエッジ *********************************************************/
					case B_SKILL_BURST:
					// フォースカット *********************************************************/
					case B_SKILL_FORCECUT:
					// 防御ブレイク *********************************************************/
					case B_SKILL_GUARDBRAKE:
					// 盗む *********************************************************/
					case B_SKILL_STEAL:
					// 状态付加攻击 *********************************************************/
					case B_SKILL_ADD_BAD_CONDITION_POISON:		// 毒状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_SLEEP:		// 眠り状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_STONE:		// 石化状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_INEBRIETY:	// 酔い状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_CONFUSION:	// 混乱状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_FORGET:		// 忘却状态付加攻击
					
					case B_SKILL_ATTACK_BLOOD:					// 吸血攻击
					case B_SKILL_ENERGYDRAIN:					// エナジードレイン
					case B_SKILL_BREAKWEAPON:					// 装备破坏攻击
					case B_SKILL_ATTACKGOLD:					// ゴールド攻击
					
					case B_SKILL_RANDOM_SHOT:					// 乱れうち（弓术士）
					case B_SKILL_ASSASSIN:						// 暗杀（忍者）
					case B_SKILL_KAKURAN:						// 搅乱
#ifdef _TEST_TECH_YUK
					case B_SKILL_RANBU:					// 乱舞
					case B_SKILL_POISON_ARROW:			// 毒矢
					case B_SKILL_PICKPOCKET:			// 掏る
#endif /* _TEST_TECH_YUK */
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
					
						// 超例外处理、盗む时は飞び道具装备してても直接攻击扱い
#ifdef _TEST_TECH_YUK
						if( skillId == B_SKILL_STEAL || skillId == B_SKILL_PICKPOCKET) weaponId = B_WEAPON_DIRECT;
#else
						if( skillId == B_SKILL_STEAL ) weaponId = B_WEAPON_DIRECT;
#endif /* _TEST_TECH_YUK */
					
						// 武器ＩＤで分岐
						switch( weaponId ){
							
							case B_WEAPON_DIRECT:		// 直接攻击武器
							
								// 直接攻击読み込み处理
								ReadDirectAttack( skillId, techId, consumeFp, &myId, &myIdBak, &enemyId, &bodyGuardId );
							
								// 行动リストスタート
								BattleStartActList( pActBc[ myIdBak ] );
								
								break;
							
							case B_WEAPON_KNIFE:		// ナイフ
							case B_WEAPON_BOOMERANG:	// ブーメラン
							case B_WEAPON_BOW:			// 弓矢
							
								// バトルムービーデータから数字を読み込む
								myId = ReadBmDataNum();		// 自分の番号
								//kind = ReadBmDataNum();		// 飞び道具の种类
								
								// スキルＩＤ记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
								// テックＩＤ记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
								// 消费フォースポイント记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
								
								// いるかチェック
								if( pActBc[ myId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
									MessageBox( hWnd, moji, "BM_ACT_MSL", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								// 飞び道具フラグ记忆
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->weaponId = weaponId;
								
								// デフォルトの方向へ向ける
								BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
								
								// 连続攻击のとき *************************************************
								if( skillId == B_SKILL_DOUBLE_ATTACK ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
									// 残像フラグＯＮ
									//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->actZanzouFlag = TRUE;
								}else
								// エッジ *********************************************************/
								if( skillId == B_SKILL_EDGE ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// バーストアタック *********************************************************/
								if( skillId == B_SKILL_BURST ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// フォースカット *********************************************************/
								if( skillId == B_SKILL_FORCECUT ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// 状态付加攻击 *********************************************************/
								if( skillId >= B_SKILL_ADD_BAD_CONDITION_POISON && skillId <= B_SKILL_ADD_BAD_CONDITION_FORGET ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// 吸血攻击 *********************************************************/
								if( skillId == B_SKILL_ATTACK_BLOOD ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// エナジードレイン *********************************************************/
								if( skillId == B_SKILL_ENERGYDRAIN ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// 装备破坏攻击 *********************************************************/
								if( skillId == B_SKILL_BREAKWEAPON ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// GOLD攻击 *********************************************************/
								if( skillId == B_SKILL_ATTACKGOLD ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// 吸血攻击 *********************************************************/
								//if( skillId == B_SKILL_ATTACK_BLOOD ){
									// 行动リストをセットする（自分）
								//	BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								//}else
								// 防御ブレイク *********************************************************/
								if( skillId == B_SKILL_GUARDBRAKE ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}else
								// 乱れ打ち（弓术士）********************************************************/
								if( skillId == B_SKILL_RANDOM_SHOT ){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}
#ifdef _TEST_TECH_YUK
								else
								if( skillId == B_SKILL_RANBU){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
									// 残像フラグＯＮ
									( ( BC_YOBI *)pActBc[ myId ]->pYobi )->actZanzouFlag = TRUE;
								}
								else
								if( skillId == B_SKILL_POISON_ARROW){
									// 行动リストをセットする（自分）
									BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
								}
#endif /* _TEST_TECH_YUK */
								
								// 魔力マイナス
								BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
								
								// 行动リストをセットする（自分）
								if( weaponId == B_WEAPON_KNIFE ){		// ナイフ
									BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
									BattleSetActList( pActBc[ myId ], BC_ACT_THROW );
								}else 
								if( weaponId == B_WEAPON_BOOMERANG ){	// ブーメラン
									BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
									BattleSetActList( pActBc[ myId ], BC_ACT_THROW );
									//BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
									BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_OTHER_POINTER );
									BattleSetActList( pActBc[ myId ], BC_ACT_BOOMERANG_CATCH );
								}else 
								if( weaponId == B_WEAPON_BOW ){	// 弓矢
									BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
									BattleSetActList( pActBc[ myId ], BC_ACT_BOW );
								}
								//enemyCnt = 0; // 敌カウンター初期化
								
								while( 1 ){
									enemyId = ReadBmDataNum();		// 相手の番号
									// 攻击相手がいなくなったら拔ける
									if( enemyId == 0xFF ){ 
										
										break;
									}
									
									//enemyCnt++; // 敌カウンタープラス
									
									// いるかチェック
									if( pActBc[ enemyId ] == NULL ){
										#ifdef _DEBUG
										sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
										MessageBox( hWnd, moji, "BM_ACT_MSL", MB_OK | MB_ICONSTOP ); //MLHIDE
										#endif
										continue;
									}
									
									bmFlag = ReadBmDataNum();		// フラグ
									damage = ReadBmDataNum();		// 受伤
									
									// 武器坏れたとき
									if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
										newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
										newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
									}else{
										newGraNo = 0;
									}
									
									// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
									//BattleMastarCheckBmChat( bmFlag );
									
									// 敌リストをセットする
									BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
									
									// 行动リストをセットする（相手）
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
									
									// 回避じゃないとき
									if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
										// ＡＫＯ１のとき
										if( bmFlag & BM_FLAG_AKO1 ){
											BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
											// 自分のとき
											if( BattleMyNo == enemyId ){
												BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
											}else{
												BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
												BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
											}
										}else{
											// 受伤が０じゃないとき
											if( damage != 0 ){
												// 通常攻击かクリティカルの时
												if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
													// 物理无效のとき
													if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
														// 无效エフェクト
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
														BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS );
													}else
													// 物理吸收のとき
													if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
														// 吸收エフェクト
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
														BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS );
													}else
													// 物理反射のとき
													if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
														// 反射エフェクト
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS_WAIT );
														BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
													}else{
													
														// 乱れ打ち（弓术士）じゃ无い时、かつ受伤が０じゃない时
														if( skillId == B_SKILL_RANDOM_SHOT ){
															// 気絶の时
															if( bmFlag & BM_FLAG_DEATH ){
																BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
															}else{
																// ヒットバック处理（乱れ打ち用）
																BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK_BOW );
															}
														}else{
															BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
														}
													}
												}
											}
										}
									}
									// 回避の时
									//if( bmFlag & BM_FLAG_AVOIDANCE ){
										//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
									//}else
									
									// 受伤が０じゃないとき（受伤食らったとき）または、回避の时
									if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
										// 物理无效のとき
										if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
										}else
										// 物理吸收のとき
										if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
										}else
										// 物理反射のとき
										if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
										}else{
											// 乱れ打ち（弓术士）じゃ无い时、かつ受伤が０じゃない时
											//if( skillId == B_SKILL_RANDOM_SHOT && ( bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ) ){
											if( skillId == B_SKILL_RANDOM_SHOT ){
												// 気絶の时
												if( bmFlag & BM_FLAG_DEATH ){
													BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
												}
#if 0
												else{
													
													// 回避の时
													if( bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
														// 直前の行动リストを初期化する
					 									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->actSetListCnt--;
														BattleSetActList( pActBc[ enemyId ], BC_ACT_END );
					 									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->actSetListCnt--;
					 								}
												}
#endif
											}else{
												BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
												// 身交わしのとき
												if( bmFlag & BM_FLAG_MIKAWASHI ){
													BattleSetActList( pActBc[ enemyId ], BC_ACT_ZANZOU_OFF );
												}
											}
										}
									}
									// 気絶の时
									if( bmFlag & BM_FLAG_DEATH ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
									}
									
								}
								
								// 行动リストスタート
								BattleStartActList( pActBc[ myId ] );
							
								break;
						}
						
						pYobiBm->subActNo++; // 次へ
						
						break;
						
						
					// 气孔弹 *************************************************************/
					case B_SKILL_KIKOH:
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_KIKOH", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 飞び道具フラグ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->weaponId = weaponId;
						
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
						// 气孔弹発射
						BattleSetActList( pActBc[ myId ], BC_ACT_SKILL_SPIRACLE_SHOT_START );
						
						//enemyCnt = 0; // 敌カウンター初期化
						
						while( 1 ){
							enemyId = ReadBmDataNum();		// 相手の番号
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ){ 
								
								break;
							}
							
							//enemyCnt++; // 敌カウンタープラス
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_KIKOH", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								continue;
							}
							
							bmFlag = ReadBmDataNum();		// フラグ
							damage = ReadBmDataNum();		// 受伤
							
							// 武器坏れたとき
							if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
								newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
								newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
							}else{
								newGraNo = 0;
							}
							
							// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
							//BattleMastarCheckBmChat( bmFlag );
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
							
							// 行动リストをセットする（相手）
							BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
							
							// 回避じゃないとき
							if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
								// ＡＫＯ１のとき
								if( bmFlag & BM_FLAG_AKO1 ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
									// 自分のとき
									if( BattleMyNo == enemyId ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// 通常攻击かクリティカルの时
										if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
											// 物理无效のとき
											if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
												// 无效エフェクト
												//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS );
											}else
											// 物理吸收のとき
											if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
												// 吸收エフェクト
												//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS );
											}else
											// 物理反射のとき
											if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
												// 反射エフェクト
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
											}else{
												BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
											}
										}
									}
								}
							}
							// 回避の时
							//if( bmFlag & BM_FLAG_AVOIDANCE ){
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
							//}else
							
							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
								// 物理无效のとき
								if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
								}else
								// 物理吸收のとき
								if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
								}else
								// 物理反射のとき
								if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
								}else{
									BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
									// 身交わしのとき
									if( bmFlag & BM_FLAG_MIKAWASHI ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ZANZOU_OFF );
									}
								}
							}
							
							// 気絶の时
							if( bmFlag & BM_FLAG_DEATH ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}
						}
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;

#ifdef _TEST_TECH_YUK
					case B_SKILL_ATTACKALL:				// 全体攻击
					case B_SKILL_AXEBOMBER:
					case B_SKILL_THROWITEM:

						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_ATTACKALL", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 飞び道具フラグ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->weaponId = weaponId;
						
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );

						if( skillId == B_SKILL_ATTACKALL ){
							// 行动リストをセットする（自分）
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SUPERSKILL_GENERATE );
/*							// 全体攻击准备移动
							BattleSetActList( pActBc[ myId ], BC_ACT_ATTACKALL_MOVE );
							// 0.5秒待つ
							BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05 );
							// 残像フラグON
							BattleSetActList( pActBc[ myId ], BC_ACT_ZANZOU_ON );
							// 全体攻击
							BattleSetActList( pActBc[ myId ], BC_ACT_ATTACKALL_HIT );
							// 0.5秒待つ
							BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05 );*/
							// 点灭
							BattleSetActList( pActBc[ myId ], BC_ACT_TENMETU_ON);
							// 0.5秒待つ
							BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05 );
							// 隠す
							BattleSetActList( pActBc[ myId ], BC_ACT_HIDE);
							// 全体に分身を飞ばす
							BattleSetActList( pActBc[ myId ], BC_ACT_ATTACKALL_ILLUSION );
						}
						else if( skillId == B_SKILL_AXEBOMBER){
							// 行动リストをセットする（自分）
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SUPERSKILL_GENERATE );
							// 移动
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START);
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE);
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END);
						}
						else if( skillId == B_SKILL_THROWITEM){
							// 行动リストをセットする（自分）
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
							if( techId == 9){
								BattleSetActList( pActBc[ myId ], BC_ACT_ITEM_THROW);
//								BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05);
								BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND);
							}
						}

						while( 1 ){
							enemyId = ReadBmDataNum();		// 相手の番号
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ){ 
								
								break;
							}
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_ATTACKALL", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								continue;
							}
							
							bmFlag = ReadBmDataNum();		// フラグ
							damage = ReadBmDataNum();		// 受伤
							
							// 武器坏れたとき
							if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
								newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
								newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
							}else{
								newGraNo = 0;
							}

							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );

							// 护卫のとき *********************************************************
							if( bmFlag & BM_FLAG_BODYGUARD && skillId == B_SKILL_AXEBOMBER){
								// 护卫のＩＤ読み込み
								bodyGuardId = ReadBmDataNum();
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ myId ], bodyGuardId, bmFlag, damage, newGraNo );

								// 次の敌リストへ
								BattleSetActList( pActBc[ myId ], BC_ACT_NEXT_ENEMY_LIST );
								// 行动リストをセットする（自分）
								BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
								// 护卫の人をスタートさせる
								BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
								// いったん停止
								BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
								// 行动リストをセットする（自分）
								BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );

								// 护卫侧
								// 行动リストをセットする
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_STANDBY );

								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ bodyGuardId ], enemyId, bmFlag, damage );
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ bodyGuardId ], enemyId, bmFlag, damage );

								// 相手のＩＤ记忆
								( ( BC_YOBI *)pActBc[ bodyGuardId ]->pYobi )->otherId = enemyId;
								// 相手の方向へ向ける
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_CHANGE_OTHER_ID_DIR );
								// ＳＥ番号记忆
								( ( BC_YOBI *)pActBc[ bodyGuardId ]->pYobi )->seNo = 252;
								// ＳＥ鸣らす
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_PLAY_SE );
								// ジャンプグラフィック番号
								( ( BC_YOBI *)pActBc[ bodyGuardId ]->pYobi )->jumpGraNo = CG_B_STR_SURPRISAL;
								// グラフィック表示
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_GRA_DISP );
								// ０．５秒待つ
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_WAIT_05 );
								// 残像フラグＯＮ
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_ZANZOU_ON );
								// 护卫する人のところまで移动
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_B_MOVE_START );
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_MOVE_NEAR );

								// 护卫される人をこっちに向ける
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_CHANGE_MY_ID_DIR );
								// 护卫される人の行动をスタート
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_ENEMY_NEXT_ACT_LIST );

								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_B_MOVE_END );
								// 残像フラグＯＦＦ
								BattleSetActList( pActBc[ bodyGuardId ], BC_ACT_ZANZOU_OFF );

								// 行动リストをセットする
								BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								// 护卫される人突き飞ばされる处理
								BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK2 );
								// 相手を次の行动へ
								BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
								// 次の敌リストへ
								BattleSetActList( pActBc[ enemyId ], BC_ACT_NEXT_ENEMY_LIST );
								// 立ちアニメーションにする
								BattleSetActList( pActBc[ enemyId ], BC_ACT_ANIM_STAND );
								// 行动リストをセットする
								BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY2 );
								// デフォルト位置に移动する
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );

								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ enemyId ], myId, bmFlag, damage );

								// 敌を护卫の人に入れ替え
								enemyId = bodyGuardId;

							}

							// 行动リストをセットする（相手）
							BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );

							if( skillId == B_SKILL_THROWITEM && techId < 9){
								BattleSetActList( pActBc[ myId ], BC_ACT_ITEM_THROW);
								BattleSetActList( pActBc[ myId ], BC_ACT_NEXT_ENEMY_LIST);
								BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND);
							}

							// 回避じゃないとき
							if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
								// ＡＫＯ１のとき
								if( bmFlag & BM_FLAG_AKO1 ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
									// 护卫のとき（后始末处理）***************************************
									if( bmFlag & BM_FLAG_BODYGUARD && skillId == B_SKILL_AXEBOMBER){
										// 相手を次の行动へ
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
										// 次の敌リストへ
										BattleSetActList( pActBc[ enemyId ], BC_ACT_NEXT_ENEMY_LIST );
									}
									// 自分のとき
									if( BattleMyNo == enemyId ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// 通常攻击かクリティカルの时
										if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
											// 物理无效のとき
											if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
												// 无效エフェクト
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS );
											}else
											// 物理吸收のとき
											if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
												// 吸收エフェクト
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS );
											}else
											// 物理反射のとき
											if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
												// 反射エフェクト
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS );
											}else{
												BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
											}
										}
									}
								}
							}

							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
								// 物理无效のとき
								if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS ){
								}else
								// 物理吸收のとき
								if( bmFlag & BM_FLAG_ABSORB_PHYSICS ){
								}else
								// 物理反射のとき
								if( bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
								}else{
									BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
									// 身交わしのとき
									if( bmFlag & BM_FLAG_MIKAWASHI ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ZANZOU_OFF );
									}
								}
							}
							
							// 気絶の时
							if( bmFlag & BM_FLAG_DEATH ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}
						}

						if( skillId == B_SKILL_ATTACKALL ){
/*
							// 残像フラグOFF
							BattleSetActList( pActBc[ myId ], BC_ACT_ZANZOU_OFF );

							// 返回处理
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
							BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
*/
						}
						else if( skillId == B_SKILL_AXEBOMBER){
							// 残像フラグON
							BattleSetActList( pActBc[ myId ], BC_ACT_ZANZOU_ON );
							// アックスボンバー
							BattleSetActList( pActBc[ myId ], BC_ACT_AXEBOMBER );
							// 残像フラグOFF
							BattleSetActList( pActBc[ myId ], BC_ACT_ZANZOU_OFF );

							// 护卫のとき（后始末处理）***************************************
							if( bmFlag & BM_FLAG_BODYGUARD ){

								// 护卫しに行って、相手の攻击がミスの时
								if( damage == 0 ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1 );
								}
								// 相手を次の行动へ
								BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
								// 次の敌リストへ
								BattleSetActList( pActBc[ enemyId ], BC_ACT_NEXT_ENEMY_LIST );
								// 物理无效のとき、物理吸收のとき、 物理反射のとき
								if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS 
									|| bmFlag & BM_FLAG_ABSORB_PHYSICS 
									|| bmFlag & BM_FLAG_REFLECTION_PHYSICS ){
									// デフォルト位置に移动する
									BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
								}
								// 気絶じゃない时
								if( !( bmFlag & BM_FLAG_DEATH ) ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_DEF_DIR );
								}
								// 护卫しに行って、相手の攻击がミスの时
								if( damage == 0 ){
									//BattleSetActList( pActBc[ *enemyId ], BC_ACT_WAIT_2 );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
								}

							}

							// 返回处理
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
							BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
						}
						else if( skillId == B_SKILL_THROWITEM){
//							BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05);
							BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND);
						}

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ

						break;
#endif /* _TEST_TECH_YUK */

					// 体力回复魔法 *************************************************************/
					case B_SKILL_LP_INCREASE_SORO:
					case B_SKILL_LP_INCREASE_AREA:
					case B_SKILL_LP_INCREASE_SIDE:
					
					// サクリファイス ***********************************************************/
					case B_SKILL_SACRIFICE:
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// いろいろ初期化
						pYobiBm->cmbCnt = 0;
						pYobiBm->cmbCntMax = 0;
						point = 0;
					
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_LP_INCREASE", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 相手がいたら
						//if( cnt > 0 ){
							// 行动リストをセットする（自分）
							BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						//}
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// 行动リストをセットする（自分）
						//BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_ITEM_GENERATE );
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// ＳＥ番号记忆
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 261;
						// ＳＥ鸣らす
						//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// サクリファイスの时
						if( skillId == B_SKILL_SACRIFICE ){
							// 魔法発生
							//BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
							// 立ちアニメーションにする
							//BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						
							// ここで発生演出を切り替える
							// エフェクト番号セット
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_l_start_big + effectSizeTbl[ techId ];
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_hadou;
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_sakuri;
							
							// 移动先指定
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveX = BcPos[ BcPosId[ 15 ] ].defX + ( BcPos[ BcPosId[ 5 ] ].defX - BcPos[ BcPosId[ 15 ] ].defX ) / 2;
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveY = BcPos[ BcPosId[ 15 ] ].defY + ( BcPos[ BcPosId[ 5 ] ].defY - BcPos[ BcPosId[ 15 ] ].defY ) / 2;
							// 目的地に移动する
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START_2 );
							BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_POINT );
							BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
							
							// 立ちアニメーションにする
							BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
							
							// サクリファイスエフェクト
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SACRIFICE );
							// 次々に分身を飞ばす
							BattleSetActList( pActBc[ myId ], BC_ACT_SACRIFICE );
							// 攻击する侧が自分（ BattleMyNo )のとき
							if( BattleMyNo == myId ){
								BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
							}else{
								BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
								BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
							}
							
							// 行动开始
							//BattleSetActList( pActBc[ myId ], BC_ACT_ITEM_RECOVERY );
						}else{
							// 魔法発生
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
							// 立ちアニメーションにする
							BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
							// エフェクト番号セット
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_l_start_big + effectSizeTbl[ techId ];
						}
						
						// サクリファイスの时
						//if( skillId == B_SKILL_SACRIFICE ){
							// フラグＯＮ
						//	BattleSetActList( pActBc[ myId ], BC_ACT_DEAD_FLAG_ON );
							// 体力を０にする
						//	BattleSetActList( pActBc[ myId ], BC_ACT_LP_0 );
							// 死亡
						//	BattleSetActList( pActBc[ myId ], BC_ACT_DEAD );
						//}
						
						// エフェクトサイズ
						//effectSize = ReadBmDataNum();
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_LP_INCREASE", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							
							bmFlag = ReadBmDataNum();		// フラグ
							damage = ReadBmDataNum();		// ポイントの読み込み
							
							// 魔法反射のとき
							if( bmFlag & BM_FLAG_REFLECTION_MAGIC ){
								// 反射する人のＩＤ
								reflectId = enemyId;
								// 合计受伤计算
								point += damage;
								// フラグバックアップ
								bmFlagBak = bmFlag;
								
								// 死亡フラグだけ落とす
								bmFlag &= ~BM_FLAG_DEATH;
								
								// 反射する人を登録
								pYobiBm->cmbList[ pYobiBm->cmbCntMax ] = enemyId;
								// コンボ数プラス
								pYobiBm->cmbCntMax++;
							}
							// カウントプラス
							//cnt++;
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage );
							// 敌リストをセットする
							//BattleSetEnemyList( pActBc[ myId ], enemyId, , 0 );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
							
							// 回复エフェクト
							BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_LP_INCREASE );
							
							// 魔法无效のとき
							if( bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
								// 无效エフェクト
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INCREASE );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_MAGIC );
							}else
							// 魔法吸收のとき
							if( bmFlag & BM_FLAG_ABSORB_MAGIC ){
								// 吸收エフェクト
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INCREASE );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_MAGIC );
								// 回复ポイント表示
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP );
								// 受伤アニメーション
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DAMAGE );
								// 回复ポイント记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
								// 文字色记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_RED;
								// 気絶の时
								if( bmFlag & BM_FLAG_DEATH ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD_FLAG_ON );
								}
							}else
							// 魔法反射のとき
							if( bmFlag & BM_FLAG_REFLECTION_MAGIC ){
								// 反射エフェクト
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INCREASE );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_MAGIC );
							}else{
								// 回复ポイント表示
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
								// 回复ポイント记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
								// 文字色记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
							}
						}
						
						// 相手がいたら
						//if( cnt > 0 ){
							// アイテム回复处理
							//BattleSetActList( pActBc[ myId ], BC_ACT_ITEM_INCREASE );
						//}
						
						// 魔法反射のとき
						if( point != 0 ){
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ reflectId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ reflectId ]->pYobi )->techId = techId;
							
							// エフェクト番号学习
							//( ( BC_YOBI *)pActBc[ reflectId ]->pYobi )->attackEffectNo = ( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo;							
							
							// 反射フラグだけ落とす
							bmFlagBak &= ~BM_FLAG_REFLECTION_MAGIC;
							
							// 反射するまでの一时休憩行动
							BattleSetActList( pActBc[ reflectId ], BC_ACT_STANDBY2 );
							// 反射する相手に魔法攻击のエフェクトを出す
							BattleSetActList( pActBc[ reflectId ], BC_ACT_EFFECT_REFLECTION_MAGIC_GENERATE );
							
							// 反射された侧の行动
							// 行动リストをセットする（相手）
							BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
							// 回复エフェクト
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_LP_INCREASE );
							// 回复ポイント表示
							BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
							// 回复ポイント记忆
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = point;
							// 文字色记忆
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_GREEN;
							
#if 0							
							// 回避じゃないとき
							if( !( bmFlagBak & BM_FLAG_AVOIDANCE ) ){
								// ＡＫＯ１のとき
								if( bmFlagBak & BM_FLAG_AKO1 ){
									BattleSetActList( pActBc[ myId ], BC_ACT_AKO1 );
									// 自分のとき
									if( BattleMyNo == myId ){
										BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// 通常攻击かクリティカルの时
										if( bmFlagBak & BM_FLAG_NORMAL || bmFlagBak & BM_FLAG_CRITICAL ){
											BattleSetActList( pActBc[ myId ], BC_ACT_HIT_BACK );
										}
									}
								}
							}
							// 回避の时
							//if( bmFlagBak & BM_FLAG_AVOIDANCE ){
								//BattleSetActList( pActBc[ myId ], BC_ACT_HIT_BACK );
							//}else
							
							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlagBak & BM_FLAG_AVOIDANCE ){
								BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
							}
							
							// 気絶の时
							if( bmFlagBak & BM_FLAG_DEATH ){
								BattleSetActList( pActBc[ myId ], BC_ACT_DEAD );
							}
#endif
						}
						
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo = 2; // 次へ
						
						break;
				
				
					// 攻击魔法 ******************************************************
					case B_SKILL_MAGICATTACK_SORO_EARTH:
					case B_SKILL_MAGICATTACK_SORO_WATER:
					case B_SKILL_MAGICATTACK_SORO_FIRE:
					case B_SKILL_MAGICATTACK_SORO_WIND:
					
					case B_SKILL_MAGICATTACK_AREA_EARTH:
					case B_SKILL_MAGICATTACK_AREA_WATER:
					case B_SKILL_MAGICATTACK_AREA_FIRE:
					case B_SKILL_MAGICATTACK_AREA_WIND:
					
					case B_SKILL_MAGICATTACK_SIDE_EARTH:
					case B_SKILL_MAGICATTACK_SIDE_WATER:
					case B_SKILL_MAGICATTACK_SIDE_FIRE:
					case B_SKILL_MAGICATTACK_SIDE_WIND:
					
					// ドレイン ******************************************************
					case B_SKILL_DRAIN:
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// いろいろ初期化
						pYobiBm->cmbCnt = 0;
						pYobiBm->cmbCntMax = 0;
						point = 0;
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// ＳＥ番号记忆
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 259;
						// ＳＥ鸣らす
						//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// 攻击魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						
						// ここで発生演出を切り替える
						// エフェクト番号セット
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_a_start_big + effectSizeTbl[ techId ];
						
						// 攻击魔法作成
						//BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_HIT_MAGIC_SINGLE );
						
						// 攻击エフェクト番号セット
						// 单体と周围のとき（地）
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo = SPR_bigstone1;// + Rnd( 0, 2 );
						// 地のとき
						if( skillId == B_SKILL_MAGICATTACK_SORO_EARTH
							|| skillId == B_SKILL_MAGICATTACK_AREA_EARTH
							|| skillId == B_SKILL_MAGICATTACK_SIDE_EARTH ){
							
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo = SPR_bigstone1 + effectSizeTbl[ techId ] * 3;
						}else
						// 水のとき
						if( skillId == B_SKILL_MAGICATTACK_SORO_WATER
							|| skillId == B_SKILL_MAGICATTACK_AREA_WATER
							|| skillId == B_SKILL_MAGICATTACK_SIDE_WATER ){
							
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo = SPR_bigice1 + effectSizeTbl[ techId ] * 3;
						}else
						// 火のとき
						if( skillId == B_SKILL_MAGICATTACK_SORO_FIRE
							|| skillId == B_SKILL_MAGICATTACK_AREA_FIRE
							|| skillId == B_SKILL_MAGICATTACK_SIDE_FIRE ){
							
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo = SPR_bigfire1 + effectSizeTbl[ techId ] * 3;
						}else
						// 风のとき
						if( skillId == B_SKILL_MAGICATTACK_SORO_WIND
							|| skillId == B_SKILL_MAGICATTACK_AREA_WIND
							|| skillId == B_SKILL_MAGICATTACK_SIDE_WIND ){
							
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo = SPR_bigwind1 + effectSizeTbl[ techId ];
						}else
						// ドレインのとき
						if( skillId == B_SKILL_DRAIN ){
							// エフェクトのサイズは统一
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo = SPR_dorein_big1 + effectSizeTbl[ techId ];// + Rnd( 0, 2 );
						}
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ
							damage = ReadBmDataNum();		// 受伤
							
							// 武器坏れたとき
							if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
								newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
								newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
							}else{
								newGraNo = 0;
							}
							// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック）
							//BattleMastarCheckBmChat( bmFlag );
							
							// 魔法反射のとき
							if( bmFlag & BM_FLAG_REFLECTION_MAGIC ){
								// 反射する人のＩＤ
								reflectId = enemyId;
								// 合计受伤计算
								point += damage;
								// フラグバックアップ
								bmFlagBak = bmFlag;
								// 新グラフィック番号バックアップ
								newGraNoBak = newGraNo;
								
								// フラグ落とす
								bmFlag &= ~BM_FLAG_DEATH;
								bmFlag &= ~BM_FLAG_INJURY;
								bmFlag &= ~BM_FLAG_MY_WEPON_BREAK;
								bmFlag &= ~BM_FLAG_MY_WEPON_BROKEN;
								bmFlag &= ~BM_FLAG_OTHER_WEPON_BREAK;
								bmFlag &= ~BM_FLAG_OTHER_WEPON_BROKEN;
								
								// 反射する人を登録
								pYobiBm->cmbList[ pYobiBm->cmbCntMax ] = enemyId;
								// コンボ数プラス
								pYobiBm->cmbCntMax++;
							}
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
							
							// 行动リストをセットする（相手）
							BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
							
							// 自分じゃないとき
							//if( myId != enemyId ){
								// 行动リストをセットする（相手）
							//	BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
							//}
							
							// 回避じゃないとき
							if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
								// ＡＫＯ１のとき
								if( bmFlag & BM_FLAG_AKO1 && !( bmFlag & BM_FLAG_REFLECTION_MAGIC ) ){
#if 1
									// ドレインのとき
									if( skillId == B_SKILL_DRAIN ){
										// マジック防御かスペシャル防御
										if( bmFlag & BM_FLAG_MAGIC_GUARD || bmFlag & BM_FLAG_SPECIAL_GUARD ){
											// 防御アニメーション
											BattleSetActList( pActBc[ enemyId ], BC_ACT_GUARD );
											// 一秒待つ
											//BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1 );
										}else{
											// 受伤アニメーション
										//	BattleSetActList( pActBc[ enemyId ], BC_ACT_DAMAGE );
										}
										
										BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
										
										// 自分じゃないとき
										if( myId != enemyId ){
											// 敌リストをセットする
											BattleSetEnemyList( pActBc[ enemyId ], myId, bmFlag, damage );
											// 相手の准备できるまで待つ（自分）
											BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_STANDBY_WAIT );
										}
										// 相手を次の行动へ
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
										
										// 自分じゃないとき
										if( myId != enemyId ){
											// 行动リストをセットする（回复侧）
											BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
										}else{
											// 立ちアニメーションにする
											BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
										}
										// 気絶の时かつ、自分のとき
										if( bmFlag & BM_FLAG_DEATH && myId == enemyId ){
										}else{
											// 回复エフェクト
											BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_DRAIN_2 );
											// ポイント表示
											BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
											// 回复ポイント记忆
											( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage / 2;
											// 文字色记忆
											( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_GREEN;
										}
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
									}
#endif										
									// 自分のとき
									if( BattleMyNo == enemyId ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// 通常攻击かクリティカルの时
										if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
											// 魔法无效のとき
											if( bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
												// 无效エフェクト
												//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INCREASE );
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_MAGIC );
											}else
											// 魔法吸收のとき
											if( bmFlag & BM_FLAG_ABSORB_MAGIC ){
												// 吸收エフェクト
												//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INCREASE );
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_MAGIC );
												
											}else
											// 魔法反射のとき
											if( bmFlag & BM_FLAG_REFLECTION_MAGIC ){
												// 反射エフェクト
												//BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INCREASE );
												BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_MAGIC );
											}else{
												// ドレインのとき
												if( skillId == B_SKILL_DRAIN ){
													// マジック防御かスペシャル防御
													if( bmFlag & BM_FLAG_MAGIC_GUARD || bmFlag & BM_FLAG_SPECIAL_GUARD ){
														// 防御アニメーション
														BattleSetActList( pActBc[ enemyId ], BC_ACT_GUARD );
														// 一秒待つ
														//BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1 );
													}else{
														// 受伤アニメーション
														BattleSetActList( pActBc[ enemyId ], BC_ACT_DAMAGE );
														// 気絶の时かつ、自分のとき
														//if( bmFlag & BM_FLAG_DEATH ){
														//	BattleSetActList( pActBc[ enemyId ], BC_ACT_SLOW_FLAG_OFF );
														//}
													}
													// 自分じゃないとき
													if( myId != enemyId ){
														// 敌リストをセットする
														BattleSetEnemyList( pActBc[ enemyId ], myId, bmFlag, damage );
														// 相手の准备できるまで待つ（自分）
														BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_STANDBY_WAIT );
													}
													// 相手を次の行动へ
													BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
													
													// 自分じゃないとき
													if( myId != enemyId ){
														// 行动リストをセットする（回复侧）
														BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
													}else{
														// 立ちアニメーションにする
														BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
													}
													// 気絶の时かつ、自分のとき
													if( bmFlag & BM_FLAG_DEATH && myId == enemyId ){
													}else{
														// 回复エフェクト
														BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_DRAIN_2 );
														// ポイント表示
														BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
														// 回复ポイント记忆
														( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage / 2;
														// 文字色记忆
														( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_GREEN;
													}
												}else{
													BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
												}
											}
										}
									}
								}
							}
							// 回避の时
							//if( bmFlag & BM_FLAG_AVOIDANCE ){
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
							//}else
							
							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
								// 物理无效のとき
								if( bmFlag & BM_FLAG_INEFFECTIVE_MAGIC ){
								}else
								// 物理吸收のとき
								if( bmFlag & BM_FLAG_ABSORB_MAGIC ){
								}else
								// 物理反射のとき
								if( bmFlag & BM_FLAG_REFLECTION_MAGIC ){
								}else{
									// ドレインのとき
									if( skillId == B_SKILL_DRAIN ){
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
										// 身交わしのとき
										if( bmFlag & BM_FLAG_MIKAWASHI ){
											BattleSetActList( pActBc[ enemyId ], BC_ACT_ZANZOU_OFF );
										}
									}
								}
							}
							
							// 気絶の时
							if( bmFlag & BM_FLAG_DEATH && !( bmFlag & BM_FLAG_REFLECTION_MAGIC ) ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}
						}
						
						// 魔法反射のとき
						if( point != 0 ){
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ reflectId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ reflectId ]->pYobi )->techId = techId;
							
							// エフェクト番号学习
							( ( BC_YOBI *)pActBc[ reflectId ]->pYobi )->attackEffectNo = ( ( BC_YOBI *)pActBc[ myId ]->pYobi )->attackEffectNo;							
							
							// 反射フラグだけ落とす
							bmFlagBak &= ~BM_FLAG_REFLECTION_MAGIC;
							
							// 敌リストをセットする（魔法をかけた侧のＩＤ）
							//BattleSetEnemyList( pActBc[ reflectId ], myId, bmFlagBak, point );
							
							// 反射するまでの一时休憩行动
							BattleSetActList( pActBc[ reflectId ], BC_ACT_STANDBY2 );
							// 反射する相手に魔法攻击のエフェクトを出す
							BattleSetActList( pActBc[ reflectId ], BC_ACT_EFFECT_REFLECTION_MAGIC_GENERATE );
							
							// 反射された侧の行动
							// 行动リストをセットする（相手）
							BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
							
							// 回避じゃないとき
							if( !( bmFlagBak & BM_FLAG_AVOIDANCE ) ){
								// ＡＫＯ１のとき
								if( bmFlagBak & BM_FLAG_AKO1 ){
									BattleSetActList( pActBc[ myId ], BC_ACT_AKO1 );
									// 自分のとき
									if( BattleMyNo == myId ){
										BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// ドレインのとき
										if( skillId == B_SKILL_DRAIN ){
											// マジック防御かスペシャル防御
											if( bmFlag & BM_FLAG_MAGIC_GUARD || bmFlag & BM_FLAG_SPECIAL_GUARD ){
												// 防御アニメーション
												BattleSetActList( pActBc[ myId ], BC_ACT_GUARD );
												// 一秒待つ
												//BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
											}else{
												// 受伤アニメーション
												BattleSetActList( pActBc[ myId ], BC_ACT_DAMAGE );
											}
											// 受伤アニメーション
											//BattleSetActList( pActBc[ myId ], BC_ACT_DAMAGE );
											// 立ちアニメーションにする
											BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
											
											// 気絶の时
											if( bmFlagBak & BM_FLAG_DEATH ){
											}else{
												// 吸い取りエフェクト
												BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_DRAIN_2 );
												// ポイント表示
												BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
												// 回复ポイント记忆
												( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage / 2;
												// 文字色记忆
												( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_GREEN;
											}
										}else
										// 通常攻击かクリティカルの时
										if( bmFlagBak & BM_FLAG_NORMAL || bmFlagBak & BM_FLAG_CRITICAL ){
											BattleSetActList( pActBc[ myId ], BC_ACT_HIT_BACK );
										}
									}
								}
							}
							// 回避の时
							//if( bmFlagBak & BM_FLAG_AVOIDANCE ){
								//BattleSetActList( pActBc[ myId ], BC_ACT_HIT_BACK );
							//}else
							
							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlagBak & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
								// ドレインのとき
								if( skillId == B_SKILL_DRAIN ){
								}else{
									BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF );
									// 身交わしのとき
									if( bmFlag & BM_FLAG_MIKAWASHI ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ZANZOU_OFF );
									}
								}
							}
							
							// 気絶の时
							if( bmFlagBak & BM_FLAG_DEATH ){
								BattleSetActList( pActBc[ myId ], BC_ACT_DEAD );
							}
						}
						
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo = 2; // 次へ
						
						break;


					// 反射、吸收、无效 ******************************************************
					case B_SKILL_REFLECTION_PHYSICS:	// 物理反射魔法
					case B_SKILL_REFLECTION_MAGIC:		// 魔法反射魔法
					
					case B_SKILL_ABSORB_PHYSICS:		// 物理吸收魔法
					case B_SKILL_ABSORB_MAGIC:			// 魔法吸收魔法
					
					case B_SKILL_INEFFECTIVE_PHYSICS:	// 物理无效魔法
					case B_SKILL_INEFFECTIVE_MAGIC:		// 魔法无效魔法
					
					// 体力再生（单体、周辺、片侧） ******************************************************
					case B_SKILL_LP_RECOVER_SORO:		// LP再生单体魔法
					case B_SKILL_LP_RECOVER_AREA:		// LP再生周辺魔法
					case B_SKILL_LP_RECOVER_SIDE:		// LP再生片侧魔法
					
					// 状态异常魔法（单体、周辺、片侧） *********************************************
					case B_SKILL_ABNORMAL_SORO_POISON:		// 毒状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_SLEEP:		// 眠り状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_STONE:		// 石化状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_INEBRIETY:	// 酔い状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_CONFUSION:	// 混乱状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_FORGET:		// 忘却状态异常单体魔法
					
					case B_SKILL_ABNORMAL_AREA_POISON:		// 毒状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_SLEEP:		// 眠り状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_STONE:		// 石化状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_INEBRIETY:	// 酔い状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_CONFUSION:	// 混乱状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_FORGET:		// 忘却状态异常周辺魔法
					
					case B_SKILL_ABNORMAL_SIDE_POISON:		// 毒状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_SLEEP:		// 眠り状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_STONE:		// 石化状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_INEBRIETY:	// 酔い状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_CONFUSION:	// 混乱状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_FORGET:		// 忘却状态异常片侧魔法
					
					// 状态异常回复魔法 **************************************************************
					case B_SKILL_STATUS_RECOVER:			// 状态异常回复魔法
					
					// 属性反転魔法 **************************************************************
					case B_SKILL_REVERSE_TYPE:			// 属性反転魔法
					
					// 気絶回复魔法 **************************************************************
					case B_SKILL_REVIVE:			// 気絶回复魔法
					
					// 即死 **************************************************************
					case B_SKILL_TERROR:			// 即死魔法
					
					// 攻击、防御、アップダウン ******************************************
//					case B_SKILL_DEF_UP:			// 防御力アップ
//					case B_SKILL_DEF_DOWN:			// 防御力ダウン
//					case B_SKILL_ATK_UP:			// 攻击力アップ
//					case B_SKILL_ATK_DOWN:			// 攻击力ダウン
//					case B_SKILL_AGL_UP:			// すばやさアップ
//					case B_SKILL_AGL_DOWN:			// すばやさダウン
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "反射、吸収、無効、体力再生、Ｓ異常、Ｓ異常回復、属性反転、気絶回復", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						//デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// ここで発生演出を切り替える
						// エフェクト番号セット
						// スキルＩＤで分岐
						//if( skillId == B_SKILL_REVERSE_TYPE ){
							// ＳＥ番号记忆
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 261;
							// ＳＥ鸣らす
							//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						//	( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_zokuhan2_big + effectSizeTbl[ techId ];
						//}else
						if( skillId == B_SKILL_TERROR ){
							// ＳＥ番号记忆
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 261;
							// ＳＥ鸣らす
							//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_a_start_big + effectSizeTbl[ techId ];
						}else
						if( ( skillId >= B_SKILL_LP_RECOVER_SORO && skillId <= B_SKILL_LP_RECOVER_SIDE )
							|| skillId == B_SKILL_REVIVE ){
							// ＳＥ番号记忆
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 261;
							// ＳＥ鸣らす
							//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_l_start_big + effectSizeTbl[ techId ];
						}else{
							// ＳＥ番号记忆
							//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 260;
							// ＳＥ鸣らす
							//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_s_start_big + effectSizeTbl[ techId ];
						}
						
						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0/*damage*/ );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
							
							// 行动リストをセットする（相手）
							//BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
							
							// 物理、魔法（反射、吸收、无效）のとき
							if( skillId >= B_SKILL_REFLECTION_PHYSICS && skillId <= B_SKILL_INEFFECTIVE_MAGIC ){
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_DEFENSE_MAGIC );
							}else
							// 体力再生のとき
							if( skillId >= B_SKILL_LP_RECOVER_SORO && skillId <= B_SKILL_LP_RECOVER_SIDE ){
								// 回复エフェクト（旧エフェクト）
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_RECOVERY );
							}else
							// 状态异常のとき
							if( skillId >= B_SKILL_ABNORMAL_SORO_POISON && skillId <= B_SKILL_ABNORMAL_SIDE_FORGET ){
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABNORMAL_STATUS );
								// 成功したとき
								if( bmFlag & BM_FLAG_SUCCESS ){
									// スキルＩＤ教えてもらう
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
									// 状态异常マーク作成
									BattleSetActList( pActBc[ enemyId ], BC_ACT_ABNORMAL_ON );
									
								}
							}
							else
							// 攻击、防御、アップダウンのとき
//							if( skillId >= B_SKILL_DEF_UP && skillId <= B_SKILL_ATK_DOWN )
							if( skillId >= B_SKILL_DEF_UP && skillId <= B_SKILL_AGL_DOWN )
							{
								// 回复エフェクト
//								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABNORMAL_STATUS );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_PARAMETER );
								// 成功したとき
								if( bmFlag & BM_FLAG_SUCCESS )
								{
									// スキルＩＤ教えてもらう
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
									// 状态异常マーク作成
									BattleSetActList( pActBc[ enemyId ], BC_ACT_PARAMETER_UP_DOWN_ON );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1 );									
								}
							}
							else
							// 状态异常回复のとき
							if( skillId == B_SKILL_STATUS_RECOVER ){
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_STATUS_RECOVER );
								// 成功したとき
								if( bmFlag & BM_FLAG_SUCCESS ){
									// 状态异常マーク抹杀
									BattleSetActList( pActBc[ enemyId ], BC_ACT_ABNORMAL_OFF );
								}
							}else
							// 属性反転魔法
							if( skillId == B_SKILL_REVERSE_TYPE ){
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REVERSE_TYPE );
								// 成功したとき
								if( bmFlag & BM_FLAG_SUCCESS ){
									// 属性反転エフェクト発生
									BattleSetActList( pActBc[ enemyId ], BC_ACT_REVERSE_TYPE_ON );
								}
							}else
							// 即死の时
							if( skillId == B_SKILL_TERROR ){
								// 即死エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_TERROR );
								// 成功したとき
								if( bmFlag & BM_FLAG_SUCCESS ){
									// フラグＯＮ
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD_FLAG_ON );
									// 体力を０にする
									BattleSetActList( pActBc[ enemyId ], BC_ACT_LP_0 );
									// 死亡
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
								}
							}else
							// 気絶回复魔法
							if( skillId == B_SKILL_REVIVE ){
								// 回复ポイント読み込み
								damage = ReadBmDataNum();		// 受伤
								
								// デフォルトの场所にいないとき（死んで归ってるとき）
						//		if( pActBc[ enemyId ]->fx != ( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->defX
						//			|| pActBc[ enemyId ]->fy != ( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->defY ){
								if( 1 ){	
									// リストの初期化（ＩＤ指定版、死んでる人も）
									BattleInitListWithDead( enemyId );
									// 待机にする
									pActBc[ enemyId ]->actNo = BC_ACT_STANDBY;
									// ＳＥ番号记忆
									//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 280;
									// ＳＥ鸣らす
									//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
									// 気絶回复魔法エフェクト発生
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REVIVE );
									// 気絶フラグＯＦＦ
								//	( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->bcFlag &= ~BC_FLAG_DEATH;
									// 成功したとき
									if( bmFlag & BM_FLAG_SUCCESS ){
										// スピード
										pActBc[ enemyId ]->speed = 8;
										// 気絶フラグＯＦＦ
										BattleSetActList( pActBc[ enemyId ], BC_ACT_REVIVE );
										// 回复ポイント表示
										BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
										// 回复ポイント记忆
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
										// 文字色记忆
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
									}else{
										// 失败したらやっぱり、死亡フラグ立てる
								//		BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD_FLAG_ON );
									}
									
									// デフォルト位置に移动
								//	BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
									// デフォルトの方向に向ける
									BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_DEF_DIR );
									
								}else{
									// ＳＥ番号记忆
									//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 280;
									// ＳＥ鸣らす
									//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
									// 気絶回复魔法エフェクト発生
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REVIVE );
									// 成功したとき
									if( bmFlag & BM_FLAG_SUCCESS ){
										// 気絶フラグＯＦＦ
										BattleSetActList( pActBc[ enemyId ], BC_ACT_REVIVE );
										// 回复ポイント表示
										BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
										// 回复ポイント记忆
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
										// 文字色记忆
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
									}
								}
							}
						}
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
					
					
					// 属性优遇魔法 **************************************************************
					case B_SKILL_TREAT_TYPE_EARTH:		// 地属性优遇魔法
					case B_SKILL_TREAT_TYPE_WATER:		// 水属性优遇魔法
					case B_SKILL_TREAT_TYPE_FIRE:		// 火属性优遇魔法
					case B_SKILL_TREAT_TYPE_WIND:		// 风属性优遇魔法
					// 静寂
					case B_SKILL_SILENCE:				// 静寂
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "反射、吸収、無効、体力再生、Ｓ異常、Ｓ異常回復", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// 例外处理（静寂されて失败する时）
						// バトルムービーデータから数字を読み込む
						bmFlag = ReadBmDataNum();		// 成功失败フラグ
						// 成功した时
						if( bmFlag != 255 ){
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->enemySetListCnt++;
						}
						// 例外处理ここまで
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// ＳＥ番号记忆
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 260;
						// ＳＥ鸣らす
						//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						
						// ここで発生演出を切り替える
						// エフェクト番号セット
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_s_start_big + effectSizeTbl[ techId ];
						
						// 属性优遇エフェクト作成
						//BattleSetActList( pActBc[ myId ], BC_ACT_TREAT_TYPE_ON );
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
					
			
					// 精神统一 **************************************************************
					case B_SKILL_CONSENTRATION:		// 精神统一
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						damage = ReadBmDataNum();		// 受伤
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "精神统一", MB_OK | MB_ICONSTOP );         //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						//BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// ＳＥ番号记忆
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 261;
						// ＳＥ鸣らす
						//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						
						// ここで発生演出を切り替える
						// エフェクト番号セット
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_l_start_big + effectSizeTbl[ techId ];
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_sei_big + effectSizeTbl[ techId ];
						
						// 敌リストをセットする
						BattleSetEnemyList( pActBc[ myId ], myId, 0, damage );
						
						
						// 回复エフェクト
						//BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_CONSENTRATION );
						
						// ＳＥ番号记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 256;
						// ＳＥ鸣らす
						BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// 回复ポイント表示
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
						// 回复ポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->point = damage;
						// 文字色记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->color = FONT_PAL_GREEN;
						
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
					
					
					// 自爆 **************************************************************
					case B_SKILL_BOMB:			// 自爆
					
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "反射、吸収、無効、体力再生、Ｓ異常、Ｓ異常回復、属性反転、気絶回復", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
						// 目的地に移动する
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START_2 );
						BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_POINT );
						//BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE );
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
						// 自分が右グループの时
						if( myId < 10 ){
							// 移动先指定
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveX = BcPos[ BcPosId[ 10 ] ].defX + ( BcPos[ BcPosId[ 15 ] ].defX - BcPos[ BcPosId[ 10 ] ].defX ) / 2;
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveY = BcPos[ BcPosId[ 10 ] ].defY + ( BcPos[ BcPosId[ 15 ] ].defY - BcPos[ BcPosId[ 10 ] ].defY ) / 2;
						}
						// 自分が左グループの时
						else{
							// 移动先指定
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveX = BcPos[ BcPosId[ 5 ] ].defX + ( BcPos[ BcPosId[ 0 ] ].defX - BcPos[ BcPosId[ 5 ] ].defX ) / 2;
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveY = BcPos[ BcPosId[ 5 ] ].defY + ( BcPos[ BcPosId[ 0 ] ].defY - BcPos[ BcPosId[ 5 ] ].defY ) / 2;
						}
						
						// ＳＥ番号记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 288;
						// ＳＥ鸣らす
						BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// 膨れる
						BattleSetActList( pActBc[ myId ], BC_ACT_SCALE_2 );
						// 爆発エフェクト作成
						//BattleSetActList( pActBc[ myId ], BC_ACT_SCALE_2 );
						
						// ちょっと待つ
						BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05 );
						// 自分一时消える（ＨＩＤＥ）
						BattleSetActList( pActBc[ myId ], BC_ACT_HIDE );
						// 全敌の行动开始
						BattleSetActList( pActBc[ myId ], BC_ACT_BOM_START );
						// ちょっと待つ
						BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
						// 地震フラグＯＦＦ
						BattleSetActList( pActBc[ myId ], BC_ACT_QUAKE_OFF );
						// 攻击する侧が自分（ BattleMyNo )のとき
						if( BattleMyNo == myId ){
							BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
						}else{
							BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
							BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
						}
						// 自分完全抹杀
						//BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );
						
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							
							bmFlag = ReadBmDataNum();		// フラグ
							damage = ReadBmDataNum();		// 受伤
							
							// 武器坏れたとき
							if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
								newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
								newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
							}else{
								newGraNo = 0;
							}
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
							
							// 行动リストをセットする（相手）
							//BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_OTHER_ID_DIR_2 );
							// 目的ＩＤ记忆
							//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->otherId = myId;
							
							
							// 回避じゃないとき
							if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
								// ＡＫＯ１のとき
								if( bmFlag & BM_FLAG_AKO1 ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
									// 自分のとき
									if( BattleMyNo == enemyId ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// 通常攻击かクリティカルの时
										if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
											BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
										}
									}
								}
							}
							
							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
								// デフォルト位置に移动
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
							}
							
							// 気絶の时
							if( bmFlag & BM_FLAG_DEATH && !( bmFlag & BM_FLAG_REFLECTION_MAGIC ) ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}
						}
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
						
						
						
					// 地震 **************************************************************
					case B_SKILL_EARTHQUAKE:			// 地震
					
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "地震", MB_OK | MB_ICONSTOP );           //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
						
						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						// エフェクト番号セット
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_a_start_big + effectSizeTbl[ techId ];
						//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_sei_big + effectSizeTbl[ techId ];
						
						// ＳＥ番号记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 5;
						// ＳＥ鸣らす
						BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						
						// 地震フラグＯＮ
						BattleSetActList( pActBc[ myId ], BC_ACT_QUAKE_ON );
						// 烟エフェクト
						BattleSetActList( pActBc[ myId ], BC_ACT_FOG_EFFECT );
						// ちょっと待つ
					//	BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					//	BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					//	BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					//	BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
					//	BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05 );
						// 地震受伤处理
						BattleSetActList( pActBc[ myId ], BC_ACT_BOM_START );
						// 地震フラグＯＦＦ
						BattleSetActList( pActBc[ myId ], BC_ACT_QUAKE_OFF );
						
#if 0						
						// 目的地に移动する
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START_2 );
						BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_POINT );
						//BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE );
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
						// 自分が右グループの时
						if( myId < 10 ){
							// 移动先指定
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveX = BcPos[ BcPosId[ 10 ] ].defX + ( BcPos[ BcPosId[ 15 ] ].defX - BcPos[ BcPosId[ 10 ] ].defX ) / 2;
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveY = BcPos[ BcPosId[ 10 ] ].defY + ( BcPos[ BcPosId[ 15 ] ].defY - BcPos[ BcPosId[ 10 ] ].defY ) / 2;
						}
						// 自分が左グループの时
						else{
							// 移动先指定
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveX = BcPos[ BcPosId[ 5 ] ].defX + ( BcPos[ BcPosId[ 0 ] ].defX - BcPos[ BcPosId[ 5 ] ].defX ) / 2;
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->moveY = BcPos[ BcPosId[ 5 ] ].defY + ( BcPos[ BcPosId[ 0 ] ].defY - BcPos[ BcPosId[ 5 ] ].defY ) / 2;
						}
						
						// ＳＥ番号记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 288;
						// ＳＥ鸣らす
						BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
						// 膨れる
						BattleSetActList( pActBc[ myId ], BC_ACT_SCALE_2 );
						// 爆発エフェクト作成
						//BattleSetActList( pActBc[ myId ], BC_ACT_SCALE_2 );
						
						// ちょっと待つ
					//	BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_05 );
						// 自分一时消える（ＨＩＤＥ）
					//	BattleSetActList( pActBc[ myId ], BC_ACT_HIDE );
						// 全敌の行动开始
						BattleSetActList( pActBc[ myId ], BC_ACT_BOM_START );
						// ちょっと待つ
						BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1 );
						// 地震フラグＯＦＦ
						BattleSetActList( pActBc[ myId ], BC_ACT_QUAKE_OFF );
						// 攻击する侧が自分（ BattleMyNo )のとき
					//	if( BattleMyNo == myId ){
					//		BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
					//	}else{
					//		BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
					//		BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
					//	}
						// 自分完全抹杀
						//BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );
						
#endif
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							
							bmFlag = ReadBmDataNum();		// フラグ
							damage = ReadBmDataNum();		// 受伤
							
							// 武器坏れたとき
							if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
								newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
								newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
							}else{
								newGraNo = 0;
							}
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage, newGraNo );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
							
							// 行动リストをセットする（相手）
							//BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_OTHER_ID_DIR_2 );
							// 目的ＩＤ记忆
							//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->otherId = myId;
							
							// 受伤アニメーション
							//BattleSetActList( pActBc[ myId ], BC_ACT_DAMAGE );
							
							// デフォルトの方向へ向ける
							//	BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_DEF_DIR );
							
							// 回避じゃないとき
							if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
								// ＡＫＯ１のとき
								if( bmFlag & BM_FLAG_AKO1 ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
									// 自分のとき
									if( BattleMyNo == enemyId ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}else{
									// 受伤が０じゃないとき
									if( damage != 0 ){
										// 通常攻击かクリティカルの时
										if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
											BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
										}
									}
								}
							}
							
							// 受伤が０じゃないかつ、回避じゃないとき
							if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
								// デフォルト位置に移动
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
							}
							
							// 気絶の时
							if( bmFlag & BM_FLAG_DEATH && !( bmFlag & BM_FLAG_REFLECTION_MAGIC ) ){
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
							}
						}
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
						
						
					// 召唤 **************************************************************
					case B_SKILL_SUMMON:		// 召唤（ＩＤ　93）
					
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "召唤", MB_OK | MB_ICONSTOP );           //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						
						// グラフィックジャンプさせる
						BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
						// ジャンプグラフィック番号
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_PET_SUMMON;
						
						// ペット召唤モーション
						BattleSetActList( pActBc[ myId ], BC_ACT_PET_SUMMON );
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							//if( pActBc[ enemyId ] == NULL ){
							//	#ifdef _DEBUG
							//	sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId );
							//	MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP );
							//	#endif
							//	break;
							//}
							
							
							// アクションがある时
						//	if( pActBc[ enemyId ] != NULL ){
								// どんな场合でも强制的に抹杀する
								DeathAction( pActBc[ enemyId ] );
						//	}
							// 新规にアクション作成
							pActBc[ enemyId ] = MakeBattleChar( enemyId );
							
							ACTION *pAct = pActBc[ enemyId ];
							// 予备构造体
							BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
							
							ReadBmDataStr( pAct->name, sizeof( pAct->name ) );			// 名称読み込み
							//ReadBmDataStr( pAct->freeName, sizeof( pAct->freeName ) );	// 称号読み込み

#ifdef _CG2_NEWGRAPHIC
							pAct->anim_chr_no = getNewGraphicNo( ReadBmDataNum() ) ;	// グラフィック番号読み込み
#else
							pAct->anim_chr_no = ReadBmDataNum();	// グラフィック番号読み込み
#endif


							
							pAct->level = ReadBmDataNum();	// 等级読み込み
							pAct->hp = ReadBmDataNum();		// ＨＰ読み込み
							pAct->maxHp = ReadBmDataNum();	// 最大ＨＰ読み込み
							pAct->fp = ReadBmDataNum();		// 魔力読み込み
							pAct->maxFp = ReadBmDataNum();	// 最大魔力読み込み
							
							pYobi->bcFlag = ReadBmDataNum();	// フラグ読み込み
							
							// 行动リスト初期化
							pYobi->actListCnt = 0;
							pYobi->actSetListCnt = -1;
							pYobi->actList[ 0 ] = BC_ACT_STANDBY;
							pYobi->actList[ 1 ] = (BC_ACT)-1;
							
							// 初期位置
							pAct->fx = ( float )BcPos[ BcPosId[ enemyId ] ].defX;
							pAct->fy = ( float )BcPos[ BcPosId[ enemyId ] ].defY;
							pAct->x = BcPos[ BcPosId[ enemyId ] ].defX;
							pAct->y = BcPos[ BcPosId[ enemyId ] ].defY;
							// 初期スケール
							pAct->scaleX = 0;
							pAct->scaleY = 0;
							// 自分のＩＤ记忆
							pYobi->myId = enemyId;
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, 0, 0 );
							
							// ＳＥ番号记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 204;
							// ＳＥ鸣らす
							BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE );
							
							// ペット扩大
							BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_CHANGE2 );
							// 行动リストスタート
							//BattleStartActList( pActBc[ enemyId ] );
						}
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
					
					// 踊る **************************************************************
					case B_SKILL_DANCE:			// 踊る
					
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						danceId = ReadBmDataNum();		// 踊り效果ＩＤ読み込み
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "反射、吸収、無効、体力再生、Ｓ異常、Ｓ異常回復、属性反転、気絶回復", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;
						// ダンスＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->danceId = danceId;
						
						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_SPIN );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						
						// エフェクトは补助系魔法
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_s_start_big + effectSizeTbl[ techId ];
						
						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
						
						if( danceId == B_DANCE_POISON 				// 毒状态异常
							|| danceId == B_DANCE_SLEEP             // 眠り状态异常
							|| danceId == B_DANCE_STONE             // 石化状态异常
							|| danceId == B_DANCE_INEBRIETY         // 酔い状态异常
							|| danceId == B_DANCE_CONFUSION         // 混乱状态异常
							|| danceId == B_DANCE_FORGET            // 忘却状态异常
							
							|| danceId == B_DANCE_DEATH             // 死亡
							|| danceId == B_DANCE_STATUS_RECOVER    // 状态异常回复
							|| danceId == B_DANCE_REVIVE            // 全体复活
							|| danceId == B_DANCE_NONE				// 不発
						){	
							
							// 相手がいなくなるまでループ
							while( 1 )
							{
								// 次の相手を読み込み
								enemyId = ReadBmDataNum();
								
								// 攻击相手がいなくなったら拔ける
								if( enemyId == 0xFF ) break;
								
								// いるかチェック
								if( pActBc[ enemyId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
									MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								bmFlag = ReadBmDataNum();		// フラグ
								
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0/*damage*/ );
								
								// スキルＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
								// テックＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
								
								// 行动リストをセットする（相手）
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								
								// 状态异常のとき
								if( danceId >= B_DANCE_POISON && danceId <= B_DANCE_FORGET ){
									// 回复エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABNORMAL_STATUS );
									// 成功したとき
									if( bmFlag & BM_FLAG_SUCCESS ){
										// スキルＩＤ教えてもらう
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
										// ダンスＩＤ教えてもらう
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->danceId = danceId;
										// 状态异常マーク作成
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ABNORMAL_ON );
										
									}
								}else
								// 即死の时
								if( danceId == B_DANCE_DEATH ){
									// 即死エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_TERROR );
									// 成功したとき
									if( bmFlag & BM_FLAG_SUCCESS ){
										// フラグＯＮ
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD_FLAG_ON );
										// 体力を０にする
										BattleSetActList( pActBc[ enemyId ], BC_ACT_LP_0 );
										// 死亡
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
									}
								}else
								// 状态异常回复のとき
								if( danceId == B_DANCE_STATUS_RECOVER ){
									// 回复エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_STATUS_RECOVER );
									// 成功したとき
									if( bmFlag & BM_FLAG_SUCCESS ){
										// 状态异常マーク抹杀
										BattleSetActList( pActBc[ enemyId ], BC_ACT_ABNORMAL_OFF );
									}
								}else
								// 気絶回复魔法
								if( danceId == B_DANCE_REVIVE ){
									// 回复ポイント読み込み
									damage = ReadBmDataNum();		// 受伤
									
									// デフォルトの场所にいないとき（死んで归ってるとき）
									//if( pActBc[ enemyId ]->fx != ( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->defX
									//|| pActBc[ enemyId ]->fy != ( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->defY ){
									if( 1 ){	
										// リストの初期化（ＩＤ指定版、死んでる人も）
										BattleInitListWithDead( enemyId );
										// 待机にする
										pActBc[ enemyId ]->actNo = BC_ACT_STANDBY;
										// ＳＥ番号记忆
										//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 280;
										// ＳＥ鸣らす
										//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
										// 気絶回复魔法エフェクト発生
										BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REVIVE );
										// 気絶フラグＯＦＦ
									//	( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->bcFlag &= ~BC_FLAG_DEATH;
										// 成功したとき
										if( bmFlag & BM_FLAG_SUCCESS ){
											// スピード
											pActBc[ enemyId ]->speed = 8;
											// 気絶フラグＯＦＦ
											BattleSetActList( pActBc[ enemyId ], BC_ACT_REVIVE );
											// 回复ポイント表示
											BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
											// 回复ポイント记忆
											( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
											// 文字色记忆
											( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
										}else{
											// 失败したらやっぱり、死亡フラグ立てる
									//		BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD_FLAG_ON );
										}
										
										// デフォルト位置に移动
									//	BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
										// デフォルトの方向に向ける
										BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_DEF_DIR );
										
									}else{
										// ＳＥ番号记忆
										//( ( BC_YOBI *)pActBc[ myId ]->pYobi )->seNo = 280;
										// ＳＥ鸣らす
										//BattleSetActList( pActBc[ myId ], BC_ACT_PLAY_SE );
										// 気絶回复魔法エフェクト発生
										BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REVIVE );
										// 成功したとき
										if( bmFlag & BM_FLAG_SUCCESS ){
											// 気絶フラグＯＦＦ
											BattleSetActList( pActBc[ enemyId ], BC_ACT_REVIVE );
											// 回复ポイント表示
											BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
											// 回复ポイント记忆
											( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
											// 文字色记忆
											( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
										}
									}
								}
							}
						}
						else
						// パワーアップ演出 ************************************************
						if( danceId == B_DANCE_POWER )
						{
							// 相手がいなくなるまでループ
							while( 1 )
							{
								// 次の相手を読み込み
								enemyId = ReadBmDataNum();
								
								// 攻击相手がいなくなったら拔ける
								if( enemyId == 0xFF ) break;
								
								// いるかチェック
								if( pActBc[ enemyId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
									MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ myId ], enemyId, 0, 0/*damage*/ );
								
								// スキルＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
								// テックＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
								
								// ＳＥ番号记忆
								//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 198;
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 288;
								// ＳＥ鸣らす
								BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE );
								// 膨らます
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DANCE_POW_UP_SCALE );
								
								// グラフィックジャンプさせる
								BattleSetActList( pActBc[ enemyId ], BC_ACT_JUMP_DISP );
								// ジャンプグラフィック番号
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->jumpGraNo = CG_B_STR_POW_UP;
								// ＳＥ番号记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo2 = 168;
								// ＳＥ鸣らす
								BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE_2 );
								
								// 攻击アニメーション
								BattleSetActList( pActBc[ enemyId ], BC_ACT_ANIM_ATTACK );
								
								
								//	// ネットローグ
								//	ANIM_WALK,          // 歩き walk １ 
								//	ANIM_B_WALK,        // バトル時移動（中） bw2 ３ 
								//	ANIM_DAMAGE,        // 受伤 dmg ８ 
								//	ANIM_GUARD,         // 防御 guard ９ 
								//	ANIM_DEAD,          // 気絶 die １０ 
								
							}	
						}
						else
						// ポジションチェンジの时 *********************************************
						if( danceId == B_DANCE_POSITION )
						{
							// 相手がいなくなるまでループ
							while( 1 )
							{
								// 次の相手を読み込み
								enemyId = ReadBmDataNum();
								
								// 攻击相手がいなくなったら拔ける
								if( enemyId == 0xFF ) break;
								
								// いるかチェック
								if( pActBc[ enemyId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
									MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								{
									
									// ペットのＩＤ
									int petId = BattleCheckPetId( enemyId );
									// ＩＤバックアップ
									int idBak = enemyId;
									// アクションポインタバックアップ
									ACTION *actBak = pActBc[ enemyId ];
									
									// 自分の时はＭｙＩｄの变更
									if( BattleMyNo == enemyId ) BattleMyNo = petId;
									
									// 敌リストをセットする（踊る用カスタマイズ）
									BattleSetEnemyList( pActBc[ myId ], petId, 0, 0/*damage*/ );
									
									// 自分もポジションチェンジする时
									if( myId == enemyId ) myId = petId;
								//	if( myId == petId ) myId = enemyId;
									
									
									
									// 自分
									// デフォルト座标の更新
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->defX = BcPos[ BcPosId[ petId ] ].defX;
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->defY = BcPos[ BcPosId[ petId ] ].defY;
									// スタート位置设定
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->startX = BcPos[ BcPosId[ petId ] ].startX;
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->startY = BcPos[ BcPosId[ petId ] ].startY;
									// ＩＤの更新
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->myId = petId;
									
									// ペットいるとき
									if( pActBc[ petId ] != NULL ){
									//if( pActBc[ petId ] != NULL ){
									
										// ＩＤの更新
										( ( BC_YOBI *)pActBc[ petId ]->pYobi )->myId = enemyId;
										// アルティメットじゃないとき
										if( pActBc[ petId ] != NULL && pActBc[ petId ]->actNo != BC_ACT_AKO1 ){
											// デフォルト座标の更新
											( ( BC_YOBI *)pActBc[ petId ]->pYobi )->defX = BcPos[ BcPosId[ enemyId ] ].defX;
											( ( BC_YOBI *)pActBc[ petId ]->pYobi )->defY = BcPos[ BcPosId[ enemyId ] ].defY;
											// スタート位置设定
											( ( BC_YOBI *)pActBc[ petId ]->pYobi )->startX = BcPos[ BcPosId[ enemyId ] ].startX;
											( ( BC_YOBI *)pActBc[ petId ]->pYobi )->startY = BcPos[ BcPosId[ enemyId ] ].startY;
										}
									}
									
									// アクション入れ替え
									pActBc[ enemyId ] = pActBc[ petId ];
									pActBc[ petId ] = actBak;
									// ＩＤ入れ替え
									enemyId = petId;
									petId = idBak;
									
									
									
									// 敌リストをセットする（踊る用カスタマイズ）
									//BattleSetEnemyList( pActBc[ enemyId ], petId, bmFlag, 0/*damage*/ );
									// 相手を次の行动へ
									//BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
									
									
									// 自分
									// ＳＥ番号记忆
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 210;
									// ＳＥ鸣らす
									BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE );
									// デフォルト位置に移动
									BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
									// 行动リストスタート
							//		BattleStartActList( pActBc[ enemyId ] );
									// スピード
									pActBc[ enemyId ]->speed = 3;
									// アニメーションスピード
									pActBc[ enemyId ]->anim_speed = 100;
									
									// ペットいるとき
									if( pActBc[ petId ] != NULL && pActBc[ petId ]->actNo != BC_ACT_AKO1 ){
										
										// 敌リストをセットする（踊る用カスタマイズ）
										BattleSetEnemyList( pActBc[ myId ], petId, 0, 0/*damage*/ );
										
										// 待机状态にする
									//	BattleSetActList( pActBc[ petId ], BC_ACT_STANDBY );
										// デフォルト位置に移动
										BattleSetActList( pActBc[ petId ], BC_ACT_MOVE_DEF );
										// 行动リストスタート
							//			BattleStartActList( pActBc[ petId ] );
										// 死亡移动中ではないとき
										if( pActBc[ petId ]->speed != 1 ){
											// スピード
											pActBc[ petId ]->speed = 3;
											// アニメーションスピード
											pActBc[ petId ]->anim_speed = 100;
										}
									}
								}
							}
						}
						else
						// ペット戾す *********************************************
						if( danceId == B_DANCE_PET_RETURN )
						{
							// 相手がいなくなるまでループ
							while( 1 )
							{
								// 次の相手を読み込み
								enemyId = ReadBmDataNum();
								
								// 攻击相手がいなくなったら拔ける
								if( enemyId == 0xFF ) break;
								
								// いるかチェック
								if( pActBc[ enemyId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
									MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								// 敌リストをセットする（踊る用カスタマイズ）
								BattleSetEnemyList( pActBc[ myId ], enemyId, 0, 0/*damage*/ );
								
								// 気絶していたら
								if( ( ( BC_YOBI* )pActBc[ enemyId ]->pYobi )->bcFlag & BC_FLAG_DEATH ){
									// リストの初期化（ＩＤ指定版、死んでる人も）
									BattleInitListWithDead( enemyId );
								}
								
								// ＳＥ番号记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 205;
								// ＳＥ鸣らす
								BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE );
								// ペット缩小
								BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_CHANGE );
								// アクション抹杀
								BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );
							}
						}
						else
						// 自爆 *********************************************
						if( danceId == B_DANCE_BOMB )
						{
							// 自爆する人ＩＤ読み込み
							myId2 = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( myId2 == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ myId2 ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "没找到ＩＤ。skillId = %d, myId2 = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_DANCE_BOMB", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							
							// 敌リストをセットする（踊る用カスタマイズ）
							//BattleSetEnemyList( pActBc[ myId ], myId2, 0, 0/*damage*/ );
							// 自爆者ＩＤ记忆（ここだけ例外で、otherId だけの行动をスタートする
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->otherId = myId2;
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->techId = techId;
							
							// 行动リストをセットする（自分）
							BattleSetActList( pActBc[ myId2 ], BC_ACT_EFFECT_SKILL_GENERATE );
							// 目的地に移动する
							BattleSetActList( pActBc[ myId2 ], BC_ACT_B_MOVE_START_2 );
							BattleSetActList( pActBc[ myId2 ], BC_ACT_MOVE_POINT );
							//BattleSetActList( pActBc[ myId2 ], BC_ACT_B_MOVE );
							BattleSetActList( pActBc[ myId2 ], BC_ACT_B_MOVE_END );
							// 自分が右グループの时
							if( myId2 < 10 ){
								// 移动先指定
								( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->moveX = BcPos[ BcPosId[ 10 ] ].defX + ( BcPos[ BcPosId[ 15 ] ].defX - BcPos[ BcPosId[ 10 ] ].defX ) / 2;
								( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->moveY = BcPos[ BcPosId[ 10 ] ].defY + ( BcPos[ BcPosId[ 15 ] ].defY - BcPos[ BcPosId[ 10 ] ].defY ) / 2;
							}
							// 自分が左グループの时
							else{
								// 移动先指定
								( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->moveX = BcPos[ BcPosId[ 5 ] ].defX + ( BcPos[ BcPosId[ 0 ] ].defX - BcPos[ BcPosId[ 5 ] ].defX ) / 2;
								( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->moveY = BcPos[ BcPosId[ 5 ] ].defY + ( BcPos[ BcPosId[ 0 ] ].defY - BcPos[ BcPosId[ 5 ] ].defY ) / 2;
							}
							
							// ＳＥ番号记忆
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->seNo = 288;
							// ＳＥ鸣らす
							BattleSetActList( pActBc[ myId2 ], BC_ACT_PLAY_SE );
							// 膨れる
							BattleSetActList( pActBc[ myId2 ], BC_ACT_SCALE_2 );
							// 爆発エフェクト作成
							//BattleSetActList( pActBc[ myId2 ], BC_ACT_SCALE_2 );
							
							// ちょっと待つ
							BattleSetActList( pActBc[ myId2 ], BC_ACT_WAIT_05 );
							// 自分一时消える（ＨＩＤＥ）
							BattleSetActList( pActBc[ myId2 ], BC_ACT_HIDE );
							// 全敌の行动开始
							BattleSetActList( pActBc[ myId2 ], BC_ACT_BOM_START );
							// ちょっと待つ
							BattleSetActList( pActBc[ myId2 ], BC_ACT_WAIT_1 );
							// 地震フラグＯＦＦ
							BattleSetActList( pActBc[ myId2 ], BC_ACT_QUAKE_OFF );
							// 攻击する侧が自分（ BattleMyNo )のとき
							if( BattleMyNo == myId2 ){
								BattleSetActList( pActBc[ myId2 ], BC_ACT_BATTLE_END );	// 战闘終了
							}else{
								BattleSetActList( pActBc[ myId2 ], BC_ACT_PET_OUT );		// ペットいたら退场
								BattleSetActList( pActBc[ myId2 ], BC_ACT_DEATH_ACT );	// アクション抹杀
							}
							// 自分完全抹杀
							//BattleSetActList( pActBc[ myId2 ], BC_ACT_DEATH_ACT );
							
							
							// 相手がいなくなるまでループ
							while( 1 ){
								
								// 次の相手を読み込み
								enemyId = ReadBmDataNum();
								
								// 攻击相手がいなくなったら拔ける
								if( enemyId == 0xFF ) break;
								
								// いるかチェック
								if( pActBc[ enemyId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
									MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								bmFlag = ReadBmDataNum();		// フラグ
								damage = ReadBmDataNum();		// 受伤
								
								// 武器坏れたとき
								if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
#ifdef _CG2_NEWGRAPHIC
									newGraNo = getNewGraphicNo(ReadBmDataNum());	// 新グラフィック番号読み込み
#else
									newGraNo = ReadBmDataNum();	// 新グラフィック番号読み込み
#endif
								}else{
									newGraNo = 0;
								}
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ myId2 ], enemyId, bmFlag, damage, newGraNo );
								
								// スキルＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
								// テックＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
								
								// 行动リストをセットする（相手）
								//BattleSetActList( pActBc[ enemyId ], BC_ACT_CHANGE_OTHER_ID_DIR_2 );
								// 目的ＩＤ记忆
								//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->otherId = myId;
								
								
								// 术者が受伤を食らう时
								if( myId == enemyId ){
									// 待机状态にする
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								}
								
								// 回避じゃないとき
								if( !( bmFlag & BM_FLAG_AVOIDANCE ) && !( bmFlag & BM_FLAG_MIKAWASHI ) ){
									// ＡＫＯ１のとき
									if( bmFlag & BM_FLAG_AKO1 ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_AKO1 );
										// 自分のとき
										if( BattleMyNo == enemyId ){
											BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
										}else{
											BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
											BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
										}
									}else{
										// 受伤が０じゃないとき
										if( damage != 0 ){
											// 通常攻击かクリティカルの时
											if( bmFlag & BM_FLAG_NORMAL || bmFlag & BM_FLAG_CRITICAL ){
												BattleSetActList( pActBc[ enemyId ], BC_ACT_HIT_BACK );
											}
										}
									}
								}
								
								// 受伤が０じゃないかつ、回避じゃないとき
								if( damage != 0 || bmFlag & BM_FLAG_AVOIDANCE || bmFlag & BM_FLAG_MIKAWASHI ){
									// デフォルト位置に移动
									BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF );
								}
								
								// 気絶の时
								if( bmFlag & BM_FLAG_DEATH && !( bmFlag & BM_FLAG_REFLECTION_MAGIC ) ){
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD );
								}
							}
						}
						else
						// サクリファイス *********************************************
						if( danceId == B_DANCE_SACRIFICE )
						{
							// 牺牲者ＩＤ読み込み
							myId2 = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( myId2 == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ myId2 ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "没找到ＩＤ。skillId = %d, myId2 = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_DANCE_BOMB", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							
							// 敌リストをセットする（踊る用カスタマイズ）
							//BattleSetEnemyList( pActBc[ myId ], myId2, 0, 0/*damage*/ );
							// 自爆者ＩＤ记忆（ここだけ例外で、otherId だけの行动をスタートする
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->otherId = myId2;
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->techId = techId;
							
							
							// 移动先指定
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->moveX = BcPos[ BcPosId[ 15 ] ].defX + ( BcPos[ BcPosId[ 5 ] ].defX - BcPos[ BcPosId[ 15 ] ].defX ) / 2;
							( ( BC_YOBI *)pActBc[ myId2 ]->pYobi )->moveY = BcPos[ BcPosId[ 15 ] ].defY + ( BcPos[ BcPosId[ 5 ] ].defY - BcPos[ BcPosId[ 15 ] ].defY ) / 2;
							// 目的地に移动する
							BattleSetActList( pActBc[ myId2 ], BC_ACT_B_MOVE_START_2 );
							BattleSetActList( pActBc[ myId2 ], BC_ACT_MOVE_POINT );
							BattleSetActList( pActBc[ myId2 ], BC_ACT_B_MOVE_END );
							
							// 立ちアニメーションにする
							BattleSetActList( pActBc[ myId2 ], BC_ACT_ANIM_STAND );
							
							// サクリファイスエフェクト
							BattleSetActList( pActBc[ myId2 ], BC_ACT_EFFECT_SACRIFICE );
							// 次々に分身を飞ばす
							BattleSetActList( pActBc[ myId2 ], BC_ACT_SACRIFICE );
							// 攻击する侧が自分（ BattleMyNo )のとき
							if( BattleMyNo == myId2 ){
								BattleSetActList( pActBc[ myId2 ], BC_ACT_BATTLE_END );	// 战闘終了
							}else{
								BattleSetActList( pActBc[ myId2 ], BC_ACT_PET_OUT );		// ペットいたら退场
								BattleSetActList( pActBc[ myId2 ], BC_ACT_DEATH_ACT );	// アクション抹杀
							}
							
							// 相手がいなくなるまでループ
							while( 1 ){
								
								// 次の相手を読み込み
								enemyId = ReadBmDataNum();
								
								// 攻击相手がいなくなったら拔ける
								if( enemyId == 0xFF ) break;
								
								// いるかチェック
								if( pActBc[ enemyId ] == NULL ){
									#ifdef _DEBUG
									sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
									MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
									#endif
									break;
								}
								
								bmFlag = ReadBmDataNum();		// フラグ
								damage = ReadBmDataNum();		// 受伤
								
								// 敌リストをセットする
								BattleSetEnemyList( pActBc[ myId2 ], enemyId, bmFlag, damage, 0 );
								
								// スキルＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
								// テックＩＤ记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
								
								// 术者も回复する时
								if( myId == enemyId ){
									// 待机状态にする
									BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								}
								
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_LP_INCREASE );
								// 回复ポイント表示
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
								// 回复ポイント记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
								// 文字色记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
							}
						}
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
					
					//パラメータ变更
					case B_SKILL_DEF_UP:			// 防御力アップ
					case B_SKILL_DEF_DOWN:			// 防御力ダウン
					case B_SKILL_ATK_UP:			// 攻击力アップ
					case B_SKILL_ATK_DOWN:			// 攻击力ダウン
					case B_SKILL_AGL_UP:			// すばやさアップ
					case B_SKILL_AGL_DOWN:			// すばやさダウン
					
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "反射、吸収、無効、体力再生、Ｓ異常、Ｓ異常回復、属性反転、気絶回復", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_SPIN );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_s_start_big + effectSizeTbl[ techId ];						
						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );
					
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_MAGICATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0/*damage*/ );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
							
							// 行动リストをセットする（相手）
							//BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
							
							if( skillId >= B_SKILL_DEF_UP && skillId <= B_SKILL_AGL_DOWN )
							{
								// 回复エフェクト
//								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABNORMAL_STATUS );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_PARAMETER );
								// 成功したとき
								if( bmFlag & BM_FLAG_SUCCESS )
								{
									// スキルＩＤ教えてもらう
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
									// 状态异常マーク作成
									BattleSetActList( pActBc[ enemyId ], BC_ACT_PARAMETER_UP_DOWN_ON );
									if(skillId == B_SKILL_DEF_UP || skillId == B_SKILL_ATK_UP || skillId == B_SKILL_AGL_UP
									){
										// ＳＥ番号记忆
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 253;
									}else{
										// ＳＥ番号记忆
										( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 274;
									}
									// ＳＥ鸣らす
									BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE );
									BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1 );									
								}
							}
						}
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
					
#ifdef _TEST_TECH_YUK
					// 敌状态调查
					case B_SKILL_DETECTENEMY:
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号

						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_DETECTENEMY", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );
						
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );

						// 敌を调べる
						BattleSetActList( pActBc[ myId ], BC_ACT_DETECTENEMY );

						// 相手がいなくなるまでループ
						while( 1 ){
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();

							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;

							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_DETECTENEMY", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ

							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0/*damage*/ );
						}

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ

						break;

					// 紧急手当
					case B_SKILL_URGENTALLOWANCE:
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号

						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_URGENTALLOWANCE", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}

						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;

						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );

						// 相手がいなくなるまでループ
						while( 1 ){
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();

							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;

							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_URGENTALLOWANCE", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							damage = ReadBmDataNum();		// 受伤
							bmFlag = ReadBmDataNum();		// フラグ

							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;

							// ターゲットが自分じゃなかった场合
							if( myId != enemyId){
								// 行动リストをセットする（相手）
								BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
								BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE );
								BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
							}
							// 紧急手当
							BattleSetActList( pActBc[ myId ], BC_ACT_URGENTALLOWANCE );

							// 成功した场合
							if( bmFlag & BM_FLAG_SUCCESS){
								if( myId == enemyId){
									// 喜ぶ２（終了まで待たない）
									BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY_2 );
								}
								else {
									// 喜ぶ
									BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY );
								}
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_LP_INCREASE );
								// エフェクト番号セット
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->effectNo = SPR_l_start_big + effectSizeTbl[ techId ];
								// 回复ポイント表示
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP );
								// 回复ポイント记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = damage;
								// 文字色记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
							}
							// 失败した场合
							else {
								// ふきだし表示
								BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
								// グラフィック番号
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_MISS;
								// 悲伤
								BattleSetActList( pActBc[ myId ], BC_ACT_SAD );
							}

							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, damage );
						}

						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ

						break;

					// 紧急治疗
					case B_SKILL_URGENTMEDIC:
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号

						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_URGENTMEDIC", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}

						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;

						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );

						// 相手がいなくなるまでループ
						while( 1 ){
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();

							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;

							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_URGENTMEDIC", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ

							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;

							// ターゲットが自分じゃなかった场合
							if( myId != enemyId){
								// 行动リストをセットする（相手）
								BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
								BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
								BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE );
								BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );
							}
							// 紧急治疗
							BattleSetActList( pActBc[ myId ], BC_ACT_URGENTALLOWANCE );

							// 成功した场合
							if( bmFlag & BM_FLAG_SUCCESS){
								if( myId == enemyId){
									// 喜ぶ２（終了まで待たない）
									BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY_2 );
								}
								else {
									// 喜ぶ
									BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY );
								}
								// 回复エフェクト
								BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_STATUS_RECOVER );
								// エフェクト番号セット
								//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->effectNo = SPR_l_start_big + effectSizeTbl[ techId ];
							}
							else if( bmFlag & BM_FLAG_INJURY ){
								if( bmFlag & BM_FLAG_CRITICAL ){
									// 状态异常エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABNORMAL_STATUS );
									// ふきだし表示
									BattleSetActList( pActBc[ enemyId ], BC_ACT_JUMP_DISP );
									// グラフィック番号
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->jumpGraNo = CG_B_STR_INJURY;
									// 倒下
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DEAD_2 );
									// 悲伤
									BattleSetActList( pActBc[ myId ], BC_ACT_SAD );
								}
								else {
									// ふきだし表示
									BattleSetActList( pActBc[ enemyId ], BC_ACT_JUMP_DISP );
									// グラフィック番号
									( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->jumpGraNo = CG_B_STR_INJURY;
									// 受伤
									BattleSetActList( pActBc[ enemyId ], BC_ACT_DAMAGE );
									// 悲伤
									BattleSetActList( pActBc[ myId ], BC_ACT_SAD );
								}
							}
							// 失败した场合
							else {
								// ふきだし表示
								BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
								// グラフィック番号
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_MISS;
								// 悲伤
								BattleSetActList( pActBc[ myId ], BC_ACT_SAD );
							}

							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0 );
						}

						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );

						pYobiBm->subActNo++; // 次へ

						break;

					// 狮子咆
					case B_SKILL_ULTIMATEATTACK:
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号

						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_ULTIMATEATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}

						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;

						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SUPERSKILL_GENERATE );
						BattleSetActList( pActBc[ myId ], BC_ACT_ZANZOU_ON );

						// 相手がいなくなるまでループ
						while( 1 ){
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();

							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;

							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_ULTIMATEATTACK", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ

							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;

							BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY);
							BattleSetActList( pActBc[ myId ], BC_ACT_ULTIMATEATTACK_START);
							BattleSetActList( pActBc[ myId ], BC_ACT_ULTIMATEATTACK_MOVE);

							if( bmFlag & BM_FLAG_SUCCESS){
								if( bmFlag & BM_FLAG_REFLECTION_PHYSICS){
									// 反射エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_REFLECTION_PHYSICS);
									BattleSetActList( pActBc[ myId ], BC_ACT_ULTIMATEATTACK_AKO);
									BattleSetActList( pActBc[ myId ], BC_ACT_WAIT_1);
									BattleSetActList( pActBc[ myId ], BC_ACT_QUAKE_OFF);
									// 攻击する侧が自分（ BattleMyNo )のとき
									if( BattleMyNo == myId ){
										BattleSetActList( pActBc[ myId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ myId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ myId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}
								else if( bmFlag & BM_FLAG_ABSORB_PHYSICS){
									// 吸收エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_ABSORB_PHYSICS);
								}
								else if( bmFlag & BM_FLAG_INEFFECTIVE_PHYSICS){
									// 无效エフェクト
									BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_INEFFECTIVE_PHYSICS);
								}
								else{
									BattleSetActList( pActBc[ enemyId ], BC_ACT_ULTIMATEATTACK_AKO);
									BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1);
									BattleSetActList( pActBc[ enemyId ], BC_ACT_QUAKE_OFF);
									// 自分のとき（攻击された侧が飞ぶ）
									if( BattleMyNo == enemyId ){
										BattleSetActList( pActBc[ enemyId ], BC_ACT_BATTLE_END );	// 战闘終了
									}else{
										BattleSetActList( pActBc[ enemyId ], BC_ACT_PET_OUT );		// ペットいたら退场
										BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );	// アクション抹杀
									}
								}
							}
							else {
								BattleSetActList( pActBc[ enemyId ], BC_ACT_MOVE_DEF);
							}

							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0 );
						}

						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_START );
						BattleSetActList( pActBc[ myId ], BC_ACT_MOVE_DEF);
						BattleSetActList( pActBc[ myId ], BC_ACT_B_MOVE_END );

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );

						pYobiBm->subActNo++; // 次へ

						break;

					// 回复力アップ
					case B_SKILL_RCV_UP:
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号

						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_RCV_UP", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}

						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;

						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						//デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );

						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->effectNo = SPR_s_start_big + effectSizeTbl[ techId ];

						// 魔法発生
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_MAGIC_GENERATE );
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );

						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_RCV_UP", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0/*damage*/ );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;
							
							BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_PARAMETER );

							// 成功したとき
							if( bmFlag & BM_FLAG_SUCCESS )
							{
								// 状态异常マーク作成
								BattleSetActList( pActBc[ enemyId ], BC_ACT_PARAMETER_UP_DOWN_ON );
								// ＳＥ番号记忆
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->seNo = 253;
								// ＳＥ鸣らす
								BattleSetActList( pActBc[ enemyId ], BC_ACT_PLAY_SE );
								BattleSetActList( pActBc[ enemyId ], BC_ACT_WAIT_1 );									
							}
						}

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );

						pYobiBm->subActNo++; // 次へ

						break;

					// 煽る
					case B_SKILL_PROVOCATION:
						// バトルムービーデータから数字を読み込む
						weaponId = ReadBmDataNum();		// 武器ＩＤ
						
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号

						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。skillId = %d, myId = %d", skillId, myId ); //MLHIDE
							MessageBox( hWnd, moji, "B_SKILL_RCV_UP", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}

						// スキルＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->skillId = skillId;
						// テックＩＤ记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->techId = techId;

						// 消费フォースポイント记忆
						( ( BC_YOBI *)pActBc[ myId ]->pYobi )->consumeFp = consumeFp;

						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						// 魔力マイナス
						BattleSetActList( pActBc[ myId ], BC_ACT_CONSUME_FP );
						//デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_SKILL_GENERATE );

						// アクションする
						//BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_ATTACK);
						// 立ちアニメーションにする
						BattleSetActList( pActBc[ myId ], BC_ACT_ANIM_STAND );

						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。skillId = %d, enemyId = %d", skillId, enemyId ); //MLHIDE
								MessageBox( hWnd, moji, "B_SKILL_PROVOCATION", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							bmFlag = ReadBmDataNum();		// フラグ
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, bmFlag, 0/*damage*/ );
							
							// スキルＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = techId;

							BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY);

							// 成功したとき
							if( bmFlag & BM_FLAG_SUCCESS )
							{
								// 怒る
								BattleSetActList( pActBc[ enemyId ], BC_ACT_ANIM_ATTACK);
							}
							else {
								// 失败
								BattleSetActList( pActBc[ enemyId ], BC_ACT_JUMP_DISP);
								( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->jumpGraNo = CG_B_STR_MISS;
							}
							BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_NEXT_ACT_LIST);
							BattleSetActList( pActBc[ myId ], BC_ACT_NEXT_ENEMY_LIST);
						}

						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );

						pYobiBm->subActNo++; // 次へ

						break;

#endif /* _TEST_TECH_YUK */
						
				}
				break;
			
			
			
			
			case 1: // 終了チェック ****************************************************************/
			
				// スキル番号で分岐
				switch( skillId ){
					
					
					// 通常攻击 *********************************************************/
					case B_SKILL_NORMAL_ATTACK:
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// 自分がいるときかつ、死んでいないとき
							if( pActBc[ myIdBak ] != NULL && !( ( ( BC_YOBI *)pActBc[ myIdBak ]->pYobi )->bcFlag & BC_FLAG_DEATH ) ){
								// デフォルトの方向へ向ける
								ChangeDefDir( pActBc[ myIdBak ] );
							}
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
					
					// 连続攻击 ************************************************************/
					case B_SKILL_DOUBLE_ATTACK:
					// エッジ *********************************************************/
					case B_SKILL_EDGE:
					// バーストアタックエッジ *********************************************************/
					case B_SKILL_BURST:
					// フォースカット *********************************************************/
					case B_SKILL_FORCECUT:
					// 防御ブレイク *********************************************************/
					case B_SKILL_GUARDBRAKE:
					// 盗む *********************************************************/
					case B_SKILL_STEAL:
					// 状态异常付加攻击 ****************************************
					case B_SKILL_ADD_BAD_CONDITION_POISON:		// 毒状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_SLEEP:		// 眠り状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_STONE:		// 石化状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_INEBRIETY:	// 酔い状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_CONFUSION:	// 混乱状态付加攻击
					case B_SKILL_ADD_BAD_CONDITION_FORGET:		// 忘却状态付加攻击
					
					case B_SKILL_ATTACK_BLOOD:					// 吸血攻击
					case B_SKILL_ENERGYDRAIN:					// エナジードレイン
					case B_SKILL_BREAKWEAPON:					// 装备破坏攻击
					case B_SKILL_ATTACKGOLD:					// ゴールド攻击
					
					case B_SKILL_RANDOM_SHOT:					// 乱れうち（弓术士）
					case B_SKILL_ASSASSIN:						// 暗杀（忍者）
					case B_SKILL_KAKURAN:						// 搅乱
#ifdef _TEST_TECH_YUK
					case B_SKILL_RANBU:					// ＹＵＫテスト
					case B_SKILL_POISON_ARROW:			// 毒矢
					case B_SKILL_PICKPOCKET:			// 掏る
#endif /* _TEST_TECH_YUK */
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// 自分がいるときかつ、死んでいないとき
							if( pActBc[ myIdBak ] != NULL && !( ( ( BC_YOBI *)pActBc[ myIdBak ]->pYobi )->bcFlag & BC_FLAG_DEATH ) ){
#ifdef _TEST_TECH_YUK
								if( skillId == B_SKILL_RANBU){
								// 残像フラグＯＦＦ
								( ( BC_YOBI *)pActBc[ myIdBak ]->pYobi )->actZanzouFlag = FALSE;
								}
#endif /* _TEST_TECH_YUK */
							// 残像フラグＯＦＦ
							//( ( BC_YOBI *)pActBc[ myIdBak ]->pYobi )->actZanzouFlag = FALSE;
								// デフォルトの方向へ向ける
								ChangeDefDir( pActBc[ myIdBak ] );
							}
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
						
					// 气孔弹 *************************************************************/
					case B_SKILL_KIKOH:
						
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;

#ifdef _TEST_TECH_YUK
					// 全体攻击
					case B_SKILL_ATTACKALL:
					case B_SKILL_AXEBOMBER:
					case B_SKILL_THROWITEM:
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
							// ヒットフラグOFF
							pActBc[ myId ]->anim_hit = 0;
							// 隠しフラグOFF
							pActBc[ myId ]->atr &= ~ACT_ATR_HIDE;
							// 点灭フラグＯＦＦ
	#ifdef PUK2
		#ifdef PUK2_3DDEVICECHANGE_BATTLE
							pActBc[ myId ]->atr &= ~(ACT_ATR_TRANCEPARENT | ACT_ATR_FLASH_0);
		#else
								if (lpDraw->lpDe) pActBc[ myId ]->atr &= ~ACT_ATR_TRANCEPARENT;
								else pActBc[ myId ]->atr &= ~ACT_ATR_FLASH_0;
		#endif
	#else
							pActBc[ myId ]->atr &= ~ACT_ATR_FLASH_0;
	#endif
						}
						break;
#endif /* _TEST_TECH_YUK */
						
					// 体力回复魔法 *************************************************************/
					case B_SKILL_LP_INCREASE_SORO:
					case B_SKILL_LP_INCREASE_AREA:
					case B_SKILL_LP_INCREASE_SIDE:
						
					// サクリファイス ***********************************************************/
					case B_SKILL_SACRIFICE:
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
						
					// 攻击魔法（地：单体）*************************************
					case B_SKILL_MAGICATTACK_SORO_EARTH:
					case B_SKILL_MAGICATTACK_SORO_WATER:
					case B_SKILL_MAGICATTACK_SORO_FIRE:
					case B_SKILL_MAGICATTACK_SORO_WIND:
					
					case B_SKILL_MAGICATTACK_AREA_EARTH:
					case B_SKILL_MAGICATTACK_AREA_WATER:
					case B_SKILL_MAGICATTACK_AREA_FIRE:
					case B_SKILL_MAGICATTACK_AREA_WIND:
					
					case B_SKILL_MAGICATTACK_SIDE_EARTH:
					case B_SKILL_MAGICATTACK_SIDE_WATER:
					case B_SKILL_MAGICATTACK_SIDE_FIRE:
					case B_SKILL_MAGICATTACK_SIDE_WIND:
					// ドレイン ******************************************************
					case B_SKILL_DRAIN:

						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							//ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
						
					// 反射、吸收、无效 ******************************************************
					case B_SKILL_REFLECTION_PHYSICS:	// 物理反射魔法
					case B_SKILL_REFLECTION_MAGIC:		// 魔法反射魔法
					case B_SKILL_ABSORB_PHYSICS:		// 物理吸收魔法
					
					case B_SKILL_ABSORB_MAGIC:			// 魔法吸收魔法
					case B_SKILL_INEFFECTIVE_PHYSICS:	// 物理无效魔法
					case B_SKILL_INEFFECTIVE_MAGIC:		// 魔法无效魔法

					// 体力再生（单体、周辺、片侧） ******************************************************
					case B_SKILL_LP_RECOVER_SORO:		// LP再生单体魔法
					case B_SKILL_LP_RECOVER_AREA:		// LP再生周辺魔法
					case B_SKILL_LP_RECOVER_SIDE:		// LP再生片侧魔法
					
					// 状态异常魔法（单体、周辺、片侧） *********************************************
					case B_SKILL_ABNORMAL_SORO_POISON:		// 毒状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_SLEEP:		// 眠り状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_STONE:		// 石化状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_INEBRIETY:	// 酔い状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_CONFUSION:	// 混乱状态异常单体魔法
					case B_SKILL_ABNORMAL_SORO_FORGET:		// 忘却状态异常单体魔法
					
					case B_SKILL_ABNORMAL_AREA_POISON:		// 毒状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_SLEEP:		// 眠り状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_STONE:		// 石化状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_INEBRIETY:	// 酔い状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_CONFUSION:	// 混乱状态异常周辺魔法
					case B_SKILL_ABNORMAL_AREA_FORGET:		// 忘却状态异常周辺魔法
					
					case B_SKILL_ABNORMAL_SIDE_POISON:		// 毒状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_SLEEP:		// 眠り状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_STONE:		// 石化状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_INEBRIETY:	// 酔い状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_CONFUSION:	// 混乱状态异常片侧魔法
					case B_SKILL_ABNORMAL_SIDE_FORGET:		// 忘却状态异常片侧魔法
					
					// 状态异常回复魔法 **************************************************************
					case B_SKILL_STATUS_RECOVER:			// 状态异常回复魔法
					
					// 属性反転魔法 **************************************************************
					case B_SKILL_REVERSE_TYPE:			// 属性反転魔法
					
					// 気絶回复魔法 **************************************************************
					case B_SKILL_REVIVE:			// 気絶回复魔法
					
					// 即死 **************************************************************
					case B_SKILL_TERROR:			// 即死魔法
						
					// 攻击、防御、アップダウン ******************************************
					case B_SKILL_DEF_UP:			// 防御力アップ
					case B_SKILL_DEF_DOWN:			// 防御力ダウン
					case B_SKILL_ATK_UP:			// 攻击力アップ
					case B_SKILL_ATK_DOWN:			// 攻击力ダウン
					case B_SKILL_AGL_UP:			// すばやさアップ
					case B_SKILL_AGL_DOWN:			// すばやさダウン
					
						// 気絶回复の时
						if( skillId == B_SKILL_REVIVE ){
							// 終了チェック
							if( BattleCheckActEnd( TRUE ) == TRUE ){
								// デフォルトの方向へ向ける
								ChangeDefDir( pActBc[ myId ] );
								//BattleInitList();
								// ムービー読み込みフラグＯＮ
								ReadBmDataFlag = FALSE;
							}
						}else
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;

#ifdef _TEST_TECH_YUK
					// 敌状态调查
					case B_SKILL_DETECTENEMY:
					// 紧急手当
					case B_SKILL_URGENTALLOWANCE:
					// 紧急治疗
					case B_SKILL_URGENTMEDIC:
					// 回复力上升
					case B_SKILL_RCV_UP:
					// 煽る
					case B_SKILL_PROVOCATION:
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;

					// 狮子咆
					case B_SKILL_ULTIMATEATTACK:
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// 自分がいるときかつ、死んでいないとき
							if( pActBc[ myId ] != NULL && !( ( ( BC_YOBI *)pActBc[ myId ]->pYobi )->bcFlag & BC_FLAG_DEATH ) ){
								// デフォルトの方向へ向ける
								ChangeDefDir( pActBc[ myId ] );
								// 残像フラグＯＦＦ
								( ( BC_YOBI *)pActBc[ myId ]->pYobi )->actZanzouFlag = FALSE;
							}
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
#endif /* _TEST_TECH_YUK */

					// 属性优遇魔法 **************************************************************
					case B_SKILL_TREAT_TYPE_EARTH:		// 地属性优遇魔法
					case B_SKILL_TREAT_TYPE_WATER:		// 水属性优遇魔法
					case B_SKILL_TREAT_TYPE_FIRE:		// 火属性优遇魔法
					case B_SKILL_TREAT_TYPE_WIND:		// 风属性优遇魔法
					// 静寂
					case B_SKILL_SILENCE:				// 静寂
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
					
					// 精神统一 **************************************************************
					case B_SKILL_CONSENTRATION:		// 精神统一
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
					
					// 自爆 **************************************************************
					case B_SKILL_BOMB:			// 自爆
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
					
					// 地震 **************************************************************
					case B_SKILL_EARTHQUAKE:			// 地震
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
					
					// 召唤 **************************************************************
					case B_SKILL_SUMMON:		// 召唤（ＩＤ　93）
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
						
					// 踊る **************************************************************
					case B_SKILL_DANCE:			// 踊る
						
						// 気絶回复の时
					//	if( skillId == B_SKILL_REVIVE ){
							// 終了チェック
					//		if( BattleCheckActEnd( TRUE ) == TRUE ){
								// デフォルトの方向へ向ける
					//			ChangeDefDir( pActBc[ myId ] );
								//BattleInitList();
								// ムービー読み込みフラグＯＮ
					//			ReadBmDataFlag = FALSE;
					//		}
					//	}else
						// ポジション变更踊りの时
						if( danceId == B_DANCE_POSITION )
						{
							// 終了チェック
							if( BattleCheckActEnd( FALSE ) == TRUE ){
								int i = 0;
								int id;		// ポジションチェンジしたＩＤ
							//	int petId;	// ペットのＩＤ
								BC_YOBI* pYobi = ( BC_YOBI *)pActBc[ myId ]->pYobi;
								
								// 敌リストの最后までループ
								while( pYobi->enemyList[ i ].enemyId != -1 )
								{
									// ＩＤ记忆
									id = pYobi->enemyList[ i ].enemyId;
							//		petId = ( ( BC_YOBI *)pActBc[ id ]->pYobi )->enemyList[ 0 ].enemyId;
									
									// 死んでるかチェック
							//		if( pActBc[ petId ] != NULL && pActBc[ petId ]->anim_no != ANIM_DEAD
							//			&& pActBc[ petId ]->actNo != BC_ACT_AKO1 )
									// 死んでるかチェック
									if( pActBc[ id ] != NULL && pActBc[ id ]->anim_no != ANIM_DEAD
										&& pActBc[ id ]->actNo != BC_ACT_AKO1 )
									{
										// デフォルトの方向へ向ける
										ChangeDefDir( pActBc[ id ] );
									}
									i++;
								}
								
								// ムービー読み込みフラグＯＮ
								ReadBmDataFlag = FALSE;
							}
						}
						else
						{
							// 終了チェック
							if( BattleCheckActEnd( FALSE ) == TRUE ){
								// デフォルトの方向へ向ける
								ChangeDefDir( pActBc[ myId ] );
								//BattleInitList();
								// ムービー読み込みフラグＯＮ
								ReadBmDataFlag = FALSE;
							}
						}
						break;
					
				}
				break;
			
			case 2: // その他の行动 ****************************************************************/
					
				// スキル番号で分岐
				switch( skillId ){
					// 体力回复魔法 *************************************************************/
					case B_SKILL_LP_INCREASE_SORO:
					case B_SKILL_LP_INCREASE_AREA:
					case B_SKILL_LP_INCREASE_SIDE:
					
					// サクリファイス ***********************************************************/
					case B_SKILL_SACRIFICE:
					
						// 反射する人がいないとき次の行动へ
						if( pYobiBm->cmbCntMax == 0 ){
							pYobiBm->subActNo = 1; // 次へ
							break;
						}
						
						// 魔法反射する人の行动が終わるまで待つ
						for( i = 0 ; i < pYobiBm->cmbCntMax - 1 ; i++ ){
							// 反射する人の行动が終わったかどうかチェック
							if( pActBc[ pYobiBm->cmbList[ i ] ]->actNo == BC_ACT_STANDBY ){
								// フラグＯＮ
								cnt++;
							}
						}
						
						// 实际に反射する人の行动が終わったかどうかチェック
						if( pActBc[ pYobiBm->cmbList[ pYobiBm->cmbCntMax - 1 ] ]->actNo == BC_ACT_STANDBY2 ){
							cnt++;
						}
						// 行动が終わったとき反射する
						//if( cnt >= pYobiBm->cmbCntMax && pActBc[ myId ]->actNo == BC_ACT_STANDBY ){
						if( cnt >= pYobiBm->cmbCntMax && ( pActBc[ myId ]->actNo == BC_ACT_STANDBY || pActBc[ myId ]->actNo == BC_ACT_STANDBY2 ) ){
						//if( cnt >= pYobiBm->cmbCntMax ){
							// 敌リストをセットする（魔法をかけた侧のＩＤ）
							BattleSetEnemyList( pActBc[ reflectId ], myId, bmFlagBak, point );
							// 反射する人次の行动へ
							BattleNextActList( pActBc[ pYobiBm->cmbList[ pYobiBm->cmbCntMax - 1 ] ] );
							pYobiBm->subActNo = 1; // 次へ
						}
						
						break;
				
					// 攻击魔法（地：单体）*************************************
					case B_SKILL_MAGICATTACK_SORO_EARTH:
					case B_SKILL_MAGICATTACK_SORO_WATER:
					case B_SKILL_MAGICATTACK_SORO_FIRE:
					case B_SKILL_MAGICATTACK_SORO_WIND:
					
					case B_SKILL_MAGICATTACK_AREA_EARTH:
					case B_SKILL_MAGICATTACK_AREA_WATER:
					case B_SKILL_MAGICATTACK_AREA_FIRE:
					case B_SKILL_MAGICATTACK_AREA_WIND:
					
					case B_SKILL_MAGICATTACK_SIDE_EARTH:
					case B_SKILL_MAGICATTACK_SIDE_WATER:
					case B_SKILL_MAGICATTACK_SIDE_FIRE:
					case B_SKILL_MAGICATTACK_SIDE_WIND:
					// ドレイン ******************************************************
					case B_SKILL_DRAIN:
						
						// 反射する人がいないとき次の行动へ
						if( pYobiBm->cmbCntMax == 0 ){
							pYobiBm->subActNo = 1; // 次へ
							break;
						}
						
						// 魔法反射する人の行动が終わるまで待つ
						for( i = 0 ; i < pYobiBm->cmbCntMax - 1 ; i++ ){
							// 反射する人の行动が終わったかどうかチェック
							if( pActBc[ pYobiBm->cmbList[ i ] ]->actNo == BC_ACT_STANDBY ){
								// フラグＯＮ
								cnt++;
							}
						}
						
						// 实际に反射する人の行动が終わったかどうかチェック
						if( pActBc[ pYobiBm->cmbList[ pYobiBm->cmbCntMax - 1 ] ]->actNo == BC_ACT_STANDBY2 ){
							cnt++;
						}
						// 行动が終わったとき反射する
						//if( cnt >= pYobiBm->cmbCntMax && pActBc[ myId ]->actNo == BC_ACT_STANDBY ){
						if( cnt >= pYobiBm->cmbCntMax && ( pActBc[ myId ]->actNo == BC_ACT_STANDBY || pActBc[ myId ]->actNo == BC_ACT_STANDBY2 ) ){
							// 敌リストをセットする（魔法をかけた侧のＩＤ）
							BattleSetEnemyList( pActBc[ reflectId ], myId, bmFlagBak, point, newGraNoBak );
							// 反射する人次の行动へ
							BattleNextActList( pActBc[ pYobiBm->cmbList[ pYobiBm->cmbCntMax - 1 ] ] );
							pYobiBm->subActNo = 1; // 次へ
						}
						
						break;
				}
				break;
		}
		break;
		
			
	case BM_ACT_ITM:	// アイテム ****************************************************/
	
		//pBcYobi = ( BC_YOBI *)pActBc[ myId ]->pYobi;
		
		switch( pYobiBm->subActNo ){
		
			case 0:	// 行动パターンセット
				
				// アイテム番号を読み込む
				itemId = ReadBmDataNum();
				
				// アイテム番号で分岐
				switch( itemId ){
				
					// 体力回复 **********************************************************
					case B_ITEM_RECOVERY_LP:			
					
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                     //MLHIDE
							MessageBox( hWnd, moji, "B_ITEM_RECOVERY_LP", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_ITEM_GENERATE );
						// アイテム回复处理
						BattleSetActList( pActBc[ myId ], BC_ACT_ITEM_RECOVERY );
						
						// エフェクトサイズ
						effectSize = ReadBmDataNum();
						
						// 相手がいなくなるまでループ
						while( 1 ){
							
							// 次の相手を読み込み
							enemyId = ReadBmDataNum();
							
							// 攻击相手がいなくなったら拔ける
							if( enemyId == 0xFF ) break;
							
							// いるかチェック
							if( pActBc[ enemyId ] == NULL ){
								#ifdef _DEBUG
								sprintf( moji, "ＩＤ无效错误。enemyId = %d", enemyId );              //MLHIDE
								MessageBox( hWnd, moji, "B_ITEM_RECOVERY_LP", MB_OK | MB_ICONSTOP ); //MLHIDE
								#endif
								break;
							}
							// ポイントの読み込み
							point = ReadBmDataNum();
							
							// 敌リストをセットする
							BattleSetEnemyList( pActBc[ myId ], enemyId, 0, 0 );
							
							// スキルＩＤ记忆
							//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->skillId = skillId;
							// テックＩＤ记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->techId = 0;
							// 回复エフェクト
							BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_LP_INCREASE );
							// 回复ポイント表示
							BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_POINT_DISP_WAIT );
							
							// 回复ポイント记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = point;
							// 文字色记忆
							( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->color = FONT_PAL_GREEN;
							
						}
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
						
						
					// 封印 **********************************************************
					case B_ITEM_SEAL:
					
						// バトルムービーデータから数字を読み込む
						myId = ReadBmDataNum();		// 自分の番号
						
						// いるかチェック
						if( pActBc[ myId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。myId = %d", myId );                     //MLHIDE
							MessageBox( hWnd, moji, "B_ITEM_RECOVERY_LP", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_STANDBY_WAIT );
						
						// デフォルトの方向へ向ける
						BattleSetActList( pActBc[ myId ], BC_ACT_CHANGE_DEF_DIR );
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_EFFECT_ITEM_GENERATE );
						// 相手を次の行动へ
						BattleSetActList( pActBc[ myId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
						// 行动リストをセットする（自分）
						BattleSetActList( pActBc[ myId ], BC_ACT_STANDBY );
						
						
						// 次の相手を読み込み
						enemyId = ReadBmDataNum();
						
						// 攻击相手がいなくなったら拔ける
						if( enemyId == 0xFF ) break;
						
						// いるかチェック
						if( pActBc[ enemyId ] == NULL ){
							#ifdef _DEBUG
							sprintf( moji, "ＩＤ无效错误。enemyId = %d", enemyId );               //MLHIDE
							MessageBox( hWnd, moji, "B_ITEM_RECOVERY_LP", MB_OK | MB_ICONSTOP ); //MLHIDE
							#endif
							break;
						}
						
						// 成功失败フラグ読み込み
						point = ReadBmDataNum();
						
						// 成功失败フラグ记忆
						( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = point;
						//( ( BC_YOBI *)pActBc[ enemyId ]->pYobi )->point = Rnd( 0, 1 );
						
						// 敌リストをセットする
						BattleSetEnemyList( pActBc[ myId ], enemyId, 0, 0 );
						
						// 行动リストをセットする（相手）
						BattleSetActList( pActBc[ enemyId ], BC_ACT_STANDBY );
						
						// カードを足元に扩大しながら出す
						BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_CARD_APPEAR );
						// ペット戾す演出と共に扩大缩小させる
						BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_MON_SEAL );
						// 終わったらカード缩小
						BattleSetActList( pActBc[ enemyId ], BC_ACT_EFFECT_CARD_DISAPPEAR );
						
						// 敌リストをセットする
						BattleSetEnemyList( pActBc[ enemyId ], myId, 0, 0 );
						// 相手を次の行动へ
						BattleSetActList( pActBc[ enemyId ], BC_ACT_ENEMY_NEXT_ACT_LIST );
						
						// 成功していたら
						if( point == TRUE ){
							// グラフィックジャンプさせる
							BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
							// ジャンプグラフィック番号
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_SEAL;
							// 喜ぶ
							BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY );
							BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY );
							BattleSetActList( pActBc[ myId ], BC_ACT_HAPPY );
							//BattleSetActList( pActBc[ myId ], BC_ACT_CHOKI );
							
							// アクション抹杀
							BattleSetActList( pActBc[ enemyId ], BC_ACT_DEATH_ACT );
						}else{
							// グラフィックジャンプさせる
							BattleSetActList( pActBc[ myId ], BC_ACT_JUMP_DISP );
							// ジャンプグラフィック番号
							( ( BC_YOBI *)pActBc[ myId ]->pYobi )->jumpGraNo = CG_B_STR_STEAL_MISS;
							// 怒る
							BattleSetActList( pActBc[ myId ], BC_ACT_ANGRY );
							BattleSetActList( pActBc[ myId ], BC_ACT_ANGRY );
						}
						
						
						
						
						// 行动リストスタート
						BattleStartActList( pActBc[ myId ] );
						
						pYobiBm->subActNo++; // 次へ
						
						break;
						
						
					// 魔力回复 *************************************************************/
					case B_ITEM_RECOVERY_FP:			
					case B_ITEM_ANABIOSIS:				// 苏生
					case B_ITEM_STATUS_TROUBLE:			// 状态异常
					case B_ITEM_STATUS_RECOVERY:		// 状态异常回复
						
						break;
						
				}
				break;
			
			case 1: // 終了チェック ****************************************************************/
			
				// アイテム番号で分岐
				switch( itemId ){
				
					// 体力回复 **********************************************************
					case B_ITEM_RECOVERY_LP:
						
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
						
					// 封印 **********************************************************
					case B_ITEM_SEAL:
					
						// 終了チェック
						if( BattleCheckActEnd( FALSE ) == TRUE ){
							// デフォルトの方向へ向ける
							ChangeDefDir( pActBc[ myId ] );
							//BattleInitList();
							// ムービー読み込みフラグＯＮ
							ReadBmDataFlag = FALSE;
						}
						break;
					
					// 魔力回复 *************************************************************/
					case B_ITEM_RECOVERY_FP:			
					case B_ITEM_ANABIOSIS:				// 苏生
					case B_ITEM_STATUS_TROUBLE:			// 状态异常
					case B_ITEM_STATUS_RECOVERY:		// 状态异常回复
						
						break;
						
				}
				break;
		}
		break;
		
		
	case BM_ACT_END:	// 战闘終了 ****************************************************/
		
		// 終了チェック
		if( BattleCheckActEnd( FALSE ) == TRUE ){
			// デフォルトの方向へ向ける
			//ChangeDefDir( pActBc[ myIdBak ] );
			//BattleInitList();
			// ムービー読み込みフラグＯＮ
			ReadBmDataFlag = FALSE;
			// 战闘終了演出へ
			SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;
		}
		
		break;
	
	}
}

// バトルキャラクター作成 ***********************************************/
ACTION *MakeBattleChar( int id )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_CHR + 10 * id, sizeof( BC_YOBI ) );
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIT;
	// アニメーションフラグ立てる
	pAct->atr |= ACT_ATR_BTL_CMD_END;
	// マウスの当たり判定ＯＮ
	pAct->atr |= ACT_ATR_INFO;	
	// 战闘时のＹ座标ソート处理ＯＮ
	pAct->atr |= ACT_ATR_BATTLE_SORT;
	// アクションスローフラグＯＮ
	pAct->atr |= ACT_ATR_ACT_SLOW;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	pAct->anim_chr_no = 100500;
	// 步くアニメーション
	pAct->anim_no = ANIM_STAND;
	// 步くアニメーション
	pAct->anim_speed = ANM_NOMAL_SPD;
	// 移动スピード
	pAct->speed = 3;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 1;
	
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_B_CHAR;
	/* 初期位置 */
	pAct->fx = (float)BcPos[ BcPosId[ id ] ].startX;
	pAct->fy = (float)BcPos[ BcPosId[ id ] ].startY;
	// デフォルト位置设定
	pYobi->defX = BcPos[ BcPosId[ id ] ].defX;
	pYobi->defY = BcPos[ BcPosId[ id ] ].defY;
	// スタート位置设定
	pYobi->startX = BcPos[ BcPosId[ id ] ].startX;
	pYobi->startY = BcPos[ BcPosId[ id ] ].startY;
	// 自分のＩＤ番号
	pYobi->myId = id;
	// 行动番号
	pAct->actNo = BC_ACT_STANDBY;
	
	// アニメーション
	pattern( pAct, ANM_LOOP );
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
	return pAct;
}

// バトルマスター作成 ***********************************************/
ACTION *MakeBattleMaster( void )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
	// アクションポインタ取得
	pAct = GetAction( PRIO_MASTER, sizeof( BM_YOBI ) );
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleMaster;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	pAct->anim_chr_no = 0;
	// 行动番号
	//pAct->actNo = BC_APPEAR;
	
	return pAct;
}

// バトル初期化 ****************************************************************/
void InitBattle( void )
{
	int i;

	// バトルマスターアクション作成。
	pActBm = MakeBattleMaster();
	
	// キャラクター分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
		// ポインタ初期化
		pActBc[ i ] = NULL;
	}
	
	// 自分がＡＫＯかフラグ
	MyAkoFlag = FALSE;
	// 地震フラグ初期化
	BattleMapQuakeFlag = FALSE;
	BattleMapAccele = 0;
	// アクションスローフラグ初期化
	ActSlowFlag = FALSE;
	
}

// バトルキャラクターデータから数字を読み込む *************************************/
int ReadBcDataNum( void )
{
	int num = 0, i;
	BOOL flag = FALSE;
	
	while( 1 ){
		// １文捉Kiみ込み
		i = BcData[ BcReadPoint ];
		
		// 文字列の最后まで行ったら
		if( i == NULL ) return num;
		
		// 数字だったら
		if( i >= '0' && i <= '9' || i >= 'A' && i <= 'F' ){ 
		
			// 一文字目の时
			if( flag == FALSE ) flag = TRUE; // フラグＯＮ
			// ２文字目の以降の时
			else num = num << 4; // 一桁上げる（４ビット左シフト）
			
			// 文字を数字に变换
			if( i >= 'A' ) num += i - 'A' + 10;
			else num += i - '0';
			
		}else{
			// ２文字目以降の时
			if( flag == TRUE || i == '|' ){ 
				BcReadPoint++; // 次の文字へ
				return num;
			}
		}
		
		BcReadPoint++; // 次の文字へ
	}
}

// バトルキャラクターデータから文字を読み込む *************************************/
void ReadBcDataStr( char *str, int len )
{
	char c;
	int cnt = 0;
	
	while( 1 ){
		// １文捉Kiみ込み
		c = BcData[ BcReadPoint ];
		
		// リミットチェック
		// 文字列が終った时
		if( c == NULL ) break;
		// 区切り文字の时
		if( c == '|' ){ 
			BcReadPoint++;
			break;
		}
		
		// 文字列オーバーでないとき
		if( cnt < len - 1 ){
			// ＥＵＣの全角なら
			if( c < 0 ){
				str[ cnt++ ] = c;
				// 文字列オーバーでないとき
				if( cnt < len - 1 ){
					str[ cnt++ ] = BcData[ ++BcReadPoint ];
				}else{
					BcReadPoint++;
				}
			}else{
				str[ cnt++ ] = c;
			}
		}
		// 文字列オーバーの时は书き込まない
		else{
			// ＥＵＣの全角なら
			if( c < 0 ){
				BcReadPoint++;
			}
		}
		
		BcReadPoint++;	// 次の文字へ
	}
	// 終端记号
	str[ cnt ] = NULL;
	
	// エスケープ解除
	makeStringFromEscaped( str );
	// ＥＵＣからＳＪＩＳに变换
	eucStringToSjisString( str );
}

// バトルキャラクターデータの読み込み ****************************************/
void ReadBcData( void )
{
	int id = 0;
	int flag = 0;
	ACTION *pAct;
	BC_YOBI *pYobi;
	
#ifdef _DEBUG
	static int num = -1;
	num += 1;
#endif	
	
	// ２バイト目から読みこむ
	BcReadPoint = 2;
	
	//フィールド属性読み込み
	BattleBcFieldFlag = ReadBcDataNum();
	
	// 强制終了の时
	if( BattleBcFieldFlag & BC_FIELD_FLAG_END ) return;
	
	// 属性优遇（地）
	if( BattleBcFieldFlag & BC_FIELD_FLAG_EARTH ){
		// 属性优遇エフェクト作成
		MakeBattleTreatType( SPR_tuci_zoku );
	}else
	// 属性优遇（水）
	if( BattleBcFieldFlag & BC_FIELD_FLAG_WATER ){
		// 属性优遇エフェクト作成
		MakeBattleTreatType( SPR_mizu_zoku );
	}else
	// 属性优遇（火）
	if( BattleBcFieldFlag & BC_FIELD_FLAG_FIRE ){
		// 属性优遇エフェクト作成
		MakeBattleTreatType( SPR_hi_zoku );
	}else
	// 属性优遇（风）
	if( BattleBcFieldFlag & BC_FIELD_FLAG_WIND ){
		// 属性优遇エフェクト作成
		MakeBattleTreatType( SPR_kaze_zoku );
	}else
	// 静寂
	if( BattleBcFieldFlag & BC_FIELD_FLAG_SILENCE ){
		// 静寂
		MakeBattleTreatType( SPR_seijyaku );
	}else{
		// 无いときは属性优遇エフェクト抹杀
		DeathAction( ( ( BM_YOBI *)pActBm->pYobi )->pTreatType );
		// ポインタ初期化
		( ( BM_YOBI *)pActBm->pYobi )->pTreatType = NULL;
	}
	
	// ＢＣの最后までループ
	//while( BcData[ BcReadPointbc_pointer ] != NULL ){
	while( BcData[ BcReadPoint ] != NULL ){
	
		id = ReadBcDataNum();			// ＩＤ番号読み込み
		// リミットチェック
		if( id < 0 || id >= BC_MAX ){
#ifdef _DEBUG
			char moji[ 256 ];
			sprintf( moji, "没找到ＩＤ。BcId = %d", id );                            //MLHIDE
			MessageBox( hWnd, moji, "确认", MB_OK | MB_ICONSTOP );               //MLHIDE
#endif
			id = 0;
		}
		// アクションが无い时
		if( pActBc[ id ] == NULL ){
			pActBc[ id ] = MakeBattleChar( id );
			// リストの初期化（ＩＤ指定版、死んでる人も）
			BattleInitListWithDead( id );
		}
		pAct = pActBc[ id ];				// アクションポインタセット
		pYobi = ( BC_YOBI *)pAct->pYobi;	// 予备构造体アドレス。
		//pAct->func = BattleChar;	// 实行关数登録
		ReadBcDataStr( pAct->name, sizeof( pAct->name ) );			// 名称読み込み
		//ReadBcDataStr( pAct->freeName, sizeof( pAct->freeName ) );	// 称号読み込み
		
#ifdef _DEBUG
		if( offlineFlag == TRUE){
		
#if 1
			pAct->anim_chr_no = ReadBcDataNum() + num;	// グラフィック番号読み込み
			//pAct->anim_chr_no = ReadBcDataNum();	// グラフィック番号読み込み
#else
			ReadBcDataNum();	// グラフィック番号読み込み
			// プレイヤーグラフィック番号読み込み
			//pAct->anim_chr_no = 100000 + Rnd( 0, 6 ) * 25 + Rnd( 0, 23 );
			pAct->anim_chr_no = 106000 + 250 + Rnd( 0, 1 ) * 25 + Rnd( 0, 5 );
			//if( id < 10 ) pAct->anim_chr_no = 101920;
			//pAct->anim_chr_no = ReadBcDataNum();	// グラフィック番号読み込み
			// モンスターグラフィック番号読み込み
			//pAct->anim_chr_no = 101000 + Rnd( 0, 9 ) * 100 + Rnd( 0, 3 );	
			
			//pAct->anim_chr_no = 100000 + Rnd( 0, 23 );	// グラフィック番号読み込み
			//pAct->anim_chr_no = 100327 + Rnd( 0, 23 );	// グラフィック番号読み込み
			//pAct->anim_chr_no = 101823;	// グラフィック番号読み込み
			
			//pAct->anim_chr_no = 101930 + Rnd( 0, 4 );	// グラフィック番号読み込み
			//pAct->anim_chr_no = 101520 + Rnd( 0, 4 );	// グラフィック番号読み込み
			//pAct->anim_chr_no = 100052;	// グラフィック番号読み込み
			
			//pAct->anim_chr_no = 100052 + Rnd( 0, 0 );	// グラフィック番号読み込み
			
			//if( id == 10 ) pAct->anim_chr_no = 101920 + Rnd( 0, 3 );	// グラフィック番号読み込み
			//if( id == 10 ) pAct->anim_chr_no = CG_TITLE;	// グラフィック番号読み込み
			
			//pAct->anim_chr_no = 101122;	// グラフィック番号読み込み
			//pAct->anim_chr_no = 101214;	// グラフィック番号読み込み
#endif
		}else{

#ifdef _CG2_NEWGRAPHIC
			pAct->anim_chr_no = getNewGraphicNo( ReadBcDataNum() );	// グラフィック番号読み込み
#else
			pAct->anim_chr_no = ReadBcDataNum();	// グラフィック番号読み込み
#endif

		}
#else


#ifdef _CG2_NEWGRAPHIC
		pAct->anim_chr_no = getNewGraphicNo( ReadBcDataNum() );	// グラフィック番号読み込み
#else
		pAct->anim_chr_no = ReadBcDataNum();	// グラフィック番号読み込み
#endif


#endif
		
		pAct->level = ReadBcDataNum();	// 等级読み込み
		pAct->hp = ReadBcDataNum();		// ＨＰ読み込み
		pAct->maxHp = ReadBcDataNum();	// 最大ＨＰ読み込み
		pAct->fp = ReadBcDataNum();		// 魔力読み込み
		pAct->maxFp = ReadBcDataNum();	// 最大魔力読み込み
		
		flag = ReadBcDataNum();			// フラグ読み込み
		
		// 登场する时
		if( flag & BC_FLAG_APPEAR ){ 
			pYobi->bcFlag &= ~BC_FLAG_APPEAR;
			/* 初期位置 */
			pAct->fx = (float)BcPos[ BcPosId[ id ] ].startX;
			pAct->fy = (float)BcPos[ BcPosId[ id ] ].startY;
		}else{
			pYobi->bcFlag |= BC_FLAG_APPEAR;
			
			// 死んで无かったら
			if( !( pYobi->bcFlag & BC_FLAG_DEATH ) ){
				/* 初期位置 */
				pAct->fx = (float)BcPos[ BcPosId[ id ] ].defX;
				pAct->fy = (float)BcPos[ BcPosId[ id ] ].defY;
				// デフォルトの方向へ向ける
				ChangeDefDir( pAct );
				// 最初から死んでいたら
				if( flag & BC_FLAG_DEATH ){
					// デフォルトの方向へ向ける
					ChangeDefDir( pAct );
					// 死亡アニメーション
					pAct->anim_no = ANIM_DEAD;
					// アニメーションが終るまでループ
					while( 1 ){
						if( pattern( pAct, ANM_NO_LOOP ) == 1 ) break;
					}
					// 気絶エフェクト作成
					MakeBattleStun( pAct, SPR_piyo );
				}
			}
		}
		
		// 死亡だったらフラグＯＮ
		if( flag & BC_FLAG_DEATH ) pYobi->bcFlag |= BC_FLAG_DEATH;
		else pYobi->bcFlag &= ~BC_FLAG_DEATH;
		// プレイヤーの时
		if( flag & BC_FLAG_PLAYER ) pYobi->bcFlag |= BC_FLAG_PLAYER;
		else pYobi->bcFlag &= ~BC_FLAG_PLAYER;
		// モンスターのとき
		if( flag & BC_FLAG_MON ) pYobi->bcFlag |= BC_FLAG_MON;
		else pYobi->bcFlag &= ~BC_FLAG_MON;
		
		// 毒
		if( flag & BC_FLAG_ABNORMAL_POISON ){
		//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_POISON;
			// マーク作成
			MakeBattleAbnormal( pAct, SPR_doku );
		}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_POISON;
		
		// 眠り
		if( flag & BC_FLAG_ABNORMAL_SLEEP ){
		//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_SLEEP;
			// マーク作成
			MakeBattleAbnormal( pAct, SPR_doku + 1 );
		}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_SLEEP;
		
		// 石化
		if( flag & BC_FLAG_ABNORMAL_STONE ){
		//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_STONE;
			// マーク作成
			MakeBattleAbnormal( pAct, SPR_doku + 2 );
		}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_STONE;
		
		// 酔い
		if( flag & BC_FLAG_ABNORMAL_INEBRIETY ){
		//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_INEBRIETY;
			// マーク作成
			MakeBattleAbnormal( pAct, SPR_doku + 3 );
		}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_INEBRIETY;
		
		// 混乱
		if( flag & BC_FLAG_ABNORMAL_CONFUSION ){
		//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_CONFUSION;
			// マーク作成
			MakeBattleAbnormal( pAct, SPR_doku + 4 );
		}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_CONFUSION;
		
		// 忘却
		if( flag & BC_FLAG_ABNORMAL_FORGET ){
		//	pYobi->bcFlag |= BC_FLAG_ABNORMAL_FORGET;
			// マーク作成
			MakeBattleAbnormal( pAct, SPR_doku + 5 );
		}else pYobi->bcFlag &= ~BC_FLAG_ABNORMAL_FORGET;
		
		// 受伤アップフラグ立っている时
		if( flag & BC_FLAG_DAMAGE_UP )
		{
			// スケール设定
			pAct->scaleX = 1.3f;
			pAct->scaleY = 1.3f;
		}
		else
		{
			// スケール设定
			pAct->scaleX = 1.0f;
			pAct->scaleY = 1.0f;
		}
		
		// ２アクションフラグ立っているとき
		if( flag & BC_FLAG_2ACT ){
			// ２アクションマーク作成
			MakeBattle2Action( pAct, 1 );
		}else{
			// ２アクションマーク抹杀
			DeathAction( ( ( BC_YOBI *)pAct->pYobi )->p2Action );
			// ポインタ初期化
			( ( BC_YOBI *)pAct->pYobi )->p2Action = NULL;
		}
		
		
		// 属性反転フラグ
		if( flag & BC_FLAG_REVERSE_TYPE ){
			pYobi->bcFlag |= BC_FLAG_REVERSE_TYPE;
			// マーク作成
			MakeBattleReverseType( pAct, SPR_zokuhan );
		}else{ 
			pYobi->bcFlag &= ~BC_FLAG_REVERSE_TYPE;
			// 属性反転エフェクト抹杀
			DeathAction( pYobi->pReverseType );
			// ポインタ初期化
			pYobi->pReverseType = NULL;
		}
	
		// 属性反転フラグ
		if( flag & BC_FLAG_MORPH ){
			pYobi->bcFlag |= BC_FLAG_MORPH;
		}
		
		
		// 防御力アップフラグ
		if( flag & BC_FLAG_DEF_UP ){
			// パラメーターアップダウンマーク作成
			MakeBattleParameterUpDown( pAct, 0, 0);
		}else pYobi->bcFlag &= ~BC_FLAG_DEF_UP;
		
		// 防御力ダウンフラグ
		if( flag & BC_FLAG_DEF_DOWN ){
			// パラメーターアップダウンマーク作成
			MakeBattleParameterUpDown( pAct, 1, 0);
		}else pYobi->bcFlag &= ~BC_FLAG_DEF_UP;
		
		// 攻击力アップフラグ
		if( flag & BC_FLAG_ATK_UP ){
			// パラメーターアップダウンマーク作成
			MakeBattleParameterUpDown( pAct, 2, 0);
		}else pYobi->bcFlag &= ~BC_FLAG_ATK_UP;
		
		// 攻击力ダウンフラグ
		if( flag & BC_FLAG_ATK_DOWN ){
			// パラメーターアップダウンマーク作成
			MakeBattleParameterUpDown( pAct, 3, 0);
		}else pYobi->bcFlag &= ~BC_FLAG_ATK_UP;
		
		// すばやさアップフラグ
		if( flag & BC_FLAG_AGL_UP ){
			// パラメーターアップダウンマーク作成
			MakeBattleParameterUpDown( pAct, 4, 0);
		}else pYobi->bcFlag &= ~BC_FLAG_AGL_UP;
		
		// すばやさダウンフラグ
		if( flag & BC_FLAG_AGL_DOWN ){
			// パラメーターアップダウンマーク作成
			MakeBattleParameterUpDown( pAct, 5, 0);
		}else pYobi->bcFlag &= ~BC_FLAG_AGL_UP;

		//pAct->atr |= ACT_ATR_HIT_BOX | ACT_ATR_INFO;	// マウスの当たり判定ＯＮ
		//pAct->atr |= ACT_ATR_INFO;	// マウスの当たり判定ＯＮ
		
		// 一回だけアニメーションさせておく
		//pattern( pAct, ANM_NO_LOOP );
		pattern( pAct, ANM_LOOP );
		
	}

}

// バトルムービーデータから数字を読み込む *************************************/
int ReadBmDataNum( void )
{
	int num = 0, i;
	BOOL flag = FALSE;
	
	while( 1 ){
		// １文捉Kiみ込み
		i = BmData[ BmReadPoint ];
		
		// 文字列の最后まで行ったら
		if( i == NULL ) return num;
		
		// 数字だったら
		if( i >= '0' && i <= '9' || i >= 'A' && i <= 'F' ){ 
		
			// 一文字目の时
			if( flag == FALSE ) flag = TRUE; // フラグＯＮ
			// ２文字目の以降の时
			else num = num << 4; // 一桁上げる（４ビット左シフト）
			
			// 文字を数字に变换
			if( i >= 'A' ) num += i - 'A' + 10;
			else num += i - '0';
			
		}else{
			// ２文字目以降の时
			if( flag == TRUE || i == '|' ){ 
				BmReadPoint++; // 次の文字へ
				return num;
			}
		}
		
		BmReadPoint++; // 次の文字へ
	}
}

// バトルムービーデータから文字列を読み込む *************************************/
void ReadBmDataStr( char *str, int len )
{
	char c;
	int cnt = 0;
	
	while( 1 ){
		// １文捉Kiみ込み
		c = BmData[ BmReadPoint ];
		
		// リミットチェック
		// 文字列が終った时
		if( c == NULL ) break;
		// 区切り文字の时
		if( c == '|' ){ 
			BmReadPoint++;
			break;
		}
		
		// 文字列オーバーでないとき
		if( cnt < len - 1 ){
			// ＥＵＣの全角なら
			if( c < 0 ){
				str[ cnt++ ] = c;
				// 文字列オーバーでないとき
				if( cnt < len - 1 ){
					str[ cnt++ ] = BmData[ ++BmReadPoint ];
				}else{
					BmReadPoint++;
				}
			}else{
				str[ cnt++ ] = c;
			}
		}
		// 文字列オーバーの时は书き込まない
		else{
			// ＥＵＣの全角なら
			if( c < 0 ){
				BmReadPoint++;
			}
		}
		
		BmReadPoint++;	// 次の文字へ
	}
	// 終端记号
	str[ cnt ] = NULL;
	
	// エスケープ解除
	makeStringFromEscaped( str );
	// ＥＵＣからＳＪＩＳに变换
	eucStringToSjisString( str );
}

// バトルムービーデータからコマンドを読み込む *************************************/
int ReadBmDataCmd( char *cmd )
{
	int i;
	
	// 文字列が无かったら
	if( BmData[ BmReadPoint ] == NULL || BmData[ BmReadPoint + 1 ] == NULL ){
		return TRUE;
	}
	
	// 大文字がくるまで进める
	while( 1 ){
		// １文捉Kiみ込み
		i = BmData[ BmReadPoint ];
		
		// 文字列の最后まで行ったら
		if( i == NULL ) return TRUE;
		
		// 大文字だったら
		if( i >= 'A' && i <= 'Z' ) break;
		
		// 一文字进める
		BmReadPoint++;
	}
	
	// ３文字コピー
	strncpy( cmd, &BmData[ BmReadPoint ], 3 );
	// 終端记号
	cmd[ 3 ] = NULL;
	// ３文字进める
	BmReadPoint += 3;
	// もう１文字进める
	BmReadPoint++;
	
	return FALSE;
}

// リストの初期化 **************************************************************/
void BattleInitList( void )
{
	int i;
	// 予备构造体
	BC_YOBI *pYobi;
	
	// 人数分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
		// アクションがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 予备构造体
		pYobi = ( BC_YOBI *)pActBc[ i ]->pYobi;
		// 気絶していたら拔ける
		if( pYobi->bcFlag & BC_FLAG_DEATH ) continue;
		// 画面外に移动中なら拔ける
		if( pActBc[ i ]->actNo == BC_ACT_MOVE_OUT ) continue;
		
		pYobi->hitBackCnt = 0;				// ヒットバック时のカウンター
		pYobi->hitStopCnt = 0;				// ヒットストップ时のカウンター
		// 攻击目标初期化
		pYobi->enemyListCnt = 0;
		pYobi->enemySetListCnt = 0;
		pYobi->enemyList[ 0 ].enemyId = -1;
		pYobi->enemyList[ 1 ].enemyId = -1;
		// 行动リスト初期化
		pYobi->actListCnt = 0;
		pYobi->actSetListCnt = -1;
		pYobi->actList[ 0 ] = BC_ACT_STANDBY;
		//pYobi->actList[ 0 ] = BC_ACT_END;
		pYobi->actList[ 1 ] = (BC_ACT)-1;
		
		// スキルＩＤ初期化
		pYobi->skillId = -1;
		// テックＩＤ初期化
		pYobi->techId = -1;
		
		// 武器坏れたときの新ＳＰＲ番号ストックカウンター
		//BmWeaponBreakBufferStockCnt = 0;
		// 武器坏れたときの新ＳＰＲ番号ゲットカウンター
		//BmWeaponBreakBufferGetCnt = 0;
		
		// Ｂｍチャットバッファーストックカウンター初期化
		//BmChatBufferStockCnt = 0;
		// Ｂｍチャットバッファーゲットカウンター初期化
		//BmChatBufferGetCnt = 0;
		
		// 気絶してなかったら
		if( !( pYobi->bcFlag & BC_FLAG_DEATH ) ){
			// 不意ついた时、つかれた时
			if( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL || BattleBpFlag & BP_FLAG_ENEMY_SURPRISAL ){
				// デフォルトの方向へ向ける
				ChangeDefDir( pActBc[ i ] );
			}
		}
	}
}

// リストの初期化（ＩＤ指定版、死んでる人も） **********************************************/
void BattleInitListWithDead( int id )
{
	// 予备构造体
	BC_YOBI *pYobi;
	
	// アクションがない时返回
	if( pActBc[ id ] == NULL ) return;
	
	// 予备构造体
	pYobi = ( BC_YOBI *)pActBc[ id ]->pYobi;
	
	pYobi->hitBackCnt = 0;				// ヒットバック时のカウンター
	pYobi->hitStopCnt = 0;				// ヒットストップ时のカウンター
	// 攻击目标初期化
	pYobi->enemyListCnt = 0;
	pYobi->enemySetListCnt = 0;
	pYobi->enemyList[ 0 ].enemyId = -1;
	pYobi->enemyList[ 1 ].enemyId = -1;
	// 行动リスト初期化
	pYobi->actListCnt = 0;
	pYobi->actSetListCnt = -1;
	pYobi->actList[ 0 ] = BC_ACT_STANDBY;
	pYobi->actList[ 1 ] = (BC_ACT)-1;
}

// 行动リストをセットする **************************************************************/
void BattleSetActList( ACTION *pAct, BC_ACT actNo )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return;
		
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// カウンタープラス
	pYobi->actSetListCnt++;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->actSetListCnt >= BC_ACT_MAX - 1 ){
		MessageBox( hWnd, "行动列表溢出。Set", "确认", MB_OK | MB_ICONSTOP );        //MLHIDE
	}
#endif

	// 行动リストセット
	pYobi->actList[ pYobi->actSetListCnt ] = actNo;
	pYobi->actList[ pYobi->actSetListCnt + 1 ] = ( BC_ACT )-1;
	
	// 行动フラグＯＮ
	pYobi->actFlag = TRUE;
}

// 行动リストをセットする **************************************************************/
void BattleSetActList( ACTION *pAct, BC_ACT actNo, int actListCnt )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return;
	
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	pYobi->actSetListCnt = actListCnt;
	pYobi->actSetListCnt++;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->actSetListCnt >= BC_ACT_MAX - 1 ){
		MessageBox( hWnd, "行动列表溢出。Set", "确认", MB_OK | MB_ICONSTOP );        //MLHIDE
	}
#endif

	// 行动リストセット
	pYobi->actList[ pYobi->actSetListCnt ] = actNo;
	pYobi->actList[ pYobi->actSetListCnt + 1 ] = ( BC_ACT )-1;
	
	// 行动フラグＯＮ
	pYobi->actFlag = TRUE;
}

// 次の行动リストへ **************************************************************/
void BattleNextActList( ACTION *pAct )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return;
	
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// カウンタープラス
	pYobi->actListCnt++;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->actListCnt >= BC_ACT_MAX ){
		MessageBox( hWnd, "行动列表溢出。Next", "确认", MB_OK | MB_ICONSTOP );       //MLHIDE
		pYobi->actListCnt = 0;
		return;
	}
#endif

	// リミットチェック
	if( pYobi->actListCnt >= BC_ACT_MAX ||
		pYobi->actList[ pYobi->actListCnt ] == -1 ){
		// 待机状态へ
		pAct->actNo = BC_ACT_STANDBY;
		// 行动フラグＯＦＦ
		pYobi->actFlag = FALSE;
		
		return;	
	}
	
	// 行动切り替え
	pAct->actNo = pYobi->actList[ pYobi->actListCnt ];
}

// 行动リストスタート **************************************************************/
void BattleStartActList( ACTION *pAct )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return;
	
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->actListCnt >= BC_ACT_MAX ){
		MessageBox( hWnd, "行动列表溢出。Next", "确认", MB_OK | MB_ICONSTOP );       //MLHIDE
		pYobi->actListCnt = 0;
		return;
	}
#endif
	
	// 行动切り替え
	pAct->actNo = pYobi->actList[ pYobi->actListCnt ];
}

// 敌リストをセットする **************************************************************/
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return;
	
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->enemyListCnt <= -1 || pYobi->enemyListCnt >= BC_ACT_MAX - 1 ){
		MessageBox( hWnd, "敌人列表溢出。Set", "确认", MB_OK | MB_ICONSTOP );        //MLHIDE
		pYobi->enemyListCnt = 0;
	}
#endif
	
	// 敌リストセット
	pYobi->enemyList[ pYobi->enemySetListCnt ].enemyId = enemyId;
	pYobi->enemyList[ pYobi->enemySetListCnt ].bmFlag = bmFlag;
	pYobi->enemyList[ pYobi->enemySetListCnt ].damage = damage;
	pYobi->enemyList[ pYobi->enemySetListCnt + 1 ].enemyId = -1;
	// カウンタープラス
	pYobi->enemySetListCnt++;
}

// 敌リストをセットする **************************************************************/
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage, int newGraNo )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return;
	
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->enemyListCnt <= -1 || pYobi->enemyListCnt >= BC_ACT_MAX - 1 ){
		MessageBox( hWnd, "敌人列表溢出。Set", "确认", MB_OK | MB_ICONSTOP );        //MLHIDE
		pYobi->enemyListCnt = 0;
	}
#endif
	
	// 敌リストセット
	pYobi->enemyList[ pYobi->enemySetListCnt ].enemyId = enemyId;
	pYobi->enemyList[ pYobi->enemySetListCnt ].bmFlag = bmFlag;
	pYobi->enemyList[ pYobi->enemySetListCnt ].damage = damage;
	pYobi->enemyList[ pYobi->enemySetListCnt ].newGraNo = newGraNo;
	pYobi->enemyList[ pYobi->enemySetListCnt + 1 ].enemyId = -1;
	// カウンタープラス
	pYobi->enemySetListCnt++;
}

// 次の敌リストへ **************************************************************/
BOOL BattleNextEnemyList( ACTION *pAct )
{
	// ＮＵＬＬのとき返回
	if( pAct == NULL ) return FALSE;
	
	// 予备构造体
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// カウンタープラス
	pYobi->enemyListCnt++;
	
#ifdef _DEBUG
	// リミットチェック
	if( pYobi->enemyListCnt <= -1 || pYobi->enemyListCnt >= BC_ACT_MAX ){
		MessageBox( hWnd, "敌人列表溢出。Next", "确认", MB_OK | MB_ICONSTOP );       //MLHIDE
		pYobi->enemyListCnt = 0;
		return FALSE;
	}
#endif

#if 0
	// リミットチェック
	if( pYobi->enemyListCnt >= BC_ACT_MAX ||
		pYobi->enemyList[ pYobi->enemyListCnt ].enemyId == -1 ){
		pYobi->enemyListCnt--;
		pAct->actNo = BC_ACT_STANDBY;
		MessageBox( hWnd, "敌人列表溢出。", "确认", MB_OK | MB_ICONSTOP );           //MLHIDE
		
		return FALSE;
	}
#endif
	
	// 狙う敌がおらへんようになったら
	if( pYobi->enemyList[ pYobi->enemyListCnt ].enemyId == -1 ){
	
		return TRUE;
	}
	
	return FALSE;
	
}

// 行动終了チェック *****************************************************/
//
//	int flag：死亡者も见るかフラグ  TRUE:见る　FALSE:见ない
//
//**********************************************************************/
BOOL BattleCheckActEnd( int flag )
{
	// 予备构造体
	BC_YOBI *pYobi;
	int i;
	
	// 人数分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
		// アクションがない时返回
		if( pActBc[ i ] == NULL ) continue;
		// 予备构造体
		pYobi = ( BC_YOBI *)pActBc[ i ]->pYobi;
		// 战闘終了じゃないとき
		if( pActBm->actNo != BM_ACT_END ){ 
			// 気絶を见ないフラグＯＮのとき、
			// 気絶しているときかつ、
			// デフォルト移动のとき、または
			// 倒下演出のとき、または
			// アルティメットＯＫかつスローフラグＯＦＦのとき
			//if( flag == FALSE && pYobi->bcFlag & BC_FLAG_DEATH
			//	&& ( pActBc[ i ]->actNo == BC_ACT_MOVE_DEF 
			//		|| pActBc[ i ]->actNo == BC_ACT_DEAD 
			//		|| pActBc[ i ]->actNo == BC_ACT_AKO1 && pYobi->actSlowFlag == FALSE ) ){
			
			// 気絶を见ないフラグＯＮのとき、
			// 気絶しているときかつ、
			// スローフラグＯＦＦのとき（ヒットストップ中）
			if( flag == FALSE && pYobi->bcFlag & BC_FLAG_DEATH
				&& pYobi->actSlowFlag == FALSE && pActBc[ i ]->actNo != BC_ACT_HIT_BACK ){
				
				// プレイヤーのとき
				//if( pYobi->bcFlag & BC_FLAG_PLAYER ){
					// ペットのＩＤ
				//	int petId = BattleCheckPetId( pYobi->myId );
					// ペットいないとき
				//	if( pActBc[ petId ] == NULL ) continue;
				//}
				continue;
			}
		}
		// 行动中の时
		if( pYobi->actFlag == TRUE ){ 
			return FALSE;
		}
		else
		// 战闘終了じゃないとき
		if( pActBm->actNo != BM_ACT_END ){
		//&& !( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL ) ){ 
			// デフォルト位置に归ってきてないとき
			if( pActBc[ i ]->fx != (float)BcPos[ BcPosId[ i ] ].defX
				|| pActBc[ i ]->fy != (float)BcPos[ BcPosId[ i ] ].defY ){
			
#ifdef PUK2
				// 强制的にデフォルト位置に移动させる
				BattleSetActList( pActBc[ i ], BC_ACT_MOVE_DEF );
				// デフォルトの方向へ向ける
				BattleSetActList( pActBc[ i ], BC_ACT_CHANGE_DEF_DIR );
				// 行动リストスタート
				BattleStartActList( pActBc[ i ] );
#endif				
				return FALSE;
			
			//}else{
				// デフォルトの方向へ向ける
			//	ChangeDefDir( pActBc[ i ] );
			}
		}
	}
	
	return TRUE;
}

#if 0
// Ｂｍフラグにチャット文字列があるかチェック（マスターチェック） *****************************************************
void BattleMastarCheckBmChat( int bmFlag )
{
	char str[ 256 ];

#if 0	
	// 怪我フラグがあるとき
	if( bmFlag & BM_FLAG_INJURY ){
		// Ｂｍデータから文字列を取り出す
		ReadBmDataStr( str, 256 );
		// Ｂｍチャットバッファーにためる
		StockBmChatBuffer( str );
	}
#endif
	// 自分の武器が坏れそうフラグが立っているとき
	if( bmFlag & BM_FLAG_MY_WEPON_BREAK ){
		// Ｂｍデータから文字列を取り出す
		ReadBmDataStr( str, 256 );
		// Ｂｍチャットバッファーにためる
		StockBmChatBuffer( str );
	}
	// 自分の武器が坏れそうフラグが立っているとき
	if( bmFlag & BM_FLAG_MY_WEPON_BROKEN ){
		// Ｂｍデータから文字列を取り出す
		ReadBmDataStr( str, 256 );
		// Ｂｍチャットバッファーにためる
		StockBmChatBuffer( str );
	}
	// 相手の武器が坏れそうフラグが立っているとき
	if( bmFlag & BM_FLAG_OTHER_WEPON_BREAK ){
		// Ｂｍデータから文字列を取り出す
		ReadBmDataStr( str, 256 );
		// Ｂｍチャットバッファーにためる
		StockBmChatBuffer( str );
	}
	// 相手の武器が坏れそうフラグが立っているとき
	if( bmFlag & BM_FLAG_OTHER_WEPON_BROKEN ){
		// Ｂｍデータから文字列を取り出す
		ReadBmDataStr( str, 256 );
		// Ｂｍチャットバッファーにためる
		StockBmChatBuffer( str );
	}
	
}

// Ｂｍチャットバッファーにためる *********************************************
void StockBmChatBuffer( char *moji )
{
	// リミットチェック
	if( BmChatBufferStockCnt >= BM_CHAT_BUFFER_SIZE ) return;
	// Ｂｍチャットバッファーにコピー
	strcpy( BmChatBuffer[ BmChatBufferStockCnt ], moji );
	// カウンタープラス
	BmChatBufferStockCnt++;
}

// Ｂｍチャットバッファーからとって来る *********************************************
char *GetBmChatBuffer( void )
{
	// リミットチェック
	if( BmChatBufferGetCnt > BmChatBufferStockCnt ) return "NULL";       //MLHIDE
	// Ｂｍチャットバッファーにコピー
	return BmChatBuffer[ BmChatBufferGetCnt++ ];
	// カウンタープラス
	//BmChatBufferGetCnt++;
}
#endif

#endif // PUK2

