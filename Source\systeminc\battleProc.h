﻿/************************/
/*	battleProc.h		*/
/************************/
#ifndef _BATTLE_PROC_H_
#define _BATTLE_PROC_H_

// バトルプロセス サブプロセス番号
enum{
	BATTLE_PROC_INIT,				// ０：初始化
	BATTLE_PROC_IN_PRODUCE,			// １：进入战斗
	BATTLE_PROC_RECV_BC_DATA,		// ２：等到ＢＣ受信
	BATTLE_PROC_CHAR_APPEAR,		// ３：登场
	BATTLE_PROC_CMD_INPUT,			// ４：输入指令
	BATTLE_PROC_RECV_MOVIE_DATA,	// ５：等待动画受信
	BATTLE_PROC_MOVIE,				// ６：处理动画
	BATTLE_PROC_OUT_PRODUCE_INIT,	// ７：结束演出初始化
	BATTLE_PROC_OUT_PRODUCE,		// ８：演出结束
#ifdef PUK3_BATTLEEND_LOGOUT
	BATTLE_PROC_LOGOUT_INIT,		// ９：战闘中登出初期化
#endif
};

// エンカウントフラグ
extern BOOL EncountFlag;
// エンカウントの种类
extern int EncountStatus;
// ヘルプ无しの时
extern BOOL NoHelpFlag;
// エンカウントOFFフラグ
extern BOOL EncountOffFlag;
// デュエルフラグ
extern BOOL DuelFlag;

#ifdef PUK2

extern unsigned char efficacydisp;
#ifdef PUK2_TITILEELEMENTEFFECT_2
	extern unsigned char efficacydisp_2;
#endif
enum{
	EFFICACYDISP_NOMAL = 1,		// 通常战闘时表示
	EFFICACYDISP_DUEL = 2,		// デュエル时表示
	EFFICACYDISP_BOSS = 4,		// ボス战时表示
};

#endif

// バトルプロセス 
void BattleProc( void );

// 怪我表示处理
void BattleInjuryDisp( void );

#endif

