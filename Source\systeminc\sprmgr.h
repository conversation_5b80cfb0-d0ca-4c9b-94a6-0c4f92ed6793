/************************/
/*	sprmgr.h			*/
/************************/
#ifndef _SPRMGR_H_
#define _SPRMGR_H_

#include<ddraw.h>
#ifdef PUK2
	#include "directDraw.h"
#endif

#ifdef PUK2
	#define MAX_GRAPHICS_MULTI	 	2200000	// 画像の最大数	新バージョンのグラフィック
#else
#define MAX_GRAPHICS_MULTI	 	595000	// 画像の最大数	新バージョンのグラフィック
#endif
#define MAX_GRAPHICS_SINGLE	 	260000	// 画像の最大数
extern int MAX_GRAPHICS;

//#define MAX_GRAPHICS	 	300000	// 画像の最大数
#define	DEF_COLORKEY		0	// 透明色とする色番号
//#define	DEF_COLORKEY		240	// 透明色とする色番号

#define SURFACE_WIDTH   64 	// 分割サーフェイスの幅
#define SURFACE_HEIGHT  48  // 分割サーフェイスの高さ
//#define SURFACE_WIDTH   32 	// 分割サーフェイスの幅
//#define SURFACE_HEIGHT  24  // 分割サーフェイスの高さ

#ifdef PUK2
	typedef unsigned char PALDATA[3];				// パレットのポインタ
#endif

// サーフェスインフォ构造体
struct surfaceInfo{

#ifdef PUK2
	LPDIRECTDRAWSURFACE7 lpSurface; 	// サーフェスのポインタ
#else
	LPDIRECTDRAWSURFACE lpSurface; 	// サーフェスのポインタ
#endif

	int	bmpNo;						// ビットマップ番号
									// この值が -1 の时はこのサーフェスは空である
	UINT date;						// このサーフェスを使用した日付を记忆
	
	short offsetX;					// 表示位置からのOFFセットＸ座标
	short offsetY;					// 表示位置からのOFFセットＹ座标

	surfaceInfo	*pNext;				// 次のサーフェスインフォ构造体へのポインタ
	short PalNo; 				// 使用しているパレットの番号
	short PalCount; 				// 使用しているパレットのフェードカウント
#ifdef PUK2
	short AnimPalNo;				// 使用しているアニメーションパレットの番号
#endif
#ifdef PUK2_DIFFPAL_SURFACE
	char palchg;					// パレットチェンジ用かしない用かを示す
									// = -1		:サーフェース未使用
									// = 0		:パレットチェンジ无し用サーフェース
									// = 1		:パレットチェンジ有り用サーフェース
									// = 2		:サーフェースは使用中だが、未だ絵が书き込まれていない
	surfaceInfo *otherPalSrf;		// 别のパレットのサーフェースへのポインタ
#endif
};

typedef struct surfaceInfo SURFACE_INFO;

// スプライトインフォ构造体
typedef struct{

//	LPBITMAPINFO lpBmp; 			// ビットマップインフォ构造体へのポインタ
	
	SURFACE_INFO *lpSurfaceInfo; 	// 使用しているサーフェスリストの先头アドレス
								 	// この值が NULL の时はこのＢＭＰは VRAM にはいない
	short width,height;				// ＢＭＰのサイズ
	
#ifdef PUK2
	struct PALLETE_LIST *lpPalList;	// パレットリストへのポインタ
	short AnimPalNo;				// 使用しているアニメーションパレットの番号
	char BmpVer;					// パレットを持っているかのフラグ
#endif
}SPRITE_INFO;

#ifdef PUK2

struct SPRPAL{	//= アニメーション用パレットデータ =//
	short PalNo;				// 使用しているパレットの番号
	short PalCount; 			// 使用しているパレットのフェードカウント
	unsigned long ChgBf[256];	// パレットチェンジ前の现在のＢＰＰ用パレット
	unsigned long ChgAf[256];	// パレットチェンジ后の现在のＢＰＰ用パレット
	PALDATA Base[256];			// 基本パレットデータ(読み込んだ时のままのパレットデータ)
};

struct SPRPAL_MASTER{	//= アニメーション用パレットデータ管理构造体 =//
	struct SPRPAL *dt;			// アニメーション用パレットデータ
	unsigned char useFlag;		// 使用してるかのフラグ
};

#endif

// グローバル变数 ////////////////////////////////////////////////////////////

// スプライトインフォ
extern SPRITE_INFO *SpriteInfo;

// サーフェスインフォ
extern SURFACE_INFO SurfaceInfo[];

// バトルサーフェスのポインタ
#ifdef PUK2
extern LPDIRECTDRAWSURFACE7 lpBattleSurface;
#else
extern LPDIRECTDRAWSURFACE lpBattleSurface;
#endif

// サーフェスの数
extern int SurfaceCnt;

// VRAM に作成したサーフェスの数カウント
extern int VramSurfaceCnt;

// SYSTEMRAM に作成したサーフェスの数カウント
extern int SysramSurfaceCnt;

// サーフェスを使用した日付
extern UINT SurfaceDate;

// 现在のサーフェス检索位置
extern int SurfaceSearchPoint;

#ifdef _DEBUG		
// 现在使っているサーフェスの数カウント
extern int SurfaceUseCnt;

// 现在表示しているサーフェスの数カウント
extern int SurfaceDispCnt;
#endif
// 分割サーフェイスの幅
extern int SurfaceSizeX;
// 分割サーフェイスの高さ
extern int SurfaceSizeY;

#ifdef PUK2
	extern int LoadSprPal[];		// アニメーション用パレットデータの元にする、又はなった絵のグラフィックＩＤ
	extern int SprPalNum;			// パレットの数(アニメーション用)
	extern struct SPRPAL_MASTER *SprPal;	// パレットデータ(アニメーション用)
#endif

#ifdef PUK2
	extern BOOL LimiteLoadBmpFlag;
#endif

// 关数の型宣言 //////////////////////////////////////////////////////////////

/* ビットマップの読み込み *****************************************************/
void OpenBmp( void );      

/* OFFスクリーンサーフェスの作成 *********************************************/
BOOL InitOffScreenSurface( void );

/* サーフェスインフォ构造体の初期化　******************************************/
void InitSurfaceInfo( void );

/* スプライトインフォ构造体の初期化　**************************************/
void InitSpriteInfo( void );

#ifdef PUK2
	// 絵の大きさを取得、サーフェースを确保 ++++
	BOOL GetBmpSize( int bmpNo );
#endif
#ifdef PUK2_DIFFPAL_SURFACE
	// サーフェースを探し、先头のポインタを取得する
	SURFACE_INFO *SearchPaletteSurface( int bmpNo, int AnimPalNo, char palchg );
	// サーフェースを确保 ++++
	void GetPaletteSurface( int bmpNo, int AnimPalNo, char palchg );
#endif

/* ＢＭＰをサーフェスへ割り当て ***********************************************/
void AllocateBmpToSurface( int bmpNo );

// ＢＭＰをロードする *********************************************************/
BOOL LoadBmp( int bmpNo );

// ＢＭＰをロードする（グラフィック番号变换もする） ***************************/
BOOL LoadBmp2( int bmpNo );

#ifdef PUK2
	// ＢＭＰをロードする（グラフィック番号变换もする）（ LoadBmp() の新型パレットチェンジ对应型 ） ***************************/
	BOOL LoadBmp2_PUK2_PAL( int bmpNo, char usechg );

	#ifdef PUK2
		// SprPal初期化关数 ++++
		void InitSprPal();
		// SprPal解放关数 ++++
		void RelSprPal();
		// SprPal解放管理关数 ++++
		void SprPalDeathCheck();
	#endif

	/* ＢＭＰデータをクリアする ***********************************************/
	void ClearBmpData();
#endif

#endif
