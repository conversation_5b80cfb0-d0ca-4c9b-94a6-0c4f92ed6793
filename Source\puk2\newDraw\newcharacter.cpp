﻿/***************************************
			newcharacter.cpp
***************************************/

#ifdef PUK2

BOOL CharHPLPStatusFlag;
BOOL CharFukidashiFlag = TRUE;

// ＮＰＣをＰＣのほうに向かせる ++++
void changeNpcCharDir( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;
	char pcdir;			// ＰＣキャラのいる方向
	short sx,sy;		// ＮＰＣキャラから见た、ＰＣキャラの相对座标

	if( pc.ptAct == NULL ) return;
	if( ptAct == NULL ) return;

	// 新マップのみキャラの向きを变える
	if( SugiMapStat.MapVer ==1 ){
		ext = (CHAREXTRA *)ptAct->pYobi;
		no = ext->charObjTblId;
		// ＮＰＣキャラならＰＣキャラの方へ向ける
		if (charObj[no].type==CHAROBJ_TYPE_NPC){
			sx=(int)pc.ptAct->mx-(int)ptAct->mx;
			sy=(int)pc.ptAct->my-(int)ptAct->my;
			if (sx<0){
				if (sy<0) pcdir=7;
				else if (sy==0) pcdir=6;
				else pcdir=5;
			}else if (sx==0){
				if (sy<0) pcdir=0;
				else if (sy>0) pcdir=4;
			}else{
				if (sy<0) pcdir=1;
				else if (sy==0) pcdir=2;
				else pcdir=3;
			}
			ptAct->anim_ang=pcdir;
		}
	}
}

// キャラに怪我情报を出す
void setCharHealth( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 |= CHR_STATUS2_HEALTH;
}

// キャラに怪我情报を消す
void delCharHealth( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 &= (~CHR_STATUS2_HEALTH);
}

// キャラをスキル使用中にする
void setCharSkill( ACTION *ptAct, int headNo )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 |= CHR_STATUS2_SKILL;
	charObj[no].headNo = headNo;
}

// キャラをスキル使用中から元に戾す
void delCharSkill( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 &= (~CHR_STATUS2_SKILL);
	charObj[no].headNo = 0;
}

// キャラに等级アップマークを表示
void setCharLevelup( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 |= CHR_STATUS2_LEVELUP | CHR_STATUS2_LEVELUPSE;
}

// ペットに怪我情报を出す
void setCharPetHealth( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 |= CHR_STATUS2_PETHEALTH;
}

// ペットに怪我情报を消す
void delCharPetHealth( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL )
		return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 &= (~CHR_STATUS2_PETHEALTH);
}

// ペットに等级アップマークを表示
void setCharPetLevelup( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 |= CHR_STATUS2_PETLEVELUP | CHR_STATUS2_PETLEVELUPSE;
}

#ifdef PUK3_VEHICLE

// キャラを不可视にする
void setCharInvisible( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 |= CHR_STATUS2_INVISIBLE;
}

// キャラを不可视から元に戾す
void delCharInvisible( ACTION *ptAct )
{
	CHAREXTRA *ext;
	int no;

	if( ptAct == NULL ) return;

	ext = (CHAREXTRA *)ptAct->pYobi;
	no = ext->charObjTblId;
	charObj[no].status2 &= (~CHR_STATUS2_INVISIBLE);
}

#endif

// キャラの状态を表示
void CharaStatusDisp( ACTION *pAct, short ofx, short ofy, unsigned long rgba )
{
	struct BLT_MEMBER bm={0};
	short x,y;
	int bmpNo;

	realGetNo( GID_StatusDispLp, (U4 *)&bmpNo );
	realGetPos( bmpNo, &x, &y );

	bm.rgba.rgba = rgba;
	bm.bltf = BLTF_NOCHG;

	bm.BltVer=BLTVER_PUK2;
	bm.u = -16;
	bm.v = -1;
	bm.h = 2;
	if (pAct->maxHp){
		// LP表示
		bm.w = (32 * pAct->hp) / pAct->maxHp;
		StockDispBuffer_PUK2( pAct->x-12+ofx, pAct->y-2+ofy, DISP_PRIO_METER, GID_StatusDispLp, 0, 1, &bm );
	}
	if (pAct->maxFp){
		// FP表示
		bm.w = (32 * pAct->fp) / pAct->maxFp;
		StockDispBuffer_PUK2( pAct->x-12+ofx, pAct->y+1+ofy, DISP_PRIO_METER, GID_StatusDispFp, 0, 1, &bm );
	}

	bm.BltVer=BLTVER_NOMAL;
	// 枠表示
	StockDispBuffer( pAct->x+ofx, pAct->y+ofy, DISP_PRIO_METER, GID_StatusDispWindow, 0, &bm );
}

#ifdef PUK3_PUT_ON
ACTION *makePuton( int graNo )
{
	ACTION *pAct;

	pAct = GetAction( PRIO_CHR, NULL );
	if ( pAct == NULL ) return NULL;

	// 实行关数
	pAct->func = NULL;
	// グラフィックの番号
	pAct->anim_chr_no = graNo;
	// 动作番号
	pAct->anim_no = ANIM_STAND;
	// 表示优先度
	pAct->dispPrio = DISP_PRIO_CHAR;
	// 1行インフォ表示フラグ
	pAct->atr = ACT_ATR_INFO | ACT_ATR_HIT | ACT_ATR_HIDE2;

	return pAct;
}
#endif
#ifdef PUK2_NEW_PETTAKE

// ビットフラグ
enum{
	RCDEX_PUT = 1 << 0,			// 被り物データ
	RCDEX_PETRIDE = 1 << 1,		// モンスターライドデータ
	RCDEX_PETTAKE = 1 << 2,		// モンスター连れ歩きデータ
};
int RecvCharaDataEx( ACTION *pAct, char *bigtoken, int starttoken )
{
#ifdef PUK3_CHECK_VALUE
	char smalltoken[2048];
	int flag;
	CHAREXTRA *pYobi = NULL;
	int token;

	if ( pAct ) pYobi = ( CHAREXTRA *)pAct->pYobi;

	token = starttoken;
	// 最初のトークンはどのデータが送られてきているかのビットフラグ
	getStringToken( bigtoken , DELIMITER , token , sizeof( smalltoken )-1, smalltoken ),	token++;
	flag = getNewGraphicNo( atoi( smalltoken ) );

	// 被り物データが送られてきているなら
	if ( flag & RCDEX_PUT ){
		int pGraNo;
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
#ifdef PUK2
		pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
		pGraNo = atoi( smalltoken );
#endif
#ifdef PUK3_PUT_ON
		if ( pYobi ){
			if (pYobi->ptPuton){
				DeathAction(pYobi->ptPuton);
				pYobi->ptPuton = NULL;
			}
			pYobi->ptPuton = makePuton( pGraNo );
		}
	}else{
		if ( pYobi ){
			if ( pYobi->ptPuton ){
				DeathAction(pYobi->ptPuton);
				pYobi->ptPuton = NULL;
			}
		}
	}
#else
	}
#endif

	// モンスターライドデータが送られてきているなら
	if ( flag & RCDEX_PETRIDE ){
		int pGraNo;
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
#ifdef PUK2
		pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
		pGraNo = atoi( smalltoken );
#endif
#ifdef PUK3_RIDE
		if ( pYobi ){
			// モンスターがでてるなら情报更新
			if (pYobi->ptRide){
				// グラフィックの番号
				pYobi->ptRide->anim_chr_no = pGraNo;

				// キャラの絵をライドキャラの絵に变更
				if ( ( pc.ptAct != NULL && pc.ptAct == pAct ) ||
					 pYobi->charObjTblId == -1 ){
					pAct->anim_chr_no = getRiderCharaGra(pc.graNo);
				}else{
					pAct->anim_chr_no = getRiderCharaGra(charObj[pYobi->charObjTblId].graNo);
				}
			}
			// モンスターが出てないなら作成
			else{
				pYobi->ptRide = GetAction( PRIO_CHR, NULL );
				if ( pYobi->ptRide ){
					// 实行关数
					pYobi->ptRide->func = NULL;
					// グラフィックの番号
					pYobi->ptRide->anim_chr_no = pGraNo;
					// 动作番号
					pYobi->ptRide->anim_no = ANIM_STAND;
					// 表示优先度
					pYobi->ptRide->dispPrio = DISP_PRIO_CHAR;
					// 1行インフォ表示フラグ
					pYobi->ptRide->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
		#ifdef PUK3_RIDE_SOUND
					// ペット扱いで音鸣らさない
					pYobi->ptRide->atr |= ACT_ATR_TYPE_PET;
		#endif
			
					// キャラの絵をライドキャラの絵に变更
					if ( ( pc.ptAct != NULL && pc.ptAct == pAct ) ||
						 pYobi->charObjTblId == -1 ){
						pAct->anim_chr_no = getRiderCharaGra(pc.graNo);
					}else{
						pAct->anim_chr_no = getRiderCharaGra(charObj[pYobi->charObjTblId].graNo);
					}

					// 座りキャラなら
					if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
						pAct->anim_no = ANIM_STAND;
					}
					// 普通のキャラなら
					else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
					else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
					else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
					// モンスターなら
					else{
						pAct->anim_no = ANIM_STAND;
					}
				}
			}
		}
	}else{
		if ( pYobi ){
			if ( pYobi->ptRide ){
				pAct->actNo = CHARACT_GET_OFF;
				pYobi->actCnt = 0;
			}
		}
	}
#else
	}
#endif
	

	// モンスター连れ歩きデータが送られてきているなら
	if ( flag & RCDEX_PETTAKE ){
		int pGraNo;
		int pDir,pDir2;
		int pLv;
		int pCol;
		char pName[256];
		int x, y;

		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
#ifdef PUK2
		pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
		pGraNo = atoi( smalltoken );
#endif
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
		pDir = atoi( smalltoken );
		pDir2 = ( pDir / 10 ) - 1;
		pDir = ( pDir % 10 ) - 1;
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1,smalltoken ),	token++;
		pLv = atoi( smalltoken );
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1,smalltoken ),	token++;
		pCol = atoi( smalltoken );
		getStringToken( bigtoken, DELIMITER, token, sizeof( pName )-1, pName ),	token++;
		makeRecvString( pName );

		if ( pAct ){ 
			x = pAct->gx,	y = pAct->gy;

			if ( pYobi ){
				// ペットがいる场合は一旦ペットを消す
				if( pYobi->ptPet != NULL ){
					DeathAction( pYobi->ptPet );
					pYobi->ptPet = NULL;
				}
				if( pDir != -1 ){
					x += moveAddTbl[pDir][0];
					y += moveAddTbl[pDir][1];
				}
				pYobi->ptPet = createCharAction( pGraNo, x, y, pDir2 );

				if ( pYobi->ptPet ){
					if( pc.ptAct == pAct ){
						(( CHAREXTRA *)pYobi->ptPet->pYobi)->charObjTblId = -1;
					} else {
						(( CHAREXTRA *)pYobi->ptPet->pYobi)->charObjTblId = pYobi->charObjTblId;
					}
	#ifdef PUK2
					pYobi->ptPet->atr |= ACT_ATR_TYPE_OTHER_PC | ACT_ATR_TYPE_TAKENPET;
	#else
					pYobi->ptPet->atr |= ACT_ATR_TYPE_OTHER_PC;
	#endif

					strcpy( pYobi->ptPet->name, pName );
					pYobi->ptPet->level = pLv;
					pYobi->ptPet->itemNameColor = pCol;
				}
			}
		}
	} else{
		if( pYobi ) {
			if( pYobi->ptPet != NULL ) {
				// ペットをつれていて、连れ歩きの情报が无い场合はペットを消す
				DeathAction( pYobi->ptPet );
				pYobi->ptPet = NULL;
			}
		}
	}

	return token;
#else
	char smalltoken[2048];
	int flag;
#ifdef PUK3_CHECK_VALUE
	CHAREXTRA *pYobi = NULL;
	int token;

	if ( pAct ) pYobi = ( CHAREXTRA *)pAct->pYobi;
#else
	CHAREXTRA *pYobi = ( CHAREXTRA *)pAct->pYobi;
	int token;
#endif

	token = starttoken;
	// 最初のトークンはどのデータが送られてきているかのビットフラグ
	getStringToken( bigtoken , DELIMITER , token , sizeof( smalltoken )-1, smalltoken ),	token++;
	flag = getNewGraphicNo( atoi( smalltoken ) );

	// 被り物データが送られてきているなら
	if ( flag & RCDEX_PUT ){
		int pGraNo;
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
#ifdef PUK2
		pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
		pGraNo = atoi( smalltoken );
#endif
#ifdef PUK3_PUT_ON
	#ifdef PUK3_CHECK_VALUE
		if ( pYobi ){
			if (pYobi->ptPuton){
				DeathAction(pYobi->ptPuton);
				pYobi->ptPuton = NULL;
			}
			pYobi->ptPuton = makePuton( pGraNo );
		}
	}else if ( pYobi && pYobi->ptPuton ){
	#else
		if (pYobi->ptPuton){
			DeathAction(pYobi->ptPuton);
			pYobi->ptPuton = NULL;
		}
		pYobi->ptPuton = makePuton( pGraNo );
	}else if (pYobi->ptPuton){
	#endif
		DeathAction(pYobi->ptPuton);
		pYobi->ptPuton = NULL;
	}
#else
	}
#endif

	// モンスターライドデータが送られてきているなら
	if ( flag & RCDEX_PETRIDE ){
		int pGraNo;
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
#ifdef PUK2
		pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
		pGraNo = atoi( smalltoken );
#endif
#ifdef PUK3_RIDE
	#ifdef PUK3_CHECK_VALUE
		if ( pYobi ){
	#endif
		// モンスターがでてるなら情报更新
		if (pYobi->ptRide){
			// グラフィックの番号
			pYobi->ptRide->anim_chr_no = pGraNo;

			// キャラの絵をライドキャラの絵に变更
			if ( ( pc.ptAct != NULL && pc.ptAct == pAct ) ||
				 pYobi->charObjTblId == -1 ){
				pAct->anim_chr_no = getRiderCharaGra(pc.graNo);
			}else{
				pAct->anim_chr_no = getRiderCharaGra(charObj[pYobi->charObjTblId].graNo);
			}
		}
		// モンスターが出てないなら作成
		else{
			pYobi->ptRide = GetAction( PRIO_CHR, NULL );
	#ifdef PUK3_CHECK_VALUE
			if ( pYobi->ptRide ){
	#endif
		
			// 实行关数
			pYobi->ptRide->func = NULL;
			// グラフィックの番号
			pYobi->ptRide->anim_chr_no = pGraNo;
			// 动作番号
			pYobi->ptRide->anim_no = ANIM_STAND;
			// 表示优先度
			pYobi->ptRide->dispPrio = DISP_PRIO_CHAR;
			// 1行インフォ表示フラグ
			pYobi->ptRide->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
#ifdef PUK3_RIDE_SOUND
			// ペット扱いで音鸣らさない
			pYobi->ptRide->atr |= ACT_ATR_TYPE_PET;
#endif
	
			// キャラの絵をライドキャラの絵に变更
			if ( ( pc.ptAct != NULL && pc.ptAct == pAct ) ||
				 pYobi->charObjTblId == -1 ){
				pAct->anim_chr_no = getRiderCharaGra(pc.graNo);
			}else{
				pAct->anim_chr_no = getRiderCharaGra(charObj[pYobi->charObjTblId].graNo);
			}

			// 座りキャラなら
			if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
				pAct->anim_no = ANIM_STAND;
			}
			// 普通のキャラなら
			else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
			else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
			else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
			// モンスターなら
			else{
				pAct->anim_no = ANIM_STAND;
			}
	#ifdef PUK3_CHECK_VALUE
			}
	#endif
		}
	#ifdef PUK3_CHECK_VALUE
		}
	}else if ( pYobi && pYobi->ptRide ){
	#else
	}else if (pYobi->ptRide){
	#endif
		pAct->actNo = CHARACT_GET_OFF;
		pYobi->actCnt = 0;
	}
#else
	}
#endif
	

	// モンスター连れ歩きデータが送られてきているなら
	if ( flag & RCDEX_PETTAKE ){
		int pGraNo;
		int pDir,pDir2;
		int pLv;
		int pCol;
		char pName[256];
		int x, y;

		x = pAct->gx,	y = pAct->gy;

		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
#ifdef PUK2
		pGraNo = getNewGraphicNo( atoi( smalltoken ) );
#else
		pGraNo = atoi( smalltoken );
#endif
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1, smalltoken ),	token++;
		pDir = atoi( smalltoken );
		pDir2 = ( pDir / 10 ) - 1;
		pDir = ( pDir % 10 ) - 1;
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1,smalltoken ),	token++;
		pLv = atoi( smalltoken );
		getStringToken( bigtoken, DELIMITER, token, sizeof( smalltoken )-1,smalltoken ),	token++;
		pCol = atoi( smalltoken );
		getStringToken( bigtoken, DELIMITER, token, sizeof( pName )-1, pName ),	token++;
		makeRecvString( pName );

	#ifdef PUK3_CHECK_VALUE
		if ( pYobi ){
	#endif
		// ペットがいる场合は一旦ペットを消す
		if( pYobi->ptPet != NULL ){
			DeathAction( pYobi->ptPet );
			pYobi->ptPet = NULL;
		}
		if( pDir != -1 ){
			x += moveAddTbl[pDir][0];
			y += moveAddTbl[pDir][1];
		}
		pYobi->ptPet = createCharAction( pGraNo, x, y, pDir2 );

	#ifdef PUK3_CHECK_VALUE
		if ( pYobi->ptPet ){
	#endif
		if( pc.ptAct == pAct ){
			(( CHAREXTRA *)pYobi->ptPet->pYobi)->charObjTblId = -1;
		} else {
			(( CHAREXTRA *)pYobi->ptPet->pYobi)->charObjTblId = pYobi->charObjTblId;
		}
#ifdef PUK2
		pYobi->ptPet->atr |= ACT_ATR_TYPE_OTHER_PC | ACT_ATR_TYPE_TAKENPET;
#else
		pYobi->ptPet->atr |= ACT_ATR_TYPE_OTHER_PC;
#endif

		strcpy( pYobi->ptPet->name, pName );
		pYobi->ptPet->level = pLv;
		pYobi->ptPet->itemNameColor = pCol;
	#ifdef PUK3_CHECK_VALUE
		}
		}
	} else if( pYobi && pYobi->ptPet != NULL ) {
	#else
	} else if( pYobi->ptPet != NULL ) {
	#endif
		// ペットをつれていて、连れ歩きの情报が无い场合はペットを消す
		DeathAction( pYobi->ptPet );
		pYobi->ptPet = NULL;
	}

	return token;
#endif
}

#endif

#ifdef PUK3_CHARA_ACT

// 呼びもとの关数が終了して欲しいならTRUE、続行して欲しいならFALSE
BOOL charAction( ACTION *pAct )
{
	CHAREXTRA *ext = (CHAREXTRA *)pAct->pYobi;
	int charNum, tblId = ext->charObjTblId;
	float mx,my;

	// 画面表示位置
	camMapToGamen( pAct->mx, pAct->my, &mx, &my );
	pAct->x = (int)(mx+.5);
	pAct->y = (int)(my+.5);

	// 各行动をとったら終了
	switch(pAct->actNo){
	case CHARACT_NOMAL:
		return FALSE;

#ifdef PUK3_RIDE
	case CHARACT_RIDE_ON:
		// 移动处理
		charMove( pAct );
		// 移动后の向き换え
		if( pAct->vx == 0 && pAct->vy == 0
		 && charObj[tblId].stockDir != -1
		 && charObj[tblId].stockDirX == pAct->gx && charObj[tblId].stockDirY == pAct->gy )
		{
			pAct->anim_ang = charObj[tblId].stockDir;
			charObj[tblId].stockDir = -1;
			charObj[tblId].stockDirX = 0;
			charObj[tblId].stockDirY = 0;
		}

		// ライド中なら
		if ( ext->ptRide ){
			int x, y;
			ext->ptRide->anim_ang = pAct->anim_ang;

			x = (ext->ptRide->anim_cdx * ext->actCnt) / (RIDE_JMPCNT_HALF * 2);

			y = ext->actCnt - RIDE_JMPCNT_HALF;
			y *= y;
			y = RIDE_JMPCNT_HALF*RIDE_JMPCNT_HALF - y;
			y = - y;
			if ( ext->actCnt > RIDE_JMPCNT_HALF ){
				if ( ext->ptRide->anim_cdy <= y ) y = ext->ptRide->anim_cdy;
			}

			pAct->anim_no = ANIM_SIT;

			// キャラのアニメーション
			pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );

			// キャラを描画
#ifdef PUK2_DRESS_UP
			charNum =
#endif
	#ifdef PUK2
			setMapChar( pAct->bmpNo, pAct->mx, pAct->my+0.001f,
				 pAct->x+Blt_adjust(pAct,0) + x,
				 pAct->y+Blt_adjust(pAct,1) + y, 1, &pAct->bm );
	#else
			setMapChar( pAct->bmpNo, pAct->mx, pAct->my+0.001f,
				 pAct->x + x, pAct->y + y, 1 );
	#endif

			// ペットのアニメーション
			pattern( ext->ptRide, ANM_NOMAL_SPD, ANM_LOOP, 0 );

			// ペットを描画
#ifdef PUK2_DRESS_UP
			setMapCharWith( charNum, FALSE, ext->ptRide->bmpNo,
				 pAct->x+Blt_adjust(ext->ptRide,0),
				 pAct->y+Blt_adjust(ext->ptRide,1), &ext->ptRide->bm );
#else
	#ifdef PUK2
			setMapChar( ext->ptRide->bmpNo, pAct->mx, pAct->my, pAct->x+Blt_adjust(ext->ptRide,0), pAct->y+Blt_adjust(ext->ptRide,1), 1, &ext->ptRide->bm );
	#else
			setMapChar( ext->ptRide->bmpNo, pAct->mx, pAct->my, pAct->x, pAct->y, 1 );
	#endif
#endif

			// 召唤エフェクト
			if ( !ext->ptActMagicEffect ){
				ext->ptActMagicEffect = GetAction( PRIO_CHR, NULL );
			
				// 实行关数
				ext->ptActMagicEffect->func = NULL;
				// グラフィックの番号
				ext->ptActMagicEffect->anim_chr_no = SPR_tukaima;
				// 动作番号
				ext->ptActMagicEffect->anim_no = ANIM_STAND;
				// 表示优先度
				ext->ptActMagicEffect->dispPrio = DISP_PRIO_CHAR;
				// 1行インフォ表示フラグ
				ext->ptActMagicEffect->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
			}
			if ( ext->ptActMagicEffect ){
				pattern( ext->ptActMagicEffect, ANM_NOMAL_SPD, ANM_LOOP, 0 );
#ifdef PUK2_DRESS_UP
				setMapCharWith( charNum, TRUE, ext->ptActMagicEffect->bmpNo,
					 pAct->x+Blt_adjust(ext->ptActMagicEffect,0),
					 pAct->y+Blt_adjust(ext->ptActMagicEffect,1), &ext->ptActMagicEffect->bm );
#else
	#ifdef PUK2
				setMapChar( ext->ptActMagicEffect->bmpNo, pAct->mx, pAct->my, pAct->x+Blt_adjust(ext->ptActMagicEffect,0), pAct->y+Blt_adjust(ext->ptActMagicEffect,1), 1, &ext->ptActMagicEffect->bm );
	#else
				setMapChar( ext->ptActMagicEffect->bmpNo, pAct->mx, pAct->my, pAct->x, pAct->y, 1 );
	#endif
#endif
			}
			if ( !ext->actCnt &&  RIDE_SE_GETON_START > 0 ) play_se( RIDE_SE_GETON_START, pAct->x, pAct->y );

			ext->actCnt++;

			if ( ext->actCnt > RIDE_JMPCNT_HALF &&
				 ext->ptRide->anim_cdy <= y ){
				pAct->actNo = CHARACT_NOMAL;
				// 座りキャラなら
				if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
					pAct->anim_no = ANIM_STAND;
				}
				// 普通のキャラなら
				else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
				else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
				else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
				// モンスターなら
				else{
					pAct->anim_no = ANIM_STAND;
				}
				if ( RIDE_SE_GETON_END > 0 ) play_se( RIDE_SE_GETON_END, pAct->x, pAct->y );

				if ( ext->ptActMagicEffect ){
					DeathAction(ext->ptActMagicEffect);
					ext->ptActMagicEffect = NULL;
				}
			}
		}else{
			pAct->actNo = CHARACT_NOMAL;
			// 座りキャラなら
			if ( SPR_s_000 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_s_kage_pcm ){
				pAct->anim_no = ANIM_STAND;
			}
			// 普通のキャラなら
			else if ( SPRPC_START <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END ) pAct->anim_no = ANIM_SIT;
			else if ( SPRPC_START_V2 <= pAct->anim_chr_no && pAct->anim_chr_no <= SPRPC_END_V2 ) pAct->anim_no = ANIM_SIT;
			else if ( SPR_rt00_ax <= pAct->anim_chr_no && pAct->anim_chr_no <= SPR_rk00_sp ) pAct->anim_no = ANIM_SIT;
			// モンスターなら
			else{
				pAct->anim_no = ANIM_STAND;
			}
		}
		return TRUE;

	case CHARACT_GET_OFF:
		// 移动处理
		charMove( pAct );
		// 移动后の向き换え
		if( pAct->vx == 0 && pAct->vy == 0
		 && charObj[tblId].stockDir != -1
		 && charObj[tblId].stockDirX == pAct->gx && charObj[tblId].stockDirY == pAct->gy )
		{
			pAct->anim_ang = charObj[tblId].stockDir;
			charObj[tblId].stockDir = -1;
			charObj[tblId].stockDirX = 0;
			charObj[tblId].stockDirY = 0;
		}

		// ライド中なら
		if ( ext->ptRide ){
			int x, y;
			ext->ptRide->anim_ang = pAct->anim_ang;

			x = ext->ptRide->anim_cdx - (ext->ptRide->anim_cdx * ext->actCnt) / (RIDE_JMPCNT_HALF * 2);

			if ( ext->actCnt == 0 ){
				for(;ext->actCnt<RIDE_JMPCNT_HALF;ext->actCnt++){
					y = ext->actCnt - RIDE_JMPCNT_HALF;
					y *= y;
					y = RIDE_JMPCNT_HALF*RIDE_JMPCNT_HALF - y;
					y = - y;
					if ( ext->ptRide->anim_cdy >= y ) break;
				}
			}
			y = ext->actCnt - RIDE_JMPCNT_HALF;
			y *= y;
			y = RIDE_JMPCNT_HALF*RIDE_JMPCNT_HALF - y;
			y = - y;
			if ( ext->actCnt > RIDE_JMPCNT_HALF ){
				if ( 0 <= y ) y = 0;
			}

			pAct->anim_no = ANIM_SIT;

			// キャラのアニメーション
			pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );

			// キャラを描画
#ifdef PUK2_DRESS_UP
			charNum =
#endif
	#ifdef PUK2
			setMapChar( pAct->bmpNo, pAct->mx, pAct->my+0.001f,
				 pAct->x+Blt_adjust(pAct,0) + x,
				 pAct->y+Blt_adjust(pAct,1) + y, 1, &pAct->bm );
	#else
			setMapChar( pAct->bmpNo, pAct->mx, pAct->my+0.001f,
				 pAct->x + x, pAct->y + y, 1 );
	#endif

			// ペットのアニメーション
			pattern( ext->ptRide, ANM_NOMAL_SPD, ANM_LOOP, 0 );

			// ペットを描画
#ifdef PUK2_DRESS_UP
			setMapCharWith( charNum, FALSE, ext->ptRide->bmpNo,
				 pAct->x+Blt_adjust(ext->ptRide,0),
				 pAct->y+Blt_adjust(ext->ptRide,1), &ext->ptRide->bm );
#else
	#ifdef PUK2
			setMapChar( ext->ptRide->bmpNo, pAct->mx, pAct->my, pAct->x+Blt_adjust(ext->ptRide,0), pAct->y+Blt_adjust(ext->ptRide,1), 1, &ext->ptRide->bm );
	#else
			setMapChar( ext->ptRide->bmpNo, pAct->mx, pAct->my, pAct->x, pAct->y, 1 );
	#endif
#endif

			// 召唤エフェクト
			if ( !ext->ptActMagicEffect ){
				ext->ptActMagicEffect = GetAction( PRIO_CHR, NULL );
			
				// 实行关数
				ext->ptActMagicEffect->func = NULL;
				// グラフィックの番号
				ext->ptActMagicEffect->anim_chr_no = SPR_fuuin;
				// 动作番号
				ext->ptActMagicEffect->anim_no = ANIM_STAND;
				// 表示优先度
				ext->ptActMagicEffect->dispPrio = DISP_PRIO_CHAR;
				// 1行インフォ表示フラグ
				ext->ptActMagicEffect->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
			}
			if ( ext->ptActMagicEffect ){
				pattern( ext->ptActMagicEffect, ANM_NOMAL_SPD, ANM_LOOP, 0 );
#ifdef PUK2_DRESS_UP
				setMapCharWith( charNum, TRUE, ext->ptActMagicEffect->bmpNo,
					 pAct->x+Blt_adjust(ext->ptActMagicEffect,0),
					 pAct->y+Blt_adjust(ext->ptActMagicEffect,1), &ext->ptActMagicEffect->bm );
#else
	#ifdef PUK2
				setMapChar( ext->ptActMagicEffect->bmpNo, pAct->mx, pAct->my, pAct->x+Blt_adjust(ext->ptActMagicEffect,0), pAct->y+Blt_adjust(ext->ptActMagicEffect,1), 1, &ext->ptActMagicEffect->bm );
	#else
				setMapChar( ext->ptActMagicEffect->bmpNo, pAct->mx, pAct->my, pAct->x, pAct->y, 1 );
	#endif
#endif
			}

			if ( !ext->actCnt &&  RIDE_SE_GETOFF_START > 0 ) play_se( RIDE_SE_GETOFF_START, pAct->x, pAct->y );
			ext->actCnt++;

			if ( ext->actCnt > RIDE_JMPCNT_HALF &&
				 0 <= y ){
				pAct->actNo = CHARACT_NOMAL;
				pAct->anim_no = ANIM_STAND;

				DeathAction(ext->ptRide);
				ext->ptRide = NULL;
				if ( ( pc.ptAct != NULL && pc.ptAct == pAct ) ||
					 ext->charObjTblId == -1 ){
					pAct->anim_chr_no = pc.graNo;
				}else{
					pAct->anim_chr_no = charObj[ext->charObjTblId].graNo;
				}

				if ( RIDE_SE_GETOFF_END > 0 ) play_se( RIDE_SE_GETOFF_END, pAct->x, pAct->y );
				if ( ext->ptActMagicEffect ){
					DeathAction(ext->ptActMagicEffect);
					ext->ptActMagicEffect = NULL;
				}
			}
		}else{
			pAct->actNo = CHARACT_NOMAL;
			pAct->anim_no = ANIM_STAND;

			DeathAction(ext->ptRide);
			ext->ptRide = NULL;
			if ( ( pc.ptAct != NULL && pc.ptAct == pAct ) ||
				 ext->charObjTblId == -1 ){
				pAct->anim_chr_no = pc.graNo;
			}else{
				pAct->anim_chr_no = charObj[ext->charObjTblId].graNo;
			}
		}
		return TRUE;
#endif
#ifdef PUK3_VEHICLE
	case CHARACT_VEHICLE_RETURN:	// 乘り物が归还する
		{
			int x, y;
			double dx, dy;
			int cnt;
			float mx, my;
			short anim_angToang[8] = {  45,   0, 315, 270, 225, 180, 135,  90 };
//			short anim_angToang[8] = { 225, 180, 135,  90,  45,   0, 315, 270 };

			// 画面表示位置
			camMapToGamen( pAct->mx, pAct->my, &mx, &my );
			x = (int)(mx+.5);
			y = (int)(my+.5);

			if ( ext->actCnt <= 360 ){
				cnt = 360 - ext->actCnt;
	
				pAct->anim_no = ANIM_B_WALK;
	
				// 时间で上升する
				y -= (int)(cnt * 1.4f);

				if ( ext->actCnt == 0 ) pAct->delta = pAct->anim_ang;

				// キャラの向きを设定
				pAct->anim_ang = pAct->delta - (int)( ( (cnt+8) % 120 ) / 15 );
				if ( pAct->anim_ang < 0 ) pAct->anim_ang += 8;
				pAct->anim_ang %= 8;
	
				// 60フレームで一回転
				dx =  cos( (double)( cnt*3 + anim_angToang[(pAct->delta+4)%8] ) * 3.1415926535 / 180 );
				dy = -sin( (double)( cnt*3 + anim_angToang[(pAct->delta+4)%8] ) * 3.1415926535 / 180 );
	
				x += (int)( dx * ( (float)cnt * 1.75f) );
				y += (int)( dy * ( (float)cnt * 0.6f) );
			}else{
				pAct->anim_no = ANIM_B_WALK_END;
				// キャラのアニメーション
				if ( pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP, 0 ) == 1 ){
					pAct->actNo = CHARACT_NOMAL;
					ext->actCnt = 0;

					pAct->anim_no = ANIM_STAND;
				}
			}

			// キャラのアニメーション
			pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP, 0 );
	
			// キャラを描画
			setMapChar( pAct->bmpNo, pAct->mx, pAct->my,
				 x+Blt_adjust(pAct,0),
				 y+Blt_adjust(pAct,1), 1, &pAct->bm );

			ext->actCnt++;
		}
		return TRUE;
#endif
	default:
		pAct->actNo = CHARACT_NOMAL;
	}
	return FALSE;
}

#endif

#ifdef PUK3
	#include "../../puk3/character/ride_character.cpp"
	#include "../../puk3/character/vehicle.cpp"
#endif

#endif