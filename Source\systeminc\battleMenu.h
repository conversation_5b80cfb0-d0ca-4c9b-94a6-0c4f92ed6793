﻿/************************/
/*	battleMenu.h		*/
/************************/
#ifndef _BATTLE_MENU_H_
#define _BATTLE_MENU_H_

// BattleBpFlagのビット情报
//#define BATTLE_BP_JOIN				( 1 << 0 )		// 参战
//#define BATTLE_BP_PLAYER_MENU_NON	( 1 << 1 )		// プレイヤーメニューなし
//#define BATTLE_BP_BOOMERANG			( 1 << 2 )		// ブーメラン
//#define BATTLE_BP_PET_MENU_NON		( 1 << 3 )		// ペットメニューなし
//#define BATTLE_BP_ENEMY_SURPRISAL	( 1 << 4 )		// 敌が不意打ついた时（不意つかれた时）
//#define BATTLE_BP_PLAYER_SURPRISAL	( 1 << 5 )		// プレイヤーが不意打をついた时

// 战闘ウィンドウアクションポインタ
extern ACTION *pActBattleWnd;
// バトルメニューフラグ
extern int battleMenuFlag2;

// 自分の配列番号
extern int BattleMyNo;
// 自分のＩＤバックアップ
extern int BattleMyNoBak;
#ifdef PUK3_RIDE_BATTLE
	// その场に出ているペットのインデックス
	extern int BattleNowPetNo;
	// ライドペットのインデックス
	extern int BattleRidePetNo;
#endif
// ＢＰフラグ
extern int BattleBpFlag;
// 战闘时の自分のＭＰ
extern int BattleMyMp;
// 使用できるスキルフラグ
extern int BattleUsableSkillFlag;
// ペットの使用できるスキルフラグ
extern int BattlePetUsableSkillFlag;
#ifdef PUK2
// リバースの等级（０ならリバース使用不可）
extern int BattleRebirthLevel;
#endif

// バトルキャラクターフィールドフラグ
extern int BattleBcFieldFlag;

// 战闘强制終了フラグ
extern int BattleEscFlag;
// 参战ペット番号バックアップ
extern int battlePetNoBak;
// 参战ペット番号バックアップ２
extern int battlePetNoBak2;

// コマンド入力济みフラグ
extern int BattleAnimFlag;
// ターン受信フラグ
extern BOOL BattleTurnReceiveFlag;
// 现在のクライアントターン番号
extern int BattleCliTurnNo;
// 现在のサーバーターン番号
extern int BattleSvTurnNo;

// バトルメニュープロセス番号
typedef enum{
	B_MENU_PLAYER_INIT,		// プレイヤーメニュー初期化
	B_MENU_PLAYER,			// プレイヤーメニュー处理
	B_MENU_PLAYER_BACK,		// プレイヤーメニュー消す
	B_MENU_PET_INIT,		// ペットメニュー初期化
	B_MENU_PET,				// ペットメニュー
	B_MENU_PET_BACK,		// ペットメニュー消す
}B_MENU_PROC_NO;

// バトルメニュープロセス番号
extern B_MENU_PROC_NO BattleMenuProcNo;

// プレイヤーコマンド入力济みフラグ
extern int BattleCmdPlayerInputFlag;
// ペットコマンド入力济みフラグ
extern int BattleCmdPetInputFlag;

// ペットのバックアップ番号记忆用
extern int BattlePetNoBak;
// ペットのスキルバックアップ番号记忆用
extern int BattlePetSkillSelectNo;

// バトルメニュー处理 *********************************************************/
void BattleMenuProc( void );
// バトルメニュー初期化 ********************************************************/
void InitBattleMenu( void );
// ターン每のバトルメニュー初期化 *******************************************************/
void InitTurnBattleMenu( void );
// コマンド入力济みフラグのチェック ********************************************/
void CheckBattleAnimFlag( void );
// 一人用か二人用かチェック ********************************************/
int CheckBattle1P2P( void );
// 耐久力メーター表示 ***********************************************************/
void HpMeterDisp( int no );
// ヒットボックスＯＮ ***********************************************************/
void BattleHitBox( int no );
// ペットのバックアップ番号のクリア *****************************************************/
//void ClearBattlePetBakUp( void );
// 战闘コマンドバックアップ初期化关数 **************************************************/
void InitBattleBakUpCmd( void );
#ifdef PUK3_RIDE_BATTLE
	// 战闘にでいてるペットの番号を调べる *********************************************/
	int CheckBattlePet( void );
#endif
#ifdef PUK3_RIDE_BATTLE
	void InitBattlePetdata();
	void SetBattlePetdata();
#endif


#endif

