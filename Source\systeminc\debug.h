﻿/************************/	
/*	debug.h				*/
/************************/

#ifndef _DEBUG__H_
#define _DEBUG__H_

#ifdef _DEBUG
// フレームレート计算用
extern int	  FrameRate;			// フレームレート
extern int	  DrawFrameCnt;			// 一秒间に何枚描画したかを记忆
#ifdef PUK2_FPS
	extern int ProcRate;				// 处理レート
	extern int ProcCnt;					// 处理レートカウンター
#endif
extern DWORD DrawFrameTime;			// 一秒间を数えるカウンター

extern int testCnt;				// ほげカウンター

// デバッグ用关数 **************************************************************/
void DebugProc( void );
#endif

// システムログファイル初期化 ********************
void InitSystemLogfile( void );
// システムログファイル书き込み *****************
void WriteSystemLogfile( char *str );

#ifdef PUK3_MEMALLOCLOG
// メモリログファイル初期化 ********************
void InitMemoryLogfile( void );
// メモリログファイル书き込み *****************
void WriteMemoryLogfile( bool allocFlag, void *pointer, int size, char *file, int line );
#endif

// チャットでデバッグ制御 ****************************************
void chatDebug( char *str );

#ifdef PUK2_PROC_USE_TIME
	#define PPCOLOR_1 0xff7f7f7f
	#define PPCOLOR_2 0xffffff00
	#define PPCOLOR_3 0xffff00ff
	#define PPCOLOR_4 0xff00ffff
	#define PPCOLOR_5 0xffff0000
	#define PPCOLOR_6 0xff00ff00
	#define PPCOLOR_7 0xff0000ff
	#define PPCOLOR_8 0xffffffff
	#define PPCOLOR_9 0xff000000

	void initProcUseTime();
	void SetProcPoint( unsigned long color );
	void Draw_ProcUseTime();
#endif
#ifdef PUK3_GRAPH
	void NextGraph();
	void SetGraphNum( int index, int num );
	int GetGraphNum( int index );
	void Draw_Graph();
#endif

#endif
