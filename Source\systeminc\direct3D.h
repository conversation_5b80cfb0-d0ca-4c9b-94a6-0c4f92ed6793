﻿//**********************************
// direct3D.h
//**********************************
#ifndef _DIRECT_3D_H_
#define _DIRECT_3D_H_

//#define DIRECT3D_ON		// Ｄｉｒｅｃｔ３Ｄを使用可能にする

extern BOOL bD3DReady;						// セットアップが完了したことを示すフラグ

extern LPDIRECT3DRM2			lpD3DRM2;		// Direct3D保持モードAPI
extern LPDIRECT3DRMFRAME2		lpD3DScene;		// Direct3Dの基准となるフレーム
extern LPDIRECT3DRMFRAME2		lpD3DCamera;	// Direct3Dのカメラフレーム
extern LPDIRECT3DRMDEVICE2		lpD3DRMDevice;	// Direct3Dデバイス
extern LPDIRECT3DRMVIEWPORT		lpD3DViewport;	// Direct3Dのビューポート

// Direct3Dの保持モードを初期化します
BOOL InitDirect3DRetainedMode( void );

// Direct3Dの保持モードを終了します
void ReleaseDirect3DRetainedMode( void );

// デバイスとビューポートを作成します
BOOL CreateDeviceAndViewport( void );

// メッシュなどを作りシーンを作成します
BOOL BuildScene( void );

// 描画オプションを设定します (照明や品质など)
BOOL SetRenderingOptions( void );

//****************************************************************************
//Ｄ３Ｄテストプロセス
//****************************************************************************
void D3dTestProc( void );

#endif
