﻿/************************/
/*	battleMap.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/main.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/battleMap.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/math2.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/battleProc.h"

// バトルマップのサイズ
#define BATTLE_MAP_SIZE 441
#define BATTLE_MAP_GRID 21

// 最大マグニチュード
#define BATTLE_MAP_QUAKE_MAX 4

// 现在のバトルマップ番号
int BattleMapNo = 0;

// バトルマップ座标
int BattleMapX = 0;
int BattleMapY = 0;
int BattleMapAccele = 0;
int BattleMapAcceleMax = BATTLE_MAP_QUAKE_MAX;

// 地震フラグ
BOOL BattleMapQuakeFlag;

// バトルマップ ファイル名
char *BattleMapFile[] = {	
	#include "../systeminc/battleMapName.h"
};

#if 0						
// バトルマップ読み込みとバトルサーフェスの画像作成 ***************************/
BOOL ReadBattleMap( int no )
{
	char msg[ 256 ];
	FILE *fp;
	//char header[ 5 ];
	unsigned short tile[ BATTLE_MAP_SIZE ];
	int cnt = 0;
	int i, j, x = 0, y = 0, posX, posY;
	unsigned short c1, c2;
	
	// リミットチェック
	if( no >= BATTLE_MAP_FILES ) no = 0;
	
	// バトルマップ番号记忆
	BattleMapNo = no;
	
	// バトルマップファイルオープン
	if( ( fp = fopen( BattleMapFile[ no ], "rb" ) ) == NULL )            //MLHIDE
	{
#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( msg, ERRMSG_60, BattleMapFile[ no ] );
#else
		sprintf( msg, "%s 读取失败。", BattleMapFile[ no ] );                    //MLHIDE
#endif
		MessageBox( hWnd, msg, "战斗地图错误", MB_OK | MB_ICONSTOP );             //MLHIDE
		return FALSE;
	}
	
	// ヘッダーの読み込み
	//fread( header, sizeof( char ), 4, fp );
	//header[ 4 ] = '\0';
	
	// ヘッダーチェック
	//if( !( strstr( header, "NRB" )) )
	//{
	//	MessageBox( hWnd, "NRB 文件不存在。", "战斗地图错误", MB_OK | MB_ICONSTOP );
	//}
	
	// ポインタ进ませる（ ヘッダ飞ばし ）
	//fseek( fp, 4, SEEK_SET );
	
	// タイルデータの読み込み
	//fread( tile, sizeof( short ), BATTLE_MAP_SIZE, fp );
	
	//fread( tile, 1, 800, fp );
	
	// ポインタ进ませる
	fseek( fp, 44, SEEK_SET );
	
	// タイルの読み込み
	
	// 読み込みループ
	for( i = 0 ; i < BATTLE_MAP_SIZE ; i++ )
	{
		// １バイトずつ読み込む（ データ构造が逆のため ）
		c1 = fgetc( fp );
		c2 = fgetc( fp );
		
		// ２バイトにする
		tile[ i ] = ( c1 << 8 ) | c2;
	}
	
	// 位置设定
	posX = 32 * ( -10 );
	posY = 24 * 10;
	
	// タイル分ループ
	for( i = 0 ; i < BATTLE_MAP_GRID ; i++ ){
		x = 0;
		y = 0;
		for( j = 0 ; j < BATTLE_MAP_GRID ; j++ ){
			// ＢＭＰ表示バッファにためる
			StockDispBuffer( posX + x + ( DEF_APPSIZEX - 640 ) / 2, posY + y + ( DEF_APPSIZEY - 480 ) / 2, DISP_PRIO_TILE, tile[ cnt++ ], 0 );
			
			x += 32;
			y -= 24;
		}
		posX += 32;
		posY += 24;
	}
	
	// ２バイト进ませる
	fgetc( fp );
	fgetc( fp );
	// 布ツ読み込み
	// 読み込みループ
	for( i = 0 ; i < BATTLE_MAP_SIZE ; i++ )
	{
		// １バイトずつ読み込む（ データ构造が逆のため ）
		c1 = fgetc( fp );
		c2 = fgetc( fp );
		
		// ２バイトにする
		tile[ i ] = ( c1 << 8 ) | c2;
	}
	// ファイル关闭
	fclose( fp );
#if 1	
	// 位置设定
	//posX = 32 * ( -10 );
	//posY = 24 * 10;
	
	//posX -= 32;
	posX -= 32;
	posY -= 24;
	
	// カウンター初期化
	cnt = BATTLE_MAP_SIZE - 1;
	
	// 布ツ分ループ
	for( i = 0 ; i < BATTLE_MAP_GRID ; i++ ){
	//for( i = BATTLE_MAP_GRID - 1 ; i > 0 ; i-- ){
		x = 0;
		y = 0;
		for( j = 0 ; j < BATTLE_MAP_GRID ; j++ ){
		//for( j = BATTLE_MAP_GRID ; j > 0 ; j-- ){
			// 表示するものがあるとき
			if( tile[ cnt - BATTLE_MAP_GRID + j ] > CG_INVISIBLE ){
				// ＢＭＰ表示バッファにためる
				StockDispBuffer( posX + x + ( DEF_APPSIZEX - 640 ) / 2, posY + y +( DEF_APPSIZEY - 480 ) / 2, DISP_PRIO_TILE + 1, tile[ cnt - BATTLE_MAP_GRID + j ], 0 );
			}
			x += 32;
			y -= 24;
		}
		cnt -= BATTLE_MAP_GRID;
		posX -= 32;
		posY -= 24;
	}
#else
	// 位置设定
	posX = 32 * ( -10 );
	posY = 24 * 10;
	
	// カウンター初期化
	cnt = 0;
	
	// 布ツ分ループ
	for( i = 0 ; i < BATTLE_MAP_GRID ; i++ ){
	//for( i = BATTLE_MAP_GRID - 1 ; i > 0 ; i-- ){
		x = 0;
		y = 0;
		for( j = 0 ; j < BATTLE_MAP_GRID ; j++ ){
		//for( j = BATTLE_MAP_GRID ; j > 0 ; j-- ){
			// 表示するものがあるとき
			if( tile[ cnt ] > CG_INVISIBLE ){
				// ＢＭＰ表示バッファにためる
				StockDispBuffer( posX + x, posY + y, DISP_PRIO_TILE + 1, tile[ cnt ], 0 );
			}
			cnt ++;
			x += 32;
			y -= 24;
		}
		posX += 32;
		posY += 24;
	}

#endif
	return TRUE;
}	

#endif





	
#define	RASTER_CLEARANCE	8
int piyo_tbl[]={
	0,1,2,3,4,5,6,7,8,9,10,10,11,11,11,12,
	12,12,11,11,11,10,9,9,8,7,6,5,4,3,2,1,
	0,-1,-2,-3,-4,-5,-6,-7,-8,-9,-10,-10,-11,-11,-11,-12,
	-12,-12,-11,-11,-11,-10,-9,-9,-8,-7,-6,-5,-4,-3,-2,-1,
};
int piyo_point = 0;

// バトルマップ描画 ***********************************************************/
void DrawBattleMap( void )
{
	RECT rect = { 0, 0, DEF_APPSIZEX, DEF_APPSIZEY };
	int d0,d1,d6,d7;
	short bx , by;

	//if(BattleMapNo >= 148 && BattleMapNo <= 150){		//ラスタースクロールなら
	//	d1 = 1;
	//} else {
		d1 = 0;
	//}

#if 0
#ifdef _DEBUG		
	if( VK[ VK_SPACE ] & KEY_ON ){
		BattleMapQuakeFlag = TRUE;
	}else{
		BattleMapQuakeFlag = FALSE;
	}
#endif
#endif

//	switch( BattleMapNo & 1 ){
	switch( d1 ){
	case 0:
		//bx = Rnd( 0,10);
		//by = Rnd( 0,10);
		
		
		// スローの时
		if( ActSlowFlag == TRUE ){
			// ランアクションした时だけ动かす
			if( ActSlowCnt == 0 ){
				BattleMapX = Rnd( -BattleMapAccele, BattleMapAccele );
				BattleMapY = Rnd( -BattleMapAccele, BattleMapAccele );
			}
		}else{
			BattleMapX = Rnd( -BattleMapAccele, BattleMapAccele );
			BattleMapY = Rnd( -BattleMapAccele, BattleMapAccele );
		}
		
		bx = BattleMapX;
		by = BattleMapY;
		
		// 地震フラグＯＮの时
		if( BattleMapQuakeFlag == TRUE ){
			// 地震加速
			BattleMapAccele++;
			// リミットチェック
			if( BattleMapAccele > BattleMapAcceleMax ){
				BattleMapAccele = BattleMapAcceleMax;
			}
		}else{
			// 地震减速
			BattleMapAccele--;
			// リミットチェック
			if( BattleMapAccele < 0 ){
				BattleMapAccele = 0;
			}
		}
		
		
		short x0, y0;
		long w, h;

		// 転送领域のセット
		x0 = bx;
		y0 = by;
		w = rect.right - rect.left;
		h = rect.bottom - rect.top;

		// クリッピング处理
		//   （ちなみに RECT の右下座标のドットは表示されない）

		// 全く表示する部分が无ければ返回
		if( bx >= lpDraw->xSize || bx + w <= 0 || by >= lpDraw->ySize || by + h <= 0 ){
			return;
		}
		
		// 左端のチェック
		if( bx < 0 ){
			rect.left -= bx;
			x0 = 0;
		}
		// 右端のチェック
		if( bx + w > lpDraw->xSize ){
			rect.right -= bx + w - lpDraw->xSize;
		}
		// 上端のチェック
		if( by < 0 ){
			rect.top -= by;
			y0 = 0;
		}
		// 下端のチェック
		if( by + h > lpDraw->ySize ){
			rect.bottom -= by + h - lpDraw->ySize;
		}
		
#ifdef _DEBUG		
		// 现在表示しているサーフェスの数カウント
		SurfaceDispCnt++;
#endif
		
		// バックサーフェスへ高速転送
		lpDraw->lpBACKBUFFER->BltFast( x0, y0, lpBattleSurface, &rect, DDBLTFAST_WAIT );
		break;

	case 1:
		d6 = 0;
		rect.left = 0;		//左端
		rect.right = 640;		//右端
		for(d7=0; d7<480; d7+=RASTER_CLEARANCE){
			rect.top = d7 + piyo_tbl[(d6+piyo_point) & 63] + 12;		//上端
			rect.bottom = rect.top + RASTER_CLEARANCE;		//下端
			// バックサーフェスへ高速転送
			lpDraw->lpBACKBUFFER->BltFast( 0, d7, lpBattleSurface, &rect, DDBLTFAST_WAIT );
			d6++;
		}
		break;

	case 2:
		d6 = 0;
		for(d7=0; d7<480; d7+=RASTER_CLEARANCE/2){
			rect.top = d7;		//上端
			rect.bottom = rect.top + RASTER_CLEARANCE;		//下端

			d0 = piyo_tbl[(d6+piyo_point) & 63];		//左端
			if(d0 < 0){		//マイナスなら
				rect.left = 0 - d0;
				rect.right = 640 + d0;		//右端
				d0 = 0;
			} else {
				rect.left = 0;
				rect.right = 640 - d0;		//右端
			}
			// バックサーフェスへ高速転送
			lpDraw->lpBACKBUFFER->BltFast( d0, d7, lpBattleSurface, &rect, DDBLTFAST_WAIT );
			d6++;
		}
		break;
	}
}


