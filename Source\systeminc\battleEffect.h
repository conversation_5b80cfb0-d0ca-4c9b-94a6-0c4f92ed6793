﻿/************************/
/*	battleEffect.h		*/
/************************/
#ifndef _BATTLE_EFFECT_H_
#define _BATTLE_EFFECT_H_

// エフェクトの种类
enum{
	BATTLE_EFFECT_COUNTER,		// カウンターエフェクト
	BATTLE_EFFECT_DRAIN,		// ドレイン实行时
	BATTLE_EFFECT_DRAIN_2,		// ドレイン吸收时

	BATTLE_EFFECT_CROSS_COUNTER,// クロスカウンター
	
	BATTLE_EFFECT_ABNORMAL_STATUS,	// 状态异常实行
	BATTLE_EFFECT_DEFENSE_MAGIC,	// 防御系魔法实行（反射、吸收、无效）
	
	BATTLE_EFFECT_REVERSE_TYPE,			// 属性反転エフェクト（实行时）作成

	BATTLE_EFFECT_REFLECTION_PHYSICS,	// 物理反射エフェクト
	BATTLE_EFFECT_ABSORB_PHYSICS,		// 物理吸收エフェクト
	BATTLE_EFFECT_INEFFECTIVE_PHYSICS,	// 物理无效エフェクト
	BATTLE_EFFECT_REFLECTION_MAGIC,		// 魔法反射エフェクト
	BATTLE_EFFECT_ABSORB_MAGIC,			// 魔法吸收エフェクト
	BATTLE_EFFECT_INEFFECTIVE_MAGIC,	// 魔法无效エフェクト
	
	BATTLE_EFFECT_LP_RECOVER_GENERATE,	// 体力再生発生エフェクト
	BATTLE_EFFECT_LP_RECOVER,			// 体力再生エフェクト（継続时）
	BATTLE_EFFECT_CONSENTRATION,		// 精神统一エフェクト
	
	BATTLE_EFFECT_LP_INCREASE,		// 体力回复エフェクト
	BATTLE_EFFECT_LP_INCREASE_2,	// 体力回复补助エフェクト
	
	BATTLE_EFFECT_ITEM_GENERATE,	// アイテム使用エフェクト
	
	BATTLE_EFFECT_SKILL_GENERATE,	// スキル発生エフェクト
	
	BATTLE_EFFECT_PET_IN,	// ペット戾すエフェクト
	BATTLE_EFFECT_PET_OUT,	// ペット出すエフェクト
	
	BATTLE_EFFECT_CARD,		// 封印カードエフェクト
	
	BATTLE_EFFECT_STATUS_RECOVER,// 状态异常回复
	
	BATTLE_EFFECT_TERROR,		// 即死エフェクト
	BATTLE_EFFECT_SACRIFICE,	// サクリファイス
	
	BATTLE_EFFECT_GENERATE_2,	// 発生エフェクト（回転）
	BATTLE_EFFECT_GENERATE_3,	// 発生エフェクト（波纹）
	BATTLE_EFFECT_GENERATE_4,	// 発生エフェクト（下から上へループ）
	
	BATTLE_EFFECT_ANABIOSIS,	// 苏生エフェクト
	
	BATTLE_EFFECT_MAGIC_GENERATE,	// 魔法発生エフェクト
	
	BATTLE_EFFECT_HIT_MAGIC_SINGLE,			// 攻击魔法单体
	//BATTLE_EFFECT_HIT_MAGIC_SINGLE_FALL,	// 攻击魔法单体落下（地）
	
	BATTLE_EFFECT_DEAD,			// 死亡エフェクト
	BATTLE_EFFECT_PARAMETER,		//パラメータ变更エフェクト

#ifdef _TEST_TECH_YUK
	BATTLE_EFFECT_SUPERSKILL,		// スー布スキル発生エフェクト
#endif /* _TEST_TECH_YUK */
#ifdef PUK2_NEWSKILL_BARRIER
	BATTLE_EFFECT_BARRIER,		// バリア発生エフェクト
#endif
};

#ifdef PUK2_NEWSKILL
	// 战闘ヒットマークタスク作成
	ACTION *MakeBattleHitMark( int x, int y, int sprNo, int ang = 0 );
	// 战闘ヒットマークタスク作成
	ACTION *MakeBattleHitMark( ACTION *pAct, int sprNo, int ang = 0 );
#else
// 战闘ヒットマークタスク作成
ACTION *MakeBattleHitMark( int x, int y, int sprNo );
// 战闘ヒットマークタスク作成
ACTION *MakeBattleHitMark( ACTION *pAct, int sprNo );
#endif
// 受伤表示タスク作成
#ifdef PUK3_MONSTER_HELPER
	#ifdef _BATTLE_H_
		ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind, int bmFlag );
	#else
		ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind, int bmFlag = 0 );
	#endif
#else
ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind );
#endif
// バトルナイフ作成
ACTION *MakeBattleMissile( ACTION *pParent, BC_ENEMY_LIST *enemyList, int sprNo );
#ifdef TECH_BLASTWAVE
//追月
ACTION* MakeBattleBlastWaveMissile(ACTION* pParent, BC_ENEMY_LIST* enemyList, int sprNo);
#endif
// バトル矢作成
ACTION *MakeBattleAllow( ACTION *pOther, BC_ENEMY_LIST *enemyList, int sprNo );
// ブーメラン作成
ACTION *MakeBattleBoomerang( ACTION *pOther );
#ifdef PUK3_NEWSKILL_PSYBLAST
// ホーミング弹作成
ACTION *MakeBattlHomingeMissile( ACTION *pParent, BC_ENEMY_LIST *enemyList, int sprNo );
// 放物线弹作成
ACTION *MakeBattleParabolaMissile( ACTION *pParent, BC_ENEMY_LIST *enemyList, int sprNo, int Num, BOOL reflection );
#endif
// 战闘ヒットマークタスク作成
ACTION *MakeBattleUltimateMark( ACTION *pOther, int sprNo );

// 战闘エフェクト作成 *******************************************************/
ACTION *MakeBattleEffect( ACTION *pOther, int offsetY, int effectNo, int flashFlag, int waitFlag );
// 战闘エフェクト作成 *******************************************************/
ACTION *MakeBattleEffect( int x, int y, int sprNo );

// 战闘残像作成
ACTION *MakeBattleZanzou( ACTION *pParent );
// 战闘けむり作成
ACTION *MakeBattleKemuri( ACTION *pParent, int sprNo );
// 战闘けむり作成 *******************************************************/
ACTION *MakeBattleKemuri( float x, float y, int sprNo );
// 战闘彗星作成
ACTION *MakeBattleSuisei( float fx, float fy, int sprNo, int battleSortFlag, int dispPrio );
// アクション构造体を复制する
ACTION *BattleActionCopy( ACTION *pParent );
// １フレーム画面フラッシュ
void BattleEffectFlash( int sprNo );
#ifdef PUK3_R10
	ACTION *BattleActionFlash( int type, int time );
#endif
// 受伤表示タスク作成 *******************************************************/
#ifdef PUK2
ACTION *MakeBattleJumpDisp( ACTION *pParent, int sprNo, int x=0, int y=0 );
#else
ACTION *MakeBattleJumpDisp( ACTION *pParent, int sprNo );
#endif
// 状态异常マーク作成 *******************************************************/
ACTION *MakeBattleAbnormal( ACTION *pParent, int sprNo );
// ２アクションマーク作成 *******************************************************/
ACTION *MakeBattle2Action( ACTION *pParent, int no );
// パラメーターアップダウンマーク作成 *******************************************************/
ACTION *MakeBattleParameterUpDown( ACTION *pParent, int sprNo ,int viewflg);
// 属性优遇エフェクト作成 *******************************************************/
ACTION *MakeBattleTreatType( int sprNo );
// 属性反転エフェクト作成 *******************************************************/
ACTION *MakeBattleReverseType( ACTION *pParent, int sprNo );
// グラフィック表示タスク作成 *******************************************************/
ACTION *MakeBattleGraDisp( ACTION *pParent, int x, int y, int frame, int sprNo );
// グラフィック表示タスク作成 *******************************************************/
ACTION *MakeBattleStun( ACTION *pParent, int sprNo );
// Ｘアニメセット处理 ***********************************************/
void set_combo_x_anime();
// コンボ数表示セット处理 ***********************************************/
void set_combo_num( char cmbDispNum, int group );

#ifdef _TEST_TECH_YUK
// 分身アクション作成
ACTION *MakeBattleIllusionChar( ACTION *pParent);
#endif /* _TEST_TECH_YUK */
ACTION *MakeTranceEffect( ACTION *pParent, int fx, int fy, int SprNo );
#ifdef PUK2

#endif

#ifdef PUK2
	void BattleEffectFade( ACTION *pAct, int a_ssti, int a_add, unsigned long atr_on, unsigned long atr_off );

	#ifdef PUK3_NEWSKILL_LVDOWN
		ACTION *MakeBGBmp( int SprNo, unsigned char speed, BOOL changeFlag );
	#else
		ACTION *MakeBGBmp( int SprNo, unsigned char speed );
	#endif

	ACTION *MakeTranceAnim( ACTION *pParent, int SprNo );
	void MakeEffectRebirthHitMark( ACTION *pMather, ACTION *pEnemy );

	#define EFFECT_ALPHA 0x80

	ACTION *MakeBattleNewDeath( ACTION *pParent );
	unsigned char MakeElementWL( ACTION *pAt, ACTION *pDm );
	#define CRI_X 0
	#define CRI_Y 0

	ACTION *MakeBattleFlushlightMaker( int cnt );

	// 信息ボックス作成
	ACTION *MakeBattleMassageBox( char type, char *Message, int fonttype, int time );

	#if defined(PUK3_PUT_ON) && defined(PUK3_RIDE_BATTLE)
		ACTION *MakeRideChar( ACTION *pParent, int sprNo );
		void RideCharFunc( ACTION *pAct );
		void setBattleRide( ACTION *pAct, int rideCharGraNo, int ridePetGraNo );
		// 状态异常アイコンなどを设定する
		void SetIconFormFlag( ACTION *pAct );

		ACTION *MakePutOn( ACTION *pParent, int sprNo );
		void PutOnFunc( ACTION *pAct );
		void setBattlePuton( ACTION *pAct, int putonGraNo );
	#endif
	#ifdef PUK3_NEWSKILL_COINSHOT
		// 战闘ヒットマークタスク作成 *******************************************************/
		ACTION *MakeBattleCoinHit( ACTION *pOther, int soundNum );
	#endif

#endif

#endif
