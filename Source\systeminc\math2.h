﻿/************************/
/*	math2.h				*/
/************************/
#ifndef _MATH2_H_
#define _MATH2_H_

#define PAI	3.1415926535897932384626433832795

#define ABS(a)	( ( (a)<0 ) ?  -(a) : (a) )

#define RINT( a ) ( int )( ( a ) + 0.5 )

/* 角度修正 （ ０～ ３５９ にする ）*******************************************************/
void AdjustDir( float *dir );
void AdjustDir( int *dir );

/* テーブルから取ってくるＳＩＮ ***********************************************************/
float SinT( float theat );

/* テーブルから取ってくるＣＯＳ ***********************************************************/
float CosT( float theat );

/* アークタンジェント *********************************************************************/
float Atan( float x, float y );

/* ランダム数発生 *************************************************************************/
int	Rnd( int min, int max );

void initRand2( void );
int rand2( void );

/* ２点间の距离を求める *******************************************************************/
float pointLen( float x1, float y1, float x2, float y2 );
float pointLen2( float x1, float y1, float x2, float y2 );

#endif

