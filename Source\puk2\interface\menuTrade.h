﻿//メニュー＞アイテム

#ifndef _MENUTRADE_H_
#define _MENUTRADE_H_

//========================================
// メイン
//========================================

// ボタン处理关数 *********************//

BOOL MenuSwitchTradeClose( int no, unsigned int flag );

BOOL MenuSwitchTradeBtItemOpen( int no, unsigned int flag );
BOOL MenuSwitchTradeBtClose( int no, unsigned int flag );
BOOL MenuSwitchTradeBtTradeOK( int no, unsigned int flag );

BOOL MenuSwitchTradeBtItem( int no, unsigned int flag );
BOOL MenuSwitchTradeBtMonster( int no, unsigned int flag );
BOOL MenuSwitchTradeBtMoney( int no, unsigned int flag );

BOOL MenuSwitchGetTopPrio( int no, unsigned int flag );

BOOL MenuSwitchTradeMyPanel( int no, unsigned int flag );
BOOL MenuSwitchTradeOpenPanel( int no, unsigned int flag );
BOOL MenuSwitchTradeTradePanel( int no, unsigned int flag );

BOOL MenuTradeGetKeyForcus( int no, unsigned int flag );


BOOL MenuWindowTradeBf( int mouse );
BOOL MenuWindowTradeAf( int mouse );
BOOL closeTradeWindowAf();


GRAPHIC_SWITCH MenuWindowTradeGraph[]={
	{GID_TradeWidowBase,0,0,0,0,0xFFFFFFFF},			// ベース
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},				// クローズボタン

	{GID_ItemOpenOn,0,0,0,0,0xFFFFFFFF},				// アイテムオープンボタン
	{GID_TradeOKOn,0,0,0,0,0xFFFFFFFF},					// トレードＯＫボタン
	{GID_CloseButtonOn,0,0,0,0,0xFFFFFFFF},				// クローズボタン

	{GID_My_Item,0,0,0,0,0xFFFFFFFF},					// 自分侧パネル
	{GID_OpenItemPanel,0,0,0,0,0xFFFFFFFF},				// オープンパネル
	{GID_TradeItemPanel,0,0,0,0,0xFFFFFFFF},			// 相手侧パネル

	{GID_ItemChgOn,0,0,0,0,0xFFFFFFFF},					// 自分侧アイテムボタン
	{GID_MonsterChgOn,0,0,0,0,0xFFFFFFFF},				// 自分侧モンスターボタン
	{GID_MoneyChgOn,0,0,0,0,0xFFFFFFFF},				// 自分侧お金ボタン

	{GID_ItemChgOn,0,0,0,0,0xFFFFFFFF},					// オープンアイテムボタン
	{GID_MonsterChgOn,0,0,0,0,0xFFFFFFFF},				// オープンモンスターボタン
	{GID_MoneyChgOn,0,0,0,0,0xFFFFFFFF},				// オープンお金ボタン

	{GID_ItemChgOn,0,0,0,0,0xFFFFFFFF},					// 相手侧アイテムボタン
	{GID_MonsterChgOn,0,0,0,0,0xFFFFFFFF},				// 相手侧モンスターボタン
	{GID_MoneyChgOn,0,0,0,0,0xFFFFFFFF},				// 相手侧お金ボタン

	{0,0,0,0,0,0xFFFFFFFF},								// 絵无し
};

BUTTON_SWITCH MenuWindowTradeButton[]={ {0,0},{0,0},{0,0} };

TEXT_SWITCH MenuWindowTradeText[]={
	{FONT_PAL_BLACK|FONT_PAL_NOSHADOW,FONT_KIND_SMALL,""},
};


// スイッチ
static SWITCH_DATA TradeSwitch[] = {

{ SWITCH_DIALOG,   0,  0, 567,339, TRUE, NULL, MenuTradeGetKeyForcus },								// キーフォーカス取得用假入力栏

{ SWITCH_GRAPHIC,547,  9,  11, 11, TRUE, &MenuWindowTradeGraph[1], MenuSwitchTradeClose },			// クローズボタン

{ SWITCH_GRAPHIC,476,309,  49, 17, TRUE, &MenuWindowTradeGraph[2], MenuSwitchTradeBtItemOpen },		// アイテムオープンボタン
{ SWITCH_GRAPHIC,446,309,  49, 17, TRUE, &MenuWindowTradeGraph[3], MenuSwitchTradeBtTradeOK },		// トレードＯＫボタン
{ SWITCH_GRAPHIC,502,309,  49, 17, TRUE, &MenuWindowTradeGraph[4], MenuSwitchTradeBtClose },		// クローズボタン

{ SWITCH_NONE,   376,136, 106, 85, TRUE, NULL, MenuSwitchNone },									// 电卓コントロール

{ SWITCH_NONE,	 541, 66,   0, 14, TRUE, NULL, MenuSwitchNone },									// オープンスクロールバー(つまみ)
{ SWITCH_BUTTON, 541, 66,  11,183, TRUE, &MenuWindowTradeButton[1], MenuSwitchScrollBarV },			// オープンスクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC, 15, 28, 273,280, TRUE, &MenuWindowTradeGraph[17], MenuSwitchTradeOpenPanel },		// オープンパネル

{ SWITCH_NONE,	 541, 66,   0, 14, TRUE, NULL, MenuSwitchNone },									// 自分侧スクロールバー(つまみ)
{ SWITCH_BUTTON, 541, 66,  11,183, TRUE, &MenuWindowTradeButton[0], MenuSwitchScrollBarV },			// 自分侧スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,297, 28, 273,280, TRUE, &MenuWindowTradeGraph[17], MenuSwitchTradeMyPanel },		// 自分侧パネル

{ SWITCH_NONE,	 541, 66,   0, 14, TRUE, NULL, MenuSwitchNone },									// 相手侧スクロールバー(つまみ)
{ SWITCH_BUTTON, 541, 66,  11,183, TRUE, &MenuWindowTradeButton[2], MenuSwitchScrollBarV },			// 相手侧スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC, 15, 28, 273,280, TRUE, &MenuWindowTradeGraph[17], MenuSwitchTradeTradePanel },	// 相手侧パネル

{ SWITCH_TEXT,   133,310,  0,  0,  TRUE, &MenuWindowTradeText[0],	MenuSwitchNone },				// 相手名称
{ SWITCH_TEXT,   281,310,  0,  0,  TRUE, &MenuWindowTradeText[0],	MenuSwitchNone },				// 矢印
{ SWITCH_TEXT,   317,310,  0,  0,  TRUE, &MenuWindowTradeText[0],	MenuSwitchNone },				// 自分名称

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowTradeGraph[0], MenuSwitchNone },				// ベース

{ SWITCH_NONE,	  571, 7,  18, 78, TRUE, NULL, MenuSwitchGetTopPrio },								// ドラッグ用

};

enum{
	EnumTradeGetKeyForcus,

	EnumTradeClose,

	EnumTradeBtOpen,
	EnumTradeBtTradeOK,
	EnumTradeBtClose,

	EnumTradeCalculator,

	EnumTradeOpenScrollGra,
	EnumTradeOpenScrollBtn,
	EnumTradeOpenPanel,

	EnumTradeMyScrollGra,
	EnumTradeMyScrollBtn,
	EnumTradeMyPanel,

	EnumTradeTradeScrollGra,
	EnumTradeTradeScrollBtn,
	EnumTradeTradePanel,

	EnumTradeName1,
	EnumTradeArrow,
	EnumTradeName2,

	EnumTradeWindow,

	EnumTradeDragBack,

	EnumTradeEnd,
};

// ウィンドウ管理构造体
struct TRADEWINDOWMASTER{
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 

	int itemInfoNo;
	int itemInfoPage;

	int MonsterStart[2];
};

const WINDOW_DATA WindowDataMenuTrade = {
 0,															// メニューウィンドウ
     4,   20, 25,567,339, 0x80010101,  EnumTradeEnd,  TradeSwitch, MenuWindowTradeBf,MenuWindowTradeAf,closeTradeWindowAf
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateTrade={
	2,
	 9,  0,558, 27,
	 571, 7,18,  78,
};

enum{
	TRADEMODE_A,
	TRADEMODE_AtoB,
	TRADEMODE_BtoA,
	TRADEMODE_B,
};

ACTION *openTradeWindow();
void closeTradeWindow();

#endif