﻿//メニュー＞スキル

#ifndef _MENUORTHOPEDIST_H_
#define _MENUORTHOPEDIST_H_

//====================================//
//				メイン				  //
//====================================//

// ボタン处理关数 *********************//
BOOL MenuSwitchOrthopedistOK( int no, unsigned int flag );

BOOL MenuSwitchOrthopedistVerChg( int no, unsigned int flag );
BOOL MenuSwitchOrthopedistEyeChg( int no, unsigned int flag );
BOOL MenuSwitchOrthopedistMouthChg( int no, unsigned int flag );


BOOL MenuWindowOrthopedistBf( int mouse );
BOOL MenuWindowOrthopedistAf( int mouse );
BOOL MenuWindowOrthopedistCl();


GRAPHIC_SWITCH MenuWindowOrthopedistGraph[]={
	{GID_BigOKButtonOn,0,0,0,0,0xFFFFFFFF},			// OKボタン
	{CG_FACE_0,0,0,0,0,0xFFFFFFFF},					// 名刺
	{GID_AddressBookFaceWin,0,0,0,0,0xFFFFFFFF},	// 名刺枠
	{GID_LeftButtonOn,0,0,0,0,0xFFFFFFFF},			// 左ボタン
	{GID_RightButtonOn,0,0,0,0,0xFFFFFFFF},			// 右ボタン
};

TEXT_SWITCH MenuWindowOrthopedistText[]={
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,"MASSAGE789??123456789??123456789??123456789??123456789"},	// テキスト
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,"Face"},		// テキスト
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,"Eyes"},		// テキスト
	{FONT_PAL_WHITE,FONT_KIND_MIDDLE,"Mouth"},		// テキスト
};

ACTION_SWITCH_INIT MenuWindowOrthopedistAction[]={
	{100000},
};

// スイッチ
static SWITCH_DATA OrthopedistSwitch[] = {
{ SWITCH_GRAPHIC,197,206,  66, 17, TRUE, &MenuWindowOrthopedistGraph[0], MenuSwitchOrthopedistOK },					// OKボタン

{ SWITCH_GRAPHIC, 48, 19,   0,  0, TRUE, &MenuWindowOrthopedistGraph[2], MenuSwitchNone },							// 名刺枠
{ SWITCH_GRAPHIC, 48, 19,   0,  0, TRUE, &MenuWindowOrthopedistGraph[1], MenuSwitchNone },							// 名刺

{ SWITCH_GRAPHIC, 144+  0, 26+ 0,  18, 18, TRUE, &MenuWindowOrthopedistGraph[3], MenuSwitchOrthopedistVerChg },		// 左ボタン
{ SWITCH_TEXT,	  144+ 50, 28+ 0,   0,  0, TRUE, &MenuWindowOrthopedistText[1], MenuSwitchNone },					// コメント
{ SWITCH_GRAPHIC, 144+116, 26+ 0,  18, 18, TRUE, &MenuWindowOrthopedistGraph[4], MenuSwitchOrthopedistVerChg },		// OKボタン

{ SWITCH_GRAPHIC, 144+  0, 26+21,  18, 18, TRUE, &MenuWindowOrthopedistGraph[3], MenuSwitchOrthopedistEyeChg },		// 左ボタン
{ SWITCH_TEXT,	  144+ 50, 28+21,   0,  0, TRUE, &MenuWindowOrthopedistText[2], MenuSwitchNone },					// コメント
{ SWITCH_GRAPHIC, 144+116, 26+21,  18, 18, TRUE, &MenuWindowOrthopedistGraph[4], MenuSwitchOrthopedistEyeChg },		// 左ボタン

{ SWITCH_GRAPHIC, 144+  0, 26+42,  18, 18, TRUE, &MenuWindowOrthopedistGraph[3], MenuSwitchOrthopedistMouthChg },	// 左ボタン
{ SWITCH_TEXT,	  144+ 46, 28+42,   0,  0, TRUE, &MenuWindowOrthopedistText[3], MenuSwitchNone },					// コメント
{ SWITCH_GRAPHIC, 144+116, 26+42,  18, 18, TRUE, &MenuWindowOrthopedistGraph[4], MenuSwitchOrthopedistMouthChg },	// 左ボタン

{ SWITCH_ACTION,  390,120,  0,  0, TRUE, &MenuWindowOrthopedistAction[0], MenuSwitchNone },							// キャラ画像

{ SWITCH_TEXT,	  20,147+16*0,   0,  0, TRUE, &MenuWindowOrthopedistText[0], MenuSwitchNone },						// 信息１
{ SWITCH_TEXT,	  20,147+16*1,   0,  0, TRUE, &MenuWindowOrthopedistText[0], MenuSwitchNone },						// 信息２
};

enum{
	EnumGraOrthopedistOK,

	EnumGraOrthopedistCardFrame,
	EnumGraOrthopedistCard,

	EnumGraOrthopedistVerLeft,
	EnumGraOrthopedistVerText,
	EnumGraOrthopedistVerRight,

	EnumGraOrthopedistEyesLeft,
	EnumGraOrthopedistEyesText,
	EnumGraOrthopedistEyesRight,

	EnumGraOrthopedistMouthLeft,
	EnumGraOrthopedistMouthText,
	EnumGraOrthopedistMouthRight,

	EnumGraOrthopedistCharaGra,

	EnumGraOrthopedistText1,
	EnumGraOrthopedistText2,

	EnumOrthopedistEnd,
};


const WINDOW_DATA WindowDataMenuOrthopedist = {
 0,															// メニューウィンドウ
     4,   (DEF_APPSIZEX-448)>>1,(DEF_APPSIZEY-240-24)>>1,448,240, 0x80010101,  EnumOrthopedistEnd,  OrthopedistSwitch, MenuWindowOrthopedistBf,MenuWindowOrthopedistAf,MenuWindowOrthopedistCl
};

ACTION *openOrthopedistWindow( int seqno, int objindex, char *data );

// 整形外科确认ウィンドウ
ACTION *openOrCfWindow( int BottonType, int SeqNo, int ObjIndex, char *data );

#endif