﻿#include<stdio.h>
#include<stdlib.h>
#include<time.h>

#include "../systeminc/system.h"
#include "../systeminc/menu.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/anim_tbl2.h"
#include "../systeminc/mouse.h"
#include "../systeminc/t_music.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/font.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/map.h"
#include "../systeminc/tool.h"
#include "../systeminc/loadsprbin.h"
#include "../systeminc/pattern.h"

#ifdef _APPEND_JEWEL
#include "../systeminc/menu_appendjewel.h"

/*--------------------------------------
 * 宝石追加素材选择ウインドウ
 *------------------------------------*/
MENU_WINDOW_INFO appendJewelMaterialMenuWin =
{//   x,   y,   w,   h, menuNo,
	368,   0, 272, 380, MENU_APPEND_JEWEL_MATERIAL,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_MATERIAL_SELECT_WIN
	},
};

char appendJewelSelItemFlag[MAX_DRAW_WIN_ITEM];
int appendJewelMaterialMenuMsg;
static int materialSelTakeItemNo;
static int materialSelTakeItemGraNo;

// 初期化
void initAppendJewelMaterialMenu( void )
{
	ZeroMemory( appendJewelSelItemFlag, sizeof( appendJewelSelItemFlag));
	appendJewelMaterialMenuMsg = 0;
	materialSelTakeItemNo = -1;
	materialSelTakeItemGraNo = 0;
}

// そのアイテムのレシピを持っているかどうか
BOOL checkItemRecipe( int itemid)
{
	int i, j;

	for( i = 0; i <= MAX_SKILL; i++){
		if( job.skill[i].id < 200 || job.skill[i].id > 214) continue;
		for( j = 0; j < RECIPE_MAX; j++){
			if( itemid == job.skill[i].recipe[j].itemid) return TRUE;
		}
	}

	return FALSE;
}

// 选择されている素材が正しいかどうかチェック
int checkAppendJewelMaterials( void)
{
	int i;
	BOOL materialflg = FALSE;
	BOOL materialBflg = FALSE;

	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++){
		if( !appendJewelSelItemFlag[i]) continue;
		else if((pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_JEWEL
			  || pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_MATERIAL
			  || pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_MATERIAL_B )
			  &&(pc.item[MAX_EQUIP_ITEM+i].checkFlag))
		{
			if( materialBflg) return 2;
			else {
				materialBflg = TRUE;
				continue;
			}
		}
		else if( checkItemRecipe( pc.item[MAX_EQUIP_ITEM+i].id)){
			if( materialflg) return 2;
			else if( pc.item[MAX_EQUIP_ITEM+i].flag & ITEM_ETC_FLAG_CANADDJEWEL){
				materialflg = TRUE;
				continue;
			}
			else return 2;
		}
		else{
			return 2;
		}
	}

	if( materialflg && materialBflg) return 4;
	else return 2;
}

// そのアイテムを登録することが出来るかどうかチェックする
#ifdef PUK2
BOOL checkRegistItem( int itempos)
#else
static BOOL checkRegistItem( int itempos)
#endif
{
	//鉴定されているかどうかをチェック
	if( !pc.item[itempos].checkFlag) return FALSE;

	// 素材Ｂかそうでないかを判断する
	if( pc.item[itempos].kind == ITEM_JEWEL
	 || pc.item[itempos].kind == ITEM_MATERIAL
	 || pc.item[itempos].kind == ITEM_MATERIAL_B ){
		// 鉴定されている素材ＢならOK
		return TRUE;
	}
	// 素材Ｂでなければ
	else{
		// 宝石追加可能フラグをチェック
		if( pc.item[itempos].flag & ITEM_ETC_FLAG_CANADDJEWEL){
			// このアイテムのレシピを覚えているか
			if( checkItemRecipe( pc.item[itempos].id)){
				// 全てのチェックを通过
				return TRUE;
			}
			else return FALSE;
		}
		else return FALSE;
	}
}

void appendJewelMaterialMenu( int status)
{
	GRA_BTN_INFO1 registerBtn =
	{
		appendJewelMaterialMenuWin.x+140, appendJewelMaterialMenuWin.y+357,
		appendJewelMaterialMenuWin.x+100, appendJewelMaterialMenuWin.y+347,
		80, 20,
		CG_MATERIAL_SELECT_REGISTER_BTN_1, CG_MATERIAL_SELECT_REGISTER_BTN_2
	};
	GRA_BTN_INFO1 register2Btn =
	{
		appendJewelMaterialMenuWin.x+140, appendJewelMaterialMenuWin.y+357,
		appendJewelMaterialMenuWin.x+100, appendJewelMaterialMenuWin.y+347,
		80, 20,
		CG_MATERIAL_SELECT_REGISTER_BTN_3
	};
	int i, j;
	int focusItemNo;
	static int oldFocusItemNo;
	static int infoPage;
	int selCnt = 0;
	int ret;
	int len;
	char str[256];
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif

	// メニュー共通处理
	ret = menuCommonProc( status, &appendJewelMaterialMenuWin, initAppendJewelMaterialMenu,
			MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		// 宝石追加ウィンドウを关闭
		menuClose( MENU_APPEND_JEWEL );
		return;
	}

	for( i = 0; i < MAX_DRAW_WIN_ITEM; i++){
		selCnt += appendJewelSelItemFlag[i];
	}

	focusItemNo = -1;
	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( MakeHitBox(
				appendJewelMaterialMenuWin.x +8+j*52,
				appendJewelMaterialMenuWin.y+36+i*53,
				appendJewelMaterialMenuWin.x +8+j*52+48,
				appendJewelMaterialMenuWin.y+36+i*53+48, -1 ) )
			{
				focusItemNo = i * ITEM_DRAW_COLUMN + j;
				break;
			}
		}
	}

	// カーソル位置がかわったら说明ページを元に戾す
	if( focusItemNo != oldFocusItemNo )
	{
		infoPage = 0;
	}

	// つかんだアイテムを离す
	if( materialSelTakeItemNo >= 0
	 && (mouse.onceState & MOUSE_RIGHT_CRICK) )
	{
		materialSelTakeItemNo = -1;
		materialSelTakeItemGraNo = 0;
	}
	else
	// アイテム说明が出てる时に右クリックしたらページ切り替え
	if( focusItemNo >= 0 && (mouse.onceState & MOUSE_RIGHT_CRICK)
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag )
	{
		infoPage++;
		if( infoPage >= pc.item[MAX_EQUIP_ITEM+focusItemNo].memoPage )
		{
			infoPage = 0;
		}
	}
	else
	if( appendJewelMaterialMenuMsg >= 4)
	{
	}
	else
	// アイテムをダブルクリックした时
	if( (mouse.onceState & MOUSE_LEFT_DBL_CRICK)
	 && focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag )
	{
		if( !checkRegistItem( MAX_EQUIP_ITEM+focusItemNo))
		{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
		else
		if( appendJewelSelItemFlag[focusItemNo] )
		{
			appendJewelSelItemFlag[focusItemNo] = 0;
			// キャンセル音
			play_se( 54, 320, 240 );
		}
		else
		{
//			if( selCnt < MATERIAL_SEL_MAX )
			if( selCnt < 2 )
			{
				appendJewelSelItemFlag[focusItemNo] = 1;
				// 决定音c（文字等クリック时）
				play_se( SE_NO_OK3, 320, 240 );
			}
		}
		materialSelTakeItemNo = -1;
		materialSelTakeItemGraNo = 0;
	}
	else
	// Registerボタンチェック
	if( ( pushGraBtnInfo1( &registerBtn) & BTN_LEFT_CLICK)){
		// 正しい素材が登録されているかチェック
		if( selCnt > 2){
			appendJewelMaterialMenuMsg = 2;
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
		else if( selCnt < 2){
			appendJewelMaterialMenuMsg = 1;
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
		else appendJewelMaterialMenuMsg = checkAppendJewelMaterials();
		// 正しい素材が登録されているなら、宝石追加ウインドウにセットする
		if( appendJewelMaterialMenuMsg == 4){
			for( i = 0, j = 0; i < MAX_DRAW_WIN_ITEM; i++){
				if( !appendJewelSelItemFlag[i]) continue;
				else if( pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_JEWEL
					  || pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_MATERIAL
					  || pc.item[MAX_EQUIP_ITEM+i].kind == ITEM_MATERIAL_B)
				{
					registItemGraNo[5] = pc.item[MAX_EQUIP_ITEM+i].graNo;
					registItemNum[5] = 1;
					registItemIndex[5] = i;
				}
				else {
					registItemGraNo[j] = pc.item[MAX_EQUIP_ITEM+i].graNo;
					registItemNum[j] = 1;
					registItemIndex[j] = i;
					j++;
				}
				if( j >= MATERIAL_SEL_MAX) break;
			}
			// 决定音b（ボタンクリック时）
			play_se( SE_NO_OK2, 320, 240 );
		}
		else {
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}
	}
	else
	// アイテムを掴んだ状态で别の枠を右键したらアイテム移动
	if( focusItemNo >= 0
	 && materialSelTakeItemNo >= 0
	 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		if( materialSelTakeItemNo != focusItemNo )
		{
			// アイテム移动プロトコル送信
			nrproto_MI_send( sockfd,
				MAX_EQUIP_ITEM+materialSelTakeItemNo, MAX_EQUIP_ITEM+focusItemNo, 0 );
			appendJewelSelItemFlag[materialSelTakeItemNo] = 0;
			appendJewelSelItemFlag[focusItemNo] = 0;
		}
		materialSelTakeItemNo = -1;
		materialSelTakeItemGraNo = 0;
		// クリック音	// ohta
		play_se( SE_NO_CLICK, 320, 240 );
	}
	else
	// アイテムを掴む
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag
	 && materialSelTakeItemNo < 0
	 && (mouse.onceState & MOUSE_LEFT_CRICK) )
	{
		materialSelTakeItemNo = focusItemNo;
		materialSelTakeItemGraNo = pc.item[MAX_EQUIP_ITEM+focusItemNo].graNo;
		// クリック音	// ohta
		play_se( SE_NO_CLICK, 320, 240 );
	}

	// 掴んだアイテムをマウスといっしょに移动
	if( materialSelTakeItemNo >= 0 )
	{
#ifdef PUK2
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_ITEM, materialSelTakeItemGraNo, 0 );
#else
		StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_ITEM, materialSelTakeItemGraNo, 0 );
#endif
	}

	for( i = 0; i < ITEM_DRAW_LINE; i++ )
	{
		for( j = 0; j < ITEM_DRAW_COLUMN; j++ )
		{
			if( pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].useFlag )
			{
				// アイテム画像表示
#ifdef PUK2
				StockDispBuffer( 
					appendJewelMaterialMenuWin.x +8+24+j*52,
					appendJewelMaterialMenuWin.y+36+24+i*53,
					DISP_PRIO_MENU, pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].graNo, 0, &bm );
#else
				StockDispBuffer( 
					appendJewelMaterialMenuWin.x +8+24+j*52,
					appendJewelMaterialMenuWin.y+36+24+i*53,
					DISP_PRIO_MENU, pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].graNo, 0 );
#endif
				if( pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].num > 0 )
				{
					// スタック数表示
					sprintf( str, "%3d", pc.item[MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j].num ); //MLHIDE
					StockFontBuffer(
						appendJewelMaterialMenuWin.x +8+21+j*52,
						appendJewelMaterialMenuWin.y+36+31+i*53,
						FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
						str, 0, 0 );
				}
				if( !checkRegistItem( MAX_EQUIP_ITEM+i*ITEM_DRAW_COLUMN+j))
				{
					StockDispBuffer(
						appendJewelMaterialMenuWin.x +8+24+j*52+105,
						appendJewelMaterialMenuWin.y+36+24+i*53+93,
						DISP_PRIO_ITEM2, 22739, 0 );
				}
			}

			if( focusItemNo == i * ITEM_DRAW_COLUMN + j )
			{
				StockBoxDispBuffer(
					appendJewelMaterialMenuWin.x +8+j*52,
					appendJewelMaterialMenuWin.y+36+i*53,
					appendJewelMaterialMenuWin.x +8+j*52+48,
					appendJewelMaterialMenuWin.y+36+i*53+48,
					DISP_PRIO_MENU, BoxColor, 0 );
			}
			if( materialSelTakeItemNo == i * ITEM_DRAW_COLUMN + j )
			{
				StockBoxDispBuffer(
					appendJewelMaterialMenuWin.x +10+j*52,
					appendJewelMaterialMenuWin.y+38+i*53,
					appendJewelMaterialMenuWin.x +10+j*52+44,
					appendJewelMaterialMenuWin.y+38+i*53+44,
					DISP_PRIO_MENU, SYSTEM_PAL_AQUA, 0 );
			}
			else
			if( appendJewelSelItemFlag[i * ITEM_DRAW_COLUMN + j] )
			{
				StockBoxDispBuffer(
					appendJewelMaterialMenuWin.x + 10 + j * 52,
					appendJewelMaterialMenuWin.y + 38 + i * 53,
					appendJewelMaterialMenuWin.x + 10 + j * 52+44,
					appendJewelMaterialMenuWin.y + 38 + i * 53+44,
					DISP_PRIO_MENU, SYSTEM_PAL_YELLOW, 0 );
			}
		}
	}

	// カーソルがあっているアイテムの说明を出す
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag )
	{
		itemInfoWindow( appendJewelMaterialMenuWin.x, appendJewelMaterialMenuWin.y+appendJewelMaterialMenuWin.h/2+54,
			MAX_EQUIP_ITEM+focusItemNo, infoPage );
	}

	if( appendJewelMaterialMenuMsg < 4){
		drawGraBtnInfo1( &registerBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	else {
		drawGraBtnInfo1( &register2Btn, DISP_PRIO_MENU, 1, BoxColor, 1 );
	}

	if( appendJewelMaterialMenuMsg){
		switch( appendJewelMaterialMenuMsg)
		{
		case 1:
			strcpy( str, ML_STRING(633, "材料不足。"));
			break;
		case 2:
			strcpy( str, ML_STRING(634, "材料选择不正确。"));
			break;
		case 3:
			strcpy( str, ML_STRING(635, "无法登录材料。"));
			break;
		case 4:
			strcpy( str, ML_STRING(636, "已经登录了材料。"));
			break;
		case 5:
			strcpy( str, ML_STRING(544, "魔力不足。"));
			break;
		case 6:
		case 7:
			strcpy( str, ML_STRING(637, "加工中......"));
			break;

		default:
			break;
		}
		len = GetStrWidth( str, FONT_KIND_MIDDLE );
		StockFontBuffer(
			appendJewelMaterialMenuWin.x+appendJewelMaterialMenuWin.w/2-len/2,
			appendJewelMaterialMenuWin.y+278,
			FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, str, 0, 0 );
	}
	// 一行インフォ表示
	// x ボタン
	if( checkFocusMenuClose( &appendJewelMaterialMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	// Registerボタン
	if( ( pushGraBtnInfo1( &registerBtn) & BTN_FOCUS_ON) &&
		appendJewelMaterialMenuMsg <= 4)
	{
		if( selCnt > 0 )
		{
			strcpy( OneLineInfoStr, ML_STRING(638, "将选择的物品作为材料登录。") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(639, "无法选择材料。") );
		}
	}
	// アイテム
	else
	if( focusItemNo >= 0
	 && pc.item[MAX_EQUIP_ITEM+focusItemNo].useFlag)
	{
		if(pc.item[MAX_EQUIP_ITEM+focusItemNo].flag & ITEM_ETC_FLAG_INCUSE){
			strcpy( OneLineInfoStr, pc.item[MAX_EQUIP_ITEM+focusItemNo].name  );
			strcat( OneLineInfoStr,ITEM_INCUSE_STRING);
		}else if(pc.item[MAX_EQUIP_ITEM+focusItemNo].flag & ITEM_ETC_FLAG_HANKO){
			strcpy( OneLineInfoStr, pc.item[MAX_EQUIP_ITEM+focusItemNo].freeName );
			strcat( OneLineInfoStr,ITEM_HANKO_STRING);
		}else
		if( appendJewelSelItemFlag[focusItemNo] )
		{
			strcpy( OneLineInfoStr, ML_STRING(640, "选择这个物品。") );
		}
		else
		// 素材Ｂなら
		if( checkRegistItem( MAX_EQUIP_ITEM+focusItemNo))
		{
			strcpy( OneLineInfoStr, ML_STRING(640, "选择这个物品。") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(814, "无法选择这个物品。") );
		}
	}

	// メニュー共通表示处理
	menuCommonDraw( &appendJewelMaterialMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );

	oldFocusItemNo = focusItemNo;
}

/*--------------------------------------
 * 宝石追加ウインドウ
 *------------------------------------*/
MENU_WINDOW_INFO appendJewelMenuWin =
{//   x,   y,   w,   h, menuNo,
	  0,   0, 344, 380, MENU_APPEND_JEWEL,
//      winGraNo[]
	{// ウィンドウのグラフィック番号
		CG_CREATE_WIN
	},
};

static ACTION *ptActCreateChara;

// 加工中キャラの演出（最初のポーズに设定する）
static void createCharaProduceStart( void )
{
	if( ptActCreateChara != NULL )
	{
		if( ( SPRPC_START <= ptActCreateChara->anim_chr_no
			&& ptActCreateChara->anim_chr_no <= SPRPC_END)
#ifdef _CG2_NEWGRAPHIC
		 || ( SPRPC_START_V2 <= ptActCreateChara->anim_chr_no
			&& ptActCreateChara->anim_chr_no <= SPRPC_END_V2 )
#endif
		){
			ptActCreateChara->anim_no = ANIM_NOD;
		}else{
			ptActCreateChara->anim_no = ANIM_MAGIC;
		}
		if( ptActCreateChara->anim_no != ANIM_WALK )
		{
			ptActCreateChara->anim_no_bak = -1;
		}
	}
	pattern( ptActCreateChara, ANM_NOMAL_SPD, ANM_LOOP );
}

void initAppendJewelMenu( void )
{
	memoryMapGridPos( mapGx, mapGy );
	if( !ptActCreateChara){
		ptActCreateChara = createActEmChara( pc.graNo, appendJewelMenuWin.x + 94, appendJewelMenuWin.y + 290);
	}
	ptActCreateChara->atr |= ACT_ATR_HIDE;
	clearRegistItem();
}

void appendJewelMenu( int status)
{
	GRA_BTN_INFO1 executeBtn =
	{
		appendJewelMenuWin.x +97, appendJewelMenuWin.y+357,
		appendJewelMenuWin.x +57, appendJewelMenuWin.y+347,
		80, 20,
		CG_CREATE_EXECUTE_BTN_1, CG_CREATE_EXECUTE_BTN_2
	};
	GRA_BTN_INFO1 execute2Btn =
	{
		appendJewelMenuWin.x +97, appendJewelMenuWin.y+357,
		appendJewelMenuWin.x +57, appendJewelMenuWin.y+347,
		80, 20,
		CG_CREATE_EXECUTE_BTN_3
	};
	GRA_BTN_INFO1 cancelBtn =
	{
		appendJewelMenuWin.x+257, appendJewelMenuWin.y+357,
		appendJewelMenuWin.x+217, appendJewelMenuWin.y+347,
		80, 20,
		CG_COMMON_SMALL_CANCEL_BTN_1, CG_COMMON_SMALL_CANCEL_BTN_2
	};
	int itemPos[][2] =
	{
		{ 177, 214 },
		{ 229, 214 },
		{ 281, 214 },
		{ 177, 267 },
		{ 229, 267 },
		{ 281, 267 }
	};
	int i;
	int ret;
	int len;
	int index = job.sortSkill[abilityPage].index;
	static int startTime = 0, produceTime = 0, elapsedTime = 0;
	char str[256];
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif

	// メニュー共通处理
	ret = menuCommonProc( status, &appendJewelMenuWin, initAppendJewelMenu,
			MENU_COMMON_FLAG_CLOSE_BUTTON );
	if( ret == 0 )
	{
		return;
	}
	else
	if( ret == 2 )
	{
		if( ptActCreateChara){
			DeathAction( ptActCreateChara);
			ptActCreateChara = NULL;
		}
		// 宝石追加素材选择ウィンドウを关闭
		menuClose( MENU_APPEND_JEWEL_MATERIAL );
		return;
	}

	if( ptActCreateChara){
		ptActCreateChara->atr &= ~ACT_ATR_HIDE;
	}

	// キャラが移动したら終わる
	if( checkMoveMapGridPos( 1, 1 ) )
	{
		// このウィンドウを关闭
		menuClose( appendJewelMenuWin.menuNo );
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	// ＣＡＮＣＥＬボタン押したか？
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_LEFT_CLICK) )
	{
		// このウィンドウを关闭
		menuClose( appendJewelMenuWin.menuNo );
		// アビリティウィンドウ开く
		menuOpen( MENU_ABILITY );
		// 决定音c（文字等クリック时）// ohta
		//play_se( SE_NO_OK3, 320, 240 );
	}
	else
	if( appendJewelMaterialMenuMsg == 5 || appendJewelMaterialMenuMsg == 7)
	{
	}
	else
	// 加工中でオペレーション抑制
	if( appendJewelMaterialMenuMsg == 6 )
	{
		elapsedTime = GetTickCount() - startTime;
		//createCharaProduce( elapsedTime );
		if( produceTime < elapsedTime )
		{
			char str2[128];
			// 送信文字列作成
			// 宝石追加では、登録アイテムの一番最初と最后しか使われていない
			if( registItemIndex[0] >= 0 )
			{
				sprintf( str2, "%d|", MAX_EQUIP_ITEM+registItemIndex[0] );        //MLHIDE
				strcpy( str, str2 );
			}
			if( registItemIndex[5] >= 0){
				sprintf( str2, "%d", MAX_EQUIP_ITEM+registItemIndex[5] );         //MLHIDE
				strcat( str, str2 );
			}

			// S-JIS から EUC に变换
			sjisStringToEucString( str );

			// 宝石を追加するアイテム名の保存
			strcpy( recipeNameBak, pc.item[MAX_EQUIP_ITEM+registItemIndex[0]].name);

			// 技使用プロトコル送信
			nrproto_TU_send( sockfd, index, selTech, -1, str );
			appendJewelMaterialMenuMsg = 7;
		}
	}
	else
	// EXECUTEボタン押したか？
	if( (pushGraBtnInfo1( &executeBtn ) & BTN_LEFT_CLICK) )
	{
		if( registItemGraNo[0] > 0 )
		{
			if( pc.fp >= job.skill[index].tech[0].fp*job.skill[index].fpRate/100)
			{
				appendJewelMaterialMenuMsg = 6;
				startTime = GetTickCount();
//				produceTime = 5000 + (rand() % 6) * 1000
//								+ pc.item[MAX_EQUIP_ITEM+registItemIndex[0]].lv * 3000;
				produceTime = 8000;

				createCharaProduceStart();
				// 决定音b（ボタンクリック时）
				play_se( SE_NO_OK2, 320, 240 );
			}
			else
			{
				appendJewelMaterialMenuMsg = 5;
				// ＮＧ音（短い）
				play_se( SE_NO_NG, 320, 240 );
			}
		}
		else
		{
			// 「素材が登録されてません。」って表示
			appendJewelMaterialMenuMsg = 3;
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
		}
	}

	// 宝石追加表示
	len = GetStrWidth( job.skill[index].name, FONT_KIND_MIDDLE );
	StockFontBuffer( appendJewelMenuWin.x+11+126-len/2, appendJewelMenuWin.y+37,
		FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE,
		job.skill[index].name, 0, 0 );

	// 魔力表示
	sprintf( str, "%4d", job.skill[index].tech[0].fp*job.skill[index].fpRate/100 ); //MLHIDE
	StockFontBuffer( appendJewelMenuWin.x+295, appendJewelMenuWin.y+39,
		FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, str, 0, 0 );

	if( registItemGraNo[0] > 0){
		for( i = 0; i < MATERIAL_SEL_MAX; i++){
			if( registItemGraNo[i] > 0){
				// 素材画像表示
#ifdef PUK2
				StockDispBuffer(
					appendJewelMenuWin.x+itemPos[i][0]+24,
					appendJewelMenuWin.y+itemPos[i][1]+24,
					DISP_PRIO_ITEM2, registItemGraNo[i], 0, &bm );
#else
				StockDispBuffer(
					appendJewelMenuWin.x+itemPos[i][0]+24,
					appendJewelMenuWin.y+itemPos[i][1]+24,
					DISP_PRIO_ITEM2, registItemGraNo[i], 0 );
#endif
				if( registItemNum[i] > 0 )
				{
					// スタック数表示
					sprintf( str, "%3d", registItemNum[i] );                         //MLHIDE
					StockFontBuffer(
						appendJewelMenuWin.x+itemPos[i][0]+21,
						appendJewelMenuWin.y+itemPos[i][1]+31,
						FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE,
						str, 0, 0 );
				}
			}
		}
	}

	// ＥＸＥＣＵＴＥボタン表示
	if( appendJewelMaterialMenuMsg <= 4){
		drawGraBtnInfo1( &executeBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );
	}
	else {
		drawGraBtnInfo1( &execute2Btn, DISP_PRIO_MENU, 1, BoxColor, 1 );
	}

	// ＣＡＮＣＥＬボタン表示
	drawGraBtnInfo1( &cancelBtn, DISP_PRIO_MENU, 1, BoxColor, 0 );

	// x ボタン
	if( checkFocusMenuClose( &appendJewelMenuWin ) )
	{
		strcpy( OneLineInfoStr, ML_STRING(143, "关闭这个窗口。") );
	}
	else
	// EXECUTE ボタン
	if( (pushGraBtnInfo1( &executeBtn ) & BTN_FOCUS_ON) )
	{
		if( registItemGraNo[0] > 0 )
		{
			strcpy( OneLineInfoStr, ML_STRING(815, "装上宝石。") );
		}
		else
		{
			strcpy( OneLineInfoStr, ML_STRING(635, "无法登录材料。") );
		}
	}
	else
	// CANCEL ボタン
	if( (pushGraBtnInfo1( &cancelBtn ) & BTN_FOCUS_ON) )
	{
		strcpy( OneLineInfoStr, ML_STRING(816, "返回能力窗口。") );
	}

	// メニュー共通表示处理
	menuCommonDraw( &appendJewelMenuWin, MENU_COMMON_FLAG_CLOSE_BUTTON );
}
#endif /* _APPEND_JEWEL */
