﻿//メニュー＞アドレス

#ifndef _MENUADDRESS_H_
#define _MENUADDRESS_H_

#define GID_AddressBack			0
#define GID_AddressBase			0
#define GID_AddressCloseOn		GID_WindowCloseOn//239585
#define GID_AddressCloseOff		GID_WindowCloseOff//239585
#define GID_AddressCloseOver	GID_WindowCloseOver//239585


BOOL MenuWindowAddress( int mouse );
BOOL MenuWindowAddressDraw( int mouse );


GRAPHIC_SWITCH MenuWindowAddressGraph[]={
	{GID_AddressCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_AddressBase,0,0,0,0,0xFFFFFFFF},		//ベース
	{GID_AddressBack,0,0,0,0,0x80FFFFFF},		//背景
};

BUTTON_SWITCH MenuWindowAddressButton[]={
	{0},									//状态
};


// マップスイッチ
static SWITCH_DATA AddressSwitch[] = {
{ SWITCH_GRAPHIC,291,  9,   0,  0, TRUE, &MenuWindowAddressGraph[ 0], MenuSwitchNone },			//クローズボタン

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowAddressGraph[ 1], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC, 20, 16,   0,  0, TRUE, &MenuWindowAddressGraph[ 2], MenuSwitchNone },			//背景
};

enum{
	EnumGraphAddressClose,		
	EnumGraphAddressBase,		
	EnumGraphAddressWindow,		

	EnumAddressEnd,
};


const WINDOW_DATA WindowDataMenuAddress = {
 0,															// メニューウィンドウ
     4,   100, 100,217,264, 0x80010101,  EnumAddressEnd,  AddressSwitch, MenuWindowAddress,MenuWindowAddressDraw,MenuWindowDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateAddress={
	1,
	 16,  0,184, 21,
};

#endif