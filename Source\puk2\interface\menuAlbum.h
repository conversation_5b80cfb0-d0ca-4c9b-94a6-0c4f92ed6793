﻿//メニュー＞アルバム

#ifndef _MENUALBUM_H_
#define _MENUALBUM_H_

void InitMenuWindowAlbumInLogin(void);
BOOL MenuWindowAlbum( int mouse );
BOOL MenuWindowAlbumDraw( int mouse );
BOOL MenuWindowAlbumDetail( int mouse );
BOOL MenuWindowAlbumDetailDraw( int mouse );
BOOL MenuSwitchAlbumSwitch( int no, unsigned int flag );
BOOL MenuSwitchAlbumScrollSwitch( int no, unsigned int flag );
BOOL MenuSwitchAlbumScrollWheel( int no, unsigned int flag );
BOOL MenuSwitchAlbumPanel( int no, unsigned int flag );
BOOL MenuSwitchAlbumDetailSwitch( int no, unsigned int flag );

//アルバム---------------------------------------------------------------------------------------------------------
GRAPHIC_SWITCH MenuWindowAlbumGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_AlbumWindow,0,0,0,0,0xFFFFFFFF},		//ベース
	{GID_AlbumBack,0,0,0,0,0x80FFFFFF},			//背景

	{GID_AlbumPanelOn,0,0,0,0,0xFFFFFFFF},		//枠
	{GID_Num_Blue2_0,0,0,0,0,0xFFFFFFFF},		//ナンバー

	{GID_ScrollBar,0,0,0,0,0xFFFFFFFF},						//スクロールバー(つまみ)
	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(上ボタン)
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},					//スクロールバー(下ボタン)

	{GID_AlbumPanelNew,0,0,0,0,0xFFFFFFFF},					//New

	{GID_AlbumDownOn,0,0,0,0,0xFFFFFFFF},		//<
	{GID_AlbumUpOn,0,0,0,0,0xFFFFFFFF},		//>
};

BUTTON_SWITCH MenuWindowAlbumButton[]={
	{0},
};

TEXT_SWITCH MenuWindowAlbumText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"怪物名字"},	//名称                      //MLHIDE
};

// スイッチ
static SWITCH_DATA AlbumSwitch[] = {
{ SWITCH_GRAPHIC,241-35,  9-1,  11, 11, TRUE, &MenuWindowAlbumGraph[ 0], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_GRAPHIC,224-18, 62-21,   0, 14, TRUE, &MenuWindowAlbumGraph[5], MenuSwitchNone },				//スクロールバー(つまみ)
{ SWITCH_BUTTON, 223-18, 62-21,  11,134, TRUE, &MenuWindowAlbumButton[0], MenuSwitchAlbumScrollSwitch },	//スクロールバー(ドラッグ部分)
{ SWITCH_GRAPHIC,224-18, 51-21,  11, 11, TRUE, &MenuWindowAlbumGraph[6], MenuSwitchAlbumSwitch },			//スクロールバー(上ボタン)
{ SWITCH_GRAPHIC,224-18,195-21,  11, 11, TRUE, &MenuWindowAlbumGraph[7], MenuSwitchAlbumSwitch },			//スクロールバー(下ボタン)

{ SWITCH_GRAPHIC,  40, 189, 20, 20, TRUE, &MenuWindowAlbumGraph[ 9], MenuSwitchAlbumSwitch },		//＜
{ SWITCH_GRAPHIC, 170, 189, 20, 20, TRUE, &MenuWindowAlbumGraph[10], MenuSwitchAlbumSwitch },		//＞
{ SWITCH_NONE,	   0,  0, 225,220, TRUE, NULL, MenuSwitchAlbumScrollWheel },								//マウスホイール判定

{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*0-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//ナンバー
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*0-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*0-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*1-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*1-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*1-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*2-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*2-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*2-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*3-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*3-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*3-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*4-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*4-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*4-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*5-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*5-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*5-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*6-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*6-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*6-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*7-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*7-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*7-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*8-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*8-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*8-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*0-13, 53+16*9-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*1-13, 53+16*9-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 32+6*2-13, 53+16*9-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 4], MenuSwitchNone },	//

{ SWITCH_GRAPHIC, 60-13, 53+16*0-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//Ｎｅｗ
{ SWITCH_GRAPHIC, 60-13, 53+16*1-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*2-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*3-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*4-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*5-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*6-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*7-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*8-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//
{ SWITCH_GRAPHIC, 60-13, 53+16*9-22,   0,  0, TRUE, &MenuWindowAlbumGraph[ 8], MenuSwitchNone },	//

{ SWITCH_TEXT	  ,  88-13, 51+16*0-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//モンスター名称00
{ SWITCH_TEXT	  ,  88-13, 51+16*1-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//01
{ SWITCH_TEXT	  ,  88-13, 51+16*2-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//02
{ SWITCH_TEXT	  ,  88-13, 51+16*3-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//03
{ SWITCH_TEXT	  ,  88-13, 51+16*4-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//04
{ SWITCH_TEXT	  ,  88-13, 51+16*5-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//05
{ SWITCH_TEXT	  ,  88-13, 51+16*6-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//06
{ SWITCH_TEXT	  ,  88-13, 51+16*7-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//07
{ SWITCH_TEXT	  ,  88-13, 51+16*8-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//08
{ SWITCH_TEXT	  ,  88-13, 51+16*9-22,   0,  0, TRUE, &MenuWindowAlbumText[0], MenuSwitchNone },	//09

{ SWITCH_GRAPHIC, 28-13, 50+16*0-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//枠00
{ SWITCH_GRAPHIC, 28-13, 50+16*1-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//01
{ SWITCH_GRAPHIC, 28-13, 50+16*2-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//02
{ SWITCH_GRAPHIC, 28-13, 50+16*3-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//03
{ SWITCH_GRAPHIC, 28-13, 50+16*4-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//04
{ SWITCH_GRAPHIC, 28-13, 50+16*5-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//05
{ SWITCH_GRAPHIC, 28-13, 50+16*6-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//06
{ SWITCH_GRAPHIC, 28-13, 50+16*7-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//07
{ SWITCH_GRAPHIC, 28-13, 50+16*8-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//08
{ SWITCH_GRAPHIC, 28-13, 50+16*9-22, 186, 16, TRUE, &MenuWindowAlbumGraph[ 3], MenuSwitchAlbumPanel },	//09

{ SWITCH_GRAPHIC,  0,  0,   0,  0, TRUE, &MenuWindowAlbumGraph[ 1], MenuSwitchNone },			//ベース
{ SWITCH_GRAPHIC, 12, 27,   0,  0, TRUE, &MenuWindowAlbumGraph[ 2], MenuSwitchNone },			//背景

{ SWITCH_NONE  , 225, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumGraphAlbumClose,		

	EnumGraphMenuAlbumTumami,
	EnumBtMenuAlbumDrag,
	EnumGraphMenuAlbumScrollUp,
	EnumGraphMenuAlbumScrollDown,

	EnumGraphMenuAlbumUp,
	EnumGraphMenuAlbumDown,
	EnumGraphMenuAlbumScrollWheel,

	EnumGraphAlbumNo0000,		
	EnumGraphAlbumNo0001,		
	EnumGraphAlbumNo0002,		
	EnumGraphAlbumNo0100,		
	EnumGraphAlbumNo0101,		
	EnumGraphAlbumNo0102,		
	EnumGraphAlbumNo0200,		
	EnumGraphAlbumNo0201,		
	EnumGraphAlbumNo0202,		
	EnumGraphAlbumNo0300,		
	EnumGraphAlbumNo0301,		
	EnumGraphAlbumNo0302,		
	EnumGraphAlbumNo0400,		
	EnumGraphAlbumNo0401,		
	EnumGraphAlbumNo0402,		
	EnumGraphAlbumNo0500,		
	EnumGraphAlbumNo0501,		
	EnumGraphAlbumNo0502,		
	EnumGraphAlbumNo0600,		
	EnumGraphAlbumNo0601,		
	EnumGraphAlbumNo0602,		
	EnumGraphAlbumNo0700,		
	EnumGraphAlbumNo0701,		
	EnumGraphAlbumNo0702,		
	EnumGraphAlbumNo0800,		
	EnumGraphAlbumNo0801,		
	EnumGraphAlbumNo0802,		
	EnumGraphAlbumNo0900,		
	EnumGraphAlbumNo0901,		
	EnumGraphAlbumNo0902,		

	EnumGraphAlbumNew00,		
	EnumGraphAlbumNew01,		
	EnumGraphAlbumNew02,		
	EnumGraphAlbumNew03,		
	EnumGraphAlbumNew04,		
	EnumGraphAlbumNew05,		
	EnumGraphAlbumNew06,		
	EnumGraphAlbumNew07,		
	EnumGraphAlbumNew08,		
	EnumGraphAlbumNew09,		

	EnumTextAlbumPanel00,		
	EnumTextAlbumPanel01,		
	EnumTextAlbumPanel02,		
	EnumTextAlbumPanel03,		
	EnumTextAlbumPanel04,		
	EnumTextAlbumPanel05,		
	EnumTextAlbumPanel06,		
	EnumTextAlbumPanel07,		
	EnumTextAlbumPanel08,		
	EnumTextAlbumPanel09,		

	EnumGraphAlbumPanel00,		
	EnumGraphAlbumPanel01,		
	EnumGraphAlbumPanel02,		
	EnumGraphAlbumPanel03,		
	EnumGraphAlbumPanel04,		
	EnumGraphAlbumPanel05,		
	EnumGraphAlbumPanel06,		
	EnumGraphAlbumPanel07,		
	EnumGraphAlbumPanel08,		
	EnumGraphAlbumPanel09,		

	EnumGraphAlbumBase,		
	EnumGraphAlbumWindow,		

	EnumHitAlbum1,		

	EnumAlbumEnd,
};


const WINDOW_DATA WindowDataMenuAlbum = {
 0,															// メニューウィンドウ
     4,  38,  95,225,220, 0x80010101,  EnumAlbumEnd,  AlbumSwitch, MenuWindowAlbum,MenuWindowAlbumDraw,MenuWindowDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateAlbum={
	2,
	0,  0,231,25,
	221,  0,30,100,
};


//アルバム详细-----------------------------------------------------------------------------------------------------
GRAPHIC_SWITCH MenuWindowAlbumDetailGraph[]={
	{GID_WindowCloseOff,0,0,0,0,0xFFFFFFFF},	//クローズボタン
	{GID_AlbumDetailWindow,0,0,0,0,0xFFFFFFFF},	//ウインドウ
	{GID_AlbumDetailBack,0,0,0,0,0x80FFFFFF},	//バック

	{GID_AlbumDetailBack,0,0,0,0,0xFFFFFFFF},	//カード
	{GID_AlbumDetailStar,0,0,0,0,0xFFFFFFFF},	//☆
	{GID_AlbumDetailStarHalf,0,0,0,0,0xFFFFFFFF},	//半分☆

	{GID_AlbumDetailPageDownOn,0,0,0,0,0xFFFFFFFF},	//<<
	{GID_AlbumDetailDownOn,0,0,0,0,0xFFFFFFFF},		//<
	{GID_AlbumDetailUpOn,0,0,0,0,0xFFFFFFFF},		//>
	{GID_AlbumDetailPageUpOn,0,0,0,0,0xFFFFFFFF},	//>>

	{GID_StatusEarthCenter,0,0,0,0,0xFFFFFFFF},	//属性
};



TEXT_SWITCH MenuWindowAlbumDetailText[]={
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"怪物名字"},	//名称                      //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"000"},				//ナンバー                  //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"00"},					//スロット                  //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"种类"},				//种类                     //MLHIDE
	{FONT_PAL_WHITE,FONT_KIND_SIZE_12,"255/255"},			//ページ数               //MLHIDE
};

BUTTON_SWITCH MenuWindowAlbumDetailButton[]={
	{0},										
};

ACTION_SWITCH_INIT MenuWindowAlbumDetailAction[]={
	{100000},
};

// スイッチ
static SWITCH_DATA AlbumDetailSwitch[] = {
{ SWITCH_GRAPHIC,257-22,  9-1,  11, 11, TRUE, &MenuWindowAlbumDetailGraph[ 0], MenuSwitchCloseButton },	//クローズボタン

{ SWITCH_ACTION,195-9,135, 0, 0,TRUE,&MenuWindowAlbumDetailAction[0], MenuSwitchNone},		//キャラクター画像（アクション）

{ SWITCH_TEXT	  ,  71-16, 39-12,   0,  0, TRUE, &MenuWindowAlbumDetailText[0], MenuSwitchNone },	//モンスター名称
{ SWITCH_TEXT	  , 232-18, 39-11,   0,  0, TRUE, &MenuWindowAlbumDetailText[1], MenuSwitchNone },	//ナンバー
{ SWITCH_TEXT	  , 111-16, 58-11,   0,  0, TRUE, &MenuWindowAlbumDetailText[2], MenuSwitchNone },	//スロット
{ SWITCH_TEXT	  ,  76-13, 73-10,   0,  0, TRUE, &MenuWindowAlbumDetailText[3], MenuSwitchNone },	//种类

{ SWITCH_TEXT	  ,  56-8,275-13,   0,  0, TRUE, &MenuWindowAlbumDetailText[4], MenuSwitchNone },	//ページ数

{ SWITCH_GRAPHIC,  31-13, 186-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 3], MenuSwitchNone },		//封印可不可
{ SWITCH_GRAPHIC,  67-13, 184-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 3], MenuSwitchNone },		//レア度

{ SWITCH_GRAPHIC,  23-5, 273-12, 13, 11, TRUE, &MenuWindowAlbumDetailGraph[ 6], MenuSwitchAlbumDetailSwitch },		//＜＜
{ SWITCH_GRAPHIC,  43-5, 273-12, 10, 10, TRUE, &MenuWindowAlbumDetailGraph[ 7], MenuSwitchAlbumDetailSwitch },		//＜
{ SWITCH_GRAPHIC,  95-5, 273-12, 10, 10, TRUE, &MenuWindowAlbumDetailGraph[ 8], MenuSwitchAlbumDetailSwitch },		//＞
{ SWITCH_GRAPHIC, 107-5, 273-12, 13, 11, TRUE, &MenuWindowAlbumDetailGraph[ 9], MenuSwitchAlbumDetailSwitch },		//＞＞

{ SWITCH_GRAPHIC,  42+6*0-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//属性土
{ SWITCH_GRAPHIC,  42+6*1-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*2-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*3-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*4-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*5-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*6-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*7-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*8-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*9-11, 111-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//

{ SWITCH_GRAPHIC,  42+6*0-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//属性水
{ SWITCH_GRAPHIC,  42+6*1-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*2-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*3-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*4-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*5-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*6-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*7-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*8-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*9-11, 127-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//

{ SWITCH_GRAPHIC,  42+6*0-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//属性火
{ SWITCH_GRAPHIC,  42+6*1-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*2-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*3-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*4-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*5-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*6-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*7-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*8-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*9-11, 143-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//

{ SWITCH_GRAPHIC,  42+6*0-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//属性风
{ SWITCH_GRAPHIC,  42+6*1-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*2-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*3-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*4-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*5-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*6-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*7-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*8-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//
{ SWITCH_GRAPHIC,  42+6*9-11, 159-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[10], MenuSwitchNone },		//

{ SWITCH_GRAPHIC, 157-12, 184-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Vit
{ SWITCH_GRAPHIC, 177-12, 184-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Vit
{ SWITCH_GRAPHIC, 197-12, 184-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Vit
{ SWITCH_GRAPHIC, 217-12, 184-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Vit
{ SWITCH_GRAPHIC, 237-12, 184-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Vit

{ SWITCH_GRAPHIC, 157-12, 205-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Str
{ SWITCH_GRAPHIC, 177-12, 205-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Str
{ SWITCH_GRAPHIC, 197-12, 205-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Str
{ SWITCH_GRAPHIC, 217-12, 205-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Str
{ SWITCH_GRAPHIC, 237-12, 205-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Str

{ SWITCH_GRAPHIC, 157-12, 226-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Tgh
{ SWITCH_GRAPHIC, 177-12, 226-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Tgh
{ SWITCH_GRAPHIC, 197-12, 226-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Tgh
{ SWITCH_GRAPHIC, 217-12, 226-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Tgh
{ SWITCH_GRAPHIC, 237-12, 226-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Tgh

{ SWITCH_GRAPHIC, 157-12, 247-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Qui
{ SWITCH_GRAPHIC, 177-12, 247-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Qui
{ SWITCH_GRAPHIC, 197-12, 247-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Qui
{ SWITCH_GRAPHIC, 217-12, 247-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Qui
{ SWITCH_GRAPHIC, 237-12, 247-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Qui

{ SWITCH_GRAPHIC, 157-12, 268-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Mgc
{ SWITCH_GRAPHIC, 177-12, 268-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Mgc
{ SWITCH_GRAPHIC, 197-12, 268-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Mgc
{ SWITCH_GRAPHIC, 217-12, 268-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Mgc
{ SWITCH_GRAPHIC, 237-12, 268-11,  0, 0, TRUE, &MenuWindowAlbumDetailGraph[ 4], MenuSwitchNone },		//Mgc

{ SWITCH_GRAPHIC,  0,  0,  11, 11, TRUE, &MenuWindowAlbumDetailGraph[ 1], MenuSwitchNone },	//ウインドウ
{ SWITCH_GRAPHIC, 13, 26,  11, 11, TRUE, &MenuWindowAlbumDetailGraph[ 2], MenuSwitchNone },	//バック

{ SWITCH_BUTTON , 140-9, 59,114,110, TRUE, &MenuWindowAlbumDetailButton[0], MenuSwitchAlbumDetailSwitch },	//キャラクタ回転ボタン

{ SWITCH_NONE  , 256, 0, 20, 130, TRUE, NULL, MenuSwitchDelMouse },					//ヒットスイッチ

};

enum{

	EnumGraphAlbumDetailClose,		

	EnumActionAlbumDetailChar,		

	EnumTextAlbumDetailName,		
	EnumTextAlbumDetailNo,		
	EnumTextAlbumDetailSlot,		
	EnumTextAlbumDetailType,		

	EnumTextAlbumDetailPageNum,		

	EnumGraphAlbumDetailCard00,		
	EnumGraphAlbumDetailCard01,		

	EnumGraphAlbumDetailPageDown,		
	EnumGraphAlbumDetailDown,		
	EnumGraphAlbumDetailUp,		
	EnumGraphAlbumDetailPageUp,		

	EnumGraphAlbumDetailEarth00,		
	EnumGraphAlbumDetailEarth01,		
	EnumGraphAlbumDetailEarth02,		
	EnumGraphAlbumDetailEarth03,		
	EnumGraphAlbumDetailEarth04,		
	EnumGraphAlbumDetailEarth05,		
	EnumGraphAlbumDetailEarth06,		
	EnumGraphAlbumDetailEarth07,		
	EnumGraphAlbumDetailEarth08,		
	EnumGraphAlbumDetailEarth09,		

	EnumGraphAlbumDetailWater00,		
	EnumGraphAlbumDetailWater01,		
	EnumGraphAlbumDetailWater02,		
	EnumGraphAlbumDetailWater03,		
	EnumGraphAlbumDetailWater04,		
	EnumGraphAlbumDetailWater05,		
	EnumGraphAlbumDetailWater06,		
	EnumGraphAlbumDetailWater07,		
	EnumGraphAlbumDetailWater08,		
	EnumGraphAlbumDetailWater09,		

	EnumGraphAlbumDetailFire00,		
	EnumGraphAlbumDetailFire01,		
	EnumGraphAlbumDetailFire02,		
	EnumGraphAlbumDetailFire03,		
	EnumGraphAlbumDetailFire04,		
	EnumGraphAlbumDetailFire05,		
	EnumGraphAlbumDetailFire06,		
	EnumGraphAlbumDetailFire07,		
	EnumGraphAlbumDetailFire08,		
	EnumGraphAlbumDetailFire09,		

	EnumGraphAlbumDetailWind00,		
	EnumGraphAlbumDetailWind01,		
	EnumGraphAlbumDetailWind02,		
	EnumGraphAlbumDetailWind03,		
	EnumGraphAlbumDetailWind04,		
	EnumGraphAlbumDetailWind05,		
	EnumGraphAlbumDetailWind06,		
	EnumGraphAlbumDetailWind07,		
	EnumGraphAlbumDetailWind08,		
	EnumGraphAlbumDetailWind09,		

	EnumGraphAlbumDetailStarVit00,		
	EnumGraphAlbumDetailStarVit01,		
	EnumGraphAlbumDetailStarVit02,		
	EnumGraphAlbumDetailStarVit03,		
	EnumGraphAlbumDetailStarVit04,		

	EnumGraphAlbumDetailStarStr00,		
	EnumGraphAlbumDetailStarStr01,		
	EnumGraphAlbumDetailStarStr02,		
	EnumGraphAlbumDetailStarStr03,		
	EnumGraphAlbumDetailStarStr04,		

	EnumGraphAlbumDetailStarTgh00,		
	EnumGraphAlbumDetailStarTgh01,		
	EnumGraphAlbumDetailStarTgh02,		
	EnumGraphAlbumDetailStarTgh03,		
	EnumGraphAlbumDetailStarTgh04,		

	EnumGraphAlbumDetailStarQui00,		
	EnumGraphAlbumDetailStarQui01,		
	EnumGraphAlbumDetailStarQui02,		
	EnumGraphAlbumDetailStarQui03,		
	EnumGraphAlbumDetailStarQui04,		

	EnumGraphAlbumDetailStarMgc00,		
	EnumGraphAlbumDetailStarMgc01,		
	EnumGraphAlbumDetailStarMgc02,		
	EnumGraphAlbumDetailStarMgc03,		
	EnumGraphAlbumDetailStarMgc04,		

	EnumGraphAlbumDetailWindow,		
	EnumGraphAlbumDetailBack,		

	EnumBTAlbumDetailAng,		

	EnumHitAlbumDetail1,		

	EnumAlbumDetailEnd,
};

const WINDOW_DATA WindowDataMenuAlbumDetail = {
 0,															// メニューウィンドウ
     4,  357,  90,256,289, 0x80010101, EnumAlbumDetailEnd,  AlbumDetailSwitch, MenuWindowAlbumDetail,MenuWindowAlbumDetailDraw,MenuWindowDel 
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateAlbumDetail={
	2,
	 0,  0,279,25,
	 249,  0,25,120,
};


#endif