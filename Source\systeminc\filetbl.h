﻿// main.cpp ヘッダファイル

#ifndef _FILETBL_H_
#define _FILETBL_H_


extern char binFolder[];
extern char _graphicBinName[];
extern char _graphicInfoBinName[];
extern char _animeInfoBinName[];
extern char _animeBinName[];
extern char _animeInfoBin[];

#ifdef MULTI_GRABIN
extern char _graphicBinName_plus[BINMODE_MAX][128];
extern char _graphicInfoBinName_plus[BINMODE_MAX][128];
extern char _animeBinName_plus[BINMODE_MAX][128];
extern char _animeInfoBinName_plus[BINMODE_MAX][128];
	#ifdef PUK3_RIDEBIN
	extern char _coordinateBinName_plus[BINMODE_MAX][128];
	extern char _coordinateInfoBinName_plus[BINMODE_MAX][128];
		#ifdef _DEBUG
		extern char coordinateLogName_plus[BINMODE_MAX][128];
		#endif
	#endif

extern char* graphicBinName[];
extern char* graphicInfoBinName[];
extern char* animeBinName[];
extern char* animeInfoBinName[];
	#ifdef PUK3_RIDEBIN
	extern char* coordinateBinName[];
	extern char* coordinateInfoBinName[];
		#ifdef _DEBUG
		extern char* coordinateLogName[];
		#endif
	#endif
#else
extern char graphicBinName[];
extern char graphicInfoBinName[];
extern char animeBinName[];
extern char animeInfoBinName[];
#endif


// ファイルチェック构造体
typedef struct{
	unsigned int uSize;
}CHECKBINFILE;

enum{
	// ------- First Version -------
	CHECK_GRAPHIC_BIN,		// Graphic.bin
	CHECK_GRAPHICINFO_BIN,	// GraphicInfo.Bin
	CHECK_ANIME_BIN, 		// Anime.Bin
	CHECK_ANIMEINFO_BIN, 	// AnimeInfo.Bin
	CHECK_BATTLE_BIN, 		// Battle.Bin
	CHECK_BATTLETXT_TXT,	// BattleTxt.Txt
	CHECK_SOUND_BIN, 		// Sound.Bin
	CHECK_SOUNDADDR_TXT, 	// SoundAddr.Txt
	#ifdef PUK3_RIDEBIN
		CHECK_CRD_BIN, 		// Coordinate.Bin
		CHECK_CRDINFO_BIN, 	// CoordinateInfo.Bin
	#endif
	CHECK_FIRST_END,
	// ------- Ex Version ----------
	// Graphic.bin
	CHECK_EX_GRAPHIC_BIN = CHECK_FIRST_END,
	CHECK_EX_GRAPHICINFO_BIN,	// GraphicInfo.Bin
	CHECK_EX_ANIME_BIN, 		// Anime.Bin
	CHECK_EX_ANIMEINFO_BIN, 	// AnimeInfo.Bin
	#ifdef PUK3_RIDEBIN
		CHECK_EX_CRD_BIN, 		// Coordinate.Bin
		CHECK_EX_CRDINFO_BIN, 	// CoordinateInfo.Bin
	#endif
	CHECK_EX_END,

	// ------- VER2 Version ----------
	CHECK_VER2_GRAPHIC_BIN = CHECK_EX_END, // Graphic.bin
	CHECK_VER2_GRAPHICINFO_BIN,	// GraphicInfo.Bin
	CHECK_VER2_ANIME_BIN, 		// Anime.Bin
	CHECK_VER2_ANIMEINFO_BIN, 	// AnimeInfo.Bin
	#ifdef PUK3_RIDEBIN
		CHECK_VER2_CRD_BIN, 		// Coordinate.Bin
		CHECK_VER2_CRDINFO_BIN, 	// CoordinateInfo.Bin
	#endif
	CHECK_VER2_END,

#ifdef PUK2
	// ------- VER3 Version ----------
	CHECK_VER3_GRAPHIC_BIN = CHECK_VER2_END, // Graphic.bin
	CHECK_VER3_GRAPHICINFO_BIN,	// GraphicInfo.Bin
	CHECK_VER3_ANIME_BIN, 		// Anime.Bin
	CHECK_VER3_ANIMEINFO_BIN, 	// AnimeInfo.Bin
	#ifdef PUK3_RIDEBIN
		CHECK_VER3_CRD_BIN, 		// Coordinate.Bin
		CHECK_VER3_CRDINFO_BIN, 	// CoordinateInfo.Bin
	#endif
	CHECK_VER3_END,

	// ------- PUK2 Version ----------
	CHECK_PUK2_GRAPHIC_BIN = CHECK_VER3_END, // Graphic.bin
	CHECK_PUK2_GRAPHICINFO_BIN,	// GraphicInfo.Bin
	CHECK_PUK2_ANIME_BIN, 		// Anime.Bin
	CHECK_PUK2_ANIMEINFO_BIN, 	// AnimeInfo.Bin
	#ifdef PUK3_RIDEBIN
		CHECK_PUK2_CRD_BIN, 		// Coordinate.Bin
		CHECK_PUK2_CRDINFO_BIN, 	// CoordinateInfo.Bin
	#endif
	CHECK_PUK2_END,
#endif
#ifdef PUK3_BIN
	// ------- PUK2 Version ----------
	CHECK_PUK3_GRAPHIC_BIN = CHECK_PUK2_END, // Graphic.bin
	CHECK_PUK3_GRAPHICINFO_BIN,	// GraphicInfo.Bin
	CHECK_PUK3_ANIME_BIN, 		// Anime.Bin
	CHECK_PUK3_ANIMEINFO_BIN, 	// AnimeInfo.Bin
	#ifdef PUK3_RIDEBIN
		CHECK_PUK3_CRD_BIN, 		// Coordinate.Bin
		CHECK_PUK3_CRDINFO_BIN, 	// CoordinateInfo.Bin
	#endif
	CHECK_PUK3_END,
#endif
};

extern CHECKBINFILE *CheckBinFileTbl[];

//---------------------------------------------------
// グラフィックファイルのチェックをする。
//---------------------------------------------------
BOOL CheckBinFile( char *pFileName, int iType );

void SetReadFileName( void );


//------------------------------------------------
//  ファイル名からファイルのバージョンを调べるs
//
//  aaaaaa_10.bin とかだったら 10を返す。
//  ファイル名の最后から '_' までを调べてその后ろにある
//  数字を返す。もし无かったら 0 を返す
//------------------------------------------------
int getBinFileVersion( char *pFileName );

int AllCheckBinFile( void );

#endif