﻿/************************/
/*	battle.h			*/
/************************/
#ifndef _BATTLE_H_
#define _BATTLE_H_

#ifdef PUK2_GUILMON_BATTLE
	#define BC_MAX_EX 22 	// バトルキャラクターの最大数(公会宠物等を含む)
#endif
#define BC_MAX 20 			// バトルキャラクターの最大数
#define BC_SORT_MAX 256		// ソートバトルキャラクター配列の最大数
#define BC_ACT_MAX 200		// 行动リストの最大值
#define BC_HIT_STOP_CNT 14	// ヒットストップフレーム数
#define BC_ATTAK_WAIT 10	// 攻击終了时のウェイト

#define B_ATTACK_DIST 70 	// 直接攻击する时の敌との距离
#define B_NEAR_DIST 8 		// 直接攻击する时の敌との近距离
#define B_MISSILE_DIST 20 	// 飞び道具が到着する距离
#define B_CHECK_DIST 5 		// 到着判定する距离

#define B_CHAR_DATA_SIZE		4096	// バトルキャラクターデーターサイズ
#define B_MOVIE_DATA_SIZE		4096	// バトルムービーデータサイズ
#define B_BUF_SIZE				4		// バトルバッファーサイズ
#define BM_CHAT_BUFFER_SIZE		140		// Ｂｍチャットバッファーサイズ

// バトル布ソンフラグ *******************************************************************
#define BP_FLAG_PLAYER_SURPRISAL	( 1 << 0 )		// 0x0001：プレイヤーが不意打をついた时
#define BP_FLAG_ENEMY_SURPRISAL		( 1 << 1 )		// 0x0002：敌が不意打ついた时（不意つかれた时）
#define BP_FLAG_PLAYER_MENU_NON		( 1 << 2 )		// 0x0004：プレイヤーメニュー无し
#define BP_FLAG_PET_MENU_NON		( 1 << 3 )		// 0x0008：ペットメニュー无し
#define BP_FLAG_PLAYER_MENU2_NON	( 1 << 4 )		// 0x0010：プレイヤーメニュー２无し（２アクション）
#define BP_FLAG_PET					( 1 << 5 )		// 0x0020：ペットあり（これによって２アクションかどうかを判断）

#define BP_FLAG_JOIN				( 1 << 6 )		// 0x0040：参战フラグ

#define BP_FLAG_WEAPON_DIRECT		( 1 << 7 )		// 0x0080：直接攻击武器フラグ（これによって前卫后卫システム作动）
#define BP_FLAG_WEAPON_BOW			( 1 << 8 )		// 0x0100：弓矢
#define BP_FLAG_WEAPON_BOOMERANG	( 1 << 9 )		// 0x0200：ブーメラン
#define BP_FLAG_WEAPON_KNIFE		( 1 << 10 )		// 0x0400：ナイフ
#ifdef PUK3_RIDE_BATTLE
#define BP_FLAG_RIDING				( 1 << 16 )		// 0x00010000：ライド中
#define BP_FLAG_PET_MENU2_NON		( 1 << 17 )		// 0x00020000：ペットメニュー２无し
#endif


// バトルキャラクターフラグ **************************************************************
#define BC_FLAG_APPEAR				( 1 << 0 )		// 0x00000001：登场
#define BC_FLAG_DEATH				( 1 << 1 )		// 0x00000002：死亡
#define BC_FLAG_PLAYER				( 1 << 2 )		// 0x00000004：プレイヤーフラグ
#define BC_FLAG_MON					( 1 << 3 )		// 0x00000008：モンスターフラグ

#define BC_FLAG_ABNORMAL_POISON		( 1 << 4 )		// 0x00000010：状态异常フラグ（毒）
#define BC_FLAG_ABNORMAL_SLEEP		( 1 << 5 )		// 0x00000020：状态异常フラグ（眠り）
#define BC_FLAG_ABNORMAL_STONE		( 1 << 6 )		// 0x00000040：状态异常フラグ（石化）
#define BC_FLAG_ABNORMAL_INEBRIETY	( 1 << 7 )		// 0x00000080：状态异常フラグ（酔い）
#define BC_FLAG_ABNORMAL_CONFUSION	( 1 << 8 )		// 0x00000100：状态异常フラグ（混乱）
#define BC_FLAG_ABNORMAL_FORGET		( 1 << 9 )		// 0x00000200：状态异常フラグ（忘却）

#define BC_FLAG_2ACT				( 1 << 10 )		// 0x00000400：２アクションフラグ

#define BC_FLAG_REVERSE_TYPE		( 1 << 11 )		// 0x00000800：属性反転フラグ

#define BC_FLAG_DAMAGE_UP			( 1 << 12 )		// 0x00001000：受伤アップフラグ

#define BC_FLAG_MORPH				( 1 << 13 )		// 0x00002000：变身中か

#define BC_FLAG_DEF_UP				( 1 << 14 )		// 0x00004000：防御力アップ中フラグ
#define BC_FLAG_DEF_DOWN			( 1 << 15 )		// 0x00008000：防御力ダウン中フラグ
#define BC_FLAG_ATK_UP				( 1 << 16 )		// 0x00010000：攻击力アップ中フラグ
#define BC_FLAG_ATK_DOWN			( 1 << 17 )		// 0x00020000：攻击力ダウン中フラグ
#define BC_FLAG_AGL_UP				( 1 << 18 )		// 0x00040000：すばやさアップ中フラグ
#define BC_FLAG_AGL_DOWN			( 1 << 19 )		// 0x00080000：すばやさダウン中フラグ
#ifdef _TEST_TECH_YUK
#define BC_FLAG_RCV_UP				( 1 << 20 )		// 0x00100000：回复力アップ中フラグ
#endif /* _TEST_TECH_YUK */
#ifdef PUK2
#define	BC_FLAG_ATTR_EARTH			( 1 << 24 )		// 0x01000000：地属性
#define	BC_FLAG_ATTR_WATER			( 1 << 25 )		// 0x02000000：水属性
#define	BC_FLAG_ATTR_FIRE			( 1 << 26 )		// 0x04000000：火属性
#define	BC_FLAG_ATTR_WIND			( 1 << 27 )		// 0x08000000：风属性
#endif
#ifdef PUK3_REBIRTHFLAG
	#define	BC_FLAG_REBIRTH				( 1 << 28 )		// 0x10000000：リ??バース中

	// バトルキャラクターフラグ(クライアント独自) **************************************************************
	#define	BCCL_FLAG_ALBUM				( 1 << 0 )		// 0x00000001：アルバムに登録されている
	#define	BCCL_FLAG_DEATHEND			( 1 << 1 )		// 0x20000002：死亡中（行动終了している）
#else
#ifdef PUK2
#define	BC_FLAG_ALBUM				( 1 << 28 )		// 0x10000000：アルバムに登録されている
#endif
#ifdef PUK2_BATTLE_END
#define	BC_FLAG_DEATHEND			( 1 << 29 )		// 0x20000000：死亡中（行动終了している）
#endif
#endif


// バトルキャラクターフィールドフラグ ****************************************************
#define BC_FIELD_FLAG_EARTH			( 1 << 0 )		// 0x0001：地 属性
#define BC_FIELD_FLAG_WATER			( 1 << 1 )		// 0x0002：水 属性
#define BC_FIELD_FLAG_FIRE			( 1 << 2 )		// 0x0004：火 属性
#define BC_FIELD_FLAG_WIND			( 1 << 3 )		// 0x0008：风 属性
#define BC_FIELD_FLAG_SILENCE		( 1 << 4 )		// 0x0010：静寂
#define BC_FIELD_FLAG_END			( 1 << 5 )		// 0x0020：战闘終了

// バトルムービーフラグ ******************************************************************
#define BM_FLAG_NORMAL				( 1 <<  0 )		// 0x00000001：通常攻击
#define BM_FLAG_CRITICAL			( 1 <<  1 )		// 0x00000002：クリティカル
#define BM_FLAG_GUARD				( 1 <<  2 )		// 0x00000004：防御
#define BM_FLAG_AVOIDANCE			( 1 <<  3 )		// 0x00000008：回避
#define BM_FLAG_COUNTER				( 1 <<  4 )		// 0x00000010：カウンター
#define BM_FLAG_DEATH				( 1 <<  5 )		// 0x00000020：気絶
#define BM_FLAG_AKO1				( 1 <<  6 )		// 0x00000040：アルティメットＫＯ１
#define BM_FLAG_AKO2				( 1 <<  7 )		// 0x00000080：アルティメットＫＯ２
#define BM_FLAG_MIKAWASHI			( 1 <<  8 )		// 0x00000100：身交わし
#define BM_FLAG_BODYGUARD			( 1 <<  9 )		// 0x00000200：护卫
#define BM_FLAG_SPECIAL_GUARD		( 1 << 10 )		// 0x00000400：スペシャル防御
#define BM_FLAG_MAGIC_GUARD			( 1 << 11 )		// 0x00000800：マジック防御
#define BM_FLAG_CROSS_COUNTER		( 1 << 12 )		// 0x00001000：クロスカウンター

#define BM_FLAG_REFLECTION_PHYSICS	( 1 << 13 )		// 0x00002000：物理反射
#define BM_FLAG_ABSORB_PHYSICS		( 1 << 14 )		// 0x00004000：物理吸收
#define BM_FLAG_INEFFECTIVE_PHYSICS	( 1 << 15 )		// 0x00008000：物理无效

#define BM_FLAG_REFLECTION_MAGIC	( 1 << 16 )		// 0x00010000：魔法反射
#define BM_FLAG_ABSORB_MAGIC		( 1 << 17 )		// 0x00020000：魔法吸收
#define BM_FLAG_INEFFECTIVE_MAGIC	( 1 << 18 )		// 0x00040000：魔法无效

#define BM_FLAG_SUCCESS				( 1 << 19 )		// 0x00080000：成功フラグ

#define BM_FLAG_WAKE_UP				( 1 << 20 )		// 0x00100000：お目覚めフラグ

#define BM_FLAG_INJURY				( 1 << 21 )		// 0x00200000：怪我フラグ

#ifdef PUK2
	// コメントの变更
	#define BM_FLAG_MY_WEPON_BREAK		( 1 << 22 )		// 0x00400000：武器が坏れそうフラグ
	#define BM_FLAG_MY_WEPON_BROKEN		( 1 << 23 )		// 0x00800000：武器が坏れたフラグ
	
	#define BM_FLAG_OTHER_WEPON_BREAK	( 1 << 24 )		// 0x01000000：武器以外の装备が坏れそうフラグ
	#define BM_FLAG_OTHER_WEPON_BROKEN	( 1 << 25 )		// 0x02000000：武器以外の装备が坏れたフラグ
#else
#define BM_FLAG_MY_WEPON_BREAK		( 1 << 22 )		// 0x00400000：自分の武器が坏れそうフラグ
#define BM_FLAG_MY_WEPON_BROKEN		( 1 << 23 )		// 0x00800000：自分の武器が坏れたフラグ

#define BM_FLAG_OTHER_WEPON_BREAK	( 1 << 24 )		// 0x01000000：相手の武器が坏れそうフラグ
#define BM_FLAG_OTHER_WEPON_BROKEN	( 1 << 25 )		// 0x02000000：相手の武器が坏れたフラグ
#endif

#ifdef _TEST_TECH_YUK
#define BM_FLAG_PARRY				( 1 << 27 )		// 0x08000000：捌く
#endif /* _TEST_TECH_YUK */

#ifdef PUK2
#define	BM_FLAG_REBIRTH_GUARD		( 1 << 28 )		// 0x10000000：リバース発动时の无敌防御
#endif
#ifdef PUK2_NEWSKILL_BARRIER
#define	BM_FLAG_BARRIER				( 1 << 29 )		// 0x20000000：バリアスキルのバリア效果
#endif
#ifdef PUK3_MONSTER_HELPER
	#define	BM_FLAG_DAMAGE_UP			( 1 << 30 )		// 0x40000000：武器防具种族による优劣
	#define	BM_FLAG_DAMAGE_DOWN			( 1 << 31 )		// 0x80000000：武器防具种族による优劣
#endif

// 飞び道具の种类 *******************************************************************
#define BM_MSL_BOW					( 1 << 0 )		// 0x0001：弓矢
#define BM_MSL_BOOMERANG			( 1 << 1 )		// 0x0002：ブーメラン
#define BM_MSL_KNIFE				( 1 << 2 )		// 0x0004：ナイフ

// バトルキャラクターの行动
typedef enum{
	BC_ACT_END = -1,			// 終端记号
	
	BC_ACT_STANDBY,				// 待机
	BC_ACT_STANDBY2,			// 待机2
	BC_ACT_APPEAR,				// 登场
	
	BC_ACT_MOVE,				// 通常歩き
	BC_ACT_MOVE_NEAR,			// 歩き移动近距离
	BC_ACT_MOVE_BACK,			// 里侧に回る
	BC_ACT_MOVE_POINT,			// 指定の座标に移动する
	
	BC_ACT_B_MOVE_START,		// 战闘歩き前
	BC_ACT_B_MOVE_START_2,		// 战闘歩き前２（ポイント移动用）
	BC_ACT_B_MOVE,				// 战闘歩き
	BC_ACT_B_MOVE_END,			// 战闘歩き后
	BC_ACT_MOVE_DEF, 			// デフォルト位置に移动
	BC_ACT_MOVE_ESC, 			// 逃げる准备
	BC_ACT_MOVE_OUT, 			// 画面外に移动
	BC_ACT_ATTACK,				// 攻击
	BC_ACT_THROW,				// 投げアニメーション
	BC_ACT_MSL,					// 飞び道具攻击
	BC_ACT_MSL_DEATH,			// 飞び道具抹杀
	BC_ACT_BOW,					// 弓アニメーション
	BC_ACT_ALLOW,				// 矢の移动
	BC_ACT_BOOMERANG_START,		// ブーメラン初期ポイントへ移动
	BC_ACT_BOOMERANG,			// ブーメラン移动
	BC_ACT_BOOMERANG_RETURN,	// ブーメラン戾ってくる
	BC_ACT_BOOMERANG_CATCH,		// ブーメラン受け取る
	BC_ACT_ENEMY_STANDBY_WAIT,	// 待机になるまで待つ
	BC_ACT_HIT_STOP,			// ヒットストップ
	BC_ACT_HIT_BACK,			// ヒットバック处理
	BC_ACT_HIT_BACK2,			// ヒットバック处理２（护卫用）
	BC_ACT_HIT_BACK_BOW,		// ヒットバック处理（乱れ打ち用）
	BC_ACT_AVOIDANCE,			// 回避处理
	BC_ACT_DAMAGE,				// 受伤
	BC_ACT_GUARD,				// 防御
	BC_ACT_AKO1,				// アルティメットＯＫ１
	BC_ACT_AKO2,				// アルティメットＯＫ２
	
	BC_ACT_WAIT_05,				// ０．５秒间待つ
	BC_ACT_WAIT_1,				// １秒间待つ
	BC_ACT_WAIT_2,				// ２秒间待つ
	BC_ACT_SPIN,				// 回転
	
	BC_ACT_CHANGE_DEF_DIR,		// デフォルトの方向へ向ける
	BC_ACT_CHANGE_OTHER_ID_DIR,	// 他のＩＤの方向へ向ける
	BC_ACT_CHANGE_OTHER_ID_DIR_2,// 他のＩＤの方向へ向ける（アクション版）
	BC_ACT_CHANGE_OTHER_POINTER,// 自分をpOtherの方向へ向ける
	BC_ACT_CHANGE_MY_ID_DIR,	// 相手を自分のＩＤの方向へ向ける
	BC_ACT_LP_0,				// 体力を０にする
	BC_ACT_DEAD,				// 死亡
	BC_ACT_DEAD_FLAG_ON,		// 死亡フラグ立てる
	BC_ACT_DEAD_FLASH_1,		// 死亡点灭１
	BC_ACT_DEAD_FLASH_2,		// 死亡点灭２
	
	BC_ACT_DEAD_2,				// 死亡（アニメーション）
	
	BC_ACT_DEATH_ACT,			// 自分のアクション抹杀
	BC_ACT_PET_OUT,				// ペットを退场させる
	
	BC_ACT_PET_CHANGE,			// ペット戾す
	BC_ACT_PET_CHANGE2,			// ペット出す
	
	BC_ACT_CONSUME_FP,			// フォースポイントマイナス
	
	BC_ACT_ANIM_ATTACK,			// 攻击アニメーション
	BC_ACT_HAPPY,				// 喜ぶ
	BC_ACT_ANGRY,				// 怒る
	BC_ACT_SAD,					// 悲伤
	BC_ACT_SAD_2,				// 悲伤２（終了まで待たない）
	BC_ACT_HAND,				// 挥手
	BC_ACT_CHOKI,				// 剪刀
	BC_ACT_KYORO,				// きょろきょろする
	BC_ACT_ANIM_STAND,			// 立ちアニメーションに戾す
	
	BC_ACT_PET_NO,				// ペットが嫌がる
	
	BC_ACT_EQUIP_CHANGE,		// 装备变更
	BC_ACT_PET_SUMMON,			// ペット召唤
	
	
	BC_ACT_JUMP_DISP,			// グラフィックジャンプさせる
	BC_ACT_GRA_DISP,			// グラフィック表示（ビックリマーク）
	
	BC_ACT_SKILL_SPIRACLE_SHOT_START,	// 气孔弹素手アニメーション
	
	BC_ACT_ITEM_RECOVERY,		// アイテム回复处理
	
	BC_ACT_CMB_STANDBY_OK,		// 合体攻击准备完了通知
	
	BC_ACT_ENEMY_NEXT_ACT_LIST,	// 相手を次の行动にする
	BC_ACT_NEXT_ENEMY_LIST,		// 次の敌リストへ
	
	BC_ACT_ZANZOU_ON,			// 残像フラグＯＮ
	BC_ACT_ZANZOU_OFF,			// 残像フラグＯＦＦ
	
	BC_ACT_TENMETU_ON,			// 点灭フラグＯＮ
	BC_ACT_TENMETU_OFF,			// 点灭フラグＯＦＦ
	
	BC_ACT_REVIVE,				// 気絶回复处理
	
	BC_ACT_PLAY_SE,				// 效果音鸣らす
	BC_ACT_PLAY_SE_2,			// 效果音鸣らす２
	BC_ACT_PLAY_SE_3,			// 效果音鸣らす２
	
	//BC_ACT_STEAL_GET,			// 盗む成功
	//BC_ACT_PLAY_FAIL,			// 盗む失败
	
	BC_ACT_DANCE_POW_UP_SCALE,	// 受伤アップ扩大
	BC_ACT_DANCE_POW_DOWN_SCALE,// 受伤ダウン缩小
	BC_ACT_SCALE_2,				// ２倍に膨らます
	BC_ACT_BOM_START,			// 自爆スタート
	BC_ACT_FOG_EFFECT,			// 烟エフェクト
	BC_ACT_QUAKE_ON,			// 地震フラグＯＮ
	BC_ACT_QUAKE_OFF,			// 地震フラグＯＦＦ
	BC_ACT_SACRIFICE,			// サクリファイス
	BC_ACT_HIDE,				// アクション隠す
	BC_ACT_HIDE_OFF,			// 隠すフラグＯＦＦ
	BC_ACT_SLOW_FLAG_OFF,		// 自分のスローフラグをＯＦＦにする
	
	BC_ACT_WARP_ENEMY_BACK,		// 敌の后ろに瞬间ワープ
	BC_ACT_MOVE_ASSASSIN,		// 暗杀移动
	
	BC_ACT_ANIM_ON,				// 相手をアニメーションさせる
	BC_ACT_ANIM_OFF,			// 相手のアニメーションを止める

	BC_ACT_KAKURAN_GENERATE,	// 搅乱演出
	
#ifdef _TEST_TECH_YUK
	BC_ACT_ANIMSPD_X4,			// アニメーションスピード４倍早くする
	BC_ACT_RANBU_ACT,			// ランダムなアクションをする

	BC_ACT_ATTACKALL_MOVE,		// 全体攻击移动
	BC_ACT_ATTACKALL_HIT,		// 全体攻击ヒット
	BC_ACT_ATTACKALL_ILLUSION,	// 分身を飞ばす

	BC_ACT_DETECTENEMY,			// 敌を调べる

	BC_ACT_URGENTALLOWANCE,		// 紧急手当
	BC_ACT_HAPPY_2,				// 喜ぶ２（終了まで待たない）

	BC_ACT_DESTRUCT_ILLUSION,	// 分身を破弃する

	BC_ACT_AXEBOMBER,			// アックスボンバー

	BC_ACT_ULTIMATEATTACK_START,	// 狮子咆移动准备
	BC_ACT_ULTIMATEATTACK_MOVE,	// 狮子咆移动
	BC_ACT_ULTIMATEATTACK_AKO,	// 狮子咆アルティメットKO

	BC_ACT_ITEM_THROW,			// アイテム投げ
	BC_ACT_THROWING_ITEM_MOVE,	// 投げアイテム移动
	BC_ACT_THROWING_ITEM_HIT,	// 投げアイテムヒット
#endif /* _TEST_TECH_YUK */

// エフェクト *************************************************
	BC_ACT_EFFECT_COUNTER,		// カウンターエフェクト
	
	BC_ACT_EFFECT_DRAIN,		// ドレイン实行时
	BC_ACT_EFFECT_DRAIN_2,		// ドレイン吸收时



	BC_ACT_EFFECT_CROSS_COUNTER,// クロスカウンター
	
	BC_ACT_EFFECT_ABNORMAL_STATUS,	// 状态异常实行
	BC_ACT_EFFECT_DEFENSE_MAGIC,	// 防御系魔法实行（反射、吸收、无效）

	BC_ACT_EFFECT_REFLECTION_PHYSICS,	// 物理反射エフェクト
	BC_ACT_EFFECT_ABSORB_PHYSICS,		// 物理吸收エフェクト
	BC_ACT_EFFECT_INEFFECTIVE_PHYSICS,	// 物理无效エフェクト
	BC_ACT_EFFECT_REFLECTION_MAGIC,		// 魔法反射エフェクト
	BC_ACT_EFFECT_ABSORB_MAGIC,			// 魔法吸收エフェクト
	BC_ACT_EFFECT_INEFFECTIVE_MAGIC,	// 魔法无效エフェクト

	BC_ACT_EFFECT_REFLECTION_PHYSICS_NO_WAIT,	// 物理反射エフェクト
	BC_ACT_EFFECT_ABSORB_PHYSICS_NO_WAIT,		// 物理吸收エフェクト
	BC_ACT_EFFECT_INEFFECTIVE_PHYSICS_NO_WAIT,	// 物理无效エフェクト
	

	BC_ACT_EFFECT_RECOVERY,		// 体力再生エフェクト（实行时）
	BC_ACT_EFFECT_LP_RECOVER,	// 体力再生エフェクト（継続时）
	
	BC_ACT_EFFECT_CONSENTRATION,// 精神统一エフェクト
	

	BC_ACT_EFFECT_STATUS_RECOVER,// 状态异常回复
	
	BC_ACT_EFFECT_TERROR,		// 即死魔法
	BC_ACT_EFFECT_SACRIFICE,	// サクリファイス
	
	
	BC_ACT_EFFECT_ITEM_GENERATE,// アイテム使用エフェクト
	BC_ACT_EFFECT_MAGIC_GENERATE,// 魔法使用エフェクト
	BC_ACT_EFFECT_HIT_MAGIC_SINGLE, // 攻击魔法作成
	
	BC_ACT_EFFECT_REFLECTION_MAGIC_GENERATE, // 魔法反射発动エフェクト作成
	
	BC_ACT_ABNORMAL_ON,			// 状态异常マーク作成
	BC_ACT_ABNORMAL_OFF,		// 状态异常マーク消す
	
	BC_ACT_PARAMETER_UP_DOWN_ON,	// パラメータアップダウンマーク作成
	BC_ACT_PARAMETER_UP_DOWN_OFF,	// パラメータアップダウンマーク消す
	
	BC_ACT_TREAT_TYPE_ON,		// 属性优遇エフェクト作成
	
	BC_ACT_EFFECT_REVERSE_TYPE,		// 属性反転エフェクト（实行时）作成
	BC_ACT_REVERSE_TYPE_ON,		// 属性反転エフェクト（継続时）作成
	
	BC_ACT_EFFECT_REVIVE,		// 気絶回复エフェクト作成
	
	BC_ACT_EFFECT_LP_INCREASE,	// 体力回复エフェクト
	
	BC_ACT_EFFECT_SKILL_GENERATE,	// スキル発生エフェクト
	
	BC_ACT_EFFECT_PET_IN,	// ペット戾すエフェクト
	BC_ACT_EFFECT_PET_OUT,	// ペット出すエフェクト
	
	BC_ACT_EFFECT_CARD_APPEAR,		// カード出现
	BC_ACT_EFFECT_MON_SEAL,			// モンスター封印处理
	BC_ACT_EFFECT_CARD_DISAPPEAR,	// カード消灭
	
	BC_ACT_EFFECT_PARAMETER,			// 白波エフェクト
	
	//BC_ACT_EFFECT_SKILL_GENERATE,	// スキル発生エフェクト
	//BC_ACT_EFFECT_SKILL_GENERATE,	// スキル発生エフェクト
	//BC_ACT_EFFECT_SKILL_GENERATE,	// スキル発生エフェクト
	
	
	BC_ACT_EFFECT_POINT_DISP,	// 数字表示
	BC_ACT_EFFECT_POINT_DISP_WAIT,	// 数字表示（ウェイトあり）

#ifdef _TEST_TECH_YUK
	BC_ACT_EFFECT_SUPERSKILL_GENERATE,	// スー布スキル発生エフェクト
#endif /* _TEST_TECH_YUK */
	
	BC_ACT_EFFECT_WAIT,		// エフェクト終了待ち

#ifdef PUK2
	BC_ACT_TRANCE,			// トランス
	BC_ACT_TRANCE_DEATH,
	BC_ACT_HIT_BACK_CRITICAL,
	BC_ACT_BST_SUB,
#endif
#ifdef PUK2
	BC_ACT_RGBA__ON,			// プログラム中色变更ON
	BC_ACT_RGBA_OFF,			// プログラム中色变更OFF
	BC_ACT_BLTF__ON,			// プログラム中绘图设定变更ON
	BC_ACT_BLTF_OFF,			// プログラム中绘图设定变更OFF

	BC_ACT_FADEIN,				// フェードインする
	BC_ACT_FADEOUT_1,			// フェードアウトする
	BC_ACT_FADEOUT_2,			// フェードアウトする

	BC_ACT_PALCHG,				// パレットチェンジをする

	BC_ACT_MAKEBG,				// 背景を作成(フェードインさせる)
	BC_ACT_DEATHBG,				// 背景を削除(フェードアウトさせる)
#ifdef PUK3_NEWSKILL_LVDOWN
	BC_ACT_CHANGEBG,			// 背景の变更
#endif

	BC_ACT_STOP,				// キャラの行动を一时中止する
	
	BC_ACT_MAGIC,				// 魔法使用アニメーション


	BC_ACT_EFFECT_RECOVERY_GUILMON,					// 回复エフェクト
	BC_ACT_EFFECT_DRAIN_GUILMON,					// ドレイン实行时
	BC_ACT_EFFECT_DRAIN_2_GUILMON,					// ドレイン吸收时
	BC_ACT_EFFECT_STATUS_RECOVER_GUILMON,			// 状态异常回复
	BC_ACT_EFFECT_LP_INCREASE_GUILMON,				// 体力回复エフェクト
	BC_ACT_EFFECT_SKILL_GENERATE_GUILMON,			// スキル発生エフェクト

	BC_ACT_EFFECT_PET_IN_GUILMON,					// ペット戾すエフェクト
	BC_ACT_EFFECT_PET_OUT_GUILMON,					// ペット出すエフェクト

	BC_ACT_EFFECT_CROSS_COUNTER_GUILMON,			// クロスカウンター
	BC_ACT_EFFECT_ABNORMAL_STATUS_GUILMON,			// 状态异常实行
	BC_ACT_EFFECT_DEFENSE_MAGIC_GUILMON,			// 防御系魔法实行（反射、吸收、无效）
	BC_ACT_EFFECT_REFLECTION_PHYSICS_GUILMON,		// 物理反射エフェクト
	BC_ACT_EFFECT_ABSORB_PHYSICS_GUILMON,			// 物理吸收エフェクト
	BC_ACT_EFFECT_INEFFECTIVE_PHYSICS_GUILMON,		// 物理无效エフェクト
	BC_ACT_EFFECT_REFLECTION_MAGIC_GUILMON,			// 魔法反射エフェクト
	BC_ACT_EFFECT_ABSORB_MAGIC_GUILMON,				// 魔法吸收エフェクト
	BC_ACT_EFFECT_INEFFECTIVE_MAGIC_GUILMON,		// 魔法无效エフェクト
	BC_ACT_EFFECT_TERROR_GUILMON,					// 即死魔法
	BC_ACT_EFFECT_SACRIFICE_GUILMON,				// サクリファイス

	BC_ACT_EFFECT_LP_RECOVER_GUILMON,				// 体力再生エフェクト（継続时）状态异常回复
	BC_ACT_EFFECT_CONSENTRATION_GUILMON,			// 精神统一エフェクト
	BC_ACT_EFFECT_REVERSE_TYPE_GUILMON,				// 属性反転エフェクト（实行时）作成
	BC_ACT_EFFECT_PARAMETER_GUILMON,				// 属性反転エフェクト（实行时）作成
#ifdef _TEST_TECH_YUK
	BC_ACT_EFFECT_SUPERSKILL_GENERATE_GUILMON,		// スー布スキル発生エフェクト
#endif /* _TEST_TECH_YUK */

	BC_ACT_EFFECT_WAIT_FADEIN,

	BC_ACT_NOD,
#endif
#ifdef PUK2_NEWSKILL
	BC_ACT_CHANGE_ENEMY_DIR,		// 敌の方向へ向かせる
	BC_ACT_MOVE_PONT_LIST,			// 指定目的地へ移动
	BC_ACT_MOVE_PONT_FRONT,			// キャラの手前(デフォルト位置から见ての)へ移动
	BC_ACT_HIT_BACK_ATTACK,			// 一石二鸟用ヒットバック
	BC_ACT_MOVEx2,					// 移动x2
	BC_ACT_CHANGE_WEATHER,			// 天气变更
	BC_ACT_SMOKE,					// 烟
	BC_ACT_DEAD_3,					// 死亡３
	BC_ACT_DEAD_3_FLASH_1,			// 死亡３点灭１
	BC_ACT_DEAD_3_FLASH_2,			// 死亡３点灭２
	BC_ACT_EFFECT_MAGIC_GENERATE_2,	// 魔法使用エフェクト２
	BC_ACT_EFFECT_BARRIER,			// バリアエフェクト
	BC_ACT_METAMO,					// 变身
	BC_ACT_METAMO_DEATH,			// 变身时死亡
#ifdef PUK2_NEWSKILL_KYOSHIIJO
	BC_ACT_METAMO_DEATH2,			// 变身时死亡
#endif
	BC_ACT_INNGAOHO,				// 因果应报
	BC_ACT_BOOMERANG_RETURN_BODYGUARD,	// ブーメラン戾ってくる(护卫)
#endif
#ifdef PUK3_RIDE_BATTLE
	BC_ACT_MYPET_CHANGE,			// プレイヤーが今出しているペットを示す数值を变更する
	BC_ACT_RIDE,					// ライド
	BC_ACT_RIDE_MISS,				// ライド失败
	BC_ACT_RIDE_DEATH,				// ライド死亡
#endif
#ifdef PUK3_NEWSKILL_BREAK
	BC_ACT_RIDE_CANCEL,				// ライド解除(すぐにペットは次の行动へ)
	BC_ACT_IDLING,					// アイドリング
	BC_ACT_IDLING_MAIN,				// アイドリングのメイン部分
	BC_ACT_CURVE,					// 曲がる
	BC_ACT_BRAKE,					// ブレーキ
	BC_ACT_TACKLE,					// 体当たり
	BC_ACT_HIT_SPIN,				// ヒットバック处理(回転するだけ)
	BC_ACT_AKO1_BREAK,				// アルティメットＯＫ１(ブレイク自分用)
	BC_ACT_INJURY_CHECK,			// 怪我のチェック(ブレイク自分用)
#endif
#ifdef PUK2_BATTLE_SLOW
	BC_ACT_ACT_END,					// 行动終了でスタンドバイ状态に
#endif
#ifdef PUK3_STEAL_ATTACK
	BC_ACT_CAPTURE,					// 分捕る
#endif
#ifdef PUK3_NEWSKILL_PSYBLAST
	BC_ACT_HOMING,					// ホーミング弹
	BC_ACT_HOMING2,					// ホーミング弹
	BC_ACT_MAGICSHOT,				// 魔法打ち出し
	BC_ACT_MAGICSHOT2,				// 魔法打ち出し
	BC_ACT_MAGICSHOT_REFLECTION,	// 魔法打ち出し反射
	BC_ACT_MSL_REFLECTION,			// 飞び道具攻击反射（矢とナイフ、气孔弹）
	BC_ACT_DEAD_FLAG_ON_WITH_SLOW,	// 死亡フラグ立てる
#endif
#ifdef PUK3_NEWSKILL_JUMP
	BC_ACT_JUMP_UP,					// ジャンプ上升
	BC_ACT_JUMP_UP_MIDST,			// ジャンプ上升最中
	BC_ACT_JUMP_DOWN,				// ジャンプ下降
	BC_ACT_JUMP_DOWN_MIDST,			// ジャンプ下降最中
#endif
#ifdef PUK3_R10
	BC_ACT_MAKE_FLASH,				// フラッシュを作成する
#endif
#ifdef PUK3_TOPPUSLOW
	BC_ACT_SETSPEED,				// 移动速度の设定
#endif
#ifdef PUK3_COUNTER_REFLECT
	BC_ACT_SETDAMAGEPOINT,			// 受伤前の位置记忆（カウンター用）
	BC_ACT_MOVE_DAMAGE,				// 受伤前の位置へ移动
#endif
#ifdef TECH_BLASTWAVE			    //追月
	BC_ACT_SKILL_BLASTWAVE_START,
#endif

	BC_ACT_BATTLE_END,			// 战闘終了
}BC_ACT;

#if defined(PUK3_PUT_ON) || defined(PUK3_RIDE_BATTLE)
// プライオリティーが同じ场合のプライオリティー
enum{
	BC_EQUAL_PRIO_CHAR = 0,
	BC_EQUAL_PRIO_RIDECHAR,
	BC_EQUAL_PRIO_PUTON,
};
#endif
#ifdef PUK2
	#define SPR_TRANCE_ATACK SPR_jibaku
	#define SPR_TRANCE_DAMEGE SPR_sk_start_b
#endif

// バトルキャラクター位置管理构造体
typedef struct{
	int startX, startY;
	int defX, defY;
}BC_POS;


// キャラクターの位置顺番
extern int BcPosId[];
// バトルキャラクター位置管理构造体
extern BC_POS BcPos[];

#ifdef WIN_SIZE_DEF
//根据窗口分辨率计算战斗站位坐标
extern BC_POS BcPosTmp[];
#endif

// 敌リスト构造体
typedef struct{
	int enemyId; 		// 敌のＩＤ
	int bmFlag; 		// バトルキャラクターフラグ
	int damage; 		// 受伤
	int newGraNo; 		// 新グラフィック番号
}BC_ENEMY_LIST;

// バトルキャラクターの予备构造体
typedef struct{
	int defX, defY;						// デフォルトの座标
	int startX, startY;					// スタート座标
	int damageX, damageY;				// 受伤を食らった座标
	int myId;							// 自分のＩＤ
	BC_ENEMY_LIST enemyList[ BC_ACT_MAX ];		// 敌のリスト构造体
	int enemyListCnt;					// 敌のＩＤリストのカウンター
	int enemySetListCnt;				// 设定时の敌のＩＤリストのカウンター
	BC_ACT actList[ BC_ACT_MAX ];		// 行动リスト（マスターがいれる）
	int actListCnt;						// 行动リストのカウンター
	int actSetListCnt;					// 设定时の行动リストのカウンター
	int hitBackCnt;						// ヒットバック时のカウンター
	int hitStopCnt;						// ヒットストップ时のカウンター
	int attackWait;						// 攻击終了时のウェイト
	int actFlag;						// 行动フラグ（ＴＲＵＥ：行动中）
	int bcFlag;							// バトルキャラクターフラグ
#ifdef PUK3_REBIRTHFLAG
	int bcFlagCl;						// バトルキャラクターフラグ(クライアント独自フラグ用)
#endif
	int bmFlag;							// 相手から教えてもらうバトルムービーフラグ
	int moveX, moveY;					// 移动ポイント记忆用
	
	int waitCnt;						// ウェイトカウンター
	int weaponId;						// 武器ＩＤ
	int actSlowFlag;					// スローフラグ
	int actDeathFlag;					// 気絶时の行动終了フラグ
	int actZanzouCnt;					// 残像カウンター
	int actZanzouFlag;					// 残像フラグ
	int consumeFp;						// 消费フォースポイント
	int otherId;						// 他のＩＤ记忆
	int jumpGraNo;						// ジャンプグラフィック番号
	int epuipChangeGraNo;				// 装备变更グラフィック番号
	int seNo;							// ＳＥ番号
	int seNo2;							// ＳＥ番号２
	int seNo3;							// ＳＥ番号３
	int effectFlag;						// エフェクト中フラグ
	int effectNo;						// エフェクト番号
	int attackEffectNo;					// 攻击时エフェクト番号
	int point;							// ポイント记忆
	int color;							// 色记忆
	int hitStopFlag;					// 强制ヒットストップフラグ
	int skillId;						// 现在のスキル番号
	int techId;							// 现在のテック番号
	int danceId;						// 踊り效果ＩＤ（踊りスキルのみ使用）
	int newGraNo;						// 新しいグラフィック番号
	int kakuranKind;					// 搅乱时の状态异常の种类
	ACTION *pAbnormal;					// 状态异常ポインタ
	ACTION *p2Action;					// ２アクション用ポインタ
	ACTION *pReverseType;				// 属性反転用ポインタ
	ACTION *pCard;						// 封印カード用ポインタ
	ACTION *pStun;						// 気絶用ポインタ
	ACTION *pParameterUpDown;			// パラメーターアップダウン用ポインタ
#ifdef PUK2
	ACTION *pActTrance;					// トランスエフェクト用ポインタ
	ACTION *pActBmm;					// このアクションを操作するバトルマスター（ミラー）
	ACTION *pActSubRbth[3];				// トランスエフェクト用ポインタ
	char RbthElement;					// リバースの属性

	int bstsub[ BC_ACT_MAX ];			// バーストタイム减少量
	int bstListCnt;						// 行动リストのカウンター
	int bstSetListCnt;					// 设定时の行动リストのカウンター
	int originGraNo;					// 变身解除时のキャラクターグラフィック
#endif
#ifdef PUK2_NEWSKILL_YOTOKUNIKU
	BOOL metamoFlag;					// 变身しているかを示すフラグ
#endif
#ifdef PUK3_PUT_ON
	ACTION *pActPuton;					// 被り物用ポインタ
#endif
#ifdef PUK3_RIDE_BATTLE
	ACTION *pActRideChar;				// ライドキャラ用ポインタ
#endif
}BC_YOBI;

// バトルキャラクターアクションポインタ
#ifdef PUK2_GUILMON_BATTLE
	extern ACTION *pActBc[ BC_MAX_EX ];
#else
extern ACTION *pActBc[ BC_MAX ];
#endif
#ifdef PUK3_RIDE_BATTLE_2

// ライド时に分离后のキャラデータ保存などに使う
typedef struct{
	ACTION *pActCh;		// キャラのデータ
	ACTION *pActPt;		// ペットのデータ
	int bcFlag;			// フラグ
	int putonGraNo;		// カブリモノのグラフィック番号

	// 合体时、以下のデータはこの构造体を保有するACTIONに格纳
	//	name		← 名称
	//	anim_chr_no	← キャラのグラフィック番号
	//	level		← 等级
	//	hp			← 体力
	//	maxHp		← 最大体力
	//	fp			← 魔力
	//	maxFp		← 最大魔力
}RIDE_YOBI;

#endif

// 表示バッファソート ***************************************************/
void SortBattleChar();

// 方向からアングルに变换する **************************************************/
void DirToAngle( ACTION *pAct );
// その方向に移动する **************************************************/
void MoveDir( ACTION *pAct );

#ifdef PUK2_BOOMERANG_ORBIT_1
	// あるポイントより左侧なのか右侧なのかチェック
	// 左なら-1、右なら1、どちらでもないなら0を返す、angは余裕
	char CheckUpperLeftOrLowerRight( float x1, float y1, float x2, float y2, float ang );
#endif
#ifdef PUK2_ICHIGEKI_ORBIT
	// 角度差を求める
	float CheckDir( ACTION *pAct, float x, float y );
#endif
#ifdef PUK2_BOOMERANG_ORBIT_1
	// 角度差を求める
	float CheckDir( ACTION *pAct, ACTION *pAct2 );
#endif
// 目的の方向に向ける **************************************************/
//
//	ACTION *pAct：向き变えをするアクションポインタ
//	float x：目的のＸ座标
//	float y：目的のＹ座标
//	戾り值：目的地との距离
//	pAct->dir に方向（角度）が入る
//
//**********************************************************************/
void ChangeDir( ACTION *pAct, float x, float y, int flag, float angle );
void ChangeDir( ACTION *pAct, ACTION *pAct2, int flag, float angle );

// デフォルトの方向へ向ける ********************************************/
void ChangeDefDir( ACTION *pAct );

// 目的地との距离を求める **************************************************/
//
//	ACTION *pAct：このアクションポインタとの距离を求める
//	float x：目的のＸ座标
//	float y：目的のＹ座标
//	戾り值：目的地との距离
//
//**********************************************************************/
int CheckDistance( ACTION *pAct, float x, float y );
int CheckDistance( ACTION *pAct, ACTION *pAct2 );

// バトルキャラクターデータの読み込み ****************************************/
void ReadBcData( void );
// リストの初期化 ************************************************************/
void BattleInitList( void );

// 敌リストをセットする **************************************************************/
void BattleSetEnemyList( ACTION *pAct, int enemyId, int bmFlag, int damage );
// 次の敌リストへ **************************************************************/
BOOL BattleNextEnemyList( ACTION *pAct );
#ifdef PUK2
	// バースト时间减少リストをセットする
	void BattleSetBstList( ACTION *pAct, int time );
	// 次のバースト时间减少リストへ
	BOOL BattleNextBstList( ACTION *pAct );
#endif

// 受伤表示タスク作成
#ifdef PUK3_MONSTER_HELPER
	#ifdef _BATTLE_EFFECT_H_
		ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind, int bmFlag );
	#else
		ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind, int bmFlag = 0 );
	#endif
#else
ACTION *MakeBattlePointDisp( int x, int y, int point, int color, int fontKind );
#endif

#ifdef PUK2
	// 时间表示タスク作成 *******************************************************/
	ACTION *MakeBattleTimeDisp( int x, int y, int time );
#endif

// ペット番号を调べる ******************************************************/
int BattleCheckPetId( int myId );
// フォースポイントマイナス处理 **********************************************/
void BattleConsumeFp( ACTION *pAct );
// 全员のアニメーションをＯＮ，ＯＦＦを切り替える *****************************************************/
void BattleAnimOff( int flag );
// Ｂｍフラグにチャット文字列があるかチェック *****************************************************
#ifdef PUK3_WEPON_BREAK
void BattleCheckBmStrGraphic( int myId, ACTION *pAct );
#else
void BattleCheckBmStrGraphic( int myId, BC_ENEMY_LIST *enemyList );
#endif
// 方向を调整する（０～３５９）*****************************************/
void AdjustDir( ACTION *pAct );

#ifdef PUK2
	unsigned char MakeElementWL( ACTION *pAt, ACTION *pDm );
#endif

#ifdef PUK3_PACTBC_CHECKRANGE
	void _CheckIdRange( int id, char *file, int line );
	#define CheckIdRange( id ) _CheckIdRange( id, __FILE__, __LINE__ );
#endif
#endif
