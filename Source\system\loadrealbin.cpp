﻿#define UNPACK2	0
#define WIN32_LEAN_AND_MEAN
#include <io.h>
#include <time.h>
#include <sys\types.h>
#include <sys\stat.h>
#include <stdio.h>
#include <stdlib.h>
#include "../systeminc/system.h"
#include "../systeminc/main.h"
#include "../systeminc/loadrealbin.h"
#include "../systeminc/unpack.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/directDraw.h"

#define arraysizeof( a ) ( sizeof(a)/sizeof(a[0]) )

#ifdef MULTI_GRABIN
// グラビンの名称が复数あるばあいはこれ。
static char REALBINFILENAME[BINMODE_MAX][255];
static char ADDRBINFILENAME[BINMODE_MAX][255];
#else
static char REALBINFILENAME[255];
static char ADDRBINFILENAME[255];
#endif

#define LINESTRINGS			255

unsigned char autoMapColorTbl[65536];	// オートマップ作成时の各画像の色テーブル
ADRNBIN *adrnbuff = NULL;

unsigned long *bitmapnumbertable = NULL;//ナンバーからビットマップ番号を引くテーブル U2

//unsigned char autoMapColorTbl[MAX_GRAPHICS];	// オートマップ作成时の各画像の色テーブル


// 配列で直接アクセス
#define BMP_NUMBER2TBL( a ) ( bitmapnumbertable[(a)] )

FILE  *Addrbinfp, *debughistory;

#ifdef MULTI_GRABIN
FILE*	Realbinfp[BINMODE_MAX];	// グラビンが复数ある场合はこれ。
#else
FILE *Realbinfp;	// グラビン单独の场合
#endif

int iAllBmpNum = 0;	// ビットマップの全部の数

BITMAPINFO		tmpBitMapInfo;


PBITMAPINFO 		PBiInfo;
PBYTE				PBits;
BITMAPFILEHEADER	header;

LONG	GetWidth(){return PBiInfo->bmiHeader.biWidth;}
LONG	GetHeight(){return PBiInfo->bmiHeader.biHeight;}
LONG	GetLineSize(){return (PBiInfo->bmiHeader.biWidth + 3) & ~3;}
DWORD	GetRealSize(){return GetWidth()*GetHeight();}
DWORD	GetImageSize(){return GetLineSize()*GetHeight();}

struct RECODEHEAD{
	unsigned short magicnumber;
	long size;
};

// メモリ节约のための旧型构造体
// 无駄なデータはメモリに置かない。
typedef struct {
	unsigned char atari_x,atari_y;	//大きさ
	unsigned short hit;				//步けるか
	short indamage;				//入HP受伤（无駄）
	short outdamage;			//出受伤　（无駄）
	unsigned int bmpnumber;		//画像番号
} MAP_ATTR2;

struct ADRNBIN2{
	unsigned long	bitmapno;
	unsigned long	adder;
	unsigned long	size;
	int	xoffset;
	int	yoffset;
	unsigned int width;
	unsigned int height;

	MAP_ATTR2 attr;
};
#ifdef PUK2
	int g_id_num;
#endif


#ifdef MULTI_GRABIN

int BinOffsetTbl[BINMODE_MAX];	// BIN モード别のインフォOFFセット

//ファイルを开いてみる
//无事开けたらTRUEを返す
#ifdef PUK2
int initRealbinFileOpen(char* realbinfilename[], char* addrbinfilename[])
#else
BOOL initRealbinFileOpen(char* realbinfilename[], char* addrbinfilename[])
#endif
{
	//ADRNBIN tmpadrnbuff;
	// 无駄なデータ变换用构造体
	ADRNBIN2 tmpadrnbuff2;

	int count = 0, fsize = 0, num, offset = 0, seqnumber;
	unsigned int MaxIndex = 0;
	int i;

	for( i = 0; i < BINMODE_MAX; i ++ ){
		Realbinfp[i] = NULL;
	}


	// 开けっ放しにするグラフィック实体ファイルをオープン
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// 一应ファイル名コピー
		strcpy(REALBINFILENAME[i], realbinfilename[i]);

		//Graphic.bin开き（っぱなし）
		if ((Realbinfp[i] = fopen(REALBINFILENAME[i], "rb"))==NULL){        //MLHIDE
			// 失败したら次のファイルをあける。
			// 初期のファイルが开けなかったらエラー
#ifdef PUK2
			if( i == 0 )goto initRealbinFileOpen_Err;
			if( i == 3 )goto initRealbinFileOpen_Err;
#else
			if( i == 0 )goto initRealbinFileOpen_Err;
#endif
		}

	}

	// インフォファイルのサイズの合计を计算する。
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// 一应ファイル名コピー
		strcpy(ADDRBINFILENAME[i], addrbinfilename[i]);
		//adrn.bin开き
		if ((Addrbinfp = fopen(ADDRBINFILENAME[i], "rb"))==NULL){           //MLHIDE
			// 失败したら次のファイルをあける。
#ifdef PUK2
			if( i == 0 )return 1;
			if( i == 3 )return 2;
#else
			if( i == 0 )return FALSE;
#endif
			continue;
		}
		// ファイルサイズ取得
		fseek( Addrbinfp, 0, SEEK_END );
		// 全体のサイズに加算。
		fsize += ftell( Addrbinfp );
		fclose( Addrbinfp );
	}

	// adrnbin 全部のBMP个数を割り出す
	num = fsize / sizeof(tmpadrnbuff2);	
	num = num + 10;

#ifdef PUK2
	g_id_num=num;
#endif
	// 最大が配列を超えていたらエラー
	if( num >= MAX_GRAPHICS ){
		char szBuffer[256];
#ifdef PUK3_ERRORMESSAGE_NUM
		sprintf( szBuffer, ERRMSG_93, MAX_GRAPHICS,num );
#else
		sprintf( szBuffer, "MAX_GRAPHICS=%d,Need=%d", MAX_GRAPHICS,num );   //MLHIDE
#endif
		MessageBox( hWnd, szBuffer, "sprmgr.h MAX_GRAPHICS不足。", MB_OK );    //MLHIDE
		goto initRealbinFileOpen_Err;
	}


	num = num + 10;
#ifdef PUK2
	bitmapnumbertable = ( unsigned long *)malloc( sizeof( unsigned long ) * (MAX_GRAPHICS+10) );
#else
	bitmapnumbertable = ( unsigned long *)malloc( sizeof( unsigned long ) * (num) );
#endif
	if( bitmapnumbertable == NULL){
		// メモリ确保できん。
#ifdef PUK2
		return -1;
#else
		return FALSE;
#endif
	}
#ifdef PUK2_MEMCHECK
	memlistset( bitmapnumbertable, MEMLISTTYPE_BMPNUMTABLE );
#endif
#ifdef PUK2
	memset( bitmapnumbertable, 0, sizeof( unsigned long ) * (MAX_GRAPHICS) );
#else
	memset( bitmapnumbertable, 0, sizeof( unsigned long ) * (num) );
#endif
	// グラフィックインフォをオープンしてテーブルに整顿する。
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// このモードのOFFセットを学习。
		BinOffsetTbl[i] = offset;
		//adrn.bin开き
		if ((Addrbinfp = fopen(ADDRBINFILENAME[i], "rb"))==NULL){           //MLHIDE
#ifdef PUK2
			if( i == 0 )return 1;
			if( i == 3 )return 2;
#else
			if( i == 0 )return FALSE;
#endif
			continue;
		}

		// adrn.binバッファに読み込み
		while( !feof(Addrbinfp) ){ // ファイルなくなるまで
			// １个読み込み
			fread(&tmpadrnbuff2, sizeof(tmpadrnbuff2), 1, Addrbinfp);
			count ++; // 読んだ数カウント

			// 必要なデータだけコピーして本当の构造体に入れる
			seqnumber = tmpadrnbuff2.bitmapno + offset;	// OFFセットを足した通番
			adrnbuff[seqnumber].bitmapno = seqnumber;	// 通番セット
			adrnbuff[seqnumber].adder = tmpadrnbuff2.adder;	// 场所
			adrnbuff[seqnumber].size = tmpadrnbuff2.size;	// サイズ
			adrnbuff[seqnumber].xoffset = tmpadrnbuff2.xoffset; // ＸＹOFFセット
			adrnbuff[seqnumber].yoffset = tmpadrnbuff2.yoffset;
			adrnbuff[seqnumber].width = tmpadrnbuff2.width;	// 幅と高さ
			adrnbuff[seqnumber].height = tmpadrnbuff2.height;
			// 当りデータ
			adrnbuff[seqnumber].attr.atari_x = tmpadrnbuff2.attr.atari_x;
			adrnbuff[seqnumber].attr.atari_y = tmpadrnbuff2.attr.atari_y;
			adrnbuff[seqnumber].attr.hit = tmpadrnbuff2.attr.hit;	
			// 画像番号（ビットマップ番号）
			adrnbuff[seqnumber].attr.bmpnumber = tmpadrnbuff2.attr.bmpnumber;

			// モード
			adrnbuff[seqnumber].BinMode = i;

			// bitmapnumbertable[ビットマップ番号] = 通し番号
			// ファイル名が画像番号のとき（タイル、布ツ、ＮＰＣなど）
			// tmpadrnbuff2.attr.bmpnumber にファイル名の画像番号が入っている
			if ( tmpadrnbuff2.attr.bmpnumber != 0){
//				bitmapnumbertable[tmpadrnbuff2.attr.bmpnumber] = tmpadrnbuff2.bitmapno;

				// 配列オーバーしそうだったら入れない。
				if( tmpadrnbuff2.attr.bmpnumber < (unsigned int)MAX_GRAPHICS )
                {
					bitmapnumbertable[tmpadrnbuff2.attr.bmpnumber] = seqnumber;
				}
				// 最大更新
				if( MaxIndex < tmpadrnbuff2.attr.bmpnumber )
                {
					MaxIndex = tmpadrnbuff2.attr.bmpnumber;
				}
			}
            else
            {
				// 意味あるのか？
				bitmapnumbertable[0] = 0;
			}
		}
		//adrn.bin关闭
		fclose(Addrbinfp);
		offset = count;	// 今回まで読んだカウンタが次のOFFセット
	}


#ifdef PUK2
	return 0;
#else
	return TRUE;
#endif /* PUK2 */

initRealbinFileOpen_Err:
#ifdef PUK2
	num = i;
#endif /* PUK2 */
	// あいているファイルをクローズ
	for( i = 0; i < BINMODE_MAX; i ++ )
    {
		if( Realbinfp[i] != NULL )
        {
			fclose(Realbinfp[i]);
			Realbinfp[i] = NULL;
		}
	}
#ifdef PUK2
	if( num == 0 )return 1;
	if( num == 3 )return 2;
	return 3;
#else
	return FALSE;
#endif /* PUK2 */

}

#else





//ファイルを开いてみる
//无事开けたらTRUEを返す
BOOL initRealbinFileOpen(char *realbinfilename, char *addrbinfilename)
{
	int count = 0, fsize = 0, num;
	strcpy(REALBINFILENAME, realbinfilename);
	strcpy(ADDRBINFILENAME, addrbinfilename);
	
	// 无駄なデータ变换用构造体
	ADRNBIN2 tmpadrnbuff2;
	//ADRNBIN tmpadrnbuff;
	
	//Graphic.bin开き（っぱなし）
	if ((Realbinfp = fopen(REALBINFILENAME, "rb"))==NULL){               //MLHIDE
		return FALSE;
	}

	//adrn.bin开き
	if ((Addrbinfp = fopen(ADDRBINFILENAME, "rb"))==NULL){               //MLHIDE
		return FALSE;
	}

	// adrn.bin のサイズ読み取り
	// ファイルサイズ取得
	fseek( Addrbinfp, 0, SEEK_END );
	fsize = ftell( Addrbinfp );
	fseek( Addrbinfp, 0, SEEK_SET );	// 最初に戾す
	num = fsize / sizeof(tmpadrnbuff2);	// adrnbin 内部のBMP个数を割り出す


	//adrn.binバッファに読み込み
	while(!feof(Addrbinfp))
	{

		fread(&tmpadrnbuff2, sizeof(tmpadrnbuff2), 1, Addrbinfp);

		// 必要なデータだけコピーして本当の构造体に入れる
		adrnbuff[tmpadrnbuff2.bitmapno].bitmapno = tmpadrnbuff2.bitmapno;
		adrnbuff[tmpadrnbuff2.bitmapno].adder = tmpadrnbuff2.adder;
		adrnbuff[tmpadrnbuff2.bitmapno].size = tmpadrnbuff2.size;
		adrnbuff[tmpadrnbuff2.bitmapno].xoffset = tmpadrnbuff2.xoffset;
		adrnbuff[tmpadrnbuff2.bitmapno].yoffset = tmpadrnbuff2.yoffset;
		adrnbuff[tmpadrnbuff2.bitmapno].width = tmpadrnbuff2.width;
		adrnbuff[tmpadrnbuff2.bitmapno].height = tmpadrnbuff2.height;
		
		adrnbuff[tmpadrnbuff2.bitmapno].attr.atari_x = tmpadrnbuff2.attr.atari_x;
		adrnbuff[tmpadrnbuff2.bitmapno].attr.atari_y = tmpadrnbuff2.attr.atari_y;
		adrnbuff[tmpadrnbuff2.bitmapno].attr.hit = tmpadrnbuff2.attr.hit;
		adrnbuff[tmpadrnbuff2.bitmapno].attr.bmpnumber = tmpadrnbuff2.attr.bmpnumber;

		// bitmapnumbertable[ビットマップ番号] = 通し番号
		// ファイル名が画像番号のとき（タイル、布ツ、ＮＰＣなど）
		// tmpadrnbuff2.attr.bmpnumber にファイル名の画像番号が入っている
		if ( tmpadrnbuff2.attr.bmpnumber != 0){
			bitmapnumbertable[tmpadrnbuff2.attr.bmpnumber] = tmpadrnbuff2.bitmapno;
		}else{
			// 意味あるのか？
			bitmapnumbertable[0] = 0;
			//bitmapnumbertable[tmpadrnbuff.bitmapno] = tmpadrnbuff.bitmapno;
		}
	}
	//adrn.bin关闭
	fclose(Addrbinfp);

	return TRUE;
}

#endif




#ifdef MULTI_GRABIN
// オートマップ用画像色の作成または読み込み
void initAutoMapColor( char* addrbinfilename[] )
{
	char szAutoFileName[128];
	sprintf( szAutoFileName, "%s\\%s", dataFolder, "auto.dat" );         //MLHIDE

	// オートマップ用画像色のファイルから読みこむ
	if( readAutoMapColor( szAutoFileName, addrbinfilename ) == 0 ){
		// 无いのでオートマップ用画像色を作成
		makeAutoMapColor();
		writeAutoMapColor( szAutoFileName, addrbinfilename );
	}

}
#else
// オートマップ用画像色の作成または読み込み
void initAutoMapColor( char *addrbinfilename )
{
	char szAutoFileName[128];
	sprintf( szAutoFileName, "%s\\%s", dataFolder, "auto.dat" );         //MLHIDE

	// オートマップ用画像色のファイルから読みこむ
	if( readAutoMapColor( szAutoFileName, addrbinfilename ) == 0 )
	{
		// 无いのでオートマップ用画像色を作成
		makeAutoMapColor();
		writeAutoMapColor( szAutoFileName, addrbinfilename );
	}
}
#endif


int LoadPaletteToBuffer( int palNo, PALETTEENTRY *pPalette );

// オートマップ用画像色の作成
void makeAutoMapColor( void )
{
	PALETTEENTRY	tmpPalette[256];	// パレット番号０用の构造体
	int i, bmpno, index;
	int allnum = MAX_GRAPHICS;
	// パレットバッファ初期化
	memset( tmpPalette, 0, sizeof(tmpPalette) );
	// デフォルトパレット読み込み
	if( LoadPaletteToBuffer( 0, tmpPalette ) == FALSE ){
		// 失败
		return ;
	}
	// テーブル初期化
	memset( autoMapColorTbl, 0, sizeof( autoMapColorTbl ) );
	// 全部の画像番号でループ
	for( i = 0; i < allnum; i++ ){

		bmpno = adrnbuff[i].attr.bmpnumber;
		index = i;
#ifdef PUK2_OLDAUTOMAP_SKIP
		// Ｖ２以降のbinは无视( 0(黒)でよい)
		if ( 
			 !( adrnbuff[i].BinMode == BINMODE_NORMAL ||
				adrnbuff[i].BinMode == BINMODE_EX ||
#ifdef VERSION_TW
				adrnbuff[i].BinMode == BINMODE_JOY ||
				adrnbuff[i].BinMode == BINMODE_JOYCH )
			) {
			/*V3(3),PUK3(4)中均有编号为230000以上图档,这里用作数组下标会导致数组下标越界而报错
			并且65535以上的内容并不会使用(保存adrnbuff内容的auto.dat大小为64k),所以尝试加个判断在这里.
			官方cg越界赋值不会报错,可能是编译器不同的缘故?待研究
			*/
			if (bmpno < 65536)
#else
				 adrnbuff[i].BinMode == BINMODE_VER2 )
			) {
#endif

			autoMapColorTbl[bmpno] = 0;
		}else
#endif
		// 画像０なら何もしない。( 0(黒)でよい)
		if( bmpno == 0 ){
			autoMapColorTbl[bmpno] = 0;
		}else
		// 通常タイル布ツＩＤ
		if( (TILEPARTS_NORMAL_START <= bmpno && bmpno <= TILEPARTS_NORMAL_END) ){
			// タイルと布ツの范围だったら
			autoMapColorTbl[bmpno] = getAutoMapColor( adrnbuff[index].bitmapno, tmpPalette );
		}else
		// 新タイル布ツＩＤ
		if( (TILEPARTS_EX_START <= bmpno && bmpno <= TILEPARTS_EX_END) ){ 	
			bmpno -= TILEPARTS_EX_OFFSET;	// ２０万引いておく
			autoMapColorTbl[bmpno] = getAutoMapColor( adrnbuff[index].bitmapno, tmpPalette );
		}else{
			// これに该当しないものは处理しない。
			;;;
		}
	}
}


#ifdef MULTI_GRABIN

// オートマップのバージョン
#define AUTOMAP_COLOR_VERSION 2

// オートマップ用画像色をファイルに书き込む
//  戾り值：  1 ... 成功
//            0 ... 失败
int writeAutoMapColor( char *wFName, char *addrbinfilename[] )
{
	FILE *wfp, *rfp;
	int rfh;
	struct _stat statBuf;
	unsigned int adrnTime;
	unsigned int adrnExTime = 0;
#ifdef VERSION_TW
	unsigned int adrnJoyTime = 0;
#endif
	unsigned int adrnV2Time = 0;
	unsigned short autoMapColorVersion = AUTOMAP_COLOR_VERSION;	


	// 通常インフォビンの作成日チェック
	if( (rfp = fopen( addrbinfilename[BINMODE_NORMAL], "rb" )) == NULL )return 0; //MLHIDE

	rfh = _fileno( rfp );
	if( _fstat( rfh, &statBuf ) < 0 ){
		fclose( rfp );
		return 0;
	}
	// 日付を取得して
	adrnTime = (unsigned int)statBuf.st_ctime;
	fclose( rfp );	// クローズ

	// ＥＸインフォビンの作成日チェック
	// 読めなくてもエラーではない。その场合は日付は０とする。
	if( (rfp = fopen( addrbinfilename[BINMODE_EX], "rb" )) != NULL ){    //MLHIDE
		rfh = _fileno( rfp );
		if( _fstat( rfh, &statBuf ) < 0 ){
		}else{
			// 日付を取得して
			adrnExTime = (unsigned int)statBuf.st_ctime;
		}
		fclose( rfp );	// クローズ
	}

#ifdef VERSION_TW
	if ((rfp = fopen(addrbinfilename[BINMODE_JOY], "rb")) != NULL) {     //MLHIDE
		rfh = _fileno(rfp);
		if (_fstat(rfh, &statBuf) < 0) {
		}
		else {
			// 日付を取得して
			adrnJoyTime = (unsigned int)statBuf.st_ctime;
		}
		fclose(rfp);	// クローズ
	}
#else
	// Ｖ２インフォビンの作成日チェック
	// 読めなくてもエラーではない。その场合は日付は０とする。
	if( (rfp = fopen( addrbinfilename[BINMODE_VER2], "rb" )) != NULL ){  //MLHIDE
		rfh = _fileno( rfp );
		if( _fstat( rfh, &statBuf ) < 0 ){
		}else{
			// 日付を取得して
			adrnV2Time = statBuf.st_ctime;
		}
		fclose( rfp );	// クローズ
	}
#endif


	// データ书きこみ
	if( (wfp = fopen( wFName, "wb" )) == NULL )return 0;                 //MLHIDE


	fwrite( &autoMapColorVersion, sizeof( autoMapColorVersion ), 1, wfp );
	fwrite( &adrnTime, sizeof( adrnTime ), 1, wfp );
	fwrite( &adrnExTime, sizeof( adrnExTime ), 1, wfp );
#ifdef VERSION_TW
	fwrite(&adrnJoyTime, sizeof(adrnJoyTime), 1, wfp);
#endif
	fwrite( &adrnV2Time, sizeof( adrnV2Time ), 1, wfp );
	fwrite( &autoMapColorTbl, sizeof( autoMapColorTbl ), 1, wfp );

	fclose( wfp );

	return 1;
}

#else


// オートマップ用画像色をファイルに书き込む
//  戾り值：  1 ... 成功
//            0 ... 失败
int writeAutoMapColor( char *wFName, char *addrbinfilename )
{
	FILE *wfp, *rfp;
	int rfh;
	struct _stat statBuf;
	int adrnNo;
	unsigned int adrnTime;
	unsigned short autoMapColorVersion = 0;	
	char *tmpStr;


	// GraphicInfo.binのバージョン番号チェック
	tmpStr = strstr( addrbinfilename, "GraphicInfo" );                   //MLHIDE
	if( tmpStr == NULL )
		return 0;	// 文字列无かったら終わる
	if( tmpStr[11] == '.' )
	{
		adrnNo = 0;	// デバッグバージョン
	}
	else
	{
		adrnNo = -1;
		sscanf( tmpStr, "GraphicInfo_%d.bin", &adrnNo );                    //MLHIDE
		if( adrnNo < 0 )
			return 0;
	}

	// adrn.binの作成日チェック
	if( (rfp = fopen( addrbinfilename, "rb" )) == NULL )                 //MLHIDE
		return 0;

	rfh = _fileno( rfp );
	if( _fstat( rfh, &statBuf ) < 0 )
	{
		fclose( rfp );
		return 0;
	}
	adrnTime = statBuf.st_ctime;
	fclose( rfp );

	// データ书きこみ
	if( (wfp = fopen( wFName, "wb" )) == NULL )                          //MLHIDE
		return 0;

	fwrite( &autoMapColorVersion, sizeof( autoMapColorVersion ), 1, wfp );
	fwrite( &adrnNo, sizeof( adrnNo ), 1, wfp );
	fwrite( &adrnTime, sizeof( adrnTime ), 1, wfp );
	fwrite( &autoMapColorTbl, sizeof( autoMapColorTbl ), 1, wfp );

	fclose( wfp );

	return 1;
}

#endif









#ifdef MULTI_GRABIN
// オートマップ用画像色をファイルから読みこむ
//  戾り值：  1 ... 成功
//            0 ... 失败
int readAutoMapColor( char *wFName, char *addrbinfilename[] )
{
	FILE *rfp = NULL;
	int rfh;
	struct _stat statBuf;
	unsigned int adrnTime, rAdrnTime;
	unsigned int adrnExTime = 0, rAdrnExTime;
#ifdef VERSION_TW
	unsigned int adrnJoyTime = 0, rAdrnJoyTime;
#endif
	unsigned int adrnV2Time = 0, rAdrnV2Time;
	unsigned short autoMapColorVersion = AUTOMAP_COLOR_VERSION, rAutoMapColorVersion;


	//--------------------------------------------------------------
	// 通常グラビンの作成日付を取得
	//--------------------------------------------------------------
	// ファイルをオープンして情报を取得する。
	if( (rfp = fopen( addrbinfilename[BINMODE_NORMAL], "rb" )) == NULL )return 0; //MLHIDE
	rfh = _fileno( rfp );
	if( _fstat( rfh, &statBuf ) < 0 ){	// エラーが出たら
		fclose( rfp );		// 失败
		return 0;
	}
	adrnTime = (unsigned int)statBuf.st_ctime;	// 日付を取得してクローズ
	fclose( rfp );

	//--------------------------------------------------------------
	// ＥＸグラビンの作成日付を取得
	//--------------------------------------------------------------
	// ファイルをオープンして情报を取得する。
	// ただしオープン出来なくてもエラーではない。

	adrnExTime = 0;	// 日付はクリアする。
	if( (rfp = fopen( addrbinfilename[BINMODE_EX], "rb" )) != NULL ){    //MLHIDE
		rfh = _fileno( rfp );
		if( _fstat( rfh, &statBuf ) < 0 ){	// エラーが出たら
		}else{
			adrnExTime = (unsigned int)statBuf.st_ctime;	// 日付を取得してクローズ
		}
		fclose( rfp );
	}

#ifdef VERSION_TW
	adrnJoyTime = 0;	// 日付はクリアする。
	if ((rfp = fopen(addrbinfilename[BINMODE_JOY], "rb")) != NULL) {     //MLHIDE
		rfh = _fileno(rfp);
		if (_fstat(rfh, &statBuf) < 0) {	// エラーが出たら
		}
		else {
			adrnJoyTime = (unsigned int)statBuf.st_ctime;	// 日付を取得してクローズ
		}
		fclose(rfp);
	}
#else
	//--------------------------------------------------------------
	// Ｖ２グラビンの作成日付を取得
	//--------------------------------------------------------------
	// ファイルをオープンして情报を取得する。
	// ただしオープン出来なくてもエラーではない。その际日付は０にする。

	adrnV2Time = 0;	// 日付はクリアする。
	if( (rfp = fopen( addrbinfilename[BINMODE_VER2], "rb" )) != NULL ){  //MLHIDE
		rfh = _fileno( rfp );
		if( _fstat( rfh, &statBuf ) < 0 ){	// エラーが出たら
		}else{
			adrnV2Time = statBuf.st_ctime;	// 日付を取得してクローズ
		}
		fclose( rfp );
	}
#endif

	//----------------------------------------------------------------
	//  Auto.dat のバージョンが正しいかチェック
	//----------------------------------------------------------------
	// auto.dat のバージョン番号読み込み
	if( (rfp = fopen( wFName, "rb" )) == NULL )return 0;                 //MLHIDE
	// バージョン読み込み
	if( fread( &rAutoMapColorVersion, sizeof( rAutoMapColorVersion ), 1, rfp ) != 1 ){
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	if( autoMapColorVersion != rAutoMapColorVersion ){
		// バージョン违うので終わる
		fclose( rfp );
		return 0;
	}


	//------------------------------------------------
	// 各种グラビンの时刻チェック
	//------------------------------------------------
	// 通常ＢＩＮの时间読み込み
	if( fread( &rAdrnTime, sizeof( rAdrnTime ), 1, rfp ) != 1 ){
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	if( adrnTime != rAdrnTime ){
		// 时间违うので終わる
		fclose( rfp );
		return 0;
	}


	// ＥＸＢＩＮの时间読み込み
	if( fread( &rAdrnExTime, sizeof( rAdrnExTime ), 1, rfp ) != 1 ){
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	if( adrnExTime != rAdrnExTime ){
		// 时间违うので終わる
		fclose( rfp );
		return 0;
	}

#ifdef VERSION_TW
	if (fread(&rAdrnJoyTime, sizeof(rAdrnJoyTime), 1, rfp) != 1) {
		// 読み込み失败
		fclose(rfp);
		return 0;
	}
	if (adrnJoyTime != rAdrnJoyTime) {
		// 时间违うので終わる
		fclose(rfp);
		return 0;
	}
	// Ｖ２ＢＩＮの时间読み込み
	if( fread( &rAdrnV2Time, sizeof( rAdrnV2Time ), 1, rfp ) != 1 ){
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
#else
	if( adrnV2Time != rAdrnV2Time ){
		// 时间违うので終わる
		fclose( rfp );
		return 0;
	}
#endif



	// 实データ読み込み
	if( fread( &autoMapColorTbl, sizeof( autoMapColorTbl ), 1, rfp ) != 1 )
	{
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	fclose( rfp );

	return TRUE;
}
#else


// オートマップ用画像色をファイルから読みこむ
//  戾り值：  1 ... 成功
//            0 ... 失败
int readAutoMapColor( char *wFName, char *addrbinfilename )
{
	FILE *rfp;
	int rfh;
	struct _stat statBuf;
	int adrnNo, rAdrnNo;
	unsigned int adrnTime, rAdrnTime;
	unsigned short autoMapColorVersion = 0, rAutoMapColorVersion;
	char *tmpStr;


	// GraphicInfo.binのバージョン番号取得
	tmpStr = strstr( addrbinfilename, "GraphicInfo" );                   //MLHIDE
	if( tmpStr == NULL )
		return 0;	// 文字列无かったら終わる
	if( tmpStr[11] == '.' )
	{
		adrnNo = 0;	// デバッグバージョン
	}
	else
	{
		adrnNo = -1;
		sscanf( tmpStr, "GraphicInfo_%d.bin", &adrnNo );                    //MLHIDE
		if( adrnNo < 0 )
			return 0;
	}

	// adrn.binの作成日取得
	if( (rfp = fopen( addrbinfilename, "rb" )) == NULL )                 //MLHIDE
		return 0;

	rfh = _fileno( rfp );
	if( _fstat( rfh, &statBuf ) < 0 )
	{
		fclose( rfp );
		return 0;
	}
	adrnTime = statBuf.st_ctime;
	fclose( rfp );

	// データ読み込み
	if( (rfp = fopen( wFName, "rb" )) == NULL )                          //MLHIDE
		return 0;

	// バージョン読み込み
	if( fread( &rAutoMapColorVersion, sizeof( rAutoMapColorVersion ), 1, rfp ) != 1 )
	{
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	if( autoMapColorVersion != rAutoMapColorVersion )
	{
		// バージョン违うので終わる
		fclose( rfp );
		return 0;
	}
	// adrn.binバージョン読み込み
	if( fread( &rAdrnNo, sizeof( rAdrnNo ), 1, rfp ) != 1 )
	{
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	if( adrnNo != rAdrnNo )
	{
		// バージョン违うので終わる
		fclose( rfp );
		return 0;
	}
	// adrn.bin时间読み込み
	if( fread( &rAdrnTime, sizeof( rAdrnTime ), 1, rfp ) != 1 )
	{
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	if( adrnTime != rAdrnTime )
	{
		// 时间违うので終わる
		fclose( rfp );
		return 0;
	}

	// 实データ読み込み
	if( fread( &autoMapColorTbl, sizeof( autoMapColorTbl ), 1, rfp ) != 1 )
	{
		// 読み込み失败
		fclose( rfp );
		return 0;
	}
	fclose( rfp );

	return TRUE;
}

#endif


//ファイルを闭じようとする
//开くことが出来なかった场合でも、问题无し。
void cleanupRealbin(void)
{
#ifdef PUK3_NOTFREE_GRAPHICBIN
	// ＩＤテーブルの解放
	if ( bitmapnumbertable ){ free( bitmapnumbertable );	bitmapnumbertable = NULL; }
#endif
#ifdef MULTI_GRABIN
	int i;
	for( i = 0; i < BINMODE_MAX; i ++ ){
		// オープンされていたらクローズ
		if( Realbinfp[i] != NULL ){
			fclose(Realbinfp[i]);
			Realbinfp[i] = NULL;
		}
	}
#else
	fclose(Realbinfp);
#endif
#if 0
#ifdef _DEBUG
	fclose(debughistory);
#endif
#endif
}



//グラフィックナンバーより立ち位置をもらう
//	戾值 -1:失败 or 以外成功
BOOL realGetPos(U4 GraphicNo , S2 *x , S2 *y)
{
	if(GraphicNo<0 || GraphicNo>=(unsigned int)MAX_GRAPHICS){*x=0;*y=0;return FALSE;}
	*x = adrnbuff[GraphicNo].xoffset;
	*y = adrnbuff[GraphicNo].yoffset;
	return TRUE;
}

//グラフィックナンバーからグラフィック幅、高さを返す
//	戾值 -1:失败 or 以外成功
BOOL realGetWH(U4 GraphicNo , S2 *w , S2 *h)
{
	if(GraphicNo<0 || GraphicNo>=(unsigned int)MAX_GRAPHICS){*w=0;*h=0;return FALSE;}

	*w = adrnbuff[GraphicNo].width;
	*h = adrnbuff[GraphicNo].height;

	return TRUE;
}

//グラフィックナンバーより当たりサイズを求める。
//	戾值 -1:失败 or 以外成功
BOOL realGetHitPoints(U4 GraphicNo , S2 *HitX , S2 *HitY)
{
	if(GraphicNo<0 || GraphicNo>=(unsigned int)MAX_GRAPHICS){*HitX=0;*HitY=0;return FALSE;}

	*HitX = adrnbuff[GraphicNo].attr.atari_x;
	*HitY = adrnbuff[GraphicNo].attr.atari_y;

	return TRUE;
}

//グラフィックナンバーより当たり判定を求める。
//	戾值 -1:失败 or 以外成功
BOOL realGetHitFlag(U4 GraphicNo , S2 *Hit)
{
	if(GraphicNo<0 || GraphicNo>=(unsigned int)MAX_GRAPHICS){
		*Hit=0;
		return FALSE;
	}

	*Hit = (adrnbuff[GraphicNo].attr.hit % 100);

	return TRUE;
}

//グラフィックナンバーより描画优先顺位决定法を取り出す。
//	戾值 -1:失败 or 以外成功
BOOL realGetPrioType(U4 GraphicNo , S2 *prioType)
{
	if(GraphicNo<0 || GraphicNo>=(unsigned int)MAX_GRAPHICS){
		*prioType=0;
		return FALSE;
	}

	*prioType = (adrnbuff[GraphicNo].attr.hit / 100);
	return TRUE;
}

#if 0

//グラフィックナンバーより高さの有无を求める。
//	戾值 -1:失败 or 以外成功
BOOL realGetHeightFlag(U4 GraphicNo , S2 *Height)
{
	if(GraphicNo<0 || GraphicNo>=MAX_GRAPHICS){*Height=0;return FALSE;}
	*Height = adrnbuff[GraphicNo].attr.height;

	return TRUE;
}

//グラフィックナンバーより环境音を
//	戾值 -1:失败 or 以外成功
int realGetSoundEffect(U4 GraphicNo)
{
	if(GraphicNo<0 || GraphicNo>=MAX_GRAPHICS)return FALSE;
//	return adrnbuff[bitmapnumbertable[GraphicNo]].attr.effect1;
	return adrnbuff[BMP_NUMBER2TBL(GraphicNo)].attr.effect1;

}

//グラフィックナンバーより步行音を
//	戾值 -1:失败 or 以外成功
BOOL realGetWalkSoundEffect(U4 GraphicNo )
{
	if(GraphicNo<0 || GraphicNo>=MAX_GRAPHICS){return FALSE;}
	return adrnbuff[GraphicNo].attr.effect2;

}
#endif

//引数にビットマップファイル番号(ファイル名の数字のみ)を与えて通し番号を返す
//	戾值 -1:失败 or 以外成功
BOOL realGetNo( U4 CharAction , U4 *GraphicNo )
{
//	if(CharAction<0 || CharAction>=MAX_GRAPHICS){*GraphicNo=0;return FALSE;}
	if(CharAction<0 ){ *GraphicNo=0;return FALSE;}
//	*GraphicNo = bitmapnumbertable[CharAction];
	*GraphicNo = BMP_NUMBER2TBL(CharAction);
	return TRUE;
}

//
//  引数に通し番号を与えてビットマップファイル番号を返す ( realGetNo の逆)
//  by Thai
int realGetBitmapNo( int num )
{

	if( num < 0 || num >= MAX_GRAPHICS ) return -1;
	return adrnbuff[num].attr.bmpnumber;

}




extern int unpack2(FILE *fp, char* p_out, long* bytelength);
extern int unpack3(char* bytedata, char* p_out, long* bytelength, int mode);

///////////////////////////////////////////////////////////////////ここまでデバッグ关数
#define REALGETIMAGEMAXSIZE 1000*1000
BYTE g_realgetimagebuf[REALGETIMAGEMAXSIZE];
BYTE g_realgetimagebuf2[REALGETIMAGEMAXSIZE];

//ナンバーからイメージデータを返す
#if 1
#ifdef PUK2
BOOL realGetImage(int graphicNo, unsigned char **bmpdata, int *width, int *height, int *palsiz)
#else
BOOL realGetImage(int graphicNo, unsigned char **bmpdata, int *width, int *height)
#endif
{
	ADRNBIN adrdata;
	//RD_HEADER head;
//	static BYTE bitmapinfo[sizeof(BITMAPINFO)+sizeof(RGBQUAD)*256];
	if(graphicNo<0 || graphicNo>=MAX_GRAPHICS)return FALSE;

	//graphicNoアドレス取り出し
	adrdata=adrnbuff[graphicNo];

#ifdef MULTI_GRABIN
	int iBinMode;
	// Bin Mode
	iBinMode = adrnbuff[graphicNo].BinMode;
	// オープンできてなかったら失败
	if( Realbinfp[iBinMode] == NULL )return FALSE;

	//これでadrndataにGraphic.binのアドレスが入る
	fseek(Realbinfp[iBinMode], adrdata.adder, SEEK_SET);//Graphic.binファイルポインタ合わせ 

    if( fread( &g_realgetimagebuf, adrdata.size, 1, Realbinfp[iBinMode] ) != 1 ){
		return FALSE;
	}
#else
	//これでadrndataにGraphic.binのアドレスが入る
	fseek(Realbinfp, adrdata.adder, SEEK_SET);//Graphic.binファイルポインタ合わせ 

    if( fread(&g_realgetimagebuf, adrdata.size, 1, Realbinfp ) != 1 )
		return FALSE;
#endif

#ifdef PUK2
	unsigned int len;
	unsigned int xpalsiz;
	*bmpdata = g_realgetimagebuf2;
	if (palsiz){
		if( decoder( g_realgetimagebuf, bmpdata,
				(unsigned int*)width, (unsigned int*)height, &len, (unsigned int*)palsiz ) == NULL ) return FALSE;
	}else{
		if( decoder( g_realgetimagebuf, bmpdata,
				(unsigned int*)width, (unsigned int*)height, &len, &xpalsiz ) == NULL ) return FALSE;
	}
#else
	unsigned int len;
	*bmpdata = g_realgetimagebuf2;
	if( decoder( g_realgetimagebuf, bmpdata,
			(unsigned int*)width, (unsigned int*)height, &len ) == NULL )
	{
		return FALSE;
	}
#endif

	return TRUE;
}
#else
BOOL realGetImage(int graphicNo, unsigned char **bmpdata, int *width, int *height)
{
	ADRNBIN adrdata;
	RECODEHEAD head;
//	static BYTE bitmapinfo[sizeof(BITMAPINFO)+sizeof(RGBQUAD)*256];
	if(graphicNo<0 || graphicNo>=MAX_GRAPHICS)return FALSE;

	//graphicNoアドレス取り出し
	adrdata=adrnbuff[graphicNo];

	//これでadrndataにGraphic.binのアドレスが入る
	fseek(Realbinfp, adrdata.adder, SEEK_SET);//Graphic.binファイルポインタ合わせ 

    if( fread(&head, 8, 1 , Realbinfp ) != 1 ) return FALSE;
	if((head.magicnumber != 'AP')&&(head.magicnumber != 'UC')){
	    //ヘッダーが变
#if 0
#ifdef DEBUGPUSH
		PUSH("Grahic.bin格式错误");                                             //MLHIDE
#endif
#endif
		return FALSE;
	}
#if UNPACK2
	unpack2( Realbinfp, (char *)&g_realgetimagebuf[0], &head.size);

#else



#if 0
    if( fread(&g_realgetimagebuf, head.size, 1 , Realbinfp ) != 1 )return FALSE;
	//ヘッダーが压缩用マジックナンバーだった时（11/12ヘッダ削除&データ压缩版）

	if( head.magicnumber =='AP') 
		unpack( (char *)&g_realgetimagebuf[0], &head.size , 1 );
#else
    if( fread(g_realgetimagebuf2, head.size, 1 , Realbinfp ) != 1 )return FALSE;
	//ヘッダーが压缩用マジックナンバーだった时（11/12ヘッダ削除&データ压缩版）

	if( head.magicnumber =='AP') 
		unpack3( (char *)g_realgetimagebuf2,(char *)g_realgetimagebuf, &head.size , 1 );
#endif



#endif

#if 1
	*width = *( ( int *)g_realgetimagebuf );
	*height = *( ( int *)g_realgetimagebuf + 1 );
	*bmpdata = g_realgetimagebuf + 8;
#else // 压缩なし
	*width = 64;
	*height = head.size / 64;
	*bmpdata = g_realgetimagebuf2;
#endif
	
	return TRUE;
}
#endif

#if 0
BYTE g_linearbuf[REALGETIMAGEMAXSIZE];
//线形メモリにする
BOOL Linear(void)
{
    //保管
    LPBYTE  PReserv;

	int siz = GetImageSize();

	PReserv = g_linearbuf;

    //PBitsの内容を保管
    CopyMemory(PReserv, PBits, GetImageSize() );

    LPBYTE  PSource;
    int     SourcePitch;
    LPBYTE  PDest;

    if(GetHeight() > 0){
        //ウィンドウズ形式
        PSource = PReserv + (GetHeight() - 1) * GetLineSize();
        SourcePitch = -GetLineSize();
    }else{
        //OS2形式
        PSource = PReserv;
        SourcePitch = GetLineSize();
    }


    PDest = PBits = g_realgetimagebuf;

    for(int i = 0 ; i < GetHeight() ; i ++){
        CopyMemory( PDest, PSource, GetWidth() );
        PDest   += GetWidth();
        PSource += SourcePitch;
    }

    return TRUE;
}
#endif



