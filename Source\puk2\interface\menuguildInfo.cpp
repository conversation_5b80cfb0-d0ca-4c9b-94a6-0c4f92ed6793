﻿#define AMOUNT_OF_GUILD_TITLE_BAR 7
#define GUILD_NAME_AREA GUILD_TITLE_MAX
#define GUILD_ROOM_AREA (GUILD_TITLE_MAX + 1)

static int MenuWindowGuildInfoViewLine = 0;
static int MenuWindowGuildInfoCurrentForcus = -1;

static void MenuWindowGuildInfoRedraw ();
static void MenuWindowguildInfoRedrawForMember ();
static void MenuWindowGuildInfoRedrawForMaster ();
static void MenuWindowGuildInfoDrawContents ();
static void MenuWindowGuildInfoCopyTitle ();
static void MenuWindowGuildInfoSetNewName (int no);
static void MenuWindowGuildInfoSyncViewAndTab ();
static void MenuWindowGuildInfoScrollBarTest (int mouse);
static void MenuWindowGuildInfoSendName ();
static void MenuWindowGuildInfoSendRoom ();
static void MenuWindowGuildInfoSendTitle ();

BOOL CheckResetGuildTitleData(int No);
BOOL CheckResetGuildTitleDataAll(void);
BOOL CheckGuildInfoSetDialog(void);

//ダイアログ
INPUT_STR MenuWindowGuildInfoStr;

INIT_STR_STRUCT MenuWindowGuildInfoStruct[]= {
	{&MenuWindowGuildInfoStr, 141-25,  48-17, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 24, 0, 0},
	{&MenuWindowGuildInfoStr, 141-25,  60-11, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 24, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 187-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 206-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 225-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 244-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 263-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 282-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0},
	{&MenuWindowGuildInfoStr,  60-22, 301-22, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_RED, "", 1, 16, 0, 0}
};

//ウインドウ处理
BOOL MenuWindowGuildInfo (int mouse)
{
	int i;

	if (mouse == WIN_INIT) {
		MenuWindowGuildInfoViewLine=0;
		MenuWindowGuildInfoCurrentForcus = -1;
		MenuWindowGuildInfoCopyTitle ();
		MenuWindowGuildInfoRedraw ();

		strcpy(MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name,guildBook.guildName);
		strcpy(MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name,guildBook.guildRoomName);

		for (i = 0; i < GUILD_TITLE_MAX; ++i) {
			strcpy(MenuWindowGuildInfoEditting[i].name,guildBook.title[i].name);
		}

		strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoGuildName].Switch)->text,guildBook.guildName);
		strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoRoomName].Switch)->text,guildBook.guildRoomName);
	}

	if(!CheckGuildInfoSetDialog()){
		MenuWindowGuildInfoCurrentForcus = -1;
	}
	
	return TRUE;
}

//ウインドウ描画
BOOL MenuWindowGuildInfoDraw (int mouse)
{
	MenuWindowGuildInfoScrollBarTest (mouse);
	MenuWindowGuildInfoRedraw ();
	displayMenuWindow ();
	return TRUE;
}

BOOL MenuWindowGuildInfoCheckBox (int no, unsigned int flag)
{
	int flagList[] = {GUILD_FLAG_INVITE, GUILD_FLAG_DISMISS, GUILD_FLAG_FEED, 
		GUILD_FLAG_ITEMBOX, GUILD_FLAG_BBS};
	char* checkBoxHelp [] = {
		"变更这个称号的加入权限。",
		"变更这个称号的除名权限。",
		"变更这个称号的家族宠物喂养权限。",
		"变更这个称号的物品箱使用权限。",
		"变更这个称号的留言板使用权限。"
	};

	BOOL ReturnFlag=FALSE;
	int line = (no - EnumMenuWindowGuildInfoCheckBox0) / 5;
	int commition = (no - EnumMenuWindowGuildInfoCheckBox0) % 5;

	//家族マスターの项目は变更できません
	if (line == 0 && MenuWindowGuildInfoViewLine==0)
		return FALSE;

	if(flag & MENU_MOUSE_OVER){
		strcpy( OneLineInfoStr, checkBoxHelp[commition]);
	}

	if( flag & MENU_MOUSE_LEFT ) {
		if (MenuWindowGuildInfoEditting[MenuWindowGuildInfoViewLine + line].name[0] == '\0')
			return FALSE;

		MenuWindowGuildInfoEditting[MenuWindowGuildInfoViewLine + line].flag ^= flagList[commition];

		MenuWindowGuildInfoRedrawForMaster ();

		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

BOOL MenuWindowGuildInfoSetInfo (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_guildInfoSetButtonOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo=GID_guildInfoSetButtonOver;
		switch (no) {
			case EnumMenuWindowGuildInfoGuildNameSet :
				strcpy( OneLineInfoStr, "变更家族名称。" );
				break;
			case EnumMenuWindowGuildInfoGuildRoomSet :
				strcpy( OneLineInfoStr, "变更家族房间名称。" );
				break;
			case EnumMenuWindowGuildInfoRenameButton :
				strcpy( OneLineInfoStr, "变更设定的称号。" );
				break;
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_guildInfoSetButtonOff;
		switch (no) {
			case EnumMenuWindowGuildInfoGuildNameSet :
				MenuWindowGuildInfoSendName ();
				break;
			case EnumMenuWindowGuildInfoGuildRoomSet :
				MenuWindowGuildInfoSendRoom ();
				break;
			case EnumMenuWindowGuildInfoRenameButton :
				MenuWindowGuildInfoSendTitle ();
				break;
		}

		ReturnFlag=TRUE;
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_guildInfoSetButtonOff;
	}

	return ReturnFlag;
}

BOOL MenuWindowGuildInfoInputLine (int no, unsigned int flag)
{

	BOOL ReturnFlag = FALSE;
	DIALOG_SWITCH *Dialog;

	//マスター以外は蹴る
	if (!MenuAddressBookIsMeGuildMaster ())
		return FALSE;

	//ダイアログの座标设定
	if(DiarogST.SwAdd == wI->sw[no].Switch){
		SetInputStr(&MenuWindowGuildInfoStruct[no], wI->wx, wI->wy, 2);
		return FALSE;
	}

	//绘图设定
	if(DiarogST.SwAdd == wI->sw[no].Switch){
		wI->sw[EnumMenuWindowGuildInfoGuildName + no].Enabled = FALSE;
		strcpy (((TEXT_SWITCH*)(wI->sw[no + EnumMenuWindowGuildInfoGuildName].Switch))->text,MenuWindowGuildInfoStruct[no].str);
	}else{
		wI->sw[EnumMenuWindowGuildInfoGuildName + no].Enabled = TRUE;
	}

	if (flag & MENU_MOUSE_LEFT) {
		switch(no){
			//家族名
			case EnumMenuWindowGuildInfoDlg0:
				strcpy (MenuWindowGuildInfoStruct[no].str, MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name);
				SetInputStr(&MenuWindowGuildInfoStruct[no], wI->wx, wI->wy, 0);
				GetKeyInputFocus (&MenuWindowGuildInfoStr);
				DiarogST.SwAdd = wI->sw[no].Switch;
				Dialog = (DIALOG_SWITCH*)wI->sw[no].Switch;
				Dialog->InpuStrAdd = &MenuWindowGuildInfoStr;

				MenuWindowGuildInfoCurrentForcus = GUILD_TITLE_MAX + (no - EnumMenuWindowGuildInfoDlg0);
			break;

			//家族ルーム名
			case EnumMenuWindowGuildInfoDlg1:
				strcpy (MenuWindowGuildInfoStruct[no].str, MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name);
				SetInputStr(&MenuWindowGuildInfoStruct[no], wI->wx, wI->wy, 0);
				GetKeyInputFocus (&MenuWindowGuildInfoStr);
				DiarogST.SwAdd = wI->sw[no].Switch;
				Dialog = (DIALOG_SWITCH*)wI->sw[no].Switch;
				Dialog->InpuStrAdd = &MenuWindowGuildInfoStr;

				MenuWindowGuildInfoCurrentForcus = GUILD_TITLE_MAX + (no - EnumMenuWindowGuildInfoDlg0);
			break;

			//家族称号名
			case EnumMenuWindowGuildInfoDlg2:
			case EnumMenuWindowGuildInfoDlg3:
			case EnumMenuWindowGuildInfoDlg4:
			case EnumMenuWindowGuildInfoDlg5:
			case EnumMenuWindowGuildInfoDlg6:
			case EnumMenuWindowGuildInfoDlg7:
			case EnumMenuWindowGuildInfoDlg8:
				strcpy (MenuWindowGuildInfoStruct[no].str, 
					((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoTitle0+no-EnumMenuWindowGuildInfoDlg2].Switch))->text);
				SetInputStr(&MenuWindowGuildInfoStruct[no], wI->wx, wI->wy, 0);
				GetKeyInputFocus (&MenuWindowGuildInfoStr);
				DiarogST.SwAdd = wI->sw[no].Switch;
				Dialog = (DIALOG_SWITCH*)wI->sw[no].Switch;
				Dialog->InpuStrAdd = &MenuWindowGuildInfoStr;

				MenuWindowGuildInfoCurrentForcus = MenuWindowGuildInfoViewLine + no - EnumMenuWindowGuildInfoDlg2;
			break;
		}

		ReturnFlag = TRUE;
	}

	return ReturnFlag;
}

BOOL MenuWindowGuildInfoScrollOneLine (int no, unsigned int flag)
{

	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo = (no == EnumMenuWindowGuildInfoScrollUpArrow) 
		? GID_guildInfoScrollUpArrowOn : GID_guildInfoScrollDownArrowOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo = (no == EnumMenuWindowGuildInfoScrollUpArrow) 
			? GID_guildInfoScrollUpArrowOver : GID_guildInfoScrollDownArrowOver;
	}
	if(flag & MENU_MOUSE_LEFTHOLD){
		if(no == EnumMenuWindowGuildInfoScrollUpArrow){
			Graph->graNo =GID_UpButtonOff;
		}else{
			Graph->graNo =GID_DownButtonOff;
		}
	}
	if( flag & MENU_MOUSE_LEFTAUTO ){
			MenuWindowGuildInfoViewLine += (no == EnumMenuWindowGuildInfoScrollUpArrow) 
				? -1 : 1;

		MenuWindowGuildInfoSyncViewAndTab ();

		//ダイアログをチャットに变更
		SetDialogMenuChat();

		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

BOOL MenuWindowGuildInfoScrollV (int no, unsigned int flag){

	BOOL ReturnFlag=FALSE;

	ReturnFlag=MenuSwitchScrollBarV(no,flag);

	if(ReturnFlag==TRUE){
		//ダイアログをチャットに变更
		SetDialogMenuChat();
	}

	return ReturnFlag;

}

BOOL MenuWindowGuildInfoScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) && mouse.wheel!=0){
		// スクロールバー縦ホイール移动
		MenuWindowGuildInfoViewLine = WheelToMove( &wI->sw[EnumMenuWindowGuildInfoScrollBar],
			 MenuWindowGuildInfoViewLine, GUILD_TITLE_MAX - AMOUNT_OF_GUILD_TITLE_BAR,
			 mouse.wheel );

		//ダイアログをチャットに变更
		SetDialogMenuChat();
	}

	return ReturnFlag;
}


BOOL MenuWindowGuildInfoClose (int no, unsigned int flag)
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph;

	Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	Graph->graNo=GID_WindowCloseOn;
	if(flag & MENU_MOUSE_OVER){
		Graph->graNo=GID_WindowCloseOver;
		strcpy( OneLineInfoStr, "关闭这个窗口。" );
	}

	if( flag & MENU_MOUSE_LEFT ){
		Graph->graNo=GID_WindowCloseOff;

		WindowFlag[MENU_WINDOW_GUILDINFO].wininfo->flag |= WIN_INFO_DEL;

		ReturnFlag=TRUE;
	}
	return ReturnFlag;
}

//描画
static void MenuWindowGuildInfoRedraw ()
{
	if (MenuAddressBookIsMeGuildMaster ())
		MenuWindowGuildInfoRedrawForMaster ();
	else
		MenuWindowguildInfoRedrawForMember ();
}

//メンバー时描画
static void MenuWindowguildInfoRedrawForMember ()
{
	int i;

	wI->sw[EnumMenuWindowGuildInfoGuildNameSet].Enabled = FALSE;
	wI->sw[EnumMenuWindowGuildInfoGuildRoomSet].Enabled = FALSE;
	wI->sw[EnumMenuWindowGuildInfoRenameButton].Enabled = FALSE;

	for (i = EnumMenuWindowGuildInfoGuildName; i < EnumMenuWindowGuildInfoCheckBox0; ++i) {
		wI->sw[i].Enabled = TRUE;
		wI->sw[i].func = MenuSwitchNone;
	}
	for (i = EnumMenuWindowGuildInfoCheckBox0; i < EnumMenuWindowGuildInfoMasterMark; ++i) {
		wI->sw[i].sx = wI->sw[i].sy = 0;
		wI->sw[i].func = MenuSwitchNone;
	}


	//セットボタン非表示
	wI->sw[EnumMenuWindowGuildInfoRenameButton].Enabled=FALSE;

	MenuWindowGuildInfoDrawContents ();
}

//マスター时描画
static void MenuWindowGuildInfoRedrawForMaster ()
{
	int i;
	TEXT_SWITCH	*text;

	wI->sw[EnumMenuWindowGuildInfoGuildNameSet].Enabled = TRUE;
	wI->sw[EnumMenuWindowGuildInfoGuildRoomSet].Enabled = TRUE;
	wI->sw[EnumMenuWindowGuildInfoRenameButton].Enabled = TRUE;

	for (i = EnumMenuWindowGuildInfoGuildName; i < EnumMenuWindowGuildInfoCheckBox0; ++i) 
		wI->sw[i].Enabled = TRUE;

	for (i = EnumMenuWindowGuildInfoCheckBox0; i < EnumMenuWindowGuildInfoMasterMark; ++i) {
		wI->sw[i].sx = wI->sw[i].sy = 15;
		wI->sw[i].func = MenuWindowGuildInfoCheckBox;
	}

	MenuWindowGuildInfoDrawContents ();

	//家族名
	if(DiarogST.SwAdd == wI->sw[EnumMenuWindowGuildInfoDlg0].Switch){
		strcpy(MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name,MenuWindowGuildInfoStruct[0].inputStr->buffer);

		wI->sw[EnumMenuWindowGuildInfoGuildName].Enabled=FALSE;
		//ダイアログ位置
		SetInputStr(&MenuWindowGuildInfoStruct[0],wI->wx,wI->wy,1);
	}else{
		wI->sw[EnumMenuWindowGuildInfoGuildName].Enabled=TRUE;
		strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoGuildName].Switch)->text,MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name);

		if(strcmp(MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name,guildBook.guildName)==0){
			((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoGuildName].Switch)->color=FONT_PAL_WHITE;
		}else{
			((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoGuildName].Switch)->color=FONT_PAL_RED;
		}
	}

	if(strcmp(MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name,guildBook.guildName)==0){
		wI->sw[EnumMenuWindowGuildInfoGuildNameSet].Enabled=FALSE;
	}else{
		wI->sw[EnumMenuWindowGuildInfoGuildNameSet].Enabled=TRUE;
	}


	//家族ルーム名称
	if(DiarogST.SwAdd == wI->sw[EnumMenuWindowGuildInfoDlg1].Switch){
		wI->sw[EnumMenuWindowGuildInfoRoomName].Enabled=FALSE;
		strcpy(MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name,MenuWindowGuildInfoStruct[1].inputStr->buffer);
		//ダイアログ位置
		SetInputStr(&MenuWindowGuildInfoStruct[1],wI->wx,wI->wy,1);
	}else{
		wI->sw[EnumMenuWindowGuildInfoRoomName].Enabled=TRUE;
		strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoRoomName].Switch)->text,MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name);

		if(strcmp(MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name,guildBook.guildRoomName)==0){
			((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoRoomName].Switch)->color=FONT_PAL_WHITE;
		}else{
			((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoRoomName].Switch)->color=FONT_PAL_RED;
		}
	}

	if(strcmp(MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name,guildBook.guildRoomName)==0){
		wI->sw[EnumMenuWindowGuildInfoGuildRoomSet].Enabled=FALSE;
	}else{
		wI->sw[EnumMenuWindowGuildInfoGuildRoomSet].Enabled=TRUE;
	}


	//家族称号
	for (i = 0; i < AMOUNT_OF_GUILD_TITLE_BAR; ++i){
		if(DiarogST.SwAdd == wI->sw[EnumMenuWindowGuildInfoDlg2+i].Switch){
			wI->sw[EnumMenuWindowGuildInfoTitle0 + i].Enabled = FALSE;
			MenuWindowGuildInfoSetNewName (2+i);
			strcpy(((TEXT_SWITCH *)wI->sw[EnumMenuWindowGuildInfoTitle0+i].Switch)->text,MenuWindowGuildInfoStruct[i].inputStr->buffer);
		}

		//情报が变更されていれば变更候补としてフォントカラーを变える
		text = (TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoTitle0 + i].Switch);
		if(CheckResetGuildTitleData(MenuWindowGuildInfoViewLine + i)){
			//变更されてる
			text->color=FONT_PAL_RED;
		}else{
			//すえおき
			text->color=FONT_PAL_WHITE;
		}
	}



	//变更されている个所があったときのみセットボタン表示
	if(CheckResetGuildTitleDataAll()){
		wI->sw[EnumMenuWindowGuildInfoRenameButton].Enabled=TRUE;
	}else{
		wI->sw[EnumMenuWindowGuildInfoRenameButton].Enabled=FALSE;
	}

}

//共通部分描画
static void MenuWindowGuildInfoDrawContents ()
{
	GUILD_TITLE_INFO* title;
	int i;
	BOOL master = MenuAddressBookIsMeGuildMaster ();

	wI->sw[EnumMenuWindowGuildInfoMasterMark].Enabled = FALSE;

	//家族名、ルーム名
	if (master) {
		strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoGuildName].Switch))->text,
			MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name);
		strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoRoomName].Switch))->text,
			MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name);
	}
	else {
		strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoGuildName].Switch))->text,
			guildBook.guildName);
		strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoRoomName].Switch))->text,
			guildBook.guildRoomName);
	}

	//家族モンスター
	strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoMonsterName0].Switch))->text,
		guildBook.monsterName[0]);
	strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoMonsterName1].Switch))->text,
		guildBook.monsterName[1]);
	strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoMonsterName2].Switch))->text,
		guildBook.monsterName[2]);

	//家族称号の权利
	for (i = 0; i < AMOUNT_OF_GUILD_TITLE_BAR; ++i) {
		title = (master) ? &MenuWindowGuildInfoEditting[MenuWindowGuildInfoViewLine + i]
			: &guildBook.title[MenuWindowGuildInfoViewLine + i];
		
		strcpy (((TEXT_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoTitle0 + i].Switch))->text, title->name);

		((GRAPHIC_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoCheckBox0 + i * 5].Switch))->graNo
			= (title->flag & GUILD_FLAG_INVITE)	
			? GID_guildInfoCheckBoxOn : GID_guildInfoCheckBoxOff;
		((GRAPHIC_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoCheckBox1 + i * 5].Switch))->graNo
			= (title->flag & GUILD_FLAG_DISMISS)	
			? GID_guildInfoCheckBoxOn : GID_guildInfoCheckBoxOff;
		((GRAPHIC_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoCheckBox2 + i * 5].Switch))->graNo
			= (title->flag & GUILD_FLAG_FEED)	
			? GID_guildInfoCheckBoxOn : GID_guildInfoCheckBoxOff;
		((GRAPHIC_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoCheckBox3 + i * 5].Switch))->graNo
			= (title->flag & GUILD_FLAG_ITEMBOX)	
			? GID_guildInfoCheckBoxOn : GID_guildInfoCheckBoxOff;
		((GRAPHIC_SWITCH*)(wI->sw[EnumMenuWindowGuildInfoCheckBox4 + i * 5].Switch))->graNo
			= (title->flag & GUILD_FLAG_BBS)	
			? GID_guildInfoCheckBoxOn : GID_guildInfoCheckBoxOff;

		if (title->flag & GUILD_FLAG_MASTER) {
			wI->sw[EnumMenuWindowGuildInfoMasterMark].Enabled = TRUE;
			wI->sw[EnumMenuWindowGuildInfoMasterMark].ofx = 45 -22;
			wI->sw[EnumMenuWindowGuildInfoMasterMark].ofy = 190 + i * 20 -26;
		}
	}

}

static void MenuWindowGuildInfoCopyTitle ()
{
	int i;
	
	if (!MenuAddressBookIsMeGuildMaster ())
		return;

	for (i = 0; i < GUILD_TITLE_MAX; ++i) {
		MenuWindowGuildInfoEditting[i].flag = guildBook.title[i].flag;
		strcpy (MenuWindowGuildInfoEditting[i].name, guildBook.title[i].name);
	}
	strcpy (MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name, guildBook.guildName);
	strcpy (MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name, guildBook.guildRoomName);
}

static void MenuWindowGuildInfoSetNewName (int no)
{
	if (MenuWindowGuildInfoCurrentForcus == -1)
		return;

	strcpy (MenuWindowGuildInfoEditting[MenuWindowGuildInfoCurrentForcus].name, 
		MenuWindowGuildInfoStruct[no].inputStr->buffer);

}

static void MenuWindowGuildInfoSyncViewAndTab ()
{
	int tab, bottom;

	bottom = GUILD_TITLE_MAX - AMOUNT_OF_GUILD_TITLE_BAR;

	if (MenuWindowGuildInfoViewLine < 0){
		MenuWindowGuildInfoViewLine = 0;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}else if (MenuWindowGuildInfoViewLine > bottom){
		MenuWindowGuildInfoViewLine = bottom;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}else{
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
	}
	tab = MenuWindowGuildInfoViewLine;

	NumToScrollVMove (&wI->sw[EnumMenuWindowGuildInfoScrollBar], bottom, tab);
}


static void MenuWindowGuildInfoScrollBarTest (int mouse)
{
	if (((BUTTON_SWITCH*)wI->sw[EnumMenuWindowGuildInfoScrollBar].Switch)->status & 1) {
			MenuWindowGuildInfoViewLine = ScrollVPointToNum (&wI->sw[EnumMenuWindowGuildInfoScrollBar], GUILD_TITLE_MAX - AMOUNT_OF_GUILD_TITLE_BAR);
	}

}

//家族ネーム变更要请
static void MenuWindowGuildInfoSendName ()
{
	char src[256], dest[256];
	if (strcmp (MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name, 
		guildBook.guildName) == 0)
			return;

	if(!CheckSendStr(MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name))
		return;

	// '|','\'が入っていたら駄目
	if ( VirticalCheck( (unsigned char *)MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name ) == 1 )
		return ;

	strcpy (src, MenuWindowGuildInfoEditting[GUILD_NAME_AREA].name);
	makeSendString (src, dest, sizeof (dest));
	nrproto_GI_send (sockfd, 2, dest);
	play_se (SE_NO_OK3, 320, 240);
	//ダイアログをチャットに变更
	SetDialogMenuChat();

}

//家族ルームネーム变更要请
static void MenuWindowGuildInfoSendRoom ()
{
	char src[256], dest[256];
	if (strcmp (MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name, 
		guildBook.guildRoomName) == 0)
			return;

	if(!CheckSendStr(MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name))
		return;

	// '|','\'が入っていたら駄目
	if ( VirticalCheck( (unsigned char *)MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name ) == 1 )
		return ;


	strcpy (src, MenuWindowGuildInfoEditting[GUILD_ROOM_AREA].name);
	makeSendString (src, dest, sizeof (dest));
	nrproto_GI_send (sockfd, 3, dest);
	play_se (SE_NO_OK3, 320, 240);
	//ダイアログをチャットに变更
	SetDialogMenuChat();
}

//家族称号ネーム变更要请
static void MenuWindowGuildInfoSendTitle ()
{
	char src[256], dest[256];
	int i;
	GUILD_TITLE_INFO* gi, *ge;

	for (i = 0; i < GUILD_TITLE_MAX; ++i) {
		if(CheckResetGuildTitleData(i)){
			if(!CheckSendStr(MenuWindowGuildInfoEditting[i].name))
				return;

			// '|','\'が入っていたら駄目
			if ( VirticalCheck( (unsigned char *)MenuWindowGuildInfoEditting[i].name ) == 1 )
				return ;
		}
	}

	for (i = 0; i < GUILD_TITLE_MAX; ++i) {
		gi = &guildBook.title[i];
		ge = &MenuWindowGuildInfoEditting[i];
		if ((strcmp (gi->name, ge->name) == 0) && (gi->flag == ge->flag))
			continue;

		strcpy (src, ge->name);
		makeSendString (src, dest, sizeof (dest));
		nrproto_GT_send (sockfd, i, ge->flag, dest);
		play_se (SE_NO_OK3, 320, 240);
	}
	//ダイアログをチャットに变更
	SetDialogMenuChat();
}

//タイトルの情报で更新があったか
BOOL CheckResetGuildTitleData(int No){

	int ReturnCount;

	ReturnCount=0;

	//タイトルが变更されたか？
	if(strcmp(MenuWindowGuildInfoEditting[No].name,guildBook.title[No].name)!=0){
		ReturnCount++;
	}

	//タイトル权限が变更されたか？
	//マスターは权限变更されるはず无いんスよ～
	if(!(guildBook.title[No].flag & GUILD_FLAG_MASTER)){
		if(MenuWindowGuildInfoEditting[No].flag!=guildBook.title[No].flag){
			ReturnCount++;
		}
	}
	if(ReturnCount>0){
		return TRUE;
	}else{
		return FALSE;
	}

};

//すべての情报でどれか更新があったか
BOOL CheckResetGuildTitleDataAll(void){

	int ReturnCount;
	int i;

	ReturnCount=0;

	for (i = 0; i < GUILD_TITLE_MAX; ++i) {
		if(CheckResetGuildTitleData(i)){
			ReturnCount++;
		}
	}

	if(ReturnCount>0){
		return TRUE;
	}else{
		return FALSE;
	}

}

//このウインドウ中でダイアログを使用しているか
BOOL CheckGuildInfoSetDialog(void){

	int i;

	for(i=0;i<9;i++){
		if(DiarogST.SwAdd == wI->sw[EnumMenuWindowGuildInfoDlg0+i].Switch){
			return TRUE;	
		}	
	}

	return FALSE;	
}

//家族インフォウインドウを关闭
void CloseGuildInfoWindow(void){

	if(WindowFlag[MENU_WINDOW_GUILDINFO].wininfo!=NULL){
		WindowFlag[MENU_WINDOW_GUILDINFO].wininfo->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

}