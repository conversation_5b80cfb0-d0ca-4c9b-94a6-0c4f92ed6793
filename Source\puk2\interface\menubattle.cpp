﻿//バトルメニュー

//====================================//
//				コクピット			  //
//====================================//

// ウィンドウ处理 *********************//

void ClearBoxFlag( void );
BOOL BattleSetTargetBox( int id, int flag );
void BattleInfoWndDisp( void );
// 当たり判定初期化 ++++
void InitHitNo( int *array, int max );
// ターゲットとの当たり判定 ++++
int CheckBattelTarget( void );

// スキルウィンドウ处理 ++++
void BattleSkillWnd();
// アイテムウィンドウ处理 ++++
void BattleItemWnd();
// ペットウィンドウ处理 ++++
void BattlePetWnd();
// 武器变更ウィンドウ处理 ++++
void BattleWeaponWnd();
// ペットスキルウィンドウ处理 ++++
void BattlePetSkillWnd( void );

// 战闘にでいてるペットの番号を调べる ++++
int CheckBattlePet( void );

// カウントダウン处理 ++++
void BattleCntDownDisp( void );

// 变数宣言 ***************************//

// コマンド受付时间
#define BATTLE_CNT_DOWN_TIME  30000	// ３０秒

// 自分のＩＤ
extern int BattleMyNo;
// 自分のＩＤバックアップ
extern int BattleMyNoBak;
#ifdef PUK3_RIDE_BATTLE
	// ライドペットのインデックス
	extern int BattleRidePetNo;
#endif

// ＢＰフラグ
extern int BattleBpFlag;
// 战闘时の自分のＭＰ
extern int BattleMyMp;
// 使用できるスキルフラグ
extern int BattleUsableSkillFlag;
// ペットの使用できるスキルフラグ
extern int BattlePetUsableSkillFlag;
// リバースの等级（０ならリバース使用不可）
extern int BattleRebirthLevel;

// バトルキャラクターフィールドフラグ
extern int BattleBcFieldFlag;

// 战闘强制終了フラグ
extern int BattleEscFlag;

// プレイヤー逃げたフラグ
extern int battlePlayerEscFlag;

// コマンド入力济みフラグ
extern int BattleAnimFlag;
// ターン受信フラグ
extern BOOL BattleTurnReceiveFlag;
// 现在のクライアントターン番号
extern int BattleCliTurnNo;
// 现在のサーバーターン番号
extern int BattleSvTurnNo;

// カウントダウン
extern DWORD BattleCntDown;
extern int BattleCntDownBak;
// カウントダウンフラグチェック
extern BOOL BattleCntDownFlag;

// ターゲットフラグ记忆
extern int	BattleTargetFlag;

extern bool rebirthflg;

#define B_MENU_BUTTON_MAX 9

// 战闘ボタンの当たり判定番号记忆用
extern int BattleMenuButtonHitDispNo[ B_MENU_BUTTON_MAX ];
// 战闘ボタンのフラグ记忆用
extern int BattleMenuButtonFlag[ B_MENU_BUTTON_MAX ];
// 战闘ボタンフラグバックアップ
extern int BattleMenuButtonFlagBak;
extern int BattleMenuButtonFlagBak1;
extern int BattleMenuButtonFlagBak2;
// ２アクション时の使用できないボタンフラグ
extern int BattleUnusableButtonFlag;

// コマンドインプットフラグ
extern int BattleMenuInputFlag;

// 战闘スキルウィンドウプロセス番号
extern int BattleSkillWndProcNo;
// 战闘アイテムウィンドウプロセス番号
extern int BattleItemWndProcNo;

// 战闘スキル选择番号
extern int BattleSelectSkillNo;
// 战闘アビリティ选择番号
extern int BattleSelectAbilityNo;

extern int BattleSelectItemNo;

// バトルメニュー座标
extern int battleMenuX;
extern int battleMenuY;
extern int battleMenuA;

// 战闘ペットスキルウィンドウのボタンフラグ记忆用
extern int BattleMenuPetButtonFlag;
// 战闘ペットベースウィンドウの当たり判定番号
extern int BattleMenuPetBaseHitFontNo;

char SkillTargetSelect;
char ItemTargetSelect;


static ACTION *BattleSkillOpen;
static ACTION *BattleItemOpen;
static ACTION *BattleMonsterOpen;

static char TargetSelOK;
#ifdef PUK3_RIDE_BATTLE
	struct BATTLEPETDATA{
		int lp, maxLp;
		int fp, maxFp;
	};
	static struct BATTLEPETDATA battlePet[5];
	void InitBattlePetdata()
	{
		int i;

		// 観战中なら終わる
		if ( BattleMyNo >= 20 ) return;

		for(i=0;i<MAX_PET;i++){
			battlePet[i].lp = pet[i].lp;
			battlePet[i].maxLp = pet[i].maxLp;
			battlePet[i].fp = pet[i].fp;
			battlePet[i].maxFp = pet[i].maxFp;
		}
	}
	void SetBattlePetdata()
	{
		int petId;
		BC_YOBI *pYobi;

		// 観战中なら終わる
		if ( BattleMyNo >= 20 ) return;
		// ペットが出てないなら
		if ( BattleNowPetNo < 0 ) return;

		petId = BattleCheckPetId( BattleMyNo );

#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
		if ( pActBc[BattleMyNo] == NULL ){
	#ifdef _DEBUG
			MessageBox( hWnd, "pActBc[BattleMyNo] == NULL", "SetBattlePetdata", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
			return;
		}
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[BattleMyNo] );
#endif
		pYobi = (BC_YOBI *)pActBc[BattleMyNo]->pYobi;
		if ( pYobi == NULL ){
	#ifdef _DEBUG
			MessageBox( hWnd, "pActBc[BattleMyNo]->pYobi == NULL", "SetBattlePetdata", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
			return;
		}

		// 亲がライド中ならペットはいないので
		if ( pYobi->pActRideChar ) return;

#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( petId );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[petId] );
#endif
		battlePet[BattleNowPetNo].lp = pActBc[petId]->hp;
		battlePet[BattleNowPetNo].maxLp = pActBc[petId]->maxHp;
		battlePet[BattleNowPetNo].fp = pActBc[petId]->fp;
		battlePet[BattleNowPetNo].maxFp = pActBc[petId]->maxFp;
	}
#endif
// ウィンドウ处理 *********************//

/***************************************

バトルウィンドウに登场??退散アクションを取らせる场合、

WINDOW_INFO *AppearBattleWindow( short x, short y );
void DisappBattleWindow();

を使用。

普通のウィンドウのように、生成??破弃を行いたい场合、

WINDOW_INFO *createMenuWindow( int type );
void closeBattleWindow()

を使用。

***************************************/

// バトルウィンドウ登场(ウィンドウがすでにある场合は、指定の场所に改めて登场) ++++
WINDOW_INFO *AppearBattleWindow( int WinType, short x, short y )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	GRAPHIC_SWITCH *GraphSw;
	int i;

	wm->WinType = WinType;

	// ウィンドウが存在していなければ生成、死ぬ直前なら复活
	if (wm->wininfo) wm->wininfo->flag&=~WIN_INFO_DEL;
	else wm->wininfo = createMenuWindow( MENU_WINDOW_BATTLE );

	// ウィンドウボタン配置
	GraphSw = (GRAPHIC_SWITCH *)wm->wininfo->sw[EnumGraphBattleWindowFrame].Switch;
	switch(wm->WinType){
	case BWMPROC_Player_:	// 通常プレーヤーウィンドウ
		GraphSw->graNo = GID_BattlePlayerBase;
		for(i=EnumBtBattleAtack;i<=EnumBtBattleReBirth;i++) wm->wininfo->sw[i].Enabled = TRUE;
		wm->wininfo->sw[EnumBtBattlePetSkill].Enabled = FALSE;
#ifdef PUK3_RIDE_BATTLE
		wm->wininfo->sw[EnumBtBattleRideEscape].Enabled = FALSE;
#endif
		wm->wininfo->sw[EnumTxtBattlePetName].Enabled = FALSE;
		wm->wininfo->sw[EnumTxtBattlePetFp].Enabled = FALSE;
		wm->wininfo->sw[EnumTxtBattlePetSkill].Enabled = FALSE;
		break;
	case BWMPROC_Player1:	// プレーヤー行动１回目
		GraphSw->graNo = GID_BattlePlayer1Base;
		for(i=EnumBtBattleAtack;i<=EnumBtBattleReBirth;i++) wm->wininfo->sw[i].Enabled = TRUE;
		wm->wininfo->sw[EnumBtBattlePetSkill].Enabled = FALSE;
#ifdef PUK3_RIDE_BATTLE
		wm->wininfo->sw[EnumBtBattleRideEscape].Enabled = FALSE;
#endif
		wm->wininfo->sw[EnumTxtBattlePetName].Enabled = FALSE;
		wm->wininfo->sw[EnumTxtBattlePetFp].Enabled = FALSE;
		wm->wininfo->sw[EnumTxtBattlePetSkill].Enabled = FALSE;
		break;
	case BWMPROC_Player2:	// プレーヤー行动２回目
		GraphSw->graNo = GID_BattlePlayer2Base;
		for(i=EnumBtBattleAtack;i<=EnumBtBattleReBirth;i++) wm->wininfo->sw[i].Enabled = TRUE;
		wm->wininfo->sw[EnumBtBattlePetSkill].Enabled = FALSE;
#ifdef PUK3_RIDE_BATTLE
		wm->wininfo->sw[EnumBtBattleRideEscape].Enabled = FALSE;
#endif
		wm->wininfo->sw[EnumTxtBattlePetName].Enabled = FALSE;
		wm->wininfo->sw[EnumTxtBattlePetFp].Enabled = FALSE;
		wm->wininfo->sw[EnumTxtBattlePetSkill].Enabled = FALSE;
		break;
	case BWMPROC_Monster:	// モンスターウィンドウ
		GraphSw->graNo = GID_BattleMonstarBase;
		for(i=EnumBtBattleAtack;i<=EnumBtBattleReBirth;i++) wm->wininfo->sw[i].Enabled = FALSE;
		wm->wininfo->sw[EnumBtBattlePetSkill].Enabled = TRUE;
#ifdef PUK3_RIDE_BATTLE
		wm->wininfo->sw[EnumBtBattlePetSkill].ofx = 216;
		wm->wininfo->sw[EnumBtBattleRideEscape].Enabled = FALSE;
#endif
		wm->wininfo->sw[EnumTxtBattlePetName].Enabled = TRUE;
		wm->wininfo->sw[EnumTxtBattlePetFp].Enabled = TRUE;
		wm->wininfo->sw[EnumTxtBattlePetSkill].Enabled = TRUE;

		i = BattleCheckPetId( BattleMyNo );
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( i );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[i] );
#endif
		if (pActBc[i]->freeName[0]!='\0'){
			strcpy( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetName].Switch )->text, pActBc[i]->freeName );
		}else{
			strcpy( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetName].Switch )->text, pActBc[i]->name );
		}
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
		sprintf( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetFp].Switch )->text, "%4d/%4d", //MLHIDE
			pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp );
		break;
#ifdef PUK3_RIDE_BATTLE
	case BWMPROC_PetRide:	// ライドウィンドウ
		GraphSw->graNo = GID_BattleMonstarBase;
		for(i=EnumBtBattleAtack;i<=EnumBtBattleReBirth;i++) wm->wininfo->sw[i].Enabled = FALSE;
		wm->wininfo->sw[EnumBtBattlePetSkill].Enabled = TRUE;
		wm->wininfo->sw[EnumBtBattlePetSkill].ofx = 146;
		wm->wininfo->sw[EnumBtBattleRideEscape].Enabled = TRUE;
		wm->wininfo->sw[EnumTxtBattlePetName].Enabled = TRUE;
		wm->wininfo->sw[EnumTxtBattlePetFp].Enabled = TRUE;
		wm->wininfo->sw[EnumTxtBattlePetSkill].Enabled = TRUE;

#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[BattleMyNo] );
#endif
		strcpy( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetName].Switch )->text, pActBc[BattleMyNo]->name );
		sprintf( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetFp].Switch )->text, "%4d/%4d", //MLHIDE
			pActBc[BattleMyNo]->fp, pActBc[BattleMyNo]->maxFp );
		break;
#endif
	}

	// 目标座标设定
	wm->wx=x,	wm->wy=y;

	// ズレ关系设定
	wm->difY = -153;
	wm->accY = 17;

	// ウィンドウの座标初期化
	wm->wininfo->wx = wm->wx;
	wm->wininfo->wy = wm->wy + wm->difY;

	// 处理の设定
	wm->procNo = BWMPROC_APPEAR;

	return wm->wininfo;
}

// バトルウィンドウ破弃 ++++
void closeBattleWindow()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	if (!wm->wininfo) return;
	wm->wininfo->flag|=WIN_INFO_DEL;
	wm->procNo = BWMPROC_CLOSE;
}

// バトルウィンドウ退场 ++++
void DisappBattleWindow()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	if (!wm->wininfo) return;

	// ズレ关系设定
	if (wm->difY>0) wm->difY = 0;
	if (wm->accY<0) wm->accY = 0;

	// 处理の设定
	wm->procNo = BWMPROC_DISAPP;

	// スキルウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
	// アイテムウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_BTLITEM].wininfo) WindowFlag[MENU_WINDOW_BTLITEM].wininfo->flag |= WIN_INFO_DEL;
	// 使い魔ウィンドウ破弃
	if (WindowFlag[MENU_WINDOW_BTLPET].wininfo) WindowFlag[MENU_WINDOW_BTLPET].wininfo->flag |= WIN_INFO_DEL;

	if (BattleSkillOpen){ DeathAction(BattleSkillOpen);		BattleSkillOpen = NULL; }
	if (BattleItemOpen){ DeathAction(BattleItemOpen);		BattleItemOpen = NULL; }
	if (BattleMonsterOpen){ DeathAction(BattleMonsterOpen);	BattleMonsterOpen = NULL; }
}


// ウィンドウ处理 ++++
BOOL MenuWindowBattle( int mouse )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	
	// 初期化时の处理
	if ( mouse == WIN_INIT ){
		// ズレ关系初期化
		wm->difY = 0;
		wm->accY = 0;

		// 处理の设定を初期化
		wm->procNo = BWMPROC_NOMAL;

		SkillTargetSelect = 0;
		ItemTargetSelect = 0;

		return TRUE;
	}

	// 通常处理

	switch(wm->procNo){
	case BWMPROC_APPEAR:
		// ズレを计算
		wm->difY += wm->accY;
		wm->accY--;

		// ウィンドウを移动
		wI->wy = wm->wy+wm->difY;
	
		// 处理の设定を变更
		if( wm->accY <= 0 ){
			wm->accY = 0;
			wm->procNo = BWMPROC_NOMAL;
		}

		break;
	case BWMPROC_DISAPP:
		// ズレを计算
		wm->accY++;
		wm->difY -= wm->accY;

		// ウィンドウを移动
		wI->wy = wm->wy+wm->difY;
	
		// 处理の设定を变更
		if( wm->accY >= 17 ){
			wm->accY = 17;
			wm->wininfo->flag|=WIN_INFO_DEL;
			wm->procNo = BWMPROC_CLOSE;
		}

		// スキルウィンドウ破弃
		if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
		// アイテムウィンドウ破弃
		if (WindowFlag[MENU_WINDOW_BTLITEM].wininfo) WindowFlag[MENU_WINDOW_BTLITEM].wininfo->flag |= WIN_INFO_DEL;
		// 使い魔ウィンドウ破弃
		if (WindowFlag[MENU_WINDOW_BTLPET].wininfo) WindowFlag[MENU_WINDOW_BTLPET].wininfo->flag |= WIN_INFO_DEL;
		break;
	}

#ifdef PUK3_RIDE_BATTLE
	if ( !( wm->WinType == BWMPROC_Monster || wm->WinType == BWMPROC_PetRide ) ){
		 wI->sw[EnumBtBattleReBirth].Enabled = TRUE;
	}
#else
	if (wm->WinType != BWMPROC_Monster) wI->sw[EnumBtBattleReBirth].Enabled = TRUE;
#endif

	if ( wm->WinType == BWMPROC_Player2 ){
		wI->sw[EnumBtBattleSkill].Enabled = TRUE;
		wI->sw[EnumBtBattleItem].Enabled = TRUE;
		wI->sw[EnumBtBattleMonster].Enabled = TRUE;
		wI->sw[EnumBtBattlePosition].Enabled = TRUE;
		// ボタンが使用不可になっている场合
		if (BattleUnusableButtonFlag){
			wI->sw[EnumBtBattleSkill].Enabled = FALSE;
			wI->sw[EnumBtBattleItem].Enabled = FALSE;
			wI->sw[EnumBtBattleMonster].Enabled = FALSE;
			wI->sw[EnumBtBattlePosition].Enabled = FALSE;
		}
	}

	return TRUE;
}

BOOL MenuWindowBattleDraw( int mouse )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	int i;
	short x,y;
	char HitChara;

	displayMenuWindow();

#ifdef PUK3_RIDE_BATTLE
	if ( wm->WinType == BWMPROC_Monster || wm->WinType == BWMPROC_PetRide ){
#else
	if ( wm->WinType == BWMPROC_Monster ){
#endif
		if ( BattlePetSkillSelectNo >= 0 ){
			strcpy( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetSkill].Switch )->text,
				pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].name );
		}else{
			strcpy( ( (TEXT_SWITCH *)wm->wininfo->sw[EnumTxtBattlePetSkill].Switch )->text, "" );
		}
	}

	if ( wm->procNo == BWMPROC_NOMAL ){
		HitChara = -1;
		// 选择できるかチェック
		for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
			CheckIdRange( i );
#endif
			// ポインタがない时返回
			if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ i ] );
#endif
			// 当たり判定ボックス表示フラグＯＮの时
			if( pActBc[ i ]->atr & ACT_ATR_HIT_BOX ) break;
		}
		if (i<BC_MAX){
			// ヒットをチェック
			for( i = 0 ; i < BC_MAX ; i++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( i );
#endif
				// ポインタがない时返回
				if( pActBc[ i ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBc[ i ] );
#endif
				// 当たり判定ボックス表示フラグＯＮの时
				if( pActBc[ i ]->hitDispNo == HitDispNo && pActBc[ i ]->atr & ACT_ATR_HIT_BOX ){
					HitChara = i;
					break;
				}
			}
			if (i >= BC_MAX) i = -1;
			
			x = wI->wx+2;
			y = wI->wy+74;
			if( HitChara >= 0 ){
#ifdef PUK3_PACTBC_CHECKRANGE
				CheckIdRange( HitChara );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBc[HitChara] );
#endif
				if (pActBc[HitChara]->freeName[0]!='\0'){
					StockFontBuffer( x+15, y+15, FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, pActBc[HitChara]->freeName, 0, 0 );
				}else{
					StockFontBuffer( x+15, y+15, FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, pActBc[HitChara]->name, 0, 0 );
				}
			}else StockFontBuffer( x+15, y+15, FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, ML_STRING(136, "请选择目标"), 0, 0 );

			MenuWindowCommonDraw( GID_SpeakWindow, wI->wx+2, wI->wy+69, 296, 61, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );
		}
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}


// ボタンフラグの初期化
void InitBattleMenuButtonFlag_PUK2( void )
{
	int i;
	
	// ボタンの数だけループ
	for( i = 0 ; i < B_MENU_BUTTON_MAX ; i++ ){
		BattleMenuButtonFlag[ i ] = 0;
	}
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo){
		WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
	}
	if (WindowFlag[MENU_WINDOW_BTLITEM].wininfo){
		WindowFlag[MENU_WINDOW_BTLITEM].wininfo->flag |= WIN_INFO_DEL;
	}
	if (WindowFlag[MENU_WINDOW_BTLPET].wininfo){
		WindowFlag[MENU_WINDOW_BTLPET].wininfo->flag |= WIN_INFO_DEL;
	}

	if (BattleSkillOpen){ DeathAction(BattleSkillOpen);		BattleSkillOpen = NULL; }
	if (BattleItemOpen){ DeathAction(BattleItemOpen);		BattleItemOpen = NULL; }
	if (BattleMonsterOpen){ DeathAction(BattleMonsterOpen);	BattleMonsterOpen = NULL; }
}

// 各タスクバーボタン(战闘コクピット关系)の处理 *********************//

int BattleTaskSkillButtonStatus()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	// 战闘コクピットが操作できないとき
	if ( !wm->wininfo ) return 0;
	if ( wm->procNo != BWMPROC_NOMAL ) return 0;

	// 観战のときは使用不可
	if ( BattleMyNo >= BC_MAX ) return 0;

	switch(wm->WinType){
	case BWMPROC_Player_:case BWMPROC_Player1:case BWMPROC_Player2:
		if (BattleUnusableButtonFlag) return 0;
		if (BattleMenuButtonFlag[ 2 ]) return 0;
		return -1;
	}
	return 0;
}

static void BattleSkillReturn( int lSkill, int lLevel );

void BattleTaskSkillButton()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	// 战闘コクピットが操作できないとき
	if ( !wm->wininfo ) return;
	if ( wm->procNo != BWMPROC_NOMAL ) return;

	// 観战のときは使用不可
	if ( BattleMyNo >= BC_MAX ) return;

	switch(wm->WinType){
	case BWMPROC_Player_:case BWMPROC_Player1:case BWMPROC_Player2:
		if (BattleUnusableButtonFlag) break;

		if (!BattleMenuButtonFlag[ 2 ]){
			// 战闘ボタンのフラグ初期化
			InitBattleMenuButtonFlag_PUK2();
			// ボタン凹ませる
			BattleMenuButtonFlag[ 2 ] = 1;
			// 当たり判定ボックス消す
			// バックアップ
			BattleMenuButtonFlagBak = 2;
			ClearBoxFlag();
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
			// 战闘スキルウィンドウプロセス番号初期化
			BattleSkillWndProcNo = 0;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			TargetSelOK = 0;

			BattleSkillOpen = openBattleSkillWindow( BattleSkillReturn );
		}
		break;
	}
}


int BattleTaskItemButtonStatus()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	// 战闘コクピットが操作できないとき
	if ( !wm->wininfo ) return 0;
	if ( wm->procNo != BWMPROC_NOMAL ) return 0;

	// 観战のときは使用不可
	if ( BattleMyNo >= BC_MAX ) return 0;

	switch(wm->WinType){
	case BWMPROC_Player_:case BWMPROC_Player1:case BWMPROC_Player2:
		if (BattleUnusableButtonFlag) return 0;
		if (BattleMenuButtonFlag[ 3 ]) return 0;
		return -1;
	}
	return 0;
}

void BattleTaskItemButton()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	// 战闘コクピットが操作できないとき
	if ( !wm->wininfo ) return;
	if ( wm->procNo != BWMPROC_NOMAL ) return;

	// 観战のときは使用不可
	if ( BattleMyNo >= BC_MAX ) return;

	switch(wm->WinType){
	case BWMPROC_Player_:case BWMPROC_Player1:case BWMPROC_Player2:
		if (BattleUnusableButtonFlag) break;

		if (!BattleMenuButtonFlag[ 3 ]){
			// 战闘ボタンのフラグ初期化
			InitBattleMenuButtonFlag_PUK2();
			// ボタン凹ませる
			BattleMenuButtonFlag[ 3 ] = 1;
			// バックアップ
			BattleMenuButtonFlagBak = 3;
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
			// 战闘アイテムウィンドウプロセス番号初期化
			BattleItemWndProcNo = 0;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			TargetSelOK = 0;

			BattleItemOpen = openMenuWindow( MENU_WINDOW_BTLITEM, OPENMENUWINDOW_HIT, 0 );
		}
		break;
	}
}


int BattleTaskMonsterButtonStatus()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	// 战闘コクピットが操作できないとき
	if ( !wm->wininfo ) return 0;
	if ( wm->procNo != BWMPROC_NOMAL ) return 0;

	// 観战のときは使用不可
	if ( BattleMyNo >= BC_MAX ) return 0;

	switch(wm->WinType){
	case BWMPROC_Player_:case BWMPROC_Player1:case BWMPROC_Player2:
		if (BattleUnusableButtonFlag) return 0;
		if (BattleMenuButtonFlag[ 4 ]) return 0;
		return -1;
	}
	return 0;
}

void BattleTaskMonsterButton()
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];

	// 战闘コクピットが操作できないとき
	if ( !wm->wininfo ) return;
	if ( wm->procNo != BWMPROC_NOMAL ) return;

	// 観战のときは使用不可
	if ( BattleMyNo >= BC_MAX ) return;

	switch(wm->WinType){
	case BWMPROC_Player_:case BWMPROC_Player1:case BWMPROC_Player2:
		if (BattleUnusableButtonFlag) break;

		if (!BattleMenuButtonFlag[ 4 ]){
			// 战闘ボタンのフラグ初期化
			InitBattleMenuButtonFlag_PUK2();
			// ボタン凹ませる
			BattleMenuButtonFlag[ 4 ] = 1;
			// バックアップ
			BattleMenuButtonFlagBak = 4;
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			BattleMonsterOpen = openMenuWindow( MENU_WINDOW_BTLPET, OPENMENUWINDOW_HIT, 0 );
		}
		break;
	}
}

// 各ボタンの处理 *********************//

// Attack ボタン
BOOL MenuWindowBattleAttack( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleAttackOn;
		if (BattleMenuButtonFlag[0]) Graph->graNo = GID_BattleAttackOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_ATTACK );
	}

	if( flag & MENU_MOUSE_LEFT ){
		// 战闘ボタンのフラグ初期化
		InitBattleMenuButtonFlag_PUK2();
		// ボタン凹ませる
		BattleMenuButtonFlag[ 0 ] = 1;
		// バックアップ
		BattleMenuButtonFlagBak = 0;
		// 当たり判定ボックス消す
		ClearBoxFlag();

		// ターゲットボックス作成
		BattleSetTargetBox( BattleMyNo, 1141 );	// 单体

		// ウィンドウアクション抹杀
		DeathAction( pActBattleWnd );
		// ポインタ初期化
		pActBattleWnd = NULL;
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		TargetSelOK = 0;

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BattleAttackOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleAttackOver;
	if (BattleMenuButtonFlag[0]) Graph->graNo = GID_BattleAttackOff;

	return ReturnFlag;
}

// Guard ボタン
BOOL MenuWindowBattleGuard( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleGuardOn;
		if (BattleMenuButtonFlag[1]) Graph->graNo = GID_BattleGuardOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_GUARD );
	}

	if( flag & MENU_MOUSE_LEFT ){
		// 战闘ボタンのフラグ初期化
		InitBattleMenuButtonFlag_PUK2();
		// ボタン凹ませる
		BattleMenuButtonFlag[ 1 ] = 1;
		// バックアップ
		BattleMenuButtonFlagBak = 0;
		// 当たり判定ボックス消す
		ClearBoxFlag();

		// 防御送信
		nrproto_B_send( sockfd, "G" );                                      //MLHIDE

		// ウィンドウアクション抹杀
		DeathAction( pActBattleWnd );
		// ポインタ初期化
		pActBattleWnd = NULL;
		// 入力济みフラグＯＮ
		BattleMenuInputFlag = TRUE;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BattleGuardOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleGuardOver;
	if (BattleMenuButtonFlag[1]) Graph->graNo = GID_BattleGuardOff;

	return ReturnFlag;
}

// スキル用戾り处理关数
static void BattleSkillReturn( int lSkill, int lLevel )
{
	// スキル番号决定
	BattleSelectSkillNo = lSkill;
	// アビリティ番号决定
	BattleSelectAbilityNo = lLevel;
	
	// ターゲットボックス作成
	if( BattleSetTargetBox( BattleMyNo, job.skill[ BattleSelectSkillNo ].tech[ BattleSelectAbilityNo ].target ) == FALSE ){
		char moji[256];
		// ターゲット选择无しのとき
		// 文字列作成
		sprintf( moji, "S|%X|%X|%X", BattleSelectSkillNo, BattleSelectAbilityNo, BattleMyNo ); //MLHIDE
		// スキル攻击送信
		nrproto_B_send( sockfd, moji );
		// 当たり判定ボックス消す
		ClearBoxFlag();
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		// 入力されたら
		// プレイヤーコマンド入力フラグＯＮ
		BattleCmdPlayerInputFlag++;
		// 次のプロセスへ
		BattleMenuProcNo = B_MENU_PLAYER_BACK;
		// 当たり判定ボックス消す
		ClearBoxFlag();
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		// ウィンドウ破弃
		DisappBattleWindow();
	}else{
		// ターゲット选择へ
		SkillTargetSelect = 1;
	}
}

// Skill ボタン
BOOL MenuWindowBattleSkill( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// ボタンが使用不可になっている场合
	if (BattleUnusableButtonFlag){
		Graph->graNo = GID_BattleSkillOff;
		return ReturnFlag;
	}

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleSkillOn;
		if (BattleMenuButtonFlag[2]) Graph->graNo = GID_BattleSkillOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_SKILL );
	}
	if (!BattleMenuButtonFlag[ 2 ]){
		if( flag & MENU_MOUSE_LEFT ){
			// 战闘ボタンのフラグ初期化
			InitBattleMenuButtonFlag_PUK2();
			// ボタン凹ませる
			BattleMenuButtonFlag[ 2 ] = 1;
			// 当たり判定ボックス消す
			// バックアップ
			BattleMenuButtonFlagBak = 2;
			ClearBoxFlag();
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
			// 战闘スキルウィンドウプロセス番号初期化
			BattleSkillWndProcNo = 0;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			TargetSelOK = 0;

			BattleSkillOpen = openBattleSkillWindow( BattleSkillReturn );

			ReturnFlag=TRUE;
		}
	}else{
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			if (SkillTargetSelect){
				BattleSkillOpen = openBattleAbirityWindow( BattleSelectSkillNo, BattleSkillReturn );
				SkillTargetSelect = 0;
				// 当たり判定ボックス消す
				ClearBoxFlag();
			}
			else{
				if ( !WindowFlag[MENU_WINDOW_SKILL].wininfo ) BattleSkillOpen = openBattleSkillWindow( BattleSkillReturn );
				else if ( BackSkillMode() ) BattleMenuButtonFlag[ 2 ] = 0;
			}
		}
	}

	Graph->graNo = GID_BattleSkillOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleSkillOver;
	if (BattleMenuButtonFlag[2]) Graph->graNo = GID_BattleSkillOff;

	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) BattleSkillOpen = NULL;
	else if (!BattleSkillOpen){
		if (!SkillTargetSelect) BattleMenuButtonFlag[2] = 0;
	}

	return ReturnFlag;
}

// Item ボタン
BOOL MenuWindowBattleItem( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// ボタンが使用不可になっている场合
	if (BattleUnusableButtonFlag){
		Graph->graNo = GID_BattleItemOff;
		return ReturnFlag;
	}

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleItemOn;
		if (BattleMenuButtonFlag[3]) Graph->graNo = GID_BattleItemOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_ITEM );
	}
	if (!BattleMenuButtonFlag[ 3 ]){
		if( flag & MENU_MOUSE_LEFT ){
			// 战闘ボタンのフラグ初期化
			InitBattleMenuButtonFlag_PUK2();
			// ボタン凹ませる
			BattleMenuButtonFlag[ 3 ] = 1;
			// バックアップ
			BattleMenuButtonFlagBak = 3;
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
			// 战闘アイテムウィンドウプロセス番号初期化
			BattleItemWndProcNo = 0;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			TargetSelOK = 0;

			BattleItemOpen = openMenuWindow( MENU_WINDOW_BTLITEM, OPENMENUWINDOW_HIT, 0 );

			ReturnFlag=TRUE;
		}
	}else{
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			if (ItemTargetSelect){
				BattleItemOpen = openMenuWindow( MENU_WINDOW_BTLITEM, OPENMENUWINDOW_HIT, 0 );
				ItemTargetSelect = 0;
				// 当たり判定ボックス消す
				ClearBoxFlag();
			}
// バトルアイテムウィンドウのほうで、消える处理をするので、ここはしない
//			else if ( WindowFlag[MENU_WINDOW_BTLITEM].wininfo ) WindowFlag[MENU_WINDOW_BTLITEM].wininfo->flag |= WIN_INFO_DEL;
		}
	}

	Graph->graNo = GID_BattleItemOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleItemOver;
	if (BattleMenuButtonFlag[3]) Graph->graNo = GID_BattleItemOff;

	if (WindowFlag[MENU_WINDOW_BTLITEM].wininfo) BattleItemOpen = NULL;
	else if (!BattleItemOpen){
		if (!ItemTargetSelect) BattleMenuButtonFlag[3] = 0;
	}

	return ReturnFlag;
}

// Monster ボタン
BOOL MenuWindowBattleMonster( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// ボタンが使用不可になっている场合
	if (BattleUnusableButtonFlag){
		Graph->graNo = GID_BattleMonsterOff;
		return ReturnFlag;
	}

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleMonsterOn;
		if (BattleMenuButtonFlag[4]) Graph->graNo = GID_BattleMonsterOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_MONSTER );
	}
	if (!BattleMenuButtonFlag[ 4 ]){
		if( flag & MENU_MOUSE_LEFT ){
			// 战闘ボタンのフラグ初期化
			InitBattleMenuButtonFlag_PUK2();
			// ボタン凹ませる
			BattleMenuButtonFlag[ 4 ] = 1;
			// バックアップ
			BattleMenuButtonFlagBak = 4;
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );

			BattleMonsterOpen = openMenuWindow( MENU_WINDOW_BTLPET, OPENMENUWINDOW_HIT, 0 );

			ReturnFlag=TRUE;
		}
	}else{
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			if ( WindowFlag[MENU_WINDOW_BTLPET].wininfo ) WindowFlag[MENU_WINDOW_BTLPET].wininfo->flag |= WIN_INFO_DEL;
		}
	}

	Graph->graNo = GID_BattleMonsterOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleMonsterOver;
	if (BattleMenuButtonFlag[4]) Graph->graNo = GID_BattleMonsterOff;

	if (WindowFlag[MENU_WINDOW_BTLPET].wininfo) BattleMonsterOpen = NULL;
	else if (!BattleMonsterOpen) BattleMenuButtonFlag[4] = 0;

	return ReturnFlag;
}

// Position ボタン
BOOL MenuWindowBattlePosition( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// ボタンが使用不可になっている场合
	if (BattleUnusableButtonFlag){
		Graph->graNo = GID_BattlePositionOff;
		return ReturnFlag;
	}

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattlePositionOn;
		if(BattleMenuButtonFlag[6]) Graph->graNo = GID_BattlePositionOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_POSITION );
	}
	// このスイッチが押されたときはウィンドウ切り替え
	if( flag & MENU_MOUSE_LEFT ){
		// 战闘ボタンのフラグ初期化
		InitBattleMenuButtonFlag_PUK2();
		// ボタン凹む
		BattleMenuButtonFlag[ 6 ] = 1;
		// バックアップ
		BattleMenuButtonFlagBak = 6;
		// 当たり判定ボックス消す
		ClearBoxFlag();

		// ポジションチェンジ送信
		nrproto_B_send( sockfd, "P" );                                      //MLHIDE

		// ウィンドウアクション抹杀
		DeathAction( pActBattleWnd );
		// ポインタ初期化
		pActBattleWnd = NULL;
		// 入力济みフラグＯＮ
		BattleMenuInputFlag = TRUE;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BattlePositionOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattlePositionOver;
	if(BattleMenuButtonFlag[6]) Graph->graNo = GID_BattlePositionOff;

	return ReturnFlag;
}

// Escape ボタン
BOOL MenuWindowBattleEscape( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleEscapeOn;
		if(BattleMenuButtonFlag[7]) Graph->graNo = GID_BattleEscapeOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_ESCAPE );
	}
	if( flag & MENU_MOUSE_LEFT ){
		// 战闘ボタンのフラグ初期化
		InitBattleMenuButtonFlag_PUK2();
		// ボタン凹ませる
		BattleMenuButtonFlag[ 7 ] = 1;
		// バックアップ
		BattleMenuButtonFlagBak = 0;
		// 当たり判定ボックス消す
		ClearBoxFlag();

		// 逃げる送信
		nrproto_B_send( sockfd, "E" );                                      //MLHIDE

		// ウィンドウアクション抹杀
		DeathAction( pActBattleWnd );
		// ポインタ初期化
		pActBattleWnd = NULL;
		// 入力济みフラグＯＮ
		BattleMenuInputFlag = TRUE;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BattleEscapeOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleEscapeOver;
	if(BattleMenuButtonFlag[7]) Graph->graNo = GID_BattleEscapeOff;

	return ReturnFlag;
}

// Re-Birth ボタン
BOOL MenuWindowBattleReBirth( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// リバースが出来ないときは表示を止めてボタンを押せなくする
	if ( (rebirthflg) || (pc.rebirthLevel==0) || (pc.bt<=0) ){
		wI->sw[no].Enabled = FALSE;
		Graph->graNo = GID_BattleReBirthOff;
		return ReturnFlag;
	}else wI->sw[no].Enabled = TRUE;

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleReBirthOn;
		if(BattleMenuButtonFlag[8]) Graph->graNo = GID_BattleReBirthOff;
		return FALSE;
	}

#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
	if (pActBc[ BattleMyNo ]==NULL) return ReturnFlag;
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBc[ BattleMyNo ] );
#endif
	if (pActBc[ BattleMyNo ]->pYobi==NULL) return ReturnFlag;

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		if ( ( (BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->pActTrance ){
			strcpy( OneLineInfoStr, MWONELINE_BATTLE_REBIRTH_OFF );
		}else{
			strcpy( OneLineInfoStr, MWONELINE_BATTLE_REBIRTH_ON );
		}
	}

	if( flag & MENU_MOUSE_LEFT ){
		// 战闘ボタンのフラグ初期化
		InitBattleMenuButtonFlag_PUK2();
		// ボタン凹ませる
		BattleMenuButtonFlag[ 8 ] = 1;
		// バックアップ
		BattleMenuButtonFlagBak = 8;
		// 当たり判定ボックス消す
		ClearBoxFlag();

		// リバースＯＮ??ＯＦＦ送信
		if ( ( (BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->pActTrance ){
			nrproto_B_send( sockfd, "R|0" );                                   //MLHIDE
		}else{
			nrproto_B_send( sockfd, "R|1" );                                   //MLHIDE
		}

		// ウィンドウアクション抹杀
		DeathAction( pActBattleWnd );
		// ポインタ初期化
		pActBattleWnd = NULL;
		// 入力济みフラグＯＮ
		BattleMenuInputFlag = TRUE;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BattleReBirthOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleReBirthOver;
	if(BattleMenuButtonFlag[8]) Graph->graNo = GID_BattleReBirthOff;

	return ReturnFlag;
}

// ペットウィンドウ用戾り处理关数
void BattlePetSkillReturn( int lSkill )
{
	char moji[256];

	// パスボタンが押されたとき
	if ( lSkill < 0 ){
		// ボタンもどす
		BattleMenuPetButtonFlag = 0;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		// 何もしない送信
		if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "W|FF" );        //MLHIDE
		// 当たり判定ボックス消す
		ClearBoxFlag();
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		// ペットコマンド入力フラグＯＮ
		BattleCmdPetInputFlag++;
		// 次のプロセスへ
		BattleMenuProcNo = B_MENU_PET_BACK;
		// このターンのペット番号を学习
		BattlePetNoBak = CheckBattlePet();

		// ウィンドウ破弃
		DisappBattleWindow();
	}else{
		// スキル番号决定
		BattlePetSkillSelectNo = lSkill;
		
		if( BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].target ) == FALSE ){
			// ターゲット选择无しのとき
			// 文字列作成
			sprintf( moji, "W|%X|%X", BattlePetSkillSelectNo, CheckBattlePet() ); //MLHIDE
			// 通常攻击
			nrproto_B_send( sockfd, moji );
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			// 入力されたら
			// ペットコマンド入力フラグＯＮ
			BattleCmdPetInputFlag++;
			// 次のプロセスへ
			BattleMenuProcNo = B_MENU_PET_BACK;
			// このターンのペット番号を学习
			BattlePetNoBak = CheckBattlePet();

			// ウィンドウ破弃
			DisappBattleWindow();
		}
		
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		// ペットスキルウィンドウ表示する时
		BattleMenuPetButtonFlag = 2;
	}
}

#ifdef PUK3_RIDE_BATTLE
// ペットウィンドウ用戾り处理关数
void BattlePetRideSkillReturn( int lSkill )
{
	char moji[256];

	// パスボタンが押されたとき
	if ( lSkill < 0 ){
		// ボタンもどす
		BattleMenuPetButtonFlag = 0;
		// ＰＣのATTACKボタン选择
		BattleMenuButtonFlagBak = 0;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		// 何もしない送信
		if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "W|FF" );        //MLHIDE
		// 当たり判定ボックス消す
		ClearBoxFlag();
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		// ペットコマンド入力フラグＯＮ
		BattleCmdPetInputFlag++;
		// 次のプロセスへ
		BattleMenuProcNo = B_MENU_PET_BACK;
		// このターンのペット番号を学习
		BattlePetNoBak = BattleRidePetNo;

		// ウィンドウ破弃
		DisappBattleWindow();
	}else{
		// スキル番号决定
		BattlePetSkillSelectNo = lSkill;
		// ＰＣのATTACKボタン选择
		BattleMenuButtonFlagBak = 0;
		
		if( BattleSetTargetBox( BattleMyNo, pet[ BattleRidePetNo ].tech[ BattlePetSkillSelectNo ].target ) == FALSE ){
			// ターゲット选择无しのとき
			// 文字列作成
			sprintf( moji, "W|%X|%X", BattlePetSkillSelectNo, BattleRidePetNo ); //MLHIDE
			// 通常攻击
			nrproto_B_send( sockfd, moji );
			// 当たり判定ボックス消す
			ClearBoxFlag();
			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			// 入力されたら
			// ペットコマンド入力フラグＯＮ
			BattleCmdPetInputFlag++;
			// 次のプロセスへ
			BattleMenuProcNo = B_MENU_PET_BACK;
			// このターンのペット番号を学习
			BattlePetNoBak = BattleRidePetNo;

			// ウィンドウ破弃
			DisappBattleWindow();
		}
		
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		// ペットスキルウィンドウ表示する时
		BattleMenuPetButtonFlag = 2;
	}
}
#endif

BOOL MenuWindowBattlePetSkill( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	static ACTION *pAct;

	if ( wm->procNo != BWMPROC_NOMAL ) return FALSE;

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		if ( WindowFlag[MENU_WINDOW_SKILL].wininfo ) strcpy( OneLineInfoStr, MWONELINE_BATTLEPET_SKILL_ON );
		else if ( !pAct ) strcpy( OneLineInfoStr, MWONELINE_BATTLEPET_SKILL_OFF );
	}
	if (!pAct){
		if( (flag&MENU_MOUSE_LEFT) || (mouse.onceState&MOUSE_RIGHT_CRICK) ){
			if (WindowFlag[MENU_WINDOW_SKILL].wininfo) WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
#ifdef PUK3_RIDE_BATTLE
			else{
	#ifdef PUK2_BODYGUARD_FOR_ENEMY
				// 当たり判定ボックス消す
				ClearBoxFlag();
				// ボタンフラグ变更
				BattleMenuPetButtonFlag = 1;
				// ペットスキルバックアップ初期化
				BattlePetSkillSelectNo = -1;
	#endif
				if ( wm->WinType == BWMPROC_PetRide ){
					pAct = openBattlePetRideSkillWindow( BattlePetRideSkillReturn );
				}else{
					pAct = openBattlePetSkillWindow( BattlePetSkillReturn );
				}
			}
#else
	#ifdef PUK2_BODYGUARD_FOR_ENEMY
			else{
				// 当たり判定ボックス消す
				ClearBoxFlag();
				// ボタンフラグ变更
				BattleMenuPetButtonFlag = 1;
				// ペットスキルバックアップ初期化
				BattlePetSkillSelectNo = -1;

				pAct = openBattlePetSkillWindow( BattlePetSkillReturn );
			}
	#else
			else pAct = openBattlePetSkillWindow( BattlePetSkillReturn );
	#endif
#endif

			ReturnFlag=TRUE;
		}
	}

	Graph->graNo = GID_BattleSkillOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleSkillOver;
	if (WindowFlag[MENU_WINDOW_SKILL].wininfo||pAct) Graph->graNo = GID_BattleSkillOff;

	if (WindowFlag[MENU_WINDOW_SKILL].wininfo) pAct = NULL;

	return ReturnFlag;
}
#ifdef PUK3_RIDE_BATTLE
// ライド用Escape ボタン
BOOL MenuWindowBattleRideEscape( int no, unsigned int flag )
{
	struct BATTLEWINDOWMASTER *wm=(struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE];
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH	*Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if ( wm->procNo != BWMPROC_NOMAL ){
		Graph->graNo = GID_BattleEscapeOn;
		if(BattleMenuButtonFlag[7]) Graph->graNo = GID_BattleEscapeOff;
		return FALSE;
	}

	if( flag & MENU_MOUSE_OVER ){
		// 一行インフォ
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_ESCAPE );
	}
	if( flag & MENU_MOUSE_LEFT ){
		// 战闘ボタンのフラグ初期化
		InitBattleMenuButtonFlag_PUK2();
		// ボタン凹ませる
		BattleMenuButtonFlag[ 7 ] = 1;
		// バックアップ
		BattleMenuButtonFlagBak = 0;
		// 当たり判定ボックス消す
		ClearBoxFlag();

		// 逃げる送信
		nrproto_B_send( sockfd, "E" );                                      //MLHIDE

		// ウィンドウアクション抹杀
		DeathAction( pActBattleWnd );
		// ポインタ初期化
		pActBattleWnd = NULL;
		// 入力济みフラグＯＮ
		BattleMenuInputFlag = TRUE;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BattleEscapeOn;
	if(flag & MENU_MOUSE_OVER) Graph->graNo = GID_BattleEscapeOver;
	if(BattleMenuButtonFlag[7]) Graph->graNo = GID_BattleEscapeOff;

	return ReturnFlag;
}
#endif

//===================================//

// カウントダウン处理 ****************//
void BattleCntDownDisp_PUK2( void )
{
	char moji[ 16 ];
	char *work = moji;
	int x = 320 - 16, y = 240, i;
#ifdef WIN_SIZE_DEF
	//根据分辨率计算战斗倒计时的显示位置(默认x = 320 - 16, y = 240)
	x += SymOffsetX;
	y += SymOffsetY;
#endif
	// 残り时间
	int BattleCntDownRest = BattleCntDown - GetTickCount();
	int flag = FALSE;
	int cntGraNo;
	
	// フラグチェック
	if( BattleCntDownFlag == FALSE ) return;
	
	// タイムオーバーの时
	if( BattleCntDownRest <= 0 ){
		// フラグ初期化
		BattleCntDownFlag = FALSE;
		BattleCntDownRest = 0;
		// 当たり判定ボックス消す
		ClearBoxFlag();
#ifdef PUK2_NEW_MENU
		// ウィンドウ破弃
		DisappBattleWindow();
#endif
		
#ifdef PUK3_RIDE_BATTLE
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( BattleMyNo );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[BattleMyNo] );
#endif
		// ライド中
		if ( ( (BC_YOBI *)pActBc[BattleMyNo]->pYobi )->pActRideChar ){
			// 何もしない送信(２行动あっても一回)
			nrproto_B_send( sockfd, "W|FF" );                                  //MLHIDE
			// 强制終了フラグＯＮ
			flag = TRUE;
		}
		else
#endif
		// ペットが参战しているとき
		if( BattleBpFlag & BP_FLAG_PET ){
			// プレイヤーコマンド入力济みフラグ
			if( BattleCmdPlayerInputFlag == FALSE ){
				// メニュー无しの时、または観战の时
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ) ){
					// プレイヤー何もしない送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// 强制終了フラグＯＮ
					flag = TRUE;
				}
			}
			// ペットコマンド入力济みフラグ
			if( BattleCmdPetInputFlag == FALSE ){
				// ペット何もしない送信
				nrproto_B_send( sockfd, "W|FF" );                                 //MLHIDE
				// 强制終了フラグＯＮ
				flag = TRUE;
			}
		}else{
			// プレイヤーコマンド入力济みフラグ
			if( BattleCmdPlayerInputFlag == FALSE ){
				// メニュー无しの时、または観战の时
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ) ){
					// プレイヤー何もしない２回送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// 强制終了フラグＯＮ
					flag = TRUE;
				}
			}
			// プレイヤーコマンド入力济みフラグ
			if( BattleCmdPlayerInputFlag == TRUE ){
				// メニュー无しの时、または観战の时
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ) ){
					// プレイヤー何もしない送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// 强制終了フラグＯＮ
					flag = TRUE;
				}
			}
		}

		// 强制終了フラグＯＮの时
		if( flag == TRUE ){
			// ＮＧ音
			play_se( SE_NO_NG, 320, 240 );
			// 次のプロセスへ
			SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
			// ウィンドウアクション抹杀
			DeathAction( pActBattleWnd );
			// ポインタ初期化
			pActBattleWnd = NULL;
		}
	}
	
	// １０秒より小さいとき
	if( BattleCntDownRest < 1000 * 10 ){ 
		cntGraNo = CG_B_CNT_DOWN2_0;
		if( BattleCntDownRest / 1000 != BattleCntDownBak ){
			// カウントダウンの音
			play_se( 202, 320, 240 );
			// バックアップする
			BattleCntDownBak = BattleCntDownRest / 1000;
		}
	}
	else cntGraNo = CG_B_CNT_DOWN_0;
	
	// 文字列に变换（二桁表示）
	sprintf( moji, "%2d", BattleCntDownRest / 1000 );                    //MLHIDE
	
	// 文字の数だけループ
	for( i = 0 ; i < 2 ; i++ ){
		// 空白でない时
		if( *work != 0x20 ){
			// 表示
			StockDispBuffer( x, y, DISP_PRIO_CNT_DOWN, *work - '0' + cntGraNo, 0 );
		}
		// Ｘ座标移动
		x += 32;
		// ポインタ进める
		work++;
	}
	//デュエルなら
	if( DuelFlag == TRUE ){
		// 文字列に变换（二桁表示）
		sprintf( moji, ML_STRING(138, "第 %d 回合"), BattleCliTurnNo + 1 );
		// 表示
		//根据分辨率计算战斗回合数的显示位置
		StockFontBuffer( SymOffsetX + 320 - 32, SymOffsetY + 240 - 60, FONT_PRIO_BACK, FONT_PAL_YELLOW, moji, 0 );
		
	}
}

// 战闘コマンド入力制御 **************//

void BattleMenuProc_PUK2( void )
{
	char moji[ 256 ];
	int targetNo;
	static int ButtonBaseGraNo; // ボタンベースグラフィック番号
#ifdef PUK2
	BLT_MEMBER bm={0};

	bm.rgba.rgba=0xffffffff;
	bm.bltf=BLTF_NOCHG;
#endif

#ifdef PUK3_MOUSECURSOR
	// ヒットをチェック
	for( targetNo = 0 ; targetNo < BC_MAX ; targetNo++ ){
#ifdef PUK3_PACTBC_CHECKRANGE
		CheckIdRange( targetNo );
#endif
		// ポインタがない时返回
		if( pActBc[ targetNo ] == NULL ) continue;
#ifdef PUK3_ACTION_CHECKRANGE
		CheckAction( pActBc[ targetNo ] );
#endif
		// 当たり判定ボックス表示フラグＯＮの时
		if ( pActBc[ targetNo ]->hitDispNo == HitDispNo &&
			 pActBc[ targetNo ]->atr & ACT_ATR_HIT_BOX ){
			break;
		}
	}
	// マウスカーソルの变更
	if ( targetNo < BC_MAX ) setMouseType( MOUSE_CURSOR_TYPE_BATTLE );
#endif
	// 信息ウィンドウ表示
	BattleInfoWndDisp();

#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( BattleMyNo );
	CheckIdRange( BattleCheckPetId( BattleMyNo ) );
#endif
	// バトルメニュープロセス番号で分岐
	switch( BattleMenuProcNo ){
		case B_MENU_PLAYER_INIT:	// プレイヤーメニュー初期化

#ifdef PUK3_RIDE_BATTLE
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[BattleMyNo] );
#endif
			// 観战中じゃなくて、ライド中
			if ( BattleMyNo < BC_MAX && ( (BC_YOBI *)pActBc[BattleMyNo]->pYobi )->pActRideChar ){
				// メニュー无しの时
				if( BattleBpFlag & BP_FLAG_PET_MENU_NON ){
					// 何もしない送信
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// プレイヤーコマンド入力フラグＯＮ
					BattleCmdPlayerInputFlag++;
	#ifdef PUK2_NEWSKILL
					// ウィンドウ生成
					if( BattleBpFlag & BP_FLAG_PET_MENU2_NON ){
						// 何もしない送信（２アクション目）
						nrproto_B_send( sockfd, "N" );                                  //MLHIDE
						// プレイヤーコマンド入力フラグＯＮ
						BattleCmdPlayerInputFlag++;
						// 次のプロセスへ
						SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
					}else{
						BattleBpFlag &= ~BP_FLAG_PET_MENU_NON;
						// リバースフラグの初期化
						rebirthflg=0;
					}
	#else
					// 何もしない送信（２アクション目）
					nrproto_B_send( sockfd, "N" );                                   //MLHIDE
					// プレイヤーコマンド入力フラグＯＮ
					BattleCmdPlayerInputFlag++;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
	#endif
	#ifdef PUK2_NEW_MENU
					// ウィンドウ破弃
					DisappBattleWindow();
	#endif
				}else{
					// スキル番号バックアップがあるときかつ、そのスキルが使えるとき（忘却チェック）かつ、魔力切れじゃないとき
					if( BattlePetSkillSelectNo != -1 && BattleMenuButtonFlagBak == 0 
						&& BattlePetUsableSkillFlag & ( 1 << BattlePetSkillSelectNo ) 
						&& !( pet[ BattleRidePetNo ].tech[ BattlePetSkillSelectNo ].fp > pActBc[ BattleMyNo ]->fp )
						){
						// 谁も选择できないとき
						if( BattleSetTargetBox( BattleMyNo, pet[ BattleRidePetNo ].tech[ BattlePetSkillSelectNo ].target ) == FALSE ){
							// バックアップがないときは选择しなおし
							BattleMenuPetButtonFlag = 1;
	#ifdef PUK2_NEW_MENU
							BattlePetSkillSelectNo = -1;
							openBattlePetRideSkillWindow(BattlePetRideSkillReturn);
	#endif
						}
					}else{
						// バックアップがないときは选择しなおし
						BattleMenuPetButtonFlag = 1;
	#ifdef PUK2_NEW_MENU
						BattlePetSkillSelectNo = -1;
						openBattlePetRideSkillWindow(BattlePetRideSkillReturn);
	#endif
					}
					// 战闘ペットベースウィンドウの当たり判定番号初期化
					BattleMenuPetBaseHitFontNo = -2;
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();

					// 初期へ込みボタンの设定
					// スキル选择中以外に共通する处理
					if ( BattleMenuButtonFlagBak > 0 ){
						// ボタン凹ませる
						BattleMenuButtonFlag[ BattleMenuButtonFlagBak ] = 1;
						// 当たり判定ボックス消す
						ClearBoxFlag();
					}else if ( BattleMenuButtonFlagBak == 0 ){
						// ボタン凹ませる
						BattleMenuButtonFlag[ BattleMenuButtonFlagBak ] = 1;
					}

					// 次のプロセスへ
					BattleMenuProcNo = B_MENU_PET;
	#ifdef PUK2_NEW_MENU
					// ウィンドウ生成
					AppearBattleWindow( BWMPROC_PetRide, DEFBATTLEWINPOSX, DEFBATTLEWINPOSY );
	#endif
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				}
			}else
#endif
			// メニュー无しの时、または観战の时
			if( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON ){
				// 何もしない送信
				if( BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "N" );          //MLHIDE
				// プレイヤーコマンド入力フラグＯＮ
				BattleCmdPlayerInputFlag++;
				// ペットが参加しているとき（２アクションじゃない时）
				if( BattleBpFlag & BP_FLAG_PET ){
					// 次のプロセスへ
					BattleMenuProcNo = B_MENU_PET_INIT;
				}else{
#ifdef PUK2_NEWSKILL
					// ウィンドウ生成
					if( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ){
						// 何もしない送信（２アクション目）
						if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "N" );       //MLHIDE
						// プレイヤーコマンド入力フラグＯＮ
						BattleCmdPlayerInputFlag++;
						// 次のプロセスへ
						SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
					}else{
						BattleBpFlag &= ~BP_FLAG_PLAYER_MENU_NON;
						// リバースフラグの初期化
						rebirthflg=0;
					}
#else

					// 何もしない送信（２アクション目）
					if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "N" );        //MLHIDE
					// プレイヤーコマンド入力フラグＯＮ
					BattleCmdPlayerInputFlag++;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
#endif
				}
#ifdef PUK2_NEW_MENU
				// ウィンドウ破弃
				DisappBattleWindow();
#endif
			}else{
				// リバースフラグの初期化
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
					if( BattleCmdPlayerInputFlag == 0 ) rebirthflg=0;
				}else rebirthflg=0;

				// バックアップから戾す
				// １アクション目の时
				if( BattleCmdPlayerInputFlag == 0 ){
					// １アクション目をバックアップから戾す
					BattleMenuButtonFlagBak = BattleMenuButtonFlagBak1;
				}else{
					// ２アクション目をバックアップから戾す
					BattleMenuButtonFlagBak = BattleMenuButtonFlagBak2;
				}

				// 战闘ボタンの当たり判定初期化
				InitHitNo( BattleMenuButtonHitDispNo, B_MENU_BUTTON_MAX );
				// 战闘ボタンのフラグ初期化
				InitBattleMenuButtonFlag_PUK2();

				// 初期へ込みボタンの设定
				// 全てのボタンに共通する处理
				if (BattleMenuButtonFlagBak>=0){
					// ボタン凹ませる
					BattleMenuButtonFlag[ BattleMenuButtonFlagBak ] = 1;
					// 当たり判定ボックス消す
					ClearBoxFlag();
				}

				// 各ボタン固有の处理
				switch( BattleMenuButtonFlagBak ){
				case 0:	// 攻击ボタンの时
					// ターゲットボックス作成
					BattleSetTargetBox( BattleMyNo, 1141 );
					break;
				case 2:	// スキルボタンの时
					// 战闘スキルウィンドウプロセス番号初期化
					openBattleSkillWindow( BattleSkillReturn );
					break;
				case 3:	// アイテムボタンの时
					// 战闘アイテムウィンドウプロセス番号初期化
					openMenuWindow( MENU_WINDOW_BTLITEM, OPENMENUWINDOW_HIT, 0 );
					break;
				case 4:	// ペットボタンの时
					// 战闘アイテムウィンドウプロセス番号初期化
					openMenuWindow( MENU_WINDOW_BTLPET, OPENMENUWINDOW_HIT, 0 );
					break;
				}
				
				// 次のプロセスへ
				BattleMenuProcNo = B_MENU_PLAYER;

	#ifdef PUK2_NEW_MENU
				// ウィンドウ生成
				if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
					// メニューベース
					if( BattleCmdPlayerInputFlag == 0 ) AppearBattleWindow( BWMPROC_Player1, DEFBATTLEWINPOSX, DEFBATTLEWINPOSY );
					else AppearBattleWindow( BWMPROC_Player2, DEFBATTLEWINPOSX, DEFBATTLEWINPOSY );
				}else AppearBattleWindow( BWMPROC_Player_, DEFBATTLEWINPOSX, DEFBATTLEWINPOSY );
	#endif

				// ウィンドウ开く音
				play_se( SE_NO_OPEN_WINDOW, 320, 240 );
			}

			// インプットフラグ初期化
			BattleMenuInputFlag = FALSE;
			break;

		case B_MENU_PLAYER:	// プレイヤーメニュー处理

			// 攻击ボタンの时
			if( BattleMenuButtonFlag[ 0 ] == 1 ){
				// 右クリックされた时
				if( mouse.onceState & MOUSE_RIGHT_CRICK ){
					// ボタン初期化
					BattleMenuButtonFlag[ 0 ] = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}

			// 各ウィンドウ处理
////			if( BattleMenuButtonFlag[ 2 ] == 1 ) BattleSkillWnd();
////			if( BattleMenuButtonFlag[ 3 ] == 1 ) BattleItemWnd();
////			if( BattleMenuButtonFlag[ 4 ] == 1 ) BattlePetWnd();
////			if( BattleMenuButtonFlag[ 5 ] == 1 ) BattleWeaponWnd();
/***
			// 攻击ボタンの时
			if( BattleMenuButtonHitDispNo[ 0 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"普通攻击。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 0 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();

					// ターゲットボックス作成
					BattleSetTargetBox( BattleMyNo, 1141 );	// 单体

					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// クリック音
					play_se( SE_NO_CLICK, 320, 240 );
				}
			}
			// 防御ボタンの时
			if( BattleMenuButtonHitDispNo[ 1 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"物理防御。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 1 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 防御送信
					nrproto_B_send( sockfd, "G" );
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			//	リバースボタンの时
			if( BattleMenuButtonHitDispNo[ 8 ] == HitDispNo ){
				if (rebirthflg){
					strcpy( OneLineInfoStr,"现在无法精灵变身。");
				}else
				if (pc.rebirthLevel==0){
					strcpy( OneLineInfoStr,"现在无法精灵变身。");
				}else{
					if (pc.bt<=0){
						strcpy( OneLineInfoStr,"现在无法精灵变身。");
					}else{
#ifdef PUK3_ACTION_CHECKRANGE
						CheckAction( pActBc[ BattleMyNo ] );
#endif
						// 一行インフォ
						if ( ( (BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->pActTrance ){
							strcpy( OneLineInfoStr,"解除精灵变身。");
						}else strcpy( OneLineInfoStr,"精灵变身。");
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// 战闘ボタンのフラグ初期化
							InitBattleMenuButtonFlag_PUK2();
							// ボタン凹ませる
							BattleMenuButtonFlag[ 8 ] = 1;
							// バックアップ
							BattleMenuButtonFlagBak = 0;
							// 当たり判定ボックス消す
							ClearBoxFlag();
							// リバースＯＮ送信
							if ( ( (BC_YOBI *)pActBc[ BattleMyNo ]->pYobi )->pActTrance ){
								nrproto_B_send( sockfd, "R|0" );
							}else nrproto_B_send( sockfd, "R|1" );
							// リバースを使用したことを判别するためのフラグ
							rebirthflg=1;
							// ウィンドウアクション抹杀
							DeathAction( pActBattleWnd );
							// ポインタ初期化
							pActBattleWnd = NULL;
							// 入力济みフラグＯＮ
							BattleMenuInputFlag = TRUE;
							// ウィンドウ关闭音
							play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						}
					}
				}
			}
			// スキルボタンの时
			if( BattleMenuButtonHitDispNo[ 2 ] == HitDispNo && BattleMenuButtonFlag[ 2 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"使用技能。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 2 ] = 1;
					// 当たり判定ボックス消す
					// バックアップ
					BattleMenuButtonFlagBak = 2;
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 战闘スキルウィンドウプロセス番号初期化
					BattleSkillWndProcNo = 0;
				}
			}
			// アイテムボタンの时
			if( BattleMenuButtonHitDispNo[ 3 ] == HitDispNo && BattleMenuButtonFlag[ 3 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"使用物品。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 3 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 3;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 战闘アイテムウィンドウプロセス番号初期化
					BattleItemWndProcNo = 0;
				}
			}
			// ペットボタンの时
			if( BattleMenuButtonHitDispNo[ 4 ] == HitDispNo && BattleMenuButtonFlag[ 4 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"召唤、召还宠物。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 4 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 4;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
				}
			}
			// 武器ボタンの时
			if( BattleMenuButtonHitDispNo[ 5 ] == HitDispNo && BattleMenuButtonFlag[ 5 ] == 0 ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"变更武器。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 5 ] = 1;
					// バックアップ
					BattleMenuButtonFlagBak = 5;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
				}
			}
			// ポジションチェンジボタンの时
			if( BattleMenuButtonHitDispNo[ 6 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"变更位置。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹む
					BattleMenuButtonFlag[ 6 ] = 1;
					// バックアップ
					//BattleMenuButtonFlagBak = -1;
					BattleMenuButtonFlagBak = 6;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 防御送信
					nrproto_B_send( sockfd, "P" );
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
			// 逃げるボタンの时
			if( BattleMenuButtonHitDispNo[ 7 ] == HitDispNo ){
				// 一行インフォ
				strcpy( OneLineInfoStr,"逃跑。");
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 战闘ボタンのフラグ初期化
					InitBattleMenuButtonFlag_PUK2();
					// ボタン凹ませる
					BattleMenuButtonFlag[ 7 ] = 1;
					// バックアップ
					//BattleMenuButtonFlagBak = -1;
					BattleMenuButtonFlagBak = 0;
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 逃げる送信
					nrproto_B_send( sockfd, "E" );
					// ウィンドウアクション抹杀
					DeathAction( pActBattleWnd );
					// ポインタ初期化
					pActBattleWnd = NULL;
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
***/
			// ヒットチェック **************************************************
#ifdef PUK2_NEW_MENU
			if( TargetSelOK && ( targetNo = CheckBattelTarget() ) != -1 ){
#else
			if( ( targetNo = CheckBattelTarget() ) != -1 ){
#endif
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 攻击ボタンのとき
					if( BattleMenuButtonFlagBak ==  0 ){
						// 文字列作成
						sprintf( moji, "H|%X", targetNo );                              //MLHIDE
						// 通常攻击
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					else
					// スキルボタンのとき
					if( BattleMenuButtonFlagBak ==  2 ){
						// 文字列作成
						sprintf( moji, "S|%X|%X|%X", BattleSelectSkillNo, BattleSelectAbilityNo, targetNo ); //MLHIDE
						// スキル攻击送信
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
					else
					// アイテムボタンの时
					if( BattleMenuButtonFlagBak ==  3 ){
						// 文字列作成
						sprintf( moji, "I|%X|%X", BattleSelectItemNo, targetNo );       //MLHIDE
						// 	アイテム使用を送信
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}

			// 入力されたら
			if( BattleMenuInputFlag == TRUE ){
				// プレイヤーコマンド入力フラグＯＮ
				BattleCmdPlayerInputFlag++;
				// 次のプロセスへ
				BattleMenuProcNo = B_MENU_PLAYER_BACK;
				// 当たり判定ボックス消す
				ClearBoxFlag();
#ifdef PUK2_NEW_MENU
				// ウィンドウ破弃
				DisappBattleWindow();
#endif
			}

#ifdef PUK2_NEW_MENU
			if ( !TargetSelOK ) TargetSelOK++;
#endif

			// プレイヤーメニュー出现处理
//			if( battleMenuA > 0 ){
//				battleMenuY += battleMenuA;
//				battleMenuA -= 1;
//			}
			break;

		case B_MENU_PLAYER_BACK:	// プレイヤーメニュー消える处理

			// プレイヤーメニュー消える处理
//			if( battleMenuA <= 16 ){
//				battleMenuA += 1;
//				battleMenuY -= battleMenuA;
//			}else{
			if ( ( (struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE] )->procNo == BWMPROC_CLOSE ){
				// 入力济みフラグＯＦＦ
				BattleMenuInputFlag = FALSE;

				// １アクション入力している时
				if( BattleCmdPlayerInputFlag == TRUE ){
					// ２アクションのとき
					if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){
						// 次のプロセスへ
						BattleMenuProcNo = B_MENU_PLAYER_INIT;

						// １アクション目のボタン记忆
						BattleMenuButtonFlagBak1 = BattleMenuButtonFlagBak;
						// ポジションチェンジの时だけ、攻击にする
						if( BattleMenuButtonFlagBak1 == 6 ) BattleMenuButtonFlagBak1 = 0;

						// ２アクション时の使用できないボタンの决定
						if( BattleMenuButtonFlagBak >= 2 && BattleMenuButtonFlagBak <= 6 ){
							// 使えないボタンフラグＯＮ
							BattleUnusableButtonFlag = TRUE;
							// 攻击ボタンを凹ます
							BattleMenuButtonFlagBak2 = 0;
						}
					}else{
						// １アクション目のボタン记忆
						BattleMenuButtonFlagBak1 = BattleMenuButtonFlagBak;
						// ポジションチェンジの时だけ、攻击にする
						if( BattleMenuButtonFlagBak1 == 6 ) BattleMenuButtonFlagBak1 = 0;
						// 次のプロセスへ
						BattleMenuProcNo = B_MENU_PET_INIT;
					}
					// リバースのとき
					if( BattleMenuButtonFlagBak1 == 8 ){
						// リバースを使用したことを判别するためのフラグ
						rebirthflg=1;
					}
				}else{
					// ２アクション目の时
					BattleMenuButtonFlagBak2 = BattleMenuButtonFlagBak;
					// ポジションチェンジの时だけ、攻击にする
					if( BattleMenuButtonFlagBak2 == 6 ) BattleMenuButtonFlagBak2 = 0;
					// 次のプロセスへ
					//BattleMenuProcNo = B_MENU_PET_INIT;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
				}
			}
			break;

		case B_MENU_PET_INIT:	// ペットメニュー初期化

			// ペットが参加しているとき
			if( BattleBpFlag & BP_FLAG_PET ){
				// メニュー无しの时、または観战の时
				if( BattleBpFlag & BP_FLAG_PET_MENU_NON ){
					// 何もしない送信
					if(  BattleMyNo < BC_MAX ) nrproto_B_send( sockfd, "W|FF" );     //MLHIDE
					// ペットコマンド入力フラグＯＮ
					BattleCmdPetInputFlag++;
					// 次のプロセスへ
					SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
				}else{
#ifdef PUK3_ACTION_CHECKRANGE
					CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
					// スキル番号バックアップがあるときかつ、そのスキルが使えるとき（忘却チェック）かつ、魔力切れじゃないとき
					if( BattlePetSkillSelectNo != -1
						&& BattlePetUsableSkillFlag & ( 1 << BattlePetSkillSelectNo ) 
						&& !( pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].fp > pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp )
						){
						// 谁も选择できないとき
						if( BattleSetTargetBox( BattleCheckPetId( BattleMyNo ), pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].target ) == FALSE ){
							// バックアップがないときは选择しなおし
							BattleMenuPetButtonFlag = 1;
#ifdef PUK2_NEW_MENU
							BattlePetSkillSelectNo = -1;
							openBattlePetSkillWindow(BattlePetSkillReturn);
#endif
						}
					}else{
						// バックアップがないときは选择しなおし
						BattleMenuPetButtonFlag = 1;
#ifdef PUK2_NEW_MENU
						BattlePetSkillSelectNo = -1;
						openBattlePetSkillWindow(BattlePetSkillReturn);
#endif
					}
					// 战闘ペットベースウィンドウの当たり判定番号初期化
					BattleMenuPetBaseHitFontNo = -2;
					// 次のプロセスへ
					BattleMenuProcNo = B_MENU_PET;
#ifdef PUK2_NEW_MENU
					// ウィンドウ生成
					AppearBattleWindow( BWMPROC_Monster, DEFBATTLEWINPOSX, DEFBATTLEWINPOSY );
#endif
					// ウィンドウ开く音
					play_se( SE_NO_OPEN_WINDOW, 320, 240 );
				}
			}else{
				// 次のプロセスへ
				SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
			}

			break;

		case B_MENU_PET:	// ペットメニュー

#ifdef PUK2_NEW_MENU
			if ( BattleMenuPetButtonFlag == 3 ){
				// ヒットチェック
				if( ( targetNo = CheckBattelTarget() ) != -1 ){
					// 右键された时
					if( mouse.onceState & MOUSE_LEFT_CRICK ){
						// 文字列作成
						sprintf( moji, "W|%X|%X", BattlePetSkillSelectNo, targetNo );   //MLHIDE
						// 通常攻击
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}
			}
			if ( BattleMenuPetButtonFlag == 2 ) BattleMenuPetButtonFlag++;
#else
			// ヒットチェック
			if( ( targetNo = CheckBattelTarget() ) != -1 ){
				// 右键された时
				if( mouse.onceState & MOUSE_LEFT_CRICK ){
					// 文字列作成
					sprintf( moji, "W|%X|%X", BattlePetSkillSelectNo, targetNo );    //MLHIDE
					// 通常攻击
					nrproto_B_send( sockfd, moji );
					// 当たり判定ボックス消す
					ClearBoxFlag();
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}
#endif
			// ペットスキルウィンドウの状态で分岐
			switch( BattleMenuPetButtonFlag ){

				case 0:

					// 右クリックされた时
					if( mouse.onceState & MOUSE_RIGHT_CRICK ){
						// ボタンフラグ变更
						BattleMenuPetButtonFlag = 1;
#ifdef PUK2_NEW_MENU
	#ifdef PUK3_RIDE_BATTLE
						if ( BattleRidePetNo >= 0 ){
							openBattlePetRideSkillWindow( BattlePetRideSkillReturn );
						}else{
							openBattlePetSkillWindow( BattlePetSkillReturn );
						}
	#else
						openBattlePetSkillWindow(BattlePetSkillReturn);
	#endif
#endif
					}

					// キャンセルボタン押した时
					if( BattleMenuPetBaseHitFontNo == HitDispNo ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(180, "打开技能列表窗口。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// ボタンフラグ变更
							BattleMenuPetButtonFlag = 1;
						}
					}

					// スキルリスト凸ボタン表示
					BattleMenuPetBaseHitFontNo = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_BUTTON_SKILL_LIST_UP, 2 );

				break;

				case 1:

					// ペットスキルウィンドウ表示处理
////					BattlePetSkillWnd();

					// キャンセルボタン押した时
					if( BattleMenuPetBaseHitFontNo == HitDispNo ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(181, "关闭技能列表窗口。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// スキルウィンドウ破弃
							if (WindowFlag[MENU_WINDOW_SKILL].wininfo){
								WindowFlag[MENU_WINDOW_SKILL].wininfo->flag |= WIN_INFO_DEL;
							}
//							// ウィンドウアクション抹杀
//							DeathAction( pActBattleWnd );
//							// ポインタ初期化
//							pActBattleWnd = NULL;
							// ボタンもどす
							BattleMenuPetButtonFlag = 0;
							// ウィンドウ关闭音
							play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						}
					}

					// スキルリスト凹ボタン表示
					BattleMenuPetBaseHitFontNo = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_BUTTON_SKILL_LIST_DOWN, 2 );

				break;

#ifdef PUK2_NEW_MENU
				case 2:case 3:
#else
				case 2:
#endif

					// 右クリックされた时
					if( mouse.onceState & MOUSE_RIGHT_CRICK ){
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// ボタンフラグ变更
						BattleMenuPetButtonFlag = 1;
						// ペットスキルバックアップ初期化
						BattlePetSkillSelectNo = -1;
#ifdef PUK2_NEW_MENU
	#ifdef PUK3_RIDE_BATTLE
						if ( BattleRidePetNo >= 0 ){
							openBattlePetRideSkillWindow( BattlePetRideSkillReturn );
						}else{
							openBattlePetSkillWindow( BattlePetSkillReturn );
						}
	#else
						openBattlePetSkillWindow(BattlePetSkillReturn);
	#endif
#endif
					}
					// キャンセルボタン押した时
					if( BattleMenuPetBaseHitFontNo == HitDispNo ){
						// 一行インフォ
						strcpy( OneLineInfoStr,ML_STRING(182, "回到技能列表窗口。"));
						// 右键された时
						if( mouse.onceState & MOUSE_LEFT_CRICK ){
							// 当たり判定ボックス消す
							ClearBoxFlag();
							// ボタンフラグ变更
							BattleMenuPetButtonFlag = 1;
							// ペットスキルバックアップ初期化
							BattlePetSkillSelectNo = -1;
						}
					}
					// ペットスキルバックアップがあるとき
					if( BattlePetSkillSelectNo != -1 ){
						// ペットスキル名表示
						//sprintf( moji, "%-16s", "０１２３４５６７８" );
						// 文字列作成。
						sprintf( moji,"%-16s",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].name ); //MLHIDE
						//sprintf( moji, "%-16s", "攻击" );
						//sprintf( moji, "9999" );
						StockFontBuffer( battleMenuX - 114, battleMenuY - 15, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, moji, 0 );
						// フォースポイント表示
						sprintf( moji,"%4d",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].fp ); //MLHIDE
						//sprintf( moji, "9999" );
						StockFontBuffer( battleMenuX + 90, battleMenuY - 12, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
					}
					// キャンセル凸ボタン表示
					BattleMenuPetBaseHitFontNo = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_M_BUTTON_CANCEL_UP, 2 );

				break;
			}

			// 入力されたら
			if( BattleMenuInputFlag == TRUE ){
				// ペットコマンド入力フラグＯＮ
				BattleCmdPetInputFlag++;
				// 次のプロセスへ
				BattleMenuProcNo = B_MENU_PET_BACK;
				// このターンのペット番号を学习
				BattlePetNoBak = CheckBattlePet();
#ifdef PUK2_NEW_MENU
				// ウィンドウ破弃
				DisappBattleWindow();
#endif
			}

#ifdef PUK3_RIDE_BATTLE
#else
#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
			// フォースポイント表示
			sprintf( moji, "%4d/%4d", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp ); //MLHIDE
			StockFontBuffer( battleMenuX - 146 + 121, battleMenuY - 42 + 53, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
			// メニュー画像
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_MONSTER_BASE, 1 );
			// ボタン
			//BattleMenuButtonHitDispNo[ 0 ] = StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_IME3, CG_B_BUTTON_ATTACK_UP	+ BattleMenuButtonFlag[ 0 ], 2 );
#endif

			// ペットメニュー出现处理
//			if( battleMenuA > 0 ){
//				battleMenuY += battleMenuA;
//				battleMenuA -= 1;
//			}

			break;

		case B_MENU_PET_BACK:	// ペットメニュー消える处理

			// ペットメニュー消える处理
//			if( battleMenuA <= 16 ){
//				battleMenuA += 1;
//				battleMenuY -= battleMenuA;
//			}else{
			if ( ( (struct BATTLEWINDOWMASTER *)&WindowFlag[MENU_WINDOW_BATTLE] )->procNo == BWMPROC_CLOSE ){
#ifdef PUK3_RIDE_BATTLE
#ifdef PUK3_ACTION_CHECKRANGE
				CheckAction( pActBc[BattleMyNo] );
#endif
				// ウィンドウ生成
				if ( ( (BC_YOBI *)pActBc[BattleMyNo]->pYobi )->pActRideChar ){
					// 次のプロセスへ
					if ( BattleCmdPetInputFlag < 2 ){
						// 入力济みフラグＯＦＦ
						BattleMenuInputFlag = FALSE;

						BattleMenuProcNo = B_MENU_PLAYER_INIT;
					}else{
						SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
					}
				}else
#endif
				// 次のプロセスへ
				SubProcNo = BATTLE_PROC_RECV_MOVIE_DATA;
			}

#ifdef PUK3_RIDE_BATTLE
#else
			// 文字列作成。
			sprintf( moji,"%-16s",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].name ); //MLHIDE
			StockFontBuffer( battleMenuX - 114, battleMenuY - 15, FONT_PRIO_FRONT, FONT_KIND_MIDDLE, FONT_PAL_WHITE, moji, 0 );
			// フォースポイント表示
			sprintf( moji,"%4d",pet[ CheckBattlePet() ].tech[ BattlePetSkillSelectNo ].fp ); //MLHIDE
			StockFontBuffer( battleMenuX + 90, battleMenuY - 12, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );

#ifdef PUK3_ACTION_CHECKRANGE
			CheckAction( pActBc[ BattleCheckPetId( BattleMyNo ) ] );
#endif
			// フォースポイント表示
			sprintf( moji, "%4d/%4d", pActBc[ BattleCheckPetId( BattleMyNo ) ]->fp, pActBc[ BattleCheckPetId( BattleMyNo ) ]->maxFp ); //MLHIDE
			StockFontBuffer( battleMenuX - 146 + 121, battleMenuY - 42 + 53, FONT_PRIO_FRONT, FONT_KIND_SMALL, FONT_PAL_WHITE, moji, 0 );
			// キャンセル凸ボタン表示
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_M_BUTTON_CANCEL_UP, 2 );
			// メニュー画像
			StockDispBuffer( battleMenuX, battleMenuY, DISP_PRIO_MENU, CG_B_MONSTER_BASE, 1 );
#endif

			break;
	}

	// いずれかのメニューがあるとき
	if( !( BattleBpFlag & BP_FLAG_PLAYER_MENU_NON )
		|| !( BattleBpFlag & BP_FLAG_PET_MENU_NON )
		|| !( BattleBpFlag & BP_FLAG_PLAYER_MENU2_NON ) ){

		// カウントダウン表示とリミットチェック
		BattleCntDownDisp_PUK2();
	}
}

#ifdef PUK2_NEW_MENU

// 耐久力メーター表示 ***********************************************************/
void HpMeterDisp( int no )
{
#ifdef PUK3_PACTBC_CHECKRANGE
	CheckIdRange( no );
#endif
#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBc[ no ] );
#endif
	// 存在しない又は死んでる时、返回
	if( pActBc[ no ]->func == NULL || pActBc[ no ]->hp <= 0 ) return;
	// 旅行中の时
	if( pActBc[ no ]->atr & ACT_ATR_TRAVEL ) return;

	// キャラの状态を表示
	CharaStatusDisp( pActBc[ no ], -10, 3 );
}

#endif


//====================================//
//				観战				  //
//====================================//

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

BOOL MenuWindowWatchBf( int mouse )
{
	return TRUE;
}

BOOL MenuWindowWatchAf( int mouse )
{
	displayMenuWindow();

	MenuWindowCommonDraw( GID_InfoWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

//--------------------------------------------------------
// スイッチ处理
//--------------------------------------------------------

BOOL MenuSwitchWatchEnd( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER){
		StockBoxDispBuffer( wI->wx+wI->sw[no].ofx-2, wI->wy+wI->sw[no].ofy-2,
			wI->wx+wI->sw[no].ofx+wI->sw[no].sx+2, wI->wy+wI->sw[no].ofy+wI->sw[no].sy+2,
			DISP_PRIO_WIN2, BoxColor, 0 );
		strcpy( OneLineInfoStr, MWONELINE_BATTLE_WATCH );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );
		// 观战结束プロトコル送る
		nrproto_B_send( sockfd, "U" );                                      //MLHIDE
		// 終了プロセスへ
		SubProcNo = BATTLE_PROC_OUT_PRODUCE_INIT;

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}









//====================================//
//				リザルト			  //
//====================================//

extern int HealthGraTbl[];

static unsigned int ResultDeathTime;

// ウィンドウ处理 *********************//

// ウィンドウ处理 ++++
BOOL MenuWindowResultBf( int mouse )
{
	if (mouse==WIN_INIT){
		int i, j;
		int index;

		// プレーヤー关系
		for( i = 0; i < RESULT_CHR_INFO; i++ ){
			wI->sw[EnumGraphHealth1+i].Enabled = FALSE;
			wI->sw[EnumGraphName1+i].Enabled = FALSE;
			wI->sw[EnumGraphExp1+i].Enabled = FALSE;
			wI->sw[EnumGraphLvup1+i].Enabled = FALSE;
			if( battleResultMsg.resChrInfo[i].no != -1 ){
				wI->sw[EnumGraphHealth1+i].Enabled = TRUE;
				wI->sw[EnumGraphName1+i].Enabled = TRUE;
				wI->sw[EnumGraphExp1+i].Enabled = TRUE;

				if( battleResultMsg.resChrInfo[i].no == -2 ){
					( (GRAPHIC_SWITCH *)wI->sw[EnumGraphHealth1+i].Switch )->graNo = HealthGraTbl[ getHelthColor( pc.injuryLv ) ];
					strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphName1+i].Switch )->text, pc.name );
				}
				else{
					( (GRAPHIC_SWITCH *)wI->sw[EnumGraphHealth1+i].Switch )->graNo = HealthGraTbl[ getHelthColor( pet[battleResultMsg.resChrInfo[i].no].injuryLv ) ];
					if( pet[battleResultMsg.resChrInfo[i].no].freeName[0] == '\0' ){
						strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphName1+i].Switch )->text, pet[ battleResultMsg.resChrInfo[i].no ].name );
					}else{
						strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphName1+i].Switch )->text, pet[ battleResultMsg.resChrInfo[i].no ].freeName );
					}
				}

				// 等级アップアイコン表示
				if( battleResultMsg.resChrInfo[i].lvUp ) wI->sw[EnumGraphLvup1+i].Enabled = TRUE;

				// ＥＸＰの表示

				// プレイヤーの时
				if( battleResultMsg.resChrInfo[i].no == -2 ){
					// 制限で引っかかっている时
#ifdef PUK2_MAXLEVEL_UP
					if ( pc.nextExp == -1 ){
#else
					if( pc.lv >= MAX_LEVEL ){
#endif
						strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphExp1+i].Switch )->text, "  ---  " ); //MLHIDE
					}else{
						sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphExp1+i].Switch )->text, "%+7d", battleResultMsg.resChrInfo[i].exp ); //MLHIDE
					}
				}else{
					// ＬＶ制限で引っかかっている时
#ifdef PUK2_MAXLEVEL_UP
					if ( pet[ battleResultMsg.resChrInfo[i].no ].nextExp == -1 ){
#else
					if( pet[ battleResultMsg.resChrInfo[i].no ].lv >= MAX_LEVEL ){
#endif
						strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphExp1+i].Switch )->text, "  ---  " ); //MLHIDE
					}else{
						sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphExp1+i].Switch )->text, "%+7d", battleResultMsg.resChrInfo[i].exp ); //MLHIDE
					}
				}
			}
		}

		// アイテム
		for( i = 0; i < RESULT_ITEM_INFO; i++ ){
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillItemName1+i].Switch )->text, battleResultMsg.itemName[i] );
		}

#ifdef PUK2_RESULTSKILLEXP
		// スキル
		for( i = 0, j = 0; i < RESULT_SKILL_INFO; i++ ){
			index = job.sortSkill[i].index;
			if( !job.sortSkill[i].useFlag ) continue;

#ifdef PUK3_RIDE
			// 战闘スキルの时
			if ( job.skill[index].operationCategory == 0 ||
				 job.skill[index].operationCategory == 5 ){
#else
			// 战闘スキルの时
			if( job.skill[index].operationCategory == 0 ){
#endif
#ifdef PUK3_SKILLMAXLVUP
				// 等级アップしてないとき
				if ( !battleResultMsg.resSkillInfo[index].lvUp ){
					// 经验值が0のとき
					if ( battleResultMsg.resSkillInfo[index].exp == 0 ) continue;
				}

				// テックＬＶ制限に引っかかっている时
				if( job.skill[ index ].lv >= job.skill[ index ].maxLv ){
					strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+j].Switch )->text, " --- " ); //MLHIDE
				}else{
					sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+j].Switch )->text, "%+5d", battleResultMsg.resSkillInfo[index].exp ); //MLHIDE
				}
#else
				// テックＬＶ制限に引っかかっている时
				if ( job.skill[ index ].lv >= job.skill[ index ].maxLv ) continue;
				// 经验值が0のとき
				if ( battleResultMsg.resSkillInfo[index].exp == 0 ) continue;

				sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+j].Switch )->text, "%+5d", battleResultMsg.resSkillInfo[index].exp ); //MLHIDE
#endif

				// 名称の表示
				strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+j].Switch )->text, job.skill[index].name );
				( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+j].Switch )->color = FONT_PAL_WHITE;

				( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+j].Switch )->color = FONT_PAL_WHITE;

				// 等级アップアイコン表示
				wI->sw[EnumGraphSkillLvup1+j].Enabled = FALSE;
				if( battleResultMsg.resSkillInfo[index].lvUp ) wI->sw[EnumGraphSkillLvup1+j].Enabled = TRUE;

				j++;
			}
			// クラフトスキルの时表示无し

	#ifdef PUK2_MAXSKILLSLOT_UP
			// 10以上表示できないので
			if ( j >= 10 ) break;
		}
		for(;j<14;j++){
	#else
			if ( j >= 10 ) break;
		}
		for(;j<10;j++){
	#endif
			// 名称の表示
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+j].Switch )->text, "" );

			// ＥＸＰの表示
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+j].Switch )->text, "" );

			// 等级アップアイコン表示
			wI->sw[EnumGraphSkillLvup1+j].Enabled = FALSE;
		}
#else
		// スキル
		for( i = 0, j = 0; i < RESULT_SKILL_INFO; i++ ){
			index = job.sortSkill[i].index;
			if( job.sortSkill[i].useFlag ){
#ifdef PUK3_RIDE
				// 战闘スキルの时
				if ( job.skill[index].operationCategory == 0 ||
					 job.skill[index].operationCategory == 5 ){
#else
				// 战闘スキルの时
				if( job.skill[index].operationCategory == 0 ){
#endif
					// 名称の表示
					strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+i].Switch )->text, job.skill[index].name );
					( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+i].Switch )->color = FONT_PAL_WHITE;

					// テックＬＶ制限に引っかかっている时
					if( job.skill[ index ].lv >= job.skill[ index ].maxLv ){
						strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+i].Switch )->text, " --- " ); //MLHIDE
					}else{
						sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+i].Switch )->text, "%+5d", battleResultMsg.resSkillInfo[index].exp ); //MLHIDE
					}
					( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+i].Switch )->color = FONT_PAL_WHITE;

					// 等级アップアイコン表示
					wI->sw[EnumGraphSkillLvup1+i].Enabled = FALSE;
					if( battleResultMsg.resSkillInfo[index].lvUp ) wI->sw[EnumGraphSkillLvup1+i].Enabled = TRUE;
				}
				// クラフトスキルの时
				else{
					// 名称の表示
					strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+i].Switch )->text, job.skill[index].name );
					( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+i].Switch )->color = FONT_PAL_GRAY;

					// ＥＸＰの表示
					strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+i].Switch )->text, " --- " ); //MLHIDE
					( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+i].Switch )->color = FONT_PAL_GRAY;

					// 等级アップアイコン表示
					wI->sw[EnumGraphSkillLvup1+i].Enabled = FALSE;
				}

	#ifdef PUK2_MAXSKILLSLOT_UP
				j++;
			}
			if ( j >= 14 ) break;
		}
		for(;j<14;j++){
	#else
		#ifdef PUK2
				j++;
			}
			if ( j >= 10 ) break;
		}
		for(;j<10;j++){
		#else
				j++;
			}
		}
		for(;j<RESULT_SKILL_INFO;j++){
		#endif
	#endif
			// 名称の表示
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillName1+j].Switch )->text, "" );

			// ＥＸＰの表示
			strcpy( ( (TEXT_SWITCH *)wI->sw[EnumGraphSkillExp1+j].Switch )->text, "" );

			// 等级アップアイコン表示
			wI->sw[EnumGraphSkillLvup1+j].Enabled = FALSE;
		}
#endif

		ResultDeathTime = GetTickCount() + 5000;
	}

	// ウィンドウを开いて５秒経过したら自动で关闭
	if( ResultDeathTime < GetTickCount() ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	return TRUE;
}

BOOL MenuWindowResultAf( int mouse )
{
	struct BLT_MEMBER bm={0};
	int i;

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	// アイテム
	for( i = 0; i < RESULT_ITEM_INFO; i++ ){
		if (battleResultMsg.itemgraNo[i]>=0){
			StockDispBuffer( wI->wx+39, wI->wy+166+i*49, DISP_PRIO_WIN2, battleResultMsg.itemgraNo[i], 0, &bm );
		}
	}

	displayMenuWindow();

	return TRUE;
}

// 各ボタンの处理 *********************//

// ボタン
BOOL MenuWindowResultOK( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
#ifndef PUK2
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
#endif

	if ( flag & MENU_MOUSE_OVER ) ReturnFlag = TRUE;

	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

#ifndef PUK2
	Graph->graNo = GID_BigOKButtonOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;
#endif

	return ReturnFlag;
}

//====================================//
//			デュエルリザルト		  //
//====================================//

// ウィンドウ处理 *********************//

// ウィンドウ处理 ++++
BOOL MenuWindowDuelResultBf( int mouse )
{
	if (mouse==WIN_INIT){
		ResultDeathTime = GetTickCount() + 5000;
	EnumGraphDuelDp,
	EnumGraphDuelTotal,

		// 取得ＤＰの表示
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphDuelDp].Switch )->text, "DP   %+9d", battleResultMsg.getDp ); //MLHIDE

		// ＤＰの表示
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumGraphDuelTotal].Switch )->text, "Total%9d", battleResultMsg.dp ); //MLHIDE
	}

	// ウィンドウを开いて５秒経过したら自动で关闭
	if( ResultDeathTime < GetTickCount() ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	return TRUE;
}

BOOL MenuWindowDuelResultAf( int mouse )
{
	displayMenuWindow();

	MenuWindowCommonDraw( GID_CommonWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

// 各ボタンの处理 *********************//

// ボタン
BOOL MenuWindowDuelResultOK( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	if( flag & MENU_MOUSE_OVER ) ReturnFlag=TRUE;

	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	Graph->graNo = GID_BigOKButtonOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigOKButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigOKButtonOff;

	return ReturnFlag;
}




//======================================//
// 战闘时アイテムウィンドウ				//
//======================================//

char BtlitemFrameFlag = -1;
static int BtlitemFramePos;
static int BtlitemFrameAcc;

const int BtlitemFramePosMin[]={ -213 };

#define BTLITEMWINSTARTLINE 16

void MoveBtlEquipWindow( int dif );

void (*BtlitemFrameMove[1])( int dif ) = { MoveBtlEquipWindow };

static int BtlitemInfoNo;
static int BtlitemInfoPage;

void MoveBtlEquipWindow( int dif )
{
	int BaseX;

	wI->sw[EnumGraphBtlEquipWindow].ofx = BTLITEMWINSTARTLINE + dif;
	wI->sw[EnumGraphBtlEquipWindow].func = MenuSwitchNone;

	BaseX = wI->sw[EnumGraphBtlEquipWindow].ofx;

	wI->sw[EnumGraphBtlEquipButton].ofx = BaseX;

	wI->sw[EnumActionBtlItemPc].Enabled = FALSE;

	if (dif<=-213){
		wI->sw[EnumActionBtlItemPc].Enabled = TRUE;
		wI->sw[EnumActionBtlItemPc].ofx = BaseX + 116;

		wI->sw[EnumGraphBtlEquipWindow].func = MenuBtlItemSwitchEquipWindow;
	}
}

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowBtlItemBf( int mouse )
{

	if ( mouse == WIN_INIT ){
		BtlitemInfoPage = 0;

		wI->sw[EnumGraphBtlEquipWindow].Enabled = FALSE;
		wI->sw[EnumActionBtlItemPc].Enabled = FALSE;

		if (BtlitemFrameFlag<0){
			BtlitemFramePos = 0;
		}else{
			BtlitemFramePos = BtlitemFramePosMin[BtlitemFrameFlag];
			switch(BtlitemFrameFlag){
			case 0:
				wI->sw[EnumGraphBtlEquipWindow].Enabled = TRUE;
				break;
			}
			BtlitemFrameMove[BtlitemFrameFlag]( BtlitemFramePos );
		}
	}

	BtlitemInfoNo = -1;

	return TRUE;
}

BOOL MenuWindowBtlItemAf( int Mouse )
{
	int x = wI->wx + 45;
	int y = wI->wy + 286;
	int i;

	if ( BtlitemFrameFlag >= 0 ){
		if ( BtlitemFramePos > BtlitemFramePosMin[BtlitemFrameFlag] ){
			BtlitemFramePos -= BtlitemFrameAcc;
			BtlitemFrameAcc--;
			if (BtlitemFramePos<BtlitemFramePosMin[BtlitemFrameFlag]) BtlitemFramePos = BtlitemFramePosMin[BtlitemFrameFlag];
			BtlitemFrameMove[BtlitemFrameFlag]( BtlitemFramePos );
		}
	}

	// アイテム信息
	if (BtlitemInfoNo>=0){
		// アイテム信息
		PcItemExplanationWindow( BtlitemInfoNo, BtlitemInfoPage );
	}else{
		// ドラッグ中
		i = WinDD_CheckObjType();
		if ( i==WINDD_ITEM ){
			// ドラッグ元が自分なら
			if ( WinDD_WinType()==MENU_WINDOW_BTLITEM ){
				// 右クリックしたらドラッグ終了
				if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
			}
		}
		if (i==WINDD_NONE){
			if( mouse.onceState & MOUSE_RIGHT_CRICK ) wI->flag |= WIN_INFO_DEL;
		}
	}

#ifdef PUK3_ACTION_CHECKRANGE
	CheckAction( pActBc[BattleMyNo] );
#endif
	( (ACTION_SWITCH *)wI->sw[EnumActionBtlItemPc].Switch )->ActionAdd->anim_chr_no = pActBc[BattleMyNo]->anim_chr_no;
	( (ACTION_SWITCH *)wI->sw[EnumActionBtlItemPc].Switch )->ActionAdd->anim_ang = 5;

	( (ACTION_SWITCH *)wI->sw[EnumActionBtlItemPc].Switch )->ActionAdd->bltfon = 0;
	if (wI->sw[EnumActionBtlItemPc].Enabled){
		if ( SPR_rt00_ax <= pActBc[BattleMyNo]->anim_chr_no && pActBc[BattleMyNo]->anim_chr_no <= SPR_rk00_sp ){
			( (ACTION_SWITCH *)wI->sw[EnumActionBtlItemPc].Switch )->ActionAdd->bltfon = BLTF_NOCHG;
			( (ACTION_SWITCH *)wI->sw[EnumActionBtlItemPc].Switch )->ActionAdd->bltf = BLTF_NOCHG;
		}
	}

	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuBtlItemSwitchGold( int no, unsigned int flag )
{
	sprintf( BtlItemNum_PCGold, "%10d", pc.gold );                       //MLHIDE
	return FALSE;
}

BOOL MenuBtlItemSwitchEquipButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 重なってるなら一行インフォ
	if ( flag & MENU_MOUSE_OVER ){
		if ( BtlitemFrameFlag == 0 ) strcpy( OneLineInfoStr, MWONELINE_ITEM_EQUIP_ON );
		else strcpy( OneLineInfoStr, MWONELINE_ITEM_EQUIP_OFF );
	}

	if ( flag & MENU_MOUSE_LEFT ){
		// プライオリティを最上位に
		wI->flag |= WIN_INFO_PRIO;

		// クリック音
		play_se( SE_NO_CLICK, 320, 240 );

		wI->sw[EnumGraphBtlEquipWindow].Enabled = !wI->sw[EnumGraphBtlEquipWindow].Enabled;
		wI->sw[EnumActionBtlItemPc].Enabled = FALSE;
		BtlitemFrameFlag = -1;
		BtlitemFramePos = 0;
		wI->sw[EnumGraphBtlEquipButton].ofx = 0;
		wI->sw[EnumGraphBtlEquipWindow].func = MenuSwitchNone;

		if (wI->sw[EnumGraphBtlEquipWindow].Enabled){
			BtlitemFrameFlag = 0;
			BtlitemFrameAcc = 21;
		}
	}

	Graph->graNo = GID_EquipButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_EquipButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_EquipButtonOff;

	return ReturnFlag;
}

BOOL MenuBtlItemSwitchItemWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	char str[10];
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	static int olditemNo = -2;
	char moji[ 256 ];

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		// 装备中の武器ならプロトコル送信
		if( DropitemNo == 2 || DropitemNo == 3 ){
			int handFlag = 0;	// 手にもっている数

			// 右手に持っているかチェック、盾の时はカウントに入れない。
			if( pc.item[ 2 ].useFlag == TRUE && pc.item[ 2 ].kind != 7 ) handFlag++;
			// 左手に持っているかチェック、盾の时はカウントに入れない。
			if( pc.item[ 3 ].useFlag == TRUE && pc.item[ 3 ].kind != 7 ) handFlag++;
			
			// 手にもっているアイテム数とアイテム栏の空きをチェック
			if( CheckItemBlank() >= handFlag ){
				// 何も装备してない时
				if( handFlag == 0 ){
					// 警告文字表示
					StockChatBufferLine( ML_STRING(160, "没有装备武器！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					wI->flag |= WIN_INFO_DEL;
					// ボタンもどす
					BattleMenuButtonFlag[ 5 ] = 0;
					// 文字列作成
					sprintf( moji, "Q|%X|", 255 );                                   //MLHIDE
					// 	装备变更を送信
					nrproto_B_send( sockfd, moji );
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// ウィンドウ关闭音
					play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
				}
			}else{
				// 警告文字表示
				StockChatBufferLine( ML_STRING(161, "物品栏没有空位了！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	// アイテム栏のカーソルが当っている位置を检索
	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
			if ( ItemNoOpe[i+MAX_EQUIP_ITEM] ) continue;
			x = wI->wx + wI->sw[no].ofx + 15 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 48 + ( (i/ITEM_DRAW_COLUMN) * 50 );
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}
	}

	// 一行インフォ表示
	if (itemNo>=0){
		if (pc.item[itemNo+MAX_EQUIP_ITEM].useFlag){
			if (pc.item[itemNo+MAX_EQUIP_ITEM].kind<ITEM_EQUIPCATEGORYNUM){
				if( pc.item[itemNo+MAX_EQUIP_ITEM].kind >= 0 && pc.item[itemNo+MAX_EQUIP_ITEM].kind <= 7 ){
					if ( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag ) strcpy( OneLineInfoStr, MWONELINE_ITEM_ABLEEQUIP );
					else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEEQUIP);
				}else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEEQUIP );
			}else if( pc.item[itemNo+MAX_EQUIP_ITEM].battle & ITEM_FLAG_USEABLE ){
				// 未鉴定のアイテム
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag ) strcpy( OneLineInfoStr, MWONELINE_ITEM_ABLEITEM );
				else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEITEM );
			}else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEITEM );
		}
	}

	// カーソル位置が变わっていたらページ数を最初に戾す
	if ( olditemNo != itemNo ) BtlitemInfoPage = 0;
	olditemNo = itemNo;

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分以外のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_BTLITEM || WinDD_WinType()==MENU_WINDOW_NONE ){
			// その栏にアイテムがある、かつ、武器または盾の时
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag == TRUE &&
				 pc.item[itemNo+MAX_EQUIP_ITEM].kind >= 0 && pc.item[itemNo+MAX_EQUIP_ITEM].kind <= 7 ){ 
				int handFlag = 0;	// 手にもっている数
			
				// 右手に持っているかチェック
				if( pc.item[ 2 ].useFlag == TRUE ) handFlag++;
				// 左手に持っているかチェック
				if( pc.item[ 3 ].useFlag == TRUE ) handFlag++;
				
				// 手にもっているアイテム数とアイテム栏の空きをチェック
				if( handFlag == 2 && CheckItemBlank() == 0 && pc.item[ i ].battle & ITEM_FLAG_TWO_HAND ){
					// 警告文字表示
					StockChatBufferLine( ML_STRING(161, "物品栏没有空位了！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else
				// 鉴定してるかチェック
				if( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag == FALSE ){
					// 警告文字表示
					StockChatBufferLine( ML_STRING(164, "无法装备未鉴定物品！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					wI->flag |= WIN_INFO_DEL;
					// ボタンもどす
					BattleMenuButtonFlag[ 5 ] = 0;
					// 文字列作成
					sprintf( moji, "Q|%X|", itemNo+MAX_EQUIP_ITEM );                 //MLHIDE
					// 	装备变更を送信
					nrproto_B_send( sockfd, moji );
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// アイテム使用音
					play_se( SE_NO_USE_ITEM, 320, 240 );
				}
			}else
			// その栏にアイテムがあたっら
			if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag == TRUE &&
				 pc.item[itemNo+MAX_EQUIP_ITEM].battle & ITEM_FLAG_USEABLE ){
				// 鉴定してるかチェック
				if( pc.item[itemNo+MAX_EQUIP_ITEM].checkFlag == FALSE ){
					// 警告文字表示
					StockChatBufferLine( ML_STRING(157, "无法使用未鉴定物品！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					wI->flag |= WIN_INFO_DEL;
					
					// ターゲットボックス作成
					if ( BattleSetTargetBox( BattleMyNo, pc.item[itemNo+MAX_EQUIP_ITEM].target ) ){
						// 选择アイテム番号记忆
						BattleSelectItemNo = itemNo+MAX_EQUIP_ITEM;
						// アイテム使用音
						play_se( SE_NO_USE_ITEM, 320, 240 );

						// ターゲット选择へ
						BattleItemWndProcNo = 1;
						ItemTargetSelect = 1;
					}else{
						// 选择アイテム番号记忆
						BattleSelectItemNo = itemNo+MAX_EQUIP_ITEM;
						// 文字列作成
						sprintf( moji, "I|%X|%X", BattleSelectItemNo, BattleMyNo );     //MLHIDE
						// 	アイテム使用を送信
						nrproto_B_send( sockfd, moji );
						// 当たり判定ボックス消す
						ClearBoxFlag();
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
						// アイテム使用音
						play_se( SE_NO_USE_ITEM, 320, 240 );
					}
				}
			}else{
				// ＮＧ音
				play_se( SE_NO_NG, 320, 240 );
			}
			// ドラッグ終了
			WinDD_DragFinish();
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					// 使えない装备できないアイテムの时
					if ( !( pc.item[itemNo+MAX_EQUIP_ITEM].battle & ITEM_FLAG_USEABLE ) &&
						 !( pc.item[itemNo+MAX_EQUIP_ITEM].kind >= 0 && pc.item[itemNo+MAX_EQUIP_ITEM].kind <= 7 ) );
					else{
						// ドラッグ开始
#ifdef PUK2_NEWDRAG
						DragItem( itemNo+MAX_EQUIP_ITEM, FALSE );
#else
						WinDD_DragStart( WINDD_ITEM, (void *)(itemNo+MAX_EQUIP_ITEM) );
#endif
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
					}
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo+MAX_EQUIP_ITEM].useFlag ){
					BtlitemInfoPage++;
					if( BtlitemInfoPage >= pc.item[itemNo+MAX_EQUIP_ITEM].memoPage ) BtlitemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
#ifdef PUK2_NEWDRAG
#else
	// ドラッグ中
	else if ( WinDD_CheckObjType()==WINDD_ITEM ){
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_BTLITEM ){
			// 右键したらアイテムドロップ
			if ( mouse.onceState & MOUSE_LEFT_CRICK ){
				DragitemNo = (int)WinDD_ObjData();
				WinDD_DragFinish();
				WinDD_DropObject( WINDD_ITEM, (void *)(DragitemNo), NULL, mouse.nowPoint.x, mouse.nowPoint.y );
			}
//			MenuWindowBtlItemAf() 内で、まとめて处理するのでここでは处理しない
//			// 右クリックしたらドラッグ終了
//			if ( mouse.onceState & MOUSE_RIGHT_CRICK ) WinDD_DragFinish();
		}
	}
#endif

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

#ifdef PUK2_NEWDRAG
#else
		// ドラッグ元が自分なら
		if ( WinDD_WinType()==MENU_WINDOW_BTLITEM ){
			// 掴んだアイテムの表示
			StockDispBuffer( mouse.nowPoint.x, mouse.nowPoint.y, DISP_PRIO_DRAG, pc.item[DragitemNo].graNo, 0, &bm2 );
		}
#endif

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo >= 8 ){
			x = wI->wx + wI->sw[no].ofx + 15 + ( ( (DragitemNo-MAX_EQUIP_ITEM)%ITEM_DRAW_COLUMN ) * 50 );
			y = wI->wy + wI->sw[no].ofy + 48 + ( ( (DragitemNo-MAX_EQUIP_ITEM)/ITEM_DRAW_COLUMN ) * 50 );
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	// アイテム选择枠
	if( flag & MENU_MOUSE_OVER ){
		if( itemNo >= 0 ){
			x = wI->wx + wI->sw[no].ofx + 15 + ( (itemNo%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 48 + ( (itemNo/ITEM_DRAW_COLUMN) * 50 );
			StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

			// アイテム说明をしてもらうため登録
			if (pc.item[itemNo+8].useFlag) BtlitemInfoNo = itemNo + MAX_EQUIP_ITEM;
		}
	}
#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		DragitemNo = (int)WinDD_ObjData();
		if (DragitemNo<MAX_EQUIP_ITEM){
			StockBoxDispBuffer(
				wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
				wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
				DISP_PRIO_WIN2, BoxColor, 0 );
		}
	}
#else
	if( flag & MENU_MOUSE_DRAGOVER ){
		DragitemNo = (int)WinDD_ObjData();
		if (DragitemNo<MAX_EQUIP_ITEM){
			StockBoxDispBuffer(
				wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
				wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
				DISP_PRIO_WIN2, BoxColor, 0 );
		}
	}
#endif

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);
	// プライオリティの制御
	StockFontBuffer( 0, 0, FONT_PRIO_WIN, FONT_KIND_SMALL, FONT_PAL_WHITE, "", 0, 0 );
	// アイテムの表示
	for(i=0;i<MAX_DRAW_WIN_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i+MAX_EQUIP_ITEM].useFlag ){
			x = wI->wx + wI->sw[no].ofx + 39 + ( (i%ITEM_DRAW_COLUMN) * 50 );
			y = wI->wy + wI->sw[no].ofy + 73 + ( (i/ITEM_DRAW_COLUMN) * 50 );

			// 使えないアイテムの时
			if( !( pc.item[i+MAX_EQUIP_ITEM].battle & ITEM_FLAG_USEABLE ) ){ 
				// 装备できないアイテムの时
				if( !( pc.item[i+MAX_EQUIP_ITEM].kind >= 0 && pc.item[i+MAX_EQUIP_ITEM].kind <= 7 ) ){
					StockDispBuffer( x+105, y+93, DISP_PRIO_WIN2, CG_B_UNUSE_ITEM_NET, 0 );
				}
			}
			// 未鉴定のアイテム
			if ( !pc.item[i+MAX_EQUIP_ITEM].checkFlag ) StockDispBuffer( x+105, y+93, DISP_PRIO_WIN2, CG_B_UNUSE_ITEM_NET, 0 );

			if (ItemNoOpe[i+MAX_EQUIP_ITEM]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i+MAX_EQUIP_ITEM].graNo, 0, &bm );

			// 个数表示
			if( pc.item[i+MAX_EQUIP_ITEM].num > 0 ){
				sprintf( str, "%3d", pc.item[i+MAX_EQUIP_ITEM].num );             //MLHIDE
				StockFontBuffer( x-3, y+7, FONT_PRIO_WIN, FONT_KIND_SMALL, ITEMSTACKCOLOR, str, 0, 0 );
			}
		}
	}

	return ReturnFlag;
}

const short BtlEquipPos[MAX_EQUIP_ITEM][2]={
	{115, 30 }, { 51, 130}, { 51, 80 }, {180, 80 }, {180, 130}, { 51, 30 }, {180, 30 }, {180, 180}
};
BOOL MenuBtlItemSwitchEquipWindow( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	BLT_MEMBER bm={0};
	BLT_MEMBER bm2={0};
	int i, x, y;
	int itemNo, DropitemNo, DragitemNo;
	char moji[ 256 ];

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	bm2.rgba.rgba = 0x80ffffff;
	bm2.bltf = BLTF_NOCHG;

	// ドラッグしてるもの、ドロップされたものの确认
	if ( flag&(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP) ){
		if ( WinDD_CheckObjType() != WINDD_ITEM ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}else
		// 银行からのはだめ
		if ( (int)WinDD_ObjData() >= 100 ){
			flag &= ~(MENU_MOUSE_DRAGOVER|MENU_MOUSE_DROP);
#ifdef PUK2_NEWDRAG
			ReturnFlag = TRUE;
#else
			WinDD_GetObject();
#endif
		}
	}

	// アイテムがドロップされたら
	if ( flag & MENU_MOUSE_DROP ){
#ifdef PUK2_NEWDRAG
		DropitemNo = (int)WinDD_ObjData();
#else
		DropitemNo = (int)WinDD_GetObject();
#endif

		if (DropitemNo>=MAX_EQUIP_ITEM){
			// その栏にアイテムがある、かつ、武器または盾の时
			if( pc.item[DropitemNo].useFlag == TRUE && pc.item[DropitemNo].kind >= 0 && pc.item[DropitemNo].kind <= 7 ){ 
				int handFlag = 0;	// 手にもっている数
			
				// 右手に持っているかチェック
				if( pc.item[ 2 ].useFlag == TRUE ) handFlag++;
				// 左手に持っているかチェック
				if( pc.item[ 3 ].useFlag == TRUE ) handFlag++;
				
				// 手にもっているアイテム数とアイテム栏の空きをチェック
				if( handFlag == 2 && CheckItemBlank() == 0 && pc.item[DropitemNo].battle & ITEM_FLAG_TWO_HAND ){
					// 警告文字表示
					StockChatBufferLine( ML_STRING(161, "物品栏没有空位了！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else
				// 鉴定してるかチェック
				if( pc.item[DropitemNo].checkFlag == FALSE ){
					// 警告文字表示
					StockChatBufferLine( ML_STRING(164, "无法装备未鉴定物品！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}else{
					wI->flag |= WIN_INFO_DEL;
					// ボタンもどす
					BattleMenuButtonFlag[ 5 ] = 0;
					// 文字列作成
					sprintf( moji, "Q|%X|", DropitemNo );                            //MLHIDE
					// 	装备变更を送信
					nrproto_B_send( sockfd, moji );
					// 入力济みフラグＯＮ
					BattleMenuInputFlag = TRUE;
					// アイテム使用音
					play_se( SE_NO_USE_ITEM, 320, 240 );
				}
			}
		}
#ifdef PUK2_NEWDRAG
		WinDD_AcceptObject();
#endif
	}

	itemNo = -1;
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		for(i=0;i<MAX_EQUIP_ITEM;i++){
			if ( ItemNoOpe[i] ) continue;
			x = wI->wx + wI->sw[no].ofx + BtlEquipPos[i][0] - 24;
			y = wI->wy + wI->sw[no].ofy + BtlEquipPos[i][1] - 24;
			if ( MakeHitBox( x, y, x+48, y+48, -1 ) ){ itemNo = i;	break; }
		}

		ReturnFlag = TRUE;
	}

	// プライオリティを最上位に
	if ( flag & MENU_MOUSE_LEFT ) wI->flag |= WIN_INFO_PRIO;

	// 一行インフォ表示
	if (itemNo>=0){
		if (pc.item[itemNo].useFlag){
			if (pc.item[itemNo].kind<ITEM_EQUIPCATEGORYNUM){
				if( pc.item[itemNo].kind >= 0 && pc.item[itemNo].kind <= 7 ){
					if ( pc.item[itemNo].checkFlag ) strcpy( OneLineInfoStr, MWONELINE_ITEM_ABLEEQUIP );
					else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEEQUIP);
				}else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEEQUIP );
			}else if( pc.item[itemNo].battle & ITEM_FLAG_USEABLE ){
				// 未鉴定のアイテム
				if ( pc.item[itemNo].checkFlag ) strcpy( OneLineInfoStr, MWONELINE_ITEM_ABLEITEM );
				else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEITEM );
			}else strcpy( OneLineInfoStr, MWONELINE_ITEM_UNABLEITEM );
		}
	}

	// アイテム栏を左ダブルクリックしたとき
	if ( ( itemNo >= 0 ) && (pc.item[itemNo].useFlag) && (mouse.onceState&MOUSE_LEFT_DBL_CRICK) ){
		// 自分以外のウィンドウがドラッグ元の时
		if ( WinDD_WinType()==MENU_WINDOW_ITEM || WinDD_WinType()==MENU_WINDOW_NONE ){
			// 右手、左手栏の上にあるとき
			if ( itemNo == 2 || itemNo == 3 ){
				int handFlag = 0;	// 手にもっている数

				// 右手に持っているかチェック、盾の时はカウントに入れない。
				if( pc.item[ 2 ].useFlag == TRUE && pc.item[ 2 ].kind != 7 ) handFlag++;
				// 左手に持っているかチェック、盾の时はカウントに入れない。
				if( pc.item[ 3 ].useFlag == TRUE && pc.item[ 3 ].kind != 7 ) handFlag++;
				
				// 手にもっているアイテム数とアイテム栏の空きをチェック
				if( CheckItemBlank() >= handFlag ){
					// 何も装备してない时
					if( handFlag == 0 ){
						// 警告文字表示
						StockChatBufferLine( ML_STRING(160, "没有装备武器！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
						// ＮＧ音
						play_se( SE_NO_NG, 320, 240 );
					}else{
						wI->flag |= WIN_INFO_DEL;
						// ボタンもどす
						BattleMenuButtonFlag[ 5 ] = 0;
						// 文字列作成
						sprintf( moji, "Q|%X|", 255 );                                  //MLHIDE
						// 	装备变更を送信
						nrproto_B_send( sockfd, moji );
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;
						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}
				}else{
					// 警告文字表示
					StockChatBufferLine( ML_STRING(161, "物品栏没有空位了！"), FONT_PAL_YELLOW, FONT_KIND_MIDDLE2 );
					// ＮＧ音
					play_se( SE_NO_NG, 320, 240 );
				}
			}
			// ドラッグ終了
			WinDD_DragFinish();
		}
		ReturnFlag=TRUE;
	}
	// 通常时
	else if ( flag & MENU_MOUSE_OVER ){
		// アイテム栏の上にあるとき
		if ( itemNo >= 0 ){
			// 右键したとき
			if( flag & MENU_MOUSE_LEFT ){
				// 右手、左手栏の上にあるとき
				if ( itemNo == 2 || itemNo == 3 ){
					// その场所にアイテムが盾でないなら
					if ( ( pc.item[itemNo].useFlag ) && ( pc.item[i].kind != 7 ) ){
						// ドラッグ开始
#ifdef PUK2_NEWDRAG
						DragItem( itemNo, FALSE );
#else
						WinDD_DragStart( WINDD_ITEM, (void *)(itemNo) );
#endif
						// クリック音
						play_se( SE_NO_CLICK, 320, 240 );
					}
				}
				ReturnFlag=TRUE;
			}
			// アイテムがあり、右クリックしたら说明ページを进める
			if( mouse.onceState & MOUSE_RIGHT_CRICK ){
				// その场所にアイテムがあるなら
				if ( pc.item[itemNo].useFlag ){
					BtlitemInfoPage++;
					if( BtlitemInfoPage >= pc.item[itemNo].memoPage ) BtlitemInfoPage = 0;
				}
				ReturnFlag=TRUE;
			}
		}
	}
	// ドラッグ中の处理はメインにお任せ

	if ( WinDD_CheckObjType()==WINDD_ITEM ){
		DragitemNo = (int)WinDD_ObjData();

		// 掴んだアイテムの表示はメインにお任せ

		// アイテムを掴んだ位置に枠表示
		if( DragitemNo < MAX_EQUIP_ITEM ){
			x = wI->wx + wI->sw[no].ofx + BtlEquipPos[DragitemNo][0] - 24;
			y = wI->wy + wI->sw[no].ofy + BtlEquipPos[DragitemNo][1] - 24;
			StockBoxDispBuffer( x+2, y+2, x+46, y+46, DISP_PRIO_WIN2, SYSTEM_PAL_AQUA, 0 );
		}
	}

	if( flag & MENU_MOUSE_OVER ){
		if( itemNo >= 0 ){
			// アイテム选择枠
			x = wI->wx + wI->sw[no].ofx + BtlEquipPos[i][0] - 24;
			y = wI->wy + wI->sw[no].ofy + BtlEquipPos[i][1] - 24;
			StockBoxDispBuffer( x, y, x+48, y+48, DISP_PRIO_WIN2, BoxColor, 0 );

////			// アイテム说明をしてもらうため登録
////			if ( pc.item[itemNo].useFlag ) BtlitemInfoNo = itemNo;
		}
	}
#ifdef PUK3_BANK_DBLCLICK
	// 直前でドロップを受け取って无效なアイテムを返す可能性があるので
	if( WinDD_CheckObjType()!=WINDD_NONE && ( flag & MENU_MOUSE_DRAGOVER ) ){
		DragitemNo = (int)WinDD_ObjData();
		if (DragitemNo>=MAX_EQUIP_ITEM){
			if( ( pc.item[DragitemNo].kind >= 0 && pc.item[DragitemNo].kind <= 7 ) ){
				StockBoxDispBuffer(
					wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
					wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
					DISP_PRIO_WIN2, BoxColor, 0 );
			}
		}
	}
#else
	if( flag & MENU_MOUSE_DRAGOVER ){
		DragitemNo = (int)WinDD_ObjData();
		if (DragitemNo>=MAX_EQUIP_ITEM){
			if( ( pc.item[DragitemNo].kind >= 0 && pc.item[DragitemNo].kind <= 7 ) ){
				StockBoxDispBuffer(
					wI->wx+wI->sw[no].ofx, wI->wy+wI->sw[no].ofy,
					wI->wx+wI->sw[no].ofx+wI->sw[no].sx, wI->wy+wI->sw[no].ofy+wI->sw[no].sy,
					DISP_PRIO_WIN2, BoxColor, 0 );
			}
		}
	}
#endif

	// アイテムの表示
	for(i=0;i<MAX_EQUIP_ITEM;i++){
		// アイテムがあるなら表示
		if( pc.item[i].useFlag ){
			x = wI->wx + wI->sw[no].ofx + BtlEquipPos[i][0];
			y = wI->wy + wI->sw[no].ofy + BtlEquipPos[i][1];

			// 武器でない时
			if ( i!=2 && i!=3 ){
				StockDispBuffer( x+105, y+93, DISP_PRIO_WIN2, CG_B_UNUSE_ITEM_NET, 0 );
			}else
			if ( pc.item[i].kind == 7 ){
				StockDispBuffer( x+105, y+93, DISP_PRIO_WIN2, CG_B_UNUSE_ITEM_NET, 0 );
			}
			// 未鉴定のアイテム
			if ( !pc.item[i].checkFlag ) StockDispBuffer( x+105, y+93, DISP_PRIO_WIN2, CG_B_UNUSE_ITEM_NET, 0 );

			//武器が有效の场合は１が立ってる
			if( !(pc.item[i].flag & ITEM_ETC_FLAG_VALIDEQUIP) ){
				StockDispBuffer( x + 105, y + 93, DISP_PRIO_WIN2, 22739, 0, &bm );
			}
			if (ItemNoOpe[i]){
				StockBoxDispBuffer( x-24, y-24, x+24, y+24, DISP_PRIO_WIN2, SYSTEM_PAL_RED, 0 );
				StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i].graNo, 0, &bm2 );
			}
			else StockDispBuffer( x, y, DISP_PRIO_WIN2, pc.item[i].graNo, 0, &bm );
		}
	}

	return ReturnFlag;
}








//======================================//
// 战闘时使い魔ウィンドウ				//
//======================================//

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------
BOOL MenuWindowBtlPetBf( int mouse )
{

	if ( mouse == WIN_INIT ){
	}

	wI->sw[EnumGraphBtlPetReturn].Enabled = FALSE;
	if( CheckBattlePet() != -1 ) wI->sw[EnumGraphBtlPetReturn].Enabled = TRUE;

	return TRUE;
}

BOOL MenuWindowBtlPetAf( int mouse )
{
	displayMenuWindow();

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------
int BarGra[4][4] = {
	{ GID_EarthLeft, GID_EarthMiddle, GID_EarthRight, GID_EarthLR },
	{ GID_WaterLeft, GID_WaterMiddle, GID_WaterRight, GID_WaterLR },
	{ GID_FireLeft, GID_FireMiddle, GID_FireRight, GID_FireLR },
	{ GID_WindLeft, GID_WindMiddle, GID_WindRight, GID_WindLR },
};
// モンスターの状态表示 ++++
void DrawMonsterStatus( short x, short y, int no, int FontPrio, int DispPrio, BLT_MEMBER *bm )
{
	char str[255];
	int i, j, k, a, b;

	int BtlGra = GID_PetBattleOff, WlkGra = GID_PetWalkOff, FldGra = GID_PetFeildOff, SbyGra = GID_PetStandbyOff;

	// この栏にペットがいる时かつ、バトル??待机の时
	if( pet[no].useFlag == TRUE ){
		if ( ( pet[no].battleSetting == PET_SETTING_STADBY || pet[no].battleSetting == PET_SETTING_BATTLE ) ){
			// プライオリティの制御
			FontBufCut(FontPrio);
			StockFontBuffer( 0, 0, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, "", 0, 0 );

			// 名称表示
			if (pet[no].freeName[0]!='\0'){
				StockFontBuffer( x+10, y+5, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, pet[no].freeName, 0, 0 );
			}else{
				StockFontBuffer( x+10, y+5, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, pet[no].name, 0, 0 );
			}
			// 等级表示
			sprintf( str, "%3d", pet[no].lv );                                 //MLHIDE
			StockFontBuffer( x+140, y+5, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, str, 0, 0 );
			// 种族表示
			if ( (pet[no].tribe>=0) && (pet[no].tribe< (sizeof(characterTribeStr)>>2) ) ){
				StockFontBuffer( x+130, y+19, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, characterTribeStr[pet[no].tribe], 0, 0 );
			}
#ifdef PUK3_RIDE_BATTLE
			// LP表示
			sprintf( str, "%6d/%6d", battlePet[no].lp, battlePet[no].maxLp );  //MLHIDE
			StockFontBuffer( x+35, y+19, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, str, 0, 0 );
			// FP表示
			sprintf( str, "%6d/%6d", battlePet[no].fp, battlePet[no].maxFp );  //MLHIDE
			StockFontBuffer( x+35, y+32, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, str, 0, 0 );
#else
			// LP表示
			sprintf( str, "%6d/%6d", pet[no].lp, pet[no].maxLp );              //MLHIDE
			StockFontBuffer( x+35, y+19, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, str, 0, 0 );
			// FP表示
			sprintf( str, "%6d/%6d", pet[no].fp, pet[no].maxFp );              //MLHIDE
			StockFontBuffer( x+35, y+32, FontPrio, FONT_KIND_SIZE_11, FONT_PAL_WHITE, str, 0, 0 );
#endif

			// 属性バーの表示
			b = y+6;
			for(i=0;i<4;i++){
				k = pet[no].attr[i] / 10;
				if ( k > 1 ){
					a = x+175;

					StockDispBuffer_PUK2( a, b, DispPrio, BarGra[i][0], 0, 1, bm );
					a += 6;
					for(j=1;j<k-1;j++){
						StockDispBuffer_PUK2( a, b, DispPrio, BarGra[i][1], 0, 1, bm );
						a += 6;
					}
					StockDispBuffer_PUK2( a, b, DispPrio, BarGra[i][2], 0, 1, bm );
				}else if ( k == 1 ){
					StockDispBuffer_PUK2( x+175, b, DispPrio, BarGra[i][3], 0, 1, bm );
				}
				b += 10;
			}

			if (pet[no].battleSetting==PET_SETTING_BATTLE) BtlGra = GID_PetBattleOn;
			if (pet[no].Walk) WlkGra = GID_PetWalkOn;
////			if (pet[no].battleSetting==PET_SETTING_STADBY) FldGra = GID_PetFeildOn;
			if (pet[no].battleSetting==PET_SETTING_STADBY) SbyGra = GID_PetStandbyOn;
		}
	}

	// バトル
	StockDispBuffer_PUK2( x+241, y+6, DispPrio, BtlGra, 0, 1, bm );
	// ウォーク
////	StockDispBuffer_PUK2( x+241, y+25, DispPrio, WlkGra, 0, 1, bm );
	// フィールド
////	StockDispBuffer_PUK2( x+267, y+6, DispPrio, FldGra, 0, 1, bm );
	// スタンドバイ
	StockDispBuffer_PUK2( x+267, y+25, DispPrio, SbyGra, 0, 1, bm );

	// パネル
	StockDispBuffer_PUK2( x, y, DispPrio, GID_PetPanel, 0, 1, bm );
}


BOOL MenuWindowBattlePetReturn( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	// 一行インフォ
	if( flag & MENU_MOUSE_OVER ) strcpy( OneLineInfoStr, MWONELINE_BATTLEPETLIST_RETURN );

	if( flag & MENU_MOUSE_LEFT ){
		// ペットが战闘に出ているとき
		if( CheckBattlePet() != -1 ){
			char moji[ 256 ];

			// 文字列作成
			sprintf( moji, "M|%X", 255 );                                      //MLHIDE
			// 送信
			nrproto_B_send( sockfd, moji );

			wI->flag |= WIN_INFO_DEL;
			
			// 入力济みフラグＯＮ
			BattleMenuInputFlag = TRUE;

			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_ReturnOn;
	if ( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_ReturnOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_ReturnOff;

	return ReturnFlag;
}

BOOL MenuWindowBattlePetPanel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;	
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;
	int Line = no - EnumGraphBtlPetPanel1;
	int Num = -1;
	int i, j, k;
	short x = wI->wx+wI->sw[no].ofx, y = wI->wy+wI->sw[no].ofy;
	struct BLT_MEMBER bm = {0};

	bm.rgba.rgba = 0xffffffff;
	bm.bltf = BLTF_NOCHG;

	j = -1;
	for(i=0;i<MAX_PET;i++){
		k = sortPet[i].index;
		// この栏にペットがいる时かつ、バトル??待机の时
		if( pet[k].useFlag == TRUE ){
			if ( ( pet[k].battleSetting == PET_SETTING_STADBY || pet[k].battleSetting == PET_SETTING_BATTLE ) ) j++;
		}
		if (j==Line){
			Num = k;
			break;
		}
	}

	if (Num>=0){
		// この栏にペットがいる时かつ、バトル??待机の时
		if( pet[Num].useFlag == TRUE ){
			if ( ( pet[Num].battleSetting == PET_SETTING_STADBY || pet[Num].battleSetting == PET_SETTING_BATTLE ) ){
				// 一行インフォ
				if( flag & MENU_MOUSE_OVER ){
					// 枠表示
					displaySwitchFrame( &wI->sw[no], flag, BoxColor );

					if( pet[Num].battleSetting == PET_SETTING_BATTLE );
#ifdef PUK3_RIDE_BATTLE
					else if( battlePet[Num].lp <= 0 ){
#else
					else if( pet[Num].lp <= 0 ){
#endif
						strcpy( OneLineInfoStr, MWONELINE_BATTLEPETLIST_PANEL_OFF );
					}else{
						strcpy( OneLineInfoStr, MWONELINE_BATTLEPETLIST_PANEL_ON );
					}
				}

				if( flag & MENU_MOUSE_LEFT ){
					// 死んでる时、返回
#ifdef PUK3_RIDE_BATTLE
					if( battlePet[Num].lp <= 0 ){
#else
					if( pet[Num].lp <= 0 ){
#endif
						// ＮＧ音
						play_se( SE_NO_NG, 320, 240 );
					}else
					// 既に战闘に出ているペットの时、返回
					if( pet[Num].battleSetting == PET_SETTING_BATTLE ){
						// ＮＧ音
						play_se( SE_NO_NG, 320, 240 );
					}else{
						char moji[ 256 ];

						// 文字列作成
						sprintf( moji, "M|%X", Num );                                   //MLHIDE
						// 送信
						nrproto_B_send( sockfd, moji );
						
						wI->flag |= WIN_INFO_DEL;
						
						// 入力济みフラグＯＮ
						BattleMenuInputFlag = TRUE;

						// ウィンドウ关闭音
						play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
					}

					ReturnFlag=TRUE;
				}
			}
		}

		DrawMonsterStatus( x, y, Num, FONT_PRIO_WIN, DISP_PRIO_WIN2, &bm );
	}

	return ReturnFlag;
}



//====================================//
//				不意打ち			  //
//====================================//

extern ACTION *pActSurprisalWnd;

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

BOOL MenuWindowSurpriseBf( int mouse )
{
	pActSurprisalWnd = NULL;

	wI->sw[EnumSurpriseText1].Enabled = FALSE;
	wI->sw[EnumSurpriseText2].Enabled = FALSE;

	if (SubProcNo!=BATTLE_PROC_CHAR_APPEAR) wI->flag |= WIN_INFO_DEL;

	// プレイヤーが偷袭时
	if( BattleBpFlag & BP_FLAG_PLAYER_SURPRISAL ) wI->sw[EnumSurpriseText1].Enabled = TRUE;
	// 敌が偷袭时（不意つかれた时）
	else if( BattleBpFlag & BP_FLAG_ENEMY_SURPRISAL ) wI->sw[EnumSurpriseText2].Enabled = TRUE;
	else wI->flag |= WIN_INFO_DEL;

	return TRUE;
}

BOOL MenuWindowSurpriseAf( int mouse )
{
	displayMenuWindow();

	MenuWindowCommonDraw( GID_InfoWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}
