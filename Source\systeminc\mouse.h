﻿/************************/
/*	mouse.h				*/
/************************/
#ifndef _MOUSE_H_
#define _MOUSE_H_

//#include "menu.h"

#ifdef PUK3_MOUSECURSOR
	// マウスのグラフィック构造体
	struct MOUSE_CURSOR_TYPE{
		int GraNo;			// マウスのグラフィックＩＤ
		short dfx, dfy;		// 表示位置
		int ResNo;			// マウスのリソースＩＤ
	};
	enum{
		MOUSE_CURSOR_TYPE_NOMAL,	// 通常カーソル
		MOUSE_CURSOR_TYPE_HAND,		// 手カーソル
		MOUSE_CURSOR_TYPE_BATTLE,	// 剑カーソル

		MOUSE_CURSOR_TYPE_DIR_0,	// 方向カーソル左上
		MOUSE_CURSOR_TYPE_DIR_1,	// 方向カーソル上
		MOUSE_CURSOR_TYPE_DIR_2,	// 方向カーソル右上
		MOUSE_CURSOR_TYPE_DIR_3,	// 方向カーソル右
		MOUSE_CURSOR_TYPE_DIR_4,	// 方向カーソル右下
		MOUSE_CURSOR_TYPE_DIR_5,	// 方向カーソル下
		MOUSE_CURSOR_TYPE_DIR_6,	// 方向カーソル左下
		MOUSE_CURSOR_TYPE_DIR_7,	// 方向カーソル左

		MOUSE_CURSOR_TYPE_MAX,
	};
#endif

// マウスの状态

#define	MOUSE_NO_CRICK						0		// 押されていない
#define	MOUSE_LEFT_CRICK			( 1 << 0 )		// 右键状态
#define	MOUSE_RIGHT_CRICK			( 1 << 1 )		// 右クリック状态
#define	MOUSE_LEFT_CRICK_UP			( 1 << 2 )		// 右键离した状态
#define	MOUSE_RIGHT_CRICK_UP		( 1 << 3 )		// 右クリック离した状态
#define	MOUSE_LEFT_DBL_CRICK		( 1 << 4 )		// 左ダブルクリック状态
#define	MOUSE_RIGHT_DBL_CRICK		( 1 << 5 )		// 右ダブルクリック状态
//#define	MOUSE_LEFT_AUTO_CRICK		( 1 << 6 )		// 左オートクリック状态
//#define	MOUSE_RIGHT_AUTO_CRICK		( 1 << 7 )		// 右オートクリック状态

// マウスの座标　构造体
typedef struct{
	int x, y;	// マウスの( Ｘ,Ｙ )座标
}MOUSE_POINT;

// マウス构造体
typedef struct{
	MOUSE_POINT nowPoint;			// 现在のマウスの座标
	MOUSE_POINT crickLeftDownPoint;	// 最后に右键したマウスの座标
	MOUSE_POINT crickLeftUpPoint;	// 最后に右键を离したマウスの座标
	MOUSE_POINT crickRightDownPoint;// 最后に右クリックしたマウスの座标
	MOUSE_POINT crickRightUpPoint;	// 最后に右クリックを离したマウスの座标
	int			state;				// マウスボタンの状态ビットフィールド
	int			onceState;			// マウスボタンの状态。ただし、押されたフレームにだけ值が入る
	int			autoState;			// マウスボタンの状态オートリピート
	UINT 		beforeLeftPushTime;	// 右键した时の时间を记忆
	UINT 		leftPushTime;		// 右键が押されている时间
	UINT 		beforeRightPushTime;// 右クリックした时の时间を记忆
	UINT 		rightPushTime;		// 右クリックが押されている时间
	UCHAR		level;				// 现在の等级（０～２５５）
	int 		itemNo;				// 现在ドラッグ中のアイテムの番号
	BOOL flag;						// マウスカーソルの表示／非表示フラグ
#ifdef PUK2
	int			wheel;				// マウスホイールの移动量
#endif
#ifdef PUK3_MOUSECURSOR
	int			type;				// マウスのグラフィック种类
#endif
}MOUSE;

// グローバル　マウス
extern MOUSE mouse;

// マウスカーソルに当たったフォント番号（フォント用）
extern int HitFontNo;
// マウスカーソルに当たったアクションポインタ记忆（ＢＭＰ用）
extern int HitDispNo;

// 一行インフォ文字列
extern char OneLineInfoStr[];
#ifdef PUK2
extern int OneLineInfoColor;

// 一行インフォのサブ
extern char OneLineInfoStrSub[];
extern int OneLineInfoStrSubColor;
#endif


// 表示ボックスの色
extern int BoxColor;

extern int giMouseMacroCount;	// デフォルトマウスカウント
extern int giMousePointListMax;	// デフォルトマウスポイント配列数

// マウス情报の初期化 ////////////////////////////////////////////////////////
void MouseInit( void );

// 现在のマウスの位置を记忆する //////////////////////////////////////////////
void MouseNowPoint( int x, int y );

// マウスの左ボタンが押された时のマウスの位置を记忆する //////////////////////
void MouseCrickLeftDownPoint( int x, int y );

// マウスの右ボタンが离された时のマウスの位置を记忆する //////////////////////
void MouseCrickLeftUpPoint( int x, int y );

// マウスの左ボタンが押された时のマウスの位置を记忆する //////////////////////
void MouseCrickRightDownPoint( int x, int y );

// マウスの右ボタンが离された时のマウスの位置を记忆する //////////////////////
void MouseCrickRightUpPoint( int x, int y );

// 左ダブルクリックされた时のマウスの位置を记忆する //////////////////////////
void MouseDblCrickLeftUpPoint( int x, int y );

// 右ダブルクリックされた时のマウスの位置を记忆する //////////////////////////
void MouseDblCrickRightUpPoint( int x, int y );

#ifdef PUK2
// マウスホイールの回転量を记忆
void MouseWheel( int wheel );

// マウスホイールの回転量记忆を初期化
void MouseWheelInit();
#endif
#ifdef PUK3_MOUSECURSOR
// マウスのグラフィックを变更する（变えておきたい间每フレーム呼び出す必要有り）
void setMouseType( int type, BOOL always = FALSE );
#endif

// マウス处理　///////////////////////////////////////////////////////////////
void MouseProc( void );

/* マウスカーソルのあたり判定 **************************************************/
void HitMouseCursor( void );

void InitMousePointList( void );
#ifdef PUK3_NOTFREE_MOUSE
void RelMousePointList( void );
#endif

#endif
