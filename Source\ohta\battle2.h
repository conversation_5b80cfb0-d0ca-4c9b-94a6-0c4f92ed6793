﻿/************************/
/*	battle.c			*/
/************************/
#ifndef _BATTLE_H_
#define _BATTLE_H_



#define BC_MAX 20 			// バトルキャラクターの最大数
#define BC_SORT_MAX 100		// ソートバトルキャラクター配列の最大数

// バトルキャラクターの予备构造体
typedef struct{
	int defX, defY;
	int myId;
	int attackId;
	BOOL appearFlag;
}BC_YOBI;

// バトルキャラクターの行动
enum{
	BC_APPEAR,		// 登场
	BC_STANDBY,		// 待机
	BC_MOVE,		// 移动
	BC_ATTACK,		// 攻击
};

// バトルキャラクターアクションポインタ
extern ACTION *pActBc[ BC_MAX ];

// バトル初期化 ****************************************************************/
void InitBattle( void );
// バトルキャラクター作成 ***********************************************/
ACTION *MakeBattleChar( int x, int y, int sprNo );
// 表示バッファソート ***************************************************/
void SortBattleChar();

// 方向からアングルに变换する **************************************************/
void DirToAngle( ACTION *pAct );
// その方向に移动する **************************************************/
void MoveDir( ACTION *pAct );

// 目的の方向に向ける **************************************************/
//
//	ACTION *pAct：向き变えをするアクションポインタ
//	float x：目的のＸ座标
//	float y：目的のＹ座标
//	戾り值：目的地との距离
//	pAct->dir に方向（角度）が入る
//
//**********************************************************************/
void ChangeDir( ACTION *pAct, float x, float y );
void ChangeDir( ACTION *pAct, ACTION *pAct2 );

// 目的地との距离を求める **************************************************/
//
//	ACTION *pAct：このアクションポインタとの距离を求める
//	float x：目的のＸ座标
//	float y：目的のＹ座标
//	戾り值：目的地との距离
//
//**********************************************************************/
int CheckDistance( ACTION *pAct, float x, float y );
int CheckDistance( ACTION *pAct, ACTION *pAct2 );

// バトルキャラクターデータの読み込み ****************************************/
void ReadBcData( void );

#endif
