﻿/************************/
/*	chat.h				*/
/************************/
#ifndef _CHAT_H_
#define _CHAT_H_

//一行の文字列のサイズ（杉
#define INPUT_STR_SIZE 		288 		// チャットバッファサイズ
//ChatBufferの配列数（杉
#ifdef PUK2_NEW_MENU
	#define MAX_CHAT_LINE 		512 	// チャットの行数
#else
	#define MAX_CHAT_LINE 		26 		// チャットの行数
#endif
#define DEF_CHAT_LINE 		8 			// 初期设定チャットの行数
#define MAX_CHAT_HISTORY 	64			// チャットヒストリーの数
#define MAX_VOICE 			5			// チャットの声の最大值
#define DEF_VOICE 			5			// チャットの声の大きさ
#define DEF_SOUND			1			// チャット流す时の音设定(无し)

#define MAX_CHAT_REG			24		// チャット文字列の登録数
#define MAX_CHAT_REG_PAR_PAGE	26		// １ページあたりのチャット文字列の登録数
#define MAX_CHAT_REG_STR_LEN	24		// 登録するチャット文字列の长さ


// 入力文字列バッファー构造体
typedef struct{
	// システム予约
	char buffer[ INPUT_STR_SIZE ]; 	// 文字列を记忆する
	int	imeX, imeY;					// ＩＭＥ文字列の表示开始座标（カーソル座标）
	int	cursorByte;					// カーソルのある位置（先头からのＢＹＴＥ数）
	int cnt;						// 现在の文字の数
	// ユーザー设定
	int	x, y;						// 文字列の座标
	int	fontPrio;					// 表示の优先度
	int	fontKind;					// 文字の大きさ
	int	color;						// 文字の色
	int lineMax;					// 行数设定
	int lineLen;					// 一行の文字の长さ
	int lineDist;					// 行と行の间隔さ（Ｙサイズ）
	BOOL blindFlag;					// 文字を见えなくする（＊＊＊＊＊）
	//int hitFontNo;				// 自分の配列の番号を学习（当たり判定用）
#ifdef PUK2
	int dispWidth;					// 表示幅（０なら无制限）
	int dispByte;					// 文字列表示开始位置（先头からのＢｙｔｅ数）
#endif
	
}INPUT_STR;

// チャットバッファー构造体
typedef struct{
	char 	buffer[ INPUT_STR_SIZE + 1 ]; // 一行分の文字列を记忆する
	int 	color;		// 文字の色
	int 	mojiFlag; 	// 文字を最后まで表示したかフラグ
	int 	mojiCnt; 	// 文字をどこまで表示したかカウンター
	int 	fontKind; 	// フォントの种类
}CHAT_BUFFER;

// チャットのヒストリー构造体
typedef struct{
	char str[ MAX_CHAT_HISTORY ][ INPUT_STR_SIZE + 1 ];	// チャットのヒストリー文字列
	int newNo;												// 最新のチャットヒストリー番号
	int nowNo;												// 现在のチャットヒストリー番号
}CHAT_HISTORY;



// チャット文字列登録の入力バッファ
extern INPUT_STR chatRegStr[];

// 现在のキーボードフォーカスバッファへのポインタ
extern INPUT_STR *pNowInputStr;

// チャットバッファー
extern CHAT_BUFFER ChatBuffer[];
// 自分の入力するバッファー
extern INPUT_STR MyChatBuffer;

// チャット行カウンター
extern int NowChatLine;

// 现在のチャットの行数
extern int NowMaxChatLine;

// 初期声の大きさ
extern int NowMaxVoice;

// チャットが流れる时にサウンドを鸣らすか？
extern int NowChatSound;

// カーソル点灭カウント
extern int CursorFlashCnt;
// チャットの一时非表示フラグ
extern BOOL ChatHideFlag;
// 聊天范围表示フラグ
extern BOOL ChatAreaDispFlag;
// 聊天范围表示时间
extern int ChatAreaDispTime;

/* 文字をバッファに溜める（１バイト）*/
void StockInputStrChar( char c );

#if 0
/* 文字をバッファに溜める（２バイト）*/
void StockChatBufferWord( WORD w );
#endif

/* チャットバッファーをフォントバッファに溜める */
void ChatBufferToFontBuffer( void );

/* チャット关连处理 */
void ChatProc( void );

/* チャット文字をバッファに溜める（一行）***************************************/
void StockChatBufferLine( char *str, int color );
void StockChatBufferLine( char *str, int color, int fontKind );

/* チャット文字を全てクリアする */
void ClearChatBuffer( void );

//******************************************************************************/
// 文字列を现在の入力文字列に送る
//******************************************************************************/
//	引数：	char *str	：送る文字列
//******************************************************************************/
void StrToNowInputStr( char *str );

/*******************************************************************************/
/* 文字列の最后が半角か全角かを调べる
/* 戾り值　	半角：１
/* 			全角：２
/*******************************************************************************/
int GetStrLastByte( char *str );
int GetStrLastByte( char *str, int len );

/*******************************************************************************/
/* 文字列の横幅（ドット数）を调べる
/* 戾り值　	ドット数
/*******************************************************************************/
int GetStrWidth( char *str );
int GetStrWidth( char *str ,int kind );
int GetStrWidth( char *str, int kind, int len );

/*******************************************************************************/
/* 文字が半角か全角かを调べる
/* 戾り值　	半角：１
/* 			全角：２
/*******************************************************************************/
int GetCharByte( char c );

/*******************************************************************************/
/* キー入力フォーカス取得
/*******************************************************************************/
void GetKeyInputFocus( INPUT_STR *pInputStr );

//****************************************************************************/
// 入力文字列初期化
//****************************************************************************/
//	引数：	INPUT_STR *inputStr,// 文字列の座标
//			int x, int y,		// 文字列の座标
//			int fontPrio,		// 表示の优先度
//			int fontKind,		// 文字の种类
//			int color,			// 文字の色　
//			char *str, 			// 初期文字列（无しならＮＵＬＬ）
//			int lineMax, 		// 最大行数
//			int lineLen,		// 一行の文字の长さ
//			int lineDist,		// 行と行の间隔さ（Ｙサイズ）
//			BOOL blindFlag		// 文字を见えなくする（＊＊＊＊＊）
//****************************************************************************/
void InitInputStr( INPUT_STR *inputStr, int x, int y, int fontPrio, int fontKind, int color, 
					char *str, int lineMax, int lineLen, int lineDist, BOOL blindFlag );

/* チャット关连初期化 **********************************************************/
void InitChat( void );

/* チャット关连处理 ************************************************************/
void ChatProc( void );

// キーボードカーソル点灭处理 **************************************************/
void FlashKeyboardCursor( void );

// ＬＥＦＴキー处理 *********************************************************/
void KeyboardLeft( void );
// ＲＩＧＨＴキー处理 *********************************************************/
void KeyboardRight( void );
// ＵＰキー处理 *********************************************************/
void KeyboardUp( void );
// ＤＯＷＮキー处理 *********************************************************/
void KeyboardDown( void );

/* リターンキー处理 ************************************************************/
void KeyboardReturn( void );

// ＢＳキー处理 ***************************************************************/
void KeyboardBackSpace( void );

// ＤＥＬＥＴＥキー处理 ***************************************************************/
void KeyboardDelete( void );

// ＴＡＢキー处理 ***************************************************************/
void KeyboardTab( void );

// チャットの履历文字の保存 ****************************************************************/
BOOL SaveChatHistoryStr( int no );
// チャットの履历文字の読み込み ****************************************************************/
BOOL LoadChatHistoryStr( void );

// クリップボードから入力バッファにコピー ********************************************/
void GetClipboad( void );
// チャットバッファからクリップボードに転送する处理 ***********************************/
void SetClipboad( void );


void shortCutSendChatReg( void );

#ifdef PUK2

enum{
	CHECKLETTERTYPE_HALF,		// 半角文字
	CHECKLETTERTYPE_FULL_TOP,	// 全角文字の１バイト目
	CHECKLETTERTYPE_FULL_TAIL,	// 全角文字の２バイト目
};
// 文字列のアルバイトの内容が、半角文字、全角文字の１バイト目、全角文字の２バイト目かを调べる
char CheckLetterType( char *str, short byte );

//--------------------------------------
// 文字检索(日本语对应型)
//--------------------------------------
// 引数
//	string			检索对象の文字列
//	c				检索する文字、文字列として指定、文字列最初の文字を检索する
//
// 戾り值
//	char *			!= NULL	---- 搜索文字が最初に见つかった位置を示すポインタ
//					== NULL	---- 搜索文字が见つからなかった
//
char *jstrchr( const char *string, const char *c );

#endif

#endif
