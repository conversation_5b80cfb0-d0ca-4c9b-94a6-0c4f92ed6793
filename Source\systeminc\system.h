﻿/************************/
/*	system.h			*/
/************************/
#ifndef _SYSTEM_H_
#define _SYSTEM_H_

/**** SYSTEM INCLUDE ****/
#include <windows.h>
#include <windowsx.h>
#include <string.h>
#include <stdio.h>
#ifdef _DEBUG	// PUK3_MEMORYLEAK
	#define _CRTDBG_MAP_ALLOC
	#include <stdlib.h>
	#include <crtdbg.h>
#else
#include <stdlib.h>
#endif

/**** DirectDraw Include ****/
#include <ddraw.h>			// DirectDrawのヘッダ

#ifdef DIRECTX9_UPGRADE
	// DirectX 9升级模式：先包含兼容性头文件
	#include "dx9_compat.h"
	#include <d3drmwin.h>		// Direct3Dの保持モードのAPI
#else
	// DirectX 7模式：直接包含
	#include <d3drmwin.h>		// Direct3Dの保持モードのAPI
#endif

#include "version.h"
#include "../puk2/puk2.h"
#ifdef PUK2
	#ifndef WM_MOUSEWHEEL
		#include "zmouse.h"
	#endif
#endif
#ifdef PUK3_ERRORMESSAGE_NUM
	// エラー文章
	#include "../puk3/errorMessage.h"
#endif

// デバッグ版でしか机能させないようなものはここに定义する。
#ifdef _DEBUG
#endif

#define	ANIMATION_BUFFER_LESS	// アニメーションバッファを少なくする。

#define OPERATION_SHOVEL	//スコップシステム
#define EXPAND_GRAPHICID	// タイルと布ツＩＤを扩张する。
#define MULTI_GRABIN		// グラビンの数を扩张する。
#define _CG2_NEWGRAPHIC		// 新しいグラフィックの表示を变更する
#define USE_VERSION2_FRONTEND	// バージョン２のフロントエンドを使う
#define SUPPORT_16BIT		// １６ビットモードサポート
#define DRAW_CONFIG			// 描画メニュー出す。
#define DIRECT_MUSIC		//DirectMusicシステム

#define _SYSTEMMENU_BTN_CONFIG	// システムメニューの设定键位を有效にする
//#define _FISHING_WINDOW		// 釣り关连のオペレーションを实行する
#define _APPEND_JEWEL		// 宝石组込のオペレーションを实行する
//#define _USE_RECENT_SOUND_LIST	// サウンドメモリ节约コードを有效にする
//#define _OPERATION_REMAKE_ITEM	// アイテム再作成スキルを有效にする
#define _TEST_TECH_YUK	// 战闘系上位スキルを有效にする
//#define _ENABLE_ALBUM_ITEMS	// アルバムアイテムを有效にする

//台服版没有KW图档
#ifndef VERSION_TW
#define CHINA_BIN
#endif

#ifdef MULTI_GRABIN
enum{
	BINMODE_NORMAL,
	BINMODE_EX,
	BINMODE_VER2,
#ifdef PUK2
	BINMODE_VER3,
	BINMODE_PUK2,
#endif
#ifdef PUK3_BIN
	BINMODE_PUK3,
#endif
#ifdef CHINA_BIN
	BINMODE_KW,
#endif
#ifdef VERSION_TW
	BINMODE_JOY,
	BINMODE_JOYEX,
	BINMODE_JOYCH,
#ifdef BIN_EXPAND
	BINMODE_UI,
	BINMODE_SA,
	BINMODE_EXP1,
	BINMODE_EXP2,
	BINMODE_EXP3,
	BINMODE_EXP4,
	BINMODE_EXP5,
	BINMODE_EXP6,
	BINMODE_EXP7,
	BINMODE_EXP8,
	BINMODE_EXP9,
#endif
#endif
	BINMODE_MAX
};
#endif

#ifdef PUK2
	extern unsigned long CmdLineFlg;	// コマンドラインからの指定保管用フラグ
	#define CMDLINE_3D_OFF			(1<<0)
	#define CMDLINE_ELMDISP			(1<<1)
	#define CMDLINE_DEBUGLINE		(1<<2)
	#define CMDLINE_FULLDISPMODE	(1<<3)
	#define CMDLINE_ONLYACTIVEBLT	(1<<4)
	#define CMDLINE_NOINITSOUND		(1<<5)
#endif

#ifdef PUK3_ALLOC

void *_rc_malloc( size_t size, char *file, int line );
void *_rc_calloc( size_t num, size_t size, char *file, int line );
void _rc_free( void *memblock, char *file, int line );

#undef malloc
#undef calloc
#undef free

#define malloc( size ) _rc_malloc( size, __FILE__, __LINE__ )
#define calloc( num, size ) _rc_calloc( num, size, __FILE__, __LINE__ )
#define free( memblock ) _rc_free( memblock, __FILE__, __LINE__ )

#ifdef PUK3_MEMALLOCLOG

void *_rc_GlobalAlloc( UINT uFlags, SIZE_T dwBytes, char *file, int line );
void *_rc_GlobalAllocPtr( UINT uFlags, SIZE_T dwBytes, char *file, int line );
void _rc_GlobalFree( void *memblock, char *file, int line );
void _rc_GlobalFreePtr( void *memblock, char *file, int line );

#undef GlobalAlloc
#undef GlobalAllocPtr
#undef GlobalFree
#undef GlobalFreePtr

#define GlobalAlloc( flag, size ) _rc_GlobalAlloc( flag, size, __FILE__, __LINE__ )
#define GlobalAllocPtr( flag, size ) _rc_GlobalAllocPtr( flag, size, __FILE__, __LINE__ )
#define GlobalFree( memblock ) _rc_GlobalFree( memblock, __FILE__, __LINE__ )
#define GlobalFreePtr( memblock ) _rc_GlobalFreePtr( memblock, __FILE__, __LINE__ )

#endif

#endif
#ifdef LOOP_ERRMESSAGE_BREAK

	// エラー专用の信息ボックス
	// 信息ボックスが戾り值としてexitIdを返した场合その场でアプリケーションを終了する
	int ErrMessageBox( HWND hWnd, LPCTSTR lpText, LPCTSTR lpCaption, UINT uType, int exitId );

#endif
#ifdef PUK3_MEMORYLEAK
	void MemCheckTimeClear();
	// メモリのチェック关数
	void MemCheck();
#endif
	#ifdef PUK2_MEMCHECK
		enum MEMLISTTYPE{
			MEMLISTTYPE_NONE = 0,

			// アクション周り
			MEMLISTTYPE_ACTION,
			MEMLISTTYPE_ACTIONYOBI,
			MEMLISTTYPE_ACTIONANIMLIST,
			MEMLISTTYPE_ACTIONFRAMELIST,
			MEMLISTTYPE_ACTIONCDLIST,

			// 通信关系
			MEMLISTTYPE_NRPROTOSTRINGWRAPPER,
			MEMLISTTYPE_NRPROTOSTRINGWRAPPERDATA,
			MEMLISTTYPE_STRUCT_NRPROTO,

			// キャラ关系
			MEMLISTTYPE_PC_FUKIDASHI,
			MEMLISTTYPE_CHARA_FUKIDASHI,

			// 描画关系
			MEMLISTTYPE_STRUCT_DIRECT_DRAW,
			MEMLISTTYPE_RGNDATAHEADER,
			MEMLISTTYPE_16BPP_PALTABLE,
			MEMLISTTYPE_BITMAPMEMORY,
			MEMLISTTYPE_PALLIST,
			MEMLISTTYPE_PALDATA,
			MEMLISTTYPE_SPRPAL_MASTER,
			MEMLISTTYPE_SPRPAL,
			MEMLISTTYPE_SPRITEDATA,
			MEMLISTTYPE_SPRITE_INFO,
			MEMLISTTYPE_ADRNBIN,
			MEMLISTTYPE_BMPNUMTABLE,
			MEMLISTTYPE_SCREENSHOTSRF,
			MEMLISTTYPE_SCREENSHOTBUF,

			// 战闘周り
			MEMLISTTYPE_BATTLE_MBBOX,
			MEMLISTTYPE_BATTLE_MBBOXTEXT,

			// ウィンドウ基本部分
			MEMLISTTYPE_WINDOW_INFO,
			MEMLISTTYPE_SWITCH_INFO,
			MEMLISTTYPE_SWITCH_DATA,
			MEMLISTTYPE_SWITCH_TEXT,
			MEMLISTTYPE_SPFONTBUFFER,

			// 各ウィンドウ
			MEMLISTTYPE_GeneralWindow,
			MEMLISTTYPE_GuilmonStatusWindow,

			// その他
			MEMLISTTYPE_IME,
			MEMLISTTYPE_MOUSE_POINTLIST,
			MEMLISTTYPE_LOADLOGFILE,
			MEMLISTTYPE_TESTVIEW,
			MEMLISTTYPE_UNPACK,

			MEMLISTTYPE_MAX,
		};
		#define MEMCHECK_BUF_NUM 10000
		void memlistset( void *pointer, enum MEMLISTTYPE num );
		void memlistrel( void *pointer, enum MEMLISTTYPE num );
		void writememlist();
	#endif
	#ifdef PUK2_DEBUG_DRAW
		#include "../puk2/newDraw/BLT_MEMBER.h"
		#define DEBUG_DRAW_MAX 2000

		struct STRUCT_DEBUG_DRAW{
			char type;				// どの描画を使用するか
			int x1, y1, x2, y2;		// 描画先の座标
			char str[256];			// 描画する文字列
			union RGBA_DATA rgba;	// 色
			int time;				// 表示する时间(fps)
		};
		extern struct STRUCT_DEBUG_DRAW DebugDrawData[DEBUG_DRAW_MAX];
		extern int DebugDrawDataNum;
		enum DEBUG_DRAW_TYPE{
			DDT_LINE,
			DDT_RECT,
			DDT_STRING,
		};
		// デバッグ描画データを描画する关数
		void _Debug_Draw_DrawFunc();
	
		// 线を描画する关数(3D使用时のみ有效)
		void _Debug_Draw_Line( int x1, int y1, int x2, int y2, unsigned long rgba, int time );
		// 四角を描画する关数(不透明度が0xff以外のとき3Dのみ有效)
		void _Debug_Draw_Rect( int l, int r, int t, int b, unsigned long rgba, int time );
		// 文字列を描画する关数(255文字まで有效)
		void _Debug_Draw_String( char *str, int font, int x, int y, int rgb, int time );
	#endif
	#ifdef PUK3_SEGMENTATION_FAULT
		enum PROCSTACK{
			PROCSTACK_GameMain = 0x8000,
			PROCSTACK_Process,
			PROCSTACK_RunAction,
			PROCSTACK_BattleMaster,
			PROCSTACK_CheckSequence,
			PROCSTACK_BattleChar,
			PROCSTACK_networkLoop,
			PROCSTACK_menuProc,
			PROCSTACK_PutBmp,
			PROCSTACK_charProc,
			PROCSTACK_ParentDeathCheckFunc,
			PROCSTACK_BattleAbnormal,
			PROCSTACK_BattleReverseType,
			PROCSTACK_BattleStun,
			PROCSTACK_Battle2Action,
			PROCSTACK_BattleParameterUpDown,
			PROCSTACK_BattleTreatType,
			PROCSTACK_mapCheckSum,
			PROCSTACK_changeCharAct,

			PROCSTACK_MAX,
		};
		#if 1
			extern int procstack[];
			extern int procstackNum;

			#define ProcStack( proc ) ( procstack[procstackNum++] = proc )
			#define ProcPop() ( procstackNum-- )

			#define ProcCall( funccall, proc ) \
			( ProcStack( proc ), (funccall), ProcPop() )
		#else
			void ProcStack( int proc );
			void ProcPop();
		#endif
	#endif
#endif
