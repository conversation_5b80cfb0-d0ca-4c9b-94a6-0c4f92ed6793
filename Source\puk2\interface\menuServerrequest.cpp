﻿// サーバリクエストメイン

#ifdef PUK2_NEW_MENU

ACTION *openItemShopTop( char *data );
ACTION *openSkillShopTop( char *data );
ACTION *openpTradeNpcTop( char *data );
ACTION *openpCountNpcTopTop( char *data );
ACTION *openItemShopBuyWindow( char *data );
ACTION *openItemShopSellWindow( char *data );
ACTION *openItemTradeWindow( char *data );
ACTION *openItemCountWindow( char *data );
ACTION *openItemAppraisalWindow( char *data );
ACTION *openItemRepairWindow( char *data );

extern MENU_WINDOW_INFO serverRequestWinMessage;
extern int serverRequestWinButtonType;				// ボタンの种类（状态）
extern int serverRequestWinSeqNo;					// サーバの管理番号
extern int serverRequestWinObjIndex;				// サーバの管理番号

#ifdef WN_PROC_SWITCH

// サーバリクエストウィンドウを开く关数
void openServerRequestWindow( int windowtype, int buttontype, int seqno, int objindex, char *data )
{
	ACTION *pAct = NULL;

	//メニュー开いてたら关闭
	//なにかウインドウが开いていたら
	if( serverRequestWinWindowType >= 0 ){
		switch(seqno){
		//イベントＮＰＣかウインドウマンＮＰＣなら
		case CHAR_WINDOWTYPE_EVENT_NPC:
		case CHAR_WINDOWTYPE_WINDOWMAN_NPC:
		case CHAR_WINDOWTYPE_SELECTRINGWATCH:
		//家族劝诱系
		case CHAR_WINDOWTYPE_GUILD_INVITE:
		case CHAR_WINDOWTYPE_GUILD_INVITED:
		case CHAR_WINDOWTYPE_GUILDMONSTER_MES:
			// 家族モンスター状态ウィンドウでないなら
			if( windowtype != WINDOW_MESSAGETYPE_GUILDMONSTER ){
				CloseWindowSelectType(2);
				CloseWindowSelectType(3);
				serverRequestWinWindowType = -1;
			}
			break;
		}
	}

	//开こうとする特定のウインドウが现在开いているとき
	if ( windowtype == serverRequestWinWindowType ){
		switch(serverRequestWinWindowType){
		// 银行ＮＰＣウィンドウ
		case WINDOW_MESSAGETYPE_BANK:
			if ( BankDataRenewal( data ) ){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
					pAct = openBankMenuWindow( BankType_Nomal, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
				}
			}
			if (!pAct){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
			}
			break;
		// 公会宠物部屋アイテムボックスウィンドウ
		case WINDOW_MESSAGETYPE_ITEMBOX:
			if ( BankDataRenewal( data ) ){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
					pAct = openBankMenuWindow( BankType_Guild, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
				}
			}
			if (!pAct){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
			}
			break;
		// 公会宠物部屋えさ箱ウィンドウ
		case WINDOW_MESSAGETYPE_FOODBOX:
			if ( BankDataRenewal( data ) ){
				if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo){
					pAct = openGuilMonFoodMenuWindow( 0, seqno, objindex, NULL, OPENMENUWINDOW_HIT, 0 );
				}
			}
			if (!pAct){
				if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo) serverRequestWinWindowType=-1;
			}
			break;
		// 揭示板ウィンドウ
		case WINDOW_MESSAGETYPE_BOARD:
			serverRequestWinWindowType = windowtype;
			serverRequestWinButtonType = buttontype;
			serverRequestWinSeqNo      = seqno;
			serverRequestWinObjIndex   = objindex;
			memoryMapGridPos( mapGx, mapGy );

			//揭示板作成
			openMessageNewBoardMenu( buttontype,data);
			openMenuWindow( MENU_WINDOW_BBS, OPENMENUWINDOW_HIT, 0 );
			break;
		// 家族モンスター状态ウィンドウ
		case WINDOW_MESSAGETYPE_GUILDMONSTER:
			if (WindowFlag[MENU_WINDOW_GUILMONSTATUS].wininfo) remakeGuilMonStatusData( data );
			else serverRequestWinWindowType=-1;
			break;
		}
	}

	//ウインドウ开く
	//なにもウインドウが开いていないなら
	if ( serverRequestWinWindowType < 0 ){
		// データのバックアップ
		serverRequestWinWindowType = windowtype;
		serverRequestWinButtonType = buttontype;
		serverRequestWinSeqNo      = seqno;
		serverRequestWinObjIndex   = objindex;
		memoryMapGridPos( mapGx, mapGy );

		switch(windowtype){
			// 信息のみ
			// 信息と１行入力
			// 项目选择
			case WINDOW_MESSAGETYPE_MESSAGE:
			case WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT:
			case WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT:
			case WINDOW_MESSAGETYPE_SELECT:
				openConversationWindow( (DEF_APPSIZEX-448)>>1, (DEF_APPSIZEY-240-24)>>1,
					448, 240, GID_CommonWindow, buttontype, seqno, objindex, data );
				break;

			// 信息のみ（ワイド）
			// 信息と１行入力（ワイド）
			case WINDOW_MESSAGETYPE_WIDEMESSAGE:
			case WINDOW_MESSAGETYPE_WIDEMESSAGEANDLINEINPUT:
				openConversationWindow( (DEF_APPSIZEX-576)>>1, (DEF_APPSIZEY-432-24)>>1,
					576, 432, GID_CommonWindow, buttontype, seqno, objindex, data );
				break;

			// スキルショップウィンドウ（トップウィンドウ）
			case WINDOW_MESSAGETYPE_SKILLMASTER_SHOP:
				openSkillShopTop( data );
				break;
				
			// スキルショップウィンドウ（ＰＣが买ウィンドウ）
			case WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_BUY:
				openPlayerBuySkillShopWindow( data );
				break;

			// スキルショップウィンドウ（ＰＣが外すウィンドウ）
			case WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_REMOVE:
				openPlayerForgetSkillShopWindow( data );
				break;

			// アイテムショップウィンドウ（トップウィンドウ）
			case WINDOW_MESSAGETYPE_ITEMSHOPMENU:
				openItemShopTop( data );
				break;

			// アイテムショップウィンドウ（贩卖ウィンドウ）
			case WINDOW_MESSAGETYPE_ITEMSHOPBUY:
				openItemShopBuyWindow( data );
				break;

			// アイテムショップウィンドウ（买取ウィンドウ）
			case WINDOW_MESSAGETYPE_ITEMSHOPSELL:
				openItemShopSellWindow( data );
				break;

			// 医者ＮＰＣウィンドウを开く
			case WINDOW_MESSAGETYPE_INJURY_DOCTOR:
				openInjuryWindow( (DEF_APPSIZEX-448)>>1, (DEF_APPSIZEY-240-24)>>1,448, 240, 
					GID_CommonWindow, buttontype, seqno, objindex, data );
				break;

			// 鉴定ＮＰＣウィンドウ
			case WINDOW_MESSAGETYPE_JUDGEMAN:
				openItemAppraisalWindow( data );
				break;

			// 修理ＮＰＣウィンドウ
			case WINDOW_MESSAGETYPE_REPAIR_MAN:
				openItemRepairWindow( data );
				break;

			// 揭示板ウィンドウ
			case WINDOW_MESSAGETYPE_BOARD:
				//揭示板作成
				openMessageNewBoardMenu( buttontype,data);
				openMenuWindow( MENU_WINDOW_BBS, OPENMENUWINDOW_HIT, 0 );
				break;

			// 银行ＮＰＣウィンドウ
			case WINDOW_MESSAGETYPE_BANK:
				if ( BankDataRenewal( data ) ){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
						pAct = openBankMenuWindow( BankType_Nomal, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
					}
				}
				if (!pAct){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
				}
				break;

			// モンスタースキルショップ　贩卖ウィンドウ
			case WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP1:
				openPlayerPetSkillShopWindow( data );
				break;

			// モンスタースキルショップ　モンスター选择ウィンドウ
			case WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP2:
				changePetSkillWindowPetSelect( data );
				break;

			// モンスタースキルショップ　记忆位置选择ウィンドウ
			case WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP3:
				changePetSkillWindowPosSelect( data );
				break;

			// 交换ＮＰＣトップウィンドウ
			case WINDOW_MESSAGETYPE_TRADE_NPC_MENU:
				openpTradeNpcTop( data );
				break;

			// 交换ＮＰＣメインウィンドウ
			case WINDOW_MESSAGETYPE_TRADE_NPC_MAIN:
				openItemTradeWindow( data );
				break;

			// アイテムカウントＮＰＣトップウインドウ
			case WINDOW_MESSAGETYPE_ITEMCOUNT_TOP:
				openpCountNpcTopTop( data );
				break;

			// アイテムカウントＮＰＣメインウインドウ
			case WINDOW_MESSAGETYPE_ITEMCOUNT_MAIN:
				openItemCountWindow( data );
				break;

#ifdef OPERATION_SHOVEL
			// スコップ选择ウィンドウ
			case WINDOW_MESSAGETYPE_SHOVEL_SELECT:
				//スコップアイテム
				ChangeShovelItemSelectMode( data );
				break;
#endif
			// 家族モンスター状态ウィンドウ
			case WINDOW_MESSAGETYPE_GUILDMONSTER:
				openGuilMonStatusWindow( seqno, objindex, data );
				break;

			// 公会宠物部屋アイテムボックスウィンドウ
			case WINDOW_MESSAGETYPE_ITEMBOX:
				if ( BankDataRenewal( data ) ){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
						pAct = openBankMenuWindow( BankType_Guild, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
					}
				}
				if (!pAct){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
				}
				break;

			// 公会宠物部屋えさ箱ウィンドウ
			case WINDOW_MESSAGETYPE_FOODBOX:
				if ( BankDataRenewal( data ) ){
					if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo){
						pAct = openGuilMonFoodMenuWindow( 0, seqno, objindex, NULL, OPENMENUWINDOW_HIT, 0 );
					}
				}
				if (!pAct){
					if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo) serverRequestWinWindowType=-1;
				}
				break;

			// 整形外科医ウィンドウ
			case WINDOW_MESSAGETYPE_ORTHOPEDIST:
				openOrthopedistWindow( seqno, objindex, data );
				break;

			// 整形外科医确认ウィンドウ
			case WINDOW_MESSAGETYPE_ORTHOPEDIST_CONFIRMATION:
				openOrCfWindow( buttontype, seqno, objindex, data );
				break;
#ifdef PUK3_PETSKILLSHOPEX
			// モンスタースキルショップ扩张　贩卖ウィンドウ
			case WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX1:
				openPlayerPetSkillShopWindowEx( data );
				break;

			// モンスタースキルショップ扩张　モンスター选择ウィンドウ
			case WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX2:
				changePetSkillWindowPetSelectEx( data );
				break;

			// モンスタースキルショップ扩张　记忆位置选择ウィンドウ
			case WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX3:
				changePetSkillWindowPosSelect( data );
				break;
#endif
		}
	}

}

#else

// サーバリクエストウィンドウを开く关数
void openServerRequestWindow( int windowtype, int buttontype, int seqno, int objindex, char *data )
{
	ACTION *pAct = NULL;

#ifdef PUK2
	//メニュー开いてたら关闭
	if( serverRequestWinWindowType >= 0 ){
		switch(seqno){
		//イベントＮＰＣかウインドウマンＮＰＣなら
		case CHAR_WINDOWTYPE_EVENT_NPC:
		case CHAR_WINDOWTYPE_WINDOWMAN_NPC:
		case CHAR_WINDOWTYPE_SELECTRINGWATCH:
		//家族劝诱系
		case CHAR_WINDOWTYPE_GUILD_INVITE:
		case CHAR_WINDOWTYPE_GUILD_INVITED:
		case CHAR_WINDOWTYPE_GUILDMONSTER_MES:
			// 家族モンスター状态ウィンドウでないなら
			if( windowtype != WINDOW_MESSAGETYPE_GUILDMONSTER ){
				CloseWindowSelectType(2);
				CloseWindowSelectType(3);
				serverRequestWinWindowType = -1;
			}
			break;
		}
/***
		switch(seqno){
		//イベントＮＰＣかウインドウマンＮＰＣなら
		case 326:case 327:case 369:
		//家族劝诱系
		case 388:case 389:case 390:
			CloseWindowSelectType(2);
			CloseWindowSelectType(3);
			serverRequestWinWindowType = -1;
			break;
		}
***/
	}
#else
	//イベントＮＰＣかウインドウマンＮＰＣなら
	if( seqno == 326 || seqno == 327 || seqno == 369){
		//メニュー开いてたら关闭
		if( serverRequestWinWindowType >= 0 ){
			CloseWindowSelectType(2);
			CloseWindowSelectType(3);
			serverRequestWinWindowType = -1;
		}
	}
#endif

	if ( serverRequestWinWindowType == windowtype ){
			switch(serverRequestWinWindowType){
			// 银行ＮＰＣウィンドウ
			case WINDOW_MESSAGETYPE_BANK:
				if ( BankDataRenewal( data ) ){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
						pAct = openBankMenuWindow( BankType_Nomal, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
					}
				}
				if (!pAct){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
				}
				break;
			// 公会宠物部屋アイテムボックスウィンドウ
			case WINDOW_MESSAGETYPE_ITEMBOX:
				if ( BankDataRenewal( data ) ){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
						pAct = openBankMenuWindow( BankType_Guild, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
					}
				}
				if (!pAct){
					if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
				}
				break;
			// 公会宠物部屋えさ箱ウィンドウ
			case WINDOW_MESSAGETYPE_FOODBOX:
				if ( BankDataRenewal( data ) ){
					if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo){
						pAct = openGuilMonFoodMenuWindow( 0, seqno, objindex, NULL, OPENMENUWINDOW_HIT, 0 );
					}
				}
				if (!pAct){
					if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo) serverRequestWinWindowType=-1;
				}
				break;
			// 揭示板ウィンドウ
			case WINDOW_MESSAGETYPE_BOARD:
				serverRequestWinWindowType = windowtype;
				serverRequestWinButtonType = buttontype;
				serverRequestWinSeqNo      = seqno;
				serverRequestWinObjIndex   = objindex;
				memoryMapGridPos( mapGx, mapGy );

				//揭示板作成
				openMessageNewBoardMenu( buttontype,data);
				openMenuWindow( MENU_WINDOW_BBS, OPENMENUWINDOW_HIT, 0 );
				break;
			// 家族モンスター状态ウィンドウ
			case WINDOW_MESSAGETYPE_GUILDMONSTER:
				if (WindowFlag[MENU_WINDOW_GUILMONSTATUS].wininfo) remakeGuilMonStatusData( data );
				else serverRequestWinWindowType=-1;
				break;
		}
	}
	if ( serverRequestWinWindowType < 0 ){
		// データのバックアップ
		serverRequestWinWindowType = windowtype;
		serverRequestWinButtonType = buttontype;
		serverRequestWinSeqNo      = seqno;
		serverRequestWinObjIndex   = objindex;
		memoryMapGridPos( mapGx, mapGy );

		// 信息のみ
		// 信息と１行入力
		// 项目选择
		if( windowtype == WINDOW_MESSAGETYPE_MESSAGE
		 || windowtype == WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT
		 || windowtype == WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT
		 || windowtype == WINDOW_MESSAGETYPE_SELECT ){
			openConversationWindow( (DEF_APPSIZEX-448)>>1, (DEF_APPSIZEY-240-24)>>1,
				448, 240, GID_CommonWindow, buttontype, seqno, objindex, data );
		}else
		// 信息のみ（ワイド）
		// 信息と１行入力（ワイド）
		if( windowtype == WINDOW_MESSAGETYPE_WIDEMESSAGE
			|| windowtype == WINDOW_MESSAGETYPE_WIDEMESSAGEANDLINEINPUT ){
			openConversationWindow( (DEF_APPSIZEX-576)>>1, (DEF_APPSIZEY-432-24)>>1,
				576, 432, GID_CommonWindow, buttontype, seqno, objindex, data );
		}else
		// スキルショップウィンドウ（トップウィンドウ）
		if( windowtype == WINDOW_MESSAGETYPE_SKILLMASTER_SHOP ){
			openSkillShopTop( data );
////			initSkillShopWindow1( data );
		}else
		// スキルショップウィンドウ（ＰＣが买ウィンドウ）
		if( windowtype == WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_BUY ){
			openPlayerBuySkillShopWindow( data );
////			initSkillShopWindow2( data );
		}else
		// スキルショップウィンドウ（ＰＣが外すウィンドウ）
		if( windowtype == WINDOW_MESSAGETYPE_SKILLMASTER_SHOP_REMOVE ){
			openPlayerForgetSkillShopWindow( data );
////			initSkillShopWindow3( data );
		}else
		// アイテムショップウィンドウ（トップウィンドウ）
		if( windowtype == WINDOW_MESSAGETYPE_ITEMSHOPMENU ){
			openItemShopTop( data );
////			openItemShopTopWin( data );
		}else
		// アイテムショップウィンドウ（贩卖ウィンドウ）
		if( windowtype == WINDOW_MESSAGETYPE_ITEMSHOPBUY ){
			openItemShopBuyWindow( data );
////			openItemShopSellWin( data );
		}else
		// アイテムショップウィンドウ（买取ウィンドウ）
		if( windowtype == WINDOW_MESSAGETYPE_ITEMSHOPSELL ){
			openItemShopSellWindow( data );
////			openItemShopBuyWin( data );
		}else
		// 医者ＮＰＣウィンドウを开く
		if( windowtype == WINDOW_MESSAGETYPE_INJURY_DOCTOR ){
			openInjuryWindow( (DEF_APPSIZEX-448)>>1, (DEF_APPSIZEY-240-24)>>1,
				448, 240, GID_CommonWindow, buttontype, seqno, objindex, data );
////			openDoctorNpcMenu( data );
		}else
		// 鉴定ＮＰＣウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_JUDGEMAN ){
			openItemAppraisalWindow( data );
////			openAppraisalNpcMenu( data );
		}else
		// 修理ＮＰＣウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_REPAIR_MAN ){
			openItemRepairWindow( data );
////			openRepairNpcMenu( data );
		}else
		// 揭示板ウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_BOARD ){
			//揭示板作成
			openMessageNewBoardMenu( buttontype,data);
			openMenuWindow( MENU_WINDOW_BBS, OPENMENUWINDOW_HIT, 0 );
		}else
		// 银行ＮＰＣウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_BANK ){
			if ( BankDataRenewal( data ) ){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
					pAct = openBankMenuWindow( BankType_Nomal, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
				}
			}
			if (!pAct){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
			}
		}else
		// モンスタースキルショップ　贩卖ウィンドウ
		if(	windowtype == WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP1 ){
			openPlayerPetSkillShopWindow( data );
////			openMonsterSkillShopMenu1( data );
		}else
		// モンスタースキルショップ　モンスター选择ウィンドウ
		if(	windowtype == WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP2 ){
			changePetSkillWindowPetSelect( data );
////			openMonsterSkillShopMenu2( data );
		}else
		// モンスタースキルショップ　记忆位置选择ウィンドウ
		if(	windowtype == WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOP3 ){
			changePetSkillWindowPosSelect( data );
////			openMonsterSkillShopMenu3( data );
		}else
		// 交换ＮＰＣトップウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_TRADE_NPC_MENU ){
			openpTradeNpcTop( data );
////			openTradeNpcTopMenu( data );
		}else
		// 交换ＮＰＣメインウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_TRADE_NPC_MAIN ){
			openItemTradeWindow( data );
////			openTradeNpcMenu( data );
		}else
		// アイテムカウントＮＰＣトップウインドウ
		if( windowtype == WINDOW_MESSAGETYPE_ITEMCOUNT_TOP ){
			openpCountNpcTopTop( data );
////			openItemCountNpcTopMenu( data );
		}else
		// アイテムカウントＮＰＣメインウインドウ
		if( windowtype == WINDOW_MESSAGETYPE_ITEMCOUNT_MAIN ){
			openItemCountWindow( data );
////			openItemCountNpcMainMenu( data );
		}
#ifdef OPERATION_SHOVEL
		else
		// スコップ选择ウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_SHOVEL_SELECT ){
			//スコップアイテム
			ChangeShovelItemSelectMode( data );
////			openShovelSelectMenu( data );
		}
#endif
#ifdef PUK2
		else
		// 家族モンスター状态ウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_GUILDMONSTER ){
			openGuilMonStatusWindow( seqno, objindex, data );
		}else
		// 公会宠物部屋アイテムボックスウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_ITEMBOX ){
			if ( BankDataRenewal( data ) ){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo){
					pAct = openBankMenuWindow( BankType_Guild, seqno, objindex, OPENMENUWINDOW_HIT, 0 );
				}
			}
			if (!pAct){
				if (!WindowFlag[MENU_WINDOW_BANK].wininfo) serverRequestWinWindowType=-1;
			}
		}else
		// 公会宠物部屋えさ箱ウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_FOODBOX ){
			if ( BankDataRenewal( data ) ){
				if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo){
					pAct = openGuilMonFoodMenuWindow( 0, seqno, objindex, NULL, OPENMENUWINDOW_HIT, 0 );
				}
			}
			if (!pAct){
				if (!WindowFlag[MENU_WINDOW_GUILMONFOOD].wininfo) serverRequestWinWindowType=-1;
			}
		}
		else
		// 整形外科医ウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_ORTHOPEDIST ){
			openOrthopedistWindow( seqno, objindex, data );
		}else
		// 整形外科医确认ウィンドウ
		if( windowtype == WINDOW_MESSAGETYPE_ORTHOPEDIST_CONFIRMATION ){
			openOrCfWindow( buttontype, seqno, objindex, data );
		}
#endif
#ifdef PUK3_PETSKILLSHOPEX
		// モンスタースキルショップ扩张　贩卖ウィンドウ
		if(	windowtype == WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX1 ){
			openPlayerPetSkillShopWindowEx( data );
		}else
		// モンスタースキルショップ扩张　モンスター选择ウィンドウ
		if(	windowtype == WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX2 ){
			changePetSkillWindowPetSelectEx( data );
		}else
		// モンスタースキルショップ扩张　记忆位置选择ウィンドウ
		if(	windowtype == WINDOW_MESSAGETYPE_MONSTER_SKILL_SHOPEX3 ){
			changePetSkillWindowPosSelect( data );
		}else
#endif
	}
}


#endif


#endif

#ifdef PUK3_PROF
// サーバリクエストウィンドウを开く关数
void openServerRequestWindowForProfile( int windowtype, int buttontype, int seqno, int objindex, char *data )
{
	ACTION *pAct = NULL;

	//メニュー开いてたら关闭
	if( serverRequestWinWindowType >= 0 ){
		switch(seqno){
		//イベントＮＰＣかウインドウマンＮＰＣなら
		case CHAR_WINDOWTYPE_EVENT_NPC:
		case CHAR_WINDOWTYPE_WINDOWMAN_NPC:
		case CHAR_WINDOWTYPE_SELECTRINGWATCH:
		//家族劝诱系
		case CHAR_WINDOWTYPE_GUILD_INVITE:
		case CHAR_WINDOWTYPE_GUILD_INVITED:
		case CHAR_WINDOWTYPE_GUILDMONSTER_MES:
			// 家族モンスター状态ウィンドウでないなら
			if( windowtype != WINDOW_MESSAGETYPE_GUILDMONSTER ){
				CloseWindowSelectType(2);
				CloseWindowSelectType(3);
				serverRequestWinWindowType = -1;
			}
			break;
		}
	}

	//ウインドウ开く
	// データのバックアップ
	serverRequestWinWindowType = windowtype;
	serverRequestWinButtonType = buttontype;
	serverRequestWinSeqNo      = seqno;
	serverRequestWinObjIndex   = objindex;
	memoryMapGridPos( mapGx, mapGy );

	switch(windowtype){
		//プロフィール初期ウィンドウ
		case WINDOW_MESSAGETYPE_PROFILE:
			openMessageProfileBBS1( buttontype,data);
			openMenuWindow( MENU_WINDOW_PROFILE_PROFILE_1, OPENMENUWINDOW_HIT, 0 );
			break;

		//プロフィールリストウィンドウ
		case WINDOW_MESSAGETYPE_PROFILELIST:
			openMessageProfileBBS2( buttontype,data);
			openMenuWindow( MENU_WINDOW_PROFILE_PROFILE_2, OPENMENUWINDOW_HIT, 0 );
			break;

		//プロフィールメールウィンドウ
		case WINDOW_MESSAGETYPE_PROFILEMAIL:
			openMessageProfileBBS3( buttontype,data);
			openMenuWindow( MENU_WINDOW_PROFILE_PROFILE_3, OPENMENUWINDOW_HIT, 0 );
			break;				
	}	

}

#endif

//====================================//
//		会话??医者ウィンドウ		  //
//====================================//

struct CONVERSATION_WIN{
	// サーバー情报
	int WinType;
	int SeqNo;
	int ObjIndex;

	// 泛用ウィンドウ情报
	int SelCnt;
	char Msg[30][81];
} sCv;
short Cv_InputPos[2] = { 0 };

// 怪我医者用设定
#define MAX_PATIENT		6
#define MENU_DOCTOR_OFX	32
DOCTOR_NPC_WIN_INFO patientList[MAX_PATIENT+1];
int injuryFlag;
int buttonBit;

//====================================
// 会话ウィンドウ部

BOOL Cv_ReturnFunc( int Button, int num, char **str, char linenum )
{
	char msg[256];
	char msg2[256];

	if ( Button==0 && num<0 ){
/***
		sprintf( msg2, "%d", -sCv.SelCnt );
		makeSendString( msg2, msg, sizeof( msg )-1 );
		nrproto_WN_send( sockfd, mapGx, mapGy, sCv.SeqNo, sCv.ObjIndex, 0, msg );
***/

		serverRequestWinWindowType = -1;
	}

	if ( Button != 0 ){
		char Str[256];
		char StrWork[256];
		char StrWork2[256];

		switch(sCv.WinType){
		// 项目选择ウィンドウなら
		case WINDOW_MESSAGETYPE_SELECT:
			sprintf( msg2, "%d", -sCv.SelCnt );                                //MLHIDE
			makeSendString( msg2, msg, sizeof( msg )-1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, sCv.SeqNo, sCv.ObjIndex, Button, msg );

			serverRequestWinWindowType = -1;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );

			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

			break;

		case WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT:
			// ＯＫボタンが押されたとき
			if ( Button & GRL_PUSH_OK ){
				// 何も入力されていないなら駄目
				if( str[0][0]=='\0' && str[1][0]=='\0' ) return FALSE;

				// スペースのみの文字列なら駄目
				if ( !CheckSendStr(str[0]) ) return FALSE;
				if ( !CheckSendStr(str[1]) ) return FALSE;

				// '|','\'が入っていたら駄目
				if ( VirticalCheck( (unsigned char *)str[0] ) == 1 ) return FALSE;
				if ( VirticalCheck( (unsigned char *)str[1] ) == 1 ) return FALSE;
			}

			//一行目
			strcpy( StrWork, str[0] );
			makeSendString( StrWork, StrWork, sizeof(StrWork)-1 );
			//ニ行目
			strcpy( StrWork2, str[1] );
			makeSendString( StrWork2, StrWork2, sizeof(StrWork2)-1 );
			//くっつける
			sprintf(Str,"%s|%s",StrWork,StrWork2);                             //MLHIDE
			makeEscapeString((unsigned char*)&Str, (unsigned char*)&msg, sizeof(msg) - 1 );
			nrproto_WN_send( sockfd, mapGx, mapGy, sCv.SeqNo, sCv.ObjIndex, Button, msg );

			serverRequestWinWindowType = -1;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );

			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			break;
		default:
			if ( linenum == 1 ){
				makeSendString( str[0], msg, sizeof(msg)-1 );
			}else{
				makeSendString( "", msg, sizeof(msg)-1 );
			}
			nrproto_WN_send( sockfd, mapGx, mapGy, sCv.SeqNo, sCv.ObjIndex, Button, msg );

			serverRequestWinWindowType = -1;

			// 决定音c（文字等クリック时）
			play_se( SE_NO_OK3, 320, 240 );

			// ウィンドウ关闭音
			play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			break;
		}
	}
	if ( num >= 0 ){
		sprintf( msg, "%d", num + 1 );                                      //MLHIDE

		makeSendString( msg, msg2, sizeof( msg2 )-1 );
		nrproto_WN_send( sockfd, mapGx, mapGy, sCv.SeqNo, sCv.ObjIndex, 0, msg2 );

		serverRequestWinWindowType = -1;

		// 决定音c（文字等クリック时）
		play_se( SE_NO_OK3, 320, 240 );
	}

	return TRUE;
}

void setActionFormStr( char *str, short l_x, short l_y )
{
	int actNo, ang, x, y;
	char *pc = str, *pc2;
	char s[31];
	char c;
	int i;

	// 先头が、"g,"でなければ变换しない
	if ( pc[0]!='g' || pc[1]!=',' ) return;
	pc += 2;

	//------------------------------------
	// アニメーションＩＤの取得

	// ','か'('までの文字列を拔き出す
	for(i=0;pc[i]!='\0';i++){
		if ( pc[i] == ',' ) break;
		if ( pc[i] == '(' ) break;
		if ( pc[i] == ' ' ) continue;
		s[i] = pc[i];
	}
	s[i] = '\0';
	c = pc[i];
	pc += i + 1;
	// アニメーションＩＤの取得
	actNo = atoi(s);

	//------------------------------------
	// アニメーションの向きの取得

	ang = 0;
	if ( c == ',' ){
		pc++;
		// ','か'('までの文字列を拔き出す
		for(i=0;pc[i]!='\0';i++){
			if ( pc[i] == ',' ) break;
			if ( pc[i] == '(' ) break;
			if ( pc[i] == ' ' ) continue;
			s[i] = pc[i];
		}
		s[i] = '\0';
		c = pc[i];
		pc += i + 1;
		// アニメーションＩＤの取得
		ang = atoi(s);
	}

	//------------------------------------
	// 座标の设定

	x = l_x;
	y = l_y;
	pc2 = strchr( pc, '(' );
	if (pc2){
		pc = pc2;
		pc++;
		pc2 = strchr( pc, ')' );
		if (pc2){
			// ','か'('までの文字列を拔き出す
			for(i=0;pc[i]!='\0';i++){
				if ( pc[i] == ',' ) break;
				if ( pc[i] == ' ' ) continue;
				s[i] = pc[i];
			}
			s[i] = '\0';
			c = pc[i];
			pc += i + 1;
			// アニメーションＩＤの取得
			x = atoi(s);
	
			// ','か'('までの文字列を拔き出す
			for(i=0;pc[i]!='\0';i++){
				if ( pc[i] == ',' ) break;
				if ( pc[i] == ' ' ) continue;
				s[i] = pc[i];
			}
			s[i] = '\0';
			c = pc[i];
			pc += i + 1;
			// アニメーションＩＤの取得
			y = atoi(s);
		}
	}
	addGrl_Action( 1, actNo, ang, x, y );
}

ACTION *openConversationWindow( short x, short y, short w, short h, int GraNo, int BottonType, int SeqNo, int ObjIndex, char *data )
{
	int i;

	int BtnAreaH, InputAreaH, Interval;

	int LineLen;
	int LineNum;
	char *pc;
	char *pc2;

	// アクションの初期化
	initGrl_Action();

	// サーバ情报保存
	sCv.WinType = serverRequestWinWindowType;
	sCv.SeqNo = SeqNo,	sCv.ObjIndex = ObjIndex;

	// 文字列をＳＪＳに变换
	makeRecvString( data );

	// 信息生成
	BtnAreaH = 0;
	if( BottonType ) BtnAreaH = GENERAL_BUTTON_AREA_H;

	InputAreaH = 0;
	if( sCv.WinType == WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT
		|| sCv.WinType == WINDOW_MESSAGETYPE_WIDEMESSAGEANDLINEINPUT ){
		InputAreaH = GENERAL_INPUTSTR_AREA_H;
		Cv_InputPos[0] = h - BtnAreaH - InputAreaH - GENERAL_STRPOS_Y;
	}
	//２行ラインの时
	if ( serverRequestWinWindowType == WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT ){
		Cv_InputPos[0] = h - BtnAreaH - InputAreaH - GENERAL_STRPOS_Y - ( 17 * 6 );
		Cv_InputPos[1] = h - BtnAreaH - InputAreaH - GENERAL_STRPOS_Y - ( 17 * 3 );
	}

	Interval = 16;
	sCv.SelCnt = 0;
	if ( sCv.WinType == WINDOW_MESSAGETYPE_SELECT ){
		Interval = 20;

		sCv.SelCnt = getIntegerToken( data, '\n', 1 );
		data = strchr( data, '\n' )+1;
	}

	LineLen = GeneralWindowStrLen(w);
	LineNum = GeneralWindowStrLineNum( h-InputAreaH, BottonType, Interval );

	// 文字列から、各种データを拔き出す
	pc = data;
	for(;;){
		// "@"检索
		pc = jstrchr( pc, "@" );                                            //MLHIDE
		if (!pc) break;
		pc++;

		// '@'が二つ重なっているときは'@'に变换
		if ( pc[0] == '@' ){ pc[1] = -1;	continue; }

		// "g,"のときはアクションデータ
		if ( pc[0]=='g' && pc[1]==',' ){
			// 次の"@"检索
			pc2 = jstrchr( pc, "@" );                                          //MLHIDE
			if (!pc2) break;
			pc2[0] = '\0';
			// 文字列からアクションを设定してもらう
			setActionFormStr( pc, w/2, h/2 );

			// アクションデータ部分を0xffで埋める
			memset( pc-1, -1, (int)pc2 - (int)pc + 2 );
		}
	}
	pc = pc2 = data;
	for(;;){
		if ( pc2[0] != -1 ){
			pc[0] = pc2[0];
			if ( pc[0] == '\0' ) break;
			pc++;
			pc2++;
			continue;
		}

		for(;;){
			if ( pc2[0] != -1 ) break;
			pc2++;
		}
	}
	for(i=0;i<LineNum;i++){
		getMemoLine( &sCv.Msg[i][0], sizeof( sCv.Msg[0] ), data, i, LineLen );
	}

	//２行ラインの时
	if ( serverRequestWinWindowType == WINDOW_MESSAGETYPE_MESSAGEANDTWOLINEINPUT ){
		return openMenuSrGeneralWindow( x, y, w, h, GraNo, BottonType, sCv.Msg, LineNum, Interval, sCv.SelCnt,
			2, Cv_InputPos, LineLen, 2, GRL_OPT_INPUTBACK, Cv_ReturnFunc, NULL, OPENMENUWINDOW_HIT, 0 );
	}

	return openMenuSrGeneralWindow( x, y, w, h, GraNo, BottonType, sCv.Msg, LineNum, Interval, sCv.SelCnt,
		(InputAreaH?1:0), (InputAreaH?Cv_InputPos:NULL), LineLen, 2, GRL_OPT_NONE, Cv_ReturnFunc, NULL, OPENMENUWINDOW_HIT, 0 );
}

//====================================
// 医者ウィンドウ部

BOOL Ij_ExtraFunc( WINDOW_INFO *wininfo, int ButtonOver, int StrOver, int flag )
{
	// この关数は、ウィンドウの处理中のみ使用するのでwIが使用可能
	char cost[64];
	int LineLen = GeneralWindowStrLen(wininfo->sx);
	int LineNum = GeneralWindowStrLineNum( wininfo->sy, 1, 16 );
	int i;

	if( ButtonOver ) strcpy( OneLineInfoStr, ML_STRING(438, "取消治疗。") );

	// お值段表示
	for( i = 0; i <= MAX_PATIENT ; i++ ){
		if( patientList[i].lv != 0 ){

			sprintf( cost, "%7d G", patientList[i].cost );                     //MLHIDE
			StockFontBuffer( wininfo->wx+304+MENU_DOCTOR_OFX, wininfo->wy+GENERAL_STRPOS_Y+4+( 16*(i+2) ),
				FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, cost, 0, BoxColor );

			if( i < MAX_PATIENT ){
				// コンディション表示
				StockDispBuffer( wininfo->wx+36+MENU_DOCTOR_OFX, wininfo->wy+GENERAL_STRPOS_Y+4+( 16*(i+2) )+16/2,
					DISP_PRIO_WIN2, conditionColorTbl[getHelthColor( patientList[i].injuryLv )], 0 );
			}
		}
	}

	// 所持金额表示
	sprintf( cost, ML_STRING(833, "               所持金币 %7d G"), pc.gold );
	StockFontBuffer( wininfo->wx+GENERAL_STRPOS_X, wininfo->wy+GENERAL_STRPOS_Y+4+( 16*(i+3) ),
		FONT_PRIO_WIN, FONT_KIND_MIDDLE, FONT_PAL_WHITE, cost, 0, BoxColor );

	if ( StrOver >= LineNum ) StrOver = -1;
	if ( sCv.Msg[StrOver][0] == '\0' ) StrOver = -1;

	// 选择项目の上なら
	if ( StrOver >= sCv.SelCnt && StrOver <= ( MAX_PATIENT + 2 ) ){
		// １行インフォ表示
		// 各キャラ
		if ( StrOver == ( MAX_PATIENT + 2 ) ){
			if( injuryFlag ){
				strcpy( OneLineInfoStr, MWONELINE_DOCTOR_ALL_ON );
			}else{
				strcpy( OneLineInfoStr, MWONELINE_DOCTOR_ALL_OFF );
			}
		}else if( patientList[StrOver-sCv.SelCnt].injuryLv == 0 ){
			strcpy( OneLineInfoStr, MWONELINE_DOCTOR_NAME_OFF );
		}else{
			strcpy( OneLineInfoStr, MWONELINE_DOCTOR_NAME_ON );
		}
	}

	return FALSE;
}

ACTION *openInjuryWindow( short x, short y, short w, short h, int GraNo, int BottonType, int SeqNo, int ObjIndex, char *data )
{
	int i,j;

	int BtnAreaH, Interval = 16;

	int LineLen;
	int LineNum;

	int totalcost;
	char tmp[256];

	// アクションの初期化
	initGrl_Action();

	// サーバ情报保存
	sCv.WinType = serverRequestWinWindowType;
	sCv.SeqNo = SeqNo,	sCv.ObjIndex = ObjIndex;

	// キャンセルボタンを付ける
	BottonType = 0x02;

	// 信息生成
	BtnAreaH = 0;
	if( BottonType ) BtnAreaH = GENERAL_BUTTON_AREA_H;

	/* 选择用にテキストを生成 */
	// 怪我管理用のバッファを一旦クリア
	for( i = 0 ; i < ( MAX_PATIENT + 1 ) ; i++ ) patientList[i].lv = 0;

	// 怪我の状态を取得（ついでにかかる费用も）
	totalcost = 0;
	injuryFlag = 0;
	for( i = 0, j = 1 ; i < MAX_PATIENT; i++ ){
		char str[256];

		patientList[i].lv       = getIntegerToken( data, '|', j++ );
		if( patientList[i].lv < 0 ){
			patientList[i].lv = 0;
			break;
		}
		patientList[i].injuryLv = getIntegerToken( data, '|', j++ );
		getStringToken( data, '|', j++, sizeof( str ) - 1, str );
		makeRecvString( str );
		if( strlen( str ) <= CHAR_NAME_LEN ){
			strcpy( patientList[i].name, str );
		}else{
			strcpy( patientList[i].name, "???" );                              //MLHIDE
		}
		patientList[i].cost     = getIntegerToken( data, '|', j++ );
		totalcost += patientList[i].cost;
		injuryFlag += patientList[i].injuryLv;
	}
	patientList[MAX_PATIENT].lv = 1;	// ダミー项目（一起治疗を选择できるように）
	patientList[MAX_PATIENT].cost = totalcost;

	// テキスト文字列を生成
	sprintf( tmp, ML_STRING(834, "         请选择治疗对象") );
	strcpy( sCv.Msg[0], tmp );
	sprintf( tmp, "   Health   LV        Name             Gold" );       //MLHIDE
	strcpy( sCv.Msg[1], tmp );
	for( i = 0 ; i < MAX_PATIENT ; i++ ){
		// 选择肢あり？
		if( !patientList[i].lv ){
			// ヌル文字に
			sCv.Msg[i+2][0] = '\0';
			continue;
		}
		sprintf( tmp, "           %3d  %-16s", patientList[i].lv, patientList[i].name ); //MLHIDE
		strcpy( sCv.Msg[i+2], tmp );
	}
	// 一起治疗选择肢の设定
	sprintf( tmp, ML_STRING(835, "           一起治疗") );
	strcpy( sCv.Msg[i+2], tmp );
	sprintf( tmp, "\0" );                                                //MLHIDE
	strcpy( sCv.Msg[i+3], tmp );
	strcpy( sCv.Msg[i+4], "" );

	sCv.SelCnt = 2;

	LineLen = GeneralWindowStrLen(w);
	LineNum = sCv.SelCnt + MAX_PATIENT + 1 + 2;

	return openMenuSrGeneralWindow( x, y, w, h, GraNo, BottonType, sCv.Msg, LineNum, Interval, sCv.SelCnt,
		0, NULL, LineLen, 2, GRL_OPT_NONE, Cv_ReturnFunc, Ij_ExtraFunc, OPENMENUWINDOW_HIT, 0 );
}



//====================================//
//		マップ名ウィンドウ			  //
//====================================//

// ウィンドウ处理 *********************//

// ウィンドウ处理 ++++
BOOL MenuWindowMapNameBf( int mouse )
{
	if (mouse==WIN_INIT){
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumMapNameText].Switch )->text, mapName );
		wI->sw[EnumMapNameText].ofx = ( wI->sx - GetStrWidth( mapName, FONT_KIND_MIDDLE ) ) >> 1;
		return TRUE;
	}

	if( fieldInfoTime+2000 <= GetTickCount() ){
		wI->flag |= WIN_INFO_DEL;
		fieldInfoTime = 0;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		return TRUE;
	}

	return TRUE;
}

BOOL MenuWindowMapNameAf( int mouse )
{
	displayMenuWindow();

	MenuWindowCommonDraw( GID_InfoWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

//========================================
// 对象选择
//========================================
static char PlayerSelName[10][CHAR_NAME_LEN+1];

static int PlayerSelCnt;
static int PlayerSelDispNo;

static char *TargetSelData;

static void (*TargetSelReturnFuc)( int ret, char *str );

static void MoveTargetSelDispText( int start )
{
	strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSelTargetText0].Switch )->text, PlayerSelName[start+0] );
	strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSelTargetText1].Switch )->text, PlayerSelName[start+1] );
	strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSelTargetText2].Switch )->text, PlayerSelName[start+2] );
	strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSelTargetText3].Switch )->text, PlayerSelName[start+3] );
	strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSelTargetText4].Switch )->text, PlayerSelName[start+4] );
}

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openTargetSelWindow( char *lTargetSelData, void (*lTargetSelReturnFuc)( int ret, char *str ) )
{
	char str[256];
	int i,j;

	// パラメータ初期化
	PlayerSelCnt = 0;

	for( i = 0; i < 10; i++ ) PlayerSelName[i][0] = '\0';

	// 布ティや目の前のキャラ达の情报を选择项目に追加
	j = 1;
	for( i = 0; i < 10; i++ ){
		if( PlayerSelCnt >= 10 ) break;

		if( getStringToken( lTargetSelData, '|', j++, sizeof( str ) - 1, str ) == 1 ) break;

		makeRecvString( str );
		strcpy( PlayerSelName[PlayerSelCnt], str );
		PlayerSelCnt++;
	}

	TargetSelReturnFuc = lTargetSelReturnFuc;

	// 开いた时のＰＣの位置を记忆
	memoryMapGridPos( mapGx, mapGy );

	return openMenuWindow( MENU_WINDOW_TARGETSEL, OPENMENUWINDOW_HIT, 0 );
}

BOOL MenuWindowTargetSelBf( int mouse )
{
	if (mouse==WIN_INIT){
		int i;

		PlayerSelDispNo = 0;

		if ( PlayerSelCnt <= 5 ){
			for(i=EnumTargetSelScrollGra;i<=EnumTargetSelScrollRight;i++) wI->sw[i].Enabled = FALSE;
		}else{
			for(i=EnumTargetSelScrollGra;i<=EnumTargetSelScrollRight;i++) wI->sw[i].Enabled = TRUE;
		}

		// テキストの表示を变更
		MoveTargetSelDispText(PlayerSelDispNo);
	}

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if ( checkMoveMapGridPos( 1, 1 ) ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	return TRUE;
}

BOOL MenuWindowTargetSelAf( int mouse )
{
	wI->sw[EnumTargetSelScrollGra].Enabled = TRUE;
	if (PlayerSelCnt-5<=0) wI->sw[EnumTargetSelScrollGra].Enabled = FALSE;
	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wI->sw[EnumTargetSelScroll].Switch )->status&1 ){
		PlayerSelDispNo = ScrollVPointToNum( &wI->sw[EnumTargetSelScroll], PlayerSelCnt-5 );

		// テキストの表示を变更
		MoveTargetSelDispText(PlayerSelDispNo);
	}

	displayMenuWindow();

	MenuWindowCommonDraw( GID_CommonWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuSwitchTargetSelScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (PlayerSelDispNo>0){
			PlayerSelDispNo--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-1], PlayerSelCnt-5, PlayerSelDispNo );

		// テキストの表示を变更
		MoveTargetSelDispText(PlayerSelDispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSelScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (PlayerSelDispNo<PlayerSelCnt-5){
			PlayerSelDispNo++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-2], PlayerSelCnt-5, PlayerSelDispNo );

		// テキストの表示を变更
		MoveTargetSelDispText(PlayerSelDispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSelScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (PlayerSelDispNo>0){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		PlayerSelDispNo-=5;
		if (PlayerSelDispNo<0) PlayerSelDispNo = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-3], PlayerSelCnt-5, PlayerSelDispNo );

		// テキストの表示を变更
		MoveTargetSelDispText(PlayerSelDispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSelScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if ( (PlayerSelCnt-5>0) && (PlayerSelDispNo<PlayerSelCnt-5) ){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		PlayerSelDispNo+=5;
		if (PlayerSelDispNo>PlayerSelCnt-5) PlayerSelDispNo = PlayerSelCnt-5;
		if (PlayerSelDispNo<0) PlayerSelDispNo = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-4], PlayerSelCnt-5, PlayerSelDispNo );

		// テキストの表示を变更
		MoveTargetSelDispText(PlayerSelDispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSelScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		PlayerSelDispNo = WheelToMove( &wI->sw[no-5],
			 PlayerSelDispNo, PlayerSelCnt-5, mouse.wheel );
	}

	return ReturnFlag;
}

BOOL MenuSwitchTargetSelCancel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, ML_STRING(546, "中止。") );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_CancelButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_CancelButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_CancelButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSelTextButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	if ( no-EnumTargetSelTargetTextButton0 >= PlayerSelCnt ) return FALSE;

	//重なってたら枠表示
	if(flag & MENU_MOUSE_OVER) displaySwitchFrame( &wI->sw[no], flag, BoxColor );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSelReturnFuc){
			TargetSelReturnFuc( PlayerSelDispNo + no-EnumTargetSelTargetTextButton0, PlayerSelName[PlayerSelDispNo + no-EnumTargetSelTargetTextButton0] );
		}
		wI->flag |= WIN_INFO_DEL;

		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}








//========================================
// 对象选择１
//========================================
static int TargetSel1DispNo;
static int TargetSel1Num;

static char TargetSel1On;

static char **TargetSel1SelStr;
static char *TargetSel1SelColor;

static BOOL (*TargetSel1ReturnFunc)( int ret, char *str );
static BOOL (*xTargetSel1ReturnFunc)( int ret, char *str );

static void MoveTargetSel1DispText( int start )
{
	int i;
	for(i=0;i<5;i++){
		if (start+i>=TargetSel1Num) break;
		if (!TargetSel1SelStr) continue;
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel1TargetText0+i].Switch )->text, TargetSel1SelStr[start+i] );
		( (TEXT_SWITCH *)wI->sw[EnumTargetSel1TargetText0+i].Switch )->color = TargetSel1SelColor[start+i];
	}
	for(;i<5;i++){
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel1TargetText0+i].Switch )->text, "" );
	}
}

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openTargetSel1Window( char **SelStr, char *SelColor, int Num, BOOL (*ReturnFunc)( int ret, char *str ) )
{
	TargetSel1SelStr = SelStr;
	TargetSel1SelColor = SelColor;
	TargetSel1Num = Num;
	xTargetSel1ReturnFunc = ReturnFunc;

	return openMenuWindow( MENU_WINDOW_TARGETSEL1, OPENMENUWINDOW_HIT, 0 );
}

BOOL closeTargetSel1Window()
{
	if (TargetSel1ReturnFunc){
		TargetSel1ReturnFunc( TARGETSEL1_RETURN_DEATHFUNC, NULL );
	}

	return TRUE;
}

BOOL MenuWindowTargetSel1Bf( int mouse )
{
	if (mouse==WIN_INIT){
		TargetSel1DispNo = 0;
		// テキストの表示を变更
		MoveTargetSel1DispText(TargetSel1DispNo);
		TargetSel1On = 1;
		TargetSel1ReturnFunc = xTargetSel1ReturnFunc;
	}

	return TRUE;
}

BOOL MenuWindowTargetSel1Af( int mouse )
{
	int i;

	if (TargetSel1Num-5<=0){
		for(i=EnumTargetSel1ScrollGra;i<=EnumTargetSel1ScrollRight;i++) wI->sw[i].Enabled = FALSE;
	}else{
		for(i=EnumTargetSel1ScrollGra;i<=EnumTargetSel1ScrollRight;i++) wI->sw[i].Enabled = TRUE;
	}
	// つまみを移动中なら、表示开始位置を变更
	if ( ( (BUTTON_SWITCH *)wI->sw[EnumTargetSel1Scroll].Switch )->status&1 ){
		TargetSel1DispNo = ScrollVPointToNum( &wI->sw[EnumTargetSel1Scroll], TargetSel1Num-5 );

		// テキストの表示を变更
		MoveTargetSel1DispText(TargetSel1DispNo);
	}

	displayMenuWindow();

	MenuWindowCommonDraw( GID_CommonWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuSwitchTargetSel1ScrollUp( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (TargetSel1DispNo>0){
			TargetSel1DispNo--;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-1], TargetSel1Num-5, TargetSel1DispNo );

		// テキストの表示を变更
		MoveTargetSel1DispText(TargetSel1DispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_UpButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_UpButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_UpButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel1ScrollDown( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFTAUTO ){
		if (TargetSel1DispNo<TargetSel1Num-5){
			TargetSel1DispNo++;
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-2], TargetSel1Num-5, TargetSel1DispNo );

		// テキストの表示を变更
		MoveTargetSel1DispText(TargetSel1DispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_DownButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_DownButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_DownButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel1ScrollLeft( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSel1DispNo>0){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		TargetSel1DispNo-=5;
		if (TargetSel1DispNo<0) TargetSel1DispNo = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-3], TargetSel1Num-5, TargetSel1DispNo );

		// テキストの表示を变更
		MoveTargetSel1DispText(TargetSel1DispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_LeftButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_LeftButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_LeftButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel1ScrollRight( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH *Graph=(GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if ( (TargetSel1Num-5>0) && (TargetSel1DispNo<TargetSel1Num-5) ){
			// クリック音
			play_se( SE_NO_CLICK, 320, 240 );
		}else{
			// ＮＧ音（短い）
			play_se( SE_NO_NG, 320, 240 );
		}

		TargetSel1DispNo+=5;
		if (TargetSel1DispNo>TargetSel1Num-5) TargetSel1DispNo = TargetSel1Num-5;
		if (TargetSel1DispNo<0) TargetSel1DispNo = 0;

		// つまみを移动
		NumToScrollVMove( &wI->sw[no-4], TargetSel1Num-5, TargetSel1DispNo );

		// テキストの表示を变更
		MoveTargetSel1DispText(TargetSel1DispNo);

		ReturnFlag = TRUE;
	}

	Graph->graNo = GID_RightButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_RightButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_RightButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel1ScrollWheel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;

	// マウスが上にあるなら
	if ( flag & (MENU_MOUSE_OVER|MENU_MOUSE_DRAGOVER) ){
		// スクロールバー縦ホイール移动
		TargetSel1DispNo = WheelToMove( &wI->sw[no-5],
			 TargetSel1DispNo, TargetSel1Num-5, mouse.wheel );
	}

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel1Cancel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_TARGETSEL1_CANCEL );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSel1On && TargetSel1ReturnFunc){
			if ( TargetSel1ReturnFunc( TARGETSEL1_RETURN_CLOSE, NULL ) ){
				wI->flag |= WIN_INFO_DEL;
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}

			TargetSel1On = 0;
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_CancelButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_CancelButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_CancelButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel1TextButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	int Num = no-EnumTargetSel1TargetTextButton0 + TargetSel1DispNo;

	if ( no-EnumTargetSel1TargetTextButton0 >= TargetSel1Num ) return FALSE;

	//重なってたら枠表示
	if (flag & MENU_MOUSE_OVER){
		displaySwitchFrame( &wI->sw[no], flag, BoxColor );
		strcpy( OneLineInfoStr, MWONELINE_TARGETSEL1_NAME );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSel1On && TargetSel1ReturnFunc){
			if ( TargetSel1ReturnFunc( Num, TargetSel1SelStr[Num] ) ){
				wI->flag |= WIN_INFO_DEL;
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}

			TargetSel1On = 0;
		}

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}







//========================================
// 对象选择２
//========================================
static int TargetSel2DispNo;
static int TargetSel2Num;

static char TargetSel2On;

int HealthGraTbl[]={ GID_Health0, GID_Health1, GID_Health2, GID_Health3, GID_Health4 };

struct STRUCT_TARGETSEL2 *TargetSel2data;

static BOOL (*TargetSel2ReturnFunc)( int ret, char *str );

static void MoveTargetSel2DispText( int start )
{
	int i;
	for(i=0;i<6;i++){
		if (start+i>=TargetSel2Num) break;
		wI->sw[EnumTargetSel2TargetHealth0+i].Enabled = FALSE;
		( (TEXT_SWITCH *)wI->sw[EnumTargetSel2TargetText0+i].Switch )->text[0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumTargetSel2Targetlv0+i].Switch )->text[0] = '\0';
		( (TEXT_SWITCH *)wI->sw[EnumTargetSel2Targetlpfp0+i].Switch )->text[0] = '\0';
		if (!TargetSel2data[start+i].name) continue;
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel2TargetText0+i].Switch )->text, "           %s", TargetSel2data[start+i].name ); //MLHIDE
		( (TEXT_SWITCH *)wI->sw[EnumTargetSel2TargetText0+i].Switch )->color = TargetSel2data[start+i].color;

		wI->sw[EnumTargetSel2TargetHealth0+i].Enabled = TRUE;
		( (GRAPHIC_SWITCH *)wI->sw[EnumTargetSel2TargetHealth0+i].Switch )->graNo = HealthGraTbl[ getHelthColor( TargetSel2data[start+i].health ) ];

		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel2Targetlv0+i].Switch )->text,
			"%10d", TargetSel2data[start+i].lv );                              //MLHIDE
		sprintf( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel2Targetlpfp0+i].Switch )->text,
			"                            %6d/%6d %6d/%6d",                     //MLHIDE
			TargetSel2data[start+i].lp, TargetSel2data[start+i].maxlp,
			TargetSel2data[start+i].fp, TargetSel2data[start+i].maxfp );
	}
	for(;i<6;i++){
		wI->sw[EnumTargetSel2TargetHealth0+i].Enabled = FALSE;
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel2TargetText0+i].Switch )->text, "" );
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel2Targetlv0+i].Switch )->text, "" );
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumTargetSel2Targetlpfp0+i].Switch )->text, "" );
	}
}

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

ACTION *openTargetSel2Window( struct STRUCT_TARGETSEL2 *Data, int Num, BOOL (*ReturnFunc)( int ret, char *str ) )
{
	TargetSel2data = Data;
	TargetSel2Num = Num;
	TargetSel2ReturnFunc = ReturnFunc;

	return openMenuWindow( MENU_WINDOW_TARGETSEL2, OPENMENUWINDOW_HIT, 0 );
}

BOOL closeTargetSel2Window()
{
	if (TargetSel2ReturnFunc){
		TargetSel2ReturnFunc( TARGETSEL2_RETURN_DEATHFUNC, NULL );
	}

	return TRUE;
}

BOOL MenuWindowTargetSel2Bf( int mouse )
{
	if (mouse==WIN_INIT){
		// テキストの表示を变更
		MoveTargetSel2DispText(TargetSel2DispNo);
		TargetSel2On = 1;
	}

	return TRUE;
}

BOOL MenuWindowTargetSel2Af( int mouse )
{
	displayMenuWindow();

	MenuWindowCommonDraw( GID_CommonWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

//--------------------------------------------------------
// ボタン处理
//--------------------------------------------------------

BOOL MenuSwitchTargetSel2Cancel( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_TARGETSEL2_CANCEL );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSel2On && TargetSel2ReturnFunc){
			if ( TargetSel2ReturnFunc( TARGETSEL2_RETURN_CLOSE, NULL ) ){
				wI->flag |= WIN_INFO_DEL;
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}
			TargetSel2On = 0;
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BigCancelButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigCancelButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigCancelButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel2Back( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	GRAPHIC_SWITCH	*Graph = (GRAPHIC_SWITCH *)wI->sw[no].Switch;

	//重なってたら一行インフォ表示
	if(flag & MENU_MOUSE_OVER) strcpy( OneLineInfoStr, MWONELINE_TARGETSEL2_BACK );

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSel2On && TargetSel2ReturnFunc){
			if ( TargetSel2ReturnFunc( TARGETSEL2_RETURN_BACK, NULL ) ){
				wI->flag |= WIN_INFO_DEL;
				// ウィンドウ关闭音
				play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
			}
			TargetSel2On = 0;
		}

		ReturnFlag=TRUE;
	}

	Graph->graNo = GID_BigBackButtonOn;
	if( flag & MENU_MOUSE_OVER ) Graph->graNo = GID_BigBackButtonOver;
	if( flag & MENU_MOUSE_LEFTHOLD ) Graph->graNo = GID_BigBackButtonOff;

	return ReturnFlag;
}

BOOL MenuSwitchTargetSel2TextButton( int no, unsigned int flag )
{
	BOOL ReturnFlag=FALSE;
	int Num = no-EnumTargetSel2TargetTextButton0 + TargetSel2DispNo;

	if ( no-EnumTargetSel2TargetTextButton0 >= TargetSel2Num ) return FALSE;

	//重なってたら枠表示
	if (flag & MENU_MOUSE_OVER){
		displaySwitchFrame( &wI->sw[no], flag, BoxColor );
		strcpy( OneLineInfoStr, MWONELINE_TARGETSEL2_NAME );
	}

	//押されたとき
	if( flag & MENU_MOUSE_LEFT ){
		if (TargetSel2On && TargetSel2ReturnFunc){
			if ( TargetSel2ReturnFunc( Num, TargetSel2data[Num].name ) ) wI->flag |= WIN_INFO_DEL;
			TargetSel2On = 0;

			// 决定音b（ボタンクリック时）
			play_se( SE_NO_OK2, 320, 240 );
		}

		ReturnFlag=TRUE;
	}

	return ReturnFlag;
}




//========================================
// ショップトップ
//========================================

// ボタン处理关数 *********************//

//--------------------------------------------------------
// ウインドウ处理
//--------------------------------------------------------

struct SHOPTOPMEMBER{
	int SvType;						// ウィンドウ种类
	int GraNo;						// 表示するキャラのＩＤ
	char *Title;					// タイトル文字列へのポインタ
	char **Info;					// 信息の文字列配列へのポインタ
	char **Sel;						// 选择对象の文字列配列へのポインタ
	char InfoNum;					// 信息の文字列配列の配列数
	char SelNum;					// 选择对象の文字列配列の配列数
	char **OneLine;					// 一行インフォへのポインタ
	void (*ReturnFuc)( int Num );	// ボタンが押されたとき、そのボタン番号を引数にとる、选择されなかった场合、-1を引数にとる
	char closeflag;
};

static struct SHOPTOPMEMBER ShopTop;

#define SHOPTOP_FONTSIZE 12

ACTION *openShopTop( char *Title, int GraNo, char **Info, char InfoNum, char **Sel, char SelNum, char **OneLine, void (*ReturnFuc)( int Num ) )
{
	ShopTop.SvType = serverRequestWinWindowType;
	ShopTop.Title = Title;
	ShopTop.GraNo = GraNo;
	ShopTop.Info = Info;
	ShopTop.InfoNum = InfoNum;
	ShopTop.Sel = Sel;
	ShopTop.SelNum = SelNum;
	ShopTop.ReturnFuc = ReturnFuc;
	ShopTop.OneLine = OneLine;

	if (WindowFlag[MENU_WINDOW_SHOPTOP].wininfo){
		WindowFlag[MENU_WINDOW_SHOPTOP].wininfo->flag |= WIN_INFO_DEL;
		ShopTop.closeflag = 0;
	}

	return openMenuWindow( MENU_WINDOW_SHOPTOP, OPENMENUWINDOW_HIT, 0 );
}

BOOL MenuWindowShopTopBf( int Mouse )
{
	if ( Mouse == WIN_INIT ){
		strcpy( ( (TEXT_SWITCH *)wI->sw[EnumShopTopTxtTitle].Switch )->text, ShopTop.Title );
		wI->sw[EnumShopTopTxtTitle].ofx = ( ( wI->sx-GetStrWidth( ShopTop.Title, FONT_KIND_SMALL ) )>>1 ) + 6;

		( (ACTION_SWITCH *)wI->sw[EnumShopTopAnmChara].Switch )->ActionAdd->anim_chr_no = ShopTop.GraNo;
		( (ACTION_SWITCH *)wI->sw[EnumShopTopAnmChara].Switch )->ActionAdd->anim_ang = 5;

		if (ShopTop.SelNum){
			wI->sw[EnumShopTopTextHit].sy = ( (ShopTop.SelNum-1) * (SHOPTOP_FONTSIZE+2) ) + SHOPTOP_FONTSIZE;
		}else{
			wI->sw[EnumShopTopTextHit].sy = 0;
		}
		ShopTop.closeflag = 1;
	}

	// ウィンドウを开いた位置から数グリッド离れたらウィンドウを关闭
	if ( checkMoveMapGridPos( 2, 2 ) ){
		wI->flag |= WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
		ShopTop.ReturnFuc(-1);
	}

	return TRUE;
}

BOOL MenuWindowShopTopAf( int mouse )
{
	int i;

	displayMenuWindow();

	for(i=0;i<ShopTop.InfoNum;i++){
		if (ShopTop.Info[i]) StockFontBuffer( wI->wx+87, wI->wy+40+i*13, FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, ShopTop.Info[i], 0, 0 );
	}

	for(i=0;i<ShopTop.SelNum;i++){
		if (ShopTop.Sel[i]){
			StockFontBuffer( wI->wx+wI->sw[EnumShopTopTextHit].ofx, wI->wy+wI->sw[EnumShopTopTextHit].ofy+i*(SHOPTOP_FONTSIZE+2),
				FONT_PRIO_WIN, FONT_KIND_SIZE_12, FONT_PAL_WHITE, ShopTop.Sel[i], 0, 0 );
		}
	}

	MenuWindowCommonDraw( GID_CommonWindow, wI->wx, wI->wy, wI->sx, wI->sy, DISP_PRIO_WIN2, 0xffffffff, 0x80ffffff );

	if ( serverRequestWinWindowType != ShopTop.SvType ){
		wI->flag|=WIN_INFO_DEL;
		// ウィンドウ关闭音
		play_se( SE_NO_CLOSE_WINDOW, 320, 240 );
	}

	//フォント用バッファの区切り
	FontBufCut(FONT_PRIO_WIN);

	return TRUE;
}

BOOL MenuWindowShopTopClose()
{
	if (ShopTop.closeflag){
		if ( serverRequestWinWindowType == ShopTop.SvType ) serverRequestWinWindowType = -1;
	}
	return TRUE;
}

BOOL MenuWindowShopTopTextHit( int no, unsigned int flag )
{
	short x,y;
	int i;

	if ( flag & MENU_MOUSE_OVER ){
		for(i=0;i<ShopTop.SelNum;i++){
			x = wI->wx+wI->sw[EnumShopTopTextHit].ofx;
			y = wI->wy+wI->sw[EnumShopTopTextHit].ofy+i*(SHOPTOP_FONTSIZE+2);
			if ( MakeHitBox( x, y, x+wI->sw[EnumShopTopTextHit].sx, y+SHOPTOP_FONTSIZE, -1 ) ){
				// 枠表示
				StockBoxDispBuffer( x-2, y-2, x+wI->sw[EnumShopTopTextHit].sx+2, y+SHOPTOP_FONTSIZE+2, DISP_PRIO_WIN2, BoxColor, 0 );
				strcpy( OneLineInfoStr, ShopTop.OneLine[i] );

				if (mouse.onceState&MOUSE_LEFT_CRICK){
					if (ShopTop.ReturnFuc) ShopTop.ReturnFuc(i);

					// 决定音c（文字等クリック时）
					play_se( SE_NO_OK3, 320, 240 );

					wI->flag |= WIN_INFO_DEL;
					break;
				}
			}
		}
		return TRUE;
	}

	return FALSE;
}

//--------------------------------------------------------
// 各ショップトップの处理
//--------------------------------------------------------

static char *pShopTopInfo[5];

//========================================
// アイテムショップ

static char *ItemShopTopTextBuy = LANG_MENUSERVERREQUEST_CPP_BUY;
static char *ItemShopTopTextSell = LANG_MENUSERVERREQUEST_CPP_SELL;
static char *ItemShopTopTextCancel = LANG_MENUSERVERREQUEST_CPP_CANCEL;
static char *pItemShopTopText[3];
static char *ItemShopTopOneLineBuy = MWONELINE_SHOP_BUYSELLTOPBUY;
static char *ItemShopTopOneLineSell = MWONELINE_SHOP_BUYSELLTOPSELL;
static char *ItemShopTopOneLineCancel = MWONELINE_SHOP_BUYSELLTOPCANCEL;
static char *pItemShopTopOneLine[3];

char ItemShopTopbtnTbl[3];
extern int itemShopNpcGraNo;
extern char serverRequestWinStr[30][81];			// 文字列格纳バッファ

void ItemShopTopReturn( int Num )
{
	char msg[16], msg2[16];

	if (Num<0) return;
	if (Num>1) return;

	sprintf( msg, "%d", ItemShopTopbtnTbl[Num] );                        //MLHIDE

	makeSendString( msg, msg2, sizeof( msg2 )-1 );
	nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );
}

ACTION *openItemShopTop( char *data )
{
	int i, flag;
	char str[256];

	itemShopNpcGraNo = getIntegerToken( data, '|', 1 );

	getStringToken( data, '|', 2, 21, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 5; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
		pShopTopInfo[i] = serverRequestWinStr[1+i];
	}

	flag = getIntegerToken( data, '|', 4 );

	i = 0;
	if( flag & 1 ){
		pItemShopTopText[i] = ItemShopTopTextBuy;
		pItemShopTopOneLine[i] = ItemShopTopOneLineBuy;
		ItemShopTopbtnTbl[i] = 1;
		i++;
	}
	if( flag & 2 ){
		pItemShopTopText[i] = ItemShopTopTextSell;
		pItemShopTopOneLine[i] = ItemShopTopOneLineSell;
		ItemShopTopbtnTbl[i] = 2;
		i++;
	}
	pItemShopTopText[i] = ItemShopTopTextCancel;
	pItemShopTopOneLine[i] = ItemShopTopOneLineCancel;
	ItemShopTopbtnTbl[i] = 3;
	i++;

	return openShopTop( serverRequestWinStr[0], itemShopNpcGraNo, pShopTopInfo, 5, pItemShopTopText, i, pItemShopTopOneLine, ItemShopTopReturn );
}

//========================================
// スキルショップ

static char *pSkillShopTopText[3]={ LANG_MENUSERVERREQUEST_CPP_LEARN, LANG_MENUSERVERREQUEST_CPP_FORGET, LANG_MENUSERVERREQUEST_CPP_SELCANCEL };
static char *pSkillShopTopOneLine[3]={ MWONELINE_SKILLMASTER_LEARN, MWONELINE_SKILLMASTER_FORGET, MWONELINE_SKILLMASTER_SELCANCEL };

extern int skillMasterGraNo;

void SkillShopTopReturn( int Num )
{
	char msg[16], msg2[16];

	if (Num<0) return;
	if (Num>1) return;

	sprintf( msg, "%d", Num+1 );                                         //MLHIDE

	makeSendString( msg, msg2, sizeof( msg2 )-1 );
	nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );
}

ACTION *openSkillShopTop( char *data )
{
	int i;
	char str[256];

	skillMasterGraNo = getIntegerToken( data, '|', 1 );

	getStringToken( data, '|', 2, 21, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 5; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
		pShopTopInfo[i] = serverRequestWinStr[1+i];
	}

	return openShopTop( serverRequestWinStr[0], skillMasterGraNo, pShopTopInfo, 5, pSkillShopTopText, 3, pSkillShopTopOneLine, SkillShopTopReturn );
}

//========================================
// 交换ＮＰＣ

static char *pTradeNpcTopText[3]={ "交换", "什么都不做" };
static char *pTradeNpcTopOneLine[3]={ MWONELINE_SHOP_TRADETOPTRADE, MWONELINE_SHOP_TRADETOPCANCEL };

extern int tradeNpcTopGraNo;

void TradeNpcTopReturn( int Num )
{
	char msg[16], msg2[16];

	if (Num<0) return;
	if (Num>0) return;

	strcpy( msg, "1" );                                                  //MLHIDE

	makeSendString( msg, msg2, sizeof( msg2 )-1 );
	nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2 );
}

ACTION *openpTradeNpcTop( char *data )
{
	int i;
	char str[256];

	tradeNpcTopGraNo = getIntegerToken( data, '|', 1 );

	getStringToken( data, '|', 2, 21, serverRequestWinStr[0] );
	makeRecvString( serverRequestWinStr[0] );

	getStringToken( data, '|', 3, sizeof( str )-1, str );
	makeRecvString( str );
	for( i = 0; i < 5; i++ ){
		getMemoLine( serverRequestWinStr[1+i], sizeof( serverRequestWinStr[0] ), str, i, 20 );
		pShopTopInfo[i] = serverRequestWinStr[1+i];
	}

	return openShopTop( serverRequestWinStr[0], tradeNpcTopGraNo, pShopTopInfo, 5, pTradeNpcTopText, 2, pTradeNpcTopOneLine, TradeNpcTopReturn );
}

//========================================
// アイテムカウントＮＰＣ

static char *CountNpcTopTextGiv = LANG_MENUSERVERREQUEST_CPP_GIVEITEM;
static char *CountNpcTopTextRec = LANG_MENUSERVERREQUEST_CPP_RECVITEM;
static char *pCountNpcTopText[2]={ NULL, LANG_MENUSERVERREQUEST_CPP_CANCEL };
static char *pCountNpcTopOneLine[3]={ MWONELINE_SHOP_TRADETOPTRADE, MWONELINE_SHOP_TRADETOPCANCEL };

extern int itemCountNpcTopGraNo;

void CountNpcTopReturn( int Num )
{
	char msg[16], msg2[16];

	if (Num<0) return;
	if (Num>1) return;

	sprintf( msg, "%d", Num+1);                                          //MLHIDE

	makeSendString( msg, msg2, sizeof( msg2 )-1 );
	nrproto_WN_send( sockfd, mapGx, mapGy, serverRequestWinSeqNo, serverRequestWinObjIndex, 0, msg2);
}

ACTION *openpCountNpcTopTop( char *data )
{
	int i, flag;
	char str[256];

	// ＮＰＣのグラフィック番号设定
	itemCountNpcTopGraNo = getIntegerToken( data, '|', 1);

	// ＮＰＣの名称设定
	getStringToken( data, '|', 2, 21, serverRequestWinStr[0]);
	makeRecvString( serverRequestWinStr[0]);

	// 给予物品/受け取る设定
	flag = getIntegerToken( data, '|', 3);

	pCountNpcTopText[0] = CountNpcTopTextGiv;
	if (flag) pCountNpcTopText[0] = CountNpcTopTextRec;

	// コメント设定
	getStringToken( data, '|', 4, sizeof( str), str);
	makeRecvString( str);
	for( i = 0; i < 5; i++){
		getMemoLine( serverRequestWinStr[i+1], sizeof( serverRequestWinStr[0]), str, i, 20);
		pShopTopInfo[i] = serverRequestWinStr[1+i];
	}

	return openShopTop( serverRequestWinStr[0], itemCountNpcTopGraNo, pShopTopInfo, 5, pCountNpcTopText, 2, pCountNpcTopOneLine, CountNpcTopReturn );
}
