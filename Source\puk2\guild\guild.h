﻿//

//
//	クライアント侧
//


// 家族成员データ构造体
typedef struct {
	short useFlag;					// 使用フラグ
	short onlineFlag;				// ONライン＆サーバ番号
	int id;							// 登録番号→ここ变える必要あり
	int lv;							// 等级
	int graNo;						// 颜の画像番号
	char name[CHAR_NAME_LEN+1];			// 名称
	char freeName[CHAR_FREENAME_LEN+1];	// 自己称号
} GUILD_MEMBER_INFO;

// 家族成员はとりあえず虫食いありで１００人分登録（インデックスだけ先に进める）
// で、メール呼ぶときにだけメンバー表を贳い（この辺りどうするか微妙）、メール送る
// メール送ったあとは通常动作とおんなじ
// （送るプロトコルは、多分别に用意する必要あり）

// 鲭侧のメンバーデータの更新频度、いろいろ调整の必要あり
// （ONライン、OFFラインとかあるから）
// メール侧含めて更新频度を落とす？


/*
#define LENGTH_OF_GUILD_NAME (20 * 2)
#define LENGTH_OF_GUILD_ROOM_NAME (20 * 2)
#define LENGTH_OF_GUILD_TITLE (20 * 2)
#define LENGTH_OF_GUILD_BRIEF (120 * 2)
#define AMOUNT_OF_GUILD_MEMBER 64
#define LENGTH_OF_MONSTER_NAME (20 * 2)
#define AMOUNT_OF_FOOD_BOX 20
#define AMOUNT_OF_ITEM_BOX 20


typedef struct {
	int itemID;
	int date;
	int mapID;
} BUYING_IN;

typedef struct {
	int toGuildID;
	int toGuildTitleID;
} GUILD_ADDRESS;

typedef struct {
	char title[LENGTH_OF_GUILD_TITLE];
	int titleID;
	BOOL isSignUp;
	BOOL isExcommunication;
	BOOL isGiveFood;
	BOOL isItemBox;
	BOOL isBBSDelete;
} GUILD_TITLE;

typedef strucct {
	char CDKey[CDKEYLEN];
	int titleID;
	int sequence;
	int closenessMonster0;
	int closenessMonster1;
	int closenessMonster2;
} GUILD_MEMBER;


	


typedef struct {
	int guildID;
	char ownerCDKey[CDKEYLEN];
	int floorID;
	BOOL isUse;
	char monsterName[LENGTH_OF_MONSTER_NAME];
	int monsterID;
	int earth;
	int water;
	int fire;
	int wind;
	int age;
	int maxIntake;
	int hunger;
	int favorite0Top;
	int favorite0Bottom;
	int favorite1Top;
	int favorite1Bottom;
	int favorite2Top;
	int favorite2Bottom;
	int favorite3Top;
	int favorite3Bottom;
	int favorite4Top;
	int favorite4Bottom;
	int time;
	int status;
	int escape;
	int fool0;
	int food1;
	int food2;
	int food3;
	int food4;
	int mood;
	int looks;
	int manizing;
	int cleverness;
	int foodBox[AMOUNT_OF_FOOD_BOX];
	int itemBOx[AMOUNT_OF_ITEM_BOX];
} BREEDING_ROOM;



typedef struct {
	char guildName[LENGTH_OF_GUILD_NAME];
	int serverNumber;
	int roomID;
	char ownerCDKey[CDKEYLEN];
	char guildRoomName[LENGTH_OF_GUILD_ROOM_NAME];
	GUILD_TITLE guildTitle[AMOUNT_OF_GUILD_MEMBER];
	char brief[LENGTH_OF_GUILD_BRIEF];
	int commission;
	int guildMark;
	GUILD_MEMBER members[AMOUNT_OF_GUILD_MEMBER];
	BREEDING_ROOM room0;
	BREEDING_ROOM room1;
	BREEDING_ROOM room2;
} GUILD;

*/
