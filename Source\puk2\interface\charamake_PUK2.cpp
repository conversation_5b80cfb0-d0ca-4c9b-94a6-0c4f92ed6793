﻿
void initMakeChara_PUK2();

void initMakeCharaSelect_PUK2( char Init );
int MakeCharaSelect_PUK2();

void initMakeCharaEdit_PUK2();
int MakeCharaEdit_PUK2();

BOOL MouseHitCheck( const int *Pos );

//========================================
// キャラクター作成メインループ

void makeCharacterProc_PUK2( void )
{
	int ret;

	// 初期化
	if( SubProcNo == 0 ) initMakeChara_PUK2();

	// キャラ画像选择の初期化
	if( SubProcNo == 1 ){		initMakeCharaSelect_PUK2(1);	SubProcNo = 3; }
	else if( SubProcNo == 2 ){	initMakeCharaSelect_PUK2(0);	SubProcNo++; }

	// キャラ画像选择处理
	if( SubProcNo == 3 ){
		ret = MakeCharaSelect_PUK2();	// バージョン２用
		// キャラ选择决定
		if( ret == 1 ) SubProcNo = 10;
		// 返回按键押された
		else if( ret == 2 ) ChangeProc( PROC_CHAR_SELECT, 10 );
	}

	// キャラのパラメータ设定の初期化
	if( SubProcNo == 10 && ret == 1 ){	initMakeCharaEdit_PUK2();	SubProcNo = 12; }
	else if( SubProcNo == 11 ){			initMakeCharaEdit_PUK2();	SubProcNo = 12; }

	if( SubProcNo == 12 ){
		ret = MakeCharaEdit_PUK2();
		if( ret == 1 ){
			SubProcNo = 30;
			// 以前のセーブデータを初期化
			resetCharacterList( selectPcNo );
			clearUserSetting( selectPcNo );
			if( saveNowState() )
			{
			}
			GetKeyInputFocus( NULL );
		}
		// キャンセル
		if( ret == 2 ) SubProcNo = 2;
	}


	// キャラ作成プロトコルを送信
	if( SubProcNo == 30 ){	initCreateChar();	SubProcNo = 31; }
	if( SubProcNo == 31 ){
		ret = createChar();
		if( ret == 1 ){
			maxPcNo++;
////			strcpy( gamestate_login_charname, newCharacterName );
////			ChangeProc( PROC_CHAR_LOGIN_STAT );
			// キャラクターセレクト画面へ返回
			ChangeProc( PROC_CHAR_SELECT );
			createCharFlag = 1;
		}
		else if( ret == 2 ) SubProcNo = 100;
	}

	// エラー
	if( SubProcNo == 100 ){	initCommonMsgWin();	SubProcNo++; }
	if( SubProcNo == 101 ){
#ifdef PUK3_ERRORMESSAGE_NUM
		if( commonMsgWin( INFOMSG_22 ) ){
#else
		if( commonMsgWin( ML_STRING(189, "创建角色失败。") ) ){
#endif
			// ＯＫボタンが押された
			ChangeProc( PROC_CHAR_SELECT, 10 );
		}
	}
}

void initMakeChara_PUK2()
{
	DeathAllAction();
	SubProcNo++;
}

//========================================
// キャラクターセレクト

static char MakeChara_PUK2Page;
static char MakeChara_PUK2Num;
static char MakeChara_PUK2Color;
static ACTION *MakeChara_Action[2];

void initMakeCharaSelect_PUK2( char Init )
{
	if (Init){
		MakeChara_PUK2Page = 0;
		if ( PackageVer >= PV_FIRST_VER2 ) MakeChara_PUK2Page = 1;

		MakeChara_PUK2Num = 0;
		MakeChara_PUK2Color = 0;
	}

	MakeChara_Action[0] = GetAction( PRIO_CHR, 0 );
	if (MakeChara_Action[0]){
		MakeChara_Action[0]->x = SymOffsetX + 517;
		MakeChara_Action[0]->y = SymOffsetY + 243;
		MakeChara_Action[0]->anim_ang = 5;
	}

	MakeChara_Action[1] = GetAction( PRIO_CHR, 0 );
	if (MakeChara_Action[1]){
		MakeChara_Action[1]->x = SymOffsetX + 517;
		MakeChara_Action[1]->y = SymOffsetY + 396;
		MakeChara_Action[1]->anim_ang = 5;
	}

	GetKeyInputFocus( NULL );
}

#ifdef TW_CHARA
const int SelectCharaAnimTbl[3][2][14] = {
	{
		{	SPR_000em,SPR_010em,SPR_020em,SPR_030em,SPR_040em,SPR_050em,SPR_060em,
			SPR_200em,SPR_210em,SPR_220em,SPR_230em,SPR_240em,SPR_250em,SPR_260em,	},
		{	SPR_400em,SPR_410em,SPR_420em,SPR_430em,SPR_440em,SPR_450em,SPR_460em,
			SPR_500em,SPR_510em,SPR_520em,SPR_530em,SPR_540em,SPR_550em,SPR_560em,	},
	},
	{
		{	SPR_600em,SPR_610em,SPR_620em,SPR_630em,SPR_640em,SPR_650em,SPR_660em,
			SPR_700em,SPR_710em,SPR_720em,SPR_730em,SPR_740em,SPR_750em,SPR_760em,	},
		{	SPR_600em,SPR_610em,SPR_620em,SPR_630em,SPR_640em,SPR_650em,SPR_660em,
			SPR_700em,SPR_710em,SPR_720em,SPR_730em,SPR_740em,SPR_750em,SPR_760em,	},
	},
	{
		{	TW_SPR_100em, TW_SPR_101em, TW_SPR_102em, TW_SPR_103em, TW_SPR_104em, TW_SPR_105em, TW_SPR_106em,
			100900, 100900, 100900, TW_SPR_107em, 100900, 100900, 100900, },
		{	TW_SPR_100em, TW_SPR_101em, TW_SPR_102em, TW_SPR_103em, TW_SPR_104em, TW_SPR_105em, TW_SPR_106em,
			100900, 100900, 100900, TW_SPR_107em, 100900, 100900, 100900, },
	},
};
#else
const int SelectCharaAnimTbl[2][2][14] = {
	{
		{	SPR_000em,SPR_010em,SPR_020em,SPR_030em,SPR_040em,SPR_050em,SPR_060em,
			SPR_200em,SPR_210em,SPR_220em,SPR_230em,SPR_240em,SPR_250em,SPR_260em,	},
		{	SPR_400em,SPR_410em,SPR_420em,SPR_430em,SPR_440em,SPR_450em,SPR_460em,
			SPR_500em,SPR_510em,SPR_520em,SPR_530em,SPR_540em,SPR_550em,SPR_560em,	},
	},
	{
		{	SPR_600em,SPR_610em,SPR_620em,SPR_630em,SPR_640em,SPR_650em,SPR_660em,
			SPR_700em,SPR_710em,SPR_720em,SPR_730em,SPR_740em,SPR_750em,SPR_760em,	},
		{	SPR_600em,SPR_610em,SPR_620em,SPR_630em,SPR_640em,SPR_650em,SPR_660em,
			SPR_700em,SPR_710em,SPR_720em,SPR_730em,SPR_740em,SPR_750em,SPR_760em,	},
	},
};
#endif


int CharaSelectGraPUK2[][14]={
	{
		PUK2_FACE_01,PUK2_FACE_02,PUK2_FACE_03,PUK2_FACE_04,PUK2_FACE_05,PUK2_FACE_06,PUK2_FACE_07,
		PUK2_FACE_08,PUK2_FACE_09,PUK2_FACE_10,PUK2_FACE_11,PUK2_FACE_12,PUK2_FACE_13,PUK2_FACE_14,
	},
	{
		PUK2_FACE_15,PUK2_FACE_16,PUK2_FACE_17,PUK2_FACE_18,PUK2_FACE_19,PUK2_FACE_20,PUK2_FACE_21,
		PUK2_FACE_22,PUK2_FACE_23,PUK2_FACE_24,PUK2_FACE_25,PUK2_FACE_26,PUK2_FACE_27,PUK2_FACE_28,
	},
#ifdef TW_CHARA
	{
		TW_FACE_01,TW_FACE_02,TW_FACE_03,TW_FACE_04,TW_FACE_05,TW_FACE_06,TW_FACE_07,
		TW_FACE_08,TW_FACE_09,TW_FACE_10,TW_FACE_11,TW_FACE_12,TW_FACE_13,TW_FACE_14,
	},
#endif
};
int CharaSelectGraPUK1[][14]={
	{
		RN_FACE_0,RN_FACE_1,RN_FACE_2,RN_FACE_3,RN_FACE_4,RN_FACE_5,RN_FACE_6,
		RN_FACE_7,RN_FACE_8,RN_FACE_9,RN_FACE_10,RN_FACE_11,RN_FACE_12,RN_FACE_13,
	},
	{
		V2_FACE_0,V2_FACE_1,V2_FACE_2,V2_FACE_3,V2_FACE_4,V2_FACE_5,V2_FACE_6,
		V2_FACE_7,V2_FACE_8,V2_FACE_9,V2_FACE_10,V2_FACE_11,V2_FACE_12,V2_FACE_13,
	},
};
int CharaSelectGraCG[][14]={
	{
		CG_FACE_0,	CG_FACE_1,	CG_FACE_2,	CG_FACE_3,	CG_FACE_4,	CG_FACE_5,	CG_FACE_6,	
		CG_FACE_10,	CG_FACE_11,	CG_FACE_12, CG_FACE_13,	CG_FACE_14,	CG_FACE_15,	CG_FACE_16,	
	},
	{
		CG2_FACE_SHADOW_PCM,CG2_FACE_SHADOW_PCM,CG2_FACE_SHADOW_PCM,CG2_FACE_SHADOW_PCM,CG2_FACE_SHADOW_PCM,CG2_FACE_SHADOW_PCM,CG2_FACE_SHADOW_PCM,
		CG2_FACE_SHADOW_PCF,CG2_FACE_SHADOW_PCF,CG2_FACE_SHADOW_PCF,CG2_FACE_SHADOW_PCF,CG2_FACE_SHADOW_PCF,CG2_FACE_SHADOW_PCF,CG2_FACE_SHADOW_PCF,
	},
};
const int CharaSelectGraPos[14][2] = {
	{ 111,169 },{ 111,243 },{ 111,317 },
	{ 179,133 },{ 179,207 },{ 179,281 },{ 179,355 },
	{ 315,169 },{ 315,243 },{ 315,317 },
	{ 247,133 },{ 247,207 },{ 247,281 },{ 247,355 },
};

const int CharaSelectGraHitxy[14][2] = {
	{  79,133 },{  79,207 },{  79,281 },
	{ 147, 97 },{ 147,171 },{ 147,245 },{ 147,319 },
	{ 283,133 },{ 283,207 },{ 283,281 },
	{ 215, 97 },{ 215,171 },{ 215,245 },{ 215,319 },
};
const int CharaSelectGraHitwh[2] = { 64, 72 };

// ＯＫボタン
const int CharaSelectGraOKHit[4] = { 256, 400,  65,  17 };
const int CharaSelectGraOK[3] = { PUK2_BUTTON_OK0, PUK2_BUTTON_OK1, PUK2_BUTTON_OK2 };

// Ｂａｃｋボタン
const int CharaSelectGraBackHit[4] = { 105, 400,  66,  17 };
const int CharaSelectGraBack[3] = { PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK1, PUK2_BUTTON_BACK2 };

// ページ切り替えボタン
#ifdef TW_CHARA
const int CharaSelectGraPageChangHitL[4] = { 186, 420,  18,  18 };
const int CharaSelectGraPageChangHitR[4] = { 222, 420,  18,  18 };
#else
const int CharaSelectGraPageChangHit[4] = { 203, 420,  18,  18 };
#endif
const int CharaSelectGraPageChang[2][3] = {
	{ PUK2_BUTTON_RIGHT0, PUK2_BUTTON_RIGHT1, PUK2_BUTTON_RIGHT2 },
	{ PUK2_BUTTON_LEFT0, PUK2_BUTTON_LEFT1, PUK2_BUTTON_LEFT2 },
};

// ページ
#ifdef TW_CHARA
const int CharaSelectGraPage[3] = { 245481, 245482,  245483 };
#else
const int CharaSelectGraPage[2] = { 245481, 245482 };
#endif


// キャラ向き变更范围
const int CharaSelectGraAnimFrame1LHit[4] = { 455, 138, 125, 125 };
const int CharaSelectGraAnimFrame2LHit[4] = { 455, 291, 125, 125 };

// 色切り替えボタン
const int CharaSelectGraColorChangLHit[4] = { 458, 268,  18,  18 };
const int CharaSelectGraColorChangL[3] = { PUK2_BUTTON_LEFT0, PUK2_BUTTON_LEFT1, PUK2_BUTTON_LEFT2 };
const int CharaSelectGraColorChangRHit[4] = { 559, 268,  18,  18 };
const int CharaSelectGraColorChangR[3] = { PUK2_BUTTON_RIGHT0, PUK2_BUTTON_RIGHT1, PUK2_BUTTON_RIGHT2 };

int MakeCharaSelect_PUK2()
{
	int i;
	struct BLT_MEMBER bm = {0};
	int ret = 0;

	char CharaSelectGraOKDraw = 0;
	char CharaSelectGraBackDraw = 0;
#ifdef TW_CHARA
	char CharaSelectGraPageChangeDrawL = 0;
	char CharaSelectGraPageChangeDrawR = 0;
#else
	char CharaSelectGraPageChangeDraw = 0;
#endif

	char CharaSelectGraColorChangLDraw = 0;
	char CharaSelectGraColorChangRDraw = 0;

	bm.rgba.rgba = 0x3fffffff;

	//========================================
	// 各ボタンの处理

	// 颜絵
	for(i=0;i<14;i++){
#ifdef TW_CHARA
		if (CharaSelectGraPUK2[MakeChara_PUK2Page][i] >= 206000 && CharaSelectGraPUK2[MakeChara_PUK2Page][i] <= 206300) {
			if (MakeChara_PUK2Num >= 7 && MakeChara_PUK2Num != 10) {
				MakeChara_PUK2Num = 0;
			}
			continue;
		}
#endif
		if ( MakeHitBox( SymOffsetX + CharaSelectGraHitxy[i][0], SymOffsetY + CharaSelectGraHitxy[i][1],
			SymOffsetX + CharaSelectGraHitxy[i][0]+CharaSelectGraHitwh[0], SymOffsetY + CharaSelectGraHitxy[i][1]+CharaSelectGraHitwh[1], -1 ) ){
			if( mouse.onceState & MOUSE_LEFT_CRICK ){
				MakeChara_PUK2Num = i;
				MakeChara_PUK2Color = 0;
				break;
			}
		}
	}

	// ＯＫボタン
	if ( MouseHitCheck(CharaSelectGraOKHit) ){
		CharaSelectGraOKDraw = 2;
		if( mouse.state & MOUSE_LEFT_CRICK ) CharaSelectGraOKDraw = 1;

		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			play_se( SE_NO_OK, 320, 240 );
			ret = 1;
		}
	}

	// Ｂａｃｋボタン
	if ( MouseHitCheck(CharaSelectGraBackHit) ){
		CharaSelectGraBackDraw = 2;
		if( mouse.state & MOUSE_LEFT_CRICK ) CharaSelectGraBackDraw = 1;

		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			play_se( SE_NO_BACK, 320, 240 );
			ret = 2;
		}
	}

#ifdef TW_CHARA
	// ページ切り替えボタン
	if (PackageVer >= PV_FIRST_VER2) {
		if (MouseHitCheck(CharaSelectGraPageChangHitR)) {
			CharaSelectGraPageChangeDrawR = 2;
			if (mouse.state & MOUSE_LEFT_CRICK) CharaSelectGraPageChangeDrawR = 1;

			if (mouse.onceState & MOUSE_LEFT_CRICK) {
				if (PackageVer < PV_PUK3) {
					if (MakeChara_PUK2Page < 1)
						++MakeChara_PUK2Page;
					else
						MakeChara_PUK2Page = 1;
				}
				else{
					if (MakeChara_PUK2Page < 2)
						++MakeChara_PUK2Page;
					else 
						MakeChara_PUK2Page = 2;
				}
			}
		}

		if (MouseHitCheck(CharaSelectGraPageChangHitL)) {
			CharaSelectGraPageChangeDrawL = 2;
			if (mouse.state & MOUSE_LEFT_CRICK) CharaSelectGraPageChangeDrawL = 1;

			if (mouse.onceState & MOUSE_LEFT_CRICK) {
				if (MakeChara_PUK2Page > 0)
					--MakeChara_PUK2Page;
				else
					MakeChara_PUK2Page = 0;
			}

		}
		else {
			CharaSelectGraPageChangeDrawL = 0;
		}
	}
	else {
		CharaSelectGraPageChangeDrawL = 0;

	}
#else
	// ページ切り替えボタン
	if ( PackageVer >= PV_FIRST_VER2 ){
		if ( MouseHitCheck(CharaSelectGraPageChangHit) ){
			CharaSelectGraPageChangeDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaSelectGraPageChangeDraw = 1;

			if( mouse.onceState & MOUSE_LEFT_CRICK ){
				if (MakeChara_PUK2Page>0) MakeChara_PUK2Page=0;
				else MakeChara_PUK2Page=1;
			}
		}
	}
#endif

	// 色切り替えボタン
	if ( MouseHitCheck(CharaSelectGraColorChangLHit) ){
		CharaSelectGraColorChangLDraw = 2;
		if( mouse.state & MOUSE_LEFT_CRICK ) CharaSelectGraColorChangLDraw = 1;

		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			MakeChara_PUK2Color--;
			if (MakeChara_PUK2Color<0) MakeChara_PUK2Color = 3;
		}
	}
	if ( MouseHitCheck(CharaSelectGraColorChangRHit) ){
		CharaSelectGraColorChangRDraw = 2;
		if( mouse.state & MOUSE_LEFT_CRICK ) CharaSelectGraColorChangRDraw = 1;

		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			MakeChara_PUK2Color++;
			if (MakeChara_PUK2Color>3) MakeChara_PUK2Color = 0;
		}
	}

	// キャラアクション表示枠
	if ( MouseHitCheck(CharaSelectGraAnimFrame1LHit) || MouseHitCheck(CharaSelectGraAnimFrame2LHit) ){
		if( mouse.onceState & MOUSE_LEFT_CRICK ){
			if (MakeChara_Action[0]){
				MakeChara_Action[0]->anim_ang--;
				if (MakeChara_Action[0]->anim_ang<0) MakeChara_Action[0]->anim_ang = 7;
			}
			if (MakeChara_Action[1]){
				MakeChara_Action[1]->anim_ang--;
				if (MakeChara_Action[1]->anim_ang<0) MakeChara_Action[1]->anim_ang = 7;
			}
		}
		if( mouse.onceState & MOUSE_RIGHT_CRICK ){
			if (MakeChara_Action[0]){
				MakeChara_Action[0]->anim_ang++;
				if (MakeChara_Action[0]->anim_ang>7) MakeChara_Action[0]->anim_ang = 0;
			}
			if (MakeChara_Action[1]){
				MakeChara_Action[1]->anim_ang++;
				if (MakeChara_Action[1]->anim_ang>7) MakeChara_Action[1]->anim_ang = 0;
			}
		}
	}

	//========================================
	// キャラ选择ウィンドウ描画

	// カーソル
	StockDispBuffer(
		 SymOffsetX + CharaSelectGraPos[MakeChara_PUK2Num][0], SymOffsetY + CharaSelectGraPos[MakeChara_PUK2Num][1],
		DISP_PRIO_CHAR, PUK2_CHARACREFRAME, 0 );
#ifdef PUK2
	if ( getUsable3D() ){
		StockDispBuffer(
			SymOffsetX + CharaSelectGraPos[MakeChara_PUK2Num][0], SymOffsetY + CharaSelectGraPos[MakeChara_PUK2Num][1],
			DISP_PRIO_CHAR, PUK2_CHARACREFRAMEB, 0, &bm );
	}
#endif

	// Ｂａｃｋボタン
	StockDispBuffer( SymOffsetX + 138, SymOffsetY + 409, DISP_PRIO_CHAR, CharaSelectGraBack[CharaSelectGraBackDraw], 0 );
	// ＯＫボタン
	StockDispBuffer( SymOffsetX + 289, SymOffsetY + 409, DISP_PRIO_CHAR, CharaSelectGraOK[CharaSelectGraOKDraw], 0 );
	// ページ切り替えボタン
	if ( PackageVer >= PV_FIRST_VER2 ){
#ifdef TW_CHARA
		StockDispBuffer( SymOffsetX + 230, SymOffsetY + 429, DISP_PRIO_CHAR, CharaSelectGraPageChang[0][CharaSelectGraPageChangeDrawR], 0);
		StockDispBuffer( SymOffsetX + 194, SymOffsetY + 429, DISP_PRIO_CHAR, CharaSelectGraPageChang[1][CharaSelectGraPageChangeDrawL], 0);
#else
		StockDispBuffer( SymOffsetX + 212, SymOffsetY + 429, DISP_PRIO_CHAR, CharaSelectGraPageChang[MakeChara_PUK2Page][CharaSelectGraPageChangeDraw], 0 );
#endif
	}

	// ページ
	StockDispBuffer( SymOffsetX + 203, SymOffsetY + 412, DISP_PRIO_CHAR, CharaSelectGraPage[MakeChara_PUK2Page], 0 );
	if ( PackageVer >= PV_FIRST_VER2 ){
#ifdef TW_CHARA
		if (PackageVer >= PV_PUK3) 
			StockDispBuffer( SymOffsetX + 221, SymOffsetY + 412, DISP_PRIO_CHAR, 245483, 0);
		else
#endif
		StockDispBuffer( SymOffsetX + 221, SymOffsetY + 412, DISP_PRIO_CHAR, 245482, 0 );
	}else{
		StockDispBuffer( SymOffsetX + 221, SymOffsetY + 412, DISP_PRIO_CHAR, 245481, 0 );
	}

	// 颜絵
	if ( PackageVer >= PV_PUK2 ){
		for(i=0;i<14;i++){
			StockDispBuffer( SymOffsetX + CharaSelectGraPos[i][0], SymOffsetY + CharaSelectGraPos[i][1], DISP_PRIO_CHAR, CharaSelectGraPUK2[MakeChara_PUK2Page][i], 0 );
		}
	}else if ( PackageVer >= PV_FIRST_VER2 ){
		for(i=0;i<14;i++){
			StockDispBuffer( SymOffsetX + CharaSelectGraPos[i][0], SymOffsetY + CharaSelectGraPos[i][1], DISP_PRIO_CHAR, CharaSelectGraPUK1[MakeChara_PUK2Page][i], 0 );
		}
	}else{
		for(i=0;i<14;i++){
			StockDispBuffer( SymOffsetX + CharaSelectGraPos[i][0], SymOffsetY + CharaSelectGraPos[i][1], DISP_PRIO_CHAR, CharaSelectGraCG[MakeChara_PUK2Page][i], 0 );
		}
	}

	// ウィンドウ
	StockDispBuffer( SymOffsetX +214, SymOffsetY + 254, DISP_PRIO_CHAR, PUK2_CHARACREBASE, 0 );


	//========================================
	// キャラ选择ウィンドウ描画

	// 色切り替えボタン
	StockDispBuffer( SymOffsetX + 467, SymOffsetY + 277, DISP_PRIO_CHAR, CharaSelectGraColorChangL[CharaSelectGraColorChangLDraw], 0 );
	StockDispBuffer( SymOffsetX + 568, SymOffsetY + 277, DISP_PRIO_CHAR, CharaSelectGraColorChangR[CharaSelectGraColorChangRDraw], 0 );

	if (MakeChara_Action[1]){
#ifdef TW_CHARA
		MakeChara_Action[1]->dispPrio = DISP_PRIO_SEA_END;
#endif
		MakeChara_Action[1]->anim_chr_no = SelectCharaAnimTbl[MakeChara_PUK2Page][1][MakeChara_PUK2Num] + (MakeChara_PUK2Color*6);
		pattern( MakeChara_Action[1], ANM_NOMAL_SPD, ANM_NO_LOOP );

		if ( PackageVer >= PV_FIRST_VER2 ){
			if (MakeChara_PUK2Page==0) StockDispBuffer2( SymOffsetX + 517, SymOffsetY + 396, DISP_PRIO_CHAR, MakeChara_Action[1]->bmpNo, 0 );
		}
	}

	if (MakeChara_Action[0]){
#ifdef TW_CHARA
		MakeChara_Action[0]->dispPrio = DISP_PRIO_SEA_END;
#endif
		if ( PackageVer < PV_FIRST_VER2 ){
			if (CharaSelectGraCG[MakeChara_PUK2Page][MakeChara_PUK2Num]==CG2_FACE_SHADOW_PCM){
				MakeChara_Action[0]->anim_chr_no = SPR_shadow_pcm + (MakeChara_PUK2Color*6);
			}else if (CharaSelectGraCG[MakeChara_PUK2Page][MakeChara_PUK2Num]==CG2_FACE_SHADOW_PCF){
				MakeChara_Action[0]->anim_chr_no = SPR_shadow_pcf + (MakeChara_PUK2Color*6);
			}else{
				MakeChara_Action[0]->anim_chr_no = SelectCharaAnimTbl[MakeChara_PUK2Page][0][MakeChara_PUK2Num] + (MakeChara_PUK2Color*6);
			}
		}else{
			MakeChara_Action[0]->anim_chr_no = SelectCharaAnimTbl[MakeChara_PUK2Page][0][MakeChara_PUK2Num] + (MakeChara_PUK2Color*6);
		}
		if( pattern( MakeChara_Action[0], ANM_NOMAL_SPD, ANM_NO_LOOP ) ){
			// 古い时の关数を拜借
			SetCharSelNextAction( MakeChara_Action[0] );
			if (MakeChara_Action[1]) SetCharSelNextAction( MakeChara_Action[1] );
		}

#ifndef TW_CHARA
		//使用StockDispBuffer2显示台服单独调色板动画会导致花屏
		StockDispBuffer2( SymOffsetX + 517, SymOffsetY + 243, DISP_PRIO_CHAR, MakeChara_Action[0]->bmpNo, 0 );
#endif
	}

#ifdef TW_CHARA
	StockDispBuffer( SymOffsetX + 518, SymOffsetY + 275, DISP_PRIO_SEA, PUK2_CHARACRECOLOR, 0);
	//图档偏移为-320,-240所以显示位置320,240,在不改变新分辨率的图档偏移前提下,这里不需要修改
	StockDispBuffer( 320, 240, DISP_PRIO_SEA, PUK2_CHARACREATE0, 0);
#else
	// ウィンドウ
	StockDispBuffer( SymOffsetX + 518, SymOffsetY + 275, DISP_PRIO_CHAR, PUK2_CHARACRECOLOR, 0 );

	//========================================
	// 背景描画
	StockDispBuffer( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER, DISP_PRIO_BG, PUK2_CHARACREATE0, 0 );
#endif


	return ret;
}


//========================================
// キャラクターエディット

static char MakeChara_PUK2MaxStatus;
static char MakeChara_PUK2MaxAttribute;
static char MakeChara_PUK2Status[5];
static char MakeChara_PUK2Attribute[4];
static char MakeChara_PUK2EyeType;
static char MakeChara_PUK2MouthType;
static char MakeChara_PUK2FaceVer;

static INPUT_STR MakeChara_PUK2NameInput;

void initMakeCharaEdit_PUK2()
{
	if (MakeChara_Action[0]) DeathAction(MakeChara_Action[0]);
	if (MakeChara_Action[1]) DeathAction(MakeChara_Action[1]);
#ifdef PUK3_CHECK_VALUE
	MakeChara_Action[0] = NULL;
	MakeChara_Action[1] = NULL;
#endif

	selCharName.buffer[0] = '\0';
	selCharName.cnt = 0;
	selCharName.cursorByte = 0;
	InitInputStr( &MakeChara_PUK2NameInput, SymOffsetX + 352, SymOffsetY + 108, FONT_PRIO_BACK, FONT_KIND_SIZE_11, FONT_PAL_BLUE|FONT_PAL_NOSHADOW, NULL, 1, CHAR_NAME_LEN, 0, 0 );
	GetKeyInputFocus( &MakeChara_PUK2NameInput );

	MakeChara_PUK2MaxStatus = 30;
	MakeChara_PUK2MaxAttribute = 10;
	memset( MakeChara_PUK2Status, 0, 5*sizeof(char) );
	memset( MakeChara_PUK2Attribute, 0, 4*sizeof(char) );
	MakeChara_PUK2EyeType = 0;
	MakeChara_PUK2MouthType = 0;
	if ( PackageVer >= PV_PUK2 ) MakeChara_PUK2FaceVer = 2;
	else if ( PackageVer >= PV_FIRST_VER2 ) MakeChara_PUK2FaceVer = 1;
	else MakeChara_PUK2FaceVer = 0;

	editCharParamProcNo = 1;
}

// 目切り替えボタン
const int CharaEditGraEyeChangLHit[4] = { 407, 130,   9,  13 };
const int CharaEditGraEyeChangL[3] = { PUK2_BUTTON_SLEFT0, PUK2_BUTTON_SLEFT1, PUK2_BUTTON_SLEFT2 };
const int CharaEditGraEyeChangRHit[4] = { 438, 130,   9,  13 };
const int CharaEditGraEyeChangR[3] = { PUK2_BUTTON_SRIGHT0, PUK2_BUTTON_SRIGHT1, PUK2_BUTTON_SRIGHT2 };

// 口切り替えボタン
const int CharaEditGraMouthChangLHit[4] = { 407, 148,   9,  13 };
const int CharaEditGraMouthChangL[3] = { PUK2_BUTTON_SLEFT0, PUK2_BUTTON_SLEFT1, PUK2_BUTTON_SLEFT2 };
const int CharaEditGraMouthChangRHit[4] = { 438, 148,   9,  13 };
const int CharaEditGraMouthChangR[3] = { PUK2_BUTTON_SRIGHT0, PUK2_BUTTON_SRIGHT1, PUK2_BUTTON_SRIGHT2 };

// バージョン切り替えボタン
const int CharaEditGraVerChangLHit[4] = { 457, 150,   9,  13 };
const int CharaEditGraVerChangL[3] = { PUK2_BUTTON_SLEFT0, PUK2_BUTTON_SLEFT1, PUK2_BUTTON_SLEFT2 };
const int CharaEditGraVerChangRHit[4] = { 533, 150,   9,  13 };
const int CharaEditGraVerChangR[3] = { PUK2_BUTTON_SRIGHT0, PUK2_BUTTON_SRIGHT1, PUK2_BUTTON_SRIGHT2 };

// 状态マイナス
const int CharaEditGraStatusMinusHit[4] = { 504, 199,  11,  11 };
const int CharaEditGraStatusMinus[3] = { PUK2_BUTTON_MINUS0, PUK2_BUTTON_MINUS1, PUK2_BUTTON_MINUS2 };

// 状态プラス
const int CharaEditGraStatusPlusHit[4] = { 518, 199,  11,  11 };
const int CharaEditGraStatusPlus[3] = { PUK2_BUTTON_PLUS0, PUK2_BUTTON_PLUS1, PUK2_BUTTON_PLUS2 };

// 属性マイナス
const int CharaEditGraAttributeMinusHit[4] = { 504, 307,  11,  11 };
const int CharaEditGraAttributeMinus[3] = { PUK2_BUTTON_MINUS0, PUK2_BUTTON_MINUS1, PUK2_BUTTON_MINUS2 };

// 属性プラス
const int CharaEditGraAttributePlusHit[4] = { 518, 307,  11,  11 };
const int CharaEditGraAttributePlus[3] = { PUK2_BUTTON_PLUS0, PUK2_BUTTON_PLUS1, PUK2_BUTTON_PLUS2 };

// ＯＫボタン
const int CharaEditGraOKHit[4] = { 450, 372,  65,  17 };
const int CharaEditGraOK[3] = { PUK2_BUTTON_OK0, PUK2_BUTTON_OK1, PUK2_BUTTON_OK2 };

// Ｂａｃｋボタン
const int CharaEditGraBackHit[4] = { 371, 372,  66,  17 };
const int CharaEditGraBack[3] = { PUK2_BUTTON_BACK0, PUK2_BUTTON_BACK1, PUK2_BUTTON_BACK2 };

int MakeCharaEdit_PUK2()
{
	int i,j;
////	int strWidth;
	struct BLT_MEMBER bm = {0};
	int ret = 0, ret2;
	int MaxPoint = ( MakeChara_PUK2MaxStatus
		+MakeChara_PUK2Status[0]+MakeChara_PUK2Status[1]+MakeChara_PUK2Status[2]+MakeChara_PUK2Status[3]+MakeChara_PUK2Status[4] )>>1;
	char MakeChara_PUK2FaceVerMax, MakeChara_PUK2FaceVerOK;

	char str[30];

	char CharaEditGraEyeChangLDraw = 0;
	char CharaEditGraEyeChangRDraw = 0;

	char CharaEditGraMouthChangLDraw = 0;
	char CharaEditGraMouthChangRDraw = 0;

	char CharaEditGraVerChangLDraw = 0;
	char CharaEditGraVerChangRDraw = 0;

	char CharaEditGraStatusMinusDraw[5] = {0};
	char CharaEditGraStatusPlusDraw[5] = {0};

	char CharaEditGraAttributeMinusDraw[4] = {0};
	char CharaEditGraAttributePlusDraw[4] = {0};

	char CharaEditGraOKDraw = 0;
	char CharaEditGraBackDraw = 0;

	bm.rgba.rgba = 0xaaffffff;

	if ( PackageVer >= PV_PUK2 ) MakeChara_PUK2FaceVerMax = 2;
	else if ( PackageVer >= PV_FIRST_VER2 ) MakeChara_PUK2FaceVerMax = 1;
	else MakeChara_PUK2FaceVerMax = 0;

	MakeChara_PUK2FaceVerOK = 1;
	if ( MakeChara_PUK2FaceVerMax == MakeChara_PUK2Page ) MakeChara_PUK2FaceVerOK = 0;

	if ( editCharParamProcNo == 1 ){
		//========================================
		// 各ボタンの处理

		// 目切り替えボタン
		if ( MouseHitCheck(CharaEditGraEyeChangLHit) ){
			CharaEditGraEyeChangLDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraEyeChangLDraw = 1;

			if( mouse.autoState & MOUSE_LEFT_CRICK ){
				MakeChara_PUK2EyeType--;
				if (MakeChara_PUK2EyeType<0) MakeChara_PUK2EyeType = 4;
				play_se( SE_NO_CLICK, 320, 240 );	// クリック音
			}
		}
		if ( MouseHitCheck(CharaEditGraEyeChangRHit) ){
			CharaEditGraEyeChangRDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraEyeChangRDraw = 1;

			if( mouse.autoState & MOUSE_LEFT_CRICK ){
				MakeChara_PUK2EyeType++;
				if (MakeChara_PUK2EyeType>4) MakeChara_PUK2EyeType = 0;
				play_se( SE_NO_CLICK, 320, 240 );	// クリック音
			}
		}

		// 口切り替えボタン
		if ( MouseHitCheck(CharaEditGraMouthChangLHit) ){
			CharaEditGraMouthChangLDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraMouthChangLDraw = 1;

			if( mouse.autoState & MOUSE_LEFT_CRICK ){
				MakeChara_PUK2MouthType--;
				if (MakeChara_PUK2MouthType<0) MakeChara_PUK2MouthType = 4;
				play_se( SE_NO_CLICK, 320, 240 );	// クリック音
			}
		}
		if ( MouseHitCheck(CharaEditGraMouthChangRHit) ){
			CharaEditGraMouthChangRDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraMouthChangRDraw = 1;

			if( mouse.autoState & MOUSE_LEFT_CRICK ){
				MakeChara_PUK2MouthType++;
				if (MakeChara_PUK2MouthType>4) MakeChara_PUK2MouthType = 0;
				play_se( SE_NO_CLICK, 320, 240 );	// クリック音
			}
		}

		// バージョン切り替えボタン
		if (MakeChara_PUK2FaceVerOK){
			if ( MouseHitCheck(CharaEditGraVerChangLHit) ){
				CharaEditGraVerChangLDraw = 2;
				if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraVerChangLDraw = 1;

				if( mouse.autoState & MOUSE_LEFT_CRICK ){
					MakeChara_PUK2FaceVer--;
					if (MakeChara_PUK2Page==0){
						if (MakeChara_PUK2FaceVer<0) MakeChara_PUK2FaceVer = MakeChara_PUK2FaceVerMax;
					}else{
						if (MakeChara_PUK2FaceVer<1) MakeChara_PUK2FaceVer = MakeChara_PUK2FaceVerMax;
					}
					play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				}
			}
			if ( MouseHitCheck(CharaEditGraVerChangRHit) ){
				CharaEditGraVerChangRDraw = 2;
				if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraVerChangRDraw = 1;

				if( mouse.autoState & MOUSE_LEFT_CRICK ){
					MakeChara_PUK2FaceVer++;
					if (MakeChara_PUK2Page==0){
						if (MakeChara_PUK2FaceVer>MakeChara_PUK2FaceVerMax) MakeChara_PUK2FaceVer = 0;
					}else{
						if (MakeChara_PUK2FaceVer>MakeChara_PUK2FaceVerMax) MakeChara_PUK2FaceVer = 1;
					}
					play_se( SE_NO_CLICK, 320, 240 );	// クリック音
				}
			}
		}

#ifdef PUK2_CHARMAKE_PS
		// 状态マイナス??プラスボタン
		for(i=0;i<5;i++){
			if ( MakeChara_PUK2Status[i] <= 0 ) CharaEditGraStatusMinusDraw[i] = -1;
			if ( MakeChara_PUK2MaxStatus <= 0 ) CharaEditGraStatusPlusDraw[i] = -1;
			if ( MakeChara_PUK2Status[i] >= MaxPoint ) CharaEditGraStatusPlusDraw[i] = -1;
		}
#endif
		// 状态マイナス??プラスボタン
		for(i=0;i<5;i++){
			j = SymOffsetY + CharaEditGraStatusMinusHit[1] + i*14;
#ifdef PUK2_CHARMAKE_PS
			if ( MakeChara_PUK2Status[i] <= 0 );
			else
#endif
			if ( MakeHitBox(SymOffsetX + CharaEditGraStatusMinusHit[0], j,
				SymOffsetX + CharaEditGraStatusMinusHit[0]+CharaEditGraStatusMinusHit[2], j+CharaEditGraStatusMinusHit[3], -1 ) ){
				CharaEditGraStatusMinusDraw[i] = 2;
				if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraStatusMinusDraw[i] = 1;

				if( mouse.autoState & MOUSE_LEFT_CRICK ){
					if (MakeChara_PUK2Status[i]>0){
						MakeChara_PUK2Status[i]--;
						MakeChara_PUK2MaxStatus++;
						play_se( SE_NO_CLICK, 320, 240 );
					}
					else play_se( SE_NO_NG, 320, 240 );
				}
				break;
			}

#ifdef PUK2_CHARMAKE_PS
			if ( MakeChara_PUK2MaxStatus <= 0 );
			else if ( MakeChara_PUK2Status[i] >= MaxPoint );
			else
#endif
			if ( MakeHitBox( SymOffsetX + CharaEditGraStatusPlusHit[0], j,
				SymOffsetX + CharaEditGraStatusPlusHit[0]+CharaEditGraStatusPlusHit[2], j+CharaEditGraStatusPlusHit[3], -1 ) ){
				CharaEditGraStatusPlusDraw[i] = 2;
				if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraStatusPlusDraw[i] = 1;

				if( mouse.autoState & MOUSE_LEFT_CRICK ){
					if (MakeChara_PUK2MaxStatus>0 && MakeChara_PUK2Status[i]<MaxPoint){
						MakeChara_PUK2Status[i]++;
						MakeChara_PUK2MaxStatus--;
						play_se( SE_NO_CLICK, 320, 240 );
					}
					else play_se( SE_NO_NG, 320, 240 );
				}
				break;
			}
		}
		
#ifdef PUK2_CHARMAKE_PS
		// 属性マイナス??プラスボタン
		for(i=0;i<4;i++){
			if ( MakeChara_PUK2Attribute[i] <= 0 ) CharaEditGraAttributeMinusDraw[i] = -1;
			if ( MakeChara_PUK2Attribute[i] >= 10 ) CharaEditGraAttributePlusDraw[i] = -1;
		}
#endif
		// 属性マイナス??プラスボタン
		for(i=0;i<4;i++){
			j = SymOffsetY + CharaEditGraAttributeMinusHit[1] + i*14;
#ifdef PUK2_CHARMAKE_PS
			if ( MakeChara_PUK2Attribute[i] <= 0 );
			else
#endif
			if ( MakeHitBox( SymOffsetX + CharaEditGraAttributeMinusHit[0], j,
				SymOffsetX + CharaEditGraAttributeMinusHit[0]+CharaEditGraAttributeMinusHit[2], j+CharaEditGraAttributeMinusHit[3], -1 ) ){
				CharaEditGraAttributeMinusDraw[i] = 2;
				if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraAttributeMinusDraw[i] = 1;

				if( mouse.autoState & MOUSE_LEFT_CRICK ){
					if (MakeChara_PUK2Attribute[i]>0){
						MakeChara_PUK2Attribute[i]--;
						MakeChara_PUK2MaxAttribute++;
						play_se( SE_NO_CLICK, 320, 240 );
					}
					else play_se( SE_NO_NG, 320, 240 );
				}
				break;
			}

#ifdef PUK2_CHARMAKE_PS
			if ( MakeChara_PUK2Attribute[i] >= 10 );
			else
#endif
			if ( MakeHitBox( SymOffsetX + CharaEditGraAttributePlusHit[0], j,
				SymOffsetX + CharaEditGraAttributePlusHit[0]+CharaEditGraAttributePlusHit[2], j+CharaEditGraAttributePlusHit[3], -1 ) ){
				CharaEditGraAttributePlusDraw[i] = 2;
				if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraAttributePlusDraw[i] = 1;

				if( mouse.autoState & MOUSE_LEFT_CRICK ){
					if (MakeChara_PUK2Attribute[(i<2?i+2:i-2)]==0){
						if (MakeChara_PUK2MaxAttribute>0){
							MakeChara_PUK2Attribute[i]++;
							MakeChara_PUK2MaxAttribute--;
							play_se( SE_NO_CLICK, 320, 240 );
						}else{
							// 现在押されたボタン以外にパラメータが振り分けられているところを探す
							for( j = 0; j < 4; j++ ){
								if( j != i && MakeChara_PUK2Attribute[j] > 0 ) break;
							}
							if( j < 4 ){
								MakeChara_PUK2Attribute[i]++;
								MakeChara_PUK2Attribute[j]--;
								play_se( SE_NO_CLICK, 320, 240 );	// クリック音
							}
							else play_se( SE_NO_NG, 320, 240 );
						}
					}
				}
				break;
			}
		}

		// ＯＫボタン
		if ( MouseHitCheck(CharaEditGraOKHit) ){
			CharaEditGraOKDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraOKDraw = 1;

			if( mouse.onceState & MOUSE_LEFT_CRICK ){
				// 名称が入力されてない
				if( MakeChara_PUK2NameInput.cnt <= 0 ) editCharParamProcNo = 100;
				// 状态の振り分けポイントが残ってる
				else if( MakeChara_PUK2MaxStatus > 0 ) editCharParamProcNo = 104;
				// 属性の振り分けポイントが残ってる
				else if( MakeChara_PUK2MaxAttribute > 0 ) editCharParamProcNo = 106;
				// 同じキャラ名がいる
				else{
					strcpy( newCharacterName, MakeChara_PUK2NameInput.buffer );
					newCharacterName[MakeChara_PUK2NameInput.cnt] = '\0';

					// 同じ名称があるのでダメ
					if( cmpNameCharacterList( MakeChara_PUK2NameInput.buffer ) ) editCharParamProcNo = 102;
					// 空白文字が含まれている名称はダメ
					else if( strstr( newCharacterName, " " ) != NULL || strstr( newCharacterName, "　" ) != NULL ) editCharParamProcNo = 108; //MLHIDE
					// 縦棒が含まれている名称はダメ
					else if( VirticalCheck( (unsigned char *)newCharacterName ) == 1 ) editCharParamProcNo = 110;
					else{
						// 决定
						editCharParamProcNo = 10;
						play_se( SE_NO_OK, 320, 240 );
					}
				}
			}
		}

		// Ｂａｃｋボタン
		if ( MouseHitCheck(CharaEditGraBackHit) ){
			CharaEditGraBackDraw = 2;
			if( mouse.state & MOUSE_LEFT_CRICK ) CharaEditGraBackDraw = 1;

			if( mouse.onceState & MOUSE_LEFT_CRICK ){
				play_se( SE_NO_BACK, 320, 240 );
				ret = 2;
			}
		}
	}

	// 属性マイナス??プラス表示决定
	for(i=0;i<4;i++){
		if (MakeChara_PUK2Attribute[(i<2?i+2:i-2)]!=0){
			CharaEditGraAttributeMinusDraw[i] = -1;
			CharaEditGraAttributePlusDraw[i] = -1;
		}
	}

	//========================================
	// 决定确认处理

	if( editCharParamProcNo == 10 ){
		ret2 = commonYesNoWindow( SCREEN_WIDTH_CENTER, SCREEN_HEIGHT_CENTER, ML_STRING(190, "可以吗？"), FONT_PAL_WHITE );
		// はい
		if( ret2 == 1 ){
			ret = 1;
			editCharParamProcNo = 11;	// ダミー
			sjisStringToEucString( newCharacterName );

			if (MakeChara_PUK2FaceVer==0){
				newCharacterGraNo = SelectCharaAnimTbl[MakeChara_PUK2Page][0][MakeChara_PUK2Num] + (MakeChara_PUK2Color*6);
				giCreateRenewalFlg = 0;
			}else{
				newCharacterGraNo = SelectCharaAnimTbl[MakeChara_PUK2Page][1][MakeChara_PUK2Num] + (MakeChara_PUK2Color*6);
				giCreateRenewalFlg = 1;
			}

			// 名刺絵
			switch(MakeChara_PUK2FaceVer){
			case 0:
				i = CharaSelectGraPUK2[MakeChara_PUK2Page][MakeChara_PUK2Num];
				i =	getOldFaceGraphic( getFaceGraphicFormPUK2(i) );
				i += MakeChara_PUK2Color*1000
					+ MakeChara_PUK2EyeType*10
					+ MakeChara_PUK2MouthType;
				break;
			case 1:
				i = CharaSelectGraPUK2[MakeChara_PUK2Page][MakeChara_PUK2Num];
				i = getFaceGraphicFormPUK2(i);
				i += MakeChara_PUK2Color*25
					+ MakeChara_PUK2EyeType*5
					+ MakeChara_PUK2MouthType;
				break;
			case 2:
				i = CharaSelectGraPUK2[MakeChara_PUK2Page][MakeChara_PUK2Num];
				i += MakeChara_PUK2Color*25
					+ MakeChara_PUK2EyeType*5
					+ MakeChara_PUK2MouthType;
				break;
			}
			newCharacterFaceGraNo = i;

			newCharacterVit = MakeChara_PUK2Status[0];
			newCharacterStr = MakeChara_PUK2Status[1];
			newCharacterTgh = MakeChara_PUK2Status[2];
			newCharacterQui = MakeChara_PUK2Status[3];
			newCharacterMgc = MakeChara_PUK2Status[4];
			newCharacterEarth = MakeChara_PUK2Attribute[0];
			newCharacterWater = MakeChara_PUK2Attribute[1];
			newCharacterFire  = MakeChara_PUK2Attribute[2];
			newCharacterWind  = MakeChara_PUK2Attribute[3];
			loginDp = 0;
			fade_out_bgm();
		}
		else
		// いいえ
		if( ret2 == 2 ) editCharParamProcNo = 1;
	}

	//========================================
	// エラー信息

	// 名称入力がまだ
	if( editCharParamProcNo == 100 ){
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 101 ){
		if( commonMsgWin( ML_STRING(191, "没有输入名字！") ) ){
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 同一キャラ名
	if( editCharParamProcNo == 102 ){
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 103 ){
		if( commonMsgWin( ML_STRING(192, "已经有相同名字的角色了！") ) ){
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 状态の振り分けポイントが残ってる
	if( editCharParamProcNo == 104 ){
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 105 ){
		if( commonMsgWin( ML_STRING(193, "请分配好状态点数！") ) ){
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 属性の振り分けポイントが残ってる
	if( editCharParamProcNo == 106 ){
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音
	}
	if( editCharParamProcNo == 107 ){
		if( commonMsgWin( ML_STRING(194, "请分配好属性！") ) ){
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}
	// 名称が空白文字
	if( editCharParamProcNo == 108 ){
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音

	}
	if( editCharParamProcNo == 109 ){
		if( commonMsgWin( ML_STRING(195, "无法使用带空白文字的名字！") ) ){
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}

	// 名称が縦棒文字
	if( editCharParamProcNo == 110 ){
		initCommonMsgWin();
		editCharParamProcNo++;
		play_se( SE_NO_NG, 320, 240 );	// ＮＧ音

	}
	if( editCharParamProcNo == 111 ){
		if( commonMsgWin( ML_STRING(196, " | 或者 \\ 无法使用在名字中！") ) ){
			// ＯＫボタンが押された
			editCharParamProcNo = 1;
		}
	}

	//========================================
	// パラメーター振り分けウィンドウ描画

	// 目切り替えボタン
	StockDispBuffer( SymOffsetX + 411, SymOffsetY + 137, DISP_PRIO_CHAR, CharaEditGraEyeChangL[CharaEditGraEyeChangLDraw], 0 );
	StockDispBuffer( SymOffsetX + 442, SymOffsetY + 137, DISP_PRIO_CHAR, CharaEditGraEyeChangR[CharaEditGraEyeChangRDraw], 0 );

	// 口切り替えボタン
	StockDispBuffer( SymOffsetX + 411, SymOffsetY + 155, DISP_PRIO_CHAR, CharaEditGraMouthChangL[CharaEditGraMouthChangLDraw], 0 );
	StockDispBuffer( SymOffsetX + 442, SymOffsetY + 155, DISP_PRIO_CHAR, CharaEditGraMouthChangR[CharaEditGraMouthChangRDraw], 0 );

	// バージョン切り替えボタン
	if (MakeChara_PUK2FaceVerOK){
		StockDispBuffer( SymOffsetX + 461, SymOffsetY + 157, DISP_PRIO_CHAR, CharaEditGraVerChangL[CharaEditGraVerChangLDraw], 0 );
		StockDispBuffer( SymOffsetX + 537, SymOffsetY + 157, DISP_PRIO_CHAR, CharaEditGraVerChangR[CharaEditGraVerChangRDraw], 0 );
	}

	// 状态マイナス??プラスボタン
	for(i=0;i<5;i++){
		StockDispBuffer( SymOffsetX + 509, SymOffsetY + 205+i*14, DISP_PRIO_CHAR, CharaEditGraStatusMinus[ CharaEditGraStatusMinusDraw[i] ], 0 );
		StockDispBuffer( SymOffsetX + 523, SymOffsetY + 205+i*14, DISP_PRIO_CHAR, CharaEditGraStatusPlus[ CharaEditGraStatusPlusDraw[i] ], 0 );
	}

	// 属性マイナス??プラスボタン
	for(i=0;i<4;i++){
		if (CharaEditGraAttributeMinusDraw[i]>=0){
			StockDispBuffer( SymOffsetX + 509, SymOffsetY + 313+i*14, DISP_PRIO_CHAR, CharaEditGraAttributeMinus[ CharaEditGraAttributeMinusDraw[i] ], 0 );
		}
		if (CharaEditGraAttributePlusDraw[i]>=0){
			StockDispBuffer( SymOffsetX + 523, SymOffsetY + 313+i*14, DISP_PRIO_CHAR, CharaEditGraAttributePlus[ CharaEditGraAttributePlusDraw[i] ], 0 );
		}
	}

	// Ｂａｃｋボタン
	StockDispBuffer( SymOffsetX + 403, SymOffsetY + 381, DISP_PRIO_CHAR, CharaEditGraBack[CharaEditGraBackDraw], 0 );
	// ＯＫボタン
	StockDispBuffer( SymOffsetX + 483, SymOffsetY + 381, DISP_PRIO_CHAR, CharaEditGraOK[CharaEditGraOKDraw], 0 );

	// 属性バー(土)
	for(i=0;i<MakeChara_PUK2Attribute[0];i++) StockDispBuffer( SymOffsetX + 406+i*10, SymOffsetY + 313, DISP_PRIO_CHAR, PUK2_STATUS_EARTH, 0 );
	// 属性バー(水)
	for(i=0;i<MakeChara_PUK2Attribute[1];i++) StockDispBuffer( SymOffsetX + 406+i*10, SymOffsetY + 327, DISP_PRIO_CHAR, PUK2_STATUS_WATER, 0 );
	// 属性バー(火)
	for(i=0;i<MakeChara_PUK2Attribute[2];i++) StockDispBuffer( SymOffsetX + 406+i*10, SymOffsetY + 341, DISP_PRIO_CHAR, PUK2_STATUS_FIRE, 0 );
	// 属性バー(风)
	for(i=0;i<MakeChara_PUK2Attribute[3];i++) StockDispBuffer( SymOffsetX + 406+i*10, SymOffsetY + 355, DISP_PRIO_CHAR, PUK2_STATUS_WIND, 0 );

	switch(MakeChara_PUK2FaceVer){
	case 0:
		i = CharaSelectGraPUK2[MakeChara_PUK2Page][MakeChara_PUK2Num];
		i =	getOldFaceGraphic( getFaceGraphicFormPUK2(i) );
		i += MakeChara_PUK2Color*1000
			+ MakeChara_PUK2EyeType*10
			+ MakeChara_PUK2MouthType;
		break;
	case 1:
		i = CharaSelectGraPUK2[MakeChara_PUK2Page][MakeChara_PUK2Num];
		i = getFaceGraphicFormPUK2(i);
		i += MakeChara_PUK2Color*25
			+ MakeChara_PUK2EyeType*5
			+ MakeChara_PUK2MouthType;
		break;
	case 2:
		i = CharaSelectGraPUK2[MakeChara_PUK2Page][MakeChara_PUK2Num];
		i += MakeChara_PUK2Color*25
			+ MakeChara_PUK2EyeType*5
			+ MakeChara_PUK2MouthType;
		break;
	}
	// 颜絵
	if ( MakeChara_PUK2FaceVer <= MakeChara_PUK2FaceVerMax ){
		StockDispBuffer( SymOffsetX + 500, SymOffsetY + 126, DISP_PRIO_CHAR, i, 0 );
	}else{
		if (MakeChara_PUK2Num<14) StockDispBuffer( 500, 126, DISP_PRIO_CHAR, CG2_FACE_SHADOW_PCM, 0 );
		else StockDispBuffer( SymOffsetX + 500, SymOffsetY + 126, DISP_PRIO_CHAR, CG2_FACE_SHADOW_PCF, 0 );
	}

	// 名称入力
/*
	// 文字の背景を描画
	// ＩＭＥバッファーの文字列の横幅を求める
	strWidth = GetStrWidth( ImeInfo.buffer, pNowInputStr->fontKind );

	// ボックス表示データをバッファに溜める
	StockBoxDispBuffer( pNowInputStr->imeX - 1, 
						pNowInputStr->imeY - 1, 
						pNowInputStr->imeX + strWidth, 
						pNowInputStr->imeY + FontKind[ pNowInputStr->fontKind ].zenkakuHeight, 
						DISP_PRIO_IME2, SYSTEM_PAL_BLACK, 1 );
*/	
	StockFontBuffer2( &MakeChara_PUK2NameInput );

	// 状态数值
	sprintf( str, "%02d", MakeChara_PUK2MaxStatus );                     //MLHIDE
	StockFontBuffer( SymOffsetX + 518, SymOffsetY + 199-14, FONT_PRIO_BACK, FONT_KIND_SIZE_11, FONT_PAL_BLUE|FONT_PAL_NOSHADOW, str, 0, 0 );

	// 状态数值
	for(i=0;i<5;i++){
		sprintf( str, "%02d", MakeChara_PUK2Status[i] );                    //MLHIDE
		StockFontBuffer( SymOffsetX + 479, SymOffsetY + 199+i*14, FONT_PRIO_BACK, FONT_KIND_SIZE_11, FONT_PAL_BLUE|FONT_PAL_NOSHADOW, str, 0, 0 );
	}

	// 属性数值
	sprintf( str, "%02d", MakeChara_PUK2MaxAttribute );                  //MLHIDE
	StockFontBuffer( SymOffsetX + 518, SymOffsetY + 307-14, FONT_PRIO_BACK, FONT_KIND_SIZE_11, FONT_PAL_BLUE|FONT_PAL_NOSHADOW, str, 0, 0 );

	// ウィンドウ
	StockDispBuffer( SymOffsetX + 442, SymOffsetY + 241, DISP_PRIO_CHAR, PUK2_STATUSBASE, 0 );

	//========================================
	// 背景描画
	StockDispBuffer( 320, 240, DISP_PRIO_BG, PUK2_CHARASTATUS, 0 );


	return ret;
}


//========================================
// ツール

BOOL MouseHitCheck( const int *Pos )
{
	return MakeHitBox( SymOffsetX + Pos[0], SymOffsetY + Pos[1], SymOffsetX + Pos[0]+Pos[2], SymOffsetY + Pos[1]+Pos[3], -1 );
}