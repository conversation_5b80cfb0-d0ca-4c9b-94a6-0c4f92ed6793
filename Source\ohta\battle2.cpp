﻿/************************/
/*	battle.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/action.h"
#include "../systeminc/math2.h"
//#include "battle.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/anim_tbl.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/pattern.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/mouse.h"
#include "../systeminc/t_music.h"
#include "../systeminc/loadsprbin.h"
#include "../oft/work.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/process.h"
#include <math.h>


// バトルキャラクターアクションポインタ
ACTION *pActBc[ BC_MAX ];
// ソートする个数
int BattleSortCnt;
// ソート用アクションポインタ配列
ACTION *pBattleSort[ BC_SORT_MAX ];

// バトルキャラクターデータ読み込みポイント
int BcReadPoint;
// キャラクターの位置顺番
int BcPosId[ BC_MAX ] = { 	2,1,3,0,4,
							7,6,8,5,9,
							12,11,13,10,14,
							17,16,18,15,19
#if 0						
						3, 1, 0, 2, 4,  
						8, 6, 5, 7, 9, 
						13, 11, 10, 12, 14,  
						18, 16, 15, 17, 19
#endif
						};

// バトルキャラクター位置管理构造体
typedef struct{
	int startX, startY;
	int defX, defY;
}BC_POS;

// バトルキャラクター位置管理构造体
BC_POS BcPos[ BC_MAX ] ={// スタート位置　デフォルト位置
						// 右侧プレイヤー
						{ 580 + 240, 120,  580, 120},
						{ 580 + 240, 200,  580, 200},
						{ 580 + 240, 280,  580, 280},
						{ 580 + 240, 360,  580, 360},
						{ 580 + 240, 440,  580, 440},
						// 右侧モンスター
						{ 460 + 240, 120,  460, 120},
						{ 460 + 240, 200,  460, 200},
						{ 460 + 240, 280,  460, 280},
						{ 460 + 240, 360,  460, 360},
						{ 460 + 240, 440,  460, 440},
						// 左侧プレイヤー
						{ 60 - 240, 120,  60, 120},
						{ 60 - 240, 200,  60, 200},
						{ 60 - 240, 280,  60, 280},
						{ 60 - 240, 360,  60, 360},
						{ 60 - 240, 440,  60, 440},
						// 左侧モンスター
						{ 180 - 240, 120,  180, 120},
						{ 180 - 240, 200,  180, 200},
						{ 180 - 240, 280,  180, 280},
						{ 180 - 240, 360,  180, 360},
						{ 180 - 240, 440,  180, 440},
					};

// バトルキャラクター处理 ***********************************************/
void BattleChar( ACTION *pAct )
{
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// ペットの时
	switch( pAct->actNo ){
	
	case BC_APPEAR:	// 登场
	
		// 目的の方向に向ける
		ChangeDir( pAct, ( float )pYobi->defX, ( float )pYobi->defY );
		// 目的地に移动する
		MoveDir( pAct );
		// 到着チェック
		if( CheckDistance( pAct, ( float )pYobi->defX, ( float )pYobi->defY ) < 5 ){
			// 座标调整
			pAct->fx = ( float )pYobi->defX;
			pAct->fy = ( float )pYobi->defY;
			// 次ぎの行动へ
			pAct->actNo = BC_STANDBY;
			// 止まるアニメーション
			pAct->anim_no = ANIM_STAND;
			// 登场フラグＯＮ
			pYobi->appearFlag = TRUE;
			// 方向变换
			if( pYobi->myId < 10 ) pAct->dir = 180;
			else pAct->dir = 0;
		}
			
		// 方向からアングルに变换する
		DirToAngle( pAct );
		// アニメーション
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
		
		break;
		
	case BC_STANDBY:	// 待机
	
		// 止まるアニメーション
		pAct->anim_no = ANIM_STAND;
		// 方向からアングルに变换する
		DirToAngle( pAct );
		// アニメーション
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
		
		break;
		
	case BC_MOVE:	// 移动
	
		// 目的の方向に向ける
		//ChangeDir( pAct, ( float )mouse.crickLeftDownPoint.x, ( float )mouse.crickLeftDownPoint.y );
		ChangeDir( pAct, pActBc[ pYobi->attackId ] );
		// 目的に地移动する
		MoveDir( pAct );
		// 到着チェック
		if( CheckDistance( pAct, pActBc[ pYobi->attackId ] ) < 64 ){
			// 座标调整
			//pAct->fx = ( float )mouse.crickLeftDownPoint.x;
			//pAct->fy = ( float )mouse.crickLeftDownPoint.y;
			// 次ぎの行动へ
			//pAct->actNo = BC_STANDBY;
			//pAct->actNo = BC_APPEAR;
			pAct->actNo = BC_ATTACK;
			// 止まるアニメーション
			//pAct->anim_no = ANIM_STAND;
			//pAct->anim_no = ANIM_WALK;
			pAct->anim_no = ANIM_ATTACK;
		}
		
		// 方向からアングルに变换する
		DirToAngle( pAct );
		// アニメーション
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
		
		break;
		
	case BC_ATTACK:	// 攻击
	
		// 方向からアングルに变换する
		DirToAngle( pAct );
		// アニメーション終ったら
		if( pattern( pAct, ANM_NOMAL_SPD, ANM_NO_LOOP ) == 1 ){
			pAct->actNo = BC_APPEAR;
			// 止まるアニメーション
			pAct->anim_no = ANIM_WALK;
		}
		
		break;
	}
	
	// 表示座标に变换
	pAct->x = ( int )pAct->fx;
	pAct->y = ( int )pAct->fy;
	
}

// バトルキャラクター作成 ***********************************************/
ACTION *MakeBattleChar( int id )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleChar;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIT;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	pAct->anim_chr_no = 100500;
	// 步くアニメーション
	pAct->anim_no = ANIM_WALK;
	// 移动スピード
	pAct->speed = 3;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	pAct->anim_ang = 1;
	/* 表示优先度 */
	pAct->dispPrio = DISP_PRIO_BOX3;
	/* 初期位置 */
	pAct->fx = (float)BcPos[ BcPosId[ id ] ].startX;
	pAct->fy = (float)BcPos[ BcPosId[ id ] ].startY;
	// デフォルト位置设定
	pYobi->defX = BcPos[ BcPosId[ id ] ].defX;
	pYobi->defY = BcPos[ BcPosId[ id ] ].defY;
	// 自分のＩＤ番号
	pYobi->myId = id;
	// 行动番号
	pAct->actNo = BC_APPEAR;
	
	// アニメーション
	pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
	
	return pAct;
}

// バトルマスター处理 ***********************************************/
void BattleMaster( ACTION *pAct )
{
	//int i;
	
	// ＢＣレシーブ以下の时返回
	//if( /*ProcNo ==*/ SubProcNo <= BATTLE_PROC_RECV_BC_DATA ) return;
	
	// フィールド属性表示
	// ムービー取り出し
	// 生存チェック
	// 登场处理
	
	BC_YOBI *pYobi = ( BC_YOBI *)pAct->pYobi;
	
	// ペットの时
	switch( pAct->actNo ){
	
	case BC_APPEAR:	// 登场
	
		// 目的の方向に向ける
		ChangeDir( pAct, ( float )pYobi->defX, ( float )pYobi->defY );
		// 目的に地移动する
		MoveDir( pAct );
		// 到着チェック
		if( CheckDistance( pAct, ( float )pYobi->defX, ( float )pYobi->defY ) < 5 ){
			// 座标调整
			pAct->fx = ( float )pYobi->defX;
			pAct->fy = ( float )pYobi->defY;
			// 次ぎの行动へ
			pAct->actNo = BC_STANDBY;
			// 止まるアニメーション
			pAct->anim_no = ANIM_STAND;
			// 方向变换
			if( pYobi->myId < 10 ) pAct->dir = 180;
			else pAct->dir = 0;
		}
			
		// 方向からアングルに变换する
		DirToAngle( pAct );
		// アニメーション
		pattern( pAct, ANM_NOMAL_SPD, ANM_LOOP );
		
		break;
	}
}

// バトルマスター作成 ***********************************************/
ACTION *MakeBattleMaster( void )
{
	ACTION *pAct;
	BC_YOBI *pYobi;
	
	// アクションポインタ取得
#ifdef PUK2_MEMCHECK
	pAct = GetAction( PRIO_CHR, sizeof( BC_YOBI ), ACT_T_BC_YOBI );
#else
	pAct = GetAction( PRIO_MASTER, sizeof( BC_YOBI ) );
#endif
	if( pAct == NULL ) return NULL;
	// 予备构造体
	pYobi = ( BC_YOBI *)pAct->pYobi;
	// 实行关数
	pAct->func = BattleMaster;
	// 当たり判定する
	pAct->atr |= ACT_ATR_HIDE;
	// anim_tbl.h の番号
	//pAct->anim_chr_no = sprNo;
	pAct->anim_chr_no = 0;
	// 行动番号
	//pAct->actNo = BC_APPEAR;
	
	return pAct;
}

// バトル初期化 ****************************************************************/
void InitBattle( void )
{
	//int i;

	// バトルマスターアクション作成。
	MakeBattleMaster();
}

// バトルキャラクターデータから数字を読み込む *************************************/
int ReadBcDataNum( void )
{
	int num = 0, i;
	BOOL flag = FALSE;
	
	while( 1 ){
		// １文捉Kiみ込み
		i = BattleStatus[ BcReadPoint ];
		
		// 文字列の最后まで行ったら
		if( i == NULL ) return num;
		
		// 数字だったら
		if( i >= '0' && i <= '9' || i >= 'A' && i <= 'F' ){ 
		
			// 一文字目の时
			if( flag == FALSE ) flag = TRUE; // フラグＯＮ
			// ２文字目の以降の时
			else num = num << 4; // 一桁上げる
			
			// 文字を数字に变换
			if( i >= 'A' ) num += i - 'A' + 10;
			else num += i - '0';
			
		}else{
			// ２文字目以降の时
			if( flag == TRUE || i == '|' ){ 
				BcReadPoint++; // 次の文字へ
				return num;
			}
		}
		
		BcReadPoint++; // 次の文字へ
	}
}

// バトルキャラクターデータから文字を読み込む *************************************/
void ReadBcDataStr( ACTION *pAct, int mode )
{
	char c;
	char *str;
	
	// 名称の时
	if( mode == 0 ) str = pAct->name;
	else str = pAct->freeName;
	
	while( 1 ){
		// １文捉Kiみ込み
		c = BattleStatus[ BcReadPoint ];
		
		// リミットチェック
		// 文字列が終った时
		if( c == NULL ) break;
		// 区切り文字の时
		if( c == '|' ){ 
			BcReadPoint++;
			break;
		}
		
		// ＥＵＣの全角なら
		if( c < 0 ){
			*str++ = c;
			*str++ = BattleStatus[ ++BcReadPoint ];
		}else{
			*str++ = c;
		}
		
		BcReadPoint++;	// 次の文字へ
	}
	// 終端记号
	*str = NULL;
	// ＥＵＣからＳＪＩＳに变换
//	if( mode == 0 ){
//		eucStringToSjisString( pAct->name );
//	}else{
//		eucStringToSjisString( pAct->freeName );
//	}
}

// バトルキャラクターデータの読み込み ****************************************/
void ReadBcData( void )
{
	int id = 0;
	int flag = 0;
	ACTION *pAct;
#if 1	
	strcpy(BattleStatus,"BC|0|0|ＪＳＳ零号机||18859|49|140|140|4|1|山贼志愿者近藏|超级近藏|18837|4E|2D0|2D0|4|5|特拉皮斯||1880A|4E|19C|2E4|100|6|超级卡君||187E3|4E|2CF|2CF|0|A|晓||186D2|4A|128|128|4|B|十六夜|月兔|186C0|44|F2|F5|4|C|拉库|精灵族：小判|18718|3A|BF|BF|4|D|wako|十六夜妹|18781|48|11F|11F|4|E|苹果|圣兽族·白虎|1874A|3B|0|137|6|F|巴奥||18801|41|2D9|2D9|0|10|きゃべつー||187BB|41|277|27F|0|11|拉不奇托斯||18802|36|9D|2A2|0|12|◎红星◎||18801|3A|38|299|0|13|绿光||187BF|3B|26E|26E|0");
#else
	strcpy(BattleStatus,"BC|0|0|山贼志愿者近藏|山贼？|18837|5C|280|280|5|5|超级卡君||187E3|4E|2CF|2CF|1|A|源字||186F2|4A|149|149|5|B|清二|小个子団|186A5|4F|122|122|5|F|夜叉丸||1880A|45|2B4|2B4|1|10|滤纸（５号Ｂ）||1880A|40|25A|25A|1|" );
#endif
	// ３バイト目から読みこむ
	BcReadPoint = 5;
	
	//フィールド属性
	//ATR_ATTRIB(p_master) = get_bc_num();
	
	// ＢＣの最后までループ
	//while( BattleStatus[ BcReadPointbc_pointer ] != NULL ){
	while( BattleStatus[ BcReadPoint ] != NULL ){
	
		id = ReadBcDataNum();			// ＩＤ番号読み込み
		// アクションが无い时
		if( pActBc[ id ] == NULL ){
			pActBc[ id ] = MakeBattleChar( id );
		}
		pAct = pActBc[ id ];		// アクションポインタセット
		//pAct->func = BattleChar;	// 实行关数登録
		ReadBcDataStr( pAct, 0 );		// 名称読み込み
		ReadBcDataStr( pAct, 1 );		// 称号読み込み
		pAct->anim_chr_no = ReadBcDataNum();	// グラフィック番号読み込み
		pAct->level = ReadBcDataNum();	// 等级読み込み
		pAct->hp = ReadBcDataNum();		// ＨＰ読み込み
		pAct->maxHp = ReadBcDataNum();	// 最大ＨＰ読み込み
		flag = ReadBcDataNum();			// フラグ読み込み
		
		pAct->atr |= ACT_ATR_HIT_BOX | ACT_ATR_INFO;	// マウスの当たり判定ＯＮ
	}

}

// バトルソート比较关数 //////////////////////////////////////////////////////////////
int BattleSortComp( ACTION** pBattleSort1, ACTION** pBattleSort2 )
{
	// pDisp1 の表示优先度の方が大きい时、入れ替え
	if( ( *pBattleSort1 )->y > ( *pBattleSort2 )->y ){
		return 1;
	}
	
	// pDisp2 の表示优先度の方が大きい时、そのまま
	if( ( *pBattleSort1 )->y < ( *pBattleSort2 )->y ){
		return -1;
	}
	// 等しい时は登録した顺
	// pDisp1 の方が早かった时、そのまま（のはず？）
	if( ( *pBattleSort1 )->y == ( *pBattleSort2 )->y ){
		return 1;
	}
	// どれでもない时、入れ替え（のはず？）
	return 1;
}

// ソート比较关数
typedef int CMPFUNC( const void *, const void * );

// バトル表示バッファソート ///////////////////////////////////////////////////////////
void SortBattleChar( void )
{	
	int i;
	
	// ソートする个数初期化
	BattleSortCnt = 0;
			
	// バトルキャラクター分ループ
	for( i = 0 ; i < BC_MAX ; i++ ){
		// アクションが无い时
		if( pActBc[ i ] == NULL ) continue;
		// ソート用アクションポインタ配列
		pBattleSort[ BattleSortCnt++ ] = pActBc[ i ];
		// リミットチェック
		if( BattleSortCnt >= BC_SORT_MAX ) break;
	}

	//クイックソート
	qsort( 	pBattleSort,					// 构造体のアドレス
			BattleSortCnt,					// 比较する个数
			sizeof( pBattleSort[ 0 ] ), 	// 构造体のサイズ
			( CMPFUNC * )BattleSortComp 	// 比较关数へのポインタ
		);
	
	// キャラクター分ループ
	for( i = 0 ; i < BattleSortCnt ; i++ ){
		// 表示优先度を决定
		pBattleSort[ i ]->dispPrio = i;
	}
}

// 方向からアングルに变换する **************************************************/
void DirToAngle( ACTION *pAct )
{
	pAct->anim_ang = ( int )( ( pAct->dir + 22.5f ) / 45.0f ) + 6;
	// リミットチェック
	if( pAct->anim_ang >= 8 ) pAct->anim_ang -= 8;
}

// その方向に移动する **************************************************/
void MoveDir( ACTION *pAct )
{
	// 移动
	pAct->fx += CosT( pAct->dir ) * pAct->speed;
	pAct->fy += SinT( pAct->dir ) * pAct->speed;
}

// 目的の方向に向ける **************************************************/
//
//	ACTION *pAct：向き变えをするアクションポインタ
//	float x：目的のＸ座标
//	float y：目的のＹ座标
//	戾り值：目的地との距离
//	pAct->dir に方向（角度）が入る
//
//**********************************************************************/
void ChangeDir( ACTION *pAct, float x, float y )
{
	// 目的地の方向を求める
	pAct->dir = Atan( x - pAct->fx, y - pAct->fy );
	// 方向からアングルに变换する
	DirToAngle( pAct );
}
void ChangeDir( ACTION *pAct, ACTION *pAct2 )
{
	// 目的地の方向を求める
	pAct->dir = Atan( pAct2->fx - pAct->fx, pAct2->fy - pAct->fy );
	// 方向からアングルに变换する
	DirToAngle( pAct );
}
// 目的地との距离を求める **************************************************/
//
//	ACTION *pAct：このアクションポインタとの距离を求める
//	float x：目的のＸ座标
//	float y：目的のＹ座标
//	戾り值：目的地との距离
//
//**********************************************************************/
int CheckDistance( ACTION *pAct, float x, float y )
{
	// 目的地との距离を求める
	return ( int )( sqrtf( ( x - pAct->fx ) * ( x - pAct->fx ) + ( y - pAct->fy ) * ( y - pAct->fy ) ) );
}
int CheckDistance( ACTION *pAct, ACTION *pAct2 )
{
	// 目的地との距离を求める
	return ( int )( sqrtf( ( pAct2->fx - pAct->fx ) * ( pAct2->fx - pAct->fx ) + ( pAct2->fy - pAct->fy ) * ( pAct2->fy - pAct->fy ) ) );
}

