﻿//メニュー＞アイテム

#ifndef _MENUITEM_H_
#define _MENUITEM_H_

#ifdef PUK3_PET_BANK
	#include "../puk3/anim_tbl_PUK3.h"
#endif
//======================================//
// メイン								//
//======================================//

// ボタン处理关数 *********************//
BOOL MenuItemSwitchClose( int no, unsigned int flag );

BOOL MenuItemSwitchPutMoney( int no, unsigned int flag );
BOOL MenuItemSwitchGold( int no, unsigned int flag );
#ifdef PUK3_PET_BANK
BOOL MenuItemSwitchPetItemPrice( int no, unsigned int flag );
#endif

BOOL MenuItemSwitchEquipButton( int no, unsigned int flag );
BOOL MenuItemSwitchPetItemButton( int no, unsigned int flag );

BOOL MenuItemSwitchItemWindow( int no, unsigned int flag );
BOOL MenuItemSwitchEquipWindow( int no, unsigned int flag );
BOOL MenuItemSwitchPetItemWindow( int no, unsigned int flag );
#ifdef PUK3_PET_BANK
BOOL MenuItemSwitchPetItemPanel( int no, unsigned int flag );
#endif

BOOL MenuItemSwitchUseItemWindow( int no, unsigned int flag );
BOOL MenuItemSwitchUseEquipWindow( int no, unsigned int flag );
BOOL MenuItemSwitchUsePetItemWindow( int no, unsigned int flag );

BOOL MenuWindowItemBf( int mouse );
BOOL MenuWindowItemAf( int mouse );
BOOL closeItemWindow();


GRAPHIC_SWITCH MenuWindowItemGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},		// クローズボタン
	{GID_ItemWindow,0,0,0,0,0xFFFFFFFF},		// ベース

	{GID_ItemPutMoneyOn,0,0,0,0,0xFFFFFFFF},	// お金置く

	{GID_EquipButtonOn,0,0,0,0,0xFFFFFFFF},		// 装备ボタン
	{GID_ItemPetButton1On,0,0,0,0,0xFFFFFFFF},	// ペットアイテム１ボタン
	{GID_ItemPetButton2On,0,0,0,0,0xFFFFFFFF},	// ペットアイテム２ボタン
	{GID_ItemPetButton3On,0,0,0,0,0xFFFFFFFF},	// ペットアイテム３ボタン

	{GID_EquipWindow,0,0,0,0,0xFFFFFFFF},		// 装备ウィンドウ
#ifdef PUK3_PET_BANK
	{GID_PetItemBase,0,0,0,0,0xFFFFFFFF},		// ペットアイテムウィンドウ
	{GID_ItemPanel,0,0,0,0,0xFFFFFFFF},			// アイテム栏
	{/*GID_PetItemNoSellOn*/GID_PetItemGoldPanel,0,0,0,0,0xFFFFFFFF},	// 值段栏
	{/*GID_PetItemNoSellOn*/GID_PetItemGoldPanel,0,0,0,0,0xFFFFFFFF},	// 值段栏
	{/*GID_PetItemNoSellOn*/GID_PetItemGoldPanel,0,0,0,0,0xFFFFFFFF},	// 值段栏
#else
	{GID_PetItemWindow,0,0,0,0,0xFFFFFFFF},		// ペットアイテムウィンドウ
#endif
};

TEXT_SWITCH MenuWindowItemText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,"？？？？"},                          //MLHIDE
};

ACTION_SWITCH_INIT MenuWindowItemAction[]={
	{100000},
#ifdef PUK3_PET_BANK
	{100000},
#endif
};

#ifdef PUK3_PET_BANK
char ItemNum_PCGold[11];
char ItemNum_PetItemPrice[3][11] = {"0","0","0"};                     //MLHIDE

NUMBER_SWITCH MenuWindowItemNum[4] = {
	{ ItemNum_PCGold, FONT_PAL_BLACK, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ItemNum_PetItemPrice[0], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ItemNum_PetItemPrice[1], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
	{ ItemNum_PetItemPrice[2], FONT_PAL_WHITE, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
};
#else
char ItemNum_PCGold[11];

NUMBER_SWITCH MenuWindowItemNum[1] = {
	{ ItemNum_PCGold, FONT_PAL_BLACK, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
};
#endif

// スイッチ
static SWITCH_DATA ItemSwitch[] = {
{ SWITCH_GRAPHIC,258,  8,  11, 11, TRUE, &MenuWindowItemGraph[ 0], MenuItemSwitchClose },			// クローズボタン

#ifdef VERSION_TW
//台服客户端道具窗口的金币显示位置
{ SWITCH_GRAPHIC, 221, 28,  49, 17, TRUE, &MenuWindowItemGraph[2], MenuItemSwitchPutMoney },		// お金置く
{ SWITCH_NUMBER, 198, 31,   0,  0, TRUE, &MenuWindowItemNum[0], MenuItemSwitchGold },								//ドラッグ用
#else
{ SWITCH_GRAPHIC, 23, 28,  80, 17, TRUE, &MenuWindowItemGraph[ 2], MenuItemSwitchPutMoney },		// お金置く

{ SWITCH_NUMBER, 249, 31,   0,  0, TRUE, &MenuWindowItemNum[0], MenuItemSwitchGold },								//ドラッグ用
#endif

{ SWITCH_GRAPHIC,  8,  0, 278,259, TRUE, &MenuWindowItemGraph[ 1], MenuItemSwitchItemWindow },		// ベース

{ SWITCH_GRAPHIC,  2, 17,  19, 46, TRUE, &MenuWindowItemGraph[ 3], MenuItemSwitchEquipButton },		// 装备ボタン
{ SWITCH_GRAPHIC,  2, 63,  19, 46, TRUE, &MenuWindowItemGraph[ 4], MenuItemSwitchPetItemButton },	// ペットアイテム１ボタン
{ SWITCH_GRAPHIC,  2,108,  19, 46, TRUE, &MenuWindowItemGraph[ 5], MenuItemSwitchPetItemButton },	// ペットアイテム２ボタン
{ SWITCH_GRAPHIC,  2,153,  19, 46, TRUE, &MenuWindowItemGraph[ 6], MenuItemSwitchPetItemButton },	// ペットアイテム３ボタン

{ SWITCH_ACTION,  -81,163,  0,  0, TRUE, &MenuWindowItemAction[0], MenuSwitchNone },				// ＰＣ画像
#ifdef PUK3_PET_BANK
{ SWITCH_GRAPHIC, -81,23+67*0,48,48, TRUE, &MenuWindowItemGraph[9], MenuItemSwitchPetItemPanel },	// アイテム栏
{ SWITCH_GRAPHIC, -81,23+67*1,48,48, TRUE, &MenuWindowItemGraph[9], MenuItemSwitchPetItemPanel },	// アイテム栏
{ SWITCH_GRAPHIC, -81,23+67*2,48,48, TRUE, &MenuWindowItemGraph[9], MenuItemSwitchPetItemPanel },	// アイテム栏

{ SWITCH_NUMBER,  -81,23+67*0+51,0,0, TRUE, &MenuWindowItemNum[1], MenuSwitchNone },				// アイテム值段
{ SWITCH_NUMBER,  -81,23+67*1+51,0,0, TRUE, &MenuWindowItemNum[2], MenuSwitchNone },				// アイテム值段
{ SWITCH_NUMBER,  -81,23+67*2+51,0,0, TRUE, &MenuWindowItemNum[3], MenuSwitchNone },				// アイテム值段

{ SWITCH_GRAPHIC, -81,23+67*0+49,76,16, TRUE, &MenuWindowItemGraph[10], MenuItemSwitchPetItemPrice },	// アイテム值段栏
{ SWITCH_GRAPHIC, -81,23+67*1+49,76,16, TRUE, &MenuWindowItemGraph[10], MenuItemSwitchPetItemPrice },	// アイテム值段栏
{ SWITCH_GRAPHIC, -81,23+67*2+49,76,16, TRUE, &MenuWindowItemGraph[10], MenuItemSwitchPetItemPrice },	// アイテム值段栏

{ SWITCH_TEXT,    -81, 23,  0,  0, TRUE, &MenuWindowItemText[0], MenuSwitchNone },					// ペットの名称

{ SWITCH_ACTION,  -81,200,  0,  0, TRUE, &MenuWindowItemAction[1], MenuSwitchNone },				// ペット画像
#endif

{ SWITCH_GRAPHIC,-197,17, 293,222, TRUE, &MenuWindowItemGraph[ 7], MenuItemSwitchEquipWindow },		// 装备ウィンドウ
{ SWITCH_GRAPHIC,-268,17, 293,222,FALSE, &MenuWindowItemGraph[ 8], MenuItemSwitchPetItemWindow },	// ペットアイテムウィンドウ

{ SWITCH_GRAPHIC,  2, 17,  19, 46, TRUE, &MenuWindowItemGraph[ 3], MenuItemSwitchEquipButton },		// 装备ボタン
{ SWITCH_GRAPHIC,  2, 63,  19, 46, TRUE, &MenuWindowItemGraph[ 4], MenuItemSwitchPetItemButton },	// ペットアイテム１ボタン
{ SWITCH_GRAPHIC,  2,108,  19, 46, TRUE, &MenuWindowItemGraph[ 5], MenuItemSwitchPetItemButton },	// ペットアイテム２ボタン
{ SWITCH_GRAPHIC,  2,153,  19, 46, TRUE, &MenuWindowItemGraph[ 6], MenuItemSwitchPetItemButton },	// ペットアイテム３ボタン

{ SWITCH_GRAPHIC,-197,17, 293,222, TRUE, &MenuWindowItemGraph[ 7], MenuSwitchDelMouse },			// 装备??ペットアイテムウィンドウ当たり

{ SWITCH_NONE,	  282, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },								// ドラッグ用

};

enum{
	EnumGraphItemClose,

	EnumGraphPutMoney,

	EnumTextMoneyDisp,

	EnumGraphItemWindow,

	EnumGraphEquipButton,
	EnumGraphPet1Button,
	EnumGraphPet2Button,
	EnumGraphPet3Button,

	EnumGraphItemPcAction,
#ifdef PUK3_PET_BANK
	EnumGraphItemPetItem1,
	EnumGraphItemPetItem2,
	EnumGraphItemPetItem3,

	EnumGraphItemPetItemPrice1,
	EnumGraphItemPetItemPrice2,
	EnumGraphItemPetItemPrice3,

	EnumGraphItemPetItemPricePanel1,
	EnumGraphItemPetItemPricePanel2,
	EnumGraphItemPetItemPricePanel3,

	EnumTextItemPetName,

	EnumGraphItemPetAction,
#endif

	EnumGraphEquipWindow,
	EnumGraphPetItemWindow,

	EnumGraphEquipButton2,
	EnumGraphPet1Button2,
	EnumGraphPet2Button2,
	EnumGraphPet3Button2,

	EnumGraphEquipPetWindow,

	EnumItemDragBack,

	EnumItemEnd,
};


const WINDOW_DATA WindowDataMenuItem = {
 0,															// メニューウィンドウ
     4,   340, 120,278,259, 0x80010101,  EnumItemEnd,  ItemSwitch, MenuWindowItemBf,MenuWindowItemAf,closeItemWindow
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateItem={
	2,
	 22,  0,256, 33,
	 282, 7,18,  78,
};

// ウィンドウ管理构造体
struct ITEMWINDOWMASTER{
	int				flag;					// ウィンドウ全体のフラグ
	int				wx,wy;					// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;				// WINDOW_INFOのアドレス。存在しないときはNULL 

	int itemInfoNo;
	int itemInfoPage;
};

void ChangeShovelItemSelectMode( char *data );

extern char ItemFrameFlag;

//======================================//
// 电卓									//
//======================================//

// ボタン处理关数 *********************//

BOOL MenuCalculatorMain( int no, unsigned int flag );
BOOL MenuCalculatorBtUp( int no, unsigned int flag );
BOOL MenuCalculatorBtDown( int no, unsigned int flag );
BOOL MenuCalculatorGetKeyForcus( int no, unsigned int flag );


BOOL MenuWindowCalculatorBf( int mouse );
BOOL MenuWindowCalculatorAf( int mouse );


GRAPHIC_SWITCH MenuWindowCalcuGraph[]={
	{GID_WindowCloseOn,0,0,0,0,0xFFFFFFFF},		// クローズボタン
	{GID_StackWindow,0,0,0,0,0xFFFFFFFF},		// ベース

	{GID_UpButtonOn,0,0,0,0,0xFFFFFFFF},		// 上ボタン
	{GID_DownButtonOn,0,0,0,0,0xFFFFFFFF},		// 下ボタン
};

TEXT_SWITCH MenuWindowCalculatorText[]={
	{FONT_PAL_SHADOW,FONT_KIND_SIZE_12,""},
};

char CalculatorNum[11];

NUMBER_SWITCH MenuWindowCalculatorNum[1] = {
	{ CalculatorNum, FONT_PAL_BLACK, G_NUM_SIZE__9_S, G_NUM_FLAG_RIGHT_JUSTIFIED },
};

// スイッチ
static SWITCH_DATA CalculatorSwitch[] = {

{ SWITCH_DIALOG,   0,  0, 126,136, TRUE, NULL, MenuCalculatorGetKeyForcus },						// キーフォーカス取得用假入力栏

{ SWITCH_GRAPHIC,106,  8,  11, 11, TRUE, &MenuWindowCalcuGraph[0], MenuSwitchCloseButton },			// クローズボタン

{ SWITCH_NONE,	  18, 46, 106, 85, TRUE, NULL, MenuCalculatorMain },								// 电卓

{ SWITCH_GRAPHIC, 92, 29,  11, 11, TRUE, &MenuWindowCalcuGraph[2], MenuCalculatorBtUp },			// 上ボタン
{ SWITCH_GRAPHIC,105, 29,  11, 11, TRUE, &MenuWindowCalcuGraph[3], MenuCalculatorBtDown },			// 下ボタン

{ SWITCH_NUMBER,  85, 30,   0,  0, TRUE, &MenuWindowCalculatorNum[0], MenuSwitchNone },				// 数值

{ SWITCH_GRAPHIC,  0,  0, 126,136, TRUE, &MenuWindowCalcuGraph[1], MenuSwitchNone },				// ベース

{ SWITCH_NONE,	  128, 7,  18, 78, TRUE, NULL, MenuSwitchDelMouse },								// ドラッグ用

};

enum{
	EnumCalcuGetKeyForcus,

	EnumGraphCalcuClose,

	EnumGraphCalcuMain,

	EnumGraphCalcuUp,
	EnumGraphCalcuDown,

	EnumGraphCalcuText,

	EnumGraphCalcuWindow,

	EnumCalcuDragBack,

	EnumCalcuEnd,
};


const WINDOW_DATA WindowDataMenuCalculator = {
 0,															// メニューウィンドウ
     4,   100, 100,126,136, 0x80010101,  EnumCalcuEnd,  CalculatorSwitch, MenuWindowCalculatorBf,MenuWindowCalculatorAf,MenuWindowDel
};

// ドラッグ设定
static WINDOW_DRAGMOVE DragMoveDateCalculator={
	2,
	 12,  0,113, 26,
	 128, 7,18,  78,
};

// ウィンドウ管理构造体
struct CALCULATORWINDOWMASTER{
	int				flag;						// ウィンドウ全体のフラグ
	int				wx,wy;						// ウィンドウの表示位置
	WINDOW_INFO		*wininfo;					// WINDOW_INFOのアドレス。存在しないときはNULL

	int				Max;						// 最大值
	int				All;						// ＡＬＬボタンを押したときの数值
	void			(*returnFunc)( int ret );	// ＯＫボタンが押されたときに实行する关数
#ifdef PUK3_PET_BANK
	int				option;						// オプション
#endif

	int				WinType;
	int				ButtonNo;
};

#ifdef PUK3_PET_BANK
ACTION *openCalculatorMenuWindow( int WinType, int ButtonNo, int BackNo, int Max, int All, int First, void (*func)( int ret ), unsigned char flg, char opentype, int option = 0 );
#else
ACTION *openCalculatorMenuWindow( int WinType, int ButtonNo, int BackNo, int Max, int All, int First, void (*func)( int ret ), unsigned char flg, char opentype );
#endif
void ItemMove( int WinType, int ButtonNo, int from, int to );


// アイテム使用对象选择ウィンドウ呼び出し
ACTION *openItem1Window();
ACTION *openItem2Window();

//====================================//
//		アイテム说明ウィンドウ		  //
//====================================//
void PcItemExplanationWindow( int ItemNo, int Page );
void BankItemExplanationWindow( int ItemNo, int Page );
void TradeItemExplanationWindow( char Side, int ItemNo, int Page );
void ShopItemExplanationWindow( int ItemNo, int Page, BOOL LastOn );
void CountItemExplanationWindow( int Page );
void ItemExplanationWindow( short x, short y, char *name, int namecolor, char *str, int linelen, int max, char *laststr, int lastcolor );


//======================================
// アイテム制御用关数

// アイテムの所属を返す
// 引	ItemNo ---- アイテムの场所
// 戾	アイテムの所属に对应した值
enum{
	ITEMFROM____,		// 不明
	ITEMFROMEQUIP,		// ＰＣの装备品
	ITEMFROMHAVE,		// ＰＣの手持ち
	ITEMFROMBANK,		// 银行
};
int checkItemFrom( int ItemNo );

// モンスターの所属を返す
// 引	ItemNo ---- モンスターの场所
// 戾	モンスターの所属に对应した值
enum{
	MONSTERFROM____,		// 不明
	MONSTERFROMPC,			// ＰＣの手持ち
	MONSTERFROMBANK,		// 银行
};
int checkMonsterFrom( int MonsNo );


void MonsMove( int WinType, int ButtonNo, int from, int to );
int getsortMonsPos( int Num );

#ifdef VERSION_TW
//台服默认道具数量颜色为青色,韩服为灰色
#define ITEMSTACKCOLOR FONT_PAL_AQUA
#else
#define ITEMSTACKCOLOR FONT_PAL_GRAY2
#endif

#ifdef PUK2_NEWDRAG
	void DragItem( int itemNo, BOOL PutField );
	void DragMons( int itemNo, int l_MonsPanelType );
#endif

#endif