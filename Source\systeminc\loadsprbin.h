﻿#ifndef _LOAD_SPRBIN_H_
#define _LOAD_SPRBIN_H_

//スプライトの种类个数を定义している。

//#define maxBUFFER	0x9fffff
//#define maxBUFFER	10485759
//#define mxSPRITE	32768
//#define mxSPRITE	10500

#ifdef ANIM_NUM_EXPAND
//扩容动画数量(原始20500)
#define mxSPRITE_MULTI		99999
#define maxBUFFER_MULTI		10412000
#else
#define mxSPRITE_MULTI		20500

#define maxBUFFER_MULTI		10402000		// 新バージョンのグラビン
#endif

#define mxSPRITE_SINGLE		10500
#define maxBUFFER_SINGLE	4300000		// クロスゲート通常时の值 430000

extern int mxSPRITE;
extern int maxBUFFER;

// 攻击判定のスタート值

#define ANIM_HIT_STOP_NUM 	20000 
#define ANIM_HIT_BACK_NUM 	10000 

typedef unsigned char MOJI;
typedef unsigned char U1;
typedef          char S1;
typedef unsigned short U2;
typedef          short S2;
typedef unsigned long U4;
typedef          long S4;
typedef float  F4;
typedef double F8;

typedef enum {
	// ネットローグ
	ANIM_STAND,			// 立ち stand ０ 
	ANIM_WALK,          // 歩き walk １ 
	ANIM_B_WALK_START,  // バトル時移動（前） bw1 ２ 
	ANIM_B_WALK,        // バトル時移動（中） bw2 ３ 
	ANIM_B_WALK_END,    // バトル時移動（後） bw3 ４ 
	ANIM_ATTACK,        // 攻击 atk ５ 
	ANIM_MAGIC,         // 魔法 magic ６ 
	ANIM_THROW,         // 投掷 throw ７ 
	ANIM_DAMAGE,        // 受伤 dmg ８ 
	ANIM_GUARD,         // 防御 guard ９ 
	ANIM_DEAD,          // 気絶 die １０ 
	ANIM_SIT,           // 座る sit １１ 
	ANIM_HAND,          // 挥手 bye １２ 
	ANIM_HAPPY,         // 喜ぶ yoro １３ 
	ANIM_ANGRY,         // 怒る oko １４ 
	ANIM_SAD,           // 悲伤 kana １５ 
	ANIM_NOD,           // 点头 un １６ 
	ANIM_GU,            // じゃんけん（石头） gu １７ 
	ANIM_CHOKI,         // じゃんけん（剪刀） choki １８ 
	ANIM_PA,            // じゃんけん（布） pa １９ 
	
	ANIM_LIST_
} ANIM_LIST;

// 1フレームの情报
typedef struct
{
	U4 BmpNo;				// bmp番号(Graphic.binでの位置)
	S2 PosX,PosY;			// 表示位置のOFFセット
	U2 SoundNo;				// サウンド番号
} FRAMELIST;


// １アニメーションの情报
typedef struct
{
	U2 dir;					// アニメーションの向き
	U2 no;					// 何のアニメーションか(0は立ちポーズとか)
	U4 dtAnim;				// アニメーションの实行时间
	U4 frameCnt;
#ifdef PUK2
	U2 PalNo;				// 使用するアニメーションパレットの番号
	U2 bltf;				// 描画の指定
	U4 rgba;				// 色要素の指定
#endif
	FRAMELIST *ptFramelist;	// １アニメーションの情报
} ANIMLIST;


// SPRの位置情报
typedef struct
{
	U4 sprNo;				// 处理上０から始まる(实际のSPR番号は65536ぐらいから)
	U4 offset;				// SPR.BINのファイルの読み込み位置
	U2 animSize;			// アニメーションの数
#ifdef PUK2
	U2 format;				// binのフォーマットを示す数值
#endif
#ifdef MULTI_GRABIN
	short BinMode;			// SPRBIN のモード
#endif
} SPRADRN;

#ifdef ANIMATION_BUFFER_LESS	// アニメーションバッファ少なくするなら。
typedef struct _AnimMan{
	int	iSpriteIndex;		// Sprite构造体のインデックス
	int iAllocSize;			// アロケートしたサイズ
	struct _AnimMan *pPre;	// 一个前のバッファ
	struct _AnimMan *pNext;	// 一个先のバッファ
}ANIMMAN;
int ReadAnimationData( int i );
#endif

#ifdef PUK3_RIDEBIN

typedef struct{
	int graNo;					// 絵の番号
	short x,y;					// 座标
} ANIMCOORDINATE;

#endif

typedef struct
{
#ifdef MULTI_GRABIN
	U4 sprNo;					// アニメーションキャラの番号
#endif
	U2 animSize;				// アニメーションの数
	#ifdef PUK3_RIDEBIN
		U2 cdSize;					// 座标データの数
	#endif
#ifdef ANIMATION_BUFFER_LESS
	U2			BinMode;		// SPRBIN のモード
	U4 			offset;			// SPR.BINのファイルの読み込み位置
	ANIMMAN 	*pAnimMan;		// アニメーションポジション
	#ifdef PUK3_RIDEBIN
		U4			cdoffset;		// 座标データの読み込み位置
	#endif
#endif
	ANIMLIST	*ptAnimlist;	//アニメーションリストここがNULLなら直接BMPを表示
	#ifdef PUK3_RIDEBIN
		ANIMCOORDINATE *ptCdlist;			// 座标データ
	#endif
#ifdef PUK2
	U2 format;				// binのフォーマットを示す数值
#endif
} SPRITEDATA;

#if 0
typedef struct {
	U2			dtz;			//描画されるときの顺番を决める
	U2			noSpritedata;	//スプライト番号
	U2			mdAnim;			//アニメーションモード
	U2			mdAnim2;		//次のアニメーションモード
	U2			dir;			//方向は？
	U2			ctLoop;			//何回ループしたか？
	float		ctAnim;			//アニメーションカウンター ０～１未满は不动小数点处理、整数はフレーム番号处理
	float		dtAnim;			//スピード	これが０であれば、sprファイルから引っ张る
} SPRITE;
#endif

extern SPRITEDATA *SpriteData;

#ifdef MULTI_GRABIN
extern int giAllChrNum;
int InitSprBinFileOpen( char* [], char* [] );
#ifdef PUK3_NOTFREE_ANIMEDATA
void CloseSprBinFileOpen();
#endif
int Spr_Number2Tbl( int chrNo );
// バイナリサーチを使う。
#define SPR_NUMBER2TBL( a ) ( Spr_Number2Tbl( (a) ) )
#else
BOOL InitSprBinFileOpen( char *, char * );
#define SPR_NUMBER2TBL( a ) (a)
#endif
#ifdef PUK3_RIDEBIN
	// 座标データビン使用の初期化
	#ifdef _DEBUG
		void InitCoordinateBinFilesOpen( char *Crddata[], char *CrdInfo[], char *CrdLog[] );
	#else
		void InitCoordinateBinFilesOpen( char *Crddata[], char *CrdInfo[] );
	#endif
	void CloseCoordinateBinFiles();
#endif

enum{
	LOADSPRBIN_SUCCESS,
	ERR_LOADSPRBIN_MEMORY,
	ERR_LOADSPRBIN_FILENOTFOUND,
	ERR_LOADSPRBIN_OVER_MAXSPRITE,
	ERR_LOADSPRBIN_OVER_TBBUFFER,
	ERR_LOADSPRBIN_BROKEN_FILE
};

#endif
