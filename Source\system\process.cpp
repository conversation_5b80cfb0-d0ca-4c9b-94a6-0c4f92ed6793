﻿/************************/
/*	process.c			*/
/************************/
#include "../systeminc/system.h"
#include "../systeminc/directDraw.h"
#include "../systeminc/main.h"
#include "../systeminc/gamemain.h"
#include "../systeminc/sprmgr.h"
#include "../systeminc/init.h"
#include "../systeminc/process.h"
#include "../systeminc/action.h"
#include "../systeminc/sprdisp.h"
#include "../systeminc/math2.h"
#include "../systeminc/chat.h"
#include "../systeminc/font.h"
#include "../systeminc/mouse.h"
#include "../systeminc/radar.h"
#include "../systeminc/gemini.h"
#include "../systeminc/pattern.h"
#include "../systeminc/ime_sa.h"
#include "../systeminc/menu.h"
#include "../systeminc/pc.h"
#include "../systeminc/character.h"
#include "../systeminc/login.h"
#include "../systeminc/netproc.h"
#include "../systeminc/savedata.h"
#include "../systeminc/testView.h"
#include "../systeminc/battleProc.h"
#include "../systeminc/produce.h"
#include "../systeminc/nrproto_cli.h"
#include "../systeminc/netmain.h"
#include "../systeminc/battleMenu.h"
#include "../systeminc/t_music.h"
#include "../systeminc/field.h"
#include "../systeminc/handletime.h"
#include "../systeminc/map.h"
#include "../systeminc/mapEffect.h"
#include "../ohta/ohta.h"
#include "../systeminc/keyboard.h"
#include "../systeminc/direct3d.h"
#include "../systeminc/sndcnf.h"
#include "../systeminc/mapGridCursol.h"

#include"../puk2/map/newmap.h"

#ifdef PUK2
	#include "../puk2/interface/menuwin.h"
	#include "../puk2/interface/menuShortCut.h"
#endif

#ifdef PUK2
	int GetInstallVersion( void );
#endif

#ifdef PUK3_PROF
	#include "../puk3/profile/profile.h"
#endif

#ifdef PUK3_ACCOUNT
	#include "../puk3/interface/loginTitlePuk3.h"
	#include "../puk3/account/account.h"
#endif

/* プロセス番号 */
UINT ProcNo;
/* サブプロセス番号 */
UINT SubProcNo;
/* プロセス番号(プロセスの最初に切り替える) */
int ProcNo2;
int SubProcNo2;


int palNo;
int oldPalNo;

int recvBgmNo;		// サーバからの受信ＢＧＭ番号
int recvBgmNoFlag;	// recvBgmNoが有效かどうかのフラグ

int encountEndFlag;	// エンカウント終了フラグ（オートマップ表示时に使用）
#ifdef PUK2_NEW_MENU
extern char ItemNoOpe[MAX_ITEM];		// アイテムの使用フラグ
#endif
#ifdef PUK2_ACID_PAPER_SNOWSTORM
#else
#ifdef PUK2_NEWSKILL
	// 天气记忆变数
	short memMapEffectRainLevel;
	short memMapEffectSnowLevel;
	short memMapEffectKamiFubukiLevel;
#endif
#endif
#ifdef PUK3_VEHICLE
	static int warpCnt = 0;
#endif

/* プロセス处理 ********************************************************************/
void Process( void )
{
	if( ProcNo2 >= 0 )
	{
		ProcNo = ProcNo2;
		ProcNo2 = -1;
		SubProcNo = SubProcNo2;
	}

	// 高速描画は处理のはじめにスイッチ OFF する
	highSpeedDrawSw = 0;
#ifdef PUK3_SEGMENTATION_FAULT
	ProcStack( ProcNo );
#endif

	/* プロセス分岐 */
	switch( ProcNo ){
	
		case PROC_OPENNING:
		
			break;
			
		case PROC_INIT: 	/* 初期化プロセス */
		
			//InitProc();
			break;			

		// ＩＤ、パスワード入力处理
		case PROC_ID_PASSWORD:
			BackBufferDrawType = DRAW_BACK_NORMAL; 
			KeyBoardProc();			// キーボード处理

#ifdef PUK3_ACCOUNT
			switch(giInstallVersion){
				// 通常用
				case 0:
				case 1:
					idPasswordProc();
					break;
				// バージョン２用
				case 2:
					V2_idPasswordProc();
					break;
				// ＰＵＫ２用
				case 3:
					PUK2_idPasswordProc();
					break;
				// ＰＵＫ３用
				case 4:
					PUK3_idPasswordProc();
					break;
			}
#else
			// ＰＵＫ２以降？
			if( giInstallVersion >= 3 ){
				// ＰＵＫ２用
				PUK2_idPasswordProc();
			}else if( giInstallVersion >= 2 ){
				V2_idPasswordProc();// バージョン２用
			}else{
				idPasswordProc();
			}
#endif

			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			ImeProc();				// ＩＭＥ关连处理
			FlashKeyboardCursor();	// キーボードカーソル点灭处理
			break;

		// タイトル表示とメニュー选择
		case PROC_TITLE_MENU:
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL; 
#ifdef PUK2
			// ＰＵＫ２用
			PUK2_titleProc();
#else

#ifdef PUK2
			// ＰＵＫ２以降？
			if( giInstallVersion >= 3 ){
				// ＰＵＫ２用
				PUK2_titleProc();
			}else{
#endif
			titleProc();
#ifdef PUK2
			}
#endif

#endif
			//TitleProduce();			// タイトル演出
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			break;


		// キャラ选择开始
		case PROC_CHAR_SELECT:		// キャラ选择
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL; 
#ifdef PUK2
			// ＰＵＫ２用
			PUK2_selectCharacterProc();
#else

#ifdef PUK2
			// ＰＵＫ２以降？
			if( giInstallVersion >= 3 ){
				// ＰＵＫ２用
				PUK2_selectCharacterProc();
			}else{
#endif
			selectCharacterProc();
#ifdef PUK2
			}
#endif

#endif
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			break;

		// キャラ作成
		case PROC_CHAR_MAKE:
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL; 
			KeyBoardProc();			// キーボード处理
#ifdef PUK2_NEW_MENU
			makeCharacterProc_PUK2();
////			if ( PackageVer >= PV_PUK2 ) makeCharacterProc_PUK2();
////			else makeCharacterProc();
#else
			makeCharacterProc();
#endif
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			ImeProc();				// ＩＭＥ关连处理
			FlashKeyboardCursor();	// キーボードカーソル点灭处理
			break;

		/*
		//　卒业フォーム
		case PROC_KEY_CANCELLMODE:

			BackBufferDrawType = DRAW_BACK_NORMAL; 
			KeyBoardProc();			// キーボード处理
			ProbationProc();		// 
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			ImeProc();				// ＩＭＥ关连处理
			FlashKeyboardCursor();	// キーボードカーソル点灭处理
			break;
		*/

#ifdef MULTI_GRABIN
		// アップグレードフォーム
		case PROC_UPGRADE:
			BackBufferDrawType = DRAW_BACK_NORMAL; 
			KeyBoardProc();			// キーボード处理
#ifdef PUK2
			// ＰＵＫ２以降？
			if( giInstallVersion >= 3 ){
				// ＰＵＫ２用
				PUK2_upgradeProc();
			}else{
#endif
			UpGradeProc();
#ifdef PUK2
			}
#endif
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			ImeProc();				// ＩＭＥ关连处理
			FlashKeyboardCursor();	// キーボードカーソル点灭处理
			break;
#endif
			
		// 选择キャラでログインを开始
		case PROC_CHAR_LOGIN_START:
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL; 
			ClrImeCompositionString(); // ウィンドウズのＩＭＥバッファーをクリアする
			InitChat();		/* チャット关连初期化 */
			// charLoginStart();移行はネットワークからデータが来るので
			// それまでに初期化をしておく
			initEvent();			// イベントの初期化
			initMap();				// マップ初期化
			initPcAll();			// PC情报の初期化（クリア）
			initCharObj();			// キャラ管理テーブル初期化
			initOpenServerRequestWindow();	// サーバリクエストウィンドウの初期化
			initFieldProc();		// フィールドの表示物を初期化
			initMapEffect();		// マップエフェクト初期化
			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
			EncountFlag = FALSE;
			logOutFlag = FALSE;
			InitOhtaParam();		// ログイン时の初期化（太田专用）
			InitBattleBakUpCmd();	// 战闘コマンドバックアップ初期化关数			
			initMenuOnce();			// ログイン时に１度だけするメニューの初期化
			initMail();				// メール关连初期化
			readMailFile();			// メールファイル読み込み
			recvBgmNo = -1;			// サーバからの受信ＢＧＭ番号
			recvBgmNoFlag = FALSE;	// recvBgmNoは无效
			SimpleLogoutFlag = FALSE;	// 简易登出フラグ初期化
			initAlbum();			// アルバム初期化
			readAlbumFile();		// アルバムファイルの読み込み
#ifdef PUK2_NEW_MENU	
			initMenu();
#endif
#ifdef PUK3_VEHICLE
			// 乘り物リスト初期化
			VehicleInit();
#endif
#ifdef PUK3_VEHICLE
			// ワープカウンタ初期化
			warpCnt = 0;
#endif
#ifdef PUK2
			mapName[0] = '\0';		// マップ名の初期化
#endif
			ChangeProc( PROC_CHAR_LOGIN );
			// ＢＧＭフェードアウト开始
			fade_out_bgm();

#ifdef PUK2_NEW_MENU	
			{	// アイテムの使用フラグ初期化
				int i;
				for(i=0;i<28;i++) ItemNoOpe[i]=0;
			}
#endif
			break;

		case PROC_CHAR_LOGIN:
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL; 
			characterLoginProc();
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			break;

		// 登出
		case PROC_CHAR_LOGOUT:
			// バックバッファー描画方法变更
//			BackBufferDrawType = DRAW_BACK_NORMAL; 
			characterLogoutProc();
#ifdef PUK2
			RunAction();			// アクション走らせる
#endif
			break;


		case PROC_GAME:     /* ゲームプロセス */
		
			GameProc();
			break;

		case PROC_DISCONNECT_SERVER:
			switch( SubProcNo )
			{
				case 0:
					// 初期化
					// PCリセット
					resetPc();
					// キャラ管理テーブルリセット
					initCharObj();
					// アクション抹杀
					DeathAllAction();
#ifdef PUK2_NEW_MENU	
					initMenu();
#endif
#ifdef PUK3_VEHICLE
					// 乘り物リスト初期化
					VehicleInit();
#endif
					// 演出用初期化
					ProduceInitFlag = TRUE;
					// バトルサーフェスの画像作成 
					CopyBackBuffer();
					// バックバッファー描画方法变更
					BackBufferDrawType = DRAW_BACK_PRODUCE; 
					// ＢＧＭフェードアウト开始
					fade_out_bgm();
					// 现在の时间を记忆
					NowTime = GetTickCount();
#ifdef PUK2_FPS
					NowDrawTime = NowTime;
#endif
					SubProcNo++;

					break;

				case 1:
					// 演出中
#ifdef PUK2
					if( DrawProduce( PRODUCE_DOWN_ACCELE, 0, 0.6f ) == TRUE )
#else
					if( DrawProduce( PRODUCE_DOWN_ACCELE ) == TRUE )
#endif
					{
						BackBufferDrawType = DRAW_BACK_NORMAL; 
						SubProcNo++;
					}
					break;

				case 2:
					// 演出は終わり
					break;
			}
			// 切断时の警告ウィンドウ
			if( disconnectServer() )
			{
				// パラメータセーブ
				saveUserSetting();
				// PCリセット
				resetPc();
				// キャラ管理テーブルリセット
				initCharObj();
				// ネットワーク初期化
				cleanupNetwork();
#ifdef PUK2
				// インストールしているバージョンを调べる
				giInstallVersion = GetInstallVersion( );

				if( giInstallVersion == 3 ){
					PaletteChange( 27, 0 );
				}else{
					// デフォルトパレットに戾す
					PaletteChange( DEF_PAL, 0 );
				}
#else
				// デフォルトパレットに戾す
				PaletteChange( DEF_PAL, 0 );
#endif
				// タイトル画面へ
				ChangeProc( PROC_TITLE_MENU );
				// アクション抹杀
				DeathAllAction();
				// 切断フラグ初期化
				networkDisconnectFlag = 0;
				

				break;
			}
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			break;


		case PROC_BATTLE:     /* バトルプロセス */
		
			BattleProc();
			break;
			
#ifdef _DEBUG		
		case PROC_OHTA_TEST:     /* 太田テストプロセス */
		
			OhtaTestProc();
			break;
			
		//case PROC_TAKE_TEST:     /* 竹内さんテストプロセス */
		
		//	TakeTestProc();
		//	break;

		//case PROC_DWAF_TEST:	// DWAFテスト
		//	dwafTestProc();
		//	break;

		case PROC_SPR_VIEW:		// スプライト确认プロセス
			SprViewProc();
			break;
			
		case PROC_ANIM_VIEW:	// アニメーション确认プロセス
			AnimViewProc();
			break;
			
		case PROC_SE_TEST:	// ＳＥ确认プロセス
			SeTestProc();
			break;
			
		case PROC_D3D_TEST:	// Ｄ３Ｄテストプロセス
			D3dTestProc();
			break;

	#ifdef PUK2
		case PROC_G_ID_VIEW:		// スプライト确认プロセス
			G_IDViewProc();
			break;
	#endif
	#ifdef PUK3_RIDEBIN
		case PROC_COORDINATE_MAKE:	// 座标データ作成プロセス
			AnimCoordinateMakeProc();
			break;
	#endif
			
#endif
		case PROC_ENDING:
			
			break;

#ifdef PUK2_SERVERCHANGE
		case PROC_CHAR_SERVER_CHANGE:
			ServerChangeProc();
			RunAction();
			break;
#endif
	}
#ifdef PUK3_SEGMENTATION_FAULT
	ProcPop();
#endif
}

/* プロセス变更处理 ***********************************************************/
void ChangeProc( int procNo )
{
	// プロセス番号を记忆
	ProcNo = procNo;
	// サブプロセス番号の初期化
	SubProcNo = 0;
}
/* プロセス变更处理 ***********************************************************/
void ChangeProc( int procNo, int subProcNo )
{
	// プロセス番号を记忆
	ProcNo = procNo;
	// サブプロセス番号の初期化
	SubProcNo = subProcNo;
}
/* プロセス变更处理（次のプロセスの最初に切り替える）**************************/
void ChangeProc2( int procNo )
{
	// プロセス番号を记忆
	ProcNo2 = procNo;
	SubProcNo2 = 0;
}
/* プロセス变更处理 ***********************************************************/
void ChangeProc2( int procNo, int subProcNo )
{
	// プロセス番号を记忆
	ProcNo2 = procNo;
	// サブプロセス番号の初期化
	SubProcNo2 = subProcNo;
}

#ifndef PUK2
// ゲームプロセス サブプロセス番号
enum{
	GAME_PROC_INIT,						// ０：初始化
	GAME_PROC_INIT_2,					// １：初期化２
	GAME_PROC_LOGIN_PRODUCE,			// ２：ログイン演出
	GAME_PROC_MAIN,						// ３：メイン处理
	GAME_PROC_BATTLE_IN_PRODUCE_INIT,	// ４：エンカウント演出初期化
	GAME_PROC_BATTLE_IN_PRODUCE,		// ５：エンカウント演出处理
	
	GAME_PROC_CMD_INPUT,				// ４：输入指令
	GAME_PROC_RECV_MOVIE_DATA,			// ５：等待动画受信
	GAME_PROC_MOVIE,					// ６：处理动画
	GAME_PROC_OUT_PRODUCE_INIT,			// ７：结束演出初始化
	GAME_PROC_OUT_PRODUCE,				// ８：演出结束
#ifdef PUK2
	GAME_PROC_INIT_2_2,					// 初期化２のメイン处理
#endif

	GAME_PROC_MAIN_INIT = 20,			// メイン处理初期化

	GAME_PROC_LOGIN_INIT = 100,			// ログイン初期化
	GAME_PROC_LOGIN_WAIT = 101,			// ログイン时データ待ち处理
	GAME_PROC_LOGIN_EFFECT_INIT = 102,	// ログイン演出初期化
	GAME_PROC_LOGIN_EFFECT = 103,		// ログイン演出中
#ifdef PUK2
	GAME_PROC_LOGIN_EFFECT_INIT2 = 104,	// ログイン演出初期化２
#endif

	GAME_PROC_WARP_START_EFFECT_INIT    = 200,	// ワープ开始演出初期化
	GAME_PROC_WARP_START_EFFECT         = 201,	// ワープ开始演出中
	GAME_PROC_WARP_END_EFFECT_WAIT      = 202,	// ワープ时データ待ち处理
	GAME_PROC_WARP_END_EFFECT_INIT      = 203,	// ワープ終了演出初期化
	GAME_PROC_WARP_END_EFFECT           = 204,	// ワープ終了演出中
#ifdef PUK2
	GAME_PROC_WARP_END_EFFECT_INIT2     = 205,	// ワープ終了演出初期化
#endif
#ifdef PUK3_VEHICLE
	GAME_PROC_VEHICLE_MOVING_INIT       = 250,	// 乘り物移动演出初期化
	GAME_PROC_VEHICLE_MOVING            = 251,	// 乘り物移动演出
	GAME_PROC_VEHICLE_END               = 252,	// 乘り物移动演出終了
#endif

	GAME_PROC_OFFLINE_INIT              = 1000,	// 离线模式初期化
	GAME_PROC_OFFLINE_LOGIN_EFFECT_INIT = 1001,	// 离线模式演出初期化
	GAME_PROC_OFFLINE_LOGIN_EFFECT      = 1002,	// 离线模式演出中
	GAME_PROC_OFFLINE_MAIN              = 1003,	// 离线模式メイン处理
};
#endif
/* ゲームプロセス处理 ********************************************************************/
void GameProc( void )
{
	static int now_bgm;
	static BOOL produceFlag;	// 演出フラグ
	int no;
#ifdef PUK2
	char ad;
	static int idleCnt;			// 空回りのカウンタ
#endif

	/* サブプロセス番号で分岐 */
	switch( SubProcNo )
	{
#ifdef _DEBUG
		// 离线模式 初期化
		case GAME_PROC_OFFLINE_INIT:
			initMap();				// 离线模式の时はここでマップ初期化
			ClrImeCompositionString(); // ウィンドウズのＩＭＥバッファーをクリアする
			InitChat();		/* チャット关连初期化 */
			produceFlag = FALSE;	// 演出フラグＯＦＦ
			EncountFlag = FALSE;	// エンカウントフラグＯＦＦ
			SubProcNo = GAME_PROC_OFFLINE_LOGIN_EFFECT_INIT;
			break;

		// 离线模式 ログイン演出画面作成
		case GAME_PROC_OFFLINE_LOGIN_EFFECT_INIT:
			initPc();				// プレイヤーキャラ作成
			resetFieldProc();		// フィールド处理のリセット
			restorePtActCharObjAll();	// キャラ管理テーブルのアクション复活
			initOpenServerRequestWindow();	// サーバリクエストウィンドウの初期化
			initMenu();				// メニュー关连の初期化
#ifdef PUK3_VEHICLE
			// 乘り物リスト初期化
			VehicleInit();
#endif
//			BattleStatusBak[ 0 ] = NULL;// バトル状态初期化
//			BattleStatusReadPointer = BattleStatusWritePointer =0;
			// 入力フォーカス取得
			GetKeyInputFocus( &MyChatBuffer );

			BackBufferDrawType = DRAW_BACK_NORMAL; 

			// 演出画像の作成
			// バッファ初期化
			TimeZonePaletteProc();

			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif
			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			stop_bgm();				//ＢＧＭ停止
			drawMap();				// マップ表示
			menuProc();				// メニュー处理
			// 一行インフォ栏涂りつぶし（黒色）
			//StockBoxDispBuffer( 0, 456, 640, 480, DISP_PRIO_MENU, 0, 1 );
			KeyBoardProc();			// キーボード处理
			ChatProc();				// チャット处理
			ChatBufferToFontBuffer(); // チャットバッファーをフォントバッファに溜める
			ImeProc();				// ＩＭＥ关连处理
			SortDispBuffer(); 	// 表示バッファソート
			// バックサーフェスを黒でクリアー
			ClearBackSurface();	
	#ifdef PUK2
			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
	#endif
			// ＢＭＰをバックサーフェスにセット
			PutBmp();	
			// バックサーフェスからバトルサーフェスへコピー
			lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;
	#endif
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif

			//play_bgm( map_bgm_no );
			play_bgm( 0 );

			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
			// バックバッファー描画方法变更
//			BackBufferDrawType = DRAW_BACK_NORMAL; 
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
			// 演出中
			ProduceInitFlag = TRUE;
#ifdef PUK2
			DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE, 8, 2.0f );
#else
			DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE );
#endif
			//DrawProduce( PRODUCE_BRAN_BIG );
			SubProcNo = GAME_PROC_OFFLINE_LOGIN_EFFECT;
			break;

		// 离线模式 ログイン演出中
		case GAME_PROC_OFFLINE_LOGIN_EFFECT:
			// 演出中
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
#ifdef PUK2
			if( DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE, 8, 2.0f ) == TRUE )
#else
			if( DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE ) == TRUE )
#endif
			//if( DrawProduce( PRODUCE_BRAN_BIG ) == TRUE )
			{
				produceFlag = TRUE;
				fieldInfoTime = GetTickCount();
				SubProcNo = GAME_PROC_OFFLINE_MAIN;
			}
			break;

		// 离线模式 ログイン演出中
		case GAME_PROC_OFFLINE_MAIN:
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL; 
			//highSpeedDrawSw = 1;	// 高速描画スイッチ ON
			highSpeedDrawSw = 0;	// 高速描画スイッチ ON( change by ohta )

			initItemOverlapCheck();	// アイテム重なりチェック处理の初期化
			mapGridCursolProc();	// マップ画面のグリッドカーソル表示
			fieldProc();			// フィールド关连处理
			moveProc();				// 移动处理
			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
			drawFieldInfoWin();		// ワープ后の场所情报

			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			// 指定范围はストックしない
			StockTaskDispBuffer();

//			mapEffectProc();		// マップエフェクト（雨??雪等）

			//drawMap();		// マップ表示
			drawMap2();		// マップ表示
			drawField();	// フィールド画面にいろいろ表示

			KeyBoardProc();			// キーボード处理
			/* チャット处理 */
			ChatProc();
			// チャットバッファーをフォントバッファに溜める
			ChatBufferToFontBuffer(); 
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();		
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			break;
#endif

		case GAME_PROC_INIT:	// 初期化 **********************************************
			encountEndFlag = 0;
#ifdef _DEBUG
			// 离线模式の时
			if( offlineFlag )
			{
				SubProcNo = GAME_PROC_OFFLINE_INIT;
				break;
			}
#endif

			//InitIme();		// ＩＭＥ关连の初期化
			//ClrImeCompositionString(); // ウィンドウズのＩＭＥバッファーをクリアする
			//InitChat();		/* チャット关连初期化 */
			// 演出番号ランダム（偶数のみ）
			//ProduceNo = Rnd( 0, PRODUCE_END - 1 ) & 0xfffffffe;
			produceFlag = FALSE;	// 演出フラグＯＦＦ
			EncountFlag = FALSE;	// エンカウントフラグＯＦＦ
			//フィールドＢＧＭ再生开始
//			play_bgm(0);

			SubProcNo = GAME_PROC_LOGIN_INIT;

		// ログイン时の初期化
		case GAME_PROC_LOGIN_INIT:	
			initPc();				// プレイヤーキャラ作成
			resetFieldProc();		// フィールド处理のリセット
			restorePtActCharObjAll();	// キャラ管理テーブルのアクション复活
			initOpenServerRequestWindow();	// サーバリクエストウィンドウの初期化
			initMenu();		// メニュー关连の初期化
#ifdef PUK3_VEHICLE
			// 乘り物リスト初期化
			VehicleInit();
#endif
			checkNoReadMail();
//			BattleStatusBak[ 0 ] = NULL;// バトル状态初期化
//			BattleStatusReadPointer = BattleStatusWritePointer =0;
			// 入力フォーカス取得
			GetKeyInputFocus( &MyChatBuffer );
			SubProcNo = GAME_PROC_LOGIN_WAIT;

		// ログイン时のデータ待ち处理
		case GAME_PROC_LOGIN_WAIT:
			if( loginFlag )
			{
				BackBufferDrawType = DRAW_BACK_NORMAL; 
				break;
			}
			SubProcNo = GAME_PROC_LOGIN_EFFECT_INIT;

		// ログイン演出初期化
		case GAME_PROC_LOGIN_EFFECT_INIT:
#ifdef PUK2
			idleCnt = 0;			// 空回りのカウンタ
			SubProcNo = GAME_PROC_LOGIN_EFFECT_INIT2;
			break;
		case GAME_PROC_LOGIN_EFFECT_INIT2:
			TimeZonePaletteProc();
			// 时间带处理とパレットチェンジ处理
			timeZoneProc();
			if ( idleCnt < 5 ){
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
				idleCnt++;
				break;
			}
			// 演出画像の作成
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
			skillrebirth = 0;
			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
#else
			// 演出画像の作成
			// バッファ初期化
#ifndef PUK2
TimeZonePaletteProc();
PaletteProc(  );
#endif
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
			skillrebirth = 0;
#endif
			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
#endif
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			stop_bgm();				// ＢＧＭ停止
			drawMap();				// マップ表示
			menuProc();				// メニュー处理
			// 一行インフォ栏涂りつぶし（黒色）
			//StockBoxDispBuffer( 0, 456, 640, 480, DISP_PRIO_MENU, 0, 1 );
			KeyBoardProc();			// キーボード处理
			ChatProc();				// チャット处理
			ImeProc();				// ＩＭＥ关连处理
			SortDispBuffer(); 	// 表示バッファソート
			// バックサーフェスを黒でクリアー
			ClearBackSurface();	
	#ifdef PUK2
			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
	#endif
			// ＢＭＰをバックサーフェスにセット
			PutBmp();	
			// バックサーフェスからバトルサーフェスへコピー
			lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;
	#endif
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif


#ifdef PUK2
			if(recvBgmNoFlag){
				play_map_bgm( recvBgmNo );
				// マップＢＧＭ番号をバッファに入れる
				setUserMapBgmNo( selectPcNo );
				saveNowState();
				play_bgm( map_bgm_no );
				now_bgm = map_bgm_no;
				recvBgmNoFlag = FALSE;
				draw_map_bgm_flg = 1;
			}else{
				play_bgm( map_bgm_no );
			}
#else
			play_bgm( map_bgm_no );
#endif
			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
			// バックバッファー描画方法变更
//			BackBufferDrawType = DRAW_BACK_NORMAL; 
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
			// 演出中
			ProduceInitFlag = TRUE;
#ifdef PUK2
			DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE, 8, 2.0f );
#else
			DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE );
#endif
			//DrawProduce( PRODUCE_BRAN_BIG );
			SubProcNo = GAME_PROC_LOGIN_EFFECT;
			TimeZonePaletteProc();
			break;

		// ログイン演出中
		case GAME_PROC_LOGIN_EFFECT:
			// 演出中
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
#ifdef PUK2
			if( DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE, 8, 2.0f ) == TRUE )
#else
			if( DrawProduce( PRODUCE_UP_DOWN_LINE_BRAKE ) == TRUE )
#endif
			//if( DrawProduce( PRODUCE_BRAN_BIG ) == TRUE )
			{
				produceFlag = TRUE;
				fieldInfoTime = GetTickCount();
				
#ifdef _DEBUG
				// オートデバッグモードＯＮの时
				if( debugonFlag == TRUE ){
					// チャット文字列送信
					chatStrSendForServer( "[nr debug on]", MyChatBuffer.color );     //MLHIDE
				}
#endif
				
				
				SubProcNo = GAME_PROC_MAIN_INIT;
			}
			break;

		// ワープ开始演出初期化
		case GAME_PROC_WARP_START_EFFECT_INIT:
			
			ProduceInitFlag = TRUE;

			// バッファ初期化
			//DispBuffer.DispCnt = 0;
			//FontCnt = 0;
			
			// ワープ开始音
			//play_se( SE_NO_WARP, 320, 240 );
			
			// バトルサーフェスの画像作成 
			CopyBackBuffer();

			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif

#ifdef PUK2
#else
			// 何かのメニューが出ていたら关闭
			menuClose( MENU_ALL );
			// メニュー处理
			menuProc();	
			// メニュー初期化
//			initMenu();	杉
			// メニュー处理
			//menuProc();	
			fieldProc();			// フィールド关连处理
			
			fieldInfoTime = 0;		// 场所情报が出てたら消す
			drawFieldInfoWin();		// ワープ后の场所情报
#endif

#ifdef PUK2
			// メニュー初期化
			initMenu();
#endif			



			// SプロトコルのCで warpEffectProc();が呼ばれて
			// 画面を作るのでここではしなくて良い
			SubProcNo = GAME_PROC_WARP_START_EFFECT;


		// ワープ开始演出中
		case GAME_PROC_WARP_START_EFFECT:
			// 演出中
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
			//NowTime = GetTickCount();
#ifdef PUK2_FPS
			//NowDrawTime = NowTime;
#endif
			
			// 简易登出の时
			if( SimpleLogoutFlag == TRUE ){
				no = PRODUCE_UP_DOWN_LINE_ACCELE;
#ifdef PUK2
				ad = 8;
#endif
			}else{
				no = PRODUCE_LINE_HAGARE_OCHI_OUT;
#ifdef PUK2
				ad = 32;
#endif
			}

#ifdef PUK2
			if( DrawProduce( no, ad, 2.0f ) == TRUE )
#else
			if( DrawProduce( no ) == TRUE )
#endif
			//if( DrawProduce( PRODUCE_CENTER_PRESSIN ) == TRUE )
			//if( DrawProduce( PRODUCE_LEFT_RIGHT_ACCELE ) == TRUE )
			//if( DrawProduce( PRODUCE_UNERI_ACCELE ) == TRUE )
			{
				SubProcNo = GAME_PROC_WARP_END_EFFECT_WAIT;
			}
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			
			//drawField();	// フィールド画面にいろいろ表示
			//menuProc();		// メニュー处理
			//ImeProc();		// ＩＭＥ关连处理
			break;

		// ワープ时データ待ち处理
		case GAME_PROC_WARP_END_EFFECT_WAIT:
			if( warpEffectStart )
			{
				BackBufferDrawType = DRAW_BACK_NORMAL; 
				//drawField();	// フィールド画面にいろいろ表示
				menuProc();		// メニュー处理
				ImeProc();		// ＩＭＥ关连处理
				break;
			}
			warpEffectStart = FALSE;
			SubProcNo = GAME_PROC_WARP_END_EFFECT_INIT;

			
		// ワープ終了演出初期化
		case GAME_PROC_WARP_END_EFFECT_INIT:
#ifdef PUK2
			idleCnt = 0;			// 空回りのカウンタ
			SubProcNo = GAME_PROC_WARP_END_EFFECT_INIT2;
			break;
		case GAME_PROC_WARP_END_EFFECT_INIT2:
			TimeZonePaletteProc();
			// 时间带处理とパレットチェンジ处理
			timeZoneProc();
			if ( idleCnt < 5 ){
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
				idleCnt++;
				break;
			}
			// 演出画像の作成
			// 战闘から拔けてきた时もパレットを元に戾す
			TimeZonePaletteProc();
			PaletteProc(  );

			// ワープ終了音
			//play_se( SE_NO_WARP2, 320, 240 );
			
			DispBuffer.DispCnt = 0;
			FontCnt = 0;

			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();

			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
#else
			// 演出画像の作成
// 战闘から拔けてきた时もパレットを元に戾す
TimeZonePaletteProc();
PaletteProc(  );

			// ワープ終了音
			//play_se( SE_NO_WARP2, 320, 240 );
			
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif
			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
#endif
			RunAction();			// アクション走らせる
			StockTaskDispBuffer();	// タスク表示データをバッファに溜める
			redrawMap();
			drawMap();				//	マップ表示
			drawField();	// フィールド画面にいろいろ表示

#if 1
			// エフェクトのクリア
			if( (mapEffectRainLevel == 0 && oldMapEffectRainLevel != 0 )
			 || (mapEffectSnowLevel == 0 && oldMapEffectSnowLevel != 0 ) )
			{
				initMapEffect();
			}
#endif
#ifdef PUK3_WHALE_SHIP
			// 云の流れの土台の动きを初期化
			mapEffectCloudMoveX = mapEffectCloudMoveY = 0;
#endif

			// 一行インフォ栏涂りつぶし（黒色）
			//StockBoxDispBuffer( 0, 456, 640, 480, DISP_PRIO_MENU, 0, 1 );
			KeyBoardProc();			// キーボード处理
			ChatProc();				// チャット处理
			SortDispBuffer(); 	// 表示バッファソート
			// バックサーフェスを黒でクリアー
			ClearBackSurface();	
#if 1
	#ifdef PUK3_WHALE_SHIP
			delMapEffectCloud();
	#endif
			if( (mapEffectRainLevel != 0 && oldMapEffectRainLevel == 0 )
			 || (mapEffectSnowLevel != 0 && oldMapEffectSnowLevel == 0 ) )
			{
				mapEffectProc2( 80 );		// マップエフェクト（雨??雪等）
			}
#endif
	#ifdef PUK2
			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
	#endif
			// ＢＭＰをバックサーフェスにセット
			PutBmp();	
			// バックサーフェスからバトルサーフェスへコピー
			lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;
	#endif
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif

//			if( map_bgm_no != now_bgm )
//			{
//				stop_bgm();				//ＢＧＭ停止
//				play_bgm( map_bgm_no );
//			}

			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif

			ProduceInitFlag = TRUE;

			SubProcNo = GAME_PROC_WARP_END_EFFECT;

			// バッファ初期化
			TimeZonePaletteProc();
			
		// ワープ終了演出中
		case GAME_PROC_WARP_END_EFFECT:
			// 演出中
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
			
			// 简易登出の时
			if( SimpleLogoutFlag == TRUE ){
				no = PRODUCE_UP_DOWN_LINE_BRAKE;
#ifdef PUK2
				ad = 8;
#endif
			}else{
				no = PRODUCE_LINE_HAGARE_OCHI_IN;
#ifdef PUK2
				ad = 32;
#endif
			}
			
#ifdef PUK2
			if( DrawProduce( no, ad, 2.0f ) == TRUE )
#else
			if( DrawProduce( no ) == TRUE )
#endif
			//if( DrawProduce( PRODUCE_CENTER_PRESSOUT ) == TRUE )
			//if( DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE ) == TRUE )
			//if( DrawProduce( PRODUCE_UNERI_BRAKE ) == TRUE )
			{
#ifdef PUK2
				// メニュー初期化
				initMenu();
#endif			
				fieldInfoTime = GetTickCount();
				SubProcNo = GAME_PROC_MAIN_INIT;
				// 简易登出フラグ初期化
				SimpleLogoutFlag = FALSE;
				
				//ＢＧＭがなければ
				if( recvBgmNoFlag )
				{
					play_map_bgm( recvBgmNo );
					// マップＢＧＭ番号をバッファに入れる
					setUserMapBgmNo( selectPcNo );
					saveNowState();
					play_bgm( map_bgm_no );
					now_bgm = map_bgm_no;
					recvBgmNoFlag = FALSE;
					draw_map_bgm_flg = 1;
				}
				// 何かのメニューが出ていたら关闭	// ohta
				menuClose( MENU_ALL );
				// メニュー处理	// ohta
				menuProc();	
				
			}
			
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			//drawField();	// フィールド画面にいろいろ表示
			//menuProc();		// メニュー处理
			//ImeProc();		// ＩＭＥ关连处理
			break;
#ifdef PUK3_VEHICLE
		case GAME_PROC_VEHICLE_MOVING_INIT:	// 乘り物移动演出初期化
			// メニュー初期化
			initMenu();
			delMapEffectCloud();
			if( (mapEffectRainLevel != 0 && oldMapEffectRainLevel == 0 )
			 || (mapEffectSnowLevel != 0 && oldMapEffectSnowLevel == 0 ) )
			{
				mapEffectProc2( 80 );		// マップエフェクト（雨??雪等）
			}
			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
			// ＢＭＰをバックサーフェスにセット
			PutBmp();	
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;

			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();

			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif

			ProduceInitFlag = TRUE;

			SubProcNo = GAME_PROC_WARP_END_EFFECT;

			// バッファ初期化
			TimeZonePaletteProc();

			map_bgm_no = vehicleProcInit();
			// ＢＧＭ变更
			play_bgm( map_bgm_no );
			now_bgm = map_bgm_no;

			SubProcNo = GAME_PROC_VEHICLE_MOVING;
		case GAME_PROC_VEHICLE_MOVING:		// 乘り物移动演出
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL;

			vehicleProc();

			TimeZonePaletteProc();

			fieldProc();			// フィールド关连处理
//			moveProc();				// 移动处理

			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			// 指定范围はストックしない
			StockTaskDispBuffer();

//			drawMap();		// マップ表示
			//drawMap2();		// マップ表示
			drawField();	// フィールド画面にいろいろ表示
			
			KeyBoardProc();			// キーボード处理
			/* チャット处理 */
			ChatProc();



			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();		
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// 时间带处理とパレットチェンジ处理
			timeZoneProc();
			break;
		case GAME_PROC_VEHICLE_END:		// 乘り物移动演出終了
			vehicleProcEnd();

			// 初期化
			warpCnt = 0;

			// メニュー初期化
			initMenu();
			fieldInfoTime = GetTickCount();

			delMapEffectCloud();
			if( (mapEffectRainLevel != 0 && oldMapEffectRainLevel == 0 )
			 || (mapEffectSnowLevel != 0 && oldMapEffectSnowLevel == 0 ) )
			{
				mapEffectProc2( 80 );		// マップエフェクト（雨??雪等）
			}

			// パレットチェンジ用

			TimeZonePaletteProc();

			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化

			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			// 指定范围はストックしない
			StockTaskDispBuffer();

			redrawMap();
			drawMap();		// マップ表示
			drawField();	// フィールド画面にいろいろ表示

			// メニュー处理
			menuProc();

			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
			// ＢＭＰをバックサーフェスにセット
			PutBmp();	
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;

			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif

			// 画面の描画用

			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();

			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化

			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			// 指定范围はストックしない
			StockTaskDispBuffer();

			drawMap();		// マップ表示
			drawField();	// フィールド画面にいろいろ表示

			// メニュー处理
			menuProc();


			
			// 简易登出フラグ初期化
			SimpleLogoutFlag = FALSE;
			
			//ＢＧＭがなければ
			if( recvBgmNoFlag )
			{
				play_map_bgm( recvBgmNo );
				// マップＢＧＭ番号をバッファに入れる
				setUserMapBgmNo( selectPcNo );
				saveNowState();
				play_bgm( map_bgm_no );
				now_bgm = map_bgm_no;
				recvBgmNoFlag = FALSE;
				draw_map_bgm_flg = 1;
			}

			SubProcNo = GAME_PROC_MAIN_INIT;
			break;
#endif


		case GAME_PROC_INIT_2:	// 初期化２ （战闘から戾ってきた时の演出初期化）**********************************************
#ifdef PUK2
			idleCnt = 0;			// 空回りのカウンタ
			SubProcNo = GAME_PROC_INIT_2_2;
			break;
		case GAME_PROC_INIT_2_2:
			TimeZonePaletteProc();
			// 时间带处理とパレットチェンジ处理
			timeZoneProc();
			// ここだけ长く取る
			if ( idleCnt < 30 ){
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
				idleCnt++;
				break;
			}
			// GAME_PROC_INIT_2 の次のプロセスに移るので、GAME_PROC_INIT_2_2 から GAME_PROC_INIT_2 にプロセスを变えておく
			SubProcNo = GAME_PROC_INIT_2;
#endif

			encountEndFlag = 1;
			initPc();				// プレイヤーキャラ作成
			resetFieldProc();		// フィールド处理のリセット
			restorePtActCharObjAll();	// キャラ管理テーブルのアクション复活
			initOpenServerRequestWindow();	// サーバリクエストウィンドウの初期化
			initMenu();		// メニュー关连の初期化
#ifdef PUK3_VEHICLE
			// 乘り物リスト初期化
			VehicleInit();
#endif
			checkNoReadMail();
//			BattleStatusBak[ 0 ] = NULL;// バトル状态初期化
//			BattleStatusReadPointer = BattleStatusWritePointer =0;
#ifdef PUK3_MAIL_ETC2
			// メニューの处理で设定しているのでここではしない
#else
			// 入力フォーカス取得
			GetKeyInputFocus( &MyChatBuffer );
#endif
#ifdef PUK2_ACID_PAPER_SNOWSTORM
#else
#ifdef PUK2_NEWSKILL
			// エフェクトの状态を戾す
			mapEffectRainLevel = memMapEffectRainLevel;
			mapEffectSnowLevel = memMapEffectSnowLevel;
			mapEffectKamiFubukiLevel = memMapEffectKamiFubukiLevel;
#endif
#endif

			// 演出する时
			if( produceFlag == TRUE ){

				// 演出画像の作成
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
				initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
				RunAction();			// アクション走らせる
				StockTaskDispBuffer();	// タスク表示データをバッファに溜める
				stop_bgm();				//ＢＧＭ停止
				updateMapAreaFromMapPos();
				redrawMap();
				drawMap();				//	マップ表示
				//ＢＧＭがなければ
				if( !draw_map_bgm_flg )
				{
					if( recvBgmNoFlag )
					{
						play_map_bgm( recvBgmNo );
						// マップＢＧＭ番号をバッファに入れる
						setUserMapBgmNo( selectPcNo );
						saveNowState();
						play_bgm( map_bgm_no );
						now_bgm = map_bgm_no;
						recvBgmNoFlag = FALSE;
						recvBgmNo = -1;
					}
					else
					{
						//さっきのＢＧＭ再生
						play_bgm( map_bgm_no = now_bgm );
					}
					draw_map_bgm_flg = 1;
				}
				menuProc();				// メニュー处理
#ifdef PUK2_NEWSKILL
				// エフェクトのクリア
				if( (mapEffectRainLevel == 0 && oldMapEffectRainLevel != 0 )
				 || (mapEffectSnowLevel == 0 && oldMapEffectSnowLevel != 0 ) )
				{
					initMapEffect();
				}
#endif
#ifdef PUK3_WHALE_SHIP
				// 云の流れの土台の动きを初期化
				mapEffectCloudMoveX = mapEffectCloudMoveY = 0;
#endif
				// 一行インフォ栏涂りつぶし（黒色）
				//StockBoxDispBuffer( 0, 456, 640, 480, DISP_PRIO_MENU, 0, 1 );
				KeyBoardProc();			// キーボード处理
				ChatProc();				// チャット处理
				ImeProc();				// ＩＭＥ关连处理
				SortDispBuffer(); 	// 表示バッファソート
				// バックサーフェスを黒でクリアー
				ClearBackSurface();	
#ifdef PUK2_NEWSKILL
	#ifdef PUK3_WHALE_SHIP
				delMapEffectCloud();
	#endif
				if( (mapEffectRainLevel != 0 && oldMapEffectRainLevel == 0 )
				 || (mapEffectSnowLevel != 0 && oldMapEffectSnowLevel == 0 ) )
				{
					mapEffectProc2( 80 );		// マップエフェクト（雨??雪等）
				}
#endif
#ifdef PUK2
				TimeZonePaletteProc();
				PaletteProc(  );
				// 时间带处理とパレットチェンジ处理
				timeZoneProc();
#endif
	#ifdef PUK2
				// 色变え制限を一时的にＯＦＦ
				LimiteLoadBmpFlag = FALSE;
	#endif
				// ＢＭＰをバックサーフェスにセット
				PutBmp();	
				// バックサーフェスからバトルサーフェスへコピー
				lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
				// 色变え制限をＯＮ
				LimiteLoadBmpFlag = TRUE;
	#endif
				// バッファ初期化
				DispBuffer.DispCnt = 0;
				FontCnt = 0;
#ifdef PUK2
				// フォントプライオリティ制御バッファの初期化
				FontPrioInit();
#endif
				// 现在の时间を记忆
				NowTime = GetTickCount();
#ifdef PUK2_FPS
				NowDrawTime = NowTime;
#endif
				// バックバッファー描画方法变更
				BackBufferDrawType = DRAW_BACK_PRODUCE; 
				// 演出中
				//DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE );
				// 简易登出の时
				if( SimpleLogoutFlag == TRUE ){
					no = PRODUCE_UP_DOWN_LINE_BRAKE;
				}else{
					no = PRODUCE_UNERI_BRAKE;
				}
#ifdef PUK2
				DrawProduce( no, 8, 2.0f );
#else
				DrawProduce( no );
#endif
			}else{
				// 演出フラグＯＮ
				produceFlag = TRUE;
				SubProcNo++;
			}
			
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			
			// 战闘から拔けてきた时もパレットを元に戾す
			TimeZonePaletteProc();
			
			SubProcNo++;
			
			break;
		
		case GAME_PROC_LOGIN_PRODUCE:	// 战闘から拔けてきたときの演出中 ***********************************
			
			// 简易登出の时
			if( SimpleLogoutFlag == TRUE ){
				no = PRODUCE_UP_DOWN_LINE_BRAKE;
			}else{
				no = PRODUCE_UNERI_BRAKE;
			}
			// 演出中
#ifdef PUK2
			if( DrawProduce( no, 8, 2.0f ) == TRUE ){
#else
			if( DrawProduce( no ) == TRUE ){
#endif
			//if( DrawProduce( PRODUCE_LEFT_RIGHT_BRAKE ) == TRUE ){
				warpEffectStart = FALSE;
				// 怪我情报处理
				//BattleInjuryDisp();
				SubProcNo = GAME_PROC_MAIN_INIT;
				// 简易登出フラグ初期化
				SimpleLogoutFlag = FALSE;
			}
			
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// 战闘から拔けてきた时もパレットを元に戾す
			TimeZonePaletteProc();
			
			break;


		// メイン处理初期化
		case GAME_PROC_MAIN_INIT:
			BackBufferDrawType = DRAW_BACK_NORMAL; 
#if 0
			drawTile();			// タイルだけ表示
			SortDispBuffer(); 	// 表示バッファソート
			// バックサーフェスを黒でクリアー
			ClearBackSurface();	
	#ifdef PUK2
			// 色变え制限を一时的にＯＦＦ
			LimiteLoadBmpFlag = FALSE;
	#endif
			// ＢＭＰをバックサーフェスにセット
			PutBmp();	
			// バックサーフェスからバトルサーフェスへコピー
			lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
			// 色变え制限をＯＮ
			LimiteLoadBmpFlag = TRUE;
	#endif
			// バッファ初期化
			DispBuffer.DispCnt = 0;
			FontCnt = 0;
#ifdef PUK2
			// フォントプライオリティ制御バッファの初期化
			FontPrioInit();
#endif
			// 现在の时间を记忆
			NowTime = GetTickCount();
#ifdef PUK2_FPS
			NowDrawTime = NowTime;
#endif
#else
			repairMap();
#endif
			SubProcNo = GAME_PROC_MAIN;

		
		case GAME_PROC_MAIN:	// メイン处理 ********************************************
		
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_NORMAL;
			//highSpeedDrawSw = 1;	// 高速描画スイッチ ON
			highSpeedDrawSw = 0;	// 高速描画スイッチ ON( change by ohta )

			TimeZonePaletteProc();

			initItemOverlapCheck();	// アイテム重なりチェック处理の初期化
			mapGridCursolProc();	// マップ画面のグリッドカーソル表示
			
			// 战闘结果ウィンドウの表示チェック
			CheckBattleResultMenu();
			// マップウィンドウの自动オープンチェック
			//CheckAutoMapOpen();
			
			fieldProc();			// フィールド关连处理
			moveProc();				// 移动处理
			
			
#ifdef _DEBUG
			// エンカウントボタン
			if( VK[ VK_NEXT ] & KEY_ON_ONCE ){
				if( pc.ptAct != NULL ){
					if( autoMapOpenFlag == FALSE ){	// ohta
						autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
					}
					resetMap();					// マップ处理リセット
					// エンカウント送信
					nrproto_EN_send( sockfd, pc.ptAct->gx, pc.ptAct->gy );
					eventEnemyFlag = 0;
				}
			}
#endif

			initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化
			drawFieldInfoWin();		// ワープ后の场所情报

			/* アクション走らせる */
			RunAction();
			// タスク表示データをバッファに溜める
			// 指定范围はストックしない
			StockTaskDispBuffer();

			mapEffectProc();		// マップエフェクト（雨??雪等）

			drawMap();		// マップ表示
			//drawMap2();		// マップ表示
			drawField();	// フィールド画面にいろいろ表示
			
			KeyBoardProc();			// キーボード处理
			/* チャット处理 */
			ChatProc();
			

			
			// メニュー处理
			menuProc();
			// ＩＭＥ关连处理
			ImeProc();		
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			// 时间带处理とパレットチェンジ处理
			timeZoneProc();
			
			
			// エンカウントした时
			if( EncountFlag == TRUE ){
				// オートマップウィンドウフラグ记忆
				if( autoMapOpenFlag == FALSE ){	// ohta
					autoMapOpenFlag = checkMenuOpenNo( MENU_AUTOMAP );
				}
				resetPc();				// PCリセット
				resetCharObj();			// キャラ管理テーブルリセット
				resetMap();				// マップ处理リセット
				clearPtActPartyParam();	// 仲间情报のアクションポインタだけをNULLにする
				fieldInfoTime = 0;		// 场所情报が出てたら消す
				drawFieldInfoWin();		// ワープ后の场所情报
				resetFieldProc();		// フィールド处理のリセット
				nowEncountPercentage = minEncountPercentage;// エンカウント率を最小に戾す
				sendEnFlag = 0;
				encountNowFlag = 1;
				eventEnemySendFlag = 0;
				duelSendFlag = 0;
				jbSendFlag = 0;
				// マウスのオート移动をＯＦＦにする
				mouseCursorMode = MOUSE_CURSOR_MODE_NORMAL;
				
				// 演出初期化
				ProduceInitFlag = TRUE;
				// 何かのメニューが出ていたら关闭
				menuClose( MENU_ALL );
				initMenu();			// メニュー关连の初期化
#ifdef PUK3_VEHICLE
				// 现存の乘り物を全て削除
				delVehicleAll();
				// ＰＣを见えるようにする
				delPcInvisible();
#endif
#ifdef PUK3_ENCOUNT_NULL_GRA
				// エンカウント初期化处理の直前に画面描画が行われていないとまずいので
				surelyDispDraw();
#endif
				
				// エンカウント音
				play_se( 201, 320, 240 );
				// 现在のＢＧＭ保存
				now_bgm = t_music_bgm_no;
				// ＢＧＭ停止
				stop_bgm();
				SubProcNo++;
				//ChangeProc2( PROC_GAME, SubProcNo+1 );
			}
			break;
			
		case GAME_PROC_BATTLE_IN_PRODUCE_INIT:	// エンカウント演出初期化 ****************
			// バトルサーフェスの画像作成 
			CopyBackBuffer();
			// バックバッファー描画方法变更
			BackBufferDrawType = DRAW_BACK_PRODUCE; 
#ifdef PUK2
			initMenu();			// メニュー关连の初期化
#endif
#ifdef PUK3_VEHICLE
			// 现存の乘り物を全て削除
			delVehicleAll();
#endif
			SubProcNo++;
			
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();

			break;
			
		case GAME_PROC_BATTLE_IN_PRODUCE:	// エンカウント演出处理 **********************
		
			// 演出中
			//if( DrawProduce( PRODUCE_UP_DOWN_LINE_ACCELE ) == TRUE ){
#ifdef PUK2
			if( DrawProduce( PRODUCE_UNERI_ACCELE, 8, 2.0f ) == TRUE ){
#else
			if( DrawProduce( PRODUCE_UNERI_ACCELE ) == TRUE ){
#endif
			//if( DrawProduce( PRODUCE_HAGARE_OCHI_OUT ) == TRUE ){
			//if( DrawProduce( PRODUCE_LEFT_RIGHT_ACCELE ) == TRUE ){
			//if( GameState == GAME_ENCOUNT_TO_BATTLE ){ 
#ifdef PUK2_ACID_PAPER_SNOWSTORM
#else
#ifdef PUK2_NEWSKILL
				// エフェクトの状态を保存
				memMapEffectRainLevel = mapEffectRainLevel;
				memMapEffectSnowLevel = mapEffectSnowLevel;
				memMapEffectKamiFubukiLevel = mapEffectKamiFubukiLevel;
				initMapEffect();
#endif
#endif
				// プロセスチェンジ
				ChangeProc( PROC_BATTLE );
			}
			//menuProc();	// メニュー处理
			//ImeProc();	// ＩＭＥ关连处理
			
			// キーボード处理
			KeyBoardProc();
			// 自分の入力をフォントバッファへ溜める
			StockFontBuffer2( &MyChatBuffer );
			// ＩＭＥ关连处理
			ImeProc();
			// キーボードカーソル点灭处理
			FlashKeyboardCursor();
			
			break;
	}
}


// ワープ演出用
void warpEffectProc( void )
{
	oldMapEffectRainLevel = mapEffectRainLevel;
	oldMapEffectSnowLevel = mapEffectSnowLevel;
#ifdef PUK3_VEHICLE
	// 简易登出でなく、自分が乘り物に乘っているなら专用のプロセスへ
	if ( !SimpleLogoutFlag && getRideVehicle( pc.id ) ){
		if ( warpCnt == 0 ){
			SubProcNo = GAME_PROC_VEHICLE_MOVING_INIT;

			// 乘り物移动中プロセスの初期化
			vehicleProcInit_in_warpEffectProc();
		}else{
			SubProcNo = GAME_PROC_VEHICLE_END;
		}

		warpCnt++;
	}else{
		warpCnt = 0;

		// 简易登出なら
		if ( SimpleLogoutFlag ){
			// キャラ见えるようにする
			delPcInvisible();
		}
	}
	// 现存の乘り物を全て削除
	delVehicleAll();
#endif

#if 0
	DispBuffer.DispCnt = 0;
	FontCnt = 0;
#ifdef PUK2
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();
#endif

#ifdef PUK2
	// パレット关系の处理はしない
	//TimeZonePaletteProc();

	initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化

	/* アクション走らせる */
	RunAction();
	// タスク表示データをバッファに溜める
	// 指定范围はストックしない
	StockTaskDispBuffer();

	mapEffectProc();		// マップエフェクト（雨??雪等）

	drawMap();		// マップ表示
	//drawMap2();		// マップ表示
	drawField();	// フィールド画面にいろいろ表示
	
	KeyBoardProc();			// キーボード处理
	/* チャット处理 */
	ChatProc();

	// メニュー处理
	menuProc();
	// ＩＭＥ关连处理
	ImeProc();		
	// キーボードカーソル点灭处理
	FlashKeyboardCursor();
	// パレット关系の处理はしない
	//// 时间带处理とパレットチェンジ处理
	//timeZoneProc();
#else
	// 何かのメニューが出ていたら关闭
	menuClose( MENU_ALL );
	menuProc();	// メニュー处理
	initMenu();
#ifdef PUK3_VEHICLE
	// 现存の乘り物を全て削除
	delVehicleAll();
#endif

	//fieldProc();			// フィールド关连处理
	initCharPartsPrio();	// キャラ??布ツの优先顺位决定处理の初期化

	fieldInfoTime = 0;		// 场所情报が出てたら消す
	drawFieldInfoWin();		// ワープ后の场所情报


	/* アクション走らせる */
	RunAction();
	// タスク表示データをバッファに溜める
	StockTaskDispBuffer();

	mapEffectProc();		// マップエフェクト（雨??雪等）

	redrawMap();
	drawMap();		// マップ表示
	drawField();	// フィールド画面にいろいろ表示 // ohta

	KeyBoardProc();			// キーボード处理
	/* チャット处理 */
	ChatProc();
	// 时间带处理とパレットチェンジ处理
//	timeZoneProc();

	SortDispBuffer(); 	// 表示バッファソート
#endif

	// バックサーフェスを黒でクリアー
	ClearBackSurface();	

	// 高速描画スイッチ OFF
	highSpeedDrawSw = 0;
	#ifdef PUK2
		// 色变え制限を一时的にＯＦＦ
		LimiteLoadBmpFlag = FALSE;
	#endif
	// ＢＭＰをバックサーフェスにセット
	PutBmp();	

	// バックサーフェスからバトルサーフェスへコピー
	lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
		// 色变え制限をＯＮ
		LimiteLoadBmpFlag = TRUE;
	#endif
	
	// バッファ初期化
	//DispBuffer.DispCnt = 0;
	//FontCnt = 0;
	
	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif
	
#endif

}


void repairMap( void )
{
	char _c_tmp;	// char型テンポラリ变数

	resetHighSpeedDraw();

	DispBuffer.DispCnt = 0;
	FontCnt = 0;
#ifdef PUK2
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();
#endif
	drawTile();		// タイル表示
	SortDispBuffer(); 	// 表示バッファソート
	// バックサーフェスを黒でクリアー
	ClearBackSurface();	
	// ＢＭＰをバックサーフェスにセット
	_c_tmp = highSpeedDrawSw;
	highSpeedDrawSw = 0;	// 高速描画スイッチ OFF
	#ifdef PUK2
		// 色变え制限を一时的にＯＦＦ
		LimiteLoadBmpFlag = FALSE;
	#endif
	PutBmp();	
	highSpeedDrawSw = _c_tmp;
	// バックサーフェスからバトルサーフェスへコピー
	lpBattleSurface->BltFast( 0, 0, lpDraw->lpBACKBUFFER, NULL, DDBLTFAST_WAIT );
	#ifdef PUK2
		// 色变え制限をＯＮ
		LimiteLoadBmpFlag = TRUE;
	#endif
	DispBuffer.DispCnt = 0;
	FontCnt = 0;
#ifdef PUK2
	// フォントプライオリティ制御バッファの初期化
	FontPrioInit();
#endif
	// 现在の时间を记忆
	NowTime = GetTickCount();
#ifdef PUK2_FPS
	NowDrawTime = NowTime;
#endif
}

// 时间からパレット番号に变换する ******************************************
void TimeZonePaletteProc( void )
{
	if( palNo == -1 )
	{
		// パレットチェンジ
		PaletteChange( nrTimeZoneNo, 0 );
		// 时间带でパレットチェンジする时
		TimeZonePalChangeFlag = TRUE;
		palNo = -2;
	}
	else
	if( palNo >= 0 )
	{
		// 固定パレット设定
		PaletteChange( palNo, 0 );// パレットチェンジ
		// 时间带でパレットチェンジしない时
		TimeZonePalChangeFlag = FALSE;
		palNo = -2;
	}
}

