﻿//-----------------------------------------------------------------------
//
//新ＭＡＰ表示プログラム
//
//-----------------------------------------------------------------------

#include"../puk2/map/newmap.h"
#include"../systeminc/action.h"
#include"../systeminc/pattern.h"
#include "../systeminc/t_music.h"
#include "../puk2/newdraw/graphic_ID.h"

float SugiMapDrawX,SugiMapDrawY;
int SugiIntWork1,SugiIntWork2;
SugiMapStatus	SugiMapStat;
SugiCutFileFormat SugiCutFile;
AnimeMapStatusStruct AniMapST;
unsigned char SugiAutoMapDate[DeAutoMapSize][3];
void SugiCutDateClear(void);

// 6/27 20:00 齐藤２追加
int		cutNo;

#ifdef HIT_VIEW
int hitViewFlag;
#endif

//char szMapFaqUrl[] = "http://saru.square-enix.co.jp/crossgate/faq/bg.html#faq";
char szMapFaqUrl[] = "";

//
// マップ表示
//
void drawMap_Sugi(BOOL sound)
{
	int i, j;
	int ti, tj, iTileGraNo, iPartsGraNo,pos;
	int x, y, tx, ty;
	S2 xx, yy;
	U4 bmpNo;
	BLT_MEMBER BltMember={0};
	int BmpNumber;
//	int AnimePos;
#ifdef PUK3_CHECK_VALUE
	ACTION *pAct;
#endif

	if(SugiMapStat.MapVer ==1){
	//新マップ描画----------------------------------------------------------------------

		draw_map_bgm_flg = 0;

		readMapAfterFrame++;

		// 现在のグリッド位置が变わったらファイルから読み込む
		if( mapGx != oldMapGx || mapGy != oldMapGy || mapInsideFlag ){
			if( readMap( mapKind, mapNo, mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0] ) >= 0 ){
				// hitMap[]にあたり判定データを设定する
				readHitMap_PUK2( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
					&mapTile[0], &mapParts[0],&mapEvent[0], &mapHit[0] );
					
				if( mapEmptyFlag ){
					if( !checkEmptyMap( mapEmptyDir ) ){
						// 空マップのフラグをはずす drawMap()
						mapEmptyFlag = FALSE;
						//收到数据，允许移动。
						autoMappingInitFlag = TRUE;	// オートマップ読み込み
					}else{
						//等待数据中。
					}
				}else{
					autoMappingInitFlag = TRUE;	// オートマップ読み込み
				}

				readMapAfterFrame = 0;	// マップデータを読み込んでからの経过フレームをクリア
				mapInsideFlag = 0;
			}else{
				//地图读取失败。
				return;
			}
		}

		resetHighSpeedDraw();

		tx = highSpeedDrawNowX2 + (mapAreaX1+mapAreaY2-1) * SURFACE_WIDTH/2;
		ty = highSpeedDrawNowY2 + (-mapAreaX1+mapAreaY2-1) * SURFACE_HEIGHT/2;

		// 处理の顺番が以下のように处理する
		//
		// [map]
		//               16
		//            15    14
		//         13    12    11
		//      10     9     8     7
		//          6     5     4
		//             3     2
		//                1

		ti = mapAreaHeight-1;
		tj = 0;

		while( ti >= 0 ){
			i = ti;
			j = tj;

			x = tx;
			y = ty;

			//一行	
			while( i >= 0 && j >= 0 ){

				pos = i*mapAreaWidth+j;

				i=i;
				j=j;

				// タイル表示
				iTileGraNo = (mapTile[pos]);

				if(iTileGraNo>999){
					iTileGraNo = iTileGraNo-DeMapDateOffSet;

					// 画面内なら处理する
					if( x >= -64
					 && x < DEF_APPSIZEX+64/2
					 && y >= -48
					 && y < DEF_APPSIZEY+48/2 ){
						xx = x-SugiCutFile.Date[iTileGraNo].CX;
						yy = y-SugiCutFile.Date[iTileGraNo].CY;
						BmpNumber=SugiCutFile.Date[iTileGraNo].GNum;
						BltMember.u=SugiCutFile.Date[iTileGraNo].U-320;
						BltMember.v=SugiCutFile.Date[iTileGraNo].V-240;
						BltMember.w=SugiCutFile.Date[iTileGraNo].W;
						BltMember.h=SugiCutFile.Date[iTileGraNo].H;
						BltMember.rgba.rgba=RGBA_MAKE(255,255,255,255);
						BltMember.BltVer=BLTVER_PUK2;
						BltMember.bltf=0;
						BltMember.PalNo=0;
						StockDispBuffer_PUK2( xx, yy,  DISP_PRIO_TILE, BmpNumber, 0, 1, &BltMember);
					}
				}


				// 布ツ表示
				iPartsGraNo = (mapParts[pos]);

				if( iPartsGraNo > 999 ){
					iPartsGraNo = iPartsGraNo-DeMapDateOffSet;

					switch(SugiCutFile.Date[iPartsGraNo].Flag){
						case 0:		//アニメーションしない布ツ
							// 通番を取る
							BmpNumber=SugiCutFile.Date[iPartsGraNo].GNum;
							realGetNo( BmpNumber, &bmpNo );

							BltMember.u=SugiCutFile.Date[iPartsGraNo].U-320;
							BltMember.v=SugiCutFile.Date[iPartsGraNo].V-240;
							BltMember.w=SugiCutFile.Date[iPartsGraNo].W;
							BltMember.h=SugiCutFile.Date[iPartsGraNo].H;
							BltMember.rgba.rgba=RGBA_MAKE(255,255,255,255);
							BltMember.BltVer=BLTVER_PUK2;
							BltMember.bltf=0;
							BltMember.PalNo=0;
							xx=x-(SugiCutFile.Date[iPartsGraNo].CX);
							yy=y-(SugiCutFile.Date[iPartsGraNo].CY);

							setMapParts(bmpNo,
								(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE,
								xx,yy,&BltMember);
							break;

						case 1:		//アニメーションする布ツ
							if (AniMapST.AnimePartsFlag[mapAreaX1+j][mapAreaY1+i]==0){
#ifdef PUK3_CHECK_VALUE
								//存在しないので作成
								pAct = createAnimePartsAction(
																	SugiCutFile.Date[iPartsGraNo].GNum,
																	mapAreaX1+j,
																	mapAreaY1+i);
								if ( pAct ){
									AniMapST.AnimePartsFlag[mapAreaX1+j][mapAreaY1+i]=1;
									//ACTION作成
									AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]=pAct;

									//中心からの补正值
									AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]->MapAnimePartsCX=SugiCutFile.Date[iPartsGraNo].CX;
									AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]->MapAnimePartsCY=SugiCutFile.Date[iPartsGraNo].CY;
								}
#else
								//存在しないので作成
								AniMapST.AnimePartsFlag[mapAreaX1+j][mapAreaY1+i]=1;
								//ACTION作成
								AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]=createAnimePartsAction(
																	SugiCutFile.Date[iPartsGraNo].GNum,
																	mapAreaX1+j,
																	mapAreaY1+i);

								//中心からの补正值
								AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]->MapAnimePartsCX=SugiCutFile.Date[iPartsGraNo].CX;
								AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]->MapAnimePartsCY=SugiCutFile.Date[iPartsGraNo].CY;
#endif
							}else{
								//存在するのでＰｒｃ
								AnimPartsProc(AniMapST.AnimePartsAction[mapAreaX1+j][mapAreaY1+i]);
							}
							break;
					}
				}else{
					//iPartsGraNoが999以下は非表示です。
					//严密にはチップのＩＤは999以上の数值しか入っていない作りです。
				}

#ifdef HIT_VIEW
				if( hitViewFlag == 1 ){
					if(mapHit[pos]==1){
						StockDispBufferEx( x, y, DISP_PRIO_JIKI,HIT_MAP_GRAPH, 0 );
					}
	#ifdef PUK3_OLDMAP_HIT_VIEW
					if( EVENT_WARP <= (mapEvent[pos+mapAreaWidth]&0x00ff) &&
						(mapEvent[pos+mapAreaWidth]&0x00ff) < EVENT_END ){
						realGetNo( 20, &bmpNo );
						StockDispBufferEx( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
					}
	#endif
				}
#endif
				
				i--;
				j--;
				x -= 64;
			}

			//一番端までいったら
			//一段上へ处理が移行します。
			if( tj < mapAreaWidth - 1 ){
				tj++;
				tx += 64/2;
				ty -= 48/2;
			}else{
				ti--;
				tx -= 64/2;
				ty -= 48/2;
			}
		}

		// キャラと布ツをマージする
		sortMapCharParts();
		// バッファの内容を描画（描画バッファに贮める）
		drawMapCharParts();

		oldMapGx = mapGx;
		oldMapGy = mapGy;

	}else{
	//旧マップ描画----------------------------------------------------------------------

	int i, j;
	int ti, tj, iTileGraNo, iPartsGraNo,pos;
	int x, y, tx, ty;
	S2 xx, yy, ww, hh;
	U4 bmpNo;

	draw_map_bgm_flg = 0;

	readMapAfterFrame++;

	// 现在のグリッド位置が变わったらファイルから読み込む
	if( mapGx != oldMapGx || mapGy != oldMapGy
	 || mapInsideFlag )
	{
		if( readMap( mapKind, mapNo, mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0] ) >= 0 )
		{
			// hitMap[]にあたり判定データを设定する
			readHitMap( mapAreaX1, mapAreaY1, mapAreaX2, mapAreaY2,
				&mapTile[0], &mapParts[0], &mapEvent[0], &mapHit[0] );

			if( mapEmptyFlag ){
				if( !checkEmptyMap( mapEmptyDir ) ){
					// 空マップのフラグをはずす drawMap()
					mapEmptyFlag = FALSE;
					autoMappingInitFlag = TRUE;	// オートマップ読み込み
				}else{

				}
			}else{
				autoMappingInitFlag = TRUE;	// オートマップ読み込み
			}

			readMapAfterFrame = 0;	// マップデータを読み込んでからの経过フレームをクリア
			mapInsideFlag = 0;
		}else{
			return;
		}
	}

	resetHighSpeedDraw();

	tx = highSpeedDrawNowX2 + (mapAreaX1+mapAreaY2-1) * SURFACE_WIDTH/2;
	ty = highSpeedDrawNowY2 + (-mapAreaX1+mapAreaY2-1) * SURFACE_HEIGHT/2;

	// 处理の顺番が以下のように处理する
	//
	// [map]
	//               16
	//            15    14
	//         13    12    11
	//      10     9     8     7
	//          6     5     4
	//             3     2
	//                1

	ti = mapAreaHeight-1;
	tj = 0;

	

	while( ti >= 0 )
	{
		i = ti;
		j = tj;

		x = tx;
		y = ty;

		while( i >= 0 && j >= 0 ){
			pos = i*mapAreaWidth+j;
			// タイル番号（０～６５５３５）を画像番号にコンバート
			iTileGraNo = TILENO_CONVERT( (int)(mapTile[pos]) );

			// タイル表示
			if( iTileGraNo > CG_INVISIBLE ){
				// 画面内なら处理する
				if( x >= -SURFACE_WIDTH/2
				 && x < DEF_APPSIZEX+SURFACE_WIDTH/2
				 && y >= -SURFACE_HEIGHT/2
				 && y < DEF_APPSIZEY+SURFACE_HEIGHT/2 ){
					//海タイルなら
					if( iTileGraNo >= 5000 && iTileGraNo <= 5000 + 9 ){
						StockDispBuffer( x, y, DISP_PRIO_SEA, iTileGraNo, 0 );
					}else{
						StockDispBuffer( x, y, DISP_PRIO_TILE, iTileGraNo, 0 );
					}
				}
			}else{
				// 特殊な布ツ（音とか）
				if(sound){
					// 环境音布ツなので鸣らす
					if( MAP_ENV_NO_START <= iTileGraNo && iTileGraNo <= MAP_ENV_NO_END ){
						play_environment( iTileGraNo, x, y );
					}else if( MAP_BGM_NO_START <= iTileGraNo && iTileGraNo <= MAP_BGM_NO_END ){
					// ＢＧＭ布ツなので鸣らす
						play_map_bgm( iTileGraNo );
						draw_map_bgm_flg = 1;
					}
				}
			}

			// タイル番号（０～６５５３５）を画像番号にコンバート
			iPartsGraNo = TILENO_CONVERT( (int)(mapParts[pos]) );

			// 布ツ表示
			if( iPartsGraNo > CG_INVISIBLE ){
				// 通番を取る
				realGetNo( iPartsGraNo, &bmpNo );
				// 画面内なら处理する
				realGetPos( bmpNo, &xx, &yy );
				realGetWH( bmpNo, &ww, &hh );
				xx += x;
				yy += y;
				if( xx < DEF_APPSIZEX && xx+ww-1 >= 0
				 && yy < DEF_APPSIZEY && yy+hh-1 >= 0 ){
					// 表示优先度决定のために登録する
					setMapParts( bmpNo,
						(float)(mapAreaX1+j)*GRID_SIZE,
						(float)(mapAreaY1+i)*GRID_SIZE, x, y );
				}
			}else{
				// 特殊な布ツ（音とか）
				if(sound){
					realGetNo( iPartsGraNo, &bmpNo );

					// ソートで使う布ツ
					if( iPartsGraNo == 1 ){
						setMapChar( bmpNo,
							(float)(mapAreaX1+j)*GRID_SIZE, (float)(mapAreaY1+i)*GRID_SIZE, x, y, 0 );
					}else if( MAP_ENV_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_ENV_NO_END ){
					// 环境音布ツなので鸣らす
						play_environment( mapParts[i*mapAreaWidth+j], x, y );
					}else if( MAP_BGM_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_BGM_NO_END ){
					// ＢＧＭ布ツなので鸣らす
						play_map_bgm( iPartsGraNo );
						draw_map_bgm_flg = 1;
					}
				}
		}
#ifdef PUK3_OLDMAP_HIT_VIEW
#ifdef HIT_VIEW
				if( hitViewFlag == 1 ){
					if( iTileGraNo > CG_INVISIBLE ){
					}else{
						// 特殊な布ツ（音とか）
						if(sound){
							// 环境音布ツなので鸣らす
							if( MAP_ENV_NO_START <= iTileGraNo && iTileGraNo <= MAP_ENV_NO_END ){
								realGetNo( 99, &bmpNo );
								StockDispBufferEx( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
							}else if( MAP_BGM_NO_START <= iTileGraNo && iTileGraNo <= MAP_BGM_NO_END ){
							// ＢＧＭ布ツなので鸣らす
								realGetNo( 100, &bmpNo );
								StockDispBuffer2( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
							}
						}
					}
					// 布ツ表示
					if( iPartsGraNo > CG_INVISIBLE ){
					}else{
						// 特殊な布ツ（音とか）
						realGetNo( iPartsGraNo, &bmpNo );

						// ソートで使う布ツ
						if( iPartsGraNo == 1 ){
							StockDispBuffer2( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
						}else if( MAP_ENV_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_ENV_NO_END ){
						// 环境音布ツなので鸣らす
							realGetNo( 99, &bmpNo );
							StockDispBufferEx( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
						}else if( MAP_BGM_NO_START <= iPartsGraNo && iPartsGraNo <= MAP_BGM_NO_END ){
						// ＢＧＭ布ツなので鸣らす
							realGetNo( 100, &bmpNo );
							StockDispBuffer2( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
						}
					}
					if(mapHit[pos]==1){
						StockDispBufferEx( x, y, DISP_PRIO_JIKI,HIT_MAP_GRAPH, 0 );
					}
					if( EVENT_WARP <= (mapEvent[pos+mapAreaWidth]&0x00ff) &&
						(mapEvent[pos+mapAreaWidth]&0x00ff) < EVENT_END ){
						realGetNo( 20, &bmpNo );
						StockDispBufferEx( x, y, DISP_PRIO_JIKI,bmpNo, 0 );
					}
				}
#endif
#endif
			i--;
			j--;
			x -= SURFACE_WIDTH;
		}

		if( tj < mapAreaWidth - 1 ){
			tj++;
			tx += SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}else{
			ti--;
			tx -= SURFACE_WIDTH/2;
			ty -= SURFACE_HEIGHT/2;
		}
	}
		// キャラと布ツをマージする
		sortMapCharParts();
		// バッファの内容を描画（描画バッファに贮める）
		drawMapCharParts();

		oldMapGx = mapGx;
		oldMapGy = mapGy;

	}

#ifdef PUK2
	playEnvironmentSE ();
#endif

}

// 当たり判定情报を取り出す。
void readHitMap_PUK2( int x1, int y1, int x2, int y2,
	unsigned short *tile, unsigned short *parts, unsigned short *event, unsigned short *hitMap )
{
	int width, height;
	int i, j, pos;

	//内部でもマップが新マップか旧マップかチェックします。
	//これはdrawTile()中でも使われるからです。
	if(SugiMapStat.MapVer ==1){
		//新マップ
		memset( hitMap, 0, MAP_AREA_X_SIZE * MAP_AREA_Y_SIZE * sizeof( short ) );

		width = x2 - x1;
		height = y2 - y1;

		if( width < 1 || height < 1 )
			return;

		for( i = 0; i < height; i++ ){
			for( j = 0; j < width; j++ ){
				pos = i*width+j;
				// イベント中のヒットフラグを调べる
				if( event[pos] & MAP_HIT_FLAG ){
					hitMap[pos] = 1;
				}
			}
		}

	}else{
		//旧マップ
		readHitMap(x1,y1,x2,y2,tile,parts,event,hitMap);
	}
}

//マップ状态セット
void MapStatusSet(int mapid,int floor,int cut,int MapVer){

//	if(SugiMapStat.mapID != mapid || SugiMapStat.mapNo != floor || SugiMapStat.MapVer != MapVer){
		environmentSEClean ();
		//实际のＩＤと状态のＩＤとが违う场合読み込み直します
		SugiMapStat.mapID=mapid;
		SugiMapStat.mapNo=floor;
		SugiMapStat.MapVer=MapVer;

		switch(MapVer){
		case 0:
			//旧マップはなにもしません
			break;
		case 1:
			//新マップ处理
			if(cut==-1){
				//－１のときはフロアＩＤと同じ值
				SugiMapStat.CutDateNum=floor;
				SugiMapStat.AutoMapNum=floor;
				//新マップ用カットデータとオートマップの読み込み（フロアＩＤとカットデータＩＤが同じ）
				SugiCutDateRead(long(floor));
				SugiRGBDateReadForAutoMap(long(floor));
			}else{
				//指定があるときはそれを使用する
				SugiMapStat.CutDateNum=cut;
				SugiMapStat.AutoMapNum=cut;
				//新マップ用カットデータとオートマップの読み込み（フロアＩＤとカットデータＩＤが违う）
				SugiCutDateRead(long(cut));
				SugiRGBDateReadForAutoMap(long(cut));
			}
			AnimePartsInit();
			break;
		}
//	}

}

//
// カットデータ読み込み
//
void SugiCutDateRead(long MapNo){

	FILE *fp;
	char FileName[256];
	long AllCutDateCount;
	int i;

	SugiCutDateClear();

	//カットデータ読み込み
	// 固定マップの时のファイル名
	sprintf( FileName, "bin\\PUK2\\cutdat\\%d.cut", MapNo );             //MLHIDE

	if( (fp = fopen( FileName, "rb" ))==NULL ){                          //MLHIDE
		//ファイルが无い
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_1, "确认", MB_OK | MB_ICONSTOP );            //MLHIDE
	#else
		MessageBox( hWnd, "cut Map File打开失败。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		//ヘルプサイト表示
	#ifdef PUK3_ERRORMESSAGE_NUM
		if( MessageBox( hWnd, INFOMSG_1, "INFOMSG_1", MB_OKCANCEL | MB_ICONSTOP ) == IDOK ) //MLHIDE
	#else
		if( MessageBox( hWnd, "FAQ INFOMSG_1", "INFOMSG_1", MB_OKCANCEL | MB_ICONSTOP ) == IDOK ) //MLHIDE
	#endif
			ShellExecute(hWnd, NULL, szMapFaqUrl, NULL, NULL, SW_SHOWNORMAL);
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
		return;
	}

	fread(&SugiCutFile.Header,sizeof(SugiCutFile.Header),1,fp);

	AllCutDateCount=(long)SugiCutFile.Header.Feald+(long)SugiCutFile.Header.Parts+(long)SugiCutFile.Header.Anime;

	for(i=0;i<AllCutDateCount;i++){
		fread(&SugiCutFile.Date[i].GNum,sizeof(long),1,fp);
		fread(&SugiCutFile.Date[i].Flag,sizeof(short),1,fp);
		fread(&SugiCutFile.Date[i].U,sizeof(short),1,fp);
		fread(&SugiCutFile.Date[i].V,sizeof(short),1,fp);
		fread(&SugiCutFile.Date[i].W,sizeof(short),1,fp);
		fread(&SugiCutFile.Date[i].H,sizeof(short),1,fp);
		fread(&SugiCutFile.Date[i].CX,sizeof(short),1,fp);
		fread(&SugiCutFile.Date[i].CY,sizeof(short),1,fp);
	}

	fclose(fp);

}

//カットデータ领域のクリア
void SugiCutDateClear(void){

	int i;
	
	memset(&SugiCutFile,0,sizeof(SugiCutFile));

	for(i=0;i<DeCutDataMaxSize;i++){
		SugiCutFile.Date[i].Flag=-1;
	}
}

//
// オートマップ用ファイル読み込み
//
void SugiRGBDateReadForAutoMap(long MapNo){

	FILE *fp;
	char FileName[256];

	sprintf( FileName, "bin\\PUK2\\cutdat\\%d.aut", MapNo );             //MLHIDE

	//オートマップ用ファイル読み込み
	if( (fp = fopen( FileName, "rb" ))==NULL ){                          //MLHIDE
		//ファイルが无い
	#ifdef PUK3_ERRORMESSAGE_NUM
		MessageBox( hWnd, ERRMSG_2, "确认", MB_OK | MB_ICONSTOP );            //MLHIDE
	#else
		MessageBox( hWnd, "aut Map File打开失败。", "确认", MB_OK | MB_ICONSTOP ); //MLHIDE
	#endif
		//ヘルプサイト表示
	#ifdef PUK3_ERRORMESSAGE_NUM
		if( MessageBox( hWnd, INFOMSG_2, "INFOMSG_2", MB_OKCANCEL | MB_ICONSTOP ) == IDOK ) //MLHIDE
	#else
		if( MessageBox( hWnd, "FAQ INFOMSG_2", "INFOMSG_2", MB_OKCANCEL | MB_ICONSTOP ) == IDOK ) //MLHIDE
	#endif
			ShellExecute(hWnd, NULL, szMapFaqUrl, NULL, NULL, SW_SHOWNORMAL);
		// ＷＩＮＤＯＷＳに WM_CLOSE 信息を送らせる
		PostMessage( hWnd, WM_CLOSE, 0, 0L );
		return;
	}

		fread(&SugiAutoMapDate,sizeof(unsigned char),DeAutoMapSize*3,fp);

	fclose(fp);

}

//アニメーション布ツの初期化
void AnimePartsInit(void){

	int i,j;

//	アニメタイル削除フラグ立て关数
	for(i=0;i<ANIME_PARTS_X_MAX;i++){
		for(j=0;j<ANIME_PARTS_Y_MAX;j++){
			if(AniMapST.AnimePartsFlag[i][j]==1){
				AniMapST.AnimePartsFlag[i][j]=0;
				DeathAction( AniMapST.AnimePartsAction[i][j] );
#ifdef PUK3_CHECK_VALUE
				AniMapST.AnimePartsAction[i][j] = NULL;
#endif
			}
		}
	}

}

// キャラクタアクションのデフォルト
void AnimPartsProc( ACTION *ptAct )
{
	float mx, my;
	// 画面表示位置
	//グリッド位置から画面座标を算出
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	mx=(float)(ptAct->gx*GRID_SIZE);
	my=(float)(ptAct->gy*GRID_SIZE);

	//アニメーション
	pattern( ptAct, ANM_NOMAL_SPD, ANM_LOOP);

	setMapAnimeParts( ptAct->bmpNo,
						mx,
						my,
						ptAct->x + ptAct->MapAnimePartsCX,
						ptAct->y + ptAct->MapAnimePartsCY,
						&ptAct->bm );

}

// キャラクタのアクション作成
ACTION *createAnimePartsAction( int graNo, int gx, int gy )
{
	ACTION *ptAct;
	float mx, my;

	/* アクションリストに登録 */
	ptAct = GetAction( PRIO_CHR, /*sizeof( CHAREXTRA )*/0 );
	if( ptAct == NULL )
		return NULL;

	// 实行关数
//	ptAct->func = AnimPartsProc;
	ptAct->func = NULL;
	// グラフィックの番号
	ptAct->anim_chr_no = graNo;
	// 动作番号
	ptAct->anim_no = ANIM_STAND;
	// アニメーション向き( ０～７ )( 下が０で右回り )
	ptAct->anim_ang = 0;
	// 表示优先度
	ptAct->dispPrio = DISP_PRIO_CHAR;
	// 1行インフォ表示フラグ
//	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIT | ACT_ATR_HIDE2;
	ptAct->atr = ACT_ATR_INFO |	ACT_ATR_HIDE2;
	// 初期位置
	ptAct->nextGx = gx;					// マップグリッド座标（移动先）
	ptAct->nextGy = gy;
	ptAct->bufCount = 0;
	ptAct->gx = gx;						// マップグリッド座标（现在地）
	ptAct->gy = gy;
	ptAct->mx = (float)gx * GRID_SIZE;	// マップ座标
	ptAct->my = (float)gy * GRID_SIZE;
	ptAct->vx = 0;						// 移动增分
	ptAct->vy = 0;

	// 画面表示位置
	camMapToGamen( ptAct->mx, ptAct->my, &mx, &my );
	ptAct->x = (int)(mx+.5);
	ptAct->y = (int)(my+.5);

	return ptAct;
}

// 布ツをバッファに贮める
void setMapAnimeParts( int graNo, float mx, float my, int screenX, int screenY, struct BLT_MEMBER *bm )
{

	// バッファが一杯なので終わる
	if( mapPartsSortCnt >= MAX_MAP_PARTS_SORT_INFO )
		return;

	mapPartsSortInfo[mapPartsSortCnt].graNo		= graNo;
	mapPartsSortInfo[mapPartsSortCnt].mx		= mx;
	mapPartsSortInfo[mapPartsSortCnt].my		= my;
	mapPartsSortInfo[mapPartsSortCnt].screenX	= screenX;
	mapPartsSortInfo[mapPartsSortCnt].screenY	= screenY;
	mapPartsSortInfo[mapPartsSortCnt].no		= mapPartsSortCnt;
	mapPartsSortInfo[mapPartsSortCnt].type		= 0;

	if (bm){
		mapPartsSortInfo[mapPartsSortCnt].bm=*bm;
	}else{
			mapPartsSortInfo[mapPartsSortCnt].bm.u=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.v=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.w=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.h=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.rgba.rgba=0xffffffff;
			mapPartsSortInfo[mapPartsSortCnt].bm.BltVer=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.bltf=0;
			mapPartsSortInfo[mapPartsSortCnt].bm.PalNo=0;
	}
	mapPartsSortCnt++;
}

