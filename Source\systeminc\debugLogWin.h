﻿////#ifdef _DEBUG
#if defined(_DEBUG) || defined(_RELDEB)

#ifndef _DEBUG_LOG_WIN_H_
#define _DEBUG_LOG_WIN_H_

#include<windows.h>

extern HWND hLogWindow;
extern HWND hLogList;
extern HFONT hLogFont;

extern int logWinX;
extern int logWinY;

#ifdef PUK2
void updateLogMessage( int, char * );
#else
void updateLogMessage( char * );
#endif
LRESULT CALLBACK logWindowProc( HWND, UINT, WPARAM, LPARAM );
<PERSON><PERSON><PERSON> openLogWindow( void );
void closeLogWindow( void );

void appendLogMessage( char * );
void sendNetworkMessage( void );
void setFont( HWND );

void writeLogFile( char * );
void clearLogFile( char * );
#ifdef PUK2
	void loadLogFile( void );
#endif

#endif

#endif
